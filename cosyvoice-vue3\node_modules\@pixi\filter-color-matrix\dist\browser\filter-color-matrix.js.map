{"version": 3, "file": "filter-color-matrix.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/ColorMatrixFilter.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { Filter, defaultFilterVertex } from '@pixi/core';\nimport fragment from './colorMatrix.frag';\n\nimport type { ArrayFixed } from '@pixi/utils';\n\nexport type ColorMatrix = ArrayFixed<number, 20>;\n\n/**\n * The ColorMatrixFilter class lets you apply a 5x4 matrix transformation on the RGBA\n * color and alpha values of every pixel on your displayObject to produce a result\n * with a new set of RGBA color and alpha values. It's pretty powerful!\n *\n * ```js\n *  let colorMatrix = new PIXI.filters.ColorMatrixFilter();\n *  container.filters = [colorMatrix];\n *  colorMatrix.contrast(2);\n * ```\n * <AUTHOR> <<EMAIL>>\n * @memberof PIXI.filters\n */\nexport class ColorMatrixFilter extends Filter\n{\n    public grayscale: (scale: number, multiply: boolean) => void;\n\n    constructor()\n    {\n        const uniforms = {\n            m: new Float32Array([1, 0, 0, 0, 0,\n                0, 1, 0, 0, 0,\n                0, 0, 1, 0, 0,\n                0, 0, 0, 1, 0]),\n            uAlpha: 1,\n        };\n\n        super(defaultFilterVertex, fragment, uniforms);\n\n        this.alpha = 1;\n    }\n\n    /**\n     * Transforms current matrix and set the new one\n     * @param {number[]} matrix - 5x4 matrix\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    private _loadMatrix(matrix: ColorMatrix, multiply = false): void\n    {\n        let newMatrix = matrix;\n\n        if (multiply)\n        {\n            this._multiply(newMatrix, this.uniforms.m, matrix);\n            newMatrix = this._colorMatrix(newMatrix) as any;\n        }\n\n        // set the new matrix\n        this.uniforms.m = newMatrix;\n    }\n\n    /**\n     * Multiplies two mat5's\n     * @private\n     * @param out - 5x4 matrix the receiving matrix\n     * @param a - 5x4 matrix the first operand\n     * @param b - 5x4 matrix the second operand\n     * @returns {number[]} 5x4 matrix\n     */\n    private _multiply(out: ColorMatrix, a: ColorMatrix, b: ColorMatrix): ColorMatrix\n    {\n        // Red Channel\n        out[0] = (a[0] * b[0]) + (a[1] * b[5]) + (a[2] * b[10]) + (a[3] * b[15]);\n        out[1] = (a[0] * b[1]) + (a[1] * b[6]) + (a[2] * b[11]) + (a[3] * b[16]);\n        out[2] = (a[0] * b[2]) + (a[1] * b[7]) + (a[2] * b[12]) + (a[3] * b[17]);\n        out[3] = (a[0] * b[3]) + (a[1] * b[8]) + (a[2] * b[13]) + (a[3] * b[18]);\n        out[4] = (a[0] * b[4]) + (a[1] * b[9]) + (a[2] * b[14]) + (a[3] * b[19]) + a[4];\n\n        // Green Channel\n        out[5] = (a[5] * b[0]) + (a[6] * b[5]) + (a[7] * b[10]) + (a[8] * b[15]);\n        out[6] = (a[5] * b[1]) + (a[6] * b[6]) + (a[7] * b[11]) + (a[8] * b[16]);\n        out[7] = (a[5] * b[2]) + (a[6] * b[7]) + (a[7] * b[12]) + (a[8] * b[17]);\n        out[8] = (a[5] * b[3]) + (a[6] * b[8]) + (a[7] * b[13]) + (a[8] * b[18]);\n        out[9] = (a[5] * b[4]) + (a[6] * b[9]) + (a[7] * b[14]) + (a[8] * b[19]) + a[9];\n\n        // Blue Channel\n        out[10] = (a[10] * b[0]) + (a[11] * b[5]) + (a[12] * b[10]) + (a[13] * b[15]);\n        out[11] = (a[10] * b[1]) + (a[11] * b[6]) + (a[12] * b[11]) + (a[13] * b[16]);\n        out[12] = (a[10] * b[2]) + (a[11] * b[7]) + (a[12] * b[12]) + (a[13] * b[17]);\n        out[13] = (a[10] * b[3]) + (a[11] * b[8]) + (a[12] * b[13]) + (a[13] * b[18]);\n        out[14] = (a[10] * b[4]) + (a[11] * b[9]) + (a[12] * b[14]) + (a[13] * b[19]) + a[14];\n\n        // Alpha Channel\n        out[15] = (a[15] * b[0]) + (a[16] * b[5]) + (a[17] * b[10]) + (a[18] * b[15]);\n        out[16] = (a[15] * b[1]) + (a[16] * b[6]) + (a[17] * b[11]) + (a[18] * b[16]);\n        out[17] = (a[15] * b[2]) + (a[16] * b[7]) + (a[17] * b[12]) + (a[18] * b[17]);\n        out[18] = (a[15] * b[3]) + (a[16] * b[8]) + (a[17] * b[13]) + (a[18] * b[18]);\n        out[19] = (a[15] * b[4]) + (a[16] * b[9]) + (a[17] * b[14]) + (a[18] * b[19]) + a[19];\n\n        return out;\n    }\n\n    /**\n     * Create a Float32 Array and normalize the offset component to 0-1\n     * @param {number[]} matrix - 5x4 matrix\n     * @returns {number[]} 5x4 matrix with all values between 0-1\n     */\n    private _colorMatrix(matrix: ColorMatrix): ColorMatrix\n    {\n        // Create a Float32 Array and normalize the offset component to 0-1\n        const m = new Float32Array(matrix);\n\n        m[4] /= 255;\n        m[9] /= 255;\n        m[14] /= 255;\n        m[19] /= 255;\n\n        return m as any;\n    }\n\n    /**\n     * Adjusts brightness\n     * @param b - value of the brigthness (0-1, where 0 is black)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public brightness(b: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            b, 0, 0, 0, 0,\n            0, b, 0, 0, 0,\n            0, 0, b, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Sets each channel on the diagonal of the color matrix.\n     * This can be used to achieve a tinting effect on Containers similar to the tint field of some\n     * display objects like Sprite, Text, Graphics, and Mesh.\n     * @param color - Color of the tint. This is a hex value.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public tint(color: number, multiply?: boolean): void\n    {\n        const r = (color >> 16) & 0xff;\n        const g = (color >> 8) & 0xff;\n        const b = color & 0xff;\n\n        const matrix: ColorMatrix = [\n            r / 255, 0, 0, 0, 0,\n            0, g / 255, 0, 0, 0,\n            0, 0, b / 255, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the matrices in grey scales\n     * @param scale - value of the grey (0-1, where 0 is black)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public greyscale(scale: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            scale, scale, scale, 0, 0,\n            scale, scale, scale, 0, 0,\n            scale, scale, scale, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the black and white matrice.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public blackAndWhite(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.3, 0.6, 0.1, 0, 0,\n            0.3, 0.6, 0.1, 0, 0,\n            0.3, 0.6, 0.1, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the hue property of the color\n     * @param rotation - in degrees\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public hue(rotation: number, multiply: boolean): void\n    {\n        rotation = (rotation || 0) / 180 * Math.PI;\n\n        const cosR = Math.cos(rotation);\n        const sinR = Math.sin(rotation);\n        const sqrt = Math.sqrt;\n\n        /* a good approximation for hue rotation\n         This matrix is far better than the versions with magic luminance constants\n         formerly used here, but also used in the starling framework (flash) and known from this\n         old part of the internet: quasimondo.com/archives/000565.php\n\n         This new matrix is based on rgb cube rotation in space. Look here for a more descriptive\n         implementation as a shader not a general matrix:\n         https://github.com/evanw/glfx.js/blob/58841c23919bd59787effc0333a4897b43835412/src/filters/adjust/huesaturation.js\n\n         This is the source for the code:\n         see http://stackoverflow.com/questions/8507885/shift-hue-of-an-rgb-color/8510751#8510751\n         */\n\n        const w = 1 / 3;\n        const sqrW = sqrt(w); // weight is\n\n        const a00 = cosR + ((1.0 - cosR) * w);\n        const a01 = (w * (1.0 - cosR)) - (sqrW * sinR);\n        const a02 = (w * (1.0 - cosR)) + (sqrW * sinR);\n\n        const a10 = (w * (1.0 - cosR)) + (sqrW * sinR);\n        const a11 = cosR + (w * (1.0 - cosR));\n        const a12 = (w * (1.0 - cosR)) - (sqrW * sinR);\n\n        const a20 = (w * (1.0 - cosR)) - (sqrW * sinR);\n        const a21 = (w * (1.0 - cosR)) + (sqrW * sinR);\n        const a22 = cosR + (w * (1.0 - cosR));\n\n        const matrix: ColorMatrix = [\n            a00, a01, a02, 0, 0,\n            a10, a11, a12, 0, 0,\n            a20, a21, a22, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the contrast matrix, increase the separation between dark and bright\n     * Increase contrast : shadows darker and highlights brighter\n     * Decrease contrast : bring the shadows up and the highlights down\n     * @param amount - value of the contrast (0-1)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public contrast(amount: number, multiply: boolean): void\n    {\n        const v = (amount || 0) + 1;\n        const o = -0.5 * (v - 1);\n\n        const matrix: ColorMatrix = [\n            v, 0, 0, 0, o,\n            0, v, 0, 0, o,\n            0, 0, v, 0, o,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the saturation matrix, increase the separation between colors\n     * Increase saturation : increase contrast, brightness, and sharpness\n     * @param amount - The saturation amount (0-1)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public saturate(amount = 0, multiply?: boolean): void\n    {\n        const x = (amount * 2 / 3) + 1;\n        const y = ((x - 1) * -0.5);\n\n        const matrix: ColorMatrix = [\n            x, y, y, 0, 0,\n            y, x, y, 0, 0,\n            y, y, x, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /** Desaturate image (remove color) Call the saturate function */\n    public desaturate(): void // eslint-disable-line no-unused-vars\n    {\n        this.saturate(-1);\n    }\n\n    /**\n     * Negative image (inverse of classic rgb matrix)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public negative(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            -1, 0, 0, 1, 0,\n            0, -1, 0, 1, 0,\n            0, 0, -1, 1, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Sepia image\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public sepia(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.393, 0.7689999, 0.18899999, 0, 0,\n            0.349, 0.6859999, 0.16799999, 0, 0,\n            0.272, 0.5339999, 0.13099999, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Color motion picture process invented in 1916 (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public technicolor(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.9125277891456083, -0.8545344976951645, -0.09155508482755585, 0, 11.793603434377337,\n            -0.3087833385928097, 1.7658908555458428, -0.10601743074722245, 0, -70.35205161461398,\n            -0.231103377548616, -0.7501899197440212, 1.847597816108189, 0, 30.950940869491138,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Polaroid filter\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public polaroid(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.438, -0.062, -0.062, 0, 0,\n            -0.122, 1.378, -0.122, 0, 0,\n            -0.016, -0.016, 1.483, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Filter who transforms : Red -> Blue and Blue -> Red\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public toBGR(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0, 0, 1, 0, 0,\n            0, 1, 0, 0, 0,\n            1, 0, 0, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Color reversal film introduced by Eastman Kodak in 1935. (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public kodachrome(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.1285582396593525, -0.3967382283601348, -0.03992559172921793, 0, 63.72958762196502,\n            -0.16404339962244616, 1.0835251566291304, -0.05498805115633132, 0, 24.732407896706203,\n            -0.16786010706155763, -0.5603416277695248, 1.6014850761964943, 0, 35.62982807460946,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Brown delicious browni filter (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public browni(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.5997023498159715, 0.34553243048391263, -0.2708298674538042, 0, 47.43192855600873,\n            -0.037703249837783157, 0.8609577587992641, 0.15059552388459913, 0, -36.96841498319127,\n            0.24113635128153335, -0.07441037908422492, 0.44972182064877153, 0, -7.562075277591283,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Vintage filter (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public vintage(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.6279345635605994, 0.3202183420819367, -0.03965408211312453, 0, 9.651285835294123,\n            0.02578397704808868, 0.6441188644374771, 0.03259127616149294, 0, 7.462829176470591,\n            0.0466055556782719, -0.0851232987247891, 0.5241648018700465, 0, 5.159190588235296,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * We don't know exactly what it does, kind of gradient map, but funny to play with!\n     * @param desaturation - Tone values.\n     * @param toned - Tone values.\n     * @param lightColor - Tone values, example: `0xFFE580`\n     * @param darkColor - Tone values, example: `0xFFE580`\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public colorTone(desaturation: number, toned: number, lightColor: number, darkColor: number, multiply: boolean): void\n    {\n        desaturation = desaturation || 0.2;\n        toned = toned || 0.15;\n        lightColor = lightColor || 0xFFE580;\n        darkColor = darkColor || 0x338000;\n\n        const lR = ((lightColor >> 16) & 0xFF) / 255;\n        const lG = ((lightColor >> 8) & 0xFF) / 255;\n        const lB = (lightColor & 0xFF) / 255;\n\n        const dR = ((darkColor >> 16) & 0xFF) / 255;\n        const dG = ((darkColor >> 8) & 0xFF) / 255;\n        const dB = (darkColor & 0xFF) / 255;\n\n        const matrix: ColorMatrix = [\n            0.3, 0.59, 0.11, 0, 0,\n            lR, lG, lB, desaturation, 0,\n            dR, dG, dB, toned, 0,\n            lR - dR, lG - dG, lB - dB, 0, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Night effect\n     * @param intensity - The intensity of the night effect.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public night(intensity: number, multiply: boolean): void\n    {\n        intensity = intensity || 0.1;\n\n        const matrix: ColorMatrix = [\n            intensity * (-2.0), -intensity, 0, 0, 0,\n            -intensity, 0, intensity, 0, 0,\n            0, intensity, intensity * 2.0, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Predator effect\n     *\n     * Erase the current matrix by setting a new indepent one\n     * @param amount - how much the predator feels his future victim\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public predator(amount: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            // row 1\n            11.224130630493164 * amount,\n            -4.794486999511719 * amount,\n            -2.8746118545532227 * amount,\n            0 * amount,\n            0.40342438220977783 * amount,\n            // row 2\n            -3.6330697536468506 * amount,\n            9.193157196044922 * amount,\n            -2.951810836791992 * amount,\n            0 * amount,\n            -1.316135048866272 * amount,\n            // row 3\n            -3.2184197902679443 * amount,\n            -4.2375030517578125 * amount,\n            7.476448059082031 * amount,\n            0 * amount,\n            0.8044459223747253 * amount,\n            // row 4\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * LSD effect\n     *\n     * Multiply the current matrix\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public lsd(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            2, -0.4, 0.5, 0, 0,\n            -0.5, 2, -0.4, 0, 0,\n            -0.4, -0.5, 3, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /** Erase the current matrix by setting the default one. */\n    public reset(): void\n    {\n        const matrix: ColorMatrix = [\n            1, 0, 0, 0, 0,\n            0, 1, 0, 0, 0,\n            0, 0, 1, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, false);\n    }\n\n    /**\n     * The matrix of the color matrix filter\n     * @member {number[]}\n     * @default [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0]\n     */\n    get matrix(): ColorMatrix\n    {\n        return this.uniforms.m;\n    }\n\n    set matrix(value: ColorMatrix)\n    {\n        this.uniforms.m = value;\n    }\n\n    /**\n     * The opacity value to use when mixing the original and resultant colors.\n     *\n     * When the value is 0, the original color is used without modification.\n     * When the value is 1, the result color is used.\n     * When in the range (0, 1) the color is interpolated between the original and result by this amount.\n     * @default 1\n     */\n    get alpha(): number\n    {\n        return this.uniforms.uAlpha;\n    }\n\n    set alpha(value: number)\n    {\n        this.uniforms.uAlpha = value;\n    }\n}\n\n// Americanized alias\nColorMatrixFilter.prototype.grayscale = ColorMatrixFilter.prototype.greyscale;\n"], "names": ["arguments", "defaultFilterVertex", "Filter"], "mappings": ";;;;;;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;;;IClNA;;;;;;;;;;;;IAYG;AACH,QAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;QAAuC,SAAM,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;IAIzC,IAAA,SAAA,iBAAA,GAAA;YAAA,IAaC,KAAA,GAAA,IAAA,CAAA;IAXG,QAAA,IAAM,QAAQ,GAAG;IACb,YAAA,CAAC,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,gBAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,gBAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;oBACb,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,YAAA,MAAM,EAAE,CAAC;aACZ,CAAC;IAEF,QAAA,KAAA,GAAA,kBAAMC,wBAAmB,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAC,IAAA,CAAA;IAE/C,QAAA,KAAI,CAAC,KAAK,GAAG,CAAC,CAAC;;SAClB;IAED;;;;;IAKG;IACK,IAAA,iBAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,UAAoB,MAAmB,EAAE,QAAgB,EAAA;IAAhB,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAgB,GAAA,KAAA,CAAA,EAAA;YAErD,IAAI,SAAS,GAAG,MAAM,CAAC;IAEvB,QAAA,IAAI,QAAQ,EACZ;IACI,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACnD,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAQ,CAAC;IACnD,SAAA;;IAGD,QAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;SAC/B,CAAA;IAED;;;;;;;IAOG;IACK,IAAA,iBAAA,CAAA,SAAA,CAAA,SAAS,GAAjB,UAAkB,GAAgB,EAAE,CAAc,EAAE,CAAc,EAAA;;YAG9D,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;YAGhF,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;YAGhF,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;YAGtF,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAEtF,QAAA,OAAO,GAAG,CAAC;SACd,CAAA;IAED;;;;IAIG;QACK,iBAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,MAAmB,EAAA;;IAGpC,QAAA,IAAM,CAAC,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;IAEnC,QAAA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;IACZ,QAAA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;IACZ,QAAA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;IACb,QAAA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;IAEb,QAAA,OAAO,CAAQ,CAAC;SACnB,CAAA;IAED;;;;;IAKG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,UAAkB,CAAS,EAAE,QAAiB,EAAA;IAE1C,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;;;IAOG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,IAAI,GAAX,UAAY,KAAa,EAAE,QAAkB,EAAA;YAEzC,IAAM,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC;YAC/B,IAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;IAC9B,QAAA,IAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;IAEvB,QAAA,IAAM,MAAM,GAAgB;gBACxB,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;IAKG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UAAiB,KAAa,EAAE,QAAiB,EAAA;IAE7C,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;IACzB,YAAA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;IACzB,YAAA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;IACzB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,QAAiB,EAAA;IAElC,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;IAKG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAG,GAAV,UAAW,QAAgB,EAAE,QAAiB,EAAA;IAE1C,QAAA,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;YAE3C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAChC,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAEvB;;;;;;;;;;;IAWG;IAEH,QAAA,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChB,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAErB,QAAA,IAAM,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IACtC,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAC/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAE/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAC/C,QAAA,IAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;IACtC,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAE/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAC/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAC/C,QAAA,IAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;IAEtC,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;;;IAOG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,MAAc,EAAE,QAAiB,EAAA;YAE7C,IAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5B,IAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAEzB,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;;IAMG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,MAAU,EAAE,QAAkB,EAAA;IAA9B,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;YAEtB,IAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,QAAA,IAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE3B,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;;IAGM,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,YAAA;IAEI,QAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACrB,CAAA;IAED;;;;IAIG;QACI,iBAAQ,CAAA,SAAA,CAAA,QAAA,GAAf,UAAgB,QAAiB,EAAA;IAE7B,QAAA,IAAM,MAAM,GAAgB;gBACxB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACd,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACd,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACd,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,QAAiB,EAAA;IAE1B,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;IAClC,YAAA,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;IAClC,YAAA,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;IAClC,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAW,CAAA,SAAA,CAAA,WAAA,GAAlB,UAAmB,QAAiB,EAAA;IAEhC,QAAA,IAAM,MAAM,GAAgB;gBACxB,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,kBAAkB;gBACpF,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAC,iBAAiB;gBACpF,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,CAAC,EAAE,kBAAkB;IACjF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAQ,CAAA,SAAA,CAAA,QAAA,GAAf,UAAgB,QAAiB,EAAA;IAE7B,QAAA,IAAM,MAAM,GAAgB;gBACxB,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;gBAC3B,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;gBAC3B,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;IAC3B,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,QAAiB,EAAA;IAE1B,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAU,CAAA,SAAA,CAAA,UAAA,GAAjB,UAAkB,QAAiB,EAAA;IAE/B,QAAA,IAAM,MAAM,GAAgB;gBACxB,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,iBAAiB;gBACnF,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,kBAAkB;gBACrF,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,EAAE,iBAAiB;IACnF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,QAAiB,EAAA;IAE3B,QAAA,IAAM,MAAM,GAAgB;gBACxB,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,CAAC,EAAE,iBAAiB;gBAClF,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC,iBAAiB;gBACrF,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC,iBAAiB;IACrF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;IAIG;QACI,iBAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,QAAiB,EAAA;IAE5B,QAAA,IAAM,MAAM,GAAgB;gBACxB,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,iBAAiB;IAClF,YAAA,mBAAmB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,EAAE,iBAAiB;gBAClF,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,EAAE,iBAAiB;IACjF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;;;;IAQG;QACI,iBAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,YAAoB,EAAE,KAAa,EAAE,UAAkB,EAAE,SAAiB,EAAE,QAAiB,EAAA;IAE1G,QAAA,YAAY,GAAG,YAAY,IAAI,GAAG,CAAC;IACnC,QAAA,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC;IACtB,QAAA,UAAU,GAAG,UAAU,IAAI,QAAQ,CAAC;IACpC,QAAA,SAAS,GAAG,SAAS,IAAI,QAAQ,CAAC;IAElC,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,UAAU,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC;IAC7C,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;YAC5C,IAAM,EAAE,GAAG,CAAC,UAAU,GAAG,IAAI,IAAI,GAAG,CAAC;IAErC,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC;IAC5C,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;YAC3C,IAAM,EAAE,GAAG,CAAC,SAAS,GAAG,IAAI,IAAI,GAAG,CAAC;IAEpC,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;IACrB,YAAA,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;IAC3B,YAAA,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC;IACpB,YAAA,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAClC,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;IAKG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,UAAa,SAAiB,EAAE,QAAiB,EAAA;IAE7C,QAAA,SAAS,GAAG,SAAS,IAAI,GAAG,CAAC;IAE7B,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,SAAS,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACvC,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;gBAC9B,CAAC,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;IACnC,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;;;IAOG;IACI,IAAA,iBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,MAAc,EAAE,QAAiB,EAAA;IAE7C,QAAA,IAAM,MAAM,GAAgB;;IAExB,YAAA,kBAAkB,GAAG,MAAM;gBAC3B,CAAC,iBAAiB,GAAG,MAAM;gBAC3B,CAAC,kBAAkB,GAAG,MAAM;IAC5B,YAAA,CAAC,GAAG,MAAM;IACV,YAAA,mBAAmB,GAAG,MAAM;;gBAE5B,CAAC,kBAAkB,GAAG,MAAM;IAC5B,YAAA,iBAAiB,GAAG,MAAM;gBAC1B,CAAC,iBAAiB,GAAG,MAAM;IAC3B,YAAA,CAAC,GAAG,MAAM;gBACV,CAAC,iBAAiB,GAAG,MAAM;;gBAE3B,CAAC,kBAAkB,GAAG,MAAM;gBAC5B,CAAC,kBAAkB,GAAG,MAAM;IAC5B,YAAA,iBAAiB,GAAG,MAAM;IAC1B,YAAA,CAAC,GAAG,MAAM;IACV,YAAA,kBAAkB,GAAG,MAAM;;IAE3B,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;IAED;;;;;;IAMG;QACI,iBAAG,CAAA,SAAA,CAAA,GAAA,GAAV,UAAW,QAAiB,EAAA;IAExB,QAAA,IAAM,MAAM,GAAgB;gBACxB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;gBACnB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACtC,CAAA;;IAGM,IAAA,iBAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,IAAM,MAAM,GAAgB;IACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;IAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SACnC,CAAA;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAM,CAAA,SAAA,EAAA,QAAA,EAAA;IALV;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC1B;IAED,QAAA,GAAA,EAAA,UAAW,KAAkB,EAAA;IAEzB,YAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;aAC3B;;;IALA,KAAA,CAAA,CAAA;IAeD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAK,CAAA,SAAA,EAAA,OAAA,EAAA;IART;;;;;;;IAOG;IACH,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;aAC/B;IAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;IAEnB,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;aAChC;;;IALA,KAAA,CAAA,CAAA;QAML,OAAC,iBAAA,CAAA;IAAD,CAxjBA,CAAuCC,WAAM,CAwjB5C,EAAA;IAED;IACA,iBAAiB,CAAC,SAAS,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC,SAAS;;;;;;;;;;;;;;;"}