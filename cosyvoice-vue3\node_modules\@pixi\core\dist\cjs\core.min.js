/*!
 * @pixi/core - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/core is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/settings"),t=require("@pixi/constants"),r=require("@pixi/utils"),i=require("@pixi/extensions"),n=require("@pixi/runner"),o=require("@pixi/ticker"),s=require("@pixi/math");e.settings.PREFER_ENV=r.isMobile.any?t.ENV.WEBGL:t.ENV.WEBGL2,e.settings.STRICT_TEXTURE_CACHE=!1;var a=[];function u(e,t){if(!e)return null;var r="";if("string"==typeof e){var i=/\.(\w{3,4})(?:$|\?|#)/i.exec(e);i&&(r=i[1].toLowerCase())}for(var n=a.length-1;n>=0;--n){var o=a[n];if(o.test&&o.test(e,r))return new o(e,t)}throw new Error("Unrecognized source type to auto-detect Resource")}var h=function(e,t){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},h(e,t)};function l(e,t){function r(){this.constructor=e}h(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return f=Object.assign||function(e){for(var t,r=arguments,i=1,n=arguments.length;i<n;i++)for(var o in t=r[i])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},f.apply(this,arguments)};var d=function(){function e(e,t){void 0===e&&(e=0),void 0===t&&(t=0),this._width=e,this._height=t,this.destroyed=!1,this.internal=!1,this.onResize=new n.Runner("setRealSize"),this.onUpdate=new n.Runner("update"),this.onError=new n.Runner("onError")}return e.prototype.bind=function(e){this.onResize.add(e),this.onUpdate.add(e),this.onError.add(e),(this._width||this._height)&&this.onResize.emit(this._width,this._height)},e.prototype.unbind=function(e){this.onResize.remove(e),this.onUpdate.remove(e),this.onError.remove(e)},e.prototype.resize=function(e,t){e===this._width&&t===this._height||(this._width=e,this._height=t,this.onResize.emit(e,t))},Object.defineProperty(e.prototype,"valid",{get:function(){return!!this._width&&!!this._height},enumerable:!1,configurable:!0}),e.prototype.update=function(){this.destroyed||this.onUpdate.emit()},e.prototype.load=function(){return Promise.resolve(this)},Object.defineProperty(e.prototype,"width",{get:function(){return this._width},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this._height},enumerable:!1,configurable:!0}),e.prototype.style=function(e,t,r){return!1},e.prototype.dispose=function(){},e.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.dispose(),this.onError.removeAll(),this.onError=null,this.onResize.removeAll(),this.onResize=null,this.onUpdate.removeAll(),this.onUpdate=null)},e.test=function(e,t){return!1},e}(),c=function(e){function r(t,r){var i=this,n=r||{},o=n.width,s=n.height;if(!o||!s)throw new Error("BufferResource width or height invalid");return(i=e.call(this,o,s)||this).data=t,i}return l(r,e),r.prototype.upload=function(e,r,i){var n=e.gl;n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,r.alphaMode===t.ALPHA_MODES.UNPACK);var o=r.realWidth,s=r.realHeight;return i.width===o&&i.height===s?n.texSubImage2D(r.target,0,0,0,o,s,r.format,i.type,this.data):(i.width=o,i.height=s,n.texImage2D(r.target,0,i.internalFormat,o,s,0,r.format,i.type,this.data)),!0},r.prototype.dispose=function(){this.data=null},r.test=function(e){return e instanceof Float32Array||e instanceof Uint8Array||e instanceof Uint32Array},r}(d),p={scaleMode:t.SCALE_MODES.NEAREST,format:t.FORMATS.RGBA,alphaMode:t.ALPHA_MODES.NPM},v=function(i){function n(n,o){void 0===n&&(n=null),void 0===o&&(o=null);var s=i.call(this)||this,a=(o=o||{}).alphaMode,h=o.mipmap,l=o.anisotropicLevel,f=o.scaleMode,c=o.width,p=o.height,v=o.wrapMode,m=o.format,g=o.type,_=o.target,y=o.resolution,E=o.resourceOptions;return!n||n instanceof d||((n=u(n,E)).internal=!0),s.resolution=y||e.settings.RESOLUTION,s.width=Math.round((c||0)*s.resolution)/s.resolution,s.height=Math.round((p||0)*s.resolution)/s.resolution,s._mipmap=void 0!==h?h:e.settings.MIPMAP_TEXTURES,s.anisotropicLevel=void 0!==l?l:e.settings.ANISOTROPIC_LEVEL,s._wrapMode=v||e.settings.WRAP_MODE,s._scaleMode=void 0!==f?f:e.settings.SCALE_MODE,s.format=m||t.FORMATS.RGBA,s.type=g||t.TYPES.UNSIGNED_BYTE,s.target=_||t.TARGETS.TEXTURE_2D,s.alphaMode=void 0!==a?a:t.ALPHA_MODES.UNPACK,s.uid=r.uid(),s.touched=0,s.isPowerOfTwo=!1,s._refreshPOT(),s._glTextures={},s.dirtyId=0,s.dirtyStyleId=0,s.cacheId=null,s.valid=c>0&&p>0,s.textureCacheIds=[],s.destroyed=!1,s.resource=null,s._batchEnabled=0,s._batchLocation=0,s.parentTextureArray=null,s.setResource(n),s}return l(n,i),Object.defineProperty(n.prototype,"realWidth",{get:function(){return Math.round(this.width*this.resolution)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"realHeight",{get:function(){return Math.round(this.height*this.resolution)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"mipmap",{get:function(){return this._mipmap},set:function(e){this._mipmap!==e&&(this._mipmap=e,this.dirtyStyleId++)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"scaleMode",{get:function(){return this._scaleMode},set:function(e){this._scaleMode!==e&&(this._scaleMode=e,this.dirtyStyleId++)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"wrapMode",{get:function(){return this._wrapMode},set:function(e){this._wrapMode!==e&&(this._wrapMode=e,this.dirtyStyleId++)},enumerable:!1,configurable:!0}),n.prototype.setStyle=function(e,t){var r;return void 0!==e&&e!==this.scaleMode&&(this.scaleMode=e,r=!0),void 0!==t&&t!==this.mipmap&&(this.mipmap=t,r=!0),r&&this.dirtyStyleId++,this},n.prototype.setSize=function(e,t,r){return r=r||this.resolution,this.setRealSize(e*r,t*r,r)},n.prototype.setRealSize=function(e,t,r){return this.resolution=r||this.resolution,this.width=Math.round(e)/this.resolution,this.height=Math.round(t)/this.resolution,this._refreshPOT(),this.update(),this},n.prototype._refreshPOT=function(){this.isPowerOfTwo=r.isPow2(this.realWidth)&&r.isPow2(this.realHeight)},n.prototype.setResolution=function(e){var t=this.resolution;return t===e||(this.resolution=e,this.valid&&(this.width=Math.round(this.width*t)/e,this.height=Math.round(this.height*t)/e,this.emit("update",this)),this._refreshPOT()),this},n.prototype.setResource=function(e){if(this.resource===e)return this;if(this.resource)throw new Error("Resource can be set only once");return e.bind(this),this.resource=e,this},n.prototype.update=function(){this.valid?(this.dirtyId++,this.dirtyStyleId++,this.emit("update",this)):this.width>0&&this.height>0&&(this.valid=!0,this.emit("loaded",this),this.emit("update",this))},n.prototype.onError=function(e){this.emit("error",this,e)},n.prototype.destroy=function(){this.resource&&(this.resource.unbind(this),this.resource.internal&&this.resource.destroy(),this.resource=null),this.cacheId&&(delete r.BaseTextureCache[this.cacheId],delete r.TextureCache[this.cacheId],this.cacheId=null),this.dispose(),n.removeFromCache(this),this.textureCacheIds=null,this.destroyed=!0},n.prototype.dispose=function(){this.emit("dispose",this)},n.prototype.castToBaseTexture=function(){return this},n.from=function(t,i,o){void 0===o&&(o=e.settings.STRICT_TEXTURE_CACHE);var s="string"==typeof t,a=null;if(s)a=t;else{if(!t._pixiId){var u=i&&i.pixiIdPrefix||"pixiid";t._pixiId=u+"_"+r.uid()}a=t._pixiId}var h=r.BaseTextureCache[a];if(s&&o&&!h)throw new Error('The cacheId "'+a+'" does not exist in BaseTextureCache.');return h||((h=new n(t,i)).cacheId=a,n.addToCache(h,a)),h},n.fromBuffer=function(e,r,i,o){e=e||new Float32Array(r*i*4);var s=new c(e,{width:r,height:i}),a=e instanceof Float32Array?t.TYPES.FLOAT:t.TYPES.UNSIGNED_BYTE;return new n(s,Object.assign({},p,o||{width:r,height:i,type:a}))},n.addToCache=function(e,t){t&&(-1===e.textureCacheIds.indexOf(t)&&e.textureCacheIds.push(t),r.BaseTextureCache[t]&&console.warn("BaseTexture added to the cache with an id ["+t+"] that already had an entry"),r.BaseTextureCache[t]=e)},n.removeFromCache=function(e){if("string"==typeof e){var t=r.BaseTextureCache[e];if(t){var i=t.textureCacheIds.indexOf(e);return i>-1&&t.textureCacheIds.splice(i,1),delete r.BaseTextureCache[e],t}}else if(e&&e.textureCacheIds){for(var n=0;n<e.textureCacheIds.length;++n)delete r.BaseTextureCache[e.textureCacheIds[n]];return e.textureCacheIds.length=0,e}return null},n._globalBatch=0,n}(r.EventEmitter),m=function(e){function t(t,r){var i=this,n=r||{},o=n.width,s=n.height;(i=e.call(this,o,s)||this).items=[],i.itemDirtyIds=[];for(var a=0;a<t;a++){var u=new v;i.items.push(u),i.itemDirtyIds.push(-2)}return i.length=t,i._load=null,i.baseTexture=null,i}return l(t,e),t.prototype.initFromArray=function(e,t){for(var r=0;r<this.length;r++)e[r]&&(e[r].castToBaseTexture?this.addBaseTextureAt(e[r].castToBaseTexture(),r):e[r]instanceof d?this.addResourceAt(e[r],r):this.addResourceAt(u(e[r],t),r))},t.prototype.dispose=function(){for(var e=0,t=this.length;e<t;e++)this.items[e].destroy();this.items=null,this.itemDirtyIds=null,this._load=null},t.prototype.addResourceAt=function(e,t){if(!this.items[t])throw new Error("Index "+t+" is out of bounds");return e.valid&&!this.valid&&this.resize(e.width,e.height),this.items[t].setResource(e),this},t.prototype.bind=function(t){if(null!==this.baseTexture)throw new Error("Only one base texture per TextureArray is allowed");e.prototype.bind.call(this,t);for(var r=0;r<this.length;r++)this.items[r].parentTextureArray=t,this.items[r].on("update",t.update,t)},t.prototype.unbind=function(t){e.prototype.unbind.call(this,t);for(var r=0;r<this.length;r++)this.items[r].parentTextureArray=null,this.items[r].off("update",t.update,t)},t.prototype.load=function(){var e=this;if(this._load)return this._load;var t=this.items.map((function(e){return e.resource})).filter((function(e){return e})).map((function(e){return e.load()}));return this._load=Promise.all(t).then((function(){var t=e.items[0],r=t.realWidth,i=t.realHeight;return e.resize(r,i),Promise.resolve(e)})),this._load},t}(d),g=function(e){function r(t,r){var i,n,o=this,s=r||{},a=s.width,u=s.height;return Array.isArray(t)?(i=t,n=t.length):n=t,o=e.call(this,n,{width:a,height:u})||this,i&&o.initFromArray(i,r),o}return l(r,e),r.prototype.addBaseTextureAt=function(e,t){if(!e.resource)throw new Error("ArrayResource does not support RenderTexture");return this.addResourceAt(e.resource,t),this},r.prototype.bind=function(r){e.prototype.bind.call(this,r),r.target=t.TARGETS.TEXTURE_2D_ARRAY},r.prototype.upload=function(e,t,r){var i=this,n=i.length,o=i.itemDirtyIds,s=i.items,a=e.gl;r.dirtyId<0&&a.texImage3D(a.TEXTURE_2D_ARRAY,0,r.internalFormat,this._width,this._height,n,0,t.format,r.type,null);for(var u=0;u<n;u++){var h=s[u];o[u]<h.dirtyId&&(o[u]=h.dirtyId,h.valid&&a.texSubImage3D(a.TEXTURE_2D_ARRAY,0,0,0,u,h.resource.width,h.resource.height,1,t.format,r.type,h.resource.source))}return!0},r}(m),_=function(e){function i(t){var r=this,i=t,n=i.naturalWidth||i.videoWidth||i.width,o=i.naturalHeight||i.videoHeight||i.height;return(r=e.call(this,n,o)||this).source=t,r.noSubImage=!1,r}return l(i,e),i.crossOrigin=function(e,t,i){void 0===i&&0!==t.indexOf("data:")?e.crossOrigin=r.determineCrossOrigin(t):!1!==i&&(e.crossOrigin="string"==typeof i?i:"anonymous")},i.prototype.upload=function(e,r,i,n){var o=e.gl,s=r.realWidth,a=r.realHeight;if((n=n||this.source)instanceof HTMLImageElement){if(!n.complete||0===n.naturalWidth)return!1}else if(n instanceof HTMLVideoElement&&n.readyState<=1)return!1;return o.pixelStorei(o.UNPACK_PREMULTIPLY_ALPHA_WEBGL,r.alphaMode===t.ALPHA_MODES.UNPACK),this.noSubImage||r.target!==o.TEXTURE_2D||i.width!==s||i.height!==a?(i.width=s,i.height=a,o.texImage2D(r.target,0,i.internalFormat,r.format,i.type,n)):o.texSubImage2D(o.TEXTURE_2D,0,0,0,r.format,i.type,n),!0},i.prototype.update=function(){if(!this.destroyed){var t=this.source,r=t.naturalWidth||t.videoWidth||t.width,i=t.naturalHeight||t.videoHeight||t.height;this.resize(r,i),e.prototype.update.call(this)}},i.prototype.dispose=function(){this.source=null},i}(d),y=function(e){function t(t){return e.call(this,t)||this}return l(t,e),t.test=function(e){var t=globalThis.OffscreenCanvas;return!!(t&&e instanceof t)||globalThis.HTMLCanvasElement&&e instanceof HTMLCanvasElement},t}(_),E=function(e){function r(i,n){var o=this,s=n||{},a=s.width,u=s.height,h=s.autoLoad,l=s.linkBaseTexture;if(i&&i.length!==r.SIDES)throw new Error("Invalid length. Got "+i.length+", expected 6");o=e.call(this,6,{width:a,height:u})||this;for(var f=0;f<r.SIDES;f++)o.items[f].target=t.TARGETS.TEXTURE_CUBE_MAP_POSITIVE_X+f;return o.linkBaseTexture=!1!==l,i&&o.initFromArray(i,n),!1!==h&&o.load(),o}return l(r,e),r.prototype.bind=function(r){e.prototype.bind.call(this,r),r.target=t.TARGETS.TEXTURE_CUBE_MAP},r.prototype.addBaseTextureAt=function(e,r,i){if(!this.items[r])throw new Error("Index "+r+" is out of bounds");if(!this.linkBaseTexture||e.parentTextureArray||Object.keys(e._glTextures).length>0){if(!e.resource)throw new Error("CubeResource does not support copying of renderTexture.");this.addResourceAt(e.resource,r)}else e.target=t.TARGETS.TEXTURE_CUBE_MAP_POSITIVE_X+r,e.parentTextureArray=this.baseTexture,this.items[r]=e;return e.valid&&!this.valid&&this.resize(e.realWidth,e.realHeight),this.items[r]=e,this},r.prototype.upload=function(e,t,i){for(var n=this.itemDirtyIds,o=0;o<r.SIDES;o++){var s=this.items[o];(n[o]<s.dirtyId||i.dirtyId<t.dirtyId)&&(s.valid&&s.resource?(s.resource.upload(e,s,i),n[o]=s.dirtyId):n[o]<-1&&(e.gl.texImage2D(s.target,0,i.internalFormat,t.realWidth,t.realHeight,0,t.format,i.type,null),n[o]=-1))}return!0},r.test=function(e){return Array.isArray(e)&&e.length===r.SIDES},r.SIDES=6,r}(m),T=function(r){function i(t,i){var n=this;if(i=i||{},!(t instanceof HTMLImageElement)){var o=new Image;_.crossOrigin(o,t,i.crossorigin),o.src=t,t=o}return n=r.call(this,t)||this,!t.complete&&n._width&&n._height&&(n._width=0,n._height=0),n.url=t.src,n._process=null,n.preserveBitmap=!1,n.createBitmap=(void 0!==i.createBitmap?i.createBitmap:e.settings.CREATE_IMAGE_BITMAP)&&!!globalThis.createImageBitmap,n.alphaMode="number"==typeof i.alphaMode?i.alphaMode:null,n.bitmap=null,n._load=null,!1!==i.autoLoad&&n.load(),n}return l(i,r),i.prototype.load=function(e){var t=this;return this._load||(void 0!==e&&(this.createBitmap=e),this._load=new Promise((function(e,r){var i=t.source;t.url=i.src;var n=function(){t.destroyed||(i.onload=null,i.onerror=null,t.resize(i.width,i.height),t._load=null,t.createBitmap?e(t.process()):e(t))};i.complete&&i.src?n():(i.onload=n,i.onerror=function(e){r(e),t.onError.emit(e)})}))),this._load},i.prototype.process=function(){var e=this,r=this.source;if(null!==this._process)return this._process;if(null!==this.bitmap||!globalThis.createImageBitmap)return Promise.resolve(this);var i=globalThis.createImageBitmap,n=!r.crossOrigin||"anonymous"===r.crossOrigin;return this._process=fetch(r.src,{mode:n?"cors":"no-cors"}).then((function(e){return e.blob()})).then((function(n){return i(n,0,0,r.width,r.height,{premultiplyAlpha:null===e.alphaMode||e.alphaMode===t.ALPHA_MODES.UNPACK?"premultiply":"none"})})).then((function(t){return e.destroyed?Promise.reject():(e.bitmap=t,e.update(),e._process=null,Promise.resolve(e))})),this._process},i.prototype.upload=function(e,t,i){if("number"==typeof this.alphaMode&&(t.alphaMode=this.alphaMode),!this.createBitmap)return r.prototype.upload.call(this,e,t,i);if(!this.bitmap&&(this.process(),!this.bitmap))return!1;if(r.prototype.upload.call(this,e,t,i,this.bitmap),!this.preserveBitmap){var n=!0,o=t._glTextures;for(var s in o){var a=o[s];if(a!==i&&a.dirtyId!==t.dirtyId){n=!1;break}}n&&(this.bitmap.close&&this.bitmap.close(),this.bitmap=null)}return!0},i.prototype.dispose=function(){this.source.onload=null,this.source.onerror=null,r.prototype.dispose.call(this),this.bitmap&&(this.bitmap.close(),this.bitmap=null),this._process=null,this._load=null},i.test=function(e){return"string"==typeof e||e instanceof HTMLImageElement},i}(_),x=function(t){function i(r,i){var n=this;return i=i||{},(n=t.call(this,e.settings.ADAPTER.createCanvas())||this)._width=0,n._height=0,n.svg=r,n.scale=i.scale||1,n._overrideWidth=i.width,n._overrideHeight=i.height,n._resolve=null,n._crossorigin=i.crossorigin,n._load=null,!1!==i.autoLoad&&n.load(),n}return l(i,t),i.prototype.load=function(){var e=this;return this._load||(this._load=new Promise((function(t){if(e._resolve=function(){e.resize(e.source.width,e.source.height),t(e)},i.SVG_XML.test(e.svg.trim())){if(!btoa)throw new Error("Your browser doesn't support base64 conversions.");e.svg="data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(e.svg)))}e._loadSvg()}))),this._load},i.prototype._loadSvg=function(){var e=this,t=new Image;_.crossOrigin(t,this.svg,this._crossorigin),t.src=this.svg,t.onerror=function(r){e._resolve&&(t.onerror=null,e.onError.emit(r))},t.onload=function(){if(e._resolve){var i=t.width,n=t.height;if(!i||!n)throw new Error("The SVG image must have width and height defined (in pixels), canvas API needs them.");var o=i*e.scale,s=n*e.scale;(e._overrideWidth||e._overrideHeight)&&(o=e._overrideWidth||e._overrideHeight/n*i,s=e._overrideHeight||e._overrideWidth/i*n),o=Math.round(o),s=Math.round(s);var a=e.source;a.width=o,a.height=s,a._pixiId="canvas_"+r.uid(),a.getContext("2d").drawImage(t,0,0,i,n,0,0,o,s),e._resolve(),e._resolve=null}}},i.getSize=function(e){var t=i.SVG_SIZE.exec(e),r={};return t&&(r[t[1]]=Math.round(parseFloat(t[3])),r[t[5]]=Math.round(parseFloat(t[7]))),r},i.prototype.dispose=function(){t.prototype.dispose.call(this),this._resolve=null,this._crossorigin=null},i.test=function(e,t){return"svg"===t||"string"==typeof e&&e.startsWith("data:image/svg+xml")||"string"==typeof e&&i.SVG_XML.test(e)},i.SVG_XML=/^(<\?xml[^?]+\?>)?\s*(<!--[^(-->)]*-->)?\s*\<svg/m,i.SVG_SIZE=/<svg[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*>/i,i}(_),b=function(e){function t(r,i){var n=this;if(i=i||{},!(r instanceof HTMLVideoElement)){var o=document.createElement("video");o.setAttribute("preload","auto"),o.setAttribute("webkit-playsinline",""),o.setAttribute("playsinline",""),"string"==typeof r&&(r=[r]);var s=r[0].src||r[0];_.crossOrigin(o,s,i.crossorigin);for(var a=0;a<r.length;++a){var u=document.createElement("source"),h=r[a],l=h.src,f=h.mime,d=(l=l||r[a]).split("?").shift().toLowerCase(),c=d.slice(d.lastIndexOf(".")+1);f=f||t.MIME_TYPES[c]||"video/"+c,u.src=l,u.type=f,o.appendChild(u)}r=o}return(n=e.call(this,r)||this).noSubImage=!0,n._autoUpdate=!0,n._isConnectedToTicker=!1,n._updateFPS=i.updateFPS||0,n._msToNextUpdate=0,n.autoPlay=!1!==i.autoPlay,n._load=null,n._resolve=null,n._onCanPlay=n._onCanPlay.bind(n),n._onError=n._onError.bind(n),!1!==i.autoLoad&&n.load(),n}return l(t,e),t.prototype.update=function(t){if(!this.destroyed){var r=o.Ticker.shared.elapsedMS*this.source.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-r),(!this._updateFPS||this._msToNextUpdate<=0)&&(e.prototype.update.call(this),this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0)}},t.prototype.load=function(){var e=this;if(this._load)return this._load;var t=this.source;return(t.readyState===t.HAVE_ENOUGH_DATA||t.readyState===t.HAVE_FUTURE_DATA)&&t.width&&t.height&&(t.complete=!0),t.addEventListener("play",this._onPlayStart.bind(this)),t.addEventListener("pause",this._onPlayStop.bind(this)),this._isSourceReady()?this._onCanPlay():(t.addEventListener("canplay",this._onCanPlay),t.addEventListener("canplaythrough",this._onCanPlay),t.addEventListener("error",this._onError,!0)),this._load=new Promise((function(r){e.valid?r(e):(e._resolve=r,t.load())})),this._load},t.prototype._onError=function(e){this.source.removeEventListener("error",this._onError,!0),this.onError.emit(e)},t.prototype._isSourcePlaying=function(){var e=this.source;return!e.paused&&!e.ended&&this._isSourceReady()},t.prototype._isSourceReady=function(){return this.source.readyState>2},t.prototype._onPlayStart=function(){this.valid||this._onCanPlay(),this.autoUpdate&&!this._isConnectedToTicker&&(o.Ticker.shared.add(this.update,this),this._isConnectedToTicker=!0)},t.prototype._onPlayStop=function(){this._isConnectedToTicker&&(o.Ticker.shared.remove(this.update,this),this._isConnectedToTicker=!1)},t.prototype._onCanPlay=function(){var e=this.source;e.removeEventListener("canplay",this._onCanPlay),e.removeEventListener("canplaythrough",this._onCanPlay);var t=this.valid;this.resize(e.videoWidth,e.videoHeight),!t&&this._resolve&&(this._resolve(this),this._resolve=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&e.play()},t.prototype.dispose=function(){this._isConnectedToTicker&&(o.Ticker.shared.remove(this.update,this),this._isConnectedToTicker=!1);var t=this.source;t&&(t.removeEventListener("error",this._onError,!0),t.pause(),t.src="",t.load()),e.prototype.dispose.call(this)},Object.defineProperty(t.prototype,"autoUpdate",{get:function(){return this._autoUpdate},set:function(e){e!==this._autoUpdate&&(this._autoUpdate=e,!this._autoUpdate&&this._isConnectedToTicker?(o.Ticker.shared.remove(this.update,this),this._isConnectedToTicker=!1):this._autoUpdate&&!this._isConnectedToTicker&&this._isSourcePlaying()&&(o.Ticker.shared.add(this.update,this),this._isConnectedToTicker=!0))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"updateFPS",{get:function(){return this._updateFPS},set:function(e){e!==this._updateFPS&&(this._updateFPS=e)},enumerable:!1,configurable:!0}),t.test=function(e,r){return globalThis.HTMLVideoElement&&e instanceof HTMLVideoElement||t.TYPES.indexOf(r)>-1},t.TYPES=["mp4","m4v","webm","ogg","ogv","h264","avi","mov"],t.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"},t}(_),S=function(e){function t(t){return e.call(this,t)||this}return l(t,e),t.test=function(e){return!!globalThis.createImageBitmap&&"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap},t}(_);a.push(T,S,y,b,x,c,E,g);var R={__proto__:null,Resource:d,BaseImageResource:_,INSTALLED:a,autoDetectResource:u,AbstractMultiResource:m,ArrayResource:g,BufferResource:c,CanvasResource:y,CubeResource:E,ImageResource:T,SVGResource:x,VideoResource:b,ImageBitmapResource:S},A=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return l(r,e),r.prototype.upload=function(e,r,i){var n=e.gl;n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,r.alphaMode===t.ALPHA_MODES.UNPACK);var o=r.realWidth,s=r.realHeight;return i.width===o&&i.height===s?n.texSubImage2D(r.target,0,0,0,o,s,r.format,i.type,this.data):(i.width=o,i.height=s,n.texImage2D(r.target,0,i.internalFormat,o,s,0,r.format,i.type,this.data)),!0},r}(c),M=function(){function e(e,r){this.width=Math.round(e||100),this.height=Math.round(r||100),this.stencil=!1,this.depth=!1,this.dirtyId=0,this.dirtyFormat=0,this.dirtySize=0,this.depthTexture=null,this.colorTextures=[],this.glFramebuffers={},this.disposeRunner=new n.Runner("disposeFramebuffer"),this.multisample=t.MSAA_QUALITY.NONE}return Object.defineProperty(e.prototype,"colorTexture",{get:function(){return this.colorTextures[0]},enumerable:!1,configurable:!0}),e.prototype.addColorTexture=function(e,r){return void 0===e&&(e=0),this.colorTextures[e]=r||new v(null,{scaleMode:t.SCALE_MODES.NEAREST,resolution:1,mipmap:t.MIPMAP_MODES.OFF,width:this.width,height:this.height}),this.dirtyId++,this.dirtyFormat++,this},e.prototype.addDepthTexture=function(e){return this.depthTexture=e||new v(new A(null,{width:this.width,height:this.height}),{scaleMode:t.SCALE_MODES.NEAREST,resolution:1,width:this.width,height:this.height,mipmap:t.MIPMAP_MODES.OFF,format:t.FORMATS.DEPTH_COMPONENT,type:t.TYPES.UNSIGNED_SHORT}),this.dirtyId++,this.dirtyFormat++,this},e.prototype.enableDepth=function(){return this.depth=!0,this.dirtyId++,this.dirtyFormat++,this},e.prototype.enableStencil=function(){return this.stencil=!0,this.dirtyId++,this.dirtyFormat++,this},e.prototype.resize=function(e,t){if(e=Math.round(e),t=Math.round(t),e!==this.width||t!==this.height){this.width=e,this.height=t,this.dirtyId++,this.dirtySize++;for(var r=0;r<this.colorTextures.length;r++){var i=this.colorTextures[r],n=i.resolution;i.setSize(e/n,t/n)}if(this.depthTexture){n=this.depthTexture.resolution;this.depthTexture.setSize(e/n,t/n)}}},e.prototype.dispose=function(){this.disposeRunner.emit(this,!1)},e.prototype.destroyDepthTexture=function(){this.depthTexture&&(this.depthTexture.destroy(),this.depthTexture=null,++this.dirtyId,++this.dirtyFormat)},e}(),w=function(e){function r(r){void 0===r&&(r={});var i=this;if("number"==typeof r){var n=arguments[0],o=arguments[1],s=arguments[2],a=arguments[3];r={width:n,height:o,scaleMode:s,resolution:a}}return r.width=r.width||100,r.height=r.height||100,r.multisample=void 0!==r.multisample?r.multisample:t.MSAA_QUALITY.NONE,(i=e.call(this,null,r)||this).mipmap=t.MIPMAP_MODES.OFF,i.valid=!0,i.clearColor=[0,0,0,0],i.framebuffer=new M(i.realWidth,i.realHeight).addColorTexture(0,i),i.framebuffer.multisample=r.multisample,i.maskStack=[],i.filterStack=[{}],i}return l(r,e),r.prototype.resize=function(e,t){this.framebuffer.resize(e*this.resolution,t*this.resolution),this.setRealSize(this.framebuffer.width,this.framebuffer.height)},r.prototype.dispose=function(){this.framebuffer.dispose(),e.prototype.dispose.call(this)},r.prototype.destroy=function(){e.prototype.destroy.call(this),this.framebuffer.destroyDepthTexture(),this.framebuffer=null},r}(v),I=function(){function e(){this.x0=0,this.y0=0,this.x1=1,this.y1=0,this.x2=1,this.y2=1,this.x3=0,this.y3=1,this.uvsFloat32=new Float32Array(8)}return e.prototype.set=function(e,t,r){var i=t.width,n=t.height;if(r){var o=e.width/2/i,a=e.height/2/n,u=e.x/i+o,h=e.y/n+a;r=s.groupD8.add(r,s.groupD8.NW),this.x0=u+o*s.groupD8.uX(r),this.y0=h+a*s.groupD8.uY(r),r=s.groupD8.add(r,2),this.x1=u+o*s.groupD8.uX(r),this.y1=h+a*s.groupD8.uY(r),r=s.groupD8.add(r,2),this.x2=u+o*s.groupD8.uX(r),this.y2=h+a*s.groupD8.uY(r),r=s.groupD8.add(r,2),this.x3=u+o*s.groupD8.uX(r),this.y3=h+a*s.groupD8.uY(r)}else this.x0=e.x/i,this.y0=e.y/n,this.x1=(e.x+e.width)/i,this.y1=e.y/n,this.x2=(e.x+e.width)/i,this.y2=(e.y+e.height)/n,this.x3=e.x/i,this.y3=(e.y+e.height)/n;this.uvsFloat32[0]=this.x0,this.uvsFloat32[1]=this.y0,this.uvsFloat32[2]=this.x1,this.uvsFloat32[3]=this.y1,this.uvsFloat32[4]=this.x2,this.uvsFloat32[5]=this.y2,this.uvsFloat32[6]=this.x3,this.uvsFloat32[7]=this.y3},e}(),C=new I;function O(e){e.destroy=function(){},e.on=function(){},e.once=function(){},e.emit=function(){}}var F=function(t){function i(e,r,n,o,a,u){var h=t.call(this)||this;if(h.noFrame=!1,r||(h.noFrame=!0,r=new s.Rectangle(0,0,1,1)),e instanceof i&&(e=e.baseTexture),h.baseTexture=e,h._frame=r,h.trim=o,h.valid=!1,h._uvs=C,h.uvMatrix=null,h.orig=n||r,h._rotate=Number(a||0),!0===a)h._rotate=2;else if(h._rotate%2!=0)throw new Error("attempt to use diamond-shaped UVs. If you are sure, set rotation manually");return h.defaultAnchor=u?new s.Point(u.x,u.y):new s.Point(0,0),h._updateID=0,h.textureCacheIds=[],e.valid?h.noFrame?e.valid&&h.onBaseTextureUpdated(e):h.frame=r:e.once("loaded",h.onBaseTextureUpdated,h),h.noFrame&&e.on("update",h.onBaseTextureUpdated,h),h}return l(i,t),i.prototype.update=function(){this.baseTexture.resource&&this.baseTexture.resource.update()},i.prototype.onBaseTextureUpdated=function(e){if(this.noFrame){if(!this.baseTexture.valid)return;this._frame.width=e.width,this._frame.height=e.height,this.valid=!0,this.updateUvs()}else this.frame=this._frame;this.emit("update",this)},i.prototype.destroy=function(e){if(this.baseTexture){if(e){var t=this.baseTexture.resource;t&&t.url&&r.TextureCache[t.url]&&i.removeFromCache(t.url),this.baseTexture.destroy()}this.baseTexture.off("loaded",this.onBaseTextureUpdated,this),this.baseTexture.off("update",this.onBaseTextureUpdated,this),this.baseTexture=null}this._frame=null,this._uvs=null,this.trim=null,this.orig=null,this.valid=!1,i.removeFromCache(this),this.textureCacheIds=null},i.prototype.clone=function(){var e=this._frame.clone(),t=this._frame===this.orig?e:this.orig.clone(),r=new i(this.baseTexture,!this.noFrame&&e,t,this.trim&&this.trim.clone(),this.rotate,this.defaultAnchor);return this.noFrame&&(r._frame=e),r},i.prototype.updateUvs=function(){this._uvs===C&&(this._uvs=new I),this._uvs.set(this._frame,this.baseTexture,this.rotate),this._updateID++},i.from=function(t,n,o){void 0===n&&(n={}),void 0===o&&(o=e.settings.STRICT_TEXTURE_CACHE);var s="string"==typeof t,a=null;if(s)a=t;else if(t instanceof v){if(!t.cacheId){var u=n&&n.pixiIdPrefix||"pixiid";t.cacheId=u+"-"+r.uid(),v.addToCache(t,t.cacheId)}a=t.cacheId}else{if(!t._pixiId){u=n&&n.pixiIdPrefix||"pixiid";t._pixiId=u+"_"+r.uid()}a=t._pixiId}var h=r.TextureCache[a];if(s&&o&&!h)throw new Error('The cacheId "'+a+'" does not exist in TextureCache.');return h||t instanceof v?!h&&t instanceof v&&(h=new i(t),i.addToCache(h,a)):(n.resolution||(n.resolution=r.getResolutionOfUrl(t)),(h=new i(new v(t,n))).baseTexture.cacheId=a,v.addToCache(h.baseTexture,a),i.addToCache(h,a)),h},i.fromURL=function(e,t){var r=Object.assign({autoLoad:!1},null==t?void 0:t.resourceOptions),n=i.from(e,Object.assign({resourceOptions:r},t),!1),o=n.baseTexture.resource;return n.baseTexture.valid?Promise.resolve(n):o.load().then((function(){return Promise.resolve(n)}))},i.fromBuffer=function(e,t,r,n){return new i(v.fromBuffer(e,t,r,n))},i.fromLoader=function(t,n,o,s){var a=new v(t,Object.assign({scaleMode:e.settings.SCALE_MODE,resolution:r.getResolutionOfUrl(n)},s)),u=a.resource;u instanceof T&&(u.url=n);var h=new i(a);return o||(o=n),v.addToCache(h.baseTexture,o),i.addToCache(h,o),o!==n&&(v.addToCache(h.baseTexture,n),i.addToCache(h,n)),h.baseTexture.valid?Promise.resolve(h):new Promise((function(e){h.baseTexture.once("loaded",(function(){return e(h)}))}))},i.addToCache=function(e,t){t&&(-1===e.textureCacheIds.indexOf(t)&&e.textureCacheIds.push(t),r.TextureCache[t]&&console.warn("Texture added to the cache with an id ["+t+"] that already had an entry"),r.TextureCache[t]=e)},i.removeFromCache=function(e){if("string"==typeof e){var t=r.TextureCache[e];if(t){var i=t.textureCacheIds.indexOf(e);return i>-1&&t.textureCacheIds.splice(i,1),delete r.TextureCache[e],t}}else if(e&&e.textureCacheIds){for(var n=0;n<e.textureCacheIds.length;++n)r.TextureCache[e.textureCacheIds[n]]===e&&delete r.TextureCache[e.textureCacheIds[n]];return e.textureCacheIds.length=0,e}return null},Object.defineProperty(i.prototype,"resolution",{get:function(){return this.baseTexture.resolution},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"frame",{get:function(){return this._frame},set:function(e){this._frame=e,this.noFrame=!1;var t=e.x,r=e.y,i=e.width,n=e.height,o=t+i>this.baseTexture.width,s=r+n>this.baseTexture.height;if(o||s){var a=o&&s?"and":"or",u="X: "+t+" + "+i+" = "+(t+i)+" > "+this.baseTexture.width,h="Y: "+r+" + "+n+" = "+(r+n)+" > "+this.baseTexture.height;throw new Error("Texture Error: frame does not fit inside the base Texture dimensions: "+u+" "+a+" "+h)}this.valid=i&&n&&this.baseTexture.valid,this.trim||this.rotate||(this.orig=e),this.valid&&this.updateUvs()},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"rotate",{get:function(){return this._rotate},set:function(e){this._rotate=e,this.valid&&this.updateUvs()},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"width",{get:function(){return this.orig.width},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"height",{get:function(){return this.orig.height},enumerable:!1,configurable:!0}),i.prototype.castToBaseTexture=function(){return this.baseTexture},Object.defineProperty(i,"EMPTY",{get:function(){return i._EMPTY||(i._EMPTY=new i(new v),O(i._EMPTY),O(i._EMPTY.baseTexture)),i._EMPTY},enumerable:!1,configurable:!0}),Object.defineProperty(i,"WHITE",{get:function(){if(!i._WHITE){var t=e.settings.ADAPTER.createCanvas(16,16),r=t.getContext("2d");t.width=16,t.height=16,r.fillStyle="white",r.fillRect(0,0,16,16),i._WHITE=new i(v.from(t)),O(i._WHITE),O(i._WHITE.baseTexture)}return i._WHITE},enumerable:!1,configurable:!0}),i}(r.EventEmitter),P=function(e){function t(t,r){var i=e.call(this,t,r)||this;return i.valid=!0,i.filterFrame=null,i.filterPoolKey=null,i.updateUvs(),i}return l(t,e),Object.defineProperty(t.prototype,"framebuffer",{get:function(){return this.baseTexture.framebuffer},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"multisample",{get:function(){return this.framebuffer.multisample},set:function(e){this.framebuffer.multisample=e},enumerable:!1,configurable:!0}),t.prototype.resize=function(e,t,r){void 0===r&&(r=!0);var i=this.baseTexture.resolution,n=Math.round(e*i)/i,o=Math.round(t*i)/i;this.valid=n>0&&o>0,this._frame.width=this.orig.width=n,this._frame.height=this.orig.height=o,r&&this.baseTexture.resize(n,o),this.updateUvs()},t.prototype.setResolution=function(e){var t=this.baseTexture;t.resolution!==e&&(t.setResolution(e),this.resize(t.width,t.height,!1))},t.create=function(e){for(var r=arguments,i=[],n=1;n<arguments.length;n++)i[n-1]=r[n];return"number"==typeof e&&(e={width:e,height:i[0],scaleMode:i[1],resolution:i[2]}),new t(new w(e))},t}(F),N=function(){function e(e){this.texturePool={},this.textureOptions=e||{},this.enableFullScreen=!1,this._pixelsWidth=0,this._pixelsHeight=0}return e.prototype.createTexture=function(e,r,i){void 0===i&&(i=t.MSAA_QUALITY.NONE);var n=new w(Object.assign({width:e,height:r,resolution:1,multisample:i},this.textureOptions));return new P(n)},e.prototype.getOptimalTexture=function(e,i,n,o){var s;void 0===n&&(n=1),void 0===o&&(o=t.MSAA_QUALITY.NONE),e=Math.ceil(e*n-1e-6),i=Math.ceil(i*n-1e-6),this.enableFullScreen&&e===this._pixelsWidth&&i===this._pixelsHeight?s=o>1?-o:-1:(s=((65535&(e=r.nextPow2(e)))<<16|65535&(i=r.nextPow2(i)))>>>0,o>1&&(s+=4294967296*o)),this.texturePool[s]||(this.texturePool[s]=[]);var a=this.texturePool[s].pop();return a||(a=this.createTexture(e,i,o)),a.filterPoolKey=s,a.setResolution(n),a},e.prototype.getFilterTexture=function(e,r,i){var n=this.getOptimalTexture(e.width,e.height,r||e.resolution,i||t.MSAA_QUALITY.NONE);return n.filterFrame=e.filterFrame,n},e.prototype.returnTexture=function(e){var t=e.filterPoolKey;e.filterFrame=null,this.texturePool[t].push(e)},e.prototype.returnFilterTexture=function(e){this.returnTexture(e)},e.prototype.clear=function(e){if(e=!1!==e)for(var t in this.texturePool){var r=this.texturePool[t];if(r)for(var i=0;i<r.length;i++)r[i].destroy(!0)}this.texturePool={}},e.prototype.setScreenSize=function(e){if(e.width!==this._pixelsWidth||e.height!==this._pixelsHeight){for(var t in this.enableFullScreen=e.width>0&&e.height>0,this.texturePool)if(Number(t)<0){var r=this.texturePool[t];if(r)for(var i=0;i<r.length;i++)r[i].destroy(!0);this.texturePool[t]=[]}this._pixelsWidth=e.width,this._pixelsHeight=e.height}},e.SCREEN_KEY=-1,e}(),B=function(){function e(e,r,i,n,o,s,a){void 0===r&&(r=0),void 0===i&&(i=!1),void 0===n&&(n=t.TYPES.FLOAT),this.buffer=e,this.size=r,this.normalized=i,this.type=n,this.stride=o,this.start=s,this.instance=a}return e.prototype.destroy=function(){this.buffer=null},e.from=function(t,r,i,n,o){return new e(t,r,i,n,o)},e}(),D=0,L=function(){function e(e,t,r){void 0===t&&(t=!0),void 0===r&&(r=!1),this.data=e||new Float32Array(1),this._glBuffers={},this._updateID=0,this.index=r,this.static=t,this.id=D++,this.disposeRunner=new n.Runner("disposeBuffer")}return e.prototype.update=function(e){e instanceof Array&&(e=new Float32Array(e)),this.data=e||this.data,this._updateID++},e.prototype.dispose=function(){this.disposeRunner.emit(this,!1)},e.prototype.destroy=function(){this.dispose(),this.data=null},Object.defineProperty(e.prototype,"index",{get:function(){return this.type===t.BUFFER_TYPE.ELEMENT_ARRAY_BUFFER},set:function(e){this.type=e?t.BUFFER_TYPE.ELEMENT_ARRAY_BUFFER:t.BUFFER_TYPE.ARRAY_BUFFER},enumerable:!1,configurable:!0}),e.from=function(t){return t instanceof Array&&(t=new Float32Array(t)),new e(t)},e}(),U={Float32Array:Float32Array,Uint32Array:Uint32Array,Int32Array:Int32Array,Uint8Array:Uint8Array};var G={5126:4,5123:2,5121:1},k=0,V={Float32Array:Float32Array,Uint32Array:Uint32Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array},H=function(){function e(e,t){void 0===e&&(e=[]),void 0===t&&(t={}),this.buffers=e,this.indexBuffer=null,this.attributes=t,this.glVertexArrayObjects={},this.id=k++,this.instanced=!1,this.instanceCount=1,this.disposeRunner=new n.Runner("disposeGeometry"),this.refCount=0}return e.prototype.addAttribute=function(e,t,r,i,n,o,s,a){if(void 0===r&&(r=0),void 0===i&&(i=!1),void 0===a&&(a=!1),!t)throw new Error("You must pass a buffer when creating an attribute");t instanceof L||(t instanceof Array&&(t=new Float32Array(t)),t=new L(t));var u=e.split("|");if(u.length>1){for(var h=0;h<u.length;h++)this.addAttribute(u[h],t,r,i,n);return this}var l=this.buffers.indexOf(t);return-1===l&&(this.buffers.push(t),l=this.buffers.length-1),this.attributes[e]=new B(l,r,i,n,o,s,a),this.instanced=this.instanced||a,this},e.prototype.getAttribute=function(e){return this.attributes[e]},e.prototype.getBuffer=function(e){return this.buffers[this.getAttribute(e).buffer]},e.prototype.addIndex=function(e){return e instanceof L||(e instanceof Array&&(e=new Uint16Array(e)),e=new L(e)),e.type=t.BUFFER_TYPE.ELEMENT_ARRAY_BUFFER,this.indexBuffer=e,-1===this.buffers.indexOf(e)&&this.buffers.push(e),this},e.prototype.getIndex=function(){return this.indexBuffer},e.prototype.interleave=function(){if(1===this.buffers.length||2===this.buffers.length&&this.indexBuffer)return this;var e,t=[],i=[],n=new L;for(e in this.attributes){var o=this.attributes[e],s=this.buffers[o.buffer];t.push(s.data),i.push(o.size*G[o.type]/4),o.buffer=0}for(n.data=function(e,t){for(var i=0,n=0,o={},s=0;s<e.length;s++)n+=t[s],i+=e[s].length;var a=new ArrayBuffer(4*i),u=null,h=0;for(s=0;s<e.length;s++){var l=t[s],f=e[s],d=r.getBufferType(f);o[d]||(o[d]=new U[d](a)),u=o[d];for(var c=0;c<f.length;c++)u[(c/l|0)*n+h+c%l]=f[c];h+=l}return new Float32Array(a)}(t,i),e=0;e<this.buffers.length;e++)this.buffers[e]!==this.indexBuffer&&this.buffers[e].destroy();return this.buffers=[n],this.indexBuffer&&this.buffers.push(this.indexBuffer),this},e.prototype.getSize=function(){for(var e in this.attributes){var t=this.attributes[e];return this.buffers[t.buffer].data.length/(t.stride/4||t.size)}return 0},e.prototype.dispose=function(){this.disposeRunner.emit(this,!1)},e.prototype.destroy=function(){this.dispose(),this.buffers=null,this.indexBuffer=null,this.attributes=null},e.prototype.clone=function(){for(var r=new e,i=0;i<this.buffers.length;i++)r.buffers[i]=new L(this.buffers[i].data.slice(0));for(var i in this.attributes){var n=this.attributes[i];r.attributes[i]=new B(n.buffer,n.size,n.normalized,n.type,n.stride,n.start,n.instance)}return this.indexBuffer&&(r.indexBuffer=r.buffers[this.buffers.indexOf(this.indexBuffer)],r.indexBuffer.type=t.BUFFER_TYPE.ELEMENT_ARRAY_BUFFER),r},e.merge=function(i){for(var n,o=new e,s=[],a=[],u=[],h=0;h<i.length;h++){n=i[h];for(var l=0;l<n.buffers.length;l++)a[l]=a[l]||0,a[l]+=n.buffers[l].data.length,u[l]=0}for(h=0;h<n.buffers.length;h++)s[h]=new(V[r.getBufferType(n.buffers[h].data)])(a[h]),o.buffers[h]=new L(s[h]);for(h=0;h<i.length;h++){n=i[h];for(l=0;l<n.buffers.length;l++)s[l].set(n.buffers[l].data,u[l]),u[l]+=n.buffers[l].data.length}if(o.attributes=n.attributes,n.indexBuffer){o.indexBuffer=o.buffers[n.buffers.indexOf(n.indexBuffer)],o.indexBuffer.type=t.BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;var f=0,d=0,c=0,p=0;for(h=0;h<n.buffers.length;h++)if(n.buffers[h]!==n.indexBuffer){p=h;break}for(var h in n.attributes){var v=n.attributes[h];(0|v.buffer)===p&&(d+=v.size*G[v.type]/4)}for(h=0;h<i.length;h++){var m=i[h].indexBuffer.data;for(l=0;l<m.length;l++)o.indexBuffer.data[l+c]+=f;f+=i[h].buffers[p].data.length/d,c+=m.length}}return o},e}(),j=function(e){function t(){var t=e.call(this)||this;return t.addAttribute("aVertexPosition",new Float32Array([0,0,1,0,1,1,0,1])).addIndex([0,1,3,2]),t}return l(t,e),t}(H),z=function(e){function t(){var t=e.call(this)||this;return t.vertices=new Float32Array([-1,-1,1,-1,1,1,-1,1]),t.uvs=new Float32Array([0,0,1,0,1,1,0,1]),t.vertexBuffer=new L(t.vertices),t.uvBuffer=new L(t.uvs),t.addAttribute("aVertexPosition",t.vertexBuffer).addAttribute("aTextureCoord",t.uvBuffer).addIndex([0,1,2,0,2,3]),t}return l(t,e),t.prototype.map=function(e,t){var r=0,i=0;return this.uvs[0]=r,this.uvs[1]=i,this.uvs[2]=r+t.width/e.width,this.uvs[3]=i,this.uvs[4]=r+t.width/e.width,this.uvs[5]=i+t.height/e.height,this.uvs[6]=r,this.uvs[7]=i+t.height/e.height,r=t.x,i=t.y,this.vertices[0]=r,this.vertices[1]=i,this.vertices[2]=r+t.width,this.vertices[3]=i,this.vertices[4]=r+t.width,this.vertices[5]=i+t.height,this.vertices[6]=r,this.vertices[7]=i+t.height,this.invalidate(),this},t.prototype.invalidate=function(){return this.vertexBuffer._updateID++,this.uvBuffer._updateID++,this},t}(H),X=0,Y=function(){function e(e,r,i){this.group=!0,this.syncUniforms={},this.dirtyId=0,this.id=X++,this.static=!!r,this.ubo=!!i,e instanceof L?(this.buffer=e,this.buffer.type=t.BUFFER_TYPE.UNIFORM_BUFFER,this.autoManage=!1,this.ubo=!0):(this.uniforms=e,this.ubo&&(this.buffer=new L(new Float32Array(1)),this.buffer.type=t.BUFFER_TYPE.UNIFORM_BUFFER,this.autoManage=!0))}return e.prototype.update=function(){this.dirtyId++,!this.autoManage&&this.buffer&&this.buffer.update()},e.prototype.add=function(t,r,i){if(this.ubo)throw new Error("[UniformGroup] uniform groups in ubo mode cannot be modified, or have uniform groups nested in them");this.uniforms[t]=new e(r,i)},e.from=function(t,r,i){return new e(t,r,i)},e.uboFrom=function(t,r){return new e(t,null==r||r,!0)},e}(),W=function(){function e(){this.renderTexture=null,this.target=null,this.legacy=!1,this.resolution=1,this.multisample=t.MSAA_QUALITY.NONE,this.sourceFrame=new s.Rectangle,this.destinationFrame=new s.Rectangle,this.bindingSourceFrame=new s.Rectangle,this.bindingDestinationFrame=new s.Rectangle,this.filters=[],this.transform=null}return e.prototype.clear=function(){this.target=null,this.filters=null,this.renderTexture=null},e}(),K=[new s.Point,new s.Point,new s.Point,new s.Point],Q=new s.Matrix,q=function(){function e(e){this.renderer=e,this.defaultFilterStack=[{}],this.texturePool=new N,this.texturePool.setScreenSize(e.view),this.statePool=[],this.quad=new j,this.quadUv=new z,this.tempRect=new s.Rectangle,this.activeState={},this.globalUniforms=new Y({outputFrame:new s.Rectangle,inputSize:new Float32Array(4),inputPixel:new Float32Array(4),inputClamp:new Float32Array(4),resolution:1,filterArea:new Float32Array(4),filterClamp:new Float32Array(4)},!0),this.forceClear=!1,this.useMaxPadding=!1}return e.prototype.push=function(e,t){for(var r,i,n=this.renderer,o=this.defaultFilterStack,s=this.statePool.pop()||new W,a=this.renderer.renderTexture,u=t[0].resolution,h=t[0].multisample,l=t[0].padding,f=t[0].autoFit,d=null===(r=t[0].legacy)||void 0===r||r,c=1;c<t.length;c++){var p=t[c];u=Math.min(u,p.resolution),h=Math.min(h,p.multisample),l=this.useMaxPadding?Math.max(l,p.padding):l+p.padding,f=f&&p.autoFit,d=d||null===(i=p.legacy)||void 0===i||i}1===o.length&&(this.defaultFilterStack[0].renderTexture=a.current),o.push(s),s.resolution=u,s.multisample=h,s.legacy=d,s.target=e,s.sourceFrame.copyFrom(e.filterArea||e.getBounds(!0)),s.sourceFrame.pad(l);var v=this.tempRect.copyFrom(a.sourceFrame);n.projection.transform&&this.transformAABB(Q.copyFrom(n.projection.transform).invert(),v),f?(s.sourceFrame.fit(v),(s.sourceFrame.width<=0||s.sourceFrame.height<=0)&&(s.sourceFrame.width=0,s.sourceFrame.height=0)):s.sourceFrame.intersects(v)||(s.sourceFrame.width=0,s.sourceFrame.height=0),this.roundFrame(s.sourceFrame,a.current?a.current.resolution:n.resolution,a.sourceFrame,a.destinationFrame,n.projection.transform),s.renderTexture=this.getOptimalFilterTexture(s.sourceFrame.width,s.sourceFrame.height,u,h),s.filters=t,s.destinationFrame.width=s.renderTexture.width,s.destinationFrame.height=s.renderTexture.height;var m=this.tempRect;m.x=0,m.y=0,m.width=s.sourceFrame.width,m.height=s.sourceFrame.height,s.renderTexture.filterFrame=s.sourceFrame,s.bindingSourceFrame.copyFrom(a.sourceFrame),s.bindingDestinationFrame.copyFrom(a.destinationFrame),s.transform=n.projection.transform,n.projection.transform=null,a.bind(s.renderTexture,s.sourceFrame,m),n.framebuffer.clear(0,0,0,0)},e.prototype.pop=function(){var e=this.defaultFilterStack,r=e.pop(),i=r.filters;this.activeState=r;var n=this.globalUniforms.uniforms;n.outputFrame=r.sourceFrame,n.resolution=r.resolution;var o=n.inputSize,s=n.inputPixel,a=n.inputClamp;if(o[0]=r.destinationFrame.width,o[1]=r.destinationFrame.height,o[2]=1/o[0],o[3]=1/o[1],s[0]=Math.round(o[0]*r.resolution),s[1]=Math.round(o[1]*r.resolution),s[2]=1/s[0],s[3]=1/s[1],a[0]=.5*s[2],a[1]=.5*s[3],a[2]=r.sourceFrame.width*o[2]-.5*s[2],a[3]=r.sourceFrame.height*o[3]-.5*s[3],r.legacy){var u=n.filterArea;u[0]=r.destinationFrame.width,u[1]=r.destinationFrame.height,u[2]=r.sourceFrame.x,u[3]=r.sourceFrame.y,n.filterClamp=n.inputClamp}this.globalUniforms.update();var h=e[e.length-1];if(this.renderer.framebuffer.blit(),1===i.length)i[0].apply(this,r.renderTexture,h.renderTexture,t.CLEAR_MODES.BLEND,r),this.returnFilterTexture(r.renderTexture);else{var l=r.renderTexture,f=this.getOptimalFilterTexture(l.width,l.height,r.resolution);f.filterFrame=l.filterFrame;var d=0;for(d=0;d<i.length-1;++d){1===d&&r.multisample>1&&((f=this.getOptimalFilterTexture(l.width,l.height,r.resolution)).filterFrame=l.filterFrame),i[d].apply(this,l,f,t.CLEAR_MODES.CLEAR,r);var c=l;l=f,f=c}i[d].apply(this,l,h.renderTexture,t.CLEAR_MODES.BLEND,r),d>1&&r.multisample>1&&this.returnFilterTexture(r.renderTexture),this.returnFilterTexture(l),this.returnFilterTexture(f)}r.clear(),this.statePool.push(r)},e.prototype.bindAndClear=function(e,r){void 0===r&&(r=t.CLEAR_MODES.CLEAR);var i=this.renderer,n=i.renderTexture,o=i.state;if(e===this.defaultFilterStack[this.defaultFilterStack.length-1].renderTexture?this.renderer.projection.transform=this.activeState.transform:this.renderer.projection.transform=null,e&&e.filterFrame){var s=this.tempRect;s.x=0,s.y=0,s.width=e.filterFrame.width,s.height=e.filterFrame.height,n.bind(e,e.filterFrame,s)}else e!==this.defaultFilterStack[this.defaultFilterStack.length-1].renderTexture?n.bind(e):this.renderer.renderTexture.bind(e,this.activeState.bindingSourceFrame,this.activeState.bindingDestinationFrame);var a=1&o.stateId||this.forceClear;(r===t.CLEAR_MODES.CLEAR||r===t.CLEAR_MODES.BLIT&&a)&&this.renderer.framebuffer.clear(0,0,0,0)},e.prototype.applyFilter=function(e,r,i,n){var o=this.renderer;o.state.set(e.state),this.bindAndClear(i,n),e.uniforms.uSampler=r,e.uniforms.filterGlobals=this.globalUniforms,o.shader.bind(e),e.legacy=!!e.program.attributeData.aTextureCoord,e.legacy?(this.quadUv.map(r._frame,r.filterFrame),o.geometry.bind(this.quadUv),o.geometry.draw(t.DRAW_MODES.TRIANGLES)):(o.geometry.bind(this.quad),o.geometry.draw(t.DRAW_MODES.TRIANGLE_STRIP))},e.prototype.calculateSpriteMatrix=function(e,t){var r=this.activeState,i=r.sourceFrame,n=r.destinationFrame,o=t._texture.orig,a=e.set(n.width,0,0,n.height,i.x,i.y),u=t.worldTransform.copyTo(s.Matrix.TEMP_MATRIX);return u.invert(),a.prepend(u),a.scale(1/o.width,1/o.height),a.translate(t.anchor.x,t.anchor.y),a},e.prototype.destroy=function(){this.renderer=null,this.texturePool.clear(!1)},e.prototype.getOptimalFilterTexture=function(e,r,i,n){return void 0===i&&(i=1),void 0===n&&(n=t.MSAA_QUALITY.NONE),this.texturePool.getOptimalTexture(e,r,i,n)},e.prototype.getFilterTexture=function(e,r,i){if("number"==typeof e){var n=e;e=r,r=n}e=e||this.activeState.renderTexture;var o=this.texturePool.getOptimalTexture(e.width,e.height,r||e.resolution,i||t.MSAA_QUALITY.NONE);return o.filterFrame=e.filterFrame,o},e.prototype.returnFilterTexture=function(e){this.texturePool.returnTexture(e)},e.prototype.emptyPool=function(){this.texturePool.clear(!0)},e.prototype.resize=function(){this.texturePool.setScreenSize(this.renderer.view)},e.prototype.transformAABB=function(e,t){var r=K[0],i=K[1],n=K[2],o=K[3];r.set(t.left,t.top),i.set(t.left,t.bottom),n.set(t.right,t.top),o.set(t.right,t.bottom),e.apply(r,r),e.apply(i,i),e.apply(n,n),e.apply(o,o);var s=Math.min(r.x,i.x,n.x,o.x),a=Math.min(r.y,i.y,n.y,o.y),u=Math.max(r.x,i.x,n.x,o.x),h=Math.max(r.y,i.y,n.y,o.y);t.x=s,t.y=a,t.width=u-s,t.height=h-a},e.prototype.roundFrame=function(e,t,r,i,n){if(!(e.width<=0||e.height<=0||r.width<=0||r.height<=0)){if(n){var o=n.a,s=n.b,a=n.c,u=n.d;if((Math.abs(s)>1e-4||Math.abs(a)>1e-4)&&(Math.abs(o)>1e-4||Math.abs(u)>1e-4))return}(n=n?Q.copyFrom(n):Q.identity()).translate(-r.x,-r.y).scale(i.width/r.width,i.height/r.height).translate(i.x,i.y),this.transformAABB(n,e),e.ceil(t),this.transformAABB(n.invert(),e)}},e}(),Z=function(){function e(e){this.renderer=e}return e.prototype.flush=function(){},e.prototype.destroy=function(){this.renderer=null},e.prototype.start=function(){},e.prototype.stop=function(){this.flush()},e.prototype.render=function(e){},e}(),$=function(){function e(e){this.renderer=e,this.emptyRenderer=new Z(e),this.currentRenderer=this.emptyRenderer}return e.prototype.setObjectRenderer=function(e){this.currentRenderer!==e&&(this.currentRenderer.stop(),this.currentRenderer=e,this.currentRenderer.start())},e.prototype.flush=function(){this.setObjectRenderer(this.emptyRenderer)},e.prototype.reset=function(){this.setObjectRenderer(this.emptyRenderer)},e.prototype.copyBoundTextures=function(e,t){for(var r=this.renderer.texture.boundTextures,i=t-1;i>=0;--i)e[i]=r[i]||null,e[i]&&(e[i]._batchLocation=i)},e.prototype.boundArray=function(e,t,r,i){for(var n=e.elements,o=e.ids,s=e.count,a=0,u=0;u<s;u++){var h=n[u],l=h._batchLocation;if(l>=0&&l<i&&t[l]===h)o[u]=l;else for(;a<i;){var f=t[a];if(!f||f._batchEnabled!==r||f._batchLocation!==a){o[u]=a,h._batchLocation=a,t[a]=h;break}a++}}},e.prototype.destroy=function(){this.renderer=null},e}(),J=0,ee=function(){function r(e){this.renderer=e,this.webGLVersion=1,this.extensions={},this.supports={uint32Indices:!1},this.handleContextLost=this.handleContextLost.bind(this),this.handleContextRestored=this.handleContextRestored.bind(this),e.view.addEventListener("webglcontextlost",this.handleContextLost,!1),e.view.addEventListener("webglcontextrestored",this.handleContextRestored,!1)}return Object.defineProperty(r.prototype,"isLost",{get:function(){return!this.gl||this.gl.isContextLost()},enumerable:!1,configurable:!0}),r.prototype.contextChange=function(e){this.gl=e,this.renderer.gl=e,this.renderer.CONTEXT_UID=J++},r.prototype.initFromContext=function(e){this.gl=e,this.validateContext(e),this.renderer.gl=e,this.renderer.CONTEXT_UID=J++,this.renderer.runners.contextChange.emit(e)},r.prototype.initFromOptions=function(e){var t=this.createContext(this.renderer.view,e);this.initFromContext(t)},r.prototype.createContext=function(r,i){var n;if(e.settings.PREFER_ENV>=t.ENV.WEBGL2&&(n=r.getContext("webgl2",i)),n)this.webGLVersion=2;else if(this.webGLVersion=1,!(n=r.getContext("webgl",i)||r.getContext("experimental-webgl",i)))throw new Error("This browser does not support WebGL. Try using the canvas renderer");return this.gl=n,this.getExtensions(),this.gl},r.prototype.getExtensions=function(){var e=this.gl,t={loseContext:e.getExtension("WEBGL_lose_context"),anisotropicFiltering:e.getExtension("EXT_texture_filter_anisotropic"),floatTextureLinear:e.getExtension("OES_texture_float_linear"),s3tc:e.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:e.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:e.getExtension("WEBGL_compressed_texture_etc"),etc1:e.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:e.getExtension("WEBGL_compressed_texture_pvrtc")||e.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:e.getExtension("WEBGL_compressed_texture_atc"),astc:e.getExtension("WEBGL_compressed_texture_astc")};1===this.webGLVersion?Object.assign(this.extensions,t,{drawBuffers:e.getExtension("WEBGL_draw_buffers"),depthTexture:e.getExtension("WEBGL_depth_texture"),vertexArrayObject:e.getExtension("OES_vertex_array_object")||e.getExtension("MOZ_OES_vertex_array_object")||e.getExtension("WEBKIT_OES_vertex_array_object"),uint32ElementIndex:e.getExtension("OES_element_index_uint"),floatTexture:e.getExtension("OES_texture_float"),floatTextureLinear:e.getExtension("OES_texture_float_linear"),textureHalfFloat:e.getExtension("OES_texture_half_float"),textureHalfFloatLinear:e.getExtension("OES_texture_half_float_linear")}):2===this.webGLVersion&&Object.assign(this.extensions,t,{colorBufferFloat:e.getExtension("EXT_color_buffer_float")})},r.prototype.handleContextLost=function(e){var t=this;e.preventDefault(),setTimeout((function(){t.gl.isContextLost()&&t.extensions.loseContext&&t.extensions.loseContext.restoreContext()}),0)},r.prototype.handleContextRestored=function(){this.renderer.runners.contextChange.emit(this.gl)},r.prototype.destroy=function(){var e=this.renderer.view;this.renderer=null,e.removeEventListener("webglcontextlost",this.handleContextLost),e.removeEventListener("webglcontextrestored",this.handleContextRestored),this.gl.useProgram(null),this.extensions.loseContext&&this.extensions.loseContext.loseContext()},r.prototype.postrender=function(){this.renderer.renderingToScreen&&this.gl.flush()},r.prototype.validateContext=function(e){var t=e.getContextAttributes(),r="WebGL2RenderingContext"in globalThis&&e instanceof globalThis.WebGL2RenderingContext;r&&(this.webGLVersion=2),t&&!t.stencil&&console.warn("Provided WebGL context does not have a stencil buffer, masks may not render correctly");var i=r||!!e.getExtension("OES_element_index_uint");this.supports.uint32Indices=i,i||console.warn("Provided WebGL context does not support 32 index buffer, complex graphics may not render correctly")},r}(),te=function(e){this.framebuffer=e,this.stencil=null,this.dirtyId=-1,this.dirtyFormat=-1,this.dirtySize=-1,this.multisample=t.MSAA_QUALITY.NONE,this.msaaBuffer=null,this.blitFramebuffer=null,this.mipLevel=0},re=new s.Rectangle,ie=function(){function r(e){this.renderer=e,this.managedFramebuffers=[],this.unknownFramebuffer=new M(10,10),this.msaaSamples=null}return r.prototype.contextChange=function(){this.disposeAll(!0);var r=this.gl=this.renderer.gl;if(this.CONTEXT_UID=this.renderer.CONTEXT_UID,this.current=this.unknownFramebuffer,this.viewport=new s.Rectangle,this.hasMRT=!0,this.writeDepthTexture=!0,1===this.renderer.context.webGLVersion){var i=this.renderer.context.extensions.drawBuffers,n=this.renderer.context.extensions.depthTexture;e.settings.PREFER_ENV===t.ENV.WEBGL_LEGACY&&(i=null,n=null),i?r.drawBuffers=function(e){return i.drawBuffersWEBGL(e)}:(this.hasMRT=!1,r.drawBuffers=function(){}),n||(this.writeDepthTexture=!1)}else this.msaaSamples=r.getInternalformatParameter(r.RENDERBUFFER,r.RGBA8,r.SAMPLES)},r.prototype.bind=function(e,t,r){void 0===r&&(r=0);var i=this.gl;if(e){var n=e.glFramebuffers[this.CONTEXT_UID]||this.initFramebuffer(e);this.current!==e&&(this.current=e,i.bindFramebuffer(i.FRAMEBUFFER,n.framebuffer)),n.mipLevel!==r&&(e.dirtyId++,e.dirtyFormat++,n.mipLevel=r),n.dirtyId!==e.dirtyId&&(n.dirtyId=e.dirtyId,n.dirtyFormat!==e.dirtyFormat?(n.dirtyFormat=e.dirtyFormat,n.dirtySize=e.dirtySize,this.updateFramebuffer(e,r)):n.dirtySize!==e.dirtySize&&(n.dirtySize=e.dirtySize,this.resizeFramebuffer(e)));for(var o=0;o<e.colorTextures.length;o++){var s=e.colorTextures[o];this.renderer.texture.unbind(s.parentTextureArray||s)}if(e.depthTexture&&this.renderer.texture.unbind(e.depthTexture),t){var a=t.width>>r,u=t.height>>r,h=a/t.width;this.setViewport(t.x*h,t.y*h,a,u)}else{a=e.width>>r,u=e.height>>r;this.setViewport(0,0,a,u)}}else this.current&&(this.current=null,i.bindFramebuffer(i.FRAMEBUFFER,null)),t?this.setViewport(t.x,t.y,t.width,t.height):this.setViewport(0,0,this.renderer.width,this.renderer.height)},r.prototype.setViewport=function(e,t,r,i){var n=this.viewport;e=Math.round(e),t=Math.round(t),r=Math.round(r),i=Math.round(i),n.width===r&&n.height===i&&n.x===e&&n.y===t||(n.x=e,n.y=t,n.width=r,n.height=i,this.gl.viewport(e,t,r,i))},Object.defineProperty(r.prototype,"size",{get:function(){return this.current?{x:0,y:0,width:this.current.width,height:this.current.height}:{x:0,y:0,width:this.renderer.width,height:this.renderer.height}},enumerable:!1,configurable:!0}),r.prototype.clear=function(e,r,i,n,o){void 0===o&&(o=t.BUFFER_BITS.COLOR|t.BUFFER_BITS.DEPTH);var s=this.gl;s.clearColor(e,r,i,n),s.clear(o)},r.prototype.initFramebuffer=function(e){var t=this.gl,r=new te(t.createFramebuffer());return r.multisample=this.detectSamples(e.multisample),e.glFramebuffers[this.CONTEXT_UID]=r,this.managedFramebuffers.push(e),e.disposeRunner.add(this),r},r.prototype.resizeFramebuffer=function(e){var t=this.gl,r=e.glFramebuffers[this.CONTEXT_UID];r.msaaBuffer&&(t.bindRenderbuffer(t.RENDERBUFFER,r.msaaBuffer),t.renderbufferStorageMultisample(t.RENDERBUFFER,r.multisample,t.RGBA8,e.width,e.height)),r.stencil&&(t.bindRenderbuffer(t.RENDERBUFFER,r.stencil),r.msaaBuffer?t.renderbufferStorageMultisample(t.RENDERBUFFER,r.multisample,t.DEPTH24_STENCIL8,e.width,e.height):t.renderbufferStorage(t.RENDERBUFFER,t.DEPTH_STENCIL,e.width,e.height));var i=e.colorTextures,n=i.length;t.drawBuffers||(n=Math.min(n,1));for(var o=0;o<n;o++){var s=i[o],a=s.parentTextureArray||s;this.renderer.texture.bind(a,0)}e.depthTexture&&this.writeDepthTexture&&this.renderer.texture.bind(e.depthTexture,0)},r.prototype.updateFramebuffer=function(e,t){var r=this.gl,i=e.glFramebuffers[this.CONTEXT_UID],n=e.colorTextures,o=n.length;r.drawBuffers||(o=Math.min(o,1)),i.multisample>1&&this.canMultisampleFramebuffer(e)?(i.msaaBuffer=i.msaaBuffer||r.createRenderbuffer(),r.bindRenderbuffer(r.RENDERBUFFER,i.msaaBuffer),r.renderbufferStorageMultisample(r.RENDERBUFFER,i.multisample,r.RGBA8,e.width,e.height),r.framebufferRenderbuffer(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.RENDERBUFFER,i.msaaBuffer)):i.msaaBuffer&&(r.deleteRenderbuffer(i.msaaBuffer),i.msaaBuffer=null,i.blitFramebuffer&&(i.blitFramebuffer.dispose(),i.blitFramebuffer=null));for(var s=[],a=0;a<o;a++){var u=n[a],h=u.parentTextureArray||u;this.renderer.texture.bind(h,0),0===a&&i.msaaBuffer||(r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0+a,u.target,h._glTextures[this.CONTEXT_UID].texture,t),s.push(r.COLOR_ATTACHMENT0+a))}if((s.length>1&&r.drawBuffers(s),e.depthTexture)&&this.writeDepthTexture){var l=e.depthTexture;this.renderer.texture.bind(l,0),r.framebufferTexture2D(r.FRAMEBUFFER,r.DEPTH_ATTACHMENT,r.TEXTURE_2D,l._glTextures[this.CONTEXT_UID].texture,t)}!e.stencil&&!e.depth||e.depthTexture&&this.writeDepthTexture?i.stencil&&(r.deleteRenderbuffer(i.stencil),i.stencil=null):(i.stencil=i.stencil||r.createRenderbuffer(),r.bindRenderbuffer(r.RENDERBUFFER,i.stencil),i.msaaBuffer?r.renderbufferStorageMultisample(r.RENDERBUFFER,i.multisample,r.DEPTH24_STENCIL8,e.width,e.height):r.renderbufferStorage(r.RENDERBUFFER,r.DEPTH_STENCIL,e.width,e.height),r.framebufferRenderbuffer(r.FRAMEBUFFER,r.DEPTH_STENCIL_ATTACHMENT,r.RENDERBUFFER,i.stencil))},r.prototype.canMultisampleFramebuffer=function(e){return 1!==this.renderer.context.webGLVersion&&e.colorTextures.length<=1&&!e.depthTexture},r.prototype.detectSamples=function(e){var r=this.msaaSamples,i=t.MSAA_QUALITY.NONE;if(e<=1||null===r)return i;for(var n=0;n<r.length;n++)if(r[n]<=e){i=r[n];break}return 1===i&&(i=t.MSAA_QUALITY.NONE),i},r.prototype.blit=function(e,t,r){var i=this,n=i.current,o=i.renderer,s=i.gl,a=i.CONTEXT_UID;if(2===o.context.webGLVersion&&n){var u=n.glFramebuffers[a];if(u){if(!e){if(!u.msaaBuffer)return;var h=n.colorTextures[0];if(!h)return;u.blitFramebuffer||(u.blitFramebuffer=new M(n.width,n.height),u.blitFramebuffer.addColorTexture(0,h)),(e=u.blitFramebuffer).colorTextures[0]!==h&&(e.colorTextures[0]=h,e.dirtyId++,e.dirtyFormat++),e.width===n.width&&e.height===n.height||(e.width=n.width,e.height=n.height,e.dirtyId++,e.dirtySize++)}t||((t=re).width=n.width,t.height=n.height),r||(r=t);var l=t.width===r.width&&t.height===r.height;this.bind(e),s.bindFramebuffer(s.READ_FRAMEBUFFER,u.framebuffer),s.blitFramebuffer(t.left,t.top,t.right,t.bottom,r.left,r.top,r.right,r.bottom,s.COLOR_BUFFER_BIT,l?s.NEAREST:s.LINEAR)}}},r.prototype.disposeFramebuffer=function(e,t){var r=e.glFramebuffers[this.CONTEXT_UID],i=this.gl;if(r){delete e.glFramebuffers[this.CONTEXT_UID];var n=this.managedFramebuffers.indexOf(e);n>=0&&this.managedFramebuffers.splice(n,1),e.disposeRunner.remove(this),t||(i.deleteFramebuffer(r.framebuffer),r.msaaBuffer&&i.deleteRenderbuffer(r.msaaBuffer),r.stencil&&i.deleteRenderbuffer(r.stencil)),r.blitFramebuffer&&r.blitFramebuffer.dispose()}},r.prototype.disposeAll=function(e){var t=this.managedFramebuffers;this.managedFramebuffers=[];for(var r=0;r<t.length;r++)this.disposeFramebuffer(t[r],e)},r.prototype.forceStencil=function(){var e=this.current;if(e){var t=e.glFramebuffers[this.CONTEXT_UID];if(t&&!t.stencil){e.stencil=!0;var r=e.width,i=e.height,n=this.gl,o=n.createRenderbuffer();n.bindRenderbuffer(n.RENDERBUFFER,o),t.msaaBuffer?n.renderbufferStorageMultisample(n.RENDERBUFFER,t.multisample,n.DEPTH24_STENCIL8,r,i):n.renderbufferStorage(n.RENDERBUFFER,n.DEPTH_STENCIL,r,i),t.stencil=o,n.framebufferRenderbuffer(n.FRAMEBUFFER,n.DEPTH_STENCIL_ATTACHMENT,n.RENDERBUFFER,o)}}},r.prototype.reset=function(){this.current=this.unknownFramebuffer,this.viewport=new s.Rectangle},r.prototype.destroy=function(){this.renderer=null},r}(),ne={5126:4,5123:2,5121:1},oe=function(){function r(e){this.renderer=e,this._activeGeometry=null,this._activeVao=null,this.hasVao=!0,this.hasInstance=!0,this.canUseUInt32ElementIndex=!1,this.managedGeometries={}}return r.prototype.contextChange=function(){this.disposeAll(!0);var r=this.gl=this.renderer.gl,i=this.renderer.context;if(this.CONTEXT_UID=this.renderer.CONTEXT_UID,2!==i.webGLVersion){var n=this.renderer.context.extensions.vertexArrayObject;e.settings.PREFER_ENV===t.ENV.WEBGL_LEGACY&&(n=null),n?(r.createVertexArray=function(){return n.createVertexArrayOES()},r.bindVertexArray=function(e){return n.bindVertexArrayOES(e)},r.deleteVertexArray=function(e){return n.deleteVertexArrayOES(e)}):(this.hasVao=!1,r.createVertexArray=function(){return null},r.bindVertexArray=function(){return null},r.deleteVertexArray=function(){return null})}if(2!==i.webGLVersion){var o=r.getExtension("ANGLE_instanced_arrays");o?(r.vertexAttribDivisor=function(e,t){return o.vertexAttribDivisorANGLE(e,t)},r.drawElementsInstanced=function(e,t,r,i,n){return o.drawElementsInstancedANGLE(e,t,r,i,n)},r.drawArraysInstanced=function(e,t,r,i){return o.drawArraysInstancedANGLE(e,t,r,i)}):this.hasInstance=!1}this.canUseUInt32ElementIndex=2===i.webGLVersion||!!i.extensions.uint32ElementIndex},r.prototype.bind=function(e,t){t=t||this.renderer.shader.shader;var r=this.gl,i=e.glVertexArrayObjects[this.CONTEXT_UID],n=!1;i||(this.managedGeometries[e.id]=e,e.disposeRunner.add(this),e.glVertexArrayObjects[this.CONTEXT_UID]=i={},n=!0);var o=i[t.program.id]||this.initGeometryVao(e,t,n);this._activeGeometry=e,this._activeVao!==o&&(this._activeVao=o,this.hasVao?r.bindVertexArray(o):this.activateVao(e,t.program)),this.updateBuffers()},r.prototype.reset=function(){this.unbind()},r.prototype.updateBuffers=function(){for(var e=this._activeGeometry,t=this.renderer.buffer,r=0;r<e.buffers.length;r++){var i=e.buffers[r];t.update(i)}},r.prototype.checkCompatibility=function(e,t){var r=e.attributes,i=t.attributeData;for(var n in i)if(!r[n])throw new Error('shader and geometry incompatible, geometry missing the "'+n+'" attribute')},r.prototype.getSignature=function(e,t){var r=e.attributes,i=t.attributeData,n=["g",e.id];for(var o in r)i[o]&&n.push(o,i[o].location);return n.join("-")},r.prototype.initGeometryVao=function(e,t,r){void 0===r&&(r=!0);var i=this.gl,n=this.CONTEXT_UID,o=this.renderer.buffer,s=t.program;s.glPrograms[n]||this.renderer.shader.generateProgram(t),this.checkCompatibility(e,s);var a=this.getSignature(e,s),u=e.glVertexArrayObjects[this.CONTEXT_UID],h=u[a];if(h)return u[s.id]=h,h;var l=e.buffers,f=e.attributes,d={},c={};for(var p in l)d[p]=0,c[p]=0;for(var p in f)!f[p].size&&s.attributeData[p]?f[p].size=s.attributeData[p].size:f[p].size||console.warn("PIXI Geometry attribute '"+p+"' size cannot be determined (likely the bound shader does not have the attribute)"),d[f[p].buffer]+=f[p].size*ne[f[p].type];for(var p in f){var v=f[p],m=v.size;void 0===v.stride&&(d[v.buffer]===m*ne[v.type]?v.stride=0:v.stride=d[v.buffer]),void 0===v.start&&(v.start=c[v.buffer],c[v.buffer]+=m*ne[v.type])}h=i.createVertexArray(),i.bindVertexArray(h);for(var g=0;g<l.length;g++){var _=l[g];o.bind(_),r&&_._glBuffers[n].refCount++}return this.activateVao(e,s),this._activeVao=h,u[s.id]=h,u[a]=h,h},r.prototype.disposeGeometry=function(e,t){var r;if(this.managedGeometries[e.id]){delete this.managedGeometries[e.id];var i=e.glVertexArrayObjects[this.CONTEXT_UID],n=this.gl,o=e.buffers,s=null===(r=this.renderer)||void 0===r?void 0:r.buffer;if(e.disposeRunner.remove(this),i){if(s)for(var a=0;a<o.length;a++){var u=o[a]._glBuffers[this.CONTEXT_UID];u&&(u.refCount--,0!==u.refCount||t||s.dispose(o[a],t))}if(!t)for(var h in i)if("g"===h[0]){var l=i[h];this._activeVao===l&&this.unbind(),n.deleteVertexArray(l)}delete e.glVertexArrayObjects[this.CONTEXT_UID]}}},r.prototype.disposeAll=function(e){for(var t=Object.keys(this.managedGeometries),r=0;r<t.length;r++)this.disposeGeometry(this.managedGeometries[t[r]],e)},r.prototype.activateVao=function(e,t){var r=this.gl,i=this.CONTEXT_UID,n=this.renderer.buffer,o=e.buffers,s=e.attributes;e.indexBuffer&&n.bind(e.indexBuffer);var a=null;for(var u in s){var h=s[u],l=o[h.buffer],f=l._glBuffers[i];if(t.attributeData[u]){a!==f&&(n.bind(l),a=f);var d=t.attributeData[u].location;if(r.enableVertexAttribArray(d),r.vertexAttribPointer(d,h.size,h.type||r.FLOAT,h.normalized,h.stride,h.start),h.instance){if(!this.hasInstance)throw new Error("geometry error, GPU Instancing is not supported on this device");r.vertexAttribDivisor(d,1)}}}},r.prototype.draw=function(e,t,r,i){var n=this.gl,o=this._activeGeometry;if(o.indexBuffer){var s=o.indexBuffer.data.BYTES_PER_ELEMENT,a=2===s?n.UNSIGNED_SHORT:n.UNSIGNED_INT;2===s||4===s&&this.canUseUInt32ElementIndex?o.instanced?n.drawElementsInstanced(e,t||o.indexBuffer.data.length,a,(r||0)*s,i||1):n.drawElements(e,t||o.indexBuffer.data.length,a,(r||0)*s):console.warn("unsupported index buffer type: uint32")}else o.instanced?n.drawArraysInstanced(e,r,t||o.getSize(),i||1):n.drawArrays(e,r,t||o.getSize());return this},r.prototype.unbind=function(){this.gl.bindVertexArray(null),this._activeVao=null,this._activeGeometry=null},r.prototype.destroy=function(){this.renderer=null},r}(),se=function(){function r(r){void 0===r&&(r=null),this.type=t.MASK_TYPES.NONE,this.autoDetect=!0,this.maskObject=r||null,this.pooled=!1,this.isMaskData=!0,this.resolution=null,this.multisample=e.settings.FILTER_MULTISAMPLE,this.enabled=!0,this.colorMask=15,this._filters=null,this._stencilCounter=0,this._scissorCounter=0,this._scissorRect=null,this._scissorRectLocal=null,this._colorMask=15,this._target=null}return Object.defineProperty(r.prototype,"filter",{get:function(){return this._filters?this._filters[0]:null},set:function(e){e?this._filters?this._filters[0]=e:this._filters=[e]:this._filters=null},enumerable:!1,configurable:!0}),r.prototype.reset=function(){this.pooled&&(this.maskObject=null,this.type=t.MASK_TYPES.NONE,this.autoDetect=!0),this._target=null,this._scissorRectLocal=null},r.prototype.copyCountersOrReset=function(e){e?(this._stencilCounter=e._stencilCounter,this._scissorCounter=e._scissorCounter,this._scissorRect=e._scissorRect):(this._stencilCounter=0,this._scissorCounter=0,this._scissorRect=null)},r}();function ae(e,t,r){var i=e.createShader(t);return e.shaderSource(i,r),e.compileShader(i),i}function ue(e,t){var r=e.getShaderSource(t).split("\n").map((function(e,t){return t+": "+e})),i=e.getShaderInfoLog(t),n=i.split("\n"),o={},s=n.map((function(e){return parseFloat(e.replace(/^ERROR\: 0\:([\d]+)\:.*$/,"$1"))})).filter((function(e){return!(!e||o[e])&&(o[e]=!0,!0)})),a=[""];s.forEach((function(e){r[e-1]="%c"+r[e-1]+"%c",a.push("background: #FF0000; color:#FFFFFF; font-size: 10px","font-size: 10px")}));var u=r.join("\n");a[0]=u,console.error(i),console.groupCollapsed("click to view full shader code"),console.warn.apply(console,a),console.groupEnd()}function he(e){for(var t=new Array(e),r=0;r<t.length;r++)t[r]=!1;return t}function le(e,t){switch(e){case"float":case"int":case"uint":case"sampler2D":case"sampler2DArray":return 0;case"vec2":return new Float32Array(2*t);case"vec3":return new Float32Array(3*t);case"vec4":return new Float32Array(4*t);case"ivec2":return new Int32Array(2*t);case"ivec3":return new Int32Array(3*t);case"ivec4":return new Int32Array(4*t);case"uvec2":return new Uint32Array(2*t);case"uvec3":return new Uint32Array(3*t);case"uvec4":return new Uint32Array(4*t);case"bool":return!1;case"bvec2":return he(2*t);case"bvec3":return he(3*t);case"bvec4":return he(4*t);case"mat2":return new Float32Array([1,0,0,1]);case"mat3":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}var fe,de={},ce=de;function pe(){if(ce===de||ce&&ce.isContextLost()){var r=e.settings.ADAPTER.createCanvas(),i=void 0;e.settings.PREFER_ENV>=t.ENV.WEBGL2&&(i=r.getContext("webgl2",{})),i||((i=r.getContext("webgl",{})||r.getContext("experimental-webgl",{}))?i.getExtension("WEBGL_draw_buffers"):i=null),ce=i}return ce}function ve(e,r,i){if("precision"!==e.substring(0,9)){var n=r;return r===t.PRECISION.HIGH&&i!==t.PRECISION.HIGH&&(n=t.PRECISION.MEDIUM),"precision "+n+" float;\n"+e}return i!==t.PRECISION.HIGH&&"precision highp"===e.substring(0,15)?e.replace("precision highp","precision mediump"):e}var me={float:1,vec2:2,vec3:3,vec4:4,int:1,ivec2:2,ivec3:3,ivec4:4,uint:1,uvec2:2,uvec3:3,uvec4:4,bool:1,bvec2:2,bvec3:3,bvec4:4,mat2:4,mat3:9,mat4:16,sampler2D:1};function ge(e){return me[e]}var _e=null,ye={FLOAT:"float",FLOAT_VEC2:"vec2",FLOAT_VEC3:"vec3",FLOAT_VEC4:"vec4",INT:"int",INT_VEC2:"ivec2",INT_VEC3:"ivec3",INT_VEC4:"ivec4",UNSIGNED_INT:"uint",UNSIGNED_INT_VEC2:"uvec2",UNSIGNED_INT_VEC3:"uvec3",UNSIGNED_INT_VEC4:"uvec4",BOOL:"bool",BOOL_VEC2:"bvec2",BOOL_VEC3:"bvec3",BOOL_VEC4:"bvec4",FLOAT_MAT2:"mat2",FLOAT_MAT3:"mat3",FLOAT_MAT4:"mat4",SAMPLER_2D:"sampler2D",INT_SAMPLER_2D:"sampler2D",UNSIGNED_INT_SAMPLER_2D:"sampler2D",SAMPLER_CUBE:"samplerCube",INT_SAMPLER_CUBE:"samplerCube",UNSIGNED_INT_SAMPLER_CUBE:"samplerCube",SAMPLER_2D_ARRAY:"sampler2DArray",INT_SAMPLER_2D_ARRAY:"sampler2DArray",UNSIGNED_INT_SAMPLER_2D_ARRAY:"sampler2DArray"};function Ee(e,t){if(!_e){var r=Object.keys(ye);_e={};for(var i=0;i<r.length;++i){var n=r[i];_e[e[n]]=ye[n]}}return _e[t]}var Te=[{test:function(e){return"float"===e.type&&1===e.size&&!e.isArray},code:function(e){return'\n            if(uv["'+e+'"] !== ud["'+e+'"].value)\n            {\n                ud["'+e+'"].value = uv["'+e+'"]\n                gl.uniform1f(ud["'+e+'"].location, uv["'+e+'"])\n            }\n            '}},{test:function(e,t){return!("sampler2D"!==e.type&&"samplerCube"!==e.type&&"sampler2DArray"!==e.type||1!==e.size||e.isArray||null!=t&&void 0===t.castToBaseTexture)},code:function(e){return't = syncData.textureCount++;\n\n            renderer.texture.bind(uv["'+e+'"], t);\n\n            if(ud["'+e+'"].value !== t)\n            {\n                ud["'+e+'"].value = t;\n                gl.uniform1i(ud["'+e+'"].location, t);\n; // eslint-disable-line max-len\n            }'}},{test:function(e,t){return"mat3"===e.type&&1===e.size&&!e.isArray&&void 0!==t.a},code:function(e){return'\n            gl.uniformMatrix3fv(ud["'+e+'"].location, false, uv["'+e+'"].toArray(true));\n            '},codeUbo:function(e){return"\n                var "+e+"_matrix = uv."+e+".toArray(true);\n\n                data[offset] = "+e+"_matrix[0];\n                data[offset+1] = "+e+"_matrix[1];\n                data[offset+2] = "+e+"_matrix[2];\n        \n                data[offset + 4] = "+e+"_matrix[3];\n                data[offset + 5] = "+e+"_matrix[4];\n                data[offset + 6] = "+e+"_matrix[5];\n        \n                data[offset + 8] = "+e+"_matrix[6];\n                data[offset + 9] = "+e+"_matrix[7];\n                data[offset + 10] = "+e+"_matrix[8];\n            "}},{test:function(e,t){return"vec2"===e.type&&1===e.size&&!e.isArray&&void 0!==t.x},code:function(e){return'\n                cv = ud["'+e+'"].value;\n                v = uv["'+e+'"];\n\n                if(cv[0] !== v.x || cv[1] !== v.y)\n                {\n                    cv[0] = v.x;\n                    cv[1] = v.y;\n                    gl.uniform2f(ud["'+e+'"].location, v.x, v.y);\n                }'},codeUbo:function(e){return"\n                v = uv."+e+";\n\n                data[offset] = v.x;\n                data[offset+1] = v.y;\n            "}},{test:function(e){return"vec2"===e.type&&1===e.size&&!e.isArray},code:function(e){return'\n                cv = ud["'+e+'"].value;\n                v = uv["'+e+'"];\n\n                if(cv[0] !== v[0] || cv[1] !== v[1])\n                {\n                    cv[0] = v[0];\n                    cv[1] = v[1];\n                    gl.uniform2f(ud["'+e+'"].location, v[0], v[1]);\n                }\n            '}},{test:function(e,t){return"vec4"===e.type&&1===e.size&&!e.isArray&&void 0!==t.width},code:function(e){return'\n                cv = ud["'+e+'"].value;\n                v = uv["'+e+'"];\n\n                if(cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height)\n                {\n                    cv[0] = v.x;\n                    cv[1] = v.y;\n                    cv[2] = v.width;\n                    cv[3] = v.height;\n                    gl.uniform4f(ud["'+e+'"].location, v.x, v.y, v.width, v.height)\n                }'},codeUbo:function(e){return"\n                    v = uv."+e+";\n\n                    data[offset] = v.x;\n                    data[offset+1] = v.y;\n                    data[offset+2] = v.width;\n                    data[offset+3] = v.height;\n                "}},{test:function(e){return"vec4"===e.type&&1===e.size&&!e.isArray},code:function(e){return'\n                cv = ud["'+e+'"].value;\n                v = uv["'+e+'"];\n\n                if(cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n                {\n                    cv[0] = v[0];\n                    cv[1] = v[1];\n                    cv[2] = v[2];\n                    cv[3] = v[3];\n\n                    gl.uniform4f(ud["'+e+'"].location, v[0], v[1], v[2], v[3])\n                }'}}],xe={float:"\n    if (cv !== v)\n    {\n        cu.value = v;\n        gl.uniform1f(location, v);\n    }",vec2:"\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2f(location, v[0], v[1])\n    }",vec3:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3f(location, v[0], v[1], v[2])\n    }",vec4:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4f(location, v[0], v[1], v[2], v[3]);\n    }",int:"\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }",ivec2:"\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2i(location, v[0], v[1]);\n    }",ivec3:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3i(location, v[0], v[1], v[2]);\n    }",ivec4:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n    }",uint:"\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1ui(location, v);\n    }",uvec2:"\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2ui(location, v[0], v[1]);\n    }",uvec3:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3ui(location, v[0], v[1], v[2]);\n    }",uvec4:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4ui(location, v[0], v[1], v[2], v[3]);\n    }",bool:"\n    if (cv !== v)\n    {\n        cu.value = v;\n        gl.uniform1i(location, v);\n    }",bvec2:"\n    if (cv[0] != v[0] || cv[1] != v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2i(location, v[0], v[1]);\n    }",bvec3:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3i(location, v[0], v[1], v[2]);\n    }",bvec4:"\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n    }",mat2:"gl.uniformMatrix2fv(location, false, v)",mat3:"gl.uniformMatrix3fv(location, false, v)",mat4:"gl.uniformMatrix4fv(location, false, v)",sampler2D:"\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }",samplerCube:"\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }",sampler2DArray:"\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }"},be={float:"gl.uniform1fv(location, v)",vec2:"gl.uniform2fv(location, v)",vec3:"gl.uniform3fv(location, v)",vec4:"gl.uniform4fv(location, v)",mat4:"gl.uniformMatrix4fv(location, false, v)",mat3:"gl.uniformMatrix3fv(location, false, v)",mat2:"gl.uniformMatrix2fv(location, false, v)",int:"gl.uniform1iv(location, v)",ivec2:"gl.uniform2iv(location, v)",ivec3:"gl.uniform3iv(location, v)",ivec4:"gl.uniform4iv(location, v)",uint:"gl.uniform1uiv(location, v)",uvec2:"gl.uniform2uiv(location, v)",uvec3:"gl.uniform3uiv(location, v)",uvec4:"gl.uniform4uiv(location, v)",bool:"gl.uniform1iv(location, v)",bvec2:"gl.uniform2iv(location, v)",bvec3:"gl.uniform3iv(location, v)",bvec4:"gl.uniform4iv(location, v)",sampler2D:"gl.uniform1iv(location, v)",samplerCube:"gl.uniform1iv(location, v)",sampler2DArray:"gl.uniform1iv(location, v)"};var Se,Re=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join("\n");function Ae(e){for(var t="",r=0;r<e;++r)r>0&&(t+="\nelse "),r<e-1&&(t+="if(test == "+r+".0){}");return t}function Me(e,t){if(0===e)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");for(var r=t.createShader(t.FRAGMENT_SHADER);;){var i=Re.replace(/%forloop%/gi,Ae(e));if(t.shaderSource(r,i),t.compileShader(r),t.getShaderParameter(r,t.COMPILE_STATUS))break;e=e/2|0}return e}var we=0,Ie={},Ce=function(){function i(r,n,o){void 0===o&&(o="pixi-shader"),this.id=we++,this.vertexSrc=r||i.defaultVertexSrc,this.fragmentSrc=n||i.defaultFragmentSrc,this.vertexSrc=this.vertexSrc.trim(),this.fragmentSrc=this.fragmentSrc.trim(),"#version"!==this.vertexSrc.substring(0,8)&&(o=o.replace(/\s+/g,"-"),Ie[o]?(Ie[o]++,o+="-"+Ie[o]):Ie[o]=1,this.vertexSrc="#define SHADER_NAME "+o+"\n"+this.vertexSrc,this.fragmentSrc="#define SHADER_NAME "+o+"\n"+this.fragmentSrc,this.vertexSrc=ve(this.vertexSrc,e.settings.PRECISION_VERTEX,t.PRECISION.HIGH),this.fragmentSrc=ve(this.fragmentSrc,e.settings.PRECISION_FRAGMENT,function(){if(!fe){fe=t.PRECISION.MEDIUM;var e=pe();if(e&&e.getShaderPrecisionFormat){var r=e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_FLOAT);fe=r.precision?t.PRECISION.HIGH:t.PRECISION.MEDIUM}}return fe}())),this.glPrograms={},this.syncUniforms=null}return Object.defineProperty(i,"defaultVertexSrc",{get:function(){return"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void){\n   gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n   vTextureCoord = aTextureCoord;\n}\n"},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultFragmentSrc",{get:function(){return"varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\n\nvoid main(void){\n   gl_FragColor *= texture2D(uSampler, vTextureCoord);\n}"},enumerable:!1,configurable:!0}),i.from=function(e,t,n){var o=e+t,s=r.ProgramCache[o];return s||(r.ProgramCache[o]=s=new i(e,t,n)),s},i}(),Oe=function(){function e(e,t){this.uniformBindCount=0,this.program=e,this.uniformGroup=t?t instanceof Y?t:new Y(t):new Y({}),this.disposeRunner=new n.Runner("disposeShader")}return e.prototype.checkUniformExists=function(e,t){if(t.uniforms[e])return!0;for(var r in t.uniforms){var i=t.uniforms[r];if(i.group&&this.checkUniformExists(e,i))return!0}return!1},e.prototype.destroy=function(){this.uniformGroup=null,this.disposeRunner.emit(this),this.disposeRunner.destroy()},Object.defineProperty(e.prototype,"uniforms",{get:function(){return this.uniformGroup.uniforms},enumerable:!1,configurable:!0}),e.from=function(t,r,i){return new e(Ce.from(t,r),i)},e}(),Fe=function(){function e(){this.data=0,this.blendMode=t.BLEND_MODES.NORMAL,this.polygonOffset=0,this.blend=!0,this.depthMask=!0}return Object.defineProperty(e.prototype,"blend",{get:function(){return!!(1&this.data)},set:function(e){!!(1&this.data)!==e&&(this.data^=1)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"offsets",{get:function(){return!!(2&this.data)},set:function(e){!!(2&this.data)!==e&&(this.data^=2)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"culling",{get:function(){return!!(4&this.data)},set:function(e){!!(4&this.data)!==e&&(this.data^=4)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"depthTest",{get:function(){return!!(8&this.data)},set:function(e){!!(8&this.data)!==e&&(this.data^=8)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"depthMask",{get:function(){return!!(32&this.data)},set:function(e){!!(32&this.data)!==e&&(this.data^=32)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"clockwiseFrontFace",{get:function(){return!!(16&this.data)},set:function(e){!!(16&this.data)!==e&&(this.data^=16)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"blendMode",{get:function(){return this._blendMode},set:function(e){this.blend=e!==t.BLEND_MODES.NONE,this._blendMode=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"polygonOffset",{get:function(){return this._polygonOffset},set:function(e){this.offsets=!!e,this._polygonOffset=e},enumerable:!1,configurable:!0}),e.for2d=function(){var t=new e;return t.depthTest=!1,t.blend=!0,t},e}(),Pe=function(t){function r(i,n,o){var s=this,a=Ce.from(i||r.defaultVertexSrc,n||r.defaultFragmentSrc);return(s=t.call(this,a,o)||this).padding=0,s.resolution=e.settings.FILTER_RESOLUTION,s.multisample=e.settings.FILTER_MULTISAMPLE,s.enabled=!0,s.autoFit=!0,s.state=new Fe,s}return l(r,t),r.prototype.apply=function(e,t,r,i,n){e.applyFilter(this,t,r,i)},Object.defineProperty(r.prototype,"blendMode",{get:function(){return this.state.blendMode},set:function(e){this.state.blendMode=e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"resolution",{get:function(){return this._resolution},set:function(e){this._resolution=e},enumerable:!1,configurable:!0}),Object.defineProperty(r,"defaultVertexSrc",{get:function(){return"attribute vec2 aVertexPosition;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\n\nuniform vec4 inputSize;\nuniform vec4 outputFrame;\n\nvec4 filterVertexPosition( void )\n{\n    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;\n\n    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);\n}\n\nvec2 filterTextureCoord( void )\n{\n    return aVertexPosition * (outputFrame.zw * inputSize.zw);\n}\n\nvoid main(void)\n{\n    gl_Position = filterVertexPosition();\n    vTextureCoord = filterTextureCoord();\n}\n"},enumerable:!1,configurable:!0}),Object.defineProperty(r,"defaultFragmentSrc",{get:function(){return"varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\n\nvoid main(void){\n   gl_FragColor = texture2D(uSampler, vTextureCoord);\n}\n"},enumerable:!1,configurable:!0}),r}(Oe),Ne=new s.Matrix,Be=function(){function e(e,t){this._texture=e,this.mapCoord=new s.Matrix,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,this.clampMargin=void 0===t?.5:t,this.isSimple=!1}return Object.defineProperty(e.prototype,"texture",{get:function(){return this._texture},set:function(e){this._texture=e,this._textureID=-1},enumerable:!1,configurable:!0}),e.prototype.multiplyUvs=function(e,t){void 0===t&&(t=e);for(var r=this.mapCoord,i=0;i<e.length;i+=2){var n=e[i],o=e[i+1];t[i]=n*r.a+o*r.c+r.tx,t[i+1]=n*r.b+o*r.d+r.ty}return t},e.prototype.update=function(e){var t=this._texture;if(!t||!t.valid)return!1;if(!e&&this._textureID===t._updateID)return!1;this._textureID=t._updateID,this._updateID++;var r=t._uvs;this.mapCoord.set(r.x1-r.x0,r.y1-r.y0,r.x3-r.x0,r.y3-r.y0,r.x0,r.y0);var i=t.orig,n=t.trim;n&&(Ne.set(i.width/n.width,0,0,i.height/n.height,-n.x/n.width,-n.y/n.height),this.mapCoord.append(Ne));var o=t.baseTexture,s=this.uClampFrame,a=this.clampMargin/o.resolution,u=this.clampOffset;return s[0]=(t._frame.x+a+u)/o.width,s[1]=(t._frame.y+a+u)/o.height,s[2]=(t._frame.x+t._frame.width-a+u)/o.width,s[3]=(t._frame.y+t._frame.height-a+u)/o.height,this.uClampOffset[0]=u/o.realWidth,this.uClampOffset[1]=u/o.realHeight,this.isSimple=t._frame.width===o.width&&t._frame.height===o.height&&0===t.rotate,!0},e}(),De=function(e){function t(t,r,i){var n=this,o=null;return"string"!=typeof t&&void 0===r&&void 0===i&&(o=t,t=void 0,r=void 0,i=void 0),(n=e.call(this,t||"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 otherMatrix;\n\nvarying vec2 vMaskCoord;\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = aTextureCoord;\n    vMaskCoord = ( otherMatrix * vec3( aTextureCoord, 1.0)  ).xy;\n}\n",r||"varying vec2 vMaskCoord;\nvarying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform sampler2D mask;\nuniform float alpha;\nuniform float npmAlpha;\nuniform vec4 maskClamp;\n\nvoid main(void)\n{\n    float clip = step(3.5,\n        step(maskClamp.x, vMaskCoord.x) +\n        step(maskClamp.y, vMaskCoord.y) +\n        step(vMaskCoord.x, maskClamp.z) +\n        step(vMaskCoord.y, maskClamp.w));\n\n    vec4 original = texture2D(uSampler, vTextureCoord);\n    vec4 masky = texture2D(mask, vMaskCoord);\n    float alphaMul = 1.0 - npmAlpha * (1.0 - masky.a);\n\n    original *= (alphaMul * masky.r * alpha * clip);\n\n    gl_FragColor = original;\n}\n",i)||this).maskSprite=o,n.maskMatrix=new s.Matrix,n}return l(t,e),Object.defineProperty(t.prototype,"maskSprite",{get:function(){return this._maskSprite},set:function(e){this._maskSprite=e,this._maskSprite&&(this._maskSprite.renderable=!1)},enumerable:!1,configurable:!0}),t.prototype.apply=function(e,t,r,i){var n=this._maskSprite,o=n._texture;o.valid&&(o.uvMatrix||(o.uvMatrix=new Be(o,0)),o.uvMatrix.update(),this.uniforms.npmAlpha=o.baseTexture.alphaMode?0:1,this.uniforms.mask=o,this.uniforms.otherMatrix=e.calculateSpriteMatrix(this.maskMatrix,n).prepend(o.uvMatrix.mapCoord),this.uniforms.alpha=n.worldAlpha,this.uniforms.maskClamp=o.uvMatrix.uClampFrame,e.applyFilter(this,t,r,i))},t}(Pe),Le=function(){function e(e){this.renderer=e,this.enableScissor=!0,this.alphaMaskPool=[],this.maskDataPool=[],this.maskStack=[],this.alphaMaskIndex=0}return e.prototype.setMaskStack=function(e){this.maskStack=e,this.renderer.scissor.setMaskStack(e),this.renderer.stencil.setMaskStack(e)},e.prototype.push=function(e,r){var i=r;if(!i.isMaskData){var n=this.maskDataPool.pop()||new se;n.pooled=!0,n.maskObject=r,i=n}var o=0!==this.maskStack.length?this.maskStack[this.maskStack.length-1]:null;if(i.copyCountersOrReset(o),i._colorMask=o?o._colorMask:15,i.autoDetect&&this.detect(i),i._target=e,i.type!==t.MASK_TYPES.SPRITE&&this.maskStack.push(i),i.enabled)switch(i.type){case t.MASK_TYPES.SCISSOR:this.renderer.scissor.push(i);break;case t.MASK_TYPES.STENCIL:this.renderer.stencil.push(i);break;case t.MASK_TYPES.SPRITE:i.copyCountersOrReset(null),this.pushSpriteMask(i);break;case t.MASK_TYPES.COLOR:this.pushColorMask(i)}i.type===t.MASK_TYPES.SPRITE&&this.maskStack.push(i)},e.prototype.pop=function(e){var r=this.maskStack.pop();if(r&&r._target===e){if(r.enabled)switch(r.type){case t.MASK_TYPES.SCISSOR:this.renderer.scissor.pop(r);break;case t.MASK_TYPES.STENCIL:this.renderer.stencil.pop(r.maskObject);break;case t.MASK_TYPES.SPRITE:this.popSpriteMask(r);break;case t.MASK_TYPES.COLOR:this.popColorMask(r)}if(r.reset(),r.pooled&&this.maskDataPool.push(r),0!==this.maskStack.length){var i=this.maskStack[this.maskStack.length-1];i.type===t.MASK_TYPES.SPRITE&&i._filters&&(i._filters[0].maskSprite=i.maskObject)}}},e.prototype.detect=function(e){var r=e.maskObject;r?r.isSprite?e.type=t.MASK_TYPES.SPRITE:this.enableScissor&&this.renderer.scissor.testScissor(e)?e.type=t.MASK_TYPES.SCISSOR:e.type=t.MASK_TYPES.STENCIL:e.type=t.MASK_TYPES.COLOR},e.prototype.pushSpriteMask=function(e){var t,r,i=e.maskObject,n=e._target,o=e._filters;o||(o=this.alphaMaskPool[this.alphaMaskIndex])||(o=this.alphaMaskPool[this.alphaMaskIndex]=[new De]);var s,a,u=this.renderer,h=u.renderTexture;if(h.current){var l=h.current;s=e.resolution||l.resolution,a=null!==(t=e.multisample)&&void 0!==t?t:l.multisample}else s=e.resolution||u.resolution,a=null!==(r=e.multisample)&&void 0!==r?r:u.multisample;o[0].resolution=s,o[0].multisample=a,o[0].maskSprite=i;var f=n.filterArea;n.filterArea=i.getBounds(!0),u.filter.push(n,o),n.filterArea=f,e._filters||this.alphaMaskIndex++},e.prototype.popSpriteMask=function(e){this.renderer.filter.pop(),e._filters?e._filters[0].maskSprite=null:(this.alphaMaskIndex--,this.alphaMaskPool[this.alphaMaskIndex][0].maskSprite=null)},e.prototype.pushColorMask=function(e){var t=e._colorMask,r=e._colorMask=t&e.colorMask;r!==t&&this.renderer.gl.colorMask(0!=(1&r),0!=(2&r),0!=(4&r),0!=(8&r))},e.prototype.popColorMask=function(e){var t=e._colorMask,r=this.maskStack.length>0?this.maskStack[this.maskStack.length-1]._colorMask:15;r!==t&&this.renderer.gl.colorMask(0!=(1&r),0!=(2&r),0!=(4&r),0!=(8&r))},e.prototype.destroy=function(){this.renderer=null},e}(),Ue=function(){function e(e){this.renderer=e,this.maskStack=[],this.glConst=0}return e.prototype.getStackLength=function(){return this.maskStack.length},e.prototype.setMaskStack=function(e){var t=this.renderer.gl,r=this.getStackLength();this.maskStack=e;var i=this.getStackLength();i!==r&&(0===i?t.disable(this.glConst):(t.enable(this.glConst),this._useCurrent()))},e.prototype._useCurrent=function(){},e.prototype.destroy=function(){this.renderer=null,this.maskStack=null},e}(),Ge=new s.Matrix,ke=[],Ve=function(t){function r(r){var i=t.call(this,r)||this;return i.glConst=e.settings.ADAPTER.getWebGLRenderingContext().SCISSOR_TEST,i}return l(r,t),r.prototype.getStackLength=function(){var e=this.maskStack[this.maskStack.length-1];return e?e._scissorCounter:0},r.prototype.calcScissorRect=function(e){var t;if(!e._scissorRectLocal){var r=e._scissorRect,i=e.maskObject,n=this.renderer,o=n.renderTexture,a=i.getBounds(!0,null!==(t=ke.pop())&&void 0!==t?t:new s.Rectangle);this.roundFrameToPixels(a,o.current?o.current.resolution:n.resolution,o.sourceFrame,o.destinationFrame,n.projection.transform),r&&a.fit(r),e._scissorRectLocal=a}},r.isMatrixRotated=function(e){if(!e)return!1;var t=e.a,r=e.b,i=e.c,n=e.d;return(Math.abs(r)>1e-4||Math.abs(i)>1e-4)&&(Math.abs(t)>1e-4||Math.abs(n)>1e-4)},r.prototype.testScissor=function(e){var t=e.maskObject;if(!t.isFastRect||!t.isFastRect())return!1;if(r.isMatrixRotated(t.worldTransform))return!1;if(r.isMatrixRotated(this.renderer.projection.transform))return!1;this.calcScissorRect(e);var i=e._scissorRectLocal;return i.width>0&&i.height>0},r.prototype.roundFrameToPixels=function(e,t,i,n,o){r.isMatrixRotated(o)||((o=o?Ge.copyFrom(o):Ge.identity()).translate(-i.x,-i.y).scale(n.width/i.width,n.height/i.height).translate(n.x,n.y),this.renderer.filter.transformAABB(o,e),e.fit(n),e.x=Math.round(e.x*t),e.y=Math.round(e.y*t),e.width=Math.round(e.width*t),e.height=Math.round(e.height*t))},r.prototype.push=function(e){e._scissorRectLocal||this.calcScissorRect(e);var t=this.renderer.gl;e._scissorRect||t.enable(t.SCISSOR_TEST),e._scissorCounter++,e._scissorRect=e._scissorRectLocal,this._useCurrent()},r.prototype.pop=function(e){var t=this.renderer.gl;e&&ke.push(e._scissorRectLocal),this.getStackLength()>0?this._useCurrent():t.disable(t.SCISSOR_TEST)},r.prototype._useCurrent=function(){var e,t=this.maskStack[this.maskStack.length-1]._scissorRect;e=this.renderer.renderTexture.current?t.y:this.renderer.height-t.height-t.y,this.renderer.gl.scissor(t.x,e,t.width,t.height)},r}(Ue),He=function(t){function r(r){var i=t.call(this,r)||this;return i.glConst=e.settings.ADAPTER.getWebGLRenderingContext().STENCIL_TEST,i}return l(r,t),r.prototype.getStackLength=function(){var e=this.maskStack[this.maskStack.length-1];return e?e._stencilCounter:0},r.prototype.push=function(e){var t=e.maskObject,r=this.renderer.gl,i=e._stencilCounter;0===i&&(this.renderer.framebuffer.forceStencil(),r.clearStencil(0),r.clear(r.STENCIL_BUFFER_BIT),r.enable(r.STENCIL_TEST)),e._stencilCounter++;var n=e._colorMask;0!==n&&(e._colorMask=0,r.colorMask(!1,!1,!1,!1)),r.stencilFunc(r.EQUAL,i,4294967295),r.stencilOp(r.KEEP,r.KEEP,r.INCR),t.renderable=!0,t.render(this.renderer),this.renderer.batch.flush(),t.renderable=!1,0!==n&&(e._colorMask=n,r.colorMask(0!=(1&n),0!=(2&n),0!=(4&n),0!=(8&n))),this._useCurrent()},r.prototype.pop=function(e){var t=this.renderer.gl;if(0===this.getStackLength())t.disable(t.STENCIL_TEST);else{var r=0!==this.maskStack.length?this.maskStack[this.maskStack.length-1]:null,i=r?r._colorMask:15;0!==i&&(r._colorMask=0,t.colorMask(!1,!1,!1,!1)),t.stencilOp(t.KEEP,t.KEEP,t.DECR),e.renderable=!0,e.render(this.renderer),this.renderer.batch.flush(),e.renderable=!1,0!==i&&(r._colorMask=i,t.colorMask(0!=(1&i),0!=(2&i),0!=(4&i),0!=(8&i))),this._useCurrent()}},r.prototype._useCurrent=function(){var e=this.renderer.gl;e.stencilFunc(e.EQUAL,this.getStackLength(),4294967295),e.stencilOp(e.KEEP,e.KEEP,e.KEEP)},r}(Ue),je=function(){function e(e){this.renderer=e,this.destinationFrame=null,this.sourceFrame=null,this.defaultFrame=null,this.projectionMatrix=new s.Matrix,this.transform=null}return e.prototype.update=function(e,t,r,i){this.destinationFrame=e||this.destinationFrame||this.defaultFrame,this.sourceFrame=t||this.sourceFrame||e,this.calculateProjection(this.destinationFrame,this.sourceFrame,r,i),this.transform&&this.projectionMatrix.append(this.transform);var n=this.renderer;n.globalUniforms.uniforms.projectionMatrix=this.projectionMatrix,n.globalUniforms.update(),n.shader.shader&&n.shader.syncUniformGroup(n.shader.shader.uniforms.globals)},e.prototype.calculateProjection=function(e,t,r,i){var n=this.projectionMatrix,o=i?-1:1;n.identity(),n.a=1/t.width*2,n.d=o*(1/t.height*2),n.tx=-1-t.x*n.a,n.ty=-o-t.y*n.d},e.prototype.setTransform=function(e){},e.prototype.destroy=function(){this.renderer=null},e}(),ze=new s.Rectangle,Xe=new s.Rectangle,Ye=function(){function e(e){this.renderer=e,this.clearColor=e._backgroundColorRgba,this.defaultMaskStack=[],this.current=null,this.sourceFrame=new s.Rectangle,this.destinationFrame=new s.Rectangle,this.viewportFrame=new s.Rectangle}return e.prototype.bind=function(e,t,r){void 0===e&&(e=null);var i,n,o,s=this.renderer;this.current=e,e?(o=(i=e.baseTexture).resolution,t||(ze.width=e.frame.width,ze.height=e.frame.height,t=ze),r||(Xe.x=e.frame.x,Xe.y=e.frame.y,Xe.width=t.width,Xe.height=t.height,r=Xe),n=i.framebuffer):(o=s.resolution,t||(ze.width=s.screen.width,ze.height=s.screen.height,t=ze),r||((r=ze).width=t.width,r.height=t.height));var a=this.viewportFrame;a.x=r.x*o,a.y=r.y*o,a.width=r.width*o,a.height=r.height*o,e||(a.y=s.view.height-(a.y+a.height)),a.ceil(),this.renderer.framebuffer.bind(n,a),this.renderer.projection.update(r,t,o,!n),e?this.renderer.mask.setMaskStack(i.maskStack):this.renderer.mask.setMaskStack(this.defaultMaskStack),this.sourceFrame.copyFrom(t),this.destinationFrame.copyFrom(r)},e.prototype.clear=function(e,t){e=this.current?e||this.current.baseTexture.clearColor:e||this.clearColor;var r=this.destinationFrame,i=this.current?this.current.baseTexture:this.renderer.screen,n=r.width!==i.width||r.height!==i.height;if(n){var o=this.viewportFrame,s=o.x,a=o.y,u=o.width,h=o.height;s=Math.round(s),a=Math.round(a),u=Math.round(u),h=Math.round(h),this.renderer.gl.enable(this.renderer.gl.SCISSOR_TEST),this.renderer.gl.scissor(s,a,u,h)}this.renderer.framebuffer.clear(e[0],e[1],e[2],e[3],t),n&&this.renderer.scissor.pop()},e.prototype.resize=function(){this.bind(null)},e.prototype.reset=function(){this.bind(null)},e.prototype.destroy=function(){this.renderer=null},e}();function We(e,t,r,i,n){r.buffer.update(n)}var Ke={float:"\n        data[offset] = v;\n    ",vec2:"\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n    ",vec3:"\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n\n    ",vec4:"\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n        data[offset+3] = v[3];\n    ",mat2:"\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n\n        data[offset+4] = v[2];\n        data[offset+5] = v[3];\n    ",mat3:"\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n\n        data[offset + 4] = v[3];\n        data[offset + 5] = v[4];\n        data[offset + 6] = v[5];\n\n        data[offset + 8] = v[6];\n        data[offset + 9] = v[7];\n        data[offset + 10] = v[8];\n    ",mat4:"\n        for(var i = 0; i < 16; i++)\n        {\n            data[offset + i] = v[i];\n        }\n    "},Qe={float:4,vec2:8,vec3:12,vec4:16,int:4,ivec2:8,ivec3:12,ivec4:16,uint:4,uvec2:8,uvec3:12,uvec4:16,bool:4,bvec2:8,bvec3:12,bvec4:16,mat2:32,mat3:48,mat4:64};function qe(e){for(var t=e.map((function(e){return{data:e,offset:0,dataLen:0,dirty:0}})),r=0,i=0,n=0,o=0;o<t.length;o++){var s=t[o];if(r=Qe[s.data.type],s.data.size>1&&(r=Math.max(r,16)*s.data.size),s.dataLen=r,i%r!=0&&i<16){var a=i%r%16;i+=a,n+=a}i+r>16?(n=16*Math.ceil(n/16),s.offset=n,n+=r,i=r):(s.offset=n,i+=r,n+=r)}return{uboElements:t,size:n=16*Math.ceil(n/16)}}function Ze(e,t){var r=[];for(var i in e)t[i]&&r.push(t[i]);return r.sort((function(e,t){return e.index-t.index})),r}function $e(e,t){if(!e.autoManage)return{size:0,syncFunc:We};for(var r=qe(Ze(e.uniforms,t)),i=r.uboElements,n=r.size,o=["\n    var v = null;\n    var v2 = null;\n    var cv = null;\n    var t = 0;\n    var gl = renderer.gl\n    var index = 0;\n    var data = buffer.data;\n    "],s=0;s<i.length;s++){for(var a=i[s],u=e.uniforms[a.data.name],h=a.data.name,l=!1,f=0;f<Te.length;f++){var d=Te[f];if(d.codeUbo&&d.test(a.data,u)){o.push("offset = "+a.offset/4+";",Te[f].codeUbo(a.data.name,u)),l=!0;break}}if(!l)if(a.data.size>1){var c=ge(a.data.type),p=Math.max(Qe[a.data.type]/16,1),v=c/p,m=(4-v%4)%4;o.push("\n                cv = ud."+h+".value;\n                v = uv."+h+";\n                offset = "+a.offset/4+";\n\n                t = 0;\n\n                for(var i=0; i < "+a.data.size*p+"; i++)\n                {\n                    for(var j = 0; j < "+v+"; j++)\n                    {\n                        data[offset++] = v[t++];\n                    }\n                    offset += "+m+";\n                }\n\n                ")}else{var g=Ke[a.data.type];o.push("\n                cv = ud."+h+".value;\n                v = uv."+h+";\n                offset = "+a.offset/4+";\n                "+g+";\n                ")}}return o.push("\n       renderer.buffer.update(buffer);\n    "),{size:n,syncFunc:new Function("ud","uv","renderer","syncData","buffer",o.join("\n"))}}var Je=function(){},et=function(){function e(e,t){this.program=e,this.uniformData=t,this.uniformGroups={},this.uniformDirtyGroups={},this.uniformBufferBindings={}}return e.prototype.destroy=function(){this.uniformData=null,this.uniformGroups=null,this.uniformDirtyGroups=null,this.uniformBufferBindings=null,this.program=null},e}();function tt(e,t){var r=ae(e,e.VERTEX_SHADER,t.vertexSrc),i=ae(e,e.FRAGMENT_SHADER,t.fragmentSrc),n=e.createProgram();if(e.attachShader(n,r),e.attachShader(n,i),e.linkProgram(n),e.getProgramParameter(n,e.LINK_STATUS)||function(e,t,r,i){e.getProgramParameter(t,e.LINK_STATUS)||(e.getShaderParameter(r,e.COMPILE_STATUS)||ue(e,r),e.getShaderParameter(i,e.COMPILE_STATUS)||ue(e,i),console.error("PixiJS Error: Could not initialize shader."),""!==e.getProgramInfoLog(t)&&console.warn("PixiJS Warning: gl.getProgramInfoLog()",e.getProgramInfoLog(t)))}(e,n,r,i),t.attributeData=function(e,t){for(var r={},i=t.getProgramParameter(e,t.ACTIVE_ATTRIBUTES),n=0;n<i;n++){var o=t.getActiveAttrib(e,n);if(0!==o.name.indexOf("gl_")){var s=Ee(t,o.type),a={type:s,name:o.name,size:ge(s),location:t.getAttribLocation(e,o.name)};r[o.name]=a}}return r}(n,e),t.uniformData=function(e,t){for(var r={},i=t.getProgramParameter(e,t.ACTIVE_UNIFORMS),n=0;n<i;n++){var o=t.getActiveUniform(e,n),s=o.name.replace(/\[.*?\]$/,""),a=!!o.name.match(/\[.*?\]$/),u=Ee(t,o.type);r[s]={name:s,index:n,type:u,size:o.size,isArray:a,value:le(u,o.size)}}return r}(n,e),!/^[ \t]*#[ \t]*version[ \t]+300[ \t]+es[ \t]*$/m.test(t.vertexSrc)){var o=Object.keys(t.attributeData);o.sort((function(e,t){return e>t?1:-1}));for(var s=0;s<o.length;s++)t.attributeData[o[s]].location=s,e.bindAttribLocation(n,s,o[s]);e.linkProgram(n)}e.deleteShader(r),e.deleteShader(i);var a={};for(var s in t.uniformData){var u=t.uniformData[s];a[s]={location:e.getUniformLocation(n,s),value:le(u.type,u.size)}}return new et(n,a)}var rt=0,it={textureCount:0,uboCount:0},nt=function(){function e(e){this.destroyed=!1,this.renderer=e,this.systemCheck(),this.gl=null,this.shader=null,this.program=null,this.cache={},this._uboCache={},this.id=rt++}return e.prototype.systemCheck=function(){if(!function(){if("boolean"==typeof Se)return Se;try{var e=new Function("param1","param2","param3","return param1[param2] === param3;");Se=!0===e({a:"b"},"a","b")}catch(e){Se=!1}return Se}())throw new Error("Current environment does not allow unsafe-eval, please use @pixi/unsafe-eval module to enable support.")},e.prototype.contextChange=function(e){this.gl=e,this.reset()},e.prototype.bind=function(e,t){e.disposeRunner.add(this),e.uniforms.globals=this.renderer.globalUniforms;var r=e.program,i=r.glPrograms[this.renderer.CONTEXT_UID]||this.generateProgram(e);return this.shader=e,this.program!==r&&(this.program=r,this.gl.useProgram(i.program)),t||(it.textureCount=0,it.uboCount=0,this.syncUniformGroup(e.uniformGroup,it)),i},e.prototype.setUniforms=function(e){var t=this.shader.program,r=t.glPrograms[this.renderer.CONTEXT_UID];t.syncUniforms(r.uniformData,e,this.renderer)},e.prototype.syncUniformGroup=function(e,t){var r=this.getGlProgram();e.static&&e.dirtyId===r.uniformDirtyGroups[e.id]||(r.uniformDirtyGroups[e.id]=e.dirtyId,this.syncUniforms(e,r,t))},e.prototype.syncUniforms=function(e,t,r){(e.syncUniforms[this.shader.program.id]||this.createSyncGroups(e))(t.uniformData,e.uniforms,this.renderer,r)},e.prototype.createSyncGroups=function(e){var t=this.getSignature(e,this.shader.program.uniformData,"u");return this.cache[t]||(this.cache[t]=function(e,t){var r,i=["\n        var v = null;\n        var cv = null;\n        var cu = null;\n        var t = 0;\n        var gl = renderer.gl;\n    "];for(var n in e.uniforms){var o=t[n];if(o){for(var s=e.uniforms[n],a=!1,u=0;u<Te.length;u++)if(Te[u].test(o,s)){i.push(Te[u].code(n,s)),a=!0;break}if(!a){var h=(1!==o.size||o.isArray?be:xe)[o.type].replace("location",'ud["'+n+'"].location');i.push('\n            cu = ud["'+n+'"];\n            cv = cu.value;\n            v = uv["'+n+'"];\n            '+h+";")}}else(null===(r=e.uniforms[n])||void 0===r?void 0:r.group)&&(e.uniforms[n].ubo?i.push("\n                        renderer.shader.syncUniformBufferGroup(uv."+n+", '"+n+"');\n                    "):i.push("\n                        renderer.shader.syncUniformGroup(uv."+n+", syncData);\n                    "))}return new Function("ud","uv","renderer","syncData",i.join("\n"))}(e,this.shader.program.uniformData)),e.syncUniforms[this.shader.program.id]=this.cache[t],e.syncUniforms[this.shader.program.id]},e.prototype.syncUniformBufferGroup=function(e,t){var r=this.getGlProgram();if(!e.static||0!==e.dirtyId||!r.uniformGroups[e.id]){e.dirtyId=0;var i=r.uniformGroups[e.id]||this.createSyncBufferGroup(e,r,t);e.buffer.update(),i(r.uniformData,e.uniforms,this.renderer,it,e.buffer)}this.renderer.buffer.bindBufferBase(e.buffer,r.uniformBufferBindings[t])},e.prototype.createSyncBufferGroup=function(e,t,r){var i=this.renderer.gl;this.renderer.buffer.bind(e.buffer);var n=this.gl.getUniformBlockIndex(t.program,r);t.uniformBufferBindings[r]=this.shader.uniformBindCount,i.uniformBlockBinding(t.program,n,this.shader.uniformBindCount),this.shader.uniformBindCount++;var o=this.getSignature(e,this.shader.program.uniformData,"ubo"),s=this._uboCache[o];if(s||(s=this._uboCache[o]=$e(e,this.shader.program.uniformData)),e.autoManage){var a=new Float32Array(s.size/4);e.buffer.update(a)}return t.uniformGroups[e.id]=s.syncFunc,t.uniformGroups[e.id]},e.prototype.getSignature=function(e,t,r){var i=e.uniforms,n=[r+"-"];for(var o in i)n.push(o),t[o]&&n.push(t[o].type);return n.join("-")},e.prototype.getGlProgram=function(){return this.shader?this.shader.program.glPrograms[this.renderer.CONTEXT_UID]:null},e.prototype.generateProgram=function(e){var t=this.gl,r=e.program,i=tt(t,r);return r.glPrograms[this.renderer.CONTEXT_UID]=i,i},e.prototype.reset=function(){this.program=null,this.shader=null},e.prototype.disposeShader=function(e){this.shader===e&&(this.shader=null)},e.prototype.destroy=function(){this.renderer=null,this.destroyed=!0},e}();var ot=function(){function e(){this.gl=null,this.stateId=0,this.polygonOffset=0,this.blendMode=t.BLEND_MODES.NONE,this._blendEq=!1,this.map=[],this.map[0]=this.setBlend,this.map[1]=this.setOffset,this.map[2]=this.setCullFace,this.map[3]=this.setDepthTest,this.map[4]=this.setFrontFace,this.map[5]=this.setDepthMask,this.checks=[],this.defaultState=new Fe,this.defaultState.blend=!0}return e.prototype.contextChange=function(e){this.gl=e,this.blendModes=function(e,r){return void 0===r&&(r=[]),r[t.BLEND_MODES.NORMAL]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.ADD]=[e.ONE,e.ONE],r[t.BLEND_MODES.MULTIPLY]=[e.DST_COLOR,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.SCREEN]=[e.ONE,e.ONE_MINUS_SRC_COLOR,e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.OVERLAY]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.DARKEN]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.LIGHTEN]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.COLOR_DODGE]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.COLOR_BURN]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.HARD_LIGHT]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.SOFT_LIGHT]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.DIFFERENCE]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.EXCLUSION]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.HUE]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.SATURATION]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.COLOR]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.LUMINOSITY]=[e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.NONE]=[0,0],r[t.BLEND_MODES.NORMAL_NPM]=[e.SRC_ALPHA,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.ADD_NPM]=[e.SRC_ALPHA,e.ONE,e.ONE,e.ONE],r[t.BLEND_MODES.SCREEN_NPM]=[e.SRC_ALPHA,e.ONE_MINUS_SRC_COLOR,e.ONE,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.SRC_IN]=[e.DST_ALPHA,e.ZERO],r[t.BLEND_MODES.SRC_OUT]=[e.ONE_MINUS_DST_ALPHA,e.ZERO],r[t.BLEND_MODES.SRC_ATOP]=[e.DST_ALPHA,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.DST_OVER]=[e.ONE_MINUS_DST_ALPHA,e.ONE],r[t.BLEND_MODES.DST_IN]=[e.ZERO,e.SRC_ALPHA],r[t.BLEND_MODES.DST_OUT]=[e.ZERO,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.DST_ATOP]=[e.ONE_MINUS_DST_ALPHA,e.SRC_ALPHA],r[t.BLEND_MODES.XOR]=[e.ONE_MINUS_DST_ALPHA,e.ONE_MINUS_SRC_ALPHA],r[t.BLEND_MODES.SUBTRACT]=[e.ONE,e.ONE,e.ONE,e.ONE,e.FUNC_REVERSE_SUBTRACT,e.FUNC_ADD],r}(e),this.set(this.defaultState),this.reset()},e.prototype.set=function(e){if(e=e||this.defaultState,this.stateId!==e.data){for(var t=this.stateId^e.data,r=0;t;)1&t&&this.map[r].call(this,!!(e.data&1<<r)),t>>=1,r++;this.stateId=e.data}for(r=0;r<this.checks.length;r++)this.checks[r](this,e)},e.prototype.forceState=function(e){e=e||this.defaultState;for(var t=0;t<this.map.length;t++)this.map[t].call(this,!!(e.data&1<<t));for(t=0;t<this.checks.length;t++)this.checks[t](this,e);this.stateId=e.data},e.prototype.setBlend=function(t){this.updateCheck(e.checkBlendMode,t),this.gl[t?"enable":"disable"](this.gl.BLEND)},e.prototype.setOffset=function(t){this.updateCheck(e.checkPolygonOffset,t),this.gl[t?"enable":"disable"](this.gl.POLYGON_OFFSET_FILL)},e.prototype.setDepthTest=function(e){this.gl[e?"enable":"disable"](this.gl.DEPTH_TEST)},e.prototype.setDepthMask=function(e){this.gl.depthMask(e)},e.prototype.setCullFace=function(e){this.gl[e?"enable":"disable"](this.gl.CULL_FACE)},e.prototype.setFrontFace=function(e){this.gl.frontFace(this.gl[e?"CW":"CCW"])},e.prototype.setBlendMode=function(e){if(e!==this.blendMode){this.blendMode=e;var t=this.blendModes[e],r=this.gl;2===t.length?r.blendFunc(t[0],t[1]):r.blendFuncSeparate(t[0],t[1],t[2],t[3]),6===t.length?(this._blendEq=!0,r.blendEquationSeparate(t[4],t[5])):this._blendEq&&(this._blendEq=!1,r.blendEquationSeparate(r.FUNC_ADD,r.FUNC_ADD))}},e.prototype.setPolygonOffset=function(e,t){this.gl.polygonOffset(e,t)},e.prototype.reset=function(){this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL,!1),this.forceState(this.defaultState),this._blendEq=!0,this.blendMode=-1,this.setBlendMode(0)},e.prototype.updateCheck=function(e,t){var r=this.checks.indexOf(e);t&&-1===r?this.checks.push(e):t||-1===r||this.checks.splice(r,1)},e.checkBlendMode=function(e,t){e.setBlendMode(t.blendMode)},e.checkPolygonOffset=function(e,t){e.setPolygonOffset(1,t.polygonOffset)},e.prototype.destroy=function(){this.gl=null},e}(),st=function(){function r(t){this.renderer=t,this.count=0,this.checkCount=0,this.maxIdle=e.settings.GC_MAX_IDLE,this.checkCountMax=e.settings.GC_MAX_CHECK_COUNT,this.mode=e.settings.GC_MODE}return r.prototype.postrender=function(){this.renderer.renderingToScreen&&(this.count++,this.mode!==t.GC_MODES.MANUAL&&(this.checkCount++,this.checkCount>this.checkCountMax&&(this.checkCount=0,this.run())))},r.prototype.run=function(){for(var e=this.renderer.texture,t=e.managedTextures,r=!1,i=0;i<t.length;i++){var n=t[i];!n.framebuffer&&this.count-n.touched>this.maxIdle&&(e.destroyTexture(n,!0),t[i]=null,r=!0)}if(r){var o=0;for(i=0;i<t.length;i++)null!==t[i]&&(t[o++]=t[i]);t.length=o}},r.prototype.unload=function(e){var t=this.renderer.texture,r=e._texture;r&&!r.framebuffer&&t.destroyTexture(r);for(var i=e.children.length-1;i>=0;i--)this.unload(e.children[i])},r.prototype.destroy=function(){this.renderer=null},r}();var at=function(e){this.texture=e,this.width=-1,this.height=-1,this.dirtyId=-1,this.dirtyStyleId=-1,this.mipmap=!1,this.wrapMode=33071,this.type=t.TYPES.UNSIGNED_BYTE,this.internalFormat=t.FORMATS.RGBA,this.samplerType=0},ut=function(){function e(e){this.renderer=e,this.boundTextures=[],this.currentLocation=-1,this.managedTextures=[],this._unknownBoundTextures=!1,this.unknownTexture=new v,this.hasIntegerTextures=!1}return e.prototype.contextChange=function(){var e=this.gl=this.renderer.gl;this.CONTEXT_UID=this.renderer.CONTEXT_UID,this.webGLVersion=this.renderer.context.webGLVersion,this.internalFormats=function(e){var r,i,n,o,s,a,u,h,l,f,d,c,p,v,m,g,_,y,E,T,x,b,S;return"WebGL2RenderingContext"in globalThis&&e instanceof globalThis.WebGL2RenderingContext?((r={})[t.TYPES.UNSIGNED_BYTE]=((i={})[t.FORMATS.RGBA]=e.RGBA8,i[t.FORMATS.RGB]=e.RGB8,i[t.FORMATS.RG]=e.RG8,i[t.FORMATS.RED]=e.R8,i[t.FORMATS.RGBA_INTEGER]=e.RGBA8UI,i[t.FORMATS.RGB_INTEGER]=e.RGB8UI,i[t.FORMATS.RG_INTEGER]=e.RG8UI,i[t.FORMATS.RED_INTEGER]=e.R8UI,i[t.FORMATS.ALPHA]=e.ALPHA,i[t.FORMATS.LUMINANCE]=e.LUMINANCE,i[t.FORMATS.LUMINANCE_ALPHA]=e.LUMINANCE_ALPHA,i),r[t.TYPES.BYTE]=((n={})[t.FORMATS.RGBA]=e.RGBA8_SNORM,n[t.FORMATS.RGB]=e.RGB8_SNORM,n[t.FORMATS.RG]=e.RG8_SNORM,n[t.FORMATS.RED]=e.R8_SNORM,n[t.FORMATS.RGBA_INTEGER]=e.RGBA8I,n[t.FORMATS.RGB_INTEGER]=e.RGB8I,n[t.FORMATS.RG_INTEGER]=e.RG8I,n[t.FORMATS.RED_INTEGER]=e.R8I,n),r[t.TYPES.UNSIGNED_SHORT]=((o={})[t.FORMATS.RGBA_INTEGER]=e.RGBA16UI,o[t.FORMATS.RGB_INTEGER]=e.RGB16UI,o[t.FORMATS.RG_INTEGER]=e.RG16UI,o[t.FORMATS.RED_INTEGER]=e.R16UI,o[t.FORMATS.DEPTH_COMPONENT]=e.DEPTH_COMPONENT16,o),r[t.TYPES.SHORT]=((s={})[t.FORMATS.RGBA_INTEGER]=e.RGBA16I,s[t.FORMATS.RGB_INTEGER]=e.RGB16I,s[t.FORMATS.RG_INTEGER]=e.RG16I,s[t.FORMATS.RED_INTEGER]=e.R16I,s),r[t.TYPES.UNSIGNED_INT]=((a={})[t.FORMATS.RGBA_INTEGER]=e.RGBA32UI,a[t.FORMATS.RGB_INTEGER]=e.RGB32UI,a[t.FORMATS.RG_INTEGER]=e.RG32UI,a[t.FORMATS.RED_INTEGER]=e.R32UI,a[t.FORMATS.DEPTH_COMPONENT]=e.DEPTH_COMPONENT24,a),r[t.TYPES.INT]=((u={})[t.FORMATS.RGBA_INTEGER]=e.RGBA32I,u[t.FORMATS.RGB_INTEGER]=e.RGB32I,u[t.FORMATS.RG_INTEGER]=e.RG32I,u[t.FORMATS.RED_INTEGER]=e.R32I,u),r[t.TYPES.FLOAT]=((h={})[t.FORMATS.RGBA]=e.RGBA32F,h[t.FORMATS.RGB]=e.RGB32F,h[t.FORMATS.RG]=e.RG32F,h[t.FORMATS.RED]=e.R32F,h[t.FORMATS.DEPTH_COMPONENT]=e.DEPTH_COMPONENT32F,h),r[t.TYPES.HALF_FLOAT]=((l={})[t.FORMATS.RGBA]=e.RGBA16F,l[t.FORMATS.RGB]=e.RGB16F,l[t.FORMATS.RG]=e.RG16F,l[t.FORMATS.RED]=e.R16F,l),r[t.TYPES.UNSIGNED_SHORT_5_6_5]=((f={})[t.FORMATS.RGB]=e.RGB565,f),r[t.TYPES.UNSIGNED_SHORT_4_4_4_4]=((d={})[t.FORMATS.RGBA]=e.RGBA4,d),r[t.TYPES.UNSIGNED_SHORT_5_5_5_1]=((c={})[t.FORMATS.RGBA]=e.RGB5_A1,c),r[t.TYPES.UNSIGNED_INT_2_10_10_10_REV]=((p={})[t.FORMATS.RGBA]=e.RGB10_A2,p[t.FORMATS.RGBA_INTEGER]=e.RGB10_A2UI,p),r[t.TYPES.UNSIGNED_INT_10F_11F_11F_REV]=((v={})[t.FORMATS.RGB]=e.R11F_G11F_B10F,v),r[t.TYPES.UNSIGNED_INT_5_9_9_9_REV]=((m={})[t.FORMATS.RGB]=e.RGB9_E5,m),r[t.TYPES.UNSIGNED_INT_24_8]=((g={})[t.FORMATS.DEPTH_STENCIL]=e.DEPTH24_STENCIL8,g),r[t.TYPES.FLOAT_32_UNSIGNED_INT_24_8_REV]=((_={})[t.FORMATS.DEPTH_STENCIL]=e.DEPTH32F_STENCIL8,_),S=r):((y={})[t.TYPES.UNSIGNED_BYTE]=((E={})[t.FORMATS.RGBA]=e.RGBA,E[t.FORMATS.RGB]=e.RGB,E[t.FORMATS.ALPHA]=e.ALPHA,E[t.FORMATS.LUMINANCE]=e.LUMINANCE,E[t.FORMATS.LUMINANCE_ALPHA]=e.LUMINANCE_ALPHA,E),y[t.TYPES.UNSIGNED_SHORT_5_6_5]=((T={})[t.FORMATS.RGB]=e.RGB,T),y[t.TYPES.UNSIGNED_SHORT_4_4_4_4]=((x={})[t.FORMATS.RGBA]=e.RGBA,x),y[t.TYPES.UNSIGNED_SHORT_5_5_5_1]=((b={})[t.FORMATS.RGBA]=e.RGBA,b),S=y),S}(e);var r=e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS);this.boundTextures.length=r;for(var i=0;i<r;i++)this.boundTextures[i]=null;this.emptyTextures={};var n=new at(e.createTexture());e.bindTexture(e.TEXTURE_2D,n.texture),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,1,1,0,e.RGBA,e.UNSIGNED_BYTE,new Uint8Array(4)),this.emptyTextures[e.TEXTURE_2D]=n,this.emptyTextures[e.TEXTURE_CUBE_MAP]=new at(e.createTexture()),e.bindTexture(e.TEXTURE_CUBE_MAP,this.emptyTextures[e.TEXTURE_CUBE_MAP].texture);for(i=0;i<6;i++)e.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,0,e.RGBA,1,1,0,e.RGBA,e.UNSIGNED_BYTE,null);e.texParameteri(e.TEXTURE_CUBE_MAP,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_CUBE_MAP,e.TEXTURE_MIN_FILTER,e.LINEAR);for(i=0;i<this.boundTextures.length;i++)this.bind(null,i)},e.prototype.bind=function(e,t){void 0===t&&(t=0);var r=this.gl;if((e=null==e?void 0:e.castToBaseTexture())&&e.valid&&!e.parentTextureArray){e.touched=this.renderer.textureGC.count;var i=e._glTextures[this.CONTEXT_UID]||this.initTexture(e);this.boundTextures[t]!==e&&(this.currentLocation!==t&&(this.currentLocation=t,r.activeTexture(r.TEXTURE0+t)),r.bindTexture(e.target,i.texture)),i.dirtyId!==e.dirtyId?(this.currentLocation!==t&&(this.currentLocation=t,r.activeTexture(r.TEXTURE0+t)),this.updateTexture(e)):i.dirtyStyleId!==e.dirtyStyleId&&this.updateTextureStyle(e),this.boundTextures[t]=e}else this.currentLocation!==t&&(this.currentLocation=t,r.activeTexture(r.TEXTURE0+t)),r.bindTexture(r.TEXTURE_2D,this.emptyTextures[r.TEXTURE_2D].texture),this.boundTextures[t]=null},e.prototype.reset=function(){this._unknownBoundTextures=!0,this.hasIntegerTextures=!1,this.currentLocation=-1;for(var e=0;e<this.boundTextures.length;e++)this.boundTextures[e]=this.unknownTexture},e.prototype.unbind=function(e){var t=this.gl,r=this.boundTextures;if(this._unknownBoundTextures){this._unknownBoundTextures=!1;for(var i=0;i<r.length;i++)r[i]===this.unknownTexture&&this.bind(null,i)}for(i=0;i<r.length;i++)r[i]===e&&(this.currentLocation!==i&&(t.activeTexture(t.TEXTURE0+i),this.currentLocation=i),t.bindTexture(e.target,this.emptyTextures[e.target].texture),r[i]=null)},e.prototype.ensureSamplerType=function(e){var r=this,i=r.boundTextures,n=r.hasIntegerTextures,o=r.CONTEXT_UID;if(n)for(var s=e-1;s>=0;--s){var a=i[s];if(a)a._glTextures[o].samplerType!==t.SAMPLER_TYPES.FLOAT&&this.renderer.texture.unbind(a)}},e.prototype.initTexture=function(e){var t=new at(this.gl.createTexture());return t.dirtyId=-1,e._glTextures[this.CONTEXT_UID]=t,this.managedTextures.push(e),e.on("dispose",this.destroyTexture,this),t},e.prototype.initTextureType=function(e,r){var i,n;r.internalFormat=null!==(n=null===(i=this.internalFormats[e.type])||void 0===i?void 0:i[e.format])&&void 0!==n?n:e.format,2===this.webGLVersion&&e.type===t.TYPES.HALF_FLOAT?r.type=this.gl.HALF_FLOAT:r.type=e.type},e.prototype.updateTexture=function(e){var r=e._glTextures[this.CONTEXT_UID];if(r){var i=this.renderer;if(this.initTextureType(e,r),e.resource&&e.resource.upload(i,e,r))r.samplerType!==t.SAMPLER_TYPES.FLOAT&&(this.hasIntegerTextures=!0);else{var n=e.realWidth,o=e.realHeight,s=i.gl;(r.width!==n||r.height!==o||r.dirtyId<0)&&(r.width=n,r.height=o,s.texImage2D(e.target,0,r.internalFormat,n,o,0,e.format,r.type,null))}e.dirtyStyleId!==r.dirtyStyleId&&this.updateTextureStyle(e),r.dirtyId=e.dirtyId}},e.prototype.destroyTexture=function(e,t){var i=this.gl;if((e=e.castToBaseTexture())._glTextures[this.CONTEXT_UID]&&(this.unbind(e),i.deleteTexture(e._glTextures[this.CONTEXT_UID].texture),e.off("dispose",this.destroyTexture,this),delete e._glTextures[this.CONTEXT_UID],!t)){var n=this.managedTextures.indexOf(e);-1!==n&&r.removeItems(this.managedTextures,n,1)}},e.prototype.updateTextureStyle=function(e){var r=e._glTextures[this.CONTEXT_UID];r&&(e.mipmap!==t.MIPMAP_MODES.POW2&&2===this.webGLVersion||e.isPowerOfTwo?r.mipmap=e.mipmap>=1:r.mipmap=!1,2===this.webGLVersion||e.isPowerOfTwo?r.wrapMode=e.wrapMode:r.wrapMode=t.WRAP_MODES.CLAMP,e.resource&&e.resource.style(this.renderer,e,r)||this.setStyle(e,r),r.dirtyStyleId=e.dirtyStyleId)},e.prototype.setStyle=function(e,r){var i=this.gl;if(r.mipmap&&e.mipmap!==t.MIPMAP_MODES.ON_MANUAL&&i.generateMipmap(e.target),i.texParameteri(e.target,i.TEXTURE_WRAP_S,r.wrapMode),i.texParameteri(e.target,i.TEXTURE_WRAP_T,r.wrapMode),r.mipmap){i.texParameteri(e.target,i.TEXTURE_MIN_FILTER,e.scaleMode===t.SCALE_MODES.LINEAR?i.LINEAR_MIPMAP_LINEAR:i.NEAREST_MIPMAP_NEAREST);var n=this.renderer.context.extensions.anisotropicFiltering;if(n&&e.anisotropicLevel>0&&e.scaleMode===t.SCALE_MODES.LINEAR){var o=Math.min(e.anisotropicLevel,i.getParameter(n.MAX_TEXTURE_MAX_ANISOTROPY_EXT));i.texParameterf(e.target,n.TEXTURE_MAX_ANISOTROPY_EXT,o)}}else i.texParameteri(e.target,i.TEXTURE_MIN_FILTER,e.scaleMode===t.SCALE_MODES.LINEAR?i.LINEAR:i.NEAREST);i.texParameteri(e.target,i.TEXTURE_MAG_FILTER,e.scaleMode===t.SCALE_MODES.LINEAR?i.LINEAR:i.NEAREST)},e.prototype.destroy=function(){this.renderer=null},e}(),ht={__proto__:null,FilterSystem:q,BatchSystem:$,ContextSystem:ee,FramebufferSystem:ie,GeometrySystem:oe,MaskSystem:Le,ScissorSystem:Ve,StencilSystem:He,ProjectionSystem:je,RenderTextureSystem:Ye,ShaderSystem:nt,StateSystem:ot,TextureGCSystem:st,TextureSystem:ut},lt=new s.Matrix,ft=function(i){function n(r,n){void 0===r&&(r=t.RENDERER_TYPE.UNKNOWN);var o=i.call(this)||this;return n=Object.assign({},e.settings.RENDER_OPTIONS,n),o.options=n,o.type=r,o.screen=new s.Rectangle(0,0,n.width,n.height),o.view=n.view||e.settings.ADAPTER.createCanvas(),o.resolution=n.resolution||e.settings.RESOLUTION,o.useContextAlpha=n.useContextAlpha,o.autoDensity=!!n.autoDensity,o.preserveDrawingBuffer=n.preserveDrawingBuffer,o.clearBeforeRender=n.clearBeforeRender,o._backgroundColor=0,o._backgroundColorRgba=[0,0,0,1],o._backgroundColorString="#000000",o.backgroundColor=n.backgroundColor||o._backgroundColor,o.backgroundAlpha=n.backgroundAlpha,void 0!==n.transparent&&(o.useContextAlpha=n.transparent,o.backgroundAlpha=n.transparent?0:1),o._lastObjectRendered=null,o.plugins={},o}return l(n,i),n.prototype.initPlugins=function(e){for(var t in e)this.plugins[t]=new e[t](this)},Object.defineProperty(n.prototype,"width",{get:function(){return this.view.width},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"height",{get:function(){return this.view.height},enumerable:!1,configurable:!0}),n.prototype.resize=function(e,t){this.view.width=Math.round(e*this.resolution),this.view.height=Math.round(t*this.resolution);var r=this.view.width/this.resolution,i=this.view.height/this.resolution;this.screen.width=r,this.screen.height=i,this.autoDensity&&(this.view.style.width=r+"px",this.view.style.height=i+"px"),this.emit("resize",r,i)},n.prototype.generateTexture=function(e,t,r,i){void 0===t&&(t={}),"number"==typeof t&&(t={scaleMode:t,resolution:r,region:i});var n=t.region,o=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]])}return r}(t,["region"]);0===(i=n||e.getLocalBounds(null,!0)).width&&(i.width=1),0===i.height&&(i.height=1);var s=P.create(f({width:i.width,height:i.height},o));return lt.tx=-i.x,lt.ty=-i.y,this.render(e,{renderTexture:s,clear:!1,transform:lt,skipUpdateTransform:!!e.parent}),s},n.prototype.destroy=function(e){for(var r in this.plugins)this.plugins[r].destroy(),this.plugins[r]=null;e&&this.view.parentNode&&this.view.parentNode.removeChild(this.view);var i=this;i.plugins=null,i.type=t.RENDERER_TYPE.UNKNOWN,i.view=null,i.screen=null,i._tempDisplayObjectParent=null,i.options=null,this._backgroundColorRgba=null,this._backgroundColorString=null,this._lastObjectRendered=null},Object.defineProperty(n.prototype,"backgroundColor",{get:function(){return this._backgroundColor},set:function(e){this._backgroundColor=e,this._backgroundColorString=r.hex2string(e),r.hex2rgb(e,this._backgroundColorRgba)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"backgroundAlpha",{get:function(){return this._backgroundColorRgba[3]},set:function(e){this._backgroundColorRgba[3]=e},enumerable:!1,configurable:!0}),n}(r.EventEmitter),dt=function(e){this.buffer=e||null,this.updateID=-1,this.byteLength=-1,this.refCount=0},ct=function(){function e(e){this.renderer=e,this.managedBuffers={},this.boundBufferBases={}}return e.prototype.destroy=function(){this.renderer=null},e.prototype.contextChange=function(){this.disposeAll(!0),this.gl=this.renderer.gl,this.CONTEXT_UID=this.renderer.CONTEXT_UID},e.prototype.bind=function(e){var t=this.gl,r=this.CONTEXT_UID,i=e._glBuffers[r]||this.createGLBuffer(e);t.bindBuffer(e.type,i.buffer)},e.prototype.bindBufferBase=function(e,t){var r=this.gl,i=this.CONTEXT_UID;if(this.boundBufferBases[t]!==e){var n=e._glBuffers[i]||this.createGLBuffer(e);this.boundBufferBases[t]=e,r.bindBufferBase(r.UNIFORM_BUFFER,t,n.buffer)}},e.prototype.bindBufferRange=function(e,t,r){var i=this.gl,n=this.CONTEXT_UID;r=r||0;var o=e._glBuffers[n]||this.createGLBuffer(e);i.bindBufferRange(i.UNIFORM_BUFFER,t||0,o.buffer,256*r,256)},e.prototype.update=function(e){var t=this.gl,r=this.CONTEXT_UID,i=e._glBuffers[r];if(e._updateID!==i.updateID)if(i.updateID=e._updateID,t.bindBuffer(e.type,i.buffer),i.byteLength>=e.data.byteLength)t.bufferSubData(e.type,0,e.data);else{var n=e.static?t.STATIC_DRAW:t.DYNAMIC_DRAW;i.byteLength=e.data.byteLength,t.bufferData(e.type,e.data,n)}},e.prototype.dispose=function(e,t){if(this.managedBuffers[e.id]){delete this.managedBuffers[e.id];var r=e._glBuffers[this.CONTEXT_UID],i=this.gl;e.disposeRunner.remove(this),r&&(t||i.deleteBuffer(r.buffer),delete e._glBuffers[this.CONTEXT_UID])}},e.prototype.disposeAll=function(e){for(var t=Object.keys(this.managedBuffers),r=0;r<t.length;r++)this.dispose(this.managedBuffers[t[r]],e)},e.prototype.createGLBuffer=function(e){var t=this.CONTEXT_UID,r=this.gl;return e._glBuffers[t]=new dt(r.createBuffer()),this.managedBuffers[e.id]=e,e.disposeRunner.add(this),e._glBuffers[t]},e}(),pt=function(e){function o(i){var a=e.call(this,t.RENDERER_TYPE.WEBGL,i)||this;return i=a.options,a.gl=null,a.CONTEXT_UID=0,a.runners={destroy:new n.Runner("destroy"),contextChange:new n.Runner("contextChange"),reset:new n.Runner("reset"),update:new n.Runner("update"),postrender:new n.Runner("postrender"),prerender:new n.Runner("prerender"),resize:new n.Runner("resize")},a.runners.contextChange.add(a),a.globalUniforms=new Y({projectionMatrix:new s.Matrix},!0),a.addSystem(Le,"mask").addSystem(ee,"context").addSystem(ot,"state").addSystem(nt,"shader").addSystem(ut,"texture").addSystem(ct,"buffer").addSystem(oe,"geometry").addSystem(ie,"framebuffer").addSystem(Ve,"scissor").addSystem(He,"stencil").addSystem(je,"projection").addSystem(st,"textureGC").addSystem(q,"filter").addSystem(Ye,"renderTexture").addSystem($,"batch"),a.initPlugins(o.__plugins),a.multisample=void 0,i.context?a.context.initFromContext(i.context):a.context.initFromOptions({alpha:!!a.useContextAlpha,antialias:i.antialias,premultipliedAlpha:a.useContextAlpha&&"notMultiplied"!==a.useContextAlpha,stencil:!0,preserveDrawingBuffer:i.preserveDrawingBuffer,powerPreference:a.options.powerPreference}),a.renderingToScreen=!0,r.sayHello(2===a.context.webGLVersion?"WebGL 2":"WebGL 1"),a.resize(a.options.width,a.options.height),a}return l(o,e),o.create=function(e){if(r.isWebGLSupported())return new o(e);throw new Error('WebGL unsupported in this browser, use "pixi.js-legacy" for fallback canvas2d support.')},o.prototype.contextChange=function(){var e,r=this.gl;if(1===this.context.webGLVersion){var i=r.getParameter(r.FRAMEBUFFER_BINDING);r.bindFramebuffer(r.FRAMEBUFFER,null),e=r.getParameter(r.SAMPLES),r.bindFramebuffer(r.FRAMEBUFFER,i)}else{i=r.getParameter(r.DRAW_FRAMEBUFFER_BINDING);r.bindFramebuffer(r.DRAW_FRAMEBUFFER,null),e=r.getParameter(r.SAMPLES),r.bindFramebuffer(r.DRAW_FRAMEBUFFER,i)}e>=t.MSAA_QUALITY.HIGH?this.multisample=t.MSAA_QUALITY.HIGH:e>=t.MSAA_QUALITY.MEDIUM?this.multisample=t.MSAA_QUALITY.MEDIUM:e>=t.MSAA_QUALITY.LOW?this.multisample=t.MSAA_QUALITY.LOW:this.multisample=t.MSAA_QUALITY.NONE},o.prototype.addSystem=function(e,t){var r=new e(this);if(this[t])throw new Error('Whoops! The name "'+t+'" is already in use');for(var i in this[t]=r,this.runners)this.runners[i].add(r);return this},o.prototype.render=function(e,t){var r,i,n,o;if(t&&(t instanceof P?(r=t,i=arguments[2],n=arguments[3],o=arguments[4]):(r=t.renderTexture,i=t.clear,n=t.transform,o=t.skipUpdateTransform)),this.renderingToScreen=!r,this.runners.prerender.emit(),this.emit("prerender"),this.projection.transform=n,!this.context.isLost){if(r||(this._lastObjectRendered=e),!o){var s=e.enableTempParent();e.updateTransform(),e.disableTempParent(s)}this.renderTexture.bind(r),this.batch.currentRenderer.start(),(void 0!==i?i:this.clearBeforeRender)&&this.renderTexture.clear(),e.render(this),this.batch.currentRenderer.flush(),r&&r.baseTexture.update(),this.runners.postrender.emit(),this.projection.transform=null,this.emit("postrender")}},o.prototype.generateTexture=function(t,r,i,n){void 0===r&&(r={});var o=e.prototype.generateTexture.call(this,t,r,i,n);return this.framebuffer.blit(),o},o.prototype.resize=function(t,r){e.prototype.resize.call(this,t,r),this.runners.resize.emit(this.screen.height,this.screen.width)},o.prototype.reset=function(){return this.runners.reset.emit(),this},o.prototype.clear=function(){this.renderTexture.bind(),this.renderTexture.clear()},o.prototype.destroy=function(t){for(var r in this.runners.destroy.emit(),this.runners)this.runners[r].destroy();e.prototype.destroy.call(this,t),this.gl=null},Object.defineProperty(o.prototype,"extract",{get:function(){return this.plugins.extract},enumerable:!1,configurable:!0}),o.registerPlugin=function(e,t){i.extensions.add({name:e,type:i.ExtensionType.RendererPlugin,ref:t})},o.__plugins={},o}(ft);i.extensions.handleByMap(i.ExtensionType.RendererPlugin,pt.__plugins);var vt=function(){function e(e){this.renderer=e}return e.prototype.destroy=function(){this.renderer=null},e}(),mt=function(){this.texArray=null,this.blend=0,this.type=t.DRAW_MODES.TRIANGLES,this.start=0,this.size=0,this.data=null},gt=function(){function e(){this.elements=[],this.ids=[],this.count=0}return e.prototype.clear=function(){for(var e=0;e<this.count;e++)this.elements[e]=null;this.count=0},e}(),_t=function(){function e(e){"number"==typeof e?this.rawBinaryData=new ArrayBuffer(e):e instanceof Uint8Array?this.rawBinaryData=e.buffer:this.rawBinaryData=e,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData)}return Object.defineProperty(e.prototype,"int8View",{get:function(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"uint8View",{get:function(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"int16View",{get:function(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"uint16View",{get:function(){return this._uint16View||(this._uint16View=new Uint16Array(this.rawBinaryData)),this._uint16View},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"int32View",{get:function(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View},enumerable:!1,configurable:!0}),e.prototype.view=function(e){return this[e+"View"]},e.prototype.destroy=function(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this._uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null},e.sizeOf=function(e){switch(e){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(e+" isn't a valid view type")}},e}(),yt=function(i){function n(t){var r=i.call(this,t)||this;return r.shaderGenerator=null,r.geometryClass=null,r.vertexSize=null,r.state=Fe.for2d(),r.size=4*e.settings.SPRITE_BATCH_SIZE,r._vertexCount=0,r._indexCount=0,r._bufferedElements=[],r._bufferedTextures=[],r._bufferSize=0,r._shader=null,r._packedGeometries=[],r._packedGeometryPoolSize=2,r._flushId=0,r._aBuffers={},r._iBuffers={},r.MAX_TEXTURES=1,r.renderer.on("prerender",r.onPrerender,r),t.runners.contextChange.add(r),r._dcIndex=0,r._aIndex=0,r._iIndex=0,r._attributeBuffer=null,r._indexBuffer=null,r._tempBoundTextures=[],r}return l(n,i),n.prototype.contextChange=function(){var r=this.renderer.gl;e.settings.PREFER_ENV===t.ENV.WEBGL_LEGACY?this.MAX_TEXTURES=1:(this.MAX_TEXTURES=Math.min(r.getParameter(r.MAX_TEXTURE_IMAGE_UNITS),e.settings.SPRITE_MAX_TEXTURES),this.MAX_TEXTURES=Me(this.MAX_TEXTURES,r)),this._shader=this.shaderGenerator.generateShader(this.MAX_TEXTURES);for(var i=0;i<this._packedGeometryPoolSize;i++)this._packedGeometries[i]=new this.geometryClass;this.initFlushBuffers()},n.prototype.initFlushBuffers=function(){for(var e=n._drawCallPool,t=n._textureArrayPool,r=this.size/4,i=Math.floor(r/this.MAX_TEXTURES)+1;e.length<r;)e.push(new mt);for(;t.length<i;)t.push(new gt);for(var o=0;o<this.MAX_TEXTURES;o++)this._tempBoundTextures[o]=null},n.prototype.onPrerender=function(){this._flushId=0},n.prototype.render=function(e){e._texture.valid&&(this._vertexCount+e.vertexData.length/2>this.size&&this.flush(),this._vertexCount+=e.vertexData.length/2,this._indexCount+=e.indices.length,this._bufferedTextures[this._bufferSize]=e._texture.baseTexture,this._bufferedElements[this._bufferSize++]=e)},n.prototype.buildTexturesAndDrawCalls=function(){var e=this._bufferedTextures,t=this.MAX_TEXTURES,r=n._textureArrayPool,i=this.renderer.batch,o=this._tempBoundTextures,s=this.renderer.textureGC.count,a=++v._globalBatch,u=0,h=r[0],l=0;i.copyBoundTextures(o,t);for(var f=0;f<this._bufferSize;++f){var d=e[f];e[f]=null,d._batchEnabled!==a&&(h.count>=t&&(i.boundArray(h,o,a,t),this.buildDrawCalls(h,l,f),l=f,h=r[++u],++a),d._batchEnabled=a,d.touched=s,h.elements[h.count++]=d)}h.count>0&&(i.boundArray(h,o,a,t),this.buildDrawCalls(h,l,this._bufferSize),++u,++a);for(f=0;f<o.length;f++)o[f]=null;v._globalBatch=a},n.prototype.buildDrawCalls=function(e,t,i){var o=this,s=o._bufferedElements,a=o._attributeBuffer,u=o._indexBuffer,h=o.vertexSize,l=n._drawCallPool,f=this._dcIndex,d=this._aIndex,c=this._iIndex,p=l[f];p.start=this._iIndex,p.texArray=e;for(var v=t;v<i;++v){var m=s[v],g=m._texture.baseTexture,_=r.premultiplyBlendMode[g.alphaMode?1:0][m.blendMode];s[v]=null,t<v&&p.blend!==_&&(p.size=c-p.start,t=v,(p=l[++f]).texArray=e,p.start=c),this.packInterleavedGeometry(m,a,u,d,c),d+=m.vertexData.length/2*h,c+=m.indices.length,p.blend=_}t<i&&(p.size=c-p.start,++f),this._dcIndex=f,this._aIndex=d,this._iIndex=c},n.prototype.bindAndClearTexArray=function(e){for(var t=this.renderer.texture,r=0;r<e.count;r++)t.bind(e.elements[r],e.ids[r]),e.elements[r]=null;e.count=0},n.prototype.updateGeometry=function(){var t=this,r=t._packedGeometries,i=t._attributeBuffer,n=t._indexBuffer;e.settings.CAN_UPLOAD_SAME_BUFFER?(r[this._flushId]._buffer.update(i.rawBinaryData),r[this._flushId]._indexBuffer.update(n),this.renderer.geometry.updateBuffers()):(this._packedGeometryPoolSize<=this._flushId&&(this._packedGeometryPoolSize++,r[this._flushId]=new this.geometryClass),r[this._flushId]._buffer.update(i.rawBinaryData),r[this._flushId]._indexBuffer.update(n),this.renderer.geometry.bind(r[this._flushId]),this.renderer.geometry.updateBuffers(),this._flushId++)},n.prototype.drawBatches=function(){for(var e=this._dcIndex,t=this.renderer,r=t.gl,i=t.state,o=n._drawCallPool,s=null,a=0;a<e;a++){var u=o[a],h=u.texArray,l=u.type,f=u.size,d=u.start,c=u.blend;s!==h&&(s=h,this.bindAndClearTexArray(h)),this.state.blendMode=c,i.set(this.state),r.drawElements(l,f,r.UNSIGNED_SHORT,2*d)}},n.prototype.flush=function(){0!==this._vertexCount&&(this._attributeBuffer=this.getAttributeBuffer(this._vertexCount),this._indexBuffer=this.getIndexBuffer(this._indexCount),this._aIndex=0,this._iIndex=0,this._dcIndex=0,this.buildTexturesAndDrawCalls(),this.updateGeometry(),this.drawBatches(),this._bufferSize=0,this._vertexCount=0,this._indexCount=0)},n.prototype.start=function(){this.renderer.state.set(this.state),this.renderer.texture.ensureSamplerType(this.MAX_TEXTURES),this.renderer.shader.bind(this._shader),e.settings.CAN_UPLOAD_SAME_BUFFER&&this.renderer.geometry.bind(this._packedGeometries[this._flushId])},n.prototype.stop=function(){this.flush()},n.prototype.destroy=function(){for(var e=0;e<this._packedGeometryPoolSize;e++)this._packedGeometries[e]&&this._packedGeometries[e].destroy();this.renderer.off("prerender",this.onPrerender,this),this._aBuffers=null,this._iBuffers=null,this._packedGeometries=null,this._attributeBuffer=null,this._indexBuffer=null,this._shader&&(this._shader.destroy(),this._shader=null),i.prototype.destroy.call(this)},n.prototype.getAttributeBuffer=function(e){var t=r.nextPow2(Math.ceil(e/8)),i=r.log2(t),n=8*t;this._aBuffers.length<=i&&(this._iBuffers.length=i+1);var o=this._aBuffers[n];return o||(this._aBuffers[n]=o=new _t(n*this.vertexSize*4)),o},n.prototype.getIndexBuffer=function(e){var t=r.nextPow2(Math.ceil(e/12)),i=r.log2(t),n=12*t;this._iBuffers.length<=i&&(this._iBuffers.length=i+1);var o=this._iBuffers[i];return o||(this._iBuffers[i]=o=new Uint16Array(n)),o},n.prototype.packInterleavedGeometry=function(e,t,i,n,o){for(var s=t.uint32View,a=t.float32View,u=n/this.vertexSize,h=e.uvs,l=e.indices,f=e.vertexData,d=e._texture.baseTexture._batchLocation,c=Math.min(e.worldAlpha,1),p=c<1&&e._texture.baseTexture.alphaMode?r.premultiplyTint(e._tintRGB,c):e._tintRGB+(255*c<<24),v=0;v<f.length;v+=2)a[n++]=f[v],a[n++]=f[v+1],a[n++]=h[v],a[n++]=h[v+1],s[n++]=p,a[n++]=d;for(v=0;v<l.length;v++)i[o++]=u+l[v]},n._drawCallPool=[],n._textureArrayPool=[],n}(Z),Et=function(){function e(e,t){if(this.vertexSrc=e,this.fragTemplate=t,this.programCache={},this.defaultGroupCache={},t.indexOf("%count%")<0)throw new Error('Fragment template must contain "%count%".');if(t.indexOf("%forloop%")<0)throw new Error('Fragment template must contain "%forloop%".')}return e.prototype.generateShader=function(e){if(!this.programCache[e]){for(var t=new Int32Array(e),r=0;r<e;r++)t[r]=r;this.defaultGroupCache[e]=Y.from({uSamplers:t},!0);var i=this.fragTemplate;i=(i=i.replace(/%count%/gi,""+e)).replace(/%forloop%/gi,this.generateSampleSrc(e)),this.programCache[e]=new Ce(this.vertexSrc,i)}var n={tint:new Float32Array([1,1,1,1]),translationMatrix:new s.Matrix,default:this.defaultGroupCache[e]};return new Oe(this.programCache[e],n)},e.prototype.generateSampleSrc=function(e){var t="";t+="\n",t+="\n";for(var r=0;r<e;r++)r>0&&(t+="\nelse "),r<e-1&&(t+="if(vTextureId < "+r+".5)"),t+="\n{",t+="\n\tcolor = texture2D(uSamplers["+r+"], vTextureCoord);",t+="\n}";return t+="\n",t+="\n"},e}(),Tt=function(e){function r(r){void 0===r&&(r=!1);var i=e.call(this)||this;return i._buffer=new L(null,r,!1),i._indexBuffer=new L(null,r,!0),i.addAttribute("aVertexPosition",i._buffer,2,!1,t.TYPES.FLOAT).addAttribute("aTextureCoord",i._buffer,2,!1,t.TYPES.FLOAT).addAttribute("aColor",i._buffer,4,!0,t.TYPES.UNSIGNED_BYTE).addAttribute("aTextureId",i._buffer,1,!0,t.TYPES.FLOAT).addIndex(i._indexBuffer),i}return l(r,e),r}(H),xt="precision highp float;\nattribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\nattribute vec4 aColor;\nattribute float aTextureId;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform vec4 tint;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\nvarying float vTextureId;\n\nvoid main(void){\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = aTextureCoord;\n    vTextureId = aTextureId;\n    vColor = aColor * tint;\n}\n",bt="varying vec2 vTextureCoord;\nvarying vec4 vColor;\nvarying float vTextureId;\nuniform sampler2D uSamplers[%count%];\n\nvoid main(void){\n    vec4 color;\n    %forloop%\n    gl_FragColor = color * vColor;\n}\n",St=function(){function e(){}return e.create=function(e){var t=Object.assign({vertex:xt,fragment:bt,geometryClass:Tt,vertexSize:6},e),r=t.vertex,i=t.fragment,n=t.vertexSize,o=t.geometryClass;return function(e){function t(t){var s=e.call(this,t)||this;return s.shaderGenerator=new Et(r,i),s.geometryClass=o,s.vertexSize=n,s}return l(t,e),t}(yt)},Object.defineProperty(e,"defaultVertexSrc",{get:function(){return xt},enumerable:!1,configurable:!0}),Object.defineProperty(e,"defaultFragmentTemplate",{get:function(){return bt},enumerable:!1,configurable:!0}),e}(),Rt=St.create();Object.assign(Rt,{extension:{name:"batch",type:i.ExtensionType.RendererPlugin}});var At={},Mt=function(e){Object.defineProperty(At,e,{get:function(){return R[e]}})};for(var wt in R)Mt(wt);var It={},Ct=function(e){Object.defineProperty(It,e,{get:function(){return ht[e]}})};for(var wt in ht)Ct(wt);exports.AbstractBatchRenderer=yt,exports.AbstractMultiResource=m,exports.AbstractRenderer=ft,exports.ArrayResource=g,exports.Attribute=B,exports.BaseImageResource=_,exports.BaseRenderTexture=w,exports.BaseTexture=v,exports.BatchDrawCall=mt,exports.BatchGeometry=Tt,exports.BatchPluginFactory=St,exports.BatchRenderer=Rt,exports.BatchShaderGenerator=Et,exports.BatchSystem=$,exports.BatchTextureArray=gt,exports.Buffer=L,exports.BufferResource=c,exports.CanvasResource=y,exports.ContextSystem=ee,exports.CubeResource=E,exports.Filter=Pe,exports.FilterState=W,exports.FilterSystem=q,exports.Framebuffer=M,exports.FramebufferSystem=ie,exports.GLFramebuffer=te,exports.GLProgram=et,exports.GLTexture=at,exports.Geometry=H,exports.GeometrySystem=oe,exports.IGLUniformData=Je,exports.INSTALLED=a,exports.ImageBitmapResource=S,exports.ImageResource=T,exports.MaskData=se,exports.MaskSystem=Le,exports.ObjectRenderer=Z,exports.Program=Ce,exports.ProjectionSystem=je,exports.Quad=j,exports.QuadUv=z,exports.RenderTexture=P,exports.RenderTexturePool=N,exports.RenderTextureSystem=Ye,exports.Renderer=pt,exports.Resource=d,exports.SVGResource=x,exports.ScissorSystem=Ve,exports.Shader=Oe,exports.ShaderSystem=nt,exports.SpriteMaskFilter=De,exports.State=Fe,exports.StateSystem=ot,exports.StencilSystem=He,exports.System=vt,exports.Texture=F,exports.TextureGCSystem=st,exports.TextureMatrix=Be,exports.TextureSystem=ut,exports.TextureUvs=I,exports.UniformGroup=Y,exports.VERSION="6.5.10",exports.VideoResource=b,exports.ViewableBuffer=_t,exports.autoDetectRenderer=function(e){return pt.create(e)},exports.autoDetectResource=u,exports.checkMaxIfStatementsInShader=Me,exports.createUBOElements=qe,exports.defaultFilterVertex="attribute vec2 aVertexPosition;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\n\nuniform vec4 inputSize;\nuniform vec4 outputFrame;\n\nvec4 filterVertexPosition( void )\n{\n    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;\n\n    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);\n}\n\nvec2 filterTextureCoord( void )\n{\n    return aVertexPosition * (outputFrame.zw * inputSize.zw);\n}\n\nvoid main(void)\n{\n    gl_Position = filterVertexPosition();\n    vTextureCoord = filterTextureCoord();\n}\n",exports.defaultVertex="attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n    vTextureCoord = aTextureCoord;\n}",exports.generateProgram=tt,exports.generateUniformBufferSync=$e,exports.getTestContext=pe,exports.getUBOData=Ze,exports.resources=At,exports.systems=It,exports.uniformParsers=Te,Object.keys(i).forEach((function(e){"default"===e||exports.hasOwnProperty(e)||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return i[e]}})}));
//# sourceMappingURL=core.min.js.map
