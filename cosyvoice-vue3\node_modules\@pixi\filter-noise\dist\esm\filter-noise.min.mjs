/*!
 * @pixi/filter-noise - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/filter-noise is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{defaultFilterVertex as o,Filter as n}from"@pixi/core";var r=function(o,n){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,n){o.__proto__=n}||function(o,n){for(var r in n)n.hasOwnProperty(r)&&(o[r]=n[r])},r(o,n)};var e=function(n){function e(r,e){void 0===r&&(r=.5),void 0===e&&(e=Math.random());var t=n.call(this,o,"precision highp float;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nuniform float uNoise;\nuniform float uSeed;\nuniform sampler2D uSampler;\n\nfloat rand(vec2 co)\n{\n    return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);\n}\n\nvoid main()\n{\n    vec4 color = texture2D(uSampler, vTextureCoord);\n    float randomValue = rand(gl_FragCoord.xy * uSeed);\n    float diff = (randomValue - 0.5) * uNoise;\n\n    // Un-premultiply alpha before applying the color matrix. See issue #3539.\n    if (color.a > 0.0) {\n        color.rgb /= color.a;\n    }\n\n    color.r += diff;\n    color.g += diff;\n    color.b += diff;\n\n    // Premultiply alpha again.\n    color.rgb *= color.a;\n\n    gl_FragColor = color;\n}\n",{uNoise:0,uSeed:0})||this;return t.noise=r,t.seed=e,t}return function(o,n){function e(){this.constructor=o}r(o,n),o.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}(e,n),Object.defineProperty(e.prototype,"noise",{get:function(){return this.uniforms.uNoise},set:function(o){this.uniforms.uNoise=o},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"seed",{get:function(){return this.uniforms.uSeed},set:function(o){this.uniforms.uSeed=o},enumerable:!1,configurable:!0}),e}(n);export{e as NoiseFilter};
//# sourceMappingURL=filter-noise.min.mjs.map
