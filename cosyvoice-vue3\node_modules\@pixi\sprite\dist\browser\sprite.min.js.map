{"version": 3, "file": "sprite.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/Sprite.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { BLEND_MODES } from '@pixi/constants';\nimport { Texture } from '@pixi/core';\nimport { Bounds, Container } from '@pixi/display';\nimport { ObservablePoint, Point, Rectangle } from '@pixi/math';\nimport { settings } from '@pixi/settings';\nimport { sign } from '@pixi/utils';\n\nimport type { IBaseTextureOptions, Renderer, TextureSource } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { IPointData } from '@pixi/math';\n\nconst tempPoint = new Point();\nconst indices = new Uint16Array([0, 1, 2, 0, 2, 3]);\n\nexport type SpriteSource = TextureSource | Texture;\n\nexport interface Sprite extends GlobalMixins.Sprite, Container {}\n\n/**\n * The Sprite object is the base for all textured objects that are rendered to the screen\n *\n * A sprite can be created directly from an image like this:\n *\n * ```js\n * let sprite = PIXI.Sprite.from('assets/image.png');\n * ```\n *\n * The more efficient way to create sprites is using a {@link PIXI.Spritesheet},\n * as swapping base textures when rendering to the screen is inefficient.\n *\n * ```js\n * PIXI.Loader.shared.add(\"assets/spritesheet.json\").load(setup);\n *\n * function setup() {\n *   let sheet = PIXI.Loader.shared.resources[\"assets/spritesheet.json\"].spritesheet;\n *   let sprite = new PIXI.Sprite(sheet.textures[\"image.png\"]);\n *   ...\n * }\n * ```\n * @memberof PIXI\n */\nexport class Sprite extends Container\n{\n    /**\n     * The blend mode to be applied to the sprite. Apply a value of `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public blendMode: BLEND_MODES;\n    public indices: Uint16Array;\n\n    /**\n     * Plugin that is responsible for rendering this element.\n     * Allows to customize the rendering process without overriding '_render' & '_renderCanvas' methods.\n     * @default 'batch'\n     */\n    public pluginName: string;\n\n    /**\n     * The width of the sprite (this is initially set by the texture).\n     * @protected\n     */\n    _width: number;\n\n    /**\n     * The height of the sprite (this is initially set by the texture)\n     * @protected\n     */\n    _height: number;\n\n    /**\n     * The texture that the sprite is using.\n     * @private\n     */\n    _texture: Texture;\n    _textureID: number;\n\n    /**\n     * Cached tint value so we can tell when the tint is changed.\n     * Value is used for 2d CanvasRenderer.\n     * @protected\n     * @default 0xFFFFFF\n     */\n    _cachedTint: number;\n    protected _textureTrimmedID: number;\n\n    /**\n     * This is used to store the uvs data of the sprite, assigned at the same time\n     * as the vertexData in calculateVertices().\n     * @member {Float32Array}\n     */\n    protected uvs: Float32Array;\n\n    /**\n     * The anchor point defines the normalized coordinates\n     * in the texture that map to the position of this\n     * sprite.\n     *\n     * By default, this is `(0,0)` (or `texture.defaultAnchor`\n     * if you have modified that), which means the position\n     * `(x,y)` of this `Sprite` will be the top-left corner.\n     *\n     * Note: Updating `texture.defaultAnchor` after\n     * constructing a `Sprite` does _not_ update its anchor.\n     *\n     * {@link https://docs.cocos2d-x.org/cocos2d-x/en/sprites/manipulation.html}\n     * @default `this.texture.defaultAnchor`\n     */\n    protected _anchor: ObservablePoint;\n\n    /**\n     * This is used to store the vertex data of the sprite (basically a quad).\n     * @member {Float32Array}\n     */\n    protected vertexData: Float32Array;\n\n    /**\n     * This is used to calculate the bounds of the object IF it is a trimmed sprite.\n     * @member {Float32Array}\n     */\n    private vertexTrimmedData: Float32Array;\n\n    /**\n     * Internal roundPixels field\n     * @private\n     */\n    private _roundPixels: boolean;\n    private _transformID: number;\n    private _transformTrimmedID: number;\n\n    /**\n     * The tint applied to the sprite. This is a hex value. A value of 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    private _tint: number;\n\n    // Internal-only properties\n    /**\n     * The tint applied to the sprite. This is a RGB value. A value of 0xFFFFFF will remove any tint effect.\n     * @private\n     * @default 16777215\n     */\n    _tintRGB: number;\n\n    /** @param texture - The texture for this sprite. */\n    constructor(texture?: Texture)\n    {\n        super();\n\n        this._anchor = new ObservablePoint(\n            this._onAnchorUpdate,\n            this,\n            (texture ? texture.defaultAnchor.x : 0),\n            (texture ? texture.defaultAnchor.y : 0)\n        );\n\n        this._texture = null;\n\n        this._width = 0;\n        this._height = 0;\n        this._tint = null;\n        this._tintRGB = null;\n\n        this.tint = 0xFFFFFF;\n        this.blendMode = BLEND_MODES.NORMAL;\n        this._cachedTint = 0xFFFFFF;\n        this.uvs = null;\n\n        // call texture setter\n        this.texture = texture || Texture.EMPTY;\n        this.vertexData = new Float32Array(8);\n        this.vertexTrimmedData = null;\n\n        this._transformID = -1;\n        this._textureID = -1;\n\n        this._transformTrimmedID = -1;\n        this._textureTrimmedID = -1;\n\n        // Batchable stuff..\n        // TODO could make this a mixin?\n        this.indices = indices;\n\n        this.pluginName = 'batch';\n\n        /**\n         * Used to fast check if a sprite is.. a sprite!\n         * @member {boolean}\n         */\n        this.isSprite = true;\n        this._roundPixels = settings.ROUND_PIXELS;\n    }\n\n    /** When the texture is updated, this event will fire to update the scale and frame. */\n    protected _onTextureUpdate(): void\n    {\n        this._textureID = -1;\n        this._textureTrimmedID = -1;\n        this._cachedTint = 0xFFFFFF;\n\n        // so if _width is 0 then width was not set..\n        if (this._width)\n        {\n            this.scale.x = sign(this.scale.x) * this._width / this._texture.orig.width;\n        }\n\n        if (this._height)\n        {\n            this.scale.y = sign(this.scale.y) * this._height / this._texture.orig.height;\n        }\n    }\n\n    /** Called when the anchor position updates. */\n    private _onAnchorUpdate(): void\n    {\n        this._transformID = -1;\n        this._transformTrimmedID = -1;\n    }\n\n    /** Calculates worldTransform * vertices, store it in vertexData. */\n    public calculateVertices(): void\n    {\n        const texture = this._texture;\n\n        if (this._transformID === this.transform._worldID && this._textureID === texture._updateID)\n        {\n            return;\n        }\n\n        // update texture UV here, because base texture can be changed without calling `_onTextureUpdate`\n        if (this._textureID !== texture._updateID)\n        {\n            this.uvs = this._texture._uvs.uvsFloat32;\n        }\n\n        this._transformID = this.transform._worldID;\n        this._textureID = texture._updateID;\n\n        // set the vertex data\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n        const vertexData = this.vertexData;\n        const trim = texture.trim;\n        const orig = texture.orig;\n        const anchor = this._anchor;\n\n        let w0 = 0;\n        let w1 = 0;\n        let h0 = 0;\n        let h1 = 0;\n\n        if (trim)\n        {\n            // if the sprite is trimmed and is not a tilingsprite then we need to add the extra\n            // space before transforming the sprite coords.\n            w1 = trim.x - (anchor._x * orig.width);\n            w0 = w1 + trim.width;\n\n            h1 = trim.y - (anchor._y * orig.height);\n            h0 = h1 + trim.height;\n        }\n        else\n        {\n            w1 = -anchor._x * orig.width;\n            w0 = w1 + orig.width;\n\n            h1 = -anchor._y * orig.height;\n            h0 = h1 + orig.height;\n        }\n\n        // xy\n        vertexData[0] = (a * w1) + (c * h1) + tx;\n        vertexData[1] = (d * h1) + (b * w1) + ty;\n\n        // xy\n        vertexData[2] = (a * w0) + (c * h1) + tx;\n        vertexData[3] = (d * h1) + (b * w0) + ty;\n\n        // xy\n        vertexData[4] = (a * w0) + (c * h0) + tx;\n        vertexData[5] = (d * h0) + (b * w0) + ty;\n\n        // xy\n        vertexData[6] = (a * w1) + (c * h0) + tx;\n        vertexData[7] = (d * h0) + (b * w1) + ty;\n\n        if (this._roundPixels)\n        {\n            const resolution = settings.RESOLUTION;\n\n            for (let i = 0; i < vertexData.length; ++i)\n            {\n                vertexData[i] = Math.round((vertexData[i] * resolution | 0) / resolution);\n            }\n        }\n    }\n\n    /**\n     * Calculates worldTransform * vertices for a non texture with a trim. store it in vertexTrimmedData.\n     *\n     * This is used to ensure that the true width and height of a trimmed texture is respected.\n     */\n    public calculateTrimmedVertices(): void\n    {\n        if (!this.vertexTrimmedData)\n        {\n            this.vertexTrimmedData = new Float32Array(8);\n        }\n        else if (this._transformTrimmedID === this.transform._worldID && this._textureTrimmedID === this._texture._updateID)\n        {\n            return;\n        }\n\n        this._transformTrimmedID = this.transform._worldID;\n        this._textureTrimmedID = this._texture._updateID;\n\n        // lets do some special trim code!\n        const texture = this._texture;\n        const vertexData = this.vertexTrimmedData;\n        const orig = texture.orig;\n        const anchor = this._anchor;\n\n        // lets calculate the new untrimmed bounds..\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const w1 = -anchor._x * orig.width;\n        const w0 = w1 + orig.width;\n\n        const h1 = -anchor._y * orig.height;\n        const h0 = h1 + orig.height;\n\n        // xy\n        vertexData[0] = (a * w1) + (c * h1) + tx;\n        vertexData[1] = (d * h1) + (b * w1) + ty;\n\n        // xy\n        vertexData[2] = (a * w0) + (c * h1) + tx;\n        vertexData[3] = (d * h1) + (b * w0) + ty;\n\n        // xy\n        vertexData[4] = (a * w0) + (c * h0) + tx;\n        vertexData[5] = (d * h0) + (b * w0) + ty;\n\n        // xy\n        vertexData[6] = (a * w1) + (c * h0) + tx;\n        vertexData[7] = (d * h0) + (b * w1) + ty;\n    }\n\n    /**\n     *\n     * Renders the object using the WebGL renderer\n     * @param renderer - The webgl renderer to use.\n     */\n    protected _render(renderer: Renderer): void\n    {\n        this.calculateVertices();\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n        renderer.plugins[this.pluginName].render(this);\n    }\n\n    /** Updates the bounds of the sprite. */\n    protected _calculateBounds(): void\n    {\n        const trim = this._texture.trim;\n        const orig = this._texture.orig;\n\n        // First lets check to see if the current texture has a trim..\n        if (!trim || (trim.width === orig.width && trim.height === orig.height))\n        {\n            // no trim! lets use the usual calculations..\n            this.calculateVertices();\n            this._bounds.addQuad(this.vertexData);\n        }\n        else\n        {\n            // lets calculate a special trimmed bounds...\n            this.calculateTrimmedVertices();\n            this._bounds.addQuad(this.vertexTrimmedData);\n        }\n    }\n\n    /**\n     * Gets the local bounds of the sprite object.\n     * @param rect - Optional output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        // we can do a fast local bounds if the sprite has no children!\n        if (this.children.length === 0)\n        {\n            if (!this._localBounds)\n            {\n                this._localBounds = new Bounds();\n            }\n\n            this._localBounds.minX = this._texture.orig.width * -this._anchor._x;\n            this._localBounds.minY = this._texture.orig.height * -this._anchor._y;\n            this._localBounds.maxX = this._texture.orig.width * (1 - this._anchor._x);\n            this._localBounds.maxY = this._texture.orig.height * (1 - this._anchor._y);\n\n            if (!rect)\n            {\n                if (!this._localBoundsRect)\n                {\n                    this._localBoundsRect = new Rectangle();\n                }\n\n                rect = this._localBoundsRect;\n            }\n\n            return this._localBounds.getRectangle(rect);\n        }\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /**\n     * Tests if a point is inside this sprite\n     * @param point - the point to test\n     * @returns The result of the test\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const width = this._texture.orig.width;\n        const height = this._texture.orig.height;\n        const x1 = -width * this.anchor.x;\n        let y1 = 0;\n\n        if (tempPoint.x >= x1 && tempPoint.x < x1 + width)\n        {\n            y1 = -height * this.anchor.y;\n\n            if (tempPoint.y >= y1 && tempPoint.y < y1 + height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Destroys this sprite and optionally its texture and children.\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param [options.children=false] - if set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param [options.texture=false] - Should it destroy the current texture of the sprite as well\n     * @param [options.baseTexture=false] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this._texture.off('update', this._onTextureUpdate, this);\n\n        this._anchor = null;\n\n        const destroyTexture = typeof options === 'boolean' ? options : options && options.texture;\n\n        if (destroyTexture)\n        {\n            const destroyBaseTexture = typeof options === 'boolean' ? options : options && options.baseTexture;\n\n            this._texture.destroy(!!destroyBaseTexture);\n        }\n\n        this._texture = null;\n    }\n\n    // some helper functions..\n\n    /**\n     * Helper function that creates a new sprite based on the source you provide.\n     * The source can be - frame id, image url, video url, canvas element, video element, base texture\n     * @param {string|PIXI.Texture|HTMLCanvasElement|HTMLVideoElement} source - Source to create texture from\n     * @param {object} [options] - See {@link PIXI.BaseTexture}'s constructor for options.\n     * @returns The newly created sprite\n     */\n    static from(source: SpriteSource, options?: IBaseTextureOptions): Sprite\n    {\n        const texture = (source instanceof Texture)\n            ? source\n            : Texture.from(source, options);\n\n        return new Sprite(texture);\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     *\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     *\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}.\n     * @default false\n     */\n    set roundPixels(value: boolean)\n    {\n        if (this._roundPixels !== value)\n        {\n            this._transformID = -1;\n        }\n        this._roundPixels = value;\n    }\n\n    get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    /** The width of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return Math.abs(this.scale.x) * this._texture.orig.width;\n    }\n\n    set width(value: number)\n    {\n        const s = sign(this.scale.x) || 1;\n\n        this.scale.x = s * value / this._texture.orig.width;\n        this._width = value;\n    }\n\n    /** The height of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return Math.abs(this.scale.y) * this._texture.orig.height;\n    }\n\n    set height(value: number)\n    {\n        const s = sign(this.scale.y) || 1;\n\n        this.scale.y = s * value / this._texture.orig.height;\n        this._height = value;\n    }\n\n    /**\n     * The anchor sets the origin point of the sprite. The default value is taken from the {@link PIXI.Texture|Texture}\n     * and passed to the constructor.\n     *\n     * The default is `(0,0)`, this means the sprite's origin is the top left.\n     *\n     * Setting the anchor to `(0.5,0.5)` means the sprite's origin is centered.\n     *\n     * Setting the anchor to `(1,1)` would mean the sprite's origin point will be the bottom right corner.\n     *\n     * If you pass only single parameter, it will set both x and y to the same value as shown in the example below.\n     * @example\n     * const sprite = new PIXI.Sprite(texture);\n     * sprite.anchor.set(0.5); // This will set the origin to center. (0.5) is same as (0.5, 0.5).\n     */\n    get anchor(): ObservablePoint\n    {\n        return this._anchor;\n    }\n\n    set anchor(value: ObservablePoint)\n    {\n        this._anchor.copyFrom(value);\n    }\n\n    /**\n     * The tint applied to the sprite. This is a hex value.\n     *\n     * A value of 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    get tint(): number\n    {\n        return this._tint;\n    }\n\n    set tint(value: number)\n    {\n        this._tint = value;\n        this._tintRGB = (value >> 16) + (value & 0xff00) + ((value & 0xff) << 16);\n    }\n\n    /** The texture that the sprite is using. */\n    get texture(): Texture\n    {\n        return this._texture;\n    }\n\n    set texture(value: Texture)\n    {\n        if (this._texture === value)\n        {\n            return;\n        }\n\n        if (this._texture)\n        {\n            this._texture.off('update', this._onTextureUpdate, this);\n        }\n\n        this._texture = value || Texture.EMPTY;\n        this._cachedTint = 0xFFFFFF;\n\n        this._textureID = -1;\n        this._textureTrimmedID = -1;\n\n        if (value)\n        {\n            // wait for the texture to load\n            if (value.baseTexture.valid)\n            {\n                this._onTextureUpdate();\n            }\n            else\n            {\n                value.once('update', this._onTextureUpdate, this);\n            }\n        }\n    }\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "tempPoint", "Point", "indices", "Uint16Array", "Sprite", "_super", "texture", "_this", "this", "_anchor", "ObservablePoint", "_onAnchorUpdate", "defaultAnchor", "x", "y", "_texture", "_width", "_height", "_tint", "_tintRGB", "tint", "blendMode", "BLEND_MODES", "NORMAL", "_cachedTint", "uvs", "Texture", "EMPTY", "vertexData", "Float32Array", "vertexTrimmedData", "_transformID", "_textureID", "_transformTrimmedID", "_textureTrimmedID", "pluginName", "isSprite", "_roundPixels", "settings", "ROUND_PIXELS", "__", "constructor", "prototype", "create", "__extends", "_onTextureUpdate", "scale", "sign", "orig", "width", "height", "calculateVertices", "transform", "_worldID", "_updateID", "_uvs", "uvsFloat32", "wt", "worldTransform", "a", "c", "tx", "ty", "trim", "anchor", "w0", "w1", "h0", "h1", "_x", "_y", "resolution", "RESOLUTION", "i", "length", "Math", "round", "calculateTrimmedVertices", "_render", "renderer", "batch", "setObjectR<PERSON><PERSON>", "plugins", "render", "_calculateBounds", "_bounds", "addQuad", "getLocalBounds", "rect", "children", "_localBounds", "Bounds", "minX", "minY", "maxX", "maxY", "_localBoundsRect", "Rectangle", "getRectangle", "call", "containsPoint", "point", "applyInverse", "x1", "y1", "destroy", "options", "off", "destroyBaseTexture", "baseTexture", "from", "source", "defineProperty", "get", "set", "value", "abs", "s", "copyFrom", "valid", "once", "Container"], "mappings": ";;;;;;;8EAgBA,IAAIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,ICT5B,IAAMO,EAAY,IAAIC,EAAAA,MAChBC,EAAU,IAAIC,YAAY,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,IA6BhDC,EAAA,SAAAC,GAuGI,SAAAD,EAAYE,GAAZ,IAAAC,EAEIF,cA4CHG,YA1CGD,EAAKE,QAAU,IAAIC,EAAeA,gBAC9BH,EAAKI,gBACLJ,EACCD,EAAUA,EAAQM,cAAcC,EAAI,EACpCP,EAAUA,EAAQM,cAAcE,EAAI,GAGzCP,EAAKQ,SAAW,KAEhBR,EAAKS,OAAS,EACdT,EAAKU,QAAU,EACfV,EAAKW,MAAQ,KACbX,EAAKY,SAAW,KAEhBZ,EAAKa,KAAO,SACZb,EAAKc,UAAYC,EAAWA,YAACC,OAC7BhB,EAAKiB,YAAc,SACnBjB,EAAKkB,IAAM,KAGXlB,EAAKD,QAAUA,GAAWoB,EAAAA,QAAQC,MAClCpB,EAAKqB,WAAa,IAAIC,aAAa,GACnCtB,EAAKuB,kBAAoB,KAEzBvB,EAAKwB,cAAgB,EACrBxB,EAAKyB,YAAc,EAEnBzB,EAAK0B,qBAAuB,EAC5B1B,EAAK2B,mBAAqB,EAI1B3B,EAAKL,QAAUA,EAEfK,EAAK4B,WAAa,QAMlB5B,EAAK6B,UAAW,EAChB7B,EAAK8B,aAAeC,EAAQA,SAACC,eA4brC,ODlmBO,SAAmB/C,EAAGC,GAEzB,SAAS+C,IAAOhC,KAAKiC,YAAcjD,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEkD,UAAkB,OAANjD,EAAaC,OAAOiD,OAAOlD,IAAM+C,EAAGE,UAAYjD,EAAEiD,UAAW,IAAIF,GCevDI,CAASxC,EAAAC,GAwJvBD,EAAAsC,UAAAG,iBAAV,WAEIrC,KAAKwB,YAAc,EACnBxB,KAAK0B,mBAAqB,EAC1B1B,KAAKgB,YAAc,SAGfhB,KAAKQ,SAELR,KAAKsC,MAAMjC,EAAIkC,EAAAA,KAAKvC,KAAKsC,MAAMjC,GAAKL,KAAKQ,OAASR,KAAKO,SAASiC,KAAKC,OAGrEzC,KAAKS,UAELT,KAAKsC,MAAMhC,EAAIiC,EAAAA,KAAKvC,KAAKsC,MAAMhC,GAAKN,KAAKS,QAAUT,KAAKO,SAASiC,KAAKE,SAKtE9C,EAAAsC,UAAA/B,gBAAR,WAEIH,KAAKuB,cAAgB,EACrBvB,KAAKyB,qBAAuB,GAIzB7B,EAAAsC,UAAAS,kBAAP,WAEI,IAAM7C,EAAUE,KAAKO,SAErB,GAAIP,KAAKuB,eAAiBvB,KAAK4C,UAAUC,UAAY7C,KAAKwB,aAAe1B,EAAQgD,UAAjF,CAMI9C,KAAKwB,aAAe1B,EAAQgD,YAE5B9C,KAAKiB,IAAMjB,KAAKO,SAASwC,KAAKC,YAGlChD,KAAKuB,aAAevB,KAAK4C,UAAUC,SACnC7C,KAAKwB,WAAa1B,EAAQgD,UAI1B,IAAMG,EAAKjD,KAAK4C,UAAUM,eACpBC,EAAIF,EAAGE,EACPlE,EAAIgE,EAAGhE,EACPmE,EAAIH,EAAGG,EACPpE,EAAIiE,EAAGjE,EACPqE,EAAKJ,EAAGI,GACRC,EAAKL,EAAGK,GACRlC,EAAapB,KAAKoB,WAClBmC,EAAOzD,EAAQyD,KACff,EAAO1C,EAAQ0C,KACfgB,EAASxD,KAAKC,QAEhBwD,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAqCT,GAnCIL,GAKAE,GADAC,EAAKH,EAAKlD,EAAKmD,EAAOK,GAAKrB,EAAKC,OACtBc,EAAKd,MAGfkB,GADAC,EAAKL,EAAKjD,EAAKkD,EAAOM,GAAKtB,EAAKE,QACtBa,EAAKb,SAKfe,GADAC,GAAMF,EAAOK,GAAKrB,EAAKC,OACbD,EAAKC,MAGfkB,GADAC,GAAMJ,EAAOM,GAAKtB,EAAKE,QACbF,EAAKE,QAInBtB,EAAW,GAAM+B,EAAIO,EAAON,EAAIQ,EAAMP,EACtCjC,EAAW,GAAMpC,EAAI4E,EAAO3E,EAAIyE,EAAMJ,EAGtClC,EAAW,GAAM+B,EAAIM,EAAOL,EAAIQ,EAAMP,EACtCjC,EAAW,GAAMpC,EAAI4E,EAAO3E,EAAIwE,EAAMH,EAGtClC,EAAW,GAAM+B,EAAIM,EAAOL,EAAIO,EAAMN,EACtCjC,EAAW,GAAMpC,EAAI2E,EAAO1E,EAAIwE,EAAMH,EAGtClC,EAAW,GAAM+B,EAAIO,EAAON,EAAIO,EAAMN,EACtCjC,EAAW,GAAMpC,EAAI2E,EAAO1E,EAAIyE,EAAMJ,EAElCtD,KAAK6B,aAIL,IAFA,IAAMkC,EAAajC,EAAQA,SAACkC,WAEnBC,EAAI,EAAGA,EAAI7C,EAAW8C,SAAUD,EAErC7C,EAAW6C,GAAKE,KAAKC,OAAOhD,EAAW6C,GAAKF,EAAa,GAAKA,KAUnEnE,EAAAsC,UAAAmC,yBAAP,WAEI,GAAKrE,KAAKsB,mBAIL,GAAItB,KAAKyB,sBAAwBzB,KAAK4C,UAAUC,UAAY7C,KAAK0B,oBAAsB1B,KAAKO,SAASuC,UAEtG,YAJA9C,KAAKsB,kBAAoB,IAAID,aAAa,GAO9CrB,KAAKyB,oBAAsBzB,KAAK4C,UAAUC,SAC1C7C,KAAK0B,kBAAoB1B,KAAKO,SAASuC,UAGvC,IAAMhD,EAAUE,KAAKO,SACfa,EAAapB,KAAKsB,kBAClBkB,EAAO1C,EAAQ0C,KACfgB,EAASxD,KAAKC,QAGdgD,EAAKjD,KAAK4C,UAAUM,eACpBC,EAAIF,EAAGE,EACPlE,EAAIgE,EAAGhE,EACPmE,EAAIH,EAAGG,EACPpE,EAAIiE,EAAGjE,EACPqE,EAAKJ,EAAGI,GACRC,EAAKL,EAAGK,GAERI,GAAMF,EAAOK,GAAKrB,EAAKC,MACvBgB,EAAKC,EAAKlB,EAAKC,MAEfmB,GAAMJ,EAAOM,GAAKtB,EAAKE,OACvBiB,EAAKC,EAAKpB,EAAKE,OAGrBtB,EAAW,GAAM+B,EAAIO,EAAON,EAAIQ,EAAMP,EACtCjC,EAAW,GAAMpC,EAAI4E,EAAO3E,EAAIyE,EAAMJ,EAGtClC,EAAW,GAAM+B,EAAIM,EAAOL,EAAIQ,EAAMP,EACtCjC,EAAW,GAAMpC,EAAI4E,EAAO3E,EAAIwE,EAAMH,EAGtClC,EAAW,GAAM+B,EAAIM,EAAOL,EAAIO,EAAMN,EACtCjC,EAAW,GAAMpC,EAAI2E,EAAO1E,EAAIwE,EAAMH,EAGtClC,EAAW,GAAM+B,EAAIO,EAAON,EAAIO,EAAMN,EACtCjC,EAAW,GAAMpC,EAAI2E,EAAO1E,EAAIyE,EAAMJ,GAQhC1D,EAAOsC,UAAAoC,QAAjB,SAAkBC,GAEdvE,KAAK2C,oBAEL4B,EAASC,MAAMC,kBAAkBF,EAASG,QAAQ1E,KAAK2B,aACvD4C,EAASG,QAAQ1E,KAAK2B,YAAYgD,OAAO3E,OAInCJ,EAAAsC,UAAA0C,iBAAV,WAEI,IAAMrB,EAAOvD,KAAKO,SAASgD,KACrBf,EAAOxC,KAAKO,SAASiC,MAGtBe,GAASA,EAAKd,QAAUD,EAAKC,OAASc,EAAKb,SAAWF,EAAKE,QAG5D1C,KAAK2C,oBACL3C,KAAK6E,QAAQC,QAAQ9E,KAAKoB,cAK1BpB,KAAKqE,2BACLrE,KAAK6E,QAAQC,QAAQ9E,KAAKsB,qBAS3B1B,EAAcsC,UAAA6C,eAArB,SAAsBC,GAGlB,OAA6B,IAAzBhF,KAAKiF,SAASf,QAETlE,KAAKkF,eAENlF,KAAKkF,aAAe,IAAIC,EAAAA,QAG5BnF,KAAKkF,aAAaE,KAAOpF,KAAKO,SAASiC,KAAKC,OAASzC,KAAKC,QAAQ4D,GAClE7D,KAAKkF,aAAaG,KAAOrF,KAAKO,SAASiC,KAAKE,QAAU1C,KAAKC,QAAQ6D,GACnE9D,KAAKkF,aAAaI,KAAOtF,KAAKO,SAASiC,KAAKC,OAAS,EAAIzC,KAAKC,QAAQ4D,IACtE7D,KAAKkF,aAAaK,KAAOvF,KAAKO,SAASiC,KAAKE,QAAU,EAAI1C,KAAKC,QAAQ6D,IAElEkB,IAEIhF,KAAKwF,mBAENxF,KAAKwF,iBAAmB,IAAIC,EAAAA,WAGhCT,EAAOhF,KAAKwF,kBAGTxF,KAAKkF,aAAaQ,aAAaV,IAGnCnF,EAAAqC,UAAM6C,eAAeY,KAAK3F,KAAMgF,IAQpCpF,EAAasC,UAAA0D,cAApB,SAAqBC,GAEjB7F,KAAKkD,eAAe4C,aAAaD,EAAOrG,GAExC,IAAMiD,EAAQzC,KAAKO,SAASiC,KAAKC,MAC3BC,EAAS1C,KAAKO,SAASiC,KAAKE,OAC5BqD,GAAMtD,EAAQzC,KAAKwD,OAAOnD,EAC5B2F,EAAK,EAET,OAAIxG,EAAUa,GAAK0F,GAAMvG,EAAUa,EAAI0F,EAAKtD,IAExCuD,GAAMtD,EAAS1C,KAAKwD,OAAOlD,EAEvBd,EAAUc,GAAK0F,GAAMxG,EAAUc,EAAI0F,EAAKtD,IAkB7C9C,EAAOsC,UAAA+D,QAAd,SAAeC,GAUX,GARArG,EAAAqC,UAAM+D,QAAON,KAAA3F,KAACkG,GAEdlG,KAAKO,SAAS4F,IAAI,SAAUnG,KAAKqC,iBAAkBrC,MAEnDA,KAAKC,QAAU,KAE2B,kBAAZiG,EAAwBA,EAAUA,GAAWA,EAAQpG,QAGnF,CACI,IAAMsG,EAAwC,kBAAZF,EAAwBA,EAAUA,GAAWA,EAAQG,YAEvFrG,KAAKO,SAAS0F,UAAUG,GAG5BpG,KAAKO,SAAW,MAYbX,EAAA0G,KAAP,SAAYC,EAAsBL,GAM9B,OAAO,IAAItG,EAJM2G,aAAkBrF,EAAOA,QACpCqF,EACArF,EAAAA,QAAQoF,KAAKC,EAAQL,KAc/BhH,OAAAsH,eAAI5G,EAAWsC,UAAA,cAAA,CASfuE,IAAA,WAEI,OAAOzG,KAAK6B,cAXhB6E,IAAA,SAAgBC,GAER3G,KAAK6B,eAAiB8E,IAEtB3G,KAAKuB,cAAgB,GAEzBvB,KAAK6B,aAAe8E,mCASxBzH,OAAAsH,eAAI5G,EAAKsC,UAAA,QAAA,CAATuE,IAAA,WAEI,OAAOtC,KAAKyC,IAAI5G,KAAKsC,MAAMjC,GAAKL,KAAKO,SAASiC,KAAKC,OAGvDiE,IAAA,SAAUC,GAEN,IAAME,EAAItE,EAAAA,KAAKvC,KAAKsC,MAAMjC,IAAM,EAEhCL,KAAKsC,MAAMjC,EAAIwG,EAAIF,EAAQ3G,KAAKO,SAASiC,KAAKC,MAC9CzC,KAAKQ,OAASmG,mCAIlBzH,OAAAsH,eAAI5G,EAAMsC,UAAA,SAAA,CAAVuE,IAAA,WAEI,OAAOtC,KAAKyC,IAAI5G,KAAKsC,MAAMhC,GAAKN,KAAKO,SAASiC,KAAKE,QAGvDgE,IAAA,SAAWC,GAEP,IAAME,EAAItE,EAAAA,KAAKvC,KAAKsC,MAAMhC,IAAM,EAEhCN,KAAKsC,MAAMhC,EAAIuG,EAAIF,EAAQ3G,KAAKO,SAASiC,KAAKE,OAC9C1C,KAAKS,QAAUkG,mCAkBnBzH,OAAAsH,eAAI5G,EAAMsC,UAAA,SAAA,CAAVuE,IAAA,WAEI,OAAOzG,KAAKC,SAGhByG,IAAA,SAAWC,GAEP3G,KAAKC,QAAQ6G,SAASH,oCAS1BzH,OAAAsH,eAAI5G,EAAIsC,UAAA,OAAA,CAARuE,IAAA,WAEI,OAAOzG,KAAKU,OAGhBgG,IAAA,SAASC,GAEL3G,KAAKU,MAAQiG,EACb3G,KAAKW,UAAYgG,GAAS,KAAe,MAARA,KAA4B,IAARA,IAAiB,qCAI1EzH,OAAAsH,eAAI5G,EAAOsC,UAAA,UAAA,CAAXuE,IAAA,WAEI,OAAOzG,KAAKO,UAGhBmG,IAAA,SAAYC,GAEJ3G,KAAKO,WAAaoG,IAKlB3G,KAAKO,UAELP,KAAKO,SAAS4F,IAAI,SAAUnG,KAAKqC,iBAAkBrC,MAGvDA,KAAKO,SAAWoG,GAASzF,EAAAA,QAAQC,MACjCnB,KAAKgB,YAAc,SAEnBhB,KAAKwB,YAAc,EACnBxB,KAAK0B,mBAAqB,EAEtBiF,IAGIA,EAAMN,YAAYU,MAElB/G,KAAKqC,mBAILsE,EAAMK,KAAK,SAAUhH,KAAKqC,iBAAkBrC,yCAI3DJ,EAhlBD,CAA4BqH"}