{"version": 3, "file": "mixin-get-global-position.min.mjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import { DisplayObject } from '@pixi/display';\nimport { Point } from '@pixi/math';\n\n/**\n * Returns the global position of the displayObject. Does not depend on object scale, rotation and pivot.\n * @method getGlobalPosition\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.Point} [point=new PIXI.Point()] - The point to write the global value to.\n * @param {boolean} [skipUpdate=false] - Setting to true will stop the transforms of the scene graph from\n *  being updated. This means the calculation returned MAY be out of date BUT will give you a\n *  nice performance boost.\n * @returns {PIXI.Point} The updated point.\n */\nDisplayObject.prototype.getGlobalPosition = function getGlobalPosition(point: Point = new Point(), skipUpdate = false): Point\n{\n    if (this.parent)\n    {\n        this.parent.toGlobal(this.position, point, skipUpdate);\n    }\n    else\n    {\n        point.x = this.position.x;\n        point.y = this.position.y;\n    }\n\n    return point;\n};\n"], "names": ["DisplayObject", "prototype", "getGlobalPosition", "point", "skipUpdate", "Point", "this", "parent", "toGlobal", "position", "x", "y"], "mappings": ";;;;;;;iFAaAA,EAAcC,UAAUC,kBAAoB,SAA2BC,EAA4BC,GAY/F,YAZmE,IAAAD,IAAAA,EAAmB,IAAAE,QAAS,IAAAD,IAAAA,GAAkB,GAE7GE,KAAKC,OAELD,KAAKC,OAAOC,SAASF,KAAKG,SAAUN,EAAOC,IAI3CD,EAAMO,EAAIJ,KAAKG,SAASC,EACxBP,EAAMQ,EAAIL,KAAKG,SAASE,GAGrBR"}