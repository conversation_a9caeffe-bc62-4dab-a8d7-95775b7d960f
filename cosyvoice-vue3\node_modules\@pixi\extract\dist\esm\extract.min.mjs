/*!
 * @pixi/extract - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/extract is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{MSAA_QUALITY as e}from"@pixi/constants";import{CanvasRenderTarget as t}from"@pixi/utils";import{Rectangle as r}from"@pixi/math";import{ExtensionType as i,RenderTexture as n}from"@pixi/core";var a=new r,o=function(){function r(e){this.renderer=e}return r.prototype.image=function(e,t,r){var i=new Image;return i.src=this.base64(e,t,r),i},r.prototype.base64=function(e,t,r){return this.canvas(e).toDataURL(t,r)},r.prototype.canvas=function(e,i){var n=this._rawPixels(e,i),a=n.pixels,o=n.width,h=n.height,s=n.flipY,u=new t(o,h,1),f=u.context.getImageData(0,0,o,h);if(r.arrayPostDivide(a,f.data),u.context.putImageData(f,0,0),s){var d=new t(u.width,u.height,1);d.context.scale(1,-1),d.context.drawImage(u.canvas,0,-h),u.destroy(),u=d}return u.canvas},r.prototype.pixels=function(e,t){var i=this._rawPixels(e,t).pixels;return r.arrayPostDivide(i,i),i},r.prototype._rawPixels=function(t,r){var i,o,h=this.renderer,s=!1,u=!1;if(t)if(t instanceof n)o=t;else{var f=h.context.webGLVersion>=2?h.multisample:e.NONE;if(o=this.renderer.generateTexture(t,{multisample:f}),f!==e.NONE){var d=n.create({width:o.width,height:o.height});h.framebuffer.bind(o.framebuffer),h.framebuffer.blit(d.framebuffer),h.framebuffer.bind(null),o.destroy(!0),o=d}u=!0}o?(i=o.baseTexture.resolution,r=null!=r?r:o.frame,s=!1,h.renderTexture.bind(o)):(i=h.resolution,r||((r=a).width=h.width,r.height=h.height),s=!0,h.renderTexture.bind(null));var l=Math.round(r.width*i),p=Math.round(r.height*i),m=new Uint8Array(4*l*p),c=h.gl;return c.readPixels(Math.round(r.x*i),Math.round(r.y*i),l,p,c.RGBA,c.UNSIGNED_BYTE,m),u&&o.destroy(!0),{pixels:m,width:l,height:p,flipY:s}},r.prototype.destroy=function(){this.renderer=null},r.arrayPostDivide=function(e,t){for(var r=0;r<e.length;r+=4){var i=t[r+3]=e[r+3];0!==i?(t[r]=Math.round(Math.min(255*e[r]/i,255)),t[r+1]=Math.round(Math.min(255*e[r+1]/i,255)),t[r+2]=Math.round(Math.min(255*e[r+2]/i,255))):(t[r]=e[r],t[r+1]=e[r+1],t[r+2]=e[r+2])}},r.extension={name:"extract",type:i.RendererPlugin},r}();export{o as Extract};
//# sourceMappingURL=extract.min.mjs.map
