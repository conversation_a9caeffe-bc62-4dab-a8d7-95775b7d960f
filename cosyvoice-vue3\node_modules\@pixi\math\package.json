{"name": "@pixi/math", "version": "6.5.10", "main": "dist/cjs/math.js", "module": "dist/esm/math.mjs", "bundle": "dist/browser/math.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/math.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/math.js"}}}, "description": "Collection of math utilities", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}