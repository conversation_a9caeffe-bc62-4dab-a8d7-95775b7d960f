/*!
 * @pixi/text-bitmap - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/text-bitmap is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/math"),t=require("@pixi/settings"),r=require("@pixi/mesh"),i=require("@pixi/utils"),n=require("@pixi/core"),a=require("@pixi/text"),o=require("@pixi/constants"),s=require("@pixi/display"),h=require("@pixi/loaders"),l=function(e,t){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},l(e,t)};var u=function(){this.info=[],this.common=[],this.page=[],this.char=[],this.kerning=[],this.distanceField=[]},f=function(){function e(){}return e.test=function(e){return"string"==typeof e&&0===e.indexOf("info face=")},e.parse=function(e){var t=e.match(/^[a-z]+\s+.+$/gm),r={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(var i in t){var n=t[i].match(/^[a-z]+/gm)[0],a=t[i].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),o={};for(var s in a){var h=a[s].split("="),l=h[0],f=h[1].replace(/"/gm,""),c=parseFloat(f),p=isNaN(c)?f:c;o[l]=p}r[n].push(o)}var d=new u;return r.info.forEach((function(e){return d.info.push({face:e.face,size:parseInt(e.size,10)})})),r.common.forEach((function(e){return d.common.push({lineHeight:parseInt(e.lineHeight,10)})})),r.page.forEach((function(e){return d.page.push({id:parseInt(e.id,10),file:e.file})})),r.char.forEach((function(e){return d.char.push({id:parseInt(e.id,10),page:parseInt(e.page,10),x:parseInt(e.x,10),y:parseInt(e.y,10),width:parseInt(e.width,10),height:parseInt(e.height,10),xoffset:parseInt(e.xoffset,10),yoffset:parseInt(e.yoffset,10),xadvance:parseInt(e.xadvance,10)})})),r.kerning.forEach((function(e){return d.kerning.push({first:parseInt(e.first,10),second:parseInt(e.second,10),amount:parseInt(e.amount,10)})})),r.distanceField.forEach((function(e){return d.distanceField.push({distanceRange:parseInt(e.distanceRange,10),fieldType:e.fieldType})})),d},e}(),c=function(){function e(){}return e.test=function(e){return e instanceof XMLDocument&&e.getElementsByTagName("page").length&&null!==e.getElementsByTagName("info")[0].getAttribute("face")},e.parse=function(e){for(var t=new u,r=e.getElementsByTagName("info"),i=e.getElementsByTagName("common"),n=e.getElementsByTagName("page"),a=e.getElementsByTagName("char"),o=e.getElementsByTagName("kerning"),s=e.getElementsByTagName("distanceField"),h=0;h<r.length;h++)t.info.push({face:r[h].getAttribute("face"),size:parseInt(r[h].getAttribute("size"),10)});for(h=0;h<i.length;h++)t.common.push({lineHeight:parseInt(i[h].getAttribute("lineHeight"),10)});for(h=0;h<n.length;h++)t.page.push({id:parseInt(n[h].getAttribute("id"),10)||0,file:n[h].getAttribute("file")});for(h=0;h<a.length;h++){var l=a[h];t.char.push({id:parseInt(l.getAttribute("id"),10),page:parseInt(l.getAttribute("page"),10)||0,x:parseInt(l.getAttribute("x"),10),y:parseInt(l.getAttribute("y"),10),width:parseInt(l.getAttribute("width"),10),height:parseInt(l.getAttribute("height"),10),xoffset:parseInt(l.getAttribute("xoffset"),10),yoffset:parseInt(l.getAttribute("yoffset"),10),xadvance:parseInt(l.getAttribute("xadvance"),10)})}for(h=0;h<o.length;h++)t.kerning.push({first:parseInt(o[h].getAttribute("first"),10),second:parseInt(o[h].getAttribute("second"),10),amount:parseInt(o[h].getAttribute("amount"),10)});for(h=0;h<s.length;h++)t.distanceField.push({fieldType:s[h].getAttribute("fieldType"),distanceRange:parseInt(s[h].getAttribute("distanceRange"),10)});return t},e}(),p=function(){function e(){}return e.test=function(e){if("string"==typeof e&&e.indexOf("<font>")>-1){var t=(new globalThis.DOMParser).parseFromString(e,"text/xml");return c.test(t)}return!1},e.parse=function(e){var t=(new globalThis.DOMParser).parseFromString(e,"text/xml");return c.parse(t)},e}(),d=[f,c,p];function g(e){for(var t=0;t<d.length;t++)if(d[t].test(e))return d[t];return null}function x(e,t,r,n,o,s,h){var l=r.text,u=r.fontProperties;t.translate(n,o),t.scale(s,s);var f=h.strokeThickness/2,c=-h.strokeThickness/2;if(t.font=h.toFontString(),t.lineWidth=h.strokeThickness,t.textBaseline=h.textBaseline,t.lineJoin=h.lineJoin,t.miterLimit=h.miterLimit,t.fillStyle=function(e,t,r,i,n,o){var s,h=r.fill;if(!Array.isArray(h))return h;if(1===h.length)return h[0];var l=r.dropShadow?r.dropShadowDistance:0,u=r.padding||0,f=e.width/i-l-2*u,c=e.height/i-l-2*u,p=h.slice(),d=r.fillGradientStops.slice();if(!d.length)for(var g=p.length+1,x=1;x<g;++x)d.push(x/g);if(p.unshift(h[0]),d.unshift(0),p.push(h[h.length-1]),d.push(1),r.fillGradientType===a.TEXT_GRADIENT.LINEAR_VERTICAL){s=t.createLinearGradient(f/2,u,f/2,c+u);var m=0,v=(o.fontProperties.fontSize+r.strokeThickness)/c;for(x=0;x<n.length;x++)for(var y=o.lineHeight*x,_=0;_<p.length;_++){var b=y/c+("number"==typeof d[_]?d[_]:_/p.length)*v,T=Math.max(m,b);T=Math.min(T,1),s.addColorStop(T,p[_]),m=T}}else{s=t.createLinearGradient(u,c/2,f+u,c/2);var w=p.length+1,M=1;for(x=0;x<p.length;x++){var A=void 0;A="number"==typeof d[x]?d[x]:M/w,s.addColorStop(A,p[x]),M++}}return s}(e,t,h,s,[l],r),t.strokeStyle=h.stroke,h.dropShadow){var p=h.dropShadowColor,d=i.hex2rgb("number"==typeof p?p:i.string2hex(p)),g=h.dropShadowBlur*s,x=h.dropShadowDistance*s;t.shadowColor="rgba("+255*d[0]+","+255*d[1]+","+255*d[2]+","+h.dropShadowAlpha+")",t.shadowBlur=g,t.shadowOffsetX=Math.cos(h.dropShadowAngle)*x,t.shadowOffsetY=Math.sin(h.dropShadowAngle)*x}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0;h.stroke&&h.strokeThickness&&t.strokeText(l,f,c+r.lineHeight-u.descent),h.fill&&t.fillText(l,f,c+r.lineHeight-u.descent),t.setTransform(1,0,0,1,0,0),t.fillStyle="rgba(0, 0, 0, 0)"}function m(e){return Array.from?Array.from(e):e.split("")}function v(e){return e.codePointAt?e.codePointAt(0):e.charCodeAt(0)}var y=function(){function r(t,r,a){var s,h,l=t.info[0],u=t.common[0],f=t.page[0],c=t.distanceField[0],p=i.getResolutionOfUrl(f.file),d={};this._ownsTextures=a,this.font=l.face,this.size=l.size,this.lineHeight=u.lineHeight/p,this.chars={},this.pageTextures=d;for(var g=0;g<t.page.length;g++){var x=t.page[g],m=x.id,v=x.file;d[m]=r instanceof Array?r[g]:r[v],(null==c?void 0:c.fieldType)&&"none"!==c.fieldType&&(d[m].baseTexture.alphaMode=o.ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,d[m].baseTexture.mipmap=o.MIPMAP_MODES.OFF)}for(g=0;g<t.char.length;g++){var y=t.char[g],_=(m=y.id,y.page),b=t.char[g],T=b.x,w=b.y,M=b.width,A=b.height,S=b.xoffset,P=b.yoffset,E=b.xadvance;T/=p,w/=p,M/=p,A/=p,S/=p,P/=p,E/=p;var F=new e.Rectangle(T+d[_].frame.x/p,w+d[_].frame.y/p,M,A);this.chars[m]={xOffset:S,yOffset:P,xAdvance:E,kerning:{},texture:new n.Texture(d[_].baseTexture,F),page:_}}for(g=0;g<t.kerning.length;g++){var C=t.kerning[g],O=C.first,I=C.second,D=C.amount;O/=p,I/=p,D/=p,this.chars[I]&&(this.chars[I].kerning[O]=D)}this.distanceFieldRange=null==c?void 0:c.distanceRange,this.distanceFieldType=null!==(h=null===(s=null==c?void 0:c.fieldType)||void 0===s?void 0:s.toLowerCase())&&void 0!==h?h:"none"}return r.prototype.destroy=function(){for(var e in this.chars)this.chars[e].texture.destroy(),this.chars[e].texture=null;for(var e in this.pageTextures)this._ownsTextures&&this.pageTextures[e].destroy(!0),this.pageTextures[e]=null;this.chars=null,this.pageTextures=null},r.install=function(e,t,i){var a;if(e instanceof u)a=e;else{var o=g(e);if(!o)throw new Error("Unrecognized data format for font.");a=o.parse(e)}t instanceof n.Texture&&(t=[t]);var s=new r(a,t,i);return r.available[s.font]=s,s},r.uninstall=function(e){var t=r.available[e];if(!t)throw new Error("No font found named '"+e+"'");t.destroy(),delete r.available[e]},r.from=function(e,i,o){if(!e)throw new Error("[BitmapFont] Property `name` is required.");var s=Object.assign({},r.defaultOptions,o),h=s.chars,l=s.padding,f=s.resolution,c=s.textureWidth,p=s.textureHeight,d=function(e){"string"==typeof e&&(e=[e]);for(var t=[],r=0,i=e.length;r<i;r++){var n=e[r];if(Array.isArray(n)){if(2!==n.length)throw new Error("[BitmapFont]: Invalid character range length, expecting 2 got "+n.length+".");var a=n[0].charCodeAt(0),o=n[1].charCodeAt(0);if(o<a)throw new Error("[BitmapFont]: Invalid character range.");for(var s=a,h=o;s<=h;s++)t.push(String.fromCharCode(s))}else t.push.apply(t,m(n))}if(0===t.length)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}(h),g=i instanceof a.TextStyle?i:new a.TextStyle(i),y=c,_=new u;_.info[0]={face:g.fontFamily,size:g.fontSize},_.common[0]={lineHeight:g.fontSize};for(var b,T,w,M=0,A=0,S=0,P=[],E=0;E<d.length;E++){b||((b=t.settings.ADAPTER.createCanvas()).width=c,b.height=p,T=b.getContext("2d"),w=new n.BaseTexture(b,{resolution:f}),P.push(new n.Texture(w)),_.page.push({id:P.length-1,file:""}));var F=d[E],C=a.TextMetrics.measureText(F,g,!1,b),O=C.width,I=Math.ceil(C.height),D=Math.ceil(("italic"===g.fontStyle?2:1)*O);if(A>=p-I*f){if(0===A)throw new Error("[BitmapFont] textureHeight "+p+"px is too small (fontFamily: '"+g.fontFamily+"', fontSize: "+g.fontSize+"px, char: '"+F+"')");--E,b=null,T=null,w=null,A=0,M=0,S=0}else if(S=Math.max(I+C.fontProperties.descent,S),D*f+M>=y){if(0===M)throw new Error("[BitmapFont] textureWidth "+c+"px is too small (fontFamily: '"+g.fontFamily+"', fontSize: "+g.fontSize+"px, char: '"+F+"')");--E,A+=S*f,A=Math.ceil(A),M=0,S=0}else{x(b,T,C,M,A,f,g);var N=v(C.text);_.char.push({id:N,page:P.length-1,x:M/f,y:A/f,width:D,height:I,xoffset:0,yoffset:0,xadvance:Math.ceil(O-(g.dropShadow?g.dropShadowDistance:0)-(g.stroke?g.strokeThickness:0))}),M+=(D+2*l)*f,M=Math.ceil(M)}}if(!(null==o?void 0:o.skipKerning)){E=0;for(var B=d.length;E<B;E++)for(var k=d[E],L=0;L<B;L++){var z=d[L],H=T.measureText(k).width,R=T.measureText(z).width,j=T.measureText(k+z).width-(H+R);j&&_.kerning.push({first:v(k),second:v(z),amount:j})}}var W=new r(_,P,!0);return void 0!==r.available[e]&&r.uninstall(e),r.available[e]=W,W},r.ALPHA=[["a","z"],["A","Z"]," "],r.NUMERIC=[["0","9"]],r.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],r.ASCII=[[" ","~"]],r.defaultOptions={resolution:1,textureWidth:512,textureHeight:512,padding:4,chars:r.ALPHANUMERIC},r.available={},r}(),_=[],b=[],T=[],w=function(a){function s(r,i){void 0===i&&(i={});var n=a.call(this)||this;n._tint=16777215;var o=Object.assign({},s.styleDefaults,i),h=o.align,l=o.tint,u=o.maxWidth,f=o.letterSpacing,c=o.fontName,p=o.fontSize;if(!y.available[c])throw new Error('Missing BitmapFont "'+c+'"');return n._activePagesMeshData=[],n._textWidth=0,n._textHeight=0,n._align=h,n._tint=l,n._font=void 0,n._fontName=c,n._fontSize=p,n.text=r,n._maxWidth=u,n._maxLineHeight=0,n._letterSpacing=f,n._anchor=new e.ObservablePoint((function(){n.dirty=!0}),n,0,0),n._roundPixels=t.settings.ROUND_PIXELS,n.dirty=!0,n._resolution=t.settings.RESOLUTION,n._autoResolution=!0,n._textureCache={},n}return function(e,t){function r(){this.constructor=e}l(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(s,a),s.prototype.updateText=function(){for(var t,a=y.available[this._fontName],s=this.fontSize,h=s/a.size,l=new e.Point,u=[],f=[],c=[],p=m(this._text.replace(/(?:\r\n|\r)/g,"\n")||" "),d=this._maxWidth*a.size/s,g="none"===a.distanceFieldType?_:b,x=null,w=0,M=0,A=0,S=-1,P=0,E=0,F=0,C=0,O=0;O<p.length;O++){var I=v(K=p[O]);if(/(?:\s)/.test(K)&&(S=O,P=w,C++),"\r"!==K&&"\n"!==K){var D=a.chars[I];if(D){x&&D.kerning[x]&&(l.x+=D.kerning[x]);var N=T.pop()||{texture:n.Texture.EMPTY,line:0,charCode:0,prevSpaces:0,position:new e.Point};N.texture=D.texture,N.line=A,N.charCode=I,N.position.x=l.x+D.xOffset+this._letterSpacing/2,N.position.y=l.y+D.yOffset,N.prevSpaces=C,u.push(N),w=N.position.x+Math.max(D.xAdvance-D.xOffset,D.texture.orig.width),l.x+=D.xAdvance+this._letterSpacing,F=Math.max(F,D.yOffset+D.texture.height),x=I,-1!==S&&d>0&&l.x>d&&(++E,i.removeItems(u,1+S-E,1+O-S),O=S,S=-1,f.push(P),c.push(u.length>0?u[u.length-1].prevSpaces:0),M=Math.max(M,P),A++,l.x=0,l.y+=a.lineHeight,x=null,C=0)}}else f.push(w),c.push(-1),M=Math.max(M,w),++A,++E,l.x=0,l.y+=a.lineHeight,x=null,C=0}var B=p[p.length-1];"\r"!==B&&"\n"!==B&&(/(?:\s)/.test(B)&&(w=P),f.push(w),M=Math.max(M,w),c.push(-1));var k=[];for(O=0;O<=A;O++){var L=0;"right"===this._align?L=M-f[O]:"center"===this._align?L=(M-f[O])/2:"justify"===this._align&&(L=c[O]<0?0:(M-f[O])/c[O]),k.push(L)}var z=u.length,H={},R=[],j=this._activePagesMeshData;g.push.apply(g,j);for(O=0;O<z;O++){var W=(ee=u[O].texture).baseTexture.uid;if(!H[W]){if(!(se=g.pop())){var U=new r.MeshGeometry,q=void 0,X=void 0;"none"===a.distanceFieldType?(q=new r.MeshMaterial(n.Texture.EMPTY),X=o.BLEND_MODES.NORMAL):(q=new r.MeshMaterial(n.Texture.EMPTY,{program:n.Program.from("// Mesh material default fragment\r\nattribute vec2 aVertexPosition;\r\nattribute vec2 aTextureCoord;\r\n\r\nuniform mat3 projectionMatrix;\r\nuniform mat3 translationMatrix;\r\nuniform mat3 uTextureMatrix;\r\n\r\nvarying vec2 vTextureCoord;\r\n\r\nvoid main(void)\r\n{\r\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\r\n\r\n    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;\r\n}\r\n","// Pixi texture info\r\nvarying vec2 vTextureCoord;\r\nuniform sampler2D uSampler;\r\n\r\n// Tint\r\nuniform vec4 uColor;\r\n\r\n// on 2D applications fwidth is screenScale / glyphAtlasScale * distanceFieldRange\r\nuniform float uFWidth;\r\n\r\nvoid main(void) {\r\n\r\n  // To stack MSDF and SDF we need a non-pre-multiplied-alpha texture.\r\n  vec4 texColor = texture2D(uSampler, vTextureCoord);\r\n\r\n  // MSDF\r\n  float median = texColor.r + texColor.g + texColor.b -\r\n                  min(texColor.r, min(texColor.g, texColor.b)) -\r\n                  max(texColor.r, max(texColor.g, texColor.b));\r\n  // SDF\r\n  median = min(median, texColor.a);\r\n\r\n  float screenPxDistance = uFWidth * (median - 0.5);\r\n  float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\r\n  if (median < 0.01) {\r\n    alpha = 0.0;\r\n  } else if (median > 0.99) {\r\n    alpha = 1.0;\r\n  }\r\n\r\n  // NPM Textures, NPM outputs\r\n  gl_FragColor = vec4(uColor.rgb, uColor.a * alpha);\r\n\r\n}\r\n"),uniforms:{uFWidth:0}}),X=o.BLEND_MODES.NORMAL_NPM);var Y=new r.Mesh(U,q);Y.blendMode=X,se={index:0,indexCount:0,vertexCount:0,uvsCount:0,total:0,mesh:Y,vertices:null,uvs:null,indices:null}}se.index=0,se.indexCount=0,se.vertexCount=0,se.uvsCount=0,se.total=0;var G=this._textureCache;G[W]=G[W]||new n.Texture(ee.baseTexture),se.mesh.texture=G[W],se.mesh.tint=this._tint,R.push(se),H[W]=se}H[W].total++}for(O=0;O<j.length;O++)-1===R.indexOf(j[O])&&this.removeChild(j[O].mesh);for(O=0;O<R.length;O++)R[O].mesh.parent!==this&&this.addChild(R[O].mesh);for(var O in this._activePagesMeshData=R,H){var V=(se=H[O]).total;if(!((null===(t=se.indices)||void 0===t?void 0:t.length)>6*V)||se.vertices.length<2*r.Mesh.BATCHABLE_SIZE)se.vertices=new Float32Array(8*V),se.uvs=new Float32Array(8*V),se.indices=new Uint16Array(6*V);else for(var Z=se.total,$=se.vertices,J=4*Z*2;J<$.length;J++)$[J]=0;se.mesh.size=6*V}for(O=0;O<z;O++){var K,Q=(K=u[O]).position.x+k[K.line]*("justify"===this._align?K.prevSpaces:1);this._roundPixels&&(Q=Math.round(Q));var ee,te=Q*h,re=K.position.y*h,ie=H[(ee=K.texture).baseTexture.uid],ne=ee.frame,ae=ee._uvs,oe=ie.index++;ie.indices[6*oe+0]=0+4*oe,ie.indices[6*oe+1]=1+4*oe,ie.indices[6*oe+2]=2+4*oe,ie.indices[6*oe+3]=0+4*oe,ie.indices[6*oe+4]=2+4*oe,ie.indices[6*oe+5]=3+4*oe,ie.vertices[8*oe+0]=te,ie.vertices[8*oe+1]=re,ie.vertices[8*oe+2]=te+ne.width*h,ie.vertices[8*oe+3]=re,ie.vertices[8*oe+4]=te+ne.width*h,ie.vertices[8*oe+5]=re+ne.height*h,ie.vertices[8*oe+6]=te,ie.vertices[8*oe+7]=re+ne.height*h,ie.uvs[8*oe+0]=ae.x0,ie.uvs[8*oe+1]=ae.y0,ie.uvs[8*oe+2]=ae.x1,ie.uvs[8*oe+3]=ae.y1,ie.uvs[8*oe+4]=ae.x2,ie.uvs[8*oe+5]=ae.y2,ie.uvs[8*oe+6]=ae.x3,ie.uvs[8*oe+7]=ae.y3}for(var O in this._textWidth=M*h,this._textHeight=(l.y+a.lineHeight)*h,H){var se=H[O];if(0!==this.anchor.x||0!==this.anchor.y)for(var he=0,le=this._textWidth*this.anchor.x,ue=this._textHeight*this.anchor.y,fe=0;fe<se.total;fe++)se.vertices[he++]-=le,se.vertices[he++]-=ue,se.vertices[he++]-=le,se.vertices[he++]-=ue,se.vertices[he++]-=le,se.vertices[he++]-=ue,se.vertices[he++]-=le,se.vertices[he++]-=ue;this._maxLineHeight=F*h;var ce=se.mesh.geometry.getBuffer("aVertexPosition"),pe=se.mesh.geometry.getBuffer("aTextureCoord"),de=se.mesh.geometry.getIndex();ce.data=se.vertices,pe.data=se.uvs,de.data=se.indices,ce.update(),pe.update(),de.update()}for(O=0;O<u.length;O++)T.push(u[O]);this._font=a,this.dirty=!1},s.prototype.updateTransform=function(){this.validate(),this.containerUpdateTransform()},s.prototype._render=function(e){this._autoResolution&&this._resolution!==e.resolution&&(this._resolution=e.resolution,this.dirty=!0);var t=y.available[this._fontName],r=t.distanceFieldRange,i=t.distanceFieldType,n=t.size;if("none"!==i)for(var o=this.worldTransform,s=o.a,h=o.b,l=o.c,u=o.d,f=Math.sqrt(s*s+h*h),c=Math.sqrt(l*l+u*u),p=(Math.abs(f)+Math.abs(c))/2,d=this.fontSize/n,g=0,x=this._activePagesMeshData;g<x.length;g++){x[g].mesh.shader.uniforms.uFWidth=p*r*d*this._resolution}a.prototype._render.call(this,e)},s.prototype.getLocalBounds=function(){return this.validate(),a.prototype.getLocalBounds.call(this)},s.prototype.validate=function(){var e=y.available[this._fontName];if(!e)throw new Error('Missing BitmapFont "'+this._fontName+'"');this._font!==e&&(this.dirty=!0),this.dirty&&this.updateText()},Object.defineProperty(s.prototype,"tint",{get:function(){return this._tint},set:function(e){if(this._tint!==e){this._tint=e;for(var t=0;t<this._activePagesMeshData.length;t++)this._activePagesMeshData[t].mesh.tint=e}},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"align",{get:function(){return this._align},set:function(e){this._align!==e&&(this._align=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"fontName",{get:function(){return this._fontName},set:function(e){if(!y.available[e])throw new Error('Missing BitmapFont "'+e+'"');this._fontName!==e&&(this._fontName=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"fontSize",{get:function(){var e;return null!==(e=this._fontSize)&&void 0!==e?e:y.available[this._fontName].size},set:function(e){this._fontSize!==e&&(this._fontSize=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"anchor",{get:function(){return this._anchor},set:function(e){"number"==typeof e?this._anchor.set(e):this._anchor.copyFrom(e)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"text",{get:function(){return this._text},set:function(e){e=String(null==e?"":e),this._text!==e&&(this._text=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"maxWidth",{get:function(){return this._maxWidth},set:function(e){this._maxWidth!==e&&(this._maxWidth=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"maxLineHeight",{get:function(){return this.validate(),this._maxLineHeight},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"textWidth",{get:function(){return this.validate(),this._textWidth},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"letterSpacing",{get:function(){return this._letterSpacing},set:function(e){this._letterSpacing!==e&&(this._letterSpacing=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(e){e!==this._roundPixels&&(this._roundPixels=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"textHeight",{get:function(){return this.validate(),this._textHeight},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"resolution",{get:function(){return this._resolution},set:function(e){this._autoResolution=!1,this._resolution!==e&&(this._resolution=e,this.dirty=!0)},enumerable:!1,configurable:!0}),s.prototype.destroy=function(e){var t=this._textureCache,r="none"===y.available[this._fontName].distanceFieldType?_:b;r.push.apply(r,this._activePagesMeshData);for(var i=0,o=this._activePagesMeshData;i<o.length;i++){var s=o[i];this.removeChild(s.mesh)}for(var h in this._activePagesMeshData=[],r.filter((function(e){return t[e.mesh.texture.baseTexture.uid]})).forEach((function(e){e.mesh.texture=n.Texture.EMPTY})),t){t[h].destroy(),delete t[h]}this._font=null,this._textureCache=null,a.prototype.destroy.call(this,e)},s.styleDefaults={align:"left",tint:16777215,maxWidth:0,letterSpacing:0},s}(s.Container),M=function(){function e(){}return e.add=function(){h.LoaderResource.setExtensionXhrType("fnt",h.LoaderResource.XHR_RESPONSE_TYPE.TEXT)},e.use=function(t,r){var i=g(t.data);if(i)for(var n=e.getBaseUrl(this,t),a=i.parse(t.data),o={},s=function(e){o[e.metadata.pageFile]=e.texture,Object.keys(o).length===a.page.length&&(t.bitmapFont=y.install(a,o,!0),r())},l=0;l<a.page.length;++l){var u=a.page[l].file,f=n+u,c=!1;for(var p in this.resources){var d=this.resources[p];if(d.url===f){d.metadata.pageFile=u,d.texture?s(d):d.onAfterMiddleware.add(s),c=!0;break}}if(!c){var x={crossOrigin:t.crossOrigin,loadType:h.LoaderResource.LOAD_TYPE.IMAGE,metadata:Object.assign({pageFile:u},t.metadata.imageMetadata),parentResource:t};this.add(f,x,s)}}else r()},e.getBaseUrl=function(t,r){var i=r.isDataUrl?"":e.dirname(r.url);return r.isDataUrl&&("."===i&&(i=""),t.baseUrl&&i&&"/"===t.baseUrl.charAt(t.baseUrl.length-1)&&(i+="/")),(i=i.replace(t.baseUrl,""))&&"/"!==i.charAt(i.length-1)&&(i+="/"),i},e.dirname=function(e){var t=e.replace(/\\/g,"/").replace(/\/$/,"").replace(/\/[^\/]*$/,"");return t===e?".":""===t?"/":t},e.extension=n.ExtensionType.Loader,e}();exports.BitmapFont=y,exports.BitmapFontData=u,exports.BitmapFontLoader=M,exports.BitmapText=w,exports.TextFormat=f,exports.XMLFormat=c,exports.XMLStringFormat=p,exports.autoDetectFormat=g;
//# sourceMappingURL=text-bitmap.min.js.map
