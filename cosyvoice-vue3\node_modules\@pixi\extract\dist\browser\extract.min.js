/*!
 * @pixi/extract - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/extract is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_extract=function(e,t,r,n,a){"use strict";var i=new n.Rectangle,o=function(){function e(e){this.renderer=e}return e.prototype.image=function(e,t,r){var n=new Image;return n.src=this.base64(e,t,r),n},e.prototype.base64=function(e,t,r){return this.canvas(e).toDataURL(t,r)},e.prototype.canvas=function(t,n){var a=this._rawPixels(t,n),i=a.pixels,o=a.width,s=a.height,u=a.flipY,h=new r.CanvasRenderTarget(o,s,1),d=h.context.getImageData(0,0,o,s);if(e.arrayPostDivide(i,d.data),h.context.putImageData(d,0,0),u){var f=new r.CanvasRenderTarget(h.width,h.height,1);f.context.scale(1,-1),f.context.drawImage(h.canvas,0,-s),h.destroy(),h=f}return h.canvas},e.prototype.pixels=function(t,r){var n=this._rawPixels(t,r).pixels;return e.arrayPostDivide(n,n),n},e.prototype._rawPixels=function(e,r){var n,o,s=this.renderer,u=!1,h=!1;if(e)if(e instanceof a.RenderTexture)o=e;else{var d=s.context.webGLVersion>=2?s.multisample:t.MSAA_QUALITY.NONE;if(o=this.renderer.generateTexture(e,{multisample:d}),d!==t.MSAA_QUALITY.NONE){var f=a.RenderTexture.create({width:o.width,height:o.height});s.framebuffer.bind(o.framebuffer),s.framebuffer.blit(f.framebuffer),s.framebuffer.bind(null),o.destroy(!0),o=f}h=!0}o?(n=o.baseTexture.resolution,r=null!=r?r:o.frame,u=!1,s.renderTexture.bind(o)):(n=s.resolution,r||((r=i).width=s.width,r.height=s.height),u=!0,s.renderTexture.bind(null));var l=Math.round(r.width*n),c=Math.round(r.height*n),x=new Uint8Array(4*l*c),p=s.gl;return p.readPixels(Math.round(r.x*n),Math.round(r.y*n),l,c,p.RGBA,p.UNSIGNED_BYTE,x),h&&o.destroy(!0),{pixels:x,width:l,height:c,flipY:u}},e.prototype.destroy=function(){this.renderer=null},e.arrayPostDivide=function(e,t){for(var r=0;r<e.length;r+=4){var n=t[r+3]=e[r+3];0!==n?(t[r]=Math.round(Math.min(255*e[r]/n,255)),t[r+1]=Math.round(Math.min(255*e[r+1]/n,255)),t[r+2]=Math.round(Math.min(255*e[r+2]/n,255))):(t[r]=e[r],t[r+1]=e[r+1],t[r+2]=e[r+2])}},e.extension={name:"extract",type:a.ExtensionType.RendererPlugin},e}();return e.Extract=o,Object.defineProperty(e,"__esModule",{value:!0}),e}({},PIXI,PIXI.utils,PIXI,PIXI);Object.assign(this.PIXI,_pixi_extract);
//# sourceMappingURL=extract.min.js.map
