/*!
 * @pixi/filter-alpha - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/filter-alpha is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{defaultVertex as r,Filter as t}from"@pixi/core";var o=function(r,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var o in t)t.hasOwnProperty(o)&&(r[o]=t[o])},o(r,t)};var n=function(t){function n(o){void 0===o&&(o=1);var n=t.call(this,r,"varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform float uAlpha;\n\nvoid main(void)\n{\n   gl_FragColor = texture2D(uSampler, vTextureCoord) * uAlpha;\n}\n",{uAlpha:1})||this;return n.alpha=o,n}return function(r,t){function n(){this.constructor=r}o(r,t),r.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(n,t),Object.defineProperty(n.prototype,"alpha",{get:function(){return this.uniforms.uAlpha},set:function(r){this.uniforms.uAlpha=r},enumerable:!1,configurable:!0}),n}(t);export{n as AlphaFilter};
//# sourceMappingURL=filter-alpha.min.mjs.map
