{"version": 3, "file": "runner.min.js", "sources": ["../../src/Runner.ts"], "sourcesContent": ["/**\n * A Runner is a highly performant and simple alternative to signals. Best used in situations\n * where events are dispatched to many objects at high frequency (say every frame!)\n *\n *\n * like a signal..\n * ```\n * import { Runner } from '@pixi/runner';\n *\n * const myObject = {\n *     loaded: new Runner('loaded')\n * }\n *\n * const listener = {\n *     loaded: function(){\n *         // thin\n *     }\n * }\n *\n * myObject.loaded.add(listener);\n *\n * myObject.loaded.emit();\n * ```\n *\n * Or for handling calling the same function on many items\n * ```\n * import { Runner } from '@pixi/runner';\n *\n * const myGame = {\n *     update: new Runner('update')\n * }\n *\n * const gameObject = {\n *     update: function(time){\n *         // update my gamey state\n *     }\n * }\n *\n * myGame.update.add(gameObject);\n *\n * myGame.update.emit(time);\n * ```\n * @memberof PIXI\n */\nexport class Runner\n{\n    public items: any[];\n    private _name: string;\n    private _aliasCount: number;\n\n    /**\n     * @param name - The function name that will be executed on the listeners added to this Runner.\n     */\n    constructor(name: string)\n    {\n        this.items = [];\n        this._name = name;\n        this._aliasCount = 0;\n    }\n\n    /* eslint-disable jsdoc/require-param, jsdoc/check-param-names */\n    /**\n     * Dispatch/Broadcast Runner to all listeners added to the queue.\n     * @param {...any} params - (optional) parameters to pass to each listener\n     */\n    /*  eslint-enable jsdoc/require-param, jsdoc/check-param-names */\n    public emit(a0?: unknown, a1?: unknown, a2?: unknown, a3?: unknown,\n        a4?: unknown, a5?: unknown, a6?: unknown, a7?: unknown): this\n    {\n        if (arguments.length > 8)\n        {\n            throw new Error('max arguments reached');\n        }\n\n        const { name, items } = this;\n\n        this._aliasCount++;\n\n        for (let i = 0, len = items.length; i < len; i++)\n        {\n            items[i][name](a0, a1, a2, a3, a4, a5, a6, a7);\n        }\n\n        if (items === this.items)\n        {\n            this._aliasCount--;\n        }\n\n        return this;\n    }\n\n    private ensureNonAliasedItems(): void\n    {\n        if (this._aliasCount > 0 && this.items.length > 1)\n        {\n            this._aliasCount = 0;\n            this.items = this.items.slice(0);\n        }\n    }\n\n    /**\n     * Add a listener to the Runner\n     *\n     * Runners do not need to have scope or functions passed to them.\n     * All that is required is to pass the listening object and ensure that it has contains a function that has the same name\n     * as the name provided to the Runner when it was created.\n     *\n     * Eg A listener passed to this Runner will require a 'complete' function.\n     *\n     * ```\n     * import { Runner } from '@pixi/runner';\n     *\n     * const complete = new Runner('complete');\n     * ```\n     *\n     * The scope used will be the object itself.\n     * @param {any} item - The object that will be listening.\n     */\n    public add(item: unknown): this\n    {\n        if ((item as any)[this._name])\n        {\n            this.ensureNonAliasedItems();\n            this.remove(item);\n            this.items.push(item);\n        }\n\n        return this;\n    }\n\n    /**\n     * Remove a single listener from the dispatch queue.\n     * @param {any} item - The listener that you would like to remove.\n     */\n    public remove(item: unknown): this\n    {\n        const index = this.items.indexOf(item);\n\n        if (index !== -1)\n        {\n            this.ensureNonAliasedItems();\n            this.items.splice(index, 1);\n        }\n\n        return this;\n    }\n\n    /**\n     * Check to see if the listener is already in the Runner\n     * @param {any} item - The listener that you would like to check.\n     */\n    public contains(item: unknown): boolean\n    {\n        return this.items.indexOf(item) !== -1;\n    }\n\n    /** Remove all listeners from the Runner */\n    public removeAll(): this\n    {\n        this.ensureNonAliasedItems();\n        this.items.length = 0;\n\n        return this;\n    }\n\n    /** Remove all references, don't use after this. */\n    public destroy(): void\n    {\n        this.removeAll();\n        this.items = null;\n        this._name = null;\n    }\n\n    /**\n     * `true` if there are no this Runner contains no listeners\n     * @readonly\n     */\n    public get empty(): boolean\n    {\n        return this.items.length === 0;\n    }\n\n    /**\n     * The name of the runner.\n     * @readonly\n     */\n    public get name(): string\n    {\n        return this._name;\n    }\n}\n\nObject.defineProperties(Runner.prototype, {\n    /**\n     * Alias for `emit`\n     * @memberof PIXI.Runner#\n     * @method dispatch\n     * @see PIXI.Runner#emit\n     */\n    dispatch: { value: Runner.prototype.emit },\n    /**\n     * Alias for `emit`\n     * @memberof PIXI.Runner#\n     * @method run\n     * @see PIXI.Runner#emit\n     */\n    run: { value: Runner.prototype.emit },\n});\n"], "names": ["Runner", "name", "this", "items", "_name", "_aliasCount", "prototype", "emit", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "arguments", "length", "Error", "_a", "i", "len", "ensureNonAliasedItems", "slice", "add", "item", "remove", "push", "index", "indexOf", "splice", "contains", "removeAll", "destroy", "Object", "defineProperty", "get", "defineProperties", "dispatch", "value", "run"], "mappings": ";;;;;;;oEA4CA,IAAAA,EAAA,WASI,SAAAA,EAAYC,GAERC,KAAKC,MAAQ,GACbD,KAAKE,MAAQH,EACbC,KAAKG,YAAc,EAqI3B,OA5HWL,EAAAM,UAAAC,KAAP,SAAYC,EAAcC,EAAcC,EAAcC,EAClDC,EAAcC,EAAcC,EAAcC,GAE1C,GAAIC,UAAUC,OAAS,EAEnB,MAAM,IAAIC,MAAM,yBAGd,IAAAC,EAAkBjB,KAAhBD,SAAME,UAEdD,KAAKG,cAEL,IAAK,IAAIe,EAAI,EAAGC,EAAMlB,EAAMc,OAAQG,EAAIC,EAAKD,IAEzCjB,EAAMiB,GAAGnB,GAAMO,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAQ/C,OALIZ,IAAUD,KAAKC,OAEfD,KAAKG,cAGFH,MAGHF,EAAAM,UAAAgB,sBAAR,WAEQpB,KAAKG,YAAc,GAAKH,KAAKC,MAAMc,OAAS,IAE5Cf,KAAKG,YAAc,EACnBH,KAAKC,MAAQD,KAAKC,MAAMoB,MAAM,KAsB/BvB,EAAGM,UAAAkB,IAAV,SAAWC,GASP,OAPKA,EAAavB,KAAKE,SAEnBF,KAAKoB,wBACLpB,KAAKwB,OAAOD,GACZvB,KAAKC,MAAMwB,KAAKF,IAGbvB,MAOJF,EAAMM,UAAAoB,OAAb,SAAcD,GAEV,IAAMG,EAAQ1B,KAAKC,MAAM0B,QAAQJ,GAQjC,OANe,IAAXG,IAEA1B,KAAKoB,wBACLpB,KAAKC,MAAM2B,OAAOF,EAAO,IAGtB1B,MAOJF,EAAQM,UAAAyB,SAAf,SAAgBN,GAEZ,OAAqC,IAA9BvB,KAAKC,MAAM0B,QAAQJ,IAIvBzB,EAAAM,UAAA0B,UAAP,WAKI,OAHA9B,KAAKoB,wBACLpB,KAAKC,MAAMc,OAAS,EAEbf,MAIJF,EAAAM,UAAA2B,QAAP,WAEI/B,KAAK8B,YACL9B,KAAKC,MAAQ,KACbD,KAAKE,MAAQ,MAOjB8B,OAAAC,eAAWnC,EAAKM,UAAA,QAAA,CAAhB8B,IAAA,WAEI,OAA6B,IAAtBlC,KAAKC,MAAMc,wCAOtBiB,OAAAC,eAAWnC,EAAIM,UAAA,OAAA,CAAf8B,IAAA,WAEI,OAAOlC,KAAKE,uCAEnBJ,KAEDkC,OAAOG,iBAAiBrC,EAAOM,UAAW,CAOtCgC,SAAU,CAAEC,MAAOvC,EAAOM,UAAUC,MAOpCiC,IAAK,CAAED,MAAOvC,EAAOM,UAAUC"}