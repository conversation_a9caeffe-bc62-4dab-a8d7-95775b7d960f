<!DOCTYPE html>
<html>
<head>
    <title>存储调试工具</title>
</head>
<body>
    <h1>头像存储调试工具</h1>
    <div>
        <button onclick="testSaveAvatar()">测试保存头像</button>
        <button onclick="testLoadAvatar()">测试加载头像</button>
        <button onclick="checkAllStorage()">检查所有存储</button>
        <button onclick="testStyleImages()">检查风格图片存储</button>
    </div>
    <div id="output" style="margin-top: 20px; font-family: monospace; white-space: pre-wrap;"></div>

    <script>
        function log(msg) {
            const output = document.getElementById('output');
            output.textContent += new Date().toISOString() + ': ' + msg + '\n';
        }

        async function testSaveAvatar() {
            try {
                log('开始测试保存头像...');
                
                // 模拟保存头像
                const testUrl = 'http://localhost:3001/avatars/test_debug_' + Date.now() + '.png';
                
                // 检查electronStoreManager
                if (window.electronAPI) {
                    log('检测到Electron环境');
                    // Electron环境
                    try {
                        const existing = await window.electronAPI.store.get('protagonist-avatars') || {};
                        existing['current'] = testUrl;
                        await window.electronAPI.store.set('protagonist-avatars', existing);
                        log('Electron存储保存成功: ' + testUrl);
                    } catch (e) {
                        log('Electron存储保存失败: ' + e.message);
                    }
                } else {
                    log('检测到浏览器环境');
                    // 浏览器环境 - 使用localStorage模拟
                    const existing = JSON.parse(localStorage.getItem('protagonist-avatars') || '{}');
                    existing['current'] = testUrl;
                    localStorage.setItem('protagonist-avatars', JSON.stringify(existing));
                    log('localStorage保存成功: ' + testUrl);
                }
                
            } catch (error) {
                log('保存失败: ' + error.message);
            }
        }

        async function testLoadAvatar() {
            try {
                log('开始测试加载头像...');
                
                if (window.electronAPI) {
                    log('从Electron存储加载...');
                    const avatars = await window.electronAPI.store.get('protagonist-avatars') || {};
                    const current = avatars['current'];
                    log('Electron加载结果: ' + (current || 'null'));
                } else {
                    log('从localStorage加载...');
                    const avatars = JSON.parse(localStorage.getItem('protagonist-avatars') || '{}');
                    const current = avatars['current'];
                    log('localStorage加载结果: ' + (current || 'null'));
                }
                
            } catch (error) {
                log('加载失败: ' + error.message);
            }
        }

        async function checkAllStorage() {
            try {
                log('检查所有存储...');
                
                if (window.electronAPI) {
                    log('=== Electron Store 内容 ===');
                    try {
                        const allKeys = ['protagonist-avatars', 'protagonist-avatar', 'preset-style-images', 'current-story-config'];
                        for (const key of allKeys) {
                            const value = await window.electronAPI.store.get(key);
                            log(`${key}: ${value ? JSON.stringify(value).substring(0, 100) + '...' : 'null'}`);
                        }
                    } catch (e) {
                        log('读取Electron Store失败: ' + e.message);
                    }
                } else {
                    log('=== localStorage 内容 ===');
                    const keys = ['protagonist-avatars', 'protagonist-avatar', 'preset-style-images', 'current-story-config'];
                    for (const key of keys) {
                        const value = localStorage.getItem(key);
                        log(`${key}: ${value ? value.substring(0, 100) + '...' : 'null'}`);
                    }
                }
                
            } catch (error) {
                log('检查存储失败: ' + error.message);
            }
        }

        async function testStyleImages() {
            try {
                log('检查风格图片存储（对比）...');
                
                if (window.electronAPI) {
                    const styleImages = await window.electronAPI.store.get('preset-style-images') || {};
                    log('风格图片数量: ' + Object.keys(styleImages).length);
                    log('风格图片键: ' + Object.keys(styleImages).join(', '));
                } else {
                    const styleImages = JSON.parse(localStorage.getItem('preset-style-images') || '{}');
                    log('风格图片数量: ' + Object.keys(styleImages).length);
                    log('风格图片键: ' + Object.keys(styleImages).join(', '));
                }
                
            } catch (error) {
                log('检查风格图片失败: ' + error.message);
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('页面加载完成，环境: ' + (window.electronAPI ? 'Electron' : 'Browser'));
            checkAllStorage();
        };
    </script>
</body>
</html>