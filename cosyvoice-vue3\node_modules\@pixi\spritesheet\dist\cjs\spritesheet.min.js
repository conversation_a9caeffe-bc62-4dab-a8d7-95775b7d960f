/*!
 * @pixi/spritesheet - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/spritesheet is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/math"),t=require("@pixi/core"),r=require("@pixi/utils"),s=require("@pixi/loaders"),i=function(){function s(e,r,s){void 0===s&&(s=null),this.linkedSheets=[],this._texture=e instanceof t.Texture?e:null,this.baseTexture=e instanceof t.BaseTexture?e:this._texture.baseTexture,this.textures={},this.animations={},this.data=r;var i=this.baseTexture.resource;this.resolution=this._updateResolution(s||(i?i.url:null)),this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}return s.prototype._updateResolution=function(e){void 0===e&&(e=null);var t=this.data.meta.scale,s=r.getResolutionOfUrl(e,null);return null===s&&(s=void 0!==t?parseFloat(t):1),1!==s&&this.baseTexture.setResolution(s),s},s.prototype.parse=function(e){var t=this;return new Promise((function(r){t._callback=function(t){null==e||e(t),r(t)},t._batchIndex=0,t._frameKeys.length<=s.BATCH_SIZE?(t._processFrames(0),t._processAnimations(),t._parseComplete()):t._nextBatch()}))},s.prototype._processFrames=function(r){for(var i=r,o=s.BATCH_SIZE;i-r<o&&i<this._frameKeys.length;){var a=this._frameKeys[i],n=this._frames[a],u=n.frame;if(u){var l=null,h=null,c=!1!==n.trimmed&&n.sourceSize?n.sourceSize:n.frame,f=new e.Rectangle(0,0,Math.floor(c.w)/this.resolution,Math.floor(c.h)/this.resolution);l=n.rotated?new e.Rectangle(Math.floor(u.x)/this.resolution,Math.floor(u.y)/this.resolution,Math.floor(u.h)/this.resolution,Math.floor(u.w)/this.resolution):new e.Rectangle(Math.floor(u.x)/this.resolution,Math.floor(u.y)/this.resolution,Math.floor(u.w)/this.resolution,Math.floor(u.h)/this.resolution),!1!==n.trimmed&&n.spriteSourceSize&&(h=new e.Rectangle(Math.floor(n.spriteSourceSize.x)/this.resolution,Math.floor(n.spriteSourceSize.y)/this.resolution,Math.floor(u.w)/this.resolution,Math.floor(u.h)/this.resolution)),this.textures[a]=new t.Texture(this.baseTexture,l,f,h,n.rotated?2:0,n.anchor),t.Texture.addToCache(this.textures[a],a)}i++}},s.prototype._processAnimations=function(){var e=this.data.animations||{};for(var t in e){this.animations[t]=[];for(var r=0;r<e[t].length;r++){var s=e[t][r];this.animations[t].push(this.textures[s])}}},s.prototype._parseComplete=function(){var e=this._callback;this._callback=null,this._batchIndex=0,e.call(this,this.textures)},s.prototype._nextBatch=function(){var e=this;this._processFrames(this._batchIndex*s.BATCH_SIZE),this._batchIndex++,setTimeout((function(){e._batchIndex*s.BATCH_SIZE<e._frameKeys.length?e._nextBatch():(e._processAnimations(),e._parseComplete())}),0)},s.prototype.destroy=function(e){var t;for(var r in void 0===e&&(e=!1),this.textures)this.textures[r].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,e&&(null===(t=this._texture)||void 0===t||t.destroy(),this.baseTexture.destroy()),this._texture=null,this.baseTexture=null,this.linkedSheets=[]},s.BATCH_SIZE=1e3,s}(),o=function(){function e(){}return e.use=function(t,o){var a,n,u=this,l=t.name+"_image";if(t.data&&t.type===s.LoaderResource.TYPE.JSON&&t.data.frames&&!u.resources[l]){var h=null===(n=null===(a=t.data)||void 0===a?void 0:a.meta)||void 0===n?void 0:n.related_multi_packs;if(Array.isArray(h))for(var c=function(e){if("string"!=typeof e)return"continue";var i=e.replace(".json",""),o=r.url.resolve(t.url.replace(u.baseUrl,""),e);if(u.resources[i]||Object.values(u.resources).some((function(e){return r.url.format(r.url.parse(e.url))===o})))return"continue";var a={crossOrigin:t.crossOrigin,loadType:s.LoaderResource.LOAD_TYPE.XHR,xhrType:s.LoaderResource.XHR_RESPONSE_TYPE.JSON,parentResource:t,metadata:t.metadata};u.add(i,o,a)},f=0,d=h;f<d.length;f++){c(d[f])}var p={crossOrigin:t.crossOrigin,metadata:t.metadata.imageMetadata,parentResource:t},_=e.getResourcePath(t,u.baseUrl);u.add(l,_,p,(function(e){if(e.error)o(e.error);else{var r=new i(e.texture,t.data,t.url);r.parse().then((function(){t.spritesheet=r,t.textures=r.textures,o()}))}}))}else o()},e.getResourcePath=function(e,t){return e.isDataUrl?e.data.meta.image:r.url.resolve(e.url.replace(t,""),e.data.meta.image)},e.extension=t.ExtensionType.Loader,e}();exports.Spritesheet=i,exports.SpritesheetLoader=o;
//# sourceMappingURL=spritesheet.min.js.map
