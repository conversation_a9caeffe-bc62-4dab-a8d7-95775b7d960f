/*!
 * @pixi/extensions - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/extensions is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_extensions=function(e){"use strict";var n,r=function(){return r=Object.assign||function(e){for(var n,r=arguments,t=1,i=arguments.length;t<i;t++)for(var o in n=r[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},r.apply(this,arguments)};e.ExtensionType=void 0,(n=e.ExtensionType||(e.ExtensionType={})).Application="application",n.RendererPlugin="renderer-webgl-plugin",n.CanvasRendererPlugin="renderer-canvas-plugin",n.Loader="loader",n.LoadParser="load-parser",n.ResolveParser="resolve-parser",n.<PERSON>acheParser="cache-parser",n.DetectionParser="detection-parser";var t=function(e){if("function"==typeof e||"object"==typeof e&&e.extension){var n="object"!=typeof e.extension?{type:e.extension}:e.extension;e=r(r({},n),{ref:e})}if("object"!=typeof e)throw new Error("Invalid extension type");return"string"==typeof(e=r({},e)).type&&(e.type=[e.type]),e},i={_addHandlers:null,_removeHandlers:null,_queue:{},remove:function(){for(var e=arguments,n=this,r=[],i=0;i<arguments.length;i++)r[i]=e[i];return r.map(t).forEach((function(e){e.type.forEach((function(r){var t,i;return null===(i=(t=n._removeHandlers)[r])||void 0===i?void 0:i.call(t,e)}))})),this},add:function(){for(var e=arguments,n=this,r=[],i=0;i<arguments.length;i++)r[i]=e[i];return r.map(t).forEach((function(e){e.type.forEach((function(r){var t=n._addHandlers,i=n._queue;t[r]?t[r](e):(i[r]=i[r]||[],i[r].push(e))}))})),this},handle:function(e,n,r){var t=this._addHandlers=this._addHandlers||{},i=this._removeHandlers=this._removeHandlers||{};t[e]=n,i[e]=r;var o=this._queue;return o[e]&&(o[e].forEach((function(e){return n(e)})),delete o[e]),this},handleByMap:function(e,n){return this.handle(e,(function(e){n[e.name]=e.ref}),(function(e){delete n[e.name]}))},handleByList:function(n,r){return this.handle(n,(function(t){var i,o;r.includes(t.ref)||(r.push(t.ref),n===e.ExtensionType.Loader&&(null===(o=(i=t.ref).add)||void 0===o||o.call(i)))}),(function(e){var n=r.indexOf(e.ref);-1!==n&&r.splice(n,1)}))}};return e.extensions=i,Object.defineProperty(e,"__esModule",{value:!0}),e}({});Object.assign(this.PIXI,_pixi_extensions);
//# sourceMappingURL=extensions.min.js.map
