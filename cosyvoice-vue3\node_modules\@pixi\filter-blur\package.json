{"name": "@pixi/filter-blur", "version": "6.5.10", "main": "dist/cjs/filter-blur.js", "module": "dist/esm/filter-blur.mjs", "bundle": "dist/browser/filter-blur.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/filter-blur.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/filter-blur.js"}}}, "namespace": "PIXI.filters", "description": "Filter that blurs the display object", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/constants": "6.5.10", "@pixi/core": "6.5.10", "@pixi/settings": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}