/*!
 * @pixi/ticker - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/ticker is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t,e=require("@pixi/settings"),i=require("@pixi/extensions");e.settings.TARGET_FPMS=.06,exports.UPDATE_PRIORITY=void 0,(t=exports.UPDATE_PRIORITY||(exports.UPDATE_PRIORITY={}))[t.INTERACTION=50]="INTERACTION",t[t.HIGH=25]="HIGH",t[t.NORMAL=0]="NORMAL",t[t.LOW=-25]="LOW",t[t.UTILITY=-50]="UTILITY";var s=function(){function t(t,e,i,s){void 0===e&&(e=null),void 0===i&&(i=0),void 0===s&&(s=!1),this.next=null,this.previous=null,this._destroyed=!1,this.fn=t,this.context=e,this.priority=i,this.once=s}return t.prototype.match=function(t,e){return void 0===e&&(e=null),this.fn===t&&this.context===e},t.prototype.emit=function(t){this.fn&&(this.context?this.fn.call(this.context,t):this.fn(t));var e=this.next;return this.once&&this.destroy(!0),this._destroyed&&(this.next=null),e},t.prototype.connect=function(t){this.previous=t,t.next&&(t.next.previous=this),this.next=t.next,t.next=this},t.prototype.destroy=function(t){void 0===t&&(t=!1),this._destroyed=!0,this.fn=null,this.context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);var e=this.next;return this.next=t?null:e,this.previous=null,e},t}(),n=function(){function t(){var t=this;this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new s(null,null,1/0),this.deltaMS=1/e.settings.TARGET_FPMS,this.elapsedMS=1/e.settings.TARGET_FPMS,this._tick=function(e){t._requestId=null,t.started&&(t.update(e),t.started&&null===t._requestId&&t._head.next&&(t._requestId=requestAnimationFrame(t._tick)))}}return t.prototype._requestIfNeeded=function(){null===this._requestId&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))},t.prototype._cancelIfNeeded=function(){null!==this._requestId&&(cancelAnimationFrame(this._requestId),this._requestId=null)},t.prototype._startIfPossible=function(){this.started?this._requestIfNeeded():this.autoStart&&this.start()},t.prototype.add=function(t,e,i){return void 0===i&&(i=exports.UPDATE_PRIORITY.NORMAL),this._addListener(new s(t,e,i))},t.prototype.addOnce=function(t,e,i){return void 0===i&&(i=exports.UPDATE_PRIORITY.NORMAL),this._addListener(new s(t,e,i,!0))},t.prototype._addListener=function(t){var e=this._head.next,i=this._head;if(e){for(;e;){if(t.priority>e.priority){t.connect(i);break}i=e,e=e.next}t.previous||t.connect(i)}else t.connect(i);return this._startIfPossible(),this},t.prototype.remove=function(t,e){for(var i=this._head.next;i;)i=i.match(t,e)?i.destroy():i.next;return this._head.next||this._cancelIfNeeded(),this},Object.defineProperty(t.prototype,"count",{get:function(){if(!this._head)return 0;for(var t=0,e=this._head;e=e.next;)t++;return t},enumerable:!1,configurable:!0}),t.prototype.start=function(){this.started||(this.started=!0,this._requestIfNeeded())},t.prototype.stop=function(){this.started&&(this.started=!1,this._cancelIfNeeded())},t.prototype.destroy=function(){if(!this._protected){this.stop();for(var t=this._head.next;t;)t=t.destroy(!0);this._head.destroy(),this._head=null}},t.prototype.update=function(t){var i;if(void 0===t&&(t=performance.now()),t>this.lastTime){if((i=this.elapsedMS=t-this.lastTime)>this._maxElapsedMS&&(i=this._maxElapsedMS),i*=this.speed,this._minElapsedMS){var s=t-this._lastFrame|0;if(s<this._minElapsedMS)return;this._lastFrame=t-s%this._minElapsedMS}this.deltaMS=i,this.deltaTime=this.deltaMS*e.settings.TARGET_FPMS;for(var n=this._head,r=n.next;r;)r=r.emit(this.deltaTime);n.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=t},Object.defineProperty(t.prototype,"FPS",{get:function(){return 1e3/this.elapsedMS},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"minFPS",{get:function(){return 1e3/this._maxElapsedMS},set:function(t){var i=Math.min(this.maxFPS,t),s=Math.min(Math.max(0,i)/1e3,e.settings.TARGET_FPMS);this._maxElapsedMS=1/s},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"maxFPS",{get:function(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0},set:function(t){if(0===t)this._minElapsedMS=0;else{var e=Math.max(this.minFPS,t);this._minElapsedMS=1/(e/1e3)}},enumerable:!1,configurable:!0}),Object.defineProperty(t,"shared",{get:function(){if(!t._shared){var e=t._shared=new t;e.autoStart=!0,e._protected=!0}return t._shared},enumerable:!1,configurable:!0}),Object.defineProperty(t,"system",{get:function(){if(!t._system){var e=t._system=new t;e.autoStart=!0,e._protected=!0}return t._system},enumerable:!1,configurable:!0}),t}(),r=function(){function t(){}return t.init=function(t){var e=this;t=Object.assign({autoStart:!0,sharedTicker:!1},t),Object.defineProperty(this,"ticker",{set:function(t){this._ticker&&this._ticker.remove(this.render,this),this._ticker=t,t&&t.add(this.render,this,exports.UPDATE_PRIORITY.LOW)},get:function(){return this._ticker}}),this.stop=function(){e._ticker.stop()},this.start=function(){e._ticker.start()},this._ticker=null,this.ticker=t.sharedTicker?n.shared:new n,t.autoStart&&this.start()},t.destroy=function(){if(this._ticker){var t=this._ticker;this.ticker=null,t.destroy()}},t.extension=i.ExtensionType.Application,t}();exports.Ticker=n,exports.TickerPlugin=r;
//# sourceMappingURL=ticker.min.js.map
