{"version": 3, "file": "compressed-textures.min.js", "sources": ["../../src/const.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/resources/BlobResource.ts", "../../src/resources/CompressedTextureResource.ts", "../../src/loaders/CompressedTextureLoader.ts", "../../src/loaders/registerCompressedTextures.ts", "../../src/parsers/parseDDS.ts", "../../src/parsers/parseKTX.ts", "../../src/loaders/DDSLoader.ts", "../../src/loaders/KTXLoader.ts"], "sourcesContent": ["/**\n * WebGL internal formats, including compressed texture formats provided by extensions\n * @memberof PIXI\n * @static\n * @name INTERNAL_FORMATS\n * @enum {number}\n * @property {number} [COMPRESSED_RGB_S3TC_DXT1_EXT=0x83F0] -\n * @property {number} [COMPRESSED_RGBA_S3TC_DXT1_EXT=0x83F1] -\n * @property {number} [COMPRESSED_RGBA_S3TC_DXT3_EXT=0x83F2] -\n * @property {number} [COMPRESSED_RGBA_S3TC_DXT5_EXT=0x83F3] -\n * @property {number} [COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917] -\n * @property {number} [COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918] -\n * @property {number} [COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919] -\n * @property {number} [COMPRESSED_SRGB_S3TC_DXT1_EXT=35916] -\n * @property {number} [COMPRESSED_R11_EAC=0x9270] -\n * @property {number} [COMPRESSED_SIGNED_R11_EAC=0x9271] -\n * @property {number} [COMPRESSED_RG11_EAC=0x9272] -\n * @property {number} [COMPRESSED_SIGNED_RG11_EAC=0x9273] -\n * @property {number} [COMPRESSED_RGB8_ETC2=0x9274] -\n * @property {number} [COMPRESSED_RGBA8_ETC2_EAC=0x9278] -\n * @property {number} [COMPRESSED_SRGB8_ETC2=0x9275] -\n * @property {number} [COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=0x9279] -\n * @property {number} [COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=0x9276] -\n * @property {number} [COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=0x9277] -\n * @property {number} [COMPRESSED_RGB_PVRTC_4BPPV1_IMG=0x8C00] -\n * @property {number} [COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=0x8C02] -\n * @property {number} [COMPRESSED_RGB_PVRTC_2BPPV1_IMG=0x8C01] -\n * @property {number} [COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=0x8C03] -\n * @property {number} [COMPRESSED_RGB_ETC1_WEBGL=0x8D64] -\n * @property {number} [COMPRESSED_RGB_ATC_WEBGL=0x8C92] -\n * @property {number} [COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=0x8C92] -\n * @property {number} [COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=0x87EE] -\n * @property {number} [COMPRESSED_RGBA_ASTC_4x4_KHR=0x93B0] -\n */\nexport enum INTERNAL_FORMATS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    // WEBGL_compressed_texture_s3tc\n    COMPRESSED_RGB_S3TC_DXT1_EXT = 0x83F0,\n    COMPRESSED_RGBA_S3TC_DXT1_EXT = 0x83F1,\n    COMPRESSED_RGBA_S3TC_DXT3_EXT = 0x83F2,\n    COMPRESSED_RGBA_S3TC_DXT5_EXT = 0x83F3,\n\n    // WEBGL_compressed_texture_s3tc_srgb\n    COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT = 35917,\n    COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT = 35918,\n    COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT = 35919,\n    COMPRESSED_SRGB_S3TC_DXT1_EXT = 35916,\n\n    // WEBGL_compressed_texture_etc\n    COMPRESSED_R11_EAC = 0x9270,\n    COMPRESSED_SIGNED_R11_EAC = 0x9271,\n    COMPRESSED_RG11_EAC = 0x9272,\n    COMPRESSED_SIGNED_RG11_EAC = 0x9273,\n    COMPRESSED_RGB8_ETC2 = 0x9274,\n    COMPRESSED_RGBA8_ETC2_EAC = 0x9278,\n    COMPRESSED_SRGB8_ETC2 = 0x9275,\n    COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = 0x9279,\n    COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9276,\n    COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9277,\n\n    // WEBGL_compressed_texture_pvrtc\n    COMPRESSED_RGB_PVRTC_4BPPV1_IMG = 0x8C00,\n    COMPRESSED_RGBA_PVRTC_4BPPV1_IMG = 0x8C02,\n    COMPRESSED_RGB_PVRTC_2BPPV1_IMG = 0x8C01,\n    COMPRESSED_RGBA_PVRTC_2BPPV1_IMG = 0x8C03,\n\n    // WEBGL_compressed_texture_etc1\n    COMPRESSED_RGB_ETC1_WEBGL = 0x8D64,\n\n    // WEBGL_compressed_texture_atc\n    COMPRESSED_RGB_ATC_WEBGL = 0x8C92,\n    COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL = 0x8C92, // TODO: Probably a bug on the MDN site\n    COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL = 0x87EE,\n\n    // WEBGL_compressed_texture_astc\n    /* eslint-disable-next-line camelcase */\n    COMPRESSED_RGBA_ASTC_4x4_KHR = 0x93B0,\n}\n\n/**\n * Maps the compressed texture formats in {@link PIXI.INTERNAL_FORMATS} to the number of bytes taken by\n * each texel.\n * @memberof PIXI\n * @static\n * @ignore\n */\nexport const INTERNAL_FORMAT_TO_BYTES_PER_PIXEL: { [id: number]: number } = {\n    // WEBGL_compressed_texture_s3tc\n    [INTERNAL_FORMATS.COMPRESSED_RGB_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT]: 1,\n\n    // WEBGL_compressed_texture_s3tc\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT]: 1,\n\n    // WEBGL_compressed_texture_etc\n    [INTERNAL_FORMATS.COMPRESSED_R11_EAC]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SIGNED_R11_EAC]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RG11_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_SIGNED_RG11_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGB8_ETC2]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA8_ETC2_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB8_ETC2]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2]: 0.5, // ~~\n    [INTERNAL_FORMATS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2]: 0.5, // ~~\n\n    // WEBGL_compressed_texture_pvrtc\n    [INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_2BPPV1_IMG]: 0.25,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG]: 0.25,\n\n    // WEBGL_compressed_texture_etc1\n    [INTERNAL_FORMATS.COMPRESSED_RGB_ETC1_WEBGL]: 0.5,\n\n    // @see https://www.khronos.org/registry/OpenGL/extensions/AMD/AMD_compressed_ATC_texture.txt\n    // WEBGL_compressed_texture_atc\n    [INTERNAL_FORMATS.COMPRESSED_RGB_ATC_WEBGL]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL]: 1,\n\n    // @see https://registry.khronos.org/OpenGL/extensions/KHR/KHR_texture_compression_astc_hdr.txt\n    // WEBGL_compressed_texture_astc\n    /* eslint-disable-next-line camelcase */\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_ASTC_4x4_KHR]: 1,\n};\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import type { Resource } from '@pixi/core';\nimport { ViewableBuffer, BufferResource } from '@pixi/core';\n\ninterface IBlobOptions\n{\n    autoLoad?: boolean;\n    width: number;\n    height: number;\n}\n\n/**\n * Resource that fetches texture data over the network and stores it in a buffer.\n * @class\n * @extends PIXI.Resource\n * @memberof PIXI\n */\nexport abstract class BlobResource extends BufferResource\n{\n    protected origin: string;\n    protected buffer: ViewableBuffer;\n    protected loaded: boolean;\n\n    /**\n     * @param {string} source - the URL of the texture file\n     * @param {PIXI.IBlobOptions} options\n     * @param {boolean}[options.autoLoad] - whether to fetch the data immediately;\n     *  you can fetch it later via {@link BlobResource#load}\n     * @param {boolean}[options.width] - the width in pixels.\n     * @param {boolean}[options.height] - the height in pixels.\n     */\n    constructor(source: string | Uint8Array | Uint32Array | Float32Array,\n        options: IBlobOptions = { width: 1, height: 1, autoLoad: true })\n    {\n        let origin: string;\n        let data: Uint8Array | Uint32Array | Float32Array;\n\n        if (typeof source === 'string')\n        {\n            origin = source;\n            data = new Uint8Array();\n        }\n        else\n        {\n            origin = null;\n            data = source;\n        }\n\n        super(data, options);\n\n        /**\n         * The URL of the texture file\n         * @member {string}\n         */\n        this.origin = origin;\n\n        /**\n         * The viewable buffer on the data\n         * @member {ViewableBuffer}\n         */\n        // HINT: BlobResource allows \"null\" sources, assuming the child class provides an alternative\n        this.buffer = data ? new ViewableBuffer(data) : null;\n\n        // Allow autoLoad = \"undefined\" still load the resource by default\n        if (this.origin && options.autoLoad !== false)\n        {\n            this.load();\n        }\n        if (data && data.length)\n        {\n            this.loaded = true;\n            this.onBlobLoaded(this.buffer.rawBinaryData);\n        }\n    }\n\n    protected onBlobLoaded(_data: ArrayBuffer): void\n    {\n        // TODO: Override this method\n    }\n\n    /** Loads the blob */\n    async load(): Promise<Resource>\n    {\n        const response = await fetch(this.origin);\n        const blob = await response.blob();\n        const arrayBuffer = await blob.arrayBuffer();\n\n        this.data = new Uint32Array(arrayBuffer);\n        this.buffer = new ViewableBuffer(arrayBuffer);\n        this.loaded = true;\n\n        this.onBlobLoaded(arrayBuffer);\n        this.update();\n\n        return this;\n    }\n}\n", "import { BlobResource } from './BlobResource';\nimport { INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from '../const';\nimport type { Renderer, BaseTexture, GLTexture } from '@pixi/core';\n\nimport type { INTERNAL_FORMATS } from '../const';\n\n/**\n * @ignore\n */\n// Used in PIXI.KTXLoader\nexport type CompressedLevelBuffer = {\n    levelID: number,\n    levelWidth: number,\n    levelHeight: number,\n    levelBuffer: Uint8Array\n};\n\n/**\n * @ignore\n */\nexport interface ICompressedTextureResourceOptions\n{\n    format: INTERNAL_FORMATS;\n    width: number;\n    height: number;\n    levels?: number;\n    levelBuffers?: CompressedLevelBuffer[];\n}\n\n/**\n * Resource for compressed texture formats, as follows: S3TC/DXTn (& their sRGB formats), ATC, ASTC, ETC 1/2, PVRTC.\n *\n * Compressed textures improve performance when rendering is texture-bound. The texture data stays compressed in\n * graphics memory, increasing memory locality and speeding up texture fetches. These formats can also be used to store\n * more detail in the same amount of memory.\n *\n * For most developers, container file formats are a better abstraction instead of directly handling raw texture\n * data. PixiJS provides native support for the following texture file formats (via {@link PIXI.Loader}):\n *\n * **.dds** - the DirectDraw Surface file format stores DXTn (DXT-1,3,5) data. See {@link PIXI.DDSLoader}\n * **.ktx** - the Khronos Texture Container file format supports storing all the supported WebGL compression formats.\n *  See {@link PIXI.KTXLoader}.\n * **.basis** - the BASIS supercompressed file format stores texture data in an internal format that is transcoded\n *  to the compression format supported on the device at _runtime_. It also supports transcoding into a uncompressed\n *  format as a fallback; you must install the `@pixi/basis-loader`, `@pixi/basis-transcoder` packages separately to\n *  use these files. See {@link PIXI.BasisLoader}.\n *\n * The loaders for the aforementioned formats use `CompressedTextureResource` internally. It is strongly suggested that\n * they be used instead.\n *\n * ## Working directly with CompressedTextureResource\n *\n * Since `CompressedTextureResource` inherits `BlobResource`, you can provide it a URL pointing to a file containing\n * the raw texture data (with no file headers!):\n *\n * ```js\n * // The resource backing the texture data for your textures.\n * // NOTE: You can also provide a ArrayBufferView instead of a URL. This is used when loading data from a container file\n * //   format such as KTX, DDS, or BASIS.\n * const compressedResource = new PIXI.CompressedTextureResource(\"bunny.dxt5\", {\n *   format: PIXI.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n *   width: 256,\n *   height: 256\n * });\n *\n * // You can create a base-texture to the cache, so that future `Texture`s can be created using the `Texture.from` API.\n * const baseTexture = new PIXI.BaseTexture(compressedResource, { pmaMode: PIXI.ALPHA_MODES.NPM });\n *\n * // Create a Texture to add to the TextureCache\n * const texture = new PIXI.Texture(baseTexture);\n *\n * // Add baseTexture & texture to the global texture cache\n * PIXI.BaseTexture.addToCache(baseTexture, \"bunny.dxt5\");\n * PIXI.Texture.addToCache(texture, \"bunny.dxt5\");\n * ```\n * @memberof PIXI\n */\nexport class CompressedTextureResource extends BlobResource\n{\n    /** The compression format */\n    public format: INTERNAL_FORMATS;\n    /**\n     * The number of mipmap levels stored in the resource buffer.\n     * @default 1\n     */\n    public levels: number;\n\n    // Easy access to the WebGL extension providing support for the compression format via ContextSystem\n    private _extension: 's3tc' | 's3tc_sRGB' | 'atc' | 'astc' | 'etc' | 'etc1' | 'pvrtc';\n    // Buffer views for each mipmap level in the main buffer\n    private _levelBuffers: CompressedLevelBuffer[];\n\n    /**\n     * @param source - the buffer/URL holding the compressed texture data\n     * @param options\n     * @param {PIXI.INTERNAL_FORMATS} options.format - the compression format\n     * @param {number} options.width - the image width in pixels.\n     * @param {number} options.height - the image height in pixels.\n     * @param {number} [options.level=1] - the mipmap levels stored in the compressed texture, including level 0.\n     * @param {number} [options.levelBuffers] - the buffers for each mipmap level. `CompressedTextureResource` can allows you\n     *      to pass `null` for `source`, for cases where each level is stored in non-contiguous memory.\n     */\n    constructor(source: string | Uint8Array | Uint32Array, options: ICompressedTextureResourceOptions)\n    {\n        super(source, options);\n\n        this.format = options.format;\n        this.levels = options.levels || 1;\n\n        this._width = options.width;\n        this._height = options.height;\n\n        this._extension = CompressedTextureResource._formatToExtension(this.format);\n\n        if (options.levelBuffers || this.buffer)\n        {\n            // ViewableBuffer doesn't support byteOffset :-( so allow source to be Uint8Array\n            this._levelBuffers = options.levelBuffers\n                || CompressedTextureResource._createLevelBuffers(\n                    source instanceof Uint8Array ? source : this.buffer.uint8View,\n                    this.format,\n                    this.levels,\n                    4, 4, // PVRTC has 8x4 blocks in 2bpp mode\n                    this.width,\n                    this.height);\n        }\n    }\n\n    /**\n     * @override\n     * @param renderer - A reference to the current renderer\n     * @param _texture - the texture\n     * @param _glTexture - texture instance for this webgl context\n     */\n    upload(renderer: Renderer, _texture: BaseTexture, _glTexture: GLTexture): boolean\n    {\n        const gl = renderer.gl;\n        const extension = renderer.context.extensions[this._extension];\n\n        if (!extension)\n        {\n            throw new Error(`${this._extension} textures are not supported on the current machine`);\n        }\n        if (!this._levelBuffers)\n        {\n            // Do not try to upload data before BlobResource loads, unless the levelBuffers were provided directly!\n            return false;\n        }\n\n        for (let i = 0, j = this.levels; i < j; i++)\n        {\n            const { levelID, levelWidth, levelHeight, levelBuffer } = this._levelBuffers[i];\n\n            gl.compressedTexImage2D(gl.TEXTURE_2D, levelID, this.format, levelWidth, levelHeight, 0, levelBuffer);\n        }\n\n        return true;\n    }\n\n    /** @protected */\n    protected onBlobLoaded(): void\n    {\n        this._levelBuffers = CompressedTextureResource._createLevelBuffers(\n            this.buffer.uint8View,\n            this.format,\n            this.levels,\n            4, 4, // PVRTC has 8x4 blocks in 2bpp mode\n            this.width,\n            this.height);\n    }\n\n    /**\n     * Returns the key (to ContextSystem#extensions) for the WebGL extension supporting the compression format\n     * @private\n     * @param format - the compression format to get the extension for.\n     */\n    private static _formatToExtension(format: INTERNAL_FORMATS):\n    's3tc' | 's3tc_sRGB' | 'atc' |\n    'astc' | 'etc' | 'etc1' | 'pvrtc'\n    {\n        if (format >= 0x83F0 && format <= 0x83F3)\n        {\n            return 's3tc';\n        }\n        else if (format >= 0x9270 && format <= 0x9279)\n        {\n            return 'etc';\n        }\n        else if (format >= 0x8C00 && format <= 0x8C03)\n        {\n            return 'pvrtc';\n        }\n        else if (format >= 0x8D64)\n        {\n            return 'etc1';\n        }\n        else if (format >= 0x8C92 && format <= 0x87EE)\n        {\n            return 'atc';\n        }\n\n        throw new Error('Invalid (compressed) texture format given!');\n    }\n\n    /**\n     * Pre-creates buffer views for each mipmap level\n     * @private\n     * @param buffer -\n     * @param format - compression formats\n     * @param levels - mipmap levels\n     * @param blockWidth -\n     * @param blockHeight -\n     * @param imageWidth - width of the image in pixels\n     * @param imageHeight - height of the image in pixels\n     */\n    private static _createLevelBuffers(\n        buffer: Uint8Array,\n        format: INTERNAL_FORMATS,\n        levels: number,\n        blockWidth: number,\n        blockHeight: number,\n        imageWidth: number,\n        imageHeight: number\n    ): CompressedLevelBuffer[]\n    {\n        // The byte-size of the first level buffer\n        const buffers = new Array<CompressedLevelBuffer>(levels);\n\n        let offset = buffer.byteOffset;\n\n        let levelWidth = imageWidth;\n        let levelHeight = imageHeight;\n        let alignedLevelWidth = (levelWidth + blockWidth - 1) & ~(blockWidth - 1);\n        let alignedLevelHeight = (levelHeight + blockHeight - 1) & ~(blockHeight - 1);\n\n        let levelSize = alignedLevelWidth * alignedLevelHeight * INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[format];\n\n        for (let i = 0; i < levels; i++)\n        {\n            buffers[i] = {\n                levelID: i,\n                levelWidth: levels > 1 ? levelWidth : alignedLevelWidth,\n                levelHeight: levels > 1 ? levelHeight : alignedLevelHeight,\n                levelBuffer: new Uint8Array(buffer.buffer, offset, levelSize)\n            };\n\n            offset += levelSize;\n\n            // Calculate levelBuffer dimensions for next iteration\n            levelWidth = (levelWidth >> 1) || 1;\n            levelHeight = (levelHeight >> 1) || 1;\n            alignedLevelWidth = (levelWidth + blockWidth - 1) & ~(blockWidth - 1);\n            alignedLevelHeight = (levelHeight + blockHeight - 1) & ~(blockHeight - 1);\n            levelSize = alignedLevelWidth * alignedLevelHeight * INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[format];\n        }\n\n        return buffers;\n    }\n}\n", "import { LoaderResource } from '@pixi/loaders';\nimport { url } from '@pixi/utils';\nimport { settings } from '@pixi/settings';\n\nimport type { Loader } from '@pixi/loaders';\nimport type { INTERNAL_FORMATS } from '../const';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * Schema for compressed-texture manifests\n * @ignore\n * @see PIXI.CompressedTextureLoader\n */\nexport type CompressedTextureManifest = {\n    textures: Array<{ src: string, format?: keyof INTERNAL_FORMATS}>,\n    cacheID: string;\n};\n\n// Missing typings? - https://github.com/microsoft/TypeScript/issues/39655\n/** Compressed texture extensions */\n/* eslint-disable camelcase */\nexport type CompressedTextureExtensions = {\n    s3tc?: WEBGL_compressed_texture_s3tc,\n    s3tc_sRGB: WEBGL_compressed_texture_s3tc_srgb,\n    etc: any,\n    etc1: any,\n    pvrtc: any,\n    atc: any,\n    astc: WEBGL_compressed_texture_astc\n};\nexport type CompressedTextureExtensionRef = keyof CompressedTextureExtensions;\n/* eslint-enable camelcase */\n\n/**\n * Loader plugin for handling compressed textures for all platforms.\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n */\nexport class CompressedTextureLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**  Map of available texture extensions. */\n    private static _textureExtensions: Partial<CompressedTextureExtensions>;\n\n    /** Map of available texture formats. */\n    private static _textureFormats: { [P in keyof INTERNAL_FORMATS]?: number };\n\n    /**\n     * Called after a compressed-textures manifest is loaded.\n     *\n     * This will then load the correct compression format for the device. Your manifest should adhere\n     * to the following schema:\n     *\n     * ```js\n     * import { INTERNAL_FORMATS } from '@pixi/constants';\n     *\n     * type CompressedTextureManifest = {\n     *  textures: Array<{ src: string, format?: keyof INTERNAL_FORMATS}>,\n     *  cacheID: string;\n     * };\n     * ```\n     *\n     * This is an example of a .json manifest file\n     *\n     * ```json\n     * {\n     *   \"cacheID\":\"asset\",\n     *   \"textures\":[\n     *     { \"src\":\"asset.fallback.png\" },\n     *     { \"format\":\"COMPRESSED_RGBA_S3TC_DXT5_EXT\", \"src\":\"asset.s3tc.ktx\" },\n     *     { \"format\":\"COMPRESSED_RGBA8_ETC2_EAC\", \"src\":\"asset.etc.ktx\" },\n     *     { \"format\":\"RGBA_PVRTC_4BPPV1_IMG\", \"src\":\"asset.pvrtc.ktx\" }\n     *   ]\n     * }\n     * ```\n     */\n    static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        const data: CompressedTextureManifest = resource.data;\n        const loader = this as unknown as Loader;\n\n        if (resource.type === LoaderResource.TYPE.JSON\n            && data\n            && data.cacheID\n            && data.textures)\n        {\n            const textures = data.textures;\n\n            let textureURL: string;\n            let fallbackURL: string;\n\n            // Search for an extension that holds one the formats\n            for (let i = 0, j = textures.length; i < j; i++)\n            {\n                const texture = textures[i];\n                const url = texture.src;\n                const format = texture.format;\n\n                if (!format)\n                {\n                    fallbackURL = url;\n                }\n                if (CompressedTextureLoader.textureFormats[format])\n                {\n                    textureURL = url;\n                    break;\n                }\n            }\n\n            textureURL = textureURL || fallbackURL;\n\n            // Make sure we have a URL\n            if (!textureURL)\n            {\n                next(new Error(`Cannot load compressed-textures in ${resource.url}, make sure you provide a fallback`));\n\n                return;\n            }\n            if (textureURL === resource.url)\n            {\n                // Prevent infinite loops\n                next(new Error('URL of compressed texture cannot be the same as the manifest\\'s URL'));\n\n                return;\n            }\n\n            const loadOptions = {\n                crossOrigin: resource.crossOrigin,\n                metadata: resource.metadata.imageMetadata,\n                parentResource: resource\n            };\n\n            const resourcePath = url.resolve(resource.url.replace(loader.baseUrl, ''), textureURL);\n            const resourceName = data.cacheID;\n\n            // The appropriate loader should register the texture\n            loader.add(resourceName, resourcePath, loadOptions, (res: LoaderResource) =>\n            {\n                if (res.error)\n                {\n                    next(res.error);\n\n                    return;\n                }\n\n                const { texture = null, textures = {} } = res;\n\n                // Make sure texture/textures is assigned to parent resource\n                Object.assign(resource, { texture, textures });\n\n                // Pass along any error\n                next();\n            });\n        }\n        else\n        {\n            next();\n        }\n    }\n\n    /**  Map of available texture extensions. */\n    public static get textureExtensions(): Partial<CompressedTextureExtensions>\n    {\n        if (!CompressedTextureLoader._textureExtensions)\n        {\n            // Auto-detect WebGL compressed-texture extensions\n            const canvas = settings.ADAPTER.createCanvas();\n            const gl = canvas.getContext('webgl');\n\n            if (!gl)\n            {\n                // #if _DEBUG\n                console.warn('WebGL not available for compressed textures. Silently failing.');\n                // #endif\n\n                return {};\n            }\n\n            const extensions = {\n                s3tc: gl.getExtension('WEBGL_compressed_texture_s3tc'),\n                s3tc_sRGB: gl.getExtension('WEBGL_compressed_texture_s3tc_srgb'), /* eslint-disable-line camelcase */\n                etc: gl.getExtension('WEBGL_compressed_texture_etc'),\n                etc1: gl.getExtension('WEBGL_compressed_texture_etc1'),\n                pvrtc: gl.getExtension('WEBGL_compressed_texture_pvrtc')\n                    || gl.getExtension('WEBKIT_WEBGL_compressed_texture_pvrtc'),\n                atc: gl.getExtension('WEBGL_compressed_texture_atc'),\n                astc: gl.getExtension('WEBGL_compressed_texture_astc')\n            };\n\n            CompressedTextureLoader._textureExtensions = extensions;\n        }\n\n        return CompressedTextureLoader._textureExtensions;\n    }\n\n    /** Map of available texture formats. */\n    public static get textureFormats(): { [P in keyof INTERNAL_FORMATS]?: number }\n    {\n        if (!CompressedTextureLoader._textureFormats)\n        {\n            const extensions = CompressedTextureLoader.textureExtensions;\n\n            CompressedTextureLoader._textureFormats = {};\n\n            // Assign all available compressed-texture formats\n            for (const extensionName in extensions)\n            {\n                const extension = extensions[extensionName as CompressedTextureExtensionRef];\n\n                if (!extension)\n                {\n                    continue;\n                }\n\n                Object.assign(\n                    CompressedTextureLoader._textureFormats,\n                    Object.getPrototypeOf(extension));\n            }\n        }\n\n        return CompressedTextureLoader._textureFormats;\n    }\n}\n", "import { MIPMAP_MODES, ALPHA_MODES } from '@pixi/constants';\nimport { BaseTexture, Texture } from '@pixi/core';\n\nimport type { LoaderResource, IResourceMetadata } from '@pixi/loaders';\nimport type { CompressedTextureResource } from '../resources/CompressedTextureResource';\n\n/**\n * Result when calling registerCompressedTextures.\n * @ignore\n */\ntype CompressedTexturesResult = Pick<LoaderResource, 'textures' | 'texture'>;\n\n/**\n * Creates base-textures and textures for each compressed-texture resource and adds them into the global\n * texture cache. The first texture has two IDs - `${url}`, `${url}-1`; while the rest have an ID of the\n * form `${url}-i`.\n * @param url - the original address of the resources\n * @param resources - the resources backing texture data\n * @ignore\n */\nexport function registerCompressedTextures(\n    url: string,\n    resources: CompressedTextureResource[],\n    metadata: IResourceMetadata\n): CompressedTexturesResult\n{\n    const result: CompressedTexturesResult = {\n        textures: {},\n        texture: null,\n    };\n\n    if (!resources)\n    {\n        return result;\n    }\n\n    const textures = resources.map((resource) =>\n        (\n            new Texture(new BaseTexture(resource, Object.assign({\n                mipmap: MIPMAP_MODES.OFF,\n                alphaMode: ALPHA_MODES.NO_PREMULTIPLIED_ALPHA\n            }, metadata)))\n        ));\n\n    textures.forEach((texture, i) =>\n    {\n        const { baseTexture } = texture;\n        const cacheID = `${url}-${i + 1}`;\n\n        BaseTexture.addToCache(baseTexture, cacheID);\n        Texture.addToCache(texture, cacheID);\n\n        if (i === 0)\n        {\n            BaseTexture.addToCache(baseTexture, url);\n            Texture.addToCache(texture, url);\n            result.texture = texture;\n        }\n\n        result.textures[cacheID] = texture;\n    });\n\n    return result;\n}\n", "import { CompressedTextureResource } from '../resources';\nimport { INTERNAL_FORMATS, INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from '../const';\n\nconst DDS_MAGIC_SIZE = 4;\nconst DDS_HEADER_SIZE = 124;\nconst DDS_HEADER_PF_SIZE = 32;\nconst DDS_HEADER_DX10_SIZE = 20;\n\n// DDS file format magic word\nconst DDS_MAGIC = 0x20534444;\n\n/**\n * DWORD offsets of the DDS file header fields (relative to file start).\n * @ignore\n */\nconst DDS_FIELDS = {\n    SIZE: 1,\n    FLAGS: 2,\n    HEIGHT: 3,\n    WIDTH: 4,\n    MIPMAP_COUNT: 7,\n    PIXEL_FORMAT: 19,\n};\n\n/**\n * DWORD offsets of the DDS PIXEL_FORMAT fields.\n * @ignore\n */\nconst DDS_PF_FIELDS = {\n    SIZE: 0,\n    FLAGS: 1,\n    FOURCC: 2,\n    RGB_BITCOUNT: 3,\n    R_BIT_MASK: 4,\n    G_BIT_MASK: 5,\n    B_BIT_MASK: 6,\n    A_BIT_MASK: 7\n};\n\n/**\n * DWORD offsets of the DDS_HEADER_DX10 fields.\n * @ignore\n */\nconst DDS_DX10_FIELDS = {\n    DXGI_FORMAT: 0,\n    RESOURCE_DIMENSION: 1,\n    MISC_FLAG: 2,\n    ARRAY_SIZE: 3,\n    MISC_FLAGS2: 4\n};\n\n/**\n * @see https://docs.microsoft.com/en-us/windows/win32/api/dxgiformat/ne-dxgiformat-dxgi_format\n * @ignore\n */\n// This is way over-blown for us! Lend us a hand, and remove the ones that aren't used (but set the remaining\n// ones to their correct value)\nenum DXGI_FORMAT\n    {\n    DXGI_FORMAT_UNKNOWN,\n    DXGI_FORMAT_R32G32B32A32_TYPELESS,\n    DXGI_FORMAT_R32G32B32A32_FLOAT,\n    DXGI_FORMAT_R32G32B32A32_UINT,\n    DXGI_FORMAT_R32G32B32A32_SINT,\n    DXGI_FORMAT_R32G32B32_TYPELESS,\n    DXGI_FORMAT_R32G32B32_FLOAT,\n    DXGI_FORMAT_R32G32B32_UINT,\n    DXGI_FORMAT_R32G32B32_SINT,\n    DXGI_FORMAT_R16G16B16A16_TYPELESS,\n    DXGI_FORMAT_R16G16B16A16_FLOAT,\n    DXGI_FORMAT_R16G16B16A16_UNORM,\n    DXGI_FORMAT_R16G16B16A16_UINT,\n    DXGI_FORMAT_R16G16B16A16_SNORM,\n    DXGI_FORMAT_R16G16B16A16_SINT,\n    DXGI_FORMAT_R32G32_TYPELESS,\n    DXGI_FORMAT_R32G32_FLOAT,\n    DXGI_FORMAT_R32G32_UINT,\n    DXGI_FORMAT_R32G32_SINT,\n    DXGI_FORMAT_R32G8X24_TYPELESS,\n    DXGI_FORMAT_D32_FLOAT_S8X24_UINT,\n    DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS,\n    DXGI_FORMAT_X32_TYPELESS_G8X24_UINT,\n    DXGI_FORMAT_R10G10B10A2_TYPELESS,\n    DXGI_FORMAT_R10G10B10A2_UNORM,\n    DXGI_FORMAT_R10G10B10A2_UINT,\n    DXGI_FORMAT_R11G11B10_FLOAT,\n    DXGI_FORMAT_R8G8B8A8_TYPELESS,\n    DXGI_FORMAT_R8G8B8A8_UNORM,\n    DXGI_FORMAT_R8G8B8A8_UNORM_SRGB,\n    DXGI_FORMAT_R8G8B8A8_UINT,\n    DXGI_FORMAT_R8G8B8A8_SNORM,\n    DXGI_FORMAT_R8G8B8A8_SINT,\n    DXGI_FORMAT_R16G16_TYPELESS,\n    DXGI_FORMAT_R16G16_FLOAT,\n    DXGI_FORMAT_R16G16_UNORM,\n    DXGI_FORMAT_R16G16_UINT,\n    DXGI_FORMAT_R16G16_SNORM,\n    DXGI_FORMAT_R16G16_SINT,\n    DXGI_FORMAT_R32_TYPELESS,\n    DXGI_FORMAT_D32_FLOAT,\n    DXGI_FORMAT_R32_FLOAT,\n    DXGI_FORMAT_R32_UINT,\n    DXGI_FORMAT_R32_SINT,\n    DXGI_FORMAT_R24G8_TYPELESS,\n    DXGI_FORMAT_D24_UNORM_S8_UINT,\n    DXGI_FORMAT_R24_UNORM_X8_TYPELESS,\n    DXGI_FORMAT_X24_TYPELESS_G8_UINT,\n    DXGI_FORMAT_R8G8_TYPELESS,\n    DXGI_FORMAT_R8G8_UNORM,\n    DXGI_FORMAT_R8G8_UINT,\n    DXGI_FORMAT_R8G8_SNORM,\n    DXGI_FORMAT_R8G8_SINT,\n    DXGI_FORMAT_R16_TYPELESS,\n    DXGI_FORMAT_R16_FLOAT,\n    DXGI_FORMAT_D16_UNORM,\n    DXGI_FORMAT_R16_UNORM,\n    DXGI_FORMAT_R16_UINT,\n    DXGI_FORMAT_R16_SNORM,\n    DXGI_FORMAT_R16_SINT,\n    DXGI_FORMAT_R8_TYPELESS,\n    DXGI_FORMAT_R8_UNORM,\n    DXGI_FORMAT_R8_UINT,\n    DXGI_FORMAT_R8_SNORM,\n    DXGI_FORMAT_R8_SINT,\n    DXGI_FORMAT_A8_UNORM,\n    DXGI_FORMAT_R1_UNORM,\n    DXGI_FORMAT_R9G9B9E5_SHAREDEXP,\n    DXGI_FORMAT_R8G8_B8G8_UNORM,\n    DXGI_FORMAT_G8R8_G8B8_UNORM,\n    DXGI_FORMAT_BC1_TYPELESS,\n    DXGI_FORMAT_BC1_UNORM,\n    DXGI_FORMAT_BC1_UNORM_SRGB,\n    DXGI_FORMAT_BC2_TYPELESS,\n    DXGI_FORMAT_BC2_UNORM,\n    DXGI_FORMAT_BC2_UNORM_SRGB,\n    DXGI_FORMAT_BC3_TYPELESS,\n    DXGI_FORMAT_BC3_UNORM,\n    DXGI_FORMAT_BC3_UNORM_SRGB,\n    DXGI_FORMAT_BC4_TYPELESS,\n    DXGI_FORMAT_BC4_UNORM,\n    DXGI_FORMAT_BC4_SNORM,\n    DXGI_FORMAT_BC5_TYPELESS,\n    DXGI_FORMAT_BC5_UNORM,\n    DXGI_FORMAT_BC5_SNORM,\n    DXGI_FORMAT_B5G6R5_UNORM,\n    DXGI_FORMAT_B5G5R5A1_UNORM,\n    DXGI_FORMAT_B8G8R8A8_UNORM,\n    DXGI_FORMAT_B8G8R8X8_UNORM,\n    DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM,\n    DXGI_FORMAT_B8G8R8A8_TYPELESS,\n    DXGI_FORMAT_B8G8R8A8_UNORM_SRGB,\n    DXGI_FORMAT_B8G8R8X8_TYPELESS,\n    DXGI_FORMAT_B8G8R8X8_UNORM_SRGB,\n    DXGI_FORMAT_BC6H_TYPELESS,\n    DXGI_FORMAT_BC6H_UF16,\n    DXGI_FORMAT_BC6H_SF16,\n    DXGI_FORMAT_BC7_TYPELESS,\n    DXGI_FORMAT_BC7_UNORM,\n    DXGI_FORMAT_BC7_UNORM_SRGB,\n    DXGI_FORMAT_AYUV,\n    DXGI_FORMAT_Y410,\n    DXGI_FORMAT_Y416,\n    DXGI_FORMAT_NV12,\n    DXGI_FORMAT_P010,\n    DXGI_FORMAT_P016,\n    DXGI_FORMAT_420_OPAQUE,\n    DXGI_FORMAT_YUY2,\n    DXGI_FORMAT_Y210,\n    DXGI_FORMAT_Y216,\n    DXGI_FORMAT_NV11,\n    DXGI_FORMAT_AI44,\n    DXGI_FORMAT_IA44,\n    DXGI_FORMAT_P8,\n    DXGI_FORMAT_A8P8,\n    DXGI_FORMAT_B4G4R4A4_UNORM,\n    DXGI_FORMAT_P208,\n    DXGI_FORMAT_V208,\n    DXGI_FORMAT_V408,\n    DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE,\n    DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE,\n    DXGI_FORMAT_FORCE_UINT\n}\n\n/**\n * Possible values of the field {@link DDS_DX10_FIELDS.RESOURCE_DIMENSION}\n * @ignore\n */\nenum D3D10_RESOURCE_DIMENSION\n    {\n    DDS_DIMENSION_TEXTURE1D = 2,\n    DDS_DIMENSION_TEXTURE2D = 3,\n    DDS_DIMENSION_TEXTURE3D = 6\n}\n\nconst PF_FLAGS = 1;\n\n// PIXEL_FORMAT flags\nconst DDPF_ALPHA = 0x2;\nconst DDPF_FOURCC = 0x4;\nconst DDPF_RGB = 0x40;\nconst DDPF_YUV = 0x200;\nconst DDPF_LUMINANCE = 0x20000;\n\n// Four character codes for DXTn formats\nconst FOURCC_DXT1 = 0x31545844;\nconst FOURCC_DXT3 = 0x33545844;\nconst FOURCC_DXT5 = 0x35545844;\nconst FOURCC_DX10 = 0x30315844;\n\n// Cubemap texture flag (for DDS_DX10_FIELDS.MISC_FLAG)\nconst DDS_RESOURCE_MISC_TEXTURECUBE = 0x4;\n\n/**\n * Maps `FOURCC_*` formats to internal formats (see {@link PIXI.INTERNAL_FORMATS}).\n * @ignore\n */\nconst FOURCC_TO_FORMAT: { [id: number]: number } = {\n    [FOURCC_DXT1]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n    [FOURCC_DXT3]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    [FOURCC_DXT5]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT\n};\n\n/**\n * Maps {@link DXGI_FORMAT} to types/internal-formats (see {@link PIXI.TYPES}, {@link PIXI.INTERNAL_FORMATS})\n * @ignore\n */\nconst DXGI_TO_FORMAT: { [id: number]: number } = {\n    // WEBGL_compressed_texture_s3tc\n    [DXGI_FORMAT.DXGI_FORMAT_BC1_TYPELESS]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC1_UNORM]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC2_TYPELESS]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC2_UNORM]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC3_TYPELESS]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC3_UNORM]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n\n    // WEBGL_compressed_texture_s3tc_srgb\n    [DXGI_FORMAT.DXGI_FORMAT_BC1_UNORM_SRGB]: INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC2_UNORM_SRGB]: INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC3_UNORM_SRGB]: INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT\n};\n\n/**\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n * @see https://docs.microsoft.com/en-us/windows/win32/direct3ddds/dx-graphics-dds-pguide\n */\n/**\n * Parses the DDS file header, generates base-textures, and puts them into the texture cache.\n * @param arrayBuffer\n */\nexport function parseDDS(arrayBuffer: ArrayBuffer): CompressedTextureResource[]\n{\n    const data = new Uint32Array(arrayBuffer);\n    const magicWord = data[0];\n\n    if (magicWord !== DDS_MAGIC)\n    {\n        throw new Error('Invalid DDS file magic word');\n    }\n\n    const header = new Uint32Array(arrayBuffer, 0, DDS_HEADER_SIZE / Uint32Array.BYTES_PER_ELEMENT);\n\n    // DDS header fields\n    const height = header[DDS_FIELDS.HEIGHT];\n    const width = header[DDS_FIELDS.WIDTH];\n    const mipmapCount = header[DDS_FIELDS.MIPMAP_COUNT];\n\n    // PIXEL_FORMAT fields\n    const pixelFormat = new Uint32Array(\n        arrayBuffer,\n        DDS_FIELDS.PIXEL_FORMAT * Uint32Array.BYTES_PER_ELEMENT,\n        DDS_HEADER_PF_SIZE / Uint32Array.BYTES_PER_ELEMENT);\n    const formatFlags = pixelFormat[PF_FLAGS];\n\n    // File contains compressed texture(s)\n    if (formatFlags & DDPF_FOURCC)\n    {\n        const fourCC = pixelFormat[DDS_PF_FIELDS.FOURCC];\n\n        // File contains one DXTn compressed texture\n        if (fourCC !== FOURCC_DX10)\n        {\n            const internalFormat = FOURCC_TO_FORMAT[fourCC];\n\n            const dataOffset = DDS_MAGIC_SIZE + DDS_HEADER_SIZE;\n            const texData = new Uint8Array(arrayBuffer, dataOffset);\n\n            const resource = new CompressedTextureResource(texData, {\n                format: internalFormat,\n                width,\n                height,\n                levels: mipmapCount // CompressedTextureResource will separate the levelBuffers for us!\n            });\n\n            return [resource];\n        }\n\n        // FOURCC_DX10 indicates there is a 20-byte DDS_HEADER_DX10 after DDS_HEADER\n        const dx10Offset = DDS_MAGIC_SIZE + DDS_HEADER_SIZE;\n        const dx10Header = new Uint32Array(\n            data.buffer,\n            dx10Offset,\n            DDS_HEADER_DX10_SIZE / Uint32Array.BYTES_PER_ELEMENT);\n        const dxgiFormat = dx10Header[DDS_DX10_FIELDS.DXGI_FORMAT];\n        const resourceDimension = dx10Header[DDS_DX10_FIELDS.RESOURCE_DIMENSION];\n        const miscFlag = dx10Header[DDS_DX10_FIELDS.MISC_FLAG];\n        const arraySize = dx10Header[DDS_DX10_FIELDS.ARRAY_SIZE];\n\n        // Map dxgiFormat to PIXI.INTERNAL_FORMATS\n        const internalFormat = DXGI_TO_FORMAT[dxgiFormat];\n\n        if (internalFormat === undefined)\n        {\n            throw new Error(`DDSParser cannot parse texture data with DXGI format ${dxgiFormat}`);\n        }\n        if (miscFlag === DDS_RESOURCE_MISC_TEXTURECUBE)\n        {\n            // FIXME: Anybody excited about cubemap compressed textures?\n            throw new Error('DDSParser does not support cubemap textures');\n        }\n        if (resourceDimension === D3D10_RESOURCE_DIMENSION.DDS_DIMENSION_TEXTURE3D)\n        {\n            // FIXME: Anybody excited about 3D compressed textures?\n            throw new Error('DDSParser does not supported 3D texture data');\n        }\n\n        // Uint8Array buffers of image data, including all mipmap levels in each image\n        const imageBuffers = new Array<Uint8Array>();\n        const dataOffset = DDS_MAGIC_SIZE\n                + DDS_HEADER_SIZE\n                + DDS_HEADER_DX10_SIZE;\n\n        if (arraySize === 1)\n        {\n            // No need bothering with the imageSize calculation!\n            imageBuffers.push(new Uint8Array(arrayBuffer, dataOffset));\n        }\n        else\n        {\n            // Calculate imageSize for each texture, and then locate each image's texture data\n\n            const pixelSize = INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[internalFormat];\n            let imageSize = 0;\n            let levelWidth = width;\n            let levelHeight = height;\n\n            for (let i = 0; i < mipmapCount; i++)\n            {\n                const alignedLevelWidth = Math.max(1, (levelWidth + 3) & ~3);\n                const alignedLevelHeight = Math.max(1, (levelHeight + 3) & ~3);\n\n                const levelSize = alignedLevelWidth * alignedLevelHeight * pixelSize;\n\n                imageSize += levelSize;\n\n                levelWidth = levelWidth >>> 1;\n                levelHeight = levelHeight >>> 1;\n            }\n\n            let imageOffset = dataOffset;\n\n            // NOTE: Cubemaps have 6-images per texture (but they aren't supported so ^_^)\n            for (let i = 0; i < arraySize; i++)\n            {\n                imageBuffers.push(new Uint8Array(arrayBuffer, imageOffset, imageSize));\n                imageOffset += imageSize;\n            }\n        }\n\n        // Uint8Array -> CompressedTextureResource, and we're done!\n        return imageBuffers.map((buffer) => new CompressedTextureResource(buffer, {\n            format: internalFormat,\n            width,\n            height,\n            levels: mipmapCount\n        }));\n    }\n    if (formatFlags & DDPF_RGB)\n    {\n        // FIXME: We might want to allow uncompressed *.dds files?\n        throw new Error('DDSParser does not support uncompressed texture data.');\n    }\n    if (formatFlags & DDPF_YUV)\n    {\n        // FIXME: Does anybody need this feature?\n        throw new Error('DDSParser does not supported YUV uncompressed texture data.');\n    }\n    if (formatFlags & DDPF_LUMINANCE)\n    {\n        // FIXME: Microsoft says older DDS filers use this feature! Probably not worth the effort!\n        throw new Error('DDSParser does not support single-channel (lumninance) texture data!');\n    }\n    if (formatFlags & DDPF_ALPHA)\n    {\n        // FIXME: I'm tired! See above =)\n        throw new Error('DDSParser does not support single-channel (alpha) texture data!');\n    }\n\n    throw new Error('DDSParser failed to load a texture file due to an unknown reason!');\n}\n\n", "import { FORMATS, TYPES } from '@pixi/constants';\nimport { BufferResource } from '@pixi/core';\n\nimport { INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from '../const';\nimport type { CompressedLevelBuffer } from '../resources';\nimport { CompressedTextureResource } from '../resources';\n\n/**\n * The 12-byte KTX file identifier\n * @see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/#2.1\n * @ignore\n */\nconst FILE_IDENTIFIER = [0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A];\n\n/**\n * The value stored in the \"endianness\" field.\n * @see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/#2.2\n * @ignore\n */\nconst ENDIANNESS = 0x04030201;\n\n/**\n * Byte offsets of the KTX file header fields\n * @ignore\n */\nconst KTX_FIELDS = {\n    FILE_IDENTIFIER: 0,\n    ENDIANNESS: 12,\n    GL_TYPE: 16,\n    GL_TYPE_SIZE: 20,\n    GL_FORMAT: 24,\n    GL_INTERNAL_FORMAT: 28,\n    GL_BASE_INTERNAL_FORMAT: 32,\n    PIXEL_WIDTH: 36,\n    PIXEL_HEIGHT: 40,\n    PIXEL_DEPTH: 44,\n    NUMBER_OF_ARRAY_ELEMENTS: 48,\n    NUMBER_OF_FACES: 52,\n    NUMBER_OF_MIPMAP_LEVELS: 56,\n    BYTES_OF_KEY_VALUE_DATA: 60\n};\n\n/**\n * Byte size of the file header fields in {@code KTX_FIELDS}\n * @ignore\n */\nconst FILE_HEADER_SIZE = 64;\n\n/**\n * Maps {@link PIXI.TYPES} to the bytes taken per component, excluding those ones that are bit-fields.\n * @ignore\n */\nexport const TYPES_TO_BYTES_PER_COMPONENT: { [id: number]: number } = {\n    [TYPES.UNSIGNED_BYTE]: 1,\n    [TYPES.UNSIGNED_SHORT]: 2,\n    [TYPES.INT]: 4,\n    [TYPES.UNSIGNED_INT]: 4,\n    [TYPES.FLOAT]: 4,\n    [TYPES.HALF_FLOAT]: 8\n};\n\n/**\n * Number of components in each {@link PIXI.FORMATS}\n * @ignore\n */\nexport const FORMATS_TO_COMPONENTS: { [id: number]: number } = {\n    [FORMATS.RGBA]: 4,\n    [FORMATS.RGB]: 3,\n    [FORMATS.RG]: 2,\n    [FORMATS.RED]: 1,\n    [FORMATS.LUMINANCE]: 1,\n    [FORMATS.LUMINANCE_ALPHA]: 2,\n    [FORMATS.ALPHA]: 1\n};\n\n/**\n * Number of bytes per pixel in bit-field types in {@link PIXI.TYPES}\n * @ignore\n */\nexport const TYPES_TO_BYTES_PER_PIXEL: { [id: number]: number } = {\n    [TYPES.UNSIGNED_SHORT_4_4_4_4]: 2,\n    [TYPES.UNSIGNED_SHORT_5_5_5_1]: 2,\n    [TYPES.UNSIGNED_SHORT_5_6_5]: 2\n};\n\nexport function parseKTX(url: string, arrayBuffer: ArrayBuffer, loadKeyValueData = false): {\n    compressed?: CompressedTextureResource[]\n    uncompressed?: { resource: BufferResource, type: TYPES, format: FORMATS }[]\n    kvData: Map<string, DataView> | null\n}\n{\n    const dataView = new DataView(arrayBuffer);\n\n    if (!validate(url, dataView))\n    {\n        return null;\n    }\n\n    const littleEndian = dataView.getUint32(KTX_FIELDS.ENDIANNESS, true) === ENDIANNESS;\n    const glType = dataView.getUint32(KTX_FIELDS.GL_TYPE, littleEndian);\n    // const glTypeSize = dataView.getUint32(KTX_FIELDS.GL_TYPE_SIZE, littleEndian);\n    const glFormat = dataView.getUint32(KTX_FIELDS.GL_FORMAT, littleEndian);\n    const glInternalFormat = dataView.getUint32(KTX_FIELDS.GL_INTERNAL_FORMAT, littleEndian);\n    const pixelWidth = dataView.getUint32(KTX_FIELDS.PIXEL_WIDTH, littleEndian);\n    const pixelHeight = dataView.getUint32(KTX_FIELDS.PIXEL_HEIGHT, littleEndian) || 1;// \"pixelHeight = 0\" -> \"1\"\n    const pixelDepth = dataView.getUint32(KTX_FIELDS.PIXEL_DEPTH, littleEndian) || 1;// ^^\n    const numberOfArrayElements = dataView.getUint32(KTX_FIELDS.NUMBER_OF_ARRAY_ELEMENTS, littleEndian) || 1;// ^^\n    const numberOfFaces = dataView.getUint32(KTX_FIELDS.NUMBER_OF_FACES, littleEndian);\n    const numberOfMipmapLevels = dataView.getUint32(KTX_FIELDS.NUMBER_OF_MIPMAP_LEVELS, littleEndian);\n    const bytesOfKeyValueData = dataView.getUint32(KTX_FIELDS.BYTES_OF_KEY_VALUE_DATA, littleEndian);\n\n    // Whether the platform architecture is little endian. If littleEndian !== platformLittleEndian, then the\n    // file contents must be endian-converted!\n    // TODO: Endianness conversion\n    // const platformLittleEndian = new Uint8Array((new Uint32Array([ENDIANNESS])).buffer)[0] === 0x01;\n\n    if (pixelHeight === 0 || pixelDepth !== 1)\n    {\n        throw new Error('Only 2D textures are supported');\n    }\n    if (numberOfFaces !== 1)\n    {\n        throw new Error('CubeTextures are not supported by KTXLoader yet!');\n    }\n    if (numberOfArrayElements !== 1)\n    {\n        // TODO: Support splitting array-textures into multiple BaseTextures\n        throw new Error('WebGL does not support array textures');\n    }\n\n    // TODO: 8x4 blocks for 2bpp pvrtc\n    const blockWidth = 4;\n    const blockHeight = 4;\n\n    const alignedWidth = (pixelWidth + 3) & ~3;\n    const alignedHeight = (pixelHeight + 3) & ~3;\n    const imageBuffers = new Array<CompressedLevelBuffer[]>(numberOfArrayElements);\n    let imagePixels = pixelWidth * pixelHeight;\n\n    if (glType === 0)\n    {\n        // Align to 16 pixels (4x4 blocks)\n        imagePixels = alignedWidth * alignedHeight;\n    }\n\n    let imagePixelByteSize: number;\n\n    if (glType !== 0)\n    {\n        // Uncompressed texture format\n        if (TYPES_TO_BYTES_PER_COMPONENT[glType])\n        {\n            imagePixelByteSize = TYPES_TO_BYTES_PER_COMPONENT[glType] * FORMATS_TO_COMPONENTS[glFormat];\n        }\n        else\n        {\n            imagePixelByteSize = TYPES_TO_BYTES_PER_PIXEL[glType];\n        }\n    }\n    else\n    {\n        imagePixelByteSize = INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[glInternalFormat];\n    }\n\n    if (imagePixelByteSize === undefined)\n    {\n        throw new Error('Unable to resolve the pixel format stored in the *.ktx file!');\n    }\n\n    const kvData: Map<string, DataView> | null = loadKeyValueData\n        ? parseKvData(dataView, bytesOfKeyValueData, littleEndian)\n        : null;\n\n    const imageByteSize = imagePixels * imagePixelByteSize;\n    let mipByteSize = imageByteSize;\n    let mipWidth = pixelWidth;\n    let mipHeight = pixelHeight;\n    let alignedMipWidth = alignedWidth;\n    let alignedMipHeight = alignedHeight;\n    let imageOffset = FILE_HEADER_SIZE + bytesOfKeyValueData;\n\n    for (let mipmapLevel = 0; mipmapLevel < numberOfMipmapLevels; mipmapLevel++)\n    {\n        const imageSize = dataView.getUint32(imageOffset, littleEndian);\n        let elementOffset = imageOffset + 4;\n\n        for (let arrayElement = 0; arrayElement < numberOfArrayElements; arrayElement++)\n        {\n            // TODO: Maybe support 3D textures? :-)\n            // for (let zSlice = 0; zSlice < pixelDepth; zSlice)\n\n            let mips = imageBuffers[arrayElement];\n\n            if (!mips)\n            {\n                mips = imageBuffers[arrayElement] = new Array(numberOfMipmapLevels);\n            }\n\n            mips[mipmapLevel] = {\n                levelID: mipmapLevel,\n\n                // don't align mipWidth when texture not compressed! (glType not zero)\n                levelWidth: numberOfMipmapLevels > 1 || glType !== 0 ? mipWidth : alignedMipWidth,\n                levelHeight: numberOfMipmapLevels > 1 || glType !== 0 ? mipHeight : alignedMipHeight,\n                levelBuffer: new Uint8Array(arrayBuffer, elementOffset, mipByteSize)\n            };\n            elementOffset += mipByteSize;\n        }\n\n        // HINT: Aligns to 4-byte boundary after jumping imageSize (in lieu of mipPadding)\n        imageOffset += imageSize + 4;// (+4 to jump the imageSize field itself)\n        imageOffset = imageOffset % 4 !== 0 ? imageOffset + 4 - (imageOffset % 4) : imageOffset;\n\n        // Calculate mipWidth, mipHeight for _next_ iteration\n        mipWidth = (mipWidth >> 1) || 1;\n        mipHeight = (mipHeight >> 1) || 1;\n        alignedMipWidth = (mipWidth + blockWidth - 1) & ~(blockWidth - 1);\n        alignedMipHeight = (mipHeight + blockHeight - 1) & ~(blockHeight - 1);\n\n        // Each mipmap level is 4-times smaller?\n        mipByteSize = alignedMipWidth * alignedMipHeight * imagePixelByteSize;\n    }\n\n    // We use the levelBuffers feature of CompressedTextureResource b/c texture data is image-major, not level-major.\n    if (glType !== 0)\n    {\n        return {\n            uncompressed: imageBuffers.map((levelBuffers) =>\n            {\n                let buffer: Float32Array | Uint32Array | Int32Array | Uint8Array = levelBuffers[0].levelBuffer;\n                let convertToInt = false;\n\n                if (glType === TYPES.FLOAT)\n                {\n                    buffer = new Float32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n                else if (glType === TYPES.UNSIGNED_INT)\n                {\n                    convertToInt = true;\n                    buffer = new Uint32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n                else if (glType === TYPES.INT)\n                {\n                    convertToInt = true;\n                    buffer = new Int32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n\n                return {\n                    resource: new BufferResource(\n                        buffer,\n                        {\n                            width: levelBuffers[0].levelWidth,\n                            height: levelBuffers[0].levelHeight,\n                        }\n                    ),\n                    type: glType,\n                    format: convertToInt ? convertFormatToInteger(glFormat) : glFormat,\n                };\n            }),\n            kvData\n        };\n    }\n\n    return {\n        compressed: imageBuffers.map((levelBuffers) => new CompressedTextureResource(null, {\n            format: glInternalFormat,\n            width: pixelWidth,\n            height: pixelHeight,\n            levels: numberOfMipmapLevels,\n            levelBuffers,\n        })),\n        kvData\n    };\n}\n\n/**\n * Checks whether the arrayBuffer contains a valid *.ktx file.\n * @param url\n * @param dataView\n */\nfunction validate(url: string, dataView: DataView): boolean\n{\n    // NOTE: Do not optimize this into 3 32-bit integer comparison because the endianness\n    // of the data is not specified.\n    for (let i = 0; i < FILE_IDENTIFIER.length; i++)\n    {\n        if (dataView.getUint8(i) !== FILE_IDENTIFIER[i])\n        {\n            // #if _DEBUG\n            console.error(`${url} is not a valid *.ktx file!`);\n            // #endif\n\n            return false;\n        }\n    }\n\n    return true;\n}\n\nfunction convertFormatToInteger(format: FORMATS)\n{\n    switch (format)\n    {\n        case FORMATS.RGBA: return FORMATS.RGBA_INTEGER;\n        case FORMATS.RGB: return FORMATS.RGB_INTEGER;\n        case FORMATS.RG: return FORMATS.RG_INTEGER;\n        case FORMATS.RED: return FORMATS.RED_INTEGER;\n        default: return format;\n    }\n}\n\nfunction parseKvData(dataView: DataView, bytesOfKeyValueData: number, littleEndian: boolean): Map<string, DataView>\n{\n    const kvData = new Map<string, DataView>();\n    let bytesIntoKeyValueData = 0;\n\n    while (bytesIntoKeyValueData < bytesOfKeyValueData)\n    {\n        const keyAndValueByteSize = dataView.getUint32(FILE_HEADER_SIZE + bytesIntoKeyValueData, littleEndian);\n        const keyAndValueByteOffset = FILE_HEADER_SIZE + bytesIntoKeyValueData + 4;\n        const valuePadding = 3 - ((keyAndValueByteSize + 3) % 4);\n\n        // Bounds check\n        if (keyAndValueByteSize === 0 || keyAndValueByteSize > bytesOfKeyValueData - bytesIntoKeyValueData)\n        {\n            console.error('KTXLoader: keyAndValueByteSize out of bounds');\n            break;\n        }\n\n        // Note: keyNulByte can't be 0 otherwise the key is an empty string.\n        let keyNulByte = 0;\n\n        for (; keyNulByte < keyAndValueByteSize; keyNulByte++)\n        {\n            if (dataView.getUint8(keyAndValueByteOffset + keyNulByte) === 0x00)\n            {\n                break;\n            }\n        }\n\n        if (keyNulByte === -1)\n        {\n            console.error('KTXLoader: Failed to find null byte terminating kvData key');\n            break;\n        }\n\n        const key = new TextDecoder().decode(\n            new Uint8Array(dataView.buffer, keyAndValueByteOffset, keyNulByte)\n        );\n        const value = new DataView(\n            dataView.buffer,\n            keyAndValueByteOffset + keyNulByte + 1,\n            keyAndValueByteSize - keyNulByte - 1,\n        );\n\n        kvData.set(key, value);\n\n        // 4 = the keyAndValueByteSize field itself\n        // keyAndValueByteSize = the bytes taken by the key and value\n        // valuePadding = extra padding to align with 4 bytes\n        bytesIntoKeyValueData += 4 + keyAndValueByteSize + valuePadding;\n    }\n\n    return kvData;\n}\n", "import { LoaderResource } from '@pixi/loaders';\nimport { registerCompressedTextures } from './registerCompressedTextures';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport { parseDDS } from '../parsers';\n\n// Set DDS files to be loaded as an ArrayBuffer\nLoaderResource.setExtensionXhrType('dds', LoaderResource.XHR_RESPONSE_TYPE.BUFFER);\n\n/**\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n * @see https://docs.microsoft.com/en-us/windows/win32/direct3ddds/dx-graphics-dds-pguide\n */\nexport class DDSLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * Registers a DDS compressed texture\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource - loader resource that is checked to see if it is a DDS file\n     * @param next - callback Function to call when done\n     */\n    public static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        if (resource.extension === 'dds' && resource.data)\n        {\n            try\n            {\n                Object.assign(resource, registerCompressedTextures(\n                    resource.name || resource.url,\n                    parseDDS(resource.data),\n                    resource.metadata,\n                ));\n            }\n            catch (err)\n            {\n                next(err);\n\n                return;\n            }\n        }\n\n        next();\n    }\n}\n", "import { ALP<PERSON>_MODES, MIPMAP_MODES } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { BaseTexture, ExtensionType, Texture } from '@pixi/core';\nimport { LoaderResource } from '@pixi/loaders';\nimport { registerCompressedTextures } from './registerCompressedTextures';\nimport { parseKTX } from '../parsers';\n\n// Set KTX files to be loaded as an ArrayBuffer\nLoaderResource.setExtensionXhrType('ktx', LoaderResource.XHR_RESPONSE_TYPE.BUFFER);\n\n/**\n * Loader plugin for handling KTX texture container files.\n *\n * This KTX loader does not currently support the following features:\n * * cube textures\n * * 3D textures\n * * endianness conversion for big-endian machines\n * * embedded *.basis files\n *\n * It does supports the following features:\n * * multiple textures per file\n * * mipmapping (only for compressed formats)\n * * vendor-specific key/value data parsing (enable {@link PIXI.KTXLoader.loadKeyValueData})\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n */\nexport class KTXLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * If set to `true`, {@link PIXI.KTXLoader} will parse key-value data in KTX textures. This feature relies\n     * on the [Encoding Standard]{@link https://encoding.spec.whatwg.org}.\n     *\n     * The key-value data will be available on the base-textures as {@code PIXI.BaseTexture.ktxKeyValueData}. They\n     * will hold a reference to the texture data buffer, so make sure to delete key-value data once you are done\n     * using it.\n     */\n    static loadKeyValueData = false;\n\n    /**\n     * Called after a KTX file is loaded.\n     *\n     * This will parse the KTX file header and add a {@code BaseTexture} to the texture\n     * cache.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource - loader resource that is checked to see if it is a KTX file\n     * @param next - callback Function to call when done\n     */\n    public static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        if (resource.extension === 'ktx' && resource.data)\n        {\n            try\n            {\n                const url = resource.name || resource.url;\n                const { compressed, uncompressed, kvData } = parseKTX(url, resource.data, this.loadKeyValueData);\n\n                if (compressed)\n                {\n                    const result = registerCompressedTextures(\n                        url,\n                        compressed,\n                        resource.metadata,\n                    );\n\n                    if (kvData && result.textures)\n                    {\n                        for (const textureId in result.textures)\n                        {\n                            result.textures[textureId].baseTexture.ktxKeyValueData = kvData;\n                        }\n                    }\n\n                    Object.assign(resource, result);\n                }\n                else if (uncompressed)\n                {\n                    const textures: Record<string, Texture> = {};\n\n                    uncompressed.forEach((image, i) =>\n                    {\n                        const texture = new Texture(new BaseTexture(\n                            image.resource,\n                            {\n                                mipmap: MIPMAP_MODES.OFF,\n                                alphaMode: ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,\n                                type: image.type,\n                                format: image.format,\n                            }\n                        ));\n                        const cacheID = `${url}-${i + 1}`;\n\n                        if (kvData) texture.baseTexture.ktxKeyValueData = kvData;\n\n                        BaseTexture.addToCache(texture.baseTexture, cacheID);\n                        Texture.addToCache(texture, cacheID);\n\n                        if (i === 0)\n                        {\n                            textures[url] = texture;\n                            BaseTexture.addToCache(texture.baseTexture, url);\n                            Texture.addToCache(texture, url);\n                        }\n\n                        textures[cacheID] = texture;\n                    });\n\n                    Object.assign(resource, { textures });\n                }\n            }\n            catch (err)\n            {\n                next(err);\n\n                return;\n            }\n        }\n\n        next();\n    }\n}\n"], "names": ["INTERNAL_FORMATS", "INTERNAL_FORMAT_TO_BYTES_PER_PIXEL", "_a", "COMPRESSED_RGB_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "COMPRESSED_SRGB_S3TC_DXT1_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT", "COMPRESSED_R11_EAC", "COMPRESSED_SIGNED_R11_EAC", "COMPRESSED_RG11_EAC", "COMPRESSED_SIGNED_RG11_EAC", "COMPRESSED_RGB8_ETC2", "COMPRESSED_RGBA8_ETC2_EAC", "COMPRESSED_SRGB8_ETC2", "COMPRESSED_SRGB8_ALPHA8_ETC2_EAC", "COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "COMPRESSED_RGB_PVRTC_2BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "COMPRESSED_RGB_ETC1_WEBGL", "COMPRESSED_RGB_ATC_WEBGL", "COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL", "COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL", "COMPRESSED_RGBA_ASTC_4x4_KHR", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "this", "constructor", "prototype", "create", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__generator", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "TypeError", "call", "pop", "length", "push", "BlobResource", "_super", "source", "options", "width", "height", "autoLoad", "origin", "data", "_this", "Uint8Array", "buffer", "ViewableBuffer", "load", "loaded", "onBlobLoaded", "rawBinaryData", "_data", "fetch", "blob", "arrayBuffer", "Uint32Array", "update", "BufferResource", "CompressedTextureResource", "format", "levels", "_width", "_height", "_extension", "_formatToExtension", "levelBuffers", "_levelBuffers", "_createLevelBuffers", "uint8View", "upload", "renderer", "_texture", "_glTexture", "gl", "context", "extensions", "Error", "i", "j", "levelID", "levelWidth", "levelHeight", "<PERSON><PERSON><PERSON><PERSON>", "compressedTexImage2D", "TEXTURE_2D", "blockWidth", "blockHeight", "imageWidth", "imageHeight", "buffers", "offset", "byteOffset", "alignedLevelWidth", "alignedLevelHeight", "levelSize", "CompressedTextureLoader", "use", "resource", "type", "LoaderResource", "TYPE", "JSON", "cacheID", "textures", "textureURL", "fallbackURL", "texture", "url_1", "src", "textureFormats", "url", "loadOptions", "crossOrigin", "metadata", "imageMetadata", "parentResource", "resourcePath", "replace", "baseUrl", "resourceName", "add", "res", "error", "_b", "assign", "defineProperty", "get", "_textureExtensions", "settings", "ADAPTER", "createCanvas", "getContext", "s3tc", "getExtension", "s3tc_sRGB", "etc", "etc1", "pvrtc", "atc", "astc", "_textureFormats", "textureExtensions", "extensionName", "extension", "getPrototypeOf", "ExtensionType", "Loader", "registerCompressedTextures", "resources", "map", "Texture", "BaseTexture", "mipmap", "MIPMAP_MODES", "OFF", "alphaMode", "ALPHA_MODES", "NO_PREMULTIPLIED_ALPHA", "for<PERSON>ach", "baseTexture", "addToCache", "DXGI_FORMAT", "D3D10_RESOURCE_DIMENSION", "DDS_FIELDS", "DDS_PF_FIELDS", "DDS_DX10_FIELDS", "FOURCC_TO_FORMAT", "DXGI_TO_FORMAT", "DXGI_FORMAT_BC1_TYPELESS", "DXGI_FORMAT_BC1_UNORM", "DXGI_FORMAT_BC2_TYPELESS", "DXGI_FORMAT_BC2_UNORM", "DXGI_FORMAT_BC3_TYPELESS", "DXGI_FORMAT_BC3_UNORM", "DXGI_FORMAT_BC1_UNORM_SRGB", "DXGI_FORMAT_BC2_UNORM_SRGB", "DXGI_FORMAT_BC3_UNORM_SRGB", "parseDDS", "header", "BYTES_PER_ELEMENT", "mipmapCount", "pixelFormat", "formatFlags", "fourCC", "internalFormat_1", "texData", "DDS_MAGIC_SIZE", "dx10Header", "dxgiFormat", "resourceDimension", "miscFlag", "arraySize", "internalFormat_2", "undefined", "DDS_DIMENSION_TEXTURE3D", "imageBuffers", "pixelSize", "imageSize", "Math", "max", "imageOffset", "FILE_IDENTIFIER", "KTX_FIELDS", "TYPES_TO_BYTES_PER_COMPONENT", "TYPES", "UNSIGNED_BYTE", "UNSIGNED_SHORT", "INT", "UNSIGNED_INT", "FLOAT", "HALF_FLOAT", "FORMATS_TO_COMPONENTS", "FORMATS", "RGBA", "RGB", "RG", "RED", "LUMINANCE", "LUMINANCE_ALPHA", "ALPHA", "TYPES_TO_BYTES_PER_PIXEL", "_c", "UNSIGNED_SHORT_4_4_4_4", "UNSIGNED_SHORT_5_5_5_1", "UNSIGNED_SHORT_5_6_5", "parseKTX", "loadKeyValueData", "dataView", "DataView", "getUint8", "validate", "littleEndian", "getUint32", "glType", "glFormat", "glInternalFormat", "pixelWidth", "pixelHeight", "pixelDepth", "numberOfArrayElements", "numberOfFaces", "numberOfMipmapLevels", "bytesOfKeyValueData", "imagePixelByteSize", "alignedWidth", "alignedHeight", "imagePixels", "kvData", "Map", "bytesIntoKeyValueData", "keyAndValueByteSize", "keyAndValueByteOffset", "valuePadding", "console", "keyNulByte", "key", "TextDecoder", "decode", "set", "parseKvData", "mipByteSize", "mip<PERSON><PERSON><PERSON>", "mipHeight", "alignedMipW<PERSON>th", "alignedMipHeight", "mipmapLevel", "elementOffset", "arrayElement", "mips", "uncompressed", "convertToInt", "Float32Array", "byteLength", "Int32Array", "convertFormatToInteger", "compressed", "RGBA_INTEGER", "RGB_INTEGER", "RG_INTEGER", "RED_INTEGER", "setExtensionXhrType", "XHR_RESPONSE_TYPE", "BUFFER", "DDSLoader", "name", "err", "KTXLoader", "kvData_1", "textureId", "ktxKeyValueData", "textures_1", "image"], "mappings": ";;;;;;;0EAkCYA,uIAAAA,QA4CXA,sBAAA,GA5CWA,EAAAA,2BAAAA,QAAAA,iBA4CX,KAxCGA,EAAA,6BAAA,OAAA,+BACAA,EAAAA,EAAA,8BAAA,OAAA,gCACAA,EAAAA,EAAA,8BAAA,OAAA,gCACAA,EAAAA,EAAA,8BAAA,OAAA,gCAGAA,EAAAA,EAAA,oCAAA,OAAA,sCACAA,EAAAA,EAAA,oCAAA,OAAA,sCACAA,EAAAA,EAAA,oCAAA,OAAA,sCACAA,EAAAA,EAAA,8BAAA,OAAA,gCAGAA,EAAAA,EAAA,mBAAA,OAAA,qBACAA,EAAAA,EAAA,0BAAA,OAAA,4BACAA,EAAAA,EAAA,oBAAA,OAAA,sBACAA,EAAAA,EAAA,2BAAA,OAAA,6BACAA,EAAAA,EAAA,qBAAA,OAAA,uBACAA,EAAAA,EAAA,0BAAA,OAAA,4BACAA,EAAAA,EAAA,sBAAA,OAAA,wBACAA,EAAAA,EAAA,iCAAA,OAAA,mCACAA,EAAAA,EAAA,yCAAA,OAAA,2CACAA,EAAAA,EAAA,0CAAA,OAAA,4CAGAA,EAAAA,EAAA,gCAAA,OAAA,kCACAA,EAAAA,EAAA,iCAAA,OAAA,mCACAA,EAAAA,EAAA,gCAAA,OAAA,kCACAA,EAAAA,EAAA,iCAAA,OAAA,mCAGAA,EAAAA,EAAA,0BAAA,OAAA,4BAGAA,EAAAA,EAAA,yBAAA,OAAA,2BACAA,EAAAA,EAAA,yCAAA,OAAA,2CACAA,EAAAA,EAAA,6CAAA,OAAA,+CAIAA,EAAAA,EAAA,6BAAA,OAAA,mCAUSC,IAAkCC,EAAA,IAE1CF,QAAAA,iBAAiBG,8BAA+B,GACjDD,EAACF,QAAAA,iBAAiBI,+BAAgC,GAClDF,EAACF,QAAAA,iBAAiBK,+BAAgC,EAClDH,EAACF,QAAAA,iBAAiBM,+BAAgC,EAGlDJ,EAACF,QAAAA,iBAAiBO,+BAAgC,GAClDL,EAACF,QAAAA,iBAAiBQ,qCAAsC,GACxDN,EAACF,QAAAA,iBAAiBS,qCAAsC,EACxDP,EAACF,QAAAA,iBAAiBU,qCAAsC,EAGxDR,EAACF,QAAAA,iBAAiBW,oBAAqB,GACvCT,EAACF,QAAAA,iBAAiBY,2BAA4B,GAC9CV,EAACF,QAAAA,iBAAiBa,qBAAsB,EACxCX,EAACF,QAAAA,iBAAiBc,4BAA6B,EAC/CZ,EAACF,QAAAA,iBAAiBe,sBAAuB,GACzCb,EAACF,QAAAA,iBAAiBgB,2BAA4B,EAC9Cd,EAACF,QAAAA,iBAAiBiB,uBAAwB,GAC1Cf,EAACF,QAAAA,iBAAiBkB,kCAAmC,EACrDhB,EAACF,QAAAA,iBAAiBmB,0CAA2C,GAC7DjB,EAACF,QAAAA,iBAAiBoB,2CAA4C,GAG9DlB,EAACF,QAAAA,iBAAiBqB,iCAAkC,GACpDnB,EAACF,QAAAA,iBAAiBsB,kCAAmC,GACrDpB,EAACF,QAAAA,iBAAiBuB,iCAAkC,IACpDrB,EAACF,QAAAA,iBAAiBwB,kCAAmC,IAGrDtB,EAACF,QAAAA,iBAAiByB,2BAA4B,GAI9CvB,EAACF,QAAAA,iBAAiB0B,0BAA2B,GAC7CxB,EAACF,QAAAA,iBAAiB2B,0CAA2C,EAC7DzB,EAACF,QAAAA,iBAAiB4B,8CAA+C,EAKjE1B,EAACF,QAAAA,iBAAiB6B,8BAA+B,KClHjDC,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,IAGrB,SAASO,EAAUR,EAAGC,GAEzB,SAASQ,IAAOC,KAAKC,YAAcX,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEY,UAAkB,OAANX,EAAaC,OAAOW,OAAOZ,IAAMQ,EAAGG,UAAYX,EAAEW,UAAW,IAAIH,GAyC5E,SAASK,EAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,IAAW,MAAOG,GAAKL,EAAOK,IACpF,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiB,MAAEK,IAAW,MAAOG,GAAKL,EAAOK,IACvF,SAASF,EAAKI,GAJlB,IAAeL,EAIaK,EAAOC,KAAOT,EAAQQ,EAAOL,QAJ1CA,EAIyDK,EAAOL,MAJhDA,aAAiBN,EAAIM,EAAQ,IAAIN,GAAE,SAAUG,GAAWA,EAAQG,OAITO,KAAKR,EAAWK,GAClGH,GAAMN,EAAYA,EAAUa,MAAMhB,EAASC,GAAc,KAAKS,WAI/D,SAASO,EAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGC,EAAGC,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPJ,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,IAAOK,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEZ,KAAMkB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BT,EAAES,OAAOC,UAAY,WAAa,OAAOrC,OAAU2B,EACvJ,SAASM,EAAKK,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIhB,EAAG,MAAM,IAAIiB,UAAU,mCAC3B,KAAOb,GAAG,IACN,GAAIJ,EAAI,EAAGC,IAAMC,EAAY,EAARc,EAAG,GAASf,EAAU,OAAIe,EAAG,GAAKf,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAEgB,KAAKjB,GAAI,GAAKA,EAAEV,SAAWW,EAAIA,EAAEgB,KAAKjB,EAAGe,EAAG,KAAKrB,KAAM,OAAOO,EAE3J,OADID,EAAI,EAAGC,IAAGc,EAAK,CAAS,EAARA,EAAG,GAAQd,EAAEb,QACzB2B,EAAG,IACP,KAAK,EAAG,KAAK,EAAGd,EAAIc,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAEhB,MAAO2B,EAAG,GAAIrB,MAAM,GAChD,KAAK,EAAGS,EAAEC,QAASJ,EAAIe,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAIW,MAAOf,EAAEG,KAAKY,MAAO,SACxC,QACI,KAAMjB,EAAIE,EAAEG,MAAML,EAAIA,EAAEkB,OAAS,GAAKlB,EAAEA,EAAEkB,OAAS,KAAkB,IAAVJ,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,SACjG,GAAc,IAAVY,EAAG,MAAcd,GAAMc,EAAG,GAAKd,EAAE,IAAMc,EAAG,GAAKd,EAAE,IAAM,CAAEE,EAAEC,MAAQW,EAAG,GAAI,MAC9E,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIA,EAAIc,EAAI,MAC7D,GAAId,GAAKE,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIE,EAAEI,IAAIa,KAAKL,GAAK,MACvDd,EAAE,IAAIE,EAAEI,IAAIW,MAChBf,EAAEG,KAAKY,MAAO,SAEtBH,EAAKjB,EAAKmB,KAAKrC,EAASuB,GAC1B,MAAOZ,GAAKwB,EAAK,CAAC,EAAGxB,GAAIS,EAAI,EAAI,QAAWD,EAAIE,EAAI,EACtD,GAAY,EAARc,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3B,MAAO2B,EAAG,GAAKA,EAAG,QAAK,EAAQrB,MAAM,GArB9BL,CAAK,CAACwB,EAAGC,MChE7D,QAAAO,EAAA,SAAAC,GAcI,SAAYD,EAAAE,EACRC,QAAA,IAAAA,IAAAA,EAAA,CAA0BC,MAAO,EAAGC,OAAQ,EAAGC,UAAU,IAD7D,IAGQC,EACAC,EAsCPC,EAAAvD,WApCyB,iBAAXgD,GAEPK,EAASL,EACTM,EAAO,IAAIE,aAIXH,EAAS,KACTC,EAAON,IAGXO,EAAAR,EAAML,KAAA1C,KAAAsD,EAAML,IAASjD,MAMhBqD,OAASA,EAOdE,EAAKE,OAASH,EAAO,IAAII,EAAAA,eAAeJ,GAAQ,KAG5CC,EAAKF,SAA+B,IAArBJ,EAAQG,UAEvBG,EAAKI,OAELL,GAAQA,EAAKV,SAEbW,EAAKK,QAAS,EACdL,EAAKM,aAAaN,EAAKE,OAAOK,kBAyB1C,OA/E2ChE,EAAcgD,EAAAC,GA0D3CD,EAAY5C,UAAA2D,aAAtB,SAAuBE,KAMjBjB,EAAA5C,UAAAyD,KAAN,gCAAclD,SAAO,4DAEA,KAAA,EAAA,MAAA,CAAA,EAAMuD,MAAMhE,KAAKqD,gBACrB,MAAA,CAAA,EADI5F,EAAwBqE,OACbmC,eACR,MAAA,CAAA,EADPxG,EAAqBqE,OACHoC,sBAS/B,OATMA,EAAczG,EAAwBqE,OAE5C9B,KAAKsD,KAAO,IAAIa,YAAYD,GAC5BlE,KAAKyD,OAAS,IAAIC,EAAcA,eAACQ,GACjClE,KAAK4D,QAAS,EAEd5D,KAAK6D,aAAaK,GAClBlE,KAAKoE,SAEL,CAAA,EAAOpE,cAEd8C,EA/ED,CAA2CuB,kBC6D3CC,EAAA,SAAAvB,GAyBI,SAAYuB,EAAAtB,EAA2CC,GAAvD,IAAAM,EAEIR,EAAML,KAAA1C,KAAAgD,EAAQC,IAsBjBjD,YApBGuD,EAAKgB,OAAStB,EAAQsB,OACtBhB,EAAKiB,OAASvB,EAAQuB,QAAU,EAEhCjB,EAAKkB,OAASxB,EAAQC,MACtBK,EAAKmB,QAAUzB,EAAQE,OAEvBI,EAAKoB,WAAaL,EAA0BM,mBAAmBrB,EAAKgB,SAEhEtB,EAAQ4B,cAAgBtB,EAAKE,UAG7BF,EAAKuB,cAAgB7B,EAAQ4B,cACtBP,EAA0BS,oBACzB/B,aAAkBQ,WAAaR,EAASO,EAAKE,OAAOuB,UACpDzB,EAAKgB,OACLhB,EAAKiB,OACL,EAAG,EACHjB,EAAKL,MACLK,EAAKJ,WAsIzB,OArL+CrD,EAAYwE,EAAAvB,GAyDvDuB,EAAApE,UAAA+E,OAAA,SAAOC,EAAoBC,EAAuBC,GAE9C,IAAMC,EAAKH,EAASG,GAGpB,IAFkBH,EAASI,QAAQC,WAAWvF,KAAK2E,YAI/C,MAAM,IAAIa,MAASxF,KAAK2E,WAAU,sDAEtC,IAAK3E,KAAK8E,cAGN,OAAO,EAGX,IAAK,IAAIW,EAAI,EAAGC,EAAI1F,KAAKwE,OAAQiB,EAAIC,EAAGD,IACxC,CACU,IAAAhI,EAAoDuC,KAAK8E,cAAcW,GAArEE,YAASC,eAAYC,gBAAaC,gBAE1CT,EAAGU,qBAAqBV,EAAGW,WAAYL,EAAS3F,KAAKuE,OAAQqB,EAAYC,EAAa,EAAGC,GAG7F,OAAO,GAIDxB,EAAApE,UAAA2D,aAAV,WAEI7D,KAAK8E,cAAgBR,EAA0BS,oBAC3C/E,KAAKyD,OAAOuB,UACZhF,KAAKuE,OACLvE,KAAKwE,OACL,EAAG,EACHxE,KAAKkD,MACLlD,KAAKmD,SAQEmB,EAAkBM,mBAAjC,SAAkCL,GAI9B,GAAIA,GAAU,OAAUA,GAAU,MAE9B,MAAO,OAEN,GAAIA,GAAU,OAAUA,GAAU,MAEnC,MAAO,MAEN,GAAIA,GAAU,OAAUA,GAAU,MAEnC,MAAO,QAEN,GAAIA,GAAU,MAEf,MAAO,OAEN,GAAIA,GAAU,OAAUA,GAAU,MAEnC,MAAO,MAGX,MAAM,IAAIiB,MAAM,+CAcLlB,EAAAS,oBAAf,SACItB,EACAc,EACAC,EACAyB,EACAC,EACAC,EACAC,GAeA,IAXA,IAAMC,EAAU,IAAI1G,MAA6B6E,GAE7C8B,EAAS7C,EAAO8C,WAEhBX,EAAaO,EACbN,EAAcO,EACdI,EAAqBZ,EAAaK,EAAa,IAAOA,EAAa,GACnEQ,EAAsBZ,EAAcK,EAAc,IAAOA,EAAc,GAEvEQ,EAAYF,EAAoBC,EAAqBjJ,EAAmC+G,GAEnFkB,EAAI,EAAGA,EAAIjB,EAAQiB,IAExBY,EAAQZ,GAAK,CACTE,QAASF,EACTG,WAAYpB,EAAS,EAAIoB,EAAaY,EACtCX,YAAarB,EAAS,EAAIqB,EAAcY,EACxCX,YAAa,IAAItC,WAAWC,EAAOA,OAAQ6C,EAAQI,IAGvDJ,GAAUI,EAOVA,GAFAF,GAFAZ,EAAcA,GAAc,GAAM,GAEAK,EAAa,IAAOA,EAAa,KACnEQ,GAFAZ,EAAeA,GAAe,GAAM,GAEAK,EAAc,IAAOA,EAAc,IAClB1I,EAAmC+G,GAG5F,OAAO8B,GAEd/B,EArLD,CAA+CxB,GCrC/C6D,EAAA,WAAA,SAAAA,KA0LA,OAlJWA,EAAAC,IAAP,SAAWC,EAA0B9F,GAEjC,IAAMuC,EAAkCuD,EAASvD,KAGjD,GAAIuD,EAASC,OAASC,EAAcA,eAACC,KAAKC,MACnC3D,GACAA,EAAK4D,SACL5D,EAAK6D,SACZ,CAOI,IANA,IAAMA,EAAW7D,EAAK6D,SAElBC,SACAC,SAGK5B,EAAI,EAAGC,EAAIyB,EAASvE,OAAQ6C,EAAIC,EAAGD,IAC5C,CACI,IAAM6B,EAAUH,EAAS1B,GACnB8B,EAAMD,EAAQE,IACdjD,EAAS+C,EAAQ/C,OAMvB,GAJKA,IAED8C,EAAcE,GAEdZ,EAAwBc,eAAelD,GAC3C,CACI6C,EAAaG,EACb,OAOR,KAHAH,EAAaA,GAAcC,GAOvB,YAFAtG,EAAK,IAAIyE,MAAM,sCAAsCqB,EAASa,IAAG,uCAIrE,GAAIN,IAAeP,EAASa,IAKxB,YAFA3G,EAAK,IAAIyE,MAAM,uEAKnB,IAAMmC,EAAc,CAChBC,YAAaf,EAASe,YACtBC,SAAUhB,EAASgB,SAASC,cAC5BC,eAAgBlB,GAGdmB,EAAeN,EAAAA,IAAIhH,QAAQmG,EAASa,IAAIO,QArDnCjI,KAqDkDkI,QAAS,IAAKd,GACrEe,EAAe7E,EAAK4D,QAtDflH,KAyDJoI,IAAID,EAAcH,EAAcL,GAAa,SAACU,GAEjD,GAAIA,EAAIC,MAEJvH,EAAKsH,EAAIC,WAFb,CAOQ,IAAA7K,EAAkC4K,EAAGf,QAArCA,aAAU,KAAI7J,EAAE8K,EAAkBF,EAALlB,SAAbA,OAAW,IAAAoB,EAAA,KAGnC/I,OAAOgJ,OAAO3B,EAAU,CAAES,QAAOA,EAAEH,SAAQA,IAG3CpG,aAKJA,KAKRvB,OAAAiJ,eAAkB9B,EAAiB,oBAAA,CAAnC+B,IAAA,WAEI,IAAK/B,EAAwBgC,mBAC7B,CAEI,IACMtD,EADSuD,EAAAA,SAASC,QAAQC,eACdC,WAAW,SAE7B,IAAK1D,EAMD,MAAO,GAGX,IAAME,EAAa,CACfyD,KAAM3D,EAAG4D,aAAa,iCACtBC,UAAW7D,EAAG4D,aAAa,sCAC3BE,IAAK9D,EAAG4D,aAAa,gCACrBG,KAAM/D,EAAG4D,aAAa,iCACtBI,MAAOhE,EAAG4D,aAAa,mCAChB5D,EAAG4D,aAAa,yCACvBK,IAAKjE,EAAG4D,aAAa,gCACrBM,KAAMlE,EAAG4D,aAAa,kCAG1BtC,EAAwBgC,mBAAqBpD,EAGjD,OAAOoB,EAAwBgC,oDAInCnJ,OAAAiJ,eAAkB9B,EAAc,iBAAA,CAAhC+B,IAAA,WAEI,IAAK/B,EAAwB6C,gBAC7B,CACI,IAAMjE,EAAaoB,EAAwB8C,kBAK3C,IAAK,IAAMC,KAHX/C,EAAwB6C,gBAAkB,GAGdjE,EAC5B,CACI,IAAMoE,EAAYpE,EAAWmE,GAExBC,GAKLnK,OAAOgJ,OACH7B,EAAwB6C,gBACxBhK,OAAOoK,eAAeD,KAIlC,OAAOhD,EAAwB6C,iDArL5B7C,EAAAgD,UAA+BE,EAAaA,cAACC,OAuLvDnD,cC9MeoD,EACZrC,EACAsC,EACAnC,GAGA,IAAM3G,EAAmC,CACrCiG,SAAU,GACVG,QAAS,MAGb,OAAK0C,GAKYA,EAAUC,KAAI,SAACpD,GAC5B,OACI,IAAIqD,EAAOA,QAAC,IAAIC,EAAAA,YAAYtD,EAAUrH,OAAOgJ,OAAO,CAChD4B,OAAQC,EAAYA,aAACC,IACrBC,UAAWC,EAAWA,YAACC,wBACxB5C,QAGF6C,SAAQ,SAACpD,EAAS7B,GAEf,IAAAkF,EAAgBrD,EAAOqD,YACzBzD,EAAaQ,EAAG,KAAIjC,EAAI,GAE9B0E,EAAAA,YAAYS,WAAWD,EAAazD,GACpCgD,EAAAA,QAAQU,WAAWtD,EAASJ,GAElB,IAANzB,IAEA0E,EAAAA,YAAYS,WAAWD,EAAajD,GACpCwC,EAAAA,QAAQU,WAAWtD,EAASI,GAC5BxG,EAAOoG,QAAUA,GAGrBpG,EAAOiG,SAASD,GAAWI,KAGxBpG,GA7BIA,EC9Bf,IAsDK2J,EAkIAC,EA5KCC,EAGM,EAHNA,EAIK,EAJLA,EAKY,EALZA,EAMY,GAOZC,EAGM,EAYNC,EACW,EADXA,EAEkB,EAFlBA,EAGS,EAHTA,EAIU,GAUhB,SAAKJ,GAEDA,EAAAA,EAAA,oBAAA,GAAA,sBACAA,EAAAA,EAAA,kCAAA,GAAA,oCACAA,EAAAA,EAAA,+BAAA,GAAA,iCACAA,EAAAA,EAAA,8BAAA,GAAA,gCACAA,EAAAA,EAAA,8BAAA,GAAA,gCACAA,EAAAA,EAAA,+BAAA,GAAA,iCACAA,EAAAA,EAAA,4BAAA,GAAA,8BACAA,EAAAA,EAAA,2BAAA,GAAA,6BACAA,EAAAA,EAAA,2BAAA,GAAA,6BACAA,EAAAA,EAAA,kCAAA,GAAA,oCACAA,EAAAA,EAAA,+BAAA,IAAA,iCACAA,EAAAA,EAAA,+BAAA,IAAA,iCACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,+BAAA,IAAA,iCACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,4BAAA,IAAA,8BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,wBAAA,IAAA,0BACAA,EAAAA,EAAA,wBAAA,IAAA,0BACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,iCAAA,IAAA,mCACAA,EAAAA,EAAA,qCAAA,IAAA,uCACAA,EAAAA,EAAA,oCAAA,IAAA,sCACAA,EAAAA,EAAA,iCAAA,IAAA,mCACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,6BAAA,IAAA,+BACAA,EAAAA,EAAA,4BAAA,IAAA,8BACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,gCAAA,IAAA,kCACAA,EAAAA,EAAA,0BAAA,IAAA,4BACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,0BAAA,IAAA,4BACAA,EAAAA,EAAA,4BAAA,IAAA,8BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,wBAAA,IAAA,0BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,wBAAA,IAAA,0BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,kCAAA,IAAA,oCACAA,EAAAA,EAAA,iCAAA,IAAA,mCACAA,EAAAA,EAAA,0BAAA,IAAA,4BACAA,EAAAA,EAAA,uBAAA,IAAA,yBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,uBAAA,IAAA,yBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,wBAAA,IAAA,0BACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,oBAAA,IAAA,sBACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,oBAAA,IAAA,sBACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,qBAAA,IAAA,uBACAA,EAAAA,EAAA,+BAAA,IAAA,iCACAA,EAAAA,EAAA,4BAAA,IAAA,8BACAA,EAAAA,EAAA,4BAAA,IAAA,8BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,uCAAA,IAAA,yCACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,gCAAA,IAAA,kCACAA,EAAAA,EAAA,8BAAA,IAAA,gCACAA,EAAAA,EAAA,gCAAA,IAAA,kCACAA,EAAAA,EAAA,0BAAA,IAAA,4BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,yBAAA,IAAA,2BACAA,EAAAA,EAAA,sBAAA,IAAA,wBACAA,EAAAA,EAAA,2BAAA,IAAA,6BACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,uBAAA,KAAA,yBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,eAAA,KAAA,iBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,2BAAA,KAAA,6BACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,iBAAA,KAAA,mBACAA,EAAAA,EAAA,4CAAA,KAAA,8CACAA,EAAAA,EAAA,oDAAA,KAAA,sDACAA,EAAAA,EAAA,uBAAA,KAAA,yBA3HJ,CAAKA,IAAAA,EA4HJ,KAMD,SAAKC,GAEDA,EAAAA,EAAA,wBAAA,GAAA,0BACAA,EAAAA,EAAA,wBAAA,GAAA,0BACAA,EAAAA,EAAA,wBAAA,GAAA,0BAJJ,CAAKA,IAAAA,EAKJ,KAED,UAsBMI,IAAgBzN,EAAA,IAZF,WAaDF,QAAAA,iBAAiBI,8BAChCF,EAbgB,WAaDF,QAAAA,iBAAiBK,8BAChCH,EAbgB,WAaDF,QAAAA,iBAAiBM,iCAO9BsN,IAAc5C,EAAA,IAEfsC,EAAYO,0BAA2B7N,QAAgBA,iBAACI,8BACzD4K,EAACsC,EAAYQ,uBAAwB9N,QAAgBA,iBAACI,8BACtD4K,EAACsC,EAAYS,0BAA2B/N,QAAgBA,iBAACK,8BACzD2K,EAACsC,EAAYU,uBAAwBhO,QAAgBA,iBAACK,8BACtD2K,EAACsC,EAAYW,0BAA2BjO,QAAgBA,iBAACM,8BACzD0K,EAACsC,EAAYY,uBAAwBlO,QAAgBA,iBAACM,8BAGtD0K,EAACsC,EAAYa,4BAA6BnO,QAAgBA,iBAACQ,oCAC3DwK,EAACsC,EAAYc,4BAA6BpO,QAAgBA,iBAACS,oCAC3DuK,EAACsC,EAAYe,4BAA6BrO,QAAgBA,iBAACU,uCAazD,SAAU4N,EAAS3H,GAErB,IAAMZ,EAAO,IAAIa,YAAYD,GAG7B,GAvPc,YAqPIZ,EAAK,GAInB,MAAM,IAAIkC,MAAM,+BAGpB,IAAMsG,EAAS,IAAI3H,YAAYD,EAAa,EAjQxB,IAiQ6CC,YAAY4H,mBAGvE5I,EAAS2I,EAAOf,GAChB7H,EAAQ4I,EAAOf,GACfiB,EAAcF,EAAOf,GAGrBkB,EAAc,IAAI9H,YACpBD,EACA6G,EAA0B5G,YAAY4H,kBA1QnB,GA2QE5H,YAAY4H,mBAC/BG,EAAcD,EA/EP,GAkFb,GA9EgB,EA8EZC,EACJ,CACI,IAAMC,EAASF,EAAYjB,GAG3B,GA1EY,YA0ERmB,EACJ,CACI,IAAMC,EAAiBlB,EAAiBiB,GAGlCE,EAAU,IAAI7I,WAAWU,EADZoI,KAUnB,MAAO,CAPU,IAAIhI,EAA0B+H,EAAS,CACpD9H,OAAQ6H,EACRlJ,MAAKA,EACLC,OAAMA,EACNqB,OAAQwH,KAOhB,IACMO,EAAa,IAAIpI,YACnBb,EAAKG,OAFU6I,IArSE,GAySMnI,YAAY4H,mBACjCS,EAAaD,EAAWtB,GACxBwB,EAAoBF,EAAWtB,GAC/ByB,EAAWH,EAAWtB,GACtB0B,EAAYJ,EAAWtB,GAGvB2B,EAAiBzB,EAAeqB,GAEtC,QAAuBK,IAAnBD,EAEA,MAAM,IAAIpH,MAAM,wDAAwDgH,GAE5E,GA1G8B,IA0G1BE,EAGA,MAAM,IAAIlH,MAAM,+CAEpB,GAAIiH,IAAsB3B,EAAyBgC,wBAG/C,MAAM,IAAItH,MAAM,gDAIpB,IAAMuH,EAAe,IAAIpN,MAKzB,GAAkB,IAAdgN,EAGAI,EAAalK,KAAK,IAAIW,WAAWU,EAPlBoI,UAUnB,CAQI,IALA,IAAMU,EAAYxP,EAAmCoP,GACjDK,EAAY,EACZrH,EAAa1C,EACb2C,EAAc1C,EAETsC,EAAI,EAAGA,EAAIuG,EAAavG,IACjC,CAMIwH,GAL0BC,KAAKC,IAAI,EAAIvH,EAAa,GAAK,GAC9BsH,KAAKC,IAAI,EAAItH,EAAc,GAAK,GAEAmH,EAI3DpH,KAA4B,EAC5BC,KAA8B,EAGlC,IAAIuH,EA/BWd,IAkCf,IAAS7G,EAAI,EAAGA,EAAIkH,EAAWlH,IAE3BsH,EAAalK,KAAK,IAAIW,WAAWU,EAAakJ,EAAaH,IAC3DG,GAAeH,EAKvB,OAAOF,EAAa9C,KAAI,SAACxG,GAAW,OAAA,IAAIa,EAA0Bb,EAAQ,CACtEc,OAAQqI,EACR1J,MAAKA,EACLC,OAAMA,EACNqB,OAAQwH,OAGhB,GAnLa,GAmLTE,EAGA,MAAM,IAAI1G,MAAM,yDAEpB,GAvLa,IAuLT0G,EAGA,MAAM,IAAI1G,MAAM,+DAEpB,GA3LmB,OA2Lf0G,EAGA,MAAM,IAAI1G,MAAM,wEAEpB,GApMe,EAoMX0G,EAGA,MAAM,IAAI1G,MAAM,mEAGpB,MAAM,IAAIA,MAAM,qECnYpB,IAAM6H,EAAkB,CAAC,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,GAAM,GAAM,GAAM,IAarFC,EAEU,GAFVA,EAGO,GAHPA,EAKS,GALTA,EAMkB,GANlBA,EAQW,GARXA,EASY,GATZA,EAUW,GAVXA,EAWwB,GAXxBA,EAYe,GAZfA,EAauB,GAbvBA,EAcuB,GAahBC,IAA4B9P,EAAA,IACpC+P,EAAAA,MAAMC,eAAgB,EACvBhQ,EAAC+P,EAAAA,MAAME,gBAAiB,EACxBjQ,EAAC+P,EAAAA,MAAMG,KAAM,EACblQ,EAAC+P,EAAAA,MAAMI,cAAe,EACtBnQ,EAAC+P,EAAAA,MAAMK,OAAQ,EACfpQ,EAAC+P,EAAAA,MAAMM,YAAa,KAOXC,IAAqBxF,EAAA,IAC7ByF,EAAAA,QAAQC,MAAO,EAChB1F,EAACyF,EAAAA,QAAQE,KAAM,EACf3F,EAACyF,EAAAA,QAAQG,IAAK,EACd5F,EAACyF,EAAAA,QAAQI,KAAM,EACf7F,EAACyF,EAAAA,QAAQK,WAAY,EACrB9F,EAACyF,EAAAA,QAAQM,iBAAkB,EAC3B/F,EAACyF,EAAAA,QAAQO,OAAQ,KAORC,IAAwBC,EAAA,IAChCjB,EAAAA,MAAMkB,wBAAyB,EAChCD,EAACjB,EAAAA,MAAMmB,wBAAyB,EAChCF,EAACjB,EAAAA,MAAMoB,sBAAuB,cAGlBC,EAASnH,EAAaxD,EAA0B4K,QAAA,IAAAA,IAAAA,GAAwB,GAMpF,IAAMC,EAAW,IAAIC,SAAS9K,GAE9B,IAoMJ,SAAkBwD,EAAaqH,GAI3B,IAAK,IAAItJ,EAAI,EAAGA,EAAI4H,EAAgBzK,OAAQ6C,IAExC,GAAIsJ,EAASE,SAASxJ,KAAO4H,EAAgB5H,GAMzC,OAAO,EAIf,OAAO,EApNFyJ,CAASxH,EAAKqH,GAEf,OAAO,KAGX,IAAMI,EA/ES,WA+EMJ,EAASK,UAAU9B,GAAuB,GACzD+B,EAASN,EAASK,UAAU9B,EAAoB6B,GAEhDG,EAAWP,EAASK,UAAU9B,EAAsB6B,GACpDI,EAAmBR,EAASK,UAAU9B,EAA+B6B,GACrEK,EAAaT,EAASK,UAAU9B,EAAwB6B,GACxDM,EAAcV,EAASK,UAAU9B,EAAyB6B,IAAiB,EAC3EO,EAAaX,EAASK,UAAU9B,EAAwB6B,IAAiB,EACzEQ,EAAwBZ,EAASK,UAAU9B,EAAqC6B,IAAiB,EACjGS,EAAgBb,EAASK,UAAU9B,EAA4B6B,GAC/DU,EAAuBd,EAASK,UAAU9B,EAAoC6B,GAC9EW,EAAsBf,EAASK,UAAU9B,EAAoC6B,GAOnF,GAAoB,IAAhBM,GAAoC,IAAfC,EAErB,MAAM,IAAIlK,MAAM,kCAEpB,GAAsB,IAAlBoK,EAEA,MAAM,IAAIpK,MAAM,oDAEpB,GAA8B,IAA1BmK,EAGA,MAAM,IAAInK,MAAM,yCAIpB,IAcIuK,EAXEC,EAAgBR,EAAa,GAAK,EAClCS,EAAiBR,EAAc,GAAK,EACpC1C,EAAe,IAAIpN,MAA+BgQ,GACpDO,EAAcV,EAAaC,EA2B/B,GAzBe,IAAXJ,IAGAa,EAAcF,EAAeC,QAsBNpD,KAZnBkD,EALO,IAAXV,EAGI9B,EAA6B8B,GAER9B,EAA6B8B,GAAUtB,EAAsBuB,GAI7Dd,EAAyBa,GAK7B7R,EAAmC+R,IAKxD,MAAM,IAAI/J,MAAM,gEAepB,IAZA,IAAM2K,EAAuCrB,EAuJjD,SAAqBC,EAAoBe,EAA6BX,GAElE,IAAMgB,EAAS,IAAIC,IACfC,EAAwB,EAE5B,KAAOA,EAAwBP,GAC/B,CACI,IAAMQ,EAAsBvB,EAASK,UAzRpB,GAyRiDiB,EAAuBlB,GACnFoB,EA1RW,GA0RgCF,EAAwB,EACnEG,EAAe,GAAMF,EAAsB,GAAK,EAGtD,GAA4B,IAAxBA,GAA6BA,EAAsBR,EAAsBO,EAC7E,CACII,QAAQnI,MAAM,gDACd,MAMJ,IAFA,IAAIoI,EAAa,EAEVA,EAAaJ,GAE8C,IAA1DvB,EAASE,SAASsB,EAAwBG,GAFTA,KAQzC,IAAoB,IAAhBA,EACJ,CACID,QAAQnI,MAAM,8DACd,MAGJ,IAAMqI,GAAM,IAAIC,aAAcC,OAC1B,IAAIrN,WAAWuL,EAAStL,OAAQ8M,EAAuBG,IAErD7P,EAAQ,IAAImO,SACdD,EAAStL,OACT8M,EAAwBG,EAAa,EACrCJ,EAAsBI,EAAa,GAGvCP,EAAOW,IAAIH,EAAK9P,GAKhBwP,GAAyB,EAAIC,EAAsBE,EAGvD,OAAOL,EA1MDY,CAAYhC,EAAUe,EAAqBX,GAC3C,KAGF6B,EADkBd,EAAcH,EAEhCkB,EAAWzB,EACX0B,EAAYzB,EACZ0B,EAAkBnB,EAClBoB,EAAmBnB,EACnB7C,EArIiB,GAqIgB0C,EAE5BuB,EAAc,EAAGA,EAAcxB,EAAsBwB,IAC9D,CAII,IAHA,IAAMpE,EAAY8B,EAASK,UAAUhC,EAAa+B,GAC9CmC,EAAgBlE,EAAc,EAEzBmE,EAAe,EAAGA,EAAe5B,EAAuB4B,IACjE,CAII,IAAIC,EAAOzE,EAAawE,GAEnBC,IAEDA,EAAOzE,EAAawE,GAAgB,IAAI5R,MAAMkQ,IAGlD2B,EAAKH,GAAe,CAChB1L,QAAS0L,EAGTzL,WAAYiK,EAAuB,GAAgB,IAAXR,EAAe4B,EAAWE,EAClEtL,YAAagK,EAAuB,GAAgB,IAAXR,EAAe6B,EAAYE,EACpEtL,YAAa,IAAItC,WAAWU,EAAaoN,EAAeN,IAE5DM,GAAiBN,EAKrB5D,GADAA,GAAeH,EAAY,GACC,GAAM,EAAIG,EAAc,EAAKA,EAAc,EAAKA,EAS5E4D,GAJAG,GAFAF,EAAYA,GAAY,GAAM,GAnFf,EAqF4B,GAAK,IAChDG,GAFAF,EAAaA,GAAa,GAAM,GAnFhB,EAqF8B,GAAK,GAGAnB,EAIvD,OAAe,IAAXV,EAEO,CACHoC,aAAc1E,EAAa9C,KAAI,SAACpF,GAE5B,IAAIpB,EAA+DoB,EAAa,GAAGiB,YAC/E4L,GAAe,EA0BnB,OAxBIrC,IAAW7B,EAAKA,MAACK,MAEjBpK,EAAS,IAAIkO,aACT9M,EAAa,GAAGiB,YAAYrC,OAC5BoB,EAAa,GAAGiB,YAAYS,WAC5B1B,EAAa,GAAGiB,YAAY8L,WAAa,GAExCvC,IAAW7B,EAAKA,MAACI,cAEtB8D,GAAe,EACfjO,EAAS,IAAIU,YACTU,EAAa,GAAGiB,YAAYrC,OAC5BoB,EAAa,GAAGiB,YAAYS,WAC5B1B,EAAa,GAAGiB,YAAY8L,WAAa,IAExCvC,IAAW7B,EAAKA,MAACG,MAEtB+D,GAAe,EACfjO,EAAS,IAAIoO,WACThN,EAAa,GAAGiB,YAAYrC,OAC5BoB,EAAa,GAAGiB,YAAYS,WAC5B1B,EAAa,GAAGiB,YAAY8L,WAAa,IAG1C,CACH/K,SAAU,IAAIxC,EAAcA,eACxBZ,EACA,CACIP,MAAO2B,EAAa,GAAGe,WACvBzC,OAAQ0B,EAAa,GAAGgB,cAGhCiB,KAAMuI,EACN9K,OAAQmN,EAAeI,EAAuBxC,GAAYA,MAGlEa,OAAMA,GAIP,CACH4B,WAAYhF,EAAa9C,KAAI,SAACpF,GAAiB,OAAA,IAAIP,EAA0B,KAAM,CAC/EC,OAAQgL,EACRrM,MAAOsM,EACPrM,OAAQsM,EACRjL,OAAQqL,EACRhL,aAAYA,OAEhBsL,OAAMA,GA4Bd,SAAS2B,EAAuBvN,GAE5B,OAAQA,GAEJ,KAAKyJ,UAAQC,KAAM,OAAOD,EAAAA,QAAQgE,aAClC,KAAKhE,UAAQE,IAAK,OAAOF,EAAAA,QAAQiE,YACjC,KAAKjE,UAAQG,GAAI,OAAOH,EAAAA,QAAQkE,WAChC,KAAKlE,UAAQI,IAAK,OAAOJ,EAAAA,QAAQmE,YACjC,QAAS,OAAO5N,GCrTxBwC,EAAcA,eAACqL,oBAAoB,MAAOrL,EAAcA,eAACsL,kBAAkBC,QAQ3E,IAAAC,EAAA,WAAA,SAAAA,KAiCA,OAtBkBA,EAAA3L,IAAd,SAAkBC,EAA0B9F,GAExC,GAA2B,QAAvB8F,EAAS8C,WAAuB9C,EAASvD,KAEzC,IAEI9D,OAAOgJ,OAAO3B,EAAUkD,EACpBlD,EAAS2L,MAAQ3L,EAASa,IAC1BmE,EAAShF,EAASvD,MAClBuD,EAASgB,WAGjB,MAAO4K,GAIH,YAFA1R,EAAK0R,GAMb1R,KA5BGwR,EAAA5I,UAA+BE,EAAaA,cAACC,OA8BvDyI,KCxCDxL,EAAcA,eAACqL,oBAAoB,MAAOrL,EAAcA,eAACsL,kBAAkBC,QAmB3E,IAAAI,EAAA,WAAA,SAAAA,KAgGA,OAxEkBA,EAAA9L,IAAd,SAAkBC,EAA0B9F,GAExC,GAA2B,QAAvB8F,EAAS8C,WAAuB9C,EAASvD,KAEzC,IAEI,IAAMiE,EAAMV,EAAS2L,MAAQ3L,EAASa,IAChCjK,EAAuCoR,EAAStH,EAAKV,EAASvD,KAAMtD,KAAK8O,kBAAvEiD,EAAUtU,EAAAsU,WAAEN,EAAYhU,EAAAgU,aAAEkB,EAAMlV,EAAA0S,OAExC,GAAI4B,EACJ,CACI,IAAM7Q,EAAS6I,EACXxC,EACAwK,EACAlL,EAASgB,UAGb,GAAI8K,GAAUzR,EAAOiG,SAEjB,IAAK,IAAMyL,KAAa1R,EAAOiG,SAE3BjG,EAAOiG,SAASyL,GAAWjI,YAAYkI,gBAAkBF,EAIjEnT,OAAOgJ,OAAO3B,EAAU3F,QAEvB,GAAIuQ,EACT,CACI,IAAMqB,EAAoC,GAE1CrB,EAAa/G,SAAQ,SAACqI,EAAOtN,GAEzB,IAAM6B,EAAU,IAAI4C,EAAOA,QAAC,IAAIC,EAAAA,YAC5B4I,EAAMlM,SACN,CACIuD,OAAQC,EAAYA,aAACC,IACrBC,UAAWC,EAAWA,YAACC,uBACvB3D,KAAMiM,EAAMjM,KACZvC,OAAQwO,EAAMxO,UAGhB2C,EAAaK,EAAG,KAAI9B,EAAI,GAE1BkN,IAAQrL,EAAQqD,YAAYkI,gBAAkBF,GAElDxI,EAAAA,YAAYS,WAAWtD,EAAQqD,YAAazD,GAC5CgD,EAAAA,QAAQU,WAAWtD,EAASJ,GAElB,IAANzB,IAEAqN,EAASvL,GAAOD,EAChB6C,EAAAA,YAAYS,WAAWtD,EAAQqD,YAAapD,GAC5C2C,EAAAA,QAAQU,WAAWtD,EAASC,IAGhCuL,EAAS5L,GAAWI,KAGxB9H,OAAOgJ,OAAO3B,EAAU,CAAEM,SAAQ2L,KAG1C,MAAOL,GAIH,YAFA1R,EAAK0R,GAMb1R,KA3FG2R,EAAA/I,UAA+BE,EAAaA,cAACC,OAU7C4I,EAAgB5D,kBAAG,EAmF7B4D"}