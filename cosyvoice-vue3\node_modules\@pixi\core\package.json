{"name": "@pixi/core", "version": "6.5.10", "main": "dist/cjs/core.js", "module": "dist/esm/core.mjs", "bundle": "dist/browser/core.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/core.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/core.js"}}}, "description": "Core PixiJS", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "funding": {"type": "opencollective", "url": "https://opencollective.com/pixijs"}, "dependencies": {"@types/offscreencanvas": "^2019.6.4"}, "peerDependencies": {"@pixi/constants": "6.5.10", "@pixi/extensions": "6.5.10", "@pixi/math": "6.5.10", "@pixi/runner": "6.5.10", "@pixi/settings": "6.5.10", "@pixi/ticker": "6.5.10", "@pixi/utils": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}