/*!
 * @pixi/mesh-extras - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mesh-extras is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_mesh_extras=function(t,e,i,r){"use strict";var h=function(t,e){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},h(t,e)};function o(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var s=function(t){function e(e,i,r,h){void 0===e&&(e=100),void 0===i&&(i=100),void 0===r&&(r=10),void 0===h&&(h=10);var o=t.call(this)||this;return o.segWidth=r,o.segHeight=h,o.width=e,o.height=i,o.build(),o}return o(e,t),e.prototype.build=function(){for(var t=this.segWidth*this.segHeight,e=[],i=[],r=[],h=this.segWidth-1,o=this.segHeight-1,s=this.width/h,n=this.height/o,a=0;a<t;a++){var u=a%this.segWidth,d=a/this.segWidth|0;e.push(u*s,d*n),i.push(u/h,d/o)}var f=h*o;for(a=0;a<f;a++){var p=a%h,g=a/h|0,c=g*this.segWidth+p,l=g*this.segWidth+p+1,_=(g+1)*this.segWidth+p,y=(g+1)*this.segWidth+p+1;r.push(c,l,_,l,y,_)}this.buffers[0].data=new Float32Array(e),this.buffers[1].data=new Float32Array(i),this.indexBuffer.data=new Uint16Array(r),this.buffers[0].update(),this.buffers[1].update(),this.indexBuffer.update()},e}(e.MeshGeometry),n=function(t){function e(e,i,r){void 0===e&&(e=200),void 0===r&&(r=0);var h=t.call(this,new Float32Array(4*i.length),new Float32Array(4*i.length),new Uint16Array(6*(i.length-1)))||this;return h.points=i,h._width=e,h.textureScale=r,h.build(),h}return o(e,t),Object.defineProperty(e.prototype,"width",{get:function(){return this._width},enumerable:!1,configurable:!0}),e.prototype.build=function(){var t=this.points;if(t){var e=this.getBuffer("aVertexPosition"),i=this.getBuffer("aTextureCoord"),r=this.getIndex();if(!(t.length<1)){e.data.length/4!==t.length&&(e.data=new Float32Array(4*t.length),i.data=new Float32Array(4*t.length),r.data=new Uint16Array(6*(t.length-1)));var h=i.data,o=r.data;h[0]=0,h[1]=0,h[2]=0,h[3]=1;for(var s=0,n=t[0],a=this._width*this.textureScale,u=t.length,d=0;d<u;d++){var f=4*d;if(this.textureScale>0){var p=n.x-t[d].x,g=n.y-t[d].y,c=Math.sqrt(p*p+g*g);n=t[d],s+=c/a}else s=d/(u-1);h[f]=s,h[f+1]=0,h[f+2]=s,h[f+3]=1}var l=0;for(d=0;d<u-1;d++){f=2*d;o[l++]=f,o[l++]=f+1,o[l++]=f+2,o[l++]=f+2,o[l++]=f+1,o[l++]=f+3}i.update(),r.update(),this.updateVertices()}}},e.prototype.updateVertices=function(){var t=this.points;if(!(t.length<1)){for(var e,i=t[0],r=0,h=0,o=this.buffers[0].data,s=t.length,n=0;n<s;n++){var a=t[n],u=4*n;h=-((e=n<t.length-1?t[n+1]:a).x-i.x),r=e.y-i.y;var d=10*(1-n/(s-1));d>1&&(d=1);var f=Math.sqrt(r*r+h*h),p=this.textureScale>0?this.textureScale*this._width/2:this._width/2;r/=f,h/=f,r*=p,h*=p,o[u]=a.x+r,o[u+1]=a.y+h,o[u+2]=a.x-r,o[u+3]=a.y-h,i=a}this.buffers[0].update()}},e.prototype.update=function(){this.textureScale>0?this.build():this.updateVertices()},e}(e.MeshGeometry),a=function(t){function r(r,h,o){void 0===o&&(o=0);var s=this,a=new n(r.height,h,o),u=new e.MeshMaterial(r);return o>0&&(r.baseTexture.wrapMode=i.WRAP_MODES.REPEAT),(s=t.call(this,a,u)||this).autoUpdate=!0,s}return o(r,t),r.prototype._render=function(e){var i=this.geometry;(this.autoUpdate||i._width!==this.shader.texture.height)&&(i._width=this.shader.texture.height,i.update()),t.prototype._render.call(this,e)},r}(e.Mesh),u=function(t){function i(i,h,o){var n=this,a=new s(i.width,i.height,h,o),u=new e.MeshMaterial(r.Texture.WHITE);return(n=t.call(this,a,u)||this).texture=i,n.autoResize=!0,n}return o(i,t),i.prototype.textureUpdated=function(){this._textureID=this.shader.texture._updateID;var t=this.geometry,e=this.shader.texture,i=e.width,r=e.height;!this.autoResize||t.width===i&&t.height===r||(t.width=this.shader.texture.width,t.height=this.shader.texture.height,t.build())},Object.defineProperty(i.prototype,"texture",{get:function(){return this.shader.texture},set:function(t){this.shader.texture!==t&&(this.shader.texture=t,this._textureID=-1,t.baseTexture.valid?this.textureUpdated():t.once("update",this.textureUpdated,this))},enumerable:!1,configurable:!0}),i.prototype._render=function(e){this._textureID!==this.shader.texture._updateID&&this.textureUpdated(),t.prototype._render.call(this,e)},i.prototype.destroy=function(e){this.shader.texture.off("update",this.textureUpdated,this),t.prototype.destroy.call(this,e)},i}(e.Mesh),d=function(t){function i(i,h,o,s,n){void 0===i&&(i=r.Texture.EMPTY);var a=this,u=new e.MeshGeometry(h,o,s);u.getBuffer("aVertexPosition").static=!1;var d=new e.MeshMaterial(i);return(a=t.call(this,u,d,null,n)||this).autoUpdate=!0,a}return o(i,t),Object.defineProperty(i.prototype,"vertices",{get:function(){return this.geometry.getBuffer("aVertexPosition").data},set:function(t){this.geometry.getBuffer("aVertexPosition").data=t},enumerable:!1,configurable:!0}),i.prototype._render=function(e){this.autoUpdate&&this.geometry.getBuffer("aVertexPosition").update(),t.prototype._render.call(this,e)},i}(e.Mesh),f=function(t){function e(e,i,h,o,s){void 0===i&&(i=10),void 0===h&&(h=10),void 0===o&&(o=10),void 0===s&&(s=10);var n=t.call(this,r.Texture.WHITE,4,4)||this;return n._origWidth=e.orig.width,n._origHeight=e.orig.height,n._width=n._origWidth,n._height=n._origHeight,n._leftWidth=i,n._rightWidth=o,n._topHeight=h,n._bottomHeight=s,n.texture=e,n}return o(e,t),e.prototype.textureUpdated=function(){this._textureID=this.shader.texture._updateID,this._refresh()},Object.defineProperty(e.prototype,"vertices",{get:function(){return this.geometry.getBuffer("aVertexPosition").data},set:function(t){this.geometry.getBuffer("aVertexPosition").data=t},enumerable:!1,configurable:!0}),e.prototype.updateHorizontalVertices=function(){var t=this.vertices,e=this._getMinScale();t[9]=t[11]=t[13]=t[15]=this._topHeight*e,t[17]=t[19]=t[21]=t[23]=this._height-this._bottomHeight*e,t[25]=t[27]=t[29]=t[31]=this._height},e.prototype.updateVerticalVertices=function(){var t=this.vertices,e=this._getMinScale();t[2]=t[10]=t[18]=t[26]=this._leftWidth*e,t[4]=t[12]=t[20]=t[28]=this._width-this._rightWidth*e,t[6]=t[14]=t[22]=t[30]=this._width},e.prototype._getMinScale=function(){var t=this._leftWidth+this._rightWidth,e=this._width>t?1:this._width/t,i=this._topHeight+this._bottomHeight,r=this._height>i?1:this._height/i;return Math.min(e,r)},Object.defineProperty(e.prototype,"width",{get:function(){return this._width},set:function(t){this._width=t,this._refresh()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this._height},set:function(t){this._height=t,this._refresh()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"leftWidth",{get:function(){return this._leftWidth},set:function(t){this._leftWidth=t,this._refresh()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rightWidth",{get:function(){return this._rightWidth},set:function(t){this._rightWidth=t,this._refresh()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"topHeight",{get:function(){return this._topHeight},set:function(t){this._topHeight=t,this._refresh()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bottomHeight",{get:function(){return this._bottomHeight},set:function(t){this._bottomHeight=t,this._refresh()},enumerable:!1,configurable:!0}),e.prototype._refresh=function(){var t=this.texture,e=this.geometry.buffers[1].data;this._origWidth=t.orig.width,this._origHeight=t.orig.height;var i=1/this._origWidth,r=1/this._origHeight;e[0]=e[8]=e[16]=e[24]=0,e[1]=e[3]=e[5]=e[7]=0,e[6]=e[14]=e[22]=e[30]=1,e[25]=e[27]=e[29]=e[31]=1,e[2]=e[10]=e[18]=e[26]=i*this._leftWidth,e[4]=e[12]=e[20]=e[28]=1-i*this._rightWidth,e[9]=e[11]=e[13]=e[15]=r*this._topHeight,e[17]=e[19]=e[21]=e[23]=1-r*this._bottomHeight,this.updateHorizontalVertices(),this.updateVerticalVertices(),this.geometry.buffers[0].update(),this.geometry.buffers[1].update()},e}(u);return t.NineSlicePlane=f,t.PlaneGeometry=s,t.RopeGeometry=n,t.SimpleMesh=d,t.SimplePlane=u,t.SimpleRope=a,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI,PIXI);Object.assign(this.PIXI,_pixi_mesh_extras);
//# sourceMappingURL=mesh-extras.min.js.map
