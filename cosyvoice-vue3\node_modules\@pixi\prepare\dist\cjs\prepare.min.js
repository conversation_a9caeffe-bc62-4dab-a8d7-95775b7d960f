/*!
 * @pixi/prepare - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/prepare is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/settings"),t=require("@pixi/core"),i=require("@pixi/graphics"),r=require("@pixi/ticker"),n=require("@pixi/display"),o=require("@pixi/text");e.settings.UPLOADS_PER_FRAME=4;var s=function(e,t){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},s(e,t)};var u=function(){function e(e){this.maxItemsPerFrame=e,this.itemsLeft=0}return e.prototype.beginFrame=function(){this.itemsLeft=this.maxItemsPerFrame},e.prototype.allowedToUpload=function(){return this.itemsLeft-- >0},e}();function a(e,i){var r=!1;if(e&&e._textures&&e._textures.length)for(var n=0;n<e._textures.length;n++)if(e._textures[n]instanceof t.Texture){var o=e._textures[n].baseTexture;-1===i.indexOf(o)&&(i.push(o),r=!0)}return r}function h(e,i){if(e.baseTexture instanceof t.BaseTexture){var r=e.baseTexture;return-1===i.indexOf(r)&&i.push(r),!0}return!1}function p(e,i){if(e._texture&&e._texture instanceof t.Texture){var r=e._texture.baseTexture;return-1===i.indexOf(r)&&i.push(r),!0}return!1}function c(e,t){return t instanceof o.Text&&(t.updateText(!0),!0)}function l(e,t){if(t instanceof o.TextStyle){var i=t.toFontString();return o.TextMetrics.measureFont(i),!0}return!1}function f(e,t){if(e instanceof o.Text){-1===t.indexOf(e.style)&&t.push(e.style),-1===t.indexOf(e)&&t.push(e);var i=e._texture.baseTexture;return-1===t.indexOf(i)&&t.push(i),!0}return!1}function d(e,t){return e instanceof o.TextStyle&&(-1===t.indexOf(e)&&t.push(e),!0)}var x=function(){function t(t){var i=this;this.limiter=new u(e.settings.UPLOADS_PER_FRAME),this.renderer=t,this.uploadHookHelper=null,this.queue=[],this.addHooks=[],this.uploadHooks=[],this.completes=[],this.ticking=!1,this.delayedTick=function(){i.queue&&i.prepareItems()},this.registerFindHook(f),this.registerFindHook(d),this.registerFindHook(a),this.registerFindHook(h),this.registerFindHook(p),this.registerUploadHook(c),this.registerUploadHook(l)}return t.prototype.upload=function(e,t){var i=this;return"function"==typeof e&&(t=e,e=null),new Promise((function(n){e&&i.add(e);var o=function(){null==t||t(),n()};i.queue.length?(i.completes.push(o),i.ticking||(i.ticking=!0,r.Ticker.system.addOnce(i.tick,i,r.UPDATE_PRIORITY.UTILITY))):o()}))},t.prototype.tick=function(){setTimeout(this.delayedTick,0)},t.prototype.prepareItems=function(){for(this.limiter.beginFrame();this.queue.length&&this.limiter.allowedToUpload();){var e=this.queue[0],t=!1;if(e&&!e._destroyed)for(var i=0,n=this.uploadHooks.length;i<n;i++)if(this.uploadHooks[i](this.uploadHookHelper,e)){this.queue.shift(),t=!0;break}t||this.queue.shift()}if(this.queue.length)r.Ticker.system.addOnce(this.tick,this,r.UPDATE_PRIORITY.UTILITY);else{this.ticking=!1;var o=this.completes.slice(0);this.completes.length=0;for(i=0,n=o.length;i<n;i++)o[i]()}},t.prototype.registerFindHook=function(e){return e&&this.addHooks.push(e),this},t.prototype.registerUploadHook=function(e){return e&&this.uploadHooks.push(e),this},t.prototype.add=function(e){for(var t=0,i=this.addHooks.length;t<i&&!this.addHooks[t](e,this.queue);t++);if(e instanceof n.Container)for(t=e.children.length-1;t>=0;t--)this.add(e.children[t]);return this},t.prototype.destroy=function(){this.ticking&&r.Ticker.system.remove(this.tick,this),this.ticking=!1,this.addHooks=null,this.uploadHooks=null,this.renderer=null,this.completes=null,this.queue=null,this.limiter=null,this.uploadHookHelper=null},t}();function k(e,i){return i instanceof t.BaseTexture&&(i._glTextures[e.CONTEXT_UID]||e.texture.bind(i),!0)}function g(e,t){if(!(t instanceof i.Graphics))return!1;var r=t.geometry;t.finishPoly(),r.updateBatches();for(var n=r.batches,o=0;o<n.length;o++){var s=n[o].style.texture;s&&k(e,s.baseTexture)}return r.batchable||e.geometry.bind(r,t._resolveDirectShader(e)),!0}function m(e,t){return e instanceof i.Graphics&&(t.push(e),!0)}var T=function(e){function i(t){var i=e.call(this,t)||this;return i.uploadHookHelper=i.renderer,i.registerFindHook(m),i.registerUploadHook(k),i.registerUploadHook(g),i}return function(e,t){function i(){this.constructor=e}s(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}(i,e),i.extension={name:"prepare",type:t.ExtensionType.RendererPlugin},i}(x),y=function(){function e(e){this.maxMilliseconds=e,this.frameStart=0}return e.prototype.beginFrame=function(){this.frameStart=Date.now()},e.prototype.allowedToUpload=function(){return Date.now()-this.frameStart<this.maxMilliseconds},e}();exports.BasePrepare=x,exports.CountLimiter=u,exports.Prepare=T,exports.TimeLimiter=y;
//# sourceMappingURL=prepare.min.js.map
