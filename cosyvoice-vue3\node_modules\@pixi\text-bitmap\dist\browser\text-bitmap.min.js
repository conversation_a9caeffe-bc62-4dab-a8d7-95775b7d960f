/*!
 * @pixi/text-bitmap - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/text-bitmap is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_text_bitmap=function(e,t,r,i,n,a,o,s,h,l){"use strict";var u=function(e,t){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},u(e,t)};var f=function(){this.info=[],this.common=[],this.page=[],this.char=[],this.kerning=[],this.distanceField=[]},c=function(){function e(){}return e.test=function(e){return"string"==typeof e&&0===e.indexOf("info face=")},e.parse=function(e){var t=e.match(/^[a-z]+\s+.+$/gm),r={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(var i in t){var n=t[i].match(/^[a-z]+/gm)[0],a=t[i].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),o={};for(var s in a){var h=a[s].split("="),l=h[0],u=h[1].replace(/"/gm,""),c=parseFloat(u),d=isNaN(c)?u:c;o[l]=d}r[n].push(o)}var p=new f;return r.info.forEach((function(e){return p.info.push({face:e.face,size:parseInt(e.size,10)})})),r.common.forEach((function(e){return p.common.push({lineHeight:parseInt(e.lineHeight,10)})})),r.page.forEach((function(e){return p.page.push({id:parseInt(e.id,10),file:e.file})})),r.char.forEach((function(e){return p.char.push({id:parseInt(e.id,10),page:parseInt(e.page,10),x:parseInt(e.x,10),y:parseInt(e.y,10),width:parseInt(e.width,10),height:parseInt(e.height,10),xoffset:parseInt(e.xoffset,10),yoffset:parseInt(e.yoffset,10),xadvance:parseInt(e.xadvance,10)})})),r.kerning.forEach((function(e){return p.kerning.push({first:parseInt(e.first,10),second:parseInt(e.second,10),amount:parseInt(e.amount,10)})})),r.distanceField.forEach((function(e){return p.distanceField.push({distanceRange:parseInt(e.distanceRange,10),fieldType:e.fieldType})})),p},e}(),d=function(){function e(){}return e.test=function(e){return e instanceof XMLDocument&&e.getElementsByTagName("page").length&&null!==e.getElementsByTagName("info")[0].getAttribute("face")},e.parse=function(e){for(var t=new f,r=e.getElementsByTagName("info"),i=e.getElementsByTagName("common"),n=e.getElementsByTagName("page"),a=e.getElementsByTagName("char"),o=e.getElementsByTagName("kerning"),s=e.getElementsByTagName("distanceField"),h=0;h<r.length;h++)t.info.push({face:r[h].getAttribute("face"),size:parseInt(r[h].getAttribute("size"),10)});for(h=0;h<i.length;h++)t.common.push({lineHeight:parseInt(i[h].getAttribute("lineHeight"),10)});for(h=0;h<n.length;h++)t.page.push({id:parseInt(n[h].getAttribute("id"),10)||0,file:n[h].getAttribute("file")});for(h=0;h<a.length;h++){var l=a[h];t.char.push({id:parseInt(l.getAttribute("id"),10),page:parseInt(l.getAttribute("page"),10)||0,x:parseInt(l.getAttribute("x"),10),y:parseInt(l.getAttribute("y"),10),width:parseInt(l.getAttribute("width"),10),height:parseInt(l.getAttribute("height"),10),xoffset:parseInt(l.getAttribute("xoffset"),10),yoffset:parseInt(l.getAttribute("yoffset"),10),xadvance:parseInt(l.getAttribute("xadvance"),10)})}for(h=0;h<o.length;h++)t.kerning.push({first:parseInt(o[h].getAttribute("first"),10),second:parseInt(o[h].getAttribute("second"),10),amount:parseInt(o[h].getAttribute("amount"),10)});for(h=0;h<s.length;h++)t.distanceField.push({fieldType:s[h].getAttribute("fieldType"),distanceRange:parseInt(s[h].getAttribute("distanceRange"),10)});return t},e}(),p=function(){function e(){}return e.test=function(e){if("string"==typeof e&&e.indexOf("<font>")>-1){var t=(new globalThis.DOMParser).parseFromString(e,"text/xml");return d.test(t)}return!1},e.parse=function(e){var t=(new globalThis.DOMParser).parseFromString(e,"text/xml");return d.parse(t)},e}(),g=[c,d,p];function m(e){for(var t=0;t<g.length;t++)if(g[t].test(e))return g[t];return null}function x(e,t,r,i,a,s,h){var l=r.text,u=r.fontProperties;t.translate(i,a),t.scale(s,s);var f=h.strokeThickness/2,c=-h.strokeThickness/2;if(t.font=h.toFontString(),t.lineWidth=h.strokeThickness,t.textBaseline=h.textBaseline,t.lineJoin=h.lineJoin,t.miterLimit=h.miterLimit,t.fillStyle=function(e,t,r,i,n,a){var s,h=r.fill;if(!Array.isArray(h))return h;if(1===h.length)return h[0];var l=r.dropShadow?r.dropShadowDistance:0,u=r.padding||0,f=e.width/i-l-2*u,c=e.height/i-l-2*u,d=h.slice(),p=r.fillGradientStops.slice();if(!p.length)for(var g=d.length+1,m=1;m<g;++m)p.push(m/g);if(d.unshift(h[0]),p.unshift(0),d.push(h[h.length-1]),p.push(1),r.fillGradientType===o.TEXT_GRADIENT.LINEAR_VERTICAL){s=t.createLinearGradient(f/2,u,f/2,c+u);var x=0,v=(a.fontProperties.fontSize+r.strokeThickness)/c;for(m=0;m<n.length;m++)for(var y=a.lineHeight*m,_=0;_<d.length;_++){var b=y/c+("number"==typeof p[_]?p[_]:_/d.length)*v,T=Math.max(x,b);T=Math.min(T,1),s.addColorStop(T,d[_]),x=T}}else{s=t.createLinearGradient(u,c/2,f+u,c/2);var w=d.length+1,P=1;for(m=0;m<d.length;m++){var M=void 0;M="number"==typeof p[m]?p[m]:P/w,s.addColorStop(M,d[m]),P++}}return s}(e,t,h,s,[l],r),t.strokeStyle=h.stroke,h.dropShadow){var d=h.dropShadowColor,p=n.hex2rgb("number"==typeof d?d:n.string2hex(d)),g=h.dropShadowBlur*s,m=h.dropShadowDistance*s;t.shadowColor="rgba("+255*p[0]+","+255*p[1]+","+255*p[2]+","+h.dropShadowAlpha+")",t.shadowBlur=g,t.shadowOffsetX=Math.cos(h.dropShadowAngle)*m,t.shadowOffsetY=Math.sin(h.dropShadowAngle)*m}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0;h.stroke&&h.strokeThickness&&t.strokeText(l,f,c+r.lineHeight-u.descent),h.fill&&t.fillText(l,f,c+r.lineHeight-u.descent),t.setTransform(1,0,0,1,0,0),t.fillStyle="rgba(0, 0, 0, 0)"}function v(e){return Array.from?Array.from(e):e.split("")}function y(e){return e.codePointAt?e.codePointAt(0):e.charCodeAt(0)}var _=function(){function e(e,r,i){var o,h,l=e.info[0],u=e.common[0],f=e.page[0],c=e.distanceField[0],d=n.getResolutionOfUrl(f.file),p={};this._ownsTextures=i,this.font=l.face,this.size=l.size,this.lineHeight=u.lineHeight/d,this.chars={},this.pageTextures=p;for(var g=0;g<e.page.length;g++){var m=e.page[g],x=m.id,v=m.file;p[x]=r instanceof Array?r[g]:r[v],(null==c?void 0:c.fieldType)&&"none"!==c.fieldType&&(p[x].baseTexture.alphaMode=s.ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,p[x].baseTexture.mipmap=s.MIPMAP_MODES.OFF)}for(g=0;g<e.char.length;g++){var y=e.char[g],_=(x=y.id,y.page),b=e.char[g],T=b.x,w=b.y,P=b.width,M=b.height,I=b.xoffset,A=b.yoffset,S=b.xadvance;T/=d,w/=d,P/=d,M/=d,I/=d,A/=d,S/=d;var E=new t.Rectangle(T+p[_].frame.x/d,w+p[_].frame.y/d,P,M);this.chars[x]={xOffset:I,yOffset:A,xAdvance:S,kerning:{},texture:new a.Texture(p[_].baseTexture,E),page:_}}for(g=0;g<e.kerning.length;g++){var F=e.kerning[g],C=F.first,O=F.second,D=F.amount;C/=d,O/=d,D/=d,this.chars[O]&&(this.chars[O].kerning[C]=D)}this.distanceFieldRange=null==c?void 0:c.distanceRange,this.distanceFieldType=null!==(h=null===(o=null==c?void 0:c.fieldType)||void 0===o?void 0:o.toLowerCase())&&void 0!==h?h:"none"}return e.prototype.destroy=function(){for(var e in this.chars)this.chars[e].texture.destroy(),this.chars[e].texture=null;for(var e in this.pageTextures)this._ownsTextures&&this.pageTextures[e].destroy(!0),this.pageTextures[e]=null;this.chars=null,this.pageTextures=null},e.install=function(t,r,i){var n;if(t instanceof f)n=t;else{var o=m(t);if(!o)throw new Error("Unrecognized data format for font.");n=o.parse(t)}r instanceof a.Texture&&(r=[r]);var s=new e(n,r,i);return e.available[s.font]=s,s},e.uninstall=function(t){var r=e.available[t];if(!r)throw new Error("No font found named '"+t+"'");r.destroy(),delete e.available[t]},e.from=function(t,i,n){if(!t)throw new Error("[BitmapFont] Property `name` is required.");var s=Object.assign({},e.defaultOptions,n),h=s.chars,l=s.padding,u=s.resolution,c=s.textureWidth,d=s.textureHeight,p=function(e){"string"==typeof e&&(e=[e]);for(var t=[],r=0,i=e.length;r<i;r++){var n=e[r];if(Array.isArray(n)){if(2!==n.length)throw new Error("[BitmapFont]: Invalid character range length, expecting 2 got "+n.length+".");var a=n[0].charCodeAt(0),o=n[1].charCodeAt(0);if(o<a)throw new Error("[BitmapFont]: Invalid character range.");for(var s=a,h=o;s<=h;s++)t.push(String.fromCharCode(s))}else t.push.apply(t,v(n))}if(0===t.length)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}(h),g=i instanceof o.TextStyle?i:new o.TextStyle(i),m=c,_=new f;_.info[0]={face:g.fontFamily,size:g.fontSize},_.common[0]={lineHeight:g.fontSize};for(var b,T,w,P=0,M=0,I=0,A=[],S=[],E=0;E<p.length;E++){b||((b=r.settings.ADAPTER.createCanvas()).width=c,b.height=d,T=b.getContext("2d"),w=new a.BaseTexture(b,{resolution:u}),A.push(w),S.push(new a.Texture(w)),_.page.push({id:S.length-1,file:""}));var F=p[E],C=o.TextMetrics.measureText(F,g,!1,b),O=C.width,D=Math.ceil(C.height),N=Math.ceil(("italic"===g.fontStyle?2:1)*O);if(M>=d-D*u){if(0===M)throw new Error("[BitmapFont] textureHeight "+d+"px is too small (fontFamily: '"+g.fontFamily+"', fontSize: "+g.fontSize+"px, char: '"+F+"')");--E,b=null,T=null,w=null,M=0,P=0,I=0}else if(I=Math.max(D+C.fontProperties.descent,I),N*u+P>=m){if(0===P)throw new Error("[BitmapFont] textureWidth "+c+"px is too small (fontFamily: '"+g.fontFamily+"', fontSize: "+g.fontSize+"px, char: '"+F+"')");--E,M+=I*u,M=Math.ceil(M),P=0,I=0}else{x(b,T,C,P,M,u,g);var B=y(C.text);_.char.push({id:B,page:S.length-1,x:P/u,y:M/u,width:N,height:D,xoffset:0,yoffset:0,xadvance:Math.ceil(O-(g.dropShadow?g.dropShadowDistance:0)-(g.stroke?g.strokeThickness:0))}),P+=(N+2*l)*u,P=Math.ceil(P)}}if(!(null==n?void 0:n.skipKerning)){E=0;for(var k=p.length;E<k;E++)for(var L=p[E],z=0;z<k;z++){var H=p[z],R=T.measureText(L).width,j=T.measureText(H).width,X=T.measureText(L+H).width-(R+j);X&&_.kerning.push({first:y(L),second:y(H),amount:X})}}var W=new e(_,S,!0);return void 0!==e.available[t]&&e.uninstall(t),e.available[t]=W,W},e.ALPHA=[["a","z"],["A","Z"]," "],e.NUMERIC=[["0","9"]],e.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],e.ASCII=[[" ","~"]],e.defaultOptions={resolution:1,textureWidth:512,textureHeight:512,padding:4,chars:e.ALPHANUMERIC},e.available={},e}(),b=[],T=[],w=[],P=function(e){function o(i,n){void 0===n&&(n={});var a=e.call(this)||this;a._tint=16777215;var s=Object.assign({},o.styleDefaults,n),h=s.align,l=s.tint,u=s.maxWidth,f=s.letterSpacing,c=s.fontName,d=s.fontSize;if(!_.available[c])throw new Error('Missing BitmapFont "'+c+'"');return a._activePagesMeshData=[],a._textWidth=0,a._textHeight=0,a._align=h,a._tint=l,a._font=void 0,a._fontName=c,a._fontSize=d,a.text=i,a._maxWidth=u,a._maxLineHeight=0,a._letterSpacing=f,a._anchor=new t.ObservablePoint((function(){a.dirty=!0}),a,0,0),a._roundPixels=r.settings.ROUND_PIXELS,a.dirty=!0,a._resolution=r.settings.RESOLUTION,a._autoResolution=!0,a._textureCache={},a}return function(e,t){function r(){this.constructor=e}u(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(o,e),o.prototype.updateText=function(){for(var e,r=_.available[this._fontName],o=this.fontSize,h=o/r.size,l=new t.Point,u=[],f=[],c=[],d=v(this._text.replace(/(?:\r\n|\r)/g,"\n")||" "),p=this._maxWidth*r.size/o,g="none"===r.distanceFieldType?b:T,m=null,x=0,P=0,M=0,I=-1,A=0,S=0,E=0,F=0,C=0;C<d.length;C++){var O=y(K=d[C]);if(/(?:\s)/.test(K)&&(I=C,A=x,F++),"\r"!==K&&"\n"!==K){var D=r.chars[O];if(D){m&&D.kerning[m]&&(l.x+=D.kerning[m]);var N=w.pop()||{texture:a.Texture.EMPTY,line:0,charCode:0,prevSpaces:0,position:new t.Point};N.texture=D.texture,N.line=M,N.charCode=O,N.position.x=l.x+D.xOffset+this._letterSpacing/2,N.position.y=l.y+D.yOffset,N.prevSpaces=F,u.push(N),x=N.position.x+Math.max(D.xAdvance-D.xOffset,D.texture.orig.width),l.x+=D.xAdvance+this._letterSpacing,E=Math.max(E,D.yOffset+D.texture.height),m=O,-1!==I&&p>0&&l.x>p&&(++S,n.removeItems(u,1+I-S,1+C-I),C=I,I=-1,f.push(A),c.push(u.length>0?u[u.length-1].prevSpaces:0),P=Math.max(P,A),M++,l.x=0,l.y+=r.lineHeight,m=null,F=0)}}else f.push(x),c.push(-1),P=Math.max(P,x),++M,++S,l.x=0,l.y+=r.lineHeight,m=null,F=0}var B=d[d.length-1];"\r"!==B&&"\n"!==B&&(/(?:\s)/.test(B)&&(x=A),f.push(x),P=Math.max(P,x),c.push(-1));var k=[];for(C=0;C<=M;C++){var L=0;"right"===this._align?L=P-f[C]:"center"===this._align?L=(P-f[C])/2:"justify"===this._align&&(L=c[C]<0?0:(P-f[C])/c[C]),k.push(L)}var z=u.length,H={},R=[],j=this._activePagesMeshData;g.push.apply(g,j);for(C=0;C<z;C++){var X=(ee=u[C].texture).baseTexture.uid;if(!H[X]){if(!(se=g.pop())){var W=new i.MeshGeometry,U=void 0,Y=void 0;"none"===r.distanceFieldType?(U=new i.MeshMaterial(a.Texture.EMPTY),Y=s.BLEND_MODES.NORMAL):(U=new i.MeshMaterial(a.Texture.EMPTY,{program:a.Program.from("// Mesh material default fragment\r\nattribute vec2 aVertexPosition;\r\nattribute vec2 aTextureCoord;\r\n\r\nuniform mat3 projectionMatrix;\r\nuniform mat3 translationMatrix;\r\nuniform mat3 uTextureMatrix;\r\n\r\nvarying vec2 vTextureCoord;\r\n\r\nvoid main(void)\r\n{\r\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\r\n\r\n    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;\r\n}\r\n","// Pixi texture info\r\nvarying vec2 vTextureCoord;\r\nuniform sampler2D uSampler;\r\n\r\n// Tint\r\nuniform vec4 uColor;\r\n\r\n// on 2D applications fwidth is screenScale / glyphAtlasScale * distanceFieldRange\r\nuniform float uFWidth;\r\n\r\nvoid main(void) {\r\n\r\n  // To stack MSDF and SDF we need a non-pre-multiplied-alpha texture.\r\n  vec4 texColor = texture2D(uSampler, vTextureCoord);\r\n\r\n  // MSDF\r\n  float median = texColor.r + texColor.g + texColor.b -\r\n                  min(texColor.r, min(texColor.g, texColor.b)) -\r\n                  max(texColor.r, max(texColor.g, texColor.b));\r\n  // SDF\r\n  median = min(median, texColor.a);\r\n\r\n  float screenPxDistance = uFWidth * (median - 0.5);\r\n  float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\r\n  if (median < 0.01) {\r\n    alpha = 0.0;\r\n  } else if (median > 0.99) {\r\n    alpha = 1.0;\r\n  }\r\n\r\n  // NPM Textures, NPM outputs\r\n  gl_FragColor = vec4(uColor.rgb, uColor.a * alpha);\r\n\r\n}\r\n"),uniforms:{uFWidth:0}}),Y=s.BLEND_MODES.NORMAL_NPM);var G=new i.Mesh(W,U);G.blendMode=Y,se={index:0,indexCount:0,vertexCount:0,uvsCount:0,total:0,mesh:G,vertices:null,uvs:null,indices:null}}se.index=0,se.indexCount=0,se.vertexCount=0,se.uvsCount=0,se.total=0;var V=this._textureCache;V[X]=V[X]||new a.Texture(ee.baseTexture),se.mesh.texture=V[X],se.mesh.tint=this._tint,R.push(se),H[X]=se}H[X].total++}for(C=0;C<j.length;C++)-1===R.indexOf(j[C])&&this.removeChild(j[C].mesh);for(C=0;C<R.length;C++)R[C].mesh.parent!==this&&this.addChild(R[C].mesh);for(var C in this._activePagesMeshData=R,H){var Z=(se=H[C]).total;if(!((null===(e=se.indices)||void 0===e?void 0:e.length)>6*Z)||se.vertices.length<2*i.Mesh.BATCHABLE_SIZE)se.vertices=new Float32Array(8*Z),se.uvs=new Float32Array(8*Z),se.indices=new Uint16Array(6*Z);else for(var q=se.total,$=se.vertices,J=4*q*2;J<$.length;J++)$[J]=0;se.mesh.size=6*Z}for(C=0;C<z;C++){var K,Q=(K=u[C]).position.x+k[K.line]*("justify"===this._align?K.prevSpaces:1);this._roundPixels&&(Q=Math.round(Q));var ee,te=Q*h,re=K.position.y*h,ie=H[(ee=K.texture).baseTexture.uid],ne=ee.frame,ae=ee._uvs,oe=ie.index++;ie.indices[6*oe+0]=0+4*oe,ie.indices[6*oe+1]=1+4*oe,ie.indices[6*oe+2]=2+4*oe,ie.indices[6*oe+3]=0+4*oe,ie.indices[6*oe+4]=2+4*oe,ie.indices[6*oe+5]=3+4*oe,ie.vertices[8*oe+0]=te,ie.vertices[8*oe+1]=re,ie.vertices[8*oe+2]=te+ne.width*h,ie.vertices[8*oe+3]=re,ie.vertices[8*oe+4]=te+ne.width*h,ie.vertices[8*oe+5]=re+ne.height*h,ie.vertices[8*oe+6]=te,ie.vertices[8*oe+7]=re+ne.height*h,ie.uvs[8*oe+0]=ae.x0,ie.uvs[8*oe+1]=ae.y0,ie.uvs[8*oe+2]=ae.x1,ie.uvs[8*oe+3]=ae.y1,ie.uvs[8*oe+4]=ae.x2,ie.uvs[8*oe+5]=ae.y2,ie.uvs[8*oe+6]=ae.x3,ie.uvs[8*oe+7]=ae.y3}for(var C in this._textWidth=P*h,this._textHeight=(l.y+r.lineHeight)*h,H){var se=H[C];if(0!==this.anchor.x||0!==this.anchor.y)for(var he=0,le=this._textWidth*this.anchor.x,ue=this._textHeight*this.anchor.y,fe=0;fe<se.total;fe++)se.vertices[he++]-=le,se.vertices[he++]-=ue,se.vertices[he++]-=le,se.vertices[he++]-=ue,se.vertices[he++]-=le,se.vertices[he++]-=ue,se.vertices[he++]-=le,se.vertices[he++]-=ue;this._maxLineHeight=E*h;var ce=se.mesh.geometry.getBuffer("aVertexPosition"),de=se.mesh.geometry.getBuffer("aTextureCoord"),pe=se.mesh.geometry.getIndex();ce.data=se.vertices,de.data=se.uvs,pe.data=se.indices,ce.update(),de.update(),pe.update()}for(C=0;C<u.length;C++)w.push(u[C]);this._font=r,this.dirty=!1},o.prototype.updateTransform=function(){this.validate(),this.containerUpdateTransform()},o.prototype._render=function(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0);var r=_.available[this._fontName],i=r.distanceFieldRange,n=r.distanceFieldType,a=r.size;if("none"!==n)for(var o=this.worldTransform,s=o.a,h=o.b,l=o.c,u=o.d,f=Math.sqrt(s*s+h*h),c=Math.sqrt(l*l+u*u),d=(Math.abs(f)+Math.abs(c))/2,p=this.fontSize/a,g=0,m=this._activePagesMeshData;g<m.length;g++){m[g].mesh.shader.uniforms.uFWidth=d*i*p*this._resolution}e.prototype._render.call(this,t)},o.prototype.getLocalBounds=function(){return this.validate(),e.prototype.getLocalBounds.call(this)},o.prototype.validate=function(){var e=_.available[this._fontName];if(!e)throw new Error('Missing BitmapFont "'+this._fontName+'"');this._font!==e&&(this.dirty=!0),this.dirty&&this.updateText()},Object.defineProperty(o.prototype,"tint",{get:function(){return this._tint},set:function(e){if(this._tint!==e){this._tint=e;for(var t=0;t<this._activePagesMeshData.length;t++)this._activePagesMeshData[t].mesh.tint=e}},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"align",{get:function(){return this._align},set:function(e){this._align!==e&&(this._align=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"fontName",{get:function(){return this._fontName},set:function(e){if(!_.available[e])throw new Error('Missing BitmapFont "'+e+'"');this._fontName!==e&&(this._fontName=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"fontSize",{get:function(){var e;return null!==(e=this._fontSize)&&void 0!==e?e:_.available[this._fontName].size},set:function(e){this._fontSize!==e&&(this._fontSize=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"anchor",{get:function(){return this._anchor},set:function(e){"number"==typeof e?this._anchor.set(e):this._anchor.copyFrom(e)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"text",{get:function(){return this._text},set:function(e){e=String(null==e?"":e),this._text!==e&&(this._text=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"maxWidth",{get:function(){return this._maxWidth},set:function(e){this._maxWidth!==e&&(this._maxWidth=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"maxLineHeight",{get:function(){return this.validate(),this._maxLineHeight},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"textWidth",{get:function(){return this.validate(),this._textWidth},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"letterSpacing",{get:function(){return this._letterSpacing},set:function(e){this._letterSpacing!==e&&(this._letterSpacing=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(e){e!==this._roundPixels&&(this._roundPixels=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"textHeight",{get:function(){return this.validate(),this._textHeight},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"resolution",{get:function(){return this._resolution},set:function(e){this._autoResolution=!1,this._resolution!==e&&(this._resolution=e,this.dirty=!0)},enumerable:!1,configurable:!0}),o.prototype.destroy=function(t){var r=this._textureCache,i="none"===_.available[this._fontName].distanceFieldType?b:T;i.push.apply(i,this._activePagesMeshData);for(var n=0,o=this._activePagesMeshData;n<o.length;n++){var s=o[n];this.removeChild(s.mesh)}for(var h in this._activePagesMeshData=[],i.filter((function(e){return r[e.mesh.texture.baseTexture.uid]})).forEach((function(e){e.mesh.texture=a.Texture.EMPTY})),r){r[h].destroy(),delete r[h]}this._font=null,this._textureCache=null,e.prototype.destroy.call(this,t)},o.styleDefaults={align:"left",tint:16777215,maxWidth:0,letterSpacing:0},o}(h.Container),M=function(){function e(){}return e.add=function(){l.LoaderResource.setExtensionXhrType("fnt",l.LoaderResource.XHR_RESPONSE_TYPE.TEXT)},e.use=function(t,r){var i=m(t.data);if(i)for(var n=e.getBaseUrl(this,t),a=i.parse(t.data),o={},s=function(e){o[e.metadata.pageFile]=e.texture,Object.keys(o).length===a.page.length&&(t.bitmapFont=_.install(a,o,!0),r())},h=0;h<a.page.length;++h){var u=a.page[h].file,f=n+u,c=!1;for(var d in this.resources){var p=this.resources[d];if(p.url===f){p.metadata.pageFile=u,p.texture?s(p):p.onAfterMiddleware.add(s),c=!0;break}}if(!c){var g={crossOrigin:t.crossOrigin,loadType:l.LoaderResource.LOAD_TYPE.IMAGE,metadata:Object.assign({pageFile:u},t.metadata.imageMetadata),parentResource:t};this.add(f,g,s)}}else r()},e.getBaseUrl=function(t,r){var i=r.isDataUrl?"":e.dirname(r.url);return r.isDataUrl&&("."===i&&(i=""),t.baseUrl&&i&&"/"===t.baseUrl.charAt(t.baseUrl.length-1)&&(i+="/")),(i=i.replace(t.baseUrl,""))&&"/"!==i.charAt(i.length-1)&&(i+="/"),i},e.dirname=function(e){var t=e.replace(/\\/g,"/").replace(/\/$/,"").replace(/\/[^\/]*$/,"");return t===e?".":""===t?"/":t},e.extension=a.ExtensionType.Loader,e}();return e.BitmapFont=_,e.BitmapFontData=f,e.BitmapFontLoader=M,e.BitmapText=P,e.TextFormat=c,e.XMLFormat=d,e.XMLStringFormat=p,e.autoDetectFormat=m,Object.defineProperty(e,"__esModule",{value:!0}),e}({},PIXI,PIXI,PIXI,PIXI.utils,PIXI,PIXI,PIXI,PIXI,PIXI);Object.assign(this.PIXI,_pixi_text_bitmap);
//# sourceMappingURL=text-bitmap.min.js.map
