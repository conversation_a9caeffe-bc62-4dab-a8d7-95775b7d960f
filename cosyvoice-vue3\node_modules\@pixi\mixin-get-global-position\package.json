{"name": "@pixi/mixin-get-global-position", "version": "6.5.10", "main": "dist/cjs/mixin-get-global-position.js", "module": "dist/esm/mixin-get-global-position.mjs", "bundle": "dist/browser/mixin-get-global-position.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/mixin-get-global-position.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/mixin-get-global-position.js"}}}, "bundleNoExports": true, "description": "Mixin to find the global position of a DisplayObject", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/display": "6.5.10", "@pixi/math": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}