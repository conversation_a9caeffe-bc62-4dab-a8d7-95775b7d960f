{"name": "earcut", "version": "2.2.4", "description": "The fastest and smallest JavaScript polygon triangulation library for your WebGL apps", "main": "src/earcut.js", "unpkg": "dist/earcut.min.js", "jsdelivr": "dist/earcut.min.js", "files": ["dist/earcut.min.js", "dist/earcut.dev.js"], "scripts": {"pretest": "eslint src test/test.js", "test": "tape test/test.js", "watch": "mkdirp dist && watchify -v -d src/earcut.js -s earcut -o dist/earcut.dev.js", "build-dev": "mkdirp dist && browserify -d src/earcut.js -s earcut > dist/earcut.dev.js", "build-min": "mkdirp dist && browserify src/earcut.js -s earcut | uglifyjs -c -m > dist/earcut.min.js", "prepublishOnly": "npm run build-dev && npm run build-min", "cov": "c8 tape test/*.js", "coveralls": "npm run cov && c8 report -r lcov && coveralls < ./coverage/lcov.info"}, "author": "<PERSON>", "license": "ISC", "devDependencies": {"benchmark": "^2.1.4", "browserify": "^17.0.0", "c8": "^7.11.3", "coveralls": "^3.1.1", "eslint": "^8.19.0", "eslint-config-mourner": "^2.0.3", "mkdirp": "^1.0.4", "tape": "^5.5.3", "uglify-js": "^3.16.2", "watchify": "^4.0.0"}, "eslintConfig": {"extends": "mourner"}, "repository": {"type": "git", "url": "git://github.com/mapbox/earcut.git"}}