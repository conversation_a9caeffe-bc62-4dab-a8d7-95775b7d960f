/*!
 * @pixi/sprite - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/sprite is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{BLEND_MODES as t}from"@pixi/constants";import{Texture as e}from"@pixi/core";import{Bounds as i,Container as r}from"@pixi/display";import{Point as o,Rectangle as n,ObservablePoint as s}from"@pixi/math";import{settings as h}from"@pixi/settings";import{sign as a}from"@pixi/utils";var u=function(t,e){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},u(t,e)};var c=new o,_=new Uint16Array([0,1,2,0,2,3]),l=function(r){function o(i){var o=r.call(this)||this;return o._anchor=new s(o._onAnchorUpdate,o,i?i.defaultAnchor.x:0,i?i.defaultAnchor.y:0),o._texture=null,o._width=0,o._height=0,o._tint=null,o._tintRGB=null,o.tint=16777215,o.blendMode=t.NORMAL,o._cachedTint=16777215,o.uvs=null,o.texture=i||e.EMPTY,o.vertexData=new Float32Array(8),o.vertexTrimmedData=null,o._transformID=-1,o._textureID=-1,o._transformTrimmedID=-1,o._textureTrimmedID=-1,o.indices=_,o.pluginName="batch",o.isSprite=!0,o._roundPixels=h.ROUND_PIXELS,o}return function(t,e){function i(){this.constructor=t}u(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(o,r),o.prototype._onTextureUpdate=function(){this._textureID=-1,this._textureTrimmedID=-1,this._cachedTint=16777215,this._width&&(this.scale.x=a(this.scale.x)*this._width/this._texture.orig.width),this._height&&(this.scale.y=a(this.scale.y)*this._height/this._texture.orig.height)},o.prototype._onAnchorUpdate=function(){this._transformID=-1,this._transformTrimmedID=-1},o.prototype.calculateVertices=function(){var t=this._texture;if(this._transformID!==this.transform._worldID||this._textureID!==t._updateID){this._textureID!==t._updateID&&(this.uvs=this._texture._uvs.uvsFloat32),this._transformID=this.transform._worldID,this._textureID=t._updateID;var e=this.transform.worldTransform,i=e.a,r=e.b,o=e.c,n=e.d,s=e.tx,a=e.ty,u=this.vertexData,c=t.trim,_=t.orig,l=this._anchor,d=0,p=0,x=0,f=0;if(c?(d=(p=c.x-l._x*_.width)+c.width,x=(f=c.y-l._y*_.height)+c.height):(d=(p=-l._x*_.width)+_.width,x=(f=-l._y*_.height)+_.height),u[0]=i*p+o*f+s,u[1]=n*f+r*p+a,u[2]=i*d+o*f+s,u[3]=n*f+r*d+a,u[4]=i*d+o*x+s,u[5]=n*x+r*d+a,u[6]=i*p+o*x+s,u[7]=n*x+r*p+a,this._roundPixels)for(var m=h.RESOLUTION,g=0;g<u.length;++g)u[g]=Math.round((u[g]*m|0)/m)}},o.prototype.calculateTrimmedVertices=function(){if(this.vertexTrimmedData){if(this._transformTrimmedID===this.transform._worldID&&this._textureTrimmedID===this._texture._updateID)return}else this.vertexTrimmedData=new Float32Array(8);this._transformTrimmedID=this.transform._worldID,this._textureTrimmedID=this._texture._updateID;var t=this._texture,e=this.vertexTrimmedData,i=t.orig,r=this._anchor,o=this.transform.worldTransform,n=o.a,s=o.b,h=o.c,a=o.d,u=o.tx,c=o.ty,_=-r._x*i.width,l=_+i.width,d=-r._y*i.height,p=d+i.height;e[0]=n*_+h*d+u,e[1]=a*d+s*_+c,e[2]=n*l+h*d+u,e[3]=a*d+s*l+c,e[4]=n*l+h*p+u,e[5]=a*p+s*l+c,e[6]=n*_+h*p+u,e[7]=a*p+s*_+c},o.prototype._render=function(t){this.calculateVertices(),t.batch.setObjectRenderer(t.plugins[this.pluginName]),t.plugins[this.pluginName].render(this)},o.prototype._calculateBounds=function(){var t=this._texture.trim,e=this._texture.orig;!t||t.width===e.width&&t.height===e.height?(this.calculateVertices(),this._bounds.addQuad(this.vertexData)):(this.calculateTrimmedVertices(),this._bounds.addQuad(this.vertexTrimmedData))},o.prototype.getLocalBounds=function(t){return 0===this.children.length?(this._localBounds||(this._localBounds=new i),this._localBounds.minX=this._texture.orig.width*-this._anchor._x,this._localBounds.minY=this._texture.orig.height*-this._anchor._y,this._localBounds.maxX=this._texture.orig.width*(1-this._anchor._x),this._localBounds.maxY=this._texture.orig.height*(1-this._anchor._y),t||(this._localBoundsRect||(this._localBoundsRect=new n),t=this._localBoundsRect),this._localBounds.getRectangle(t)):r.prototype.getLocalBounds.call(this,t)},o.prototype.containsPoint=function(t){this.worldTransform.applyInverse(t,c);var e=this._texture.orig.width,i=this._texture.orig.height,r=-e*this.anchor.x,o=0;return c.x>=r&&c.x<r+e&&(o=-i*this.anchor.y,c.y>=o&&c.y<o+i)},o.prototype.destroy=function(t){if(r.prototype.destroy.call(this,t),this._texture.off("update",this._onTextureUpdate,this),this._anchor=null,"boolean"==typeof t?t:t&&t.texture){var e="boolean"==typeof t?t:t&&t.baseTexture;this._texture.destroy(!!e)}this._texture=null},o.from=function(t,i){return new o(t instanceof e?t:e.from(t,i))},Object.defineProperty(o.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(t){this._roundPixels!==t&&(this._transformID=-1),this._roundPixels=t},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"width",{get:function(){return Math.abs(this.scale.x)*this._texture.orig.width},set:function(t){var e=a(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"height",{get:function(){return Math.abs(this.scale.y)*this._texture.orig.height},set:function(t){var e=a(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"anchor",{get:function(){return this._anchor},set:function(t){this._anchor.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t,this._tintRGB=(t>>16)+(65280&t)+((255&t)<<16)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"texture",{get:function(){return this._texture},set:function(t){this._texture!==t&&(this._texture&&this._texture.off("update",this._onTextureUpdate,this),this._texture=t||e.EMPTY,this._cachedTint=16777215,this._textureID=-1,this._textureTrimmedID=-1,t&&(t.baseTexture.valid?this._onTextureUpdate():t.once("update",this._onTextureUpdate,this)))},enumerable:!1,configurable:!0}),o}(r);export{l as Sprite};
//# sourceMappingURL=sprite.min.mjs.map
