const { app, BrowserWindow, Menu, shell, ipcMain, dialog, protocol } = require('electron')
const { autoUpdater } = require('electron-updater')
const path = require('path')
const { spawn } = require('child_process')
const fs = require('fs')
const os = require('os')
const express = require('express')
const cors = require('cors')
const http = require('http')
const multer = require('multer')
const WebSocket = require('ws')

// Node.js 18+ 内置 fetch，18以下需要polyfill
const nodeFetch = globalThis.fetch || require('node-fetch')
const fetch = nodeFetch

// 禁用安全警告
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

// 设置UTF-8编码，修复后端Unicode错误
process.env['PYTHONIOENCODING'] = 'utf-8'
process.env['PYTHONUTF8'] = '1'

// 注册自定义协议以支持本地文件访问 - 必须在app.ready之前
// 这个协议用于安全地从 public 目录加载本地资源
protocol.registerSchemesAsPrivileged([
  { 
    scheme: 'safe-file', 
    privileges: { 
      standard: true, 
      secure: true, 
      supportFetchAPI: true, 
      corsEnabled: true 
    } 
  }
])

let mainWindow
let backendProcess
let apiServer
let store // 将在动态导入后初始化

// 自动更新配置
autoUpdater.autoDownload = false // 手动下载
autoUpdater.autoInstallOnAppQuit = true

// 环境检测
const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev')

// 自动更新事件处理
autoUpdater.on('checking-for-update', () => {
  console.log('🔍 检查更新中...')
})

autoUpdater.on('update-available', (info) => {
  console.log('✅ 发现新版本:', info.version)
  if (mainWindow) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '发现新版本',
      message: `发现新版本 ${info.version}`,
      detail: '是否立即下载更新？',
      buttons: ['立即下载', '稍后提醒'],
      defaultId: 0
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.downloadUpdate()
      }
    })
  }
})

autoUpdater.on('update-not-available', () => {
  console.log('✅ 已是最新版本')
})

autoUpdater.on('error', (err) => {
  console.error('❌ 更新检查失败:', err)
})

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = "下载速度: " + progressObj.bytesPerSecond
  log_message = log_message + ' - 已下载 ' + progressObj.percent + '%'
  log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')'
  console.log(log_message)
  
  if (mainWindow) {
    mainWindow.webContents.send('download-progress', progressObj)
  }
})

autoUpdater.on('update-downloaded', (info) => {
  console.log('✅ 更新下载完成')
  if (mainWindow) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '更新准备就绪',
      message: '更新已下载完成',
      detail: '应用将重启以安装更新',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall()
      }
    })
  }
})

// 后端服务配置 - 优化为动态路径检测
const BACKEND_CONFIG = {
  host: 'localhost',
  port: 7860,
  // 智能检测Python路径
  pythonPath: (() => {
    const paths = [
      path.join(__dirname, '../../env/python.exe'),
      path.join(__dirname, '../../env/pythonw.exe'),
      path.join(process.resourcesPath, 'env/python.exe'),
      'python'
    ]
    return paths.find(p => {
      try {
        return require('fs').existsSync(p) || p === 'python'
      } catch {
        return false
      }
    }) || paths[0]
  })(),
  // 智能检测启动脚本
  scriptPath: (() => {
    const paths = [
      path.join(__dirname, '../../start_api_server.py'),
      path.join(process.resourcesPath, 'start_api_server.py')
    ]
    return paths.find(p => {
      try {
        return require('fs').existsSync(p)
      } catch {
        return false
      }
    }) || paths[0]
  })(),
  // 智能检测工作目录
  workingDir: (() => {
    const paths = [
      path.join(__dirname, '../../'),
      process.resourcesPath || path.join(__dirname, '../../')
    ]
    return paths[0]
  })()
}

// 创建主窗口
async function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: false, // 允许跨域请求本地API
      allowRunningInsecureContent: true, // 允许运行不安全内容
      experimentalFeatures: true, // 启用实验性功能
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, '../public/icon.png'), // 应用图标
    titleBarStyle: 'default',
    show: true // 立即显示窗口，便于调试
  })

  // 开发环境加载 Vite 服务器，生产环境加载本地文件
  // const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev') // 移至顶部
  
  console.log('🔍 环境检测:')
  console.log('  NODE_ENV:', process.env.NODE_ENV)
  console.log('  process.argv:', process.argv)
  console.log('  isDev:', isDev)
  
  // 智能检测模式：先尝试开发服务器，失败则尝试生产文件
  const devPorts = [5173, 5174, 5175, 5176] // 常用的Vite端口
  let devServerUrl = null
  
  for (const port of devPorts) {
    console.log(`🌐 尝试连接Vue开发服务器 (http://localhost:${port})...`)
    try {
      const testResponse = await fetch(`http://localhost:${port}`)
      if (testResponse.ok) {
        devServerUrl = `http://localhost:${port}`
        console.log(`✅ 检测到Vue开发服务器在端口 ${port}`)
        break
      }
    } catch (error) {
      console.log(`❌ 端口 ${port} 连接失败:`, error.message)
    }
  }
  
  if (devServerUrl) {
    console.log(`🚀 加载开发模式: ${devServerUrl}`)
    await mainWindow.loadURL(devServerUrl)
    // 开发环境打开开发者工具
    mainWindow.webContents.openDevTools()
    
    // 立即检查后端服务
    console.log('🔍 立即检查后端服务状态...')
    checkBackendService()
    return
  }

  // 如果开发服务器不可用，尝试加载生产文件
  const distIndexPath = path.join(__dirname, '../dist/index.html')
  console.log('🔄 尝试加载生产模式文件:', distIndexPath)
  
  if (require('fs').existsSync(distIndexPath)) {
    console.log('✅ 找到生产文件，加载生产模式')
    await mainWindow.loadFile(distIndexPath)
  } else {
    console.log('⚠️ 未找到生产文件，显示提示页面')
    // 显示提示页面
    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(`
      <html>
        <head>
          <title>CosyVoice</title>
          <style>
            body { font-family: Arial; text-align: center; padding: 50px; background: #f5f5f5; }
            .container { background: white; padding: 30px; border-radius: 10px; max-width: 500px; margin: 0 auto; }
            button { background: #007acc; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
            code { background: #f0f0f0; padding: 10px; border-radius: 5px; display: inline-block; margin: 10px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>🚧 CosyVoice 配置</h2>
            <p>请选择启动方式：</p>
            <div>
              <p><strong>开发模式：</strong></p>
              <code>npm run dev</code>
              <br>
              <p><strong>生产模式：</strong></p>
              <code>npm run build</code>
            </div>
            <br>
            <button onclick="location.reload()">重新检测</button>
            <br><br>
            <p><small>当前路径: ${distIndexPath}</small></p>
          </div>
        </body>
      </html>
    `))
  }

  // 窗口准备显示时显示
  mainWindow.once('ready-to-show', () => {
    console.log('🖼️ 窗口准备就绪，显示窗口')
    mainWindow.show()
    
    // 检查后端服务
    checkBackendService()
  })
  
  // 监听加载完成事件
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ 页面加载完成')
  })
  
  // 监听加载失败事件
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('❌ 页面加载失败:', errorCode, errorDescription, validatedURL)
  })

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })

  // 窗口关闭时的处理
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// 检查后端服务状态 - 支持启动状态识别
async function checkBackendService() {
  try {
    // 🔧 改用健康检查端点，支持识别启动状态
    const response = await fetch(`http://${BACKEND_CONFIG.host}:${BACKEND_CONFIG.port}/api/health`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('🔍 [DEBUG] 健康检查响应数据:', JSON.stringify(data, null, 2))
      // 兼容两种格式: 直接在顶层的status 或 在data里的status
      const status = data.status || (data.data || {}).status
      console.log('🔍 [DEBUG] 提取的status值:', status)
      if (status === 'healthy') {
        console.log('✅ 后端API服务正常运行')
        if (mainWindow) {
          mainWindow.webContents.send('backend-status', { status: 'running' })
        }
        return true
      } else {
        console.log('⚠️ [DEBUG] status不是healthy，实际值:', status)
      }
    } else if (response.status === 503) {
      // HTTP 503 表示服务正在启动，这是正常状态
      try {
        const data = await response.json()
        // 兼容两种格式: 直接在顶层的status 或 在data里的status
        const status = data.status || (data.data || {}).status
        if (status === 'starting') {
          console.log('🚀 后端服务正在启动中... (模型加载等)')
          if (mainWindow) {
            mainWindow.webContents.send('backend-status', { 
              status: 'starting', 
              message: '模型加载中，请耐心等待...' 
            })
          }
          return false // 返回false但不重启服务
        }
      } catch (parseError) {
        console.log('⚠️ 无法解析启动状态响应')
      }
    }
    
  } catch (error) {
    console.log(`⚠️ 后端服务检查失败: ${error.message}`)
    // 只有在确实无法连接且没有后端进程运行时才启动
    if (!backendProcess || backendProcess.killed) {
      console.log('⚠️ 后端服务未运行，尝试启动...')
      startBackendService()
    } else {
      console.log('📡 后端服务启动中，请等待...')
    }
  }
  return false
}

// 启动后端服务
function startBackendService() {
  // 防止重复启动
  if (backendProcess && !backendProcess.killed) {
    console.log('🔄 后端服务已在启动中，跳过重复启动')
    return
  }

  try {
    console.log('🚀 启动后端服务...')
    console.log('📁 后端配置信息:')
    console.log(`   Python路径: ${BACKEND_CONFIG.pythonPath}`)
    console.log(`   脚本路径: ${BACKEND_CONFIG.scriptPath}`)
    console.log(`   工作目录: ${BACKEND_CONFIG.workingDir}`)
    console.log(`   目标端口: ${BACKEND_CONFIG.port}`)
    
    // 验证文件是否存在
    if (!require('fs').existsSync(BACKEND_CONFIG.scriptPath)) {
      console.error(`❌ 启动脚本不存在: ${BACKEND_CONFIG.scriptPath}`)
      return
    }
    
    console.log('✅ 启动脚本文件存在，开始启动进程...')
    
    backendProcess = spawn(BACKEND_CONFIG.pythonPath, [BACKEND_CONFIG.scriptPath], {
      cwd: BACKEND_CONFIG.workingDir,
      stdio: ['pipe', 'pipe', 'pipe'],
      detached: false
    })
    
    console.log(`🆔 后端进程PID: ${backendProcess.pid}`)

    backendProcess.stdout.on('data', (data) => {
      console.log(`[后端] ${data.toString()}`)
    })

    backendProcess.stderr.on('data', (data) => {
      console.error(`[后端错误] ${data.toString()}`)
    })

    backendProcess.on('close', (code) => {
      console.log(`后端进程退出，代码: ${code}`)
      backendProcess = null // 重置进程引用
      if (mainWindow) {
        mainWindow.webContents.send('backend-status', { status: 'stopped', code })
      }
    })

    // 🔧 大幅优化健康检查：适应模型加载时间，防止无限重启循环
    setTimeout(async () => {
      console.log('🔍 延长等待后开始检查后端服务状态...')
      for (let i = 0; i < 15; i++) {  // 增加检查次数到15次，总时长2.5分钟
        if (await checkBackendService()) {
          console.log('✅ 后端服务检查成功')
          break
        }
        console.log(`⚠️ 后端服务检查失败: 第${i+1}次尝试 (最多15次)`)
        await new Promise(resolve => setTimeout(resolve, 10000))  // 延长间隔到10秒
        console.log('📡 后端服务启动中，请等待... (模型加载可能需要较长时间)')
      }
    }, 15000)  // 初始等待时间增加到15秒

    if (mainWindow) {
      mainWindow.webContents.send('backend-status', { status: 'starting' })
    }

  } catch (error) {
    console.error('❌ 启动后端服务失败:', error)
    backendProcess = null
    if (mainWindow) {
      mainWindow.webContents.send('backend-status', { status: 'error', error: error.message })
    }
  }
}

// 创建菜单
function createMenu() {
  const template = [
    {
      label: '应用',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于 CosyVoice',
              message: 'CosyVoice 桌面版',
              detail: '一个强大的AI语音对话应用'
            })
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: 'CmdOrCtrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: '开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: '全屏', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '服务',
      submenu: [
        {
          label: '重启后端服务',
          click: () => {
            if (backendProcess) {
              backendProcess.kill()
            }
            setTimeout(() => {
              startBackendService()
            }, 2000)
          }
        },
        {
          label: '检查服务状态',
          click: () => {
            checkBackendService()
          }
        },
        { type: 'separator' },
        {
          label: '数据迁移工具',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/migrate-data.html')
            }
          }
        },
        {
          label: '存储测试页面',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/test-storage.html')
            }
          }
        },
        {
          label: '返回主页',
          accelerator: 'CmdOrCtrl+H',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/')
            }
          }
        },
        {
          label: 'IndexedDB调试工具',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/debug-indexeddb.html')
            }
          }
        },
        {
          label: '🚀 快速迁移工具',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/quick-migration.html')
            }
          }
        },
        {
          label: '🔄 通用迁移工具',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/universal-migration.html')
            }
          }
        },
        {
          label: '🖼️ 图片迁移工具',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/migrate-images.html')
            }
          }
        },
        {
          label: '🔧 URL格式修复',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL('http://localhost:5173/fix-urls.html')
            }
          }
        },
        { type: 'separator' },
        {
          label: '检查更新',
          click: () => {
            if (isDev) {
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: '检查更新',
                message: '开发模式',
                detail: '开发模式下不检查更新'
              })
            } else {
              autoUpdater.checkForUpdatesAndNotify()
            }
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// Electron Store IPC 处理器
function setupStoreIPC() {
  // 获取数据
  ipcMain.handle('store-get', (event, key, defaultValue) => {
    try {
      if (!store) {
        console.warn('⚠️ Store 未初始化，返回默认值');
        return defaultValue;
      }
      const value = store.get(key, defaultValue);
      console.log(`📥 Store GET [${key}]:`, typeof value, Array.isArray(value) ? `${value.length} items` : '');
      return value;
    } catch (error) {
      console.error(`❌ Store GET 失败 [${key}]:`, error);
      return defaultValue;
    }
  });

  // 设置数据
  ipcMain.handle('store-set', (event, key, value) => {
    try {
      if (!store) {
        console.warn('⚠️ Store 未初始化，无法设置数据');
        return false;
      }
      const oldValue = store.get(key);
      store.set(key, value);
      console.log(`📤 Store SET [${key}]:`, typeof value, Array.isArray(value) ? `${value.length} items` : '');
      
      // 通知所有窗口数据已更改
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send(`store-changed-${key}`, value, oldValue);
      });
      
      return true;
    } catch (error) {
      console.error(`❌ Store SET 失败 [${key}]:`, error);
      return false;
    }
  });

  // 检查键是否存在
  ipcMain.handle('store-has', (event, key) => {
    try {
      if (!store) {
        console.warn('⚠️ Store 未初始化，返回false');
        return false;
      }
      return store.has(key);
    } catch (error) {
      console.error(`❌ Store HAS 失败 [${key}]:`, error);
      return false;
    }
  });

  // 删除数据
  ipcMain.handle('store-delete', (event, key) => {
    try {
      if (!store) {
        console.warn('⚠️ Store 未初始化，无法删除数据');
        return false;
      }
      const oldValue = store.get(key);
      store.delete(key);
      
      // 通知所有窗口数据已删除
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send(`store-changed-${key}`, undefined, oldValue);
      });
      
      return true;
    } catch (error) {
      console.error(`❌ Store DELETE 失败 [${key}]:`, error);
      return false;
    }
  });

  // 清空所有数据
  ipcMain.handle('store-clear', (event) => {
    try {
      if (!store) {
        console.warn('⚠️ Store 未初始化，无法清空数据');
        return false;
      }
      store.clear();
      
      // 通知所有窗口数据已清空
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('store-cleared');
      });
      
      return true;
    } catch (error) {
      console.error('❌ Store CLEAR 失败:', error);
      return false;
    }
  });

  console.log('✅ Electron Store IPC 处理器已设置');
}

// 设置HTTP API服务器（供浏览器访问）
function setupHTTPAPI() {
  const apiApp = express();
  
  // 🌐 增强CORS配置，支持Chrome Private Network Access (PNA)
  apiApp.use(cors({
    origin: true, // 允许所有域
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Access-Control-Request-Private-Network'],
    exposedHeaders: ['Access-Control-Allow-Private-Network']
  }));
  
  // 🔧 专门处理PNA预检请求的中间件
  apiApp.use((req, res, next) => {
    // 检查是否为私有网络访问请求
    const isPrivateNetworkRequest = req.headers['access-control-request-private-network'];
    
    if (isPrivateNetworkRequest || req.method === 'OPTIONS') {
      console.log('🔍 PNA预检请求检测:', {
        method: req.method,
        origin: req.headers.origin,
        pnaHeader: isPrivateNetworkRequest,
        userAgent: req.headers['user-agent']?.includes('Chrome') ? 'Chrome' : 'Other',
        path: req.path
      });
      
      // 允许私有网络访问
      res.setHeader('Access-Control-Allow-Private-Network', 'true');
      res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Access-Control-Request-Private-Network');
      res.setHeader('Access-Control-Max-Age', '86400'); // 24小时缓存
      
      // 如果是OPTIONS预检请求，直接返回成功
      if (req.method === 'OPTIONS') {
        console.log('✅ PNA预检请求通过');
        return res.status(200).end();
      }
    }
    
    // 为所有响应添加PNA头部
    res.setHeader('Access-Control-Allow-Private-Network', 'true');
    next();
  });
  // 📦 增加请求体大小限制，支持大型图片base64数据
  apiApp.use(express.json({ 
    limit: '200mb',
    parameterLimit: 100000,  // 增加参数数量限制
    type: ['application/json', 'text/plain'] // 支持多种内容类型
  }));
  apiApp.use(express.urlencoded({ 
    limit: '200mb', 
    extended: true,
    parameterLimit: 100000  // URL编码数据也增加参数限制
  }));
  
  // 🛡️ 添加全局错误处理，防止大请求崩溃
  apiApp.use((error, req, res, next) => {
    if (error.type === 'entity.too.large') {
      console.error('❌ 请求体过大:', error.message);
      return res.status(413).json({ 
        error: '请求数据过大', 
        message: '图片数据超过200MB限制，请压缩后重试',
        limit: '200MB'
      });
    }
    if (error.type === 'entity.parse.failed') {
      console.error('❌ JSON解析失败:', error.message);
      return res.status(400).json({ error: 'JSON数据格式错误' });
    }
    next(error);
  });

  // 🖼️ 静态图片服务配置
  const imagesDir = path.join(__dirname, '../public/generated-images');
  // 确保图片目录存在
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
    console.log('📁 已创建图片存储目录:', imagesDir);
  }
  
  // 🔧 历史兼容性：处理缺少扩展名的图片请求
  apiApp.use('/static/images', (req, res, next) => {
    const requestedPath = req.path;
    const fullPath = path.join(imagesDir, requestedPath);
    
    // 检查文件是否存在
    if (fs.existsSync(fullPath)) {
      return next(); // 文件存在，继续正常处理
    }
    
    // 如果文件不存在，尝试添加.png扩展名
    if (!requestedPath.includes('.')) {
      const pngPath = fullPath + '.png';
      if (fs.existsSync(pngPath)) {
        console.log(`🔧 历史兼容：${requestedPath} -> ${requestedPath}.png`);
        return res.sendFile(pngPath);
      }
    }
    
    next(); // 继续到下一个中间件
  });
  
  // 🔑 关键修复：添加静态图片中间件
  apiApp.use('/static/images', express.static(imagesDir, {
    setHeaders: (res, path, stat) => {
      // 设置缓存和CORS头
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 24小时缓存
    }
  }));
  
  // 🔑 关键修复：添加兼容的图片路径映射
  apiApp.use('/generated-images', express.static(imagesDir, {
    setHeaders: (res, path, stat) => {
      // 设置缓存和CORS头
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 24小时缓存
    }
  }));

  // 🔑 新增：头像HTTP文件服务
  const avatarsDir = path.join(__dirname, '../public/avatars');
  if (!fs.existsSync(avatarsDir)) {
    fs.mkdirSync(avatarsDir, { recursive: true });
  }
  apiApp.use('/avatars', express.static(avatarsDir, {
    setHeaders: (res, path, stat) => {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 24小时缓存
    }
  }));

  // 🔑 新增：风格图片HTTP文件服务
  const stylesDir = path.join(__dirname, '../public/styles');
  if (!fs.existsSync(stylesDir)) {
    fs.mkdirSync(stylesDir, { recursive: true });
  }
  apiApp.use('/styles', express.static(stylesDir, {
    setHeaders: (res, path, stat) => {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 24小时缓存
    }
  }));
  
  console.log(`📂 静态图片服务已启用: /static/images -> ${imagesDir}`);
  console.log(`📂 兼容图片服务已启用: /generated-images -> ${imagesDir}`);
  console.log(`📂 头像HTTP服务已启用: /avatars -> ${avatarsDir}`);
  console.log(`📂 风格图片HTTP服务已启用: /styles -> ${stylesDir}`);
  console.log(`🔧 历史兼容中间件已启用: 自动处理缺少扩展名的图片请求`);

  // --- ComfyUI Proxy Endpoints ---
  // Multer setup for file uploads
  const upload = multer({ storage: multer.memoryStorage() });

  // 🎯 图片文件存储配置 - 用于解决Base64内存泄漏
  const tempImagesDir = path.join(__dirname, '../temp-images');
  if (!fs.existsSync(tempImagesDir)) {
    fs.mkdirSync(tempImagesDir, { recursive: true });
    console.log('📁 已创建临时图片存储目录:', tempImagesDir);
  }

  // 🎯 图片上传API - 将Base64/文件保存为本地文件，返回路径引用
  apiApp.post('/api/upload-image', upload.single('image'), async (req, res) => {
    try {
      let imageBuffer = null;
      let originalName = null;

      if (req.file) {
        // 处理文件上传
        imageBuffer = req.file.buffer;
        originalName = req.file.originalname;
      } else if (req.body.base64Data) {
        // 处理Base64数据
        const base64Data = req.body.base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
        imageBuffer = Buffer.from(base64Data, 'base64');
        originalName = req.body.filename || 'image.png';
      } else {
        return res.status(400).json({ error: '缺少图片数据' });
      }

      // 生成唯一文件名 - 🔧 修复：避免重复.png后缀
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const ext = path.extname(originalName) || '.png';
      const baseName = `image_${timestamp}_${randomStr}`;
      const filename = baseName + ext;
      
      // 🔧 统一持久化：保存到 generated-images 目录而非临时目录
      const filePath = path.join(imagesDir, filename);

      // 保存文件
      await fs.promises.writeFile(filePath, imageBuffer);

      // 返回文件引用信息 - 🔧 修复：使用 generated-images 路径
      const fileReference = {
        filepath: filePath,
        filename: filename,
        url: `/generated-images/${filename}`,
        size: imageBuffer.length,
        timestamp: timestamp,
        originalName: originalName
      };

      console.log(`✅ 图片已保存到统一目录: ${filename} (${imageBuffer.length} bytes)`);
      res.json({
        success: true,
        fileReference: fileReference
      });

    } catch (error) {
      console.error('❌ 图片上传失败:', error);
      res.status(500).json({ error: `图片上传失败: ${error.message}` });
    }
  });

  // 🎯 临时图片访问API - 提供文件访问服务
  apiApp.use('/api/temp-images', express.static(tempImagesDir, {
    setHeaders: (res, path, stat) => {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Cache-Control', 'public, max-age=3600'); // 1小时缓存
    }
  }));

  // 🔑 漫画数据API路由 - 修复局域网访问404问题
  apiApp.get('/api/store/comic-generation-results', (req, res) => {
    try {
      if (!store) return res.status(503).json({ error: 'Store 未初始化' });
      
      const raw = store.get('comic-generation-results', []);
      const arr = Array.isArray(raw) ? raw : Object.values(raw);
      console.log(`📖 [HTTP GET] 返回${arr.length}个作品数据`);
      
      res.json({ 
        success: true, 
        value: arr,
        count: arr.length,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ 获取漫画数据失败:', error);
      res.status(500).json({ error: `获取漫画数据失败: ${error.message}` });
    }
  });

  apiApp.post('/api/store/comic-generation-results', (req, res) => {
    try {
      if (!store) return res.status(503).json({ error: 'Store 未初始化' });
      
      const { value } = req.body;
      if (!Array.isArray(value)) {
        return res.status(400).json({ success: false, error: '数据必须是数组格式' });
      }
      
      store.set('comic-generation-results', value);
      console.log(`💾 [HTTP POST] 保存${value.length}个作品数据`);
      
      res.json({ success: true, count: value.length });
      
      // 通过WebSocket通知所有客户端数据更新
      if (typeof io !== 'undefined' && io.sockets) {
        io.emit('DATA_UPDATED', { 
          type: 'comic-generation-results', 
          count: value.length,
          timestamp: new Date().toISOString()
        });
        console.log(`📡 数据更新已通知${io.sockets.sockets.size}个WebSocket客户端`);
      }
    } catch (error) {
      console.error('❌ 保存漫画数据失败:', error);
      res.status(500).json({ error: `保存漫画数据失败: ${error.message}` });
    }
  });

  // 🎯 图片清理API - 定期清理临时文件
  apiApp.post('/api/cleanup-temp-images', async (req, res) => {
    try {
      const maxAge = req.body.maxAge || 24 * 60 * 60 * 1000; // 默认24小时
      const files = await fs.promises.readdir(tempImagesDir);
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(tempImagesDir, file);
        const stats = await fs.promises.stat(filePath);
        const age = Date.now() - stats.mtime.getTime();

        if (age > maxAge) {
          await fs.promises.unlink(filePath);
          deletedCount++;
        }
      }

      console.log(`🧹 临时图片清理完成: 删除了 ${deletedCount} 个过期文件`);
      res.json({
        success: true,
        deletedCount: deletedCount,
        remainingFiles: files.length - deletedCount
      });

    } catch (error) {
      console.error('❌ 图片清理失败:', error);
      res.status(500).json({ error: `图片清理失败: ${error.message}` });
    }
  });

  apiApp.post('/comic/upload', upload.single('image'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: '缺少图片文件' });
      }

      const comfyUIUploadUrl = "http://localhost:8188/upload/image";
      const formData = new FormData();
      formData.append('image', req.file.buffer, req.file.originalname);
      formData.append('overwrite', 'true');

      const response = await fetch(comfyUIUploadUrl, {
        method: 'POST',
        body: formData,
        // fetch with FormData automatically sets Content-Type: multipart/form-data
        // and the correct boundary
      });

      if (!response.ok) {
        throw new Error(`ComfyUI upload failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      res.json(result);
    } catch (e) {
      console.error('❌ 图片上传代理失败:', e);
      res.status(500).json({ error: `图片上传代理失败: ${e.message}` });
    }
  });

  // 健康检查
  apiApp.get('/api/health', (req, res) => {
    res.json({ status: 'ok', store: 'electron-store' });
  });

  apiApp.get('/comic/status', async (req, res) => {
    try {
      const response = await fetch("http://localhost:8188/system_stats");
      res.json({ comfyui_available: response.ok, backend_status: "running" });
    } catch (e) {
      res.json({ comfyui_available: false, backend_status: "running", message: e.message });
    }
  });

  apiApp.get('/comic/view', async (req, res) => {
    try {
      const params = {
        filename: req.query.filename,
        subfolder: req.query.subfolder || '',
        type: req.query.type || 'output'
      };
      if (!params.filename) {
        return res.status(400).json({ error: '缺少filename参数' });
      }

      const comfyUIUrl = new URL("http://localhost:8188/view");
      for (const key in params) {
        if (params[key]) {
          comfyUIUrl.searchParams.append(key, params[key]);
        }
      }

      const response = await fetch(comfyUIUrl.toString());
      
      if (!response.ok) {
        throw new Error(`ComfyUI view error: ${response.status} ${response.statusText}`);
      }

      res.setHeader('Content-Type', response.headers.get('Content-Type'));
      response.body.pipe(res);
    } catch (e) {
      res.status(500).json({ error: `图片查看代理失败: ${e.message}` });
    }
  });

  // 🌐 网络信息API - 用于动态IP检测
  apiApp.get('/api/network-info', (req, res) => {
    try {
      const os = require('os');
      const networkInterfaces = os.networkInterfaces();
      const privateIPs = [];
      
      // 检测所有私有IP地址
      const privateIPRanges = [
        /^192\.168\./,
        /^10\./,
        /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
        /^169\.254\./
      ];
      
      for (const name of Object.keys(networkInterfaces)) {
        for (const net of networkInterfaces[name] || []) {
          if (net.family === 'IPv4' && !net.internal) {
            if (privateIPRanges.some(range => range.test(net.address))) {
              privateIPs.push({
                interface: name,
                address: net.address,
                netmask: net.netmask,
                mac: net.mac
              });
            }
          }
        }
      }
      
      // 返回第一个找到的私有IP作为localIP，同时返回所有检测到的IP
      const localIP = privateIPs.length > 0 ? privateIPs[0].address : '127.0.0.1';
      
      res.json({
        localIP,
        allPrivateIPs: privateIPs,
        hostname: os.hostname(),
        platform: os.platform(),
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ 获取网络信息失败:', error);
      res.status(500).json({ 
        error: '获取网络信息失败',
        localIP: '127.0.0.1',
        allPrivateIPs: [],
        timestamp: new Date().toISOString()
      });
    }
  });

  // 统一的错误处理函数
  const handleStoreError = (res, error, operation) => {
    console.error(`❌ Store ${operation} 失败:`, error);
    res.status(500).json({ error: `Store ${operation} 失败: ${error.message}` });
  };

  // 获取数据
  apiApp.get('/api/store/get', (req, res) => {
    try {
      if (!store) return res.status(503).json({ error: 'Store 未初始化' });
      const key = decodeURIComponent(req.query.key);
      if (!key) return res.status(400).json({ error: '缺少key参数' });
      
      const value = store.get(key);
      res.json({ key, value, exists: store.has(key), type: typeof value });
    } catch (e) {
      handleStoreError(res, e, 'GET');
    }
  });

  // 设置数据
  apiApp.post('/api/store/set', (req, res) => {
    try {
      if (!store) return res.status(503).json({ error: 'Store 未初始化' });
      const { key, value } = req.body;
      if (!key) return res.status(400).json({ error: '缺少key参数' });
      
      store.set(key, value);
      
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send(`store-changed-${key}`, value);
      });
      
      res.json({ success: true, key, type: typeof value });
    } catch (e) {
      handleStoreError(res, e, 'SET');
    }
  });

  // 🔑 专门处理comic-generation-results的设置API，包含WebSocket推送
  apiApp.post('/api/store/comic-generation-results', (req, res) => {
    try {
      if (!store) return res.status(503).json({ error: 'Store 未初始化' });
      const { value } = req.body;
      if (!value) return res.status(400).json({ error: '缺少value参数' });
      
      // 保存数据到store
      store.set('comic-generation-results', value);
      console.log(`💾 作品数据已保存到Store: ${Array.isArray(value) ? value.length : 0}个作品`);
      
      // 通知Electron窗口
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('store-changed-comic-generation-results', value);
      });
      
      // 🚀 通过WebSocket推送给所有连接的客户端
      if (typeof io !== 'undefined' && io.sockets) {
        const pushData = {
          success: true,
          data: value,
          count: Array.isArray(value) ? value.length : 0,
          timestamp: new Date().toISOString(),
          source: 'http_api'
        };
        
        io.emit('GALLERY_DATA_RESPONSE', pushData);
        console.log(`📡 作品数据已推送给${io.sockets.sockets.size}个WebSocket客户端`);
      }
      
      res.json({ 
        success: true, 
        count: Array.isArray(value) ? value.length : 0,
        message: '作品数据已保存并推送'
      });
    } catch (e) {
      console.error('❌ 设置comic-generation-results失败:', e);
      res.status(500).json({ error: `设置comic-generation-results失败: ${e.message}` });
    }
  });

  // 🔑 专门的GET路由：获取comic-generation-results（修复HTTP 404 fallback）
  apiApp.get('/api/store/comic-generation-results', (req, res) => {
    try {
      if (!store) return res.status(503).json({ error: 'Store 未初始化' });
      
      const value = store.get('comic-generation-results', []);
      console.log(`📖 [HTTP GET] 返回${Array.isArray(value) ? value.length : 0}个作品数据`);
      
      res.json({ 
        success: true,
        value: value,
        count: Array.isArray(value) ? value.length : 0,
        timestamp: new Date().toISOString()
      });
    } catch (e) {
      console.error('❌ 获取comic-generation-results失败:', e);
      res.status(500).json({ error: `获取comic-generation-results失败: ${e.message}` });
    }
  });

  // 删除数据
  apiApp.post('/api/store/delete', (req, res) => {
    try {
      if (!store) return res.status(503).json({ error: 'Store 未初始化' });
      const { key } = req.body;
      if (!key) return res.status(400).json({ error: '缺少key参数' });

      const oldValue = store.get(key);
      store.delete(key);
      
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send(`store-changed-${key}`, undefined, oldValue);
      });
      
      res.json({ success: true, key });
    } catch (e) {
      handleStoreError(res, e, 'DELETE');
    }
  });

  // 🔑 新增：作品图片上传API
  apiApp.post('/api/upload-image', async (req, res) => {
    try {
      const { imageId, base64Data, subPath = 'static/images' } = req.body;
      
      if (!imageId || !base64Data) {
        return res.status(400).json({ error: '缺少imageId或base64Data参数' });
      }

      if (!base64Data.startsWith('data:image/')) {
        return res.status(400).json({ error: '无效的base64图片数据' });
      }

      // 确保目录存在
      const targetDir = path.join(__dirname, '../public', subPath);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // 生成安全的文件名
      const safeFilename = `img_${imageId}.png`;
      const filePath = path.join(targetDir, safeFilename);

      // 将base64数据转换为Buffer并保存
      const base64Content = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
      const buffer = Buffer.from(base64Content, 'base64');

      // 保存文件
      fs.writeFileSync(filePath, buffer);

      // 返回HTTP访问URL
      const publicUrl = `/${subPath}/${safeFilename}`;

      console.log(`✅ 作品图片保存成功: ${filePath} -> ${publicUrl}`);
      res.json({ 
        success: true, 
        url: publicUrl,
        localPath: filePath,
        filename: safeFilename 
      });

    } catch (error) {
      console.error('❌ 保存作品图片失败:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message 
      });
    }
  });

  // 🌐 创建HTTP服务器并添加Socket.IO支持
  const port = 3001;
  const httpServer = http.createServer(apiApp);
  const io = require('socket.io')(httpServer, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"],
      credentials: true
    }
  });

  // 🌟 全局状态存储 - 与 storyStore 保持一致
  let globalState = {
    // 连接状态
    isConnected: true,
    isConnecting: false,
    lastConnectedTime: Date.now(),
    connectionError: null,

    // 主角和故事配置 (来自 StoryInputPanel)
    protagonistName: '',
    protagonistGender: 'male',
    protagonistAge: 'young',
    protagonistImage: null,
    selectedStoryType: null,
    generatedStory: '',

    // 风格配置 (来自 StyleConfigPanel)
    selectedStyle: null,

    // 生成状态与结果 (来自 ComicGenerationStage)
    isGenerating: false,
    generationProgress: 0,
    currentStatusText: '',
    currentSceneIndex: 0,
    finalComicResult: null,

    // 错误状态
    error: null,
    lastError: null,

    // 兼容旧版本的字段
    lastHeartbeat: Date.now(),
    config: {
      selectedCharacter: null,
      storyText: '',
      selectedStyle: 'anime',
      selectedModel: 'flux',
      selectedWorkflow: 'new',
      layouts: {
        columns: 2,
        spacing: 10
      }
    }
  };

  // 🔄 增强客户端连接管理
  const clients = new Map(); // Socket.IO 客户端将通过 io.sockets.sockets 管理

  // 🚨 僵尸连接清理机制 (Socket.IO 内部有心跳，这里可以简化或移除)
  let connectionWatchdog = null; // 暂时保留，后续根据 Socket.IO 行为调整

  // 🌍 多设备协调管理
  const deviceRegistry = new Map(); // 设备注册表
  const resourceLocks = new Map();   // 资源锁定表
  const pendingDecisions = new Map(); // 待决策事件

  // 设备优先级映射
  const getDevicePriority = (deviceType) => {
    const priorities = {
      'electron': 1, // 高优先级
      'browser': 2,  // 中优先级
      'mobile': 3    // 低优先级
    };
    return priorities[deviceType] || 3;
  };

  // 资源类型枚举
  const ResourceType = {
    MICROPHONE: 'microphone',
    SPEAKER: 'speaker',
    CAMERA: 'camera',
    REALTIME_SESSION: 'realtime_session',
    GENERATION_QUEUE: 'generation_queue',
    TTS_ENGINE: 'tts_engine'
  };

  // 启动连接监控 (Socket.IO 内部有心跳，这里可以简化或移除)
  const startConnectionWatchdog = () => {
    if (connectionWatchdog) clearInterval(connectionWatchdog);
    connectionWatchdog = setInterval(() => {
      // Socket.IO 内部处理心跳和断开，这里可以用于更高级的逻辑
      // 例如，检查长时间未活动的设备并清理
      console.log(`[Watchdog] 当前活跃 Socket.IO 客户端: ${io.sockets.sockets.size}`);
    }, 60000); // 每分钟检查一次
  };

  // 停止连接监控
  const stopConnectionWatchdog = () => {
    if (connectionWatchdog) {
      clearInterval(connectionWatchdog);
      connectionWatchdog = null;
    }
  };

  // 📡 广播消息给所有客户端
  const broadcast = (event, payload, excludeSocketId = null) => {
    io.emit(event, payload); // 默认广播给所有连接的客户端
    if (excludeSocketId) {
      // 如果需要排除特定客户端，可以使用 io.except(excludeSocketId).emit(event, payload);
      // 但这里为了简化，先不实现精确排除
    }
  };

  // 📤 发送消息给特定客户端
  const sendToClient = (socketId, event, payload) => {
    io.to(socketId).emit(event, payload);
  };

  // 🔥 Socket.IO 连接处理
  io.on('connection', (socket) => {
    const clientId = socket.id; // Socket.IO 提供了唯一的 socket.id
    const deviceType = socket.handshake.query.deviceType || 'unknown';
    const sessionId = socket.handshake.query.sessionId;

    // 增强客户端信息
    const clientInfo = {
      id: clientId,
      deviceType,
      sessionId,
      connectedAt: new Date(),
      lastSeen: new Date(),
      userAgent: socket.handshake.headers['user-agent'] || 'Unknown',
      ip: socket.handshake.address || 'unknown',
      isRegistered: false
    };
    clients.set(socket, clientInfo); // 仍然使用 Map 存储额外信息

    console.log(`🔗 新 Socket.IO 客户端连接: ${clientId} [${deviceType}] (总计: ${io.sockets.sockets.size})`);

    // 启动连接监控（如果还没启动）
    if (!connectionWatchdog) {
      startConnectionWatchdog();
    }

    // 立即发送完整状态给新连接的客户端
    // 🎯 优化：发送初始状态时过滤Base64数据，避免WebSocket传输大数据
    const filteredInitialState = { ...globalState };
    
    // 过滤主角图片Base64数据
    if (filteredInitialState.protagonistImage && typeof filteredInitialState.protagonistImage === 'string' && filteredInitialState.protagonistImage.startsWith('data:')) {
      console.warn(`⚠️ [${clientId}] 过滤FULL_STATE中的Base64主角图片数据`);
      filteredInitialState.protagonistImage = {
        isPlaceholder: true,
        originalSize: filteredInitialState.protagonistImage.length,
        message: '图片数据过大，请使用文件引用模式',
        timestamp: Date.now()
      };
    }
    
    // 过滤风格图片Base64数据
    if (filteredInitialState.selectedStyle && filteredInitialState.selectedStyle.referenceImage && 
        typeof filteredInitialState.selectedStyle.referenceImage === 'string' && 
        filteredInitialState.selectedStyle.referenceImage.startsWith('data:')) {
      console.warn(`⚠️ [${clientId}] 过滤FULL_STATE中的Base64风格图片数据`);
      filteredInitialState.selectedStyle = {
        ...filteredInitialState.selectedStyle,
        referenceImage: {
          isPlaceholder: true,
          originalSize: filteredInitialState.selectedStyle.referenceImage.length,
          message: '图片数据过大，请使用文件引用模式',
          timestamp: Date.now()
        }
      };
    }
    
    sendToClient(clientId, 'FULL_STATE', {
      payload: filteredInitialState,
      clientId: clientId,
      timestamp: Date.now()
    });

    // 🔑 专门的GET_GALLERY_DATA事件监听器（确保响应）
    socket.on('GET_GALLERY_DATA', () => {
      console.log(`📦 [socket.on] 处理画廊数据请求 [${clientId}]`);
      try {
        const raw = store.get('comic-generation-results', []);
        const arr = Array.isArray(raw) ? raw : Object.values(raw);
        console.log(`📦 [socket.on] 返回${arr.length}个作品数据`);
        
        socket.emit('GALLERY_DATA_RESPONSE', {
          success: true,
          data: arr,
          count: arr.length,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error(`❌ [socket.on] 获取画廊数据失败 [${clientId}]:`, error);
        socket.emit('GALLERY_DATA_RESPONSE', {
          success: false,
          error: error.message,
          data: [],
          count: 0
        });
      }
    });

    // 📨 处理客户端消息
    socket.onAny((event, payload) => {
      console.log(`📨 收到 Socket.IO 消息 [${clientId}]: ${event}`, payload);

      switch (event) {
        case 'ELECTRON_STORE_GET':
          try {
            const { key } = payload;
            const value = store.get(key);
            socket.emit('ELECTRON_STORE_GET_RESPONSE', { success: true, value });
          } catch (error) {
            console.error('ELECTRON_STORE_GET 失败:', error);
            socket.emit('ELECTRON_STORE_GET_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ELECTRON_STORE_SET':
          try {
            const { key, value } = payload;
            store.set(key, value);
            socket.emit('ELECTRON_STORE_SET_RESPONSE', { success: true });
          } catch (error) {
            console.error('ELECTRON_STORE_SET 失败:', error);
            socket.emit('ELECTRON_STORE_SET_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ELECTRON_STORE_DELETE':
          try {
            const { key } = payload;
            store.delete(key);
            socket.emit('ELECTRON_STORE_DELETE_RESPONSE', { success: true });
          } catch (error) {
            console.error('ELECTRON_STORE_DELETE 失败:', error);
            socket.emit('ELECTRON_STORE_DELETE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ELECTRON_STORE_CLEAR':
          try {
            store.clear();
            socket.emit('ELECTRON_STORE_CLEAR_RESPONSE', { success: true });
          } catch (error) {
            console.error('ELECTRON_STORE_CLEAR 失败:', error);
            socket.emit('ELECTRON_STORE_CLEAR_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_AI_PRESETS':
          try {
            // 模拟从文件加载预设
            const presetsPath = path.join(__dirname, '../../chatdata/llm_presets.json');
            let presets = {};
            if (fs.existsSync(presetsPath)) {
              presets = JSON.parse(fs.readFileSync(presetsPath, 'utf8'));
            } else {
              // 提供默认预设
              presets = {
                'gemma3': {
                  temperature: 1.0,
                  topP: 0.95,
                  topK: 64,
                  maxTokens: 8000,
                  description: '默认预设，用于确保应用正常运行'
                },
                '默认参数': {
                  temperature: 0.7,
                  topP: 0.9,
                  topK: 40,
                  maxTokens: 2000,
                  description: '平衡的默认参数设置'
                }
              };
            }
            socket.emit('GET_AI_PRESETS_RESPONSE', { success: true, data: presets });
          } catch (error) {
            console.error('GET_AI_PRESETS 失败:', error);
            socket.emit('GET_AI_PRESETS_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_LLM_PRESET_DETAIL':
          try {
            const { presetName } = payload;
            const presetsPath = path.join(__dirname, '../../chatdata/llm_presets.json');
            let presets = {};
            if (fs.existsSync(presetsPath)) {
              presets = JSON.parse(fs.readFileSync(presetsPath, 'utf8'));
            }
            const preset = presets[presetName];
            if (preset) {
              socket.emit('GET_LLM_PRESET_DETAIL_RESPONSE', { success: true, data: preset });
            } else {
              socket.emit('GET_LLM_PRESET_DETAIL_RESPONSE', { success: false, message: '预设未找到' });
            }
          } catch (error) {
            console.error('GET_LLM_PRESET_DETAIL 失败:', error);
            socket.emit('GET_LLM_PRESET_DETAIL_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GENERATE_TTS_AUDIO':
          try {
            const { text, voiceId, speed, volume, quality, synthesisMode, selectedUserVoice, selectedProjectVoice } = payload;
            // 这里需要调用 Python 后端进行 TTS 生成
            // 暂时模拟返回一个音频URL
            const audioUrl = `data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxPp+PwtmMcBjiR1/LMeSwFJHfH8NQQAoUXrTp66hVFApGn+DyvmMdBTWJ0fLQe`; // 模拟数据
            socket.emit('TTS_AUDIO_GENERATED_RESPONSE', { success: true, data: { audioUrl } });
          } catch (error) {
            console.error('GENERATE_TTS_AUDIO 失败:', error);
            socket.emit('TTS_AUDIO_GENERATED_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_GALLERY_DATA':
          console.log(`📦 处理画廊数据请求 [${clientId}]`);
          try {
            const raw = store.get('comic-generation-results', []);
            const arr = Array.isArray(raw) ? raw : Object.values(raw);
            console.log(`📦 [WebSocket] 返回${arr.length}个作品数据 (原始类型: ${Array.isArray(raw) ? 'array' : 'object'})`);
            
            socket.emit('GALLERY_DATA_RESPONSE', {
              success: true,
              data: arr,
              count: arr.length,
              timestamp: new Date().toISOString()
            });
          } catch (error) {
            console.error(`❌ 获取画廊数据失败 [${clientId}]:`, error);
            socket.emit('GALLERY_DATA_RESPONSE', {
              success: false,
              error: error.message,
              data: [],
              count: 0
            });
          }
          break;
        case 'ELECTRON_STORE_GET':
          try {
            const { key } = payload;
            const value = store.get(key);
            socket.emit('ELECTRON_STORE_GET_RESPONSE', { success: true, value });
          } catch (error) {
            console.error('ELECTRON_STORE_GET 失败:', error);
            socket.emit('ELECTRON_STORE_GET_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ELECTRON_STORE_SET':
          try {
            const { key, value } = payload;
            store.set(key, value);
            socket.emit('ELECTRON_STORE_SET_RESPONSE', { success: true });
          } catch (error) {
            console.error('ELECTRON_STORE_SET 失败:', error);
            socket.emit('ELECTRON_STORE_SET_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ELECTRON_STORE_DELETE':
          try {
            const { key } = payload;
            store.delete(key);
            socket.emit('ELECTRON_STORE_DELETE_RESPONSE', { success: true });
          } catch (error) {
            console.error('ELECTRON_STORE_DELETE 失败:', error);
            socket.emit('ELECTRON_STORE_DELETE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ELECTRON_STORE_CLEAR':
          try {
            store.clear();
            socket.emit('ELECTRON_STORE_CLEAR_RESPONSE', { success: true });
          } catch (error) {
            console.error('ELECTRON_STORE_CLEAR 失败:', error);
            socket.emit('ELECTRON_STORE_CLEAR_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_AI_PRESETS':
          try {
            // 模拟从文件加载预设
            const presetsPath = path.join(__dirname, '../../chatdata/llm_presets.json');
            let presets = {};
            if (fs.existsSync(presetsPath)) {
              presets = JSON.parse(fs.readFileSync(presetsPath, 'utf8'));
            } else {
              // 提供默认预设
              presets = {
                'gemma3': {
                  temperature: 1.0,
                  topP: 0.95,
                  topK: 64,
                  maxTokens: 8000,
                  description: '默认预设，用于确保应用正常运行'
                },
                '默认参数': {
                  temperature: 0.7,
                  topP: 0.9,
                  topK: 40,
                  maxTokens: 2000,
                  description: '平衡的默认参数设置'
                }
              };
            }
            socket.emit('GET_AI_PRESETS_RESPONSE', { success: true, data: presets });
          } catch (error) {
            console.error('GET_AI_PRESETS 失败:', error);
            socket.emit('GET_AI_PRESETS_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_LLM_PRESET_DETAIL':
          try {
            const { presetName } = payload;
            const presetsPath = path.join(__dirname, '../../chatdata/llm_presets.json');
            let presets = {};
            if (fs.existsSync(presetsPath)) {
              presets = JSON.parse(fs.readFileSync(presetsPath, 'utf8'));
            }
            const preset = presets[presetName];
            if (preset) {
              socket.emit('GET_LLM_PRESET_DETAIL_RESPONSE', { success: true, data: preset });
            } else {
              socket.emit('GET_LLM_PRESET_DETAIL_RESPONSE', { success: false, message: '预设未找到' });
            }
          } catch (error) {
            console.error('GET_LLM_PRESET_DETAIL 失败:', error);
            socket.emit('GET_LLM_PRESET_DETAIL_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GENERATE_TTS_AUDIO':
          try {
            const { text, voiceId, speed, volume, quality, synthesisMode, selectedUserVoice, selectedProjectVoice } = payload;
            // 这里需要调用 Python 后端进行 TTS 生成
            // 暂时模拟返回一个音频URL
            const audioUrl = `data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxPp+PwtmMcBjiR1/LMeSwFJHfH8NQQAoUXrTp66hVFApGn+DyvmMdBTWJ0fLQe`; // 模拟数据
            socket.emit('TTS_AUDIO_GENERATED_RESPONSE', { success: true, data: { audioUrl } });
          } catch (error) {
            console.error('GENERATE_TTS_AUDIO 失败:', error);
            socket.emit('TTS_AUDIO_GENERATED_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ACQUIRE_TTS_LOCK':
          try {
            const { clientId, deviceName, operation } = payload;
            // 检查是否已被锁定
            if (ttsLockState.locked && ttsLockState.client_id !== clientId) {
              socket.emit('TTS_LOCK_RESPONSE', { success: false, message: `已被 ${ttsLockState.device_name} 锁定` });
              return;
            }

            // 锁定
            clearTTSLock(); // 清除旧的锁定
            ttsLockState.locked = true;
            ttsLockState.client_id = clientId;
            ttsLockState.device_name = deviceName;
            ttsLockState.operation = operation;
            ttsLockState.locked_at = Date.now();
            ttsLockState.expires_at = Date.now() + 300000; // 5分钟后过期

            // 设置自动解锁定时器
            ttsLockState.timeoutTimer = setTimeout(() => {
              clearTTSLock();
              console.log('🔒 [TTS锁定] 自动解锁');
            }, 300000);

            socket.emit('TTS_LOCK_RESPONSE', { success: true, data: ttsLockState });
          } catch (error) {
            console.error('ACQUIRE_TTS_LOCK 失败:', error);
            socket.emit('TTS_LOCK_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'RELEASE_TTS_LOCK':
          try {
            const { clientId } = payload;
            if (ttsLockState.locked && ttsLockState.client_id === clientId) {
              clearTTSLock();
              socket.emit('TTS_LOCK_RELEASE_RESPONSE', { success: true });
            } else {
              socket.emit('TTS_LOCK_RELEASE_RESPONSE', { success: false, message: '未锁定或非本客户端锁定' });
            }
          } catch (error) {
            console.error('RELEASE_TTS_LOCK 失败:', error);
            socket.emit('TTS_LOCK_RELEASE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'CHECK_TTS_LOCK_STATUS':
          try {
            socket.emit('TTS_LOCK_STATUS_RESPONSE', { success: true, data: ttsLockState });
          } catch (error) {
            console.error('CHECK_TTS_LOCK_STATUS 失败:', error);
            socket.emit('TTS_LOCK_STATUS_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'FORCE_RESET_ORACLE':
          try {
            // 这里需要调用 Python 后端进行强制重置
            // 暂时模拟成功
            socket.emit('FORCE_RESET_ORACLE_RESPONSE', { success: true });
          } catch (error) {
            console.error('FORCE_RESET_ORACLE 失败:', error);
            socket.emit('FORCE_RESET_ORACLE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'STOP_REALTIME_DIALOGUE':
          try {
            // 这里需要调用 Python 后端停止实时对话
            // 暂时模拟成功
            socket.emit('STOP_REALTIME_DIALOGUE_RESPONSE', { success: true });
          } catch (error) {
            console.error('STOP_REALTIME_DIALOGUE 失败:', error);
            socket.emit('STOP_REALTIME_DIALOGUE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'SEND_REALTIME_MESSAGE':
          try {
            const { message, settings, fullSystemPrompt, userInfo, hexagramInfo, topicInfo, tianrenShenying, dialogueHistory } = payload;
            // 这里需要调用 Python 后端进行实时消息处理
            // 暂时模拟返回LLM回复和TTS音频
            socket.emit('llm_response', { response: '这是来自 Electron 后端的模拟回复。' });
            socket.emit('tts_audio', { audioUrl: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxPp+PwtmMcBjiR1/LMeSwFJHfH8NQQAoUXrTp66hVFApGn+DyvmMdBTWJ0fLQe' });
          } catch (error) {
            console.error('SEND_REALTIME_MESSAGE 失败:', error);
            socket.emit('ERROR', { message: error.message });
          }
          break;
        case 'START_REALTIME_DIALOGUE':
          try {
            const { mode, synthesisMode, disableVAD, ttsConfig, llmConfig } = payload;
            // 这里需要调用 Python 后端启动实时对话
            // 暂时模拟成功
            socket.emit('START_REALTIME_DIALOGUE_RESPONSE', { success: true });
          } catch (error) {
            console.error('START_REALTIME_DIALOGUE 失败:', error);
            socket.emit('START_REALTIME_DIALOGUE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'ENABLE_VAD_LISTENING':
          try {
            // 这里需要调用 Python 后端启用 VAD 监听
            // 暂时模拟成功
            socket.emit('ENABLE_VAD_LISTENING_RESPONSE', { success: true });
          } catch (error) {
            console.error('ENABLE_VAD_LISTENING 失败:', error);
            socket.emit('ENABLE_VAD_LISTENING_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'SEND_DIRECT_TTS_MESSAGE':
          try {
            const { message, settings, ttsConfig } = payload;
            // 这里需要调用 Python 后端进行直接 TTS 消息处理
            // 暂时模拟返回LLM回复和TTS音频
            socket.emit('SEND_DIRECT_TTS_MESSAGE_RESPONSE', { success: true, data: { response: '这是来自 Electron 后端的模拟直接TTS回复。' } });
            socket.emit('tts_audio', { audioUrl: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxPp+PwtmMcBjiR1/LMeSwFJHfH8NQQAoUXrTp66hVFApGn+DyvmMdBTWJ0fLQe' });
          } catch (error) {
            console.error('SEND_DIRECT_TTS_MESSAGE 失败:', error);
            socket.emit('SEND_DIRECT_TTS_MESSAGE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'SEND_DIRECT_LLM_MESSAGE':
          try {
            const { message, settings } = payload;
            // 这里需要调用 Python 后端进行直接 LLM 消息处理
            // 暂时模拟返回LLM回复
            socket.emit('SEND_DIRECT_LLM_MESSAGE_RESPONSE', { success: true, data: { response: '这是来自 Electron 后端的模拟直接LLM回复。' } });
          } catch (error) {
            console.error('SEND_DIRECT_LLM_MESSAGE 失败:', error);
            socket.emit('SEND_DIRECT_LLM_MESSAGE_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_AVAILABLE_MODELS':
          try {
            // 模拟返回可用模型列表
            const models = ['lmstudio:gemma-3-4b-it', 'ollama:llama2', 'openai:gpt-3.5-turbo'];
            socket.emit('GET_AVAILABLE_MODELS_RESPONSE', { success: true, data: { models } });
          } catch (error) {
            console.error('GET_AVAILABLE_MODELS 失败:', error);
            socket.emit('GET_AVAILABLE_MODELS_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_VOICE_PROFILES':
          try {
            // 模拟返回音色列表
            const voices = [
              { id: 'zangshixianling', name: '藏识仙灵' },
              { id: 'default_female', name: '默认女声' },
              { id: 'default_male', name: '默认男声' }
            ];
            socket.emit('GET_VOICE_PROFILES_RESPONSE', { success: true, data: voices });
          } catch (error) {
            console.error('GET_VOICE_PROFILES 失败:', error);
            socket.emit('GET_VOICE_PROFILES_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'GET_CHARACTER_LIST':
          try {
            // 模拟返回角色列表
            const characters = [
              { name: '藏识仙灵', systemPrompt: '你是一位深谙周易玄学的神谕大师' },
              { name: 'AI助手', systemPrompt: '你是一位乐于助人的AI助手' }
            ];
            socket.emit('GET_CHARACTER_LIST_RESPONSE', { success: true, data: characters });
          } catch (error) {
            console.error('GET_CHARACTER_LIST 失败:', error);
            socket.emit('GET_CHARACTER_LIST_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'SAVE_LLM_PRESET':
          try {
            const { preset } = payload;
            // 模拟保存预设到文件
            const presetsPath = path.join(__dirname, '../../chatdata/llm_presets.json');
            let presets = {};
            if (fs.existsSync(presetsPath)) {
              presets = JSON.parse(fs.readFileSync(presetsPath, 'utf8'));
            }
            presets[preset.name] = preset;
            fs.writeFileSync(presetsPath, JSON.stringify(presets, null, 2));
            socket.emit('SAVE_LLM_PRESET_RESPONSE', { success: true, message: '预设保存成功' });
          } catch (error) {
            console.error('SAVE_LLM_PRESET 失败:', error);
            socket.emit('SAVE_LLM_PRESET_RESPONSE', { success: false, message: error.message });
          }
          break;
        case 'CROSS_ENV_DATA_REQUEST':
          try {
            const { type, requestId } = payload;
            if (type === 'comics') {
              const comics = store.get('comic-generation-results', []);
              socket.emit('CROSS_ENV_DATA_RESPONSE', { success: true, comics, requestId });
            } else {
              socket.emit('CROSS_ENV_DATA_RESPONSE', { success: false, message: '不支持的数据类型', requestId });
            }
          } catch (error) {
            console.error('CROSS_ENV_DATA_REQUEST 失败:', error);
            socket.emit('CROSS_ENV_DATA_RESPONSE', { success: false, message: error.message, requestId });
          }
          break;
        case 'SAVE_AVATAR_IMAGE':
          try {
            const { avatarId, base64Data } = payload;
            const avatarsDir = path.join(__dirname, '../public/avatars');
            
            // 确保头像目录存在
            if (!fs.existsSync(avatarsDir)) {
              fs.mkdirSync(avatarsDir, { recursive: true });
            }
            
            // 生成安全的文件名
            const safeFilename = `avatar_${avatarId}.png`;
            const filePath = path.join(avatarsDir, safeFilename);
            
            // 将base64数据转换为Buffer并保存
            const base64Content = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
            const buffer = Buffer.from(base64Content, 'base64');
            
            // 保存文件
            fs.writeFileSync(filePath, buffer);
            
            // 返回HTTP访问URL
            const publicUrl = `/avatars/${safeFilename}`;
            
            console.log(`✅ WebSocket保存头像成功: ${filePath} -> ${publicUrl}`);
            socket.emit('SAVE_AVATAR_IMAGE_RESPONSE', { 
              success: true, 
              url: publicUrl,
              localPath: filePath,
              filename: safeFilename 
            });
            
          } catch (error) {
            console.error('❌ WebSocket保存头像失败:', error);
            socket.emit('SAVE_AVATAR_IMAGE_RESPONSE', { 
              success: false, 
              error: error.message 
            });
          }
          break;
        case 'SAVE_STYLE_IMAGE':
          try {
            const { styleId, base64Data } = payload;
            const stylesDir = path.join(__dirname, '../public/styles');
            
            // 确保风格目录存在
            if (!fs.existsSync(stylesDir)) {
              fs.mkdirSync(stylesDir, { recursive: true });
            }
            
            // 生成安全的文件名
            const safeFilename = `style_${styleId}.png`;
            const filePath = path.join(stylesDir, safeFilename);
            
            // 将base64数据转换为Buffer并保存
            const base64Content = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
            const buffer = Buffer.from(base64Content, 'base64');
            
            // 保存文件
            fs.writeFileSync(filePath, buffer);
            
            // 返回HTTP访问URL
            const publicUrl = `/styles/${safeFilename}`;
            
            console.log(`✅ WebSocket保存风格图片成功: ${filePath} -> ${publicUrl}`);
            socket.emit('SAVE_STYLE_IMAGE_RESPONSE', { 
              success: true, 
              url: publicUrl,
              localPath: filePath,
              filename: safeFilename 
            });
            
          } catch (error) {
            console.error('❌ WebSocket保存风格图片失败:', error);
            socket.emit('SAVE_STYLE_IMAGE_RESPONSE', { 
              success: false, 
              error: error.message 
            });
          }
          break;
        case 'COPY_IMAGE_TO_STYLES':
          try {
            const { sourcePath, newFilename } = payload;
            console.log(`📁 处理图片复制请求: ${sourcePath} -> ${newFilename}`);
            
            const stylesDir = path.join(__dirname, '../public/styles');
            
            // 确保styles目录存在
            if (!fs.existsSync(stylesDir)) {
              fs.mkdirSync(stylesDir, { recursive: true });
            }
            
            // 处理源文件路径
            let sourceFullPath;
            if (sourcePath.startsWith('/')) {
              // 相对路径，基于public目录
              sourceFullPath = path.join(__dirname, '../public', sourcePath);
            } else {
              // 绝对路径
              sourceFullPath = sourcePath;
            }
            
            const destPath = path.join(stylesDir, newFilename);
            
            // 检查源文件是否存在
            if (!fs.existsSync(sourceFullPath)) {
              throw new Error(`源文件不存在: ${sourceFullPath}`);
            }
            
            // 复制文件
            fs.copyFileSync(sourceFullPath, destPath);
            
            // 返回相对路径URL
            const publicUrl = `/styles/${newFilename}`;
            
            console.log(`✅ 文件复制成功: ${sourceFullPath} -> ${destPath}`);
            socket.emit('COPY_IMAGE_TO_STYLES_RESPONSE', { 
              success: true, 
              url: publicUrl,
              localPath: destPath,
              filename: newFilename 
            });
            
          } catch (error) {
            console.error('❌ 文件复制失败:', error);
            socket.emit('COPY_IMAGE_TO_STYLES_RESPONSE', { 
              success: false, 
              error: error.message 
            });
          }
          break;
        // 🔧 统一持久化：漫画数据同步
        case 'ADD_COMIC':
          try {
            const { comic } = payload;
            console.log('📚 收到漫画数据同步请求:', comic?.id || 'undefined');
            
            if (!comic || !comic.id) {
              socket.emit('ADD_COMIC_RESPONSE', { 
                success: false, 
                error: '漫画数据无效' 
              });
              return;
            }
            
            // 获取现有的漫画列表
            const existingComics = store.get('comic-generation-results', []);
            const comicList = Array.isArray(existingComics) ? existingComics : [];
            
            // 检查是否已存在相同ID的漫画
            const existingIndex = comicList.findIndex(c => c.id === comic.id);
            
            if (existingIndex !== -1) {
              // 更新现有漫画
              comicList[existingIndex] = comic;
              console.log(`📝 更新现有漫画: ${comic.id}`);
            } else {
              // 添加新漫画到列表开头
              comicList.unshift(comic);
              console.log(`➕ 添加新漫画: ${comic.id}`);
            }
            
            // 保存到Electron Store
            store.set('comic-generation-results', comicList);
            
            // 响应客户端
            socket.emit('ADD_COMIC_RESPONSE', { 
              success: true, 
              comicId: comic.id,
              totalCount: comicList.length
            });
            
            // 通过WebSocket广播数据更新通知
            io.emit('DATA_UPDATED', {
              type: 'comic-generation-results',
              action: existingIndex !== -1 ? 'update' : 'add',
              comicId: comic.id,
              totalCount: comicList.length,
              timestamp: new Date().toISOString()
            });
            
            console.log(`✅ 漫画数据同步完成: ${comic.id} (总数: ${comicList.length})`);
            
          } catch (error) {
            console.error('❌ 漫画数据同步失败:', error);
            socket.emit('ADD_COMIC_RESPONSE', { 
              success: false, 
              error: error.message 
            });
          }
          break;
        // 🔐 增强客户端注册
        case 'CLIENT_REGISTER':
          const clientInfo = clients.get(socket);
          if (clientInfo && payload.clientInfo) {
            Object.assign(clientInfo, {
              ...payload.clientInfo,
              isRegistered: true,
              capabilities: payload.capabilities || {},
              lastSeen: new Date(),
              priority: getDevicePriority(payload.clientInfo.deviceType),
              occupiedResources: [],
              currentPage: 'unknown'
            });

            deviceRegistry.set(clientInfo.id, {
              deviceId: clientInfo.id,
              deviceType: clientInfo.deviceType,
              currentPage: 'unknown',
              priority: clientInfo.priority,
              occupiedResources: [],
              isActive: true,
              lastActivity: Date.now(),
              capabilities: payload.capabilities || {},
              socket: socket // 存储 socket 引用
            });

            console.log(`🔐 客户端已注册: ${clientInfo.id} [${clientInfo.deviceType}] 优先级:${clientInfo.priority}`);

            sendToClient(clientId, 'CLIENT_REGISTERED', {
              success: true,
              serverId: `server_${Date.now()}`,
              serverTime: Date.now(),
              deviceRegistry: Array.from(deviceRegistry.values()).map(d => ({
                deviceId: d.deviceId,
                deviceType: d.deviceType,
                currentPage: d.currentPage,
                priority: d.priority,
                isActive: d.isActive,
                lastActivity: d.lastActivity
              })),
              resourceLocks: Array.from(resourceLocks.entries())
            });

            broadcast('DEVICE_JOINED', {
              device: {
                deviceId: clientInfo.id,
                deviceType: clientInfo.deviceType,
                priority: clientInfo.priority
              }
            }, clientId); // 广播给其他客户端
          }
          break;

        // 💓 心跳检测 (Socket.IO 内部处理，这里可以用于额外逻辑)
        case 'ping':
        case 'HEARTBEAT':
        case 'HEARTBEAT_PING':
          const client = clients.get(socket);
          if (client) {
            client.lastSeen = new Date();
          }
          globalState.lastHeartbeat = Date.now();
          sendToClient(clientId, 'pong', { timestamp: Date.now() });
          break;

        // 🔌 客户端主动断开 (Socket.IO 内部处理，这里可以用于额外逻辑)
        case 'CLIENT_DISCONNECT':
          console.log(`🔌 客户端主动断开: ${clientId} (原因: ${payload.reason || 'unknown'})`);
          socket.disconnect(true); // 优雅断开
          break;

        // 📊 请求完整状态
        case 'GET_CURRENT_STATE':
          sendToClient(clientId, 'FULL_STATE', {
            payload: globalState,
            timestamp: Date.now()
          });
          break;

        // ⚙️ 更新配置
        case 'UPDATE_CONFIG':
          if (payload.config || payload) {
            const configData = payload.config || payload;
            console.log(`⚙️ 收到配置更新 [${clientId}]:`, configData);

            // 更新主角和故事配置
            if (configData.protagonistName !== undefined) {
              globalState.protagonistName = configData.protagonistName;
            }
            if (configData.protagonistGender !== undefined) {
              globalState.protagonistGender = configData.protagonistGender;
            }
            if (configData.protagonistAge !== undefined) {
              globalState.protagonistAge = configData.protagonistAge;
            }
            if (configData.protagonistImage !== undefined) {
              // 🎯 优化：处理图片文件引用，避免在状态同步中传输Base64数据
              if (typeof configData.protagonistImage === 'object' && configData.protagonistImage.filepath) {
                // 只保存文件引用信息，不保存Base64数据
                globalState.protagonistImage = {
                  filename: configData.protagonistImage.filename,
                  url: configData.protagonistImage.url,
                  timestamp: configData.protagonistImage.timestamp,
                  isFileReference: true // 标记为文件引用
                };
                console.log('✅ 主角图片已更新为文件引用模式:', globalState.protagonistImage.filename);
              } else if (typeof configData.protagonistImage === 'string' && configData.protagonistImage.startsWith('data:')) {
                // 🚫 严格禁止Base64数据广播：检测到Base64数据时，创建占位符
                console.warn('⚠️ 检测到Base64主角图片数据，将创建占位符避免WebSocket传输');
                globalState.protagonistImage = {
                  isPlaceholder: true,
                  originalSize: configData.protagonistImage.length,
                  message: '图片数据过大，请使用文件引用模式',
                  timestamp: Date.now()
                };
                console.log(`🚫 已拒绝Base64广播，原始大小: ${configData.protagonistImage.length} bytes`);
              } else {
                // 其他情况的兼容处理
                globalState.protagonistImage = configData.protagonistImage;
              }
            }
            if (configData.selectedStoryType !== undefined) {
              globalState.selectedStoryType = configData.selectedStoryType;
            }
            if (configData.generatedStory !== undefined) {
              globalState.generatedStory = configData.generatedStory;
            }

            // 更新风格配置
            if (configData.selectedStyle !== undefined) {
              // 🎯 优化：处理风格图片中的Base64数据，避免在状态同步中传输
              if (configData.selectedStyle && typeof configData.selectedStyle === 'object') {
                const cleanedStyle = { ...configData.selectedStyle };
                
                // 检查风格对象中的图片字段
                if (cleanedStyle.referenceImage && typeof cleanedStyle.referenceImage === 'string' && cleanedStyle.referenceImage.startsWith('data:')) {
                  console.warn('⚠️ 检测到风格图片Base64数据，将创建占位符避免WebSocket传输');
                  cleanedStyle.referenceImage = {
                    isPlaceholder: true,
                    originalSize: cleanedStyle.referenceImage.length,
                    message: '图片数据过大，请使用文件引用模式',
                    timestamp: Date.now()
                  };
                  console.log(`🚫 已拒绝风格图片Base64广播，原始大小: ${configData.selectedStyle.referenceImage.length} bytes`);
                }
                
                globalState.selectedStyle = cleanedStyle;
              } else {
                globalState.selectedStyle = configData.selectedStyle;
              }
            }

            console.log(`✅ 配置已更新 [${clientId}]`);

            broadcast('STATE_UPDATED', {
              protagonistName: globalState.protagonistName,
              protagonistGender: globalState.protagonistGender,
              protagonistAge: globalState.protagonistAge,
              protagonistImage: globalState.protagonistImage,
              selectedStoryType: globalState.selectedStoryType,
              generatedStory: globalState.generatedStory,
              selectedStyle: globalState.selectedStyle
            }, clientId);
          }
          break;

        // 🎯 开始生成 (这里将是新的 ComfyUI 逻辑)
        case 'START_GENERATION':
          if (!globalState.isGenerating) {
            globalState.isGenerating = true;
            globalState.generationProgress = 0;
            globalState.currentStatusText = '初始化生成...';
            globalState.finalComicResult = null;

            console.log(`🎯 开始生成 [${clientId}]，参数:`, payload);
            broadcast('GENERATION_STARTED', { ...globalState }, clientId);

            startComfyUIGeneration(payload, (progress, statusText, sceneIndex) => {
              globalState.generationProgress = progress;
              globalState.currentStatusText = statusText;
              globalState.currentSceneIndex = sceneIndex;
              broadcast('GENERATION_PROGRESS', { progress, statusText, sceneIndex });
            })
              .then((result) => {
                console.log('✅ ComfyUI生成完成:', result);
                globalState.isGenerating = false;
                globalState.generationProgress = 100;
                globalState.currentStatusText = '生成完成';
                globalState.finalComicResult = result;
                const existingComics = store.get('comic-generation-results', []);
                const updatedComics = [result, ...existingComics];
                store.set('comic-generation-results', updatedComics);
                console.log('✅ 漫画已保存到 electron-store');
                // 🔑 关键修复：增强调试信息，确保前端收到完整数据
                console.log('📡 发送 GENERATION_COMPLETE 事件，结果概览:');
                console.log('  - 结果ID:', result.id);
                console.log('  - 图片数量:', result.images?.length || 0);
                console.log('  - 主要图片URL:', result.finalComicUrl);
                console.log('  - 完整图片URLs:', result.images);
                
                broadcast('GENERATION_COMPLETE', { result });
              })
              .catch((error) => {
                console.error('❌ ComfyUI生成失败:', error);
                globalState.isGenerating = false;
                globalState.generationProgress = 0;
                globalState.currentStatusText = '';
                globalState.finalComicResult = null;
                broadcast('GENERATION_ERROR', { message: error.message || '生成失败' });
              });
          } else {
            sendToClient(clientId, 'GENERATION_ERROR', { message: '当前已有生成任务在进行中' });
          }
          break;

        case 'DEVICE_REGISTER':
        case 'register-device':
          const deviceInfo = payload.deviceInfo || payload;
          const existingDevice = deviceRegistry.get(deviceInfo.id);
          
          const newDeviceData = {
            ...(existingDevice || {}),
            ...deviceInfo,
            socketId: clientId,
            lastActivity: Date.now(),
            priority: getDevicePriority(deviceInfo.type)
          };
          
          deviceRegistry.set(deviceInfo.id, newDeviceData);
          console.log(`🔐 客户端已注册/更新: ${deviceInfo.id} [${deviceInfo.type}]`);
          
          broadcast('DEVICE_LIST_UPDATED', Array.from(deviceRegistry.values()).map(d => ({
            deviceId: d.id,
            deviceType: d.type,
            isActive: d.isActive,
            name: d.name
          })));
          break;

        case 'DEVICE_ACTIVATE':
          const { deviceId: activatedDeviceId } = payload;
          console.log(`👑 设备激活请求: ${activatedDeviceId}`);
          deviceRegistry.forEach((device, id) => {
            device.isActive = (id === activatedDeviceId);
          });
          broadcast('DEVICE_STATE_SYNC', { activeDeviceId: activatedDeviceId });
          break;

        case 'parameter-sync':
          console.log(`🔄 收到参数同步 [${clientId}]:`, payload.parameters);
          broadcast('parameter-sync', payload, clientId);
          break;

        case 'REQUEST_GENERATION_STATE':
          // 🎯 优化：发送状态时过滤Base64数据，避免WebSocket传输大数据
          const filteredGlobalState = { ...globalState };
          
          // 过滤主角图片Base64数据
          if (filteredGlobalState.protagonistImage && typeof filteredGlobalState.protagonistImage === 'string' && filteredGlobalState.protagonistImage.startsWith('data:')) {
            console.warn(`⚠️ [${clientId}] 过滤REQUEST_GENERATION_STATE中的Base64主角图片数据`);
            filteredGlobalState.protagonistImage = {
              isPlaceholder: true,
              originalSize: filteredGlobalState.protagonistImage.length,
              message: '图片数据过大，请使用文件引用模式',
              timestamp: Date.now()
            };
          }
          
          // 过滤风格图片Base64数据
          if (filteredGlobalState.selectedStyle && filteredGlobalState.selectedStyle.referenceImage && 
              typeof filteredGlobalState.selectedStyle.referenceImage === 'string' && 
              filteredGlobalState.selectedStyle.referenceImage.startsWith('data:')) {
            console.warn(`⚠️ [${clientId}] 过滤REQUEST_GENERATION_STATE中的Base64风格图片数据`);
            filteredGlobalState.selectedStyle = {
              ...filteredGlobalState.selectedStyle,
              referenceImage: {
                isPlaceholder: true,
                originalSize: filteredGlobalState.selectedStyle.referenceImage.length,
                message: '图片数据过大，请使用文件引用模式',
                timestamp: Date.now()
              }
            };
          }
          
          sendToClient(clientId, 'GENERATION_STATE_SYNC', { state: filteredGlobalState });
          break;

        case 'GENERATION_STATE_SYNC':
          // 🎯 优化：处理来自前端的状态同步消息（文件引用模式）
          console.log(`🔄 收到生成状态同步 [${clientId}]:`, payload.state);
          
          // 🎯 优化：过滤和验证状态中的图片数据
          if (payload.state) {
            const filteredState = { ...payload.state };
            
            // 处理主角图片
            if (filteredState.protagonistImage) {
              if (typeof filteredState.protagonistImage === 'string' && filteredState.protagonistImage.startsWith('data:')) {
                console.warn(`⚠️ [${clientId}] 检测到Base64主角图片数据，将创建占位符避免WebSocket传输`);
                filteredState.protagonistImage = {
                  isPlaceholder: true,
                  originalSize: filteredState.protagonistImage.length,
                  message: '图片数据过大，请使用文件引用模式',
                  timestamp: Date.now()
                };
                console.log(`🚫 [${clientId}] 已拒绝Base64广播，原始大小: ${filteredState.protagonistImage.length} bytes`);
              } else if (filteredState.protagonistImage.isFileReference) {
                console.log(`✅ [${clientId}] 主角图片使用文件引用模式: ${filteredState.protagonistImage.filename}`);
              }
            }
            
            // 处理风格图片
            if (filteredState.styleImage) {
              if (typeof filteredState.styleImage === 'string' && filteredState.styleImage.startsWith('data:')) {
                console.warn(`⚠️ [${clientId}] 检测到Base64风格图片数据，将创建占位符避免WebSocket传输`);
                filteredState.styleImage = {
                  isPlaceholder: true,
                  originalSize: filteredState.styleImage.length,
                  message: '图片数据过大，请使用文件引用模式',
                  timestamp: Date.now()
                };
                console.log(`🚫 [${clientId}] 已拒绝风格图片Base64广播，原始大小: ${filteredState.styleImage.length} bytes`);
              } else if (filteredState.styleImage.isFileReference) {
                console.log(`✅ [${clientId}] 风格图片使用文件引用模式: ${filteredState.styleImage.filename}`);
              }
            }
            
            // 广播状态给其他设备（只广播文件引用，不广播Base64数据）
            broadcast('GENERATION_STATE_SYNC', { 
              state: filteredState,
              deviceId: payload.deviceId,
              timestamp: payload.timestamp
            }, clientId);
          }
          break;


        default:
          console.warn(`⚠️ 未知消息类型 [${clientId}]: ${event}`);
          sendToClient(clientId, 'ERROR', { message: `未知消息类型: ${event}` });
      }
    });

    // 🔌 Socket.IO 断开连接处理
    socket.on('disconnect', (reason) => {
      const clientInfo = clients.get(socket);
      if (clientInfo) {
        console.log(`🔌 Socket.IO 客户端断开连接: ${clientInfo.id} [${clientInfo.deviceType}] (原因: ${reason}) (总计: ${io.sockets.sockets.size})`);

        const device = deviceRegistry.get(clientInfo.id);
        if (device) {
          device.occupiedResources.forEach(resource => {
            const lock = resourceLocks.get(resource);
            if (lock && lock.ownerDeviceId === clientInfo.id) {
              resourceLocks.delete(resource);
              console.log(`🔓 自动释放资源: ${resource}`);
            }
          });
          deviceRegistry.delete(clientInfo.id);
          broadcast('DEVICE_LEFT', {
            deviceId: clientInfo.id,
            deviceType: clientInfo.deviceType,
            releasedResources: device.occupiedResources
          }, clientId);
        }
        clients.delete(socket);
        if (io.sockets.sockets.size === 0) {
          console.log('📴 所有 Socket.IO 客户端已断开，停止连接监控');
          stopConnectionWatchdog();
        }
      }
    });

    // ❌ Socket.IO 错误处理
    socket.on('error', (error) => {
      const clientInfo = clients.get(socket);
      console.error(`❌ Socket.IO 错误 [${clientInfo?.id || 'unknown'}]:`, error.message);
      clients.delete(socket);
    });
  });

  // 🎯 ComfyUI生成处理函数
  
  /**
   * 启动ComfyUI生成任务 (Node.js 实现)
   * @param {object} generationParams - 生成参数
   * @param {function} onProgress - 进度回调函数 (progress, statusText, sceneIndex)
   */
  const startComfyUIGeneration = async (generationParams, onProgress) => {
    console.log('🚀 启动ComfyUI生成任务 (Node.js)，参数:', generationParams);
    
    // 🔍 [DEBUG] 详细参数分析
    console.log('🔍 [DEBUG] 接收到的完整参数:', JSON.stringify(generationParams, null, 2));
    console.log('🔍 [DEBUG] newWorkflowParams:', generationParams?.newWorkflowParams);
    console.log('🔍 [DEBUG] generatedStory:', generationParams?.generatedStory);
    console.log('🔍 [DEBUG] protagonistImage存在:', !!generationParams?.protagonistImage);
    console.log('🔍 [DEBUG] styleImage存在:', !!generationParams?.styleImage);

    const comfyuiUrl = 'http://127.0.0.1:8188';
    const promptApiUrl = `${comfyuiUrl}/prompt`;

    try {
      // 1. 加载 ComfyUI 工作流
      const workflowPath = path.join(__dirname, '../src/components/modules/漫画生成/新漫画藏识漫画制作api.json');
      let baseWorkflow;

      try {
        const workflowContent = fs.readFileSync(workflowPath, 'utf8');
        baseWorkflow = JSON.parse(workflowContent);
        console.log('✅ 成功加载ComfyUI工作流:', workflowPath);
      } catch (error) {
        console.error('❌ 加载工作流失败:', error, '路径:', workflowPath);
        throw new Error('无法加载ComfyUI工作流配置');
      }

      // 2. 修改工作流中的动态参数
      const clientId = generationParams?.clientId || `client_${Date.now()}`;
      const workflowPayload = {
        prompt: baseWorkflow,
        client_id: clientId
      };

      // 🔑 修复：构建故事提示词（已组合名字、性别、故事类型）传递给节点103
      if (workflowPayload.prompt["103"] && workflowPayload.prompt["103"].inputs) {
        // 🔍 详细调试故事提示词来源
        console.log('🔍 [STORY DEBUG] 故事提示词构建分析:');
        console.log('  - generationParams.generatedStory存在:', !!generationParams?.generatedStory);
        console.log('  - generatedStory长度:', generationParams?.generatedStory?.length || 0);
        console.log('  - generatedStory内容:', generationParams?.generatedStory || '无内容');
        console.log('  - protagonistName:', generationParams?.protagonistName);
        console.log('  - protagonistGender:', generationParams?.protagonistGender);
        console.log('  - storyType:', generationParams?.storyType);
        
        // 🔑 关键修复：强制要求使用前端传递的完整故事
        if (!generationParams?.generatedStory || !generationParams.generatedStory.trim()) {
          console.error('❌ 严重错误：前端未传递故事内容！');
          console.error('  - generationParams.generatedStory:', generationParams?.generatedStory);
          throw new Error('故事内容缺失：前端必须传递完整的故事内容，不允许使用后备逻辑');
        }
        
        const storyPrompt = generationParams.generatedStory;
        console.log('✅ 使用前端传递的完整故事:', storyPrompt.substring(0, 100) + '...');
        
        // 🚨 关键修复：使用正确的字段名 "故事提示词" 而不是 "故事预览"
        workflowPayload.prompt["103"].inputs.故事提示词 = storyPrompt;
        // 🔑 关键修复：为故事生成器（节点103）设置随机种子，避免ComfyUI缓存
        const randomStorySeed = Math.floor(Math.random() * 1000000000000000);
        workflowPayload.prompt["103"].inputs.随机种子 = randomStorySeed;
        console.log(`✅ 已更新故事提示词到节点103: ${storyPrompt.substring(0, 50)}...`);
        console.log(`✅ 已设置随机种子: ${randomStorySeed}`);
      }

      // 🔑 关键修复：为图像生成器（节点72）设置随机种子，避免ComfyUI缓存
      if (workflowPayload.prompt["72"] && workflowPayload.prompt["72"].inputs) {
        const randomSeed = Math.floor(Math.random() * 1000000000000000); // 生成一个大范围的随机数
        workflowPayload.prompt["72"].inputs.seed = randomSeed;
        console.log(`✅ 已为图像生成器（节点72）设置随机种子: ${randomSeed}`);
      }

      // 🎯 前端已经负责上传主角图片到ComfyUI，后端只需要使用前端返回的文件名
      if (workflowPayload.prompt["42"]) {
        try {
          let protagonistFileName = null;
          
          // 优先使用 protagonistImageFileName  
          if (generationParams?.protagonistImageFileName) {
            protagonistFileName = generationParams.protagonistImageFileName;
            console.log('📝 使用 protagonistImageFileName:', protagonistFileName);
          } 
          // 如果 protagonistImage 是文件名格式（不是HTTP URL），也可以使用
          else if (generationParams?.protagonistImage && 
                   typeof generationParams.protagonistImage === 'string' && 
                   !generationParams.protagonistImage.startsWith('http://') && 
                   !generationParams.protagonistImage.startsWith('https://') &&
                   !generationParams.protagonistImage.startsWith('data:')) {
            protagonistFileName = generationParams.protagonistImage;
            console.log('📝 使用 protagonistImage 文件名:', protagonistFileName);
          }
          
          if (protagonistFileName) {
            workflowPayload.prompt["42"].inputs.image = protagonistFileName;
            console.log('✅ 已更新主角图片到节点42:', protagonistFileName);
          } else {
            console.warn('⚠️ 未找到有效的主角图片文件名，跳过配置');
          }
        } catch (imageError) {
          console.warn('⚠️ 配置主角图片节点失败:', imageError);
        }
      }

      // 🎯 前端已经负责上传风格图片到ComfyUI，后端只需要使用前端返回的文件名
      if (workflowPayload.prompt["51"]) {
        try {
          let styleFileName = null;
          
          // 优先使用 styleImageFileName
          if (generationParams?.styleImageFileName) {
            styleFileName = generationParams.styleImageFileName;
            console.log('📝 使用 styleImageFileName:', styleFileName);
          } 
          // 如果 styleImage 是文件名格式（不是HTTP URL），也可以使用
          else if (generationParams?.styleImage && 
                   typeof generationParams.styleImage === 'string' && 
                   !generationParams.styleImage.startsWith('http://') && 
                   !generationParams.styleImage.startsWith('https://') &&
                   !generationParams.styleImage.startsWith('data:')) {
            styleFileName = generationParams.styleImage;
            console.log('📝 使用 styleImage 文件名:', styleFileName);
          }
          
          if (styleFileName) {
            workflowPayload.prompt["51"].inputs.image = styleFileName;
            console.log('✅ 已更新风格图片到51号Load Image Prompt节点:', styleFileName);
          } else {
            console.warn('⚠️ 未找到有效的风格图片文件名，跳过配置');
          }
        } catch (styleImageError) {
          console.warn('⚠️ 配置风格图片节点失败:', styleImageError);
        }
      }

      // 🔑 修复：强制要求风格配置参数
      if (!generationParams?.visualStylePreference) {
        console.error('❌ 严重错误：前端未传递视觉风格偏好参数！');
        throw new Error('风格配置缺失：前端必须传递完整的风格配置');
      }
      
      if (!generationParams?.globalStyleDescription) {
        console.error('❌ 严重错误：前端未传递全局风格描述参数！');
        throw new Error('风格描述缺失：前端必须传递完整的风格描述');
      }

      if (workflowPayload.prompt["106"]) {
        workflowPayload.prompt["106"].inputs.视觉风格偏好 = generationParams.visualStylePreference;
        console.log('✅ 已更新106号节点视觉风格偏好:', generationParams.visualStylePreference);
      }

      if (workflowPayload.prompt["80"]) {
        workflowPayload.prompt["80"].inputs.全局风格描述 = generationParams.globalStyleDescription;
        console.log('✅ 已更新80号节点全局风格描述:', generationParams.globalStyleDescription);
      }

      // 🔑 关键修复：强制要求新工作流控制参数
      if (!generationParams?.newWorkflowParams) {
        console.error('❌ 严重错误：前端未传递新工作流控制参数！');
        throw new Error('控制参数缺失：前端必须传递完整的控制参数');
      }

      const { fusionStrength, pulidWeight, conditioningStrength, steps } = generationParams.newWorkflowParams;
      console.log('🎛️ 处理新工作流控制参数:', generationParams.newWorkflowParams);
      
      // 90号节点：风格强度 - 使用正确字段名 "fusion_strength"
      if (workflowPayload.prompt["90"] && workflowPayload.prompt["90"].inputs && fusionStrength !== undefined) {
        workflowPayload.prompt["90"].inputs.fusion_strength = fusionStrength;
        console.log('✅ 已更新90号节点风格强度(fusion_strength):', fusionStrength);
      }
      
      // 38号节点：人像权重 - 需要检查实际字段名
      if (workflowPayload.prompt["38"] && workflowPayload.prompt["38"].inputs && pulidWeight !== undefined) {
        workflowPayload.prompt["38"].inputs.weight = pulidWeight;
        console.log('✅ 已更新38号节点人像权重(weight):', pulidWeight);
      }
      
      // 72号节点：生成步数 - 使用正确字段名 "steps"
      if (workflowPayload.prompt["72"] && workflowPayload.prompt["72"].inputs && steps !== undefined) {
        workflowPayload.prompt["72"].inputs.steps = steps;
        console.log('✅ 已更新72号节点生成步数(steps):', steps);
      }

      // 🔍 最终验证：输出关键节点的参数值
      console.log('🔍 [FINAL CHECK] 最终发送到ComfyUI的关键参数:');
      console.log('  - 节点103故事提示词:', workflowPayload.prompt["103"]?.inputs?.故事提示词?.substring(0, 100) + '...');
      console.log('  - 节点42主角图片:', workflowPayload.prompt["42"]?.inputs?.image);
      console.log('  - 节点51风格图片:', workflowPayload.prompt["51"]?.inputs?.image);
      console.log('  - 节点90风格强度:', workflowPayload.prompt["90"]?.inputs?.fusion_strength);
      console.log('  - 节点38人像权重:', workflowPayload.prompt["38"]?.inputs?.weight);
      console.log('  - 节点72生成步数:', workflowPayload.prompt["72"]?.inputs?.steps);
      console.log('  - 节点106视觉风格偏好:', workflowPayload.prompt["106"]?.inputs?.视觉风格偏好);
      console.log('  - 节点80全局风格描述:', workflowPayload.prompt["80"]?.inputs?.全局风格描述);
      
      console.log('📤 发送完整工作流到ComfyUI...');

      // 3. 提交任务到 ComfyUI
      const response = await fetch(promptApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workflowPayload)
      });

      if (!response.ok) {
        throw new Error(`ComfyUI API错误: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const promptId = result.prompt_id;
      if (!promptId) {
        throw new Error('ComfyUI 未返回 prompt_id');
      }
      console.log('✅ ComfyUI任务提交成功, Prompt ID:', promptId);

      // 4. 监控 ComfyUI 进度
      const historyApiUrl = `${comfyuiUrl}/history/${promptId}`;
      const timeoutSeconds = 600; // 10分钟超时
      const startTime = Date.now();

      while (true) {
        if (Date.now() - startTime > timeoutSeconds * 1000) {
          throw new Error('ComfyUI 任务超时');
        }

        await new Promise(resolve => setTimeout(resolve, 2000)); // 每2秒轮询一次

        const historyResponse = await fetch(historyApiUrl);
        if (!historyResponse.ok) {
          throw new Error(`ComfyUI 历史 API 错误: ${historyResponse.status} ${historyResponse.statusText}`);
        }

        const history = await historyResponse.json();
        if (history[promptId] && history[promptId].outputs) {
          console.log('✅ ComfyUI 任务完成');
          const outputs = history[promptId].outputs;
          const image_urls = [];

          for (const nodeId in outputs) {
            if (outputs[nodeId].images) {
              for (const image of outputs[nodeId].images) {
                if (image.type === 'output') {
                  // 🔑 关键修复：下载图片并保存到本地
                  try {
                    const comfyuiImageUrl = `${comfyuiUrl}/view?filename=${image.filename}&subfolder=${image.subfolder || ''}&type=output`;
                    console.log('📥 正在下载图片:', comfyuiImageUrl);
                    
                    // 下载图片
                    const imageResponse = await fetch(comfyuiImageUrl);
                    if (!imageResponse.ok) {
                      throw new Error(`下载图片失败: ${imageResponse.status}`);
                    }
                    
                    const imageBuffer = await imageResponse.arrayBuffer();
                    
                    // 生成唯一文件名
                    const timestamp = Date.now();
                    const randomId = Math.random().toString(36).substr(2, 9);
                    const fileExtension = path.extname(image.filename) || '.png';
                    // 🔑 修复：生成唯一且兼容的文件名，移除重复扩展名
                    const baseFilename = path.parse(image.filename).name; // 移除原文件扩展名
                    const localFileName = `img_${timestamp}_${randomId}_${baseFilename}${fileExtension}`;
                    
                    // 确保目录存在
                    const publicDir = path.join(__dirname, '../public');
                    const imagesDir = path.join(publicDir, 'generated-images');
                    if (!fs.existsSync(imagesDir)) {
                      fs.mkdirSync(imagesDir, { recursive: true });
                    }
                    
                    // 保存图片到本地
                    const localPath = path.join(imagesDir, localFileName);
                    fs.writeFileSync(localPath, Buffer.from(imageBuffer));
                    
                    // 🔑 关键修复：返回完整的API服务器URL，确保前端可以访问
                    const publicUrl = `http://localhost:3001/generated-images/${localFileName}`;
                    image_urls.push(publicUrl);
                    console.log('✅ 图片已保存到:', localPath);
                    console.log('📡 前端访问URL:', publicUrl);
                    
                  } catch (error) {
                    console.error('❌ 保存图片失败:', error);
                    // 🔑 修复：降级到API代理URL，确保包含正确端口
                    const viewUrl = `http://localhost:3001/comic/view?filename=${image.filename}&subfolder=${image.subfolder || ''}&type=output`;
                    image_urls.push(viewUrl);
                    console.log('⚠️ 图片保存失败，使用代理URL:', viewUrl);
                  }
                }
              }
            }
          }

          if (image_urls.length > 0) {
            onProgress(100, '生成完成', 5); // 最终进度
            
            // 🔑 关键修复：从120号节点输出的预览信息中提取故事标题
            let extractedTitle = null;
            
            // 尝试从120号节点的text输出中提取标题
            if (outputs['120'] && outputs['120'].text && outputs['120'].text.length > 0) {
              const previewInfo = outputs['120'].text[0];
              console.log('🔍 120号节点预览信息:', previewInfo);
              
              // 匹配 "📚 视觉小说: 标题名称" 的格式
              const storyTitleMatch = previewInfo.match(/📚\s*视觉小说[：:]\s*(.+?)(?:\n|$)/);
              if (storyTitleMatch && storyTitleMatch[1]) {
                extractedTitle = storyTitleMatch[1].trim();
                console.log('✅ 从120号节点预览信息提取故事标题:', extractedTitle);
              } else {
                console.log('⚠️ 未能从120号节点预览信息中匹配到故事标题');
              }
            } else {
              console.log('⚠️ 120号节点没有text输出或为空');
            }
            
            // 如果没有提取到标题，使用默认值
            const finalTitle = extractedTitle || 
                              `${generationParams?.protagonistName || '主角'}的${generationParams?.storyType || '冒险'}故事` || 
                              `漫画作品_${new Date().toLocaleString()}`;
            
            console.log('📚 最终使用的作品标题:', finalTitle);
            
            return {
              id: `comic_${promptId}`,
              promptId: promptId,
              title: finalTitle,
              storyText: generationParams.userInput?.storyPrompt || generationParams?.storyPrompt || '自动生成的漫画故事',
              images: image_urls, // 🔑 前端期望的字段：所有图片URL数组
              finalComicUrl: image_urls[0] || '', // 🔑 前端期望的字段：主要图片URL
              parameters: generationParams,
              createdAt: new Date(),
              status: 'completed',
              // 🆕 保留scenes作为额外信息，但前端主要使用images
              scenes: image_urls.map((url, index) => ({
                id: `scene_${index + 1}`,
                title: `场景 ${index + 1}`,
                image: url,
                description: `漫画场景 ${index + 1}`
              })),
              metadata: {
                generatedAt: new Date().toISOString(),
                generationParams,
                comfyUIResult: outputs,
                extractedTitle: extractedTitle // 保存提取的标题用于调试
              }
            };
          } else {
            throw new Error('ComfyUI 任务完成但未在输出中找到图片');
          }
        }

        // 模拟进度更新 (更复杂的实现可以从 ComfyUI 的 WebSocket 获取真实进度)
        const elapsedTime = (Date.now() - startTime) / 1000;
        const fakeProgress = Math.min(95, Math.floor((elapsedTime / 120) * 100)); // 假设任务需要120秒
        onProgress(fakeProgress, '后端正在协调ComfyUI生成图片...', Math.floor(fakeProgress / 20));
      }
    } catch (error) {
      console.error('❌ ComfyUI生成失败:', error);
      throw error;
    }
  };

  // 🌍 多设备协调处理函数
  
  /**
   * 处理资源请求
   */
  const handleResourceRequest = (ws, payload) => {
    const clientInfo = clients.get(ws);
    if (!clientInfo) return;
    
    const { resourceType, deviceId } = payload;
    console.log(`📋 资源请求: ${deviceId} 请求 ${resourceType}`);
    
    // 检查资源是否已被占用
    const existingLock = resourceLocks.get(resourceType);
    
    if (!existingLock) {
      // 资源可用，直接分配
      grantResourceAccess(deviceId, resourceType, ws);
    } else {
      // 资源冲突，触发协调流程
      handleResourceConflict(deviceId, existingLock.ownerDeviceId, resourceType);
    }
  };
  
  /**
   * 处理资源释放
   */
  const handleResourceRelease = (ws, payload) => {
    const clientInfo = clients.get(ws);
    if (!clientInfo) return;
    
    const { resourceType, deviceId } = payload;
    console.log(`🔓 资源释放: ${deviceId} 释放 ${resourceType}`);
    
    const lock = resourceLocks.get(resourceType);
    if (lock && (lock.ownerDeviceId === deviceId || lock.ownerDeviceId.includes(deviceId))) {
      resourceLocks.delete(resourceType);
      
      // 更新设备状态
      const device = deviceRegistry.get(deviceId);
      if (device) {
        device.occupiedResources = device.occupiedResources.filter(r => r !== resourceType);
      }
      
      // 广播资源释放通知
      broadcast({
        event: 'RESOURCE_RELEASED',
        payload: {
          resourceType,
          deviceId,
          timestamp: Date.now()
        }
      });
    }
  };
  
  /**
   * 处理页面变化
   */
  const handlePageChange = (ws, payload) => {
    const clientInfo = clients.get(ws);
    if (!clientInfo) return;
    
    const { deviceId, newPage, oldPage } = payload;
    console.log(`📄 页面变化: ${deviceId} 从 ${oldPage} 切换到 ${newPage}`);
    
    // 更新设备注册信息
    const device = deviceRegistry.get(deviceId);
    if (device) {
      device.currentPage = newPage;
      device.lastActivity = Date.now();
      
      // 根据页面自动请求/释放资源
      autoManageResourcesByPage(deviceId, newPage, oldPage);
    }
    
    // 广播页面变化通知
    broadcast({
      event: 'DEVICE_PAGE_CHANGED',
      payload: {
        deviceId,
        newPage,
        oldPage,
        timestamp: Date.now()
      }
    });
  };
  
  /**
   * 处理用户决策响应
   */
  const handleUserDecision = (ws, payload) => {
    const { conflictId, decision } = payload;
    console.log(`👤 用户决策: 冲突${conflictId} 决策${decision}`);
    
    const pendingDecision = pendingDecisions.get(conflictId);
    if (!pendingDecision) {
      console.warn('⚠️ 未找到对应的待决策冲突');
      return;
    }
    
    // 执行决策
    executeDecision(pendingDecision, decision);
    
    // 清理待决策记录
    pendingDecisions.delete(conflictId);
  };
  
  /**
   * 处理设备状态同步
   */
  const handleDeviceStateSync = (ws, payload) => {
    const clientInfo = clients.get(ws);
    if (!clientInfo) return;
    
    console.log(`🔄 设备状态同步: ${clientInfo.id}`);
    
    // 发送完整的设备状态
    sendToClient(ws, {
      event: 'DEVICE_STATE_SYNC_RESPONSE',
      payload: {
        devices: Array.from(deviceRegistry.values()).map(d => ({
          deviceId: d.deviceId,
          deviceType: d.deviceType,
          currentPage: d.currentPage,
          priority: d.priority,
          occupiedResources: d.occupiedResources,
          isActive: d.isActive,
          lastActivity: d.lastActivity
        })),
        resourceLocks: Array.from(resourceLocks.entries()),
        globalState: globalState
      },
      timestamp: Date.now()
    });
  };
  
  /**
   * 处理强制重连所有设备
   */
  const handleForceReconnectAll = (ws) => {
    console.log('🔄 强制重连所有设备');
    
    broadcast({
      event: 'FORCE_RECONNECT_REQUESTED',
      payload: {
        reason: 'Server maintenance or emergency cleanup',
        timestamp: Date.now()
      }
    });
  };
  
  /**
   * 分配资源访问权限
   */
  const grantResourceAccess = (deviceId, resourceType, ws) => {
    const device = deviceRegistry.get(deviceId);
    if (!device) return;
    
    // 创建资源锁定
    resourceLocks.set(resourceType, {
      resourceType,
      ownerDeviceId: deviceId,
      lockedAt: Date.now()
    });
    
    // 更新设备状态
    device.occupiedResources.push(resourceType);
    
    console.log(`✅ 资源已分配: ${resourceType} -> ${deviceId}`);
    
    // 通知设备资源已分配
    sendToClient(ws, {
      event: 'RESOURCE_GRANTED',
      payload: {
        resourceType,
        deviceId,
        timestamp: Date.now()
      }
    });
    
    // 广播资源状态变化
    broadcast({
      event: 'RESOURCE_LOCK_UPDATED',
      payload: {
        resourceType,
        ownerDeviceId: deviceId,
        action: 'locked'
      }
    }, ws);
  };
  
  /**
   * 处理资源冲突
   */
  const handleResourceConflict = (newDeviceId, existingDeviceId, resourceType) => {
    console.log(`⚔️ 资源冲突: ${newDeviceId} vs ${existingDeviceId} 争夺 ${resourceType}`);
    
    const newDevice = deviceRegistry.get(newDeviceId);
    const existingDevice = deviceRegistry.get(existingDeviceId);
    
    if (!newDevice || !existingDevice) return;
    
    // 创建冲突决策请求
    const conflictId = `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const conflictData = {
      type: 'USER_DECISION_REQUIRED',
      scenario: 'resource_conflict',
      conflictId,
      newDevice: {
        id: newDeviceId,
        type: newDevice.deviceType,
        page: newDevice.currentPage
      },
      existingDevice: {
        id: existingDeviceId,
        type: existingDevice.deviceType,
        page: existingDevice.currentPage
      },
      conflictingResources: [resourceType],
      options: [
        {
          id: 'takeover',
          label: `让${getDeviceDisplayName(newDevice.deviceType)}接管控制权`,
          description: '新设备将获得完全控制权，现有会话将被暂停'
        },
        {
          id: 'share',
          label: '共享模式',
          description: '两个设备同时使用，音频输出到两个设备'
        },
        {
          id: 'deny',
          label: `保持${getDeviceDisplayName(existingDevice.deviceType)}的控制权`,
          description: '拒绝新设备的请求，维持现状'
        }
      ],
      timestamp: Date.now()
    };
    
    // 保存待决策记录
    pendingDecisions.set(conflictId, {
      newDeviceId,
      existingDeviceId,
      resourceType,
      conflictData
    });
    
    // 广播冲突决策请求
    broadcast({
      event: 'COORDINATION_EVENT',
      payload: conflictData
    });
  };
  
  /**
   * 执行用户决策
   */
  const executeDecision = (pendingDecision, decision) => {
    const { newDeviceId, existingDeviceId, resourceType } = pendingDecision;
    
    console.log(`🎯 执行决策: ${decision} (${resourceType})`);
    
    switch (decision) {
      case 'takeover':
        executeResourceTakeover(newDeviceId, existingDeviceId, resourceType);
        break;
        
      case 'share':
        setupResourceSharing(newDeviceId, existingDeviceId, resourceType);
        break;
        
      case 'deny':
      default:
        denyResourceRequest(newDeviceId, resourceType);
        break;
    }
  };
  
  /**
   * 执行资源接管
   */
  const executeResourceTakeover = (newDeviceId, existingDeviceId, resourceType) => {
    console.log(`🔄 执行资源接管: ${resourceType} (${existingDeviceId} -> ${newDeviceId})`);
    
    // 释放原设备的资源
    const existingDevice = deviceRegistry.get(existingDeviceId);
    if (existingDevice) {
      existingDevice.occupiedResources = existingDevice.occupiedResources.filter(r => r !== resourceType);
      
      // 通知原设备资源被接管
      const existingWs = existingDevice.ws;
      if (existingWs) {
        sendToClient(existingWs, {
          event: 'RESOURCE_TAKEN_OVER',
          payload: {
            resourceType,
            newOwner: newDeviceId,
            timestamp: Date.now()
          }
        });
      }
    }
    
    // 分配给新设备
    const newDevice = deviceRegistry.get(newDeviceId);
    if (newDevice) {
      grantResourceAccess(newDeviceId, resourceType, newDevice.ws);
    }
    
    // 广播接管完成通知
    broadcast({
      event: 'RESOURCE_TAKEOVER_COMPLETED',
      payload: {
        resourceType,
        newOwner: newDeviceId,
        previousOwner: existingDeviceId,
        timestamp: Date.now()
      }
    });
  };
  
  /**
   * 设置资源共享
   */
  const setupResourceSharing = (device1Id, device2Id, resourceType) => {
    console.log(`🤝 设置资源共享: ${resourceType} (${device1Id} + ${device2Id})`);
    
    // 创建共享锁定
    resourceLocks.set(resourceType, {
      resourceType,
      ownerDeviceId: `shared:${device1Id}:${device2Id}`,
      lockedAt: Date.now(),
      metadata: {
        sharedDevices: [device1Id, device2Id],
        shareMode: 'simultaneous'
      }
    });
    
    // 更新两个设备的资源状态
    [device1Id, device2Id].forEach(deviceId => {
      const device = deviceRegistry.get(deviceId);
      if (device) {
        if (!device.occupiedResources.includes(resourceType)) {
          device.occupiedResources.push(resourceType);
        }
        
        // 通知设备进入共享模式
        sendToClient(device.ws, {
          event: 'RESOURCE_SHARING_ENABLED',
          payload: {
            resourceType,
            sharedWith: [device1Id, device2Id].filter(id => id !== deviceId),
            shareMode: 'simultaneous',
            timestamp: Date.now()
          }
        });
      }
    });
    
    // 广播共享模式启用通知
    broadcast({
      event: 'RESOURCE_SHARING_STARTED',
      payload: {
        resourceType,
        participants: [device1Id, device2Id],
        shareMode: 'simultaneous',
        timestamp: Date.now()
      }
    });
  };
  
  /**
   * 拒绝资源请求
   */
  const denyResourceRequest = (deviceId, resourceType) => {
    console.log(`❌ 拒绝资源请求: ${deviceId} -> ${resourceType}`);
    
    const device = deviceRegistry.get(deviceId);
    if (device) {
      sendToClient(device.ws, {
        event: 'RESOURCE_REQUEST_DENIED',
        payload: {
          resourceType,
          reason: 'Resource already in use by higher priority device',
          timestamp: Date.now()
        }
      });
    }
  };
  
  /**
   * 根据页面自动管理资源
   */
  const autoManageResourcesByPage = (deviceId, newPage, oldPage) => {
    const device = deviceRegistry.get(deviceId);
    if (!device) return;
    
    // 页面资源需求映射
    const pageResourceRequirements = {
      'realtime': [ResourceType.MICROPHONE, ResourceType.SPEAKER, ResourceType.REALTIME_SESSION, ResourceType.TTS_ENGINE],
      'comic-generation': [ResourceType.GENERATION_QUEUE],
      'yijing': [],
      'tarot': []
    };
    
    const oldRequirements = pageResourceRequirements[oldPage] || [];
    const newRequirements = pageResourceRequirements[newPage] || [];
    
    // 释放不再需要的资源
    oldRequirements.forEach(resource => {
      if (!newRequirements.includes(resource)) {
        const lock = resourceLocks.get(resource);
        if (lock && lock.ownerDeviceId === deviceId) {
          resourceLocks.delete(resource);
          device.occupiedResources = device.occupiedResources.filter(r => r !== resource);
          console.log(`🔓 自动释放资源: ${resource} (页面切换)`);
        }
      }
    });
    
    // 请求新需要的资源
    newRequirements.forEach(resource => {
      if (!oldRequirements.includes(resource)) {
        console.log(`📋 自动请求资源: ${resource} (页面切换)`);
        handleResourceRequest(device.ws, {
          resourceType: resource,
          deviceId: deviceId,
          reason: 'page_change'
        });
      }
    });
  };
  
  /**
   * 获取设备显示名称
   */
  const getDeviceDisplayName = (deviceType) => {
    const names = {
      'electron': 'PC桌面端',
      'browser': '浏览器',
      'mobile': 'iPad/手机'
    };
    return names[deviceType] || deviceType;
  };

  // 启动服务器 - 监听所有IP地址以支持LAN访问
  apiServer = httpServer.listen(port, '0.0.0.0', () => {
    console.log(`🌐 Electron Store HTTP API + WebSocket 服务器运行在端口 ${port}`);
    console.log(`   本地访问: http://localhost:${port}/api`);
    console.log(`   局域网访问: http://192.168.x.x:${port}/api`);
    console.log(`🖼️ 静态图片服务器:`);
    console.log(`   本地访问: http://localhost:${port}/static/images/`);
    console.log(`   局域网访问: http://192.168.x.x:${port}/static/images/`);
    console.log(`📡 WebSocket 实时状态同步:`);
    console.log(`   本地连接: ws://localhost:${port}`);
    console.log(`   局域网连接: ws://192.168.x.x:${port}`);
  });

  // 🚨 关键修复：监听服务器错误事件
  httpServer.on('error', (error) => {
    console.error('❌❌❌ HTTP 服务器启动失败:', error);
    // 在这里可以弹出一个对话框通知用户
    dialog.showErrorBox('服务器启动失败', `端口 ${port} 可能已被占用或发生未知错误。\n\n${error.message}`);
    app.quit(); // 无法恢复，退出应用
  });
}

// 🗑️ 已移除图片路径迁移功能 - 2025年1月优化

// 监听store变化并记录日志
function setupStoreWatching() {
  if (!store) {
    console.warn('⚠️ Store 未初始化，跳过变化监听器设置');
    return;
  }

  // 监听漫画数据变化
  store.onDidChange('comic-generation-results', (newValue, oldValue) => {
    const newCount = Array.isArray(newValue) ? newValue.length : 0;
    const oldCount = Array.isArray(oldValue) ? oldValue.length : 0;
    
    console.log(`🎨 漫画数据已更改: ${oldCount} → ${newCount} 个作品`);
  });

  // 监听用户设置变化
  store.onDidChange('user-settings', (newValue, oldValue) => {
    console.log('⚙️ 用户设置已更改');
  });

  console.log('👁️ Store 变化监听器已设置');
}

// 🖼️ 图片文件管理 IPC 处理器
ipcMain.handle('save-image-file', async (event, imageId, base64Data, filename, subPath = '') => {
  try {
    const baseImagesDir = path.join(__dirname, '../public/generated-images');
    
    // 如果有子路径，创建子文件夹
    const imagesDir = subPath ? path.join(baseImagesDir, subPath) : baseImagesDir;
    
    // 确保图片目录存在
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
      console.log('📁 创建图片目录:', imagesDir);
    }
    
    // 🔧 修复：检查imageId是否已经包含.png后缀，避免重复添加
    const safeFilename = imageId.endsWith('.png') ? imageId : `${imageId}.png`;
    const filePath = path.join(imagesDir, safeFilename);
    
    console.log('🔍 文件名处理:', {
      original_imageId: imageId,
      has_png_suffix: imageId.endsWith('.png'),
      final_filename: safeFilename
    });
    
    // 将base64数据转换为Buffer并保存
    const base64Content = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Content, 'base64');
    
    fs.writeFileSync(filePath, buffer);
    
    console.log('✅ 图片已保存到主进程:', filePath);
    
    // 返回前端可访问的URL，包含子文件夹路径
    const publicUrl = subPath ? `/generated-images/${subPath}/${safeFilename}` : `/generated-images/${safeFilename}`;
    console.log('🔗 生成的URL:', publicUrl);
    
    return { 
      success: true, 
      url: publicUrl,
      localPath: filePath,
      filename: safeFilename,
      subPath: subPath 
    };
    
  } catch (error) {
    console.error('❌ 保存图片失败:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
});

// 🔑 新增：主角头像HTTP文件服务
ipcMain.handle('save-avatar-image', async (event, avatarId, base64Data) => {
  try {
    const avatarsDir = path.join(__dirname, '../public/avatars');
    
    // 确保头像目录存在
    if (!fs.existsSync(avatarsDir)) {
      fs.mkdirSync(avatarsDir, { recursive: true });
    }
    
    // 生成安全的文件名
    const safeFilename = `avatar_${avatarId}.png`;
    const filePath = path.join(avatarsDir, safeFilename);
    
    // 将base64数据转换为Buffer并保存
    const base64Content = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Content, 'base64');
    
    // 保存文件
    fs.writeFileSync(filePath, buffer);
    
    // 返回HTTP访问URL
    const publicUrl = `/avatars/${safeFilename}`;
    
    console.log(`✅ 头像保存成功: ${filePath} -> ${publicUrl}`);
    return { 
      success: true, 
      url: publicUrl,
      localPath: filePath,
      filename: safeFilename 
    };
    
  } catch (error) {
    console.error('❌ 保存头像失败:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
});

// 🔑 新增：风格配置图片HTTP文件服务
ipcMain.handle('save-style-image', async (event, styleId, base64Data, presetStyleId = null) => {
  try {
    const stylesDir = path.join(__dirname, '../public/styles');
    
    // 确保风格目录存在
    if (!fs.existsSync(stylesDir)) {
      fs.mkdirSync(stylesDir, { recursive: true });
    }
    
    // 🔧 修复：智能生成文件名，避免重复前缀和后缀
    let cleanStyleId = styleId;
    
    // 移除重复的 'style_' 前缀（如果已存在）
    if (cleanStyleId.startsWith('style_')) {
      cleanStyleId = cleanStyleId.substring(6); // 移除 'style_' 前缀
      console.log(`🧹 [save-style-image] 移除重复前缀: ${styleId} -> ${cleanStyleId}`);
    }
    
    // 🔧 关键修复：移除双后缀问题
    if (cleanStyleId.endsWith('.png.png')) {
      cleanStyleId = cleanStyleId.replace('.png.png', '.png');
      console.log(`🧹 [save-style-image] 移除双后缀: ${cleanStyleId}.png.png -> ${cleanStyleId}`);
    }
    
    // 确保文件名有正确的前缀和后缀，避免重复
    const safeFilename = cleanStyleId.endsWith('.png') 
      ? `style_${cleanStyleId}` 
      : `style_${cleanStyleId}.png`;
      
    console.log(`🏷️ [save-style-image] 最终文件名: ${safeFilename}`);
    const filePath = path.join(stylesDir, safeFilename);
    
    // 将base64数据转换为Buffer并保存
    const base64Content = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Content, 'base64');
    
    // 保存主文件
    fs.writeFileSync(filePath, buffer);
    
    // 🔑 关键修复：如果是预设风格，创建预设风格ID的文件映射
    console.log(`🔍 [save-style-image] 检查预设风格ID: ${presetStyleId}`);
    console.log(`🔍 [save-style-image] 保存参数详情:`, {
      styleId: styleId,
      presetStyleId: presetStyleId,
      safeFilename: safeFilename,
      filePath: filePath,
      stylesDir: stylesDir
    });
    
    if (presetStyleId) {
      // 🔧 修复：预设风格文件名也需要避免重复前缀和后缀
      let cleanPresetId = presetStyleId;
      
      // 移除重复的 'style_' 前缀（如果已存在）
      if (cleanPresetId.startsWith('style_')) {
        cleanPresetId = cleanPresetId.substring(6);
        console.log(`🧹 [save-style-image] 预设ID移除重复前缀: ${presetStyleId} -> ${cleanPresetId}`);
      }
      
      // 🔧 关键修复：移除双后缀问题
      if (cleanPresetId.endsWith('.png.png')) {
        cleanPresetId = cleanPresetId.replace('.png.png', '.png');
        console.log(`🧹 [save-style-image] 预设ID移除双后缀: ${cleanPresetId}.png.png -> ${cleanPresetId}`);
      }
      
      // 确保预设文件名格式正确
      const presetFilename = cleanPresetId.endsWith('.png') 
        ? `style_${cleanPresetId}` 
        : `style_${cleanPresetId}.png`;
        
      console.log(`🏷️ [save-style-image] 预设文件名: ${presetFilename}`);
      const presetFilePath = path.join(stylesDir, presetFilename);
      
      console.log(`📝 [save-style-image] 准备创建文件映射:`);
      console.log(`   源文件: ${safeFilename}`);
      console.log(`   目标文件: ${presetFilename}`);
      console.log(`   目标路径: ${presetFilePath}`);
      
      // 创建软链接或复制文件
      try {
        // 删除旧的链接/文件
        if (fs.existsSync(presetFilePath)) {
          fs.unlinkSync(presetFilePath);
          console.log(`🗑️ [save-style-image] 删除旧文件: ${presetFilename}`);
        }
        
        // 尝试创建软链接 (Windows/Linux都支持)
        try {
          fs.symlinkSync(safeFilename, presetFilePath);
          console.log(`✅ [save-style-image] 为预设风格创建软链接: ${presetFilename} -> ${safeFilename}`);
          
          // 验证软链接是否成功
          if (fs.existsSync(presetFilePath)) {
            console.log(`✅ [save-style-image] 软链接验证成功: ${presetFilename} 文件存在`);
          } else {
            console.error(`❌ [save-style-image] 软链接验证失败: ${presetFilename} 文件不存在`);
          }
        } catch (linkError) {
          console.log(`⚠️ [save-style-image] 软链接失败，使用文件复制: ${linkError.message}`);
          // 软链接失败，使用文件复制
          fs.copyFileSync(filePath, presetFilePath);
          console.log(`✅ [save-style-image] 为预设风格复制文件: ${presetFilename} (软链接失败，使用复制)`);
          
          // 验证文件复制是否成功
          if (fs.existsSync(presetFilePath)) {
            console.log(`✅ [save-style-image] 文件复制验证成功: ${presetFilename} 文件存在`);
          } else {
            console.error(`❌ [save-style-image] 文件复制验证失败: ${presetFilename} 文件不存在`);
          }
        }
        
        // 最终验证：检查文件映射是否可访问
        console.log(`🔍 [save-style-image] 最终验证文件映射:`);
        console.log(`   源文件存在: ${fs.existsSync(filePath)}`);
        console.log(`   映射文件存在: ${fs.existsSync(presetFilePath)}`);
        
        // 检查映射文件的访问URL
        const mappingUrl = `/styles/${presetFilename}`;
        console.log(`🔗 [save-style-image] 映射文件的访问URL: ${mappingUrl}`);
        
      } catch (mappingError) {
        console.error(`❌ [save-style-image] 创建文件映射失败: ${mappingError.message}`);
      }
    } else {
      console.log(`ℹ️ [save-style-image] 非预设风格，跳过文件映射创建`);
    }
    
    // 返回HTTP访问URL
    const publicUrl = `/styles/${safeFilename}`;
    
    console.log(`✅ 风格图片保存成功: ${filePath} -> ${publicUrl}`);
    return { 
      success: true, 
      url: publicUrl,
      localPath: filePath,
      filename: safeFilename 
    };
    
  } catch (error) {
    console.error('❌ 保存风格图片失败:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
});

ipcMain.handle('load-image-file', async (event, filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`图片文件不存在: ${filePath}`);
    }
    
    // 读取文件并转换为base64
    const buffer = fs.readFileSync(filePath);
    const extension = path.extname(filePath).toLowerCase();
    
    let mimeType = 'image/png';
    if (extension === '.jpg' || extension === '.jpeg') {
      mimeType = 'image/jpeg';
    } else if (extension === '.gif') {
      mimeType = 'image/gif';
    } else if (extension === '.webp') {
      mimeType = 'image/webp';
    }
    
    const base64Data = `data:${mimeType};base64,${buffer.toString('base64')}`;
    
    console.log('✅ 图片已从主进程加载:', filePath);
    return base64Data;
    
  } catch (error) {
    console.error('❌ 主进程加载图片失败:', error);
    throw error;
  }
});

// IPC 处理器
ipcMain.handle('save-file-to-public', async (event, { relativePath, data }) => {
  try {
    const publicDir = path.join(__dirname, '../public');
    const absolutePath = path.join(publicDir, relativePath);

    // 确保目标目录存在
    const dirName = path.dirname(absolutePath);
    if (!fs.existsSync(dirName)) {
      fs.mkdirSync(dirName, { recursive: true });
    }

    // 将 ArrayBuffer 转换为 Buffer 并写入文件
    fs.writeFileSync(absolutePath, Buffer.from(data));
    
    console.log(`[IPC] 文件已保存: ${absolutePath}`);
    return relativePath; // 返回相对路径给渲染进程
  } catch (error) {
    console.error('[IPC] 保存文件失败:', error);
    throw error; // 将错误抛回给渲染进程
  }
});

ipcMain.handle('delete-file-from-public', async (event, relativePath) => {
  try {
    const publicDir = path.join(__dirname, '../public');
    const absolutePath = path.join(publicDir, relativePath);

    if (fs.existsSync(absolutePath)) {
      fs.unlinkSync(absolutePath);
      console.log(`[IPC] 文件已删除: ${absolutePath}`);
    } else {
      console.warn(`[IPC] 尝试删除不存在的文件: ${absolutePath}`);
    }
  } catch (error) {
    console.error('[IPC] 删除文件失败:', error);
    throw error;
  }
});

ipcMain.handle('get-backend-config', () => {
  return BACKEND_CONFIG
})

ipcMain.handle('restart-backend', () => {
  if (backendProcess) {
    backendProcess.kill()
  }
  setTimeout(() => {
    startBackendService()
  }, 2000)
  return { success: true }
})

// 🗑️ 已移除数据迁移功能 - 2025年1月优化，避免启动时内存消耗

// 应用事件处理
app.whenReady().then(async () => {
  try {
    // 🚀 动态导入 electron-store
    console.log('📦 正在加载 electron-store...')
    const Store = (await import('electron-store')).default
    
    // 初始化 store
    store = new Store({
      name: 'cosyvoice-data',
      defaults: {
        'comic-generation-results': [],
        'user-settings': {},
        'app-preferences': {}
      }
    })
    
    console.log('📁 Electron Store 数据路径:', store.path)
    
    // 🚀 初始化 Electron Store 系统
    console.log('✅ 初始化 Electron Store 系统...')
    setupStoreIPC()
    setupHTTPAPI()
    setupStoreWatching()
    
    // 🚀 快速启动 - 已移除数据迁移逻辑，提升启动性能
    console.log('⚡ 快速启动模式 - 已跳过数据迁移步骤');
    
    // 显示当前数据统计
    setTimeout(() => {
      if (store) {
        const comics = store.get('comic-generation-results', [])
        console.log(`📊 当前数据统计: ${comics.length} 个漫画作品`)
        console.log(`📁 数据文件位置: ${store.path}`)
      } else {
        console.log('📊 Store 未初始化，无法显示数据统计')
      }
    }, 1000)
    
    console.log('✅ Electron Store 系统初始化完成')
  } catch (error) {
    console.error('❌ electron-store 初始化失败:', error)
    console.log('⚠️ 将禁用 electron-store 功能，使用降级模式')
    store = null
  }
  
  // 检查更新（仅生产环境）
  if (!isDev) {
    console.log('🔍 生产环境，启动更新检查...')
    setTimeout(() => {
      autoUpdater.checkForUpdatesAndNotify()
    }, 3000) // 延迟3秒检查更新
  }
  
  // 注册 'safe-file' 协议的处理器
  protocol.registerFileProtocol('safe-file', (request, callback) => {
    // 从 URL 中剥离协议头 'safe-file://'
    const requestedPath = decodeURIComponent(request.url.substring(12));

    // 定义所有可能的资源根目录
    const resourceRoots = [
      path.join(__dirname, '../public'),      // 前端静态资源
      path.join(__dirname, '../../data'),       // 后端数据目录 (例如卡牌图片)
      path.join(__dirname, '../../uploads'),    // 后端上传目录 (例如头像)
      path.join(__dirname, '../../')          // 项目根目录 (作为备用)
    ];

    let foundPath = '';

    // 依次在每个根目录中查找文件
    for (const root of resourceRoots) {
      const fullPath = path.normalize(path.join(root, requestedPath));
      if (fs.existsSync(fullPath)) {
        foundPath = fullPath;
        break; // 找到文件后立即停止搜索
      }
    }

    if (foundPath) {
      // console.log(`[safe-file] ✅ Found: ${requestedPath} -> ${foundPath}`);
      callback(foundPath);
    } else {
      console.error(`[safe-file] ❌ Not Found: ${requestedPath}`);
      callback({ error: -6 }); // -6 corresponds to FILE_NOT_FOUND
    }
  });

  // 🚀 主动启动后端服务
  console.log('🚀 强制启动后端服务...')
  startBackendService()
  
  createMainWindow()
  createMenu()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow()
    }
  })
})

app.on('window-all-closed', () => {
  // 关闭后端进程
  if (backendProcess) {
    backendProcess.kill()
  }
  
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  // 应用退出前清理
  if (backendProcess) {
    backendProcess.kill()
  }
})

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
}) 