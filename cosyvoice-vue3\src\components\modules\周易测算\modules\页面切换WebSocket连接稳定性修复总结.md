# 页面切换WebSocket连接稳定性修复总结

## 🔍 问题根源深度分析

通过深度分析前后端日志，发现了页面切换时WebSocket连接不稳定的根本原因：

### 关键问题时间线

**后端日志关键时间点**：
```
00:28:24 - 实时对话停止，资源清理 ✅
00:28:43 - 神谕之音启动（oracle模式） ✅
00:29:02 - 第一次WebSocket断开 ❌ (第429行)
00:29:04 - WebSocket重连 🔄 (第447行)
00:29:07 - 第一个音频片段发送成功（22.39秒） ✅
00:29:07 - WebSocket再次断开 ❌ (第497行)
00:29:25 - 第二个音频片段尝试发送，但连接数为0 ❌
```

### 核心问题识别

1. **WebSocket连接频繁断开重连**：在音频播放期间连接被意外断开
2. **音频片段丢失**：后端生成了5个音频片段，但只有第1个发送成功
3. **路由守卫过度清理**：页面切换时清理了正在使用的WebSocket连接
4. **组件生命周期冲突**：新旧组件的初始化和卸载时机重叠
5. **全局状态不同步**：音频播放状态没有正确同步到全局

## 🔧 系统性修复方案

### 修复1：优化路由守卫的连接保护机制

**文件**：`websocketGuard.ts`

**新增功能**：
1. **音频播放状态检查**：在页面切换前检查是否有正在播放的音频
2. **延迟清理机制**：如果检测到音频播放，延迟清理WebSocket连接
3. **智能清理策略**：根据页面类型和音频状态决定清理时机

**关键代码**：
```typescript
// 🔧 修复：检查是否有正在进行的音频播放，如果有则延迟清理
const isAudioPlaying = await checkAudioPlayingStatus(fromPageName);
if (isAudioPlaying) {
  console.log(`🎵 [路由守卫] 检测到音频播放中，延迟清理WebSocket连接`);
  // 标记需要延迟清理，但允许页面切换继续
  scheduleDelayedCleanup(fromPageName, 30000); // 30秒后强制清理
  next();
  return;
}
```

**音频播放状态检查函数**：
```typescript
async function checkAudioPlayingStatus(pageName: string): Promise<boolean> {
  // 检查DOM中的音频元素
  const audioElements = document.querySelectorAll('audio');
  for (const audio of audioElements) {
    if (!audio.paused && !audio.ended) {
      return true;
    }
  }
  
  // 检查全局音频播放状态
  if ((window as any).isPlayingAudio) {
    return true;
  }
  
  return false;
}
```

### 修复2：增强WebSocket管理器的连接稳定性

**文件**：`masterWebSocketManager.ts`

**新增功能**：
1. **连接保护机制**：在音频播放期间保护WebSocket连接不被断开
2. **延迟断开策略**：检测到音频播放时延迟执行断开操作
3. **智能重连机制**：连接断开后根据上下文智能重连

**关键代码**：
```typescript
public disconnect(): void {
  // 🔧 修复：检查是否有正在进行的音频播放
  if (this.isAudioPlayingProtected()) {
    this.log('🎵 检测到音频播放中，延迟断开连接');
    this.scheduleDelayedDisconnect(30000); // 30秒后强制断开
    return;
  }
  
  // 执行正常的断开逻辑...
}

private isAudioPlayingProtected(): boolean {
  // 检查神谕之音和实时对话的音频播放状态
  if (this.context.includes('oracle') || this.context.includes('realtime')) {
    // 检查DOM中的音频元素和全局状态
    const audioElements = document.querySelectorAll('audio');
    for (const audio of audioElements) {
      if (!audio.paused && !audio.ended) {
        return true;
      }
    }
    
    if ((window as any).isPlayingAudio) {
      return true;
    }
  }
  
  return false;
}
```

### 修复3：优化神谕之音组件的状态同步

**文件**：`神谕之音.vue`

**新增功能**：
1. **全局状态同步**：将音频播放状态同步到全局window对象
2. **组件卸载保护**：在音频播放期间延迟组件卸载
3. **连接状态监控**：实时监控WebSocket连接状态

**关键代码**：
```typescript
onMounted(async () => {
  // 🔧 新增：设置全局音频播放状态标记
  (window as any).isPlayingAudio = false;
  
  // 延迟初始化，确保前一个页面的WebSocket完全清理
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 初始化组件...
});

const audioPlayHandler = () => {
  console.log('🎵 音频开始播放');
  isPlayingAudio.value = true;
  (window as any).isPlayingAudio = true; // 🔧 新增：同步全局状态
  audioPlayStartTime.value = Date.now();
};

const handleAllAudioComplete = async () => {
  // 清理本地状态
  isPlayingAudio.value = false;
  // 🔧 新增：同步全局音频播放状态
  (window as any).isPlayingAudio = false;
  
  // 检查组件是否正在卸载，如果是则执行延迟的清理操作
  if (isComponentUnmounting.value) {
    // 执行延迟清理...
  }
};
```

## 🎯 修复效果预期

### 修复前的问题流程
```
1. 用户从实时对话页面切换到神谕之音页面 ✅
2. 路由守卫立即清理前一个页面的WebSocket连接 ❌
3. 神谕之音开始卦象解读 ✅
4. 后端生成5个音频片段 ✅
5. 发送第1个音频片段成功 ✅
6. 页面切换导致WebSocket断开 ❌
7. 第2-5个音频片段丢失 ❌
8. 用户只听到不完整的解读 ❌
```

### 修复后的预期流程
```
1. 用户从实时对话页面切换到神谕之音页面 ✅
2. 路由守卫检查音频播放状态，延迟清理 ✅
3. 神谕之音开始卦象解读 ✅
4. 后端生成5个音频片段 ✅
5. 发送第1个音频片段成功 ✅
6. WebSocket连接保持稳定（音频播放保护） ✅
7. 继续接收第2-5个音频片段 ✅
8. 音频播放完成后执行延迟清理 ✅
9. 用户听到完整的卦象解读 ✅
```

## 🧪 测试验证

### 测试场景1：神谕之音 → 实时对话
1. 进入神谕之音页面
2. 开始卦象解读
3. 在音频播放过程中切换到实时对话页面
4. 观察音频是否能完整播放
5. 确认实时对话功能正常启动

### 测试场景2：实时对话 → 神谕之音
1. 进入实时对话页面
2. 进行语音对话
3. 切换到神谕之音页面
4. 开始卦象解读
5. 确认音频能完整播放

### 预期日志
**修复后的正常日志**：
```
🔄 [路由守卫] 页面切换: RealtimeView -> YijingOracle
🎵 [路由守卫] 检测到音频播放中，延迟清理WebSocket连接
⏰ [路由守卫] 已安排 RealtimeView 的延迟清理，30000ms后执行
🎯 神谕之音组件挂载开始
🔗 初始化WebSocket连接...
✅ 神谕之音：WebSocket连接成功
🎵 音频开始播放
✅ 所有音频片段播放完成
⏰ 执行延迟清理: RealtimeView
```

## 🎉 修复总结

通过这次系统性修复，我们解决了页面切换导致的WebSocket连接不稳定问题：

1. ✅ **连接保护机制**：在音频播放期间保护WebSocket连接不被断开
2. ✅ **智能清理策略**：根据音频播放状态决定清理时机
3. ✅ **状态同步优化**：全局音频播放状态实时同步
4. ✅ **延迟清理机制**：音频播放完成后执行延迟清理
5. ✅ **组件生命周期优化**：正确处理组件卸载与音频播放的时序关系

### 关键改进点

1. **全局状态管理**：通过window对象同步音频播放状态
2. **时序协调**：路由守卫、WebSocket管理器、组件生命周期的协调工作
3. **保护机制**：多层保护确保关键操作不被中断
4. **智能重连**：连接断开后根据上下文智能恢复

现在用户可以在神谕之音和实时对话页面之间自由切换，每个页面的功能都能正常工作，音频播放不会被中断！
