/*!
 * @pixi/mixin-get-child-by-name - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-get-child-by-name is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";var e=require("@pixi/display");e.DisplayObject.prototype.name=null,e.Container.prototype.getChildByName=function(e,i){for(var r=0,t=this.children.length;r<t;r++)if(this.children[r].name===e)return this.children[r];if(i)for(r=0,t=this.children.length;r<t;r++){var n=this.children[r];if(n.getChildByName){var l=n.getChildByName(e,!0);if(l)return l}}return null};
//# sourceMappingURL=mixin-get-child-by-name.min.js.map
