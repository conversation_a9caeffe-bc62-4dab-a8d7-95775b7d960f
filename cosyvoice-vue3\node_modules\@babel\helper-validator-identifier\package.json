{"name": "@babel/helper-validator-identifier", "version": "7.27.1", "description": "Validate identifier/keywords name", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-identifier"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "devDependencies": {"@unicode/unicode-16.0.0": "^1.0.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}