{"version": 3, "file": "compressed-textures.js", "sources": ["../../src/const.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/resources/BlobResource.ts", "../../src/resources/CompressedTextureResource.ts", "../../src/loaders/CompressedTextureLoader.ts", "../../src/loaders/registerCompressedTextures.ts", "../../src/parsers/parseDDS.ts", "../../src/parsers/parseKTX.ts", "../../src/loaders/DDSLoader.ts", "../../src/loaders/KTXLoader.ts"], "sourcesContent": ["/**\n * WebGL internal formats, including compressed texture formats provided by extensions\n * @memberof PIXI\n * @static\n * @name INTERNAL_FORMATS\n * @enum {number}\n * @property {number} [COMPRESSED_RGB_S3TC_DXT1_EXT=0x83F0] -\n * @property {number} [COMPRESSED_RGBA_S3TC_DXT1_EXT=0x83F1] -\n * @property {number} [COMPRESSED_RGBA_S3TC_DXT3_EXT=0x83F2] -\n * @property {number} [COMPRESSED_RGBA_S3TC_DXT5_EXT=0x83F3] -\n * @property {number} [COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917] -\n * @property {number} [COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918] -\n * @property {number} [COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919] -\n * @property {number} [COMPRESSED_SRGB_S3TC_DXT1_EXT=35916] -\n * @property {number} [COMPRESSED_R11_EAC=0x9270] -\n * @property {number} [COMPRESSED_SIGNED_R11_EAC=0x9271] -\n * @property {number} [COMPRESSED_RG11_EAC=0x9272] -\n * @property {number} [COMPRESSED_SIGNED_RG11_EAC=0x9273] -\n * @property {number} [COMPRESSED_RGB8_ETC2=0x9274] -\n * @property {number} [COMPRESSED_RGBA8_ETC2_EAC=0x9278] -\n * @property {number} [COMPRESSED_SRGB8_ETC2=0x9275] -\n * @property {number} [COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=0x9279] -\n * @property {number} [COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=0x9276] -\n * @property {number} [COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=0x9277] -\n * @property {number} [COMPRESSED_RGB_PVRTC_4BPPV1_IMG=0x8C00] -\n * @property {number} [COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=0x8C02] -\n * @property {number} [COMPRESSED_RGB_PVRTC_2BPPV1_IMG=0x8C01] -\n * @property {number} [COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=0x8C03] -\n * @property {number} [COMPRESSED_RGB_ETC1_WEBGL=0x8D64] -\n * @property {number} [COMPRESSED_RGB_ATC_WEBGL=0x8C92] -\n * @property {number} [COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=0x8C92] -\n * @property {number} [COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=0x87EE] -\n * @property {number} [COMPRESSED_RGBA_ASTC_4x4_KHR=0x93B0] -\n */\nexport enum INTERNAL_FORMATS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    // WEBGL_compressed_texture_s3tc\n    COMPRESSED_RGB_S3TC_DXT1_EXT = 0x83F0,\n    COMPRESSED_RGBA_S3TC_DXT1_EXT = 0x83F1,\n    COMPRESSED_RGBA_S3TC_DXT3_EXT = 0x83F2,\n    COMPRESSED_RGBA_S3TC_DXT5_EXT = 0x83F3,\n\n    // WEBGL_compressed_texture_s3tc_srgb\n    COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT = 35917,\n    COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT = 35918,\n    COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT = 35919,\n    COMPRESSED_SRGB_S3TC_DXT1_EXT = 35916,\n\n    // WEBGL_compressed_texture_etc\n    COMPRESSED_R11_EAC = 0x9270,\n    COMPRESSED_SIGNED_R11_EAC = 0x9271,\n    COMPRESSED_RG11_EAC = 0x9272,\n    COMPRESSED_SIGNED_RG11_EAC = 0x9273,\n    COMPRESSED_RGB8_ETC2 = 0x9274,\n    COMPRESSED_RGBA8_ETC2_EAC = 0x9278,\n    COMPRESSED_SRGB8_ETC2 = 0x9275,\n    COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = 0x9279,\n    COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9276,\n    COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9277,\n\n    // WEBGL_compressed_texture_pvrtc\n    COMPRESSED_RGB_PVRTC_4BPPV1_IMG = 0x8C00,\n    COMPRESSED_RGBA_PVRTC_4BPPV1_IMG = 0x8C02,\n    COMPRESSED_RGB_PVRTC_2BPPV1_IMG = 0x8C01,\n    COMPRESSED_RGBA_PVRTC_2BPPV1_IMG = 0x8C03,\n\n    // WEBGL_compressed_texture_etc1\n    COMPRESSED_RGB_ETC1_WEBGL = 0x8D64,\n\n    // WEBGL_compressed_texture_atc\n    COMPRESSED_RGB_ATC_WEBGL = 0x8C92,\n    COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL = 0x8C92, // TODO: Probably a bug on the MDN site\n    COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL = 0x87EE,\n\n    // WEBGL_compressed_texture_astc\n    /* eslint-disable-next-line camelcase */\n    COMPRESSED_RGBA_ASTC_4x4_KHR = 0x93B0,\n}\n\n/**\n * Maps the compressed texture formats in {@link PIXI.INTERNAL_FORMATS} to the number of bytes taken by\n * each texel.\n * @memberof PIXI\n * @static\n * @ignore\n */\nexport const INTERNAL_FORMAT_TO_BYTES_PER_PIXEL: { [id: number]: number } = {\n    // WEBGL_compressed_texture_s3tc\n    [INTERNAL_FORMATS.COMPRESSED_RGB_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT]: 1,\n\n    // WEBGL_compressed_texture_s3tc\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT]: 1,\n\n    // WEBGL_compressed_texture_etc\n    [INTERNAL_FORMATS.COMPRESSED_R11_EAC]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SIGNED_R11_EAC]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RG11_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_SIGNED_RG11_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGB8_ETC2]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA8_ETC2_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB8_ETC2]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2]: 0.5, // ~~\n    [INTERNAL_FORMATS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2]: 0.5, // ~~\n\n    // WEBGL_compressed_texture_pvrtc\n    [INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_2BPPV1_IMG]: 0.25,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG]: 0.25,\n\n    // WEBGL_compressed_texture_etc1\n    [INTERNAL_FORMATS.COMPRESSED_RGB_ETC1_WEBGL]: 0.5,\n\n    // @see https://www.khronos.org/registry/OpenGL/extensions/AMD/AMD_compressed_ATC_texture.txt\n    // WEBGL_compressed_texture_atc\n    [INTERNAL_FORMATS.COMPRESSED_RGB_ATC_WEBGL]: 0.5,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL]: 1,\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL]: 1,\n\n    // @see https://registry.khronos.org/OpenGL/extensions/KHR/KHR_texture_compression_astc_hdr.txt\n    // WEBGL_compressed_texture_astc\n    /* eslint-disable-next-line camelcase */\n    [INTERNAL_FORMATS.COMPRESSED_RGBA_ASTC_4x4_KHR]: 1,\n};\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import type { Resource } from '@pixi/core';\nimport { ViewableBuffer, BufferResource } from '@pixi/core';\n\ninterface IBlobOptions\n{\n    autoLoad?: boolean;\n    width: number;\n    height: number;\n}\n\n/**\n * Resource that fetches texture data over the network and stores it in a buffer.\n * @class\n * @extends PIXI.Resource\n * @memberof PIXI\n */\nexport abstract class BlobResource extends BufferResource\n{\n    protected origin: string;\n    protected buffer: ViewableBuffer;\n    protected loaded: boolean;\n\n    /**\n     * @param {string} source - the URL of the texture file\n     * @param {PIXI.IBlobOptions} options\n     * @param {boolean}[options.autoLoad] - whether to fetch the data immediately;\n     *  you can fetch it later via {@link BlobResource#load}\n     * @param {boolean}[options.width] - the width in pixels.\n     * @param {boolean}[options.height] - the height in pixels.\n     */\n    constructor(source: string | Uint8Array | Uint32Array | Float32Array,\n        options: IBlobOptions = { width: 1, height: 1, autoLoad: true })\n    {\n        let origin: string;\n        let data: Uint8Array | Uint32Array | Float32Array;\n\n        if (typeof source === 'string')\n        {\n            origin = source;\n            data = new Uint8Array();\n        }\n        else\n        {\n            origin = null;\n            data = source;\n        }\n\n        super(data, options);\n\n        /**\n         * The URL of the texture file\n         * @member {string}\n         */\n        this.origin = origin;\n\n        /**\n         * The viewable buffer on the data\n         * @member {ViewableBuffer}\n         */\n        // HINT: BlobResource allows \"null\" sources, assuming the child class provides an alternative\n        this.buffer = data ? new ViewableBuffer(data) : null;\n\n        // Allow autoLoad = \"undefined\" still load the resource by default\n        if (this.origin && options.autoLoad !== false)\n        {\n            this.load();\n        }\n        if (data && data.length)\n        {\n            this.loaded = true;\n            this.onBlobLoaded(this.buffer.rawBinaryData);\n        }\n    }\n\n    protected onBlobLoaded(_data: ArrayBuffer): void\n    {\n        // TODO: Override this method\n    }\n\n    /** Loads the blob */\n    async load(): Promise<Resource>\n    {\n        const response = await fetch(this.origin);\n        const blob = await response.blob();\n        const arrayBuffer = await blob.arrayBuffer();\n\n        this.data = new Uint32Array(arrayBuffer);\n        this.buffer = new ViewableBuffer(arrayBuffer);\n        this.loaded = true;\n\n        this.onBlobLoaded(arrayBuffer);\n        this.update();\n\n        return this;\n    }\n}\n", "import { BlobResource } from './BlobResource';\nimport { INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from '../const';\nimport type { Renderer, BaseTexture, GLTexture } from '@pixi/core';\n\nimport type { INTERNAL_FORMATS } from '../const';\n\n/**\n * @ignore\n */\n// Used in PIXI.KTXLoader\nexport type CompressedLevelBuffer = {\n    levelID: number,\n    levelWidth: number,\n    levelHeight: number,\n    levelBuffer: Uint8Array\n};\n\n/**\n * @ignore\n */\nexport interface ICompressedTextureResourceOptions\n{\n    format: INTERNAL_FORMATS;\n    width: number;\n    height: number;\n    levels?: number;\n    levelBuffers?: CompressedLevelBuffer[];\n}\n\n/**\n * Resource for compressed texture formats, as follows: S3TC/DXTn (& their sRGB formats), ATC, ASTC, ETC 1/2, PVRTC.\n *\n * Compressed textures improve performance when rendering is texture-bound. The texture data stays compressed in\n * graphics memory, increasing memory locality and speeding up texture fetches. These formats can also be used to store\n * more detail in the same amount of memory.\n *\n * For most developers, container file formats are a better abstraction instead of directly handling raw texture\n * data. PixiJS provides native support for the following texture file formats (via {@link PIXI.Loader}):\n *\n * **.dds** - the DirectDraw Surface file format stores DXTn (DXT-1,3,5) data. See {@link PIXI.DDSLoader}\n * **.ktx** - the Khronos Texture Container file format supports storing all the supported WebGL compression formats.\n *  See {@link PIXI.KTXLoader}.\n * **.basis** - the BASIS supercompressed file format stores texture data in an internal format that is transcoded\n *  to the compression format supported on the device at _runtime_. It also supports transcoding into a uncompressed\n *  format as a fallback; you must install the `@pixi/basis-loader`, `@pixi/basis-transcoder` packages separately to\n *  use these files. See {@link PIXI.BasisLoader}.\n *\n * The loaders for the aforementioned formats use `CompressedTextureResource` internally. It is strongly suggested that\n * they be used instead.\n *\n * ## Working directly with CompressedTextureResource\n *\n * Since `CompressedTextureResource` inherits `BlobResource`, you can provide it a URL pointing to a file containing\n * the raw texture data (with no file headers!):\n *\n * ```js\n * // The resource backing the texture data for your textures.\n * // NOTE: You can also provide a ArrayBufferView instead of a URL. This is used when loading data from a container file\n * //   format such as KTX, DDS, or BASIS.\n * const compressedResource = new PIXI.CompressedTextureResource(\"bunny.dxt5\", {\n *   format: PIXI.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n *   width: 256,\n *   height: 256\n * });\n *\n * // You can create a base-texture to the cache, so that future `Texture`s can be created using the `Texture.from` API.\n * const baseTexture = new PIXI.BaseTexture(compressedResource, { pmaMode: PIXI.ALPHA_MODES.NPM });\n *\n * // Create a Texture to add to the TextureCache\n * const texture = new PIXI.Texture(baseTexture);\n *\n * // Add baseTexture & texture to the global texture cache\n * PIXI.BaseTexture.addToCache(baseTexture, \"bunny.dxt5\");\n * PIXI.Texture.addToCache(texture, \"bunny.dxt5\");\n * ```\n * @memberof PIXI\n */\nexport class CompressedTextureResource extends BlobResource\n{\n    /** The compression format */\n    public format: INTERNAL_FORMATS;\n    /**\n     * The number of mipmap levels stored in the resource buffer.\n     * @default 1\n     */\n    public levels: number;\n\n    // Easy access to the WebGL extension providing support for the compression format via ContextSystem\n    private _extension: 's3tc' | 's3tc_sRGB' | 'atc' | 'astc' | 'etc' | 'etc1' | 'pvrtc';\n    // Buffer views for each mipmap level in the main buffer\n    private _levelBuffers: CompressedLevelBuffer[];\n\n    /**\n     * @param source - the buffer/URL holding the compressed texture data\n     * @param options\n     * @param {PIXI.INTERNAL_FORMATS} options.format - the compression format\n     * @param {number} options.width - the image width in pixels.\n     * @param {number} options.height - the image height in pixels.\n     * @param {number} [options.level=1] - the mipmap levels stored in the compressed texture, including level 0.\n     * @param {number} [options.levelBuffers] - the buffers for each mipmap level. `CompressedTextureResource` can allows you\n     *      to pass `null` for `source`, for cases where each level is stored in non-contiguous memory.\n     */\n    constructor(source: string | Uint8Array | Uint32Array, options: ICompressedTextureResourceOptions)\n    {\n        super(source, options);\n\n        this.format = options.format;\n        this.levels = options.levels || 1;\n\n        this._width = options.width;\n        this._height = options.height;\n\n        this._extension = CompressedTextureResource._formatToExtension(this.format);\n\n        if (options.levelBuffers || this.buffer)\n        {\n            // ViewableBuffer doesn't support byteOffset :-( so allow source to be Uint8Array\n            this._levelBuffers = options.levelBuffers\n                || CompressedTextureResource._createLevelBuffers(\n                    source instanceof Uint8Array ? source : this.buffer.uint8View,\n                    this.format,\n                    this.levels,\n                    4, 4, // PVRTC has 8x4 blocks in 2bpp mode\n                    this.width,\n                    this.height);\n        }\n    }\n\n    /**\n     * @override\n     * @param renderer - A reference to the current renderer\n     * @param _texture - the texture\n     * @param _glTexture - texture instance for this webgl context\n     */\n    upload(renderer: Renderer, _texture: BaseTexture, _glTexture: GLTexture): boolean\n    {\n        const gl = renderer.gl;\n        const extension = renderer.context.extensions[this._extension];\n\n        if (!extension)\n        {\n            throw new Error(`${this._extension} textures are not supported on the current machine`);\n        }\n        if (!this._levelBuffers)\n        {\n            // Do not try to upload data before BlobResource loads, unless the levelBuffers were provided directly!\n            return false;\n        }\n\n        for (let i = 0, j = this.levels; i < j; i++)\n        {\n            const { levelID, levelWidth, levelHeight, levelBuffer } = this._levelBuffers[i];\n\n            gl.compressedTexImage2D(gl.TEXTURE_2D, levelID, this.format, levelWidth, levelHeight, 0, levelBuffer);\n        }\n\n        return true;\n    }\n\n    /** @protected */\n    protected onBlobLoaded(): void\n    {\n        this._levelBuffers = CompressedTextureResource._createLevelBuffers(\n            this.buffer.uint8View,\n            this.format,\n            this.levels,\n            4, 4, // PVRTC has 8x4 blocks in 2bpp mode\n            this.width,\n            this.height);\n    }\n\n    /**\n     * Returns the key (to ContextSystem#extensions) for the WebGL extension supporting the compression format\n     * @private\n     * @param format - the compression format to get the extension for.\n     */\n    private static _formatToExtension(format: INTERNAL_FORMATS):\n    's3tc' | 's3tc_sRGB' | 'atc' |\n    'astc' | 'etc' | 'etc1' | 'pvrtc'\n    {\n        if (format >= 0x83F0 && format <= 0x83F3)\n        {\n            return 's3tc';\n        }\n        else if (format >= 0x9270 && format <= 0x9279)\n        {\n            return 'etc';\n        }\n        else if (format >= 0x8C00 && format <= 0x8C03)\n        {\n            return 'pvrtc';\n        }\n        else if (format >= 0x8D64)\n        {\n            return 'etc1';\n        }\n        else if (format >= 0x8C92 && format <= 0x87EE)\n        {\n            return 'atc';\n        }\n\n        throw new Error('Invalid (compressed) texture format given!');\n    }\n\n    /**\n     * Pre-creates buffer views for each mipmap level\n     * @private\n     * @param buffer -\n     * @param format - compression formats\n     * @param levels - mipmap levels\n     * @param blockWidth -\n     * @param blockHeight -\n     * @param imageWidth - width of the image in pixels\n     * @param imageHeight - height of the image in pixels\n     */\n    private static _createLevelBuffers(\n        buffer: Uint8Array,\n        format: INTERNAL_FORMATS,\n        levels: number,\n        blockWidth: number,\n        blockHeight: number,\n        imageWidth: number,\n        imageHeight: number\n    ): CompressedLevelBuffer[]\n    {\n        // The byte-size of the first level buffer\n        const buffers = new Array<CompressedLevelBuffer>(levels);\n\n        let offset = buffer.byteOffset;\n\n        let levelWidth = imageWidth;\n        let levelHeight = imageHeight;\n        let alignedLevelWidth = (levelWidth + blockWidth - 1) & ~(blockWidth - 1);\n        let alignedLevelHeight = (levelHeight + blockHeight - 1) & ~(blockHeight - 1);\n\n        let levelSize = alignedLevelWidth * alignedLevelHeight * INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[format];\n\n        for (let i = 0; i < levels; i++)\n        {\n            buffers[i] = {\n                levelID: i,\n                levelWidth: levels > 1 ? levelWidth : alignedLevelWidth,\n                levelHeight: levels > 1 ? levelHeight : alignedLevelHeight,\n                levelBuffer: new Uint8Array(buffer.buffer, offset, levelSize)\n            };\n\n            offset += levelSize;\n\n            // Calculate levelBuffer dimensions for next iteration\n            levelWidth = (levelWidth >> 1) || 1;\n            levelHeight = (levelHeight >> 1) || 1;\n            alignedLevelWidth = (levelWidth + blockWidth - 1) & ~(blockWidth - 1);\n            alignedLevelHeight = (levelHeight + blockHeight - 1) & ~(blockHeight - 1);\n            levelSize = alignedLevelWidth * alignedLevelHeight * INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[format];\n        }\n\n        return buffers;\n    }\n}\n", "import { LoaderResource } from '@pixi/loaders';\nimport { url } from '@pixi/utils';\nimport { settings } from '@pixi/settings';\n\nimport type { Loader } from '@pixi/loaders';\nimport type { INTERNAL_FORMATS } from '../const';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * Schema for compressed-texture manifests\n * @ignore\n * @see PIXI.CompressedTextureLoader\n */\nexport type CompressedTextureManifest = {\n    textures: Array<{ src: string, format?: keyof INTERNAL_FORMATS}>,\n    cacheID: string;\n};\n\n// Missing typings? - https://github.com/microsoft/TypeScript/issues/39655\n/** Compressed texture extensions */\n/* eslint-disable camelcase */\nexport type CompressedTextureExtensions = {\n    s3tc?: WEBGL_compressed_texture_s3tc,\n    s3tc_sRGB: WEBGL_compressed_texture_s3tc_srgb,\n    etc: any,\n    etc1: any,\n    pvrtc: any,\n    atc: any,\n    astc: WEBGL_compressed_texture_astc\n};\nexport type CompressedTextureExtensionRef = keyof CompressedTextureExtensions;\n/* eslint-enable camelcase */\n\n/**\n * Loader plugin for handling compressed textures for all platforms.\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n */\nexport class CompressedTextureLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**  Map of available texture extensions. */\n    private static _textureExtensions: Partial<CompressedTextureExtensions>;\n\n    /** Map of available texture formats. */\n    private static _textureFormats: { [P in keyof INTERNAL_FORMATS]?: number };\n\n    /**\n     * Called after a compressed-textures manifest is loaded.\n     *\n     * This will then load the correct compression format for the device. Your manifest should adhere\n     * to the following schema:\n     *\n     * ```js\n     * import { INTERNAL_FORMATS } from '@pixi/constants';\n     *\n     * type CompressedTextureManifest = {\n     *  textures: Array<{ src: string, format?: keyof INTERNAL_FORMATS}>,\n     *  cacheID: string;\n     * };\n     * ```\n     *\n     * This is an example of a .json manifest file\n     *\n     * ```json\n     * {\n     *   \"cacheID\":\"asset\",\n     *   \"textures\":[\n     *     { \"src\":\"asset.fallback.png\" },\n     *     { \"format\":\"COMPRESSED_RGBA_S3TC_DXT5_EXT\", \"src\":\"asset.s3tc.ktx\" },\n     *     { \"format\":\"COMPRESSED_RGBA8_ETC2_EAC\", \"src\":\"asset.etc.ktx\" },\n     *     { \"format\":\"RGBA_PVRTC_4BPPV1_IMG\", \"src\":\"asset.pvrtc.ktx\" }\n     *   ]\n     * }\n     * ```\n     */\n    static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        const data: CompressedTextureManifest = resource.data;\n        const loader = this as unknown as Loader;\n\n        if (resource.type === LoaderResource.TYPE.JSON\n            && data\n            && data.cacheID\n            && data.textures)\n        {\n            const textures = data.textures;\n\n            let textureURL: string;\n            let fallbackURL: string;\n\n            // Search for an extension that holds one the formats\n            for (let i = 0, j = textures.length; i < j; i++)\n            {\n                const texture = textures[i];\n                const url = texture.src;\n                const format = texture.format;\n\n                if (!format)\n                {\n                    fallbackURL = url;\n                }\n                if (CompressedTextureLoader.textureFormats[format])\n                {\n                    textureURL = url;\n                    break;\n                }\n            }\n\n            textureURL = textureURL || fallbackURL;\n\n            // Make sure we have a URL\n            if (!textureURL)\n            {\n                next(new Error(`Cannot load compressed-textures in ${resource.url}, make sure you provide a fallback`));\n\n                return;\n            }\n            if (textureURL === resource.url)\n            {\n                // Prevent infinite loops\n                next(new Error('URL of compressed texture cannot be the same as the manifest\\'s URL'));\n\n                return;\n            }\n\n            const loadOptions = {\n                crossOrigin: resource.crossOrigin,\n                metadata: resource.metadata.imageMetadata,\n                parentResource: resource\n            };\n\n            const resourcePath = url.resolve(resource.url.replace(loader.baseUrl, ''), textureURL);\n            const resourceName = data.cacheID;\n\n            // The appropriate loader should register the texture\n            loader.add(resourceName, resourcePath, loadOptions, (res: LoaderResource) =>\n            {\n                if (res.error)\n                {\n                    next(res.error);\n\n                    return;\n                }\n\n                const { texture = null, textures = {} } = res;\n\n                // Make sure texture/textures is assigned to parent resource\n                Object.assign(resource, { texture, textures });\n\n                // Pass along any error\n                next();\n            });\n        }\n        else\n        {\n            next();\n        }\n    }\n\n    /**  Map of available texture extensions. */\n    public static get textureExtensions(): Partial<CompressedTextureExtensions>\n    {\n        if (!CompressedTextureLoader._textureExtensions)\n        {\n            // Auto-detect WebGL compressed-texture extensions\n            const canvas = settings.ADAPTER.createCanvas();\n            const gl = canvas.getContext('webgl');\n\n            if (!gl)\n            {\n                // #if _DEBUG\n                console.warn('WebGL not available for compressed textures. Silently failing.');\n                // #endif\n\n                return {};\n            }\n\n            const extensions = {\n                s3tc: gl.getExtension('WEBGL_compressed_texture_s3tc'),\n                s3tc_sRGB: gl.getExtension('WEBGL_compressed_texture_s3tc_srgb'), /* eslint-disable-line camelcase */\n                etc: gl.getExtension('WEBGL_compressed_texture_etc'),\n                etc1: gl.getExtension('WEBGL_compressed_texture_etc1'),\n                pvrtc: gl.getExtension('WEBGL_compressed_texture_pvrtc')\n                    || gl.getExtension('WEBKIT_WEBGL_compressed_texture_pvrtc'),\n                atc: gl.getExtension('WEBGL_compressed_texture_atc'),\n                astc: gl.getExtension('WEBGL_compressed_texture_astc')\n            };\n\n            CompressedTextureLoader._textureExtensions = extensions;\n        }\n\n        return CompressedTextureLoader._textureExtensions;\n    }\n\n    /** Map of available texture formats. */\n    public static get textureFormats(): { [P in keyof INTERNAL_FORMATS]?: number }\n    {\n        if (!CompressedTextureLoader._textureFormats)\n        {\n            const extensions = CompressedTextureLoader.textureExtensions;\n\n            CompressedTextureLoader._textureFormats = {};\n\n            // Assign all available compressed-texture formats\n            for (const extensionName in extensions)\n            {\n                const extension = extensions[extensionName as CompressedTextureExtensionRef];\n\n                if (!extension)\n                {\n                    continue;\n                }\n\n                Object.assign(\n                    CompressedTextureLoader._textureFormats,\n                    Object.getPrototypeOf(extension));\n            }\n        }\n\n        return CompressedTextureLoader._textureFormats;\n    }\n}\n", "import { MIPMAP_MODES, ALPHA_MODES } from '@pixi/constants';\nimport { BaseTexture, Texture } from '@pixi/core';\n\nimport type { LoaderResource, IResourceMetadata } from '@pixi/loaders';\nimport type { CompressedTextureResource } from '../resources/CompressedTextureResource';\n\n/**\n * Result when calling registerCompressedTextures.\n * @ignore\n */\ntype CompressedTexturesResult = Pick<LoaderResource, 'textures' | 'texture'>;\n\n/**\n * Creates base-textures and textures for each compressed-texture resource and adds them into the global\n * texture cache. The first texture has two IDs - `${url}`, `${url}-1`; while the rest have an ID of the\n * form `${url}-i`.\n * @param url - the original address of the resources\n * @param resources - the resources backing texture data\n * @ignore\n */\nexport function registerCompressedTextures(\n    url: string,\n    resources: CompressedTextureResource[],\n    metadata: IResourceMetadata\n): CompressedTexturesResult\n{\n    const result: CompressedTexturesResult = {\n        textures: {},\n        texture: null,\n    };\n\n    if (!resources)\n    {\n        return result;\n    }\n\n    const textures = resources.map((resource) =>\n        (\n            new Texture(new BaseTexture(resource, Object.assign({\n                mipmap: MIPMAP_MODES.OFF,\n                alphaMode: ALPHA_MODES.NO_PREMULTIPLIED_ALPHA\n            }, metadata)))\n        ));\n\n    textures.forEach((texture, i) =>\n    {\n        const { baseTexture } = texture;\n        const cacheID = `${url}-${i + 1}`;\n\n        BaseTexture.addToCache(baseTexture, cacheID);\n        Texture.addToCache(texture, cacheID);\n\n        if (i === 0)\n        {\n            BaseTexture.addToCache(baseTexture, url);\n            Texture.addToCache(texture, url);\n            result.texture = texture;\n        }\n\n        result.textures[cacheID] = texture;\n    });\n\n    return result;\n}\n", "import { CompressedTextureResource } from '../resources';\nimport { INTERNAL_FORMATS, INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from '../const';\n\nconst DDS_MAGIC_SIZE = 4;\nconst DDS_HEADER_SIZE = 124;\nconst DDS_HEADER_PF_SIZE = 32;\nconst DDS_HEADER_DX10_SIZE = 20;\n\n// DDS file format magic word\nconst DDS_MAGIC = 0x20534444;\n\n/**\n * DWORD offsets of the DDS file header fields (relative to file start).\n * @ignore\n */\nconst DDS_FIELDS = {\n    SIZE: 1,\n    FLAGS: 2,\n    HEIGHT: 3,\n    WIDTH: 4,\n    MIPMAP_COUNT: 7,\n    PIXEL_FORMAT: 19,\n};\n\n/**\n * DWORD offsets of the DDS PIXEL_FORMAT fields.\n * @ignore\n */\nconst DDS_PF_FIELDS = {\n    SIZE: 0,\n    FLAGS: 1,\n    FOURCC: 2,\n    RGB_BITCOUNT: 3,\n    R_BIT_MASK: 4,\n    G_BIT_MASK: 5,\n    B_BIT_MASK: 6,\n    A_BIT_MASK: 7\n};\n\n/**\n * DWORD offsets of the DDS_HEADER_DX10 fields.\n * @ignore\n */\nconst DDS_DX10_FIELDS = {\n    DXGI_FORMAT: 0,\n    RESOURCE_DIMENSION: 1,\n    MISC_FLAG: 2,\n    ARRAY_SIZE: 3,\n    MISC_FLAGS2: 4\n};\n\n/**\n * @see https://docs.microsoft.com/en-us/windows/win32/api/dxgiformat/ne-dxgiformat-dxgi_format\n * @ignore\n */\n// This is way over-blown for us! Lend us a hand, and remove the ones that aren't used (but set the remaining\n// ones to their correct value)\nenum DXGI_FORMAT\n    {\n    DXGI_FORMAT_UNKNOWN,\n    DXGI_FORMAT_R32G32B32A32_TYPELESS,\n    DXGI_FORMAT_R32G32B32A32_FLOAT,\n    DXGI_FORMAT_R32G32B32A32_UINT,\n    DXGI_FORMAT_R32G32B32A32_SINT,\n    DXGI_FORMAT_R32G32B32_TYPELESS,\n    DXGI_FORMAT_R32G32B32_FLOAT,\n    DXGI_FORMAT_R32G32B32_UINT,\n    DXGI_FORMAT_R32G32B32_SINT,\n    DXGI_FORMAT_R16G16B16A16_TYPELESS,\n    DXGI_FORMAT_R16G16B16A16_FLOAT,\n    DXGI_FORMAT_R16G16B16A16_UNORM,\n    DXGI_FORMAT_R16G16B16A16_UINT,\n    DXGI_FORMAT_R16G16B16A16_SNORM,\n    DXGI_FORMAT_R16G16B16A16_SINT,\n    DXGI_FORMAT_R32G32_TYPELESS,\n    DXGI_FORMAT_R32G32_FLOAT,\n    DXGI_FORMAT_R32G32_UINT,\n    DXGI_FORMAT_R32G32_SINT,\n    DXGI_FORMAT_R32G8X24_TYPELESS,\n    DXGI_FORMAT_D32_FLOAT_S8X24_UINT,\n    DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS,\n    DXGI_FORMAT_X32_TYPELESS_G8X24_UINT,\n    DXGI_FORMAT_R10G10B10A2_TYPELESS,\n    DXGI_FORMAT_R10G10B10A2_UNORM,\n    DXGI_FORMAT_R10G10B10A2_UINT,\n    DXGI_FORMAT_R11G11B10_FLOAT,\n    DXGI_FORMAT_R8G8B8A8_TYPELESS,\n    DXGI_FORMAT_R8G8B8A8_UNORM,\n    DXGI_FORMAT_R8G8B8A8_UNORM_SRGB,\n    DXGI_FORMAT_R8G8B8A8_UINT,\n    DXGI_FORMAT_R8G8B8A8_SNORM,\n    DXGI_FORMAT_R8G8B8A8_SINT,\n    DXGI_FORMAT_R16G16_TYPELESS,\n    DXGI_FORMAT_R16G16_FLOAT,\n    DXGI_FORMAT_R16G16_UNORM,\n    DXGI_FORMAT_R16G16_UINT,\n    DXGI_FORMAT_R16G16_SNORM,\n    DXGI_FORMAT_R16G16_SINT,\n    DXGI_FORMAT_R32_TYPELESS,\n    DXGI_FORMAT_D32_FLOAT,\n    DXGI_FORMAT_R32_FLOAT,\n    DXGI_FORMAT_R32_UINT,\n    DXGI_FORMAT_R32_SINT,\n    DXGI_FORMAT_R24G8_TYPELESS,\n    DXGI_FORMAT_D24_UNORM_S8_UINT,\n    DXGI_FORMAT_R24_UNORM_X8_TYPELESS,\n    DXGI_FORMAT_X24_TYPELESS_G8_UINT,\n    DXGI_FORMAT_R8G8_TYPELESS,\n    DXGI_FORMAT_R8G8_UNORM,\n    DXGI_FORMAT_R8G8_UINT,\n    DXGI_FORMAT_R8G8_SNORM,\n    DXGI_FORMAT_R8G8_SINT,\n    DXGI_FORMAT_R16_TYPELESS,\n    DXGI_FORMAT_R16_FLOAT,\n    DXGI_FORMAT_D16_UNORM,\n    DXGI_FORMAT_R16_UNORM,\n    DXGI_FORMAT_R16_UINT,\n    DXGI_FORMAT_R16_SNORM,\n    DXGI_FORMAT_R16_SINT,\n    DXGI_FORMAT_R8_TYPELESS,\n    DXGI_FORMAT_R8_UNORM,\n    DXGI_FORMAT_R8_UINT,\n    DXGI_FORMAT_R8_SNORM,\n    DXGI_FORMAT_R8_SINT,\n    DXGI_FORMAT_A8_UNORM,\n    DXGI_FORMAT_R1_UNORM,\n    DXGI_FORMAT_R9G9B9E5_SHAREDEXP,\n    DXGI_FORMAT_R8G8_B8G8_UNORM,\n    DXGI_FORMAT_G8R8_G8B8_UNORM,\n    DXGI_FORMAT_BC1_TYPELESS,\n    DXGI_FORMAT_BC1_UNORM,\n    DXGI_FORMAT_BC1_UNORM_SRGB,\n    DXGI_FORMAT_BC2_TYPELESS,\n    DXGI_FORMAT_BC2_UNORM,\n    DXGI_FORMAT_BC2_UNORM_SRGB,\n    DXGI_FORMAT_BC3_TYPELESS,\n    DXGI_FORMAT_BC3_UNORM,\n    DXGI_FORMAT_BC3_UNORM_SRGB,\n    DXGI_FORMAT_BC4_TYPELESS,\n    DXGI_FORMAT_BC4_UNORM,\n    DXGI_FORMAT_BC4_SNORM,\n    DXGI_FORMAT_BC5_TYPELESS,\n    DXGI_FORMAT_BC5_UNORM,\n    DXGI_FORMAT_BC5_SNORM,\n    DXGI_FORMAT_B5G6R5_UNORM,\n    DXGI_FORMAT_B5G5R5A1_UNORM,\n    DXGI_FORMAT_B8G8R8A8_UNORM,\n    DXGI_FORMAT_B8G8R8X8_UNORM,\n    DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM,\n    DXGI_FORMAT_B8G8R8A8_TYPELESS,\n    DXGI_FORMAT_B8G8R8A8_UNORM_SRGB,\n    DXGI_FORMAT_B8G8R8X8_TYPELESS,\n    DXGI_FORMAT_B8G8R8X8_UNORM_SRGB,\n    DXGI_FORMAT_BC6H_TYPELESS,\n    DXGI_FORMAT_BC6H_UF16,\n    DXGI_FORMAT_BC6H_SF16,\n    DXGI_FORMAT_BC7_TYPELESS,\n    DXGI_FORMAT_BC7_UNORM,\n    DXGI_FORMAT_BC7_UNORM_SRGB,\n    DXGI_FORMAT_AYUV,\n    DXGI_FORMAT_Y410,\n    DXGI_FORMAT_Y416,\n    DXGI_FORMAT_NV12,\n    DXGI_FORMAT_P010,\n    DXGI_FORMAT_P016,\n    DXGI_FORMAT_420_OPAQUE,\n    DXGI_FORMAT_YUY2,\n    DXGI_FORMAT_Y210,\n    DXGI_FORMAT_Y216,\n    DXGI_FORMAT_NV11,\n    DXGI_FORMAT_AI44,\n    DXGI_FORMAT_IA44,\n    DXGI_FORMAT_P8,\n    DXGI_FORMAT_A8P8,\n    DXGI_FORMAT_B4G4R4A4_UNORM,\n    DXGI_FORMAT_P208,\n    DXGI_FORMAT_V208,\n    DXGI_FORMAT_V408,\n    DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE,\n    DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE,\n    DXGI_FORMAT_FORCE_UINT\n}\n\n/**\n * Possible values of the field {@link DDS_DX10_FIELDS.RESOURCE_DIMENSION}\n * @ignore\n */\nenum D3D10_RESOURCE_DIMENSION\n    {\n    DDS_DIMENSION_TEXTURE1D = 2,\n    DDS_DIMENSION_TEXTURE2D = 3,\n    DDS_DIMENSION_TEXTURE3D = 6\n}\n\nconst PF_FLAGS = 1;\n\n// PIXEL_FORMAT flags\nconst DDPF_ALPHA = 0x2;\nconst DDPF_FOURCC = 0x4;\nconst DDPF_RGB = 0x40;\nconst DDPF_YUV = 0x200;\nconst DDPF_LUMINANCE = 0x20000;\n\n// Four character codes for DXTn formats\nconst FOURCC_DXT1 = 0x31545844;\nconst FOURCC_DXT3 = 0x33545844;\nconst FOURCC_DXT5 = 0x35545844;\nconst FOURCC_DX10 = 0x30315844;\n\n// Cubemap texture flag (for DDS_DX10_FIELDS.MISC_FLAG)\nconst DDS_RESOURCE_MISC_TEXTURECUBE = 0x4;\n\n/**\n * Maps `FOURCC_*` formats to internal formats (see {@link PIXI.INTERNAL_FORMATS}).\n * @ignore\n */\nconst FOURCC_TO_FORMAT: { [id: number]: number } = {\n    [FOURCC_DXT1]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n    [FOURCC_DXT3]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    [FOURCC_DXT5]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT\n};\n\n/**\n * Maps {@link DXGI_FORMAT} to types/internal-formats (see {@link PIXI.TYPES}, {@link PIXI.INTERNAL_FORMATS})\n * @ignore\n */\nconst DXGI_TO_FORMAT: { [id: number]: number } = {\n    // WEBGL_compressed_texture_s3tc\n    [DXGI_FORMAT.DXGI_FORMAT_BC1_TYPELESS]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC1_UNORM]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC2_TYPELESS]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC2_UNORM]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC3_TYPELESS]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC3_UNORM]: INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n\n    // WEBGL_compressed_texture_s3tc_srgb\n    [DXGI_FORMAT.DXGI_FORMAT_BC1_UNORM_SRGB]: INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC2_UNORM_SRGB]: INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,\n    [DXGI_FORMAT.DXGI_FORMAT_BC3_UNORM_SRGB]: INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT\n};\n\n/**\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n * @see https://docs.microsoft.com/en-us/windows/win32/direct3ddds/dx-graphics-dds-pguide\n */\n/**\n * Parses the DDS file header, generates base-textures, and puts them into the texture cache.\n * @param arrayBuffer\n */\nexport function parseDDS(arrayBuffer: ArrayBuffer): CompressedTextureResource[]\n{\n    const data = new Uint32Array(arrayBuffer);\n    const magicWord = data[0];\n\n    if (magicWord !== DDS_MAGIC)\n    {\n        throw new Error('Invalid DDS file magic word');\n    }\n\n    const header = new Uint32Array(arrayBuffer, 0, DDS_HEADER_SIZE / Uint32Array.BYTES_PER_ELEMENT);\n\n    // DDS header fields\n    const height = header[DDS_FIELDS.HEIGHT];\n    const width = header[DDS_FIELDS.WIDTH];\n    const mipmapCount = header[DDS_FIELDS.MIPMAP_COUNT];\n\n    // PIXEL_FORMAT fields\n    const pixelFormat = new Uint32Array(\n        arrayBuffer,\n        DDS_FIELDS.PIXEL_FORMAT * Uint32Array.BYTES_PER_ELEMENT,\n        DDS_HEADER_PF_SIZE / Uint32Array.BYTES_PER_ELEMENT);\n    const formatFlags = pixelFormat[PF_FLAGS];\n\n    // File contains compressed texture(s)\n    if (formatFlags & DDPF_FOURCC)\n    {\n        const fourCC = pixelFormat[DDS_PF_FIELDS.FOURCC];\n\n        // File contains one DXTn compressed texture\n        if (fourCC !== FOURCC_DX10)\n        {\n            const internalFormat = FOURCC_TO_FORMAT[fourCC];\n\n            const dataOffset = DDS_MAGIC_SIZE + DDS_HEADER_SIZE;\n            const texData = new Uint8Array(arrayBuffer, dataOffset);\n\n            const resource = new CompressedTextureResource(texData, {\n                format: internalFormat,\n                width,\n                height,\n                levels: mipmapCount // CompressedTextureResource will separate the levelBuffers for us!\n            });\n\n            return [resource];\n        }\n\n        // FOURCC_DX10 indicates there is a 20-byte DDS_HEADER_DX10 after DDS_HEADER\n        const dx10Offset = DDS_MAGIC_SIZE + DDS_HEADER_SIZE;\n        const dx10Header = new Uint32Array(\n            data.buffer,\n            dx10Offset,\n            DDS_HEADER_DX10_SIZE / Uint32Array.BYTES_PER_ELEMENT);\n        const dxgiFormat = dx10Header[DDS_DX10_FIELDS.DXGI_FORMAT];\n        const resourceDimension = dx10Header[DDS_DX10_FIELDS.RESOURCE_DIMENSION];\n        const miscFlag = dx10Header[DDS_DX10_FIELDS.MISC_FLAG];\n        const arraySize = dx10Header[DDS_DX10_FIELDS.ARRAY_SIZE];\n\n        // Map dxgiFormat to PIXI.INTERNAL_FORMATS\n        const internalFormat = DXGI_TO_FORMAT[dxgiFormat];\n\n        if (internalFormat === undefined)\n        {\n            throw new Error(`DDSParser cannot parse texture data with DXGI format ${dxgiFormat}`);\n        }\n        if (miscFlag === DDS_RESOURCE_MISC_TEXTURECUBE)\n        {\n            // FIXME: Anybody excited about cubemap compressed textures?\n            throw new Error('DDSParser does not support cubemap textures');\n        }\n        if (resourceDimension === D3D10_RESOURCE_DIMENSION.DDS_DIMENSION_TEXTURE3D)\n        {\n            // FIXME: Anybody excited about 3D compressed textures?\n            throw new Error('DDSParser does not supported 3D texture data');\n        }\n\n        // Uint8Array buffers of image data, including all mipmap levels in each image\n        const imageBuffers = new Array<Uint8Array>();\n        const dataOffset = DDS_MAGIC_SIZE\n                + DDS_HEADER_SIZE\n                + DDS_HEADER_DX10_SIZE;\n\n        if (arraySize === 1)\n        {\n            // No need bothering with the imageSize calculation!\n            imageBuffers.push(new Uint8Array(arrayBuffer, dataOffset));\n        }\n        else\n        {\n            // Calculate imageSize for each texture, and then locate each image's texture data\n\n            const pixelSize = INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[internalFormat];\n            let imageSize = 0;\n            let levelWidth = width;\n            let levelHeight = height;\n\n            for (let i = 0; i < mipmapCount; i++)\n            {\n                const alignedLevelWidth = Math.max(1, (levelWidth + 3) & ~3);\n                const alignedLevelHeight = Math.max(1, (levelHeight + 3) & ~3);\n\n                const levelSize = alignedLevelWidth * alignedLevelHeight * pixelSize;\n\n                imageSize += levelSize;\n\n                levelWidth = levelWidth >>> 1;\n                levelHeight = levelHeight >>> 1;\n            }\n\n            let imageOffset = dataOffset;\n\n            // NOTE: Cubemaps have 6-images per texture (but they aren't supported so ^_^)\n            for (let i = 0; i < arraySize; i++)\n            {\n                imageBuffers.push(new Uint8Array(arrayBuffer, imageOffset, imageSize));\n                imageOffset += imageSize;\n            }\n        }\n\n        // Uint8Array -> CompressedTextureResource, and we're done!\n        return imageBuffers.map((buffer) => new CompressedTextureResource(buffer, {\n            format: internalFormat,\n            width,\n            height,\n            levels: mipmapCount\n        }));\n    }\n    if (formatFlags & DDPF_RGB)\n    {\n        // FIXME: We might want to allow uncompressed *.dds files?\n        throw new Error('DDSParser does not support uncompressed texture data.');\n    }\n    if (formatFlags & DDPF_YUV)\n    {\n        // FIXME: Does anybody need this feature?\n        throw new Error('DDSParser does not supported YUV uncompressed texture data.');\n    }\n    if (formatFlags & DDPF_LUMINANCE)\n    {\n        // FIXME: Microsoft says older DDS filers use this feature! Probably not worth the effort!\n        throw new Error('DDSParser does not support single-channel (lumninance) texture data!');\n    }\n    if (formatFlags & DDPF_ALPHA)\n    {\n        // FIXME: I'm tired! See above =)\n        throw new Error('DDSParser does not support single-channel (alpha) texture data!');\n    }\n\n    throw new Error('DDSParser failed to load a texture file due to an unknown reason!');\n}\n\n", "import { FORMATS, TYPES } from '@pixi/constants';\nimport { BufferResource } from '@pixi/core';\n\nimport { INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from '../const';\nimport type { CompressedLevelBuffer } from '../resources';\nimport { CompressedTextureResource } from '../resources';\n\n/**\n * The 12-byte KTX file identifier\n * @see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/#2.1\n * @ignore\n */\nconst FILE_IDENTIFIER = [0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A];\n\n/**\n * The value stored in the \"endianness\" field.\n * @see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/#2.2\n * @ignore\n */\nconst ENDIANNESS = 0x04030201;\n\n/**\n * Byte offsets of the KTX file header fields\n * @ignore\n */\nconst KTX_FIELDS = {\n    FILE_IDENTIFIER: 0,\n    ENDIANNESS: 12,\n    GL_TYPE: 16,\n    GL_TYPE_SIZE: 20,\n    GL_FORMAT: 24,\n    GL_INTERNAL_FORMAT: 28,\n    GL_BASE_INTERNAL_FORMAT: 32,\n    PIXEL_WIDTH: 36,\n    PIXEL_HEIGHT: 40,\n    PIXEL_DEPTH: 44,\n    NUMBER_OF_ARRAY_ELEMENTS: 48,\n    NUMBER_OF_FACES: 52,\n    NUMBER_OF_MIPMAP_LEVELS: 56,\n    BYTES_OF_KEY_VALUE_DATA: 60\n};\n\n/**\n * Byte size of the file header fields in {@code KTX_FIELDS}\n * @ignore\n */\nconst FILE_HEADER_SIZE = 64;\n\n/**\n * Maps {@link PIXI.TYPES} to the bytes taken per component, excluding those ones that are bit-fields.\n * @ignore\n */\nexport const TYPES_TO_BYTES_PER_COMPONENT: { [id: number]: number } = {\n    [TYPES.UNSIGNED_BYTE]: 1,\n    [TYPES.UNSIGNED_SHORT]: 2,\n    [TYPES.INT]: 4,\n    [TYPES.UNSIGNED_INT]: 4,\n    [TYPES.FLOAT]: 4,\n    [TYPES.HALF_FLOAT]: 8\n};\n\n/**\n * Number of components in each {@link PIXI.FORMATS}\n * @ignore\n */\nexport const FORMATS_TO_COMPONENTS: { [id: number]: number } = {\n    [FORMATS.RGBA]: 4,\n    [FORMATS.RGB]: 3,\n    [FORMATS.RG]: 2,\n    [FORMATS.RED]: 1,\n    [FORMATS.LUMINANCE]: 1,\n    [FORMATS.LUMINANCE_ALPHA]: 2,\n    [FORMATS.ALPHA]: 1\n};\n\n/**\n * Number of bytes per pixel in bit-field types in {@link PIXI.TYPES}\n * @ignore\n */\nexport const TYPES_TO_BYTES_PER_PIXEL: { [id: number]: number } = {\n    [TYPES.UNSIGNED_SHORT_4_4_4_4]: 2,\n    [TYPES.UNSIGNED_SHORT_5_5_5_1]: 2,\n    [TYPES.UNSIGNED_SHORT_5_6_5]: 2\n};\n\nexport function parseKTX(url: string, arrayBuffer: ArrayBuffer, loadKeyValueData = false): {\n    compressed?: CompressedTextureResource[]\n    uncompressed?: { resource: BufferResource, type: TYPES, format: FORMATS }[]\n    kvData: Map<string, DataView> | null\n}\n{\n    const dataView = new DataView(arrayBuffer);\n\n    if (!validate(url, dataView))\n    {\n        return null;\n    }\n\n    const littleEndian = dataView.getUint32(KTX_FIELDS.ENDIANNESS, true) === ENDIANNESS;\n    const glType = dataView.getUint32(KTX_FIELDS.GL_TYPE, littleEndian);\n    // const glTypeSize = dataView.getUint32(KTX_FIELDS.GL_TYPE_SIZE, littleEndian);\n    const glFormat = dataView.getUint32(KTX_FIELDS.GL_FORMAT, littleEndian);\n    const glInternalFormat = dataView.getUint32(KTX_FIELDS.GL_INTERNAL_FORMAT, littleEndian);\n    const pixelWidth = dataView.getUint32(KTX_FIELDS.PIXEL_WIDTH, littleEndian);\n    const pixelHeight = dataView.getUint32(KTX_FIELDS.PIXEL_HEIGHT, littleEndian) || 1;// \"pixelHeight = 0\" -> \"1\"\n    const pixelDepth = dataView.getUint32(KTX_FIELDS.PIXEL_DEPTH, littleEndian) || 1;// ^^\n    const numberOfArrayElements = dataView.getUint32(KTX_FIELDS.NUMBER_OF_ARRAY_ELEMENTS, littleEndian) || 1;// ^^\n    const numberOfFaces = dataView.getUint32(KTX_FIELDS.NUMBER_OF_FACES, littleEndian);\n    const numberOfMipmapLevels = dataView.getUint32(KTX_FIELDS.NUMBER_OF_MIPMAP_LEVELS, littleEndian);\n    const bytesOfKeyValueData = dataView.getUint32(KTX_FIELDS.BYTES_OF_KEY_VALUE_DATA, littleEndian);\n\n    // Whether the platform architecture is little endian. If littleEndian !== platformLittleEndian, then the\n    // file contents must be endian-converted!\n    // TODO: Endianness conversion\n    // const platformLittleEndian = new Uint8Array((new Uint32Array([ENDIANNESS])).buffer)[0] === 0x01;\n\n    if (pixelHeight === 0 || pixelDepth !== 1)\n    {\n        throw new Error('Only 2D textures are supported');\n    }\n    if (numberOfFaces !== 1)\n    {\n        throw new Error('CubeTextures are not supported by KTXLoader yet!');\n    }\n    if (numberOfArrayElements !== 1)\n    {\n        // TODO: Support splitting array-textures into multiple BaseTextures\n        throw new Error('WebGL does not support array textures');\n    }\n\n    // TODO: 8x4 blocks for 2bpp pvrtc\n    const blockWidth = 4;\n    const blockHeight = 4;\n\n    const alignedWidth = (pixelWidth + 3) & ~3;\n    const alignedHeight = (pixelHeight + 3) & ~3;\n    const imageBuffers = new Array<CompressedLevelBuffer[]>(numberOfArrayElements);\n    let imagePixels = pixelWidth * pixelHeight;\n\n    if (glType === 0)\n    {\n        // Align to 16 pixels (4x4 blocks)\n        imagePixels = alignedWidth * alignedHeight;\n    }\n\n    let imagePixelByteSize: number;\n\n    if (glType !== 0)\n    {\n        // Uncompressed texture format\n        if (TYPES_TO_BYTES_PER_COMPONENT[glType])\n        {\n            imagePixelByteSize = TYPES_TO_BYTES_PER_COMPONENT[glType] * FORMATS_TO_COMPONENTS[glFormat];\n        }\n        else\n        {\n            imagePixelByteSize = TYPES_TO_BYTES_PER_PIXEL[glType];\n        }\n    }\n    else\n    {\n        imagePixelByteSize = INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[glInternalFormat];\n    }\n\n    if (imagePixelByteSize === undefined)\n    {\n        throw new Error('Unable to resolve the pixel format stored in the *.ktx file!');\n    }\n\n    const kvData: Map<string, DataView> | null = loadKeyValueData\n        ? parseKvData(dataView, bytesOfKeyValueData, littleEndian)\n        : null;\n\n    const imageByteSize = imagePixels * imagePixelByteSize;\n    let mipByteSize = imageByteSize;\n    let mipWidth = pixelWidth;\n    let mipHeight = pixelHeight;\n    let alignedMipWidth = alignedWidth;\n    let alignedMipHeight = alignedHeight;\n    let imageOffset = FILE_HEADER_SIZE + bytesOfKeyValueData;\n\n    for (let mipmapLevel = 0; mipmapLevel < numberOfMipmapLevels; mipmapLevel++)\n    {\n        const imageSize = dataView.getUint32(imageOffset, littleEndian);\n        let elementOffset = imageOffset + 4;\n\n        for (let arrayElement = 0; arrayElement < numberOfArrayElements; arrayElement++)\n        {\n            // TODO: Maybe support 3D textures? :-)\n            // for (let zSlice = 0; zSlice < pixelDepth; zSlice)\n\n            let mips = imageBuffers[arrayElement];\n\n            if (!mips)\n            {\n                mips = imageBuffers[arrayElement] = new Array(numberOfMipmapLevels);\n            }\n\n            mips[mipmapLevel] = {\n                levelID: mipmapLevel,\n\n                // don't align mipWidth when texture not compressed! (glType not zero)\n                levelWidth: numberOfMipmapLevels > 1 || glType !== 0 ? mipWidth : alignedMipWidth,\n                levelHeight: numberOfMipmapLevels > 1 || glType !== 0 ? mipHeight : alignedMipHeight,\n                levelBuffer: new Uint8Array(arrayBuffer, elementOffset, mipByteSize)\n            };\n            elementOffset += mipByteSize;\n        }\n\n        // HINT: Aligns to 4-byte boundary after jumping imageSize (in lieu of mipPadding)\n        imageOffset += imageSize + 4;// (+4 to jump the imageSize field itself)\n        imageOffset = imageOffset % 4 !== 0 ? imageOffset + 4 - (imageOffset % 4) : imageOffset;\n\n        // Calculate mipWidth, mipHeight for _next_ iteration\n        mipWidth = (mipWidth >> 1) || 1;\n        mipHeight = (mipHeight >> 1) || 1;\n        alignedMipWidth = (mipWidth + blockWidth - 1) & ~(blockWidth - 1);\n        alignedMipHeight = (mipHeight + blockHeight - 1) & ~(blockHeight - 1);\n\n        // Each mipmap level is 4-times smaller?\n        mipByteSize = alignedMipWidth * alignedMipHeight * imagePixelByteSize;\n    }\n\n    // We use the levelBuffers feature of CompressedTextureResource b/c texture data is image-major, not level-major.\n    if (glType !== 0)\n    {\n        return {\n            uncompressed: imageBuffers.map((levelBuffers) =>\n            {\n                let buffer: Float32Array | Uint32Array | Int32Array | Uint8Array = levelBuffers[0].levelBuffer;\n                let convertToInt = false;\n\n                if (glType === TYPES.FLOAT)\n                {\n                    buffer = new Float32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n                else if (glType === TYPES.UNSIGNED_INT)\n                {\n                    convertToInt = true;\n                    buffer = new Uint32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n                else if (glType === TYPES.INT)\n                {\n                    convertToInt = true;\n                    buffer = new Int32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n\n                return {\n                    resource: new BufferResource(\n                        buffer,\n                        {\n                            width: levelBuffers[0].levelWidth,\n                            height: levelBuffers[0].levelHeight,\n                        }\n                    ),\n                    type: glType,\n                    format: convertToInt ? convertFormatToInteger(glFormat) : glFormat,\n                };\n            }),\n            kvData\n        };\n    }\n\n    return {\n        compressed: imageBuffers.map((levelBuffers) => new CompressedTextureResource(null, {\n            format: glInternalFormat,\n            width: pixelWidth,\n            height: pixelHeight,\n            levels: numberOfMipmapLevels,\n            levelBuffers,\n        })),\n        kvData\n    };\n}\n\n/**\n * Checks whether the arrayBuffer contains a valid *.ktx file.\n * @param url\n * @param dataView\n */\nfunction validate(url: string, dataView: DataView): boolean\n{\n    // NOTE: Do not optimize this into 3 32-bit integer comparison because the endianness\n    // of the data is not specified.\n    for (let i = 0; i < FILE_IDENTIFIER.length; i++)\n    {\n        if (dataView.getUint8(i) !== FILE_IDENTIFIER[i])\n        {\n            // #if _DEBUG\n            console.error(`${url} is not a valid *.ktx file!`);\n            // #endif\n\n            return false;\n        }\n    }\n\n    return true;\n}\n\nfunction convertFormatToInteger(format: FORMATS)\n{\n    switch (format)\n    {\n        case FORMATS.RGBA: return FORMATS.RGBA_INTEGER;\n        case FORMATS.RGB: return FORMATS.RGB_INTEGER;\n        case FORMATS.RG: return FORMATS.RG_INTEGER;\n        case FORMATS.RED: return FORMATS.RED_INTEGER;\n        default: return format;\n    }\n}\n\nfunction parseKvData(dataView: DataView, bytesOfKeyValueData: number, littleEndian: boolean): Map<string, DataView>\n{\n    const kvData = new Map<string, DataView>();\n    let bytesIntoKeyValueData = 0;\n\n    while (bytesIntoKeyValueData < bytesOfKeyValueData)\n    {\n        const keyAndValueByteSize = dataView.getUint32(FILE_HEADER_SIZE + bytesIntoKeyValueData, littleEndian);\n        const keyAndValueByteOffset = FILE_HEADER_SIZE + bytesIntoKeyValueData + 4;\n        const valuePadding = 3 - ((keyAndValueByteSize + 3) % 4);\n\n        // Bounds check\n        if (keyAndValueByteSize === 0 || keyAndValueByteSize > bytesOfKeyValueData - bytesIntoKeyValueData)\n        {\n            console.error('KTXLoader: keyAndValueByteSize out of bounds');\n            break;\n        }\n\n        // Note: keyNulByte can't be 0 otherwise the key is an empty string.\n        let keyNulByte = 0;\n\n        for (; keyNulByte < keyAndValueByteSize; keyNulByte++)\n        {\n            if (dataView.getUint8(keyAndValueByteOffset + keyNulByte) === 0x00)\n            {\n                break;\n            }\n        }\n\n        if (keyNulByte === -1)\n        {\n            console.error('KTXLoader: Failed to find null byte terminating kvData key');\n            break;\n        }\n\n        const key = new TextDecoder().decode(\n            new Uint8Array(dataView.buffer, keyAndValueByteOffset, keyNulByte)\n        );\n        const value = new DataView(\n            dataView.buffer,\n            keyAndValueByteOffset + keyNulByte + 1,\n            keyAndValueByteSize - keyNulByte - 1,\n        );\n\n        kvData.set(key, value);\n\n        // 4 = the keyAndValueByteSize field itself\n        // keyAndValueByteSize = the bytes taken by the key and value\n        // valuePadding = extra padding to align with 4 bytes\n        bytesIntoKeyValueData += 4 + keyAndValueByteSize + valuePadding;\n    }\n\n    return kvData;\n}\n", "import { LoaderResource } from '@pixi/loaders';\nimport { registerCompressedTextures } from './registerCompressedTextures';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport { parseDDS } from '../parsers';\n\n// Set DDS files to be loaded as an ArrayBuffer\nLoaderResource.setExtensionXhrType('dds', LoaderResource.XHR_RESPONSE_TYPE.BUFFER);\n\n/**\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n * @see https://docs.microsoft.com/en-us/windows/win32/direct3ddds/dx-graphics-dds-pguide\n */\nexport class DDSLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * Registers a DDS compressed texture\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource - loader resource that is checked to see if it is a DDS file\n     * @param next - callback Function to call when done\n     */\n    public static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        if (resource.extension === 'dds' && resource.data)\n        {\n            try\n            {\n                Object.assign(resource, registerCompressedTextures(\n                    resource.name || resource.url,\n                    parseDDS(resource.data),\n                    resource.metadata,\n                ));\n            }\n            catch (err)\n            {\n                next(err);\n\n                return;\n            }\n        }\n\n        next();\n    }\n}\n", "import { ALP<PERSON>_MODES, MIPMAP_MODES } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { BaseTexture, ExtensionType, Texture } from '@pixi/core';\nimport { LoaderResource } from '@pixi/loaders';\nimport { registerCompressedTextures } from './registerCompressedTextures';\nimport { parseKTX } from '../parsers';\n\n// Set KTX files to be loaded as an ArrayBuffer\nLoaderResource.setExtensionXhrType('ktx', LoaderResource.XHR_RESPONSE_TYPE.BUFFER);\n\n/**\n * Loader plugin for handling KTX texture container files.\n *\n * This KTX loader does not currently support the following features:\n * * cube textures\n * * 3D textures\n * * endianness conversion for big-endian machines\n * * embedded *.basis files\n *\n * It does supports the following features:\n * * multiple textures per file\n * * mipmapping (only for compressed formats)\n * * vendor-specific key/value data parsing (enable {@link PIXI.KTXLoader.loadKeyValueData})\n * @class\n * @memberof PIXI\n * @implements {PIXI.ILoaderPlugin}\n */\nexport class KTXLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * If set to `true`, {@link PIXI.KTXLoader} will parse key-value data in KTX textures. This feature relies\n     * on the [Encoding Standard]{@link https://encoding.spec.whatwg.org}.\n     *\n     * The key-value data will be available on the base-textures as {@code PIXI.BaseTexture.ktxKeyValueData}. They\n     * will hold a reference to the texture data buffer, so make sure to delete key-value data once you are done\n     * using it.\n     */\n    static loadKeyValueData = false;\n\n    /**\n     * Called after a KTX file is loaded.\n     *\n     * This will parse the KTX file header and add a {@code BaseTexture} to the texture\n     * cache.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource - loader resource that is checked to see if it is a KTX file\n     * @param next - callback Function to call when done\n     */\n    public static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        if (resource.extension === 'ktx' && resource.data)\n        {\n            try\n            {\n                const url = resource.name || resource.url;\n                const { compressed, uncompressed, kvData } = parseKTX(url, resource.data, this.loadKeyValueData);\n\n                if (compressed)\n                {\n                    const result = registerCompressedTextures(\n                        url,\n                        compressed,\n                        resource.metadata,\n                    );\n\n                    if (kvData && result.textures)\n                    {\n                        for (const textureId in result.textures)\n                        {\n                            result.textures[textureId].baseTexture.ktxKeyValueData = kvData;\n                        }\n                    }\n\n                    Object.assign(resource, result);\n                }\n                else if (uncompressed)\n                {\n                    const textures: Record<string, Texture> = {};\n\n                    uncompressed.forEach((image, i) =>\n                    {\n                        const texture = new Texture(new BaseTexture(\n                            image.resource,\n                            {\n                                mipmap: MIPMAP_MODES.OFF,\n                                alphaMode: ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,\n                                type: image.type,\n                                format: image.format,\n                            }\n                        ));\n                        const cacheID = `${url}-${i + 1}`;\n\n                        if (kvData) texture.baseTexture.ktxKeyValueData = kvData;\n\n                        BaseTexture.addToCache(texture.baseTexture, cacheID);\n                        Texture.addToCache(texture, cacheID);\n\n                        if (i === 0)\n                        {\n                            textures[url] = texture;\n                            BaseTexture.addToCache(texture.baseTexture, url);\n                            Texture.addToCache(texture, url);\n                        }\n\n                        textures[cacheID] = texture;\n                    });\n\n                    Object.assign(resource, { textures });\n                }\n            }\n            catch (err)\n            {\n                next(err);\n\n                return;\n            }\n        }\n\n        next();\n    }\n}\n"], "names": ["INTERNAL_FORMATS", "_a", "ViewableBuffer", "BufferResource", "LoaderResource", "url", "settings", "ExtensionType", "Texture", "BaseTexture", "MIPMAP_MODES", "ALPHA_MODES", "_b", "TYPES", "FORMATS"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;AACSA,kCA4CX;AA5CD,CAAA,UAAY,gBAAgB,EAAA;;AAIxB,IAAA,gBAAA,CAAA,gBAAA,CAAA,8BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,8BAAqC,CAAA;AACrC,IAAA,gBAAA,CAAA,gBAAA,CAAA,+BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,+BAAsC,CAAA;AACtC,IAAA,gBAAA,CAAA,gBAAA,CAAA,+BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,+BAAsC,CAAA;AACtC,IAAA,gBAAA,CAAA,gBAAA,CAAA,+BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,+BAAsC,CAAA;;AAGtC,IAAA,gBAAA,CAAA,gBAAA,CAAA,qCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,qCAA2C,CAAA;AAC3C,IAAA,gBAAA,CAAA,gBAAA,CAAA,qCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,qCAA2C,CAAA;AAC3C,IAAA,gBAAA,CAAA,gBAAA,CAAA,qCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,qCAA2C,CAAA;AAC3C,IAAA,gBAAA,CAAA,gBAAA,CAAA,+BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,+BAAqC,CAAA;;AAGrC,IAAA,gBAAA,CAAA,gBAAA,CAAA,oBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,oBAA2B,CAAA;AAC3B,IAAA,gBAAA,CAAA,gBAAA,CAAA,2BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,2BAAkC,CAAA;AAClC,IAAA,gBAAA,CAAA,gBAAA,CAAA,qBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,qBAA4B,CAAA;AAC5B,IAAA,gBAAA,CAAA,gBAAA,CAAA,4BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,4BAAmC,CAAA;AACnC,IAAA,gBAAA,CAAA,gBAAA,CAAA,sBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAA6B,CAAA;AAC7B,IAAA,gBAAA,CAAA,gBAAA,CAAA,2BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,2BAAkC,CAAA;AAClC,IAAA,gBAAA,CAAA,gBAAA,CAAA,uBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,uBAA8B,CAAA;AAC9B,IAAA,gBAAA,CAAA,gBAAA,CAAA,kCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,kCAAyC,CAAA;AACzC,IAAA,gBAAA,CAAA,gBAAA,CAAA,0CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,0CAAiD,CAAA;AACjD,IAAA,gBAAA,CAAA,gBAAA,CAAA,2CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,2CAAkD,CAAA;;AAGlD,IAAA,gBAAA,CAAA,gBAAA,CAAA,iCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,iCAAwC,CAAA;AACxC,IAAA,gBAAA,CAAA,gBAAA,CAAA,kCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,kCAAyC,CAAA;AACzC,IAAA,gBAAA,CAAA,gBAAA,CAAA,iCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,iCAAwC,CAAA;AACxC,IAAA,gBAAA,CAAA,gBAAA,CAAA,kCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,kCAAyC,CAAA;;AAGzC,IAAA,gBAAA,CAAA,gBAAA,CAAA,2BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,2BAAkC,CAAA;;AAGlC,IAAA,gBAAA,CAAA,gBAAA,CAAA,0BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,0BAAiC,CAAA;AACjC,IAAA,gBAAA,CAAA,gBAAA,CAAA,0CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,0CAAiD,CAAA;AACjD,IAAA,gBAAA,CAAA,gBAAA,CAAA,8CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,8CAAqD,CAAA;;;AAIrD,IAAA,gBAAA,CAAA,gBAAA,CAAA,8BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,8BAAqC,CAAA;AACzC,CAAC,EA5CWA,wBAAgB,KAAhBA,wBAAgB,GA4C3B,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;AAMG;IACU,kCAAkC,IAAAC,IAAA,GAAA,EAAA;;AAE3C,IAAAA,IAAA,CAACD,wBAAgB,CAAC,4BAA4B,CAAA,GAAG,GAAG;AACpD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,6BAA6B,CAAA,GAAG,GAAG;AACrD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,6BAA6B,CAAA,GAAG,CAAC;AACnD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,6BAA6B,CAAA,GAAG,CAAC;;AAGnD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,6BAA6B,CAAA,GAAG,GAAG;AACrD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,mCAAmC,CAAA,GAAG,GAAG;AAC3D,IAAAC,IAAA,CAACD,wBAAgB,CAAC,mCAAmC,CAAA,GAAG,CAAC;AACzD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,mCAAmC,CAAA,GAAG,CAAC;;AAGzD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,kBAAkB,CAAA,GAAG,GAAG;AAC1C,IAAAC,IAAA,CAACD,wBAAgB,CAAC,yBAAyB,CAAA,GAAG,GAAG;AACjD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,mBAAmB,CAAA,GAAG,CAAC;AACzC,IAAAC,IAAA,CAACD,wBAAgB,CAAC,0BAA0B,CAAA,GAAG,CAAC;AAChD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,oBAAoB,CAAA,GAAG,GAAG;AAC5C,IAAAC,IAAA,CAACD,wBAAgB,CAAC,yBAAyB,CAAA,GAAG,CAAC;AAC/C,IAAAC,IAAA,CAACD,wBAAgB,CAAC,qBAAqB,CAAA,GAAG,GAAG;AAC7C,IAAAC,IAAA,CAACD,wBAAgB,CAAC,gCAAgC,CAAA,GAAG,CAAC;AACtD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,wCAAwC,CAAA,GAAG,GAAG;AAChE,IAAAC,IAAA,CAACD,wBAAgB,CAAC,yCAAyC,CAAA,GAAG,GAAG;;AAGjE,IAAAC,IAAA,CAACD,wBAAgB,CAAC,+BAA+B,CAAA,GAAG,GAAG;AACvD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,gCAAgC,CAAA,GAAG,GAAG;AACxD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,+BAA+B,CAAA,GAAG,IAAI;AACxD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,gCAAgC,CAAA,GAAG,IAAI;;AAGzD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,yBAAyB,CAAA,GAAG,GAAG;;;AAIjD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,wBAAwB,CAAA,GAAG,GAAG;AAChD,IAAAC,IAAA,CAACD,wBAAgB,CAAC,wCAAwC,CAAA,GAAG,CAAC;AAC9D,IAAAC,IAAA,CAACD,wBAAgB,CAAC,4CAA4C,CAAA,GAAG,CAAC;;;;AAKlE,IAAAC,IAAA,CAACD,wBAAgB,CAAC,4BAA4B,CAAA,GAAG,CAAC;;;AClItD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF,CAAC;AAuCD;AACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;AAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;AAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;AACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9E,KAAK,CAAC,CAAC;AACP,CAAC;AACD;AACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;AAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;AACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;AACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;AACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;AACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;AACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;AACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;AACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;AAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;AACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;AACjE,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;AAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;AAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;AACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;AACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;AAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;AAC3C,aAAa;AACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;AAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACzF,KAAK;AACL;;AC7FA;;;;;AAKG;AACH,IAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;IAA2C,SAAc,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;AAMrD;;;;;;;AAOG;IACH,SAAY,YAAA,CAAA,MAAwD,EAChE,OAA+D,EAAA;AAA/D,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAA0B,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAA,EAAA;QADnE,IA0CC,KAAA,GAAA,IAAA,CAAA;AAvCG,QAAA,IAAI,MAAc,CAAC;AACnB,QAAA,IAAI,IAA6C,CAAC;AAElD,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAC9B;YACI,MAAM,GAAG,MAAM,CAAC;AAChB,YAAA,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;AAC3B,SAAA;AAED,aAAA;YACI,MAAM,GAAG,IAAI,CAAC;YACd,IAAI,GAAG,MAAM,CAAC;AACjB,SAAA;AAED,QAAA,KAAA,GAAA,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,IAAI,EAAE,OAAO,CAAC,IAAC,IAAA,CAAA;AAErB;;;AAGG;AACH,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAErB;;;AAGG;;AAEH,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAIE,mBAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;;QAGrD,IAAI,KAAI,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAC7C;YACI,KAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;AACD,QAAA,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EACvB;AACI,YAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,KAAI,CAAC,YAAY,CAAC,KAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAChD,SAAA;;KACJ;IAES,YAAY,CAAA,SAAA,CAAA,YAAA,GAAtB,UAAuB,KAAkB,EAAA;;KAGxC,CAAA;;AAGK,IAAA,YAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;uCAAc,OAAO,EAAA,YAAA;;;;AAEA,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,CAAA;;AAAnC,wBAAA,QAAQ,GAAG,EAAwB,CAAA,IAAA,EAAA,CAAA;AAC5B,wBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;AAA5B,wBAAA,IAAI,GAAG,EAAqB,CAAA,IAAA,EAAA,CAAA;AACd,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,WAAW,EAAE,CAAA,CAAA;;AAAtC,wBAAA,WAAW,GAAG,EAAwB,CAAA,IAAA,EAAA,CAAA;wBAE5C,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;wBACzC,IAAI,CAAC,MAAM,GAAG,IAAIA,mBAAc,CAAC,WAAW,CAAC,CAAC;AAC9C,wBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAEnB,wBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;wBAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;AAEd,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;AACf,KAAA,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CA/EA,CAA2CC,mBAAc,CA+ExD;;AClED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CG;AACH,IAAA,yBAAA,kBAAA,UAAA,MAAA,EAAA;IAA+C,SAAY,CAAA,yBAAA,EAAA,MAAA,CAAA,CAAA;AAevD;;;;;;;;;AASG;IACH,SAAY,yBAAA,CAAA,MAAyC,EAAE,OAA0C,EAAA;AAAjG,QAAA,IAAA,KAAA,GAEI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,MAAM,EAAE,OAAO,CAAC,IAsBzB,IAAA,CAAA;AApBG,QAAA,KAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,KAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;AAElC,QAAA,KAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;AAC5B,QAAA,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,KAAI,CAAC,UAAU,GAAG,yBAAyB,CAAC,kBAAkB,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC;AAE5E,QAAA,IAAI,OAAO,CAAC,YAAY,IAAI,KAAI,CAAC,MAAM,EACvC;;AAEI,YAAA,KAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY;AAClC,mBAAA,yBAAyB,CAAC,mBAAmB,CAC5C,MAAM,YAAY,UAAU,GAAG,MAAM,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,EAC7D,KAAI,CAAC,MAAM,EACX,KAAI,CAAC,MAAM,EACX,CAAC,EAAE,CAAC;AACJ,gBAAA,KAAI,CAAC,KAAK,EACV,KAAI,CAAC,MAAM,CAAC,CAAC;AACxB,SAAA;;KACJ;AAED;;;;;AAKG;AACH,IAAA,yBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAO,QAAkB,EAAE,QAAqB,EAAE,UAAqB,EAAA;AAEnE,QAAA,IAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AACvB,QAAA,IAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE/D,IAAI,CAAC,SAAS,EACd;YACI,MAAM,IAAI,KAAK,CAAI,IAAI,CAAC,UAAU,GAAA,oDAAoD,CAAC,CAAC;AAC3F,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EACvB;;AAEI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC3C;AACU,YAAA,IAAA,KAAoD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAvE,OAAO,aAAA,EAAE,UAAU,gBAAA,EAAE,WAAW,iBAAA,EAAE,WAAW,iBAA0B,CAAC;YAEhF,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;AACzG,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;;AAGS,IAAA,yBAAA,CAAA,SAAA,CAAA,YAAY,GAAtB,YAAA;QAEI,IAAI,CAAC,aAAa,GAAG,yBAAyB,CAAC,mBAAmB,CAC9D,IAAI,CAAC,MAAM,CAAC,SAAS,EACrB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,CAAC,EAAE,CAAC;AACJ,QAAA,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,CAAC,CAAC;KACpB,CAAA;AAED;;;;AAIG;IACY,yBAAkB,CAAA,kBAAA,GAAjC,UAAkC,MAAwB,EAAA;AAItD,QAAA,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,EACxC;AACI,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AACI,aAAA,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,EAC7C;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AACI,aAAA,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,EAC7C;AACI,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;aACI,IAAI,MAAM,IAAI,MAAM,EACzB;AACI,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AACI,aAAA,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,EAC7C;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;KACjE,CAAA;AAED;;;;;;;;;;AAUG;AACY,IAAA,yBAAA,CAAA,mBAAmB,GAAlC,UACI,MAAkB,EAClB,MAAwB,EACxB,MAAc,EACd,UAAkB,EAClB,WAAmB,EACnB,UAAkB,EAClB,WAAmB,EAAA;;AAInB,QAAA,IAAM,OAAO,GAAG,IAAI,KAAK,CAAwB,MAAM,CAAC,CAAC;AAEzD,QAAA,IAAI,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;QAE/B,IAAI,UAAU,GAAG,UAAU,CAAC;QAC5B,IAAI,WAAW,GAAG,WAAW,CAAC;AAC9B,QAAA,IAAI,iBAAiB,GAAG,CAAC,UAAU,GAAG,UAAU,GAAG,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;AAC1E,QAAA,IAAI,kBAAkB,GAAG,CAAC,WAAW,GAAG,WAAW,GAAG,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;QAE9E,IAAI,SAAS,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,kCAAkC,CAAC,MAAM,CAAC,CAAC;QAEpG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAC/B;YACI,OAAO,CAAC,CAAC,CAAC,GAAG;AACT,gBAAA,OAAO,EAAE,CAAC;gBACV,UAAU,EAAE,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,iBAAiB;gBACvD,WAAW,EAAE,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,kBAAkB;gBAC1D,WAAW,EAAE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;aAChE,CAAC;YAEF,MAAM,IAAI,SAAS,CAAC;;YAGpB,UAAU,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,WAAW,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,CAAC;AACtC,YAAA,iBAAiB,GAAG,CAAC,UAAU,GAAG,UAAU,GAAG,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;AACtE,YAAA,kBAAkB,GAAG,CAAC,WAAW,GAAG,WAAW,GAAG,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;YAC1E,SAAS,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,kCAAkC,CAAC,MAAM,CAAC,CAAC;AACnG,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB,CAAA;IACL,OAAC,yBAAA,CAAA;AAAD,CArLA,CAA+C,YAAY,CAqL1D;;AClOD;AAEA;;;;;AAKG;AACH,IAAA,uBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,uBAAA,GAAA;KA0LC;AA/KG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACI,IAAA,uBAAA,CAAA,GAAG,GAAV,UAAW,QAAwB,EAAE,IAA8B,EAAA;AAE/D,QAAA,IAAM,IAAI,GAA8B,QAAQ,CAAC,IAAI,CAAC;QACtD,IAAM,MAAM,GAAG,IAAyB,CAAC;QAEzC,IAAI,QAAQ,CAAC,IAAI,KAAKC,sBAAc,CAAC,IAAI,CAAC,IAAI;eACvC,IAAI;AACJ,eAAA,IAAI,CAAC,OAAO;eACZ,IAAI,CAAC,QAAQ,EACpB;AACI,YAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE/B,IAAI,UAAU,SAAQ,CAAC;YACvB,IAAI,WAAW,SAAQ,CAAC;;AAGxB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC/C;AACI,gBAAA,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5B,gBAAA,IAAM,KAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACxB,gBAAA,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAE9B,IAAI,CAAC,MAAM,EACX;oBACI,WAAW,GAAG,KAAG,CAAC;AACrB,iBAAA;AACD,gBAAA,IAAI,uBAAuB,CAAC,cAAc,CAAC,MAAM,CAAC,EAClD;oBACI,UAAU,GAAG,KAAG,CAAC;oBACjB,MAAM;AACT,iBAAA;AACJ,aAAA;AAED,YAAA,UAAU,GAAG,UAAU,IAAI,WAAW,CAAC;;YAGvC,IAAI,CAAC,UAAU,EACf;gBACI,IAAI,CAAC,IAAI,KAAK,CAAC,qCAAA,GAAsC,QAAQ,CAAC,GAAG,GAAA,oCAAoC,CAAC,CAAC,CAAC;gBAExG,OAAO;AACV,aAAA;AACD,YAAA,IAAI,UAAU,KAAK,QAAQ,CAAC,GAAG,EAC/B;;AAEI,gBAAA,IAAI,CAAC,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC,CAAC;gBAEvF,OAAO;AACV,aAAA;AAED,YAAA,IAAM,WAAW,GAAG;gBAChB,WAAW,EAAE,QAAQ,CAAC,WAAW;AACjC,gBAAA,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,aAAa;AACzC,gBAAA,cAAc,EAAE,QAAQ;aAC3B,CAAC;YAEF,IAAM,YAAY,GAAGC,SAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AACvF,YAAA,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;;YAGlC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,UAAC,GAAmB,EAAA;gBAEpE,IAAI,GAAG,CAAC,KAAK,EACb;AACI,oBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAEhB,OAAO;AACV,iBAAA;AAEO,gBAAA,IAAA,KAAkC,GAAG,CAAA,OAAvB,EAAd,OAAO,mBAAG,IAAI,GAAA,EAAA,EAAE,EAAA,GAAkB,GAAG,CAAR,QAAA,EAAb,QAAQ,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,EAAE,KAAA,CAAS;;AAG9C,gBAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAA,OAAA,EAAE,QAAQ,EAAA,QAAA,EAAE,CAAC,CAAC;;AAG/C,gBAAA,IAAI,EAAE,CAAC;AACX,aAAC,CAAC,CAAC;AACN,SAAA;AAED,aAAA;AACI,YAAA,IAAI,EAAE,CAAC;AACV,SAAA;KACJ,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAkB,uBAAiB,EAAA,mBAAA,EAAA;;AAAnC,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,EAC/C;;gBAEI,IAAM,MAAM,GAAGC,iBAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC/C,IAAM,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAEtC,IAAI,CAAC,EAAE,EACP;AAEI,oBAAA,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;AAG/E,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA;AAED,gBAAA,IAAM,UAAU,GAAG;AACf,oBAAA,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,+BAA+B,CAAC;AACtD,oBAAA,SAAS,EAAE,EAAE,CAAC,YAAY,CAAC,oCAAoC,CAAC;AAChE,oBAAA,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,8BAA8B,CAAC;AACpD,oBAAA,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,+BAA+B,CAAC;AACtD,oBAAA,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,gCAAgC,CAAC;AACjD,2BAAA,EAAE,CAAC,YAAY,CAAC,uCAAuC,CAAC;AAC/D,oBAAA,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,8BAA8B,CAAC;AACpD,oBAAA,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,+BAA+B,CAAC;iBACzD,CAAC;AAEF,gBAAA,uBAAuB,CAAC,kBAAkB,GAAG,UAAU,CAAC;AAC3D,aAAA;YAED,OAAO,uBAAuB,CAAC,kBAAkB,CAAC;SACrD;;;AAAA,KAAA,CAAA,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAkB,uBAAc,EAAA,gBAAA,EAAA;;AAAhC,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAC5C;AACI,gBAAA,IAAM,UAAU,GAAG,uBAAuB,CAAC,iBAAiB,CAAC;AAE7D,gBAAA,uBAAuB,CAAC,eAAe,GAAG,EAAE,CAAC;;AAG7C,gBAAA,KAAK,IAAM,aAAa,IAAI,UAAU,EACtC;AACI,oBAAA,IAAM,SAAS,GAAG,UAAU,CAAC,aAA8C,CAAC,CAAC;oBAE7E,IAAI,CAAC,SAAS,EACd;wBACI,SAAS;AACZ,qBAAA;AAED,oBAAA,MAAM,CAAC,MAAM,CACT,uBAAuB,CAAC,eAAe,EACvC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;AACzC,iBAAA;AACJ,aAAA;YAED,OAAO,uBAAuB,CAAC,eAAe,CAAC;SAClD;;;AAAA,KAAA,CAAA,CAAA;;AAtLM,IAAA,uBAAA,CAAA,SAAS,GAAsBC,kBAAa,CAAC,MAAM,CAAC;IAuL/D,OAAC,uBAAA,CAAA;AAAA,CA1LD,EA0LC;;ACtND;;;;;;;AAOG;SACa,0BAA0B,CACtC,GAAW,EACX,SAAsC,EACtC,QAA2B,EAAA;AAG3B,IAAA,IAAM,MAAM,GAA6B;AACrC,QAAA,QAAQ,EAAE,EAAE;AACZ,QAAA,OAAO,EAAE,IAAI;KAChB,CAAC;IAEF,IAAI,CAAC,SAAS,EACd;AACI,QAAA,OAAO,MAAM,CAAC;AACjB,KAAA;AAED,IAAA,IAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,UAAC,QAAQ,EAAA;AACpC,QAAA,QACI,IAAIC,YAAO,CAAC,IAAIC,gBAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC;YAChD,MAAM,EAAEC,sBAAY,CAAC,GAAG;YACxB,SAAS,EAAEC,qBAAW,CAAC,sBAAsB;AAChD,SAAA,EAAE,QAAQ,CAAC,CAAC,CAAC,EACjB;AALD,KAKC,CAAC,CAAC;AAEP,IAAA,QAAQ,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,CAAC,EAAA;AAEhB,QAAA,IAAA,WAAW,GAAK,OAAO,CAAA,WAAZ,CAAa;QAChC,IAAM,OAAO,GAAM,GAAG,GAAA,GAAA,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC;AAElC,QAAAF,gBAAW,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC7C,QAAAD,YAAO,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAErC,IAAI,CAAC,KAAK,CAAC,EACX;AACI,YAAAC,gBAAW,CAAC,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACzC,YAAAD,YAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACjC,YAAA,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAC5B,SAAA;AAED,QAAA,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AACvC,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,MAAM,CAAC;AAClB;;;AC5DA,IAAM,cAAc,GAAG,CAAC,CAAC;AACzB,IAAM,eAAe,GAAG,GAAG,CAAC;AAC5B,IAAM,kBAAkB,GAAG,EAAE,CAAC;AAC9B,IAAM,oBAAoB,GAAG,EAAE,CAAC;AAEhC;AACA,IAAM,SAAS,GAAG,UAAU,CAAC;AAE7B;;;AAGG;AACH,IAAM,UAAU,GAAG;AACf,IAAA,IAAI,EAAE,CAAC;AACP,IAAA,KAAK,EAAE,CAAC;AACR,IAAA,MAAM,EAAE,CAAC;AACT,IAAA,KAAK,EAAE,CAAC;AACR,IAAA,YAAY,EAAE,CAAC;AACf,IAAA,YAAY,EAAE,EAAE;CACnB,CAAC;AAEF;;;AAGG;AACH,IAAM,aAAa,GAAG;AAClB,IAAA,IAAI,EAAE,CAAC;AACP,IAAA,KAAK,EAAE,CAAC;AACR,IAAA,MAAM,EAAE,CAAC;AACT,IAAA,YAAY,EAAE,CAAC;AACf,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,UAAU,EAAE,CAAC;CAChB,CAAC;AAEF;;;AAGG;AACH,IAAM,eAAe,GAAG;AACpB,IAAA,WAAW,EAAE,CAAC;AACd,IAAA,kBAAkB,EAAE,CAAC;AACrB,IAAA,SAAS,EAAE,CAAC;AACZ,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,WAAW,EAAE,CAAC;CACjB,CAAC;AAEF;;;AAGG;AACH;AACA;AACA,IAAK,WA4HJ,CAAA;AA5HD,CAAA,UAAK,WAAW,EAAA;AAEZ,IAAA,WAAA,CAAA,WAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,qBAAmB,CAAA;AACnB,IAAA,WAAA,CAAA,WAAA,CAAA,mCAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mCAAiC,CAAA;AACjC,IAAA,WAAA,CAAA,WAAA,CAAA,gCAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gCAA8B,CAAA;AAC9B,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,gCAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gCAA8B,CAAA;AAC9B,IAAA,WAAA,CAAA,WAAA,CAAA,6BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,6BAA2B,CAAA;AAC3B,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,mCAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mCAAiC,CAAA;AACjC,IAAA,WAAA,CAAA,WAAA,CAAA,gCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gCAA8B,CAAA;AAC9B,IAAA,WAAA,CAAA,WAAA,CAAA,gCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gCAA8B,CAAA;AAC9B,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,gCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gCAA8B,CAAA;AAC9B,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,6BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,6BAA2B,CAAA;AAC3B,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,yBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,yBAAuB,CAAA;AACvB,IAAA,WAAA,CAAA,WAAA,CAAA,yBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,yBAAuB,CAAA;AACvB,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,kCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,kCAAgC,CAAA;AAChC,IAAA,WAAA,CAAA,WAAA,CAAA,sCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sCAAoC,CAAA;AACpC,IAAA,WAAA,CAAA,WAAA,CAAA,qCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,qCAAmC,CAAA;AACnC,IAAA,WAAA,CAAA,WAAA,CAAA,kCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,kCAAgC,CAAA;AAChC,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,8BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,8BAA4B,CAAA;AAC5B,IAAA,WAAA,CAAA,WAAA,CAAA,6BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,6BAA2B,CAAA;AAC3B,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,iCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,iCAA+B,CAAA;AAC/B,IAAA,WAAA,CAAA,WAAA,CAAA,2BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,2BAAyB,CAAA;AACzB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,2BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,2BAAyB,CAAA;AACzB,IAAA,WAAA,CAAA,WAAA,CAAA,6BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,6BAA2B,CAAA;AAC3B,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,yBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,yBAAuB,CAAA;AACvB,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,yBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,yBAAuB,CAAA;AACvB,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,mCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,mCAAiC,CAAA;AACjC,IAAA,WAAA,CAAA,WAAA,CAAA,kCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,kCAAgC,CAAA;AAChC,IAAA,WAAA,CAAA,WAAA,CAAA,2BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,2BAAyB,CAAA;AACzB,IAAA,WAAA,CAAA,WAAA,CAAA,wBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,wBAAsB,CAAA;AACtB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,wBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,wBAAsB,CAAA;AACtB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,yBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,yBAAuB,CAAA;AACvB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,qBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,qBAAmB,CAAA;AACnB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,qBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,qBAAmB,CAAA;AACnB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,sBAAoB,CAAA;AACpB,IAAA,WAAA,CAAA,WAAA,CAAA,gCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gCAA8B,CAAA;AAC9B,IAAA,WAAA,CAAA,WAAA,CAAA,6BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,6BAA2B,CAAA;AAC3B,IAAA,WAAA,CAAA,WAAA,CAAA,6BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,6BAA2B,CAAA;AAC3B,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,wCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,wCAAsC,CAAA;AACtC,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,iCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,iCAA+B,CAAA;AAC/B,IAAA,WAAA,CAAA,WAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,WAAA,CAAA,iCAAA,CAAA,GAAA,EAAA,CAAA,GAAA,iCAA+B,CAAA;AAC/B,IAAA,WAAA,CAAA,WAAA,CAAA,2BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,2BAAyB,CAAA;AACzB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB,CAAA;AACxB,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,wBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,wBAAsB,CAAA;AACtB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,gBAAc,CAAA;AACd,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,GAAA,CAAA,GAAA,4BAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,6CAAA,CAAA,GAAA,GAAA,CAAA,GAAA,6CAA2C,CAAA;AAC3C,IAAA,WAAA,CAAA,WAAA,CAAA,qDAAA,CAAA,GAAA,GAAA,CAAA,GAAA,qDAAmD,CAAA;AACnD,IAAA,WAAA,CAAA,WAAA,CAAA,wBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,wBAAsB,CAAA;AAC1B,CAAC,EA5HI,WAAW,KAAX,WAAW,GA4Hf,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;AACH,IAAK,wBAKJ,CAAA;AALD,CAAA,UAAK,wBAAwB,EAAA;AAEzB,IAAA,wBAAA,CAAA,wBAAA,CAAA,yBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,yBAA2B,CAAA;AAC3B,IAAA,wBAAA,CAAA,wBAAA,CAAA,yBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,yBAA2B,CAAA;AAC3B,IAAA,wBAAA,CAAA,wBAAA,CAAA,yBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,yBAA2B,CAAA;AAC/B,CAAC,EALI,wBAAwB,KAAxB,wBAAwB,GAK5B,EAAA,CAAA,CAAA,CAAA;AAED,IAAM,QAAQ,GAAG,CAAC,CAAC;AAEnB;AACA,IAAM,UAAU,GAAG,GAAG,CAAC;AACvB,IAAM,WAAW,GAAG,GAAG,CAAC;AACxB,IAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,IAAM,QAAQ,GAAG,KAAK,CAAC;AACvB,IAAM,cAAc,GAAG,OAAO,CAAC;AAE/B;AACA,IAAM,WAAW,GAAG,UAAU,CAAC;AAC/B,IAAM,WAAW,GAAG,UAAU,CAAC;AAC/B,IAAM,WAAW,GAAG,UAAU,CAAC;AAC/B,IAAM,WAAW,GAAG,UAAU,CAAC;AAE/B;AACA,IAAM,6BAA6B,GAAG,GAAG,CAAC;AAE1C;;;AAGG;AACH,IAAM,gBAAgB,IAAAP,IAAA,GAAA,EAAA;AAClB,IAAAA,IAAA,CAAC,WAAW,CAAA,GAAGD,wBAAgB,CAAC,6BAA6B;AAC7D,IAAAC,IAAA,CAAC,WAAW,CAAA,GAAGD,wBAAgB,CAAC,6BAA6B;AAC7D,IAAAC,IAAA,CAAC,WAAW,CAAA,GAAGD,wBAAgB,CAAC,6BAA6B;SAChE,CAAC;AAEF;;;AAGG;AACH,IAAM,cAAc,IAAAY,IAAA,GAAA,EAAA;;AAEhB,IAAAA,IAAA,CAAC,WAAW,CAAC,wBAAwB,CAAG,GAAAZ,wBAAgB,CAAC,6BAA6B;AACtF,IAAAY,IAAA,CAAC,WAAW,CAAC,qBAAqB,CAAG,GAAAZ,wBAAgB,CAAC,6BAA6B;AACnF,IAAAY,IAAA,CAAC,WAAW,CAAC,wBAAwB,CAAG,GAAAZ,wBAAgB,CAAC,6BAA6B;AACtF,IAAAY,IAAA,CAAC,WAAW,CAAC,qBAAqB,CAAG,GAAAZ,wBAAgB,CAAC,6BAA6B;AACnF,IAAAY,IAAA,CAAC,WAAW,CAAC,wBAAwB,CAAG,GAAAZ,wBAAgB,CAAC,6BAA6B;AACtF,IAAAY,IAAA,CAAC,WAAW,CAAC,qBAAqB,CAAG,GAAAZ,wBAAgB,CAAC,6BAA6B;;AAGnF,IAAAY,IAAA,CAAC,WAAW,CAAC,0BAA0B,CAAG,GAAAZ,wBAAgB,CAAC,mCAAmC;AAC9F,IAAAY,IAAA,CAAC,WAAW,CAAC,0BAA0B,CAAG,GAAAZ,wBAAgB,CAAC,mCAAmC;AAC9F,IAAAY,IAAA,CAAC,WAAW,CAAC,0BAA0B,CAAG,GAAAZ,wBAAgB,CAAC,mCAAmC;SACjG,CAAC;AAEF;;;;;AAKG;AACH;;;AAGG;AACG,SAAU,QAAQ,CAAC,WAAwB,EAAA;AAE7C,IAAA,IAAM,IAAI,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;AAC1C,IAAA,IAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAE1B,IAAI,SAAS,KAAK,SAAS,EAC3B;AACI,QAAA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;AAClD,KAAA;AAED,IAAA,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC,EAAE,eAAe,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;;IAGhG,IAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IACzC,IAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACvC,IAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;;IAGpD,IAAM,WAAW,GAAG,IAAI,WAAW,CAC/B,WAAW,EACX,UAAU,CAAC,YAAY,GAAG,WAAW,CAAC,iBAAiB,EACvD,kBAAkB,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACxD,IAAA,IAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;;IAG1C,IAAI,WAAW,GAAG,WAAW,EAC7B;QACI,IAAM,MAAM,GAAG,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;;QAGjD,IAAI,MAAM,KAAK,WAAW,EAC1B;AACI,YAAA,IAAM,gBAAc,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAEhD,YAAA,IAAM,YAAU,GAAG,cAAc,GAAG,eAAe,CAAC;YACpD,IAAM,OAAO,GAAG,IAAI,UAAU,CAAC,WAAW,EAAE,YAAU,CAAC,CAAC;AAExD,YAAA,IAAM,QAAQ,GAAG,IAAI,yBAAyB,CAAC,OAAO,EAAE;AACpD,gBAAA,MAAM,EAAE,gBAAc;AACtB,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,MAAM,EAAA,MAAA;gBACN,MAAM,EAAE,WAAW;AACtB,aAAA,CAAC,CAAC;YAEH,OAAO,CAAC,QAAQ,CAAC,CAAC;AACrB,SAAA;;AAGD,QAAA,IAAM,UAAU,GAAG,cAAc,GAAG,eAAe,CAAC;AACpD,QAAA,IAAM,UAAU,GAAG,IAAI,WAAW,CAC9B,IAAI,CAAC,MAAM,EACX,UAAU,EACV,oBAAoB,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;QAC1D,IAAM,UAAU,GAAG,UAAU,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC3D,IAAM,iBAAiB,GAAG,UAAU,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QACzE,IAAM,QAAQ,GAAG,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACvD,IAAM,SAAS,GAAG,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;;AAGzD,QAAA,IAAM,gBAAc,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,gBAAc,KAAK,SAAS,EAChC;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,uDAAwD,GAAA,UAAY,CAAC,CAAC;AACzF,SAAA;QACD,IAAI,QAAQ,KAAK,6BAA6B,EAC9C;;AAEI,YAAA,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAClE,SAAA;AACD,QAAA,IAAI,iBAAiB,KAAK,wBAAwB,CAAC,uBAAuB,EAC1E;;AAEI,YAAA,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACnE,SAAA;;AAGD,QAAA,IAAM,YAAY,GAAG,IAAI,KAAK,EAAc,CAAC;QAC7C,IAAM,UAAU,GAAG,cAAc;cACvB,eAAe;AACf,cAAA,oBAAoB,CAAC;QAE/B,IAAI,SAAS,KAAK,CAAC,EACnB;;YAEI,YAAY,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;AAC9D,SAAA;AAED,aAAA;;AAGI,YAAA,IAAM,SAAS,GAAG,kCAAkC,CAAC,gBAAc,CAAC,CAAC;YACrE,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,IAAI,WAAW,GAAG,MAAM,CAAC;YAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EACpC;AACI,gBAAA,IAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7D,gBAAA,IAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE/D,gBAAA,IAAM,SAAS,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,SAAS,CAAC;gBAErE,SAAS,IAAI,SAAS,CAAC;AAEvB,gBAAA,UAAU,GAAG,UAAU,KAAK,CAAC,CAAC;AAC9B,gBAAA,WAAW,GAAG,WAAW,KAAK,CAAC,CAAC;AACnC,aAAA;YAED,IAAI,WAAW,GAAG,UAAU,CAAC;;YAG7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAClC;AACI,gBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;gBACvE,WAAW,IAAI,SAAS,CAAC;AAC5B,aAAA;AACJ,SAAA;;AAGD,QAAA,OAAO,YAAY,CAAC,GAAG,CAAC,UAAC,MAAM,EAAK,EAAA,OAAA,IAAI,yBAAyB,CAAC,MAAM,EAAE;AACtE,YAAA,MAAM,EAAE,gBAAc;AACtB,YAAA,KAAK,EAAA,KAAA;AACL,YAAA,MAAM,EAAA,MAAA;AACN,YAAA,MAAM,EAAE,WAAW;SACtB,CAAC,CAAA,EAAA,CAAC,CAAC;AACP,KAAA;IACD,IAAI,WAAW,GAAG,QAAQ,EAC1B;;AAEI,QAAA,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC5E,KAAA;IACD,IAAI,WAAW,GAAG,QAAQ,EAC1B;;AAEI,QAAA,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;AAClF,KAAA;IACD,IAAI,WAAW,GAAG,cAAc,EAChC;;AAEI,QAAA,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;AAC3F,KAAA;IACD,IAAI,WAAW,GAAG,UAAU,EAC5B;;AAEI,QAAA,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;AACtF,KAAA;AAED,IAAA,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;AACzF;;;ACzYA;;;;AAIG;AACH,IAAM,eAAe,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAEjG;;;;AAIG;AACH,IAAM,UAAU,GAAG,UAAU,CAAC;AAE9B;;;AAGG;AACH,IAAM,UAAU,GAAG;AACf,IAAA,eAAe,EAAE,CAAC;AAClB,IAAA,UAAU,EAAE,EAAE;AACd,IAAA,OAAO,EAAE,EAAE;AACX,IAAA,YAAY,EAAE,EAAE;AAChB,IAAA,SAAS,EAAE,EAAE;AACb,IAAA,kBAAkB,EAAE,EAAE;AACtB,IAAA,uBAAuB,EAAE,EAAE;AAC3B,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,YAAY,EAAE,EAAE;AAChB,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,wBAAwB,EAAE,EAAE;AAC5B,IAAA,eAAe,EAAE,EAAE;AACnB,IAAA,uBAAuB,EAAE,EAAE;AAC3B,IAAA,uBAAuB,EAAE,EAAE;CAC9B,CAAC;AAEF;;;AAGG;AACH,IAAM,gBAAgB,GAAG,EAAE,CAAC;AAE5B;;;AAGG;IACU,4BAA4B,IAAA,EAAA,GAAA,EAAA;AACrC,IAAA,EAAA,CAACa,eAAK,CAAC,aAAa,CAAA,GAAG,CAAC;AACxB,IAAA,EAAA,CAACA,eAAK,CAAC,cAAc,CAAA,GAAG,CAAC;AACzB,IAAA,EAAA,CAACA,eAAK,CAAC,GAAG,CAAA,GAAG,CAAC;AACd,IAAA,EAAA,CAACA,eAAK,CAAC,YAAY,CAAA,GAAG,CAAC;AACvB,IAAA,EAAA,CAACA,eAAK,CAAC,KAAK,CAAA,GAAG,CAAC;AAChB,IAAA,EAAA,CAACA,eAAK,CAAC,UAAU,CAAA,GAAG,CAAC;QACvB;AAEF;;;AAGG;IACU,qBAAqB,IAAA,EAAA,GAAA,EAAA;AAC9B,IAAA,EAAA,CAACC,iBAAO,CAAC,IAAI,CAAA,GAAG,CAAC;AACjB,IAAA,EAAA,CAACA,iBAAO,CAAC,GAAG,CAAA,GAAG,CAAC;AAChB,IAAA,EAAA,CAACA,iBAAO,CAAC,EAAE,CAAA,GAAG,CAAC;AACf,IAAA,EAAA,CAACA,iBAAO,CAAC,GAAG,CAAA,GAAG,CAAC;AAChB,IAAA,EAAA,CAACA,iBAAO,CAAC,SAAS,CAAA,GAAG,CAAC;AACtB,IAAA,EAAA,CAACA,iBAAO,CAAC,eAAe,CAAA,GAAG,CAAC;AAC5B,IAAA,EAAA,CAACA,iBAAO,CAAC,KAAK,CAAA,GAAG,CAAC;QACpB;AAEF;;;AAGG;IACU,wBAAwB,IAAA,EAAA,GAAA,EAAA;AACjC,IAAA,EAAA,CAACD,eAAK,CAAC,sBAAsB,CAAA,GAAG,CAAC;AACjC,IAAA,EAAA,CAACA,eAAK,CAAC,sBAAsB,CAAA,GAAG,CAAC;AACjC,IAAA,EAAA,CAACA,eAAK,CAAC,oBAAoB,CAAA,GAAG,CAAC;QACjC;SAEc,QAAQ,CAAC,GAAW,EAAE,WAAwB,EAAE,gBAAwB,EAAA;AAAxB,IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,gBAAwB,GAAA,KAAA,CAAA,EAAA;AAMpF,IAAA,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;AAE3C,IAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,EAC5B;AACI,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,IAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,UAAU,CAAC;AACpF,IAAA,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;;AAEpE,IAAA,IAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AACxE,IAAA,IAAM,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;AACzF,IAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAC5E,IAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;AACnF,IAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;AACjF,IAAA,IAAM,qBAAqB,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,wBAAwB,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;AACzG,IAAA,IAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;AACnF,IAAA,IAAM,oBAAoB,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;AAClG,IAAA,IAAM,mBAAmB,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;;;;;AAOjG,IAAA,IAAI,WAAW,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EACzC;AACI,QAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACrD,KAAA;IACD,IAAI,aAAa,KAAK,CAAC,EACvB;AACI,QAAA,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACvE,KAAA;IACD,IAAI,qBAAqB,KAAK,CAAC,EAC/B;;AAEI,QAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC5D,KAAA;;IAGD,IAAM,UAAU,GAAG,CAAC,CAAC;IACrB,IAAM,WAAW,GAAG,CAAC,CAAC;IAEtB,IAAM,YAAY,GAAG,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3C,IAAM,aAAa,GAAG,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7C,IAAA,IAAM,YAAY,GAAG,IAAI,KAAK,CAA0B,qBAAqB,CAAC,CAAC;AAC/E,IAAA,IAAI,WAAW,GAAG,UAAU,GAAG,WAAW,CAAC;IAE3C,IAAI,MAAM,KAAK,CAAC,EAChB;;AAEI,QAAA,WAAW,GAAG,YAAY,GAAG,aAAa,CAAC;AAC9C,KAAA;AAED,IAAA,IAAI,kBAA0B,CAAC;IAE/B,IAAI,MAAM,KAAK,CAAC,EAChB;;AAEI,QAAA,IAAI,4BAA4B,CAAC,MAAM,CAAC,EACxC;YACI,kBAAkB,GAAG,4BAA4B,CAAC,MAAM,CAAC,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AAC/F,SAAA;AAED,aAAA;AACI,YAAA,kBAAkB,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;AACzD,SAAA;AACJ,KAAA;AAED,SAAA;AACI,QAAA,kBAAkB,GAAG,kCAAkC,CAAC,gBAAgB,CAAC,CAAC;AAC7E,KAAA;IAED,IAAI,kBAAkB,KAAK,SAAS,EACpC;AACI,QAAA,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;AACnF,KAAA;IAED,IAAM,MAAM,GAAiC,gBAAgB;UACvD,WAAW,CAAC,QAAQ,EAAE,mBAAmB,EAAE,YAAY,CAAC;UACxD,IAAI,CAAC;AAEX,IAAA,IAAM,aAAa,GAAG,WAAW,GAAG,kBAAkB,CAAC;IACvD,IAAI,WAAW,GAAG,aAAa,CAAC;IAChC,IAAI,QAAQ,GAAG,UAAU,CAAC;IAC1B,IAAI,SAAS,GAAG,WAAW,CAAC;IAC5B,IAAI,eAAe,GAAG,YAAY,CAAC;IACnC,IAAI,gBAAgB,GAAG,aAAa,CAAC;AACrC,IAAA,IAAI,WAAW,GAAG,gBAAgB,GAAG,mBAAmB,CAAC;IAEzD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,oBAAoB,EAAE,WAAW,EAAE,EAC3E;QACI,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAChE,QAAA,IAAI,aAAa,GAAG,WAAW,GAAG,CAAC,CAAC;QAEpC,KAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,qBAAqB,EAAE,YAAY,EAAE,EAC/E;;;AAII,YAAA,IAAI,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;YAEtC,IAAI,CAAC,IAAI,EACT;gBACI,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AACvE,aAAA;YAED,IAAI,CAAC,WAAW,CAAC,GAAG;AAChB,gBAAA,OAAO,EAAE,WAAW;;AAGpB,gBAAA,UAAU,EAAE,oBAAoB,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG,eAAe;AACjF,gBAAA,WAAW,EAAE,oBAAoB,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,gBAAgB;gBACpF,WAAW,EAAE,IAAI,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;aACvE,CAAC;YACF,aAAa,IAAI,WAAW,CAAC;AAChC,SAAA;;AAGD,QAAA,WAAW,IAAI,SAAS,GAAG,CAAC,CAAC;QAC7B,WAAW,GAAG,WAAW,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC;;QAGxF,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,SAAS,GAAG,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,QAAA,eAAe,GAAG,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;AAClE,QAAA,gBAAgB,GAAG,CAAC,SAAS,GAAG,WAAW,GAAG,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;;AAGtE,QAAA,WAAW,GAAG,eAAe,GAAG,gBAAgB,GAAG,kBAAkB,CAAC;AACzE,KAAA;;IAGD,IAAI,MAAM,KAAK,CAAC,EAChB;QACI,OAAO;AACH,YAAA,YAAY,EAAE,YAAY,CAAC,GAAG,CAAC,UAAC,YAAY,EAAA;gBAExC,IAAI,MAAM,GAAyD,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;gBAC/F,IAAI,YAAY,GAAG,KAAK,CAAC;AAEzB,gBAAA,IAAI,MAAM,KAAKA,eAAK,CAAC,KAAK,EAC1B;AACI,oBAAA,MAAM,GAAG,IAAI,YAAY,CACrB,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,EAClC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,EACtC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AACnD,iBAAA;AACI,qBAAA,IAAI,MAAM,KAAKA,eAAK,CAAC,YAAY,EACtC;oBACI,YAAY,GAAG,IAAI,CAAC;AACpB,oBAAA,MAAM,GAAG,IAAI,WAAW,CACpB,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,EAClC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,EACtC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AACnD,iBAAA;AACI,qBAAA,IAAI,MAAM,KAAKA,eAAK,CAAC,GAAG,EAC7B;oBACI,YAAY,GAAG,IAAI,CAAC;AACpB,oBAAA,MAAM,GAAG,IAAI,UAAU,CACnB,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,EAClC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,EACtC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AACnD,iBAAA;gBAED,OAAO;AACH,oBAAA,QAAQ,EAAE,IAAIV,mBAAc,CACxB,MAAM,EACN;AACI,wBAAA,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU;AACjC,wBAAA,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW;qBACtC,CACJ;AACD,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,MAAM,EAAE,YAAY,GAAG,sBAAsB,CAAC,QAAQ,CAAC,GAAG,QAAQ;iBACrE,CAAC;AACN,aAAC,CAAC;AACF,YAAA,MAAM,EAAA,MAAA;SACT,CAAC;AACL,KAAA;IAED,OAAO;AACH,QAAA,UAAU,EAAE,YAAY,CAAC,GAAG,CAAC,UAAC,YAAY,EAAA,EAAK,OAAA,IAAI,yBAAyB,CAAC,IAAI,EAAE;AAC/E,YAAA,MAAM,EAAE,gBAAgB;AACxB,YAAA,KAAK,EAAE,UAAU;AACjB,YAAA,MAAM,EAAE,WAAW;AACnB,YAAA,MAAM,EAAE,oBAAoB;AAC5B,YAAA,YAAY,EAAA,YAAA;SACf,CAAC,CAAA,EAAA,CAAC;AACH,QAAA,MAAM,EAAA,MAAA;KACT,CAAC;AACN,CAAC;AAED;;;;AAIG;AACH,SAAS,QAAQ,CAAC,GAAW,EAAE,QAAkB,EAAA;;;AAI7C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAC/C;QACI,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,EAC/C;AAEI,YAAA,OAAO,CAAC,KAAK,CAAI,GAAG,GAAA,6BAA6B,CAAC,CAAC;AAGnD,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,sBAAsB,CAAC,MAAe,EAAA;AAE3C,IAAA,QAAQ,MAAM;QAEV,KAAKW,iBAAO,CAAC,IAAI,EAAE,OAAOA,iBAAO,CAAC,YAAY,CAAC;QAC/C,KAAKA,iBAAO,CAAC,GAAG,EAAE,OAAOA,iBAAO,CAAC,WAAW,CAAC;QAC7C,KAAKA,iBAAO,CAAC,EAAE,EAAE,OAAOA,iBAAO,CAAC,UAAU,CAAC;QAC3C,KAAKA,iBAAO,CAAC,GAAG,EAAE,OAAOA,iBAAO,CAAC,WAAW,CAAC;AAC7C,QAAA,SAAS,OAAO,MAAM,CAAC;AAC1B,KAAA;AACL,CAAC;AAED,SAAS,WAAW,CAAC,QAAkB,EAAE,mBAA2B,EAAE,YAAqB,EAAA;AAEvF,IAAA,IAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;IAC3C,IAAI,qBAAqB,GAAG,CAAC,CAAC;IAE9B,OAAO,qBAAqB,GAAG,mBAAmB,EAClD;AACI,QAAA,IAAM,mBAAmB,GAAG,QAAQ,CAAC,SAAS,CAAC,gBAAgB,GAAG,qBAAqB,EAAE,YAAY,CAAC,CAAC;AACvG,QAAA,IAAM,qBAAqB,GAAG,gBAAgB,GAAG,qBAAqB,GAAG,CAAC,CAAC;AAC3E,QAAA,IAAM,YAAY,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;;QAGzD,IAAI,mBAAmB,KAAK,CAAC,IAAI,mBAAmB,GAAG,mBAAmB,GAAG,qBAAqB,EAClG;AACI,YAAA,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC9D,MAAM;AACT,SAAA;;QAGD,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB,QAAA,OAAO,UAAU,GAAG,mBAAmB,EAAE,UAAU,EAAE,EACrD;YACI,IAAI,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,GAAG,UAAU,CAAC,KAAK,IAAI,EAClE;gBACI,MAAM;AACT,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,UAAU,KAAK,CAAC,CAAC,EACrB;AACI,YAAA,OAAO,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC5E,MAAM;AACT,SAAA;QAED,IAAM,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAChC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,qBAAqB,EAAE,UAAU,CAAC,CACrE,CAAC;QACF,IAAM,KAAK,GAAG,IAAI,QAAQ,CACtB,QAAQ,CAAC,MAAM,EACf,qBAAqB,GAAG,UAAU,GAAG,CAAC,EACtC,mBAAmB,GAAG,UAAU,GAAG,CAAC,CACvC,CAAC;AAEF,QAAA,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;;;;AAKvB,QAAA,qBAAqB,IAAI,CAAC,GAAG,mBAAmB,GAAG,YAAY,CAAC;AACnE,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAClB;;AC/WA;AACAV,sBAAc,CAAC,mBAAmB,CAAC,KAAK,EAAEA,sBAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAEnF;;;;;AAKG;AACH,IAAA,SAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,SAAA,GAAA;KAiCC;AA5BG;;;;;AAKG;AACW,IAAA,SAAA,CAAA,GAAG,GAAjB,UAAkB,QAAwB,EAAE,IAA8B,EAAA;QAEtE,IAAI,QAAQ,CAAC,SAAS,KAAK,KAAK,IAAI,QAAQ,CAAC,IAAI,EACjD;YACI,IACA;gBACI,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,0BAA0B,CAC9C,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,EAC7B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EACvB,QAAQ,CAAC,QAAQ,CACpB,CAAC,CAAC;AACN,aAAA;AACD,YAAA,OAAO,GAAG,EACV;gBACI,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEV,OAAO;AACV,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,EAAE,CAAC;KACV,CAAA;;AA7BM,IAAA,SAAA,CAAA,SAAS,GAAsBG,kBAAa,CAAC,MAAM,CAAC;IA8B/D,OAAC,SAAA,CAAA;AAAA,CAjCD,EAiCC;;ACzCD;AACAH,sBAAc,CAAC,mBAAmB,CAAC,KAAK,EAAEA,sBAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAEnF;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,SAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,SAAA,GAAA;KAgGC;AAjFG;;;;;;;;AAQG;AACW,IAAA,SAAA,CAAA,GAAG,GAAjB,UAAkB,QAAwB,EAAE,IAA8B,EAAA;QAEtE,IAAI,QAAQ,CAAC,SAAS,KAAK,KAAK,IAAI,QAAQ,CAAC,IAAI,EACjD;YACI,IACA;gBACI,IAAM,KAAG,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC;gBACpC,IAAA,EAAA,GAAuC,QAAQ,CAAC,KAAG,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAxF,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,YAAY,GAAA,EAAA,CAAA,YAAA,EAAE,QAAM,GAAA,EAAA,CAAA,MAAwD,CAAC;AAEjG,gBAAA,IAAI,UAAU,EACd;AACI,oBAAA,IAAM,MAAM,GAAG,0BAA0B,CACrC,KAAG,EACH,UAAU,EACV,QAAQ,CAAC,QAAQ,CACpB,CAAC;AAEF,oBAAA,IAAI,QAAM,IAAI,MAAM,CAAC,QAAQ,EAC7B;AACI,wBAAA,KAAK,IAAM,SAAS,IAAI,MAAM,CAAC,QAAQ,EACvC;4BACI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,eAAe,GAAG,QAAM,CAAC;AACnE,yBAAA;AACJ,qBAAA;AAED,oBAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACnC,iBAAA;AACI,qBAAA,IAAI,YAAY,EACrB;oBACI,IAAM,UAAQ,GAA4B,EAAE,CAAC;AAE7C,oBAAA,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,CAAC,EAAA;wBAE1B,IAAM,OAAO,GAAG,IAAII,YAAO,CAAC,IAAIC,gBAAW,CACvC,KAAK,CAAC,QAAQ,EACd;4BACI,MAAM,EAAEC,sBAAY,CAAC,GAAG;4BACxB,SAAS,EAAEC,qBAAW,CAAC,sBAAsB;4BAC7C,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,MAAM,EAAE,KAAK,CAAC,MAAM;AACvB,yBAAA,CACJ,CAAC,CAAC;wBACH,IAAM,OAAO,GAAM,KAAG,GAAA,GAAA,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC;AAElC,wBAAA,IAAI,QAAM;AAAE,4BAAA,EAAA,OAAO,CAAC,WAAW,CAAC,eAAe,GAAG,QAAM,CAAC,EAAA;wBAEzDF,gBAAW,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AACrD,wBAAAD,YAAO,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;wBAErC,IAAI,CAAC,KAAK,CAAC,EACX;AACI,4BAAA,UAAQ,CAAC,KAAG,CAAC,GAAG,OAAO,CAAC;4BACxBC,gBAAW,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,KAAG,CAAC,CAAC;AACjD,4BAAAD,YAAO,CAAC,UAAU,CAAC,OAAO,EAAE,KAAG,CAAC,CAAC;AACpC,yBAAA;AAED,wBAAA,UAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AAChC,qBAAC,CAAC,CAAC;oBAEH,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAA,UAAA,EAAE,CAAC,CAAC;AACzC,iBAAA;AACJ,aAAA;AACD,YAAA,OAAO,GAAG,EACV;gBACI,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEV,OAAO;AACV,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,EAAE,CAAC;KACV,CAAA;;AA5FM,IAAA,SAAA,CAAA,SAAS,GAAsBD,kBAAa,CAAC,MAAM,CAAC;AAE3D;;;;;;;AAOG;IACI,SAAgB,CAAA,gBAAA,GAAG,KAAK,CAAC;IAmFpC,OAAC,SAAA,CAAA;AAAA,CAhGD,EAgGC;;;;;;;;;;;;;;"}