/*!
 * @pixi/interaction - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/interaction is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{Point as t}from"@pixi/math";import{Ticker as e,UPDATE_PRIORITY as i}from"@pixi/ticker";import{DisplayObject as n,TemporaryDisplayObject as o}from"@pixi/display";import{EventEmitter as r}from"@pixi/utils";import{ExtensionType as s}from"@pixi/core";var a=function(){function e(){this.pressure=0,this.rotationAngle=0,this.twist=0,this.tangentialPressure=0,this.global=new t,this.target=null,this.originalEvent=null,this.identifier=null,this.isPrimary=!1,this.button=0,this.buttons=0,this.width=0,this.height=0,this.tiltX=0,this.tiltY=0,this.pointerType=null,this.pressure=0,this.rotationAngle=0,this.twist=0,this.tangentialPressure=0}return Object.defineProperty(e.prototype,"pointerId",{get:function(){return this.identifier},enumerable:!1,configurable:!0}),e.prototype.getLocalPosition=function(t,e,i){return t.worldTransform.applyInverse(i||this.global,e)},e.prototype.copyEvent=function(t){"isPrimary"in t&&t.isPrimary&&(this.isPrimary=!0),this.button="button"in t&&t.button;var e="buttons"in t&&t.buttons;this.buttons=Number.isInteger(e)?e:"which"in t&&t.which,this.width="width"in t&&t.width,this.height="height"in t&&t.height,this.tiltX="tiltX"in t&&t.tiltX,this.tiltY="tiltY"in t&&t.tiltY,this.pointerType="pointerType"in t&&t.pointerType,this.pressure="pressure"in t&&t.pressure,this.rotationAngle="rotationAngle"in t&&t.rotationAngle,this.twist="twist"in t&&t.twist||0,this.tangentialPressure="tangentialPressure"in t&&t.tangentialPressure||0},e.prototype.reset=function(){this.isPrimary=!1},e}(),h=function(t,e){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},h(t,e)};var p=function(){function t(){this.stopped=!1,this.stopsPropagatingAt=null,this.stopPropagationHint=!1,this.target=null,this.currentTarget=null,this.type=null,this.data=null}return t.prototype.stopPropagation=function(){this.stopped=!0,this.stopPropagationHint=!0,this.stopsPropagatingAt=this.currentTarget},t.prototype.reset=function(){this.stopped=!1,this.stopsPropagatingAt=null,this.stopPropagationHint=!1,this.currentTarget=null,this.target=null},t}(),c=function(){function t(e){this._pointerId=e,this._flags=t.FLAGS.NONE}return t.prototype._doSet=function(t,e){this._flags=e?this._flags|t:this._flags&~t},Object.defineProperty(t.prototype,"pointerId",{get:function(){return this._pointerId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"flags",{get:function(){return this._flags},set:function(t){this._flags=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"none",{get:function(){return this._flags===t.FLAGS.NONE},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"over",{get:function(){return 0!=(this._flags&t.FLAGS.OVER)},set:function(e){this._doSet(t.FLAGS.OVER,e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"rightDown",{get:function(){return 0!=(this._flags&t.FLAGS.RIGHT_DOWN)},set:function(e){this._doSet(t.FLAGS.RIGHT_DOWN,e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"leftDown",{get:function(){return 0!=(this._flags&t.FLAGS.LEFT_DOWN)},set:function(e){this._doSet(t.FLAGS.LEFT_DOWN,e)},enumerable:!1,configurable:!0}),t.FLAGS=Object.freeze({NONE:0,OVER:1,LEFT_DOWN:2,RIGHT_DOWN:4}),t}(),u=function(){function e(){this._tempPoint=new t}return e.prototype.recursiveFindHit=function(t,e,i,n,o){var r;if(!e||!e.visible)return!1;var s=t.data.global,a=!1,h=o=e.interactive||o,p=!0;if(e.hitArea)n&&(e.worldTransform.applyInverse(s,this._tempPoint),e.hitArea.contains(this._tempPoint.x,this._tempPoint.y)?a=!0:(n=!1,p=!1)),h=!1;else if(e._mask&&n){var c=e._mask.isMaskData?e._mask.maskObject:e._mask;c&&!(null===(r=c.containsPoint)||void 0===r?void 0:r.call(c,s))&&(n=!1)}if(p&&e.interactiveChildren&&e.children)for(var u=e.children,l=u.length-1;l>=0;l--){var v=u[l],d=this.recursiveFindHit(t,v,i,n,h);if(d){if(!v.parent)continue;h=!1,d&&(t.target&&(n=!1),a=!0)}}return o&&(n&&!t.target&&!e.hitArea&&e.containsPoint&&e.containsPoint(s)&&(a=!0),e.interactive&&(a&&!t.target&&(t.target=e),i&&i(t,e,!!a))),a},e.prototype.findHit=function(t,e,i,n){this.recursiveFindHit(t,e,i,n,!1)},e}(),l={interactive:!1,interactiveChildren:!0,hitArea:null,get buttonMode(){return"pointer"===this.cursor},set buttonMode(t){t?this.cursor="pointer":"pointer"===this.cursor&&(this.cursor=null)},cursor:null,get trackedPointers(){return void 0===this._trackedPointers&&(this._trackedPointers={}),this._trackedPointers},_trackedPointers:void 0};n.mixin(l);var v={target:null,data:{global:null}},d=function(t){function n(e,i){var n=t.call(this)||this;return i=i||{},n.renderer=e,n.autoPreventDefault=void 0===i.autoPreventDefault||i.autoPreventDefault,n.interactionFrequency=i.interactionFrequency||10,n.mouse=new a,n.mouse.identifier=1,n.mouse.global.set(-999999),n.activeInteractionData={},n.activeInteractionData[1]=n.mouse,n.interactionDataPool=[],n.eventData=new p,n.interactionDOMElement=null,n.moveWhenInside=!1,n.eventsAdded=!1,n.tickerAdded=!1,n.mouseOverRenderer=!("PointerEvent"in globalThis),n.supportsTouchEvents="ontouchstart"in globalThis,n.supportsPointerEvents=!!globalThis.PointerEvent,n.onPointerUp=n.onPointerUp.bind(n),n.processPointerUp=n.processPointerUp.bind(n),n.onPointerCancel=n.onPointerCancel.bind(n),n.processPointerCancel=n.processPointerCancel.bind(n),n.onPointerDown=n.onPointerDown.bind(n),n.processPointerDown=n.processPointerDown.bind(n),n.onPointerMove=n.onPointerMove.bind(n),n.processPointerMove=n.processPointerMove.bind(n),n.onPointerOut=n.onPointerOut.bind(n),n.processPointerOverOut=n.processPointerOverOut.bind(n),n.onPointerOver=n.onPointerOver.bind(n),n.cursorStyles={default:"inherit",pointer:"pointer"},n.currentCursorMode=null,n.cursor=null,n.resolution=1,n.delayedEvents=[],n.search=new u,n._tempDisplayObject=new o,n._eventListenerOptions={capture:!0,passive:!1},n._useSystemTicker=void 0===i.useSystemTicker||i.useSystemTicker,n.setTargetElement(n.renderer.view,n.renderer.resolution),n}return function(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(n,t),Object.defineProperty(n.prototype,"useSystemTicker",{get:function(){return this._useSystemTicker},set:function(t){this._useSystemTicker=t,t?this.addTickerListener():this.removeTickerListener()},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"lastObjectRendered",{get:function(){return this.renderer._lastObjectRendered||this._tempDisplayObject},enumerable:!1,configurable:!0}),n.prototype.hitTest=function(t,e){return v.target=null,v.data.global=t,e||(e=this.lastObjectRendered),this.processInteractive(v,e,null,!0),v.target},n.prototype.setTargetElement=function(t,e){void 0===e&&(e=1),this.removeTickerListener(),this.removeEvents(),this.interactionDOMElement=t,this.resolution=e,this.addEvents(),this.addTickerListener()},n.prototype.addTickerListener=function(){!this.tickerAdded&&this.interactionDOMElement&&this._useSystemTicker&&(e.system.add(this.tickerUpdate,this,i.INTERACTION),this.tickerAdded=!0)},n.prototype.removeTickerListener=function(){this.tickerAdded&&(e.system.remove(this.tickerUpdate,this),this.tickerAdded=!1)},n.prototype.addEvents=function(){if(!this.eventsAdded&&this.interactionDOMElement){var t=this.interactionDOMElement.style;globalThis.navigator.msPointerEnabled?(t.msContentZooming="none",t.msTouchAction="none"):this.supportsPointerEvents&&(t.touchAction="none"),this.supportsPointerEvents?(globalThis.document.addEventListener("pointermove",this.onPointerMove,this._eventListenerOptions),this.interactionDOMElement.addEventListener("pointerdown",this.onPointerDown,this._eventListenerOptions),this.interactionDOMElement.addEventListener("pointerleave",this.onPointerOut,this._eventListenerOptions),this.interactionDOMElement.addEventListener("pointerover",this.onPointerOver,this._eventListenerOptions),globalThis.addEventListener("pointercancel",this.onPointerCancel,this._eventListenerOptions),globalThis.addEventListener("pointerup",this.onPointerUp,this._eventListenerOptions)):(globalThis.document.addEventListener("mousemove",this.onPointerMove,this._eventListenerOptions),this.interactionDOMElement.addEventListener("mousedown",this.onPointerDown,this._eventListenerOptions),this.interactionDOMElement.addEventListener("mouseout",this.onPointerOut,this._eventListenerOptions),this.interactionDOMElement.addEventListener("mouseover",this.onPointerOver,this._eventListenerOptions),globalThis.addEventListener("mouseup",this.onPointerUp,this._eventListenerOptions)),this.supportsTouchEvents&&(this.interactionDOMElement.addEventListener("touchstart",this.onPointerDown,this._eventListenerOptions),this.interactionDOMElement.addEventListener("touchcancel",this.onPointerCancel,this._eventListenerOptions),this.interactionDOMElement.addEventListener("touchend",this.onPointerUp,this._eventListenerOptions),this.interactionDOMElement.addEventListener("touchmove",this.onPointerMove,this._eventListenerOptions)),this.eventsAdded=!0}},n.prototype.removeEvents=function(){if(this.eventsAdded&&this.interactionDOMElement){var t=this.interactionDOMElement.style;globalThis.navigator.msPointerEnabled?(t.msContentZooming="",t.msTouchAction=""):this.supportsPointerEvents&&(t.touchAction=""),this.supportsPointerEvents?(globalThis.document.removeEventListener("pointermove",this.onPointerMove,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("pointerdown",this.onPointerDown,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("pointerleave",this.onPointerOut,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("pointerover",this.onPointerOver,this._eventListenerOptions),globalThis.removeEventListener("pointercancel",this.onPointerCancel,this._eventListenerOptions),globalThis.removeEventListener("pointerup",this.onPointerUp,this._eventListenerOptions)):(globalThis.document.removeEventListener("mousemove",this.onPointerMove,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("mousedown",this.onPointerDown,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("mouseout",this.onPointerOut,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("mouseover",this.onPointerOver,this._eventListenerOptions),globalThis.removeEventListener("mouseup",this.onPointerUp,this._eventListenerOptions)),this.supportsTouchEvents&&(this.interactionDOMElement.removeEventListener("touchstart",this.onPointerDown,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("touchcancel",this.onPointerCancel,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("touchend",this.onPointerUp,this._eventListenerOptions),this.interactionDOMElement.removeEventListener("touchmove",this.onPointerMove,this._eventListenerOptions)),this.interactionDOMElement=null,this.eventsAdded=!1}},n.prototype.tickerUpdate=function(t){this._deltaTime+=t,this._deltaTime<this.interactionFrequency||(this._deltaTime=0,this.update())},n.prototype.update=function(){if(this.interactionDOMElement)if(this._didMove)this._didMove=!1;else{for(var t in this.cursor=null,this.activeInteractionData)if(this.activeInteractionData.hasOwnProperty(t)){var e=this.activeInteractionData[t];if(e.originalEvent&&"touch"!==e.pointerType){var i=this.configureInteractionEventForDOMEvent(this.eventData,e.originalEvent,e);this.processInteractive(i,this.lastObjectRendered,this.processPointerOverOut,!0)}}this.setCursorMode(this.cursor)}},n.prototype.setCursorMode=function(t){t=t||"default";var e=!0;if(globalThis.OffscreenCanvas&&this.interactionDOMElement instanceof OffscreenCanvas&&(e=!1),this.currentCursorMode!==t){this.currentCursorMode=t;var i=this.cursorStyles[t];if(i)switch(typeof i){case"string":e&&(this.interactionDOMElement.style.cursor=i);break;case"function":i(t);break;case"object":e&&Object.assign(this.interactionDOMElement.style,i)}else e&&"string"==typeof t&&!Object.prototype.hasOwnProperty.call(this.cursorStyles,t)&&(this.interactionDOMElement.style.cursor=t)}},n.prototype.dispatchEvent=function(t,e,i){i.stopPropagationHint&&t!==i.stopsPropagatingAt||(i.currentTarget=t,i.type=e,t.emit(e,i),t[e]&&t[e](i))},n.prototype.delayDispatchEvent=function(t,e,i){this.delayedEvents.push({displayObject:t,eventString:e,eventData:i})},n.prototype.mapPositionToPoint=function(t,e,i){var n;n=this.interactionDOMElement.parentElement?this.interactionDOMElement.getBoundingClientRect():{x:0,y:0,width:this.interactionDOMElement.width,height:this.interactionDOMElement.height,left:0,top:0};var o=1/this.resolution;t.x=(e-n.left)*(this.interactionDOMElement.width/n.width)*o,t.y=(i-n.top)*(this.interactionDOMElement.height/n.height)*o},n.prototype.processInteractive=function(t,e,i,n){var o=this.search.findHit(t,e,i,n),r=this.delayedEvents;if(!r.length)return o;t.stopPropagationHint=!1;var s=r.length;this.delayedEvents=[];for(var a=0;a<s;a++){var h=r[a],p=h.displayObject,c=h.eventString,u=h.eventData;u.stopsPropagatingAt===p&&(u.stopPropagationHint=!0),this.dispatchEvent(p,c,u)}return o},n.prototype.onPointerDown=function(t){if(!this.supportsTouchEvents||"touch"!==t.pointerType){var e=this.normalizeToPointerData(t);if(this.autoPreventDefault&&e[0].isNormalized)(t.cancelable||!("cancelable"in t))&&t.preventDefault();for(var i=e.length,n=0;n<i;n++){var o=e[n],r=this.getInteractionDataForPointerId(o),s=this.configureInteractionEventForDOMEvent(this.eventData,o,r);if(s.data.originalEvent=t,this.processInteractive(s,this.lastObjectRendered,this.processPointerDown,!0),this.emit("pointerdown",s),"touch"===o.pointerType)this.emit("touchstart",s);else if("mouse"===o.pointerType||"pen"===o.pointerType){var a=2===o.button;this.emit(a?"rightdown":"mousedown",this.eventData)}}}},n.prototype.processPointerDown=function(t,e,i){var n=t.data,o=t.data.identifier;if(i)if(e.trackedPointers[o]||(e.trackedPointers[o]=new c(o)),this.dispatchEvent(e,"pointerdown",t),"touch"===n.pointerType)this.dispatchEvent(e,"touchstart",t);else if("mouse"===n.pointerType||"pen"===n.pointerType){var r=2===n.button;r?e.trackedPointers[o].rightDown=!0:e.trackedPointers[o].leftDown=!0,this.dispatchEvent(e,r?"rightdown":"mousedown",t)}},n.prototype.onPointerComplete=function(t,e,i){var n=this.normalizeToPointerData(t),o=n.length,r=t.target;t.composedPath&&t.composedPath().length>0&&(r=t.composedPath()[0]);for(var s=r!==this.interactionDOMElement?"outside":"",a=0;a<o;a++){var h=n[a],p=this.getInteractionDataForPointerId(h),c=this.configureInteractionEventForDOMEvent(this.eventData,h,p);if(c.data.originalEvent=t,this.processInteractive(c,this.lastObjectRendered,i,e||!s),this.emit(e?"pointercancel":"pointerup"+s,c),"mouse"===h.pointerType||"pen"===h.pointerType){var u=2===h.button;this.emit(u?"rightup"+s:"mouseup"+s,c)}else"touch"===h.pointerType&&(this.emit(e?"touchcancel":"touchend"+s,c),this.releaseInteractionDataForPointerId(h.pointerId))}},n.prototype.onPointerCancel=function(t){this.supportsTouchEvents&&"touch"===t.pointerType||this.onPointerComplete(t,!0,this.processPointerCancel)},n.prototype.processPointerCancel=function(t,e){var i=t.data,n=t.data.identifier;void 0!==e.trackedPointers[n]&&(delete e.trackedPointers[n],this.dispatchEvent(e,"pointercancel",t),"touch"===i.pointerType&&this.dispatchEvent(e,"touchcancel",t))},n.prototype.onPointerUp=function(t){this.supportsTouchEvents&&"touch"===t.pointerType||this.onPointerComplete(t,!1,this.processPointerUp)},n.prototype.processPointerUp=function(t,e,i){var n=t.data,o=t.data.identifier,r=e.trackedPointers[o],s="touch"===n.pointerType,a="mouse"===n.pointerType||"pen"===n.pointerType,h=!1;if(a){var p=2===n.button,u=c.FLAGS,l=p?u.RIGHT_DOWN:u.LEFT_DOWN,v=void 0!==r&&r.flags&l;i?(this.dispatchEvent(e,p?"rightup":"mouseup",t),v&&(this.dispatchEvent(e,p?"rightclick":"click",t),h=!0)):v&&this.dispatchEvent(e,p?"rightupoutside":"mouseupoutside",t),r&&(p?r.rightDown=!1:r.leftDown=!1)}i?(this.dispatchEvent(e,"pointerup",t),s&&this.dispatchEvent(e,"touchend",t),r&&(a&&!h||this.dispatchEvent(e,"pointertap",t),s&&(this.dispatchEvent(e,"tap",t),r.over=!1))):r&&(this.dispatchEvent(e,"pointerupoutside",t),s&&this.dispatchEvent(e,"touchendoutside",t)),r&&r.none&&delete e.trackedPointers[o]},n.prototype.onPointerMove=function(t){if(!this.supportsTouchEvents||"touch"!==t.pointerType){var e=this.normalizeToPointerData(t);"mouse"!==e[0].pointerType&&"pen"!==e[0].pointerType||(this._didMove=!0,this.cursor=null);for(var i=e.length,n=0;n<i;n++){var o=e[n],r=this.getInteractionDataForPointerId(o),s=this.configureInteractionEventForDOMEvent(this.eventData,o,r);s.data.originalEvent=t,this.processInteractive(s,this.lastObjectRendered,this.processPointerMove,!0),this.emit("pointermove",s),"touch"===o.pointerType&&this.emit("touchmove",s),"mouse"!==o.pointerType&&"pen"!==o.pointerType||this.emit("mousemove",s)}"mouse"===e[0].pointerType&&this.setCursorMode(this.cursor)}},n.prototype.processPointerMove=function(t,e,i){var n=t.data,o="touch"===n.pointerType,r="mouse"===n.pointerType||"pen"===n.pointerType;r&&this.processPointerOverOut(t,e,i),this.moveWhenInside&&!i||(this.dispatchEvent(e,"pointermove",t),o&&this.dispatchEvent(e,"touchmove",t),r&&this.dispatchEvent(e,"mousemove",t))},n.prototype.onPointerOut=function(t){if(!this.supportsTouchEvents||"touch"!==t.pointerType){var e=this.normalizeToPointerData(t)[0];"mouse"===e.pointerType&&(this.mouseOverRenderer=!1,this.setCursorMode(null));var i=this.getInteractionDataForPointerId(e),n=this.configureInteractionEventForDOMEvent(this.eventData,e,i);n.data.originalEvent=e,this.processInteractive(n,this.lastObjectRendered,this.processPointerOverOut,!1),this.emit("pointerout",n),"mouse"===e.pointerType||"pen"===e.pointerType?this.emit("mouseout",n):this.releaseInteractionDataForPointerId(i.identifier)}},n.prototype.processPointerOverOut=function(t,e,i){var n=t.data,o=t.data.identifier,r="mouse"===n.pointerType||"pen"===n.pointerType,s=e.trackedPointers[o];i&&!s&&(s=e.trackedPointers[o]=new c(o)),void 0!==s&&(i&&this.mouseOverRenderer?(s.over||(s.over=!0,this.delayDispatchEvent(e,"pointerover",t),r&&this.delayDispatchEvent(e,"mouseover",t)),r&&null===this.cursor&&(this.cursor=e.cursor)):s.over&&(s.over=!1,this.dispatchEvent(e,"pointerout",this.eventData),r&&this.dispatchEvent(e,"mouseout",t),s.none&&delete e.trackedPointers[o]))},n.prototype.onPointerOver=function(t){if(!this.supportsTouchEvents||"touch"!==t.pointerType){var e=this.normalizeToPointerData(t)[0],i=this.getInteractionDataForPointerId(e),n=this.configureInteractionEventForDOMEvent(this.eventData,e,i);n.data.originalEvent=e,"mouse"===e.pointerType&&(this.mouseOverRenderer=!0),this.emit("pointerover",n),"mouse"!==e.pointerType&&"pen"!==e.pointerType||this.emit("mouseover",n)}},n.prototype.getInteractionDataForPointerId=function(t){var e,i=t.pointerId;return 1===i||"mouse"===t.pointerType?e=this.mouse:this.activeInteractionData[i]?e=this.activeInteractionData[i]:((e=this.interactionDataPool.pop()||new a).identifier=i,this.activeInteractionData[i]=e),e.copyEvent(t),e},n.prototype.releaseInteractionDataForPointerId=function(t){var e=this.activeInteractionData[t];e&&(delete this.activeInteractionData[t],e.reset(),this.interactionDataPool.push(e))},n.prototype.configureInteractionEventForDOMEvent=function(t,e,i){return t.data=i,this.mapPositionToPoint(i.global,e.clientX,e.clientY),"touch"===e.pointerType&&(e.globalX=i.global.x,e.globalY=i.global.y),i.originalEvent=e,t.reset(),t},n.prototype.normalizeToPointerData=function(t){var e=[];if(this.supportsTouchEvents&&t instanceof TouchEvent)for(var i=0,n=t.changedTouches.length;i<n;i++){var o=t.changedTouches[i];void 0===o.button&&(o.button=t.touches.length?1:0),void 0===o.buttons&&(o.buttons=t.touches.length?1:0),void 0===o.isPrimary&&(o.isPrimary=1===t.touches.length&&"touchstart"===t.type),void 0===o.width&&(o.width=o.radiusX||1),void 0===o.height&&(o.height=o.radiusY||1),void 0===o.tiltX&&(o.tiltX=0),void 0===o.tiltY&&(o.tiltY=0),void 0===o.pointerType&&(o.pointerType="touch"),void 0===o.pointerId&&(o.pointerId=o.identifier||0),void 0===o.pressure&&(o.pressure=o.force||.5),void 0===o.twist&&(o.twist=0),void 0===o.tangentialPressure&&(o.tangentialPressure=0),void 0===o.layerX&&(o.layerX=o.offsetX=o.clientX),void 0===o.layerY&&(o.layerY=o.offsetY=o.clientY),o.isNormalized=!0,e.push(o)}else if(globalThis.MouseEvent&&(!(t instanceof MouseEvent)||this.supportsPointerEvents&&t instanceof globalThis.PointerEvent))e.push(t);else{var r=t;void 0===r.isPrimary&&(r.isPrimary=!0),void 0===r.width&&(r.width=1),void 0===r.height&&(r.height=1),void 0===r.tiltX&&(r.tiltX=0),void 0===r.tiltY&&(r.tiltY=0),void 0===r.pointerType&&(r.pointerType="mouse"),void 0===r.pointerId&&(r.pointerId=1),void 0===r.pressure&&(r.pressure=.5),void 0===r.twist&&(r.twist=0),void 0===r.tangentialPressure&&(r.tangentialPressure=0),r.isNormalized=!0,e.push(r)}return e},n.prototype.destroy=function(){this.removeEvents(),this.removeTickerListener(),this.removeAllListeners(),this.renderer=null,this.mouse=null,this.eventData=null,this.interactionDOMElement=null,this.onPointerDown=null,this.processPointerDown=null,this.onPointerUp=null,this.processPointerUp=null,this.onPointerCancel=null,this.processPointerCancel=null,this.onPointerMove=null,this.processPointerMove=null,this.onPointerOut=null,this.processPointerOverOut=null,this.onPointerOver=null,this.search=null},n.extension={name:"interaction",type:[s.RendererPlugin,s.CanvasRendererPlugin]},n}(r);export{a as InteractionData,p as InteractionEvent,d as InteractionManager,c as InteractionTrackingData,l as interactiveTarget};
//# sourceMappingURL=interaction.min.mjs.map
