/// <reference path="./global.d.ts" />

import { ALPHA_MODES } from '@pixi/constants';
import type { ArrayFixed } from '@pixi/utils';
import type { BaseTexture as BaseTexture_2 } from '@pixi/core';
import { BLEND_MODES } from '@pixi/constants';
import { BUFFER_BITS } from '@pixi/constants';
import { BUFFER_TYPE } from '@pixi/constants';
import { CLEAR_MODES } from '@pixi/constants';
import type { COLOR_MASK_BITS } from '@pixi/constants';
import type { Dict } from '@pixi/utils';
import { DRAW_MODES } from '@pixi/constants';
import { EventEmitter } from '@pixi/utils';
import { FORMATS } from '@pixi/constants';
import { GC_MODES } from '@pixi/constants';
import type { IPointData } from '@pixi/math';
import type { ISize } from '@pixi/math';
import type { ISpriteMaskFilter as ISpriteMaskFilter_2 } from '@pixi/core';
import { MASK_TYPES } from '@pixi/constants';
import { Matrix } from '@pixi/math';
import type { MIPMAP_MODES } from '@pixi/constants';
import { MSAA_QUALITY } from '@pixi/constants';
import { Point } from '@pixi/math';
import { Rectangle } from '@pixi/math';
import { RENDERER_TYPE } from '@pixi/constants';
import { Runner } from '@pixi/runner';
import { SCALE_MODES } from '@pixi/constants';
import { TARGETS } from '@pixi/constants';
import { TYPES } from '@pixi/constants';
import type { WRAP_MODES } from '@pixi/constants';

/**
 * Renderer dedicated to drawing and batching sprites.
 *
 * This is the default batch renderer. It buffers objects
 * with texture-based geometries and renders them in
 * batches. It uploads multiple textures to the GPU to
 * reduce to the number of draw calls.
 * @memberof PIXI
 */
export declare class AbstractBatchRenderer extends ObjectRenderer {
    /** The WebGL state in which this renderer will work. */
    readonly state: State;
    /**
     * The number of bufferable objects before a flush
     * occurs automatically.
     * @default settings.SPRITE_BATCH_SIZE * 4
     */
    size: number;
    /**
     * Maximum number of textures that can be uploaded to
     * the GPU under the current context. It is initialized
     * properly in `this.contextChange`.
     * @see PIXI.AbstractBatchRenderer#contextChange
     * @readonly
     */
    MAX_TEXTURES: number;
    /**
     * This is used to generate a shader that can
     * color each vertex based on a `aTextureId`
     * attribute that points to an texture in `uSampler`.
     *
     * This enables the objects with different textures
     * to be drawn in the same draw call.
     *
     * You can customize your shader by creating your
     * custom shader generator.
     */
    protected shaderGenerator: BatchShaderGenerator;
    /**
     * The class that represents the geometry of objects
     * that are going to be batched with this.
     * @member {object}
     * @default PIXI.BatchGeometry
     */
    protected geometryClass: typeof BatchGeometry;
    /**
     * Size of data being buffered per vertex in the
     * attribute buffers (in floats). By default, the
     * batch-renderer plugin uses 6:
     *
     * | aVertexPosition | 2 |
     * |-----------------|---|
     * | aTextureCoords  | 2 |
     * | aColor          | 1 |
     * | aTextureId      | 1 |
     * @readonly
     */
    protected vertexSize: number;
    /** Total count of all vertices used by the currently buffered objects. */
    protected _vertexCount: number;
    /** Total count of all indices used by the currently buffered objects. */
    protected _indexCount: number;
    /**
     * Buffer of objects that are yet to be rendered.
     * @member {PIXI.DisplayObject[]}
     */
    protected _bufferedElements: Array<IBatchableElement>;
    /**
     * Data for texture batch builder, helps to save a bit of CPU on a pass.
     * @member {PIXI.BaseTexture[]}
     */
    protected _bufferedTextures: Array<BaseTexture>;
    /** Number of elements that are buffered and are waiting to be flushed. */
    protected _bufferSize: number;
    /**
     * This shader is generated by `this.shaderGenerator`.
     *
     * It is generated specifically to handle the required
     * number of textures being batched together.
     */
    protected _shader: Shader;
    /**
     * A flush may occur multiple times in a single
     * frame. On iOS devices or when
     * `settings.CAN_UPLOAD_SAME_BUFFER` is false, the
     * batch renderer does not upload data to the same
     * `WebGLBuffer` for performance reasons.
     *
     * This is the index into `packedGeometries` that points to
     * geometry holding the most recent buffers.
     */
    protected _flushId: number;
    /**
     * Pool of `ViewableBuffer` objects that are sorted in
     * order of increasing size. The flush method uses
     * the buffer with the least size above the amount
     * it requires. These are used for passing attributes.
     *
     * The first buffer has a size of 8; each subsequent
     * buffer has double capacity of its previous.
     * @member {PIXI.ViewableBuffer[]}
     * @see PIXI.AbstractBatchRenderer#getAttributeBuffer
     */
    protected _aBuffers: Array<ViewableBuffer>;
    /**
     * Pool of `Uint16Array` objects that are sorted in
     * order of increasing size. The flush method uses
     * the buffer with the least size above the amount
     * it requires. These are used for passing indices.
     *
     * The first buffer has a size of 12; each subsequent
     * buffer has double capacity of its previous.
     * @member {Uint16Array[]}
     * @see PIXI.AbstractBatchRenderer#getIndexBuffer
     */
    protected _iBuffers: Array<Uint16Array>;
    protected _dcIndex: number;
    protected _aIndex: number;
    protected _iIndex: number;
    protected _attributeBuffer: ViewableBuffer;
    protected _indexBuffer: Uint16Array;
    protected _tempBoundTextures: BaseTexture[];
    /**
     * Pool of `this.geometryClass` geometry objects
     * that store buffers. They are used to pass data
     * to the shader on each draw call.
     *
     * These are never re-allocated again, unless a
     * context change occurs; however, the pool may
     * be expanded if required.
     * @member {PIXI.Geometry[]}
     * @see PIXI.AbstractBatchRenderer.contextChange
     */
    private _packedGeometries;
    /**
     * Size of `this._packedGeometries`. It can be expanded
     * if more than `this._packedGeometryPoolSize` flushes
     * occur in a single frame.
     */
    private _packedGeometryPoolSize;
    /**
     * This will hook onto the renderer's `contextChange`
     * and `prerender` signals.
     * @param {PIXI.Renderer} renderer - The renderer this works for.
     */
    constructor(renderer: Renderer);
    /**
     * Handles the `contextChange` signal.
     *
     * It calculates `this.MAX_TEXTURES` and allocating the packed-geometry object pool.
     */
    contextChange(): void;
    /** Makes sure that static and dynamic flush pooled objects have correct dimensions. */
    initFlushBuffers(): void;
    /** Handles the `prerender` signal. It ensures that flushes start from the first geometry object again. */
    onPrerender(): void;
    /**
     * Buffers the "batchable" object. It need not be rendered immediately.
     * @param {PIXI.DisplayObject} element - the element to render when
     *    using this renderer
     */
    render(element: IBatchableElement): void;
    buildTexturesAndDrawCalls(): void;
    /**
     * Populating drawcalls for rendering
     * @param texArray
     * @param start
     * @param finish
     */
    buildDrawCalls(texArray: BatchTextureArray, start: number, finish: number): void;
    /**
     * Bind textures for current rendering
     * @param texArray
     */
    bindAndClearTexArray(texArray: BatchTextureArray): void;
    updateGeometry(): void;
    drawBatches(): void;
    /** Renders the content _now_ and empties the current batch. */
    flush(): void;
    /** Starts a new sprite batch. */
    start(): void;
    /** Stops and flushes the current batch. */
    stop(): void;
    /** Destroys this `AbstractBatchRenderer`. It cannot be used again. */
    destroy(): void;
    /**
     * Fetches an attribute buffer from `this._aBuffers` that can hold atleast `size` floats.
     * @param size - minimum capacity required
     * @returns - buffer than can hold atleast `size` floats
     */
    getAttributeBuffer(size: number): ViewableBuffer;
    /**
     * Fetches an index buffer from `this._iBuffers` that can
     * have at least `size` capacity.
     * @param size - minimum required capacity
     * @returns - buffer that can fit `size` indices.
     */
    getIndexBuffer(size: number): Uint16Array;
    /**
     * Takes the four batching parameters of `element`, interleaves
     * and pushes them into the batching attribute/index buffers given.
     *
     * It uses these properties: `vertexData` `uvs`, `textureId` and
     * `indicies`. It also uses the "tint" of the base-texture, if
     * present.
     * @param {PIXI.DisplayObject} element - element being rendered
     * @param attributeBuffer - attribute buffer.
     * @param indexBuffer - index buffer
     * @param aIndex - number of floats already in the attribute buffer
     * @param iIndex - number of indices already in `indexBuffer`
     */
    packInterleavedGeometry(element: IBatchableElement, attributeBuffer: ViewableBuffer, indexBuffer: Uint16Array, aIndex: number, iIndex: number): void;
    /**
     * Pool of `BatchDrawCall` objects that `flush` used
     * to create "batches" of the objects being rendered.
     *
     * These are never re-allocated again.
     * Shared between all batch renderers because it can be only one "flush" working at the moment.
     * @member {PIXI.BatchDrawCall[]}
     */
    static _drawCallPool: Array<BatchDrawCall>;
    /**
     * Pool of `BatchDrawCall` objects that `flush` used
     * to create "batches" of the objects being rendered.
     *
     * These are never re-allocated again.
     * Shared between all batch renderers because it can be only one "flush" working at the moment.
     * @member {PIXI.BatchTextureArray[]}
     */
    static _textureArrayPool: Array<BatchTextureArray>;
}

/**
 * System plugin to the renderer to manage specific types of masking operations.
 * @memberof PIXI
 */
declare class AbstractMaskSystem implements ISystem {
    /**
     * The mask stack
     * @member {PIXI.MaskData[]}
     */
    protected maskStack: Array<MaskData>;
    /**
     * Constant for gl.enable
     * @private
     */
    protected glConst: number;
    protected renderer: Renderer;
    /**
     * @param renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    /** Gets count of masks of certain type. */
    getStackLength(): number;
    /**
     * Changes the mask stack that is used by this System.
     * @param {PIXI.MaskData[]} maskStack - The mask stack
     */
    setMaskStack(maskStack: Array<MaskData>): void;
    /**
     * Setup renderer to use the current mask data.
     * @private
     */
    protected _useCurrent(): void;
    /** Destroys the mask stack. */
    destroy(): void;
}

/**
 * Resource that can manage several resource (items) inside.
 * All resources need to have the same pixel size.
 * Parent class for CubeResource and ArrayResource
 * @memberof PIXI
 */
export declare abstract class AbstractMultiResource extends Resource {
    /** Number of elements in array. */
    readonly length: number;
    /**
     * Collection of partial baseTextures that correspond to resources.
     * @readonly
     */
    items: Array<BaseTexture>;
    /**
     * Dirty IDs for each part.
     * @readonly
     */
    itemDirtyIds: Array<number>;
    /**
     * Promise when loading.
     * @default null
     */
    private _load;
    /** Bound baseTexture, there can only be one. */
    baseTexture: BaseTexture;
    /**
     * @param length
     * @param options - Options to for Resource constructor
     * @param {number} [options.width] - Width of the resource
     * @param {number} [options.height] - Height of the resource
     */
    constructor(length: number, options?: ISize);
    /**
     * Used from ArrayResource and CubeResource constructors.
     * @param resources - Can be resources, image elements, canvas, etc. ,
     *  length should be same as constructor length
     * @param options - Detect options for resources
     */
    protected initFromArray(resources: Array<any>, options?: IAutoDetectOptions): void;
    /** Destroy this BaseImageResource. */
    dispose(): void;
    /**
     * Set a baseTexture by ID
     * @param baseTexture
     * @param index - Zero-based index of resource to set
     * @returns - Instance for chaining
     */
    abstract addBaseTextureAt(baseTexture: BaseTexture, index: number): this;
    /**
     * Set a resource by ID
     * @param resource
     * @param index - Zero-based index of resource to set
     * @returns - Instance for chaining
     */
    addResourceAt(resource: Resource, index: number): this;
    /**
     * Set the parent base texture.
     * @param baseTexture
     */
    bind(baseTexture: BaseTexture): void;
    /**
     * Unset the parent base texture.
     * @param baseTexture
     */
    unbind(baseTexture: BaseTexture): void;
    /**
     * Load all the resources simultaneously
     * @returns - When load is resolved
     */
    load(): Promise<this>;
}

/**
 * The AbstractRenderer is the base for a PixiJS Renderer. It is extended by the {@link PIXI.CanvasRenderer}
 * and {@link PIXI.Renderer} which can be used for rendering a PixiJS scene.
 * @abstract
 * @class
 * @extends PIXI.utils.EventEmitter
 * @memberof PIXI
 */
export declare abstract class AbstractRenderer extends EventEmitter {
    resolution: number;
    clearBeforeRender?: boolean;
    readonly options: IRendererOptions;
    readonly type: RENDERER_TYPE;
    readonly screen: Rectangle;
    readonly view: HTMLCanvasElement;
    readonly plugins: IRendererPlugins;
    readonly useContextAlpha: boolean | 'notMultiplied';
    readonly autoDensity: boolean;
    readonly preserveDrawingBuffer: boolean;
    protected _backgroundColor: number;
    protected _backgroundColorString: string;
    _backgroundColorRgba: number[];
    _lastObjectRendered: IRenderableObject;
    /**
     * @param type - The renderer type.
     * @param {PIXI.IRendererOptions} [options] - The optional renderer parameters.
     * @param {boolean} [options.antialias=false] -
     *  **WebGL Only.** Whether to enable anti-aliasing. This may affect performance.
     * @param {boolean} [options.autoDensity=false] -
     *  Whether the CSS dimensions of the renderer's view should be resized automatically.
     * @param {number} [options.backgroundAlpha=1] -
     *  Transparency of the background color, value from `0` (fully transparent) to `1` (fully opaque).
     * @param {number} [options.backgroundColor=0x000000] -
     *  The background color used to clear the canvas. It accepts hex numbers (e.g. `0xff0000`).
     * @param {boolean} [options.clearBeforeRender=true] - Whether to clear the canvas before new render passes.
     * @param {PIXI.IRenderingContext} [options.context] - **WebGL Only.** User-provided WebGL rendering context object.
     * @param {number} [options.height=600] - The height of the renderer's view.
     * @param {string} [options.powerPreference] -
     *  **WebGL Only.** A hint indicating what configuration of GPU is suitable for the WebGL context,
     *  can be `'default'`, `'high-performance'` or `'low-power'`.
     *  Setting to `'high-performance'` will prioritize rendering performance over power consumption,
     *  while setting to `'low-power'` will prioritize power saving over rendering performance.
     * @param {boolean} [options.premultipliedAlpha=true] -
     *  **WebGL Only.** Whether the compositor will assume the drawing buffer contains colors with premultiplied alpha.
     * @param {boolean} [options.preserveDrawingBuffer=false] -
     *  **WebGL Only.** Whether to enable drawing buffer preservation. If enabled, the drawing buffer will preserve
     *  its value until cleared or overwritten. Enable this if you need to call `toDataUrl` on the WebGL context.
     * @param {number} [options.resolution=PIXI.settings.RESOLUTION] -
     *  The resolution / device pixel ratio of the renderer.
     * @param {boolean} [options.transparent] -
     *  **Deprecated since 6.0.0, Use `backgroundAlpha` instead.** \
     *  `true` sets `backgroundAlpha` to `0`, `false` sets `backgroundAlpha` to `1`.
     * @param {boolean|'notMultiplied'} [options.useContextAlpha=true] -
     *  Pass-through value for canvas' context attribute `alpha`. This option is for cases where the
     *  canvas needs to be opaque, possibly for performance reasons on some older devices.
     *  If you want to set transparency, please use `backgroundAlpha`. \
     *  **WebGL Only:** When set to `'notMultiplied'`, the canvas' context attribute `alpha` will be
     *  set to `true` and `premultipliedAlpha` will be to `false`.
     * @param {HTMLCanvasElement} [options.view=null] -
     *  The canvas to use as the view. If omitted, a new canvas will be created.
     * @param {number} [options.width=800] - The width of the renderer's view.
     */
    constructor(type?: RENDERER_TYPE, options?: IRendererOptions);
    /**
     * Initialize the plugins.
     * @protected
     * @param {object} staticMap - The dictionary of statically saved plugins.
     */
    initPlugins(staticMap: IRendererPlugins): void;
    /**
     * Same as view.width, actual number of pixels in the canvas by horizontal.
     * @member {number}
     * @readonly
     * @default 800
     */
    get width(): number;
    /**
     * Same as view.height, actual number of pixels in the canvas by vertical.
     * @member {number}
     * @readonly
     * @default 600
     */
    get height(): number;
    /**
     * Resizes the screen and canvas as close as possible to the specified width and height.
     * Canvas dimensions are multiplied by resolution and rounded to the nearest integers.
     * The new canvas dimensions divided by the resolution become the new screen dimensions.
     * @param desiredScreenWidth - The desired width of the screen.
     * @param desiredScreenHeight - The desired height of the screen.
     */
    resize(desiredScreenWidth: number, desiredScreenHeight: number): void;
    /**
     * Useful function that returns a texture of the display object that can then be used to create sprites
     * This can be quite useful if your displayObject is complicated and needs to be reused multiple times.
     * @method PIXI.AbstractRenderer#generateTexture
     * @param displayObject - The displayObject the object will be generated from.
     * @param {object} options - Generate texture options.
     * @param {PIXI.SCALE_MODES} options.scaleMode - The scale mode of the texture.
     * @param {number} options.resolution - The resolution / device pixel ratio of the texture being generated.
     * @param {PIXI.Rectangle} options.region - The region of the displayObject, that shall be rendered,
     *        if no region is specified, defaults to the local bounds of the displayObject.
     * @param {PIXI.MSAA_QUALITY} options.multisample - The number of samples of the frame buffer.
     * @returns A texture of the graphics object.
     */
    generateTexture(displayObject: IRenderableObject, options?: IGenerateTextureOptions): RenderTexture;
    /**
     * Please use the options argument instead.
     * @method PIXI.AbstractRenderer#generateTexture
     * @deprecated Since 6.1.0
     * @param displayObject - The displayObject the object will be generated from.
     * @param scaleMode - The scale mode of the texture.
     * @param resolution - The resolution / device pixel ratio of the texture being generated.
     * @param region - The region of the displayObject, that shall be rendered,
     *        if no region is specified, defaults to the local bounds of the displayObject.
     * @returns A texture of the graphics object.
     */
    generateTexture(displayObject: IRenderableObject, scaleMode?: SCALE_MODES, resolution?: number, region?: Rectangle): RenderTexture;
    /**
     * Adds a new system to the renderer.
     * @param ClassRef - Class reference
     * @param name - Property name for system
     * @returns Return instance of renderer
     */
    abstract addSystem(ClassRef: ISystemConstructor, name: string): this;
    abstract render(displayObject: IRenderableObject, options?: IRendererRenderOptions): void;
    /**
     * Removes everything from the renderer and optionally removes the Canvas DOM element.
     * @param [removeView=false] - Removes the Canvas element from the DOM.
     */
    destroy(removeView?: boolean): void;
    /**
     * The background color to fill if not transparent
     * @member {number}
     */
    get backgroundColor(): number;
    set backgroundColor(value: number);
    /**
     * The background color alpha. Setting this to 0 will make the canvas transparent.
     * @member {number}
     */
    get backgroundAlpha(): number;
    set backgroundAlpha(value: number);
}

/**
 * A resource that contains a number of sources.
 * @memberof PIXI
 */
export declare class ArrayResource extends AbstractMultiResource {
    /**
     * @param source - Number of items in array or the collection
     *        of image URLs to use. Can also be resources, image elements, canvas, etc.
     * @param options - Options to apply to {@link PIXI.autoDetectResource}
     * @param {number} [options.width] - Width of the resource
     * @param {number} [options.height] - Height of the resource
     */
    constructor(source: number | Array<any>, options?: ISize);
    /**
     * Set a baseTexture by ID,
     * ArrayResource just takes resource from it, nothing more
     * @param baseTexture
     * @param index - Zero-based index of resource to set
     * @returns - Instance for chaining
     */
    addBaseTextureAt(baseTexture: BaseTexture, index: number): this;
    /**
     * Add binding
     * @param baseTexture
     */
    bind(baseTexture: BaseTexture): void;
    /**
     * Upload the resources to the GPU.
     * @param renderer
     * @param texture
     * @param glTexture
     * @returns - whether texture was uploaded
     */
    upload(renderer: Renderer, texture: BaseTexture, glTexture: GLTexture): boolean;
}

/**
 * Holds the information for a single attribute structure required to render geometry.
 *
 * This does not contain the actual data, but instead has a buffer id that maps to a {@link PIXI.Buffer}
 * This can include anything from positions, uvs, normals, colors etc.
 * @memberof PIXI
 */
export declare class Attribute {
    buffer: number;
    size: number;
    normalized: boolean;
    type: TYPES;
    stride: number;
    start: number;
    instance: boolean;
    /**
     * @param buffer - the id of the buffer that this attribute will look for
     * @param size - the size of the attribute. If you have 2 floats per vertex (eg position x and y) this would be 2.
     * @param normalized - should the data be normalized.
     * @param {PIXI.TYPES} [type=PIXI.TYPES.FLOAT] - what type of number is the attribute. Check {@link PIXI.TYPES} to see the ones available
     * @param [stride=0] - How far apart, in bytes, the start of each value is. (used for interleaving data)
     * @param [start=0] - How far into the array to start reading values (used for interleaving data)
     * @param [instance=false] - Whether the geometry is instanced.
     */
    constructor(buffer: number, size?: number, normalized?: boolean, type?: TYPES, stride?: number, start?: number, instance?: boolean);
    /** Destroys the Attribute. */
    destroy(): void;
    /**
     * Helper function that creates an Attribute based on the information provided
     * @param buffer - the id of the buffer that this attribute will look for
     * @param [size=0] - the size of the attribute. If you have 2 floats per vertex (eg position x and y) this would be 2
     * @param [normalized=false] - should the data be normalized.
     * @param [type=PIXI.TYPES.FLOAT] - what type of number is the attribute. Check {@link PIXI.TYPES} to see the ones available
     * @param [stride=0] - How far apart, in bytes, the start of each value is. (used for interleaving data)
     * @returns - A new {@link PIXI.Attribute} based on the information provided
     */
    static from(buffer: number, size?: number, normalized?: boolean, type?: TYPES, stride?: number): Attribute;
}

/**
 * This helper function will automatically detect which renderer you should be using.
 * WebGL is the preferred renderer as it is a lot faster. If WebGL is not supported by
 * the browser then this function will return a canvas renderer.
 * @memberof PIXI
 * @function autoDetectRenderer
 * @param {PIXI.IRendererOptionsAuto} [options] - The optional renderer parameters.
 * @param {boolean} [options.antialias=false] -
 *  **WebGL Only.** Whether to enable anti-aliasing. This may affect performance.
 * @param {boolean} [options.autoDensity=false] -
 *  Whether the CSS dimensions of the renderer's view should be resized automatically.
 * @param {number} [options.backgroundAlpha=1] -
 *  Transparency of the background color, value from `0` (fully transparent) to `1` (fully opaque).
 * @param {number} [options.backgroundColor=0x000000] -
 *  The background color used to clear the canvas. It accepts hex numbers (e.g. `0xff0000`).
 * @param {boolean} [options.clearBeforeRender=true] - Whether to clear the canvas before new render passes.
 * @param {PIXI.IRenderingContext} [options.context] - **WebGL Only.** User-provided WebGL rendering context object.
 * @param {boolean} [options.forceCanvas=false] -
 *  Force using {@link PIXI.CanvasRenderer}, even if WebGL is available. This option only is available when
 *  using **pixi.js-legacy** or **@pixi/canvas-renderer** packages, otherwise it is ignored.
 * @param {number} [options.height=600] - The height of the renderer's view.
 * @param {string} [options.powerPreference] -
 *  **WebGL Only.** A hint indicating what configuration of GPU is suitable for the WebGL context,
 *  can be `'default'`, `'high-performance'` or `'low-power'`.
 *  Setting to `'high-performance'` will prioritize rendering performance over power consumption,
 *  while setting to `'low-power'` will prioritize power saving over rendering performance.
 * @param {boolean} [options.premultipliedAlpha=true] -
 *  **WebGL Only.** Whether the compositor will assume the drawing buffer contains colors with premultiplied alpha.
 * @param {boolean} [options.preserveDrawingBuffer=false] -
 *  **WebGL Only.** Whether to enable drawing buffer preservation. If enabled, the drawing buffer will preserve
 *  its value until cleared or overwritten. Enable this if you need to call `toDataUrl` on the WebGL context.
 * @param {number} [options.resolution=PIXI.settings.RESOLUTION] -
 *  The resolution / device pixel ratio of the renderer.
 * @param {boolean} [options.transparent] -
 *  **Deprecated since 6.0.0, Use `backgroundAlpha` instead.** \
 *  `true` sets `backgroundAlpha` to `0`, `false` sets `backgroundAlpha` to `1`.
 * @param {boolean|'notMultiplied'} [options.useContextAlpha=true] -
 *  Pass-through value for canvas' context attribute `alpha`. This option is for cases where the
 *  canvas needs to be opaque, possibly for performance reasons on some older devices.
 *  If you want to set transparency, please use `backgroundAlpha`. \
 *  **WebGL Only:** When set to `'notMultiplied'`, the canvas' context attribute `alpha` will be
 *  set to `true` and `premultipliedAlpha` will be to `false`.
 * @param {HTMLCanvasElement} [options.view=null] -
 *  The canvas to use as the view. If omitted, a new canvas will be created.
 * @param {number} [options.width=800] - The width of the renderer's view.
 * @returns {PIXI.Renderer|PIXI.CanvasRenderer}
 *  Returns {@link PIXI.Renderer} if WebGL is available, otherwise {@link PIXI.CanvasRenderer}.
 */
export declare function autoDetectRenderer(options?: IRendererOptionsAuto): AbstractRenderer;

/**
 * Create a resource element from a single source element. This
 * auto-detects which type of resource to create. All resources that
 * are auto-detectable must have a static `test` method and a constructor
 * with the arguments `(source, options?)`. Currently, the supported
 * resources for auto-detection include:
 *  - {@link PIXI.ImageResource}
 *  - {@link PIXI.CanvasResource}
 *  - {@link PIXI.VideoResource}
 *  - {@link PIXI.SVGResource}
 *  - {@link PIXI.BufferResource}
 * @static
 * @memberof PIXI
 * @function autoDetectResource
 * @param {string|*} source - Resource source, this can be the URL to the resource,
 *        a typed-array (for BufferResource), HTMLVideoElement, SVG data-uri
 *        or any other resource that can be auto-detected. If not resource is
 *        detected, it's assumed to be an ImageResource.
 * @param {object} [options] - Pass-through options to use for Resource
 * @param {number} [options.width] - Width of BufferResource or SVG rasterization
 * @param {number} [options.height] - Height of BufferResource or SVG rasterization
 * @param {boolean} [options.autoLoad=true] - Image, SVG and Video flag to start loading
 * @param {number} [options.scale=1] - SVG source scale. Overridden by width, height
 * @param {boolean} [options.createBitmap=PIXI.settings.CREATE_IMAGE_BITMAP] - Image option to create Bitmap object
 * @param {boolean} [options.crossorigin=true] - Image and Video option to set crossOrigin
 * @param {boolean} [options.autoPlay=true] - Video option to start playing video immediately
 * @param {number} [options.updateFPS=0] - Video option to update how many times a second the
 *        texture should be updated from the video. Leave at 0 to update at every render
 * @returns {PIXI.Resource} The created resource.
 */
export declare function autoDetectResource<R extends Resource, RO>(source: unknown, options?: RO): R;

/**
 * Base for all the image/canvas resources.
 * @memberof PIXI
 */
export declare class BaseImageResource extends Resource {
    /**
     * The source element.
     * @member {HTMLImageElement|HTMLCanvasElement|HTMLVideoElement|SVGElement}
     * @readonly
     */
    source: ImageSource;
    /**
     * If set to `true`, will force `texImage2D` over `texSubImage2D` for uploading.
     * Certain types of media (e.g. video) using `texImage2D` is more performant.
     * @default false
     * @private
     */
    noSubImage: boolean;
    /**
     * @param {HTMLImageElement|HTMLCanvasElement|HTMLVideoElement|SVGElement} source
     */
    constructor(source: ImageSource);
    /**
     * Set cross origin based detecting the url and the crossorigin
     * @param element - Element to apply crossOrigin
     * @param url - URL to check
     * @param crossorigin - Cross origin value to use
     */
    static crossOrigin(element: HTMLImageElement | HTMLVideoElement, url: string, crossorigin?: boolean | string): void;
    /**
     * Upload the texture to the GPU.
     * @param renderer - Upload to the renderer
     * @param baseTexture - Reference to parent texture
     * @param glTexture
     * @param {HTMLImageElement|HTMLCanvasElement|HTMLVideoElement|SVGElement} [source] - (optional)
     * @returns - true is success
     */
    upload(renderer: Renderer, baseTexture: BaseTexture, glTexture: GLTexture, source?: ImageSource): boolean;
    /**
     * Checks if source width/height was changed, resize can cause extra baseTexture update.
     * Triggers one update in any case.
     */
    update(): void;
    /** Destroy this {@link BaseImageResource} */
    dispose(): void;
}

export declare interface BaseRenderTexture extends GlobalMixins.BaseRenderTexture, BaseTexture {
}

/**
 * A BaseRenderTexture is a special texture that allows any PixiJS display object to be rendered to it.
 *
 * __Hint__: All DisplayObjects (i.e. Sprites) that render to a BaseRenderTexture should be preloaded
 * otherwise black rectangles will be drawn instead.
 *
 * A BaseRenderTexture takes a snapshot of any Display Object given to its render method. The position
 * and rotation of the given Display Objects is ignored. For example:
 *
 * ```js
 * let renderer = PIXI.autoDetectRenderer();
 * let baseRenderTexture = new PIXI.BaseRenderTexture({ width: 800, height: 600 });
 * let renderTexture = new PIXI.RenderTexture(baseRenderTexture);
 * let sprite = PIXI.Sprite.from("spinObj_01.png");
 *
 * sprite.position.x = 800/2;
 * sprite.position.y = 600/2;
 * sprite.anchor.x = 0.5;
 * sprite.anchor.y = 0.5;
 *
 * renderer.render(sprite, {renderTexture});
 * ```
 *
 * The Sprite in this case will be rendered using its local transform. To render this sprite at 0,0
 * you can clear the transform
 *
 * ```js
 *
 * sprite.setTransform()
 *
 * let baseRenderTexture = new PIXI.BaseRenderTexture({ width: 100, height: 100 });
 * let renderTexture = new PIXI.RenderTexture(baseRenderTexture);
 *
 * renderer.render(sprite, {renderTexture});  // Renders to center of RenderTexture
 * ```
 * @memberof PIXI
 */
export declare class BaseRenderTexture extends BaseTexture {
    clearColor: number[];
    framebuffer: Framebuffer;
    /** The data structure for the stencil masks. */
    maskStack: Array<MaskData>;
    /** The data structure for the filters. */
    filterStack: Array<any>;
    /**
     * @param options
     * @param {number} [options.width=100] - The width of the base render texture.
     * @param {number} [options.height=100] - The height of the base render texture.
     * @param {PIXI.SCALE_MODES} [options.scaleMode=PIXI.settings.SCALE_MODE] - See {@link PIXI.SCALE_MODES}
     *   for possible values.
     * @param {number} [options.resolution=PIXI.settings.RESOLUTION] - The resolution / device pixel ratio
     *   of the texture being generated.
     * @param {PIXI.MSAA_QUALITY} [options.multisample=PIXI.MSAA_QUALITY.NONE] - The number of samples of the frame buffer.
     */
    constructor(options?: IBaseTextureOptions);
    /**
     * Resizes the BaseRenderTexture.
     * @param desiredWidth - The desired width to resize to.
     * @param desiredHeight - The desired height to resize to.
     */
    resize(desiredWidth: number, desiredHeight: number): void;
    /**
     * Frees the texture and framebuffer from WebGL memory without destroying this texture object.
     * This means you can still use the texture later which will upload it to GPU
     * memory again.
     * @fires PIXI.BaseTexture#dispose
     */
    dispose(): void;
    /** Destroys this texture. */
    destroy(): void;
}

export declare interface BaseTexture extends GlobalMixins.BaseTexture, EventEmitter {
}

/**
 * A Texture stores the information that represents an image.
 * All textures have a base texture, which contains information about the source.
 * Therefore you can have many textures all using a single BaseTexture
 * @memberof PIXI
 * @typeParam R - The BaseTexture's Resource type.
 * @typeParam RO - The options for constructing resource.
 */
export declare class BaseTexture<R extends Resource = Resource, RO = IAutoDetectOptions> extends EventEmitter {
    /**
     * The width of the base texture set when the image has loaded
     * @readonly
     */
    width: number;
    /**
     * The height of the base texture set when the image has loaded
     * @readonly
     */
    height: number;
    /**
     * The resolution / device pixel ratio of the texture
     * @readonly
     * @default PIXI.settings.RESOLUTION
     */
    resolution: number;
    /**
     * How to treat premultiplied alpha, see {@link PIXI.ALPHA_MODES}.
     * @member {PIXI.ALPHA_MODES}
     * @default PIXI.ALPHA_MODES.UNPACK
     */
    alphaMode?: ALPHA_MODES;
    /**
     * Anisotropic filtering level of texture
     * @member {number}
     * @default PIXI.settings.ANISOTROPIC_LEVEL
     */
    anisotropicLevel?: number;
    /**
     * The pixel format of the texture
     * @default PIXI.FORMATS.RGBA
     */
    format?: FORMATS;
    /**
     * The type of resource data
     * @default PIXI.TYPES.UNSIGNED_BYTE
     */
    type?: TYPES;
    /**
     * The target type
     * @default PIXI.TARGETS.TEXTURE_2D
     */
    target?: TARGETS;
    /**
     * Global unique identifier for this BaseTexture
     * @protected
     */
    readonly uid: number;
    /**
     * Used by automatic texture Garbage Collection, stores last GC tick when it was bound
     * @protected
     */
    touched: number;
    /**
     * Whether or not the texture is a power of two, try to use power of two textures as much
     * as you can
     * @readonly
     * @default false
     */
    isPowerOfTwo: boolean;
    /**
     * The map of render context textures where this is bound
     * @private
     */
    _glTextures: {
        [key: number]: GLTexture;
    };
    /**
     * Used by TextureSystem to only update texture to the GPU when needed.
     * Please call `update()` to increment it.
     * @readonly
     */
    dirtyId: number;
    /**
     * Used by TextureSystem to only update texture style when needed.
     * @protected
     */
    dirtyStyleId: number;
    /**
     * Currently default cache ID.
     * @member {string}
     */
    cacheId: string;
    /**
     * Generally speaking means when resource is loaded.
     * @readonly
     * @member {boolean}
     */
    valid: boolean;
    /**
     * The collection of alternative cache ids, since some BaseTextures
     * can have more than one ID, short name and longer full URL
     * @member {Array<string>}
     * @readonly
     */
    textureCacheIds: Array<string>;
    /**
     * Flag if BaseTexture has been destroyed.
     * @member {boolean}
     * @readonly
     */
    destroyed: boolean;
    /**
     * The resource used by this BaseTexture, there can only
     * be one resource per BaseTexture, but textures can share
     * resources.
     * @member {PIXI.Resource}
     * @readonly
     */
    resource: R;
    /**
     * Number of the texture batch, used by multi-texture renderers
     * @member {number}
     */
    _batchEnabled: number;
    /**
     * Location inside texture batch, used by multi-texture renderers
     * @member {number}
     */
    _batchLocation: number;
    /**
     * Whether its a part of another texture, handled by ArrayResource or CubeResource
     * @member {PIXI.BaseTexture}
     */
    parentTextureArray: BaseTexture;
    private _mipmap?;
    private _scaleMode?;
    private _wrapMode?;
    /**
     * @param {PIXI.Resource|string|HTMLImageElement|HTMLCanvasElement|HTMLVideoElement} [resource=null] -
     *        The current resource to use, for things that aren't Resource objects, will be converted
     *        into a Resource.
     * @param options - Collection of options
     * @param {PIXI.MIPMAP_MODES} [options.mipmap=PIXI.settings.MIPMAP_TEXTURES] - If mipmapping is enabled for texture
     * @param {number} [options.anisotropicLevel=PIXI.settings.ANISOTROPIC_LEVEL] - Anisotropic filtering level of texture
     * @param {PIXI.WRAP_MODES} [options.wrapMode=PIXI.settings.WRAP_MODE] - Wrap mode for textures
     * @param {PIXI.SCALE_MODES} [options.scaleMode=PIXI.settings.SCALE_MODE] - Default scale mode, linear, nearest
     * @param {PIXI.FORMATS} [options.format=PIXI.FORMATS.RGBA] - GL format type
     * @param {PIXI.TYPES} [options.type=PIXI.TYPES.UNSIGNED_BYTE] - GL data type
     * @param {PIXI.TARGETS} [options.target=PIXI.TARGETS.TEXTURE_2D] - GL texture target
     * @param {PIXI.ALPHA_MODES} [options.alphaMode=PIXI.ALPHA_MODES.UNPACK] - Pre multiply the image alpha
     * @param {number} [options.width=0] - Width of the texture
     * @param {number} [options.height=0] - Height of the texture
     * @param {number} [options.resolution=PIXI.settings.RESOLUTION] - Resolution of the base texture
     * @param {object} [options.resourceOptions] - Optional resource options,
     *        see {@link PIXI.autoDetectResource autoDetectResource}
     */
    constructor(resource?: R | ImageSource | string | any, options?: IBaseTextureOptions<RO>);
    /**
     * Pixel width of the source of this texture
     * @readonly
     */
    get realWidth(): number;
    /**
     * Pixel height of the source of this texture
     * @readonly
     */
    get realHeight(): number;
    /**
     * Mipmap mode of the texture, affects downscaled images
     * @default PIXI.settings.MIPMAP_TEXTURES
     */
    get mipmap(): MIPMAP_MODES;
    set mipmap(value: MIPMAP_MODES);
    /**
     * The scale mode to apply when scaling this texture
     * @default PIXI.settings.SCALE_MODE
     */
    get scaleMode(): SCALE_MODES;
    set scaleMode(value: SCALE_MODES);
    /**
     * How the texture wraps
     * @default PIXI.settings.WRAP_MODE
     */
    get wrapMode(): WRAP_MODES;
    set wrapMode(value: WRAP_MODES);
    /**
     * Changes style options of BaseTexture
     * @param scaleMode - Pixi scalemode
     * @param mipmap - enable mipmaps
     * @returns - this
     */
    setStyle(scaleMode?: SCALE_MODES, mipmap?: MIPMAP_MODES): this;
    /**
     * Changes w/h/resolution. Texture becomes valid if width and height are greater than zero.
     * @param desiredWidth - Desired visual width
     * @param desiredHeight - Desired visual height
     * @param resolution - Optionally set resolution
     * @returns - this
     */
    setSize(desiredWidth: number, desiredHeight: number, resolution?: number): this;
    /**
     * Sets real size of baseTexture, preserves current resolution.
     * @param realWidth - Full rendered width
     * @param realHeight - Full rendered height
     * @param resolution - Optionally set resolution
     * @returns - this
     */
    setRealSize(realWidth: number, realHeight: number, resolution?: number): this;
    /**
     * Refresh check for isPowerOfTwo texture based on size
     * @private
     */
    protected _refreshPOT(): void;
    /**
     * Changes resolution
     * @param resolution - res
     * @returns - this
     */
    setResolution(resolution: number): this;
    /**
     * Sets the resource if it wasn't set. Throws error if resource already present
     * @param resource - that is managing this BaseTexture
     * @returns - this
     */
    setResource(resource: R): this;
    /** Invalidates the object. Texture becomes valid if width and height are greater than zero. */
    update(): void;
    /**
     * Handle errors with resources.
     * @private
     * @param event - Error event emitted.
     */
    onError(event: ErrorEvent): void;
    /**
     * Destroys this base texture.
     * The method stops if resource doesn't want this texture to be destroyed.
     * Removes texture from all caches.
     */
    destroy(): void;
    /**
     * Frees the texture from WebGL memory without destroying this texture object.
     * This means you can still use the texture later which will upload it to GPU
     * memory again.
     * @fires PIXI.BaseTexture#dispose
     */
    dispose(): void;
    /** Utility function for BaseTexture|Texture cast. */
    castToBaseTexture(): BaseTexture;
    /**
     * Helper function that creates a base texture based on the source you provide.
     * The source can be - image url, image element, canvas element. If the
     * source is an image url or an image element and not in the base texture
     * cache, it will be created and loaded.
     * @static
     * @param {string|string[]|HTMLImageElement|HTMLCanvasElement|SVGElement|HTMLVideoElement} source - The
     *        source to create base texture from.
     * @param options - See {@link PIXI.BaseTexture}'s constructor for options.
     * @param {string} [options.pixiIdPrefix=pixiid] - If a source has no id, this is the prefix of the generated id
     * @param {boolean} [strict] - Enforce strict-mode, see {@link PIXI.settings.STRICT_TEXTURE_CACHE}.
     * @returns {PIXI.BaseTexture} The new base texture.
     */
    static from<R extends Resource = Resource, RO = IAutoDetectOptions>(source: ImageSource | string | string[], options?: IBaseTextureOptions<RO>, strict?: boolean): BaseTexture<R>;
    /**
     * Create a new BaseTexture with a BufferResource from a Float32Array.
     * RGBA values are floats from 0 to 1.
     * @param {Float32Array|Uint8Array} buffer - The optional array to use, if no data
     *        is provided, a new Float32Array is created.
     * @param width - Width of the resource
     * @param height - Height of the resource
     * @param options - See {@link PIXI.BaseTexture}'s constructor for options.
     *        Default properties are different from the constructor's defaults.
     * @param {PIXI.FORMATS} [options.format=PIXI.FORMATS.RGBA] - GL format type
     * @param {PIXI.ALPHA_MODES} [options.alphaMode=PIXI.ALPHA_MODES.NPM] - Image alpha, not premultiplied by default
     * @param {PIXI.SCALE_MODES} [options.scaleMode=PIXI.SCALE_MODES.NEAREST] - Scale mode, pixelating by default
     * @returns - The resulting new BaseTexture
     */
    static fromBuffer(buffer: Float32Array | Uint8Array, width: number, height: number, options?: IBaseTextureOptions): BaseTexture<BufferResource>;
    /**
     * Adds a BaseTexture to the global BaseTextureCache. This cache is shared across the whole PIXI object.
     * @param {PIXI.BaseTexture} baseTexture - The BaseTexture to add to the cache.
     * @param {string} id - The id that the BaseTexture will be stored against.
     */
    static addToCache(baseTexture: BaseTexture, id: string): void;
    /**
     * Remove a BaseTexture from the global BaseTextureCache.
     * @param {string|PIXI.BaseTexture} baseTexture - id of a BaseTexture to be removed, or a BaseTexture instance itself.
     * @returns {PIXI.BaseTexture|null} The BaseTexture that was removed.
     */
    static removeFromCache(baseTexture: string | BaseTexture): BaseTexture | null;
    /** Global number of the texture batch, used by multi-texture renderers. */
    static _globalBatch: number;
}

/**
 * Used by the batcher to draw batches.
 * Each one of these contains all information required to draw a bound geometry.
 * @memberof PIXI
 */
export declare class BatchDrawCall {
    texArray: BatchTextureArray;
    type: DRAW_MODES;
    blend: BLEND_MODES;
    start: number;
    size: number;
    /** Data for uniforms or custom webgl state. */
    data: any;
    constructor();
}

/**
 * Geometry used to batch standard PIXI content (e.g. Mesh, Sprite, Graphics objects).
 * @memberof PIXI
 */
export declare class BatchGeometry extends Geometry {
    /**
     * Buffer used for position, color, texture IDs
     * @protected
     */
    _buffer: Buffer_2;
    /**
     * Index buffer data
     * @protected
     */
    _indexBuffer: Buffer_2;
    /**
     * @param {boolean} [_static=false] - Optimization flag, where `false`
     *        is updated every frame, `true` doesn't change frame-to-frame.
     */
    constructor(_static?: boolean);
}

/** @memberof PIXI */
export declare class BatchPluginFactory {
    /**
     * Create a new BatchRenderer plugin for Renderer. this convenience can provide an easy way
     * to extend BatchRenderer with all the necessary pieces.
     * @example
     * const fragment = `
     * varying vec2 vTextureCoord;
     * varying vec4 vColor;
     * varying float vTextureId;
     * uniform sampler2D uSamplers[%count%];
     *
     * void main(void){
     *     vec4 color;
     *     %forloop%
     *     gl_FragColor = vColor * vec4(color.a - color.rgb, color.a);
     * }
     * `;
     * const InvertBatchRenderer = PIXI.BatchPluginFactory.create({ fragment });
     * PIXI.extensions.add({
     *  name: 'invert',
     *  ref: InvertBatchRenderer,
     *  type: PIXI.ExtensionType.RendererPlugin,
     * });
     * const sprite = new PIXI.Sprite();
     * sprite.pluginName = 'invert';
     * @param {object} [options]
     * @param {string} [options.vertex=PIXI.BatchPluginFactory.defaultVertexSrc] - Vertex shader source
     * @param {string} [options.fragment=PIXI.BatchPluginFactory.defaultFragmentTemplate] - Fragment shader template
     * @param {number} [options.vertexSize=6] - Vertex size
     * @param {object} [options.geometryClass=PIXI.BatchGeometry]
     * @returns {*} New batch renderer plugin
     */
    static create(options?: IBatchFactoryOptions): typeof AbstractBatchRenderer;
    /**
     * The default vertex shader source
     * @readonly
     */
    static get defaultVertexSrc(): string;
    /**
     * The default fragment shader source
     * @readonly
     */
    static get defaultFragmentTemplate(): string;
}

export declare const BatchRenderer: typeof AbstractBatchRenderer;

/**
 * Helper that generates batching multi-texture shader. Use it with your new BatchRenderer
 * @memberof PIXI
 */
export declare class BatchShaderGenerator {
    /** Reference to the vertex shader source. */
    vertexSrc: string;
    /** Reference to the fragment shader template. Must contain "%count%" and "%forloop%". */
    fragTemplate: string;
    programCache: {
        [key: number]: Program;
    };
    defaultGroupCache: {
        [key: number]: UniformGroup;
    };
    /**
     * @param vertexSrc - Vertex shader
     * @param fragTemplate - Fragment shader template
     */
    constructor(vertexSrc: string, fragTemplate: string);
    generateShader(maxTextures: number): Shader;
    generateSampleSrc(maxTextures: number): string;
}

/**
 * System plugin to the renderer to manage batching.
 * @memberof PIXI
 */
export declare class BatchSystem implements ISystem {
    /** An empty renderer. */
    readonly emptyRenderer: ObjectRenderer;
    /** The currently active ObjectRenderer. */
    currentRenderer: ObjectRenderer;
    private renderer;
    /**
     * @param renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    /**
     * Changes the current renderer to the one given in parameter
     * @param objectRenderer - The object renderer to use.
     */
    setObjectRenderer(objectRenderer: ObjectRenderer): void;
    /**
     * This should be called if you wish to do some custom rendering
     * It will basically render anything that may be batched up such as sprites
     */
    flush(): void;
    /** Reset the system to an empty renderer */
    reset(): void;
    /**
     * Handy function for batch renderers: copies bound textures in first maxTextures locations to array
     * sets actual _batchLocation for them
     * @param arr - arr copy destination
     * @param maxTextures - number of copied elements
     */
    copyBoundTextures(arr: BaseTexture[], maxTextures: number): void;
    /**
     * Assigns batch locations to textures in array based on boundTextures state.
     * All textures in texArray should have `_batchEnabled = _batchId`,
     * and their count should be less than `maxTextures`.
     * @param texArray - textures to bound
     * @param boundTextures - current state of bound textures
     * @param batchId - marker for _batchEnabled param of textures in texArray
     * @param maxTextures - number of texture locations to manipulate
     */
    boundArray(texArray: BatchTextureArray, boundTextures: Array<BaseTexture>, batchId: number, maxTextures: number): void;
    /**
     * @ignore
     */
    destroy(): void;
}

/**
 * Used by the batcher to build texture batches.
 * Holds list of textures and their respective locations.
 * @memberof PIXI
 */
export declare class BatchTextureArray {
    /** Inside textures array. */
    elements: BaseTexture_2[];
    /** Respective locations for textures. */
    ids: number[];
    /** Number of filled elements. */
    count: number;
    constructor();
    clear(): void;
}

/**
 * A wrapper for data so that it can be used and uploaded by WebGL
 * @memberof PIXI
 */
declare class Buffer_2 {
    /**
     * The data in the buffer, as a typed array
     * @type {PIXI.IArrayBuffer}
     */
    data: ITypedArray;
    /**
     * The type of buffer this is, one of:
     * + ELEMENT_ARRAY_BUFFER - used as an index buffer
     * + ARRAY_BUFFER - used as an attribute buffer
     * + UNIFORM_BUFFER - used as a uniform buffer (if available)
     */
    type: BUFFER_TYPE;
    static: boolean;
    id: number;
    disposeRunner: Runner;
    /**
     * A map of renderer IDs to webgl buffer
     * @private
     * @type {Object<number, GLBuffer>}
     */
    _glBuffers: {
        [key: number]: GLBuffer;
    };
    _updateID: number;
    /**
     * @param {PIXI.IArrayBuffer} data - the data to store in the buffer.
     * @param _static - `true` for static buffer
     * @param index - `true` for index buffer
     */
    constructor(data?: IArrayBuffer, _static?: boolean, index?: boolean);
    /**
     * Flags this buffer as requiring an upload to the GPU.
     * @param {PIXI.IArrayBuffer|number[]} [data] - the data to update in the buffer.
     */
    update(data?: IArrayBuffer | Array<number>): void;
    /** Disposes WebGL resources that are connected to this geometry. */
    dispose(): void;
    /** Destroys the buffer. */
    destroy(): void;
    /**
     * Flags whether this is an index buffer.
     *
     * Index buffers are of type `ELEMENT_ARRAY_BUFFER`. Note that setting this property to false will make
     * the buffer of type `ARRAY_BUFFER`.
     *
     * For backwards compatibility.
     */
    set index(value: boolean);
    get index(): boolean;
    /**
     * Helper function that creates a buffer based on an array or TypedArray
     * @param {ArrayBufferView | number[]} data - the TypedArray that the buffer will store. If this is a regular Array it will be converted to a Float32Array.
     * @returns - A new Buffer based on the data provided.
     */
    static from(data: IArrayBuffer | number[]): Buffer_2;
}
export { Buffer_2 as Buffer }

/**
 * @interface SharedArrayBuffer
 */
/**
 * Buffer resource with data of typed array.
 * @memberof PIXI
 */
export declare class BufferResource extends Resource {
    /** Source array Cannot be {@code ClampedUint8Array} because it cant be uploaded to WebGL */
    data: Float32Array | Uint8Array | Uint16Array | Int32Array | Uint32Array;
    /**
     * @param source - Source buffer
     * @param options - Options
     * @param {number} options.width - Width of the texture
     * @param {number} options.height - Height of the texture
     */
    constructor(source: Float32Array | Uint8Array | Uint16Array | Int32Array | Uint32Array, options: ISize);
    /**
     * Upload the texture to the GPU.
     * @param renderer - Upload to the renderer
     * @param baseTexture - Reference to parent texture
     * @param glTexture - glTexture
     * @returns - true is success
     */
    upload(renderer: Renderer, baseTexture: BaseTexture, glTexture: GLTexture): boolean;
    /** Destroy and don't use after this. */
    dispose(): void;
    /**
     * Used to auto-detect the type of resource.
     * @param {*} source - The source object
     * @returns {boolean} `true` if <canvas>
     */
    static test(source: unknown): source is Float32Array | Uint8Array | Uint32Array;
}

/**
 * System plugin to the renderer to manage buffers.
 *
 * WebGL uses Buffers as a way to store objects to the GPU.
 * This system makes working with them a lot easier.
 *
 * Buffers are used in three main places in WebGL
 * - geometry information
 * - Uniform information (via uniform buffer objects - a WebGL 2 only feature)
 * - Transform feedback information. (WebGL 2 only feature)
 *
 * This system will handle the binding of buffers to the GPU as well as uploading
 * them. With this system, you never need to work directly with GPU buffers, but instead work with
 * the PIXI.Buffer class.
 * @class
 * @memberof PIXI
 */
declare class BufferSystem implements ISystem {
    CONTEXT_UID: number;
    gl: IRenderingContext;
    /** Cache for all buffers by id, used in case renderer gets destroyed or for profiling */
    readonly managedBuffers: {
        [key: number]: Buffer_2;
    };
    /** Cache keeping track of the base bound buffer bases */
    readonly boundBufferBases: {
        [key: number]: Buffer_2;
    };
    private renderer;
    /**
     * @param {PIXI.Renderer} renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    /**
     * @ignore
     */
    destroy(): void;
    /** Sets up the renderer context and necessary buffers. */
    protected contextChange(): void;
    /**
     * This binds specified buffer. On first run, it will create the webGL buffers for the context too
     * @param buffer - the buffer to bind to the renderer
     */
    bind(buffer: Buffer_2): void;
    /**
     * Binds an uniform buffer to at the given index.
     *
     * A cache is used so a buffer will not be bound again if already bound.
     * @param buffer - the buffer to bind
     * @param index - the base index to bind it to.
     */
    bindBufferBase(buffer: Buffer_2, index: number): void;
    /**
     * Binds a buffer whilst also binding its range.
     * This will make the buffer start from the offset supplied rather than 0 when it is read.
     * @param buffer - the buffer to bind
     * @param index - the base index to bind at, defaults to 0
     * @param offset - the offset to bind at (this is blocks of 256). 0 = 0, 1 = 256, 2 = 512 etc
     */
    bindBufferRange(buffer: Buffer_2, index?: number, offset?: number): void;
    /**
     * Will ensure the data in the buffer is uploaded to the GPU.
     * @param {PIXI.Buffer} buffer - the buffer to update
     */
    update(buffer: Buffer_2): void;
    /**
     * Disposes buffer
     * @param {PIXI.Buffer} buffer - buffer with data
     * @param {boolean} [contextLost=false] - If context was lost, we suppress deleteVertexArray
     */
    dispose(buffer: Buffer_2, contextLost?: boolean): void;
    /**
     * dispose all WebGL resources of all managed buffers
     * @param {boolean} [contextLost=false] - If context was lost, we suppress `gl.delete` calls
     */
    disposeAll(contextLost?: boolean): void;
    /**
     * creates and attaches a GLBuffer object tied to the current context.
     * @param buffer
     * @protected
     */
    protected createGLBuffer(buffer: Buffer_2): GLBuffer;
}

/**
 * @interface OffscreenCanvas
 */
/**
 * Resource type for HTMLCanvasElement.
 * @memberof PIXI
 */
export declare class CanvasResource extends BaseImageResource {
    /**
     * @param source - Canvas element to use
     */
    constructor(source: HTMLCanvasElement);
    /**
     * Used to auto-detect the type of resource.
     * @param {*} source - The source object
     * @returns {boolean} `true` if source is HTMLCanvasElement or OffscreenCanvas
     */
    static test(source: unknown): source is OffscreenCanvas | HTMLCanvasElement;
}

export declare function checkMaxIfStatementsInShader(maxIfs: number, gl: IRenderingContext): number;

/**
 * System plugin to the renderer to manage the context.
 * @memberof PIXI
 */
export declare class ContextSystem implements ISystem {
    /**
     * Either 1 or 2 to reflect the WebGL version being used.
     * @readonly
     */
    webGLVersion: number;
    /**
     * Features supported by current context.
     * @type {object}
     * @readonly
     * @property {boolean} uint32Indices - Support for 32-bit indices buffer.
     */
    readonly supports: ISupportDict;
    protected CONTEXT_UID: number;
    protected gl: IRenderingContext;
    /**
     * Extensions available.
     * @type {object}
     * @readonly
     * @property {WEBGL_draw_buffers} drawBuffers - WebGL v1 extension
     * @property {WEBGL_depth_texture} depthTexture - WebGL v1 extension
     * @property {OES_texture_float} floatTexture - WebGL v1 extension
     * @property {WEBGL_lose_context} loseContext - WebGL v1 extension
     * @property {OES_vertex_array_object} vertexArrayObject - WebGL v1 extension
     * @property {EXT_texture_filter_anisotropic} anisotropicFiltering - WebGL v1 and v2 extension
     */
    extensions: WebGLExtensions;
    private renderer;
    /** @param renderer - The renderer this System works for. */
    constructor(renderer: Renderer);
    /**
     * `true` if the context is lost
     * @readonly
     */
    get isLost(): boolean;
    /**
     * Handles the context change event.
     * @param {WebGLRenderingContext} gl - New WebGL context.
     */
    protected contextChange(gl: IRenderingContext): void;
    /**
     * Initializes the context.
     * @protected
     * @param {WebGLRenderingContext} gl - WebGL context
     */
    initFromContext(gl: IRenderingContext): void;
    /**
     * Initialize from context options
     * @protected
     * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/getContext
     * @param {object} options - context attributes
     */
    initFromOptions(options: WebGLContextAttributes): void;
    /**
     * Helper class to create a WebGL Context
     * @param canvas - the canvas element that we will get the context from
     * @param options - An options object that gets passed in to the canvas element containing the
     *    context attributes
     * @see https://developer.mozilla.org/en/docs/Web/API/HTMLCanvasElement/getContext
     * @returns {WebGLRenderingContext} the WebGL context
     */
    createContext(canvas: HTMLCanvasElement, options: WebGLContextAttributes): IRenderingContext;
    /** Auto-populate the {@link PIXI.ContextSystem.extensions extensions}. */
    protected getExtensions(): void;
    /**
     * Handles a lost webgl context
     * @param {WebGLContextEvent} event - The context lost event.
     */
    protected handleContextLost(event: WebGLContextEvent): void;
    /** Handles a restored webgl context. */
    protected handleContextRestored(): void;
    destroy(): void;
    /** Handle the post-render runner event. */
    protected postrender(): void;
    /**
     * Validate context.
     * @param {WebGLRenderingContext} gl - Render context.
     */
    protected validateContext(gl: IRenderingContext): void;
}

/**
 * logic originally from here: https://github.com/sketchpunk/FunWithWebGL2/blob/master/lesson_022/Shaders.js
 * rewrote it, but this was a great starting point to get a solid understanding of whats going on :)
 * @ignore
 * @param uniformData
 */
export declare function createUBOElements(uniformData: IUniformData[]): {
    uboElements: UBOElement[];
    size: number;
};

/**
 * Resource for a CubeTexture which contains six resources.
 * @memberof PIXI
 */
export declare class CubeResource extends AbstractMultiResource {
    items: ArrayFixed<BaseTexture, 6>;
    /**
     * In case BaseTextures are supplied, whether to use same resource or bind baseTexture itself.
     * @protected
     */
    linkBaseTexture: boolean;
    /**
     * @param {Array<string|PIXI.Resource>} [source] - Collection of URLs or resources
     *        to use as the sides of the cube.
     * @param options - ImageResource options
     * @param {number} [options.width] - Width of resource
     * @param {number} [options.height] - Height of resource
     * @param {number} [options.autoLoad=true] - Whether to auto-load resources
     * @param {number} [options.linkBaseTexture=true] - In case BaseTextures are supplied,
     *   whether to copy them or use
     */
    constructor(source?: ArrayFixed<string | Resource, 6>, options?: ICubeResourceOptions);
    /**
     * Add binding.
     * @param baseTexture - parent base texture
     */
    bind(baseTexture: BaseTexture): void;
    addBaseTextureAt(baseTexture: BaseTexture, index: number, linkBaseTexture?: boolean): this;
    /**
     * Upload the resource
     * @param renderer
     * @param _baseTexture
     * @param glTexture
     * @returns {boolean} true is success
     */
    upload(renderer: Renderer, _baseTexture: BaseTexture, glTexture: GLTexture): boolean;
    /** Number of texture sides to store for CubeResources. */
    static SIDES: number;
    /**
     * Used to auto-detect the type of resource.
     * @param {*} source - The source object
     * @returns {boolean} `true` if source is an array of 6 elements
     */
    static test(source: unknown): source is ArrayFixed<string | Resource, 6>;
}

export declare const defaultFilterVertex: string;

/**
 * Default filter vertex shader
 * @memberof PIXI
 * @member {string} defaultFilterVertex
 */
export declare const defaultVertex: string;

/**
 * A filter is a special shader that applies post-processing effects to an input texture and writes into an output
 * render-target.
 *
 * {@link http://pixijs.io/examples/#/filters/blur-filter.js Example} of the
 * {@link PIXI.filters.BlurFilter BlurFilter}.
 *
 * ### Usage
 * Filters can be applied to any DisplayObject or Container.
 * PixiJS' `FilterSystem` renders the container into temporary Framebuffer,
 * then filter renders it to the screen.
 * Multiple filters can be added to the `filters` array property and stacked on each other.
 *
 * ```
 * const filter = new PIXI.Filter(myShaderVert, myShaderFrag, { myUniform: 0.5 });
 * const container = new PIXI.Container();
 * container.filters = [filter];
 * ```
 *
 * ### Previous Version Differences
 *
 * In PixiJS **v3**, a filter was always applied to _whole screen_.
 *
 * In PixiJS **v4**, a filter can be applied _only part of the screen_.
 * Developers had to create a set of uniforms to deal with coordinates.
 *
 * In PixiJS **v5** combines _both approaches_.
 * Developers can use normal coordinates of v3 and then allow filter to use partial Framebuffers,
 * bringing those extra uniforms into account.
 *
 * Also be aware that we have changed default vertex shader, please consult
 * {@link https://github.com/pixijs/pixi.js/wiki/v5-Creating-filters Wiki}.
 *
 * ### Frames
 *
 * The following table summarizes the coordinate spaces used in the filtering pipeline:
 *
 * <table>
 * <thead>
 *   <tr>
 *     <th>Coordinate Space</th>
 *     <th>Description</th>
 *   </tr>
 * </thead>
 * <tbody>
 *   <tr>
 *     <td>Texture Coordinates</td>
 *     <td>
 *         The texture (or UV) coordinates in the input base-texture's space. These are normalized into the (0,1) range along
 *         both axes.
 *     </td>
 *   </tr>
 *   <tr>
 *     <td>World Space</td>
 *     <td>
 *         A point in the same space as the world bounds of any display-object (i.e. in the scene graph's space).
 *     </td>
 *   </tr>
 *   <tr>
 *     <td>Physical Pixels</td>
 *     <td>
 *         This is base-texture's space with the origin on the top-left. You can calculate these by multiplying the texture
 *         coordinates by the dimensions of the texture.
 *     </td>
 *   </tr>
 * </tbody>
 * </table>
 *
 * ### Built-in Uniforms
 *
 * PixiJS viewport uses screen (CSS) coordinates, `(0, 0, renderer.screen.width, renderer.screen.height)`,
 * and `projectionMatrix` uniform maps it to the gl viewport.
 *
 * **uSampler**
 *
 * The most important uniform is the input texture that container was rendered into.
 * _Important note: as with all Framebuffers in PixiJS, both input and output are
 * premultiplied by alpha._
 *
 * By default, input normalized coordinates are passed to fragment shader with `vTextureCoord`.
 * Use it to sample the input.
 *
 * ```
 * const fragment = `
 * varying vec2 vTextureCoord;
 * uniform sampler2D uSampler;
 * void main(void)
 * {
 *    gl_FragColor = texture2D(uSampler, vTextureCoord);
 * }
 * `;
 *
 * const myFilter = new PIXI.Filter(null, fragment);
 * ```
 *
 * This filter is just one uniform less than {@link PIXI.filters.AlphaFilter AlphaFilter}.
 *
 * **outputFrame**
 *
 * The `outputFrame` holds the rectangle where filter is applied in screen (CSS) coordinates.
 * It's the same as `renderer.screen` for a fullscreen filter.
 * Only a part of  `outputFrame.zw` size of temporary Framebuffer is used,
 * `(0, 0, outputFrame.width, outputFrame.height)`,
 *
 * Filters uses this quad to normalized (0-1) space, its passed into `aVertexPosition` attribute.
 * To calculate vertex position in screen space using normalized (0-1) space:
 *
 * ```
 * vec4 filterVertexPosition( void )
 * {
 *     vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;
 *     return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
 * }
 * ```
 *
 * **inputSize**
 *
 * Temporary framebuffer is different, it can be either the size of screen, either power-of-two.
 * The `inputSize.xy` are size of temporary framebuffer that holds input.
 * The `inputSize.zw` is inverted, it's a shortcut to evade division inside the shader.
 *
 * Set `inputSize.xy = outputFrame.zw` for a fullscreen filter.
 *
 * To calculate input normalized coordinate, you have to map it to filter normalized space.
 * Multiply by `outputFrame.zw` to get input coordinate.
 * Divide by `inputSize.xy` to get input normalized coordinate.
 *
 * ```
 * vec2 filterTextureCoord( void )
 * {
 *     return aVertexPosition * (outputFrame.zw * inputSize.zw); // same as /inputSize.xy
 * }
 * ```
 * **resolution**
 *
 * The `resolution` is the ratio of screen (CSS) pixels to real pixels.
 *
 * **inputPixel**
 *
 * `inputPixel.xy` is the size of framebuffer in real pixels, same as `inputSize.xy * resolution`
 * `inputPixel.zw` is inverted `inputPixel.xy`.
 *
 * It's handy for filters that use neighbour pixels, like {@link PIXI.filters.FXAAFilter FXAAFilter}.
 *
 * **inputClamp**
 *
 * If you try to get info from outside of used part of Framebuffer - you'll get undefined behaviour.
 * For displacements, coordinates has to be clamped.
 *
 * The `inputClamp.xy` is left-top pixel center, you may ignore it, because we use left-top part of Framebuffer
 * `inputClamp.zw` is bottom-right pixel center.
 *
 * ```
 * vec4 color = texture2D(uSampler, clamp(modifiedTextureCoord, inputClamp.xy, inputClamp.zw))
 * ```
 * OR
 * ```
 * vec4 color = texture2D(uSampler, min(modifigedTextureCoord, inputClamp.zw))
 * ```
 *
 * ### Additional Information
 *
 * Complete documentation on Filter usage is located in the
 * {@link https://github.com/pixijs/pixi.js/wiki/v5-Creating-filters Wiki}.
 *
 * Since PixiJS only had a handful of built-in filters, additional filters can be downloaded
 * {@link https://github.com/pixijs/pixi-filters here} from the PixiJS Filters repository.
 * @memberof PIXI
 */
export declare class Filter extends Shader {
    /**
     * The padding of the filter. Some filters require extra space to breath such as a blur.
     * Increasing this will add extra width and height to the bounds of the object that the
     * filter is applied to.
     */
    padding: number;
    /** The samples of the filter. */
    multisample: MSAA_QUALITY;
    /** If enabled is true the filter is applied, if false it will not. */
    enabled: boolean;
    /**
     * If enabled, PixiJS will fit the filter area into boundaries for better performance.
     * Switch it off if it does not work for specific shader.
     * @default true
     */
    autoFit: boolean;
    /**
     * Legacy filters use position and uvs from attributes (set by filter system)
     * @readonly
     */
    legacy: boolean;
    /** The WebGL state the filter requires to render. */
    state: State;
    protected _resolution: number;
    /**
     * @param vertexSrc - The source of the vertex shader.
     * @param fragmentSrc - The source of the fragment shader.
     * @param uniforms - Custom uniforms to use to augment the built-in ones.
     */
    constructor(vertexSrc?: string, fragmentSrc?: string, uniforms?: Dict<any>);
    /**
     * Applies the filter
     * @param {PIXI.FilterSystem} filterManager - The renderer to retrieve the filter from
     * @param {PIXI.RenderTexture} input - The input render target.
     * @param {PIXI.RenderTexture} output - The target to output to.
     * @param {PIXI.CLEAR_MODES} [clearMode] - Should the output be cleared before rendering to it.
     * @param {object} [_currentState] - It's current state of filter.
     *        There are some useful properties in the currentState :
     *        target, filters, sourceFrame, destinationFrame, renderTarget, resolution
     */
    apply(filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode?: CLEAR_MODES, _currentState?: FilterState): void;
    /**
     * Sets the blend mode of the filter.
     * @default PIXI.BLEND_MODES.NORMAL
     */
    get blendMode(): BLEND_MODES;
    set blendMode(value: BLEND_MODES);
    /**
     * The resolution of the filter. Setting this to be lower will lower the quality but
     * increase the performance of the filter.
     */
    get resolution(): number;
    set resolution(value: number);
    /**
     * The default vertex shader source
     * @constant
     */
    static get defaultVertexSrc(): string;
    /**
     * The default fragment shader source
     * @constant
     */
    static get defaultFragmentSrc(): string;
    /** Used for caching shader IDs. */
    static SOURCE_KEY_MAP: Dict<string>;
}

/**
 * System plugin to the renderer to manage filter states.
 * @ignore
 */
export declare class FilterState {
    renderTexture: RenderTexture;
    /**
     * Target of the filters
     * We store for case when custom filter wants to know the element it was applied on
     * @member {PIXI.DisplayObject}
     */
    target: IFilterTarget;
    /**
     * Compatibility with PixiJS v4 filters
     * @default false
     */
    legacy: boolean;
    /**
     * Resolution of filters
     * @default 1
     */
    resolution: number;
    /**
     * Number of samples
     * @default MSAA_QUALITY.NONE
     */
    multisample: MSAA_QUALITY;
    /** Source frame. */
    sourceFrame: Rectangle;
    /** Destination frame. */
    destinationFrame: Rectangle;
    /** Original render-target source frame. */
    bindingSourceFrame: Rectangle;
    /** Original render-target destination frame. */
    bindingDestinationFrame: Rectangle;
    /** Collection of filters. */
    filters: Array<Filter>;
    /** Projection system transform saved by link. */
    transform: Matrix;
    constructor();
    /** Clears the state */
    clear(): void;
}

/**
 * System plugin to the renderer to manage filters.
 *
 * ## Pipeline
 *
 * The FilterSystem executes the filtering pipeline by rendering the display-object into a texture, applying its
 * [filters]{@link PIXI.Filter} in series, and the last filter outputs into the final render-target.
 *
 * The filter-frame is the rectangle in world space being filtered, and those contents are mapped into
 * `(0, 0, filterFrame.width, filterFrame.height)` into the filter render-texture. The filter-frame is also called
 * the source-frame, as it is used to bind the filter render-textures. The last filter outputs to the `filterFrame`
 * in the final render-target.
 *
 * ## Usage
 *
 * {@link PIXI.Container#renderAdvanced} is an example of how to use the filter system. It is a 3 step process:
 *
 * **push**: Use {@link PIXI.FilterSystem#push} to push the set of filters to be applied on a filter-target.
 * **render**: Render the contents to be filtered using the renderer. The filter-system will only capture the contents
 *      inside the bounds of the filter-target. NOTE: Using {@link PIXI.Renderer#render} is
 *      illegal during an existing render cycle, and it may reset the filter system.
 * **pop**: Use {@link PIXI.FilterSystem#pop} to pop & execute the filters you initially pushed. It will apply them
 *      serially and output to the bounds of the filter-target.
 * @memberof PIXI
 */
export declare class FilterSystem implements ISystem {
    /**
     * List of filters for the FilterSystem
     * @member {object[]}
     */
    readonly defaultFilterStack: Array<FilterState>;
    /** A pool for storing filter states, save us creating new ones each tick. */
    statePool: Array<FilterState>;
    /** Stores a bunch of POT textures used for filtering. */
    texturePool: RenderTexturePool;
    /** Whether to clear output renderTexture in AUTO/BLIT mode. See {@link PIXI.CLEAR_MODES}. */
    forceClear: boolean;
    /**
     * Old padding behavior is to use the max amount instead of sum padding.
     * Use this flag if you need the old behavior.
     * @default false
     */
    useMaxPadding: boolean;
    /** A very simple geometry used when drawing a filter effect to the screen. */
    protected quad: Quad;
    /** Quad UVs */
    protected quadUv: QuadUv;
    /**
     * Active state
     * @member {object}
     */
    protected activeState: FilterState;
    /**
     * This uniform group is attached to filter uniforms when used.
     * @property {PIXI.Rectangle} outputFrame -
     * @property {Float32Array} inputSize -
     * @property {Float32Array} inputPixel -
     * @property {Float32Array} inputClamp -
     * @property {number} resolution -
     * @property {Float32Array} filterArea -
     * @property {Float32Array} filterClamp -
     */
    protected globalUniforms: UniformGroup;
    /** Temporary rect for math. */
    private tempRect;
    renderer: Renderer;
    /**
     * @param renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    /**
     * Pushes a set of filters to be applied later to the system. This will redirect further rendering into an
     * input render-texture for the rest of the filtering pipeline.
     * @param {PIXI.DisplayObject} target - The target of the filter to render.
     * @param filters - The filters to apply.
     */
    push(target: IFilterTarget, filters: Array<Filter>): void;
    /** Pops off the filter and applies it. */
    pop(): void;
    /**
     * Binds a renderTexture with corresponding `filterFrame`, clears it if mode corresponds.
     * @param filterTexture - renderTexture to bind, should belong to filter pool or filter stack
     * @param clearMode - clearMode, by default its CLEAR/YES. See {@link PIXI.CLEAR_MODES}
     */
    bindAndClear(filterTexture: RenderTexture, clearMode?: CLEAR_MODES): void;
    /**
     * Draws a filter using the default rendering process.
     *
     * This should be called only by {@link Filter#apply}.
     * @param filter - The filter to draw.
     * @param input - The input render target.
     * @param output - The target to output to.
     * @param clearMode - Should the output be cleared before rendering to it
     */
    applyFilter(filter: Filter, input: RenderTexture, output: RenderTexture, clearMode?: CLEAR_MODES): void;
    /**
     * Multiply _input normalized coordinates_ to this matrix to get _sprite texture normalized coordinates_.
     *
     * Use `outputMatrix * vTextureCoord` in the shader.
     * @param outputMatrix - The matrix to output to.
     * @param {PIXI.Sprite} sprite - The sprite to map to.
     * @returns The mapped matrix.
     */
    calculateSpriteMatrix(outputMatrix: Matrix, sprite: ISpriteMaskTarget): Matrix;
    /** Destroys this Filter System. */
    destroy(): void;
    /**
     * Gets a Power-of-Two render texture or fullScreen texture
     * @param minWidth - The minimum width of the render texture in real pixels.
     * @param minHeight - The minimum height of the render texture in real pixels.
     * @param resolution - The resolution of the render texture.
     * @param multisample - Number of samples of the render texture.
     * @returns - The new render texture.
     */
    protected getOptimalFilterTexture(minWidth: number, minHeight: number, resolution?: number, multisample?: MSAA_QUALITY): RenderTexture;
    /**
     * Gets extra render texture to use inside current filter
     * To be compliant with older filters, you can use params in any order
     * @param input - renderTexture from which size and resolution will be copied
     * @param resolution - override resolution of the renderTexture
     * @param multisample - number of samples of the renderTexture
     */
    getFilterTexture(input?: RenderTexture, resolution?: number, multisample?: MSAA_QUALITY): RenderTexture;
    /**
     * Frees a render texture back into the pool.
     * @param renderTexture - The renderTarget to free
     */
    returnFilterTexture(renderTexture: RenderTexture): void;
    /** Empties the texture pool. */
    emptyPool(): void;
    /** Calls `texturePool.resize()`, affects fullScreen renderTextures. */
    resize(): void;
    /**
     * @param matrix - first param
     * @param rect - second param
     */
    private transformAABB;
    private roundFrame;
}

/**
 * A framebuffer can be used to render contents off of the screen. {@link PIXI.BaseRenderTexture} uses
 * one internally to render into itself. You can attach a depth or stencil buffer to a framebuffer.
 *
 * On WebGL 2 machines, shaders can output to multiple textures simultaneously with GLSL 300 ES.
 * @memberof PIXI
 */
export declare class Framebuffer {
    /** Width of framebuffer in pixels. */
    width: number;
    /** Height of framebuffer in pixels. */
    height: number;
    /**
     * Desired number of samples for antialiasing. 0 means AA should not be used.
     *
     * Experimental WebGL2 feature, allows to use antialiasing in individual renderTextures.
     * Antialiasing is the same as for main buffer with renderer `antialias:true` options.
     * Seriously affects GPU memory consumption and GPU performance.
     *
     *```js
     * renderTexture.framebuffer.multisample = PIXI.MSAA_QUALITY.HIGH;
     * //...
     * renderer.render(myContainer, {renderTexture});
     * renderer.framebuffer.blit(); // copies data from MSAA framebuffer to texture
     *  ```
     * @default PIXI.MSAA_QUALITY.NONE
     */
    multisample: MSAA_QUALITY;
    stencil: boolean;
    depth: boolean;
    dirtyId: number;
    dirtyFormat: number;
    dirtySize: number;
    depthTexture: BaseTexture;
    colorTextures: Array<BaseTexture>;
    glFramebuffers: {
        [key: string]: GLFramebuffer;
    };
    disposeRunner: Runner;
    /**
     * @param width - Width of the frame buffer
     * @param height - Height of the frame buffer
     */
    constructor(width: number, height: number);
    /**
     * Reference to the colorTexture.
     * @readonly
     */
    get colorTexture(): BaseTexture;
    /**
     * Add texture to the colorTexture array.
     * @param index - Index of the array to add the texture to
     * @param texture - Texture to add to the array
     */
    addColorTexture(index?: number, texture?: BaseTexture): this;
    /**
     * Add a depth texture to the frame buffer.
     * @param texture - Texture to add.
     */
    addDepthTexture(texture?: BaseTexture): this;
    /** Enable depth on the frame buffer. */
    enableDepth(): this;
    /** Enable stencil on the frame buffer. */
    enableStencil(): this;
    /**
     * Resize the frame buffer
     * @param width - Width of the frame buffer to resize to
     * @param height - Height of the frame buffer to resize to
     */
    resize(width: number, height: number): void;
    /** Disposes WebGL resources that are connected to this geometry. */
    dispose(): void;
    /** Destroys and removes the depth texture added to this framebuffer. */
    destroyDepthTexture(): void;
}

/**
 * System plugin to the renderer to manage framebuffers.
 * @memberof PIXI
 */
export declare class FramebufferSystem implements ISystem {
    /** A list of managed framebuffers. */
    readonly managedFramebuffers: Array<Framebuffer>;
    current: Framebuffer;
    viewport: Rectangle;
    hasMRT: boolean;
    writeDepthTexture: boolean;
    protected CONTEXT_UID: number;
    protected gl: IRenderingContext;
    /** Framebuffer value that shows that we don't know what is bound. */
    protected unknownFramebuffer: Framebuffer;
    protected msaaSamples: Array<number>;
    renderer: Renderer;
    /**
     * @param renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    /** Sets up the renderer context and necessary buffers. */
    protected contextChange(): void;
    /**
     * Bind a framebuffer.
     * @param framebuffer
     * @param frame - frame, default is framebuffer size
     * @param mipLevel - optional mip level to set on the framebuffer - defaults to 0
     */
    bind(framebuffer?: Framebuffer, frame?: Rectangle, mipLevel?: number): void;
    /**
     * Set the WebGLRenderingContext's viewport.
     * @param x - X position of viewport
     * @param y - Y position of viewport
     * @param width - Width of viewport
     * @param height - Height of viewport
     */
    setViewport(x: number, y: number, width: number, height: number): void;
    /**
     * Get the size of the current width and height. Returns object with `width` and `height` values.
     * @readonly
     */
    get size(): {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    /**
     * Clear the color of the context
     * @param r - Red value from 0 to 1
     * @param g - Green value from 0 to 1
     * @param b - Blue value from 0 to 1
     * @param a - Alpha value from 0 to 1
     * @param {PIXI.BUFFER_BITS} [mask=BUFFER_BITS.COLOR | BUFFER_BITS.DEPTH] - Bitwise OR of masks
     *  that indicate the buffers to be cleared, by default COLOR and DEPTH buffers.
     */
    clear(r: number, g: number, b: number, a: number, mask?: BUFFER_BITS): void;
    /**
     * Initialize framebuffer for this context
     * @protected
     * @param framebuffer
     * @returns - created GLFramebuffer
     */
    initFramebuffer(framebuffer: Framebuffer): GLFramebuffer;
    /**
     * Resize the framebuffer
     * @param framebuffer
     * @protected
     */
    resizeFramebuffer(framebuffer: Framebuffer): void;
    /**
     * Update the framebuffer
     * @param framebuffer
     * @param mipLevel
     * @protected
     */
    updateFramebuffer(framebuffer: Framebuffer, mipLevel: number): void;
    /**
     * Returns true if the frame buffer can be multisampled.
     * @param framebuffer
     */
    protected canMultisampleFramebuffer(framebuffer: Framebuffer): boolean;
    /**
     * Detects number of samples that is not more than a param but as close to it as possible
     * @param samples - number of samples
     * @returns - recommended number of samples
     */
    protected detectSamples(samples: MSAA_QUALITY): MSAA_QUALITY;
    /**
     * Only works with WebGL2
     *
     * blits framebuffer to another of the same or bigger size
     * after that target framebuffer is bound
     *
     * Fails with WebGL warning if blits multisample framebuffer to different size
     * @param framebuffer - by default it blits "into itself", from renderBuffer to texture.
     * @param sourcePixels - source rectangle in pixels
     * @param destPixels - dest rectangle in pixels, assumed to be the same as sourcePixels
     */
    blit(framebuffer?: Framebuffer, sourcePixels?: Rectangle, destPixels?: Rectangle): void;
    /**
     * Disposes framebuffer.
     * @param framebuffer - framebuffer that has to be disposed of
     * @param contextLost - If context was lost, we suppress all delete function calls
     */
    disposeFramebuffer(framebuffer: Framebuffer, contextLost?: boolean): void;
    /**
     * Disposes all framebuffers, but not textures bound to them.
     * @param [contextLost=false] - If context was lost, we suppress all delete function calls
     */
    disposeAll(contextLost?: boolean): void;
    /**
     * Forcing creation of stencil buffer for current framebuffer, if it wasn't done before.
     * Used by MaskSystem, when its time to use stencil mask for Graphics element.
     *
     * Its an alternative for public lazy `framebuffer.enableStencil`, in case we need stencil without rebind.
     * @private
     */
    forceStencil(): void;
    /** Resets framebuffer stored state, binds screen framebuffer. Should be called before renderTexture reset(). */
    reset(): void;
    destroy(): void;
}

/**
 * generates a WebGL Program object from a high level Pixi Program.
 * @param gl - a rendering context on which to generate the program
 * @param program - the high level Pixi Program.
 */
export declare function generateProgram(gl: IRenderingContext, program: Program): GLProgram;

export declare function generateUniformBufferSync(group: UniformGroup, uniformData: Dict<any>): {
    size: number;
    syncFunc: UniformsSyncCallback;
};

/**
 * The Geometry represents a model. It consists of two components:
 * - GeometryStyle - The structure of the model such as the attributes layout
 * - GeometryData - the data of the model - this consists of buffers.
 * This can include anything from positions, uvs, normals, colors etc.
 *
 * Geometry can be defined without passing in a style or data if required (thats how I prefer!)
 *
 * ```js
 * let geometry = new PIXI.Geometry();
 *
 * geometry.addAttribute('positions', [0, 0, 100, 0, 100, 100, 0, 100], 2);
 * geometry.addAttribute('uvs', [0,0,1,0,1,1,0,1],2)
 * geometry.addIndex([0,1,2,1,3,2])
 * ```
 * @memberof PIXI
 */
export declare class Geometry {
    buffers: Array<Buffer_2>;
    indexBuffer: Buffer_2;
    attributes: {
        [key: string]: Attribute;
    };
    id: number;
    /** Whether the geometry is instanced. */
    instanced: boolean;
    /**
     * Number of instances in this geometry, pass it to `GeometrySystem.draw()`.
     * @default 1
     */
    instanceCount: number;
    /**
     * A map of renderer IDs to webgl VAOs
     * @type {object}
     */
    glVertexArrayObjects: {
        [key: number]: {
            [key: string]: WebGLVertexArrayObject;
        };
    };
    disposeRunner: Runner;
    /** Count of existing (not destroyed) meshes that reference this geometry. */
    refCount: number;
    /**
     * @param buffers - An array of buffers. optional.
     * @param attributes - Of the geometry, optional structure of the attributes layout
     */
    constructor(buffers?: Array<Buffer_2>, attributes?: {
        [key: string]: Attribute;
    });
    /**
     *
     * Adds an attribute to the geometry
     * Note: `stride` and `start` should be `undefined` if you dont know them, not 0!
     * @param id - the name of the attribute (matching up to a shader)
     * @param {PIXI.Buffer|number[]} buffer - the buffer that holds the data of the attribute . You can also provide an Array and a buffer will be created from it.
     * @param size - the size of the attribute. If you have 2 floats per vertex (eg position x and y) this would be 2
     * @param normalized - should the data be normalized.
     * @param [type=PIXI.TYPES.FLOAT] - what type of number is the attribute. Check {PIXI.TYPES} to see the ones available
     * @param [stride=0] - How far apart, in bytes, the start of each value is. (used for interleaving data)
     * @param [start=0] - How far into the array to start reading values (used for interleaving data)
     * @param instance - Instancing flag
     * @returns - Returns self, useful for chaining.
     */
    addAttribute(id: string, buffer: Buffer_2 | Float32Array | Uint32Array | Array<number>, size?: number, normalized?: boolean, type?: TYPES, stride?: number, start?: number, instance?: boolean): this;
    /**
     * Returns the requested attribute.
     * @param id - The name of the attribute required
     * @returns - The attribute requested.
     */
    getAttribute(id: string): Attribute;
    /**
     * Returns the requested buffer.
     * @param id - The name of the buffer required.
     * @returns - The buffer requested.
     */
    getBuffer(id: string): Buffer_2;
    /**
     *
     * Adds an index buffer to the geometry
     * The index buffer contains integers, three for each triangle in the geometry, which reference the various attribute buffers (position, colour, UV coordinates, other UV coordinates, normal, …). There is only ONE index buffer.
     * @param {PIXI.Buffer|number[]} [buffer] - The buffer that holds the data of the index buffer. You can also provide an Array and a buffer will be created from it.
     * @returns - Returns self, useful for chaining.
     */
    addIndex(buffer?: Buffer_2 | IArrayBuffer | number[]): Geometry;
    /**
     * Returns the index buffer
     * @returns - The index buffer.
     */
    getIndex(): Buffer_2;
    /**
     * This function modifies the structure so that all current attributes become interleaved into a single buffer
     * This can be useful if your model remains static as it offers a little performance boost
     * @returns - Returns self, useful for chaining.
     */
    interleave(): Geometry;
    /** Get the size of the geometries, in vertices. */
    getSize(): number;
    /** Disposes WebGL resources that are connected to this geometry. */
    dispose(): void;
    /** Destroys the geometry. */
    destroy(): void;
    /**
     * Returns a clone of the geometry.
     * @returns - A new clone of this geometry.
     */
    clone(): Geometry;
    /**
     * Merges an array of geometries into a new single one.
     *
     * Geometry attribute styles must match for this operation to work.
     * @param geometries - array of geometries to merge
     * @returns - Shiny new geometry!
     */
    static merge(geometries: Array<Geometry>): Geometry;
}

/**
 * System plugin to the renderer to manage geometry.
 * @memberof PIXI
 */
export declare class GeometrySystem implements ISystem {
    /**
     * `true` if we has `*_vertex_array_object` extension.
     * @readonly
     */
    hasVao: boolean;
    /**
     * `true` if has `ANGLE_instanced_arrays` extension.
     * @readonly
     */
    hasInstance: boolean;
    /**
     * `true` if support `gl.UNSIGNED_INT` in `gl.drawElements` or `gl.drawElementsInstanced`.
     * @readonly
     */
    canUseUInt32ElementIndex: boolean;
    protected CONTEXT_UID: number;
    protected gl: IRenderingContext;
    protected _activeGeometry: Geometry;
    protected _activeVao: WebGLVertexArrayObject;
    protected _boundBuffer: GLBuffer;
    /** Cache for all geometries by id, used in case renderer gets destroyed or for profiling. */
    readonly managedGeometries: {
        [key: number]: Geometry;
    };
    /** Renderer that owns this {@link GeometrySystem}. */
    private renderer;
    /** @param renderer - The renderer this System works for. */
    constructor(renderer: Renderer);
    /** Sets up the renderer context and necessary buffers. */
    protected contextChange(): void;
    /**
     * Binds geometry so that is can be drawn. Creating a Vao if required
     * @param geometry - Instance of geometry to bind.
     * @param shader - Instance of shader to use vao for.
     */
    bind(geometry?: Geometry, shader?: Shader): void;
    /** Reset and unbind any active VAO and geometry. */
    reset(): void;
    /** Update buffers of the currently bound geometry. */
    updateBuffers(): void;
    /**
     * Check compatibility between a geometry and a program
     * @param geometry - Geometry instance.
     * @param program - Program instance.
     */
    protected checkCompatibility(geometry: Geometry, program: Program): void;
    /**
     * Takes a geometry and program and generates a unique signature for them.
     * @param geometry - To get signature from.
     * @param program - To test geometry against.
     * @returns - Unique signature of the geometry and program
     */
    protected getSignature(geometry: Geometry, program: Program): string;
    /**
     * Creates or gets Vao with the same structure as the geometry and stores it on the geometry.
     * If vao is created, it is bound automatically. We use a shader to infer what and how to set up the
     * attribute locations.
     * @param geometry - Instance of geometry to to generate Vao for.
     * @param shader - Instance of the shader.
     * @param incRefCount - Increment refCount of all geometry buffers.
     */
    protected initGeometryVao(geometry: Geometry, shader: Shader, incRefCount?: boolean): WebGLVertexArrayObject;
    /**
     * Disposes geometry.
     * @param geometry - Geometry with buffers. Only VAO will be disposed
     * @param [contextLost=false] - If context was lost, we suppress deleteVertexArray
     */
    disposeGeometry(geometry: Geometry, contextLost?: boolean): void;
    /**
     * Dispose all WebGL resources of all managed geometries.
     * @param [contextLost=false] - If context was lost, we suppress `gl.delete` calls
     */
    disposeAll(contextLost?: boolean): void;
    /**
     * Activate vertex array object.
     * @param geometry - Geometry instance.
     * @param program - Shader program instance.
     */
    protected activateVao(geometry: Geometry, program: Program): void;
    /**
     * Draws the currently bound geometry.
     * @param type - The type primitive to render.
     * @param size - The number of elements to be rendered. If not specified, all vertices after the
     *  starting vertex will be drawn.
     * @param start - The starting vertex in the geometry to start drawing from. If not specified,
     *  drawing will start from the first vertex.
     * @param instanceCount - The number of instances of the set of elements to execute. If not specified,
     *  all instances will be drawn.
     */
    draw(type: DRAW_MODES, size?: number, start?: number, instanceCount?: number): this;
    /** Unbind/reset everything. */
    protected unbind(): void;
    destroy(): void;
}

/**
 * returns a little WebGL context to use for program inspection.
 * @static
 * @private
 * @returns {WebGLRenderingContext} a gl context to test with
 */
export declare function getTestContext(): WebGLRenderingContext | WebGL2RenderingContext;

export declare function getUBOData(uniforms: Dict<any>, uniformData: Dict<any>): any[];

declare class GLBuffer {
    buffer: WebGLBuffer;
    updateID: number;
    byteLength: number;
    refCount: number;
    constructor(buffer?: WebGLBuffer);
}

/**
 * Internal framebuffer for WebGL context.
 * @memberof PIXI
 */
export declare class GLFramebuffer {
    /** The WebGL framebuffer. */
    framebuffer: WebGLFramebuffer;
    /** Stencil+depth , usually costs 32bits per pixel. */
    stencil: WebGLRenderbuffer;
    /** Detected AA samples number. */
    multisample: MSAA_QUALITY;
    /** In case MSAA, we use this Renderbuffer instead of colorTextures[0] when we write info. */
    msaaBuffer: WebGLRenderbuffer;
    /**
     * In case we use MSAA, this is actual framebuffer that has colorTextures[0]
     * The contents of that framebuffer are read when we use that renderTexture in sprites
     */
    blitFramebuffer: Framebuffer;
    /** Latest known version of framebuffer. */
    dirtyId: number;
    /** Latest known version of framebuffer format. */
    dirtyFormat: number;
    /** Latest known version of framebuffer size. */
    dirtySize: number;
    /** Store the current mipmap of the textures the framebuffer will write too. */
    mipLevel: number;
    constructor(framebuffer: WebGLTexture);
}

/**
 * Helper class to create a WebGL Program
 * @memberof PIXI
 */
export declare class GLProgram {
    /** The shader program. */
    program: WebGLProgram;
    /**
     * Holds the uniform data which contains uniform locations
     * and current uniform values used for caching and preventing unneeded GPU commands.
     */
    uniformData: Dict<any>;
    /**
     * UniformGroups holds the various upload functions for the shader. Each uniform group
     * and program have a unique upload function generated.
     */
    uniformGroups: Dict<any>;
    /** A hash that stores where UBOs are bound to on the program. */
    uniformBufferBindings: Dict<any>;
    /** A hash for lazily-generated uniform uploading functions. */
    uniformSync: Dict<any>;
    /**
     * A place where dirty ticks are stored for groups
     * If a tick here does not match with the Higher level Programs tick, it means
     * we should re upload the data.
     */
    uniformDirtyGroups: Dict<any>;
    /**
     * Makes a new Pixi program.
     * @param program - webgl program
     * @param uniformData - uniforms
     */
    constructor(program: WebGLProgram, uniformData: {
        [key: string]: IGLUniformData;
    });
    /** Destroys this program. */
    destroy(): void;
}

/**
 * Internal texture for WebGL context.
 * @memberof PIXI
 */
export declare class GLTexture {
    /** The WebGL texture. */
    texture: WebGLTexture;
    /** Width of texture that was used in texImage2D. */
    width: number;
    /** Height of texture that was used in texImage2D. */
    height: number;
    /** Whether mip levels has to be generated. */
    mipmap: boolean;
    /** WrapMode copied from baseTexture. */
    wrapMode: number;
    /** Type copied from baseTexture. */
    type: number;
    /** Type copied from baseTexture. */
    internalFormat: number;
    /** Type of sampler corresponding to this texture. See {@link PIXI.SAMPLER_TYPES} */
    samplerType: number;
    /** Texture contents dirty flag. */
    dirtyId: number;
    /** Texture style dirty flag. */
    dirtyStyleId: number;
    constructor(texture: WebGLTexture);
}

/**
 * Marks places in PixiJS where you can pass Float32Array, UInt32Array, any typed arrays, and ArrayBuffer
 *
 * Same as ArrayBuffer in typescript lib, defined here just for documentation
 */
export declare interface IArrayBuffer extends ArrayBuffer {
}

export declare interface IAttributeData {
    type: string;
    size: number;
    location: number;
    name: string;
}

export declare type IAutoDetectOptions = ISize | ICubeResourceOptions | IImageResourceOptions | ISVGResourceOptions | IVideoResourceOptions | IResourcePluginOptions;

export declare interface IBaseTextureOptions<RO = any> {
    alphaMode?: ALPHA_MODES;
    mipmap?: MIPMAP_MODES;
    anisotropicLevel?: number;
    scaleMode?: SCALE_MODES;
    width?: number;
    height?: number;
    wrapMode?: WRAP_MODES;
    format?: FORMATS;
    type?: TYPES;
    target?: TARGETS;
    resolution?: number;
    multisample?: MSAA_QUALITY;
    resourceOptions?: RO;
    pixiIdPrefix?: string;
}

/**
 * Interface for elements like Sprite, Mesh etc. for batching.
 * @memberof PIXI
 */
export declare interface IBatchableElement {
    _texture: Texture;
    vertexData: Float32Array;
    indices: Uint16Array | Uint32Array | Array<number>;
    uvs: Float32Array;
    worldAlpha: number;
    _tintRGB: number;
    blendMode: BLEND_MODES;
}

export declare interface IBatchFactoryOptions {
    vertex?: string;
    fragment?: string;
    geometryClass?: typeof BatchGeometry;
    vertexSize?: number;
}

/** Constructor options for CubeResource */
export declare interface ICubeResourceOptions extends ISize {
    /** Whether to auto-load resources */
    autoLoad?: boolean;
    /** In case BaseTextures are supplied, whether to copy them or use. */
    linkBaseTexture?: boolean;
}

export declare interface IFilterTarget {
    filterArea: Rectangle;
    getBounds(skipUpdate?: boolean): Rectangle;
}

export declare interface IGenerateTextureOptions {
    scaleMode?: SCALE_MODES;
    resolution?: number;
    region?: Rectangle;
    multisample?: MSAA_QUALITY;
}

/**
 * @private
 */
export declare class IGLUniformData {
    location: WebGLUniformLocation;
    value: number | boolean | Float32Array | Int32Array | Uint32Array | boolean[];
}

export declare interface IImageResourceOptions {
    /** Start loading process */
    autoLoad?: boolean;
    /** Whether its required to create a bitmap before upload. */
    createBitmap?: boolean;
    /** Load image using cross origin. */
    crossorigin?: boolean | string;
    /** Premultiply image alpha in bitmap. */
    alphaMode?: ALPHA_MODES;
}

/**
 * Resource type for ImageBitmap.
 * @memberof PIXI
 */
export declare class ImageBitmapResource extends BaseImageResource {
    /**
     * @param source - Image element to use
     */
    constructor(source: ImageBitmap);
    /**
     * Used to auto-detect the type of resource.
     * @param {*} source - The source object
     * @returns {boolean} `true` if source is an ImageBitmap
     */
    static test(source: unknown): source is ImageBitmap;
}

/**
 * Resource type for HTMLImageElement.
 * @memberof PIXI
 */
export declare class ImageResource extends BaseImageResource {
    /** URL of the image source */
    url: string;
    /**
     * If the image should be disposed after upload
     * @default false
     */
    preserveBitmap: boolean;
    /**
     * If capable, convert the image using createImageBitmap API.
     * @default PIXI.settings.CREATE_IMAGE_BITMAP
     */
    createBitmap: boolean;
    /**
     * Controls texture alphaMode field
     * Copies from options
     * Default is `null`, copies option from baseTexture
     * @readonly
     */
    alphaMode: ALPHA_MODES;
    /**
     * The ImageBitmap element created for a {@code HTMLImageElement}.
     * @default null
     */
    bitmap: ImageBitmap;
    /**
     * Promise when loading.
     * @default null
     */
    private _load;
    /** When process is completed */
    private _process;
    /**
     * @param source - image source or URL
     * @param options
     * @param {boolean} [options.autoLoad=true] - start loading process
     * @param {boolean} [options.createBitmap=PIXI.settings.CREATE_IMAGE_BITMAP] - whether its required to create
     *        a bitmap before upload
     * @param {boolean} [options.crossorigin=true] - Load image using cross origin
     * @param {PIXI.ALPHA_MODES} [options.alphaMode=PIXI.ALPHA_MODES.UNPACK] - Premultiply image alpha in bitmap
     */
    constructor(source: HTMLImageElement | string, options?: IImageResourceOptions);
    /**
     * Returns a promise when image will be loaded and processed.
     * @param createBitmap - whether process image into bitmap
     */
    load(createBitmap?: boolean): Promise<ImageResource>;
    /**
     * Called when we need to convert image into BitmapImage.
     * Can be called multiple times, real promise is cached inside.
     * @returns - Cached promise to fill that bitmap
     */
    process(): Promise<ImageResource>;
    /**
     * Upload the image resource to GPU.
     * @param renderer - Renderer to upload to
     * @param baseTexture - BaseTexture for this resource
     * @param glTexture - GLTexture to use
     * @returns {boolean} true is success
     */
    upload(renderer: Renderer, baseTexture: BaseTexture, glTexture: GLTexture): boolean;
    /** Destroys this resource. */
    dispose(): void;
    /**
     * Used to auto-detect the type of resource.
     * @param {*} source - The source object
     * @returns {boolean} `true` if source is string or HTMLImageElement
     */
    static test(source: unknown): source is string | HTMLImageElement;
}

export declare type ImageSource = HTMLImageElement | HTMLCanvasElement | HTMLVideoElement | ImageBitmap;

export declare interface IMaskTarget extends IFilterTarget {
    renderable: boolean;
    isSprite?: boolean;
    worldTransform: Matrix;
    isFastRect?(): boolean;
    getBounds(skipUpdate?: boolean, rect?: Rectangle): Rectangle;
    render(renderer: Renderer): void;
}

/**
 * Collection of installed resource types, class must extend {@link PIXI.Resource}.
 * @example
 * class CustomResource extends PIXI.Resource {
 *   // MUST have source, options constructor signature
 *   // for auto-detected resources to be created.
 *   constructor(source, options) {
 *     super();
 *   }
 *   upload(renderer, baseTexture, glTexture) {
 *     // upload with GL
 *     return true;
 *   }
 *   // used to auto-detect resource
 *   static test(source, extension) {
 *     return extension === 'xyz'|| source instanceof SomeClass;
 *   }
 * }
 * // Install the new resource type
 * PIXI.INSTALLED.push(CustomResource);
 * @memberof PIXI
 * @type {Array<PIXI.IResourcePlugin>}
 * @static
 * @readonly
 */
export declare const INSTALLED: Array<IResourcePlugin<any, any>>;

/**
 * Interface for Container to interface with Renderer.
 * @memberof PIXI
 */
export declare interface IRenderableContainer extends IRenderableObject {
    /** Get Local bounds for container */
    getLocalBounds(rect?: Rectangle, skipChildrenUpdate?: boolean): Rectangle;
}

/**
 * Interface for DisplayObject to interface with Renderer.
 * The minimum APIs needed to implement a renderable object.
 * @memberof PIXI
 */
export declare interface IRenderableObject {
    /** Object must have a parent container */
    parent: IRenderableContainer;
    /** Before method for transform updates */
    enableTempParent(): IRenderableContainer;
    /** Update the transforms */
    updateTransform(): void;
    /** After method for transform updates */
    disableTempParent(parent: IRenderableContainer): void;
    /** Render object directly */
    render(renderer: Renderer): void;
}

/**
 * Renderer options supplied to constructor.
 * @memberof PIXI
 * @see PIXI.settings.RENDER_OPTIONS
 */
export declare interface IRendererOptions extends GlobalMixins.IRendererOptions {
    /** The canvas to use as the view. If omitted, a new canvas will be created. */
    view?: HTMLCanvasElement;
    /**
     * The width of the renderer's view.
     * @default 800
     */
    width?: number;
    /**
     * The height of the renderer's view.
     * @default 600
     */
    height?: number;
    /**
     * The resolution / device pixel ratio of the renderer.
     * @default PIXI.settings.RESOLUTION
     */
    resolution?: number;
    /**
     * Whether the CSS dimensions of the renderer's view should be resized automatically.
     * @default false
     */
    autoDensity?: boolean;
    /**
     * The background color used to clear the canvas. It accepts hex numbers (e.g. `0xff0000`).
     * @default 0x000000
     */
    backgroundColor?: number;
    /**
     * Transparency of the background color, value from `0` (fully transparent) to `1` (fully opaque).
     * @default 1
     */
    backgroundAlpha?: number;
    /**
     * Pass-through value for canvas' context attribute `alpha`. This option is for cases where the
     * canvas needs to be opaque, possibly for performance reasons on some older devices.
     * If you want to set transparency, please use `backgroundAlpha`.
     *
     * **WebGL Only:** When set to `'notMultiplied'`, the canvas' context attribute `alpha` will be
     * set to `true` and `premultipliedAlpha` will be to `false`.
     * @default true
     */
    useContextAlpha?: boolean | 'notMultiplied';
    /**
     * Use `backgroundAlpha` instead.
     * @deprecated since 6.0.0
     */
    transparent?: boolean;
    /**
     * Whether to clear the canvas before new render passes.
     * @default true
     */
    clearBeforeRender?: boolean;
    /** **WebGL Only.** User-provided WebGL rendering context object. */
    context?: IRenderingContext;
    /**
     * **WebGL Only.** Whether to enable anti-aliasing. This may affect performance.
     * @default false
     */
    antialias?: boolean;
    /**
     * **WebGL Only.** A hint indicating what configuration of GPU is suitable for the WebGL context,
     * can be `'default'`, `'high-performance'` or `'low-power'`.
     * Setting to `'high-performance'` will prioritize rendering performance over power consumption,
     * while setting to `'low-power'` will prioritize power saving over rendering performance.
     */
    powerPreference?: WebGLPowerPreference;
    /**
     * **WebGL Only.** Whether to enable drawing buffer preservation. If enabled, the drawing buffer will preserve
     * its value until cleared or overwritten. Enable this if you need to call `toDataUrl` on the WebGL context.
     * @default false
     */
    preserveDrawingBuffer?: boolean;
}

/**
 * Renderer options supplied to `autoDetectRenderer`.
 * @memberof PIXI
 */
export declare interface IRendererOptionsAuto extends IRendererOptions {
    forceCanvas?: boolean;
}

export declare interface IRendererPlugin {
    destroy(): void;
}

export declare interface IRendererPluginConstructor {
    new (renderer: Renderer, options?: any): IRendererPlugin;
}

export declare interface IRendererPlugins {
    [key: string]: any;
}

export declare interface IRendererRenderOptions {
    renderTexture?: RenderTexture;
    clear?: boolean;
    transform?: Matrix;
    skipUpdateTransform?: boolean;
}

/** Mixed WebGL1/WebGL2 Rendering Context. Either its WebGL2, either its WebGL1 with PixiJS polyfills on it */
export declare interface IRenderingContext extends WebGL2RenderingContext {
}

/**
 * Shape of supported resource plugins
 * @memberof PIXI
 */
export declare interface IResourcePlugin<R, RO> {
    test(source: unknown, extension: string): boolean;
    new (source: any, options?: RO): R;
}

export declare type IResourcePluginOptions = {
    [key: string]: any;
};

export declare interface ISpriteMaskFilter extends Filter {
    maskSprite: IMaskTarget;
}

export declare interface ISpriteMaskTarget extends IMaskTarget {
    _texture: Texture;
    worldAlpha: number;
    anchor: Point;
}

export declare interface ISupportDict {
    uint32Indices: boolean;
}

export declare interface ISVGResourceOptions {
    source?: string;
    scale?: number;
    width?: number;
    height?: number;
    autoLoad?: boolean;
    crossorigin?: boolean | string;
}

/**
 * Interface for systems used by the {@link PIXI.Renderer}.
 * @memberof PIXI
 */
export declare interface ISystem {
    /** Generic destroy methods to be overridden by the subclass */
    destroy(): void;
}

/**
 * Types for system and pipe classes.
 * @ignore
 */
export declare interface ISystemConstructor<R = Renderer> {
    new (renderer: R): ISystem;
}

/**
 * PixiJS classes use this type instead of ArrayBuffer and typed arrays
 * to support expressions like `geometry.buffers[0].data[0] = position.x`.
 *
 * Gives access to indexing and `length` field
 * * @popelyshev: If data is actually ArrayBuffer and throws Exception on indexing - its user problem :)
 */
export declare interface ITypedArray extends IArrayBuffer {
    readonly length: number;
    [index: number]: number;
    readonly BYTES_PER_ELEMENT: number;
}

export declare interface IUniformData {
    index: number;
    type: string;
    size: number;
    isArray: boolean;
    value: any;
    name: string;
}

export declare interface IUniformParser {
    test(data: unknown, uniform: any): boolean;
    code(name: string, uniform: any): string;
    codeUbo?(name: string, uniform: any): string;
}

export declare interface IUnloadableTexture {
    _texture: Texture | RenderTexture;
    children: IUnloadableTexture[];
}

export declare interface IVideoResourceOptions {
    autoLoad?: boolean;
    autoPlay?: boolean;
    updateFPS?: number;
    crossorigin?: boolean | string;
}

export declare interface IVideoResourceOptionsElement {
    src: string;
    mime: string;
}

/**
 * Component for masked elements.
 *
 * Holds mask mode and temporary data about current mask.
 * @memberof PIXI
 */
export declare class MaskData {
    /** Mask type */
    type: MASK_TYPES;
    /**
     * Whether we know the mask type beforehand
     * @default true
     */
    autoDetect: boolean;
    /**
     * Which element we use to mask
     * @member {PIXI.DisplayObject}
     */
    maskObject: IMaskTarget;
    /** Whether it belongs to MaskSystem pool */
    pooled: boolean;
    /** Indicator of the type (always true for {@link MaskData} objects) */
    isMaskData: boolean;
    /**
     * Resolution of the sprite mask filter.
     * If set to `null` or `0`, the resolution of the current render target is used.
     * @default null
     */
    resolution: number;
    /**
     * Number of samples of the sprite mask filter.
     * If set to `null`, the sample count of the current render target is used.
     * @default PIXI.settings.FILTER_MULTISAMPLE
     */
    multisample: MSAA_QUALITY;
    /** If enabled is true the mask is applied, if false it will not. */
    enabled: boolean;
    /** Color mask. */
    colorMask: COLOR_MASK_BITS;
    /**
     * The sprite mask filter wrapped in an array.
     * @private
     */
    _filters: ISpriteMaskFilter_2[];
    /**
     * Stencil counter above the mask in stack
     * @private
     */
    _stencilCounter: number;
    /**
     * Scissor counter above the mask in stack
     * @private
     */
    _scissorCounter: number;
    /**
     * Scissor operation above the mask in stack.
     * Null if _scissorCounter is zero, rectangle instance if positive.
     * @private
     */
    _scissorRect: Rectangle;
    /**
     * pre-computed scissor rect
     * does become _scissorRect when mask is actually pushed
     * @private
     */
    _scissorRectLocal: Rectangle;
    /**
     * pre-computed color mask
     * @private
     */
    _colorMask: number;
    /**
     * Targeted element. Temporary variable set by MaskSystem
     * @member {PIXI.DisplayObject}
     * @private
     */
    _target: IMaskTarget;
    /**
     * Create MaskData
     * @param {PIXI.DisplayObject} [maskObject=null] - object that describes the mask
     */
    constructor(maskObject?: IMaskTarget);
    /**
     * The sprite mask filter.
     * If set to `null`, the default sprite mask filter is used.
     * @default null
     */
    get filter(): ISpriteMaskFilter_2;
    set filter(value: ISpriteMaskFilter_2);
    /** Resets the mask data after popMask(). */
    reset(): void;
    /**
     * Copies counters from maskData above, called from pushMask().
     * @param maskAbove
     */
    copyCountersOrReset(maskAbove?: MaskData): void;
}

/**
 * System plugin to the renderer to manage masks.
 *
 * There are three built-in types of masking:
 * **Scissor Masking**: Scissor masking discards pixels that are outside of a rectangle called the scissor box. It is
 *  the most performant as the scissor test is inexpensive. However, it can only be used when the mask is rectangular.
 * **Stencil Masking**: Stencil masking discards pixels that don't overlap with the pixels rendered into the stencil
 *  buffer. It is the next fastest option as it does not require rendering into a separate framebuffer. However, it does
 *  cause the mask to be rendered **twice** for each masking operation; hence, minimize the rendering cost of your masks.
 * **Sprite Mask Filtering**: Sprite mask filtering discards pixels based on the red channel of the sprite-mask's
 *  texture. (Generally, the masking texture is grayscale). Using advanced techniques, you might be able to embed this
 *  type of masking in a custom shader - and hence, bypassing the masking system fully for performance wins.
 *
 * The best type of masking is auto-detected when you `push` one. To use scissor masking, you must pass in a `Graphics`
 * object with just a rectangle drawn.
 *
 * ## Mask Stacks
 *
 * In the scene graph, masks can be applied recursively, i.e. a mask can be applied during a masking operation. The mask
 * stack stores the currently applied masks in order. Each {@link PIXI.BaseRenderTexture} holds its own mask stack, i.e.
 * when you switch render-textures, the old masks only applied when you switch back to rendering to the old render-target.
 * @memberof PIXI
 */
export declare class MaskSystem implements ISystem {
    /**
     * Flag to enable scissor masking.
     * @default true
     */
    enableScissor: boolean;
    /** Pool of used sprite mask filters. */
    protected readonly alphaMaskPool: Array<SpriteMaskFilter[]>;
    /**
     * Current index of alpha mask pool.
     * @default 0
     * @readonly
     */
    protected alphaMaskIndex: number;
    /** Pool of mask data. */
    private readonly maskDataPool;
    private maskStack;
    private renderer;
    /**
     * @param renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    /**
     * Changes the mask stack that is used by this System.
     * @param maskStack - The mask stack
     */
    setMaskStack(maskStack: Array<MaskData>): void;
    /**
     * Enables the mask and appends it to the current mask stack.
     *
     * NOTE: The batch renderer should be flushed beforehand to prevent pending renders from being masked.
     * @param {PIXI.DisplayObject} target - Display Object to push the mask to
     * @param {PIXI.MaskData|PIXI.Sprite|PIXI.Graphics|PIXI.DisplayObject} maskDataOrTarget - The masking data.
     */
    push(target: IMaskTarget, maskDataOrTarget: MaskData | IMaskTarget): void;
    /**
     * Removes the last mask from the mask stack and doesn't return it.
     *
     * NOTE: The batch renderer should be flushed beforehand to render the masked contents before the mask is removed.
     * @param {PIXI.IMaskTarget} target - Display Object to pop the mask from
     */
    pop(target: IMaskTarget): void;
    /**
     * Sets type of MaskData based on its maskObject.
     * @param maskData
     */
    detect(maskData: MaskData): void;
    /**
     * Applies the Mask and adds it to the current filter stack.
     * @param maskData - Sprite to be used as the mask.
     */
    pushSpriteMask(maskData: MaskData): void;
    /**
     * Removes the last filter from the filter stack and doesn't return it.
     * @param maskData - Sprite to be used as the mask.
     */
    popSpriteMask(maskData: MaskData): void;
    /**
     * Pushes the color mask.
     * @param maskData - The mask data
     */
    pushColorMask(maskData: MaskData): void;
    /**
     * Pops the color mask.
     * @param maskData - The mask data
     */
    popColorMask(maskData: MaskData): void;
    destroy(): void;
}

/**
 * Base for a common object renderer that can be used as a
 * system renderer plugin.
 * @memberof PIXI
 */
export declare class ObjectRenderer implements ISystem {
    /** The renderer this manager works for. */
    protected renderer: Renderer;
    /**
     * @param renderer - The renderer this manager works for.
     */
    constructor(renderer: Renderer);
    /** Stub method that should be used to empty the current batch by rendering objects now. */
    flush(): void;
    /** Generic destruction method that frees all resources. This should be called by subclasses. */
    destroy(): void;
    /**
     * Stub method that initializes any state required before
     * rendering starts. It is different from the `prerender`
     * signal, which occurs every frame, in that it is called
     * whenever an object requests _this_ renderer specifically.
     */
    start(): void;
    /** Stops the renderer. It should free up any state and become dormant. */
    stop(): void;
    /**
     * Keeps the object to render. It doesn't have to be
     * rendered immediately.
     * @param {PIXI.DisplayObject} _object - The object to render.
     */
    render(_object: any): void;
}

/**
 * Helper class to create a shader program.
 * @memberof PIXI
 */
export declare class Program {
    id: number;
    /** Source code for the vertex shader. */
    vertexSrc: string;
    /** Source code for the fragment shader. */
    fragmentSrc: string;
    nameCache: any;
    glPrograms: {
        [key: number]: GLProgram;
    };
    syncUniforms: any;
    /** Assigned when a program is first bound to the shader system. */
    attributeData: {
        [key: string]: IAttributeData;
    };
    /** Assigned when a program is first bound to the shader system. */
    uniformData: {
        [key: string]: IUniformData;
    };
    /**
     * @param vertexSrc - The source of the vertex shader.
     * @param fragmentSrc - The source of the fragment shader.
     * @param name - Name for shader
     */
    constructor(vertexSrc?: string, fragmentSrc?: string, name?: string);
    /**
     * The default vertex shader source.
     * @constant
     */
    static get defaultVertexSrc(): string;
    /**
     * The default fragment shader source.
     * @constant
     */
    static get defaultFragmentSrc(): string;
    /**
     * A short hand function to create a program based of a vertex and fragment shader.
     *
     * This method will also check to see if there is a cached program.
     * @param vertexSrc - The source of the vertex shader.
     * @param fragmentSrc - The source of the fragment shader.
     * @param name - Name for shader
     * @returns A shiny new PixiJS shader program!
     */
    static from(vertexSrc?: string, fragmentSrc?: string, name?: string): Program;
}

/**
 * System plugin to the renderer to manage the projection matrix.
 *
 * The `projectionMatrix` is a global uniform provided to all shaders. It is used to transform points in world space to
 * normalized device coordinates.
 * @memberof PIXI
 */
export declare class ProjectionSystem implements ISystem {
    /**
     * The destination frame used to calculate the current projection matrix.
     *
     * The destination frame is the rectangle in the render-target into which contents are rendered. If rendering
     * to the screen, the origin is on the top-left. If rendering to a framebuffer, the origin is on the
     * bottom-left. This "flipping" phenomenon is because of WebGL convention for (shader) texture coordinates, where
     * the bottom-left corner is (0,0). It allows display-objects to map their (0,0) position in local-space (top-left)
     * to (0,0) in texture space (bottom-left). In other words, a sprite's top-left corner actually renders the
     * texture's bottom-left corner. You will also notice this when using a tool like SpectorJS to view your textures
     * at runtime.
     *
     * The destination frame's dimensions (width,height) should be equal to the source frame. This is because,
     * otherwise, the contents will be scaled to fill the destination frame. Similarly, the destination frame's (x,y)
     * coordinates are (0,0) unless you know what you're doing.
     * @readonly
     */
    destinationFrame: Rectangle;
    /**
     * The source frame used to calculate the current projection matrix.
     *
     * The source frame is the rectangle in world space containing the contents to be rendered.
     * @readonly
     */
    sourceFrame: Rectangle;
    /**
     * Default destination frame
     *
     * This is not used internally. It is not advised to use this feature specifically unless you know what
     * you're doing. The `update` method will default to this frame if you do not pass the destination frame.
     * @readonly
     */
    defaultFrame: Rectangle;
    /**
     * Projection matrix
     *
     * This matrix can be used to transform points from world space to normalized device coordinates, and is calculated
     * from the sourceFrame → destinationFrame mapping provided.
     *
     * The renderer's `globalUniforms` keeps a reference to this, and so it is available for all shaders to use as a
     * uniform.
     * @readonly
     */
    projectionMatrix: Matrix;
    /**
     * A transform to be appended to the projection matrix.
     *
     * This can be used to transform points in world-space one last time before they are outputted by the shader. You can
     * use to rotate the whole scene, for example. Remember to clear it once you've rendered everything.
     * @member {PIXI.Matrix}
     */
    transform: Matrix;
    private renderer;
    /** @param renderer - The renderer this System works for. */
    constructor(renderer: Renderer);
    /**
     * Updates the projection-matrix based on the sourceFrame → destinationFrame mapping provided.
     *
     * NOTE: It is expected you call `renderer.framebuffer.setViewport(destinationFrame)` after this. This is because
     * the framebuffer viewport converts shader vertex output in normalized device coordinates to window coordinates.
     *
     * NOTE-2: {@link RenderTextureSystem#bind} updates the projection-matrix when you bind a render-texture. It is expected
     * that you dirty the current bindings when calling this manually.
     * @param destinationFrame - The rectangle in the render-target to render the contents into. If rendering to the canvas,
     *  the origin is on the top-left; if rendering to a render-texture, the origin is on the bottom-left.
     * @param sourceFrame - The rectangle in world space that contains the contents being rendered.
     * @param resolution - The resolution of the render-target, which is the ratio of
     *  world-space (or CSS) pixels to physical pixels.
     * @param root - Whether the render-target is the screen. This is required because rendering to textures
     *  is y-flipped (i.e. upside down relative to the screen).
     */
    update(destinationFrame: Rectangle, sourceFrame: Rectangle, resolution: number, root: boolean): void;
    /**
     * Calculates the `projectionMatrix` to map points inside `sourceFrame` to inside `destinationFrame`.
     * @param _destinationFrame - The destination frame in the render-target.
     * @param sourceFrame - The source frame in world space.
     * @param _resolution - The render-target's resolution, i.e. ratio of CSS to physical pixels.
     * @param root - Whether rendering into the screen. Otherwise, if rendering to a framebuffer, the projection
     *  is y-flipped.
     */
    calculateProjection(_destinationFrame: Rectangle, sourceFrame: Rectangle, _resolution: number, root: boolean): void;
    /**
     * Sets the transform of the active render target to the given matrix.
     * @param _matrix - The transformation matrix
     */
    setTransform(_matrix: Matrix): void;
    destroy(): void;
}

/**
 * Helper class to create a quad
 * @memberof PIXI
 */
export declare class Quad extends Geometry {
    constructor();
}

/**
 * Helper class to create a quad with uvs like in v4
 * @memberof PIXI
 */
export declare class QuadUv extends Geometry {
    vertexBuffer: Buffer_2;
    uvBuffer: Buffer_2;
    /** An array of vertices. */
    vertices: Float32Array;
    /** The Uvs of the quad. */
    uvs: Float32Array;
    constructor();
    /**
     * Maps two Rectangle to the quad.
     * @param targetTextureFrame - The first rectangle
     * @param destinationFrame - The second rectangle
     * @returns - Returns itself.
     */
    map(targetTextureFrame: Rectangle, destinationFrame: Rectangle): this;
    /**
     * Legacy upload method, just marks buffers dirty.
     * @returns - Returns itself.
     */
    invalidate(): this;
}

/**
 * The Renderer draws the scene and all its content onto a WebGL enabled canvas.
 *
 * This renderer should be used for browsers that support WebGL.
 *
 * This renderer works by automatically managing WebGLBatchesm, so no need for Sprite Batches or Sprite Clouds.
 * Don't forget to add the view to your DOM or you will not see anything!
 *
 * Renderer is composed of systems that manage specific tasks. The following systems are added by default
 * whenever you create a renderer:
 *
 * | System                               | Description                                                                   |
 * | ------------------------------------ | ----------------------------------------------------------------------------- |
 * | {@link PIXI.BatchSystem}             | This manages object renderers that defer rendering until a flush.             |
 * | {@link PIXI.ContextSystem}           | This manages the WebGL context and extensions.                                |
 * | {@link PIXI.EventSystem}             | This manages UI events.                                                       |
 * | {@link PIXI.FilterSystem}            | This manages the filtering pipeline for post-processing effects.              |
 * | {@link PIXI.FramebufferSystem}       | This manages framebuffers, which are used for offscreen rendering.            |
 * | {@link PIXI.GeometrySystem}          | This manages geometries & buffers, which are used to draw object meshes.      |
 * | {@link PIXI.MaskSystem}              | This manages masking operations.                                              |
 * | {@link PIXI.ProjectionSystem}        | This manages the `projectionMatrix`, used by shaders to get NDC coordinates.  |
 * | {@link PIXI.RenderTextureSystem}     | This manages render-textures, which are an abstraction over framebuffers.     |
 * | {@link PIXI.ScissorSystem}           | This handles scissor masking, and is used internally by {@link MaskSystem}    |
 * | {@link PIXI.ShaderSystem}            | This manages shaders, programs that run on the GPU to calculate 'em pixels.   |
 * | {@link PIXI.StateSystem}             | This manages the WebGL state variables like blend mode, depth testing, etc.   |
 * | {@link PIXI.StencilSystem}           | This handles stencil masking, and is used internally by {@link MaskSystem}    |
 * | {@link PIXI.TextureSystem}           | This manages textures and their resources on the GPU.                         |
 * | {@link PIXI.TextureGCSystem}         | This will automatically remove textures from the GPU if they are not used.    |
 *
 * The breadth of the API surface provided by the renderer is contained within these systems.
 * @memberof PIXI
 */
export declare class Renderer extends AbstractRenderer {
    /**
     * WebGL context, set by {@link PIXI.ContextSystem this.context}.
     * @readonly
     * @member {WebGLRenderingContext}
     */
    gl: IRenderingContext;
    /** Global uniforms */
    globalUniforms: UniformGroup;
    /** Unique UID assigned to the renderer's WebGL context. */
    CONTEXT_UID: number;
    /**
     * Flag if we are rendering to the screen vs renderTexture
     * @readonly
     * @default true
     */
    renderingToScreen: boolean;
    /**
     * The number of msaa samples of the canvas.
     * @readonly
     */
    multisample: MSAA_QUALITY;
    /**
     * Mask system instance
     * @readonly
     */
    mask: MaskSystem;
    /**
     * Context system instance
     * @readonly
     */
    context: ContextSystem;
    /**
     * State system instance
     * @readonly
     */
    state: StateSystem;
    /**
     * Shader system instance
     * @readonly
     */
    shader: ShaderSystem;
    /**
     * Texture system instance
     * @readonly
     */
    texture: TextureSystem;
    /**
     * Buffer system instance
     * @readonly
     */
    buffer: BufferSystem;
    /**
     * Geometry system instance
     * @readonly
     */
    geometry: GeometrySystem;
    /**
     * Framebuffer system instance
     * @readonly
     */
    framebuffer: FramebufferSystem;
    /**
     * Scissor system instance
     * @readonly
     */
    scissor: ScissorSystem;
    /**
     * Stencil system instance
     * @readonly
     */
    stencil: StencilSystem;
    /**
     * Projection system instance
     * @readonly
     */
    projection: ProjectionSystem;
    /**
     * Texture garbage collector system instance
     * @readonly
     */
    textureGC: TextureGCSystem;
    /**
     * Filter system instance
     * @readonly
     */
    filter: FilterSystem;
    /**
     * RenderTexture system instance
     * @readonly
     */
    renderTexture: RenderTextureSystem;
    /**
     * Batch system instance
     * @readonly
     */
    batch: BatchSystem;
    /**
     * Internal signal instances of **runner**, these
     * are assigned to each system created.
     * @see PIXI.Runner
     * @name runners
     * @private
     * @type {object}
     * @readonly
     * @property {PIXI.Runner} destroy - Destroy runner
     * @property {PIXI.Runner} contextChange - Context change runner
     * @property {PIXI.Runner} reset - Reset runner
     * @property {PIXI.Runner} update - Update runner
     * @property {PIXI.Runner} postrender - Post-render runner
     * @property {PIXI.Runner} prerender - Pre-render runner
     * @property {PIXI.Runner} resize - Resize runner
     */
    runners: {
        [key: string]: Runner;
    };
    /**
     * Create renderer if WebGL is available. Overrideable
     * by the **@pixi/canvas-renderer** package to allow fallback.
     * throws error if WebGL is not available.
     * @param options
     * @private
     */
    static create(options?: IRendererOptions): AbstractRenderer;
    /**
     * @param {PIXI.IRendererOptions} [options] - The optional renderer parameters.
     * @param {boolean} [options.antialias=false] -
     *  **WebGL Only.** Whether to enable anti-aliasing. This may affect performance.
     * @param {boolean} [options.autoDensity=false] -
     *  Whether the CSS dimensions of the renderer's view should be resized automatically.
     * @param {number} [options.backgroundAlpha=1] -
     *  Transparency of the background color, value from `0` (fully transparent) to `1` (fully opaque).
     * @param {number} [options.backgroundColor=0x000000] -
     *  The background color used to clear the canvas. It accepts hex numbers (e.g. `0xff0000`).
     * @param {boolean} [options.clearBeforeRender=true] - Whether to clear the canvas before new render passes.
     * @param {PIXI.IRenderingContext} [options.context] - **WebGL Only.** User-provided WebGL rendering context object.
     * @param {number} [options.height=600] - The height of the renderer's view.
     * @param {string} [options.powerPreference] -
     *  **WebGL Only.** A hint indicating what configuration of GPU is suitable for the WebGL context,
     *  can be `'default'`, `'high-performance'` or `'low-power'`.
     *  Setting to `'high-performance'` will prioritize rendering performance over power consumption,
     *  while setting to `'low-power'` will prioritize power saving over rendering performance.
     * @param {boolean} [options.premultipliedAlpha=true] -
     *  **WebGL Only.** Whether the compositor will assume the drawing buffer contains colors with premultiplied alpha.
     * @param {boolean} [options.preserveDrawingBuffer=false] -
     *  **WebGL Only.** Whether to enable drawing buffer preservation. If enabled, the drawing buffer will preserve
     *  its value until cleared or overwritten. Enable this if you need to call `toDataUrl` on the WebGL context.
     * @param {number} [options.resolution=PIXI.settings.RESOLUTION] -
     *  The resolution / device pixel ratio of the renderer.
     * @param {boolean} [options.transparent] -
     *  **Deprecated since 6.0.0, Use `backgroundAlpha` instead.** \
     *  `true` sets `backgroundAlpha` to `0`, `false` sets `backgroundAlpha` to `1`.
     * @param {boolean|'notMultiplied'} [options.useContextAlpha=true] -
     *  Pass-through value for canvas' context attribute `alpha`. This option is for cases where the
     *  canvas needs to be opaque, possibly for performance reasons on some older devices.
     *  If you want to set transparency, please use `backgroundAlpha`. \
     *  **WebGL Only:** When set to `'notMultiplied'`, the canvas' context attribute `alpha` will be
     *  set to `true` and `premultipliedAlpha` will be to `false`.
     * @param {HTMLCanvasElement} [options.view=null] -
     *  The canvas to use as the view. If omitted, a new canvas will be created.
     * @param {number} [options.width=800] - The width of the renderer's view.
     */
    constructor(options?: IRendererOptions);
    protected contextChange(): void;
    /**
     * Add a new system to the renderer.
     * @param ClassRef - Class reference
     * @param name - Property name for system, if not specified
     *        will use a static `name` property on the class itself. This
     *        name will be assigned as s property on the Renderer so make
     *        sure it doesn't collide with properties on Renderer.
     * @returns Return instance of renderer
     */
    addSystem(ClassRef: ISystemConstructor, name: string): this;
    /**
     * Renders the object to its WebGL view.
     * @param displayObject - The object to be rendered.
     * @param {object} [options] - Object to use for render options.
     * @param {PIXI.RenderTexture} [options.renderTexture] - The render texture to render to.
     * @param {boolean} [options.clear=true] - Should the canvas be cleared before the new render.
     * @param {PIXI.Matrix} [options.transform] - A transform to apply to the render texture before rendering.
     * @param {boolean} [options.skipUpdateTransform=false] - Should we skip the update transform pass?
     */
    render(displayObject: IRenderableObject, options?: IRendererRenderOptions): void;
    /**
     * Please use the `option` render arguments instead.
     * @deprecated Since 6.0.0
     * @param displayObject
     * @param renderTexture
     * @param clear
     * @param transform
     * @param skipUpdateTransform
     */
    render(displayObject: IRenderableObject, renderTexture?: RenderTexture, clear?: boolean, transform?: Matrix, skipUpdateTransform?: boolean): void;
    /**
     * @override
     * @ignore
     */
    generateTexture(displayObject: IRenderableObject, options?: IGenerateTextureOptions | SCALE_MODES, resolution?: number, region?: Rectangle): RenderTexture;
    /**
     * Resizes the WebGL view to the specified width and height.
     * @param desiredScreenWidth - The desired width of the screen.
     * @param desiredScreenHeight - The desired height of the screen.
     */
    resize(desiredScreenWidth: number, desiredScreenHeight: number): void;
    /**
     * Resets the WebGL state so you can render things however you fancy!
     * @returns Returns itself.
     */
    reset(): this;
    /** Clear the frame buffer. */
    clear(): void;
    /**
     * Removes everything from the renderer (event listeners, spritebatch, etc...)
     * @param [removeView=false] - Removes the Canvas element from the DOM.
     *  See: https://github.com/pixijs/pixi.js/issues/2233
     */
    destroy(removeView?: boolean): void;
    /**
     * Please use `plugins.extract` instead.
     * @member {PIXI.Extract} extract
     * @deprecated since 6.0.0
     * @readonly
     */
    get extract(): any;
    /**
     * Collection of installed plugins. These are included by default in PIXI, but can be excluded
     * by creating a custom build. Consult the README for more information about creating custom
     * builds and excluding plugins.
     * @readonly
     * @property {PIXI.AccessibilityManager} accessibility Support tabbing interactive elements.
     * @property {PIXI.Extract} extract Extract image data from renderer.
     * @property {PIXI.InteractionManager} interaction Handles mouse, touch and pointer events.
     * @property {PIXI.ParticleRenderer} particle Renderer for ParticleContainer objects.
     * @property {PIXI.Prepare} prepare Pre-render display objects.
     * @property {PIXI.BatchRenderer} batch Batching of Sprite, Graphics and Mesh objects.
     * @property {PIXI.TilingSpriteRenderer} tilingSprite Renderer for TilingSprite objects.
     */
    static __plugins: IRendererPlugins;
    /**
     * Use the {@link PIXI.extensions.add} API to register plugins.
     * @deprecated since 6.5.0
     * @param pluginName - The name of the plugin.
     * @param ctor - The constructor function or class for the plugin.
     */
    static registerPlugin(pluginName: string, ctor: IRendererPluginConstructor): void;
}

/**
 * A RenderTexture is a special texture that allows any PixiJS display object to be rendered to it.
 *
 * __Hint__: All DisplayObjects (i.e. Sprites) that render to a RenderTexture should be preloaded
 * otherwise black rectangles will be drawn instead.
 *
 * __Hint-2__: The actual memory allocation will happen on first render.
 * You shouldn't create renderTextures each frame just to delete them after, try to reuse them.
 *
 * A RenderTexture takes a snapshot of any Display Object given to its render method. For example:
 *
 * ```js
 * let renderer = PIXI.autoDetectRenderer();
 * let renderTexture = PIXI.RenderTexture.create({ width: 800, height: 600 });
 * let sprite = PIXI.Sprite.from("spinObj_01.png");
 *
 * sprite.position.x = 800/2;
 * sprite.position.y = 600/2;
 * sprite.anchor.x = 0.5;
 * sprite.anchor.y = 0.5;
 *
 * renderer.render(sprite, {renderTexture});
 * ```
 * Note that you should not create a new renderer, but reuse the same one as the rest of the application.
 *
 * The Sprite in this case will be rendered using its local transform. To render this sprite at 0,0
 * you can clear the transform
 *
 * ```js
 *
 * sprite.setTransform()
 *
 * let renderTexture = new PIXI.RenderTexture.create({ width: 100, height: 100 });
 *
 * renderer.render(sprite, {renderTexture});  // Renders to center of RenderTexture
 * ```
 * @memberof PIXI
 */
export declare class RenderTexture extends Texture {
    baseTexture: BaseRenderTexture;
    /**
     * Stores `sourceFrame` when this texture is inside current filter stack.
     *
     * You can read it inside filters.
     * @readonly
     */
    filterFrame: Rectangle | null;
    /**
     * The key for pooled texture of FilterSystem.
     * @see PIXI.RenderTexturePool
     */
    filterPoolKey: string | number | null;
    /**
     * @param baseRenderTexture - The base texture object that this texture uses.
     * @param frame - The rectangle frame of the texture to show.
     */
    constructor(baseRenderTexture: BaseRenderTexture, frame?: Rectangle);
    /**
     * Shortcut to `this.baseTexture.framebuffer`, saves baseTexture cast.
     * @readonly
     */
    get framebuffer(): Framebuffer;
    /**
     * Shortcut to `this.framebuffer.multisample`.
     * @default PIXI.MSAA_QUALITY.NONE
     */
    get multisample(): MSAA_QUALITY;
    set multisample(value: MSAA_QUALITY);
    /**
     * Resizes the RenderTexture.
     * @param desiredWidth - The desired width to resize to.
     * @param desiredHeight - The desired height to resize to.
     * @param resizeBaseTexture - Should the baseTexture.width and height values be resized as well?
     */
    resize(desiredWidth: number, desiredHeight: number, resizeBaseTexture?: boolean): void;
    /**
     * Changes the resolution of baseTexture, but does not change framebuffer size.
     * @param resolution - The new resolution to apply to RenderTexture
     */
    setResolution(resolution: number): void;
    /**
     * Use the object-based construction instead.
     * @deprecated since 6.0.0
     * @param {number} [width]
     * @param {number} [height]
     * @param {PIXI.SCALE_MODES} [scaleMode=PIXI.settings.SCALE_MODE]
     * @param {number} [resolution=PIXI.settings.FILTER_RESOLUTION]
     */
    static create(width: number, height: number, scaleMode?: SCALE_MODES, resolution?: number): RenderTexture;
    /**
     * A short hand way of creating a render texture.
     * @param options - Options
     * @param {number} [options.width=100] - The width of the render texture
     * @param {number} [options.height=100] - The height of the render texture
     * @param {PIXI.SCALE_MODES} [options.scaleMode=PIXI.settings.SCALE_MODE] - See {@link PIXI.SCALE_MODES}
     *    for possible values
     * @param {number} [options.resolution=PIXI.settings.RESOLUTION] - The resolution / device pixel ratio of the texture
     *    being generated
     * @param {PIXI.MSAA_QUALITY} [options.multisample=PIXI.MSAA_QUALITY.NONE] - The number of samples of the frame buffer
     * @returns The new render texture
     */
    static create(options?: IBaseTextureOptions): RenderTexture;
}

/**
 * Texture pool, used by FilterSystem and plugins.
 *
 * Stores collection of temporary pow2 or screen-sized renderTextures
 *
 * If you use custom RenderTexturePool for your filters, you can use methods
 * `getFilterTexture` and `returnFilterTexture` same as in
 * @memberof PIXI
 */
export declare class RenderTexturePool {
    textureOptions: IBaseTextureOptions;
    /**
     * Allow renderTextures of the same size as screen, not just pow2
     *
     * Automatically sets to true after `setScreenSize`
     * @default false
     */
    enableFullScreen: boolean;
    texturePool: {
        [x in string | number]: RenderTexture[];
    };
    private _pixelsWidth;
    private _pixelsHeight;
    /**
     * @param textureOptions - options that will be passed to BaseRenderTexture constructor
     * @param {PIXI.SCALE_MODES} [textureOptions.scaleMode] - See {@link PIXI.SCALE_MODES} for possible values.
     */
    constructor(textureOptions?: IBaseTextureOptions);
    /**
     * Creates texture with params that were specified in pool constructor.
     * @param realWidth - Width of texture in pixels.
     * @param realHeight - Height of texture in pixels.
     * @param multisample - Number of samples of the framebuffer.
     */
    createTexture(realWidth: number, realHeight: number, multisample?: MSAA_QUALITY): RenderTexture;
    /**
     * Gets a Power-of-Two render texture or fullScreen texture
     * @param minWidth - The minimum width of the render texture.
     * @param minHeight - The minimum height of the render texture.
     * @param resolution - The resolution of the render texture.
     * @param multisample - Number of samples of the render texture.
     * @returns The new render texture.
     */
    getOptimalTexture(minWidth: number, minHeight: number, resolution?: number, multisample?: MSAA_QUALITY): RenderTexture;
    /**
     * Gets extra texture of the same size as input renderTexture
     *
     * `getFilterTexture(input, 0.5)` or `getFilterTexture(0.5, input)`
     * @param input - renderTexture from which size and resolution will be copied
     * @param resolution - override resolution of the renderTexture
     *  It overrides, it does not multiply
     * @param multisample - number of samples of the renderTexture
     */
    getFilterTexture(input: RenderTexture, resolution?: number, multisample?: MSAA_QUALITY): RenderTexture;
    /**
     * Place a render texture back into the pool.
     * @param renderTexture - The renderTexture to free
     */
    returnTexture(renderTexture: RenderTexture): void;
    /**
     * Alias for returnTexture, to be compliant with FilterSystem interface.
     * @param renderTexture - The renderTexture to free
     */
    returnFilterTexture(renderTexture: RenderTexture): void;
    /**
     * Clears the pool.
     * @param destroyTextures - Destroy all stored textures.
     */
    clear(destroyTextures?: boolean): void;
    /**
     * If screen size was changed, drops all screen-sized textures,
     * sets new screen size, sets `enableFullScreen` to true
     *
     * Size is measured in pixels, `renderer.view` can be passed here, not `renderer.screen`
     * @param size - Initial size of screen.
     */
    setScreenSize(size: ISize): void;
    /**
     * Key that is used to store fullscreen renderTextures in a pool
     * @constant
     */
    static SCREEN_KEY: number;
}

/**
 * System plugin to the renderer to manage render textures.
 *
 * Should be added after FramebufferSystem
 *
 * ### Frames
 *
 * The `RenderTextureSystem` holds a sourceFrame → destinationFrame projection. The following table explains the different
 * coordinate spaces used:
 *
 * | Frame                  | Description                                                      | Coordinate System                                       |
 * | ---------------------- | ---------------------------------------------------------------- | ------------------------------------------------------- |
 * | sourceFrame            | The rectangle inside of which display-objects are being rendered | **World Space**: The origin on the top-left             |
 * | destinationFrame       | The rectangle in the render-target (canvas or texture) into which contents should be rendered | If rendering to the canvas, this is in screen space and the origin is on the top-left. If rendering to a render-texture, this is in its base-texture's space with the origin on the bottom-left.  |
 * | viewportFrame          | The framebuffer viewport corresponding to the destination-frame  | **Window Coordinates**: The origin is always on the bottom-left. |
 * @memberof PIXI
 */
export declare class RenderTextureSystem implements ISystem {
    /** The clear background color as RGBA. */
    clearColor: number[];
    /**
     * List of masks for the {@link PIXI.StencilSystem}.
     * @readonly
     */
    defaultMaskStack: Array<MaskData>;
    /**
     * Render texture currently bound. {@code null} if rendering to the canvas.
     * @readonly
     */
    current: RenderTexture | null;
    /**
     * The source frame for the render-target's projection mapping.
     *
     * See {@link PIXI.ProjectionSystem#sourceFrame} for more details
     */
    readonly sourceFrame: Rectangle;
    /**
     * The destination frame for the render-target's projection mapping.
     *
     * See {@link PIXI.Projection#destinationFrame} for more details.
     */
    readonly destinationFrame: Rectangle;
    /**
     * The viewport frame for the render-target's viewport binding. This is equal to the destination-frame
     * for render-textures, while it is y-flipped when rendering to the screen (i.e. its origin is always on
     * the bottom-left).
     */
    readonly viewportFrame: Rectangle;
    private renderer;
    /**
     * @param renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    /**
     * Bind the current render texture.
     * @param renderTexture - RenderTexture to bind, by default its `null` - the screen.
     * @param sourceFrame - Part of world that is mapped to the renderTexture.
     * @param destinationFrame - Part of renderTexture, by default it has the same size as sourceFrame.
     */
    bind(renderTexture?: RenderTexture, sourceFrame?: Rectangle, destinationFrame?: Rectangle): void;
    /**
     * Erases the render texture and fills the drawing area with a colour.
     * @param clearColor - The color as rgba, default to use the renderer backgroundColor
     * @param [mask=BUFFER_BITS.COLOR | BUFFER_BITS.DEPTH] - Bitwise OR of masks
     *  that indicate the buffers to be cleared, by default COLOR and DEPTH buffers.
     */
    clear(clearColor?: number[], mask?: BUFFER_BITS): void;
    resize(): void;
    /** Resets render-texture state. */
    reset(): void;
    destroy(): void;
}

/**
 * Base resource class for textures that manages validation and uploading, depending on its type.
 *
 * Uploading of a base texture to the GPU is required.
 * @memberof PIXI
 */
export declare abstract class Resource {
    /** The url of the resource */
    src: string;
    /**
     * If resource has been destroyed.
     * @readonly
     * @default false
     */
    destroyed: boolean;
    /**
     * `true` if resource is created by BaseTexture
     * useful for doing cleanup with BaseTexture destroy
     * and not cleaning up resources that were created
     * externally.
     */
    internal: boolean;
    /** Internal width of the resource. */
    protected _width: number;
    /** Internal height of the resource. */
    protected _height: number;
    /**
     * Mini-runner for handling resize events
     * accepts 2 parameters: width, height
     * @member {Runner}
     * @private
     */
    protected onResize: Runner;
    /**
     * Mini-runner for handling update events
     * @member {Runner}
     * @private
     */
    protected onUpdate: Runner;
    /**
     * Handle internal errors, such as loading errors
     * accepts 1 param: error
     * @member {Runner}
     * @private
     */
    protected onError: Runner;
    /**
     * @param width - Width of the resource
     * @param height - Height of the resource
     */
    constructor(width?: number, height?: number);
    /**
     * Bind to a parent BaseTexture
     * @param baseTexture - Parent texture
     */
    bind(baseTexture: BaseTexture): void;
    /**
     * Unbind to a parent BaseTexture
     * @param baseTexture - Parent texture
     */
    unbind(baseTexture: BaseTexture): void;
    /**
     * Trigger a resize event
     * @param width - X dimension
     * @param height - Y dimension
     */
    resize(width: number, height: number): void;
    /**
     * Has been validated
     * @readonly
     */
    get valid(): boolean;
    /** Has been updated trigger event. */
    update(): void;
    /**
     * This can be overridden to start preloading a resource
     * or do any other prepare step.
     * @protected
     * @returns Handle the validate event
     */
    load(): Promise<Resource>;
    /**
     * The width of the resource.
     * @readonly
     */
    get width(): number;
    /**
     * The height of the resource.
     * @readonly
     */
    get height(): number;
    /**
     * Uploads the texture or returns false if it cant for some reason. Override this.
     * @param renderer - yeah, renderer!
     * @param baseTexture - the texture
     * @param glTexture - texture instance for this webgl context
     * @returns - true is success
     */
    abstract upload(renderer: Renderer, baseTexture: BaseTexture, glTexture: GLTexture): boolean;
    /**
     * Set the style, optional to override
     * @param _renderer - yeah, renderer!
     * @param _baseTexture - the texture
     * @param _glTexture - texture instance for this webgl context
     * @returns - `true` is success
     */
    style(_renderer: Renderer, _baseTexture: BaseTexture, _glTexture: GLTexture): boolean;
    /** Clean up anything, this happens when destroying is ready. */
    dispose(): void;
    /**
     * Call when destroying resource, unbind any BaseTexture object
     * before calling this method, as reference counts are maintained
     * internally.
     */
    destroy(): void;
    /**
     * Abstract, used to auto-detect resource type.
     * @param {*} _source - The source object
     * @param {string} _extension - The extension of source, if set
     */
    static test(_source: unknown, _extension?: string): boolean;
}

/**
 * @memberof PIXI
 * @namespace resources
 * @see PIXI
 * @deprecated since 6.0.0
 */
export declare const resources: {};

/**
 * System plugin to the renderer to manage scissor masking.
 *
 * Scissor masking discards pixels outside of a rectangle called the scissor box. The scissor box is in the framebuffer
 * viewport's space; however, the mask's rectangle is projected from world-space to viewport space automatically
 * by this system.
 * @memberof PIXI
 */
export declare class ScissorSystem extends AbstractMaskSystem {
    /**
     * @param {PIXI.Renderer} renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    getStackLength(): number;
    /**
     * evaluates _boundsTransformed, _scissorRect for MaskData
     * @param maskData
     */
    calcScissorRect(maskData: MaskData): void;
    private static isMatrixRotated;
    /**
     * Test, whether the object can be scissor mask with current renderer projection.
     * Calls "calcScissorRect()" if its true.
     * @param maskData - mask data
     * @returns whether Whether the object can be scissor mask
     */
    testScissor(maskData: MaskData): boolean;
    private roundFrameToPixels;
    /**
     * Applies the Mask and adds it to the current stencil stack.
     * <AUTHOR>
     * @param maskData - The mask data.
     */
    push(maskData: MaskData): void;
    /**
     * This should be called after a mask is popped off the mask stack. It will rebind the scissor box to be latest with the
     * last mask in the stack.
     *
     * This can also be called when you directly modify the scissor box and want to restore PixiJS state.
     * @param maskData - The mask data.
     */
    pop(maskData?: MaskData): void;
    /**
     * Setup renderer to use the current scissor data.
     * @private
     */
    _useCurrent(): void;
}

/**
 * A helper class for shaders.
 * @memberof PIXI
 */
export declare class Shader {
    /** Program that the shader uses. */
    program: Program;
    uniformGroup: UniformGroup;
    /**
     * Used internally to bind uniform buffer objects.
     * @ignore
     */
    uniformBindCount: number;
    disposeRunner: Runner;
    /**
     * @param program - The program the shader will use.
     * @param uniforms - Custom uniforms to use to augment the built-in ones.
     */
    constructor(program: Program, uniforms?: Dict<any>);
    checkUniformExists(name: string, group: UniformGroup): boolean;
    destroy(): void;
    /**
     * Shader uniform values, shortcut for `uniformGroup.uniforms`.
     * @readonly
     */
    get uniforms(): Dict<any>;
    /**
     * A short hand function to create a shader based of a vertex and fragment shader.
     * @param vertexSrc - The source of the vertex shader.
     * @param fragmentSrc - The source of the fragment shader.
     * @param uniforms - Custom uniforms to use to augment the built-in ones.
     * @returns A shiny new PixiJS shader!
     */
    static from(vertexSrc?: string, fragmentSrc?: string, uniforms?: Dict<any>): Shader;
}

/**
 * System plugin to the renderer to manage shaders.
 * @memberof PIXI
 */
export declare class ShaderSystem implements ISystem {
    /**
     * The current WebGL rendering context.
     * @member {WebGLRenderingContext}
     */
    protected gl: IRenderingContext;
    shader: Shader;
    program: Program;
    id: number;
    destroyed: boolean;
    /** Cache to holds the generated functions. Stored against UniformObjects unique signature. */
    private cache;
    private _uboCache;
    private renderer;
    /** @param renderer - The renderer this System works for. */
    constructor(renderer: Renderer);
    /**
     * Overrideable function by `@pixi/unsafe-eval` to silence
     * throwing an error if platform doesn't support unsafe-evals.
     * @private
     */
    systemCheck(): void;
    protected contextChange(gl: IRenderingContext): void;
    /**
     * Changes the current shader to the one given in parameter.
     * @param shader - the new shader
     * @param dontSync - false if the shader should automatically sync its uniforms.
     * @returns the glProgram that belongs to the shader.
     */
    bind(shader: Shader, dontSync?: boolean): GLProgram;
    /**
     * Uploads the uniforms values to the currently bound shader.
     * @param uniforms - the uniforms values that be applied to the current shader
     */
    setUniforms(uniforms: Dict<any>): void;
    /**
     * Syncs uniforms on the group
     * @param group - the uniform group to sync
     * @param syncData - this is data that is passed to the sync function and any nested sync functions
     */
    syncUniformGroup(group: UniformGroup, syncData?: any): void;
    /**
     * Overrideable by the @pixi/unsafe-eval package to use static syncUniforms instead.
     * @param group
     * @param glProgram
     * @param syncData
     */
    syncUniforms(group: UniformGroup, glProgram: GLProgram, syncData: any): void;
    createSyncGroups(group: UniformGroup): UniformsSyncCallback_2;
    /**
     * Syncs uniform buffers
     * @param group - the uniform buffer group to sync
     * @param name - the name of the uniform buffer
     */
    syncUniformBufferGroup(group: UniformGroup, name?: string): void;
    /**
     * Will create a function that uploads a uniform buffer using the STD140 standard.
     * The upload function will then be cached for future calls
     * If a group is manually managed, then a simple upload function is generated
     * @param group - the uniform buffer group to sync
     * @param glProgram - the gl program to attach the uniform bindings to
     * @param name - the name of the uniform buffer (must exist on the shader)
     */
    protected createSyncBufferGroup(group: UniformGroup, glProgram: GLProgram, name: string): UniformsSyncCallback_2;
    /**
     * Takes a uniform group and data and generates a unique signature for them.
     * @param group - The uniform group to get signature of
     * @param group.uniforms
     * @param uniformData - Uniform information generated by the shader
     * @param preFix
     * @returns Unique signature of the uniform group
     */
    private getSignature;
    /**
     * Returns the underlying GLShade rof the currently bound shader.
     *
     * This can be handy for when you to have a little more control over the setting of your uniforms.
     * @returns The glProgram for the currently bound Shader for this context
     */
    getGlProgram(): GLProgram;
    /**
     * Generates a glProgram version of the Shader provided.
     * @param shader - The shader that the glProgram will be based on.
     * @returns A shiny new glProgram!
     */
    generateProgram(shader: Shader): GLProgram;
    /** Resets ShaderSystem state, does not affect WebGL state. */
    reset(): void;
    /**
     * Disposes shader.
     * If disposing one equals with current shader, set current as null.
     * @param shader - Shader object
     */
    disposeShader(shader: Shader): void;
    /** Destroys this System and removes all its textures. */
    destroy(): void;
}

/**
 * This handles a Sprite acting as a mask, as opposed to a Graphic.
 *
 * WebGL only.
 * @memberof PIXI
 */
export declare class SpriteMaskFilter extends Filter {
    /** @private */
    _maskSprite: IMaskTarget;
    /** Mask matrix */
    maskMatrix: Matrix;
    /**
     * @param {PIXI.Sprite} sprite - The target sprite.
     */
    constructor(sprite: IMaskTarget);
    /**
     * @param vertexSrc - The source of the vertex shader.
     * @param fragmentSrc - The source of the fragment shader.
     * @param uniforms - Custom uniforms to use to augment the built-in ones.
     */
    constructor(vertexSrc?: string, fragmentSrc?: string, uniforms?: Dict<any>);
    /**
     * Sprite mask
     * @type {PIXI.DisplayObject}
     */
    get maskSprite(): IMaskTarget;
    set maskSprite(value: IMaskTarget);
    /**
     * Applies the filter
     * @param filterManager - The renderer to retrieve the filter from
     * @param input - The input render target.
     * @param output - The target to output to.
     * @param clearMode - Should the output be cleared before rendering to it.
     */
    apply(filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode: CLEAR_MODES): void;
}

/**
 * This is a WebGL state, and is is passed to {@link PIXI.StateSystem}.
 *
 * Each mesh rendered may require WebGL to be in a different state.
 * For example you may want different blend mode or to enable polygon offsets
 * @memberof PIXI
 */
export declare class State {
    data: number;
    _blendMode: BLEND_MODES;
    _polygonOffset: number;
    constructor();
    /**
     * Activates blending of the computed fragment color values.
     * @default true
     */
    get blend(): boolean;
    set blend(value: boolean);
    /**
     * Activates adding an offset to depth values of polygon's fragments
     * @default false
     */
    get offsets(): boolean;
    set offsets(value: boolean);
    /**
     * Activates culling of polygons.
     * @default false
     */
    get culling(): boolean;
    set culling(value: boolean);
    /**
     * Activates depth comparisons and updates to the depth buffer.
     * @default false
     */
    get depthTest(): boolean;
    set depthTest(value: boolean);
    /**
     * Enables or disables writing to the depth buffer.
     * @default true
     */
    get depthMask(): boolean;
    set depthMask(value: boolean);
    /**
     * Specifies whether or not front or back-facing polygons can be culled.
     * @default false
     */
    get clockwiseFrontFace(): boolean;
    set clockwiseFrontFace(value: boolean);
    /**
     * The blend mode to be applied when this state is set. Apply a value of `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.
     * Setting this mode to anything other than NO_BLEND will automatically switch blending on.
     * @default PIXI.BLEND_MODES.NORMAL
     */
    get blendMode(): BLEND_MODES;
    set blendMode(value: BLEND_MODES);
    /**
     * The polygon offset. Setting this property to anything other than 0 will automatically enable polygon offset fill.
     * @default 0
     */
    get polygonOffset(): number;
    set polygonOffset(value: number);
    toString(): string;
    static for2d(): State;
}

/**
 * System plugin to the renderer to manage WebGL state machines.
 * @memberof PIXI
 */
export declare class StateSystem implements ISystem {
    /**
     * State ID
     * @readonly
     */
    stateId: number;
    /**
     * Polygon offset
     * @readonly
     */
    polygonOffset: number;
    /**
     * Blend mode
     * @default PIXI.BLEND_MODES.NONE
     * @readonly
     */
    blendMode: BLEND_MODES;
    /** Whether current blend equation is different */
    protected _blendEq: boolean;
    /**
     * GL context
     * @member {WebGLRenderingContext}
     * @readonly
     */
    protected gl: IRenderingContext;
    protected blendModes: number[][];
    /**
     * Collection of calls
     * @member {Function[]}
     */
    protected readonly map: Array<(value: boolean) => void>;
    /**
     * Collection of check calls
     * @member {Function[]}
     */
    protected readonly checks: Array<(system: this, state: State) => void>;
    /**
     * Default WebGL State
     * @readonly
     */
    protected defaultState: State;
    constructor();
    contextChange(gl: IRenderingContext): void;
    /**
     * Sets the current state
     * @param {*} state - The state to set.
     */
    set(state: State): void;
    /**
     * Sets the state, when previous state is unknown.
     * @param {*} state - The state to set
     */
    forceState(state: State): void;
    /**
     * Sets whether to enable or disable blending.
     * @param value - Turn on or off WebGl blending.
     */
    setBlend(value: boolean): void;
    /**
     * Sets whether to enable or disable polygon offset fill.
     * @param value - Turn on or off webgl polygon offset testing.
     */
    setOffset(value: boolean): void;
    /**
     * Sets whether to enable or disable depth test.
     * @param value - Turn on or off webgl depth testing.
     */
    setDepthTest(value: boolean): void;
    /**
     * Sets whether to enable or disable depth mask.
     * @param value - Turn on or off webgl depth mask.
     */
    setDepthMask(value: boolean): void;
    /**
     * Sets whether to enable or disable cull face.
     * @param {boolean} value - Turn on or off webgl cull face.
     */
    setCullFace(value: boolean): void;
    /**
     * Sets the gl front face.
     * @param {boolean} value - true is clockwise and false is counter-clockwise
     */
    setFrontFace(value: boolean): void;
    /**
     * Sets the blend mode.
     * @param {number} value - The blend mode to set to.
     */
    setBlendMode(value: number): void;
    /**
     * Sets the polygon offset.
     * @param {number} value - the polygon offset
     * @param {number} scale - the polygon offset scale
     */
    setPolygonOffset(value: number, scale: number): void;
    /** Resets all the logic and disables the VAOs. */
    reset(): void;
    /**
     * Checks to see which updates should be checked based on which settings have been activated.
     *
     * For example, if blend is enabled then we should check the blend modes each time the state is changed
     * or if polygon fill is activated then we need to check if the polygon offset changes.
     * The idea is that we only check what we have too.
     * @param func - the checking function to add or remove
     * @param value - should the check function be added or removed.
     */
    updateCheck(func: (system: this, state: State) => void, value: boolean): void;
    /**
     * A private little wrapper function that we call to check the blend mode.
     * @param system - the System to perform the state check on
     * @param state - the state that the blendMode will pulled from
     */
    private static checkBlendMode;
    /**
     * A private little wrapper function that we call to check the polygon offset.
     * @param system - the System to perform the state check on
     * @param state - the state that the blendMode will pulled from
     */
    private static checkPolygonOffset;
    /**
     * @ignore
     */
    destroy(): void;
}

/**
 * System plugin to the renderer to manage stencils (used for masks).
 * @memberof PIXI
 */
export declare class StencilSystem extends AbstractMaskSystem {
    /**
     * @param renderer - The renderer this System works for.
     */
    constructor(renderer: Renderer);
    getStackLength(): number;
    /**
     * Applies the Mask and adds it to the current stencil stack.
     * @param maskData - The mask data
     */
    push(maskData: MaskData): void;
    /**
     * Pops stencil mask. MaskData is already removed from stack
     * @param {PIXI.DisplayObject} maskObject - object of popped mask data
     */
    pop(maskObject: IMaskTarget): void;
    /**
     * Setup renderer to use the current stencil data.
     * @private
     */
    _useCurrent(): void;
}

/**
 * Resource type for SVG elements and graphics.
 * @memberof PIXI
 */
export declare class SVGResource extends BaseImageResource {
    /** Base64 encoded SVG element or URL for SVG file. */
    readonly svg: string;
    /** The source scale to apply when rasterizing on load. */
    readonly scale: number;
    /** A width override for rasterization on load. */
    readonly _overrideWidth: number;
    /** A height override for rasterization on load. */
    readonly _overrideHeight: number;
    /** Call when completely loaded. */
    private _resolve;
    /** Promise when loading */
    private _load;
    /** Cross origin value to use */
    private _crossorigin?;
    /**
     * @param sourceBase64 - Base64 encoded SVG element or URL for SVG file.
     * @param {object} [options] - Options to use
     * @param {number} [options.scale=1] - Scale to apply to SVG. Overridden by...
     * @param {number} [options.width] - Rasterize SVG this wide. Aspect ratio preserved if height not specified.
     * @param {number} [options.height] - Rasterize SVG this high. Aspect ratio preserved if width not specified.
     * @param {boolean} [options.autoLoad=true] - Start loading right away.
     */
    constructor(sourceBase64: string, options?: ISVGResourceOptions);
    load(): Promise<SVGResource>;
    /** Loads an SVG image from `imageUrl` or `data URL`. */
    private _loadSvg;
    /**
     * Get size from an svg string using a regular expression.
     * @param svgString - a serialized svg element
     * @returns - image extension
     */
    static getSize(svgString?: string): ISize;
    /** Destroys this texture. */
    dispose(): void;
    /**
     * Used to auto-detect the type of resource.
     * @param {*} source - The source object
     * @param {string} extension - The extension of source, if set
     * @returns {boolean} - If the source is a SVG source or data file
     */
    static test(source: unknown, extension?: string): boolean;
    /**
     * Regular expression for SVG XML document.
     * @example &lt;?xml version="1.0" encoding="utf-8" ?&gt;&lt;!-- image/svg --&gt;&lt;svg
     * @readonly
     */
    static SVG_XML: RegExp;
    /**
     * Regular expression for SVG size.
     * @example &lt;svg width="100" height="100"&gt;&lt;/svg&gt;
     * @readonly
     */
    static SVG_SIZE: RegExp;
}

/**
 * Use the ISystem interface instead.
 * @deprecated since 6.1.0
 * @memberof PIXI
 */
export declare class System implements ISystem {
    /** Reference to the main renderer */
    renderer: Renderer;
    /**
     * @param renderer - Reference to Renderer
     */
    constructor(renderer: Renderer);
    /** Destroy and don't use after this. */
    destroy(): void;
}

/**
 * @memberof PIXI
 * @namespace systems
 * @see PIXI
 * @deprecated since 6.0.0
 */
export declare const systems: {};

export declare interface Texture extends GlobalMixins.Texture, EventEmitter {
}

/**
 * A texture stores the information that represents an image or part of an image.
 *
 * It cannot be added to the display list directly; instead use it as the texture for a Sprite.
 * If no frame is provided for a texture, then the whole image is used.
 *
 * You can directly create a texture from an image and then reuse it multiple times like this :
 *
 * ```js
 * let texture = PIXI.Texture.from('assets/image.png');
 * let sprite1 = new PIXI.Sprite(texture);
 * let sprite2 = new PIXI.Sprite(texture);
 * ```
 *
 * If you didnt pass the texture frame to constructor, it enables `noFrame` mode:
 * it subscribes on baseTexture events, it automatically resizes at the same time as baseTexture.
 *
 * Textures made from SVGs, loaded or not, cannot be used before the file finishes processing.
 * You can check for this by checking the sprite's _textureID property.
 * ```js
 * var texture = PIXI.Texture.from('assets/image.svg');
 * var sprite1 = new PIXI.Sprite(texture);
 * //sprite1._textureID should not be undefined if the texture has finished processing the SVG file
 * ```
 * You can use a ticker or rAF to ensure your sprites load the finished textures after processing. See issue #3068.
 * @memberof PIXI
 * @typeParam R - The BaseTexture's Resource type.
 */
export declare class Texture<R extends Resource = Resource> extends EventEmitter {
    /** The base texture that this texture uses. */
    baseTexture: BaseTexture<R>;
    /** This is the area of original texture, before it was put in atlas. */
    orig: Rectangle;
    /**
     * This is the trimmed area of original texture, before it was put in atlas
     * Please call `updateUvs()` after you change coordinates of `trim` manually.
     */
    trim: Rectangle;
    /** This will let the renderer know if the texture is valid. If it's not then it cannot be rendered. */
    valid: boolean;
    /**
     * Does this Texture have any frame data assigned to it?
     *
     * This mode is enabled automatically if no frame was passed inside constructor.
     *
     * In this mode texture is subscribed to baseTexture events, and fires `update` on any change.
     *
     * Beware, after loading or resize of baseTexture event can fired two times!
     * If you want more control, subscribe on baseTexture itself.
     *
     * ```js
     * texture.on('update', () => {});
     * ```
     *
     * Any assignment of `frame` switches off `noFrame` mode.
     */
    noFrame: boolean;
    /**
     * Anchor point that is used as default if sprite is created with this texture.
     * Changing the `defaultAnchor` at a later point of time will not update Sprite's anchor point.
     * @default {0,0}
     */
    defaultAnchor: Point;
    /** Default TextureMatrix instance for this texture. By default, that object is not created because its heavy. */
    uvMatrix: TextureMatrix;
    protected _rotate: number;
    /**
     * Update ID is observed by sprites and TextureMatrix instances.
     * Call updateUvs() to increment it.
     * @protected
     */
    _updateID: number;
    /**
     * This is the area of the BaseTexture image to actually copy to the Canvas / WebGL when rendering,
     * irrespective of the actual frame size or placement (which can be influenced by trimmed texture atlases)
     */
    _frame: Rectangle;
    /**
     * The WebGL UV data cache. Can be used as quad UV.
     * @protected
     */
    _uvs: TextureUvs;
    /**
     * The ids under which this Texture has been added to the texture cache. This is
     * automatically set as long as Texture.addToCache is used, but may not be set if a
     * Texture is added directly to the TextureCache array.
     */
    textureCacheIds: Array<string>;
    /**
     * @param baseTexture - The base texture source to create the texture from
     * @param frame - The rectangle frame of the texture to show
     * @param orig - The area of original texture
     * @param trim - Trimmed rectangle of original texture
     * @param rotate - indicates how the texture was rotated by texture packer. See {@link PIXI.groupD8}
     * @param anchor - Default anchor point used for sprite placement / rotation
     */
    constructor(baseTexture: BaseTexture<R>, frame?: Rectangle, orig?: Rectangle, trim?: Rectangle, rotate?: number, anchor?: IPointData);
    /**
     * Updates this texture on the gpu.
     *
     * Calls the TextureResource update.
     *
     * If you adjusted `frame` manually, please call `updateUvs()` instead.
     */
    update(): void;
    /**
     * Called when the base texture is updated
     * @protected
     * @param baseTexture - The base texture.
     */
    onBaseTextureUpdated(baseTexture: BaseTexture): void;
    /**
     * Destroys this texture
     * @param [destroyBase=false] - Whether to destroy the base texture as well
     */
    destroy(destroyBase?: boolean): void;
    /**
     * Creates a new texture object that acts the same as this one.
     * @returns - The new texture
     */
    clone(): Texture;
    /**
     * Updates the internal WebGL UV cache. Use it after you change `frame` or `trim` of the texture.
     * Call it after changing the frame
     */
    updateUvs(): void;
    /**
     * Helper function that creates a new Texture based on the source you provide.
     * The source can be - frame id, image url, video url, canvas element, video element, base texture
     * @param {string|HTMLImageElement|HTMLCanvasElement|HTMLVideoElement|PIXI.BaseTexture} source -
     *        Source or array of sources to create texture from
     * @param options - See {@link PIXI.BaseTexture}'s constructor for options.
     * @param {string} [options.pixiIdPrefix=pixiid] - If a source has no id, this is the prefix of the generated id
     * @param {boolean} [strict] - Enforce strict-mode, see {@link PIXI.settings.STRICT_TEXTURE_CACHE}.
     * @returns {PIXI.Texture} The newly created texture
     */
    static from<R extends Resource = Resource, RO = any>(source: TextureSource | TextureSource[], options?: IBaseTextureOptions<RO>, strict?: boolean): Texture<R>;
    /**
     * Useful for loading textures via URLs. Use instead of `Texture.from` because
     * it does a better job of handling failed URLs more effectively. This also ignores
     * `PIXI.settings.STRICT_TEXTURE_CACHE`. Works for Videos, SVGs, Images.
     * @param url - The remote URL or array of URLs to load.
     * @param options - Optional options to include
     * @returns - A Promise that resolves to a Texture.
     */
    static fromURL<R extends Resource = Resource, RO = any>(url: string | string[], options?: IBaseTextureOptions<RO>): Promise<Texture<R>>;
    /**
     * Create a new Texture with a BufferResource from a Float32Array.
     * RGBA values are floats from 0 to 1.
     * @param {Float32Array|Uint8Array} buffer - The optional array to use, if no data
     *        is provided, a new Float32Array is created.
     * @param width - Width of the resource
     * @param height - Height of the resource
     * @param options - See {@link PIXI.BaseTexture}'s constructor for options.
     * @returns - The resulting new BaseTexture
     */
    static fromBuffer(buffer: Float32Array | Uint8Array, width: number, height: number, options?: IBaseTextureOptions<ISize>): Texture<BufferResource>;
    /**
     * Create a texture from a source and add to the cache.
     * @param {HTMLImageElement|HTMLCanvasElement|string} source - The input source.
     * @param imageUrl - File name of texture, for cache and resolving resolution.
     * @param name - Human readable name for the texture cache. If no name is
     *        specified, only `imageUrl` will be used as the cache ID.
     * @param options
     * @returns - Output texture
     */
    static fromLoader<R extends Resource = Resource>(source: HTMLImageElement | HTMLCanvasElement | string, imageUrl: string, name?: string, options?: IBaseTextureOptions): Promise<Texture<R>>;
    /**
     * Adds a Texture to the global TextureCache. This cache is shared across the whole PIXI object.
     * @param texture - The Texture to add to the cache.
     * @param id - The id that the Texture will be stored against.
     */
    static addToCache(texture: Texture, id: string): void;
    /**
     * Remove a Texture from the global TextureCache.
     * @param texture - id of a Texture to be removed, or a Texture instance itself
     * @returns - The Texture that was removed
     */
    static removeFromCache(texture: string | Texture): Texture | null;
    /**
     * Returns resolution of baseTexture
     * @readonly
     */
    get resolution(): number;
    /**
     * The frame specifies the region of the base texture that this texture uses.
     * Please call `updateUvs()` after you change coordinates of `frame` manually.
     */
    get frame(): Rectangle;
    set frame(frame: Rectangle);
    /**
     * Indicates whether the texture is rotated inside the atlas
     * set to 2 to compensate for texture packer rotation
     * set to 6 to compensate for spine packer rotation
     * can be used to rotate or mirror sprites
     * See {@link PIXI.groupD8} for explanation
     */
    get rotate(): number;
    set rotate(rotate: number);
    /** The width of the Texture in pixels. */
    get width(): number;
    /** The height of the Texture in pixels. */
    get height(): number;
    /** Utility function for BaseTexture|Texture cast. */
    castToBaseTexture(): BaseTexture;
    private static _EMPTY;
    private static _WHITE;
    /** An empty texture, used often to not have to create multiple empty textures. Can not be destroyed. */
    static get EMPTY(): Texture<Resource>;
    /** A white texture of 16x16 size, used for graphics and other things Can not be destroyed. */
    static get WHITE(): Texture<CanvasResource>;
}

/**
 * System plugin to the renderer to manage texture garbage collection on the GPU,
 * ensuring that it does not get clogged up with textures that are no longer being used.
 * @memberof PIXI
 */
export declare class TextureGCSystem implements ISystem {
    /**
     * Count
     * @readonly
     */
    count: number;
    /**
     * Check count
     * @readonly
     */
    checkCount: number;
    /**
     * Maximum idle time, in seconds
     * @see PIXI.settings.GC_MAX_IDLE
     */
    maxIdle: number;
    /**
     * Maximum number of item to check
     * @see PIXI.settings.GC_MAX_CHECK_COUNT
     */
    checkCountMax: number;
    /**
     * Current garbage collection mode
     * @see PIXI.settings.GC_MODE
     */
    mode: GC_MODES;
    private renderer;
    /** @param renderer - The renderer this System works for. */
    constructor(renderer: Renderer);
    /**
     * Checks to see when the last time a texture was used
     * if the texture has not been used for a specified amount of time it will be removed from the GPU
     */
    protected postrender(): void;
    /**
     * Checks to see when the last time a texture was used
     * if the texture has not been used for a specified amount of time it will be removed from the GPU
     */
    run(): void;
    /**
     * Removes all the textures within the specified displayObject and its children from the GPU
     * @param {PIXI.DisplayObject} displayObject - the displayObject to remove the textures from.
     */
    unload(displayObject: IUnloadableTexture): void;
    destroy(): void;
}

/**
 * Class controls uv mapping from Texture normal space to BaseTexture normal space.
 *
 * Takes `trim` and `rotate` into account. May contain clamp settings for Meshes and TilingSprite.
 *
 * Can be used in Texture `uvMatrix` field, or separately, you can use different clamp settings on the same texture.
 * If you want to add support for texture region of certain feature or filter, that's what you're looking for.
 *
 * Takes track of Texture changes through `_lastTextureID` private field.
 * Use `update()` method call to track it from outside.
 * @see PIXI.Texture
 * @see PIXI.Mesh
 * @see PIXI.TilingSprite
 * @memberof PIXI
 */
export declare class TextureMatrix {
    /**
     * Matrix operation that converts texture region coords to texture coords
     * @readonly
     */
    mapCoord: Matrix;
    /**
     * Changes frame clamping
     * Works with TilingSprite and Mesh
     * Change to 1.5 if you texture has repeated right and bottom lines, that leads to smoother borders
     * @default 0
     */
    clampOffset: number;
    /**
     * Changes frame clamping
     * Works with TilingSprite and Mesh
     * Change to -0.5 to add a pixel to the edge, recommended for transparent trimmed textures in atlas
     * @default 0.5
     */
    clampMargin: number;
    /**
     * Clamp region for normalized coords, left-top pixel center in xy , bottom-right in zw.
     * Calculated based on clampOffset.
     */
    readonly uClampFrame: Float32Array;
    /** Normalized clamp offset. Calculated based on clampOffset. */
    readonly uClampOffset: Float32Array;
    /**
     * Tracks Texture frame changes.
     * @protected
     */
    _textureID: number;
    /**
     * Tracks Texture frame changes.
     * @protected
     */
    _updateID: number;
    _texture: Texture;
    /**
     * If texture size is the same as baseTexture.
     * @default false
     * @readonly
     */
    isSimple: boolean;
    /**
     * @param texture - observed texture
     * @param clampMargin - Changes frame clamping, 0.5 by default. Use -0.5 for extra border.
     */
    constructor(texture: Texture, clampMargin?: number);
    /** Texture property. */
    get texture(): Texture;
    set texture(value: Texture);
    /**
     * Multiplies uvs array to transform
     * @param uvs - mesh uvs
     * @param [out=uvs] - output
     * @returns - output
     */
    multiplyUvs(uvs: Float32Array, out?: Float32Array): Float32Array;
    /**
     * Updates matrices if texture was changed.
     * @param [forceUpdate=false] - if true, matrices will be updated any case
     * @returns - Whether or not it was updated
     */
    update(forceUpdate?: boolean): boolean;
}

export declare type TextureSource = string | BaseTexture | ImageSource;

/**
 * System plugin to the renderer to manage textures.
 * @memberof PIXI
 */
export declare class TextureSystem implements ISystem {
    /**
     * Bound textures.
     * @readonly
     */
    boundTextures: BaseTexture[];
    /**
     * List of managed textures.
     * @readonly
     */
    managedTextures: Array<BaseTexture>;
    /** Whether glTexture with int/uint sampler type was uploaded. */
    protected hasIntegerTextures: boolean;
    protected CONTEXT_UID: number;
    protected gl: IRenderingContext;
    protected internalFormats: {
        [type: number]: {
            [format: number]: number;
        };
    };
    protected webGLVersion: number;
    /**
     * BaseTexture value that shows that we don't know what is bound.
     * @readonly
     */
    protected unknownTexture: BaseTexture;
    /**
     * Did someone temper with textures state? We'll overwrite them when we need to unbind something.
     * @private
     */
    protected _unknownBoundTextures: boolean;
    /**
     * Current location.
     * @readonly
     */
    currentLocation: number;
    emptyTextures: {
        [key: number]: GLTexture;
    };
    private renderer;
    /**
     * @param renderer - The renderer this system works for.
     */
    constructor(renderer: Renderer);
    /** Sets up the renderer context and necessary buffers. */
    contextChange(): void;
    /**
     * Bind a texture to a specific location
     *
     * If you want to unbind something, please use `unbind(texture)` instead of `bind(null, textureLocation)`
     * @param texture - Texture to bind
     * @param [location=0] - Location to bind at
     */
    bind(texture: Texture | BaseTexture, location?: number): void;
    /** Resets texture location and bound textures Actual `bind(null, i)` calls will be performed at next `unbind()` call */
    reset(): void;
    /**
     * Unbind a texture.
     * @param texture - Texture to bind
     */
    unbind(texture?: BaseTexture): void;
    /**
     * Ensures that current boundTextures all have FLOAT sampler type,
     * see {@link PIXI.SAMPLER_TYPES} for explanation.
     * @param maxTextures - number of locations to check
     */
    ensureSamplerType(maxTextures: number): void;
    /**
     * Initialize a texture
     * @private
     * @param texture - Texture to initialize
     */
    initTexture(texture: BaseTexture): GLTexture;
    initTextureType(texture: BaseTexture, glTexture: GLTexture): void;
    /**
     * Update a texture
     * @private
     * @param {PIXI.BaseTexture} texture - Texture to initialize
     */
    updateTexture(texture: BaseTexture): void;
    /**
     * Deletes the texture from WebGL
     * @private
     * @param texture - the texture to destroy
     * @param [skipRemove=false] - Whether to skip removing the texture from the TextureManager.
     */
    destroyTexture(texture: BaseTexture | Texture, skipRemove?: boolean): void;
    /**
     * Update texture style such as mipmap flag
     * @private
     * @param {PIXI.BaseTexture} texture - Texture to update
     */
    updateTextureStyle(texture: BaseTexture): void;
    /**
     * Set style for texture
     * @private
     * @param texture - Texture to update
     * @param glTexture
     */
    setStyle(texture: BaseTexture, glTexture: GLTexture): void;
    destroy(): void;
}

/**
 * Stores a texture's frame in UV coordinates, in
 * which everything lies in the rectangle `[(0,0), (1,0),
 * (1,1), (0,1)]`.
 *
 * | Corner       | Coordinates |
 * |--------------|-------------|
 * | Top-Left     | `(x0,y0)`   |
 * | Top-Right    | `(x1,y1)`   |
 * | Bottom-Right | `(x2,y2)`   |
 * | Bottom-Left  | `(x3,y3)`   |
 * @protected
 * @memberof PIXI
 */
export declare class TextureUvs {
    /** X-component of top-left corner `(x0,y0)`. */
    x0: number;
    /** Y-component of top-left corner `(x0,y0)`. */
    y0: number;
    /** X-component of top-right corner `(x1,y1)`. */
    x1: number;
    /** Y-component of top-right corner `(x1,y1)`. */
    y1: number;
    /** X-component of bottom-right corner `(x2,y2)`. */
    x2: number;
    /** Y-component of bottom-right corner `(x2,y2)`. */
    y2: number;
    /** X-component of bottom-left corner `(x3,y3)`. */
    x3: number;
    /** Y-component of bottom-right corner `(x3,y3)`. */
    y3: number;
    uvsFloat32: Float32Array;
    constructor();
    /**
     * Sets the texture Uvs based on the given frame information.
     * @protected
     * @param frame - The frame of the texture
     * @param baseFrame - The base frame of the texture
     * @param rotate - Rotation of frame, see {@link PIXI.groupD8}
     */
    set(frame: Rectangle, baseFrame: ISize, rotate: number): void;
    toString(): string;
}

declare interface UBOElement {
    data: IUniformData;
    offset: number;
    dataLen: number;
    dirty: number;
}

/**
 * Uniform group holds uniform map and some ID's for work
 *
 * `UniformGroup` has two modes:
 *
 * 1: Normal mode
 * Normal mode will upload the uniforms with individual function calls as required
 *
 * 2: Uniform buffer mode
 * This mode will treat the uniforms as a uniform buffer. You can pass in either a buffer that you manually handle, or
 * or a generic object that PixiJS will automatically map to a buffer for you.
 * For maximum benefits, make Ubo UniformGroups static, and only update them each frame.
 *
 * Rules of UBOs:
 * - UBOs only work with WebGL2, so make sure you have a fallback!
 * - Only floats are supported (including vec[2,3,4], mat[2,3,4])
 * - Samplers cannot be used in ubo's (a GPU limitation)
 * - You must ensure that the object you pass in exactly matches in the shader ubo structure.
 * Otherwise, weirdness will ensue!
 * - The name of the ubo object added to the group must match exactly the name of the ubo in the shader.
 *
 * ```
 * // ubo in shader:
 * uniform myCoolData { // declaring a ubo..
 * mat4 uCoolMatrix;
 * float uFloatyMcFloatFace
 *
 *
 * // a new uniform buffer object..
 * const myCoolData = new UniformBufferGroup({
 *   uCoolMatrix: new Matrix(),
 *   uFloatyMcFloatFace: 23,
 * }}
 *
 * // build a shader...
 * const shader = Shader.from(srcVert, srcFrag, {
 *   myCoolData // name matches the ubo name in the shader. will be processed accordingly.
 * })
 *
 *  ```
 * @memberof PIXI
 */
export declare class UniformGroup<LAYOUT = Dict<any>> {
    /**
     * Uniform values
     * @member {object}
     */
    readonly uniforms: LAYOUT;
    /**
     * Its a group and not a single uniforms.
     * @default true
     */
    readonly group: boolean;
    /**
     * unique id
     * @protected
     */
    id: number;
    syncUniforms: Dict<UniformsSyncCallback_2>;
    /**
     * Dirty version
     * @protected
     */
    dirtyId: number;
    /** Flag for if uniforms wont be changed after creation. */
    static: boolean;
    /** Flags whether this group is treated like a uniform buffer object. */
    ubo: boolean;
    buffer?: Buffer_2;
    autoManage: boolean;
    /**
     * @param {object | Buffer} [uniforms] - Custom uniforms to use to augment the built-in ones. Or a pixi buffer.
     * @param isStatic - Uniforms wont be changed after creation.
     * @param isUbo - If true, will treat this uniform group as a uniform buffer object.
     */
    constructor(uniforms: LAYOUT | Buffer_2, isStatic?: boolean, isUbo?: boolean);
    update(): void;
    add(name: string, uniforms: Dict<any>, _static?: boolean): void;
    static from(uniforms: Dict<any> | Buffer_2, _static?: boolean, _ubo?: boolean): UniformGroup;
    /**
     * A short hand function for creating a static UBO UniformGroup.
     * @param uniforms - the ubo item
     * @param _static - should this be updated each time it is used? defaults to true here!
     */
    static uboFrom(uniforms: Dict<any> | Buffer_2, _static?: boolean): UniformGroup;
}

export declare const uniformParsers: IUniformParser[];

export declare type UniformsSyncCallback = (...args: any[]) => void;

declare type UniformsSyncCallback_2 = (...args: any[]) => void;

/**
 * String of the current PIXI version.
 * @memberof PIXI
 */
export declare const VERSION = "$_VERSION";

/**
 * Resource type for {@code HTMLVideoElement}.
 * @memberof PIXI
 */
export declare class VideoResource extends BaseImageResource {
    /** Override the source to be the video element. */
    source: HTMLVideoElement;
    /**
     * `true` to use PIXI.Ticker.shared to auto update the base texture.
     * @default true
     */
    protected _autoUpdate: boolean;
    /**
     * `true` if the instance is currently connected to PIXI.Ticker.shared to auto update the base texture.
     * @default false
     */
    protected _isConnectedToTicker: boolean;
    protected _updateFPS: number;
    protected _msToNextUpdate: number;
    /**
     * When set to true will automatically play videos used by this texture once
     * they are loaded. If false, it will not modify the playing state.
     * @default true
     */
    protected autoPlay: boolean;
    /**
     * Promise when loading.
     * @default null
     */
    private _load;
    /** Callback when completed with load. */
    private _resolve;
    /**
     * @param {HTMLVideoElement|object|string|Array<string|object>} source - Video element to use.
     * @param {object} [options] - Options to use
     * @param {boolean} [options.autoLoad=true] - Start loading the video immediately
     * @param {boolean} [options.autoPlay=true] - Start playing video immediately
     * @param {number} [options.updateFPS=0] - How many times a second to update the texture from the video.
     * Leave at 0 to update at every render.
     * @param {boolean} [options.crossorigin=true] - Load image using cross origin
     */
    constructor(source?: HTMLVideoElement | Array<string | IVideoResourceOptionsElement> | string, options?: IVideoResourceOptions);
    /**
     * Trigger updating of the texture.
     * @param _deltaTime - time delta since last tick
     */
    update(_deltaTime?: number): void;
    /**
     * Start preloading the video resource.
     * @returns {Promise<void>} Handle the validate event
     */
    load(): Promise<VideoResource>;
    /**
     * Handle video error events.
     * @param event
     */
    private _onError;
    /**
     * Returns true if the underlying source is playing.
     * @returns - True if playing.
     */
    private _isSourcePlaying;
    /**
     * Returns true if the underlying source is ready for playing.
     * @returns - True if ready.
     */
    private _isSourceReady;
    /** Runs the update loop when the video is ready to play. */
    private _onPlayStart;
    /** Fired when a pause event is triggered, stops the update loop. */
    private _onPlayStop;
    /** Fired when the video is loaded and ready to play. */
    private _onCanPlay;
    /** Destroys this texture. */
    dispose(): void;
    /** Should the base texture automatically update itself, set to true by default. */
    get autoUpdate(): boolean;
    set autoUpdate(value: boolean);
    /**
     * How many times a second to update the texture from the video. Leave at 0 to update at every render.
     * A lower fps can help performance, as updating the texture at 60fps on a 30ps video may not be efficient.
     */
    get updateFPS(): number;
    set updateFPS(value: number);
    /**
     * Used to auto-detect the type of resource.
     * @param {*} source - The source object
     * @param {string} extension - The extension of source, if set
     * @returns {boolean} `true` if video source
     */
    static test(source: unknown, extension?: string): source is HTMLVideoElement;
    /**
     * List of common video file extensions supported by VideoResource.
     * @readonly
     */
    static TYPES: Array<string>;
    /**
     * Map of video MIME types that can't be directly derived from file extensions.
     * @readonly
     */
    static MIME_TYPES: Dict<string>;
}

/**
 * Flexible wrapper around `ArrayBuffer` that also provides typed array views on demand.
 * @memberof PIXI
 */
export declare class ViewableBuffer {
    size: number;
    /** Underlying `ArrayBuffer` that holds all the data and is of capacity `this.size`. */
    rawBinaryData: ArrayBuffer;
    /** View on the raw binary data as a `Uint32Array`. */
    uint32View: Uint32Array;
    /** View on the raw binary data as a `Float32Array`. */
    float32View: Float32Array;
    private _int8View;
    private _uint8View;
    private _int16View;
    private _uint16View;
    private _int32View;
    /**
     * @param length - The size of the buffer in bytes.
     */
    constructor(length: number);
    /**
     * @param arrayBuffer - The source array buffer.
     */
    constructor(arrayBuffer: ArrayBuffer);
    /** View on the raw binary data as a `Int8Array`. */
    get int8View(): Int8Array;
    /** View on the raw binary data as a `Uint8Array`. */
    get uint8View(): Uint8Array;
    /**  View on the raw binary data as a `Int16Array`. */
    get int16View(): Int16Array;
    /** View on the raw binary data as a `Uint16Array`. */
    get uint16View(): Uint16Array;
    /** View on the raw binary data as a `Int32Array`. */
    get int32View(): Int32Array;
    /**
     * Returns the view of the given type.
     * @param type - One of `int8`, `uint8`, `int16`,
     *    `uint16`, `int32`, `uint32`, and `float32`.
     * @returns - typed array of given type
     */
    view(type: string): ITypedArray;
    /** Destroys all buffer references. Do not use after calling this. */
    destroy(): void;
    static sizeOf(type: string): number;
}

declare interface WEBGL_compressed_texture_atc {
    COMPRESSED_RGB_ATC_WEBGL: number;
    COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: number;
    COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: number;
}

declare interface WEBGL_compressed_texture_etc1_2 {
    COMPRESSED_RGB_ETC1_WEBGL: number;
}

declare interface WEBGL_compressed_texture_etc_2 {
    COMPRESSED_R11_EAC: number;
    COMPRESSED_SIGNED_R11_EAC: number;
    COMPRESSED_RG11_EAC: number;
    COMPRESSED_SIGNED_RG11_EAC: number;
    COMPRESSED_RGB8_ETC2: number;
    COMPRESSED_RGBA8_ETC2_EAC: number;
    COMPRESSED_SRGB8_ETC2: number;
    COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: number;
    COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: number;
    COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: number;
}

declare interface WEBGL_compressed_texture_pvrtc_2 {
    COMPRESSED_RGB_PVRTC_4BPPV1_IMG: number;
    COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: number;
    COMPRESSED_RGB_PVRTC_2BPPV1_IMG: number;
    COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: number;
}

declare interface WebGLExtensions {
    drawBuffers?: WEBGL_draw_buffers;
    depthTexture?: OES_texture_float;
    loseContext?: WEBGL_lose_context;
    vertexArrayObject?: OES_vertex_array_object;
    anisotropicFiltering?: EXT_texture_filter_anisotropic;
    uint32ElementIndex?: OES_element_index_uint;
    floatTexture?: OES_texture_float;
    floatTextureLinear?: OES_texture_float_linear;
    textureHalfFloat?: OES_texture_half_float;
    textureHalfFloatLinear?: OES_texture_half_float_linear;
    colorBufferFloat?: WEBGL_color_buffer_float;
    s3tc?: WEBGL_compressed_texture_s3tc;
    s3tc_sRGB?: WEBGL_compressed_texture_s3tc_srgb;
    etc?: WEBGL_compressed_texture_etc_2;
    etc1?: WEBGL_compressed_texture_etc1_2;
    pvrtc?: WEBGL_compressed_texture_pvrtc_2;
    atc?: WEBGL_compressed_texture_atc;
    astc?: WEBGL_compressed_texture_astc;
}


export * from "@pixi/extensions";

export { }
