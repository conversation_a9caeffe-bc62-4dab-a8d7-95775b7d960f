/*!
 * @pixi/polyfill - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/polyfill is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";var a=require("promise-polyfill"),e=require("object-assign");function r(a){return a&&"object"==typeof a&&"default"in a?a:{default:a}}var i=r(a),o=r(e);"undefined"==typeof globalThis&&("undefined"!=typeof self?self.globalThis=self:"undefined"!=typeof global&&(global.globalThis=global)),globalThis.Promise||(globalThis.Promise=i.default),Object.assign||(Object.assign=o.default);if(Date.now&&Date.prototype.getTime||(Date.now=function(){return(new Date).getTime()}),!globalThis.performance||!globalThis.performance.now){var n=Date.now();globalThis.performance||(globalThis.performance={}),globalThis.performance.now=function(){return Date.now()-n}}for(var l=Date.now(),t=["ms","moz","webkit","o"],s=0;s<t.length&&!globalThis.requestAnimationFrame;++s){var g=t[s];globalThis.requestAnimationFrame=globalThis[g+"RequestAnimationFrame"],globalThis.cancelAnimationFrame=globalThis[g+"CancelAnimationFrame"]||globalThis[g+"CancelRequestAnimationFrame"]}globalThis.requestAnimationFrame||(globalThis.requestAnimationFrame=function(a){if("function"!=typeof a)throw new TypeError(a+"is not a function");var e=Date.now(),r=16+l-e;return r<0&&(r=0),l=e,globalThis.self.setTimeout((function(){l=Date.now(),a(performance.now())}),r)}),globalThis.cancelAnimationFrame||(globalThis.cancelAnimationFrame=function(a){return clearTimeout(a)}),Math.sign||(Math.sign=function(a){return 0===(a=Number(a))||isNaN(a)?a:a>0?1:-1}),Number.isInteger||(Number.isInteger=function(a){return"number"==typeof a&&isFinite(a)&&Math.floor(a)===a}),globalThis.ArrayBuffer||(globalThis.ArrayBuffer=Array),globalThis.Float32Array||(globalThis.Float32Array=Array),globalThis.Uint32Array||(globalThis.Uint32Array=Array),globalThis.Uint16Array||(globalThis.Uint16Array=Array),globalThis.Uint8Array||(globalThis.Uint8Array=Array),globalThis.Int32Array||(globalThis.Int32Array=Array);
//# sourceMappingURL=polyfill.min.js.map
