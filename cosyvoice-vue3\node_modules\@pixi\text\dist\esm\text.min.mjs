/*!
 * @pixi/text - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/text is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{Sprite as t}from"@pixi/sprite";import{Texture as e}from"@pixi/core";import{settings as i}from"@pixi/settings";import{Rectangle as n}from"@pixi/math";import{hex2string as r,hex2rgb as o,string2hex as a,trimCanvas as s,sign as h}from"@pixi/utils";var l,c=function(t,e){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},c(t,e)};!function(t){t[t.LINEAR_VERTICAL=0]="LINEAR_VERTICAL",t[t.LINEAR_HORIZONTAL=1]="LINEAR_HORIZONTAL"}(l||(l={}));var f={align:"left",breakWords:!1,dropShadow:!1,dropShadowAlpha:1,dropShadowAngle:Math.PI/6,dropShadowBlur:0,dropShadowColor:"black",dropShadowDistance:5,fill:"black",fillGradientType:l.LINEAR_VERTICAL,fillGradientStops:[],fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",letterSpacing:0,lineHeight:0,lineJoin:"miter",miterLimit:10,padding:0,stroke:"black",strokeThickness:0,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100,leading:0},u=["serif","sans-serif","monospace","cursive","fantasy","system-ui"],p=function(){function t(t){this.styleID=0,this.reset(),y(this,t,t)}return t.prototype.clone=function(){var e={};return y(e,this,f),new t(e)},t.prototype.reset=function(){y(this,f,f)},Object.defineProperty(t.prototype,"align",{get:function(){return this._align},set:function(t){this._align!==t&&(this._align=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"breakWords",{get:function(){return this._breakWords},set:function(t){this._breakWords!==t&&(this._breakWords=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadow",{get:function(){return this._dropShadow},set:function(t){this._dropShadow!==t&&(this._dropShadow=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowAlpha",{get:function(){return this._dropShadowAlpha},set:function(t){this._dropShadowAlpha!==t&&(this._dropShadowAlpha=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowAngle",{get:function(){return this._dropShadowAngle},set:function(t){this._dropShadowAngle!==t&&(this._dropShadowAngle=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowBlur",{get:function(){return this._dropShadowBlur},set:function(t){this._dropShadowBlur!==t&&(this._dropShadowBlur=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowColor",{get:function(){return this._dropShadowColor},set:function(t){var e=g(t);this._dropShadowColor!==e&&(this._dropShadowColor=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowDistance",{get:function(){return this._dropShadowDistance},set:function(t){this._dropShadowDistance!==t&&(this._dropShadowDistance=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fill",{get:function(){return this._fill},set:function(t){var e=g(t);this._fill!==e&&(this._fill=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fillGradientType",{get:function(){return this._fillGradientType},set:function(t){this._fillGradientType!==t&&(this._fillGradientType=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fillGradientStops",{get:function(){return this._fillGradientStops},set:function(t){(function(t,e){if(!Array.isArray(t)||!Array.isArray(e))return!1;if(t.length!==e.length)return!1;for(var i=0;i<t.length;++i)if(t[i]!==e[i])return!1;return!0})(this._fillGradientStops,t)||(this._fillGradientStops=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontFamily",{get:function(){return this._fontFamily},set:function(t){this.fontFamily!==t&&(this._fontFamily=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontSize",{get:function(){return this._fontSize},set:function(t){this._fontSize!==t&&(this._fontSize=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontStyle",{get:function(){return this._fontStyle},set:function(t){this._fontStyle!==t&&(this._fontStyle=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontVariant",{get:function(){return this._fontVariant},set:function(t){this._fontVariant!==t&&(this._fontVariant=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontWeight",{get:function(){return this._fontWeight},set:function(t){this._fontWeight!==t&&(this._fontWeight=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"letterSpacing",{get:function(){return this._letterSpacing},set:function(t){this._letterSpacing!==t&&(this._letterSpacing=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lineHeight",{get:function(){return this._lineHeight},set:function(t){this._lineHeight!==t&&(this._lineHeight=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"leading",{get:function(){return this._leading},set:function(t){this._leading!==t&&(this._leading=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lineJoin",{get:function(){return this._lineJoin},set:function(t){this._lineJoin!==t&&(this._lineJoin=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"miterLimit",{get:function(){return this._miterLimit},set:function(t){this._miterLimit!==t&&(this._miterLimit=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"padding",{get:function(){return this._padding},set:function(t){this._padding!==t&&(this._padding=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"stroke",{get:function(){return this._stroke},set:function(t){var e=g(t);this._stroke!==e&&(this._stroke=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"strokeThickness",{get:function(){return this._strokeThickness},set:function(t){this._strokeThickness!==t&&(this._strokeThickness=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"textBaseline",{get:function(){return this._textBaseline},set:function(t){this._textBaseline!==t&&(this._textBaseline=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"trim",{get:function(){return this._trim},set:function(t){this._trim!==t&&(this._trim=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"whiteSpace",{get:function(){return this._whiteSpace},set:function(t){this._whiteSpace!==t&&(this._whiteSpace=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"wordWrap",{get:function(){return this._wordWrap},set:function(t){this._wordWrap!==t&&(this._wordWrap=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"wordWrapWidth",{get:function(){return this._wordWrapWidth},set:function(t){this._wordWrapWidth!==t&&(this._wordWrapWidth=t,this.styleID++)},enumerable:!1,configurable:!0}),t.prototype.toFontString=function(){var t="number"==typeof this.fontSize?this.fontSize+"px":this.fontSize,e=this.fontFamily;Array.isArray(this.fontFamily)||(e=this.fontFamily.split(","));for(var i=e.length-1;i>=0;i--){var n=e[i].trim();!/([\"\'])[^\'\"]+\1/.test(n)&&u.indexOf(n)<0&&(n='"'+n+'"'),e[i]=n}return this.fontStyle+" "+this.fontVariant+" "+this.fontWeight+" "+t+" "+e.join(",")},t}();function d(t){return"number"==typeof t?r(t):("string"==typeof t&&0===t.indexOf("0x")&&(t=t.replace("0x","#")),t)}function g(t){if(Array.isArray(t)){for(var e=0;e<t.length;++e)t[e]=d(t[e]);return t}return d(t)}function y(t,e,i){for(var n in i)Array.isArray(e[n])?t[n]=e[n].slice():t[n]=e[n]}var _={willReadFrequently:!0},b=function(){function t(t,e,i,n,r,o,a,s,h){this.text=t,this.style=e,this.width=i,this.height=n,this.lines=r,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=s,this.fontProperties=h}return t.measureText=function(e,i,n,r){void 0===r&&(r=t._canvas),n=null==n?i.wordWrap:n;var o=i.toFontString(),a=t.measureFont(o);0===a.fontSize&&(a.fontSize=i.fontSize,a.ascent=i.fontSize);var s=r.getContext("2d",_);s.font=o;for(var h=(n?t.wordWrap(e,i,r):e).split(/(?:\r\n|\r|\n)/),l=new Array(h.length),c=0,f=0;f<h.length;f++){var u=s.measureText(h[f]).width+(h[f].length-1)*i.letterSpacing;l[f]=u,c=Math.max(c,u)}var p=c+i.strokeThickness;i.dropShadow&&(p+=i.dropShadowDistance);var d=i.lineHeight||a.fontSize+i.strokeThickness,g=Math.max(d,a.fontSize+i.strokeThickness)+(h.length-1)*(d+i.leading);return i.dropShadow&&(g+=i.dropShadowDistance),new t(e,i,p,g,h,l,d+i.leading,c,a)},t.wordWrap=function(e,i,n){void 0===n&&(n=t._canvas);for(var r=n.getContext("2d",_),o=0,a="",s="",h=Object.create(null),l=i.letterSpacing,c=i.whiteSpace,f=t.collapseSpaces(c),u=t.collapseNewlines(c),p=!f,d=i.wordWrapWidth+l,g=t.tokenize(e),y=0;y<g.length;y++){var b=g[y];if(t.isNewline(b)){if(!u){s+=t.addLine(a),p=!f,a="",o=0;continue}b=" "}if(f){var S=t.isBreakingSpace(b),m=t.isBreakingSpace(a[a.length-1]);if(S&&m)continue}var w=t.getFromCache(b,l,h,r);if(w>d)if(""!==a&&(s+=t.addLine(a),a="",o=0),t.canBreakWords(b,i.breakWords))for(var v=t.wordWrapSplit(b),x=0;x<v.length;x++){for(var I=v[x],k=1;v[x+k];){var T=v[x+k],L=I[I.length-1];if(t.canBreakChars(L,T,b,x,i.breakWords))break;I+=T,k++}x+=I.length-1;var O=t.getFromCache(I,l,h,r);O+o>d&&(s+=t.addLine(a),p=!1,a="",o=0),a+=I,o+=O}else{a.length>0&&(s+=t.addLine(a),a="",o=0);var A=y===g.length-1;s+=t.addLine(b,!A),p=!1,a="",o=0}else w+o>d&&(p=!1,s+=t.addLine(a),a="",o=0),(a.length>0||!t.isBreakingSpace(b)||p)&&(a+=b,o+=w)}return s+=t.addLine(a,!1)},t.addLine=function(e,i){return void 0===i&&(i=!0),e=t.trimRight(e),e=i?e+"\n":e},t.getFromCache=function(t,e,i,n){var r=i[t];if("number"!=typeof r){var o=t.length*e;r=n.measureText(t).width+o,i[t]=r}return r},t.collapseSpaces=function(t){return"normal"===t||"pre-line"===t},t.collapseNewlines=function(t){return"normal"===t},t.trimRight=function(e){if("string"!=typeof e)return"";for(var i=e.length-1;i>=0;i--){var n=e[i];if(!t.isBreakingSpace(n))break;e=e.slice(0,-1)}return e},t.isNewline=function(e){return"string"==typeof e&&t._newlines.indexOf(e.charCodeAt(0))>=0},t.isBreakingSpace=function(e,i){return"string"==typeof e&&t._breakingSpaces.indexOf(e.charCodeAt(0))>=0},t.tokenize=function(e){var i=[],n="";if("string"!=typeof e)return i;for(var r=0;r<e.length;r++){var o=e[r],a=e[r+1];t.isBreakingSpace(o,a)||t.isNewline(o)?(""!==n&&(i.push(n),n=""),i.push(o)):n+=o}return""!==n&&i.push(n),i},t.canBreakWords=function(t,e){return e},t.canBreakChars=function(t,e,i,n,r){return!0},t.wordWrapSplit=function(t){return t.split("")},t.measureFont=function(e){if(t._fonts[e])return t._fonts[e];var i={ascent:0,descent:0,fontSize:0},n=t._canvas,r=t._context;r.font=e;var o=t.METRICS_STRING+t.BASELINE_SYMBOL,a=Math.ceil(r.measureText(o).width),s=Math.ceil(r.measureText(t.BASELINE_SYMBOL).width),h=Math.ceil(t.HEIGHT_MULTIPLIER*s);s=s*t.BASELINE_MULTIPLIER|0,n.width=a,n.height=h,r.fillStyle="#f00",r.fillRect(0,0,a,h),r.font=e,r.textBaseline="alphabetic",r.fillStyle="#000",r.fillText(o,0,s);var l=r.getImageData(0,0,a,h).data,c=l.length,f=4*a,u=0,p=0,d=!1;for(u=0;u<s;++u){for(var g=0;g<f;g+=4)if(255!==l[p+g]){d=!0;break}if(d)break;p+=f}for(i.ascent=s-u,p=c-f,d=!1,u=h;u>s;--u){for(g=0;g<f;g+=4)if(255!==l[p+g]){d=!0;break}if(d)break;p-=f}return i.descent=u-s,i.fontSize=i.ascent+i.descent,t._fonts[e]=i,i},t.clearMetrics=function(e){void 0===e&&(e=""),e?delete t._fonts[e]:t._fonts={}},Object.defineProperty(t,"_canvas",{get:function(){if(!t.__canvas){var e=void 0;try{var n=new OffscreenCanvas(0,0),r=n.getContext("2d",_);if(r&&r.measureText)return t.__canvas=n,n;e=i.ADAPTER.createCanvas()}catch(t){e=i.ADAPTER.createCanvas()}e.width=e.height=10,t.__canvas=e}return t.__canvas},enumerable:!1,configurable:!0}),Object.defineProperty(t,"_context",{get:function(){return t.__context||(t.__context=t._canvas.getContext("2d",_)),t.__context},enumerable:!1,configurable:!0}),t}();b._fonts={},b.METRICS_STRING="|ÉqÅ",b.BASELINE_SYMBOL="M",b.BASELINE_MULTIPLIER=1.4,b.HEIGHT_MULTIPLIER=2,b._newlines=[10,13],b._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];var S={texture:!0,children:!1,baseTexture:!0},m=function(t){function r(r,o,a){var s=this,h=!1;a||(a=i.ADAPTER.createCanvas(),h=!0),a.width=3,a.height=3;var l=e.from(a);return l.orig=new n,l.trim=new n,(s=t.call(this,l)||this)._ownCanvas=h,s.canvas=a,s.context=a.getContext("2d",{willReadFrequently:!0}),s._resolution=i.RESOLUTION,s._autoResolution=!0,s._text=null,s._style=null,s._styleListener=null,s._font="",s.text=r,s.style=o,s.localStyleID=-1,s}return function(t,e){function i(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(r,t),r.prototype.updateText=function(t){var e=this._style;if(this.localStyleID!==e.styleID&&(this.dirty=!0,this.localStyleID=e.styleID),this.dirty||!t){this._font=this._style.toFontString();var i,n,s=this.context,h=b.measureText(this._text||" ",this._style,this._style.wordWrap,this.canvas),l=h.width,c=h.height,f=h.lines,u=h.lineHeight,p=h.lineWidths,d=h.maxLineWidth,g=h.fontProperties;this.canvas.width=Math.ceil(Math.ceil(Math.max(1,l)+2*e.padding)*this._resolution),this.canvas.height=Math.ceil(Math.ceil(Math.max(1,c)+2*e.padding)*this._resolution),s.scale(this._resolution,this._resolution),s.clearRect(0,0,this.canvas.width,this.canvas.height),s.font=this._font,s.lineWidth=e.strokeThickness,s.textBaseline=e.textBaseline,s.lineJoin=e.lineJoin,s.miterLimit=e.miterLimit;for(var y=e.dropShadow?2:1,_=0;_<y;++_){var S=e.dropShadow&&0===_,m=S?Math.ceil(Math.max(1,c)+2*e.padding):0,w=m*this._resolution;if(S){s.fillStyle="black",s.strokeStyle="black";var v=e.dropShadowColor,x=o("number"==typeof v?v:a(v)),I=e.dropShadowBlur*this._resolution,k=e.dropShadowDistance*this._resolution;s.shadowColor="rgba("+255*x[0]+","+255*x[1]+","+255*x[2]+","+e.dropShadowAlpha+")",s.shadowBlur=I,s.shadowOffsetX=Math.cos(e.dropShadowAngle)*k,s.shadowOffsetY=Math.sin(e.dropShadowAngle)*k+w}else s.fillStyle=this._generateFillStyle(e,f,h),s.strokeStyle=e.stroke,s.shadowColor="black",s.shadowBlur=0,s.shadowOffsetX=0,s.shadowOffsetY=0;var T=(u-g.fontSize)/2;(!r.nextLineHeightBehavior||u-g.fontSize<0)&&(T=0);for(var L=0;L<f.length;L++)i=e.strokeThickness/2,n=e.strokeThickness/2+L*u+g.ascent+T,"right"===e.align?i+=d-p[L]:"center"===e.align&&(i+=(d-p[L])/2),e.stroke&&e.strokeThickness&&this.drawLetterSpacing(f[L],i+e.padding,n+e.padding-m,!0),e.fill&&this.drawLetterSpacing(f[L],i+e.padding,n+e.padding-m)}this.updateTexture()}},r.prototype.drawLetterSpacing=function(t,e,i,n){void 0===n&&(n=!1);var o=this._style.letterSpacing,a=r.experimentalLetterSpacing&&("letterSpacing"in CanvasRenderingContext2D.prototype||"textLetterSpacing"in CanvasRenderingContext2D.prototype);if(0===o||a)return a&&(this.context.letterSpacing=o,this.context.textLetterSpacing=o),void(n?this.context.strokeText(t,e,i):this.context.fillText(t,e,i));for(var s=e,h=Array.from?Array.from(t):t.split(""),l=this.context.measureText(t).width,c=0,f=0;f<h.length;++f){var u=h[f];n?this.context.strokeText(u,s,i):this.context.fillText(u,s,i);for(var p="",d=f+1;d<h.length;++d)p+=h[d];s+=l-(c=this.context.measureText(p).width)+o,l=c}},r.prototype.updateTexture=function(){var t=this.canvas;if(this._style.trim){var e=s(t);e.data&&(t.width=e.width,t.height=e.height,this.context.putImageData(e.data,0,0))}var i=this._texture,n=this._style,r=n.trim?0:n.padding,o=i.baseTexture;i.trim.width=i._frame.width=t.width/this._resolution,i.trim.height=i._frame.height=t.height/this._resolution,i.trim.x=-r,i.trim.y=-r,i.orig.width=i._frame.width-2*r,i.orig.height=i._frame.height-2*r,this._onTextureUpdate(),o.setRealSize(t.width,t.height,this._resolution),i.updateUvs(),this.dirty=!1},r.prototype._render=function(e){this._autoResolution&&this._resolution!==e.resolution&&(this._resolution=e.resolution,this.dirty=!0),this.updateText(!0),t.prototype._render.call(this,e)},r.prototype.updateTransform=function(){this.updateText(!0),t.prototype.updateTransform.call(this)},r.prototype.getBounds=function(e,i){return this.updateText(!0),-1===this._textureID&&(e=!1),t.prototype.getBounds.call(this,e,i)},r.prototype.getLocalBounds=function(e){return this.updateText(!0),t.prototype.getLocalBounds.call(this,e)},r.prototype._calculateBounds=function(){this.calculateVertices(),this._bounds.addQuad(this.vertexData)},r.prototype._generateFillStyle=function(t,e,i){var n,r=t.fill;if(!Array.isArray(r))return r;if(1===r.length)return r[0];var o=t.dropShadow?t.dropShadowDistance:0,a=t.padding||0,s=this.canvas.width/this._resolution-o-2*a,h=this.canvas.height/this._resolution-o-2*a,c=r.slice(),f=t.fillGradientStops.slice();if(!f.length)for(var u=c.length+1,p=1;p<u;++p)f.push(p/u);if(c.unshift(r[0]),f.unshift(0),c.push(r[r.length-1]),f.push(1),t.fillGradientType===l.LINEAR_VERTICAL){n=this.context.createLinearGradient(s/2,a,s/2,h+a);var d=i.fontProperties.fontSize+t.strokeThickness;for(p=0;p<e.length;p++){var g=i.lineHeight*(p-1)+d,y=i.lineHeight*p,_=y;p>0&&g>y&&(_=(y+g)/2);var b=y+d,S=i.lineHeight*(p+1),m=b;p+1<e.length&&S<b&&(m=(b+S)/2);for(var w=(m-_)/h,v=0;v<c.length;v++){var x=0;x="number"==typeof f[v]?f[v]:v/c.length;var I=Math.min(1,Math.max(0,_/h+x*w));I=Number(I.toFixed(5)),n.addColorStop(I,c[v])}}}else{n=this.context.createLinearGradient(a,h/2,s+a,h/2);var k=c.length+1,T=1;for(p=0;p<c.length;p++){var L=void 0;L="number"==typeof f[p]?f[p]:T/k,n.addColorStop(L,c[p]),T++}}return n},r.prototype.destroy=function(e){"boolean"==typeof e&&(e={children:e}),e=Object.assign({},S,e),t.prototype.destroy.call(this,e),this._ownCanvas&&(this.canvas.height=this.canvas.width=0),this.context=null,this.canvas=null,this._style=null},Object.defineProperty(r.prototype,"width",{get:function(){return this.updateText(!0),Math.abs(this.scale.x)*this._texture.orig.width},set:function(t){this.updateText(!0);var e=h(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"height",{get:function(){return this.updateText(!0),Math.abs(this.scale.y)*this._texture.orig.height},set:function(t){this.updateText(!0);var e=h(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"style",{get:function(){return this._style},set:function(t){t=t||{},this._style=t instanceof p?t:new p(t),this.localStyleID=-1,this.dirty=!0},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"text",{get:function(){return this._text},set:function(t){t=String(null==t?"":t),this._text!==t&&(this._text=t,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"resolution",{get:function(){return this._resolution},set:function(t){this._autoResolution=!1,this._resolution!==t&&(this._resolution=t,this.dirty=!0)},enumerable:!1,configurable:!0}),r.nextLineHeightBehavior=!1,r.experimentalLetterSpacing=!1,r}(t);export{l as TEXT_GRADIENT,m as Text,b as TextMetrics,p as TextStyle};
//# sourceMappingURL=text.min.mjs.map
