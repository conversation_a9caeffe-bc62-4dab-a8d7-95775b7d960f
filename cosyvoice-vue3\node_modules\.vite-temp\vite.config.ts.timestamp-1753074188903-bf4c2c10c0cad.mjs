// vite.config.ts
import { defineConfig } from "file:///H:/AI/CosyVoice/cosyvoice-vue3/node_modules/vite/dist/node/index.js";
import vue from "file:///H:/AI/CosyVoice/cosyvoice-vue3/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import Pages from "file:///H:/AI/CosyVoice/cosyvoice-vue3/node_modules/vite-plugin-pages/dist/index.js";
import AutoImport from "file:///H:/AI/CosyVoice/cosyvoice-vue3/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///H:/AI/CosyVoice/cosyvoice-vue3/node_modules/unplugin-vue-components/dist/vite.js";
import { NaiveUiResolver } from "file:///H:/AI/CosyVoice/cosyvoice-vue3/node_modules/unplugin-vue-components/dist/resolvers.js";
import { networkInterfaces } from "os";
var __vite_injected_original_dirname = "H:\\AI\\CosyVoice\\cosyvoice-vue3";
function getLocalNetworkIP() {
  const nets = networkInterfaces();
  const privateIPRanges = [
    /^192\.168\./,
    // ***********/16
    /^10\./,
    // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
    // **********/12
    /^169\.254\./
    // ***********/16 (链路本地)
  ];
  for (const name of Object.keys(nets)) {
    for (const net of nets[name] || []) {
      if (net.family === "IPv4" && !net.internal) {
        if (privateIPRanges.some((range) => range.test(net.address))) {
          console.log(`\u{1F50D} \u53D1\u73B0\u5C40\u57DF\u7F51IP: ${net.address} (\u7F51\u5361: ${name})`);
          return net.address;
        }
      }
    }
  }
  console.log("\u26A0\uFE0F \u672A\u627E\u5230\u5C40\u57DF\u7F51IP\uFF0C\u4F7F\u7528localhost");
  return "127.0.0.1";
}
var localIP = getLocalNetworkIP();
console.log(`\u{1F310} \u68C0\u6D4B\u5230\u5C40\u57DF\u7F51IP: ${localIP}`);
var comfyuiTarget = `http://${localIP}:8188`;
var realtimeTarget = `http://localhost:7860`;
console.log(`\u{1F527} ComfyUI\u4EE3\u7406\u76EE\u6807\u8BBE\u7F6E\u4E3A: ${comfyuiTarget}`);
console.log(`\u{1F527} \u5B9E\u65F6\u5BF9\u8BDD\u4EE3\u7406\u76EE\u6807\u8BBE\u7F6E\u4E3A: ${realtimeTarget}`);
var vite_config_default = defineConfig({
  plugins: [
    vue(),
    // dynamicCSPPlugin(), // 🔒 暂时禁用，使用静态CSP策略
    Pages({
      dirs: "src/pages"
    }),
    AutoImport({
      imports: [
        "vue",
        "vue-router",
        "@vueuse/core",
        "pinia",
        {
          "naive-ui": [
            "useDialog",
            "useMessage",
            "useNotification",
            "useLoadingBar"
          ]
        }
      ],
      dts: true
    }),
    Components({
      resolvers: [NaiveUiResolver()],
      dts: true
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "src"),
      // 添加Live2D Framework别名
      "@framework": path.resolve(__vite_injected_original_dirname, "public/live2d-sdk/Framework/src"),
      "@live2d-core": path.resolve(__vite_injected_original_dirname, "public/live2d-sdk/Core")
    }
  },
  server: {
    host: "0.0.0.0",
    // 允许外部访问
    port: 5173,
    strictPort: true,
    // 强制使用5173端口，如果被占用就报错而不是切换端口
    cors: {
      origin: "*",
      // 允许所有来源的CORS请求
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"]
    },
    proxy: {
      // ComfyUI相关API代理到8188端口
      "/api/comfyui": {
        target: process.env.VITE_API_TARGET || comfyuiTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/comfyui/, "")
      },
      // 工作流API代理到8188端口
      "/api/workflow": {
        target: process.env.VITE_API_TARGET || comfyuiTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/workflow/, "")
      },
      // TTS和漫画生成API代理到9880端口
      "/api/tts": {
        target: "http://127.0.0.1:9880",
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/tts/, "")
      },
      "/api/comic": {
        target: "http://127.0.0.1:9880",
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/comic/, "/comic")
      },
      // LMStudio API 代理到1234端口
      "/api/lmstudio": {
        target: "http://127.0.0.1:1234",
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/lmstudio/, "")
      },
      // 实时对话API代理到7860端口 (IndexTTS实时对话后端)
      "/api/realtime": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/realtime/, "/api/realtime")
      },
      // 健康检查和其他基础API代理到7860端口
      "/api/health": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/health/, "/api/health")
      },
      "/api/status": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/status/, "/api/status")
      },
      // LLM API代理到7860端口
      "/api/llm": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/llm/, "/api/llm")
      },
      // 语音配置文件API代理到7860端口
      "/api/voice": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/voice/, "/api/voice")
      },
      // Live2D配置API代理到7860端口
      "/api/live2d": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/live2d/, "/api/live2d")
      },
      // 🔮 神谕之音专用API代理到7860端口
      "/api/oracle": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/oracle/, "/api/oracle")
      },
      // 角色列表API代理到7860端口
      "/api/characters": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/characters/, "/api/characters")
      },
      // 文件服务API代理到7860端口
      "/api/files": {
        target: realtimeTarget,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api\/files/, "/api/files")
      }
      // electron-store API (store, images, network-info) 不代理，直接访问3001端口
    }
  },
  define: {
    __VUE_PROD_DEVTOOLS__: false,
    // 注入局域网IP信息到前端
    __VITE_LOCAL_IP__: JSON.stringify(localIP),
    __VITE_DEV_SERVER_PORT__: JSON.stringify(5173),
    __VITE_BUILD_TIME__: JSON.stringify((/* @__PURE__ */ new Date()).toISOString())
  },
  json: {
    namedExports: false,
    stringify: false
  },
  assetsInclude: ["**/*.json"]
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
