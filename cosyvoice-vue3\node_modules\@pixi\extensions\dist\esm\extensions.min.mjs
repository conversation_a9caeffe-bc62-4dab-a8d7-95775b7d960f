/*!
 * @pixi/extensions - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/extensions is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
var e,n=function(){return n=Object.assign||function(e){for(var n,r=arguments,t=1,a=arguments.length;t<a;t++)for(var o in n=r[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},n.apply(this,arguments)};!function(e){e.Application="application",e.RendererPlugin="renderer-webgl-plugin",e.CanvasRendererPlugin="renderer-canvas-plugin",e.<PERSON><PERSON>="loader",e.<PERSON><PERSON>er="load-parser",e.ResolveParser="resolve-parser",e.<PERSON><PERSON>arser="cache-parser",e.DetectionParser="detection-parser"}(e||(e={}));var r=function(e){if("function"==typeof e||"object"==typeof e&&e.extension){var r="object"!=typeof e.extension?{type:e.extension}:e.extension;e=n(n({},r),{ref:e})}if("object"!=typeof e)throw new Error("Invalid extension type");return"string"==typeof(e=n({},e)).type&&(e.type=[e.type]),e},t={_addHandlers:null,_removeHandlers:null,_queue:{},remove:function(){for(var e=arguments,n=this,t=[],a=0;a<arguments.length;a++)t[a]=e[a];return t.map(r).forEach((function(e){e.type.forEach((function(r){var t,a;return null===(a=(t=n._removeHandlers)[r])||void 0===a?void 0:a.call(t,e)}))})),this},add:function(){for(var e=arguments,n=this,t=[],a=0;a<arguments.length;a++)t[a]=e[a];return t.map(r).forEach((function(e){e.type.forEach((function(r){var t=n._addHandlers,a=n._queue;t[r]?t[r](e):(a[r]=a[r]||[],a[r].push(e))}))})),this},handle:function(e,n,r){var t=this._addHandlers=this._addHandlers||{},a=this._removeHandlers=this._removeHandlers||{};t[e]=n,a[e]=r;var o=this._queue;return o[e]&&(o[e].forEach((function(e){return n(e)})),delete o[e]),this},handleByMap:function(e,n){return this.handle(e,(function(e){n[e.name]=e.ref}),(function(e){delete n[e.name]}))},handleByList:function(n,r){return this.handle(n,(function(t){var a,o;r.includes(t.ref)||(r.push(t.ref),n===e.Loader&&(null===(o=(a=t.ref).add)||void 0===o||o.call(a)))}),(function(e){var n=r.indexOf(e.ref);-1!==n&&r.splice(n,1)}))}};export{e as ExtensionType,t as extensions};
//# sourceMappingURL=extensions.min.mjs.map
