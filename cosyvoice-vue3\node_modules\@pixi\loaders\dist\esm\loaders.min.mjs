/*!
 * @pixi/loaders - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/loaders is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{extensions as t,ExtensionType as e,Texture as r}from"@pixi/core";var i=function(){function t(t,e,r){void 0===e&&(e=!1),this._fn=t,this._once=e,this._thisArg=r,this._next=this._prev=this._owner=null}return t.prototype.detach=function(){return null!==this._owner&&(this._owner.detach(this),!0)},t}();function n(t,e){return t._head?(t._tail._next=e,e._prev=t._tail,t._tail=e):(t._head=e,t._tail=e),e._owner=t,e}var s,o=function(){function t(){this._head=this._tail=void 0}return t.prototype.handlers=function(t){void 0===t&&(t=!1);var e=this._head;if(t)return!!e;for(var r=[];e;)r.push(e),e=e._next;return r},t.prototype.has=function(t){if(!(t instanceof i))throw new Error("MiniSignal#has(): First arg must be a SignalBinding object.");return t._owner===this},t.prototype.dispatch=function(){for(var t=arguments,e=[],r=0;r<arguments.length;r++)e[r]=t[r];var i=this._head;if(!i)return!1;for(;i;)i._once&&this.detach(i),i._fn.apply(i._thisArg,e),i=i._next;return!0},t.prototype.add=function(t,e){if(void 0===e&&(e=null),"function"!=typeof t)throw new Error("MiniSignal#add(): First arg must be a Function.");return n(this,new i(t,!1,e))},t.prototype.once=function(t,e){if(void 0===e&&(e=null),"function"!=typeof t)throw new Error("MiniSignal#once(): First arg must be a Function.");return n(this,new i(t,!0,e))},t.prototype.detach=function(t){if(!(t instanceof i))throw new Error("MiniSignal#detach(): First arg must be a SignalBinding object.");return t._owner!==this||(t._prev&&(t._prev._next=t._next),t._next&&(t._next._prev=t._prev),t===this._head?(this._head=t._next,null===t._next&&(this._tail=null)):t===this._tail&&(this._tail=t._prev,this._tail._next=null),t._owner=null),this},t.prototype.detachAll=function(){var t=this._head;if(!t)return this;for(this._head=this._tail=null;t;)t._owner=null,t=t._next;return this},t}();function a(t,e){e=e||{};for(var r={key:["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}},i=r.parser[e.strictMode?"strict":"loose"].exec(t),n={},s=14;s--;)n[r.key[s]]=i[s]||"";return n[r.q.name]={},n[r.key[12]].replace(r.q.parser,(function(t,e,i){e&&(n[r.q.name][e]=i)})),n}var h=null;function u(){}function d(t,e,r){e&&0===e.indexOf(".")&&(e=e.substring(1)),e&&(t[e]=r)}function l(t){return t.toString().replace("object ","")}var p=function(){function t(e,r,i){if(this._dequeue=u,this._onLoadBinding=null,this._elementTimer=0,this._boundComplete=null,this._boundOnError=null,this._boundOnProgress=null,this._boundOnTimeout=null,this._boundXhrOnError=null,this._boundXhrOnTimeout=null,this._boundXhrOnAbort=null,this._boundXhrOnLoad=null,"string"!=typeof e||"string"!=typeof r)throw new Error("Both name and url are required for constructing a resource.");i=i||{},this._flags=0,this._setFlag(t.STATUS_FLAGS.DATA_URL,0===r.indexOf("data:")),this.name=e,this.url=r,this.extension=this._getExtension(),this.data=null,this.crossOrigin=!0===i.crossOrigin?"anonymous":i.crossOrigin,this.timeout=i.timeout||0,this.loadType=i.loadType||this._determineLoadType(),this.xhrType=i.xhrType,this.metadata=i.metadata||{},this.error=null,this.xhr=null,this.children=[],this.type=t.TYPE.UNKNOWN,this.progressChunk=0,this._dequeue=u,this._onLoadBinding=null,this._elementTimer=0,this._boundComplete=this.complete.bind(this),this._boundOnError=this._onError.bind(this),this._boundOnProgress=this._onProgress.bind(this),this._boundOnTimeout=this._onTimeout.bind(this),this._boundXhrOnError=this._xhrOnError.bind(this),this._boundXhrOnTimeout=this._xhrOnTimeout.bind(this),this._boundXhrOnAbort=this._xhrOnAbort.bind(this),this._boundXhrOnLoad=this._xhrOnLoad.bind(this),this.onStart=new o,this.onProgress=new o,this.onComplete=new o,this.onAfterMiddleware=new o}return t.setExtensionLoadType=function(e,r){d(t._loadTypeMap,e,r)},t.setExtensionXhrType=function(e,r){d(t._xhrTypeMap,e,r)},Object.defineProperty(t.prototype,"isDataUrl",{get:function(){return this._hasFlag(t.STATUS_FLAGS.DATA_URL)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isComplete",{get:function(){return this._hasFlag(t.STATUS_FLAGS.COMPLETE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isLoading",{get:function(){return this._hasFlag(t.STATUS_FLAGS.LOADING)},enumerable:!1,configurable:!0}),t.prototype.complete=function(){this._clearEvents(),this._finish()},t.prototype.abort=function(e){if(!this.error){if(this.error=new Error(e),this._clearEvents(),this.xhr)this.xhr.abort();else if(this.xdr)this.xdr.abort();else if(this.data)if(this.data.src)this.data.src=t.EMPTY_GIF;else for(;this.data.firstChild;)this.data.removeChild(this.data.firstChild);this._finish()}},t.prototype.load=function(e){var r=this;if(!this.isLoading)if(this.isComplete)e&&setTimeout((function(){return e(r)}),1);else switch(e&&this.onComplete.once(e),this._setFlag(t.STATUS_FLAGS.LOADING,!0),this.onStart.dispatch(this),!1!==this.crossOrigin&&"string"==typeof this.crossOrigin||(this.crossOrigin=this._determineCrossOrigin(this.url)),this.loadType){case t.LOAD_TYPE.IMAGE:this.type=t.TYPE.IMAGE,this._loadElement("image");break;case t.LOAD_TYPE.AUDIO:this.type=t.TYPE.AUDIO,this._loadSourceElement("audio");break;case t.LOAD_TYPE.VIDEO:this.type=t.TYPE.VIDEO,this._loadSourceElement("video");break;case t.LOAD_TYPE.XHR:default:void 0===s&&(s=!(!globalThis.XDomainRequest||"withCredentials"in new XMLHttpRequest)),s&&this.crossOrigin?this._loadXdr():this._loadXhr()}},t.prototype._hasFlag=function(t){return 0!=(this._flags&t)},t.prototype._setFlag=function(t,e){this._flags=e?this._flags|t:this._flags&~t},t.prototype._clearEvents=function(){clearTimeout(this._elementTimer),this.data&&this.data.removeEventListener&&(this.data.removeEventListener("error",this._boundOnError,!1),this.data.removeEventListener("load",this._boundComplete,!1),this.data.removeEventListener("progress",this._boundOnProgress,!1),this.data.removeEventListener("canplaythrough",this._boundComplete,!1)),this.xhr&&(this.xhr.removeEventListener?(this.xhr.removeEventListener("error",this._boundXhrOnError,!1),this.xhr.removeEventListener("timeout",this._boundXhrOnTimeout,!1),this.xhr.removeEventListener("abort",this._boundXhrOnAbort,!1),this.xhr.removeEventListener("progress",this._boundOnProgress,!1),this.xhr.removeEventListener("load",this._boundXhrOnLoad,!1)):(this.xhr.onerror=null,this.xhr.ontimeout=null,this.xhr.onprogress=null,this.xhr.onload=null))},t.prototype._finish=function(){if(this.isComplete)throw new Error("Complete called again for an already completed resource.");this._setFlag(t.STATUS_FLAGS.COMPLETE,!0),this._setFlag(t.STATUS_FLAGS.LOADING,!1),this.onComplete.dispatch(this)},t.prototype._loadElement=function(t){this.metadata.loadElement?this.data=this.metadata.loadElement:"image"===t&&void 0!==globalThis.Image?this.data=new Image:this.data=document.createElement(t),this.crossOrigin&&(this.data.crossOrigin=this.crossOrigin),this.metadata.skipSource||(this.data.src=this.url),this.data.addEventListener("error",this._boundOnError,!1),this.data.addEventListener("load",this._boundComplete,!1),this.data.addEventListener("progress",this._boundOnProgress,!1),this.timeout&&(this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout))},t.prototype._loadSourceElement=function(t){if(this.metadata.loadElement?this.data=this.metadata.loadElement:"audio"===t&&void 0!==globalThis.Audio?this.data=new Audio:this.data=document.createElement(t),null!==this.data){if(this.crossOrigin&&(this.data.crossOrigin=this.crossOrigin),!this.metadata.skipSource)if(navigator.isCocoonJS)this.data.src=Array.isArray(this.url)?this.url[0]:this.url;else if(Array.isArray(this.url))for(var e=this.metadata.mimeType,r=0;r<this.url.length;++r)this.data.appendChild(this._createSource(t,this.url[r],Array.isArray(e)?e[r]:e));else{e=this.metadata.mimeType;this.data.appendChild(this._createSource(t,this.url,Array.isArray(e)?e[0]:e))}this.data.addEventListener("error",this._boundOnError,!1),this.data.addEventListener("load",this._boundComplete,!1),this.data.addEventListener("progress",this._boundOnProgress,!1),this.data.addEventListener("canplaythrough",this._boundComplete,!1),this.data.load(),this.timeout&&(this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout))}else this.abort("Unsupported element: "+t)},t.prototype._loadXhr=function(){"string"!=typeof this.xhrType&&(this.xhrType=this._determineXhrType());var e=this.xhr=new XMLHttpRequest;"use-credentials"===this.crossOrigin&&(e.withCredentials=!0),e.open("GET",this.url,!0),e.timeout=this.timeout,this.xhrType===t.XHR_RESPONSE_TYPE.JSON||this.xhrType===t.XHR_RESPONSE_TYPE.DOCUMENT?e.responseType=t.XHR_RESPONSE_TYPE.TEXT:e.responseType=this.xhrType,e.addEventListener("error",this._boundXhrOnError,!1),e.addEventListener("timeout",this._boundXhrOnTimeout,!1),e.addEventListener("abort",this._boundXhrOnAbort,!1),e.addEventListener("progress",this._boundOnProgress,!1),e.addEventListener("load",this._boundXhrOnLoad,!1),e.send()},t.prototype._loadXdr=function(){"string"!=typeof this.xhrType&&(this.xhrType=this._determineXhrType());var t=this.xhr=new globalThis.XDomainRequest;t.timeout=this.timeout||5e3,t.onerror=this._boundXhrOnError,t.ontimeout=this._boundXhrOnTimeout,t.onprogress=this._boundOnProgress,t.onload=this._boundXhrOnLoad,t.open("GET",this.url,!0),setTimeout((function(){return t.send()}),1)},t.prototype._createSource=function(t,e,r){r||(r=t+"/"+this._getExtension(e));var i=document.createElement("source");return i.src=e,i.type=r,i},t.prototype._onError=function(t){this.abort("Failed to load element using: "+t.target.nodeName)},t.prototype._onProgress=function(t){t&&t.lengthComputable&&this.onProgress.dispatch(this,t.loaded/t.total)},t.prototype._onTimeout=function(){this.abort("Load timed out.")},t.prototype._xhrOnError=function(){var t=this.xhr;this.abort(l(t)+" Request failed. Status: "+t.status+', text: "'+t.statusText+'"')},t.prototype._xhrOnTimeout=function(){var t=this.xhr;this.abort(l(t)+" Request timed out.")},t.prototype._xhrOnAbort=function(){var t=this.xhr;this.abort(l(t)+" Request was aborted by the user.")},t.prototype._xhrOnLoad=function(){var e=this.xhr,r="",i=void 0===e.status?200:e.status;if(""!==e.responseType&&"text"!==e.responseType&&void 0!==e.responseType||(r=e.responseText),0===i&&(r.length>0||e.responseType===t.XHR_RESPONSE_TYPE.BUFFER)?i=200:1223===i&&(i=204),2===(i/100|0)){if(this.xhrType===t.XHR_RESPONSE_TYPE.TEXT)this.data=r,this.type=t.TYPE.TEXT;else if(this.xhrType===t.XHR_RESPONSE_TYPE.JSON)try{this.data=JSON.parse(r),this.type=t.TYPE.JSON}catch(t){return void this.abort("Error trying to parse loaded json: "+t)}else if(this.xhrType===t.XHR_RESPONSE_TYPE.DOCUMENT)try{if(globalThis.DOMParser){var n=new DOMParser;this.data=n.parseFromString(r,"text/xml")}else{var s=document.createElement("div");s.innerHTML=r,this.data=s}this.type=t.TYPE.XML}catch(t){return void this.abort("Error trying to parse loaded xml: "+t)}else this.data=e.response||r;this.complete()}else this.abort("["+e.status+"] "+e.statusText+": "+e.responseURL)},t.prototype._determineCrossOrigin=function(t,e){if(0===t.indexOf("data:"))return"";if(globalThis.origin!==globalThis.location.origin)return"anonymous";e=e||globalThis.location,h||(h=document.createElement("a")),h.href=t;var r=a(h.href,{strictMode:!0}),i=!r.port&&""===e.port||r.port===e.port,n=r.protocol?r.protocol+":":"";return r.host===e.hostname&&i&&n===e.protocol?"":"anonymous"},t.prototype._determineXhrType=function(){return t._xhrTypeMap[this.extension]||t.XHR_RESPONSE_TYPE.TEXT},t.prototype._determineLoadType=function(){return t._loadTypeMap[this.extension]||t.LOAD_TYPE.XHR},t.prototype._getExtension=function(t){void 0===t&&(t=this.url);var e="";if(this.isDataUrl){var r=t.indexOf("/");e=t.substring(r+1,t.indexOf(";",r))}else{var i=t.indexOf("?"),n=t.indexOf("#"),s=Math.min(i>-1?i:t.length,n>-1?n:t.length);e=(t=t.substring(0,s)).substring(t.lastIndexOf(".")+1)}return e.toLowerCase()},t.prototype._getMimeFromXhrType=function(e){switch(e){case t.XHR_RESPONSE_TYPE.BUFFER:return"application/octet-binary";case t.XHR_RESPONSE_TYPE.BLOB:return"application/blob";case t.XHR_RESPONSE_TYPE.DOCUMENT:return"application/xml";case t.XHR_RESPONSE_TYPE.JSON:return"application/json";case t.XHR_RESPONSE_TYPE.DEFAULT:case t.XHR_RESPONSE_TYPE.TEXT:default:return"text/plain"}},t}();function c(){}function _(t){return function(){for(var e=arguments,r=[],i=0;i<arguments.length;i++)r[i]=e[i];if(null===t)throw new Error("Callback was already called.");var n=t;t=null,n.apply(this,r)}}!function(t){var e,r,i,n;(e=t.STATUS_FLAGS||(t.STATUS_FLAGS={}))[e.NONE=0]="NONE",e[e.DATA_URL=1]="DATA_URL",e[e.COMPLETE=2]="COMPLETE",e[e.LOADING=4]="LOADING",(r=t.TYPE||(t.TYPE={}))[r.UNKNOWN=0]="UNKNOWN",r[r.JSON=1]="JSON",r[r.XML=2]="XML",r[r.IMAGE=3]="IMAGE",r[r.AUDIO=4]="AUDIO",r[r.VIDEO=5]="VIDEO",r[r.TEXT=6]="TEXT",(i=t.LOAD_TYPE||(t.LOAD_TYPE={}))[i.XHR=1]="XHR",i[i.IMAGE=2]="IMAGE",i[i.AUDIO=3]="AUDIO",i[i.VIDEO=4]="VIDEO",(n=t.XHR_RESPONSE_TYPE||(t.XHR_RESPONSE_TYPE={})).DEFAULT="text",n.BUFFER="arraybuffer",n.BLOB="blob",n.DOCUMENT="document",n.JSON="json",n.TEXT="text",t._loadTypeMap={gif:t.LOAD_TYPE.IMAGE,png:t.LOAD_TYPE.IMAGE,bmp:t.LOAD_TYPE.IMAGE,jpg:t.LOAD_TYPE.IMAGE,jpeg:t.LOAD_TYPE.IMAGE,tif:t.LOAD_TYPE.IMAGE,tiff:t.LOAD_TYPE.IMAGE,webp:t.LOAD_TYPE.IMAGE,tga:t.LOAD_TYPE.IMAGE,avif:t.LOAD_TYPE.IMAGE,svg:t.LOAD_TYPE.IMAGE,"svg+xml":t.LOAD_TYPE.IMAGE,mp3:t.LOAD_TYPE.AUDIO,ogg:t.LOAD_TYPE.AUDIO,wav:t.LOAD_TYPE.AUDIO,mp4:t.LOAD_TYPE.VIDEO,webm:t.LOAD_TYPE.VIDEO},t._xhrTypeMap={xhtml:t.XHR_RESPONSE_TYPE.DOCUMENT,html:t.XHR_RESPONSE_TYPE.DOCUMENT,htm:t.XHR_RESPONSE_TYPE.DOCUMENT,xml:t.XHR_RESPONSE_TYPE.DOCUMENT,tmx:t.XHR_RESPONSE_TYPE.DOCUMENT,svg:t.XHR_RESPONSE_TYPE.DOCUMENT,tsx:t.XHR_RESPONSE_TYPE.DOCUMENT,gif:t.XHR_RESPONSE_TYPE.BLOB,png:t.XHR_RESPONSE_TYPE.BLOB,bmp:t.XHR_RESPONSE_TYPE.BLOB,jpg:t.XHR_RESPONSE_TYPE.BLOB,jpeg:t.XHR_RESPONSE_TYPE.BLOB,tif:t.XHR_RESPONSE_TYPE.BLOB,tiff:t.XHR_RESPONSE_TYPE.BLOB,webp:t.XHR_RESPONSE_TYPE.BLOB,tga:t.XHR_RESPONSE_TYPE.BLOB,avif:t.XHR_RESPONSE_TYPE.BLOB,json:t.XHR_RESPONSE_TYPE.JSON,text:t.XHR_RESPONSE_TYPE.TEXT,txt:t.XHR_RESPONSE_TYPE.TEXT,ttf:t.XHR_RESPONSE_TYPE.BUFFER,otf:t.XHR_RESPONSE_TYPE.BUFFER},t.EMPTY_GIF="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="}(p||(p={}));var E=function(t,e){this.data=t,this.callback=e},f=function(){function t(t,e){var r=this;if(void 0===e&&(e=1),this.workers=0,this.saturated=c,this.unsaturated=c,this.empty=c,this.drain=c,this.error=c,this.started=!1,this.paused=!1,this._tasks=[],this._insert=function(t,e,i){if(i&&"function"!=typeof i)throw new Error("task callback must be a function");if(r.started=!0,null==t&&r.idle())setTimeout((function(){return r.drain()}),1);else{var n=new E(t,"function"==typeof i?i:c);e?r._tasks.unshift(n):r._tasks.push(n),setTimeout(r.process,1)}},this.process=function(){for(;!r.paused&&r.workers<r.concurrency&&r._tasks.length;){var t=r._tasks.shift();0===r._tasks.length&&r.empty(),r.workers+=1,r.workers===r.concurrency&&r.saturated(),r._worker(t.data,_(r._next(t)))}},this._worker=t,0===e)throw new Error("Concurrency must not be zero");this.concurrency=e,this.buffer=e/4}return t.prototype._next=function(t){var e=this;return function(){for(var r=arguments,i=[],n=0;n<arguments.length;n++)i[n]=r[n];e.workers-=1,t.callback.apply(t,i),null!=i[0]&&e.error(i[0],t.data),e.workers<=e.concurrency-e.buffer&&e.unsaturated(),e.idle()&&e.drain(),e.process()}},t.prototype.push=function(t,e){this._insert(t,!1,e)},t.prototype.kill=function(){this.workers=0,this.drain=c,this.started=!1,this._tasks=[]},t.prototype.unshift=function(t,e){this._insert(t,!0,e)},t.prototype.length=function(){return this._tasks.length},t.prototype.running=function(){return this.workers},t.prototype.idle=function(){return this._tasks.length+this.workers===0},t.prototype.pause=function(){!0!==this.paused&&(this.paused=!0)},t.prototype.resume=function(){if(!1!==this.paused){this.paused=!1;for(var t=1;t<=this.concurrency;t++)this.process()}},t.eachSeries=function(t,e,r,i){var n=0,s=t.length;!function o(a){a||n===s?r&&r(a):i?setTimeout((function(){e(t[n++],o)}),1):e(t[n++],o)}()},t.queue=function(e,r){return new t(e,r)},t}(),g=/(#[\w-]+)?$/,T=function(){function r(t,e){var i=this;void 0===t&&(t=""),void 0===e&&(e=10),this.progress=0,this.loading=!1,this.defaultQueryString="",this._beforeMiddleware=[],this._afterMiddleware=[],this._resourcesParsing=[],this._boundLoadResource=function(t,e){return i._loadResource(t,e)},this.resources={},this.baseUrl=t,this._beforeMiddleware=[],this._afterMiddleware=[],this._resourcesParsing=[],this._boundLoadResource=function(t,e){return i._loadResource(t,e)},this._queue=f.queue(this._boundLoadResource,e),this._queue.pause(),this.resources={},this.onProgress=new o,this.onError=new o,this.onLoad=new o,this.onStart=new o,this.onComplete=new o;for(var n=0;n<r._plugins.length;++n){var s=r._plugins[n],a=s.pre,h=s.use;a&&this.pre(a),h&&this.use(h)}this._protected=!1}return r.prototype._add=function(t,e,r,i){if(this.loading&&(!r||!r.parentResource))throw new Error("Cannot add resources while the loader is running.");if(this.resources[t])throw new Error('Resource named "'+t+'" already exists.');if(e=this._prepareUrl(e),this.resources[t]=new p(t,e,r),"function"==typeof i&&this.resources[t].onAfterMiddleware.once(i),this.loading){for(var n=r.parentResource,s=[],o=0;o<n.children.length;++o)n.children[o].isComplete||s.push(n.children[o]);var a=n.progressChunk*(s.length+1)/(s.length+2);n.children.push(this.resources[t]),n.progressChunk=a;for(o=0;o<s.length;++o)s[o].progressChunk=a;this.resources[t].progressChunk=a}return this._queue.push(this.resources[t]),this},r.prototype.pre=function(t){return this._beforeMiddleware.push(t),this},r.prototype.use=function(t){return this._afterMiddleware.push(t),this},r.prototype.reset=function(){for(var t in this.progress=0,this.loading=!1,this._queue.kill(),this._queue.pause(),this.resources){var e=this.resources[t];e._onLoadBinding&&e._onLoadBinding.detach(),e.isLoading&&e.abort("loader reset")}return this.resources={},this},r.prototype.load=function(t){if("function"==typeof t&&this.onComplete.once(t),this.loading)return this;if(this._queue.idle())this._onStart(),this._onComplete();else{for(var e=100/this._queue._tasks.length,r=0;r<this._queue._tasks.length;++r)this._queue._tasks[r].data.progressChunk=e;this._onStart(),this._queue.resume()}return this},Object.defineProperty(r.prototype,"concurrency",{get:function(){return this._queue.concurrency},set:function(t){this._queue.concurrency=t},enumerable:!1,configurable:!0}),r.prototype._prepareUrl=function(t){var e,r=a(t,{strictMode:!0});if(e=r.protocol||!r.path||0===t.indexOf("//")?t:this.baseUrl.length&&this.baseUrl.lastIndexOf("/")!==this.baseUrl.length-1&&"/"!==t.charAt(0)?this.baseUrl+"/"+t:this.baseUrl+t,this.defaultQueryString){var i=g.exec(e)[0];-1!==(e=e.slice(0,e.length-i.length)).indexOf("?")?e+="&"+this.defaultQueryString:e+="?"+this.defaultQueryString,e+=i}return e},r.prototype._loadResource=function(t,e){var r=this;t._dequeue=e,f.eachSeries(this._beforeMiddleware,(function(e,i){e.call(r,t,(function(){i(t.isComplete?{}:null)}))}),(function(){t.isComplete?r._onLoad(t):(t._onLoadBinding=t.onComplete.once(r._onLoad,r),t.load())}),!0)},r.prototype._onStart=function(){this.progress=0,this.loading=!0,this.onStart.dispatch(this)},r.prototype._onComplete=function(){this.progress=100,this.loading=!1,this.onComplete.dispatch(this,this.resources)},r.prototype._onLoad=function(t){var e=this;t._onLoadBinding=null,this._resourcesParsing.push(t),t._dequeue(),f.eachSeries(this._afterMiddleware,(function(r,i){r.call(e,t,i)}),(function(){t.onAfterMiddleware.dispatch(t),e.progress=Math.min(100,e.progress+t.progressChunk),e.onProgress.dispatch(e,t),t.error?e.onError.dispatch(t.error,e,t):e.onLoad.dispatch(e,t),e._resourcesParsing.splice(e._resourcesParsing.indexOf(t),1),e._queue.idle()&&0===e._resourcesParsing.length&&e._onComplete()}),!0)},r.prototype.destroy=function(){this._protected||this.reset()},Object.defineProperty(r,"shared",{get:function(){var t=r._shared;return t||((t=new r)._protected=!0,r._shared=t),t},enumerable:!1,configurable:!0}),r.registerPlugin=function(i){return t.add({type:e.Loader,ref:i}),r},r._plugins=[],r}();t.handleByList(e.Loader,T._plugins),T.prototype.add=function(t,e,r,i){if(Array.isArray(t)){for(var n=0;n<t.length;++n)this.add(t[n]);return this}if("object"==typeof t&&(r=t,i=e||r.callback||r.onComplete,e=r.url,t=r.name||r.key||r.url),"string"!=typeof e&&(i=r,r=e,e=t),"string"!=typeof e)throw new Error("No url passed to add resource to loader.");return"function"==typeof r&&(i=r,r=null),this._add(t,e,r,i)};var O=function(){function t(){}return t.init=function(t){t=Object.assign({sharedLoader:!1},t),this.loader=t.sharedLoader?T.shared:new T},t.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},t.extension=e.Application,t}(),m=function(){function t(){}return t.add=function(){p.setExtensionLoadType("svg",p.LOAD_TYPE.XHR),p.setExtensionXhrType("svg",p.XHR_RESPONSE_TYPE.TEXT)},t.use=function(t,e){if(!t.data||t.type!==p.TYPE.IMAGE&&"svg"!==t.extension)e();else{var i=t.data,n=t.url,s=t.name,o=t.metadata;r.fromLoader(i,n,s,o).then((function(r){t.texture=r,e()})).catch(e)}},t.extension=e.Loader,t}();function y(t,e){if(t.data){if(t.xhr&&t.xhrType===p.XHR_RESPONSE_TYPE.BLOB)if(self.Blob&&"string"!=typeof t.data){if(0===t.data.type.indexOf("image")){var r=globalThis.URL||globalThis.webkitURL,i=r.createObjectURL(t.data);return t.blob=t.data,t.data=new Image,t.data.src=i,t.type=p.TYPE.IMAGE,void(t.data.onload=function(){r.revokeObjectURL(i),t.data.onload=null,e()})}}else{var n=t.xhr.getResponseHeader("content-type");if(n&&0===n.indexOf("image"))return t.data=new Image,t.data.src="data:"+n+";base64,"+function(t){for(var e="",r=0;r<t.length;){for(var i=[0,0,0],n=[0,0,0,0],s=0;s<i.length;++s)r<t.length?i[s]=255&t.charCodeAt(r++):i[s]=0;switch(n[0]=i[0]>>2,n[1]=(3&i[0])<<4|i[1]>>4,n[2]=(15&i[1])<<2|i[2]>>6,n[3]=63&i[2],r-(t.length-1)){case 2:n[3]=64,n[2]=64;break;case 1:n[3]=64}for(s=0;s<n.length;++s)e+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(n[s])}return e}(t.xhr.responseText),t.type=p.TYPE.IMAGE,void(t.data.onload=function(){t.data.onload=null,e()})}e()}else e()}var b=function(){function t(){}return t.extension=e.Loader,t.use=y,t}();t.add(m,b);export{O as AppLoaderPlugin,T as Loader,p as LoaderResource,m as TextureLoader};
//# sourceMappingURL=loaders.min.mjs.map
