/*!
 * @pixi/sprite-tiling - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/sprite-tiling is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{Texture as e,TextureMatrix as t,Shader as r,ExtensionType as n,QuadUv as o,State as i,ObjectRenderer as a}from"@pixi/core";import{Point as u,Rectangle as s,Transform as c,Matrix as l}from"@pixi/math";import{Sprite as h}from"@pixi/sprite";import{WRAP_MODES as p}from"@pixi/constants";import{premultiplyTintToRgba as d,correctBlendMode as m}from"@pixi/utils";var f=function(e,t){return f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},f(e,t)};function v(e,t){function r(){this.constructor=e}f(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var x=new u,_=function(r){function n(e,n,o){void 0===n&&(n=100),void 0===o&&(o=100);var i=r.call(this,e)||this;return i.tileTransform=new c,i._width=n,i._height=o,i.uvMatrix=i.texture.uvMatrix||new t(e),i.pluginName="tilingSprite",i.uvRespectAnchor=!1,i}return v(n,r),Object.defineProperty(n.prototype,"clampMargin",{get:function(){return this.uvMatrix.clampMargin},set:function(e){this.uvMatrix.clampMargin=e,this.uvMatrix.update(!0)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"tileScale",{get:function(){return this.tileTransform.scale},set:function(e){this.tileTransform.scale.copyFrom(e)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"tilePosition",{get:function(){return this.tileTransform.position},set:function(e){this.tileTransform.position.copyFrom(e)},enumerable:!1,configurable:!0}),n.prototype._onTextureUpdate=function(){this.uvMatrix&&(this.uvMatrix.texture=this._texture),this._cachedTint=16777215},n.prototype._render=function(e){var t=this._texture;t&&t.valid&&(this.tileTransform.updateLocalTransform(),this.uvMatrix.update(),e.batch.setObjectRenderer(e.plugins[this.pluginName]),e.plugins[this.pluginName].render(this))},n.prototype._calculateBounds=function(){var e=this._width*-this._anchor._x,t=this._height*-this._anchor._y,r=this._width*(1-this._anchor._x),n=this._height*(1-this._anchor._y);this._bounds.addFrame(this.transform,e,t,r,n)},n.prototype.getLocalBounds=function(e){return 0===this.children.length?(this._bounds.minX=this._width*-this._anchor._x,this._bounds.minY=this._height*-this._anchor._y,this._bounds.maxX=this._width*(1-this._anchor._x),this._bounds.maxY=this._height*(1-this._anchor._y),e||(this._localBoundsRect||(this._localBoundsRect=new s),e=this._localBoundsRect),this._bounds.getRectangle(e)):r.prototype.getLocalBounds.call(this,e)},n.prototype.containsPoint=function(e){this.worldTransform.applyInverse(e,x);var t=this._width,r=this._height,n=-t*this.anchor._x;if(x.x>=n&&x.x<n+t){var o=-r*this.anchor._y;if(x.y>=o&&x.y<o+r)return!0}return!1},n.prototype.destroy=function(e){r.prototype.destroy.call(this,e),this.tileTransform=null,this.uvMatrix=null},n.from=function(t,r){return new n(t instanceof e?t:e.from(t,r),r.width,r.height)},Object.defineProperty(n.prototype,"width",{get:function(){return this._width},set:function(e){this._width=e},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"height",{get:function(){return this._height},set:function(e){this._height=e},enumerable:!1,configurable:!0}),n}(h),g="#version 100\n#define SHADER_NAME Tiling-Sprite-100\n\nprecision lowp float;\n\nattribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTransform;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;\n}\n",y=new l,C=function(e){function t(t){var r=e.call(this,t)||this;return t.runners.contextChange.add(r),r.quad=new o,r.state=i.for2d(),r}return v(t,e),t.prototype.contextChange=function(){var e=this.renderer,t={globals:e.globalUniforms};this.simpleShader=r.from(g,"#version 100\n#define SHADER_NAME Tiling-Sprite-Simple-100\n\nprecision lowp float;\n\nvarying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\n\nvoid main(void)\n{\n    vec4 texSample = texture2D(uSampler, vTextureCoord);\n    gl_FragColor = texSample * uColor;\n}\n",t),this.shader=e.context.webGLVersion>1?r.from("#version 300 es\n#define SHADER_NAME Tiling-Sprite-300\n\nprecision lowp float;\n\nin vec2 aVertexPosition;\nin vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTransform;\n\nout vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;\n}\n","#version 300 es\n#define SHADER_NAME Tiling-Sprite-100\n\nprecision lowp float;\n\nin vec2 vTextureCoord;\n\nout vec4 fragmentColor;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\nuniform mat3 uMapCoord;\nuniform vec4 uClampFrame;\nuniform vec2 uClampOffset;\n\nvoid main(void)\n{\n    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);\n    coord = (uMapCoord * vec3(coord, 1.0)).xy;\n    vec2 unclamped = coord;\n    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);\n\n    vec4 texSample = texture(uSampler, coord, unclamped == coord ? 0.0f : -32.0f);// lod-bias very negative to force lod 0\n\n    fragmentColor = texSample * uColor;\n}\n",t):r.from(g,"#version 100\n#ifdef GL_EXT_shader_texture_lod\n    #extension GL_EXT_shader_texture_lod : enable\n#endif\n#define SHADER_NAME Tiling-Sprite-100\n\nprecision lowp float;\n\nvarying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\nuniform mat3 uMapCoord;\nuniform vec4 uClampFrame;\nuniform vec2 uClampOffset;\n\nvoid main(void)\n{\n    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);\n    coord = (uMapCoord * vec3(coord, 1.0)).xy;\n    vec2 unclamped = coord;\n    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);\n\n    #ifdef GL_EXT_shader_texture_lod\n        vec4 texSample = unclamped == coord\n            ? texture2D(uSampler, coord) \n            : texture2DLodEXT(uSampler, coord, 0);\n    #else\n        vec4 texSample = texture2D(uSampler, coord);\n    #endif\n\n    gl_FragColor = texSample * uColor;\n}\n",t)},t.prototype.render=function(e){var t=this.renderer,r=this.quad,n=r.vertices;n[0]=n[6]=e._width*-e.anchor.x,n[1]=n[3]=e._height*-e.anchor.y,n[2]=n[4]=e._width*(1-e.anchor.x),n[5]=n[7]=e._height*(1-e.anchor.y);var o=e.uvRespectAnchor?e.anchor.x:0,i=e.uvRespectAnchor?e.anchor.y:0;(n=r.uvs)[0]=n[6]=-o,n[1]=n[3]=-i,n[2]=n[4]=1-o,n[5]=n[7]=1-i,r.invalidate();var a=e._texture,u=a.baseTexture,s=u.alphaMode>0,c=e.tileTransform.localTransform,l=e.uvMatrix,h=u.isPowerOfTwo&&a.frame.width===u.width&&a.frame.height===u.height;h&&(u._glTextures[t.CONTEXT_UID]?h=u.wrapMode!==p.CLAMP:u.wrapMode===p.CLAMP&&(u.wrapMode=p.REPEAT));var f=h?this.simpleShader:this.shader,v=a.width,x=a.height,_=e._width,g=e._height;y.set(c.a*v/_,c.b*v/g,c.c*x/_,c.d*x/g,c.tx/_,c.ty/g),y.invert(),h?y.prepend(l.mapCoord):(f.uniforms.uMapCoord=l.mapCoord.toArray(!0),f.uniforms.uClampFrame=l.uClampFrame,f.uniforms.uClampOffset=l.uClampOffset),f.uniforms.uTransform=y.toArray(!0),f.uniforms.uColor=d(e.tint,e.worldAlpha,f.uniforms.uColor,s),f.uniforms.translationMatrix=e.transform.worldTransform.toArray(!0),f.uniforms.uSampler=a,t.shader.bind(f),t.geometry.bind(r),this.state.blendMode=m(e.blendMode,s),t.state.set(this.state),t.geometry.draw(this.renderer.gl.TRIANGLES,6,0)},t.extension={name:"tilingSprite",type:n.RendererPlugin},t}(a);export{_ as TilingSprite,C as TilingSpriteRenderer};
//# sourceMappingURL=sprite-tiling.min.mjs.map
