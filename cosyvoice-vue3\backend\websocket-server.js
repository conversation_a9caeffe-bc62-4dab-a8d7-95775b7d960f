const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const stat = promisify(fs.stat);
let fetch;
try {
  fetch = require('node-fetch');
} catch (error) {
  console.log('⚠️ node-fetch模块未安装，图片代理功能将受限');
}

const app = express();
const server = http.createServer(app);

// 启用CORS - 支持局域网访问
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173', /^http:\/\/192\.168\.\d+\.\d+:5173$/],
  credentials: true
}));

app.use(express.json());

// 数据存储路径
const STORAGE_DIR = path.join(__dirname, '..', 'public', 'generated-images');
const DATA_FILE = path.join(__dirname, 'comic-data.json');

// 确保存储目录存在
async function ensureStorageDir() {
  try {
    await stat(STORAGE_DIR);
  } catch (error) {
    if (error.code === 'ENOENT') {
      await mkdir(STORAGE_DIR, { recursive: true });
      console.log(`📁 创建存储目录: ${STORAGE_DIR}`);
    }
  }
}

// 初始化存储
ensureStorageDir();

// WebSocket服务器
const wss = new WebSocket.Server({ 
  server,
  path: '/ws'
});

// 客户端连接管理
const clients = new Map();
const deviceGroups = new Map();

// 客户端连接处理
wss.on('connection', (ws, req) => {
  const clientId = req.url ? new URL(req.url, 'http://localhost').searchParams.get('clientId') : null;
  const deviceType = req.url ? new URL(req.url, 'http://localhost').searchParams.get('deviceType') : 'browser';
  const sessionId = req.url ? new URL(req.url, 'http://localhost').searchParams.get('sessionId') : null;
  
  console.log(`🔗 WebSocket客户端连接: ${clientId} (${deviceType})`);
  
  // 注册客户端
  const clientInfo = {
    id: clientId || `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    ws,
    deviceType,
    sessionId,
    connectedAt: new Date(),
    lastActivity: new Date()
  };
  
  clients.set(clientInfo.id, clientInfo);
  
  // 发送连接确认
  send(ws, 'CONNECTION_ESTABLISHED', {
    clientId: clientInfo.id,
    serverTime: new Date().toISOString()
  });
  
  // 消息处理
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      handleMessage(clientInfo, message);
    } catch (error) {
      console.error('解析消息失败:', error);
      send(ws, 'ERROR', { message: '无效的消息格式' });
    }
  });
  
  // 连接关闭处理
  ws.on('close', () => {
    console.log(`🔌 WebSocket客户端断开: ${clientInfo.id}`);
    clients.delete(clientInfo.id);
    broadcastToOthers(clientInfo.id, 'CLIENT_DISCONNECTED', {
      clientId: clientInfo.id,
      deviceType: clientInfo.deviceType
    });
  });
  
  // 错误处理
  ws.on('error', (error) => {
    console.error(`WebSocket错误 [${clientInfo.id}]:`, error);
  });
  
  // 通知其他客户端新连接
  broadcastToOthers(clientInfo.id, 'CLIENT_CONNECTED', {
    clientId: clientInfo.id,
    deviceType: clientInfo.deviceType
  });
});

// 消息处理函数
function handleMessage(clientInfo, message) {
  const { event, payload } = message;
  clientInfo.lastActivity = new Date();
  
  console.log(`📨 收到消息 [${clientInfo.id}]: ${event}`);
  
  switch (event) {
    case 'CLIENT_REGISTER':
      handleClientRegister(clientInfo, payload);
      break;
      
    case 'HEARTBEAT_PING':
      handleHeartbeat(clientInfo, payload);
      break;
      
    case 'SESSION_STATE_UPDATE':
      handleSessionStateUpdate(clientInfo, payload);
      break;
      
    case 'GENERATION_STATUS':
      broadcastToAll('GENERATION_STATUS', payload);
      break;
      
    case 'STATE_SYNC':
      broadcastToOthers(clientInfo.id, 'STATE_SYNC', payload);
      break;
      
    case 'GET_GALLERY_DATA':
      handleGetGalleryData(clientInfo, payload);
      break;
      
    default:
      console.log(`未处理的消息类型: ${event}`);
      // 转发给其他客户端
      broadcastToOthers(clientInfo.id, event, payload);
  }
}

// 客户端注册处理
function handleClientRegister(clientInfo, payload) {
  console.log(`📝 客户端注册: ${clientInfo.id}`, payload);
  
  // 更新客户端信息
  if (payload.clientInfo) {
    Object.assign(clientInfo, payload.clientInfo);
  }
  
  // 发送注册确认
  send(clientInfo.ws, 'CLIENT_REGISTERED', {
    clientId: clientInfo.id,
    connectedClients: Array.from(clients.values()).map(c => ({
      id: c.id,
      deviceType: c.deviceType,
      connectedAt: c.connectedAt
    }))
  });
}

// 心跳处理
function handleHeartbeat(clientInfo, payload) {
  const { pingId, timestamp } = payload;
  
  // 发送心跳响应
  send(clientInfo.ws, 'HEARTBEAT_PONG', {
    pingId,
    timestamp,
    serverTime: Date.now()
  });
}

// 会话状态更新
function handleSessionStateUpdate(clientInfo, payload) {
  console.log(`🔄 会话状态更新 [${clientInfo.id}]:`, payload);
  
  // 广播给其他客户端
  broadcastToOthers(clientInfo.id, 'SESSION_STATE_UPDATE', {
    ...payload,
    fromClient: clientInfo.id,
    deviceType: clientInfo.deviceType
  });
}

// 处理画廊数据请求
async function handleGetGalleryData(clientInfo, payload) {
  console.log(`📦 处理画廊数据请求 [${clientInfo.id}]`);
  
  try {
    // 获取画廊数据（漫画生成结果）
    const data = await loadComicData();
    const galleryData = data['comic-generation-results'] || [];
    
    console.log(`📤 返回${galleryData.length}个画廊作品给客户端 [${clientInfo.id}]`);
    
    // 发送响应给请求的客户端
    send(clientInfo.ws, 'GALLERY_DATA_RESPONSE', {
      success: true,
      data: galleryData,
      count: galleryData.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error(`❌ 获取画廊数据失败 [${clientInfo.id}]:`, error);
    
    // 发送错误响应
    send(clientInfo.ws, 'GALLERY_DATA_RESPONSE', {
      success: false,
      error: error.message,
      data: [],
      count: 0,
      timestamp: new Date().toISOString()
    });
  }
}

// 发送消息给特定客户端
function send(ws, event, payload) {
  if (ws.readyState === WebSocket.OPEN) {
    const message = {
      event,
      payload,
      timestamp: Date.now(),
      messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    ws.send(JSON.stringify(message));
  }
}

// 广播消息给所有客户端
function broadcastToAll(event, payload) {
  clients.forEach((client) => {
    send(client.ws, event, payload);
  });
}

// 广播消息给除指定客户端外的所有客户端
function broadcastToOthers(excludeClientId, event, payload) {
  clients.forEach((client) => {
    if (client.id !== excludeClientId) {
      send(client.ws, event, payload);
    }
  });
}

// ============== API 服务端点 ==============

// 数据管理函数
async function loadComicData() {
  try {
    const data = await readFile(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    if (error.code === 'ENOENT') {
      return { 'comic-generation-results': [] };
    }
    console.error('读取数据文件失败:', error);
    return { 'comic-generation-results': [] };
  }
}

async function saveComicData(data) {
  try {
    await writeFile(DATA_FILE, JSON.stringify(data, null, 2), 'utf8');
    console.log('💾 数据已保存到文件');
  } catch (error) {
    console.error('保存数据文件失败:', error);
    throw error;
  }
}

// API: 健康检查 
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    connectedClients: clients.size,
    services: ['websocket', 'api', 'static-images', 'image-proxy'],
    timestamp: new Date().toISOString()
  });
});

// API: 存储 - 获取漫画生成结果
app.get('/api/store/comic-generation-results', async (req, res) => {
  try {
    const data = await loadComicData();
    const comics = data['comic-generation-results'] || [];
    console.log(`📖 返回 ${comics.length} 个漫画作品`);
    res.json({ success: true, value: comics });
  } catch (error) {
    console.error('获取漫画数据失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API: 存储 - 保存漫画生成结果
app.post('/api/store/comic-generation-results', async (req, res) => {
  try {
    const { value } = req.body;
    if (!Array.isArray(value)) {
      return res.status(400).json({ success: false, error: '数据格式错误，必须是数组' });
    }

    const data = await loadComicData();
    data['comic-generation-results'] = value;
    await saveComicData(data);

    console.log(`💾 保存了 ${value.length} 个漫画作品`);
    res.json({ success: true });

    // 通知所有客户端数据更新
    broadcastToAll('DATA_UPDATED', {
      type: 'comic-generation-results',
      count: value.length
    });
  } catch (error) {
    console.error('保存漫画数据失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API: 存储 - 通用GET接口
app.get('/api/store/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const data = await loadComicData();
    const value = data[key] || null;
    
    console.log(`📖 获取存储数据 [${key}]:`, value ? '有数据' : '无数据');
    res.json({ success: true, value });
  } catch (error) {
    console.error(`获取存储数据失败 [${key}]:`, error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API: 存储 - 通用POST接口
app.post('/api/store/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;
    
    const data = await loadComicData();
    data[key] = value;
    await saveComicData(data);

    console.log(`💾 保存存储数据 [${key}]:`, typeof value, JSON.stringify(value).length + ' chars');
    
    // 广播给所有连接的客户端
    broadcastToAll('STORE_DATA_UPDATED', {
      key,
      timestamp: new Date().toISOString()
    });
    
    res.json({ success: true });
  } catch (error) {
    console.error(`保存存储数据失败 [${key}]:`, error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API: 静态图片服务
app.use('/static/images', express.static(STORAGE_DIR, {
  setHeaders: (res, path) => {
    res.set('Cache-Control', 'public, max-age=31536000'); // 1年缓存
    res.set('Access-Control-Allow-Origin', '*');
  }
}));

// API: 图片代理服务
app.get('/proxy/image/:encodedUrl', async (req, res) => {
  try {
    const { encodedUrl } = req.params;
    const imageUrl = decodeURIComponent(encodedUrl);
    
    console.log(`🖼️ 代理图片请求: ${imageUrl}`);

    // 简单的URL验证
    if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
      return res.status(400).json({ error: '无效的图片URL' });
    }

    // 检查是否为node-fetch可用环境，如果不可用则返回原URL
    if (!fetch) {
      console.log('⚠️ node-fetch不可用，返回重定向');
      return res.redirect(imageUrl);
    }

    // 代理请求
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'CosyVoice-ImageProxy/1.0'
      },
      timeout: 10000
    });

    if (!response.ok) {
      console.error(`代理失败: ${response.status} ${response.statusText}`);
      return res.status(response.status).json({ error: `代理失败: ${response.statusText}` });
    }

    // 转发响应头
    const contentType = response.headers.get('content-type');
    if (contentType) {
      res.set('Content-Type', contentType);
    }
    res.set('Cache-Control', 'public, max-age=3600'); // 1小时缓存
    res.set('Access-Control-Allow-Origin', '*');

    // 转发图片数据
    response.body.pipe(res);

  } catch (error) {
    console.error('图片代理错误:', error);
    res.status(500).json({ error: '图片代理服务异常' });
  }
});

// API: 网络信息
app.get('/api/network-info', (req, res) => {
  const os = require('os');
  const networkInterfaces = os.networkInterfaces();
  
  // 查找局域网IP
  let localIP = 'localhost';
  for (const [name, interfaces] of Object.entries(networkInterfaces)) {
    for (const iface of interfaces) {
      if (iface.family === 'IPv4' && !iface.internal && iface.address.startsWith('192.168.')) {
        localIP = iface.address;
        break;
      }
    }
  }
  
  res.json({
    localIP,
    hostname: os.hostname(),
    platform: os.platform(),
    apiEndpoints: {
      health: '/api/health',
      comicData: '/api/store/comic-generation-results',
      staticImages: '/static/images/',
      imageProxy: '/proxy/image/'
    },
    timestamp: new Date().toISOString()
  });
});

// HTTP健康检查端点（兼容性）
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    connectedClients: clients.size,
    timestamp: new Date().toISOString()
  });
});

// 获取连接状态
app.get('/status', (req, res) => {
  const clientList = Array.from(clients.values()).map(client => ({
    id: client.id,
    deviceType: client.deviceType,
    connectedAt: client.connectedAt,
    lastActivity: client.lastActivity
  }));
  
  res.json({
    server: 'CosyVoice WebSocket Server',
    status: 'running',
    connectedClients: clients.size,
    clients: clientList,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  });
});



// 启动服务器
const PORT = process.env.PORT || 3001;
server.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 CosyVoice WebSocket + API服务器启动成功!');
  console.log(`📡 WebSocket: ws://localhost:${PORT}/ws`);
  console.log(`🌐 HTTP API: http://localhost:${PORT}`);
  console.log(`💚 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`📊 状态查看: http://localhost:${PORT}/status`);
  console.log(`📖 漫画数据: http://localhost:${PORT}/api/store/comic-generation-results`);
  console.log(`🖼️ 静态图片: http://localhost:${PORT}/static/images/`);
  console.log(`🔗 图片代理: http://localhost:${PORT}/proxy/image/`);
  console.log(`🌍 局域网访问: ws://[你的IP]:${PORT}/ws`);
  console.log(`🌍 局域网API: http://[你的IP]:${PORT}/api`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('💤 服务器正在关闭...');
  
  // 通知所有客户端服务器即将关闭
  broadcastToAll('SERVER_SHUTDOWN_WARNING', {
    message: '服务器即将关闭，请准备重连',
    shutdownIn: 5000
  });
  
  setTimeout(() => {
    broadcastToAll('SERVER_SHUTDOWN_FINAL', {
      message: '服务器已关闭',
      timestamp: new Date().toISOString()
    });
    
    server.close(() => {
      console.log('✅ 服务器已关闭');
      process.exit(0);
    });
  }, 5000);
});

// 清理不活跃连接
setInterval(() => {
  const now = new Date();
  const inactiveThreshold = 5 * 60 * 1000; // 5分钟
  
  clients.forEach((client, clientId) => {
    if (now - client.lastActivity > inactiveThreshold) {
      console.log(`🧹 清理不活跃连接: ${clientId}`);
      client.ws.terminate();
      clients.delete(clientId);
    }
  });
}, 60000); // 每分钟检查一次 