/*!
 * @pixi/app - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/app is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/core"),i=require("@pixi/display"),n=function(){function i(){}return i.init=function(e){var i=this;Object.defineProperty(this,"resizeTo",{set:function(e){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=e,e&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get:function(){return this._resizeTo}}),this.queueResize=function(){i._resizeTo&&(i.cancelResize(),i._resizeId=requestAnimationFrame((function(){return i.resize()})))},this.cancelResize=function(){i._resizeId&&(cancelAnimationFrame(i._resizeId),i._resizeId=null)},this.resize=function(){if(i._resizeTo){var e,n;if(i.cancelResize(),i._resizeTo===globalThis.window)e=globalThis.innerWidth,n=globalThis.innerHeight;else{var t=i._resizeTo;e=t.clientWidth,n=t.clientHeight}i.renderer.resize(e,n)}},this._resizeId=null,this._resizeTo=null,this.resizeTo=e.resizeTo||null},i.destroy=function(){globalThis.removeEventListener("resize",this.queueResize),this.cancelResize(),this.cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null},i.extension=e.ExtensionType.Application,i}(),t=function(){function n(t){var r=this;this.stage=new i.Container,t=Object.assign({forceCanvas:!1},t),this.renderer=e.autoDetectRenderer(t),n._plugins.forEach((function(e){e.init.call(r,t)}))}return n.registerPlugin=function(i){e.extensions.add({type:e.ExtensionType.Application,ref:i})},n.prototype.render=function(){this.renderer.render(this.stage)},Object.defineProperty(n.prototype,"view",{get:function(){return this.renderer.view},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"screen",{get:function(){return this.renderer.screen},enumerable:!1,configurable:!0}),n.prototype.destroy=function(e,i){var t=this,r=n._plugins.slice(0);r.reverse(),r.forEach((function(e){e.destroy.call(t)})),this.stage.destroy(i),this.stage=null,this.renderer.destroy(e),this.renderer=null},n._plugins=[],n}();e.extensions.handleByList(e.ExtensionType.Application,t._plugins),e.extensions.add(n),exports.Application=t,exports.ResizePlugin=n;
//# sourceMappingURL=app.min.js.map
