/**
 * 🎨 轻量级占位符样式
 * 替代Base64 SVG占位符，减少内存占用
 */

/* 通用占位符样式 */
.style-image-placeholder {
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-height: 150px;
  min-width: 200px;
}

.style-image-placeholder::before {
  content: "📷";
  font-size: 2rem;
  color: #999;
  margin-bottom: 8px;
  display: block;
}

.style-image-placeholder::after {
  content: "加载中...";
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* 错误占位符样式 */
.style-image-error {
  background: linear-gradient(135deg, #ffeaea 0%, #ffcccc 100%);
  border: 2px dashed #ff9999;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-height: 150px;
  min-width: 200px;
}

.style-image-error::before {
  content: "❌";
  font-size: 2rem;
  color: #cc0000;
  margin-bottom: 8px;
  display: block;
}

.style-image-error::after {
  content: "图片加载失败";
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: #cc0000;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* 内存优化的通用图片占位符 */
.memory-optimized-placeholder {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 12px;
  min-height: 100px;
  width: 100%;
  height: 100%;
}

.memory-optimized-placeholder::before {
  content: "🖼️";
  font-size: 1.5rem;
  margin-bottom: 4px;
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .style-image-placeholder,
  .style-image-error {
    min-height: 120px;
    min-width: 150px;
  }
  
  .style-image-placeholder::before,
  .style-image-error::before {
    font-size: 1.5rem;
  }
  
  .style-image-placeholder::after,
  .style-image-error::after {
    font-size: 12px;
  }
}

/* 动画效果（可选，轻量级） */
.style-image-placeholder {
  animation: placeholderPulse 2s ease-in-out infinite;
}

@keyframes placeholderPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}