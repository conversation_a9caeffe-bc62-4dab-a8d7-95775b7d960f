/*!
 * @pixi/text - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/text is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t,e=require("@pixi/sprite"),i=require("@pixi/core"),r=require("@pixi/settings"),n=require("@pixi/math"),o=require("@pixi/utils"),s=function(t,e){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},s(t,e)};exports.TEXT_GRADIENT=void 0,(t=exports.TEXT_GRADIENT||(exports.TEXT_GRADIENT={}))[t.LINEAR_VERTICAL=0]="LINEAR_VERTICAL",t[t.LINEAR_HORIZONTAL=1]="LINEAR_HORIZONTAL";var a={align:"left",breakWords:!1,dropShadow:!1,dropShadowAlpha:1,dropShadowAngle:Math.PI/6,dropShadowBlur:0,dropShadowColor:"black",dropShadowDistance:5,fill:"black",fillGradientType:exports.TEXT_GRADIENT.LINEAR_VERTICAL,fillGradientStops:[],fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",letterSpacing:0,lineHeight:0,lineJoin:"miter",miterLimit:10,padding:0,stroke:"black",strokeThickness:0,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100,leading:0},h=["serif","sans-serif","monospace","cursive","fantasy","system-ui"],l=function(){function t(t){this.styleID=0,this.reset(),f(this,t,t)}return t.prototype.clone=function(){var e={};return f(e,this,a),new t(e)},t.prototype.reset=function(){f(this,a,a)},Object.defineProperty(t.prototype,"align",{get:function(){return this._align},set:function(t){this._align!==t&&(this._align=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"breakWords",{get:function(){return this._breakWords},set:function(t){this._breakWords!==t&&(this._breakWords=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadow",{get:function(){return this._dropShadow},set:function(t){this._dropShadow!==t&&(this._dropShadow=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowAlpha",{get:function(){return this._dropShadowAlpha},set:function(t){this._dropShadowAlpha!==t&&(this._dropShadowAlpha=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowAngle",{get:function(){return this._dropShadowAngle},set:function(t){this._dropShadowAngle!==t&&(this._dropShadowAngle=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowBlur",{get:function(){return this._dropShadowBlur},set:function(t){this._dropShadowBlur!==t&&(this._dropShadowBlur=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowColor",{get:function(){return this._dropShadowColor},set:function(t){var e=u(t);this._dropShadowColor!==e&&(this._dropShadowColor=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowDistance",{get:function(){return this._dropShadowDistance},set:function(t){this._dropShadowDistance!==t&&(this._dropShadowDistance=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fill",{get:function(){return this._fill},set:function(t){var e=u(t);this._fill!==e&&(this._fill=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fillGradientType",{get:function(){return this._fillGradientType},set:function(t){this._fillGradientType!==t&&(this._fillGradientType=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fillGradientStops",{get:function(){return this._fillGradientStops},set:function(t){(function(t,e){if(!Array.isArray(t)||!Array.isArray(e))return!1;if(t.length!==e.length)return!1;for(var i=0;i<t.length;++i)if(t[i]!==e[i])return!1;return!0})(this._fillGradientStops,t)||(this._fillGradientStops=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontFamily",{get:function(){return this._fontFamily},set:function(t){this.fontFamily!==t&&(this._fontFamily=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontSize",{get:function(){return this._fontSize},set:function(t){this._fontSize!==t&&(this._fontSize=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontStyle",{get:function(){return this._fontStyle},set:function(t){this._fontStyle!==t&&(this._fontStyle=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontVariant",{get:function(){return this._fontVariant},set:function(t){this._fontVariant!==t&&(this._fontVariant=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontWeight",{get:function(){return this._fontWeight},set:function(t){this._fontWeight!==t&&(this._fontWeight=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"letterSpacing",{get:function(){return this._letterSpacing},set:function(t){this._letterSpacing!==t&&(this._letterSpacing=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lineHeight",{get:function(){return this._lineHeight},set:function(t){this._lineHeight!==t&&(this._lineHeight=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"leading",{get:function(){return this._leading},set:function(t){this._leading!==t&&(this._leading=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lineJoin",{get:function(){return this._lineJoin},set:function(t){this._lineJoin!==t&&(this._lineJoin=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"miterLimit",{get:function(){return this._miterLimit},set:function(t){this._miterLimit!==t&&(this._miterLimit=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"padding",{get:function(){return this._padding},set:function(t){this._padding!==t&&(this._padding=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"stroke",{get:function(){return this._stroke},set:function(t){var e=u(t);this._stroke!==e&&(this._stroke=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"strokeThickness",{get:function(){return this._strokeThickness},set:function(t){this._strokeThickness!==t&&(this._strokeThickness=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"textBaseline",{get:function(){return this._textBaseline},set:function(t){this._textBaseline!==t&&(this._textBaseline=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"trim",{get:function(){return this._trim},set:function(t){this._trim!==t&&(this._trim=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"whiteSpace",{get:function(){return this._whiteSpace},set:function(t){this._whiteSpace!==t&&(this._whiteSpace=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"wordWrap",{get:function(){return this._wordWrap},set:function(t){this._wordWrap!==t&&(this._wordWrap=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"wordWrapWidth",{get:function(){return this._wordWrapWidth},set:function(t){this._wordWrapWidth!==t&&(this._wordWrapWidth=t,this.styleID++)},enumerable:!1,configurable:!0}),t.prototype.toFontString=function(){var t="number"==typeof this.fontSize?this.fontSize+"px":this.fontSize,e=this.fontFamily;Array.isArray(this.fontFamily)||(e=this.fontFamily.split(","));for(var i=e.length-1;i>=0;i--){var r=e[i].trim();!/([\"\'])[^\'\"]+\1/.test(r)&&h.indexOf(r)<0&&(r='"'+r+'"'),e[i]=r}return this.fontStyle+" "+this.fontVariant+" "+this.fontWeight+" "+t+" "+e.join(",")},t}();function c(t){return"number"==typeof t?o.hex2string(t):("string"==typeof t&&0===t.indexOf("0x")&&(t=t.replace("0x","#")),t)}function u(t){if(Array.isArray(t)){for(var e=0;e<t.length;++e)t[e]=c(t[e]);return t}return c(t)}function f(t,e,i){for(var r in i)Array.isArray(e[r])?t[r]=e[r].slice():t[r]=e[r]}var p={willReadFrequently:!0},d=function(){function t(t,e,i,r,n,o,s,a,h){this.text=t,this.style=e,this.width=i,this.height=r,this.lines=n,this.lineWidths=o,this.lineHeight=s,this.maxLineWidth=a,this.fontProperties=h}return t.measureText=function(e,i,r,n){void 0===n&&(n=t._canvas),r=null==r?i.wordWrap:r;var o=i.toFontString(),s=t.measureFont(o);0===s.fontSize&&(s.fontSize=i.fontSize,s.ascent=i.fontSize);var a=n.getContext("2d",p);a.font=o;for(var h=(r?t.wordWrap(e,i,n):e).split(/(?:\r\n|\r|\n)/),l=new Array(h.length),c=0,u=0;u<h.length;u++){var f=a.measureText(h[u]).width+(h[u].length-1)*i.letterSpacing;l[u]=f,c=Math.max(c,f)}var d=c+i.strokeThickness;i.dropShadow&&(d+=i.dropShadowDistance);var g=i.lineHeight||s.fontSize+i.strokeThickness,y=Math.max(g,s.fontSize+i.strokeThickness)+(h.length-1)*(g+i.leading);return i.dropShadow&&(y+=i.dropShadowDistance),new t(e,i,d,y,h,l,g+i.leading,c,s)},t.wordWrap=function(e,i,r){void 0===r&&(r=t._canvas);for(var n=r.getContext("2d",p),o=0,s="",a="",h=Object.create(null),l=i.letterSpacing,c=i.whiteSpace,u=t.collapseSpaces(c),f=t.collapseNewlines(c),d=!u,g=i.wordWrapWidth+l,y=t.tokenize(e),_=0;_<y.length;_++){var b=y[_];if(t.isNewline(b)){if(!f){a+=t.addLine(s),d=!u,s="",o=0;continue}b=" "}if(u){var S=t.isBreakingSpace(b),m=t.isBreakingSpace(s[s.length-1]);if(S&&m)continue}var w=t.getFromCache(b,l,h,n);if(w>g)if(""!==s&&(a+=t.addLine(s),s="",o=0),t.canBreakWords(b,i.breakWords))for(var x=t.wordWrapSplit(b),v=0;v<x.length;v++){for(var T=x[v],I=1;x[v+I];){var k=x[v+I],O=T[T.length-1];if(t.canBreakChars(O,k,b,v,i.breakWords))break;T+=k,I++}v+=T.length-1;var A=t.getFromCache(T,l,h,n);A+o>g&&(a+=t.addLine(s),d=!1,s="",o=0),s+=T,o+=A}else{s.length>0&&(a+=t.addLine(s),s="",o=0);var L=_===y.length-1;a+=t.addLine(b,!L),d=!1,s="",o=0}else w+o>g&&(d=!1,a+=t.addLine(s),s="",o=0),(s.length>0||!t.isBreakingSpace(b)||d)&&(s+=b,o+=w)}return a+=t.addLine(s,!1)},t.addLine=function(e,i){return void 0===i&&(i=!0),e=t.trimRight(e),e=i?e+"\n":e},t.getFromCache=function(t,e,i,r){var n=i[t];if("number"!=typeof n){var o=t.length*e;n=r.measureText(t).width+o,i[t]=n}return n},t.collapseSpaces=function(t){return"normal"===t||"pre-line"===t},t.collapseNewlines=function(t){return"normal"===t},t.trimRight=function(e){if("string"!=typeof e)return"";for(var i=e.length-1;i>=0;i--){var r=e[i];if(!t.isBreakingSpace(r))break;e=e.slice(0,-1)}return e},t.isNewline=function(e){return"string"==typeof e&&t._newlines.indexOf(e.charCodeAt(0))>=0},t.isBreakingSpace=function(e,i){return"string"==typeof e&&t._breakingSpaces.indexOf(e.charCodeAt(0))>=0},t.tokenize=function(e){var i=[],r="";if("string"!=typeof e)return i;for(var n=0;n<e.length;n++){var o=e[n],s=e[n+1];t.isBreakingSpace(o,s)||t.isNewline(o)?(""!==r&&(i.push(r),r=""),i.push(o)):r+=o}return""!==r&&i.push(r),i},t.canBreakWords=function(t,e){return e},t.canBreakChars=function(t,e,i,r,n){return!0},t.wordWrapSplit=function(t){return t.split("")},t.measureFont=function(e){if(t._fonts[e])return t._fonts[e];var i={ascent:0,descent:0,fontSize:0},r=t._canvas,n=t._context;n.font=e;var o=t.METRICS_STRING+t.BASELINE_SYMBOL,s=Math.ceil(n.measureText(o).width),a=Math.ceil(n.measureText(t.BASELINE_SYMBOL).width),h=Math.ceil(t.HEIGHT_MULTIPLIER*a);a=a*t.BASELINE_MULTIPLIER|0,r.width=s,r.height=h,n.fillStyle="#f00",n.fillRect(0,0,s,h),n.font=e,n.textBaseline="alphabetic",n.fillStyle="#000",n.fillText(o,0,a);var l=n.getImageData(0,0,s,h).data,c=l.length,u=4*s,f=0,p=0,d=!1;for(f=0;f<a;++f){for(var g=0;g<u;g+=4)if(255!==l[p+g]){d=!0;break}if(d)break;p+=u}for(i.ascent=a-f,p=c-u,d=!1,f=h;f>a;--f){for(g=0;g<u;g+=4)if(255!==l[p+g]){d=!0;break}if(d)break;p-=u}return i.descent=f-a,i.fontSize=i.ascent+i.descent,t._fonts[e]=i,i},t.clearMetrics=function(e){void 0===e&&(e=""),e?delete t._fonts[e]:t._fonts={}},Object.defineProperty(t,"_canvas",{get:function(){if(!t.__canvas){var e=void 0;try{var i=new OffscreenCanvas(0,0),n=i.getContext("2d",p);if(n&&n.measureText)return t.__canvas=i,i;e=r.settings.ADAPTER.createCanvas()}catch(t){e=r.settings.ADAPTER.createCanvas()}e.width=e.height=10,t.__canvas=e}return t.__canvas},enumerable:!1,configurable:!0}),Object.defineProperty(t,"_context",{get:function(){return t.__context||(t.__context=t._canvas.getContext("2d",p)),t.__context},enumerable:!1,configurable:!0}),t}();d._fonts={},d.METRICS_STRING="|ÉqÅ",d.BASELINE_SYMBOL="M",d.BASELINE_MULTIPLIER=1.4,d.HEIGHT_MULTIPLIER=2,d._newlines=[10,13],d._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];var g={texture:!0,children:!1,baseTexture:!0},y=function(t){function e(e,o,s){var a=this,h=!1;s||(s=r.settings.ADAPTER.createCanvas(),h=!0),s.width=3,s.height=3;var l=i.Texture.from(s);return l.orig=new n.Rectangle,l.trim=new n.Rectangle,(a=t.call(this,l)||this)._ownCanvas=h,a.canvas=s,a.context=s.getContext("2d",{willReadFrequently:!0}),a._resolution=r.settings.RESOLUTION,a._autoResolution=!0,a._text=null,a._style=null,a._styleListener=null,a._font="",a.text=e,a.style=o,a.localStyleID=-1,a}return function(t,e){function i(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(e,t),e.prototype.updateText=function(t){var i=this._style;if(this.localStyleID!==i.styleID&&(this.dirty=!0,this.localStyleID=i.styleID),this.dirty||!t){this._font=this._style.toFontString();var r,n,s=this.context,a=d.measureText(this._text||" ",this._style,this._style.wordWrap,this.canvas),h=a.width,l=a.height,c=a.lines,u=a.lineHeight,f=a.lineWidths,p=a.maxLineWidth,g=a.fontProperties;this.canvas.width=Math.ceil(Math.ceil(Math.max(1,h)+2*i.padding)*this._resolution),this.canvas.height=Math.ceil(Math.ceil(Math.max(1,l)+2*i.padding)*this._resolution),s.scale(this._resolution,this._resolution),s.clearRect(0,0,this.canvas.width,this.canvas.height),s.font=this._font,s.lineWidth=i.strokeThickness,s.textBaseline=i.textBaseline,s.lineJoin=i.lineJoin,s.miterLimit=i.miterLimit;for(var y=i.dropShadow?2:1,_=0;_<y;++_){var b=i.dropShadow&&0===_,S=b?Math.ceil(Math.max(1,l)+2*i.padding):0,m=S*this._resolution;if(b){s.fillStyle="black",s.strokeStyle="black";var w=i.dropShadowColor,x=o.hex2rgb("number"==typeof w?w:o.string2hex(w)),v=i.dropShadowBlur*this._resolution,T=i.dropShadowDistance*this._resolution;s.shadowColor="rgba("+255*x[0]+","+255*x[1]+","+255*x[2]+","+i.dropShadowAlpha+")",s.shadowBlur=v,s.shadowOffsetX=Math.cos(i.dropShadowAngle)*T,s.shadowOffsetY=Math.sin(i.dropShadowAngle)*T+m}else s.fillStyle=this._generateFillStyle(i,c,a),s.strokeStyle=i.stroke,s.shadowColor="black",s.shadowBlur=0,s.shadowOffsetX=0,s.shadowOffsetY=0;var I=(u-g.fontSize)/2;(!e.nextLineHeightBehavior||u-g.fontSize<0)&&(I=0);for(var k=0;k<c.length;k++)r=i.strokeThickness/2,n=i.strokeThickness/2+k*u+g.ascent+I,"right"===i.align?r+=p-f[k]:"center"===i.align&&(r+=(p-f[k])/2),i.stroke&&i.strokeThickness&&this.drawLetterSpacing(c[k],r+i.padding,n+i.padding-S,!0),i.fill&&this.drawLetterSpacing(c[k],r+i.padding,n+i.padding-S)}this.updateTexture()}},e.prototype.drawLetterSpacing=function(t,i,r,n){void 0===n&&(n=!1);var o=this._style.letterSpacing,s=e.experimentalLetterSpacing&&("letterSpacing"in CanvasRenderingContext2D.prototype||"textLetterSpacing"in CanvasRenderingContext2D.prototype);if(0===o||s)return s&&(this.context.letterSpacing=o,this.context.textLetterSpacing=o),void(n?this.context.strokeText(t,i,r):this.context.fillText(t,i,r));for(var a=i,h=Array.from?Array.from(t):t.split(""),l=this.context.measureText(t).width,c=0,u=0;u<h.length;++u){var f=h[u];n?this.context.strokeText(f,a,r):this.context.fillText(f,a,r);for(var p="",d=u+1;d<h.length;++d)p+=h[d];a+=l-(c=this.context.measureText(p).width)+o,l=c}},e.prototype.updateTexture=function(){var t=this.canvas;if(this._style.trim){var e=o.trimCanvas(t);e.data&&(t.width=e.width,t.height=e.height,this.context.putImageData(e.data,0,0))}var i=this._texture,r=this._style,n=r.trim?0:r.padding,s=i.baseTexture;i.trim.width=i._frame.width=t.width/this._resolution,i.trim.height=i._frame.height=t.height/this._resolution,i.trim.x=-n,i.trim.y=-n,i.orig.width=i._frame.width-2*n,i.orig.height=i._frame.height-2*n,this._onTextureUpdate(),s.setRealSize(t.width,t.height,this._resolution),i.updateUvs(),this.dirty=!1},e.prototype._render=function(e){this._autoResolution&&this._resolution!==e.resolution&&(this._resolution=e.resolution,this.dirty=!0),this.updateText(!0),t.prototype._render.call(this,e)},e.prototype.updateTransform=function(){this.updateText(!0),t.prototype.updateTransform.call(this)},e.prototype.getBounds=function(e,i){return this.updateText(!0),-1===this._textureID&&(e=!1),t.prototype.getBounds.call(this,e,i)},e.prototype.getLocalBounds=function(e){return this.updateText(!0),t.prototype.getLocalBounds.call(this,e)},e.prototype._calculateBounds=function(){this.calculateVertices(),this._bounds.addQuad(this.vertexData)},e.prototype._generateFillStyle=function(t,e,i){var r,n=t.fill;if(!Array.isArray(n))return n;if(1===n.length)return n[0];var o=t.dropShadow?t.dropShadowDistance:0,s=t.padding||0,a=this.canvas.width/this._resolution-o-2*s,h=this.canvas.height/this._resolution-o-2*s,l=n.slice(),c=t.fillGradientStops.slice();if(!c.length)for(var u=l.length+1,f=1;f<u;++f)c.push(f/u);if(l.unshift(n[0]),c.unshift(0),l.push(n[n.length-1]),c.push(1),t.fillGradientType===exports.TEXT_GRADIENT.LINEAR_VERTICAL){r=this.context.createLinearGradient(a/2,s,a/2,h+s);var p=i.fontProperties.fontSize+t.strokeThickness;for(f=0;f<e.length;f++){var d=i.lineHeight*(f-1)+p,g=i.lineHeight*f,y=g;f>0&&d>g&&(y=(g+d)/2);var _=g+p,b=i.lineHeight*(f+1),S=_;f+1<e.length&&b<_&&(S=(_+b)/2);for(var m=(S-y)/h,w=0;w<l.length;w++){var x=0;x="number"==typeof c[w]?c[w]:w/l.length;var v=Math.min(1,Math.max(0,y/h+x*m));v=Number(v.toFixed(5)),r.addColorStop(v,l[w])}}}else{r=this.context.createLinearGradient(s,h/2,a+s,h/2);var T=l.length+1,I=1;for(f=0;f<l.length;f++){var k=void 0;k="number"==typeof c[f]?c[f]:I/T,r.addColorStop(k,l[f]),I++}}return r},e.prototype.destroy=function(e){"boolean"==typeof e&&(e={children:e}),e=Object.assign({},g,e),t.prototype.destroy.call(this,e),this._ownCanvas&&(this.canvas.height=this.canvas.width=0),this.context=null,this.canvas=null,this._style=null},Object.defineProperty(e.prototype,"width",{get:function(){return this.updateText(!0),Math.abs(this.scale.x)*this._texture.orig.width},set:function(t){this.updateText(!0);var e=o.sign(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this.updateText(!0),Math.abs(this.scale.y)*this._texture.orig.height},set:function(t){this.updateText(!0);var e=o.sign(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"style",{get:function(){return this._style},set:function(t){t=t||{},this._style=t instanceof l?t:new l(t),this.localStyleID=-1,this.dirty=!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"text",{get:function(){return this._text},set:function(t){t=String(null==t?"":t),this._text!==t&&(this._text=t,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resolution",{get:function(){return this._resolution},set:function(t){this._autoResolution=!1,this._resolution!==t&&(this._resolution=t,this.dirty=!0)},enumerable:!1,configurable:!0}),e.nextLineHeightBehavior=!1,e.experimentalLetterSpacing=!1,e}(e.Sprite);exports.Text=y,exports.TextMetrics=d,exports.TextStyle=l;
//# sourceMappingURL=text.min.js.map
