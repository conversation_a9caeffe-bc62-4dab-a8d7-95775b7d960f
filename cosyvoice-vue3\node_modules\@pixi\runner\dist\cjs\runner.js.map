{"version": 3, "file": "runner.js", "sources": ["../../src/Runner.ts"], "sourcesContent": ["/**\n * A Runner is a highly performant and simple alternative to signals. Best used in situations\n * where events are dispatched to many objects at high frequency (say every frame!)\n *\n *\n * like a signal..\n * ```\n * import { Runner } from '@pixi/runner';\n *\n * const myObject = {\n *     loaded: new Runner('loaded')\n * }\n *\n * const listener = {\n *     loaded: function(){\n *         // thin\n *     }\n * }\n *\n * myObject.loaded.add(listener);\n *\n * myObject.loaded.emit();\n * ```\n *\n * Or for handling calling the same function on many items\n * ```\n * import { Runner } from '@pixi/runner';\n *\n * const myGame = {\n *     update: new Runner('update')\n * }\n *\n * const gameObject = {\n *     update: function(time){\n *         // update my gamey state\n *     }\n * }\n *\n * myGame.update.add(gameObject);\n *\n * myGame.update.emit(time);\n * ```\n * @memberof PIXI\n */\nexport class Runner\n{\n    public items: any[];\n    private _name: string;\n    private _aliasCount: number;\n\n    /**\n     * @param name - The function name that will be executed on the listeners added to this Runner.\n     */\n    constructor(name: string)\n    {\n        this.items = [];\n        this._name = name;\n        this._aliasCount = 0;\n    }\n\n    /* eslint-disable jsdoc/require-param, jsdoc/check-param-names */\n    /**\n     * Dispatch/Broadcast Runner to all listeners added to the queue.\n     * @param {...any} params - (optional) parameters to pass to each listener\n     */\n    /*  eslint-enable jsdoc/require-param, jsdoc/check-param-names */\n    public emit(a0?: unknown, a1?: unknown, a2?: unknown, a3?: unknown,\n        a4?: unknown, a5?: unknown, a6?: unknown, a7?: unknown): this\n    {\n        if (arguments.length > 8)\n        {\n            throw new Error('max arguments reached');\n        }\n\n        const { name, items } = this;\n\n        this._aliasCount++;\n\n        for (let i = 0, len = items.length; i < len; i++)\n        {\n            items[i][name](a0, a1, a2, a3, a4, a5, a6, a7);\n        }\n\n        if (items === this.items)\n        {\n            this._aliasCount--;\n        }\n\n        return this;\n    }\n\n    private ensureNonAliasedItems(): void\n    {\n        if (this._aliasCount > 0 && this.items.length > 1)\n        {\n            this._aliasCount = 0;\n            this.items = this.items.slice(0);\n        }\n    }\n\n    /**\n     * Add a listener to the Runner\n     *\n     * Runners do not need to have scope or functions passed to them.\n     * All that is required is to pass the listening object and ensure that it has contains a function that has the same name\n     * as the name provided to the Runner when it was created.\n     *\n     * Eg A listener passed to this Runner will require a 'complete' function.\n     *\n     * ```\n     * import { Runner } from '@pixi/runner';\n     *\n     * const complete = new Runner('complete');\n     * ```\n     *\n     * The scope used will be the object itself.\n     * @param {any} item - The object that will be listening.\n     */\n    public add(item: unknown): this\n    {\n        if ((item as any)[this._name])\n        {\n            this.ensureNonAliasedItems();\n            this.remove(item);\n            this.items.push(item);\n        }\n\n        return this;\n    }\n\n    /**\n     * Remove a single listener from the dispatch queue.\n     * @param {any} item - The listener that you would like to remove.\n     */\n    public remove(item: unknown): this\n    {\n        const index = this.items.indexOf(item);\n\n        if (index !== -1)\n        {\n            this.ensureNonAliasedItems();\n            this.items.splice(index, 1);\n        }\n\n        return this;\n    }\n\n    /**\n     * Check to see if the listener is already in the Runner\n     * @param {any} item - The listener that you would like to check.\n     */\n    public contains(item: unknown): boolean\n    {\n        return this.items.indexOf(item) !== -1;\n    }\n\n    /** Remove all listeners from the Runner */\n    public removeAll(): this\n    {\n        this.ensureNonAliasedItems();\n        this.items.length = 0;\n\n        return this;\n    }\n\n    /** Remove all references, don't use after this. */\n    public destroy(): void\n    {\n        this.removeAll();\n        this.items = null;\n        this._name = null;\n    }\n\n    /**\n     * `true` if there are no this Runner contains no listeners\n     * @readonly\n     */\n    public get empty(): boolean\n    {\n        return this.items.length === 0;\n    }\n\n    /**\n     * The name of the runner.\n     * @readonly\n     */\n    public get name(): string\n    {\n        return this._name;\n    }\n}\n\nObject.defineProperties(Runner.prototype, {\n    /**\n     * Alias for `emit`\n     * @memberof PIXI.Runner#\n     * @method dispatch\n     * @see PIXI.Runner#emit\n     */\n    dispatch: { value: Runner.prototype.emit },\n    /**\n     * Alias for `emit`\n     * @memberof PIXI.Runner#\n     * @method run\n     * @see PIXI.Runner#emit\n     */\n    run: { value: Runner.prototype.emit },\n});\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CG;AACH,IAAA,MAAA,kBAAA,YAAA;AAMI;;AAEG;AACH,IAAA,SAAA,MAAA,CAAY,IAAY,EAAA;AAEpB,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;KACxB;;AAGD;;;AAGG;;AAEI,IAAA,MAAA,CAAA,SAAA,CAAA,IAAI,GAAX,UAAY,EAAY,EAAE,EAAY,EAAE,EAAY,EAAE,EAAY,EAC9D,EAAY,EAAE,EAAY,EAAE,EAAY,EAAE,EAAY,EAAA;AAEtD,QAAA,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EACxB;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC5C,SAAA;QAEK,IAAA,EAAA,GAAkB,IAAI,EAApB,IAAI,UAAA,EAAE,KAAK,WAAS,CAAC;QAE7B,IAAI,CAAC,WAAW,EAAE,CAAC;AAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAChD;YACI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAClD,SAAA;AAED,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EACxB;YACI,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAEO,IAAA,MAAA,CAAA,SAAA,CAAA,qBAAqB,GAA7B,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EACjD;AACI,YAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpC,SAAA;KACJ,CAAA;AAED;;;;;;;;;;;;;;;;;AAiBG;IACI,MAAG,CAAA,SAAA,CAAA,GAAA,GAAV,UAAW,IAAa,EAAA;AAEpB,QAAA,IAAK,IAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAC7B;YACI,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClB,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACI,MAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,IAAa,EAAA;QAEvB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAEvC,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAChB;YACI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC/B,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACI,MAAQ,CAAA,SAAA,CAAA,QAAA,GAAf,UAAgB,IAAa,EAAA;QAEzB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KAC1C,CAAA;;AAGM,IAAA,MAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,YAAA;QAEI,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAEtB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;;AAGM,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KACrB,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAW,MAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AAJhB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;SAClC;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAW,MAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAJf;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;;;AAAA,KAAA,CAAA,CAAA;IACL,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,EAAE;AACtC;;;;;AAKG;IACH,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;AAC1C;;;;;AAKG;IACH,GAAG,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;AACxC,CAAA,CAAC;;;;"}