{"version": 3, "file": "extract.min.js", "sources": ["../../src/Extract.ts"], "sourcesContent": ["import { MSAA_QUALITY } from '@pixi/constants';\nimport { CanvasRenderTarget } from '@pixi/utils';\nimport { Rectangle } from '@pixi/math';\nimport { ExtensionType, RenderTexture } from '@pixi/core';\n\nimport type { Renderer, IRendererPlugin, ExtensionMetadata } from '@pixi/core';\nimport type { DisplayObject } from '@pixi/display';\n\nconst TEMP_RECT = new Rectangle();\nconst BYTES_PER_PIXEL = 4;\n\n/**\n * this interface is used to extract only  a single pixel of Render Texture or Display Object\n * if you use this Interface all fields is required\n * @deprecated\n * @example\n * test: PixelExtractOptions = { x: 15, y: 20, resolution: 4, width: 10, height: 10 }\n */\nexport interface PixelExtractOptions\n{\n    x: number,\n    y: number,\n    height: number,\n    resolution: number,\n    width: number\n}\n\n/**\n * This class provides renderer-specific plugins for exporting content from a renderer.\n * For instance, these plugins can be used for saving an Image, Canvas element or for exporting the raw image data (pixels).\n *\n * Do not instantiate these plugins directly. It is available from the `renderer.plugins` property.\n * See {@link PIXI.CanvasRenderer#plugins} or {@link PIXI.Renderer#plugins}.\n * @example\n * // Create a new app (will auto-add extract plugin to renderer)\n * const app = new PIXI.Application();\n *\n * // Draw a red circle\n * const graphics = new PIXI.Graphics()\n *     .beginFill(0xFF0000)\n *     .drawCircle(0, 0, 50);\n *\n * // Render the graphics as an HTMLImageElement\n * const image = app.renderer.plugins.extract.image(graphics);\n * document.body.appendChild(image);\n * @memberof PIXI\n */\n\nexport class Extract implements IRendererPlugin\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'extract',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    private renderer: Renderer;\n\n    /**\n     * @param renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n    }\n\n    /**\n     * Will return a HTML Image of the target\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param format - Image format, e.g. \"image/jpeg\" or \"image/webp\".\n     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.\n     * @returns - HTML Image of the target\n     */\n    public image(target: DisplayObject | RenderTexture, format?: string, quality?: number): HTMLImageElement\n    {\n        const image = new Image();\n\n        image.src = this.base64(target, format, quality);\n\n        return image;\n    }\n\n    /**\n     * Will return a base64 encoded string of this target. It works by calling\n     *  `Extract.getCanvas` and then running toDataURL on that.\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param format - Image format, e.g. \"image/jpeg\" or \"image/webp\".\n     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.\n     * @returns - A base64 encoded string of the texture.\n     */\n    public base64(target: DisplayObject | RenderTexture, format?: string, quality?: number): string\n    {\n        return this.canvas(target).toDataURL(format, quality);\n    }\n\n    /**\n     * Creates a Canvas element, renders this target to it and then returns it.\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - A Canvas element with the texture rendered on.\n     */\n    public canvas(target?: DisplayObject | RenderTexture, frame?: Rectangle): HTMLCanvasElement\n    {\n        const { pixels, width, height, flipY } = this._rawPixels(target, frame);\n\n        let canvasBuffer = new CanvasRenderTarget(width, height, 1);\n\n        // Add the pixels to the canvas\n        const canvasData = canvasBuffer.context.getImageData(0, 0, width, height);\n\n        Extract.arrayPostDivide(pixels, canvasData.data);\n\n        canvasBuffer.context.putImageData(canvasData, 0, 0);\n\n        // Flipping pixels\n        if (flipY)\n        {\n            const target = new CanvasRenderTarget(canvasBuffer.width, canvasBuffer.height, 1);\n\n            target.context.scale(1, -1);\n\n            // We can't render to itself because we should be empty before render.\n            target.context.drawImage(canvasBuffer.canvas, 0, -height);\n\n            canvasBuffer.destroy();\n            canvasBuffer = target;\n        }\n\n        // Send the canvas back\n        return canvasBuffer.canvas;\n    }\n\n    /**\n     * Will return a one-dimensional array containing the pixel data of the entire texture in RGBA\n     * order, with integer values between 0 and 255 (included).\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - One-dimensional array containing the pixel data of the entire texture\n     */\n    public pixels(target?: DisplayObject | RenderTexture, frame?: Rectangle | PixelExtractOptions): Uint8Array\n    {\n        const { pixels } = this._rawPixels(target, frame as Rectangle);\n\n        Extract.arrayPostDivide(pixels, pixels);\n\n        return pixels;\n    }\n\n    private _rawPixels(target?: DisplayObject | RenderTexture, frame?: Rectangle): {\n        pixels: Uint8Array, width: number, height: number, flipY: boolean,\n    }\n    {\n        const renderer = this.renderer;\n        let resolution;\n        let flipY = false;\n        let renderTexture;\n        let generated = false;\n\n        if (target)\n        {\n            if (target instanceof RenderTexture)\n            {\n                renderTexture = target;\n            }\n            else\n            {\n                const multisample = renderer.context.webGLVersion >= 2 ? renderer.multisample : MSAA_QUALITY.NONE;\n\n                renderTexture = this.renderer.generateTexture(target, { multisample });\n\n                if (multisample !== MSAA_QUALITY.NONE)\n                {\n                    // Resolve the multisampled texture to a non-multisampled texture\n                    const resolvedTexture = RenderTexture.create({\n                        width: renderTexture.width,\n                        height: renderTexture.height,\n                    });\n\n                    renderer.framebuffer.bind(renderTexture.framebuffer);\n                    renderer.framebuffer.blit(resolvedTexture.framebuffer);\n                    renderer.framebuffer.bind(null);\n\n                    renderTexture.destroy(true);\n                    renderTexture = resolvedTexture;\n                }\n\n                generated = true;\n            }\n        }\n\n        if (renderTexture)\n        {\n            resolution = renderTexture.baseTexture.resolution;\n            frame = frame ?? renderTexture.frame;\n            flipY = false;\n            renderer.renderTexture.bind(renderTexture);\n        }\n        else\n        {\n            resolution = renderer.resolution;\n\n            if (!frame)\n            {\n                frame = TEMP_RECT;\n                frame.width = renderer.width;\n                frame.height = renderer.height;\n            }\n\n            flipY = true;\n            renderer.renderTexture.bind(null);\n        }\n\n        const width = Math.round(frame.width * resolution);\n        const height = Math.round(frame.height * resolution);\n\n        const pixels = new Uint8Array(BYTES_PER_PIXEL * width * height);\n\n        // Read pixels to the array\n        const gl = renderer.gl;\n\n        gl.readPixels(\n            Math.round(frame.x * resolution),\n            Math.round(frame.y * resolution),\n            width,\n            height,\n            gl.RGBA,\n            gl.UNSIGNED_BYTE,\n            pixels\n        );\n\n        if (generated)\n        {\n            renderTexture.destroy(true);\n        }\n\n        return { pixels, width, height, flipY };\n    }\n\n    /** Destroys the extract. */\n    public destroy(): void\n    {\n        this.renderer = null;\n    }\n\n    /**\n     * Takes premultiplied pixel data and produces regular pixel data\n     * @private\n     * @param pixels - array of pixel data\n     * @param out - output array\n     */\n    static arrayPostDivide(\n        pixels: number[] | Uint8Array | Uint8ClampedArray, out: number[] | Uint8Array | Uint8ClampedArray\n    ): void\n    {\n        for (let i = 0; i < pixels.length; i += 4)\n        {\n            const alpha = out[i + 3] = pixels[i + 3];\n\n            if (alpha !== 0)\n            {\n                out[i] = Math.round(Math.min(pixels[i] * 255.0 / alpha, 255.0));\n                out[i + 1] = Math.round(Math.min(pixels[i + 1] * 255.0 / alpha, 255.0));\n                out[i + 2] = Math.round(Math.min(pixels[i + 2] * 255.0 / alpha, 255.0));\n            }\n            else\n            {\n                out[i] = pixels[i];\n                out[i + 1] = pixels[i + 1];\n                out[i + 2] = pixels[i + 2];\n            }\n        }\n    }\n}\n"], "names": ["TEMP_RECT", "Rectangle", "Extract", "renderer", "this", "prototype", "image", "target", "format", "quality", "Image", "src", "base64", "canvas", "toDataURL", "frame", "_a", "_rawPixels", "pixels", "width", "height", "flipY", "canvasBuffer", "CanvasRenderTarget", "canvasData", "context", "getImageData", "arrayPostDivide", "data", "putImageData", "target_1", "scale", "drawImage", "destroy", "resolution", "renderTexture", "generated", "RenderTexture", "multisample", "webGLVersion", "MSAA_QUALITY", "NONE", "generateTexture", "resolvedTexture", "create", "framebuffer", "bind", "blit", "baseTexture", "Math", "round", "Uint8Array", "gl", "readPixels", "x", "y", "RGBA", "UNSIGNED_BYTE", "out", "i", "length", "alpha", "min", "extension", "name", "type", "ExtensionType", "RendererPlugin"], "mappings": ";;;;;;;8KAQMA,EAAY,IAAIC,EAAAA,UAwCtBC,EAAA,WAaI,SAAAA,EAAYC,GAERC,KAAKD,SAAWA,EAqNxB,OA1MWD,EAAAG,UAAAC,MAAP,SAAaC,EAAuCC,EAAiBC,GAEjE,IAAMH,EAAQ,IAAII,MAIlB,OAFAJ,EAAMK,IAAMP,KAAKQ,OAAOL,EAAQC,EAAQC,GAEjCH,GAYJJ,EAAAG,UAAAO,OAAP,SAAcL,EAAuCC,EAAiBC,GAElE,OAAOL,KAAKS,OAAON,GAAQO,UAAUN,EAAQC,IAU1CP,EAAAG,UAAAQ,OAAP,SAAcN,EAAwCQ,GAE5C,IAAAC,EAAmCZ,KAAKa,WAAWV,EAAQQ,GAAzDG,EAAMF,EAAAE,OAAEC,EAAKH,EAAAG,MAAEC,EAAMJ,EAAAI,OAAEC,EAAKL,EAAAK,MAEhCC,EAAe,IAAIC,EAAkBA,mBAACJ,EAAOC,EAAQ,GAGnDI,EAAaF,EAAaG,QAAQC,aAAa,EAAG,EAAGP,EAAOC,GAOlE,GALAlB,EAAQyB,gBAAgBT,EAAQM,EAAWI,MAE3CN,EAAaG,QAAQI,aAAaL,EAAY,EAAG,GAG7CH,EACJ,CACI,IAAMS,EAAS,IAAIP,EAAAA,mBAAmBD,EAAaH,MAAOG,EAAaF,OAAQ,GAE/EU,EAAOL,QAAQM,MAAM,GAAI,GAGzBD,EAAOL,QAAQO,UAAUV,EAAaT,OAAQ,GAAIO,GAElDE,EAAaW,UACbX,EAAeQ,EAInB,OAAOR,EAAaT,QAWjBX,EAAAG,UAAAa,OAAP,SAAcX,EAAwCQ,GAE1C,IAAAG,EAAWd,KAAKa,WAAWV,EAAQQ,GAAmBG,OAI9D,OAFAhB,EAAQyB,gBAAgBT,EAAQA,GAEzBA,GAGHhB,EAAAG,UAAAY,WAAR,SAAmBV,EAAwCQ,GAIvD,IACImB,EAEAC,EAHEhC,EAAWC,KAAKD,SAElBkB,GAAQ,EAERe,GAAY,EAEhB,GAAI7B,EAEA,GAAIA,aAAkB8B,EAAAA,cAElBF,EAAgB5B,MAGpB,CACI,IAAM+B,EAAcnC,EAASsB,QAAQc,cAAgB,EAAIpC,EAASmC,YAAcE,EAAYA,aAACC,KAI7F,GAFAN,EAAgB/B,KAAKD,SAASuC,gBAAgBnC,EAAQ,CAAE+B,YAAWA,IAE/DA,IAAgBE,EAAYA,aAACC,KACjC,CAEI,IAAME,EAAkBN,EAAaA,cAACO,OAAO,CACzCzB,MAAOgB,EAAchB,MACrBC,OAAQe,EAAcf,SAG1BjB,EAAS0C,YAAYC,KAAKX,EAAcU,aACxC1C,EAAS0C,YAAYE,KAAKJ,EAAgBE,aAC1C1C,EAAS0C,YAAYC,KAAK,MAE1BX,EAAcF,SAAQ,GACtBE,EAAgBQ,EAGpBP,GAAY,EAIhBD,GAEAD,EAAaC,EAAca,YAAYd,WACvCnB,EAAQA,MAAAA,EAAAA,EAASoB,EAAcpB,MAC/BM,GAAQ,EACRlB,EAASgC,cAAcW,KAAKX,KAI5BD,EAAa/B,EAAS+B,WAEjBnB,KAEDA,EAAQf,GACFmB,MAAQhB,EAASgB,MACvBJ,EAAMK,OAASjB,EAASiB,QAG5BC,GAAQ,EACRlB,EAASgC,cAAcW,KAAK,OAGhC,IAAM3B,EAAQ8B,KAAKC,MAAMnC,EAAMI,MAAQe,GACjCd,EAAS6B,KAAKC,MAAMnC,EAAMK,OAASc,GAEnChB,EAAS,IAAIiC,WAlNH,EAkNgChC,EAAQC,GAGlDgC,EAAKjD,EAASiD,GAiBpB,OAfAA,EAAGC,WACCJ,KAAKC,MAAMnC,EAAMuC,EAAIpB,GACrBe,KAAKC,MAAMnC,EAAMwC,EAAIrB,GACrBf,EACAC,EACAgC,EAAGI,KACHJ,EAAGK,cACHvC,GAGAkB,GAEAD,EAAcF,SAAQ,GAGnB,CAAEf,OAAMA,EAAEC,MAAKA,EAAEC,OAAMA,EAAEC,MAAKA,IAIlCnB,EAAAG,UAAA4B,QAAP,WAEI7B,KAAKD,SAAW,MASbD,EAAAyB,gBAAP,SACIT,EAAmDwC,GAGnD,IAAK,IAAIC,EAAI,EAAGA,EAAIzC,EAAO0C,OAAQD,GAAK,EACxC,CACI,IAAME,EAAQH,EAAIC,EAAI,GAAKzC,EAAOyC,EAAI,GAExB,IAAVE,GAEAH,EAAIC,GAAKV,KAAKC,MAAMD,KAAKa,IAAgB,IAAZ5C,EAAOyC,GAAaE,EAAO,MACxDH,EAAIC,EAAI,GAAKV,KAAKC,MAAMD,KAAKa,IAAoB,IAAhB5C,EAAOyC,EAAI,GAAaE,EAAO,MAChEH,EAAIC,EAAI,GAAKV,KAAKC,MAAMD,KAAKa,IAAoB,IAAhB5C,EAAOyC,EAAI,GAAaE,EAAO,QAIhEH,EAAIC,GAAKzC,EAAOyC,GAChBD,EAAIC,EAAI,GAAKzC,EAAOyC,EAAI,GACxBD,EAAIC,EAAI,GAAKzC,EAAOyC,EAAI,MA7N7BzD,EAAA6D,UAA+B,CAClCC,KAAM,UACNC,KAAMC,EAAaA,cAACC,gBA+N3BjE"}