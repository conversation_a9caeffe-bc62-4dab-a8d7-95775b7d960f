<template>
  <div class="voice-oracle-bar">
    <!-- 神谕之音长条形容器 -->
    <div class="oracle-bar-container">
      <!-- 左侧：状态和标题 -->
      <div class="oracle-left-section">
        <div class="oracle-brand">
          <div class="oracle-icon">
            <i class="fas fa-microphone-alt"></i>
            <div class="sound-pulse" v-if="isVoiceActive"></div>
          </div>
          <div class="oracle-info">
            <h4 class="oracle-title">神谕之音</h4>
            <div class="connection-status" :class="connectionStatus">
              <span class="status-dot"></span>
              <span class="status-text">{{ getStatusText() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：核心功能区 -->
      <div class="oracle-center-section">
        <!-- 🎙️ 转录结果显示区域（主界面长条） - 常驻显示 -->
        <div class="transcript-display-bar">
          <div class="transcript-bar-content">
            <div class="transcript-bar-header">
              <i class="fas fa-microphone"></i>
              <span>语音转录结果</span>
              <!-- 次数计算显示已隐藏，简化界面 -->
              <div class="dialogue-status" v-if="!hasTianrenShenying">
                <span class="no-tianren">
                  <i class="fas fa-lock"></i> 需要天人感应
                </span>
              </div>
            </div>
            <input 
              v-model="currentTranscript"
              class="transcript-bar-input"
              :placeholder="isListening ? '正在录制语音...' : '输入文字或点击语音按钮录制'"
              @input="onTranscriptEdit"
              :disabled="isListening"
            />
            <div class="transcript-bar-actions">
              <!-- 发送按钮常驻显示 -->
              <button 
                class="transcript-bar-btn send-btn" 
                @click="handleSendMessage" 
                title="发送消息"
                :disabled="!canSendMessage"
              >
                <i class="fas fa-paper-plane"></i>
                <span>发送</span>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 配置快捷按钮 - 隐藏 -->
        <div class="quick-config" v-if="false" style="display: none;">
          <div class="config-item model-select">
            <label>智慧源泉</label>
            <select v-model="oracleConfig.selectedModel" @change="onModelChange" class="mini-select">
              <option value="" disabled>选择模型...</option>
              <option v-for="model in availableModels" :key="model" :value="model">
                {{ model.split(':')[1] || model }}
              </option>
            </select>
          </div>
          
          <div class="config-item voice-select">
            <label>神谕音色</label>
            <select v-model="oracleConfig.selectedVoice" @change="onVoiceChange" class="mini-select">
              <option value="" disabled>选择音色...</option>
              <option v-for="voice in availableVoices" :key="voice.id" :value="voice.id">
                {{ voice.name }}
              </option>
            </select>
          </div>

          <div class="config-item character-select">
            <label>神谕角色</label>
            <select v-model="oracleConfig.selectedCharacter" @change="onCharacterChange" class="mini-select">
              <option :value="null" disabled>选择角色...</option>
              <option v-for="character in availableCharacters" :key="character.name" :value="character">
                {{ character.name }}
              </option>
            </select>
          </div>

          <!-- 预设选择框 -->
          <div class="config-item preset-select">
            <label>参数预设</label>
            <select v-model="selectedPreset" @change="onPresetChange" class="mini-select">
              <option value="">选择预设...</option>
              <option v-for="preset in availablePresets" :key="preset.name" :value="preset.name">
                {{ preset.name }}
              </option>
            </select>
          </div>

          <!-- 配置按钮移到选择框同一行 -->
          <div class="config-item config-button">
            <label>&nbsp;</label>
            <button 
              class="control-btn config-btn compact"
              :class="{ active: showConfigPanel }"
              @click="toggleConfigPanel"
            >
              <i class="fas fa-cog"></i>
              <span>配置</span>
            </button>
          </div>
        </div>

        <!-- 对话状态显示 - 已隐藏文字显示，纯语音模式 -->
        <div class="conversation-status" v-if="isVoiceActive" style="display: none;">
          <!-- 文字显示已隐藏，只保留语音功能 -->
        </div>
      </div>

      <!-- 右侧：控制按钮 -->
      <div class="oracle-right-section">
        <div class="oracle-controls">
          <!-- 启动神谕按钮已隐藏，使用语音作为主要交互方式 -->
          
          <button 
            class="control-btn voice-btn"
            :class="{ active: isListening, disabled: !canUseVoice }"
            @click="startListening"
            :disabled="!canUseVoice"
            title="点击开始语音录制"
          >
            <i class="fas fa-microphone"></i>
            <span>{{ isListening ? '录制中...' : '语音录制' }}</span>
          </button>
          

          
          <!-- 展开对话按钮已隐藏 - 纯语音模式不需要文字显示 -->
          <button 
            class="control-btn expand-btn"
            @click="toggleExpandedView"
            v-if="false"
            style="display: none;"
          >
            <i class="fas fa-expand"></i>
            <span>展开对话</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 展开的配置面板 - 隐藏 -->
    <div v-if="false" class="expanded-config-panel" style="display: none;">
      <div class="config-grid">
        <!-- 预设管理 -->
        <div class="preset-section">
          <h5><i class="fas fa-bookmark"></i> 参数预设</h5>
          <div class="preset-controls">
            <div class="preset-selector">
              <select v-model="selectedPreset" @change="onPresetChange" class="mini-select">
                <option value="">选择预设...</option>
                <option v-for="preset in availablePresets" :key="preset.name" :value="preset.name">
                  {{ preset.name }}
                </option>
              </select>
              <button @click="showPresetModal = true" class="action-btn preset-add-btn">
                <i class="fas fa-plus"></i>
              </button>
              <button v-if="selectedPreset" @click="clearPresetSelection" class="action-btn preset-clear-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div v-if="selectedPreset" class="preset-status">
              <i class="fas fa-bookmark"></i>
              使用预设：{{ selectedPreset }}
            </div>
          </div>
        </div>

        <!-- LLM参数调节 -->
        <div class="param-section">
          <h5><i class="fas fa-sliders-h"></i> 智慧参数</h5>
          <div v-if="selectedPreset" class="params-disabled-notice">
            <i class="fas fa-info-circle"></i>
            当前使用预设参数，手动参数设置已禁用
          </div>
          <div class="param-sliders" :class="{ disabled: !!selectedPreset }">
            <div class="slider-row">
              <div class="slider-item">
                <label>创造性 (Temperature)</label>
                <input type="range" v-model.number="oracleConfig.temperature" min="0.1" max="2.0" step="0.1" class="mini-slider" :disabled="!!selectedPreset">
                <span class="slider-value">{{ oracleConfig.temperature }}</span>
              </div>
              <div class="slider-item">
                <label>专注度 (Top P)</label>
                <input type="range" v-model.number="oracleConfig.topP" min="0.1" max="1.0" step="0.05" class="mini-slider" :disabled="!!selectedPreset">
                <span class="slider-value">{{ oracleConfig.topP }}</span>
              </div>
            </div>
            <div class="slider-row">
              <div class="slider-item">
                <label>候选词数 (Top K)</label>
                <input type="range" v-model.number="oracleConfig.topK" min="1" max="100" step="1" class="mini-slider" :disabled="!!selectedPreset">
                <span class="slider-value">{{ oracleConfig.topK }}</span>
              </div>
              <div class="slider-item">
                <label>最大字数 (Max Tokens)</label>
                <input type="range" v-model.number="oracleConfig.maxTokens" min="500" max="4000" step="100" class="mini-slider" :disabled="!!selectedPreset">
                <span class="slider-value">{{ oracleConfig.maxTokens }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 下半部分：提示词和操作 -->
        <div class="bottom-section">
          <!-- 自定义提示词 -->
          <div class="prompt-section">
            <h5><i class="fas fa-scroll"></i> 自定义指引</h5>
            <textarea 
              v-model="oracleConfig.customSystemPrompt"
              @input="onPromptChange"
              placeholder="可自定义神谕大师的性格和风格..."
              class="mini-textarea"
              rows="3"
            ></textarea>
          </div>

          <!-- 快速操作 -->
          <div class="actions-section">
            <h5><i class="fas fa-tools"></i> 快速操作</h5>
            <div class="action-buttons">
              <button @click="refreshModelList" :disabled="isRefreshingModels" class="action-btn">
                <i :class="isRefreshingModels ? 'fas fa-spinner fa-spin' : 'fas fa-sync-alt'"></i>
                刷新模型
              </button>
              <button @click="refreshVoiceList" :disabled="isRefreshingVoices" class="action-btn">
                <i :class="isRefreshingVoices ? 'fas fa-spinner fa-spin' : 'fas fa-music'"></i>
                刷新音色
              </button>
              <button @click="refreshCharacterList" :disabled="isRefreshingCharacters" class="action-btn">
                <i :class="isRefreshingCharacters ? 'fas fa-spinner fa-spin' : 'fas fa-user-friends'"></i>
                刷新角色
              </button>
                        <button @click="previewPrompt" class="action-btn">
            <i class="fas fa-eye"></i>
            预览提示词
          </button>
          <button @click="forceResetOracle" class="action-btn reset-btn">
            <i class="fas fa-power-off"></i>
            强制重置
          </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 展开的对话视图 - 已隐藏，纯语音模式 -->
    <div v-if="false" class="expanded-conversation-modal" style="display: none;" @click.self="closeExpandedView">
      <div class="expanded-conversation-content">
        <div class="conversation-header">
          <h3><i class="fas fa-comments"></i> 神谕对话</h3>
          <button @click="closeExpandedView" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="conversation-area" ref="expandedConversationArea">
          <!-- 空状态 -->
          <div v-if="messages.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-comments"></i>
            </div>
            <h4>开始您的神谕之旅</h4>
            <p>点击"开始输入语音"或在下方输入您的问题</p>
          </div>

          <!-- 消息列表 -->
          <div v-else class="messages-list">
            <div 
              v-for="(message, index) in messages" 
              :key="index"
              class="message-item"
              :class="message.role"
            >
              <!-- 用户消息 -->
              <div v-if="message.role === 'user'" class="message-bubble user-message">
                <div class="message-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender-name">您</span>
                    <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                  </div>
                  <div class="message-text">{{ message.content }}</div>
                </div>
              </div>

              <!-- AI回复 -->
              <div v-else class="message-bubble ai-message">
                <div class="message-avatar">
                  <i class="fas fa-user-astronaut"></i>
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender-name">神谕大师</span>
                    <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                    <button 
                      v-if="message.audioUrl" 
                      class="play-audio-btn"
                      @click="playAudio(message.audioUrl)"
                      :disabled="isPlayingAudio"
                    >
                      <i :class="isPlayingAudio ? 'fas fa-spinner fa-spin' : 'fas fa-volume-up'"></i>
                    </button>
                  </div>
                  <div class="message-text" :class="{ streaming: message.isStreaming }">
                    {{ message.content }}
                    <span v-if="message.isStreaming" class="typing-cursor">|</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI思考指示器 -->
            <div v-if="isThinking" class="thinking-indicator">
              <div class="message-bubble ai-message thinking">
                <div class="message-avatar">
                  <i class="fas fa-user-astronaut"></i>
                  <div class="thinking-pulse"></div>
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender-name">神谕大师</span>
                    <span class="thinking-text">正在解读天机...</span>
                  </div>
                  <div class="thinking-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="expanded-input-area">
          <!-- 语音输入状态 -->
          <div v-if="isListening || isWaitingConfirm" class="voice-input-status">
            <div class="voice-indicator">
              <div v-if="!isWaitingConfirm" class="voice-waves">
                <div class="wave wave-1"></div>
                <div class="wave wave-2"></div>
                <div class="wave wave-3"></div>
              </div>
              <div v-else class="voice-confirm-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="voice-text">
                <p class="listening-text">
                  {{ isWaitingConfirm ? '录音完成，请确认发送' : '正在聆听您的话语...' }}
                </p>
                <p v-if="currentTranscript" class="transcript-preview">{{ currentTranscript }}</p>
              </div>
            </div>
            <button 
              v-if="isWaitingConfirm" 
              class="confirm-btn" 
              @click="confirmVoiceInput"
            >
              <i class="fas fa-check"></i>
              <span>确认发送</span>
            </button>
            <button 
              v-else 
              class="stop-listening-btn" 
              @click="stopListening"
            >
              <i class="fas fa-stop"></i>
              <span>停止录音</span>
            </button>
          </div>

          <!-- 手动输入 -->
          <div v-else class="manual-input">
            <div class="input-group">
              <input 
                v-model="manualInput"
                @keyup.enter="sendManualMessage"
                :disabled="isThinking || !isVoiceActive"
                placeholder="输入您的问题，或点击语音输入..."
                class="text-input"
              />
              <button 
                class="input-btn voice-btn"
                @click="handleVoiceAction"
                :disabled="!isVoiceActive || isThinking || isPlayingAudio"
              >
                <i :class="isListening ? 'fas fa-stop' : 'fas fa-microphone'"></i>
              </button>
              <button 
                class="input-btn send-btn"
                @click="sendManualMessage"
                :disabled="!manualInput.trim() || isThinking || !isVoiceActive"
              >
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的音频元素，用于播放TTS生成的音频 -->
    <audio ref="audioPlayerRef" style="display: none;" preload="auto"></audio>

    <!-- 预设管理模态框 -->
    <div v-if="showPresetModal" class="preset-modal-overlay" @click="showPresetModal = false">
      <div class="preset-modal" @click.stop>
        <div class="modal-header">
          <h3>新建参数预设</h3>
          <button class="close-btn" @click="showPresetModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-item">
            <label>预设名称</label>
            <input 
              v-model="presetForm.name" 
              type="text" 
              placeholder="输入预设名称"
              class="form-input"
            />
          </div>
          
          <div class="form-item">
            <label>描述</label>
            <input 
              v-model="presetForm.description" 
              type="text" 
              placeholder="输入预设描述"
              class="form-input"
            />
          </div>
          
          <!-- 参数设置 -->
          <div class="params-grid-modal">
            <div class="param-item-modal">
              <label>创造性: {{ presetForm.temperature }}</label>
              <input 
                v-model.number="presetForm.temperature" 
                type="range" 
                min="0.1" 
                max="2.0" 
                step="0.1"
                class="form-slider"
              />
            </div>
            
            <div class="param-item-modal">
              <label>专注度: {{ presetForm.topP }}</label>
              <input 
                v-model.number="presetForm.topP" 
                type="range" 
                min="0.1" 
                max="1.0" 
                step="0.05"
                class="form-slider"
              />
            </div>
            
            <div class="param-item-modal">
              <label>候选词数: {{ presetForm.topK }}</label>
              <input 
                v-model.number="presetForm.topK" 
                type="range" 
                min="1" 
                max="100" 
                step="1"
                class="form-slider"
              />
            </div>
            
            <div class="param-item-modal">
              <label>最大字数: {{ presetForm.maxTokens }}</label>
              <input 
                v-model.number="presetForm.maxTokens" 
                type="range" 
                min="500" 
                max="4000" 
                step="100"
                class="form-slider"
              />
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button class="cancel-btn" @click="showPresetModal = false" :disabled="isSavingPreset">取消</button>
          <button class="save-btn" @click="savePreset" :disabled="isSavingPreset || !presetForm.name.trim()">
            <i class="fas fa-spinner fa-spin" v-if="isSavingPreset"></i>
            <i class="fas fa-save" v-else></i>
            {{ isSavingPreset ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch, readonly } from 'vue';
import { storeToRefs } from 'pinia';
import * as API from '@/services/api';
import { voiceProfileAPI } from '@/services/audioManager';
import { useRealtimeStore } from '@/stores/modules/realtimeStore';
import { getFormattedTimeForLLM } from '@/utils/timeUtils';
// 🔧 修复：只使用useWebSocket和globalWebSocketManager，移除废弃的unifiedWebSocketManager
import useWebSocket from '@/composables/useWebSocket';
import { 
  createTimer, 
  createInterval, 
  clearTimer, 
  clearAllTimers, 
  clearComponentTimers,
  enableTimerDebug 
} from '@/utils/timerManager';

// ===================== Props 和 Emits =====================
// 🔧 定义卦象相关类型（与神机秒卦组件保持一致）
interface HexagramLine {
  type: 'yin' | 'yang' | 'changing-yin' | 'changing-yang';
  text: string; // 爻辞原文
  meaning?: string; // 爻辞含义
}

interface Trigram {
  name: string;
  meaning?: string; // 与神机秒卦传递的实际数据保持一致
}

interface Props {
  selectedTopic?: string;
  currentHexagram?: {
    id: string;
    name: string;
    meaning: string;
    interpretation: string;
    advice: string;
    number?: number; // 卦序号
    image?: string; // 象辞
    upperTrigram?: Trigram; // 上卦
    lowerTrigram?: Trigram; // 下卦
    lines?: HexagramLine[]; // 爻辞
  };
  userInfo?: {
    name: string;
    gender: string;
    birthYear: string | number;
    birthMonth: string | number;
    birthDay: string | number;
    birthHour: string;
  };
  tianrenShenying?: string; // 天人感应信息
}

const props = withDefaults(defineProps<Props>(), {
  selectedTopic: '',
  currentHexagram: undefined,
  userInfo: () => ({
    name: '',
    gender: '',
    birthYear: '',
    birthMonth: '',
    birthDay: '',
    birthHour: ''
  }),
  tianrenShenying: ''
});

const emit = defineEmits<{
  'oracle-started': [];
  'oracle-stopped': [];
  'message-sent': [message: string];
  'response-received': [response: string];
  'config-changed': [config: any];
  'hexagram-reading': [hexagram: any];
}>();

// ===================== Store 和状态 =====================
const realtimeStore = useRealtimeStore();

// 🔧 新增：使用WebSocket组合函数
// 🔧 关键修复：使用专门的上下文，参考实时对话的成功配置
const websocket = useWebSocket({
  pageName: '周易测算',
  context: 'oracle-dialogue', // 🔑 指定专门的神谕之音上下文
  autoConnect: false, // 🔧 关闭自动连接，手动控制连接时机
  events: ['transcription', 'llm_response', 'tts_audio', 'tts_error', 'status_update', 'error', 'connected', 'disconnected']
});

// 配置相关状态
const showConfigPanel = ref(false);
const showExpandedView = ref(false);

// 🔧 添加初始化状态，供YijingMainPanel检查
const isInitialized = ref(false);
const availableModels = ref<string[]>([]);
const availableVoices = ref<any[]>([]);
const availableCharacters = ref<any[]>([]);
const isRefreshingModels = ref(false);
const isRefreshingVoices = ref(false);
const isRefreshingCharacters = ref(false);

// 预设管理状态
const showPresetModal = ref(false);
const isSavingPreset = ref(false);
const selectedPreset = ref('');
const availablePresets = ref<any[]>([]);
const presetForm = ref({
  name: '',
  description: '',
  temperature: 0.8,
  topP: 0.9,
  topK: 40,
  maxTokens: 2000
});

// 神谕配置
const oracleConfig = reactive({
  selectedModel: '',
  selectedVoice: '',
  selectedCharacter: null as any,
  customSystemPrompt: '',
  currentTemplate: '经典神谕',
  temperature: 0.8,
  topP: 0.9,
  topK: 40,
  maxTokens: 2000
});

// 提示词模板
const promptTemplates = [
  {
    name: '经典神谕',
    prompt: `你是一位深谙周易玄学的神谕大师，拥有深厚的易学功底和人生智慧。以古雅而亲切的语调，结合周易哲学为求问者提供精准指导。`
  },
  {
    name: '现代解读',
    prompt: `你是一位融合传统智慧与现代心理学的人生导师，善于用周易哲学指导现代生活，用现代语言解释古老智慧。`
  },
  {
    name: '慈悲导师',
    prompt: `你是一位慈悲智慧的人生导师，如同古代的大德高僧，以慈悲宽容的态度，用温暖的话语抚慰心灵。`
  },
  {
    name: '智慧长者',
    prompt: `你是一位饱经沧桑的智慧长者，见证了人世间的起伏变迁，以长者的睿智和沉稳分享人生经验。`
  }
];

// ===================== 响应式状态 =====================
const isVoiceActive = ref(false);
const isListening = ref(false);
const isThinking = ref(false);
const isPlayingAudio = ref(false);
const isComponentUnmounting = ref(false); // 🔧 新增：组件卸载状态跟踪
const connectionStatus = ref<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
const currentTranscript = ref('');
const manualInput = ref('');
const isWaitingConfirm = ref(false); // 新增：等待确认状态

// 🔧 新增：对话轮次控制
const maxDialogueRounds = ref(2); // 最大对话轮次（2次语音对话 + 1次初始解卦 = 3次）
const currentDialogueRound = ref(0); // 当前语音对话轮次
const totalInteractions = ref(0); // 总交互次数（包括初始解卦）
const isAutoDialogueActive = ref(false); // 是否在自动对话模式中
const hasHexagramData = ref(false); // 是否有卦象数据
const isInHexagramReadingPhase = ref(false); // 🎯 新增：是否在卦象解读阶段

// 🔧 天人感应状态检查
const hasTianrenShenying = computed(() => {
  return !!(props.tianrenShenying && props.tianrenShenying.trim());
});

// 🔧 语音功能是否可用（简化版本，去掉复杂的次数计算）
const canUseVoice = computed(() => {
  return hasTianrenShenying.value && !isThinking.value && !isPlayingAudio.value;
});

// 🔧 发送消息功能是否可用（只有在卦象解读后才可以点击）
const canSendMessage = computed(() => {
  return props.currentHexagram && // 必须有卦象
         currentTranscript.value.trim() && // 必须有输入内容
         !isThinking.value && // 不在思考中
         !isPlayingAudio.value; // 不在播放音频
});

interface OracleMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  audioUrl?: string;
  isStreaming?: boolean;
}

const messages = ref<OracleMessage[]>([]);

// DOM 引用
const expandedConversationArea = ref<HTMLElement>();
const audioPlayerRef = ref<HTMLAudioElement>();

// 🕒 定时器管理 - 解决内存泄漏问题
const componentName = '神谕之音';

// 启用定时器调试模式（开发时使用）
if (process.env.NODE_ENV === 'development') {
  // enableTimerDebug(); // 已禁用定时器调试，避免控制台刷屏
}

// 占卜主题映射
const DIVINATION_TOPICS = {
  'love': '求姻缘 - 红线牵引，良缘天定',
  'career': '问前程 - 仕途运势，功名利禄',
  'health': '察健康 - 身心调养，延年益寿',
  'wealth': '探财运 - 财源广进，富贵盈门',
  'study': '卜学业 - 金榜题名，学而时习',
  'family': '观家宅 - 阖家安康，六亲和睦',
  'travel': '测出行 - 千里之行，平安归来',
  'decision': '断疑惑 - 迷津指点，拨云见日',
  'friendship': '论人缘 - 贵人相助，友谊长存',
  'investment': '问投资 - 投资理财与商机把握',
  'competition': '问竞争 - 竞赛考试与竞争优势',
  'general': '问综运 - 整体运势与人生指导'
};

// ===================== 计算属性 =====================
const hasContextInfo = computed(() => {
  return props.selectedTopic || props.currentHexagram || props.userInfo.name;
});

const canStartOracle = computed(() => {
  return hasContextInfo.value && oracleConfig.selectedModel && oracleConfig.selectedVoice && oracleConfig.selectedCharacter;
});

const canAutoReadHexagram = computed(() => {
  return oracleConfig.selectedModel && oracleConfig.selectedVoice && oracleConfig.selectedCharacter;
});

const lastMessage = computed(() => {
  return messages.value.length > 0 ? messages.value[messages.value.length - 1] : null;
});

const currentCharacterName = computed(() => {
  return oracleConfig.selectedCharacter?.name || '神谕大师';
});

const currentVoiceName = computed(() => {
  const voice = availableVoices.value.find(v => v.id === oracleConfig.selectedVoice);
  return voice?.name || '未选择';
});

// ===================== 配置相关方法 =====================
const toggleConfigPanel = () => {
  showConfigPanel.value = !showConfigPanel.value;
};

const toggleExpandedView = () => {
  showExpandedView.value = !showExpandedView.value;
};

const closeExpandedView = () => {
  showExpandedView.value = false;
};

const refreshModelList = async () => {
  isRefreshingModels.value = true;
  try {
    console.log('🔄 刷新模型列表...');
    const result = await API.getAvailableModels();
    
    if (result.success && result.data) {
      availableModels.value = result.data.models || [];
      console.log('✅ 模型列表刷新成功, 共', availableModels.value.length, '个模型');
      
      // 如果当前选择的模型不在列表中，清空选择
      if (oracleConfig.selectedModel && !availableModels.value.includes(oracleConfig.selectedModel)) {
        oracleConfig.selectedModel = '';
      }
      
      // 如果没有选择模型且有可用模型，自动选择第一个
      if (!oracleConfig.selectedModel && availableModels.value.length > 0) {
        oracleConfig.selectedModel = availableModels.value[0];
      }
    } else {
      console.warn('⚠️ 模型列表刷新失败:', result.message);
    }
  } catch (error) {
    console.error('❌ 模型列表刷新出错:', error);
  } finally {
    isRefreshingModels.value = false;
  }
};

const refreshVoiceList = async () => {
  isRefreshingVoices.value = true;
  try {
    console.log('🔄 刷新音色列表...');
    const result = await voiceProfileAPI.getVoiceProfiles();
    
    if (result.success && result.data) {
      availableVoices.value = result.data || [];
      console.log('✅ 音色列表刷新成功, 共', availableVoices.value.length, '个音色');
      
      // 如果当前选择的音色不在列表中，清空选择
      if (oracleConfig.selectedVoice && !availableVoices.value.find(v => v.id === oracleConfig.selectedVoice)) {
        oracleConfig.selectedVoice = '';
      }
      
      // 如果没有选择音色且有可用音色，自动选择第一个
      if (!oracleConfig.selectedVoice && availableVoices.value.length > 0) {
        oracleConfig.selectedVoice = availableVoices.value[0].id;
      }
    } else {
      console.warn('⚠️ 音色列表刷新失败:', result.message);
    }
  } catch (error) {
    console.error('❌ 音色列表刷新出错:', error);
  } finally {
    isRefreshingVoices.value = false;
  }
};

const refreshCharacterList = async () => {
  isRefreshingCharacters.value = true;
  try {
    console.log('🔄 刷新角色列表...');
    const result = await API.getCharacterList();
    
    if (result.success && result.data) {
      availableCharacters.value = result.data || [];
      console.log('✅ 角色列表刷新成功, 共', availableCharacters.value.length, '个角色');
      
      // 如果当前选择的角色不在列表中，清空选择
      if (oracleConfig.selectedCharacter && !availableCharacters.value.find(c => c.name === oracleConfig.selectedCharacter.name)) {
        oracleConfig.selectedCharacter = null;
      }
      
      // 如果没有选择角色且有可用角色，自动选择第一个
      if (!oracleConfig.selectedCharacter && availableCharacters.value.length > 0) {
        oracleConfig.selectedCharacter = availableCharacters.value[0];
      }
    } else {
      console.warn('⚠️ 角色列表刷新失败:', result.message);
    }
  } catch (error) {
    console.error('❌ 角色列表刷新出错:', error);
  } finally {
    isRefreshingCharacters.value = false;
  }
};

const onModelChange = () => {
  console.log('🎯 模型选择变更:', oracleConfig.selectedModel);
  emit('config-changed', { ...oracleConfig });
};

const onVoiceChange = () => {
  console.log('🎵 音色选择变更:', oracleConfig.selectedVoice);
  emit('config-changed', { ...oracleConfig });
};

const onTemplateChange = () => {
  console.log('📜 提示词模板变更:', oracleConfig.currentTemplate);
  emit('config-changed', { ...oracleConfig });
};

const onCharacterChange = () => {
  console.log('🎭 角色选择变更:', oracleConfig.selectedCharacter?.name);
  emit('config-changed', { ...oracleConfig });
};

const onPromptChange = () => {
  console.log('📜 提示词变更:', oracleConfig.customSystemPrompt);
  emit('config-changed', { ...oracleConfig });
};

// 截断消息用于显示
const truncateMessage = (content: string, maxLength: number = 60) => {
  if (!content) return '';
  if (content.length <= maxLength) return content;
  return content.substring(0, maxLength) + '...';
};

// 预览提示词
const previewPrompt = () => {
  const currentTemplate = promptTemplates.find(t => t.name === oracleConfig.currentTemplate);
  const previewText = oracleConfig.customSystemPrompt || currentTemplate?.prompt || '';
  
  // 创建一个简单的弹出预览
  const previewWindow = window.open('', '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
  if (previewWindow) {
    previewWindow.document.write(`
      <html>
        <head>
          <title>提示词预览 - ${oracleConfig.currentTemplate}</title>
          <style>
            body { 
              font-family: '微软雅黑', Arial, sans-serif; 
              padding: 20px; 
              line-height: 1.6; 
              background: #f5f5f5;
            }
            .container {
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h2 { 
              color: #D4AF37; 
              border-bottom: 2px solid #D4AF37;
              padding-bottom: 10px;
            }
            .prompt-content {
              background: #f8f9fa;
              padding: 15px;
              border-left: 4px solid #D4AF37;
              white-space: pre-wrap;
              font-size: 14px;
            }
            .close-btn {
              margin-top: 20px;
              padding: 8px 16px;
              background: #D4AF37;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>📜 提示词预览 - ${oracleConfig.currentTemplate}</h2>
            <div class="prompt-content">${previewText}</div>
            <button class="close-btn" onclick="window.close()">关闭</button>
          </div>
        </body>
      </html>
    `);
    previewWindow.document.close();
  }
};

// 🔧 新增：强制重置神谕系统
const forceResetOracle = async () => {
  console.log('🚨 用户触发强制重置神谕系统...');
  
  try {
    // 🎨 使用更美观的自定义确认弹框，不显示任何确认直接执行
    // 因为这通常是重新抽卦时自动调用，不需要用户确认
    
    console.log('🔄 开始强制重置...');
    
    // 1. 立即停止所有状态
    isVoiceActive.value = false;
    isListening.value = false;
    isThinking.value = false;
    isPlayingAudio.value = false;
    isInHexagramReadingPhase.value = false;
    connectionStatus.value = 'disconnected';
    
    // 2. 清理音频播放器
    if (audioPlayerRef.value) {
      audioPlayerRef.value.pause();
      audioPlayerRef.value.currentTime = 0;
      audioPlayerRef.value.src = '';
    }
    
    // 3. 清理音频状态
    audioChunks.value = [];
    currentChunkIndex.value = 0;
    isWaitingForAudioChunks.value = false;
    
    // 🔧 清理音频块超时定时器
    if (audioChunkTimeout.value) {
      clearTimer(audioChunkTimeout.value);
      audioChunkTimeout.value = null;
    }
    
    // 4. 清理语音识别状态
    try {
      if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
        // 停止可能正在运行的语音识别
        currentTranscript.value = '';
        isWaitingConfirm.value = false;
      }
    } catch (speechError) {
      console.warn('⚠️ 清理语音识别状态失败:', speechError);
    }
    
    // 5. 🔧 修复：轻量级WebSocket重置，保持连接但重置状态
    lightWeightWebSocketReset();
    
    // 6. 强制停止后端服务
    try {
      await API.stopRealtimeDialogue();
      console.log('✅ 强制重置：实时对话服务已停止');
    } catch (error) {
      console.warn('⚠️ 强制重置：停止实时对话服务失败:', error);
    }
    
    // 7. 强制重启VAD和音频流
    try {
      await API.forceRestartVADAudioStream();
      console.log('✅ 强制重置：VAD和音频流已重启');
    } catch (error) {
      console.warn('⚠️ 强制重置：VAD重启失败:', error);
    }
    
    // 8. 清理实时对话历史
    try {
      await API.clearRealtimeHistory();
      console.log('✅ 强制重置：实时对话历史已清理');
    } catch (error) {
      console.warn('⚠️ 强制重置：清理历史失败:', error);
    }
    
    // 9. 重置对话相关状态
    currentDialogueRound.value = 0;
    totalInteractions.value = 0;
    isAutoDialogueActive.value = false;
    hasHexagramData.value = false;
    maxDialogueRounds.value = 2;
    currentTranscript.value = '';
    manualInput.value = '';
    isWaitingConfirm.value = false;
    isListening.value = false;
    
    // 10. 清空消息列表
    messages.value = [];
    
    console.log('🏁 强制重置完成，神谕系统已恢复初始状态');
    
    // 11. 简洁的成功提示（不显示弹框，仅在控制台记录）
    
  } catch (error) {
    console.error('❌ 强制重置失败:', error);
    // 🎨 简洁的错误提示，不显示弹框
    console.warn('⚠️ 强制重置过程中出现错误，建议刷新页面');
  }
};

// ===================== 预设管理方法 =====================

// 预设选择变化
const onPresetChange = () => {
  if (!selectedPreset.value) return;
  
  const preset = availablePresets.value.find(p => p.name === selectedPreset.value);
  if (preset) {
    applyPreset(preset);
  }
};

// 清除预设选择
const clearPresetSelection = () => {
  selectedPreset.value = '';
  console.log('🧹 已清除预设选择');
};

// 应用预设
const applyPreset = (preset: any) => {
  oracleConfig.temperature = preset.temperature;
  oracleConfig.topP = preset.topP;
  oracleConfig.topK = preset.topK;
  oracleConfig.maxTokens = preset.maxTokens;
  
  console.log('📋 应用预设:', preset.name);
  emit('config-changed', { ...oracleConfig });
};

// 保存预设
const savePreset = async () => {
  if (!presetForm.value.name.trim()) {
    console.warn('⚠️ 预设名称不能为空');
    return;
  }
  
  isSavingPreset.value = true;
  
  try {
    const preset = {
      name: presetForm.value.name,
      description: presetForm.value.description,
      temperature: presetForm.value.temperature,
      topP: presetForm.value.topP,
      topK: presetForm.value.topK,
      maxTokens: presetForm.value.maxTokens
    };
    
    console.log('🌐 API调用: POST /api/llm-presets/save');
    
    let apiSaved = false;
    
    // 尝试API保存预设
    try {
      const response = await fetch('http://localhost:7860/api/llm-presets/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preset)
      });
      
      console.log('📡 Response status:', response.status);
      
      if (response.ok) {
        const result = await response.json();
        console.log('📦 API返回结果:', result);
        
        if (result.success) {
          console.log('✅ 预设API保存成功:', result.message);
          apiSaved = true;
        }
      }
    } catch (apiError) {
      console.warn('⚠️ API保存失败，使用本地存储:', apiError);
    }
    
    // 保存到本地存储（使用AI配置的键）
    try {
      const localPresets = JSON.parse(localStorage.getItem('cosyvoice-ai-presets') || '[]');
      
      // 检查是否已存在同名预设
      const existingIndex = localPresets.findIndex((p: any) => p.name === preset.name);
      if (existingIndex >= 0) {
        localPresets[existingIndex] = preset;
        console.log('🔄 更新现有预设:', preset.name);
      } else {
        localPresets.push(preset);
        console.log('➕ 添加新预设:', preset.name);
      }
      
      localStorage.setItem('cosyvoice-ai-presets', JSON.stringify(localPresets));
      
      // 更新当前组件的预设列表
      const currentPresets = [...availablePresets.value];
      const currentIndex = currentPresets.findIndex(p => p.name === preset.name);
      if (currentIndex >= 0) {
        currentPresets[currentIndex] = preset;
      } else {
        currentPresets.push(preset);
      }
      
      availablePresets.value = currentPresets;
      
      // 关闭模态框并重置表单
      resetPresetForm();
      showPresetModal.value = false;
      
      const saveMethod = apiSaved ? 'API和本地存储' : '本地存储';
      console.log(`🎯 预设已保存到${saveMethod}, 当前预设数量:`, currentPresets.length);
      
    } catch (localError) {
      console.error('❌ 本地存储保存失败:', localError);
      throw new Error('预设保存失败');
    }
    
  } catch (error) {
    console.error('❌ 保存预设时发生错误:', error);
  } finally {
    isSavingPreset.value = false;
  }
};

// 重置预设表单
const resetPresetForm = () => {
  presetForm.value = {
    name: '',
    description: '',
    temperature: oracleConfig.temperature,
    topP: oracleConfig.topP,
    topK: oracleConfig.topK,
    maxTokens: oracleConfig.maxTokens
  };
};

// 加载预设列表
const loadPresets = async () => {
  try {
    console.log('🔄 加载预设列表...');
    
    let presets = [];
    
    // 首先尝试从localStorage加载（使用AI配置的键）
    try {
      const localPresets = JSON.parse(localStorage.getItem('cosyvoice-ai-presets') || '[]');
      if (Array.isArray(localPresets) && localPresets.length > 0) {
        presets = localPresets;
        console.log('✅ 从AI配置本地存储加载预设成功, 共', presets.length, '个预设');
      }
    } catch (localError) {
      console.warn('⚠️ 从AI配置本地存储加载预设失败:', localError);
    }
    
    // 如果没有找到AI配置的预设，尝试从神谕专用存储加载
    if (presets.length === 0) {
      try {
        const oraclePresets = JSON.parse(localStorage.getItem('cosyvoice-oracle-presets') || '[]');
        if (Array.isArray(oraclePresets) && oraclePresets.length > 0) {
          presets = oraclePresets;
          console.log('✅ 从神谕专用存储加载预设成功, 共', presets.length, '个预设');
        }
      } catch (oracleError) {
        console.warn('⚠️ 从神谕专用存储加载预设失败:', oracleError);
      }
    }
    
    // 如果本地存储没有数据，尝试从API加载
    if (presets.length === 0) {
      try {
        const response = await fetch('/api/llm-presets/list', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        
        const result = await response.json();
        
        if (result.success && result.data && Array.isArray(result.data)) {
          console.log('✅ 从API加载预设列表成功, 共', result.data.length, '个预设');
          
          // 过滤掉"选择预设..."这样的默认选项
          const validPresets = result.data.filter((name: string) => name !== '选择预设...');
          
          // 如果有有效的预设名称，需要获取详细信息
          for (const presetName of validPresets) {
            try {
              const detailResponse = await fetch(`/api/llm-presets/get?name=${encodeURIComponent(presetName)}`);
              const detailResult = await detailResponse.json();
              
              if (detailResult.success && detailResult.data) {
                presets.push(detailResult.data);
              }
            } catch (error) {
              console.warn(`获取预设详情失败 ${presetName}:`, error);
            }
          }
          
          // 将API加载的预设保存到localStorage
          if (presets.length > 0) {
            localStorage.setItem('cosyvoice-ai-presets', JSON.stringify(presets));
            console.log('💾 API预设已同步到本地存储');
          }
        } else {
          console.warn('⚠️ API预设列表加载失败或为空:', result.message);
        }
      } catch (apiError) {
        console.warn('⚠️ 从API加载预设失败:', apiError);
      }
    }
    
    // 如果还是没有预设，使用默认预设
    if (presets.length === 0) {
      presets = [
        {
          name: 'gemma3',
          description: 'Gemma3模型优化参数',
          temperature: 0.7,
          topP: 0.9,
          topK: 40,
          maxTokens: 2000
        },
        {
          name: '创意模式',
          description: '高创意性，适合创作',
          temperature: 1.2,
          topP: 0.9,
          topK: 50,
          maxTokens: 3000
        },
        {
          name: '平衡模式',
          description: '平衡性能和质量',
          temperature: 0.8,
          topP: 0.9,
          topK: 40,
          maxTokens: 2000
        },
        {
          name: '精准模式',
          description: '低随机性，适合分析',
          temperature: 0.3,
          topP: 0.8,
          topK: 20,
          maxTokens: 1500
        }
      ];
      
      localStorage.setItem('cosyvoice-ai-presets', JSON.stringify(presets));
      console.log('📝 使用默认预设');
    }
    
    // 更新组件状态
    if (presets.length > 0) {
      availablePresets.value = presets;
      console.log('🎯 已更新预设列表, 共', presets.length, '个预设');
    }
    
  } catch (error) {
    console.error('❌ 加载预设时发生错误:', error);
  }
};

// ===================== 原有方法 =====================
const getStatusText = () => {
  switch (connectionStatus.value) {
    case 'connecting': return '连接中...';
    case 'connected': return '已连接';
    case 'error': return '连接失败';
    default: return '未连接';
  }
};

const getTopicInfo = (topicId: string) => {
  return DIVINATION_TOPICS[topicId as keyof typeof DIVINATION_TOPICS] || topicId;
};

const getBirthInfo = () => {
  const { birthYear, birthMonth, birthDay } = props.userInfo;
  if (birthYear && birthMonth && birthDay) {
    return `${birthYear}年${birthMonth}月${birthDay}日`;
  }
  return '生辰信息不完整';
};

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

// 开始/停止神谕对话
const toggleVoiceOracle = async () => {
  if (isVoiceActive.value) {
    await stopVoiceOracle();
  } else {
    await startVoiceOracle();
  }
};

// 🔧 修改：神谕功能启动逻辑 - 纯卦象解读模式，不启动语音检测
const startVoiceOracle = async () => {
  if (isVoiceActive.value) {
    console.log('🔄 卦象解读正在进行中，忽略重复触发');
    return;
  }
  
  try {
    // 🔧 关键修复：启动前先清理任何残留的实时对话服务
    console.log('🧹 启动前预清理，确保无残留服务...');
    
    try {
      // 尝试停止可能存在的残留实时对话服务
      await API.stopRealtimeDialogue();
      console.log('✅ 启动前清理：实时对话服务已停止');
    } catch (preCleanError) {
      console.log('ℹ️ 启动前清理：没有运行中的实时对话服务');
    }
    
    // 延迟一点时间确保清理完成
    await new Promise(resolve => {
      createTimer(() => resolve(undefined), 200, undefined, '启动前清理延迟', componentName);
    });
    console.log('✅ 启动前清理完成');
    // 🔧 重要：设置为卦象解读阶段，不启动WebSocket
    isVoiceActive.value = true;
    isInHexagramReadingPhase.value = true;
    currentDialogueRound.value = 0;
    isAutoDialogueActive.value = true;
    hasHexagramData.value = !!(props.currentHexagram || props.selectedTopic);
    maxDialogueRounds.value = 1;
    
    console.log('✅ 卦象解读模式已启动（不启动语音检测）');
    console.log('🎯 解读配置:', {
      maxRounds: maxDialogueRounds.value,
      hasHexagramData: hasHexagramData.value,
      autoMode: isAutoDialogueActive.value,
      isReadingPhase: isInHexagramReadingPhase.value
    });
    
    emit('oracle-started');
    
    // 🎯 立即发送卦象解读请求，使用专门的卦象解读API
    if (hasHexagramData.value && props.currentHexagram) {
      const autoMessage = buildAutoHexagramMessage(props.currentHexagram);
      console.log('🚀 立即发送卦象解读请求:', autoMessage);
      
      // 🔧 关键修改：使用简化的神谕之音流程，不启动VAD
      await sendSimpleOracleRequest(autoMessage);
    } else {
      console.log('📝 无卦象数据，添加欢迎消息');
      addWelcomeMessage();
    }
    
  } catch (error) {
    console.error('❌ 神谕启动失败:', error);
    isVoiceActive.value = false;
    isInHexagramReadingPhase.value = false;
    throw error;
  }
};

// 🔧 修复：彻底关闭实时对话服务的stopVoiceOracle函数
const stopVoiceOracle = async () => {
  try {
    console.log('🛑 正在停止神谕对话系统...');
    
    // 🔧 第一步：立即停止音频播放和VAD监听
    isListening.value = false;
    isThinking.value = false;
    isPlayingAudio.value = false;
    
    // 🔧 第二步：停止音频播放器
    if (audioPlayerRef.value) {
      try {
        audioPlayerRef.value.pause();
        audioPlayerRef.value.currentTime = 0;
        audioPlayerRef.value.src = '';
        
        // 清理所有事件监听器
        audioPlayerRef.value.removeEventListener('play', audioPlayHandler);
        audioPlayerRef.value.removeEventListener('ended', audioEndedHandler);
        audioPlayerRef.value.removeEventListener('error', audioErrorHandler);
        audioPlayerRef.value.removeEventListener('canplay', audioCanPlayHandler);
        
        console.log('✅ 音频播放器已停止并清理');
      } catch (audioError) {
        console.warn('⚠️ 音频播放器清理失败:', audioError);
      }
    }
    
    // 🔧 第三步：清理音频相关状态
    audioChunks.value = [];
    currentChunkIndex.value = 0;
    isWaitingForAudioChunks.value = false;
    audioPlayStartTime.value = 0;
    wasAudioPlayingBeforePause.value = false;
    
    // 🔧 清理音频块超时定时器
    if (audioChunkTimeout.value) {
      clearTimer(audioChunkTimeout.value);
      audioChunkTimeout.value = null;
    }
    
    console.log('✅ 音频状态已清理');
    
    // 🔧 第四步：清理WebSocket连接（无论状态如何都执行）
    console.log('🧹 清理WebSocket连接...');
    cleanupWebSocketListeners();
    
    // 🔧 第五步：强制停止实时对话服务（关键修复）
    console.log('🛑 强制停止实时对话服务...');
    
    // 🔧 关键修复：无论连接状态如何，都尝试停止实时对话服务
    try {
      const stopResponse = await API.stopRealtimeDialogue();
      console.log('📡 停止实时对话API调用结果:', stopResponse);
      
      if (stopResponse.success) {
        console.log('✅ 实时对话服务已正常停止');
      } else {
        console.warn('⚠️ 实时对话服务停止API返回失败，但继续清理:', stopResponse.message);
      }
    } catch (stopError: any) {
      console.warn('⚠️ 调用停止实时对话API失败，强制清理:', stopError.message || stopError);
    }
    
         // 🔧 第六步：额外的强制清理措施（针对残留会话）
     try {
       console.log('🔨 执行强制清理措施...');
       
       // 1. 尝试强制重启VAD和音频流（这会清理VAD状态）
       try {
         await API.forceRestartVADAudioStream();
         console.log('✅ VAD和音频流已强制重启（清理完成）');
       } catch (vadError) {
         console.warn('⚠️ 强制重启VAD失败:', vadError);
       }
       
       // 2. 尝试清理实时对话历史
       try {
         await API.clearRealtimeHistory();
         console.log('✅ 实时对话历史已清理');
       } catch (historyError) {
         console.warn('⚠️ 清理实时对话历史失败:', historyError);
       }
       
       // 3. 延迟确保清理完成
       await new Promise(resolve => {
         createTimer(() => resolve(undefined), 500, undefined, '强制清理确认延迟', componentName);
       });
       console.log('✅ 强制清理措施执行完成');
       
     } catch (forceCleanupError) {
       console.warn('⚠️ 强制清理措施执行失败:', forceCleanupError);
     }
    
    // 🔧 第七步：重置所有组件状态
    isVoiceActive.value = false;
    isListening.value = false;
    isThinking.value = false;
    isPlayingAudio.value = false;
    connectionStatus.value = 'disconnected';
    currentTranscript.value = '';
    isWaitingConfirm.value = false;
    
    // 🔧 重置对话控制状态
    currentDialogueRound.value = 0;
    isAutoDialogueActive.value = false;
    hasHexagramData.value = false;
    isInHexagramReadingPhase.value = false;
    maxDialogueRounds.value = 2;
    
    console.log('✅ 所有组件状态已重置');
    
    // 🔧 第八步：延迟确认清理完成
    createTimer(() => {
      console.log('🏁 神谕对话系统完全停止，所有状态已清理');
      console.log('📊 最终状态检查:', {
        isVoiceActive: isVoiceActive.value,
        connectionStatus: connectionStatus.value,
        isListening: isListening.value,
        isThinking: isThinking.value,
        isPlayingAudio: isPlayingAudio.value
      });
    }, 1000, undefined, '神谕系统清理确认', componentName);
    
    emit('oracle-stopped');
    
  } catch (error: any) {
    console.error('❌ 停止神谕对话时发生严重错误:', error);
    
    // 🔧 即使出错也要强制重置状态，防止UI异常
    isVoiceActive.value = false;
    isListening.value = false;
    isThinking.value = false;
    isPlayingAudio.value = false;
    connectionStatus.value = 'disconnected';
    isInHexagramReadingPhase.value = false;
    currentDialogueRound.value = 0;
    isAutoDialogueActive.value = false;
    isWaitingConfirm.value = false;
    
    console.log('🚨 强制重置状态完成，防止UI异常');
    emit('oracle-stopped');
  }
};

// 构建系统提示
const buildSystemPrompt = () => {
  let prompt = '';
  
  // 优先使用选中的角色提示词
  if (oracleConfig.selectedCharacter && oracleConfig.selectedCharacter.systemPrompt) {
    prompt = oracleConfig.selectedCharacter.systemPrompt;
  } else if (oracleConfig.customSystemPrompt) {
    prompt = oracleConfig.customSystemPrompt;
  } else {
    const currentTemplate = promptTemplates.find(t => t.name === oracleConfig.currentTemplate);
    prompt = currentTemplate?.prompt || promptTemplates[0].prompt;
  }
  
  prompt += '\n\n基于以下信息为求问者提供专业的解答：\n\n';

  // 添加求问者信息
  if (props.userInfo.name) {
    prompt += `## 求问者信息
- 姓名：${props.userInfo.name}
- 性别：${props.userInfo.gender === 'male' ? '男' : '女'}
- 生辰：${getBirthInfo()}
- 时辰：${props.userInfo.birthHour || '未知'}

`;
  }

  // 添加叩问主题
  if (props.selectedTopic) {
    prompt += `## 叩问主题
${getTopicInfo(props.selectedTopic)}

`;
  }

  // 添加卦象信息
  if (props.currentHexagram) {
    prompt += `## 神机妙卦
- 卦名：${props.currentHexagram.name}（第${props.currentHexagram.number || '未知'}卦）
- 卦辞：${props.currentHexagram.meaning}
- 象辞：${props.currentHexagram.image || '无象辞记录'}
- 上卦：${props.currentHexagram.upperTrigram?.name || '未知'}（${props.currentHexagram.upperTrigram?.meaning || ''}）
- 下卦：${props.currentHexagram.lowerTrigram?.name || '未知'}（${props.currentHexagram.lowerTrigram?.meaning || ''}）
- 综合解释：${props.currentHexagram.interpretation}

### 六爻详解
${props.currentHexagram.lines && props.currentHexagram.lines.length > 0 
  ? props.currentHexagram.lines.map((line: any, index: number) => {
      const positions = ['初', '二', '三', '四', '五', '上'];
      const lineType = line.type === 'yang' || line.type === 'changing-yang' ? '九' : '六';
      const position = `${positions[index]}${lineType}`;
      return `- ${position}：${line.text}${line.meaning ? `（${line.meaning}）` : ''}`;
    }).join('\n')
  : '- 爻辞信息暂缺'
}

`;
  }

  // 添加天人感应信息
  if (props.tianrenShenying && props.tianrenShenying.trim()) {
    prompt += `## 天人感应
${props.tianrenShenying}

`;
  }

  // 🔧 修改：将时间信息移到最后
  try {
    const timeInfo = getFormattedTimeForLLM();
    prompt += timeInfo;
  } catch (error) {
    console.warn('⚠️ 获取时间信息失败，使用基础时间信息:', error);
    // 如果时间工具失败，添加基础时间信息作为后备方案
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit',
      weekday: 'long'
    });
    prompt += `## 当前时间信息（不是用户生辰的时间）\n**现在的时间是，${timeStr}\n\n*给求问者指导时可以结合当下的时间*\n\n`;
  }

  return prompt;
};

// 添加欢迎消息
const addWelcomeMessage = () => {
  let welcomeText = '🔮 神谕之音已开启，本座已感知到您的占卜信息。';
  
  if (props.currentHexagram) {
    welcomeText += `\n\n根据您所得的${props.currentHexagram.name}卦，${props.currentHexagram.meaning}。`;
  }
  
  if (props.selectedTopic) {
    welcomeText += `\n\n您所叩问的是${getTopicInfo(props.selectedTopic).split(' - ')[0]}之事，本座愿为您答疑解惑。`;
  }
  
  welcomeText += '\n\n请述说您心中的疑问，本座将结合天机为您指点迷津。';

  const welcomeMessage: OracleMessage = {
    role: 'assistant',
    content: welcomeText,
    timestamp: new Date()
  };

  messages.value.push(welcomeMessage);
  scrollToBottom();
};

// 获取语音按钮文字
const getVoiceButtonText = () => {
  if (isWaitingConfirm.value) {
    return '点击确认';
  } else if (isListening.value) {
    return '录音中';
  } else {
    return '开始输入语音';
  }
};

// 获取语音按钮提示文字（简化版本）
const getVoiceButtonTooltip = () => {
  if (!hasTianrenShenying.value) {
    return '需要"天人感应"才能使用语音功能';
  } else if (isThinking.value) {
    return '神谕大师正在思考中...';
  } else if (isPlayingAudio.value) {
    return '正在播放神谕回应...';
  } else {
    return '点击开始语音录制';
  }
};

// 处理语音按钮点击（简化版本）
const handleVoiceAction = async () => {
  if (isListening.value) {
    // 如果正在录制，停止录制
    console.log('🛑 用户点击停止录制');
    if ((window as any).currentRecognition) {
      (window as any).currentRecognition.stop();
    }
  } else {
    // 开始录音
    await startListening();
  }
};

// 转录文本编辑事件
const onTranscriptEdit = () => {
  console.log('📝 用户编辑转录文本:', currentTranscript.value);
};

// 🔧 统一的发送消息处理方法
const handleSendMessage = async () => {
  if (!canSendMessage.value) {
    console.log('⚠️ 当前无法发送消息');
    return;
  }
  
  const messageText = currentTranscript.value.trim();
  if (!messageText) {
    console.log('⚠️ 消息内容为空');
    return;
  }
  
  console.log('📤 发送消息:', messageText);
  
  try {
    // 停止任何正在进行的语音识别
    if (isListening.value) {
      isListening.value = false;
      if ((window as any).currentRecognition) {
        try {
          (window as any).currentRecognition.stop();
        } catch (e) {
          // 忽略错误
        }
      }
    }
    
    // 重置等待确认状态
    isWaitingConfirm.value = false;
    
    // 添加用户消息到对话历史
    const userMessage: OracleMessage = {
      role: 'user',
      content: messageText,
      timestamp: new Date()
    };
    messages.value.push(userMessage);
    scrollToBottom();
    
    // 发送消息到TTS系统
    await sendRealtimeMessage(messageText);
    
    // 清空输入框
    currentTranscript.value = '';
    
  } catch (error: any) {
    console.error('❌ 发送消息时发生错误:', error);
    
    const errorMessage: OracleMessage = {
      role: 'assistant',
      content: '发送消息时发生错误，请重试。',
      timestamp: new Date()
    };
    messages.value.push(errorMessage);
    scrollToBottom();
  }
};

// 取消语音输入
const cancelVoiceInput = () => {
  console.log('❌ 用户取消语音输入');
  isWaitingConfirm.value = false;
  isListening.value = false;
  currentTranscript.value = '';
  
  // 清理语音识别实例
  if ((window as any).currentRecognition) {
    try {
      (window as any).currentRecognition.abort();
    } catch (e) {
      // 忽略清理错误
    }
    (window as any).currentRecognition = null;
  }
  
  const cancelMessage: OracleMessage = {
    role: 'assistant',
    content: '语音输入已取消，您可以重新开始录制。',
    timestamp: new Date()
  };
  messages.value.push(cancelMessage);
  scrollToBottom();
};

// 确认语音输入
const confirmVoiceInput = async () => {
  console.log('✅ 用户点击确认，停止录音并处理转录结果...');
  
  // 🔧 首先停止当前的语音识别
  if ((window as any).currentRecognition && isListening.value) {
    console.log('🛑 手动停止语音识别...');
    (window as any).currentRecognition.stop();
    isListening.value = false;
  }
  
  isWaitingConfirm.value = false;
  
  // 🔧 等待一小段时间确保转录结果完整
  await new Promise(resolve => {
    createTimer(() => resolve(undefined), 500, undefined, '转录结果等待', componentName);
  });
  
  try {
    // 检查是否有转录内容
    if (currentTranscript.value && currentTranscript.value.trim()) {
      console.log('📝 处理语音转录结果:', currentTranscript.value);
      
      // 添加用户消息到对话历史
      const userMessage: OracleMessage = {
        role: 'user',
        content: currentTranscript.value.trim(),
        timestamp: new Date()
      };
      messages.value.push(userMessage);
      scrollToBottom();
      
      // 🔧 关键：通过TTS-only系统发送消息（和神谕模式一样）
      console.log('📤 通过TTS-only系统发送转录消息:', currentTranscript.value.trim());
      await sendRealtimeMessage(currentTranscript.value.trim());
      
      // 清空转录内容
      currentTranscript.value = '';
      
    } else {
      console.log('📝 无语音转录内容');
      const errorMessage: OracleMessage = {
        role: 'assistant',
        content: '未检测到语音内容，请重新尝试录音。',
        timestamp: new Date()
      };
      messages.value.push(errorMessage);
      scrollToBottom();
    }
    
  } catch (error: any) {
    console.error('❌ 发送语音转录内容时发生错误:', error);
    isWaitingConfirm.value = false;
    isListening.value = false;
    
    const errorMessage: OracleMessage = {
      role: 'assistant',
      content: '发送语音内容时发生错误，请重试。',
      timestamp: new Date()
    };
    messages.value.push(errorMessage);
    scrollToBottom();
  }
};

// 开始语音监听
const startListening = async () => {
  // 🔧 检查是否可以使用语音功能
  if (!canUseVoice.value) {
    console.log('⚠️ 当前无法使用语音功能:', getVoiceButtonTooltip());
    return;
  }
  
  // 🔧 防止重复启动
  if (isListening.value || isWaitingConfirm.value) {
    console.log('⚠️ 语音识别正在进行中或等待确认，忽略重复启动');
    return;
  }
  
  // 🔧 清理之前的实例
  if ((window as any).currentRecognition) {
    try {
      (window as any).currentRecognition.abort();
    } catch (e) {
      // 忽略清理错误
    }
    (window as any).currentRecognition = null;
  }
  
  isListening.value = true;
  isWaitingConfirm.value = false;
  currentTranscript.value = '';
  
  try {
    console.log('🎤 开始语音录制...');
    
    // 启动浏览器的语音录制功能
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = true;  // 🔧 改为持续录音
      recognition.interimResults = true;
      recognition.lang = 'zh-CN';
      
      recognition.onstart = () => {
        console.log('🎤 语音识别已启动');
        const successMessage: OracleMessage = {
          role: 'assistant',
          content: '🎤 开始录制语音，请说话...',
          timestamp: new Date()
        };
        messages.value.push(successMessage);
        scrollToBottom();
      };
      
      recognition.onresult = (event: any) => {
        let interimTranscript = '';
        let finalTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }
        
        if (finalTranscript) {
          currentTranscript.value = finalTranscript;
          console.log('🎯 获得最终转录结果:', finalTranscript);
          
          // 🔧 获得最终结果后，延迟一下然后停止识别
          createTimer(() => {
            if (recognition && isListening.value) {
              console.log('🛑 最终结果获得，停止语音识别');
              recognition.stop();
            }
          }, 1000, undefined, '语音识别自动停止', componentName);
        } else if (interimTranscript) {
          currentTranscript.value = interimTranscript;
          console.log('🎙️ 临时转录结果:', interimTranscript);
        }
      };
      
      recognition.onend = async () => {
        console.log('🎤 语音识别结束');
        isListening.value = false;
        
        // 🔧 修改：自动发送转录结果，不需要确认
        if (currentTranscript.value && currentTranscript.value.trim()) {
          console.log('📝 自动发送语音转录结果:', currentTranscript.value);
          await handleSendMessage();
        } else {
          console.log('📝 无语音识别结果');
          const errorMessage: OracleMessage = {
            role: 'assistant',
            content: '未检测到语音内容，请重新尝试录音。',
            timestamp: new Date()
          };
          messages.value.push(errorMessage);
          scrollToBottom();
        }
      };
      
      recognition.onerror = async (event: any) => {
        console.error('❌ 语音识别错误:', event.error);
        isListening.value = false;
        
        // 🔧 修改：如果是aborted错误且有转录结果，则自动发送
        if (event.error === 'aborted' && currentTranscript.value && currentTranscript.value.trim()) {
          console.log('🔧 aborted错误但有转录结果，自动发送');
          await handleSendMessage();
        } else {
          const errorMessage: OracleMessage = {
            role: 'assistant',
            content: `语音识别失败: ${event.error}，请重试。`,
            timestamp: new Date()
          };
          messages.value.push(errorMessage);
          scrollToBottom();
        }
      };
      
      // 🔧 保存recognition实例，以便手动停止
      (window as any).currentRecognition = recognition;
      
      recognition.start();
      
      // 🔧 移除自动停止，让用户手动点击"确认"来停止录音
      
    } else {
      throw new Error('浏览器不支持语音识别功能');
    }
    
  } catch (error: any) {
    console.error('❌ 启动语音录制失败:', error);
    isListening.value = false;
    isWaitingConfirm.value = false;
    
    const errorMessage: OracleMessage = {
      role: 'assistant',
      content: '语音录制启动失败，请检查麦克风权限或稍后再试。',
      timestamp: new Date()
    };
    messages.value.push(errorMessage);
    scrollToBottom();
  }
};

const stopListening = async () => {
  if (!isListening.value) return;
  
  console.log('🛑 停止语音识别...');
  
  try {
    // 🔧 修复：VAD会在识别到语音结束后自动停止，这里只需要设置状态
    console.log('✅ VAD语音识别已停止（自动）');
    
  } catch (error: any) {
    console.warn('⚠️ 停止VAD时出错:', error);
  }
  
  isListening.value = false;
  
  // 🔧 修复：如果有识别结果，发送消息
  if (currentTranscript.value && currentTranscript.value.trim()) {
    console.log('📝 发送语音识别结果:', currentTranscript.value);
    await sendRealtimeMessage(currentTranscript.value);
  } else {
    console.log('📝 无语音识别结果');
  }
  
  currentTranscript.value = '';
};

// 发送手动输入的消息
const sendManualMessage = () => {
  if (!manualInput.value.trim() || !isVoiceActive.value) return;
  
  // 🔧 修改：根据当前阶段决定处理方式
  if (isInHexagramReadingPhase.value) {
    console.log('📖 卦象解读阶段，忽略手动输入');
    manualInput.value = '';
    return;
  }
  
  // 🔧 修改：使用WebSocket实时对话系统
  sendRealtimeMessage(manualInput.value);
  manualInput.value = '';
};

// 发送消息（已修改为WebSocket系统）
const sendMessage = async (content: string) => {
  if (!content.trim()) return;

  // 🔧 修改：直接使用WebSocket实时对话系统
  await sendRealtimeMessage(content);
};

// 获取当前LLM参数设置
const getCurrentLLMParams = () => {
  // 如果使用预设，则使用预设参数
  if (selectedPreset.value) {
    const preset = availablePresets.value.find(p => p.name === selectedPreset.value);
    if (preset) {
      console.log('📋 使用预设参数:', selectedPreset.value, preset);
      return {
        temperature: preset.temperature,
        topP: preset.topP,
        topK: preset.topK,
        maxTokens: preset.maxTokens
      };
    }
  }
  
  // 否则使用手动设置的参数
  console.log('⚙️ 使用手动参数设置');
  return {
    temperature: oracleConfig.temperature,
    topP: oracleConfig.topP,
    topK: oracleConfig.topK,
    maxTokens: oracleConfig.maxTokens
  };
};

// 播放音频 - 🔧 修改：适配WebSocket实时对话系统的音频播放
const playAudio = async (audioUrl: string) => {
  if (!audioPlayerRef.value) {
    console.error('❌ 音频播放器引用不存在');
    return;
  }
  
  console.log('🎵 播放音频:', audioUrl, '当前阶段:', isInHexagramReadingPhase.value ? '卦象解读' : '实时对话');
  
  if (!audioUrl || audioUrl.trim() === '') {
    console.error('❌ 音频URL为空');
    return;
  }
  
  // 🔧 新增：检查是否是分块音频的开始
  const isStreamingAudio = audioUrl.includes('chunk') || audioChunks.value.length > 0;
  
  if (isStreamingAudio) {
    console.log('🎵 检测到流式音频，使用分块播放模式');
    playAudioChunkWithQuickTransition(audioUrl, 0, 1);
  } else {
    console.log('🎵 单个音频直接播放');
    playAudioChunkWithQuickTransition(audioUrl, 0, 1);
  }
};

// 🔧 新增：优化的音频播放方法（参考RealtimeView）
const playAudioChunkWithQuickTransition = (audioUrl: string, chunkIndex: number, totalChunks: number) => {
  if (!audioPlayerRef.value) {
    console.error('❌ 音频播放器元素不存在');
    return;
  }
  
  // 🔧 关键修复：添加音频有效性检查
  if (!audioUrl || audioUrl.trim() === '') {
    console.error('❌ 音频URL无效，跳过播放');
    handleAllAudioComplete();
    return;
  }
  
  // 🔧 重要修复：只在接收到新音频事件时检查播放状态，在播放下一个片段时不检查
  // 这个检查移除，因为playNextAudioChunk会正确管理播放状态
  
  // 🔧 简化音频播放日志，隐藏长URL
  const shortUrl = audioUrl.length > 60 ? audioUrl.substring(0, 60) + '...' : audioUrl;
  console.log(`🎵 开始播放音频片段 ${chunkIndex + 1}/${totalChunks}: ${shortUrl}`);
  
  // 🔧 神谕之音特殊处理：在音频播放开始前立即暂停VAD
  if (chunkIndex === 0) {
    console.log('🎵 神谕之音：音频开始播放，立即暂停VAD监听');
    pauseVADForAudioPlayback();
  }
  
  // 🔧 重要修复：确保状态正确设置
  isPlayingAudio.value = true;
  (window as any).isPlayingAudio = true; // 🔧 新增：同步全局状态
  
  // 设置音频源
  audioPlayerRef.value.src = audioUrl;
  
  // 预加载音频以减少播放延迟
  audioPlayerRef.value.preload = 'auto';
  
  // 🔧 关键修复：在播放前记录开始时间
  audioPlayStartTime.value = Date.now();
  
  // 立即开始播放
  const playPromise = audioPlayerRef.value.play();
  
  playPromise.then(() => {
    console.log(`✅ 音频片段 ${chunkIndex + 1}/${totalChunks} 开始播放`);
    isPlayingAudio.value = true;
  }).catch(error => {
    console.error(`❌ 音频片段 ${chunkIndex + 1}/${totalChunks} 播放失败: ${error.message || error}`);
    isPlayingAudio.value = false;
    
    // 🔧 关键修复：移除弹窗提示，避免影响用户体验
    console.warn('⚠️ 音频播放失败，等待自动恢复VAD');
    
    // 如果是多片段音频，尝试播放下一个片段
    if (chunkIndex < totalChunks - 1) {
      createTimer(() => playNextAudioChunk(), 100, undefined, '播放下一个音频片段', componentName);
    } else {
      // 播放失败也要恢复VAD
      createTimer(() => resumeVADAfterAudioComplete(), 500, undefined, '播放失败后恢复VAD', componentName);
    }
  });
};

// 🔧 新增：音频事件处理器（参考RealtimeView）
const audioCanPlayHandler = () => {
  console.log('🎵 音频预加载完成，可以播放');
};

const audioPlayHandler = () => {
  console.log('🎵 音频开始播放');
  isPlayingAudio.value = true;
  (window as any).isPlayingAudio = true; // 🔧 新增：同步全局状态
  audioPlayStartTime.value = Date.now();
};

const audioEndedHandler = () => {
  console.log('🎵 音频播放结束');
  
  // 🔧 关键修复：立即重置播放状态，允许下一个片段播放
  isPlayingAudio.value = false;
  
  // 🔧 关键修复：检查播放时长，防止误判（参考RealtimeView）
  const playDuration = (Date.now() - audioPlayStartTime.value) / 1000;
  const minValidDuration = 0.1;
  
  // 🔧 新增：获取音频实际时长进行对比验证
  const audioDuration = audioPlayerRef.value?.duration || 0;
  const playRatio = audioDuration > 0 ? playDuration / audioDuration : 1;
  
  const isStreamingAudio = audioChunks.value && audioChunks.value.length > 1;
  
  console.log(`🔍 播放验证: 播放时长=${playDuration.toFixed(2)}s, 音频时长=${audioDuration.toFixed(2)}s, 播放比例=${(playRatio * 100).toFixed(1)}%, 流式音频=${isStreamingAudio}, 卦象解读阶段=${isInHexagramReadingPhase.value}`);
  
  // 🔧 关键修复：统一使用宽松的验证策略，适配长文本流式音频
  const isLongTextAudio = audioChunks.value.length > 3; // 超过3个片段认为是长文本
  
  if (isInHexagramReadingPhase.value || isLongTextAudio) {
    // 卦象解读阶段或长文本：使用宽松验证，避免误判中断
    if (playDuration < 0.01) {
      console.warn(`⚠️ 音频播放时间极短 (${playDuration.toFixed(3)}秒)，但卦象解读阶段，继续处理`);
      // 不return，继续处理
    }
    console.log(`✅ 音频播放完成 (${playDuration.toFixed(2)}秒)，片段${currentChunkIndex.value + 1}/${audioChunks.value.length}`);
  } else {
    // 实时对话短文本：使用原有的验证逻辑
    if (isStreamingAudio) {
      if (playDuration < 0.05) {
        console.warn(`⚠️ 流式音频片段播放时间极短 (${playDuration.toFixed(2)}秒)，但继续处理`);
        // 不return，继续处理
      }
      console.log(`✅ 流式音频片段播放完成 (${playDuration.toFixed(2)}秒)`);
    } else {
      if (playDuration < minValidDuration) {
        console.warn(`⚠️ 单个音频播放时间极短 (${playDuration.toFixed(2)}秒)，但尝试继续处理`);
        // 不return，给音频播放一个机会
      }
      
      // 🔧 修复：对于多片段音频，降低播放比例要求
      const minPlayRatio = audioChunks.value.length > 1 ? 0.1 : 0.3;
      if (audioDuration > 2.0 && playRatio < minPlayRatio) {
        console.warn(`⚠️ 音频播放比例低 (${(playRatio * 100).toFixed(1)}%)，继续播放下一个片段`);
        // 不return，继续播放下一个片段
      }
      
      console.log(`✅ 音频正常播放完成 (${playDuration.toFixed(2)}秒)`);
    }
  }
  
  // ⚡ 快速检查是否有下一个片段需要播放
  if (audioChunks.value && audioChunks.value.length > 1) {
    createTimer(() => playNextAudioChunk(), 50, undefined, '播放下一个音频片段快速检查', componentName); // 减少延迟到50ms
  } else {
    handleAllAudioComplete();
  }
};

const audioErrorHandler = (e: Event) => {
  console.error('🎵 音频加载错误:', e);
  isPlayingAudio.value = false;
  console.warn('⚠️ 音频播放错误，等待自动恢复VAD');
};

// 🔧 新增：播放下一个音频片段（参考RealtimeView）
const playNextAudioChunk = () => {
  if (!audioChunks.value || audioChunks.value.length === 0) {
    console.log('🏁 没有更多音频片段');
    handleAllAudioComplete();
    return;
  }
  
  currentChunkIndex.value++;
  
  // 🔧 关键修复：检查是否有下一个片段，或者是否应该等待流式音频
      if (currentChunkIndex.value < audioChunks.value.length) {
      const nextChunkUrl = audioChunks.value[currentChunkIndex.value];
      if (nextChunkUrl && audioPlayerRef.value) {
        console.log(`🔗 播放下一个音频片段 ${currentChunkIndex.value + 1}/${audioChunks.value.length}`);
        playAudioChunkWithQuickTransition(nextChunkUrl, currentChunkIndex.value, audioChunks.value.length);
      } else {
        console.log('⚠️ 下一个片段URL无效，尝试播放后续片段');
        createTimer(() => playNextAudioChunk(), 50, undefined, '重试播放后续片段', componentName);
      }
    } else if (isWaitingForAudioChunks.value) {
    // 🔧 新增：如果正在等待更多流式音频片段，延迟重试
    console.log(`⏳ 等待更多音频片段... 当前: ${currentChunkIndex.value + 1}, 队列长度: ${audioChunks.value.length}`);
    
    // 🔧 关键修复：增加重试次数计数器，避免无限等待
    const maxRetries = 20; // 最多重试20次 (20 * 500ms = 10秒)
    let retryCount = 0;
    
    const waitForMoreChunks = () => {
      retryCount++;
      console.log(`🔄 等待重试 ${retryCount}/${maxRetries}...`);
      
      // 重新检查是否有新的音频片段到达
      if (currentChunkIndex.value < audioChunks.value.length) {
        console.log('📦 检测到新的音频片段，继续播放');
        playNextAudioChunk();
      } else if (isWaitingForAudioChunks.value && retryCount < maxRetries) {
        // 仍在等待，继续重试
        createTimer(waitForMoreChunks, 500, undefined, '等待更多音频片段重试', componentName);
      } else {
        // 不再等待或达到重试上限，结束播放
        console.log(`🏁 流式音频等待结束 (重试${retryCount}次)，完成播放`);
        isWaitingForAudioChunks.value = false;
        handleAllAudioComplete();
      }
    };
    
    createTimer(waitForMoreChunks, 500, undefined, '开始等待更多音频片段', componentName); // 500ms后开始重试
  } else {
    console.log('🏁 所有音频片段播放完成');
    handleAllAudioComplete();
  }
};

// 🔧 新增：音频播放完成处理（参考RealtimeView精准VAD恢复）
const handleAllAudioComplete = async () => {
  console.log('🎵 神谕之音：音频播放序列完成，立即恢复VAD监听');
  console.log(`📊 播放完成统计: 音频片段数=${audioChunks.value.length}, 当前片段=${currentChunkIndex.value}`);

  // 🔧 重要修复：彻底清理状态
  audioChunks.value = [];
  currentChunkIndex.value = 0;
  isPlayingAudio.value = false;
  isWaitingForAudioChunks.value = false;
  wasAudioPlayingBeforePause.value = false;

  // 🔧 新增：同步全局音频播放状态
  (window as any).isPlayingAudio = false;

  // 🔧 清理音频块超时定时器
  if (audioChunkTimeout.value) {
    clearTimer(audioChunkTimeout.value);
    audioChunkTimeout.value = null;
  }

  // 🔧 修复：检查组件是否正在卸载，如果是则执行延迟的清理操作
  if (isComponentUnmounting.value) {
    console.log('🗑️ 音频播放完成，执行延迟的组件卸载清理...');
    try {
      // 执行之前被延迟的清理操作
      await API.stopRealtimeDialogue();
      console.log('✅ 延迟清理：实时对话服务已停止');

      // 清理WebSocket连接
      cleanupWebSocketListeners();
      console.log('✅ 延迟清理：WebSocket连接已清理');

      // 清理VAD状态
      await API.forceRestartVADAudioStream();
      console.log('✅ 延迟清理：VAD状态已清理');

    } catch (error) {
      console.warn('⚠️ 延迟清理失败:', error);
    }
    return; // 不执行正常的VAD恢复
  }
  
  // ⚡ 立即通知后端音频播放完成，恢复VAD监听（参考RealtimeView）
  resumeVADAfterAudioComplete();

  // 🔧 关键修改：根据当前阶段决定后续处理
  createTimer(() => {
    console.log('🎵 音频播放完成，检查当前阶段...');
    console.log(`📊 当前状态: isInHexagramReadingPhase=${isInHexagramReadingPhase.value}, connectionStatus=${connectionStatus.value}`);
    
    if (isInHexagramReadingPhase.value) {
      console.log('📖 卦象解读音频播放完成，结束解读阶段');
      // 🔧 修复：直接结束卦象解读阶段，不需要启动实时对话
      isInHexagramReadingPhase.value = false;
      isProcessingHexagram.value = false;
      console.log('✅ 卦象解读流程完成');
    } else {
      console.log('💬 实时对话音频播放完成，检查是否需要结束对话');
      checkAndEndDialogue();
    }
  }, 1000, undefined, '音频播放完成后续处理', componentName); // 延迟1秒，确保VAD恢复完成
};

// 🔧 新增：暂停VAD用于音频播放（神谕之音专用）
const pauseVADForAudioPlayback = async () => {
  try {
    console.log('🔇 神谕之音：暂停VAD监听用于音频播放');
    // 注意：神谕之音使用的是实时对话系统，VAD暂停由后端自动管理
    // 这里主要是标记状态，实际的VAD控制由WebSocket实时对话系统处理
    isListening.value = false;
  } catch (error) {
    console.warn('⚠️ 暂停VAD失败:', error);
  }
};

// 🔧 新增：恢复VAD监听（参考RealtimeView的精准恢复）
const resumeVADAfterAudioComplete = async () => {
  try {
    console.log('📡 神谕之音：通知后端音频播放完成，请求恢复VAD监听');
    
    // 🔧 关键：只在实时对话模式下恢复VAD，卦象解读模式不需要
    if (!isInHexagramReadingPhase.value && connectionStatus.value === 'connected') {
      const result = await API.resumeVADListening();
      if (result.success) {
        console.log('✅ VAD监听已恢复');
      } else {
        console.warn('⚠️ VAD监听恢复失败:', result.message);
      }
    } else {
      console.log('📖 卦象解读阶段或WebSocket未连接，跳过VAD恢复');
    }
  } catch (error) {
    console.warn('⚠️ 通知音频播放完成失败:', error);
  }
};

// 🔧 新增：预先通知音频即将结束（参考RealtimeView）
const notifyAudioPlaybackNearEnd = async () => {
  try {
    console.log('📡 神谕之音：通知后端音频即将结束');
    // 这里可以调用API通知后端准备恢复VAD
    // 神谕之音的实现可以根据需要决定是否使用此功能
  } catch (error) {
    console.warn('⚠️ 通知音频即将结束失败:', error);
  }
};

// 🔧 新增：直接测试音频播放功能 - 修复为使用统一的播放方法
const testDirectAudioPlay = async (audioUrl: string) => {
  if (!audioPlayerRef.value) {
    console.error('❌ 测试音频播放：音频播放器不存在');
    return;
  }
  
  try {
    console.log('🧪 开始直接音频播放测试');
    
    // 🔧 修复：使用统一的音频播放方法，避免重复设置播放器状态
    // 直接调用现有的播放方法，不要重复设置状态
    playAudioChunkWithQuickTransition(audioUrl, 0, 1);
    
  } catch (error) {
    console.error('❌ 测试音频播放异常:', error);
  }
};

// 🔧 新增：创建需要用户交互的音频播放
const createUserInteractionAudioPlay = (audioUrl: string) => {
  console.log('👆 创建用户交互音频播放');
  
  // 暂时显示一个提示，让用户点击播放
  const playButton = document.createElement('button');
  playButton.innerText = '▶️ 点击播放神谕之音';
  playButton.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
    padding: 10px 20px;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  `;
  
  playButton.onclick = () => {
    if (audioPlayerRef.value) {
      audioPlayerRef.value.src = audioUrl;
      audioPlayerRef.value.play().then(() => {
        console.log('✅ 用户交互后音频播放成功');
        isPlayingAudio.value = true;
        document.body.removeChild(playButton);
      }).catch(error => {
        console.error('❌ 用户交互后音频播放仍然失败:', error);
        document.body.removeChild(playButton);
      });
    }
  };
  
  document.body.appendChild(playButton);
  
  // 5秒后自动移除按钮
  createTimer(() => {
    if (document.body.contains(playButton)) {
      document.body.removeChild(playButton);
    }
  }, 5000, undefined, '移除音频播放按钮', componentName);
};

// 清空对话
const clearConversation = () => {
  messages.value = [];
  if (isVoiceActive.value) {
    addWelcomeMessage();
  }
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (expandedConversationArea.value) {
    expandedConversationArea.value.scrollTop = expandedConversationArea.value.scrollHeight;
  }
};

// ===================== 初始化 =====================
const initializeOracle = async () => {
  console.log('🔮 神谕之音初始化开始...');

  // 🚀 立即设置基础默认配置，确保功能可用
  console.log('⚡ 立即设置基础默认配置...');
  oracleConfig.currentTemplate = '经典神谕';
  oracleConfig.selectedModel = 'lmstudio/csxl0.6'; // 🔧 强制设置csxl0.6模型
  oracleConfig.selectedVoice = '21'; // 使用默认音色ID

  // 🔧 设置默认角色：藏识仙灵
  oracleConfig.selectedCharacter = {
    id: 'cangshi-xianling',
    name: '藏识仙灵',
    description: '深谙周易玄学的神谕大师',
    avatar: '',
    systemPrompt: '你是一位深谙周易玄学的神谕大师，拥有深厚的易学功底和人生智慧。以古雅而亲切的语调，结合周易哲学为求问者提供精准指导。'
  };

  console.log('✅ 基础默认配置已设置:', {
    模型: oracleConfig.selectedModel,
    音色: oracleConfig.selectedVoice,
    角色: oracleConfig.selectedCharacter?.name
  });

  // 🚀 后台异步加载完整配置，不阻塞主流程
  Promise.resolve().then(async () => {
    console.log('🔄 后台开始加载完整配置数据...');
    try {
      // 加载模型、音色、角色列表和预设
      await Promise.allSettled([
        refreshModelList().catch(err => {
          console.warn('⚠️ 模型列表加载失败，使用默认配置:', err.message);
          // 设置fallback模型列表
          availableModels.value = ['lmstudio/csxl0.6', 'lmstudio/gemma-3-4b-it'];
        }),
        refreshVoiceList().catch(err => {
          console.warn('⚠️ 音色列表加载失败，使用默认配置:', err.message);
        }),
        refreshCharacterList().catch(err => {
          console.warn('⚠️ 角色列表加载失败，使用默认配置:', err.message);
        }),
        loadPresets().catch(err => {
          console.warn('⚠️ 预设加载失败，使用默认配置:', err.message);
        })
      ]);

      // 重新设置配置（如果API加载成功的话）
      setDefaultConfiguration();
      console.log('✅ 后台配置加载完成');
    } catch (error) {
      console.warn('⚠️ 后台配置加载过程出错，但不影响基本功能:', error);
    }
  });

  console.log('✅ 神谕之音初始化完成，基本功能已可用');
};

// 设置默认配置
const setDefaultConfiguration = () => {
  console.log('🎯 设置默认神谕配置...');
  
  // 1. 设置默认智慧源泉：lmstudio的csxl0.6模型
  const targetModel = availableModels.value.find(model => 
    model.toLowerCase().includes('csxl0.6') || 
    model.toLowerCase().includes('lmstudio:csxl0.6') ||
    model.toLowerCase().includes('lmstudio') && model.toLowerCase().includes('csxl')
  );
  if (targetModel) {
    oracleConfig.selectedModel = targetModel;
    console.log('✅ 设置默认智慧源泉:', targetModel);
  } else {
    console.warn('⚠️ 未找到csxl0.6模型，可用模型:', availableModels.value);
    // 如果找不到csxl0.6，寻找包含lmstudio的模型
    const lmstudioModel = availableModels.value.find(model => 
      model.toLowerCase().includes('lmstudio')
    );
    if (lmstudioModel) {
      oracleConfig.selectedModel = lmstudioModel;
      console.log('✅ 设置备选lmstudio模型:', lmstudioModel);
    }
  }
  
  // 2. 设置默认神谕音色：藏识仙灵  
  const targetVoice = availableVoices.value.find(voice => 
    voice.name && voice.name.includes('藏识仙灵')
  );
  if (targetVoice) {
    oracleConfig.selectedVoice = targetVoice.id;
    console.log('✅ 设置默认神谕音色:', targetVoice.name);
  } else {
    console.warn('⚠️ 未找到藏识仙灵音色，可用音色:', availableVoices.value.map(v => v.name));
  }
  
  // 3. 设置默认角色：藏识仙灵角色
  const targetCharacter = availableCharacters.value.find(character => 
    character.name && (
      character.name.includes('藏识仙灵') || 
      character.name.includes('莫些人藏识仙灵') ||
      character.name.includes('藏识')
    )
  );
  if (targetCharacter) {
    oracleConfig.selectedCharacter = targetCharacter;
    console.log('✅ 设置默认神谕角色:', targetCharacter.name);
  } else {
    console.warn('⚠️ 未找到藏识仙灵角色，可用角色:', availableCharacters.value.map(c => c.name));
    // 如果找不到藏识仙灵角色，选择第一个可用角色
    if (availableCharacters.value.length > 0) {
      oracleConfig.selectedCharacter = availableCharacters.value[0];
      console.log('✅ 设置备选神谕角色:', oracleConfig.selectedCharacter.name);
    }
  }
  
  // 4. 设置默认参数预设：gemma3
  const targetPreset = availablePresets.value.find(preset => 
    preset.name && preset.name.toLowerCase().includes('gemma3')
  );
  if (targetPreset) {
    selectedPreset.value = targetPreset.name;
    applyPreset(targetPreset);
    console.log('✅ 设置默认参数预设:', targetPreset.name);
  } else {
    console.warn('⚠️ 未找到gemma3预设，可用预设:', availablePresets.value.map(p => p.name));
    // 如果找不到gemma3预设，寻找包含gemma的预设
    const gemmaPreset = availablePresets.value.find(preset => 
      preset.name && preset.name.toLowerCase().includes('gemma')
    );
    if (gemmaPreset) {
      selectedPreset.value = gemmaPreset.name;
      applyPreset(gemmaPreset);
      console.log('✅ 设置备选gemma预设:', gemmaPreset.name);
    }
  }
  
  // 发出配置变更事件
  emit('config-changed', { ...oracleConfig });

  // 🔧 设置初始化完成标志
  isInitialized.value = true;
  console.log('✅ 神谕之音初始化完成');
};

// ===================== 生命周期 =====================
onMounted(async () => {
  console.log('🎯 神谕之音组件挂载开始');

  // 🔧 页面切换修复：延迟初始化，确保前一个页面的WebSocket完全清理
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 🔧 新增：设置全局音频播放状态标记
  (window as any).isPlayingAudio = false;

  try {
    await initializeOracle();

    // 🔧 修复：注册WebSocket事件处理器
    websocket.registerHandlers({
      onLLMResponse: (data: any) => {
        console.log('🤖 神谕之音：useWebSocket收到LLM回复事件', data)
        handleLLMResponse(data)
      },
      onTTSAudio: (data: any) => {
        console.log('🎵 神谕之音：useWebSocket收到TTS音频事件', data)
        handleTTSAudio(data)
      },
      onTTSError: (error: any) => {
        console.error('🎵 神谕之音：useWebSocket收到TTS错误事件', error)
        handleTTSError(error)
      },
      onTranscription: (data: any) => {
        console.log('🎤 神谕之音：useWebSocket收到转录事件', data)
        handleTranscriptionUpdate(data)
      },
    onStatusUpdate: (data: any) => {
      console.log('📊 神谕之音：useWebSocket收到状态更新事件', data)
      handleStatusUpdate(data)
    },
    onConnected: (data: any) => {
      console.log('✅ 神谕之音：useWebSocket连接成功', data)
      connectionStatus.value = 'connected'
    },
    onDisconnected: (data: any) => {
      console.log('❌ 神谕之音：useWebSocket连接断开', data)
      connectionStatus.value = 'disconnected'

      // 🔧 神谕之音：在TTS播放期间不要立即重连，避免中断音频
      if (isPlayingAudio.value) {
        console.log('🎵 神谕之音：TTS播放中，延迟重连避免音频中断')
        // 等待音频播放完成后再重连
        setTimeout(() => {
          if (!isPlayingAudio.value && connectionStatus.value === 'disconnected') {
            console.log('🔄 神谕之音：音频播放完成，尝试重连')
            // 这里可以触发重连逻辑
          }
        }, 5000)
      }
    },
    onError: (error: any) => {
      console.error('❌ 神谕之音：useWebSocket错误', error)
      handleWebSocketError(error)
    }
  });
  
  console.log('✅ 神谕之音组件已挂载，WebSocket事件处理器已注册');
  
  // 🔧 新增：页面刷新/离开时的清理
  const handleBeforeUnload = async (event: BeforeUnloadEvent) => {
    if (isVoiceActive.value) {
      console.log('🌍 页面即将离开，快速清理神谕对话...');
      
      // 设置一个简短的延迟让浏览器有时间处理清理请求
      event.preventDefault();
      
      try {
        // 快速停止关键服务
        await Promise.race([
          API.stopRealtimeDialogue(),
          new Promise(resolve => {
            createTimer(() => resolve(undefined), 500, undefined, '页面离开清理超时', componentName);
          }) // 最多等500ms
        ]);
        console.log('✅ 页面离开时清理完成');
      } catch (error) {
        console.warn('⚠️ 页面离开时清理失败:', error);
      }
    }
  };
  
  // 监听页面刷新/关闭事件
  window.addEventListener('beforeunload', handleBeforeUnload);

  // 🔧 保存清理函数以便在卸载时移除
  (onMounted as any)._beforeUnloadHandler = handleBeforeUnload;

  console.log('✅ 神谕之音组件挂载完成');

  } catch (error) {
    console.error('❌ 神谕之音组件挂载失败:', error);
    // 即使挂载失败也要注册基本的事件处理器
    websocket.registerHandlers({
      onLLMResponse: (data: any) => handleLLMResponse(data),
      onTTSAudio: (data: any) => handleTTSAudio(data),
      onTTSError: (error: any) => handleTTSError(error),
      onTranscription: (data: any) => handleTranscriptionUpdate(data),
    });
  }
});

onUnmounted(async () => {
  console.log('🗑️ 神谕之音组件正在卸载，执行彻底清理...');
  
  try {
    // 🕒 优先清理所有定时器
    const clearedTimers = clearComponentTimers(componentName);
    console.log(`🧹 清理组件定时器: ${clearedTimers} 个`);
    
    // 移除页面离开事件监听器
    const beforeUnloadHandler = (onMounted as any)._beforeUnloadHandler;
    if (beforeUnloadHandler) {
      window.removeEventListener('beforeunload', beforeUnloadHandler);
      console.log('✅ 页面离开事件监听器已移除');
    }
    
    // 🔧 修复：检查是否正在播放音频，如果是则延迟卸载
    if (isPlayingAudio.value) {
      console.log('🎵 检测到音频正在播放，标记组件正在卸载但保持连接');
      isComponentUnmounting.value = true;
      // 不立即断开连接，等待音频播放完成
      return;
    }

    // 强制停止神谕对话
    if (isVoiceActive.value) {
      await stopVoiceOracle();
    }

    // 🔧 修复：只有在没有音频播放时才清理WebSocket
    if (!isPlayingAudio.value) {
      // 额外的组件卸载清理
      cleanupWebSocketListeners();

      // 🔧 修复：使用useWebSocket取消订阅，移除废弃的unifiedWebSocketManager
      console.log('✅ 使用useWebSocket进行组件清理（在onUnmounted中自动处理）');

      // 确保后端服务完全停止
      try {
        await API.stopRealtimeDialogue();
        console.log('✅ 组件卸载：实时对话服务已停止');
      } catch (error) {
        console.warn('⚠️ 组件卸载：停止实时对话服务失败:', error);
      }
    } else {
      console.log('🎵 音频播放中，延迟清理WebSocket连接');
    }
    
    // 强制清理残留状态
    try {
      await API.forceRestartVADAudioStream();
      console.log('✅ 组件卸载：VAD状态已清理');
    } catch (error) {
      console.warn('⚠️ 组件卸载：VAD清理失败:', error);
    }
    
    // 🔧 新增：强制清理连接管理器中的所有活跃连接
    console.log('🧹 清理HTTP连接...');
    // 连接清理已通过其他机制处理
    console.log('✅ HTTP连接清理完成');
    
    console.log('🏁 神谕之音组件卸载清理完成');
    
  } catch (error) {
    console.error('❌ 组件卸载清理失败:', error);
  }
});

// ===================== 监听卦象变化 =====================
// 防止重复触发的标志
const isProcessingHexagram = ref(false);
const lastProcessedHexagramId = ref<string | null>(null);
let hexagramProcessTimeout: string | null = null; // 🔧 修改为字符串类型，存储定时器ID

// 监听卦象变化，自动进行神谕解读
watch(() => props.currentHexagram, (newHexagram, oldHexagram) => {
  // 防止重复触发
  if (isProcessingHexagram.value) {
    console.log('🔄 卦象解读正在进行中，忽略重复触发');
    return;
  }
  
  // 如果有新卦象，且不是组件初始化时的变化，且不是重复的卦象
  if (newHexagram && 
      newHexagram !== oldHexagram && 
      newHexagram.id !== lastProcessedHexagramId.value) {
    
    console.log('🎯 检测到新卦象，准备自动神谕解读:', newHexagram.name);
    
    // 设置处理标志和记录当前卦象ID
    isProcessingHexagram.value = true;
    lastProcessedHexagramId.value = newHexagram.id;
    
    // 🔧 清除之前的定时器
    if (hexagramProcessTimeout) {
      clearTimer(hexagramProcessTimeout);
    }
    
    // 🔧 使用防抖机制，避免重复处理
    hexagramProcessTimeout = createTimer(async () => {
      try {
        // 检查必要的配置是否齐全
        if (!canAutoReadHexagram.value) {
          console.warn('⚠️ 神谕配置不完整，无法自动解读');
          console.log('当前配置状态:', {
            selectedModel: oracleConfig.selectedModel,
            selectedVoice: oracleConfig.selectedVoice,
            selectedCharacter: oracleConfig.selectedCharacter?.name
          });
          return;
        }
        
        // 确保WebSocket实时对话系统已启动
        if (!isVoiceActive.value) {
          console.log('🚀 启动WebSocket实时对话系统进行自动解读...');
          await startVoiceOracle();
          // 启动成功后会自动发送解读请求，这里不需要额外操作
        } else {
          // 如果已经启动，直接发送解读请求
          console.log('🚀 直接发送自动解读请求...');
          const autoMessage = buildAutoHexagramMessage(newHexagram);
          await sendRealtimeMessage(autoMessage);
        }
      } catch (error) {
        console.error('❌ 卦象解读处理失败:', error);
      } finally {
        isProcessingHexagram.value = false;
      }
    }, 2000, 'hexagramProcessTimeout', '卦象解读防抖', componentName); // 2秒防抖延迟，给系统更多时间稳定
  }
}, { deep: true });

// 构建自动卦象解读消息
const buildAutoHexagramMessage = (hexagram: any) => {
  let message = `请为我解读刚刚抽取的卦象：${hexagram.name}卦`;
  
  if (props.selectedTopic) {
    const topicInfo = getTopicInfo(props.selectedTopic);
    message += `，关于${topicInfo.split(' - ')[0]}方面的问题`;
  }
  
  message += '。';
  
  console.log('📜 自动构建的解读消息:', message);
  return message;
};

// 🔧 新增：自动发送卦象解读消息
const sendAutoHexagramMessage = async () => {
  if (!hasHexagramData.value) return;

  const autoMessage = buildAutoHexagramMessage(props.currentHexagram);
  console.log('🚀 自动发送卦象解读请求:', autoMessage);

  // 发送到WebSocket实时对话系统
  await sendRealtimeMessage(autoMessage);
};

// 🔧 新增：简化的神谕之音请求方法（优先使用实时对话系统，降级到直接API）
const sendSimpleOracleRequest = async (content: string) => {
  if (!content.trim()) return;

  console.log('🎯 神谕之音：发送卦象解读请求:', content);

  try {
    // 🔧 开始AI思考状态
    isThinking.value = true;

    // 添加用户消息到界面
    const userMessage: OracleMessage = {
      role: 'user',
      content: content,
      timestamp: new Date()
    };

    messages.value.push(userMessage);
    scrollToBottom();
    emit('message-sent', content);

    // 🔧 关键修复：确保完整的AI配置参数
    const modelConfig = oracleConfig.selectedModel || 'lmstudio/csxl0.6';
    const aiProvider = extractAIProvider(modelConfig);
    const modelName = extractModelName(modelConfig);

    console.log('🤖 神谕之音：AI配置:', {
      原始配置: oracleConfig.selectedModel,
      提取的提供商: aiProvider,
      提取的模型名: modelName
    });

    // 🔧 策略1：优先尝试使用实时对话系统（禁用VAD）
    console.log('🚀 神谕之音：尝试启动实时对话系统（TTS-only模式）...');

    try {
      const startParams = {
        mode: 'oracle', // 🔧 关键修复：使用oracle模式，启用神谕之音专用文本分割
        synthesisMode: 'user-voice',
        disableVAD: true, // 🔧 关键：禁用VAD
        ttsConfig: {
          mode: 'user-voice',
          userVoiceProfile: oracleConfig.selectedVoice || '21',
          speed: 1.0,
          volume: 0.8,
          audioQuality: 'high'
        },
        llmConfig: {
          characterName: oracleConfig.selectedCharacter?.name || '藏识仙灵',
          systemPrompt: buildSystemPrompt(),
          temperature: oracleConfig.temperature,
          topP: oracleConfig.topP,
          topK: oracleConfig.topK,
          maxTokens: oracleConfig.maxTokens,
          useHistory: false,
          modelName: modelName,
          provider: aiProvider,
          oracle_mode: true // 🔧 添加神谕模式标记
        }
      };

      const startResponse = await API.startRealtimeDialogue(startParams);

      if (startResponse.success) {
        console.log('✅ 神谕之音：实时对话系统启动成功，发送消息...');

        // 设置WebSocket监听器
        await setupWebSocketListeners();

        // 等待连接建立
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 发送消息到实时对话系统（使用正确的API格式）
        const messageResponse = await API.sendManualMessage({
          message: content,
          settings: {
            temperature: oracleConfig.temperature,
            topP: oracleConfig.topP,
            topK: oracleConfig.topK,
            maxTokens: oracleConfig.maxTokens
          }
        });

        if (messageResponse.success) {
          console.log('✅ 神谕之音：消息已发送到实时对话系统，等待流式回复...');
          return; // 等待WebSocket回调处理
        } else {
          throw new Error('发送消息到实时对话系统失败');
        }
      } else {
        throw new Error('启动实时对话系统失败');
      }

    } catch (realtimeError) {
      console.warn('⚠️ 神谕之音：实时对话系统失败，降级到直接API调用:', realtimeError);

      // 🔧 策略2：降级到直接API调用
      console.log('📡 神谕之音：使用直接聊天API...');

      const chatResponse = await API.sendChatMessage({
        message: content,
        history: [], // 不使用历史记录
        useHistory: false,
        temperature: oracleConfig.temperature,
        topP: oracleConfig.topP,
        topK: oracleConfig.topK,
        maxTokens: oracleConfig.maxTokens,
        stream: false
      });

      console.log('📡 神谕之音：聊天API响应:', chatResponse);

      if (chatResponse.success && chatResponse.data?.response) {
        const aiResponse = chatResponse.data.response;
        console.log('✅ 神谕之音：AI回复生成成功，长度:', aiResponse.length);

        // 添加AI回复到界面
        const aiMessage: OracleMessage = {
          role: 'assistant',
          content: aiResponse,
          timestamp: new Date()
        };

        messages.value.push(aiMessage);
        scrollToBottom();

        // 🔧 关键：调用TTS生成语音
        console.log('🎵 神谕之音：开始TTS语音生成...');

        const ttsResponse = await API.textToSpeech({
          text: aiResponse,
          mode: 'user-voice',
          voiceId: oracleConfig.selectedVoice || '21',
          speed: 1.0
        });

        console.log('🎵 神谕之音：TTS响应:', ttsResponse);

        if (ttsResponse.success && ttsResponse.data?.audioUrl) {
          console.log('✅ 神谕之音：TTS生成成功，开始播放音频');

          // 播放音频
          isPlayingAudio.value = true;
          await playAudio(ttsResponse.data.audioUrl);
          isPlayingAudio.value = false;

          console.log('✅ 神谕之音：音频播放完成');

        } else {
          console.error('❌ 神谕之音：TTS生成失败:', ttsResponse.message);
          throw new Error('TTS生成失败');
        }

      } else {
        console.error('❌ 神谕之音：AI回复生成失败:', chatResponse.message);
        throw new Error('AI回复生成失败');
      }
    }

  } catch (error) {
    console.error('❌ 神谕之音：请求失败:', error);

    // 显示错误消息
    const errorMessage: OracleMessage = {
      role: 'assistant',
      content: '抱歉，神谕之音暂时无法为您解读卦象，请稍后再试。',
      timestamp: new Date()
    };

    messages.value.push(errorMessage);
    scrollToBottom();

  } finally {
    isThinking.value = false;
  }
};

// 🔧 删除：复杂的sendHexagramReadingRequest方法，已被sendSimpleOracleRequest替代

// 🔧 新增：直接TTS API调用方法（最终降级方案）
const sendDirectTTSMessage = async (content: string) => {
  try {
    console.log('🔄 使用直接TTS API调用方案');
    
    isThinking.value = true;
    
    // 第一步：调用LLM获取AI回复
    const result = await API.sendChatMessage({
      message: content,
      history: [], // 不使用历史记录
      useHistory: false,
      temperature: oracleConfig.temperature,
      topP: oracleConfig.topP,
      topK: oracleConfig.topK,
      maxTokens: oracleConfig.maxTokens,
      stream: false
    });
    
    if (result.success && result.data?.response) {
      console.log('✅ AI回复获取成功');
      
      // 🔧 新增：预处理AI回复文本用于TTS
      const originalResponse = result.data.response;
      const processedResponse = preprocessTextForTTS(originalResponse);
      
      console.log('📝 AI回复 (前200字符):', originalResponse.substring(0, 200) + (originalResponse.length > 200 ? '...' : ''));
      
      // 添加AI回复到界面
      const aiMessage: OracleMessage = {
        role: 'assistant',
        content: originalResponse, // 界面显示原文
        timestamp: new Date()
      };
      
      messages.value.push(aiMessage);
      scrollToBottom();
      emit('response-received', originalResponse);
      
      // 停止思考状态
      isThinking.value = false;
      
      // 第二步：调用TTS API生成音频
      console.log('🎵 开始TTS音频生成...');
      isPlayingAudio.value = true;
      
      const ttsResult = await API.textToSpeech({
        text: processedResponse, // 🔧 关键：使用预处理过的文本进行TTS
        mode: 'user-voice',
        voiceId: oracleConfig.selectedVoice,
        speed: 1.0
      });
      
      if (ttsResult.success && ttsResult.data?.audioUrl) {
        console.log('🎵 TTS音频生成成功:', ttsResult.data.audioUrl);
        
        // 更新消息的音频URL
        aiMessage.audioUrl = ttsResult.data.audioUrl;
        
        // 播放音频
        await playAudio(ttsResult.data.audioUrl);
        
      } else {
        console.error('❌ TTS音频生成失败:', ttsResult.message);
        isPlayingAudio.value = false;
      }
      
    } else {
      throw new Error(result.message || 'AI回复获取失败');
    }
    
  } catch (error: any) {
    console.error('❌ 直接TTS API调用失败:', error);
    isThinking.value = false;
    isPlayingAudio.value = false;
    
    // 显示错误消息
    const errorMessage: OracleMessage = {
      role: 'assistant',
      content: '抱歉，神谕系统暂时无法为您解读。请稍后再试。',
      timestamp: new Date()
    };
    
    messages.value.push(errorMessage);
    scrollToBottom();
  }
};

// 🔧 新增：提取AI提供商信息
const extractAIProvider = (modelString: string) => {
  console.log('🔍 神谕之音：提取AI提供商，输入:', modelString);
  
  if (!modelString) {
    console.log('🔍 神谕之音：模型字符串为空，使用默认lmstudio');
    return 'lmstudio';
  }
  
  // 根据模型字符串判断提供商
  const lowerModel = modelString.toLowerCase();
  
  let provider = 'lmstudio'; // 默认值
  
  if (lowerModel.includes('lmstudio') || lowerModel.includes('gemma')) {
    provider = 'lmstudio';
  } else if (lowerModel.includes('ollama')) {
    provider = 'ollama';
  } else if (lowerModel.includes('openai') || lowerModel.includes('gpt')) {
    provider = 'openai';
  }
  
  console.log('🔍 神谕之音：提取到的提供商:', provider);
  return provider;
};

// 🔧 新增：提取模型名称
const extractModelName = (modelString: string) => {
  console.log('🔍 神谕之音：提取模型名称，输入:', modelString);
  
  if (!modelString) {
    console.log('🔍 神谕之音：模型字符串为空，使用默认gemma-3-4b-it');
    return 'gemma-3-4b-it';
  }
  
  let modelName = modelString;
  
  // 如果包含冒号，提取冒号后的部分作为模型名
  if (modelString.includes(':')) {
    modelName = modelString.split(':')[1].trim();
  }
  
  console.log('🔍 神谕之音：提取到的模型名称:', modelName);
  return modelName;
};

// 🔧 删除独立的TTS WebSocket代码，改用实时对话系统的WebSocket

// 🔧 新增：直接发送LLM消息（备用方法，用于卦象解读）
const sendDirectLLMMessage = async (content: string) => {
  try {
    console.log('🔄 使用备用方法：直接LLM调用');
    
    isThinking.value = true;
    
    // 🔧 使用现有的sendChatMessage API
    const result = await API.sendChatMessage({
      message: content,
      history: [], // 不使用历史记录
      useHistory: false,
      temperature: oracleConfig.temperature,
      topP: oracleConfig.topP,
      topK: oracleConfig.topK,
      maxTokens: oracleConfig.maxTokens,
      stream: false
    });
    
    if (result.success && result.data?.response) {
      console.log('✅ 备用LLM调用成功:', result.data.response);
      
      // 添加AI回复到界面
      const aiMessage: OracleMessage = {
        role: 'assistant',
        content: result.data.response,
        timestamp: new Date()
      };
      
      messages.value.push(aiMessage);
      scrollToBottom();
      emit('response-received', result.data.response);
      
      // 🔧 关键：调用TTS生成语音
      console.log('🎵 神谕之音：开始TTS语音生成...');

      try {
        const ttsResponse = await API.textToSpeech({
          text: result.data.response,
          mode: 'user-voice',
          voiceId: oracleConfig.selectedVoice || '21',
          speed: 1.0
        });

        console.log('🎵 神谕之音：TTS响应:', ttsResponse);

        if (ttsResponse.success && ttsResponse.data?.audioUrl) {
          console.log('✅ 神谕之音：TTS生成成功，开始播放音频');

          // 播放音频
          isPlayingAudio.value = true;
          await playAudio(ttsResponse.data.audioUrl);
          isPlayingAudio.value = false;

          console.log('✅ 神谕之音：音频播放完成');

        } else {
          console.error('❌ 神谕之音：TTS生成失败:', ttsResponse.message);
        }

      } catch (ttsError) {
        console.error('❌ 神谕之音：TTS调用失败:', ttsError);
      }

      // 停止思考状态
      isThinking.value = false;
      
    } else {
      throw new Error(result.message || '备用LLM调用失败');
    }
    
  } catch (error: any) {
    console.error('❌ 备用LLM调用失败:', error);
    isThinking.value = false;
    
    // 显示错误消息
    const errorMessage: OracleMessage = {
      role: 'assistant',
      content: '抱歉，神谕系统暂时无法为您解读。请稍后再试。',
      timestamp: new Date()
    };
    
    messages.value.push(errorMessage);
    scrollToBottom();
  }
};


// 🔧 修改：实时消息发送（仅在实时对话模式下使用WebSocket）
const sendRealtimeMessage = async (content: string) => {
  if (!content.trim() || !isVoiceActive.value) return;

  // 🔧 检查WebSocket连接状态，确保能通过TTS-only系统发送
  if (connectionStatus.value !== 'connected') {
    console.log('🔄 WebSocket未连接，使用直接API调用');
    await sendDirectLLMMessage(content);
    return;
  }
  
  // 🔧 语音转录的消息即使在卦象解读阶段也通过WebSocket发送
  console.log('📡 通过WebSocket发送消息（TTS-only模式）');

  // 添加用户消息到界面
  const userMessage: OracleMessage = {
    role: 'user',
    content: content,
    timestamp: new Date()
  };
  
  messages.value.push(userMessage);
  scrollToBottom();
  
  emit('message-sent', content);

  // 开始AI思考
  isThinking.value = true;
  
  try {
    console.log('📤 发送WebSocket实时消息:', content);
    console.log('📝 用户输入详情:', {
      原始内容: content,
      字符数: content.length,
      是否包含表情: /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(content),
      是否包含特殊字符: /[^\u4e00-\u9fa5a-zA-Z0-9\s，。？！；：""''（）【】]/g.test(content)
    });
    
    // 🔧 记录完整的系统提示词上下文（给LLM的完整信息）
    const fullSystemPrompt = buildSystemPrompt();
    console.log('🧠 发送给LLM的完整上下文:');
    console.log('📜 系统提示词长度:', fullSystemPrompt.length, '字符');
    console.log('📜 系统提示词内容 (前200字符):', fullSystemPrompt.substring(0, 200) + '...');
    
    // 🔧 记录用户个人信息上下文
    console.log('👤 用户个人信息上下文:', {
      姓名: props.userInfo.name || '未提供',
      性别: props.userInfo.gender || '未提供',
      生辰: `${props.userInfo.birthYear || '?'}年${props.userInfo.birthMonth || '?'}月${props.userInfo.birthDay || '?'}日`,
      时辰: props.userInfo.birthHour || '未提供'
    });
    
    // 🔧 记录卦象信息上下文
    if (props.currentHexagram) {
      console.log('🎯 当前卦象完整信息:');
      console.log(`   卦名: ${props.currentHexagram.name}${props.currentHexagram.number ? ` (第${props.currentHexagram.number}卦)` : ''}`);
      console.log(`   卦辞: ${props.currentHexagram.meaning}`);
      if (props.currentHexagram.image) {
        console.log(`   象辞: ${props.currentHexagram.image}`);
      }
                    if (props.currentHexagram.upperTrigram && props.currentHexagram.lowerTrigram) {
         console.log(`   上卦: ${props.currentHexagram.upperTrigram.name}${props.currentHexagram.upperTrigram.meaning ? ` (${props.currentHexagram.upperTrigram.meaning})` : ''}`);
         console.log(`   下卦: ${props.currentHexagram.lowerTrigram.name}${props.currentHexagram.lowerTrigram.meaning ? ` (${props.currentHexagram.lowerTrigram.meaning})` : ''}`);
       }
      console.log(`   解释: ${props.currentHexagram.interpretation} (长度: ${props.currentHexagram.interpretation?.length || 0}字符)`);
      console.log(`   建议: ${props.currentHexagram.advice} (长度: ${props.currentHexagram.advice?.length || 0}字符)`);
      
      if (props.currentHexagram.lines && props.currentHexagram.lines.length > 0) {
        console.log(`   六爻详解 (${props.currentHexagram.lines.length}条):`);
        props.currentHexagram.lines.forEach((line, index) => {
          const positions = ['初', '二', '三', '四', '五', '上'];
          const lineType = line.type === 'yang' || line.type === 'changing-yang' ? '九' : '六';
          const position = `${positions[index]}${lineType}`;
          console.log(`     ${position}: ${line.text}${line.meaning ? ` (${line.meaning})` : ''}`);
        });
      } else {
        console.log('   六爻详解: 无爻辞信息');
      }
    } else {
      console.log('🎯 无卦象信息');
    }
    
    // 🔧 记录占卜主题上下文
    if (props.selectedTopic) {
      console.log('🎯 占卜主题上下文:', {
        主题ID: props.selectedTopic,
        主题描述: getTopicInfo(props.selectedTopic)
      });
    } else {
      console.log('🎯 无占卜主题');
    }
    
    // 🔧 记录天人感应信息
    if (props.tianrenShenying && props.tianrenShenying.trim()) {
      console.log('🌟 天人感应信息:', {
        长度: props.tianrenShenying.length,
        内容预览: props.tianrenShenying.substring(0, 100) + (props.tianrenShenying.length > 100 ? '...' : '')
      });
    } else {
      console.log('🌟 无天人感应信息');
    }
    
    // 🔧 记录对话历史上下文
    console.log('💬 对话历史上下文:', {
      历史消息数: messages.value.length,
      用户消息数: messages.value.filter(m => m.role === 'user').length,
      AI消息数: messages.value.filter(m => m.role === 'assistant').length,
      当前轮次: currentDialogueRound.value,
      最大轮次: maxDialogueRounds.value
    });
    
    // 🔧 记录LLM配置参数
    const currentLLMParams = getCurrentLLMParams();
    console.log('⚙️ LLM配置参数:', {
      模型: oracleConfig.selectedModel,
      AI提供商: extractAIProvider(oracleConfig.selectedModel),
      模型名称: extractModelName(oracleConfig.selectedModel),
      参数设置: currentLLMParams,
      使用预设: selectedPreset.value || '无',
      角色名称: oracleConfig.selectedCharacter?.name || '默认',
      音色ID: oracleConfig.selectedVoice
    });
    
    // 🔧 关键修改：直接调用sendManualMessage API发送消息给TTS-only系统
    const llmParams = getCurrentLLMParams();
    
    console.log('📤 神谕之音：调用sendManualMessage API发送消息');
    console.log('📜 用户提问:', content);
    console.log('⚙️ LLM参数:', llmParams);
    
    const messageResult = await API.sendManualMessage({
      message: content,
      settings: llmParams
    });
    
    console.log('📤 神谕之音：sendManualMessage API返回结果:', messageResult);
    
    if (messageResult.success) {
      console.log('✅ 神谕之音：消息已发送到TTS-only系统，等待AI回复和流式TTS音频...');
      
      // 🔧 使用realtimeStore添加本地消息记录
      const simulatedTranscriptMessage = {
        id: `transcript-${Date.now()}`,
        role: 'user' as const,
        content: content,
        timestamp: new Date(),
        confidence: 0.95
      };
      
      realtimeStore.addMessage(simulatedTranscriptMessage);
      console.log('📡 消息ID:', simulatedTranscriptMessage.id);
      
    } else {
      console.error('❌ 神谕之音：sendManualMessage调用失败:', messageResult.message);
      
      // 🔧 如果sendManualMessage失败，说明TTS-only系统可能没有正确启动
      console.log('🔄 神谕之音：TTS-only系统可能未正确启动，降级到直接API调用');
      throw new Error(messageResult.message || 'sendManualMessage调用失败');
    }
    
    // 🔧 增加对话轮次（仅在用户发送消息时增加）
    currentDialogueRound.value++;
    console.log('🔢 对话轮次已更新:', currentDialogueRound.value, '/', maxDialogueRounds.value);
    
    // AI回复将通过WebSocket事件监听器处理
    
  } catch (error: any) {
    console.error('❌ WebSocket消息发送失败:', error);
    
    // 🔧 备用方法：使用直接API调用
    console.log('🔄 WebSocket失败，改用直接API调用');
    await sendDirectLLMMessage(content);
  }
};

// 🔧 新增：检查卦象解读是否完成，决定后续流程
const checkHexagramReadingComplete = () => {
  console.log('🔍 检查卦象解读完成状态...');
  
  // 🔧 检查音频是否还在播放或等待中
  if (isPlayingAudio.value) {
    console.log('🎵 音频还在播放中，延迟检查');
    createTimer(() => checkHexagramReadingComplete(), 2000, undefined, '卦象解读完成检查-音频播放中', componentName);
    return;
  }
  
  // 🔧 检查是否还在等待流式音频片段
  if (isWaitingForAudioChunks.value) {
    console.log('⏳ 还在等待音频片段，延迟检查');
    createTimer(() => checkHexagramReadingComplete(), 3000, undefined, '卦象解读完成检查-等待音频片段', componentName);
    return;
  }
  
  console.log('🎉 卦象解读音频播放完成，检查是否需要开启实时互动');
  
  // 🔧 标记卦象解读阶段结束
  isInHexagramReadingPhase.value = false;
  currentDialogueRound.value++; // 增加对话轮次计数
  
  // 🔧 关键：根据天人感应信息决定是否开启实时对话
  if (props.tianrenShenying && props.tianrenShenying.trim()) {
    console.log('🌟 检测到天人感应信息，开启实时互动对话');
    
    // 🎯 重置并开启实时互动模式
    maxDialogueRounds.value = 3; // 允许卦象解读(1轮) + 实时对话(2轮)
    isAutoDialogueActive.value = false; // 关闭自动模式
    
    // 🔧 关键：切换到实时语音对话模式（启用VAD语音检测）
    switchToRealtimeMode();
    
    console.log('✨ 实时互动对话已开启，用户可以语音提问');
    console.log('📊 新的对话配置:', {
      maxRounds: maxDialogueRounds.value, 
      currentRound: currentDialogueRound.value,
      autoMode: isAutoDialogueActive.value,
      tianrenInfo: '已检测'
    });
      
    } else {
    console.log('📝 无天人感应信息，自动结束神谕对话');
      
    // 🎯 延迟3秒后自动结束对话，给用户时间查看结果
      createTimer(() => {
      console.log('🚪 自动关闭神谕之门，返回原始页面');
      stopVoiceOracle();
    }, 3000, undefined, '自动结束神谕对话', componentName);
  }
};

// 🔧 新增：切换到实时语音对话模式
const switchToRealtimeMode = async () => {
  try {
    console.log('🔄 切换到实时语音对话模式...');
    
    // 🔧 第一步：先确保WebSocket连接存在
    if (connectionStatus.value !== 'connected') {
      console.log('🚀 WebSocket未连接，重新启动实时对话系统...');
      
      // 🔧 重新启动实时对话，这次启用VAD语音检测
      const response = await API.startRealtimeDialogue({
        mode: 'realtime', // 🎯 改为实时模式，启用VAD
        synthesisMode: 'user-voice',
        disableVAD: false, // 🔧 关键：启用VAD
        ttsConfig: {
          mode: 'user-voice',
          userVoiceProfile: oracleConfig.selectedVoice,
          speed: 1.0,
          volume: 0.8,
          audioQuality: 'high'
        },
        llmConfig: {
          characterName: oracleConfig.selectedCharacter?.name || '神谕大师',
          systemPrompt: buildSystemPrompt(),
          temperature: oracleConfig.temperature,
          topP: oracleConfig.topP,
          topK: oracleConfig.topK,
          maxTokens: oracleConfig.maxTokens,
          useHistory: true,
          // 🔧 关键修复：添加完整的AI配置参数
          provider: extractAIProvider(oracleConfig.selectedModel),
          modelName: extractModelName(oracleConfig.selectedModel)
        }
      });
      
      if (response.success) {
        console.log('✅ 实时语音对话系统重新启动成功');
        
        // 🔧 设置WebSocket事件监听
        setupWebSocketListeners();
        
        // 🔧 更新连接状态
        connectionStatus.value = 'connected';
      } else {
        console.error('❌ 重新启动实时对话失败:', response.message);
        throw new Error(response.message || '重新启动实时对话失败');
      }
    } else {
      console.log('📞 WebSocket已连接，直接启用VAD监听...');
      
      // 🔧 第二步：直接调用enable-vad API启用VAD监听
      const vadResponse = await API.enableVADListening();
      
      if (vadResponse.success) {
        console.log('✅ VAD监听已启用');
      } else {
        console.error('❌ 启用VAD失败:', vadResponse.message);
        throw new Error(vadResponse.message || 'VAD启用失败');
      }
    }
    
    // 📢 向用户显示提示消息
    const hintMessage: OracleMessage = {
      role: 'assistant',
      content: '🎤 实时对话已开启，您可以继续语音提问，或在展开对话中手动输入问题。',
      timestamp: new Date()
    };
    
    messages.value.push(hintMessage);
    scrollToBottom();
    
    console.log('✅ 成功切换到实时语音对话模式');
    
  } catch (error) {
    console.error('❌ 切换实时模式出错:', error);
    
    // 📢 显示错误提示
    const errorMessage: OracleMessage = {
      role: 'assistant',
      content: '抱歉，无法启动实时对话功能。神谕之门即将关闭。',
      timestamp: new Date()
    };
    
    messages.value.push(errorMessage);
    scrollToBottom();
    
    // 如果启动失败，自动关闭神谕对话
    createTimer(() => {
      console.log('🚪 实时模式启动出错，自动关闭神谕对话');
      stopVoiceOracle();
    }, 3000, undefined, '实时模式启动失败自动关闭', componentName);
  }
};

// 🔧 新增：检查是否需要结束对话（等待TTS完成后再判断）
const checkAndEndDialogue = () => {
  // 🔧 新增：检查音频是否还在播放或等待中
  if (isPlayingAudio.value) {
    console.log('🎵 音频还在播放中，延迟结束对话检查');
    createTimer(() => checkAndEndDialogue(), 1000, undefined, '结束对话检查-音频播放中', componentName);
    return;
  }
  
  // 🔧 新增：检查是否还在等待流式音频片段
  if (isWaitingForAudioChunks.value) {
    console.log('⏳ 还在等待音频片段，延迟结束对话检查');
    createTimer(() => checkAndEndDialogue(), 2000, undefined, '结束对话检查-等待音频片段', componentName);
    return;
  }
  
  // 🔧 关键修复：TTS完成后，根据天人感应信息决定是否开启实时对话
  if (currentDialogueRound.value >= maxDialogueRounds.value) {
    console.log('🎉 所有对话轮次已完成，准备结束神谕对话');
    
    // 🎯 延迟结束，给用户时间查看最后的回复
    createTimer(() => {
      console.log('🚪 对话已完成，自动关闭神谕之门');
      stopVoiceOracle();
    }, 5000, undefined, '对话完成自动关闭', componentName);
    
  } else {
    console.log('📊 对话继续，当前轮次:', currentDialogueRound.value, '/', maxDialogueRounds.value);
  }
};

// ===================== WebSocket事件处理 =====================

// 🔧 防止重复注册的标志
let isWebSocketListenersSetup = false

// 🔧 修复：增强WebSocket事件监听器设置，支持重新激活
const setupWebSocketListeners = () => {
  if (isWebSocketListenersSetup) {
    console.log('⚠️ 神谕之音：WebSocket事件监听器已设置，跳过重复注册')
    return
  }
  
  console.log('🔗 神谕之音：设置/重新激活WebSocket连接...');

  // 📊 详细的WebSocket状态诊断
  console.log('🔍 神谕之音：WebSocket诊断信息', {
    实例存在: !!websocket,
    实例类型: typeof websocket,
    是否已连接: websocket.isConnected.value,
    连接ID: websocket.connectionId.value
  });

  // 🔧 关键修复：确保WebSocket连接已建立（参考实时对话模式）
  if (!websocket.isConnected.value) {
    console.log('🔌 神谕之音：WebSocket未连接，启动连接...');
    websocket.connect().then(() => {
      console.log('✅ 神谕之音：WebSocket连接成功');
    }).catch(error => {
      console.error('❌ 神谕之音：WebSocket连接失败:', error);
    });
  } else {
    console.log('✅ 神谕之音：WebSocket已连接');
  }
  
  // 🔧 关键修复：检查是否需要重新注册事件处理器
  const needsReregistration = Object.keys(websocket.getStatus().localHandlers || {}).length === 0;
  if (needsReregistration) {
    console.log('🔧 神谕之音：检测到事件处理器缺失，重新注册...');
    websocket.registerHandlers({
      onLLMResponse: (data: any) => {
        console.log('🤖 神谕之音：重新注册-收到LLM回复事件', data)
        handleLLMResponse(data)
      },
      onTTSAudio: (data: any) => {
        console.log('🎵 神谕之音：重新注册-收到TTS音频事件', data)
        handleTTSAudio(data)
      },
      onTTSError: (error: any) => {
        console.error('🎵 神谕之音：重新注册-收到TTS错误事件', error)
        handleTTSError(error)
      },
      onTranscription: (data: any) => {
        console.log('🎤 神谕之音：重新注册-收到转录事件', data)
        handleTranscriptionUpdate(data)
      },
      onStatusUpdate: (data: any) => {
        console.log('📊 神谕之音：重新注册-收到状态更新事件', data)
        handleStatusUpdate(data)
      },
      onConnected: (data: any) => {
        console.log('✅ 神谕之音：重新注册-连接成功', data)
        connectionStatus.value = 'connected'
      },
      onDisconnected: (data: any) => {
        console.log('❌ 神谕之音：重新注册-连接断开', data)
        connectionStatus.value = 'disconnected'
      },
      onError: (error: any) => {
        console.error('❌ 神谕之音：重新注册-错误', error)
        handleWebSocketError(error)
      }
    });
  } else {
    console.log('🔧 神谕之音：事件处理器已存在，无需重新注册');
  }
  
  // 🔧 关键修复：确保WebSocket连接已建立
  if (!websocket.isConnected.value) {
    console.log('🔌 神谕之音：WebSocket未连接，启动连接...');
    websocket.connect().then(() => {
      console.log('✅ 神谕之音：WebSocket连接成功');
      connectionStatus.value = 'connected';
    }).catch(error => {
      console.error('❌ 神谕之音：WebSocket连接失败:', error);
      connectionStatus.value = 'error';
    });
  } else {
    console.log('✅ 神谕之音：WebSocket已连接');
    connectionStatus.value = 'connected';
  }
  
  // 🔧 关键修复：标记事件监听器已设置
  isWebSocketListenersSetup = true
  console.log('✅ 神谕之音：WebSocket事件监听器设置完成')
  
  // 通过realtimeStore监听消息变化（作为备用）
  const unsubscribe = realtimeStore.$subscribe((mutation, state) => {
    const conversationHistory = state.conversationHistory;
    if (conversationHistory && conversationHistory.length > 0) {
      const latestMessage = conversationHistory[conversationHistory.length - 1];
      
      // 处理AI回复消息
      if (latestMessage.role === 'assistant' && latestMessage.content) {
        console.log('🤖 收到AI回复(store):', latestMessage.content);
        
        // 添加到神谕消息列表
        const aiMessage: OracleMessage = {
          role: 'assistant',
          content: latestMessage.content,
          timestamp: new Date(),
          audioUrl: latestMessage.audioUrl
        };
        
        // 更新界面 - 检查是否已存在相同内容的消息
        const existingIndex = messages.value.findIndex((m: OracleMessage) => 
          m.role === 'assistant' && m.content === latestMessage.content
        );
        
        if (existingIndex === -1) {
          messages.value.push(aiMessage);
          scrollToBottom();
          emit('response-received', latestMessage.content);
          
          // 停止思考状态
          isThinking.value = false;
          
          // 如果有音频URL，自动播放
          if (latestMessage.audioUrl) {
            playAudio(latestMessage.audioUrl);
          }
          
          // 🔧 新增：AI回复完成后检查是否需要结束对话（store监听的备用处理）
          createTimer(() => {
            checkAndEndDialogue();
          }, 3000, undefined, 'AI回复完成后检查对话结束', componentName); // 延迟3秒后检查，给音频播放留出更多时间
        }
      }
    }
  });
  
  // 保存取消订阅函数以便清理
  (setupWebSocketListeners as any)._unsubscribe = unsubscribe;
  
  console.log('✅ 神谕之音：WebSocket事件监听器准备就绪');
  
  // 🔧 添加监听器状态验证和调试
  createTimer(() => {
    console.log('🔍 神谕之音：监听器设置后状态验证', {
      WebSocket实例: !!websocket,
      是否已连接: websocket.isConnected.value,
      连接ID: websocket.connectionId.value,
      llm_response监听器: '已设置',
      tts_audio监听器: '已设置'
    });
    
    // 🔧 新增：测试事件监听器是否正常工作
    console.log('🧪 测试WebSocket事件监听器...');
    
    // 发送测试事件
    createTimer(() => {
      websocket.send('test_神谕之音', { test: '监听器测试', timestamp: new Date() });
    }, 100, undefined, 'WebSocket测试事件', componentName);
    
  }, 1000, undefined, 'WebSocket监听器状态验证', componentName);
};

// 🔧 新增：WebSocket事件处理器（参考RealtimeView）
// 🔧 新增：WebSocket事件测试函数
const testWebSocketEvents = () => {
  console.log('🧪 神谕之音：测试WebSocket事件...');
  
  if (!websocket.isConnected.value) {
    console.error('❌ WebSocket未连接，无法测试事件');
    return;
  }
  
  // 发送测试消息
  websocket.send('test_event', {
    message: '神谕之音测试消息',
    timestamp: new Date().toISOString()
  });
  
  console.log('📤 已发送测试事件到WebSocket服务器');
};

// 🔧 新增：TTS文本预处理函数，修正发音问题
const preprocessTextForTTS = (text: string) => {
  if (!text) return text;
  
  // 修正常见的发音问题
  let processedText = text
    .replace(/神机秒卦/g, '神机妙卦')     // 修正 "秒" 被读成 "S" 的问题
    .replace(/(\d+)月/g, '$1月份')        // 改善月份发音
    .replace(/(\d+)日/g, '$1号')          // 改善日期发音
    .replace(/占卜/g, '占卜')             // 确保正确发音
    .replace(/([A-Z]+)/g, (match) => {   // 处理英文缩写，添加空格
      return match.split('').join(' ');
    });
  
  console.log('🗣️ TTS文本预处理完成');
  if (text !== processedText) {
    console.log('🔧 文本已修正:', {
      原文: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      修正后: processedText.substring(0, 100) + (processedText.length > 100 ? '...' : '')
    });
  }
  
  return processedText;
};

const handleLLMResponse = (data: any) => {
  console.log('🤖 神谕之音：收到LLM回复事件', {
    hasData: !!data,
    dataType: typeof data,
    hasResponse: !!(data && data.response),
    thinking: !!(data && data.thinking),
    responseLength: data && data.response ? data.response.length : 0,
    character: data && data.character_name ? data.character_name : 'unknown',
    timestamp: new Date().toISOString(),
    allKeys: data ? Object.keys(data) : [],
    fullData: data // 🔧 新增：完整数据用于调试
  });
  
  // 🔧 新增：详细的数据格式适配
  if (!data) {
    console.error('❌ [handleLLMResponse] 接收到空数据');
    return;
  }
  
  // 🔧 兼容多种可能的数据格式
  const actualData = data.payload || data.data || data;
  const response = actualData.response || actualData.content || actualData.text;
  const thinking = actualData.thinking || actualData.isThinking || false;
  
  console.log('🔍 [handleLLMResponse] 数据格式适配结果:', {
    原始数据类型: typeof data,
    实际数据类型: typeof actualData,
    回复内容: response ? `${response.substring(0, 100)}...` : 'null',
    思考状态: thinking
  });
  
  // 🔧 使用适配后的数据进行处理
  if (thinking && !response) {
    // AI正在思考
    console.log('🧠 AI正在思考...');
    isThinking.value = true;
  } else if (response) {
    // AI回复完成
    console.log('✅ 收到AI回复:', response.substring(0, 100) + (response.length > 100 ? '...' : ''));
    isThinking.value = false;
    
    // 🔧 新增：预处理AI回复文本，修正TTS发音问题
    const originalResponse = response;
    const processedResponse = preprocessTextForTTS(originalResponse);
    
    const aiMessage: OracleMessage = {
      role: 'assistant',
      content: originalResponse, // 界面显示原文
      timestamp: new Date()
    };
    
    // 检查是否已存在相同内容的消息
    const existingIndex = messages.value.findIndex((m: OracleMessage) => 
      m.role === 'assistant' && m.content === originalResponse
    );
    
    if (existingIndex === -1) {
      console.log('➕ 添加AI回复到消息列表');
      messages.value.push(aiMessage);
      scrollToBottom();
      emit('response-received', originalResponse);
      
      // 🔧 修改：不在这里立即结束对话，等待音频播放完成
      console.log('🎵 AI回复收到，等待音频播放完成后结束对话');
    } else {
      console.log('⏭️ 跳过重复的AI回复');
    }
  } else {
    console.error('⚠️ [handleLLMResponse] 收到无效的LLM回复数据，详细调试信息:', {
      原始数据: data,
      适配后数据: actualData,
      提取的回复: response,
      提取的思考状态: thinking,
      数据结构: JSON.stringify(data, null, 2)
    });
  }
};

const handleTTSAudio = (data: any) => {
  console.log('🎵 [神谕之音] 收到TTS音频回调', {
    hasData: !!data,
    dataType: typeof data,
    hasAudioUrl: !!(data && (data.audioUrl || data.audio_url)),
    audioUrlLength: data && (data.audioUrl || data.audio_url) ? (data.audioUrl || data.audio_url).length : 0,
    isStreaming: !!(data && (data.isStreaming || data.is_streaming)),
    chunkIndex: data && (data.chunkIndex || data.chunk_index),
    totalChunks: data && (data.totalChunks || data.total_chunks)
  });
  
  // 🔧 关键修复：添加页面活跃性和DOM安全检查
  if (!websocket.isConnected.value) {
    console.warn('⚠️ [神谕之音] WebSocket未连接，跳过TTS音频处理')
    return
  }

  // 🔧 页面切换修复：确保音频播放器存在且页面处于活跃状态
  if (!audioPlayerRef.value) {
    console.warn('⚠️ [神谕之音] 音频播放器不存在，可能页面已切换，跳过音频处理')
    return
  }
  
  // 验证audio元素引用是否有效
  if (!audioPlayerRef.value) {
    console.error('❌ [神谕之音] audioPlayerRef未找到，无法播放TTS音频')
    return
  }
  
  // 验证当前页面是否为活跃页面
  const wsStatus = websocket.getStatus()
  if (wsStatus.pageName !== '周易测算') {
    console.warn('⚠️ [神谕之音] 当前页面非活跃状态，跳过TTS音频处理. 活跃页面:', wsStatus.pageName)
    return
  }
  
  // 🔧 关键修复：立即检查数据有效性并记录详细信息
  if (!data) {
    console.error('❌ [神谕之音] TTS音频数据为空');
    return;
  }
  
  console.log('🔍 神谕之音：TTS音频数据详细分析:', {
    data类型: typeof data,
    data内容: data,
    audioUrl字段: data.audioUrl,
    audio_url字段: data.audio_url,
    payload字段: data.payload,
    payload_audioUrl: data.payload?.audioUrl,
    payload_audio_url: data.payload?.audio_url,
    所有字段: Object.keys(data),
    payload所有字段: data.payload ? Object.keys(data.payload) : null
  });

  // 🔧 修复：尝试从多个可能的位置提取audioUrl
  let audioUrl = null;
  if (data.audioUrl) {
    audioUrl = data.audioUrl;
    console.log('✅ 从data.audioUrl获取音频URL');
  } else if (data.audio_url) {
    audioUrl = data.audio_url;
    console.log('✅ 从data.audio_url获取音频URL');
  } else if (data.payload?.audioUrl) {
    audioUrl = data.payload.audioUrl;
    console.log('✅ 从data.payload.audioUrl获取音频URL');
  } else if (data.payload?.audio_url) {
    audioUrl = data.payload.audio_url;
    console.log('✅ 从data.payload.audio_url获取音频URL');
  } else {
    console.error('❌ 无法从任何位置找到音频URL:', {
      检查的字段: ['audioUrl', 'audio_url', 'payload.audioUrl', 'payload.audio_url'],
      数据结构: JSON.stringify(data, null, 2)
    });
    return;
  }

  if (audioUrl) {
    const shortUrl = audioUrl.length > 80 ? audioUrl.substring(0, 80) + '...' : audioUrl;
    console.log('🔊 设置音频URL:', shortUrl);
    
    // 🔧 关键修复：立即验证音频播放器状态
    console.log('🎵 神谕之音：音频播放器状态检查', {
      音频播放器存在: !!audioPlayerRef.value,
      播放器类型: audioPlayerRef.value?.constructor.name,
      播放器状态: audioPlayerRef.value?.readyState,
      当前src: audioPlayerRef.value?.src,
      当前音量: audioPlayerRef.value?.volume
    });
    
    // 🔧 修复：只有在没有音频播放的时候才立即播放，否则排队等待
    if (audioPlayerRef.value) {
      if (!isPlayingAudio.value) {
        console.log('🧪 神谕之音：立即播放音频（无其他音频）');
        testDirectAudioPlay(audioUrl);
      } else {
        console.log('🔄 神谕之音：有音频正在播放，将当前音频加入队列');
        // 不立即播放，让现有的播放流程继续
      }
    } else {
      console.error('❌ 神谕之音：音频播放器元素不存在，无法播放音频');
    }
    
    // 🔧 重要修复：检查是否为新对话的第一个音频片段，如果是则重置状态
    const isNewConversation = audioChunks.value.length === 0 && !isPlayingAudio.value;
    if (isNewConversation) {
      console.log('🆕 检测到新对话，重置音频状态');
      currentChunkIndex.value = 0;
      isWaitingForAudioChunks.value = false;
      if (audioChunkTimeout.value) {
        clearTimeout(audioChunkTimeout.value);
        audioChunkTimeout.value = null;
      }
    }
    
    // 🔧 新增：流式音频检测和处理
    const chunkIndex = data.chunkIndex || data.chunk_index || audioChunks.value.length;
    const totalChunks = data.totalChunks || data.total_chunks || -1; // -1表示未知总数（流式）
    const isLastChunk = data.isLastChunk || data.is_last_chunk || false;
    const isStreaming = data.isStreaming || data.is_streaming || totalChunks === -1;
    const isFirstChunk = chunkIndex === 0;
    
    console.log(`📦 音频片段: #${chunkIndex}, 总数=${totalChunks === -1 ? '流式' : totalChunks}, 最后=${isLastChunk}`);
    
    // 🔧 关键修复：流式音频处理
    if (isStreaming || totalChunks === -1) {
      console.log('🌊 检测到流式音频，使用队列模式');
      
      // 添加到音频队列
      audioChunks.value.push(audioUrl);
      console.log(`📦 音频队列: 片段${chunkIndex}->位置${audioChunks.value.length}, 总队列=${audioChunks.value.length}`);
      
      // 🔧 重要修复：只为第一个片段设置监听器和开始播放
      if (audioChunks.value.length === 1 && !isPlayingAudio.value) {
        console.log('🚀 流式音频：开始播放首片段');
        currentChunkIndex.value = 0;
        
        // 确保音频元素存在
        if (audioPlayerRef.value) {
          // 清理之前的监听器
          audioPlayerRef.value.removeEventListener('play', audioPlayHandler);
          audioPlayerRef.value.removeEventListener('ended', audioEndedHandler);
          audioPlayerRef.value.removeEventListener('error', audioErrorHandler);
          audioPlayerRef.value.removeEventListener('canplay', audioCanPlayHandler);
          
          // 重新绑定监听器
          audioPlayerRef.value.addEventListener('play', audioPlayHandler);
          audioPlayerRef.value.addEventListener('ended', audioEndedHandler);
          audioPlayerRef.value.addEventListener('error', audioErrorHandler);
          audioPlayerRef.value.addEventListener('canplay', audioCanPlayHandler);
          
          console.log('✅ 流式音频监听器已设置');
        }
        
        playAudioChunkWithQuickTransition(audioUrl, 0, audioChunks.value.length);
      } else if (audioChunks.value.length > 1) {
        console.log(`📥 收到第${audioChunks.value.length}个片段，等待当前播放完成`);
        // 🔧 重要修复：不立即播放，等待当前片段播放完成后由audioEndedHandler触发下一个
      }
      
          // 🔧 关键修复：动态设置等待超时时间，长文本给更多时间
    // 🔧 清理之前的音频块超时定时器
    if (audioChunkTimeout.value) {
      clearTimer(audioChunkTimeout.value);
    }
    
    // 🎯 动态超时：根据已收到的音频片段数量调整等待时间
    const dynamicTimeout = Math.max(15000, audioChunks.value.length * 3000); // 最少15秒，每个片段增加3秒
    console.log(`⏰ 动态超时: ${dynamicTimeout/1000}秒 (${audioChunks.value.length}个片段)`);
    
    audioChunkTimeout.value = createTimer(() => {
      console.log(`⏰ 音频片段等待超时 (${dynamicTimeout/1000}秒)，检查播放状态`);
      console.log(`📊 超时检查: 当前播放=${isPlayingAudio.value}, 等待状态=${isWaitingForAudioChunks.value}, 队列长度=${audioChunks.value.length}`);
      
      // 🔧 重要修复：只有在确实没有音频在播放时才结束等待
      if (!isPlayingAudio.value && audioChunks.value.length > 0) {
        console.log('🏁 音频播放空闲且有片段，认为流式播放结束');
        isWaitingForAudioChunks.value = false;
        createTimer(() => handleAllAudioComplete(), 500, undefined, '音频播放完成回调', componentName);
      } else if (isPlayingAudio.value) {
        console.log('🎵 音频正在播放中，延长等待时间');
        // 如果还在播放，延长等待时间
        audioChunkTimeout.value = createTimer(() => {
          isWaitingForAudioChunks.value = false;
          if (!isPlayingAudio.value) {
            createTimer(() => handleAllAudioComplete(), 500, undefined, '延长等待后音频完成回调', componentName);
          }
        }, 10000, undefined, '音频播放延长等待', componentName); // 再等待10秒
      } else {
        console.log('⚠️ 无音频片段或播放状态异常，直接结束');
        isWaitingForAudioChunks.value = false;
      }
    }, dynamicTimeout, 'audioChunkWaitTimeout', '音频片段等待超时', componentName);
      
      isWaitingForAudioChunks.value = true;
      
    } else {
      // 🔧 传统分块音频处理
      if (totalChunks > 1) {
        audioChunks.value[chunkIndex] = audioUrl;
        console.log(`📦 存储音频片段 ${chunkIndex + 1}/${totalChunks}`);
        
        if (chunkIndex === 0) {
          console.log('🚀 立即播放第一个音频片段');
          playAudioChunkWithQuickTransition(audioUrl, 0, totalChunks);
        }
        
        if (isLastChunk) {
          console.log('✅ 收到最后一个音频片段，准备连续播放');
          isWaitingForAudioChunks.value = false;
          // 🔧 清理音频块超时定时器
          if (audioChunkTimeout.value) {
            clearTimer(audioChunkTimeout.value);
            audioChunkTimeout.value = null;
          }
          
          // ⚡ 预估最后片段播放时间，提前通知后端准备恢复VAD（参考RealtimeView）
          const estimatedDuration = data.duration || 3; // 默认3秒
          createTimer(() => {
            console.log('⚡ 预计音频即将结束，准备恢复VAD监听');
            notifyAudioPlaybackNearEnd();
          }, Math.max(0, (estimatedDuration - 0.5) * 1000), undefined, 'VAD恢复通知', componentName); // 提前0.5秒通知
        }
      } else {
        // 🎯 单个音频直接播放
        console.log('🎵 单个音频直接播放');
        audioChunks.value = [audioUrl]; // 即使是单个音频也放入队列便于统一处理
        currentChunkIndex.value = 0;
        playAudioChunkWithQuickTransition(audioUrl, 0, 1);
      }
    }
    
    // 🎯 关键：只为第一个片段设置事件监听器，避免重复设置（参考RealtimeView）
    if (isFirstChunk && audioPlayerRef.value) {
      // 🔧 重要修复：确保只在第一个片段时设置监听器，后续片段复用
      console.log('🎵 为第一个音频片段设置事件监听器');
      
      // 移除之前可能存在的监听器，防止重复绑定
      audioPlayerRef.value.removeEventListener('play', audioPlayHandler);
      audioPlayerRef.value.removeEventListener('ended', audioEndedHandler);
      audioPlayerRef.value.removeEventListener('error', audioErrorHandler);
      audioPlayerRef.value.removeEventListener('canplay', audioCanPlayHandler);
      
      // 重新绑定监听器
      audioPlayerRef.value.addEventListener('play', audioPlayHandler);
      audioPlayerRef.value.addEventListener('ended', audioEndedHandler);
      audioPlayerRef.value.addEventListener('error', audioErrorHandler);
      audioPlayerRef.value.addEventListener('canplay', audioCanPlayHandler);
      
      console.log('✅ 音频事件监听器已设置（首个片段）');
    }
    
    // 更新最后一条AI消息的音频URL
    const aiMessagesForAudio = messages.value.filter((m: OracleMessage) => m.role === 'assistant');
    const lastAIMessageForAudio = aiMessagesForAudio[aiMessagesForAudio.length - 1];
    if (lastAIMessageForAudio) {
      lastAIMessageForAudio.audioUrl = audioUrl;
    }
  }
};

const handleTTSError = (data: any) => {
  console.error('🎵 TTS生成错误:', data);
  isThinking.value = false;
  
  // 添加错误提示
  const errorMessage: OracleMessage = {
    role: 'assistant',
    content: '抱歉，语音生成时遇到了问题，请稍后再试。',
    timestamp: new Date()
  };
  
  messages.value.push(errorMessage);
  scrollToBottom();
};

const handleStatusUpdate = (data: any) => {
  if (data.connectionStatus) {
    // 更新连接状态
    switch (data.connectionStatus) {
      case 'connected':
        connectionStatus.value = 'connected';
        break;
      case 'disconnected':
        connectionStatus.value = 'disconnected';
        break;
      case 'error':
        connectionStatus.value = 'error';
        break;
    }
  }
};

const handleTranscriptionUpdate = (data: any) => {
  console.log('🎤 收到转录更新:', {
    hasCurrent: !!data.currentTranscript,
    hasFinal: !!data.finalTranscript,
    confidence: data.confidence
  });

  if (data.currentTranscript) {
    isListening.value = true;
    currentTranscript.value = data.currentTranscript;
    console.log('🎙️ 当前转录内容:', data.currentTranscript);
  }
  
  if (data.finalTranscript) {
    isListening.value = false;
    currentTranscript.value = '';
    
    console.log('🎯 最终转录结果:', data.finalTranscript);
    
    // 🔧 修复：改进重复检测逻辑，只检查最近30秒内的相同内容
    const now = new Date().getTime();
    const isDuplicate = messages.value.some((msg: OracleMessage) => 
      msg.role === 'user' && 
      msg.content.trim() === data.finalTranscript.trim() &&
      Math.abs(now - new Date(msg.timestamp).getTime()) < 30000 // 30秒内重复
    );
    
    // 🔧 修复：只跳过明确重复的结果，对于新的有效转录总是处理
    if (!isDuplicate && data.finalTranscript.trim().length > 0) {
      console.log('📝 发送最终转录结果 (长度:', data.finalTranscript.trim().length, '字符)');
      sendRealtimeMessage(data.finalTranscript);
    } else if (isDuplicate) {
      console.log('⏭️ 跳过重复的转录结果 (30秒内已处理相同内容)');
    } else {
      console.log('⏭️ 跳过空的转录结果');
    }
  }
};

const handleWebSocketError = (error: any) => {
  console.error('WebSocket错误:', error);
  connectionStatus.value = 'error';
  isThinking.value = false;
};

// 🔧 新增：轻量级WebSocket重置，保持连接但重置对话相关状态
const lightWeightWebSocketReset = () => {
  console.log('🔄 执行轻量级WebSocket重置...');
  
  // 只重置对话相关的状态标志，不断开连接
  isWebSocketListenersSetup = false;
  connectionStatus.value = 'disconnected';
  
  // 重置对话相关状态，但保持WebSocket连接和事件处理器
  console.log('✅ 轻量级WebSocket重置完成，连接和事件处理器保持不变');
};

// 🔧 修改：正确的清理方法
const cleanupWebSocketListeners = () => {
  console.log('🧹 清理WebSocket事件监听器');
  
  // 🔧 使用新的WebSocket管理器断开连接
  websocket.disconnect();
  
  // 🔧 关键修复：重置事件监听器标志，允许下次重新注册
  isWebSocketListenersSetup = false
  
  console.log('✅ 神谕之音WebSocket监听器已清理');
  
  // 取消realtimeStore订阅
  if ((setupWebSocketListeners as any)._unsubscribe) {
    (setupWebSocketListeners as any)._unsubscribe();
    (setupWebSocketListeners as any)._unsubscribe = null;
  }
};

// ===================== 音频播放相关状态 =====================
const audioChunks = ref<string[]>([]);
const currentChunkIndex = ref(0);
const isWaitingForAudioChunks = ref(false);
const audioChunkTimeout = ref<string | null>(null); // 🔧 修改为字符串类型，存储定时器ID
const audioPlayStartTime = ref(0);
const wasAudioPlayingBeforePause = ref(false); // 标记暂停前是否在播放

// ===================== 暴露给父组件的方法和引用 =====================
defineExpose({
  audioPlayerRef,
  startVoiceOracle,
  stopVoiceOracle,
  resetOracleState: forceResetOracle, // 🔧 新增：暴露重置方法供父组件调用
  isVoiceActive: readonly(isVoiceActive),
  connectionStatus: readonly(connectionStatus),
  isInitialized: readonly(isInitialized) // 🔧 添加初始化状态供父组件检查
});
</script>

<style scoped>
.voice-oracle-bar {
  position: relative;
  height: 100%;
  min-height: 70px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, 
    rgba(15, 15, 35, 0.95) 0%, 
    rgba(25, 25, 50, 0.98) 100%);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 
    0 6px 24px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  overflow: visible;
  z-index: 100;
}

.oracle-bar-container {
  display: flex;
  align-items: center;
  height: 100%;
  min-height: 70px;
}

/* 左侧：状态和标题 */
.oracle-left-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  min-width: 180px;
}

.oracle-brand {
  display: flex;
  align-items: center;
  gap: 8px;
}

.oracle-icon {
  position: relative;
}

.sound-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #D4AF37;
  animation: pulse 1.5s infinite;
}

.oracle-info {
  display: flex;
  flex-direction: column;
}

.oracle-title {
  color: #D4AF37;
  font-size: 1.1rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
}

.connection-status.connected .status-dot {
  background: #22c55e;
  box-shadow: 0 0 6px rgba(34, 197, 94, 0.6);
}

.connection-status.connecting .status-dot {
  background: #f59e0b;
  animation: pulse 1s infinite;
}

.connection-status.error .status-dot {
  background: #ef4444;
}

/* 中间：核心功能区 */
.oracle-center-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px 16px;
}

/* 配置快捷按钮 */
.quick-config {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 0;
}

.config-item.model-select {
  flex: 0 0 140px;
}

.config-item.voice-select {
  flex: 0 0 120px;
}

.config-item.character-select {
  flex: 0 0 120px;
}

.config-item.preset-select {
  flex: 0 0 120px;
}

.config-item.config-button {
  flex: 0 0 80px;
}

.config-item label {
  color: #D4AF37;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.mini-select {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 6px;
  color: white;
  font-size: 0.8rem;
  height: 36px;
  box-sizing: border-box;
}

.mini-select:focus {
  outline: none;
  border-color: #D4AF37;
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.mini-select option {
  background: rgba(15, 23, 42, 0.95);
  color: white;
}

/* 对话状态显示 */
.conversation-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

.current-message {
  display: flex;
  gap: 16px;
  align-items: center;
}

.message-content {
  flex: 1;
}

.message-role {
  font-weight: 600;
  font-size: 0.85rem;
}

.message-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-size: 0.9rem;
}

.message-time {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.thinking-status {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.thinking-animation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dots span {
  width: 6px;
  height: 6px;
  background: #D4AF37;
  border-radius: 50%;
  animation: thinking-bounce 1.4s infinite ease-in-out;
}

.thinking-text {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  font-size: 0.8rem;
}

/* 右侧：控制按钮 */
.oracle-right-section {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  min-width: 200px;
}

.oracle-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 8px;
  color: #D4AF37;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.1);
  border-color: #D4AF37;
  transform: translateY(-1px);
}

.control-btn.active {
  background: rgba(212, 175, 55, 0.2);
  border-color: #D4AF37;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: rgba(212, 175, 55, 0.05);
  color: rgba(212, 175, 55, 0.4);
  border-color: rgba(212, 175, 55, 0.2);
  transform: none;
  box-shadow: none;
}

.control-btn.disabled:hover {
  transform: none;
  box-shadow: none;
  background: rgba(212, 175, 55, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
}

.control-btn.compact {
  padding: 6px 12px;
  font-size: 0.8rem;
  height: 36px;
  min-width: 70px;
}

/* 展开的配置面板 */
.expanded-config-panel {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, 
    rgba(15, 15, 35, 0.95) 0%, 
    rgba(25, 25, 50, 0.98) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  box-shadow: 
    0 -6px 24px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  max-height: 500px;
  overflow-y: auto;
  z-index: 2000;
  margin-bottom: 2px;
}

.config-grid {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 预设管理样式 */
.preset-section {
  margin-bottom: 0;
}

/* 底部区域样式 */
.bottom-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
}

.preset-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preset-selector {
  display: flex;
  gap: 8px;
  align-items: center;
}

.preset-selector .mini-select {
  flex: 1;
}

.preset-add-btn {
  padding: 6px 8px;
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.5);
  color: #22c55e;
}

.preset-add-btn:hover {
  background: rgba(34, 197, 94, 0.3);
}

.preset-clear-btn {
  padding: 6px 8px;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: #ef4444;
}

.preset-clear-btn:hover {
  background: rgba(239, 68, 68, 0.3);
}

.preset-status {
  font-size: 0.75rem;
  color: #D4AF37;
  padding: 6px 12px;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 参数禁用状态 */
.params-disabled-notice {
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  color: #ffa500;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.param-sliders.disabled {
  opacity: 0.5;
  pointer-events: none;
  filter: grayscale(50%);
}

.param-sliders.disabled .mini-slider {
  cursor: not-allowed;
}

.param-section {
  margin-bottom: 0;
}

.param-sliders {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.slider-row {
  display: flex;
  gap: 12px;
}

.slider-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.slider-item label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
  font-weight: 500;
}

.slider-value {
  color: #D4AF37;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  min-width: 40px;
}

.mini-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  outline: none;
}

.mini-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #D4AF37;
  border-radius: 50%;
  cursor: pointer;
}

.mini-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #D4AF37;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.prompt-section {
  flex: 1;
}

.mini-textarea {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 6px;
  color: white;
  font-size: 0.85rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 80px;
}

/* 通用标题样式 */
.config-grid h5 {
  color: #D4AF37;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.mini-textarea:focus {
  outline: none;
  border-color: #D4AF37;
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.mini-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.actions-section {
  flex: 1;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.2);
  border-color: #D4AF37;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.reset-btn {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.5);
  color: #ff6b6b;
}

.action-btn.reset-btn:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ff8a8a;
}

/* 展开的对话视图 */
.expanded-conversation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.expanded-conversation-content {
  background: linear-gradient(135deg, 
    rgba(20, 20, 45, 0.9) 0%, 
    rgba(30, 30, 60, 0.8) 100%);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  width: 80%;
  max-height: 80%;
  overflow: hidden;
}

.conversation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(15, 15, 35, 0.9);
  border-bottom: 1px solid rgba(212, 175, 55, 0.1);
}

.conversation-header h3 {
  margin: 0;
  color: #D4AF37;
  font-size: 1.1rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #D4AF37;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.2);
}

.conversation-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
  font-size: 3rem;
  color: #D4AF37;
  margin-bottom: 16px;
  opacity: 0.7;
}

.empty-state h4 {
  color: #D4AF37;
  margin-bottom: 8px;
}

.empty-state p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  line-height: 1.3;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: linear-gradient(135deg, #4169E1, #87CEEB);
  color: white;
}

.ai-message .message-avatar {
  background: linear-gradient(135deg, #D4AF37, #FFD700);
  color: rgba(15, 23, 42, 0.9);
  position: relative;
}

.message-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 16px;
  border-radius: 16px;
  backdrop-filter: blur(8px);
}

.user-message .message-content {
  background: rgba(65, 105, 225, 0.2);
  border: 1px solid rgba(65, 105, 225, 0.3);
}

.ai-message .message-content {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.sender-name {
  font-weight: 600;
  font-size: 0.85rem;
}

.user-message .sender-name {
  color: #87CEEB;
}

.ai-message .sender-name {
  color: #D4AF37;
}

.message-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-size: 0.9rem;
}

.message-text.streaming {
  position: relative;
}

.typing-cursor {
  animation: blink 1s infinite;
  color: #D4AF37;
}

/* 思考指示器 */
.thinking-indicator .message-avatar {
  position: relative;
}

.thinking-pulse {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #D4AF37;
  border-radius: 50%;
  animation: pulse-ring 1.5s infinite;
}

.thinking-text {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  font-size: 0.8rem;
}

.thinking-dots {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.thinking-dots span {
  width: 6px;
  height: 6px;
  background: #D4AF37;
  border-radius: 50%;
  animation: thinking-bounce 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

/* 输入区域 */
.expanded-input-area {
  padding: 16px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(212, 175, 55, 0.2);
}

.voice-input-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.voice-indicator {
  display: flex;
  align-items: center;
  gap: 16px;
}

.voice-waves {
  display: flex;
  gap: 4px;
  align-items: center;
}

.wave {
  width: 4px;
  background: #D4AF37;
  border-radius: 2px;
  animation: wave-animation 1.5s infinite ease-in-out;
}

.wave-1 { height: 20px; animation-delay: 0s; }
.wave-2 { height: 30px; animation-delay: 0.1s; }
.wave-3 { height: 25px; animation-delay: 0.2s; }

.voice-text {
  color: rgba(255, 255, 255, 0.9);
}

.listening-text {
  margin: 0;
  font-weight: 600;
  color: #D4AF37;
}

.transcript-preview {
  margin: 4px 0 0 0;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

.stop-listening-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  border-radius: 8px;
  color: #ff6b6b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stop-listening-btn:hover {
  background: rgba(239, 68, 68, 0.3);
}

.manual-input .input-group {
  display: flex;
  gap: 8px;
}

.text-input {
  flex: 1;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
}

.text-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.text-input:focus {
  outline: none;
  border-color: #D4AF37;
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.input-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(212, 175, 55, 0.2);
  border: 1px solid rgba(212, 175, 55, 0.5);
  border-radius: 8px;
  color: #D4AF37;
  cursor: pointer;
  transition: all 0.3s ease;
}

.input-btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.3);
  transform: translateY(-1px);
}

.input-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes pulse-ring {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.2); opacity: 0; }
}

@keyframes thinking-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes wave-animation {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.5); }
}

/* 预设模态框样式 */
.preset-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.preset-modal {
  background: linear-gradient(135deg, 
    rgba(20, 20, 45, 0.95) 0%, 
    rgba(30, 30, 60, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  width: 400px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
  background: rgba(212, 175, 55, 0.1);
}

.modal-header h3 {
  color: #D4AF37;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  color: #D4AF37;
  background: rgba(212, 175, 55, 0.2);
}

.modal-body {
  padding: 20px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-item label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 6px;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 0.85rem;
  outline: none;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #D4AF37;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.params-grid-modal {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.param-item-modal {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-item-modal label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.form-slider {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  appearance: none;
}

.form-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #D4AF37;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-slider::-webkit-slider-thumb:hover {
  background: #FFD700;
  transform: scale(1.1);
}

.form-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #D4AF37;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid rgba(212, 175, 55, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.cancel-btn, .save-btn {
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.save-btn {
  background: rgba(212, 175, 55, 0.2);
  border: 1px solid #D4AF37;
  color: #D4AF37;
}

.cancel-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
}

.save-btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.3);
  color: #FFD700;
  transform: translateY(-1px);
}

.save-btn:disabled, .cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 响应式 */
@media (max-width: 768px) {
  .oracle-bar-container {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .oracle-left-section {
    justify-content: center;
  }
  
  .oracle-center-section {
    padding: 16px;
  }
  
  .quick-config {
    flex-direction: column;
    align-items: stretch;
  }
  
  .config-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .action-btn {
    width: 100%;
    height: 40px;
  }
  
  /* 移动端预设模态框适配 */
  .preset-modal {
    width: 95vw;
    margin: 10px;
  }
  
  .params-grid-modal {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .preset-selector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .preset-selector .mini-select {
    margin-bottom: 8px;
  }
  
  .preset-add-btn, .preset-clear-btn {
    width: 100%;
    justify-content: center;
  }
  
  .bottom-section {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 🎙️ 简洁语音输入区样式 */
.transcript-display-bar {
  width: 100%;
  margin-bottom: 8px;
}

.transcript-bar-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.transcript-bar-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #D4AF37;
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
  min-width: fit-content;
}

.transcript-bar-header i {
  font-size: 0.9rem;
  opacity: 0.8;
}

.dialogue-counter {
  margin-left: 8px;
  padding: 2px 8px;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 12px;
  font-size: 0.7rem;
}

.counter-text {
  color: #D4AF37;
  font-weight: 600;
}

.no-tianren {
  color: rgba(255, 107, 107, 0.9);
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 107, 107, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

.transcript-bar-input {
  flex: 1;
  padding: 10px 16px;
  background: transparent;
  border: none;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3);
  color: #ffffff;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0;
}

.transcript-bar-input:focus {
  border-bottom-color: #D4AF37;
  background: rgba(212, 175, 55, 0.05);
}

.transcript-bar-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.transcript-bar-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.transcript-bar-actions {
  display: flex;
  gap: 6px;
}

.transcript-bar-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  position: relative;
  overflow: hidden;
}

.transcript-bar-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0.1;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.transcript-bar-btn:hover::before {
  opacity: 0.2;
}

.transcript-bar-btn.cancel-btn {
  color: #ffc107;
  border: 1.5px solid rgba(255, 193, 7, 0.4);
}

.transcript-bar-btn.confirm-btn {
  color: #28a745;
  border: 1.5px solid rgba(40, 167, 69, 0.4);
}

.transcript-bar-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.transcript-bar-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.transcript-bar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

@media (max-width: 768px) {
  .transcript-bar-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px 0;
  }
  
  .transcript-bar-header {
    justify-content: center;
    width: 100%;
  }
  
  .transcript-bar-input {
    margin: 0 12px;
  }
  
  .transcript-bar-actions {
    justify-content: center;
  }
  
  .transcript-bar-btn {
    width: 36px;
    height: 36px;
  }
}
</style>

