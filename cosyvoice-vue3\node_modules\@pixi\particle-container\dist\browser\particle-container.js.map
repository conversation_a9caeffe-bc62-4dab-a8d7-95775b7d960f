{"version": 3, "file": "particle-container.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/ParticleContainer.ts", "../../src/ParticleBuffer.ts", "../../src/ParticleRenderer.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { BLEND_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\nimport { hex2rgb } from '@pixi/utils';\n\nimport type { BaseTexture, Renderer } from '@pixi/core';\nimport type { ParticleBuffer } from './ParticleBuffer';\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Sprite } from '@pixi/sprite';\n\nexport interface IParticleProperties\n{\n    vertices?: boolean;\n    position?: boolean;\n    rotation?: boolean;\n    uvs?: boolean;\n    tint?: boolean;\n    alpha?: boolean;\n    scale?: boolean;\n}\n\n/**\n * The ParticleContainer class is a really fast version of the Container built solely for speed,\n * so use when you need a lot of sprites or particles.\n *\n * The tradeoff of the ParticleContainer is that most advanced functionality will not work.\n * ParticleContainer implements the basic object transform (position, scale, rotation)\n * and some advanced functionality like tint (as of v4.5.6).\n *\n * Other more advanced functionality like masking, children, filters, etc will not work on sprites in this batch.\n *\n * It's extremely easy to use:\n * ```js\n * let container = new ParticleContainer();\n *\n * for (let i = 0; i < 100; ++i)\n * {\n *     let sprite = PIXI.Sprite.from(\"myImage.png\");\n *     container.addChild(sprite);\n * }\n * ```\n *\n * And here you have a hundred sprites that will be rendered at the speed of light.\n * @memberof PIXI\n */\nexport class ParticleContainer extends Container<Sprite>\n{\n    /**\n     * The blend mode to be applied to the sprite. Apply a value of `PIXI.BLEND_MODES.NORMAL`\n     * to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public blendMode: BLEND_MODES;\n\n    /**\n     * If true, container allocates more batches in case there are more than `maxSize` particles.\n     * @default false\n     */\n    public autoResize: boolean;\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * Default to true here as performance is usually the priority for particles.\n     * @default true\n     */\n    public roundPixels: boolean;\n\n    /**\n     * The texture used to render the children.\n     * @readonly\n     */\n    public baseTexture: BaseTexture;\n    public tintRgb: Float32Array;\n\n    /** @private */\n    _maxSize: number;\n\n    /** @private */\n    _buffers: ParticleBuffer[];\n\n    /** @private */\n    _batchSize: number;\n\n    /**\n     * Set properties to be dynamic (true) / static (false).\n     * @private\n     */\n    _properties: boolean[];\n\n    /**\n     * For every batch, stores _updateID corresponding to the last change in that batch.\n     * @private\n     */\n    _bufferUpdateIDs: number[];\n\n    /**\n     * When child inserted, removed or changes position this number goes up.\n     * @private\n     */\n    _updateID: number;\n\n    /**\n     * The tint applied to the container.\n     * This is a hex value. A value of 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    private _tint: number;\n\n    /**\n     * @param maxSize - The maximum number of particles that can be rendered by the container.\n     *  Affects size of allocated buffers.\n     * @param properties - The properties of children that should be uploaded to the gpu and applied.\n     * @param {boolean} [properties.vertices=false] - When true, vertices be uploaded and applied.\n     *                  if sprite's ` scale/anchor/trim/frame/orig` is dynamic, please set `true`.\n     * @param {boolean} [properties.position=true] - When true, position be uploaded and applied.\n     * @param {boolean} [properties.rotation=false] - When true, rotation be uploaded and applied.\n     * @param {boolean} [properties.uvs=false] - When true, uvs be uploaded and applied.\n     * @param {boolean} [properties.tint=false] - When true, alpha and tint be uploaded and applied.\n     * @param {number} [batchSize=16384] - Number of particles per batch. If less than maxSize, it uses maxSize instead.\n     * @param {boolean} [autoResize=false] - If true, container allocates more batches in case\n     *  there are more than `maxSize` particles.\n     */\n    constructor(maxSize = 1500, properties?: IParticleProperties, batchSize = 16384, autoResize = false)\n    {\n        super();\n\n        // Making sure the batch size is valid\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        const maxBatchSize = 16384;\n\n        if (batchSize > maxBatchSize)\n        {\n            batchSize = maxBatchSize;\n        }\n\n        this._properties = [false, true, false, false, false];\n        this._maxSize = maxSize;\n        this._batchSize = batchSize;\n        this._buffers = null;\n        this._bufferUpdateIDs = [];\n        this._updateID = 0;\n\n        this.interactiveChildren = false;\n        this.blendMode = BLEND_MODES.NORMAL;\n        this.autoResize = autoResize;\n        this.roundPixels = true;\n        this.baseTexture = null;\n\n        this.setProperties(properties);\n\n        this._tint = 0;\n        this.tintRgb = new Float32Array(4);\n        this.tint = 0xFFFFFF;\n    }\n\n    /**\n     * Sets the private properties array to dynamic / static based on the passed properties object\n     * @param properties - The properties to be uploaded\n     */\n    public setProperties(properties: IParticleProperties): void\n    {\n        if (properties)\n        {\n            this._properties[0] = 'vertices' in properties || 'scale' in properties\n                ? !!properties.vertices || !!properties.scale : this._properties[0];\n            this._properties[1] = 'position' in properties ? !!properties.position : this._properties[1];\n            this._properties[2] = 'rotation' in properties ? !!properties.rotation : this._properties[2];\n            this._properties[3] = 'uvs' in properties ? !!properties.uvs : this._properties[3];\n            this._properties[4] = 'tint' in properties || 'alpha' in properties\n                ? !!properties.tint || !!properties.alpha : this._properties[4];\n        }\n    }\n\n    updateTransform(): void\n    {\n        // TODO don't need to!\n        this.displayObjectUpdateTransform();\n    }\n\n    /**\n     * The tint applied to the container. This is a hex value.\n     * A value of 0xFFFFFF will remove any tint effect.\n     * IMPORTANT: This is a WebGL only feature and will be ignored by the canvas renderer.\n     * @default 0xFFFFFF\n     */\n    get tint(): number\n    {\n        return this._tint;\n    }\n\n    set tint(value: number)\n    {\n        this._tint = value;\n        hex2rgb(value, this.tintRgb);\n    }\n\n    /**\n     * Renders the container using the WebGL renderer.\n     * @param renderer - The WebGL renderer.\n     */\n    public render(renderer: Renderer): void\n    {\n        if (!this.visible || this.worldAlpha <= 0 || !this.children.length || !this.renderable)\n        {\n            return;\n        }\n\n        if (!this.baseTexture)\n        {\n            this.baseTexture = this.children[0]._texture.baseTexture;\n            if (!this.baseTexture.valid)\n            {\n                this.baseTexture.once('update', () => this.onChildrenChange(0));\n            }\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins.particle);\n        renderer.plugins.particle.render(this);\n    }\n\n    /**\n     * Set the flag that static data should be updated to true\n     * @param smallestChildIndex - The smallest child index.\n     */\n    protected onChildrenChange(smallestChildIndex: number): void\n    {\n        const bufferIndex = Math.floor(smallestChildIndex / this._batchSize);\n\n        while (this._bufferUpdateIDs.length < bufferIndex)\n        {\n            this._bufferUpdateIDs.push(0);\n        }\n        this._bufferUpdateIDs[bufferIndex] = ++this._updateID;\n    }\n\n    public dispose(): void\n    {\n        if (this._buffers)\n        {\n            for (let i = 0; i < this._buffers.length; ++i)\n            {\n                this._buffers[i].destroy();\n            }\n\n            this._buffers = null;\n        }\n    }\n\n    /**\n     * Destroys the container\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their\n     *  destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this.dispose();\n\n        this._properties = null;\n        this._buffers = null;\n        this._bufferUpdateIDs = null;\n    }\n}\n", "import { createIndicesForQuads } from '@pixi/utils';\nimport { Geo<PERSON>, Buffer } from '@pixi/core';\nimport { TYPES } from '@pixi/constants';\n\nimport type { Sprite } from '@pixi/sprite';\nimport type { IParticleRendererProperty } from './ParticleRenderer';\n\n/*\n * <AUTHOR>\n *\n * Big thanks to the very clever <PERSON> <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that\n * they now share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleBuffer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleBuffer.java\n */\n\n/**\n * The particle buffer manages the static and dynamic buffers for a particle container.\n * @private\n * @memberof PIXI\n */\nexport class ParticleBuffer\n{\n    public geometry: Geometry;\n    public staticStride: number;\n    public staticBuffer: Buffer;\n    public staticData: Float32Array;\n    public staticDataUint32: Uint32Array;\n    public dynamicStride: number;\n    public dynamicBuffer: Buffer;\n    public dynamicData: Float32Array;\n    public dynamicDataUint32: Uint32Array;\n    public _updateID: number;\n\n    /** Holds the indices of the geometry (quads) to draw. */\n    indexBuffer: Buffer;\n\n    /** The number of particles the buffer can hold. */\n    private size: number;\n\n    /** A list of the properties that are dynamic. */\n    private dynamicProperties: IParticleRendererProperty[];\n\n    /** A list of the properties that are static. */\n    private staticProperties: IParticleRendererProperty[];\n\n    /**\n     * @param {object} properties - The properties to upload.\n     * @param {boolean[]} dynamicPropertyFlags - Flags for which properties are dynamic.\n     * @param {number} size - The size of the batch.\n     */\n    constructor(properties: IParticleRendererProperty[], dynamicPropertyFlags: boolean[], size: number)\n    {\n        this.geometry = new Geometry();\n\n        this.indexBuffer = null;\n\n        this.size = size;\n        this.dynamicProperties = [];\n        this.staticProperties = [];\n\n        for (let i = 0; i < properties.length; ++i)\n        {\n            let property = properties[i];\n\n            // Make copy of properties object so that when we edit the offset it doesn't\n            // change all other instances of the object literal\n            property = {\n                attributeName: property.attributeName,\n                size: property.size,\n                uploadFunction: property.uploadFunction,\n                type: property.type || TYPES.FLOAT,\n                offset: property.offset,\n            };\n\n            if (dynamicPropertyFlags[i])\n            {\n                this.dynamicProperties.push(property);\n            }\n            else\n            {\n                this.staticProperties.push(property);\n            }\n        }\n\n        this.staticStride = 0;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n\n        this.dynamicStride = 0;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this._updateID = 0;\n\n        this.initBuffers();\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    private initBuffers(): void\n    {\n        const geometry = this.geometry;\n\n        let dynamicOffset = 0;\n\n        this.indexBuffer = new Buffer(createIndicesForQuads(this.size), true, true);\n        geometry.addIndex(this.indexBuffer);\n\n        this.dynamicStride = 0;\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.offset = dynamicOffset;\n            dynamicOffset += property.size;\n            this.dynamicStride += property.size;\n        }\n\n        const dynBuffer = new ArrayBuffer(this.size * this.dynamicStride * 4 * 4);\n\n        this.dynamicData = new Float32Array(dynBuffer);\n        this.dynamicDataUint32 = new Uint32Array(dynBuffer);\n        this.dynamicBuffer = new Buffer(this.dynamicData, false, false);\n\n        // static //\n        let staticOffset = 0;\n\n        this.staticStride = 0;\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            property.offset = staticOffset;\n            staticOffset += property.size;\n            this.staticStride += property.size;\n        }\n\n        const statBuffer = new ArrayBuffer(this.size * this.staticStride * 4 * 4);\n\n        this.staticData = new Float32Array(statBuffer);\n        this.staticDataUint32 = new Uint32Array(statBuffer);\n        this.staticBuffer = new Buffer(this.staticData, true, false);\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.dynamicBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.dynamicStride * 4,\n                property.offset * 4\n            );\n        }\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.staticBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.staticStride * 4,\n                property.offset * 4\n            );\n        }\n    }\n\n    /**\n     * Uploads the dynamic properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadDynamic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.dynamicProperties.length; i++)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.dynamicDataUint32 : this.dynamicData,\n                this.dynamicStride, property.offset);\n        }\n\n        this.dynamicBuffer._updateID++;\n    }\n\n    /**\n     * Uploads the static properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadStatic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.staticProperties.length; i++)\n        {\n            const property = this.staticProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.staticDataUint32 : this.staticData,\n                this.staticStride, property.offset);\n        }\n\n        this.staticBuffer._updateID++;\n    }\n\n    /** Destroys the ParticleBuffer. */\n    destroy(): void\n    {\n        this.indexBuffer = null;\n\n        this.dynamicProperties = null;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this.staticProperties = null;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n        // all buffers are destroyed inside geometry\n        this.geometry.destroy();\n    }\n}\n", "import { TYPES } from '@pixi/constants';\nimport { ExtensionType, ObjectRenderer, Shader, State } from '@pixi/core';\nimport { Matrix } from '@pixi/math';\nimport { correctBlendMode, premultiplyRgba, premultiplyTint } from '@pixi/utils';\nimport { ParticleBuffer } from './ParticleBuffer';\nimport fragment from './particles.frag';\nimport vertex from './particles.vert';\n\nimport type { ParticleContainer } from './ParticleContainer';\nimport type { Renderer, ExtensionMetadata } from '@pixi/core';\nimport type { Sprite } from '@pixi/sprite';\n\nexport interface IParticleRendererProperty\n{\n    attributeName: string;\n    size: number;\n    type?: TYPES;\n    uploadFunction: (...params: any[]) => any;\n    offset: number;\n}\n\n/*\n * <AUTHOR> <PERSON>\n *\n * Big thanks to the very clever <PERSON>riers <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that they now\n * share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleRenderer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleRenderer.java\n */\n\n/**\n * Renderer for Particles that is designer for speed over feature set.\n * @memberof PIXI\n */\nexport class ParticleRenderer extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'particle',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /** The WebGL state in which this renderer will work. */\n    public readonly state: State;\n\n    /** The default shader that is used if a sprite doesn't have a more specific one. */\n    public shader: Shader;\n    public tempMatrix: Matrix;\n    public properties: IParticleRendererProperty[];\n\n    /**\n     * @param renderer - The renderer this sprite batch works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        // and max number of element in the index buffer is 16384 * 6 = 98304\n        // Creating a full index buffer, overhead is 98304 * 2 = 196Ko\n        // let numIndices = 98304;\n\n        this.shader = null;\n\n        this.properties = null;\n\n        this.tempMatrix = new Matrix();\n\n        this.properties = [\n            // verticesData\n            {\n                attributeName: 'aVertexPosition',\n                size: 2,\n                uploadFunction: this.uploadVertices,\n                offset: 0,\n            },\n            // positionData\n            {\n                attributeName: 'aPositionCoord',\n                size: 2,\n                uploadFunction: this.uploadPosition,\n                offset: 0,\n            },\n            // rotationData\n            {\n                attributeName: 'aRotation',\n                size: 1,\n                uploadFunction: this.uploadRotation,\n                offset: 0,\n            },\n            // uvsData\n            {\n                attributeName: 'aTextureCoord',\n                size: 2,\n                uploadFunction: this.uploadUvs,\n                offset: 0,\n            },\n            // tintData\n            {\n                attributeName: 'aColor',\n                size: 1,\n                type: TYPES.UNSIGNED_BYTE,\n                uploadFunction: this.uploadTint,\n                offset: 0,\n            },\n        ];\n\n        this.shader = Shader.from(vertex, fragment, {});\n        this.state = State.for2d();\n    }\n\n    /**\n     * Renders the particle container object.\n     * @param container - The container to render using this ParticleRenderer.\n     */\n    public render(container: ParticleContainer): void\n    {\n        const children = container.children;\n        const maxSize = container._maxSize;\n        const batchSize = container._batchSize;\n        const renderer = this.renderer;\n        let totalChildren = children.length;\n\n        if (totalChildren === 0)\n        {\n            return;\n        }\n        else if (totalChildren > maxSize && !container.autoResize)\n        {\n            totalChildren = maxSize;\n        }\n\n        let buffers = container._buffers;\n\n        if (!buffers)\n        {\n            buffers = container._buffers = this.generateBuffers(container);\n        }\n\n        const baseTexture = children[0]._texture.baseTexture;\n        const premultiplied = baseTexture.alphaMode > 0;\n\n        // if the uvs have not updated then no point rendering just yet!\n        this.state.blendMode = correctBlendMode(container.blendMode, premultiplied);\n        renderer.state.set(this.state);\n\n        const gl = renderer.gl;\n\n        const m = container.worldTransform.copyTo(this.tempMatrix);\n\n        m.prepend(renderer.globalUniforms.uniforms.projectionMatrix);\n\n        this.shader.uniforms.translationMatrix = m.toArray(true);\n\n        this.shader.uniforms.uColor = premultiplyRgba(container.tintRgb,\n            container.worldAlpha, this.shader.uniforms.uColor, premultiplied);\n\n        this.shader.uniforms.uSampler = baseTexture;\n\n        this.renderer.shader.bind(this.shader);\n\n        let updateStatic = false;\n\n        // now lets upload and render the buffers..\n        for (let i = 0, j = 0; i < totalChildren; i += batchSize, j += 1)\n        {\n            let amount = (totalChildren - i);\n\n            if (amount > batchSize)\n            {\n                amount = batchSize;\n            }\n\n            if (j >= buffers.length)\n            {\n                buffers.push(this._generateOneMoreBuffer(container));\n            }\n\n            const buffer = buffers[j];\n\n            // we always upload the dynamic\n            buffer.uploadDynamic(children, i, amount);\n\n            const bid = container._bufferUpdateIDs[j] || 0;\n\n            updateStatic = updateStatic || (buffer._updateID < bid);\n            // we only upload the static content when we have to!\n            if (updateStatic)\n            {\n                buffer._updateID = container._updateID;\n                buffer.uploadStatic(children, i, amount);\n            }\n\n            // bind the buffer\n            renderer.geometry.bind(buffer.geometry);\n            gl.drawElements(gl.TRIANGLES, amount * 6, gl.UNSIGNED_SHORT, 0);\n        }\n    }\n\n    /**\n     * Creates one particle buffer for each child in the container we want to render and updates internal properties.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The buffers\n     */\n    private generateBuffers(container: ParticleContainer): ParticleBuffer[]\n    {\n        const buffers = [];\n        const size = container._maxSize;\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        for (let i = 0; i < size; i += batchSize)\n        {\n            buffers.push(new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize));\n        }\n\n        return buffers;\n    }\n\n    /**\n     * Creates one more particle buffer, because container has autoResize feature.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The generated buffer\n     */\n    private _generateOneMoreBuffer(container: ParticleContainer): ParticleBuffer\n    {\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        return new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize);\n    }\n\n    /**\n     * Uploads the vertices.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their vertices uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadVertices(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        let w0 = 0;\n        let w1 = 0;\n        let h0 = 0;\n        let h1 = 0;\n\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const texture = sprite._texture;\n            const sx = sprite.scale.x;\n            const sy = sprite.scale.y;\n            const trim = texture.trim;\n            const orig = texture.orig;\n\n            if (trim)\n            {\n                // if the sprite is trimmed and is not a tilingsprite then we need to add the\n                // extra space before transforming the sprite coords..\n                w1 = trim.x - (sprite.anchor.x * orig.width);\n                w0 = w1 + trim.width;\n\n                h1 = trim.y - (sprite.anchor.y * orig.height);\n                h0 = h1 + trim.height;\n            }\n            else\n            {\n                w0 = (orig.width) * (1 - sprite.anchor.x);\n                w1 = (orig.width) * -sprite.anchor.x;\n\n                h0 = orig.height * (1 - sprite.anchor.y);\n                h1 = orig.height * -sprite.anchor.y;\n            }\n\n            array[offset] = w1 * sx;\n            array[offset + 1] = h1 * sy;\n\n            array[offset + stride] = w0 * sx;\n            array[offset + stride + 1] = h1 * sy;\n\n            array[offset + (stride * 2)] = w0 * sx;\n            array[offset + (stride * 2) + 1] = h0 * sy;\n\n            array[offset + (stride * 3)] = w1 * sx;\n            array[offset + (stride * 3) + 1] = h0 * sy;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the position.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their positions uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadPosition(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spritePosition = children[startIndex + i].position;\n\n            array[offset] = spritePosition.x;\n            array[offset + 1] = spritePosition.y;\n\n            array[offset + stride] = spritePosition.x;\n            array[offset + stride + 1] = spritePosition.y;\n\n            array[offset + (stride * 2)] = spritePosition.x;\n            array[offset + (stride * 2) + 1] = spritePosition.y;\n\n            array[offset + (stride * 3)] = spritePosition.x;\n            array[offset + (stride * 3) + 1] = spritePosition.y;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the rotation.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadRotation(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spriteRotation = children[startIndex + i].rotation;\n\n            array[offset] = spriteRotation;\n            array[offset + stride] = spriteRotation;\n            array[offset + (stride * 2)] = spriteRotation;\n            array[offset + (stride * 3)] = spriteRotation;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the UVs.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadUvs(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const textureUvs = children[startIndex + i]._texture._uvs;\n\n            if (textureUvs)\n            {\n                array[offset] = textureUvs.x0;\n                array[offset + 1] = textureUvs.y0;\n\n                array[offset + stride] = textureUvs.x1;\n                array[offset + stride + 1] = textureUvs.y1;\n\n                array[offset + (stride * 2)] = textureUvs.x2;\n                array[offset + (stride * 2) + 1] = textureUvs.y2;\n\n                array[offset + (stride * 3)] = textureUvs.x3;\n                array[offset + (stride * 3) + 1] = textureUvs.y3;\n\n                offset += stride * 4;\n            }\n            else\n            {\n                // TODO you know this can be easier!\n                array[offset] = 0;\n                array[offset + 1] = 0;\n\n                array[offset + stride] = 0;\n                array[offset + stride + 1] = 0;\n\n                array[offset + (stride * 2)] = 0;\n                array[offset + (stride * 2) + 1] = 0;\n\n                array[offset + (stride * 3)] = 0;\n                array[offset + (stride * 3) + 1] = 0;\n\n                offset += stride * 4;\n            }\n        }\n    }\n\n    /**\n     * Uploads the tint.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadTint(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const premultiplied = sprite._texture.baseTexture.alphaMode > 0;\n            const alpha = sprite.alpha;\n\n            // we dont call extra function if alpha is 1.0, that's faster\n            const argb = alpha < 1.0 && premultiplied\n                ? premultiplyTint(sprite._tintRGB, alpha) : sprite._tintRGB + (alpha * 255 << 24);\n\n            array[offset] = argb;\n            array[offset + stride] = argb;\n            array[offset + (stride * 2)] = argb;\n            array[offset + (stride * 3)] = argb;\n\n            offset += stride * 4;\n        }\n    }\n\n    /** Destroys the ParticleRenderer. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        if (this.shader)\n        {\n            this.shader.destroy();\n            this.shader = null;\n        }\n\n        this.tempMatrix = null;\n    }\n}\n"], "names": ["arguments", "BLEND_MODES", "hex2rgb", "Container", "Geometry", "TYPES", "<PERSON><PERSON><PERSON>", "createIndicesForQuads", "Matrix", "Shader", "State", "correctBlendMode", "premultiplyRgba", "premultiplyTint", "ExtensionType", "O<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;ICrMA;;;;;;;;;;;;;;;;;;;;;;;IAuBG;AACH,QAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;QAAuC,SAAiB,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;IAiEpD;;;;;;;;;;;;;IAaG;IACH,IAAA,SAAA,iBAAA,CAAY,OAAc,EAAE,UAAgC,EAAE,SAAiB,EAAE,UAAkB,EAAA;IAAvF,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAc,GAAA,IAAA,CAAA,EAAA;IAAoC,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAiB,GAAA,KAAA,CAAA,EAAA;IAAE,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAkB,GAAA,KAAA,CAAA,EAAA;IAAnG,QAAA,IAAA,KAAA,GAEI,iBAAO,IA8BV,IAAA,CAAA;;;;YAzBG,IAAM,YAAY,GAAG,KAAK,CAAC;YAE3B,IAAI,SAAS,GAAG,YAAY,EAC5B;gBACI,SAAS,GAAG,YAAY,CAAC;IAC5B,SAAA;IAED,QAAA,KAAI,CAAC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACtD,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACxB,QAAA,KAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC5B,QAAA,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,KAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;IAC3B,QAAA,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IAEnB,QAAA,KAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACjC,QAAA,KAAI,CAAC,SAAS,GAAGC,qBAAW,CAAC,MAAM,CAAC;IACpC,QAAA,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,QAAA,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAExB,QAAA,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAE/B,QAAA,KAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YACf,KAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;IACnC,QAAA,KAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;;SACxB;IAED;;;IAGG;QACI,iBAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,UAA+B,EAAA;IAEhD,QAAA,IAAI,UAAU,EACd;IACI,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,UAAU,IAAI,OAAO,IAAI,UAAU;sBACjE,CAAC,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBAC7F,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBAC7F,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACnF,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,OAAO,IAAI,UAAU;sBAC7D,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACvE,SAAA;SACJ,CAAA;IAED,IAAA,iBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;;YAGI,IAAI,CAAC,4BAA4B,EAAE,CAAC;SACvC,CAAA;IAQD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAI,CAAA,SAAA,EAAA,MAAA,EAAA;IANR;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,KAAK,CAAC;aACrB;IAED,QAAA,GAAA,EAAA,UAAS,KAAa,EAAA;IAElB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,YAAAC,aAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAChC;;;IANA,KAAA,CAAA,CAAA;IAQD;;;IAGG;QACI,iBAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,QAAkB,EAAA;YAAhC,IAkBC,KAAA,GAAA,IAAA,CAAA;YAhBG,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EACtF;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EACrB;IACI,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;IACzD,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAC3B;IACI,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAM,EAAA,OAAA,KAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAxB,EAAwB,CAAC,CAAC;IACnE,aAAA;IACJ,SAAA;YAED,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5D,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC1C,CAAA;IAED;;;IAGG;QACO,iBAAgB,CAAA,SAAA,CAAA,gBAAA,GAA1B,UAA2B,kBAA0B,EAAA;IAEjD,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAErE,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,WAAW,EACjD;IACI,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjC,SAAA;YACD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC;SACzD,CAAA;IAEM,IAAA,iBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;YAEI,IAAI,IAAI,CAAC,QAAQ,EACjB;IACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAC7C;oBACI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAC9B,aAAA;IAED,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACxB,SAAA;SACJ,CAAA;IAED;;;;;;;;;;IAUG;QACI,iBAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;IAE9C,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;YAEvB,IAAI,CAAC,OAAO,EAAE,CAAC;IAEf,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC,CAAA;QACL,OAAC,iBAAA,CAAA;IAAD,CAnOA,CAAuCC,iBAAS,CAmO/C;;ICxQD;;;;;;;;;;IAUG;IAEH;;;;IAIG;IACH,IAAA,cAAA,kBAAA,YAAA;IAyBI;;;;IAIG;IACH,IAAA,SAAA,cAAA,CAAY,UAAuC,EAAE,oBAA+B,EAAE,IAAY,EAAA;IAE9F,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAIC,aAAQ,EAAE,CAAC;IAE/B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAExB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;IAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;IAE3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAC1C;IACI,YAAA,IAAI,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;;;IAI7B,YAAA,QAAQ,GAAG;oBACP,aAAa,EAAE,QAAQ,CAAC,aAAa;oBACrC,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,cAAc,EAAE,QAAQ,CAAC,cAAc;IACvC,gBAAA,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAIC,eAAK,CAAC,KAAK;oBAClC,MAAM,EAAE,QAAQ,CAAC,MAAM;iBAC1B,CAAC;IAEF,YAAA,IAAI,oBAAoB,CAAC,CAAC,CAAC,EAC3B;IACI,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,aAAA;IAED,iBAAA;IACI,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,aAAA;IACJ,SAAA;IAED,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IACtB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IACzB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAE7B,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IACvB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAE9B,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YAEnB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;;IAGO,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;IAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE/B,IAAI,aAAa,GAAG,CAAC,CAAC;IAEtB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAIC,WAAM,CAACC,2BAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5E,QAAA,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAEpC,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAEvB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,EACtD;gBACI,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAE3C,YAAA,QAAQ,CAAC,MAAM,GAAG,aAAa,CAAC;IAChC,YAAA,aAAa,IAAI,QAAQ,CAAC,IAAI,CAAC;IAC/B,YAAA,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC,IAAI,CAAC;IACvC,SAAA;IAED,QAAA,IAAM,SAAS,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAE1E,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC;IACpD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAID,WAAM,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;;YAGhE,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IAEtB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EACrD;gBACI,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAE1C,YAAA,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC;IAC/B,YAAA,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC;IAC9B,YAAA,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC;IACtC,SAAA;IAED,QAAA,IAAM,UAAU,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAE1E,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,CAAC;IACpD,QAAA,IAAI,CAAC,YAAY,GAAG,IAAIA,WAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAE7D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,EACtD;gBACI,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAE3C,YAAA,QAAQ,CAAC,YAAY,CACjB,QAAQ,CAAC,aAAa,EACtB,IAAI,CAAC,aAAa,EAClB,CAAC,EACD,QAAQ,CAAC,IAAI,KAAKD,eAAK,CAAC,aAAa,EACrC,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,aAAa,GAAG,CAAC,EACtB,QAAQ,CAAC,MAAM,GAAG,CAAC,CACtB,CAAC;IACL,SAAA;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EACrD;gBACI,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAE1C,YAAA,QAAQ,CAAC,YAAY,CACjB,QAAQ,CAAC,aAAa,EACtB,IAAI,CAAC,YAAY,EACjB,CAAC,EACD,QAAQ,CAAC,IAAI,KAAKA,eAAK,CAAC,aAAa,EACrC,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,YAAY,GAAG,CAAC,EACrB,QAAQ,CAAC,MAAM,GAAG,CAAC,CACtB,CAAC;IACL,SAAA;SACJ,CAAA;IAED;;;;;IAKG;IACH,IAAA,cAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,QAAkB,EAAE,UAAkB,EAAE,MAAc,EAAA;IAEhE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EACtD;gBACI,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAE3C,YAAA,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAChD,QAAQ,CAAC,IAAI,KAAKA,eAAK,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,EACjF,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC5C,SAAA;IAED,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;SAClC,CAAA;IAED;;;;;IAKG;IACH,IAAA,cAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,QAAkB,EAAE,UAAkB,EAAE,MAAc,EAAA;IAE/D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EACrD;gBACI,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAE1C,YAAA,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAChD,QAAQ,CAAC,IAAI,KAAKA,eAAK,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,EAC/E,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3C,SAAA;IAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;SACjC,CAAA;;IAGD,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IAEI,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAExB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAC9B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAE9B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IACzB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;;IAE7B,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;SAC3B,CAAA;QACL,OAAC,cAAA,CAAA;IAAD,CAAC,EAAA,CAAA;;;;;;ICzND;;;;;;;;;;IAUG;IAEH;;;IAGG;AACH,QAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;QAAsC,SAAc,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;IAgBhD;;IAEG;IACH,IAAA,SAAA,gBAAA,CAAY,QAAkB,EAAA;YAA9B,IAEI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,QAAQ,CAAC,IAuDlB,IAAA,CAAA;;;;;;IA/CG,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IAEnB,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAEvB,QAAA,KAAI,CAAC,UAAU,GAAG,IAAIG,WAAM,EAAE,CAAC;YAE/B,KAAI,CAAC,UAAU,GAAG;;IAEd,YAAA;IACI,gBAAA,aAAa,EAAE,iBAAiB;IAChC,gBAAA,IAAI,EAAE,CAAC;oBACP,cAAc,EAAE,KAAI,CAAC,cAAc;IACnC,gBAAA,MAAM,EAAE,CAAC;IACZ,aAAA;;IAED,YAAA;IACI,gBAAA,aAAa,EAAE,gBAAgB;IAC/B,gBAAA,IAAI,EAAE,CAAC;oBACP,cAAc,EAAE,KAAI,CAAC,cAAc;IACnC,gBAAA,MAAM,EAAE,CAAC;IACZ,aAAA;;IAED,YAAA;IACI,gBAAA,aAAa,EAAE,WAAW;IAC1B,gBAAA,IAAI,EAAE,CAAC;oBACP,cAAc,EAAE,KAAI,CAAC,cAAc;IACnC,gBAAA,MAAM,EAAE,CAAC;IACZ,aAAA;;IAED,YAAA;IACI,gBAAA,aAAa,EAAE,eAAe;IAC9B,gBAAA,IAAI,EAAE,CAAC;oBACP,cAAc,EAAE,KAAI,CAAC,SAAS;IAC9B,gBAAA,MAAM,EAAE,CAAC;IACZ,aAAA;;IAED,YAAA;IACI,gBAAA,aAAa,EAAE,QAAQ;IACvB,gBAAA,IAAI,EAAE,CAAC;oBACP,IAAI,EAAEH,eAAK,CAAC,aAAa;oBACzB,cAAc,EAAE,KAAI,CAAC,UAAU;IAC/B,gBAAA,MAAM,EAAE,CAAC;IACZ,aAAA,EACJ,CAAC;IAEF,QAAA,KAAI,CAAC,MAAM,GAAGI,WAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;IAChD,QAAA,KAAI,CAAC,KAAK,GAAGC,UAAK,CAAC,KAAK,EAAE,CAAC;;SAC9B;IAED;;;IAGG;QACI,gBAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,SAA4B,EAAA;IAEtC,QAAA,IAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;IACpC,QAAA,IAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;IACnC,QAAA,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;IACvC,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/B,QAAA,IAAI,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;YAEpC,IAAI,aAAa,KAAK,CAAC,EACvB;gBACI,OAAO;IACV,SAAA;iBACI,IAAI,aAAa,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EACzD;gBACI,aAAa,GAAG,OAAO,CAAC;IAC3B,SAAA;IAED,QAAA,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;YAEjC,IAAI,CAAC,OAAO,EACZ;gBACI,OAAO,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAClE,SAAA;YAED,IAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;IACrD,QAAA,IAAM,aAAa,GAAG,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;;IAGhD,QAAA,IAAI,CAAC,KAAK,CAAC,SAAS,GAAGC,sBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAC5E,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAE/B,QAAA,IAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;IAEvB,QAAA,IAAM,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3D,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IAE7D,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAGC,qBAAe,CAAC,SAAS,CAAC,OAAO,EAC3D,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAEtE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC;YAE5C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEvC,IAAI,YAAY,GAAG,KAAK,CAAC;;YAGzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,IAAI,CAAC,EAChE;IACI,YAAA,IAAI,MAAM,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;gBAEjC,IAAI,MAAM,GAAG,SAAS,EACtB;oBACI,MAAM,GAAG,SAAS,CAAC;IACtB,aAAA;IAED,YAAA,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EACvB;oBACI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC;IACxD,aAAA;IAED,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;;gBAG1B,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;gBAE1C,IAAM,GAAG,GAAG,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAE/C,YAAY,GAAG,YAAY,KAAK,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;;IAExD,YAAA,IAAI,YAAY,EAChB;IACI,gBAAA,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;oBACvC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5C,aAAA;;gBAGD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACxC,YAAA,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;IACnE,SAAA;SACJ,CAAA;IAED;;;;IAIG;QACK,gBAAe,CAAA,SAAA,CAAA,eAAA,GAAvB,UAAwB,SAA4B,EAAA;YAEhD,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,QAAA,IAAM,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;IAChC,QAAA,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;IACvC,QAAA,IAAM,oBAAoB,GAAG,SAAS,CAAC,WAAW,CAAC;IAEnD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,SAAS,EACxC;IACI,YAAA,OAAO,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE,SAAS,CAAC,CAAC,CAAC;IACtF,SAAA;IAED,QAAA,OAAO,OAAO,CAAC;SAClB,CAAA;IAED;;;;IAIG;QACK,gBAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,SAA4B,EAAA;IAEvD,QAAA,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;IACvC,QAAA,IAAM,oBAAoB,GAAG,SAAS,CAAC,WAAW,CAAC;YAEnD,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE,SAAS,CAAC,CAAC;SAC/E,CAAA;IAED;;;;;;;;IAQG;IACI,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAArB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;YAG/C,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,EAAE,GAAG,CAAC,CAAC;YAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAC/B;gBACI,IAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACxC,YAAA,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC;IAChC,YAAA,IAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1B,YAAA,IAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1B,YAAA,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,YAAA,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAE1B,YAAA,IAAI,IAAI,EACR;;;IAGI,gBAAA,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7C,gBAAA,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;IAErB,gBAAA,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9C,gBAAA,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;IACzB,aAAA;IAED,iBAAA;IACI,gBAAA,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1C,gBAAA,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAErC,gBAAA,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACzC,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACvC,aAAA;IAED,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;gBACxB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;gBAE5B,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;gBACjC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAErC,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IACvC,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAE3C,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IACvC,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAE3C,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;IACxB,SAAA;SACJ,CAAA;IAED;;;;;;;;IAQG;IACI,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAArB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;YAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAC/B;gBACI,IAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;IAEzD,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;gBACjC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;gBAErC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;gBAC1C,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;IAE9C,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;IAChD,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;IAEpD,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;IAChD,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;IAEpD,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;IACxB,SAAA;SACJ,CAAA;IAED;;;;;;;;IAQG;IACI,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAArB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;YAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAC/B;gBACI,IAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;IAEzD,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC;IAC/B,YAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,cAAc,CAAC;gBACxC,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;gBAC9C,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;IAE9C,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;IACxB,SAAA;SACJ,CAAA;IAED;;;;;;;;IAQG;IACI,IAAA,gBAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;YAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAC/B;IACI,YAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;IAE1D,YAAA,IAAI,UAAU,EACd;IACI,gBAAA,KAAK,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;oBAC9B,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;oBAElC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;oBACvC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;IAE3C,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;IAC7C,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;IAEjD,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;IAC7C,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;IAEjD,gBAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;IACxB,aAAA;IAED,iBAAA;;IAEI,gBAAA,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAClB,gBAAA,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAEtB,gBAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC3B,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBAE/B,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjC,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBAErC,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjC,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAErC,gBAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;IACxB,aAAA;IACJ,SAAA;SACJ,CAAA;IAED;;;;;;;;IAQG;IACI,IAAA,gBAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;YAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAC/B;gBACI,IAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBACxC,IAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;IAChE,YAAA,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;;IAG3B,YAAA,IAAM,IAAI,GAAG,KAAK,GAAG,GAAG,IAAI,aAAa;sBACnCC,qBAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,IAAI,KAAK,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;IAEtF,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;IACrB,YAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC;gBAC9B,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBACpC,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAEpC,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;IACxB,SAAA;SACJ,CAAA;;IAGM,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;YAEI,MAAM,CAAA,SAAA,CAAA,OAAO,WAAE,CAAC;YAEhB,IAAI,IAAI,CAAC,MAAM,EACf;IACI,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IACtB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,SAAA;IAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B,CAAA;;IAnaM,IAAA,gBAAA,CAAA,SAAS,GAAsB;IAClC,QAAA,IAAI,EAAE,UAAU;YAChB,IAAI,EAAEC,kBAAa,CAAC,cAAc;SACrC,CAAC;QAiaN,OAAC,gBAAA,CAAA;KAAA,CAvaqCC,mBAAc,CAuanD;;;;;;;;;;;;;;;;"}