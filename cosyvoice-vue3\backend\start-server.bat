@echo off
echo 🚀 启动 CosyVoice WebSocket 服务器...
echo.

REM 检查是否安装了 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Node.js
    echo 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version

REM 检查是否安装了依赖包
if not exist "node_modules" (
    echo 📦 安装依赖包...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 🌟 WebSocket 服务器信息:
echo    📡 WebSocket: ws://localhost:3001
echo    🌐 HTTP API: http://localhost:3001
echo    💚 健康检查: http://localhost:3001/health
echo    📊 状态查看: http://localhost:3001/status
echo.
echo 按 Ctrl+C 停止服务器
echo.

REM 启动服务器
npm start

pause 