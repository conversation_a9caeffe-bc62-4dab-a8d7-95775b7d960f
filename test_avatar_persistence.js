/**
 * 头像持久化测试脚本
 * 用于验证头像保存和加载是否正常工作
 */

console.log('🔍 开始测试头像持久化功能...');

// 模拟头像数据
const testAvatarData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

async function testAvatarPersistence() {
  try {
    console.log('1. 测试保存头像...');
    
    // 动态导入unifiedConfigManager
    const { unifiedConfigManager } = await import('./cosyvoice-vue3/src/utils/unifiedConfigManager.ts');
    
    // 保存测试头像
    await unifiedConfigManager.saveProtagonistAvatar(testAvatarData);
    console.log('✅ 头像保存成功');
    
    console.log('2. 测试加载头像...');
    
    // 立即加载头像
    const loadedAvatar = await unifiedConfigManager.getProtagonistAvatar();
    
    if (loadedAvatar && loadedAvatar === testAvatarData) {
      console.log('✅ 头像加载成功，数据一致');
      console.log('✅ 头像持久化测试通过');
    } else if (loadedAvatar) {
      console.log('⚠️ 头像已加载，但数据不一致');
      console.log('保存的长度:', testAvatarData.length);
      console.log('加载的长度:', loadedAvatar.length);
      console.log('前50个字符匹配:', testAvatarData.substring(0, 50) === loadedAvatar.substring(0, 50) ? '是' : '否');
    } else {
      console.log('❌ 头像加载失败，返回null');
    }
    
    console.log('3. 检查存储状态...');
    
    // 检查electronStoreManager状态
    const { electronStoreManager } = await import('./cosyvoice-vue3/src/utils/electronStoreManager.ts');
    const hasKey = await electronStoreManager.has('protagonist-avatar');
    console.log('存储中是否有protagonist-avatar键:', hasKey);
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
}

// 执行测试
testAvatarPersistence().then(() => {
  console.log('🏁 头像持久化测试完成');
}).catch(error => {
  console.error('❌ 测试执行失败:', error);
});