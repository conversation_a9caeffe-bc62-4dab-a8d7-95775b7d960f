<!DOCTYPE html>
<html>
<head>
    <title>直接测试Electron Store</title>
</head>
<body>
    <h1>直接测试Electron Store</h1>
    <button onclick="testElectronStore()">测试Electron Store</button>
    <button onclick="testStyleImages()">测试风格图片（对比）</button>
    <div id="output" style="font-family: monospace; white-space: pre-wrap; margin-top: 20px;"></div>

    <script>
        function log(msg) {
            const output = document.getElementById('output');
            output.textContent += new Date().toISOString() + ': ' + msg + '\n';
        }

        async function testElectronStore() {
            try {
                log('=== 直接测试Electron Store ===');
                
                if (!window.electronAPI) {
                    log('❌ 没有检测到Electron API');
                    return;
                }

                // 1. 测试直接保存
                log('1. 测试直接保存头像...');
                const testUrl = 'http://localhost:3001/avatars/test_direct_' + Date.now() + '.png';
                
                const avatars = await window.electronAPI.store.get('protagonist-avatars') || {};
                log('保存前avatars对象: ' + JSON.stringify(avatars));
                
                avatars['current'] = testUrl;
                await window.electronAPI.store.set('protagonist-avatars', avatars);
                log('✅ 保存完成');
                
                // 2. 立即验证
                log('2. 立即验证...');
                const verification = await window.electronAPI.store.get('protagonist-avatars') || {};
                log('立即验证结果: ' + JSON.stringify(verification));
                log('current键: ' + verification['current']);
                
                // 3. 等待一秒后再次验证
                log('3. 等待1秒后再次验证...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const delayed = await window.electronAPI.store.get('protagonist-avatars') || {};
                log('延迟验证结果: ' + JSON.stringify(delayed));
                log('current键: ' + delayed['current']);
                
            } catch (error) {
                log('❌ 测试失败: ' + error.message);
            }
        }

        async function testStyleImages() {
            try {
                log('=== 测试风格图片（对比） ===');
                
                if (!window.electronAPI) {
                    log('❌ 没有检测到Electron API');
                    return;
                }

                const styleImages = await window.electronAPI.store.get('preset-style-images') || {};
                log('风格图片数据: ' + JSON.stringify(styleImages).substring(0, 200) + '...');
                log('风格图片键数量: ' + Object.keys(styleImages).length);
                
                // 对比头像数据
                const avatars = await window.electronAPI.store.get('protagonist-avatars') || {};
                log('头像数据: ' + JSON.stringify(avatars));
                log('头像键数量: ' + Object.keys(avatars).length);
                
            } catch (error) {
                log('❌ 测试失败: ' + error.message);
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成');
            testElectronStore();
        };
    </script>
</body>
</html>