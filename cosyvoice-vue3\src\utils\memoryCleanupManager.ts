/**
 * 🧹 全局内存清理管理器
 * 统一管理应用的内存清理和垃圾回收
 */

export interface MemoryStats {
  used: number;
  total: number;
  percentage: number;
  timestamp: number;
}

export class MemoryCleanupManager {
  private static instance: MemoryCleanupManager;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private memoryThreshold = 0.85; // 85%内存使用率阈值
  private cleanupInterval = 2 * 60 * 1000; // 2分钟检查一次
  private lastCleanupTime = 0;
  private minCleanupInterval = 30 * 1000; // 最小清理间隔30秒

  private constructor() {
    // 🔧 延迟初始化：等待页面加载完成后再启动内存监控（避免初始内存爆炸）
    this.delayedStart();
  }

  public static getInstance(): MemoryCleanupManager {
    if (!MemoryCleanupManager.instance) {
      MemoryCleanupManager.instance = new MemoryCleanupManager();
    }
    return MemoryCleanupManager.instance;
  }

  /**
   * 🕰️ 延迟启动内存监控（避免初始化时的内存占用）
   */
  private delayedStart() {
    // 🔑 完全禁用自动内存清理，因为发现它实际上在增加内存使用量
    console.log('🚫 内存清理管理器已禁用 - 发现它会增加内存使用量而不是减少');
    console.log('💡 如需手动清理，请调用: memoryCleanupManager.forceCleanup()');
    
    // 不再启动自动监控
    // setTimeout(() => {
    //   this.startMemoryMonitoring();
    // }, 10000);
  }

  /**
   * 🚀 启动内存监控
   */
  private startMemoryMonitoring() {
    this.cleanupTimer = setInterval(() => {
      this.checkMemoryAndCleanup();
    }, this.cleanupInterval);

    console.log('🔄 内存清理管理器已启动');
  }

  /**
   * 📊 获取当前内存统计
   */
  public getMemoryStats(): MemoryStats | null {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: memory.usedJSHeapSize / memory.totalJSHeapSize,
        timestamp: Date.now()
      };
    }
    return null;
  }

  /**
   * 🔍 检查内存并执行清理
   */
  private checkMemoryAndCleanup() {
    const stats = this.getMemoryStats();
    if (!stats) return;

    const now = Date.now();
    const timeSinceLastCleanup = now - this.lastCleanupTime;

    // 检查是否需要清理
    const needsCleanup = stats.percentage > this.memoryThreshold || 
                         stats.used > 800 * 1024 * 1024; // 超过800MB

    if (needsCleanup && timeSinceLastCleanup > this.minCleanupInterval) {
      console.log(`🚨 内存使用率过高: ${(stats.percentage * 100).toFixed(1)}% (${(stats.used / 1024 / 1024).toFixed(1)}MB)`);
      this.performGlobalCleanup();
      this.lastCleanupTime = now;
    }
  }

  /**
   * 🧹 执行全局内存清理
   */
  public performGlobalCleanup() {
    console.log('🧹 开始全局内存清理...');

    const beforeStats = this.getMemoryStats();

    // 1. 清理图片缓存
    this.cleanupImageCaches();

    // 2. 清理组件缓存
    this.cleanupComponentCaches();

    // 3. 触发垃圾回收（如果可用）
    this.triggerGarbageCollection();

    const afterStats = this.getMemoryStats();
    
    if (beforeStats && afterStats) {
      const memoryFreed = beforeStats.used - afterStats.used;
      console.log(`✅ 内存清理完成: 释放了 ${(memoryFreed / 1024 / 1024).toFixed(1)}MB`);
      console.log(`📊 清理前: ${(beforeStats.used / 1024 / 1024).toFixed(1)}MB, 清理后: ${(afterStats.used / 1024 / 1024).toFixed(1)}MB`);
    }
  }

  /**
   * 🖼️ 清理图片缓存
   */
  private cleanupImageCaches() {
    // 发送清理信号给所有SafeImage组件
    window.dispatchEvent(new CustomEvent('clear-image-cache'));
    
    // 清理ComicGallery的缓存
    window.dispatchEvent(new CustomEvent('clear-comic-gallery-cache'));
    
    console.log('🖼️ 图片缓存清理信号已发送');
  }

  /**
   * 🧩 清理组件缓存
   */
  private cleanupComponentCaches() {
    // 清理Vue组件缓存
    if (typeof window !== 'undefined' && (window as any).Vue) {
      // 清理Vue的内部缓存
      try {
        // 这里可以添加Vue特定的清理逻辑
        console.log('🧩 Vue组件缓存清理完成');
      } catch (error) {
        console.warn('⚠️ Vue缓存清理失败:', error);
      }
    }
  }

  /**
   * 🗑️ 触发垃圾回收
   */
  private triggerGarbageCollection() {
    // 在支持的环境中触发垃圾回收
    if (typeof window !== 'undefined' && (window as any).gc) {
      try {
        (window as any).gc();
        console.log('🗑️ 手动垃圾回收已触发');
      } catch (error) {
        console.warn('⚠️ 手动垃圾回收失败:', error);
      }
    }

    // 使用其他方式提示浏览器进行垃圾回收
    if (typeof global !== 'undefined' && global.gc) {
      try {
        global.gc();
        console.log('🗑️ Node.js垃圾回收已触发');
      } catch (error) {
        console.warn('⚠️ Node.js垃圾回收失败:', error);
      }
    }
  }

  /**
   * 🔧 手动强制清理（用于用户操作触发）
   */
  public forceCleanup() {
    const stats = this.getMemoryStats();
    if (stats) {
      console.log(`🔧 手动强制清理，当前内存: ${(stats.used / 1024 / 1024).toFixed(1)}MB`);
    }
    
    this.performGlobalCleanup();
  }

  /**
   * 📈 设置内存阈值
   */
  public setMemoryThreshold(threshold: number) {
    if (threshold > 0 && threshold < 1) {
      this.memoryThreshold = threshold;
      console.log(`📈 内存阈值已设置为: ${(threshold * 100).toFixed(1)}%`);
    }
  }

  /**
   * ⏰ 设置清理间隔
   */
  public setCleanupInterval(interval: number) {
    if (interval > 0) {
      this.cleanupInterval = interval;
      
      // 重启定时器
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
        this.startMemoryMonitoring();
      }
      
      console.log(`⏰ 清理间隔已设置为: ${interval / 1000}秒`);
    }
  }

  /**
   * 🛑 停止内存监控
   */
  public stopMemoryMonitoring() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
      console.log('🛑 内存监控已停止');
    }
  }

  /**
   * 📊 获取详细的内存报告
   */
  public getMemoryReport(): {
    stats: MemoryStats | null;
    threshold: number;
    lastCleanup: number;
    cleanupInterval: number;
  } {
    return {
      stats: this.getMemoryStats(),
      threshold: this.memoryThreshold,
      lastCleanup: this.lastCleanupTime,
      cleanupInterval: this.cleanupInterval
    };
  }
}

// 导出单例实例
export const memoryCleanupManager = MemoryCleanupManager.getInstance();

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).memoryCleanupManager = memoryCleanupManager;
}