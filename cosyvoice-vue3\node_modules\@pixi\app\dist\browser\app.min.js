/*!
 * @pixi/app - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/app is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_app=function(e,i,n){"use strict";var t=function(){function e(){}return e.init=function(e){var i=this;Object.defineProperty(this,"resizeTo",{set:function(e){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=e,e&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get:function(){return this._resizeTo}}),this.queueResize=function(){i._resizeTo&&(i.cancelResize(),i._resizeId=requestAnimationFrame((function(){return i.resize()})))},this.cancelResize=function(){i._resizeId&&(cancelAnimationFrame(i._resizeId),i._resizeId=null)},this.resize=function(){if(i._resizeTo){var e,n;if(i.cancelResize(),i._resizeTo===globalThis.window)e=globalThis.innerWidth,n=globalThis.innerHeight;else{var t=i._resizeTo;e=t.clientWidth,n=t.clientHeight}i.renderer.resize(e,n)}},this._resizeId=null,this._resizeTo=null,this.resizeTo=e.resizeTo||null},e.destroy=function(){globalThis.removeEventListener("resize",this.queueResize),this.cancelResize(),this.cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null},e.extension=i.ExtensionType.Application,e}(),s=function(){function e(t){var s=this;this.stage=new n.Container,t=Object.assign({forceCanvas:!1},t),this.renderer=i.autoDetectRenderer(t),e._plugins.forEach((function(e){e.init.call(s,t)}))}return e.registerPlugin=function(e){i.extensions.add({type:i.ExtensionType.Application,ref:e})},e.prototype.render=function(){this.renderer.render(this.stage)},Object.defineProperty(e.prototype,"view",{get:function(){return this.renderer.view},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"screen",{get:function(){return this.renderer.screen},enumerable:!1,configurable:!0}),e.prototype.destroy=function(i,n){var t=this,s=e._plugins.slice(0);s.reverse(),s.forEach((function(e){e.destroy.call(t)})),this.stage.destroy(n),this.stage=null,this.renderer.destroy(i),this.renderer=null},e._plugins=[],e}();return i.extensions.handleByList(i.ExtensionType.Application,s._plugins),i.extensions.add(t),e.Application=s,e.ResizePlugin=t,Object.defineProperty(e,"__esModule",{value:!0}),e}({},PIXI,PIXI);Object.assign(this.PIXI,_pixi_app);
//# sourceMappingURL=app.min.js.map
