{"version": 3, "file": "text-bitmap.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/BitmapFontData.ts", "../../src/formats/TextFormat.ts", "../../src/formats/XMLFormat.ts", "../../src/formats/XMLStringFormat.ts", "../../src/formats/index.ts", "../../src/utils/drawGlyph.ts", "../../src/utils/generateFillStyle.ts", "../../src/utils/splitTextToCharacters.ts", "../../src/utils/extractCharCode.ts", "../../src/BitmapFont.ts", "../../src/utils/resolveCharacters.ts", "../../src/BitmapText.ts", "../../src/BitmapFontLoader.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/* eslint-disable max-len */\n\n/**\n * Normalized parsed data from .fnt files.\n * @memberof PIXI\n */\nexport class BitmapFontData\n{\n    /** @readonly */\n    public info: IBitmapFontDataInfo[];\n\n    /** @readonly */\n    public common: IBitmapFontDataCommon[];\n\n    /** @readonly */\n    public page: IBitmapFontDataPage[];\n\n    /** @readonly */\n    public char: IBitmapFontDataChar[];\n\n    /** @readonly */\n    public kerning: IBitmapFontDataKerning[];\n\n    /** @readonly */\n    public distanceField: IBitmapFontDataDistanceField[];\n\n    constructor()\n    {\n        this.info = [];\n        this.common = [];\n        this.page = [];\n        this.char = [];\n        this.kerning = [];\n        this.distanceField = [];\n    }\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataInfo\n{\n    /** Font face */\n    face: string;\n\n    /** Font size */\n    size: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataCommon\n{\n    /** Line height, in pixels. */\n    lineHeight: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataPage\n{\n    /** Unique id for bitmap texture */\n    id: number;\n\n    /** File name */\n    file: string;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataChar\n{\n    /** Unique id of character */\n    id: number;\n\n    /** {@link PIXI.IBitmapFontDataPage} id */\n    page: number;\n\n    /** x-position of character in page. */\n    x: number;\n\n    /** y-position of character in page. */\n    y: number;\n\n    /** Width of character in page. */\n    width: number;\n\n    /** Height of character in page. */\n    height: number;\n\n    /** x-offset to apply when rendering character */\n    xoffset: number;\n\n    /** y-offset to apply when rendering character. */\n    yoffset: number;\n\n    /** Advancement to apply to next character. */\n    xadvance: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataKerning\n{\n    /** First character of pair */\n    first: number;\n\n    /** Second character of pair */\n    second: number;\n\n    /** x-offset to apply between first & second characters when they are next to each other. */\n    amount: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataDistanceField\n{\n    /** Type of distance field */\n    fieldType: string;\n\n    /** Range of distance */\n    distanceRange: number;\n}\n", "import { BitmapFontData } from '../BitmapFontData';\n\n/**\n * Internal data format used to convert to BitmapFontData.\n * @private\n */\nexport interface IBitmapFontRawData\n{\n    info: {\n        face: string;\n        size: string;\n    }[];\n    common: { lineHeight: string }[];\n    page: {\n        id: string;\n        file: string;\n    }[];\n    chars: {\n        count: number;\n    }[];\n    char: {\n        id: string;\n        page: string;\n        x: string;\n        y: string;\n        width: string;\n        height: string;\n        xoffset: string;\n        yoffset: string;\n        xadvance: string;\n    }[];\n    kernings?: {\n        count: number;\n    }[];\n    kerning?: {\n        first: string;\n        second: string;\n        amount: string;\n    }[];\n    distanceField?: {\n        fieldType: string;\n        distanceRange: string;\n    }[]\n}\n\n/**\n * BitmapFont format that's Text-based.\n * @private\n */\nexport class TextFormat\n{\n    /**\n     * Check if resource refers to txt font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: unknown): boolean\n    {\n        return typeof data === 'string' && data.indexOf('info face=') === 0;\n    }\n\n    /**\n     * Convert text font data to a javascript object.\n     * @param txt - Raw string data to be converted\n     * @returns - Parsed font data\n     */\n    static parse(txt: string): BitmapFontData\n    {\n        // Retrieve data item\n        const items = txt.match(/^[a-z]+\\s+.+$/gm);\n        const rawData: IBitmapFontRawData = {\n            info: [],\n            common: [],\n            page: [],\n            char: [],\n            chars: [],\n            kerning: [],\n            kernings: [],\n            distanceField: [],\n        };\n\n        for (const i in items)\n        {\n            // Extract item name\n            const name = items[i].match(/^[a-z]+/gm)[0] as keyof BitmapFontData;\n\n            // Extract item attribute list as string ex.: \"width=10\"\n            const attributeList = items[i].match(/[a-zA-Z]+=([^\\s\"']+|\"([^\"]*)\")/gm);\n\n            // Convert attribute list into an object\n            const itemData: any = {};\n\n            for (const i in attributeList)\n            {\n                // Split key-value pairs\n                const split = attributeList[i].split('=');\n                const key = split[0];\n\n                // Remove eventual quotes from value\n                const strValue = split[1].replace(/\"/gm, '');\n\n                // Try to convert value into float\n                const floatValue = parseFloat(strValue);\n\n                // Use string value case float value is NaN\n                const value = isNaN(floatValue) ? strValue : floatValue;\n\n                itemData[key] = value;\n            }\n\n            // Push current item to the resulting data\n            rawData[name].push(itemData);\n        }\n\n        const font = new BitmapFontData();\n\n        rawData.info.forEach((info) => font.info.push({\n            face: info.face,\n            size: parseInt(info.size, 10),\n        }));\n\n        rawData.common.forEach((common) => font.common.push({\n            lineHeight: parseInt(common.lineHeight, 10),\n        }));\n\n        rawData.page.forEach((page) => font.page.push({\n            id: parseInt(page.id, 10),\n            file: page.file,\n        }));\n\n        rawData.char.forEach((char) => font.char.push({\n            id: parseInt(char.id, 10),\n            page: parseInt(char.page, 10),\n            x: parseInt(char.x, 10),\n            y: parseInt(char.y, 10),\n            width: parseInt(char.width, 10),\n            height: parseInt(char.height, 10),\n            xoffset: parseInt(char.xoffset, 10),\n            yoffset: parseInt(char.yoffset, 10),\n            xadvance: parseInt(char.xadvance, 10),\n        }));\n\n        rawData.kerning.forEach((kerning) => font.kerning.push({\n            first: parseInt(kerning.first, 10),\n            second: parseInt(kerning.second, 10),\n            amount: parseInt(kerning.amount, 10),\n        }));\n\n        rawData.distanceField.forEach((df) => font.distanceField.push({\n            distanceRange: parseInt(df.distanceRange, 10),\n            fieldType: df.fieldType,\n        }));\n\n        return font;\n    }\n}\n", "import { BitmapFontData } from '../BitmapFontData';\n\n/**\n * BitmapFont format that's XML-based.\n * @private\n */\nexport class XMLFormat\n{\n    /**\n     * Check if resource refers to xml font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: unknown): boolean\n    {\n        return data instanceof XMLDocument\n            && data.getElementsByTagName('page').length\n            && data.getElementsByTagName('info')[0].getAttribute('face') !== null;\n    }\n\n    /**\n     * Convert the XML into BitmapFontData that we can use.\n     * @param xml\n     * @returns - Data to use for BitmapFont\n     */\n    static parse(xml: XMLDocument): BitmapFontData\n    {\n        const data = new BitmapFontData();\n        const info = xml.getElementsByTagName('info');\n        const common = xml.getElementsByTagName('common');\n        const page = xml.getElementsByTagName('page');\n        const char = xml.getElementsByTagName('char');\n        const kerning = xml.getElementsByTagName('kerning');\n        const distanceField = xml.getElementsByTagName('distanceField');\n\n        for (let i = 0; i < info.length; i++)\n        {\n            data.info.push({\n                face: info[i].getAttribute('face'),\n                size: parseInt(info[i].getAttribute('size'), 10),\n            });\n        }\n\n        for (let i = 0; i < common.length; i++)\n        {\n            data.common.push({\n                lineHeight: parseInt(common[i].getAttribute('lineHeight'), 10),\n            });\n        }\n\n        for (let i = 0; i < page.length; i++)\n        {\n            data.page.push({\n                id: parseInt(page[i].getAttribute('id'), 10) || 0,\n                file: page[i].getAttribute('file'),\n            });\n        }\n\n        for (let i = 0; i < char.length; i++)\n        {\n            const letter = char[i];\n\n            data.char.push({\n                id: parseInt(letter.getAttribute('id'), 10),\n                page: parseInt(letter.getAttribute('page'), 10) || 0,\n                x: parseInt(letter.getAttribute('x'), 10),\n                y: parseInt(letter.getAttribute('y'), 10),\n                width: parseInt(letter.getAttribute('width'), 10),\n                height: parseInt(letter.getAttribute('height'), 10),\n                xoffset: parseInt(letter.getAttribute('xoffset'), 10),\n                yoffset: parseInt(letter.getAttribute('yoffset'), 10),\n                xadvance: parseInt(letter.getAttribute('xadvance'), 10),\n            });\n        }\n\n        for (let i = 0; i < kerning.length; i++)\n        {\n            data.kerning.push({\n                first: parseInt(kerning[i].getAttribute('first'), 10),\n                second: parseInt(kerning[i].getAttribute('second'), 10),\n                amount: parseInt(kerning[i].getAttribute('amount'), 10),\n            });\n        }\n\n        for (let i = 0; i < distanceField.length; i++)\n        {\n            data.distanceField.push({\n                fieldType: distanceField[i].getAttribute('fieldType'),\n                distanceRange: parseInt(distanceField[i].getAttribute('distanceRange'), 10),\n            });\n        }\n\n        return data;\n    }\n}\n", "import type { BitmapFontData } from '../BitmapFontData';\nimport { XMLFormat } from './XMLFormat';\n\n/**\n * BitmapFont format that's XML-based.\n * @private\n */\nexport class XMLStringFormat\n{\n    /**\n     * Check if resource refers to text xml font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: unknown): boolean\n    {\n        if (typeof data === 'string' && data.indexOf('<font>') > -1)\n        {\n            const xml = new globalThis.DOMParser().parseFromString(data, 'text/xml');\n\n            return XMLFormat.test(xml);\n        }\n\n        return false;\n    }\n\n    /**\n     * Convert the text XML into BitmapFontData that we can use.\n     * @param xmlTxt\n     * @returns - Data to use for BitmapFont\n     */\n    static parse(xmlTxt: string): BitmapFontData\n    {\n        const xml = new globalThis.DOMParser().parseFromString(xmlTxt, 'text/xml');\n\n        return XMLFormat.parse(xml);\n    }\n}\n", "import { TextFormat } from './TextFormat';\nimport { XMLFormat } from './XMLFormat';\nimport { XMLStringFormat } from './XMLStringFormat';\n\n// Registered formats, maybe make this extensible in the future?\nconst formats = [\n    TextFormat,\n    XMLFormat,\n    XMLStringFormat,\n] as const;\n\n/**\n * Auto-detect BitmapFont parsing format based on data.\n * @private\n * @param {any} data - Data to detect format\n * @returns {any} Format or null\n */\nexport function autoDetectFormat(data: unknown): typeof formats[number] | null\n{\n    for (let i = 0; i < formats.length; i++)\n    {\n        if (formats[i].test(data))\n        {\n            return formats[i];\n        }\n    }\n\n    return null;\n}\n\nexport type { IBitmapFontRawData } from './TextFormat';\nexport { TextFormat, XMLFormat, XMLStringFormat };\n", "import { generateFillStyle } from './generateFillStyle';\nimport { hex2rgb, string2hex } from '@pixi/utils';\nimport type { TextMetrics, TextStyle } from '@pixi/text';\n\n// TODO: Prevent code duplication b/w drawGlyph & Text#updateText\n\n/**\n * Draws the glyph `metrics.text` on the given canvas.\n *\n * Ignored because not directly exposed.\n * @ignore\n * @param {HTMLCanvasElement} canvas\n * @param {CanvasRenderingContext2D} context\n * @param {TextMetrics} metrics\n * @param {number} x\n * @param {number} y\n * @param {number} resolution\n * @param {TextStyle} style\n */\nexport function drawGlyph(\n    canvas: HTMLCanvasElement,\n    context: CanvasRenderingContext2D,\n    metrics: TextMetrics,\n    x: number,\n    y: number,\n    resolution: number,\n    style: TextStyle\n): void\n{\n    const char = metrics.text;\n    const fontProperties = metrics.fontProperties;\n\n    context.translate(x, y);\n    context.scale(resolution, resolution);\n\n    const tx = style.strokeThickness / 2;\n    const ty = -(style.strokeThickness / 2);\n\n    context.font = style.toFontString();\n    context.lineWidth = style.strokeThickness;\n    context.textBaseline = style.textBaseline;\n    context.lineJoin = style.lineJoin;\n    context.miterLimit = style.miterLimit;\n\n    // set canvas text styles\n    context.fillStyle = generateFillStyle(canvas, context, style, resolution, [char], metrics);\n    context.strokeStyle = style.stroke as string;\n\n    if (style.dropShadow)\n    {\n        const dropShadowColor = style.dropShadowColor;\n        const rgb = hex2rgb(typeof dropShadowColor === 'number' ? dropShadowColor : string2hex(dropShadowColor));\n        const dropShadowBlur = style.dropShadowBlur * resolution;\n        const dropShadowDistance = style.dropShadowDistance * resolution;\n\n        context.shadowColor = `rgba(${rgb[0] * 255},${rgb[1] * 255},${rgb[2] * 255},${style.dropShadowAlpha})`;\n        context.shadowBlur = dropShadowBlur;\n        context.shadowOffsetX = Math.cos(style.dropShadowAngle) * dropShadowDistance;\n        context.shadowOffsetY = Math.sin(style.dropShadowAngle) * dropShadowDistance;\n    }\n    else\n    {\n        context.shadowColor = 'black';\n        context.shadowBlur = 0;\n        context.shadowOffsetX = 0;\n        context.shadowOffsetY = 0;\n    }\n\n    if (style.stroke && style.strokeThickness)\n    {\n        context.strokeText(char, tx, ty + metrics.lineHeight - fontProperties.descent);\n    }\n    if (style.fill)\n    {\n        context.fillText(char, tx, ty + metrics.lineHeight - fontProperties.descent);\n    }\n\n    context.setTransform(1, 0, 0, 1, 0, 0); // defaults needed for older browsers (e.g. Opera 29)\n\n    context.fillStyle = 'rgba(0, 0, 0, 0)';\n}\n", "import type { TextStyle, TextMetrics } from '@pixi/text';\nimport { TEXT_GRADIENT } from '@pixi/text';\n\n// TODO: Prevent code duplication b/w generateFillStyle & Text#generateFillStyle\n\n/**\n * Generates the fill style. Can automatically generate a gradient based on the fill style being an array\n * @private\n * @param canvas\n * @param context\n * @param {object} style - The style.\n * @param resolution\n * @param {string[]} lines - The lines of text.\n * @param metrics\n * @returns {string|number|CanvasGradient} The fill style\n */\nexport function generateFillStyle(\n    canvas: HTMLCanvasElement,\n    context: CanvasRenderingContext2D,\n    style: TextStyle,\n    resolution: number,\n    lines: string[],\n    metrics: TextMetrics\n): string | CanvasGradient | CanvasPattern\n{\n    // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n    //       the setter converts to string. See this thread for more details:\n    //       https://github.com/microsoft/TypeScript/issues/2521\n    const fillStyle: string | string[] | CanvasGradient | CanvasPattern = style.fill as any;\n\n    if (!Array.isArray(fillStyle))\n    {\n        return fillStyle;\n    }\n    else if (fillStyle.length === 1)\n    {\n        return fillStyle[0];\n    }\n\n    // the gradient will be evenly spaced out according to how large the array is.\n    // ['#FF0000', '#00FF00', '#0000FF'] would created stops at 0.25, 0.5 and 0.75\n    let gradient: string[] | CanvasGradient;\n\n    // a dropshadow will enlarge the canvas and result in the gradient being\n    // generated with the incorrect dimensions\n    const dropShadowCorrection = (style.dropShadow) ? style.dropShadowDistance : 0;\n\n    // should also take padding into account, padding can offset the gradient\n    const padding = style.padding || 0;\n\n    const width = (canvas.width / resolution) - dropShadowCorrection - (padding * 2);\n    const height = (canvas.height / resolution) - dropShadowCorrection - (padding * 2);\n\n    // make a copy of the style settings, so we can manipulate them later\n    const fill = fillStyle.slice();\n    const fillGradientStops = style.fillGradientStops.slice();\n\n    // wanting to evenly distribute the fills. So an array of 4 colours should give fills of 0.25, 0.5 and 0.75\n    if (!fillGradientStops.length)\n    {\n        const lengthPlus1 = fill.length + 1;\n\n        for (let i = 1; i < lengthPlus1; ++i)\n        {\n            fillGradientStops.push(i / lengthPlus1);\n        }\n    }\n\n    // stop the bleeding of the last gradient on the line above to the top gradient of the this line\n    // by hard defining the first gradient colour at point 0, and last gradient colour at point 1\n    fill.unshift(fillStyle[0]);\n    fillGradientStops.unshift(0);\n\n    fill.push(fillStyle[fillStyle.length - 1]);\n    fillGradientStops.push(1);\n\n    if (style.fillGradientType === TEXT_GRADIENT.LINEAR_VERTICAL)\n    {\n        // start the gradient at the top center of the canvas, and end at the bottom middle of the canvas\n        gradient = context.createLinearGradient(width / 2, padding, width / 2, height + padding);\n\n        // we need to repeat the gradient so that each individual line of text has the same vertical gradient effect\n        // ['#FF0000', '#00FF00', '#0000FF'] over 2 lines would create stops at 0.125, 0.25, 0.375, 0.625, 0.75, 0.875\n\n        // There's potential for floating point precision issues at the seams between gradient repeats.\n        // The loop below generates the stops in order, so track the last generated one to prevent\n        // floating point precision from making us go the teeniest bit backwards, resulting in\n        // the first and last colors getting swapped.\n        let lastIterationStop = 0;\n\n        // Actual height of the text itself, not counting spacing for lineHeight/leading/dropShadow etc\n        const textHeight = metrics.fontProperties.fontSize + style.strokeThickness;\n\n        // textHeight, but as a 0-1 size in global gradient stop space\n        const gradStopLineHeight = textHeight / height;\n\n        for (let i = 0; i < lines.length; i++)\n        {\n            const thisLineTop = metrics.lineHeight * i;\n\n            for (let j = 0; j < fill.length; j++)\n            {\n                // 0-1 stop point for the current line, multiplied to global space afterwards\n                let lineStop = 0;\n\n                if (typeof fillGradientStops[j] === 'number')\n                {\n                    lineStop = fillGradientStops[j];\n                }\n                else\n                {\n                    lineStop = j / fill.length;\n                }\n\n                const globalStop = (thisLineTop / height) + (lineStop * gradStopLineHeight);\n\n                // Prevent color stop generation going backwards from floating point imprecision\n                let clampedStop = Math.max(lastIterationStop, globalStop);\n\n                clampedStop = Math.min(clampedStop, 1); // Cap at 1 as well for safety's sake to avoid a possible throw.\n                gradient.addColorStop(clampedStop, fill[j]);\n                lastIterationStop = clampedStop;\n            }\n        }\n    }\n    else\n    {\n        // start the gradient at the center left of the canvas, and end at the center right of the canvas\n        gradient = context.createLinearGradient(padding, height / 2, width + padding, height / 2);\n\n        // can just evenly space out the gradients in this case, as multiple lines makes no difference\n        // to an even left to right gradient\n        const totalIterations = fill.length + 1;\n        let currentIteration = 1;\n\n        for (let i = 0; i < fill.length; i++)\n        {\n            let stop: number;\n\n            if (typeof fillGradientStops[i] === 'number')\n            {\n                stop = fillGradientStops[i];\n            }\n            else\n            {\n                stop = currentIteration / totalIterations;\n            }\n            gradient.addColorStop(stop, fill[i]);\n            currentIteration++;\n        }\n    }\n\n    return gradient;\n}\n", "/**\n * Ponyfill for IE because it doesn't support `Array.from`\n * @param text\n * @private\n */\nexport function splitTextToCharacters(text: string): string[]\n{\n    return Array.from ? Array.from(text) : text.split('');\n}\n", "/**\n * Ponyfill for IE because it doesn't support `codePointAt`\n * @param str\n * @private\n */\nexport function extractCharCode(str: string): number\n{\n    return str.codePointAt ? str.codePointAt(0) : str.charCodeAt(0);\n}\n", "import { getResolutionOfUrl } from '@pixi/utils';\nimport { Rectangle } from '@pixi/math';\nimport { Texture, BaseTexture } from '@pixi/core';\nimport { TextStyle, TextMetrics } from '@pixi/text';\nimport { autoDetectFormat } from './formats';\nimport { BitmapFontData } from './BitmapFontData';\nimport { resolveCharacters, drawGlyph, extractCharCode } from './utils';\n\nimport type { Dict } from '@pixi/utils';\nimport type { ITextStyle } from '@pixi/text';\nimport { ALPHA_MODES, MIPMAP_MODES } from '@pixi/constants';\nimport { settings } from '@pixi/settings';\n\nexport interface IBitmapFontCharacter\n{\n    xOffset: number;\n    yOffset: number;\n    xAdvance: number;\n    texture: Texture;\n    page: number;\n    kerning: Dict<number>;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontOptions\n{\n    /**\n     * The character set to generate.\n     * @default PIXI.BitmapFont.ALPHANUMERIC\n     */\n    chars?: string | (string | string[])[];\n\n    /**\n     * The resolution for rendering.\n     * @default 1\n     */\n    resolution?: number;\n\n    /**\n     * The padding between glyphs in the atlas.\n     * @default 4\n     */\n    padding?: number;\n\n    /**\n     * The width of the texture atlas.\n     * @default 512\n     */\n    textureWidth?: number;\n\n    /**\n     * The height of the texture atlas.\n     * @default 512\n     */\n    textureHeight?: number;\n\n    /**\n     * Skip generation of kerning information for the BitmapFont.\n     * If true, this could potentially increase the performance, but may impact the rendered text appearance.\n     * @default false\n     */\n    skipKerning?: boolean;\n}\n\n/**\n * BitmapFont represents a typeface available for use with the BitmapText class. Use the `install`\n * method for adding a font to be used.\n * @memberof PIXI\n */\nexport class BitmapFont\n{\n    /**\n     * This character set includes all the letters in the alphabet (both lower- and upper- case).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from(\"ExampleFont\", style, { chars: BitmapFont.ALPHA })\n     */\n    public static readonly ALPHA = [['a', 'z'], ['A', 'Z'], ' '];\n\n    /**\n     * This character set includes all decimal digits (from 0 to 9).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from(\"ExampleFont\", style, { chars: BitmapFont.NUMERIC })\n     */\n    public static readonly NUMERIC = [['0', '9']];\n\n    /**\n     * This character set is the union of `BitmapFont.ALPHA` and `BitmapFont.NUMERIC`.\n     * @type {string[][]}\n     */\n    public static readonly ALPHANUMERIC = [['a', 'z'], ['A', 'Z'], ['0', '9'], ' '];\n\n    /**\n     * This character set consists of all the ASCII table.\n     * @member {string[][]}\n     * @see http://www.asciitable.com/\n     */\n    public static readonly ASCII = [[' ', '~']];\n\n    /**\n     * Collection of default options when using `BitmapFont.from`.\n     * @property {number} [resolution=1] -\n     * @property {number} [textureWidth=512] -\n     * @property {number} [textureHeight=512] -\n     * @property {number} [padding=4] -\n     * @property {string|string[]|string[][]} chars = PIXI.BitmapFont.ALPHANUMERIC\n     */\n    public static readonly defaultOptions: IBitmapFontOptions = {\n        resolution: 1,\n        textureWidth: 512,\n        textureHeight: 512,\n        padding: 4,\n        chars: BitmapFont.ALPHANUMERIC,\n    };\n\n    /** Collection of available/installed fonts. */\n    public static readonly available: Dict<BitmapFont> = {};\n\n    /** The name of the font face. */\n    public readonly font: string;\n\n    /** The size of the font face in pixels. */\n    public readonly size: number;\n\n    /** The line-height of the font face in pixels. */\n    public readonly lineHeight: number;\n\n    /** The map of characters by character code. */\n    public readonly chars: Dict<IBitmapFontCharacter>;\n\n    /** The map of base page textures (i.e., sheets of glyphs). */\n    public readonly pageTextures: Dict<Texture>;\n\n    /** The range of the distance field in pixels. */\n    public readonly distanceFieldRange: number;\n\n    /** The kind of distance field for this font or \"none\". */\n    public readonly distanceFieldType: string;\n\n    private _ownsTextures: boolean;\n\n    /**\n     * @param data\n     * @param textures\n     * @param ownsTextures - Setting to `true` will destroy page textures\n     *        when the font is uninstalled.\n     */\n    constructor(data: BitmapFontData, textures: Texture[] | Dict<Texture>, ownsTextures?: boolean)\n    {\n        const [info] = data.info;\n        const [common] = data.common;\n        const [page] = data.page;\n        const [distanceField] = data.distanceField;\n        const res = getResolutionOfUrl(page.file);\n        const pageTextures: Dict<Texture> = {};\n\n        this._ownsTextures = ownsTextures;\n        this.font = info.face;\n        this.size = info.size;\n        this.lineHeight = common.lineHeight / res;\n        this.chars = {};\n        this.pageTextures = pageTextures;\n\n        // Convert the input Texture, Textures or object\n        // into a page Texture lookup by \"id\"\n        for (let i = 0; i < data.page.length; i++)\n        {\n            const { id, file } = data.page[i];\n\n            pageTextures[id] = textures instanceof Array\n                ? textures[i] : textures[file];\n\n            // only MSDF and SDF fonts need no-premultiplied-alpha\n            if (distanceField?.fieldType && distanceField.fieldType !== 'none')\n            {\n                pageTextures[id].baseTexture.alphaMode = ALPHA_MODES.NO_PREMULTIPLIED_ALPHA;\n                pageTextures[id].baseTexture.mipmap = MIPMAP_MODES.OFF;\n            }\n        }\n\n        // parse letters\n        for (let i = 0; i < data.char.length; i++)\n        {\n            const { id, page } = data.char[i];\n            let { x, y, width, height, xoffset, yoffset, xadvance } = data.char[i];\n\n            x /= res;\n            y /= res;\n            width /= res;\n            height /= res;\n            xoffset /= res;\n            yoffset /= res;\n            xadvance /= res;\n\n            const rect = new Rectangle(\n                x + (pageTextures[page].frame.x / res),\n                y + (pageTextures[page].frame.y / res),\n                width,\n                height\n            );\n\n            this.chars[id] = {\n                xOffset: xoffset,\n                yOffset: yoffset,\n                xAdvance: xadvance,\n                kerning: {},\n                texture: new Texture(\n                    pageTextures[page].baseTexture,\n                    rect\n                ),\n                page,\n            };\n        }\n\n        // parse kernings\n        for (let i = 0; i < data.kerning.length; i++)\n        {\n            let { first, second, amount } = data.kerning[i];\n\n            first /= res;\n            second /= res;\n            amount /= res;\n\n            if (this.chars[second])\n            {\n                this.chars[second].kerning[first] = amount;\n            }\n        }\n\n        // Store distance field information\n        this.distanceFieldRange = distanceField?.distanceRange;\n        this.distanceFieldType = distanceField?.fieldType?.toLowerCase() ?? 'none';\n    }\n\n    /** Remove references to created glyph textures. */\n    public destroy(): void\n    {\n        for (const id in this.chars)\n        {\n            this.chars[id].texture.destroy();\n            this.chars[id].texture = null;\n        }\n\n        for (const id in this.pageTextures)\n        {\n            if (this._ownsTextures)\n            {\n                this.pageTextures[id].destroy(true);\n            }\n\n            this.pageTextures[id] = null;\n        }\n\n        // Set readonly null.\n        (this as any).chars = null;\n        (this as any).pageTextures = null;\n    }\n\n    /**\n     * Register a new bitmap font.\n     * @param data - The\n     *        characters map that could be provided as xml or raw string.\n     * @param textures - List of textures for each page.\n     * @param ownsTextures - Set to `true` to destroy page textures\n     *        when the font is uninstalled. By default fonts created with\n     *        `BitmapFont.from` or from the `BitmapFontLoader` are `true`.\n     * @returns {PIXI.BitmapFont} Result font object with font, size, lineHeight\n     *         and char fields.\n     */\n    public static install(\n        data: string | XMLDocument | BitmapFontData,\n        textures: Texture | Texture[] | Dict<Texture>,\n        ownsTextures?: boolean\n    ): BitmapFont\n    {\n        let fontData;\n\n        if (data instanceof BitmapFontData)\n        {\n            fontData = data;\n        }\n        else\n        {\n            const format = autoDetectFormat(data);\n\n            if (!format)\n            {\n                throw new Error('Unrecognized data format for font.');\n            }\n\n            fontData = format.parse(data as any);\n        }\n\n        // Single texture, convert to list\n        if (textures instanceof Texture)\n        {\n            textures = [textures];\n        }\n\n        const font = new BitmapFont(fontData, textures, ownsTextures);\n\n        BitmapFont.available[font.font] = font;\n\n        return font;\n    }\n\n    /**\n     * Remove bitmap font by name.\n     * @param name - Name of the font to uninstall.\n     */\n    public static uninstall(name: string): void\n    {\n        const font = BitmapFont.available[name];\n\n        if (!font)\n        {\n            throw new Error(`No font found named '${name}'`);\n        }\n\n        font.destroy();\n        delete BitmapFont.available[name];\n    }\n\n    /**\n     * Generates a bitmap-font for the given style and character set. This does not support\n     * kernings yet. With `style` properties, only the following non-layout properties are used:\n     *\n     * - {@link PIXI.TextStyle#dropShadow|dropShadow}\n     * - {@link PIXI.TextStyle#dropShadowDistance|dropShadowDistance}\n     * - {@link PIXI.TextStyle#dropShadowColor|dropShadowColor}\n     * - {@link PIXI.TextStyle#dropShadowBlur|dropShadowBlur}\n     * - {@link PIXI.TextStyle#dropShadowAngle|dropShadowAngle}\n     * - {@link PIXI.TextStyle#fill|fill}\n     * - {@link PIXI.TextStyle#fillGradientStops|fillGradientStops}\n     * - {@link PIXI.TextStyle#fillGradientType|fillGradientType}\n     * - {@link PIXI.TextStyle#fontFamily|fontFamily}\n     * - {@link PIXI.TextStyle#fontSize|fontSize}\n     * - {@link PIXI.TextStyle#fontVariant|fontVariant}\n     * - {@link PIXI.TextStyle#fontWeight|fontWeight}\n     * - {@link PIXI.TextStyle#lineJoin|lineJoin}\n     * - {@link PIXI.TextStyle#miterLimit|miterLimit}\n     * - {@link PIXI.TextStyle#stroke|stroke}\n     * - {@link PIXI.TextStyle#strokeThickness|strokeThickness}\n     * - {@link PIXI.TextStyle#textBaseline|textBaseline}\n     * @param name - The name of the custom font to use with BitmapText.\n     * @param textStyle - Style options to render with BitmapFont.\n     * @param options - Setup options for font or name of the font.\n     * @param {string|string[]|string[][]} [options.chars=PIXI.BitmapFont.ALPHANUMERIC] - characters included\n     *      in the font set. You can also use ranges. For example, `[['a', 'z'], ['A', 'Z'], \"!@#$%^&*()~{}[] \"]`.\n     *      Don't forget to include spaces ' ' in your character set!\n     * @param {number} [options.resolution=1] - Render resolution for glyphs.\n     * @param {number} [options.textureWidth=512] - Optional width of atlas, smaller values to reduce memory.\n     * @param {number} [options.textureHeight=512] - Optional height of atlas, smaller values to reduce memory.\n     * @param {number} [options.padding=4] - Padding between glyphs on texture atlas.\n     * @returns Font generated by style options.\n     * @example\n     * PIXI.BitmapFont.from(\"TitleFont\", {\n     *     fontFamily: \"Arial\",\n     *     fontSize: 12,\n     *     strokeThickness: 2,\n     *     fill: \"purple\"\n     * });\n     *\n     * const title = new PIXI.BitmapText(\"This is the title\", { fontName: \"TitleFont\" });\n     */\n    public static from(name: string, textStyle?: TextStyle | Partial<ITextStyle>, options?: IBitmapFontOptions): BitmapFont\n    {\n        if (!name)\n        {\n            throw new Error('[BitmapFont] Property `name` is required.');\n        }\n\n        const {\n            chars,\n            padding,\n            resolution,\n            textureWidth,\n            textureHeight } = Object.assign(\n            {}, BitmapFont.defaultOptions, options);\n\n        const charsList = resolveCharacters(chars);\n        const style = textStyle instanceof TextStyle ? textStyle : new TextStyle(textStyle);\n        const lineWidth = textureWidth;\n        const fontData = new BitmapFontData();\n\n        fontData.info[0] = {\n            face: style.fontFamily as string,\n            size: style.fontSize as number,\n        };\n        fontData.common[0] = {\n            lineHeight: style.fontSize as number,\n        };\n\n        let positionX = 0;\n        let positionY = 0;\n\n        let canvas: HTMLCanvasElement;\n        let context: CanvasRenderingContext2D;\n        let baseTexture: BaseTexture;\n        let maxCharHeight = 0;\n        const baseTextures: BaseTexture[] = [];\n        const textures: Texture[] = [];\n\n        for (let i = 0; i < charsList.length; i++)\n        {\n            if (!canvas)\n            {\n                canvas = settings.ADAPTER.createCanvas();\n                canvas.width = textureWidth;\n                canvas.height = textureHeight;\n\n                context = canvas.getContext('2d');\n                baseTexture = new BaseTexture(canvas, { resolution });\n\n                baseTextures.push(baseTexture);\n                textures.push(new Texture(baseTexture));\n\n                fontData.page.push({\n                    id: textures.length - 1,\n                    file: '',\n                });\n            }\n\n            // Measure glyph dimensions\n            const character = charsList[i];\n            const metrics = TextMetrics.measureText(character, style, false, canvas);\n            const width = metrics.width;\n            const height = Math.ceil(metrics.height);\n\n            // This is ugly - but italics are given more space so they don't overlap\n            const textureGlyphWidth = Math.ceil((style.fontStyle === 'italic' ? 2 : 1) * width);\n\n            // Can't fit char anymore: next canvas please!\n            if (positionY >= textureHeight - (height * resolution))\n            {\n                if (positionY === 0)\n                {\n                    // We don't want user debugging an infinite loop (or do we? :)\n                    throw new Error(`[BitmapFont] textureHeight ${textureHeight}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n\n                // Create new atlas once current has filled up\n                canvas = null;\n                context = null;\n                baseTexture = null;\n                positionY = 0;\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            maxCharHeight = Math.max(height + metrics.fontProperties.descent, maxCharHeight);\n\n            // Wrap line once full row has been rendered\n            if ((textureGlyphWidth * resolution) + positionX >= lineWidth)\n            {\n                if (positionX === 0)\n                {\n                    // Avoid infinite loop (There can be some very wide char like '\\uFDFD'!)\n                    throw new Error(`[BitmapFont] textureWidth ${textureWidth}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n                positionY += maxCharHeight * resolution;\n                positionY = Math.ceil(positionY);\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            drawGlyph(canvas, context, metrics, positionX, positionY, resolution, style);\n\n            // Unique (numeric) ID mapping to this glyph\n            const id = extractCharCode(metrics.text);\n\n            // Create a texture holding just the glyph\n            fontData.char.push({\n                id,\n                page: textures.length - 1,\n                x: positionX / resolution,\n                y: positionY / resolution,\n                width: textureGlyphWidth,\n                height,\n                xoffset: 0,\n                yoffset: 0,\n                xadvance: Math.ceil(width\n                        - (style.dropShadow ? style.dropShadowDistance : 0)\n                        - (style.stroke ? style.strokeThickness : 0)),\n            });\n\n            positionX += (textureGlyphWidth + (2 * padding)) * resolution;\n            positionX = Math.ceil(positionX);\n        }\n\n        if (!options?.skipKerning)\n        {\n            // Brute-force kerning info, this can be expensive b/c it's an O(n²),\n            // but we're using measureText which is native and fast.\n            for (let i = 0, len = charsList.length; i < len; i++)\n            {\n                const first = charsList[i];\n\n                for (let j = 0; j < len; j++)\n                {\n                    const second = charsList[j];\n                    const c1 = context.measureText(first).width;\n                    const c2 = context.measureText(second).width;\n                    const total = context.measureText(first + second).width;\n                    const amount = total - (c1 + c2);\n\n                    if (amount)\n                    {\n                        fontData.kerning.push({\n                            first: extractCharCode(first),\n                            second: extractCharCode(second),\n                            amount,\n                        });\n                    }\n                }\n            }\n        }\n\n        const font = new BitmapFont(fontData, textures, true);\n\n        // Make it easier to replace a font\n        if (BitmapFont.available[name] !== undefined)\n        {\n            BitmapFont.uninstall(name);\n        }\n\n        BitmapFont.available[name] = font;\n\n        return font;\n    }\n}\n", "import { splitTextToCharacters } from './splitTextToCharacters';\n\n/**\n * Processes the passed character set data and returns a flattened array of all the characters.\n *\n * Ignored because not directly exposed.\n * @ignore\n * @param {string | string[] | string[][] } chars\n * @returns {string[]} the flattened array of characters\n */\nexport function resolveCharacters(chars: string | (string | string[])[]): string[]\n{\n    // Split the chars string into individual characters\n    if (typeof chars === 'string')\n    {\n        chars = [chars];\n    }\n\n    // Handle an array of characters+ranges\n    const result: string[] = [];\n\n    for (let i = 0, j = chars.length; i < j; i++)\n    {\n        const item = chars[i];\n\n        // Handle range delimited by start/end chars\n        if (Array.isArray(item))\n        {\n            if (item.length !== 2)\n            {\n                throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${item.length}.`);\n            }\n\n            const startCode = item[0].charCodeAt(0);\n            const endCode = item[1].charCodeAt(0);\n\n            if (endCode < startCode)\n            {\n                throw new Error('[BitmapFont]: Invalid character range.');\n            }\n\n            for (let i = startCode, j = endCode; i <= j; i++)\n            {\n                result.push(String.fromCharCode(i));\n            }\n        }\n        // Handle a character set string\n        else\n        {\n            result.push(...splitTextToCharacters(item));\n        }\n    }\n\n    if (result.length === 0)\n    {\n        throw new Error('[BitmapFont]: Empty set when resolving characters.');\n    }\n\n    return result;\n}\n", "import { ObservablePoint, Point } from '@pixi/math';\nimport { settings } from '@pixi/settings';\nimport { Mesh, MeshGeometry, MeshMaterial } from '@pixi/mesh';\nimport { removeItems } from '@pixi/utils';\nimport { BitmapFont } from './BitmapFont';\nimport { splitTextToCharacters, extractCharCode } from './utils';\nimport msdfFrag from './shader/msdf.frag';\nimport msdfVert from './shader/msdf.vert';\nimport type { Rectangle } from '@pixi/math';\nimport type { Renderer } from '@pixi/core';\nimport { Program, Texture } from '@pixi/core';\nimport type { IBitmapTextStyle } from './BitmapTextStyle';\nimport type { TextStyleAlign } from '@pixi/text';\nimport { Container } from '@pixi/display';\nimport type { IDestroyOptions } from '@pixi/display';\nimport { BLEND_MODES } from '@pixi/constants';\n\ninterface PageMeshData\n{\n    index: number;\n    indexCount: number;\n    vertexCount: number;\n    uvsCount: number;\n    total: number;\n    mesh: Mesh;\n    vertices?: Float32Array;\n    uvs?: Float32Array;\n    indices?: Uint16Array;\n}\ninterface CharRenderData\n{\n    texture: Texture;\n    line: number;\n    charCode: number;\n    position: Point;\n    prevSpaces: number;\n}\n\n// If we ever need more than two pools, please make a Dict or something better.\nconst pageMeshDataDefaultPageMeshData: PageMeshData[] = [];\nconst pageMeshDataMSDFPageMeshData: PageMeshData[] = [];\nconst charRenderDataPool: CharRenderData[] = [];\n\n/**\n * A BitmapText object will create a line or multiple lines of text using bitmap font.\n *\n * The primary advantage of this class over Text is that all of your textures are pre-generated and loading,\n * meaning that rendering is fast, and changing text has no performance implications.\n *\n * Supporting character sets other than latin, such as CJK languages, may be impractical due to the number of characters.\n *\n * To split a line you can use '\\n', '\\r' or '\\r\\n' in your string.\n *\n * PixiJS can auto-generate fonts on-the-fly using BitmapFont or use fnt files provided by:\n * http://www.angelcode.com/products/bmfont/ for Windows or\n * http://www.bmglyph.com/ for Mac.\n *\n * You can also use SDF, MSDF and MTSDF BitmapFonts for vector-like scaling appearance provided by:\n * https://github.com/soimy/msdf-bmfont-xml for SDF and MSDF fnt files or\n * https://github.com/Chlumsky/msdf-atlas-gen for SDF, MSDF and MTSDF json files\n *\n * A BitmapText can only be created when the font is loaded.\n *\n * ```js\n * // in this case the font is in a file called 'desyrel.fnt'\n * let bitmapText = new PIXI.BitmapText(\"text using a fancy font!\", {\n *   fontName: \"Desyrel\",\n *   fontSize: 35,\n *   align: \"right\"\n * });\n * ```\n * @memberof PIXI\n */\nexport class BitmapText extends Container\n{\n    public static styleDefaults: Partial<IBitmapTextStyle> = {\n        align: 'left',\n        tint: 0xFFFFFF,\n        maxWidth: 0,\n        letterSpacing: 0,\n    };\n\n    /** Set to `true` if the BitmapText needs to be redrawn. */\n    public dirty: boolean;\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default PIXI.settings.RESOLUTION\n     */\n    _resolution: number;\n    _autoResolution: boolean;\n\n    /**\n     * Private tracker for the width of the overall text.\n     * @private\n     */\n    protected _textWidth: number;\n\n    /**\n     * Private tracker for the height of the overall text.\n     * @private\n     */\n    protected _textHeight: number;\n\n    /**\n     * Private tracker for the current text.\n     * @private\n     */\n    protected _text: string;\n\n    /**\n     * The max width of this bitmap text in pixels. If the text provided is longer than the\n     * value provided, line breaks will be automatically inserted in the last whitespace.\n     * Disable by setting value to 0\n     * @private\n     */\n    protected _maxWidth: number;\n\n    /**\n     * The max line height. This is useful when trying to use the total height of the Text,\n     * ie: when trying to vertically align. (Internally used)\n     * @private\n     */\n    protected _maxLineHeight: number;\n\n    /**\n     * Letter spacing. This is useful for setting the space between characters.\n     * @private\n     */\n    protected _letterSpacing: number;\n\n    /**\n     * Text anchor.\n     * @readonly\n     * @private\n     */\n    protected _anchor: ObservablePoint;\n\n    /**\n     * Private tracker for the current font.\n     * @private\n     */\n    protected _font?: BitmapFont;\n\n    /**\n     * Private tracker for the current font name.\n     * @private\n     */\n    protected _fontName: string;\n\n    /**\n     * Private tracker for the current font size.\n     * @private\n     */\n    protected _fontSize?: number;\n\n    /**\n     * Private tracker for the current text align.\n     * @type {string}\n     * @private\n     */\n    protected _align: TextStyleAlign;\n\n    /** Collection of page mesh data. */\n    protected _activePagesMeshData: PageMeshData[];\n\n    /**\n     * Private tracker for the current tint.\n     * @private\n     */\n    protected _tint = 0xFFFFFF;\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering.\n     * @default PIXI.settings.ROUND_PIXELS\n     */\n    protected _roundPixels: boolean;\n\n    /** Cached char texture is destroyed when BitmapText is destroyed. */\n    private _textureCache: Record<number, Texture>;\n\n    /**\n     * @param text - A string that you would like the text to display.\n     * @param style - The style parameters.\n     * @param {string} style.fontName - The installed BitmapFont name.\n     * @param {number} [style.fontSize] - The size of the font in pixels, e.g. 24. If undefined,\n     *.     this will default to the BitmapFont size.\n     * @param {string} [style.align='left'] - Alignment for multiline text ('left', 'center', 'right' or 'justify'),\n     *      does not affect single line text.\n     * @param {number} [style.tint=0xFFFFFF] - The tint color.\n     * @param {number} [style.letterSpacing=0] - The amount of spacing between letters.\n     * @param {number} [style.maxWidth=0] - The max width of the text before line wrapping.\n     */\n    constructor(text: string, style: Partial<IBitmapTextStyle> = {})\n    {\n        super();\n\n        // Apply the defaults\n        const { align, tint, maxWidth, letterSpacing, fontName, fontSize } = Object.assign(\n            {}, BitmapText.styleDefaults, style);\n\n        if (!BitmapFont.available[fontName])\n        {\n            throw new Error(`Missing BitmapFont \"${fontName}\"`);\n        }\n\n        this._activePagesMeshData = [];\n        this._textWidth = 0;\n        this._textHeight = 0;\n        this._align = align;\n        this._tint = tint;\n        this._font = undefined;\n        this._fontName = fontName;\n        this._fontSize = fontSize;\n        this.text = text;\n        this._maxWidth = maxWidth;\n        this._maxLineHeight = 0;\n        this._letterSpacing = letterSpacing;\n        this._anchor = new ObservablePoint((): void => { this.dirty = true; }, this, 0, 0);\n        this._roundPixels = settings.ROUND_PIXELS;\n        this.dirty = true;\n        this._resolution = settings.RESOLUTION;\n        this._autoResolution = true;\n        this._textureCache = {};\n    }\n\n    /** Renders text and updates it when needed. This should only be called if the BitmapFont is regenerated. */\n    public updateText(): void\n    {\n        const data = BitmapFont.available[this._fontName];\n        const fontSize = this.fontSize;\n        const scale = fontSize / data.size;\n        const pos = new Point();\n        const chars: CharRenderData[] = [];\n        const lineWidths = [];\n        const lineSpaces = [];\n        const text = this._text.replace(/(?:\\r\\n|\\r)/g, '\\n') || ' ';\n        const charsInput = splitTextToCharacters(text);\n        const maxWidth = this._maxWidth * data.size / fontSize;\n        const pageMeshDataPool = data.distanceFieldType === 'none'\n            ? pageMeshDataDefaultPageMeshData : pageMeshDataMSDFPageMeshData;\n\n        let prevCharCode = null;\n        let lastLineWidth = 0;\n        let maxLineWidth = 0;\n        let line = 0;\n        let lastBreakPos = -1;\n        let lastBreakWidth = 0;\n        let spacesRemoved = 0;\n        let maxLineHeight = 0;\n        let spaceCount = 0;\n\n        for (let i = 0; i < charsInput.length; i++)\n        {\n            const char = charsInput[i];\n            const charCode = extractCharCode(char);\n\n            if ((/(?:\\s)/).test(char))\n            {\n                lastBreakPos = i;\n                lastBreakWidth = lastLineWidth;\n                spaceCount++;\n            }\n\n            if (char === '\\r' || char === '\\n')\n            {\n                lineWidths.push(lastLineWidth);\n                lineSpaces.push(-1);\n                maxLineWidth = Math.max(maxLineWidth, lastLineWidth);\n                ++line;\n                ++spacesRemoved;\n\n                pos.x = 0;\n                pos.y += data.lineHeight;\n                prevCharCode = null;\n                spaceCount = 0;\n                continue;\n            }\n\n            const charData = data.chars[charCode];\n\n            if (!charData)\n            {\n                continue;\n            }\n\n            if (prevCharCode && charData.kerning[prevCharCode])\n            {\n                pos.x += charData.kerning[prevCharCode];\n            }\n\n            const charRenderData: CharRenderData = charRenderDataPool.pop() || {\n                texture: Texture.EMPTY,\n                line: 0,\n                charCode: 0,\n                prevSpaces: 0,\n                position: new Point(),\n            };\n\n            charRenderData.texture = charData.texture;\n            charRenderData.line = line;\n            charRenderData.charCode = charCode;\n            charRenderData.position.x = pos.x + charData.xOffset + (this._letterSpacing / 2);\n            charRenderData.position.y = pos.y + charData.yOffset;\n            charRenderData.prevSpaces = spaceCount;\n\n            chars.push(charRenderData);\n\n            lastLineWidth = charRenderData.position.x\n                + Math.max(charData.xAdvance - charData.xOffset, charData.texture.orig.width);\n            pos.x += charData.xAdvance + this._letterSpacing;\n            maxLineHeight = Math.max(maxLineHeight, (charData.yOffset + charData.texture.height));\n            prevCharCode = charCode;\n\n            if (lastBreakPos !== -1 && maxWidth > 0 && pos.x > maxWidth)\n            {\n                ++spacesRemoved;\n                removeItems(chars, 1 + lastBreakPos - spacesRemoved, 1 + i - lastBreakPos);\n                i = lastBreakPos;\n                lastBreakPos = -1;\n\n                lineWidths.push(lastBreakWidth);\n                lineSpaces.push(chars.length > 0 ? chars[chars.length - 1].prevSpaces : 0);\n                maxLineWidth = Math.max(maxLineWidth, lastBreakWidth);\n                line++;\n\n                pos.x = 0;\n                pos.y += data.lineHeight;\n                prevCharCode = null;\n                spaceCount = 0;\n            }\n        }\n\n        const lastChar = charsInput[charsInput.length - 1];\n\n        if (lastChar !== '\\r' && lastChar !== '\\n')\n        {\n            if ((/(?:\\s)/).test(lastChar))\n            {\n                lastLineWidth = lastBreakWidth;\n            }\n\n            lineWidths.push(lastLineWidth);\n            maxLineWidth = Math.max(maxLineWidth, lastLineWidth);\n            lineSpaces.push(-1);\n        }\n\n        const lineAlignOffsets = [];\n\n        for (let i = 0; i <= line; i++)\n        {\n            let alignOffset = 0;\n\n            if (this._align === 'right')\n            {\n                alignOffset = maxLineWidth - lineWidths[i];\n            }\n            else if (this._align === 'center')\n            {\n                alignOffset = (maxLineWidth - lineWidths[i]) / 2;\n            }\n            else if (this._align === 'justify')\n            {\n                alignOffset = lineSpaces[i] < 0 ? 0 : (maxLineWidth - lineWidths[i]) / lineSpaces[i];\n            }\n\n            lineAlignOffsets.push(alignOffset);\n        }\n\n        const lenChars = chars.length;\n\n        const pagesMeshData: Record<number, PageMeshData> = {};\n\n        const newPagesMeshData: PageMeshData[] = [];\n\n        const activePagesMeshData = this._activePagesMeshData;\n\n        pageMeshDataPool.push(...activePagesMeshData);\n\n        for (let i = 0; i < lenChars; i++)\n        {\n            const texture = chars[i].texture;\n            const baseTextureUid = texture.baseTexture.uid;\n\n            if (!pagesMeshData[baseTextureUid])\n            {\n                let pageMeshData = pageMeshDataPool.pop();\n\n                if (!pageMeshData)\n                {\n                    const geometry = new MeshGeometry();\n                    let material: MeshMaterial;\n                    let meshBlendMode: BLEND_MODES;\n\n                    if (data.distanceFieldType === 'none')\n                    {\n                        material = new MeshMaterial(Texture.EMPTY);\n                        meshBlendMode = BLEND_MODES.NORMAL;\n                    }\n                    else\n                    {\n                        material = new MeshMaterial(Texture.EMPTY,\n                            { program: Program.from(msdfVert, msdfFrag), uniforms: { uFWidth: 0 } });\n                        meshBlendMode = BLEND_MODES.NORMAL_NPM;\n                    }\n\n                    const mesh = new Mesh(geometry, material);\n\n                    mesh.blendMode = meshBlendMode;\n\n                    pageMeshData = {\n                        index: 0,\n                        indexCount: 0,\n                        vertexCount: 0,\n                        uvsCount: 0,\n                        total: 0,\n                        mesh,\n                        vertices: null,\n                        uvs: null,\n                        indices: null,\n                    };\n                }\n\n                // reset data..\n                pageMeshData.index = 0;\n                pageMeshData.indexCount = 0;\n                pageMeshData.vertexCount = 0;\n                pageMeshData.uvsCount = 0;\n                pageMeshData.total = 0;\n\n                // TODO need to get page texture here somehow..\n                const { _textureCache } = this;\n\n                _textureCache[baseTextureUid] = _textureCache[baseTextureUid] || new Texture(texture.baseTexture);\n                pageMeshData.mesh.texture = _textureCache[baseTextureUid];\n\n                pageMeshData.mesh.tint = this._tint;\n\n                newPagesMeshData.push(pageMeshData);\n\n                pagesMeshData[baseTextureUid] = pageMeshData;\n            }\n\n            pagesMeshData[baseTextureUid].total++;\n        }\n\n        // lets find any previously active pageMeshDatas that are no longer required for\n        // the updated text (if any), removed and return them to the pool.\n        for (let i = 0; i < activePagesMeshData.length; i++)\n        {\n            if (newPagesMeshData.indexOf(activePagesMeshData[i]) === -1)\n            {\n                this.removeChild(activePagesMeshData[i].mesh);\n            }\n        }\n\n        // next lets add any new meshes, that have not yet been added to this BitmapText\n        // we only add if its not already a child of this BitmapObject\n        for (let i = 0; i < newPagesMeshData.length; i++)\n        {\n            if (newPagesMeshData[i].mesh.parent !== this)\n            {\n                this.addChild(newPagesMeshData[i].mesh);\n            }\n        }\n\n        // active page mesh datas are set to be the new pages added.\n        this._activePagesMeshData = newPagesMeshData;\n\n        for (const i in pagesMeshData)\n        {\n            const pageMeshData = pagesMeshData[i];\n            const total = pageMeshData.total;\n\n            // lets only allocate new buffers if we can fit the new text in the current ones..\n            // unless that is, we will be batching. Currently batching dose not respect the size property of mesh\n            if (!(pageMeshData.indices?.length > 6 * total) || pageMeshData.vertices.length < Mesh.BATCHABLE_SIZE * 2)\n            {\n                pageMeshData.vertices = new Float32Array(4 * 2 * total);\n                pageMeshData.uvs = new Float32Array(4 * 2 * total);\n                pageMeshData.indices = new Uint16Array(6 * total);\n            }\n            else\n            {\n                const total = pageMeshData.total;\n                const vertices = pageMeshData.vertices;\n\n                // Clear the garbage at the end of the vertices buffer. This will prevent the bounds miscalculation.\n                for (let i = total * 4 * 2; i < vertices.length; i++)\n                {\n                    vertices[i] = 0;\n                }\n            }\n\n            // as a buffer maybe bigger than the current word, we set the size of the meshMaterial\n            // to match the number of letters needed\n            pageMeshData.mesh.size = 6 * total;\n        }\n\n        for (let i = 0; i < lenChars; i++)\n        {\n            const char = chars[i];\n            let offset = char.position.x + (lineAlignOffsets[char.line] * (this._align === 'justify' ? char.prevSpaces : 1));\n\n            if (this._roundPixels)\n            {\n                offset = Math.round(offset);\n            }\n\n            const xPos = offset * scale;\n            const yPos = char.position.y * scale;\n            const texture = char.texture;\n\n            const pageMesh = pagesMeshData[texture.baseTexture.uid];\n\n            const textureFrame = texture.frame;\n            const textureUvs = texture._uvs;\n\n            const index = pageMesh.index++;\n\n            pageMesh.indices[(index * 6) + 0] = 0 + (index * 4);\n            pageMesh.indices[(index * 6) + 1] = 1 + (index * 4);\n            pageMesh.indices[(index * 6) + 2] = 2 + (index * 4);\n            pageMesh.indices[(index * 6) + 3] = 0 + (index * 4);\n            pageMesh.indices[(index * 6) + 4] = 2 + (index * 4);\n            pageMesh.indices[(index * 6) + 5] = 3 + (index * 4);\n\n            pageMesh.vertices[(index * 8) + 0] = xPos;\n            pageMesh.vertices[(index * 8) + 1] = yPos;\n\n            pageMesh.vertices[(index * 8) + 2] = xPos + (textureFrame.width * scale);\n            pageMesh.vertices[(index * 8) + 3] = yPos;\n\n            pageMesh.vertices[(index * 8) + 4] = xPos + (textureFrame.width * scale);\n            pageMesh.vertices[(index * 8) + 5] = yPos + (textureFrame.height * scale);\n\n            pageMesh.vertices[(index * 8) + 6] = xPos;\n            pageMesh.vertices[(index * 8) + 7] = yPos + (textureFrame.height * scale);\n\n            pageMesh.uvs[(index * 8) + 0] = textureUvs.x0;\n            pageMesh.uvs[(index * 8) + 1] = textureUvs.y0;\n\n            pageMesh.uvs[(index * 8) + 2] = textureUvs.x1;\n            pageMesh.uvs[(index * 8) + 3] = textureUvs.y1;\n\n            pageMesh.uvs[(index * 8) + 4] = textureUvs.x2;\n            pageMesh.uvs[(index * 8) + 5] = textureUvs.y2;\n\n            pageMesh.uvs[(index * 8) + 6] = textureUvs.x3;\n            pageMesh.uvs[(index * 8) + 7] = textureUvs.y3;\n        }\n\n        this._textWidth = maxLineWidth * scale;\n        this._textHeight = (pos.y + data.lineHeight) * scale;\n\n        for (const i in pagesMeshData)\n        {\n            const pageMeshData = pagesMeshData[i];\n\n            // apply anchor\n            if (this.anchor.x !== 0 || this.anchor.y !== 0)\n            {\n                let vertexCount = 0;\n\n                const anchorOffsetX = this._textWidth * this.anchor.x;\n                const anchorOffsetY = this._textHeight * this.anchor.y;\n\n                for (let i = 0; i < pageMeshData.total; i++)\n                {\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n                }\n            }\n\n            this._maxLineHeight = maxLineHeight * scale;\n\n            const vertexBuffer = pageMeshData.mesh.geometry.getBuffer('aVertexPosition');\n            const textureBuffer = pageMeshData.mesh.geometry.getBuffer('aTextureCoord');\n            const indexBuffer = pageMeshData.mesh.geometry.getIndex();\n\n            vertexBuffer.data = pageMeshData.vertices;\n            textureBuffer.data = pageMeshData.uvs;\n            indexBuffer.data = pageMeshData.indices;\n\n            vertexBuffer.update();\n            textureBuffer.update();\n            indexBuffer.update();\n        }\n\n        for (let i = 0; i < chars.length; i++)\n        {\n            charRenderDataPool.push(chars[i]);\n        }\n\n        this._font = data;\n        this.dirty = false;\n    }\n\n    updateTransform(): void\n    {\n        this.validate();\n        this.containerUpdateTransform();\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this._autoResolution && this._resolution !== renderer.resolution)\n        {\n            this._resolution = renderer.resolution;\n            this.dirty = true;\n        }\n\n        // Update the uniform\n        const { distanceFieldRange, distanceFieldType, size } = BitmapFont.available[this._fontName];\n\n        if (distanceFieldType !== 'none')\n        {\n            // Inject the shader code with the correct value\n            const { a, b, c, d } = this.worldTransform;\n\n            const dx = Math.sqrt((a * a) + (b * b));\n            const dy = Math.sqrt((c * c) + (d * d));\n            const worldScale = (Math.abs(dx) + Math.abs(dy)) / 2;\n\n            const fontScale = this.fontSize / size;\n\n            for (const mesh of this._activePagesMeshData)\n            {\n                mesh.mesh.shader.uniforms.uFWidth = worldScale * distanceFieldRange * fontScale * this._resolution;\n            }\n        }\n\n        super._render(renderer);\n    }\n\n    /**\n     * Validates text before calling parent's getLocalBounds\n     * @returns - The rectangular bounding area\n     */\n    public getLocalBounds(): Rectangle\n    {\n        this.validate();\n\n        return super.getLocalBounds();\n    }\n\n    /**\n     * Updates text when needed\n     * @private\n     */\n    protected validate(): void\n    {\n        const font = BitmapFont.available[this._fontName];\n\n        if (!font)\n        {\n            throw new Error(`Missing BitmapFont \"${this._fontName}\"`);\n        }\n        if (this._font !== font)\n        {\n            this.dirty = true;\n        }\n\n        if (this.dirty)\n        {\n            this.updateText();\n        }\n    }\n\n    /**\n     * The tint of the BitmapText object.\n     * @default 0xffffff\n     */\n    public get tint(): number\n    {\n        return this._tint;\n    }\n\n    public set tint(value: number)\n    {\n        if (this._tint === value) return;\n\n        this._tint = value;\n\n        for (let i = 0; i < this._activePagesMeshData.length; i++)\n        {\n            this._activePagesMeshData[i].mesh.tint = value;\n        }\n    }\n\n    /**\n     * The alignment of the BitmapText object.\n     * @member {string}\n     * @default 'left'\n     */\n    public get align(): TextStyleAlign\n    {\n        return this._align;\n    }\n\n    public set align(value: TextStyleAlign)\n    {\n        if (this._align !== value)\n        {\n            this._align = value;\n            this.dirty = true;\n        }\n    }\n\n    /** The name of the BitmapFont. */\n    public get fontName(): string\n    {\n        return this._fontName;\n    }\n\n    public set fontName(value: string)\n    {\n        if (!BitmapFont.available[value])\n        {\n            throw new Error(`Missing BitmapFont \"${value}\"`);\n        }\n\n        if (this._fontName !== value)\n        {\n            this._fontName = value;\n            this.dirty = true;\n        }\n    }\n\n    /** The size of the font to display. */\n    public get fontSize(): number\n    {\n        return this._fontSize ?? BitmapFont.available[this._fontName].size;\n    }\n\n    public set fontSize(value: number | undefined)\n    {\n        if (this._fontSize !== value)\n        {\n            this._fontSize = value;\n            this.dirty = true;\n        }\n    }\n\n    /**\n     * The anchor sets the origin point of the text.\n     *\n     * The default is `(0,0)`, this means the text's origin is the top left.\n     *\n     * Setting the anchor to `(0.5,0.5)` means the text's origin is centered.\n     *\n     * Setting the anchor to `(1,1)` would mean the text's origin point will be the bottom right corner.\n     */\n    public get anchor(): ObservablePoint\n    {\n        return this._anchor;\n    }\n\n    public set anchor(value: ObservablePoint)\n    {\n        if (typeof value === 'number')\n        {\n            this._anchor.set(value);\n        }\n        else\n        {\n            this._anchor.copyFrom(value);\n        }\n    }\n\n    /** The text of the BitmapText object. */\n    public get text(): string\n    {\n        return this._text;\n    }\n\n    public set text(text: string)\n    {\n        text = String(text === null || text === undefined ? '' : text);\n\n        if (this._text === text)\n        {\n            return;\n        }\n        this._text = text;\n        this.dirty = true;\n    }\n\n    /**\n     * The max width of this bitmap text in pixels. If the text provided is longer than the\n     * value provided, line breaks will be automatically inserted in the last whitespace.\n     * Disable by setting the value to 0.\n     */\n    public get maxWidth(): number\n    {\n        return this._maxWidth;\n    }\n\n    public set maxWidth(value: number)\n    {\n        if (this._maxWidth === value)\n        {\n            return;\n        }\n        this._maxWidth = value;\n        this.dirty = true;\n    }\n\n    /**\n     * The max line height. This is useful when trying to use the total height of the Text,\n     * i.e. when trying to vertically align.\n     * @readonly\n     */\n    public get maxLineHeight(): number\n    {\n        this.validate();\n\n        return this._maxLineHeight;\n    }\n\n    /**\n     * The width of the overall text, different from fontSize,\n     * which is defined in the style object.\n     * @readonly\n     */\n    public get textWidth(): number\n    {\n        this.validate();\n\n        return this._textWidth;\n    }\n\n    /** Additional space between characters. */\n    public get letterSpacing(): number\n    {\n        return this._letterSpacing;\n    }\n\n    public set letterSpacing(value: number)\n    {\n        if (this._letterSpacing !== value)\n        {\n            this._letterSpacing = value;\n            this.dirty = true;\n        }\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}\n     * @default PIXI.settings.ROUND_PIXELS\n     */\n    public get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    public set roundPixels(value: boolean)\n    {\n        if (value !== this._roundPixels)\n        {\n            this._roundPixels = value;\n            this.dirty = true;\n        }\n    }\n\n    /**\n     * The height of the overall text, different from fontSize,\n     * which is defined in the style object.\n     * @readonly\n     */\n    public get textHeight(): number\n    {\n        this.validate();\n\n        return this._textHeight;\n    }\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default 1\n     */\n    get resolution(): number\n    {\n        return this._resolution;\n    }\n\n    set resolution(value: number)\n    {\n        this._autoResolution = false;\n\n        if (this._resolution === value)\n        {\n            return;\n        }\n\n        this._resolution = value;\n        this.dirty = true;\n    }\n\n    destroy(options?: boolean | IDestroyOptions): void\n    {\n        const { _textureCache } = this;\n        const data = BitmapFont.available[this._fontName];\n        const pageMeshDataPool = data.distanceFieldType === 'none'\n            ? pageMeshDataDefaultPageMeshData : pageMeshDataMSDFPageMeshData;\n\n        pageMeshDataPool.push(...this._activePagesMeshData);\n        for (const pageMeshData of this._activePagesMeshData)\n        {\n            this.removeChild(pageMeshData.mesh);\n        }\n        this._activePagesMeshData = [];\n\n        // Release references to any cached textures in page pool\n        pageMeshDataPool\n            .filter((page) => _textureCache[page.mesh.texture.baseTexture.uid])\n            .forEach((page) =>\n            {\n                page.mesh.texture = Texture.EMPTY;\n            });\n\n        for (const id in _textureCache)\n        {\n            const texture = _textureCache[id];\n\n            texture.destroy();\n            delete _textureCache[id];\n        }\n\n        this._font = null;\n        this._textureCache = null;\n\n        super.destroy(options);\n    }\n}\n", "import { LoaderResource } from '@pixi/loaders';\nimport { autoDetectFormat } from './formats';\nimport { BitmapFont } from './BitmapFont';\n\nimport type { Loader } from '@pixi/loaders';\nimport type { Dict } from '@pixi/utils';\nimport type { ExtensionMetadata, Texture } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * {@link PIXI.Loader Loader} middleware for loading\n * bitmap-based fonts suitable for using with {@link PIXI.BitmapText}.\n * @memberof PIXI\n */\nexport class BitmapFontLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * Called when the plugin is installed.\n     * @see PIXI.extensions.add\n     */\n    public static add(): void\n    {\n        LoaderResource.setExtensionXhrType('fnt', LoaderResource.XHR_RESPONSE_TYPE.TEXT);\n    }\n\n    /**\n     * Called after a resource is loaded.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param this\n     * @param {PIXI.LoaderResource} resource\n     * @param {Function} next\n     */\n    static use(this: Loader, resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        const format = autoDetectFormat(resource.data);\n\n        // Resource was not recognised as any of the expected font data format\n        if (!format)\n        {\n            next();\n\n            return;\n        }\n\n        const baseUrl = BitmapFontLoader.getBaseUrl(this, resource);\n        const data = format.parse(resource.data);\n        const textures: Dict<Texture> = {};\n\n        // Handle completed, when the number of textures\n        // load is the same number as references in the fnt file\n        const completed = (page: LoaderResource): void =>\n        {\n            textures[page.metadata.pageFile] = page.texture;\n\n            if (Object.keys(textures).length === data.page.length)\n            {\n                resource.bitmapFont = BitmapFont.install(data, textures, true);\n                next();\n            }\n        };\n\n        for (let i = 0; i < data.page.length; ++i)\n        {\n            const pageFile = data.page[i].file;\n            const url = baseUrl + pageFile;\n            let exists = false;\n\n            // incase the image is loaded outside\n            // using the same loader, resource will be available\n            for (const name in this.resources)\n            {\n                const bitmapResource: LoaderResource = this.resources[name];\n\n                if (bitmapResource.url === url)\n                {\n                    bitmapResource.metadata.pageFile = pageFile;\n                    if (bitmapResource.texture)\n                    {\n                        completed(bitmapResource);\n                    }\n                    else\n                    {\n                        bitmapResource.onAfterMiddleware.add(completed);\n                    }\n                    exists = true;\n                    break;\n                }\n            }\n\n            // texture is not loaded, we'll attempt to add\n            // it to the load and add the texture to the list\n            if (!exists)\n            {\n                // Standard loading options for images\n                const options = {\n                    crossOrigin: resource.crossOrigin,\n                    loadType: LoaderResource.LOAD_TYPE.IMAGE,\n                    metadata: Object.assign(\n                        { pageFile },\n                        resource.metadata.imageMetadata\n                    ),\n                    parentResource: resource,\n                };\n\n                this.add(url, options, completed);\n            }\n        }\n    }\n\n    /**\n     * Get folder path from a resource.\n     * @param loader\n     * @param resource\n     */\n    private static getBaseUrl(loader: Loader, resource: LoaderResource): string\n    {\n        let resUrl = !resource.isDataUrl ? BitmapFontLoader.dirname(resource.url) : '';\n\n        if (resource.isDataUrl)\n        {\n            if (resUrl === '.')\n            {\n                resUrl = '';\n            }\n\n            if (loader.baseUrl && resUrl)\n            {\n                // if baseurl has a trailing slash then add one to resUrl so the replace works below\n                if (loader.baseUrl.charAt(loader.baseUrl.length - 1) === '/')\n                {\n                    resUrl += '/';\n                }\n            }\n        }\n\n        // remove baseUrl from resUrl\n        resUrl = resUrl.replace(loader.baseUrl, '');\n\n        // if there is an resUrl now, it needs a trailing slash. Ensure that it does if the string isn't empty.\n        if (resUrl && resUrl.charAt(resUrl.length - 1) !== '/')\n        {\n            resUrl += '/';\n        }\n\n        return resUrl;\n    }\n\n    /**\n     * Replacement for NodeJS's path.dirname\n     * @param {string} url - Path to get directory for\n     */\n    private static dirname(url: string): string\n    {\n        const dir = url\n            .replace(/\\\\/g, '/') // convert windows notation to UNIX notation, URL-safe because it's a forbidden character\n            .replace(/\\/$/, '') // replace trailing slash\n            .replace(/\\/[^\\/]*$/, ''); // remove everything after the last\n\n        // File request is relative, use current directory\n        if (dir === url)\n        {\n            return '.';\n        }\n        // Started with a slash\n        else if (dir === '')\n        {\n            return '/';\n        }\n\n        return dir;\n    }\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "BitmapFontData", "this", "info", "common", "page", "char", "kerning", "distanceField", "TextFormat", "test", "data", "indexOf", "parse", "txt", "items", "match", "rawData", "chars", "kernings", "i", "name", "attributeList", "itemData", "i_1", "split", "key", "strValue", "replace", "floatValue", "parseFloat", "value", "isNaN", "push", "font", "for<PERSON>ach", "face", "size", "parseInt", "lineHeight", "id", "file", "x", "y", "width", "height", "xoffset", "yoffset", "xadvance", "first", "second", "amount", "df", "distanceRange", "fieldType", "XMLFormat", "XMLDocument", "getElementsByTagName", "length", "getAttribute", "xml", "letter", "XMLStringFormat", "globalThis", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "xmlTxt", "formats", "autoDetectFormat", "drawGlyph", "canvas", "context", "metrics", "resolution", "style", "text", "fontProperties", "translate", "scale", "tx", "strokeThickness", "ty", "toFontString", "lineWidth", "textBaseline", "lineJoin", "miterLimit", "fillStyle", "lines", "gradient", "fill", "isArray", "dropShadowCorrection", "dropShadowDistance", "padding", "slice", "fillGradientStops", "lengthPlus1", "unshift", "fillGradientType", "TEXT_GRADIENT", "LINEAR_VERTICAL", "createLinearGradient", "lastIterationStop", "gradStopLineHeight", "fontSize", "thisLineTop", "j", "globalStop", "clampedStop", "Math", "max", "min", "addColorStop", "totalIterations", "currentIteration", "stop", "generateFillStyle", "strokeStyle", "stroke", "dropShadow", "dropShadowColor", "rgb", "hex2rgb", "string2hex", "dropShadowBlur", "shadowColor", "dropShadowAlpha", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "cos", "dropShadowAngle", "shadowOffsetY", "sin", "strokeText", "descent", "fillText", "setTransform", "splitTextToCharacters", "from", "extractCharCode", "str", "codePointAt", "charCodeAt", "BitmapFont", "textures", "ownsTextures", "res", "getResolutionOfUrl", "pageTextures", "_ownsTextures", "_c", "baseTexture", "alphaMode", "ALPHA_MODES", "NO_PREMULTIPLIED_ALPHA", "mipmap", "MIPMAP_MODES", "OFF", "_d", "page_1", "_e", "rect", "Rectangle", "frame", "xOffset", "yOffset", "xAdvance", "texture", "Texture", "_f", "distanceFieldRange", "distanceFieldType", "_b", "_a", "toLowerCase", "prototype", "destroy", "install", "fontData", "format", "Error", "available", "uninstall", "textStyle", "options", "assign", "defaultOptions", "textureWidth", "textureHeight", "charsList", "result", "item", "startCode", "endCode", "j_1", "String", "fromCharCode", "apply", "resolveCharacters", "TextStyle", "fontFamily", "positionX", "positionY", "maxCharHeight", "settings", "ADAPTER", "createCanvas", "getContext", "BaseTexture", "character", "TextMetrics", "measureText", "ceil", "textureGlyphWidth", "fontStyle", "skip<PERSON><PERSON>ing", "len", "c1", "c2", "undefined", "ALPHA", "NUMERIC", "ALPHANUMERIC", "ASCII", "pageMeshDataDefaultPageMeshData", "pageMeshDataMSDFPageMeshData", "charRenderDataPool", "BitmapText", "_super", "_this", "_tint", "styleDefaults", "align", "tint", "max<PERSON><PERSON><PERSON>", "letterSpacing", "fontName", "_activePagesMeshData", "_textWidth", "_textHeight", "_align", "_font", "_fontName", "_fontSize", "_maxWidth", "_maxLineHeight", "_letterSpacing", "_anchor", "ObservablePoint", "dirty", "_roundPixels", "ROUND_PIXELS", "_resolution", "RESOLUTION", "_autoResolution", "_textureCache", "__", "constructor", "create", "__extends", "updateText", "pos", "Point", "lineWidths", "lineSpaces", "charsInput", "_text", "pageMeshDataPool", "prevCharCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "lastBreakPos", "lastBreakWidth", "spacesRemoved", "maxLineHeight", "spaceCount", "charCode", "char<PERSON><PERSON>", "charRenderData", "pop", "EMPTY", "prevSpaces", "position", "orig", "removeItems", "lastChar", "lineAlignOffsets", "alignOffset", "lenChars", "pagesMeshData", "newPagesMeshData", "activePagesMeshData", "baseTextureUid", "uid", "pageMeshData", "geometry", "MeshGeometry", "material", "meshBlendMode", "MeshMaterial", "BLEND_MODES", "NORMAL", "program", "Program", "uniforms", "uFWidth", "NORMAL_NPM", "mesh", "<PERSON><PERSON>", "blendMode", "index", "indexCount", "vertexCount", "uvsCount", "total", "vertices", "uvs", "indices", "<PERSON><PERSON><PERSON><PERSON>", "parent", "<PERSON><PERSON><PERSON><PERSON>", "BATCHABLE_SIZE", "Float32Array", "Uint16Array", "total_1", "offset", "round", "xPos", "yPos", "page<PERSON><PERSON>", "textureFrame", "textureUvs", "_uvs", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "anchor", "anchorOffsetX", "anchorOffsetY", "i_2", "vertexBuffer", "<PERSON><PERSON><PERSON><PERSON>", "textureBuffer", "indexBuffer", "getIndex", "update", "updateTransform", "validate", "containerUpdateTransform", "_render", "renderer", "worldTransform", "a", "c", "dx", "sqrt", "dy", "worldScale", "abs", "fontScale", "_i", "shader", "call", "getLocalBounds", "defineProperty", "get", "set", "copyFrom", "filter", "Container", "BitmapFontLoader", "add", "LoaderResource", "setExtensionXhrType", "XHR_RESPONSE_TYPE", "TEXT", "use", "resource", "next", "baseUrl", "getBaseUrl", "completed", "metadata", "pageFile", "keys", "bitmapFont", "url", "exists", "resources", "bitmapResource", "onAfterMiddleware", "crossOrigin", "loadType", "LOAD_TYPE", "IMAGE", "imageMetadata", "parentResource", "loader", "resUrl", "isDataUrl", "dirname", "char<PERSON>t", "dir", "extension", "ExtensionType", "Loader"], "mappings": ";;;;;;;gTAgBIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,ICd5B,IAAAO,EAoBI,WAEIC,KAAKC,KAAO,GACZD,KAAKE,OAAS,GACdF,KAAKG,KAAO,GACZH,KAAKI,KAAO,GACZJ,KAAKK,QAAU,GACfL,KAAKM,cAAgB,ICgB7BC,EAAA,WAAA,SAAAA,KA0GA,OAnGWA,EAAIC,KAAX,SAAYC,GAER,MAAuB,iBAATA,GAAoD,IAA/BA,EAAKC,QAAQ,eAQ7CH,EAAKI,MAAZ,SAAaC,GAGT,IAAMC,EAAQD,EAAIE,MAAM,mBAClBC,EAA8B,CAChCd,KAAM,GACNC,OAAQ,GACRC,KAAM,GACNC,KAAM,GACNY,MAAO,GACPX,QAAS,GACTY,SAAU,GACVX,cAAe,IAGnB,IAAK,IAAMY,KAAKL,EAChB,CAEI,IAAMM,EAAON,EAAMK,GAAGJ,MAAM,aAAa,GAGnCM,EAAgBP,EAAMK,GAAGJ,MAAM,oCAG/BO,EAAgB,GAEtB,IAAK,IAAMC,KAAKF,EAChB,CAEI,IAAMG,EAAQH,EAAcE,GAAGC,MAAM,KAC/BC,EAAMD,EAAM,GAGZE,EAAWF,EAAM,GAAGG,QAAQ,MAAO,IAGnCC,EAAaC,WAAWH,GAGxBI,EAAQC,MAAMH,GAAcF,EAAWE,EAE7CN,EAASG,GAAOK,EAIpBd,EAAQI,GAAMY,KAAKV,GAGvB,IAAMW,EAAO,IAAIjC,EAuCjB,OArCAgB,EAAQd,KAAKgC,SAAQ,SAAChC,GAAS,OAAA+B,EAAK/B,KAAK8B,KAAK,CAC1CG,KAAMjC,EAAKiC,KACXC,KAAMC,SAASnC,EAAKkC,KAAM,SAG9BpB,EAAQb,OAAO+B,SAAQ,SAAC/B,GAAW,OAAA8B,EAAK9B,OAAO6B,KAAK,CAChDM,WAAYD,SAASlC,EAAOmC,WAAY,SAG5CtB,EAAQZ,KAAK8B,SAAQ,SAAC9B,GAAS,OAAA6B,EAAK7B,KAAK4B,KAAK,CAC1CO,GAAIF,SAASjC,EAAKmC,GAAI,IACtBC,KAAMpC,EAAKoC,UAGfxB,EAAQX,KAAK6B,SAAQ,SAAC7B,GAAS,OAAA4B,EAAK5B,KAAK2B,KAAK,CAC1CO,GAAIF,SAAShC,EAAKkC,GAAI,IACtBnC,KAAMiC,SAAShC,EAAKD,KAAM,IAC1BqC,EAAGJ,SAAShC,EAAKoC,EAAG,IACpBC,EAAGL,SAAShC,EAAKqC,EAAG,IACpBC,MAAON,SAAShC,EAAKsC,MAAO,IAC5BC,OAAQP,SAAShC,EAAKuC,OAAQ,IAC9BC,QAASR,SAAShC,EAAKwC,QAAS,IAChCC,QAAST,SAAShC,EAAKyC,QAAS,IAChCC,SAAUV,SAAShC,EAAK0C,SAAU,SAGtC/B,EAAQV,QAAQ4B,SAAQ,SAAC5B,GAAY,OAAA2B,EAAK3B,QAAQ0B,KAAK,CACnDgB,MAAOX,SAAS/B,EAAQ0C,MAAO,IAC/BC,OAAQZ,SAAS/B,EAAQ2C,OAAQ,IACjCC,OAAQb,SAAS/B,EAAQ4C,OAAQ,SAGrClC,EAAQT,cAAc2B,SAAQ,SAACiB,GAAO,OAAAlB,EAAK1B,cAAcyB,KAAK,CAC1DoB,cAAef,SAASc,EAAGC,cAAe,IAC1CC,UAAWF,EAAGE,eAGXpB,GAEdzB,KCrJD8C,EAAA,WAAA,SAAAA,KAwFA,OAjFWA,EAAI7C,KAAX,SAAYC,GAER,OAAOA,aAAgB6C,aAChB7C,EAAK8C,qBAAqB,QAAQC,QAC4B,OAA9D/C,EAAK8C,qBAAqB,QAAQ,GAAGE,aAAa,SAQtDJ,EAAK1C,MAAZ,SAAa+C,GAUT,IARA,IAAMjD,EAAO,IAAIV,EACXE,EAAOyD,EAAIH,qBAAqB,QAChCrD,EAASwD,EAAIH,qBAAqB,UAClCpD,EAAOuD,EAAIH,qBAAqB,QAChCnD,EAAOsD,EAAIH,qBAAqB,QAChClD,EAAUqD,EAAIH,qBAAqB,WACnCjD,EAAgBoD,EAAIH,qBAAqB,iBAEtCrC,EAAI,EAAGA,EAAIjB,EAAKuD,OAAQtC,IAE7BT,EAAKR,KAAK8B,KAAK,CACXG,KAAMjC,EAAKiB,GAAGuC,aAAa,QAC3BtB,KAAMC,SAASnC,EAAKiB,GAAGuC,aAAa,QAAS,MAIrD,IAASvC,EAAI,EAAGA,EAAIhB,EAAOsD,OAAQtC,IAE/BT,EAAKP,OAAO6B,KAAK,CACbM,WAAYD,SAASlC,EAAOgB,GAAGuC,aAAa,cAAe,MAInE,IAASvC,EAAI,EAAGA,EAAIf,EAAKqD,OAAQtC,IAE7BT,EAAKN,KAAK4B,KAAK,CACXO,GAAIF,SAASjC,EAAKe,GAAGuC,aAAa,MAAO,KAAO,EAChDlB,KAAMpC,EAAKe,GAAGuC,aAAa,UAInC,IAASvC,EAAI,EAAGA,EAAId,EAAKoD,OAAQtC,IACjC,CACI,IAAMyC,EAASvD,EAAKc,GAEpBT,EAAKL,KAAK2B,KAAK,CACXO,GAAIF,SAASuB,EAAOF,aAAa,MAAO,IACxCtD,KAAMiC,SAASuB,EAAOF,aAAa,QAAS,KAAO,EACnDjB,EAAGJ,SAASuB,EAAOF,aAAa,KAAM,IACtChB,EAAGL,SAASuB,EAAOF,aAAa,KAAM,IACtCf,MAAON,SAASuB,EAAOF,aAAa,SAAU,IAC9Cd,OAAQP,SAASuB,EAAOF,aAAa,UAAW,IAChDb,QAASR,SAASuB,EAAOF,aAAa,WAAY,IAClDZ,QAAST,SAASuB,EAAOF,aAAa,WAAY,IAClDX,SAAUV,SAASuB,EAAOF,aAAa,YAAa,MAI5D,IAASvC,EAAI,EAAGA,EAAIb,EAAQmD,OAAQtC,IAEhCT,EAAKJ,QAAQ0B,KAAK,CACdgB,MAAOX,SAAS/B,EAAQa,GAAGuC,aAAa,SAAU,IAClDT,OAAQZ,SAAS/B,EAAQa,GAAGuC,aAAa,UAAW,IACpDR,OAAQb,SAAS/B,EAAQa,GAAGuC,aAAa,UAAW,MAI5D,IAASvC,EAAI,EAAGA,EAAIZ,EAAckD,OAAQtC,IAEtCT,EAAKH,cAAcyB,KAAK,CACpBqB,UAAW9C,EAAcY,GAAGuC,aAAa,aACzCN,cAAef,SAAS9B,EAAcY,GAAGuC,aAAa,iBAAkB,MAIhF,OAAOhD,GAEd4C,KCvFDO,EAAA,WAAA,SAAAA,KA8BA,OAvBWA,EAAIpD,KAAX,SAAYC,GAER,GAAoB,iBAATA,GAAqBA,EAAKC,QAAQ,WAAa,EAC1D,CACI,IAAMgD,GAAM,IAAIG,WAAWC,WAAYC,gBAAgBtD,EAAM,YAE7D,OAAO4C,EAAU7C,KAAKkD,GAG1B,OAAO,GAQJE,EAAKjD,MAAZ,SAAaqD,GAET,IAAMN,GAAM,IAAIG,WAAWC,WAAYC,gBAAgBC,EAAQ,YAE/D,OAAOX,EAAU1C,MAAM+C,IAE9BE,KChCKK,EAAU,CACZ1D,EACA8C,EACAO,GASE,SAAUM,EAAiBzD,GAE7B,IAAK,IAAIS,EAAI,EAAGA,EAAI+C,EAAQT,OAAQtC,IAEhC,GAAI+C,EAAQ/C,GAAGV,KAAKC,GAEhB,OAAOwD,EAAQ/C,GAIvB,OAAO,KCRK,SAAAiD,EACZC,EACAC,EACAC,EACA9B,EACAC,EACA8B,EACAC,GAGA,IAAMpE,EAAOkE,EAAQG,KACfC,EAAiBJ,EAAQI,eAE/BL,EAAQM,UAAUnC,EAAGC,GACrB4B,EAAQO,MAAML,EAAYA,GAE1B,IAAMM,EAAKL,EAAMM,gBAAkB,EAC7BC,GAAOP,EAAMM,gBAAkB,EAYrC,GAVAT,EAAQrC,KAAOwC,EAAMQ,eACrBX,EAAQY,UAAYT,EAAMM,gBAC1BT,EAAQa,aAAeV,EAAMU,aAC7Bb,EAAQc,SAAWX,EAAMW,SACzBd,EAAQe,WAAaZ,EAAMY,WAG3Bf,EAAQgB,UC7BI,SACZjB,EACAC,EACAG,EACAD,EACAe,EACAhB,GAMA,IAaIiB,EAbEF,EAAgEb,EAAMgB,KAE5E,IAAK5F,MAAM6F,QAAQJ,GAEf,OAAOA,EAEN,GAAyB,IAArBA,EAAU7B,OAEf,OAAO6B,EAAU,GASrB,IAAMK,EAAwBlB,EAAgB,WAAIA,EAAMmB,mBAAqB,EAGvEC,EAAUpB,EAAMoB,SAAW,EAE3BlD,EAAS0B,EAAO1B,MAAQ6B,EAAcmB,EAAkC,EAAVE,EAC9DjD,EAAUyB,EAAOzB,OAAS4B,EAAcmB,EAAkC,EAAVE,EAGhEJ,EAAOH,EAAUQ,QACjBC,EAAoBtB,EAAMsB,kBAAkBD,QAGlD,IAAKC,EAAkBtC,OAInB,IAFA,IAAMuC,EAAcP,EAAKhC,OAAS,EAEzBtC,EAAI,EAAGA,EAAI6E,IAAe7E,EAE/B4E,EAAkB/D,KAAKb,EAAI6E,GAYnC,GANAP,EAAKQ,QAAQX,EAAU,IACvBS,EAAkBE,QAAQ,GAE1BR,EAAKzD,KAAKsD,EAAUA,EAAU7B,OAAS,IACvCsC,EAAkB/D,KAAK,GAEnByC,EAAMyB,mBAAqBC,EAAaA,cAACC,gBAC7C,CAEIZ,EAAWlB,EAAQ+B,qBAAqB1D,EAAQ,EAAGkD,EAASlD,EAAQ,EAAGC,EAASiD,GAShF,IAAIS,EAAoB,EAMlBC,GAHahC,EAAQI,eAAe6B,SAAW/B,EAAMM,iBAGnBnC,EAExC,IAASzB,EAAI,EAAGA,EAAIoE,EAAM9B,OAAQtC,IAI9B,IAFA,IAAMsF,EAAclC,EAAQjC,WAAanB,EAEhCuF,EAAI,EAAGA,EAAIjB,EAAKhC,OAAQiD,IACjC,CAEI,IAWMC,EAAcF,EAAc7D,GATE,iBAAzBmD,EAAkBW,GAEdX,EAAkBW,GAIlBA,EAAIjB,EAAKhC,QAGgC8C,EAGpDK,EAAcC,KAAKC,IAAIR,EAAmBK,GAE9CC,EAAcC,KAAKE,IAAIH,EAAa,GACpCpB,EAASwB,aAAaJ,EAAanB,EAAKiB,IACxCJ,EAAoBM,OAKhC,CAEIpB,EAAWlB,EAAQ+B,qBAAqBR,EAASjD,EAAS,EAAGD,EAAQkD,EAASjD,EAAS,GAIvF,IAAMqE,EAAkBxB,EAAKhC,OAAS,EAClCyD,EAAmB,EAEvB,IAAS/F,EAAI,EAAGA,EAAIsE,EAAKhC,OAAQtC,IACjC,CACI,IAAIgG,SAIAA,EAFgC,iBAAzBpB,EAAkB5E,GAElB4E,EAAkB5E,GAIlB+F,EAAmBD,EAE9BzB,EAASwB,aAAaG,EAAM1B,EAAKtE,IACjC+F,KAIR,OAAO1B,ED3Ga4B,CAAkB/C,EAAQC,EAASG,EAAOD,EAAY,CAACnE,GAAOkE,GAClFD,EAAQ+C,YAAc5C,EAAM6C,OAExB7C,EAAM8C,WACV,CACI,IAAMC,EAAkB/C,EAAM+C,gBACxBC,EAAMC,EAAOA,QAA4B,iBAApBF,EAA+BA,EAAkBG,EAAAA,WAAWH,IACjFI,EAAiBnD,EAAMmD,eAAiBpD,EACxCoB,EAAqBnB,EAAMmB,mBAAqBpB,EAEtDF,EAAQuD,YAAc,QAAiB,IAATJ,EAAI,GAAY,IAAS,IAATA,EAAI,GAAQ,IAAa,IAATA,EAAI,GAAY,IAAAhD,EAAMqD,oBACpFxD,EAAQyD,WAAaH,EACrBtD,EAAQ0D,cAAgBnB,KAAKoB,IAAIxD,EAAMyD,iBAAmBtC,EAC1DtB,EAAQ6D,cAAgBtB,KAAKuB,IAAI3D,EAAMyD,iBAAmBtC,OAI1DtB,EAAQuD,YAAc,QACtBvD,EAAQyD,WAAa,EACrBzD,EAAQ0D,cAAgB,EACxB1D,EAAQ6D,cAAgB,EAGxB1D,EAAM6C,QAAU7C,EAAMM,iBAEtBT,EAAQ+D,WAAWhI,EAAMyE,EAAIE,EAAKT,EAAQjC,WAAaqC,EAAe2D,SAEtE7D,EAAMgB,MAENnB,EAAQiE,SAASlI,EAAMyE,EAAIE,EAAKT,EAAQjC,WAAaqC,EAAe2D,SAGxEhE,EAAQkE,aAAa,EAAG,EAAG,EAAG,EAAG,EAAG,GAEpClE,EAAQgB,UAAY,mBE1ElB,SAAUmD,EAAsB/D,GAElC,OAAO7E,MAAM6I,KAAO7I,MAAM6I,KAAKhE,GAAQA,EAAKlD,MAAM,ICFhD,SAAUmH,EAAgBC,GAE5B,OAAOA,EAAIC,YAAcD,EAAIC,YAAY,GAAKD,EAAIE,WAAW,GC8DjE,IAAAC,EAAA,WA+EI,SAAAA,EAAYrI,EAAsBsI,EAAqCC,WAE5D/I,EAAQQ,EAAKR,QACbC,EAAUO,EAAKP,UACfC,EAAQM,EAAKN,QACbG,EAAiBG,EAAKH,iBACvB2I,EAAMC,EAAAA,mBAAmB/I,EAAKoC,MAC9B4G,EAA8B,GAEpCnJ,KAAKoJ,cAAgBJ,EACrBhJ,KAAKgC,KAAO/B,EAAKiC,KACjBlC,KAAKmC,KAAOlC,EAAKkC,KACjBnC,KAAKqC,WAAanC,EAAOmC,WAAa4G,EACtCjJ,KAAKgB,MAAQ,GACbhB,KAAKmJ,aAAeA,EAIpB,IAAK,IAAIjI,EAAI,EAAGA,EAAIT,EAAKN,KAAKqD,OAAQtC,IACtC,CACU,IAAAmI,EAAe5I,EAAKN,KAAKe,GAAvBoB,EAAE+G,EAAA/G,GAAEC,SAEZ4G,EAAa7G,GAAMyG,aAAoBnJ,MACjCmJ,EAAS7H,GAAK6H,EAASxG,IAGzBjC,MAAAA,SAAAA,EAAe8C,YAAyC,SAA5B9C,EAAc8C,YAE1C+F,EAAa7G,GAAIgH,YAAYC,UAAYC,EAAAA,YAAYC,uBACrDN,EAAa7G,GAAIgH,YAAYI,OAASC,EAAAA,aAAaC,KAK3D,IAAS1I,EAAI,EAAGA,EAAIT,EAAKL,KAAKoD,OAAQtC,IACtC,CACU,IAAA2I,EAAepJ,EAAKL,KAAKc,GAAnB4I,GAAJxH,EAAEuH,EAAAvH,WACNyH,EAAsDtJ,EAAKL,KAAKc,GAA9DsB,EAACuH,EAAAvH,EAAEC,EAACsH,EAAAtH,EAAEC,EAAKqH,EAAArH,MAAEC,EAAMoH,EAAApH,OAAEC,EAAOmH,EAAAnH,QAAEC,EAAOkH,EAAAlH,QAAEC,EAAQiH,EAAAjH,SAErDN,GAAKyG,EACLxG,GAAKwG,EACLvG,GAASuG,EACTtG,GAAUsG,EACVrG,GAAWqG,EACXpG,GAAWoG,EACXnG,GAAYmG,EAEZ,IAAMe,EAAO,IAAIC,EAAAA,UACbzH,EAAK2G,EAAaW,GAAMI,MAAM1H,EAAIyG,EAClCxG,EAAK0G,EAAaW,GAAMI,MAAMzH,EAAIwG,EAClCvG,EACAC,GAGJ3C,KAAKgB,MAAMsB,GAAM,CACb6H,QAASvH,EACTwH,QAASvH,EACTwH,SAAUvH,EACVzC,QAAS,GACTiK,QAAS,IAAIC,EAAAA,QACTpB,EAAaW,GAAMR,YACnBU,GAEJ7J,KAAI2J,GAKZ,IAAS5I,EAAI,EAAGA,EAAIT,EAAKJ,QAAQmD,OAAQtC,IACzC,CACQ,IAAAsJ,EAA4B/J,EAAKJ,QAAQa,GAAvC6B,EAAKyH,EAAAzH,MAAEC,EAAMwH,EAAAxH,OAAEC,WAErBF,GAASkG,EACTjG,GAAUiG,EACVhG,GAAUgG,EAENjJ,KAAKgB,MAAMgC,KAEXhD,KAAKgB,MAAMgC,GAAQ3C,QAAQ0C,GAASE,GAK5CjD,KAAKyK,mBAAqBnK,MAAAA,OAAA,EAAAA,EAAe6C,cACzCnD,KAAK0K,kBAA2D,QAAvCC,EAAwB,QAAxBC,EAAAtK,MAAAA,OAAA,EAAAA,EAAe8C,iBAAS,IAAAwH,OAAA,EAAAA,EAAEC,qBAAa,IAAAF,EAAAA,EAAI,OAqT5E,OAjTW7B,EAAAgC,UAAAC,QAAP,WAEI,IAAK,IAAMzI,KAAMtC,KAAKgB,MAElBhB,KAAKgB,MAAMsB,GAAIgI,QAAQS,UACvB/K,KAAKgB,MAAMsB,GAAIgI,QAAU,KAG7B,IAAK,IAAMhI,KAAMtC,KAAKmJ,aAEdnJ,KAAKoJ,eAELpJ,KAAKmJ,aAAa7G,GAAIyI,SAAQ,GAGlC/K,KAAKmJ,aAAa7G,GAAM,KAI3BtC,KAAagB,MAAQ,KACrBhB,KAAamJ,aAAe,MAcnBL,EAAAkC,QAAd,SACIvK,EACAsI,EACAC,GAGA,IAAIiC,EAEJ,GAAIxK,aAAgBV,EAEhBkL,EAAWxK,MAGf,CACI,IAAMyK,EAAShH,EAAiBzD,GAEhC,IAAKyK,EAED,MAAM,IAAIC,MAAM,sCAGpBF,EAAWC,EAAOvK,MAAMF,GAIxBsI,aAAoBwB,EAAAA,UAEpBxB,EAAW,CAACA,IAGhB,IAAM/G,EAAO,IAAI8G,EAAWmC,EAAUlC,EAAUC,GAIhD,OAFAF,EAAWsC,UAAUpJ,EAAKA,MAAQA,EAE3BA,GAOG8G,EAASuC,UAAvB,SAAwBlK,GAEpB,IAAMa,EAAO8G,EAAWsC,UAAUjK,GAElC,IAAKa,EAED,MAAM,IAAImJ,MAAM,wBAAwBhK,EAAI,KAGhDa,EAAK+I,iBACEjC,EAAWsC,UAAUjK,IA6ClB2H,EAAAL,KAAd,SAAmBtH,EAAcmK,EAA6CC,GAE1E,IAAKpK,EAED,MAAM,IAAIgK,MAAM,6CAGd,IAAAP,EAKgBnL,OAAO+L,OACzB,GAAI1C,EAAW2C,eAAgBF,GAL/BvK,EAAK4J,EAAA5J,MACL4E,EAAOgF,EAAAhF,QACPrB,EAAUqG,EAAArG,WACVmH,EAAYd,EAAAc,aACZC,EAAaf,EAAAe,cAGXC,ECnXR,SAA4B5K,GAGT,iBAAVA,IAEPA,EAAQ,CAACA,IAMb,IAFA,IAAM6K,EAAmB,GAEhB3K,EAAI,EAAGuF,EAAIzF,EAAMwC,OAAQtC,EAAIuF,EAAGvF,IACzC,CACI,IAAM4K,EAAO9K,EAAME,GAGnB,GAAItB,MAAM6F,QAAQqG,GAClB,CACI,GAAoB,IAAhBA,EAAKtI,OAEL,MAAM,IAAI2H,MAAM,iEAAiEW,EAAKtI,OAAS,KAGnG,IAAMuI,EAAYD,EAAK,GAAGjD,WAAW,GAC/BmD,EAAUF,EAAK,GAAGjD,WAAW,GAEnC,GAAImD,EAAUD,EAEV,MAAM,IAAIZ,MAAM,0CAGpB,IAAK,IAAI7J,EAAIyK,EAAWE,EAAID,EAAS1K,GAAK2K,EAAG3K,IAEzCuK,EAAO9J,KAAKmK,OAAOC,aAAa7K,SAMpCuK,EAAO9J,KAAPqK,MAAAP,EAAerD,EAAsBsD,IAI7C,GAAsB,IAAlBD,EAAOrI,OAEP,MAAM,IAAI2H,MAAM,sDAGpB,OAAOU,EDmUeQ,CAAkBrL,GAC9BwD,EAAQ8G,aAAqBgB,EAASA,UAAGhB,EAAY,IAAIgB,EAAAA,UAAUhB,GACnErG,EAAYyG,EACZT,EAAW,IAAIlL,EAErBkL,EAAShL,KAAK,GAAK,CACfiC,KAAMsC,EAAM+H,WACZpK,KAAMqC,EAAM+B,UAEhB0E,EAAS/K,OAAO,GAAK,CACjBmC,WAAYmC,EAAM+B,UAatB,IAVA,IAGInC,EACAC,EACAiF,EALAkD,EAAY,EACZC,EAAY,EAKZC,EAAgB,EAEd3D,EAAsB,GAEnB7H,EAAI,EAAGA,EAAI0K,EAAUpI,OAAQtC,IACtC,CACSkD,KAEDA,EAASuI,EAAQA,SAACC,QAAQC,gBACnBnK,MAAQgJ,EACftH,EAAOzB,OAASgJ,EAEhBtH,EAAUD,EAAO0I,WAAW,MAC5BxD,EAAc,IAAIyD,EAAAA,YAAY3I,EAAQ,CAAEG,WAAUA,IAGlDwE,EAAShH,KAAK,IAAIwI,UAAQjB,IAE1B2B,EAAS9K,KAAK4B,KAAK,CACfO,GAAIyG,EAASvF,OAAS,EACtBjB,KAAM,MAKd,IAAMyK,EAAYpB,EAAU1K,GACtBoD,EAAU2I,EAAAA,YAAYC,YAAYF,EAAWxI,GAAO,EAAOJ,GAC3D1B,EAAQ4B,EAAQ5B,MAChBC,EAASiE,KAAKuG,KAAK7I,EAAQ3B,QAG3ByK,EAAoBxG,KAAKuG,MAA0B,WAApB3I,EAAM6I,UAAyB,EAAI,GAAK3K,GAG7E,GAAI+J,GAAad,EAAiBhJ,EAAS4B,EAA3C,CAEI,GAAkB,IAAdkI,EAGA,MAAM,IAAItB,MAAM,8BAA8BQ,EAA9B,iCACOnH,EAAM+H,WAAU,gBAAgB/H,EAAM+B,SAAQ,cAAcyG,EAAS,QAG9F9L,EAGFkD,EAAS,KACTC,EAAU,KACViF,EAAc,KACdmD,EAAY,EACZD,EAAY,EACZE,EAAgB,OAQpB,GAHAA,EAAgB9F,KAAKC,IAAIlE,EAAS2B,EAAQI,eAAe2D,QAASqE,GAG7DU,EAAoB7I,EAAciI,GAAavH,EAApD,CAEI,GAAkB,IAAduH,EAGA,MAAM,IAAIrB,MAAM,6BAA6BO,EAA7B,iCACOlH,EAAM+H,WAAU,gBAAgB/H,EAAM+B,SAAQ,cAAcyG,EAAS,QAG9F9L,EACFuL,GAAaC,EAAgBnI,EAC7BkI,EAAY7F,KAAKuG,KAAKV,GACtBD,EAAY,EACZE,EAAgB,MAbpB,CAkBAvI,EAAUC,EAAQC,EAASC,EAASkI,EAAWC,EAAWlI,EAAYC,GAGtE,IAAMlC,EAAKoG,EAAgBpE,EAAQG,MAGnCwG,EAAS7K,KAAK2B,KAAK,CACfO,GAAEA,EACFnC,KAAM4I,EAASvF,OAAS,EACxBhB,EAAGgK,EAAYjI,EACf9B,EAAGgK,EAAYlI,EACf7B,MAAO0K,EACPzK,OAAMA,EACNC,QAAS,EACTC,QAAS,EACTC,SAAU8D,KAAKuG,KAAKzK,GACT8B,EAAM8C,WAAa9C,EAAMmB,mBAAqB,IAC9CnB,EAAM6C,OAAS7C,EAAMM,gBAAkB,MAGtD0H,IAAcY,EAAqB,EAAIxH,GAAYrB,EACnDiI,EAAY5F,KAAKuG,KAAKX,IAG1B,KAAKjB,MAAAA,SAAAA,EAAS+B,aAIL,CAAIpM,EAAI,EAAb,IAAK,IAAWqM,EAAM3B,EAAUpI,OAAQtC,EAAIqM,EAAKrM,IAI7C,IAFA,IAAM6B,EAAQ6I,EAAU1K,GAEfuF,EAAI,EAAGA,EAAI8G,EAAK9G,IACzB,CACI,IAAMzD,EAAS4I,EAAUnF,GACnB+G,EAAKnJ,EAAQ6I,YAAYnK,GAAOL,MAChC+K,EAAKpJ,EAAQ6I,YAAYlK,GAAQN,MAEjCO,EADQoB,EAAQ6I,YAAYnK,EAAQC,GAAQN,OAC1B8K,EAAKC,GAEzBxK,GAEAgI,EAAS5K,QAAQ0B,KAAK,CAClBgB,MAAO2F,EAAgB3F,GACvBC,OAAQ0F,EAAgB1F,GACxBC,OAAMA,KAO1B,IAAMjB,EAAO,IAAI8G,EAAWmC,EAAUlC,GAAU,GAUhD,YAPmC2E,IAA/B5E,EAAWsC,UAAUjK,IAErB2H,EAAWuC,UAAUlK,GAGzB2H,EAAWsC,UAAUjK,GAAQa,EAEtBA,GA9cY8G,EAAA6E,MAAQ,CAAC,CAAC,IAAK,KAAM,CAAC,IAAK,KAAM,KAQjC7E,EAAO8E,QAAG,CAAC,CAAC,IAAK,MAMjB9E,EAAY+E,aAAG,CAAC,CAAC,IAAK,KAAM,CAAC,IAAK,KAAM,CAAC,IAAK,KAAM,KAOpD/E,EAAKgF,MAAG,CAAC,CAAC,IAAK,MAUfhF,EAAA2C,eAAqC,CACxDlH,WAAY,EACZmH,aAAc,IACdC,cAAe,IACf/F,QAAS,EACT5E,MAAO8H,EAAW+E,cAIC/E,EAASsC,UAAqB,GAwaxDtC,KEtfKiF,EAAkD,GAClDC,EAA+C,GAC/CC,EAAuC,GAgC7CC,EAAA,SAAAC,GA0HI,SAAYD,EAAAzJ,EAAcD,QAAA,IAAAA,IAAAA,EAAqC,IAA/D,IAAA4J,EAEID,cA6BHnO,KAtDSoO,EAAKC,MAAG,SA4BR,IAAAzD,EAA+DnL,OAAO+L,OACxE,GAAI0C,EAAWI,cAAe9J,GAD1B+J,EAAK3D,EAAA2D,MAAEC,EAAI5D,EAAA4D,KAAEC,aAAUC,EAAa9D,EAAA8D,cAAEC,EAAQ/D,EAAA+D,SAAEpI,aAGxD,IAAKuC,EAAWsC,UAAUuD,GAEtB,MAAM,IAAIxD,MAAM,uBAAuBwD,EAAQ,YAGnDP,EAAKQ,qBAAuB,GAC5BR,EAAKS,WAAa,EAClBT,EAAKU,YAAc,EACnBV,EAAKW,OAASR,EACdH,EAAKC,MAAQG,EACbJ,EAAKY,WAAQtB,EACbU,EAAKa,UAAYN,EACjBP,EAAKc,UAAY3I,EACjB6H,EAAK3J,KAAOA,EACZ2J,EAAKe,UAAYV,EACjBL,EAAKgB,eAAiB,EACtBhB,EAAKiB,eAAiBX,EACtBN,EAAKkB,QAAU,IAAIC,EAAeA,iBAAC,WAAcnB,EAAKoB,OAAQ,IAASpB,EAAM,EAAG,GAChFA,EAAKqB,aAAe9C,EAAQA,SAAC+C,aAC7BtB,EAAKoB,OAAQ,EACbpB,EAAKuB,YAAchD,EAAQA,SAACiD,WAC5BxB,EAAKyB,iBAAkB,EACvBzB,EAAK0B,cAAgB,KAqtB7B,OZ/5BO,SAAmBvQ,EAAGC,GAEzB,SAASuQ,IAAO/P,KAAKgQ,YAAczQ,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEuL,UAAkB,OAANtL,EAAaC,OAAOwQ,OAAOzQ,IAAMuQ,EAAGjF,UAAYtL,EAAEsL,UAAW,IAAIiF,GY+CnDG,CAAShC,EAAAC,GA4J9BD,EAAApD,UAAAqF,WAAP,WAyBI,UAvBM1P,EAAOqI,EAAWsC,UAAUpL,KAAKiP,WACjC1I,EAAWvG,KAAKuG,SAChB3B,EAAQ2B,EAAW9F,EAAK0B,KACxBiO,EAAM,IAAIC,EAAAA,MACVrP,EAA0B,GAC1BsP,EAAa,GACbC,EAAa,GAEbC,EAAahI,EADNxI,KAAKyQ,MAAM/O,QAAQ,eAAgB,OAAS,KAEnD+M,EAAWzO,KAAKmP,UAAY1O,EAAK0B,KAAOoE,EACxCmK,EAA8C,SAA3BjQ,EAAKiK,kBACxBqD,EAAkCC,EAEpC2C,EAAe,KACfC,EAAgB,EAChBC,EAAe,EACfC,EAAO,EACPC,GAAgB,EAChBC,EAAiB,EACjBC,EAAgB,EAChBC,EAAgB,EAChBC,EAAa,EAERjQ,EAAI,EAAGA,EAAIsP,EAAWhN,OAAQtC,IACvC,CACI,IACMkQ,EAAW1I,EADXtI,EAAOoQ,EAAWtP,IAUxB,GAPI,SAAWV,KAAKJ,KAEhB2Q,EAAe7P,EACf8P,EAAiBJ,EACjBO,KAGS,OAAT/Q,GAA0B,OAATA,EAArB,CAeA,IAAMiR,EAAW5Q,EAAKO,MAAMoQ,GAE5B,GAAKC,EAAL,CAKIV,GAAgBU,EAAShR,QAAQsQ,KAEjCP,EAAI5N,GAAK6O,EAAShR,QAAQsQ,IAG9B,IAAMW,EAAiCrD,EAAmBsD,OAAS,CAC/DjH,QAASC,EAAOA,QAACiH,MACjBV,KAAM,EACNM,SAAU,EACVK,WAAY,EACZC,SAAU,IAAIrB,EAAAA,OAGlBiB,EAAehH,QAAU+G,EAAS/G,QAClCgH,EAAeR,KAAOA,EACtBQ,EAAeF,SAAWA,EAC1BE,EAAeI,SAASlP,EAAI4N,EAAI5N,EAAI6O,EAASlH,QAAWnK,KAAKqP,eAAiB,EAC9EiC,EAAeI,SAASjP,EAAI2N,EAAI3N,EAAI4O,EAASjH,QAC7CkH,EAAeG,WAAaN,EAE5BnQ,EAAMe,KAAKuP,GAEXV,EAAgBU,EAAeI,SAASlP,EAClCoE,KAAKC,IAAIwK,EAAShH,SAAWgH,EAASlH,QAASkH,EAAS/G,QAAQqH,KAAKjP,OAC3E0N,EAAI5N,GAAK6O,EAAShH,SAAWrK,KAAKqP,eAClC6B,EAAgBtK,KAAKC,IAAIqK,EAAgBG,EAASjH,QAAUiH,EAAS/G,QAAQ3H,QAC7EgO,EAAeS,GAEO,IAAlBL,GAAuBtC,EAAW,GAAK2B,EAAI5N,EAAIiM,MAE7CwC,EACFW,cAAY5Q,EAAO,EAAI+P,EAAeE,EAAe,EAAI/P,EAAI6P,GAC7D7P,EAAI6P,EACJA,GAAgB,EAEhBT,EAAWvO,KAAKiP,GAChBT,EAAWxO,KAAKf,EAAMwC,OAAS,EAAIxC,EAAMA,EAAMwC,OAAS,GAAGiO,WAAa,GACxEZ,EAAejK,KAAKC,IAAIgK,EAAcG,GACtCF,IAEAV,EAAI5N,EAAI,EACR4N,EAAI3N,GAAKhC,EAAK4B,WACdsO,EAAe,KACfQ,EAAa,SA/Dbb,EAAWvO,KAAK6O,GAChBL,EAAWxO,MAAM,GACjB8O,EAAejK,KAAKC,IAAIgK,EAAcD,KACpCE,IACAG,EAEFb,EAAI5N,EAAI,EACR4N,EAAI3N,GAAKhC,EAAK4B,WACdsO,EAAe,KACfQ,EAAa,EA0DrB,IAAMU,EAAWrB,EAAWA,EAAWhN,OAAS,GAE/B,OAAbqO,GAAkC,OAAbA,IAEjB,SAAWrR,KAAKqR,KAEhBjB,EAAgBI,GAGpBV,EAAWvO,KAAK6O,GAChBC,EAAejK,KAAKC,IAAIgK,EAAcD,GACtCL,EAAWxO,MAAM,IAGrB,IAAM+P,EAAmB,GAEzB,IAAS5Q,EAAI,EAAGA,GAAK4P,EAAM5P,IAC3B,CACI,IAAI6Q,EAAc,EAEE,UAAhB/R,KAAK+O,OAELgD,EAAclB,EAAeP,EAAWpP,GAEnB,WAAhBlB,KAAK+O,OAEVgD,GAAelB,EAAeP,EAAWpP,IAAM,EAE1B,YAAhBlB,KAAK+O,SAEVgD,EAAcxB,EAAWrP,GAAK,EAAI,GAAK2P,EAAeP,EAAWpP,IAAMqP,EAAWrP,IAGtF4Q,EAAiB/P,KAAKgQ,GAG1B,IAAMC,EAAWhR,EAAMwC,OAEjByO,EAA8C,GAE9CC,EAAmC,GAEnCC,EAAsBnS,KAAK4O,qBAEjC8B,EAAiB3O,KAAIqK,MAArBsE,EAAyByB,GAEzB,IAASjR,EAAI,EAAGA,EAAI8Q,EAAU9Q,IAC9B,CACI,IACMkR,GADA9H,GAAUtJ,EAAME,GAAGoJ,SACMhB,YAAY+I,IAE3C,IAAKJ,EAAcG,GACnB,CAGI,KAFIE,GAAe5B,EAAiBa,OAGpC,CACI,IAAMgB,EAAW,IAAIC,EAAAA,aACjBC,SACAC,SAE2B,SAA3BjS,EAAKiK,mBAEL+H,EAAW,IAAIE,EAAAA,aAAapI,EAAOA,QAACiH,OACpCkB,EAAgBE,EAAWA,YAACC,SAI5BJ,EAAW,IAAIE,EAAAA,aAAapI,EAAAA,QAAQiH,MAChC,CAAEsB,QAASC,UAAQtK,07CAA0BuK,SAAU,CAAEC,QAAS,KACtEP,EAAgBE,EAAWA,YAACM,YAGhC,IAAMC,EAAO,IAAIC,EAAAA,KAAKb,EAAUE,GAEhCU,EAAKE,UAAYX,EAEjBJ,GAAe,CACXgB,MAAO,EACPC,WAAY,EACZC,YAAa,EACbC,SAAU,EACVC,MAAO,EACPP,KAAIA,EACJQ,SAAU,KACVC,IAAK,KACLC,QAAS,MAKjBvB,GAAagB,MAAQ,EACrBhB,GAAaiB,WAAa,EAC1BjB,GAAakB,YAAc,EAC3BlB,GAAamB,SAAW,EACxBnB,GAAaoB,MAAQ,EAGb,IAAA5D,EAAkB9P,KAAI8P,cAE9BA,EAAcsC,GAAkBtC,EAAcsC,IAAmB,IAAI7H,EAAOA,QAACD,GAAQhB,aACrFgJ,GAAaa,KAAK7I,QAAUwF,EAAcsC,GAE1CE,GAAaa,KAAK3E,KAAOxO,KAAKqO,MAE9B6D,EAAiBnQ,KAAKuQ,IAEtBL,EAAcG,GAAkBE,GAGpCL,EAAcG,GAAgBsB,QAKlC,IAASxS,EAAI,EAAGA,EAAIiR,EAAoB3O,OAAQtC,KAEc,IAAtDgR,EAAiBxR,QAAQyR,EAAoBjR,KAE7ClB,KAAK8T,YAAY3B,EAAoBjR,GAAGiS,MAMhD,IAASjS,EAAI,EAAGA,EAAIgR,EAAiB1O,OAAQtC,IAErCgR,EAAiBhR,GAAGiS,KAAKY,SAAW/T,MAEpCA,KAAKgU,SAAS9B,EAAiBhR,GAAGiS,MAO1C,IAAK,IAAMjS,KAFXlB,KAAK4O,qBAAuBsD,EAEZD,EAChB,CACI,IACMyB,GADApB,GAAeL,EAAc/Q,IACRwS,MAI3B,MAA4B,QAAtB9I,EAAA0H,GAAauB,eAAS,IAAAjJ,OAAA,EAAAA,EAAApH,QAAS,EAAIkQ,IAAUpB,GAAaqB,SAASnQ,OAA+B,EAAtB4P,EAAAA,KAAKa,eAEnF3B,GAAaqB,SAAW,IAAIO,aAAa,EAAQR,GACjDpB,GAAasB,IAAM,IAAIM,aAAa,EAAQR,GAC5CpB,GAAauB,QAAU,IAAIM,YAAY,EAAIT,QAQ3C,IAJA,IAAMU,EAAQ9B,GAAaoB,MACrBC,EAAWrB,GAAaqB,SAGrBrS,EAAY,EAAR8S,EAAY,EAAG9S,EAAIqS,EAASnQ,OAAQlC,IAE7CqS,EAASrS,GAAK,EAMtBgR,GAAaa,KAAKhR,KAAO,EAAIuR,EAGjC,IAASxS,EAAI,EAAGA,EAAI8Q,EAAU9Q,IAC9B,CACI,IAAMd,EACFiU,GADEjU,EAAOY,EAAME,IACDwQ,SAASlP,EAAKsP,EAAiB1R,EAAK0Q,OAAyB,YAAhB9Q,KAAK+O,OAAuB3O,EAAKqR,WAAa,GAEzGzR,KAAKyP,eAEL4E,EAASzN,KAAK0N,MAAMD,IAGxB,IAEM/J,GAFAiK,GAAOF,EAASzP,EAChB4P,GAAOpU,EAAKsR,SAASjP,EAAImC,EAGzB6P,GAAWxC,GAFX3H,GAAUlK,EAAKkK,SAEkBhB,YAAY+I,KAE7CqC,GAAepK,GAAQJ,MACvByK,GAAarK,GAAQsK,KAErBtB,GAAQmB,GAASnB,QAEvBmB,GAASZ,QAAiB,EAARP,GAAa,GAAK,EAAa,EAARA,GACzCmB,GAASZ,QAAiB,EAARP,GAAa,GAAK,EAAa,EAARA,GACzCmB,GAASZ,QAAiB,EAARP,GAAa,GAAK,EAAa,EAARA,GACzCmB,GAASZ,QAAiB,EAARP,GAAa,GAAK,EAAa,EAARA,GACzCmB,GAASZ,QAAiB,EAARP,GAAa,GAAK,EAAa,EAARA,GACzCmB,GAASZ,QAAiB,EAARP,GAAa,GAAK,EAAa,EAARA,GAEzCmB,GAASd,SAAkB,EAARL,GAAa,GAAKiB,GACrCE,GAASd,SAAkB,EAARL,GAAa,GAAKkB,GAErCC,GAASd,SAAkB,EAARL,GAAa,GAAKiB,GAAQG,GAAahS,MAAQkC,EAClE6P,GAASd,SAAkB,EAARL,GAAa,GAAKkB,GAErCC,GAASd,SAAkB,EAARL,GAAa,GAAKiB,GAAQG,GAAahS,MAAQkC,EAClE6P,GAASd,SAAkB,EAARL,GAAa,GAAKkB,GAAQE,GAAa/R,OAASiC,EAEnE6P,GAASd,SAAkB,EAARL,GAAa,GAAKiB,GACrCE,GAASd,SAAkB,EAARL,GAAa,GAAKkB,GAAQE,GAAa/R,OAASiC,EAEnE6P,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWE,GAC3CJ,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWG,GAE3CL,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWI,GAC3CN,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWK,GAE3CP,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWM,GAC3CR,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWO,GAE3CT,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWQ,GAC3CV,GAASb,IAAa,EAARN,GAAa,GAAKqB,GAAWS,GAM/C,IAAK,IAAMlU,KAHXlB,KAAK6O,WAAagC,EAAejM,EACjC5E,KAAK8O,aAAesB,EAAI3N,EAAIhC,EAAK4B,YAAcuC,EAE/BqN,EAChB,CACI,IAAMK,GAAeL,EAAc/Q,GAGnC,GAAsB,IAAlBlB,KAAKqV,OAAO7S,GAA6B,IAAlBxC,KAAKqV,OAAO5S,EAOnC,IALA,IAAI+Q,GAAc,EAEZ8B,GAAgBtV,KAAK6O,WAAa7O,KAAKqV,OAAO7S,EAC9C+S,GAAgBvV,KAAK8O,YAAc9O,KAAKqV,OAAO5S,EAE5C+S,GAAI,EAAGA,GAAIlD,GAAaoB,MAAO8B,KAEpClD,GAAaqB,SAASH,OAAkB8B,GACxChD,GAAaqB,SAASH,OAAkB+B,GAExCjD,GAAaqB,SAASH,OAAkB8B,GACxChD,GAAaqB,SAASH,OAAkB+B,GAExCjD,GAAaqB,SAASH,OAAkB8B,GACxChD,GAAaqB,SAASH,OAAkB+B,GAExCjD,GAAaqB,SAASH,OAAkB8B,GACxChD,GAAaqB,SAASH,OAAkB+B,GAIhDvV,KAAKoP,eAAiB8B,EAAgBtM,EAEtC,IAAM6Q,GAAenD,GAAaa,KAAKZ,SAASmD,UAAU,mBACpDC,GAAgBrD,GAAaa,KAAKZ,SAASmD,UAAU,iBACrDE,GAActD,GAAaa,KAAKZ,SAASsD,WAE/CJ,GAAahV,KAAO6R,GAAaqB,SACjCgC,GAAclV,KAAO6R,GAAasB,IAClCgC,GAAYnV,KAAO6R,GAAauB,QAEhC4B,GAAaK,SACbH,GAAcG,SACdF,GAAYE,SAGhB,IAAS5U,EAAI,EAAGA,EAAIF,EAAMwC,OAAQtC,IAE9B+M,EAAmBlM,KAAKf,EAAME,IAGlClB,KAAKgP,MAAQvO,EACbT,KAAKwP,OAAQ,GAGjBtB,EAAApD,UAAAiL,gBAAA,WAEI/V,KAAKgW,WACLhW,KAAKiW,4BAGT/H,EAAOpD,UAAAoL,QAAP,SAAQC,GAEAnW,KAAK6P,iBAAmB7P,KAAK2P,cAAgBwG,EAAS5R,aAEtDvE,KAAK2P,YAAcwG,EAAS5R,WAC5BvE,KAAKwP,OAAQ,GAIX,IAAA5E,EAAkD9B,EAAWsC,UAAUpL,KAAKiP,WAA1ExE,uBAAoBC,sBAAmBvI,SAE/C,GAA0B,SAAtBuI,EAWA,IARM,IAAAC,EAAiB3K,KAAKoW,eAApBC,EAAC1L,EAAA0L,EAAE7W,EAACmL,EAAAnL,EAAE8W,EAAC3L,EAAA2L,EAAE/W,MAEXgX,EAAK3P,KAAK4P,KAAMH,EAAIA,EAAM7W,EAAIA,GAC9BiX,EAAK7P,KAAK4P,KAAMF,EAAIA,EAAM/W,EAAIA,GAC9BmX,GAAc9P,KAAK+P,IAAIJ,GAAM3P,KAAK+P,IAAIF,IAAO,EAE7CG,EAAY5W,KAAKuG,SAAWpE,EAEf0U,EAAA,EAAAxN,EAAArJ,KAAK4O,qBAALiI,EAAAxN,EAAA7F,OAAAqT,IACnB,CADexN,EAAAwN,GAEN1D,KAAK2D,OAAO9D,SAASC,QAAUyD,EAAajM,EAAqBmM,EAAY5W,KAAK2P,YAI/FxB,EAAArD,UAAMoL,QAAOa,KAAA/W,KAACmW,IAOXjI,EAAApD,UAAAkM,eAAP,WAII,OAFAhX,KAAKgW,WAEE7H,EAAArD,UAAMkM,eAAcD,KAAA/W,OAOrBkO,EAAApD,UAAAkL,SAAV,WAEI,IAAMhU,EAAO8G,EAAWsC,UAAUpL,KAAKiP,WAEvC,IAAKjN,EAED,MAAM,IAAImJ,MAAM,uBAAuBnL,KAAKiP,UAAY,KAExDjP,KAAKgP,QAAUhN,IAEfhC,KAAKwP,OAAQ,GAGbxP,KAAKwP,OAELxP,KAAKmQ,cAQb1Q,OAAAwX,eAAW/I,EAAIpD,UAAA,OAAA,CAAfoM,IAAA,WAEI,OAAOlX,KAAKqO,OAGhB8I,IAAA,SAAgBtV,GAEZ,GAAI7B,KAAKqO,QAAUxM,EAAnB,CAEA7B,KAAKqO,MAAQxM,EAEb,IAAK,IAAIX,EAAI,EAAGA,EAAIlB,KAAK4O,qBAAqBpL,OAAQtC,IAElDlB,KAAK4O,qBAAqB1N,GAAGiS,KAAK3E,KAAO3M,oCASjDpC,OAAAwX,eAAW/I,EAAKpD,UAAA,QAAA,CAAhBoM,IAAA,WAEI,OAAOlX,KAAK+O,QAGhBoI,IAAA,SAAiBtV,GAET7B,KAAK+O,SAAWlN,IAEhB7B,KAAK+O,OAASlN,EACd7B,KAAKwP,OAAQ,oCAKrB/P,OAAAwX,eAAW/I,EAAQpD,UAAA,WAAA,CAAnBoM,IAAA,WAEI,OAAOlX,KAAKiP,WAGhBkI,IAAA,SAAoBtV,GAEhB,IAAKiH,EAAWsC,UAAUvJ,GAEtB,MAAM,IAAIsJ,MAAM,uBAAuBtJ,EAAK,KAG5C7B,KAAKiP,YAAcpN,IAEnB7B,KAAKiP,UAAYpN,EACjB7B,KAAKwP,OAAQ,oCAKrB/P,OAAAwX,eAAW/I,EAAQpD,UAAA,WAAA,CAAnBoM,IAAA,iBAEI,eAAOtM,EAAA5K,KAAKkP,yBAAapG,EAAWsC,UAAUpL,KAAKiP,WAAW9M,MAGlEgV,IAAA,SAAoBtV,GAEZ7B,KAAKkP,YAAcrN,IAEnB7B,KAAKkP,UAAYrN,EACjB7B,KAAKwP,OAAQ,oCAarB/P,OAAAwX,eAAW/I,EAAMpD,UAAA,SAAA,CAAjBoM,IAAA,WAEI,OAAOlX,KAAKsP,SAGhB6H,IAAA,SAAkBtV,GAEO,iBAAVA,EAEP7B,KAAKsP,QAAQ6H,IAAItV,GAIjB7B,KAAKsP,QAAQ8H,SAASvV,oCAK9BpC,OAAAwX,eAAW/I,EAAIpD,UAAA,OAAA,CAAfoM,IAAA,WAEI,OAAOlX,KAAKyQ,OAGhB0G,IAAA,SAAgB1S,GAEZA,EAAOyH,OAAOzH,MAAAA,EAAsC,GAAKA,GAErDzE,KAAKyQ,QAAUhM,IAInBzE,KAAKyQ,MAAQhM,EACbzE,KAAKwP,OAAQ,oCAQjB/P,OAAAwX,eAAW/I,EAAQpD,UAAA,WAAA,CAAnBoM,IAAA,WAEI,OAAOlX,KAAKmP,WAGhBgI,IAAA,SAAoBtV,GAEZ7B,KAAKmP,YAActN,IAIvB7B,KAAKmP,UAAYtN,EACjB7B,KAAKwP,OAAQ,oCAQjB/P,OAAAwX,eAAW/I,EAAapD,UAAA,gBAAA,CAAxBoM,IAAA,WAII,OAFAlX,KAAKgW,WAEEhW,KAAKoP,gDAQhB3P,OAAAwX,eAAW/I,EAASpD,UAAA,YAAA,CAApBoM,IAAA,WAII,OAFAlX,KAAKgW,WAEEhW,KAAK6O,4CAIhBpP,OAAAwX,eAAW/I,EAAapD,UAAA,gBAAA,CAAxBoM,IAAA,WAEI,OAAOlX,KAAKqP,gBAGhB8H,IAAA,SAAyBtV,GAEjB7B,KAAKqP,iBAAmBxN,IAExB7B,KAAKqP,eAAiBxN,EACtB7B,KAAKwP,OAAQ,oCAWrB/P,OAAAwX,eAAW/I,EAAWpD,UAAA,cAAA,CAAtBoM,IAAA,WAEI,OAAOlX,KAAKyP,cAGhB0H,IAAA,SAAuBtV,GAEfA,IAAU7B,KAAKyP,eAEfzP,KAAKyP,aAAe5N,EACpB7B,KAAKwP,OAAQ,oCASrB/P,OAAAwX,eAAW/I,EAAUpD,UAAA,aAAA,CAArBoM,IAAA,WAII,OAFAlX,KAAKgW,WAEEhW,KAAK8O,6CAShBrP,OAAAwX,eAAI/I,EAAUpD,UAAA,aAAA,CAAdoM,IAAA,WAEI,OAAOlX,KAAK2P,aAGhBwH,IAAA,SAAetV,GAEX7B,KAAK6P,iBAAkB,EAEnB7P,KAAK2P,cAAgB9N,IAKzB7B,KAAK2P,YAAc9N,EACnB7B,KAAKwP,OAAQ,oCAGjBtB,EAAOpD,UAAAC,QAAP,SAAQQ,GAEI,IAAAuE,EAAkB9P,KAAI8P,cAExBY,EAA8C,SADvC5H,EAAWsC,UAAUpL,KAAKiP,WACTvE,kBACxBqD,EAAkCC,EAExC0C,EAAiB3O,KAAjBqK,MAAAsE,EAAyB1Q,KAAK4O,sBAC9B,IAA2B,IAAAiI,EAAA,EAAAjM,EAAA5K,KAAK4O,qBAALiI,EAAAjM,EAAApH,OAAAqT,IAC3B,CADK,IAAMvE,EAAY1H,EAAAiM,GAEnB7W,KAAK8T,YAAYxB,EAAaa,MAYlC,IAAK,IAAM7Q,KAVXtC,KAAK4O,qBAAuB,GAG5B8B,EACK2G,QAAO,SAAClX,GAAS,OAAA2P,EAAc3P,EAAKgT,KAAK7I,QAAQhB,YAAY+I,QAC7DpQ,SAAQ,SAAC9B,GAENA,EAAKgT,KAAK7I,QAAUC,EAAAA,QAAQiH,SAGnB1B,EACjB,CACoBA,EAAcxN,GAEtByI,iBACD+E,EAAcxN,GAGzBtC,KAAKgP,MAAQ,KACbhP,KAAK8P,cAAgB,KAErB3B,EAAArD,UAAMC,QAAOgM,KAAA/W,KAACuL,IAz2BJ2C,EAAAI,cAA2C,CACrDC,MAAO,OACPC,KAAM,SACNC,SAAU,EACVC,cAAe,GAu2BtBR,EA72BD,CAAgCoJ,aC3DhCC,EAAA,WAAA,SAAAA,KAgKA,OAvJkBA,EAAAC,IAAd,WAEIC,EAAcA,eAACC,oBAAoB,MAAOD,EAAcA,eAACE,kBAAkBC,OAUxEL,EAAAM,IAAP,SAAyBC,EAA0BC,GAE/C,IAAM7M,EAAShH,EAAiB4T,EAASrX,MAGzC,GAAKyK,EAwBL,IAjBA,IAAM8M,EAAUT,EAAiBU,WAAWjY,KAAM8X,GAC5CrX,EAAOyK,EAAOvK,MAAMmX,EAASrX,MAC7BsI,EAA0B,GAI1BmP,EAAY,SAAC/X,GAEf4I,EAAS5I,EAAKgY,SAASC,UAAYjY,EAAKmK,QAEpC7K,OAAO4Y,KAAKtP,GAAUvF,SAAW/C,EAAKN,KAAKqD,SAE3CsU,EAASQ,WAAaxP,EAAWkC,QAAQvK,EAAMsI,GAAU,GACzDgP,MAIC7W,EAAI,EAAGA,EAAIT,EAAKN,KAAKqD,SAAUtC,EACxC,CACI,IAAMkX,EAAW3X,EAAKN,KAAKe,GAAGqB,KACxBgW,EAAMP,EAAUI,EAClBI,GAAS,EAIb,IAAK,IAAMrX,KAAQnB,KAAKyY,UACxB,CACI,IAAMC,EAAiC1Y,KAAKyY,UAAUtX,GAEtD,GAAIuX,EAAeH,MAAQA,EAC3B,CACIG,EAAeP,SAASC,SAAWA,EAC/BM,EAAepO,QAEf4N,EAAUQ,GAIVA,EAAeC,kBAAkBnB,IAAIU,GAEzCM,GAAS,EACT,OAMR,IAAKA,EACL,CAEI,IAAMjN,EAAU,CACZqN,YAAad,EAASc,YACtBC,SAAUpB,EAAAA,eAAeqB,UAAUC,MACnCZ,SAAU1Y,OAAO+L,OACb,CAAE4M,SAAQA,GACVN,EAASK,SAASa,eAEtBC,eAAgBnB,GAGpB9X,KAAKwX,IAAIe,EAAKhN,EAAS2M,SAjE3BH,KA2EOR,EAAAU,WAAf,SAA0BiB,EAAgBpB,GAEtC,IAAIqB,EAAUrB,EAASsB,UAAqD,GAAzC7B,EAAiB8B,QAAQvB,EAASS,KA4BrE,OA1BIT,EAASsB,YAEM,MAAXD,IAEAA,EAAS,IAGTD,EAAOlB,SAAWmB,GAGuC,MAArDD,EAAOlB,QAAQsB,OAAOJ,EAAOlB,QAAQxU,OAAS,KAE9C2V,GAAU,OAMtBA,EAASA,EAAOzX,QAAQwX,EAAOlB,QAAS,MAGW,MAArCmB,EAAOG,OAAOH,EAAO3V,OAAS,KAExC2V,GAAU,KAGPA,GAOI5B,EAAO8B,QAAtB,SAAuBd,GAEnB,IAAMgB,EAAMhB,EACP7W,QAAQ,MAAO,KACfA,QAAQ,MAAO,IACfA,QAAQ,YAAa,IAG1B,OAAI6X,IAAQhB,EAED,IAGM,KAARgB,EAEE,IAGJA,GA3JJhC,EAAAiC,UAA+BC,EAAaA,cAACC,OA6JvDnC"}