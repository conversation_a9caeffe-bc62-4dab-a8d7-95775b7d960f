{"version": 3, "file": "text.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/const.ts", "../../src/TextStyle.ts", "../../src/TextMetrics.ts", "../../src/Text.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * Constants that define the type of gradient on text.\n * @static\n * @constant\n * @name TEXT_GRADIENT\n * @memberof PIXI\n * @type {object}\n * @property {number} LINEAR_VERTICAL Vertical gradient\n * @property {number} LINEAR_HORIZONTAL Linear gradient\n */\nexport enum TEXT_GRADIENT\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    LINEAR_VERTICAL = 0,\n    LINEAR_HORIZONTAL = 1\n}\n", "// disabling eslint for now, going to rewrite this in v5\n/* eslint-disable */\n\nimport { TEXT_GRADIENT } from './const';\nimport { hex2string } from '@pixi/utils';\n\nexport type TextStyleAlign = 'left'|'center'|'right'|'justify';\nexport type TextStyleFill = string|string[]|number|number[]|CanvasGradient|CanvasPattern;\nexport type TextStyleFontStyle = 'normal'|'italic'|'oblique';\nexport type TextStyleFontVariant = 'normal'|'small-caps';\nexport type TextStyleFontWeight = 'normal'|'bold'|'bolder'|'lighter'|'100'|'200'|'300'|'400'|'500'|'600'|'700'|'800'|'900';\nexport type TextStyleLineJoin = 'miter'|'round'|'bevel';\nexport type TextStyleTextBaseline = 'alphabetic'|'top'|'hanging'|'middle'|'ideographic'|'bottom';\nexport type TextStyleWhiteSpace = 'normal'|'pre'|'pre-line';\n\nexport interface ITextStyle {\n    align: TextStyleAlign;\n    breakWords: boolean;\n    dropShadow: boolean;\n    dropShadowAlpha: number;\n    dropShadowAngle: number;\n    dropShadowBlur: number;\n    dropShadowColor: string|number;\n    dropShadowDistance: number;\n    fill: TextStyleFill;\n    fillGradientType: TEXT_GRADIENT;\n    fillGradientStops: number[];\n    fontFamily: string | string[];\n    fontSize: number | string;\n    fontStyle: TextStyleFontStyle;\n    fontVariant: TextStyleFontVariant;\n    fontWeight: TextStyleFontWeight;\n    letterSpacing: number;\n    lineHeight: number;\n    lineJoin: TextStyleLineJoin;\n    miterLimit: number;\n    padding: number;\n    stroke: string|number;\n    strokeThickness: number;\n    textBaseline: TextStyleTextBaseline;\n    trim: boolean;\n    whiteSpace: TextStyleWhiteSpace;\n    wordWrap: boolean;\n    wordWrapWidth: number;\n    leading: number;\n}\n\nconst defaultStyle: ITextStyle = {\n    align: 'left',\n    breakWords: false,\n    dropShadow: false,\n    dropShadowAlpha: 1,\n    dropShadowAngle: Math.PI / 6,\n    dropShadowBlur: 0,\n    dropShadowColor: 'black',\n    dropShadowDistance: 5,\n    fill: 'black',\n    fillGradientType: TEXT_GRADIENT.LINEAR_VERTICAL,\n    fillGradientStops: [],\n    fontFamily: 'Arial',\n    fontSize: 26,\n    fontStyle: 'normal',\n    fontVariant: 'normal',\n    fontWeight: 'normal',\n    letterSpacing: 0,\n    lineHeight: 0,\n    lineJoin: 'miter',\n    miterLimit: 10,\n    padding: 0,\n    stroke: 'black',\n    strokeThickness: 0,\n    textBaseline: 'alphabetic',\n    trim: false,\n    whiteSpace: 'pre',\n    wordWrap: false,\n    wordWrapWidth: 100,\n    leading: 0,\n};\n\nconst genericFontFamilies = [\n    'serif',\n    'sans-serif',\n    'monospace',\n    'cursive',\n    'fantasy',\n    'system-ui',\n];\n\n/**\n * A TextStyle Object contains information to decorate a Text objects.\n *\n * An instance can be shared between multiple Text objects; then changing the style will update all text objects using it.\n *\n * A tool can be used to generate a text style [here](https://pixijs.io/pixi-text-style).\n *\n * @memberof PIXI\n */\nexport class TextStyle implements ITextStyle\n{\n    public styleID: number;\n\n    protected _align: TextStyleAlign;\n    protected _breakWords: boolean;\n    protected _dropShadow: boolean;\n    protected _dropShadowAlpha: number;\n    protected _dropShadowAngle: number;\n    protected _dropShadowBlur: number;\n    protected _dropShadowColor: string|number;\n    protected _dropShadowDistance: number;\n    protected _fill: TextStyleFill;\n    protected _fillGradientType: TEXT_GRADIENT;\n    protected _fillGradientStops: number[];\n    protected _fontFamily: string|string[];\n    protected _fontSize: number|string;\n    protected _fontStyle: TextStyleFontStyle;\n    protected _fontVariant: TextStyleFontVariant;\n    protected _fontWeight: TextStyleFontWeight;\n    protected _letterSpacing: number;\n    protected _lineHeight: number;\n    protected _lineJoin: TextStyleLineJoin;\n    protected _miterLimit: number;\n    protected _padding: number;\n    protected _stroke: string|number;\n    protected _strokeThickness: number;\n    protected _textBaseline: TextStyleTextBaseline;\n    protected _trim: boolean;\n    protected _whiteSpace: TextStyleWhiteSpace;\n    protected _wordWrap: boolean;\n    protected _wordWrapWidth: number;\n    protected _leading: number;\n\n    /**\n     * @param {object} [style] - The style parameters\n     * @param {string} [style.align='left'] - Alignment for multiline text ('left', 'center' or 'right'),\n     *  does not affect single line text\n     * @param {boolean} [style.breakWords=false] - Indicates if lines can be wrapped within words, it\n     *  needs wordWrap to be set to true\n     * @param {boolean} [style.dropShadow=false] - Set a drop shadow for the text\n     * @param {number} [style.dropShadowAlpha=1] - Set alpha for the drop shadow\n     * @param {number} [style.dropShadowAngle=Math.PI/6] - Set a angle of the drop shadow\n     * @param {number} [style.dropShadowBlur=0] - Set a shadow blur radius\n     * @param {string|number} [style.dropShadowColor='black'] - A fill style to be used on the dropshadow e.g 'red', '#00FF00'\n     * @param {number} [style.dropShadowDistance=5] - Set a distance of the drop shadow\n     * @param {string|string[]|number|number[]|CanvasGradient|CanvasPattern} [style.fill='black'] - A canvas\n     *  fillstyle that will be used on the text e.g 'red', '#00FF00'. Can be an array to create a gradient\n     *  eg ['#000000','#FFFFFF']\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle|MDN}\n     * @param {number} [style.fillGradientType=PIXI.TEXT_GRADIENT.LINEAR_VERTICAL] - If fill is an array of colours\n     *  to create a gradient, this can change the type/direction of the gradient. See {@link PIXI.TEXT_GRADIENT}\n     * @param {number[]} [style.fillGradientStops] - If fill is an array of colours to create a gradient, this array can set\n     * the stop points (numbers between 0 and 1) for the color, overriding the default behaviour of evenly spacing them.\n     * @param {string|string[]} [style.fontFamily='Arial'] - The font family\n     * @param {number|string} [style.fontSize=26] - The font size (as a number it converts to px, but as a string,\n     *  equivalents are '26px','20pt','160%' or '1.6em')\n     * @param {string} [style.fontStyle='normal'] - The font style ('normal', 'italic' or 'oblique')\n     * @param {string} [style.fontVariant='normal'] - The font variant ('normal' or 'small-caps')\n     * @param {string} [style.fontWeight='normal'] - The font weight ('normal', 'bold', 'bolder', 'lighter' and '100',\n     *  '200', '300', '400', '500', '600', '700', '800' or '900')\n     * @param {number} [style.leading=0] - The space between lines\n     * @param {number} [style.letterSpacing=0] - The amount of spacing between letters, default is 0\n     * @param {number} [style.lineHeight] - The line height, a number that represents the vertical space that a letter uses\n     * @param {string} [style.lineJoin='miter'] - The lineJoin property sets the type of corner created, it can resolve\n     *      spiked text issues. Possible values \"miter\" (creates a sharp corner), \"round\" (creates a round corner) or \"bevel\"\n     *      (creates a squared corner).\n     * @param {number} [style.miterLimit=10] - The miter limit to use when using the 'miter' lineJoin mode. This can reduce\n     *      or increase the spikiness of rendered text.\n     * @param {number} [style.padding=0] - Occasionally some fonts are cropped. Adding some padding will prevent this from\n     *     happening by adding padding to all sides of the text.\n     * @param {string|number} [style.stroke='black'] - A canvas fillstyle that will be used on the text stroke\n     *  e.g 'blue', '#FCFF00'\n     * @param {number} [style.strokeThickness=0] - A number that represents the thickness of the stroke.\n     *  Default is 0 (no stroke)\n     * @param {boolean} [style.trim=false] - Trim transparent borders\n     * @param {string} [style.textBaseline='alphabetic'] - The baseline of the text that is rendered.\n     * @param {string} [style.whiteSpace='pre'] - Determines whether newlines & spaces are collapsed or preserved \"normal\"\n     *      (collapse, collapse), \"pre\" (preserve, preserve) | \"pre-line\" (preserve, collapse). It needs wordWrap to be set to true\n     * @param {boolean} [style.wordWrap=false] - Indicates if word wrap should be used\n     * @param {number} [style.wordWrapWidth=100] - The width at which text will wrap, it needs wordWrap to be set to true\n     */\n    constructor(style?: Partial<ITextStyle>)\n    {\n        this.styleID = 0;\n\n        this.reset();\n\n        deepCopyProperties(this, style, style);\n    }\n\n    /**\n     * Creates a new TextStyle object with the same values as this one.\n     * Note that the only the properties of the object are cloned.\n     *\n     * @return New cloned TextStyle object\n     */\n    public clone(): TextStyle\n    {\n        const clonedProperties: Partial<ITextStyle> = {};\n\n        deepCopyProperties(clonedProperties, this, defaultStyle);\n\n        return new TextStyle(clonedProperties);\n    }\n\n    /** Resets all properties to the defaults specified in TextStyle.prototype._default */\n    public reset(): void\n    {\n        deepCopyProperties(this, defaultStyle, defaultStyle);\n    }\n\n    /**\n     * Alignment for multiline text ('left', 'center' or 'right'), does not affect single line text\n     *\n     * @member {string}\n     */\n    get align(): TextStyleAlign\n    {\n        return this._align;\n    }\n    set align(align: TextStyleAlign)\n    {\n        if (this._align !== align)\n        {\n            this._align = align;\n            this.styleID++;\n        }\n    }\n\n    /** Indicates if lines can be wrapped within words, it needs wordWrap to be set to true. */\n    get breakWords(): boolean\n    {\n        return this._breakWords;\n    }\n    set breakWords(breakWords: boolean)\n    {\n        if (this._breakWords !== breakWords)\n        {\n            this._breakWords = breakWords;\n            this.styleID++;\n        }\n    }\n\n    /** Set a drop shadow for the text. */\n    get dropShadow(): boolean\n    {\n        return this._dropShadow;\n    }\n    set dropShadow(dropShadow: boolean)\n    {\n        if (this._dropShadow !== dropShadow)\n        {\n            this._dropShadow = dropShadow;\n            this.styleID++;\n        }\n    }\n\n    /** Set alpha for the drop shadow. */\n    get dropShadowAlpha(): number\n    {\n        return this._dropShadowAlpha;\n    }\n    set dropShadowAlpha(dropShadowAlpha: number)\n    {\n        if (this._dropShadowAlpha !== dropShadowAlpha)\n        {\n            this._dropShadowAlpha = dropShadowAlpha;\n            this.styleID++;\n        }\n    }\n\n    /** Set a angle of the drop shadow. */\n    get dropShadowAngle(): number\n    {\n        return this._dropShadowAngle;\n    }\n    set dropShadowAngle(dropShadowAngle: number)\n    {\n        if (this._dropShadowAngle !== dropShadowAngle)\n        {\n            this._dropShadowAngle = dropShadowAngle;\n            this.styleID++;\n        }\n    }\n\n    /** Set a shadow blur radius. */\n    get dropShadowBlur(): number\n    {\n        return this._dropShadowBlur;\n    }\n    set dropShadowBlur(dropShadowBlur: number)\n    {\n        if (this._dropShadowBlur !== dropShadowBlur)\n        {\n            this._dropShadowBlur = dropShadowBlur;\n            this.styleID++;\n        }\n    }\n\n    /** A fill style to be used on the dropshadow e.g 'red', '#00FF00'. */\n    get dropShadowColor(): number | string\n    {\n        return this._dropShadowColor;\n    }\n    set dropShadowColor(dropShadowColor: number | string)\n    {\n        const outputColor = getColor(dropShadowColor);\n        if (this._dropShadowColor !== outputColor)\n        {\n            this._dropShadowColor = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /** Set a distance of the drop shadow. */\n    get dropShadowDistance(): number\n    {\n        return this._dropShadowDistance;\n    }\n    set dropShadowDistance(dropShadowDistance: number)\n    {\n        if (this._dropShadowDistance !== dropShadowDistance)\n        {\n            this._dropShadowDistance = dropShadowDistance;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A canvas fillstyle that will be used on the text e.g 'red', '#00FF00'.\n     *\n     * Can be an array to create a gradient eg ['#000000','#FFFFFF']\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle|MDN}\n     *\n     * @member {string|string[]|number|number[]|CanvasGradient|CanvasPattern}\n     */\n    get fill(): TextStyleFill\n    {\n        return this._fill;\n    }\n    set fill(fill: TextStyleFill)\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        // TODO: Not sure if getColor works properly with CanvasGradient and/or CanvasPattern, can't pass in\n        //       without casting here.\n        const outputColor = getColor(fill as any);\n        if (this._fill !== outputColor)\n        {\n            this._fill = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * If fill is an array of colours to create a gradient, this can change the type/direction of the gradient.\n     *\n     * @see PIXI.TEXT_GRADIENT\n     */\n    get fillGradientType(): TEXT_GRADIENT\n    {\n        return this._fillGradientType;\n    }\n    set fillGradientType(fillGradientType: TEXT_GRADIENT)\n    {\n        if (this._fillGradientType !== fillGradientType)\n        {\n            this._fillGradientType = fillGradientType;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * If fill is an array of colours to create a gradient, this array can set the stop points\n     * (numbers between 0 and 1) for the color, overriding the default behaviour of evenly spacing them.\n     */\n    get fillGradientStops(): number[]\n    {\n        return this._fillGradientStops;\n    }\n    set fillGradientStops(fillGradientStops: number[])\n    {\n        if (!areArraysEqual(this._fillGradientStops,fillGradientStops))\n        {\n            this._fillGradientStops = fillGradientStops;\n            this.styleID++;\n        }\n    }\n\n    /** The font family. */\n    get fontFamily(): string | string[]\n    {\n        return this._fontFamily;\n    }\n    set fontFamily(fontFamily: string | string[])\n    {\n        if (this.fontFamily !== fontFamily)\n        {\n            this._fontFamily = fontFamily;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font size\n     * (as a number it converts to px, but as a string, equivalents are '26px','20pt','160%' or '1.6em')\n     */\n    get fontSize(): number | string\n    {\n        return this._fontSize;\n    }\n    set fontSize(fontSize: number | string)\n    {\n        if (this._fontSize !== fontSize)\n        {\n            this._fontSize = fontSize;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font style\n     * ('normal', 'italic' or 'oblique')\n     *\n     * @member {string}\n     */\n    get fontStyle(): TextStyleFontStyle\n    {\n        return this._fontStyle;\n    }\n    set fontStyle(fontStyle: TextStyleFontStyle)\n    {\n        if (this._fontStyle !== fontStyle)\n        {\n            this._fontStyle = fontStyle;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font variant\n     * ('normal' or 'small-caps')\n     *\n     * @member {string}\n     */\n    get fontVariant(): TextStyleFontVariant\n    {\n        return this._fontVariant;\n    }\n    set fontVariant(fontVariant: TextStyleFontVariant)\n    {\n        if (this._fontVariant !== fontVariant)\n        {\n            this._fontVariant = fontVariant;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font weight\n     * ('normal', 'bold', 'bolder', 'lighter' and '100', '200', '300', '400', '500', '600', '700', 800' or '900')\n     *\n     * @member {string}\n     */\n    get fontWeight(): TextStyleFontWeight\n    {\n        return this._fontWeight;\n    }\n    set fontWeight(fontWeight: TextStyleFontWeight)\n    {\n        if (this._fontWeight !== fontWeight)\n        {\n            this._fontWeight = fontWeight;\n            this.styleID++;\n        }\n    }\n\n    /** The amount of spacing between letters, default is 0. */\n    get letterSpacing(): number\n    {\n        return this._letterSpacing;\n    }\n    set letterSpacing(letterSpacing: number)\n    {\n        if (this._letterSpacing !== letterSpacing)\n        {\n            this._letterSpacing = letterSpacing;\n            this.styleID++;\n        }\n    }\n\n    /** The line height, a number that represents the vertical space that a letter uses. */\n    get lineHeight(): number\n    {\n        return this._lineHeight;\n    }\n    set lineHeight(lineHeight: number)\n    {\n        if (this._lineHeight !== lineHeight)\n        {\n            this._lineHeight = lineHeight;\n            this.styleID++;\n        }\n    }\n\n    /** The space between lines. */\n    get leading(): number\n    {\n        return this._leading;\n    }\n    set leading(leading: number)\n    {\n        if (this._leading !== leading)\n        {\n            this._leading = leading;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The lineJoin property sets the type of corner created, it can resolve spiked text issues.\n     * Default is 'miter' (creates a sharp corner).\n     *\n     * @member {string}\n     */\n    get lineJoin(): TextStyleLineJoin\n    {\n        return this._lineJoin;\n    }\n    set lineJoin(lineJoin: TextStyleLineJoin)\n    {\n        if (this._lineJoin !== lineJoin)\n        {\n            this._lineJoin = lineJoin;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The miter limit to use when using the 'miter' lineJoin mode.\n     *\n     * This can reduce or increase the spikiness of rendered text.\n     */\n    get miterLimit(): number\n    {\n        return this._miterLimit;\n    }\n    set miterLimit(miterLimit: number)\n    {\n        if (this._miterLimit !== miterLimit)\n        {\n            this._miterLimit = miterLimit;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Occasionally some fonts are cropped. Adding some padding will prevent this from happening\n     * by adding padding to all sides of the text.\n     */\n    get padding(): number\n    {\n        return this._padding;\n    }\n    set padding(padding: number)\n    {\n        if (this._padding !== padding)\n        {\n            this._padding = padding;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A canvas fillstyle that will be used on the text stroke\n     * e.g 'blue', '#FCFF00'\n     */\n    get stroke(): string | number\n    {\n        return this._stroke;\n    }\n    set stroke(stroke: string | number)\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        const outputColor = getColor(stroke);\n        if (this._stroke !== outputColor)\n        {\n            this._stroke = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A number that represents the thickness of the stroke.\n     *\n     * @default 0\n     */\n    get strokeThickness(): number\n    {\n        return this._strokeThickness;\n    }\n    set strokeThickness(strokeThickness: number)\n    {\n        if (this._strokeThickness !== strokeThickness)\n        {\n            this._strokeThickness = strokeThickness;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The baseline of the text that is rendered.\n     *\n     * @member {string}\n     */\n    get textBaseline(): TextStyleTextBaseline\n    {\n        return this._textBaseline;\n    }\n    set textBaseline(textBaseline: TextStyleTextBaseline)\n    {\n        if (this._textBaseline !== textBaseline)\n        {\n            this._textBaseline = textBaseline;\n            this.styleID++;\n        }\n    }\n\n    /** Trim transparent borders. */\n    get trim(): boolean\n    {\n        return this._trim;\n    }\n    set trim(trim: boolean)\n    {\n        if (this._trim !== trim)\n        {\n            this._trim = trim;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * How newlines and spaces should be handled.\n     * Default is 'pre' (preserve, preserve).\n     *\n     *  value       | New lines     |   Spaces\n     *  ---         | ---           |   ---\n     * 'normal'     | Collapse      |   Collapse\n     * 'pre'        | Preserve      |   Preserve\n     * 'pre-line'   | Preserve      |   Collapse\n     *\n     * @member {string}\n     */\n    get whiteSpace(): TextStyleWhiteSpace\n    {\n        return this._whiteSpace;\n    }\n    set whiteSpace(whiteSpace: TextStyleWhiteSpace)\n    {\n        if (this._whiteSpace !== whiteSpace)\n        {\n            this._whiteSpace = whiteSpace;\n            this.styleID++;\n        }\n    }\n\n    /** Indicates if word wrap should be used. */\n    get wordWrap(): boolean\n    {\n        return this._wordWrap;\n    }\n    set wordWrap(wordWrap: boolean)\n    {\n        if (this._wordWrap !== wordWrap)\n        {\n            this._wordWrap = wordWrap;\n            this.styleID++;\n        }\n    }\n\n    /** The width at which text will wrap, it needs wordWrap to be set to true. */\n    get wordWrapWidth(): number\n    {\n        return this._wordWrapWidth;\n    }\n    set wordWrapWidth(wordWrapWidth: number)\n    {\n        if (this._wordWrapWidth !== wordWrapWidth)\n        {\n            this._wordWrapWidth = wordWrapWidth;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Generates a font style string to use for `TextMetrics.measureFont()`.\n     *\n     * @return Font style string, for passing to `TextMetrics.measureFont()`\n     */\n    public toFontString(): string\n    {\n        // build canvas api font setting from individual components. Convert a numeric this.fontSize to px\n        const fontSizeString = (typeof this.fontSize === 'number') ? `${this.fontSize}px` : this.fontSize;\n\n        // Clean-up fontFamily property by quoting each font name\n        // this will support font names with spaces\n        let fontFamilies: string|string[] = this.fontFamily;\n\n        if (!Array.isArray(this.fontFamily))\n        {\n            fontFamilies = this.fontFamily.split(',');\n        }\n\n        for (let i = fontFamilies.length - 1; i >= 0; i--)\n        {\n            // Trim any extra white-space\n            let fontFamily = fontFamilies[i].trim();\n\n            // Check if font already contains strings\n            if (!(/([\\\"\\'])[^\\'\\\"]+\\1/).test(fontFamily) && genericFontFamilies.indexOf(fontFamily) < 0)\n            {\n                fontFamily = `\"${fontFamily}\"`;\n            }\n            (fontFamilies as string[])[i] = fontFamily;\n        }\n\n        return `${this.fontStyle} ${this.fontVariant} ${this.fontWeight} ${fontSizeString} ${(fontFamilies as string[]).join(',')}`;\n    }\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * @private\n * @param color\n * @return The color as a string.\n */\nfunction getSingleColor(color: string|number): string\n{\n    if (typeof color === 'number')\n    {\n        return hex2string(color);\n    }\n    else if (typeof color === 'string')\n    {\n        if ( color.indexOf('0x') === 0 )\n        {\n            color = color.replace('0x', '#');\n        }\n    }\n\n    return color;\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * This version can also convert array of colors\n * @private\n * @param color\n * @return The color as a string.\n */\nfunction getColor(color: (string|number)[]): string[];\nfunction getColor(color: string|number): string;\nfunction getColor(color: string|number|(string|number)[]): string|string[]\n{\n    if (!Array.isArray(color))\n    {\n        return getSingleColor(color);\n    }\n    else\n    {\n        for (let i = 0; i < color.length; ++i)\n        {\n            color[i] = getSingleColor(color[i]);\n        }\n\n        return color as string[];\n    }\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * This version can also convert array of colors\n * @private\n * @param array1 - First array to compare\n * @param array2 - Second array to compare\n * @return Do the arrays contain the same values in the same order\n */\nfunction areArraysEqual<T>(array1: T[], array2: T[]): boolean\n{\n    if (!Array.isArray(array1) || !Array.isArray(array2))\n    {\n        return false;\n    }\n\n    if (array1.length !== array2.length)\n    {\n        return false;\n    }\n\n    for (let i = 0; i < array1.length; ++i)\n    {\n        if (array1[i] !== array2[i])\n        {\n            return false;\n        }\n    }\n\n    return true;\n}\n\n/**\n * Utility function to ensure that object properties are copied by value, and not by reference\n * @private\n * @param target - Target object to copy properties into\n * @param source - Source object for the properties to copy\n * @param propertyObj - Object containing properties names we want to loop over\n */\nfunction deepCopyProperties(target: Record<string, any>, source: Record<string, any>, propertyObj: Record<string, any>): void {\n    for (const prop in propertyObj) {\n        if (Array.isArray(source[prop])) {\n            target[prop] = source[prop].slice();\n        } else {\n            target[prop] = source[prop];\n        }\n    }\n}\n", "import { settings } from '@pixi/settings';\n\nimport type { TextStyle, TextStyleWhiteSpace } from './TextStyle';\n\ninterface IFontMetrics\n{\n    ascent: number;\n    descent: number;\n    fontSize: number;\n}\n\ntype CharacterWidthCache = { [key: string]: number };\n\n// Default settings used for all getContext calls\nconst contextSettings = {\n    // TextMetrics requires getImageData readback for measuring fonts.\n    willReadFrequently: true,\n} as CanvasRenderingContext2DSettings;\n\n/**\n * The TextMetrics object represents the measurement of a block of text with a specified style.\n *\n * ```js\n * let style = new PIXI.TextStyle({fontFamily : 'Arial', fontSize: 24, fill : 0xff1010, align : 'center'})\n * let textMetrics = PIXI.TextMetrics.measureText('Your text', style)\n * ```\n * @memberof PIXI\n */\nexport class TextMetrics\n{\n    /** The text that was measured. */\n    public text: string;\n\n    /** The style that was measured. */\n    public style: TextStyle;\n\n    /** The measured width of the text. */\n    public width: number;\n\n    /** The measured height of the text. */\n    public height: number;\n\n    /** An array of lines of the text broken by new lines and wrapping is specified in style. */\n    public lines: string[];\n\n    /** An array of the line widths for each line matched to `lines`. */\n    public lineWidths: number[];\n\n    /** The measured line height for this style. */\n    public lineHeight: number;\n\n    /** The maximum line width for all measured lines. */\n    public maxLineWidth: number;\n\n    /**\n     * The font properties object from TextMetrics.measureFont.\n     * @type {PIXI.IFontMetrics}\n     */\n    public fontProperties: IFontMetrics;\n\n    public static METRICS_STRING: string;\n    public static BASELINE_SYMBOL: string;\n    public static BASELINE_MULTIPLIER: number;\n    public static HEIGHT_MULTIPLIER: number;\n\n    private static __canvas: HTMLCanvasElement | OffscreenCanvas;\n    private static __context: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D;\n\n    // TODO: These should be protected but they're initialized outside of the class.\n    public static _fonts: { [font: string]: IFontMetrics };\n    public static _newlines: number[];\n    public static _breakingSpaces: number[];\n\n    /**\n     * @param text - the text that was measured\n     * @param style - the style that was measured\n     * @param width - the measured width of the text\n     * @param height - the measured height of the text\n     * @param lines - an array of the lines of text broken by new lines and wrapping if specified in style\n     * @param lineWidths - an array of the line widths for each line matched to `lines`\n     * @param lineHeight - the measured line height for this style\n     * @param maxLineWidth - the maximum line width for all measured lines\n     * @param {PIXI.IFontMetrics} fontProperties - the font properties object from TextMetrics.measureFont\n     */\n    constructor(text: string, style: TextStyle, width: number, height: number, lines: string[], lineWidths: number[],\n        lineHeight: number, maxLineWidth: number, fontProperties: IFontMetrics)\n    {\n        this.text = text;\n        this.style = style;\n        this.width = width;\n        this.height = height;\n        this.lines = lines;\n        this.lineWidths = lineWidths;\n        this.lineHeight = lineHeight;\n        this.maxLineWidth = maxLineWidth;\n        this.fontProperties = fontProperties;\n    }\n\n    /**\n     * Measures the supplied string of text and returns a Rectangle.\n     * @param text - The text to measure.\n     * @param style - The text style to use for measuring\n     * @param wordWrap - Override for if word-wrap should be applied to the text.\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns Measured width and height of the text.\n     */\n    public static measureText(\n        text: string,\n        style: TextStyle,\n        wordWrap?: boolean,\n        canvas: HTMLCanvasElement | OffscreenCanvas = TextMetrics._canvas\n    ): TextMetrics\n    {\n        wordWrap = (wordWrap === undefined || wordWrap === null) ? style.wordWrap : wordWrap;\n        const font = style.toFontString();\n        const fontProperties = TextMetrics.measureFont(font);\n\n        // fallback in case UA disallow canvas data extraction\n        // (toDataURI, getImageData functions)\n        if (fontProperties.fontSize === 0)\n        {\n            fontProperties.fontSize = style.fontSize as number;\n            fontProperties.ascent = style.fontSize as number;\n        }\n\n        const context = canvas.getContext('2d', contextSettings);\n\n        context.font = font;\n\n        const outputText = wordWrap ? TextMetrics.wordWrap(text, style, canvas) : text;\n        const lines = outputText.split(/(?:\\r\\n|\\r|\\n)/);\n        const lineWidths = new Array<number>(lines.length);\n        let maxLineWidth = 0;\n\n        for (let i = 0; i < lines.length; i++)\n        {\n            const lineWidth = context.measureText(lines[i]).width + ((lines[i].length - 1) * style.letterSpacing);\n\n            lineWidths[i] = lineWidth;\n            maxLineWidth = Math.max(maxLineWidth, lineWidth);\n        }\n        let width = maxLineWidth + style.strokeThickness;\n\n        if (style.dropShadow)\n        {\n            width += style.dropShadowDistance;\n        }\n\n        const lineHeight = style.lineHeight || fontProperties.fontSize + style.strokeThickness;\n        let height = Math.max(lineHeight, fontProperties.fontSize + style.strokeThickness)\n            + ((lines.length - 1) * (lineHeight + style.leading));\n\n        if (style.dropShadow)\n        {\n            height += style.dropShadowDistance;\n        }\n\n        return new TextMetrics(\n            text,\n            style,\n            width,\n            height,\n            lines,\n            lineWidths,\n            lineHeight + style.leading,\n            maxLineWidth,\n            fontProperties\n        );\n    }\n\n    /**\n     * Applies newlines to a string to have it optimally fit into the horizontal\n     * bounds set by the Text object's wordWrapWidth property.\n     * @param text - String to apply word wrapping to\n     * @param style - the style to use when wrapping\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns New string with new lines applied where required\n     */\n    private static wordWrap(\n        text: string,\n        style: TextStyle,\n        canvas: HTMLCanvasElement | OffscreenCanvas = TextMetrics._canvas\n    ): string\n    {\n        const context = canvas.getContext('2d', contextSettings);\n\n        let width = 0;\n        let line = '';\n        let lines = '';\n\n        const cache: CharacterWidthCache = Object.create(null);\n        const { letterSpacing, whiteSpace } = style;\n\n        // How to handle whitespaces\n        const collapseSpaces = TextMetrics.collapseSpaces(whiteSpace);\n        const collapseNewlines = TextMetrics.collapseNewlines(whiteSpace);\n\n        // whether or not spaces may be added to the beginning of lines\n        let canPrependSpaces = !collapseSpaces;\n\n        // There is letterSpacing after every char except the last one\n        // t_h_i_s_' '_i_s_' '_a_n_' '_e_x_a_m_p_l_e_' '_!\n        // so for convenience the above needs to be compared to width + 1 extra letterSpace\n        // t_h_i_s_' '_i_s_' '_a_n_' '_e_x_a_m_p_l_e_' '_!_\n        // ________________________________________________\n        // And then the final space is simply no appended to each line\n        const wordWrapWidth = style.wordWrapWidth + letterSpacing;\n\n        // break text into words, spaces and newline chars\n        const tokens = TextMetrics.tokenize(text);\n\n        for (let i = 0; i < tokens.length; i++)\n        {\n            // get the word, space or newlineChar\n            let token = tokens[i];\n\n            // if word is a new line\n            if (TextMetrics.isNewline(token))\n            {\n                // keep the new line\n                if (!collapseNewlines)\n                {\n                    lines += TextMetrics.addLine(line);\n                    canPrependSpaces = !collapseSpaces;\n                    line = '';\n                    width = 0;\n                    continue;\n                }\n\n                // if we should collapse new lines\n                // we simply convert it into a space\n                token = ' ';\n            }\n\n            // if we should collapse repeated whitespaces\n            if (collapseSpaces)\n            {\n                // check both this and the last tokens for spaces\n                const currIsBreakingSpace = TextMetrics.isBreakingSpace(token);\n                const lastIsBreakingSpace = TextMetrics.isBreakingSpace(line[line.length - 1]);\n\n                if (currIsBreakingSpace && lastIsBreakingSpace)\n                {\n                    continue;\n                }\n            }\n\n            // get word width from cache if possible\n            const tokenWidth = TextMetrics.getFromCache(token, letterSpacing, cache, context);\n\n            // word is longer than desired bounds\n            if (tokenWidth > wordWrapWidth)\n            {\n                // if we are not already at the beginning of a line\n                if (line !== '')\n                {\n                    // start newlines for overflow words\n                    lines += TextMetrics.addLine(line);\n                    line = '';\n                    width = 0;\n                }\n\n                // break large word over multiple lines\n                if (TextMetrics.canBreakWords(token, style.breakWords))\n                {\n                    // break word into characters\n                    const characters = TextMetrics.wordWrapSplit(token);\n\n                    // loop the characters\n                    for (let j = 0; j < characters.length; j++)\n                    {\n                        let char = characters[j];\n\n                        let k = 1;\n                        // we are not at the end of the token\n\n                        while (characters[j + k])\n                        {\n                            const nextChar = characters[j + k];\n                            const lastChar = char[char.length - 1];\n\n                            // should not split chars\n                            if (!TextMetrics.canBreakChars(lastChar, nextChar, token, j, style.breakWords))\n                            {\n                                // combine chars & move forward one\n                                char += nextChar;\n                            }\n                            else\n                            {\n                                break;\n                            }\n\n                            k++;\n                        }\n\n                        j += char.length - 1;\n\n                        const characterWidth = TextMetrics.getFromCache(char, letterSpacing, cache, context);\n\n                        if (characterWidth + width > wordWrapWidth)\n                        {\n                            lines += TextMetrics.addLine(line);\n                            canPrependSpaces = false;\n                            line = '';\n                            width = 0;\n                        }\n\n                        line += char;\n                        width += characterWidth;\n                    }\n                }\n\n                // run word out of the bounds\n                else\n                {\n                    // if there are words in this line already\n                    // finish that line and start a new one\n                    if (line.length > 0)\n                    {\n                        lines += TextMetrics.addLine(line);\n                        line = '';\n                        width = 0;\n                    }\n\n                    const isLastToken = i === tokens.length - 1;\n\n                    // give it its own line if it's not the end\n                    lines += TextMetrics.addLine(token, !isLastToken);\n                    canPrependSpaces = false;\n                    line = '';\n                    width = 0;\n                }\n            }\n\n            // word could fit\n            else\n            {\n                // word won't fit because of existing words\n                // start a new line\n                if (tokenWidth + width > wordWrapWidth)\n                {\n                    // if its a space we don't want it\n                    canPrependSpaces = false;\n\n                    // add a new line\n                    lines += TextMetrics.addLine(line);\n\n                    // start a new line\n                    line = '';\n                    width = 0;\n                }\n\n                // don't add spaces to the beginning of lines\n                if (line.length > 0 || !TextMetrics.isBreakingSpace(token) || canPrependSpaces)\n                {\n                    // add the word to the current line\n                    line += token;\n\n                    // update width counter\n                    width += tokenWidth;\n                }\n            }\n        }\n\n        lines += TextMetrics.addLine(line, false);\n\n        return lines;\n    }\n\n    /**\n     * Convienience function for logging each line added during the wordWrap method.\n     * @param line    - The line of text to add\n     * @param newLine - Add new line character to end\n     * @returns A formatted line\n     */\n    private static addLine(line: string, newLine = true): string\n    {\n        line = TextMetrics.trimRight(line);\n\n        line = (newLine) ? `${line}\\n` : line;\n\n        return line;\n    }\n\n    /**\n     * Gets & sets the widths of calculated characters in a cache object\n     * @param key            - The key\n     * @param letterSpacing  - The letter spacing\n     * @param cache          - The cache\n     * @param context        - The canvas context\n     * @returns The from cache.\n     */\n    private static getFromCache(key: string, letterSpacing: number, cache: CharacterWidthCache,\n        context: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D): number\n    {\n        let width = cache[key];\n\n        if (typeof width !== 'number')\n        {\n            const spacing = ((key.length) * letterSpacing);\n\n            width = context.measureText(key).width + spacing;\n            cache[key] = width;\n        }\n\n        return width;\n    }\n\n    /**\n     * Determines whether we should collapse breaking spaces.\n     * @param whiteSpace - The TextStyle property whiteSpace\n     * @returns Should collapse\n     */\n    private static collapseSpaces(whiteSpace: TextStyleWhiteSpace): boolean\n    {\n        return (whiteSpace === 'normal' || whiteSpace === 'pre-line');\n    }\n\n    /**\n     * Determines whether we should collapse newLine chars.\n     * @param whiteSpace - The white space\n     * @returns  should collapse\n     */\n    private static collapseNewlines(whiteSpace: TextStyleWhiteSpace): boolean\n    {\n        return (whiteSpace === 'normal');\n    }\n\n    /**\n     * Trims breaking whitespaces from string.\n     * @param  text - The text\n     * @returns Trimmed string\n     */\n    private static trimRight(text: string): string\n    {\n        if (typeof text !== 'string')\n        {\n            return '';\n        }\n\n        for (let i = text.length - 1; i >= 0; i--)\n        {\n            const char = text[i];\n\n            if (!TextMetrics.isBreakingSpace(char))\n            {\n                break;\n            }\n\n            text = text.slice(0, -1);\n        }\n\n        return text;\n    }\n\n    /**\n     * Determines if char is a newline.\n     * @param  char - The character\n     * @returns True if newline, False otherwise.\n     */\n    private static isNewline(char: string): boolean\n    {\n        if (typeof char !== 'string')\n        {\n            return false;\n        }\n\n        return (TextMetrics._newlines.indexOf(char.charCodeAt(0)) >= 0);\n    }\n\n    /**\n     * Determines if char is a breaking whitespace.\n     *\n     * It allows one to determine whether char should be a breaking whitespace\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param char - The character\n     * @param [_nextChar] - The next character\n     * @returns True if whitespace, False otherwise.\n     */\n    static isBreakingSpace(char: string, _nextChar?: string): boolean\n    {\n        if (typeof char !== 'string')\n        {\n            return false;\n        }\n\n        return (TextMetrics._breakingSpaces.indexOf(char.charCodeAt(0)) >= 0);\n    }\n\n    /**\n     * Splits a string into words, breaking-spaces and newLine characters\n     * @param  text - The text\n     * @returns  A tokenized array\n     */\n    private static tokenize(text: string): string[]\n    {\n        const tokens: string[] = [];\n        let token = '';\n\n        if (typeof text !== 'string')\n        {\n            return tokens;\n        }\n\n        for (let i = 0; i < text.length; i++)\n        {\n            const char = text[i];\n            const nextChar = text[i + 1];\n\n            if (TextMetrics.isBreakingSpace(char, nextChar) || TextMetrics.isNewline(char))\n            {\n                if (token !== '')\n                {\n                    tokens.push(token);\n                    token = '';\n                }\n\n                tokens.push(char);\n\n                continue;\n            }\n\n            token += char;\n        }\n\n        if (token !== '')\n        {\n            tokens.push(token);\n        }\n\n        return tokens;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to customise which words should break\n     * Examples are if the token is CJK or numbers.\n     * It must return a boolean.\n     * @param _token - The token\n     * @param  breakWords - The style attr break words\n     * @returns Whether to break word or not\n     */\n    static canBreakWords(_token: string, breakWords: boolean): boolean\n    {\n        return breakWords;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to determine whether a pair of characters\n     * should be broken by newlines\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param _char - The character\n     * @param _nextChar - The next character\n     * @param _token - The token/word the characters are from\n     * @param _index - The index in the token of the char\n     * @param _breakWords - The style attr break words\n     * @returns whether to break word or not\n     */\n    static canBreakChars(_char: string, _nextChar: string, _token: string, _index: number,\n        _breakWords: boolean): boolean\n    {\n        return true;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It is called when a token (usually a word) has to be split into separate pieces\n     * in order to determine the point to break a word.\n     * It must return an array of characters.\n     * @example\n     * // Correctly splits emojis, eg \"🤪🤪\" will result in two element array, each with one emoji.\n     * TextMetrics.wordWrapSplit = (token) => [...token];\n     * @param  token - The token to split\n     * @returns The characters of the token\n     */\n    static wordWrapSplit(token: string): string[]\n    {\n        return token.split('');\n    }\n\n    /**\n     * Calculates the ascent, descent and fontSize of a given font-style\n     * @param font - String representing the style of the font\n     * @returns Font properties object\n     */\n    public static measureFont(font: string): IFontMetrics\n    {\n        // as this method is used for preparing assets, don't recalculate things if we don't need to\n        if (TextMetrics._fonts[font])\n        {\n            return TextMetrics._fonts[font];\n        }\n\n        const properties: IFontMetrics = {\n            ascent: 0,\n            descent: 0,\n            fontSize: 0,\n        };\n\n        const canvas = TextMetrics._canvas;\n        const context = TextMetrics._context;\n\n        context.font = font;\n\n        const metricsString = TextMetrics.METRICS_STRING + TextMetrics.BASELINE_SYMBOL;\n        const width = Math.ceil(context.measureText(metricsString).width);\n        let baseline = Math.ceil(context.measureText(TextMetrics.BASELINE_SYMBOL).width);\n        const height = Math.ceil(TextMetrics.HEIGHT_MULTIPLIER * baseline);\n\n        baseline = baseline * TextMetrics.BASELINE_MULTIPLIER | 0;\n\n        canvas.width = width;\n        canvas.height = height;\n\n        context.fillStyle = '#f00';\n        context.fillRect(0, 0, width, height);\n\n        context.font = font;\n\n        context.textBaseline = 'alphabetic';\n        context.fillStyle = '#000';\n        context.fillText(metricsString, 0, baseline);\n\n        const imagedata = context.getImageData(0, 0, width, height).data;\n        const pixels = imagedata.length;\n        const line = width * 4;\n\n        let i = 0;\n        let idx = 0;\n        let stop = false;\n\n        // ascent. scan from top to bottom until we find a non red pixel\n        for (i = 0; i < baseline; ++i)\n        {\n            for (let j = 0; j < line; j += 4)\n            {\n                if (imagedata[idx + j] !== 255)\n                {\n                    stop = true;\n                    break;\n                }\n            }\n            if (!stop)\n            {\n                idx += line;\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        properties.ascent = baseline - i;\n\n        idx = pixels - line;\n        stop = false;\n\n        // descent. scan from bottom to top until we find a non red pixel\n        for (i = height; i > baseline; --i)\n        {\n            for (let j = 0; j < line; j += 4)\n            {\n                if (imagedata[idx + j] !== 255)\n                {\n                    stop = true;\n                    break;\n                }\n            }\n\n            if (!stop)\n            {\n                idx -= line;\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        properties.descent = i - baseline;\n        properties.fontSize = properties.ascent + properties.descent;\n\n        TextMetrics._fonts[font] = properties;\n\n        return properties;\n    }\n\n    /**\n     * Clear font metrics in metrics cache.\n     * @param {string} [font] - font name. If font name not set then clear cache for all fonts.\n     */\n    public static clearMetrics(font = ''): void\n    {\n        if (font)\n        {\n            delete TextMetrics._fonts[font];\n        }\n        else\n        {\n            TextMetrics._fonts = {};\n        }\n    }\n\n    /**\n     * Cached canvas element for measuring text\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    public static get _canvas(): HTMLCanvasElement | OffscreenCanvas\n    {\n        if (!TextMetrics.__canvas)\n        {\n            let canvas: HTMLCanvasElement | OffscreenCanvas;\n\n            try\n            {\n                // OffscreenCanvas2D measureText can be up to 40% faster.\n                const c = new OffscreenCanvas(0, 0);\n                const context = c.getContext('2d', contextSettings);\n\n                if (context && context.measureText)\n                {\n                    TextMetrics.__canvas = c;\n\n                    return c;\n                }\n\n                canvas = settings.ADAPTER.createCanvas();\n            }\n            catch (ex)\n            {\n                canvas = settings.ADAPTER.createCanvas();\n            }\n            canvas.width = canvas.height = 10;\n            TextMetrics.__canvas = canvas;\n        }\n\n        return TextMetrics.__canvas;\n    }\n\n    /**\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    public static get _context(): CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D\n    {\n        if (!TextMetrics.__context)\n        {\n            TextMetrics.__context = TextMetrics._canvas.getContext('2d', contextSettings);\n        }\n\n        return TextMetrics.__context;\n    }\n}\n\n/**\n * Internal return object for {@link PIXI.TextMetrics.measureFont `TextMetrics.measureFont`}.\n * @typedef {object} FontMetrics\n * @property {number} ascent - The ascent distance\n * @property {number} descent - The descent distance\n * @property {number} fontSize - Font size from ascent to descent\n * @memberof PIXI.TextMetrics\n * @private\n */\n\n/**\n * Cache of {@see PIXI.TextMetrics.FontMetrics} objects.\n * @memberof PIXI.TextMetrics\n * @type {object}\n * @private\n */\nTextMetrics._fonts = {};\n\n/**\n * String used for calculate font metrics.\n * These characters are all tall to help calculate the height required for text.\n * @static\n * @memberof PIXI.TextMetrics\n * @name METRICS_STRING\n * @type {string}\n * @default |ÉqÅ\n */\nTextMetrics.METRICS_STRING = '|ÉqÅ';\n\n/**\n * Baseline symbol for calculate font metrics.\n * @static\n * @memberof PIXI.TextMetrics\n * @name BASELINE_SYMBOL\n * @type {string}\n * @default M\n */\nTextMetrics.BASELINE_SYMBOL = 'M';\n\n/**\n * Baseline multiplier for calculate font metrics.\n * @static\n * @memberof PIXI.TextMetrics\n * @name BASELINE_MULTIPLIER\n * @type {number}\n * @default 1.4\n */\nTextMetrics.BASELINE_MULTIPLIER = 1.4;\n\n/**\n * Height multiplier for setting height of canvas to calculate font metrics.\n * @static\n * @memberof PIXI.TextMetrics\n * @name HEIGHT_MULTIPLIER\n * @type {number}\n * @default 2.00\n */\nTextMetrics.HEIGHT_MULTIPLIER = 2.0;\n\n/**\n * Cache of new line chars.\n * @memberof PIXI.TextMetrics\n * @type {number[]}\n * @private\n */\nTextMetrics._newlines = [\n    0x000A, // line feed\n    0x000D, // carriage return\n];\n\n/**\n * Cache of breaking spaces.\n * @memberof PIXI.TextMetrics\n * @type {number[]}\n * @private\n */\nTextMetrics._breakingSpaces = [\n    0x0009, // character tabulation\n    0x0020, // space\n    0x2000, // en quad\n    0x2001, // em quad\n    0x2002, // en space\n    0x2003, // em space\n    0x2004, // three-per-em space\n    0x2005, // four-per-em space\n    0x2006, // six-per-em space\n    0x2008, // punctuation space\n    0x2009, // thin space\n    0x200A, // hair space\n    0x205F, // medium mathematical space\n    0x3000, // ideographic space\n];\n\n/**\n * A number, or a string containing a number.\n * @memberof PIXI\n * @typedef {object} IFontMetrics\n * @property {number} ascent - Font ascent\n * @property {number} descent - Font descent\n * @property {number} fontSize - Font size\n */\n", "/* eslint max-depth: [2, 8] */\nimport { Sprite } from '@pixi/sprite';\nimport { Texture  } from '@pixi/core';\nimport { settings } from '@pixi/settings';\nimport { Rectangle } from '@pixi/math';\nimport { sign, trimCanvas, hex2rgb, string2hex } from '@pixi/utils';\nimport { TEXT_GRADIENT } from './const';\nimport { TextStyle } from './TextStyle';\nimport { TextMetrics } from './TextMetrics';\n\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Renderer } from '@pixi/core';\nimport type { ITextStyle } from './TextStyle';\n\nconst defaultDestroyOptions: IDestroyOptions = {\n    texture: true,\n    children: false,\n    baseTexture: true,\n};\n\ninterface ModernContext2D extends CanvasRenderingContext2D\n{\n    // for chrome less 94\n    textLetterSpacing?: number;\n    // for chrome greater 94\n    letterSpacing?: number;\n}\n\n/**\n * A Text Object will create a line or multiple lines of text.\n *\n * The text is created using the [Canvas API](https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API).\n *\n * The primary advantage of this class over BitmapText is that you have great control over the style of the text,\n * which you can change at runtime.\n *\n * The primary disadvantages is that each piece of text has it's own texture, which can use more memory.\n * When text changes, this texture has to be re-generated and re-uploaded to the GPU, taking up time.\n *\n * To split a line you can use '\\n' in your text string, or, on the `style` object,\n * change its `wordWrap` property to true and and give the `wordWrapWidth` property a value.\n *\n * A Text can be created directly from a string and a style object,\n * which can be generated [here](https://pixijs.io/pixi-text-style).\n *\n * ```js\n * let text = new PIXI.Text('This is a PixiJS text',{fontFamily : 'Arial', fontSize: 24, fill : 0xff1010, align : 'center'});\n * ```\n * @memberof PIXI\n */\nexport class Text extends Sprite\n{\n    /**\n     * New behavior for `lineHeight` that's meant to mimic HTML text. A value of `true` will\n     * make sure the first baseline is offset by the `lineHeight` value if it is greater than `fontSize`.\n     * A value of `false` will use the legacy behavior and not change the baseline of the first line.\n     * In the next major release, we'll enable this by default.\n     */\n    public static nextLineHeightBehavior = false;\n\n    /**\n     * New rendering behavior for letter-spacing which uses Chrome's new native API. This will\n     * lead to more accurate letter-spacing results because it does not try to manually draw\n     * each character. However, this Chrome API is experimental and may not serve all cases yet.\n     */\n    public static experimentalLetterSpacing = false;\n\n    /** The canvas element that everything is drawn to. */\n    public canvas: HTMLCanvasElement;\n    /** The canvas 2d context that everything is drawn with. */\n    public context: ModernContext2D;\n    public localStyleID: number;\n    public dirty: boolean;\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default PIXI.settings.RESOLUTION\n     */\n    _resolution: number;\n    _autoResolution: boolean;\n\n    /**\n     * Private tracker for the current text.\n     * @private\n     */\n    protected _text: string;\n\n    /**\n     * Private tracker for the current font.\n     * @private\n     */\n    protected _font: string;\n\n    /**\n     * Private tracker for the current style.\n     * @private\n     */\n    protected _style: TextStyle;\n\n    /**\n     * Private listener to track style changes.\n     * @private\n     */\n    protected _styleListener: () => void;\n\n    /**\n     * Keep track if this Text object created it's own canvas\n     * element (`true`) or uses the constructor argument (`false`).\n     * Used to workaround a GC issues with Safari < 13 when\n     * destroying Text. See `destroy` for more info.\n     */\n    private _ownCanvas: boolean;\n\n    /**\n     * @param text - The string that you would like the text to display\n     * @param {object|PIXI.TextStyle} [style] - The style parameters\n     * @param canvas - The canvas element for drawing text\n     */\n    constructor(text?: string | number, style?: Partial<ITextStyle> | TextStyle, canvas?: HTMLCanvasElement)\n    {\n        let ownCanvas = false;\n\n        if (!canvas)\n        {\n            canvas = settings.ADAPTER.createCanvas();\n            ownCanvas = true;\n        }\n\n        canvas.width = 3;\n        canvas.height = 3;\n\n        const texture = Texture.from(canvas);\n\n        texture.orig = new Rectangle();\n        texture.trim = new Rectangle();\n\n        super(texture);\n\n        this._ownCanvas = ownCanvas;\n        this.canvas = canvas;\n        this.context = canvas.getContext('2d', {\n            // required for trimming to work without warnings\n            willReadFrequently: true,\n        }) as CanvasRenderingContext2D;\n\n        this._resolution = settings.RESOLUTION;\n        this._autoResolution = true;\n        this._text = null;\n        this._style = null;\n        this._styleListener = null;\n        this._font = '';\n\n        this.text = text;\n        this.style = style;\n\n        this.localStyleID = -1;\n    }\n\n    /**\n     * Renders text to its canvas, and updates its texture.\n     *\n     * By default this is used internally to ensure the texture is correct before rendering,\n     * but it can be used called externally, for example from this class to 'pre-generate' the texture from a piece of text,\n     * and then shared across multiple Sprites.\n     * @param respectDirty - Whether to abort updating the text if the Text isn't dirty and the function is called.\n     */\n    public updateText(respectDirty: boolean): void\n    {\n        const style = this._style;\n\n        // check if style has changed..\n        if (this.localStyleID !== style.styleID)\n        {\n            this.dirty = true;\n            this.localStyleID = style.styleID;\n        }\n\n        if (!this.dirty && respectDirty)\n        {\n            return;\n        }\n\n        this._font = this._style.toFontString();\n\n        const context = this.context;\n        const measured = TextMetrics.measureText(this._text || ' ', this._style, this._style.wordWrap, this.canvas);\n        const width = measured.width;\n        const height = measured.height;\n        const lines = measured.lines;\n        const lineHeight = measured.lineHeight;\n        const lineWidths = measured.lineWidths;\n        const maxLineWidth = measured.maxLineWidth;\n        const fontProperties = measured.fontProperties;\n\n        this.canvas.width = Math.ceil(Math.ceil((Math.max(1, width) + (style.padding * 2))) * this._resolution);\n        this.canvas.height = Math.ceil(Math.ceil((Math.max(1, height) + (style.padding * 2))) * this._resolution);\n\n        context.scale(this._resolution, this._resolution);\n\n        context.clearRect(0, 0, this.canvas.width, this.canvas.height);\n\n        context.font = this._font;\n        context.lineWidth = style.strokeThickness;\n        context.textBaseline = style.textBaseline;\n        context.lineJoin = style.lineJoin;\n        context.miterLimit = style.miterLimit;\n\n        let linePositionX: number;\n        let linePositionY: number;\n\n        // require 2 passes if a shadow; the first to draw the drop shadow, the second to draw the text\n        const passesCount = style.dropShadow ? 2 : 1;\n\n        // For v4, we drew text at the colours of the drop shadow underneath the normal text. This gave the correct zIndex,\n        // but features such as alpha and shadowblur did not look right at all, since we were using actual text as a shadow.\n        //\n        // For v5.0.0, we moved over to just use the canvas API for drop shadows, which made them look much nicer and more\n        // visually please, but now because the stroke is drawn and then the fill, drop shadows would appear on both the fill\n        // and the stroke; and fill drop shadows would appear over the top of the stroke.\n        //\n        // For v5.1.1, the new route is to revert to v4 style of drawing text first to get the drop shadows underneath normal\n        // text, but instead drawing text in the correct location, we'll draw it off screen (-paddingY), and then adjust the\n        // drop shadow so only that appears on screen (+paddingY). Now we'll have the correct draw order of the shadow\n        // beneath the text, whilst also having the proper text shadow styling.\n        for (let i = 0; i < passesCount; ++i)\n        {\n            const isShadowPass = style.dropShadow && i === 0;\n            // we only want the drop shadow, so put text way off-screen\n            const dsOffsetText = isShadowPass ? Math.ceil(Math.max(1, height) + (style.padding * 2)) : 0;\n            const dsOffsetShadow = dsOffsetText * this._resolution;\n\n            if (isShadowPass)\n            {\n                // On Safari, text with gradient and drop shadows together do not position correctly\n                // if the scale of the canvas is not 1: https://bugs.webkit.org/show_bug.cgi?id=197689\n                // Therefore we'll set the styles to be a plain black whilst generating this drop shadow\n                context.fillStyle = 'black';\n                context.strokeStyle = 'black';\n\n                const dropShadowColor = style.dropShadowColor;\n                const rgb = hex2rgb(typeof dropShadowColor === 'number' ? dropShadowColor : string2hex(dropShadowColor));\n                const dropShadowBlur = style.dropShadowBlur * this._resolution;\n                const dropShadowDistance = style.dropShadowDistance * this._resolution;\n\n                context.shadowColor = `rgba(${rgb[0] * 255},${rgb[1] * 255},${rgb[2] * 255},${style.dropShadowAlpha})`;\n                context.shadowBlur = dropShadowBlur;\n                context.shadowOffsetX = Math.cos(style.dropShadowAngle) * dropShadowDistance;\n                context.shadowOffsetY = (Math.sin(style.dropShadowAngle) * dropShadowDistance) + dsOffsetShadow;\n            }\n            else\n            {\n                // set canvas text styles\n                context.fillStyle = this._generateFillStyle(style, lines, measured);\n                // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n                //       the setter converts to string. See this thread for more details:\n                //       https://github.com/microsoft/TypeScript/issues/2521\n                context.strokeStyle = style.stroke as string;\n\n                context.shadowColor = 'black';\n                context.shadowBlur = 0;\n                context.shadowOffsetX = 0;\n                context.shadowOffsetY = 0;\n            }\n\n            let linePositionYShift = (lineHeight - fontProperties.fontSize) / 2;\n\n            if (!Text.nextLineHeightBehavior || lineHeight - fontProperties.fontSize < 0)\n            {\n                linePositionYShift = 0;\n            }\n\n            // draw lines line by line\n            for (let i = 0; i < lines.length; i++)\n            {\n                linePositionX = style.strokeThickness / 2;\n                linePositionY = ((style.strokeThickness / 2) + (i * lineHeight)) + fontProperties.ascent\n                    + linePositionYShift;\n\n                if (style.align === 'right')\n                {\n                    linePositionX += maxLineWidth - lineWidths[i];\n                }\n                else if (style.align === 'center')\n                {\n                    linePositionX += (maxLineWidth - lineWidths[i]) / 2;\n                }\n\n                if (style.stroke && style.strokeThickness)\n                {\n                    this.drawLetterSpacing(\n                        lines[i],\n                        linePositionX + style.padding,\n                        linePositionY + style.padding - dsOffsetText,\n                        true\n                    );\n                }\n\n                if (style.fill)\n                {\n                    this.drawLetterSpacing(\n                        lines[i],\n                        linePositionX + style.padding,\n                        linePositionY + style.padding - dsOffsetText\n                    );\n                }\n            }\n        }\n\n        this.updateTexture();\n    }\n\n    /**\n     * Render the text with letter-spacing.\n     * @param text - The text to draw\n     * @param x - Horizontal position to draw the text\n     * @param y - Vertical position to draw the text\n     * @param isStroke - Is this drawing for the outside stroke of the\n     *  text? If not, it's for the inside fill\n     */\n    private drawLetterSpacing(text: string, x: number, y: number, isStroke = false): void\n    {\n        const style = this._style;\n\n        // letterSpacing of 0 means normal\n        const letterSpacing = style.letterSpacing;\n\n        // Checking that we can use moddern canvas2D api\n        // https://developer.chrome.com/origintrials/#/view_trial/3585991203293757441\n        // note: this is unstable API, Chrome less 94 use a `textLetterSpacing`, newest use a letterSpacing\n        // eslint-disable-next-line max-len\n        const supportLetterSpacing = Text.experimentalLetterSpacing\n            && ('letterSpacing' in CanvasRenderingContext2D.prototype\n                || 'textLetterSpacing' in CanvasRenderingContext2D.prototype);\n\n        if (letterSpacing === 0 || supportLetterSpacing)\n        {\n            if (supportLetterSpacing)\n            {\n                this.context.letterSpacing = letterSpacing;\n                this.context.textLetterSpacing = letterSpacing;\n            }\n\n            if (isStroke)\n            {\n                this.context.strokeText(text, x, y);\n            }\n            else\n            {\n                this.context.fillText(text, x, y);\n            }\n\n            return;\n        }\n\n        let currentPosition = x;\n\n        // Using Array.from correctly splits characters whilst keeping emoji together.\n        // This is not supported on IE as it requires ES6, so regular text splitting occurs.\n        // This also doesn't account for emoji that are multiple emoji put together to make something else.\n        // Handling all of this would require a big library itself.\n        // https://medium.com/@giltayar/iterating-over-emoji-characters-the-es6-way-f06e4589516\n        // https://github.com/orling/grapheme-splitter\n        const stringArray = Array.from ? Array.from(text) : text.split('');\n        let previousWidth = this.context.measureText(text).width;\n        let currentWidth = 0;\n\n        for (let i = 0; i < stringArray.length; ++i)\n        {\n            const currentChar = stringArray[i];\n\n            if (isStroke)\n            {\n                this.context.strokeText(currentChar, currentPosition, y);\n            }\n            else\n            {\n                this.context.fillText(currentChar, currentPosition, y);\n            }\n            let textStr = '';\n\n            for (let j = i + 1; j < stringArray.length; ++j)\n            {\n                textStr += stringArray[j];\n            }\n            currentWidth = this.context.measureText(textStr).width;\n            currentPosition += previousWidth - currentWidth + letterSpacing;\n            previousWidth = currentWidth;\n        }\n    }\n\n    /** Updates texture size based on canvas size. */\n    private updateTexture(): void\n    {\n        const canvas = this.canvas;\n\n        if (this._style.trim)\n        {\n            const trimmed = trimCanvas(canvas);\n\n            if (trimmed.data)\n            {\n                canvas.width = trimmed.width;\n                canvas.height = trimmed.height;\n                this.context.putImageData(trimmed.data, 0, 0);\n            }\n        }\n\n        const texture = this._texture;\n        const style = this._style;\n        const padding = style.trim ? 0 : style.padding;\n        const baseTexture = texture.baseTexture;\n\n        texture.trim.width = texture._frame.width = canvas.width / this._resolution;\n        texture.trim.height = texture._frame.height = canvas.height / this._resolution;\n        texture.trim.x = -padding;\n        texture.trim.y = -padding;\n\n        texture.orig.width = texture._frame.width - (padding * 2);\n        texture.orig.height = texture._frame.height - (padding * 2);\n\n        // call sprite onTextureUpdate to update scale if _width or _height were set\n        this._onTextureUpdate();\n\n        baseTexture.setRealSize(canvas.width, canvas.height, this._resolution);\n\n        texture.updateUvs();\n\n        this.dirty = false;\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        if (this._autoResolution && this._resolution !== renderer.resolution)\n        {\n            this._resolution = renderer.resolution;\n            this.dirty = true;\n        }\n\n        this.updateText(true);\n\n        super._render(renderer);\n    }\n\n    /** Updates the transform on all children of this container for rendering. */\n    public updateTransform(): void\n    {\n        this.updateText(true);\n\n        super.updateTransform();\n    }\n\n    public getBounds(skipUpdate?: boolean, rect?: Rectangle): Rectangle\n    {\n        this.updateText(true);\n\n        if (this._textureID === -1)\n        {\n            // texture was updated: recalculate transforms\n            skipUpdate = false;\n        }\n\n        return super.getBounds(skipUpdate, rect);\n    }\n\n    /**\n     * Gets the local bounds of the text object.\n     * @param rect - The output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        this.updateText(true);\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /** Calculates the bounds of the Text as a rectangle. The bounds calculation takes the worldTransform into account. */\n    protected _calculateBounds(): void\n    {\n        this.calculateVertices();\n        // if we have already done this on THIS frame.\n        this._bounds.addQuad(this.vertexData);\n    }\n\n    /**\n     * Generates the fill style. Can automatically generate a gradient based on the fill style being an array\n     * @param style - The style.\n     * @param lines - The lines of text.\n     * @param metrics\n     * @returns The fill style\n     */\n    private _generateFillStyle(\n        style: TextStyle, lines: string[], metrics: TextMetrics\n    ): string | CanvasGradient | CanvasPattern\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        const fillStyle: string | string[] | CanvasGradient | CanvasPattern = style.fill as any;\n\n        if (!Array.isArray(fillStyle))\n        {\n            return fillStyle;\n        }\n        else if (fillStyle.length === 1)\n        {\n            return fillStyle[0];\n        }\n\n        // the gradient will be evenly spaced out according to how large the array is.\n        // ['#FF0000', '#00FF00', '#0000FF'] would created stops at 0.25, 0.5 and 0.75\n        let gradient: string[] | CanvasGradient;\n\n        // a dropshadow will enlarge the canvas and result in the gradient being\n        // generated with the incorrect dimensions\n        const dropShadowCorrection = (style.dropShadow) ? style.dropShadowDistance : 0;\n\n        // should also take padding into account, padding can offset the gradient\n        const padding = style.padding || 0;\n\n        const width = (this.canvas.width / this._resolution) - dropShadowCorrection - (padding * 2);\n        const height = (this.canvas.height / this._resolution) - dropShadowCorrection - (padding * 2);\n\n        // make a copy of the style settings, so we can manipulate them later\n        const fill = fillStyle.slice();\n        const fillGradientStops = style.fillGradientStops.slice();\n\n        // wanting to evenly distribute the fills. So an array of 4 colours should give fills of 0.25, 0.5 and 0.75\n        if (!fillGradientStops.length)\n        {\n            const lengthPlus1 = fill.length + 1;\n\n            for (let i = 1; i < lengthPlus1; ++i)\n            {\n                fillGradientStops.push(i / lengthPlus1);\n            }\n        }\n\n        // stop the bleeding of the last gradient on the line above to the top gradient of the this line\n        // by hard defining the first gradient colour at point 0, and last gradient colour at point 1\n        fill.unshift(fillStyle[0]);\n        fillGradientStops.unshift(0);\n\n        fill.push(fillStyle[fillStyle.length - 1]);\n        fillGradientStops.push(1);\n\n        if (style.fillGradientType === TEXT_GRADIENT.LINEAR_VERTICAL)\n        {\n            // start the gradient at the top center of the canvas, and end at the bottom middle of the canvas\n            gradient = this.context.createLinearGradient(width / 2, padding, width / 2, height + padding);\n\n            // we need to repeat the gradient so that each individual line of text has the same vertical gradient effect\n            // ['#FF0000', '#00FF00', '#0000FF'] over 2 lines would create stops at 0.125, 0.25, 0.375, 0.625, 0.75, 0.875\n\n            // Actual height of the text itself, not counting spacing for lineHeight/leading/dropShadow etc\n            const textHeight = metrics.fontProperties.fontSize + style.strokeThickness;\n\n            for (let i = 0; i < lines.length; i++)\n            {\n                const lastLineBottom = (metrics.lineHeight * (i - 1)) + textHeight;\n                const thisLineTop = metrics.lineHeight * i;\n                let thisLineGradientStart = thisLineTop;\n\n                // Handle case where last & this line overlap\n                if (i > 0 && lastLineBottom > thisLineTop)\n                {\n                    thisLineGradientStart = (thisLineTop + lastLineBottom) / 2;\n                }\n\n                const thisLineBottom = thisLineTop + textHeight;\n                const nextLineTop = metrics.lineHeight * (i + 1);\n                let thisLineGradientEnd = thisLineBottom;\n\n                // Handle case where this & next line overlap\n                if (i + 1 < lines.length && nextLineTop < thisLineBottom)\n                {\n                    thisLineGradientEnd = (thisLineBottom + nextLineTop) / 2;\n                }\n\n                // textHeight, but as a 0-1 size in global gradient stop space\n                const gradStopLineHeight = (thisLineGradientEnd - thisLineGradientStart) / height;\n\n                for (let j = 0; j < fill.length; j++)\n                {\n                    // 0-1 stop point for the current line, multiplied to global space afterwards\n                    let lineStop = 0;\n\n                    if (typeof fillGradientStops[j] === 'number')\n                    {\n                        lineStop = fillGradientStops[j];\n                    }\n                    else\n                    {\n                        lineStop = j / fill.length;\n                    }\n\n                    let globalStop = Math.min(1, Math.max(0,\n                        (thisLineGradientStart / height) + (lineStop * gradStopLineHeight)));\n\n                    // There's potential for floating point precision issues at the seams between gradient repeats.\n                    globalStop = Number(globalStop.toFixed(5));\n                    gradient.addColorStop(globalStop, fill[j]);\n                }\n            }\n        }\n        else\n        {\n            // start the gradient at the center left of the canvas, and end at the center right of the canvas\n            gradient = this.context.createLinearGradient(padding, height / 2, width + padding, height / 2);\n\n            // can just evenly space out the gradients in this case, as multiple lines makes no difference\n            // to an even left to right gradient\n            const totalIterations = fill.length + 1;\n            let currentIteration = 1;\n\n            for (let i = 0; i < fill.length; i++)\n            {\n                let stop: number;\n\n                if (typeof fillGradientStops[i] === 'number')\n                {\n                    stop = fillGradientStops[i];\n                }\n                else\n                {\n                    stop = currentIteration / totalIterations;\n                }\n                gradient.addColorStop(stop, fill[i]);\n                currentIteration++;\n            }\n        }\n\n        return gradient;\n    }\n\n    /**\n     * Destroys this text object.\n     *\n     * Note* Unlike a Sprite, a Text object will automatically destroy its baseTexture and texture as\n     * the majority of the time the texture will not be shared with any other Sprites.\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their\n     *  destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=true] - Should it destroy the current texture of the sprite as well\n     * @param {boolean} [options.baseTexture=true] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        if (typeof options === 'boolean')\n        {\n            options = { children: options };\n        }\n\n        options = Object.assign({}, defaultDestroyOptions, options);\n\n        super.destroy(options);\n\n        // set canvas width and height to 0 to workaround memory leak in Safari < 13\n        // https://stackoverflow.com/questions/52532614/total-canvas-memory-use-exceeds-the-maximum-limit-safari-12\n        if (this._ownCanvas)\n        {\n            this.canvas.height = this.canvas.width = 0;\n        }\n\n        // make sure to reset the context and canvas.. dont want this hanging around in memory!\n        this.context = null;\n        this.canvas = null;\n\n        this._style = null;\n    }\n\n    /** The width of the Text, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        this.updateText(true);\n\n        return Math.abs(this.scale.x) * this._texture.orig.width;\n    }\n\n    set width(value: number)\n    {\n        this.updateText(true);\n\n        const s = sign(this.scale.x) || 1;\n\n        this.scale.x = s * value / this._texture.orig.width;\n        this._width = value;\n    }\n\n    /** The height of the Text, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        this.updateText(true);\n\n        return Math.abs(this.scale.y) * this._texture.orig.height;\n    }\n\n    set height(value: number)\n    {\n        this.updateText(true);\n\n        const s = sign(this.scale.y) || 1;\n\n        this.scale.y = s * value / this._texture.orig.height;\n        this._height = value;\n    }\n\n    /**\n     * Set the style of the text.\n     *\n     * Set up an event listener to listen for changes on the style object and mark the text as dirty.\n     */\n    get style(): TextStyle | Partial<ITextStyle>\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the ITextStyle\n        //       since the setter creates the TextStyle. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        return this._style;\n    }\n\n    set style(style: TextStyle | Partial<ITextStyle>)\n    {\n        style = style || {};\n\n        if (style instanceof TextStyle)\n        {\n            this._style = style;\n        }\n        else\n        {\n            this._style = new TextStyle(style);\n        }\n\n        this.localStyleID = -1;\n        this.dirty = true;\n    }\n\n    /** Set the copy for the text object. To split a line you can use '\\n'. */\n    get text(): string\n    {\n        return this._text;\n    }\n\n    set text(text: string | number)\n    {\n        text = String(text === null || text === undefined ? '' : text);\n\n        if (this._text === text)\n        {\n            return;\n        }\n        this._text = text;\n        this.dirty = true;\n    }\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default 1\n     */\n    get resolution(): number\n    {\n        return this._resolution;\n    }\n\n    set resolution(value: number)\n    {\n        this._autoResolution = false;\n\n        if (this._resolution === value)\n        {\n            return;\n        }\n\n        this._resolution = value;\n        this.dirty = true;\n    }\n}\n"], "names": ["arguments", "TEXT_GRADIENT", "hex2string", "settings", "Texture", "Rectangle", "hex2rgb", "string2hex", "trimCanvas", "sign", "Sprite"], "mappings": ";;;;;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;ICzNA;;;;;;;;;IASG;AACSC,mCAKX;IALD,CAAA,UAAY,aAAa,EAAA;IAGrB,IAAA,aAAA,CAAA,aAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,iBAAmB,CAAA;IACnB,IAAA,aAAA,CAAA,aAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;IACzB,CAAC,EALWA,qBAAa,KAAbA,qBAAa,GAKxB,EAAA,CAAA,CAAA;;ICfD;IA+CA,IAAM,YAAY,GAAe;IAC7B,IAAA,KAAK,EAAE,MAAM;IACb,IAAA,UAAU,EAAE,KAAK;IACjB,IAAA,UAAU,EAAE,KAAK;IACjB,IAAA,eAAe,EAAE,CAAC;IAClB,IAAA,eAAe,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IAC5B,IAAA,cAAc,EAAE,CAAC;IACjB,IAAA,eAAe,EAAE,OAAO;IACxB,IAAA,kBAAkB,EAAE,CAAC;IACrB,IAAA,IAAI,EAAE,OAAO;QACb,gBAAgB,EAAEA,qBAAa,CAAC,eAAe;IAC/C,IAAA,iBAAiB,EAAE,EAAE;IACrB,IAAA,UAAU,EAAE,OAAO;IACnB,IAAA,QAAQ,EAAE,EAAE;IACZ,IAAA,SAAS,EAAE,QAAQ;IACnB,IAAA,WAAW,EAAE,QAAQ;IACrB,IAAA,UAAU,EAAE,QAAQ;IACpB,IAAA,aAAa,EAAE,CAAC;IAChB,IAAA,UAAU,EAAE,CAAC;IACb,IAAA,QAAQ,EAAE,OAAO;IACjB,IAAA,UAAU,EAAE,EAAE;IACd,IAAA,OAAO,EAAE,CAAC;IACV,IAAA,MAAM,EAAE,OAAO;IACf,IAAA,eAAe,EAAE,CAAC;IAClB,IAAA,YAAY,EAAE,YAAY;IAC1B,IAAA,IAAI,EAAE,KAAK;IACX,IAAA,UAAU,EAAE,KAAK;IACjB,IAAA,QAAQ,EAAE,KAAK;IACf,IAAA,aAAa,EAAE,GAAG;IAClB,IAAA,OAAO,EAAE,CAAC;KACb,CAAC;IAEF,IAAM,mBAAmB,GAAG;QACxB,OAAO;QACP,YAAY;QACZ,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW,EACd,CAAC;IAEF;;;;;;;;IAQG;AACH,QAAA,SAAA,kBAAA,YAAA;IAkCI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+CG;IACH,IAAA,SAAA,SAAA,CAAY,KAA2B,EAAA;IAEnC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;YAEjB,IAAI,CAAC,KAAK,EAAE,CAAC;IAEb,QAAA,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SAC1C;IAED;;;;;IAKG;IACI,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;YAEI,IAAM,gBAAgB,GAAwB,EAAE,CAAC;IAEjD,QAAA,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAEzD,QAAA,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC;SAC1C,CAAA;;IAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;SACxD,CAAA;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAK,CAAA,SAAA,EAAA,OAAA,EAAA;IALT;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,MAAM,CAAC;aACtB;IACD,QAAA,GAAA,EAAA,UAAU,KAAqB,EAAA;IAE3B,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EACzB;IACI,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;oBACpB,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;IAAd,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IACD,QAAA,GAAA,EAAA,UAAe,UAAmB,EAAA;IAE9B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;IAAd,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IACD,QAAA,GAAA,EAAA,UAAe,UAAmB,EAAA;IAE9B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;;IAAnB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;aAChC;IACD,QAAA,GAAA,EAAA,UAAoB,eAAuB,EAAA;IAEvC,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAC7C;IACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;oBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;;IAAnB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;aAChC;IACD,QAAA,GAAA,EAAA,UAAoB,eAAuB,EAAA;IAEvC,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAC7C;IACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;oBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAc,CAAA,SAAA,EAAA,gBAAA,EAAA;;IAAlB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,eAAe,CAAC;aAC/B;IACD,QAAA,GAAA,EAAA,UAAmB,cAAsB,EAAA;IAErC,YAAA,IAAI,IAAI,CAAC,eAAe,KAAK,cAAc,EAC3C;IACI,gBAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;oBACtC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;;IAAnB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;aAChC;IACD,QAAA,GAAA,EAAA,UAAoB,eAAgC,EAAA;IAEhD,YAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC;IAC9C,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,WAAW,EACzC;IACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;oBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IATA,KAAA,CAAA,CAAA;IAYD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;;IAAtB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,mBAAmB,CAAC;aACnC;IACD,QAAA,GAAA,EAAA,UAAuB,kBAA0B,EAAA;IAE7C,YAAA,IAAI,IAAI,CAAC,mBAAmB,KAAK,kBAAkB,EACnD;IACI,gBAAA,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;oBAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAkBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAI,CAAA,SAAA,EAAA,MAAA,EAAA;IARR;;;;;;;IAOG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,KAAK,CAAC;aACrB;IACD,QAAA,GAAA,EAAA,UAAS,IAAmB,EAAA;;;;;;IAOxB,YAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,IAAW,CAAC,CAAC;IAC1C,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAC9B;IACI,gBAAA,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;oBACzB,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IAdA,KAAA,CAAA,CAAA;IAqBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAgB,CAAA,SAAA,EAAA,kBAAA,EAAA;IALpB;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,iBAAiB,CAAC;aACjC;IACD,QAAA,GAAA,EAAA,UAAqB,gBAA+B,EAAA;IAEhD,YAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,gBAAgB,EAC/C;IACI,gBAAA,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;oBAC1C,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAiB,CAAA,SAAA,EAAA,mBAAA,EAAA;IAJrB;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,kBAAkB,CAAC;aAClC;IACD,QAAA,GAAA,EAAA,UAAsB,iBAA2B,EAAA;gBAE7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAC,iBAAiB,CAAC,EAC9D;IACI,gBAAA,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;oBAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;IAAd,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IACD,QAAA,GAAA,EAAA,UAAe,UAA6B,EAAA;IAExC,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,EAClC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IAJZ;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;IACD,QAAA,GAAA,EAAA,UAAa,QAAyB,EAAA;IAElC,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAC/B;IACI,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;oBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAS,CAAA,SAAA,EAAA,WAAA,EAAA;IANb;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,UAAU,CAAC;aAC1B;IACD,QAAA,GAAA,EAAA,UAAc,SAA6B,EAAA;IAEvC,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EACjC;IACI,gBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;oBAC5B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAW,CAAA,SAAA,EAAA,aAAA,EAAA;IANf;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,YAAY,CAAC;aAC5B;IACD,QAAA,GAAA,EAAA,UAAgB,WAAiC,EAAA;IAE7C,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW,EACrC;IACI,gBAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;oBAChC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;IANd;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IACD,QAAA,GAAA,EAAA,UAAe,UAA+B,EAAA;IAE1C,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;;IAAjB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,cAAc,CAAC;aAC9B;IACD,QAAA,GAAA,EAAA,UAAkB,aAAqB,EAAA;IAEnC,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EACzC;IACI,gBAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;oBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;IAAd,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IACD,QAAA,GAAA,EAAA,UAAe,UAAkB,EAAA;IAE7B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;;IAAX,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,QAAQ,CAAC;aACxB;IACD,QAAA,GAAA,EAAA,UAAY,OAAe,EAAA;IAEvB,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAC7B;IACI,gBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;oBACxB,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IANZ;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;IACD,QAAA,GAAA,EAAA,UAAa,QAA2B,EAAA;IAEpC,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAC/B;IACI,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;oBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAeD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;IALd;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IACD,QAAA,GAAA,EAAA,UAAe,UAAkB,EAAA;IAE7B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;IAJX;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,QAAQ,CAAC;aACxB;IACD,QAAA,GAAA,EAAA,UAAY,OAAe,EAAA;IAEvB,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAC7B;IACI,gBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;oBACxB,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAM,CAAA,SAAA,EAAA,QAAA,EAAA;IAJV;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,OAAO,CAAC;aACvB;IACD,QAAA,GAAA,EAAA,UAAW,MAAuB,EAAA;;;;IAK9B,YAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrC,YAAA,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,EAChC;IACI,gBAAA,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;oBAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IAZA,KAAA,CAAA,CAAA;IAmBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;IALnB;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;aAChC;IACD,QAAA,GAAA,EAAA,UAAoB,eAAuB,EAAA;IAEvC,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAC7C;IACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;oBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAeD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAY,CAAA,SAAA,EAAA,cAAA,EAAA;IALhB;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,aAAa,CAAC;aAC7B;IACD,QAAA,GAAA,EAAA,UAAiB,YAAmC,EAAA;IAEhD,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY,EACvC;IACI,gBAAA,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;oBAClC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAI,CAAA,SAAA,EAAA,MAAA,EAAA;;IAAR,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,KAAK,CAAC;aACrB;IACD,QAAA,GAAA,EAAA,UAAS,IAAa,EAAA;IAElB,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EACvB;IACI,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;oBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAsBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;IAZd;;;;;;;;;;;IAWG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IACD,QAAA,GAAA,EAAA,UAAe,UAA+B,EAAA;IAE1C,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;;IAAZ,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;IACD,QAAA,GAAA,EAAA,UAAa,QAAiB,EAAA;IAE1B,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAC/B;IACI,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;oBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;;IAAjB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,cAAc,CAAC;aAC9B;IACD,QAAA,GAAA,EAAA,UAAkB,aAAqB,EAAA;IAEnC,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EACzC;IACI,gBAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;oBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;IAClB,aAAA;aACJ;;;IARA,KAAA,CAAA,CAAA;IAUD;;;;IAIG;IACI,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAnB,YAAA;;YAGI,IAAM,cAAc,GAAG,CAAC,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAO,IAAI,CAAC,QAAQ,GAAI,IAAA,GAAG,IAAI,CAAC,QAAQ,CAAC;;;IAIlG,QAAA,IAAI,YAAY,GAAoB,IAAI,CAAC,UAAU,CAAC;YAEpD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EACnC;gBACI,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,SAAA;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EACjD;;gBAEI,IAAI,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;IAGxC,YAAA,IAAI,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAC3F;IACI,gBAAA,UAAU,GAAG,IAAA,GAAI,UAAU,GAAA,IAAG,CAAC;IAClC,aAAA;IACA,YAAA,YAAyB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;IAC9C,SAAA;YAED,OAAU,IAAI,CAAC,SAAS,GAAA,GAAA,GAAI,IAAI,CAAC,WAAW,SAAI,IAAI,CAAC,UAAU,GAAI,GAAA,GAAA,cAAc,SAAK,YAAyB,CAAC,IAAI,CAAC,GAAG,CAAG,CAAC;SAC/H,CAAA;QACL,OAAC,SAAA,CAAA;IAAD,CAAC,EAAA,EAAA;IAED;;;;;IAKG;IACH,SAAS,cAAc,CAAC,KAAoB,EAAA;IAExC,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAC7B;IACI,QAAA,OAAOC,gBAAU,CAAC,KAAK,CAAC,CAAC;IAC5B,KAAA;IACI,SAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAClC;YACI,IAAK,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAC9B;gBACI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACpC,SAAA;IACJ,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAWD,SAAS,QAAQ,CAAC,KAAsC,EAAA;IAEpD,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EACzB;IACI,QAAA,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;IAChC,KAAA;IAED,SAAA;IACI,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EACrC;gBACI,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,SAAA;IAED,QAAA,OAAO,KAAiB,CAAC;IAC5B,KAAA;IACL,CAAC;IAED;;;;;;;IAOG;IACH,SAAS,cAAc,CAAI,MAAW,EAAE,MAAW,EAAA;IAE/C,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EACpD;IACI,QAAA,OAAO,KAAK,CAAC;IAChB,KAAA;IAED,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EACnC;IACI,QAAA,OAAO,KAAK,CAAC;IAChB,KAAA;IAED,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EACtC;YACI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAC3B;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IACJ,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,kBAAkB,CAAC,MAA2B,EAAE,MAA2B,EAAE,WAAgC,EAAA;IAClH,IAAA,KAAK,IAAM,IAAI,IAAI,WAAW,EAAE;YAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;gBAC7B,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IACvC,SAAA;IAAM,aAAA;gBACH,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/B,SAAA;IACJ,KAAA;IACL;;IC9yBA;IACA,IAAM,eAAe,GAAG;;IAEpB,IAAA,kBAAkB,EAAE,IAAI;KACS,CAAC;IAEtC;;;;;;;;IAQG;AACH,QAAA,WAAA,kBAAA,YAAA;IA6CI;;;;;;;;;;IAUG;IACH,IAAA,SAAA,WAAA,CAAY,IAAY,EAAE,KAAgB,EAAE,KAAa,EAAE,MAAc,EAAE,KAAe,EAAE,UAAoB,EAC5G,UAAkB,EAAE,YAAoB,EAAE,cAA4B,EAAA;IAEtE,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACjC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;SACxC;IAED;;;;;;;IAOG;QACW,WAAW,CAAA,WAAA,GAAzB,UACI,IAAY,EACZ,KAAgB,EAChB,QAAkB,EAClB,MAAiE,EAAA;IAAjE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAA8C,WAAW,CAAC,OAAO,CAAA,EAAA;YAGjE,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACrF,QAAA,IAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YAClC,IAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;;IAIrD,QAAA,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,EACjC;IACI,YAAA,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;IACnD,YAAA,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC,QAAkB,CAAC;IACpD,SAAA;YAED,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAEzD,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YAEpB,IAAM,UAAU,GAAG,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;YAC/E,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjD,IAAM,UAAU,GAAG,IAAI,KAAK,CAAS,KAAK,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;IACI,YAAA,IAAM,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;IAEtG,YAAA,UAAU,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;gBAC1B,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACpD,SAAA;IACD,QAAA,IAAI,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC,eAAe,CAAC;YAEjD,IAAI,KAAK,CAAC,UAAU,EACpB;IACI,YAAA,KAAK,IAAI,KAAK,CAAC,kBAAkB,CAAC;IACrC,SAAA;IAED,QAAA,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,CAAC;IACvF,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,CAAC;IAC5E,eAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAE1D,IAAI,KAAK,CAAC,UAAU,EACpB;IACI,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;IACtC,SAAA;YAED,OAAO,IAAI,WAAW,CAClB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,UAAU,EACV,UAAU,GAAG,KAAK,CAAC,OAAO,EAC1B,YAAY,EACZ,cAAc,CACjB,CAAC;SACL,CAAA;IAED;;;;;;;IAOG;IACY,IAAA,WAAA,CAAA,QAAQ,GAAvB,UACI,IAAY,EACZ,KAAgB,EAChB,MAAiE,EAAA;IAAjE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAA8C,WAAW,CAAC,OAAO,CAAA,EAAA;YAGjE,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAEzD,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,IAAI,KAAK,GAAG,EAAE,CAAC;YAEf,IAAM,KAAK,GAAwB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAA,aAAa,GAAiB,KAAK,CAAA,aAAtB,EAAE,UAAU,GAAK,KAAK,CAAA,UAAV,CAAW;;YAG5C,IAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC9D,IAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;;IAGlE,QAAA,IAAI,gBAAgB,GAAG,CAAC,cAAc,CAAC;;;;;;;IAQvC,QAAA,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;;YAG1D,IAAM,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAE1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EACtC;;IAEI,YAAA,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;;IAGtB,YAAA,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,EAChC;;oBAEI,IAAI,CAAC,gBAAgB,EACrB;IACI,oBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBACnC,gBAAgB,GAAG,CAAC,cAAc,CAAC;wBACnC,IAAI,GAAG,EAAE,CAAC;wBACV,KAAK,GAAG,CAAC,CAAC;wBACV,SAAS;IACZ,iBAAA;;;oBAID,KAAK,GAAG,GAAG,CAAC;IACf,aAAA;;IAGD,YAAA,IAAI,cAAc,EAClB;;oBAEI,IAAM,mBAAmB,GAAG,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC/D,gBAAA,IAAM,mBAAmB,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;oBAE/E,IAAI,mBAAmB,IAAI,mBAAmB,EAC9C;wBACI,SAAS;IACZ,iBAAA;IACJ,aAAA;;IAGD,YAAA,IAAM,UAAU,GAAG,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;;gBAGlF,IAAI,UAAU,GAAG,aAAa,EAC9B;;oBAEI,IAAI,IAAI,KAAK,EAAE,EACf;;IAEI,oBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBACnC,IAAI,GAAG,EAAE,CAAC;wBACV,KAAK,GAAG,CAAC,CAAC;IACb,iBAAA;;oBAGD,IAAI,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,EACtD;;wBAEI,IAAM,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;IAGpD,oBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAC1C;IACI,wBAAA,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;4BAEzB,IAAI,CAAC,GAAG,CAAC,CAAC;;IAGV,wBAAA,OAAO,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EACxB;gCACI,IAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gCACnC,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;IAGvC,4BAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,EAC9E;;oCAEI,IAAI,IAAI,QAAQ,CAAC;IACpB,6BAAA;IAED,iCAAA;oCACI,MAAM;IACT,6BAAA;IAED,4BAAA,CAAC,EAAE,CAAC;IACP,yBAAA;IAED,wBAAA,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAErB,wBAAA,IAAM,cAAc,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAErF,wBAAA,IAAI,cAAc,GAAG,KAAK,GAAG,aAAa,EAC1C;IACI,4BAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gCACnC,gBAAgB,GAAG,KAAK,CAAC;gCACzB,IAAI,GAAG,EAAE,CAAC;gCACV,KAAK,GAAG,CAAC,CAAC;IACb,yBAAA;4BAED,IAAI,IAAI,IAAI,CAAC;4BACb,KAAK,IAAI,cAAc,CAAC;IAC3B,qBAAA;IACJ,iBAAA;;IAID,qBAAA;;;IAGI,oBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EACnB;IACI,wBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BACnC,IAAI,GAAG,EAAE,CAAC;4BACV,KAAK,GAAG,CAAC,CAAC;IACb,qBAAA;wBAED,IAAM,WAAW,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;;wBAG5C,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;wBAClD,gBAAgB,GAAG,KAAK,CAAC;wBACzB,IAAI,GAAG,EAAE,CAAC;wBACV,KAAK,GAAG,CAAC,CAAC;IACb,iBAAA;IACJ,aAAA;;IAID,iBAAA;;;IAGI,gBAAA,IAAI,UAAU,GAAG,KAAK,GAAG,aAAa,EACtC;;wBAEI,gBAAgB,GAAG,KAAK,CAAC;;IAGzB,oBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;wBAGnC,IAAI,GAAG,EAAE,CAAC;wBACV,KAAK,GAAG,CAAC,CAAC;IACb,iBAAA;;IAGD,gBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,gBAAgB,EAC9E;;wBAEI,IAAI,IAAI,KAAK,CAAC;;wBAGd,KAAK,IAAI,UAAU,CAAC;IACvB,iBAAA;IACJ,aAAA;IACJ,SAAA;YAED,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAE1C,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAED;;;;;IAKG;IACY,IAAA,WAAA,CAAA,OAAO,GAAtB,UAAuB,IAAY,EAAE,OAAc,EAAA;IAAd,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAc,GAAA,IAAA,CAAA,EAAA;IAE/C,QAAA,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAEnC,QAAA,IAAI,GAAG,CAAC,OAAO,IAAO,IAAI,GAAI,IAAA,GAAG,IAAI,CAAC;IAEtC,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;IAOG;QACY,WAAY,CAAA,YAAA,GAA3B,UAA4B,GAAW,EAAE,aAAqB,EAAE,KAA0B,EACtF,OAAqE,EAAA;IAErE,QAAA,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IAEvB,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAC7B;gBACI,IAAM,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,aAAa,CAAC,CAAC;gBAE/C,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;IACjD,YAAA,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtB,SAAA;IAED,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAED;;;;IAIG;QACY,WAAc,CAAA,cAAA,GAA7B,UAA8B,UAA+B,EAAA;YAEzD,QAAQ,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,UAAU,EAAE;SACjE,CAAA;IAED;;;;IAIG;QACY,WAAgB,CAAA,gBAAA,GAA/B,UAAgC,UAA+B,EAAA;IAE3D,QAAA,QAAQ,UAAU,KAAK,QAAQ,EAAE;SACpC,CAAA;IAED;;;;IAIG;QACY,WAAS,CAAA,SAAA,GAAxB,UAAyB,IAAY,EAAA;IAEjC,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;IACI,YAAA,OAAO,EAAE,CAAC;IACb,SAAA;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EACzC;IACI,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAErB,YAAA,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,EACtC;oBACI,MAAM;IACT,aAAA;gBAED,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5B,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACY,WAAS,CAAA,SAAA,GAAxB,UAAyB,IAAY,EAAA;IAEjC,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,QAAQ,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;SACnE,CAAA;IAED;;;;;;;;;IASG;IACI,IAAA,WAAA,CAAA,eAAe,GAAtB,UAAuB,IAAY,EAAE,SAAkB,EAAA;IAEnD,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,QAAQ,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;SACzE,CAAA;IAED;;;;IAIG;QACY,WAAQ,CAAA,QAAA,GAAvB,UAAwB,IAAY,EAAA;YAEhC,IAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,IAAI,KAAK,GAAG,EAAE,CAAC;IAEf,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;IACI,YAAA,OAAO,MAAM,CAAC;IACjB,SAAA;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;IACI,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrB,IAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7B,YAAA,IAAI,WAAW,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9E;oBACI,IAAI,KAAK,KAAK,EAAE,EAChB;IACI,oBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnB,KAAK,GAAG,EAAE,CAAC;IACd,iBAAA;IAED,gBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAElB,SAAS;IACZ,aAAA;gBAED,KAAK,IAAI,IAAI,CAAC;IACjB,SAAA;YAED,IAAI,KAAK,KAAK,EAAE,EAChB;IACI,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtB,SAAA;IAED,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;IAED;;;;;;;;;IASG;IACI,IAAA,WAAA,CAAA,aAAa,GAApB,UAAqB,MAAc,EAAE,UAAmB,EAAA;IAEpD,QAAA,OAAO,UAAU,CAAC;SACrB,CAAA;IAED;;;;;;;;;;;;;IAaG;QACI,WAAa,CAAA,aAAA,GAApB,UAAqB,KAAa,EAAE,SAAiB,EAAE,MAAc,EAAE,MAAc,EACjF,WAAoB,EAAA;IAEpB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;;;;;IAWG;QACI,WAAa,CAAA,aAAA,GAApB,UAAqB,KAAa,EAAA;IAE9B,QAAA,OAAO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SAC1B,CAAA;IAED;;;;IAIG;QACW,WAAW,CAAA,WAAA,GAAzB,UAA0B,IAAY,EAAA;;IAGlC,QAAA,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAC5B;IACI,YAAA,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,SAAA;IAED,QAAA,IAAM,UAAU,GAAiB;IAC7B,YAAA,MAAM,EAAE,CAAC;IACT,YAAA,OAAO,EAAE,CAAC;IACV,YAAA,QAAQ,EAAE,CAAC;aACd,CAAC;IAEF,QAAA,IAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IACnC,QAAA,IAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;IAErC,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YAEpB,IAAM,aAAa,GAAG,WAAW,CAAC,cAAc,GAAG,WAAW,CAAC,eAAe,CAAC;IAC/E,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC;IAClE,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC;IACjF,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,GAAG,QAAQ,CAAC,CAAC;YAEnE,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC,mBAAmB,GAAG,CAAC,CAAC;IAE1D,QAAA,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,QAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IAEvB,QAAA,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC;YAC3B,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAEtC,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IAEpB,QAAA,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,QAAA,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC;YAC3B,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;IAE7C,QAAA,IAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;IACjE,QAAA,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;IAChC,QAAA,IAAM,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;YAEvB,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,IAAI,GAAG,KAAK,CAAC;;YAGjB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAC7B;IACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAChC;oBACI,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAC9B;wBACI,IAAI,GAAG,IAAI,CAAC;wBACZ,MAAM;IACT,iBAAA;IACJ,aAAA;gBACD,IAAI,CAAC,IAAI,EACT;oBACI,GAAG,IAAI,IAAI,CAAC;IACf,aAAA;IAED,iBAAA;oBACI,MAAM;IACT,aAAA;IACJ,SAAA;IAED,QAAA,UAAU,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC;IAEjC,QAAA,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;YACpB,IAAI,GAAG,KAAK,CAAC;;YAGb,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAClC;IACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAChC;oBACI,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAC9B;wBACI,IAAI,GAAG,IAAI,CAAC;wBACZ,MAAM;IACT,iBAAA;IACJ,aAAA;gBAED,IAAI,CAAC,IAAI,EACT;oBACI,GAAG,IAAI,IAAI,CAAC;IACf,aAAA;IAED,iBAAA;oBACI,MAAM;IACT,aAAA;IACJ,SAAA;IAED,QAAA,UAAU,CAAC,OAAO,GAAG,CAAC,GAAG,QAAQ,CAAC;YAClC,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;IAE7D,QAAA,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;IAEtC,QAAA,OAAO,UAAU,CAAC;SACrB,CAAA;IAED;;;IAGG;QACW,WAAY,CAAA,YAAA,GAA1B,UAA2B,IAAS,EAAA;IAAT,QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAS,GAAA,EAAA,CAAA,EAAA;IAEhC,QAAA,IAAI,IAAI,EACR;IACI,YAAA,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,SAAA;IAED,aAAA;IACI,YAAA,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;IAC3B,SAAA;SACJ,CAAA;IAOD,IAAA,MAAA,CAAA,cAAA,CAAkB,WAAO,EAAA,SAAA,EAAA;IALzB;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EACzB;oBACI,IAAI,MAAM,SAAqC,CAAC;oBAEhD,IACA;;wBAEI,IAAM,CAAC,GAAG,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACpC,IAAM,OAAO,GAAG,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAEpD,oBAAA,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,EAClC;IACI,wBAAA,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC;IAEzB,wBAAA,OAAO,CAAC,CAAC;IACZ,qBAAA;IAED,oBAAA,MAAM,GAAGC,iBAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;IAC5C,iBAAA;IACD,gBAAA,OAAO,EAAE,EACT;IACI,oBAAA,MAAM,GAAGA,iBAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;IAC5C,iBAAA;oBACD,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;IAClC,gBAAA,WAAW,CAAC,QAAQ,GAAG,MAAM,CAAC;IACjC,aAAA;gBAED,OAAO,WAAW,CAAC,QAAQ,CAAC;aAC/B;;;IAAA,KAAA,CAAA,CAAA;IAMD,IAAA,MAAA,CAAA,cAAA,CAAkB,WAAQ,EAAA,UAAA,EAAA;IAJ1B;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAC1B;IACI,gBAAA,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IACjF,aAAA;gBAED,OAAO,WAAW,CAAC,SAAS,CAAC;aAChC;;;IAAA,KAAA,CAAA,CAAA;QACL,OAAC,WAAA,CAAA;IAAD,CAAC,EAAA,EAAA;IAED;;;;;;;;IAQG;IAEH;;;;;IAKG;IACH,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;IAExB;;;;;;;;IAQG;IACH,WAAW,CAAC,cAAc,GAAG,MAAM,CAAC;IAEpC;;;;;;;IAOG;IACH,WAAW,CAAC,eAAe,GAAG,GAAG,CAAC;IAElC;;;;;;;IAOG;IACH,WAAW,CAAC,mBAAmB,GAAG,GAAG,CAAC;IAEtC;;;;;;;IAOG;IACH,WAAW,CAAC,iBAAiB,GAAG,GAAG,CAAC;IAEpC;;;;;IAKG;IACH,WAAW,CAAC,SAAS,GAAG;QACpB,MAAM;IACN,IAAA,MAAM,EACT,CAAC;IAEF;;;;;IAKG;IACH,WAAW,CAAC,eAAe,GAAG;QAC1B,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACN,IAAA,MAAM,EACT,CAAC;IAEF;;;;;;;IAOG;;IC/0BH,IAAM,qBAAqB,GAAoB;IAC3C,IAAA,OAAO,EAAE,IAAI;IACb,IAAA,QAAQ,EAAE,KAAK;IACf,IAAA,WAAW,EAAE,IAAI;KACpB,CAAC;IAUF;;;;;;;;;;;;;;;;;;;;;IAqBG;AACH,QAAA,IAAA,kBAAA,UAAA,MAAA,EAAA;QAA0B,SAAM,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;IAiE5B;;;;IAIG;IACH,IAAA,SAAA,IAAA,CAAY,IAAsB,EAAE,KAAuC,EAAE,MAA0B,EAAA;YAAvG,IAsCC,KAAA,GAAA,IAAA,CAAA;YApCG,IAAI,SAAS,GAAG,KAAK,CAAC;YAEtB,IAAI,CAAC,MAAM,EACX;IACI,YAAA,MAAM,GAAGA,iBAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzC,SAAS,GAAG,IAAI,CAAC;IACpB,SAAA;IAED,QAAA,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;IACjB,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAElB,IAAM,OAAO,GAAGC,YAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAErC,QAAA,OAAO,CAAC,IAAI,GAAG,IAAIC,cAAS,EAAE,CAAC;IAC/B,QAAA,OAAO,CAAC,IAAI,GAAG,IAAIA,cAAS,EAAE,CAAC;YAE/B,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,IAAC,IAAA,CAAA;IAEf,QAAA,KAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC5B,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,KAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE;;IAEnC,YAAA,kBAAkB,EAAE,IAAI;IAC3B,SAAA,CAA6B,CAAC;IAE/B,QAAA,KAAI,CAAC,WAAW,GAAGF,iBAAQ,CAAC,UAAU,CAAC;IACvC,QAAA,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC5B,QAAA,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,QAAA,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC3B,QAAA,KAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAEhB,QAAA,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAEnB,QAAA,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;;SAC1B;IAED;;;;;;;IAOG;QACI,IAAU,CAAA,SAAA,CAAA,UAAA,GAAjB,UAAkB,YAAqB,EAAA;IAEnC,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;;IAG1B,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,OAAO,EACvC;IACI,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;IACrC,SAAA;IAED,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,YAAY,EAC/B;gBACI,OAAO;IACV,SAAA;YAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;IAExC,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7B,IAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5G,QAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC7B,QAAA,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC/B,QAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC7B,QAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;IACvC,QAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;IACvC,QAAA,IAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;IAC3C,QAAA,IAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;IAE/C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IACxG,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YAE1G,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAElD,QAAA,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAE/D,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;IAC1B,QAAA,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,eAAe,CAAC;IAC1C,QAAA,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;IAC1C,QAAA,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAClC,QAAA,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;IAEtC,QAAA,IAAI,aAAqB,CAAC;IAC1B,QAAA,IAAI,aAAqB,CAAC;;IAG1B,QAAA,IAAM,WAAW,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;YAa7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EACpC;gBACI,IAAM,YAAY,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC;;IAEjD,YAAA,IAAM,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7F,YAAA,IAAM,cAAc,GAAG,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;IAEvD,YAAA,IAAI,YAAY,EAChB;;;;IAII,gBAAA,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC;IAC5B,gBAAA,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;IAE9B,gBAAA,IAAM,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC;oBAC9C,IAAM,GAAG,GAAGG,aAAO,CAAC,OAAO,eAAe,KAAK,QAAQ,GAAG,eAAe,GAAGC,gBAAU,CAAC,eAAe,CAAC,CAAC,CAAC;oBACzG,IAAM,cAAc,GAAG,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;oBAC/D,IAAM,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC;IAEvE,gBAAA,OAAO,CAAC,WAAW,GAAG,OAAA,GAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,GAAA,GAAA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAA,GAAA,GAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,GAAA,GAAA,KAAK,CAAC,eAAe,MAAG,CAAC;IACvG,gBAAA,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC;IACpC,gBAAA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,kBAAkB,CAAC;IAC7E,gBAAA,OAAO,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,kBAAkB,IAAI,cAAc,CAAC;IACnG,aAAA;IAED,iBAAA;;IAEI,gBAAA,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;;;;IAIpE,gBAAA,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,MAAgB,CAAC;IAE7C,gBAAA,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;IAC9B,gBAAA,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;IACvB,gBAAA,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;IAC1B,gBAAA,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;IAC7B,aAAA;gBAED,IAAI,kBAAkB,GAAG,CAAC,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,CAAC,CAAC;IAEpE,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,GAAG,CAAC,EAC5E;oBACI,kBAAkB,GAAG,CAAC,CAAC;IAC1B,aAAA;;IAGD,YAAA,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,KAAK,CAAC,MAAM,EAAE,GAAC,EAAE,EACrC;IACI,gBAAA,aAAa,GAAG,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC;IAC1C,gBAAA,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,KAAK,GAAC,GAAG,UAAU,CAAC,IAAI,cAAc,CAAC,MAAM;IAClF,sBAAA,kBAAkB,CAAC;IAEzB,gBAAA,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,EAC3B;IACI,oBAAA,aAAa,IAAI,YAAY,GAAG,UAAU,CAAC,GAAC,CAAC,CAAC;IACjD,iBAAA;IACI,qBAAA,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,EACjC;wBACI,aAAa,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,GAAC,CAAC,IAAI,CAAC,CAAC;IACvD,iBAAA;IAED,gBAAA,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,EACzC;wBACI,IAAI,CAAC,iBAAiB,CAClB,KAAK,CAAC,GAAC,CAAC,EACR,aAAa,GAAG,KAAK,CAAC,OAAO,EAC7B,aAAa,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY,EAC5C,IAAI,CACP,CAAC;IACL,iBAAA;oBAED,IAAI,KAAK,CAAC,IAAI,EACd;wBACI,IAAI,CAAC,iBAAiB,CAClB,KAAK,CAAC,GAAC,CAAC,EACR,aAAa,GAAG,KAAK,CAAC,OAAO,EAC7B,aAAa,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY,CAC/C,CAAC;IACL,iBAAA;IACJ,aAAA;IACJ,SAAA;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB,CAAA;IAED;;;;;;;IAOG;QACK,IAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UAA0B,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,QAAgB,EAAA;IAAhB,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAgB,GAAA,KAAA,CAAA,EAAA;IAE1E,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;;IAG1B,QAAA,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;;;;;IAM1C,QAAA,IAAM,oBAAoB,GAAG,IAAI,CAAC,yBAAyB;IACpD,gBAAC,eAAe,IAAI,wBAAwB,CAAC,SAAS;IAClD,mBAAA,mBAAmB,IAAI,wBAAwB,CAAC,SAAS,CAAC,CAAC;IAEtE,QAAA,IAAI,aAAa,KAAK,CAAC,IAAI,oBAAoB,EAC/C;IACI,YAAA,IAAI,oBAAoB,EACxB;IACI,gBAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;IAC3C,gBAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,aAAa,CAAC;IAClD,aAAA;IAED,YAAA,IAAI,QAAQ,EACZ;oBACI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACvC,aAAA;IAED,iBAAA;oBACI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,aAAA;gBAED,OAAO;IACV,SAAA;YAED,IAAI,eAAe,GAAG,CAAC,CAAC;;;;;;;YAQxB,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACnE,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;YACzD,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAC3C;IACI,YAAA,IAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnC,YAAA,IAAI,QAAQ,EACZ;oBACI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAC5D,aAAA;IAED,iBAAA;oBACI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAC1D,aAAA;gBACD,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAC/C;IACI,gBAAA,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7B,aAAA;gBACD,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;IACvD,YAAA,eAAe,IAAI,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;gBAChE,aAAa,GAAG,YAAY,CAAC;IAChC,SAAA;SACJ,CAAA;;IAGO,IAAA,IAAA,CAAA,SAAA,CAAA,aAAa,GAArB,YAAA;IAEI,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EACpB;IACI,YAAA,IAAM,OAAO,GAAGC,gBAAU,CAAC,MAAM,CAAC,CAAC;gBAEnC,IAAI,OAAO,CAAC,IAAI,EAChB;IACI,gBAAA,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,gBAAA,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC/B,gBAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjD,aAAA;IACJ,SAAA;IAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC9B,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,QAAA,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;IAC/C,QAAA,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;IAExC,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;IAC5E,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;IAC/E,QAAA,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;IAC1B,QAAA,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;IAE1B,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;IAC1D,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;;YAG5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAExB,QAAA,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAEvE,OAAO,CAAC,SAAS,EAAE,CAAC;IAEpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACtB,CAAA;IAED;;;IAGG;QACO,IAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,QAAkB,EAAA;YAEhC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,EACpE;IACI,YAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;IACvC,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACrB,SAAA;IAED,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtB,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;SAC3B,CAAA;;IAGM,IAAA,IAAA,CAAA,SAAA,CAAA,eAAe,GAAtB,YAAA;IAEI,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEtB,MAAM,CAAA,SAAA,CAAA,eAAe,WAAE,CAAC;SAC3B,CAAA;IAEM,IAAA,IAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UAAiB,UAAoB,EAAE,IAAgB,EAAA;IAEnD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtB,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,EAC1B;;gBAEI,UAAU,GAAG,KAAK,CAAC;IACtB,SAAA;IAED,QAAA,OAAO,iBAAM,SAAS,CAAA,IAAA,CAAA,IAAA,EAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SAC5C,CAAA;IAED;;;;IAIG;QACI,IAAc,CAAA,SAAA,CAAA,cAAA,GAArB,UAAsB,IAAgB,EAAA;IAElC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEtB,OAAO,MAAA,CAAA,SAAA,CAAM,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAChD,CAAA;;IAGS,IAAA,IAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;YAEI,IAAI,CAAC,iBAAiB,EAAE,CAAC;;YAEzB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACzC,CAAA;IAED;;;;;;IAMG;IACK,IAAA,IAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,UACI,KAAgB,EAAE,KAAe,EAAE,OAAoB,EAAA;;;;IAMvD,QAAA,IAAM,SAAS,GAAuD,KAAK,CAAC,IAAW,CAAC;IAExF,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAC7B;IACI,YAAA,OAAO,SAAS,CAAC;IACpB,SAAA;IACI,aAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAC/B;IACI,YAAA,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;IACvB,SAAA;;;IAID,QAAA,IAAI,QAAmC,CAAC;;;IAIxC,QAAA,IAAM,oBAAoB,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC;;IAG/E,QAAA,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;YAEnC,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI,oBAAoB,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;YAC5F,IAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,IAAI,oBAAoB,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;;IAG9F,QAAA,IAAM,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;;IAG1D,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAC7B;IACI,YAAA,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EACpC;IACI,gBAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;IAC3C,aAAA;IACJ,SAAA;;;YAID,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,QAAA,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C,QAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAE1B,QAAA,IAAI,KAAK,CAAC,gBAAgB,KAAKP,qBAAa,CAAC,eAAe,EAC5D;;gBAEI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC;;;;gBAM9F,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,CAAC;IAE3E,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;IACI,gBAAA,IAAM,cAAc,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC;IACnE,gBAAA,IAAM,WAAW,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;oBAC3C,IAAI,qBAAqB,GAAG,WAAW,CAAC;;IAGxC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,cAAc,GAAG,WAAW,EACzC;wBACI,qBAAqB,GAAG,CAAC,WAAW,GAAG,cAAc,IAAI,CAAC,CAAC;IAC9D,iBAAA;IAED,gBAAA,IAAM,cAAc,GAAG,WAAW,GAAG,UAAU,CAAC;oBAChD,IAAM,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBACjD,IAAI,mBAAmB,GAAG,cAAc,CAAC;;oBAGzC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,WAAW,GAAG,cAAc,EACxD;wBACI,mBAAmB,GAAG,CAAC,cAAc,GAAG,WAAW,IAAI,CAAC,CAAC;IAC5D,iBAAA;;oBAGD,IAAM,kBAAkB,GAAG,CAAC,mBAAmB,GAAG,qBAAqB,IAAI,MAAM,CAAC;IAElF,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;;wBAEI,IAAI,QAAQ,GAAG,CAAC,CAAC;IAEjB,oBAAA,IAAI,OAAO,iBAAiB,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC5C;IACI,wBAAA,QAAQ,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACnC,qBAAA;IAED,yBAAA;IACI,wBAAA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IAC9B,qBAAA;wBAED,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EACnC,CAAC,qBAAqB,GAAG,MAAM,KAAK,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;;wBAGzE,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3C,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,iBAAA;IACJ,aAAA;IACJ,SAAA;IAED,aAAA;;gBAEI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAI/F,YAAA,IAAM,eAAe,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBACxC,IAAI,gBAAgB,GAAG,CAAC,CAAC;IAEzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;oBACI,IAAI,IAAI,SAAQ,CAAC;IAEjB,gBAAA,IAAI,OAAO,iBAAiB,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC5C;IACI,oBAAA,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC/B,iBAAA;IAED,qBAAA;IACI,oBAAA,IAAI,GAAG,gBAAgB,GAAG,eAAe,CAAC;IAC7C,iBAAA;oBACD,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,gBAAA,gBAAgB,EAAE,CAAC;IACtB,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,QAAQ,CAAC;SACnB,CAAA;IAED;;;;;;;;;;;IAWG;QACI,IAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;IAE9C,QAAA,IAAI,OAAO,OAAO,KAAK,SAAS,EAChC;IACI,YAAA,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;IACnC,SAAA;YAED,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAE5D,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;;;YAIvB,IAAI,IAAI,CAAC,UAAU,EACnB;IACI,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;IAC9C,SAAA;;IAGD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IAEnB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAK,CAAA,SAAA,EAAA,OAAA,EAAA;;IAAT,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtB,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;aAC5D;IAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;IAEnB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtB,YAAA,IAAM,CAAC,GAAGQ,UAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAElC,YAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;IACpD,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;aACvB;;;IAVA,KAAA,CAAA,CAAA;IAaD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAM,CAAA,SAAA,EAAA,QAAA,EAAA;;IAAV,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtB,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;aAC7D;IAED,QAAA,GAAA,EAAA,UAAW,KAAa,EAAA;IAEpB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtB,YAAA,IAAM,CAAC,GAAGA,UAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAElC,YAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IACrD,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;aACxB;;;IAVA,KAAA,CAAA,CAAA;IAiBD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAK,CAAA,SAAA,EAAA,OAAA,EAAA;IALT;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;;;;gBAKI,OAAO,IAAI,CAAC,MAAM,CAAC;aACtB;IAED,QAAA,GAAA,EAAA,UAAU,KAAsC,EAAA;IAE5C,YAAA,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;gBAEpB,IAAI,KAAK,YAAY,SAAS,EAC9B;IACI,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACvB,aAAA;IAED,iBAAA;oBACI,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;IACtC,aAAA;IAED,YAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IACvB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACrB;;;IAjBA,KAAA,CAAA,CAAA;IAoBD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAI,CAAA,SAAA,EAAA,MAAA,EAAA;;IAAR,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,KAAK,CAAC;aACrB;IAED,QAAA,GAAA,EAAA,UAAS,IAAqB,EAAA;IAE1B,YAAA,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAE/D,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EACvB;oBACI,OAAO;IACV,aAAA;IACD,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACrB;;;IAZA,KAAA,CAAA,CAAA;IAoBD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAU,CAAA,SAAA,EAAA,YAAA,EAAA;IANd;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IAED,QAAA,GAAA,EAAA,UAAe,KAAa,EAAA;IAExB,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAE7B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAC9B;oBACI,OAAO;IACV,aAAA;IAED,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IACzB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACrB;;;IAbA,KAAA,CAAA,CAAA;IA/sBD;;;;;IAKG;QACW,IAAsB,CAAA,sBAAA,GAAG,KAAK,CAAC;IAE7C;;;;IAIG;QACW,IAAyB,CAAA,yBAAA,GAAG,KAAK,CAAC;QAgtBpD,OAAC,IAAA,CAAA;KAAA,CA/tByBC,aAAM,CA+tB/B;;;;;;;;;;;;;;;;;"}