{"version": 3, "file": "constants.js", "sources": ["../../src/index.ts"], "sourcesContent": ["/**\n * Different types of environments for WebGL.\n * @static\n * @memberof PIXI\n * @name ENV\n * @enum {number}\n * @property {number} WEBGL_LEGACY - Used for older v1 WebGL devices. PixiJS will aim to ensure compatibility\n *  with older / less advanced devices. If you experience unexplained flickering prefer this environment.\n * @property {number} WEBGL - Version 1 of WebGL\n * @property {number} WEBGL2 - Version 2 of WebGL\n */\nexport enum ENV\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    WEBGL_LEGACY,\n    WEBGL,\n    WEBGL2,\n}\n\n/**\n * Constant to identify the Renderer Type.\n * @static\n * @memberof PIXI\n * @name RENDERER_TYPE\n * @enum {number}\n * @property {number} UNKNOWN - Unknown render type.\n * @property {number} WEBGL - WebGL render type.\n * @property {number} CANVAS - Canvas render type.\n */\nexport enum RENDERER_TYPE\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    UNKNOWN,\n    WEBGL,\n    CANVAS,\n}\n\n/**\n * Bitwise OR of masks that indicate the buffers to be cleared.\n * @static\n * @memberof PIXI\n * @name BUFFER_BITS\n * @enum {number}\n * @property {number} COLOR - Indicates the buffers currently enabled for color writing.\n * @property {number} DEPTH - Indicates the depth buffer.\n * @property {number} STENCIL - Indicates the stencil buffer.\n */\nexport enum BUFFER_BITS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    COLOR = 0x00004000,\n    DEPTH = 0x00000100,\n    STENCIL = 0x00000400\n}\n\n/**\n * Various blend modes supported by PIXI.\n *\n * IMPORTANT - The WebGL renderer only supports the NORMAL, ADD, MULTIPLY and SCREEN blend modes.\n * Anything else will silently act like NORMAL.\n * @memberof PIXI\n * @name BLEND_MODES\n * @enum {number}\n * @property {number} NORMAL -\n * @property {number} ADD -\n * @property {number} MULTIPLY -\n * @property {number} SCREEN -\n * @property {number} OVERLAY -\n * @property {number} DARKEN -\n * @property {number} LIGHTEN -\n * @property {number} COLOR_DODGE -\n * @property {number} COLOR_BURN -\n * @property {number} HARD_LIGHT -\n * @property {number} SOFT_LIGHT -\n * @property {number} DIFFERENCE -\n * @property {number} EXCLUSION -\n * @property {number} HUE -\n * @property {number} SATURATION -\n * @property {number} COLOR -\n * @property {number} LUMINOSITY -\n * @property {number} NORMAL_NPM -\n * @property {number} ADD_NPM -\n * @property {number} SCREEN_NPM -\n * @property {number} NONE -\n * @property {number} SRC_IN -\n * @property {number} SRC_OUT -\n * @property {number} SRC_ATOP -\n * @property {number} DST_OVER -\n * @property {number} DST_IN -\n * @property {number} DST_OUT -\n * @property {number} DST_ATOP -\n * @property {number} SUBTRACT -\n * @property {number} SRC_OVER -\n * @property {number} ERASE -\n * @property {number} XOR -\n */\nexport enum BLEND_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NORMAL = 0,\n    ADD = 1,\n    MULTIPLY = 2,\n    SCREEN = 3,\n    OVERLAY = 4,\n    DARKEN = 5,\n    LIGHTEN = 6,\n    COLOR_DODGE = 7,\n    COLOR_BURN = 8,\n    HARD_LIGHT = 9,\n    SOFT_LIGHT = 10,\n    DIFFERENCE = 11,\n    EXCLUSION = 12,\n    HUE = 13,\n    SATURATION = 14,\n    COLOR = 15,\n    LUMINOSITY = 16,\n    NORMAL_NPM = 17,\n    ADD_NPM = 18,\n    SCREEN_NPM = 19,\n    NONE = 20,\n\n    SRC_OVER = 0,\n    SRC_IN = 21,\n    SRC_OUT = 22,\n    SRC_ATOP = 23,\n    DST_OVER = 24,\n    DST_IN = 25,\n    DST_OUT = 26,\n    DST_ATOP = 27,\n    ERASE = 26,\n    SUBTRACT = 28,\n    XOR = 29,\n}\n\n/**\n * Various webgl draw modes. These can be used to specify which GL drawMode to use\n * under certain situations and renderers.\n * @memberof PIXI\n * @static\n * @name DRAW_MODES\n * @enum {number}\n * @property {number} POINTS -\n * @property {number} LINES -\n * @property {number} LINE_LOOP -\n * @property {number} LINE_STRIP -\n * @property {number} TRIANGLES -\n * @property {number} TRIANGLE_STRIP -\n * @property {number} TRIANGLE_FAN -\n */\nexport enum DRAW_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    POINTS,\n    LINES,\n    LINE_LOOP,\n    LINE_STRIP,\n    TRIANGLES,\n    TRIANGLE_STRIP,\n    TRIANGLE_FAN,\n}\n\n/**\n * Various GL texture/resources formats.\n * @memberof PIXI\n * @static\n * @name FORMATS\n * @enum {number}\n * @property {number} [RGBA=6408] -\n * @property {number} [RGB=6407] -\n * @property {number} [RG=33319] -\n * @property {number} [RED=6403] -\n * @property {number} [RGBA_INTEGER=36249] -\n * @property {number} [RGB_INTEGER=36248] -\n * @property {number} [RG_INTEGER=33320] -\n * @property {number} [RED_INTEGER=36244] -\n * @property {number} [ALPHA=6406] -\n * @property {number} [LUMINANCE=6409] -\n * @property {number} [LUMINANCE_ALPHA=6410] -\n * @property {number} [DEPTH_COMPONENT=6402] -\n * @property {number} [DEPTH_STENCIL=34041] -\n */\nexport enum FORMATS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    RGBA = 6408,\n    RGB = 6407,\n    RG = 33319,\n    RED = 6403,\n    RGBA_INTEGER = 36249,\n    RGB_INTEGER = 36248,\n    RG_INTEGER = 33320,\n    RED_INTEGER = 36244,\n    ALPHA = 6406,\n    LUMINANCE = 6409,\n    LUMINANCE_ALPHA = 6410,\n    DEPTH_COMPONENT = 6402,\n    DEPTH_STENCIL = 34041,\n}\n\n/**\n * Various GL target types.\n * @memberof PIXI\n * @static\n * @name TARGETS\n * @enum {number}\n * @property {number} [TEXTURE_2D=3553] -\n * @property {number} [TEXTURE_CUBE_MAP=34067] -\n * @property {number} [TEXTURE_2D_ARRAY=35866] -\n * @property {number} [TEXTURE_CUBE_MAP_POSITIVE_X=34069] -\n * @property {number} [TEXTURE_CUBE_MAP_NEGATIVE_X=34070] -\n * @property {number} [TEXTURE_CUBE_MAP_POSITIVE_Y=34071] -\n * @property {number} [TEXTURE_CUBE_MAP_NEGATIVE_Y=34072] -\n * @property {number} [TEXTURE_CUBE_MAP_POSITIVE_Z=34073] -\n * @property {number} [TEXTURE_CUBE_MAP_NEGATIVE_Z=34074] -\n */\nexport enum TARGETS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    TEXTURE_2D = 3553,\n    TEXTURE_CUBE_MAP = 34067,\n    TEXTURE_2D_ARRAY = 35866,\n    TEXTURE_CUBE_MAP_POSITIVE_X = 34069,\n    TEXTURE_CUBE_MAP_NEGATIVE_X = 34070,\n    TEXTURE_CUBE_MAP_POSITIVE_Y = 34071,\n    TEXTURE_CUBE_MAP_NEGATIVE_Y = 34072,\n    TEXTURE_CUBE_MAP_POSITIVE_Z = 34073,\n    TEXTURE_CUBE_MAP_NEGATIVE_Z = 34074,\n}\n\n/**\n * Various GL data format types.\n * @memberof PIXI\n * @static\n * @name TYPES\n * @enum {number}\n * @property {number} [UNSIGNED_BYTE=5121] -\n * @property {number} [UNSIGNED_SHORT=5123] -\n * @property {number} [UNSIGNED_SHORT_5_6_5=33635] -\n * @property {number} [UNSIGNED_SHORT_4_4_4_4=32819] -\n * @property {number} [UNSIGNED_SHORT_5_5_5_1=32820] -\n * @property {number} [UNSIGNED_INT=5125] -\n * @property {number} [UNSIGNED_INT_10F_11F_11F_REV=35899] -\n * @property {number} [UNSIGNED_INT_2_10_10_10_REV=33640] -\n * @property {number} [UNSIGNED_INT_24_8=34042] -\n * @property {number} [UNSIGNED_INT_5_9_9_9_REV=35902] -\n * @property {number} [BYTE=5120] -\n * @property {number} [SHORT=5122] -\n * @property {number} [INT=5124] -\n * @property {number} [FLOAT=5126] -\n * @property {number} [FLOAT_32_UNSIGNED_INT_24_8_REV=36269] -\n * @property {number} [HALF_FLOAT=36193] -\n */\nexport enum TYPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    UNSIGNED_BYTE = 5121,\n    UNSIGNED_SHORT = 5123,\n    UNSIGNED_SHORT_5_6_5 = 33635,\n    UNSIGNED_SHORT_4_4_4_4 = 32819,\n    UNSIGNED_SHORT_5_5_5_1 = 32820,\n    UNSIGNED_INT = 5125,\n    UNSIGNED_INT_10F_11F_11F_REV = 35899,\n    UNSIGNED_INT_2_10_10_10_REV = 33640,\n    UNSIGNED_INT_24_8 = 34042,\n    UNSIGNED_INT_5_9_9_9_REV = 35902,\n    BYTE = 5120,\n    SHORT = 5122,\n    INT = 5124,\n    FLOAT = 5126,\n    FLOAT_32_UNSIGNED_INT_24_8_REV = 36269,\n    HALF_FLOAT = 36193,\n}\n\n/**\n * Various sampler types. Correspond to `sampler`, `isampler`, `usampler` GLSL types respectively.\n * WebGL1 works only with FLOAT.\n * @memberof PIXI\n * @static\n * @name SAMPLER_TYPES\n * @enum {number}\n * @property {number} [FLOAT=0] -\n * @property {number} [INT=1] -\n * @property {number} [UINT=2] -\n */\nexport enum SAMPLER_TYPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    FLOAT = 0,\n    INT = 1,\n    UINT = 2,\n}\n\n/**\n * The scale modes that are supported by pixi.\n *\n * The {@link PIXI.settings.SCALE_MODE} scale mode affects the default scaling mode of future operations.\n * It can be re-assigned to either LINEAR or NEAREST, depending upon suitability.\n * @memberof PIXI\n * @static\n * @name SCALE_MODES\n * @enum {number}\n * @property {number} LINEAR Smooth scaling\n * @property {number} NEAREST Pixelating scaling\n */\nexport enum SCALE_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NEAREST,\n    LINEAR,\n}\n\n/**\n * The wrap modes that are supported by pixi.\n *\n * The {@link PIXI.settings.WRAP_MODE} wrap mode affects the default wrapping mode of future operations.\n * It can be re-assigned to either CLAMP or REPEAT, depending upon suitability.\n * If the texture is non power of two then clamp will be used regardless as WebGL can\n * only use REPEAT if the texture is po2.\n *\n * This property only affects WebGL.\n * @name WRAP_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} CLAMP - The textures uvs are clamped\n * @property {number} REPEAT - The texture uvs tile and repeat\n * @property {number} MIRRORED_REPEAT - The texture uvs tile and repeat with mirroring\n */\nexport enum WRAP_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    CLAMP = 33071,\n    REPEAT = 10497,\n    MIRRORED_REPEAT = 33648,\n}\n\n/**\n * Mipmap filtering modes that are supported by pixi.\n *\n * The {@link PIXI.settings.MIPMAP_TEXTURES} affects default texture filtering.\n * Mipmaps are generated for a baseTexture if its `mipmap` field is `ON`,\n * or its `POW2` and texture dimensions are powers of 2.\n * Due to platform restriction, `ON` option will work like `POW2` for webgl-1.\n *\n * This property only affects WebGL.\n * @name MIPMAP_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} OFF - No mipmaps\n * @property {number} POW2 - Generate mipmaps if texture dimensions are pow2\n * @property {number} ON - Always generate mipmaps\n * @property {number} ON_MANUAL - Use mipmaps, but do not auto-generate them; this is used with a resource\n *   that supports buffering each level-of-detail.\n */\nexport enum MIPMAP_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    OFF,\n    POW2,\n    ON,\n    ON_MANUAL\n}\n\n/**\n * How to treat textures with premultiplied alpha\n * @name ALPHA_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} NO_PREMULTIPLIED_ALPHA - Source is not premultiplied, leave it like that.\n *  Option for compressed and data textures that are created from typed arrays.\n * @property {number} PREMULTIPLY_ON_UPLOAD - Source is not premultiplied, premultiply on upload.\n *  Default option, used for all loaded images.\n * @property {number} PREMULTIPLIED_ALPHA - Source is already premultiplied\n *  Example: spine atlases with `_pma` suffix.\n * @property {number} NPM - Alias for NO_PREMULTIPLIED_ALPHA.\n * @property {number} UNPACK - Default option, alias for PREMULTIPLY_ON_UPLOAD.\n * @property {number} PMA - Alias for PREMULTIPLIED_ALPHA.\n */\nexport enum ALPHA_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NPM = 0,\n    UNPACK = 1,\n    PMA = 2,\n    NO_PREMULTIPLIED_ALPHA = 0,\n    PREMULTIPLY_ON_UPLOAD = 1,\n    PREMULTIPLY_ALPHA = 2, // deprecated, undocumented\n    PREMULTIPLIED_ALPHA = 2,\n}\n\n/**\n * Configure whether filter textures are cleared after binding.\n *\n * Filter textures need not be cleared if the filter does not use pixel blending. {@link CLEAR_MODES.BLIT} will detect\n * this and skip clearing as an optimization.\n * @name CLEAR_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} BLEND - Do not clear the filter texture. The filter's output will blend on top of the output texture.\n * @property {number} CLEAR - Always clear the filter texture.\n * @property {number} BLIT - Clear only if {@link FilterSystem.forceClear} is set or if the filter uses pixel blending.\n * @property {number} NO - Alias for BLEND, same as `false` in earlier versions\n * @property {number} YES - Alias for CLEAR, same as `true` in earlier versions\n * @property {number} AUTO - Alias for BLIT\n */\nexport enum CLEAR_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NO = 0,\n    YES = 1,\n    AUTO = 2,\n    BLEND = 0,\n    CLEAR = 1,\n    BLIT = 2,\n}\n\n/**\n * The gc modes that are supported by pixi.\n *\n * The {@link PIXI.settings.GC_MODE} Garbage Collection mode for PixiJS textures is AUTO\n * If set to GC_MODE, the renderer will occasionally check textures usage. If they are not\n * used for a specified period of time they will be removed from the GPU. They will of course\n * be uploaded again when they are required. This is a silent behind the scenes process that\n * should ensure that the GPU does not  get filled up.\n *\n * Handy for mobile devices!\n * This property only affects WebGL.\n * @name GC_MODES\n * @enum {number}\n * @static\n * @memberof PIXI\n * @property {number} AUTO - Garbage collection will happen periodically automatically\n * @property {number} MANUAL - Garbage collection will need to be called manually\n */\nexport enum GC_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    AUTO,\n    MANUAL,\n}\n\n/**\n * Constants that specify float precision in shaders.\n * @name PRECISION\n * @memberof PIXI\n * @constant\n * @static\n * @enum {string}\n * @property {string} [LOW='lowp'] -\n * @property {string} [MEDIUM='mediump'] -\n * @property {string} [HIGH='highp'] -\n */\nexport enum PRECISION\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    LOW = 'lowp',\n    MEDIUM = 'mediump',\n    HIGH = 'highp',\n}\n\n/**\n * Constants for mask implementations.\n * We use `type` suffix because it leads to very different behaviours\n * @name MASK_TYPES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} NONE - Mask is ignored\n * @property {number} SCISSOR - Scissor mask, rectangle on screen, cheap\n * @property {number} STENCIL - Stencil mask, 1-bit, medium, works only if renderer supports stencil\n * @property {number} SPRITE - Mask that uses SpriteMaskFilter, uses temporary RenderTexture\n * @property {number} COLOR - Color mask (RGBA)\n */\nexport enum MASK_TYPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NONE = 0,\n    SCISSOR = 1,\n    STENCIL = 2,\n    SPRITE = 3,\n    COLOR = 4,\n}\n\n/**\n * Bitwise OR of masks that indicate the color channels that are rendered to.\n * @static\n * @memberof PIXI\n * @name COLOR_MASK_BITS\n * @enum {number}\n * @property {number} RED - Red channel.\n * @property {number} GREEN - Green channel\n * @property {number} BLUE - Blue channel.\n * @property {number} ALPHA - Alpha channel.\n */\nexport enum COLOR_MASK_BITS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    RED = 0x1,\n    GREEN = 0x2,\n    BLUE = 0x4,\n    ALPHA = 0x8\n}\n\n/**\n * Constants for multi-sampling antialiasing.\n * @see PIXI.Framebuffer#multisample\n * @name MSAA_QUALITY\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} NONE - No multisampling for this renderTexture\n * @property {number} LOW - Try 2 samples\n * @property {number} MEDIUM - Try 4 samples\n * @property {number} HIGH - Try 8 samples\n */\nexport enum MSAA_QUALITY\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NONE = 0,\n    LOW = 2,\n    MEDIUM = 4,\n    HIGH = 8\n}\n\n/**\n * Constants for various buffer types in Pixi\n * @see PIXI.BUFFER_TYPE\n * @name BUFFER_TYPE\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} ELEMENT_ARRAY_BUFFER - buffer type for using as an index buffer\n * @property {number} ARRAY_BUFFER - buffer type for using attribute data\n * @property {number} UNIFORM_BUFFER - the buffer type is for uniform buffer objects\n */\nexport enum BUFFER_TYPE\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    ELEMENT_ARRAY_BUFFER = 34963,\n    ARRAY_BUFFER = 34962,\n    // NOT YET SUPPORTED\n    UNIFORM_BUFFER = 35345,\n}\n"], "names": ["ENV", "RENDERER_TYPE", "BUFFER_BITS", "BLEND_MODES", "DRAW_MODES", "FORMATS", "TARGETS", "TYPES", "SAMPLER_TYPES", "SCALE_MODES", "WRAP_MODES", "MIPMAP_MODES", "ALPHA_MODES", "CLEAR_MODES", "GC_MODES", "PRECISION", "MASK_TYPES", "COLOR_MASK_BITS", "MSAA_QUALITY", "BUFFER_TYPE"], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;AAUG;AACSA,qBAMX;AAND,CAAA,UAAY,GAAG,EAAA;AAGX,IAAA,GAAA,CAAA,GAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY,CAAA;AACZ,IAAA,GAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,GAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACV,CAAC,EANWA,WAAG,KAAHA,WAAG,GAMd,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;AASG;AACSC,+BAMX;AAND,CAAA,UAAY,aAAa,EAAA;AAGrB,IAAA,aAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,aAAA,CAAA,aAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACV,CAAC,EANWA,qBAAa,KAAbA,qBAAa,GAMxB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;AASG;AACSC,6BAMX;AAND,CAAA,UAAY,WAAW,EAAA;AAGnB,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAkB,CAAA;AAClB,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA,GAAA,OAAkB,CAAA;AAClB,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA,GAAA,SAAoB,CAAA;AACxB,CAAC,EANWA,mBAAW,KAAXA,mBAAW,GAMtB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCG;AACSC,6BAoCX;AApCD,CAAA,UAAY,WAAW,EAAA;AAGnB,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY,CAAA;AACZ,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;AACX,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;AACX,IAAA,WAAA,CAAA,WAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe,CAAA;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc,CAAA;AACd,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc,CAAA;AACd,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe,CAAA;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe,CAAA;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,WAAA,CAAA,GAAA,EAAA,CAAA,GAAA,WAAc,CAAA;AACd,IAAA,WAAA,CAAA,WAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAQ,CAAA;AACR,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe,CAAA;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAU,CAAA;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe,CAAA;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe,CAAA;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY,CAAA;AACZ,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe,CAAA;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAS,CAAA;AAET,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY,CAAA;AACZ,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAW,CAAA;AACX,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY,CAAA;AACZ,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa,CAAA;AACb,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa,CAAA;AACb,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAW,CAAA;AACX,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY,CAAA;AACZ,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa,CAAA;AACb,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAU,CAAA;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa,CAAA;AACb,IAAA,WAAA,CAAA,WAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAQ,CAAA;AACZ,CAAC,EApCWA,mBAAW,KAAXA,mBAAW,GAoCtB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;AAcG;AACSC,4BAUX;AAVD,CAAA,UAAY,UAAU,EAAA;AAGlB,IAAA,UAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,UAAA,CAAA,UAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,UAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,UAAA,CAAA,UAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU,CAAA;AACV,IAAA,UAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,UAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc,CAAA;AACd,IAAA,UAAA,CAAA,UAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY,CAAA;AAChB,CAAC,EAVWA,kBAAU,KAAVA,kBAAU,GAUrB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;;;AAmBG;AACSC,yBAgBX;AAhBD,CAAA,UAAY,OAAO,EAAA;AAGf,IAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAA,GAAA,MAAW,CAAA;AACX,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,IAAA,CAAA,GAAA,KAAU,CAAA;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAU,CAAA;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,IAAA,CAAA,GAAA,KAAU,CAAA;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,cAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAoB,CAAA;AACpB,IAAA,OAAA,CAAA,OAAA,CAAA,aAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAmB,CAAA;AACnB,IAAA,OAAA,CAAA,OAAA,CAAA,YAAA,CAAA,GAAA,KAAA,CAAA,GAAA,YAAkB,CAAA;AAClB,IAAA,OAAA,CAAA,OAAA,CAAA,aAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAmB,CAAA;AACnB,IAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,GAAA,OAAY,CAAA;AACZ,IAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,GAAA,WAAgB,CAAA;AAChB,IAAA,OAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,GAAA,iBAAsB,CAAA;AACtB,IAAA,OAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,GAAA,iBAAsB,CAAA;AACtB,IAAA,OAAA,CAAA,OAAA,CAAA,eAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAqB,CAAA;AACzB,CAAC,EAhBWA,eAAO,KAAPA,eAAO,GAgBlB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;AAeG;AACSC,yBAYX;AAZD,CAAA,UAAY,OAAO,EAAA;AAGf,IAAA,OAAA,CAAA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA,CAAA,GAAA,YAAiB,CAAA;AACjB,IAAA,OAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,kBAAwB,CAAA;AACxB,IAAA,OAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,kBAAwB,CAAA;AACxB,IAAA,OAAA,CAAA,OAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,6BAAmC,CAAA;AACnC,IAAA,OAAA,CAAA,OAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,6BAAmC,CAAA;AACnC,IAAA,OAAA,CAAA,OAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,6BAAmC,CAAA;AACnC,IAAA,OAAA,CAAA,OAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,6BAAmC,CAAA;AACnC,IAAA,OAAA,CAAA,OAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,6BAAmC,CAAA;AACnC,IAAA,OAAA,CAAA,OAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,6BAAmC,CAAA;AACvC,CAAC,EAZWA,eAAO,KAAPA,eAAO,GAYlB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACSC,uBAmBX;AAnBD,CAAA,UAAY,KAAK,EAAA;AAGb,IAAA,KAAA,CAAA,KAAA,CAAA,eAAA,CAAA,GAAA,IAAA,CAAA,GAAA,eAAoB,CAAA;AACpB,IAAA,KAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,IAAA,CAAA,GAAA,gBAAqB,CAAA;AACrB,IAAA,KAAA,CAAA,KAAA,CAAA,sBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAA4B,CAAA;AAC5B,IAAA,KAAA,CAAA,KAAA,CAAA,wBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,wBAA8B,CAAA;AAC9B,IAAA,KAAA,CAAA,KAAA,CAAA,wBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,wBAA8B,CAAA;AAC9B,IAAA,KAAA,CAAA,KAAA,CAAA,cAAA,CAAA,GAAA,IAAA,CAAA,GAAA,cAAmB,CAAA;AACnB,IAAA,KAAA,CAAA,KAAA,CAAA,8BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,8BAAoC,CAAA;AACpC,IAAA,KAAA,CAAA,KAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,6BAAmC,CAAA;AACnC,IAAA,KAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,mBAAyB,CAAA;AACzB,IAAA,KAAA,CAAA,KAAA,CAAA,0BAAA,CAAA,GAAA,KAAA,CAAA,GAAA,0BAAgC,CAAA;AAChC,IAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAA,GAAA,MAAW,CAAA;AACX,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,GAAA,OAAY,CAAA;AACZ,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,IAAA,CAAA,GAAA,KAAU,CAAA;AACV,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,GAAA,OAAY,CAAA;AACZ,IAAA,KAAA,CAAA,KAAA,CAAA,gCAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gCAAsC,CAAA;AACtC,IAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,KAAA,CAAA,GAAA,YAAkB,CAAA;AACtB,CAAC,EAnBWA,aAAK,KAALA,aAAK,GAmBhB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;AACSC,+BAMX;AAND,CAAA,UAAY,aAAa,EAAA;AAGrB,IAAA,aAAA,CAAA,aAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACT,IAAA,aAAA,CAAA,aAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACZ,CAAC,EANWA,qBAAa,KAAbA,qBAAa,GAMxB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;AAWG;AACSC,6BAKX;AALD,CAAA,UAAY,WAAW,EAAA;AAGnB,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACV,CAAC,EALWA,mBAAW,KAAXA,mBAAW,GAKtB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;AAgBG;AACSC,4BAMX;AAND,CAAA,UAAY,UAAU,EAAA;AAGlB,IAAA,UAAA,CAAA,UAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAa,CAAA;AACb,IAAA,UAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAc,CAAA;AACd,IAAA,UAAA,CAAA,UAAA,CAAA,iBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,iBAAuB,CAAA;AAC3B,CAAC,EANWA,kBAAU,KAAVA,kBAAU,GAMrB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;;AAkBG;AACSC,8BAOX;AAPD,CAAA,UAAY,YAAY,EAAA;AAGpB,IAAA,YAAA,CAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,YAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,YAAA,CAAA,YAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,YAAA,CAAA,YAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACb,CAAC,EAPWA,oBAAY,KAAZA,oBAAY,GAOvB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;AAeG;AACSC,6BAUX;AAVD,CAAA,UAAY,WAAW,EAAA;AAGnB,IAAA,WAAA,CAAA,WAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,wBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,wBAA0B,CAAA;AAC1B,IAAA,WAAA,CAAA,WAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,uBAAyB,CAAA;AACzB,IAAA,WAAA,CAAA,WAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,qBAAuB,CAAA;AAC3B,CAAC,EAVWA,mBAAW,KAAXA,mBAAW,GAUtB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;AAeG;AACSC,6BASX;AATD,CAAA,UAAY,WAAW,EAAA;AAGnB,IAAA,WAAA,CAAA,WAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,IAAM,CAAA;AACN,IAAA,WAAA,CAAA,WAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACT,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACT,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACZ,CAAC,EATWA,mBAAW,KAAXA,mBAAW,GAStB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;AAiBG;AACSC,0BAKX;AALD,CAAA,UAAY,QAAQ,EAAA;AAGhB,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACV,CAAC,EALWA,gBAAQ,KAARA,gBAAQ,GAKnB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;AACSC,2BAMX;AAND,CAAA,UAAY,SAAS,EAAA;AAGjB,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,MAAY,CAAA;AACZ,IAAA,SAAA,CAAA,QAAA,CAAA,GAAA,SAAkB,CAAA;AAClB,IAAA,SAAA,CAAA,MAAA,CAAA,GAAA,OAAc,CAAA;AAClB,CAAC,EANWA,iBAAS,KAATA,iBAAS,GAMpB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;AAYG;AACSC,4BAQX;AARD,CAAA,UAAY,UAAU,EAAA;AAGlB,IAAA,UAAA,CAAA,UAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,UAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;AACX,IAAA,UAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;AACX,IAAA,UAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,UAAA,CAAA,UAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACb,CAAC,EARWA,kBAAU,KAAVA,kBAAU,GAQrB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;AACSC,iCAOX;AAPD,CAAA,UAAY,eAAe,EAAA;AAGvB,IAAA,eAAA,CAAA,eAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAS,CAAA;AACT,IAAA,eAAA,CAAA,eAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAW,CAAA;AACX,IAAA,eAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAU,CAAA;AACV,IAAA,eAAA,CAAA,eAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAW,CAAA;AACf,CAAC,EAPWA,uBAAe,KAAfA,uBAAe,GAO1B,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;AAWG;AACSC,8BAOX;AAPD,CAAA,UAAY,YAAY,EAAA;AAGpB,IAAA,YAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,YAAA,CAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,YAAA,CAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,YAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACZ,CAAC,EAPWA,oBAAY,KAAZA,oBAAY,GAOvB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;AACSC,6BAOX;AAPD,CAAA,UAAY,WAAW,EAAA;AAGnB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAA4B,CAAA;AAC5B,IAAA,WAAA,CAAA,WAAA,CAAA,cAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAoB,CAAA;;AAEpB,IAAA,WAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAsB,CAAA;AAC1B,CAAC,EAPWA,mBAAW,KAAXA,mBAAW,GAOtB,EAAA,CAAA,CAAA;;"}