/**
 * 🌐 离线模式管理器
 * 当后端服务器不可用时，启用离线模式确保前端正常工作
 */

export class OfflineModeManager {
  private static instance: OfflineModeManager;
  private isOfflineMode = false;
  private offlineData = new Map<string, any>();

  private constructor() {}

  public static getInstance(): OfflineModeManager {
    if (!OfflineModeManager.instance) {
      OfflineModeManager.instance = new OfflineModeManager();
    }
    return OfflineModeManager.instance;
  }

  /**
   * 🚀 启用离线模式
   */
  public enableOfflineMode(): void {
    if (this.isOfflineMode) {
      console.log('⚠️ 离线模式已启用');
      return;
    }

    this.isOfflineMode = true;
    console.log('🌐 启用离线模式...');

    try {
      // 1. 设置storyStore离线状态
      this.setupOfflineStoryStore();

      // 2. 模拟WebSocket连接成功
      this.mockWebSocketConnection();

      // 3. 提供离线数据服务
      this.setupOfflineDataService();

      // 4. 隐藏连接状态指示器
      this.hideConnectionIndicators();

      // 5. 显示离线模式提示
      this.showOfflineModeNotification();

      console.log('✅ 离线模式启用成功');
    } catch (error) {
      console.error('❌ 离线模式启用失败:', error);
    }
  }

  /**
   * 📡 设置storyStore离线状态
   */
  private setupOfflineStoryStore(): void {
    try {
      // 查找storyStore实例
      let storyStore = null;
      
      // 尝试从Pinia获取
      if ((window as any).pinia) {
        const pinia = (window as any).pinia;
        if (pinia.state && pinia.state.value && pinia.state.value.story) {
          storyStore = pinia.state.value.story;
        }
      }

      // 尝试从全局获取
      if (!storyStore && (window as any).storyStore) {
        storyStore = (window as any).storyStore;
      }

      if (storyStore) {
        // 设置离线状态
        if (storyStore.isConnected !== undefined) {
          storyStore.isConnected = true; // 在离线模式下模拟连接成功
        }
        if (storyStore.connectionStatus !== undefined) {
          storyStore.connectionStatus = 'offline-ready'; // 自定义状态
        }
        if (storyStore.isConnecting !== undefined) {
          storyStore.isConnecting = false;
        }

        console.log('✅ storyStore离线状态设置完成');
      } else {
        console.warn('⚠️ 未找到storyStore实例');
      }
    } catch (error) {
      console.warn('⚠️ storyStore离线状态设置失败:', error);
    }
  }

  /**
   * 🔌 模拟WebSocket连接
   */
  private mockWebSocketConnection(): void {
    try {
      // 创建模拟的WebSocket管理器
      const mockWebSocketManager = {
        isConnected: true,
        connectionStatus: 'offline-mock',
        cleanupAllConnections: () => {
          console.log('🧹 [离线模式] 模拟连接清理');
        },
        cleanup: () => {
          console.log('🧹 [离线模式] 模拟清理');
        },
        resetAllConnections: () => {
          console.log('🔄 [离线模式] 模拟重置连接');
        },
        connect: () => {
          console.log('🔗 [离线模式] 模拟连接请求');
          return Promise.resolve();
        },
        disconnect: () => {
          console.log('🔌 [离线模式] 模拟断开连接');
        }
      };

      // 设置到全局
      (window as any).masterWebSocketManager = mockWebSocketManager;
      (window as any).cleanupAllConnections = mockWebSocketManager.cleanupAllConnections;

      console.log('✅ 模拟WebSocket连接设置完成');
    } catch (error) {
      console.warn('⚠️ 模拟WebSocket连接设置失败:', error);
    }
  }

  /**
   * 📁 设置离线数据服务
   */
  private setupOfflineDataService(): void {
    try {
      // 提供模拟的生成状态同步函数
      if (typeof (window as any).handleGenerationStateSync === 'undefined') {
        (window as any).handleGenerationStateSync = (event: CustomEvent) => {
          console.log('📡 [离线模式] 生成状态同步:', event.detail);
          // 在离线模式下，简单记录事件，不执行实际同步
          this.offlineData.set('lastGenerationState', event.detail);
        };
      }

      // 提供模拟的数据同步服务
      const mockDataSync = {
        syncStoryConfig: (config: any) => {
          console.log('💾 [离线模式] 故事配置同步:', config);
          this.offlineData.set('storyConfig', config);
          return Promise.resolve();
        },
        syncComicData: (data: any) => {
          console.log('🎨 [离线模式] 漫画数据同步:', data);
          this.offlineData.set('comicData', data);
          return Promise.resolve();
        }
      };

      (window as any).offlineDataSync = mockDataSync;
      console.log('✅ 离线数据服务设置完成');
    } catch (error) {
      console.warn('⚠️ 离线数据服务设置失败:', error);
    }
  }

  /**
   * 🙈 隐藏连接状态指示器
   */
  private hideConnectionIndicators(): void {
    try {
      const selectors = [
        '.connection-status',
        '.status-indicator.offline',
        '.websocket-status',
        '.connection-error'
      ];

      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          (el as HTMLElement).style.display = 'none';
        });
      });

      console.log('✅ 连接状态指示器已隐藏');
    } catch (error) {
      console.warn('⚠️ 隐藏连接状态指示器失败:', error);
    }
  }

  /**
   * 📢 显示离线模式提示
   */
  private showOfflineModeNotification(): void {
    try {
      // 创建离线模式提示
      const notification = document.createElement('div');
      notification.id = 'offline-mode-notification';
      notification.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: #e3f2fd;
        color: #1976d2;
        padding: 8px 16px;
        border: 1px solid #2196f3;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10000;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      `;
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
          <span>🌐</span>
          <span>离线模式 - 功能正常可用</span>
          <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: #1976d2; cursor: pointer; font-size: 16px;">&times;</button>
        </div>
      `;

      // 添加到页面
      document.body.appendChild(notification);

      // 3秒后自动隐藏
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 3000);

      console.log('✅ 离线模式提示已显示');
    } catch (error) {
      console.warn('⚠️ 离线模式提示显示失败:', error);
    }
  }

  /**
   * 🔍 检查是否需要启用离线模式
   */
  public async checkAndEnableOfflineMode(): Promise<boolean> {
    try {
      // 检查后端服务器是否可用
      const isServerAvailable = await this.checkServerAvailability();
      
      if (!isServerAvailable) {
        console.log('🌐 后端服务器不可用，启用离线模式');
        this.enableOfflineMode();
        return true;
      } else {
        console.log('✅ 后端服务器可用，使用在线模式');
        return false;
      }
    } catch (error) {
      console.warn('⚠️ 服务器检查失败，启用离线模式:', error);
      this.enableOfflineMode();
      return true;
    }
  }

  /**
   * 🔗 检查服务器可用性
   */
  private async checkServerAvailability(): Promise<boolean> {
    try {
      // 快速检查，不等待太久
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000); // 2秒超时

      const response = await fetch('http://localhost:3001/api/health', {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      // 网络错误或超时，认为服务器不可用
      return false;
    }
  }

  /**
   * 📊 获取离线模式状态
   */
  public getStatus(): {
    isOfflineMode: boolean;
    offlineDataCount: number;
    serverAvailable: boolean;
  } {
    return {
      isOfflineMode: this.isOfflineMode,
      offlineDataCount: this.offlineData.size,
      serverAvailable: !this.isOfflineMode
    };
  }

  /**
   * 🔄 尝试重新连接
   */
  public async attemptReconnection(): Promise<boolean> {
    try {
      console.log('🔄 尝试重新连接后端服务器...');
      
      const isAvailable = await this.checkServerAvailability();
      if (isAvailable) {
        console.log('✅ 服务器连接恢复');
        this.isOfflineMode = false;
        
        // 隐藏离线模式提示
        const notification = document.getElementById('offline-mode-notification');
        if (notification) {
          notification.remove();
        }
        
        // 重新加载页面以恢复正常模式
        window.location.reload();
        return true;
      } else {
        console.log('⚠️ 服务器仍不可用');
        return false;
      }
    } catch (error) {
      console.warn('⚠️ 重新连接失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const offlineModeManager = OfflineModeManager.getInstance();

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).offlineModeManager = offlineModeManager;
  (window as any).enableOfflineMode = () => offlineModeManager.enableOfflineMode();
  (window as any).checkServerStatus = () => offlineModeManager.checkAndEnableOfflineMode();
}