{"version": 3, "file": "graphics.mjs", "sources": ["../../src/const.ts", "../../src/styles/FillStyle.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/utils/buildPoly.ts", "../../src/utils/buildCircle.ts", "../../src/utils/buildRectangle.ts", "../../src/utils/buildRoundedRectangle.ts", "../../src/utils/buildLine.ts", "../../src/utils/ArcUtils.ts", "../../src/utils/BezierUtils.ts", "../../src/utils/QuadraticUtils.ts", "../../src/utils/BatchPart.ts", "../../src/utils/index.ts", "../../src/GraphicsData.ts", "../../src/GraphicsGeometry.ts", "../../src/styles/LineStyle.ts", "../../src/Graphics.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * Supported line joints in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @see https://graphicdesign.stackexchange.com/questions/59018/what-is-a-bevel-join-of-two-lines-exactly-illustrator\n * @name LINE_JOIN\n * @memberof PIXI\n * @static\n * @enum {string}\n * @property {string} MITER - 'miter': make a sharp corner where outer part of lines meet\n * @property {string} BEVEL - 'bevel': add a square butt at each end of line segment and fill the triangle at turn\n * @property {string} ROUND - 'round': add an arc at the joint\n */\nexport enum LINE_JOIN\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    MITER = 'miter',\n    BEVEL = 'bevel',\n    ROUND = 'round'\n}\n\n/**\n * Support line caps in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @name LINE_CAP\n * @memberof PIXI\n * @static\n * @enum {string}\n * @property {string} BUTT - 'butt': don't add any cap at line ends (leaves orthogonal edges)\n * @property {string} ROUND - 'round': add semicircle at ends\n * @property {string} SQUARE - 'square': add square at end (like `BUTT` except more length at end)\n */\nexport enum LINE_CAP\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    BUTT = 'butt',\n    ROUND = 'round',\n    SQUARE = 'square'\n}\n\nexport interface IGraphicsCurvesSettings\n{\n    adaptive: boolean;\n    maxLength: number;\n    minSegments: number;\n    maxSegments: number;\n\n    epsilon: number;\n\n    _segmentsCount(length: number, defaultSegments?: number): number;\n}\n\n/**\n * Graphics curves resolution settings. If `adaptive` flag is set to `true`,\n * the resolution is calculated based on the curve's length to ensure better visual quality.\n * Adaptive draw works with `bezierCurveTo` and `quadraticCurveTo`.\n * @static\n * @constant\n * @memberof PIXI\n * @name GRAPHICS_CURVES\n * @type {object}\n * @property {boolean} [adaptive=true] - flag indicating if the resolution should be adaptive\n * @property {number} [maxLength=10] - maximal length of a single segment of the curve (if adaptive = false, ignored)\n * @property {number} [minSegments=8] - minimal number of segments in the curve (if adaptive = false, ignored)\n * @property {number} [maxSegments=2048] - maximal number of segments in the curve (if adaptive = false, ignored)\n */\nexport const GRAPHICS_CURVES: IGraphicsCurvesSettings = {\n    adaptive: true,\n    maxLength: 10,\n    minSegments: 8,\n    maxSegments:  2048,\n\n    epsilon: 0.0001,\n\n    _segmentsCount(length: number, defaultSegments = 20)\n    {\n        if (!this.adaptive || !length || isNaN(length))\n        {\n            return defaultSegments;\n        }\n\n        let result = Math.ceil(length / this.maxLength);\n\n        if (result < this.minSegments)\n        {\n            result = this.minSegments;\n        }\n        else if (result > this.maxSegments)\n        {\n            result = this.maxSegments;\n        }\n\n        return result;\n    },\n};\n", "import { Texture } from '@pixi/core';\nimport type { Matrix } from '@pixi/math';\n\n/**\n * Fill style object for Graphics.\n * @memberof PIXI\n */\nexport class FillStyle\n{\n    /**\n     * The hex color value used when coloring the Graphics object.\n     * @default 0xFFFFFF\n     */\n    public color = 0xFFFFFF;\n\n    /** The alpha value used when filling the Graphics object. */\n    public alpha = 1.0;\n\n    /**\n     * The texture to be used for the fill.\n     * @default 0\n     */\n    public texture: Texture = Texture.WHITE;\n\n    /**\n     * The transform applied to the texture.\n     * @default null\n     */\n    public matrix: Matrix = null;\n\n    /** If the current fill is visible. */\n    public visible = false;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /** Clones the object */\n    public clone(): FillStyle\n    {\n        const obj = new FillStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n\n        return obj;\n    }\n\n    /** Reset */\n    public reset(): void\n    {\n        this.color = 0xFFFFFF;\n        this.alpha = 1;\n        this.texture = Texture.WHITE;\n        this.matrix = null;\n        this.visible = false;\n    }\n\n    /** Destroy and don't use after this. */\n    public destroy(): void\n    {\n        this.texture = null;\n        this.matrix = null;\n    }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { earcut } from '@pixi/utils';\n\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { Polygon } from '@pixi/math';\n\nfunction fixOrientation(points: number[], hole = false)\n{\n    const m = points.length;\n\n    if (m < 6)\n    {\n        return;\n    }\n\n    let area = 0;\n\n    for (let i = 0, x1 = points[m - 2], y1 = points[m - 1]; i < m; i += 2)\n    {\n        const x2 = points[i];\n        const y2 = points[i + 1];\n\n        area += (x2 - x1) * (y2 + y1);\n\n        x1 = x2;\n        y1 = y2;\n    }\n\n    if ((!hole && area > 0) || (hole && area <= 0))\n    {\n        const n = m / 2;\n\n        for (let i = n + (n % 2); i < m; i += 2)\n        {\n            const i1 = m - i - 2;\n            const i2 = m - i - 1;\n            const i3 = i;\n            const i4 = i + 1;\n\n            [points[i1], points[i3]] = [points[i3], points[i1]];\n            [points[i2], points[i4]] = [points[i4], points[i2]];\n        }\n    }\n}\n/**\n * Builds a polygon to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildPoly: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        graphicsData.points = (graphicsData.shape as Polygon).points.slice();\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        let points = graphicsData.points;\n        const holes = graphicsData.holes;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length >= 6)\n        {\n            fixOrientation(points, false);\n\n            const holeArray = [];\n            // Process holes..\n\n            for (let i = 0; i < holes.length; i++)\n            {\n                const hole = holes[i];\n\n                fixOrientation(hole.points, true);\n\n                holeArray.push(points.length / 2);\n                points = points.concat(hole.points);\n            }\n\n            // sort color\n            const triangles = earcut(points, holeArray, 2);\n\n            if (!triangles)\n            {\n                return;\n            }\n\n            const vertPos = verts.length / 2;\n\n            for (let i = 0; i < triangles.length; i += 3)\n            {\n                indices.push(triangles[i] + vertPos);\n                indices.push(triangles[i + 1] + vertPos);\n                indices.push(triangles[i + 2] + vertPos);\n            }\n\n            for (let i = 0; i < points.length; i++)\n            {\n                verts.push(points[i]);\n            }\n        }\n    },\n};\n", "// for type only\nimport { SHAPES } from '@pixi/math';\n\nimport type { Circle, Ellipse, RoundedRectangle } from '@pixi/math';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Builds a circle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object to draw\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildCircle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        // need to convert points to a nice regular data\n        const points = graphicsData.points;\n\n        let x;\n        let y;\n        let dx;\n        let dy;\n        let rx;\n        let ry;\n\n        if (graphicsData.type === SHAPES.CIRC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n            rx = ry = circle.radius;\n            dx = dy = 0;\n        }\n        else if (graphicsData.type === SHAPES.ELIP)\n        {\n            const ellipse = graphicsData.shape as Ellipse;\n\n            x = ellipse.x;\n            y = ellipse.y;\n            rx = ellipse.width;\n            ry = ellipse.height;\n            dx = dy = 0;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n            const halfWidth = roundedRect.width / 2;\n            const halfHeight = roundedRect.height / 2;\n\n            x = roundedRect.x + halfWidth;\n            y = roundedRect.y + halfHeight;\n            rx = ry = Math.max(0, Math.min(roundedRect.radius, Math.min(halfWidth, halfHeight)));\n            dx = halfWidth - rx;\n            dy = halfHeight - ry;\n        }\n\n        if (!(rx >= 0 && ry >= 0 && dx >= 0 && dy >= 0))\n        {\n            points.length = 0;\n\n            return;\n        }\n\n        // Choose a number of segments such that the maximum absolute deviation from the circle is approximately 0.029\n        const n = Math.ceil(2.3 * Math.sqrt(rx + ry));\n        const m = (n * 8) + (dx ? 4 : 0) + (dy ? 4 : 0);\n\n        points.length = m;\n\n        if (m === 0)\n        {\n            return;\n        }\n\n        if (n === 0)\n        {\n            points.length = 8;\n            points[0] = points[6] = x + dx;\n            points[1] = points[3] = y + dy;\n            points[2] = points[4] = x - dx;\n            points[5] = points[7] = y - dy;\n\n            return;\n        }\n\n        let j1 = 0;\n        let j2 = (n * 4) + (dx ? 2 : 0) + 2;\n        let j3 = j2;\n        let j4 = m;\n\n        {\n            const x0 = dx + rx;\n            const y0 = dy;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n\n            if (dy)\n            {\n                const y2 = y - y0;\n\n                points[j3++] = x2;\n                points[j3++] = y2;\n                points[--j4] = y2;\n                points[--j4] = x1;\n            }\n        }\n\n        for (let i = 1; i < n; i++)\n        {\n            const a = Math.PI / 2 * (i / n);\n            const x0 = dx + (Math.cos(a) * rx);\n            const y0 = dy + (Math.sin(a) * ry);\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n            points[j3++] = x2;\n            points[j3++] = y2;\n            points[--j4] = y2;\n            points[--j4] = x1;\n        }\n\n        {\n            const x0 = dx;\n            const y0 = dy + ry;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j4] = y2;\n            points[--j4] = x1;\n\n            if (dx)\n            {\n                points[j1++] = x2;\n                points[j1++] = y1;\n                points[--j4] = y2;\n                points[--j4] = x2;\n            }\n        }\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        const points = graphicsData.points;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length === 0)\n        {\n            return;\n        }\n\n        let vertPos = verts.length / 2;\n        const center = vertPos;\n\n        let x;\n        let y;\n\n        if (graphicsData.type !== SHAPES.RREC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n\n            x = roundedRect.x + (roundedRect.width / 2);\n            y = roundedRect.y + (roundedRect.height / 2);\n        }\n\n        const matrix = graphicsData.matrix;\n\n        // Push center (special point)\n        verts.push(\n            graphicsData.matrix ? (matrix.a * x) + (matrix.c * y) + matrix.tx : x,\n            graphicsData.matrix ? (matrix.b * x) + (matrix.d * y) + matrix.ty : y);\n\n        vertPos++;\n\n        verts.push(points[0], points[1]);\n\n        for (let i = 2; i < points.length; i += 2)\n        {\n            verts.push(points[i], points[i + 1]);\n\n            // add some uvs\n            indices.push(vertPos++, center, vertPos);\n        }\n\n        indices.push(center + 1, center, vertPos);\n    },\n};\n", "import type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { Rectangle } from '@pixi/math';\n\n/**\n * Builds a rectangle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildRectangle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        // --- //\n        // need to convert points to a nice regular data\n        //\n        const rectData = graphicsData.shape as Rectangle;\n        const x = rectData.x;\n        const y = rectData.y;\n        const width = rectData.width;\n        const height = rectData.height;\n\n        const points = graphicsData.points;\n\n        points.length = 0;\n\n        points.push(x, y,\n            x + width, y,\n            x + width, y + height,\n            x, y + height);\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        const points = graphicsData.points;\n        const verts = graphicsGeometry.points;\n\n        const vertPos = verts.length / 2;\n\n        verts.push(points[0], points[1],\n            points[2], points[3],\n            points[6], points[7],\n            points[4], points[5]);\n\n        graphicsGeometry.indices.push(vertPos, vertPos + 1, vertPos + 2,\n            vertPos + 1, vertPos + 2, vertPos + 3);\n    },\n};\n", "import { earcut } from '@pixi/utils';\n\n// for type only\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { RoundedRectangle } from '@pixi/math';\nimport { Graphics } from '../Graphics';\nimport { buildCircle } from './buildCircle';\n\n/**\n * Calculate a single point for a quadratic bezier curve.\n * Utility function used by quadraticBezierCurve.\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} n1 - first number\n * @param {number} n2 - second number\n * @param {number} perc - percentage\n * @returns {number} the result\n */\nfunction getPt(n1: number, n2: number, perc: number): number\n{\n    const diff = n2 - n1;\n\n    return n1 + (diff * perc);\n}\n\n/**\n * Calculate the points for a quadratic bezier curve. (helper function..)\n * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} fromX - Origin point x\n * @param {number} fromY - Origin point x\n * @param {number} cpX - Control point x\n * @param {number} cpY - Control point y\n * @param {number} toX - Destination point x\n * @param {number} toY - Destination point y\n * @param {number[]} [out=[]] - The output array to add points into. If not passed, a new array is created.\n * @returns {number[]} an array of points\n */\nfunction quadraticBezierCurve(\n    fromX: number, fromY: number,\n    cpX: number, cpY: number,\n    toX: number, toY: number,\n    out: Array<number> = []): Array<number>\n{\n    const n = 20;\n    const points = out;\n\n    let xa = 0;\n    let ya = 0;\n    let xb = 0;\n    let yb = 0;\n    let x = 0;\n    let y = 0;\n\n    for (let i = 0, j = 0; i <= n; ++i)\n    {\n        j = i / n;\n\n        // The Green Line\n        xa = getPt(fromX, cpX, j);\n        ya = getPt(fromY, cpY, j);\n        xb = getPt(cpX, toX, j);\n        yb = getPt(cpY, toY, j);\n\n        // The Black Dot\n        x = getPt(xa, xb, j);\n        y = getPt(ya, yb, j);\n\n        // Handle case when first curve points overlaps and earcut fails to triangulate\n        if (i === 0 && points[points.length - 2] === x && points[points.length - 1] === y)\n        {\n            continue;\n        }\n\n        points.push(x, y);\n    }\n\n    return points;\n}\n\n/**\n * Builds a rounded rectangle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildRoundedRectangle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        if (Graphics.nextRoundedRectBehavior)\n        {\n            buildCircle.build(graphicsData);\n\n            return;\n        }\n\n        const rrectData = graphicsData.shape as RoundedRectangle;\n        const points = graphicsData.points;\n        const x = rrectData.x;\n        const y = rrectData.y;\n        const width = rrectData.width;\n        const height = rrectData.height;\n\n        // Don't allow negative radius or greater than half the smallest width\n        const radius = Math.max(0, Math.min(rrectData.radius, Math.min(width, height) / 2));\n\n        points.length = 0;\n\n        // No radius, do a simple rectangle\n        if (!radius)\n        {\n            points.push(x, y,\n                x + width, y,\n                x + width, y + height,\n                x, y + height);\n        }\n        else\n        {\n            quadraticBezierCurve(x, y + radius,\n                x, y,\n                x + radius, y,\n                points);\n            quadraticBezierCurve(x + width - radius,\n                y, x + width, y,\n                x + width, y + radius,\n                points);\n            quadraticBezierCurve(x + width, y + height - radius,\n                x + width, y + height,\n                x + width - radius, y + height,\n                points);\n            quadraticBezierCurve(x + radius, y + height,\n                x, y + height,\n                x, y + height - radius,\n                points);\n        }\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        if (Graphics.nextRoundedRectBehavior)\n        {\n            buildCircle.triangulate(graphicsData, graphicsGeometry);\n\n            return;\n        }\n\n        const points = graphicsData.points;\n\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        const vecPos = verts.length / 2;\n\n        const triangles = earcut(points, null, 2);\n\n        for (let i = 0, j = triangles.length; i < j; i += 3)\n        {\n            indices.push(triangles[i] + vecPos);\n            //     indices.push(triangles[i] + vecPos);\n            indices.push(triangles[i + 1] + vecPos);\n            //   indices.push(triangles[i + 2] + vecPos);\n            indices.push(triangles[i + 2] + vecPos);\n        }\n\n        for (let i = 0, j = points.length; i < j; i++)\n        {\n            verts.push(points[i], points[++i]);\n        }\n    },\n};\n", "import { Point, SHAPES } from '@pixi/math';\n\nimport type { Polygon } from '@pixi/math';\nimport type { GraphicsData } from '../GraphicsData';\nimport type { GraphicsGeometry } from '../GraphicsGeometry';\nimport { LINE_JOIN, LINE_CAP, GRAPHICS_CURVES } from '../const';\n\n/**\n * Buffers vertices to draw a square cap.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} x - X-coord of end point\n * @param {number} y - Y-coord of end point\n * @param {number} nx - X-coord of line normal pointing inside\n * @param {number} ny - Y-coord of line normal pointing inside\n * @param {number} innerWeight - Weight of inner points\n * @param {number} outerWeight - Weight of outer points\n * @param {boolean} clockwise - Whether the cap is drawn clockwise\n * @param {Array<number>} verts - vertex buffer\n * @returns {number} - no. of vertices pushed\n */\nfunction square(\n    x: number,\n    y: number,\n    nx: number,\n    ny: number,\n    innerWeight: number,\n    outerWeight: number,\n    clockwise: boolean, /* rotation for square (true at left end, false at right end) */\n    verts: Array<number>\n): number\n{\n    const ix = x - (nx * innerWeight);\n    const iy = y - (ny * innerWeight);\n    const ox = x + (nx * outerWeight);\n    const oy = y + (ny * outerWeight);\n\n    /* Rotate nx,ny for extension vector */\n    let exx; let\n        eyy;\n\n    if (clockwise)\n    {\n        exx = ny;\n        eyy = -nx;\n    }\n    else\n    {\n        exx = -ny;\n        eyy = nx;\n    }\n\n    /* [i|0]x,y extended at cap */\n    const eix = ix + exx;\n    const eiy = iy + eyy;\n    const eox = ox + exx;\n    const eoy = oy + eyy;\n\n    /* Square itself must be inserted clockwise*/\n    verts.push(eix, eiy);\n    verts.push(eox, eoy);\n\n    return 2;\n}\n\n/**\n * Buffers vertices to draw an arc at the line joint or cap.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} cx - X-coord of center\n * @param {number} cy - Y-coord of center\n * @param {number} sx - X-coord of arc start\n * @param {number} sy - Y-coord of arc start\n * @param {number} ex - X-coord of arc end\n * @param {number} ey - Y-coord of arc end\n * @param {Array<number>} verts - buffer of vertices\n * @param {boolean} clockwise - orientation of vertices\n * @returns {number} - no. of vertices pushed\n */\nfunction round(\n    cx: number,\n    cy: number,\n    sx: number,\n    sy: number,\n    ex: number,\n    ey: number,\n    verts: Array<number>,\n    clockwise: boolean, /* if not cap, then clockwise is turn of joint, otherwise rotation from angle0 to angle1 */\n): number\n{\n    const cx2p0x = sx - cx;\n    const cy2p0y = sy - cy;\n\n    let angle0 = Math.atan2(cx2p0x, cy2p0y);\n    let angle1 = Math.atan2(ex - cx, ey - cy);\n\n    if (clockwise && angle0 < angle1)\n    {\n        angle0 += Math.PI * 2;\n    }\n    else if (!clockwise && angle0 > angle1)\n    {\n        angle1 += Math.PI * 2;\n    }\n\n    let startAngle = angle0;\n    const angleDiff = angle1 - angle0;\n    const absAngleDiff = Math.abs(angleDiff);\n\n    /* if (absAngleDiff >= PI_LBOUND && absAngleDiff <= PI_UBOUND)\n    {\n        const r1x = cx - nxtPx;\n        const r1y = cy - nxtPy;\n\n        if (r1x === 0)\n        {\n            if (r1y > 0)\n            {\n                angleDiff = -angleDiff;\n            }\n        }\n        else if (r1x >= -GRAPHICS_CURVES.epsilon)\n        {\n            angleDiff = -angleDiff;\n        }\n    }*/\n\n    const radius = Math.sqrt((cx2p0x * cx2p0x) + (cy2p0y * cy2p0y));\n    const segCount = ((15 * absAngleDiff * Math.sqrt(radius) / Math.PI) >> 0) + 1;\n    const angleInc = angleDiff / segCount;\n\n    startAngle += angleInc;\n\n    if (clockwise)\n    {\n        verts.push(cx, cy);\n        verts.push(sx, sy);\n\n        for (let i = 1, angle = startAngle; i < segCount; i++, angle += angleInc)\n        {\n            verts.push(cx, cy);\n            verts.push(cx + ((Math.sin(angle) * radius)),\n                cy + ((Math.cos(angle) * radius)));\n        }\n\n        verts.push(cx, cy);\n        verts.push(ex, ey);\n    }\n    else\n    {\n        verts.push(sx, sy);\n        verts.push(cx, cy);\n\n        for (let i = 1, angle = startAngle; i < segCount; i++, angle += angleInc)\n        {\n            verts.push(cx + ((Math.sin(angle) * radius)),\n                cy + ((Math.cos(angle) * radius)));\n            verts.push(cx, cy);\n        }\n\n        verts.push(ex, ey);\n        verts.push(cx, cy);\n    }\n\n    return segCount * 2;\n}\n\n/**\n * Builds a line to draw using the polygon method.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nfunction buildNonNativeLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    const shape = graphicsData.shape as Polygon;\n    let points = graphicsData.points || shape.points.slice();\n    const eps = graphicsGeometry.closePointEps;\n\n    if (points.length === 0)\n    {\n        return;\n    }\n    // if the line width is an odd number add 0.5 to align to a whole pixel\n    // commenting this out fixes #711 and #1620\n    // if (graphicsData.lineWidth%2)\n    // {\n    //     for (i = 0; i < points.length; i++)\n    //     {\n    //         points[i] += 0.5;\n    //     }\n    // }\n\n    const style = graphicsData.lineStyle;\n\n    // get first and last point.. figure out the middle!\n    const firstPoint = new Point(points[0], points[1]);\n    const lastPoint = new Point(points[points.length - 2], points[points.length - 1]);\n    const closedShape = shape.type !== SHAPES.POLY || shape.closeStroke;\n    const closedPath = Math.abs(firstPoint.x - lastPoint.x) < eps\n        && Math.abs(firstPoint.y - lastPoint.y) < eps;\n\n    // if the first point is the last point - gonna have issues :)\n    if (closedShape)\n    {\n        // need to clone as we are going to slightly modify the shape..\n        points = points.slice();\n\n        if (closedPath)\n        {\n            points.pop();\n            points.pop();\n            lastPoint.set(points[points.length - 2], points[points.length - 1]);\n        }\n\n        const midPointX = (firstPoint.x + lastPoint.x) * 0.5;\n        const midPointY = (lastPoint.y + firstPoint.y) * 0.5;\n\n        points.unshift(midPointX, midPointY);\n        points.push(midPointX, midPointY);\n    }\n\n    const verts = graphicsGeometry.points;\n    const length = points.length / 2;\n    let indexCount = points.length;\n    const indexStart = verts.length / 2;\n\n    // Max. inner and outer width\n    const width = style.width / 2;\n    const widthSquared = width * width;\n    const miterLimitSquared = style.miterLimit * style.miterLimit;\n\n    /* Line segments of interest where (x1,y1) forms the corner. */\n    let x0 = points[0];\n    let y0 = points[1];\n    let x1 = points[2];\n    let y1 = points[3];\n    let x2 = 0;\n    let y2 = 0;\n\n    /* perp[?](x|y) = the line normal with magnitude lineWidth. */\n    let perpx = -(y0 - y1);\n    let perpy = x0 - x1;\n    let perp1x = 0;\n    let perp1y = 0;\n\n    let dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n\n    perpx /= dist;\n    perpy /= dist;\n    perpx *= width;\n    perpy *= width;\n\n    const ratio = style.alignment;// 0.5;\n    const innerWeight = (1 - ratio) * 2;\n    const outerWeight = ratio * 2;\n\n    if (!closedShape)\n    {\n        if (style.cap === LINE_CAP.ROUND)\n        {\n            indexCount += round(\n                x0 - (perpx * (innerWeight - outerWeight) * 0.5),\n                y0 - (perpy * (innerWeight - outerWeight) * 0.5),\n                x0 - (perpx * innerWeight),\n                y0 - (perpy * innerWeight),\n                x0 + (perpx * outerWeight),\n                y0 + (perpy * outerWeight),\n                verts,\n                true,\n            ) + 2;\n        }\n        else if (style.cap === LINE_CAP.SQUARE)\n        {\n            indexCount += square(x0, y0, perpx, perpy, innerWeight, outerWeight, true, verts);\n        }\n    }\n\n    // Push first point (below & above vertices)\n    verts.push(\n        x0 - (perpx * innerWeight),\n        y0 - (perpy * innerWeight));\n    verts.push(\n        x0 + (perpx * outerWeight),\n        y0 + (perpy * outerWeight));\n\n    for (let i = 1; i < length - 1; ++i)\n    {\n        x0 = points[(i - 1) * 2];\n        y0 = points[((i - 1) * 2) + 1];\n\n        x1 = points[i * 2];\n        y1 = points[(i * 2) + 1];\n\n        x2 = points[(i + 1) * 2];\n        y2 = points[((i + 1) * 2) + 1];\n\n        perpx = -(y0 - y1);\n        perpy = x0 - x1;\n\n        dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n        perpx /= dist;\n        perpy /= dist;\n        perpx *= width;\n        perpy *= width;\n\n        perp1x = -(y1 - y2);\n        perp1y = x1 - x2;\n\n        dist = Math.sqrt((perp1x * perp1x) + (perp1y * perp1y));\n        perp1x /= dist;\n        perp1y /= dist;\n        perp1x *= width;\n        perp1y *= width;\n\n        /* d[x|y](0|1) = the component displacement between points p(0,1|1,2) */\n        const dx0 = x1 - x0;\n        const dy0 = y0 - y1;\n        const dx1 = x1 - x2;\n        const dy1 = y2 - y1;\n\n        /* +ve if internal angle < 90 degree, -ve if internal angle > 90 degree. */\n        const dot = (dx0 * dx1) + (dy0 * dy1);\n        /* +ve if internal angle counterclockwise, -ve if internal angle clockwise. */\n        const cross = (dy0 * dx1) - (dy1 * dx0);\n        const clockwise = (cross < 0);\n\n        /* Going nearly parallel? */\n        /* atan(0.001) ~= 0.001 rad ~= 0.057 degree */\n        if (Math.abs(cross) < 0.001 * Math.abs(dot))\n        {\n            verts.push(\n                x1 - (perpx * innerWeight),\n                y1 - (perpy * innerWeight));\n            verts.push(\n                x1 + (perpx * outerWeight),\n                y1 + (perpy * outerWeight));\n\n            /* 180 degree corner? */\n            if (dot >= 0)\n            {\n                if (style.join === LINE_JOIN.ROUND)\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false) + 4;\n                }\n                else\n                {\n                    indexCount += 2;\n                }\n\n                verts.push(\n                    x1 - (perp1x * outerWeight),\n                    y1 - (perp1y * outerWeight));\n                verts.push(\n                    x1 + (perp1x * innerWeight),\n                    y1 + (perp1y * innerWeight));\n            }\n\n            continue;\n        }\n\n        /* p[x|y] is the miter point. pdist is the distance between miter point and p1. */\n        const c1 = ((-perpx + x0) * (-perpy + y1)) - ((-perpx + x1) * (-perpy + y0));\n        const c2 = ((-perp1x + x2) * (-perp1y + y1)) - ((-perp1x + x1) * (-perp1y + y2));\n        const px = ((dx0 * c2) - (dx1 * c1)) / cross;\n        const py = ((dy1 * c1) - (dy0 * c2)) / cross;\n        const pdist = ((px - x1) * (px - x1)) + ((py - y1) * (py - y1));\n\n        /* Inner miter point */\n        const imx = x1 + ((px - x1) * innerWeight);\n        const imy = y1 + ((py - y1) * innerWeight);\n        /* Outer miter point */\n        const omx = x1 - ((px - x1) * outerWeight);\n        const omy = y1 - ((py - y1) * outerWeight);\n\n        /* Is the inside miter point too far away, creating a spike? */\n        const smallerInsideSegmentSq = Math.min((dx0 * dx0) + (dy0 * dy0), (dx1 * dx1) + (dy1 * dy1));\n        const insideWeight = clockwise ? innerWeight : outerWeight;\n        const smallerInsideDiagonalSq = smallerInsideSegmentSq + (insideWeight * insideWeight * widthSquared);\n        const insideMiterOk = pdist <= smallerInsideDiagonalSq;\n\n        if (insideMiterOk)\n        {\n            if (style.join === LINE_JOIN.BEVEL || pdist / widthSquared > miterLimitSquared)\n            {\n                if (clockwise) /* rotating at inner angle */\n                {\n                    verts.push(imx, imy); // inner miter point\n                    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight)); // first segment's outer vertex\n                    verts.push(imx, imy); // inner miter point\n                    verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight)); // second segment's outer vertex\n                }\n                else /* rotating at outer angle */\n                {\n                    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight)); // first segment's inner vertex\n                    verts.push(omx, omy); // outer miter point\n                    verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight)); // second segment's outer vertex\n                    verts.push(omx, omy); // outer miter point\n                }\n\n                indexCount += 2;\n            }\n            else if (style.join === LINE_JOIN.ROUND)\n            {\n                if (clockwise) /* arc is outside */\n                {\n                    verts.push(imx, imy);\n                    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight));\n\n                    indexCount += round(\n                        x1, y1,\n                        x1 + (perpx * outerWeight), y1 + (perpy * outerWeight),\n                        x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight),\n                        verts, true\n                    ) + 4;\n\n                    verts.push(imx, imy);\n                    verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight));\n                }\n                else /* arc is inside */\n                {\n                    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight));\n                    verts.push(omx, omy);\n\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false\n                    ) + 4;\n\n                    verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight));\n                    verts.push(omx, omy);\n                }\n            }\n            else\n            {\n                verts.push(imx, imy);\n                verts.push(omx, omy);\n            }\n        }\n        else // inside miter is NOT ok\n        {\n            verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight)); // first segment's inner vertex\n            verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight)); // first segment's outer vertex\n            if (style.join === LINE_JOIN.ROUND)\n            {\n                if (clockwise) /* arc is outside */\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 + (perpx * outerWeight), y1 + (perpy * outerWeight),\n                        x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight),\n                        verts, true\n                    ) + 2;\n                }\n                else /* arc is inside */\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false\n                    ) + 2;\n                }\n            }\n            else if (style.join === LINE_JOIN.MITER && pdist / widthSquared <= miterLimitSquared)\n            {\n                if (clockwise)\n                {\n                    verts.push(omx, omy); // inner miter point\n                    verts.push(omx, omy); // inner miter point\n                }\n                else\n                {\n                    verts.push(imx, imy); // outer miter point\n                    verts.push(imx, imy); // outer miter point\n                }\n                indexCount += 2;\n            }\n            verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight)); // second segment's inner vertex\n            verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight)); // second segment's outer vertex\n            indexCount += 2;\n        }\n    }\n\n    x0 = points[(length - 2) * 2];\n    y0 = points[((length - 2) * 2) + 1];\n\n    x1 = points[(length - 1) * 2];\n    y1 = points[((length - 1) * 2) + 1];\n\n    perpx = -(y0 - y1);\n    perpy = x0 - x1;\n\n    dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n    perpx /= dist;\n    perpy /= dist;\n    perpx *= width;\n    perpy *= width;\n\n    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight));\n    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight));\n\n    if (!closedShape)\n    {\n        if (style.cap === LINE_CAP.ROUND)\n        {\n            indexCount += round(\n                x1 - (perpx * (innerWeight - outerWeight) * 0.5),\n                y1 - (perpy * (innerWeight - outerWeight) * 0.5),\n                x1 - (perpx * innerWeight),\n                y1 - (perpy * innerWeight),\n                x1 + (perpx * outerWeight),\n                y1 + (perpy * outerWeight),\n                verts,\n                false\n            ) + 2;\n        }\n        else if (style.cap === LINE_CAP.SQUARE)\n        {\n            indexCount += square(x1, y1, perpx, perpy, innerWeight, outerWeight, false, verts);\n        }\n    }\n\n    const indices = graphicsGeometry.indices;\n    const eps2 = GRAPHICS_CURVES.epsilon * GRAPHICS_CURVES.epsilon;\n\n    // indices.push(indexStart);\n    for (let i = indexStart; i < indexCount + indexStart - 2; ++i)\n    {\n        x0 = verts[(i * 2)];\n        y0 = verts[(i * 2) + 1];\n\n        x1 = verts[(i + 1) * 2];\n        y1 = verts[((i + 1) * 2) + 1];\n\n        x2 = verts[(i + 2) * 2];\n        y2 = verts[((i + 2) * 2) + 1];\n\n        /* Skip zero area triangles */\n        if (Math.abs((x0 * (y1 - y2)) + (x1 * (y2 - y0)) + (x2 * (y0 - y1))) < eps2)\n        {\n            continue;\n        }\n\n        indices.push(i, i + 1, i + 2);\n    }\n}\n\n/**\n * Builds a line to draw using the gl.drawArrays(gl.LINES) method\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nfunction buildNativeLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    let i = 0;\n\n    const shape = graphicsData.shape as Polygon;\n    const points = graphicsData.points || shape.points;\n    const closedShape = shape.type !== SHAPES.POLY || shape.closeStroke;\n\n    if (points.length === 0) return;\n\n    const verts = graphicsGeometry.points;\n    const indices = graphicsGeometry.indices;\n    const length = points.length / 2;\n\n    const startIndex = verts.length / 2;\n    let currentIndex = startIndex;\n\n    verts.push(points[0], points[1]);\n\n    for (i = 1; i < length; i++)\n    {\n        verts.push(points[i * 2], points[(i * 2) + 1]);\n        indices.push(currentIndex, currentIndex + 1);\n\n        currentIndex++;\n    }\n\n    if (closedShape)\n    {\n        indices.push(currentIndex, startIndex);\n    }\n}\n\n/**\n * Builds a line to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nexport function buildLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    if (graphicsData.lineStyle.native)\n    {\n        buildNativeLine(graphicsData, graphicsGeometry);\n    }\n    else\n    {\n        buildNonNativeLine(graphicsData, graphicsGeometry);\n    }\n}\n", "import { GRAPHICS_CURVES } from '../const';\nimport { PI_2 } from '@pixi/math';\n\ninterface IArcLikeShape\n{\n    cx: number;\n    cy: number;\n    radius: number;\n    startAngle: number;\n    endAngle: number;\n    anticlockwise: boolean;\n}\n\n/**\n * Utilities for arc curves.\n * @private\n */\nexport class ArcUtils\n{\n    /**\n     * The arcTo() method creates an arc/curve between two tangents on the canvas.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @private\n     * @param x1 - The x-coordinate of the beginning of the arc\n     * @param y1 - The y-coordinate of the beginning of the arc\n     * @param x2 - The x-coordinate of the end of the arc\n     * @param y2 - The y-coordinate of the end of the arc\n     * @param radius - The radius of the arc\n     * @param points -\n     * @returns - If the arc length is valid, return center of circle, radius and other info otherwise `null`.\n     */\n    static curveTo(x1: number, y1: number, x2: number, y2: number, radius: number, points: Array<number>): IArcLikeShape\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        const a1 = fromY - y1;\n        const b1 = fromX - x1;\n        const a2 = y2 - y1;\n        const b2 = x2 - x1;\n        const mm = Math.abs((a1 * b2) - (b1 * a2));\n\n        if (mm < 1.0e-8 || radius === 0)\n        {\n            if (points[points.length - 2] !== x1 || points[points.length - 1] !== y1)\n            {\n                points.push(x1, y1);\n            }\n\n            return null;\n        }\n\n        const dd = (a1 * a1) + (b1 * b1);\n        const cc = (a2 * a2) + (b2 * b2);\n        const tt = (a1 * a2) + (b1 * b2);\n        const k1 = radius * Math.sqrt(dd) / mm;\n        const k2 = radius * Math.sqrt(cc) / mm;\n        const j1 = k1 * tt / dd;\n        const j2 = k2 * tt / cc;\n        const cx = (k1 * b2) + (k2 * b1);\n        const cy = (k1 * a2) + (k2 * a1);\n        const px = b1 * (k2 + j1);\n        const py = a1 * (k2 + j1);\n        const qx = b2 * (k1 + j2);\n        const qy = a2 * (k1 + j2);\n        const startAngle = Math.atan2(py - cy, px - cx);\n        const endAngle = Math.atan2(qy - cy, qx - cx);\n\n        return {\n            cx: (cx + x1),\n            cy: (cy + y1),\n            radius,\n            startAngle,\n            endAngle,\n            anticlockwise: (b1 * a2 > b2 * a1),\n        };\n    }\n\n    /* eslint-disable max-len */\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @private\n     * @param _startX - Start x location of arc\n     * @param _startY - Start y location of arc\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param _anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @param points - Collection of points to add to\n     */\n    static arc(_startX: number, _startY: number, cx: number, cy: number, radius: number,\n        startAngle: number, endAngle: number, _anticlockwise: boolean, points: Array<number>): void\n    {\n        const sweep = endAngle - startAngle;\n        const n = GRAPHICS_CURVES._segmentsCount(\n            Math.abs(sweep) * radius,\n            Math.ceil(Math.abs(sweep) / PI_2) * 40\n        );\n\n        const theta = (sweep) / (n * 2);\n        const theta2 = theta * 2;\n        const cTheta = Math.cos(theta);\n        const sTheta = Math.sin(theta);\n        const segMinus = n - 1;\n        const remainder = (segMinus % 1) / segMinus;\n\n        for (let i = 0; i <= segMinus; ++i)\n        {\n            const real = i + (remainder * i);\n            const angle = ((theta) + startAngle + (theta2 * real));\n            const c = Math.cos(angle);\n            const s = -Math.sin(angle);\n\n            points.push(\n                (((cTheta * c) + (sTheta * s)) * radius) + cx,\n                (((cTheta * -s) + (sTheta * c)) * radius) + cy\n            );\n        }\n    }\n    /* eslint-enable max-len */\n}\n", "import { GRAPHICS_CURVES } from '../const';\n\n/**\n * Utilities for bezier curves\n * @private\n */\nexport class BezierUtils\n{\n    /**\n     * Calculate length of bezier curve.\n     * Analytical solution is impossible, since it involves an integral that does not integrate in general.\n     * Therefore numerical solution is used.\n     * @private\n     * @param fromX - Starting point x\n     * @param fromY - Starting point y\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - Length of bezier curve\n     */\n    static curveLength(\n        fromX: number, fromY: number,\n        cpX: number, cpY: number,\n        cpX2: number, cpY2: number,\n        toX: number, toY: number): number\n    {\n        const n = 10;\n        let result = 0.0;\n        let t = 0.0;\n        let t2 = 0.0;\n        let t3 = 0.0;\n        let nt = 0.0;\n        let nt2 = 0.0;\n        let nt3 = 0.0;\n        let x = 0.0;\n        let y = 0.0;\n        let dx = 0.0;\n        let dy = 0.0;\n        let prevX = fromX;\n        let prevY = fromY;\n\n        for (let i = 1; i <= n; ++i)\n        {\n            t = i / n;\n            t2 = t * t;\n            t3 = t2 * t;\n            nt = (1.0 - t);\n            nt2 = nt * nt;\n            nt3 = nt2 * nt;\n\n            x = (nt3 * fromX) + (3.0 * nt2 * t * cpX) + (3.0 * nt * t2 * cpX2) + (t3 * toX);\n            y = (nt3 * fromY) + (3.0 * nt2 * t * cpY) + (3 * nt * t2 * cpY2) + (t3 * toY);\n            dx = prevX - x;\n            dy = prevY - y;\n            prevX = x;\n            prevY = y;\n\n            result += Math.sqrt((dx * dx) + (dy * dy));\n        }\n\n        return result;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     *\n     * Ignored from docs since it is not directly exposed.\n     * @ignore\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @param points - Path array to push points into\n     */\n    static curveTo(\n        cpX: number, cpY: number,\n        cpX2: number, cpY2: number,\n        toX: number, toY: number,\n        points: Array<number>): void\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        points.length -= 2;\n\n        const n = GRAPHICS_CURVES._segmentsCount(\n            BezierUtils.curveLength(fromX, fromY, cpX, cpY, cpX2, cpY2, toX, toY)\n        );\n\n        let dt = 0;\n        let dt2 = 0;\n        let dt3 = 0;\n        let t2 = 0;\n        let t3 = 0;\n\n        points.push(fromX, fromY);\n\n        for (let i = 1, j = 0; i <= n; ++i)\n        {\n            j = i / n;\n\n            dt = (1 - j);\n            dt2 = dt * dt;\n            dt3 = dt2 * dt;\n\n            t2 = j * j;\n            t3 = t2 * j;\n\n            points.push(\n                (dt3 * fromX) + (3 * dt2 * j * cpX) + (3 * dt * t2 * cpX2) + (t3 * toX),\n                (dt3 * fromY) + (3 * dt2 * j * cpY) + (3 * dt * t2 * cpY2) + (t3 * toY)\n            );\n        }\n    }\n}\n", "import { GRAPHICS_CURVES } from '../const';\n\n/**\n * Utilities for quadratic curves.\n * @private\n */\nexport class QuadraticUtils\n{\n    /**\n     * Calculate length of quadratic curve\n     * @see {@link http://www.malczak.linuxpl.com/blog/quadratic-bezier-curve-length/}\n     * for the detailed explanation of math behind this.\n     * @private\n     * @param fromX - x-coordinate of curve start point\n     * @param fromY - y-coordinate of curve start point\n     * @param cpX - x-coordinate of curve control point\n     * @param cpY - y-coordinate of curve control point\n     * @param toX - x-coordinate of curve end point\n     * @param toY - y-coordinate of curve end point\n     * @returns - Length of quadratic curve\n     */\n    static curveLength(\n        fromX: number, fromY: number,\n        cpX: number, cpY: number,\n        toX: number, toY: number): number\n    {\n        const ax = fromX - (2.0 * cpX) + toX;\n        const ay = fromY - (2.0 * cpY) + toY;\n        const bx = (2.0 * cpX) - (2.0 * fromX);\n        const by = (2.0 * cpY) - (2.0 * fromY);\n        const a = 4.0 * ((ax * ax) + (ay * ay));\n        const b = 4.0 * ((ax * bx) + (ay * by));\n        const c = (bx * bx) + (by * by);\n\n        const s = 2.0 * Math.sqrt(a + b + c);\n        const a2 = Math.sqrt(a);\n        const a32 = 2.0 * a * a2;\n        const c2 = 2.0 * Math.sqrt(c);\n        const ba = b / a2;\n\n        return (\n            (a32 * s)\n                + (a2 * b * (s - c2))\n                + (\n                    ((4.0 * c * a) - (b * b))\n                   * Math.log(((2.0 * a2) + ba + s) / (ba + c2))\n                )\n        ) / (4.0 * a32);\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @private\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @param points - Points to add segments to.\n     */\n    static curveTo(cpX: number, cpY: number, toX: number, toY: number, points: Array<number>): void\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        const n = GRAPHICS_CURVES._segmentsCount(\n            QuadraticUtils.curveLength(fromX, fromY, cpX, cpY, toX, toY)\n        );\n\n        let xa = 0;\n        let ya = 0;\n\n        for (let i = 1; i <= n; ++i)\n        {\n            const j = i / n;\n\n            xa = fromX + ((cpX - fromX) * j);\n            ya = fromY + ((cpY - fromY) * j);\n\n            points.push(xa + (((cpX + ((toX - cpX) * j)) - xa) * j),\n                ya + (((cpY + ((toY - cpY) * j)) - ya) * j));\n        }\n    }\n}\n", "import type { LineStyle } from '../styles/LineStyle';\nimport type { FillStyle } from '../styles/FillStyle';\n\n/**\n * A structure to hold interim batch objects for Graphics.\n * @memberof PIXI.graphicsUtils\n */\nexport class BatchPart\n{\n    public style: LineStyle | FillStyle;\n    public start: number;\n    public size: number;\n    public attribStart: number;\n    public attribSize: number;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /**\n     * Begin batch part.\n     * @param style\n     * @param startIndex\n     * @param attribStart\n     */\n    public begin(style: LineStyle | FillStyle, startIndex: number, attribStart: number): void\n    {\n        this.reset();\n        this.style = style;\n        this.start = startIndex;\n        this.attribStart = attribStart;\n    }\n\n    /**\n     * End batch part.\n     * @param endIndex\n     * @param endAttrib\n     */\n    public end(endIndex: number, endAttrib: number): void\n    {\n        this.attribSize = endAttrib - this.attribStart;\n        this.size = endIndex - this.start;\n    }\n\n    public reset(): void\n    {\n        this.style = null;\n        this.size = 0;\n        this.start = 0;\n        this.attribStart = 0;\n        this.attribSize = 0;\n    }\n}\n", "/**\n * Generalized convenience utilities for Graphics.\n * @namespace graphicsUtils\n * @memberof PIXI\n */\n\nimport { buildPoly } from './buildPoly';\nexport { buildPoly };\n\nimport { buildCircle } from './buildCircle';\nexport { buildCircle };\n\nimport { buildRectangle } from './buildRectangle';\nexport { buildRectangle };\n\nimport { buildRoundedRectangle } from './buildRoundedRectangle';\nexport { buildRoundedRectangle };\n\nexport * from './buildLine';\nexport * from './ArcUtils';\nexport * from './BezierUtils';\nexport * from './QuadraticUtils';\nexport * from './BatchPart';\n\n// for type only\nimport type { BatchPart } from './BatchPart';\nimport { SHAPES } from '@pixi/math';\nimport type { BatchDrawCall } from '@pixi/core';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Map of fill commands for each shape type.\n * @memberof PIXI.graphicsUtils\n * @member {object} FILL_COMMANDS\n */\nexport const FILL_COMMANDS: Record<SHAPES, IShapeBuildCommand> = {\n    [SHAPES.POLY]: buildPoly,\n    [SHAPES.CIRC]: buildCircle,\n    [SHAPES.ELIP]: buildCircle,\n    [SHAPES.RECT]: buildRectangle,\n    [SHAPES.RREC]: buildRoundedRectangle,\n};\n\n/**\n * Batch pool, stores unused batches for preventing allocations.\n * @memberof PIXI.graphicsUtils\n * @member {Array<PIXI.graphicsUtils.BatchPart>} BATCH_POOL\n */\nexport const BATCH_POOL: Array<BatchPart> = [];\n\n/**\n * Draw call pool, stores unused draw calls for preventing allocations.\n * @memberof PIXI.graphicsUtils\n * @member {Array<PIXI.BatchDrawCall>} DRAW_CALL_POOL\n */\nexport const DRAW_CALL_POOL: Array<BatchDrawCall> = [];\n", "import type { Matrix, SHAPES, IShape } from '@pixi/math';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/**\n * A class to contain data useful for Graphics objects\n * @memberof PIXI\n */\nexport class GraphicsData\n{\n    /**\n     * The shape object to draw.\n     * @member {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle}\n     */\n    shape: IShape;\n\n    /** The style of the line. */\n    lineStyle: LineStyle;\n\n    /** The style of the fill. */\n    fillStyle: FillStyle;\n\n    /** The transform matrix. */\n    matrix: Matrix;\n\n    /** The type of the shape, see the Const.Shapes file for all the existing types, */\n    type: SHAPES;\n\n    /** The collection of points. */\n    points: number[] = [];\n\n    /** The collection of holes. */\n\n    holes: Array<GraphicsData> = [];\n\n    /**\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - the width of the line to draw\n     * @param lineStyle - the color of the line to draw\n     * @param matrix - Transform matrix\n     */\n    constructor(shape: IShape, fillStyle: FillStyle = null, lineStyle: LineStyle = null, matrix: Matrix = null)\n    {\n        this.shape = shape;\n        this.lineStyle = lineStyle;\n        this.fillStyle = fillStyle;\n        this.matrix = matrix;\n        this.type = shape.type;\n    }\n\n    /**\n     * Creates a new GraphicsData object with the same values as this one.\n     * @returns - Cloned GraphicsData object\n     */\n    public clone(): GraphicsData\n    {\n        return new GraphicsData(\n            this.shape,\n            this.fillStyle,\n            this.lineStyle,\n            this.matrix\n        );\n    }\n\n    /** Destroys the Graphics data. */\n    public destroy(): void\n    {\n        this.shape = null;\n        this.holes.length = 0;\n        this.holes = null;\n        this.points.length = 0;\n        this.points = null;\n        this.lineStyle = null;\n        this.fillStyle = null;\n    }\n}\n", "import {\n    buildLine,\n    buildP<PERSON>,\n    <PERSON>ch<PERSON><PERSON>,\n    FILL_COMMANDS,\n    BATCH_POOL,\n    DRAW_CALL_POOL,\n} from './utils';\n\nimport type {\n    Texture } from '@pixi/core';\nimport {\n    BatchGeometry,\n    BatchDrawCall,\n    BatchTextureArray,\n    BaseTexture\n} from '@pixi/core';\n\nimport { DRAW_MODES, WRAP_MODES } from '@pixi/constants';\nimport { Point } from '@pixi/math';\nimport { GraphicsData } from './GraphicsData';\nimport { premultiplyTint } from '@pixi/utils';\nimport { Bounds } from '@pixi/display';\n\nimport type { Circle, Ellipse, Polygon, Rectangle, RoundedRectangle, IPointData, Matrix } from '@pixi/math';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/*\n * Complex shape type\n * @todo Move to Math shapes\n */\ntype IShape = Circle | Ellipse | Polygon | Rectangle | RoundedRectangle;\n\nconst tmpPoint = new Point();\n\n/**\n * The Graphics class contains methods used to draw primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.\n *\n * GraphicsGeometry is designed to not be continually updating the geometry since it's expensive\n * to re-tesselate using **earcut**. Consider using {@link PIXI.Mesh} for this use-case, it's much faster.\n * @memberof PIXI\n */\nexport class GraphicsGeometry extends BatchGeometry\n{\n    /**\n     * The maximum number of points to consider an object \"batchable\",\n     * able to be batched by the renderer's batch system.\n\\\n     */\n    public static BATCHABLE_SIZE = 100;\n\n    /** Minimal distance between points that are considered different. Affects line tesselation. */\n    public closePointEps = 1e-4;\n\n    /** Padding to add to the bounds. */\n    public boundsPadding = 0;\n\n    uvsFloat32: Float32Array = null;\n    indicesUint16: Uint16Array | Uint32Array = null;\n    batchable = false;\n\n    /** An array of points to draw, 2 numbers per point */\n    points: number[] = [];\n\n    /** The collection of colors */\n    colors: number[] = [];\n\n    /** The UVs collection */\n    uvs: number[] = [];\n\n    /** The indices of the vertices */\n    indices: number[] = [];\n\n    /** Reference to the texture IDs. */\n    textureIds: number[] = [];\n\n    /**\n     * The collection of drawn shapes.\n     * @member {PIXI.GraphicsData[]}\n     */\n    graphicsData: Array<GraphicsData> = [];\n\n    /**\n     * List of current draw calls drived from the batches.\n     * @member {PIXI.BatchDrawCall[]}\n     */\n    drawCalls: Array<BatchDrawCall> = [];\n\n    /** Batches need to regenerated if the geometry is updated. */\n    batchDirty = -1;\n\n    /**\n     * Intermediate abstract format sent to batch system.\n     * Can be converted to drawCalls or to batchable objects.\n     * @member {PIXI.graphicsUtils.BatchPart[]}\n     */\n    batches: Array<BatchPart> = [];\n\n    /** Used to detect if the graphics object has changed. */\n    protected dirty = 0;\n\n    /** Used to check if the cache is dirty. */\n    protected cacheDirty = -1;\n\n    /** Used to detect if we cleared the graphicsData. */\n    protected clearDirty = 0;\n\n    /** Index of the last batched shape in the stack of calls. */\n    protected shapeIndex = 0;\n\n    /** Cached bounds. */\n    protected _bounds: Bounds = new Bounds();\n\n    /** The bounds dirty flag. */\n    protected boundsDirty = -1;\n\n    // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n    constructor()\n    {\n        super();\n    }\n\n    /**\n     * Get the current bounds of the graphic geometry.\n     * @readonly\n     */\n    public get bounds(): Bounds\n    {\n        this.updateBatches();\n\n        if (this.boundsDirty !== this.dirty)\n        {\n            this.boundsDirty = this.dirty;\n            this.calculateBounds();\n        }\n\n        return this._bounds;\n    }\n\n    /** Call if you changed graphicsData manually. Empties all batch buffers. */\n    protected invalidate(): void\n    {\n        this.boundsDirty = -1;\n        this.dirty++;\n        this.batchDirty++;\n        this.shapeIndex = 0;\n\n        this.points.length = 0;\n        this.colors.length = 0;\n        this.uvs.length = 0;\n        this.indices.length = 0;\n        this.textureIds.length = 0;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const batchPart = this.batches[i];\n\n            batchPart.reset();\n            BATCH_POOL.push(batchPart);\n        }\n\n        this.batches.length = 0;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This GraphicsGeometry object. Good for chaining method calls\n     */\n    public clear(): GraphicsGeometry\n    {\n        if (this.graphicsData.length > 0)\n        {\n            this.invalidate();\n            this.clearDirty++;\n            this.graphicsData.length = 0;\n        }\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - Defines style of the fill.\n     * @param lineStyle - Defines style of the lines.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawShape(\n        shape: IShape,\n        fillStyle: FillStyle = null,\n        lineStyle: LineStyle = null,\n        matrix: Matrix = null): GraphicsGeometry\n    {\n        const data = new GraphicsData(shape, fillStyle, lineStyle, matrix);\n\n        this.graphicsData.push(data);\n        this.dirty++;\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawHole(shape: IShape, matrix: Matrix = null): GraphicsGeometry\n    {\n        if (!this.graphicsData.length)\n        {\n            return null;\n        }\n\n        const data = new GraphicsData(shape, null, null, matrix);\n\n        const lastShape = this.graphicsData[this.graphicsData.length - 1];\n\n        data.lineStyle = lastShape.lineStyle;\n\n        lastShape.holes.push(data);\n\n        this.dirty++;\n\n        return this;\n    }\n\n    /** Destroys the GraphicsGeometry object. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        // destroy each of the GraphicsData objects\n        for (let i = 0; i < this.graphicsData.length; ++i)\n        {\n            this.graphicsData[i].destroy();\n        }\n\n        this.points.length = 0;\n        this.points = null;\n        this.colors.length = 0;\n        this.colors = null;\n        this.uvs.length = 0;\n        this.uvs = null;\n        this.indices.length = 0;\n        this.indices = null;\n        this.indexBuffer.destroy();\n        this.indexBuffer = null;\n        this.graphicsData.length = 0;\n        this.graphicsData = null;\n        this.drawCalls.length = 0;\n        this.drawCalls = null;\n        this.batches.length = 0;\n        this.batches = null;\n        this._bounds = null;\n    }\n\n    /**\n     * Check to see if a point is contained within this geometry.\n     * @param point - Point to check if it's contained.\n     * @returns {boolean} `true` if the point is contained within geometry.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        const graphicsData = this.graphicsData;\n\n        for (let i = 0; i < graphicsData.length; ++i)\n        {\n            const data = graphicsData[i];\n\n            if (!data.fillStyle.visible)\n            {\n                continue;\n            }\n\n            // only deal with fills..\n            if (data.shape)\n            {\n                if (data.matrix)\n                {\n                    data.matrix.applyInverse(point, tmpPoint);\n                }\n                else\n                {\n                    tmpPoint.copyFrom(point);\n                }\n\n                if (data.shape.contains(tmpPoint.x, tmpPoint.y))\n                {\n                    let hitHole = false;\n\n                    if (data.holes)\n                    {\n                        for (let i = 0; i < data.holes.length; i++)\n                        {\n                            const hole = data.holes[i];\n\n                            if (hole.shape.contains(tmpPoint.x, tmpPoint.y))\n                            {\n                                hitHole = true;\n                                break;\n                            }\n                        }\n                    }\n\n                    if (!hitHole)\n                    {\n                        return true;\n                    }\n                }\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Generates intermediate batch data. Either gets converted to drawCalls\n     * or used to convert to batch objects directly by the Graphics object.\n     */\n    updateBatches(): void\n    {\n        if (!this.graphicsData.length)\n        {\n            this.batchable = true;\n\n            return;\n        }\n\n        if (!this.validateBatching())\n        {\n            return;\n        }\n\n        this.cacheDirty = this.dirty;\n\n        const uvs = this.uvs;\n        const graphicsData = this.graphicsData;\n\n        let batchPart: BatchPart = null;\n\n        let currentStyle = null;\n\n        if (this.batches.length > 0)\n        {\n            batchPart = this.batches[this.batches.length - 1];\n            currentStyle = batchPart.style;\n        }\n\n        for (let i = this.shapeIndex; i < graphicsData.length; i++)\n        {\n            this.shapeIndex++;\n\n            const data = graphicsData[i];\n            const fillStyle = data.fillStyle;\n            const lineStyle = data.lineStyle;\n            const command = FILL_COMMANDS[data.type];\n\n            // build out the shapes points..\n            command.build(data);\n\n            if (data.matrix)\n            {\n                this.transformPoints(data.points, data.matrix);\n            }\n\n            if (fillStyle.visible || lineStyle.visible)\n            {\n                this.processHoles(data.holes);\n            }\n\n            for (let j = 0; j < 2; j++)\n            {\n                const style = (j === 0) ? fillStyle : lineStyle;\n\n                if (!style.visible) continue;\n\n                const nextTexture = style.texture.baseTexture;\n                const index = this.indices.length;\n                const attribIndex = this.points.length / 2;\n\n                nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                if (j === 0)\n                {\n                    this.processFill(data);\n                }\n                else\n                {\n                    this.processLine(data);\n                }\n\n                const size = (this.points.length / 2) - attribIndex;\n\n                if (size === 0) continue;\n                // close batch if style is different\n                if (batchPart && !this._compareStyles(currentStyle, style))\n                {\n                    batchPart.end(index, attribIndex);\n                    batchPart = null;\n                }\n                // spawn new batch if its first batch or previous was closed\n                if (!batchPart)\n                {\n                    batchPart = BATCH_POOL.pop() || new BatchPart();\n                    batchPart.begin(style, index, attribIndex);\n                    this.batches.push(batchPart);\n                    currentStyle = style;\n                }\n\n                this.addUvs(this.points, uvs, style.texture, attribIndex, size, style.matrix);\n            }\n        }\n\n        const index = this.indices.length;\n        const attrib = this.points.length / 2;\n\n        if (batchPart)\n        {\n            batchPart.end(index, attrib);\n        }\n\n        if (this.batches.length === 0)\n        {\n            // there are no visible styles in GraphicsData\n            // its possible that someone wants Graphics just for the bounds\n            this.batchable = true;\n\n            return;\n        }\n\n        const need32 = attrib > 0xffff;\n\n        // prevent allocation when length is same as buffer\n        if (this.indicesUint16 && this.indices.length === this.indicesUint16.length\n            && need32 === (this.indicesUint16.BYTES_PER_ELEMENT > 2))\n        {\n            this.indicesUint16.set(this.indices);\n        }\n        else\n        {\n            this.indicesUint16 = need32 ? new Uint32Array(this.indices) : new Uint16Array(this.indices);\n        }\n\n        // TODO make this a const..\n        this.batchable = this.isBatchable();\n\n        if (this.batchable)\n        {\n            this.packBatches();\n        }\n        else\n        {\n            this.buildDrawCalls();\n        }\n    }\n\n    /**\n     * Affinity check\n     * @param styleA\n     * @param styleB\n     */\n    protected _compareStyles(styleA: FillStyle | LineStyle, styleB: FillStyle | LineStyle): boolean\n    {\n        if (!styleA || !styleB)\n        {\n            return false;\n        }\n\n        if (styleA.texture.baseTexture !== styleB.texture.baseTexture)\n        {\n            return false;\n        }\n\n        if (styleA.color + styleA.alpha !== styleB.color + styleB.alpha)\n        {\n            return false;\n        }\n\n        if (!!(styleA as LineStyle).native !== !!(styleB as LineStyle).native)\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /** Test geometry for batching process. */\n    protected validateBatching(): boolean\n    {\n        if (this.dirty === this.cacheDirty || !this.graphicsData.length)\n        {\n            return false;\n        }\n\n        for (let i = 0, l = this.graphicsData.length; i < l; i++)\n        {\n            const data = this.graphicsData[i];\n            const fill = data.fillStyle;\n            const line = data.lineStyle;\n\n            if (fill && !fill.texture.baseTexture.valid) return false;\n            if (line && !line.texture.baseTexture.valid) return false;\n        }\n\n        return true;\n    }\n\n    /** Offset the indices so that it works with the batcher. */\n    protected packBatches(): void\n    {\n        this.batchDirty++;\n        this.uvsFloat32 = new Float32Array(this.uvs);\n\n        const batches = this.batches;\n\n        for (let i = 0, l = batches.length; i < l; i++)\n        {\n            const batch = batches[i];\n\n            for (let j = 0; j < batch.size; j++)\n            {\n                const index = batch.start + j;\n\n                this.indicesUint16[index] = this.indicesUint16[index] - batch.attribStart;\n            }\n        }\n    }\n\n    /**\n     * Checks to see if this graphics geometry can be batched.\n     * Currently it needs to be small enough and not contain any native lines.\n     */\n    protected isBatchable(): boolean\n    {\n        // prevent heavy mesh batching\n        if (this.points.length > 0xffff * 2)\n        {\n            return false;\n        }\n\n        const batches = this.batches;\n\n        for (let i = 0; i < batches.length; i++)\n        {\n            if ((batches[i].style as LineStyle).native)\n            {\n                return false;\n            }\n        }\n\n        return (this.points.length < GraphicsGeometry.BATCHABLE_SIZE * 2);\n    }\n\n    /** Converts intermediate batches data to drawCalls. */\n    protected buildDrawCalls(): void\n    {\n        let TICK = ++BaseTexture._globalBatch;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        let currentGroup: BatchDrawCall =  DRAW_CALL_POOL.pop();\n\n        if (!currentGroup)\n        {\n            currentGroup = new BatchDrawCall();\n            currentGroup.texArray = new BatchTextureArray();\n        }\n        currentGroup.texArray.count = 0;\n        currentGroup.start = 0;\n        currentGroup.size = 0;\n        currentGroup.type = DRAW_MODES.TRIANGLES;\n\n        let textureCount = 0;\n        let currentTexture = null;\n        let textureId = 0;\n        let native = false;\n        let drawMode = DRAW_MODES.TRIANGLES;\n\n        let index = 0;\n\n        this.drawCalls.push(currentGroup);\n\n        // TODO - this can be simplified\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const data = this.batches[i];\n\n            // TODO add some full on MAX_TEXTURE CODE..\n            const MAX_TEXTURES = 8;\n\n            // Forced cast for checking `native` without errors\n            const style = data.style as LineStyle;\n\n            const nextTexture = style.texture.baseTexture;\n\n            if (native !== !!style.native)\n            {\n                native = !!style.native;\n                drawMode = native ? DRAW_MODES.LINES : DRAW_MODES.TRIANGLES;\n\n                // force the batch to break!\n                currentTexture = null;\n                textureCount = MAX_TEXTURES;\n                TICK++;\n            }\n\n            if (currentTexture !== nextTexture)\n            {\n                currentTexture = nextTexture;\n\n                if (nextTexture._batchEnabled !== TICK)\n                {\n                    if (textureCount === MAX_TEXTURES)\n                    {\n                        TICK++;\n\n                        textureCount = 0;\n\n                        if (currentGroup.size > 0)\n                        {\n                            currentGroup = DRAW_CALL_POOL.pop();\n                            if (!currentGroup)\n                            {\n                                currentGroup = new BatchDrawCall();\n                                currentGroup.texArray = new BatchTextureArray();\n                            }\n                            this.drawCalls.push(currentGroup);\n                        }\n\n                        currentGroup.start = index;\n                        currentGroup.size = 0;\n                        currentGroup.texArray.count = 0;\n                        currentGroup.type = drawMode;\n                    }\n\n                    // TODO add this to the render part..\n                    // Hack! Because texture has protected `touched`\n                    nextTexture.touched = 1;// touch;\n\n                    nextTexture._batchEnabled = TICK;\n                    nextTexture._batchLocation = textureCount;\n                    nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                    currentGroup.texArray.elements[currentGroup.texArray.count++] = nextTexture;\n                    textureCount++;\n                }\n            }\n\n            currentGroup.size += data.size;\n            index += data.size;\n\n            textureId = nextTexture._batchLocation;\n\n            this.addColors(colors, style.color, style.alpha, data.attribSize, data.attribStart);\n            this.addTextureIds(textureIds, textureId, data.attribSize, data.attribStart);\n        }\n\n        BaseTexture._globalBatch = TICK;\n\n        // upload..\n        // merge for now!\n        this.packAttributes();\n    }\n\n    /** Packs attributes to single buffer. */\n    protected packAttributes(): void\n    {\n        const verts = this.points;\n        const uvs = this.uvs;\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        // verts are 2 positions.. so we * by 3 as there are 6 properties.. then 4 cos its bytes\n        const glPoints = new ArrayBuffer(verts.length * 3 * 4);\n        const f32 = new Float32Array(glPoints);\n        const u32 = new Uint32Array(glPoints);\n\n        let p = 0;\n\n        for (let i = 0; i < verts.length / 2; i++)\n        {\n            f32[p++] = verts[i * 2];\n            f32[p++] = verts[(i * 2) + 1];\n\n            f32[p++] = uvs[i * 2];\n            f32[p++] = uvs[(i * 2) + 1];\n\n            u32[p++] = colors[i];\n\n            f32[p++] = textureIds[i];\n        }\n\n        this._buffer.update(glPoints);\n        this._indexBuffer.update(this.indicesUint16);\n    }\n\n    /**\n     * Process fill part of Graphics.\n     * @param data\n     */\n    protected processFill(data: GraphicsData): void\n    {\n        if (data.holes.length)\n        {\n            buildPoly.triangulate(data, this);\n        }\n        else\n        {\n            const command = FILL_COMMANDS[data.type];\n\n            command.triangulate(data, this);\n        }\n    }\n\n    /**\n     * Process line part of Graphics.\n     * @param data\n     */\n    protected processLine(data: GraphicsData): void\n    {\n        buildLine(data, this);\n\n        for (let i = 0; i < data.holes.length; i++)\n        {\n            buildLine(data.holes[i], this);\n        }\n    }\n\n    /**\n     * Process the holes data.\n     * @param holes\n     */\n    protected processHoles(holes: Array<GraphicsData>): void\n    {\n        for (let i = 0; i < holes.length; i++)\n        {\n            const hole = holes[i];\n            const command = FILL_COMMANDS[hole.type];\n\n            command.build(hole);\n\n            if (hole.matrix)\n            {\n                this.transformPoints(hole.points, hole.matrix);\n            }\n        }\n    }\n\n    /** Update the local bounds of the object. Expensive to use performance-wise. */\n    protected calculateBounds(): void\n    {\n        const bounds = this._bounds;\n\n        bounds.clear();\n        bounds.addVertexData((this.points as any), 0, this.points.length);\n        bounds.pad(this.boundsPadding, this.boundsPadding);\n    }\n\n    /**\n     * Transform points using matrix.\n     * @param points - Points to transform\n     * @param matrix - Transform matrix\n     */\n    protected transformPoints(points: Array<number>, matrix: Matrix): void\n    {\n        for (let i = 0; i < points.length / 2; i++)\n        {\n            const x = points[(i * 2)];\n            const y = points[(i * 2) + 1];\n\n            points[(i * 2)] = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n            points[(i * 2) + 1] = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n        }\n    }\n\n    /**\n     * Add colors.\n     * @param colors - List of colors to add to\n     * @param color - Color to add\n     * @param alpha - Alpha to use\n     * @param size - Number of colors to add\n     * @param offset\n     */\n    protected addColors(\n        colors: Array<number>,\n        color: number,\n        alpha: number,\n        size: number,\n        offset = 0): void\n    {\n        // TODO use the premultiply bits Ivan added\n        const rgb = (color >> 16) + (color & 0xff00) + ((color & 0xff) << 16);\n\n        const rgba =  premultiplyTint(rgb, alpha);\n\n        colors.length = Math.max(colors.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            colors[offset + i] = rgba;\n        }\n    }\n\n    /**\n     * Add texture id that the shader/fragment wants to use.\n     * @param textureIds\n     * @param id\n     * @param size\n     * @param offset\n     */\n    protected addTextureIds(\n        textureIds: Array<number>,\n        id: number,\n        size: number,\n        offset = 0): void\n    {\n        textureIds.length = Math.max(textureIds.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            textureIds[offset + i] = id;\n        }\n    }\n\n    /**\n     * Generates the UVs for a shape.\n     * @param verts - Vertices\n     * @param uvs - UVs\n     * @param texture - Reference to Texture\n     * @param start - Index buffer start index.\n     * @param size - The size/length for index buffer.\n     * @param matrix - Optional transform for all points.\n     */\n    protected addUvs(\n        verts: Array<number>,\n        uvs: Array<number>,\n        texture: Texture,\n        start: number,\n        size: number,\n        matrix: Matrix = null): void\n    {\n        let index = 0;\n        const uvsStart = uvs.length;\n        const frame = texture.frame;\n\n        while (index < size)\n        {\n            let x = verts[(start + index) * 2];\n            let y = verts[((start + index) * 2) + 1];\n\n            if (matrix)\n            {\n                const nx = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n\n                y = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n                x = nx;\n            }\n\n            index++;\n\n            uvs.push(x / frame.width, y / frame.height);\n        }\n\n        const baseTexture = texture.baseTexture;\n\n        if (frame.width < baseTexture.width\n            || frame.height < baseTexture.height)\n        {\n            this.adjustUvs(uvs, texture, uvsStart, size);\n        }\n    }\n\n    /**\n     * Modify uvs array according to position of texture region\n     * Does not work with rotated or trimmed textures\n     * @param uvs - array\n     * @param texture - region\n     * @param start - starting index for uvs\n     * @param size - how many points to adjust\n     */\n    protected adjustUvs(uvs: Array<number>, texture: Texture, start: number, size: number): void\n    {\n        const baseTexture = texture.baseTexture;\n        const eps = 1e-6;\n        const finish = start + (size * 2);\n        const frame = texture.frame;\n        const scaleX = frame.width / baseTexture.width;\n        const scaleY = frame.height / baseTexture.height;\n        let offsetX = frame.x / frame.width;\n        let offsetY = frame.y / frame.height;\n        let minX = Math.floor(uvs[start] + eps);\n        let minY = Math.floor(uvs[start + 1] + eps);\n\n        for (let i = start + 2; i < finish; i += 2)\n        {\n            minX = Math.min(minX, Math.floor(uvs[i] + eps));\n            minY = Math.min(minY, Math.floor(uvs[i + 1] + eps));\n        }\n        offsetX -= minX;\n        offsetY -= minY;\n        for (let i = start; i < finish; i += 2)\n        {\n            uvs[i] = (uvs[i] + offsetX) * scaleX;\n            uvs[i + 1] = (uvs[i + 1] + offsetY) * scaleY;\n        }\n    }\n}\n", "import { FillStyle } from './FillStyle';\nimport { LINE_JOIN, LINE_CAP } from '../const';\n\n/**\n * Represents the line style for Graphics.\n * @memberof PIXI\n */\nexport class LineStyle extends FillStyle\n{\n    /** The width (thickness) of any lines drawn. */\n    public width = 0;\n\n    /** The alignment of any lines drawn (0.5 = middle, 1 = outer, 0 = inner). WebGL only. */\n    public alignment = 0.5;\n\n    /** If true the lines will be draw using LINES instead of TRIANGLE_STRIP. */\n    public native = false;\n\n    /**\n     * Line cap style.\n     * @member {PIXI.LINE_CAP}\n     * @default PIXI.LINE_CAP.BUTT\n     */\n    public cap = LINE_CAP.BUTT;\n\n    /**\n     * Line join style.\n     * @member {PIXI.LINE_JOIN}\n     * @default PIXI.LINE_JOIN.MITER\n     */\n    public join = LINE_JOIN.MITER;\n\n    /** Miter limit. */\n    public miterLimit = 10;\n\n    /** Clones the object. */\n    public clone(): LineStyle\n    {\n        const obj = new LineStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n        obj.width = this.width;\n        obj.alignment = this.alignment;\n        obj.native = this.native;\n        obj.cap = this.cap;\n        obj.join = this.join;\n        obj.miterLimit = this.miterLimit;\n\n        return obj;\n    }\n\n    /** Reset the line style to default. */\n    public reset(): void\n    {\n        super.reset();\n\n        // Override default line style color\n        this.color = 0x0;\n\n        this.alignment = 0.5;\n        this.width = 0;\n        this.native = false;\n    }\n}\n", "import {\n    Circle,\n    Ellipse,\n    PI_2,\n    Point,\n    Polygon,\n    Rectangle,\n    RoundedRectangle,\n    Matrix,\n    SHAPES,\n} from '@pixi/math';\n\nimport type { <PERSON><PERSON><PERSON>, BatchDrawCall } from '@pixi/core';\nimport { Texture, UniformGroup, State, Shader } from '@pixi/core';\nimport { BezierUtils, QuadraticUtils, ArcUtils } from './utils';\nimport { hex2rgb } from '@pixi/utils';\nimport { GraphicsGeometry } from './GraphicsGeometry';\nimport { FillStyle } from './styles/FillStyle';\nimport { LineStyle } from './styles/LineStyle';\nimport { BLEND_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\n\nimport type { IShape, IPointData } from '@pixi/math';\nimport type { IDestroyOptions } from '@pixi/display';\nimport { LINE_JOIN, LINE_CAP } from './const';\n\n/** Batch element computed from Graphics geometry */\nexport interface IGraphicsBatchElement\n{\n    vertexData: Float32Array;\n    blendMode: BLEND_MODES;\n    indices: Uint16Array | Uint32Array;\n    uvs: Float32Array;\n    alpha: number;\n    worldAlpha: number;\n    _batchRGB: number[];\n    _tintRGB: number;\n    _texture: Texture;\n}\n\nexport interface IFillStyleOptions\n{\n    color?: number;\n    alpha?: number;\n    texture?: Texture;\n    matrix?: Matrix;\n}\n\nexport interface ILineStyleOptions extends IFillStyleOptions\n{\n    width?: number;\n    alignment?: number;\n    native?: boolean;\n    cap?: LINE_CAP;\n    join?: LINE_JOIN;\n    miterLimit?: number;\n}\n\nconst temp = new Float32Array(3);\n\n// a default shaders map used by graphics..\nconst DEFAULT_SHADERS: {[key: string]: Shader} = {};\n\nexport interface Graphics extends GlobalMixins.Graphics, Container {}\n\n/**\n * The Graphics class is primarily used to render primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.  However, you can also use a Graphics\n * object to build a list of primitives to use as a mask, or as a complex hitArea.\n *\n * Please note that due to legacy naming conventions, the behavior of some functions in this class\n * can be confusing.  Each call to `drawRect()`, `drawPolygon()`, etc. actually stores that primitive\n * in the Geometry class's GraphicsGeometry object for later use in rendering or hit testing - the\n * functions do not directly draw anything to the screen.  Similarly, the `clear()` function doesn't\n * change the screen, it simply resets the list of primitives, which can be useful if you want to\n * rebuild the contents of an existing Graphics object.\n *\n * Once a GraphicsGeometry list is built, you can re-use it in other Geometry objects as\n * an optimization, by passing it into a new Geometry object's constructor.  Because of this\n * ability, it's important to call `destroy()` on Geometry objects once you are done with them, to\n * properly dereference each GraphicsGeometry and prevent memory leaks.\n * @memberof PIXI\n */\nexport class Graphics extends Container\n{\n    /**\n     * New rendering behavior for rounded rectangles: circular arcs instead of quadratic bezier curves.\n     * In the next major release, we'll enable this by default.\n     */\n    public static nextRoundedRectBehavior = false;\n\n    /**\n     * Temporary point to use for containsPoint.\n     * @private\n     */\n    static _TEMP_POINT = new Point();\n\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Graphics objects.\n     */\n    public shader: Shader = null;\n\n    /** Renderer plugin for batching */\n    public pluginName = 'batch';\n\n    /**\n     * Current path\n     * @readonly\n     */\n    public currentPath: Polygon = null;\n\n    /** A collections of batches! These can be drawn by the renderer batch system. */\n    protected batches: Array<IGraphicsBatchElement> = [];\n\n    /** Update dirty for limiting calculating tints for batches. */\n    protected batchTint = -1;\n\n    /** Update dirty for limiting calculating batches.*/\n    protected batchDirty = -1;\n\n    /** Copy of the object vertex data. */\n    protected vertexData: Float32Array = null;\n\n    /** Current fill style. */\n    protected _fillStyle: FillStyle = new FillStyle();\n\n    /** Current line style. */\n    protected _lineStyle: LineStyle = new LineStyle();\n\n    /** Current shape transform matrix. */\n    protected _matrix: Matrix = null;\n\n    /** Current hole mode is enabled. */\n    protected _holeMode = false;\n    protected _transformID: number;\n    protected _tint: number;\n\n    /**\n     * Represents the WebGL state the Graphics required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    private state: State = State.for2d();\n    private _geometry: GraphicsGeometry;\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh or Graphics objects.\n     * @readonly\n     */\n    public get geometry(): GraphicsGeometry\n    {\n        return this._geometry;\n    }\n\n    /**\n     * @param geometry - Geometry to use, if omitted will create a new GraphicsGeometry instance.\n     */\n    constructor(geometry: GraphicsGeometry = null)\n    {\n        super();\n\n        this._geometry = geometry || new GraphicsGeometry();\n        this._geometry.refCount++;\n\n        /**\n         * When cacheAsBitmap is set to true the graphics object will be rendered as if it was a sprite.\n         * This is useful if your graphics element does not change often, as it will speed up the rendering\n         * of the object in exchange for taking up texture memory. It is also useful if you need the graphics\n         * object to be anti-aliased, because it will be rendered using canvas. This is not recommended if\n         * you are constantly redrawing the graphics element.\n         * @name cacheAsBitmap\n         * @member {boolean}\n         * @memberof PIXI.Graphics#\n         * @default false\n         */\n\n        this._transformID = -1;\n\n        // Set default\n        this.tint = 0xFFFFFF;\n        this.blendMode = BLEND_MODES.NORMAL;\n    }\n\n    /**\n     * Creates a new Graphics object with the same values as this one.\n     * Note that only the geometry of the object is cloned, not its transform (position,scale,etc)\n     * @returns - A clone of the graphics object\n     */\n    public clone(): Graphics\n    {\n        this.finishPoly();\n\n        return new Graphics(this._geometry);\n    }\n\n    /**\n     * The blend mode to be applied to the graphic shape. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.  Note that, since each\n     * primitive in the GraphicsGeometry list is rendered sequentially, modes\n     * such as `PIXI.BLEND_MODES.ADD` and `PIXI.BLEND_MODES.MULTIPLY` will\n     * be applied per-primitive.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    public get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * The tint applied to each graphic shape. This is a hex value. A value of\n     * 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    public get tint(): number\n    {\n        return this._tint;\n    }\n\n    public set tint(value: number)\n    {\n        this._tint = value;\n    }\n\n    /**\n     * The current fill style.\n     * @readonly\n     */\n    public get fill(): FillStyle\n    {\n        return this._fillStyle;\n    }\n\n    /**\n     * The current line style.\n     * @readonly\n     */\n    public get line(): LineStyle\n    {\n        return this._lineStyle;\n    }\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param [width=0] - width of the line to draw, will update the objects stored style\n     * @param [color=0x0] - color of the line to draw, will update the objects stored style\n     * @param [alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param [alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param [native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(width: number, color?: number, alpha?: number, alignment?: number, native?: boolean): this;\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param options - Line style options\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {number} [options.color=0x0] - color of the line to draw, will update the objects stored style\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(options?: ILineStyleOptions): this;\n\n    public lineStyle(options: ILineStyleOptions | number = null,\n        color = 0x0, alpha = 1, alignment = 0.5, native = false): this\n    {\n        // Support non-object params: (width, color, alpha, alignment, native)\n        if (typeof options === 'number')\n        {\n            options = { width: options, color, alpha, alignment, native } as ILineStyleOptions;\n        }\n\n        return this.lineTextureStyle(options);\n    }\n\n    /**\n     * Like line style but support texture for line fill.\n     * @param [options] - Collection of options for setting line style.\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to use\n     * @param {number} [options.color=0x0] - color of the line to draw, will update the objects stored style.\n     *  Default 0xFFFFFF if texture present.\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {PIXI.Matrix} [options.matrix=null] - Texture matrix to transform texture\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineTextureStyle(options?: ILineStyleOptions): this\n    {\n        // Apply defaults\n        options = Object.assign({\n            width: 0,\n            texture: Texture.WHITE,\n            color: (options && options.texture) ? 0xFFFFFF : 0x0,\n            alpha: 1,\n            matrix: null,\n            alignment: 0.5,\n            native: false,\n            cap: LINE_CAP.BUTT,\n            join: LINE_JOIN.MITER,\n            miterLimit: 10,\n        }, options);\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.width > 0 && options.alpha > 0;\n\n        if (!visible)\n        {\n            this._lineStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._lineStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Start a polygon object internally.\n     * @protected\n     */\n    protected startPoly(): void\n    {\n        if (this.currentPath)\n        {\n            const points = this.currentPath.points;\n            const len = this.currentPath.points.length;\n\n            if (len > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = new Polygon();\n                this.currentPath.closeStroke = false;\n                this.currentPath.points.push(points[len - 2], points[len - 1]);\n            }\n        }\n        else\n        {\n            this.currentPath = new Polygon();\n            this.currentPath.closeStroke = false;\n        }\n    }\n\n    /**\n     * Finish the polygon object.\n     * @protected\n     */\n    finishPoly(): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = null;\n            }\n            else\n            {\n                this.currentPath.points.length = 0;\n            }\n        }\n    }\n\n    /**\n     * Moves the current drawing position to x, y.\n     * @param x - the X coordinate to move to\n     * @param y - the Y coordinate to move to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public moveTo(x: number, y: number): this\n    {\n        this.startPoly();\n        this.currentPath.points[0] = x;\n        this.currentPath.points[1] = y;\n\n        return this;\n    }\n\n    /**\n     * Draws a line using the current line style from the current drawing position to (x, y);\n     * The current drawing position is then set to (x, y).\n     * @param x - the X coordinate to draw to\n     * @param y - the Y coordinate to draw to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineTo(x: number, y: number): this\n    {\n        if (!this.currentPath)\n        {\n            this.moveTo(0, 0);\n        }\n\n        // remove duplicates..\n        const points = this.currentPath.points;\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        if (fromX !== x || fromY !== y)\n        {\n            points.push(x, y);\n        }\n\n        return this;\n    }\n\n    /**\n     * Initialize the curve\n     * @param x\n     * @param y\n     */\n    protected _initCurve(x = 0, y = 0): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length === 0)\n            {\n                this.currentPath.points = [x, y];\n            }\n        }\n        else\n        {\n            this.moveTo(x, y);\n        }\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public quadraticCurveTo(cpX: number, cpY: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        const points = this.currentPath.points;\n\n        if (points.length === 0)\n        {\n            this.moveTo(0, 0);\n        }\n\n        QuadraticUtils.curveTo(cpX, cpY, toX, toY, points);\n\n        return this;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns This Graphics object. Good for chaining method calls\n     */\n    public bezierCurveTo(cpX: number, cpY: number, cpX2: number, cpY2: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        BezierUtils.curveTo(cpX, cpY, cpX2, cpY2, toX, toY, this.currentPath.points);\n\n        return this;\n    }\n\n    /**\n     * The arcTo() method creates an arc/curve between two tangents on the canvas.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @param x1 - The x-coordinate of the first tangent point of the arc\n     * @param y1 - The y-coordinate of the first tangent point of the arc\n     * @param x2 - The x-coordinate of the end of the arc\n     * @param y2 - The y-coordinate of the end of the arc\n     * @param radius - The radius of the arc\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this\n    {\n        this._initCurve(x1, y1);\n\n        const points = this.currentPath.points;\n\n        const result = ArcUtils.curveTo(x1, y1, x2, y2, radius, points);\n\n        if (result)\n        {\n            const { cx, cy, radius, startAngle, endAngle, anticlockwise } = result;\n\n            this.arc(cx, cy, radius, startAngle, endAngle, anticlockwise);\n        }\n\n        return this;\n    }\n\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arc(cx: number, cy: number, radius: number, startAngle: number, endAngle: number, anticlockwise = false): this\n    {\n        if (startAngle === endAngle)\n        {\n            return this;\n        }\n\n        if (!anticlockwise && endAngle <= startAngle)\n        {\n            endAngle += PI_2;\n        }\n        else if (anticlockwise && startAngle <= endAngle)\n        {\n            startAngle += PI_2;\n        }\n\n        const sweep = endAngle - startAngle;\n\n        if (sweep === 0)\n        {\n            return this;\n        }\n\n        const startX = cx + (Math.cos(startAngle) * radius);\n        const startY = cy + (Math.sin(startAngle) * radius);\n        const eps = this._geometry.closePointEps;\n\n        // If the currentPath exists, take its points. Otherwise call `moveTo` to start a path.\n        let points = this.currentPath ? this.currentPath.points : null;\n\n        if (points)\n        {\n            // TODO: make a better fix.\n\n            // We check how far our start is from the last existing point\n            const xDiff = Math.abs(points[points.length - 2] - startX);\n            const yDiff = Math.abs(points[points.length - 1] - startY);\n\n            if (xDiff < eps && yDiff < eps)\n            {\n                // If the point is very close, we don't add it, since this would lead to artifacts\n                // during tessellation due to floating point imprecision.\n            }\n            else\n            {\n                points.push(startX, startY);\n            }\n        }\n        else\n        {\n            this.moveTo(startX, startY);\n            points = this.currentPath.points;\n        }\n\n        ArcUtils.arc(startX, startY, cx, cy, radius, startAngle, endAngle, anticlockwise, points);\n\n        return this;\n    }\n\n    /**\n     * Specifies a simple one-color fill that subsequent calls to other Graphics methods\n     * (such as lineTo() or drawCircle()) use when drawing.\n     * @param color - the color of the fill\n     * @param alpha - the alpha of the fill\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public beginFill(color = 0, alpha = 1): this\n    {\n        return this.beginTextureFill({ texture: Texture.WHITE, color, alpha });\n    }\n\n    /**\n     * Begin the texture fill\n     * @param options - Object object.\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to fill\n     * @param {number} [options.color=0xffffff] - Background to fill behind texture\n     * @param {number} [options.alpha=1] - Alpha of fill\n     * @param {PIXI.Matrix} [options.matrix=null] - Transform matrix\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    beginTextureFill(options?: IFillStyleOptions): this\n    {\n        // Apply defaults\n        options = Object.assign({\n            texture: Texture.WHITE,\n            color: 0xFFFFFF,\n            alpha: 1,\n            matrix: null,\n        }, options) as IFillStyleOptions;\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.alpha > 0;\n\n        if (!visible)\n        {\n            this._fillStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._fillStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Applies a fill to the lines and shapes that were added since the last call to the beginFill() method.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public endFill(): this\n    {\n        this.finishPoly();\n\n        this._fillStyle.reset();\n\n        return this;\n    }\n\n    /**\n     * Draws a rectangle shape.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRect(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Rectangle(x, y, width, height));\n    }\n\n    /**\n     * Draw a rectangle shape with rounded/beveled corners.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @param radius - Radius of the rectangle corners\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRoundedRect(x: number, y: number, width: number, height: number, radius: number): this\n    {\n        return this.drawShape(new RoundedRectangle(x, y, width, height, radius));\n    }\n\n    /**\n     * Draws a circle.\n     * @param x - The X coordinate of the center of the circle\n     * @param y - The Y coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawCircle(x: number, y: number, radius: number): this\n    {\n        return this.drawShape(new Circle(x, y, radius));\n    }\n\n    /**\n     * Draws an ellipse.\n     * @param x - The X coordinate of the center of the ellipse\n     * @param y - The Y coordinate of the center of the ellipse\n     * @param width - The half width of the ellipse\n     * @param height - The half height of the ellipse\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawEllipse(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Ellipse(x, y, width, height));\n    }\n\n    public drawPolygon(...path: Array<number> | Array<IPointData>): this;\n    public drawPolygon(path: Array<number> | Array<IPointData> | Polygon): this;\n\n    /**\n     * Draws a polygon using the given path.\n     * @param {number[]|PIXI.IPointData[]|PIXI.Polygon} path - The path data used to construct the polygon.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawPolygon(...path: any[]): this\n    {\n        let points: Array<number> | Array<IPointData>;\n        let closeStroke = true;// !!this._fillStyle;\n\n        const poly = path[0] as Polygon;\n\n        // check if data has points..\n        if (poly.points)\n        {\n            closeStroke = poly.closeStroke;\n            points = poly.points;\n        }\n        else\n        if (Array.isArray(path[0]))\n        {\n            points = path[0];\n        }\n        else\n        {\n            points = path;\n        }\n\n        const shape = new Polygon(points);\n\n        shape.closeStroke = closeStroke;\n\n        this.drawShape(shape);\n\n        return this;\n    }\n\n    /**\n     * Draw any shape.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - Shape to draw\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawShape(shape: IShape): this\n    {\n        if (!this._holeMode)\n        {\n            this._geometry.drawShape(\n                shape,\n                this._fillStyle.clone(),\n                this._lineStyle.clone(),\n                this._matrix\n            );\n        }\n        else\n        {\n            this._geometry.drawHole(shape, this._matrix);\n        }\n\n        return this;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public clear(): this\n    {\n        this._geometry.clear();\n        this._lineStyle.reset();\n        this._fillStyle.reset();\n\n        this._boundsID++;\n        this._matrix = null;\n        this._holeMode = false;\n        this.currentPath = null;\n\n        return this;\n    }\n\n    /**\n     * True if graphics consists of one rectangle, and thus, can be drawn like a Sprite and\n     * masked with gl.scissor.\n     * @returns - True if only 1 rect.\n     */\n    public isFastRect(): boolean\n    {\n        const data = this._geometry.graphicsData;\n\n        return data.length === 1\n            && data[0].shape.type === SHAPES.RECT\n            && !data[0].matrix\n            && !data[0].holes.length\n            && !(data[0].lineStyle.visible && data[0].lineStyle.width);\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n        // batch part..\n        // batch it!\n\n        geometry.updateBatches();\n\n        if (geometry.batchable)\n        {\n            if (this.batchDirty !== geometry.batchDirty)\n            {\n                this._populateBatches();\n            }\n\n            this._renderBatched(renderer);\n        }\n        else\n        {\n            // no batching...\n            renderer.batch.flush();\n\n            this._renderDirect(renderer);\n        }\n    }\n\n    /** Populating batches for rendering. */\n    protected _populateBatches(): void\n    {\n        const geometry = this._geometry;\n        const blendMode = this.blendMode;\n        const len = geometry.batches.length;\n\n        this.batchTint = -1;\n        this._transformID = -1;\n        this.batchDirty = geometry.batchDirty;\n        this.batches.length = len;\n\n        this.vertexData = new Float32Array(geometry.points);\n\n        for (let i = 0; i < len; i++)\n        {\n            const gI = geometry.batches[i];\n            const color = gI.style.color;\n            const vertexData = new Float32Array(this.vertexData.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const uvs = new Float32Array(geometry.uvsFloat32.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const indices = new Uint16Array(geometry.indicesUint16.buffer,\n                gI.start * 2,\n                gI.size);\n\n            const batch = {\n                vertexData,\n                blendMode,\n                indices,\n                uvs,\n                _batchRGB: hex2rgb(color) as Array<number>,\n                _tintRGB: color,\n                _texture: gI.style.texture,\n                alpha: gI.style.alpha,\n                worldAlpha: 1 };\n\n            this.batches[i] = batch;\n        }\n    }\n\n    /**\n     * Renders the batches using the BathedRenderer plugin\n     * @param renderer - The renderer\n     */\n    protected _renderBatched(renderer: Renderer): void\n    {\n        if (!this.batches.length)\n        {\n            return;\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n\n        this.calculateVertices();\n        this.calculateTints();\n\n        for (let i = 0, l = this.batches.length; i < l; i++)\n        {\n            const batch = this.batches[i];\n\n            batch.worldAlpha = this.worldAlpha * batch.alpha;\n\n            renderer.plugins[this.pluginName].render(batch);\n        }\n    }\n\n    /**\n     * Renders the graphics direct\n     * @param renderer - The renderer\n     */\n    protected _renderDirect(renderer: Renderer): void\n    {\n        const shader = this._resolveDirectShader(renderer);\n\n        const geometry = this._geometry;\n        const tint = this.tint;\n        const worldAlpha = this.worldAlpha;\n        const uniforms = shader.uniforms;\n        const drawCalls = geometry.drawCalls;\n\n        // lets set the transfomr\n        uniforms.translationMatrix = this.transform.worldTransform;\n\n        // and then lets set the tint..\n        uniforms.tint[0] = (((tint >> 16) & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[1] = (((tint >> 8) & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[2] = ((tint & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[3] = worldAlpha;\n\n        // the first draw call, we can set the uniforms of the shader directly here.\n\n        // this means that we can tack advantage of the sync function of pixi!\n        // bind and sync uniforms..\n        // there is a way to optimise this..\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(geometry, shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // then render the rest of them...\n        for (let i = 0, l = drawCalls.length; i < l; i++)\n        {\n            this._renderDrawCallDirect(renderer, geometry.drawCalls[i]);\n        }\n    }\n\n    /**\n     * Renders specific DrawCall\n     * @param renderer\n     * @param drawCall\n     */\n    protected _renderDrawCallDirect(renderer: Renderer, drawCall: BatchDrawCall): void\n    {\n        const { texArray, type, size, start } = drawCall;\n        const groupTextureCount = texArray.count;\n\n        for (let j = 0; j < groupTextureCount; j++)\n        {\n            renderer.texture.bind(texArray.elements[j], j);\n        }\n\n        renderer.geometry.draw(type, size, start);\n    }\n\n    /**\n     * Resolves shader for direct rendering\n     * @param renderer - The renderer\n     */\n    protected _resolveDirectShader(renderer: Renderer): Shader\n    {\n        let shader = this.shader;\n\n        const pluginName = this.pluginName;\n\n        if (!shader)\n        {\n            // if there is no shader here, we can use the default shader.\n            // and that only gets created if we actually need it..\n            // but may be more than one plugins for graphics\n            if (!DEFAULT_SHADERS[pluginName])\n            {\n                const { MAX_TEXTURES } = renderer.plugins[pluginName];\n                const sampleValues = new Int32Array(MAX_TEXTURES);\n\n                for (let i = 0; i < MAX_TEXTURES; i++)\n                {\n                    sampleValues[i] = i;\n                }\n\n                const uniforms = {\n                    tint: new Float32Array([1, 1, 1, 1]),\n                    translationMatrix: new Matrix(),\n                    default: UniformGroup.from({ uSamplers: sampleValues }, true),\n                };\n\n                const program = renderer.plugins[pluginName]._shader.program;\n\n                DEFAULT_SHADERS[pluginName] = new Shader(program, uniforms);\n            }\n\n            shader = DEFAULT_SHADERS[pluginName];\n        }\n\n        return shader;\n    }\n\n    /** Retrieves the bounds of the graphic shape as a rectangle object. */\n    protected _calculateBounds(): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n\n        // skipping when graphics is empty, like a container\n        if (!geometry.graphicsData.length)\n        {\n            return;\n        }\n\n        const { minX, minY, maxX, maxY } = geometry.bounds;\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Tests if a point is inside this graphics object\n     * @param point - the point to test\n     * @returns - the result of the test\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, Graphics._TEMP_POINT);\n\n        return this._geometry.containsPoint(Graphics._TEMP_POINT);\n    }\n\n    /** Recalculate the tint by applying tint to batches using Graphics tint. */\n    protected calculateTints(): void\n    {\n        if (this.batchTint !== this.tint)\n        {\n            this.batchTint = this.tint;\n\n            const tintRGB = hex2rgb(this.tint, temp);\n\n            for (let i = 0; i < this.batches.length; i++)\n            {\n                const batch = this.batches[i];\n\n                const batchTint = batch._batchRGB;\n\n                const r = (tintRGB[0] * batchTint[0]) * 255;\n                const g = (tintRGB[1] * batchTint[1]) * 255;\n                const b = (tintRGB[2] * batchTint[2]) * 255;\n\n                // TODO Ivan, can this be done in one go?\n                const color = (r << 16) + (g << 8) + (b | 0);\n\n                batch._tintRGB = (color >> 16)\n                        + (color & 0xff00)\n                        + ((color & 0xff) << 16);\n            }\n        }\n    }\n\n    /** If there's a transform update or a change to the shape of the geometry, recalculate the vertices. */\n    protected calculateVertices(): void\n    {\n        const wtID = this.transform._worldID;\n\n        if (this._transformID === wtID)\n        {\n            return;\n        }\n\n        this._transformID = wtID;\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const data = this._geometry.points;// batch.vertexDataOriginal;\n        const vertexData = this.vertexData;\n\n        let count = 0;\n\n        for (let i = 0; i < data.length; i += 2)\n        {\n            const x = data[i];\n            const y = data[i + 1];\n\n            vertexData[count++] = (a * x) + (c * y) + tx;\n            vertexData[count++] = (d * y) + (b * x) + ty;\n        }\n    }\n\n    /**\n     * Closes the current path.\n     * @returns - Returns itself.\n     */\n    public closePath(): this\n    {\n        const currentPath = this.currentPath;\n\n        if (currentPath)\n        {\n            // we don't need to add extra point in the end because buildLine will take care of that\n            currentPath.closeStroke = true;\n            // ensure that the polygon is completed, and is available for hit detection\n            // (even if the graphics is not rendered yet)\n            this.finishPoly();\n        }\n\n        return this;\n    }\n\n    /**\n     * Apply a matrix to the positional data.\n     * @param matrix - Matrix to use for transform current shape.\n     * @returns - Returns itself.\n     */\n    public setMatrix(matrix: Matrix): this\n    {\n        this._matrix = matrix;\n\n        return this;\n    }\n\n    /**\n     * Begin adding holes to the last draw shape\n     * IMPORTANT: holes must be fully inside a shape to work\n     * Also weirdness ensues if holes overlap!\n     * Ellipses, Circles, Rectangles and Rounded Rectangles cannot be holes or host for holes in CanvasRenderer,\n     * please use `moveTo` `lineTo`, `quadraticCurveTo` if you rely on pixi-legacy bundle.\n     * @returns - Returns itself.\n     */\n    public beginHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = true;\n\n        return this;\n    }\n\n    /**\n     * End adding holes to the last draw shape.\n     * @returns - Returns itself.\n     */\n    public endHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = false;\n\n        return this;\n    }\n\n    /**\n     * Destroys the Graphics object.\n     * @param options - Options parameter. A boolean will act as if all\n     *  options have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have\n     *  their destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this._geometry.refCount--;\n        if (this._geometry.refCount === 0)\n        {\n            this._geometry.dispose();\n        }\n\n        this._matrix = null;\n        this.currentPath = null;\n        this._lineStyle.destroy();\n        this._lineStyle = null;\n        this._fillStyle.destroy();\n        this._fillStyle = null;\n        this._geometry = null;\n        this.shader = null;\n        this.vertexData = null;\n        this.batches.length = 0;\n        this.batches = null;\n\n        super.destroy(options);\n    }\n}\n", "export * from './const';\nexport * from './styles/FillStyle';\nexport * from './Graphics';\nexport * from './GraphicsData';\nexport * from './GraphicsGeometry';\nexport * from './styles/LineStyle';\n\nimport {\n    buildPoly,\n    buildCircle,\n    buildRectangle,\n    buildRoundedRectangle,\n    buildLine,\n    ArcUtils,\n    BezierUtils,\n    QuadraticUtils,\n    BatchPart,\n    FILL_COMMANDS,\n    BATCH_POOL,\n    DRAW_CALL_POOL\n} from './utils';\nimport type { BatchDrawCall } from '@pixi/core/';\nimport type { IShapeBuildCommand } from './utils/IShapeBuildCommand';\nimport type { SHAPES } from '@pixi/math';\n\nexport const graphicsUtils = {\n    buildPoly: buildPoly as IShapeBuildCommand,\n    buildCircle: buildCircle as IShapeBuildCommand,\n    buildRectangle: buildRectangle as IShapeBuildCommand,\n    buildRoundedRectangle: buildRoundedRectangle as IShapeBuildCommand,\n    buildLine,\n    ArcUtils,\n    BezierUtils,\n    QuadraticUtils,\n    BatchPart,\n    FILL_COMMANDS: FILL_COMMANDS as Record<SHAPES, IShapeBuildCommand>,\n    BATCH_POOL: BATCH_POOL as Array<BatchPart>,\n    DRAW_CALL_POOL: DRAW_CALL_POOL as Array<BatchDrawCall>\n};\n"], "names": ["arguments"], "mappings": ";;;;;;;;;;;;;AAAA;;;;;;;;;;;AAWG;AACH,IAAY,UAMX;AAND,CAAA,UAAY,SAAS,EAAA;AAGjB,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACnB,CAAC,EANW,SAAS,KAAT,SAAS,GAMpB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;AACH,IAAY,SAMX;AAND,CAAA,UAAY,QAAQ,EAAA;AAGhB,IAAA,QAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,QAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,QAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACrB,CAAC,EANW,QAAQ,KAAR,QAAQ,GAMnB,EAAA,CAAA,CAAA,CAAA;AAcD;;;;;;;;;;;;;AAaG;AACI,IAAM,eAAe,GAA4B;AACpD,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,SAAS,EAAE,EAAE;AACb,IAAA,WAAW,EAAE,CAAC;AACd,IAAA,WAAW,EAAG,IAAI;AAElB,IAAA,OAAO,EAAE,MAAM;AAEf,IAAA,cAAc,EAAd,UAAe,MAAc,EAAE,eAAoB,EAAA;AAApB,QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAoB,GAAA,EAAA,CAAA,EAAA;AAE/C,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,EAC9C;AACI,YAAA,OAAO,eAAe,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;AAEhD,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,EAC7B;AACI,YAAA,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;AAC7B,SAAA;AACI,aAAA,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,EAClC;AACI,YAAA,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;AAC7B,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;;;ACzFL;;;AAGG;AACH,IAAA,SAAA,kBAAA,YAAA;AA0BI,IAAA,SAAA,SAAA,GAAA;AAxBA;;;AAGG;QACI,IAAK,CAAA,KAAA,GAAG,QAAQ,CAAC;;QAGjB,IAAK,CAAA,KAAA,GAAG,GAAG,CAAC;AAEnB;;;AAGG;AACI,QAAA,IAAA,CAAA,OAAO,GAAY,OAAO,CAAC,KAAK,CAAC;AAExC;;;AAGG;QACI,IAAM,CAAA,MAAA,GAAW,IAAI,CAAC;;QAGtB,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;QAInB,IAAI,CAAC,KAAK,EAAE,CAAC;KAChB;;AAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAM,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;AAE5B,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3B,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAE3B,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;;AAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB,CAAA;;AAGM,IAAA,SAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;ACpED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;ACtBA,SAAS,cAAc,CAAC,MAAgB,EAAE,IAAY,EAAA;;AAAZ,IAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAY,GAAA,KAAA,CAAA,EAAA;AAElD,IAAA,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IAExB,IAAI,CAAC,GAAG,CAAC,EACT;QACI,OAAO;AACV,KAAA;IAED,IAAI,IAAI,GAAG,CAAC,CAAC;AAEb,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EACrE;AACI,QAAA,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEzB,QAAA,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;QAE9B,EAAE,GAAG,EAAE,CAAC;QACR,EAAE,GAAG,EAAE,CAAC;AACX,KAAA;AAED,IAAA,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,EAC9C;AACI,QAAA,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEhB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EACvC;AACI,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,IAAM,EAAE,GAAG,CAAC,CAAC;AACb,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;YAEjB,EAA2B,GAAA,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAlD,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,CAA6B;YACpD,EAA2B,GAAA,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAlD,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,CAA6B;AACvD,SAAA;AACJ,KAAA;AACL,CAAC;AACD;;;;;;;;;AASG;AACI,IAAM,SAAS,GAAuB;IAEzC,KAAK,EAAL,UAAM,YAAY,EAAA;QAEd,YAAY,CAAC,MAAM,GAAI,YAAY,CAAC,KAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;KACxE;IAED,WAAW,EAAA,UAAC,YAAY,EAAE,gBAAgB,EAAA;AAEtC,QAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACjC,QAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AACjC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACtC,QAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAEzC,QAAA,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EACtB;AACI,YAAA,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAE9B,IAAM,SAAS,GAAG,EAAE,CAAC;;AAGrB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;AACI,gBAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAEtB,gBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAElC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAClC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,aAAA;;YAGD,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YAE/C,IAAI,CAAC,SAAS,EACd;gBACI,OAAO;AACV,aAAA;AAED,YAAA,IAAM,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAEjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAC5C;gBACI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AACrC,gBAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AACzC,gBAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC5C,aAAA;AAED,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EACtC;gBACI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,aAAA;AACJ,SAAA;KACJ;CACJ;;AC3GD;AAMA;;;;;;;;;AASG;AACI,IAAM,WAAW,GAAuB;IAE3C,KAAK,EAAL,UAAM,YAAY,EAAA;;AAGd,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AAEnC,QAAA,IAAI,CAAC,CAAC;AACN,QAAA,IAAI,CAAC,CAAC;AACN,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,CAAC;AAEP,QAAA,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EACrC;AACI,YAAA,IAAM,MAAM,GAAG,YAAY,CAAC,KAAe,CAAC;AAE5C,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACb,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACb,YAAA,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACf,SAAA;AACI,aAAA,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAC1C;AACI,YAAA,IAAM,OAAO,GAAG,YAAY,CAAC,KAAgB,CAAC;AAE9C,YAAA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AACd,YAAA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AACd,YAAA,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;AACnB,YAAA,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;AACpB,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACf,SAAA;AAED,aAAA;AACI,YAAA,IAAM,WAAW,GAAG,YAAY,CAAC,KAAyB,CAAC;AAC3D,YAAA,IAAM,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;AACxC,YAAA,IAAM,UAAU,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;AAE1C,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,SAAS,CAAC;AAC9B,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,UAAU,CAAC;YAC/B,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AACrF,YAAA,EAAE,GAAG,SAAS,GAAG,EAAE,CAAC;AACpB,YAAA,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;AACxB,SAAA;AAED,QAAA,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAC/C;AACI,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAElB,OAAO;AACV,SAAA;;AAGD,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9C,QAAA,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAEhD,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAElB,IAAI,CAAC,KAAK,CAAC,EACX;YACI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,KAAK,CAAC,EACX;AACI,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAClB,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC/B,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC/B,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC/B,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAE/B,OAAO;AACV,SAAA;QAED,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,EAAE,GAAG,EAAE,CAAC;QACZ,IAAI,EAAE,GAAG,CAAC,CAAC;AAEX,QAAA;AACI,YAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YACnB,IAAM,EAAE,GAAG,EAAE,CAAC;AACd,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAElB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAElB,YAAA,IAAI,EAAE,EACN;AACI,gBAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAElB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AACrB,aAAA;AACJ,SAAA;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC1B;AACI,YAAA,IAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAChC,YAAA,IAAM,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,YAAA,IAAM,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAElB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AACrB,SAAA;AAED,QAAA;YACI,IAAM,EAAE,GAAG,EAAE,CAAC;AACd,YAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAElB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAElB,YAAA,IAAI,EAAE,EACN;AACI,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AACrB,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,WAAW,EAAX,UAAY,YAAY,EAAE,gBAAgB,EAAA;AAEtC,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACnC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACtC,QAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAEzC,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EACvB;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAM,MAAM,GAAG,OAAO,CAAC;AAEvB,QAAA,IAAI,CAAC,CAAC;AACN,QAAA,IAAI,CAAC,CAAC;AAEN,QAAA,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EACrC;AACI,YAAA,IAAM,MAAM,GAAG,YAAY,CAAC,KAAe,CAAC;AAE5C,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACb,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAChB,SAAA;AAED,aAAA;AACI,YAAA,IAAM,WAAW,GAAG,YAAY,CAAC,KAAyB,CAAC;AAE3D,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC5C,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChD,SAAA;AAED,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;;AAGnC,QAAA,KAAK,CAAC,IAAI,CACN,YAAY,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,EACrE,YAAY,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAE3E,QAAA,OAAO,EAAE,CAAC;AAEV,QAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAEjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EACzC;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;YAGrC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC5C,SAAA;QAED,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;KAC7C;CACJ;;ACpND;;;;;;;;;AASG;AACI,IAAM,cAAc,GAAuB;IAE9C,KAAK,EAAL,UAAM,YAAY,EAAA;;;;AAKd,QAAA,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAkB,CAAC;AACjD,QAAA,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;AACrB,QAAA,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;AACrB,QAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC7B,QAAA,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAE/B,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AAEnC,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAElB,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;KACtB;IAED,WAAW,EAAA,UAAC,YAAY,EAAE,gBAAgB,EAAA;AAEtC,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACnC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;AAEtC,QAAA,IAAM,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAEjC,QAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAC3B,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EACpB,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EACpB,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1B,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAC3D,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;KAC9C;CACJ;;AC3CD;;;;;;;;;;AAUG;AACH,SAAS,KAAK,CAAC,EAAU,EAAE,EAAU,EAAE,IAAY,EAAA;AAE/C,IAAA,IAAM,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AAErB,IAAA,OAAO,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;AAC9B,CAAC;AAED;;;;;;;;;;;;;;;AAeG;AACH,SAAS,oBAAoB,CACzB,KAAa,EAAE,KAAa,EAC5B,GAAW,EAAE,GAAW,EACxB,GAAW,EAAE,GAAW,EACxB,GAAuB,EAAA;AAAvB,IAAA,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAuB,GAAA,EAAA,CAAA,EAAA;IAEvB,IAAM,CAAC,GAAG,EAAE,CAAC;IACb,IAAM,MAAM,GAAG,GAAG,CAAC;IAEnB,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;AAEV,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAClC;AACI,QAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;QAGV,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1B,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1B,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACxB,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;;QAGxB,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;;QAGrB,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EACjF;YACI,SAAS;AACZ,SAAA;AAED,QAAA,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;;AASG;AACI,IAAM,qBAAqB,GAAuB;IAErD,KAAK,EAAL,UAAM,YAAY,EAAA;QAEd,IAAI,QAAQ,CAAC,uBAAuB,EACpC;AACI,YAAA,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAEhC,OAAO;AACV,SAAA;AAED,QAAA,IAAM,SAAS,GAAG,YAAY,CAAC,KAAyB,CAAC;AACzD,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACnC,QAAA,IAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACtB,QAAA,IAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACtB,QAAA,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AAC9B,QAAA,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;;AAGhC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAEpF,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;;QAGlB,IAAI,CAAC,MAAM,EACX;YACI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;AACtB,SAAA;AAED,aAAA;AACI,YAAA,oBAAoB,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,EAC9B,CAAC,EAAE,CAAC,EACJ,CAAC,GAAG,MAAM,EAAE,CAAC,EACb,MAAM,CAAC,CAAC;YACZ,oBAAoB,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,EACnC,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EACf,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,MAAM,CAAC,CAAC;AACZ,YAAA,oBAAoB,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,GAAG,MAAM,EAC/C,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAC9B,MAAM,CAAC,CAAC;YACZ,oBAAoB,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EACvC,CAAC,EAAE,CAAC,GAAG,MAAM,EACb,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,MAAM,EACtB,MAAM,CAAC,CAAC;AACf,SAAA;KACJ;IAED,WAAW,EAAA,UAAC,YAAY,EAAE,gBAAgB,EAAA;QAEtC,IAAI,QAAQ,CAAC,uBAAuB,EACpC;AACI,YAAA,WAAW,CAAC,WAAW,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;YAExD,OAAO;AACV,SAAA;AAED,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AAEnC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACtC,QAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAEzC,QAAA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAEhC,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAE1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EACnD;YACI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;;AAEpC,YAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;;AAExC,YAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC3C,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC7C;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACtC,SAAA;KACJ;CACJ;;AC3KD;;;;;;;;;;;;;;;AAeG;AACH,SAAS,MAAM,CACX,CAAS,EACT,CAAS,EACT,EAAU,EACV,EAAU,EACV,WAAmB,EACnB,WAAmB,EACnB,SAAkB,mEAClB,KAAoB,EAAA;IAGpB,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;IAClC,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;IAClC,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;IAClC,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;;AAGlC,IAAA,IAAI,GAAG,CAAC;AAAC,IAAA,IACL,GAAG,CAAC;AAER,IAAA,IAAI,SAAS,EACb;QACI,GAAG,GAAG,EAAE,CAAC;QACT,GAAG,GAAG,CAAC,EAAE,CAAC;AACb,KAAA;AAED,SAAA;QACI,GAAG,GAAG,CAAC,EAAE,CAAC;QACV,GAAG,GAAG,EAAE,CAAC;AACZ,KAAA;;AAGD,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;AACrB,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;AACrB,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;AACrB,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;;AAGrB,IAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrB,IAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAErB,IAAA,OAAO,CAAC,CAAC;AACb,CAAC;AAED;;;;;;;;;;;;;;;AAeG;AACH,SAAS,KAAK,CACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,KAAoB,EACpB,SAAkB,EAAA;AAGlB,IAAA,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;AACvB,IAAA,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACxC,IAAA,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AAE1C,IAAA,IAAI,SAAS,IAAI,MAAM,GAAG,MAAM,EAChC;AACI,QAAA,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACzB,KAAA;AACI,SAAA,IAAI,CAAC,SAAS,IAAI,MAAM,GAAG,MAAM,EACtC;AACI,QAAA,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACzB,KAAA;IAED,IAAI,UAAU,GAAG,MAAM,CAAC;AACxB,IAAA,IAAM,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC;IAClC,IAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAEzC;;;;;;;;;;;;;;;;AAgBG;AAEH,IAAA,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;IAChE,IAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9E,IAAA,IAAM,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;IAEtC,UAAU,IAAI,QAAQ,CAAC;AAEvB,IAAA,IAAI,SAAS,EACb;AACI,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,QAAQ,EACxE;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnB,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,EACxC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC;AAC1C,SAAA;AAED,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,KAAA;AAED,SAAA;AACI,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,QAAQ,EACxE;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,EACxC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC;AACvC,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,SAAA;AAED,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,KAAA;IAED,OAAO,QAAQ,GAAG,CAAC,CAAC;AACxB,CAAC;AAED;;;;;;;;AAQG;AACH,SAAS,kBAAkB,CAAC,YAA0B,EAAE,gBAAkC,EAAA;AAEtF,IAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAgB,CAAC;AAC5C,IAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACzD,IAAA,IAAM,GAAG,GAAG,gBAAgB,CAAC,aAAa,CAAC;AAE3C,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EACvB;QACI,OAAO;AACV,KAAA;;;;;;;;;;AAWD,IAAA,IAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;;AAGrC,IAAA,IAAM,UAAU,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,IAAM,SAAS,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAClF,IAAA,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC;AACpE,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;AACtD,WAAA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;;AAGlD,IAAA,IAAI,WAAW,EACf;;AAEI,QAAA,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;AAExB,QAAA,IAAI,UAAU,EACd;YACI,MAAM,CAAC,GAAG,EAAE,CAAC;YACb,MAAM,CAAC,GAAG,EAAE,CAAC;YACb,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE,SAAA;AAED,QAAA,IAAM,SAAS,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,GAAG,CAAC;AACrD,QAAA,IAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,GAAG,CAAC;AAErD,QAAA,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACrC,QAAA,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACrC,KAAA;AAED,IAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACtC,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACjC,IAAA,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;AAC/B,IAAA,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;;AAGpC,IAAA,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC9B,IAAA,IAAM,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;IACnC,IAAM,iBAAiB,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;;AAG9D,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;;IAGX,IAAI,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AACvB,IAAA,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;IACpB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf,IAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;IAExD,KAAK,IAAI,IAAI,CAAC;IACd,KAAK,IAAI,IAAI,CAAC;IACd,KAAK,IAAI,KAAK,CAAC;IACf,KAAK,IAAI,KAAK,CAAC;AAEf,IAAA,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC;IAC9B,IAAM,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;AACpC,IAAA,IAAM,WAAW,GAAG,KAAK,GAAG,CAAC,CAAC;IAE9B,IAAI,CAAC,WAAW,EAChB;AACI,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,CAAC,KAAK,EAChC;AACI,YAAA,UAAU,IAAI,KAAK,CACf,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,KAAK,EACL,IAAI,CACP,GAAG,CAAC,CAAC;AACT,SAAA;AACI,aAAA,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,CAAC,MAAM,EACtC;AACI,YAAA,UAAU,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACrF,SAAA;AACJ,KAAA;;AAGD,IAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;AAChC,IAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;AAEhC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EACnC;QACI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACzB,QAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAE/B,QAAA,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnB,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAEzB,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACzB,QAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAE/B,QAAA,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AACnB,QAAA,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;AAEhB,QAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;QACpD,KAAK,IAAI,IAAI,CAAC;QACd,KAAK,IAAI,IAAI,CAAC;QACd,KAAK,IAAI,KAAK,CAAC;QACf,KAAK,IAAI,KAAK,CAAC;AAEf,QAAA,MAAM,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AACpB,QAAA,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;AAEjB,QAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;QACxD,MAAM,IAAI,IAAI,CAAC;QACf,MAAM,IAAI,IAAI,CAAC;QACf,MAAM,IAAI,KAAK,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC;;AAGhB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACpB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACpB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACpB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;;AAGpB,QAAA,IAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;;AAEtC,QAAA,IAAM,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;AACxC,QAAA,IAAM,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;;;AAI9B,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAC3C;AACI,YAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;AAChC,YAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;;YAGhC,IAAI,GAAG,IAAI,CAAC,EACZ;AACI,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,EAClC;oBACI,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,iBAAA;AAED,qBAAA;oBACI,UAAU,IAAI,CAAC,CAAC;AACnB,iBAAA;AAED,gBAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAC3B,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AACjC,gBAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAC3B,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AACpC,aAAA;YAED,SAAS;AACZ,SAAA;;AAGD,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;AAC7E,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AACjF,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;AAC7C,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;AAC7C,QAAA,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;;AAGhE,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;AAC3C,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;;AAE3C,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;AAC3C,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;;AAG3C,QAAA,IAAM,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;QAC9F,IAAM,YAAY,GAAG,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC;QAC3D,IAAM,uBAAuB,GAAG,sBAAsB,IAAI,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC,CAAC;AACtG,QAAA,IAAM,aAAa,GAAG,KAAK,IAAI,uBAAuB,CAAC;AAEvD,QAAA,IAAI,aAAa,EACjB;AACI,YAAA,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,IAAI,KAAK,GAAG,YAAY,GAAG,iBAAiB,EAC9E;gBACI,IAAI,SAAS,gCACb;oBACI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACrB,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBACnE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACrB,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AACxE,iBAAA;AACI,mDACL;oBACI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBACnE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACrB,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;oBACrE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,iBAAA;gBAED,UAAU,IAAI,CAAC,CAAC;AACnB,aAAA;AACI,iBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,EACvC;gBACI,IAAI,SAAS,uBACb;AACI,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrB,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAEnE,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,IAAI,CACd,GAAG,CAAC,CAAC;AAEN,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrB,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AACxE,iBAAA;AACI,yCACL;AACI,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;AACnE,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBAErB,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,KAAK,CACf,GAAG,CAAC,CAAC;AAEN,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AACrE,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,iBAAA;AACJ,aAAA;AAED,iBAAA;AACI,gBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrB,gBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,aAAA;AACJ,SAAA;;AAED,SAAA;YACI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;YACnE,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;AACnE,YAAA,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,EAClC;gBACI,IAAI,SAAS,uBACb;oBACI,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,IAAI,CACd,GAAG,CAAC,CAAC;AACT,iBAAA;AACI,yCACL;oBACI,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,KAAK,CACf,GAAG,CAAC,CAAC;AACT,iBAAA;AACJ,aAAA;AACI,iBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,IAAI,KAAK,GAAG,YAAY,IAAI,iBAAiB,EACpF;AACI,gBAAA,IAAI,SAAS,EACb;oBACI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACrB,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,iBAAA;AAED,qBAAA;oBACI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACrB,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,iBAAA;gBACD,UAAU,IAAI,CAAC,CAAC;AACnB,aAAA;YACD,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;YACrE,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;YACrE,UAAU,IAAI,CAAC,CAAC;AACnB,SAAA;AACJ,KAAA;IAED,EAAE,GAAG,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9B,IAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAEpC,EAAE,GAAG,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9B,IAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAEpC,IAAA,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AACnB,IAAA,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;AAEhB,IAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;IACpD,KAAK,IAAI,IAAI,CAAC;IACd,KAAK,IAAI,IAAI,CAAC;IACd,KAAK,IAAI,KAAK,CAAC;IACf,KAAK,IAAI,KAAK,CAAC;AAEf,IAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;AACnE,IAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;IAEnE,IAAI,CAAC,WAAW,EAChB;AACI,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,CAAC,KAAK,EAChC;AACI,YAAA,UAAU,IAAI,KAAK,CACf,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,KAAK,EACL,KAAK,CACR,GAAG,CAAC,CAAC;AACT,SAAA;AACI,aAAA,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,CAAC,MAAM,EACtC;AACI,YAAA,UAAU,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACtF,SAAA;AACJ,KAAA;AAED,IAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;IACzC,IAAM,IAAI,GAAG,eAAe,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;;AAG/D,IAAA,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,GAAG,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,EAC7D;QACI,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;QACpB,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAExB,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACxB,QAAA,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE9B,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACxB,QAAA,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;AAG9B,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,EAC3E;YACI,SAAS;AACZ,SAAA;AAED,QAAA,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,KAAA;AACL,CAAC;AAED;;;;;;;;AAQG;AACH,SAAS,eAAe,CAAC,YAA0B,EAAE,gBAAkC,EAAA;IAEnF,IAAI,CAAC,GAAG,CAAC,CAAC;AAEV,IAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAgB,CAAC;IAC5C,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;AACnD,IAAA,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC;AAEpE,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;UAAE,OAAO,EAAA;AAEhC,IAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACtC,IAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACzC,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAEjC,IAAA,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,IAAI,YAAY,GAAG,UAAU,CAAC;AAE9B,IAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAC3B;QACI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;AAE7C,QAAA,YAAY,EAAE,CAAC;AAClB,KAAA;AAED,IAAA,IAAI,WAAW,EACf;AACI,QAAA,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AAC1C,KAAA;AACL,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,SAAS,CAAC,YAA0B,EAAE,gBAAkC,EAAA;AAEpF,IAAA,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,EACjC;AACI,QAAA,eAAe,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACnD,KAAA;AAED,SAAA;AACI,QAAA,kBAAkB,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACtD,KAAA;AACL;;ACjmBA;;;AAGG;AACH,IAAA,QAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,QAAA,GAAA;KA6GC;AA3GG;;;;;;;;;;;;AAYG;AACI,IAAA,QAAA,CAAA,OAAO,GAAd,UAAe,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,MAAc,EAAE,MAAqB,EAAA;QAEhG,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAExC,QAAA,IAAM,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;AACtB,QAAA,IAAM,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;AACtB,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAE3C,QAAA,IAAI,EAAE,GAAG,MAAM,IAAI,MAAM,KAAK,CAAC,EAC/B;YACI,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EACxE;AACI,gBAAA,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACvB,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AACjC,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AACjC,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AACjC,QAAA,IAAM,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AACvC,QAAA,IAAM,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AACvC,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACxB,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACxB,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AACjC,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;QACjC,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1B,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1B,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1B,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1B,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;QAE9C,OAAO;AACH,YAAA,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACb,YAAA,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACb,YAAA,MAAM,EAAA,MAAA;AACN,YAAA,UAAU,EAAA,UAAA;AACV,YAAA,QAAQ,EAAA,QAAA;YACR,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SACrC,CAAC;KACL,CAAA;;AAGD;;;;;;;;;;;;;;;AAeG;AACI,IAAA,QAAA,CAAA,GAAG,GAAV,UAAW,OAAe,EAAE,OAAe,EAAE,EAAU,EAAE,EAAU,EAAE,MAAc,EAC/E,UAAkB,EAAE,QAAgB,EAAE,cAAuB,EAAE,MAAqB,EAAA;AAEpF,QAAA,IAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,CAAC;AACpC,QAAA,IAAM,CAAC,GAAG,eAAe,CAAC,cAAc,CACpC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CACzC,CAAC;QAEF,IAAM,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAChC,QAAA,IAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;QACzB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;QACvB,IAAM,SAAS,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,QAAQ,CAAC;QAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,QAAQ,EAAE,EAAE,CAAC,EAClC;YACI,IAAM,IAAI,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;AACjC,YAAA,IAAM,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;YACvD,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAE3B,MAAM,CAAC,IAAI,CACP,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,EAAE,EAC7C,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,EAAE,CACjD,CAAC;AACL,SAAA;KACJ,CAAA;IAEL,OAAC,QAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AC5HD;;;AAGG;AACH,IAAA,WAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,WAAA,GAAA;KAiHC;AA/GG;;;;;;;;;;;;;;AAcG;AACI,IAAA,WAAA,CAAA,WAAW,GAAlB,UACI,KAAa,EAAE,KAAa,EAC5B,GAAW,EAAE,GAAW,EACxB,IAAY,EAAE,IAAY,EAC1B,GAAW,EAAE,GAAW,EAAA;QAExB,IAAM,CAAC,GAAG,EAAE,CAAC;QACb,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,IAAI,EAAE,GAAG,GAAG,CAAC;QACb,IAAI,EAAE,GAAG,GAAG,CAAC;QACb,IAAI,EAAE,GAAG,GAAG,CAAC;QACb,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,IAAI,EAAE,GAAG,GAAG,CAAC;QACb,IAAI,EAAE,GAAG,GAAG,CAAC;QACb,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,IAAI,KAAK,GAAG,KAAK,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAC3B;AACI,YAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACV,YAAA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACX,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACZ,YAAA,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACf,YAAA,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACd,YAAA,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AAEf,YAAA,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC;AAChF,YAAA,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC;AAC9E,YAAA,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;AACf,YAAA,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;YACf,KAAK,GAAG,CAAC,CAAC;YACV,KAAK,GAAG,CAAC,CAAC;AAEV,YAAA,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9C,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;;;;;;;;;AAYG;AACI,IAAA,WAAA,CAAA,OAAO,GAAd,UACI,GAAW,EAAE,GAAW,EACxB,IAAY,EAAE,IAAY,EAC1B,GAAW,EAAE,GAAW,EACxB,MAAqB,EAAA;QAErB,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAExC,QAAA,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;QAEnB,IAAM,CAAC,GAAG,eAAe,CAAC,cAAc,CACpC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CACxE,CAAC;QAEF,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;AAEX,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAE1B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAClC;AACI,YAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEV,YAAA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACb,YAAA,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACd,YAAA,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AAEf,YAAA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACX,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAEZ,YAAA,MAAM,CAAC,IAAI,CACP,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,EACvE,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAC1E,CAAC;AACL,SAAA;KACJ,CAAA;IACL,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACrHD;;;AAGG;AACH,IAAA,cAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,cAAA,GAAA;KA6EC;AA3EG;;;;;;;;;;;;AAYG;AACI,IAAA,cAAA,CAAA,WAAW,GAAlB,UACI,KAAa,EAAE,KAAa,EAC5B,GAAW,EAAE,GAAW,EACxB,GAAW,EAAE,GAAW,EAAA;QAExB,IAAM,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACrC,IAAM,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AACrC,QAAA,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;AACvC,QAAA,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;AACvC,QAAA,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC,QAAA,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC,QAAA,IAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAEhC,QAAA,IAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACrC,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,QAAA,IAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;QACzB,IAAM,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B,QAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAElB,QAAA,OAAO,CACH,CAAC,GAAG,GAAG,CAAC;eACD,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACnB,eACE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvB,kBAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAC/C,KACJ,GAAG,GAAG,GAAG,CAAC,CAAC;KACnB,CAAA;AAED;;;;;;;;;AASG;IACI,cAAO,CAAA,OAAA,GAAd,UAAe,GAAW,EAAE,GAAW,EAAE,GAAW,EAAE,GAAW,EAAE,MAAqB,EAAA;QAEpF,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAExC,IAAM,CAAC,GAAG,eAAe,CAAC,cAAc,CACpC,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAC/D,CAAC;QAEF,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;QAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAC3B;AACI,YAAA,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEhB,YAAA,EAAE,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;AACjC,YAAA,EAAE,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;YAEjC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EACnD,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACpD,SAAA;KACJ,CAAA;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AChFD;;;AAGG;AACH,IAAA,SAAA,kBAAA,YAAA;AAQI,IAAA,SAAA,SAAA,GAAA;QAEI,IAAI,CAAC,KAAK,EAAE,CAAC;KAChB;AAED;;;;;AAKG;AACI,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,UAAa,KAA4B,EAAE,UAAkB,EAAE,WAAmB,EAAA;QAE9E,IAAI,CAAC,KAAK,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAClC,CAAA;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAV,UAAW,QAAgB,EAAE,SAAiB,EAAA;QAE1C,IAAI,CAAC,UAAU,GAAG,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;KACrC,CAAA;AAEM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;KACvB,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACrDD;;;;AAIG;;AA0BH;;;;AAIG;AACI,IAAM,aAAa,IAAA,EAAA,GAAA,EAAA;AACtB,IAAA,EAAA,CAAC,MAAM,CAAC,IAAI,CAAA,GAAG,SAAS;AACxB,IAAA,EAAA,CAAC,MAAM,CAAC,IAAI,CAAA,GAAG,WAAW;AAC1B,IAAA,EAAA,CAAC,MAAM,CAAC,IAAI,CAAA,GAAG,WAAW;AAC1B,IAAA,EAAA,CAAC,MAAM,CAAC,IAAI,CAAA,GAAG,cAAc;AAC7B,IAAA,EAAA,CAAC,MAAM,CAAC,IAAI,CAAA,GAAG,qBAAqB;OACvC,CAAC;AAEF;;;;AAIG;AACI,IAAM,UAAU,GAAqB,EAAE,CAAC;AAE/C;;;;AAIG;AACI,IAAM,cAAc,GAAyB,EAAE;;ACnDtD;;;AAGG;AACH,IAAA,YAAA,kBAAA,YAAA;AA2BI;;;;;AAKG;AACH,IAAA,SAAA,YAAA,CAAY,KAAa,EAAE,SAA2B,EAAE,SAA2B,EAAE,MAAqB,EAAA;AAA/E,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;AAAE,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;;QAZ1G,IAAM,CAAA,MAAA,GAAa,EAAE,CAAC;;QAItB,IAAK,CAAA,KAAA,GAAwB,EAAE,CAAC;AAU5B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;KAC1B;AAED;;;AAGG;AACI,IAAA,YAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,OAAO,IAAI,YAAY,CACnB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,CACd,CAAC;KACL,CAAA;;AAGM,IAAA,YAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;AAEI,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA;;ACzCD,IAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;AAE7B;;;;;;;AAOG;AACH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;IAAsC,SAAa,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;;AA2E/C,IAAA,SAAA,gBAAA,GAAA;AAAA,QAAA,IAAA,KAAA,GAEI,iBAAO,IACV,IAAA,CAAA;;QApEM,KAAa,CAAA,aAAA,GAAG,IAAI,CAAC;;QAGrB,KAAa,CAAA,aAAA,GAAG,CAAC,CAAC;QAEzB,KAAU,CAAA,UAAA,GAAiB,IAAI,CAAC;QAChC,KAAa,CAAA,aAAA,GAA8B,IAAI,CAAC;QAChD,KAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;QAGlB,KAAM,CAAA,MAAA,GAAa,EAAE,CAAC;;QAGtB,KAAM,CAAA,MAAA,GAAa,EAAE,CAAC;;QAGtB,KAAG,CAAA,GAAA,GAAa,EAAE,CAAC;;QAGnB,KAAO,CAAA,OAAA,GAAa,EAAE,CAAC;;QAGvB,KAAU,CAAA,UAAA,GAAa,EAAE,CAAC;AAE1B;;;AAGG;QACH,KAAY,CAAA,YAAA,GAAwB,EAAE,CAAC;AAEvC;;;AAGG;QACH,KAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;;QAGrC,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC,CAAC;AAEhB;;;;AAIG;QACH,KAAO,CAAA,OAAA,GAAqB,EAAE,CAAC;;QAGrB,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;;QAGV,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC,CAAC;;QAGhB,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC;;QAGf,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC;;AAGf,QAAA,KAAA,CAAA,OAAO,GAAW,IAAI,MAAM,EAAE,CAAC;;QAG/B,KAAW,CAAA,WAAA,GAAG,CAAC,CAAC,CAAC;;KAM1B;AAMD,IAAA,MAAA,CAAA,cAAA,CAAW,gBAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAJjB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,IAAI,CAAC,aAAa,EAAE,CAAC;AAErB,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,KAAK,EACnC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC9B,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,aAAA;YAED,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;;;AAAA,KAAA,CAAA,CAAA;;AAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,UAAU,GAApB,YAAA;AAEI,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAEpB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AAE3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAC9C;YACI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAE1B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAC5C;YACI,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAElC,SAAS,CAAC,KAAK,EAAE,CAAC;AAClB,YAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KAC3B,CAAA;AAED;;;AAGG;AACI,IAAA,gBAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAChC;YACI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;AAOG;IACI,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UACI,KAAa,EACb,SAA2B,EAC3B,SAA2B,EAC3B,MAAqB,EAAA;AAFrB,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;AAC3B,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;AAC3B,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;AAErB,QAAA,IAAM,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAEnE,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;AAEb,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;AACI,IAAA,gBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,KAAa,EAAE,MAAqB,EAAA;AAArB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;AAEhD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAC7B;AACI,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAM,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAEzD,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAElE,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AAErC,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3B,IAAI,CAAC,KAAK,EAAE,CAAC;AAEb,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;;AAGM,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,MAAM,CAAA,SAAA,CAAA,OAAO,WAAE,CAAC;;AAGhB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EACjD;YACI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AAChB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB,CAAA;AAED;;;;AAIG;IACI,gBAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,KAAiB,EAAA;AAElC,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAEvC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAC5C;AACI,YAAA,IAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AAE7B,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAC3B;gBACI,SAAS;AACZ,aAAA;;YAGD,IAAI,IAAI,CAAC,KAAK,EACd;gBACI,IAAI,IAAI,CAAC,MAAM,EACf;oBACI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7C,iBAAA;AAED,qBAAA;AACI,oBAAA,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC5B,iBAAA;AAED,gBAAA,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAC/C;oBACI,IAAI,OAAO,GAAG,KAAK,CAAC;oBAEpB,IAAI,IAAI,CAAC,KAAK,EACd;AACI,wBAAA,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAC,EAAE,EAC1C;4BACI,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAC,CAAC,CAAC;AAE3B,4BAAA,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAC/C;gCACI,OAAO,GAAG,IAAI,CAAC;gCACf,MAAM;AACT,6BAAA;AACJ,yBAAA;AACJ,qBAAA;oBAED,IAAI,CAAC,OAAO,EACZ;AACI,wBAAA,OAAO,IAAI,CAAC;AACf,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;AAGG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;AAEI,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAC7B;AACI,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAC5B;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;AAE7B,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACrB,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAEvC,IAAI,SAAS,GAAc,IAAI,CAAC;QAEhC,IAAI,YAAY,GAAG,IAAI,CAAC;AAExB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAC3B;AACI,YAAA,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAClD,YAAA,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;AAClC,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAC1D;YACI,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,YAAA,IAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AAC7B,YAAA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACjC,YAAA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,IAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;AAGzC,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEpB,IAAI,IAAI,CAAC,MAAM,EACf;gBACI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAClD,aAAA;AAED,YAAA,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,EAC1C;AACI,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,aAAA;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC1B;AACI,gBAAA,IAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC;gBAEhD,IAAI,CAAC,KAAK,CAAC,OAAO;sBAAE,SAAS,EAAA;AAE7B,gBAAA,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;AAC9C,gBAAA,IAAM,OAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;gBAClC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAE3C,gBAAA,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC;gBAEzC,IAAI,CAAC,KAAK,CAAC,EACX;AACI,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,iBAAA;AAED,qBAAA;AACI,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,iBAAA;AAED,gBAAA,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC;gBAEpD,IAAI,IAAI,KAAK,CAAC;sBAAE,SAAS,EAAA;;gBAEzB,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC,EAC1D;AACI,oBAAA,SAAS,CAAC,GAAG,CAAC,OAAK,EAAE,WAAW,CAAC,CAAC;oBAClC,SAAS,GAAG,IAAI,CAAC;AACpB,iBAAA;;gBAED,IAAI,CAAC,SAAS,EACd;oBACI,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,IAAI,SAAS,EAAE,CAAC;oBAChD,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,OAAK,EAAE,WAAW,CAAC,CAAC;AAC3C,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC7B,YAAY,GAAG,KAAK,CAAC;AACxB,iBAAA;gBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACjF,aAAA;AACJ,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAClC,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAEtC,QAAA,IAAI,SAAS,EACb;AACI,YAAA,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAC7B;;;AAGI,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,OAAO;AACV,SAAA;AAED,QAAA,IAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;;AAG/B,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM;eACpE,MAAM,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,CAAC,CAAC,EAC5D;YACI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACxC,SAAA;AAED,aAAA;YACI,IAAI,CAAC,aAAa,GAAG,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/F,SAAA;;AAGD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,SAAS,EAClB;YACI,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;AAED,aAAA;YACI,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACO,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,UAAyB,MAA6B,EAAE,MAA6B,EAAA;AAEjF,QAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EACtB;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,MAAM,CAAC,OAAO,CAAC,WAAW,EAC7D;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAC/D;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAI,CAAC,CAAE,MAAoB,CAAC,MAAM,KAAK,CAAC,CAAE,MAAoB,CAAC,MAAM,EACrE;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;;AAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAC/D;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACxD;YACI,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAClC,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;AAC5B,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;YAE5B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK;AAAE,gBAAA,EAAA,OAAO,KAAK,CAAC,EAAA;YAC1D,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK;AAAE,gBAAA,EAAA,OAAO,KAAK,CAAC,EAAA;AAC7D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;;AAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,WAAW,GAArB,YAAA;QAEI,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAE7C,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAE7B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC9C;AACI,YAAA,IAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAEzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EACnC;AACI,gBAAA,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAE9B,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;AAC7E,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;AAGG;AACO,IAAA,gBAAA,CAAA,SAAA,CAAA,WAAW,GAArB,YAAA;;QAGI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,EACnC;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAE7B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EACvC;YACI,IAAK,OAAO,CAAC,CAAC,CAAC,CAAC,KAAmB,CAAC,MAAM,EAC1C;AACI,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AACJ,SAAA;AAED,QAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,gBAAgB,CAAC,cAAc,GAAG,CAAC,EAAE;KACrE,CAAA;;AAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;AAEI,QAAA,IAAI,IAAI,GAAG,EAAE,WAAW,CAAC,YAAY,CAAC;AAEtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAC9C;YACI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAE1B,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAEnC,QAAA,IAAI,YAAY,GAAmB,cAAc,CAAC,GAAG,EAAE,CAAC;QAExD,IAAI,CAAC,YAAY,EACjB;AACI,YAAA,YAAY,GAAG,IAAI,aAAa,EAAE,CAAC;AACnC,YAAA,YAAY,CAAC,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;AACnD,SAAA;AACD,QAAA,YAAY,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;AAChC,QAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;AACvB,QAAA,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;AACtB,QAAA,YAAY,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC;QAEzC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,cAAc,GAAG,IAAI,CAAC;QAC1B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,MAAM,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC;QAEpC,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;AAGlC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAC5C;YACI,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;YAG7B,IAAM,YAAY,GAAG,CAAC,CAAC;;AAGvB,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAkB,CAAC;AAEtC,YAAA,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;AAE9C,YAAA,IAAI,MAAM,KAAK,CAAC,CAAC,KAAK,CAAC,MAAM,EAC7B;AACI,gBAAA,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;AACxB,gBAAA,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC;;gBAG5D,cAAc,GAAG,IAAI,CAAC;gBACtB,YAAY,GAAG,YAAY,CAAC;AAC5B,gBAAA,IAAI,EAAE,CAAC;AACV,aAAA;YAED,IAAI,cAAc,KAAK,WAAW,EAClC;gBACI,cAAc,GAAG,WAAW,CAAC;AAE7B,gBAAA,IAAI,WAAW,CAAC,aAAa,KAAK,IAAI,EACtC;oBACI,IAAI,YAAY,KAAK,YAAY,EACjC;AACI,wBAAA,IAAI,EAAE,CAAC;wBAEP,YAAY,GAAG,CAAC,CAAC;AAEjB,wBAAA,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,EACzB;AACI,4BAAA,YAAY,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC;4BACpC,IAAI,CAAC,YAAY,EACjB;AACI,gCAAA,YAAY,GAAG,IAAI,aAAa,EAAE,CAAC;AACnC,gCAAA,YAAY,CAAC,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;AACnD,6BAAA;AACD,4BAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACrC,yBAAA;AAED,wBAAA,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,wBAAA,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;AACtB,wBAAA,YAAY,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;AAChC,wBAAA,YAAY,CAAC,IAAI,GAAG,QAAQ,CAAC;AAChC,qBAAA;;;AAID,oBAAA,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC;AAExB,oBAAA,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC;AACjC,oBAAA,WAAW,CAAC,cAAc,GAAG,YAAY,CAAC;AAC1C,oBAAA,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC;AAEzC,oBAAA,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,CAAC;AAC5E,oBAAA,YAAY,EAAE,CAAC;AAClB,iBAAA;AACJ,aAAA;AAED,YAAA,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;AAC/B,YAAA,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;AAEnB,YAAA,SAAS,GAAG,WAAW,CAAC,cAAc,CAAC;YAEvC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACpF,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAChF,SAAA;AAED,QAAA,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;;;QAIhC,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB,CAAA;;AAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;AAEI,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC1B,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACrB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;;AAGnC,QAAA,IAAM,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACvD,QAAA,IAAM,GAAG,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAA,IAAM,GAAG,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEtC,IAAI,CAAC,GAAG,CAAC,CAAC;AAEV,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EACzC;YACI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACxB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YAE9B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACtB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YAE5B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAErB,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAChD,CAAA;AAED;;;AAGG;IACO,gBAAW,CAAA,SAAA,CAAA,WAAA,GAArB,UAAsB,IAAkB,EAAA;AAEpC,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EACrB;AACI,YAAA,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACrC,SAAA;AAED,aAAA;YACI,IAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEzC,YAAA,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACnC,SAAA;KACJ,CAAA;AAED;;;AAGG;IACO,gBAAW,CAAA,SAAA,CAAA,WAAA,GAArB,UAAsB,IAAkB,EAAA;AAEpC,QAAA,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAEtB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAC1C;YACI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,SAAA;KACJ,CAAA;AAED;;;AAGG;IACO,gBAAY,CAAA,SAAA,CAAA,YAAA,GAAtB,UAAuB,KAA0B,EAAA;AAE7C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;AACI,YAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEzC,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEpB,IAAI,IAAI,CAAC,MAAM,EACf;gBACI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAClD,aAAA;AACJ,SAAA;KACJ,CAAA;;AAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,eAAe,GAAzB,YAAA;AAEI,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5B,MAAM,CAAC,KAAK,EAAE,CAAC;AACf,QAAA,MAAM,CAAC,aAAa,CAAE,IAAI,CAAC,MAAc,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;KACtD,CAAA;AAED;;;;AAIG;AACO,IAAA,gBAAA,CAAA,SAAA,CAAA,eAAe,GAAzB,UAA0B,MAAqB,EAAE,MAAc,EAAA;AAE3D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAC1C;YACI,IAAM,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAC1B,YAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YAE9B,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;AAC9D,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;AACrE,SAAA;KACJ,CAAA;AAED;;;;;;;AAOG;IACO,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAnB,UACI,MAAqB,EACrB,KAAa,EACb,KAAa,EACb,IAAY,EACZ,MAAU,EAAA;AAAV,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;;QAGV,IAAM,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;QAEtE,IAAM,IAAI,GAAI,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAE1C,QAAA,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;QAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAC7B;AACI,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC7B,SAAA;KACJ,CAAA;AAED;;;;;;AAMG;IACO,gBAAa,CAAA,SAAA,CAAA,aAAA,GAAvB,UACI,UAAyB,EACzB,EAAU,EACV,IAAY,EACZ,MAAU,EAAA;AAAV,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;AAEV,QAAA,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAC7B;AACI,YAAA,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAC/B,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;AACO,IAAA,gBAAA,CAAA,SAAA,CAAA,MAAM,GAAhB,UACI,KAAoB,EACpB,GAAkB,EAClB,OAAgB,EAChB,KAAa,EACb,IAAY,EACZ,MAAqB,EAAA;AAArB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;QAErB,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,QAAA,IAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;AAC5B,QAAA,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAE5B,OAAO,KAAK,GAAG,IAAI,EACnB;AACI,YAAA,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;AACnC,YAAA,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAEzC,YAAA,IAAI,MAAM,EACV;gBACI,IAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;gBAEvD,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;gBAChD,CAAC,GAAG,EAAE,CAAC;AACV,aAAA;AAED,YAAA,KAAK,EAAE,CAAC;AAER,YAAA,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAC/C,SAAA;AAED,QAAA,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAExC,QAAA,IAAI,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK;AAC5B,eAAA,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,EACxC;YACI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAChD,SAAA;KACJ,CAAA;AAED;;;;;;;AAOG;IACO,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAnB,UAAoB,GAAkB,EAAE,OAAgB,EAAE,KAAa,EAAE,IAAY,EAAA;AAEjF,QAAA,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,IAAM,GAAG,GAAG,IAAI,CAAC;QACjB,IAAM,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClC,QAAA,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAC/C,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACjD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QACpC,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACrC,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACxC,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAE5C,QAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAC1C;AACI,YAAA,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAChD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACvD,SAAA;QACD,OAAO,IAAI,IAAI,CAAC;QAChB,OAAO,IAAI,IAAI,CAAC;AAChB,QAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EACtC;AACI,YAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC;AACrC,YAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC;AAChD,SAAA;KACJ,CAAA;AAh3BD;;;;AAIG;IACW,gBAAc,CAAA,cAAA,GAAG,GAAG,CAAC;IA42BvC,OAAC,gBAAA,CAAA;CAAA,CAn3BqC,aAAa,CAm3BlD;;AC55BD;;;AAGG;AACH,IAAA,SAAA,kBAAA,UAAA,MAAA,EAAA;IAA+B,SAAS,CAAA,SAAA,EAAA,MAAA,CAAA,CAAA;AAAxC,IAAA,SAAA,SAAA,GAAA;QAAA,IA4DC,KAAA,GAAA,MAAA,KAAA,IAAA,IAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,IAAA,CAAA;;QAzDU,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;;QAGV,KAAS,CAAA,SAAA,GAAG,GAAG,CAAC;;QAGhB,KAAM,CAAA,MAAA,GAAG,KAAK,CAAC;AAEtB;;;;AAIG;AACI,QAAA,KAAA,CAAA,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;AAE3B;;;;AAIG;AACI,QAAA,KAAA,CAAA,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC;;QAGvB,KAAU,CAAA,UAAA,GAAG,EAAE,CAAC;;KAkC1B;;AA/BU,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAM,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;AAE5B,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3B,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3B,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB,QAAA,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAC/B,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACnB,QAAA,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAEjC,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;;AAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;QAEI,MAAM,CAAA,SAAA,CAAA,KAAK,WAAE,CAAC;;AAGd,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AAEjB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACvB,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CA5DA,CAA+B,SAAS,CA4DvC;;ACTD,IAAM,IAAI,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AAEjC;AACA,IAAM,eAAe,GAA4B,EAAE,CAAC;AAIpD;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,QAAA,kBAAA,UAAA,MAAA,EAAA;IAA8B,SAAS,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;AAyEnC;;AAEG;AACH,IAAA,SAAA,QAAA,CAAY,QAAiC,EAAA;AAAjC,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAiC,GAAA,IAAA,CAAA,EAAA;AAA7C,QAAA,IAAA,KAAA,GAEI,iBAAO,IAsBV,IAAA,CAAA;AAtFD;;;AAGG;QACI,KAAM,CAAA,MAAA,GAAW,IAAI,CAAC;;QAGtB,KAAU,CAAA,UAAA,GAAG,OAAO,CAAC;AAE5B;;;AAGG;QACI,KAAW,CAAA,WAAA,GAAY,IAAI,CAAC;;QAGzB,KAAO,CAAA,OAAA,GAAiC,EAAE,CAAC;;QAG3C,KAAS,CAAA,SAAA,GAAG,CAAC,CAAC,CAAC;;QAGf,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC,CAAC;;QAGhB,KAAU,CAAA,UAAA,GAAiB,IAAI,CAAC;;AAGhC,QAAA,KAAA,CAAA,UAAU,GAAc,IAAI,SAAS,EAAE,CAAC;;AAGxC,QAAA,KAAA,CAAA,UAAU,GAAc,IAAI,SAAS,EAAE,CAAC;;QAGxC,KAAO,CAAA,OAAA,GAAW,IAAI,CAAC;;QAGvB,KAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AAI5B;;;AAGG;AACK,QAAA,KAAA,CAAA,KAAK,GAAU,KAAK,CAAC,KAAK,EAAE,CAAC;QAqBjC,KAAI,CAAC,SAAS,GAAG,QAAQ,IAAI,IAAI,gBAAgB,EAAE,CAAC;AACpD,QAAA,KAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAE1B;;;;;;;;;;AAUG;AAEH,QAAA,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;;AAGvB,QAAA,KAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACrB,QAAA,KAAI,CAAC,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC;;KACvC;AAhCD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AANnB;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;;;AAAA,KAAA,CAAA,CAAA;AA+BD;;;;AAIG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;QAEI,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,QAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACvC,CAAA;AAUD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AAKpB,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;SAC/B;AAhBD;;;;;;;AAOG;AACH,QAAA,GAAA,EAAA,UAAqB,KAAkB,EAAA;AAEnC,YAAA,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;SAChC;;;AAAA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AALf;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AAED,QAAA,GAAA,EAAA,UAAgB,KAAa,EAAA;AAEzB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACtB;;;AALA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAJf;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAJf;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;IAgCM,QAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,OAA0C,EACvD,KAAW,EAAE,KAAS,EAAE,SAAe,EAAE,MAAc,EAAA;AAD1C,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA0C,GAAA,IAAA,CAAA,EAAA;AACvD,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAW,GAAA,GAAA,CAAA,EAAA;AAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAe,GAAA,GAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAc,GAAA,KAAA,CAAA,EAAA;;AAGvD,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAC/B;AACI,YAAA,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAA,KAAA,EAAE,KAAK,EAAA,KAAA,EAAE,SAAS,EAAA,SAAA,EAAE,MAAM,EAAA,MAAA,EAAuB,CAAC;AACtF,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;KACzC,CAAA;AAED;;;;;;;;;;;;;;;;AAgBG;IACI,QAAgB,CAAA,SAAA,CAAA,gBAAA,GAAvB,UAAwB,OAA2B,EAAA;;AAG/C,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACpB,YAAA,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,OAAO,CAAC,KAAK;AACtB,YAAA,KAAK,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,QAAQ,GAAG,GAAG;AACpD,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,MAAM,EAAE,IAAI;AACZ,YAAA,SAAS,EAAE,GAAG;AACd,YAAA,MAAM,EAAE,KAAK;YACb,GAAG,EAAE,QAAQ,CAAC,IAAI;YAClB,IAAI,EAAE,SAAS,CAAC,KAAK;AACrB,YAAA,UAAU,EAAE,EAAE;SACjB,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,IAAI,CAAC,WAAW,EACpB;YACI,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,EACZ;AACI,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAC3B,SAAA;AAED,aAAA;YACI,IAAI,OAAO,CAAC,MAAM,EAClB;gBACI,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACxC,gBAAA,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;AAC3B,aAAA;AAED,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,EAAA,OAAA,EAAE,EAAE,OAAO,CAAC,CAAC;AACxD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACO,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAnB,YAAA;QAEI,IAAI,IAAI,CAAC,WAAW,EACpB;AACI,YAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACvC,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;YAE3C,IAAI,GAAG,GAAG,CAAC,EACX;AACI,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACjC,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;AACjC,gBAAA,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAClE,aAAA;AACJ,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;AACjC,YAAA,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,KAAK,CAAC;AACxC,SAAA;KACJ,CAAA;AAED;;;AAGG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QAEI,IAAI,IAAI,CAAC,WAAW,EACpB;YACI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EACtC;AACI,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACjC,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,MAAM,GAAb,UAAc,CAAS,EAAE,CAAS,EAAA;QAE9B,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAE/B,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;AAMG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,MAAM,GAAb,UAAc,CAAS,EAAE,CAAS,EAAA;AAE9B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EACrB;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB,SAAA;;AAGD,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACvC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAExC,QAAA,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAC9B;AACI,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;AACO,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAApB,UAAqB,CAAK,EAAE,CAAK,EAAA;AAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;QAE7B,IAAI,IAAI,CAAC,WAAW,EACpB;YACI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxC;gBACI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,aAAA;AACJ,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;IACI,QAAgB,CAAA,SAAA,CAAA,gBAAA,GAAvB,UAAwB,GAAW,EAAE,GAAW,EAAE,GAAW,EAAE,GAAW,EAAA;QAEtE,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAEvC,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EACvB;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB,SAAA;AAED,QAAA,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAEnD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;;;AASG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,aAAa,GAApB,UAAqB,GAAW,EAAE,GAAW,EAAE,IAAY,EAAE,IAAY,EAAE,GAAW,EAAE,GAAW,EAAA;QAE/F,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAE7E,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;;;;AAUG;IACI,QAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,MAAc,EAAA;AAEvE,QAAA,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAExB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAEvC,QAAA,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAEhE,QAAA,IAAI,MAAM,EACV;AACY,YAAA,IAAA,EAAE,GAAsD,MAAM,CAAA,EAA5D,EAAE,EAAE,GAAkD,MAAM,CAAxD,EAAA,EAAE,QAAM,GAA0C,MAAM,CAAA,MAAhD,EAAE,UAAU,GAA8B,MAAM,CAApC,UAAA,EAAE,QAAQ,GAAoB,MAAM,CAAA,QAA1B,EAAE,aAAa,GAAK,MAAM,cAAX,CAAY;AAEvE,YAAA,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,QAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;AACjE,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;;;;;;AAYG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,GAAG,GAAV,UAAW,EAAU,EAAE,EAAU,EAAE,MAAc,EAAE,UAAkB,EAAE,QAAgB,EAAE,aAAqB,EAAA;AAArB,QAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAAqB,GAAA,KAAA,CAAA,EAAA;QAE1G,IAAI,UAAU,KAAK,QAAQ,EAC3B;AACI,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,IAAI,QAAQ,IAAI,UAAU,EAC5C;YACI,QAAQ,IAAI,IAAI,CAAC;AACpB,SAAA;AACI,aAAA,IAAI,aAAa,IAAI,UAAU,IAAI,QAAQ,EAChD;YACI,UAAU,IAAI,IAAI,CAAC;AACtB,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,CAAC;QAEpC,IAAI,KAAK,KAAK,CAAC,EACf;AACI,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAM,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;AACpD,QAAA,IAAM,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;AACpD,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;;AAGzC,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;AAE/D,QAAA,IAAI,MAAM,EACV;;;AAII,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC3D,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAE3D,YAAA,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,EAC9B,CAGC;AAED,iBAAA;AACI,gBAAA,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC/B,aAAA;AACJ,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC5B,YAAA,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AACpC,SAAA;QAED,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;AAE1F,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;AAMG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UAAiB,KAAS,EAAE,KAAS,EAAA;AAApB,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;AAEjC,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,EAAA,KAAA,EAAE,KAAK,EAAA,KAAA,EAAE,CAAC,CAAC;KAC1E,CAAA;AAED;;;;;;;;AAQG;IACH,QAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,OAA2B,EAAA;;AAGxC,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACpB,OAAO,EAAE,OAAO,CAAC,KAAK;AACtB,YAAA,KAAK,EAAE,QAAQ;AACf,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,MAAM,EAAE,IAAI;SACf,EAAE,OAAO,CAAsB,CAAC;QAEjC,IAAI,IAAI,CAAC,WAAW,EACpB;YACI,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,OAAO,EACZ;AACI,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAC3B,SAAA;AAED,aAAA;YACI,IAAI,OAAO,CAAC,MAAM,EAClB;gBACI,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACxC,gBAAA,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;AAC3B,aAAA;AAED,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,EAAA,OAAA,EAAE,EAAE,OAAO,CAAC,CAAC;AACxD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAExB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;AAOG;IACI,QAAQ,CAAA,SAAA,CAAA,QAAA,GAAf,UAAgB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAA;AAE/D,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;KAC7D,CAAA;AAED;;;;;;;;AAQG;IACI,QAAe,CAAA,SAAA,CAAA,eAAA,GAAtB,UAAuB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,MAAc,EAAA;AAEtF,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;KAC5E,CAAA;AAED;;;;;;AAMG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,UAAkB,CAAS,EAAE,CAAS,EAAE,MAAc,EAAA;AAElD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;KACnD,CAAA;AAED;;;;;;;AAOG;IACI,QAAW,CAAA,SAAA,CAAA,WAAA,GAAlB,UAAmB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAA;AAElE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;KAC3D,CAAA;AAKD;;;;AAIG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,WAAW,GAAlB,YAAA;;AAAA;QAAmB,IAAc,IAAA,GAAA,EAAA,CAAA;aAAd,IAAc,EAAA,GAAA,CAAA,EAAd,EAAc,GAAA,SAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAA;YAAd,IAAc,CAAA,EAAA,CAAA,GAAAA,WAAA,CAAA,EAAA,CAAA,CAAA;;AAE7B,QAAA,IAAI,MAAyC,CAAC;AAC9C,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC;AAEvB,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAY,CAAC;;QAGhC,IAAI,IAAI,CAAC,MAAM,EACf;AACI,YAAA,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AAC/B,YAAA,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,SAAA;aAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAC1B;AACI,YAAA,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,SAAA;AAED,aAAA;YACI,MAAM,GAAG,IAAI,CAAC;AACjB,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;AAElC,QAAA,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;AAEhC,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAEtB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACI,QAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,KAAa,EAAA;AAE1B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EACnB;YACI,IAAI,CAAC,SAAS,CAAC,SAAS,CACpB,KAAK,EACL,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EACvB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EACvB,IAAI,CAAC,OAAO,CACf,CAAC;AACL,SAAA;AAED,aAAA;YACI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAChD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAExB,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAExB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,YAAA;AAEI,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;AAEzC,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC;eACjB,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAClC,eAAA,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;AACf,eAAA,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM;AACrB,eAAA,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KAClE,CAAA;AAED;;;AAGG;IACO,QAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,QAAkB,EAAA;QAEhC,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;;;QAIhC,QAAQ,CAAC,aAAa,EAAE,CAAC;QAEzB,IAAI,QAAQ,CAAC,SAAS,EACtB;AACI,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,UAAU,EAC3C;gBACI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC3B,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACjC,SAAA;AAED,aAAA;;AAEI,YAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAEvB,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChC,SAAA;KACJ,CAAA;;AAGS,IAAA,QAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;AAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACjC,QAAA,IAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;AAEpC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AACtC,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC;QAE1B,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAC5B;YACI,IAAM,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/B,YAAA,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAC7B,IAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtD,EAAE,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,EACtB,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YAEvB,IAAM,GAAG,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EACnD,EAAE,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,EACtB,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YAEvB,IAAM,OAAO,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EACzD,EAAE,CAAC,KAAK,GAAG,CAAC,EACZ,EAAE,CAAC,IAAI,CAAC,CAAC;AAEb,YAAA,IAAM,KAAK,GAAG;AACV,gBAAA,UAAU,EAAA,UAAA;AACV,gBAAA,SAAS,EAAA,SAAA;AACT,gBAAA,OAAO,EAAA,OAAA;AACP,gBAAA,GAAG,EAAA,GAAA;AACH,gBAAA,SAAS,EAAE,OAAO,CAAC,KAAK,CAAkB;AAC1C,gBAAA,QAAQ,EAAE,KAAK;AACf,gBAAA,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO;AAC1B,gBAAA,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK;AACrB,gBAAA,UAAU,EAAE,CAAC;aAAE,CAAC;AAEpB,YAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC3B,SAAA;KACJ,CAAA;AAED;;;AAGG;IACO,QAAc,CAAA,SAAA,CAAA,cAAA,GAAxB,UAAyB,QAAkB,EAAA;AAEvC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EACxB;YACI,OAAO;AACV,SAAA;AAED,QAAA,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAEpE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,EAAE,CAAC;AAEtB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACnD;YACI,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAE9B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;AAEjD,YAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnD,SAAA;KACJ,CAAA;AAED;;;AAGG;IACO,QAAa,CAAA,SAAA,CAAA,aAAA,GAAvB,UAAwB,QAAkB,EAAA;QAEtC,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAEnD,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACnC,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;;QAGrC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;;QAG3D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC;QAC9D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC;AAC7D,QAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC;AACtD,QAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;;;;;AAO9B,QAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;;QAGzC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;AAG/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAChD;AACI,YAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACO,IAAA,QAAA,CAAA,SAAA,CAAA,qBAAqB,GAA/B,UAAgC,QAAkB,EAAE,QAAuB,EAAA;AAE/D,QAAA,IAAA,QAAQ,GAAwB,QAAQ,SAAhC,EAAE,IAAI,GAAkB,QAAQ,CAAA,IAA1B,EAAE,IAAI,GAAY,QAAQ,CAApB,IAAA,EAAE,KAAK,GAAK,QAAQ,MAAb,CAAc;AACjD,QAAA,IAAM,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAAC;QAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAC1C;AACI,YAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,SAAA;QAED,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC7C,CAAA;AAED;;;AAGG;IACO,QAAoB,CAAA,SAAA,CAAA,oBAAA,GAA9B,UAA+B,QAAkB,EAAA;AAE7C,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAEzB,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QAEnC,IAAI,CAAC,MAAM,EACX;;;;AAII,YAAA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAChC;gBACY,IAAA,YAAY,GAAK,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,YAAjC,CAAkC;AACtD,gBAAA,IAAM,YAAY,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC;gBAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EACrC;AACI,oBAAA,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACvB,iBAAA;AAED,gBAAA,IAAM,QAAQ,GAAG;AACb,oBAAA,IAAI,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACpC,iBAAiB,EAAE,IAAI,MAAM,EAAE;AAC/B,oBAAA,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE,IAAI,CAAC;iBAChE,CAAC;AAEF,gBAAA,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;gBAE7D,eAAe,CAAC,UAAU,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC/D,aAAA;AAED,YAAA,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;AACxC,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;;AAGS,IAAA,QAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;QAEI,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;;AAGhC,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,EACjC;YACI,OAAO;AACV,SAAA;AAEK,QAAA,IAAA,KAA6B,QAAQ,CAAC,MAAM,EAA1C,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,IAAI,UAAoB,CAAC;AAEnD,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACjE,CAAA;AAED;;;;AAIG;IACI,QAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,KAAiB,EAAA;QAElC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;KAC7D,CAAA;;AAGS,IAAA,QAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI,EAChC;AACI,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;YAE3B,IAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAEzC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAC5C;gBACI,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAE9B,gBAAA,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAElC,gBAAA,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AAC5C,gBAAA,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AAC5C,gBAAA,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;;AAG5C,gBAAA,IAAM,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAE7C,gBAAA,KAAK,CAAC,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE;uBAClB,KAAK,GAAG,MAAM,CAAC;uBACf,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;AACpC,aAAA;AACJ,SAAA;KACJ,CAAA;;AAGS,IAAA,QAAA,CAAA,SAAA,CAAA,iBAAiB,GAA3B,YAAA;AAEI,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AAErC,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAC9B;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAEzB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;AACzC,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACjB,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAEjB,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACnC,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QAEnC,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EACvC;AACI,YAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEtB,YAAA,UAAU,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAC7C,YAAA,UAAU,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAChD,SAAA;KACJ,CAAA;AAED;;;AAGG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,YAAA;AAEI,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AAErC,QAAA,IAAI,WAAW,EACf;;AAEI,YAAA,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;;;YAG/B,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACI,QAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,MAAc,EAAA;AAE3B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AAEtB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;AAOG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,YAAA;QAEI,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAEtB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACI,IAAA,QAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAEvB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;;;;AAUG;IACI,QAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;AAE9C,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,CAAC,EACjC;AACI,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;AAC5B,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAEpB,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;KAC1B,CAAA;AArmCD;;;AAGG;IACW,QAAuB,CAAA,uBAAA,GAAG,KAAK,CAAC;AAE9C;;;AAGG;AACI,IAAA,QAAA,CAAA,WAAW,GAAG,IAAI,KAAK,EAAE,CAAC;IA4lCrC,OAAC,QAAA,CAAA;CAAA,CAxmC6B,SAAS,CAwmCtC;;AClqCM,IAAM,aAAa,GAAG;AACzB,IAAA,SAAS,EAAE,SAA+B;AAC1C,IAAA,WAAW,EAAE,WAAiC;AAC9C,IAAA,cAAc,EAAE,cAAoC;AACpD,IAAA,qBAAqB,EAAE,qBAA2C;AAClE,IAAA,SAAS,EAAA,SAAA;AACT,IAAA,QAAQ,EAAA,QAAA;AACR,IAAA,WAAW,EAAA,WAAA;AACX,IAAA,cAAc,EAAA,cAAA;AACd,IAAA,SAAS,EAAA,SAAA;AACT,IAAA,aAAa,EAAE,aAAmD;AAClE,IAAA,UAAU,EAAE,UAA8B;AAC1C,IAAA,cAAc,EAAE,cAAsC;;;;;"}