{"version": 3, "file": "graphics.min.js", "sources": ["../../src/const.ts", "../../src/styles/FillStyle.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/utils/buildPoly.ts", "../../src/utils/buildCircle.ts", "../../src/utils/buildRectangle.ts", "../../src/utils/buildRoundedRectangle.ts", "../../src/utils/buildLine.ts", "../../src/utils/ArcUtils.ts", "../../src/utils/BezierUtils.ts", "../../src/utils/QuadraticUtils.ts", "../../src/utils/BatchPart.ts", "../../src/utils/index.ts", "../../src/GraphicsData.ts", "../../src/GraphicsGeometry.ts", "../../src/styles/LineStyle.ts", "../../src/Graphics.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * Supported line joints in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @see https://graphicdesign.stackexchange.com/questions/59018/what-is-a-bevel-join-of-two-lines-exactly-illustrator\n * @name LINE_JOIN\n * @memberof PIXI\n * @static\n * @enum {string}\n * @property {string} MITER - 'miter': make a sharp corner where outer part of lines meet\n * @property {string} BEVEL - 'bevel': add a square butt at each end of line segment and fill the triangle at turn\n * @property {string} ROUND - 'round': add an arc at the joint\n */\nexport enum LINE_JOIN\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    MITER = 'miter',\n    BEVEL = 'bevel',\n    ROUND = 'round'\n}\n\n/**\n * Support line caps in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @name LINE_CAP\n * @memberof PIXI\n * @static\n * @enum {string}\n * @property {string} BUTT - 'butt': don't add any cap at line ends (leaves orthogonal edges)\n * @property {string} ROUND - 'round': add semicircle at ends\n * @property {string} SQUARE - 'square': add square at end (like `BUTT` except more length at end)\n */\nexport enum LINE_CAP\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    BUTT = 'butt',\n    ROUND = 'round',\n    SQUARE = 'square'\n}\n\nexport interface IGraphicsCurvesSettings\n{\n    adaptive: boolean;\n    maxLength: number;\n    minSegments: number;\n    maxSegments: number;\n\n    epsilon: number;\n\n    _segmentsCount(length: number, defaultSegments?: number): number;\n}\n\n/**\n * Graphics curves resolution settings. If `adaptive` flag is set to `true`,\n * the resolution is calculated based on the curve's length to ensure better visual quality.\n * Adaptive draw works with `bezierCurveTo` and `quadraticCurveTo`.\n * @static\n * @constant\n * @memberof PIXI\n * @name GRAPHICS_CURVES\n * @type {object}\n * @property {boolean} [adaptive=true] - flag indicating if the resolution should be adaptive\n * @property {number} [maxLength=10] - maximal length of a single segment of the curve (if adaptive = false, ignored)\n * @property {number} [minSegments=8] - minimal number of segments in the curve (if adaptive = false, ignored)\n * @property {number} [maxSegments=2048] - maximal number of segments in the curve (if adaptive = false, ignored)\n */\nexport const GRAPHICS_CURVES: IGraphicsCurvesSettings = {\n    adaptive: true,\n    maxLength: 10,\n    minSegments: 8,\n    maxSegments:  2048,\n\n    epsilon: 0.0001,\n\n    _segmentsCount(length: number, defaultSegments = 20)\n    {\n        if (!this.adaptive || !length || isNaN(length))\n        {\n            return defaultSegments;\n        }\n\n        let result = Math.ceil(length / this.maxLength);\n\n        if (result < this.minSegments)\n        {\n            result = this.minSegments;\n        }\n        else if (result > this.maxSegments)\n        {\n            result = this.maxSegments;\n        }\n\n        return result;\n    },\n};\n", "import { Texture } from '@pixi/core';\nimport type { Matrix } from '@pixi/math';\n\n/**\n * Fill style object for Graphics.\n * @memberof PIXI\n */\nexport class FillStyle\n{\n    /**\n     * The hex color value used when coloring the Graphics object.\n     * @default 0xFFFFFF\n     */\n    public color = 0xFFFFFF;\n\n    /** The alpha value used when filling the Graphics object. */\n    public alpha = 1.0;\n\n    /**\n     * The texture to be used for the fill.\n     * @default 0\n     */\n    public texture: Texture = Texture.WHITE;\n\n    /**\n     * The transform applied to the texture.\n     * @default null\n     */\n    public matrix: Matrix = null;\n\n    /** If the current fill is visible. */\n    public visible = false;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /** Clones the object */\n    public clone(): FillStyle\n    {\n        const obj = new FillStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n\n        return obj;\n    }\n\n    /** Reset */\n    public reset(): void\n    {\n        this.color = 0xFFFFFF;\n        this.alpha = 1;\n        this.texture = Texture.WHITE;\n        this.matrix = null;\n        this.visible = false;\n    }\n\n    /** Destroy and don't use after this. */\n    public destroy(): void\n    {\n        this.texture = null;\n        this.matrix = null;\n    }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { earcut } from '@pixi/utils';\n\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { Polygon } from '@pixi/math';\n\nfunction fixOrientation(points: number[], hole = false)\n{\n    const m = points.length;\n\n    if (m < 6)\n    {\n        return;\n    }\n\n    let area = 0;\n\n    for (let i = 0, x1 = points[m - 2], y1 = points[m - 1]; i < m; i += 2)\n    {\n        const x2 = points[i];\n        const y2 = points[i + 1];\n\n        area += (x2 - x1) * (y2 + y1);\n\n        x1 = x2;\n        y1 = y2;\n    }\n\n    if ((!hole && area > 0) || (hole && area <= 0))\n    {\n        const n = m / 2;\n\n        for (let i = n + (n % 2); i < m; i += 2)\n        {\n            const i1 = m - i - 2;\n            const i2 = m - i - 1;\n            const i3 = i;\n            const i4 = i + 1;\n\n            [points[i1], points[i3]] = [points[i3], points[i1]];\n            [points[i2], points[i4]] = [points[i4], points[i2]];\n        }\n    }\n}\n/**\n * Builds a polygon to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildPoly: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        graphicsData.points = (graphicsData.shape as Polygon).points.slice();\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        let points = graphicsData.points;\n        const holes = graphicsData.holes;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length >= 6)\n        {\n            fixOrientation(points, false);\n\n            const holeArray = [];\n            // Process holes..\n\n            for (let i = 0; i < holes.length; i++)\n            {\n                const hole = holes[i];\n\n                fixOrientation(hole.points, true);\n\n                holeArray.push(points.length / 2);\n                points = points.concat(hole.points);\n            }\n\n            // sort color\n            const triangles = earcut(points, holeArray, 2);\n\n            if (!triangles)\n            {\n                return;\n            }\n\n            const vertPos = verts.length / 2;\n\n            for (let i = 0; i < triangles.length; i += 3)\n            {\n                indices.push(triangles[i] + vertPos);\n                indices.push(triangles[i + 1] + vertPos);\n                indices.push(triangles[i + 2] + vertPos);\n            }\n\n            for (let i = 0; i < points.length; i++)\n            {\n                verts.push(points[i]);\n            }\n        }\n    },\n};\n", "// for type only\nimport { SHAPES } from '@pixi/math';\n\nimport type { Circle, Ellipse, RoundedRectangle } from '@pixi/math';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Builds a circle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object to draw\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildCircle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        // need to convert points to a nice regular data\n        const points = graphicsData.points;\n\n        let x;\n        let y;\n        let dx;\n        let dy;\n        let rx;\n        let ry;\n\n        if (graphicsData.type === SHAPES.CIRC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n            rx = ry = circle.radius;\n            dx = dy = 0;\n        }\n        else if (graphicsData.type === SHAPES.ELIP)\n        {\n            const ellipse = graphicsData.shape as Ellipse;\n\n            x = ellipse.x;\n            y = ellipse.y;\n            rx = ellipse.width;\n            ry = ellipse.height;\n            dx = dy = 0;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n            const halfWidth = roundedRect.width / 2;\n            const halfHeight = roundedRect.height / 2;\n\n            x = roundedRect.x + halfWidth;\n            y = roundedRect.y + halfHeight;\n            rx = ry = Math.max(0, Math.min(roundedRect.radius, Math.min(halfWidth, halfHeight)));\n            dx = halfWidth - rx;\n            dy = halfHeight - ry;\n        }\n\n        if (!(rx >= 0 && ry >= 0 && dx >= 0 && dy >= 0))\n        {\n            points.length = 0;\n\n            return;\n        }\n\n        // Choose a number of segments such that the maximum absolute deviation from the circle is approximately 0.029\n        const n = Math.ceil(2.3 * Math.sqrt(rx + ry));\n        const m = (n * 8) + (dx ? 4 : 0) + (dy ? 4 : 0);\n\n        points.length = m;\n\n        if (m === 0)\n        {\n            return;\n        }\n\n        if (n === 0)\n        {\n            points.length = 8;\n            points[0] = points[6] = x + dx;\n            points[1] = points[3] = y + dy;\n            points[2] = points[4] = x - dx;\n            points[5] = points[7] = y - dy;\n\n            return;\n        }\n\n        let j1 = 0;\n        let j2 = (n * 4) + (dx ? 2 : 0) + 2;\n        let j3 = j2;\n        let j4 = m;\n\n        {\n            const x0 = dx + rx;\n            const y0 = dy;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n\n            if (dy)\n            {\n                const y2 = y - y0;\n\n                points[j3++] = x2;\n                points[j3++] = y2;\n                points[--j4] = y2;\n                points[--j4] = x1;\n            }\n        }\n\n        for (let i = 1; i < n; i++)\n        {\n            const a = Math.PI / 2 * (i / n);\n            const x0 = dx + (Math.cos(a) * rx);\n            const y0 = dy + (Math.sin(a) * ry);\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n            points[j3++] = x2;\n            points[j3++] = y2;\n            points[--j4] = y2;\n            points[--j4] = x1;\n        }\n\n        {\n            const x0 = dx;\n            const y0 = dy + ry;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j4] = y2;\n            points[--j4] = x1;\n\n            if (dx)\n            {\n                points[j1++] = x2;\n                points[j1++] = y1;\n                points[--j4] = y2;\n                points[--j4] = x2;\n            }\n        }\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        const points = graphicsData.points;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length === 0)\n        {\n            return;\n        }\n\n        let vertPos = verts.length / 2;\n        const center = vertPos;\n\n        let x;\n        let y;\n\n        if (graphicsData.type !== SHAPES.RREC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n\n            x = roundedRect.x + (roundedRect.width / 2);\n            y = roundedRect.y + (roundedRect.height / 2);\n        }\n\n        const matrix = graphicsData.matrix;\n\n        // Push center (special point)\n        verts.push(\n            graphicsData.matrix ? (matrix.a * x) + (matrix.c * y) + matrix.tx : x,\n            graphicsData.matrix ? (matrix.b * x) + (matrix.d * y) + matrix.ty : y);\n\n        vertPos++;\n\n        verts.push(points[0], points[1]);\n\n        for (let i = 2; i < points.length; i += 2)\n        {\n            verts.push(points[i], points[i + 1]);\n\n            // add some uvs\n            indices.push(vertPos++, center, vertPos);\n        }\n\n        indices.push(center + 1, center, vertPos);\n    },\n};\n", "import type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { Rectangle } from '@pixi/math';\n\n/**\n * Builds a rectangle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildRectangle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        // --- //\n        // need to convert points to a nice regular data\n        //\n        const rectData = graphicsData.shape as Rectangle;\n        const x = rectData.x;\n        const y = rectData.y;\n        const width = rectData.width;\n        const height = rectData.height;\n\n        const points = graphicsData.points;\n\n        points.length = 0;\n\n        points.push(x, y,\n            x + width, y,\n            x + width, y + height,\n            x, y + height);\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        const points = graphicsData.points;\n        const verts = graphicsGeometry.points;\n\n        const vertPos = verts.length / 2;\n\n        verts.push(points[0], points[1],\n            points[2], points[3],\n            points[6], points[7],\n            points[4], points[5]);\n\n        graphicsGeometry.indices.push(vertPos, vertPos + 1, vertPos + 2,\n            vertPos + 1, vertPos + 2, vertPos + 3);\n    },\n};\n", "import { earcut } from '@pixi/utils';\n\n// for type only\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { RoundedRectangle } from '@pixi/math';\nimport { Graphics } from '../Graphics';\nimport { buildCircle } from './buildCircle';\n\n/**\n * Calculate a single point for a quadratic bezier curve.\n * Utility function used by quadraticBezierCurve.\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} n1 - first number\n * @param {number} n2 - second number\n * @param {number} perc - percentage\n * @returns {number} the result\n */\nfunction getPt(n1: number, n2: number, perc: number): number\n{\n    const diff = n2 - n1;\n\n    return n1 + (diff * perc);\n}\n\n/**\n * Calculate the points for a quadratic bezier curve. (helper function..)\n * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} fromX - Origin point x\n * @param {number} fromY - Origin point x\n * @param {number} cpX - Control point x\n * @param {number} cpY - Control point y\n * @param {number} toX - Destination point x\n * @param {number} toY - Destination point y\n * @param {number[]} [out=[]] - The output array to add points into. If not passed, a new array is created.\n * @returns {number[]} an array of points\n */\nfunction quadraticBezierCurve(\n    fromX: number, fromY: number,\n    cpX: number, cpY: number,\n    toX: number, toY: number,\n    out: Array<number> = []): Array<number>\n{\n    const n = 20;\n    const points = out;\n\n    let xa = 0;\n    let ya = 0;\n    let xb = 0;\n    let yb = 0;\n    let x = 0;\n    let y = 0;\n\n    for (let i = 0, j = 0; i <= n; ++i)\n    {\n        j = i / n;\n\n        // The Green Line\n        xa = getPt(fromX, cpX, j);\n        ya = getPt(fromY, cpY, j);\n        xb = getPt(cpX, toX, j);\n        yb = getPt(cpY, toY, j);\n\n        // The Black Dot\n        x = getPt(xa, xb, j);\n        y = getPt(ya, yb, j);\n\n        // Handle case when first curve points overlaps and earcut fails to triangulate\n        if (i === 0 && points[points.length - 2] === x && points[points.length - 1] === y)\n        {\n            continue;\n        }\n\n        points.push(x, y);\n    }\n\n    return points;\n}\n\n/**\n * Builds a rounded rectangle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildRoundedRectangle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        if (Graphics.nextRoundedRectBehavior)\n        {\n            buildCircle.build(graphicsData);\n\n            return;\n        }\n\n        const rrectData = graphicsData.shape as RoundedRectangle;\n        const points = graphicsData.points;\n        const x = rrectData.x;\n        const y = rrectData.y;\n        const width = rrectData.width;\n        const height = rrectData.height;\n\n        // Don't allow negative radius or greater than half the smallest width\n        const radius = Math.max(0, Math.min(rrectData.radius, Math.min(width, height) / 2));\n\n        points.length = 0;\n\n        // No radius, do a simple rectangle\n        if (!radius)\n        {\n            points.push(x, y,\n                x + width, y,\n                x + width, y + height,\n                x, y + height);\n        }\n        else\n        {\n            quadraticBezierCurve(x, y + radius,\n                x, y,\n                x + radius, y,\n                points);\n            quadraticBezierCurve(x + width - radius,\n                y, x + width, y,\n                x + width, y + radius,\n                points);\n            quadraticBezierCurve(x + width, y + height - radius,\n                x + width, y + height,\n                x + width - radius, y + height,\n                points);\n            quadraticBezierCurve(x + radius, y + height,\n                x, y + height,\n                x, y + height - radius,\n                points);\n        }\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        if (Graphics.nextRoundedRectBehavior)\n        {\n            buildCircle.triangulate(graphicsData, graphicsGeometry);\n\n            return;\n        }\n\n        const points = graphicsData.points;\n\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        const vecPos = verts.length / 2;\n\n        const triangles = earcut(points, null, 2);\n\n        for (let i = 0, j = triangles.length; i < j; i += 3)\n        {\n            indices.push(triangles[i] + vecPos);\n            //     indices.push(triangles[i] + vecPos);\n            indices.push(triangles[i + 1] + vecPos);\n            //   indices.push(triangles[i + 2] + vecPos);\n            indices.push(triangles[i + 2] + vecPos);\n        }\n\n        for (let i = 0, j = points.length; i < j; i++)\n        {\n            verts.push(points[i], points[++i]);\n        }\n    },\n};\n", "import { Point, SHAPES } from '@pixi/math';\n\nimport type { Polygon } from '@pixi/math';\nimport type { GraphicsData } from '../GraphicsData';\nimport type { GraphicsGeometry } from '../GraphicsGeometry';\nimport { LINE_JOIN, LINE_CAP, GRAPHICS_CURVES } from '../const';\n\n/**\n * Buffers vertices to draw a square cap.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} x - X-coord of end point\n * @param {number} y - Y-coord of end point\n * @param {number} nx - X-coord of line normal pointing inside\n * @param {number} ny - Y-coord of line normal pointing inside\n * @param {number} innerWeight - Weight of inner points\n * @param {number} outerWeight - Weight of outer points\n * @param {boolean} clockwise - Whether the cap is drawn clockwise\n * @param {Array<number>} verts - vertex buffer\n * @returns {number} - no. of vertices pushed\n */\nfunction square(\n    x: number,\n    y: number,\n    nx: number,\n    ny: number,\n    innerWeight: number,\n    outerWeight: number,\n    clockwise: boolean, /* rotation for square (true at left end, false at right end) */\n    verts: Array<number>\n): number\n{\n    const ix = x - (nx * innerWeight);\n    const iy = y - (ny * innerWeight);\n    const ox = x + (nx * outerWeight);\n    const oy = y + (ny * outerWeight);\n\n    /* Rotate nx,ny for extension vector */\n    let exx; let\n        eyy;\n\n    if (clockwise)\n    {\n        exx = ny;\n        eyy = -nx;\n    }\n    else\n    {\n        exx = -ny;\n        eyy = nx;\n    }\n\n    /* [i|0]x,y extended at cap */\n    const eix = ix + exx;\n    const eiy = iy + eyy;\n    const eox = ox + exx;\n    const eoy = oy + eyy;\n\n    /* Square itself must be inserted clockwise*/\n    verts.push(eix, eiy);\n    verts.push(eox, eoy);\n\n    return 2;\n}\n\n/**\n * Buffers vertices to draw an arc at the line joint or cap.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} cx - X-coord of center\n * @param {number} cy - Y-coord of center\n * @param {number} sx - X-coord of arc start\n * @param {number} sy - Y-coord of arc start\n * @param {number} ex - X-coord of arc end\n * @param {number} ey - Y-coord of arc end\n * @param {Array<number>} verts - buffer of vertices\n * @param {boolean} clockwise - orientation of vertices\n * @returns {number} - no. of vertices pushed\n */\nfunction round(\n    cx: number,\n    cy: number,\n    sx: number,\n    sy: number,\n    ex: number,\n    ey: number,\n    verts: Array<number>,\n    clockwise: boolean, /* if not cap, then clockwise is turn of joint, otherwise rotation from angle0 to angle1 */\n): number\n{\n    const cx2p0x = sx - cx;\n    const cy2p0y = sy - cy;\n\n    let angle0 = Math.atan2(cx2p0x, cy2p0y);\n    let angle1 = Math.atan2(ex - cx, ey - cy);\n\n    if (clockwise && angle0 < angle1)\n    {\n        angle0 += Math.PI * 2;\n    }\n    else if (!clockwise && angle0 > angle1)\n    {\n        angle1 += Math.PI * 2;\n    }\n\n    let startAngle = angle0;\n    const angleDiff = angle1 - angle0;\n    const absAngleDiff = Math.abs(angleDiff);\n\n    /* if (absAngleDiff >= PI_LBOUND && absAngleDiff <= PI_UBOUND)\n    {\n        const r1x = cx - nxtPx;\n        const r1y = cy - nxtPy;\n\n        if (r1x === 0)\n        {\n            if (r1y > 0)\n            {\n                angleDiff = -angleDiff;\n            }\n        }\n        else if (r1x >= -GRAPHICS_CURVES.epsilon)\n        {\n            angleDiff = -angleDiff;\n        }\n    }*/\n\n    const radius = Math.sqrt((cx2p0x * cx2p0x) + (cy2p0y * cy2p0y));\n    const segCount = ((15 * absAngleDiff * Math.sqrt(radius) / Math.PI) >> 0) + 1;\n    const angleInc = angleDiff / segCount;\n\n    startAngle += angleInc;\n\n    if (clockwise)\n    {\n        verts.push(cx, cy);\n        verts.push(sx, sy);\n\n        for (let i = 1, angle = startAngle; i < segCount; i++, angle += angleInc)\n        {\n            verts.push(cx, cy);\n            verts.push(cx + ((Math.sin(angle) * radius)),\n                cy + ((Math.cos(angle) * radius)));\n        }\n\n        verts.push(cx, cy);\n        verts.push(ex, ey);\n    }\n    else\n    {\n        verts.push(sx, sy);\n        verts.push(cx, cy);\n\n        for (let i = 1, angle = startAngle; i < segCount; i++, angle += angleInc)\n        {\n            verts.push(cx + ((Math.sin(angle) * radius)),\n                cy + ((Math.cos(angle) * radius)));\n            verts.push(cx, cy);\n        }\n\n        verts.push(ex, ey);\n        verts.push(cx, cy);\n    }\n\n    return segCount * 2;\n}\n\n/**\n * Builds a line to draw using the polygon method.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nfunction buildNonNativeLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    const shape = graphicsData.shape as Polygon;\n    let points = graphicsData.points || shape.points.slice();\n    const eps = graphicsGeometry.closePointEps;\n\n    if (points.length === 0)\n    {\n        return;\n    }\n    // if the line width is an odd number add 0.5 to align to a whole pixel\n    // commenting this out fixes #711 and #1620\n    // if (graphicsData.lineWidth%2)\n    // {\n    //     for (i = 0; i < points.length; i++)\n    //     {\n    //         points[i] += 0.5;\n    //     }\n    // }\n\n    const style = graphicsData.lineStyle;\n\n    // get first and last point.. figure out the middle!\n    const firstPoint = new Point(points[0], points[1]);\n    const lastPoint = new Point(points[points.length - 2], points[points.length - 1]);\n    const closedShape = shape.type !== SHAPES.POLY || shape.closeStroke;\n    const closedPath = Math.abs(firstPoint.x - lastPoint.x) < eps\n        && Math.abs(firstPoint.y - lastPoint.y) < eps;\n\n    // if the first point is the last point - gonna have issues :)\n    if (closedShape)\n    {\n        // need to clone as we are going to slightly modify the shape..\n        points = points.slice();\n\n        if (closedPath)\n        {\n            points.pop();\n            points.pop();\n            lastPoint.set(points[points.length - 2], points[points.length - 1]);\n        }\n\n        const midPointX = (firstPoint.x + lastPoint.x) * 0.5;\n        const midPointY = (lastPoint.y + firstPoint.y) * 0.5;\n\n        points.unshift(midPointX, midPointY);\n        points.push(midPointX, midPointY);\n    }\n\n    const verts = graphicsGeometry.points;\n    const length = points.length / 2;\n    let indexCount = points.length;\n    const indexStart = verts.length / 2;\n\n    // Max. inner and outer width\n    const width = style.width / 2;\n    const widthSquared = width * width;\n    const miterLimitSquared = style.miterLimit * style.miterLimit;\n\n    /* Line segments of interest where (x1,y1) forms the corner. */\n    let x0 = points[0];\n    let y0 = points[1];\n    let x1 = points[2];\n    let y1 = points[3];\n    let x2 = 0;\n    let y2 = 0;\n\n    /* perp[?](x|y) = the line normal with magnitude lineWidth. */\n    let perpx = -(y0 - y1);\n    let perpy = x0 - x1;\n    let perp1x = 0;\n    let perp1y = 0;\n\n    let dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n\n    perpx /= dist;\n    perpy /= dist;\n    perpx *= width;\n    perpy *= width;\n\n    const ratio = style.alignment;// 0.5;\n    const innerWeight = (1 - ratio) * 2;\n    const outerWeight = ratio * 2;\n\n    if (!closedShape)\n    {\n        if (style.cap === LINE_CAP.ROUND)\n        {\n            indexCount += round(\n                x0 - (perpx * (innerWeight - outerWeight) * 0.5),\n                y0 - (perpy * (innerWeight - outerWeight) * 0.5),\n                x0 - (perpx * innerWeight),\n                y0 - (perpy * innerWeight),\n                x0 + (perpx * outerWeight),\n                y0 + (perpy * outerWeight),\n                verts,\n                true,\n            ) + 2;\n        }\n        else if (style.cap === LINE_CAP.SQUARE)\n        {\n            indexCount += square(x0, y0, perpx, perpy, innerWeight, outerWeight, true, verts);\n        }\n    }\n\n    // Push first point (below & above vertices)\n    verts.push(\n        x0 - (perpx * innerWeight),\n        y0 - (perpy * innerWeight));\n    verts.push(\n        x0 + (perpx * outerWeight),\n        y0 + (perpy * outerWeight));\n\n    for (let i = 1; i < length - 1; ++i)\n    {\n        x0 = points[(i - 1) * 2];\n        y0 = points[((i - 1) * 2) + 1];\n\n        x1 = points[i * 2];\n        y1 = points[(i * 2) + 1];\n\n        x2 = points[(i + 1) * 2];\n        y2 = points[((i + 1) * 2) + 1];\n\n        perpx = -(y0 - y1);\n        perpy = x0 - x1;\n\n        dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n        perpx /= dist;\n        perpy /= dist;\n        perpx *= width;\n        perpy *= width;\n\n        perp1x = -(y1 - y2);\n        perp1y = x1 - x2;\n\n        dist = Math.sqrt((perp1x * perp1x) + (perp1y * perp1y));\n        perp1x /= dist;\n        perp1y /= dist;\n        perp1x *= width;\n        perp1y *= width;\n\n        /* d[x|y](0|1) = the component displacement between points p(0,1|1,2) */\n        const dx0 = x1 - x0;\n        const dy0 = y0 - y1;\n        const dx1 = x1 - x2;\n        const dy1 = y2 - y1;\n\n        /* +ve if internal angle < 90 degree, -ve if internal angle > 90 degree. */\n        const dot = (dx0 * dx1) + (dy0 * dy1);\n        /* +ve if internal angle counterclockwise, -ve if internal angle clockwise. */\n        const cross = (dy0 * dx1) - (dy1 * dx0);\n        const clockwise = (cross < 0);\n\n        /* Going nearly parallel? */\n        /* atan(0.001) ~= 0.001 rad ~= 0.057 degree */\n        if (Math.abs(cross) < 0.001 * Math.abs(dot))\n        {\n            verts.push(\n                x1 - (perpx * innerWeight),\n                y1 - (perpy * innerWeight));\n            verts.push(\n                x1 + (perpx * outerWeight),\n                y1 + (perpy * outerWeight));\n\n            /* 180 degree corner? */\n            if (dot >= 0)\n            {\n                if (style.join === LINE_JOIN.ROUND)\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false) + 4;\n                }\n                else\n                {\n                    indexCount += 2;\n                }\n\n                verts.push(\n                    x1 - (perp1x * outerWeight),\n                    y1 - (perp1y * outerWeight));\n                verts.push(\n                    x1 + (perp1x * innerWeight),\n                    y1 + (perp1y * innerWeight));\n            }\n\n            continue;\n        }\n\n        /* p[x|y] is the miter point. pdist is the distance between miter point and p1. */\n        const c1 = ((-perpx + x0) * (-perpy + y1)) - ((-perpx + x1) * (-perpy + y0));\n        const c2 = ((-perp1x + x2) * (-perp1y + y1)) - ((-perp1x + x1) * (-perp1y + y2));\n        const px = ((dx0 * c2) - (dx1 * c1)) / cross;\n        const py = ((dy1 * c1) - (dy0 * c2)) / cross;\n        const pdist = ((px - x1) * (px - x1)) + ((py - y1) * (py - y1));\n\n        /* Inner miter point */\n        const imx = x1 + ((px - x1) * innerWeight);\n        const imy = y1 + ((py - y1) * innerWeight);\n        /* Outer miter point */\n        const omx = x1 - ((px - x1) * outerWeight);\n        const omy = y1 - ((py - y1) * outerWeight);\n\n        /* Is the inside miter point too far away, creating a spike? */\n        const smallerInsideSegmentSq = Math.min((dx0 * dx0) + (dy0 * dy0), (dx1 * dx1) + (dy1 * dy1));\n        const insideWeight = clockwise ? innerWeight : outerWeight;\n        const smallerInsideDiagonalSq = smallerInsideSegmentSq + (insideWeight * insideWeight * widthSquared);\n        const insideMiterOk = pdist <= smallerInsideDiagonalSq;\n\n        if (insideMiterOk)\n        {\n            if (style.join === LINE_JOIN.BEVEL || pdist / widthSquared > miterLimitSquared)\n            {\n                if (clockwise) /* rotating at inner angle */\n                {\n                    verts.push(imx, imy); // inner miter point\n                    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight)); // first segment's outer vertex\n                    verts.push(imx, imy); // inner miter point\n                    verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight)); // second segment's outer vertex\n                }\n                else /* rotating at outer angle */\n                {\n                    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight)); // first segment's inner vertex\n                    verts.push(omx, omy); // outer miter point\n                    verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight)); // second segment's outer vertex\n                    verts.push(omx, omy); // outer miter point\n                }\n\n                indexCount += 2;\n            }\n            else if (style.join === LINE_JOIN.ROUND)\n            {\n                if (clockwise) /* arc is outside */\n                {\n                    verts.push(imx, imy);\n                    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight));\n\n                    indexCount += round(\n                        x1, y1,\n                        x1 + (perpx * outerWeight), y1 + (perpy * outerWeight),\n                        x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight),\n                        verts, true\n                    ) + 4;\n\n                    verts.push(imx, imy);\n                    verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight));\n                }\n                else /* arc is inside */\n                {\n                    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight));\n                    verts.push(omx, omy);\n\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false\n                    ) + 4;\n\n                    verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight));\n                    verts.push(omx, omy);\n                }\n            }\n            else\n            {\n                verts.push(imx, imy);\n                verts.push(omx, omy);\n            }\n        }\n        else // inside miter is NOT ok\n        {\n            verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight)); // first segment's inner vertex\n            verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight)); // first segment's outer vertex\n            if (style.join === LINE_JOIN.ROUND)\n            {\n                if (clockwise) /* arc is outside */\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 + (perpx * outerWeight), y1 + (perpy * outerWeight),\n                        x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight),\n                        verts, true\n                    ) + 2;\n                }\n                else /* arc is inside */\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false\n                    ) + 2;\n                }\n            }\n            else if (style.join === LINE_JOIN.MITER && pdist / widthSquared <= miterLimitSquared)\n            {\n                if (clockwise)\n                {\n                    verts.push(omx, omy); // inner miter point\n                    verts.push(omx, omy); // inner miter point\n                }\n                else\n                {\n                    verts.push(imx, imy); // outer miter point\n                    verts.push(imx, imy); // outer miter point\n                }\n                indexCount += 2;\n            }\n            verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight)); // second segment's inner vertex\n            verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight)); // second segment's outer vertex\n            indexCount += 2;\n        }\n    }\n\n    x0 = points[(length - 2) * 2];\n    y0 = points[((length - 2) * 2) + 1];\n\n    x1 = points[(length - 1) * 2];\n    y1 = points[((length - 1) * 2) + 1];\n\n    perpx = -(y0 - y1);\n    perpy = x0 - x1;\n\n    dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n    perpx /= dist;\n    perpy /= dist;\n    perpx *= width;\n    perpy *= width;\n\n    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight));\n    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight));\n\n    if (!closedShape)\n    {\n        if (style.cap === LINE_CAP.ROUND)\n        {\n            indexCount += round(\n                x1 - (perpx * (innerWeight - outerWeight) * 0.5),\n                y1 - (perpy * (innerWeight - outerWeight) * 0.5),\n                x1 - (perpx * innerWeight),\n                y1 - (perpy * innerWeight),\n                x1 + (perpx * outerWeight),\n                y1 + (perpy * outerWeight),\n                verts,\n                false\n            ) + 2;\n        }\n        else if (style.cap === LINE_CAP.SQUARE)\n        {\n            indexCount += square(x1, y1, perpx, perpy, innerWeight, outerWeight, false, verts);\n        }\n    }\n\n    const indices = graphicsGeometry.indices;\n    const eps2 = GRAPHICS_CURVES.epsilon * GRAPHICS_CURVES.epsilon;\n\n    // indices.push(indexStart);\n    for (let i = indexStart; i < indexCount + indexStart - 2; ++i)\n    {\n        x0 = verts[(i * 2)];\n        y0 = verts[(i * 2) + 1];\n\n        x1 = verts[(i + 1) * 2];\n        y1 = verts[((i + 1) * 2) + 1];\n\n        x2 = verts[(i + 2) * 2];\n        y2 = verts[((i + 2) * 2) + 1];\n\n        /* Skip zero area triangles */\n        if (Math.abs((x0 * (y1 - y2)) + (x1 * (y2 - y0)) + (x2 * (y0 - y1))) < eps2)\n        {\n            continue;\n        }\n\n        indices.push(i, i + 1, i + 2);\n    }\n}\n\n/**\n * Builds a line to draw using the gl.drawArrays(gl.LINES) method\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nfunction buildNativeLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    let i = 0;\n\n    const shape = graphicsData.shape as Polygon;\n    const points = graphicsData.points || shape.points;\n    const closedShape = shape.type !== SHAPES.POLY || shape.closeStroke;\n\n    if (points.length === 0) return;\n\n    const verts = graphicsGeometry.points;\n    const indices = graphicsGeometry.indices;\n    const length = points.length / 2;\n\n    const startIndex = verts.length / 2;\n    let currentIndex = startIndex;\n\n    verts.push(points[0], points[1]);\n\n    for (i = 1; i < length; i++)\n    {\n        verts.push(points[i * 2], points[(i * 2) + 1]);\n        indices.push(currentIndex, currentIndex + 1);\n\n        currentIndex++;\n    }\n\n    if (closedShape)\n    {\n        indices.push(currentIndex, startIndex);\n    }\n}\n\n/**\n * Builds a line to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nexport function buildLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    if (graphicsData.lineStyle.native)\n    {\n        buildNativeLine(graphicsData, graphicsGeometry);\n    }\n    else\n    {\n        buildNonNativeLine(graphicsData, graphicsGeometry);\n    }\n}\n", "import { GRAPHICS_CURVES } from '../const';\nimport { PI_2 } from '@pixi/math';\n\ninterface IArcLikeShape\n{\n    cx: number;\n    cy: number;\n    radius: number;\n    startAngle: number;\n    endAngle: number;\n    anticlockwise: boolean;\n}\n\n/**\n * Utilities for arc curves.\n * @private\n */\nexport class ArcUtils\n{\n    /**\n     * The arcTo() method creates an arc/curve between two tangents on the canvas.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @private\n     * @param x1 - The x-coordinate of the beginning of the arc\n     * @param y1 - The y-coordinate of the beginning of the arc\n     * @param x2 - The x-coordinate of the end of the arc\n     * @param y2 - The y-coordinate of the end of the arc\n     * @param radius - The radius of the arc\n     * @param points -\n     * @returns - If the arc length is valid, return center of circle, radius and other info otherwise `null`.\n     */\n    static curveTo(x1: number, y1: number, x2: number, y2: number, radius: number, points: Array<number>): IArcLikeShape\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        const a1 = fromY - y1;\n        const b1 = fromX - x1;\n        const a2 = y2 - y1;\n        const b2 = x2 - x1;\n        const mm = Math.abs((a1 * b2) - (b1 * a2));\n\n        if (mm < 1.0e-8 || radius === 0)\n        {\n            if (points[points.length - 2] !== x1 || points[points.length - 1] !== y1)\n            {\n                points.push(x1, y1);\n            }\n\n            return null;\n        }\n\n        const dd = (a1 * a1) + (b1 * b1);\n        const cc = (a2 * a2) + (b2 * b2);\n        const tt = (a1 * a2) + (b1 * b2);\n        const k1 = radius * Math.sqrt(dd) / mm;\n        const k2 = radius * Math.sqrt(cc) / mm;\n        const j1 = k1 * tt / dd;\n        const j2 = k2 * tt / cc;\n        const cx = (k1 * b2) + (k2 * b1);\n        const cy = (k1 * a2) + (k2 * a1);\n        const px = b1 * (k2 + j1);\n        const py = a1 * (k2 + j1);\n        const qx = b2 * (k1 + j2);\n        const qy = a2 * (k1 + j2);\n        const startAngle = Math.atan2(py - cy, px - cx);\n        const endAngle = Math.atan2(qy - cy, qx - cx);\n\n        return {\n            cx: (cx + x1),\n            cy: (cy + y1),\n            radius,\n            startAngle,\n            endAngle,\n            anticlockwise: (b1 * a2 > b2 * a1),\n        };\n    }\n\n    /* eslint-disable max-len */\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @private\n     * @param _startX - Start x location of arc\n     * @param _startY - Start y location of arc\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param _anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @param points - Collection of points to add to\n     */\n    static arc(_startX: number, _startY: number, cx: number, cy: number, radius: number,\n        startAngle: number, endAngle: number, _anticlockwise: boolean, points: Array<number>): void\n    {\n        const sweep = endAngle - startAngle;\n        const n = GRAPHICS_CURVES._segmentsCount(\n            Math.abs(sweep) * radius,\n            Math.ceil(Math.abs(sweep) / PI_2) * 40\n        );\n\n        const theta = (sweep) / (n * 2);\n        const theta2 = theta * 2;\n        const cTheta = Math.cos(theta);\n        const sTheta = Math.sin(theta);\n        const segMinus = n - 1;\n        const remainder = (segMinus % 1) / segMinus;\n\n        for (let i = 0; i <= segMinus; ++i)\n        {\n            const real = i + (remainder * i);\n            const angle = ((theta) + startAngle + (theta2 * real));\n            const c = Math.cos(angle);\n            const s = -Math.sin(angle);\n\n            points.push(\n                (((cTheta * c) + (sTheta * s)) * radius) + cx,\n                (((cTheta * -s) + (sTheta * c)) * radius) + cy\n            );\n        }\n    }\n    /* eslint-enable max-len */\n}\n", "import { GRAPHICS_CURVES } from '../const';\n\n/**\n * Utilities for bezier curves\n * @private\n */\nexport class BezierUtils\n{\n    /**\n     * Calculate length of bezier curve.\n     * Analytical solution is impossible, since it involves an integral that does not integrate in general.\n     * Therefore numerical solution is used.\n     * @private\n     * @param fromX - Starting point x\n     * @param fromY - Starting point y\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - Length of bezier curve\n     */\n    static curveLength(\n        fromX: number, fromY: number,\n        cpX: number, cpY: number,\n        cpX2: number, cpY2: number,\n        toX: number, toY: number): number\n    {\n        const n = 10;\n        let result = 0.0;\n        let t = 0.0;\n        let t2 = 0.0;\n        let t3 = 0.0;\n        let nt = 0.0;\n        let nt2 = 0.0;\n        let nt3 = 0.0;\n        let x = 0.0;\n        let y = 0.0;\n        let dx = 0.0;\n        let dy = 0.0;\n        let prevX = fromX;\n        let prevY = fromY;\n\n        for (let i = 1; i <= n; ++i)\n        {\n            t = i / n;\n            t2 = t * t;\n            t3 = t2 * t;\n            nt = (1.0 - t);\n            nt2 = nt * nt;\n            nt3 = nt2 * nt;\n\n            x = (nt3 * fromX) + (3.0 * nt2 * t * cpX) + (3.0 * nt * t2 * cpX2) + (t3 * toX);\n            y = (nt3 * fromY) + (3.0 * nt2 * t * cpY) + (3 * nt * t2 * cpY2) + (t3 * toY);\n            dx = prevX - x;\n            dy = prevY - y;\n            prevX = x;\n            prevY = y;\n\n            result += Math.sqrt((dx * dx) + (dy * dy));\n        }\n\n        return result;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     *\n     * Ignored from docs since it is not directly exposed.\n     * @ignore\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @param points - Path array to push points into\n     */\n    static curveTo(\n        cpX: number, cpY: number,\n        cpX2: number, cpY2: number,\n        toX: number, toY: number,\n        points: Array<number>): void\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        points.length -= 2;\n\n        const n = GRAPHICS_CURVES._segmentsCount(\n            BezierUtils.curveLength(fromX, fromY, cpX, cpY, cpX2, cpY2, toX, toY)\n        );\n\n        let dt = 0;\n        let dt2 = 0;\n        let dt3 = 0;\n        let t2 = 0;\n        let t3 = 0;\n\n        points.push(fromX, fromY);\n\n        for (let i = 1, j = 0; i <= n; ++i)\n        {\n            j = i / n;\n\n            dt = (1 - j);\n            dt2 = dt * dt;\n            dt3 = dt2 * dt;\n\n            t2 = j * j;\n            t3 = t2 * j;\n\n            points.push(\n                (dt3 * fromX) + (3 * dt2 * j * cpX) + (3 * dt * t2 * cpX2) + (t3 * toX),\n                (dt3 * fromY) + (3 * dt2 * j * cpY) + (3 * dt * t2 * cpY2) + (t3 * toY)\n            );\n        }\n    }\n}\n", "import { GRAPHICS_CURVES } from '../const';\n\n/**\n * Utilities for quadratic curves.\n * @private\n */\nexport class QuadraticUtils\n{\n    /**\n     * Calculate length of quadratic curve\n     * @see {@link http://www.malczak.linuxpl.com/blog/quadratic-bezier-curve-length/}\n     * for the detailed explanation of math behind this.\n     * @private\n     * @param fromX - x-coordinate of curve start point\n     * @param fromY - y-coordinate of curve start point\n     * @param cpX - x-coordinate of curve control point\n     * @param cpY - y-coordinate of curve control point\n     * @param toX - x-coordinate of curve end point\n     * @param toY - y-coordinate of curve end point\n     * @returns - Length of quadratic curve\n     */\n    static curveLength(\n        fromX: number, fromY: number,\n        cpX: number, cpY: number,\n        toX: number, toY: number): number\n    {\n        const ax = fromX - (2.0 * cpX) + toX;\n        const ay = fromY - (2.0 * cpY) + toY;\n        const bx = (2.0 * cpX) - (2.0 * fromX);\n        const by = (2.0 * cpY) - (2.0 * fromY);\n        const a = 4.0 * ((ax * ax) + (ay * ay));\n        const b = 4.0 * ((ax * bx) + (ay * by));\n        const c = (bx * bx) + (by * by);\n\n        const s = 2.0 * Math.sqrt(a + b + c);\n        const a2 = Math.sqrt(a);\n        const a32 = 2.0 * a * a2;\n        const c2 = 2.0 * Math.sqrt(c);\n        const ba = b / a2;\n\n        return (\n            (a32 * s)\n                + (a2 * b * (s - c2))\n                + (\n                    ((4.0 * c * a) - (b * b))\n                   * Math.log(((2.0 * a2) + ba + s) / (ba + c2))\n                )\n        ) / (4.0 * a32);\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @private\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @param points - Points to add segments to.\n     */\n    static curveTo(cpX: number, cpY: number, toX: number, toY: number, points: Array<number>): void\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        const n = GRAPHICS_CURVES._segmentsCount(\n            QuadraticUtils.curveLength(fromX, fromY, cpX, cpY, toX, toY)\n        );\n\n        let xa = 0;\n        let ya = 0;\n\n        for (let i = 1; i <= n; ++i)\n        {\n            const j = i / n;\n\n            xa = fromX + ((cpX - fromX) * j);\n            ya = fromY + ((cpY - fromY) * j);\n\n            points.push(xa + (((cpX + ((toX - cpX) * j)) - xa) * j),\n                ya + (((cpY + ((toY - cpY) * j)) - ya) * j));\n        }\n    }\n}\n", "import type { LineStyle } from '../styles/LineStyle';\nimport type { FillStyle } from '../styles/FillStyle';\n\n/**\n * A structure to hold interim batch objects for Graphics.\n * @memberof PIXI.graphicsUtils\n */\nexport class BatchPart\n{\n    public style: LineStyle | FillStyle;\n    public start: number;\n    public size: number;\n    public attribStart: number;\n    public attribSize: number;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /**\n     * Begin batch part.\n     * @param style\n     * @param startIndex\n     * @param attribStart\n     */\n    public begin(style: LineStyle | FillStyle, startIndex: number, attribStart: number): void\n    {\n        this.reset();\n        this.style = style;\n        this.start = startIndex;\n        this.attribStart = attribStart;\n    }\n\n    /**\n     * End batch part.\n     * @param endIndex\n     * @param endAttrib\n     */\n    public end(endIndex: number, endAttrib: number): void\n    {\n        this.attribSize = endAttrib - this.attribStart;\n        this.size = endIndex - this.start;\n    }\n\n    public reset(): void\n    {\n        this.style = null;\n        this.size = 0;\n        this.start = 0;\n        this.attribStart = 0;\n        this.attribSize = 0;\n    }\n}\n", "/**\n * Generalized convenience utilities for Graphics.\n * @namespace graphicsUtils\n * @memberof PIXI\n */\n\nimport { buildPoly } from './buildPoly';\nexport { buildPoly };\n\nimport { buildCircle } from './buildCircle';\nexport { buildCircle };\n\nimport { buildRectangle } from './buildRectangle';\nexport { buildRectangle };\n\nimport { buildRoundedRectangle } from './buildRoundedRectangle';\nexport { buildRoundedRectangle };\n\nexport * from './buildLine';\nexport * from './ArcUtils';\nexport * from './BezierUtils';\nexport * from './QuadraticUtils';\nexport * from './BatchPart';\n\n// for type only\nimport type { BatchPart } from './BatchPart';\nimport { SHAPES } from '@pixi/math';\nimport type { BatchDrawCall } from '@pixi/core';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Map of fill commands for each shape type.\n * @memberof PIXI.graphicsUtils\n * @member {object} FILL_COMMANDS\n */\nexport const FILL_COMMANDS: Record<SHAPES, IShapeBuildCommand> = {\n    [SHAPES.POLY]: buildPoly,\n    [SHAPES.CIRC]: buildCircle,\n    [SHAPES.ELIP]: buildCircle,\n    [SHAPES.RECT]: buildRectangle,\n    [SHAPES.RREC]: buildRoundedRectangle,\n};\n\n/**\n * Batch pool, stores unused batches for preventing allocations.\n * @memberof PIXI.graphicsUtils\n * @member {Array<PIXI.graphicsUtils.BatchPart>} BATCH_POOL\n */\nexport const BATCH_POOL: Array<BatchPart> = [];\n\n/**\n * Draw call pool, stores unused draw calls for preventing allocations.\n * @memberof PIXI.graphicsUtils\n * @member {Array<PIXI.BatchDrawCall>} DRAW_CALL_POOL\n */\nexport const DRAW_CALL_POOL: Array<BatchDrawCall> = [];\n", "import type { Matrix, SHAPES, IShape } from '@pixi/math';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/**\n * A class to contain data useful for Graphics objects\n * @memberof PIXI\n */\nexport class GraphicsData\n{\n    /**\n     * The shape object to draw.\n     * @member {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle}\n     */\n    shape: IShape;\n\n    /** The style of the line. */\n    lineStyle: LineStyle;\n\n    /** The style of the fill. */\n    fillStyle: FillStyle;\n\n    /** The transform matrix. */\n    matrix: Matrix;\n\n    /** The type of the shape, see the Const.Shapes file for all the existing types, */\n    type: SHAPES;\n\n    /** The collection of points. */\n    points: number[] = [];\n\n    /** The collection of holes. */\n\n    holes: Array<GraphicsData> = [];\n\n    /**\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - the width of the line to draw\n     * @param lineStyle - the color of the line to draw\n     * @param matrix - Transform matrix\n     */\n    constructor(shape: IShape, fillStyle: FillStyle = null, lineStyle: LineStyle = null, matrix: Matrix = null)\n    {\n        this.shape = shape;\n        this.lineStyle = lineStyle;\n        this.fillStyle = fillStyle;\n        this.matrix = matrix;\n        this.type = shape.type;\n    }\n\n    /**\n     * Creates a new GraphicsData object with the same values as this one.\n     * @returns - Cloned GraphicsData object\n     */\n    public clone(): GraphicsData\n    {\n        return new GraphicsData(\n            this.shape,\n            this.fillStyle,\n            this.lineStyle,\n            this.matrix\n        );\n    }\n\n    /** Destroys the Graphics data. */\n    public destroy(): void\n    {\n        this.shape = null;\n        this.holes.length = 0;\n        this.holes = null;\n        this.points.length = 0;\n        this.points = null;\n        this.lineStyle = null;\n        this.fillStyle = null;\n    }\n}\n", "import {\n    buildLine,\n    buildP<PERSON>,\n    <PERSON>ch<PERSON><PERSON>,\n    FILL_COMMANDS,\n    BATCH_POOL,\n    DRAW_CALL_POOL,\n} from './utils';\n\nimport type {\n    Texture } from '@pixi/core';\nimport {\n    BatchGeometry,\n    BatchDrawCall,\n    BatchTextureArray,\n    BaseTexture\n} from '@pixi/core';\n\nimport { DRAW_MODES, WRAP_MODES } from '@pixi/constants';\nimport { Point } from '@pixi/math';\nimport { GraphicsData } from './GraphicsData';\nimport { premultiplyTint } from '@pixi/utils';\nimport { Bounds } from '@pixi/display';\n\nimport type { Circle, Ellipse, Polygon, Rectangle, RoundedRectangle, IPointData, Matrix } from '@pixi/math';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/*\n * Complex shape type\n * @todo Move to Math shapes\n */\ntype IShape = Circle | Ellipse | Polygon | Rectangle | RoundedRectangle;\n\nconst tmpPoint = new Point();\n\n/**\n * The Graphics class contains methods used to draw primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.\n *\n * GraphicsGeometry is designed to not be continually updating the geometry since it's expensive\n * to re-tesselate using **earcut**. Consider using {@link PIXI.Mesh} for this use-case, it's much faster.\n * @memberof PIXI\n */\nexport class GraphicsGeometry extends BatchGeometry\n{\n    /**\n     * The maximum number of points to consider an object \"batchable\",\n     * able to be batched by the renderer's batch system.\n\\\n     */\n    public static BATCHABLE_SIZE = 100;\n\n    /** Minimal distance between points that are considered different. Affects line tesselation. */\n    public closePointEps = 1e-4;\n\n    /** Padding to add to the bounds. */\n    public boundsPadding = 0;\n\n    uvsFloat32: Float32Array = null;\n    indicesUint16: Uint16Array | Uint32Array = null;\n    batchable = false;\n\n    /** An array of points to draw, 2 numbers per point */\n    points: number[] = [];\n\n    /** The collection of colors */\n    colors: number[] = [];\n\n    /** The UVs collection */\n    uvs: number[] = [];\n\n    /** The indices of the vertices */\n    indices: number[] = [];\n\n    /** Reference to the texture IDs. */\n    textureIds: number[] = [];\n\n    /**\n     * The collection of drawn shapes.\n     * @member {PIXI.GraphicsData[]}\n     */\n    graphicsData: Array<GraphicsData> = [];\n\n    /**\n     * List of current draw calls drived from the batches.\n     * @member {PIXI.BatchDrawCall[]}\n     */\n    drawCalls: Array<BatchDrawCall> = [];\n\n    /** Batches need to regenerated if the geometry is updated. */\n    batchDirty = -1;\n\n    /**\n     * Intermediate abstract format sent to batch system.\n     * Can be converted to drawCalls or to batchable objects.\n     * @member {PIXI.graphicsUtils.BatchPart[]}\n     */\n    batches: Array<BatchPart> = [];\n\n    /** Used to detect if the graphics object has changed. */\n    protected dirty = 0;\n\n    /** Used to check if the cache is dirty. */\n    protected cacheDirty = -1;\n\n    /** Used to detect if we cleared the graphicsData. */\n    protected clearDirty = 0;\n\n    /** Index of the last batched shape in the stack of calls. */\n    protected shapeIndex = 0;\n\n    /** Cached bounds. */\n    protected _bounds: Bounds = new Bounds();\n\n    /** The bounds dirty flag. */\n    protected boundsDirty = -1;\n\n    // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n    constructor()\n    {\n        super();\n    }\n\n    /**\n     * Get the current bounds of the graphic geometry.\n     * @readonly\n     */\n    public get bounds(): Bounds\n    {\n        this.updateBatches();\n\n        if (this.boundsDirty !== this.dirty)\n        {\n            this.boundsDirty = this.dirty;\n            this.calculateBounds();\n        }\n\n        return this._bounds;\n    }\n\n    /** Call if you changed graphicsData manually. Empties all batch buffers. */\n    protected invalidate(): void\n    {\n        this.boundsDirty = -1;\n        this.dirty++;\n        this.batchDirty++;\n        this.shapeIndex = 0;\n\n        this.points.length = 0;\n        this.colors.length = 0;\n        this.uvs.length = 0;\n        this.indices.length = 0;\n        this.textureIds.length = 0;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const batchPart = this.batches[i];\n\n            batchPart.reset();\n            BATCH_POOL.push(batchPart);\n        }\n\n        this.batches.length = 0;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This GraphicsGeometry object. Good for chaining method calls\n     */\n    public clear(): GraphicsGeometry\n    {\n        if (this.graphicsData.length > 0)\n        {\n            this.invalidate();\n            this.clearDirty++;\n            this.graphicsData.length = 0;\n        }\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - Defines style of the fill.\n     * @param lineStyle - Defines style of the lines.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawShape(\n        shape: IShape,\n        fillStyle: FillStyle = null,\n        lineStyle: LineStyle = null,\n        matrix: Matrix = null): GraphicsGeometry\n    {\n        const data = new GraphicsData(shape, fillStyle, lineStyle, matrix);\n\n        this.graphicsData.push(data);\n        this.dirty++;\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawHole(shape: IShape, matrix: Matrix = null): GraphicsGeometry\n    {\n        if (!this.graphicsData.length)\n        {\n            return null;\n        }\n\n        const data = new GraphicsData(shape, null, null, matrix);\n\n        const lastShape = this.graphicsData[this.graphicsData.length - 1];\n\n        data.lineStyle = lastShape.lineStyle;\n\n        lastShape.holes.push(data);\n\n        this.dirty++;\n\n        return this;\n    }\n\n    /** Destroys the GraphicsGeometry object. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        // destroy each of the GraphicsData objects\n        for (let i = 0; i < this.graphicsData.length; ++i)\n        {\n            this.graphicsData[i].destroy();\n        }\n\n        this.points.length = 0;\n        this.points = null;\n        this.colors.length = 0;\n        this.colors = null;\n        this.uvs.length = 0;\n        this.uvs = null;\n        this.indices.length = 0;\n        this.indices = null;\n        this.indexBuffer.destroy();\n        this.indexBuffer = null;\n        this.graphicsData.length = 0;\n        this.graphicsData = null;\n        this.drawCalls.length = 0;\n        this.drawCalls = null;\n        this.batches.length = 0;\n        this.batches = null;\n        this._bounds = null;\n    }\n\n    /**\n     * Check to see if a point is contained within this geometry.\n     * @param point - Point to check if it's contained.\n     * @returns {boolean} `true` if the point is contained within geometry.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        const graphicsData = this.graphicsData;\n\n        for (let i = 0; i < graphicsData.length; ++i)\n        {\n            const data = graphicsData[i];\n\n            if (!data.fillStyle.visible)\n            {\n                continue;\n            }\n\n            // only deal with fills..\n            if (data.shape)\n            {\n                if (data.matrix)\n                {\n                    data.matrix.applyInverse(point, tmpPoint);\n                }\n                else\n                {\n                    tmpPoint.copyFrom(point);\n                }\n\n                if (data.shape.contains(tmpPoint.x, tmpPoint.y))\n                {\n                    let hitHole = false;\n\n                    if (data.holes)\n                    {\n                        for (let i = 0; i < data.holes.length; i++)\n                        {\n                            const hole = data.holes[i];\n\n                            if (hole.shape.contains(tmpPoint.x, tmpPoint.y))\n                            {\n                                hitHole = true;\n                                break;\n                            }\n                        }\n                    }\n\n                    if (!hitHole)\n                    {\n                        return true;\n                    }\n                }\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Generates intermediate batch data. Either gets converted to drawCalls\n     * or used to convert to batch objects directly by the Graphics object.\n     */\n    updateBatches(): void\n    {\n        if (!this.graphicsData.length)\n        {\n            this.batchable = true;\n\n            return;\n        }\n\n        if (!this.validateBatching())\n        {\n            return;\n        }\n\n        this.cacheDirty = this.dirty;\n\n        const uvs = this.uvs;\n        const graphicsData = this.graphicsData;\n\n        let batchPart: BatchPart = null;\n\n        let currentStyle = null;\n\n        if (this.batches.length > 0)\n        {\n            batchPart = this.batches[this.batches.length - 1];\n            currentStyle = batchPart.style;\n        }\n\n        for (let i = this.shapeIndex; i < graphicsData.length; i++)\n        {\n            this.shapeIndex++;\n\n            const data = graphicsData[i];\n            const fillStyle = data.fillStyle;\n            const lineStyle = data.lineStyle;\n            const command = FILL_COMMANDS[data.type];\n\n            // build out the shapes points..\n            command.build(data);\n\n            if (data.matrix)\n            {\n                this.transformPoints(data.points, data.matrix);\n            }\n\n            if (fillStyle.visible || lineStyle.visible)\n            {\n                this.processHoles(data.holes);\n            }\n\n            for (let j = 0; j < 2; j++)\n            {\n                const style = (j === 0) ? fillStyle : lineStyle;\n\n                if (!style.visible) continue;\n\n                const nextTexture = style.texture.baseTexture;\n                const index = this.indices.length;\n                const attribIndex = this.points.length / 2;\n\n                nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                if (j === 0)\n                {\n                    this.processFill(data);\n                }\n                else\n                {\n                    this.processLine(data);\n                }\n\n                const size = (this.points.length / 2) - attribIndex;\n\n                if (size === 0) continue;\n                // close batch if style is different\n                if (batchPart && !this._compareStyles(currentStyle, style))\n                {\n                    batchPart.end(index, attribIndex);\n                    batchPart = null;\n                }\n                // spawn new batch if its first batch or previous was closed\n                if (!batchPart)\n                {\n                    batchPart = BATCH_POOL.pop() || new BatchPart();\n                    batchPart.begin(style, index, attribIndex);\n                    this.batches.push(batchPart);\n                    currentStyle = style;\n                }\n\n                this.addUvs(this.points, uvs, style.texture, attribIndex, size, style.matrix);\n            }\n        }\n\n        const index = this.indices.length;\n        const attrib = this.points.length / 2;\n\n        if (batchPart)\n        {\n            batchPart.end(index, attrib);\n        }\n\n        if (this.batches.length === 0)\n        {\n            // there are no visible styles in GraphicsData\n            // its possible that someone wants Graphics just for the bounds\n            this.batchable = true;\n\n            return;\n        }\n\n        const need32 = attrib > 0xffff;\n\n        // prevent allocation when length is same as buffer\n        if (this.indicesUint16 && this.indices.length === this.indicesUint16.length\n            && need32 === (this.indicesUint16.BYTES_PER_ELEMENT > 2))\n        {\n            this.indicesUint16.set(this.indices);\n        }\n        else\n        {\n            this.indicesUint16 = need32 ? new Uint32Array(this.indices) : new Uint16Array(this.indices);\n        }\n\n        // TODO make this a const..\n        this.batchable = this.isBatchable();\n\n        if (this.batchable)\n        {\n            this.packBatches();\n        }\n        else\n        {\n            this.buildDrawCalls();\n        }\n    }\n\n    /**\n     * Affinity check\n     * @param styleA\n     * @param styleB\n     */\n    protected _compareStyles(styleA: FillStyle | LineStyle, styleB: FillStyle | LineStyle): boolean\n    {\n        if (!styleA || !styleB)\n        {\n            return false;\n        }\n\n        if (styleA.texture.baseTexture !== styleB.texture.baseTexture)\n        {\n            return false;\n        }\n\n        if (styleA.color + styleA.alpha !== styleB.color + styleB.alpha)\n        {\n            return false;\n        }\n\n        if (!!(styleA as LineStyle).native !== !!(styleB as LineStyle).native)\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /** Test geometry for batching process. */\n    protected validateBatching(): boolean\n    {\n        if (this.dirty === this.cacheDirty || !this.graphicsData.length)\n        {\n            return false;\n        }\n\n        for (let i = 0, l = this.graphicsData.length; i < l; i++)\n        {\n            const data = this.graphicsData[i];\n            const fill = data.fillStyle;\n            const line = data.lineStyle;\n\n            if (fill && !fill.texture.baseTexture.valid) return false;\n            if (line && !line.texture.baseTexture.valid) return false;\n        }\n\n        return true;\n    }\n\n    /** Offset the indices so that it works with the batcher. */\n    protected packBatches(): void\n    {\n        this.batchDirty++;\n        this.uvsFloat32 = new Float32Array(this.uvs);\n\n        const batches = this.batches;\n\n        for (let i = 0, l = batches.length; i < l; i++)\n        {\n            const batch = batches[i];\n\n            for (let j = 0; j < batch.size; j++)\n            {\n                const index = batch.start + j;\n\n                this.indicesUint16[index] = this.indicesUint16[index] - batch.attribStart;\n            }\n        }\n    }\n\n    /**\n     * Checks to see if this graphics geometry can be batched.\n     * Currently it needs to be small enough and not contain any native lines.\n     */\n    protected isBatchable(): boolean\n    {\n        // prevent heavy mesh batching\n        if (this.points.length > 0xffff * 2)\n        {\n            return false;\n        }\n\n        const batches = this.batches;\n\n        for (let i = 0; i < batches.length; i++)\n        {\n            if ((batches[i].style as LineStyle).native)\n            {\n                return false;\n            }\n        }\n\n        return (this.points.length < GraphicsGeometry.BATCHABLE_SIZE * 2);\n    }\n\n    /** Converts intermediate batches data to drawCalls. */\n    protected buildDrawCalls(): void\n    {\n        let TICK = ++BaseTexture._globalBatch;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        let currentGroup: BatchDrawCall =  DRAW_CALL_POOL.pop();\n\n        if (!currentGroup)\n        {\n            currentGroup = new BatchDrawCall();\n            currentGroup.texArray = new BatchTextureArray();\n        }\n        currentGroup.texArray.count = 0;\n        currentGroup.start = 0;\n        currentGroup.size = 0;\n        currentGroup.type = DRAW_MODES.TRIANGLES;\n\n        let textureCount = 0;\n        let currentTexture = null;\n        let textureId = 0;\n        let native = false;\n        let drawMode = DRAW_MODES.TRIANGLES;\n\n        let index = 0;\n\n        this.drawCalls.push(currentGroup);\n\n        // TODO - this can be simplified\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const data = this.batches[i];\n\n            // TODO add some full on MAX_TEXTURE CODE..\n            const MAX_TEXTURES = 8;\n\n            // Forced cast for checking `native` without errors\n            const style = data.style as LineStyle;\n\n            const nextTexture = style.texture.baseTexture;\n\n            if (native !== !!style.native)\n            {\n                native = !!style.native;\n                drawMode = native ? DRAW_MODES.LINES : DRAW_MODES.TRIANGLES;\n\n                // force the batch to break!\n                currentTexture = null;\n                textureCount = MAX_TEXTURES;\n                TICK++;\n            }\n\n            if (currentTexture !== nextTexture)\n            {\n                currentTexture = nextTexture;\n\n                if (nextTexture._batchEnabled !== TICK)\n                {\n                    if (textureCount === MAX_TEXTURES)\n                    {\n                        TICK++;\n\n                        textureCount = 0;\n\n                        if (currentGroup.size > 0)\n                        {\n                            currentGroup = DRAW_CALL_POOL.pop();\n                            if (!currentGroup)\n                            {\n                                currentGroup = new BatchDrawCall();\n                                currentGroup.texArray = new BatchTextureArray();\n                            }\n                            this.drawCalls.push(currentGroup);\n                        }\n\n                        currentGroup.start = index;\n                        currentGroup.size = 0;\n                        currentGroup.texArray.count = 0;\n                        currentGroup.type = drawMode;\n                    }\n\n                    // TODO add this to the render part..\n                    // Hack! Because texture has protected `touched`\n                    nextTexture.touched = 1;// touch;\n\n                    nextTexture._batchEnabled = TICK;\n                    nextTexture._batchLocation = textureCount;\n                    nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                    currentGroup.texArray.elements[currentGroup.texArray.count++] = nextTexture;\n                    textureCount++;\n                }\n            }\n\n            currentGroup.size += data.size;\n            index += data.size;\n\n            textureId = nextTexture._batchLocation;\n\n            this.addColors(colors, style.color, style.alpha, data.attribSize, data.attribStart);\n            this.addTextureIds(textureIds, textureId, data.attribSize, data.attribStart);\n        }\n\n        BaseTexture._globalBatch = TICK;\n\n        // upload..\n        // merge for now!\n        this.packAttributes();\n    }\n\n    /** Packs attributes to single buffer. */\n    protected packAttributes(): void\n    {\n        const verts = this.points;\n        const uvs = this.uvs;\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        // verts are 2 positions.. so we * by 3 as there are 6 properties.. then 4 cos its bytes\n        const glPoints = new ArrayBuffer(verts.length * 3 * 4);\n        const f32 = new Float32Array(glPoints);\n        const u32 = new Uint32Array(glPoints);\n\n        let p = 0;\n\n        for (let i = 0; i < verts.length / 2; i++)\n        {\n            f32[p++] = verts[i * 2];\n            f32[p++] = verts[(i * 2) + 1];\n\n            f32[p++] = uvs[i * 2];\n            f32[p++] = uvs[(i * 2) + 1];\n\n            u32[p++] = colors[i];\n\n            f32[p++] = textureIds[i];\n        }\n\n        this._buffer.update(glPoints);\n        this._indexBuffer.update(this.indicesUint16);\n    }\n\n    /**\n     * Process fill part of Graphics.\n     * @param data\n     */\n    protected processFill(data: GraphicsData): void\n    {\n        if (data.holes.length)\n        {\n            buildPoly.triangulate(data, this);\n        }\n        else\n        {\n            const command = FILL_COMMANDS[data.type];\n\n            command.triangulate(data, this);\n        }\n    }\n\n    /**\n     * Process line part of Graphics.\n     * @param data\n     */\n    protected processLine(data: GraphicsData): void\n    {\n        buildLine(data, this);\n\n        for (let i = 0; i < data.holes.length; i++)\n        {\n            buildLine(data.holes[i], this);\n        }\n    }\n\n    /**\n     * Process the holes data.\n     * @param holes\n     */\n    protected processHoles(holes: Array<GraphicsData>): void\n    {\n        for (let i = 0; i < holes.length; i++)\n        {\n            const hole = holes[i];\n            const command = FILL_COMMANDS[hole.type];\n\n            command.build(hole);\n\n            if (hole.matrix)\n            {\n                this.transformPoints(hole.points, hole.matrix);\n            }\n        }\n    }\n\n    /** Update the local bounds of the object. Expensive to use performance-wise. */\n    protected calculateBounds(): void\n    {\n        const bounds = this._bounds;\n\n        bounds.clear();\n        bounds.addVertexData((this.points as any), 0, this.points.length);\n        bounds.pad(this.boundsPadding, this.boundsPadding);\n    }\n\n    /**\n     * Transform points using matrix.\n     * @param points - Points to transform\n     * @param matrix - Transform matrix\n     */\n    protected transformPoints(points: Array<number>, matrix: Matrix): void\n    {\n        for (let i = 0; i < points.length / 2; i++)\n        {\n            const x = points[(i * 2)];\n            const y = points[(i * 2) + 1];\n\n            points[(i * 2)] = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n            points[(i * 2) + 1] = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n        }\n    }\n\n    /**\n     * Add colors.\n     * @param colors - List of colors to add to\n     * @param color - Color to add\n     * @param alpha - Alpha to use\n     * @param size - Number of colors to add\n     * @param offset\n     */\n    protected addColors(\n        colors: Array<number>,\n        color: number,\n        alpha: number,\n        size: number,\n        offset = 0): void\n    {\n        // TODO use the premultiply bits Ivan added\n        const rgb = (color >> 16) + (color & 0xff00) + ((color & 0xff) << 16);\n\n        const rgba =  premultiplyTint(rgb, alpha);\n\n        colors.length = Math.max(colors.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            colors[offset + i] = rgba;\n        }\n    }\n\n    /**\n     * Add texture id that the shader/fragment wants to use.\n     * @param textureIds\n     * @param id\n     * @param size\n     * @param offset\n     */\n    protected addTextureIds(\n        textureIds: Array<number>,\n        id: number,\n        size: number,\n        offset = 0): void\n    {\n        textureIds.length = Math.max(textureIds.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            textureIds[offset + i] = id;\n        }\n    }\n\n    /**\n     * Generates the UVs for a shape.\n     * @param verts - Vertices\n     * @param uvs - UVs\n     * @param texture - Reference to Texture\n     * @param start - Index buffer start index.\n     * @param size - The size/length for index buffer.\n     * @param matrix - Optional transform for all points.\n     */\n    protected addUvs(\n        verts: Array<number>,\n        uvs: Array<number>,\n        texture: Texture,\n        start: number,\n        size: number,\n        matrix: Matrix = null): void\n    {\n        let index = 0;\n        const uvsStart = uvs.length;\n        const frame = texture.frame;\n\n        while (index < size)\n        {\n            let x = verts[(start + index) * 2];\n            let y = verts[((start + index) * 2) + 1];\n\n            if (matrix)\n            {\n                const nx = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n\n                y = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n                x = nx;\n            }\n\n            index++;\n\n            uvs.push(x / frame.width, y / frame.height);\n        }\n\n        const baseTexture = texture.baseTexture;\n\n        if (frame.width < baseTexture.width\n            || frame.height < baseTexture.height)\n        {\n            this.adjustUvs(uvs, texture, uvsStart, size);\n        }\n    }\n\n    /**\n     * Modify uvs array according to position of texture region\n     * Does not work with rotated or trimmed textures\n     * @param uvs - array\n     * @param texture - region\n     * @param start - starting index for uvs\n     * @param size - how many points to adjust\n     */\n    protected adjustUvs(uvs: Array<number>, texture: Texture, start: number, size: number): void\n    {\n        const baseTexture = texture.baseTexture;\n        const eps = 1e-6;\n        const finish = start + (size * 2);\n        const frame = texture.frame;\n        const scaleX = frame.width / baseTexture.width;\n        const scaleY = frame.height / baseTexture.height;\n        let offsetX = frame.x / frame.width;\n        let offsetY = frame.y / frame.height;\n        let minX = Math.floor(uvs[start] + eps);\n        let minY = Math.floor(uvs[start + 1] + eps);\n\n        for (let i = start + 2; i < finish; i += 2)\n        {\n            minX = Math.min(minX, Math.floor(uvs[i] + eps));\n            minY = Math.min(minY, Math.floor(uvs[i + 1] + eps));\n        }\n        offsetX -= minX;\n        offsetY -= minY;\n        for (let i = start; i < finish; i += 2)\n        {\n            uvs[i] = (uvs[i] + offsetX) * scaleX;\n            uvs[i + 1] = (uvs[i + 1] + offsetY) * scaleY;\n        }\n    }\n}\n", "import { FillStyle } from './FillStyle';\nimport { LINE_JOIN, LINE_CAP } from '../const';\n\n/**\n * Represents the line style for Graphics.\n * @memberof PIXI\n */\nexport class LineStyle extends FillStyle\n{\n    /** The width (thickness) of any lines drawn. */\n    public width = 0;\n\n    /** The alignment of any lines drawn (0.5 = middle, 1 = outer, 0 = inner). WebGL only. */\n    public alignment = 0.5;\n\n    /** If true the lines will be draw using LINES instead of TRIANGLE_STRIP. */\n    public native = false;\n\n    /**\n     * Line cap style.\n     * @member {PIXI.LINE_CAP}\n     * @default PIXI.LINE_CAP.BUTT\n     */\n    public cap = LINE_CAP.BUTT;\n\n    /**\n     * Line join style.\n     * @member {PIXI.LINE_JOIN}\n     * @default PIXI.LINE_JOIN.MITER\n     */\n    public join = LINE_JOIN.MITER;\n\n    /** Miter limit. */\n    public miterLimit = 10;\n\n    /** Clones the object. */\n    public clone(): LineStyle\n    {\n        const obj = new LineStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n        obj.width = this.width;\n        obj.alignment = this.alignment;\n        obj.native = this.native;\n        obj.cap = this.cap;\n        obj.join = this.join;\n        obj.miterLimit = this.miterLimit;\n\n        return obj;\n    }\n\n    /** Reset the line style to default. */\n    public reset(): void\n    {\n        super.reset();\n\n        // Override default line style color\n        this.color = 0x0;\n\n        this.alignment = 0.5;\n        this.width = 0;\n        this.native = false;\n    }\n}\n", "import {\n    Circle,\n    Ellipse,\n    PI_2,\n    Point,\n    Polygon,\n    Rectangle,\n    RoundedRectangle,\n    Matrix,\n    SHAPES,\n} from '@pixi/math';\n\nimport type { <PERSON><PERSON><PERSON>, BatchDrawCall } from '@pixi/core';\nimport { Texture, UniformGroup, State, Shader } from '@pixi/core';\nimport { BezierUtils, QuadraticUtils, ArcUtils } from './utils';\nimport { hex2rgb } from '@pixi/utils';\nimport { GraphicsGeometry } from './GraphicsGeometry';\nimport { FillStyle } from './styles/FillStyle';\nimport { LineStyle } from './styles/LineStyle';\nimport { BLEND_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\n\nimport type { IShape, IPointData } from '@pixi/math';\nimport type { IDestroyOptions } from '@pixi/display';\nimport { LINE_JOIN, LINE_CAP } from './const';\n\n/** Batch element computed from Graphics geometry */\nexport interface IGraphicsBatchElement\n{\n    vertexData: Float32Array;\n    blendMode: BLEND_MODES;\n    indices: Uint16Array | Uint32Array;\n    uvs: Float32Array;\n    alpha: number;\n    worldAlpha: number;\n    _batchRGB: number[];\n    _tintRGB: number;\n    _texture: Texture;\n}\n\nexport interface IFillStyleOptions\n{\n    color?: number;\n    alpha?: number;\n    texture?: Texture;\n    matrix?: Matrix;\n}\n\nexport interface ILineStyleOptions extends IFillStyleOptions\n{\n    width?: number;\n    alignment?: number;\n    native?: boolean;\n    cap?: LINE_CAP;\n    join?: LINE_JOIN;\n    miterLimit?: number;\n}\n\nconst temp = new Float32Array(3);\n\n// a default shaders map used by graphics..\nconst DEFAULT_SHADERS: {[key: string]: Shader} = {};\n\nexport interface Graphics extends GlobalMixins.Graphics, Container {}\n\n/**\n * The Graphics class is primarily used to render primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.  However, you can also use a Graphics\n * object to build a list of primitives to use as a mask, or as a complex hitArea.\n *\n * Please note that due to legacy naming conventions, the behavior of some functions in this class\n * can be confusing.  Each call to `drawRect()`, `drawPolygon()`, etc. actually stores that primitive\n * in the Geometry class's GraphicsGeometry object for later use in rendering or hit testing - the\n * functions do not directly draw anything to the screen.  Similarly, the `clear()` function doesn't\n * change the screen, it simply resets the list of primitives, which can be useful if you want to\n * rebuild the contents of an existing Graphics object.\n *\n * Once a GraphicsGeometry list is built, you can re-use it in other Geometry objects as\n * an optimization, by passing it into a new Geometry object's constructor.  Because of this\n * ability, it's important to call `destroy()` on Geometry objects once you are done with them, to\n * properly dereference each GraphicsGeometry and prevent memory leaks.\n * @memberof PIXI\n */\nexport class Graphics extends Container\n{\n    /**\n     * New rendering behavior for rounded rectangles: circular arcs instead of quadratic bezier curves.\n     * In the next major release, we'll enable this by default.\n     */\n    public static nextRoundedRectBehavior = false;\n\n    /**\n     * Temporary point to use for containsPoint.\n     * @private\n     */\n    static _TEMP_POINT = new Point();\n\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Graphics objects.\n     */\n    public shader: Shader = null;\n\n    /** Renderer plugin for batching */\n    public pluginName = 'batch';\n\n    /**\n     * Current path\n     * @readonly\n     */\n    public currentPath: Polygon = null;\n\n    /** A collections of batches! These can be drawn by the renderer batch system. */\n    protected batches: Array<IGraphicsBatchElement> = [];\n\n    /** Update dirty for limiting calculating tints for batches. */\n    protected batchTint = -1;\n\n    /** Update dirty for limiting calculating batches.*/\n    protected batchDirty = -1;\n\n    /** Copy of the object vertex data. */\n    protected vertexData: Float32Array = null;\n\n    /** Current fill style. */\n    protected _fillStyle: FillStyle = new FillStyle();\n\n    /** Current line style. */\n    protected _lineStyle: LineStyle = new LineStyle();\n\n    /** Current shape transform matrix. */\n    protected _matrix: Matrix = null;\n\n    /** Current hole mode is enabled. */\n    protected _holeMode = false;\n    protected _transformID: number;\n    protected _tint: number;\n\n    /**\n     * Represents the WebGL state the Graphics required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    private state: State = State.for2d();\n    private _geometry: GraphicsGeometry;\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh or Graphics objects.\n     * @readonly\n     */\n    public get geometry(): GraphicsGeometry\n    {\n        return this._geometry;\n    }\n\n    /**\n     * @param geometry - Geometry to use, if omitted will create a new GraphicsGeometry instance.\n     */\n    constructor(geometry: GraphicsGeometry = null)\n    {\n        super();\n\n        this._geometry = geometry || new GraphicsGeometry();\n        this._geometry.refCount++;\n\n        /**\n         * When cacheAsBitmap is set to true the graphics object will be rendered as if it was a sprite.\n         * This is useful if your graphics element does not change often, as it will speed up the rendering\n         * of the object in exchange for taking up texture memory. It is also useful if you need the graphics\n         * object to be anti-aliased, because it will be rendered using canvas. This is not recommended if\n         * you are constantly redrawing the graphics element.\n         * @name cacheAsBitmap\n         * @member {boolean}\n         * @memberof PIXI.Graphics#\n         * @default false\n         */\n\n        this._transformID = -1;\n\n        // Set default\n        this.tint = 0xFFFFFF;\n        this.blendMode = BLEND_MODES.NORMAL;\n    }\n\n    /**\n     * Creates a new Graphics object with the same values as this one.\n     * Note that only the geometry of the object is cloned, not its transform (position,scale,etc)\n     * @returns - A clone of the graphics object\n     */\n    public clone(): Graphics\n    {\n        this.finishPoly();\n\n        return new Graphics(this._geometry);\n    }\n\n    /**\n     * The blend mode to be applied to the graphic shape. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.  Note that, since each\n     * primitive in the GraphicsGeometry list is rendered sequentially, modes\n     * such as `PIXI.BLEND_MODES.ADD` and `PIXI.BLEND_MODES.MULTIPLY` will\n     * be applied per-primitive.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    public get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * The tint applied to each graphic shape. This is a hex value. A value of\n     * 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    public get tint(): number\n    {\n        return this._tint;\n    }\n\n    public set tint(value: number)\n    {\n        this._tint = value;\n    }\n\n    /**\n     * The current fill style.\n     * @readonly\n     */\n    public get fill(): FillStyle\n    {\n        return this._fillStyle;\n    }\n\n    /**\n     * The current line style.\n     * @readonly\n     */\n    public get line(): LineStyle\n    {\n        return this._lineStyle;\n    }\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param [width=0] - width of the line to draw, will update the objects stored style\n     * @param [color=0x0] - color of the line to draw, will update the objects stored style\n     * @param [alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param [alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param [native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(width: number, color?: number, alpha?: number, alignment?: number, native?: boolean): this;\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param options - Line style options\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {number} [options.color=0x0] - color of the line to draw, will update the objects stored style\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(options?: ILineStyleOptions): this;\n\n    public lineStyle(options: ILineStyleOptions | number = null,\n        color = 0x0, alpha = 1, alignment = 0.5, native = false): this\n    {\n        // Support non-object params: (width, color, alpha, alignment, native)\n        if (typeof options === 'number')\n        {\n            options = { width: options, color, alpha, alignment, native } as ILineStyleOptions;\n        }\n\n        return this.lineTextureStyle(options);\n    }\n\n    /**\n     * Like line style but support texture for line fill.\n     * @param [options] - Collection of options for setting line style.\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to use\n     * @param {number} [options.color=0x0] - color of the line to draw, will update the objects stored style.\n     *  Default 0xFFFFFF if texture present.\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {PIXI.Matrix} [options.matrix=null] - Texture matrix to transform texture\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineTextureStyle(options?: ILineStyleOptions): this\n    {\n        // Apply defaults\n        options = Object.assign({\n            width: 0,\n            texture: Texture.WHITE,\n            color: (options && options.texture) ? 0xFFFFFF : 0x0,\n            alpha: 1,\n            matrix: null,\n            alignment: 0.5,\n            native: false,\n            cap: LINE_CAP.BUTT,\n            join: LINE_JOIN.MITER,\n            miterLimit: 10,\n        }, options);\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.width > 0 && options.alpha > 0;\n\n        if (!visible)\n        {\n            this._lineStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._lineStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Start a polygon object internally.\n     * @protected\n     */\n    protected startPoly(): void\n    {\n        if (this.currentPath)\n        {\n            const points = this.currentPath.points;\n            const len = this.currentPath.points.length;\n\n            if (len > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = new Polygon();\n                this.currentPath.closeStroke = false;\n                this.currentPath.points.push(points[len - 2], points[len - 1]);\n            }\n        }\n        else\n        {\n            this.currentPath = new Polygon();\n            this.currentPath.closeStroke = false;\n        }\n    }\n\n    /**\n     * Finish the polygon object.\n     * @protected\n     */\n    finishPoly(): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = null;\n            }\n            else\n            {\n                this.currentPath.points.length = 0;\n            }\n        }\n    }\n\n    /**\n     * Moves the current drawing position to x, y.\n     * @param x - the X coordinate to move to\n     * @param y - the Y coordinate to move to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public moveTo(x: number, y: number): this\n    {\n        this.startPoly();\n        this.currentPath.points[0] = x;\n        this.currentPath.points[1] = y;\n\n        return this;\n    }\n\n    /**\n     * Draws a line using the current line style from the current drawing position to (x, y);\n     * The current drawing position is then set to (x, y).\n     * @param x - the X coordinate to draw to\n     * @param y - the Y coordinate to draw to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineTo(x: number, y: number): this\n    {\n        if (!this.currentPath)\n        {\n            this.moveTo(0, 0);\n        }\n\n        // remove duplicates..\n        const points = this.currentPath.points;\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        if (fromX !== x || fromY !== y)\n        {\n            points.push(x, y);\n        }\n\n        return this;\n    }\n\n    /**\n     * Initialize the curve\n     * @param x\n     * @param y\n     */\n    protected _initCurve(x = 0, y = 0): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length === 0)\n            {\n                this.currentPath.points = [x, y];\n            }\n        }\n        else\n        {\n            this.moveTo(x, y);\n        }\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public quadraticCurveTo(cpX: number, cpY: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        const points = this.currentPath.points;\n\n        if (points.length === 0)\n        {\n            this.moveTo(0, 0);\n        }\n\n        QuadraticUtils.curveTo(cpX, cpY, toX, toY, points);\n\n        return this;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns This Graphics object. Good for chaining method calls\n     */\n    public bezierCurveTo(cpX: number, cpY: number, cpX2: number, cpY2: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        BezierUtils.curveTo(cpX, cpY, cpX2, cpY2, toX, toY, this.currentPath.points);\n\n        return this;\n    }\n\n    /**\n     * The arcTo() method creates an arc/curve between two tangents on the canvas.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @param x1 - The x-coordinate of the first tangent point of the arc\n     * @param y1 - The y-coordinate of the first tangent point of the arc\n     * @param x2 - The x-coordinate of the end of the arc\n     * @param y2 - The y-coordinate of the end of the arc\n     * @param radius - The radius of the arc\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this\n    {\n        this._initCurve(x1, y1);\n\n        const points = this.currentPath.points;\n\n        const result = ArcUtils.curveTo(x1, y1, x2, y2, radius, points);\n\n        if (result)\n        {\n            const { cx, cy, radius, startAngle, endAngle, anticlockwise } = result;\n\n            this.arc(cx, cy, radius, startAngle, endAngle, anticlockwise);\n        }\n\n        return this;\n    }\n\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arc(cx: number, cy: number, radius: number, startAngle: number, endAngle: number, anticlockwise = false): this\n    {\n        if (startAngle === endAngle)\n        {\n            return this;\n        }\n\n        if (!anticlockwise && endAngle <= startAngle)\n        {\n            endAngle += PI_2;\n        }\n        else if (anticlockwise && startAngle <= endAngle)\n        {\n            startAngle += PI_2;\n        }\n\n        const sweep = endAngle - startAngle;\n\n        if (sweep === 0)\n        {\n            return this;\n        }\n\n        const startX = cx + (Math.cos(startAngle) * radius);\n        const startY = cy + (Math.sin(startAngle) * radius);\n        const eps = this._geometry.closePointEps;\n\n        // If the currentPath exists, take its points. Otherwise call `moveTo` to start a path.\n        let points = this.currentPath ? this.currentPath.points : null;\n\n        if (points)\n        {\n            // TODO: make a better fix.\n\n            // We check how far our start is from the last existing point\n            const xDiff = Math.abs(points[points.length - 2] - startX);\n            const yDiff = Math.abs(points[points.length - 1] - startY);\n\n            if (xDiff < eps && yDiff < eps)\n            {\n                // If the point is very close, we don't add it, since this would lead to artifacts\n                // during tessellation due to floating point imprecision.\n            }\n            else\n            {\n                points.push(startX, startY);\n            }\n        }\n        else\n        {\n            this.moveTo(startX, startY);\n            points = this.currentPath.points;\n        }\n\n        ArcUtils.arc(startX, startY, cx, cy, radius, startAngle, endAngle, anticlockwise, points);\n\n        return this;\n    }\n\n    /**\n     * Specifies a simple one-color fill that subsequent calls to other Graphics methods\n     * (such as lineTo() or drawCircle()) use when drawing.\n     * @param color - the color of the fill\n     * @param alpha - the alpha of the fill\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public beginFill(color = 0, alpha = 1): this\n    {\n        return this.beginTextureFill({ texture: Texture.WHITE, color, alpha });\n    }\n\n    /**\n     * Begin the texture fill\n     * @param options - Object object.\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to fill\n     * @param {number} [options.color=0xffffff] - Background to fill behind texture\n     * @param {number} [options.alpha=1] - Alpha of fill\n     * @param {PIXI.Matrix} [options.matrix=null] - Transform matrix\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    beginTextureFill(options?: IFillStyleOptions): this\n    {\n        // Apply defaults\n        options = Object.assign({\n            texture: Texture.WHITE,\n            color: 0xFFFFFF,\n            alpha: 1,\n            matrix: null,\n        }, options) as IFillStyleOptions;\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.alpha > 0;\n\n        if (!visible)\n        {\n            this._fillStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._fillStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Applies a fill to the lines and shapes that were added since the last call to the beginFill() method.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public endFill(): this\n    {\n        this.finishPoly();\n\n        this._fillStyle.reset();\n\n        return this;\n    }\n\n    /**\n     * Draws a rectangle shape.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRect(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Rectangle(x, y, width, height));\n    }\n\n    /**\n     * Draw a rectangle shape with rounded/beveled corners.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @param radius - Radius of the rectangle corners\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRoundedRect(x: number, y: number, width: number, height: number, radius: number): this\n    {\n        return this.drawShape(new RoundedRectangle(x, y, width, height, radius));\n    }\n\n    /**\n     * Draws a circle.\n     * @param x - The X coordinate of the center of the circle\n     * @param y - The Y coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawCircle(x: number, y: number, radius: number): this\n    {\n        return this.drawShape(new Circle(x, y, radius));\n    }\n\n    /**\n     * Draws an ellipse.\n     * @param x - The X coordinate of the center of the ellipse\n     * @param y - The Y coordinate of the center of the ellipse\n     * @param width - The half width of the ellipse\n     * @param height - The half height of the ellipse\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawEllipse(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Ellipse(x, y, width, height));\n    }\n\n    public drawPolygon(...path: Array<number> | Array<IPointData>): this;\n    public drawPolygon(path: Array<number> | Array<IPointData> | Polygon): this;\n\n    /**\n     * Draws a polygon using the given path.\n     * @param {number[]|PIXI.IPointData[]|PIXI.Polygon} path - The path data used to construct the polygon.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawPolygon(...path: any[]): this\n    {\n        let points: Array<number> | Array<IPointData>;\n        let closeStroke = true;// !!this._fillStyle;\n\n        const poly = path[0] as Polygon;\n\n        // check if data has points..\n        if (poly.points)\n        {\n            closeStroke = poly.closeStroke;\n            points = poly.points;\n        }\n        else\n        if (Array.isArray(path[0]))\n        {\n            points = path[0];\n        }\n        else\n        {\n            points = path;\n        }\n\n        const shape = new Polygon(points);\n\n        shape.closeStroke = closeStroke;\n\n        this.drawShape(shape);\n\n        return this;\n    }\n\n    /**\n     * Draw any shape.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - Shape to draw\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawShape(shape: IShape): this\n    {\n        if (!this._holeMode)\n        {\n            this._geometry.drawShape(\n                shape,\n                this._fillStyle.clone(),\n                this._lineStyle.clone(),\n                this._matrix\n            );\n        }\n        else\n        {\n            this._geometry.drawHole(shape, this._matrix);\n        }\n\n        return this;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public clear(): this\n    {\n        this._geometry.clear();\n        this._lineStyle.reset();\n        this._fillStyle.reset();\n\n        this._boundsID++;\n        this._matrix = null;\n        this._holeMode = false;\n        this.currentPath = null;\n\n        return this;\n    }\n\n    /**\n     * True if graphics consists of one rectangle, and thus, can be drawn like a Sprite and\n     * masked with gl.scissor.\n     * @returns - True if only 1 rect.\n     */\n    public isFastRect(): boolean\n    {\n        const data = this._geometry.graphicsData;\n\n        return data.length === 1\n            && data[0].shape.type === SHAPES.RECT\n            && !data[0].matrix\n            && !data[0].holes.length\n            && !(data[0].lineStyle.visible && data[0].lineStyle.width);\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n        // batch part..\n        // batch it!\n\n        geometry.updateBatches();\n\n        if (geometry.batchable)\n        {\n            if (this.batchDirty !== geometry.batchDirty)\n            {\n                this._populateBatches();\n            }\n\n            this._renderBatched(renderer);\n        }\n        else\n        {\n            // no batching...\n            renderer.batch.flush();\n\n            this._renderDirect(renderer);\n        }\n    }\n\n    /** Populating batches for rendering. */\n    protected _populateBatches(): void\n    {\n        const geometry = this._geometry;\n        const blendMode = this.blendMode;\n        const len = geometry.batches.length;\n\n        this.batchTint = -1;\n        this._transformID = -1;\n        this.batchDirty = geometry.batchDirty;\n        this.batches.length = len;\n\n        this.vertexData = new Float32Array(geometry.points);\n\n        for (let i = 0; i < len; i++)\n        {\n            const gI = geometry.batches[i];\n            const color = gI.style.color;\n            const vertexData = new Float32Array(this.vertexData.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const uvs = new Float32Array(geometry.uvsFloat32.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const indices = new Uint16Array(geometry.indicesUint16.buffer,\n                gI.start * 2,\n                gI.size);\n\n            const batch = {\n                vertexData,\n                blendMode,\n                indices,\n                uvs,\n                _batchRGB: hex2rgb(color) as Array<number>,\n                _tintRGB: color,\n                _texture: gI.style.texture,\n                alpha: gI.style.alpha,\n                worldAlpha: 1 };\n\n            this.batches[i] = batch;\n        }\n    }\n\n    /**\n     * Renders the batches using the BathedRenderer plugin\n     * @param renderer - The renderer\n     */\n    protected _renderBatched(renderer: Renderer): void\n    {\n        if (!this.batches.length)\n        {\n            return;\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n\n        this.calculateVertices();\n        this.calculateTints();\n\n        for (let i = 0, l = this.batches.length; i < l; i++)\n        {\n            const batch = this.batches[i];\n\n            batch.worldAlpha = this.worldAlpha * batch.alpha;\n\n            renderer.plugins[this.pluginName].render(batch);\n        }\n    }\n\n    /**\n     * Renders the graphics direct\n     * @param renderer - The renderer\n     */\n    protected _renderDirect(renderer: Renderer): void\n    {\n        const shader = this._resolveDirectShader(renderer);\n\n        const geometry = this._geometry;\n        const tint = this.tint;\n        const worldAlpha = this.worldAlpha;\n        const uniforms = shader.uniforms;\n        const drawCalls = geometry.drawCalls;\n\n        // lets set the transfomr\n        uniforms.translationMatrix = this.transform.worldTransform;\n\n        // and then lets set the tint..\n        uniforms.tint[0] = (((tint >> 16) & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[1] = (((tint >> 8) & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[2] = ((tint & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[3] = worldAlpha;\n\n        // the first draw call, we can set the uniforms of the shader directly here.\n\n        // this means that we can tack advantage of the sync function of pixi!\n        // bind and sync uniforms..\n        // there is a way to optimise this..\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(geometry, shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // then render the rest of them...\n        for (let i = 0, l = drawCalls.length; i < l; i++)\n        {\n            this._renderDrawCallDirect(renderer, geometry.drawCalls[i]);\n        }\n    }\n\n    /**\n     * Renders specific DrawCall\n     * @param renderer\n     * @param drawCall\n     */\n    protected _renderDrawCallDirect(renderer: Renderer, drawCall: BatchDrawCall): void\n    {\n        const { texArray, type, size, start } = drawCall;\n        const groupTextureCount = texArray.count;\n\n        for (let j = 0; j < groupTextureCount; j++)\n        {\n            renderer.texture.bind(texArray.elements[j], j);\n        }\n\n        renderer.geometry.draw(type, size, start);\n    }\n\n    /**\n     * Resolves shader for direct rendering\n     * @param renderer - The renderer\n     */\n    protected _resolveDirectShader(renderer: Renderer): Shader\n    {\n        let shader = this.shader;\n\n        const pluginName = this.pluginName;\n\n        if (!shader)\n        {\n            // if there is no shader here, we can use the default shader.\n            // and that only gets created if we actually need it..\n            // but may be more than one plugins for graphics\n            if (!DEFAULT_SHADERS[pluginName])\n            {\n                const { MAX_TEXTURES } = renderer.plugins[pluginName];\n                const sampleValues = new Int32Array(MAX_TEXTURES);\n\n                for (let i = 0; i < MAX_TEXTURES; i++)\n                {\n                    sampleValues[i] = i;\n                }\n\n                const uniforms = {\n                    tint: new Float32Array([1, 1, 1, 1]),\n                    translationMatrix: new Matrix(),\n                    default: UniformGroup.from({ uSamplers: sampleValues }, true),\n                };\n\n                const program = renderer.plugins[pluginName]._shader.program;\n\n                DEFAULT_SHADERS[pluginName] = new Shader(program, uniforms);\n            }\n\n            shader = DEFAULT_SHADERS[pluginName];\n        }\n\n        return shader;\n    }\n\n    /** Retrieves the bounds of the graphic shape as a rectangle object. */\n    protected _calculateBounds(): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n\n        // skipping when graphics is empty, like a container\n        if (!geometry.graphicsData.length)\n        {\n            return;\n        }\n\n        const { minX, minY, maxX, maxY } = geometry.bounds;\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Tests if a point is inside this graphics object\n     * @param point - the point to test\n     * @returns - the result of the test\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, Graphics._TEMP_POINT);\n\n        return this._geometry.containsPoint(Graphics._TEMP_POINT);\n    }\n\n    /** Recalculate the tint by applying tint to batches using Graphics tint. */\n    protected calculateTints(): void\n    {\n        if (this.batchTint !== this.tint)\n        {\n            this.batchTint = this.tint;\n\n            const tintRGB = hex2rgb(this.tint, temp);\n\n            for (let i = 0; i < this.batches.length; i++)\n            {\n                const batch = this.batches[i];\n\n                const batchTint = batch._batchRGB;\n\n                const r = (tintRGB[0] * batchTint[0]) * 255;\n                const g = (tintRGB[1] * batchTint[1]) * 255;\n                const b = (tintRGB[2] * batchTint[2]) * 255;\n\n                // TODO Ivan, can this be done in one go?\n                const color = (r << 16) + (g << 8) + (b | 0);\n\n                batch._tintRGB = (color >> 16)\n                        + (color & 0xff00)\n                        + ((color & 0xff) << 16);\n            }\n        }\n    }\n\n    /** If there's a transform update or a change to the shape of the geometry, recalculate the vertices. */\n    protected calculateVertices(): void\n    {\n        const wtID = this.transform._worldID;\n\n        if (this._transformID === wtID)\n        {\n            return;\n        }\n\n        this._transformID = wtID;\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const data = this._geometry.points;// batch.vertexDataOriginal;\n        const vertexData = this.vertexData;\n\n        let count = 0;\n\n        for (let i = 0; i < data.length; i += 2)\n        {\n            const x = data[i];\n            const y = data[i + 1];\n\n            vertexData[count++] = (a * x) + (c * y) + tx;\n            vertexData[count++] = (d * y) + (b * x) + ty;\n        }\n    }\n\n    /**\n     * Closes the current path.\n     * @returns - Returns itself.\n     */\n    public closePath(): this\n    {\n        const currentPath = this.currentPath;\n\n        if (currentPath)\n        {\n            // we don't need to add extra point in the end because buildLine will take care of that\n            currentPath.closeStroke = true;\n            // ensure that the polygon is completed, and is available for hit detection\n            // (even if the graphics is not rendered yet)\n            this.finishPoly();\n        }\n\n        return this;\n    }\n\n    /**\n     * Apply a matrix to the positional data.\n     * @param matrix - Matrix to use for transform current shape.\n     * @returns - Returns itself.\n     */\n    public setMatrix(matrix: Matrix): this\n    {\n        this._matrix = matrix;\n\n        return this;\n    }\n\n    /**\n     * Begin adding holes to the last draw shape\n     * IMPORTANT: holes must be fully inside a shape to work\n     * Also weirdness ensues if holes overlap!\n     * Ellipses, Circles, Rectangles and Rounded Rectangles cannot be holes or host for holes in CanvasRenderer,\n     * please use `moveTo` `lineTo`, `quadraticCurveTo` if you rely on pixi-legacy bundle.\n     * @returns - Returns itself.\n     */\n    public beginHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = true;\n\n        return this;\n    }\n\n    /**\n     * End adding holes to the last draw shape.\n     * @returns - Returns itself.\n     */\n    public endHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = false;\n\n        return this;\n    }\n\n    /**\n     * Destroys the Graphics object.\n     * @param options - Options parameter. A boolean will act as if all\n     *  options have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have\n     *  their destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this._geometry.refCount--;\n        if (this._geometry.refCount === 0)\n        {\n            this._geometry.dispose();\n        }\n\n        this._matrix = null;\n        this.currentPath = null;\n        this._lineStyle.destroy();\n        this._lineStyle = null;\n        this._fillStyle.destroy();\n        this._fillStyle = null;\n        this._geometry = null;\n        this.shader = null;\n        this.vertexData = null;\n        this.batches.length = 0;\n        this.batches = null;\n\n        super.destroy(options);\n    }\n}\n", "export * from './const';\nexport * from './styles/FillStyle';\nexport * from './Graphics';\nexport * from './GraphicsData';\nexport * from './GraphicsGeometry';\nexport * from './styles/LineStyle';\n\nimport {\n    buildPoly,\n    buildCircle,\n    buildRectangle,\n    buildRoundedRectangle,\n    buildLine,\n    ArcUtils,\n    BezierUtils,\n    QuadraticUtils,\n    BatchPart,\n    FILL_COMMANDS,\n    BATCH_POOL,\n    DRAW_CALL_POOL\n} from './utils';\nimport type { BatchDrawCall } from '@pixi/core/';\nimport type { IShapeBuildCommand } from './utils/IShapeBuildCommand';\nimport type { SHAPES } from '@pixi/math';\n\nexport const graphicsUtils = {\n    buildPoly: buildPoly as IShapeBuildCommand,\n    buildCircle: buildCircle as IShapeBuildCommand,\n    buildRectangle: buildRectangle as IShapeBuildCommand,\n    buildRoundedRectangle: buildRoundedRectangle as IShapeBuildCommand,\n    buildLine,\n    ArcUtils,\n    BezierUtils,\n    QuadraticUtils,\n    BatchPart,\n    FILL_COMMANDS: FILL_COMMANDS as Record<SHAPES, IShapeBuildCommand>,\n    BATCH_POOL: BATCH_POOL as Array<BatchPart>,\n    DRAW_CALL_POOL: DRAW_CALL_POOL as Array<BatchDrawCall>\n};\n"], "names": ["LINE_JOIN", "LINE_CAP", "GRAPHICS_CURVES", "adaptive", "max<PERSON><PERSON><PERSON>", "minSegments", "maxSegments", "epsilon", "_segmentsCount", "length", "defaultSegments", "this", "isNaN", "result", "Math", "ceil", "FillStyle", "color", "alpha", "texture", "Texture", "WHITE", "matrix", "visible", "reset", "prototype", "clone", "obj", "destroy", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "constructor", "create", "fixOrientation", "points", "hole", "m", "area", "i", "x1", "y1", "x2", "y2", "n", "i1", "i2", "i3", "i4", "_a", "_b", "buildPoly", "build", "graphicsData", "shape", "slice", "triangulate", "graphicsGeometry", "holes", "verts", "indices", "holeArray", "push", "concat", "triangles", "earcut", "vertPos", "buildCircle", "x", "y", "dx", "dy", "rx", "ry", "type", "SHAPES", "CIRC", "circle", "radius", "ELIP", "ellipse", "width", "height", "roundedRect", "halfWidth", "halfHeight", "max", "min", "sqrt", "j1", "j2", "j3", "j4", "x0", "y0", "a", "PI", "cos", "sin", "center", "RREC", "c", "tx", "ty", "buildRectangle", "rectData", "getPt", "n1", "n2", "perc", "quadraticBezierCurve", "fromX", "fromY", "cpX", "cpY", "toX", "toY", "out", "xa", "ya", "xb", "yb", "j", "buildRoundedRectangle", "Graphics", "nextRoundedRectBehavior", "rrectData", "vecPos", "square", "nx", "ny", "innerWeight", "outerWeight", "clockwise", "exx", "eyy", "eix", "eiy", "eox", "eoy", "round", "cx", "cy", "sx", "sy", "ex", "ey", "cx2p0x", "cy2p0y", "angle0", "atan2", "angle1", "startAngle", "angleDiff", "absAngleDiff", "abs", "segCount", "angleInc", "angle", "buildLine", "lineStyle", "native", "closedShape", "POLY", "closeStroke", "startIndex", "currentIndex", "buildNativeLine", "eps", "closePointEps", "style", "firstPoint", "Point", "lastPoint", "closedPath", "pop", "set", "midPointX", "midPointY", "unshift", "indexCount", "indexStart", "widthSquared", "miterLimitSquared", "miterLimit", "perpx", "perpy", "perp1x", "perp1y", "dist", "ratio", "alignment", "cap", "ROUND", "SQUARE", "dx0", "dy0", "dx1", "dy1", "dot", "cross", "join", "c1", "c2", "px", "py", "pdist", "imx", "imy", "omx", "omy", "insideWeight", "BEVEL", "MITER", "eps2", "buildNonNativeLine", "ArcUtils", "curveTo", "a1", "b1", "a2", "b2", "mm", "dd", "cc", "tt", "k1", "k2", "qx", "qy", "endAngle", "anticlockwise", "arc", "_startX", "_startY", "_anticlockwise", "sweep", "PI_2", "theta", "theta2", "c<PERSON><PERSON><PERSON>", "s<PERSON><PERSON>ta", "segMinus", "remainder", "s", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "curveLength", "cpX2", "cpY2", "t", "t2", "t3", "nt", "nt2", "nt3", "prevX", "prevY", "dt", "dt2", "dt3", "QuadraticUtils", "ax", "ay", "bx", "by", "a32", "ba", "log", "<PERSON><PERSON><PERSON>art", "begin", "attribStart", "start", "end", "endIndex", "endAttrib", "attribSize", "size", "FILL_COMMANDS", "RECT", "BATCH_POOL", "DRAW_CALL_POOL", "GraphicsData", "fillStyle", "tmpPoint", "GraphicsGeometry", "_super", "_this", "boundsPadding", "uvsFloat32", "indicesUint16", "batchable", "colors", "uvs", "textureIds", "drawCalls", "batchDirty", "batches", "dirty", "cacheDirty", "clearDirty", "shapeIndex", "_bounds", "Bounds", "boundsDirty", "defineProperty", "get", "updateBatches", "calculateBounds", "invalidate", "texArray", "clear", "batchPart", "drawShape", "data", "drawHole", "lastShape", "indexBuffer", "containsPoint", "point", "applyInverse", "copyFrom", "contains", "hitHole", "i_1", "validateBatching", "currentStyle", "transformPoints", "processHoles", "nextTexture", "baseTexture", "index_1", "attribIndex", "wrapMode", "WRAP_MODES", "REPEAT", "processFill", "processLine", "_compareStyles", "addUvs", "index", "attrib", "need32", "BYTES_PER_ELEMENT", "Uint32Array", "Uint16Array", "isBatchable", "packBatches", "buildDrawCalls", "styleA", "styleB", "l", "fill", "line", "valid", "Float32Array", "batch", "BATCHABLE_SIZE", "TICK", "BaseTexture", "_globalBatch", "currentGroup", "BatchDrawCall", "BatchTextureArray", "count", "DRAW_MODES", "TRIANGLES", "textureCount", "currentTexture", "textureId", "drawMode", "LINES", "_batchEnabled", "touched", "_batchLocation", "elements", "addColors", "addTextureIds", "packAttributes", "glPoints", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f32", "u32", "_buffer", "update", "_indexBuffer", "bounds", "addVertexData", "pad", "offset", "rgb", "rgba", "premultiplyTint", "id", "uvsStart", "frame", "adjustUvs", "finish", "scaleX", "scaleY", "offsetX", "offsetY", "minX", "floor", "minY", "BatchGeometry", "LineStyle", "apply", "arguments", "BUTT", "temp", "DEFAULT_SHADERS", "geometry", "shader", "pluginName", "currentPath", "batchTint", "vertexData", "_fillStyle", "_lineStyle", "_matrix", "_holeMode", "state", "State", "for2d", "_geometry", "refCount", "_transformID", "tint", "blendMode", "BLEND_MODES", "NORMAL", "finishPoly", "value", "_tint", "options", "lineTextureStyle", "assign", "startPoly", "invert", "len", "Polygon", "moveTo", "lineTo", "_initCurve", "quadraticCurveTo", "bezierCurveTo", "arcTo", "radius_1", "startX", "startY", "xDiff", "yDiff", "beginFill", "beginTextureFill", "endFill", "drawRect", "Rectangle", "drawRoundedRect", "RoundedRectangle", "drawCircle", "Circle", "draw<PERSON><PERSON><PERSON>", "Ellipse", "drawPolygon", "path", "_i", "poly", "isArray", "_boundsID", "isFastRect", "_render", "renderer", "_populateBatches", "_renderBatched", "flush", "_renderDirect", "gI", "buffer", "_batchRGB", "hex2rgb", "_tintRGB", "_texture", "worldAlpha", "setObjectR<PERSON><PERSON>", "plugins", "calculateVertices", "calculateTints", "render", "_resolveDirectShader", "uniforms", "translationMatrix", "transform", "worldTransform", "bind", "_renderDrawCallDirect", "drawCall", "groupTextureCount", "draw", "MAX_TEXTURES", "sampleValues", "Int32Array", "Matrix", "default", "UniformGroup", "from", "uSamplers", "program", "_shader", "Shader", "_calculateBounds", "maxX", "maxY", "addFrame", "_TEMP_POINT", "tintRGB", "wtID", "_worldID", "wt", "closePath", "setMatrix", "beginHole", "endHole", "dispose", "call", "Container", "graphicsUtils"], "mappings": ";;;;;;;wEAYYA,EAmBAC,mIAnBAD,QAMXA,eAAA,GANWA,EAAAA,oBAAAA,QAAAA,UAMX,KAHG,MAAA,QACAA,EAAA,MAAA,QACAA,EAAA,MAAA,QAcQC,QAMXA,cAAA,GANWA,EAAAA,mBAAAA,QAAAA,SAMX,KAHG,KAAA,OACAA,EAAA,MAAA,QACAA,EAAA,OAAA,SA6BG,IAAMC,EAA2C,CACpDC,UAAU,EACVC,UAAW,GACXC,YAAa,EACbC,YAAc,KAEdC,QAAS,KAETC,eAAA,SAAeC,EAAgBC,GAE3B,QAF2B,IAAAA,IAAAA,EAAoB,KAE1CC,KAAKR,WAAaM,GAAUG,MAAMH,GAEnC,OAAOC,EAGX,IAAIG,EAASC,KAAKC,KAAKN,EAASE,KAAKP,WAWrC,OATIS,EAASF,KAAKN,YAEdQ,EAASF,KAAKN,YAETQ,EAASF,KAAKL,cAEnBO,EAASF,KAAKL,aAGXO,ICpFfG,EAAA,WA0BI,SAAAA,IApBOL,KAAKM,MAAG,SAGRN,KAAKO,MAAG,EAMRP,KAAAQ,QAAmBC,EAAOA,QAACC,MAM3BV,KAAMW,OAAW,KAGjBX,KAAOY,SAAG,EAIbZ,KAAKa,QAiCb,OA7BWR,EAAAS,UAAAC,MAAP,WAEI,IAAMC,EAAM,IAAIX,EAQhB,OANAW,EAAIV,MAAQN,KAAKM,MACjBU,EAAIT,MAAQP,KAAKO,MACjBS,EAAIR,QAAUR,KAAKQ,QACnBQ,EAAIL,OAASX,KAAKW,OAClBK,EAAIJ,QAAUZ,KAAKY,QAEZI,GAIJX,EAAAS,UAAAD,MAAP,WAEIb,KAAKM,MAAQ,SACbN,KAAKO,MAAQ,EACbP,KAAKQ,QAAUC,EAAOA,QAACC,MACvBV,KAAKW,OAAS,KACdX,KAAKY,SAAU,GAIZP,EAAAS,UAAAG,QAAP,WAEIjB,KAAKQ,QAAU,KACfR,KAAKW,OAAS,MAErBN,KCpDGa,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,IAGrB,SAASO,EAAUR,EAAGC,GAEzB,SAASQ,IAAO5B,KAAK6B,YAAcV,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEL,UAAkB,OAANM,EAAaC,OAAOS,OAAOV,IAAMQ,EAAGd,UAAYM,EAAEN,UAAW,IAAIc,GCrBnF,SAASG,EAAeC,EAAkBC,gBAAA,IAAAA,IAAAA,GAAY,GAElD,IAAMC,EAAIF,EAAOlC,OAEjB,KAAIoC,EAAI,GAAR,CAOA,IAFA,IAAIC,EAAO,EAEFC,EAAI,EAAGC,EAAKL,EAAOE,EAAI,GAAII,EAAKN,EAAOE,EAAI,GAAIE,EAAIF,EAAGE,GAAK,EACpE,CACI,IAAMG,EAAKP,EAAOI,GACZI,EAAKR,EAAOI,EAAI,GAEtBD,IAASI,EAAKF,IAAOG,EAAKF,GAE1BD,EAAKE,EACLD,EAAKE,EAGT,IAAMP,GAAQE,EAAO,GAAOF,GAAQE,GAAQ,EAExC,CAAA,IAAMM,EAAIP,EAAI,EAEd,IAASE,EAAIK,EAAKA,EAAI,EAAIL,EAAIF,EAAGE,GAAK,EACtC,CACI,IAAMM,EAAKR,EAAIE,EAAI,EACbO,EAAKT,EAAIE,EAAI,EACbQ,EAAKR,EACLS,EAAKT,EAAI,EAEfU,EAA2B,CAACd,EAAOY,GAAKZ,EAAOU,IAA9CV,EAAOU,GAAGI,EAAA,GAAEd,EAAOY,GAAGE,EAAA,GACvBC,EAA2B,CAACf,EAAOa,GAAKb,EAAOW,IAA9CX,EAAOW,GAAGI,EAAA,GAAEf,EAAOa,GAAGE,EAAA,MAc5B,IAAMC,EAAgC,CAEzCC,MAAA,SAAMC,GAEFA,EAAalB,OAAUkB,EAAaC,MAAkBnB,OAAOoB,SAGjEC,YAAW,SAACH,EAAcI,GAEtB,IAAItB,EAASkB,EAAalB,OACpBuB,EAAQL,EAAaK,MACrBC,EAAQF,EAAiBtB,OACzByB,EAAUH,EAAiBG,QAEjC,GAAIzB,EAAOlC,QAAU,EACrB,CACIiC,EAAeC,GAAQ,GAKvB,IAHA,IAAM0B,EAAY,GAGTtB,EAAI,EAAGA,EAAImB,EAAMzD,OAAQsC,IAClC,CACI,IAAMH,EAAOsB,EAAMnB,GAEnBL,EAAeE,EAAKD,QAAQ,GAE5B0B,EAAUC,KAAK3B,EAAOlC,OAAS,GAC/BkC,EAASA,EAAO4B,OAAO3B,EAAKD,QAIhC,IAAM6B,EAAYC,EAAMA,OAAC9B,EAAQ0B,EAAW,GAE5C,IAAKG,EAED,OAGJ,IAAME,EAAUP,EAAM1D,OAAS,EAE/B,IAASsC,EAAI,EAAGA,EAAIyB,EAAU/D,OAAQsC,GAAK,EAEvCqB,EAAQE,KAAKE,EAAUzB,GAAK2B,GAC5BN,EAAQE,KAAKE,EAAUzB,EAAI,GAAK2B,GAChCN,EAAQE,KAAKE,EAAUzB,EAAI,GAAK2B,GAGpC,IAAS3B,EAAI,EAAGA,EAAIJ,EAAOlC,OAAQsC,IAE/BoB,EAAMG,KAAK3B,EAAOI,OCvFrB4B,EAAkC,CAE3Cf,MAAA,SAAMC,GAGF,IAEIe,EACAC,EACAC,EACAC,EACAC,EACAC,EAPEtC,EAASkB,EAAalB,OAS5B,GAAIkB,EAAaqB,OAASC,EAAMA,OAACC,KACjC,CACI,IAAMC,EAASxB,EAAaC,MAE5Bc,EAAIS,EAAOT,EACXC,EAAIQ,EAAOR,EACXG,EAAKC,EAAKI,EAAOC,OACjBR,EAAKC,EAAK,OAET,GAAIlB,EAAaqB,OAASC,EAAMA,OAACI,KACtC,CACI,IAAMC,EAAU3B,EAAaC,MAE7Bc,EAAIY,EAAQZ,EACZC,EAAIW,EAAQX,EACZG,EAAKQ,EAAQC,MACbR,EAAKO,EAAQE,OACbZ,EAAKC,EAAK,MAGd,CACI,IAAMY,EAAc9B,EAAaC,MAC3B8B,EAAYD,EAAYF,MAAQ,EAChCI,EAAaF,EAAYD,OAAS,EAExCd,EAAIe,EAAYf,EAAIgB,EACpBf,EAAIc,EAAYd,EAAIgB,EAEpBf,EAAKc,GADLZ,EAAKC,EAAKnE,KAAKgF,IAAI,EAAGhF,KAAKiF,IAAIJ,EAAYL,OAAQxE,KAAKiF,IAAIH,EAAWC,MAEvEd,EAAKc,EAAaZ,EAGtB,GAAMD,GAAM,GAAKC,GAAM,GAAKH,GAAM,GAAKC,GAAM,EAA7C,CAQA,IAAM3B,EAAItC,KAAKC,KAAK,IAAMD,KAAKkF,KAAKhB,EAAKC,IACnCpC,EAAS,EAAJO,GAAU0B,EAAK,EAAI,IAAMC,EAAK,EAAI,GAI7C,GAFApC,EAAOlC,OAASoC,EAEN,IAANA,EAAJ,CAKA,GAAU,IAANO,EAQA,OANAT,EAAOlC,OAAS,EAChBkC,EAAO,GAAKA,EAAO,GAAKiC,EAAIE,EAC5BnC,EAAO,GAAKA,EAAO,GAAKkC,EAAIE,EAC5BpC,EAAO,GAAKA,EAAO,GAAKiC,EAAIE,OAC5BnC,EAAO,GAAKA,EAAO,GAAKkC,EAAIE,GAKhC,IAAIkB,EAAK,EACLC,EAAU,EAAJ9C,GAAU0B,EAAK,EAAI,GAAK,EAC9BqB,EAAKD,EACLE,EAAKvD,EAKCG,EAAK4B,GAFLyB,EAAKvB,EAAKE,GAGV9B,EAAK0B,EAAIyB,EACTpD,EAAK4B,GAHLyB,EAAKvB,GAUX,GALApC,EAAOsD,KAAQjD,EACfL,EAAOsD,KAAQhD,EACfN,IAASuD,GAAMjD,EACfN,IAASuD,GAAMhD,EAEX6B,EACJ,CACI,IAAM5B,EAAK0B,EAAIyB,EAEf3D,EAAOwD,KAAQjD,EACfP,EAAOwD,KAAQhD,EACfR,IAASyD,GAAMjD,EACfR,IAASyD,GAAMpD,EAIvB,IAAK,IAAID,EAAI,EAAGA,EAAIK,EAAGL,IACvB,CACI,IAAMwD,EAAIzF,KAAK0F,GAAK,GAAKzD,EAAIK,GAGvBJ,EAAK4B,GAFLyB,EAAKvB,EAAMhE,KAAK2F,IAAIF,GAAKvB,GAGzB9B,EAAK0B,EAAIyB,EACTpD,EAAK4B,GAHLyB,EAAKvB,EAAMjE,KAAK4F,IAAIH,GAAKtB,GAIzB9B,EAAK0B,EAAIyB,EAEf3D,EAAOsD,KAAQjD,EACfL,EAAOsD,KAAQhD,EACfN,IAASuD,GAAMjD,EACfN,IAASuD,GAAMhD,EACfP,EAAOwD,KAAQjD,EACfP,EAAOwD,KAAQhD,EACfR,IAASyD,GAAMjD,EACfR,IAASyD,GAAMpD,EAIf,IAAMqD,EACAC,EACAtD,EAAK4B,GAFLyB,EAAKvB,GAGL5B,EAAK0B,EAAIyB,EACTpD,EAAK4B,GAHLyB,EAAKvB,EAAKE,GAIV9B,EAAK0B,EAAIyB,EAEf3D,EAAOsD,KAAQjD,EACfL,EAAOsD,KAAQhD,EACfN,IAASyD,GAAMjD,EACfR,IAASyD,GAAMpD,EAEX8B,IAEAnC,EAAOsD,KAAQ/C,EACfP,EAAOsD,KAAQhD,EACfN,IAASyD,GAAMjD,EACfR,IAASyD,GAAMlD,SA7FnBP,EAAOlC,OAAS,GAkGxBuD,YAAA,SAAYH,EAAcI,GAEtB,IAAMtB,EAASkB,EAAalB,OACtBwB,EAAQF,EAAiBtB,OACzByB,EAAUH,EAAiBG,QAEjC,GAAsB,IAAlBzB,EAAOlC,OAAX,CAKA,IAGImE,EACAC,EAJAH,EAAUP,EAAM1D,OAAS,EACvBkG,EAASjC,EAKf,GAAIb,EAAaqB,OAASC,EAAMA,OAACyB,KACjC,CACI,IAAMvB,EAASxB,EAAaC,MAE5Bc,EAAIS,EAAOT,EACXC,EAAIQ,EAAOR,MAGf,CACI,IAAMc,EAAc9B,EAAaC,MAEjCc,EAAIe,EAAYf,EAAKe,EAAYF,MAAQ,EACzCZ,EAAIc,EAAYd,EAAKc,EAAYD,OAAS,EAG9C,IAAMpE,EAASuC,EAAavC,OAG5B6C,EAAMG,KACFT,EAAavC,OAAUA,EAAOiF,EAAI3B,EAAMtD,EAAOuF,EAAIhC,EAAKvD,EAAOwF,GAAKlC,EACpEf,EAAavC,OAAUA,EAAOS,EAAI6C,EAAMtD,EAAOQ,EAAI+C,EAAKvD,EAAOyF,GAAKlC,GAExEH,IAEAP,EAAMG,KAAK3B,EAAO,GAAIA,EAAO,IAE7B,IAAK,IAAII,EAAI,EAAGA,EAAIJ,EAAOlC,OAAQsC,GAAK,EAEpCoB,EAAMG,KAAK3B,EAAOI,GAAIJ,EAAOI,EAAI,IAGjCqB,EAAQE,KAAKI,IAAWiC,EAAQjC,GAGpCN,EAAQE,KAAKqC,EAAS,EAAGA,EAAQjC,MCxM5BsC,EAAqC,CAE9CpD,MAAA,SAAMC,GAKF,IAAMoD,EAAWpD,EAAaC,MACxBc,EAAIqC,EAASrC,EACbC,EAAIoC,EAASpC,EACbY,EAAQwB,EAASxB,MACjBC,EAASuB,EAASvB,OAElB/C,EAASkB,EAAalB,OAE5BA,EAAOlC,OAAS,EAEhBkC,EAAO2B,KAAKM,EAAGC,EACXD,EAAIa,EAAOZ,EACXD,EAAIa,EAAOZ,EAAIa,EACfd,EAAGC,EAAIa,IAGf1B,YAAW,SAACH,EAAcI,GAEtB,IAAMtB,EAASkB,EAAalB,OACtBwB,EAAQF,EAAiBtB,OAEzB+B,EAAUP,EAAM1D,OAAS,EAE/B0D,EAAMG,KAAK3B,EAAO,GAAIA,EAAO,GACzBA,EAAO,GAAIA,EAAO,GAClBA,EAAO,GAAIA,EAAO,GAClBA,EAAO,GAAIA,EAAO,IAEtBsB,EAAiBG,QAAQE,KAAKI,EAASA,EAAU,EAAGA,EAAU,EAC1DA,EAAU,EAAGA,EAAU,EAAGA,EAAU,KC9BhD,SAASwC,EAAMC,EAAYC,EAAYC,GAInC,OAAOF,GAFMC,EAAKD,GAEEE,EAmBxB,SAASC,EACLC,EAAeC,EACfC,EAAaC,EACbC,EAAaC,EACbC,QAAA,IAAAA,IAAAA,EAAuB,IAYvB,IAVA,IACMlF,EAASkF,EAEXC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLrD,EAAI,EACJC,EAAI,EAEC9B,EAAI,EAAGmF,EAAI,EAAGnF,GAVb,KAUuBA,EAK7B+E,EAAKZ,EAAMK,EAAOE,EAHlBS,EAAInF,EAZE,IAgBNgF,EAAKb,EAAMM,EAAOE,EAAKQ,GACvBF,EAAKd,EAAMO,EAAKE,EAAKO,GACrBD,EAAKf,EAAMQ,EAAKE,EAAKM,GAGrBtD,EAAIsC,EAAMY,EAAIE,EAAIE,GAClBrD,EAAIqC,EAAMa,EAAIE,EAAIC,GAGR,IAANnF,GAAWJ,EAAOA,EAAOlC,OAAS,KAAOmE,GAAKjC,EAAOA,EAAOlC,OAAS,KAAOoE,GAKhFlC,EAAO2B,KAAKM,EAAGC,GAGnB,OAAOlC,EAaJ,IAAMwF,EAA4C,CAErDvE,MAAA,SAAMC,GAEF,GAAIuE,EAASC,wBAET1D,EAAYf,MAAMC,OAFtB,CAOA,IAAMyE,EAAYzE,EAAaC,MACzBnB,EAASkB,EAAalB,OACtBiC,EAAI0D,EAAU1D,EACdC,EAAIyD,EAAUzD,EACdY,EAAQ6C,EAAU7C,MAClBC,EAAS4C,EAAU5C,OAGnBJ,EAASxE,KAAKgF,IAAI,EAAGhF,KAAKiF,IAAIuC,EAAUhD,OAAQxE,KAAKiF,IAAIN,EAAOC,GAAU,IAEhF/C,EAAOlC,OAAS,EAGX6E,GASDgC,EAAqB1C,EAAGC,EAAIS,EACxBV,EAAGC,EACHD,EAAIU,EAAQT,EACZlC,GACJ2E,EAAqB1C,EAAIa,EAAQH,EAC7BT,EAAGD,EAAIa,EAAOZ,EACdD,EAAIa,EAAOZ,EAAIS,EACf3C,GACJ2E,EAAqB1C,EAAIa,EAAOZ,EAAIa,EAASJ,EACzCV,EAAIa,EAAOZ,EAAIa,EACfd,EAAIa,EAAQH,EAAQT,EAAIa,EACxB/C,GACJ2E,EAAqB1C,EAAIU,EAAQT,EAAIa,EACjCd,EAAGC,EAAIa,EACPd,EAAGC,EAAIa,EAASJ,EAChB3C,IAtBJA,EAAO2B,KAAKM,EAAGC,EACXD,EAAIa,EAAOZ,EACXD,EAAIa,EAAOZ,EAAIa,EACfd,EAAGC,EAAIa,KAuBnB1B,YAAW,SAACH,EAAcI,GAEtB,GAAImE,EAASC,wBAET1D,EAAYX,YAAYH,EAAcI,OAF1C,CAgBA,IATA,IAAMtB,EAASkB,EAAalB,OAEtBwB,EAAQF,EAAiBtB,OACzByB,EAAUH,EAAiBG,QAE3BmE,EAASpE,EAAM1D,OAAS,EAExB+D,EAAYC,EAAMA,OAAC9B,EAAQ,KAAM,GAE9BI,EAAI,EAAGmF,EAAI1D,EAAU/D,OAAQsC,EAAImF,EAAGnF,GAAK,EAE9CqB,EAAQE,KAAKE,EAAUzB,GAAKwF,GAE5BnE,EAAQE,KAAKE,EAAUzB,EAAI,GAAKwF,GAEhCnE,EAAQE,KAAKE,EAAUzB,EAAI,GAAKwF,GAGpC,IAASxF,EAAI,EAAGmF,EAAIvF,EAAOlC,OAAQsC,EAAImF,EAAGnF,IAEtCoB,EAAMG,KAAK3B,EAAOI,GAAIJ,IAASI,OCxJ3C,SAASyF,EACL5D,EACAC,EACA4D,EACAC,EACAC,EACAC,EACAC,EACA1E,GAGA,IAMI2E,EACAC,EAEAF,GAEAC,EAAMJ,EACNK,GAAON,IAIPK,GAAOJ,EACPK,EAAMN,GAIV,IAAMO,EArBKpE,EAAK6D,EAAKE,EAqBJG,EACXG,EArBKpE,EAAK6D,EAAKC,EAqBJI,EACXG,EArBKtE,EAAK6D,EAAKG,EAqBJE,EACXK,EArBKtE,EAAK6D,EAAKE,EAqBJG,EAMjB,OAHA5E,EAAMG,KAAK0E,EAAKC,GAChB9E,EAAMG,KAAK4E,EAAKC,GAET,EAmBX,SAASC,EACLC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAvF,EACA0E,GAGA,IAAMc,EAASJ,EAAKF,EACdO,EAASJ,EAAKF,EAEhBO,EAAS/I,KAAKgJ,MAAMH,EAAQC,GAC5BG,EAASjJ,KAAKgJ,MAAML,EAAKJ,EAAIK,EAAKJ,GAElCT,GAAagB,EAASE,EAEtBF,GAAoB,EAAV/I,KAAK0F,IAETqC,GAAagB,EAASE,IAE5BA,GAAoB,EAAVjJ,KAAK0F,IAGnB,IAAIwD,EAAaH,EACXI,EAAYF,EAASF,EACrBK,EAAepJ,KAAKqJ,IAAIF,GAoBxB3E,EAASxE,KAAKkF,KAAM2D,EAASA,EAAWC,EAASA,GACjDQ,EAAsE,GAAzD,GAAKF,EAAepJ,KAAKkF,KAAKV,GAAUxE,KAAK0F,IAAO,GACjE6D,EAAWJ,EAAYG,EAI7B,GAFAJ,GAAcK,EAEVxB,EACJ,CACI1E,EAAMG,KAAK+E,EAAIC,GACfnF,EAAMG,KAAKiF,EAAIC,GAEf,IAAK,IAAIzG,EAAI,EAAGuH,EAAQN,EAAYjH,EAAIqH,EAAUrH,IAAKuH,GAASD,EAE5DlG,EAAMG,KAAK+E,EAAIC,GACfnF,EAAMG,KAAK+E,EAAOvI,KAAK4F,IAAI4D,GAAShF,EAChCgE,EAAOxI,KAAK2F,IAAI6D,GAAShF,GAGjCnB,EAAMG,KAAK+E,EAAIC,GACfnF,EAAMG,KAAKmF,EAAIC,OAGnB,CACIvF,EAAMG,KAAKiF,EAAIC,GACfrF,EAAMG,KAAK+E,EAAIC,GAEf,IAASvG,EAAI,EAAGuH,EAAQN,EAAYjH,EAAIqH,EAAUrH,IAAKuH,GAASD,EAE5DlG,EAAMG,KAAK+E,EAAOvI,KAAK4F,IAAI4D,GAAShF,EAChCgE,EAAOxI,KAAK2F,IAAI6D,GAAShF,GAC7BnB,EAAMG,KAAK+E,EAAIC,GAGnBnF,EAAMG,KAAKmF,EAAIC,GACfvF,EAAMG,KAAK+E,EAAIC,GAGnB,OAAkB,EAAXc,EA4bK,SAAAG,EAAU1G,EAA4BI,GAE9CJ,EAAa2G,UAAUC,OA5C/B,SAAyB5G,EAA4BI,GAEjD,IAAIlB,EAAI,EAEFe,EAAQD,EAAaC,MACrBnB,EAASkB,EAAalB,QAAUmB,EAAMnB,OACtC+H,EAAc5G,EAAMoB,OAASC,EAAMA,OAACwF,MAAQ7G,EAAM8G,YAExD,GAAsB,IAAlBjI,EAAOlC,OAAX,CAEA,IAAM0D,EAAQF,EAAiBtB,OACzByB,EAAUH,EAAiBG,QAC3B3D,EAASkC,EAAOlC,OAAS,EAEzBoK,EAAa1G,EAAM1D,OAAS,EAC9BqK,EAAeD,EAInB,IAFA1G,EAAMG,KAAK3B,EAAO,GAAIA,EAAO,IAExBI,EAAI,EAAGA,EAAItC,EAAQsC,IAEpBoB,EAAMG,KAAK3B,EAAW,EAAJI,GAAQJ,EAAY,EAAJI,EAAS,IAC3CqB,EAAQE,KAAKwG,EAAcA,EAAe,GAE1CA,IAGAJ,GAEAtG,EAAQE,KAAKwG,EAAcD,IAiB3BE,CAAgBlH,EAAcI,GApbtC,SAA4BJ,EAA4BI,GAEpD,IAAMH,EAAQD,EAAaC,MACvBnB,EAASkB,EAAalB,QAAUmB,EAAMnB,OAAOoB,QAC3CiH,EAAM/G,EAAiBgH,cAE7B,GAAsB,IAAlBtI,EAAOlC,OAAX,CAcA,IAAMyK,EAAQrH,EAAa2G,UAGrBW,EAAa,IAAIC,EAAAA,MAAMzI,EAAO,GAAIA,EAAO,IACzC0I,EAAY,IAAID,EAAAA,MAAMzI,EAAOA,EAAOlC,OAAS,GAAIkC,EAAOA,EAAOlC,OAAS,IACxEiK,EAAc5G,EAAMoB,OAASC,EAAMA,OAACwF,MAAQ7G,EAAM8G,YAClDU,EAAaxK,KAAKqJ,IAAIgB,EAAWvG,EAAIyG,EAAUzG,GAAKoG,GACnDlK,KAAKqJ,IAAIgB,EAAWtG,EAAIwG,EAAUxG,GAAKmG,EAG9C,GAAIN,EACJ,CAEI/H,EAASA,EAAOoB,QAEZuH,IAEA3I,EAAO4I,MACP5I,EAAO4I,MACPF,EAAUG,IAAI7I,EAAOA,EAAOlC,OAAS,GAAIkC,EAAOA,EAAOlC,OAAS,KAGpE,IAAMgL,EAA2C,IAA9BN,EAAWvG,EAAIyG,EAAUzG,GACtC8G,EAA2C,IAA9BL,EAAUxG,EAAIsG,EAAWtG,GAE5ClC,EAAOgJ,QAAQF,EAAWC,GAC1B/I,EAAO2B,KAAKmH,EAAWC,GAG3B,IAAMvH,EAAQF,EAAiBtB,OACzBlC,EAASkC,EAAOlC,OAAS,EAC3BmL,EAAajJ,EAAOlC,OAClBoL,EAAa1H,EAAM1D,OAAS,EAG5BgF,EAAQyF,EAAMzF,MAAQ,EACtBqG,EAAerG,EAAQA,EACvBsG,EAAoBb,EAAMc,WAAad,EAAMc,WAG/C3F,EAAK1D,EAAO,GACZ2D,EAAK3D,EAAO,GACZK,EAAKL,EAAO,GACZM,EAAKN,EAAO,GACZO,EAAK,EACLC,EAAK,EAGL8I,IAAU3F,EAAKrD,GACfiJ,EAAQ7F,EAAKrD,EACbmJ,EAAS,EACTC,EAAS,EAETC,EAAOvL,KAAKkF,KAAMiG,EAAQA,EAAUC,EAAQA,GAEhDD,GAASI,EACTH,GAASG,EACTJ,GAASxG,EACTyG,GAASzG,EAET,IAAM6G,EAAQpB,EAAMqB,UACd5D,EAA4B,GAAb,EAAI2D,GACnB1D,EAAsB,EAAR0D,EAEf5B,IAEGQ,EAAMsB,MAAQvM,QAAQA,SAACwM,MAEvBb,GAAcxC,EACV/C,EAAM4F,GAAStD,EAAcC,GAAe,GAC5CtC,EAAM4F,GAASvD,EAAcC,GAAe,GAC5CvC,EAAM4F,EAAQtD,EACdrC,EAAM4F,EAAQvD,EACdtC,EAAM4F,EAAQrD,EACdtC,EAAM4F,EAAQtD,EACdzE,GACA,GACA,EAEC+G,EAAMsB,MAAQvM,QAAQA,SAACyM,SAE5Bd,GAAcpD,EAAOnC,EAAIC,EAAI2F,EAAOC,EAAOvD,EAAaC,GAAa,EAAMzE,KAKnFA,EAAMG,KACF+B,EAAM4F,EAAQtD,EACdrC,EAAM4F,EAAQvD,GAClBxE,EAAMG,KACF+B,EAAM4F,EAAQrD,EACdtC,EAAM4F,EAAQtD,GAElB,IAAK,IAAI7F,EAAI,EAAGA,EAAItC,EAAS,IAAKsC,EAClC,CACIsD,EAAK1D,EAAiB,GAATI,EAAI,IACjBuD,EAAK3D,EAAkB,GAATI,EAAI,GAAU,GAE5BC,EAAKL,EAAW,EAAJI,GACZE,EAAKN,EAAY,EAAJI,EAAS,GAEtBG,EAAKP,EAAiB,GAATI,EAAI,IACjBI,EAAKR,EAAkB,GAATI,EAAI,GAAU,GAE5BkJ,IAAU3F,EAAKrD,GACfiJ,EAAQ7F,EAAKrD,EAGbiJ,GADAI,EAAOvL,KAAKkF,KAAMiG,EAAQA,EAAUC,EAAQA,GAE5CA,GAASG,EACTJ,GAASxG,EACTyG,GAASzG,EAET0G,IAAWlJ,EAAKE,GAChBiJ,EAASpJ,EAAKE,EAGdiJ,GADAE,EAAOvL,KAAKkF,KAAMmG,EAASA,EAAWC,EAASA,GAE/CA,GAAUC,EACVF,GAAU1G,EACV2G,GAAU3G,EAGV,IAAMkH,EAAM3J,EAAKqD,EACXuG,EAAMtG,EAAKrD,EACX4J,EAAM7J,EAAKE,EACX4J,EAAM3J,EAAKF,EAGX8J,EAAOJ,EAAME,EAAQD,EAAME,EAE3BE,EAASJ,EAAMC,EAAQC,EAAMH,EAC7B9D,EAAamE,EAAQ,EAI3B,GAAIlM,KAAKqJ,IAAI6C,GAAS,KAAQlM,KAAKqJ,IAAI4C,GAEnC5I,EAAMG,KACFtB,EAAMiJ,EAAQtD,EACd1F,EAAMiJ,EAAQvD,GAClBxE,EAAMG,KACFtB,EAAMiJ,EAAQrD,EACd3F,EAAMiJ,EAAQtD,GAGdmE,GAAO,IAEH7B,EAAM+B,OAASjN,QAASA,UAACyM,MAEzBb,GAAcxC,EACVpG,EAAIC,EACJD,EAAMiJ,EAAQtD,EAAc1F,EAAMiJ,EAAQvD,EAC1C3F,EAAMmJ,EAASxD,EAAc1F,EAAMmJ,EAASzD,EAC5CxE,GAAO,GAAS,EAIpByH,GAAc,EAGlBzH,EAAMG,KACFtB,EAAMmJ,EAASvD,EACf3F,EAAMmJ,EAASxD,GACnBzE,EAAMG,KACFtB,EAAMmJ,EAASxD,EACf1F,EAAMmJ,EAASzD,QA9B3B,CAqCA,IAAMuE,IAAQjB,EAAQ5F,KAAQ6F,EAAQjJ,KAAUgJ,EAAQjJ,KAAQkJ,EAAQ5F,GAClE6G,IAAQhB,EAASjJ,KAAQkJ,EAASnJ,KAAUkJ,EAASnJ,KAAQoJ,EAASjJ,GACtEiK,GAAOT,EAAMQ,EAAON,EAAMK,GAAOF,EACjCK,GAAOP,EAAMI,EAAON,EAAMO,GAAOH,EACjCM,GAAUF,EAAKpK,IAAOoK,EAAKpK,IAASqK,EAAKpK,IAAOoK,EAAKpK,GAGrDsK,EAAMvK,GAAOoK,EAAKpK,GAAM2F,EACxB6E,EAAMvK,GAAOoK,EAAKpK,GAAM0F,EAExB8E,EAAMzK,GAAOoK,EAAKpK,GAAM4F,EACxB8E,EAAMzK,GAAOoK,EAAKpK,GAAM2F,EAIxB+E,GAAe9E,EAAYF,EAAcC,EAEzB0E,GAHSxM,KAAKiF,IAAK4G,EAAMA,EAAQC,EAAMA,EAAOC,EAAMA,EAAQC,EAAMA,GAE9Ba,GAAeA,GAAe7B,EAKhFZ,EAAM+B,OAASjN,QAASA,UAAC4N,OAASN,EAAQxB,EAAeC,GAErDlD,GAEA1E,EAAMG,KAAKiJ,EAAKC,GAChBrJ,EAAMG,KAAKtB,EAAMiJ,EAAQrD,EAAc3F,EAAMiJ,EAAQtD,GACrDzE,EAAMG,KAAKiJ,EAAKC,GAChBrJ,EAAMG,KAAKtB,EAAMmJ,EAASvD,EAAc3F,EAAMmJ,EAASxD,KAIvDzE,EAAMG,KAAKtB,EAAMiJ,EAAQtD,EAAc1F,EAAMiJ,EAAQvD,GACrDxE,EAAMG,KAAKmJ,EAAKC,GAChBvJ,EAAMG,KAAKtB,EAAMmJ,EAASxD,EAAc1F,EAAMmJ,EAASzD,GACvDxE,EAAMG,KAAKmJ,EAAKC,IAGpB9B,GAAc,GAETV,EAAM+B,OAASjN,QAASA,UAACyM,MAE1B5D,GAEA1E,EAAMG,KAAKiJ,EAAKC,GAChBrJ,EAAMG,KAAKtB,EAAMiJ,EAAQrD,EAAc3F,EAAMiJ,EAAQtD,GAErDgD,GAAcxC,EACVpG,EAAIC,EACJD,EAAMiJ,EAAQrD,EAAc3F,EAAMiJ,EAAQtD,EAC1C5F,EAAMmJ,EAASvD,EAAc3F,EAAMmJ,EAASxD,EAC5CzE,GAAO,GACP,EAEJA,EAAMG,KAAKiJ,EAAKC,GAChBrJ,EAAMG,KAAKtB,EAAMmJ,EAASvD,EAAc3F,EAAMmJ,EAASxD,KAIvDzE,EAAMG,KAAKtB,EAAMiJ,EAAQtD,EAAc1F,EAAMiJ,EAAQvD,GACrDxE,EAAMG,KAAKmJ,EAAKC,GAEhB9B,GAAcxC,EACVpG,EAAIC,EACJD,EAAMiJ,EAAQtD,EAAc1F,EAAMiJ,EAAQvD,EAC1C3F,EAAMmJ,EAASxD,EAAc1F,EAAMmJ,EAASzD,EAC5CxE,GAAO,GACP,EAEJA,EAAMG,KAAKtB,EAAMmJ,EAASxD,EAAc1F,EAAMmJ,EAASzD,GACvDxE,EAAMG,KAAKmJ,EAAKC,KAKpBvJ,EAAMG,KAAKiJ,EAAKC,GAChBrJ,EAAMG,KAAKmJ,EAAKC,KAKpBvJ,EAAMG,KAAKtB,EAAMiJ,EAAQtD,EAAc1F,EAAMiJ,EAAQvD,GACrDxE,EAAMG,KAAKtB,EAAMiJ,EAAQrD,EAAc3F,EAAMiJ,EAAQtD,GACjDsC,EAAM+B,OAASjN,QAASA,UAACyM,MAIrBb,GAFA/C,EAEcO,EACVpG,EAAIC,EACJD,EAAMiJ,EAAQrD,EAAc3F,EAAMiJ,EAAQtD,EAC1C5F,EAAMmJ,EAASvD,EAAc3F,EAAMmJ,EAASxD,EAC5CzE,GAAO,GACP,EAIUiF,EACVpG,EAAIC,EACJD,EAAMiJ,EAAQtD,EAAc1F,EAAMiJ,EAAQvD,EAC1C3F,EAAMmJ,EAASxD,EAAc1F,EAAMmJ,EAASzD,EAC5CxE,GAAO,GACP,EAGH+G,EAAM+B,OAASjN,QAASA,UAAC6N,OAASP,EAAQxB,GAAgBC,IAE3DlD,GAEA1E,EAAMG,KAAKmJ,EAAKC,GAChBvJ,EAAMG,KAAKmJ,EAAKC,KAIhBvJ,EAAMG,KAAKiJ,EAAKC,GAChBrJ,EAAMG,KAAKiJ,EAAKC,IAEpB5B,GAAc,GAElBzH,EAAMG,KAAKtB,EAAMmJ,EAASxD,EAAc1F,EAAMmJ,EAASzD,GACvDxE,EAAMG,KAAKtB,EAAMmJ,EAASvD,EAAc3F,EAAMmJ,EAASxD,GACvDgD,GAAc,IAItBvF,EAAK1D,EAAsB,GAAdlC,EAAS,IACtB6F,EAAK3D,EAAuB,GAAdlC,EAAS,GAAU,GAEjCuC,EAAKL,EAAsB,GAAdlC,EAAS,IAGtBwL,IAAU3F,GAFVrD,EAAKN,EAAuB,GAAdlC,EAAS,GAAU,KAGjCyL,EAAQ7F,EAAKrD,EAGbiJ,GADAI,EAAOvL,KAAKkF,KAAMiG,EAAQA,EAAUC,EAAQA,GAE5CA,GAASG,EACTJ,GAASxG,EACTyG,GAASzG,EAETtB,EAAMG,KAAKtB,EAAMiJ,EAAQtD,EAAc1F,EAAMiJ,EAAQvD,GACrDxE,EAAMG,KAAKtB,EAAMiJ,EAAQrD,EAAc3F,EAAMiJ,EAAQtD,GAEhD8B,IAEGQ,EAAMsB,MAAQvM,QAAQA,SAACwM,MAEvBb,GAAcxC,EACVpG,EAAMiJ,GAAStD,EAAcC,GAAe,GAC5C3F,EAAMiJ,GAASvD,EAAcC,GAAe,GAC5C5F,EAAMiJ,EAAQtD,EACd1F,EAAMiJ,EAAQvD,EACd3F,EAAMiJ,EAAQrD,EACd3F,EAAMiJ,EAAQtD,EACdzE,GACA,GACA,EAEC+G,EAAMsB,MAAQvM,QAAQA,SAACyM,SAE5Bd,GAAcpD,EAAOxF,EAAIC,EAAIgJ,EAAOC,EAAOvD,EAAaC,GAAa,EAAOzE,KAIpF,IAAMC,GAAUH,EAAiBG,QAC3B0J,GAAO5N,EAAgBK,QAAUL,EAAgBK,QAGvD,IAASwC,EAAI8I,EAAY9I,EAAI6I,EAAaC,EAAa,IAAK9I,EAExDsD,EAAKlC,EAAW,EAAJpB,GACZuD,EAAKnC,EAAW,EAAJpB,EAAS,GAErBC,EAAKmB,EAAgB,GAATpB,EAAI,IAChBE,EAAKkB,EAAiB,GAATpB,EAAI,GAAU,GAE3BG,EAAKiB,EAAgB,GAATpB,EAAI,IAChBI,EAAKgB,EAAiB,GAATpB,EAAI,GAAU,GAGvBjC,KAAKqJ,IAAK9D,GAAMpD,EAAKE,GAAQH,GAAMG,EAAKmD,GAAQpD,GAAMoD,EAAKrD,IAAQ6K,IAKvE1J,GAAQE,KAAKvB,EAAGA,EAAI,EAAGA,EAAI,IA+D3BgL,CAAmBlK,EAAcI,GC3lBzC,MAAA+J,EAAA,WAAA,SAAAA,KA6GA,OA9FWA,EAAAC,QAAP,SAAejL,EAAYC,EAAYC,EAAYC,EAAYmC,EAAgB3C,GAE3E,IAAM4E,EAAQ5E,EAAOA,EAAOlC,OAAS,GAG/ByN,EAFQvL,EAAOA,EAAOlC,OAAS,GAElBwC,EACbkL,EAAK5G,EAAQvE,EACboL,EAAKjL,EAAKF,EACVoL,EAAKnL,EAAKF,EACVsL,EAAKxN,KAAKqJ,IAAK+D,EAAKG,EAAOF,EAAKC,GAEtC,GAAIE,EAAK,MAAqB,IAAXhJ,EAOf,OALI3C,EAAOA,EAAOlC,OAAS,KAAOuC,GAAML,EAAOA,EAAOlC,OAAS,KAAOwC,GAElEN,EAAO2B,KAAKtB,EAAIC,GAGb,KAGX,IAAMsL,EAAML,EAAKA,EAAOC,EAAKA,EACvBK,EAAMJ,EAAKA,EAAOC,EAAKA,EACvBI,EAAMP,EAAKE,EAAOD,EAAKE,EACvBK,EAAKpJ,EAASxE,KAAKkF,KAAKuI,GAAMD,EAC9BK,EAAKrJ,EAASxE,KAAKkF,KAAKwI,GAAMF,EAC9BrI,EAAKyI,EAAKD,EAAKF,EACfrI,EAAKyI,EAAKF,EAAKD,EACfnF,EAAMqF,EAAKL,EAAOM,EAAKR,EACvB7E,EAAMoF,EAAKN,EAAOO,EAAKT,EACvBd,EAAKe,GAAMQ,EAAK1I,GAChBoH,EAAKa,GAAMS,EAAK1I,GAChB2I,EAAKP,GAAMK,EAAKxI,GAChB2I,EAAKT,GAAMM,EAAKxI,GAItB,MAAO,CACHmD,GAAKA,EAAKrG,EACVsG,GAAKA,EAAKrG,EACVqC,OAAMA,EACN0E,WAPelJ,KAAKgJ,MAAMuD,EAAK/D,EAAI8D,EAAK/D,GAQxCyF,SAPahO,KAAKgJ,MAAM+E,EAAKvF,EAAIsF,EAAKvF,GAQtC0F,cAAgBZ,EAAKC,EAAKC,EAAKH,IAqBhCF,EAAAgB,IAAP,SAAWC,EAAiBC,EAAiB7F,EAAYC,EAAYhE,EACjE0E,EAAoB8E,EAAkBK,EAAyBxM,GAe/D,IAbA,IAAMyM,EAAQN,EAAW9E,EACnB5G,EAAIlD,EAAgBM,eACtBM,KAAKqJ,IAAIiF,GAAS9J,EACkB,GAApCxE,KAAKC,KAAKD,KAAKqJ,IAAIiF,GAASC,EAAIA,OAG9BC,EAAQ,GAAe,EAAJlM,GACnBmM,EAAiB,EAARD,EACTE,EAAS1O,KAAK2F,IAAI6I,GAClBG,EAAS3O,KAAK4F,IAAI4I,GAClBI,EAAWtM,EAAI,EACfuM,EAAaD,EAAW,EAAKA,EAE1B3M,EAAI,EAAGA,GAAK2M,IAAY3M,EACjC,CACI,IACMuH,EAAS,EAAUN,EAAcuF,GAD1BxM,EAAK4M,EAAY5M,GAExB8D,EAAI/F,KAAK2F,IAAI6D,GACbsF,GAAK9O,KAAK4F,IAAI4D,GAEpB3H,EAAO2B,MACAkL,EAAS3I,EAAM4I,EAASG,GAAMtK,EAAU+D,GACxCmG,GAAUI,EAAMH,EAAS5I,GAAMvB,EAAUgE,KAK3D0E,KCxHD6B,EAAA,WAAA,SAAAA,KAiHA,OAhGWA,EAAAC,YAAP,SACIvI,EAAeC,EACfC,EAAaC,EACbqI,EAAcC,EACdrI,EAAaC,GAiBb,IAfA,IACI/G,EAAS,EACToP,EAAI,EACJC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAM,EACNC,EAAM,EACN1L,EAAI,EACJC,EAAI,EACJC,EAAK,EACLC,EAAK,EACLwL,EAAQhJ,EACRiJ,EAAQhJ,EAEHzE,EAAI,EAAGA,GAfN,KAegBA,EAWtB+B,EAAKyL,GAFL3L,GAFA0L,GADAD,GADAD,EAAM,GAHNH,EAAIlN,EAjBE,KAqBKqN,GACCA,GAED7I,EAAU,EAAM8I,EAAMJ,EAAIxI,EAAQ,EAAM2I,GANnDF,EAAKD,EAAIA,GAMoDF,GAL7DI,EAAKD,EAAKD,GAKiEtI,GAG3E5C,EAAKyL,GAFL3L,EAAKyL,EAAM9I,EAAU,EAAM6I,EAAMJ,EAAIvI,EAAQ,EAAI0I,EAAKF,EAAKF,EAASG,EAAKvI,GAGzE2I,EAAQ3L,EACR4L,EAAQ3L,EAERhE,GAAUC,KAAKkF,KAAMlB,EAAKA,EAAOC,EAAKA,GAG1C,OAAOlE,GAgBJgP,EAAA5B,QAAP,SACIxG,EAAaC,EACbqI,EAAcC,EACdrI,EAAaC,EACbjF,GAEA,IAAM4E,EAAQ5E,EAAOA,EAAOlC,OAAS,GAC/B+G,EAAQ7E,EAAOA,EAAOlC,OAAS,GAErCkC,EAAOlC,QAAU,EAEjB,IAAM2C,EAAIlD,EAAgBM,eACtBqP,EAAYC,YAAYvI,EAAOC,EAAOC,EAAKC,EAAKqI,EAAMC,EAAMrI,EAAKC,IAGjE6I,EAAK,EACLC,EAAM,EACNC,EAAM,EACNT,EAAK,EACLC,EAAK,EAETxN,EAAO2B,KAAKiD,EAAOC,GAEnB,IAAK,IAAIzE,EAAI,EAAGmF,EAAI,EAAGnF,GAAKK,IAAKL,EAM7B4N,GADAD,GADAD,EAAM,GAFNvI,EAAInF,EAAIK,IAGGqN,GACCA,EAGZN,GADAD,EAAKhI,EAAIA,GACCA,EAEVvF,EAAO2B,KACFqM,EAAMpJ,EAAU,EAAImJ,EAAMxI,EAAIT,EAAQ,EAAIgJ,EAAKP,EAAKH,EAASI,EAAKxI,EAClEgJ,EAAMnJ,EAAU,EAAIkJ,EAAMxI,EAAIR,EAAQ,EAAI+I,EAAKP,EAAKF,EAASG,EAAKvI,IAIlFiI,KCjHDe,EAAA,WAAA,SAAAA,KA6EA,OA9DWA,EAAAd,YAAP,SACIvI,EAAeC,EACfC,EAAaC,EACbC,EAAaC,GAEb,IAAMiJ,EAAKtJ,EAAS,EAAME,EAAOE,EAC3BmJ,EAAKtJ,EAAS,EAAME,EAAOE,EAC3BmJ,EAAM,EAAMtJ,EAAQ,EAAMF,EAC1ByJ,EAAM,EAAMtJ,EAAQ,EAAMF,EAC1BjB,EAAI,GAAQsK,EAAKA,EAAOC,EAAKA,GAC7B/O,EAAI,GAAQ8O,EAAKE,EAAOD,EAAKE,GAC7BnK,EAAKkK,EAAKA,EAAOC,EAAKA,EAEtBpB,EAAI,EAAM9O,KAAKkF,KAAKO,EAAIxE,EAAI8E,GAC5BuH,EAAKtN,KAAKkF,KAAKO,GACf0K,EAAM,EAAM1K,EAAI6H,EAChBjB,EAAK,EAAMrM,KAAKkF,KAAKa,GACrBqK,EAAKnP,EAAIqM,EAEf,OACK6C,EAAMrB,EACAxB,EAAKrM,GAAK6N,EAAIzC,IAEX,EAAMtG,EAAIN,EAAMxE,EAAIA,GACrBjB,KAAKqQ,KAAM,EAAM/C,EAAM8C,EAAKtB,IAAMsB,EAAK/D,MAE/C,EAAM8D,IAaRL,EAAO3C,QAAd,SAAexG,EAAaC,EAAaC,EAAaC,EAAajF,GAY/D,IAVA,IAAM4E,EAAQ5E,EAAOA,EAAOlC,OAAS,GAC/B+G,EAAQ7E,EAAOA,EAAOlC,OAAS,GAE/B2C,EAAIlD,EAAgBM,eACtBoQ,EAAed,YAAYvI,EAAOC,EAAOC,EAAKC,EAAKC,EAAKC,IAGxDE,EAAK,EACLC,EAAK,EAEAhF,EAAI,EAAGA,GAAKK,IAAKL,EAC1B,CACI,IAAMmF,EAAInF,EAAIK,EAEd0E,EAAKP,GAAUE,EAAMF,GAASW,EAC9BH,EAAKP,GAAUE,EAAMF,GAASU,EAE9BvF,EAAO2B,KAAKwD,GAAQL,GAAQE,EAAMF,GAAOS,EAAMJ,GAAMI,EACjDH,GAAQL,GAAQE,EAAMF,GAAOQ,EAAMH,GAAMG,KAGxD0I,KC5EDQ,EAAA,WAQI,SAAAA,IAEIzQ,KAAKa,QAoCb,OA3BW4P,EAAA3P,UAAA4P,MAAP,SAAanG,EAA8BL,EAAoByG,GAE3D3Q,KAAKa,QACLb,KAAKuK,MAAQA,EACbvK,KAAK4Q,MAAQ1G,EACblK,KAAK2Q,YAAcA,GAQhBF,EAAA3P,UAAA+P,IAAP,SAAWC,EAAkBC,GAEzB/Q,KAAKgR,WAAaD,EAAY/Q,KAAK2Q,YACnC3Q,KAAKiR,KAAOH,EAAW9Q,KAAK4Q,OAGzBH,EAAA3P,UAAAD,MAAP,WAEIb,KAAKuK,MAAQ,KACbvK,KAAKiR,KAAO,EACZjR,KAAK4Q,MAAQ,EACb5Q,KAAK2Q,YAAc,EACnB3Q,KAAKgR,WAAa,GAEzBP,KClBYS,IAAapO,EAAA,IACrB0B,EAAAA,OAAOwF,MAAOhH,EACfF,EAAC0B,EAAAA,OAAOC,MAAOT,EACflB,EAAC0B,EAAAA,OAAOI,MAAOZ,EACflB,EAAC0B,EAAAA,OAAO2M,MAAO9K,EACfvD,EAAC0B,EAAAA,OAAOyB,MAAOuB,KAQN4J,EAA+B,GAO/BC,EAAuC,GC/CpDC,EAAA,WAiCI,SAAAA,EAAYnO,EAAeoO,EAA6B1H,EAA6BlJ,QAA1D,IAAA4Q,IAAAA,EAA2B,WAAE,IAAA1H,IAAAA,EAA2B,WAAE,IAAAlJ,IAAAA,EAAqB,MAZ1GX,KAAMgC,OAAa,GAInBhC,KAAKuD,MAAwB,GAUzBvD,KAAKmD,MAAQA,EACbnD,KAAK6J,UAAYA,EACjB7J,KAAKuR,UAAYA,EACjBvR,KAAKW,OAASA,EACdX,KAAKuE,KAAOpB,EAAMoB,KA4B1B,OArBW+M,EAAAxQ,UAAAC,MAAP,WAEI,OAAO,IAAIuQ,EACPtR,KAAKmD,MACLnD,KAAKuR,UACLvR,KAAK6J,UACL7J,KAAKW,SAKN2Q,EAAAxQ,UAAAG,QAAP,WAEIjB,KAAKmD,MAAQ,KACbnD,KAAKuD,MAAMzD,OAAS,EACpBE,KAAKuD,MAAQ,KACbvD,KAAKgC,OAAOlC,OAAS,EACrBE,KAAKgC,OAAS,KACdhC,KAAK6J,UAAY,KACjB7J,KAAKuR,UAAY,MAExBD,KCzCKE,EAAW,IAAI/G,EAAAA,MAUrBgH,EAAA,SAAAC,GA2EI,SAAAD,IAAA,IAAAE,EAEID,cACH1R,YApEM2R,EAAarH,cAAG,KAGhBqH,EAAaC,cAAG,EAEvBD,EAAUE,WAAiB,KAC3BF,EAAaG,cAA8B,KAC3CH,EAASI,WAAG,EAGZJ,EAAM3P,OAAa,GAGnB2P,EAAMK,OAAa,GAGnBL,EAAGM,IAAa,GAGhBN,EAAOlO,QAAa,GAGpBkO,EAAUO,WAAa,GAMvBP,EAAYzO,aAAwB,GAMpCyO,EAASQ,UAAyB,GAGlCR,EAAUS,YAAI,EAOdT,EAAOU,QAAqB,GAGlBV,EAAKW,MAAG,EAGRX,EAAUY,YAAI,EAGdZ,EAAUa,WAAG,EAGbb,EAAUc,WAAG,EAGbd,EAAAe,QAAkB,IAAIC,EAAAA,OAGtBhB,EAAWiB,aAAI,IA2yB7B,OAn3BsCjR,EAAa8P,EAAAC,GAoF/CrQ,OAAAwR,eAAWpB,EAAM3Q,UAAA,SAAA,CAAjBgS,IAAA,WAUI,OARA9S,KAAK+S,gBAED/S,KAAK4S,cAAgB5S,KAAKsS,QAE1BtS,KAAK4S,YAAc5S,KAAKsS,MACxBtS,KAAKgT,mBAGFhT,KAAK0S,yCAINjB,EAAA3Q,UAAAmS,WAAV,WAEIjT,KAAK4S,aAAe,EACpB5S,KAAKsS,QACLtS,KAAKoS,aACLpS,KAAKyS,WAAa,EAElBzS,KAAKgC,OAAOlC,OAAS,EACrBE,KAAKgS,OAAOlS,OAAS,EACrBE,KAAKiS,IAAInS,OAAS,EAClBE,KAAKyD,QAAQ3D,OAAS,EACtBE,KAAKkS,WAAWpS,OAAS,EAEzB,IAAK,IAAIsC,EAAI,EAAGA,EAAIpC,KAAKmS,UAAUrS,OAAQsC,IAEvCpC,KAAKmS,UAAU/P,GAAG8Q,SAASC,QAC3B9B,EAAe1N,KAAK3D,KAAKmS,UAAU/P,IAGvCpC,KAAKmS,UAAUrS,OAAS,EAExB,IAASsC,EAAI,EAAGA,EAAIpC,KAAKqS,QAAQvS,OAAQsC,IACzC,CACI,IAAMgR,EAAYpT,KAAKqS,QAAQjQ,GAE/BgR,EAAUvS,QACVuQ,EAAWzN,KAAKyP,GAGpBpT,KAAKqS,QAAQvS,OAAS,GAOnB2R,EAAA3Q,UAAAqS,MAAP,WASI,OAPInT,KAAKkD,aAAapD,OAAS,IAE3BE,KAAKiT,aACLjT,KAAKwS,aACLxS,KAAKkD,aAAapD,OAAS,GAGxBE,MAWJyR,EAAS3Q,UAAAuS,UAAhB,SACIlQ,EACAoO,EACA1H,EACAlJ,QAFA,IAAA4Q,IAAAA,EAA2B,WAC3B,IAAA1H,IAAAA,EAA2B,WAC3B,IAAAlJ,IAAAA,EAAqB,MAErB,IAAM2S,EAAO,IAAIhC,EAAanO,EAAOoO,EAAW1H,EAAWlJ,GAK3D,OAHAX,KAAKkD,aAAaS,KAAK2P,GACvBtT,KAAKsS,QAEEtS,MASJyR,EAAA3Q,UAAAyS,SAAP,SAAgBpQ,EAAexC,GAE3B,QAF2B,IAAAA,IAAAA,EAAqB,OAE3CX,KAAKkD,aAAapD,OAEnB,OAAO,KAGX,IAAMwT,EAAO,IAAIhC,EAAanO,EAAO,KAAM,KAAMxC,GAE3C6S,EAAYxT,KAAKkD,aAAalD,KAAKkD,aAAapD,OAAS,GAQ/D,OANAwT,EAAKzJ,UAAY2J,EAAU3J,UAE3B2J,EAAUjQ,MAAMI,KAAK2P,GAErBtT,KAAKsS,QAEEtS,MAIJyR,EAAA3Q,UAAAG,QAAP,WAEIyQ,EAAM5Q,UAAAG,mBAGN,IAAK,IAAImB,EAAI,EAAGA,EAAIpC,KAAKkD,aAAapD,SAAUsC,EAE5CpC,KAAKkD,aAAad,GAAGnB,UAGzBjB,KAAKgC,OAAOlC,OAAS,EACrBE,KAAKgC,OAAS,KACdhC,KAAKgS,OAAOlS,OAAS,EACrBE,KAAKgS,OAAS,KACdhS,KAAKiS,IAAInS,OAAS,EAClBE,KAAKiS,IAAM,KACXjS,KAAKyD,QAAQ3D,OAAS,EACtBE,KAAKyD,QAAU,KACfzD,KAAKyT,YAAYxS,UACjBjB,KAAKyT,YAAc,KACnBzT,KAAKkD,aAAapD,OAAS,EAC3BE,KAAKkD,aAAe,KACpBlD,KAAKmS,UAAUrS,OAAS,EACxBE,KAAKmS,UAAY,KACjBnS,KAAKqS,QAAQvS,OAAS,EACtBE,KAAKqS,QAAU,KACfrS,KAAK0S,QAAU,MAQZjB,EAAa3Q,UAAA4S,cAApB,SAAqBC,GAIjB,IAFA,IAAMzQ,EAAelD,KAAKkD,aAEjBd,EAAI,EAAGA,EAAIc,EAAapD,SAAUsC,EAC3C,CACI,IAAMkR,EAAOpQ,EAAad,GAE1B,GAAKkR,EAAK/B,UAAU3Q,UAMhB0S,EAAKnQ,QAEDmQ,EAAK3S,OAEL2S,EAAK3S,OAAOiT,aAAaD,EAAOnC,GAIhCA,EAASqC,SAASF,GAGlBL,EAAKnQ,MAAM2Q,SAAStC,EAASvN,EAAGuN,EAAStN,KAC7C,CACI,IAAI6P,GAAU,EAEd,GAAIT,EAAK/P,MAEL,IAAK,IAAIyQ,EAAI,EAAGA,EAAIV,EAAK/P,MAAMzD,OAAQkU,IACvC,CAGI,GAFaV,EAAK/P,MAAMyQ,GAEf7Q,MAAM2Q,SAAStC,EAASvN,EAAGuN,EAAStN,GAC7C,CACI6P,GAAU,EACV,OAKZ,IAAKA,EAED,OAAO,GAMvB,OAAO,GAOXtC,EAAA3Q,UAAAiS,cAAA,WAEI,GAAK/S,KAAKkD,aAAapD,QAOvB,GAAKE,KAAKiU,mBAAV,CAKAjU,KAAKuS,WAAavS,KAAKsS,MAEvB,IAAML,EAAMjS,KAAKiS,IACX/O,EAAelD,KAAKkD,aAEtBkQ,EAAuB,KAEvBc,EAAe,KAEflU,KAAKqS,QAAQvS,OAAS,IAGtBoU,GADAd,EAAYpT,KAAKqS,QAAQrS,KAAKqS,QAAQvS,OAAS,IACtByK,OAG7B,IAAK,IAAInI,EAAIpC,KAAKyS,WAAYrQ,EAAIc,EAAapD,OAAQsC,IACvD,CACIpC,KAAKyS,aAEL,IAAMa,EAAOpQ,EAAad,GACpBmP,EAAY+B,EAAK/B,UACjB1H,EAAYyJ,EAAKzJ,UACPqH,EAAcoC,EAAK/O,MAG3BtB,MAAMqQ,GAEVA,EAAK3S,QAELX,KAAKmU,gBAAgBb,EAAKtR,OAAQsR,EAAK3S,SAGvC4Q,EAAU3Q,SAAWiJ,EAAUjJ,UAE/BZ,KAAKoU,aAAad,EAAK/P,OAG3B,IAAK,IAAIgE,EAAI,EAAGA,EAAI,EAAGA,IACvB,CACI,IAAMgD,EAAe,IAANhD,EAAWgK,EAAY1H,EAEtC,GAAKU,EAAM3J,QAAX,CAEA,IAAMyT,EAAc9J,EAAM/J,QAAQ8T,YAC5BC,EAAQvU,KAAKyD,QAAQ3D,OACrB0U,EAAcxU,KAAKgC,OAAOlC,OAAS,EAEzCuU,EAAYI,SAAWC,EAAUA,WAACC,OAExB,IAANpN,EAEAvH,KAAK4U,YAAYtB,GAIjBtT,KAAK6U,YAAYvB,GAGrB,IAAMrC,EAAQjR,KAAKgC,OAAOlC,OAAS,EAAK0U,EAE3B,IAATvD,IAEAmC,IAAcpT,KAAK8U,eAAeZ,EAAc3J,KAEhD6I,EAAUvC,IAAI0D,EAAOC,GACrBpB,EAAY,MAGXA,KAEDA,EAAYhC,EAAWxG,OAAS,IAAI6F,GAC1BC,MAAMnG,EAAOgK,EAAOC,GAC9BxU,KAAKqS,QAAQ1O,KAAKyP,GAClBc,EAAe3J,GAGnBvK,KAAK+U,OAAO/U,KAAKgC,OAAQiQ,EAAK1H,EAAM/J,QAASgU,EAAavD,EAAM1G,EAAM5J,WAI9E,IAAMqU,EAAQhV,KAAKyD,QAAQ3D,OACrBmV,EAASjV,KAAKgC,OAAOlC,OAAS,EAOpC,GALIsT,GAEAA,EAAUvC,IAAImE,EAAOC,GAGG,IAAxBjV,KAAKqS,QAAQvS,OAAjB,CASA,IAAMoV,EAASD,EAAS,MAGpBjV,KAAK8R,eAAiB9R,KAAKyD,QAAQ3D,SAAWE,KAAK8R,cAAchS,QAC9DoV,IAAYlV,KAAK8R,cAAcqD,kBAAoB,EAEtDnV,KAAK8R,cAAcjH,IAAI7K,KAAKyD,SAI5BzD,KAAK8R,cAAgBoD,EAAS,IAAIE,YAAYpV,KAAKyD,SAAW,IAAI4R,YAAYrV,KAAKyD,SAIvFzD,KAAK+R,UAAY/R,KAAKsV,cAElBtV,KAAK+R,UAEL/R,KAAKuV,cAILvV,KAAKwV,sBA3BLxV,KAAK+R,WAAY,QAtGjB/R,KAAK+R,WAAY,GA0IfN,EAAA3Q,UAAAgU,eAAV,SAAyBW,EAA+BC,GAEpD,SAAKD,IAAWC,KAKZD,EAAOjV,QAAQ8T,cAAgBoB,EAAOlV,QAAQ8T,cAK9CmB,EAAOnV,MAAQmV,EAAOlV,QAAUmV,EAAOpV,MAAQoV,EAAOnV,SAKnDkV,EAAqB3L,UAAc4L,EAAqB5L,UASzD2H,EAAA3Q,UAAAmT,iBAAV,WAEI,GAAIjU,KAAKsS,QAAUtS,KAAKuS,aAAevS,KAAKkD,aAAapD,OAErD,OAAO,EAGX,IAAK,IAAIsC,EAAI,EAAGuT,EAAI3V,KAAKkD,aAAapD,OAAQsC,EAAIuT,EAAGvT,IACrD,CACI,IAAMkR,EAAOtT,KAAKkD,aAAad,GACzBwT,EAAOtC,EAAK/B,UACZsE,EAAOvC,EAAKzJ,UAElB,GAAI+L,IAASA,EAAKpV,QAAQ8T,YAAYwB,MAAO,OAAO,EACpD,GAAID,IAASA,EAAKrV,QAAQ8T,YAAYwB,MAAO,OAAO,EAGxD,OAAO,GAIDrE,EAAA3Q,UAAAyU,YAAV,WAEIvV,KAAKoS,aACLpS,KAAK6R,WAAa,IAAIkE,aAAa/V,KAAKiS,KAIxC,IAFA,IAAMI,EAAUrS,KAAKqS,QAEZjQ,EAAI,EAAGuT,EAAItD,EAAQvS,OAAQsC,EAAIuT,EAAGvT,IAIvC,IAFA,IAAM4T,EAAQ3D,EAAQjQ,GAEbmF,EAAI,EAAGA,EAAIyO,EAAM/E,KAAM1J,IAChC,CACI,IAAMyN,EAAQgB,EAAMpF,MAAQrJ,EAE5BvH,KAAK8R,cAAckD,GAAShV,KAAK8R,cAAckD,GAASgB,EAAMrF,cAShEc,EAAA3Q,UAAAwU,YAAV,WAGI,GAAItV,KAAKgC,OAAOlC,OAAS,OAErB,OAAO,EAKX,IAFA,IAAMuS,EAAUrS,KAAKqS,QAEZjQ,EAAI,EAAGA,EAAIiQ,EAAQvS,OAAQsC,IAEhC,GAAKiQ,EAAQjQ,GAAGmI,MAAoBT,OAEhC,OAAO,EAIf,OAAQ9J,KAAKgC,OAAOlC,OAA2C,EAAlC2R,EAAiBwE,gBAIxCxE,EAAA3Q,UAAA0U,eAAV,WAII,IAFA,IAAIU,IAASC,EAAWA,YAACC,aAEhBhU,EAAI,EAAGA,EAAIpC,KAAKmS,UAAUrS,OAAQsC,IAEvCpC,KAAKmS,UAAU/P,GAAG8Q,SAASC,QAC3B9B,EAAe1N,KAAK3D,KAAKmS,UAAU/P,IAGvCpC,KAAKmS,UAAUrS,OAAS,EAExB,IAAMkS,EAAShS,KAAKgS,OACdE,EAAalS,KAAKkS,WAEpBmE,EAA+BhF,EAAezG,MAE7CyL,KAEDA,EAAe,IAAIC,EAAAA,eACNpD,SAAW,IAAIqD,EAAAA,mBAEhCF,EAAanD,SAASsD,MAAQ,EAC9BH,EAAazF,MAAQ,EACrByF,EAAapF,KAAO,EACpBoF,EAAa9R,KAAOkS,EAAUA,WAACC,UAE/B,IAAIC,EAAe,EACfC,EAAiB,KACjBC,EAAY,EACZ/M,GAAS,EACTgN,EAAWL,EAAUA,WAACC,UAEtB1B,EAAQ,EAEZhV,KAAKmS,UAAUxO,KAAK0S,GAGpB,IAASjU,EAAI,EAAGA,EAAIpC,KAAKqS,QAAQvS,OAAQsC,IACzC,CACI,IAAMkR,EAAOtT,KAAKqS,QAAQjQ,GAMpBmI,EAAQ+I,EAAK/I,MAEb8J,EAAc9J,EAAM/J,QAAQ8T,YAE9BxK,MAAaS,EAAMT,SAGnBgN,GADAhN,IAAWS,EAAMT,QACG2M,EAAAA,WAAWM,MAAQN,EAAAA,WAAWC,UAGlDE,EAAiB,KACjBD,EAdiB,EAejBT,KAGAU,IAAmBvC,IAEnBuC,EAAiBvC,EAEbA,EAAY2C,gBAAkBd,IAtBjB,IAwBTS,IAEAT,IAEAS,EAAe,EAEXN,EAAapF,KAAO,KAEpBoF,EAAehF,EAAezG,UAG1ByL,EAAe,IAAIC,EAAAA,eACNpD,SAAW,IAAIqD,EAAAA,mBAEhCvW,KAAKmS,UAAUxO,KAAK0S,IAGxBA,EAAazF,MAAQoE,EACrBqB,EAAapF,KAAO,EACpBoF,EAAanD,SAASsD,MAAQ,EAC9BH,EAAa9R,KAAOuS,GAKxBzC,EAAY4C,QAAU,EAEtB5C,EAAY2C,cAAgBd,EAC5B7B,EAAY6C,eAAiBP,EAC7BtC,EAAYI,SAAWC,EAAUA,WAACC,OAElC0B,EAAanD,SAASiE,SAASd,EAAanD,SAASsD,SAAWnC,EAChEsC,MAIRN,EAAapF,MAAQqC,EAAKrC,KAC1B+D,GAAS1B,EAAKrC,KAEd4F,EAAYxC,EAAY6C,eAExBlX,KAAKoX,UAAUpF,EAAQzH,EAAMjK,MAAOiK,EAAMhK,MAAO+S,EAAKtC,WAAYsC,EAAK3C,aACvE3Q,KAAKqX,cAAcnF,EAAY2E,EAAWvD,EAAKtC,WAAYsC,EAAK3C,aAGpEwF,EAAWA,YAACC,aAAeF,EAI3BlW,KAAKsX,kBAIC7F,EAAA3Q,UAAAwW,eAAV,WAcI,IAZA,IAAM9T,EAAQxD,KAAKgC,OACbiQ,EAAMjS,KAAKiS,IACXD,EAAShS,KAAKgS,OACdE,EAAalS,KAAKkS,WAGlBqF,EAAW,IAAIC,YAA2B,EAAfhU,EAAM1D,OAAa,GAC9C2X,EAAM,IAAI1B,aAAawB,GACvBG,EAAM,IAAItC,YAAYmC,GAExB9V,EAAI,EAECW,EAAI,EAAGA,EAAIoB,EAAM1D,OAAS,EAAGsC,IAElCqV,EAAIhW,KAAO+B,EAAU,EAAJpB,GACjBqV,EAAIhW,KAAO+B,EAAW,EAAJpB,EAAS,GAE3BqV,EAAIhW,KAAOwQ,EAAQ,EAAJ7P,GACfqV,EAAIhW,KAAOwQ,EAAS,EAAJ7P,EAAS,GAEzBsV,EAAIjW,KAAOuQ,EAAO5P,GAElBqV,EAAIhW,KAAOyQ,EAAW9P,GAG1BpC,KAAK2X,QAAQC,OAAOL,GACpBvX,KAAK6X,aAAaD,OAAO5X,KAAK8R,gBAOxBL,EAAW3Q,UAAA8T,YAArB,SAAsBtB,GAEdA,EAAK/P,MAAMzD,OAEXkD,EAAUK,YAAYiQ,EAAMtT,MAIZkR,EAAcoC,EAAK/O,MAE3BlB,YAAYiQ,EAAMtT,OAQxByR,EAAW3Q,UAAA+T,YAArB,SAAsBvB,GAElB1J,EAAU0J,EAAMtT,MAEhB,IAAK,IAAIoC,EAAI,EAAGA,EAAIkR,EAAK/P,MAAMzD,OAAQsC,IAEnCwH,EAAU0J,EAAK/P,MAAMnB,GAAIpC,OAQvByR,EAAY3Q,UAAAsT,aAAtB,SAAuB7Q,GAEnB,IAAK,IAAInB,EAAI,EAAGA,EAAImB,EAAMzD,OAAQsC,IAClC,CACI,IAAMH,EAAOsB,EAAMnB,GACH8O,EAAcjP,EAAKsC,MAE3BtB,MAAMhB,GAEVA,EAAKtB,QAELX,KAAKmU,gBAAgBlS,EAAKD,OAAQC,EAAKtB,UAMzC8Q,EAAA3Q,UAAAkS,gBAAV,WAEI,IAAM8E,EAAS9X,KAAK0S,QAEpBoF,EAAO3E,QACP2E,EAAOC,cAAe/X,KAAKgC,OAAgB,EAAGhC,KAAKgC,OAAOlC,QAC1DgY,EAAOE,IAAIhY,KAAK4R,cAAe5R,KAAK4R,gBAQ9BH,EAAA3Q,UAAAqT,gBAAV,SAA0BnS,EAAuBrB,GAE7C,IAAK,IAAIyB,EAAI,EAAGA,EAAIJ,EAAOlC,OAAS,EAAGsC,IACvC,CACI,IAAM6B,EAAIjC,EAAY,EAAJI,GACZ8B,EAAIlC,EAAY,EAAJI,EAAS,GAE3BJ,EAAY,EAAJI,GAAWzB,EAAOiF,EAAI3B,EAAMtD,EAAOuF,EAAIhC,EAAKvD,EAAOwF,GAC3DnE,EAAY,EAAJI,EAAS,GAAMzB,EAAOS,EAAI6C,EAAMtD,EAAOQ,EAAI+C,EAAKvD,EAAOyF,KAY7DqL,EAAS3Q,UAAAsW,UAAnB,SACIpF,EACA1R,EACAC,EACA0Q,EACAgH,QAAA,IAAAA,IAAAA,EAAU,GAGV,IAAMC,GAAO5X,GAAS,KAAe,MAARA,KAA4B,IAARA,IAAiB,IAE5D6X,EAAQC,EAAAA,gBAAgBF,EAAK3X,GAEnCyR,EAAOlS,OAASK,KAAKgF,IAAI6M,EAAOlS,OAAQmY,EAAShH,GAEjD,IAAK,IAAI7O,EAAI,EAAGA,EAAI6O,EAAM7O,IAEtB4P,EAAOiG,EAAS7V,GAAK+V,GAWnB1G,EAAa3Q,UAAAuW,cAAvB,SACInF,EACAmG,EACApH,EACAgH,QAAA,IAAAA,IAAAA,EAAU,GAEV/F,EAAWpS,OAASK,KAAKgF,IAAI+M,EAAWpS,OAAQmY,EAAShH,GAEzD,IAAK,IAAI7O,EAAI,EAAGA,EAAI6O,EAAM7O,IAEtB8P,EAAW+F,EAAS7V,GAAKiW,GAavB5G,EAAA3Q,UAAAiU,OAAV,SACIvR,EACAyO,EACAzR,EACAoQ,EACAK,EACAtQ,QAAA,IAAAA,IAAAA,EAAqB,MAMrB,IAJA,IAAIqU,EAAQ,EACNsD,EAAWrG,EAAInS,OACfyY,EAAQ/X,EAAQ+X,MAEfvD,EAAQ/D,GACf,CACI,IAAIhN,EAAIT,EAAwB,GAAjBoN,EAAQoE,IACnB9Q,EAAIV,EAAyB,GAAjBoN,EAAQoE,GAAc,GAEtC,GAAIrU,EACJ,CACI,IAAMmH,EAAMnH,EAAOiF,EAAI3B,EAAMtD,EAAOuF,EAAIhC,EAAKvD,EAAOwF,GAEpDjC,EAAKvD,EAAOS,EAAI6C,EAAMtD,EAAOQ,EAAI+C,EAAKvD,EAAOyF,GAC7CnC,EAAI6D,EAGRkN,IAEA/C,EAAItO,KAAKM,EAAIsU,EAAMzT,MAAOZ,EAAIqU,EAAMxT,QAGxC,IAAMuP,EAAc9T,EAAQ8T,aAExBiE,EAAMzT,MAAQwP,EAAYxP,OACvByT,EAAMxT,OAASuP,EAAYvP,SAE9B/E,KAAKwY,UAAUvG,EAAKzR,EAAS8X,EAAUrH,IAYrCQ,EAAS3Q,UAAA0X,UAAnB,SAAoBvG,EAAoBzR,EAAkBoQ,EAAeK,GAarE,IAXA,IAAMqD,EAAc9T,EAAQ8T,YACtBjK,EAAM,KACNoO,EAAS7H,EAAgB,EAAPK,EAClBsH,EAAQ/X,EAAQ+X,MAChBG,EAASH,EAAMzT,MAAQwP,EAAYxP,MACnC6T,EAASJ,EAAMxT,OAASuP,EAAYvP,OACtC6T,EAAUL,EAAMtU,EAAIsU,EAAMzT,MAC1B+T,EAAUN,EAAMrU,EAAIqU,EAAMxT,OAC1B+T,EAAO3Y,KAAK4Y,MAAM9G,EAAIrB,GAASvG,GAC/B2O,EAAO7Y,KAAK4Y,MAAM9G,EAAIrB,EAAQ,GAAKvG,GAE9BjI,EAAIwO,EAAQ,EAAGxO,EAAIqW,EAAQrW,GAAK,EAErC0W,EAAO3Y,KAAKiF,IAAI0T,EAAM3Y,KAAK4Y,MAAM9G,EAAI7P,GAAKiI,IAC1C2O,EAAO7Y,KAAKiF,IAAI4T,EAAM7Y,KAAK4Y,MAAM9G,EAAI7P,EAAI,GAAKiI,IAElDuO,GAAWE,EACXD,GAAWG,EACX,IAAS5W,EAAIwO,EAAOxO,EAAIqW,EAAQrW,GAAK,EAEjC6P,EAAI7P,IAAM6P,EAAI7P,GAAKwW,GAAWF,EAC9BzG,EAAI7P,EAAI,IAAM6P,EAAI7P,EAAI,GAAKyW,GAAWF,GAz2BhClH,EAAcwE,eAAG,IA42BlCxE,EAn3BD,CAAsCwH,iBCrCtCC,EAAA,SAAAxH,GAAA,SAAAwH,IAAA,IA4DCvH,EAAA,OAAAD,GAAAA,EAAAyH,MAAAnZ,KAAAoZ,YAAApZ,YAzDU2R,EAAK7M,MAAG,EAGR6M,EAAS/F,UAAG,GAGZ+F,EAAM7H,QAAG,EAOT6H,EAAA9F,IAAMvM,QAAQA,SAAC+Z,KAOf1H,EAAArF,KAAOjN,QAASA,UAAC6N,MAGjByE,EAAUtG,WAAG,KAkCxB,OA5D+B1J,EAASuX,EAAAxH,GA6B7BwH,EAAApY,UAAAC,MAAP,WAEI,IAAMC,EAAM,IAAIkY,EAchB,OAZAlY,EAAIV,MAAQN,KAAKM,MACjBU,EAAIT,MAAQP,KAAKO,MACjBS,EAAIR,QAAUR,KAAKQ,QACnBQ,EAAIL,OAASX,KAAKW,OAClBK,EAAIJ,QAAUZ,KAAKY,QACnBI,EAAI8D,MAAQ9E,KAAK8E,MACjB9D,EAAI4K,UAAY5L,KAAK4L,UACrB5K,EAAI8I,OAAS9J,KAAK8J,OAClB9I,EAAI6K,IAAM7L,KAAK6L,IACf7K,EAAIsL,KAAOtM,KAAKsM,KAChBtL,EAAIqK,WAAarL,KAAKqL,WAEfrK,GAIJkY,EAAApY,UAAAD,MAAP,WAEI6Q,EAAM5Q,UAAAD,iBAGNb,KAAKM,MAAQ,EAEbN,KAAK4L,UAAY,GACjB5L,KAAK8E,MAAQ,EACb9E,KAAK8J,QAAS,GAErBoP,EA5DD,CAA+B7Y,GCmDzBiZ,EAAO,IAAIvD,aAAa,GAGxBwD,EAA2C,GAsBjD9R,EAAA,SAAAiK,GA4EI,SAAAjK,EAAY+R,QAAA,IAAAA,IAAAA,EAAiC,MAA7C,IAAA7H,EAEID,cAsBH1R,YAlFM2R,EAAM8H,OAAW,KAGjB9H,EAAU+H,WAAG,QAMb/H,EAAWgI,YAAY,KAGpBhI,EAAOU,QAAiC,GAGxCV,EAASiI,WAAI,EAGbjI,EAAUS,YAAI,EAGdT,EAAUkI,WAAiB,KAG3BlI,EAAAmI,WAAwB,IAAIzZ,EAG5BsR,EAAAoI,WAAwB,IAAIb,EAG5BvH,EAAOqI,QAAW,KAGlBrI,EAASsI,WAAG,EAQdtI,EAAAuI,MAAeC,QAAMC,QAqBzBzI,EAAK0I,UAAYb,GAAY,IAAI/H,EACjCE,EAAK0I,UAAUC,WAcf3I,EAAK4I,cAAgB,EAGrB5I,EAAK6I,KAAO,SACZ7I,EAAK8I,UAAYC,EAAWA,YAACC,SAqgCrC,OAxmC8BhZ,EAAS8F,EAAAiK,GAoEnCrQ,OAAAwR,eAAWpL,EAAQ3G,UAAA,WAAA,CAAnBgS,IAAA,WAEI,OAAO9S,KAAKqa,2CAqCT5S,EAAA3G,UAAAC,MAAP,WAII,OAFAf,KAAK4a,aAEE,IAAInT,EAASzH,KAAKqa,YAW7BhZ,OAAAwR,eAAWpL,EAAS3G,UAAA,YAAA,CAKpBgS,IAAA,WAEI,OAAO9S,KAAKka,MAAMO,WAPtB5P,IAAA,SAAqBgQ,GAEjB7a,KAAKka,MAAMO,UAAYI,mCAa3BxZ,OAAAwR,eAAWpL,EAAI3G,UAAA,OAAA,CAAfgS,IAAA,WAEI,OAAO9S,KAAK8a,OAGhBjQ,IAAA,SAAgBgQ,GAEZ7a,KAAK8a,MAAQD,mCAOjBxZ,OAAAwR,eAAWpL,EAAI3G,UAAA,OAAA,CAAfgS,IAAA,WAEI,OAAO9S,KAAK8Z,4CAOhBzY,OAAAwR,eAAWpL,EAAI3G,UAAA,OAAA,CAAfgS,IAAA,WAEI,OAAO9S,KAAK+Z,4CAiCTtS,EAAS3G,UAAA+I,UAAhB,SAAiBkR,EACbza,EAAaC,EAAWqL,EAAiB9B,GAQzC,YATa,IAAAiR,IAAAA,EAA0C,WACvD,IAAAza,IAAAA,EAAW,QAAE,IAAAC,IAAAA,EAAS,QAAE,IAAAqL,IAAAA,EAAe,SAAE,IAAA9B,IAAAA,GAAc,GAGhC,iBAAZiR,IAEPA,EAAU,CAAEjW,MAAOiW,EAASza,MAAKA,EAAEC,MAAKA,EAAEqL,UAASA,EAAE9B,OAAMA,IAGxD9J,KAAKgb,iBAAiBD,IAoB1BtT,EAAgB3G,UAAAka,iBAAvB,SAAwBD,GAGpBA,EAAU1Z,OAAO4Z,OAAO,CACpBnW,MAAO,EACPtE,QAASC,EAAOA,QAACC,MACjBJ,MAAQya,GAAWA,EAAQva,QAAW,SAAW,EACjDD,MAAO,EACPI,OAAQ,KACRiL,UAAW,GACX9B,QAAQ,EACR+B,IAAKvM,QAAQA,SAAC+Z,KACd/M,KAAMjN,QAASA,UAAC6N,MAChB7B,WAAY,IACb0P,GAEC/a,KAAK2Z,aAEL3Z,KAAKkb,YAGT,IAAMta,EAAUma,EAAQjW,MAAQ,GAAKiW,EAAQxa,MAAQ,EAiBrD,OAfKK,GAMGma,EAAQpa,SAERoa,EAAQpa,OAASoa,EAAQpa,OAAOI,QAChCga,EAAQpa,OAAOwa,UAGnB9Z,OAAO4Z,OAAOjb,KAAK+Z,WAAY,CAAEnZ,QAAOA,GAAIma,IAV5C/a,KAAK+Z,WAAWlZ,QAabb,MAODyH,EAAA3G,UAAAoa,UAAV,WAEI,GAAIlb,KAAK2Z,YACT,CACI,IAAM3X,EAAShC,KAAK2Z,YAAY3X,OAC1BoZ,EAAMpb,KAAK2Z,YAAY3X,OAAOlC,OAEhCsb,EAAM,IAENpb,KAAKqT,UAAUrT,KAAK2Z,aACpB3Z,KAAK2Z,YAAc,IAAI0B,EAAAA,QACvBrb,KAAK2Z,YAAY1P,aAAc,EAC/BjK,KAAK2Z,YAAY3X,OAAO2B,KAAK3B,EAAOoZ,EAAM,GAAIpZ,EAAOoZ,EAAM,UAK/Dpb,KAAK2Z,YAAc,IAAI0B,EAAAA,QACvBrb,KAAK2Z,YAAY1P,aAAc,GAQvCxC,EAAA3G,UAAA8Z,WAAA,WAEQ5a,KAAK2Z,cAED3Z,KAAK2Z,YAAY3X,OAAOlC,OAAS,GAEjCE,KAAKqT,UAAUrT,KAAK2Z,aACpB3Z,KAAK2Z,YAAc,MAInB3Z,KAAK2Z,YAAY3X,OAAOlC,OAAS,IAWtC2H,EAAA3G,UAAAwa,OAAP,SAAcrX,EAAWC,GAMrB,OAJAlE,KAAKkb,YACLlb,KAAK2Z,YAAY3X,OAAO,GAAKiC,EAC7BjE,KAAK2Z,YAAY3X,OAAO,GAAKkC,EAEtBlE,MAUJyH,EAAA3G,UAAAya,OAAP,SAActX,EAAWC,GAEhBlE,KAAK2Z,aAEN3Z,KAAKsb,OAAO,EAAG,GAInB,IAAMtZ,EAAShC,KAAK2Z,YAAY3X,OAC1B4E,EAAQ5E,EAAOA,EAAOlC,OAAS,GAC/B+G,EAAQ7E,EAAOA,EAAOlC,OAAS,GAOrC,OALI8G,IAAU3C,GAAK4C,IAAU3C,GAEzBlC,EAAO2B,KAAKM,EAAGC,GAGZlE,MAQDyH,EAAA3G,UAAA0a,WAAV,SAAqBvX,EAAOC,QAAP,IAAAD,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAK,GAEzBlE,KAAK2Z,YAEkC,IAAnC3Z,KAAK2Z,YAAY3X,OAAOlC,SAExBE,KAAK2Z,YAAY3X,OAAS,CAACiC,EAAGC,IAKlClE,KAAKsb,OAAOrX,EAAGC,IAahBuD,EAAgB3G,UAAA2a,iBAAvB,SAAwB3U,EAAaC,EAAaC,EAAaC,GAE3DjH,KAAKwb,aAEL,IAAMxZ,EAAShC,KAAK2Z,YAAY3X,OAShC,OAPsB,IAAlBA,EAAOlC,QAEPE,KAAKsb,OAAO,EAAG,GAGnBrL,EAAe3C,QAAQxG,EAAKC,EAAKC,EAAKC,EAAKjF,GAEpChC,MAaJyH,EAAA3G,UAAA4a,cAAP,SAAqB5U,EAAaC,EAAaqI,EAAcC,EAAcrI,EAAaC,GAMpF,OAJAjH,KAAKwb,aAELtM,EAAY5B,QAAQxG,EAAKC,EAAKqI,EAAMC,EAAMrI,EAAKC,EAAKjH,KAAK2Z,YAAY3X,QAE9DhC,MAcJyH,EAAK3G,UAAA6a,MAAZ,SAAatZ,EAAYC,EAAYC,EAAYC,EAAYmC,GAEzD3E,KAAKwb,WAAWnZ,EAAIC,GAEpB,IAAMN,EAAShC,KAAK2Z,YAAY3X,OAE1B9B,EAASmN,EAASC,QAAQjL,EAAIC,EAAIC,EAAIC,EAAImC,EAAQ3C,GAExD,GAAI9B,EACJ,CACY,IAAAwI,EAAwDxI,EAAMwI,GAA1DC,EAAoDzI,EAAlDyI,GAAEiT,EAAgD1b,EAAMyE,OAA9C0E,EAAwCnJ,EAA9BmJ,WAAE8E,EAA4BjO,EAAMiO,SAAxBC,EAAkBlO,gBAEhEF,KAAKqO,IAAI3F,EAAIC,EAAIiT,EAAQvS,EAAY8E,EAAUC,GAGnD,OAAOpO,MAgBJyH,EAAA3G,UAAAuN,IAAP,SAAW3F,EAAYC,EAAYhE,EAAgB0E,EAAoB8E,EAAkBC,GAErF,QAFqF,IAAAA,IAAAA,GAAqB,GAEtG/E,IAAe8E,EAEf,OAAOnO,KAcX,IAXKoO,GAAiBD,GAAY9E,EAE9B8E,GAAYO,EAAAA,KAEPN,GAAiB/E,GAAc8E,IAEpC9E,GAAcqF,EAAAA,MAKJ,IAFAP,EAAW9E,EAIrB,OAAOrJ,KAGX,IAAM6b,EAASnT,EAAMvI,KAAK2F,IAAIuD,GAAc1E,EACtCmX,EAASnT,EAAMxI,KAAK4F,IAAIsD,GAAc1E,EACtC0F,EAAMrK,KAAKqa,UAAU/P,cAGvBtI,EAAShC,KAAK2Z,YAAc3Z,KAAK2Z,YAAY3X,OAAS,KAE1D,GAAIA,EACJ,CAII,IAAM+Z,EAAQ5b,KAAKqJ,IAAIxH,EAAOA,EAAOlC,OAAS,GAAK+b,GAC7CG,EAAQ7b,KAAKqJ,IAAIxH,EAAOA,EAAOlC,OAAS,GAAKgc,GAE/CC,EAAQ1R,GAAO2R,EAAQ3R,GAOvBrI,EAAO2B,KAAKkY,EAAQC,QAKxB9b,KAAKsb,OAAOO,EAAQC,GACpB9Z,EAAShC,KAAK2Z,YAAY3X,OAK9B,OAFAqL,EAASgB,IAAIwN,EAAQC,EAAQpT,EAAIC,EAAIhE,EAAQ0E,EAAY8E,EAAUC,EAAepM,GAE3EhC,MAUJyH,EAAA3G,UAAAmb,UAAP,SAAiB3b,EAAWC,GAExB,YAFa,IAAAD,IAAAA,EAAS,QAAE,IAAAC,IAAAA,EAAS,GAE1BP,KAAKkc,iBAAiB,CAAE1b,QAASC,EAAOA,QAACC,MAAOJ,MAAKA,EAAEC,MAAKA,KAYvEkH,EAAgB3G,UAAAob,iBAAhB,SAAiBnB,GAGbA,EAAU1Z,OAAO4Z,OAAO,CACpBza,QAASC,EAAOA,QAACC,MACjBJ,MAAO,SACPC,MAAO,EACPI,OAAQ,MACToa,GAEC/a,KAAK2Z,aAEL3Z,KAAKkb,YAGT,IAAMta,EAAUma,EAAQxa,MAAQ,EAiBhC,OAfKK,GAMGma,EAAQpa,SAERoa,EAAQpa,OAASoa,EAAQpa,OAAOI,QAChCga,EAAQpa,OAAOwa,UAGnB9Z,OAAO4Z,OAAOjb,KAAK8Z,WAAY,CAAElZ,QAAOA,GAAIma,IAV5C/a,KAAK8Z,WAAWjZ,QAabb,MAOJyH,EAAA3G,UAAAqb,QAAP,WAMI,OAJAnc,KAAK4a,aAEL5a,KAAK8Z,WAAWjZ,QAETb,MAWJyH,EAAQ3G,UAAAsb,SAAf,SAAgBnY,EAAWC,EAAWY,EAAeC,GAEjD,OAAO/E,KAAKqT,UAAU,IAAIgJ,EAASA,UAACpY,EAAGC,EAAGY,EAAOC,KAY9C0C,EAAe3G,UAAAwb,gBAAtB,SAAuBrY,EAAWC,EAAWY,EAAeC,EAAgBJ,GAExE,OAAO3E,KAAKqT,UAAU,IAAIkJ,mBAAiBtY,EAAGC,EAAGY,EAAOC,EAAQJ,KAU7D8C,EAAA3G,UAAA0b,WAAP,SAAkBvY,EAAWC,EAAWS,GAEpC,OAAO3E,KAAKqT,UAAU,IAAIoJ,EAAAA,OAAOxY,EAAGC,EAAGS,KAWpC8C,EAAW3G,UAAA4b,YAAlB,SAAmBzY,EAAWC,EAAWY,EAAeC,GAEpD,OAAO/E,KAAKqT,UAAU,IAAIsJ,EAAOA,QAAC1Y,EAAGC,EAAGY,EAAOC,KAW5C0C,EAAA3G,UAAA8b,YAAP,mBAEQ5a,cAFyB6a,EAAA,GAAAC,EAAA,EAAdA,EAAc1D,UAAAtZ,OAAdgd,IAAAD,EAAcC,GAAA1D,EAAA0D,GAG7B,IAAI7S,GAAc,EAEZ8S,EAAOF,EAAK,GAGdE,EAAK/a,QAELiI,EAAc8S,EAAK9S,YACnBjI,EAAS+a,EAAK/a,QAKdA,EAFAR,MAAMwb,QAAQH,EAAK,IAEVA,EAAK,GAILA,EAGb,IAAM1Z,EAAQ,IAAIkY,UAAQrZ,GAM1B,OAJAmB,EAAM8G,YAAcA,EAEpBjK,KAAKqT,UAAUlQ,GAERnD,MAQJyH,EAAS3G,UAAAuS,UAAhB,SAAiBlQ,GAgBb,OAdKnD,KAAKia,UAWNja,KAAKqa,UAAU9G,SAASpQ,EAAOnD,KAAKga,SATpCha,KAAKqa,UAAUhH,UACXlQ,EACAnD,KAAK8Z,WAAW/Y,QAChBf,KAAK+Z,WAAWhZ,QAChBf,KAAKga,SAQNha,MAOJyH,EAAA3G,UAAAqS,MAAP,WAWI,OATAnT,KAAKqa,UAAUlH,QACfnT,KAAK+Z,WAAWlZ,QAChBb,KAAK8Z,WAAWjZ,QAEhBb,KAAKid,YACLjd,KAAKga,QAAU,KACfha,KAAKia,WAAY,EACjBja,KAAK2Z,YAAc,KAEZ3Z,MAQJyH,EAAA3G,UAAAoc,WAAP,WAEI,IAAM5J,EAAOtT,KAAKqa,UAAUnX,aAE5B,QAAuB,IAAhBoQ,EAAKxT,QACLwT,EAAK,GAAGnQ,MAAMoB,OAASC,EAAMA,OAAC2M,MAC7BmC,EAAK,GAAG3S,QACR2S,EAAK,GAAG/P,MAAMzD,QACbwT,EAAK,GAAGzJ,UAAUjJ,SAAW0S,EAAK,GAAGzJ,UAAU/E,QAOlD2C,EAAO3G,UAAAqc,QAAjB,SAAkBC,GAEdpd,KAAK4a,aAEL,IAAMpB,EAAWxZ,KAAKqa,UAItBb,EAASzG,gBAELyG,EAASzH,WAEL/R,KAAKoS,aAAeoH,EAASpH,YAE7BpS,KAAKqd,mBAGTrd,KAAKsd,eAAeF,KAKpBA,EAASpH,MAAMuH,QAEfvd,KAAKwd,cAAcJ,KAKjB3V,EAAA3G,UAAAuc,iBAAV,WAEI,IAAM7D,EAAWxZ,KAAKqa,UAChBI,EAAYza,KAAKya,UACjBW,EAAM5B,EAASnH,QAAQvS,OAE7BE,KAAK4Z,WAAa,EAClB5Z,KAAKua,cAAgB,EACrBva,KAAKoS,WAAaoH,EAASpH,WAC3BpS,KAAKqS,QAAQvS,OAASsb,EAEtBpb,KAAK6Z,WAAa,IAAI9D,aAAayD,EAASxX,QAE5C,IAAK,IAAII,EAAI,EAAGA,EAAIgZ,EAAKhZ,IACzB,CACI,IAAMqb,EAAKjE,EAASnH,QAAQjQ,GACtB9B,EAAQmd,EAAGlT,MAAMjK,MACjBuZ,EAAa,IAAI9D,aAAa/V,KAAK6Z,WAAW6D,OAC/B,EAAjBD,EAAG9M,YAAkB,EACL,EAAhB8M,EAAGzM,YAEDiB,EAAM,IAAI8D,aAAayD,EAAS3H,WAAW6L,OAC5B,EAAjBD,EAAG9M,YAAkB,EACL,EAAhB8M,EAAGzM,YAMDgF,EAAQ,CACV6D,WAAUA,EACVY,UAASA,EACThX,QAPY,IAAI4R,YAAYmE,EAAS1H,cAAc4L,OACxC,EAAXD,EAAG7M,MACH6M,EAAGxM,MAMHgB,IAAGA,EACH0L,UAAWC,EAAOA,QAACtd,GACnBud,SAAUvd,EACVwd,SAAUL,EAAGlT,MAAM/J,QACnBD,MAAOkd,EAAGlT,MAAMhK,MAChBwd,WAAY,GAEhB/d,KAAKqS,QAAQjQ,GAAK4T,IAQhBvO,EAAc3G,UAAAwc,eAAxB,SAAyBF,GAErB,GAAKpd,KAAKqS,QAAQvS,OAAlB,CAKAsd,EAASpH,MAAMgI,kBAAkBZ,EAASa,QAAQje,KAAK0Z,aAEvD1Z,KAAKke,oBACLle,KAAKme,iBAEL,IAAK,IAAI/b,EAAI,EAAGuT,EAAI3V,KAAKqS,QAAQvS,OAAQsC,EAAIuT,EAAGvT,IAChD,CACI,IAAM4T,EAAQhW,KAAKqS,QAAQjQ,GAE3B4T,EAAM+H,WAAa/d,KAAK+d,WAAa/H,EAAMzV,MAE3C6c,EAASa,QAAQje,KAAK0Z,YAAY0E,OAAOpI,MAQvCvO,EAAa3G,UAAA0c,cAAvB,SAAwBJ,GAEpB,IAAM3D,EAASzZ,KAAKqe,qBAAqBjB,GAEnC5D,EAAWxZ,KAAKqa,UAChBG,EAAOxa,KAAKwa,KACZuD,EAAa/d,KAAK+d,WAClBO,EAAW7E,EAAO6E,SAClBnM,EAAYqH,EAASrH,UAG3BmM,EAASC,kBAAoBve,KAAKwe,UAAUC,eAG5CH,EAAS9D,KAAK,IAAQA,GAAQ,GAAM,KAAQ,IAAOuD,EACnDO,EAAS9D,KAAK,IAAQA,GAAQ,EAAK,KAAQ,IAAOuD,EAClDO,EAAS9D,KAAK,IAAc,IAAPA,GAAe,IAAOuD,EAC3CO,EAAS9D,KAAK,GAAKuD,EAOnBX,EAAS3D,OAAOiF,KAAKjF,GACrB2D,EAAS5D,SAASkF,KAAKlF,EAAUC,GAGjC2D,EAASlD,MAAMrP,IAAI7K,KAAKka,OAGxB,IAAK,IAAI9X,EAAI,EAAGuT,EAAIxD,EAAUrS,OAAQsC,EAAIuT,EAAGvT,IAEzCpC,KAAK2e,sBAAsBvB,EAAU5D,EAASrH,UAAU/P,KAStDqF,EAAA3G,UAAA6d,sBAAV,SAAgCvB,EAAoBwB,GAKhD,IAHQ,IAAA1L,EAAgC0L,WAAtBra,EAAsBqa,EAAQra,KAAxB0M,EAAgB2N,EAAZ3N,KAAEL,EAAUgO,QAClCC,EAAoB3L,EAASsD,MAE1BjP,EAAI,EAAGA,EAAIsX,EAAmBtX,IAEnC6V,EAAS5c,QAAQke,KAAKxL,EAASiE,SAAS5P,GAAIA,GAGhD6V,EAAS5D,SAASsF,KAAKva,EAAM0M,EAAML,IAO7BnJ,EAAoB3G,UAAAud,qBAA9B,SAA+BjB,GAE3B,IAAI3D,EAASzZ,KAAKyZ,OAEZC,EAAa1Z,KAAK0Z,WAExB,IAAKD,EACL,CAII,IAAKF,EAAgBG,GACrB,CAII,IAHQ,IAAAqF,EAAiB3B,EAASa,QAAQvE,GAAWqF,aAC/CC,EAAe,IAAIC,WAAWF,GAE3B3c,EAAI,EAAGA,EAAI2c,EAAc3c,IAE9B4c,EAAa5c,GAAKA,EAGtB,IAAMkc,EAAW,CACb9D,KAAM,IAAIzE,aAAa,CAAC,EAAG,EAAG,EAAG,IACjCwI,kBAAmB,IAAIW,EAAAA,OACvBC,QAASC,EAAYA,aAACC,KAAK,CAAEC,UAAWN,IAAgB,IAGtDO,EAAUnC,EAASa,QAAQvE,GAAY8F,QAAQD,QAErDhG,EAAgBG,GAAc,IAAI+F,EAAMA,OAACF,EAASjB,GAGtD7E,EAASF,EAAgBG,GAG7B,OAAOD,GAIDhS,EAAA3G,UAAA4e,iBAAV,WAEI1f,KAAK4a,aAEL,IAAMpB,EAAWxZ,KAAKqa,UAGtB,GAAKb,EAAStW,aAAapD,OAA3B,CAKM,IAAAgD,EAA6B0W,EAAS1B,OAApCgB,EAAIhW,EAAAgW,KAAEE,EAAIlW,EAAAkW,KAAE2G,EAAI7c,EAAA6c,KAAEC,SAE1B5f,KAAK0S,QAAQmN,SAAS7f,KAAKwe,UAAW1F,EAAME,EAAM2G,EAAMC,KAQrDnY,EAAa3G,UAAA4S,cAApB,SAAqBC,GAIjB,OAFA3T,KAAKye,eAAe7K,aAAaD,EAAOlM,EAASqY,aAE1C9f,KAAKqa,UAAU3G,cAAcjM,EAASqY,cAIvCrY,EAAA3G,UAAAqd,eAAV,WAEI,GAAIne,KAAK4Z,YAAc5Z,KAAKwa,KAC5B,CACIxa,KAAK4Z,UAAY5Z,KAAKwa,KAItB,IAFA,IAAMuF,EAAUnC,EAAOA,QAAC5d,KAAKwa,KAAMlB,GAE1BlX,EAAI,EAAGA,EAAIpC,KAAKqS,QAAQvS,OAAQsC,IACzC,CACI,IAAM4T,EAAQhW,KAAKqS,QAAQjQ,GAErBwX,EAAY5D,EAAM2H,UAOlBrd,GALKyf,EAAQ,GAAKnG,EAAU,GAAM,KAKpB,KAJTmG,EAAQ,GAAKnG,EAAU,GAAM,KAIR,IAAU,EAH/BmG,EAAQ,GAAKnG,EAAU,GAAM,KAKxC5D,EAAM6H,UAAYvd,GAAS,KACR,MAARA,KACS,IAARA,IAAiB,OAM/BmH,EAAA3G,UAAAod,kBAAV,WAEI,IAAM8B,EAAOhgB,KAAKwe,UAAUyB,SAE5B,GAAIjgB,KAAKua,eAAiByF,EAA1B,CAKAhgB,KAAKua,aAAeyF,EAepB,IAbA,IAAME,EAAKlgB,KAAKwe,UAAUC,eACpB7Y,EAAIsa,EAAGta,EACPxE,EAAI8e,EAAG9e,EACP8E,EAAIga,EAAGha,EACP/E,EAAI+e,EAAG/e,EACPgF,EAAK+Z,EAAG/Z,GACRC,EAAK8Z,EAAG9Z,GAERkN,EAAOtT,KAAKqa,UAAUrY,OACtB6X,EAAa7Z,KAAK6Z,WAEpBrD,EAAQ,EAEHpU,EAAI,EAAGA,EAAIkR,EAAKxT,OAAQsC,GAAK,EACtC,CACI,IAAM6B,EAAIqP,EAAKlR,GACT8B,EAAIoP,EAAKlR,EAAI,GAEnByX,EAAWrD,KAAY5Q,EAAI3B,EAAMiC,EAAIhC,EAAKiC,EAC1C0T,EAAWrD,KAAYrV,EAAI+C,EAAM9C,EAAI6C,EAAKmC,KAQ3CqB,EAAA3G,UAAAqf,UAAP,WAEI,IAAMxG,EAAc3Z,KAAK2Z,YAWzB,OATIA,IAGAA,EAAY1P,aAAc,EAG1BjK,KAAK4a,cAGF5a,MAQJyH,EAAS3G,UAAAsf,UAAhB,SAAiBzf,GAIb,OAFAX,KAAKga,QAAUrZ,EAERX,MAWJyH,EAAA3G,UAAAuf,UAAP,WAKI,OAHArgB,KAAK4a,aACL5a,KAAKia,WAAY,EAEVja,MAOJyH,EAAA3G,UAAAwf,QAAP,WAKI,OAHAtgB,KAAK4a,aACL5a,KAAKia,WAAY,EAEVja,MAcJyH,EAAO3G,UAAAG,QAAd,SAAe8Z,GAEX/a,KAAKqa,UAAUC,WACiB,IAA5Bta,KAAKqa,UAAUC,UAEfta,KAAKqa,UAAUkG,UAGnBvgB,KAAKga,QAAU,KACfha,KAAK2Z,YAAc,KACnB3Z,KAAK+Z,WAAW9Y,UAChBjB,KAAK+Z,WAAa,KAClB/Z,KAAK8Z,WAAW7Y,UAChBjB,KAAK8Z,WAAa,KAClB9Z,KAAKqa,UAAY,KACjBra,KAAKyZ,OAAS,KACdzZ,KAAK6Z,WAAa,KAClB7Z,KAAKqS,QAAQvS,OAAS,EACtBE,KAAKqS,QAAU,KAEfX,EAAA5Q,UAAMG,QAAOuf,KAAAxgB,KAAC+a,IAhmCJtT,EAAuBC,yBAAG,EAMjCD,EAAAqY,YAAc,IAAIrV,EAAAA,MA4lC5BhD,EAxmCD,CAA8BgZ,aC1DjBC,EAAgB,CACzB1d,UAAWA,EACXgB,YAAaA,EACbqC,eAAgBA,EAChBmB,sBAAuBA,EACvBoC,UAASA,EACTyD,SAAQA,EACR6B,YAAWA,EACXe,eAAcA,EACdQ,UAASA,EACTS,cAAeA,EACfE,WAAYA,EACZC,eAAgBA"}