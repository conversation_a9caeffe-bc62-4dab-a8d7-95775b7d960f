/*!
 * @pixi/constants - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/constants is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
var E,_,N,T,R,I,A,L,O,U,S,P,D,G,C,M,B,H,F,n;!function(E){E[E.WEBGL_LEGACY=0]="WEBGL_LEGACY",E[E.WEBGL=1]="WEBGL",E[E.WEBGL2=2]="WEBGL2"}(E||(E={})),function(E){E[E.UNKNOWN=0]="UNKNOWN",E[E.WEBGL=1]="WEBGL",E[E.CANVAS=2]="CANVAS"}(_||(_={})),function(E){E[E.COLOR=16384]="COLOR",E[E.DEPTH=256]="DEPTH",E[E.STENCIL=1024]="STENCIL"}(N||(N={})),function(E){E[E.NORMAL=0]="NORMAL",E[E.ADD=1]="ADD",E[E.MULTIPLY=2]="MULTIPLY",E[E.SCREEN=3]="SCREEN",E[E.OVERLAY=4]="OVERLAY",E[E.DARKEN=5]="DARKEN",E[E.LIGHTEN=6]="LIGHTEN",E[E.COLOR_DODGE=7]="COLOR_DODGE",E[E.COLOR_BURN=8]="COLOR_BURN",E[E.HARD_LIGHT=9]="HARD_LIGHT",E[E.SOFT_LIGHT=10]="SOFT_LIGHT",E[E.DIFFERENCE=11]="DIFFERENCE",E[E.EXCLUSION=12]="EXCLUSION",E[E.HUE=13]="HUE",E[E.SATURATION=14]="SATURATION",E[E.COLOR=15]="COLOR",E[E.LUMINOSITY=16]="LUMINOSITY",E[E.NORMAL_NPM=17]="NORMAL_NPM",E[E.ADD_NPM=18]="ADD_NPM",E[E.SCREEN_NPM=19]="SCREEN_NPM",E[E.NONE=20]="NONE",E[E.SRC_OVER=0]="SRC_OVER",E[E.SRC_IN=21]="SRC_IN",E[E.SRC_OUT=22]="SRC_OUT",E[E.SRC_ATOP=23]="SRC_ATOP",E[E.DST_OVER=24]="DST_OVER",E[E.DST_IN=25]="DST_IN",E[E.DST_OUT=26]="DST_OUT",E[E.DST_ATOP=27]="DST_ATOP",E[E.ERASE=26]="ERASE",E[E.SUBTRACT=28]="SUBTRACT",E[E.XOR=29]="XOR"}(T||(T={})),function(E){E[E.POINTS=0]="POINTS",E[E.LINES=1]="LINES",E[E.LINE_LOOP=2]="LINE_LOOP",E[E.LINE_STRIP=3]="LINE_STRIP",E[E.TRIANGLES=4]="TRIANGLES",E[E.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",E[E.TRIANGLE_FAN=6]="TRIANGLE_FAN"}(R||(R={})),function(E){E[E.RGBA=6408]="RGBA",E[E.RGB=6407]="RGB",E[E.RG=33319]="RG",E[E.RED=6403]="RED",E[E.RGBA_INTEGER=36249]="RGBA_INTEGER",E[E.RGB_INTEGER=36248]="RGB_INTEGER",E[E.RG_INTEGER=33320]="RG_INTEGER",E[E.RED_INTEGER=36244]="RED_INTEGER",E[E.ALPHA=6406]="ALPHA",E[E.LUMINANCE=6409]="LUMINANCE",E[E.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",E[E.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",E[E.DEPTH_STENCIL=34041]="DEPTH_STENCIL"}(I||(I={})),function(E){E[E.TEXTURE_2D=3553]="TEXTURE_2D",E[E.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",E[E.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY",E[E.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",E[E.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",E[E.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",E[E.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",E[E.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",E[E.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z"}(A||(A={})),function(E){E[E.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",E[E.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",E[E.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",E[E.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",E[E.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",E[E.UNSIGNED_INT=5125]="UNSIGNED_INT",E[E.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",E[E.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",E[E.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",E[E.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",E[E.BYTE=5120]="BYTE",E[E.SHORT=5122]="SHORT",E[E.INT=5124]="INT",E[E.FLOAT=5126]="FLOAT",E[E.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV",E[E.HALF_FLOAT=36193]="HALF_FLOAT"}(L||(L={})),function(E){E[E.FLOAT=0]="FLOAT",E[E.INT=1]="INT",E[E.UINT=2]="UINT"}(O||(O={})),function(E){E[E.NEAREST=0]="NEAREST",E[E.LINEAR=1]="LINEAR"}(U||(U={})),function(E){E[E.CLAMP=33071]="CLAMP",E[E.REPEAT=10497]="REPEAT",E[E.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT"}(S||(S={})),function(E){E[E.OFF=0]="OFF",E[E.POW2=1]="POW2",E[E.ON=2]="ON",E[E.ON_MANUAL=3]="ON_MANUAL"}(P||(P={})),function(E){E[E.NPM=0]="NPM",E[E.UNPACK=1]="UNPACK",E[E.PMA=2]="PMA",E[E.NO_PREMULTIPLIED_ALPHA=0]="NO_PREMULTIPLIED_ALPHA",E[E.PREMULTIPLY_ON_UPLOAD=1]="PREMULTIPLY_ON_UPLOAD",E[E.PREMULTIPLY_ALPHA=2]="PREMULTIPLY_ALPHA",E[E.PREMULTIPLIED_ALPHA=2]="PREMULTIPLIED_ALPHA"}(D||(D={})),function(E){E[E.NO=0]="NO",E[E.YES=1]="YES",E[E.AUTO=2]="AUTO",E[E.BLEND=0]="BLEND",E[E.CLEAR=1]="CLEAR",E[E.BLIT=2]="BLIT"}(G||(G={})),function(E){E[E.AUTO=0]="AUTO",E[E.MANUAL=1]="MANUAL"}(C||(C={})),function(E){E.LOW="lowp",E.MEDIUM="mediump",E.HIGH="highp"}(M||(M={})),function(E){E[E.NONE=0]="NONE",E[E.SCISSOR=1]="SCISSOR",E[E.STENCIL=2]="STENCIL",E[E.SPRITE=3]="SPRITE",E[E.COLOR=4]="COLOR"}(B||(B={})),function(E){E[E.RED=1]="RED",E[E.GREEN=2]="GREEN",E[E.BLUE=4]="BLUE",E[E.ALPHA=8]="ALPHA"}(H||(H={})),function(E){E[E.NONE=0]="NONE",E[E.LOW=2]="LOW",E[E.MEDIUM=4]="MEDIUM",E[E.HIGH=8]="HIGH"}(F||(F={})),function(E){E[E.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",E[E.ARRAY_BUFFER=34962]="ARRAY_BUFFER",E[E.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER"}(n||(n={}));export{D as ALPHA_MODES,T as BLEND_MODES,N as BUFFER_BITS,n as BUFFER_TYPE,G as CLEAR_MODES,H as COLOR_MASK_BITS,R as DRAW_MODES,E as ENV,I as FORMATS,C as GC_MODES,B as MASK_TYPES,P as MIPMAP_MODES,F as MSAA_QUALITY,M as PRECISION,_ as RENDERER_TYPE,O as SAMPLER_TYPES,U as SCALE_MODES,A as TARGETS,L as TYPES,S as WRAP_MODES};
//# sourceMappingURL=constants.min.mjs.map
