{"version": 3, "file": "math.js", "sources": ["../../src/const.ts", "../../src/Point.ts", "../../src/shapes/Rectangle.ts", "../../src/shapes/Circle.ts", "../../src/shapes/Ellipse.ts", "../../src/shapes/Polygon.ts", "../../src/shapes/RoundedRectangle.ts", "../../src/IPointData.ts", "../../src/IPoint.ts", "../../src/ObservablePoint.ts", "../../src/Matrix.ts", "../../src/groupD8.ts", "../../src/Transform.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * Two Pi.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const PI_2 = Math.PI * 2;\n\n/**\n * Conversion factor for converting radians to degrees.\n * @static\n * @member {number} RAD_TO_DEG\n * @memberof PIXI\n */\nexport const RAD_TO_DEG = 180 / Math.PI;\n\n/**\n * Conversion factor for converting degrees to radians.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const DEG_TO_RAD = Math.PI / 180;\n\n/**\n * Constants that identify shapes, mainly to prevent `instanceof` calls.\n * @static\n * @memberof PIXI\n * @enum {number}\n * @property {number} POLY Polygon\n * @property {number} RECT Rectangle\n * @property {number} CIRC Circle\n * @property {number} ELIP Ellipse\n * @property {number} RREC Rounded Rectangle\n */\nexport enum SHAPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    POLY = 0,\n    RECT = 1,\n    CIRC = 2,\n    ELIP = 3,\n    RREC = 4,\n}\n", "import type { IPoint } from './IPoint';\nimport type { IPointData } from './IPointData';\n\nexport interface Point extends GlobalMixins.Point, IPoint {}\n\n/**\n * The Point object represents a location in a two-dimensional coordinate system, where `x` represents\n * the position on the horizontal axis and `y` represents the position on the vertical axis\n * @class\n * @memberof PIXI\n * @implements {IPoint}\n */\nexport class Point implements IPoint\n{\n    /** Position of the point on the x axis */\n    public x = 0;\n    /** Position of the point on the y axis */\n    public y = 0;\n\n    /**\n     * Creates a new `Point`\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=0] - position of the point on the y axis\n     */\n    constructor(x = 0, y = 0)\n    {\n        this.x = x;\n        this.y = y;\n    }\n\n    /**\n     * Creates a clone of this point\n     * @returns A clone of this point\n     */\n    clone(): Point\n    {\n        return new Point(this.x, this.y);\n    }\n\n    /**\n     * Copies `x` and `y` from the given point into this point\n     * @param p - The point to copy from\n     * @returns The point instance itself\n     */\n    copyFrom(p: IPointData): this\n    {\n        this.set(p.x, p.y);\n\n        return this;\n    }\n\n    /**\n     * Copies this point's x and y into the given point (`p`).\n     * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n     * @returns The point (`p`) with values updated\n     */\n    copyTo<T extends IPoint>(p: T): T\n    {\n        p.set(this.x, this.y);\n\n        return p;\n    }\n\n    /**\n     * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n     * @param p - The point to check\n     * @returns Returns `true` if both `x` and `y` are equal\n     */\n    equals(p: IPointData): boolean\n    {\n        return (p.x === this.x) && (p.y === this.y);\n    }\n\n    /**\n     * Sets the point to a new `x` and `y` position.\n     * If `y` is omitted, both `x` and `y` will be set to `x`.\n     * @param {number} [x=0] - position of the point on the `x` axis\n     * @param {number} [y=x] - position of the point on the `y` axis\n     * @returns The point instance itself\n     */\n    set(x = 0, y = x): this\n    {\n        this.x = x;\n        this.y = y;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Point x=${this.x} y=${this.y}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\nimport type { Matrix } from '../Matrix';\nimport { Point } from '../Point';\n\nconst tempPoints = [new Point(), new Point(), new Point(), new Point()];\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Rectangle extends GlobalMixins.Rectangle {}\n\n/**\n * Size object, contains width and height\n * @memberof PIXI\n * @typedef {object} ISize\n * @property {number} width - Width component\n * @property {number} height - Height component\n */\n\n/**\n * Rectangle object is an area defined by its position, as indicated by its top-left corner\n * point (x, y) and by its width and its height.\n * @memberof PIXI\n */\nexport class Rectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RECT\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RECT;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rectangle\n     * @param width - The overall width of the rectangle\n     * @param height - The overall height of the rectangle\n     */\n    constructor(x: string | number = 0, y: string | number = 0, width: string | number = 0, height: string | number = 0)\n    {\n        this.x = Number(x);\n        this.y = Number(y);\n        this.width = Number(width);\n        this.height = Number(height);\n        this.type = SHAPES.RECT;\n    }\n\n    /** Returns the left edge of the rectangle. */\n    get left(): number\n    {\n        return this.x;\n    }\n\n    /** Returns the right edge of the rectangle. */\n    get right(): number\n    {\n        return this.x + this.width;\n    }\n\n    /** Returns the top edge of the rectangle. */\n    get top(): number\n    {\n        return this.y;\n    }\n\n    /** Returns the bottom edge of the rectangle. */\n    get bottom(): number\n    {\n        return this.y + this.height;\n    }\n\n    /** A constant empty rectangle. */\n    static get EMPTY(): Rectangle\n    {\n        return new Rectangle(0, 0, 0, 0);\n    }\n\n    /**\n     * Creates a clone of this Rectangle\n     * @returns a copy of the rectangle\n     */\n    clone(): Rectangle\n    {\n        return new Rectangle(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Copies another rectangle to this one.\n     * @param rectangle - The rectangle to copy from.\n     * @returns Returns itself.\n     */\n    copyFrom(rectangle: Rectangle): Rectangle\n    {\n        this.x = rectangle.x;\n        this.y = rectangle.y;\n        this.width = rectangle.width;\n        this.height = rectangle.height;\n\n        return this;\n    }\n\n    /**\n     * Copies this rectangle to another one.\n     * @param rectangle - The rectangle to copy to.\n     * @returns Returns given parameter.\n     */\n    copyTo(rectangle: Rectangle): Rectangle\n    {\n        rectangle.x = this.x;\n        rectangle.y = this.y;\n        rectangle.width = this.width;\n        rectangle.height = this.height;\n\n        return rectangle;\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rectangle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Rectangle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        if (x >= this.x && x < this.x + this.width)\n        {\n            if (y >= this.y && y < this.y + this.height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Determines whether the `other` Rectangle transformed by `transform` intersects with `this` Rectangle object.\n     * Returns true only if the area of the intersection is >0, this means that Rectangles\n     * sharing a side are not overlapping. Another side effect is that an arealess rectangle\n     * (width or height equal to zero) can't intersect any other rectangle.\n     * @param {Rectangle} other - The Rectangle to intersect with `this`.\n     * @param {Matrix} transform - The transformation matrix of `other`.\n     * @returns {boolean} A value of `true` if the transformed `other` Rectangle intersects with `this`; otherwise `false`.\n     */\n    intersects(other: Rectangle, transform?: Matrix): boolean\n    {\n        if (!transform)\n        {\n            const x0 = this.x < other.x ? other.x : this.x;\n            const x1 = this.right > other.right ? other.right : this.right;\n\n            if (x1 <= x0)\n            {\n                return false;\n            }\n\n            const y0 = this.y < other.y ? other.y : this.y;\n            const y1 = this.bottom > other.bottom ? other.bottom : this.bottom;\n\n            return y1 > y0;\n        }\n\n        const x0 = this.left;\n        const x1 = this.right;\n        const y0 = this.top;\n        const y1 = this.bottom;\n\n        if (x1 <= x0 || y1 <= y0)\n        {\n            return false;\n        }\n\n        const lt = tempPoints[0].set(other.left, other.top);\n        const lb = tempPoints[1].set(other.left, other.bottom);\n        const rt = tempPoints[2].set(other.right, other.top);\n        const rb = tempPoints[3].set(other.right, other.bottom);\n\n        if (rt.x <= lt.x || lb.y <= lt.y)\n        {\n            return false;\n        }\n\n        const s = Math.sign((transform.a * transform.d) - (transform.b * transform.c));\n\n        if (s === 0)\n        {\n            return false;\n        }\n\n        transform.apply(lt, lt);\n        transform.apply(lb, lb);\n        transform.apply(rt, rt);\n        transform.apply(rb, rb);\n\n        if (Math.max(lt.x, lb.x, rt.x, rb.x) <= x0\n            || Math.min(lt.x, lb.x, rt.x, rb.x) >= x1\n            || Math.max(lt.y, lb.y, rt.y, rb.y) <= y0\n            || Math.min(lt.y, lb.y, rt.y, rb.y) >= y1)\n        {\n            return false;\n        }\n\n        const nx = s * (lb.y - lt.y);\n        const ny = s * (lt.x - lb.x);\n        const n00 = (nx * x0) + (ny * y0);\n        const n10 = (nx * x1) + (ny * y0);\n        const n01 = (nx * x0) + (ny * y1);\n        const n11 = (nx * x1) + (ny * y1);\n\n        if (Math.max(n00, n10, n01, n11) <= (nx * lt.x) + (ny * lt.y)\n            || Math.min(n00, n10, n01, n11) >= (nx * rb.x) + (ny * rb.y))\n        {\n            return false;\n        }\n\n        const mx = s * (lt.y - rt.y);\n        const my = s * (rt.x - lt.x);\n        const m00 = (mx * x0) + (my * y0);\n        const m10 = (mx * x1) + (my * y0);\n        const m01 = (mx * x0) + (my * y1);\n        const m11 = (mx * x1) + (my * y1);\n\n        if (Math.max(m00, m10, m01, m11) <= (mx * lt.x) + (my * lt.y)\n            || Math.min(m00, m10, m01, m11) >= (mx * rb.x) + (my * rb.y))\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * Pads the rectangle making it grow in all directions.\n     * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n     * @param paddingX - The horizontal padding amount.\n     * @param paddingY - The vertical padding amount.\n     * @returns Returns itself.\n     */\n    pad(paddingX = 0, paddingY = paddingX): this\n    {\n        this.x -= paddingX;\n        this.y -= paddingY;\n\n        this.width += paddingX * 2;\n        this.height += paddingY * 2;\n\n        return this;\n    }\n\n    /**\n     * Fits this rectangle around the passed one.\n     * @param rectangle - The rectangle to fit.\n     * @returns Returns itself.\n     */\n    fit(rectangle: Rectangle): this\n    {\n        const x1 = Math.max(this.x, rectangle.x);\n        const x2 = Math.min(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.max(this.y, rectangle.y);\n        const y2 = Math.min(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = Math.max(x2 - x1, 0);\n        this.y = y1;\n        this.height = Math.max(y2 - y1, 0);\n\n        return this;\n    }\n\n    /**\n     * Enlarges rectangle that way its corners lie on grid\n     * @param resolution - resolution\n     * @param eps - precision\n     * @returns Returns itself.\n     */\n    ceil(resolution = 1, eps = 0.001): this\n    {\n        const x2 = Math.ceil((this.x + this.width - eps) * resolution) / resolution;\n        const y2 = Math.ceil((this.y + this.height - eps) * resolution) / resolution;\n\n        this.x = Math.floor((this.x + eps) * resolution) / resolution;\n        this.y = Math.floor((this.y + eps) * resolution) / resolution;\n\n        this.width = x2 - this.x;\n        this.height = y2 - this.y;\n\n        return this;\n    }\n\n    /**\n     * Enlarges this rectangle to include the passed rectangle.\n     * @param rectangle - The rectangle to include.\n     * @returns Returns itself.\n     */\n    enlarge(rectangle: Rectangle): this\n    {\n        const x1 = Math.min(this.x, rectangle.x);\n        const x2 = Math.max(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.min(this.y, rectangle.y);\n        const y2 = Math.max(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = x2 - x1;\n        this.y = y1;\n        this.height = y2 - y1;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from './../const';\nimport { Rectangle } from './Rectangle';\n\n/**\n * The Circle object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Circle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.CIRC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.CIRC;\n\n    /**\n     * @param x - The X coordinate of the center of this circle\n     * @param y - The Y coordinate of the center of this circle\n     * @param radius - The radius of the circle\n     */\n    constructor(x = 0, y = 0, radius = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.radius = radius;\n\n        this.type = SHAPES.CIRC;\n    }\n\n    /**\n     * Creates a clone of this Circle instance\n     * @returns A copy of the Circle\n     */\n    clone(): Circle\n    {\n        return new Circle(this.x, this.y, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this circle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Circle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.radius <= 0)\n        {\n            return false;\n        }\n\n        const r2 = this.radius * this.radius;\n        let dx = (this.x - x);\n        let dy = (this.y - y);\n\n        dx *= dx;\n        dy *= dy;\n\n        return (dx + dy <= r2);\n    }\n\n    /**\n     * Returns the framing rectangle of the circle as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.radius, this.y - this.radius, this.radius * 2, this.radius * 2);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`;\n    }\n    // #endif\n}\n", "import { Rectangle } from './Rectangle';\nimport { SHAPES } from '../const';\n\n/**\n * The Ellipse object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Ellipse\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.ELIP\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.ELIP;\n\n    /**\n     * @param x - The X coordinate of the center of this ellipse\n     * @param y - The Y coordinate of the center of this ellipse\n     * @param halfWidth - The half width of this ellipse\n     * @param halfHeight - The half height of this ellipse\n     */\n    constructor(x = 0, y = 0, halfWidth = 0, halfHeight = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = halfWidth;\n        this.height = halfHeight;\n\n        this.type = SHAPES.ELIP;\n    }\n\n    /**\n     * Creates a clone of this Ellipse instance\n     * @returns {PIXI.Ellipse} A copy of the ellipse\n     */\n    clone(): Ellipse\n    {\n        return new Ellipse(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this ellipse\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coords are within this ellipse\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        // normalize the coords to an ellipse with center 0,0\n        let normx = ((x - this.x) / this.width);\n        let normy = ((y - this.y) / this.height);\n\n        normx *= normx;\n        normy *= normy;\n\n        return (normx + normy <= 1);\n    }\n\n    /**\n     * Returns the framing rectangle of the ellipse as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.width, this.y - this.height, this.width, this.height);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Ellipse x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\nimport type { IPointData } from '../IPointData';\n\n/**\n * A class to define a shape via user defined coordinates.\n * @memberof PIXI\n */\nexport class Polygon\n{\n    /** An array of the points of this polygon. */\n    public points: number[];\n\n    /** `false` after moveTo, `true` after `closePath`. In all other cases it is `true`. */\n    public closeStroke: boolean;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.POLY\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.POLY;\n\n    constructor(points: IPointData[] | number[]);\n    constructor(...points: IPointData[] | number[]);\n\n    /**\n     * @param {PIXI.IPointData[]|number[]} points - This can be an array of Points\n     *  that form the polygon, a flat array of numbers that will be interpreted as [x,y, x,y, ...], or\n     *  the arguments passed can be all the points of the polygon e.g.\n     *  `new PIXI.Polygon(new PIXI.Point(), new PIXI.Point(), ...)`, or the arguments passed can be flat\n     *  x,y values e.g. `new Polygon(x,y, x,y, x,y, ...)` where `x` and `y` are Numbers.\n     */\n    constructor(...points: any[])\n    {\n        let flat: IPointData[] | number[] = Array.isArray(points[0]) ? points[0] : points;\n\n        // if this is an array of points, convert it to a flat array of numbers\n        if (typeof flat[0] !== 'number')\n        {\n            const p: number[] = [];\n\n            for (let i = 0, il = flat.length; i < il; i++)\n            {\n                p.push((flat[i] as IPointData).x, (flat[i] as IPointData).y);\n            }\n\n            flat = p;\n        }\n\n        this.points = flat as number[];\n        this.type = SHAPES.POLY;\n        this.closeStroke = true;\n    }\n\n    /**\n     * Creates a clone of this polygon.\n     * @returns - A copy of the polygon.\n     */\n    clone(): Polygon\n    {\n        const points = this.points.slice();\n        const polygon = new Polygon(points);\n\n        polygon.closeStroke = this.closeStroke;\n\n        return polygon;\n    }\n\n    /**\n     * Checks whether the x and y coordinates passed to this function are contained within this polygon.\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this polygon.\n     */\n    contains(x: number, y: number): boolean\n    {\n        let inside = false;\n\n        // use some raycasting to test hits\n        // https://github.com/substack/point-in-polygon/blob/master/index.js\n        const length = this.points.length / 2;\n\n        for (let i = 0, j = length - 1; i < length; j = i++)\n        {\n            const xi = this.points[i * 2];\n            const yi = this.points[(i * 2) + 1];\n            const xj = this.points[j * 2];\n            const yj = this.points[(j * 2) + 1];\n            const intersect = ((yi > y) !== (yj > y)) && (x < ((xj - xi) * ((y - yi) / (yj - yi))) + xi);\n\n            if (intersect)\n            {\n                inside = !inside;\n            }\n        }\n\n        return inside;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Polygon`\n            + `closeStroke=${this.closeStroke}`\n            + `points=${this.points.reduce((pointsDesc, currentPoint) => `${pointsDesc}, ${currentPoint}`, '')}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\n\n/**\n * The Rounded Rectangle object is an area that has nice rounded corners, as indicated by its\n * top-left corner point (x, y) and by its width and its height and its radius.\n * @memberof PIXI\n */\nexport class RoundedRectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /** @default 20 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RREC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RREC;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rounded rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rounded rectangle\n     * @param width - The overall width of this rounded rectangle\n     * @param height - The overall height of this rounded rectangle\n     * @param radius - Controls the radius of the rounded corners\n     */\n    constructor(x = 0, y = 0, width = 0, height = 0, radius = 20)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n        this.radius = radius;\n        this.type = SHAPES.RREC;\n    }\n\n    /**\n     * Creates a clone of this Rounded Rectangle.\n     * @returns - A copy of the rounded rectangle.\n     */\n    clone(): RoundedRectangle\n    {\n        return new RoundedRectangle(this.x, this.y, this.width, this.height, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rounded Rectangle\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this Rounded Rectangle.\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n        if (x >= this.x && x <= this.x + this.width)\n        {\n            if (y >= this.y && y <= this.y + this.height)\n            {\n                const radius = Math.max(0, Math.min(this.radius, Math.min(this.width, this.height) / 2));\n\n                if ((y >= this.y + radius && y <= this.y + this.height - radius)\n                || (x >= this.x + radius && x <= this.x + this.width - radius))\n                {\n                    return true;\n                }\n                let dx = x - (this.x + radius);\n                let dy = y - (this.y + radius);\n                const radius2 = radius * radius;\n\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + this.width - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dy = y - (this.y + this.height - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:RoundedRectangle x=${this.x} y=${this.y}`\n            + `width=${this.width} height=${this.height} radius=${this.radius}]`;\n    }\n    // #endif\n}\n", "export interface IPointData extends GlobalMixins.IPointData\n{\n    x: number;\n    y: number;\n}\n\n/**\n * Common interface for points. Both Point and ObservablePoint implement it\n * @memberof PIXI\n * @interface IPointData\n */\n\n/**\n * X coord\n * @memberof PIXI.IPointData#\n * @member {number} x\n */\n\n/**\n * Y coord\n * @memberof PIXI.IPointData#\n * @member {number} y\n */\n", "import type { IPointData } from './IPointData';\n\nexport interface IPoint extends IPointData\n{\n    copyFrom(p: IPointData): this;\n    copyTo<T extends IPoint>(p: T): T;\n    equals(p: IPointData): boolean;\n    set(x?: number, y?: number): void;\n}\n/**\n * Common interface for points. Both Point and ObservablePoint implement it\n * @memberof PIXI\n * @interface IPoint\n * @extends PIXI.IPointData\n */\n\n/**\n * Sets the point to a new x and y position.\n * If y is omitted, both x and y will be set to x.\n * @method set\n * @memberof PIXI.IPoint#\n * @param {number} [x=0] - position of the point on the x axis\n * @param {number} [y=x] - position of the point on the y axis\n */\n\n/**\n * Copies x and y from the given point\n * @method copyFrom\n * @memberof PIXI.IPoint#\n * @param {PIXI.IPointData} p - The point to copy from\n * @returns {this} Returns itself.\n */\n\n/**\n * Copies x and y into the given point\n * @method copyTo\n * @memberof PIXI.IPoint#\n * @param {PIXI.IPoint} p - The point to copy.\n * @returns {PIXI.IPoint} Given point with values updated\n */\n\n/**\n * Returns true if the given point is equal to this point\n * @method equals\n * @memberof PIXI.IPoint#\n * @param {PIXI.IPointData} p - The point to check\n * @returns {boolean} Whether the given point equal to this point\n */\n\n", "import type { IPointData } from './IPointData';\nimport type { IPoint } from './IPoint';\n\nexport interface ObservablePoint extends GlobalMixins.Point, IPoint {}\n\n/**\n * The ObservablePoint object represents a location in a two-dimensional coordinate system, where `x` represents\n * the position on the horizontal axis and `y` represents the position on the vertical axis.\n *\n * An `ObservablePoint` is a point that triggers a callback when the point's position is changed.\n * @memberof PIXI\n */\nexport class ObservablePoint<T = any> implements IPoint\n{\n    /** The callback function triggered when `x` and/or `y` are changed */\n    public cb: (this: T) => any;\n\n    /** The owner of the callback */\n    public scope: any;\n\n    _x: number;\n    _y: number;\n\n    /**\n     * Creates a new `ObservablePoint`\n     * @param cb - callback function triggered when `x` and/or `y` are changed\n     * @param scope - owner of callback\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=0] - position of the point on the y axis\n     */\n    constructor(cb: (this: T) => any, scope: T, x = 0, y = 0)\n    {\n        this._x = x;\n        this._y = y;\n\n        this.cb = cb;\n        this.scope = scope;\n    }\n\n    /**\n     * Creates a clone of this point.\n     * The callback and scope params can be overridden otherwise they will default\n     * to the clone object's values.\n     * @override\n     * @param cb - The callback function triggered when `x` and/or `y` are changed\n     * @param scope - The owner of the callback\n     * @returns a copy of this observable point\n     */\n    clone(cb = this.cb, scope = this.scope): ObservablePoint\n    {\n        return new ObservablePoint(cb, scope, this._x, this._y);\n    }\n\n    /**\n     * Sets the point to a new `x` and `y` position.\n     * If `y` is omitted, both `x` and `y` will be set to `x`.\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=x] - position of the point on the y axis\n     * @returns The observable point instance itself\n     */\n    set(x = 0, y = x): this\n    {\n        if (this._x !== x || this._y !== y)\n        {\n            this._x = x;\n            this._y = y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies x and y from the given point (`p`)\n     * @param p - The point to copy from. Can be any of type that is or extends `IPointData`\n     * @returns The observable point instance itself\n     */\n    copyFrom(p: IPointData): this\n    {\n        if (this._x !== p.x || this._y !== p.y)\n        {\n            this._x = p.x;\n            this._y = p.y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies this point's x and y into that of the given point (`p`)\n     * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n     * @returns The point (`p`) with values updated\n     */\n    copyTo<T extends IPoint>(p: T): T\n    {\n        p.set(this._x, this._y);\n\n        return p;\n    }\n\n    /**\n     * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n     * @param p - The point to check\n     * @returns Returns `true` if both `x` and `y` are equal\n     */\n    equals(p: IPointData): boolean\n    {\n        return (p.x === this._x) && (p.y === this._y);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:ObservablePoint x=${0} y=${0} scope=${this.scope}]`;\n    }\n    // #endif\n\n    /** Position of the observable point on the x axis. */\n    get x(): number\n    {\n        return this._x;\n    }\n\n    set x(value: number)\n    {\n        if (this._x !== value)\n        {\n            this._x = value;\n            this.cb.call(this.scope);\n        }\n    }\n\n    /** Position of the observable point on the y axis. */\n    get y(): number\n    {\n        return this._y;\n    }\n\n    set y(value: number)\n    {\n        if (this._y !== value)\n        {\n            this._y = value;\n            this.cb.call(this.scope);\n        }\n    }\n}\n", "import { Point } from './Point';\nimport { PI_2 } from './const';\n\nimport type { Transform } from './Transform';\nimport type { IPointData } from './IPointData';\n\n/**\n * The PixiJS Matrix as a class makes it a lot faster.\n *\n * Here is a representation of it:\n * ```js\n * | a | c | tx|\n * | b | d | ty|\n * | 0 | 0 | 1 |\n * ```\n * @memberof PIXI\n */\nexport class Matrix\n{\n    /** @default 1 */\n    public a: number;\n\n    /** @default 0 */\n    public b: number;\n\n    /** @default 0 */\n    public c: number;\n\n    /** @default 1 */\n    public d: number;\n\n    /** @default 0 */\n    public tx: number;\n\n    /** @default 0 */\n    public ty: number;\n\n    public array: Float32Array | null = null;\n\n    /**\n     * @param a - x scale\n     * @param b - y skew\n     * @param c - x skew\n     * @param d - y scale\n     * @param tx - x translation\n     * @param ty - y translation\n     */\n    constructor(a = 1, b = 0, c = 0, d = 1, tx = 0, ty = 0)\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n    }\n\n    /**\n     * Creates a Matrix object based on the given array. The Element to Matrix mapping order is as follows:\n     *\n     * a = array[0]\n     * b = array[1]\n     * c = array[3]\n     * d = array[4]\n     * tx = array[2]\n     * ty = array[5]\n     * @param array - The array that the matrix will be populated from.\n     */\n    fromArray(array: number[]): void\n    {\n        this.a = array[0];\n        this.b = array[1];\n        this.c = array[3];\n        this.d = array[4];\n        this.tx = array[2];\n        this.ty = array[5];\n    }\n\n    /**\n     * Sets the matrix properties.\n     * @param a - Matrix component\n     * @param b - Matrix component\n     * @param c - Matrix component\n     * @param d - Matrix component\n     * @param tx - Matrix component\n     * @param ty - Matrix component\n     * @returns This matrix. Good for chaining method calls.\n     */\n    set(a: number, b: number, c: number, d: number, tx: number, ty: number): this\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n\n        return this;\n    }\n\n    /**\n     * Creates an array from the current Matrix object.\n     * @param transpose - Whether we need to transpose the matrix or not\n     * @param [out=new Float32Array(9)] - If provided the array will be assigned to out\n     * @returns The newly created array which contains the matrix\n     */\n    toArray(transpose: boolean, out?: Float32Array): Float32Array\n    {\n        if (!this.array)\n        {\n            this.array = new Float32Array(9);\n        }\n\n        const array = out || this.array;\n\n        if (transpose)\n        {\n            array[0] = this.a;\n            array[1] = this.b;\n            array[2] = 0;\n            array[3] = this.c;\n            array[4] = this.d;\n            array[5] = 0;\n            array[6] = this.tx;\n            array[7] = this.ty;\n            array[8] = 1;\n        }\n        else\n        {\n            array[0] = this.a;\n            array[1] = this.c;\n            array[2] = this.tx;\n            array[3] = this.b;\n            array[4] = this.d;\n            array[5] = this.ty;\n            array[6] = 0;\n            array[7] = 0;\n            array[8] = 1;\n        }\n\n        return array;\n    }\n\n    /**\n     * Get a new position with the current transformation applied.\n     * Can be used to go from a child's coordinate space to the world coordinate space. (e.g. rendering)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, transformed through this matrix\n     */\n    apply<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.a * x) + (this.c * y) + this.tx;\n        newPos.y = (this.b * x) + (this.d * y) + this.ty;\n\n        return newPos;\n    }\n\n    /**\n     * Get a new position with the inverse of the current transformation applied.\n     * Can be used to go from the world coordinate space to a child's coordinate space. (e.g. input)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, inverse-transformed through this matrix\n     */\n    applyInverse<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const id = 1 / ((this.a * this.d) + (this.c * -this.b));\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.d * id * x) + (-this.c * id * y) + (((this.ty * this.c) - (this.tx * this.d)) * id);\n        newPos.y = (this.a * id * y) + (-this.b * id * x) + (((-this.ty * this.a) + (this.tx * this.b)) * id);\n\n        return newPos;\n    }\n\n    /**\n     * Translates the matrix on the x and y.\n     * @param x - How much to translate x by\n     * @param y - How much to translate y by\n     * @returns This matrix. Good for chaining method calls.\n     */\n    translate(x: number, y: number): this\n    {\n        this.tx += x;\n        this.ty += y;\n\n        return this;\n    }\n\n    /**\n     * Applies a scale transformation to the matrix.\n     * @param x - The amount to scale horizontally\n     * @param y - The amount to scale vertically\n     * @returns This matrix. Good for chaining method calls.\n     */\n    scale(x: number, y: number): this\n    {\n        this.a *= x;\n        this.d *= y;\n        this.c *= x;\n        this.b *= y;\n        this.tx *= x;\n        this.ty *= y;\n\n        return this;\n    }\n\n    /**\n     * Applies a rotation transformation to the matrix.\n     * @param angle - The angle in radians.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    rotate(angle: number): this\n    {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n\n        const a1 = this.a;\n        const c1 = this.c;\n        const tx1 = this.tx;\n\n        this.a = (a1 * cos) - (this.b * sin);\n        this.b = (a1 * sin) + (this.b * cos);\n        this.c = (c1 * cos) - (this.d * sin);\n        this.d = (c1 * sin) + (this.d * cos);\n        this.tx = (tx1 * cos) - (this.ty * sin);\n        this.ty = (tx1 * sin) + (this.ty * cos);\n\n        return this;\n    }\n\n    /**\n     * Appends the given Matrix to this Matrix.\n     * @param matrix - The matrix to append.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    append(matrix: Matrix): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n\n        this.a = (matrix.a * a1) + (matrix.b * c1);\n        this.b = (matrix.a * b1) + (matrix.b * d1);\n        this.c = (matrix.c * a1) + (matrix.d * c1);\n        this.d = (matrix.c * b1) + (matrix.d * d1);\n\n        this.tx = (matrix.tx * a1) + (matrix.ty * c1) + this.tx;\n        this.ty = (matrix.tx * b1) + (matrix.ty * d1) + this.ty;\n\n        return this;\n    }\n\n    /**\n     * Sets the matrix based on all the available properties\n     * @param x - Position on the x axis\n     * @param y - Position on the y axis\n     * @param pivotX - Pivot on the x axis\n     * @param pivotY - Pivot on the y axis\n     * @param scaleX - Scale on the x axis\n     * @param scaleY - Scale on the y axis\n     * @param rotation - Rotation in radians\n     * @param skewX - Skew on the x axis\n     * @param skewY - Skew on the y axis\n     * @returns This matrix. Good for chaining method calls.\n     */\n    setTransform(x: number, y: number, pivotX: number, pivotY: number, scaleX: number,\n        scaleY: number, rotation: number, skewX: number, skewY: number): this\n    {\n        this.a = Math.cos(rotation + skewY) * scaleX;\n        this.b = Math.sin(rotation + skewY) * scaleX;\n        this.c = -Math.sin(rotation - skewX) * scaleY;\n        this.d = Math.cos(rotation - skewX) * scaleY;\n\n        this.tx = x - ((pivotX * this.a) + (pivotY * this.c));\n        this.ty = y - ((pivotX * this.b) + (pivotY * this.d));\n\n        return this;\n    }\n\n    /**\n     * Prepends the given Matrix to this Matrix.\n     * @param matrix - The matrix to prepend\n     * @returns This matrix. Good for chaining method calls.\n     */\n    prepend(matrix: Matrix): this\n    {\n        const tx1 = this.tx;\n\n        if (matrix.a !== 1 || matrix.b !== 0 || matrix.c !== 0 || matrix.d !== 1)\n        {\n            const a1 = this.a;\n            const c1 = this.c;\n\n            this.a = (a1 * matrix.a) + (this.b * matrix.c);\n            this.b = (a1 * matrix.b) + (this.b * matrix.d);\n            this.c = (c1 * matrix.a) + (this.d * matrix.c);\n            this.d = (c1 * matrix.b) + (this.d * matrix.d);\n        }\n\n        this.tx = (tx1 * matrix.a) + (this.ty * matrix.c) + matrix.tx;\n        this.ty = (tx1 * matrix.b) + (this.ty * matrix.d) + matrix.ty;\n\n        return this;\n    }\n\n    /**\n     * Decomposes the matrix (x, y, scaleX, scaleY, and rotation) and sets the properties on to a transform.\n     * @param transform - The transform to apply the properties to.\n     * @returns The transform with the newly applied properties\n     */\n    decompose(transform: Transform): Transform\n    {\n        // sort out rotation / skew..\n        const a = this.a;\n        const b = this.b;\n        const c = this.c;\n        const d = this.d;\n        const pivot = transform.pivot;\n\n        const skewX = -Math.atan2(-c, d);\n        const skewY = Math.atan2(b, a);\n\n        const delta = Math.abs(skewX + skewY);\n\n        if (delta < 0.00001 || Math.abs(PI_2 - delta) < 0.00001)\n        {\n            transform.rotation = skewY;\n            transform.skew.x = transform.skew.y = 0;\n        }\n        else\n        {\n            transform.rotation = 0;\n            transform.skew.x = skewX;\n            transform.skew.y = skewY;\n        }\n\n        // next set scale\n        transform.scale.x = Math.sqrt((a * a) + (b * b));\n        transform.scale.y = Math.sqrt((c * c) + (d * d));\n\n        // next set position\n        transform.position.x = this.tx + ((pivot.x * a) + (pivot.y * c));\n        transform.position.y = this.ty + ((pivot.x * b) + (pivot.y * d));\n\n        return transform;\n    }\n\n    /**\n     * Inverts this matrix\n     * @returns This matrix. Good for chaining method calls.\n     */\n    invert(): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n        const tx1 = this.tx;\n        const n = (a1 * d1) - (b1 * c1);\n\n        this.a = d1 / n;\n        this.b = -b1 / n;\n        this.c = -c1 / n;\n        this.d = a1 / n;\n        this.tx = ((c1 * this.ty) - (d1 * tx1)) / n;\n        this.ty = -((a1 * this.ty) - (b1 * tx1)) / n;\n\n        return this;\n    }\n\n    /**\n     * Resets this Matrix to an identity (default) matrix.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    identity(): this\n    {\n        this.a = 1;\n        this.b = 0;\n        this.c = 0;\n        this.d = 1;\n        this.tx = 0;\n        this.ty = 0;\n\n        return this;\n    }\n\n    /**\n     * Creates a new Matrix object with the same values as this one.\n     * @returns A copy of this matrix. Good for chaining method calls.\n     */\n    clone(): Matrix\n    {\n        const matrix = new Matrix();\n\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the given matrix to be the same as the ones in this matrix\n     * @param matrix - The matrix to copy to.\n     * @returns The matrix given in parameter with its values updated.\n     */\n    copyTo(matrix: Matrix): Matrix\n    {\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the matrix to be the same as the ones in given matrix\n     * @param {PIXI.Matrix} matrix - The matrix to copy from.\n     * @returns {PIXI.Matrix} this\n     */\n    copyFrom(matrix: Matrix): this\n    {\n        this.a = matrix.a;\n        this.b = matrix.b;\n        this.c = matrix.c;\n        this.d = matrix.d;\n        this.tx = matrix.tx;\n        this.ty = matrix.ty;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`;\n    }\n    // #endif\n\n    /**\n     * A default (identity) matrix\n     * @readonly\n     */\n    static get IDENTITY(): Matrix\n    {\n        return new Matrix();\n    }\n\n    /**\n     * A temp matrix\n     * @readonly\n     */\n    static get TEMP_MATRIX(): Matrix\n    {\n        return new Matrix();\n    }\n}\n", "// Your friendly neighbour https://en.wikipedia.org/wiki/Dihedral_group\n//\n// This file implements the dihedral group of order 16, also called\n// of degree 8. That's why its called groupD8.\n\nimport { Matrix } from './Matrix';\n\n/*\n * Transform matrix for operation n is:\n * | ux | vx |\n * | uy | vy |\n */\n\nconst ux = [1, 1, 0, -1, -1, -1, 0, 1, 1, 1, 0, -1, -1, -1, 0, 1];\nconst uy = [0, 1, 1, 1, 0, -1, -1, -1, 0, 1, 1, 1, 0, -1, -1, -1];\nconst vx = [0, -1, -1, -1, 0, 1, 1, 1, 0, 1, 1, 1, 0, -1, -1, -1];\nconst vy = [1, 1, 0, -1, -1, -1, 0, 1, -1, -1, 0, 1, 1, 1, 0, -1];\n\n/**\n * [Cayley Table]{@link https://en.wikipedia.org/wiki/Cayley_table}\n * for the composition of each rotation in the dihederal group D8.\n * @type {number[][]}\n * @private\n */\nconst rotationCayley: number[][] = [];\n\n/**\n * Matrices for each `GD8Symmetry` rotation.\n * @type {PIXI.Matrix[]}\n * @private\n */\nconst rotationMatrices: Matrix[] = [];\n\n/*\n * Alias for {@code Math.sign}.\n */\nconst signum = Math.sign;\n\n/*\n * Initializes `rotationCayley` and `rotationMatrices`. It is called\n * only once below.\n */\nfunction init(): void\n{\n    for (let i = 0; i < 16; i++)\n    {\n        const row: number[] = [];\n\n        rotationCayley.push(row);\n\n        for (let j = 0; j < 16; j++)\n        {\n            /* Multiplies rotation matrices i and j. */\n            const _ux = signum((ux[i] * ux[j]) + (vx[i] * uy[j]));\n            const _uy = signum((uy[i] * ux[j]) + (vy[i] * uy[j]));\n            const _vx = signum((ux[i] * vx[j]) + (vx[i] * vy[j]));\n            const _vy = signum((uy[i] * vx[j]) + (vy[i] * vy[j]));\n\n            /* Finds rotation matrix matching the product and pushes it. */\n            for (let k = 0; k < 16; k++)\n            {\n                if (ux[k] === _ux && uy[k] === _uy\n                      && vx[k] === _vx && vy[k] === _vy)\n                {\n                    row.push(k);\n                    break;\n                }\n            }\n        }\n    }\n\n    for (let i = 0; i < 16; i++)\n    {\n        const mat = new Matrix();\n\n        mat.set(ux[i], uy[i], vx[i], vy[i], 0, 0);\n        rotationMatrices.push(mat);\n    }\n}\n\ninit();\n\ntype GD8Symmetry = number;\n/**\n * @memberof PIXI\n * @typedef {number} GD8Symmetry\n * @see PIXI.groupD8\n */\n\n/**\n * Implements the dihedral group D8, which is similar to\n * [group D4]{@link http://mathworld.wolfram.com/DihedralGroupD4.html};\n * D8 is the same but with diagonals, and it is used for texture\n * rotations.\n *\n * The directions the U- and V- axes after rotation\n * of an angle of `a: GD8Constant` are the vectors `(uX(a), uY(a))`\n * and `(vX(a), vY(a))`. These aren't necessarily unit vectors.\n *\n * **Origin:**<br>\n *  This is the small part of gameofbombs.com portal system. It works.\n * @see PIXI.groupD8.E\n * @see PIXI.groupD8.SE\n * @see PIXI.groupD8.S\n * @see PIXI.groupD8.SW\n * @see PIXI.groupD8.W\n * @see PIXI.groupD8.NW\n * @see PIXI.groupD8.N\n * @see PIXI.groupD8.NE\n * <AUTHOR> @ivanpopelyshev\n * @namespace PIXI.groupD8\n * @memberof PIXI\n */\nexport const groupD8 = {\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 0°       | East      |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    E: 0,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 45°↻     | Southeast |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    SE: 1,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 90°↻     | South     |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    S: 2,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 135°↻    | Southwest |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    SW: 3,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 180°     | West      |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    W: 4,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -135°/225°↻ | Northwest    |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    NW: 5,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -90°/270°↻  | North        |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    N: 6,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -45°/315°↻  | Northeast    |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    NE: 7,\n\n    /**\n     * Reflection about Y-axis.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MIRROR_VERTICAL: 8,\n\n    /**\n     * Reflection about the main diagonal.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MAIN_DIAGONAL: 10,\n\n    /**\n     * Reflection about X-axis.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MIRROR_HORIZONTAL: 12,\n\n    /**\n     * Reflection about reverse diagonal.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    REVERSE_DIAGONAL: 14,\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The X-component of the U-axis\n     *    after rotating the axes.\n     */\n    uX: (ind: GD8Symmetry): GD8Symmetry => ux[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The Y-component of the U-axis\n     *    after rotating the axes.\n     */\n    uY: (ind: GD8Symmetry): GD8Symmetry => uy[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The X-component of the V-axis\n     *    after rotating the axes.\n     */\n    vX: (ind: GD8Symmetry): GD8Symmetry => vx[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The Y-component of the V-axis\n     *    after rotating the axes.\n     */\n    vY: (ind: GD8Symmetry): GD8Symmetry => vy[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotation - symmetry whose opposite\n     *   is needed. Only rotations have opposite symmetries while\n     *   reflections don't.\n     * @returns {PIXI.GD8Symmetry} The opposite symmetry of `rotation`\n     */\n    inv: (rotation: GD8Symmetry): GD8Symmetry =>\n    {\n        if (rotation & 8)// true only if between 8 & 15 (reflections)\n        {\n            return rotation & 15;// or rotation % 16\n        }\n\n        return (-rotation) & 7;// or (8 - rotation) % 8\n    },\n\n    /**\n     * Composes the two D8 operations.\n     *\n     * Taking `^` as reflection:\n     *\n     * |       | E=0 | S=2 | W=4 | N=6 | E^=8 | S^=10 | W^=12 | N^=14 |\n     * |-------|-----|-----|-----|-----|------|-------|-------|-------|\n     * | E=0   | E   | S   | W   | N   | E^   | S^    | W^    | N^    |\n     * | S=2   | S   | W   | N   | E   | S^   | W^    | N^    | E^    |\n     * | W=4   | W   | N   | E   | S   | W^   | N^    | E^    | S^    |\n     * | N=6   | N   | E   | S   | W   | N^   | E^    | S^    | W^    |\n     * | E^=8  | E^  | N^  | W^  | S^  | E    | N     | W     | S     |\n     * | S^=10 | S^  | E^  | N^  | W^  | S    | E     | N     | W     |\n     * | W^=12 | W^  | S^  | E^  | N^  | W    | S     | E     | N     |\n     * | N^=14 | N^  | W^  | S^  | E^  | N    | W     | S     | E     |\n     *\n     * [This is a Cayley table]{@link https://en.wikipedia.org/wiki/Cayley_table}\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotationSecond - Second operation, which\n     *   is the row in the above cayley table.\n     * @param {PIXI.GD8Symmetry} rotationFirst - First operation, which\n     *   is the column in the above cayley table.\n     * @returns {PIXI.GD8Symmetry} Composed operation\n     */\n    add: (rotationSecond: GD8Symmetry, rotationFirst: GD8Symmetry): GD8Symmetry => (\n        rotationCayley[rotationSecond][rotationFirst]\n    ),\n\n    /**\n     * Reverse of `add`.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotationSecond - Second operation\n     * @param {PIXI.GD8Symmetry} rotationFirst - First operation\n     * @returns {PIXI.GD8Symmetry} Result\n     */\n    sub: (rotationSecond: GD8Symmetry, rotationFirst: GD8Symmetry): GD8Symmetry => (\n        rotationCayley[rotationSecond][groupD8.inv(rotationFirst)]\n    ),\n\n    /**\n     * Adds 180 degrees to rotation, which is a commutative\n     * operation.\n     * @memberof PIXI.groupD8\n     * @param {number} rotation - The number to rotate.\n     * @returns {number} Rotated number\n     */\n    rotate180: (rotation: number): number => rotation ^ 4,\n\n    /**\n     * Checks if the rotation angle is vertical, i.e. south\n     * or north. It doesn't work for reflections.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotation - The number to check.\n     * @returns {boolean} Whether or not the direction is vertical\n     */\n    isVertical: (rotation: GD8Symmetry): boolean => (rotation & 3) === 2, // rotation % 4 === 2\n\n    /**\n     * Approximates the vector `V(dx,dy)` into one of the\n     * eight directions provided by `groupD8`.\n     * @memberof PIXI.groupD8\n     * @param {number} dx - X-component of the vector\n     * @param {number} dy - Y-component of the vector\n     * @returns {PIXI.GD8Symmetry} Approximation of the vector into\n     *  one of the eight symmetries.\n     */\n    byDirection: (dx: number, dy: number): GD8Symmetry =>\n    {\n        if (Math.abs(dx) * 2 <= Math.abs(dy))\n        {\n            if (dy >= 0)\n            {\n                return groupD8.S;\n            }\n\n            return groupD8.N;\n        }\n        else if (Math.abs(dy) * 2 <= Math.abs(dx))\n        {\n            if (dx > 0)\n            {\n                return groupD8.E;\n            }\n\n            return groupD8.W;\n        }\n        else if (dy > 0)\n        {\n            if (dx > 0)\n            {\n                return groupD8.SE;\n            }\n\n            return groupD8.SW;\n        }\n        else if (dx > 0)\n        {\n            return groupD8.NE;\n        }\n\n        return groupD8.NW;\n    },\n\n    /**\n     * Helps sprite to compensate texture packer rotation.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.Matrix} matrix - sprite world matrix\n     * @param {PIXI.GD8Symmetry} rotation - The rotation factor to use.\n     * @param {number} tx - sprite anchoring\n     * @param {number} ty - sprite anchoring\n     */\n    matrixAppendRotationInv: (matrix: Matrix, rotation: GD8Symmetry, tx = 0, ty = 0): void =>\n    {\n        // Packer used \"rotation\", we use \"inv(rotation)\"\n        const mat: Matrix = rotationMatrices[groupD8.inv(rotation)];\n\n        mat.tx = tx;\n        mat.ty = ty;\n        matrix.append(mat);\n    },\n};\n", "import { ObservablePoint } from './ObservablePoint';\nimport { Matrix } from './Matrix';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Transform extends GlobalMixins.Transform {}\n\n/**\n * Transform that takes care about its versions.\n * @memberof PIXI\n */\nexport class Transform\n{\n    /** A default (identity) transform. */\n    public static readonly IDENTITY = new Transform();\n\n    /** The world transformation matrix. */\n    public worldTransform: Matrix;\n\n    /** The local transformation matrix. */\n    public localTransform: Matrix;\n\n    /** The coordinate of the object relative to the local coordinates of the parent. */\n    public position: ObservablePoint;\n\n    /** The scale factor of the object. */\n    public scale: ObservablePoint;\n\n    /** The pivot point of the displayObject that it rotates around. */\n    public pivot: ObservablePoint;\n\n    /** The skew amount, on the x and y axis. */\n    public skew: ObservablePoint;\n\n    /** The locally unique ID of the parent's world transform used to calculate the current world transformation matrix. */\n    public _parentID: number;\n\n    /** The locally unique ID of the world transform. */\n    _worldID: number;\n\n    /** The rotation amount. */\n    protected _rotation: number;\n\n    /**\n     * The X-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _cx: number;\n\n    /**\n     * The Y-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _sx: number;\n\n    /**\n     * The X-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _cy: number;\n\n    /**\n     * The Y-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _sy: number;\n\n    /** The locally unique ID of the local transform. */\n    protected _localID: number;\n\n    /** The locally unique ID of the local transform used to calculate the current local transformation matrix. */\n    protected _currentLocalID: number;\n\n    constructor()\n    {\n        this.worldTransform = new Matrix();\n        this.localTransform = new Matrix();\n        this.position = new ObservablePoint(this.onChange, this, 0, 0);\n        this.scale = new ObservablePoint(this.onChange, this, 1, 1);\n        this.pivot = new ObservablePoint(this.onChange, this, 0, 0);\n        this.skew = new ObservablePoint(this.updateSkew, this, 0, 0);\n\n        this._rotation = 0;\n        this._cx = 1;\n        this._sx = 0;\n        this._cy = 0;\n        this._sy = 1;\n        this._localID = 0;\n        this._currentLocalID = 0;\n\n        this._worldID = 0;\n        this._parentID = 0;\n    }\n\n    /** Called when a value changes. */\n    protected onChange(): void\n    {\n        this._localID++;\n    }\n\n    /** Called when the skew or the rotation changes. */\n    protected updateSkew(): void\n    {\n        this._cx = Math.cos(this._rotation + this.skew.y);\n        this._sx = Math.sin(this._rotation + this.skew.y);\n        this._cy = -Math.sin(this._rotation - this.skew.x); // cos, added PI/2\n        this._sy = Math.cos(this._rotation - this.skew.x); // sin, added PI/2\n\n        this._localID++;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Transform `\n            + `position=(${this.position.x}, ${this.position.y}) `\n            + `rotation=${this.rotation} `\n            + `scale=(${this.scale.x}, ${this.scale.y}) `\n            + `skew=(${this.skew.x}, ${this.skew.y}) `\n            + `]`;\n    }\n    // #endif\n\n    /** Updates the local transformation matrix. */\n    updateLocalTransform(): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n    }\n\n    /**\n     * Updates the local and the world transformation matrices.\n     * @param parentTransform - The parent transform\n     */\n    updateTransform(parentTransform: Transform): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n\n        if (this._parentID !== parentTransform._worldID)\n        {\n            // concat the parent matrix with the objects transform.\n            const pt = parentTransform.worldTransform;\n            const wt = this.worldTransform;\n\n            wt.a = (lt.a * pt.a) + (lt.b * pt.c);\n            wt.b = (lt.a * pt.b) + (lt.b * pt.d);\n            wt.c = (lt.c * pt.a) + (lt.d * pt.c);\n            wt.d = (lt.c * pt.b) + (lt.d * pt.d);\n            wt.tx = (lt.tx * pt.a) + (lt.ty * pt.c) + pt.tx;\n            wt.ty = (lt.tx * pt.b) + (lt.ty * pt.d) + pt.ty;\n\n            this._parentID = parentTransform._worldID;\n\n            // update the id of the transform..\n            this._worldID++;\n        }\n    }\n\n    /**\n     * Decomposes a matrix and sets the transforms properties based on it.\n     * @param matrix - The matrix to decompose\n     */\n    setFromMatrix(matrix: Matrix): void\n    {\n        matrix.decompose(this);\n        this._localID++;\n    }\n\n    /** The rotation of the object in radians. */\n    get rotation(): number\n    {\n        return this._rotation;\n    }\n\n    set rotation(value: number)\n    {\n        if (this._rotation !== value)\n        {\n            this._rotation = value;\n            this.updateSkew();\n        }\n    }\n}\n", "/*\n * Math classes and utilities mixed into PIXI namespace.\n */\n\nimport { Circle } from './shapes/Circle';\nimport { Ellipse } from './shapes/Ellipse';\nimport { Polygon } from './shapes/Polygon';\nimport { Rectangle } from './shapes/Rectangle';\nimport { RoundedRectangle } from './shapes/RoundedRectangle';\n\nexport * from './IPointData';\nexport * from './IPoint';\nexport * from './Point';\nexport * from './ObservablePoint';\nexport * from './Matrix';\nexport * from './groupD8';\nexport * from './Transform';\n\nexport { Circle };\nexport { Ellipse };\nexport { Polygon };\nexport { Rectangle };\nexport { RoundedRectangle };\n\nexport * from './const';\n\n/*\n * @description Complex shape type\n */\nexport type IShape = Circle | Ellipse | Polygon | Rectangle | RoundedRectangle;\n\nexport interface ISize\n{\n    width: number;\n    height: number;\n}\n"], "names": ["SHAPES", "arguments"], "mappings": ";;;;;;;;;;;IAAA;;;;;IAKG;AACI,QAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE;IAEhC;;;;;IAKG;AACI,QAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG;IAExC;;;;;IAKG;AACI,QAAM,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI;IAExC;;;;;;;;;;IAUG;AACSA,4BAQX;IARD,CAAA,UAAY,MAAM,EAAA;IAGd,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;IACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;IACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;IACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;IACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;IACZ,CAAC,EARWA,cAAM,KAANA,cAAM,GAQjB,EAAA,CAAA,CAAA;;ICtCD;;;;;;IAMG;AACH,QAAA,KAAA,kBAAA,YAAA;IAOI;;;;IAIG;QACH,SAAY,KAAA,CAAA,CAAK,EAAE,CAAK,EAAA;IAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;;YATjB,IAAC,CAAA,CAAA,GAAG,CAAC,CAAC;;YAEN,IAAC,CAAA,CAAA,GAAG,CAAC,CAAC;IAST,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SACd;IAED;;;IAGG;IACH,IAAA,KAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;YAEI,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;SACpC,CAAA;IAED;;;;IAIG;QACH,KAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,CAAa,EAAA;YAElB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,KAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAyB,CAAI,EAAA;YAEzB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAEtB,QAAA,OAAO,CAAC,CAAC;SACZ,CAAA;IAED;;;;IAIG;QACH,KAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,CAAa,EAAA;IAEhB,QAAA,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;SAC/C,CAAA;IAED;;;;;;IAMG;IACH,IAAA,KAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,CAAK,EAAE,CAAK,EAAA;IAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAEZ,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAEX,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAGD,IAAA,KAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;YAEI,OAAO,sBAAA,GAAuB,IAAI,CAAC,CAAC,WAAM,IAAI,CAAC,CAAC,GAAA,GAAG,CAAC;SACvD,CAAA;QAEL,OAAC,KAAA,CAAA;IAAD,CAAC,EAAA;;IC1FD,IAAM,UAAU,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,CAAC,CAAC;IAKxE;;;;;;IAMG;IAEH;;;;IAIG;AACH,QAAA,SAAA,kBAAA,YAAA;IAoBI;;;;;IAKG;IACH,IAAA,SAAA,SAAA,CAAY,CAAsB,EAAE,CAAsB,EAAE,KAA0B,EAAE,MAA2B,EAAA;IAAvG,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAsB,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAsB,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAA0B,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAA2B,GAAA,CAAA,CAAA,EAAA;IAE/G,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7B,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;SAC3B;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAI,CAAA,SAAA,EAAA,MAAA,EAAA;;IAAR,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,CAAC,CAAC;aACjB;;;IAAA,KAAA,CAAA,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAK,CAAA,SAAA,EAAA,OAAA,EAAA;;IAAT,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;aAC9B;;;IAAA,KAAA,CAAA,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAG,CAAA,SAAA,EAAA,KAAA,EAAA;;IAAP,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,CAAC,CAAC;aACjB;;;IAAA,KAAA,CAAA,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAM,CAAA,SAAA,EAAA,QAAA,EAAA;;IAAV,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;aAC/B;;;IAAA,KAAA,CAAA,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAK,EAAA,OAAA,EAAA;;IAAhB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACpC;;;IAAA,KAAA,CAAA,CAAA;IAED;;;IAGG;IACH,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;IAEI,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SACjE,CAAA;IAED;;;;IAIG;QACH,SAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,SAAoB,EAAA;IAEzB,QAAA,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;IACrB,QAAA,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;IACrB,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;IAC7B,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;IAE/B,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,SAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,SAAoB,EAAA;IAEvB,QAAA,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACrB,QAAA,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACrB,QAAA,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IAC7B,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE/B,QAAA,OAAO,SAAS,CAAC;SACpB,CAAA;IAED;;;;;IAKG;IACH,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;YAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACvC;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAC1C;IACI,YAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAC3C;IACI,gBAAA,OAAO,IAAI,CAAC;IACf,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAED;;;;;;;;IAQG;IACH,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,KAAgB,EAAE,SAAkB,EAAA;YAE3C,IAAI,CAAC,SAAS,EACd;gBACI,IAAM,IAAE,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAC/C,IAAM,IAAE,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;gBAE/D,IAAI,IAAE,IAAI,IAAE,EACZ;IACI,gBAAA,OAAO,KAAK,CAAC;IAChB,aAAA;gBAED,IAAM,IAAE,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAC/C,IAAM,IAAE,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAEnE,OAAO,IAAE,GAAG,IAAE,CAAC;IAClB,SAAA;IAED,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;IACrB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;IACtB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;IAEvB,QAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EACxB;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IACpD,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACvD,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IACrD,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAExD,QAAA,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAChC;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;YAED,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/E,IAAI,CAAC,KAAK,CAAC,EACX;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACxB,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACxB,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACxB,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAExB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;mBACnC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;mBACtC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;mBACtC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,EAC7C;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YAElC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;mBACtD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAChE;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YAElC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;mBACtD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAChE;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;IAMG;IACH,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,QAAY,EAAE,QAAmB,EAAA;IAAjC,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAY,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAmB,GAAA,QAAA,CAAA,EAAA;IAEjC,QAAA,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;IACnB,QAAA,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;IAEnB,QAAA,IAAI,CAAC,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;IAC3B,QAAA,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,CAAC;IAE5B,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,SAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,SAAoB,EAAA;IAEpB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;IACxE,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAE1E,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IACZ,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IAClC,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IACZ,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IAEnC,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;IAKG;IACH,IAAA,SAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,UAAc,EAAE,GAAW,EAAA;IAA3B,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAc,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAW,GAAA,KAAA,CAAA,EAAA;YAE5B,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;YAC5E,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;IAE7E,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;IAC9D,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;YAE9D,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAE1B,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,SAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,SAAoB,EAAA;IAExB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;IACxE,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAE1E,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IACZ,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;IACrB,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IACZ,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;IAEtB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAGD,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;IAEI,QAAA,OAAO,6BAA2B,IAAI,CAAC,CAAC,GAAA,KAAA,GAAM,IAAI,CAAC,CAAC,GAAU,SAAA,GAAA,IAAI,CAAC,KAAK,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,MAAG,CAAC;SACrG,CAAA;QAEL,OAAC,SAAA,CAAA;IAAD,CAAC,EAAA;;ICvUD;;;IAGG;AACH,QAAA,MAAA,kBAAA,YAAA;IAkBI;;;;IAIG;IACH,IAAA,SAAA,MAAA,CAAY,CAAK,EAAE,CAAK,EAAE,MAAU,EAAA;IAAxB,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;IAEhC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IAErB,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;SAC3B;IAED;;;IAGG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;IAEI,QAAA,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAClD,CAAA;IAED;;;;;IAKG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;IAEzB,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACpB;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;YAED,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACrC,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACtB,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEtB,EAAE,IAAI,EAAE,CAAC;YACT,EAAE,IAAI,EAAE,CAAC;IAET,QAAA,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;SAC1B,CAAA;IAED;;;IAGG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;IAEI,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACtG,CAAA;IAGD,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;IAEI,QAAA,OAAO,uBAAwB,GAAA,IAAI,CAAC,CAAC,GAAM,KAAA,GAAA,IAAI,CAAC,CAAC,GAAW,UAAA,GAAA,IAAI,CAAC,MAAM,MAAG,CAAC;SAC9E,CAAA;QAEL,OAAC,MAAA,CAAA;IAAD,CAAC,EAAA;;ICnFD;;;IAGG;AACH,QAAA,OAAA,kBAAA,YAAA;IAqBI;;;;;IAKG;IACH,IAAA,SAAA,OAAA,CAAY,CAAK,EAAE,CAAK,EAAE,SAAa,EAAE,UAAc,EAAA;IAA3C,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAa,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAc,GAAA,CAAA,CAAA,EAAA;IAEnD,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;IACvB,QAAA,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;IAEzB,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;SAC3B;IAED;;;IAGG;IACH,IAAA,OAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;IAEI,QAAA,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/D,CAAA;IAED;;;;;IAKG;IACH,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;YAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACvC;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;;IAGD,QAAA,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,QAAA,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;YAEzC,KAAK,IAAI,KAAK,CAAC;YACf,KAAK,IAAI,KAAK,CAAC;IAEf,QAAA,QAAQ,KAAK,GAAG,KAAK,IAAI,CAAC,EAAE;SAC/B,CAAA;IAED;;;IAGG;IACH,IAAA,OAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;YAEI,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAC5F,CAAA;IAGD,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;IAEI,QAAA,OAAO,2BAAyB,IAAI,CAAC,CAAC,GAAA,KAAA,GAAM,IAAI,CAAC,CAAC,GAAU,SAAA,GAAA,IAAI,CAAC,KAAK,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,MAAG,CAAC;SACnG,CAAA;QAEL,OAAC,OAAA,CAAA;IAAD,CAAC,EAAA;;ICxFD;;;IAGG;AACH,QAAA,OAAA,kBAAA,YAAA;IAkBI;;;;;;IAMG;IACH,IAAA,SAAA,OAAA,GAAA;;AAAA;YAAY,IAAgB,MAAA,GAAA,EAAA,CAAA;iBAAhB,IAAgB,EAAA,GAAA,CAAA,EAAhB,EAAgB,GAAA,SAAA,CAAA,MAAA,EAAhB,EAAgB,EAAA,EAAA;gBAAhB,MAAgB,CAAA,EAAA,CAAA,GAAAC,WAAA,CAAA,EAAA,CAAA,CAAA;;YAExB,IAAI,IAAI,GAA4B,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;;IAGlF,QAAA,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC/B;gBACI,IAAM,CAAC,GAAa,EAAE,CAAC;IAEvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC7C;IACI,gBAAA,CAAC,CAAC,IAAI,CAAE,IAAI,CAAC,CAAC,CAAgB,CAAC,CAAC,EAAG,IAAI,CAAC,CAAC,CAAgB,CAAC,CAAC,CAAC,CAAC;IAChE,aAAA;gBAED,IAAI,GAAG,CAAC,CAAC;IACZ,SAAA;IAED,QAAA,IAAI,CAAC,MAAM,GAAG,IAAgB,CAAC;IAC/B,QAAA,IAAI,CAAC,IAAI,GAAGD,cAAM,CAAC,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;IAED;;;IAGG;IACH,IAAA,OAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;YAEI,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACnC,QAAA,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;IAEpC,QAAA,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IAEvC,QAAA,OAAO,OAAO,CAAC;SAClB,CAAA;IAED;;;;;IAKG;IACH,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;YAEzB,IAAI,MAAM,GAAG,KAAK,CAAC;;;YAInB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAEtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EACnD;gBACI,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9B,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBACpC,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9B,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACpC,YAAA,IAAM,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAE7F,YAAA,IAAI,SAAS,EACb;oBACI,MAAM,GAAG,CAAC,MAAM,CAAC;IACpB,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;IAGD,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;IAEI,QAAA,OAAO,qBAAqB;mBACtB,cAAe,GAAA,IAAI,CAAC,WAAa,CAAA;mBACjC,SAAU,GAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,UAAU,EAAE,YAAY,EAAA,EAAK,OAAG,UAAU,GAAA,IAAA,GAAK,YAAc,CAAhC,EAAgC,EAAE,EAAE,CAAC,GAAG,GAAA,CAAA,CAAC;SAC7G,CAAA;QAEL,OAAC,OAAA,CAAA;IAAD,CAAC,EAAA;;ICzGD;;;;IAIG;AACH,QAAA,gBAAA,kBAAA,YAAA;IAwBI;;;;;;IAMG;QACH,SAAY,gBAAA,CAAA,CAAK,EAAE,CAAK,EAAE,KAAS,EAAE,MAAU,EAAE,MAAW,EAAA;IAAhD,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAW,GAAA,EAAA,CAAA,EAAA;IAExD,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;SAC3B;IAED;;;IAGG;IACH,IAAA,gBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;YAEI,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SACrF,CAAA;IAED;;;;;IAKG;IACH,IAAA,gBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;YAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACvC;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IACD,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAC3C;IACI,YAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAC5C;IACI,gBAAA,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAEzF,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM;4BAC3D,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,EAC9D;IACI,oBAAA,OAAO,IAAI,CAAC;IACf,iBAAA;oBACD,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;oBAC/B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IAC/B,gBAAA,IAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;IAEhC,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;IACI,oBAAA,OAAO,IAAI,CAAC;IACf,iBAAA;IACD,gBAAA,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;IACxC,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;IACI,oBAAA,OAAO,IAAI,CAAC;IACf,iBAAA;IACD,gBAAA,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IACzC,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;IACI,oBAAA,OAAO,IAAI,CAAC;IACf,iBAAA;oBACD,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IAC3B,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;IACI,oBAAA,OAAO,IAAI,CAAC;IACf,iBAAA;IACJ,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAGD,IAAA,gBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;IAEI,QAAA,OAAO,oCAAkC,IAAI,CAAC,CAAC,GAAM,KAAA,GAAA,IAAI,CAAC,CAAG;IACvD,eAAA,QAAA,GAAS,IAAI,CAAC,KAAK,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,GAAA,GAAG,CAAA,CAAC;SAC5E,CAAA;QAEL,OAAC,gBAAA,CAAA;IAAD,CAAC,EAAA;;IC9GD;;;;IAIG;IAEH;;;;IAIG;IAEH;;;;IAIG;;ICbH;;;;;IAKG;IAEH;;;;;;;IAOG;IAEH;;;;;;IAMG;IAEH;;;;;;IAMG;IAEH;;;;;;IAMG;;IC1CH;;;;;;IAMG;AACH,QAAA,eAAA,kBAAA,YAAA;IAWI;;;;;;IAMG;IACH,IAAA,SAAA,eAAA,CAAY,EAAoB,EAAE,KAAQ,EAAE,CAAK,EAAE,CAAK,EAAA;IAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAEpD,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAEZ,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACtB;IAED;;;;;;;;IAQG;IACH,IAAA,eAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAM,EAAY,EAAE,KAAkB,EAAA;IAAhC,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAA,GAAK,IAAI,CAAC,EAAE,CAAA,EAAA;IAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAA,GAAQ,IAAI,CAAC,KAAK,CAAA,EAAA;IAElC,QAAA,OAAO,IAAI,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;SAC3D,CAAA;IAED;;;;;;IAMG;IACH,IAAA,eAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,CAAK,EAAE,CAAK,EAAA;IAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;YAEZ,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAClC;IACI,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;gBACZ,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,eAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,CAAa,EAAA;IAElB,QAAA,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EACtC;IACI,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACd,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACd,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,eAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAyB,CAAI,EAAA;YAEzB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAExB,QAAA,OAAO,CAAC,CAAC;SACZ,CAAA;IAED;;;;IAIG;QACH,eAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,CAAa,EAAA;IAEhB,QAAA,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;SACjD,CAAA;IAGD,IAAA,eAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;YAEI,OAAO,gCAAA,GAAiC,CAAC,GAAM,KAAA,GAAA,CAAC,eAAU,IAAI,CAAC,KAAK,GAAA,GAAG,CAAC;SAC3E,CAAA;IAID,IAAA,MAAA,CAAA,cAAA,CAAI,eAAC,CAAA,SAAA,EAAA,GAAA,EAAA;;IAAL,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,EAAE,CAAC;aAClB;IAED,QAAA,GAAA,EAAA,UAAM,KAAa,EAAA;IAEf,YAAA,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,EACrB;IACI,gBAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;oBAChB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,aAAA;aACJ;;;IATA,KAAA,CAAA,CAAA;IAYD,IAAA,MAAA,CAAA,cAAA,CAAI,eAAC,CAAA,SAAA,EAAA,GAAA,EAAA;;IAAL,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,EAAE,CAAC;aAClB;IAED,QAAA,GAAA,EAAA,UAAM,KAAa,EAAA;IAEf,YAAA,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,EACrB;IACI,gBAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;oBAChB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,aAAA;aACJ;;;IATA,KAAA,CAAA,CAAA;QAUL,OAAC,eAAA,CAAA;IAAD,CAAC,EAAA;;IC7ID;;;;;;;;;;IAUG;AACH,QAAA,MAAA,kBAAA,YAAA;IAsBI;;;;;;;IAOG;QACH,SAAY,MAAA,CAAA,CAAK,EAAE,CAAK,EAAE,CAAK,EAAE,CAAK,EAAE,EAAM,EAAE,EAAM,EAAA;IAA1C,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;YAV/C,IAAK,CAAA,KAAA,GAAwB,IAAI,CAAC;IAYrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACb,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;SAChB;IAED;;;;;;;;;;IAUG;QACH,MAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,KAAe,EAAA;IAErB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACnB,QAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;SACtB,CAAA;IAED;;;;;;;;;IASG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,EAAU,EAAE,EAAU,EAAA;IAElE,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACb,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IAEb,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;IAKG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,SAAkB,EAAE,GAAkB,EAAA;IAE1C,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EACf;gBACI,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;IACpC,SAAA;IAED,QAAA,IAAM,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;IAEhC,QAAA,IAAI,SAAS,EACb;IACI,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChB,SAAA;IAED,aAAA;IACI,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChB,SAAA;IAED,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAED;;;;;;IAMG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAoC,GAAe,EAAE,MAAU,EAAA;YAE3D,MAAM,IAAI,MAAM,IAAI,IAAI,KAAK,EAAE,CAAM,CAAC;IAEtC,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAChB,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAEhB,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;YACjD,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAEjD,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;IAED;;;;;;IAMG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAA2C,GAAe,EAAE,MAAU,EAAA;YAElE,MAAM,IAAI,MAAM,IAAI,IAAI,KAAK,EAAE,CAAM,CAAC;YAEtC,IAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAExD,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAChB,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAEhB,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACrG,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAEtG,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;IAED;;;;;IAKG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,CAAS,EAAE,CAAS,EAAA;IAE1B,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAEb,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;IAKG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAM,CAAS,EAAE,CAAS,EAAA;IAEtB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACZ,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACZ,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACZ,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACZ,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAEb,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,MAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,KAAa,EAAA;YAEhB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAE5B,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;IAEpB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACrC,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;IACxC,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;IAExC,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,MAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,MAAc,EAAA;IAEjB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAElB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC3C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC3C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC3C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAE3C,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAExD,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;;;;;;IAYG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,CAAS,EAAE,CAAS,EAAE,MAAc,EAAE,MAAc,EAAE,MAAc,EAC7E,MAAc,EAAE,QAAgB,EAAE,KAAa,EAAE,KAAa,EAAA;IAE9D,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;IAC7C,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;IAC7C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;IAC9C,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;YAE7C,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtD,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,MAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,MAAc,EAAA;IAElB,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;YAEpB,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,EACxE;IACI,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;gBAElB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAClD,SAAA;YAED,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;YAC9D,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;IAE9D,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACH,MAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,SAAoB,EAAA;;IAG1B,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACjB,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACjB,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACjB,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACjB,QAAA,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;IAE9B,QAAA,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE/B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;IAEtC,QAAA,IAAI,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,EACvD;IACI,YAAA,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC3B,YAAA,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3C,SAAA;IAED,aAAA;IACI,YAAA,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;IACvB,YAAA,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;IACzB,YAAA,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;IAC5B,SAAA;;YAGD,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;YAGjD,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjE,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEjE,QAAA,OAAO,SAAS,CAAC;SACpB,CAAA;IAED;;;IAGG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;IAEI,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;IACpB,QAAA,IAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAEhC,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAChB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IACjB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IACjB,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAE7C,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;IAGG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;IAEI,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAEZ,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;IAGG;IACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;IAEI,QAAA,IAAM,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;IAE5B,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACpB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IAEpB,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;IAED;;;;IAIG;QACH,MAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,MAAc,EAAA;IAEjB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAClB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACpB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IAEpB,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;IAED;;;;IAIG;QACH,MAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,MAAc,EAAA;IAEnB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;IACpB,QAAA,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;IAEpB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAGD,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;YAEI,OAAO,uBAAA,GAAwB,IAAI,CAAC,CAAC,GAAA,KAAA,GAAM,IAAI,CAAC,CAAC,GAAM,KAAA,GAAA,IAAI,CAAC,CAAC,WAAM,IAAI,CAAC,CAAC,GAAA,MAAA,GAAO,IAAI,CAAC,EAAE,GAAA,MAAA,GAAO,IAAI,CAAC,EAAE,GAAA,GAAG,CAAC;SAC5G,CAAA;IAOD,IAAA,MAAA,CAAA,cAAA,CAAW,MAAQ,EAAA,UAAA,EAAA;IAJnB;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,MAAM,EAAE,CAAC;aACvB;;;IAAA,KAAA,CAAA,CAAA;IAMD,IAAA,MAAA,CAAA,cAAA,CAAW,MAAW,EAAA,aAAA,EAAA;IAJtB;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,MAAM,EAAE,CAAC;aACvB;;;IAAA,KAAA,CAAA,CAAA;QACL,OAAC,MAAA,CAAA;IAAD,CAAC,EAAA;;IC1dD;IAOA;;;;IAIG;IAEH,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClE,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClE,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClE,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAElE;;;;;IAKG;IACH,IAAM,cAAc,GAAe,EAAE,CAAC;IAEtC;;;;IAIG;IACH,IAAM,gBAAgB,GAAa,EAAE,CAAC;IAEtC;;IAEG;IACH,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;IAEzB;;;IAGG;IACH,SAAS,IAAI,GAAA;QAET,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;YACI,IAAM,GAAG,GAAa,EAAE,CAAC;IAEzB,QAAA,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;;IAEI,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAGtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;IACI,gBAAA,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG;IACzB,uBAAA,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,EACvC;IACI,oBAAA,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACZ,MAAM;IACT,iBAAA;IACJ,aAAA;IACJ,SAAA;IACJ,KAAA;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;IACI,QAAA,IAAM,GAAG,GAAG,IAAI,MAAM,EAAE,CAAC;YAEzB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1C,QAAA,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,KAAA;IACL,CAAC;IAED,IAAI,EAAE,CAAC;IAGP;;;;IAIG;IAEH;;;;;;;;;;;;;;;;;;;;;;;IAuBG;AACI,QAAM,OAAO,GAAG;IACnB;;;;;;IAMG;IACH,IAAA,CAAC,EAAE,CAAC;IAEJ;;;;;;IAMG;IACH,IAAA,EAAE,EAAE,CAAC;IAEL;;;;;;IAMG;IACH,IAAA,CAAC,EAAE,CAAC;IAEJ;;;;;;IAMG;IACH,IAAA,EAAE,EAAE,CAAC;IAEL;;;;;;IAMG;IACH,IAAA,CAAC,EAAE,CAAC;IAEJ;;;;;;IAMG;IACH,IAAA,EAAE,EAAE,CAAC;IAEL;;;;;;IAMG;IACH,IAAA,CAAC,EAAE,CAAC;IAEJ;;;;;;IAMG;IACH,IAAA,EAAE,EAAE,CAAC;IAEL;;;;IAIG;IACH,IAAA,eAAe,EAAE,CAAC;IAElB;;;;IAIG;IACH,IAAA,aAAa,EAAE,EAAE;IAEjB;;;;IAIG;IACH,IAAA,iBAAiB,EAAE,EAAE;IAErB;;;;IAIG;IACH,IAAA,gBAAgB,EAAE,EAAE;IAEpB;;;;;IAKG;QACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;IAE9C;;;;;IAKG;QACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;IAE9C;;;;;IAKG;QACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;IAE9C;;;;;IAKG;QACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;IAE9C;;;;;;IAMG;QACH,GAAG,EAAE,UAAC,QAAqB,EAAA;IAEvB,QAAA,IAAI,QAAQ,GAAG,CAAC;IAChB,SAAA;IACI,YAAA,OAAO,QAAQ,GAAG,EAAE,CAAC;IACxB,SAAA;YAED,OAAO,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC;SAC1B;IAED;;;;;;;;;;;;;;;;;;;;;;;IAuBG;IACH,IAAA,GAAG,EAAE,UAAC,cAA2B,EAAE,aAA0B,IAAkB,QAC3E,cAAc,CAAC,cAAc,CAAC,CAAC,aAAa,CAAC,IAChD;IAED;;;;;;IAMG;QACH,GAAG,EAAE,UAAC,cAA2B,EAAE,aAA0B,EAAkB,EAAA,QAC3E,cAAc,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,EAC7D,EAAA;IAED;;;;;;IAMG;QACH,SAAS,EAAE,UAAC,QAAgB,EAAA,EAAa,OAAA,QAAQ,GAAG,CAAC,CAAA,EAAA;IAErD;;;;;;IAMG;IACH,IAAA,UAAU,EAAE,UAAC,QAAqB,EAAA,EAAc,OAAA,CAAC,QAAQ,GAAG,CAAC,MAAM,CAAC,GAAA;IAEpE;;;;;;;;IAQG;IACH,IAAA,WAAW,EAAE,UAAC,EAAU,EAAE,EAAU,EAAA;IAEhC,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EACpC;gBACI,IAAI,EAAE,IAAI,CAAC,EACX;oBACI,OAAO,OAAO,CAAC,CAAC,CAAC;IACpB,aAAA;gBAED,OAAO,OAAO,CAAC,CAAC,CAAC;IACpB,SAAA;IACI,aAAA,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EACzC;gBACI,IAAI,EAAE,GAAG,CAAC,EACV;oBACI,OAAO,OAAO,CAAC,CAAC,CAAC;IACpB,aAAA;gBAED,OAAO,OAAO,CAAC,CAAC,CAAC;IACpB,SAAA;iBACI,IAAI,EAAE,GAAG,CAAC,EACf;gBACI,IAAI,EAAE,GAAG,CAAC,EACV;oBACI,OAAO,OAAO,CAAC,EAAE,CAAC;IACrB,aAAA;gBAED,OAAO,OAAO,CAAC,EAAE,CAAC;IACrB,SAAA;iBACI,IAAI,EAAE,GAAG,CAAC,EACf;gBACI,OAAO,OAAO,CAAC,EAAE,CAAC;IACrB,SAAA;YAED,OAAO,OAAO,CAAC,EAAE,CAAC;SACrB;IAED;;;;;;;IAOG;QACH,uBAAuB,EAAE,UAAC,MAAc,EAAE,QAAqB,EAAE,EAAM,EAAE,EAAM,EAAA;IAAd,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;;YAG3E,IAAM,GAAG,GAAW,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE5D,QAAA,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;IACZ,QAAA,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;IACZ,QAAA,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACtB;;;ICxXL;;;IAGG;AACH,QAAA,SAAA,kBAAA,YAAA;IA8DI,IAAA,SAAA,SAAA,GAAA;IAEI,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,EAAE,CAAC;IACnC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,EAAE,CAAC;IACnC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/D,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5D,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7D,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IACnB,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACb,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACb,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACb,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACb,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;IAEzB,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IAClB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;SACtB;;IAGS,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAlB,YAAA;YAEI,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB,CAAA;;IAGS,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAApB,YAAA;IAEI,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAElD,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB,CAAA;IAGD,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;IAEI,QAAA,OAAO,wBAAwB;IACzB,eAAA,YAAA,GAAa,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAA,IAAA,GAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAA,IAAI,CAAA;IACpD,eAAA,WAAA,GAAY,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAA;IAC5B,eAAA,SAAA,GAAU,IAAI,CAAC,KAAK,CAAC,CAAC,GAAA,IAAA,GAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAA,IAAI,CAAA;IAC3C,eAAA,QAAA,GAAS,IAAI,CAAC,IAAI,CAAC,CAAC,GAAA,IAAA,GAAK,IAAI,CAAC,IAAI,CAAC,CAAC,GAAA,IAAI,CAAA;IACxC,cAAA,GAAG,CAAC;SACb,CAAA;;IAID,IAAA,SAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;IAEI,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;IAE/B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,EAC1C;;IAEI,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAE/B,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;;IAGrC,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IACvB,SAAA;SACJ,CAAA;IAED;;;IAGG;QACH,SAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,eAA0B,EAAA;IAEtC,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;IAE/B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,EAC1C;;IAEI,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAE/B,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;;IAGrC,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IACvB,SAAA;IAED,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,eAAe,CAAC,QAAQ,EAC/C;;IAEI,YAAA,IAAM,EAAE,GAAG,eAAe,CAAC,cAAc,CAAC;IAC1C,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;gBAE/B,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;gBAChD,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;IAEhD,YAAA,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,QAAQ,CAAC;;gBAG1C,IAAI,CAAC,QAAQ,EAAE,CAAC;IACnB,SAAA;SACJ,CAAA;IAED;;;IAGG;QACH,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,MAAc,EAAA;IAExB,QAAA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;;IAAZ,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;IAED,QAAA,GAAA,EAAA,UAAa,KAAa,EAAA;IAEtB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAC5B;IACI,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,IAAI,CAAC,UAAU,EAAE,CAAC;IACrB,aAAA;aACJ;;;IATA,KAAA,CAAA,CAAA;;IA7LsB,IAAA,SAAA,CAAA,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC;QAuMtD,OAAC,SAAA,CAAA;IAAA,CA1MD,EA0MC;;ICpND;;IAEG;;;;;;;;;;;;;;;;;;;;;;;;;;;"}