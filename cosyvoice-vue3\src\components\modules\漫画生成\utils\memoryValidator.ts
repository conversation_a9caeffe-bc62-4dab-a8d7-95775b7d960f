/**
 * 🔍 内存优化验证器
 * 用于验证内存优化措施是否生效
 */

interface MemoryStats {
  current: number;
  peak: number;
  baseline: number;
  improvement: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
}

export class MemoryValidator {
  private static instance: MemoryValidator;
  private baseline: number = 0;
  private peak: number = 0;
  private measurements: number[] = [];
  private startTime: number = 0;

  private constructor() {}

  public static getInstance(): MemoryValidator {
    if (!MemoryValidator.instance) {
      MemoryValidator.instance = new MemoryValidator();
    }
    return MemoryValidator.instance;
  }

  /**
   * 🚀 开始内存监控
   */
  public startMonitoring(): void {
    this.startTime = Date.now();
    this.baseline = this.getCurrentMemory();
    this.peak = this.baseline;
    this.measurements = [this.baseline];
    
    console.log('🔍 内存监控开始:', {
      baseline: `${(this.baseline / 1024 / 1024).toFixed(1)}MB`,
      timestamp: new Date().toLocaleTimeString()
    });
  }

  /**
   * 📊 获取当前内存使用量
   */
  private getCurrentMemory(): number {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * 📈 记录内存快照
   */
  public recordSnapshot(label?: string): void {
    const current = this.getCurrentMemory();
    this.measurements.push(current);
    
    if (current > this.peak) {
      this.peak = current;
    }

    const improvement = this.baseline > 0 ? ((this.baseline - current) / this.baseline * 100) : 0;
    
    console.log(`📊 内存快照${label ? ` (${label})` : ''}:`, {
      current: `${(current / 1024 / 1024).toFixed(1)}MB`,
      peak: `${(this.peak / 1024 / 1024).toFixed(1)}MB`,
      baseline: `${(this.baseline / 1024 / 1024).toFixed(1)}MB`,
      improvement: `${improvement > 0 ? '-' : '+'}${Math.abs(improvement).toFixed(1)}%`,
      timestamp: new Date().toLocaleTimeString()
    });
  }

  /**
   * 🎯 验证SafeImage缓存优化
   */
  public async validateSafeImageOptimization(): Promise<boolean> {
    console.log('🔍 验证SafeImage缓存优化...');
    
    const beforeSnapshot = this.getCurrentMemory();
    
    // 模拟大量图片加载
    const testImages = Array.from({ length: 10 }, (_, i) => 
      `http://localhost:5173/api/test-image-${i}.jpg`
    );
    
    // 触发SafeImage缓存清理事件
    window.dispatchEvent(new CustomEvent('clear-image-cache'));
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const afterSnapshot = this.getCurrentMemory();
    const memoryReduction = beforeSnapshot - afterSnapshot;
    const isOptimized = memoryReduction > 0;
    
    console.log('✅ SafeImage缓存优化验证:', {
      before: `${(beforeSnapshot / 1024 / 1024).toFixed(1)}MB`,
      after: `${(afterSnapshot / 1024 / 1024).toFixed(1)}MB`,
      reduction: `${(memoryReduction / 1024 / 1024).toFixed(1)}MB`,
      isOptimized
    });
    
    return isOptimized;
  }

  /**
   * 🖼️ 验证ComicGallery缓存优化
   */
  public validateComicGalleryOptimization(): boolean {
    console.log('🔍 验证ComicGallery缓存优化...');
    
    // 触发ComicGallery缓存清理事件
    window.dispatchEvent(new CustomEvent('clear-comic-gallery-cache'));
    
    const beforeMemory = this.getCurrentMemory();
    
    // 模拟filteredComics重复计算
    const testComics = Array.from({ length: 50 }, (_, i) => ({
      id: `test-${i}`,
      title: `Test Comic ${i}`,
      createdAt: new Date().toISOString()
    }));
    
    // 检查是否有缓存机制
    const hasCacheSupport = typeof (window as any).comicGalleryCache !== 'undefined';
    
    console.log('✅ ComicGallery缓存优化验证:', {
      hasCacheSupport,
      currentMemory: `${(beforeMemory / 1024 / 1024).toFixed(1)}MB`
    });
    
    return hasCacheSupport;
  }

  /**
   * 📱 验证WebSocket连接管理
   */
  public validateWebSocketOptimization(): boolean {
    console.log('🔍 验证WebSocket连接管理...');
    
    const masterWSManager = (window as any).masterWebSocketManager;
    const hasUnifiedManager = typeof masterWSManager !== 'undefined';
    
    let activeConnections = 0;
    if (hasUnifiedManager && typeof masterWSManager.getActiveConnections === 'function') {
      activeConnections = masterWSManager.getActiveConnections();
    }
    
    console.log('✅ WebSocket连接管理验证:', {
      hasUnifiedManager,
      activeConnections,
      isOptimized: hasUnifiedManager && activeConnections <= 3
    });
    
    return hasUnifiedManager && activeConnections <= 3;
  }

  /**
   * 🎯 获取完整的内存统计报告
   */
  public getMemoryReport(): MemoryStats {
    const current = this.getCurrentMemory();
    const improvement = this.baseline > 0 ? ((this.baseline - current) / this.baseline * 100) : 0;
    
    let status: MemoryStats['status'] = 'excellent';
    const currentMB = current / 1024 / 1024;
    
    if (currentMB > 600) {
      status = 'critical';
    } else if (currentMB > 400) {
      status = 'warning';
    } else if (currentMB > 200) {
      status = 'good';
    }
    
    return {
      current,
      peak: this.peak,
      baseline: this.baseline,
      improvement,
      status
    };
  }

  /**
   * 📋 生成优化报告
   */
  public async generateOptimizationReport(): Promise<void> {
    console.log('📋 生成内存优化报告...');
    
    const memoryStats = this.getMemoryReport();
    const safeImageOptimized = await this.validateSafeImageOptimization();
    const comicGalleryOptimized = this.validateComicGalleryOptimization();
    const webSocketOptimized = this.validateWebSocketOptimization();
    
    const report = {
      timestamp: new Date().toLocaleString(),
      duration: `${((Date.now() - this.startTime) / 1000).toFixed(1)}秒`,
      memoryStats: {
        current: `${(memoryStats.current / 1024 / 1024).toFixed(1)}MB`,
        peak: `${(memoryStats.peak / 1024 / 1024).toFixed(1)}MB`,
        baseline: `${(memoryStats.baseline / 1024 / 1024).toFixed(1)}MB`,
        improvement: `${memoryStats.improvement > 0 ? '-' : '+'}${Math.abs(memoryStats.improvement).toFixed(1)}%`,
        status: memoryStats.status
      },
      optimizations: {
        safeImageCache: safeImageOptimized ? '✅ 优化生效' : '❌ 需要检查',
        comicGalleryCache: comicGalleryOptimized ? '✅ 优化生效' : '❌ 需要检查',
        webSocketManager: webSocketOptimized ? '✅ 优化生效' : '❌ 需要检查'
      },
      recommendations: this.generateRecommendations(memoryStats)
    };
    
    console.log('📊 内存优化报告:', report);
    
    // 将报告保存到全局变量，方便调试
    (window as any).lastMemoryReport = report;
    
    return report;
  }

  /**
   * 💡 生成优化建议
   */
  private generateRecommendations(stats: MemoryStats): string[] {
    const recommendations: string[] = [];
    
    if (stats.status === 'critical') {
      recommendations.push('立即执行紧急内存清理');
      recommendations.push('检查是否有内存泄漏');
      recommendations.push('考虑减少并发图片加载数量');
    } else if (stats.status === 'warning') {
      recommendations.push('建议定期清理缓存');
      recommendations.push('优化图片压缩质量');
    } else if (stats.status === 'good') {
      recommendations.push('当前内存使用合理');
      recommendations.push('继续监控内存变化');
    } else {
      recommendations.push('内存优化效果良好');
      recommendations.push('可以考虑增加功能');
    }
    
    return recommendations;
  }
}

// 导出单例
export const memoryValidator = MemoryValidator.getInstance();

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).memoryValidator = memoryValidator;
  (window as any).startMemoryValidation = () => {
    memoryValidator.startMonitoring();
    console.log('🔍 内存验证工具已启动，使用以下命令:');
    console.log('  • memoryValidator.recordSnapshot("描述") - 记录快照');
    console.log('  • memoryValidator.generateOptimizationReport() - 生成报告');
    console.log('  • memoryValidator.validateSafeImageOptimization() - 验证SafeImage');
    console.log('  • memoryValidator.validateComicGalleryOptimization() - 验证ComicGallery');
    console.log('  • memoryValidator.validateWebSocketOptimization() - 验证WebSocket');
  };
}