/*!
 * @pixi/graphics - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/graphics is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{Texture as t,BaseTexture as e,BatchDrawCall as i,BatchTextureArray as r,BatchGeometry as n,UniformGroup as s,Shader as h,State as a}from"@pixi/core";import{SHAPES as o,Point as l,PI_2 as u,Polygon as p,Rectangle as c,RoundedRectangle as f,Circle as d,Ellipse as y,Matrix as v}from"@pixi/math";import{earcut as g,premultiplyTint as b,hex2rgb as m}from"@pixi/utils";import{WRAP_MODES as x,DRAW_MODES as _,BLEND_MODES as w}from"@pixi/constants";import{Bounds as M,Container as S}from"@pixi/display";var P,T;!function(t){t.MITER="miter",t.BEVEL="bevel",t.ROUND="round"}(P||(P={})),function(t){t.BUTT="butt",t.ROUND="round",t.SQUARE="square"}(T||(T={}));var D={adaptive:!0,maxLength:10,minSegments:8,maxSegments:2048,epsilon:1e-4,_segmentsCount:function(t,e){if(void 0===e&&(e=20),!this.adaptive||!t||isNaN(t))return e;var i=Math.ceil(t/this.maxLength);return i<this.minSegments?i=this.minSegments:i>this.maxSegments&&(i=this.maxSegments),i}},A=function(){function e(){this.color=16777215,this.alpha=1,this.texture=t.WHITE,this.matrix=null,this.visible=!1,this.reset()}return e.prototype.clone=function(){var t=new e;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t},e.prototype.reset=function(){this.color=16777215,this.alpha=1,this.texture=t.WHITE,this.matrix=null,this.visible=!1},e.prototype.destroy=function(){this.texture=null,this.matrix=null},e}(),E=function(t,e){return E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},E(t,e)};function C(t,e){function i(){this.constructor=t}E(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}function R(t,e){var i,r;void 0===e&&(e=!1);var n=t.length;if(!(n<6)){for(var s=0,h=0,a=t[n-2],o=t[n-1];h<n;h+=2){var l=t[h],u=t[h+1];s+=(l-a)*(u+o),a=l,o=u}if(!e&&s>0||e&&s<=0){var p=n/2;for(h=p+p%2;h<n;h+=2){var c=n-h-2,f=n-h-1,d=h,y=h+1;i=[t[d],t[c]],t[c]=i[0],t[d]=i[1],r=[t[y],t[f]],t[f]=r[0],t[y]=r[1]}}}}var I={build:function(t){t.points=t.shape.points.slice()},triangulate:function(t,e){var i=t.points,r=t.holes,n=e.points,s=e.indices;if(i.length>=6){R(i,!1);for(var h=[],a=0;a<r.length;a++){var o=r[a];R(o.points,!0),h.push(i.length/2),i=i.concat(o.points)}var l=g(i,h,2);if(!l)return;var u=n.length/2;for(a=0;a<l.length;a+=3)s.push(l[a]+u),s.push(l[a+1]+u),s.push(l[a+2]+u);for(a=0;a<i.length;a++)n.push(i[a])}}},B={build:function(t){var e,i,r,n,s,h,a=t.points;if(t.type===o.CIRC){var l=t.shape;e=l.x,i=l.y,s=h=l.radius,r=n=0}else if(t.type===o.ELIP){var u=t.shape;e=u.x,i=u.y,s=u.width,h=u.height,r=n=0}else{var p=t.shape,c=p.width/2,f=p.height/2;e=p.x+c,i=p.y+f,r=c-(s=h=Math.max(0,Math.min(p.radius,Math.min(c,f)))),n=f-h}if(s>=0&&h>=0&&r>=0&&n>=0){var d=Math.ceil(2.3*Math.sqrt(s+h)),y=8*d+(r?4:0)+(n?4:0);if(a.length=y,0!==y){if(0===d)return a.length=8,a[0]=a[6]=e+r,a[1]=a[3]=i+n,a[2]=a[4]=e-r,void(a[5]=a[7]=i-n);var v=0,g=4*d+(r?2:0)+2,b=g,m=y,x=e+(T=r+s),_=e-T,w=i+(D=n);if(a[v++]=x,a[v++]=w,a[--g]=w,a[--g]=_,n){var M=i-D;a[b++]=_,a[b++]=M,a[--m]=M,a[--m]=x}for(var S=1;S<d;S++){var P=Math.PI/2*(S/d);x=e+(T=r+Math.cos(P)*s),_=e-T,w=i+(D=n+Math.sin(P)*h),M=i-D;a[v++]=x,a[v++]=w,a[--g]=w,a[--g]=_,a[b++]=_,a[b++]=M,a[--m]=M,a[--m]=x}var T,D;x=e+(T=r),_=e-T,w=i+(D=n+h),M=i-D;a[v++]=x,a[v++]=w,a[--m]=M,a[--m]=x,r&&(a[v++]=_,a[v++]=w,a[--m]=M,a[--m]=_)}}else a.length=0},triangulate:function(t,e){var i=t.points,r=e.points,n=e.indices;if(0!==i.length){var s,h,a=r.length/2,l=a;if(t.type!==o.RREC){var u=t.shape;s=u.x,h=u.y}else{var p=t.shape;s=p.x+p.width/2,h=p.y+p.height/2}var c=t.matrix;r.push(t.matrix?c.a*s+c.c*h+c.tx:s,t.matrix?c.b*s+c.d*h+c.ty:h),a++,r.push(i[0],i[1]);for(var f=2;f<i.length;f+=2)r.push(i[f],i[f+1]),n.push(a++,l,a);n.push(l+1,l,a)}}},L={build:function(t){var e=t.shape,i=e.x,r=e.y,n=e.width,s=e.height,h=t.points;h.length=0,h.push(i,r,i+n,r,i+n,r+s,i,r+s)},triangulate:function(t,e){var i=t.points,r=e.points,n=r.length/2;r.push(i[0],i[1],i[2],i[3],i[6],i[7],i[4],i[5]),e.indices.push(n,n+1,n+2,n+1,n+2,n+3)}};function U(t,e,i){return t+(e-t)*i}function O(t,e,i,r,n,s,h){void 0===h&&(h=[]);for(var a=h,o=0,l=0,u=0,p=0,c=0,f=0,d=0,y=0;d<=20;++d)o=U(t,i,y=d/20),l=U(e,r,y),u=U(i,n,y),p=U(r,s,y),c=U(o,u,y),f=U(l,p,y),0===d&&a[a.length-2]===c&&a[a.length-1]===f||a.push(c,f);return a}var j={build:function(t){if(et.nextRoundedRectBehavior)B.build(t);else{var e=t.shape,i=t.points,r=e.x,n=e.y,s=e.width,h=e.height,a=Math.max(0,Math.min(e.radius,Math.min(s,h)/2));i.length=0,a?(O(r,n+a,r,n,r+a,n,i),O(r+s-a,n,r+s,n,r+s,n+a,i),O(r+s,n+h-a,r+s,n+h,r+s-a,n+h,i),O(r+a,n+h,r,n+h,r,n+h-a,i)):i.push(r,n,r+s,n,r+s,n+h,r,n+h)}},triangulate:function(t,e){if(et.nextRoundedRectBehavior)B.triangulate(t,e);else{for(var i=t.points,r=e.points,n=e.indices,s=r.length/2,h=g(i,null,2),a=0,o=h.length;a<o;a+=3)n.push(h[a]+s),n.push(h[a+1]+s),n.push(h[a+2]+s);for(a=0,o=i.length;a<o;a++)r.push(i[a],i[++a])}}};function N(t,e,i,r,n,s,h,a){var o,l;h?(o=r,l=-i):(o=-r,l=i);var u=t-i*n+o,p=e-r*n+l,c=t+i*s+o,f=e+r*s+l;return a.push(u,p),a.push(c,f),2}function F(t,e,i,r,n,s,h,a){var o=i-t,l=r-e,u=Math.atan2(o,l),p=Math.atan2(n-t,s-e);a&&u<p?u+=2*Math.PI:!a&&u>p&&(p+=2*Math.PI);var c=u,f=p-u,d=Math.abs(f),y=Math.sqrt(o*o+l*l),v=1+(15*d*Math.sqrt(y)/Math.PI>>0),g=f/v;if(c+=g,a){h.push(t,e),h.push(i,r);for(var b=1,m=c;b<v;b++,m+=g)h.push(t,e),h.push(t+Math.sin(m)*y,e+Math.cos(m)*y);h.push(t,e),h.push(n,s)}else{h.push(i,r),h.push(t,e);for(b=1,m=c;b<v;b++,m+=g)h.push(t+Math.sin(m)*y,e+Math.cos(m)*y),h.push(t,e);h.push(n,s),h.push(t,e)}return 2*v}function z(t,e){t.lineStyle.native?function(t,e){var i=0,r=t.shape,n=t.points||r.points,s=r.type!==o.POLY||r.closeStroke;if(0!==n.length){var h=e.points,a=e.indices,l=n.length/2,u=h.length/2,p=u;for(h.push(n[0],n[1]),i=1;i<l;i++)h.push(n[2*i],n[2*i+1]),a.push(p,p+1),p++;s&&a.push(p,u)}}(t,e):function(t,e){var i=t.shape,r=t.points||i.points.slice(),n=e.closePointEps;if(0!==r.length){var s=t.lineStyle,h=new l(r[0],r[1]),a=new l(r[r.length-2],r[r.length-1]),u=i.type!==o.POLY||i.closeStroke,p=Math.abs(h.x-a.x)<n&&Math.abs(h.y-a.y)<n;if(u){r=r.slice(),p&&(r.pop(),r.pop(),a.set(r[r.length-2],r[r.length-1]));var c=.5*(h.x+a.x),f=.5*(a.y+h.y);r.unshift(c,f),r.push(c,f)}var d=e.points,y=r.length/2,v=r.length,g=d.length/2,b=s.width/2,m=b*b,x=s.miterLimit*s.miterLimit,_=r[0],w=r[1],M=r[2],S=r[3],A=0,E=0,C=-(w-S),R=_-M,I=0,B=0,L=Math.sqrt(C*C+R*R);C/=L,R/=L,C*=b,R*=b;var U=s.alignment,O=2*(1-U),j=2*U;u||(s.cap===T.ROUND?v+=F(_-C*(O-j)*.5,w-R*(O-j)*.5,_-C*O,w-R*O,_+C*j,w+R*j,d,!0)+2:s.cap===T.SQUARE&&(v+=N(_,w,C,R,O,j,!0,d))),d.push(_-C*O,w-R*O),d.push(_+C*j,w+R*j);for(var z=1;z<y-1;++z){_=r[2*(z-1)],w=r[2*(z-1)+1],M=r[2*z],S=r[2*z+1],A=r[2*(z+1)],E=r[2*(z+1)+1],C=-(w-S),R=_-M,C/=L=Math.sqrt(C*C+R*R),R/=L,C*=b,R*=b,I=-(S-E),B=M-A,I/=L=Math.sqrt(I*I+B*B),B/=L,I*=b,B*=b;var q=M-_,k=w-S,H=M-A,G=E-S,W=q*H+k*G,Y=k*H-G*q,V=Y<0;if(Math.abs(Y)<.001*Math.abs(W))d.push(M-C*O,S-R*O),d.push(M+C*j,S+R*j),W>=0&&(s.join===P.ROUND?v+=F(M,S,M-C*O,S-R*O,M-I*O,S-B*O,d,!1)+4:v+=2,d.push(M-I*j,S-B*j),d.push(M+I*O,S+B*O));else{var Q=(-C+_)*(-R+S)-(-C+M)*(-R+w),X=(-I+A)*(-B+S)-(-I+M)*(-B+E),Z=(q*X-H*Q)/Y,J=(G*Q-k*X)/Y,K=(Z-M)*(Z-M)+(J-S)*(J-S),$=M+(Z-M)*O,tt=S+(J-S)*O,et=M-(Z-M)*j,it=S-(J-S)*j,rt=V?O:j;K<=Math.min(q*q+k*k,H*H+G*G)+rt*rt*m?s.join===P.BEVEL||K/m>x?(V?(d.push($,tt),d.push(M+C*j,S+R*j),d.push($,tt),d.push(M+I*j,S+B*j)):(d.push(M-C*O,S-R*O),d.push(et,it),d.push(M-I*O,S-B*O),d.push(et,it)),v+=2):s.join===P.ROUND?V?(d.push($,tt),d.push(M+C*j,S+R*j),v+=F(M,S,M+C*j,S+R*j,M+I*j,S+B*j,d,!0)+4,d.push($,tt),d.push(M+I*j,S+B*j)):(d.push(M-C*O,S-R*O),d.push(et,it),v+=F(M,S,M-C*O,S-R*O,M-I*O,S-B*O,d,!1)+4,d.push(M-I*O,S-B*O),d.push(et,it)):(d.push($,tt),d.push(et,it)):(d.push(M-C*O,S-R*O),d.push(M+C*j,S+R*j),s.join===P.ROUND?v+=V?F(M,S,M+C*j,S+R*j,M+I*j,S+B*j,d,!0)+2:F(M,S,M-C*O,S-R*O,M-I*O,S-B*O,d,!1)+2:s.join===P.MITER&&K/m<=x&&(V?(d.push(et,it),d.push(et,it)):(d.push($,tt),d.push($,tt)),v+=2),d.push(M-I*O,S-B*O),d.push(M+I*j,S+B*j),v+=2)}}_=r[2*(y-2)],w=r[2*(y-2)+1],M=r[2*(y-1)],C=-(w-(S=r[2*(y-1)+1])),R=_-M,C/=L=Math.sqrt(C*C+R*R),R/=L,C*=b,R*=b,d.push(M-C*O,S-R*O),d.push(M+C*j,S+R*j),u||(s.cap===T.ROUND?v+=F(M-C*(O-j)*.5,S-R*(O-j)*.5,M-C*O,S-R*O,M+C*j,S+R*j,d,!1)+2:s.cap===T.SQUARE&&(v+=N(M,S,C,R,O,j,!1,d)));var nt=e.indices,st=D.epsilon*D.epsilon;for(z=g;z<v+g-2;++z)_=d[2*z],w=d[2*z+1],M=d[2*(z+1)],S=d[2*(z+1)+1],A=d[2*(z+2)],E=d[2*(z+2)+1],Math.abs(_*(S-E)+M*(E-w)+A*(w-S))<st||nt.push(z,z+1,z+2)}}(t,e)}var q,k=function(){function t(){}return t.curveTo=function(t,e,i,r,n,s){var h=s[s.length-2],a=s[s.length-1]-e,o=h-t,l=r-e,u=i-t,p=Math.abs(a*u-o*l);if(p<1e-8||0===n)return s[s.length-2]===t&&s[s.length-1]===e||s.push(t,e),null;var c=a*a+o*o,f=l*l+u*u,d=a*l+o*u,y=n*Math.sqrt(c)/p,v=n*Math.sqrt(f)/p,g=y*d/c,b=v*d/f,m=y*u+v*o,x=y*l+v*a,_=o*(v+g),w=a*(v+g),M=u*(y+b),S=l*(y+b);return{cx:m+t,cy:x+e,radius:n,startAngle:Math.atan2(w-x,_-m),endAngle:Math.atan2(S-x,M-m),anticlockwise:o*l>u*a}},t.arc=function(t,e,i,r,n,s,h,a,o){for(var l=h-s,p=D._segmentsCount(Math.abs(l)*n,40*Math.ceil(Math.abs(l)/u)),c=l/(2*p),f=2*c,d=Math.cos(c),y=Math.sin(c),v=p-1,g=v%1/v,b=0;b<=v;++b){var m=c+s+f*(b+g*b),x=Math.cos(m),_=-Math.sin(m);o.push((d*x+y*_)*n+i,(d*-_+y*x)*n+r)}},t}(),H=function(){function t(){}return t.curveLength=function(t,e,i,r,n,s,h,a){for(var o=0,l=0,u=0,p=0,c=0,f=0,d=0,y=0,v=0,g=0,b=0,m=t,x=e,_=1;_<=10;++_)g=m-(y=(d=(f=(c=1-(l=_/10))*c)*c)*t+3*f*l*i+3*c*(u=l*l)*n+(p=u*l)*h),b=x-(v=d*e+3*f*l*r+3*c*u*s+p*a),m=y,x=v,o+=Math.sqrt(g*g+b*b);return o},t.curveTo=function(e,i,r,n,s,h,a){var o=a[a.length-2],l=a[a.length-1];a.length-=2;var u=D._segmentsCount(t.curveLength(o,l,e,i,r,n,s,h)),p=0,c=0,f=0,d=0,y=0;a.push(o,l);for(var v=1,g=0;v<=u;++v)f=(c=(p=1-(g=v/u))*p)*p,y=(d=g*g)*g,a.push(f*o+3*c*g*e+3*p*d*r+y*s,f*l+3*c*g*i+3*p*d*n+y*h)},t}(),G=function(){function t(){}return t.curveLength=function(t,e,i,r,n,s){var h=t-2*i+n,a=e-2*r+s,o=2*i-2*t,l=2*r-2*e,u=4*(h*h+a*a),p=4*(h*o+a*l),c=o*o+l*l,f=2*Math.sqrt(u+p+c),d=Math.sqrt(u),y=2*u*d,v=2*Math.sqrt(c),g=p/d;return(y*f+d*p*(f-v)+(4*c*u-p*p)*Math.log((2*d+g+f)/(g+v)))/(4*y)},t.curveTo=function(e,i,r,n,s){for(var h=s[s.length-2],a=s[s.length-1],o=D._segmentsCount(t.curveLength(h,a,e,i,r,n)),l=0,u=0,p=1;p<=o;++p){var c=p/o;l=h+(e-h)*c,u=a+(i-a)*c,s.push(l+(e+(r-e)*c-l)*c,u+(i+(n-i)*c-u)*c)}},t}(),W=function(){function t(){this.reset()}return t.prototype.begin=function(t,e,i){this.reset(),this.style=t,this.start=e,this.attribStart=i},t.prototype.end=function(t,e){this.attribSize=e-this.attribStart,this.size=t-this.start},t.prototype.reset=function(){this.style=null,this.size=0,this.start=0,this.attribStart=0,this.attribSize=0},t}(),Y=((q={})[o.POLY]=I,q[o.CIRC]=B,q[o.ELIP]=B,q[o.RECT]=L,q[o.RREC]=j,q),V=[],Q=[],X=function(){function t(t,e,i,r){void 0===e&&(e=null),void 0===i&&(i=null),void 0===r&&(r=null),this.points=[],this.holes=[],this.shape=t,this.lineStyle=i,this.fillStyle=e,this.matrix=r,this.type=t.type}return t.prototype.clone=function(){return new t(this.shape,this.fillStyle,this.lineStyle,this.matrix)},t.prototype.destroy=function(){this.shape=null,this.holes.length=0,this.holes=null,this.points.length=0,this.points=null,this.lineStyle=null,this.fillStyle=null},t}(),Z=new l,J=function(t){function n(){var e=t.call(this)||this;return e.closePointEps=1e-4,e.boundsPadding=0,e.uvsFloat32=null,e.indicesUint16=null,e.batchable=!1,e.points=[],e.colors=[],e.uvs=[],e.indices=[],e.textureIds=[],e.graphicsData=[],e.drawCalls=[],e.batchDirty=-1,e.batches=[],e.dirty=0,e.cacheDirty=-1,e.clearDirty=0,e.shapeIndex=0,e._bounds=new M,e.boundsDirty=-1,e}return C(n,t),Object.defineProperty(n.prototype,"bounds",{get:function(){return this.updateBatches(),this.boundsDirty!==this.dirty&&(this.boundsDirty=this.dirty,this.calculateBounds()),this._bounds},enumerable:!1,configurable:!0}),n.prototype.invalidate=function(){this.boundsDirty=-1,this.dirty++,this.batchDirty++,this.shapeIndex=0,this.points.length=0,this.colors.length=0,this.uvs.length=0,this.indices.length=0,this.textureIds.length=0;for(var t=0;t<this.drawCalls.length;t++)this.drawCalls[t].texArray.clear(),Q.push(this.drawCalls[t]);this.drawCalls.length=0;for(t=0;t<this.batches.length;t++){var e=this.batches[t];e.reset(),V.push(e)}this.batches.length=0},n.prototype.clear=function(){return this.graphicsData.length>0&&(this.invalidate(),this.clearDirty++,this.graphicsData.length=0),this},n.prototype.drawShape=function(t,e,i,r){void 0===e&&(e=null),void 0===i&&(i=null),void 0===r&&(r=null);var n=new X(t,e,i,r);return this.graphicsData.push(n),this.dirty++,this},n.prototype.drawHole=function(t,e){if(void 0===e&&(e=null),!this.graphicsData.length)return null;var i=new X(t,null,null,e),r=this.graphicsData[this.graphicsData.length-1];return i.lineStyle=r.lineStyle,r.holes.push(i),this.dirty++,this},n.prototype.destroy=function(){t.prototype.destroy.call(this);for(var e=0;e<this.graphicsData.length;++e)this.graphicsData[e].destroy();this.points.length=0,this.points=null,this.colors.length=0,this.colors=null,this.uvs.length=0,this.uvs=null,this.indices.length=0,this.indices=null,this.indexBuffer.destroy(),this.indexBuffer=null,this.graphicsData.length=0,this.graphicsData=null,this.drawCalls.length=0,this.drawCalls=null,this.batches.length=0,this.batches=null,this._bounds=null},n.prototype.containsPoint=function(t){for(var e=this.graphicsData,i=0;i<e.length;++i){var r=e[i];if(r.fillStyle.visible&&(r.shape&&(r.matrix?r.matrix.applyInverse(t,Z):Z.copyFrom(t),r.shape.contains(Z.x,Z.y)))){var n=!1;if(r.holes)for(var s=0;s<r.holes.length;s++){if(r.holes[s].shape.contains(Z.x,Z.y)){n=!0;break}}if(!n)return!0}}return!1},n.prototype.updateBatches=function(){if(this.graphicsData.length){if(this.validateBatching()){this.cacheDirty=this.dirty;var t=this.uvs,e=this.graphicsData,i=null,r=null;this.batches.length>0&&(r=(i=this.batches[this.batches.length-1]).style);for(var n=this.shapeIndex;n<e.length;n++){this.shapeIndex++;var s=e[n],h=s.fillStyle,a=s.lineStyle;Y[s.type].build(s),s.matrix&&this.transformPoints(s.points,s.matrix),(h.visible||a.visible)&&this.processHoles(s.holes);for(var o=0;o<2;o++){var l=0===o?h:a;if(l.visible){var u=l.texture.baseTexture,p=this.indices.length,c=this.points.length/2;u.wrapMode=x.REPEAT,0===o?this.processFill(s):this.processLine(s);var f=this.points.length/2-c;0!==f&&(i&&!this._compareStyles(r,l)&&(i.end(p,c),i=null),i||((i=V.pop()||new W).begin(l,p,c),this.batches.push(i),r=l),this.addUvs(this.points,t,l.texture,c,f,l.matrix))}}}var d=this.indices.length,y=this.points.length/2;if(i&&i.end(d,y),0!==this.batches.length){var v=y>65535;this.indicesUint16&&this.indices.length===this.indicesUint16.length&&v===this.indicesUint16.BYTES_PER_ELEMENT>2?this.indicesUint16.set(this.indices):this.indicesUint16=v?new Uint32Array(this.indices):new Uint16Array(this.indices),this.batchable=this.isBatchable(),this.batchable?this.packBatches():this.buildDrawCalls()}else this.batchable=!0}}else this.batchable=!0},n.prototype._compareStyles=function(t,e){return!(!t||!e)&&(t.texture.baseTexture===e.texture.baseTexture&&(t.color+t.alpha===e.color+e.alpha&&!!t.native==!!e.native))},n.prototype.validateBatching=function(){if(this.dirty===this.cacheDirty||!this.graphicsData.length)return!1;for(var t=0,e=this.graphicsData.length;t<e;t++){var i=this.graphicsData[t],r=i.fillStyle,n=i.lineStyle;if(r&&!r.texture.baseTexture.valid)return!1;if(n&&!n.texture.baseTexture.valid)return!1}return!0},n.prototype.packBatches=function(){this.batchDirty++,this.uvsFloat32=new Float32Array(this.uvs);for(var t=this.batches,e=0,i=t.length;e<i;e++)for(var r=t[e],n=0;n<r.size;n++){var s=r.start+n;this.indicesUint16[s]=this.indicesUint16[s]-r.attribStart}},n.prototype.isBatchable=function(){if(this.points.length>131070)return!1;for(var t=this.batches,e=0;e<t.length;e++)if(t[e].style.native)return!1;return this.points.length<2*n.BATCHABLE_SIZE},n.prototype.buildDrawCalls=function(){for(var t=++e._globalBatch,n=0;n<this.drawCalls.length;n++)this.drawCalls[n].texArray.clear(),Q.push(this.drawCalls[n]);this.drawCalls.length=0;var s=this.colors,h=this.textureIds,a=Q.pop();a||((a=new i).texArray=new r),a.texArray.count=0,a.start=0,a.size=0,a.type=_.TRIANGLES;var o=0,l=null,u=0,p=!1,c=_.TRIANGLES,f=0;this.drawCalls.push(a);for(n=0;n<this.batches.length;n++){var d=this.batches[n],y=d.style,v=y.texture.baseTexture;p!==!!y.native&&(c=(p=!!y.native)?_.LINES:_.TRIANGLES,l=null,o=8,t++),l!==v&&(l=v,v._batchEnabled!==t&&(8===o&&(t++,o=0,a.size>0&&((a=Q.pop())||((a=new i).texArray=new r),this.drawCalls.push(a)),a.start=f,a.size=0,a.texArray.count=0,a.type=c),v.touched=1,v._batchEnabled=t,v._batchLocation=o,v.wrapMode=x.REPEAT,a.texArray.elements[a.texArray.count++]=v,o++)),a.size+=d.size,f+=d.size,u=v._batchLocation,this.addColors(s,y.color,y.alpha,d.attribSize,d.attribStart),this.addTextureIds(h,u,d.attribSize,d.attribStart)}e._globalBatch=t,this.packAttributes()},n.prototype.packAttributes=function(){for(var t=this.points,e=this.uvs,i=this.colors,r=this.textureIds,n=new ArrayBuffer(3*t.length*4),s=new Float32Array(n),h=new Uint32Array(n),a=0,o=0;o<t.length/2;o++)s[a++]=t[2*o],s[a++]=t[2*o+1],s[a++]=e[2*o],s[a++]=e[2*o+1],h[a++]=i[o],s[a++]=r[o];this._buffer.update(n),this._indexBuffer.update(this.indicesUint16)},n.prototype.processFill=function(t){t.holes.length?I.triangulate(t,this):Y[t.type].triangulate(t,this)},n.prototype.processLine=function(t){z(t,this);for(var e=0;e<t.holes.length;e++)z(t.holes[e],this)},n.prototype.processHoles=function(t){for(var e=0;e<t.length;e++){var i=t[e];Y[i.type].build(i),i.matrix&&this.transformPoints(i.points,i.matrix)}},n.prototype.calculateBounds=function(){var t=this._bounds;t.clear(),t.addVertexData(this.points,0,this.points.length),t.pad(this.boundsPadding,this.boundsPadding)},n.prototype.transformPoints=function(t,e){for(var i=0;i<t.length/2;i++){var r=t[2*i],n=t[2*i+1];t[2*i]=e.a*r+e.c*n+e.tx,t[2*i+1]=e.b*r+e.d*n+e.ty}},n.prototype.addColors=function(t,e,i,r,n){void 0===n&&(n=0);var s=b((e>>16)+(65280&e)+((255&e)<<16),i);t.length=Math.max(t.length,n+r);for(var h=0;h<r;h++)t[n+h]=s},n.prototype.addTextureIds=function(t,e,i,r){void 0===r&&(r=0),t.length=Math.max(t.length,r+i);for(var n=0;n<i;n++)t[r+n]=e},n.prototype.addUvs=function(t,e,i,r,n,s){void 0===s&&(s=null);for(var h=0,a=e.length,o=i.frame;h<n;){var l=t[2*(r+h)],u=t[2*(r+h)+1];if(s){var p=s.a*l+s.c*u+s.tx;u=s.b*l+s.d*u+s.ty,l=p}h++,e.push(l/o.width,u/o.height)}var c=i.baseTexture;(o.width<c.width||o.height<c.height)&&this.adjustUvs(e,i,a,n)},n.prototype.adjustUvs=function(t,e,i,r){for(var n=e.baseTexture,s=1e-6,h=i+2*r,a=e.frame,o=a.width/n.width,l=a.height/n.height,u=a.x/a.width,p=a.y/a.height,c=Math.floor(t[i]+s),f=Math.floor(t[i+1]+s),d=i+2;d<h;d+=2)c=Math.min(c,Math.floor(t[d]+s)),f=Math.min(f,Math.floor(t[d+1]+s));u-=c,p-=f;for(d=i;d<h;d+=2)t[d]=(t[d]+u)*o,t[d+1]=(t[d+1]+p)*l},n.BATCHABLE_SIZE=100,n}(n),K=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.width=0,e.alignment=.5,e.native=!1,e.cap=T.BUTT,e.join=P.MITER,e.miterLimit=10,e}return C(e,t),e.prototype.clone=function(){var t=new e;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t.width=this.width,t.alignment=this.alignment,t.native=this.native,t.cap=this.cap,t.join=this.join,t.miterLimit=this.miterLimit,t},e.prototype.reset=function(){t.prototype.reset.call(this),this.color=0,this.alignment=.5,this.width=0,this.native=!1},e}(A),$=new Float32Array(3),tt={},et=function(e){function i(t){void 0===t&&(t=null);var i=e.call(this)||this;return i.shader=null,i.pluginName="batch",i.currentPath=null,i.batches=[],i.batchTint=-1,i.batchDirty=-1,i.vertexData=null,i._fillStyle=new A,i._lineStyle=new K,i._matrix=null,i._holeMode=!1,i.state=a.for2d(),i._geometry=t||new J,i._geometry.refCount++,i._transformID=-1,i.tint=16777215,i.blendMode=w.NORMAL,i}return C(i,e),Object.defineProperty(i.prototype,"geometry",{get:function(){return this._geometry},enumerable:!1,configurable:!0}),i.prototype.clone=function(){return this.finishPoly(),new i(this._geometry)},Object.defineProperty(i.prototype,"blendMode",{get:function(){return this.state.blendMode},set:function(t){this.state.blendMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"fill",{get:function(){return this._fillStyle},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"line",{get:function(){return this._lineStyle},enumerable:!1,configurable:!0}),i.prototype.lineStyle=function(t,e,i,r,n){return void 0===t&&(t=null),void 0===e&&(e=0),void 0===i&&(i=1),void 0===r&&(r=.5),void 0===n&&(n=!1),"number"==typeof t&&(t={width:t,color:e,alpha:i,alignment:r,native:n}),this.lineTextureStyle(t)},i.prototype.lineTextureStyle=function(e){e=Object.assign({width:0,texture:t.WHITE,color:e&&e.texture?16777215:0,alpha:1,matrix:null,alignment:.5,native:!1,cap:T.BUTT,join:P.MITER,miterLimit:10},e),this.currentPath&&this.startPoly();var i=e.width>0&&e.alpha>0;return i?(e.matrix&&(e.matrix=e.matrix.clone(),e.matrix.invert()),Object.assign(this._lineStyle,{visible:i},e)):this._lineStyle.reset(),this},i.prototype.startPoly=function(){if(this.currentPath){var t=this.currentPath.points,e=this.currentPath.points.length;e>2&&(this.drawShape(this.currentPath),this.currentPath=new p,this.currentPath.closeStroke=!1,this.currentPath.points.push(t[e-2],t[e-1]))}else this.currentPath=new p,this.currentPath.closeStroke=!1},i.prototype.finishPoly=function(){this.currentPath&&(this.currentPath.points.length>2?(this.drawShape(this.currentPath),this.currentPath=null):this.currentPath.points.length=0)},i.prototype.moveTo=function(t,e){return this.startPoly(),this.currentPath.points[0]=t,this.currentPath.points[1]=e,this},i.prototype.lineTo=function(t,e){this.currentPath||this.moveTo(0,0);var i=this.currentPath.points,r=i[i.length-2],n=i[i.length-1];return r===t&&n===e||i.push(t,e),this},i.prototype._initCurve=function(t,e){void 0===t&&(t=0),void 0===e&&(e=0),this.currentPath?0===this.currentPath.points.length&&(this.currentPath.points=[t,e]):this.moveTo(t,e)},i.prototype.quadraticCurveTo=function(t,e,i,r){this._initCurve();var n=this.currentPath.points;return 0===n.length&&this.moveTo(0,0),G.curveTo(t,e,i,r,n),this},i.prototype.bezierCurveTo=function(t,e,i,r,n,s){return this._initCurve(),H.curveTo(t,e,i,r,n,s,this.currentPath.points),this},i.prototype.arcTo=function(t,e,i,r,n){this._initCurve(t,e);var s=this.currentPath.points,h=k.curveTo(t,e,i,r,n,s);if(h){var a=h.cx,o=h.cy,l=h.radius,u=h.startAngle,p=h.endAngle,c=h.anticlockwise;this.arc(a,o,l,u,p,c)}return this},i.prototype.arc=function(t,e,i,r,n,s){if(void 0===s&&(s=!1),r===n)return this;if(!s&&n<=r?n+=u:s&&r<=n&&(r+=u),0===n-r)return this;var h=t+Math.cos(r)*i,a=e+Math.sin(r)*i,o=this._geometry.closePointEps,l=this.currentPath?this.currentPath.points:null;if(l){var p=Math.abs(l[l.length-2]-h),c=Math.abs(l[l.length-1]-a);p<o&&c<o||l.push(h,a)}else this.moveTo(h,a),l=this.currentPath.points;return k.arc(h,a,t,e,i,r,n,s,l),this},i.prototype.beginFill=function(e,i){return void 0===e&&(e=0),void 0===i&&(i=1),this.beginTextureFill({texture:t.WHITE,color:e,alpha:i})},i.prototype.beginTextureFill=function(e){e=Object.assign({texture:t.WHITE,color:16777215,alpha:1,matrix:null},e),this.currentPath&&this.startPoly();var i=e.alpha>0;return i?(e.matrix&&(e.matrix=e.matrix.clone(),e.matrix.invert()),Object.assign(this._fillStyle,{visible:i},e)):this._fillStyle.reset(),this},i.prototype.endFill=function(){return this.finishPoly(),this._fillStyle.reset(),this},i.prototype.drawRect=function(t,e,i,r){return this.drawShape(new c(t,e,i,r))},i.prototype.drawRoundedRect=function(t,e,i,r,n){return this.drawShape(new f(t,e,i,r,n))},i.prototype.drawCircle=function(t,e,i){return this.drawShape(new d(t,e,i))},i.prototype.drawEllipse=function(t,e,i,r){return this.drawShape(new y(t,e,i,r))},i.prototype.drawPolygon=function(){for(var t,e=arguments,i=[],r=0;r<arguments.length;r++)i[r]=e[r];var n=!0,s=i[0];s.points?(n=s.closeStroke,t=s.points):t=Array.isArray(i[0])?i[0]:i;var h=new p(t);return h.closeStroke=n,this.drawShape(h),this},i.prototype.drawShape=function(t){return this._holeMode?this._geometry.drawHole(t,this._matrix):this._geometry.drawShape(t,this._fillStyle.clone(),this._lineStyle.clone(),this._matrix),this},i.prototype.clear=function(){return this._geometry.clear(),this._lineStyle.reset(),this._fillStyle.reset(),this._boundsID++,this._matrix=null,this._holeMode=!1,this.currentPath=null,this},i.prototype.isFastRect=function(){var t=this._geometry.graphicsData;return!(1!==t.length||t[0].shape.type!==o.RECT||t[0].matrix||t[0].holes.length||t[0].lineStyle.visible&&t[0].lineStyle.width)},i.prototype._render=function(t){this.finishPoly();var e=this._geometry;e.updateBatches(),e.batchable?(this.batchDirty!==e.batchDirty&&this._populateBatches(),this._renderBatched(t)):(t.batch.flush(),this._renderDirect(t))},i.prototype._populateBatches=function(){var t=this._geometry,e=this.blendMode,i=t.batches.length;this.batchTint=-1,this._transformID=-1,this.batchDirty=t.batchDirty,this.batches.length=i,this.vertexData=new Float32Array(t.points);for(var r=0;r<i;r++){var n=t.batches[r],s=n.style.color,h=new Float32Array(this.vertexData.buffer,4*n.attribStart*2,2*n.attribSize),a=new Float32Array(t.uvsFloat32.buffer,4*n.attribStart*2,2*n.attribSize),o={vertexData:h,blendMode:e,indices:new Uint16Array(t.indicesUint16.buffer,2*n.start,n.size),uvs:a,_batchRGB:m(s),_tintRGB:s,_texture:n.style.texture,alpha:n.style.alpha,worldAlpha:1};this.batches[r]=o}},i.prototype._renderBatched=function(t){if(this.batches.length){t.batch.setObjectRenderer(t.plugins[this.pluginName]),this.calculateVertices(),this.calculateTints();for(var e=0,i=this.batches.length;e<i;e++){var r=this.batches[e];r.worldAlpha=this.worldAlpha*r.alpha,t.plugins[this.pluginName].render(r)}}},i.prototype._renderDirect=function(t){var e=this._resolveDirectShader(t),i=this._geometry,r=this.tint,n=this.worldAlpha,s=e.uniforms,h=i.drawCalls;s.translationMatrix=this.transform.worldTransform,s.tint[0]=(r>>16&255)/255*n,s.tint[1]=(r>>8&255)/255*n,s.tint[2]=(255&r)/255*n,s.tint[3]=n,t.shader.bind(e),t.geometry.bind(i,e),t.state.set(this.state);for(var a=0,o=h.length;a<o;a++)this._renderDrawCallDirect(t,i.drawCalls[a])},i.prototype._renderDrawCallDirect=function(t,e){for(var i=e.texArray,r=e.type,n=e.size,s=e.start,h=i.count,a=0;a<h;a++)t.texture.bind(i.elements[a],a);t.geometry.draw(r,n,s)},i.prototype._resolveDirectShader=function(t){var e=this.shader,i=this.pluginName;if(!e){if(!tt[i]){for(var r=t.plugins[i].MAX_TEXTURES,n=new Int32Array(r),a=0;a<r;a++)n[a]=a;var o={tint:new Float32Array([1,1,1,1]),translationMatrix:new v,default:s.from({uSamplers:n},!0)},l=t.plugins[i]._shader.program;tt[i]=new h(l,o)}e=tt[i]}return e},i.prototype._calculateBounds=function(){this.finishPoly();var t=this._geometry;if(t.graphicsData.length){var e=t.bounds,i=e.minX,r=e.minY,n=e.maxX,s=e.maxY;this._bounds.addFrame(this.transform,i,r,n,s)}},i.prototype.containsPoint=function(t){return this.worldTransform.applyInverse(t,i._TEMP_POINT),this._geometry.containsPoint(i._TEMP_POINT)},i.prototype.calculateTints=function(){if(this.batchTint!==this.tint){this.batchTint=this.tint;for(var t=m(this.tint,$),e=0;e<this.batches.length;e++){var i=this.batches[e],r=i._batchRGB,n=(t[0]*r[0]*255<<16)+(t[1]*r[1]*255<<8)+(0|t[2]*r[2]*255);i._tintRGB=(n>>16)+(65280&n)+((255&n)<<16)}}},i.prototype.calculateVertices=function(){var t=this.transform._worldID;if(this._transformID!==t){this._transformID=t;for(var e=this.transform.worldTransform,i=e.a,r=e.b,n=e.c,s=e.d,h=e.tx,a=e.ty,o=this._geometry.points,l=this.vertexData,u=0,p=0;p<o.length;p+=2){var c=o[p],f=o[p+1];l[u++]=i*c+n*f+h,l[u++]=s*f+r*c+a}}},i.prototype.closePath=function(){var t=this.currentPath;return t&&(t.closeStroke=!0,this.finishPoly()),this},i.prototype.setMatrix=function(t){return this._matrix=t,this},i.prototype.beginHole=function(){return this.finishPoly(),this._holeMode=!0,this},i.prototype.endHole=function(){return this.finishPoly(),this._holeMode=!1,this},i.prototype.destroy=function(t){this._geometry.refCount--,0===this._geometry.refCount&&this._geometry.dispose(),this._matrix=null,this.currentPath=null,this._lineStyle.destroy(),this._lineStyle=null,this._fillStyle.destroy(),this._fillStyle=null,this._geometry=null,this.shader=null,this.vertexData=null,this.batches.length=0,this.batches=null,e.prototype.destroy.call(this,t)},i.nextRoundedRectBehavior=!1,i._TEMP_POINT=new l,i}(S),it={buildPoly:I,buildCircle:B,buildRectangle:L,buildRoundedRectangle:j,buildLine:z,ArcUtils:k,BezierUtils:H,QuadraticUtils:G,BatchPart:W,FILL_COMMANDS:Y,BATCH_POOL:V,DRAW_CALL_POOL:Q};export{A as FillStyle,D as GRAPHICS_CURVES,et as Graphics,X as GraphicsData,J as GraphicsGeometry,T as LINE_CAP,P as LINE_JOIN,K as LineStyle,it as graphicsUtils};
//# sourceMappingURL=graphics.min.mjs.map
