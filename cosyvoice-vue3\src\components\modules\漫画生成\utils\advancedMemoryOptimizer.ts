/**
 * 🧠 高级内存优化器 - 解决709MB内存占用问题
 * 专门针对Vue.js漫画应用的深度内存管理
 */

export interface MemoryDiagnostics {
  totalMemory: number;
  usedMemory: number;
  imageCache: {
    size: number;
    totalSize: number;
    oldestTimestamp: number;
  };
  websocketInstances: number;
  componentInstances: {
    safeImage: number;
    comicGallery: number;
  };
  leakSources: string[];
}

export class AdvancedMemoryOptimizer {
  private static instance: AdvancedMemoryOptimizer;
  private memoryThreshold = 500 * 1024 * 1024; // 500MB 阈值
  private cleanupInterval: NodeJS.Timeout | null = null;
  private lastCleanupTime = 0;
  private isOptimizing = false;

  // 内存追踪器
  private imageInstances = new WeakSet();
  private componentInstances = new Map<string, number>();
  private websocketTracker = new Set<any>();

  private constructor() {
    this.startAdvancedMonitoring();
  }

  public static getInstance(): AdvancedMemoryOptimizer {
    if (!AdvancedMemoryOptimizer.instance) {
      AdvancedMemoryOptimizer.instance = new AdvancedMemoryOptimizer();
    }
    return AdvancedMemoryOptimizer.instance;
  }

  /**
   * 🔍 深度内存诊断
   */
  public async diagnoseMemory(): Promise<MemoryDiagnostics> {
    console.log('🔍 开始深度内存诊断...');

    const diagnostics: MemoryDiagnostics = {
      totalMemory: 0,
      usedMemory: 0,
      imageCache: { size: 0, totalSize: 0, oldestTimestamp: Date.now() },
      websocketInstances: 0,
      componentInstances: { safeImage: 0, comicGallery: 0 },
      leakSources: []
    };

    try {
      // 1. 基础内存信息
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        const memory = (performance as any).memory;
        diagnostics.totalMemory = memory.totalJSHeapSize;
        diagnostics.usedMemory = memory.usedJSHeapSize;
      }

      // 2. 图片缓存分析
      await this.analyzeImageCache(diagnostics);

      // 3. WebSocket实例检查
      await this.analyzeWebSocketInstances(diagnostics);

      // 4. 组件实例统计
      this.analyzeComponentInstances(diagnostics);

      // 5. 内存泄漏源识别
      this.identifyLeakSources(diagnostics);

      console.log('📊 内存诊断完成:', diagnostics);
      return diagnostics;

    } catch (error) {
      console.error('❌ 内存诊断失败:', error);
      return diagnostics;
    }
  }

  /**
   * 🖼️ 分析图片缓存内存使用
   */
  private async analyzeImageCache(diagnostics: MemoryDiagnostics): Promise<void> {
    try {
      // 检查SafeImage全局缓存
      const safeImageCache = (window as any).globalUrlCache;
      if (safeImageCache && safeImageCache instanceof Map) {
        diagnostics.imageCache.size = safeImageCache.size;
        
        // 估算缓存内存占用
        let totalSize = 0;
        let oldestTime = Date.now();
        
        safeImageCache.forEach((value: string, key: string) => {
          // Base64数据通常占用更多内存
          if (value.startsWith('data:image')) {
            totalSize += value.length * 2; // Base64编码大约2倍原始大小
          } else {
            totalSize += key.length + value.length;
          }
        });

        diagnostics.imageCache.totalSize = totalSize;
        
        // 检查时间戳缓存
        const timestampCache = (window as any).cacheTimestamps;
        if (timestampCache && timestampCache instanceof Map) {
          timestampCache.forEach((timestamp: number) => {
            if (timestamp < oldestTime) {
              oldestTime = timestamp;
            }
          });
          diagnostics.imageCache.oldestTimestamp = oldestTime;
        }

        console.log(`🖼️ 图片缓存分析: ${diagnostics.imageCache.size}个条目, 约${(totalSize / 1024 / 1024).toFixed(2)}MB`);
      }
    } catch (error) {
      console.warn('⚠️ 图片缓存分析失败:', error);
    }
  }

  /**
   * 🔌 分析WebSocket实例（使用统一管理器）
   */
  private async analyzeWebSocketInstances(diagnostics: MemoryDiagnostics): Promise<void> {
    try {
      // 🚀 使用新的WebSocket诊断功能
      const { getWebSocketMemoryDiagnostics } = await import('@/services/masterWebSocketManager');
      const wsInfo = getWebSocketMemoryDiagnostics();

      diagnostics.websocketInstances = wsInfo.totalInstances;
      diagnostics.activeConnections = wsInfo.activeConnections;
      diagnostics.memoryEstimate += wsInfo.memoryEstimate;

      console.log(`🔌 WebSocket实例分析: ${diagnostics.websocketInstances}个实例 (活跃: ${wsInfo.activeConnections})`);

      // 检查异常情况
      if (diagnostics.websocketInstances > 2) { // 允许最多2个实例
        diagnostics.leakSources.push(`WebSocket实例过多: ${diagnostics.websocketInstances}个`);
      }

      if (wsInfo.inactiveConnections > 0) {
        diagnostics.leakSources.push(`WebSocket僵尸连接: ${wsInfo.inactiveConnections}个`);
      }
    } catch (error) {
      console.warn('⚠️ WebSocket实例分析失败:', error);

      // 降级方案：直接检查全局实例
      try {
        const wsManager = (window as any).masterWebSocketManager;
        if (wsManager && wsManager.instances) {
          diagnostics.websocketInstances = wsManager.instances.size;
          console.log(`🔌 WebSocket实例分析（降级）: ${diagnostics.websocketInstances}个实例`);
        }
      } catch (fallbackError) {
        console.warn('⚠️ 降级WebSocket分析也失败:', fallbackError);
      }
    }
  }

  /**
   * 🧩 分析组件实例
   */
  private analyzeComponentInstances(diagnostics: MemoryDiagnostics): void {
    try {
      // 统计DOM中的组件实例
      const safeImageElements = document.querySelectorAll('.safe-image-container');
      const comicGalleryElements = document.querySelectorAll('.comic-gallery');
      
      diagnostics.componentInstances.safeImage = safeImageElements.length;
      diagnostics.componentInstances.comicGallery = comicGalleryElements.length;
      
      console.log(`🧩 组件实例: SafeImage=${diagnostics.componentInstances.safeImage}, ComicGallery=${diagnostics.componentInstances.comicGallery}`);
    } catch (error) {
      console.warn('⚠️ 组件实例分析失败:', error);
    }
  }

  /**
   * 🕵️ 识别内存泄漏源
   */
  private identifyLeakSources(diagnostics: MemoryDiagnostics): void {
    const { usedMemory, imageCache, websocketInstances, componentInstances } = diagnostics;
    
    // 高内存使用警告
    if (usedMemory > 600 * 1024 * 1024) { // 600MB+
      diagnostics.leakSources.push(`总内存过高: ${(usedMemory / 1024 / 1024).toFixed(2)}MB`);
    }

    // 图片缓存过大
    if (imageCache.size > 100) {
      diagnostics.leakSources.push(`图片缓存过多: ${imageCache.size}个条目`);
    }

    // 缓存数据过大
    if (imageCache.totalSize > 50 * 1024 * 1024) { // 50MB+
      diagnostics.leakSources.push(`图片缓存过大: ${(imageCache.totalSize / 1024 / 1024).toFixed(2)}MB`);
    }

    // 过期缓存
    const cacheAge = Date.now() - imageCache.oldestTimestamp;
    if (cacheAge > 10 * 60 * 1000) { // 10分钟+
      diagnostics.leakSources.push(`过期缓存: 最老缓存${Math.round(cacheAge / 60000)}分钟`);
    }

    // WebSocket实例过多
    if (websocketInstances > 1) {
      diagnostics.leakSources.push(`WebSocket实例重复: ${websocketInstances}个`);
    }

    // SafeImage组件过多
    if (componentInstances.safeImage > 20) {
      diagnostics.leakSources.push(`SafeImage组件过多: ${componentInstances.safeImage}个`);
    }
  }

  /**
   * 🧹 深度内存清理
   */
  public async performDeepCleanup(): Promise<{ cleaned: boolean; freedMemory: number; actions: string[] }> {
    if (this.isOptimizing) {
      console.log('⏳ 内存优化正在进行中...');
      return { cleaned: false, freedMemory: 0, actions: ['优化进行中'] };
    }

    this.isOptimizing = true;
    const actions: string[] = [];
    let beforeMemory = 0;
    let afterMemory = 0;

    try {
      console.log('🧹 开始深度内存清理...');

      // 记录清理前内存
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        beforeMemory = (performance as any).memory.usedJSHeapSize;
      }

      // 1. 清理图片缓存
      await this.cleanupImageCaches();
      actions.push('清理图片缓存');

      // 2. 清理WebSocket重复实例
      await this.cleanupWebSocketInstances();
      actions.push('清理WebSocket实例');

      // 3. 清理Base64数据
      await this.cleanupBase64Data();
      actions.push('清理Base64数据');

      // 4. 清理DOM引用
      this.cleanupDOMReferences();
      actions.push('清理DOM引用');

      // 5. 强制垃圾回收
      this.forceGarbageCollection();
      actions.push('强制垃圾回收');

      // 记录清理后内存
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        afterMemory = (performance as any).memory.usedJSHeapSize;
      }

      const freedMemory = beforeMemory - afterMemory;
      console.log(`✅ 深度清理完成: 释放了${(freedMemory / 1024 / 1024).toFixed(2)}MB内存`);

      return { cleaned: true, freedMemory, actions };

    } catch (error) {
      console.error('❌ 深度内存清理失败:', error);
      return { cleaned: false, freedMemory: 0, actions: ['清理失败'] };
    } finally {
      this.isOptimizing = false;
      this.lastCleanupTime = Date.now();
    }
  }

  /**
   * 🖼️ 清理图片缓存
   */
  private async cleanupImageCaches(): Promise<void> {
    try {
      // 清理SafeImage全局缓存
      const globalCache = (window as any).globalUrlCache;
      if (globalCache && globalCache instanceof Map) {
        const beforeSize = globalCache.size;
        
        // 清理过期缓存
        const timestampCache = (window as any).cacheTimestamps;
        if (timestampCache && timestampCache instanceof Map) {
          const now = Date.now();
          const expireTime = 2 * 60 * 1000; // 2分钟过期
          
          for (const [key, timestamp] of timestampCache.entries()) {
            if (now - timestamp > expireTime) {
              globalCache.delete(key);
              timestampCache.delete(key);
            }
          }
        }

        // 如果仍然太大，清理最老的缓存
        if (globalCache.size > 50) {
          const entries = Array.from(globalCache.entries());
          const toDelete = entries.slice(0, globalCache.size - 50);
          toDelete.forEach(([key]) => {
            globalCache.delete(key);
            if (timestampCache) timestampCache.delete(key);
          });
        }

        console.log(`🖼️ 图片缓存清理: ${beforeSize} → ${globalCache.size}`);
      }

      // 发送清理事件给所有SafeImage组件
      window.dispatchEvent(new CustomEvent('clear-image-cache'));
      
    } catch (error) {
      console.warn('⚠️ 图片缓存清理失败:', error);
    }
  }

  /**
   * 🔌 清理WebSocket实例（使用统一管理器）
   */
  private async cleanupWebSocketInstances(): Promise<void> {
    try {
      // 🚀 使用新的masterWebSocketManager清理功能
      const { cleanupAllWebSocketConnections } = await import('@/services/masterWebSocketManager');
      cleanupAllWebSocketConnections();

      console.log('🔌 WebSocket实例已通过masterWebSocketManager清理');
    } catch (error) {
      console.warn('⚠️ WebSocket实例清理失败:', error);

      // 备用清理方案
      try {
        const cleanupFunction = (window as any).cleanupAllWebSocketConnections;
        if (cleanupFunction && typeof cleanupFunction === 'function') {
          cleanupFunction();
          console.log('🔌 WebSocket实例已通过备用方案清理');
        }
      } catch (fallbackError) {
        console.warn('⚠️ 备用WebSocket清理也失败:', fallbackError);
      }
    }
  }

  /**
   * 📄 清理Base64数据
   */
  private async cleanupBase64Data(): Promise<void> {
    try {
      // 查找并清理DOM中的Base64图片
      const images = document.querySelectorAll('img[src^="data:image"]');
      let cleanedCount = 0;
      
      images.forEach((img: Element) => {
        const imgElement = img as HTMLImageElement;
        if (imgElement.src.length > 1000) { // 大Base64数据
          // 替换为占位符
          imgElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjwvc3ZnPg==';
          cleanedCount++;
        }
      });
      
      if (cleanedCount > 0) {
        console.log(`📄 Base64数据清理: 清理了${cleanedCount}个大图片`);
      }
    } catch (error) {
      console.warn('⚠️ Base64数据清理失败:', error);
    }
  }

  /**
   * 🧩 清理DOM引用
   */
  private cleanupDOMReferences(): void {
    try {
      // 清理可能的DOM引用泄漏
      const elementsToClean = [
        '.safe-image-container img[data-cleanup="true"]',
        '.comic-card[data-removed="true"]',
        '.temp-element'
      ];

      let cleanedCount = 0;
      elementsToClean.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          el.remove();
          cleanedCount++;
        });
      });

      if (cleanedCount > 0) {
        console.log(`🧩 DOM引用清理: 清理了${cleanedCount}个元素`);
      }
    } catch (error) {
      console.warn('⚠️ DOM引用清理失败:', error);
    }
  }

  /**
   * 🗑️ 强制垃圾回收
   */
  private forceGarbageCollection(): void {
    try {
      // 尝试多种方式触发垃圾回收
      if (typeof window !== 'undefined' && (window as any).gc) {
        (window as any).gc();
        console.log('🗑️ 浏览器垃圾回收已触发');
      }

      if (typeof global !== 'undefined' && global.gc) {
        global.gc();
        console.log('🗑️ Node.js垃圾回收已触发');
      }

      // 创建一些压力来触发自动垃圾回收
      const pressure = new Array(1000).fill(null).map(() => ({ data: new Array(1000).fill(0) }));
      pressure.length = 0; // 立即释放
      
    } catch (error) {
      console.warn('⚠️ 垃圾回收触发失败:', error);
    }
  }

  /**
   * 📊 启动高级监控
   */
  private startAdvancedMonitoring(): void {
    this.cleanupInterval = setInterval(() => {
      this.checkMemoryAndOptimize();
    }, 30000); // 每30秒检查一次

    console.log('📊 高级内存监控已启动');
  }

  /**
   * 🔍 检查内存并自动优化
   */
  private async checkMemoryAndOptimize(): Promise<void> {
    try {
      const diagnostics = await this.diagnoseMemory();
      
      // 如果内存使用超过阈值或检测到泄漏源
      if (diagnostics.usedMemory > this.memoryThreshold || diagnostics.leakSources.length > 0) {
        console.warn(`🚨 内存使用过高: ${(diagnostics.usedMemory / 1024 / 1024).toFixed(2)}MB`);
        console.log('🧹 自动触发深度清理...');
        
        await this.performDeepCleanup();
      }
    } catch (error) {
      console.warn('⚠️ 自动内存检查失败:', error);
    }
  }

  /**
   * 🛑 停止监控
   */
  public stopMonitoring(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log('🛑 高级内存监控已停止');
    }
  }
}

// 导出单例实例
export const advancedMemoryOptimizer = AdvancedMemoryOptimizer.getInstance();

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).advancedMemoryOptimizer = advancedMemoryOptimizer;
  (window as any).diagnoseMemory = () => advancedMemoryOptimizer.diagnoseMemory();
  (window as any).deepCleanup = () => advancedMemoryOptimizer.performDeepCleanup();
}