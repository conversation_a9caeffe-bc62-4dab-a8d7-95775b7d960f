/*!
 * @pixi/polyfill - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/polyfill is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
!function(){"use strict";"undefined"==typeof globalThis&&("undefined"!=typeof self?self.globalThis=self:"undefined"!=typeof global&&(global.globalThis=global));var e=setTimeout;function n(e){return Boolean(e&&void 0!==e.length)}function t(){}function r(e){if(!(this instanceof r))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],c(e,this)}function o(e,n){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,r._immediateFn((function(){var t=1===e._state?n.onFulfilled:n.onRejected;if(null!==t){var r;try{r=t(e._value)}catch(e){return void a(n.promise,e)}i(n.promise,r)}else(1===e._state?i:a)(n.promise,e._value)}))):e._deferreds.push(n)}function i(e,n){try{if(n===e)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if(n instanceof r)return e._state=3,e._value=n,void l(e);if("function"==typeof t)return void c((o=t,i=n,function(){o.apply(i,arguments)}),e)}e._state=1,e._value=n,l(e)}catch(n){a(e,n)}var o,i}function a(e,n){e._state=2,e._value=n,l(e)}function l(e){2===e._state&&0===e._deferreds.length&&r._immediateFn((function(){e._handled||r._unhandledRejectionFn(e._value)}));for(var n=0,t=e._deferreds.length;n<t;n++)o(e,e._deferreds[n]);e._deferreds=null}function f(e,n,t){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof n?n:null,this.promise=t}function c(e,n){var t=!1;try{e((function(e){t||(t=!0,i(n,e))}),(function(e){t||(t=!0,a(n,e))}))}catch(e){if(t)return;t=!0,a(n,e)}}r.prototype.catch=function(e){return this.then(null,e)},r.prototype.then=function(e,n){var r=new this.constructor(t);return o(this,new f(e,n,r)),r},r.prototype.finally=function(e){var n=this.constructor;return this.then((function(t){return n.resolve(e()).then((function(){return t}))}),(function(t){return n.resolve(e()).then((function(){return n.reject(t)}))}))},r.all=function(e){return new r((function(t,r){if(!n(e))return r(new TypeError("Promise.all accepts an array"));var o=Array.prototype.slice.call(e);if(0===o.length)return t([]);var i=o.length;function a(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var l=n.then;if("function"==typeof l)return void l.call(n,(function(n){a(e,n)}),r)}o[e]=n,0==--i&&t(o)}catch(e){r(e)}}for(var l=0;l<o.length;l++)a(l,o[l])}))},r.allSettled=function(e){return new this((function(n,t){if(!e||void 0===e.length)return t(new TypeError(typeof e+" "+e+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(e);if(0===r.length)return n([]);var o=r.length;function i(e,t){if(t&&("object"==typeof t||"function"==typeof t)){var a=t.then;if("function"==typeof a)return void a.call(t,(function(n){i(e,n)}),(function(t){r[e]={status:"rejected",reason:t},0==--o&&n(r)}))}r[e]={status:"fulfilled",value:t},0==--o&&n(r)}for(var a=0;a<r.length;a++)i(a,r[a])}))},r.resolve=function(e){return e&&"object"==typeof e&&e.constructor===r?e:new r((function(n){n(e)}))},r.reject=function(e){return new r((function(n,t){t(e)}))},r.race=function(e){return new r((function(t,o){if(!n(e))return o(new TypeError("Promise.race accepts an array"));for(var i=0,a=e.length;i<a;i++)r.resolve(e[i]).then(t,o)}))},r._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(n){e(n,0)},r._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},globalThis.Promise||(globalThis.Promise=r);var u=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;function y(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}var p=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var n={},t=0;t<10;t++)n["_"+String.fromCharCode(t)]=t;if("0123456789"!==Object.getOwnPropertyNames(n).map((function(e){return n[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(e){return!1}}()?Object.assign:function(e,n){for(var t,r,o=arguments,i=y(e),a=1;a<arguments.length;a++){for(var l in t=Object(o[a]))s.call(t,l)&&(i[l]=t[l]);if(u){r=u(t);for(var f=0;f<r.length;f++)h.call(t,r[f])&&(i[r[f]]=t[r[f]])}}return i};Object.assign||(Object.assign=p);if(Date.now&&Date.prototype.getTime||(Date.now=function(){return(new Date).getTime()}),!globalThis.performance||!globalThis.performance.now){var b=Date.now();globalThis.performance||(globalThis.performance={}),globalThis.performance.now=function(){return Date.now()-b}}for(var g=Date.now(),m=["ms","moz","webkit","o"],d=0;d<m.length&&!globalThis.requestAnimationFrame;++d){var v=m[d];globalThis.requestAnimationFrame=globalThis[v+"RequestAnimationFrame"],globalThis.cancelAnimationFrame=globalThis[v+"CancelAnimationFrame"]||globalThis[v+"CancelRequestAnimationFrame"]}globalThis.requestAnimationFrame||(globalThis.requestAnimationFrame=function(e){if("function"!=typeof e)throw new TypeError(e+"is not a function");var n=Date.now(),t=16+g-n;return t<0&&(t=0),g=n,globalThis.self.setTimeout((function(){g=Date.now(),e(performance.now())}),t)}),globalThis.cancelAnimationFrame||(globalThis.cancelAnimationFrame=function(e){return clearTimeout(e)}),Math.sign||(Math.sign=function(e){return 0===(e=Number(e))||isNaN(e)?e:e>0?1:-1}),Number.isInteger||(Number.isInteger=function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e}),globalThis.ArrayBuffer||(globalThis.ArrayBuffer=Array),globalThis.Float32Array||(globalThis.Float32Array=Array),globalThis.Uint32Array||(globalThis.Uint32Array=Array),globalThis.Uint16Array||(globalThis.Uint16Array=Array),globalThis.Uint8Array||(globalThis.Uint8Array=Array),globalThis.Int32Array||(globalThis.Int32Array=Array)}();
//# sourceMappingURL=polyfill.min.js.map
