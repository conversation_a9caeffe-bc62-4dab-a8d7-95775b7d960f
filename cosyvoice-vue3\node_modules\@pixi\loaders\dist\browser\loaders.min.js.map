{"version": 3, "file": "loaders.min.js", "sources": ["../../src/base/Signal.ts", "../../src/LoaderResource.ts", "../../src/base/parseUri.ts", "../../src/base/AsyncQueue.ts", "../../src/Loader.ts", "../../src/AppLoaderPlugin.ts", "../../src/TextureLoader.ts", "../../src/middleware/parsing.ts", "../../src/base/encodeBinary.ts", "../../src/ParsingLoader.ts", "../../src/index.ts"], "sourcesContent": ["/* jshint -W097 */\n\n/**\n * @memberof PIXI\n */\nexport class SignalBinding<CbType>\n{\n    _fn: any;\n    _once: boolean;\n    _next: SignalBinding<CbType>;\n    _prev: SignalBinding<CbType>;\n    _owner: Signal<CbType>;\n    _thisArg: any;\n\n    /**\n     * SignalBinding constructor.\n     * @constructs SignalBinding\n     * @param {Function} fn - Event handler to be called.\n     * @param {boolean} [once=false] - Should this listener be removed after dispatch\n     * @param {object} [thisArg] - The context of the callback function.\n     * @api private\n     */\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    constructor(fn: CbType, once = false, thisArg: any)\n    {\n        this._fn = fn;\n        this._once = once;\n        this._thisArg = thisArg;\n        this._next = this._prev = this._owner = null;\n    }\n\n    detach(): boolean\n    {\n        if (this._owner === null) return false;\n        this._owner.detach(this);\n\n        return true;\n    }\n}\n\n/**\n * @param self\n * @param node\n * @private\n */\nfunction _addSignalBinding<CbType>(self: Signal<CbType>, node: SignalBinding<CbType>)\n{\n    if (!self._head)\n    {\n        self._head = node;\n        self._tail = node;\n    }\n    else\n    {\n        self._tail._next = node;\n        node._prev = self._tail;\n        self._tail = node;\n    }\n\n    node._owner = self;\n\n    return node;\n}\n\n/**\n * @memberof PIXI\n */\nexport class Signal<CbType = (...args: any) => void>\n{\n    _head: SignalBinding<CbType>;\n    _tail: SignalBinding<CbType>;\n\n    /**\n     * MiniSignal constructor.\n     * @example\n     * let mySignal = new Signal();\n     * let binding = mySignal.add(onSignal);\n     * mySignal.dispatch('foo', 'bar');\n     * mySignal.detach(binding);\n     */\n    constructor()\n    {\n        this._head = this._tail = undefined;\n    }\n\n    /**\n     * Return an array of attached SignalBinding.\n     * @param {boolean} [exists=false] - We only need to know if there are handlers.\n     * @returns {PIXI.SignalBinding[] | boolean} Array of attached SignalBinding or Boolean if called with exists = true\n     * @api public\n     */\n    handlers(exists = false): Array<SignalBinding<CbType>> | boolean\n    {\n        let node = this._head;\n\n        if (exists) return !!node;\n\n        const ee = [];\n\n        while (node)\n        {\n            ee.push(node);\n            node = node._next;\n        }\n\n        return ee;\n    }\n\n    /**\n     * Return true if node is a SignalBinding attached to this MiniSignal\n     * @param {PIXI.SignalBinding} node - Node to check.\n     * @returns {boolean} True if node is attache to mini-signal\n     */\n    has(node: SignalBinding<CbType>): boolean\n    {\n        if (!(node instanceof SignalBinding))\n        {\n            throw new Error('MiniSignal#has(): First arg must be a SignalBinding object.');\n        }\n\n        return node._owner === this;\n    }\n\n    /**\n     * Dispaches a signal to all registered listeners.\n     * @param {...any} args\n     * @returns {boolean} Indication if we've emitted an event.\n     */\n    dispatch(...args: any[]): boolean\n    {\n        let node = this._head;\n\n        if (!node) return false;\n\n        while (node)\n        {\n            if (node._once) this.detach(node);\n            node._fn.apply(node._thisArg, args);\n            node = node._next;\n        }\n\n        return true;\n    }\n\n    /**\n     * Register a new listener.\n     * @param {Function} fn - Callback function.\n     * @param {object} [thisArg] - The context of the callback function.\n     * @returns {PIXI.SignalBinding} The SignalBinding node that was added.\n     */\n    add(fn: CbType, thisArg: any = null): SignalBinding<CbType>\n    {\n        if (typeof fn !== 'function')\n        {\n            throw new Error('MiniSignal#add(): First arg must be a Function.');\n        }\n\n        return _addSignalBinding<CbType>(this, new SignalBinding<CbType>(fn, false, thisArg));\n    }\n\n    /**\n     * Register a new listener that will be executed only once.\n     * @param {Function} fn - Callback function.\n     * @param {object} [thisArg] - The context of the callback function.\n     * @returns {PIXI.SignalBinding} The SignalBinding node that was added.\n     */\n    once(fn: CbType, thisArg: any = null): SignalBinding<CbType>\n    {\n        if (typeof fn !== 'function')\n        {\n            throw new Error('MiniSignal#once(): First arg must be a Function.');\n        }\n\n        return _addSignalBinding<CbType>(this, new SignalBinding<CbType>(fn, true, thisArg));\n    }\n\n    /**\n     * Remove binding object.\n     * @param {PIXI.SignalBinding} node - The binding node that will be removed.\n     * @returns {Signal} The instance on which this method was called.\n      @api public */\n    detach(node: SignalBinding<CbType>): this\n    {\n        if (!(node instanceof SignalBinding))\n        {\n            throw new Error('MiniSignal#detach(): First arg must be a SignalBinding object.');\n        }\n        if (node._owner !== this) return this; // todo: or error?\n\n        if (node._prev) node._prev._next = node._next;\n        if (node._next) node._next._prev = node._prev;\n\n        if (node === this._head)\n        { // first node\n            this._head = node._next;\n            if (node._next === null)\n            {\n                this._tail = null;\n            }\n        }\n        else if (node === this._tail)\n        { // last node\n            this._tail = node._prev;\n            this._tail._next = null;\n        }\n\n        node._owner = null;\n\n        return this;\n    }\n\n    /**\n     * Detach all listeners.\n     * @returns {Signal} The instance on which this method was called.\n     */\n    detachAll(): this\n    {\n        let node = this._head;\n\n        if (!node) return this;\n\n        this._head = this._tail = null;\n\n        while (node)\n        {\n            node._owner = null;\n            node = node._next;\n        }\n\n        return this;\n    }\n}\n", "import type { Dict } from '@pixi/utils';\nimport { Signal } from './base/Signal';\nimport { parseUri } from './base/parseUri';\nimport type { IBaseTextureOptions, Texture } from '@pixi/core';\n\n// tests if CORS is supported in XHR, if not we need to use XDR\nlet useXdr: boolean;\nlet tempAnchor: any = null;\n\n// some status constants\nconst STATUS_NONE = 0;\nconst STATUS_OK = 200;\nconst STATUS_EMPTY = 204;\nconst STATUS_IE_BUG_EMPTY = 1223;\nconst STATUS_TYPE_OK = 2;\n\n// noop\nfunction _noop(): void { /* empty */ }\n\n/**\n * Quick helper to set a value on one of the extension maps. Ensures there is no\n * dot at the start of the extension.\n * @ignore\n * @param map - The map to set on.\n * @param extname - The extension (or key) to set.\n * @param val - The value to set.\n */\nfunction setExtMap(map: Dict<any>, extname: string, val: number)\n{\n    if (extname && extname.indexOf('.') === 0)\n    {\n        extname = extname.substring(1);\n    }\n\n    if (!extname)\n    {\n        return;\n    }\n\n    map[extname] = val;\n}\n\n/**\n * Quick helper to get string xhr type.\n * @ignore\n * @param xhr - The request to check.\n * @returns The type.\n */\nfunction reqType(xhr: XMLHttpRequest)\n{\n    return xhr.toString().replace('object ', '');\n}\n\n/**\n * Metadata for loader resource. It is very messy way to pass options for loader middlewares\n *\n * Can be extended in `GlobalMixins.IResourceMetadata`\n * @memberof PIXI\n */\nexport interface IResourceMetadata extends GlobalMixins.IResourceMetadata, IBaseTextureOptions\n{\n    /** The element to use for loading, instead of creating one. */\n    loadElement?: HTMLImageElement | HTMLAudioElement | HTMLVideoElement;\n    /**\n     * Skips adding source(s) to the load element. This\n     * is useful if you want to pass in a `loadElement` that you already added load sources to.\n     */\n    skipSource?: boolean;\n    /**\n     * The mime type to use for the source element\n     * of a video/audio elment. If the urls are an array, you can pass this as an array as well\n     * where each index is the mime type to use for the corresponding url index.\n     */\n    mimeType?: string | string[];\n\n    /**\n     * Used by BitmapFonts, Spritesheet and CompressedTextures as the options to used for\n     * metadata when loading the child image.\n     */\n    imageMetadata?: IResourceMetadata;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\ninterface LoaderResource extends GlobalMixins.LoaderResource, GlobalMixins.ILoaderResource {}\n\n/**\n * Manages the state and loading of a resource and all child resources.\n *\n * Can be extended in `GlobalMixins.LoaderResource`.\n * @memberof PIXI\n */\nclass LoaderResource\n{\n    /**\n     * Texture reference for loading images and other textures.\n     * @type {PIXI.Texture}\n     */\n    texture?: Texture;\n\n    /** used by parsing middleware */\n    blob?: Blob;\n\n    /**\n     * The name of this resource.\n     * @readonly\n     * @type {string}\n     */\n    readonly name: string;\n    /**\n     * The url used to load this resource.\n     * @readonly\n     * @type {string}\n     */\n    readonly url: string;\n    /**\n     * The extension used to load this resource.\n     * @readonly\n     * @type {string}\n     */\n    readonly extension: string;\n    /** The data that was loaded by the resource. */\n    data: any;\n    /** Is this request cross-origin? If unset, determined automatically. */\n    crossOrigin: string | boolean;\n    /**\n     * A timeout in milliseconds for the load. If the load takes longer than this time\n     * it is cancelled and the load is considered a failure. If this value is set to `0`\n     * then there is no explicit timeout.\n     * @type {number}\n     */\n    timeout: number;\n    /**\n     * The method of loading to use for this resource.\n     * @type {PIXI.LoaderResource.LOAD_TYPE}\n     */\n    loadType: LoaderResource.LOAD_TYPE;\n    /**\n     * The type used to load the resource via XHR. If unset, determined automatically.\n     * @member {string}\n     */\n    xhrType: string;\n\n    /**\n     * Extra info for middleware, and controlling specifics about how the resource loads.\n     *\n     * Note that if you pass in a `loadElement`, the Resource class takes ownership of it.\n     * Meaning it will modify it as it sees fit.\n     * @type {PIXI.IResourceMetadata}\n     */\n    metadata: IResourceMetadata;\n    /**\n     * The error that occurred while loading (if any).\n     * @readonly\n     * @member {Error}\n     */\n    error: Error;\n    /**\n     * The XHR object that was used to load this resource. This is only set\n     * when `loadType` is `LoaderResource.LOAD_TYPE.XHR`.\n     * @readonly\n     */\n    xhr: XMLHttpRequest;\n\n    private xdr: any;\n    /**\n     * The child resources this resource owns.\n     * @type {PIXI.LoaderResource[]}\n     */\n    readonly children: LoaderResource[];\n    /**\n     * The resource type.\n     * @readonly\n     * @type {PIXI.LoaderResource.TYPE}\n     */\n    type: LoaderResource.TYPE;\n    /**\n     * The progress chunk owned by this resource.\n     * @readonly\n     * @member {number}\n     */\n    progressChunk: number;\n    /**\n     * Dispatched when the resource beings to load.\n     *\n     * The callback looks like {@link LoaderResource.OnStartSignal}.\n     * @type {PIXI.Signal}\n     */\n    onStart: Signal<LoaderResource.OnStartSignal>;\n    /**\n     * Dispatched each time progress of this resource load updates.\n     * Not all resources types and loader systems can support this event\n     * so sometimes it may not be available. If the resource\n     * is being loaded on a modern browser, using XHR, and the remote server\n     * properly sets Content-Length headers, then this will be available.\n     *\n     * The callback looks like {@link LoaderResource.OnProgressSignal}.\n     * @type {PIXI.Signal}\n     */\n    onProgress: Signal<LoaderResource.OnProgressSignal>;\n    /**\n     * Dispatched once this resource has loaded, if there was an error it will\n     * be in the `error` property.\n     *\n     * The callback looks like {@link LoaderResource.OnCompleteSignal}.\n     * @type {PIXI.Signal}\n     */\n    onComplete: Signal<LoaderResource.OnCompleteSignal>;\n    /**\n     * Dispatched after this resource has had all the *after* middleware run on it.\n     *\n     * The callback looks like {@link LoaderResource.OnCompleteSignal}.\n     * @type {PIXI.Signal}\n     */\n    onAfterMiddleware: Signal<LoaderResource.OnCompleteSignal>;\n\n    /**\n     * The state flags of this resource.\n     * @private\n     * @member {number}\n     */\n    private _flags: number;\n\n    /**\n     * The `dequeue` method that will be used a storage place for the async queue dequeue method\n     * used privately by the loader.\n     * @private\n     * @member {Function}\n     */\n    _dequeue: any = _noop;\n\n    /**\n     * Used a storage place for the on load binding used privately by the loader.\n     * @private\n     * @member {Function}\n     */\n    _onLoadBinding: any = null;\n\n    /**\n     * The timer for element loads to check if they timeout.\n     * @private\n     */\n    private _elementTimer = 0;\n\n    /**\n     * The `complete` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundComplete: any = null;\n\n    /**\n     * The `_onError` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundOnError: any = null;\n\n    /**\n     * The `_onProgress` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundOnProgress: any = null;\n\n    /**\n     * The `_onTimeout` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundOnTimeout: any = null;\n\n    private _boundXhrOnError: any = null;\n    private _boundXhrOnTimeout: any = null;\n    private _boundXhrOnAbort: any = null;\n    private _boundXhrOnLoad: any = null;\n\n    /**\n     * Sets the load type to be used for a specific extension.\n     * @static\n     * @param {string} extname - The extension to set the type for, e.g. \"png\" or \"fnt\"\n     * @param {PIXI.LoaderResource.LOAD_TYPE} loadType - The load type to set it to.\n     */\n    static setExtensionLoadType(extname: string, loadType: LoaderResource.LOAD_TYPE): void\n    {\n        setExtMap(LoaderResource._loadTypeMap, extname, loadType);\n    }\n    /**\n     * Sets the load type to be used for a specific extension.\n     * @static\n     * @param {string} extname - The extension to set the type for, e.g. \"png\" or \"fnt\"\n     * @param {PIXI.LoaderResource.XHR_RESPONSE_TYPE} xhrType - The xhr type to set it to.\n     */\n    static setExtensionXhrType(extname: string, xhrType: LoaderResource.XHR_RESPONSE_TYPE): void\n    {\n        setExtMap(LoaderResource._xhrTypeMap, extname, xhrType as any);\n    }\n\n    /**\n     * @param {string} name - The name of the resource to load.\n     * @param {string|string[]} url - The url for this resource, for audio/video loads you can pass\n     *      an array of sources.\n     * @param {object} [options] - The options for the load.\n     * @param {string|boolean} [options.crossOrigin] - Is this request cross-origin? Default is to\n     *      determine automatically.\n     * @param {number} [options.timeout=0] - A timeout in milliseconds for the load. If the load takes\n     *      longer than this time it is cancelled and the load is considered a failure. If this value is\n     *      set to `0` then there is no explicit timeout.\n     * @param {PIXI.LoaderResource.LOAD_TYPE} [options.loadType=LOAD_TYPE.XHR] - How should this resource\n     *      be loaded?\n     * @param {PIXI.LoaderResource.XHR_RESPONSE_TYPE} [options.xhrType=XHR_RESPONSE_TYPE.DEFAULT] - How\n     *      should the data being loaded be interpreted when using XHR?\n     * @param {PIXI.LoaderResource.IMetadata} [options.metadata] - Extra configuration for middleware\n     *      and the Resource object.\n     */\n    constructor(name: string, url: string | string[], options?: {\n        crossOrigin?: string | boolean;\n        timeout?: number;\n        loadType?: LoaderResource.LOAD_TYPE;\n        xhrType?: LoaderResource.XHR_RESPONSE_TYPE;\n        metadata?: IResourceMetadata;\n    })\n    {\n        if (typeof name !== 'string' || typeof url !== 'string')\n        {\n            throw new Error('Both name and url are required for constructing a resource.');\n        }\n\n        options = options || {};\n\n        this._flags = 0;\n\n        // set data url flag, needs to be set early for some _determineX checks to work.\n        this._setFlag(LoaderResource.STATUS_FLAGS.DATA_URL, url.indexOf('data:') === 0);\n\n        this.name = name;\n\n        this.url = url;\n\n        this.extension = this._getExtension();\n\n        this.data = null;\n\n        this.crossOrigin = options.crossOrigin === true ? 'anonymous' : options.crossOrigin;\n\n        this.timeout = options.timeout || 0;\n\n        this.loadType = options.loadType || this._determineLoadType();\n\n        // The type used to load the resource via XHR. If unset, determined automatically.\n        this.xhrType = options.xhrType;\n\n        // Extra info for middleware, and controlling specifics about how the resource loads.\n        // Note that if you pass in a `loadElement`, the Resource class takes ownership of it.\n        // Meaning it will modify it as it sees fit.\n        this.metadata = options.metadata || {};\n\n        // The error that occurred while loading (if any).\n        this.error = null;\n\n        // The XHR object that was used to load this resource. This is only set\n        // when `loadType` is `LoaderResource.LOAD_TYPE.XHR`.\n        this.xhr = null;\n\n        // The child resources this resource owns.\n        this.children = [];\n\n        // The resource type.\n        this.type = LoaderResource.TYPE.UNKNOWN;\n\n        // The progress chunk owned by this resource.\n        this.progressChunk = 0;\n\n        // The `dequeue` method that will be used a storage place for the async queue dequeue method\n        // used privately by the loader.\n        this._dequeue = _noop;\n\n        // Used a storage place for the on load binding used privately by the loader.\n        this._onLoadBinding = null;\n\n        // The timer for element loads to check if they timeout.\n        this._elementTimer = 0;\n\n        this._boundComplete = this.complete.bind(this);\n        this._boundOnError = this._onError.bind(this);\n        this._boundOnProgress = this._onProgress.bind(this);\n        this._boundOnTimeout = this._onTimeout.bind(this);\n\n        // xhr callbacks\n        this._boundXhrOnError = this._xhrOnError.bind(this);\n        this._boundXhrOnTimeout = this._xhrOnTimeout.bind(this);\n        this._boundXhrOnAbort = this._xhrOnAbort.bind(this);\n        this._boundXhrOnLoad = this._xhrOnLoad.bind(this);\n\n        // Dispatched when the resource beings to load.\n        this.onStart = new Signal();\n\n        // Dispatched each time progress of this resource load updates.\n        // Not all resources types and loader systems can support this event\n        // so sometimes it may not be available. If the resource\n        // is being loaded on a modern browser, using XHR, and the remote server\n        // properly sets Content-Length headers, then this will be available.\n        this.onProgress = new Signal();\n\n        // Dispatched once this resource has loaded, if there was an error it will\n        // be in the `error` property.\n        this.onComplete = new Signal();\n\n        // Dispatched after this resource has had all the *after* middleware run on it.\n        this.onAfterMiddleware = new Signal();\n    }\n\n    /**\n     * When the resource starts to load.\n     * @memberof PIXI.LoaderResource\n     * @callback OnStartSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n\n    /**\n     * When the resource reports loading progress.\n     * @memberof PIXI.LoaderResource\n     * @callback OnProgressSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     * @param {number} percentage - The progress of the load in the range [0, 1].\n     */\n\n    /**\n     * When the resource finishes loading.\n     * @memberof PIXI.LoaderResource\n     * @callback OnCompleteSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n\n    /**\n     * @memberof PIXI.LoaderResource\n     * @typedef {object} IMetadata\n     * @property {HTMLImageElement|HTMLAudioElement|HTMLVideoElement} [loadElement=null] - The\n     *      element to use for loading, instead of creating one.\n     * @property {boolean} [skipSource=false] - Skips adding source(s) to the load element. This\n     *      is useful if you want to pass in a `loadElement` that you already added load sources to.\n     * @property {string|string[]} [mimeType] - The mime type to use for the source element\n     *      of a video/audio elment. If the urls are an array, you can pass this as an array as well\n     *      where each index is the mime type to use for the corresponding url index.\n     */\n\n    /**\n     * Stores whether or not this url is a data url.\n     * @readonly\n     * @member {boolean}\n     */\n    get isDataUrl(): boolean\n    {\n        return this._hasFlag(LoaderResource.STATUS_FLAGS.DATA_URL);\n    }\n\n    /**\n     * Describes if this resource has finished loading. Is true when the resource has completely\n     * loaded.\n     * @readonly\n     * @member {boolean}\n     */\n    get isComplete(): boolean\n    {\n        return this._hasFlag(LoaderResource.STATUS_FLAGS.COMPLETE);\n    }\n\n    /**\n     * Describes if this resource is currently loading. Is true when the resource starts loading,\n     * and is false again when complete.\n     * @readonly\n     * @member {boolean}\n     */\n    get isLoading(): boolean\n    {\n        return this._hasFlag(LoaderResource.STATUS_FLAGS.LOADING);\n    }\n\n    /** Marks the resource as complete. */\n    complete(): void\n    {\n        this._clearEvents();\n        this._finish();\n    }\n\n    /**\n     * Aborts the loading of this resource, with an optional message.\n     * @param {string} message - The message to use for the error\n     */\n    abort(message: string): void\n    {\n        // abort can be called multiple times, ignore subsequent calls.\n        if (this.error)\n        {\n            return;\n        }\n\n        // store error\n        this.error = new Error(message);\n\n        // clear events before calling aborts\n        this._clearEvents();\n\n        // abort the actual loading\n        if (this.xhr)\n        {\n            this.xhr.abort();\n        }\n        else if (this.xdr)\n        {\n            this.xdr.abort();\n        }\n        else if (this.data)\n        {\n            // single source\n            if (this.data.src)\n            {\n                this.data.src = LoaderResource.EMPTY_GIF;\n            }\n            // multi-source\n            else\n            {\n                while (this.data.firstChild)\n                {\n                    this.data.removeChild(this.data.firstChild);\n                }\n            }\n        }\n\n        // done now.\n        this._finish();\n    }\n\n    /**\n     * Kicks off loading of this resource. This method is asynchronous.\n     * @param {PIXI.LoaderResource.OnCompleteSignal} [cb] - Optional callback to call once the resource is loaded.\n     */\n    load(cb?: LoaderResource.OnCompleteSignal): void\n    {\n        if (this.isLoading)\n        {\n            return;\n        }\n\n        if (this.isComplete)\n        {\n            if (cb)\n            {\n                setTimeout(() => cb(this), 1);\n            }\n\n            return;\n        }\n        else if (cb)\n        {\n            this.onComplete.once(cb);\n        }\n\n        this._setFlag(LoaderResource.STATUS_FLAGS.LOADING, true);\n\n        this.onStart.dispatch(this);\n\n        // if unset, determine the value\n        if (this.crossOrigin === false || typeof this.crossOrigin !== 'string')\n        {\n            this.crossOrigin = this._determineCrossOrigin(this.url);\n        }\n\n        switch (this.loadType)\n        {\n            case LoaderResource.LOAD_TYPE.IMAGE:\n                this.type = LoaderResource.TYPE.IMAGE;\n                this._loadElement('image');\n                break;\n\n            case LoaderResource.LOAD_TYPE.AUDIO:\n                this.type = LoaderResource.TYPE.AUDIO;\n                this._loadSourceElement('audio');\n                break;\n\n            case LoaderResource.LOAD_TYPE.VIDEO:\n                this.type = LoaderResource.TYPE.VIDEO;\n                this._loadSourceElement('video');\n                break;\n\n            case LoaderResource.LOAD_TYPE.XHR:\n            /* falls through */\n            default:\n                if (typeof useXdr === 'undefined')\n                {\n                    useXdr = !!((globalThis as any).XDomainRequest && !('withCredentials' in (new XMLHttpRequest())));\n                }\n                if (useXdr && this.crossOrigin)\n                {\n                    this._loadXdr();\n                }\n                else\n                {\n                    this._loadXhr();\n                }\n                break;\n        }\n    }\n\n    /**\n     * Checks if the flag is set.\n     * @param flag - The flag to check.\n     * @returns True if the flag is set.\n     */\n    private _hasFlag(flag: number): boolean\n    {\n        return (this._flags & flag) !== 0;\n    }\n\n    /**\n     * (Un)Sets the flag.\n     * @param flag - The flag to (un)set.\n     * @param value - Whether to set or (un)set the flag.\n     */\n    private _setFlag(flag: number, value: boolean): void\n    {\n        this._flags = value ? (this._flags | flag) : (this._flags & ~flag);\n    }\n\n    /** Clears all the events from the underlying loading source. */\n    private _clearEvents(): void\n    {\n        clearTimeout(this._elementTimer);\n\n        if (this.data && this.data.removeEventListener)\n        {\n            this.data.removeEventListener('error', this._boundOnError, false);\n            this.data.removeEventListener('load', this._boundComplete, false);\n            this.data.removeEventListener('progress', this._boundOnProgress, false);\n            this.data.removeEventListener('canplaythrough', this._boundComplete, false);\n        }\n\n        if (this.xhr)\n        {\n            if (this.xhr.removeEventListener)\n            {\n                this.xhr.removeEventListener('error', this._boundXhrOnError, false);\n                this.xhr.removeEventListener('timeout', this._boundXhrOnTimeout, false);\n                this.xhr.removeEventListener('abort', this._boundXhrOnAbort, false);\n                this.xhr.removeEventListener('progress', this._boundOnProgress, false);\n                this.xhr.removeEventListener('load', this._boundXhrOnLoad, false);\n            }\n            else\n            {\n                this.xhr.onerror = null;\n                this.xhr.ontimeout = null;\n                this.xhr.onprogress = null;\n                this.xhr.onload = null;\n            }\n        }\n    }\n\n    /** Finalizes the load. */\n    private _finish(): void\n    {\n        if (this.isComplete)\n        {\n            throw new Error('Complete called again for an already completed resource.');\n        }\n\n        this._setFlag(LoaderResource.STATUS_FLAGS.COMPLETE, true);\n        this._setFlag(LoaderResource.STATUS_FLAGS.LOADING, false);\n\n        this.onComplete.dispatch(this);\n    }\n\n    /**\n     * Loads this resources using an element that has a single source,\n     * like an HTMLImageElement.\n     * @private\n     * @param type - The type of element to use.\n     */\n    _loadElement(type: string): void\n    {\n        if (this.metadata.loadElement)\n        {\n            this.data = this.metadata.loadElement;\n        }\n        else if (type === 'image' && typeof globalThis.Image !== 'undefined')\n        {\n            this.data = new Image();\n        }\n        else\n        {\n            this.data = document.createElement(type);\n        }\n\n        if (this.crossOrigin)\n        {\n            this.data.crossOrigin = this.crossOrigin;\n        }\n\n        if (!this.metadata.skipSource)\n        {\n            this.data.src = this.url;\n        }\n\n        this.data.addEventListener('error', this._boundOnError, false);\n        this.data.addEventListener('load', this._boundComplete, false);\n        this.data.addEventListener('progress', this._boundOnProgress, false);\n\n        if (this.timeout)\n        {\n            this._elementTimer = setTimeout(this._boundOnTimeout, this.timeout) as any;\n        }\n    }\n\n    /**\n     * Loads this resources using an element that has multiple sources,\n     * like an HTMLAudioElement or HTMLVideoElement.\n     * @param type - The type of element to use.\n     */\n    private _loadSourceElement(type: string): void\n    {\n        if (this.metadata.loadElement)\n        {\n            this.data = this.metadata.loadElement;\n        }\n        else if (type === 'audio' && typeof globalThis.Audio !== 'undefined')\n        {\n            this.data = new Audio();\n        }\n        else\n        {\n            this.data = document.createElement(type);\n        }\n\n        if (this.data === null)\n        {\n            this.abort(`Unsupported element: ${type}`);\n\n            return;\n        }\n\n        if (this.crossOrigin)\n        {\n            this.data.crossOrigin = this.crossOrigin;\n        }\n\n        if (!this.metadata.skipSource)\n        {\n            // support for CocoonJS Canvas+ runtime, lacks document.createElement('source')\n            if ((navigator as any).isCocoonJS)\n            {\n                this.data.src = Array.isArray(this.url) ? this.url[0] : this.url;\n            }\n            else if (Array.isArray(this.url))\n            {\n                const mimeTypes = this.metadata.mimeType;\n\n                for (let i = 0; i < this.url.length; ++i)\n                {\n                    this.data.appendChild(\n                        this._createSource(type, this.url[i], Array.isArray(mimeTypes) ? mimeTypes[i] : mimeTypes)\n                    );\n                }\n            }\n            else\n            {\n                const mimeTypes = this.metadata.mimeType;\n\n                this.data.appendChild(\n                    this._createSource(type, this.url, Array.isArray(mimeTypes) ? mimeTypes[0] : mimeTypes)\n                );\n            }\n        }\n\n        this.data.addEventListener('error', this._boundOnError, false);\n        this.data.addEventListener('load', this._boundComplete, false);\n        this.data.addEventListener('progress', this._boundOnProgress, false);\n        this.data.addEventListener('canplaythrough', this._boundComplete, false);\n\n        this.data.load();\n\n        if (this.timeout)\n        {\n            this._elementTimer = setTimeout(this._boundOnTimeout, this.timeout) as any;\n        }\n    }\n\n    /** Loads this resources using an XMLHttpRequest. */\n    private _loadXhr(): void\n    {\n        // if unset, determine the value\n        if (typeof this.xhrType !== 'string')\n        {\n            this.xhrType = this._determineXhrType();\n        }\n\n        const xhr = this.xhr = new XMLHttpRequest();\n\n        // send credentials when crossOrigin with credentials requested\n        if (this.crossOrigin === 'use-credentials')\n        {\n            xhr.withCredentials = true;\n        }\n\n        // set the request type and url\n        xhr.open('GET', this.url, true);\n\n        xhr.timeout = this.timeout;\n\n        // load json as text and parse it ourselves. We do this because some browsers\n        // *cough* safari *cough* can't deal with it.\n        if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.JSON\n            || this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT)\n        {\n            xhr.responseType = LoaderResource.XHR_RESPONSE_TYPE.TEXT;\n        }\n        else\n        {\n            xhr.responseType = this.xhrType as any;\n        }\n\n        xhr.addEventListener('error', this._boundXhrOnError, false);\n        xhr.addEventListener('timeout', this._boundXhrOnTimeout, false);\n        xhr.addEventListener('abort', this._boundXhrOnAbort, false);\n        xhr.addEventListener('progress', this._boundOnProgress, false);\n        xhr.addEventListener('load', this._boundXhrOnLoad, false);\n\n        xhr.send();\n    }\n\n    /** Loads this resources using an XDomainRequest. This is here because we need to support IE9 (gross). */\n    private _loadXdr(): void\n    {\n        // if unset, determine the value\n        if (typeof this.xhrType !== 'string')\n        {\n            this.xhrType = this._determineXhrType();\n        }\n\n        const xdr = this.xhr = new (globalThis as any).XDomainRequest(); // eslint-disable-line no-undef\n\n        // XDomainRequest has a few quirks. Occasionally it will abort requests\n        // A way to avoid this is to make sure ALL callbacks are set even if not used\n        // More info here: http://stackoverflow.com/questions/15786966/xdomainrequest-aborts-post-on-ie-9\n        xdr.timeout = this.timeout || 5000; // XDR needs a timeout value or it breaks in IE9\n\n        xdr.onerror = this._boundXhrOnError;\n        xdr.ontimeout = this._boundXhrOnTimeout;\n        xdr.onprogress = this._boundOnProgress;\n        xdr.onload = this._boundXhrOnLoad;\n\n        xdr.open('GET', this.url, true);\n\n        // Note: The xdr.send() call is wrapped in a timeout to prevent an\n        // issue with the interface where some requests are lost if multiple\n        // XDomainRequests are being sent at the same time.\n        // Some info here: https://github.com/photonstorm/phaser/issues/1248\n        setTimeout(() => xdr.send(), 1);\n    }\n\n    /**\n     * Creates a source used in loading via an element.\n     * @param type - The element type (video or audio).\n     * @param url - The source URL to load from.\n     * @param [mime] - The mime type of the video\n     * @returns The source element.\n     */\n    private _createSource(type: string, url: string, mime: string): HTMLSourceElement\n    {\n        if (!mime)\n        {\n            mime = `${type}/${this._getExtension(url)}`;\n        }\n\n        const source = document.createElement('source');\n\n        source.src = url;\n        source.type = mime;\n\n        return source;\n    }\n\n    /**\n     * Called if a load errors out.\n     * @param event - The error event from the element that emits it.\n     */\n    private _onError(event: Event): void\n    {\n        this.abort(`Failed to load element using: ${(event.target as any).nodeName}`);\n    }\n\n    /**\n     * Called if a load progress event fires for an element or xhr/xdr.\n     * @param event - Progress event.\n     */\n    private _onProgress(event: ProgressEvent): void\n    {\n        if (event && event.lengthComputable)\n        {\n            this.onProgress.dispatch(this, event.loaded / event.total);\n        }\n    }\n\n    /** Called if a timeout event fires for an element. */\n    private _onTimeout(): void\n    {\n        this.abort(`Load timed out.`);\n    }\n\n    /** Called if an error event fires for xhr/xdr. */\n    private _xhrOnError(): void\n    {\n        const xhr = this.xhr;\n\n        this.abort(`${reqType(xhr)} Request failed. Status: ${xhr.status}, text: \"${xhr.statusText}\"`);\n    }\n\n    /** Called if an error event fires for xhr/xdr. */\n    private _xhrOnTimeout(): void\n    {\n        const xhr = this.xhr;\n\n        this.abort(`${reqType(xhr)} Request timed out.`);\n    }\n\n    /** Called if an abort event fires for xhr/xdr. */\n    private _xhrOnAbort(): void\n    {\n        const xhr = this.xhr;\n\n        this.abort(`${reqType(xhr)} Request was aborted by the user.`);\n    }\n\n    /** Called when data successfully loads from an xhr/xdr request. */\n    private _xhrOnLoad(): void\n    {\n        const xhr = this.xhr;\n        let text = '';\n        let status = typeof xhr.status === 'undefined' ? STATUS_OK : xhr.status; // XDR has no `.status`, assume 200.\n\n        // responseText is accessible only if responseType is '' or 'text' and on older browsers\n        if (xhr.responseType === '' || xhr.responseType === 'text' || typeof xhr.responseType === 'undefined')\n        {\n            text = xhr.responseText;\n        }\n\n        // status can be 0 when using the `file://` protocol so we also check if a response is set.\n        // If it has a response, we assume 200; otherwise a 0 status code with no contents is an aborted request.\n        if (status === STATUS_NONE && (text.length > 0 || xhr.responseType === LoaderResource.XHR_RESPONSE_TYPE.BUFFER))\n        {\n            status = STATUS_OK;\n        }\n        // handle IE9 bug: http://stackoverflow.com/questions/10046972/msie-returns-status-code-of-1223-for-ajax-request\n        else if (status === STATUS_IE_BUG_EMPTY)\n        {\n            status = STATUS_EMPTY;\n        }\n\n        const statusType = (status / 100) | 0;\n\n        if (statusType === STATUS_TYPE_OK)\n        {\n            // if text, just return it\n            if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.TEXT)\n            {\n                this.data = text;\n                this.type = LoaderResource.TYPE.TEXT;\n            }\n            // if json, parse into json object\n            else if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.JSON)\n            {\n                try\n                {\n                    this.data = JSON.parse(text);\n                    this.type = LoaderResource.TYPE.JSON;\n                }\n                catch (e)\n                {\n                    this.abort(`Error trying to parse loaded json: ${e}`);\n\n                    return;\n                }\n            }\n            // if xml, parse into an xml document or div element\n            else if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT)\n            {\n                try\n                {\n                    if (globalThis.DOMParser)\n                    {\n                        const domparser = new DOMParser();\n\n                        this.data = domparser.parseFromString(text, 'text/xml');\n                    }\n                    else\n                    {\n                        const div = document.createElement('div');\n\n                        div.innerHTML = text;\n\n                        this.data = div;\n                    }\n\n                    this.type = LoaderResource.TYPE.XML;\n                }\n                catch (e)\n                {\n                    this.abort(`Error trying to parse loaded xml: ${e}`);\n\n                    return;\n                }\n            }\n            // other types just return the response\n            else\n            {\n                this.data = xhr.response || text;\n            }\n        }\n        else\n        {\n            this.abort(`[${xhr.status}] ${xhr.statusText}: ${xhr.responseURL}`);\n\n            return;\n        }\n\n        this.complete();\n    }\n\n    /**\n     * Sets the `crossOrigin` property for this resource based on if the url\n     * for this resource is cross-origin. If crossOrigin was manually set, this\n     * function does nothing.\n     * @private\n     * @param url - The url to test.\n     * @param [loc=globalThis.location] - The location object to test against.\n     * @returns The crossOrigin value to use (or empty string for none).\n     */\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    _determineCrossOrigin(url: string, loc?: any): string\n    {\n        // data: and javascript: urls are considered same-origin\n        if (url.indexOf('data:') === 0)\n        {\n            return '';\n        }\n\n        // A sandboxed iframe without the 'allow-same-origin' attribute will have a special\n        // origin designed not to match globalThis.location.origin, and will always require\n        // crossOrigin requests regardless of whether the location matches.\n        if (globalThis.origin !== globalThis.location.origin)\n        {\n            return 'anonymous';\n        }\n\n        // default is globalThis.location\n        loc = loc || globalThis.location;\n\n        if (!tempAnchor)\n        {\n            tempAnchor = document.createElement('a');\n        }\n\n        // let the browser determine the full href for the url of this resource and then\n        // parse with the node url lib, we can't use the properties of the anchor element\n        // because they don't work in IE9 :(\n        tempAnchor.href = url;\n        const parsedUrl = parseUri(tempAnchor.href, { strictMode: true });\n\n        const samePort = (!parsedUrl.port && loc.port === '') || (parsedUrl.port === loc.port);\n        const protocol = parsedUrl.protocol ? `${parsedUrl.protocol}:` : '';\n\n        // if cross origin\n        if (parsedUrl.host !== loc.hostname || !samePort || protocol !== loc.protocol)\n        {\n            return 'anonymous';\n        }\n\n        return '';\n    }\n\n    /**\n     * Determines the responseType of an XHR request based on the extension of the\n     * resource being loaded.\n     * @private\n     * @returns {PIXI.LoaderResource.XHR_RESPONSE_TYPE} The responseType to use.\n     */\n    private _determineXhrType(): LoaderResource.XHR_RESPONSE_TYPE\n    {\n        return LoaderResource._xhrTypeMap[this.extension] || LoaderResource.XHR_RESPONSE_TYPE.TEXT;\n    }\n\n    /**\n     * Determines the loadType of a resource based on the extension of the\n     * resource being loaded.\n     * @private\n     * @returns {PIXI.LoaderResource.LOAD_TYPE} The loadType to use.\n     */\n    private _determineLoadType(): LoaderResource.LOAD_TYPE\n    {\n        return LoaderResource._loadTypeMap[this.extension] || LoaderResource.LOAD_TYPE.XHR;\n    }\n\n    /**\n     * Extracts the extension (sans '.') of the file being loaded by the resource.\n     * @param [url] - url to parse, `this.url` by default.\n     * @returns The extension.\n     */\n    private _getExtension(url = this.url): string\n    {\n        let ext = '';\n\n        if (this.isDataUrl)\n        {\n            const slashIndex = url.indexOf('/');\n\n            ext = url.substring(slashIndex + 1, url.indexOf(';', slashIndex));\n        }\n        else\n        {\n            const queryStart = url.indexOf('?');\n            const hashStart = url.indexOf('#');\n            const index = Math.min(\n                queryStart > -1 ? queryStart : url.length,\n                hashStart > -1 ? hashStart : url.length\n            );\n\n            url = url.substring(0, index);\n            ext = url.substring(url.lastIndexOf('.') + 1);\n        }\n\n        return ext.toLowerCase();\n    }\n\n    /**\n     * Determines the mime type of an XHR request based on the responseType of\n     * resource being loaded.\n     * @param type - The type to get a mime type for.\n     * @private\n     * @returns The mime type to use.\n     */\n    _getMimeFromXhrType(type: LoaderResource.XHR_RESPONSE_TYPE): string\n    {\n        switch (type)\n        {\n            case LoaderResource.XHR_RESPONSE_TYPE.BUFFER:\n                return 'application/octet-binary';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.BLOB:\n                return 'application/blob';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT:\n                return 'application/xml';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.JSON:\n                return 'application/json';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.DEFAULT:\n            case LoaderResource.XHR_RESPONSE_TYPE.TEXT:\n            /* falls through */\n            default:\n                return 'text/plain';\n        }\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace LoaderResource\n{\n    /**\n     * When the resource starts to load.\n     * @memberof PIXI.LoaderResource\n     * @callback OnStartSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n    export type OnStartSignal = (resource: LoaderResource) => void;\n    /**\n     * When the resource reports loading progress.\n     * @memberof PIXI.LoaderResource\n     * @callback OnProgressSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     * @param {number} percentage - The progress of the load in the range [0, 1].\n     */\n    export type OnProgressSignal = (resource: LoaderResource, percentage: number) => void;\n    /**\n     * When the resource finishes loading.\n     * @memberof PIXI.LoaderResource\n     * @callback OnCompleteSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n    export type OnCompleteSignal = (resource: LoaderResource) => void;\n\n    /**\n     * The types of resources a resource could represent.\n     * @static\n     * @readonly\n     * @enum {number}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum STATUS_FLAGS\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** None */\n        NONE = 0,\n        /** Data URL */\n        DATA_URL = (1 << 0),\n        /** Complete */\n        COMPLETE = (1 << 1),\n        /** Loading */\n        LOADING = (1 << 2),\n    }\n\n    /**\n     * The types of resources a resource could represent.\n     * @static\n     * @readonly\n     * @enum {number}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum TYPE\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** Unknown */\n        UNKNOWN = 0,\n        /** JSON */\n        JSON = 1,\n        /** XML */\n        XML = 2,\n        /** Image */\n        IMAGE = 3,\n        /** Audio */\n        AUDIO = 4,\n        /** Video */\n        VIDEO = 5,\n        /** Plain text */\n        TEXT = 6,\n    }\n\n    /**\n     * The types of loading a resource can use.\n     * @static\n     * @readonly\n     * @enum {number}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum LOAD_TYPE\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** Uses XMLHttpRequest to load the resource. */\n        XHR = 1,\n        /** Uses an `Image` object to load the resource. */\n        IMAGE = 2,\n        /** Uses an `Audio` object to load the resource. */\n        AUDIO = 3,\n        /** Uses a `Video` object to load the resource. */\n        VIDEO = 4,\n    }\n\n    /**\n     * The XHR ready states, used internally.\n     * @static\n     * @readonly\n     * @enum {string}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum XHR_RESPONSE_TYPE\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** string */\n        DEFAULT = 'text',\n        /** ArrayBuffer */\n        BUFFER = 'arraybuffer',\n        /** Blob */\n        BLOB = 'blob',\n        /** Document */\n        DOCUMENT = 'document',\n        /** Object */\n        JSON = 'json',\n        /** String */\n        TEXT = 'text',\n    }\n\n    export const _loadTypeMap: Dict<number> = {\n        // images\n        gif: LoaderResource.LOAD_TYPE.IMAGE,\n        png: LoaderResource.LOAD_TYPE.IMAGE,\n        bmp: LoaderResource.LOAD_TYPE.IMAGE,\n        jpg: LoaderResource.LOAD_TYPE.IMAGE,\n        jpeg: LoaderResource.LOAD_TYPE.IMAGE,\n        tif: LoaderResource.LOAD_TYPE.IMAGE,\n        tiff: LoaderResource.LOAD_TYPE.IMAGE,\n        webp: LoaderResource.LOAD_TYPE.IMAGE,\n        tga: LoaderResource.LOAD_TYPE.IMAGE,\n        avif: LoaderResource.LOAD_TYPE.IMAGE,\n        svg: LoaderResource.LOAD_TYPE.IMAGE,\n        'svg+xml': LoaderResource.LOAD_TYPE.IMAGE, // for SVG data urls\n\n        // audio\n        mp3: LoaderResource.LOAD_TYPE.AUDIO,\n        ogg: LoaderResource.LOAD_TYPE.AUDIO,\n        wav: LoaderResource.LOAD_TYPE.AUDIO,\n\n        // videos\n        mp4: LoaderResource.LOAD_TYPE.VIDEO,\n        webm: LoaderResource.LOAD_TYPE.VIDEO,\n    };\n\n    export const _xhrTypeMap: Dict<XHR_RESPONSE_TYPE> = {\n        // xml\n        xhtml: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        html: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        htm: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        xml: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        tmx: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        svg: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n\n        // This was added to handle Tiled Tileset XML, but .tsx is also a TypeScript React Component.\n        // Since it is way less likely for people to be loading TypeScript files instead of Tiled files,\n        // this should probably be fine.\n        tsx: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n\n        // images\n        gif: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        png: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        bmp: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        jpg: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        jpeg: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        tif: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        tiff: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        webp: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        tga: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        avif: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n\n        // json\n        json: LoaderResource.XHR_RESPONSE_TYPE.JSON,\n\n        // text\n        text: LoaderResource.XHR_RESPONSE_TYPE.TEXT,\n        txt: LoaderResource.XHR_RESPONSE_TYPE.TEXT,\n\n        // fonts\n        ttf: LoaderResource.XHR_RESPONSE_TYPE.BUFFER,\n        otf: LoaderResource.XHR_RESPONSE_TYPE.BUFFER,\n    };\n\n    // We can't set the `src` attribute to empty string, so on abort we set it to this 1px transparent gif\n    export const EMPTY_GIF = 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';\n}\n\nexport { LoaderResource };\n\n/** @deprecated - Use LoaderResource instead */\nexport type ILoaderResource = LoaderResource;\n", "/**\n * function from npm package `parseUri`, converted to TS to avoid leftpad incident\n * @param {string} str\n * @param [opts] - options\n * @param {boolean} [opts.strictMode] - type of parser\n */\nexport function parseUri(str: string, opts: { strictMode?: boolean }): any\n{\n    opts = opts || {};\n\n    const o = {\n        // eslint-disable-next-line max-len\n        key: ['source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'],\n        q: {\n            name: 'query<PERSON>ey',\n            parser: /(?:^|&)([^&=]*)=?([^&]*)/g\n        },\n        parser: {\n            // eslint-disable-next-line max-len\n            strict: /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n            // eslint-disable-next-line max-len\n            loose: /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/\n        }\n    };\n\n    const m = o.parser[opts.strictMode ? 'strict' : 'loose'].exec(str);\n    const uri: any = {};\n    let i = 14;\n\n    while (i--) uri[o.key[i]] = m[i] || '';\n\n    uri[o.q.name] = {};\n    uri[o.key[12]].replace(o.q.parser, (_t0: any, t1: any, t2: any) =>\n    {\n        if (t1) uri[o.q.name][t1] = t2;\n    });\n\n    return uri;\n}\n", "/**\n * Smaller version of the async library constructs.\n * @ignore\n */\nfunction _noop(): void\n{ /* empty */\n}\n\n/**\n * Ensures a function is only called once.\n * @ignore\n * @param {Function} fn - The function to wrap.\n * @returns {Function} The wrapping function.\n */\nfunction onlyOnce(fn: () => void): () => void\n{\n    return function onceWrapper(this: any, ...args: any)\n    {\n        if (fn === null)\n        {\n            throw new Error('Callback was already called.');\n        }\n\n        const callFn = fn;\n\n        fn = null;\n        callFn.apply(this, args);\n    };\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IQueue\n{\n\n}\n\n/**\n * @private\n * @memberof PIXI\n */\nexport class AsyncQueueItem<TaskData>\n{\n    data: TaskData;\n    callback: (...args: any[]) => void;\n\n    /**\n     * @param data\n     * @param callback\n     * @private\n     */\n    constructor(data: TaskData, callback: (...args: any[]) => void)\n    {\n        this.data = data;\n        this.callback = callback;\n    }\n}\n\n/**\n * @private\n * @memberof PIXI\n */\nexport class AsyncQueue<TaskData>\n{\n    workers = 0;\n\n    concurrency: number;\n    buffer: number;\n\n    saturated: () => void = _noop;\n    unsaturated: () => void = _noop;\n    empty: () => void = _noop;\n    drain: () => void = _noop;\n    error: (err: Error, task: TaskData) => void = _noop;\n\n    started = false;\n    paused = false;\n\n    private _worker: (x: TaskData, next: () => void) => void;\n    _tasks: Array<AsyncQueueItem<TaskData>> = [];\n\n    /**\n     * @param worker\n     * @param concurrency\n     * @private\n     */\n    constructor(worker: (x: TaskData, next: () => void) => void, concurrency = 1)\n    {\n        this._worker = worker;\n\n        if (concurrency === 0)\n        {\n            throw new Error('Concurrency must not be zero');\n        }\n\n        this.concurrency = concurrency;\n        this.buffer = concurrency / 4.0;\n    }\n\n    private _insert = (data: any, insertAtFront: boolean, callback?: () => void) =>\n    {\n        if (callback && typeof callback !== 'function')\n        {\n            throw new Error('task callback must be a function');\n        }\n\n        this.started = true;\n\n        // eslint-disable-next-line no-eq-null,eqeqeq\n        if (data == null && this.idle())\n        {\n            // call drain immediately if there are no tasks\n            setTimeout(() => this.drain(), 1);\n\n            return;\n        }\n\n        const item = new AsyncQueueItem<TaskData>(\n            data,\n            typeof callback === 'function' ? callback : _noop\n        );\n\n        if (insertAtFront)\n        {\n            this._tasks.unshift(item);\n        }\n        else\n        {\n            this._tasks.push(item);\n        }\n\n        setTimeout(this.process, 1);\n    };\n\n    process = (): void =>\n    {\n        while (!this.paused && this.workers < this.concurrency && this._tasks.length)\n        {\n            const task = this._tasks.shift();\n\n            if (this._tasks.length === 0)\n            {\n                this.empty();\n            }\n\n            this.workers += 1;\n\n            if (this.workers === this.concurrency)\n            {\n                this.saturated();\n            }\n\n            this._worker(task.data, onlyOnce(this._next(task)));\n        }\n    };\n\n    /**\n     * @param task\n     * @private\n     */\n    _next(task: AsyncQueueItem<TaskData>): (...args: any) => void\n    {\n        return (...args: any) =>\n        {\n            this.workers -= 1;\n\n            task.callback(...args);\n\n            // eslint-disable-next-line no-eq-null,eqeqeq\n            if (args[0] != null)\n            {\n                this.error(args[0], task.data);\n            }\n\n            if (this.workers <= (this.concurrency - this.buffer))\n            {\n                this.unsaturated();\n            }\n\n            if (this.idle())\n            {\n                this.drain();\n            }\n\n            this.process();\n        };\n    }\n\n    // That was in object\n\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    push(data: any, callback?: (...args: any[]) => void): void\n    {\n        this._insert(data, false, callback);\n    }\n\n    kill(): void\n    {\n        this.workers = 0;\n        this.drain = _noop;\n        this.started = false;\n        this._tasks = [];\n    }\n\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    unshift(data: any, callback?: (...args: any[]) => void): void\n    {\n        this._insert(data, true, callback);\n    }\n\n    length(): number\n    {\n        return this._tasks.length;\n    }\n\n    running(): number\n    {\n        return this.workers;\n    }\n\n    idle(): boolean\n    {\n        return this._tasks.length + this.workers === 0;\n    }\n\n    pause(): void\n    {\n        if (this.paused === true)\n        {\n            return;\n        }\n\n        this.paused = true;\n    }\n\n    resume(): void\n    {\n        if (this.paused === false)\n        {\n            return;\n        }\n\n        this.paused = false;\n\n        // Need to call this.process once per concurrent\n        // worker to preserve full concurrency after pause\n        for (let w = 1; w <= this.concurrency; w++)\n        {\n            this.process();\n        }\n    }\n\n    /**\n     * Iterates an array in series.\n     * @param {Array.<*>} array - Array to iterate.\n     * @param {Function} iterator - Function to call for each element.\n     * @param {Function} callback - Function to call when done, or on error.\n     * @param {boolean} [deferNext=false] - Break synchronous each loop by calling next with a setTimeout of 1.\n     */\n    static eachSeries(array: Array<any>, iterator: (x: any, next: (err?: any) => void) => void,\n        callback?: (err?: any) => void, deferNext?: boolean): void\n    {\n        let i = 0;\n        const len = array.length;\n\n        function next(err?: any)\n        {\n            if (err || i === len)\n            {\n                if (callback)\n                {\n                    callback(err);\n                }\n\n                return;\n            }\n\n            if (deferNext)\n            {\n                setTimeout(() =>\n                {\n                    iterator(array[i++], next);\n                }, 1);\n            }\n            else\n            {\n                iterator(array[i++], next);\n            }\n        }\n\n        next();\n    }\n\n    /**\n     * Async queue implementation,\n     * @param {Function} worker - The worker function to call for each task.\n     * @param {number} concurrency - How many workers to run in parrallel.\n     * @returns {*} The async queue object.\n     */\n    static queue(worker: (x: any, next: (...args: any) => void) => void, concurrency?: number): AsyncQueue<any>\n    {\n        return new AsyncQueue<any>(worker, concurrency);\n    }\n}\n", "import { Signal } from './base/Signal';\nimport { parseUri } from './base/parseUri';\nimport type { IResourceMetadata } from './LoaderResource';\nimport { LoaderResource } from './LoaderResource';\nimport { AsyncQueue } from './base/AsyncQueue';\nimport type { Dict } from '@pixi/utils';\nimport { deprecation } from '@pixi/utils';\nimport { extensions, ExtensionType } from '@pixi/core';\n\n// some constants\nconst MAX_PROGRESS = 100;\nconst rgxExtractUrlHash = /(#[\\w-]+)?$/;\n\nexport type ILoaderMiddleware = (resource: LoaderResource, next: (...args: any[]) => void) => void;\n\nexport interface ILoaderAdd\n{\n    (this: Loader, name: string, url: string, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, name: string, url: string, options?: IAddOptions, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, url: string, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, url: string, options?: IAddOptions, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, options: IAddOptions, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, resources: (IAddOptions | string)[], callback?: LoaderResource.OnCompleteSignal): Loader;\n}\n\n/**\n * Options for a call to `.add()`.\n * @see Loader#add\n * @property {string} name - The name of the resource to load, if not passed the url is used.\n * @property {string} key - Alias for `name`.\n * @property {string} url - The url for this resource, relative to the baseUrl of this loader.\n * @property {string|boolean} crossOrigin - Is this request cross-origin? Default is to determine automatically.\n * @property {number} [timeout=0] - A timeout in milliseconds for the load. If the load takes longer\n *      than this time it is cancelled and the load is considered a failure. If this value is\n *      set to `0` then there is no explicit timeout.\n * @property {LoaderResource.LOAD_TYPE} [loadType=LoaderResource.LOAD_TYPE.XHR] - How should this resource be loaded?\n * @property {LoaderResource.XHR_RESPONSE_TYPE} [xhrType=LoaderResource.XHR_RESPONSE_TYPE.DEFAULT] - How should the data\n *      being loaded be interpreted when using XHR?\n * @property {LoaderResource.OnCompleteSignal} onComplete - Callback to add an an onComplete signal istener.\n * @property {LoaderResource.OnCompleteSignal} callback - Alias for `onComplete`.\n * @property {IResourceMetadata} metadata - Extra configuration for middleware and the Resource object.\n */\nexport interface IAddOptions\n{\n    name?: string;\n    key?: string;\n    url?: string;\n    crossOrigin?: string | boolean;\n    timeout?: number;\n    parentResource?: LoaderResource;\n    loadType?: LoaderResource.LOAD_TYPE;\n    xhrType?: LoaderResource.XHR_RESPONSE_TYPE;\n    onComplete?: LoaderResource.OnCompleteSignal;\n    callback?: LoaderResource.OnCompleteSignal;\n    metadata?: IResourceMetadata;\n}\n\n/**\n * The new loader, forked from Resource Loader by Chad Engler: https://github.com/englercj/resource-loader\n *\n * ```js\n * const loader = PIXI.Loader.shared; // PixiJS exposes a premade instance for you to use.\n * // or\n * const loader = new PIXI.Loader(); // You can also create your own if you want\n *\n * const sprites = {};\n *\n * // Chainable `add` to enqueue a resource\n * loader.add('bunny', 'data/bunny.png')\n *       .add('spaceship', 'assets/spritesheet.json');\n * loader.add('scoreFont', 'assets/score.fnt');\n *\n * // Chainable `pre` to add a middleware that runs for each resource, *before* loading that resource.\n * // This is useful to implement custom caching modules (using filesystem, indexeddb, memory, etc).\n * loader.pre(cachingMiddleware);\n *\n * // Chainable `use` to add a middleware that runs for each resource, *after* loading that resource.\n * // This is useful to implement custom parsing modules (like spritesheet parsers, spine parser, etc).\n * loader.use(parsingMiddleware);\n *\n * // The `load` method loads the queue of resources, and calls the passed in callback called once all\n * // resources have loaded.\n * loader.load((loader, resources) => {\n *     // resources is an object where the key is the name of the resource loaded and the value is the resource object.\n *     // They have a couple default properties:\n *     // - `url`: The URL that the resource was loaded from\n *     // - `error`: The error that happened when trying to load (if any)\n *     // - `data`: The raw data that was loaded\n *     // also may contain other properties based on the middleware that runs.\n *     sprites.bunny = new PIXI.TilingSprite(resources.bunny.texture);\n *     sprites.spaceship = new PIXI.TilingSprite(resources.spaceship.texture);\n *     sprites.scoreFont = new PIXI.TilingSprite(resources.scoreFont.texture);\n * });\n *\n * // throughout the process multiple signals can be dispatched.\n * loader.onProgress.add(() => {}); // called once per loaded/errored file\n * loader.onError.add(() => {}); // called once per errored file\n * loader.onLoad.add(() => {}); // called once per loaded file\n * loader.onComplete.add(() => {}); // called once when the queued resources all load.\n * ```\n * @memberof PIXI\n */\nclass Loader\n{\n    /** The base url for all resources loaded by this loader. */\n    baseUrl: string;\n\n    /** The progress percent of the loader going through the queue. */\n    progress = 0;\n\n    /** Loading state of the loader, true if it is currently loading resources. */\n    loading = false;\n\n    /**\n     * A querystring to append to every URL added to the loader.\n     *\n     * This should be a valid query string *without* the question-mark (`?`). The loader will\n     * also *not* escape values for you. Make sure to escape your parameters with\n     * [`encodeURIComponent`](https://mdn.io/encodeURIComponent) before assigning this property.\n     * @example\n     * const loader = new Loader();\n     *\n     * loader.defaultQueryString = 'user=me&password=secret';\n     *\n     * // This will request 'image.png?user=me&password=secret'\n     * loader.add('image.png').load();\n     *\n     * loader.reset();\n     *\n     * // This will request 'image.png?v=1&user=me&password=secret'\n     * loader.add('iamge.png?v=1').load();\n     */\n    defaultQueryString = '';\n\n    /** The middleware to run before loading each resource. */\n    private _beforeMiddleware: Array<ILoaderMiddleware> = [];\n\n    /** The middleware to run after loading each resource. */\n    private _afterMiddleware: Array<ILoaderMiddleware> = [];\n\n    /** The tracks the resources we are currently completing parsing for. */\n    private _resourcesParsing: Array<LoaderResource> = [];\n\n    /**\n     * The `_loadResource` function bound with this object context.\n     * @param r - The resource to load\n     * @param d - The dequeue function\n     */\n    private _boundLoadResource = (r: LoaderResource, d: () => void): void => this._loadResource(r, d);\n\n    /** The resources waiting to be loaded. */\n    private _queue: AsyncQueue<any>;\n\n    /** All the resources for this loader keyed by name. */\n    resources: Dict<LoaderResource> = {};\n\n    /** Dispatched once per loaded or errored resource. */\n    onProgress: Signal<Loader.OnProgressSignal>;\n\n    /** Dispatched once per errored resource. */\n    onError: Signal<Loader.OnErrorSignal>;\n\n    /** Dispatched once per loaded resource. */\n    onLoad: Signal<Loader.OnLoadSignal>;\n\n    /** Dispatched when the loader begins to process the queue. */\n    onStart: Signal<Loader.OnStartSignal>;\n\n    /** Dispatched when the queued resources all load. */\n    onComplete: Signal<Loader.OnCompleteSignal>;\n\n    /**\n     * @param baseUrl - The base url for all resources loaded by this loader.\n     * @param concurrency - The number of resources to load concurrently.\n     */\n    constructor(baseUrl = '', concurrency = 10)\n    {\n        this.baseUrl = baseUrl;\n        this._beforeMiddleware = [];\n        this._afterMiddleware = [];\n        this._resourcesParsing = [];\n        this._boundLoadResource = (r, d) => this._loadResource(r, d);\n        this._queue = AsyncQueue.queue(this._boundLoadResource, concurrency);\n        this._queue.pause();\n        this.resources = {};\n        this.onProgress = new Signal();\n        this.onError = new Signal();\n        this.onLoad = new Signal();\n        this.onStart = new Signal();\n        this.onComplete = new Signal();\n\n        for (let i = 0; i < Loader._plugins.length; ++i)\n        {\n            const plugin = Loader._plugins[i];\n            const { pre, use } = plugin;\n\n            if (pre)\n            {\n                this.pre(pre);\n            }\n\n            if (use)\n            {\n                this.use(use);\n            }\n        }\n\n        this._protected = false;\n    }\n\n    /**\n     * Adds a resource (or multiple resources) to the loader queue.\n     *\n     * This function can take a wide variety of different parameters. The only thing that is always\n     * required the url to load. All the following will work:\n     *\n     * ```js\n     * loader\n     *     // normal param syntax\n     *     .add('key', 'http://...', function () {})\n     *     .add('http://...', function () {})\n     *     .add('http://...')\n     *\n     *     // object syntax\n     *     .add({\n     *         name: 'key2',\n     *         url: 'http://...'\n     *     }, function () {})\n     *     .add({\n     *         url: 'http://...'\n     *     }, function () {})\n     *     .add({\n     *         name: 'key3',\n     *         url: 'http://...'\n     *         onComplete: function () {}\n     *     })\n     *     .add({\n     *         url: 'https://...',\n     *         onComplete: function () {},\n     *         crossOrigin: true\n     *     })\n     *\n     *     // you can also pass an array of objects or urls or both\n     *     .add([\n     *         { name: 'key4', url: 'http://...', onComplete: function () {} },\n     *         { url: 'http://...', onComplete: function () {} },\n     *         'http://...'\n     *     ])\n     *\n     *     // and you can use both params and options\n     *     .add('key', 'http://...', { crossOrigin: true }, function () {})\n     *     .add('http://...', { crossOrigin: true }, function () {});\n     * ```\n     */\n    add: ILoaderAdd;\n\n    /**\n     * Same as add, params have strict order\n     * @private\n     * @param name - The name of the resource to load.\n     * @param url - The url for this resource, relative to the baseUrl of this loader.\n     * @param options - The options for the load.\n     * @param callback - Function to call when this specific resource completes loading.\n     * @returns The loader itself.\n     */\n    protected _add(name: string, url: string, options: IAddOptions, callback?: LoaderResource.OnCompleteSignal): this\n    {\n        // if loading already you can only add resources that have a parent.\n        if (this.loading && (!options || !options.parentResource))\n        {\n            throw new Error('Cannot add resources while the loader is running.');\n        }\n\n        // check if resource already exists.\n        if (this.resources[name])\n        {\n            throw new Error(`Resource named \"${name}\" already exists.`);\n        }\n\n        // add base url if this isn't an absolute url\n        url = this._prepareUrl(url);\n\n        // create the store the resource\n        this.resources[name] = new LoaderResource(name, url, options);\n\n        if (typeof callback === 'function')\n        {\n            this.resources[name].onAfterMiddleware.once(callback);\n        }\n\n        // if actively loading, make sure to adjust progress chunks for that parent and its children\n        if (this.loading)\n        {\n            const parent = options.parentResource;\n            const incompleteChildren = [];\n\n            for (let i = 0; i < parent.children.length; ++i)\n            {\n                if (!parent.children[i].isComplete)\n                {\n                    incompleteChildren.push(parent.children[i]);\n                }\n            }\n\n            const fullChunk = parent.progressChunk * (incompleteChildren.length + 1); // +1 for parent\n            const eachChunk = fullChunk / (incompleteChildren.length + 2); // +2 for parent & new child\n\n            parent.children.push(this.resources[name]);\n            parent.progressChunk = eachChunk;\n\n            for (let i = 0; i < incompleteChildren.length; ++i)\n            {\n                incompleteChildren[i].progressChunk = eachChunk;\n            }\n\n            this.resources[name].progressChunk = eachChunk;\n        }\n\n        // add the resource to the queue\n        this._queue.push(this.resources[name]);\n\n        return this;\n    }\n\n    /* eslint-enable require-jsdoc,valid-jsdoc */\n\n    /**\n     * Sets up a middleware function that will run *before* the\n     * resource is loaded.\n     * @param fn - The middleware function to register.\n     * @returns The loader itself.\n     */\n    pre(fn: ILoaderMiddleware): this\n    {\n        this._beforeMiddleware.push(fn);\n\n        return this;\n    }\n\n    /**\n     * Sets up a middleware function that will run *after* the\n     * resource is loaded.\n     * @param fn - The middleware function to register.\n     * @returns The loader itself.\n     */\n    use(fn: ILoaderMiddleware): this\n    {\n        this._afterMiddleware.push(fn);\n\n        return this;\n    }\n\n    /**\n     * Resets the queue of the loader to prepare for a new load.\n     * @returns The loader itself.\n     */\n    reset(): this\n    {\n        this.progress = 0;\n        this.loading = false;\n\n        this._queue.kill();\n        this._queue.pause();\n\n        // abort all resource loads\n        for (const k in this.resources)\n        {\n            const res = this.resources[k];\n\n            if (res._onLoadBinding)\n            {\n                res._onLoadBinding.detach();\n            }\n\n            if (res.isLoading)\n            {\n                res.abort('loader reset');\n            }\n        }\n\n        this.resources = {};\n\n        return this;\n    }\n\n    /**\n     * Starts loading the queued resources.\n     * @param cb - Optional callback that will be bound to the `complete` event.\n     * @returns The loader itself.\n     */\n    load(cb?: Loader.OnCompleteSignal): this\n    {\n        // #if _DEBUG\n        deprecation('6.5.0', '@pixi/loaders is being replaced with @pixi/assets in the next major release.');\n        // #endif\n\n        // register complete callback if they pass one\n        if (typeof cb === 'function')\n        {\n            this.onComplete.once(cb);\n        }\n\n        // if the queue has already started we are done here\n        if (this.loading)\n        {\n            return this;\n        }\n\n        if (this._queue.idle())\n        {\n            this._onStart();\n            this._onComplete();\n        }\n        else\n        {\n            // distribute progress chunks\n            const numTasks = this._queue._tasks.length;\n            const chunk = MAX_PROGRESS / numTasks;\n\n            for (let i = 0; i < this._queue._tasks.length; ++i)\n            {\n                this._queue._tasks[i].data.progressChunk = chunk;\n            }\n\n            // notify we are starting\n            this._onStart();\n\n            // start loading\n            this._queue.resume();\n        }\n\n        return this;\n    }\n\n    /**\n     * The number of resources to load concurrently.\n     * @default 10\n     */\n    get concurrency(): number\n    {\n        return this._queue.concurrency;\n    }\n    set concurrency(concurrency: number)\n    {\n        this._queue.concurrency = concurrency;\n    }\n\n    /**\n     * Prepares a url for usage based on the configuration of this object\n     * @param url - The url to prepare.\n     * @returns The prepared url.\n     */\n    private _prepareUrl(url: string): string\n    {\n        const parsedUrl = parseUri(url, { strictMode: true });\n        let result;\n\n        // absolute url, just use it as is.\n        if (parsedUrl.protocol || !parsedUrl.path || url.indexOf('//') === 0)\n        {\n            result = url;\n        }\n        // if baseUrl doesn't end in slash and url doesn't start with slash, then add a slash inbetween\n        else if (this.baseUrl.length\n            && this.baseUrl.lastIndexOf('/') !== this.baseUrl.length - 1\n            && url.charAt(0) !== '/'\n        )\n        {\n            result = `${this.baseUrl}/${url}`;\n        }\n        else\n        {\n            result = this.baseUrl + url;\n        }\n\n        // if we need to add a default querystring, there is a bit more work\n        if (this.defaultQueryString)\n        {\n            const hash = rgxExtractUrlHash.exec(result)[0];\n\n            result = result.slice(0, result.length - hash.length);\n\n            if (result.indexOf('?') !== -1)\n            {\n                result += `&${this.defaultQueryString}`;\n            }\n            else\n            {\n                result += `?${this.defaultQueryString}`;\n            }\n\n            result += hash;\n        }\n\n        return result;\n    }\n\n    /**\n     * Loads a single resource.\n     * @param resource - The resource to load.\n     * @param dequeue - The function to call when we need to dequeue this item.\n     */\n    private _loadResource(resource: LoaderResource, dequeue: () => void): void\n    {\n        resource._dequeue = dequeue;\n\n        // run before middleware\n        AsyncQueue.eachSeries(\n            this._beforeMiddleware,\n            (fn: any, next: (...args: any) => void) =>\n            {\n                fn.call(this, resource, () =>\n                {\n                    // if the before middleware marks the resource as complete,\n                    // break and don't process any more before middleware\n                    next(resource.isComplete ? {} : null);\n                });\n            },\n            () =>\n            {\n                if (resource.isComplete)\n                {\n                    this._onLoad(resource);\n                }\n                else\n                {\n                    resource._onLoadBinding = resource.onComplete.once(this._onLoad, this);\n                    resource.load();\n                }\n            },\n            true\n        );\n    }\n\n    /** Called once loading has started. */\n    private _onStart(): void\n    {\n        this.progress = 0;\n        this.loading = true;\n        this.onStart.dispatch(this);\n    }\n\n    /** Called once each resource has loaded. */\n    private _onComplete(): void\n    {\n        this.progress = MAX_PROGRESS;\n        this.loading = false;\n        this.onComplete.dispatch(this, this.resources);\n    }\n\n    /**\n     * Called each time a resources is loaded.\n     * @param resource - The resource that was loaded\n     */\n    private _onLoad(resource: LoaderResource): void\n    {\n        resource._onLoadBinding = null;\n\n        // remove this resource from the async queue, and add it to our list of resources that are being parsed\n        this._resourcesParsing.push(resource);\n        resource._dequeue();\n\n        // run all the after middleware for this resource\n        AsyncQueue.eachSeries(\n            this._afterMiddleware,\n            (fn: any, next: any) =>\n            {\n                fn.call(this, resource, next);\n            },\n            () =>\n            {\n                resource.onAfterMiddleware.dispatch(resource);\n\n                this.progress = Math.min(MAX_PROGRESS, this.progress + resource.progressChunk);\n                this.onProgress.dispatch(this, resource);\n\n                if (resource.error)\n                {\n                    this.onError.dispatch(resource.error, this, resource);\n                }\n                else\n                {\n                    this.onLoad.dispatch(this, resource);\n                }\n\n                this._resourcesParsing.splice(this._resourcesParsing.indexOf(resource), 1);\n\n                // do completion check\n                if (this._queue.idle() && this._resourcesParsing.length === 0)\n                {\n                    this._onComplete();\n                }\n            },\n            true\n        );\n    }\n\n    static _plugins: Array<ILoaderPlugin> = [];\n    private static _shared: Loader;\n    /**\n     * If this loader cannot be destroyed.\n     * @default false\n     */\n    private _protected: boolean;\n\n    /** Destroy the loader, removes references. */\n    public destroy(): void\n    {\n        if (!this._protected)\n        {\n            this.reset();\n        }\n    }\n\n    /** A premade instance of the loader that can be used to load resources. */\n    public static get shared(): Loader\n    {\n        let shared = Loader._shared;\n\n        if (!shared)\n        {\n            shared = new Loader();\n            shared._protected = true;\n            Loader._shared = shared;\n        }\n\n        return shared;\n    }\n\n    /**\n     * Use the {@link PIXI.extensions.add} API to register plugins.\n     * @deprecated since 6.5.0\n     * @param plugin - The plugin to add\n     * @returns Reference to PIXI.Loader for chaining\n     */\n    public static registerPlugin(plugin: ILoaderPlugin): typeof Loader\n    {\n        // #if _DEBUG\n        deprecation('6.5.0', 'Loader.registerPlugin() is deprecated, use extensions.add() instead.');\n        // #endif\n\n        extensions.add({\n            type: ExtensionType.Loader,\n            ref: plugin,\n        });\n\n        return Loader;\n    }\n}\n\nextensions.handleByList(ExtensionType.Loader, Loader._plugins);\n\nLoader.prototype.add = function add(this: Loader, name: any, url?: any, options?: any, callback?: any): Loader\n{\n    // special case of an array of objects or urls\n    if (Array.isArray(name))\n    {\n        for (let i = 0; i < name.length; ++i)\n        {\n            this.add((name as any)[i]);\n        }\n\n        return this;\n    }\n\n    // if an object is passed instead of params\n    if (typeof name === 'object')\n    {\n        options = name;\n        callback = (url as any) || options.callback || options.onComplete;\n        url = options.url;\n        name = options.name || options.key || options.url;\n    }\n\n    // case where no name is passed shift all args over by one.\n    if (typeof url !== 'string')\n    {\n        callback = options as any;\n        options = url;\n        url = name;\n    }\n\n    // now that we shifted make sure we have a proper url.\n    if (typeof url !== 'string')\n    {\n        throw new Error('No url passed to add resource to loader.');\n    }\n\n    // options are optional so people might pass a function and no options\n    if (typeof options === 'function')\n    {\n        callback = options;\n        options = null;\n    }\n\n    return this._add(name, url, options, callback);\n};\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Loader\n{\n    /**\n     * When the resource starts to load.\n     * @param resource - The resource that the event happened on.\n     */\n    export type OnStartSignal = (loader: Loader) => void;\n    /**\n     * When the progress changes the loader and resource are dispatched.\n     * @param loader - The loader the progress is advancing on.\n     * @param resource - The resource that has completed or failed to cause the progress to advance.\n     */\n    export type OnProgressSignal = (loader: Loader, resource: LoaderResource) => void;\n    /**\n     * When a load completes without error the loader and resource are dispatched.\n     * @param loader - The loader that has started loading resources.\n     * @param resource - The resource that has completed.\n     */\n    export type OnLoadSignal = (loader: Loader, resource: LoaderResource) => void;\n    /**\n     * When the loader starts loading resources it dispatches this callback.\n     * @param loader - The loader that has started loading resources.\n     */\n    export type OnCompleteSignal = (loader: Loader, resources: Dict<LoaderResource>) => void;\n    /**\n     * When an error occurs the loader and resource are dispatched.\n     * @param loader - The loader the error happened in.\n     * @param resource - The resource that caused the error.\n     */\n    export type OnErrorSignal = (error: Error, loader: Loader, resource: LoaderResource) => void;\n}\n\nexport { Loader };\n\n/**\n * Plugin to be installed for handling specific Loader resources.\n * @property {Function} add - Function to call immediate after registering plugin.\n * @property {Function} pre - Middleware function to run before load, the\n *           arguments for this are `(resource, next)`\n * @property {Function} use - Middleware function to run after load, the\n *           arguments for this are `(resource, next)`\n */\nexport interface ILoaderPlugin\n{\n    /** Function to call immediate after registering plugin. */\n    add?(): void;\n\n    /**\n     * Middleware function to run before load\n     * @param {LoaderResource} resource - resource\n     * @param {LoaderResource} next - next middleware\n     */\n    pre?(resource: LoaderResource, next: (...args: any[]) => void): void;\n\n    /**\n     * Middleware function to run after load\n     * @param {LoaderResource} resource - resource\n     * @param {LoaderResource} next - next middleware\n     */\n    use?(resource: LoaderResource, next: (...args: any[]) => void): void;\n}\n", "import type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport { Loader } from './Loader';\n\n/**\n * Application plugin for supporting loader option. Installing the LoaderPlugin\n * is not necessary if using **pixi.js** or **pixi.js-legacy**.\n * @example\n * import {AppLoaderPlugin} from '@pixi/loaders';\n * import {extensions} from '@pixi/core';\n * extensions.add(AppLoaderPlugin);\n * @memberof PIXI\n */\nexport class AppLoaderPlugin\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Application;\n\n    /**\n     * Loader instance to help with asset loading.\n     * @memberof PIXI.Application#\n     * @readonly\n     */\n    public static loader: Loader;\n\n    /**\n     * Called on application constructor\n     * @param options\n     * @private\n     */\n    static init(options?: GlobalMixins.IApplicationOptions): void\n    {\n        options = Object.assign({\n            sharedLoader: false,\n        }, options);\n\n        this.loader = options.sharedLoader ? Loader.shared : new Loader();\n    }\n\n    /**\n     * Called when application destroyed\n     * @private\n     */\n    static destroy(): void\n    {\n        if (this.loader)\n        {\n            this.loader.destroy();\n            this.loader = null;\n        }\n    }\n}\n", "import type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType, Texture } from '@pixi/core';\nimport { LoaderResource } from './LoaderResource';\n\n/**\n * Loader plugin for handling Texture resources.\n * @memberof PIXI\n */\nexport class TextureLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /** Handle SVG elements a text, render with SVGResource. */\n    public static add(): void\n    {\n        LoaderResource.setExtensionLoadType('svg', LoaderResource.LOAD_TYPE.XHR);\n        LoaderResource.setExtensionXhrType('svg', LoaderResource.XHR_RESPONSE_TYPE.TEXT);\n    }\n\n    /**\n     * Called after a resource is loaded.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource\n     * @param {Function} next\n     */\n    public static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        // create a new texture if the data is an Image object\n        if (resource.data && (resource.type === LoaderResource.TYPE.IMAGE || resource.extension === 'svg'))\n        {\n            const { data, url, name, metadata } = resource;\n\n            Texture.fromLoader(data, url, name, metadata).then((texture) =>\n            {\n                resource.texture = texture;\n                next();\n            })\n            // TODO: handle errors in Texture.fromLoader\n            // so we can pass them to the Loader\n                .catch(next);\n        }\n        else\n        {\n            next();\n        }\n    }\n}\n", "import { LoaderResource } from '../LoaderResource';\nimport { encodeBinary } from '../base/encodeBinary';\n\n/**\n * A middleware for transforming XHR loaded Blobs into more useful objects\n * @ignore\n * @function parsing\n * @example\n * import { Loader, middleware } from 'resource-loader';\n * const loader = new Loader();\n * loader.use(middleware.parsing);\n * @param resource - Current Resource\n * @param next - Callback when complete\n */\nexport function parsing(resource: LoaderResource, next: (...args: any) => void): void\n{\n    if (!resource.data)\n    {\n        next();\n\n        return;\n    }\n\n    // if this was an XHR load of a blob\n    if (resource.xhr && resource.xhrType === LoaderResource.XHR_RESPONSE_TYPE.BLOB)\n    {\n        // if there is no blob support we probably got a binary string back\n        if (!self.Blob || typeof resource.data === 'string')\n        {\n            const type = resource.xhr.getResponseHeader('content-type');\n\n            // this is an image, convert the binary string into a data url\n            if (type && type.indexOf('image') === 0)\n            {\n                resource.data = new Image();\n                resource.data.src = `data:${type};base64,${encodeBinary(resource.xhr.responseText)}`;\n\n                resource.type = LoaderResource.TYPE.IMAGE;\n\n                // wait until the image loads and then callback\n                resource.data.onload = () =>\n                {\n                    resource.data.onload = null;\n\n                    next();\n                };\n\n                // next will be called on load\n                return;\n            }\n        }\n        // if content type says this is an image, then we should transform the blob into an Image object\n        else if (resource.data.type.indexOf('image') === 0)\n        {\n            const Url = globalThis.URL || globalThis.webkitURL;\n            const src = Url.createObjectURL(resource.data);\n\n            resource.blob = resource.data;\n            resource.data = new Image();\n            resource.data.src = src;\n\n            resource.type = LoaderResource.TYPE.IMAGE;\n\n            // cleanup the no longer used blob after the image loads\n            // TODO: Is this correct? Will the image be invalid after revoking?\n            resource.data.onload = () =>\n            {\n                Url.revokeObjectURL(src);\n                resource.data.onload = null;\n\n                next();\n            };\n\n            // next will be called on load.\n            return;\n        }\n    }\n\n    next();\n}\n", "const _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n\n/**\n * Encodes binary into base64.\n * @function encodeBinary\n * @param {string} input - The input data to encode.\n * @returns {string} The encoded base64 string\n */\nexport function encodeBinary(input: string): string\n{\n    let output = '';\n    let inx = 0;\n\n    while (inx < input.length)\n    {\n        // Fill byte buffer array\n        const bytebuffer = [0, 0, 0];\n        const encodedCharIndexes = [0, 0, 0, 0];\n\n        for (let jnx = 0; jnx < bytebuffer.length; ++jnx)\n        {\n            if (inx < input.length)\n            {\n                // throw away high-order byte, as documented at:\n                // https://developer.mozilla.org/En/Using_XMLHttpRequest#Handling_binary_data\n                bytebuffer[jnx] = input.charCodeAt(inx++) & 0xff;\n            }\n            else\n            {\n                bytebuffer[jnx] = 0;\n            }\n        }\n\n        // Get each encoded character, 6 bits at a time\n        // index 1: first 6 bits\n        encodedCharIndexes[0] = bytebuffer[0] >> 2;\n\n        // index 2: second 6 bits (2 least significant bits from input byte 1 + 4 most significant bits from byte 2)\n        encodedCharIndexes[1] = ((bytebuffer[0] & 0x3) << 4) | (bytebuffer[1] >> 4);\n\n        // index 3: third 6 bits (4 least significant bits from input byte 2 + 2 most significant bits from byte 3)\n        encodedCharIndexes[2] = ((bytebuffer[1] & 0x0f) << 2) | (bytebuffer[2] >> 6);\n\n        // index 3: forth 6 bits (6 least significant bits from input byte 3)\n        encodedCharIndexes[3] = bytebuffer[2] & 0x3f;\n\n        // Determine whether padding happened, and adjust accordingly\n        const paddingBytes = inx - (input.length - 1);\n\n        switch (paddingBytes)\n        {\n            case 2:\n                // Set last 2 characters to padding char\n                encodedCharIndexes[3] = 64;\n                encodedCharIndexes[2] = 64;\n                break;\n\n            case 1:\n                // Set last character to padding char\n                encodedCharIndexes[3] = 64;\n                break;\n\n            default:\n                break; // No padding - proceed\n        }\n\n        // Now we will grab each appropriate character out of our keystring\n        // based on our index array and append it to the output string\n        for (let jnx = 0; jnx < encodedCharIndexes.length; ++jnx)\n        {\n            output += _keyStr.charAt(encodedCharIndexes[jnx]);\n        }\n    }\n\n    return output;\n}\n", "import { parsing } from './middleware';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * Parse any blob into more usable objects (e.g. Image).\n * @memberof PIXI\n */\nclass ParsingLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    static use = parsing;\n}\n\nexport { ParsingLoader };\n", "export * from './AppLoaderPlugin';\nexport * from './LoaderResource';\nexport * from './Loader';\nexport * from './TextureLoader';\n\nimport { ParsingLoader } from './ParsingLoader';\nimport { TextureLoader } from './TextureLoader';\nimport { extensions } from '@pixi/core';\n\nextensions.add(\n    TextureLoader,\n    ParsingLoader\n);\n"], "names": ["SignalBinding", "fn", "once", "thisArg", "this", "_fn", "_once", "_thisArg", "_next", "_prev", "_owner", "prototype", "detach", "_addSignalBinding", "self", "node", "_head", "_tail", "useXdr", "Signal", "undefined", "handlers", "exists", "ee", "push", "has", "Error", "dispatch", "args", "_i", "arguments", "length", "apply", "add", "detachAll", "parseUri", "str", "opts", "o", "key", "q", "name", "parser", "strict", "loose", "m", "strictMode", "exec", "uri", "i", "replace", "_t0", "t1", "t2", "XHR_RESPONSE_TYPE", "LOAD_TYPE", "TYPE", "STATUS_FLAGS", "LoaderResource", "tempAnchor", "_noop", "setExtMap", "map", "extname", "val", "indexOf", "substring", "reqType", "xhr", "toString", "onlyOnce", "callFn", "url", "options", "_dequeue", "_onLoadBinding", "_elementTimer", "_boundComplete", "_boundOnError", "_boundOnProgress", "_boundOnTimeout", "_boundXhrOnError", "_boundXhrOnTimeout", "_boundXhrOnAbort", "_boundXhrOnLoad", "_flags", "_setFlag", "DATA_URL", "extension", "_getExtension", "data", "crossOrigin", "timeout", "loadType", "_determineLoadType", "xhrType", "metadata", "error", "children", "type", "UNKNOWN", "progressChunk", "complete", "bind", "_onError", "_onProgress", "_onTimeout", "_xhrOnError", "_xhrOnTimeout", "_xhrOnAbort", "_xhrOnLoad", "onStart", "onProgress", "onComplete", "onAfterMiddleware", "setExtensionLoadType", "_loadTypeMap", "setExtensionXhrType", "_xhrTypeMap", "Object", "defineProperty", "get", "_hasFlag", "COMPLETE", "LOADING", "_clearEvents", "_finish", "abort", "message", "xdr", "src", "EMPTY_GIF", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "load", "cb", "_this", "isLoading", "isComplete", "setTimeout", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IMAGE", "_loadElement", "AUDIO", "_loadSourceElement", "VIDEO", "XHR", "globalThis", "XDomainRequest", "XMLHttpRequest", "_loadXdr", "_loadXhr", "flag", "value", "clearTimeout", "removeEventListener", "onerror", "ontimeout", "onprogress", "onload", "loadElement", "Image", "document", "createElement", "skipSource", "addEventListener", "Audio", "navigator", "isCocoonJS", "Array", "isArray", "mimeTypes", "mimeType", "append<PERSON><PERSON><PERSON>", "_createSource", "_determineXhrType", "withCredentials", "open", "JSON", "DOCUMENT", "responseType", "TEXT", "send", "mime", "source", "event", "target", "nodeName", "lengthComputable", "loaded", "total", "status", "statusText", "text", "responseText", "BUFFER", "parse", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "parseFromString", "div", "innerHTML", "XML", "response", "responseURL", "loc", "origin", "location", "href", "parsedUrl", "samePort", "port", "protocol", "host", "hostname", "ext", "isDataUrl", "slashIndex", "queryStart", "hashStart", "index", "Math", "min", "lastIndexOf", "toLowerCase", "_getMimeFromXhrType", "BLOB", "DEFAULT", "gif", "png", "bmp", "jpg", "jpeg", "tif", "tiff", "webp", "tga", "avif", "svg", "mp3", "ogg", "wav", "mp4", "webm", "xhtml", "html", "htm", "xml", "tmx", "tsx", "json", "txt", "ttf", "otf", "AsyncQueueItem", "callback", "AsyncQueue", "worker", "concurrency", "workers", "saturated", "unsaturated", "empty", "drain", "started", "paused", "_tasks", "_insert", "insertAtFront", "idle", "item", "unshift", "process", "task", "shift", "_worker", "buffer", "kill", "running", "pause", "resume", "w", "eachSeries", "array", "iterator", "deferNext", "len", "next", "err", "queue", "rgxExtractUrlHash", "Loader", "baseUrl", "progress", "loading", "defaultQueryString", "_beforeMiddleware", "_afterMiddleware", "_resourcesParsing", "_boundLoadResource", "r", "d", "_loadResource", "resources", "_queue", "onError", "onLoad", "_plugins", "plugin", "pre", "use", "_protected", "_add", "parentResource", "_prepareUrl", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eachChunk", "reset", "k", "res", "_onStart", "_onComplete", "chunk", "set", "result", "path", "char<PERSON>t", "hash", "slice", "resource", "dequeue", "call", "_onLoad", "splice", "destroy", "shared", "_shared", "registerPlugin", "extensions", "ExtensionType", "ref", "core", "handleByList", "AppLoaderPlugin", "init", "assign", "<PERSON><PERSON><PERSON><PERSON>", "loader", "Application", "TextureLoader", "Texture", "fromLoader", "then", "texture", "catch", "parsing", "Blob", "Url_1", "URL", "webkitURL", "src_1", "createObjectURL", "blob", "revokeObjectURL", "getResponseHeader", "input", "output", "inx", "bytebuffer", "encodedCharIndexes", "jnx", "charCodeAt", "encodeBinary", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;qEAKA,IAAAA,EAAA,WAkBI,SAAAA,EAAYC,EAAYC,EAAcC,QAAd,IAAAD,IAAAA,GAAY,GAEhCE,KAAKC,IAAMJ,EACXG,KAAKE,MAAQJ,EACbE,KAAKG,SAAWJ,EAChBC,KAAKI,MAAQJ,KAAKK,MAAQL,KAAKM,OAAS,KAUhD,OAPIV,EAAAW,UAAAC,OAAA,WAEI,OAAoB,OAAhBR,KAAKM,SACTN,KAAKM,OAAOE,OAAOR,OAEZ,IAEdJ,KAOD,SAASa,EAA0BC,EAAsBC,GAgBrD,OAdKD,EAAKE,OAONF,EAAKG,MAAMT,MAAQO,EACnBA,EAAKN,MAAQK,EAAKG,MAClBH,EAAKG,MAAQF,IAPbD,EAAKE,MAAQD,EACbD,EAAKG,MAAQF,GASjBA,EAAKL,OAASI,EAEPC,EAMX,IC7DIG,ED6DJC,EAAA,WAaI,SAAAA,IAEIf,KAAKY,MAAQZ,KAAKa,WAAQG,EAqJlC,OA5IID,EAAQR,UAAAU,SAAR,SAASC,QAAA,IAAAA,IAAAA,GAAc,GAEnB,IAAIP,EAAOX,KAAKY,MAEhB,GAAIM,EAAQ,QAASP,EAIrB,IAFA,IAAMQ,EAAK,GAEJR,GAEHQ,EAAGC,KAAKT,GACRA,EAAOA,EAAKP,MAGhB,OAAOe,GAQXJ,EAAGR,UAAAc,IAAH,SAAIV,GAEA,KAAMA,aAAgBf,GAElB,MAAM,IAAI0B,MAAM,+DAGpB,OAAOX,EAAKL,SAAWN,MAQ3Be,EAAAR,UAAAgB,SAAA,+BAAuBC,EAAA,GAAAC,EAAA,EAAdA,EAAcC,UAAAC,OAAdF,IAAAD,EAAcC,GAAAC,EAAAD,GAEnB,IAAId,EAAOX,KAAKY,MAEhB,IAAKD,EAAM,OAAO,EAElB,KAAOA,GAECA,EAAKT,OAAOF,KAAKQ,OAAOG,GAC5BA,EAAKV,IAAI2B,MAAMjB,EAAKR,SAAUqB,GAC9Bb,EAAOA,EAAKP,MAGhB,OAAO,GASXW,EAAAR,UAAAsB,IAAA,SAAIhC,EAAYE,GAEZ,QAFY,IAAAA,IAAAA,EAAmB,MAEb,mBAAPF,EAEP,MAAM,IAAIyB,MAAM,mDAGpB,OAAOb,EAA0BT,KAAM,IAAIJ,EAAsBC,GAAI,EAAOE,KAShFgB,EAAAR,UAAAT,KAAA,SAAKD,EAAYE,GAEb,QAFa,IAAAA,IAAAA,EAAmB,MAEd,mBAAPF,EAEP,MAAM,IAAIyB,MAAM,oDAGpB,OAAOb,EAA0BT,KAAM,IAAIJ,EAAsBC,GAAI,EAAME,KAQ/EgB,EAAMR,UAAAC,OAAN,SAAOG,GAEH,KAAMA,aAAgBf,GAElB,MAAM,IAAI0B,MAAM,kEAEpB,OAAIX,EAAKL,SAAWN,OAEhBW,EAAKN,QAAOM,EAAKN,MAAMD,MAAQO,EAAKP,OACpCO,EAAKP,QAAOO,EAAKP,MAAMC,MAAQM,EAAKN,OAEpCM,IAASX,KAAKY,OAEdZ,KAAKY,MAAQD,EAAKP,MACC,OAAfO,EAAKP,QAELJ,KAAKa,MAAQ,OAGZF,IAASX,KAAKa,QAEnBb,KAAKa,MAAQF,EAAKN,MAClBL,KAAKa,MAAMT,MAAQ,MAGvBO,EAAKL,OAAS,MAnBmBN,MA4BrCe,EAAAR,UAAAuB,UAAA,WAEI,IAAInB,EAAOX,KAAKY,MAEhB,IAAKD,EAAM,OAAOX,KAIlB,IAFAA,KAAKY,MAAQZ,KAAKa,MAAQ,KAEnBF,GAEHA,EAAKL,OAAS,KACdK,EAAOA,EAAKP,MAGhB,OAAOJ,MAEde,KEjOe,SAAAgB,EAASC,EAAaC,GAElCA,EAAOA,GAAQ,GAqBf,IAnBA,IAAMC,EAAI,CAENC,IAAK,CAAC,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAC3IC,EAAG,CACCC,KAAM,WACNC,OAAQ,6BAEZA,OAAQ,CAEJC,OAAQ,0IAERC,MAAO,qMAITC,EAAIP,EAAEI,OAAOL,EAAKS,WAAa,SAAW,SAASC,KAAKX,GACxDY,EAAW,GACbC,EAAI,GAEDA,KAAKD,EAAIV,EAAEC,IAAIU,IAAMJ,EAAEI,IAAM,GAQpC,OANAD,EAAIV,EAAEE,EAAEC,MAAQ,GAChBO,EAAIV,EAAEC,IAAI,KAAKW,QAAQZ,EAAEE,EAAEE,QAAQ,SAACS,EAAUC,EAASC,GAE/CD,IAAIJ,EAAIV,EAAEE,EAAEC,MAAMW,GAAMC,MAGzBL,ED9BX,IAsuCgBM,EApBAC,EA1BAC,EApBAC,EAhCNC,EApoCNC,EAAkB,KAUtB,SAASC,KAUT,SAASC,EAAUC,EAAgBC,EAAiBC,GAE5CD,GAAoC,IAAzBA,EAAQE,QAAQ,OAE3BF,EAAUA,EAAQG,UAAU,IAG3BH,IAKLD,EAAIC,GAAWC,GASnB,SAASG,EAAQC,GAEb,OAAOA,EAAIC,WAAWnB,QAAQ,UAAW,IE9C7C,SAASU,KAUT,SAASU,EAASrE,GAEd,OAAO,+BAA4C2B,EAAA,GAAAC,EAAA,EAAZA,EAAYC,UAAAC,OAAZF,IAAAD,EAAYC,GAAAC,EAAAD,GAE/C,GAAW,OAAP5B,EAEA,MAAM,IAAIyB,MAAM,gCAGpB,IAAM6C,EAAStE,EAEfA,EAAK,KACLsE,EAAOvC,MAAM5B,KAAMwB,IFiE3B8B,EAAAA,eAAA,WA+NI,SAAAA,EAAYjB,EAAc+B,EAAwBC,GAQ9C,GA9FJrE,KAAQsE,SAAQd,EAOhBxD,KAAcuE,eAAQ,KAMdvE,KAAawE,cAAG,EAOhBxE,KAAcyE,eAAQ,KAOtBzE,KAAa0E,cAAQ,KAOrB1E,KAAgB2E,iBAAQ,KAOxB3E,KAAe4E,gBAAQ,KAEvB5E,KAAgB6E,iBAAQ,KACxB7E,KAAkB8E,mBAAQ,KAC1B9E,KAAgB+E,iBAAQ,KACxB/E,KAAegF,gBAAQ,KAgDP,iBAAT3C,GAAoC,iBAAR+B,EAEnC,MAAM,IAAI9C,MAAM,+DAGpB+C,EAAUA,GAAW,GAErBrE,KAAKiF,OAAS,EAGdjF,KAAKkF,SAAS5B,EAAeD,aAAa8B,SAAmC,IAAzBf,EAAIP,QAAQ,UAEhE7D,KAAKqC,KAAOA,EAEZrC,KAAKoE,IAAMA,EAEXpE,KAAKoF,UAAYpF,KAAKqF,gBAEtBrF,KAAKsF,KAAO,KAEZtF,KAAKuF,aAAsC,IAAxBlB,EAAQkB,YAAuB,YAAclB,EAAQkB,YAExEvF,KAAKwF,QAAUnB,EAAQmB,SAAW,EAElCxF,KAAKyF,SAAWpB,EAAQoB,UAAYzF,KAAK0F,qBAGzC1F,KAAK2F,QAAUtB,EAAQsB,QAKvB3F,KAAK4F,SAAWvB,EAAQuB,UAAY,GAGpC5F,KAAK6F,MAAQ,KAIb7F,KAAKgE,IAAM,KAGXhE,KAAK8F,SAAW,GAGhB9F,KAAK+F,KAAOzC,EAAeF,KAAK4C,QAGhChG,KAAKiG,cAAgB,EAIrBjG,KAAKsE,SAAWd,EAGhBxD,KAAKuE,eAAiB,KAGtBvE,KAAKwE,cAAgB,EAErBxE,KAAKyE,eAAiBzE,KAAKkG,SAASC,KAAKnG,MACzCA,KAAK0E,cAAgB1E,KAAKoG,SAASD,KAAKnG,MACxCA,KAAK2E,iBAAmB3E,KAAKqG,YAAYF,KAAKnG,MAC9CA,KAAK4E,gBAAkB5E,KAAKsG,WAAWH,KAAKnG,MAG5CA,KAAK6E,iBAAmB7E,KAAKuG,YAAYJ,KAAKnG,MAC9CA,KAAK8E,mBAAqB9E,KAAKwG,cAAcL,KAAKnG,MAClDA,KAAK+E,iBAAmB/E,KAAKyG,YAAYN,KAAKnG,MAC9CA,KAAKgF,gBAAkBhF,KAAK0G,WAAWP,KAAKnG,MAG5CA,KAAK2G,QAAU,IAAI5F,EAOnBf,KAAK4G,WAAa,IAAI7F,EAItBf,KAAK6G,WAAa,IAAI9F,EAGtBf,KAAK8G,kBAAoB,IAAI/F,EAgvBrC,OA92BWuC,EAAAyD,qBAAP,SAA4BpD,EAAiB8B,GAEzChC,EAAUH,EAAe0D,aAAcrD,EAAS8B,IAQ7CnC,EAAA2D,oBAAP,SAA2BtD,EAAiBgC,GAExClC,EAAUH,EAAe4D,YAAavD,EAASgC,IA4JnDwB,OAAAC,eAAI9D,EAAS/C,UAAA,YAAA,CAAb8G,IAAA,WAEI,OAAOrH,KAAKsH,SAAShE,EAAeD,aAAa8B,2CASrDgC,OAAAC,eAAI9D,EAAU/C,UAAA,aAAA,CAAd8G,IAAA,WAEI,OAAOrH,KAAKsH,SAAShE,EAAeD,aAAakE,2CASrDJ,OAAAC,eAAI9D,EAAS/C,UAAA,YAAA,CAAb8G,IAAA,WAEI,OAAOrH,KAAKsH,SAAShE,EAAeD,aAAamE,0CAIrDlE,EAAA/C,UAAA2F,SAAA,WAEIlG,KAAKyH,eACLzH,KAAK0H,WAOTpE,EAAK/C,UAAAoH,MAAL,SAAMC,GAGF,IAAI5H,KAAK6F,MAAT,CAYA,GANA7F,KAAK6F,MAAQ,IAAIvE,MAAMsG,GAGvB5H,KAAKyH,eAGDzH,KAAKgE,IAELhE,KAAKgE,IAAI2D,aAER,GAAI3H,KAAK6H,IAEV7H,KAAK6H,IAAIF,aAER,GAAI3H,KAAKsF,KAGV,GAAItF,KAAKsF,KAAKwC,IAEV9H,KAAKsF,KAAKwC,IAAMxE,EAAeyE,eAK/B,KAAO/H,KAAKsF,KAAK0C,YAEbhI,KAAKsF,KAAK2C,YAAYjI,KAAKsF,KAAK0C,YAM5ChI,KAAK0H,YAOTpE,EAAI/C,UAAA2H,KAAJ,SAAKC,GAAL,IAiECC,EAAApI,KA/DG,IAAIA,KAAKqI,UAKT,GAAIrI,KAAKsI,WAEDH,GAEAI,YAAW,WAAM,OAAAJ,EAAGC,KAAO,QAoBnC,OAfSD,GAELnI,KAAK6G,WAAW/G,KAAKqI,GAGzBnI,KAAKkF,SAAS5B,EAAeD,aAAamE,SAAS,GAEnDxH,KAAK2G,QAAQpF,SAASvB,OAGG,IAArBA,KAAKuF,aAAqD,iBAArBvF,KAAKuF,cAE1CvF,KAAKuF,YAAcvF,KAAKwI,sBAAsBxI,KAAKoE,MAG/CpE,KAAKyF,UAET,KAAKnC,EAAeH,UAAUsF,MAC1BzI,KAAK+F,KAAOzC,EAAeF,KAAKqF,MAChCzI,KAAK0I,aAAa,SAClB,MAEJ,KAAKpF,EAAeH,UAAUwF,MAC1B3I,KAAK+F,KAAOzC,EAAeF,KAAKuF,MAChC3I,KAAK4I,mBAAmB,SACxB,MAEJ,KAAKtF,EAAeH,UAAU0F,MAC1B7I,KAAK+F,KAAOzC,EAAeF,KAAKyF,MAChC7I,KAAK4I,mBAAmB,SACxB,MAEJ,KAAKtF,EAAeH,UAAU2F,IAE9B,aAC0B,IAAXhI,IAEPA,KAAaiI,WAAmBC,gBAAoB,wBAA0BC,iBAE9EnI,GAAUd,KAAKuF,YAEfvF,KAAKkJ,WAILlJ,KAAKmJ,aAWb7F,EAAQ/C,UAAA+G,SAAhB,SAAiB8B,GAEb,OAAgC,IAAxBpJ,KAAKiF,OAASmE,IAQlB9F,EAAA/C,UAAA2E,SAAR,SAAiBkE,EAAcC,GAE3BrJ,KAAKiF,OAASoE,EAASrJ,KAAKiF,OAASmE,EAASpJ,KAAKiF,QAAUmE,GAIzD9F,EAAA/C,UAAAkH,aAAR,WAEI6B,aAAatJ,KAAKwE,eAEdxE,KAAKsF,MAAQtF,KAAKsF,KAAKiE,sBAEvBvJ,KAAKsF,KAAKiE,oBAAoB,QAASvJ,KAAK0E,eAAe,GAC3D1E,KAAKsF,KAAKiE,oBAAoB,OAAQvJ,KAAKyE,gBAAgB,GAC3DzE,KAAKsF,KAAKiE,oBAAoB,WAAYvJ,KAAK2E,kBAAkB,GACjE3E,KAAKsF,KAAKiE,oBAAoB,iBAAkBvJ,KAAKyE,gBAAgB,IAGrEzE,KAAKgE,MAEDhE,KAAKgE,IAAIuF,qBAETvJ,KAAKgE,IAAIuF,oBAAoB,QAASvJ,KAAK6E,kBAAkB,GAC7D7E,KAAKgE,IAAIuF,oBAAoB,UAAWvJ,KAAK8E,oBAAoB,GACjE9E,KAAKgE,IAAIuF,oBAAoB,QAASvJ,KAAK+E,kBAAkB,GAC7D/E,KAAKgE,IAAIuF,oBAAoB,WAAYvJ,KAAK2E,kBAAkB,GAChE3E,KAAKgE,IAAIuF,oBAAoB,OAAQvJ,KAAKgF,iBAAiB,KAI3DhF,KAAKgE,IAAIwF,QAAU,KACnBxJ,KAAKgE,IAAIyF,UAAY,KACrBzJ,KAAKgE,IAAI0F,WAAa,KACtB1J,KAAKgE,IAAI2F,OAAS,QAMtBrG,EAAA/C,UAAAmH,QAAR,WAEI,GAAI1H,KAAKsI,WAEL,MAAM,IAAIhH,MAAM,4DAGpBtB,KAAKkF,SAAS5B,EAAeD,aAAakE,UAAU,GACpDvH,KAAKkF,SAAS5B,EAAeD,aAAamE,SAAS,GAEnDxH,KAAK6G,WAAWtF,SAASvB,OAS7BsD,EAAY/C,UAAAmI,aAAZ,SAAa3C,GAEL/F,KAAK4F,SAASgE,YAEd5J,KAAKsF,KAAOtF,KAAK4F,SAASgE,YAEZ,UAAT7D,QAAgD,IAArBgD,WAAWc,MAE3C7J,KAAKsF,KAAO,IAAIuE,MAIhB7J,KAAKsF,KAAOwE,SAASC,cAAchE,GAGnC/F,KAAKuF,cAELvF,KAAKsF,KAAKC,YAAcvF,KAAKuF,aAG5BvF,KAAK4F,SAASoE,aAEfhK,KAAKsF,KAAKwC,IAAM9H,KAAKoE,KAGzBpE,KAAKsF,KAAK2E,iBAAiB,QAASjK,KAAK0E,eAAe,GACxD1E,KAAKsF,KAAK2E,iBAAiB,OAAQjK,KAAKyE,gBAAgB,GACxDzE,KAAKsF,KAAK2E,iBAAiB,WAAYjK,KAAK2E,kBAAkB,GAE1D3E,KAAKwF,UAELxF,KAAKwE,cAAgB+D,WAAWvI,KAAK4E,gBAAiB5E,KAAKwF,WAS3DlC,EAAkB/C,UAAAqI,mBAA1B,SAA2B7C,GAevB,GAbI/F,KAAK4F,SAASgE,YAEd5J,KAAKsF,KAAOtF,KAAK4F,SAASgE,YAEZ,UAAT7D,QAAgD,IAArBgD,WAAWmB,MAE3ClK,KAAKsF,KAAO,IAAI4E,MAIhBlK,KAAKsF,KAAOwE,SAASC,cAAchE,GAGrB,OAAd/F,KAAKsF,KAAT,CAYA,GALItF,KAAKuF,cAELvF,KAAKsF,KAAKC,YAAcvF,KAAKuF,cAG5BvF,KAAK4F,SAASoE,WAGf,GAAKG,UAAkBC,WAEnBpK,KAAKsF,KAAKwC,IAAMuC,MAAMC,QAAQtK,KAAKoE,KAAOpE,KAAKoE,IAAI,GAAKpE,KAAKoE,SAE5D,GAAIiG,MAAMC,QAAQtK,KAAKoE,KAIxB,IAFA,IAAMmG,EAAYvK,KAAK4F,SAAS4E,SAEvB3H,EAAI,EAAGA,EAAI7C,KAAKoE,IAAIzC,SAAUkB,EAEnC7C,KAAKsF,KAAKmF,YACNzK,KAAK0K,cAAc3E,EAAM/F,KAAKoE,IAAIvB,GAAIwH,MAAMC,QAAQC,GAAaA,EAAU1H,GAAK0H,QAK5F,CACUA,EAAYvK,KAAK4F,SAAS4E,SAEhCxK,KAAKsF,KAAKmF,YACNzK,KAAK0K,cAAc3E,EAAM/F,KAAKoE,IAAKiG,MAAMC,QAAQC,GAAaA,EAAU,GAAKA,IAKzFvK,KAAKsF,KAAK2E,iBAAiB,QAASjK,KAAK0E,eAAe,GACxD1E,KAAKsF,KAAK2E,iBAAiB,OAAQjK,KAAKyE,gBAAgB,GACxDzE,KAAKsF,KAAK2E,iBAAiB,WAAYjK,KAAK2E,kBAAkB,GAC9D3E,KAAKsF,KAAK2E,iBAAiB,iBAAkBjK,KAAKyE,gBAAgB,GAElEzE,KAAKsF,KAAK4C,OAENlI,KAAKwF,UAELxF,KAAKwE,cAAgB+D,WAAWvI,KAAK4E,gBAAiB5E,KAAKwF,eA/C3DxF,KAAK2H,MAAM,wBAAwB5B,IAoDnCzC,EAAA/C,UAAA4I,SAAR,WAGgC,iBAAjBnJ,KAAK2F,UAEZ3F,KAAK2F,QAAU3F,KAAK2K,qBAGxB,IAAM3G,EAAMhE,KAAKgE,IAAM,IAAIiF,eAGF,oBAArBjJ,KAAKuF,cAELvB,EAAI4G,iBAAkB,GAI1B5G,EAAI6G,KAAK,MAAO7K,KAAKoE,KAAK,GAE1BJ,EAAIwB,QAAUxF,KAAKwF,QAIfxF,KAAK2F,UAAYrC,EAAeJ,kBAAkB4H,MAC/C9K,KAAK2F,UAAYrC,EAAeJ,kBAAkB6H,SAErD/G,EAAIgH,aAAe1H,EAAeJ,kBAAkB+H,KAIpDjH,EAAIgH,aAAehL,KAAK2F,QAG5B3B,EAAIiG,iBAAiB,QAASjK,KAAK6E,kBAAkB,GACrDb,EAAIiG,iBAAiB,UAAWjK,KAAK8E,oBAAoB,GACzDd,EAAIiG,iBAAiB,QAASjK,KAAK+E,kBAAkB,GACrDf,EAAIiG,iBAAiB,WAAYjK,KAAK2E,kBAAkB,GACxDX,EAAIiG,iBAAiB,OAAQjK,KAAKgF,iBAAiB,GAEnDhB,EAAIkH,QAIA5H,EAAA/C,UAAA2I,SAAR,WAGgC,iBAAjBlJ,KAAK2F,UAEZ3F,KAAK2F,QAAU3F,KAAK2K,qBAGxB,IAAM9C,EAAM7H,KAAKgE,IAAM,IAAK+E,WAAmBC,eAK/CnB,EAAIrC,QAAUxF,KAAKwF,SAAW,IAE9BqC,EAAI2B,QAAUxJ,KAAK6E,iBACnBgD,EAAI4B,UAAYzJ,KAAK8E,mBACrB+C,EAAI6B,WAAa1J,KAAK2E,iBACtBkD,EAAI8B,OAAS3J,KAAKgF,gBAElB6C,EAAIgD,KAAK,MAAO7K,KAAKoE,KAAK,GAM1BmE,YAAW,WAAM,OAAAV,EAAIqD,SAAQ,IAUzB5H,EAAA/C,UAAAmK,cAAR,SAAsB3E,EAAc3B,EAAa+G,GAExCA,IAEDA,EAAUpF,EAAQ,IAAA/F,KAAKqF,cAAcjB,IAGzC,IAAMgH,EAAStB,SAASC,cAAc,UAKtC,OAHAqB,EAAOtD,IAAM1D,EACbgH,EAAOrF,KAAOoF,EAEPC,GAOH9H,EAAQ/C,UAAA6F,SAAhB,SAAiBiF,GAEbrL,KAAK2H,MAAM,iCAAkC0D,EAAMC,OAAeC,WAO9DjI,EAAW/C,UAAA8F,YAAnB,SAAoBgF,GAEZA,GAASA,EAAMG,kBAEfxL,KAAK4G,WAAWrF,SAASvB,KAAMqL,EAAMI,OAASJ,EAAMK,QAKpDpI,EAAA/C,UAAA+F,WAAR,WAEItG,KAAK2H,MAAM,oBAIPrE,EAAA/C,UAAAgG,YAAR,WAEI,IAAMvC,EAAMhE,KAAKgE,IAEjBhE,KAAK2H,MAAS5D,EAAQC,GAAI,4BAA4BA,EAAI2H,OAAkB,YAAA3H,EAAI4H,WAAU,MAItFtI,EAAA/C,UAAAiG,cAAR,WAEI,IAAMxC,EAAMhE,KAAKgE,IAEjBhE,KAAK2H,MAAS5D,EAAQC,GAAyB,wBAI3CV,EAAA/C,UAAAkG,YAAR,WAEI,IAAMzC,EAAMhE,KAAKgE,IAEjBhE,KAAK2H,MAAS5D,EAAQC,GAAuC,sCAIzDV,EAAA/C,UAAAmG,WAAR,WAEI,IAAM1C,EAAMhE,KAAKgE,IACb6H,EAAO,GACPF,OAA+B,IAAf3H,EAAI2H,OA55Bd,IA45BmD3H,EAAI2H,OAsBjE,GAnByB,KAArB3H,EAAIgH,cAA4C,SAArBhH,EAAIgH,mBAAuD,IAArBhH,EAAIgH,eAErEa,EAAO7H,EAAI8H,cAl6BH,IAu6BRH,IAA2BE,EAAKlK,OAAS,GAAKqC,EAAIgH,eAAiB1H,EAAeJ,kBAAkB6I,QAEpGJ,EAx6BM,IAEU,OAy6BXA,IAELA,EA56BS,KAEE,KA66BKA,EAAS,IAAO,GAEpC,CAGI,GAAI3L,KAAK2F,UAAYrC,EAAeJ,kBAAkB+H,KAElDjL,KAAKsF,KAAOuG,EACZ7L,KAAK+F,KAAOzC,EAAeF,KAAK6H,UAG/B,GAAIjL,KAAK2F,UAAYrC,EAAeJ,kBAAkB4H,KAEvD,IAEI9K,KAAKsF,KAAOwF,KAAKkB,MAAMH,GACvB7L,KAAK+F,KAAOzC,EAAeF,KAAK0H,KAEpC,MAAOmB,GAIH,YAFAjM,KAAK2H,MAAM,sCAAsCsE,QAMpD,GAAIjM,KAAK2F,UAAYrC,EAAeJ,kBAAkB6H,SAEvD,IAEI,GAAIhC,WAAWmD,UACf,CACI,IAAMC,EAAY,IAAID,UAEtBlM,KAAKsF,KAAO6G,EAAUC,gBAAgBP,EAAM,gBAGhD,CACI,IAAMQ,EAAMvC,SAASC,cAAc,OAEnCsC,EAAIC,UAAYT,EAEhB7L,KAAKsF,KAAO+G,EAGhBrM,KAAK+F,KAAOzC,EAAeF,KAAKmJ,IAEpC,MAAON,GAIH,YAFAjM,KAAK2H,MAAM,qCAAqCsE,QAQpDjM,KAAKsF,KAAOtB,EAAIwI,UAAYX,EAUpC7L,KAAKkG,gBALDlG,KAAK2H,MAAM,IAAI3D,EAAI2H,OAAM,KAAK3H,EAAI4H,WAAe,KAAA5H,EAAIyI,cAkB7DnJ,EAAA/C,UAAAiI,sBAAA,SAAsBpE,EAAasI,GAG/B,GAA6B,IAAzBtI,EAAIP,QAAQ,SAEZ,MAAO,GAMX,GAAIkF,WAAW4D,SAAW5D,WAAW6D,SAASD,OAE1C,MAAO,YAIXD,EAAMA,GAAO3D,WAAW6D,SAEnBrJ,IAEDA,EAAauG,SAASC,cAAc,MAMxCxG,EAAWsJ,KAAOzI,EAClB,IAAM0I,EAAY/K,EAASwB,EAAWsJ,KAAM,CAAEnK,YAAY,IAEpDqK,GAAaD,EAAUE,MAAqB,KAAbN,EAAIM,MAAiBF,EAAUE,OAASN,EAAIM,KAC3EC,EAAWH,EAAUG,SAAcH,EAAUG,SAAW,IAAG,GAGjE,OAAIH,EAAUI,OAASR,EAAIS,UAAaJ,GAAYE,IAAaP,EAAIO,SAK9D,GAHI,aAYP3J,EAAA/C,UAAAoK,kBAAR,WAEI,OAAOrH,EAAe4D,YAAYlH,KAAKoF,YAAc9B,EAAeJ,kBAAkB+H,MASlF3H,EAAA/C,UAAAmF,mBAAR,WAEI,OAAOpC,EAAe0D,aAAahH,KAAKoF,YAAc9B,EAAeH,UAAU2F,KAQ3ExF,EAAa/C,UAAA8E,cAArB,SAAsBjB,QAAA,IAAAA,IAAAA,EAAMpE,KAAKoE,KAE7B,IAAIgJ,EAAM,GAEV,GAAIpN,KAAKqN,UACT,CACI,IAAMC,EAAalJ,EAAIP,QAAQ,KAE/BuJ,EAAMhJ,EAAIN,UAAUwJ,EAAa,EAAGlJ,EAAIP,QAAQ,IAAKyJ,QAGzD,CACI,IAAMC,EAAanJ,EAAIP,QAAQ,KACzB2J,EAAYpJ,EAAIP,QAAQ,KACxB4J,EAAQC,KAAKC,IACfJ,GAAc,EAAIA,EAAanJ,EAAIzC,OACnC6L,GAAa,EAAIA,EAAYpJ,EAAIzC,QAIrCyL,GADAhJ,EAAMA,EAAIN,UAAU,EAAG2J,IACb3J,UAAUM,EAAIwJ,YAAY,KAAO,GAG/C,OAAOR,EAAIS,eAUfvK,EAAmB/C,UAAAuN,oBAAnB,SAAoB/H,GAEhB,OAAQA,GAEJ,KAAKzC,EAAeJ,kBAAkB6I,OAClC,MAAO,2BAEX,KAAKzI,EAAeJ,kBAAkB6K,KAClC,MAAO,mBAEX,KAAKzK,EAAeJ,kBAAkB6H,SAClC,MAAO,kBAEX,KAAKzH,EAAeJ,kBAAkB4H,KAClC,MAAO,mBAEX,KAAKxH,EAAeJ,kBAAkB8K,QACtC,KAAK1K,EAAeJ,kBAAkB+H,KAEtC,QACI,MAAO,eAGtB3H,KAGSA,EAAAA,EAAcA,iBAAdA,iBAoLT,KApJeD,EAAAC,EAAYD,eAAZC,eAWX,KAPGD,EAAA,KAAA,GAAA,OAEAA,EAAAA,EAAA,SAAA,GAAA,WAEAA,EAAAA,EAAA,SAAA,GAAA,WAEAA,EAAAA,EAAA,QAAA,GAAA,WAUQD,EAAAE,EAAIF,OAAJE,OAiBX,KAbGF,EAAA,QAAA,GAAA,UAEAA,EAAAA,EAAA,KAAA,GAAA,OAEAA,EAAAA,EAAA,IAAA,GAAA,MAEAA,EAAAA,EAAA,MAAA,GAAA,QAEAA,EAAAA,EAAA,MAAA,GAAA,QAEAA,EAAAA,EAAA,MAAA,GAAA,QAEAA,EAAAA,EAAA,KAAA,GAAA,QAUQD,EAAAG,EAASH,YAATG,YAWX,KAPGH,EAAA,IAAA,GAAA,MAEAA,EAAAA,EAAA,MAAA,GAAA,QAEAA,EAAAA,EAAA,MAAA,GAAA,QAEAA,EAAAA,EAAA,MAAA,GAAA,SAUQD,EAAAI,EAAiBJ,oBAAjBI,oBAeX,KAXG,QAAA,OAEAJ,EAAA,OAAA,cAEAA,EAAA,KAAA,OAEAA,EAAA,SAAA,WAEAA,EAAA,KAAA,OAEAA,EAAA,KAAA,OAGSI,EAAA0D,aAA6B,CAEtCiH,IAAK3K,EAAeH,UAAUsF,MAC9ByF,IAAK5K,EAAeH,UAAUsF,MAC9B0F,IAAK7K,EAAeH,UAAUsF,MAC9B2F,IAAK9K,EAAeH,UAAUsF,MAC9B4F,KAAM/K,EAAeH,UAAUsF,MAC/B6F,IAAKhL,EAAeH,UAAUsF,MAC9B8F,KAAMjL,EAAeH,UAAUsF,MAC/B+F,KAAMlL,EAAeH,UAAUsF,MAC/BgG,IAAKnL,EAAeH,UAAUsF,MAC9BiG,KAAMpL,EAAeH,UAAUsF,MAC/BkG,IAAKrL,EAAeH,UAAUsF,MAC9B,UAAWnF,EAAeH,UAAUsF,MAGpCmG,IAAKtL,EAAeH,UAAUwF,MAC9BkG,IAAKvL,EAAeH,UAAUwF,MAC9BmG,IAAKxL,EAAeH,UAAUwF,MAG9BoG,IAAKzL,EAAeH,UAAU0F,MAC9BmG,KAAM1L,EAAeH,UAAU0F,OAGtBvF,EAAA4D,YAAuC,CAEhD+H,MAAO3L,EAAeJ,kBAAkB6H,SACxCmE,KAAM5L,EAAeJ,kBAAkB6H,SACvCoE,IAAK7L,EAAeJ,kBAAkB6H,SACtCqE,IAAK9L,EAAeJ,kBAAkB6H,SACtCsE,IAAK/L,EAAeJ,kBAAkB6H,SACtC4D,IAAKrL,EAAeJ,kBAAkB6H,SAKtCuE,IAAKhM,EAAeJ,kBAAkB6H,SAGtCkD,IAAK3K,EAAeJ,kBAAkB6K,KACtCG,IAAK5K,EAAeJ,kBAAkB6K,KACtCI,IAAK7K,EAAeJ,kBAAkB6K,KACtCK,IAAK9K,EAAeJ,kBAAkB6K,KACtCM,KAAM/K,EAAeJ,kBAAkB6K,KACvCO,IAAKhL,EAAeJ,kBAAkB6K,KACtCQ,KAAMjL,EAAeJ,kBAAkB6K,KACvCS,KAAMlL,EAAeJ,kBAAkB6K,KACvCU,IAAKnL,EAAeJ,kBAAkB6K,KACtCW,KAAMpL,EAAeJ,kBAAkB6K,KAGvCwB,KAAMjM,EAAeJ,kBAAkB4H,KAGvCe,KAAMvI,EAAeJ,kBAAkB+H,KACvCuE,IAAKlM,EAAeJ,kBAAkB+H,KAGtCwE,IAAKnM,EAAeJ,kBAAkB6I,OACtC2D,IAAKpM,EAAeJ,kBAAkB6I,QAI7BzI,EAASyE,UAAG,qFEtxC7B,IAAA4H,EAUI,SAAYrK,EAAgBsK,GAExB5P,KAAKsF,KAAOA,EACZtF,KAAK4P,SAAWA,GAQxBC,EAAA,WAwBI,SAAYA,EAAAC,EAAiDC,GAA7D,IAWC3H,EAAApI,KAPG,QAJyD,IAAA+P,IAAAA,EAAe,GAtB5E/P,KAAOgQ,QAAG,EAKVhQ,KAASiQ,UAAezM,EACxBxD,KAAWkQ,YAAe1M,EAC1BxD,KAAKmQ,MAAe3M,EACpBxD,KAAKoQ,MAAe5M,EACpBxD,KAAK6F,MAAyCrC,EAE9CxD,KAAOqQ,SAAG,EACVrQ,KAAMsQ,QAAG,EAGTtQ,KAAMuQ,OAAoC,GAoBlCvQ,KAAAwQ,QAAU,SAAClL,EAAWmL,EAAwBb,GAElD,GAAIA,GAAgC,mBAAbA,EAEnB,MAAM,IAAItO,MAAM,oCAMpB,GAHA8G,EAAKiI,SAAU,EAGH,MAAR/K,GAAgB8C,EAAKsI,OAGrBnI,YAAW,WAAM,OAAAH,EAAKgI,UAAS,OAHnC,CAQA,IAAMO,EAAO,IAAIhB,EACbrK,EACoB,mBAAbsK,EAA0BA,EAAWpM,GAG5CiN,EAEArI,EAAKmI,OAAOK,QAAQD,GAIpBvI,EAAKmI,OAAOnP,KAAKuP,GAGrBpI,WAAWH,EAAKyI,QAAS,KAG7B7Q,KAAA6Q,QAAU,WAEN,MAAQzI,EAAKkI,QAAUlI,EAAK4H,QAAU5H,EAAK2H,aAAe3H,EAAKmI,OAAO5O,QACtE,CACI,IAAMmP,EAAO1I,EAAKmI,OAAOQ,QAEE,IAAvB3I,EAAKmI,OAAO5O,QAEZyG,EAAK+H,QAGT/H,EAAK4H,SAAW,EAEZ5H,EAAK4H,UAAY5H,EAAK2H,aAEtB3H,EAAK6H,YAGT7H,EAAK4I,QAAQF,EAAKxL,KAAMpB,EAASkE,EAAKhI,MAAM0Q,OAhEhD9Q,KAAKgR,QAAUlB,EAEK,IAAhBC,EAEA,MAAM,IAAIzO,MAAM,gCAGpBtB,KAAK+P,YAAcA,EACnB/P,KAAKiR,OAASlB,EAAc,EA+MpC,OA/IIF,EAAKtP,UAAAH,MAAL,SAAM0Q,GAAN,IA0BC1I,EAAApI,KAxBG,OAAO,+BAAawB,EAAA,GAAAC,EAAA,EAAZA,EAAYC,UAAAC,OAAZF,IAAAD,EAAYC,GAAAC,EAAAD,GAEhB2G,EAAK4H,SAAW,EAEhBc,EAAKlB,SAAQhO,MAAbkP,EAAiBtP,GAGF,MAAXA,EAAK,IAEL4G,EAAKvC,MAAMrE,EAAK,GAAIsP,EAAKxL,MAGzB8C,EAAK4H,SAAY5H,EAAK2H,YAAc3H,EAAK6I,QAEzC7I,EAAK8H,cAGL9H,EAAKsI,QAELtI,EAAKgI,QAGThI,EAAKyI,YAObhB,EAAAtP,UAAAa,KAAA,SAAKkE,EAAWsK,GAEZ5P,KAAKwQ,QAAQlL,GAAM,EAAOsK,IAG9BC,EAAAtP,UAAA2Q,KAAA,WAEIlR,KAAKgQ,QAAU,EACfhQ,KAAKoQ,MAAQ5M,EACbxD,KAAKqQ,SAAU,EACfrQ,KAAKuQ,OAAS,IAIlBV,EAAAtP,UAAAqQ,QAAA,SAAQtL,EAAWsK,GAEf5P,KAAKwQ,QAAQlL,GAAM,EAAMsK,IAG7BC,EAAAtP,UAAAoB,OAAA,WAEI,OAAO3B,KAAKuQ,OAAO5O,QAGvBkO,EAAAtP,UAAA4Q,QAAA,WAEI,OAAOnR,KAAKgQ,SAGhBH,EAAAtP,UAAAmQ,KAAA,WAEI,OAAO1Q,KAAKuQ,OAAO5O,OAAS3B,KAAKgQ,UAAY,GAGjDH,EAAAtP,UAAA6Q,MAAA,YAEwB,IAAhBpR,KAAKsQ,SAKTtQ,KAAKsQ,QAAS,IAGlBT,EAAAtP,UAAA8Q,OAAA,WAEI,IAAoB,IAAhBrR,KAAKsQ,OAAT,CAKAtQ,KAAKsQ,QAAS,EAId,IAAK,IAAIgB,EAAI,EAAGA,GAAKtR,KAAK+P,YAAauB,IAEnCtR,KAAK6Q,YAWNhB,EAAU0B,WAAjB,SAAkBC,EAAmBC,EACjC7B,EAAgC8B,GAEhC,IAAI7O,EAAI,EACF8O,EAAMH,EAAM7P,QAElB,SAASiQ,EAAKC,GAENA,GAAOhP,IAAM8O,EAET/B,GAEAA,EAASiC,GAMbH,EAEAnJ,YAAW,WAEPkJ,EAASD,EAAM3O,KAAM+O,KACtB,GAIHH,EAASD,EAAM3O,KAAM+O,GAI7BA,IASG/B,EAAAiC,MAAP,SAAahC,EAAwDC,GAEjE,OAAO,IAAIF,EAAgBC,EAAQC,IAE1CF,KCnSKkC,EAAoB,cA2F1BC,EAAA,WAyEI,SAAYA,EAAAC,EAAclC,GAA1B,IAiCC3H,EAAApI,UAjCW,IAAAiS,IAAAA,EAAY,SAAE,IAAAlC,IAAAA,EAAgB,IAnE1C/P,KAAQkS,SAAG,EAGXlS,KAAOmS,SAAG,EAqBVnS,KAAkBoS,mBAAG,GAGbpS,KAAiBqS,kBAA6B,GAG9CrS,KAAgBsS,iBAA6B,GAG7CtS,KAAiBuS,kBAA0B,GAO3CvS,KAAAwS,mBAAqB,SAACC,EAAmBC,GAAwB,OAAAtK,EAAKuK,cAAcF,EAAGC,IAM/F1S,KAAS4S,UAAyB,GAuB9B5S,KAAKiS,QAAUA,EACfjS,KAAKqS,kBAAoB,GACzBrS,KAAKsS,iBAAmB,GACxBtS,KAAKuS,kBAAoB,GACzBvS,KAAKwS,mBAAqB,SAACC,EAAGC,GAAM,OAAAtK,EAAKuK,cAAcF,EAAGC,IAC1D1S,KAAK6S,OAAShD,EAAWiC,MAAM9R,KAAKwS,mBAAoBzC,GACxD/P,KAAK6S,OAAOzB,QACZpR,KAAK4S,UAAY,GACjB5S,KAAK4G,WAAa,IAAI7F,EACtBf,KAAK8S,QAAU,IAAI/R,EACnBf,KAAK+S,OAAS,IAAIhS,EAClBf,KAAK2G,QAAU,IAAI5F,EACnBf,KAAK6G,WAAa,IAAI9F,EAEtB,IAAK,IAAI8B,EAAI,EAAGA,EAAImP,EAAOgB,SAASrR,SAAUkB,EAC9C,CACI,IAAMoQ,EAASjB,EAAOgB,SAASnQ,GACvBqQ,EAAaD,EAAMC,IAAdC,EAAQF,EAAME,IAEvBD,GAEAlT,KAAKkT,IAAIA,GAGTC,GAEAnT,KAAKmT,IAAIA,GAIjBnT,KAAKoT,YAAa,EAyb1B,OA/XcpB,EAAIzR,UAAA8S,KAAd,SAAehR,EAAc+B,EAAaC,EAAsBuL,GAG5D,GAAI5P,KAAKmS,WAAa9N,IAAYA,EAAQiP,gBAEtC,MAAM,IAAIhS,MAAM,qDAIpB,GAAItB,KAAK4S,UAAUvQ,GAEf,MAAM,IAAIf,MAAM,mBAAmBe,EAAI,qBAe3C,GAXA+B,EAAMpE,KAAKuT,YAAYnP,GAGvBpE,KAAK4S,UAAUvQ,GAAQ,IAAIiB,EAAcA,eAACjB,EAAM+B,EAAKC,GAE7B,mBAAbuL,GAEP5P,KAAK4S,UAAUvQ,GAAMyE,kBAAkBhH,KAAK8P,GAI5C5P,KAAKmS,QACT,CAII,IAHA,IAAMqB,EAASnP,EAAQiP,eACjBG,EAAqB,GAElB5Q,EAAI,EAAGA,EAAI2Q,EAAO1N,SAASnE,SAAUkB,EAErC2Q,EAAO1N,SAASjD,GAAGyF,YAEpBmL,EAAmBrS,KAAKoS,EAAO1N,SAASjD,IAIhD,IACM6Q,EADYF,EAAOvN,eAAiBwN,EAAmB9R,OAAS,IACvC8R,EAAmB9R,OAAS,GAE3D6R,EAAO1N,SAAS1E,KAAKpB,KAAK4S,UAAUvQ,IACpCmR,EAAOvN,cAAgByN,EAEvB,IAAS7Q,EAAI,EAAGA,EAAI4Q,EAAmB9R,SAAUkB,EAE7C4Q,EAAmB5Q,GAAGoD,cAAgByN,EAG1C1T,KAAK4S,UAAUvQ,GAAM4D,cAAgByN,EAMzC,OAFA1T,KAAK6S,OAAOzR,KAAKpB,KAAK4S,UAAUvQ,IAEzBrC,MAWXgS,EAAGzR,UAAA2S,IAAH,SAAIrT,GAIA,OAFAG,KAAKqS,kBAAkBjR,KAAKvB,GAErBG,MASXgS,EAAGzR,UAAA4S,IAAH,SAAItT,GAIA,OAFAG,KAAKsS,iBAAiBlR,KAAKvB,GAEpBG,MAOXgS,EAAAzR,UAAAoT,MAAA,WASI,IAAK,IAAMC,KAPX5T,KAAKkS,SAAW,EAChBlS,KAAKmS,SAAU,EAEfnS,KAAK6S,OAAO3B,OACZlR,KAAK6S,OAAOzB,QAGIpR,KAAK4S,UACrB,CACI,IAAMiB,EAAM7T,KAAK4S,UAAUgB,GAEvBC,EAAItP,gBAEJsP,EAAItP,eAAe/D,SAGnBqT,EAAIxL,WAEJwL,EAAIlM,MAAM,gBAMlB,OAFA3H,KAAK4S,UAAY,GAEV5S,MAQXgS,EAAIzR,UAAA2H,KAAJ,SAAKC,GAaD,GANkB,mBAAPA,GAEPnI,KAAK6G,WAAW/G,KAAKqI,GAIrBnI,KAAKmS,QAEL,OAAOnS,KAGX,GAAIA,KAAK6S,OAAOnC,OAEZ1Q,KAAK8T,WACL9T,KAAK+T,kBAGT,CAKI,IAHA,IACMC,EAvZG,IAsZQhU,KAAK6S,OAAOtC,OAAO5O,OAG3BkB,EAAI,EAAGA,EAAI7C,KAAK6S,OAAOtC,OAAO5O,SAAUkB,EAE7C7C,KAAK6S,OAAOtC,OAAO1N,GAAGyC,KAAKW,cAAgB+N,EAI/ChU,KAAK8T,WAGL9T,KAAK6S,OAAOxB,SAGhB,OAAOrR,MAOXmH,OAAAC,eAAI4K,EAAWzR,UAAA,cAAA,CAAf8G,IAAA,WAEI,OAAOrH,KAAK6S,OAAO9C,aAEvBkE,IAAA,SAAgBlE,GAEZ/P,KAAK6S,OAAO9C,YAAcA,mCAQtBiC,EAAWzR,UAAAgT,YAAnB,SAAoBnP,GAEhB,IACI8P,EADEpH,EAAY/K,EAASqC,EAAK,CAAE1B,YAAY,IAsB9C,GAhBIwR,EAFApH,EAAUG,WAAaH,EAAUqH,MAA8B,IAAtB/P,EAAIP,QAAQ,MAE5CO,EAGJpE,KAAKiS,QAAQtQ,QACf3B,KAAKiS,QAAQrE,YAAY,OAAS5N,KAAKiS,QAAQtQ,OAAS,GACtC,MAAlByC,EAAIgQ,OAAO,GAGFpU,KAAKiS,QAAO,IAAI7N,EAInBpE,KAAKiS,QAAU7N,EAIxBpE,KAAKoS,mBACT,CACI,IAAMiC,EAAOtC,EAAkBpP,KAAKuR,GAAQ,IAIf,KAF7BA,EAASA,EAAOI,MAAM,EAAGJ,EAAOvS,OAAS0S,EAAK1S,SAEnCkC,QAAQ,KAEfqQ,GAAU,IAAIlU,KAAKoS,mBAInB8B,GAAU,IAAIlU,KAAKoS,mBAGvB8B,GAAUG,EAGd,OAAOH,GAQHlC,EAAAzR,UAAAoS,cAAR,SAAsB4B,EAA0BC,GAAhD,IA8BCpM,EAAApI,KA5BGuU,EAASjQ,SAAWkQ,EAGpB3E,EAAW0B,WACPvR,KAAKqS,mBACL,SAACxS,EAAS+R,GAEN/R,EAAG4U,KAAKrM,EAAMmM,GAAU,WAIpB3C,EAAK2C,EAASjM,WAAa,GAAK,YAGxC,WAEQiM,EAASjM,WAETF,EAAKsM,QAAQH,IAIbA,EAAShQ,eAAiBgQ,EAAS1N,WAAW/G,KAAKsI,EAAKsM,QAAStM,GACjEmM,EAASrM,WAGjB,IAKA8J,EAAAzR,UAAAuT,SAAR,WAEI9T,KAAKkS,SAAW,EAChBlS,KAAKmS,SAAU,EACfnS,KAAK2G,QAAQpF,SAASvB,OAIlBgS,EAAAzR,UAAAwT,YAAR,WAEI/T,KAAKkS,SAvhBQ,IAwhBblS,KAAKmS,SAAU,EACfnS,KAAK6G,WAAWtF,SAASvB,KAAMA,KAAK4S,YAOhCZ,EAAOzR,UAAAmU,QAAf,SAAgBH,GAAhB,IAyCCnM,EAAApI,KAvCGuU,EAAShQ,eAAiB,KAG1BvE,KAAKuS,kBAAkBnR,KAAKmT,GAC5BA,EAASjQ,WAGTuL,EAAW0B,WACPvR,KAAKsS,kBACL,SAACzS,EAAS+R,GAEN/R,EAAG4U,KAAKrM,EAAMmM,EAAU3C,MAE5B,WAEI2C,EAASzN,kBAAkBvF,SAASgT,GAEpCnM,EAAK8J,SAAWxE,KAAKC,IAnjBhB,IAmjBkCvF,EAAK8J,SAAWqC,EAAStO,eAChEmC,EAAKxB,WAAWrF,SAAS6G,EAAMmM,GAE3BA,EAAS1O,MAETuC,EAAK0K,QAAQvR,SAASgT,EAAS1O,MAAOuC,EAAMmM,GAI5CnM,EAAK2K,OAAOxR,SAAS6G,EAAMmM,GAG/BnM,EAAKmK,kBAAkBoC,OAAOvM,EAAKmK,kBAAkB1O,QAAQ0Q,GAAW,GAGpEnM,EAAKyK,OAAOnC,QAA4C,IAAlCtI,EAAKmK,kBAAkB5Q,QAE7CyG,EAAK2L,iBAGb,IAaD/B,EAAAzR,UAAAqU,QAAP,WAES5U,KAAKoT,YAENpT,KAAK2T,SAKbxM,OAAAC,eAAkB4K,EAAM,SAAA,CAAxB3K,IAAA,WAEI,IAAIwN,EAAS7C,EAAO8C,QASpB,OAPKD,KAEDA,EAAS,IAAI7C,GACNoB,YAAa,EACpBpB,EAAO8C,QAAUD,GAGdA,mCASG7C,EAAc+C,eAA5B,SAA6B9B,GAWzB,OALA+B,EAAAA,WAAWnT,IAAI,CACXkE,KAAMkP,EAAaA,cAACjD,OACpBkD,IAAKjC,IAGFjB,GAjDJA,EAAQgB,SAAyB,GAmD3ChB,KAESmD,EAAAH,WAACI,aAAaH,EAAaA,cAACjD,OAAQA,EAAOgB,UAErDhB,EAAOzR,UAAUsB,IAAM,SAA2BQ,EAAW+B,EAAWC,EAAeuL,GAGnF,GAAIvF,MAAMC,QAAQjI,GAClB,CACI,IAAK,IAAIQ,EAAI,EAAGA,EAAIR,EAAKV,SAAUkB,EAE/B7C,KAAK6B,IAAKQ,EAAaQ,IAG3B,OAAO7C,KAqBX,GAjBoB,iBAATqC,IAEPgC,EAAUhC,EACVuN,EAAYxL,GAAeC,EAAQuL,UAAYvL,EAAQwC,WACvDzC,EAAMC,EAAQD,IACd/B,EAAOgC,EAAQhC,MAAQgC,EAAQlC,KAAOkC,EAAQD,KAI/B,iBAARA,IAEPwL,EAAWvL,EACXA,EAAUD,EACVA,EAAM/B,GAIS,iBAAR+B,EAEP,MAAM,IAAI9C,MAAM,4CAUpB,MANuB,mBAAZ+C,IAEPuL,EAAWvL,EACXA,EAAU,MAGPrE,KAAKqT,KAAKhR,EAAM+B,EAAKC,EAASuL,IC1qBzC,IAAAyF,EAAA,WAAA,SAAAA,KAsCA,OArBWA,EAAIC,KAAX,SAAYjR,GAERA,EAAU8C,OAAOoO,OAAO,CACpBC,cAAc,GACfnR,GAEHrE,KAAKyV,OAASpR,EAAQmR,aAAexD,EAAO6C,OAAS,IAAI7C,GAOtDqD,EAAAT,QAAP,WAEQ5U,KAAKyV,SAELzV,KAAKyV,OAAOb,UACZ5U,KAAKyV,OAAS,OAhCfJ,EAAAjQ,UAA+B6P,EAAaA,cAACS,YAmCvDL,KC3CDM,EAAA,WAAA,SAAAA,KAuCA,OAjCkBA,EAAA9T,IAAd,WAEIyB,EAAcA,eAACyD,qBAAqB,MAAOzD,EAAcA,eAACH,UAAU2F,KACpExF,EAAcA,eAAC2D,oBAAoB,MAAO3D,EAAcA,eAACJ,kBAAkB+H,OASjE0K,EAAAxC,IAAd,SAAkBoB,EAA0B3C,GAGxC,IAAI2C,EAASjP,MAASiP,EAASxO,OAASzC,EAAAA,eAAeF,KAAKqF,OAAgC,QAAvB8L,EAASnP,UAe1EwM,QAdJ,CACY,IAAAtM,EAA8BiP,OAAxBnQ,EAAwBmQ,EAAQnQ,IAA3B/B,EAAmBkS,EAAflS,KAAEuD,EAAa2O,WAEtCqB,UAAQC,WAAWvQ,EAAMlB,EAAK/B,EAAMuD,GAAUkQ,MAAK,SAACC,GAEhDxB,EAASwB,QAAUA,EACnBnE,OAICoE,MAAMpE,KA7BZ+D,EAAAvQ,UAA+B6P,EAAaA,cAACjD,OAoCvD2D,KCjCe,SAAAM,EAAQ1B,EAA0B3C,GAE9C,GAAK2C,EAASjP,KAAd,CAQA,GAAIiP,EAASvQ,KAAOuQ,EAAS5O,UAAYrC,EAAcA,eAACJ,kBAAkB6K,KAGtE,GAAKrN,KAAKwV,MAAiC,iBAAlB3B,EAASjP,MAyB7B,GAA4C,IAAxCiP,EAASjP,KAAKS,KAAKlC,QAAQ,SACpC,CACI,IAAMsS,EAAMpN,WAAWqN,KAAOrN,WAAWsN,UACnCC,EAAMH,EAAII,gBAAgBhC,EAASjP,MAmBzC,OAjBAiP,EAASiC,KAAOjC,EAASjP,KACzBiP,EAASjP,KAAO,IAAIuE,MACpB0K,EAASjP,KAAKwC,IAAMwO,EAEpB/B,EAASxO,KAAOzC,iBAAeF,KAAKqF,WAIpC8L,EAASjP,KAAKqE,OAAS,WAEnBwM,EAAIM,gBAAgBH,GACpB/B,EAASjP,KAAKqE,OAAS,KAEvBiI,WA1CR,CACI,IAAM7L,EAAOwO,EAASvQ,IAAI0S,kBAAkB,gBAG5C,GAAI3Q,GAAkC,IAA1BA,EAAKlC,QAAQ,SAgBrB,OAdA0Q,EAASjP,KAAO,IAAIuE,MACpB0K,EAASjP,KAAKwC,IAAM,QAAQ/B,EAAI,WC3B1C,SAAuB4Q,GAKzB,IAHA,IAAIC,EAAS,GACTC,EAAM,EAEHA,EAAMF,EAAMhV,QACnB,CAKI,IAHA,IAAMmV,EAAa,CAAC,EAAG,EAAG,GACpBC,EAAqB,CAAC,EAAG,EAAG,EAAG,GAE5BC,EAAM,EAAGA,EAAMF,EAAWnV,SAAUqV,EAErCH,EAAMF,EAAMhV,OAIZmV,EAAWE,GAAiC,IAA1BL,EAAMM,WAAWJ,KAInCC,EAAWE,GAAO,EAoB1B,OAdAD,EAAmB,GAAKD,EAAW,IAAM,EAGzCC,EAAmB,IAAuB,EAAhBD,EAAW,KAAa,EAAMA,EAAW,IAAM,EAGzEC,EAAmB,IAAuB,GAAhBD,EAAW,KAAc,EAAMA,EAAW,IAAM,EAG1EC,EAAmB,GAAqB,GAAhBD,EAAW,GAGdD,GAAOF,EAAMhV,OAAS,IAIvC,KAAK,EAEDoV,EAAmB,GAAK,GACxBA,EAAmB,GAAK,GACxB,MAEJ,KAAK,EAEDA,EAAmB,GAAK,GAShC,IAASC,EAAM,EAAGA,EAAMD,EAAmBpV,SAAUqV,EAEjDJ,GAtEI,oEAsEcxC,OAAO2C,EAAmBC,IAIpD,OAAOJ,EDvCgDM,CAAa3C,EAASvQ,IAAI8H,cAErEyI,EAASxO,KAAOzC,iBAAeF,KAAKqF,WAGpC8L,EAASjP,KAAKqE,OAAS,WAEnB4K,EAASjP,KAAKqE,OAAS,KAEvBiI,MAkChBA,SA5DIA,IEVR,IAAAuF,EAAA,WAAA,SAAAA,KAMA,OAHWA,EAAA/R,UAA+B6P,EAAaA,cAACjD,OAE7CmF,EAAGhE,IAAG8C,EAChBkB,YCLDnC,EAAAA,WAAWnT,IACP8T,EACAwB"}