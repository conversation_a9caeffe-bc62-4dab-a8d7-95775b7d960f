{"version": 3, "file": "sprite-tiling.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/TilingSprite.ts", "../../src/TilingSpriteRenderer.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { Texture, TextureMatrix } from '@pixi/core';\nimport { Point, Rectangle, Transform  } from '@pixi/math';\nimport { Sprite } from '@pixi/sprite';\nimport type { Renderer, IBaseTextureOptions, TextureSource } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { IPoint, IPointData, ISize, ObservablePoint } from '@pixi/math';\n\nconst tempPoint = new Point();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface TilingSprite extends GlobalMixins.TilingSprite {}\n\n/**\n * A tiling sprite is a fast way of rendering a tiling image.\n * @memberof PIXI\n */\nexport class TilingSprite extends Sprite\n{\n    /** Tile transform */\n    public tileTransform: Transform;\n\n    /** Matrix that is applied to UV to get the coords in Texture normalized space to coords in BaseTexture space. */\n    public uvMatrix: TextureMatrix;\n\n    /**\n     * Flags whether the tiling pattern should originate from the origin instead of the top-left corner in\n     * local space.\n     *\n     * This will make the texture coordinates assigned to each vertex dependent on the value of the anchor. Without\n     * this, the top-left corner always gets the (0, 0) texture coordinate.\n     * @default false\n     */\n    public uvRespectAnchor: boolean;\n\n    /**\n     * @param texture - The texture of the tiling sprite.\n     * @param width - The width of the tiling sprite.\n     * @param height - The height of the tiling sprite.\n     */\n    constructor(texture: Texture, width = 100, height = 100)\n    {\n        super(texture);\n\n        this.tileTransform = new Transform();\n\n        // The width of the tiling sprite\n        this._width = width;\n\n        // The height of the tiling sprite\n        this._height = height;\n\n        this.uvMatrix = this.texture.uvMatrix || new TextureMatrix(texture);\n\n        /**\n         * Plugin that is responsible for rendering this element.\n         * Allows to customize the rendering process without overriding '_render' method.\n         * @default 'tilingSprite'\n         */\n        this.pluginName = 'tilingSprite';\n\n        this.uvRespectAnchor = false;\n    }\n    /**\n     * Changes frame clamping in corresponding textureTransform, shortcut\n     * Change to -0.5 to add a pixel to the edge, recommended for transparent trimmed textures in atlas\n     * @default 0.5\n     * @member {number}\n     */\n    get clampMargin(): number\n    {\n        return this.uvMatrix.clampMargin;\n    }\n\n    set clampMargin(value: number)\n    {\n        this.uvMatrix.clampMargin = value;\n        this.uvMatrix.update(true);\n    }\n\n    /** The scaling of the image that is being tiled. */\n    get tileScale(): ObservablePoint\n    {\n        return this.tileTransform.scale;\n    }\n\n    set tileScale(value: IPointData)\n    {\n        this.tileTransform.scale.copyFrom(value as IPoint);\n    }\n\n    /** The offset of the image that is being tiled. */\n    get tilePosition(): ObservablePoint\n    {\n        return this.tileTransform.position;\n    }\n\n    set tilePosition(value: ObservablePoint)\n    {\n        this.tileTransform.position.copyFrom(value as IPoint);\n    }\n\n    /**\n     * @protected\n     */\n    protected _onTextureUpdate(): void\n    {\n        if (this.uvMatrix)\n        {\n            this.uvMatrix.texture = this._texture;\n        }\n        this._cachedTint = 0xFFFFFF;\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // tweak our texture temporarily..\n        const texture = this._texture;\n\n        if (!texture || !texture.valid)\n        {\n            return;\n        }\n\n        this.tileTransform.updateLocalTransform();\n        this.uvMatrix.update();\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n        renderer.plugins[this.pluginName].render(this);\n    }\n\n    /** Updates the bounds of the tiling sprite. */\n    protected _calculateBounds(): void\n    {\n        const minX = this._width * -this._anchor._x;\n        const minY = this._height * -this._anchor._y;\n        const maxX = this._width * (1 - this._anchor._x);\n        const maxY = this._height * (1 - this._anchor._y);\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Gets the local bounds of the sprite object.\n     * @param rect - Optional output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        // we can do a fast local bounds if the sprite has no children!\n        if (this.children.length === 0)\n        {\n            this._bounds.minX = this._width * -this._anchor._x;\n            this._bounds.minY = this._height * -this._anchor._y;\n            this._bounds.maxX = this._width * (1 - this._anchor._x);\n            this._bounds.maxY = this._height * (1 - this._anchor._y);\n\n            if (!rect)\n            {\n                if (!this._localBoundsRect)\n                {\n                    this._localBoundsRect = new Rectangle();\n                }\n\n                rect = this._localBoundsRect;\n            }\n\n            return this._bounds.getRectangle(rect);\n        }\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /**\n     * Checks if a point is inside this tiling sprite.\n     * @param point - The point to check.\n     * @returns Whether or not the sprite contains the point.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const width = this._width;\n        const height = this._height;\n        const x1 = -width * this.anchor._x;\n\n        if (tempPoint.x >= x1 && tempPoint.x < x1 + width)\n        {\n            const y1 = -height * this.anchor._y;\n\n            if (tempPoint.y >= y1 && tempPoint.y < y1 + height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Destroys this sprite and optionally its texture and children\n     * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well\n     * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this.tileTransform = null;\n        this.uvMatrix = null;\n    }\n\n    /**\n     * Helper function that creates a new tiling sprite based on the source you provide.\n     * The source can be - frame id, image url, video url, canvas element, video element, base texture\n     * @static\n     * @param {string|PIXI.Texture|HTMLCanvasElement|HTMLVideoElement} source - Source to create texture from\n     * @param {object} options - See {@link PIXI.BaseTexture}'s constructor for options.\n     * @param {number} options.width - required width of the tiling sprite\n     * @param {number} options.height - required height of the tiling sprite\n     * @returns {PIXI.TilingSprite} The newly created texture\n     */\n    static from(source: TextureSource | Texture, options: ISize & IBaseTextureOptions): TilingSprite\n    {\n        const texture = (source instanceof Texture)\n            ? source\n            : Texture.from(source, options);\n\n        return new TilingSprite(\n            texture,\n            options.width,\n            options.height\n        );\n    }\n\n    /** The width of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    set width(value: number)\n    {\n        this._width = value;\n    }\n\n    /** The height of the TilingSprite, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    set height(value: number)\n    {\n        this._height = value;\n    }\n}\n", "import { ObjectRenderer, Shader, State, QuadUv, ExtensionType } from '@pixi/core';\nimport { WRAP_MODES } from '@pixi/constants';\nimport { Matrix } from '@pixi/math';\nimport { premultiplyTintToRgba, correctBlendMode } from '@pixi/utils';\n\nimport fragmentSimpleSrc from './sprite-tiling-simple.frag';\nimport gl1VertexSrc from './sprite-tiling-fallback.vert';\nimport gl1FragmentSrc from './sprite-tiling-fallback.frag';\nimport gl2VertexSrc from './sprite-tiling.vert';\nimport gl2FragmentSrc from './sprite-tiling.frag';\n\nimport type { Renderer, ExtensionMetadata } from '@pixi/core';\nimport type { TilingSprite } from './TilingSprite';\n\nconst tempMat = new Matrix();\n\n/**\n * WebGL renderer plugin for tiling sprites\n * @class\n * @memberof PIXI\n * @extends PIXI.ObjectRenderer\n */\nexport class TilingSprite<PERSON>ender<PERSON> extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'tilingSprite',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    public shader: Shader;\n    public simpleShader: Shader;\n    public quad: QuadUv;\n    public readonly state: State;\n\n    /**\n     * constructor for renderer\n     * @param {PIXI.Renderer} renderer - The renderer this tiling awesomeness works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // WebGL version is not available during initialization!\n        renderer.runners.contextChange.add(this);\n\n        this.quad = new QuadUv();\n\n        /**\n         * The WebGL state in which this renderer will work.\n         * @member {PIXI.State}\n         * @readonly\n         */\n        this.state = State.for2d();\n    }\n\n    /** Creates shaders when context is initialized. */\n    contextChange(): void\n    {\n        const renderer = this.renderer;\n        const uniforms = { globals: renderer.globalUniforms };\n\n        this.simpleShader = Shader.from(gl1VertexSrc, fragmentSimpleSrc, uniforms);\n        this.shader = renderer.context.webGLVersion > 1\n            ? Shader.from(gl2VertexSrc, gl2FragmentSrc, uniforms)\n            : Shader.from(gl1VertexSrc, gl1FragmentSrc, uniforms);\n    }\n\n    /**\n     * @param {PIXI.TilingSprite} ts - tilingSprite to be rendered\n     */\n    public render(ts: TilingSprite): void\n    {\n        const renderer = this.renderer;\n        const quad = this.quad;\n\n        let vertices = quad.vertices;\n\n        vertices[0] = vertices[6] = (ts._width) * -ts.anchor.x;\n        vertices[1] = vertices[3] = ts._height * -ts.anchor.y;\n\n        vertices[2] = vertices[4] = (ts._width) * (1.0 - ts.anchor.x);\n        vertices[5] = vertices[7] = ts._height * (1.0 - ts.anchor.y);\n\n        const anchorX = ts.uvRespectAnchor ? ts.anchor.x : 0;\n        const anchorY = ts.uvRespectAnchor ? ts.anchor.y : 0;\n\n        vertices = quad.uvs;\n\n        vertices[0] = vertices[6] = -anchorX;\n        vertices[1] = vertices[3] = -anchorY;\n\n        vertices[2] = vertices[4] = 1.0 - anchorX;\n        vertices[5] = vertices[7] = 1.0 - anchorY;\n\n        quad.invalidate();\n\n        const tex = ts._texture;\n        const baseTex = tex.baseTexture;\n        const premultiplied = baseTex.alphaMode > 0;\n        const lt = ts.tileTransform.localTransform;\n        const uv = ts.uvMatrix;\n        let isSimple = baseTex.isPowerOfTwo\n            && tex.frame.width === baseTex.width && tex.frame.height === baseTex.height;\n\n        // auto, force repeat wrapMode for big tiling textures\n        if (isSimple)\n        {\n            if (!baseTex._glTextures[renderer.CONTEXT_UID])\n            {\n                if (baseTex.wrapMode === WRAP_MODES.CLAMP)\n                {\n                    baseTex.wrapMode = WRAP_MODES.REPEAT;\n                }\n            }\n            else\n            {\n                isSimple = baseTex.wrapMode !== WRAP_MODES.CLAMP;\n            }\n        }\n\n        const shader = isSimple ? this.simpleShader : this.shader;\n\n        const w = tex.width;\n        const h = tex.height;\n        const W = ts._width;\n        const H = ts._height;\n\n        tempMat.set(lt.a * w / W,\n            lt.b * w / H,\n            lt.c * h / W,\n            lt.d * h / H,\n            lt.tx / W,\n            lt.ty / H);\n\n        // that part is the same as above:\n        // tempMat.identity();\n        // tempMat.scale(tex.width, tex.height);\n        // tempMat.prepend(lt);\n        // tempMat.scale(1.0 / ts._width, 1.0 / ts._height);\n\n        tempMat.invert();\n        if (isSimple)\n        {\n            tempMat.prepend(uv.mapCoord);\n        }\n        else\n        {\n            shader.uniforms.uMapCoord = uv.mapCoord.toArray(true);\n            shader.uniforms.uClampFrame = uv.uClampFrame;\n            shader.uniforms.uClampOffset = uv.uClampOffset;\n        }\n\n        shader.uniforms.uTransform = tempMat.toArray(true);\n        shader.uniforms.uColor = premultiplyTintToRgba(ts.tint, ts.worldAlpha,\n            shader.uniforms.uColor, premultiplied);\n        shader.uniforms.translationMatrix = ts.transform.worldTransform.toArray(true);\n        shader.uniforms.uSampler = tex;\n\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(quad);\n\n        this.state.blendMode = correctBlendMode(ts.blendMode, premultiplied);\n        renderer.state.set(this.state);\n        renderer.geometry.draw(this.renderer.gl.TRIANGLES, 6, 0);\n    }\n}\n"], "names": ["arguments", "Point", "Transform", "TextureMatrix", "Rectangle", "Texture", "Sprite", "Matrix", "QuadUv", "State", "Shader", "WRAP_MODES", "premultiplyTintToRgba", "correctBlendMode", "ExtensionType", "O<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;IClNA,IAAM,SAAS,GAAG,IAAIC,UAAK,EAAE,CAAC;IAK9B;;;IAGG;AACH,QAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;QAAkC,SAAM,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;IAkBpC;;;;IAIG;IACH,IAAA,SAAA,YAAA,CAAY,OAAgB,EAAE,KAAW,EAAE,MAAY,EAAA;IAAzB,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAW,GAAA,GAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAY,GAAA,GAAA,CAAA,EAAA;YAAvD,IAEI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,IAoBjB,IAAA,CAAA;IAlBG,QAAA,KAAI,CAAC,aAAa,GAAG,IAAIC,cAAS,EAAE,CAAC;;IAGrC,QAAA,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;;IAGpB,QAAA,KAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAEtB,QAAA,KAAI,CAAC,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAIC,kBAAa,CAAC,OAAO,CAAC,CAAC;IAEpE;;;;IAIG;IACH,QAAA,KAAI,CAAC,UAAU,GAAG,cAAc,CAAC;IAEjC,QAAA,KAAI,CAAC,eAAe,GAAG,KAAK,CAAC;;SAChC;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAW,CAAA,SAAA,EAAA,aAAA,EAAA;IANf;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;aACpC;IAED,QAAA,GAAA,EAAA,UAAgB,KAAa,EAAA;IAEzB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aAC9B;;;IANA,KAAA,CAAA,CAAA;IASD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAS,CAAA,SAAA,EAAA,WAAA,EAAA;;IAAb,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;aACnC;IAED,QAAA,GAAA,EAAA,UAAc,KAAiB,EAAA;gBAE3B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAe,CAAC,CAAC;aACtD;;;IALA,KAAA,CAAA,CAAA;IAQD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAY,CAAA,SAAA,EAAA,cAAA,EAAA;;IAAhB,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;aACtC;IAED,QAAA,GAAA,EAAA,UAAiB,KAAsB,EAAA;gBAEnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAe,CAAC,CAAC;aACzD;;;IALA,KAAA,CAAA,CAAA;IAOD;;IAEG;IACO,IAAA,YAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;YAEI,IAAI,IAAI,CAAC,QAAQ,EACjB;gBACI,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;IACzC,SAAA;IACD,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;SAC/B,CAAA;IAED;;;IAGG;QACO,YAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,QAAkB,EAAA;;IAGhC,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;IAE9B,QAAA,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAC9B;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;IAC1C,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IAEvB,QAAA,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACpE,QAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAClD,CAAA;;IAGS,IAAA,YAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;IAEI,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;IAC5C,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;IAC7C,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACjD,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAElD,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SACjE,CAAA;IAED;;;;IAIG;QACI,YAAc,CAAA,SAAA,CAAA,cAAA,GAArB,UAAsB,IAAgB,EAAA;;IAGlC,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAC9B;IACI,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;IACnD,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;IACpD,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACxD,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAEzD,IAAI,CAAC,IAAI,EACT;IACI,gBAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAC1B;IACI,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAIC,cAAS,EAAE,CAAC;IAC3C,iBAAA;IAED,gBAAA,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC;IAChC,aAAA;gBAED,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1C,SAAA;YAED,OAAO,MAAA,CAAA,SAAA,CAAM,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAChD,CAAA;IAED;;;;IAIG;QACI,YAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,KAAiB,EAAA;YAElC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAEnD,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAM,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IAEnC,QAAA,IAAI,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,EACjD;gBACI,IAAM,EAAE,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IAEpC,YAAA,IAAI,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,EAClD;IACI,gBAAA,OAAO,IAAI,CAAC;IACf,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAED;;;;;;;;IAQG;QACI,YAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;IAE9C,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;IAEvB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC1B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB,CAAA;IAED;;;;;;;;;IASG;IACI,IAAA,YAAA,CAAA,IAAI,GAAX,UAAY,MAA+B,EAAE,OAAoC,EAAA;IAE7E,QAAA,IAAM,OAAO,GAAG,CAAC,MAAM,YAAYC,YAAO;IACtC,cAAE,MAAM;kBACNA,YAAO,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAEpC,QAAA,OAAO,IAAI,YAAY,CACnB,OAAO,EACP,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,CACjB,CAAC;SACL,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAK,CAAA,SAAA,EAAA,OAAA,EAAA;;IAAT,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,MAAM,CAAC;aACtB;IAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;IAEnB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;aACvB;;;IALA,KAAA,CAAA,CAAA;IAQD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAM,CAAA,SAAA,EAAA,QAAA,EAAA;;IAAV,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,OAAO,CAAC;aACvB;IAED,QAAA,GAAA,EAAA,UAAW,KAAa,EAAA;IAEpB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;aACxB;;;IALA,KAAA,CAAA,CAAA;QAML,OAAC,YAAA,CAAA;IAAD,CAvPA,CAAkCC,aAAM,CAuPvC;;;;;;;;;;;;ICzPD,IAAM,OAAO,GAAG,IAAIC,WAAM,EAAE,CAAC;IAE7B;;;;;IAKG;AACH,QAAA,oBAAA,kBAAA,UAAA,MAAA,EAAA;QAA0C,SAAc,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;IAapD;;;IAGG;IACH,IAAA,SAAA,oBAAA,CAAY,QAAkB,EAAA;YAA9B,IAEI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,QAAQ,CAAC,IAalB,IAAA,CAAA;;YAVG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,KAAI,CAAC,CAAC;IAEzC,QAAA,KAAI,CAAC,IAAI,GAAG,IAAIC,WAAM,EAAE,CAAC;IAEzB;;;;IAIG;IACH,QAAA,KAAI,CAAC,KAAK,GAAGC,UAAK,CAAC,KAAK,EAAE,CAAC;;SAC9B;;IAGD,IAAA,oBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;IAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,IAAM,QAAQ,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,cAAc,EAAE,CAAC;IAEtD,QAAA,IAAI,CAAC,YAAY,GAAGC,WAAM,CAAC,IAAI,CAAC,YAAY,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAC3E,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC;kBACzCA,WAAM,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,EAAE,QAAQ,CAAC;kBACnDA,WAAM,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;SAC7D,CAAA;IAED;;IAEG;QACI,oBAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,EAAgB,EAAA;IAE1B,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/B,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAEvB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE7B,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IACvD,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;YAEtD,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9D,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAE7D,QAAA,IAAM,OAAO,GAAG,EAAE,CAAC,eAAe,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACrD,QAAA,IAAM,OAAO,GAAG,EAAE,CAAC,eAAe,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IAErD,QAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;YAEpB,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;YACrC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;IAErC,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC;IAC1C,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC;YAE1C,IAAI,CAAC,UAAU,EAAE,CAAC;IAElB,QAAA,IAAM,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;IACxB,QAAA,IAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC;IAChC,QAAA,IAAM,aAAa,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;IAC5C,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,cAAc,CAAC;IAC3C,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC;IACvB,QAAA,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY;IAC5B,eAAA,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC;;IAGhF,QAAA,IAAI,QAAQ,EACZ;gBACI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAC9C;IACI,gBAAA,IAAI,OAAO,CAAC,QAAQ,KAAKC,oBAAU,CAAC,KAAK,EACzC;IACI,oBAAA,OAAO,CAAC,QAAQ,GAAGA,oBAAU,CAAC,MAAM,CAAC;IACxC,iBAAA;IACJ,aAAA;IAED,iBAAA;oBACI,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAKA,oBAAU,CAAC,KAAK,CAAC;IACpD,aAAA;IACJ,SAAA;IAED,QAAA,IAAM,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;IAE1D,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC;IACpB,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;IACrB,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IACpB,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;YAErB,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EACpB,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EACZ,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EACZ,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EACZ,EAAE,CAAC,EAAE,GAAG,CAAC,EACT,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;;;;;YAQf,OAAO,CAAC,MAAM,EAAE,CAAC;IACjB,QAAA,IAAI,QAAQ,EACZ;IACI,YAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAChC,SAAA;IAED,aAAA;IACI,YAAA,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACtD,MAAM,CAAC,QAAQ,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC;IAClD,SAAA;YAED,MAAM,CAAC,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAGC,2BAAqB,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,EACjE,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAC3C,QAAA,MAAM,CAAC,QAAQ,CAAC,iBAAiB,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9E,QAAA,MAAM,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;IAE/B,QAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7B,QAAA,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE7B,QAAA,IAAI,CAAC,KAAK,CAAC,SAAS,GAAGC,sBAAgB,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YACrE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,QAAA,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5D,CAAA;;IA5IM,IAAA,oBAAA,CAAA,SAAS,GAAsB;IAClC,QAAA,IAAI,EAAE,cAAc;YACpB,IAAI,EAAEC,kBAAa,CAAC,cAAc;SACrC,CAAC;QA0IN,OAAC,oBAAA,CAAA;KAAA,CAhJyCC,mBAAc,CAgJvD;;;;;;;;;;;;;;;;"}