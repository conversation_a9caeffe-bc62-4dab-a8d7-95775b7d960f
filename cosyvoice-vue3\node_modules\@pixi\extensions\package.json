{"name": "@pixi/extensions", "version": "6.5.10", "main": "dist/cjs/extensions.js", "module": "dist/esm/extensions.mjs", "bundle": "dist/browser/extensions.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/extensions.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/extensions.js"}}}, "description": "Installing and uninstalling extensions for PixiJS", "author": "<PERSON> <<EMAIL>>", "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}