{"version": 3, "file": "utils.min.js", "sources": ["../../../../node_modules/eventemitter3/index.js", "../../../../node_modules/earcut/src/earcut.js", "../../../../node_modules/url/node_modules/punycode/punycode.js", "../../../../node_modules/url/util.js", "../../../../node_modules/querystring/decode.js", "../../../../node_modules/querystring/encode.js", "../../../../node_modules/querystring/index.js", "../../../../node_modules/url/url.js", "../../src/url.ts", "../../src/path.ts", "../../src/settings.ts", "../../src/browser/hello.ts", "../../src/browser/isWebGLSupported.ts", "../../src/color/premultiply.ts", "../../src/data/getBufferType.ts", "../../src/data/interleaveTypedArrays.ts", "../../src/data/uid.ts", "../../src/logging/deprecation.ts", "../../src/media/caches.ts", "../../src/media/CanvasRenderTarget.ts", "../../src/const.ts", "../../src/network/determineCrossOrigin.ts", "../../src/data/createIndicesForQuads.ts", "../../src/network/decomposeDataUri.ts", "../../src/network/getResolutionOfUrl.ts", "../../src/color/hex.ts", "../../src/data/pow2.ts", "../../src/data/removeItems.ts", "../../src/data/sign.ts", "../../src/media/trimCanvas.ts"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "'use strict';\n\nmodule.exports = earcut;\nmodule.exports.default = earcut;\n\nfunction earcut(data, holeIndices, dim) {\n\n    dim = dim || 2;\n\n    var hasHoles = holeIndices && holeIndices.length,\n        outerLen = hasHoles ? holeIndices[0] * dim : data.length,\n        outerNode = linkedList(data, 0, outerLen, dim, true),\n        triangles = [];\n\n    if (!outerNode || outerNode.next === outerNode.prev) return triangles;\n\n    var minX, minY, maxX, maxY, x, y, invSize;\n\n    if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);\n\n    // if the shape is not too simple, we'll use z-order curve hash later; calculate polygon bbox\n    if (data.length > 80 * dim) {\n        minX = maxX = data[0];\n        minY = maxY = data[1];\n\n        for (var i = dim; i < outerLen; i += dim) {\n            x = data[i];\n            y = data[i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n        }\n\n        // minX, minY and invSize are later used to transform coords into integers for z-order calculation\n        invSize = Math.max(maxX - minX, maxY - minY);\n        invSize = invSize !== 0 ? 32767 / invSize : 0;\n    }\n\n    earcutLinked(outerNode, triangles, dim, minX, minY, invSize, 0);\n\n    return triangles;\n}\n\n// create a circular doubly linked list from polygon points in the specified winding order\nfunction linkedList(data, start, end, dim, clockwise) {\n    var i, last;\n\n    if (clockwise === (signedArea(data, start, end, dim) > 0)) {\n        for (i = start; i < end; i += dim) last = insertNode(i, data[i], data[i + 1], last);\n    } else {\n        for (i = end - dim; i >= start; i -= dim) last = insertNode(i, data[i], data[i + 1], last);\n    }\n\n    if (last && equals(last, last.next)) {\n        removeNode(last);\n        last = last.next;\n    }\n\n    return last;\n}\n\n// eliminate colinear or duplicate points\nfunction filterPoints(start, end) {\n    if (!start) return start;\n    if (!end) end = start;\n\n    var p = start,\n        again;\n    do {\n        again = false;\n\n        if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {\n            removeNode(p);\n            p = end = p.prev;\n            if (p === p.next) break;\n            again = true;\n\n        } else {\n            p = p.next;\n        }\n    } while (again || p !== end);\n\n    return end;\n}\n\n// main ear slicing loop which triangulates a polygon (given as a linked list)\nfunction earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {\n    if (!ear) return;\n\n    // interlink polygon nodes in z-order\n    if (!pass && invSize) indexCurve(ear, minX, minY, invSize);\n\n    var stop = ear,\n        prev, next;\n\n    // iterate through ears, slicing them one by one\n    while (ear.prev !== ear.next) {\n        prev = ear.prev;\n        next = ear.next;\n\n        if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {\n            // cut off the triangle\n            triangles.push(prev.i / dim | 0);\n            triangles.push(ear.i / dim | 0);\n            triangles.push(next.i / dim | 0);\n\n            removeNode(ear);\n\n            // skipping the next vertex leads to less sliver triangles\n            ear = next.next;\n            stop = next.next;\n\n            continue;\n        }\n\n        ear = next;\n\n        // if we looped through the whole remaining polygon and can't find any more ears\n        if (ear === stop) {\n            // try filtering points and slicing again\n            if (!pass) {\n                earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);\n\n            // if this didn't work, try curing all small self-intersections locally\n            } else if (pass === 1) {\n                ear = cureLocalIntersections(filterPoints(ear), triangles, dim);\n                earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);\n\n            // as a last resort, try splitting the remaining polygon into two\n            } else if (pass === 2) {\n                splitEarcut(ear, triangles, dim, minX, minY, invSize);\n            }\n\n            break;\n        }\n    }\n}\n\n// check whether a polygon node forms a valid ear with adjacent nodes\nfunction isEar(ear) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    // now make sure we don't have other points inside the potential ear\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    var p = c.next;\n    while (p !== a) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) &&\n            area(p.prev, p, p.next) >= 0) return false;\n        p = p.next;\n    }\n\n    return true;\n}\n\nfunction isEarHashed(ear, minX, minY, invSize) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    // z-order range for the current triangle bbox;\n    var minZ = zOrder(x0, y0, minX, minY, invSize),\n        maxZ = zOrder(x1, y1, minX, minY, invSize);\n\n    var p = ear.prevZ,\n        n = ear.nextZ;\n\n    // look for points inside the triangle in both directions\n    while (p && p.z >= minZ && n && n.z <= maxZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    // look for remaining points in decreasing z-order\n    while (p && p.z >= minZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n    }\n\n    // look for remaining points in increasing z-order\n    while (n && n.z <= maxZ) {\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    return true;\n}\n\n// go through all polygon nodes and cure small local self-intersections\nfunction cureLocalIntersections(start, triangles, dim) {\n    var p = start;\n    do {\n        var a = p.prev,\n            b = p.next.next;\n\n        if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {\n\n            triangles.push(a.i / dim | 0);\n            triangles.push(p.i / dim | 0);\n            triangles.push(b.i / dim | 0);\n\n            // remove two nodes involved\n            removeNode(p);\n            removeNode(p.next);\n\n            p = start = b;\n        }\n        p = p.next;\n    } while (p !== start);\n\n    return filterPoints(p);\n}\n\n// try splitting polygon into two and triangulate them independently\nfunction splitEarcut(start, triangles, dim, minX, minY, invSize) {\n    // look for a valid diagonal that divides the polygon into two\n    var a = start;\n    do {\n        var b = a.next.next;\n        while (b !== a.prev) {\n            if (a.i !== b.i && isValidDiagonal(a, b)) {\n                // split the polygon in two by the diagonal\n                var c = splitPolygon(a, b);\n\n                // filter colinear points around the cuts\n                a = filterPoints(a, a.next);\n                c = filterPoints(c, c.next);\n\n                // run earcut on each half\n                earcutLinked(a, triangles, dim, minX, minY, invSize, 0);\n                earcutLinked(c, triangles, dim, minX, minY, invSize, 0);\n                return;\n            }\n            b = b.next;\n        }\n        a = a.next;\n    } while (a !== start);\n}\n\n// link every hole into the outer loop, producing a single-ring polygon without holes\nfunction eliminateHoles(data, holeIndices, outerNode, dim) {\n    var queue = [],\n        i, len, start, end, list;\n\n    for (i = 0, len = holeIndices.length; i < len; i++) {\n        start = holeIndices[i] * dim;\n        end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n        list = linkedList(data, start, end, dim, false);\n        if (list === list.next) list.steiner = true;\n        queue.push(getLeftmost(list));\n    }\n\n    queue.sort(compareX);\n\n    // process holes from left to right\n    for (i = 0; i < queue.length; i++) {\n        outerNode = eliminateHole(queue[i], outerNode);\n    }\n\n    return outerNode;\n}\n\nfunction compareX(a, b) {\n    return a.x - b.x;\n}\n\n// find a bridge between vertices that connects hole with an outer ring and and link it\nfunction eliminateHole(hole, outerNode) {\n    var bridge = findHoleBridge(hole, outerNode);\n    if (!bridge) {\n        return outerNode;\n    }\n\n    var bridgeReverse = splitPolygon(bridge, hole);\n\n    // filter collinear points around the cuts\n    filterPoints(bridgeReverse, bridgeReverse.next);\n    return filterPoints(bridge, bridge.next);\n}\n\n// David Eberly's algorithm for finding a bridge between hole and outer polygon\nfunction findHoleBridge(hole, outerNode) {\n    var p = outerNode,\n        hx = hole.x,\n        hy = hole.y,\n        qx = -Infinity,\n        m;\n\n    // find a segment intersected by a ray from the hole's leftmost point to the left;\n    // segment's endpoint with lesser x will be potential connection point\n    do {\n        if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {\n            var x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);\n            if (x <= hx && x > qx) {\n                qx = x;\n                m = p.x < p.next.x ? p : p.next;\n                if (x === hx) return m; // hole touches outer segment; pick leftmost endpoint\n            }\n        }\n        p = p.next;\n    } while (p !== outerNode);\n\n    if (!m) return null;\n\n    // look for points inside the triangle of hole point, segment intersection and endpoint;\n    // if there are no points found, we have a valid connection;\n    // otherwise choose the point of the minimum angle with the ray as connection point\n\n    var stop = m,\n        mx = m.x,\n        my = m.y,\n        tanMin = Infinity,\n        tan;\n\n    p = m;\n\n    do {\n        if (hx >= p.x && p.x >= mx && hx !== p.x &&\n                pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {\n\n            tan = Math.abs(hy - p.y) / (hx - p.x); // tangential\n\n            if (locallyInside(p, hole) &&\n                (tan < tanMin || (tan === tanMin && (p.x > m.x || (p.x === m.x && sectorContainsSector(m, p)))))) {\n                m = p;\n                tanMin = tan;\n            }\n        }\n\n        p = p.next;\n    } while (p !== stop);\n\n    return m;\n}\n\n// whether sector in vertex m contains sector in vertex p in the same coordinates\nfunction sectorContainsSector(m, p) {\n    return area(m.prev, m, p.prev) < 0 && area(p.next, m, m.next) < 0;\n}\n\n// interlink polygon nodes in z-order\nfunction indexCurve(start, minX, minY, invSize) {\n    var p = start;\n    do {\n        if (p.z === 0) p.z = zOrder(p.x, p.y, minX, minY, invSize);\n        p.prevZ = p.prev;\n        p.nextZ = p.next;\n        p = p.next;\n    } while (p !== start);\n\n    p.prevZ.nextZ = null;\n    p.prevZ = null;\n\n    sortLinked(p);\n}\n\n// Simon Tatham's linked list merge sort algorithm\n// http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html\nfunction sortLinked(list) {\n    var i, p, q, e, tail, numMerges, pSize, qSize,\n        inSize = 1;\n\n    do {\n        p = list;\n        list = null;\n        tail = null;\n        numMerges = 0;\n\n        while (p) {\n            numMerges++;\n            q = p;\n            pSize = 0;\n            for (i = 0; i < inSize; i++) {\n                pSize++;\n                q = q.nextZ;\n                if (!q) break;\n            }\n            qSize = inSize;\n\n            while (pSize > 0 || (qSize > 0 && q)) {\n\n                if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {\n                    e = p;\n                    p = p.nextZ;\n                    pSize--;\n                } else {\n                    e = q;\n                    q = q.nextZ;\n                    qSize--;\n                }\n\n                if (tail) tail.nextZ = e;\n                else list = e;\n\n                e.prevZ = tail;\n                tail = e;\n            }\n\n            p = q;\n        }\n\n        tail.nextZ = null;\n        inSize *= 2;\n\n    } while (numMerges > 1);\n\n    return list;\n}\n\n// z-order of a point given coords and inverse of the longer side of data bbox\nfunction zOrder(x, y, minX, minY, invSize) {\n    // coords are transformed into non-negative 15-bit integer range\n    x = (x - minX) * invSize | 0;\n    y = (y - minY) * invSize | 0;\n\n    x = (x | (x << 8)) & 0x00FF00FF;\n    x = (x | (x << 4)) & 0x0F0F0F0F;\n    x = (x | (x << 2)) & 0x33333333;\n    x = (x | (x << 1)) & 0x55555555;\n\n    y = (y | (y << 8)) & 0x00FF00FF;\n    y = (y | (y << 4)) & 0x0F0F0F0F;\n    y = (y | (y << 2)) & 0x33333333;\n    y = (y | (y << 1)) & 0x55555555;\n\n    return x | (y << 1);\n}\n\n// find the leftmost node of a polygon ring\nfunction getLeftmost(start) {\n    var p = start,\n        leftmost = start;\n    do {\n        if (p.x < leftmost.x || (p.x === leftmost.x && p.y < leftmost.y)) leftmost = p;\n        p = p.next;\n    } while (p !== start);\n\n    return leftmost;\n}\n\n// check if a point lies within a convex triangle\nfunction pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {\n    return (cx - px) * (ay - py) >= (ax - px) * (cy - py) &&\n           (ax - px) * (by - py) >= (bx - px) * (ay - py) &&\n           (bx - px) * (cy - py) >= (cx - px) * (by - py);\n}\n\n// check if a diagonal between two polygon nodes is valid (lies in polygon interior)\nfunction isValidDiagonal(a, b) {\n    return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) && // dones't intersect other edges\n           (locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b) && // locally visible\n            (area(a.prev, a, b.prev) || area(a, b.prev, b)) || // does not create opposite-facing sectors\n            equals(a, b) && area(a.prev, a, a.next) > 0 && area(b.prev, b, b.next) > 0); // special zero-length case\n}\n\n// signed area of a triangle\nfunction area(p, q, r) {\n    return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);\n}\n\n// check if two points are equal\nfunction equals(p1, p2) {\n    return p1.x === p2.x && p1.y === p2.y;\n}\n\n// check if two segments intersect\nfunction intersects(p1, q1, p2, q2) {\n    var o1 = sign(area(p1, q1, p2));\n    var o2 = sign(area(p1, q1, q2));\n    var o3 = sign(area(p2, q2, p1));\n    var o4 = sign(area(p2, q2, q1));\n\n    if (o1 !== o2 && o3 !== o4) return true; // general case\n\n    if (o1 === 0 && onSegment(p1, p2, q1)) return true; // p1, q1 and p2 are collinear and p2 lies on p1q1\n    if (o2 === 0 && onSegment(p1, q2, q1)) return true; // p1, q1 and q2 are collinear and q2 lies on p1q1\n    if (o3 === 0 && onSegment(p2, p1, q2)) return true; // p2, q2 and p1 are collinear and p1 lies on p2q2\n    if (o4 === 0 && onSegment(p2, q1, q2)) return true; // p2, q2 and q1 are collinear and q1 lies on p2q2\n\n    return false;\n}\n\n// for collinear points p, q, r, check if point q lies on segment pr\nfunction onSegment(p, q, r) {\n    return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);\n}\n\nfunction sign(num) {\n    return num > 0 ? 1 : num < 0 ? -1 : 0;\n}\n\n// check if a polygon diagonal intersects any polygon segments\nfunction intersectsPolygon(a, b) {\n    var p = a;\n    do {\n        if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i &&\n                intersects(p, p.next, a, b)) return true;\n        p = p.next;\n    } while (p !== a);\n\n    return false;\n}\n\n// check if a polygon diagonal is locally inside the polygon\nfunction locallyInside(a, b) {\n    return area(a.prev, a, a.next) < 0 ?\n        area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 :\n        area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;\n}\n\n// check if the middle point of a polygon diagonal is inside the polygon\nfunction middleInside(a, b) {\n    var p = a,\n        inside = false,\n        px = (a.x + b.x) / 2,\n        py = (a.y + b.y) / 2;\n    do {\n        if (((p.y > py) !== (p.next.y > py)) && p.next.y !== p.y &&\n                (px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x))\n            inside = !inside;\n        p = p.next;\n    } while (p !== a);\n\n    return inside;\n}\n\n// link two polygon vertices with a bridge; if the vertices belong to the same ring, it splits polygon into two;\n// if one belongs to the outer ring and another to a hole, it merges it into a single ring\nfunction splitPolygon(a, b) {\n    var a2 = new Node(a.i, a.x, a.y),\n        b2 = new Node(b.i, b.x, b.y),\n        an = a.next,\n        bp = b.prev;\n\n    a.next = b;\n    b.prev = a;\n\n    a2.next = an;\n    an.prev = a2;\n\n    b2.next = a2;\n    a2.prev = b2;\n\n    bp.next = b2;\n    b2.prev = bp;\n\n    return b2;\n}\n\n// create a node and optionally link it with previous one (in a circular doubly linked list)\nfunction insertNode(i, x, y, last) {\n    var p = new Node(i, x, y);\n\n    if (!last) {\n        p.prev = p;\n        p.next = p;\n\n    } else {\n        p.next = last.next;\n        p.prev = last;\n        last.next.prev = p;\n        last.next = p;\n    }\n    return p;\n}\n\nfunction removeNode(p) {\n    p.next.prev = p.prev;\n    p.prev.next = p.next;\n\n    if (p.prevZ) p.prevZ.nextZ = p.nextZ;\n    if (p.nextZ) p.nextZ.prevZ = p.prevZ;\n}\n\nfunction Node(i, x, y) {\n    // vertex index in coordinates array\n    this.i = i;\n\n    // vertex coordinates\n    this.x = x;\n    this.y = y;\n\n    // previous and next vertex nodes in a polygon ring\n    this.prev = null;\n    this.next = null;\n\n    // z-order curve value\n    this.z = 0;\n\n    // previous and next nodes in z-order\n    this.prevZ = null;\n    this.nextZ = null;\n\n    // indicates whether this is a steiner point\n    this.steiner = false;\n}\n\n// return a percentage difference between the polygon area and its triangulation area;\n// used to verify correctness of triangulation\nearcut.deviation = function (data, holeIndices, dim, triangles) {\n    var hasHoles = holeIndices && holeIndices.length;\n    var outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n\n    var polygonArea = Math.abs(signedArea(data, 0, outerLen, dim));\n    if (hasHoles) {\n        for (var i = 0, len = holeIndices.length; i < len; i++) {\n            var start = holeIndices[i] * dim;\n            var end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n            polygonArea -= Math.abs(signedArea(data, start, end, dim));\n        }\n    }\n\n    var trianglesArea = 0;\n    for (i = 0; i < triangles.length; i += 3) {\n        var a = triangles[i] * dim;\n        var b = triangles[i + 1] * dim;\n        var c = triangles[i + 2] * dim;\n        trianglesArea += Math.abs(\n            (data[a] - data[c]) * (data[b + 1] - data[a + 1]) -\n            (data[a] - data[b]) * (data[c + 1] - data[a + 1]));\n    }\n\n    return polygonArea === 0 && trianglesArea === 0 ? 0 :\n        Math.abs((trianglesArea - polygonArea) / polygonArea);\n};\n\nfunction signedArea(data, start, end, dim) {\n    var sum = 0;\n    for (var i = start, j = end - dim; i < end; i += dim) {\n        sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);\n        j = i;\n    }\n    return sum;\n}\n\n// turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts\nearcut.flatten = function (data) {\n    var dim = data[0][0].length,\n        result = {vertices: [], holes: [], dimensions: dim},\n        holeIndex = 0;\n\n    for (var i = 0; i < data.length; i++) {\n        for (var j = 0; j < data[i].length; j++) {\n            for (var d = 0; d < dim; d++) result.vertices.push(data[i][j][d]);\n        }\n        if (i > 0) {\n            holeIndex += data[i - 1].length;\n            result.holes.push(holeIndex);\n        }\n    }\n    return result;\n};\n", "/*! https://mths.be/punycode v1.3.2 by @mathias */\n;(function(root) {\n\n\t/** Detect free variables */\n\tvar freeExports = typeof exports == 'object' && exports &&\n\t\t!exports.nodeType && exports;\n\tvar freeModule = typeof module == 'object' && module &&\n\t\t!module.nodeType && module;\n\tvar freeGlobal = typeof global == 'object' && global;\n\tif (\n\t\tfreeGlobal.global === freeGlobal ||\n\t\tfreeGlobal.window === freeGlobal ||\n\t\tfreeGlobal.self === freeGlobal\n\t) {\n\t\troot = freeGlobal;\n\t}\n\n\t/**\n\t * The `punycode` object.\n\t * @name punycode\n\t * @type Object\n\t */\n\tvar punycode,\n\n\t/** Highest positive signed 32-bit float value */\n\tmaxInt = 2147483647, // aka. 0x7FFFFFFF or 2^31-1\n\n\t/** Bootstring parameters */\n\tbase = 36,\n\ttMin = 1,\n\ttMax = 26,\n\tskew = 38,\n\tdamp = 700,\n\tinitialBias = 72,\n\tinitialN = 128, // 0x80\n\tdelimiter = '-', // '\\x2D'\n\n\t/** Regular expressions */\n\tregexPunycode = /^xn--/,\n\tregexNonASCII = /[^\\x20-\\x7E]/, // unprintable ASCII chars + non-ASCII chars\n\tregexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g, // RFC 3490 separators\n\n\t/** Error messages */\n\terrors = {\n\t\t'overflow': 'Overflow: input needs wider integers to process',\n\t\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t\t'invalid-input': 'Invalid input'\n\t},\n\n\t/** Convenience shortcuts */\n\tbaseMinusTMin = base - tMin,\n\tfloor = Math.floor,\n\tstringFromCharCode = String.fromCharCode,\n\n\t/** Temporary variable */\n\tkey;\n\n\t/*--------------------------------------------------------------------------*/\n\n\t/**\n\t * A generic error utility function.\n\t * @private\n\t * @param {String} type The error type.\n\t * @returns {Error} Throws a `RangeError` with the applicable error message.\n\t */\n\tfunction error(type) {\n\t\tthrow RangeError(errors[type]);\n\t}\n\n\t/**\n\t * A generic `Array#map` utility function.\n\t * @private\n\t * @param {Array} array The array to iterate over.\n\t * @param {Function} callback The function that gets called for every array\n\t * item.\n\t * @returns {Array} A new array of values returned by the callback function.\n\t */\n\tfunction map(array, fn) {\n\t\tvar length = array.length;\n\t\tvar result = [];\n\t\twhile (length--) {\n\t\t\tresult[length] = fn(array[length]);\n\t\t}\n\t\treturn result;\n\t}\n\n\t/**\n\t * A simple `Array#map`-like wrapper to work with domain name strings or email\n\t * addresses.\n\t * @private\n\t * @param {String} domain The domain name or email address.\n\t * @param {Function} callback The function that gets called for every\n\t * character.\n\t * @returns {Array} A new string of characters returned by the callback\n\t * function.\n\t */\n\tfunction mapDomain(string, fn) {\n\t\tvar parts = string.split('@');\n\t\tvar result = '';\n\t\tif (parts.length > 1) {\n\t\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t\t// the local part (i.e. everything up to `@`) intact.\n\t\t\tresult = parts[0] + '@';\n\t\t\tstring = parts[1];\n\t\t}\n\t\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\t\tstring = string.replace(regexSeparators, '\\x2E');\n\t\tvar labels = string.split('.');\n\t\tvar encoded = map(labels, fn).join('.');\n\t\treturn result + encoded;\n\t}\n\n\t/**\n\t * Creates an array containing the numeric code points of each Unicode\n\t * character in the string. While JavaScript uses UCS-2 internally,\n\t * this function will convert a pair of surrogate halves (each of which\n\t * UCS-2 exposes as separate characters) into a single code point,\n\t * matching UTF-16.\n\t * @see `punycode.ucs2.encode`\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode.ucs2\n\t * @name decode\n\t * @param {String} string The Unicode input string (UCS-2).\n\t * @returns {Array} The new array of code points.\n\t */\n\tfunction ucs2decode(string) {\n\t\tvar output = [],\n\t\t    counter = 0,\n\t\t    length = string.length,\n\t\t    value,\n\t\t    extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\n\t/**\n\t * Creates a string based on an array of numeric code points.\n\t * @see `punycode.ucs2.decode`\n\t * @memberOf punycode.ucs2\n\t * @name encode\n\t * @param {Array} codePoints The array of numeric code points.\n\t * @returns {String} The new Unicode string (UCS-2).\n\t */\n\tfunction ucs2encode(array) {\n\t\treturn map(array, function(value) {\n\t\t\tvar output = '';\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t\treturn output;\n\t\t}).join('');\n\t}\n\n\t/**\n\t * Converts a basic code point into a digit/integer.\n\t * @see `digitToBasic()`\n\t * @private\n\t * @param {Number} codePoint The basic numeric code point value.\n\t * @returns {Number} The numeric value of a basic code point (for use in\n\t * representing integers) in the range `0` to `base - 1`, or `base` if\n\t * the code point does not represent a value.\n\t */\n\tfunction basicToDigit(codePoint) {\n\t\tif (codePoint - 48 < 10) {\n\t\t\treturn codePoint - 22;\n\t\t}\n\t\tif (codePoint - 65 < 26) {\n\t\t\treturn codePoint - 65;\n\t\t}\n\t\tif (codePoint - 97 < 26) {\n\t\t\treturn codePoint - 97;\n\t\t}\n\t\treturn base;\n\t}\n\n\t/**\n\t * Converts a digit/integer into a basic code point.\n\t * @see `basicToDigit()`\n\t * @private\n\t * @param {Number} digit The numeric value of a basic code point.\n\t * @returns {Number} The basic code point whose value (when used for\n\t * representing integers) is `digit`, which needs to be in the range\n\t * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n\t * used; else, the lowercase form is used. The behavior is undefined\n\t * if `flag` is non-zero and `digit` has no uppercase form.\n\t */\n\tfunction digitToBasic(digit, flag) {\n\t\t//  0..25 map to ASCII a..z or A..Z\n\t\t// 26..35 map to ASCII 0..9\n\t\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n\t}\n\n\t/**\n\t * Bias adaptation function as per section 3.4 of RFC 3492.\n\t * http://tools.ietf.org/html/rfc3492#section-3.4\n\t * @private\n\t */\n\tfunction adapt(delta, numPoints, firstTime) {\n\t\tvar k = 0;\n\t\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\t\tdelta += floor(delta / numPoints);\n\t\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\t\tdelta = floor(delta / baseMinusTMin);\n\t\t}\n\t\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n\t}\n\n\t/**\n\t * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n\t * symbols.\n\t * @memberOf punycode\n\t * @param {String} input The Punycode string of ASCII-only symbols.\n\t * @returns {String} The resulting string of Unicode symbols.\n\t */\n\tfunction decode(input) {\n\t\t// Don't use UCS-2\n\t\tvar output = [],\n\t\t    inputLength = input.length,\n\t\t    out,\n\t\t    i = 0,\n\t\t    n = initialN,\n\t\t    bias = initialBias,\n\t\t    basic,\n\t\t    j,\n\t\t    index,\n\t\t    oldi,\n\t\t    w,\n\t\t    k,\n\t\t    digit,\n\t\t    t,\n\t\t    /** Cached calculation results */\n\t\t    baseMinusT;\n\n\t\t// Handle the basic code points: let `basic` be the number of input code\n\t\t// points before the last delimiter, or `0` if there is none, then copy\n\t\t// the first basic code points to the output.\n\n\t\tbasic = input.lastIndexOf(delimiter);\n\t\tif (basic < 0) {\n\t\t\tbasic = 0;\n\t\t}\n\n\t\tfor (j = 0; j < basic; ++j) {\n\t\t\t// if it's not a basic code point\n\t\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\t\terror('not-basic');\n\t\t\t}\n\t\t\toutput.push(input.charCodeAt(j));\n\t\t}\n\n\t\t// Main decoding loop: start just after the last delimiter if any basic code\n\t\t// points were copied; start at the beginning otherwise.\n\n\t\tfor (index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t\t// `index` is the index of the next character to be consumed.\n\t\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t\t// which gets added to `i`. The overflow checking is easier\n\t\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t\t// value at the end to obtain `delta`.\n\t\t\tfor (oldi = i, w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\t\tif (index >= inputLength) {\n\t\t\t\t\terror('invalid-input');\n\t\t\t\t}\n\n\t\t\t\tdigit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\t\tif (digit >= base || digit > floor((maxInt - i) / w)) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\ti += digit * w;\n\t\t\t\tt = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\t\tif (digit < t) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tbaseMinusT = base - t;\n\t\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\tw *= baseMinusT;\n\n\t\t\t}\n\n\t\t\tout = output.length + 1;\n\t\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t\t// incrementing `n` each time, so we'll fix that now:\n\t\t\tif (floor(i / out) > maxInt - n) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tn += floor(i / out);\n\t\t\ti %= out;\n\n\t\t\t// Insert `n` at position `i` of the output\n\t\t\toutput.splice(i++, 0, n);\n\n\t\t}\n\n\t\treturn ucs2encode(output);\n\t}\n\n\t/**\n\t * Converts a string of Unicode symbols (e.g. a domain name label) to a\n\t * Punycode string of ASCII-only symbols.\n\t * @memberOf punycode\n\t * @param {String} input The string of Unicode symbols.\n\t * @returns {String} The resulting Punycode string of ASCII-only symbols.\n\t */\n\tfunction encode(input) {\n\t\tvar n,\n\t\t    delta,\n\t\t    handledCPCount,\n\t\t    basicLength,\n\t\t    bias,\n\t\t    j,\n\t\t    m,\n\t\t    q,\n\t\t    k,\n\t\t    t,\n\t\t    currentValue,\n\t\t    output = [],\n\t\t    /** `inputLength` will hold the number of code points in `input`. */\n\t\t    inputLength,\n\t\t    /** Cached calculation results */\n\t\t    handledCPCountPlusOne,\n\t\t    baseMinusT,\n\t\t    qMinusT;\n\n\t\t// Convert the input in UCS-2 to Unicode\n\t\tinput = ucs2decode(input);\n\n\t\t// Cache the length\n\t\tinputLength = input.length;\n\n\t\t// Initialize the state\n\t\tn = initialN;\n\t\tdelta = 0;\n\t\tbias = initialBias;\n\n\t\t// Handle the basic code points\n\t\tfor (j = 0; j < inputLength; ++j) {\n\t\t\tcurrentValue = input[j];\n\t\t\tif (currentValue < 0x80) {\n\t\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t\t}\n\t\t}\n\n\t\thandledCPCount = basicLength = output.length;\n\n\t\t// `handledCPCount` is the number of code points that have been handled;\n\t\t// `basicLength` is the number of basic code points.\n\n\t\t// Finish the basic string - if it is not empty - with a delimiter\n\t\tif (basicLength) {\n\t\t\toutput.push(delimiter);\n\t\t}\n\n\t\t// Main encoding loop:\n\t\twhile (handledCPCount < inputLength) {\n\n\t\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t\t// larger one:\n\t\t\tfor (m = maxInt, j = 0; j < inputLength; ++j) {\n\t\t\t\tcurrentValue = input[j];\n\t\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\t\tm = currentValue;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t\t// but guard against overflow\n\t\t\thandledCPCountPlusOne = handledCPCount + 1;\n\t\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\t\tn = m;\n\n\t\t\tfor (j = 0; j < inputLength; ++j) {\n\t\t\t\tcurrentValue = input[j];\n\n\t\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\tif (currentValue == n) {\n\t\t\t\t\t// Represent delta as a generalized variable-length integer\n\t\t\t\t\tfor (q = delta, k = base; /* no condition */; k += base) {\n\t\t\t\t\t\tt = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tqMinusT = q - t;\n\t\t\t\t\t\tbaseMinusT = base - t;\n\t\t\t\t\t\toutput.push(\n\t\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t\t);\n\t\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t\t}\n\n\t\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n\t\t\t\t\tdelta = 0;\n\t\t\t\t\t++handledCPCount;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t++delta;\n\t\t\t++n;\n\n\t\t}\n\t\treturn output.join('');\n\t}\n\n\t/**\n\t * Converts a Punycode string representing a domain name or an email address\n\t * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n\t * it doesn't matter if you call it on a string that has already been\n\t * converted to Unicode.\n\t * @memberOf punycode\n\t * @param {String} input The Punycoded domain name or email address to\n\t * convert to Unicode.\n\t * @returns {String} The Unicode representation of the given Punycode\n\t * string.\n\t */\n\tfunction toUnicode(input) {\n\t\treturn mapDomain(input, function(string) {\n\t\t\treturn regexPunycode.test(string)\n\t\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t\t: string;\n\t\t});\n\t}\n\n\t/**\n\t * Converts a Unicode string representing a domain name or an email address to\n\t * Punycode. Only the non-ASCII parts of the domain name will be converted,\n\t * i.e. it doesn't matter if you call it with a domain that's already in\n\t * ASCII.\n\t * @memberOf punycode\n\t * @param {String} input The domain name or email address to convert, as a\n\t * Unicode string.\n\t * @returns {String} The Punycode representation of the given domain name or\n\t * email address.\n\t */\n\tfunction toASCII(input) {\n\t\treturn mapDomain(input, function(string) {\n\t\t\treturn regexNonASCII.test(string)\n\t\t\t\t? 'xn--' + encode(string)\n\t\t\t\t: string;\n\t\t});\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\t/** Define the public API */\n\tpunycode = {\n\t\t/**\n\t\t * A string representing the current Punycode.js version number.\n\t\t * @memberOf punycode\n\t\t * @type String\n\t\t */\n\t\t'version': '1.3.2',\n\t\t/**\n\t\t * An object of methods to convert from JavaScript's internal character\n\t\t * representation (UCS-2) to Unicode code points, and back.\n\t\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t\t * @memberOf punycode\n\t\t * @type Object\n\t\t */\n\t\t'ucs2': {\n\t\t\t'decode': ucs2decode,\n\t\t\t'encode': ucs2encode\n\t\t},\n\t\t'decode': decode,\n\t\t'encode': encode,\n\t\t'toASCII': toASCII,\n\t\t'toUnicode': toUnicode\n\t};\n\n\t/** Expose `punycode` */\n\t// Some AMD build optimizers, like r.js, check for specific condition patterns\n\t// like the following:\n\tif (\n\t\ttypeof define == 'function' &&\n\t\ttypeof define.amd == 'object' &&\n\t\tdefine.amd\n\t) {\n\t\tdefine('punycode', function() {\n\t\t\treturn punycode;\n\t\t});\n\t} else if (freeExports && freeModule) {\n\t\tif (module.exports == freeExports) { // in Node.js or RingoJS v0.8.0+\n\t\t\tfreeModule.exports = punycode;\n\t\t} else { // in Narwhal or RingoJS v0.7.0-\n\t\t\tfor (key in punycode) {\n\t\t\t\tpunycode.hasOwnProperty(key) && (freeExports[key] = punycode[key]);\n\t\t\t}\n\t\t}\n\t} else { // in Rhino or a web browser\n\t\troot.punycode = punycode;\n\t}\n\n}(this));\n", "'use strict';\n\nmodule.exports = {\n  isString: function(arg) {\n    return typeof(arg) === 'string';\n  },\n  isObject: function(arg) {\n    return typeof(arg) === 'object' && arg !== null;\n  },\n  isNull: function(arg) {\n    return arg === null;\n  },\n  isNullOrUndefined: function(arg) {\n    return arg == null;\n  }\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n// If obj.hasOwnProperty has been overridden, then calling\n// obj.hasOwnProperty(prop) will break.\n// See: https://github.com/joyent/node/issues/1707\nfunction hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nmodule.exports = function(qs, sep, eq, options) {\n  sep = sep || '&';\n  eq = eq || '=';\n  var obj = {};\n\n  if (typeof qs !== 'string' || qs.length === 0) {\n    return obj;\n  }\n\n  var regexp = /\\+/g;\n  qs = qs.split(sep);\n\n  var maxKeys = 1000;\n  if (options && typeof options.maxKeys === 'number') {\n    maxKeys = options.maxKeys;\n  }\n\n  var len = qs.length;\n  // maxKeys <= 0 means that we should not limit keys count\n  if (maxKeys > 0 && len > maxKeys) {\n    len = maxKeys;\n  }\n\n  for (var i = 0; i < len; ++i) {\n    var x = qs[i].replace(regexp, '%20'),\n        idx = x.indexOf(eq),\n        kstr, vstr, k, v;\n\n    if (idx >= 0) {\n      kstr = x.substr(0, idx);\n      vstr = x.substr(idx + 1);\n    } else {\n      kstr = x;\n      vstr = '';\n    }\n\n    k = decodeURIComponent(kstr);\n    v = decodeURIComponent(vstr);\n\n    if (!hasOwnProperty(obj, k)) {\n      obj[k] = v;\n    } else if (Array.isArray(obj[k])) {\n      obj[k].push(v);\n    } else {\n      obj[k] = [obj[k], v];\n    }\n  }\n\n  return obj;\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar stringifyPrimitive = function(v) {\n  switch (typeof v) {\n    case 'string':\n      return v;\n\n    case 'boolean':\n      return v ? 'true' : 'false';\n\n    case 'number':\n      return isFinite(v) ? v : '';\n\n    default:\n      return '';\n  }\n};\n\nmodule.exports = function(obj, sep, eq, name) {\n  sep = sep || '&';\n  eq = eq || '=';\n  if (obj === null) {\n    obj = undefined;\n  }\n\n  if (typeof obj === 'object') {\n    return Object.keys(obj).map(function(k) {\n      var ks = encodeURIComponent(stringifyPrimitive(k)) + eq;\n      if (Array.isArray(obj[k])) {\n        return obj[k].map(function(v) {\n          return ks + encodeURIComponent(stringifyPrimitive(v));\n        }).join(sep);\n      } else {\n        return ks + encodeURIComponent(stringifyPrimitive(obj[k]));\n      }\n    }).join(sep);\n\n  }\n\n  if (!name) return '';\n  return encodeURIComponent(stringifyPrimitive(name)) + eq +\n         encodeURIComponent(stringifyPrimitive(obj));\n};\n", "'use strict';\n\nexports.decode = exports.parse = require('./decode');\nexports.encode = exports.stringify = require('./encode');\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar punycode = require('punycode');\nvar util = require('./util');\n\nexports.parse = urlParse;\nexports.resolve = urlResolve;\nexports.resolveObject = urlResolveObject;\nexports.format = urlFormat;\n\nexports.Url = Url;\n\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.host = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.query = null;\n  this.pathname = null;\n  this.path = null;\n  this.href = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n// define these here so at least they only have to be\n// compiled once on the first module load.\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n    portPattern = /:[0-9]*$/,\n\n    // Special case for a simple path URL\n    simplePathPattern = /^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/,\n\n    // RFC 2396: characters reserved for delimiting URLs.\n    // We actually just auto-escape these.\n    delims = ['<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t'],\n\n    // RFC 2396: characters not allowed for various reasons.\n    unwise = ['{', '}', '|', '\\\\', '^', '`'].concat(delims),\n\n    // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n    autoEscape = ['\\''].concat(unwise),\n    // Characters that are never ever allowed in a hostname.\n    // Note that any invalid chars are also handled, but these\n    // are the ones that are *expected* to be seen, so we fast-path\n    // them.\n    nonHostChars = ['%', '/', '?', ';', '#'].concat(autoEscape),\n    hostEndingChars = ['/', '?', '#'],\n    hostnameMaxLen = 255,\n    hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n    hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n    // protocols that can allow \"unsafe\" and \"unwise\" chars.\n    unsafeProtocol = {\n      'javascript': true,\n      'javascript:': true\n    },\n    // protocols that never have a hostname.\n    hostlessProtocol = {\n      'javascript': true,\n      'javascript:': true\n    },\n    // protocols that always contain a // bit.\n    slashedProtocol = {\n      'http': true,\n      'https': true,\n      'ftp': true,\n      'gopher': true,\n      'file': true,\n      'http:': true,\n      'https:': true,\n      'ftp:': true,\n      'gopher:': true,\n      'file:': true\n    },\n    querystring = require('querystring');\n\nfunction urlParse(url, parseQueryString, slashesDenoteHost) {\n  if (url && util.isObject(url) && url instanceof Url) return url;\n\n  var u = new Url;\n  u.parse(url, parseQueryString, slashesDenoteHost);\n  return u;\n}\n\nUrl.prototype.parse = function(url, parseQueryString, slashesDenoteHost) {\n  if (!util.isString(url)) {\n    throw new TypeError(\"Parameter 'url' must be a string, not \" + typeof url);\n  }\n\n  // Copy chrome, IE, opera backslash-handling behavior.\n  // Back slashes before the query string get converted to forward slashes\n  // See: https://code.google.com/p/chromium/issues/detail?id=25916\n  var queryIndex = url.indexOf('?'),\n      splitter =\n          (queryIndex !== -1 && queryIndex < url.indexOf('#')) ? '?' : '#',\n      uSplit = url.split(splitter),\n      slashRegex = /\\\\/g;\n  uSplit[0] = uSplit[0].replace(slashRegex, '/');\n  url = uSplit.join(splitter);\n\n  var rest = url;\n\n  // trim before proceeding.\n  // This is to support parse stuff like \"  http://foo.com  \\n\"\n  rest = rest.trim();\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.path = rest;\n      this.href = rest;\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n        if (parseQueryString) {\n          this.query = querystring.parse(this.search.substr(1));\n        } else {\n          this.query = this.search.substr(1);\n        }\n      } else if (parseQueryString) {\n        this.search = '';\n        this.query = {};\n      }\n      return this;\n    }\n  }\n\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    var lowerProto = proto.toLowerCase();\n    this.protocol = lowerProto;\n    rest = rest.substr(proto.length);\n  }\n\n  // figure out if it's got a host\n  // user@server is *always* interpreted as a hostname, and url\n  // resolution will treat //foo/bar as host=foo,path=bar because that's\n  // how the browser resolves relative URLs.\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@\\/]+@[^@\\/]+/)) {\n    var slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n\n  if (!hostlessProtocol[proto] &&\n      (slashes || (proto && !slashedProtocol[proto]))) {\n\n    // there's a hostname.\n    // the first instance of /, ?, ;, or # ends the host.\n    //\n    // If there is an @ in the hostname, then non-host chars *are* allowed\n    // to the left of the last @ sign, unless some host-ending character\n    // comes *before* the @-sign.\n    // URLs are obnoxious.\n    //\n    // ex:\n    // http://a@b@c/ => user:a@b host:c\n    // http://a@b?@c => user:a host:c path:/?@c\n\n    // v0.12 TODO(isaacs): This is not quite how Chrome does things.\n    // Review our test case against browsers more comprehensively.\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (var i = 0; i < hostEndingChars.length; i++) {\n      var hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd))\n        hostEnd = hec;\n    }\n\n    // at this point, either we have an explicit point where the\n    // auth portion cannot go past, or the last @ char is the decider.\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      // atSign must be in auth portion.\n      // http://a@b/c@d => host:b auth:a path:/c@d\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    // Now we have a portion which is definitely the auth.\n    // Pull that off.\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = decodeURIComponent(auth);\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (var i = 0; i < nonHostChars.length; i++) {\n      var hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd))\n        hostEnd = hec;\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1)\n      hostEnd = rest.length;\n\n    this.host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost();\n\n    // we've indicated that there is a hostname,\n    // so even if it's empty, it has to be present.\n    this.hostname = this.hostname || '';\n\n    // if hostname begins with [ and ends with ]\n    // assume that it's an IPv6 address.\n    var ipv6Hostname = this.hostname[0] === '[' &&\n        this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (var i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) continue;\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              // we replace non-ASCII char with a temporary placeholder\n              // we need this to make sure size of hostname is not\n              // broken by replacing non-ASCII by nothing\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = '/' + notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    } else {\n      // hostnames are always lower case.\n      this.hostname = this.hostname.toLowerCase();\n    }\n\n    if (!ipv6Hostname) {\n      // IDNA Support: Returns a punycoded representation of \"domain\".\n      // It only converts parts of the domain name that\n      // have non-ASCII characters, i.e. it doesn't matter if\n      // you call it with a domain that already is ASCII-only.\n      this.hostname = punycode.toASCII(this.hostname);\n    }\n\n    var p = this.port ? ':' + this.port : '';\n    var h = this.hostname || '';\n    this.host = h + p;\n    this.href += this.host;\n\n    // strip [ and ] from the hostname\n    // the host field still retains them, though\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n      if (rest[0] !== '/') {\n        rest = '/' + rest;\n      }\n    }\n  }\n\n  // now rest is set to the post-host stuff.\n  // chop off any delim chars.\n  if (!unsafeProtocol[lowerProto]) {\n\n    // First, make 100% sure that any \"autoEscape\" chars get\n    // escaped, even if encodeURIComponent doesn't think they\n    // need to be.\n    for (var i = 0, l = autoEscape.length; i < l; i++) {\n      var ae = autoEscape[i];\n      if (rest.indexOf(ae) === -1)\n        continue;\n      var esc = encodeURIComponent(ae);\n      if (esc === ae) {\n        esc = escape(ae);\n      }\n      rest = rest.split(ae).join(esc);\n    }\n  }\n\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    this.query = rest.substr(qm + 1);\n    if (parseQueryString) {\n      this.query = querystring.parse(this.query);\n    }\n    rest = rest.slice(0, qm);\n  } else if (parseQueryString) {\n    // no query string, but parseQueryString still requested\n    this.search = '';\n    this.query = {};\n  }\n  if (rest) this.pathname = rest;\n  if (slashedProtocol[lowerProto] &&\n      this.hostname && !this.pathname) {\n    this.pathname = '/';\n  }\n\n  //to support http.request\n  if (this.pathname || this.search) {\n    var p = this.pathname || '';\n    var s = this.search || '';\n    this.path = p + s;\n  }\n\n  // finally, reconstruct the href based on what has been validated.\n  this.href = this.format();\n  return this;\n};\n\n// format a parsed object into a url string\nfunction urlFormat(obj) {\n  // ensure it's an object, and not a string url.\n  // If it's an obj, this is a no-op.\n  // this way, you can call url_format() on strings\n  // to clean up potentially wonky urls.\n  if (util.isString(obj)) obj = urlParse(obj);\n  if (!(obj instanceof Url)) return Url.prototype.format.call(obj);\n  return obj.format();\n}\n\nUrl.prototype.format = function() {\n  var auth = this.auth || '';\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, ':');\n    auth += '@';\n  }\n\n  var protocol = this.protocol || '',\n      pathname = this.pathname || '',\n      hash = this.hash || '',\n      host = false,\n      query = '';\n\n  if (this.host) {\n    host = auth + this.host;\n  } else if (this.hostname) {\n    host = auth + (this.hostname.indexOf(':') === -1 ?\n        this.hostname :\n        '[' + this.hostname + ']');\n    if (this.port) {\n      host += ':' + this.port;\n    }\n  }\n\n  if (this.query &&\n      util.isObject(this.query) &&\n      Object.keys(this.query).length) {\n    query = querystring.stringify(this.query);\n  }\n\n  var search = this.search || (query && ('?' + query)) || '';\n\n  if (protocol && protocol.substr(-1) !== ':') protocol += ':';\n\n  // only the slashedProtocols get the //.  Not mailto:, xmpp:, etc.\n  // unless they had them to begin with.\n  if (this.slashes ||\n      (!protocol || slashedProtocol[protocol]) && host !== false) {\n    host = '//' + (host || '');\n    if (pathname && pathname.charAt(0) !== '/') pathname = '/' + pathname;\n  } else if (!host) {\n    host = '';\n  }\n\n  if (hash && hash.charAt(0) !== '#') hash = '#' + hash;\n  if (search && search.charAt(0) !== '?') search = '?' + search;\n\n  pathname = pathname.replace(/[?#]/g, function(match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace('#', '%23');\n\n  return protocol + host + pathname + search + hash;\n};\n\nfunction urlResolve(source, relative) {\n  return urlParse(source, false, true).resolve(relative);\n}\n\nUrl.prototype.resolve = function(relative) {\n  return this.resolveObject(urlParse(relative, false, true)).format();\n};\n\nfunction urlResolveObject(source, relative) {\n  if (!source) return relative;\n  return urlParse(source, false, true).resolveObject(relative);\n}\n\nUrl.prototype.resolveObject = function(relative) {\n  if (util.isString(relative)) {\n    var rel = new Url();\n    rel.parse(relative, false, true);\n    relative = rel;\n  }\n\n  var result = new Url();\n  var tkeys = Object.keys(this);\n  for (var tk = 0; tk < tkeys.length; tk++) {\n    var tkey = tkeys[tk];\n    result[tkey] = this[tkey];\n  }\n\n  // hash is always overridden, no matter what.\n  // even href=\"\" will remove it.\n  result.hash = relative.hash;\n\n  // if the relative url is empty, then there's nothing left to do here.\n  if (relative.href === '') {\n    result.href = result.format();\n    return result;\n  }\n\n  // hrefs like //foo/bar always cut to the protocol.\n  if (relative.slashes && !relative.protocol) {\n    // take everything except the protocol from relative\n    var rkeys = Object.keys(relative);\n    for (var rk = 0; rk < rkeys.length; rk++) {\n      var rkey = rkeys[rk];\n      if (rkey !== 'protocol')\n        result[rkey] = relative[rkey];\n    }\n\n    //urlParse appends trailing / to urls like http://www.example.com\n    if (slashedProtocol[result.protocol] &&\n        result.hostname && !result.pathname) {\n      result.path = result.pathname = '/';\n    }\n\n    result.href = result.format();\n    return result;\n  }\n\n  if (relative.protocol && relative.protocol !== result.protocol) {\n    // if it's a known url protocol, then changing\n    // the protocol does weird things\n    // first, if it's not file:, then we MUST have a host,\n    // and if there was a path\n    // to begin with, then we MUST have a path.\n    // if it is file:, then the host is dropped,\n    // because that's known to be hostless.\n    // anything else is assumed to be absolute.\n    if (!slashedProtocol[relative.protocol]) {\n      var keys = Object.keys(relative);\n      for (var v = 0; v < keys.length; v++) {\n        var k = keys[v];\n        result[k] = relative[k];\n      }\n      result.href = result.format();\n      return result;\n    }\n\n    result.protocol = relative.protocol;\n    if (!relative.host && !hostlessProtocol[relative.protocol]) {\n      var relPath = (relative.pathname || '').split('/');\n      while (relPath.length && !(relative.host = relPath.shift()));\n      if (!relative.host) relative.host = '';\n      if (!relative.hostname) relative.hostname = '';\n      if (relPath[0] !== '') relPath.unshift('');\n      if (relPath.length < 2) relPath.unshift('');\n      result.pathname = relPath.join('/');\n    } else {\n      result.pathname = relative.pathname;\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    result.host = relative.host || '';\n    result.auth = relative.auth;\n    result.hostname = relative.hostname || relative.host;\n    result.port = relative.port;\n    // to support http.request\n    if (result.pathname || result.search) {\n      var p = result.pathname || '';\n      var s = result.search || '';\n      result.path = p + s;\n    }\n    result.slashes = result.slashes || relative.slashes;\n    result.href = result.format();\n    return result;\n  }\n\n  var isSourceAbs = (result.pathname && result.pathname.charAt(0) === '/'),\n      isRelAbs = (\n          relative.host ||\n          relative.pathname && relative.pathname.charAt(0) === '/'\n      ),\n      mustEndAbs = (isRelAbs || isSourceAbs ||\n                    (result.host && relative.pathname)),\n      removeAllDots = mustEndAbs,\n      srcPath = result.pathname && result.pathname.split('/') || [],\n      relPath = relative.pathname && relative.pathname.split('/') || [],\n      psychotic = result.protocol && !slashedProtocol[result.protocol];\n\n  // if the url is a non-slashed url, then relative\n  // links like ../.. should be able\n  // to crawl up to the hostname, as well.  This is strange.\n  // result.protocol has already been set by now.\n  // Later on, put the first path part into the host field.\n  if (psychotic) {\n    result.hostname = '';\n    result.port = null;\n    if (result.host) {\n      if (srcPath[0] === '') srcPath[0] = result.host;\n      else srcPath.unshift(result.host);\n    }\n    result.host = '';\n    if (relative.protocol) {\n      relative.hostname = null;\n      relative.port = null;\n      if (relative.host) {\n        if (relPath[0] === '') relPath[0] = relative.host;\n        else relPath.unshift(relative.host);\n      }\n      relative.host = null;\n    }\n    mustEndAbs = mustEndAbs && (relPath[0] === '' || srcPath[0] === '');\n  }\n\n  if (isRelAbs) {\n    // it's absolute.\n    result.host = (relative.host || relative.host === '') ?\n                  relative.host : result.host;\n    result.hostname = (relative.hostname || relative.hostname === '') ?\n                      relative.hostname : result.hostname;\n    result.search = relative.search;\n    result.query = relative.query;\n    srcPath = relPath;\n    // fall through to the dot-handling below.\n  } else if (relPath.length) {\n    // it's relative\n    // throw away the existing file, and take the new path instead.\n    if (!srcPath) srcPath = [];\n    srcPath.pop();\n    srcPath = srcPath.concat(relPath);\n    result.search = relative.search;\n    result.query = relative.query;\n  } else if (!util.isNullOrUndefined(relative.search)) {\n    // just pull out the search.\n    // like href='?foo'.\n    // Put this after the other two cases because it simplifies the booleans\n    if (psychotic) {\n      result.hostname = result.host = srcPath.shift();\n      //occationaly the auth can get stuck only in host\n      //this especially happens in cases like\n      //url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n      var authInHost = result.host && result.host.indexOf('@') > 0 ?\n                       result.host.split('@') : false;\n      if (authInHost) {\n        result.auth = authInHost.shift();\n        result.host = result.hostname = authInHost.shift();\n      }\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    //to support http.request\n    if (!util.isNull(result.pathname) || !util.isNull(result.search)) {\n      result.path = (result.pathname ? result.pathname : '') +\n                    (result.search ? result.search : '');\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  if (!srcPath.length) {\n    // no path at all.  easy.\n    // we've already handled the other stuff above.\n    result.pathname = null;\n    //to support http.request\n    if (result.search) {\n      result.path = '/' + result.search;\n    } else {\n      result.path = null;\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  // if a url ENDs in . or .., then it must get a trailing slash.\n  // however, if it ends in anything else non-slashy,\n  // then it must NOT get a trailing slash.\n  var last = srcPath.slice(-1)[0];\n  var hasTrailingSlash = (\n      (result.host || relative.host || srcPath.length > 1) &&\n      (last === '.' || last === '..') || last === '');\n\n  // strip single dots, resolve double dots to parent dir\n  // if the path tries to go above the root, `up` ends up > 0\n  var up = 0;\n  for (var i = srcPath.length; i >= 0; i--) {\n    last = srcPath[i];\n    if (last === '.') {\n      srcPath.splice(i, 1);\n    } else if (last === '..') {\n      srcPath.splice(i, 1);\n      up++;\n    } else if (up) {\n      srcPath.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (!mustEndAbs && !removeAllDots) {\n    for (; up--; up) {\n      srcPath.unshift('..');\n    }\n  }\n\n  if (mustEndAbs && srcPath[0] !== '' &&\n      (!srcPath[0] || srcPath[0].charAt(0) !== '/')) {\n    srcPath.unshift('');\n  }\n\n  if (hasTrailingSlash && (srcPath.join('/').substr(-1) !== '/')) {\n    srcPath.push('');\n  }\n\n  var isAbsolute = srcPath[0] === '' ||\n      (srcPath[0] && srcPath[0].charAt(0) === '/');\n\n  // put the host back\n  if (psychotic) {\n    result.hostname = result.host = isAbsolute ? '' :\n                                    srcPath.length ? srcPath.shift() : '';\n    //occationaly the auth can get stuck only in host\n    //this especially happens in cases like\n    //url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n    var authInHost = result.host && result.host.indexOf('@') > 0 ?\n                     result.host.split('@') : false;\n    if (authInHost) {\n      result.auth = authInHost.shift();\n      result.host = result.hostname = authInHost.shift();\n    }\n  }\n\n  mustEndAbs = mustEndAbs || (result.host && srcPath.length);\n\n  if (mustEndAbs && !isAbsolute) {\n    srcPath.unshift('');\n  }\n\n  if (!srcPath.length) {\n    result.pathname = null;\n    result.path = null;\n  } else {\n    result.pathname = srcPath.join('/');\n  }\n\n  //to support request.http\n  if (!util.isNull(result.pathname) || !util.isNull(result.search)) {\n    result.path = (result.pathname ? result.pathname : '') +\n                  (result.search ? result.search : '');\n  }\n  result.auth = relative.auth || result.auth;\n  result.slashes = result.slashes || relative.slashes;\n  result.href = result.format();\n  return result;\n};\n\nUrl.prototype.parseHost = function() {\n  var host = this.host;\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) this.hostname = host;\n};\n", "/**\n * This file contains redeclared types for Node `url` and `querystring` modules. These modules\n * don't provide their own typings but instead are a part of the full Node typings. The purpose of\n * this file is to redeclare the required types to avoid having the whole Node types as a\n * dependency.\n */\n\nimport { parse as _parse, format as _format, resolve as _resolve } from 'url';\n\ninterface ParsedUrlQuery\n{\n    [key: string]: string | string[];\n}\n\ninterface ParsedUrlQueryInput\n{\n    [key: string]: unknown;\n}\n\ninterface UrlObjectCommon\n{\n    auth?: string;\n    hash?: string;\n    host?: string;\n    hostname?: string;\n    href?: string;\n    path?: string;\n    pathname?: string;\n    protocol?: string;\n    search?: string;\n    slashes?: boolean;\n}\n\n// Input to `url.format`\ninterface UrlObject extends UrlObjectCommon\n{\n    port?: string | number;\n    query?: string | null | ParsedUrlQueryInput;\n}\n\n// Output of `url.parse`\ninterface Url extends UrlObjectCommon\n{\n    port?: string;\n    query?: string | null | ParsedUrlQuery;\n}\n\ninterface UrlWithParsedQuery extends Url\n{\n    query: ParsedUrlQuery;\n}\n\ninterface UrlWithStringQuery extends Url\n{\n    query: string | null;\n}\n\ninterface URLFormatOptions\n{\n    auth?: boolean;\n    fragment?: boolean;\n    search?: boolean;\n    unicode?: boolean;\n}\n\ntype ParseFunction = {\n    (urlStr: string): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: false | undefined, slashesDenoteHost?: boolean): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: true, slashesDenoteHost?: boolean): UrlWithParsedQuery;\n    (urlStr: string, parseQueryString: boolean, slashesDenoteHost?: boolean): Url;\n};\n\ntype FormatFunction = {\n    (URL: URL, options?: URLFormatOptions): string;\n    (urlObject: UrlObject | string): string;\n};\n\ntype ResolveFunction = {\n    (from: string, to: string): string;\n};\n\nexport const url = {\n    parse: _parse as ParseFunction,\n    format: _format as FormatFunction,\n    resolve: _resolve as ResolveFunction,\n};\n", "import { settings } from '@pixi/settings';\n\nfunction assertPath(path: string)\n{\n    if (typeof path !== 'string')\n    {\n        throw new TypeError(`Path must be a string. Received ${JSON.stringify(path)}`);\n    }\n}\n\nfunction removeUrlParams(url: string): string\n{\n    const re = url.split('?')[0];\n\n    return re.split('#')[0];\n}\n\nfunction escapeRegExp(string: string)\n{\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n}\n\nfunction replaceAll(str: string, find: string, replace: string)\n{\n    return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path: string, allowAboveRoot: boolean)\n{\n    let res = '';\n    let lastSegmentLength = 0;\n    let lastSlash = -1;\n    let dots = 0;\n    let code: number;\n\n    for (let i = 0; i <= path.length; ++i)\n    {\n        if (i < path.length)\n        {\n            code = path.charCodeAt(i);\n        }\n        else if (code === 47)\n        {\n            break;\n        }\n        else\n        {\n            code = 47;\n        }\n        if (code === 47)\n        {\n            if (lastSlash === i - 1 || dots === 1)\n            {\n                // NOOP\n            }\n            else if (lastSlash !== i - 1 && dots === 2)\n            {\n                if (\n                    res.length < 2\n                    || lastSegmentLength !== 2\n                    || res.charCodeAt(res.length - 1) !== 46\n                    || res.charCodeAt(res.length - 2) !== 46\n                )\n                {\n                    if (res.length > 2)\n                    {\n                        const lastSlashIndex = res.lastIndexOf('/');\n\n                        if (lastSlashIndex !== res.length - 1)\n                        {\n                            if (lastSlashIndex === -1)\n                            {\n                                res = '';\n                                lastSegmentLength = 0;\n                            }\n                            else\n                            {\n                                res = res.slice(0, lastSlashIndex);\n                                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n                            }\n                            lastSlash = i;\n                            dots = 0;\n                            continue;\n                        }\n                    }\n                    else if (res.length === 2 || res.length === 1)\n                    {\n                        res = '';\n                        lastSegmentLength = 0;\n                        lastSlash = i;\n                        dots = 0;\n                        continue;\n                    }\n                }\n                if (allowAboveRoot)\n                {\n                    if (res.length > 0)\n                    { res += '/..'; }\n                    else\n                    { res = '..'; }\n                    lastSegmentLength = 2;\n                }\n            }\n            else\n            {\n                if (res.length > 0)\n                {\n                    res += `/${path.slice(lastSlash + 1, i)}`;\n                }\n                else\n                {\n                    res = path.slice(lastSlash + 1, i);\n                }\n                lastSegmentLength = i - lastSlash - 1;\n            }\n            lastSlash = i;\n            dots = 0;\n        }\n        else if (code === 46 && dots !== -1)\n        {\n            ++dots;\n        }\n        else\n        {\n            dots = -1;\n        }\n    }\n\n    return res;\n}\n\nexport interface Path\n{\n    toPosix: (path: string) => string;\n    toAbsolute: (url: string, baseUrl?: string, rootUrl?: string) => string;\n    isUrl: (path: string) => boolean;\n    isDataUrl: (path: string) => boolean;\n    hasProtocol: (path: string) => boolean;\n    getProtocol: (path: string) => string;\n    normalize: (path: string) => string;\n    join: (...paths: string[]) => string;\n    isAbsolute: (path: string) => boolean;\n    dirname: (path: string) => string;\n    rootname: (path: string) => string;\n    basename: (path: string, ext?: string) => string;\n    extname: (path: string) => string;\n    parse: (path: string) => { root?: string, dir?: string, base?: string, ext?: string, name?: string };\n    sep: string,\n    delimiter: string\n}\n\nexport const path: Path = {\n    /**\n     * Converts a path to posix format.\n     * @param path - The path to convert to posix\n     */\n    toPosix(path: string) { return replaceAll(path, '\\\\', '/'); },\n    /**\n     * Checks if the path is a URL\n     * @param path - The path to check\n     */\n    isUrl(path: string) { return (/^https?:/).test(this.toPosix(path)); },\n    /**\n     * Checks if the path is a data URL\n     * @param path - The path to check\n     */\n    isDataUrl(path: string)\n    {\n        // eslint-disable-next-line max-len\n        return (/^data:([a-z]+\\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s<>]*?)$/i)\n            .test(path);\n    },\n    /**\n     * Checks if the path has a protocol e.g. http://\n     * This will return true for windows file paths\n     * @param path - The path to check\n     */\n    hasProtocol(path: string) { return (/^[^/:]+:\\//).test(this.toPosix(path)); },\n    /**\n     * Returns the protocol of the path e.g. http://, C:/, file:///\n     * @param path - The path to get the protocol from\n     */\n    getProtocol(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let protocol = '';\n\n        const isFile = (/^file:\\/\\/\\//).exec(path);\n        const isHttp = (/^[^/:]+:\\/\\//).exec(path);\n        const isWindows = (/^[^/:]+:\\//).exec(path);\n\n        if (isFile || isHttp || isWindows)\n        {\n            const arr = isFile?.[0] || isHttp?.[0] || isWindows?.[0];\n\n            protocol = arr;\n            path = path.slice(arr.length);\n        }\n\n        return protocol;\n    },\n\n    /**\n     * Converts URL to an absolute path.\n     * When loading from a Web Worker, we must use absolute paths.\n     * If the URL is already absolute we return it as is\n     * If it's not, we convert it\n     * @param url - The URL to test\n     * @param customBaseUrl - The base URL to use\n     * @param customRootUrl - The root URL to use\n     */\n    toAbsolute(url: string, customBaseUrl?: string, customRootUrl?: string)\n    {\n        if (this.isDataUrl(url)) return url;\n\n        const baseUrl = removeUrlParams(this.toPosix(customBaseUrl ?? settings.ADAPTER.getBaseUrl()));\n        const rootUrl = removeUrlParams(this.toPosix(customRootUrl ?? this.rootname(baseUrl)));\n\n        assertPath(url);\n        url = this.toPosix(url);\n\n        // root relative url\n        if (url.startsWith('/'))\n        {\n            return path.join(rootUrl, url.slice(1));\n        }\n\n        const absolutePath = this.isAbsolute(url) ? url : this.join(baseUrl, url);\n\n        return absolutePath;\n    },\n\n    /**\n     * Normalizes the given path, resolving '..' and '.' segments\n     * @param path - The path to normalize\n     */\n    normalize(path: string)\n    {\n        path = this.toPosix(path);\n        assertPath(path);\n\n        if (path.length === 0) return '.';\n\n        let protocol = '';\n        const isAbsolute = path.startsWith('/');\n\n        if (this.hasProtocol(path))\n        {\n            protocol = this.rootname(path);\n            path = path.slice(protocol.length);\n        }\n\n        const trailingSeparator = path.endsWith('/');\n\n        // Normalize the path\n        path = normalizeStringPosix(path, false);\n\n        if (path.length > 0 && trailingSeparator) path += '/';\n        if (isAbsolute) return `/${path}`;\n\n        return protocol + path;\n    },\n\n    /**\n     * Determines if path is an absolute path.\n     * Absolute paths can be urls, data urls, or paths on disk\n     * @param path - The path to test\n     */\n    isAbsolute(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        if (this.hasProtocol(path)) return true;\n\n        return path.startsWith('/');\n    },\n\n    /**\n     * Joins all given path segments together using the platform-specific separator as a delimiter,\n     * then normalizes the resulting path\n     * @param segments - The segments of the path to join\n     */\n    join(...segments: string[])\n    {\n        if (segments.length === 0)\n        { return '.'; }\n        let joined;\n\n        for (let i = 0; i < segments.length; ++i)\n        {\n            const arg = segments[i];\n\n            assertPath(arg);\n            if (arg.length > 0)\n            {\n                if (joined === undefined) joined = arg;\n                else\n                {\n                    const prevArg = segments[i - 1] ?? '';\n\n                    if (this.extname(prevArg))\n                    {\n                        joined += `/../${arg}`;\n                    }\n                    else\n                    {\n                        joined += `/${arg}`;\n                    }\n                }\n            }\n        }\n        if (joined === undefined) { return '.'; }\n\n        return this.normalize(joined);\n    },\n\n    /**\n     * Returns the directory name of a path\n     * @param path - The path to parse\n     */\n    dirname(path: string)\n    {\n        assertPath(path);\n        if (path.length === 0) return '.';\n        path = this.toPosix(path);\n        let code = path.charCodeAt(0);\n        const hasRoot = code === 47;\n        let end = -1;\n        let matchedSlash = true;\n\n        const proto = this.getProtocol(path);\n        const origpath = path;\n\n        path = path.slice(proto.length);\n\n        for (let i = path.length - 1; i >= 1; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                if (!matchedSlash)\n                {\n                    end = i;\n                    break;\n                }\n            }\n            else\n            {\n                // We saw the first non-path separator\n                matchedSlash = false;\n            }\n        }\n\n        // if end is -1 and its a url then we need to add the path back\n        // eslint-disable-next-line no-nested-ternary\n        if (end === -1) return hasRoot ? '/' : this.isUrl(origpath) ? proto + path : proto;\n        if (hasRoot && end === 1) return '//';\n\n        return proto + path.slice(0, end);\n    },\n\n    /**\n     * Returns the root of the path e.g. /, C:/, file:///, http://domain.com/\n     * @param path - The path to parse\n     */\n    rootname(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let root = '';\n\n        if (path.startsWith('/')) root = '/';\n        else\n        {\n            root = this.getProtocol(path);\n        }\n\n        if (this.isUrl(path))\n        {\n            // need to find the first path separator\n            const index = path.indexOf('/', root.length);\n\n            if (index !== -1)\n            {\n                root = path.slice(0, index);\n            }\n            else root = path;\n\n            if (!root.endsWith('/')) root += '/';\n        }\n\n        return root;\n    },\n\n    /**\n     * Returns the last portion of a path\n     * @param path - The path to test\n     * @param ext - Optional extension to remove\n     */\n    basename(path: string, ext?: string)\n    {\n        assertPath(path);\n        if (ext) assertPath(ext);\n\n        path = this.toPosix(path);\n\n        let start = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i: number;\n\n        if (ext !== undefined && ext.length > 0 && ext.length <= path.length)\n        {\n            if (ext.length === path.length && ext === path) return '';\n            let extIdx = ext.length - 1;\n            let firstNonSlashEnd = -1;\n\n            for (i = path.length - 1; i >= 0; --i)\n            {\n                const code = path.charCodeAt(i);\n\n                if (code === 47)\n                {\n                    // If we reached a path separator that was not part of a set of path\n                    // separators at the end of the string, stop now\n                    if (!matchedSlash)\n                    {\n                        start = i + 1;\n                        break;\n                    }\n                }\n                else\n                {\n                    if (firstNonSlashEnd === -1)\n                    {\n                        // We saw the first non-path separator, remember this index in case\n                        // we need it if the extension ends up not matching\n                        matchedSlash = false;\n                        firstNonSlashEnd = i + 1;\n                    }\n                    if (extIdx >= 0)\n                    {\n                        // Try to match the explicit extension\n                        if (code === ext.charCodeAt(extIdx))\n                        {\n                            if (--extIdx === -1)\n                            {\n                                // We matched the extension, so mark this as the end of our path\n                                // component\n                                end = i;\n                            }\n                        }\n                        else\n                        {\n                            // Extension does not match, so our result is the entire path\n                            // component\n                            extIdx = -1;\n                            end = firstNonSlashEnd;\n                        }\n                    }\n                }\n            }\n\n            if (start === end) end = firstNonSlashEnd; else if (end === -1) end = path.length;\n\n            return path.slice(start, end);\n        }\n        for (i = path.length - 1; i >= 0; --i)\n        {\n            if (path.charCodeAt(i) === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    start = i + 1;\n                    break;\n                }\n            }\n            else if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // path component\n                matchedSlash = false;\n                end = i + 1;\n            }\n        }\n\n        if (end === -1) return '';\n\n        return path.slice(start, end);\n    },\n\n    /**\n     * Returns the extension of the path, from the last occurrence of the . (period) character to end of string in the last\n     * portion of the path. If there is no . in the last portion of the path, or if there are no . characters other than\n     * the first character of the basename of path, an empty string is returned.\n     * @param path - The path to parse\n     */\n    extname(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        for (let i = path.length - 1; i >= 0; --i)\n        {\n            const code = path.charCodeAt(i);\n\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            return '';\n        }\n\n        return path.slice(startDot, end);\n    },\n\n    /**\n     * Parses a path into an object containing the 'root', `dir`, `base`, `ext`, and `name` properties.\n     * @param path - The path to parse\n     */\n    parse(path: string)\n    {\n        assertPath(path);\n\n        const ret = { root: '', dir: '', base: '', ext: '', name: '' };\n\n        if (path.length === 0) return ret;\n        path = this.toPosix(path);\n\n        let code = path.charCodeAt(0);\n        const isAbsolute = this.isAbsolute(path);\n        let start: number;\n        const protocol = '';\n\n        ret.root = this.rootname(path);\n\n        if (isAbsolute || this.hasProtocol(path))\n        {\n            start = 1;\n        }\n        else\n        {\n            start = 0;\n        }\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i = path.length - 1;\n\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        // Get non-dir info\n        for (; i >= start; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            if (end !== -1)\n            {\n                if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);\n                else ret.base = ret.name = path.slice(startPart, end);\n            }\n        }\n        else\n        {\n            if (startPart === 0 && isAbsolute)\n            {\n                ret.name = path.slice(1, startDot);\n                ret.base = path.slice(1, end);\n            }\n            else\n            {\n                ret.name = path.slice(startPart, startDot);\n                ret.base = path.slice(startPart, end);\n            }\n            ret.ext = path.slice(startDot, end);\n        }\n\n        ret.dir = this.dirname(path);\n        if (protocol) ret.dir = protocol + ret.dir;\n\n        return ret;\n    },\n\n    sep: '/',\n    delimiter: ':'\n} as Path;\n", "import { settings } from '@pixi/settings';\n\n/**\n * The prefix that denotes a URL is for a retina asset.\n * @static\n * @name RETINA_PREFIX\n * @memberof PIXI.settings\n * @type {RegExp}\n * @default /@([0-9\\.]+)x/\n * @example `@2x`\n */\nsettings.RETINA_PREFIX = /@([0-9\\.]+)x/;\n\n/**\n * Should the `failIfMajorPerformanceCaveat` flag be enabled as a context option used in the `isWebGLSupported` function.\n * If set to true, a WebGL renderer can fail to be created if the browser thinks there could be performance issues when\n * using WebGL.\n *\n * In PixiJS v6 this has changed from true to false by default, to allow WebGL to work in as many scenarios as possible.\n * However, some users may have a poor experience, for example, if a user has a gpu or driver version blacklisted by the\n * browser.\n *\n * If your application requires high performance rendering, you may wish to set this to false.\n * We recommend one of two options if you decide to set this flag to false:\n *\n * 1: Use the `pixi.js-legacy` package, which includes a Canvas renderer as a fallback in case high performance WebGL is\n *    not supported.\n *\n * 2: Call `isWebGLSupported` (which if found in the PIXI.utils package) in your code before attempting to create a PixiJS\n *    renderer, and show an error message to the user if the function returns false, explaining that their device & browser\n *    combination does not support high performance WebGL.\n *    This is a much better strategy than trying to create a PixiJS renderer and finding it then fails.\n * @static\n * @name FAIL_IF_MAJOR_PERFORMANCE_CAVEAT\n * @memberof PIXI.settings\n * @type {boolean}\n * @default false\n */\nsettings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT = false;\n\nexport { settings };\n", "import { settings } from '@pixi/settings';\n\nlet saidHello = false;\nconst VERSION = '$_VERSION';\n\n/**\n * Skips the hello message of renderers that are created after this is run.\n * @function skipHello\n * @memberof PIXI.utils\n */\nexport function skipHello(): void\n{\n    saidHello = true;\n}\n\n/**\n * Logs out the version and renderer information for this running instance of PIXI.\n * If you don't want to see this message you can run `PIXI.utils.skipHello()` before\n * creating your renderer. Keep in mind that doing that will forever make you a jerk face.\n * @static\n * @function sayHello\n * @memberof PIXI.utils\n * @param {string} type - The string renderer type to log.\n */\nexport function sayHello(type: string): void\n{\n    if (saidHello)\n    {\n        return;\n    }\n\n    if (settings.ADAPTER.getNavigator().userAgent.toLowerCase().indexOf('chrome') > -1)\n    {\n        const args = [\n            `\\n %c %c %c PixiJS ${VERSION} - ✰ ${type} ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \\n\\n`,\n            'background: #ff66a5; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'color: #ff66a5; background: #030307; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'background: #ffc3dc; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n        ];\n\n        globalThis.console.log(...args);\n    }\n    else if (globalThis.console)\n    {\n        globalThis.console.log(`PixiJS ${VERSION} - ${type} - http://www.pixijs.com/`);\n    }\n\n    saidHello = true;\n}\n", "import { settings } from '../settings';\n\nlet supported: boolean | undefined;\n\n/**\n * Helper for checking for WebGL support.\n * @memberof PIXI.utils\n * @function isWebGLSupported\n * @returns {boolean} Is WebGL supported.\n */\nexport function isWebGLSupported(): boolean\n{\n    if (typeof supported === 'undefined')\n    {\n        supported = (function supported(): boolean\n        {\n            const contextOptions = {\n                stencil: true,\n                failIfMajorPerformanceCaveat: settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT,\n            };\n\n            try\n            {\n                if (!settings.ADAPTER.getWebGLRenderingContext())\n                {\n                    return false;\n                }\n\n                const canvas = settings.ADAPTER.createCanvas();\n                let gl = (\n                    canvas.getContext('webgl', contextOptions)\n                    || canvas.getContext('experimental-webgl', contextOptions)\n                ) as WebGLRenderingContext;\n\n                const success = !!(gl && gl.getContextAttributes().stencil);\n\n                if (gl)\n                {\n                    const loseContext = gl.getExtension('WEBGL_lose_context');\n\n                    if (loseContext)\n                    {\n                        loseContext.loseContext();\n                    }\n                }\n\n                gl = null;\n\n                return success;\n            }\n            catch (e)\n            {\n                return false;\n            }\n        })();\n    }\n\n    return supported;\n}\n", "import { BLEND_MODES } from '@pixi/constants';\n\n/**\n * Corrects PixiJS blend, takes premultiplied alpha into account\n * @memberof PIXI.utils\n * @function mapPremultipliedBlendModes\n * @private\n * @returns {Array<number[]>} Mapped modes.\n */\nfunction mapPremultipliedBlendModes(): number[][]\n{\n    const pm = [];\n    const npm = [];\n\n    for (let i = 0; i < 32; i++)\n    {\n        pm[i] = i;\n        npm[i] = i;\n    }\n\n    pm[BLEND_MODES.NORMAL_NPM] = BLEND_MODES.NORMAL;\n    pm[BLEND_MODES.ADD_NPM] = BLEND_MODES.ADD;\n    pm[BLEND_MODES.SCREEN_NPM] = BLEND_MODES.SCREEN;\n\n    npm[BLEND_MODES.NORMAL] = BLEND_MODES.NORMAL_NPM;\n    npm[BLEND_MODES.ADD] = BLEND_MODES.ADD_NPM;\n    npm[BLEND_MODES.SCREEN] = BLEND_MODES.SCREEN_NPM;\n\n    const array: number[][] = [];\n\n    array.push(npm);\n    array.push(pm);\n\n    return array;\n}\n\n/**\n * maps premultiply flag and blendMode to adjusted blendMode\n * @memberof PIXI.utils\n * @constant premultiplyBlendMode\n * @type {Array<number[]>}\n */\nexport const premultiplyBlendMode = mapPremultipliedBlendModes();\n\n/**\n * changes blendMode according to texture format\n * @memberof PIXI.utils\n * @function correctBlendMode\n * @param {number} blendMode - supposed blend mode\n * @param {boolean} premultiplied - whether source is premultiplied\n * @returns {number} true blend mode for this texture\n */\nexport function correctBlendMode(blendMode: number, premultiplied: boolean): number\n{\n    return premultiplyBlendMode[premultiplied ? 1 : 0][blendMode];\n}\n\n/**\n * combines rgb and alpha to out array\n * @memberof PIXI.utils\n * @function premultiplyRgba\n * @param {Float32Array|number[]} rgb - input rgb\n * @param {number} alpha - alpha param\n * @param {Float32Array} [out] - output\n * @param {boolean} [premultiply=true] - do premultiply it\n * @returns {Float32Array} vec4 rgba\n */\nexport function premultiplyRgba(\n    rgb: Float32Array | number[],\n    alpha: number,\n    out?: Float32Array,\n    premultiply?: boolean\n): Float32Array\n{\n    out = out || new Float32Array(4);\n    if (premultiply || premultiply === undefined)\n    {\n        out[0] = rgb[0] * alpha;\n        out[1] = rgb[1] * alpha;\n        out[2] = rgb[2] * alpha;\n    }\n    else\n    {\n        out[0] = rgb[0];\n        out[1] = rgb[1];\n        out[2] = rgb[2];\n    }\n    out[3] = alpha;\n\n    return out;\n}\n\n/**\n * premultiplies tint\n * @memberof PIXI.utils\n * @function premultiplyTint\n * @param {number} tint - integer RGB\n * @param {number} alpha - floating point alpha (0.0-1.0)\n * @returns {number} tint multiplied by alpha\n */\nexport function premultiplyTint(tint: number, alpha: number): number\n{\n    if (alpha === 1.0)\n    {\n        return (alpha * 255 << 24) + tint;\n    }\n    if (alpha === 0.0)\n    {\n        return 0;\n    }\n    let R = ((tint >> 16) & 0xFF);\n    let G = ((tint >> 8) & 0xFF);\n    let B = (tint & 0xFF);\n\n    R = ((R * alpha) + 0.5) | 0;\n    G = ((G * alpha) + 0.5) | 0;\n    B = ((B * alpha) + 0.5) | 0;\n\n    return (alpha * 255 << 24) + (R << 16) + (G << 8) + B;\n}\n\n/**\n * converts integer tint and float alpha to vec4 form, premultiplies by default\n * @memberof PIXI.utils\n * @function premultiplyTintToRgba\n * @param {number} tint - input tint\n * @param {number} alpha - alpha param\n * @param {Float32Array} [out] - output\n * @param {boolean} [premultiply=true] - do premultiply it\n * @returns {Float32Array} vec4 rgba\n */\nexport function premultiplyTintToRgba(tint: number, alpha: number, out: Float32Array, premultiply?: boolean): Float32Array\n{\n    out = out || new Float32Array(4);\n    out[0] = ((tint >> 16) & 0xFF) / 255.0;\n    out[1] = ((tint >> 8) & 0xFF) / 255.0;\n    out[2] = (tint & 0xFF) / 255.0;\n    if (premultiply || premultiply === undefined)\n    {\n        out[0] *= alpha;\n        out[1] *= alpha;\n        out[2] *= alpha;\n    }\n    out[3] = alpha;\n\n    return out;\n}\n", "import type { ITypedArray } from '@pixi/core';\n\nexport function getBufferType(\n    array: ITypedArray\n): 'Float32Array' | 'Uint32Array' | 'Int32Array' | 'Uint16Array' | 'Uint8Array' | null\n{\n    if (array.BYTES_PER_ELEMENT === 4)\n    {\n        if (array instanceof Float32Array)\n        {\n            return 'Float32Array';\n        }\n        else if (array instanceof Uint32Array)\n        {\n            return 'Uint32Array';\n        }\n\n        return 'Int32Array';\n    }\n    else if (array.BYTES_PER_ELEMENT === 2)\n    {\n        if (array instanceof Uint16Array)\n        {\n            return 'Uint16Array';\n        }\n    }\n    else if (array.BYTES_PER_ELEMENT === 1)\n    {\n        if (array instanceof Uint8Array)\n        {\n            return 'Uint8Array';\n        }\n    }\n\n    // TODO map out the rest of the array elements!\n    return null;\n}\n", "import { getBufferType } from './getBufferType';\n\n/* eslint-disable object-shorthand */\nconst map = { Float32Array: Float32Array, Uint32Array: Uint32Array, Int32Array: Int32Array, Uint8Array: Uint8Array };\n\ntype PackedArray = Float32Array | Uint32Array | Int32Array | Uint8Array;\n\nexport function interleaveTypedArrays(arrays: PackedArray[], sizes: number[]): Float32Array\n{\n    let outSize = 0;\n    let stride = 0;\n    const views: {[key: string]: PackedArray} = {};\n\n    for (let i = 0; i < arrays.length; i++)\n    {\n        stride += sizes[i];\n        outSize += arrays[i].length;\n    }\n\n    const buffer = new ArrayBuffer(outSize * 4);\n\n    let out = null;\n    let littleOffset = 0;\n\n    for (let i = 0; i < arrays.length; i++)\n    {\n        const size = sizes[i];\n        const array = arrays[i];\n\n        /*\n        @todo This is unsafe casting but consistent with how the code worked previously. Should it stay this way\n              or should and `getBufferTypeUnsafe` function be exposed that throws an Error if unsupported type is passed?\n         */\n        const type = getBufferType(array) as keyof typeof map;\n\n        if (!views[type])\n        {\n            views[type] = new map[type](buffer);\n        }\n\n        out = views[type];\n\n        for (let j = 0; j < array.length; j++)\n        {\n            const indexStart = ((j / size | 0) * stride) + littleOffset;\n            const index = j % size;\n\n            out[indexStart + index] = array[j];\n        }\n\n        littleOffset += size;\n    }\n\n    return new Float32Array(buffer);\n}\n", "let nextUid = 0;\n\n/**\n * Gets the next unique identifier\n * @memberof PIXI.utils\n * @function uid\n * @returns {number} The next unique identifier to use.\n */\nexport function uid(): number\n{\n    return ++nextUid;\n}\n", "import type { Dict } from '../types';\n\n// A map of warning messages already fired\nconst warnings: Dict<boolean> = {};\n\n/**\n * Helper for warning developers about deprecated features & settings.\n * A stack track for warnings is given; useful for tracking-down where\n * deprecated methods/properties/classes are being used within the code.\n * @memberof PIXI.utils\n * @function deprecation\n * @param {string} version - The version where the feature became deprecated\n * @param {string} message - Message should include what is deprecated, where, and the new solution\n * @param {number} [ignoreDepth=3] - The number of steps to ignore at the top of the error stack\n *        this is mostly to ignore internal deprecation calls.\n */\nexport function deprecation(version: string, message: string, ignoreDepth = 3): void\n{\n    // Ignore duplicat\n    if (warnings[message])\n    {\n        return;\n    }\n\n    /* eslint-disable no-console */\n    let stack = new Error().stack;\n\n    // Handle IE < 10 and Safari < 6\n    if (typeof stack === 'undefined')\n    {\n        console.warn('PixiJS Deprecation Warning: ', `${message}\\nDeprecated since v${version}`);\n    }\n    else\n    {\n        // chop off the stack trace which includes PixiJS internal calls\n        stack = stack.split('\\n').splice(ignoreDepth).join('\\n');\n\n        if (console.groupCollapsed)\n        {\n            console.groupCollapsed(\n                '%cPixiJS Deprecation Warning: %c%s',\n                'color:#614108;background:#fffbe6',\n                'font-weight:normal;color:#614108;background:#fffbe6',\n                `${message}\\nDeprecated since v${version}`\n            );\n            console.warn(stack);\n            console.groupEnd();\n        }\n        else\n        {\n            console.warn('PixiJS Deprecation Warning: ', `${message}\\nDeprecated since v${version}`);\n            console.warn(stack);\n        }\n    }\n    /* eslint-enable no-console */\n\n    warnings[message] = true;\n}\n", "import type { Program, Texture, BaseTexture } from '@pixi/core';\n\n/**\n * @todo Describe property usage\n * @static\n * @name ProgramCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const ProgramCache: {[key: string]: Program} = {};\n\n/**\n * @todo Describe property usage\n * @static\n * @name TextureCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const TextureCache: {[key: string]: Texture} = Object.create(null);\n\n/**\n * @todo Describe property usage\n * @static\n * @name BaseTextureCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const BaseTextureCache: {[key: string]: BaseTexture} = Object.create(null);\n\n/**\n * Destroys all texture in the cache\n * @memberof PIXI.utils\n * @function destroyTextureCache\n */\nexport function destroyTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        TextureCache[key].destroy();\n    }\n    for (key in BaseTextureCache)\n    {\n        BaseTextureCache[key].destroy();\n    }\n}\n\n/**\n * Removes all textures from cache, but does not destroy them\n * @memberof PIXI.utils\n * @function clearTextureCache\n */\nexport function clearTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        delete TextureCache[key];\n    }\n    for (key in BaseTextureCache)\n    {\n        delete BaseTextureCache[key];\n    }\n}\n", "import { settings } from '@pixi/settings';\n\n/**\n * Creates a Canvas element of the given size to be used as a target for rendering to.\n * @class\n * @memberof PIXI.utils\n */\nexport class CanvasRenderTarget\n{\n    /** The Canvas object that belongs to this CanvasRenderTarget. */\n    public canvas: HTMLCanvasElement;\n\n    /** A CanvasRenderingContext2D object representing a two-dimensional rendering context. */\n    public context: CanvasRenderingContext2D;\n\n    /**\n     * The resolution / device pixel ratio of the canvas\n     * @default 1\n     */\n    public resolution: number;\n\n    /**\n     * @param width - the width for the newly created canvas\n     * @param height - the height for the newly created canvas\n     * @param {number} [resolution=PIXI.settings.RESOLUTION] - The resolution / device pixel ratio of the canvas\n     */\n    constructor(width: number, height: number, resolution?: number)\n    {\n        this.canvas = settings.ADAPTER.createCanvas();\n\n        this.context = this.canvas.getContext('2d');\n\n        this.resolution = resolution || settings.RESOLUTION;\n\n        this.resize(width, height);\n    }\n\n    /**\n     * Clears the canvas that was created by the CanvasRenderTarget class.\n     * @private\n     */\n    clear(): void\n    {\n        this.context.setTransform(1, 0, 0, 1, 0, 0);\n        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    }\n\n    /**\n     * Resizes the canvas to the specified width and height.\n     * @param desiredWidth - the desired width of the canvas\n     * @param desiredHeight - the desired height of the canvas\n     */\n    resize(desiredWidth: number, desiredHeight: number): void\n    {\n        this.canvas.width = Math.round(desiredWidth * this.resolution);\n        this.canvas.height = Math.round(desiredHeight * this.resolution);\n    }\n\n    /** Destroys this canvas. */\n    destroy(): void\n    {\n        this.context = null;\n        this.canvas = null;\n    }\n\n    /**\n     * The width of the canvas buffer in pixels.\n     * @member {number}\n     */\n    get width(): number\n    {\n        return this.canvas.width;\n    }\n\n    set width(val: number)\n    {\n        this.canvas.width = Math.round(val);\n    }\n\n    /**\n     * The height of the canvas buffer in pixels.\n     * @member {number}\n     */\n    get height(): number\n    {\n        return this.canvas.height;\n    }\n\n    set height(val: number)\n    {\n        this.canvas.height = Math.round(val);\n    }\n}\n", "/**\n * Regexp for data URI.\n * Based on: {@link https://github.com/ragingwind/data-uri-regex}\n * @static\n * @constant {RegExp|string} DATA_URI\n * @memberof PIXI\n * @example data:image/png;base64\n */\nexport const DATA_URI = /^\\s*data:(?:([\\w-]+)\\/([\\w+.-]+))?(?:;charset=([\\w-]+))?(?:;(base64))?,(.*)/i;\n", "import { url as _url } from '../url';\n\nlet tempAnchor: HTMLAnchorElement | undefined;\n\n/**\n * Sets the `crossOrigin` property for this resource based on if the url\n * for this resource is cross-origin. If crossOrigin was manually set, this\n * function does nothing.\n * Nipped from the resource loader!\n * @ignore\n * @param {string} url - The url to test.\n * @param {object} [loc=window.location] - The location object to test against.\n * @returns {string} The crossOrigin value to use (or empty string for none).\n */\nexport function determineCrossOrigin(url: string, loc: Location = globalThis.location): string\n{\n    // data: and javascript: urls are considered same-origin\n    if (url.indexOf('data:') === 0)\n    {\n        return '';\n    }\n\n    // default is window.location\n    loc = loc || globalThis.location;\n\n    if (!tempAnchor)\n    {\n        tempAnchor = document.createElement('a');\n    }\n\n    // let the browser determine the full href for the url of this resource and then\n    // parse with the node url lib, we can't use the properties of the anchor element\n    // because they don't work in IE9 :(\n    tempAnchor.href = url;\n    const parsedUrl = _url.parse(tempAnchor.href);\n\n    const samePort = (!parsedUrl.port && loc.port === '') || (parsedUrl.port === loc.port);\n\n    // if cross origin\n    if (parsedUrl.hostname !== loc.hostname || !samePort || parsedUrl.protocol !== loc.protocol)\n    {\n        return 'anonymous';\n    }\n\n    return '';\n}\n", "/**\n * Generic Mask Stack data structure\n * @memberof PIXI.utils\n * @function createIndicesForQuads\n * @param {number} size - Number of quads\n * @param {Uint16Array|Uint32Array} [outBuffer] - Buffer for output, length has to be `6 * size`\n * @returns {Uint16Array|Uint32Array} - Resulting index buffer\n */\nexport function createIndicesForQuads(size: number, outBuffer: Uint16Array | Uint32Array = null): Uint16Array | Uint32Array\n{\n    // the total number of indices in our array, there are 6 points per quad.\n    const totalIndices = size * 6;\n\n    outBuffer = outBuffer || new Uint16Array(totalIndices);\n\n    if (outBuffer.length !== totalIndices)\n    {\n        throw new Error(`Out buffer length is incorrect, got ${outBuffer.length} and expected ${totalIndices}`);\n    }\n\n    // fill the indices with the quads to draw\n    for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4)\n    {\n        outBuffer[i + 0] = j + 0;\n        outBuffer[i + 1] = j + 1;\n        outBuffer[i + 2] = j + 2;\n        outBuffer[i + 3] = j + 0;\n        outBuffer[i + 4] = j + 2;\n        outBuffer[i + 5] = j + 3;\n    }\n\n    return outBuffer;\n}\n", "import { DATA_URI } from '../const';\n\nexport interface DecomposedDataUri\n{\n    mediaType: string;\n    subType: string;\n    charset: string;\n    encoding: string;\n    data: string;\n}\n\n/**\n * @memberof PIXI.utils\n * @interface DecomposedDataUri\n */\n\n/**\n * type, eg. `image`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} mediaType\n */\n\n/**\n * Sub type, eg. `png`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} subType\n */\n\n/**\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} charset\n */\n\n/**\n * Data encoding, eg. `base64`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} encoding\n */\n\n/**\n * The actual data\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} data\n */\n\n/**\n * Split a data URI into components. Returns undefined if\n * parameter `dataUri` is not a valid data URI.\n * @memberof PIXI.utils\n * @function decomposeDataUri\n * @param {string} dataUri - the data URI to check\n * @returns {PIXI.utils.DecomposedDataUri|undefined} The decomposed data uri or undefined\n */\nexport function decomposeDataUri(dataUri: string): DecomposedDataUri\n{\n    const dataUriMatch = DATA_URI.exec(dataUri);\n\n    if (dataUriMatch)\n    {\n        return {\n            mediaType: dataUriMatch[1] ? dataUriMatch[1].toLowerCase() : undefined,\n            subType: dataUriMatch[2] ? dataUriMatch[2].toLowerCase() : undefined,\n            charset: dataUriMatch[3] ? dataUriMatch[3].toLowerCase() : undefined,\n            encoding: dataUriMatch[4] ? dataUriMatch[4].toLowerCase() : undefined,\n            data: dataUriMatch[5],\n        };\n    }\n\n    return undefined;\n}\n", "import { settings } from '../settings';\n\n/**\n * get the resolution / device pixel ratio of an asset by looking for the prefix\n * used by spritesheets and image urls\n * @memberof PIXI.utils\n * @function getResolutionOfUrl\n * @param {string} url - the image path\n * @param {number} [defaultValue=1] - the defaultValue if no filename prefix is set.\n * @returns {number} resolution / device pixel ratio of an asset\n */\nexport function getResolutionOfUrl(url: string, defaultValue?: number): number\n{\n    const resolution = settings.RETINA_PREFIX.exec(url);\n\n    if (resolution)\n    {\n        return parseFloat(resolution[1]);\n    }\n\n    return defaultValue !== undefined ? defaultValue : 1;\n}\n", "import { default as cssColorNames } from 'css-color-names';\n\n/**\n * Converts a hexadecimal color number to an [R, G, B] array of normalized floats (numbers from 0.0 to 1.0).\n * @example\n * PIXI.utils.hex2rgb(0xffffff); // returns [1, 1, 1]\n * @memberof PIXI.utils\n * @function hex2rgb\n * @param {number} hex - The hexadecimal number to convert\n * @param  {number[]} [out=[]] - If supplied, this array will be used rather than returning a new one\n * @returns {number[]} An array representing the [R, G, B] of the color where all values are floats.\n */\nexport function hex2rgb(hex: number, out: Array<number> | Float32Array = []): Array<number> | Float32Array\n{\n    out[0] = ((hex >> 16) & 0xFF) / 255;\n    out[1] = ((hex >> 8) & 0xFF) / 255;\n    out[2] = (hex & 0xFF) / 255;\n\n    return out;\n}\n\n/**\n * Converts a hexadecimal color number to a string.\n * @example\n * PIXI.utils.hex2string(0xffffff); // returns \"#ffffff\"\n * @memberof PIXI.utils\n * @function hex2string\n * @param {number} hex - Number in hex (e.g., `0xffffff`)\n * @returns {string} The string color (e.g., `\"#ffffff\"`).\n */\nexport function hex2string(hex: number): string\n{\n    let hexString = hex.toString(16);\n\n    hexString = '000000'.substring(0, 6 - hexString.length) + hexString;\n\n    return `#${hexString}`;\n}\n\n/**\n * Converts a string to a hexadecimal color number.\n * It can handle:\n *  hex strings starting with #: \"#ffffff\"\n *  hex strings starting with 0x: \"0xffffff\"\n *  hex strings without prefix: \"ffffff\"\n *  css colors: \"black\"\n * @example\n * PIXI.utils.string2hex(\"#ffffff\"); // returns 0xffffff, which is 16777215 as an integer\n * @memberof PIXI.utils\n * @function string2hex\n * @param {string} string - The string color (e.g., `\"#ffffff\"`)\n * @returns {number} Number in hexadecimal.\n */\nexport function string2hex(string: string): number\n{\n    if (typeof string === 'string')\n    {\n        string = (cssColorNames as {[key: string]: string})[string.toLowerCase()] || string;\n\n        if (string[0] === '#')\n        {\n            string = string.slice(1);\n        }\n    }\n\n    return parseInt(string, 16);\n}\n\n/**\n * Converts a color as an [R, G, B] array of normalized floats to a hexadecimal number.\n * @example\n * PIXI.utils.rgb2hex([1, 1, 1]); // returns 0xffffff, which is 16777215 as an integer\n * @memberof PIXI.utils\n * @function rgb2hex\n * @param {number[]} rgb - Array of numbers where all values are normalized floats from 0.0 to 1.0.\n * @returns {number} Number in hexadecimal.\n */\nexport function rgb2hex(rgb: number[] | Float32Array): number\n{\n    return (((rgb[0] * 255) << 16) + ((rgb[1] * 255) << 8) + (rgb[2] * 255 | 0));\n}\n", "// Taken from the bit-twiddle package\n\n/**\n * Rounds to next power of two.\n * @function nextPow2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {number} - next rounded power of two\n */\nexport function nextPow2(v: number): number\n{\n    v += v === 0 ? 1 : 0;\n    --v;\n    v |= v >>> 1;\n    v |= v >>> 2;\n    v |= v >>> 4;\n    v |= v >>> 8;\n    v |= v >>> 16;\n\n    return v + 1;\n}\n\n/**\n * Checks if a number is a power of two.\n * @function isPow2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {boolean} `true` if value is power of two\n */\nexport function isPow2(v: number): boolean\n{\n    return !(v & (v - 1)) && (!!v);\n}\n\n/**\n * Computes ceil of log base 2\n * @function log2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {number} logarithm base 2\n */\nexport function log2(v: number): number\n{\n    let r = (v > 0xFFFF ? 1 : 0) << 4;\n\n    v >>>= r;\n\n    let shift = (v > 0xFF ? 1 : 0) << 3;\n\n    v >>>= shift; r |= shift;\n    shift = (v > 0xF ? 1 : 0) << 2;\n    v >>>= shift; r |= shift;\n    shift = (v > 0x3 ? 1 : 0) << 1;\n    v >>>= shift; r |= shift;\n\n    return r | (v >> 1);\n}\n", "/**\n * Remove items from a javascript array without generating garbage\n * @function removeItems\n * @memberof PIXI.utils\n * @param {Array<any>} arr - Array to remove elements from\n * @param {number} startIdx - starting index\n * @param {number} removeCount - how many to remove\n */\nexport function removeItems(arr: any[], startIdx: number, removeCount: number): void\n{\n    const length = arr.length;\n    let i;\n\n    if (startIdx >= length || removeCount === 0)\n    {\n        return;\n    }\n\n    removeCount = (startIdx + removeCount > length ? length - startIdx : removeCount);\n\n    const len = length - removeCount;\n\n    for (i = startIdx; i < len; ++i)\n    {\n        arr[i] = arr[i + removeCount];\n    }\n\n    arr.length = len;\n}\n", "/**\n * Returns sign of number\n * @memberof PIXI.utils\n * @function sign\n * @param {number} n - the number to check the sign of\n * @returns {number} 0 if `n` is 0, -1 if `n` is negative, 1 if `n` is positive\n */\nexport function sign(n: number): -1 | 0 | 1\n{\n    if (n === 0) return 0;\n\n    return n < 0 ? -1 : 1;\n}\n", "interface Inset\n{\n    top?: number;\n    left?: number;\n    right?: number;\n    bottom?: number;\n}\n\n/**\n * Trim transparent borders from a canvas\n * @memberof PIXI.utils\n * @function trimCanvas\n * @param {HTMLCanvasElement} canvas - the canvas to trim\n * @returns {object} Trim data\n */\nexport function trimCanvas(canvas: HTMLCanvasElement): {width: number; height: number; data?: ImageData}\n{\n    // https://gist.github.com/remy/784508\n\n    let width = canvas.width;\n    let height = canvas.height;\n\n    const context = canvas.getContext('2d', {\n        willReadFrequently: true,\n    } as CanvasRenderingContext2DSettings);\n    const imageData = context.getImageData(0, 0, width, height);\n    const pixels = imageData.data;\n    const len = pixels.length;\n\n    const bound: Inset = {\n        top: null,\n        left: null,\n        right: null,\n        bottom: null,\n    };\n    let data = null;\n    let i;\n    let x;\n    let y;\n\n    for (i = 0; i < len; i += 4)\n    {\n        if (pixels[i + 3] !== 0)\n        {\n            x = (i / 4) % width;\n            y = ~~((i / 4) / width);\n\n            if (bound.top === null)\n            {\n                bound.top = y;\n            }\n\n            if (bound.left === null)\n            {\n                bound.left = x;\n            }\n            else if (x < bound.left)\n            {\n                bound.left = x;\n            }\n\n            if (bound.right === null)\n            {\n                bound.right = x + 1;\n            }\n            else if (bound.right < x)\n            {\n                bound.right = x + 1;\n            }\n\n            if (bound.bottom === null)\n            {\n                bound.bottom = y;\n            }\n            else if (bound.bottom < y)\n            {\n                bound.bottom = y;\n            }\n        }\n    }\n\n    if (bound.top !== null)\n    {\n        width = bound.right - bound.left;\n        height = bound.bottom - bound.top + 1;\n        data = context.getImageData(bound.left, bound.top, width, height);\n    }\n\n    return {\n        height,\n        width,\n        data,\n    };\n}\n"], "names": ["has", "Object", "prototype", "hasOwnProperty", "prefix", "Events", "EE", "fn", "context", "once", "this", "addListener", "emitter", "event", "TypeError", "listener", "evt", "_events", "push", "_eventsCount", "clearEvent", "EventEmitter", "create", "__proto__", "eventNames", "events", "name", "names", "call", "slice", "getOwnPropertySymbols", "concat", "listeners", "handlers", "i", "l", "length", "ee", "Array", "listenerCount", "emit", "a1", "a2", "a3", "a4", "a5", "args", "len", "arguments", "removeListener", "undefined", "apply", "j", "on", "removeAllListeners", "off", "prefixed", "module", "exports", "earcut_1", "earcut", "_default", "data", "holeIndices", "dim", "minX", "minY", "maxX", "maxY", "x", "y", "invSize", "hasHoles", "outerLen", "outerNode", "linkedList", "triangles", "next", "prev", "list", "queue", "steiner", "getLeftmost", "sort", "compareX", "eliminateHole", "eliminateHoles", "Math", "max", "earcutLinked", "start", "end", "clockwise", "last", "signedArea", "insertNode", "equals", "removeNode", "filterPoints", "again", "p", "area", "ear", "pass", "z", "zOrder", "prevZ", "nextZ", "q", "e", "tail", "numMerges", "pSize", "qSize", "inSize", "sortLinked", "indexCurve", "stop", "isEarHashed", "isEar", "cureLocalIntersections", "splitEarcut", "a", "b", "c", "ax", "bx", "cx", "ay", "by", "cy", "x0", "y0", "x1", "y1", "pointInTriangle", "minZ", "maxZ", "n", "intersects", "locallyInside", "isValidDiagonal", "splitPolygon", "hole", "bridge", "m", "hx", "hy", "qx", "Infinity", "tan", "mx", "my", "tanMin", "abs", "sectorContainsSector", "findHoleBridge", "bridgeReverse", "leftmost", "px", "py", "intersectsPolygon", "inside", "middleInside", "r", "p1", "p2", "q1", "q2", "o1", "sign", "o2", "o3", "o4", "onSegment", "min", "num", "Node", "b2", "an", "bp", "sum", "deviation", "polygonArea", "trianglesArea", "flatten", "result", "vertices", "holes", "dimensions", "holeIndex", "d", "root", "freeExports", "nodeType", "freeModule", "freeGlobal", "global", "window", "self", "punycode", "key", "maxInt", "base", "regexPunycode", "regexNonASCII", "regexSeparators", "errors", "overflow", "floor", "stringFromCharCode", "String", "fromCharCode", "error", "type", "RangeError", "map", "array", "mapDomain", "string", "parts", "split", "replace", "join", "ucs2decode", "value", "extra", "output", "counter", "charCodeAt", "ucs2encode", "digitToBasic", "digit", "flag", "adapt", "delta", "numPoints", "firstTime", "k", "baseMinusTMin", "decode", "input", "out", "basic", "index", "oldi", "w", "t", "baseMinusT", "codePoint", "inputLength", "bias", "lastIndexOf", "splice", "encode", "handledCPCount", "basicLength", "currentValue", "handledCPCountPlusOne", "qMinusT", "version", "ucs2", "toASCII", "test", "toUnicode", "toLowerCase", "util", "arg", "obj", "prop", "qs", "sep", "eq", "options", "regexp", "max<PERSON>eys", "kstr", "vstr", "v", "idx", "indexOf", "substr", "decodeURIComponent", "isArray", "stringifyPrimitive", "isFinite", "keys", "ks", "encodeURIComponent", "parse", "require$$0", "stringify", "require$$1", "urlParse", "resolve", "source", "relative", "format", "Url", "protocol", "slashes", "auth", "host", "port", "hostname", "hash", "search", "query", "pathname", "path", "href", "protocolPattern", "portPattern", "simplePathPattern", "unwise", "autoEscape", "nonHostChars", "hostEndingChars", "hostnamePartPattern", "hostnamePartStart", "unsafeProtocol", "javascript", "hostlessProtocol", "slashedProtocol", "http", "https", "ftp", "gopher", "file", "url", "parseQueryString", "slashesDenoteHost", "u", "queryIndex", "splitter", "uSplit", "rest", "trim", "simplePath", "exec", "querystring", "proto", "lowerProto", "match", "atSign", "hostEnd", "hec", "parseHost", "ipv6Hostname", "hostparts", "part", "newpart", "validParts", "notHost", "bit", "unshift", "h", "ae", "esc", "escape", "qm", "s", "char<PERSON>t", "resolveObject", "rel", "tkeys", "tk", "tkey", "rkeys", "rk", "rkey", "re<PERSON><PERSON><PERSON>", "shift", "isSourceAbs", "isRelAbs", "mustEndAbs", "removeAllDots", "srcPath", "psychotic", "pop", "authInHost", "hasTrailingSlash", "up", "isAbsolute", "_parse", "_format", "_resolve", "assertPath", "JSON", "removeUrlParams", "toPosix", "find", "RegExp", "isUrl", "isDataUrl", "hasProtocol", "getProtocol", "isFile", "isHttp", "isWindows", "arr", "toAbsolute", "customBaseUrl", "customRootUrl", "baseUrl", "settings", "ADAPTER", "getBaseUrl", "rootUrl", "rootname", "startsWith", "normalize", "trailingSeparator", "endsWith", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "lastSlashIndex", "normalizeStringPosix", "joined", "segments", "_i", "prevArg", "_a", "extname", "dirname", "hasRoot", "matchedSlash", "origpath", "basename", "ext", "extIdx", "firstNonSlashEnd", "startDot", "startPart", "preDotState", "ret", "dir", "delimiter", "RETINA_PREFIX", "FAIL_IF_MAJOR_PERFORMANCE_CAVEAT", "supported", "<PERSON><PERSON><PERSON>", "premultiplyBlendMode", "pm", "npm", "BLEND_MODES", "NORMAL_NPM", "NORMAL", "ADD_NPM", "ADD", "SCREEN_NPM", "SCREEN", "mapPremultipliedBlendModes", "getBufferType", "BYTES_PER_ELEMENT", "Float32Array", "Uint32Array", "Uint16Array", "Uint8Array", "Int32Array", "nextUid", "warnings", "TextureCache", "BaseTextureCache", "CanvasRenderTarget", "width", "height", "resolution", "canvas", "createCanvas", "getContext", "RESOLUTION", "resize", "clear", "setTransform", "clearRect", "desiredWidth", "desiredHeight", "round", "destroy", "defineProperty", "get", "set", "val", "tempAnchor", "DATA_URI", "blendMode", "premultiplied", "size", "outBuffer", "totalIndices", "Error", "dataUri", "dataUriMatch", "mediaType", "subType", "charset", "encoding", "message", "<PERSON><PERSON><PERSON><PERSON>", "stack", "console", "warn", "groupCollapsed", "groupEnd", "loc", "globalThis", "location", "document", "createElement", "parsedUrl", "_url", "samePort", "defaultValue", "parseFloat", "hex", "hexString", "toString", "substring", "arrays", "sizes", "outSize", "stride", "views", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littleOffset", "contextOptions", "stencil", "failIfMajorPerformanceCaveat", "getWebGLRenderingContext", "gl", "success", "getContextAttributes", "loseContext", "getExtension", "rgb", "alpha", "premultiply", "tint", "R", "G", "B", "startIdx", "removeCount", "getNavigator", "userAgent", "log", "cssColorNames", "parseInt", "willReadFrequently", "pixels", "getImageData", "bound", "top", "left", "right", "bottom"], "mappings": ";;;;;;;+eAEA,IAAIA,EAAMC,OAAOC,UAAUC,eACvBC,EAAS,IASb,SAASC,KA4BT,SAASC,EAAGC,EAAIC,EAASC,GACvBC,KAAKH,GAAKA,EACVG,KAAKF,QAAUA,EACfE,KAAKD,KAAOA,IAAQ,EActB,SAASE,EAAYC,EAASC,EAAON,EAAIC,EAASC,GAChD,GAAkB,mBAAPF,EACT,MAAM,IAAIO,UAAU,mCAGtB,IAAIC,EAAW,IAAIT,EAAGC,EAAIC,GAAWI,EAASH,GAC1CO,EAAMZ,EAASA,EAASS,EAAQA,EAMpC,OAJKD,EAAQK,QAAQD,GACXJ,EAAQK,QAAQD,GAAKT,GAC1BK,EAAQK,QAAQD,GAAO,CAACJ,EAAQK,QAAQD,GAAMD,GADhBH,EAAQK,QAAQD,GAAKE,KAAKH,IADlCH,EAAQK,QAAQD,GAAOD,EAAUH,EAAQO,gBAI7DP,EAUT,SAASQ,EAAWR,EAASI,GACI,KAAzBJ,EAAQO,aAAoBP,EAAQK,QAAU,IAAIZ,SAC5CO,EAAQK,QAAQD,GAU9B,SAASK,IACPX,KAAKO,QAAU,IAAIZ,EACnBK,KAAKS,aAAe,EAxElBlB,OAAOqB,SACTjB,EAAOH,UAAYD,OAAOqB,OAAO,OAM5B,IAAIjB,GAASkB,YAAWnB,GAAS,IA2ExCiB,EAAanB,UAAUsB,WAAa,WAClC,IACIC,EACAC,EAFAC,EAAQ,GAIZ,GAA0B,IAAtBjB,KAAKS,aAAoB,OAAOQ,EAEpC,IAAKD,KAASD,EAASf,KAAKO,QACtBjB,EAAI4B,KAAKH,EAAQC,IAAOC,EAAMT,KAAKd,EAASsB,EAAKG,MAAM,GAAKH,GAGlE,OAAIzB,OAAO6B,sBACFH,EAAMI,OAAO9B,OAAO6B,sBAAsBL,IAG5CE,GAUTN,EAAanB,UAAU8B,UAAY,SAAmBnB,GACpD,IAAIG,EAAMZ,EAASA,EAASS,EAAQA,EAChCoB,EAAWvB,KAAKO,QAAQD,GAE5B,IAAKiB,EAAU,MAAO,GACtB,GAAIA,EAAS1B,GAAI,MAAO,CAAC0B,EAAS1B,IAElC,IAAK,IAAI2B,EAAI,EAAGC,EAAIF,EAASG,OAAQC,EAAK,IAAIC,MAAMH,GAAID,EAAIC,EAAGD,IAC7DG,EAAGH,GAAKD,EAASC,GAAG3B,GAGtB,OAAO8B,GAUThB,EAAanB,UAAUqC,cAAgB,SAAuB1B,GAC5D,IAAIG,EAAMZ,EAASA,EAASS,EAAQA,EAChCmB,EAAYtB,KAAKO,QAAQD,GAE7B,OAAKgB,EACDA,EAAUzB,GAAW,EAClByB,EAAUI,OAFM,GAYzBf,EAAanB,UAAUsC,KAAO,SAAc3B,EAAO4B,EAAIC,EAAIC,EAAIC,EAAIC,mBAC7D7B,EAAMZ,EAASA,EAASS,EAAQA,EAEpC,IAAKH,KAAKO,QAAQD,GAAM,OAAO,EAE/B,IAEI8B,EACAZ,EAHAF,EAAYtB,KAAKO,QAAQD,GACzB+B,EAAMC,UAAUZ,OAIpB,GAAIJ,EAAUzB,GAAI,CAGhB,OAFIyB,EAAUvB,MAAMC,KAAKuC,eAAepC,EAAOmB,EAAUzB,QAAI2C,GAAW,GAEhEH,GACN,KAAK,EAAG,OAAOf,EAAUzB,GAAGqB,KAAKI,EAAUxB,UAAU,EACrD,KAAK,EAAG,OAAOwB,EAAUzB,GAAGqB,KAAKI,EAAUxB,QAASiC,IAAK,EACzD,KAAK,EAAG,OAAOT,EAAUzB,GAAGqB,KAAKI,EAAUxB,QAASiC,EAAIC,IAAK,EAC7D,KAAK,EAAG,OAAOV,EAAUzB,GAAGqB,KAAKI,EAAUxB,QAASiC,EAAIC,EAAIC,IAAK,EACjE,KAAK,EAAG,OAAOX,EAAUzB,GAAGqB,KAAKI,EAAUxB,QAASiC,EAAIC,EAAIC,EAAIC,IAAK,EACrE,KAAK,EAAG,OAAOZ,EAAUzB,GAAGqB,KAAKI,EAAUxB,QAASiC,EAAIC,EAAIC,EAAIC,EAAIC,IAAK,EAG3E,IAAKX,EAAI,EAAGY,EAAO,IAAIR,MAAMS,EAAK,GAAIb,EAAIa,EAAKb,IAC7CY,EAAKZ,EAAI,GAAKc,EAAUd,GAG1BF,EAAUzB,GAAG4C,MAAMnB,EAAUxB,QAASsC,OACjC,CACL,IACIM,EADAhB,EAASJ,EAAUI,OAGvB,IAAKF,EAAI,EAAGA,EAAIE,EAAQF,IAGtB,OAFIF,EAAUE,GAAGzB,MAAMC,KAAKuC,eAAepC,EAAOmB,EAAUE,GAAG3B,QAAI2C,GAAW,GAEtEH,GACN,KAAK,EAAGf,EAAUE,GAAG3B,GAAGqB,KAAKI,EAAUE,GAAG1B,SAAU,MACpD,KAAK,EAAGwB,EAAUE,GAAG3B,GAAGqB,KAAKI,EAAUE,GAAG1B,QAASiC,GAAK,MACxD,KAAK,EAAGT,EAAUE,GAAG3B,GAAGqB,KAAKI,EAAUE,GAAG1B,QAASiC,EAAIC,GAAK,MAC5D,KAAK,EAAGV,EAAUE,GAAG3B,GAAGqB,KAAKI,EAAUE,GAAG1B,QAASiC,EAAIC,EAAIC,GAAK,MAChE,QACE,IAAKG,EAAM,IAAKM,EAAI,EAAGN,EAAO,IAAIR,MAAMS,EAAK,GAAIK,EAAIL,EAAKK,IACxDN,EAAKM,EAAI,GAAKJ,EAAUI,GAG1BpB,EAAUE,GAAG3B,GAAG4C,MAAMnB,EAAUE,GAAG1B,QAASsC,IAKpD,OAAO,GAYTzB,EAAanB,UAAUmD,GAAK,SAAYxC,EAAON,EAAIC,GACjD,OAAOG,EAAYD,KAAMG,EAAON,EAAIC,GAAS,IAY/Ca,EAAanB,UAAUO,KAAO,SAAcI,EAAON,EAAIC,GACrD,OAAOG,EAAYD,KAAMG,EAAON,EAAIC,GAAS,IAa/Ca,EAAanB,UAAU+C,eAAiB,SAAwBpC,EAAON,EAAIC,EAASC,GAClF,IAAIO,EAAMZ,EAASA,EAASS,EAAQA,EAEpC,IAAKH,KAAKO,QAAQD,GAAM,OAAON,KAC/B,IAAKH,EAEH,OADAa,EAAWV,KAAMM,GACVN,KAGT,IAAIsB,EAAYtB,KAAKO,QAAQD,GAE7B,GAAIgB,EAAUzB,GAEVyB,EAAUzB,KAAOA,GACfE,IAAQuB,EAAUvB,MAClBD,GAAWwB,EAAUxB,UAAYA,GAEnCY,EAAWV,KAAMM,OAEd,CACL,IAAK,IAAIkB,EAAI,EAAGT,EAAS,GAAIW,EAASJ,EAAUI,OAAQF,EAAIE,EAAQF,KAEhEF,EAAUE,GAAG3B,KAAOA,GACnBE,IAASuB,EAAUE,GAAGzB,MACtBD,GAAWwB,EAAUE,GAAG1B,UAAYA,IAErCiB,EAAOP,KAAKc,EAAUE,IAOtBT,EAAOW,OAAQ1B,KAAKO,QAAQD,GAAyB,IAAlBS,EAAOW,OAAeX,EAAO,GAAKA,EACpEL,EAAWV,KAAMM,GAGxB,OAAON,MAUTW,EAAanB,UAAUoD,mBAAqB,SAA4BzC,GACtE,IAAIG,EAUJ,OARIH,GACFG,EAAMZ,EAASA,EAASS,EAAQA,EAC5BH,KAAKO,QAAQD,IAAMI,EAAWV,KAAMM,KAExCN,KAAKO,QAAU,IAAIZ,EACnBK,KAAKS,aAAe,GAGfT,MAMTW,EAAanB,UAAUqD,IAAMlC,EAAanB,UAAU+C,eACpD5B,EAAanB,UAAUS,YAAcU,EAAanB,UAAUmD,GAK5DhC,EAAamC,SAAWpD,EAKxBiB,EAAaA,aAAeA,EAM1BoC,EAAAC,QAAiBrC,KC5ULsC,EAAGC,EACKC,EAAGD,EAEzB,SAASA,EAAOE,EAAMC,EAAaC,GAE/BA,EAAMA,GAAO,EAEb,IAOIC,EAAMC,EAAMC,EAAMC,EAAMC,EAAGC,EAAGC,EAP9BC,EAAWT,GAAeA,EAAY3B,OACtCqC,EAAWD,EAAWT,EAAY,GAAKC,EAAMF,EAAK1B,OAClDsC,EAAYC,EAAWb,EAAM,EAAGW,EAAUT,GAAK,GAC/CY,EAAY,GAEhB,IAAKF,GAAaA,EAAUG,OAASH,EAAUI,KAAM,OAAOF,EAO5D,GAHIJ,IAAUE,EA2PlB,SAAwBZ,EAAMC,EAAaW,EAAWV,GAClD,IACI9B,EAAGa,EAAiBgC,EADpBC,EAAQ,GAGZ,IAAK9C,EAAI,EAAGa,EAAMgB,EAAY3B,OAAQF,EAAIa,EAAKb,KAG3C6C,EAAOJ,EAAWb,EAFVC,EAAY7B,GAAK8B,EACnB9B,EAAIa,EAAM,EAAIgB,EAAY7B,EAAI,GAAK8B,EAAMF,EAAK1B,OAChB4B,GAAK,MAC5Be,EAAKF,OAAME,EAAKE,SAAU,GACvCD,EAAM9D,KAAKgE,EAAYH,IAM3B,IAHAC,EAAMG,KAAKC,GAGNlD,EAAI,EAAGA,EAAI8C,EAAM5C,OAAQF,IAC1BwC,EAAYW,EAAcL,EAAM9C,GAAIwC,GAGxC,OAAOA,EA9QmBY,CAAexB,EAAMC,EAAaW,EAAWV,IAGnEF,EAAK1B,OAAS,GAAK4B,EAAK,CACxBC,EAAOE,EAAOL,EAAK,GACnBI,EAAOE,EAAON,EAAK,GAEnB,IAAK,IAAI5B,EAAI8B,EAAK9B,EAAIuC,EAAUvC,GAAK8B,GACjCK,EAAIP,EAAK5B,IAED+B,IAAMA,EAAOI,IADrBC,EAAIR,EAAK5B,EAAI,IAELgC,IAAMA,EAAOI,GACjBD,EAAIF,IAAMA,EAAOE,GACjBC,EAAIF,IAAMA,EAAOE,GAKzBC,EAAsB,KADtBA,EAAUgB,KAAKC,IAAIrB,EAAOF,EAAMG,EAAOF,IACb,MAAQK,EAAU,EAKhD,OAFAkB,EAAaf,EAAWE,EAAWZ,EAAKC,EAAMC,EAAMK,EAAS,GAEtDK,EAIX,SAASD,EAAWb,EAAM4B,EAAOC,EAAK3B,EAAK4B,GACvC,IAAI1D,EAAG2D,EAEP,GAAID,IAAeE,EAAWhC,EAAM4B,EAAOC,EAAK3B,GAAO,EACnD,IAAK9B,EAAIwD,EAAOxD,EAAIyD,EAAKzD,GAAK8B,EAAK6B,EAAOE,EAAW7D,EAAG4B,EAAK5B,GAAI4B,EAAK5B,EAAI,GAAI2D,QAE9E,IAAK3D,EAAIyD,EAAM3B,EAAK9B,GAAKwD,EAAOxD,GAAK8B,EAAK6B,EAAOE,EAAW7D,EAAG4B,EAAK5B,GAAI4B,EAAK5B,EAAI,GAAI2D,GAQzF,OALIA,GAAQG,EAAOH,EAAMA,EAAKhB,QAC1BoB,EAAWJ,GACXA,EAAOA,EAAKhB,MAGTgB,EAIX,SAASK,EAAaR,EAAOC,GACzB,IAAKD,EAAO,OAAOA,EACdC,IAAKA,EAAMD,GAEhB,IACIS,EADAC,EAAIV,EAER,GAGI,GAFAS,GAAQ,EAEHC,EAAEnB,UAAYe,EAAOI,EAAGA,EAAEvB,OAAqC,IAA5BwB,EAAKD,EAAEtB,KAAMsB,EAAGA,EAAEvB,MAOtDuB,EAAIA,EAAEvB,SAP8D,CAGpE,GAFAoB,EAAWG,IACXA,EAAIT,EAAMS,EAAEtB,QACFsB,EAAEvB,KAAM,MAClBsB,GAAQ,SAKPA,GAASC,IAAMT,GAExB,OAAOA,EAIX,SAASF,EAAaa,EAAK1B,EAAWZ,EAAKC,EAAMC,EAAMK,EAASgC,GAC5D,GAAKD,EAAL,EAGKC,GAAQhC,GAuRjB,SAAoBmB,EAAOzB,EAAMC,EAAMK,GACnC,IAAI6B,EAAIV,EACR,GACgB,IAARU,EAAEI,IAASJ,EAAEI,EAAIC,EAAOL,EAAE/B,EAAG+B,EAAE9B,EAAGL,EAAMC,EAAMK,IAClD6B,EAAEM,MAAQN,EAAEtB,KACZsB,EAAEO,MAAQP,EAAEvB,KACZuB,EAAIA,EAAEvB,WACDuB,IAAMV,GAEfU,EAAEM,MAAMC,MAAQ,KAChBP,EAAEM,MAAQ,KAOd,SAAoB3B,GAChB,IAAI7C,EAAGkE,EAAGQ,EAAGC,EAAGC,EAAMC,EAAWC,EAAOC,EACpCC,EAAS,EAEb,EAAG,CAMC,IALAd,EAAIrB,EACJA,EAAO,KACP+B,EAAO,KACPC,EAAY,EAELX,GAAG,CAIN,IAHAW,IACAH,EAAIR,EACJY,EAAQ,EACH9E,EAAI,EAAGA,EAAIgF,IACZF,IACAJ,EAAIA,EAAED,OAFczE,KAOxB,IAFA+E,EAAQC,EAEDF,EAAQ,GAAMC,EAAQ,GAAKL,GAEhB,IAAVI,IAA0B,IAAVC,IAAgBL,GAAKR,EAAEI,GAAKI,EAAEJ,IAC9CK,EAAIT,EACJA,EAAIA,EAAEO,MACNK,MAEAH,EAAID,EACJA,EAAIA,EAAED,MACNM,KAGAH,EAAMA,EAAKH,MAAQE,EAClB9B,EAAO8B,EAEZA,EAAEH,MAAQI,EACVA,EAAOD,EAGXT,EAAIQ,EAGRE,EAAKH,MAAQ,KACbO,GAAU,QAELH,EAAY,GAnDrBI,CAAWf,GAnSWgB,CAAWd,EAAKrC,EAAMC,EAAMK,GAMlD,IAJA,IACIO,EAAMD,EADNwC,EAAOf,EAIJA,EAAIxB,OAASwB,EAAIzB,MAIpB,GAHAC,EAAOwB,EAAIxB,KACXD,EAAOyB,EAAIzB,KAEPN,EAAU+C,EAAYhB,EAAKrC,EAAMC,EAAMK,GAAWgD,EAAMjB,GAExD1B,EAAU1D,KAAK4D,EAAK5C,EAAI8B,EAAM,GAC9BY,EAAU1D,KAAKoF,EAAIpE,EAAI8B,EAAM,GAC7BY,EAAU1D,KAAK2D,EAAK3C,EAAI8B,EAAM,GAE9BiC,EAAWK,GAGXA,EAAMzB,EAAKA,KACXwC,EAAOxC,EAAKA,UAQhB,IAHAyB,EAAMzB,KAGMwC,EAAM,CAETd,EAIe,IAATA,EAEPd,EADAa,EAAMkB,EAAuBtB,EAAaI,GAAM1B,EAAWZ,GACzCY,EAAWZ,EAAKC,EAAMC,EAAMK,EAAS,GAGvC,IAATgC,GACPkB,EAAYnB,EAAK1B,EAAWZ,EAAKC,EAAMC,EAAMK,GAT7CkB,EAAaS,EAAaI,GAAM1B,EAAWZ,EAAKC,EAAMC,EAAMK,EAAS,GAYzE,QAMZ,SAASgD,EAAMjB,GACX,IAAIoB,EAAIpB,EAAIxB,KACR6C,EAAIrB,EACJsB,EAAItB,EAAIzB,KAEZ,GAAIwB,EAAKqB,EAAGC,EAAGC,IAAM,EAAG,OAAO,EAY/B,IATA,IAAIC,EAAKH,EAAErD,EAAGyD,EAAKH,EAAEtD,EAAG0D,EAAKH,EAAEvD,EAAG2D,EAAKN,EAAEpD,EAAG2D,EAAKN,EAAErD,EAAG4D,EAAKN,EAAEtD,EAGzD6D,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDK,EAAKJ,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDG,EAAKR,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDO,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EAErD9B,EAAIwB,EAAE/C,KACHuB,IAAMsB,GAAG,CACZ,GAAItB,EAAE/B,GAAK8D,GAAM/B,EAAE/B,GAAKgE,GAAMjC,EAAE9B,GAAK8D,GAAMhC,EAAE9B,GAAKgE,GAC9CC,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI9B,EAAE/B,EAAG+B,EAAE9B,IAC/C+B,EAAKD,EAAEtB,KAAMsB,EAAGA,EAAEvB,OAAS,EAAG,OAAO,EACzCuB,EAAIA,EAAEvB,KAGV,OAAO,EAGX,SAASyC,EAAYhB,EAAKrC,EAAMC,EAAMK,GAClC,IAAImD,EAAIpB,EAAIxB,KACR6C,EAAIrB,EACJsB,EAAItB,EAAIzB,KAEZ,GAAIwB,EAAKqB,EAAGC,EAAGC,IAAM,EAAG,OAAO,EAkB/B,IAhBA,IAAIC,EAAKH,EAAErD,EAAGyD,EAAKH,EAAEtD,EAAG0D,EAAKH,EAAEvD,EAAG2D,EAAKN,EAAEpD,EAAG2D,EAAKN,EAAErD,EAAG4D,EAAKN,EAAEtD,EAGzD6D,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDK,EAAKJ,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDG,EAAKR,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDO,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EAGrDM,EAAO/B,EAAO0B,EAAIC,EAAInE,EAAMC,EAAMK,GAClCkE,EAAOhC,EAAO4B,EAAIC,EAAIrE,EAAMC,EAAMK,GAElC6B,EAAIE,EAAII,MACRgC,EAAIpC,EAAIK,MAGLP,GAAKA,EAAEI,GAAKgC,GAAQE,GAAKA,EAAElC,GAAKiC,GAAM,CACzC,GAAIrC,EAAE/B,GAAK8D,GAAM/B,EAAE/B,GAAKgE,GAAMjC,EAAE9B,GAAK8D,GAAMhC,EAAE9B,GAAKgE,GAAMlC,IAAMsB,GAAKtB,IAAMwB,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI9B,EAAE/B,EAAG+B,EAAE9B,IAAM+B,EAAKD,EAAEtB,KAAMsB,EAAGA,EAAEvB,OAAS,EAAG,OAAO,EAG9F,GAFAuB,EAAIA,EAAEM,MAEFgC,EAAErE,GAAK8D,GAAMO,EAAErE,GAAKgE,GAAMK,EAAEpE,GAAK8D,GAAMM,EAAEpE,GAAKgE,GAAMI,IAAMhB,GAAKgB,IAAMd,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAIQ,EAAErE,EAAGqE,EAAEpE,IAAM+B,EAAKqC,EAAE5D,KAAM4D,EAAGA,EAAE7D,OAAS,EAAG,OAAO,EAC9F6D,EAAIA,EAAE/B,MAIV,KAAOP,GAAKA,EAAEI,GAAKgC,GAAM,CACrB,GAAIpC,EAAE/B,GAAK8D,GAAM/B,EAAE/B,GAAKgE,GAAMjC,EAAE9B,GAAK8D,GAAMhC,EAAE9B,GAAKgE,GAAMlC,IAAMsB,GAAKtB,IAAMwB,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI9B,EAAE/B,EAAG+B,EAAE9B,IAAM+B,EAAKD,EAAEtB,KAAMsB,EAAGA,EAAEvB,OAAS,EAAG,OAAO,EAC9FuB,EAAIA,EAAEM,MAIV,KAAOgC,GAAKA,EAAElC,GAAKiC,GAAM,CACrB,GAAIC,EAAErE,GAAK8D,GAAMO,EAAErE,GAAKgE,GAAMK,EAAEpE,GAAK8D,GAAMM,EAAEpE,GAAKgE,GAAMI,IAAMhB,GAAKgB,IAAMd,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAIQ,EAAErE,EAAGqE,EAAEpE,IAAM+B,EAAKqC,EAAE5D,KAAM4D,EAAGA,EAAE7D,OAAS,EAAG,OAAO,EAC9F6D,EAAIA,EAAE/B,MAGV,OAAO,EAIX,SAASa,EAAuB9B,EAAOd,EAAWZ,GAC9C,IAAIoC,EAAIV,EACR,EAAG,CACC,IAAIgC,EAAItB,EAAEtB,KACN6C,EAAIvB,EAAEvB,KAAKA,MAEVmB,EAAO0B,EAAGC,IAAMgB,EAAWjB,EAAGtB,EAAGA,EAAEvB,KAAM8C,IAAMiB,EAAclB,EAAGC,IAAMiB,EAAcjB,EAAGD,KAExF9C,EAAU1D,KAAKwG,EAAExF,EAAI8B,EAAM,GAC3BY,EAAU1D,KAAKkF,EAAElE,EAAI8B,EAAM,GAC3BY,EAAU1D,KAAKyG,EAAEzF,EAAI8B,EAAM,GAG3BiC,EAAWG,GACXH,EAAWG,EAAEvB,MAEbuB,EAAIV,EAAQiC,GAEhBvB,EAAIA,EAAEvB,WACDuB,IAAMV,GAEf,OAAOQ,EAAaE,GAIxB,SAASqB,EAAY/B,EAAOd,EAAWZ,EAAKC,EAAMC,EAAMK,GAEpD,IAAImD,EAAIhC,EACR,EAAG,CAEC,IADA,IAAIiC,EAAID,EAAE7C,KAAKA,KACR8C,IAAMD,EAAE5C,MAAM,CACjB,GAAI4C,EAAExF,IAAMyF,EAAEzF,GAAK2G,EAAgBnB,EAAGC,GAAI,CAEtC,IAAIC,EAAIkB,EAAapB,EAAGC,GASxB,OANAD,EAAIxB,EAAawB,EAAGA,EAAE7C,MACtB+C,EAAI1B,EAAa0B,EAAGA,EAAE/C,MAGtBY,EAAaiC,EAAG9C,EAAWZ,EAAKC,EAAMC,EAAMK,EAAS,QACrDkB,EAAamC,EAAGhD,EAAWZ,EAAKC,EAAMC,EAAMK,EAAS,GAGzDoD,EAAIA,EAAE9C,KAEV6C,EAAIA,EAAE7C,WACD6C,IAAMhC,GA0BnB,SAASN,EAASsC,EAAGC,GACjB,OAAOD,EAAErD,EAAIsD,EAAEtD,EAInB,SAASgB,EAAc0D,EAAMrE,GACzB,IAAIsE,EAaR,SAAwBD,EAAMrE,GAC1B,IAIIuE,EAJA7C,EAAI1B,EACJwE,EAAKH,EAAK1E,EACV8E,EAAKJ,EAAKzE,EACV8E,GAAMC,EAAAA,EAKV,EAAG,CACC,GAAIF,GAAM/C,EAAE9B,GAAK6E,GAAM/C,EAAEvB,KAAKP,GAAK8B,EAAEvB,KAAKP,IAAM8B,EAAE9B,EAAG,CACjD,IAAID,EAAI+B,EAAE/B,GAAK8E,EAAK/C,EAAE9B,IAAM8B,EAAEvB,KAAKR,EAAI+B,EAAE/B,IAAM+B,EAAEvB,KAAKP,EAAI8B,EAAE9B,GAC5D,GAAID,GAAK6E,GAAM7E,EAAI+E,IACfA,EAAK/E,EACL4E,EAAI7C,EAAE/B,EAAI+B,EAAEvB,KAAKR,EAAI+B,EAAIA,EAAEvB,KACvBR,IAAM6E,GAAI,OAAOD,EAG7B7C,EAAIA,EAAEvB,WACDuB,IAAM1B,GAEf,IAAKuE,EAAG,OAAO,KAMf,IAIIK,EAJAjC,EAAO4B,EACPM,EAAKN,EAAE5E,EACPmF,EAAKP,EAAE3E,EACPmF,EAASJ,EAAAA,EAGbjD,EAAI6C,EAEJ,GACQC,GAAM9C,EAAE/B,GAAK+B,EAAE/B,GAAKkF,GAAML,IAAO9C,EAAE/B,GAC/BkE,EAAgBY,EAAKK,EAAKN,EAAKE,EAAID,EAAII,EAAIC,EAAIL,EAAKK,EAAKJ,EAAKF,EAAIC,EAAI/C,EAAE/B,EAAG+B,EAAE9B,KAEjFgF,EAAM/D,KAAKmE,IAAIP,EAAK/C,EAAE9B,IAAM4E,EAAK9C,EAAE/B,GAE/BuE,EAAcxC,EAAG2C,KAChBO,EAAMG,GAAWH,IAAQG,IAAWrD,EAAE/B,EAAI4E,EAAE5E,GAAM+B,EAAE/B,IAAM4E,EAAE5E,GAAKsF,EAAqBV,EAAG7C,OAC1F6C,EAAI7C,EACJqD,EAASH,IAIjBlD,EAAIA,EAAEvB,WACDuB,IAAMiB,GAEf,OAAO4B,EAhEMW,CAAeb,EAAMrE,GAClC,IAAKsE,EACD,OAAOtE,EAGX,IAAImF,EAAgBf,EAAaE,EAAQD,GAIzC,OADA7C,EAAa2D,EAAeA,EAAchF,MACnCqB,EAAa8C,EAAQA,EAAOnE,MA2DvC,SAAS8E,EAAqBV,EAAG7C,GAC7B,OAAOC,EAAK4C,EAAEnE,KAAMmE,EAAG7C,EAAEtB,MAAQ,GAAKuB,EAAKD,EAAEvB,KAAMoE,EAAGA,EAAEpE,MAAQ,EAyEpE,SAAS4B,EAAOpC,EAAGC,EAAGL,EAAMC,EAAMK,GAe9B,OAPAF,EAAqB,aADrBA,EAAqB,YADrBA,EAAqB,YADrBA,EAAqB,WAHrBA,GAAKA,EAAIJ,GAAQM,EAAU,GAGjBF,GAAK,IACLA,GAAK,IACLA,GAAK,IACLA,GAAK,KAKfC,EAAqB,aADrBA,EAAqB,YADrBA,EAAqB,YADrBA,EAAqB,WAPrBA,GAAKA,EAAIJ,GAAQK,EAAU,GAOjBD,GAAK,IACLA,GAAK,IACLA,GAAK,IACLA,GAAK,KAEE,EAIrB,SAASY,EAAYQ,GACjB,IAAIU,EAAIV,EACJoE,EAAWpE,EACf,IACQU,EAAE/B,EAAIyF,EAASzF,GAAM+B,EAAE/B,IAAMyF,EAASzF,GAAK+B,EAAE9B,EAAIwF,EAASxF,KAAIwF,EAAW1D,GAC7EA,EAAIA,EAAEvB,WACDuB,IAAMV,GAEf,OAAOoE,EAIX,SAASvB,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI6B,EAAIC,GACjD,OAAQjC,EAAKgC,IAAO/B,EAAKgC,KAAQnC,EAAKkC,IAAO7B,EAAK8B,KAC1CnC,EAAKkC,IAAO9B,EAAK+B,KAAQlC,EAAKiC,IAAO/B,EAAKgC,KAC1ClC,EAAKiC,IAAO7B,EAAK8B,KAAQjC,EAAKgC,IAAO9B,EAAK+B,GAItD,SAASnB,EAAgBnB,EAAGC,GACxB,OAAOD,EAAE7C,KAAK3C,IAAMyF,EAAEzF,GAAKwF,EAAE5C,KAAK5C,IAAMyF,EAAEzF,IA2C9C,SAA2BwF,EAAGC,GAC1B,IAAIvB,EAAIsB,EACR,EAAG,CACC,GAAItB,EAAElE,IAAMwF,EAAExF,GAAKkE,EAAEvB,KAAK3C,IAAMwF,EAAExF,GAAKkE,EAAElE,IAAMyF,EAAEzF,GAAKkE,EAAEvB,KAAK3C,IAAMyF,EAAEzF,GAC7DyG,EAAWvC,EAAGA,EAAEvB,KAAM6C,EAAGC,GAAI,OAAO,EAC5CvB,EAAIA,EAAEvB,WACDuB,IAAMsB,GAEf,OAAO,EAnDyCuC,CAAkBvC,EAAGC,KAC7DiB,EAAclB,EAAGC,IAAMiB,EAAcjB,EAAGD,IA6DpD,SAAsBA,EAAGC,GACrB,IAAIvB,EAAIsB,EACJwC,GAAS,EACTH,GAAMrC,EAAErD,EAAIsD,EAAEtD,GAAK,EACnB2F,GAAMtC,EAAEpD,EAAIqD,EAAErD,GAAK,EACvB,GACU8B,EAAE9B,EAAI0F,GAAS5D,EAAEvB,KAAKP,EAAI0F,GAAQ5D,EAAEvB,KAAKP,IAAM8B,EAAE9B,GAC9CyF,GAAM3D,EAAEvB,KAAKR,EAAI+B,EAAE/B,IAAM2F,EAAK5D,EAAE9B,IAAM8B,EAAEvB,KAAKP,EAAI8B,EAAE9B,GAAK8B,EAAE/B,IAC/D6F,GAAUA,GACd9D,EAAIA,EAAEvB,WACDuB,IAAMsB,GAEf,OAAOwC,EAzE+CC,CAAazC,EAAGC,KAC7DtB,EAAKqB,EAAE5C,KAAM4C,EAAGC,EAAE7C,OAASuB,EAAKqB,EAAGC,EAAE7C,KAAM6C,KAC5C3B,EAAO0B,EAAGC,IAAMtB,EAAKqB,EAAE5C,KAAM4C,EAAGA,EAAE7C,MAAQ,GAAKwB,EAAKsB,EAAE7C,KAAM6C,EAAGA,EAAE9C,MAAQ,GAIrF,SAASwB,EAAKD,EAAGQ,EAAGwD,GAChB,OAAQxD,EAAEtC,EAAI8B,EAAE9B,IAAM8F,EAAE/F,EAAIuC,EAAEvC,IAAMuC,EAAEvC,EAAI+B,EAAE/B,IAAM+F,EAAE9F,EAAIsC,EAAEtC,GAI9D,SAAS0B,EAAOqE,EAAIC,GAChB,OAAOD,EAAGhG,IAAMiG,EAAGjG,GAAKgG,EAAG/F,IAAMgG,EAAGhG,EAIxC,SAASqE,EAAW0B,EAAIE,EAAID,EAAIE,GAC5B,IAAIC,EAAKC,EAAKrE,EAAKgE,EAAIE,EAAID,IACvBK,EAAKD,EAAKrE,EAAKgE,EAAIE,EAAIC,IACvBI,EAAKF,EAAKrE,EAAKiE,EAAIE,EAAIH,IACvBQ,EAAKH,EAAKrE,EAAKiE,EAAIE,EAAID,IAE3B,OAAIE,IAAOE,GAAMC,IAAOC,MAEb,IAAPJ,IAAYK,EAAUT,EAAIC,EAAIC,QACvB,IAAPI,IAAYG,EAAUT,EAAIG,EAAID,QACvB,IAAPK,IAAYE,EAAUR,EAAID,EAAIG,OACvB,IAAPK,IAAYC,EAAUR,EAAIC,EAAIC,OAMtC,SAASM,EAAU1E,EAAGQ,EAAGwD,GACrB,OAAOxD,EAAEvC,GAAKkB,KAAKC,IAAIY,EAAE/B,EAAG+F,EAAE/F,IAAMuC,EAAEvC,GAAKkB,KAAKwF,IAAI3E,EAAE/B,EAAG+F,EAAE/F,IAAMuC,EAAEtC,GAAKiB,KAAKC,IAAIY,EAAE9B,EAAG8F,EAAE9F,IAAMsC,EAAEtC,GAAKiB,KAAKwF,IAAI3E,EAAE9B,EAAG8F,EAAE9F,GAGzH,SAASoG,EAAKM,GACV,OAAOA,EAAM,EAAI,EAAIA,EAAM,GAAK,EAAI,EAgBxC,SAASpC,EAAclB,EAAGC,GACtB,OAAOtB,EAAKqB,EAAE5C,KAAM4C,EAAGA,EAAE7C,MAAQ,EAC7BwB,EAAKqB,EAAGC,EAAGD,EAAE7C,OAAS,GAAKwB,EAAKqB,EAAGA,EAAE5C,KAAM6C,IAAM,EACjDtB,EAAKqB,EAAGC,EAAGD,EAAE5C,MAAQ,GAAKuB,EAAKqB,EAAGA,EAAE7C,KAAM8C,GAAK,EAqBvD,SAASmB,EAAapB,EAAGC,GACrB,IAAIjF,EAAK,IAAIuI,EAAKvD,EAAExF,EAAGwF,EAAErD,EAAGqD,EAAEpD,GAC1B4G,EAAK,IAAID,EAAKtD,EAAEzF,EAAGyF,EAAEtD,EAAGsD,EAAErD,GAC1B6G,EAAKzD,EAAE7C,KACPuG,EAAKzD,EAAE7C,KAcX,OAZA4C,EAAE7C,KAAO8C,EACTA,EAAE7C,KAAO4C,EAEThF,EAAGmC,KAAOsG,EACVA,EAAGrG,KAAOpC,EAEVwI,EAAGrG,KAAOnC,EACVA,EAAGoC,KAAOoG,EAEVE,EAAGvG,KAAOqG,EACVA,EAAGpG,KAAOsG,EAEHF,EAIX,SAASnF,EAAW7D,EAAGmC,EAAGC,EAAGuB,GACzB,IAAIO,EAAI,IAAI6E,EAAK/I,EAAGmC,EAAGC,GAYvB,OAVKuB,GAKDO,EAAEvB,KAAOgB,EAAKhB,KACduB,EAAEtB,KAAOe,EACTA,EAAKhB,KAAKC,KAAOsB,EACjBP,EAAKhB,KAAOuB,IAPZA,EAAEtB,KAAOsB,EACTA,EAAEvB,KAAOuB,GAQNA,EAGX,SAASH,EAAWG,GAChBA,EAAEvB,KAAKC,KAAOsB,EAAEtB,KAChBsB,EAAEtB,KAAKD,KAAOuB,EAAEvB,KAEZuB,EAAEM,QAAON,EAAEM,MAAMC,MAAQP,EAAEO,OAC3BP,EAAEO,QAAOP,EAAEO,MAAMD,MAAQN,EAAEM,OAGnC,SAASuE,EAAK/I,EAAGmC,EAAGC,GAEhB5D,KAAKwB,EAAIA,EAGTxB,KAAK2D,EAAIA,EACT3D,KAAK4D,EAAIA,EAGT5D,KAAKoE,KAAO,KACZpE,KAAKmE,KAAO,KAGZnE,KAAK8F,EAAI,EAGT9F,KAAKgG,MAAQ,KACbhG,KAAKiG,MAAQ,KAGbjG,KAAKuE,SAAU,EAgCnB,SAASa,EAAWhC,EAAM4B,EAAOC,EAAK3B,GAElC,IADA,IAAIqH,EAAM,EACDnJ,EAAIwD,EAAOtC,EAAIuC,EAAM3B,EAAK9B,EAAIyD,EAAKzD,GAAK8B,EAC7CqH,IAAQvH,EAAKV,GAAKU,EAAK5B,KAAO4B,EAAK5B,EAAI,GAAK4B,EAAKV,EAAI,IACrDA,EAAIlB,EAER,OAAOmJ,EAjCXzH,EAAO0H,UAAY,SAAUxH,EAAMC,EAAaC,EAAKY,GACjD,IAAIJ,EAAWT,GAAeA,EAAY3B,OACtCqC,EAAWD,EAAWT,EAAY,GAAKC,EAAMF,EAAK1B,OAElDmJ,EAAchG,KAAKmE,IAAI5D,EAAWhC,EAAM,EAAGW,EAAUT,IACzD,GAAIQ,EACA,IAAK,IAAItC,EAAI,EAAGa,EAAMgB,EAAY3B,OAAQF,EAAIa,EAAKb,IAAK,CACpD,IAAIwD,EAAQ3B,EAAY7B,GAAK8B,EACzB2B,EAAMzD,EAAIa,EAAM,EAAIgB,EAAY7B,EAAI,GAAK8B,EAAMF,EAAK1B,OACxDmJ,GAAehG,KAAKmE,IAAI5D,EAAWhC,EAAM4B,EAAOC,EAAK3B,IAI7D,IAAIwH,EAAgB,EACpB,IAAKtJ,EAAI,EAAGA,EAAI0C,EAAUxC,OAAQF,GAAK,EAAG,CACtC,IAAIwF,EAAI9C,EAAU1C,GAAK8B,EACnB2D,EAAI/C,EAAU1C,EAAI,GAAK8B,EACvB4D,EAAIhD,EAAU1C,EAAI,GAAK8B,EAC3BwH,GAAiBjG,KAAKmE,KACjB5F,EAAK4D,GAAK5D,EAAK8D,KAAO9D,EAAK6D,EAAI,GAAK7D,EAAK4D,EAAI,KAC7C5D,EAAK4D,GAAK5D,EAAK6D,KAAO7D,EAAK8D,EAAI,GAAK9D,EAAK4D,EAAI,KAGtD,OAAuB,IAAhB6D,GAAuC,IAAlBC,EAAsB,EAC9CjG,KAAKmE,KAAK8B,EAAgBD,GAAeA,IAajD3H,EAAO6H,QAAU,SAAU3H,GAKvB,IAJA,IAAIE,EAAMF,EAAK,GAAG,GAAG1B,OACjBsJ,EAAS,CAACC,SAAU,GAAIC,MAAO,GAAIC,WAAY7H,GAC/C8H,EAAY,EAEP5J,EAAI,EAAGA,EAAI4B,EAAK1B,OAAQF,IAAK,CAClC,IAAK,IAAIkB,EAAI,EAAGA,EAAIU,EAAK5B,GAAGE,OAAQgB,IAChC,IAAK,IAAI2I,EAAI,EAAGA,EAAI/H,EAAK+H,IAAKL,EAAOC,SAASzK,KAAK4C,EAAK5B,GAAGkB,GAAG2I,IAE9D7J,EAAI,IACJ4J,GAAahI,EAAK5B,EAAI,GAAGE,OACzBsJ,EAAOE,MAAM1K,KAAK4K,IAG1B,OAAOJ,uCCtqBT,SAASM,GAGV,IAAIC,EAA4CvI,IAC9CA,EAAQwI,UAAYxI,EAClByI,EAA0C1I,IAC5CA,EAAOyI,UAAYzI,EACjB2I,EAA8B,iBAAVC,GAAsBA,EAE7CD,EAAWC,SAAWD,GACtBA,EAAWE,SAAWF,GACtBA,EAAWG,OAASH,IAEpBJ,EAAOI,GAQR,IAAII,EAiCJC,EA9BAC,EAAS,WAGTC,EAAO,GAUPC,EAAgB,QAChBC,EAAgB,eAChBC,EAAkB,4BAGlBC,EAAS,CACRC,SAAY,kDACZ,YAAa,iDACb,gBAAiB,iBAKlBC,EAAQ1H,KAAK0H,MACbC,EAAqBC,OAAOC,aAa5B,SAASC,EAAMC,GACd,MAAMC,WAAWR,EAAOO,IAWzB,SAASE,EAAIC,EAAOlN,GAGnB,IAFA,IAAI6B,EAASqL,EAAMrL,OACfsJ,EAAS,GACNtJ,KACNsJ,EAAOtJ,GAAU7B,EAAGkN,EAAMrL,IAE3B,OAAOsJ,EAaR,SAASgC,EAAUC,EAAQpN,GAC1B,IAAIqN,EAAQD,EAAOE,MAAM,KACrBnC,EAAS,GAWb,OAVIkC,EAAMxL,OAAS,IAGlBsJ,EAASkC,EAAM,GAAK,IACpBD,EAASC,EAAM,IAMTlC,EADO8B,GAFdG,EAASA,EAAOG,QAAQhB,EAAiB,MACrBe,MAAM,KACAtN,GAAIwN,KAAK,KAiBpC,SAASC,EAAWL,GAMnB,IALA,IAGIM,EACAC,EAJAC,EAAS,GACTC,EAAU,EACVhM,EAASuL,EAAOvL,OAGbgM,EAAUhM,IAChB6L,EAAQN,EAAOU,WAAWD,OACb,OAAUH,GAAS,OAAUG,EAAUhM,EAG3B,QAAX,OADb8L,EAAQP,EAAOU,WAAWD,OAEzBD,EAAOjN,OAAe,KAAR+M,IAAkB,KAAe,KAARC,GAAiB,QAIxDC,EAAOjN,KAAK+M,GACZG,KAGDD,EAAOjN,KAAK+M,GAGd,OAAOE,EAWR,SAASG,EAAWb,GACnB,OAAOD,EAAIC,GAAO,SAASQ,GAC1B,IAAIE,EAAS,GAOb,OANIF,EAAQ,QAEXE,GAAUjB,GADVe,GAAS,SAC8B,GAAK,KAAQ,OACpDA,EAAQ,MAAiB,KAARA,GAElBE,GAAUjB,EAAmBe,MAE3BF,KAAK,IAoCT,SAASQ,EAAaC,EAAOC,GAG5B,OAAOD,EAAQ,GAAK,IAAMA,EAAQ,MAAgB,GAARC,IAAc,GAQzD,SAASC,EAAMC,EAAOC,EAAWC,GAChC,IAAIC,EAAI,EAGR,IAFAH,EAAQE,EAAY5B,EAAM0B,EA1LpB,KA0LoCA,GAAS,EACnDA,GAAS1B,EAAM0B,EAAQC,GACOD,EAAQI,IAA2BD,GAAKnC,EACrEgC,EAAQ1B,EAAM0B,EA3KAhC,IA6Kf,OAAOM,EAAM6B,EAAI,GAAsBH,GAASA,EAhM1C,KA0MP,SAASK,EAAOC,GAEf,IAEIC,EAIAC,EACA/L,EACAgM,EACAC,EACAC,EACAR,EACAN,EACAe,EAEAC,EArEiBC,EAsDjBtB,EAAS,GACTuB,EAAcT,EAAM7M,OAEpBF,EAAI,EACJwG,EA7MM,IA8MNiH,EA/MS,GAoOb,KALAR,EAAQF,EAAMW,YA7NH,MA8NC,IACXT,EAAQ,GAGJ/L,EAAI,EAAGA,EAAI+L,IAAS/L,EAEpB6L,EAAMZ,WAAWjL,IAAM,KAC1BiK,EAAM,aAEPc,EAAOjN,KAAK+N,EAAMZ,WAAWjL,IAM9B,IAAKgM,EAAQD,EAAQ,EAAIA,EAAQ,EAAI,EAAGC,EAAQM,GAAwC,CAOvF,IAAKL,EAAOnN,EAAGoN,EAAI,EAAGR,EAAInC,EAErByC,GAASM,GACZrC,EAAM,mBAGPmB,GAxGmBiB,EAwGER,EAAMZ,WAAWe,MAvGxB,GAAK,GACbK,EAAY,GAEhBA,EAAY,GAAK,GACbA,EAAY,GAEhBA,EAAY,GAAK,GACbA,EAAY,GAEb9C,IAgGQA,GAAQ6B,EAAQvB,GAAOP,EAASxK,GAAKoN,KACjDjC,EAAM,YAGPnL,GAAKsM,EAAQc,IAGTd,GAFJe,EAAIT,GAAKa,EAvQL,EAuQoBb,GAAKa,EAtQzB,GAAA,GAsQ8Cb,EAAIa,IAbHb,GAAKnC,EAoBpD2C,EAAIrC,EAAMP,GADd8C,EAAa7C,EAAO4C,KAEnBlC,EAAM,YAGPiC,GAAKE,EAKNG,EAAOjB,EAAMxM,EAAImN,EADjBH,EAAMf,EAAO/L,OAAS,EACc,GAARiN,GAIxBpC,EAAM/K,EAAIgN,GAAOxC,EAAShE,GAC7B2E,EAAM,YAGP3E,GAAKuE,EAAM/K,EAAIgN,GACfhN,GAAKgN,EAGLf,EAAO0B,OAAO3N,IAAK,EAAGwG,GAIvB,OAAO4F,EAAWH,GAUnB,SAAS2B,EAAOb,GACf,IAAIvG,EACAiG,EACAoB,EACAC,EACAL,EACAvM,EACA6F,EACArC,EACAkI,EACAS,EACAU,EAGAP,EAEAQ,EACAV,EACAW,EANAhC,EAAS,GAoBb,IARAuB,GAHAT,EAAQjB,EAAWiB,IAGC7M,OAGpBsG,EAvUU,IAwUViG,EAAQ,EACRgB,EA1Ua,GA6URvM,EAAI,EAAGA,EAAIsM,IAAetM,GAC9B6M,EAAehB,EAAM7L,IACF,KAClB+K,EAAOjN,KAAKgM,EAAmB+C,IAejC,IAXAF,EAAiBC,EAAc7B,EAAO/L,OAMlC4N,GACH7B,EAAOjN,KAzVG,KA6VJ6O,EAAiBL,GAAa,CAIpC,IAAKzG,EAAIyD,EAAQtJ,EAAI,EAAGA,EAAIsM,IAAetM,GAC1C6M,EAAehB,EAAM7L,KACDsF,GAAKuH,EAAehH,IACvCA,EAAIgH,GAcN,IAPIhH,EAAIP,EAAIuE,GAAOP,EAASiC,IAD5BuB,EAAwBH,EAAiB,KAExC1C,EAAM,YAGPsB,IAAU1F,EAAIP,GAAKwH,EACnBxH,EAAIO,EAEC7F,EAAI,EAAGA,EAAIsM,IAAetM,EAO9B,IANA6M,EAAehB,EAAM7L,IAEFsF,KAAOiG,EAAQjC,GACjCW,EAAM,YAGH4C,GAAgBvH,EAAG,CAEtB,IAAK9B,EAAI+H,EAAOG,EAAInC,IAEf/F,GADJ2I,EAAIT,GAAKa,EAlYP,EAkYsBb,GAAKa,EAjY3B,GAAA,GAiYgDb,EAAIa,IADTb,GAAKnC,EAKlDwD,EAAUvJ,EAAI2I,EACdC,EAAa7C,EAAO4C,EACpBpB,EAAOjN,KACNgM,EAAmBqB,EAAagB,EAAIY,EAAUX,EAAY,KAE3D5I,EAAIqG,EAAMkD,EAAUX,GAGrBrB,EAAOjN,KAAKgM,EAAmBqB,EAAa3H,EAAG,KAC/C+I,EAAOjB,EAAMC,EAAOuB,EAAuBH,GAAkBC,GAC7DrB,EAAQ,IACNoB,IAIFpB,IACAjG,EAGH,OAAOyF,EAAOJ,KAAK,IA+Eb,GAnCPvB,EAAW,CAMV4D,QAAW,QAQXC,KAAQ,CACPrB,OAAUhB,EACV8B,OAAUxB,GAEXU,OAAUA,EACVc,OAAUA,EACVQ,QA/BD,SAAiBrB,GAChB,OAAOvB,EAAUuB,GAAO,SAAStB,GAChC,OAAOd,EAAc0D,KAAK5C,GACvB,OAASmC,EAAOnC,GAChBA,MA4BJ6C,UAnDD,SAAmBvB,GAClB,OAAOvB,EAAUuB,GAAO,SAAStB,GAChC,OAAOf,EAAc2D,KAAK5C,GACvBqB,EAAOrB,EAAO9L,MAAM,GAAG4O,eACvB9C,OA6DM1B,GAAeE,EACzB,GAAI1I,EAAOC,SAAWuI,EACrBE,EAAWzI,QAAU8I,OAErB,IAAKC,KAAOD,EACXA,EAASrM,eAAesM,KAASR,EAAYQ,GAAOD,EAASC,SAI/DT,EAAKQ,SAAWA,EA7gBjB,CAghBC9L,MC/gBFgQ,EACY,SAASC,GACjB,MAAuB,iBAAT,GAFlBD,EAIY,SAASC,GACjB,MAAuB,iBAAT,GAA6B,OAARA,GALvCD,EAOU,SAASC,GACf,OAAe,OAARA,GARXD,EAUqB,SAASC,GAC1B,OAAc,MAAPA,GCaX,SAASxQ,EAAeyQ,EAAKC,GAC3B,OAAO5Q,OAAOC,UAAUC,eAAeyB,KAAKgP,EAAKC,GAGnD,IAAc7B,EAAG,SAAS8B,EAAIC,EAAKC,EAAIC,GACrCF,EAAMA,GAAO,IACbC,EAAKA,GAAM,IACX,IAAIJ,EAAM,GAEV,GAAkB,iBAAPE,GAAiC,IAAdA,EAAG1O,OAC/B,OAAOwO,EAGT,IAAIM,EAAS,MACbJ,EAAKA,EAAGjD,MAAMkD,GAEd,IAAII,EAAU,IACVF,GAAsC,iBAApBA,EAAQE,UAC5BA,EAAUF,EAAQE,SAGpB,IAAIpO,EAAM+N,EAAG1O,OAET+O,EAAU,GAAKpO,EAAMoO,IACvBpO,EAAMoO,GAGR,IAAK,IAAIjP,EAAI,EAAGA,EAAIa,IAAOb,EAAG,CAC5B,IAEIkP,EAAMC,EAAMvC,EAAGwC,EAFfjN,EAAIyM,EAAG5O,GAAG4L,QAAQoD,EAAQ,OAC1BK,EAAMlN,EAAEmN,QAAQR,GAGhBO,GAAO,GACTH,EAAO/M,EAAEoN,OAAO,EAAGF,GACnBF,EAAOhN,EAAEoN,OAAOF,EAAM,KAEtBH,EAAO/M,EACPgN,EAAO,IAGTvC,EAAI4C,mBAAmBN,GACvBE,EAAII,mBAAmBL,GAElBlR,EAAeyQ,EAAK9B,GAEdxM,MAAMqP,QAAQf,EAAI9B,IAC3B8B,EAAI9B,GAAG5N,KAAKoQ,GAEZV,EAAI9B,GAAK,CAAC8B,EAAI9B,GAAIwC,GAJlBV,EAAI9B,GAAKwC,EAQb,OAAOV,GCvDLgB,EAAqB,SAASN,GAChC,cAAeA,GACb,IAAK,SACH,OAAOA,EAET,IAAK,UACH,OAAOA,EAAI,OAAS,QAEtB,IAAK,SACH,OAAOO,SAASP,GAAKA,EAAI,GAE3B,QACE,MAAO,KAICxB,EAAG,SAASc,EAAKG,EAAKC,EAAItP,GAOtC,OANAqP,EAAMA,GAAO,IACbC,EAAKA,GAAM,IACC,OAARJ,IACFA,OAAM1N,GAGW,iBAAR0N,EACF3Q,OAAO6R,KAAKlB,GAAKpD,KAAI,SAASsB,GACnC,IAAIiD,EAAKC,mBAAmBJ,EAAmB9C,IAAMkC,EACrD,OAAI1O,MAAMqP,QAAQf,EAAI9B,IACb8B,EAAI9B,GAAGtB,KAAI,SAAS8D,GACzB,OAAOS,EAAKC,mBAAmBJ,EAAmBN,OACjDvD,KAAKgD,GAEDgB,EAAKC,mBAAmBJ,EAAmBhB,EAAI9B,QAEvDf,KAAKgD,GAILrP,EACEsQ,mBAAmBJ,EAAmBlQ,IAASsP,EAC/CgB,mBAAmBJ,EAAmBhB,IAF3B,uBC1DpBlN,EAAAsL,OAAiBtL,EAAAuO,MAAgBC,EACjCxO,EAAcoM,OAAGpM,EAAiByO,UAAGC,KCuBxBH,EAAGI,GACDC,EA0Zf,SAAoBC,EAAQC,GAC1B,OAAOH,GAASE,GAAQ,GAAO,GAAMD,QAAQE,IAzZjCC,EAsVd,SAAmB7B,GAKbF,EAAcE,KAAMA,EAAMyB,GAASzB,IACvC,KAAMA,aAAe8B,GAAM,OAAOA,EAAIxS,UAAUuS,OAAO7Q,KAAKgP,GAC5D,OAAOA,EAAI6B,UAzVb,SAASC,IACPhS,KAAKiS,SAAW,KAChBjS,KAAKkS,QAAU,KACflS,KAAKmS,KAAO,KACZnS,KAAKoS,KAAO,KACZpS,KAAKqS,KAAO,KACZrS,KAAKsS,SAAW,KAChBtS,KAAKuS,KAAO,KACZvS,KAAKwS,OAAS,KACdxS,KAAKyS,MAAQ,KACbzS,KAAK0S,SAAW,KAChB1S,KAAK2S,KAAO,KACZ3S,KAAK4S,KAAO,KAOd,IAAIC,EAAkB,oBAClBC,EAAc,WAGdC,EAAoB,qCAOpBC,EAAS,CAAC,IAAK,IAAK,IAAK,KAAM,IAAK,KAAK3R,OAHhC,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,OAM/C4R,EAAa,CAAC,KAAM5R,OAAO2R,GAK3BE,EAAe,CAAC,IAAK,IAAK,IAAK,IAAK,KAAK7R,OAAO4R,GAChDE,GAAkB,CAAC,IAAK,IAAK,KAE7BC,GAAsB,yBACtBC,GAAoB,+BAEpBC,GAAiB,CACfC,YAAc,EACd,eAAe,GAGjBC,GAAmB,CACjBD,YAAc,EACd,eAAe,GAGjBE,GAAkB,CAChBC,MAAQ,EACRC,OAAS,EACTC,KAAO,EACPC,QAAU,EACVC,MAAQ,EACR,SAAS,EACT,UAAU,EACV,QAAQ,EACR,WAAW,EACX,SAAS,GAIf,SAASnC,GAASoC,EAAKC,EAAkBC,GACvC,GAAIF,GAAO/D,EAAc+D,IAAQA,aAAe/B,EAAK,OAAO+B,EAE5D,IAAIG,EAAI,IAAIlC,EAEZ,OADAkC,EAAE3C,MAAMwC,EAAKC,EAAkBC,GACxBC,EAGTlC,EAAIxS,UAAU+R,MAAQ,SAASwC,EAAKC,EAAkBC,GACpD,IAAKjE,EAAc+D,GACjB,MAAM,IAAI3T,UAAU,gDAAkD2T,GAMxE,IAAII,EAAaJ,EAAIjD,QAAQ,KACzBsD,GACqB,IAAhBD,GAAqBA,EAAaJ,EAAIjD,QAAQ,KAAQ,IAAM,IACjEuD,EAASN,EAAI5G,MAAMiH,GAEvBC,EAAO,GAAKA,EAAO,GAAGjH,QADL,MACyB,KAG1C,IAAIkH,EAFJP,EAAMM,EAAOhH,KAAK+G,GAQlB,GAFAE,EAAOA,EAAKC,QAEPN,GAA+C,IAA1BF,EAAI5G,MAAM,KAAKzL,OAAc,CAErD,IAAI8S,EAAazB,EAAkB0B,KAAKH,GACxC,GAAIE,EAeF,OAdAxU,KAAK2S,KAAO2B,EACZtU,KAAK4S,KAAO0B,EACZtU,KAAK0S,SAAW8B,EAAW,GACvBA,EAAW,IACbxU,KAAKwS,OAASgC,EAAW,GAEvBxU,KAAKyS,MADHuB,EACWU,EAAYnD,MAAMvR,KAAKwS,OAAOzB,OAAO,IAErC/Q,KAAKwS,OAAOzB,OAAO,IAEzBiD,IACThU,KAAKwS,OAAS,GACdxS,KAAKyS,MAAQ,IAERzS,KAIX,IAAI2U,EAAQ9B,EAAgB4B,KAAKH,GACjC,GAAIK,EAAO,CAET,IAAIC,GADJD,EAAQA,EAAM,IACS5E,cACvB/P,KAAKiS,SAAW2C,EAChBN,EAAOA,EAAKvD,OAAO4D,EAAMjT,QAO3B,GAAIuS,GAAqBU,GAASL,EAAKO,MAAM,wBAAyB,CACpE,IAAI3C,EAAgC,OAAtBoC,EAAKvD,OAAO,EAAG,IACzBmB,GAAayC,GAASnB,GAAiBmB,KACzCL,EAAOA,EAAKvD,OAAO,GACnB/Q,KAAKkS,SAAU,GAInB,IAAKsB,GAAiBmB,KACjBzC,GAAYyC,IAAUlB,GAAgBkB,IAAU,CAmBnD,IADA,IASIxC,EAAM2C,EATNC,GAAW,EACNvT,EAAI,EAAGA,EAAI2R,GAAgBzR,OAAQF,IAAK,EAElC,KADTwT,EAAMV,EAAKxD,QAAQqC,GAAgB3R,QACP,IAAbuT,GAAkBC,EAAMD,KACzCA,EAAUC,IAiBE,KATdF,GAFe,IAAbC,EAEOT,EAAKpF,YAAY,KAIjBoF,EAAKpF,YAAY,IAAK6F,MAM/B5C,EAAOmC,EAAKnT,MAAM,EAAG2T,GACrBR,EAAOA,EAAKnT,MAAM2T,EAAS,GAC3B9U,KAAKmS,KAAOnB,mBAAmBmB,IAIjC4C,GAAW,EACX,IAASvT,EAAI,EAAGA,EAAI0R,EAAaxR,OAAQF,IAAK,CAC5C,IAAIwT,GACS,KADTA,EAAMV,EAAKxD,QAAQoC,EAAa1R,QACJ,IAAbuT,GAAkBC,EAAMD,KACzCA,EAAUC,IAGG,IAAbD,IACFA,EAAUT,EAAK5S,QAEjB1B,KAAKoS,KAAOkC,EAAKnT,MAAM,EAAG4T,GAC1BT,EAAOA,EAAKnT,MAAM4T,GAGlB/U,KAAKiV,YAILjV,KAAKsS,SAAWtS,KAAKsS,UAAY,GAIjC,IAAI4C,EAAoC,MAArBlV,KAAKsS,SAAS,IACe,MAA5CtS,KAAKsS,SAAStS,KAAKsS,SAAS5Q,OAAS,GAGzC,IAAKwT,EAEH,IADA,IAAIC,EAAYnV,KAAKsS,SAASnF,MAAM,MACpB1L,GAAPD,EAAI,EAAO2T,EAAUzT,QAAQF,EAAIC,EAAGD,IAAK,CAChD,IAAI4T,EAAOD,EAAU3T,GACrB,GAAK4T,IACAA,EAAKP,MAAMzB,IAAsB,CAEpC,IADA,IAAIiC,EAAU,GACL3S,EAAI,EAAG0L,EAAIgH,EAAK1T,OAAQgB,EAAI0L,EAAG1L,IAClC0S,EAAKzH,WAAWjL,GAAK,IAIvB2S,GAAW,IAEXA,GAAWD,EAAK1S,GAIpB,IAAK2S,EAAQR,MAAMzB,IAAsB,CACvC,IAAIkC,EAAaH,EAAUhU,MAAM,EAAGK,GAChC+T,EAAUJ,EAAUhU,MAAMK,EAAI,GAC9BgU,EAAMJ,EAAKP,MAAMxB,IACjBmC,IACFF,EAAW9U,KAAKgV,EAAI,IACpBD,EAAQE,QAAQD,EAAI,KAElBD,EAAQ7T,SACV4S,EAAO,IAAMiB,EAAQlI,KAAK,KAAOiH,GAEnCtU,KAAKsS,SAAWgD,EAAWjI,KAAK,KAChC,QAMJrN,KAAKsS,SAAS5Q,OAjND,IAkNf1B,KAAKsS,SAAW,GAGhBtS,KAAKsS,SAAWtS,KAAKsS,SAASvC,cAG3BmF,IAKHlV,KAAKsS,SAAWxG,EAAS8D,QAAQ5P,KAAKsS,WAGxC,IAAI5M,EAAI1F,KAAKqS,KAAO,IAAMrS,KAAKqS,KAAO,GAClCqD,EAAI1V,KAAKsS,UAAY,GACzBtS,KAAKoS,KAAOsD,EAAIhQ,EAChB1F,KAAK4S,MAAQ5S,KAAKoS,KAId8C,IACFlV,KAAKsS,SAAWtS,KAAKsS,SAASvB,OAAO,EAAG/Q,KAAKsS,SAAS5Q,OAAS,GAC/C,MAAZ4S,EAAK,KACPA,EAAO,IAAMA,IAOnB,IAAKhB,GAAesB,GAKlB,IAASpT,EAAI,EAAGC,EAAIwR,EAAWvR,OAAQF,EAAIC,EAAGD,IAAK,CACjD,IAAImU,EAAK1C,EAAWzR,GACpB,IAA0B,IAAtB8S,EAAKxD,QAAQ6E,GAAjB,CAEA,IAAIC,EAAMtE,mBAAmBqE,GACzBC,IAAQD,IACVC,EAAMC,OAAOF,IAEfrB,EAAOA,EAAKnH,MAAMwI,GAAItI,KAAKuI,IAM/B,IAAIrD,EAAO+B,EAAKxD,QAAQ,MACV,IAAVyB,IAEFvS,KAAKuS,KAAO+B,EAAKvD,OAAOwB,GACxB+B,EAAOA,EAAKnT,MAAM,EAAGoR,IAEvB,IAAIuD,EAAKxB,EAAKxD,QAAQ,KAoBtB,IAnBY,IAARgF,GACF9V,KAAKwS,OAAS8B,EAAKvD,OAAO+E,GAC1B9V,KAAKyS,MAAQ6B,EAAKvD,OAAO+E,EAAK,GAC1B9B,IACFhU,KAAKyS,MAAQiC,EAAYnD,MAAMvR,KAAKyS,QAEtC6B,EAAOA,EAAKnT,MAAM,EAAG2U,IACZ9B,IAEThU,KAAKwS,OAAS,GACdxS,KAAKyS,MAAQ,IAEX6B,IAAMtU,KAAK0S,SAAW4B,GACtBb,GAAgBmB,IAChB5U,KAAKsS,WAAatS,KAAK0S,WACzB1S,KAAK0S,SAAW,KAId1S,KAAK0S,UAAY1S,KAAKwS,OAAQ,CAC5B9M,EAAI1F,KAAK0S,UAAY,GAAzB,IACIqD,EAAI/V,KAAKwS,QAAU,GACvBxS,KAAK2S,KAAOjN,EAAIqQ,EAKlB,OADA/V,KAAK4S,KAAO5S,KAAK+R,SACV/R,MAcTgS,EAAIxS,UAAUuS,OAAS,WACrB,IAAII,EAAOnS,KAAKmS,MAAQ,GACpBA,IAEFA,GADAA,EAAOb,mBAAmBa,IACd/E,QAAQ,OAAQ,KAC5B+E,GAAQ,KAGV,IAAIF,EAAWjS,KAAKiS,UAAY,GAC5BS,EAAW1S,KAAK0S,UAAY,GAC5BH,EAAOvS,KAAKuS,MAAQ,GACpBH,GAAO,EACPK,EAAQ,GAERzS,KAAKoS,KACPA,EAAOD,EAAOnS,KAAKoS,KACVpS,KAAKsS,WACdF,EAAOD,IAAwC,IAAhCnS,KAAKsS,SAASxB,QAAQ,KACjC9Q,KAAKsS,SACL,IAAMtS,KAAKsS,SAAW,KACtBtS,KAAKqS,OACPD,GAAQ,IAAMpS,KAAKqS,OAInBrS,KAAKyS,OACLzC,EAAchQ,KAAKyS,QACnBlT,OAAO6R,KAAKpR,KAAKyS,OAAO/Q,SAC1B+Q,EAAQiC,EAAYjD,UAAUzR,KAAKyS,QAGrC,IAAID,EAASxS,KAAKwS,QAAWC,GAAU,IAAMA,GAAW,GAsBxD,OApBIR,GAAoC,MAAxBA,EAASlB,QAAQ,KAAYkB,GAAY,KAIrDjS,KAAKkS,WACHD,GAAYwB,GAAgBxB,MAAuB,IAATG,GAC9CA,EAAO,MAAQA,GAAQ,IACnBM,GAAmC,MAAvBA,EAASsD,OAAO,KAAYtD,EAAW,IAAMA,IACnDN,IACVA,EAAO,IAGLG,GAA2B,MAAnBA,EAAKyD,OAAO,KAAYzD,EAAO,IAAMA,GAC7CC,GAA+B,MAArBA,EAAOwD,OAAO,KAAYxD,EAAS,IAAMA,GAOhDP,EAAWG,GALlBM,EAAWA,EAAStF,QAAQ,SAAS,SAASyH,GAC5C,OAAOvD,mBAAmBuD,QAE5BrC,EAASA,EAAOpF,QAAQ,IAAK,QAEgBmF,GAO/CP,EAAIxS,UAAUoS,QAAU,SAASE,GAC/B,OAAO9R,KAAKiW,cAActE,GAASG,GAAU,GAAO,IAAOC,UAQ7DC,EAAIxS,UAAUyW,cAAgB,SAASnE,GACrC,GAAI9B,EAAc8B,GAAW,CAC3B,IAAIoE,EAAM,IAAIlE,EACdkE,EAAI3E,MAAMO,GAAU,GAAO,GAC3BA,EAAWoE,EAKb,IAFA,IAAIlL,EAAS,IAAIgH,EACbmE,EAAQ5W,OAAO6R,KAAKpR,MACfoW,EAAK,EAAGA,EAAKD,EAAMzU,OAAQ0U,IAAM,CACxC,IAAIC,EAAOF,EAAMC,GACjBpL,EAAOqL,GAAQrW,KAAKqW,GAQtB,GAHArL,EAAOuH,KAAOT,EAASS,KAGD,KAAlBT,EAASc,KAEX,OADA5H,EAAO4H,KAAO5H,EAAO+G,SACd/G,EAIT,GAAI8G,EAASI,UAAYJ,EAASG,SAAU,CAG1C,IADA,IAAIqE,EAAQ/W,OAAO6R,KAAKU,GACfyE,EAAK,EAAGA,EAAKD,EAAM5U,OAAQ6U,IAAM,CACxC,IAAIC,EAAOF,EAAMC,GACJ,aAATC,IACFxL,EAAOwL,GAAQ1E,EAAS0E,IAU5B,OANI/C,GAAgBzI,EAAOiH,WACvBjH,EAAOsH,WAAatH,EAAO0H,WAC7B1H,EAAO2H,KAAO3H,EAAO0H,SAAW,KAGlC1H,EAAO4H,KAAO5H,EAAO+G,SACd/G,EAGT,GAAI8G,EAASG,UAAYH,EAASG,WAAajH,EAAOiH,SAAU,CAS9D,IAAKwB,GAAgB3B,EAASG,UAAW,CAEvC,IADA,IAAIb,EAAO7R,OAAO6R,KAAKU,GACdlB,EAAI,EAAGA,EAAIQ,EAAK1P,OAAQkP,IAAK,CACpC,IAAIxC,EAAIgD,EAAKR,GACb5F,EAAOoD,GAAK0D,EAAS1D,GAGvB,OADApD,EAAO4H,KAAO5H,EAAO+G,SACd/G,EAIT,GADAA,EAAOiH,SAAWH,EAASG,SACtBH,EAASM,MAASoB,GAAiB1B,EAASG,UAS/CjH,EAAO0H,SAAWZ,EAASY,aAT+B,CAE1D,IADA,IAAI+D,GAAW3E,EAASY,UAAY,IAAIvF,MAAM,KACvCsJ,EAAQ/U,UAAYoQ,EAASM,KAAOqE,EAAQC,WAC9C5E,EAASM,OAAMN,EAASM,KAAO,IAC/BN,EAASQ,WAAUR,EAASQ,SAAW,IACzB,KAAfmE,EAAQ,IAAWA,EAAQhB,QAAQ,IACnCgB,EAAQ/U,OAAS,GAAG+U,EAAQhB,QAAQ,IACxCzK,EAAO0H,SAAW+D,EAAQpJ,KAAK,KAWjC,GAPArC,EAAOwH,OAASV,EAASU,OACzBxH,EAAOyH,MAAQX,EAASW,MACxBzH,EAAOoH,KAAON,EAASM,MAAQ,GAC/BpH,EAAOmH,KAAOL,EAASK,KACvBnH,EAAOsH,SAAWR,EAASQ,UAAYR,EAASM,KAChDpH,EAAOqH,KAAOP,EAASO,KAEnBrH,EAAO0H,UAAY1H,EAAOwH,OAAQ,CACpC,IAAI9M,EAAIsF,EAAO0H,UAAY,GACvBqD,EAAI/K,EAAOwH,QAAU,GACzBxH,EAAO2H,KAAOjN,EAAIqQ,EAIpB,OAFA/K,EAAOkH,QAAUlH,EAAOkH,SAAWJ,EAASI,QAC5ClH,EAAO4H,KAAO5H,EAAO+G,SACd/G,EAGT,IAAI2L,EAAe3L,EAAO0H,UAA0C,MAA9B1H,EAAO0H,SAASsD,OAAO,GACzDY,EACI9E,EAASM,MACTN,EAASY,UAA4C,MAAhCZ,EAASY,SAASsD,OAAO,GAElDa,EAAcD,GAAYD,GACX3L,EAAOoH,MAAQN,EAASY,SACvCoE,EAAgBD,EAChBE,EAAU/L,EAAO0H,UAAY1H,EAAO0H,SAASvF,MAAM,MAAQ,GAE3D6J,GADAP,EAAU3E,EAASY,UAAYZ,EAASY,SAASvF,MAAM,MAAQ,GACnDnC,EAAOiH,WAAawB,GAAgBzI,EAAOiH,WA2B3D,GApBI+E,IACFhM,EAAOsH,SAAW,GAClBtH,EAAOqH,KAAO,KACVrH,EAAOoH,OACU,KAAf2E,EAAQ,GAAWA,EAAQ,GAAK/L,EAAOoH,KACtC2E,EAAQtB,QAAQzK,EAAOoH,OAE9BpH,EAAOoH,KAAO,GACVN,EAASG,WACXH,EAASQ,SAAW,KACpBR,EAASO,KAAO,KACZP,EAASM,OACQ,KAAfqE,EAAQ,GAAWA,EAAQ,GAAK3E,EAASM,KACxCqE,EAAQhB,QAAQ3D,EAASM,OAEhCN,EAASM,KAAO,MAElByE,EAAaA,IAA8B,KAAfJ,EAAQ,IAA4B,KAAfM,EAAQ,KAGvDH,EAEF5L,EAAOoH,KAAQN,EAASM,MAA0B,KAAlBN,EAASM,KAC3BN,EAASM,KAAOpH,EAAOoH,KACrCpH,EAAOsH,SAAYR,EAASQ,UAAkC,KAAtBR,EAASQ,SAC/BR,EAASQ,SAAWtH,EAAOsH,SAC7CtH,EAAOwH,OAASV,EAASU,OACzBxH,EAAOyH,MAAQX,EAASW,MACxBsE,EAAUN,OAEL,GAAIA,EAAQ/U,OAGZqV,IAASA,EAAU,IACxBA,EAAQE,MACRF,EAAUA,EAAQ1V,OAAOoV,GACzBzL,EAAOwH,OAASV,EAASU,OACzBxH,EAAOyH,MAAQX,EAASW,WACnB,IAAKzC,EAAuB8B,EAASU,QAAS,CAInD,GAAIwE,EACFhM,EAAOsH,SAAWtH,EAAOoH,KAAO2E,EAAQL,SAIpCQ,KAAalM,EAAOoH,MAAQpH,EAAOoH,KAAKtB,QAAQ,KAAO,IAC1C9F,EAAOoH,KAAKjF,MAAM,QAEjCnC,EAAOmH,KAAO+E,EAAWR,QACzB1L,EAAOoH,KAAOpH,EAAOsH,SAAW4E,EAAWR,SAW/C,OARA1L,EAAOwH,OAASV,EAASU,OACzBxH,EAAOyH,MAAQX,EAASW,MAEnBzC,EAAYhF,EAAO0H,WAAc1C,EAAYhF,EAAOwH,UACvDxH,EAAO2H,MAAQ3H,EAAO0H,SAAW1H,EAAO0H,SAAW,KACpC1H,EAAOwH,OAASxH,EAAOwH,OAAS,KAEjDxH,EAAO4H,KAAO5H,EAAO+G,SACd/G,EAGT,IAAK+L,EAAQrV,OAWX,OARAsJ,EAAO0H,SAAW,KAEd1H,EAAOwH,OACTxH,EAAO2H,KAAO,IAAM3H,EAAOwH,OAE3BxH,EAAO2H,KAAO,KAEhB3H,EAAO4H,KAAO5H,EAAO+G,SACd/G,EAcT,IARA,IAAI7F,EAAO4R,EAAQ5V,OAAO,GAAG,GACzBgW,GACCnM,EAAOoH,MAAQN,EAASM,MAAQ2E,EAAQrV,OAAS,KACxC,MAATyD,GAAyB,OAATA,IAA2B,KAATA,EAInCiS,EAAK,EACA5V,EAAIuV,EAAQrV,OAAQF,GAAK,EAAGA,IAEtB,OADb2D,EAAO4R,EAAQvV,IAEbuV,EAAQ5H,OAAO3N,EAAG,GACA,OAAT2D,GACT4R,EAAQ5H,OAAO3N,EAAG,GAClB4V,KACSA,IACTL,EAAQ5H,OAAO3N,EAAG,GAClB4V,KAKJ,IAAKP,IAAeC,EAClB,KAAOM,IAAMA,EACXL,EAAQtB,QAAQ,OAIhBoB,GAA6B,KAAfE,EAAQ,IACpBA,EAAQ,IAA+B,MAAzBA,EAAQ,GAAGf,OAAO,IACpCe,EAAQtB,QAAQ,IAGd0B,GAAsD,MAAjCJ,EAAQ1J,KAAK,KAAK0D,QAAQ,IACjDgG,EAAQvW,KAAK,IAGf,IAUM0W,EAVFG,EAA4B,KAAfN,EAAQ,IACpBA,EAAQ,IAA+B,MAAzBA,EAAQ,GAAGf,OAAO,GAGjCgB,IACFhM,EAAOsH,SAAWtH,EAAOoH,KAAOiF,EAAa,GACbN,EAAQrV,OAASqV,EAAQL,QAAU,IAI/DQ,KAAalM,EAAOoH,MAAQpH,EAAOoH,KAAKtB,QAAQ,KAAO,IAC1C9F,EAAOoH,KAAKjF,MAAM,QAEjCnC,EAAOmH,KAAO+E,EAAWR,QACzB1L,EAAOoH,KAAOpH,EAAOsH,SAAW4E,EAAWR,UAyB/C,OArBAG,EAAaA,GAAe7L,EAAOoH,MAAQ2E,EAAQrV,UAEhC2V,GACjBN,EAAQtB,QAAQ,IAGbsB,EAAQrV,OAIXsJ,EAAO0H,SAAWqE,EAAQ1J,KAAK,MAH/BrC,EAAO0H,SAAW,KAClB1H,EAAO2H,KAAO,MAMX3C,EAAYhF,EAAO0H,WAAc1C,EAAYhF,EAAOwH,UACvDxH,EAAO2H,MAAQ3H,EAAO0H,SAAW1H,EAAO0H,SAAW,KACpC1H,EAAOwH,OAASxH,EAAOwH,OAAS,KAEjDxH,EAAOmH,KAAOL,EAASK,MAAQnH,EAAOmH,KACtCnH,EAAOkH,QAAUlH,EAAOkH,SAAWJ,EAASI,QAC5ClH,EAAO4H,KAAO5H,EAAO+G,SACd/G,GAGTgH,EAAIxS,UAAUyV,UAAY,WACxB,IAAI7C,EAAOpS,KAAKoS,KACZC,EAAOS,EAAY2B,KAAKrC,GACxBC,IAEW,OADbA,EAAOA,EAAK,MAEVrS,KAAKqS,KAAOA,EAAKtB,OAAO,IAE1BqB,EAAOA,EAAKrB,OAAO,EAAGqB,EAAK1Q,OAAS2Q,EAAK3Q,SAEvC0Q,IAAMpS,KAAKsS,SAAWF,QCzoBf2B,GAAM,CACfxC,MAAO+F,EACPvF,OAAQwF,EACR3F,QAAS4F,GClFb,SAASC,GAAW9E,GAEhB,GAAoB,iBAATA,EAEP,MAAM,IAAIvS,UAAU,mCAAmCsX,KAAKjG,UAAUkB,IAI9E,SAASgF,GAAgB5D,GAIrB,OAFWA,EAAI5G,MAAM,KAAK,GAEhBA,MAAM,KAAK,GA0IlB,IAAMwF,GAAa,CAKtBiF,QAAA,SAAQjF,GAAgB,OAvIKkF,EAuImB,KAvILzK,EAuIW,IAAZuF,EArI/BvF,QAAQ,IAAI0K,OAAoBD,EAL7BzK,QAAQ,sBAAuB,QAKK,KAAMA,GAF5D,IAAiCyK,EAAczK,GA4I3C2K,MAAA,SAAMpF,GAAgB,MAAO,WAAa9C,KAAK7P,KAAK4X,QAAQjF,KAK5DqF,UAAA,SAAUrF,GAGN,MAAO,yIACF9C,KAAK8C,IAOdsF,YAAA,SAAYtF,GAAgB,MAAO,aAAe9C,KAAK7P,KAAK4X,QAAQjF,KAKpEuF,YAAA,SAAYvF,GAER8E,GAAW9E,GACXA,EAAO3S,KAAK4X,QAAQjF,GAEpB,IAAIV,EAAW,GAETkG,EAAS,eAAiB1D,KAAK9B,GAC/ByF,EAAS,eAAiB3D,KAAK9B,GAC/B0F,EAAY,aAAe5D,KAAK9B,GAEtC,GAAIwF,GAAUC,GAAUC,EACxB,CACI,IAAMC,GAAMH,MAAAA,OAAA,EAAAA,EAAS,MAAMC,MAAAA,OAAA,EAAAA,EAAS,MAAMC,MAAAA,OAAS,EAATA,EAAY,IAEtDpG,EAAWqG,EACX3F,EAAOA,EAAKxR,MAAMmX,EAAI5W,QAG1B,OAAOuQ,GAYXsG,WAAA,SAAWxE,EAAayE,EAAwBC,GAE5C,GAAIzY,KAAKgY,UAAUjE,GAAM,OAAOA,EAEhC,IAAM2E,EAAUf,GAAgB3X,KAAK4X,QAAQY,MAAAA,EAAAA,EAAiBG,EAAQA,SAACC,QAAQC,eACzEC,EAAUnB,GAAgB3X,KAAK4X,QAAQa,MAAAA,EAAAA,EAAiBzY,KAAK+Y,SAASL,KAM5E,OAJAjB,GAAW1D,IACXA,EAAM/T,KAAK4X,QAAQ7D,IAGXiF,WAAW,KAERrG,GAAKtF,KAAKyL,EAAS/E,EAAI5S,MAAM,IAGnBnB,KAAKqX,WAAWtD,GAAOA,EAAM/T,KAAKqN,KAAKqL,EAAS3E,IASzEkF,UAAA,SAAUtG,GAKN,GAFA8E,GADA9E,EAAO3S,KAAK4X,QAAQjF,IAGA,IAAhBA,EAAKjR,OAAc,MAAO,IAE9B,IAAIuQ,EAAW,GACToF,EAAa1E,EAAKqG,WAAW,KAE/BhZ,KAAKiY,YAAYtF,KAEjBV,EAAWjS,KAAK+Y,SAASpG,GACzBA,EAAOA,EAAKxR,MAAM8Q,EAASvQ,SAG/B,IAAMwX,EAAoBvG,EAAKwG,SAAS,KAMxC,OAHAxG,EAtOR,SAA8BA,EAAcyG,GAQxC,IANA,IAIIC,EAJAC,EAAM,GACNC,EAAoB,EACpBC,GAAa,EACbC,EAAO,EAGFjY,EAAI,EAAGA,GAAKmR,EAAKjR,SAAUF,EACpC,CACI,GAAIA,EAAImR,EAAKjR,OAET2X,EAAO1G,EAAKhF,WAAWnM,OAEtB,CAAA,GAAa,KAAT6X,EAEL,MAIAA,EAAO,GAEX,GAAa,KAATA,EACJ,CACI,GAAIG,IAAchY,EAAI,GAAc,IAATiY,QAItB,GAAID,IAAchY,EAAI,GAAc,IAATiY,EAChC,CACI,GACIH,EAAI5X,OAAS,GACY,IAAtB6X,GACmC,KAAnCD,EAAI3L,WAAW2L,EAAI5X,OAAS,IACO,KAAnC4X,EAAI3L,WAAW2L,EAAI5X,OAAS,GAG/B,GAAI4X,EAAI5X,OAAS,EACjB,CACI,IAAMgY,EAAiBJ,EAAIpK,YAAY,KAEvC,GAAIwK,IAAmBJ,EAAI5X,OAAS,EACpC,EAC4B,IAApBgY,GAEAJ,EAAM,GACNC,EAAoB,GAKpBA,GADAD,EAAMA,EAAInY,MAAM,EAAGuY,IACKhY,OAAS,EAAI4X,EAAIpK,YAAY,KAEzDsK,EAAYhY,EACZiY,EAAO,EACP,eAGH,GAAmB,IAAfH,EAAI5X,QAA+B,IAAf4X,EAAI5X,OACjC,CACI4X,EAAM,GACNC,EAAoB,EACpBC,EAAYhY,EACZiY,EAAO,EACP,SAGJL,IAEIE,EAAI5X,OAAS,EACf4X,GAAO,MAEPA,EAAM,KACRC,EAAoB,QAKpBD,EAAI5X,OAAS,EAEb4X,GAAO,IAAI3G,EAAKxR,MAAMqY,EAAY,EAAGhY,GAIrC8X,EAAM3G,EAAKxR,MAAMqY,EAAY,EAAGhY,GAEpC+X,EAAoB/X,EAAIgY,EAAY,EAExCA,EAAYhY,EACZiY,EAAO,OAEO,KAATJ,IAAyB,IAAVI,IAElBA,EAIFA,GAAQ,EAIhB,OAAOH,EAiIIK,CAAqBhH,GAAM,IAEzBjR,OAAS,GAAKwX,IAAmBvG,GAAQ,KAC9C0E,EAAmB,IAAI1E,EAEpBV,EAAWU,GAQtB0E,WAAA,SAAW1E,GAKP,OAHA8E,GAAW9E,GACXA,EAAO3S,KAAK4X,QAAQjF,KAEhB3S,KAAKiY,YAAYtF,IAEdA,EAAKqG,WAAW,MAQ3B3L,KAAA,qBAIQuM,cAJkBC,EAAA,GAAAC,EAAA,EAArBA,EAAqBxX,UAAAZ,OAArBoY,IAAAD,EAAqBC,GAAAxX,EAAAwX,GAEtB,GAAwB,IAApBD,EAASnY,OACX,MAAO,IAGT,IAAK,IAAIF,EAAI,EAAGA,EAAIqY,EAASnY,SAAUF,EACvC,CACI,IAAMyO,EAAM4J,EAASrY,GAGrB,GADAiW,GAAWxH,GACPA,EAAIvO,OAAS,EAEb,QAAec,IAAXoX,EAAsBA,EAAS3J,MAEnC,CACI,IAAM8J,EAA6B,QAAnBC,EAAAH,EAASrY,EAAI,UAAM,IAAAwY,EAAAA,EAAA,GAE/Bha,KAAKia,QAAQF,GAEbH,GAAU,OAAO3J,EAIjB2J,GAAU,IAAI3J,GAK9B,YAAezN,IAAXoX,EAA+B,IAE5B5Z,KAAKiZ,UAAUW,IAO1BM,QAAA,SAAQvH,GAGJ,GADA8E,GAAW9E,GACS,IAAhBA,EAAKjR,OAAc,MAAO,IAY9B,IAVA,IAAI2X,GADJ1G,EAAO3S,KAAK4X,QAAQjF,IACJhF,WAAW,GACrBwM,EAAmB,KAATd,EACZpU,GAAO,EACPmV,GAAe,EAEbzF,EAAQ3U,KAAKkY,YAAYvF,GACzB0H,EAAW1H,EAIRnR,GAFTmR,EAAOA,EAAKxR,MAAMwT,EAAMjT,SAENA,OAAS,EAAGF,GAAK,IAAKA,EAGpC,GAAa,MADb6X,EAAO1G,EAAKhF,WAAWnM,KAGnB,IAAK4Y,EACL,CACInV,EAAMzD,EACN,YAMJ4Y,GAAe,EAMvB,OAAa,IAATnV,EAAmBkV,EAAU,IAAMna,KAAK+X,MAAMsC,GAAY1F,EAAQhC,EAAOgC,EACzEwF,GAAmB,IAARlV,EAAkB,KAE1B0P,EAAQhC,EAAKxR,MAAM,EAAG8D,IAOjC8T,SAAA,SAASpG,GAEL8E,GAAW9E,GAGX,IAAIrH,EAAO,GAQX,GAN0BA,GAJ1BqH,EAAO3S,KAAK4X,QAAQjF,IAIXqG,WAAW,KAAa,IAGtBhZ,KAAKkY,YAAYvF,GAGxB3S,KAAK+X,MAAMpF,GACf,CAEI,IAAMjE,EAAQiE,EAAK7B,QAAQ,IAAKxF,EAAK5J,SAIjC4J,GAFW,IAAXoD,EAEOiE,EAAKxR,MAAM,EAAGuN,GAEbiE,GAEFwG,SAAS,OAAM7N,GAAQ,KAGrC,OAAOA,GAQXgP,SAAA,SAAS3H,EAAc4H,GAEnB9C,GAAW9E,GACP4H,GAAK9C,GAAW8C,GAEpB5H,EAAO3S,KAAK4X,QAAQjF,GAEpB,IAGInR,EAHAwD,EAAQ,EACRC,GAAO,EACPmV,GAAe,EAGnB,QAAY5X,IAAR+X,GAAqBA,EAAI7Y,OAAS,GAAK6Y,EAAI7Y,QAAUiR,EAAKjR,OAC9D,CACI,GAAI6Y,EAAI7Y,SAAWiR,EAAKjR,QAAU6Y,IAAQ5H,EAAM,MAAO,GACvD,IAAI6H,EAASD,EAAI7Y,OAAS,EACtB+Y,GAAoB,EAExB,IAAKjZ,EAAImR,EAAKjR,OAAS,EAAGF,GAAK,IAAKA,EACpC,CACI,IAAM6X,EAAO1G,EAAKhF,WAAWnM,GAE7B,GAAa,KAAT6X,GAIA,IAAKe,EACL,CACIpV,EAAQxD,EAAI,EACZ,YAKsB,IAAtBiZ,IAIAL,GAAe,EACfK,EAAmBjZ,EAAI,GAEvBgZ,GAAU,IAGNnB,IAASkB,EAAI5M,WAAW6M,IAEN,KAAZA,IAIFvV,EAAMzD,IAOVgZ,GAAU,EACVvV,EAAMwV,IAQtB,OAFIzV,IAAUC,EAAKA,EAAMwV,GAAoC,IAATxV,IAAYA,EAAM0N,EAAKjR,QAEpEiR,EAAKxR,MAAM6D,EAAOC,GAE7B,IAAKzD,EAAImR,EAAKjR,OAAS,EAAGF,GAAK,IAAKA,EAEhC,GAA2B,KAAvBmR,EAAKhF,WAAWnM,IAIhB,IAAK4Y,EACL,CACIpV,EAAQxD,EAAI,EACZ,YAGU,IAATyD,IAILmV,GAAe,EACfnV,EAAMzD,EAAI,GAIlB,OAAa,IAATyD,EAAmB,GAEhB0N,EAAKxR,MAAM6D,EAAOC,IAS7BgV,QAAA,SAAQtH,GAEJ8E,GAAW9E,GAWX,IARA,IAAI+H,GAAY,EACZC,EAAY,EACZ1V,GAAO,EACPmV,GAAe,EAGfQ,EAAc,EAETpZ,GAVTmR,EAAO3S,KAAK4X,QAAQjF,IAUFjR,OAAS,EAAGF,GAAK,IAAKA,EACxC,CACI,IAAM6X,EAAO1G,EAAKhF,WAAWnM,GAE7B,GAAa,KAAT6X,GAWS,IAATpU,IAIAmV,GAAe,EACfnV,EAAMzD,EAAI,GAED,KAAT6X,GAGkB,IAAdqB,EAAiBA,EAAWlZ,EACP,IAAhBoZ,IAAmBA,EAAc,IAEvB,IAAdF,IAILE,GAAe,QAxBf,IAAKR,EACL,CACIO,EAAYnZ,EAAI,EAChB,OAyBZ,OACkB,IAAdkZ,IAA4B,IAATzV,GAEA,IAAhB2V,GAGgB,IAAhBA,GAAqBF,IAAazV,EAAM,GAAKyV,IAAaC,EAAY,EAGlE,GAGJhI,EAAKxR,MAAMuZ,EAAUzV,IAOhCsM,MAAA,SAAMoB,GAEF8E,GAAW9E,GAEX,IAAMkI,EAAM,CAAEvP,KAAM,GAAIwP,IAAK,GAAI7O,KAAM,GAAIsO,IAAK,GAAIvZ,KAAM,IAE1D,GAAoB,IAAhB2R,EAAKjR,OAAc,OAAOmZ,EAG9B,IAEI7V,EAFAqU,GAFJ1G,EAAO3S,KAAK4X,QAAQjF,IAEJhF,WAAW,GACrB0J,EAAarX,KAAKqX,WAAW1E,GAInCkI,EAAIvP,KAAOtL,KAAK+Y,SAASpG,GAIrB3N,EAFAqS,GAAcrX,KAAKiY,YAAYtF,GAEvB,EAIA,EAaZ,IAXA,IAAI+H,GAAY,EACZC,EAAY,EACZ1V,GAAO,EACPmV,GAAe,EACf5Y,EAAImR,EAAKjR,OAAS,EAIlBkZ,EAAc,EAGXpZ,GAAKwD,IAASxD,EAGjB,GAAa,MADb6X,EAAO1G,EAAKhF,WAAWnM,KAYV,IAATyD,IAIAmV,GAAe,EACfnV,EAAMzD,EAAI,GAED,KAAT6X,GAGkB,IAAdqB,EAAiBA,EAAWlZ,EACP,IAAhBoZ,IAAmBA,EAAc,IAEvB,IAAdF,IAILE,GAAe,QAxBf,IAAKR,EACL,CACIO,EAAYnZ,EAAI,EAChB,MA0DZ,OAhCkB,IAAdkZ,IAA4B,IAATzV,GAEA,IAAhB2V,GAGgB,IAAhBA,GAAqBF,IAAazV,EAAM,GAAKyV,IAAaC,EAAY,GAG5D,IAAT1V,IAEmC4V,EAAI5O,KAAO4O,EAAI7Z,KAAhC,IAAd2Z,GAAmBtD,EAAkC1E,EAAKxR,MAAM,EAAG8D,GAC5C0N,EAAKxR,MAAMwZ,EAAW1V,KAKnC,IAAd0V,GAAmBtD,GAEnBwD,EAAI7Z,KAAO2R,EAAKxR,MAAM,EAAGuZ,GACzBG,EAAI5O,KAAO0G,EAAKxR,MAAM,EAAG8D,KAIzB4V,EAAI7Z,KAAO2R,EAAKxR,MAAMwZ,EAAWD,GACjCG,EAAI5O,KAAO0G,EAAKxR,MAAMwZ,EAAW1V,IAErC4V,EAAIN,IAAM5H,EAAKxR,MAAMuZ,EAAUzV,IAGnC4V,EAAIC,IAAM9a,KAAKka,QAAQvH,GAGhBkI,GAGXxK,IAAK,IACL0K,UAAW,KC5pBfpC,EAAAA,SAASqC,cAAgB,eA2BjBrC,EAAAA,SAACsC,kCAAmC,ECpC5C,ICAIC,GDAAC,IAAY,i5FEwCH,IAAAC,GAjCb,WAKI,IAHA,IAAMC,EAAK,GACLC,EAAM,GAEH9Z,EAAI,EAAGA,EAAI,GAAIA,IAEpB6Z,EAAG7Z,GAAKA,EACR8Z,EAAI9Z,GAAKA,EAGb6Z,EAAGE,EAAAA,YAAYC,YAAcD,EAAAA,YAAYE,OACzCJ,EAAGE,EAAAA,YAAYG,SAAWH,EAAAA,YAAYI,IACtCN,EAAGE,EAAAA,YAAYK,YAAcL,EAAAA,YAAYM,OAEzCP,EAAIC,EAAAA,YAAYE,QAAUF,EAAAA,YAAYC,WACtCF,EAAIC,EAAAA,YAAYI,KAAOJ,EAAAA,YAAYG,QACnCJ,EAAIC,EAAAA,YAAYM,QAAUN,EAAAA,YAAYK,WAEtC,IAAM7O,EAAoB,GAK1B,OAHAA,EAAMvM,KAAK8a,GACXvO,EAAMvM,KAAK6a,GAEJtO,EASyB+O,GCxC9B,SAAUC,GACZhP,GAGA,GAAgC,IAA5BA,EAAMiP,kBAEN,OAAIjP,aAAiBkP,aAEV,eAEFlP,aAAiBmP,YAEf,cAGJ,aAEN,GAAgC,IAA5BnP,EAAMiP,mBAEX,GAAIjP,aAAiBoP,YAEjB,MAAO,mBAGV,GAAgC,IAA5BpP,EAAMiP,mBAEPjP,aAAiBqP,WAEjB,MAAO,aAKf,OAAO,KChCX,IAAMtP,GAAM,CAAEmP,aAAcA,aAAcC,YAAaA,YAAaG,WAAYA,WAAYD,WAAYA,YCHxG,IAAIE,GAAU,ECGd,IAAMC,GAA0B,GCMnB,IASAC,GAAyCjd,OAAOqB,OAAO,MASvD6b,GAAiDld,OAAOqB,OAAO,MCpB5E,IAAA8b,GAAA,WAmBI,SAAAA,EAAYC,EAAeC,EAAgBC,GAEvC7c,KAAK8c,OAASnE,EAAAA,SAASC,QAAQmE,eAE/B/c,KAAKF,QAAUE,KAAK8c,OAAOE,WAAW,MAEtChd,KAAK6c,WAAaA,GAAclE,EAAAA,SAASsE,WAEzCjd,KAAKkd,OAAOP,EAAOC,GA0D3B,OAnDIF,EAAAld,UAAA2d,MAAA,WAEInd,KAAKF,QAAQsd,aAAa,EAAG,EAAG,EAAG,EAAG,EAAG,GACzCpd,KAAKF,QAAQud,UAAU,EAAG,EAAGrd,KAAK8c,OAAOH,MAAO3c,KAAK8c,OAAOF,SAQhEF,EAAAld,UAAA0d,OAAA,SAAOI,EAAsBC,GAEzBvd,KAAK8c,OAAOH,MAAQ9X,KAAK2Y,MAAMF,EAAetd,KAAK6c,YACnD7c,KAAK8c,OAAOF,OAAS/X,KAAK2Y,MAAMD,EAAgBvd,KAAK6c,aAIzDH,EAAAld,UAAAie,QAAA,WAEIzd,KAAKF,QAAU,KACfE,KAAK8c,OAAS,MAOlBvd,OAAAme,eAAIhB,EAAKld,UAAA,QAAA,CAATme,IAAA,WAEI,OAAO3d,KAAK8c,OAAOH,OAGvBiB,IAAA,SAAUC,GAEN7d,KAAK8c,OAAOH,MAAQ9X,KAAK2Y,MAAMK,oCAOnCte,OAAAme,eAAIhB,EAAMld,UAAA,SAAA,CAAVme,IAAA,WAEI,OAAO3d,KAAK8c,OAAOF,QAGvBgB,IAAA,SAAWC,GAEP7d,KAAK8c,OAAOF,OAAS/X,KAAK2Y,MAAMK,oCAEvCnB,KCpFY,ICNToB,GDMSC,GAAW,wQFC8B,oDA8ClD,IAAIhS,EAEJ,IAAKA,KAAOyQ,UAEDA,GAAazQ,GAExB,IAAKA,KAAO0Q,UAEDA,GAAiB1Q,uBLXhB,SAAiBiS,EAAmBC,GAEhD,OAAO7C,GAAqB6C,EAAgB,EAAI,GAAGD,4BS9CvC,SAAsBE,EAAcC,QAAA,IAAAA,IAAAA,EAA2C,MAG3F,IAAMC,EAAsB,EAAPF,EAIrB,IAFAC,EAAYA,GAAa,IAAIhC,YAAYiC,IAE3B1c,SAAW0c,EAErB,MAAM,IAAIC,MAAM,uCAAuCF,EAAUzc,OAAM,iBAAiB0c,GAI5F,IAAK,IAAI5c,EAAI,EAAGkB,EAAI,EAAGlB,EAAI4c,EAAc5c,GAAK,EAAGkB,GAAK,EAElDyb,EAAU3c,EAAI,GAAKkB,EAAI,EACvByb,EAAU3c,EAAI,GAAKkB,EAAI,EACvByb,EAAU3c,EAAI,GAAKkB,EAAI,EACvByb,EAAU3c,EAAI,GAAKkB,EAAI,EACvByb,EAAU3c,EAAI,GAAKkB,EAAI,EACvByb,EAAU3c,EAAI,GAAKkB,EAAI,EAG3B,OAAOyb,sBCsBL,SAA2BG,GAE7B,IAAMC,EAAeR,GAAStJ,KAAK6J,GAEnC,GAAIC,EAEA,MAAO,CACHC,UAAWD,EAAa,GAAKA,EAAa,GAAGxO,mBAAgBvN,EAC7Dic,QAASF,EAAa,GAAKA,EAAa,GAAGxO,mBAAgBvN,EAC3Dkc,QAASH,EAAa,GAAKA,EAAa,GAAGxO,mBAAgBvN,EAC3Dmc,SAAUJ,EAAa,GAAKA,EAAa,GAAGxO,mBAAgBvN,EAC5DY,KAAMmb,EAAa,4BNhDH7O,EAAiBkP,EAAiBC,GAG1D,QAH0D,IAAAA,IAAAA,EAAe,IAGrEtC,GAASqC,GAAb,CAMA,IAAIE,GAAQ,IAAIT,OAAQS,WAGH,IAAVA,EAEPC,QAAQC,KAAK,+BAAmCJ,EAAO,uBAAuBlP,IAK9EoP,EAAQA,EAAM3R,MAAM,MAAMgC,OAAO0P,GAAaxR,KAAK,MAE/C0R,QAAQE,gBAERF,QAAQE,eACJ,qCACA,mCACA,sDACGL,EAA8B,uBAAAlP,GAErCqP,QAAQC,KAAKF,GACbC,QAAQG,aAIRH,QAAQC,KAAK,+BAAmCJ,EAAO,uBAAuBlP,GAC9EqP,QAAQC,KAAKF,KAKrBvC,GAASqC,IAAW,qCCpBpB,IAAI7S,EAEJ,IAAKA,KAAOyQ,GAERA,GAAazQ,GAAK0R,UAEtB,IAAK1R,KAAO0Q,GAERA,GAAiB1Q,GAAK0R,kCG9Bd,SAAqB1J,EAAaoL,GAG9C,QAH8C,IAAAA,IAAAA,EAAgBC,WAAWC,UAG5C,IAAzBtL,EAAIjD,QAAQ,SAEZ,MAAO,GAIXqO,EAAMA,GAAOC,WAAWC,SAEnBvB,KAEDA,GAAawB,SAASC,cAAc,MAMxCzB,GAAWlL,KAAOmB,EAClB,IAAMyL,EAAYC,GAAKlO,MAAMuM,GAAWlL,MAElC8M,GAAaF,EAAUnN,MAAqB,KAAb8M,EAAI9M,MAAiBmN,EAAUnN,OAAS8M,EAAI9M,KAGjF,OAAImN,EAAUlN,WAAa6M,EAAI7M,UAAaoN,GAAYF,EAAUvN,WAAakN,EAAIlN,SAK5E,GAHI,gEG9BC,SAAmB8B,EAAa4L,GAE5C,IAAM9C,EAAalE,EAAQA,SAACqC,cAAcvG,KAAKV,GAE/C,OAAI8I,EAEO+C,WAAW/C,EAAW,SAGTra,IAAjBmd,EAA6BA,EAAe,aCRvC,SAAQE,EAAarR,GAMjC,YANiC,IAAAA,IAAAA,EAAsC,IAEvEA,EAAI,IAAOqR,GAAO,GAAM,KAAQ,IAChCrR,EAAI,IAAOqR,GAAO,EAAK,KAAQ,IAC/BrR,EAAI,IAAY,IAANqR,GAAc,IAEjBrR,gBAYL,SAAqBqR,GAEvB,IAAIC,EAAYD,EAAIE,SAAS,IAI7B,MAAO,KAFPD,EAAY,SAASE,UAAU,EAAG,EAAIF,EAAUpe,QAAUoe,4BV3B9C,SAAsBG,EAAuBC,GAMzD,IAJA,IAAIC,EAAU,EACVC,EAAS,EACPC,EAAsC,GAEnC7e,EAAI,EAAGA,EAAIye,EAAOve,OAAQF,IAE/B4e,GAAUF,EAAM1e,GAChB2e,GAAWF,EAAOze,GAAGE,OAGzB,IAAM4e,EAAS,IAAIC,YAAsB,EAAVJ,GAE3B3R,EAAM,KACNgS,EAAe,EAEnB,IAAShf,EAAI,EAAGA,EAAIye,EAAOve,OAAQF,IACnC,CACI,IAAM0c,EAAOgC,EAAM1e,GACbuL,EAAQkT,EAAOze,GAMfoL,EAAOmP,GAAchP,GAEtBsT,EAAMzT,KAEPyT,EAAMzT,GAAQ,IAAIE,GAAIF,GAAM0T,IAGhC9R,EAAM6R,EAAMzT,GAEZ,IAAK,IAAIlK,EAAI,EAAGA,EAAIqK,EAAMrL,OAAQgB,IAClC,CAII8L,GAHqB9L,EAAIwb,EAAO,GAAKkC,EAAUI,EACjC9d,EAAIwb,GAEQnR,EAAMrK,GAGpC8d,GAAgBtC,EAGpB,OAAO,IAAIjC,aAAaqE,aWxBtB,SAAiB1P,GAEnB,QAASA,EAAKA,EAAI,IAAUA,kCd0B5B,YA7CyB,IAAdsK,KAEPA,GAAY,WAER,IAAMuF,EAAiB,CACnBC,SAAS,EACTC,6BAA8BhI,EAAQA,SAACsC,kCAG3C,IAEI,IAAKtC,EAAQA,SAACC,QAAQgI,2BAElB,OAAO,EAGX,IAAM9D,EAASnE,EAAAA,SAASC,QAAQmE,eAC5B8D,EACA/D,EAAOE,WAAW,QAASyD,IACxB3D,EAAOE,WAAW,qBAAsByD,GAGzCK,KAAaD,IAAMA,EAAGE,uBAAuBL,SAEnD,GAAIG,EACJ,CACI,IAAMG,EAAcH,EAAGI,aAAa,sBAEhCD,GAEAA,EAAYA,cAMpB,OAFAH,EAAK,KAEEC,EAEX,MAAO3a,GAEH,OAAO,GAtCH,IA2CT+U,WchBL,SAAetK,GAEjB,IAAIlH,GAAKkH,EAAI,MAAS,EAAI,IAAM,EAI5B8F,IAFJ9F,KAAOlH,GAEU,IAAO,EAAI,IAAM,EAQlC,OANcA,GAAKgN,EAELhN,GADdgN,IADA9F,KAAO8F,GACM,GAAM,EAAI,IAAM,GAGfhN,GADdgN,IADA9F,KAAO8F,GACM,EAAM,EAAI,IAAM,IAC7B9F,KAAO8F,IAEU,cA9Cf,SAAmB9F,GAUrB,OARAA,GAAW,IAANA,EAAU,EAAI,IACjBA,EACFA,GAAKA,IAAM,EACXA,GAAKA,IAAM,EACXA,GAAKA,IAAM,EACXA,GAAKA,IAAM,GACXA,GAAKA,IAAM,IAEA,yDbgDT,SACFsQ,EACAC,EACA3S,EACA4S,GAkBA,OAfA5S,EAAMA,GAAO,IAAIyN,aAAa,GAC1BmF,QAA+B5e,IAAhB4e,GAEf5S,EAAI,GAAK0S,EAAI,GAAKC,EAClB3S,EAAI,GAAK0S,EAAI,GAAKC,EAClB3S,EAAI,GAAK0S,EAAI,GAAKC,IAIlB3S,EAAI,GAAK0S,EAAI,GACb1S,EAAI,GAAK0S,EAAI,GACb1S,EAAI,GAAK0S,EAAI,IAEjB1S,EAAI,GAAK2S,EAEF3S,qBAWK,SAAgB6S,EAAcF,GAE1C,GAAc,IAAVA,EAEA,OAAgB,IAARA,GAAe,IAAME,EAEjC,GAAc,IAAVF,EAEA,OAAO,EAEX,IAAIG,EAAMD,GAAQ,GAAM,IACpBE,EAAMF,GAAQ,EAAK,IACnBG,EAAY,IAAPH,EAMT,OAAgB,IAARF,GAAe,MAJvBG,EAAMA,EAAIH,EAAS,GAAO,IAIS,MAHnCI,EAAMA,EAAIJ,EAAS,GAAO,IAGqB,IAF/CK,EAAMA,EAAIL,EAAS,GAAO,4BAexB,SAAgCE,EAAcF,EAAe3S,EAAmB4S,GAclF,OAZA5S,EAAMA,GAAO,IAAIyN,aAAa,IAC1B,IAAOoF,GAAQ,GAAM,KAAQ,IACjC7S,EAAI,IAAO6S,GAAQ,EAAK,KAAQ,IAChC7S,EAAI,IAAa,IAAP6S,GAAe,KACrBD,QAA+B5e,IAAhB4e,KAEf5S,EAAI,IAAM2S,EACV3S,EAAI,IAAM2S,EACV3S,EAAI,IAAM2S,GAEd3S,EAAI,GAAK2S,EAEF3S,0BczIiB8J,EAAYmJ,EAAkBC,GAEtD,IACIlgB,EADEE,EAAS4W,EAAI5W,OAGnB,KAAI+f,GAAY/f,GAA0B,IAAhBggB,GAA1B,CAOA,IAAMrf,EAAMX,GAFZggB,EAAeD,EAAWC,EAAchgB,EAASA,EAAS+f,EAAWC,GAIrE,IAAKlgB,EAAIigB,EAAUjgB,EAAIa,IAAOb,EAE1B8W,EAAI9W,GAAK8W,EAAI9W,EAAIkgB,GAGrBpJ,EAAI5W,OAASW,cFkDX,SAAkB6e,GAEpB,OAAmB,IAATA,EAAI,IAAa,KAAiB,IAATA,EAAI,IAAa,IAAe,IAATA,EAAI,GAAW,edvDvE,SAAmBtU,SAErB,IAAIuO,GAAJ,CAKA,GAAIxC,WAASC,QAAQ+I,eAAeC,UAAU7R,cAAce,QAAQ,WAAa,EACjF,CACI,IAAM1O,EAAO,CACT,iCAAqCwK,EAA4D,yDACjG,sCACA,sCACA,sDACA,sCACA,sCACA,sCACA,mDACA,mDACA,qDAGJoN,EAAAoF,WAAWL,SAAQ8C,IAAOpf,MAAAuX,EAAA5X,QAErBgd,WAAWL,SAEhBK,WAAWL,QAAQ8C,IAAI,mBAAuBjV,EAA+B,6BAGjFuO,IAAY,WiB9CV,SAAenT,GAEjB,OAAU,IAANA,EAAgB,EAEbA,EAAI,GAAK,EAAI,0BjBCpBmT,IAAY,gBcyCV,SAAqBlO,GAYvB,MAVsB,iBAAXA,GAIW,OAFlBA,EAAU6U,GAA0C7U,EAAO8C,gBAAkB9C,GAElE,KAEPA,EAASA,EAAO9L,MAAM,IAIvB4gB,SAAS9U,EAAQ,kBIlDtB,SAAqB6P,GAIvB,IAiBItb,EACAmC,EACAC,EAnBA+Y,EAAQG,EAAOH,MACfC,EAASE,EAAOF,OAEd9c,EAAUgd,EAAOE,WAAW,KAAM,CACpCgF,oBAAoB,IAGlBC,EADYniB,EAAQoiB,aAAa,EAAG,EAAGvF,EAAOC,GAC3BxZ,KACnBf,EAAM4f,EAAOvgB,OAEbygB,EAAe,CACjBC,IAAK,KACLC,KAAM,KACNC,MAAO,KACPC,OAAQ,MAERnf,EAAO,KAKX,IAAK5B,EAAI,EAAGA,EAAIa,EAAKb,GAAK,EAEA,IAAlBygB,EAAOzgB,EAAI,KAEXmC,EAAKnC,EAAI,EAAKmb,EACd/Y,KAAQpC,EAAI,EAAKmb,GAEC,OAAdwF,EAAMC,MAEND,EAAMC,IAAMxe,IAGG,OAAfue,EAAME,MAID1e,EAAIwe,EAAME,QAFfF,EAAME,KAAO1e,IAOG,OAAhBwe,EAAMG,OAIDH,EAAMG,MAAQ3e,KAFnBwe,EAAMG,MAAQ3e,EAAI,IAOD,OAAjBwe,EAAMI,QAIDJ,EAAMI,OAAS3e,KAFpBue,EAAMI,OAAS3e,IAgB3B,OAPkB,OAAdue,EAAMC,MAENzF,EAAQwF,EAAMG,MAAQH,EAAME,KAC5BzF,EAASuF,EAAMI,OAASJ,EAAMC,IAAM,EACpChf,EAAOtD,EAAQoiB,aAAaC,EAAME,KAAMF,EAAMC,IAAKzF,EAAOC,IAGvD,CACHA,OAAMA,EACND,MAAKA,EACLvZ,KAAIA,qBbjFR,QAASkZ"}