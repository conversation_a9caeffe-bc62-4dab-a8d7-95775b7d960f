{"name": "@pixi/text-bitmap", "version": "6.5.10", "main": "dist/cjs/text-bitmap.js", "module": "dist/esm/text-bitmap.mjs", "bundle": "dist/browser/text-bitmap.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/text-bitmap.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/text-bitmap.js"}}}, "description": "Text via bitmap fonts", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/constants": "6.5.10", "@pixi/core": "6.5.10", "@pixi/display": "6.5.10", "@pixi/loaders": "6.5.10", "@pixi/math": "6.5.10", "@pixi/mesh": "6.5.10", "@pixi/settings": "6.5.10", "@pixi/text": "6.5.10", "@pixi/utils": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}