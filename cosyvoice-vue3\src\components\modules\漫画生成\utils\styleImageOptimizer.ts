/**
 * 🎨 风格图片优化器
 * 专门解决StyleConfigPanel预设风格图片导致的内存爆炸问题
 */

// 导入轻量级占位符CSS样式
import './lightweightPlaceholder.css';

export class StyleImageOptimizer {
  private static instance: StyleImageOptimizer;
  private processedImages = new Set<string>();
  private isOptimizing = false;

  private constructor() {}

  public static getInstance(): StyleImageOptimizer {
    if (!StyleImageOptimizer.instance) {
      StyleImageOptimizer.instance = new StyleImageOptimizer();
    }
    return StyleImageOptimizer.instance;
  }

  /**
   * 🚀 优化预设风格图片加载
   */
  public async optimizeStyleImages(): Promise<void> {
    if (this.isOptimizing) {
      console.log('⚠️ 风格图片优化正在进行中');
      return;
    }

    this.isOptimizing = true;
    console.log('🎨 开始优化预设风格图片...');

    try {
      // 1. 限制同时加载的图片数量
      await this.limitConcurrentStyleImages();

      // 2. 压缩大尺寸预设图片
      await this.compressStyleImages();

      // 3. 实施懒加载策略
      this.implementLazyLoading();

      console.log('✅ 风格图片优化完成');
    } catch (error) {
      console.error('❌ 风格图片优化失败:', error);
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * 🚦 限制同时加载的风格图片数量
   */
  private async limitConcurrentStyleImages(): Promise<void> {
    const styleImages = document.querySelectorAll('.style-preset-image img, .style-config img');
    const maxConcurrent = 3; // 最多同时加载3张风格图片
    
    if (styleImages.length > maxConcurrent) {
      console.log(`🚦 检测到${styleImages.length}张风格图片，限制为${maxConcurrent}张同时加载`);
      
      styleImages.forEach((img, index) => {
        if (index >= maxConcurrent) {
          const imgElement = img as HTMLImageElement;
          const originalSrc = imgElement.src;
          
          // 暂时替换为CSS占位符（避免Base64内存占用）
          imgElement.src = ''; // 清空src以释放内存
          imgElement.className += ' ' + this.createPlaceholderClass();
          imgElement.setAttribute('data-original-src', originalSrc);
          imgElement.setAttribute('data-lazy-load', 'true');
          
          // 延迟加载
          setTimeout(() => {
            this.loadImageWhenVisible(imgElement);
          }, index * 300); // 每张图片延迟300ms
        }
      });
    }
  }

  /**
   * 🗜️ 压缩风格图片
   */
  private async compressStyleImages(): Promise<void> {
    const images = document.querySelectorAll('.style-preset-image img, .style-config img') as NodeListOf<HTMLImageElement>;
    let compressedCount = 0;

    for (const img of images) {
      if (img.naturalWidth > 400 || img.naturalHeight > 400) {
        try {
          const compressedSrc = await this.compressImageToSize(img, 400, 400);
          if (compressedSrc && compressedSrc !== img.src) {
            img.src = compressedSrc;
            compressedCount++;
          }
        } catch (error) {
          console.warn('⚠️ 图片压缩失败:', error);
        }
      }
    }

    if (compressedCount > 0) {
      console.log(`🗜️ 压缩了${compressedCount}张风格图片`);
    }
  }

  /**
   * 👁️ 实施懒加载策略
   */
  private implementLazyLoading(): void {
    const lazyImages = document.querySelectorAll('img[data-lazy-load="true"]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          this.loadImageWhenVisible(img);
          observer.unobserve(img);
        }
      });
    }, {
      rootMargin: '50px' // 提前50px开始加载
    });

    lazyImages.forEach(img => observer.observe(img));
  }

  /**
   * 🖼️ 当图片可见时加载
   */
  private loadImageWhenVisible(img: HTMLImageElement): void {
    const originalSrc = img.getAttribute('data-original-src');
    if (originalSrc && !this.processedImages.has(originalSrc)) {
      this.processedImages.add(originalSrc);
      
      // 创建新的Image对象进行预加载
      const newImg = new Image();
      newImg.onload = () => {
        // 压缩后再设置
        this.compressImageToSize(newImg, 400, 400).then(compressedSrc => {
          img.src = compressedSrc || originalSrc;
          img.removeAttribute('data-lazy-load');
          img.removeAttribute('data-original-src');
        });
      };
      newImg.onerror = () => {
        console.warn('⚠️ 风格图片加载失败:', originalSrc);
        img.src = ''; // 清空src以释放内存
        img.className += ' ' + this.createErrorPlaceholderClass();
      };
      newImg.src = originalSrc;
    }
  }

  /**
   * 🗜️ 压缩图片到指定尺寸
   */
  private async compressImageToSize(img: HTMLImageElement, maxWidth: number, maxHeight: number): Promise<string | null> {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return null;

      // 计算缩放比例
      const scale = Math.min(maxWidth / img.naturalWidth, maxHeight / img.naturalHeight, 1);
      canvas.width = img.naturalWidth * scale;
      canvas.height = img.naturalHeight * scale;

      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // 转换为压缩后的数据
      return canvas.toDataURL('image/jpeg', 0.8); // 80%质量
    } catch (error) {
      console.warn('⚠️ 图片压缩失败:', error);
      return null;
    }
  }

  /**
   * 🖼️ 创建轻量级占位符CSS类（移除Base64 SVG以减少内存占用）
   */
  private createPlaceholderClass(): string {
    // 返回CSS类名而不是Base64数据，大幅减少内存使用
    return 'style-image-placeholder';
  }

  /**
   * ❌ 创建错误占位符CSS类（移除Base64 SVG以减少内存占用）
   */
  private createErrorPlaceholderClass(): string {
    // 返回CSS类名而不是Base64数据，大幅减少内存使用
    return 'style-image-error';
  }

  /**
   * 🧹 清理已处理的图片记录
   */
  public clearProcessedImages(): void {
    this.processedImages.clear();
    console.log('🧹 风格图片处理记录已清理');
  }

  /**
   * 📊 获取优化统计
   */
  public getOptimizationStats(): {
    processedCount: number;
    isOptimizing: boolean;
  } {
    return {
      processedCount: this.processedImages.size,
      isOptimizing: this.isOptimizing
    };
  }
}

// 导出单例实例
export const styleImageOptimizer = StyleImageOptimizer.getInstance();

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).styleImageOptimizer = styleImageOptimizer;
  (window as any).optimizeStyleImages = () => styleImageOptimizer.optimizeStyleImages();
}