{"Version": 3, "Meta": {"PhysicsSettingCount": 80, "TotalInputCount": 203, "TotalOutputCount": 248, "VertexCount": 323, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "Body X"}, {"Id": "PhysicsSetting2", "Name": "Body Y"}, {"Id": "PhysicsSetting3", "Name": "Body Z"}, {"Id": "PhysicsSetting4", "Name": "<PERSON><PERSON> Flicker"}, {"Id": "PhysicsSetting5", "Name": "Hip Sway"}, {"Id": "PhysicsSetting6", "Name": "Shoulder <PERSON><PERSON>"}, {"Id": "PhysicsSetting7", "Name": "Arm L"}, {"Id": "PhysicsSetting8", "Name": "Arm R"}, {"Id": "PhysicsSetting9", "Name": "<PERSON><PERSON>"}, {"Id": "PhysicsSetting10", "Name": "<PERSON><PERSON>"}, {"Id": "PhysicsSetting11", "Name": "Body Position X"}, {"Id": "PhysicsSetting12", "Name": "Mid Bang X"}, {"Id": "PhysicsSetting13", "Name": "<PERSON>"}, {"Id": "PhysicsSetting14", "Name": "<PERSON> (stronger)"}, {"Id": "PhysicsSetting15", "Name": "<PERSON>"}, {"Id": "PhysicsSetting16", "Name": "<PERSON> (stronger)"}, {"Id": "PhysicsSetting17", "Name": "<PERSON> (weaker)"}, {"Id": "PhysicsSetting18", "Name": "<PERSON> (weaker)"}, {"Id": "PhysicsSetting19", "Name": "Frame Hair L"}, {"Id": "PhysicsSetting20", "Name": "Frame Hair R"}, {"Id": "PhysicsSetting21", "Name": "Back Hair L"}, {"Id": "PhysicsSetting22", "Name": "Twintail L"}, {"Id": "PhysicsSetting23", "Name": "Twintail L(2)"}, {"Id": "PhysicsSetting24", "Name": "Twintail R"}, {"Id": "PhysicsSetting25", "Name": "Twintail R(2)"}, {"Id": "PhysicsSetting26", "Name": "Back Hair R"}, {"Id": "PhysicsSetting27", "Name": "Back Hair (front) L"}, {"Id": "PhysicsSetting28", "Name": "<PERSON> Hair (front) R"}, {"Id": "PhysicsSetting29", "Name": "Small Strands"}, {"Id": "PhysicsSetting30", "Name": "<PERSON><PERSON>"}, {"Id": "PhysicsSetting31", "Name": "Hair Bow L"}, {"Id": "PhysicsSetting32", "Name": "Bow L"}, {"Id": "PhysicsSetting33", "Name": "Bow R"}, {"Id": "PhysicsSetting34", "Name": "Hair Bow R"}, {"Id": "PhysicsSetting35", "Name": "Headband Dangly"}, {"Id": "PhysicsSetting36", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "PhysicsSetting37", "Name": "Ponytail L"}, {"Id": "PhysicsSetting38", "Name": "<PERSON> Ears"}, {"Id": "PhysicsSetting39", "Name": "<PERSON>"}, {"Id": "PhysicsSetting40", "Name": "Cat Pupils"}, {"Id": "PhysicsSetting41", "Name": "Pleading Shines"}, {"Id": "PhysicsSetting42", "Name": "Eyeshine Sparkle"}, {"Id": "PhysicsSetting43", "Name": "Starry Eye"}, {"Id": "PhysicsSetting44", "Name": "Bell Choker"}, {"Id": "PhysicsSetting45", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "PhysicsSetting46", "Name": "Sprout"}, {"Id": "PhysicsSetting47", "Name": "Fingers"}, {"Id": "PhysicsSetting48", "Name": "Fingers(2)"}, {"Id": "PhysicsSetting49", "Name": "Skirt X"}, {"Id": "PhysicsSetting50", "Name": "Skirt X(2)"}, {"Id": "PhysicsSetting51", "Name": "Skirt Y"}, {"Id": "PhysicsSetting52", "Name": "Ponytail Skinning L"}, {"Id": "PhysicsSetting53", "Name": "Ponytail Skinning R"}, {"Id": "PhysicsSetting54", "Name": "Earring L"}, {"Id": "PhysicsSetting55", "Name": "Earring R"}, {"Id": "PhysicsSetting56", "Name": "Shoulder Ruffles"}, {"Id": "PhysicsSetting57", "Name": "Shoulder Ruffles 2"}, {"Id": "PhysicsSetting58", "Name": "Wings"}, {"Id": "PhysicsSetting59", "Name": "Necklace"}, {"Id": "PhysicsSetting60", "Name": "Wave Arm"}, {"Id": "PhysicsSetting61", "Name": "Babydoll X"}, {"Id": "PhysicsSetting62", "Name": "Babydoll X(2)"}, {"Id": "PhysicsSetting63", "Name": "Babydoll Y"}, {"Id": "PhysicsSetting64", "Name": "Shy <PERSON> L"}, {"Id": "PhysicsSetting65", "Name": "<PERSON><PERSON> <PERSON>"}, {"Id": "PhysicsSetting66", "Name": "Neck Bow"}, {"Id": "PhysicsSetting67", "Name": "Tails"}, {"Id": "PhysicsSetting68", "Name": "Shorts Strings"}, {"Id": "PhysicsSetting69", "Name": "Stars X"}, {"Id": "PhysicsSetting70", "Name": "Stars Y"}, {"Id": "PhysicsSetting71", "Name": "Shoe Zippers"}, {"Id": "PhysicsSetting72", "Name": "Shoe Flaps"}, {"Id": "PhysicsSetting73", "Name": "Skirt Bows"}, {"Id": "PhysicsSetting74", "Name": "Leg Warmers X"}, {"Id": "PhysicsSetting75", "Name": "Leg Warmers Y"}, {"Id": "PhysicsSetting76", "Name": "Sleeve Wave"}, {"Id": "PhysicsSetting77", "Name": "<PERSON><PERSON>"}, {"Id": "PhysicsSetting78", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "PhysicsSetting79", "Name": "Cardigan X"}, {"Id": "PhysicsSetting80", "Name": "Cardigan Y"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param92"}, "VertexIndex": 1, "Scale": 41.104, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 90, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 2, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param49"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1.5, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "VertexIndex": 1, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ3"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 45, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param104"}, "VertexIndex": 1, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param105"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param106"}, "VertexIndex": 3, "Scale": 6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 45, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param431"}, "VertexIndex": 1, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param432"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param433"}, "VertexIndex": 3, "Scale": 6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param107"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param108"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param109"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param110"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "Param92"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX3"}, "VertexIndex": 1, "Scale": 7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.75, "Delay": 1, "Acceleration": 1, "Radius": 20}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -15, "Default": 0, "Maximum": 15}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param155"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param156"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param157"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param168"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param169"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param170"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 25, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param201"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param202"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param203"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param239"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param172"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param173"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param174"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param197"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param198"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param199"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param238"}, "VertexIndex": 4, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param262"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param263"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param264"}, "VertexIndex": 3, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param257"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param258"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param259"}, "VertexIndex": 3, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param158"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param159"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param160"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param194"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param195"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param196"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param163"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param164"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param165"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param166"}, "VertexIndex": 4, "Scale": 1.001, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 56}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 36, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy1"}, "VertexIndex": 1, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_twintail_wavy1"}, "VertexIndex": 2, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_twintail_wavy1"}, "VertexIndex": 3, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_twintail_wavy1"}, "VertexIndex": 4, "Scale": 88.891, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_twintail_wavy1"}, "VertexIndex": 5, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_twintail_wavy1"}, "VertexIndex": 6, "Scale": 59.941, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_twintail_wavy1"}, "VertexIndex": 7, "Scale": 49.517, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 84}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 36, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_twintail_wavy3"}, "VertexIndex": 1, "Scale": 160.856, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_twintail_wavy2"}, "VertexIndex": 2, "Scale": 144.263, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_twintail_wavy2"}, "VertexIndex": 3, "Scale": 119.36, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_twintail_wavy2"}, "VertexIndex": 4, "Scale": 102.252, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_twintail_wavy2"}, "VertexIndex": 5, "Scale": 88.308, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_twintail_wavy2"}, "VertexIndex": 6, "Scale": 73.915, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_twintail_wavy2"}, "VertexIndex": 7, "Scale": 61.612, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 84}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy2"}, "VertexIndex": 1, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy3"}, "VertexIndex": 2, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy4"}, "VertexIndex": 3, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy5"}, "VertexIndex": 4, "Scale": 89, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy6"}, "VertexIndex": 5, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy7"}, "VertexIndex": 6, "Scale": 59, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_twintail_wavy8"}, "VertexIndex": 7, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 84}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_twintail_wavy4"}, "VertexIndex": 1, "Scale": 188.815, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_twintail_wavy5"}, "VertexIndex": 2, "Scale": 149.321, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_twintail_wavy3"}, "VertexIndex": 3, "Scale": 129.597, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_twintail_wavy3"}, "VertexIndex": 4, "Scale": 114.392, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_twintail_wavy3"}, "VertexIndex": 5, "Scale": 99.871, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_twintail_wavy3"}, "VertexIndex": 6, "Scale": 86.618, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_twintail_wavy3"}, "VertexIndex": 7, "Scale": 75.095, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 84}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param189"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param190"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param191"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param192"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 56}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param178"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param179"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param180"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param181"}, "VertexIndex": 4, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param184"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param185"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param186"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param187"}, "VertexIndex": 4, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.92, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param206"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param207"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param208"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param214"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param215"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param216"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param218"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param219"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param220"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param268"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param271"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param272"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param267"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param273"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param274"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param221"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param222"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param223"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_hairband_chain"}, "VertexIndex": 1, "Scale": 139.964, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_hairband_chain"}, "VertexIndex": 2, "Scale": 96.972, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_hairband_chain"}, "VertexIndex": 3, "Scale": 90, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 36, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_corset_pendant"}, "VertexIndex": 1, "Scale": 140, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_corset_pendant"}, "VertexIndex": 2, "Scale": 96, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_corset_pendant"}, "VertexIndex": 3, "Scale": 120, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param25"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param279"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param280"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param281"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting38", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param283"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param284"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param285"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 14}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting39", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param286"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting40", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param287"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -15, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting41", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param298"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param299"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting42", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param300"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param301"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param302"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 2, "Radius": 20}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting43", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param307"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting44", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param317"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param318"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param319"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param315"}, "VertexIndex": 4, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param316"}, "VertexIndex": 5, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting45", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param330"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param331"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param332"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting46", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param334"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param335"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param336"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param339"}, "VertexIndex": 4, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param337"}, "VertexIndex": 5, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param338"}, "VertexIndex": 6, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 63}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 78}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting47", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param359"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param360"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param364"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param365"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting48", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param361"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param362"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param363"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.8, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting49", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param374"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param375"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param376"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 16}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting50", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 61, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 39, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param377"}, "VertexIndex": 1, "Scale": 4.002, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param496"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 20}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 20}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting51", "Input": [{"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param378"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param379"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param380"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting52", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ponytail5"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ponytail5"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ponytail5"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ponytail5"}, "VertexIndex": 4, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ponytail5"}, "VertexIndex": 5, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ponytail5"}, "VertexIndex": 6, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ponytail5"}, "VertexIndex": 7, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 84}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting53", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ponytail9"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ponytail9"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ponytail9"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ponytail9"}, "VertexIndex": 4, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ponytail9"}, "VertexIndex": 5, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ponytail9"}, "VertexIndex": 6, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ponytail9"}, "VertexIndex": 7, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 84}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting54", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_chain3"}, "VertexIndex": 1, "Scale": 111.744, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_chain2"}, "VertexIndex": 2, "Scale": 79.315, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_chain2"}, "VertexIndex": 3, "Scale": 51.099, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_chain2"}, "VertexIndex": 4, "Scale": 36.119, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_chain3"}, "VertexIndex": 5, "Scale": 25.721, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting55", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_chain4"}, "VertexIndex": 1, "Scale": 111, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_chain5"}, "VertexIndex": 2, "Scale": 79, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_chain6"}, "VertexIndex": 3, "Scale": 51, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_chain7"}, "VertexIndex": 4, "Scale": 36, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_chain8"}, "VertexIndex": 5, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting56", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param386"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param387"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting57", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param488"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting58", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param389"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param390"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param391"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting59", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param397"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param398"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param399"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting60", "Input": [{"Source": {"Target": "Parameter", "Id": "Param401"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param402"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 14}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting61", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param406"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param407"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param408"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting62", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param405"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting63", "Input": [{"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param409"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param410"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param411"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting64", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param413"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param414"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting65", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param415"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param416"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting66", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param418"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param419"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param436"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting67", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_tail_fluffy"}, "VertexIndex": 1, "Scale": 241.716, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_tail_fluffy"}, "VertexIndex": 2, "Scale": 342.583, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_tail_fluffy"}, "VertexIndex": 3, "Scale": 453.045, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_tail_fluffy"}, "VertexIndex": 4, "Scale": 523.238, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_tail_fluffy"}, "VertexIndex": 5, "Scale": 603.427, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_tail_fluffy"}, "VertexIndex": 6, "Scale": 693.39, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_tail_fluffy"}, "VertexIndex": 7, "Scale": 797.877, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_tail_straight"}, "VertexIndex": 1, "Scale": 241, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_tail_straight"}, "VertexIndex": 2, "Scale": 342, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_tail_straight"}, "VertexIndex": 3, "Scale": 453, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_tail_straight"}, "VertexIndex": 4, "Scale": 523, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_tail_straight"}, "VertexIndex": 5, "Scale": 603, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_tail_straight"}, "VertexIndex": 6, "Scale": 693, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting68", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_shorts_stringL"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_shorts_stringL"}, "VertexIndex": 2, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_shorts_stringL"}, "VertexIndex": 3, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_shorts_stringL"}, "VertexIndex": 4, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_shorts_stringL"}, "VertexIndex": 5, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting69", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "AngleZ"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param438"}, "VertexIndex": 1, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting70", "Input": [{"Source": {"Target": "Parameter", "Id": "AngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param439"}, "VertexIndex": 1, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting71", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param493"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting72", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param494"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting73", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param498"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param499"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param500"}, "VertexIndex": 3, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting74", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param513"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting75", "Input": [{"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param514"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting76", "Input": [{"Source": {"Target": "Parameter", "Id": "Param401"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param516"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param517"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting77", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param518"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param520"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting78", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_cardigan_string2"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_cardigan_string2"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_cardigan_string2"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_cardigan_string3"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting79", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param535"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param537"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param538"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param539"}, "VertexIndex": 4, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting80", "Input": [{"Source": {"Target": "Parameter", "Id": "Param93"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param94"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param540"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param541"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param542"}, "VertexIndex": 3, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 20}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 20}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 20}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 20}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}