<template>
  <div class="comic-gallery-wrapper">
    <div v-if="comicStore.isLoadingComics" class="gallery-loading-placeholder">
      <div class="loading-spinner"></div>
      <p>正在加载作品...</p>
    </div>
    <ComicGallery 
      v-else
      :comics="savedComics"
      @comic-selected="handleComicSelected"
      @comic-deleted="handleComicDeleted"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { useComicStore } from '../stores/comicStore';
import ComicGallery from '../modules/ComicGallery.vue';

// 🔑 只依赖comicStore，与storyStore完全隔离
const comicStore = useComicStore();

// 🔑 直接从comicStore获取数据，避免中间层依赖
const savedComics = computed(() => {
  const comics = comicStore.savedComics || [];
  console.log('📚 ComicGalleryWrapper computed savedComics:', comics.length, '个作品');
  return comics;
});

// 事件处理
const emit = defineEmits<{
  'comic-selected': [comic: any];
  'comic-deleted': [comic: any];
}>();

const handleComicSelected = (comic: any) => {
  emit('comic-selected', comic);
};

const handleComicDeleted = (comic: any) => {
  emit('comic-deleted', comic);
};

// 🔧 修复：为局域网环境添加状态同步监听
let stateUpdateHandlers: (() => void)[] = [];

const handleComicGenerationCompleted = (event: CustomEvent) => {
  console.log('📚 ComicGalleryWrapper 收到漫画生成完成事件:', event.detail);
  
  // 强制刷新画廊数据
  comicStore.loadSavedComics().then(() => {
    console.log('✅ 画廊数据刷新完成');
  }).catch(error => {
    console.error('❌ 画廊数据刷新失败:', error);
  });
};

const handleForceGalleryRefresh = (event: CustomEvent) => {
  console.log('📚 ComicGalleryWrapper 收到强制刷新事件:', event.detail);
  
  // 立即刷新画廊
  comicStore.loadSavedComics().then(() => {
    console.log('✅ 强制画廊刷新完成');
  }).catch(error => {
    console.error('❌ 强制画廊刷新失败:', error);
  });
};

const handleStoryGenerationCompleted = (event: CustomEvent) => {
  console.log('📚 ComicGalleryWrapper 收到故事生成完成事件:', event.detail);
  
  // 延迟刷新，确保数据已保存
  setTimeout(() => {
    comicStore.loadSavedComics().then(() => {
      console.log('✅ 故事完成后画廊刷新完成');
    }).catch(error => {
      console.error('❌ 故事完成后画廊刷新失败:', error);
    });
  }, 300);
};

const handleLanGenerationSync = (event: CustomEvent) => {
  console.log('🌐 ComicGalleryWrapper 收到LAN状态同步事件:', event.detail);
  
  const { comic, stage } = event.detail;
  if (stage === 'completed' && comic) {
    console.log('🌐 LAN环境检测到生成完成，刷新画廊');
    
    // 立即刷新画廊数据
    comicStore.loadSavedComics().then(() => {
      console.log('✅ LAN同步后画廊刷新完成');
    }).catch(error => {
      console.error('❌ LAN同步后画廊刷新失败:', error);
    });
  }
};

onMounted(() => {
  console.log('🎯 ComicGalleryWrapper 挂载，添加状态同步监听器');
  
  // 监听漫画生成完成事件
  if (typeof window !== 'undefined') {
    window.addEventListener('comic-generation-completed', handleComicGenerationCompleted);
    window.addEventListener('force-gallery-refresh', handleForceGalleryRefresh);
    window.addEventListener('story-generation-completed', handleStoryGenerationCompleted);
    window.addEventListener('lan-generation-sync', handleLanGenerationSync);
    
    console.log('✅ 状态同步监听器已添加（包括LAN同步）');
  }
});

onUnmounted(() => {
  console.log('🎯 ComicGalleryWrapper 卸载，移除状态同步监听器');
  
  // 清理事件监听器
  if (typeof window !== 'undefined') {
    window.removeEventListener('comic-generation-completed', handleComicGenerationCompleted);
    window.removeEventListener('force-gallery-refresh', handleForceGalleryRefresh);
    window.removeEventListener('story-generation-completed', handleStoryGenerationCompleted);
    window.removeEventListener('lan-generation-sync', handleLanGenerationSync);
    
    console.log('✅ 状态同步监听器已移除（包括LAN同步）');
  }
  
  // 清理其他处理器
  stateUpdateHandlers.forEach(cleanup => cleanup());
  stateUpdateHandlers = [];
});
</script>

<style scoped>
.comic-gallery-wrapper {
  width: 100%;
  height: 100%;
}

.gallery-loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #888;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>