#!/usr/bin/env node

/**
 * 清理重复.png后缀的脚本
 * 这个脚本会扫描generated-images目录，找到所有有重复.png后缀的文件，
 * 将它们重命名为正确的文件名（只保留一个.png后缀）
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const GENERATED_IMAGES_DIR = path.join(__dirname, 'public/generated-images');

function cleanDuplicatePngFiles() {
  console.log('🧹 开始清理重复.png后缀的文件...');
  console.log('📁 扫描目录:', GENERATED_IMAGES_DIR);

  if (!fs.existsSync(GENERATED_IMAGES_DIR)) {
    console.log('❌ 目录不存在:', GENERATED_IMAGES_DIR);
    return;
  }

  const files = fs.readdirSync(GENERATED_IMAGES_DIR);
  console.log(`📊 找到${files.length}个文件`);

  let renamedCount = 0;
  let errorCount = 0;

  files.forEach(filename => {
    // 检查是否有重复的.png后缀
    const duplicatePngMatch = filename.match(/(.+?)(\.png){2,}$/i);
    
    if (duplicatePngMatch) {
      const baseName = duplicatePngMatch[1];
      const newFilename = `${baseName}.png`;
      
      const oldPath = path.join(GENERATED_IMAGES_DIR, filename);
      const newPath = path.join(GENERATED_IMAGES_DIR, newFilename);
      
      console.log(`🔄 重命名: ${filename} → ${newFilename}`);
      
      try {
        // 检查目标文件是否已经存在
        if (fs.existsSync(newPath)) {
          console.log(`⚠️  目标文件已存在，删除重复文件: ${filename}`);
          fs.unlinkSync(oldPath);
        } else {
          fs.renameSync(oldPath, newPath);
        }
        renamedCount++;
      } catch (error) {
        console.error(`❌ 重命名失败: ${filename}`, error.message);
        errorCount++;
      }
    }
  });

  console.log('\n✅ 清理完成!');
  console.log(`📊 统计结果:`);
  console.log(`  - 重命名/删除: ${renamedCount} 个文件`);
  console.log(`  - 错误: ${errorCount} 个文件`);
  
  if (renamedCount > 0) {
    console.log('\n🎉 文件名已清理，重复.png后缀问题已解决！');
  } else {
    console.log('\n✨ 没有发现重复.png后缀的文件。');
  }
}

// 运行清理
cleanDuplicatePngFiles();