/*!
 * @pixi/spritesheet - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/spritesheet is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_spritesheet=function(e,t,r,s,i){"use strict";var a=function(){function e(e,t,s){void 0===s&&(s=null),this.linkedSheets=[],this._texture=e instanceof r.Texture?e:null,this.baseTexture=e instanceof r.BaseTexture?e:this._texture.baseTexture,this.textures={},this.animations={},this.data=t;var i=this.baseTexture.resource;this.resolution=this._updateResolution(s||(i?i.url:null)),this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}return e.prototype._updateResolution=function(e){void 0===e&&(e=null);var t=this.data.meta.scale,r=s.getResolutionOfUrl(e,null);return null===r&&(r=void 0!==t?parseFloat(t):1),1!==r&&this.baseTexture.setResolution(r),r},e.prototype.parse=function(t){var r=this;return new Promise((function(s){r._callback=function(e){null==t||t(e),s(e)},r._batchIndex=0,r._frameKeys.length<=e.BATCH_SIZE?(r._processFrames(0),r._processAnimations(),r._parseComplete()):r._nextBatch()}))},e.prototype._processFrames=function(s){for(var i=s,a=e.BATCH_SIZE;i-s<a&&i<this._frameKeys.length;){var o=this._frameKeys[i],n=this._frames[o],u=n.frame;if(u){var l=null,h=null,c=!1!==n.trimmed&&n.sourceSize?n.sourceSize:n.frame,f=new t.Rectangle(0,0,Math.floor(c.w)/this.resolution,Math.floor(c.h)/this.resolution);l=n.rotated?new t.Rectangle(Math.floor(u.x)/this.resolution,Math.floor(u.y)/this.resolution,Math.floor(u.h)/this.resolution,Math.floor(u.w)/this.resolution):new t.Rectangle(Math.floor(u.x)/this.resolution,Math.floor(u.y)/this.resolution,Math.floor(u.w)/this.resolution,Math.floor(u.h)/this.resolution),!1!==n.trimmed&&n.spriteSourceSize&&(h=new t.Rectangle(Math.floor(n.spriteSourceSize.x)/this.resolution,Math.floor(n.spriteSourceSize.y)/this.resolution,Math.floor(u.w)/this.resolution,Math.floor(u.h)/this.resolution)),this.textures[o]=new r.Texture(this.baseTexture,l,f,h,n.rotated?2:0,n.anchor),r.Texture.addToCache(this.textures[o],o)}i++}},e.prototype._processAnimations=function(){var e=this.data.animations||{};for(var t in e){this.animations[t]=[];for(var r=0;r<e[t].length;r++){var s=e[t][r];this.animations[t].push(this.textures[s])}}},e.prototype._parseComplete=function(){var e=this._callback;this._callback=null,this._batchIndex=0,e.call(this,this.textures)},e.prototype._nextBatch=function(){var t=this;this._processFrames(this._batchIndex*e.BATCH_SIZE),this._batchIndex++,setTimeout((function(){t._batchIndex*e.BATCH_SIZE<t._frameKeys.length?t._nextBatch():(t._processAnimations(),t._parseComplete())}),0)},e.prototype.destroy=function(e){var t;for(var r in void 0===e&&(e=!1),this.textures)this.textures[r].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,e&&(null===(t=this._texture)||void 0===t||t.destroy(),this.baseTexture.destroy()),this._texture=null,this.baseTexture=null,this.linkedSheets=[]},e.BATCH_SIZE=1e3,e}(),o=function(){function e(){}return e.use=function(t,r){var o,n,u=this,l=t.name+"_image";if(t.data&&t.type===i.LoaderResource.TYPE.JSON&&t.data.frames&&!u.resources[l]){var h=null===(n=null===(o=t.data)||void 0===o?void 0:o.meta)||void 0===n?void 0:n.related_multi_packs;if(Array.isArray(h))for(var c=function(e){if("string"!=typeof e)return"continue";var r=e.replace(".json",""),a=s.url.resolve(t.url.replace(u.baseUrl,""),e);if(u.resources[r]||Object.values(u.resources).some((function(e){return s.url.format(s.url.parse(e.url))===a})))return"continue";var o={crossOrigin:t.crossOrigin,loadType:i.LoaderResource.LOAD_TYPE.XHR,xhrType:i.LoaderResource.XHR_RESPONSE_TYPE.JSON,parentResource:t,metadata:t.metadata};u.add(r,a,o)},f=0,d=h;f<d.length;f++){c(d[f])}var p={crossOrigin:t.crossOrigin,metadata:t.metadata.imageMetadata,parentResource:t},_=e.getResourcePath(t,u.baseUrl);u.add(l,_,p,(function(e){if(e.error)r(e.error);else{var s=new a(e.texture,t.data,t.url);s.parse().then((function(){t.spritesheet=s,t.textures=s.textures,r()}))}}))}else r()},e.getResourcePath=function(e,t){return e.isDataUrl?e.data.meta.image:s.url.resolve(e.url.replace(t,""),e.data.meta.image)},e.extension=r.ExtensionType.Loader,e}();return e.Spritesheet=a,e.SpritesheetLoader=o,Object.defineProperty(e,"__esModule",{value:!0}),e}({},PIXI,PIXI,PIXI.utils,PIXI);Object.assign(this.PIXI,_pixi_spritesheet);
//# sourceMappingURL=spritesheet.min.js.map
