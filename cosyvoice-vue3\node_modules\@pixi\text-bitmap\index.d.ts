/// <reference path="./global.d.ts" />

import { Container } from '@pixi/display';
import type { Dict } from '@pixi/utils';
import type { ExtensionMetadata } from '@pixi/core';
import type { IDestroyOptions } from '@pixi/display';
import type { ITextStyle } from '@pixi/text';
import type { Loader } from '@pixi/loaders';
import { LoaderResource } from '@pixi/loaders';
import { Mesh } from '@pixi/mesh';
import { ObservablePoint } from '@pixi/math';
import type { Rectangle } from '@pixi/math';
import type { Renderer } from '@pixi/core';
import { TextStyle } from '@pixi/text';
import type { TextStyleAlign } from '@pixi/text';
import { Texture } from '@pixi/core';

/**
 * Auto-detect BitmapFont parsing format based on data.
 * @private
 * @param {any} data - Data to detect format
 * @returns {any} Format or null
 */
export declare function autoDetectFormat(data: unknown): typeof formats[number] | null;

/**
 * BitmapFont represents a typeface available for use with the BitmapText class. Use the `install`
 * method for adding a font to be used.
 * @memberof PIXI
 */
export declare class BitmapFont {
    /**
     * This character set includes all the letters in the alphabet (both lower- and upper- case).
     * @type {string[][]}
     * @example
     * BitmapFont.from("ExampleFont", style, { chars: BitmapFont.ALPHA })
     */
    static readonly ALPHA: (string | string[])[];
    /**
     * This character set includes all decimal digits (from 0 to 9).
     * @type {string[][]}
     * @example
     * BitmapFont.from("ExampleFont", style, { chars: BitmapFont.NUMERIC })
     */
    static readonly NUMERIC: string[][];
    /**
     * This character set is the union of `BitmapFont.ALPHA` and `BitmapFont.NUMERIC`.
     * @type {string[][]}
     */
    static readonly ALPHANUMERIC: (string | string[])[];
    /**
     * This character set consists of all the ASCII table.
     * @member {string[][]}
     * @see http://www.asciitable.com/
     */
    static readonly ASCII: string[][];
    /**
     * Collection of default options when using `BitmapFont.from`.
     * @property {number} [resolution=1] -
     * @property {number} [textureWidth=512] -
     * @property {number} [textureHeight=512] -
     * @property {number} [padding=4] -
     * @property {string|string[]|string[][]} chars = PIXI.BitmapFont.ALPHANUMERIC
     */
    static readonly defaultOptions: IBitmapFontOptions;
    /** Collection of available/installed fonts. */
    static readonly available: Dict<BitmapFont>;
    /** The name of the font face. */
    readonly font: string;
    /** The size of the font face in pixels. */
    readonly size: number;
    /** The line-height of the font face in pixels. */
    readonly lineHeight: number;
    /** The map of characters by character code. */
    readonly chars: Dict<IBitmapFontCharacter>;
    /** The map of base page textures (i.e., sheets of glyphs). */
    readonly pageTextures: Dict<Texture>;
    /** The range of the distance field in pixels. */
    readonly distanceFieldRange: number;
    /** The kind of distance field for this font or "none". */
    readonly distanceFieldType: string;
    private _ownsTextures;
    /**
     * @param data
     * @param textures
     * @param ownsTextures - Setting to `true` will destroy page textures
     *        when the font is uninstalled.
     */
    constructor(data: BitmapFontData, textures: Texture[] | Dict<Texture>, ownsTextures?: boolean);
    /** Remove references to created glyph textures. */
    destroy(): void;
    /**
     * Register a new bitmap font.
     * @param data - The
     *        characters map that could be provided as xml or raw string.
     * @param textures - List of textures for each page.
     * @param ownsTextures - Set to `true` to destroy page textures
     *        when the font is uninstalled. By default fonts created with
     *        `BitmapFont.from` or from the `BitmapFontLoader` are `true`.
     * @returns {PIXI.BitmapFont} Result font object with font, size, lineHeight
     *         and char fields.
     */
    static install(data: string | XMLDocument | BitmapFontData, textures: Texture | Texture[] | Dict<Texture>, ownsTextures?: boolean): BitmapFont;
    /**
     * Remove bitmap font by name.
     * @param name - Name of the font to uninstall.
     */
    static uninstall(name: string): void;
    /**
     * Generates a bitmap-font for the given style and character set. This does not support
     * kernings yet. With `style` properties, only the following non-layout properties are used:
     *
     * - {@link PIXI.TextStyle#dropShadow|dropShadow}
     * - {@link PIXI.TextStyle#dropShadowDistance|dropShadowDistance}
     * - {@link PIXI.TextStyle#dropShadowColor|dropShadowColor}
     * - {@link PIXI.TextStyle#dropShadowBlur|dropShadowBlur}
     * - {@link PIXI.TextStyle#dropShadowAngle|dropShadowAngle}
     * - {@link PIXI.TextStyle#fill|fill}
     * - {@link PIXI.TextStyle#fillGradientStops|fillGradientStops}
     * - {@link PIXI.TextStyle#fillGradientType|fillGradientType}
     * - {@link PIXI.TextStyle#fontFamily|fontFamily}
     * - {@link PIXI.TextStyle#fontSize|fontSize}
     * - {@link PIXI.TextStyle#fontVariant|fontVariant}
     * - {@link PIXI.TextStyle#fontWeight|fontWeight}
     * - {@link PIXI.TextStyle#lineJoin|lineJoin}
     * - {@link PIXI.TextStyle#miterLimit|miterLimit}
     * - {@link PIXI.TextStyle#stroke|stroke}
     * - {@link PIXI.TextStyle#strokeThickness|strokeThickness}
     * - {@link PIXI.TextStyle#textBaseline|textBaseline}
     * @param name - The name of the custom font to use with BitmapText.
     * @param textStyle - Style options to render with BitmapFont.
     * @param options - Setup options for font or name of the font.
     * @param {string|string[]|string[][]} [options.chars=PIXI.BitmapFont.ALPHANUMERIC] - characters included
     *      in the font set. You can also use ranges. For example, `[['a', 'z'], ['A', 'Z'], "!@#$%^&*()~{}[] "]`.
     *      Don't forget to include spaces ' ' in your character set!
     * @param {number} [options.resolution=1] - Render resolution for glyphs.
     * @param {number} [options.textureWidth=512] - Optional width of atlas, smaller values to reduce memory.
     * @param {number} [options.textureHeight=512] - Optional height of atlas, smaller values to reduce memory.
     * @param {number} [options.padding=4] - Padding between glyphs on texture atlas.
     * @returns Font generated by style options.
     * @example
     * PIXI.BitmapFont.from("TitleFont", {
     *     fontFamily: "Arial",
     *     fontSize: 12,
     *     strokeThickness: 2,
     *     fill: "purple"
     * });
     *
     * const title = new PIXI.BitmapText("This is the title", { fontName: "TitleFont" });
     */
    static from(name: string, textStyle?: TextStyle | Partial<ITextStyle>, options?: IBitmapFontOptions): BitmapFont;
}

/**
 * Normalized parsed data from .fnt files.
 * @memberof PIXI
 */
export declare class BitmapFontData {
    /** @readonly */
    info: IBitmapFontDataInfo[];
    /** @readonly */
    common: IBitmapFontDataCommon[];
    /** @readonly */
    page: IBitmapFontDataPage[];
    /** @readonly */
    char: IBitmapFontDataChar[];
    /** @readonly */
    kerning: IBitmapFontDataKerning[];
    /** @readonly */
    distanceField: IBitmapFontDataDistanceField[];
    constructor();
}

/**
 * {@link PIXI.Loader Loader} middleware for loading
 * bitmap-based fonts suitable for using with {@link PIXI.BitmapText}.
 * @memberof PIXI
 */
export declare class BitmapFontLoader {
    /** @ignore */
    static extension: ExtensionMetadata;
    /**
     * Called when the plugin is installed.
     * @see PIXI.extensions.add
     */
    static add(): void;
    /**
     * Called after a resource is loaded.
     * @see PIXI.Loader.loaderMiddleware
     * @param this
     * @param {PIXI.LoaderResource} resource
     * @param {Function} next
     */
    static use(this: Loader, resource: LoaderResource, next: (...args: any[]) => void): void;
    /**
     * Get folder path from a resource.
     * @param loader
     * @param resource
     */
    private static getBaseUrl;
    /**
     * Replacement for NodeJS's path.dirname
     * @param {string} url - Path to get directory for
     */
    private static dirname;
}

/**
 * A BitmapText object will create a line or multiple lines of text using bitmap font.
 *
 * The primary advantage of this class over Text is that all of your textures are pre-generated and loading,
 * meaning that rendering is fast, and changing text has no performance implications.
 *
 * Supporting character sets other than latin, such as CJK languages, may be impractical due to the number of characters.
 *
 * To split a line you can use '\n', '\r' or '\r\n' in your string.
 *
 * PixiJS can auto-generate fonts on-the-fly using BitmapFont or use fnt files provided by:
 * http://www.angelcode.com/products/bmfont/ for Windows or
 * http://www.bmglyph.com/ for Mac.
 *
 * You can also use SDF, MSDF and MTSDF BitmapFonts for vector-like scaling appearance provided by:
 * https://github.com/soimy/msdf-bmfont-xml for SDF and MSDF fnt files or
 * https://github.com/Chlumsky/msdf-atlas-gen for SDF, MSDF and MTSDF json files
 *
 * A BitmapText can only be created when the font is loaded.
 *
 * ```js
 * // in this case the font is in a file called 'desyrel.fnt'
 * let bitmapText = new PIXI.BitmapText("text using a fancy font!", {
 *   fontName: "Desyrel",
 *   fontSize: 35,
 *   align: "right"
 * });
 * ```
 * @memberof PIXI
 */
export declare class BitmapText extends Container {
    static styleDefaults: Partial<IBitmapTextStyle>;
    /** Set to `true` if the BitmapText needs to be redrawn. */
    dirty: boolean;
    /**
     * The resolution / device pixel ratio of the canvas.
     *
     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.
     * @default PIXI.settings.RESOLUTION
     */
    _resolution: number;
    _autoResolution: boolean;
    /**
     * Private tracker for the width of the overall text.
     * @private
     */
    protected _textWidth: number;
    /**
     * Private tracker for the height of the overall text.
     * @private
     */
    protected _textHeight: number;
    /**
     * Private tracker for the current text.
     * @private
     */
    protected _text: string;
    /**
     * The max width of this bitmap text in pixels. If the text provided is longer than the
     * value provided, line breaks will be automatically inserted in the last whitespace.
     * Disable by setting value to 0
     * @private
     */
    protected _maxWidth: number;
    /**
     * The max line height. This is useful when trying to use the total height of the Text,
     * ie: when trying to vertically align. (Internally used)
     * @private
     */
    protected _maxLineHeight: number;
    /**
     * Letter spacing. This is useful for setting the space between characters.
     * @private
     */
    protected _letterSpacing: number;
    /**
     * Text anchor.
     * @readonly
     * @private
     */
    protected _anchor: ObservablePoint;
    /**
     * Private tracker for the current font.
     * @private
     */
    protected _font?: BitmapFont;
    /**
     * Private tracker for the current font name.
     * @private
     */
    protected _fontName: string;
    /**
     * Private tracker for the current font size.
     * @private
     */
    protected _fontSize?: number;
    /**
     * Private tracker for the current text align.
     * @type {string}
     * @private
     */
    protected _align: TextStyleAlign;
    /** Collection of page mesh data. */
    protected _activePagesMeshData: PageMeshData[];
    /**
     * Private tracker for the current tint.
     * @private
     */
    protected _tint: number;
    /**
     * If true PixiJS will Math.floor() x/y values when rendering.
     * @default PIXI.settings.ROUND_PIXELS
     */
    protected _roundPixels: boolean;
    /** Cached char texture is destroyed when BitmapText is destroyed. */
    private _textureCache;
    /**
     * @param text - A string that you would like the text to display.
     * @param style - The style parameters.
     * @param {string} style.fontName - The installed BitmapFont name.
     * @param {number} [style.fontSize] - The size of the font in pixels, e.g. 24. If undefined,
     *.     this will default to the BitmapFont size.
     * @param {string} [style.align='left'] - Alignment for multiline text ('left', 'center', 'right' or 'justify'),
     *      does not affect single line text.
     * @param {number} [style.tint=0xFFFFFF] - The tint color.
     * @param {number} [style.letterSpacing=0] - The amount of spacing between letters.
     * @param {number} [style.maxWidth=0] - The max width of the text before line wrapping.
     */
    constructor(text: string, style?: Partial<IBitmapTextStyle>);
    /** Renders text and updates it when needed. This should only be called if the BitmapFont is regenerated. */
    updateText(): void;
    updateTransform(): void;
    _render(renderer: Renderer): void;
    /**
     * Validates text before calling parent's getLocalBounds
     * @returns - The rectangular bounding area
     */
    getLocalBounds(): Rectangle;
    /**
     * Updates text when needed
     * @private
     */
    protected validate(): void;
    /**
     * The tint of the BitmapText object.
     * @default 0xffffff
     */
    get tint(): number;
    set tint(value: number);
    /**
     * The alignment of the BitmapText object.
     * @member {string}
     * @default 'left'
     */
    get align(): TextStyleAlign;
    set align(value: TextStyleAlign);
    /** The name of the BitmapFont. */
    get fontName(): string;
    set fontName(value: string);
    /** The size of the font to display. */
    get fontSize(): number;
    set fontSize(value: number | undefined);
    /**
     * The anchor sets the origin point of the text.
     *
     * The default is `(0,0)`, this means the text's origin is the top left.
     *
     * Setting the anchor to `(0.5,0.5)` means the text's origin is centered.
     *
     * Setting the anchor to `(1,1)` would mean the text's origin point will be the bottom right corner.
     */
    get anchor(): ObservablePoint;
    set anchor(value: ObservablePoint);
    /** The text of the BitmapText object. */
    get text(): string;
    set text(text: string);
    /**
     * The max width of this bitmap text in pixels. If the text provided is longer than the
     * value provided, line breaks will be automatically inserted in the last whitespace.
     * Disable by setting the value to 0.
     */
    get maxWidth(): number;
    set maxWidth(value: number);
    /**
     * The max line height. This is useful when trying to use the total height of the Text,
     * i.e. when trying to vertically align.
     * @readonly
     */
    get maxLineHeight(): number;
    /**
     * The width of the overall text, different from fontSize,
     * which is defined in the style object.
     * @readonly
     */
    get textWidth(): number;
    /** Additional space between characters. */
    get letterSpacing(): number;
    set letterSpacing(value: number);
    /**
     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.
     * Advantages can include sharper image quality (like text) and faster rendering on canvas.
     * The main disadvantage is movement of objects may appear less smooth.
     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}
     * @default PIXI.settings.ROUND_PIXELS
     */
    get roundPixels(): boolean;
    set roundPixels(value: boolean);
    /**
     * The height of the overall text, different from fontSize,
     * which is defined in the style object.
     * @readonly
     */
    get textHeight(): number;
    /**
     * The resolution / device pixel ratio of the canvas.
     *
     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.
     * @default 1
     */
    get resolution(): number;
    set resolution(value: number);
    destroy(options?: boolean | IDestroyOptions): void;
}

declare const formats: readonly [typeof TextFormat, typeof XMLFormat, typeof XMLStringFormat];

export declare interface IBitmapFontCharacter {
    xOffset: number;
    yOffset: number;
    xAdvance: number;
    texture: Texture;
    page: number;
    kerning: Dict<number>;
}

/** @memberof PIXI */
export declare interface IBitmapFontDataChar {
    /** Unique id of character */
    id: number;
    /** {@link PIXI.IBitmapFontDataPage} id */
    page: number;
    /** x-position of character in page. */
    x: number;
    /** y-position of character in page. */
    y: number;
    /** Width of character in page. */
    width: number;
    /** Height of character in page. */
    height: number;
    /** x-offset to apply when rendering character */
    xoffset: number;
    /** y-offset to apply when rendering character. */
    yoffset: number;
    /** Advancement to apply to next character. */
    xadvance: number;
}

/** @memberof PIXI */
export declare interface IBitmapFontDataCommon {
    /** Line height, in pixels. */
    lineHeight: number;
}

/** @memberof PIXI */
export declare interface IBitmapFontDataDistanceField {
    /** Type of distance field */
    fieldType: string;
    /** Range of distance */
    distanceRange: number;
}

/** @memberof PIXI */
export declare interface IBitmapFontDataInfo {
    /** Font face */
    face: string;
    /** Font size */
    size: number;
}

/** @memberof PIXI */
export declare interface IBitmapFontDataKerning {
    /** First character of pair */
    first: number;
    /** Second character of pair */
    second: number;
    /** x-offset to apply between first & second characters when they are next to each other. */
    amount: number;
}

/** @memberof PIXI */
export declare interface IBitmapFontDataPage {
    /** Unique id for bitmap texture */
    id: number;
    /** File name */
    file: string;
}

/** @memberof PIXI */
export declare interface IBitmapFontOptions {
    /**
     * The character set to generate.
     * @default PIXI.BitmapFont.ALPHANUMERIC
     */
    chars?: string | (string | string[])[];
    /**
     * The resolution for rendering.
     * @default 1
     */
    resolution?: number;
    /**
     * The padding between glyphs in the atlas.
     * @default 4
     */
    padding?: number;
    /**
     * The width of the texture atlas.
     * @default 512
     */
    textureWidth?: number;
    /**
     * The height of the texture atlas.
     * @default 512
     */
    textureHeight?: number;
    /**
     * Skip generation of kerning information for the BitmapFont.
     * If true, this could potentially increase the performance, but may impact the rendered text appearance.
     * @default false
     */
    skipKerning?: boolean;
}

/**
 * Internal data format used to convert to BitmapFontData.
 * @private
 */
export declare interface IBitmapFontRawData {
    info: {
        face: string;
        size: string;
    }[];
    common: {
        lineHeight: string;
    }[];
    page: {
        id: string;
        file: string;
    }[];
    chars: {
        count: number;
    }[];
    char: {
        id: string;
        page: string;
        x: string;
        y: string;
        width: string;
        height: string;
        xoffset: string;
        yoffset: string;
        xadvance: string;
    }[];
    kernings?: {
        count: number;
    }[];
    kerning?: {
        first: string;
        second: string;
        amount: string;
    }[];
    distanceField?: {
        fieldType: string;
        distanceRange: string;
    }[];
}

export declare interface IBitmapTextFontDescriptor {
    name: string;
    size: number;
}

export declare interface IBitmapTextStyle {
    fontName: string;
    fontSize: number;
    tint: number;
    align: TextStyleAlign;
    letterSpacing: number;
    maxWidth: number;
}

declare interface PageMeshData {
    index: number;
    indexCount: number;
    vertexCount: number;
    uvsCount: number;
    total: number;
    mesh: Mesh;
    vertices?: Float32Array;
    uvs?: Float32Array;
    indices?: Uint16Array;
}

/**
 * BitmapFont format that's Text-based.
 * @private
 */
export declare class TextFormat {
    /**
     * Check if resource refers to txt font data.
     * @param data
     * @returns - True if resource could be treated as font data, false otherwise.
     */
    static test(data: unknown): boolean;
    /**
     * Convert text font data to a javascript object.
     * @param txt - Raw string data to be converted
     * @returns - Parsed font data
     */
    static parse(txt: string): BitmapFontData;
}

/**
 * BitmapFont format that's XML-based.
 * @private
 */
export declare class XMLFormat {
    /**
     * Check if resource refers to xml font data.
     * @param data
     * @returns - True if resource could be treated as font data, false otherwise.
     */
    static test(data: unknown): boolean;
    /**
     * Convert the XML into BitmapFontData that we can use.
     * @param xml
     * @returns - Data to use for BitmapFont
     */
    static parse(xml: XMLDocument): BitmapFontData;
}

/**
 * BitmapFont format that's XML-based.
 * @private
 */
export declare class XMLStringFormat {
    /**
     * Check if resource refers to text xml font data.
     * @param data
     * @returns - True if resource could be treated as font data, false otherwise.
     */
    static test(data: unknown): boolean;
    /**
     * Convert the text XML into BitmapFontData that we can use.
     * @param xmlTxt
     * @returns - Data to use for BitmapFont
     */
    static parse(xmlTxt: string): BitmapFontData;
}

export { }
