/*!
 * @pixi/loaders - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/loaders is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_loaders=function(t,e){"use strict";var r=function(){function t(t,e,r){void 0===e&&(e=!1),this._fn=t,this._once=e,this._thisArg=r,this._next=this._prev=this._owner=null}return t.prototype.detach=function(){return null!==this._owner&&(this._owner.detach(this),!0)},t}();function i(t,e){return t._head?(t._tail._next=e,e._prev=t._tail,t._tail=e):(t._head=e,t._tail=e),e._owner=t,e}var s,n=function(){function t(){this._head=this._tail=void 0}return t.prototype.handlers=function(t){void 0===t&&(t=!1);var e=this._head;if(t)return!!e;for(var r=[];e;)r.push(e),e=e._next;return r},t.prototype.has=function(t){if(!(t instanceof r))throw new Error("MiniSignal#has(): First arg must be a SignalBinding object.");return t._owner===this},t.prototype.dispatch=function(){for(var t=arguments,e=[],r=0;r<arguments.length;r++)e[r]=t[r];var i=this._head;if(!i)return!1;for(;i;)i._once&&this.detach(i),i._fn.apply(i._thisArg,e),i=i._next;return!0},t.prototype.add=function(t,e){if(void 0===e&&(e=null),"function"!=typeof t)throw new Error("MiniSignal#add(): First arg must be a Function.");return i(this,new r(t,!1,e))},t.prototype.once=function(t,e){if(void 0===e&&(e=null),"function"!=typeof t)throw new Error("MiniSignal#once(): First arg must be a Function.");return i(this,new r(t,!0,e))},t.prototype.detach=function(t){if(!(t instanceof r))throw new Error("MiniSignal#detach(): First arg must be a SignalBinding object.");return t._owner!==this||(t._prev&&(t._prev._next=t._next),t._next&&(t._next._prev=t._prev),t===this._head?(this._head=t._next,null===t._next&&(this._tail=null)):t===this._tail&&(this._tail=t._prev,this._tail._next=null),t._owner=null),this},t.prototype.detachAll=function(){var t=this._head;if(!t)return this;for(this._head=this._tail=null;t;)t._owner=null,t=t._next;return this},t}();function o(t,e){e=e||{};for(var r={key:["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}},i=r.parser[e.strictMode?"strict":"loose"].exec(t),s={},n=14;n--;)s[r.key[n]]=i[n]||"";return s[r.q.name]={},s[r.key[12]].replace(r.q.parser,(function(t,e,i){e&&(s[r.q.name][e]=i)})),s}var a,h,u,d,l,p=null;function c(){}function _(t,e,r){e&&0===e.indexOf(".")&&(e=e.substring(1)),e&&(t[e]=r)}function E(t){return t.toString().replace("object ","")}function f(){}function T(t){return function(){for(var e=arguments,r=[],i=0;i<arguments.length;i++)r[i]=e[i];if(null===t)throw new Error("Callback was already called.");var s=t;t=null,s.apply(this,r)}}t.LoaderResource=function(){function t(e,r,i){if(this._dequeue=c,this._onLoadBinding=null,this._elementTimer=0,this._boundComplete=null,this._boundOnError=null,this._boundOnProgress=null,this._boundOnTimeout=null,this._boundXhrOnError=null,this._boundXhrOnTimeout=null,this._boundXhrOnAbort=null,this._boundXhrOnLoad=null,"string"!=typeof e||"string"!=typeof r)throw new Error("Both name and url are required for constructing a resource.");i=i||{},this._flags=0,this._setFlag(t.STATUS_FLAGS.DATA_URL,0===r.indexOf("data:")),this.name=e,this.url=r,this.extension=this._getExtension(),this.data=null,this.crossOrigin=!0===i.crossOrigin?"anonymous":i.crossOrigin,this.timeout=i.timeout||0,this.loadType=i.loadType||this._determineLoadType(),this.xhrType=i.xhrType,this.metadata=i.metadata||{},this.error=null,this.xhr=null,this.children=[],this.type=t.TYPE.UNKNOWN,this.progressChunk=0,this._dequeue=c,this._onLoadBinding=null,this._elementTimer=0,this._boundComplete=this.complete.bind(this),this._boundOnError=this._onError.bind(this),this._boundOnProgress=this._onProgress.bind(this),this._boundOnTimeout=this._onTimeout.bind(this),this._boundXhrOnError=this._xhrOnError.bind(this),this._boundXhrOnTimeout=this._xhrOnTimeout.bind(this),this._boundXhrOnAbort=this._xhrOnAbort.bind(this),this._boundXhrOnLoad=this._xhrOnLoad.bind(this),this.onStart=new n,this.onProgress=new n,this.onComplete=new n,this.onAfterMiddleware=new n}return t.setExtensionLoadType=function(e,r){_(t._loadTypeMap,e,r)},t.setExtensionXhrType=function(e,r){_(t._xhrTypeMap,e,r)},Object.defineProperty(t.prototype,"isDataUrl",{get:function(){return this._hasFlag(t.STATUS_FLAGS.DATA_URL)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isComplete",{get:function(){return this._hasFlag(t.STATUS_FLAGS.COMPLETE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isLoading",{get:function(){return this._hasFlag(t.STATUS_FLAGS.LOADING)},enumerable:!1,configurable:!0}),t.prototype.complete=function(){this._clearEvents(),this._finish()},t.prototype.abort=function(e){if(!this.error){if(this.error=new Error(e),this._clearEvents(),this.xhr)this.xhr.abort();else if(this.xdr)this.xdr.abort();else if(this.data)if(this.data.src)this.data.src=t.EMPTY_GIF;else for(;this.data.firstChild;)this.data.removeChild(this.data.firstChild);this._finish()}},t.prototype.load=function(e){var r=this;if(!this.isLoading)if(this.isComplete)e&&setTimeout((function(){return e(r)}),1);else switch(e&&this.onComplete.once(e),this._setFlag(t.STATUS_FLAGS.LOADING,!0),this.onStart.dispatch(this),!1!==this.crossOrigin&&"string"==typeof this.crossOrigin||(this.crossOrigin=this._determineCrossOrigin(this.url)),this.loadType){case t.LOAD_TYPE.IMAGE:this.type=t.TYPE.IMAGE,this._loadElement("image");break;case t.LOAD_TYPE.AUDIO:this.type=t.TYPE.AUDIO,this._loadSourceElement("audio");break;case t.LOAD_TYPE.VIDEO:this.type=t.TYPE.VIDEO,this._loadSourceElement("video");break;case t.LOAD_TYPE.XHR:default:void 0===s&&(s=!(!globalThis.XDomainRequest||"withCredentials"in new XMLHttpRequest)),s&&this.crossOrigin?this._loadXdr():this._loadXhr()}},t.prototype._hasFlag=function(t){return 0!=(this._flags&t)},t.prototype._setFlag=function(t,e){this._flags=e?this._flags|t:this._flags&~t},t.prototype._clearEvents=function(){clearTimeout(this._elementTimer),this.data&&this.data.removeEventListener&&(this.data.removeEventListener("error",this._boundOnError,!1),this.data.removeEventListener("load",this._boundComplete,!1),this.data.removeEventListener("progress",this._boundOnProgress,!1),this.data.removeEventListener("canplaythrough",this._boundComplete,!1)),this.xhr&&(this.xhr.removeEventListener?(this.xhr.removeEventListener("error",this._boundXhrOnError,!1),this.xhr.removeEventListener("timeout",this._boundXhrOnTimeout,!1),this.xhr.removeEventListener("abort",this._boundXhrOnAbort,!1),this.xhr.removeEventListener("progress",this._boundOnProgress,!1),this.xhr.removeEventListener("load",this._boundXhrOnLoad,!1)):(this.xhr.onerror=null,this.xhr.ontimeout=null,this.xhr.onprogress=null,this.xhr.onload=null))},t.prototype._finish=function(){if(this.isComplete)throw new Error("Complete called again for an already completed resource.");this._setFlag(t.STATUS_FLAGS.COMPLETE,!0),this._setFlag(t.STATUS_FLAGS.LOADING,!1),this.onComplete.dispatch(this)},t.prototype._loadElement=function(t){this.metadata.loadElement?this.data=this.metadata.loadElement:"image"===t&&void 0!==globalThis.Image?this.data=new Image:this.data=document.createElement(t),this.crossOrigin&&(this.data.crossOrigin=this.crossOrigin),this.metadata.skipSource||(this.data.src=this.url),this.data.addEventListener("error",this._boundOnError,!1),this.data.addEventListener("load",this._boundComplete,!1),this.data.addEventListener("progress",this._boundOnProgress,!1),this.timeout&&(this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout))},t.prototype._loadSourceElement=function(t){if(this.metadata.loadElement?this.data=this.metadata.loadElement:"audio"===t&&void 0!==globalThis.Audio?this.data=new Audio:this.data=document.createElement(t),null!==this.data){if(this.crossOrigin&&(this.data.crossOrigin=this.crossOrigin),!this.metadata.skipSource)if(navigator.isCocoonJS)this.data.src=Array.isArray(this.url)?this.url[0]:this.url;else if(Array.isArray(this.url))for(var e=this.metadata.mimeType,r=0;r<this.url.length;++r)this.data.appendChild(this._createSource(t,this.url[r],Array.isArray(e)?e[r]:e));else{e=this.metadata.mimeType;this.data.appendChild(this._createSource(t,this.url,Array.isArray(e)?e[0]:e))}this.data.addEventListener("error",this._boundOnError,!1),this.data.addEventListener("load",this._boundComplete,!1),this.data.addEventListener("progress",this._boundOnProgress,!1),this.data.addEventListener("canplaythrough",this._boundComplete,!1),this.data.load(),this.timeout&&(this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout))}else this.abort("Unsupported element: "+t)},t.prototype._loadXhr=function(){"string"!=typeof this.xhrType&&(this.xhrType=this._determineXhrType());var e=this.xhr=new XMLHttpRequest;"use-credentials"===this.crossOrigin&&(e.withCredentials=!0),e.open("GET",this.url,!0),e.timeout=this.timeout,this.xhrType===t.XHR_RESPONSE_TYPE.JSON||this.xhrType===t.XHR_RESPONSE_TYPE.DOCUMENT?e.responseType=t.XHR_RESPONSE_TYPE.TEXT:e.responseType=this.xhrType,e.addEventListener("error",this._boundXhrOnError,!1),e.addEventListener("timeout",this._boundXhrOnTimeout,!1),e.addEventListener("abort",this._boundXhrOnAbort,!1),e.addEventListener("progress",this._boundOnProgress,!1),e.addEventListener("load",this._boundXhrOnLoad,!1),e.send()},t.prototype._loadXdr=function(){"string"!=typeof this.xhrType&&(this.xhrType=this._determineXhrType());var t=this.xhr=new globalThis.XDomainRequest;t.timeout=this.timeout||5e3,t.onerror=this._boundXhrOnError,t.ontimeout=this._boundXhrOnTimeout,t.onprogress=this._boundOnProgress,t.onload=this._boundXhrOnLoad,t.open("GET",this.url,!0),setTimeout((function(){return t.send()}),1)},t.prototype._createSource=function(t,e,r){r||(r=t+"/"+this._getExtension(e));var i=document.createElement("source");return i.src=e,i.type=r,i},t.prototype._onError=function(t){this.abort("Failed to load element using: "+t.target.nodeName)},t.prototype._onProgress=function(t){t&&t.lengthComputable&&this.onProgress.dispatch(this,t.loaded/t.total)},t.prototype._onTimeout=function(){this.abort("Load timed out.")},t.prototype._xhrOnError=function(){var t=this.xhr;this.abort(E(t)+" Request failed. Status: "+t.status+', text: "'+t.statusText+'"')},t.prototype._xhrOnTimeout=function(){var t=this.xhr;this.abort(E(t)+" Request timed out.")},t.prototype._xhrOnAbort=function(){var t=this.xhr;this.abort(E(t)+" Request was aborted by the user.")},t.prototype._xhrOnLoad=function(){var e=this.xhr,r="",i=void 0===e.status?200:e.status;if(""!==e.responseType&&"text"!==e.responseType&&void 0!==e.responseType||(r=e.responseText),0===i&&(r.length>0||e.responseType===t.XHR_RESPONSE_TYPE.BUFFER)?i=200:1223===i&&(i=204),2===(i/100|0)){if(this.xhrType===t.XHR_RESPONSE_TYPE.TEXT)this.data=r,this.type=t.TYPE.TEXT;else if(this.xhrType===t.XHR_RESPONSE_TYPE.JSON)try{this.data=JSON.parse(r),this.type=t.TYPE.JSON}catch(t){return void this.abort("Error trying to parse loaded json: "+t)}else if(this.xhrType===t.XHR_RESPONSE_TYPE.DOCUMENT)try{if(globalThis.DOMParser){var s=new DOMParser;this.data=s.parseFromString(r,"text/xml")}else{var n=document.createElement("div");n.innerHTML=r,this.data=n}this.type=t.TYPE.XML}catch(t){return void this.abort("Error trying to parse loaded xml: "+t)}else this.data=e.response||r;this.complete()}else this.abort("["+e.status+"] "+e.statusText+": "+e.responseURL)},t.prototype._determineCrossOrigin=function(t,e){if(0===t.indexOf("data:"))return"";if(globalThis.origin!==globalThis.location.origin)return"anonymous";e=e||globalThis.location,p||(p=document.createElement("a")),p.href=t;var r=o(p.href,{strictMode:!0}),i=!r.port&&""===e.port||r.port===e.port,s=r.protocol?r.protocol+":":"";return r.host===e.hostname&&i&&s===e.protocol?"":"anonymous"},t.prototype._determineXhrType=function(){return t._xhrTypeMap[this.extension]||t.XHR_RESPONSE_TYPE.TEXT},t.prototype._determineLoadType=function(){return t._loadTypeMap[this.extension]||t.LOAD_TYPE.XHR},t.prototype._getExtension=function(t){void 0===t&&(t=this.url);var e="";if(this.isDataUrl){var r=t.indexOf("/");e=t.substring(r+1,t.indexOf(";",r))}else{var i=t.indexOf("?"),s=t.indexOf("#"),n=Math.min(i>-1?i:t.length,s>-1?s:t.length);e=(t=t.substring(0,n)).substring(t.lastIndexOf(".")+1)}return e.toLowerCase()},t.prototype._getMimeFromXhrType=function(e){switch(e){case t.XHR_RESPONSE_TYPE.BUFFER:return"application/octet-binary";case t.XHR_RESPONSE_TYPE.BLOB:return"application/blob";case t.XHR_RESPONSE_TYPE.DOCUMENT:return"application/xml";case t.XHR_RESPONSE_TYPE.JSON:return"application/json";case t.XHR_RESPONSE_TYPE.DEFAULT:case t.XHR_RESPONSE_TYPE.TEXT:default:return"text/plain"}},t}(),l=t.LoaderResource||(t.LoaderResource={}),(d=l.STATUS_FLAGS||(l.STATUS_FLAGS={}))[d.NONE=0]="NONE",d[d.DATA_URL=1]="DATA_URL",d[d.COMPLETE=2]="COMPLETE",d[d.LOADING=4]="LOADING",(u=l.TYPE||(l.TYPE={}))[u.UNKNOWN=0]="UNKNOWN",u[u.JSON=1]="JSON",u[u.XML=2]="XML",u[u.IMAGE=3]="IMAGE",u[u.AUDIO=4]="AUDIO",u[u.VIDEO=5]="VIDEO",u[u.TEXT=6]="TEXT",(h=l.LOAD_TYPE||(l.LOAD_TYPE={}))[h.XHR=1]="XHR",h[h.IMAGE=2]="IMAGE",h[h.AUDIO=3]="AUDIO",h[h.VIDEO=4]="VIDEO",(a=l.XHR_RESPONSE_TYPE||(l.XHR_RESPONSE_TYPE={})).DEFAULT="text",a.BUFFER="arraybuffer",a.BLOB="blob",a.DOCUMENT="document",a.JSON="json",a.TEXT="text",l._loadTypeMap={gif:l.LOAD_TYPE.IMAGE,png:l.LOAD_TYPE.IMAGE,bmp:l.LOAD_TYPE.IMAGE,jpg:l.LOAD_TYPE.IMAGE,jpeg:l.LOAD_TYPE.IMAGE,tif:l.LOAD_TYPE.IMAGE,tiff:l.LOAD_TYPE.IMAGE,webp:l.LOAD_TYPE.IMAGE,tga:l.LOAD_TYPE.IMAGE,avif:l.LOAD_TYPE.IMAGE,svg:l.LOAD_TYPE.IMAGE,"svg+xml":l.LOAD_TYPE.IMAGE,mp3:l.LOAD_TYPE.AUDIO,ogg:l.LOAD_TYPE.AUDIO,wav:l.LOAD_TYPE.AUDIO,mp4:l.LOAD_TYPE.VIDEO,webm:l.LOAD_TYPE.VIDEO},l._xhrTypeMap={xhtml:l.XHR_RESPONSE_TYPE.DOCUMENT,html:l.XHR_RESPONSE_TYPE.DOCUMENT,htm:l.XHR_RESPONSE_TYPE.DOCUMENT,xml:l.XHR_RESPONSE_TYPE.DOCUMENT,tmx:l.XHR_RESPONSE_TYPE.DOCUMENT,svg:l.XHR_RESPONSE_TYPE.DOCUMENT,tsx:l.XHR_RESPONSE_TYPE.DOCUMENT,gif:l.XHR_RESPONSE_TYPE.BLOB,png:l.XHR_RESPONSE_TYPE.BLOB,bmp:l.XHR_RESPONSE_TYPE.BLOB,jpg:l.XHR_RESPONSE_TYPE.BLOB,jpeg:l.XHR_RESPONSE_TYPE.BLOB,tif:l.XHR_RESPONSE_TYPE.BLOB,tiff:l.XHR_RESPONSE_TYPE.BLOB,webp:l.XHR_RESPONSE_TYPE.BLOB,tga:l.XHR_RESPONSE_TYPE.BLOB,avif:l.XHR_RESPONSE_TYPE.BLOB,json:l.XHR_RESPONSE_TYPE.JSON,text:l.XHR_RESPONSE_TYPE.TEXT,txt:l.XHR_RESPONSE_TYPE.TEXT,ttf:l.XHR_RESPONSE_TYPE.BUFFER,otf:l.XHR_RESPONSE_TYPE.BUFFER},l.EMPTY_GIF="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==";var g=function(t,e){this.data=t,this.callback=e},O=function(){function t(t,e){var r=this;if(void 0===e&&(e=1),this.workers=0,this.saturated=f,this.unsaturated=f,this.empty=f,this.drain=f,this.error=f,this.started=!1,this.paused=!1,this._tasks=[],this._insert=function(t,e,i){if(i&&"function"!=typeof i)throw new Error("task callback must be a function");if(r.started=!0,null==t&&r.idle())setTimeout((function(){return r.drain()}),1);else{var s=new g(t,"function"==typeof i?i:f);e?r._tasks.unshift(s):r._tasks.push(s),setTimeout(r.process,1)}},this.process=function(){for(;!r.paused&&r.workers<r.concurrency&&r._tasks.length;){var t=r._tasks.shift();0===r._tasks.length&&r.empty(),r.workers+=1,r.workers===r.concurrency&&r.saturated(),r._worker(t.data,T(r._next(t)))}},this._worker=t,0===e)throw new Error("Concurrency must not be zero");this.concurrency=e,this.buffer=e/4}return t.prototype._next=function(t){var e=this;return function(){for(var r=arguments,i=[],s=0;s<arguments.length;s++)i[s]=r[s];e.workers-=1,t.callback.apply(t,i),null!=i[0]&&e.error(i[0],t.data),e.workers<=e.concurrency-e.buffer&&e.unsaturated(),e.idle()&&e.drain(),e.process()}},t.prototype.push=function(t,e){this._insert(t,!1,e)},t.prototype.kill=function(){this.workers=0,this.drain=f,this.started=!1,this._tasks=[]},t.prototype.unshift=function(t,e){this._insert(t,!0,e)},t.prototype.length=function(){return this._tasks.length},t.prototype.running=function(){return this.workers},t.prototype.idle=function(){return this._tasks.length+this.workers===0},t.prototype.pause=function(){!0!==this.paused&&(this.paused=!0)},t.prototype.resume=function(){if(!1!==this.paused){this.paused=!1;for(var t=1;t<=this.concurrency;t++)this.process()}},t.eachSeries=function(t,e,r,i){var s=0,n=t.length;!function o(a){a||s===n?r&&r(a):i?setTimeout((function(){e(t[s++],o)}),1):e(t[s++],o)}()},t.queue=function(e,r){return new t(e,r)},t}(),y=/(#[\w-]+)?$/,m=function(){function r(t,e){var i=this;void 0===t&&(t=""),void 0===e&&(e=10),this.progress=0,this.loading=!1,this.defaultQueryString="",this._beforeMiddleware=[],this._afterMiddleware=[],this._resourcesParsing=[],this._boundLoadResource=function(t,e){return i._loadResource(t,e)},this.resources={},this.baseUrl=t,this._beforeMiddleware=[],this._afterMiddleware=[],this._resourcesParsing=[],this._boundLoadResource=function(t,e){return i._loadResource(t,e)},this._queue=O.queue(this._boundLoadResource,e),this._queue.pause(),this.resources={},this.onProgress=new n,this.onError=new n,this.onLoad=new n,this.onStart=new n,this.onComplete=new n;for(var s=0;s<r._plugins.length;++s){var o=r._plugins[s],a=o.pre,h=o.use;a&&this.pre(a),h&&this.use(h)}this._protected=!1}return r.prototype._add=function(e,r,i,s){if(this.loading&&(!i||!i.parentResource))throw new Error("Cannot add resources while the loader is running.");if(this.resources[e])throw new Error('Resource named "'+e+'" already exists.');if(r=this._prepareUrl(r),this.resources[e]=new t.LoaderResource(e,r,i),"function"==typeof s&&this.resources[e].onAfterMiddleware.once(s),this.loading){for(var n=i.parentResource,o=[],a=0;a<n.children.length;++a)n.children[a].isComplete||o.push(n.children[a]);var h=n.progressChunk*(o.length+1)/(o.length+2);n.children.push(this.resources[e]),n.progressChunk=h;for(a=0;a<o.length;++a)o[a].progressChunk=h;this.resources[e].progressChunk=h}return this._queue.push(this.resources[e]),this},r.prototype.pre=function(t){return this._beforeMiddleware.push(t),this},r.prototype.use=function(t){return this._afterMiddleware.push(t),this},r.prototype.reset=function(){for(var t in this.progress=0,this.loading=!1,this._queue.kill(),this._queue.pause(),this.resources){var e=this.resources[t];e._onLoadBinding&&e._onLoadBinding.detach(),e.isLoading&&e.abort("loader reset")}return this.resources={},this},r.prototype.load=function(t){if("function"==typeof t&&this.onComplete.once(t),this.loading)return this;if(this._queue.idle())this._onStart(),this._onComplete();else{for(var e=100/this._queue._tasks.length,r=0;r<this._queue._tasks.length;++r)this._queue._tasks[r].data.progressChunk=e;this._onStart(),this._queue.resume()}return this},Object.defineProperty(r.prototype,"concurrency",{get:function(){return this._queue.concurrency},set:function(t){this._queue.concurrency=t},enumerable:!1,configurable:!0}),r.prototype._prepareUrl=function(t){var e,r=o(t,{strictMode:!0});if(e=r.protocol||!r.path||0===t.indexOf("//")?t:this.baseUrl.length&&this.baseUrl.lastIndexOf("/")!==this.baseUrl.length-1&&"/"!==t.charAt(0)?this.baseUrl+"/"+t:this.baseUrl+t,this.defaultQueryString){var i=y.exec(e)[0];-1!==(e=e.slice(0,e.length-i.length)).indexOf("?")?e+="&"+this.defaultQueryString:e+="?"+this.defaultQueryString,e+=i}return e},r.prototype._loadResource=function(t,e){var r=this;t._dequeue=e,O.eachSeries(this._beforeMiddleware,(function(e,i){e.call(r,t,(function(){i(t.isComplete?{}:null)}))}),(function(){t.isComplete?r._onLoad(t):(t._onLoadBinding=t.onComplete.once(r._onLoad,r),t.load())}),!0)},r.prototype._onStart=function(){this.progress=0,this.loading=!0,this.onStart.dispatch(this)},r.prototype._onComplete=function(){this.progress=100,this.loading=!1,this.onComplete.dispatch(this,this.resources)},r.prototype._onLoad=function(t){var e=this;t._onLoadBinding=null,this._resourcesParsing.push(t),t._dequeue(),O.eachSeries(this._afterMiddleware,(function(r,i){r.call(e,t,i)}),(function(){t.onAfterMiddleware.dispatch(t),e.progress=Math.min(100,e.progress+t.progressChunk),e.onProgress.dispatch(e,t),t.error?e.onError.dispatch(t.error,e,t):e.onLoad.dispatch(e,t),e._resourcesParsing.splice(e._resourcesParsing.indexOf(t),1),e._queue.idle()&&0===e._resourcesParsing.length&&e._onComplete()}),!0)},r.prototype.destroy=function(){this._protected||this.reset()},Object.defineProperty(r,"shared",{get:function(){var t=r._shared;return t||((t=new r)._protected=!0,r._shared=t),t},enumerable:!1,configurable:!0}),r.registerPlugin=function(t){return e.extensions.add({type:e.ExtensionType.Loader,ref:t}),r},r._plugins=[],r}();e.extensions.handleByList(e.ExtensionType.Loader,m._plugins),m.prototype.add=function(t,e,r,i){if(Array.isArray(t)){for(var s=0;s<t.length;++s)this.add(t[s]);return this}if("object"==typeof t&&(r=t,i=e||r.callback||r.onComplete,e=r.url,t=r.name||r.key||r.url),"string"!=typeof e&&(i=r,r=e,e=t),"string"!=typeof e)throw new Error("No url passed to add resource to loader.");return"function"==typeof r&&(i=r,r=null),this._add(t,e,r,i)};var b=function(){function t(){}return t.init=function(t){t=Object.assign({sharedLoader:!1},t),this.loader=t.sharedLoader?m.shared:new m},t.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},t.extension=e.ExtensionType.Application,t}(),P=function(){function r(){}return r.add=function(){t.LoaderResource.setExtensionLoadType("svg",t.LoaderResource.LOAD_TYPE.XHR),t.LoaderResource.setExtensionXhrType("svg",t.LoaderResource.XHR_RESPONSE_TYPE.TEXT)},r.use=function(r,i){if(!r.data||r.type!==t.LoaderResource.TYPE.IMAGE&&"svg"!==r.extension)i();else{var s=r.data,n=r.url,o=r.name,a=r.metadata;e.Texture.fromLoader(s,n,o,a).then((function(t){r.texture=t,i()})).catch(i)}},r.extension=e.ExtensionType.Loader,r}();function L(e,r){if(e.data){if(e.xhr&&e.xhrType===t.LoaderResource.XHR_RESPONSE_TYPE.BLOB)if(self.Blob&&"string"!=typeof e.data){if(0===e.data.type.indexOf("image")){var i=globalThis.URL||globalThis.webkitURL,s=i.createObjectURL(e.data);return e.blob=e.data,e.data=new Image,e.data.src=s,e.type=t.LoaderResource.TYPE.IMAGE,void(e.data.onload=function(){i.revokeObjectURL(s),e.data.onload=null,r()})}}else{var n=e.xhr.getResponseHeader("content-type");if(n&&0===n.indexOf("image"))return e.data=new Image,e.data.src="data:"+n+";base64,"+function(t){for(var e="",r=0;r<t.length;){for(var i=[0,0,0],s=[0,0,0,0],n=0;n<i.length;++n)r<t.length?i[n]=255&t.charCodeAt(r++):i[n]=0;switch(s[0]=i[0]>>2,s[1]=(3&i[0])<<4|i[1]>>4,s[2]=(15&i[1])<<2|i[2]>>6,s[3]=63&i[2],r-(t.length-1)){case 2:s[3]=64,s[2]=64;break;case 1:s[3]=64}for(n=0;n<s.length;++n)e+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(s[n])}return e}(e.xhr.responseText),e.type=t.LoaderResource.TYPE.IMAGE,void(e.data.onload=function(){e.data.onload=null,r()})}r()}else r()}var S=function(){function t(){}return t.extension=e.ExtensionType.Loader,t.use=L,t}();return e.extensions.add(P,S),t.AppLoaderPlugin=b,t.Loader=m,t.TextureLoader=P,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI);Object.assign(this.PIXI,_pixi_loaders);
//# sourceMappingURL=loaders.min.js.map
