/**
 * storyStore连接状态修复补丁
 * 专门修复storyStore中connect()方法和isConnected状态更新的逻辑问题
 */

export class StoryStoreConnectionPatch {
  private static applied = false;

  /**
   * 应用连接状态修复补丁
   */
  public static async applyPatch(): Promise<{
    success: boolean;
    message: string;
    patches: string[];
  }> {
    if (this.applied) {
      return {
        success: true,
        message: '补丁已经应用过了',
        patches: ['补丁状态检查 - 跳过重复应用']
      };
    }

    const patches: string[] = [];

    try {
      console.log('🩹 应用storyStore连接状态修复补丁...');

      // 动态导入storyStore
      const { useStoryStore } = await import('@/stores/storyStore');
      const storyStore = useStoryStore();

      // 补丁1：修复connect方法的状态更新逻辑
      const originalConnect = storyStore.connect;
      storyStore.connect = async function() {
        console.log('🩹 [补丁] 使用修复后的connect方法');
        
        try {
          const { getMasterWebSocketManager } = await import('@/services/masterWebSocketManager');
          const manager = getMasterWebSocketManager({}, 'comic-generation');
          
          // 检查是否已经连接
          if (manager.isConnected()) {
            console.log('🩹 [补丁] WebSocket管理器已连接，直接更新状态');
            (this as any).isConnected = true;
            (this as any).isConnecting = false;
            
            // 触发状态变化事件
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('storystore-connection-change', {
                detail: { 
                  connected: true, 
                  connecting: false,
                  timestamp: Date.now()
                }
              }));
            }
            return;
          }

          // 设置连接中状态
          (this as any).isConnecting = true;
          (this as any).isConnected = false;

          // 初始化WebSocket
          await (this as any).initializeWebSocket();
          
          // 连接管理器
          await manager.connect();
          
          // 强制更新状态为已连接
          if (manager.isConnected()) {
            console.log('🩹 [补丁] 连接成功，强制更新状态');
            (this as any).isConnected = true;
            (this as any).isConnecting = false;
            
            // 立即触发状态变化事件
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('storystore-connection-change', {
                detail: { 
                  connected: true, 
                  connecting: false,
                  timestamp: Date.now()
                }
              }));
            }
            
            console.log('✅ [补丁] 连接成功，状态已更新');
          } else {
            console.warn('⚠️ [补丁] 连接方法返回成功但管理器状态为未连接');
            (this as any).isConnected = false;
            (this as any).isConnecting = false;
          }

        } catch (error) {
          console.error('❌ [补丁] 连接失败:', error);
          (this as any).isConnected = false;
          (this as any).isConnecting = false;
          (this as any).error = '连接失败，请重试';
          throw error;
        }
      };
      patches.push('修复connect方法的状态更新逻辑');

      // 补丁2：增强handleConnectionStateChange方法
      const originalHandleConnectionStateChange = (storyStore as any).handleConnectionStateChange;
      if (originalHandleConnectionStateChange) {
        (storyStore as any).handleConnectionStateChange = function(state: any) {
          console.log(`🩹 [补丁] 增强的连接状态处理: ${state}`);
          
          // 防止状态回退
          if ((this as any).isConnected && state === 'connecting') {
            console.log('🩹 [补丁] 阻止已连接状态回退到连接中');
            return;
          }
          
          // 调用原始方法
          originalHandleConnectionStateChange.call(this, state);
          
          // 额外的状态同步确保
          const newConnectedState = state === 'connected';
          const newConnectingState = state === 'connecting';
          
          (this as any).isConnected = newConnectedState;
          (this as any).isConnecting = newConnectingState;
          
          console.log(`🩹 [补丁] 状态强制同步完成: isConnected=${newConnectedState}, isConnecting=${newConnectingState}`);
        };
        patches.push('增强handleConnectionStateChange方法');
      }

      // 补丁3：添加强制连接状态同步方法
      (storyStore as any).forceUpdateConnectionStatus = async function() {
        console.log('🩹 [补丁] 强制更新连接状态');
        
        try {
          const { getMasterWebSocketManager } = await import('@/services/masterWebSocketManager');
          const manager = getMasterWebSocketManager({}, 'comic-generation');
          
          const actualConnected = manager.isConnected();
          console.log(`🩹 [补丁] WebSocket管理器实际状态: ${actualConnected}`);
          
          if (actualConnected !== (this as any).isConnected) {
            console.log(`🩹 [补丁] 状态不一致，强制同步: ${actualConnected}`);
            (this as any).isConnected = actualConnected;
            (this as any).isConnecting = false;
            
            // 触发状态变化事件
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('storystore-connection-change', {
                detail: { 
                  connected: actualConnected, 
                  connecting: false,
                  timestamp: Date.now()
                }
              }));
            }
          }
          
          return actualConnected;
        } catch (error) {
          console.error('❌ [补丁] 强制更新连接状态失败:', error);
          return false;
        }
      };
      patches.push('添加强制连接状态同步方法');

      // 补丁4：定期状态同步监控
      setInterval(async () => {
        try {
          if ((storyStore as any).forceUpdateConnectionStatus) {
            await (storyStore as any).forceUpdateConnectionStatus();
          }
        } catch (error) {
          // 静默处理监控错误
        }
      }, 5000); // 每5秒检查一次
      patches.push('启动定期状态同步监控');

      this.applied = true;
      
      console.log('✅ storyStore连接状态修复补丁应用成功');
      return {
        success: true,
        message: 'storyStore连接状态修复补丁应用成功',
        patches
      };

    } catch (error) {
      console.error('❌ 应用storyStore连接状态修复补丁失败:', error);
      return {
        success: false,
        message: `补丁应用失败: ${error}`,
        patches
      };
    }
  }

  /**
   * 验证补丁是否正常工作
   */
  public static async verifyPatch(): Promise<{
    success: boolean;
    message: string;
    details: any;
  }> {
    try {
      console.log('🔍 验证storyStore连接状态补丁...');

      const { useStoryStore } = await import('@/stores/storyStore');
      const storyStore = useStoryStore();

      // 检查补丁方法是否存在
      const hasForceUpdate = typeof (storyStore as any).forceUpdateConnectionStatus === 'function';
      
      // 检查当前连接状态
      const currentStatus = storyStore.connectionStatus;
      const isConnected = storyStore.isConnected;
      const isConnecting = storyStore.isConnecting;

      // 尝试使用补丁方法
      let forceUpdateResult = null;
      if (hasForceUpdate) {
        forceUpdateResult = await (storyStore as any).forceUpdateConnectionStatus();
      }

      const details = {
        补丁已应用: this.applied,
        强制更新方法存在: hasForceUpdate,
        当前连接状态: currentStatus,
        isConnected: isConnected,
        isConnecting: isConnecting,
        强制更新结果: forceUpdateResult
      };

      console.log('📊 补丁验证结果:', details);

      const success = this.applied && hasForceUpdate;
      return {
        success,
        message: success ? '补丁验证成功' : '补丁验证失败',
        details
      };

    } catch (error) {
      console.error('❌ 补丁验证失败:', error);
      return {
        success: false,
        message: `补丁验证失败: ${error}`,
        details: { error: error.toString() }
      };
    }
  }
}

// 自动应用补丁（页面加载时）
if (typeof window !== 'undefined') {
  // 等待页面完全加载后应用补丁
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', async () => {
      await StoryStoreConnectionPatch.applyPatch();
    });
  } else {
    // 页面已加载，立即应用补丁
    setTimeout(async () => {
      await StoryStoreConnectionPatch.applyPatch();
    }, 1000);
  }

  // 全局暴露补丁方法
  (window as any).applyStoryStorePatch = () => StoryStoreConnectionPatch.applyPatch();
  (window as any).verifyStoryStorePatch = () => StoryStoreConnectionPatch.verifyPatch();

  console.log('🩹 storyStore连接状态修复补丁已准备');
  console.log('💡 使用方法：');
  console.log('   - applyStoryStorePatch() - 手动应用补丁');
  console.log('   - verifyStoryStorePatch() - 验证补丁状态');
}

export default StoryStoreConnectionPatch;