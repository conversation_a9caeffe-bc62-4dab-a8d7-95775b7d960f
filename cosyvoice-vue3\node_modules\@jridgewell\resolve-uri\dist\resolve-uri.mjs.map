{"version": 3, "file": "resolve-uri.mjs", "sources": ["../src/resolve-uri.ts"], "sourcesContent": ["// Matches the scheme of a URL, eg \"http://\"\nconst schemeRegex = /^[\\w+.-]+:\\/\\//;\n\n/**\n * Matches the parts of a URL:\n * 1. Scheme, including \":\", guaranteed.\n * 2. User/password, including \"@\", optional.\n * 3. Host, guaranteed.\n * 4. Port, including \":\", optional.\n * 5. Path, including \"/\", optional.\n * 6. Query, including \"?\", optional.\n * 7. Hash, including \"#\", optional.\n */\nconst urlRegex = /^([\\w+.-]+:)\\/\\/([^@/#?]*@)?([^:/#?]*)(:\\d+)?(\\/[^#?]*)?(\\?[^#]*)?(#.*)?/;\n\n/**\n * File URLs are weird. They dont' need the regular `//` in the scheme, they may or may not start\n * with a leading `/`, they can have a domain (but only if they don't start with a Windows drive).\n *\n * 1. Host, optional.\n * 2. Path, which may include \"/\", guaranteed.\n * 3. Query, including \"?\", optional.\n * 4. Hash, including \"#\", optional.\n */\nconst fileRegex = /^file:(?:\\/\\/((?![a-z]:)[^/#?]*)?)?(\\/?[^#?]*)(\\?[^#]*)?(#.*)?/i;\n\ntype Url = {\n  scheme: string;\n  user: string;\n  host: string;\n  port: string;\n  path: string;\n  query: string;\n  hash: string;\n  type: UrlType;\n};\n\nconst enum UrlType {\n  Empty = 1,\n  Hash = 2,\n  Query = 3,\n  RelativePath = 4,\n  AbsolutePath = 5,\n  SchemeRelative = 6,\n  Absolute = 7,\n}\n\nfunction isAbsoluteUrl(input: string): boolean {\n  return schemeRegex.test(input);\n}\n\nfunction isSchemeRelativeUrl(input: string): boolean {\n  return input.startsWith('//');\n}\n\nfunction isAbsolutePath(input: string): boolean {\n  return input.startsWith('/');\n}\n\nfunction isFileUrl(input: string): boolean {\n  return input.startsWith('file:');\n}\n\nfunction isRelative(input: string): boolean {\n  return /^[.?#]/.test(input);\n}\n\nfunction parseAbsoluteUrl(input: string): Url {\n  const match = urlRegex.exec(input)!;\n  return makeUrl(\n    match[1],\n    match[2] || '',\n    match[3],\n    match[4] || '',\n    match[5] || '/',\n    match[6] || '',\n    match[7] || '',\n  );\n}\n\nfunction parseFileUrl(input: string): Url {\n  const match = fileRegex.exec(input)!;\n  const path = match[2];\n  return makeUrl(\n    'file:',\n    '',\n    match[1] || '',\n    '',\n    isAbsolutePath(path) ? path : '/' + path,\n    match[3] || '',\n    match[4] || '',\n  );\n}\n\nfunction makeUrl(\n  scheme: string,\n  user: string,\n  host: string,\n  port: string,\n  path: string,\n  query: string,\n  hash: string,\n): Url {\n  return {\n    scheme,\n    user,\n    host,\n    port,\n    path,\n    query,\n    hash,\n    type: UrlType.Absolute,\n  };\n}\n\nfunction parseUrl(input: string): Url {\n  if (isSchemeRelativeUrl(input)) {\n    const url = parseAbsoluteUrl('http:' + input);\n    url.scheme = '';\n    url.type = UrlType.SchemeRelative;\n    return url;\n  }\n\n  if (isAbsolutePath(input)) {\n    const url = parseAbsoluteUrl('http://foo.com' + input);\n    url.scheme = '';\n    url.host = '';\n    url.type = UrlType.AbsolutePath;\n    return url;\n  }\n\n  if (isFileUrl(input)) return parseFileUrl(input);\n\n  if (isAbsoluteUrl(input)) return parseAbsoluteUrl(input);\n\n  const url = parseAbsoluteUrl('http://foo.com/' + input);\n  url.scheme = '';\n  url.host = '';\n  url.type = input\n    ? input.startsWith('?')\n      ? UrlType.Query\n      : input.startsWith('#')\n      ? UrlType.Hash\n      : UrlType.RelativePath\n    : UrlType.Empty;\n  return url;\n}\n\nfunction stripPathFilename(path: string): string {\n  // If a path ends with a parent directory \"..\", then it's a relative path with excess parent\n  // paths. It's not a file, so we can't strip it.\n  if (path.endsWith('/..')) return path;\n  const index = path.lastIndexOf('/');\n  return path.slice(0, index + 1);\n}\n\nfunction mergePaths(url: Url, base: Url) {\n  normalizePath(base, base.type);\n\n  // If the path is just a \"/\", then it was an empty path to begin with (remember, we're a relative\n  // path).\n  if (url.path === '/') {\n    url.path = base.path;\n  } else {\n    // Resolution happens relative to the base path's directory, not the file.\n    url.path = stripPathFilename(base.path) + url.path;\n  }\n}\n\n/**\n * The path can have empty directories \"//\", unneeded parents \"foo/..\", or current directory\n * \"foo/.\". We need to normalize to a standard representation.\n */\nfunction normalizePath(url: Url, type: UrlType) {\n  const rel = type <= UrlType.RelativePath;\n  const pieces = url.path.split('/');\n\n  // We need to preserve the first piece always, so that we output a leading slash. The item at\n  // pieces[0] is an empty string.\n  let pointer = 1;\n\n  // Positive is the number of real directories we've output, used for popping a parent directory.\n  // Eg, \"foo/bar/..\" will have a positive 2, and we can decrement to be left with just \"foo\".\n  let positive = 0;\n\n  // We need to keep a trailing slash if we encounter an empty directory (eg, splitting \"foo/\" will\n  // generate `[\"foo\", \"\"]` pieces). And, if we pop a parent directory. But once we encounter a\n  // real directory, we won't need to append, unless the other conditions happen again.\n  let addTrailingSlash = false;\n\n  for (let i = 1; i < pieces.length; i++) {\n    const piece = pieces[i];\n\n    // An empty directory, could be a trailing slash, or just a double \"//\" in the path.\n    if (!piece) {\n      addTrailingSlash = true;\n      continue;\n    }\n\n    // If we encounter a real directory, then we don't need to append anymore.\n    addTrailingSlash = false;\n\n    // A current directory, which we can always drop.\n    if (piece === '.') continue;\n\n    // A parent directory, we need to see if there are any real directories we can pop. Else, we\n    // have an excess of parents, and we'll need to keep the \"..\".\n    if (piece === '..') {\n      if (positive) {\n        addTrailingSlash = true;\n        positive--;\n        pointer--;\n      } else if (rel) {\n        // If we're in a relativePath, then we need to keep the excess parents. Else, in an absolute\n        // URL, protocol relative URL, or an absolute path, we don't need to keep excess.\n        pieces[pointer++] = piece;\n      }\n      continue;\n    }\n\n    // We've encountered a real directory. Move it to the next insertion pointer, which accounts for\n    // any popped or dropped directories.\n    pieces[pointer++] = piece;\n    positive++;\n  }\n\n  let path = '';\n  for (let i = 1; i < pointer; i++) {\n    path += '/' + pieces[i];\n  }\n  if (!path || (addTrailingSlash && !path.endsWith('/..'))) {\n    path += '/';\n  }\n  url.path = path;\n}\n\n/**\n * Attempts to resolve `input` URL/path relative to `base`.\n */\nexport default function resolve(input: string, base: string | undefined): string {\n  if (!input && !base) return '';\n\n  const url = parseUrl(input);\n  let inputType = url.type;\n\n  if (base && inputType !== UrlType.Absolute) {\n    const baseUrl = parseUrl(base);\n    const baseType = baseUrl.type;\n\n    switch (inputType) {\n      case UrlType.Empty:\n        url.hash = baseUrl.hash;\n      // fall through\n\n      case UrlType.Hash:\n        url.query = baseUrl.query;\n      // fall through\n\n      case UrlType.Query:\n      case UrlType.RelativePath:\n        mergePaths(url, baseUrl);\n      // fall through\n\n      case UrlType.AbsolutePath:\n        // The host, user, and port are joined, you can't copy one without the others.\n        url.user = baseUrl.user;\n        url.host = baseUrl.host;\n        url.port = baseUrl.port;\n      // fall through\n\n      case UrlType.SchemeRelative:\n        // The input doesn't have a schema at least, so we need to copy at least that over.\n        url.scheme = baseUrl.scheme;\n    }\n    if (baseType > inputType) inputType = baseType;\n  }\n\n  normalizePath(url, inputType);\n\n  const queryHash = url.query + url.hash;\n  switch (inputType) {\n    // This is impossible, because of the empty checks at the start of the function.\n    // case UrlType.Empty:\n\n    case UrlType.Hash:\n    case UrlType.Query:\n      return queryHash;\n\n    case UrlType.RelativePath: {\n      // The first char is always a \"/\", and we need it to be relative.\n      const path = url.path.slice(1);\n\n      if (!path) return queryHash || '.';\n\n      if (isRelative(base || input) && !isRelative(path)) {\n        // If base started with a leading \".\", or there is no base and input started with a \".\",\n        // then we need to ensure that the relative path starts with a \".\". We don't know if\n        // relative starts with a \"..\", though, so check before prepending.\n        return './' + path + queryHash;\n      }\n\n      return path + queryHash;\n    }\n\n    case UrlType.AbsolutePath:\n      return url.path + queryHash;\n\n    default:\n      return url.scheme + '//' + url.user + url.host + url.port + url.path + queryHash;\n  }\n}\n"], "names": [], "mappings": "AAAA;AACA,MAAM,WAAW,GAAG,gBAAgB,CAAC;AAErC;;;;;;;;;;AAUA,MAAM,QAAQ,GAAG,0EAA0E,CAAC;AAE5F;;;;;;;;;AASA,MAAM,SAAS,GAAG,iEAAiE,CAAC;AAuBpF,SAAS,aAAa,CAAC,KAAa;IAClC,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAa;IACxC,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,cAAc,CAAC,KAAa;IACnC,OAAO,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,SAAS,CAAC,KAAa;IAC9B,OAAO,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,UAAU,CAAC,KAAa;IAC/B,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAa;IACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAE,CAAC;IACpC,OAAO,OAAO,CACZ,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EACd,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EACd,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EACf,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EACd,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CACf,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAE,CAAC;IACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,OAAO,OAAO,CACZ,OAAO,EACP,EAAE,EACF,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EACd,EAAE,EACF,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,EACxC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EACd,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CACf,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CACd,MAAc,EACd,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,KAAa,EACb,IAAY;IAEZ,OAAO;QACL,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;QACL,IAAI;QACJ,IAAI;KACL,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,KAAa;IAC7B,IAAI,mBAAmB,CAAC,KAAK,CAAC,EAAE;QAC9B,MAAM,GAAG,GAAG,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;QAC9C,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC;QAChB,GAAG,CAAC,IAAI,0BAA0B;QAClC,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;QACzB,MAAM,GAAG,GAAG,gBAAgB,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC;QACvD,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC;QAChB,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;QACd,GAAG,CAAC,IAAI,wBAAwB;QAChC,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,SAAS,CAAC,KAAK,CAAC;QAAE,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;IAEjD,IAAI,aAAa,CAAC,KAAK,CAAC;QAAE,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAEzD,MAAM,GAAG,GAAG,gBAAgB,CAAC,iBAAiB,GAAG,KAAK,CAAC,CAAC;IACxD,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC;IAChB,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;IACd,GAAG,CAAC,IAAI,GAAG,KAAK;UACZ,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;;cAEnB,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;;;wBAGT;IAClB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAY;;;IAGrC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC;IACtC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACpC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,UAAU,CAAC,GAAQ,EAAE,IAAS;IACrC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;;;IAI/B,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE;QACpB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;KACtB;SAAM;;QAEL,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;KACpD;AACH,CAAC;AAED;;;;AAIA,SAAS,aAAa,CAAC,GAAQ,EAAE,IAAa;IAC5C,MAAM,GAAG,GAAG,IAAI,yBAAyB;IACzC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;;IAInC,IAAI,OAAO,GAAG,CAAC,CAAC;;;IAIhB,IAAI,QAAQ,GAAG,CAAC,CAAC;;;;IAKjB,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;;QAGxB,IAAI,CAAC,KAAK,EAAE;YACV,gBAAgB,GAAG,IAAI,CAAC;YACxB,SAAS;SACV;;QAGD,gBAAgB,GAAG,KAAK,CAAC;;QAGzB,IAAI,KAAK,KAAK,GAAG;YAAE,SAAS;;;QAI5B,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,IAAI,QAAQ,EAAE;gBACZ,gBAAgB,GAAG,IAAI,CAAC;gBACxB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,CAAC;aACX;iBAAM,IAAI,GAAG,EAAE;;;gBAGd,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC;aAC3B;YACD,SAAS;SACV;;;QAID,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC;QAC1B,QAAQ,EAAE,CAAC;KACZ;IAED,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;QAChC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACzB;IACD,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACxD,IAAI,IAAI,GAAG,CAAC;KACb;IACD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;AAClB,CAAC;AAED;;;SAGwB,OAAO,CAAC,KAAa,EAAE,IAAwB;IACrE,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,CAAC;IAE/B,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5B,IAAI,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC;IAEzB,IAAI,IAAI,IAAI,SAAS,uBAAuB;QAC1C,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;QAE9B,QAAQ,SAAS;YACf;gBACE,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;;YAG1B;gBACE,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;;YAG5B,mBAAmB;YACnB;gBACE,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;;YAG3B;;gBAEE,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;gBACxB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;gBACxB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;;YAG1B;;gBAEE,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC/B;QACD,IAAI,QAAQ,GAAG,SAAS;YAAE,SAAS,GAAG,QAAQ,CAAC;KAChD;IAED,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAE9B,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;IACvC,QAAQ,SAAS;;;QAIf,kBAAkB;QAClB;YACE,OAAO,SAAS,CAAC;QAEnB,2BAA2B;;YAEzB,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAI,CAAC,IAAI;gBAAE,OAAO,SAAS,IAAI,GAAG,CAAC;YAEnC,IAAI,UAAU,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;;;;gBAIlD,OAAO,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC;aAChC;YAED,OAAO,IAAI,GAAG,SAAS,CAAC;SACzB;QAED;YACE,OAAO,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;QAE9B;YACE,OAAO,GAAG,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;KACpF;AACH;;;;"}