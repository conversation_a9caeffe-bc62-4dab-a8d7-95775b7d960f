{"version": 3, "file": "accessibility.js", "sources": ["../../src/accessibleTarget.ts", "../../src/AccessibilityManager.ts"], "sourcesContent": ["import type { DisplayObject } from '@pixi/display';\n\nexport type PointerEvents = 'auto'\n| 'none'\n| 'visiblePainted'\n| 'visibleFill'\n| 'visibleStroke'\n| 'visible'\n| 'painted'\n| 'fill'\n| 'stroke'\n| 'all'\n| 'inherit';\n\nexport interface IAccessibleTarget\n{\n    accessible: boolean;\n    accessibleTitle: string;\n    accessibleHint: string;\n    tabIndex: number;\n    _accessibleActive: boolean;\n    _accessibleDiv: IAccessibleHTMLElement;\n    accessibleType: string;\n    accessiblePointerEvents: PointerEvents;\n    accessibleChildren: boolean;\n    renderId: number;\n}\n\nexport interface IAccessibleHTMLElement extends HTMLElement\n{\n    type?: string;\n    displayObject?: DisplayObject;\n}\n\n/**\n * Default property values of accessible objects\n * used by {@link PIXI.AccessibilityManager}.\n * @private\n * @function accessibleTarget\n * @memberof PIXI\n * @type {object}\n * @example\n *      function MyObject() {}\n *\n *      Object.assign(\n *          MyObject.prototype,\n *          PIXI.accessibleTarget\n *      );\n */\nexport const accessibleTarget: IAccessibleTarget = {\n    /**\n     *  Flag for if the object is accessible. If true AccessibilityManager will overlay a\n     *   shadow div with attributes set\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessible: false,\n\n    /**\n     * Sets the title attribute of the shadow div\n     * If accessibleTitle AND accessibleHint has not been this will default to 'displayObject [tabIndex]'\n     * @member {?string}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessibleTitle: null,\n\n    /**\n     * Sets the aria-label attribute of the shadow div\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessibleHint: null,\n\n    /**\n     * @member {number}\n     * @memberof PIXI.DisplayObject#\n     * @private\n     * @todo Needs docs.\n     */\n    tabIndex: 0,\n\n    /**\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @todo Needs docs.\n     */\n    _accessibleActive: false,\n\n    /**\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @todo Needs docs.\n     */\n    _accessibleDiv: null,\n\n    /**\n     * Specify the type of div the accessible layer is. Screen readers treat the element differently\n     * depending on this type. Defaults to button.\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     * @default 'button'\n     */\n    accessibleType: 'button',\n\n    /**\n     * Specify the pointer-events the accessible div will use\n     * Defaults to auto.\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     * @default 'auto'\n     */\n    accessiblePointerEvents: 'auto',\n\n    /**\n     * Setting to false will prevent any children inside this container to\n     * be accessible. Defaults to true.\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @default true\n     */\n    accessibleChildren: true,\n\n    renderId: -1,\n};\n", "import { DisplayObject } from '@pixi/display';\nimport { isMobile, removeItems } from '@pixi/utils';\nimport { accessibleTarget } from './accessibleTarget';\n\nimport type { Rectangle } from '@pixi/math';\nimport type { Container } from '@pixi/display';\nimport type { Ren<PERSON><PERSON>, AbstractRenderer, ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport type { IAccessibleHTMLElement } from './accessibleTarget';\n\n// add some extra variables to the container..\nDisplayObject.mixin(accessibleTarget);\n\nconst KEY_CODE_TAB = 9;\n\nconst DIV_TOUCH_SIZE = 100;\nconst DIV_TOUCH_POS_X = 0;\nconst DIV_TOUCH_POS_Y = 0;\nconst DIV_TOUCH_ZINDEX = 2;\n\nconst DIV_HOOK_SIZE = 1;\nconst DIV_HOOK_POS_X = -1000;\nconst DIV_HOOK_POS_Y = -1000;\nconst DIV_HOOK_ZINDEX = 2;\n\n/**\n * The Accessibility manager recreates the ability to tab and have content read by screen readers.\n * This is very important as it can possibly help people with disabilities access PixiJS content.\n *\n * A DisplayObject can be made accessible just like it can be made interactive. This manager will map the\n * events as if the mouse was being used, minimizing the effort required to implement.\n *\n * An instance of this class is automatically created by default, and can be found at `renderer.plugins.accessibility`\n * @class\n * @memberof PIXI\n */\nexport class AccessibilityManager\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'accessibility',\n        type: [\n            ExtensionType.RendererPlugin,\n            ExtensionType.CanvasRendererPlugin,\n        ],\n    };\n\n    /** Setting this to true will visually show the divs. */\n    public debug = false;\n\n    /**\n     * The renderer this accessibility manager works for.\n     * @type {PIXI.CanvasRenderer|PIXI.Renderer}\n     */\n    public renderer: AbstractRenderer | Renderer;\n\n    /** Internal variable, see isActive getter. */\n    private _isActive = false;\n\n    /** Internal variable, see isMobileAccessibility getter. */\n    private _isMobileAccessibility = false;\n\n    /** Button element for handling touch hooks. */\n    private _hookDiv: HTMLElement;\n\n    /** This is the dom element that will sit over the PixiJS element. This is where the div overlays will go. */\n    private div: HTMLElement;\n\n    /** A simple pool for storing divs. */\n    private pool: IAccessibleHTMLElement[] = [];\n\n    /** This is a tick used to check if an object is no longer being rendered. */\n    private renderId = 0;\n\n    /** The array of currently active accessible items. */\n    private children: DisplayObject[] = [];\n\n    /** Count to throttle div updates on android devices. */\n    private androidUpdateCount = 0;\n\n    /**  The frequency to update the div elements. */\n    private androidUpdateFrequency = 500; // 2fps\n\n    /**\n     * @param {PIXI.CanvasRenderer|PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: AbstractRenderer | Renderer)\n    {\n        this._hookDiv = null;\n\n        if (isMobile.tablet || isMobile.phone)\n        {\n            this.createTouchHook();\n        }\n\n        // first we create a div that will sit over the PixiJS element. This is where the div overlays will go.\n        const div = document.createElement('div');\n\n        div.style.width = `${DIV_TOUCH_SIZE}px`;\n        div.style.height = `${DIV_TOUCH_SIZE}px`;\n        div.style.position = 'absolute';\n        div.style.top = `${DIV_TOUCH_POS_X}px`;\n        div.style.left = `${DIV_TOUCH_POS_Y}px`;\n        div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n\n        this.div = div;\n        this.renderer = renderer;\n\n        /**\n         * pre-bind the functions\n         * @type {Function}\n         * @private\n         */\n        this._onKeyDown = this._onKeyDown.bind(this);\n\n        /**\n         * pre-bind the functions\n         * @type {Function}\n         * @private\n         */\n        this._onMouseMove = this._onMouseMove.bind(this);\n\n        // let listen for tab.. once pressed we can fire up and show the accessibility layer\n        globalThis.addEventListener('keydown', this._onKeyDown, false);\n    }\n\n    /**\n     * Value of `true` if accessibility is currently active and accessibility layers are showing.\n     * @member {boolean}\n     * @readonly\n     */\n    get isActive(): boolean\n    {\n        return this._isActive;\n    }\n\n    /**\n     * Value of `true` if accessibility is enabled for touch devices.\n     * @member {boolean}\n     * @readonly\n     */\n    get isMobileAccessibility(): boolean\n    {\n        return this._isMobileAccessibility;\n    }\n\n    /**\n     * Creates the touch hooks.\n     * @private\n     */\n    private createTouchHook(): void\n    {\n        const hookDiv = document.createElement('button');\n\n        hookDiv.style.width = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.height = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.position = 'absolute';\n        hookDiv.style.top = `${DIV_HOOK_POS_X}px`;\n        hookDiv.style.left = `${DIV_HOOK_POS_Y}px`;\n        hookDiv.style.zIndex = DIV_HOOK_ZINDEX.toString();\n        hookDiv.style.backgroundColor = '#FF0000';\n        hookDiv.title = 'select to enable accessibility for this content';\n\n        hookDiv.addEventListener('focus', () =>\n        {\n            this._isMobileAccessibility = true;\n            this.activate();\n            this.destroyTouchHook();\n        });\n\n        document.body.appendChild(hookDiv);\n        this._hookDiv = hookDiv;\n    }\n\n    /**\n     * Destroys the touch hooks.\n     * @private\n     */\n    private destroyTouchHook(): void\n    {\n        if (!this._hookDiv)\n        {\n            return;\n        }\n        document.body.removeChild(this._hookDiv);\n        this._hookDiv = null;\n    }\n\n    /**\n     * Activating will cause the Accessibility layer to be shown.\n     * This is called when a user presses the tab key.\n     * @private\n     */\n    private activate(): void\n    {\n        if (this._isActive)\n        {\n            return;\n        }\n\n        this._isActive = true;\n\n        globalThis.document.addEventListener('mousemove', this._onMouseMove, true);\n        globalThis.removeEventListener('keydown', this._onKeyDown, false);\n\n        this.renderer.on('postrender', this.update, this);\n        this.renderer.view.parentNode?.appendChild(this.div);\n    }\n\n    /**\n     * Deactivating will cause the Accessibility layer to be hidden.\n     * This is called when a user moves the mouse.\n     * @private\n     */\n    private deactivate(): void\n    {\n        if (!this._isActive || this._isMobileAccessibility)\n        {\n            return;\n        }\n\n        this._isActive = false;\n\n        globalThis.document.removeEventListener('mousemove', this._onMouseMove, true);\n        globalThis.addEventListener('keydown', this._onKeyDown, false);\n\n        this.renderer.off('postrender', this.update);\n        this.div.parentNode?.removeChild(this.div);\n    }\n\n    /**\n     * This recursive function will run through the scene graph and add any new accessible objects to the DOM layer.\n     * @private\n     * @param {PIXI.Container} displayObject - The DisplayObject to check.\n     */\n    private updateAccessibleObjects(displayObject: Container): void\n    {\n        if (!displayObject.visible || !displayObject.accessibleChildren)\n        {\n            return;\n        }\n\n        if (displayObject.accessible && displayObject.interactive)\n        {\n            if (!displayObject._accessibleActive)\n            {\n                this.addChild(displayObject);\n            }\n\n            displayObject.renderId = this.renderId;\n        }\n\n        const children = displayObject.children;\n\n        if (children)\n        {\n            for (let i = 0; i < children.length; i++)\n            {\n                this.updateAccessibleObjects(children[i] as Container);\n            }\n        }\n    }\n\n    /**\n     * Before each render this function will ensure that all divs are mapped correctly to their DisplayObjects.\n     * @private\n     */\n    private update(): void\n    {\n        /* On Android default web browser, tab order seems to be calculated by position rather than tabIndex,\n        *  moving buttons can cause focus to flicker between two buttons making it hard/impossible to navigate,\n        *  so I am just running update every half a second, seems to fix it.\n        */\n        const now = performance.now();\n\n        if (isMobile.android.device && now < this.androidUpdateCount)\n        {\n            return;\n        }\n\n        this.androidUpdateCount = now + this.androidUpdateFrequency;\n\n        if (!(this.renderer as Renderer).renderingToScreen)\n        {\n            return;\n        }\n\n        // update children...\n        if (this.renderer._lastObjectRendered)\n        {\n            this.updateAccessibleObjects(this.renderer._lastObjectRendered as Container);\n        }\n\n        const { left, top, width, height } = this.renderer.view.getBoundingClientRect();\n        const { width: viewWidth, height: viewHeight, resolution } = this.renderer;\n\n        const sx = (width / viewWidth) * resolution;\n        const sy = (height / viewHeight) * resolution;\n\n        let div = this.div;\n\n        div.style.left = `${left}px`;\n        div.style.top = `${top}px`;\n        div.style.width = `${viewWidth}px`;\n        div.style.height = `${viewHeight}px`;\n\n        for (let i = 0; i < this.children.length; i++)\n        {\n            const child = this.children[i];\n\n            if (child.renderId !== this.renderId)\n            {\n                child._accessibleActive = false;\n\n                removeItems(this.children, i, 1);\n                this.div.removeChild(child._accessibleDiv);\n                this.pool.push(child._accessibleDiv);\n                child._accessibleDiv = null;\n\n                i--;\n            }\n            else\n            {\n                // map div to display..\n                div = child._accessibleDiv;\n                let hitArea = child.hitArea as Rectangle;\n                const wt = child.worldTransform;\n\n                if (child.hitArea)\n                {\n                    div.style.left = `${(wt.tx + (hitArea.x * wt.a)) * sx}px`;\n                    div.style.top = `${(wt.ty + (hitArea.y * wt.d)) * sy}px`;\n\n                    div.style.width = `${hitArea.width * wt.a * sx}px`;\n                    div.style.height = `${hitArea.height * wt.d * sy}px`;\n                }\n                else\n                {\n                    hitArea = child.getBounds();\n\n                    this.capHitArea(hitArea);\n\n                    div.style.left = `${hitArea.x * sx}px`;\n                    div.style.top = `${hitArea.y * sy}px`;\n\n                    div.style.width = `${hitArea.width * sx}px`;\n                    div.style.height = `${hitArea.height * sy}px`;\n\n                    // update button titles and hints if they exist and they've changed\n                    if (div.title !== child.accessibleTitle && child.accessibleTitle !== null)\n                    {\n                        div.title = child.accessibleTitle;\n                    }\n                    if (div.getAttribute('aria-label') !== child.accessibleHint\n                        && child.accessibleHint !== null)\n                    {\n                        div.setAttribute('aria-label', child.accessibleHint);\n                    }\n                }\n\n                // the title or index may have changed, if so lets update it!\n                if (child.accessibleTitle !== div.title || child.tabIndex !== div.tabIndex)\n                {\n                    div.title = child.accessibleTitle;\n                    div.tabIndex = child.tabIndex;\n                    if (this.debug) this.updateDebugHTML(div);\n                }\n            }\n        }\n\n        // increment the render id..\n        this.renderId++;\n    }\n\n    /**\n     * private function that will visually add the information to the\n     * accessability div\n     * @param {HTMLElement} div -\n     */\n    public updateDebugHTML(div: IAccessibleHTMLElement): void\n    {\n        div.innerHTML = `type: ${div.type}</br> title : ${div.title}</br> tabIndex: ${div.tabIndex}`;\n    }\n\n    /**\n     * Adjust the hit area based on the bounds of a display object\n     * @param {PIXI.Rectangle} hitArea - Bounds of the child\n     */\n    public capHitArea(hitArea: Rectangle): void\n    {\n        if (hitArea.x < 0)\n        {\n            hitArea.width += hitArea.x;\n            hitArea.x = 0;\n        }\n\n        if (hitArea.y < 0)\n        {\n            hitArea.height += hitArea.y;\n            hitArea.y = 0;\n        }\n\n        const { width: viewWidth, height: viewHeight } = this.renderer;\n\n        if (hitArea.x + hitArea.width > viewWidth)\n        {\n            hitArea.width = viewWidth - hitArea.x;\n        }\n\n        if (hitArea.y + hitArea.height > viewHeight)\n        {\n            hitArea.height = viewHeight - hitArea.y;\n        }\n    }\n\n    /**\n     * Adds a DisplayObject to the accessibility manager\n     * @private\n     * @param {PIXI.DisplayObject} displayObject - The child to make accessible.\n     */\n    private addChild<T extends DisplayObject>(displayObject: T): void\n    {\n        //    this.activate();\n\n        let div = this.pool.pop();\n\n        if (!div)\n        {\n            div = document.createElement('button');\n\n            div.style.width = `${DIV_TOUCH_SIZE}px`;\n            div.style.height = `${DIV_TOUCH_SIZE}px`;\n            div.style.backgroundColor = this.debug ? 'rgba(255,255,255,0.5)' : 'transparent';\n            div.style.position = 'absolute';\n            div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n            div.style.borderStyle = 'none';\n\n            // ARIA attributes ensure that button title and hint updates are announced properly\n            if (navigator.userAgent.toLowerCase().indexOf('chrome') > -1)\n            {\n                // Chrome doesn't need aria-live to work as intended; in fact it just gets more confused.\n                div.setAttribute('aria-live', 'off');\n            }\n            else\n            {\n                div.setAttribute('aria-live', 'polite');\n            }\n\n            if (navigator.userAgent.match(/rv:.*Gecko\\//))\n            {\n                // FireFox needs this to announce only the new button name\n                div.setAttribute('aria-relevant', 'additions');\n            }\n            else\n            {\n                // required by IE, other browsers don't much care\n                div.setAttribute('aria-relevant', 'text');\n            }\n\n            div.addEventListener('click', this._onClick.bind(this));\n            div.addEventListener('focus', this._onFocus.bind(this));\n            div.addEventListener('focusout', this._onFocusOut.bind(this));\n        }\n\n        // set pointer events\n        div.style.pointerEvents = displayObject.accessiblePointerEvents;\n        // set the type, this defaults to button!\n        div.type = displayObject.accessibleType;\n\n        if (displayObject.accessibleTitle && displayObject.accessibleTitle !== null)\n        {\n            div.title = displayObject.accessibleTitle;\n        }\n        else if (!displayObject.accessibleHint\n                 || displayObject.accessibleHint === null)\n        {\n            div.title = `displayObject ${displayObject.tabIndex}`;\n        }\n\n        if (displayObject.accessibleHint\n            && displayObject.accessibleHint !== null)\n        {\n            div.setAttribute('aria-label', displayObject.accessibleHint);\n        }\n\n        if (this.debug) this.updateDebugHTML(div);\n\n        displayObject._accessibleActive = true;\n        displayObject._accessibleDiv = div;\n        div.displayObject = displayObject;\n\n        this.children.push(displayObject);\n        this.div.appendChild(displayObject._accessibleDiv);\n        displayObject._accessibleDiv.tabIndex = displayObject.tabIndex;\n    }\n\n    /**\n     * Maps the div button press to pixi's InteractionManager (click)\n     * @private\n     * @param {MouseEvent} e - The click event.\n     */\n    private _onClick(e: MouseEvent): void\n    {\n        const interactionManager = this.renderer.plugins.interaction;\n        const { displayObject } = e.target as IAccessibleHTMLElement;\n        const { eventData } = interactionManager;\n\n        interactionManager.dispatchEvent(displayObject, 'click', eventData);\n        interactionManager.dispatchEvent(displayObject, 'pointertap', eventData);\n        interactionManager.dispatchEvent(displayObject, 'tap', eventData);\n    }\n\n    /**\n     * Maps the div focus events to pixi's InteractionManager (mouseover)\n     * @private\n     * @param {FocusEvent} e - The focus event.\n     */\n    private _onFocus(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'assertive');\n        }\n\n        const interactionManager = this.renderer.plugins.interaction;\n        const { displayObject } = e.target as IAccessibleHTMLElement;\n        const { eventData } = interactionManager;\n\n        interactionManager.dispatchEvent(displayObject, 'mouseover', eventData);\n    }\n\n    /**\n     * Maps the div focus events to pixi's InteractionManager (mouseout)\n     * @private\n     * @param {FocusEvent} e - The focusout event.\n     */\n    private _onFocusOut(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'polite');\n        }\n\n        const interactionManager = this.renderer.plugins.interaction;\n        const { displayObject } = e.target as IAccessibleHTMLElement;\n        const { eventData } = interactionManager;\n\n        interactionManager.dispatchEvent(displayObject, 'mouseout', eventData);\n    }\n\n    /**\n     * Is called when a key is pressed\n     * @private\n     * @param {KeyboardEvent} e - The keydown event.\n     */\n    private _onKeyDown(e: KeyboardEvent): void\n    {\n        if (e.keyCode !== KEY_CODE_TAB)\n        {\n            return;\n        }\n\n        this.activate();\n    }\n\n    /**\n     * Is called when the mouse moves across the renderer element\n     * @private\n     * @param {MouseEvent} e - The mouse event.\n     */\n    private _onMouseMove(e: MouseEvent): void\n    {\n        if (e.movementX === 0 && e.movementY === 0)\n        {\n            return;\n        }\n\n        this.deactivate();\n    }\n\n    /** Destroys the accessibility manager */\n    public destroy(): void\n    {\n        this.destroyTouchHook();\n        this.div = null;\n\n        globalThis.document.removeEventListener('mousemove', this._onMouseMove, true);\n        globalThis.removeEventListener('keydown', this._onKeyDown);\n\n        this.pool = null;\n        this.children = null;\n        this.renderer = null;\n    }\n}\n"], "names": ["DisplayObject", "isMobile", "removeItems", "ExtensionType"], "mappings": ";;;;;;;;;;;IAkCA;;;;;;;;;;;;;;IAcG;AACI,QAAM,gBAAgB,GAAsB;IAC/C;;;;;IAKG;IACH,IAAA,UAAU,EAAE,KAAK;IAEjB;;;;;IAKG;IACH,IAAA,eAAe,EAAE,IAAI;IAErB;;;;IAIG;IACH,IAAA,cAAc,EAAE,IAAI;IAEpB;;;;;IAKG;IACH,IAAA,QAAQ,EAAE,CAAC;IAEX;;;;IAIG;IACH,IAAA,iBAAiB,EAAE,KAAK;IAExB;;;;IAIG;IACH,IAAA,cAAc,EAAE,IAAI;IAEpB;;;;;;IAMG;IACH,IAAA,cAAc,EAAE,QAAQ;IAExB;;;;;;IAMG;IACH,IAAA,uBAAuB,EAAE,MAAM;IAE/B;;;;;;IAMG;IACH,IAAA,kBAAkB,EAAE,IAAI;QAExB,QAAQ,EAAE,CAAC,CAAC;;;IChHhB;AACAA,yBAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAEtC,IAAM,YAAY,GAAG,CAAC,CAAC;IAEvB,IAAM,cAAc,GAAG,GAAG,CAAC;IAC3B,IAAM,eAAe,GAAG,CAAC,CAAC;IAC1B,IAAM,eAAe,GAAG,CAAC,CAAC;IAC1B,IAAM,gBAAgB,GAAG,CAAC,CAAC;IAE3B,IAAM,aAAa,GAAG,CAAC,CAAC;IACxB,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC;IAC7B,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC;IAC7B,IAAM,eAAe,GAAG,CAAC,CAAC;IAE1B;;;;;;;;;;IAUG;AACH,QAAA,oBAAA,kBAAA,YAAA;IA+CI;;IAEG;IACH,IAAA,SAAA,oBAAA,CAAY,QAAqC,EAAA;;YAtC1C,IAAK,CAAA,KAAA,GAAG,KAAK,CAAC;;YASb,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;YAGlB,IAAsB,CAAA,sBAAA,GAAG,KAAK,CAAC;;YAS/B,IAAI,CAAA,IAAA,GAA6B,EAAE,CAAC;;YAGpC,IAAQ,CAAA,QAAA,GAAG,CAAC,CAAC;;YAGb,IAAQ,CAAA,QAAA,GAAoB,EAAE,CAAC;;YAG/B,IAAkB,CAAA,kBAAA,GAAG,CAAC,CAAC;;IAGvB,QAAA,IAAA,CAAA,sBAAsB,GAAG,GAAG,CAAC;IAOjC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IAErB,QAAA,IAAIC,cAAQ,CAAC,MAAM,IAAIA,cAAQ,CAAC,KAAK,EACrC;gBACI,IAAI,CAAC,eAAe,EAAE,CAAC;IAC1B,SAAA;;YAGD,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAE1C,QAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,cAAc,OAAI,CAAC;IACxC,QAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,cAAc,OAAI,CAAC;IACzC,QAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;IAChC,QAAA,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,eAAe,OAAI,CAAC;IACvC,QAAA,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,eAAe,OAAI,CAAC;YACxC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;IAE/C,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACf,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAEzB;;;;IAIG;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE7C;;;;IAIG;YACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;YAGjD,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAClE;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,oBAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IALZ;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;;;IAAA,KAAA,CAAA,CAAA;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,oBAAqB,CAAA,SAAA,EAAA,uBAAA,EAAA;IALzB;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,sBAAsB,CAAC;aACtC;;;IAAA,KAAA,CAAA,CAAA;IAED;;;IAGG;IACK,IAAA,oBAAA,CAAA,SAAA,CAAA,eAAe,GAAvB,YAAA;YAAA,IAsBC,KAAA,GAAA,IAAA,CAAA;YApBG,IAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAEjD,QAAA,OAAO,CAAC,KAAK,CAAC,KAAK,GAAM,aAAa,OAAI,CAAC;IAC3C,QAAA,OAAO,CAAC,KAAK,CAAC,MAAM,GAAM,aAAa,OAAI,CAAC;IAC5C,QAAA,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;IACpC,QAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAM,cAAc,OAAI,CAAC;IAC1C,QAAA,OAAO,CAAC,KAAK,CAAC,IAAI,GAAM,cAAc,OAAI,CAAC;YAC3C,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC;IAClD,QAAA,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;IAC1C,QAAA,OAAO,CAAC,KAAK,GAAG,iDAAiD,CAAC;IAElE,QAAA,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAA;IAE9B,YAAA,KAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;gBACnC,KAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,KAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,SAAC,CAAC,CAAC;IAEH,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACnC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SAC3B,CAAA;IAED;;;IAGG;IACK,IAAA,oBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAxB,YAAA;IAEI,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAClB;gBACI,OAAO;IACV,SAAA;YACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB,CAAA;IAED;;;;IAIG;IACK,IAAA,oBAAA,CAAA,SAAA,CAAA,QAAQ,GAAhB,YAAA;;YAEI,IAAI,IAAI,CAAC,SAAS,EAClB;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAEtB,QAAA,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC3E,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAElE,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAClD,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACxD,CAAA;IAED;;;;IAIG;IACK,IAAA,oBAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,YAAA;;YAEI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,sBAAsB,EAClD;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAEvB,QAAA,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC9E,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAE/D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC9C,CAAA;IAED;;;;IAIG;QACK,oBAAuB,CAAA,SAAA,CAAA,uBAAA,GAA/B,UAAgC,aAAwB,EAAA;YAEpD,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAC/D;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,WAAW,EACzD;IACI,YAAA,IAAI,CAAC,aAAa,CAAC,iBAAiB,EACpC;IACI,gBAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IAChC,aAAA;IAED,YAAA,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC1C,SAAA;IAED,QAAA,IAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;IAExC,QAAA,IAAI,QAAQ,EACZ;IACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EACxC;oBACI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAc,CAAC,CAAC;IAC1D,aAAA;IACJ,SAAA;SACJ,CAAA;IAED;;;IAGG;IACK,IAAA,oBAAA,CAAA,SAAA,CAAA,MAAM,GAAd,YAAA;IAEI;;;IAGE;IACF,QAAA,IAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YAE9B,IAAIA,cAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAC5D;gBACI,OAAO;IACV,SAAA;YAED,IAAI,CAAC,kBAAkB,GAAG,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC;IAE5D,QAAA,IAAI,CAAE,IAAI,CAAC,QAAqB,CAAC,iBAAiB,EAClD;gBACI,OAAO;IACV,SAAA;;IAGD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EACrC;gBACI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAgC,CAAC,CAAC;IAChF,SAAA;YAEK,IAAA,EAAA,GAA+B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAvE,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,GAAG,GAAA,EAAA,CAAA,GAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAA+C,CAAC;IAC1E,QAAA,IAAA,EAAuD,GAAA,IAAI,CAAC,QAAQ,EAA3D,SAAS,GAAA,EAAA,CAAA,KAAA,EAAU,UAAU,GAAA,EAAA,CAAA,MAAA,EAAE,UAAU,gBAAkB,CAAC;YAE3E,IAAM,EAAE,GAAG,CAAC,KAAK,GAAG,SAAS,IAAI,UAAU,CAAC;YAC5C,IAAM,EAAE,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,UAAU,CAAC;IAE9C,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IAEnB,QAAA,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,IAAI,OAAI,CAAC;IAC7B,QAAA,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,GAAG,OAAI,CAAC;IAC3B,QAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,SAAS,OAAI,CAAC;IACnC,QAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,UAAU,OAAI,CAAC;IAErC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAC7C;gBACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/B,YAAA,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EACpC;IACI,gBAAA,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAEhCC,iBAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;oBAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACrC,gBAAA,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;IAE5B,gBAAA,CAAC,EAAE,CAAC;IACP,aAAA;IAED,iBAAA;;IAEI,gBAAA,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC;IAC3B,gBAAA,IAAI,OAAO,GAAG,KAAK,CAAC,OAAoB,CAAC;IACzC,gBAAA,IAAM,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC;oBAEhC,IAAI,KAAK,CAAC,OAAO,EACjB;wBACI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,GAAA,IAAI,CAAC;wBAC1D,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,GAAA,IAAI,CAAC;IAEzD,oBAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,OAAI,CAAC;IACnD,oBAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,OAAI,CAAC;IACxD,iBAAA;IAED,qBAAA;IACI,oBAAA,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAE5B,oBAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;wBAEzB,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,OAAO,CAAC,CAAC,GAAG,EAAE,GAAA,IAAI,CAAC;wBACvC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,OAAO,CAAC,CAAC,GAAG,EAAE,GAAA,IAAI,CAAC;wBAEtC,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,OAAO,CAAC,KAAK,GAAG,EAAE,GAAA,IAAI,CAAC;wBAC5C,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,OAAO,CAAC,MAAM,GAAG,EAAE,GAAA,IAAI,CAAC;;IAG9C,oBAAA,IAAI,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,eAAe,KAAK,IAAI,EACzE;IACI,wBAAA,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;IACrC,qBAAA;wBACD,IAAI,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,cAAc;IACpD,2BAAA,KAAK,CAAC,cAAc,KAAK,IAAI,EACpC;4BACI,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;IACxD,qBAAA;IACJ,iBAAA;;IAGD,gBAAA,IAAI,KAAK,CAAC,eAAe,KAAK,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,EAC1E;IACI,oBAAA,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;IAClC,oBAAA,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;wBAC9B,IAAI,IAAI,CAAC,KAAK;IAAE,wBAAA,EAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAA;IAC7C,iBAAA;IACJ,aAAA;IACJ,SAAA;;YAGD,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB,CAAA;IAED;;;;IAIG;QACI,oBAAe,CAAA,SAAA,CAAA,eAAA,GAAtB,UAAuB,GAA2B,EAAA;IAE9C,QAAA,GAAG,CAAC,SAAS,GAAG,QAAA,GAAS,GAAG,CAAC,IAAI,GAAiB,gBAAA,GAAA,GAAG,CAAC,KAAK,GAAA,kBAAA,GAAmB,GAAG,CAAC,QAAU,CAAC;SAChG,CAAA;IAED;;;IAGG;QACI,oBAAU,CAAA,SAAA,CAAA,UAAA,GAAjB,UAAkB,OAAkB,EAAA;IAEhC,QAAA,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EACjB;IACI,YAAA,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC;IAC3B,YAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACjB,SAAA;IAED,QAAA,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EACjB;IACI,YAAA,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;IAC5B,YAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACjB,SAAA;YAEK,IAAA,EAAA,GAA2C,IAAI,CAAC,QAAQ,EAA/C,SAAS,GAAA,EAAA,CAAA,KAAA,EAAU,UAAU,GAAA,EAAA,CAAA,MAAkB,CAAC;YAE/D,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,SAAS,EACzC;gBACI,OAAO,CAAC,KAAK,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC;IACzC,SAAA;YAED,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,UAAU,EAC3C;gBACI,OAAO,CAAC,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC;IAC3C,SAAA;SACJ,CAAA;IAED;;;;IAIG;QACK,oBAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAA0C,aAAgB,EAAA;;YAItD,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YAE1B,IAAI,CAAC,GAAG,EACR;IACI,YAAA,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAEvC,YAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,cAAc,OAAI,CAAC;IACxC,YAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,cAAc,OAAI,CAAC;IACzC,YAAA,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,GAAG,uBAAuB,GAAG,aAAa,CAAC;IACjF,YAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAChC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;IAC/C,YAAA,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;;IAG/B,YAAA,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAC5D;;IAEI,gBAAA,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACxC,aAAA;IAED,iBAAA;IACI,gBAAA,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC3C,aAAA;gBAED,IAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,EAC7C;;IAEI,gBAAA,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;IAClD,aAAA;IAED,iBAAA;;IAEI,gBAAA,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAC7C,aAAA;IAED,YAAA,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,YAAA,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,YAAA,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,SAAA;;YAGD,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC,uBAAuB,CAAC;;IAEhE,QAAA,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC,cAAc,CAAC;YAExC,IAAI,aAAa,CAAC,eAAe,IAAI,aAAa,CAAC,eAAe,KAAK,IAAI,EAC3E;IACI,YAAA,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC,eAAe,CAAC;IAC7C,SAAA;iBACI,IAAI,CAAC,aAAa,CAAC,cAAc;IAC1B,eAAA,aAAa,CAAC,cAAc,KAAK,IAAI,EACjD;IACI,YAAA,GAAG,CAAC,KAAK,GAAG,mBAAiB,aAAa,CAAC,QAAU,CAAC;IACzD,SAAA;YAED,IAAI,aAAa,CAAC,cAAc;IACzB,eAAA,aAAa,CAAC,cAAc,KAAK,IAAI,EAC5C;gBACI,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC;IAChE,SAAA;YAED,IAAI,IAAI,CAAC,KAAK;IAAE,YAAA,EAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAA;IAE1C,QAAA,aAAa,CAAC,iBAAiB,GAAG,IAAI,CAAC;IACvC,QAAA,aAAa,CAAC,cAAc,GAAG,GAAG,CAAC;IACnC,QAAA,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC;IAElC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YACnD,aAAa,CAAC,cAAc,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;SAClE,CAAA;IAED;;;;IAIG;QACK,oBAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAAiB,CAAa,EAAA;YAE1B,IAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IACrD,QAAA,IAAA,aAAa,GAAK,CAAC,CAAC,MAAgC,cAAvC,CAAwC;IACrD,QAAA,IAAA,SAAS,GAAK,kBAAkB,CAAA,SAAvB,CAAwB;YAEzC,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YACpE,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;YACzE,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SACrE,CAAA;IAED;;;;IAIG;QACK,oBAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAAiB,CAAa,EAAA;YAE1B,IAAI,CAAE,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,CAAC,EACpD;gBACK,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAChE,SAAA;YAED,IAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IACrD,QAAA,IAAA,aAAa,GAAK,CAAC,CAAC,MAAgC,cAAvC,CAAwC;IACrD,QAAA,IAAA,SAAS,GAAK,kBAAkB,CAAA,SAAvB,CAAwB;YAEzC,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;SAC3E,CAAA;IAED;;;;IAIG;QACK,oBAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,CAAa,EAAA;YAE7B,IAAI,CAAE,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,CAAC,EACpD;gBACK,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC7D,SAAA;YAED,IAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IACrD,QAAA,IAAA,aAAa,GAAK,CAAC,CAAC,MAAgC,cAAvC,CAAwC;IACrD,QAAA,IAAA,SAAS,GAAK,kBAAkB,CAAA,SAAvB,CAAwB;YAEzC,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;SAC1E,CAAA;IAED;;;;IAIG;QACK,oBAAU,CAAA,SAAA,CAAA,UAAA,GAAlB,UAAmB,CAAgB,EAAA;IAE/B,QAAA,IAAI,CAAC,CAAC,OAAO,KAAK,YAAY,EAC9B;gBACI,OAAO;IACV,SAAA;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB,CAAA;IAED;;;;IAIG;QACK,oBAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,CAAa,EAAA;YAE9B,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,EAC1C;gBACI,OAAO;IACV,SAAA;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;SACrB,CAAA;;IAGM,IAAA,oBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;YAEI,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACxB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IAEhB,QAAA,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC9E,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAE3D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB,CAAA;;IAziBM,IAAA,oBAAA,CAAA,SAAS,GAAsB;IAClC,QAAA,IAAI,EAAE,eAAe;IACrB,QAAA,IAAI,EAAE;IACF,YAAAC,kBAAa,CAAC,cAAc;IAC5B,YAAAA,kBAAa,CAAC,oBAAoB,EACrC;SACJ,CAAC;QAoiBN,OAAC,oBAAA,CAAA;IAAA,CA7iBD,EA6iBC;;;;;;;;;;;;;;;;"}