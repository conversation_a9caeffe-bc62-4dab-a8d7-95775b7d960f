/*!
 * @pixi/sprite - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/sprite is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("@pixi/constants"),e=require("@pixi/core"),i=require("@pixi/display"),r=require("@pixi/math"),s=require("@pixi/settings"),n=require("@pixi/utils"),o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},o(t,e)};var h=new r.Point,u=new Uint16Array([0,1,2,0,2,3]),a=function(a){function c(i){var n=a.call(this)||this;return n._anchor=new r.ObservablePoint(n._onAnchorUpdate,n,i?i.defaultAnchor.x:0,i?i.defaultAnchor.y:0),n._texture=null,n._width=0,n._height=0,n._tint=null,n._tintRGB=null,n.tint=16777215,n.blendMode=t.BLEND_MODES.NORMAL,n._cachedTint=16777215,n.uvs=null,n.texture=i||e.Texture.EMPTY,n.vertexData=new Float32Array(8),n.vertexTrimmedData=null,n._transformID=-1,n._textureID=-1,n._transformTrimmedID=-1,n._textureTrimmedID=-1,n.indices=u,n.pluginName="batch",n.isSprite=!0,n._roundPixels=s.settings.ROUND_PIXELS,n}return function(t,e){function i(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(c,a),c.prototype._onTextureUpdate=function(){this._textureID=-1,this._textureTrimmedID=-1,this._cachedTint=16777215,this._width&&(this.scale.x=n.sign(this.scale.x)*this._width/this._texture.orig.width),this._height&&(this.scale.y=n.sign(this.scale.y)*this._height/this._texture.orig.height)},c.prototype._onAnchorUpdate=function(){this._transformID=-1,this._transformTrimmedID=-1},c.prototype.calculateVertices=function(){var t=this._texture;if(this._transformID!==this.transform._worldID||this._textureID!==t._updateID){this._textureID!==t._updateID&&(this.uvs=this._texture._uvs.uvsFloat32),this._transformID=this.transform._worldID,this._textureID=t._updateID;var e=this.transform.worldTransform,i=e.a,r=e.b,n=e.c,o=e.d,h=e.tx,u=e.ty,a=this.vertexData,c=t.trim,_=t.orig,l=this._anchor,d=0,x=0,p=0,f=0;if(c?(d=(x=c.x-l._x*_.width)+c.width,p=(f=c.y-l._y*_.height)+c.height):(d=(x=-l._x*_.width)+_.width,p=(f=-l._y*_.height)+_.height),a[0]=i*x+n*f+h,a[1]=o*f+r*x+u,a[2]=i*d+n*f+h,a[3]=o*f+r*d+u,a[4]=i*d+n*p+h,a[5]=o*p+r*d+u,a[6]=i*x+n*p+h,a[7]=o*p+r*x+u,this._roundPixels)for(var m=s.settings.RESOLUTION,g=0;g<a.length;++g)a[g]=Math.round((a[g]*m|0)/m)}},c.prototype.calculateTrimmedVertices=function(){if(this.vertexTrimmedData){if(this._transformTrimmedID===this.transform._worldID&&this._textureTrimmedID===this._texture._updateID)return}else this.vertexTrimmedData=new Float32Array(8);this._transformTrimmedID=this.transform._worldID,this._textureTrimmedID=this._texture._updateID;var t=this._texture,e=this.vertexTrimmedData,i=t.orig,r=this._anchor,s=this.transform.worldTransform,n=s.a,o=s.b,h=s.c,u=s.d,a=s.tx,c=s.ty,_=-r._x*i.width,l=_+i.width,d=-r._y*i.height,x=d+i.height;e[0]=n*_+h*d+a,e[1]=u*d+o*_+c,e[2]=n*l+h*d+a,e[3]=u*d+o*l+c,e[4]=n*l+h*x+a,e[5]=u*x+o*l+c,e[6]=n*_+h*x+a,e[7]=u*x+o*_+c},c.prototype._render=function(t){this.calculateVertices(),t.batch.setObjectRenderer(t.plugins[this.pluginName]),t.plugins[this.pluginName].render(this)},c.prototype._calculateBounds=function(){var t=this._texture.trim,e=this._texture.orig;!t||t.width===e.width&&t.height===e.height?(this.calculateVertices(),this._bounds.addQuad(this.vertexData)):(this.calculateTrimmedVertices(),this._bounds.addQuad(this.vertexTrimmedData))},c.prototype.getLocalBounds=function(t){return 0===this.children.length?(this._localBounds||(this._localBounds=new i.Bounds),this._localBounds.minX=this._texture.orig.width*-this._anchor._x,this._localBounds.minY=this._texture.orig.height*-this._anchor._y,this._localBounds.maxX=this._texture.orig.width*(1-this._anchor._x),this._localBounds.maxY=this._texture.orig.height*(1-this._anchor._y),t||(this._localBoundsRect||(this._localBoundsRect=new r.Rectangle),t=this._localBoundsRect),this._localBounds.getRectangle(t)):a.prototype.getLocalBounds.call(this,t)},c.prototype.containsPoint=function(t){this.worldTransform.applyInverse(t,h);var e=this._texture.orig.width,i=this._texture.orig.height,r=-e*this.anchor.x,s=0;return h.x>=r&&h.x<r+e&&(s=-i*this.anchor.y,h.y>=s&&h.y<s+i)},c.prototype.destroy=function(t){if(a.prototype.destroy.call(this,t),this._texture.off("update",this._onTextureUpdate,this),this._anchor=null,"boolean"==typeof t?t:t&&t.texture){var e="boolean"==typeof t?t:t&&t.baseTexture;this._texture.destroy(!!e)}this._texture=null},c.from=function(t,i){return new c(t instanceof e.Texture?t:e.Texture.from(t,i))},Object.defineProperty(c.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(t){this._roundPixels!==t&&(this._transformID=-1),this._roundPixels=t},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"width",{get:function(){return Math.abs(this.scale.x)*this._texture.orig.width},set:function(t){var e=n.sign(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"height",{get:function(){return Math.abs(this.scale.y)*this._texture.orig.height},set:function(t){var e=n.sign(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"anchor",{get:function(){return this._anchor},set:function(t){this._anchor.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t,this._tintRGB=(t>>16)+(65280&t)+((255&t)<<16)},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"texture",{get:function(){return this._texture},set:function(t){this._texture!==t&&(this._texture&&this._texture.off("update",this._onTextureUpdate,this),this._texture=t||e.Texture.EMPTY,this._cachedTint=16777215,this._textureID=-1,this._textureTrimmedID=-1,t&&(t.baseTexture.valid?this._onTextureUpdate():t.once("update",this._onTextureUpdate,this)))},enumerable:!1,configurable:!0}),c}(i.Container);exports.Sprite=a;
//# sourceMappingURL=sprite.min.js.map
