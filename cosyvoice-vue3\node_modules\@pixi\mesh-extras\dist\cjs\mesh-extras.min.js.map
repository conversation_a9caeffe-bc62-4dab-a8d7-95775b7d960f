{"version": 3, "file": "mesh-extras.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/geometry/PlaneGeometry.ts", "../../src/geometry/RopeGeometry.ts", "../../src/SimpleRope.ts", "../../src/SimplePlane.ts", "../../src/SimpleMesh.ts", "../../src/NineSlicePlane.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { MeshGeometry } from '@pixi/mesh';\n\n/**\n * @memberof PIXI\n */\nexport class PlaneGeometry extends MeshGeometry\n{\n    public segWidth: number;\n    public segHeight: number;\n    public width: number;\n    public height: number;\n\n    /**\n     * @param width - The width of the plane.\n     * @param height - The height of the plane.\n     * @param segWidth - Number of horizontal segments.\n     * @param segHeight - Number of vertical segments.\n     */\n    constructor(width = 100, height = 100, segWidth = 10, segHeight = 10)\n    {\n        super();\n\n        this.segWidth = segWidth;\n        this.segHeight = segHeight;\n\n        this.width = width;\n        this.height = height;\n\n        this.build();\n    }\n\n    /**\n     * Refreshes plane coordinates\n     * @private\n     */\n    build(): void\n    {\n        const total = this.segWidth * this.segHeight;\n        const verts = [];\n        const uvs = [];\n        const indices = [];\n\n        const segmentsX = this.segWidth - 1;\n        const segmentsY = this.segHeight - 1;\n\n        const sizeX = (this.width) / segmentsX;\n        const sizeY = (this.height) / segmentsY;\n\n        for (let i = 0; i < total; i++)\n        {\n            const x = (i % this.segWidth);\n            const y = ((i / this.segWidth) | 0);\n\n            verts.push(x * sizeX, y * sizeY);\n            uvs.push(x / segmentsX, y / segmentsY);\n        }\n\n        const totalSub = segmentsX * segmentsY;\n\n        for (let i = 0; i < totalSub; i++)\n        {\n            const xpos = i % segmentsX;\n            const ypos = (i / segmentsX) | 0;\n\n            const value = (ypos * this.segWidth) + xpos;\n            const value2 = (ypos * this.segWidth) + xpos + 1;\n            const value3 = ((ypos + 1) * this.segWidth) + xpos;\n            const value4 = ((ypos + 1) * this.segWidth) + xpos + 1;\n\n            indices.push(value, value2, value3,\n                value2, value4, value3);\n        }\n\n        this.buffers[0].data = new Float32Array(verts);\n        this.buffers[1].data = new Float32Array(uvs);\n        this.indexBuffer.data = new Uint16Array(indices);\n\n        // ensure that the changes are uploaded\n        this.buffers[0].update();\n        this.buffers[1].update();\n        this.indexBuffer.update();\n    }\n}\n", "import { MeshGeometry } from '@pixi/mesh';\nimport type { IPoint } from '@pixi/math';\n\n/**\n * RopeGeometry allows you to draw a geometry across several points and then manipulate these points.\n *\n * ```js\n * for (let i = 0; i < 20; i++) {\n *     points.push(new PIXI.Point(i * 50, 0));\n * };\n * const rope = new PIXI.RopeGeometry(100, points);\n * ```\n * @memberof PIXI\n */\nexport class RopeGeometry extends MeshGeometry\n{\n    /** An array of points that determine the rope. */\n    public points: IPoint[];\n\n    /** Rope texture scale, if zero then the rope texture is stretched. */\n    public readonly textureScale: number;\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    _width: number;\n\n    /**\n     * @param width - The width (i.e., thickness) of the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param textureScale - By default the rope texture will be stretched to match\n     *     rope length. If textureScale is positive this value will be treated as a scaling\n     *     factor and the texture will preserve its aspect ratio instead. To create a tiling rope\n     *     set baseTexture.wrapMode to {@link PIXI.WRAP_MODES.REPEAT} and use a power of two texture,\n     *     then set textureScale=1 to keep the original texture pixel size.\n     *     In order to reduce alpha channel artifacts provide a larger texture and downsample -\n     *     i.e. set textureScale=0.5 to scale it down twice.\n     */\n    constructor(width = 200, points: IPoint[], textureScale = 0)\n    {\n        super(new Float32Array(points.length * 4),\n            new Float32Array(points.length * 4),\n            new Uint16Array((points.length - 1) * 6));\n\n        this.points = points;\n        this._width = width;\n        this.textureScale = textureScale;\n\n        this.build();\n    }\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    /** Refreshes Rope indices and uvs */\n    private build(): void\n    {\n        const points = this.points;\n\n        if (!points) return;\n\n        const vertexBuffer = this.getBuffer('aVertexPosition');\n        const uvBuffer = this.getBuffer('aTextureCoord');\n        const indexBuffer = this.getIndex();\n\n        // if too little points, or texture hasn't got UVs set yet just move on.\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        // if the number of points has changed we will need to recreate the arraybuffers\n        if (vertexBuffer.data.length / 4 !== points.length)\n        {\n            vertexBuffer.data = new Float32Array(points.length * 4);\n            uvBuffer.data = new Float32Array(points.length * 4);\n            indexBuffer.data = new Uint16Array((points.length - 1) * 6);\n        }\n\n        const uvs = uvBuffer.data;\n        const indices = indexBuffer.data;\n\n        uvs[0] = 0;\n        uvs[1] = 0;\n        uvs[2] = 0;\n        uvs[3] = 1;\n\n        let amount = 0;\n        let prev = points[0];\n        const textureWidth = this._width * this.textureScale;\n        const total = points.length; // - 1;\n\n        for (let i = 0; i < total; i++)\n        {\n            // time to do some smart drawing!\n            const index = i * 4;\n\n            if (this.textureScale > 0)\n            {\n                // calculate pixel distance from previous point\n                const dx = prev.x - points[i].x;\n                const dy = prev.y - points[i].y;\n                const distance = Math.sqrt((dx * dx) + (dy * dy));\n\n                prev = points[i];\n                amount += distance / textureWidth;\n            }\n            else\n            {\n                // stretch texture\n                amount = i / (total - 1);\n            }\n\n            uvs[index] = amount;\n            uvs[index + 1] = 0;\n\n            uvs[index + 2] = amount;\n            uvs[index + 3] = 1;\n        }\n\n        let indexCount = 0;\n\n        for (let i = 0; i < total - 1; i++)\n        {\n            const index = i * 2;\n\n            indices[indexCount++] = index;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 2;\n\n            indices[indexCount++] = index + 2;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 3;\n        }\n\n        // ensure that the changes are uploaded\n        uvBuffer.update();\n        indexBuffer.update();\n\n        this.updateVertices();\n    }\n\n    /** refreshes vertices of Rope mesh */\n    public updateVertices(): void\n    {\n        const points = this.points;\n\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        let lastPoint = points[0];\n        let nextPoint;\n        let perpX = 0;\n        let perpY = 0;\n\n        const vertices = this.buffers[0].data;\n        const total = points.length;\n\n        for (let i = 0; i < total; i++)\n        {\n            const point = points[i];\n            const index = i * 4;\n\n            if (i < points.length - 1)\n            {\n                nextPoint = points[i + 1];\n            }\n            else\n            {\n                nextPoint = point;\n            }\n\n            perpY = -(nextPoint.x - lastPoint.x);\n            perpX = nextPoint.y - lastPoint.y;\n\n            let ratio = (1 - (i / (total - 1))) * 10;\n\n            if (ratio > 1)\n            {\n                ratio = 1;\n            }\n\n            const perpLength = Math.sqrt((perpX * perpX) + (perpY * perpY));\n            const num = this.textureScale > 0 ? this.textureScale * this._width / 2 : this._width / 2;\n\n            perpX /= perpLength;\n            perpY /= perpLength;\n\n            perpX *= num;\n            perpY *= num;\n\n            vertices[index] = point.x + perpX;\n            vertices[index + 1] = point.y + perpY;\n            vertices[index + 2] = point.x - perpX;\n            vertices[index + 3] = point.y - perpY;\n\n            lastPoint = point;\n        }\n\n        this.buffers[0].update();\n    }\n\n    public update(): void\n    {\n        if (this.textureScale > 0)\n        {\n            this.build(); // we need to update UVs\n        }\n        else\n        {\n            this.updateVertices();\n        }\n    }\n}\n", "import { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { WRAP_MODES } from '@pixi/constants';\nimport { RopeGeometry } from './geometry/RopeGeometry';\n\nimport type { Texture, Renderer } from '@pixi/core';\nimport type { IPoint } from '@pixi/math';\n\n/**\n * The rope allows you to draw a texture across several points and then manipulate these points\n *\n *```js\n * for (let i = 0; i < 20; i++) {\n *     points.push(new PIXI.Point(i * 50, 0));\n * };\n * let rope = new PIXI.SimpleRope(PIXI.Texture.from(\"snake.png\"), points);\n *  ```\n * @memberof PIXI\n */\nexport class SimpleRope extends Mesh\n{\n    public autoUpdate: boolean;\n\n    /**\n     * @param texture - The texture to use on the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param {number} textureScale - Optional. Positive values scale rope texture\n     * keeping its aspect ratio. You can reduce alpha channel artifacts by providing a larger texture\n     * and downsampling here. If set to zero, texture will be stretched instead.\n     */\n    constructor(texture: Texture, points: IPoint[], textureScale = 0)\n    {\n        const ropeGeometry = new RopeGeometry(texture.height, points, textureScale);\n        const meshMaterial = new MeshMaterial(texture);\n\n        if (textureScale > 0)\n        {\n            // attempt to set UV wrapping, will fail on non-power of two textures\n            texture.baseTexture.wrapMode = WRAP_MODES.REPEAT;\n        }\n        super(ropeGeometry, meshMaterial);\n\n        /**\n         * re-calculate vertices by rope points each frame\n         * @member {boolean}\n         */\n        this.autoUpdate = true;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        const geometry: RopeGeometry = this.geometry as any;\n\n        if (this.autoUpdate || geometry._width !== this.shader.texture.height)\n        {\n            geometry._width = this.shader.texture.height;\n            geometry.update();\n        }\n\n        super._render(renderer);\n    }\n}\n", "import { Texture } from '@pixi/core';\nimport { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { PlaneGeometry } from './geometry/PlaneGeometry';\n\nimport type{ Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * The SimplePlane allows you to draw a texture across several points and then manipulate these points\n *\n *```js\n * for (let i = 0; i < 20; i++) {\n *     points.push(new PIXI.Point(i * 50, 0));\n * };\n * let SimplePlane = new PIXI.SimplePlane(PIXI.Texture.from(\"snake.png\"), points);\n *  ```\n * @memberof PIXI\n */\nexport class SimplePlane extends Mesh\n{\n    /** The geometry is automatically updated when the texture size changes. */\n    public autoResize: boolean;\n\n    protected _textureID: number;\n\n    /**\n     * @param texture - The texture to use on the SimplePlane.\n     * @param verticesX - The number of vertices in the x-axis\n     * @param verticesY - The number of vertices in the y-axis\n     */\n    constructor(texture: Texture, verticesX?: number, verticesY?: number)\n    {\n        const planeGeometry = new PlaneGeometry(texture.width, texture.height, verticesX, verticesY);\n        const meshMaterial = new MeshMaterial(Texture.WHITE);\n\n        super(planeGeometry, meshMaterial);\n\n        // lets call the setter to ensure all necessary updates are performed\n        this.texture = texture;\n        this.autoResize = true;\n    }\n\n    /**\n     * Method used for overrides, to do something in case texture frame was changed.\n     * Meshes based on plane can override it and change more details based on texture.\n     */\n    public textureUpdated(): void\n    {\n        this._textureID = this.shader.texture._updateID;\n\n        const geometry: PlaneGeometry = this.geometry as any;\n        const { width, height } = this.shader.texture;\n\n        if (this.autoResize && (geometry.width !== width || geometry.height !== height))\n        {\n            geometry.width = this.shader.texture.width;\n            geometry.height = this.shader.texture.height;\n            geometry.build();\n        }\n    }\n\n    set texture(value: Texture)\n    {\n        // Track texture same way sprite does.\n        // For generated meshes like NineSlicePlane it can change the geometry.\n        // Unfortunately, this method might not work if you directly change texture in material.\n\n        if (this.shader.texture === value)\n        {\n            return;\n        }\n\n        this.shader.texture = value;\n        this._textureID = -1;\n\n        if (value.baseTexture.valid)\n        {\n            this.textureUpdated();\n        }\n        else\n        {\n            value.once('update', this.textureUpdated, this);\n        }\n    }\n\n    get texture(): Texture\n    {\n        return this.shader.texture;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this._textureID !== this.shader.texture._updateID)\n        {\n            this.textureUpdated();\n        }\n\n        super._render(renderer);\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this.shader.texture.off('update', this.textureUpdated, this);\n        super.destroy(options);\n    }\n}\n", "import { Mesh, MeshGeometry, MeshMaterial } from '@pixi/mesh';\nimport { Texture } from '@pixi/core';\n\nimport type { ITypedArray, <PERSON><PERSON><PERSON><PERSON><PERSON>uffer, Renderer } from '@pixi/core';\nimport type { DRAW_MODES } from '@pixi/constants';\n\n/**\n * The Simple Mesh class mimics Mesh in PixiJS v4, providing easy-to-use constructor arguments.\n * For more robust customization, use {@link PIXI.Mesh}.\n * @memberof PIXI\n */\nexport class SimpleMesh extends Mesh\n{\n    /** Upload vertices buffer each frame. */\n    public autoUpdate: boolean;\n\n    /**\n     * @param texture - The texture to use\n     * @param {Float32Array} [vertices] - if you want to specify the vertices\n     * @param {Float32Array} [uvs] - if you want to specify the uvs\n     * @param {Uint16Array} [indices] - if you want to specify the indices\n     * @param drawMode - the drawMode, can be any of the Mesh.DRAW_MODES consts\n     */\n    constructor(\n        texture: Texture = Texture.EMPTY,\n        vertices?: I<PERSON><PERSON><PERSON><PERSON>uffer,\n        uvs?: IArrayBuffer,\n        indices?: I<PERSON><PERSON>yBuffer,\n        drawMode?: DRAW_MODES\n    )\n    {\n        const geometry = new MeshGeometry(vertices, uvs, indices);\n\n        geometry.getBuffer('aVertexPosition').static = false;\n\n        const meshMaterial = new MeshMaterial(texture);\n\n        super(geometry, meshMaterial, null, drawMode);\n\n        this.autoUpdate = true;\n    }\n\n    /**\n     * Collection of vertices data.\n     * @type {Float32Array}\n     */\n    get vertices(): ITypedArray\n    {\n        return this.geometry.getBuffer('aVertexPosition').data;\n    }\n    set vertices(value: ITypedArray)\n    {\n        this.geometry.getBuffer('aVertexPosition').data = value;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this.autoUpdate)\n        {\n            this.geometry.getBuffer('aVertexPosition').update();\n        }\n\n        super._render(renderer);\n    }\n}\n", "import { Texture } from '@pixi/core';\nimport { SimplePlane } from './SimplePlane';\n\nimport type { ITypedArray } from '@pixi/core';\n\nconst DEFAULT_BORDER_SIZE = 10;\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface NineSlicePlane extends GlobalMixins.NineSlicePlane {}\n\n/**\n * The NineSlicePlane allows you to stretch a texture using 9-slice scaling. The corners will remain unscaled (useful\n * for buttons with rounded corners for example) and the other areas will be scaled horizontally and or vertically\n *\n *```js\n * let Plane9 = new PIXI.NineSlicePlane(PIXI.Texture.from('BoxWithRoundedCorners.png'), 15, 15, 15, 15);\n *  ```\n * <pre>\n *      A                          B\n *    +---+----------------------+---+\n *  C | 1 |          2           | 3 |\n *    +---+----------------------+---+\n *    |   |                      |   |\n *    | 4 |          5           | 6 |\n *    |   |                      |   |\n *    +---+----------------------+---+\n *  D | 7 |          8           | 9 |\n *    +---+----------------------+---+\n *  When changing this objects width and/or height:\n *     areas 1 3 7 and 9 will remain unscaled.\n *     areas 2 and 8 will be stretched horizontally\n *     areas 4 and 6 will be stretched vertically\n *     area 5 will be stretched both horizontally and vertically\n * </pre>\n * @memberof PIXI\n */\nexport class NineSlicePlane extends SimplePlane\n{\n    private _origWidth: number;\n    private _origHeight: number;\n\n    /**\n     * The width of the left column (a).\n     * @private\n     */\n    _leftWidth: number;\n\n    /**\n     * The width of the right column (b)\n     * @private\n     */\n    _rightWidth: number;\n\n    /**\n     * The height of the top row (c)\n     * @private\n     */\n    _topHeight: number;\n\n    /**\n     * The height of the bottom row (d)\n     * @private\n     */\n    _bottomHeight: number;\n\n    /**\n     * @param texture - The texture to use on the NineSlicePlane.\n     * @param {number} [leftWidth=10] - size of the left vertical bar (A)\n     * @param {number} [topHeight=10] - size of the top horizontal bar (C)\n     * @param {number} [rightWidth=10] - size of the right vertical bar (B)\n     * @param {number} [bottomHeight=10] - size of the bottom horizontal bar (D)\n     */\n    constructor(\n        texture: Texture,\n        leftWidth = DEFAULT_BORDER_SIZE,\n        topHeight = DEFAULT_BORDER_SIZE,\n        rightWidth = DEFAULT_BORDER_SIZE,\n        bottomHeight = DEFAULT_BORDER_SIZE\n    )\n    {\n        super(Texture.WHITE, 4, 4);\n\n        this._origWidth = texture.orig.width;\n        this._origHeight = texture.orig.height;\n\n        /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n        this._width = this._origWidth;\n\n        /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n        this._height = this._origHeight;\n\n        this._leftWidth = leftWidth;\n        this._rightWidth = rightWidth;\n        this._topHeight = topHeight;\n        this._bottomHeight = bottomHeight;\n\n        // lets call the setter to ensure all necessary updates are performed\n        this.texture = texture;\n    }\n\n    public textureUpdated(): void\n    {\n        this._textureID = this.shader.texture._updateID;\n        this._refresh();\n    }\n\n    get vertices(): ITypedArray\n    {\n        return this.geometry.getBuffer('aVertexPosition').data;\n    }\n\n    set vertices(value: ITypedArray)\n    {\n        this.geometry.getBuffer('aVertexPosition').data = value;\n    }\n\n    /** Updates the horizontal vertices. */\n    public updateHorizontalVertices(): void\n    {\n        const vertices = this.vertices;\n\n        const scale = this._getMinScale();\n\n        vertices[9] = vertices[11] = vertices[13] = vertices[15] = this._topHeight * scale;\n        vertices[17] = vertices[19] = vertices[21] = vertices[23] = this._height - (this._bottomHeight * scale);\n        vertices[25] = vertices[27] = vertices[29] = vertices[31] = this._height;\n    }\n\n    /** Updates the vertical vertices. */\n    public updateVerticalVertices(): void\n    {\n        const vertices = this.vertices;\n\n        const scale = this._getMinScale();\n\n        vertices[2] = vertices[10] = vertices[18] = vertices[26] = this._leftWidth * scale;\n        vertices[4] = vertices[12] = vertices[20] = vertices[28] = this._width - (this._rightWidth * scale);\n        vertices[6] = vertices[14] = vertices[22] = vertices[30] = this._width;\n    }\n\n    /**\n     * Returns the smaller of a set of vertical and horizontal scale of nine slice corners.\n     * @returns Smaller number of vertical and horizontal scale.\n     */\n    private _getMinScale(): number\n    {\n        const w = this._leftWidth + this._rightWidth;\n        const scaleW = this._width > w ? 1.0 : this._width / w;\n\n        const h = this._topHeight + this._bottomHeight;\n        const scaleH = this._height > h ? 1.0 : this._height / h;\n\n        const scale = Math.min(scaleW, scaleH);\n\n        return scale;\n    }\n\n    /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    set width(value: number)\n    {\n        this._width = value;\n        this._refresh();\n    }\n\n    /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    set height(value: number)\n    {\n        this._height = value;\n        this._refresh();\n    }\n\n    /** The width of the left column. */\n    get leftWidth(): number\n    {\n        return this._leftWidth;\n    }\n\n    set leftWidth(value: number)\n    {\n        this._leftWidth = value;\n        this._refresh();\n    }\n\n    /** The width of the right column. */\n    get rightWidth(): number\n    {\n        return this._rightWidth;\n    }\n\n    set rightWidth(value: number)\n    {\n        this._rightWidth = value;\n        this._refresh();\n    }\n\n    /** The height of the top row. */\n    get topHeight(): number\n    {\n        return this._topHeight;\n    }\n\n    set topHeight(value: number)\n    {\n        this._topHeight = value;\n        this._refresh();\n    }\n\n    /** The height of the bottom row. */\n    get bottomHeight(): number\n    {\n        return this._bottomHeight;\n    }\n\n    set bottomHeight(value: number)\n    {\n        this._bottomHeight = value;\n        this._refresh();\n    }\n\n    /** Refreshes NineSlicePlane coords. All of them. */\n    private _refresh(): void\n    {\n        const texture = this.texture;\n\n        const uvs = this.geometry.buffers[1].data;\n\n        this._origWidth = texture.orig.width;\n        this._origHeight = texture.orig.height;\n\n        const _uvw = 1.0 / this._origWidth;\n        const _uvh = 1.0 / this._origHeight;\n\n        uvs[0] = uvs[8] = uvs[16] = uvs[24] = 0;\n        uvs[1] = uvs[3] = uvs[5] = uvs[7] = 0;\n        uvs[6] = uvs[14] = uvs[22] = uvs[30] = 1;\n        uvs[25] = uvs[27] = uvs[29] = uvs[31] = 1;\n\n        uvs[2] = uvs[10] = uvs[18] = uvs[26] = _uvw * this._leftWidth;\n        uvs[4] = uvs[12] = uvs[20] = uvs[28] = 1 - (_uvw * this._rightWidth);\n        uvs[9] = uvs[11] = uvs[13] = uvs[15] = _uvh * this._topHeight;\n        uvs[17] = uvs[19] = uvs[21] = uvs[23] = 1 - (_uvh * this._bottomHeight);\n\n        this.updateHorizontalVertices();\n        this.updateVerticalVertices();\n\n        this.geometry.buffers[0].update();\n        this.geometry.buffers[1].update();\n    }\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "this", "constructor", "prototype", "create", "PlaneGeometry", "_super", "width", "height", "seg<PERSON>idth", "segHeight", "_this", "build", "total", "verts", "uvs", "indices", "segmentsX", "segmentsY", "sizeX", "sizeY", "i", "x", "y", "push", "totalSub", "xpos", "ypos", "value", "value2", "value3", "value4", "buffers", "data", "Float32Array", "indexBuffer", "Uint16Array", "update", "MeshGeometry", "RopeGeometry", "points", "textureScale", "call", "length", "_width", "defineProperty", "get", "vertexBuffer", "<PERSON><PERSON><PERSON><PERSON>", "uv<PERSON><PERSON><PERSON>", "getIndex", "amount", "prev", "textureWidth", "index", "dx", "dy", "distance", "Math", "sqrt", "indexCount", "updateVertices", "nextPoint", "lastPoint", "perpX", "perpY", "vertices", "point", "perpLength", "num", "SimpleRope", "texture", "ropeGeometry", "meshMaterial", "MeshMaterial", "baseTexture", "wrapMode", "WRAP_MODES", "REPEAT", "autoUpdate", "_render", "renderer", "geometry", "shader", "<PERSON><PERSON>", "SimplePlane", "verticesX", "verticesY", "planeGeometry", "Texture", "WHITE", "autoResize", "textureUpdated", "_textureID", "_updateID", "_a", "set", "valid", "once", "destroy", "options", "off", "<PERSON><PERSON><PERSON>", "drawMode", "EMPTY", "static", "NineSlicePlane", "leftWidth", "topHeight", "rightWidth", "bottomHeight", "_origWidth", "orig", "_origHeight", "_height", "_leftWidth", "_rightWidth", "_topHeight", "_bottomHeight", "_refresh", "updateHorizontalVertices", "scale", "_getMinScale", "updateVerticalVertices", "w", "scaleW", "h", "scaleH", "min", "_uvw", "_uvh"], "mappings": ";;;;;;;qJAgBIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,IAGrB,SAASO,EAAUR,EAAGC,GAEzB,SAASQ,IAAOC,KAAKC,YAAcX,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEY,UAAkB,OAANX,EAAaC,OAAOW,OAAOZ,IAAMQ,EAAGG,UAAYX,EAAEW,UAAW,IAAIH,GCrBnF,IAAAK,EAAA,SAAAC,GAaI,SAAAD,EAAYE,EAAaC,EAAcC,EAAeC,QAA1C,IAAAH,IAAAA,EAAW,UAAE,IAAAC,IAAAA,EAAY,UAAE,IAAAC,IAAAA,EAAa,SAAE,IAAAC,IAAAA,EAAc,IAApE,IAAAC,EAEIL,cASHL,YAPGU,EAAKF,SAAWA,EAChBE,EAAKD,UAAYA,EAEjBC,EAAKJ,MAAQA,EACbI,EAAKH,OAASA,EAEdG,EAAKC,UAsDb,OA7EmCb,EAAYM,EAAAC,GA8B3CD,EAAAF,UAAAS,MAAA,WAaI,IAXA,IAAMC,EAAQZ,KAAKQ,SAAWR,KAAKS,UAC7BI,EAAQ,GACRC,EAAM,GACNC,EAAU,GAEVC,EAAYhB,KAAKQ,SAAW,EAC5BS,EAAYjB,KAAKS,UAAY,EAE7BS,EAASlB,KAAU,MAAIgB,EACvBG,EAASnB,KAAW,OAAIiB,EAErBG,EAAI,EAAGA,EAAIR,EAAOQ,IAC3B,CACI,IAAMC,EAAKD,EAAIpB,KAAKQ,SACdc,EAAMF,EAAIpB,KAAKQ,SAAY,EAEjCK,EAAMU,KAAKF,EAAIH,EAAOI,EAAIH,GAC1BL,EAAIS,KAAKF,EAAIL,EAAWM,EAAIL,GAGhC,IAAMO,EAAWR,EAAYC,EAE7B,IAASG,EAAI,EAAGA,EAAII,EAAUJ,IAC9B,CACI,IAAMK,EAAOL,EAAIJ,EACXU,EAAQN,EAAIJ,EAAa,EAEzBW,EAASD,EAAO1B,KAAKQ,SAAYiB,EACjCG,EAAUF,EAAO1B,KAAKQ,SAAYiB,EAAO,EACzCI,GAAWH,EAAO,GAAK1B,KAAKQ,SAAYiB,EACxCK,GAAWJ,EAAO,GAAK1B,KAAKQ,SAAYiB,EAAO,EAErDV,EAAQQ,KAAKI,EAAOC,EAAQC,EACxBD,EAAQE,EAAQD,GAGxB7B,KAAK+B,QAAQ,GAAGC,KAAO,IAAIC,aAAapB,GACxCb,KAAK+B,QAAQ,GAAGC,KAAO,IAAIC,aAAanB,GACxCd,KAAKkC,YAAYF,KAAO,IAAIG,YAAYpB,GAGxCf,KAAK+B,QAAQ,GAAGK,SAChBpC,KAAK+B,QAAQ,GAAGK,SAChBpC,KAAKkC,YAAYE,UAExBhC,EA7ED,CAAmCiC,gBCSnCC,EAAA,SAAAjC,GAyBI,SAAAiC,EAAYhC,EAAaiC,EAAkBC,QAA/B,IAAAlC,IAAAA,EAAW,UAAoB,IAAAkC,IAAAA,EAAgB,GAA3D,IAAA9B,EAEIL,EAAMoC,KAAAzC,KAAA,IAAIiC,aAA6B,EAAhBM,EAAOG,QAC1B,IAAIT,aAA6B,EAAhBM,EAAOG,QACxB,IAAIP,YAAkC,GAArBI,EAAOG,OAAS,MAOxC1C,YALGU,EAAK6B,OAASA,EACd7B,EAAKiC,OAASrC,EACdI,EAAK8B,aAAeA,EAEpB9B,EAAKC,UA6Kb,OAhNkCb,EAAYwC,EAAAjC,GA0C1Cb,OAAAoD,eAAIN,EAAKpC,UAAA,QAAA,CAAT2C,IAAA,WAEI,OAAO7C,KAAK2C,wCAIRL,EAAApC,UAAAS,MAAR,WAEI,IAAM4B,EAASvC,KAAKuC,OAEpB,GAAKA,EAAL,CAEA,IAAMO,EAAe9C,KAAK+C,UAAU,mBAC9BC,EAAWhD,KAAK+C,UAAU,iBAC1Bb,EAAclC,KAAKiD,WAGzB,KAAIV,EAAOG,OAAS,GAApB,CAMII,EAAad,KAAKU,OAAS,IAAMH,EAAOG,SAExCI,EAAad,KAAO,IAAIC,aAA6B,EAAhBM,EAAOG,QAC5CM,EAAShB,KAAO,IAAIC,aAA6B,EAAhBM,EAAOG,QACxCR,EAAYF,KAAO,IAAIG,YAAkC,GAArBI,EAAOG,OAAS,KAGxD,IAAM5B,EAAMkC,EAAShB,KACfjB,EAAUmB,EAAYF,KAE5BlB,EAAI,GAAK,EACTA,EAAI,GAAK,EACTA,EAAI,GAAK,EACTA,EAAI,GAAK,EAOT,IALA,IAAIoC,EAAS,EACTC,EAAOZ,EAAO,GACZa,EAAepD,KAAK2C,OAAS3C,KAAKwC,aAClC5B,EAAQ2B,EAAOG,OAEZtB,EAAI,EAAGA,EAAIR,EAAOQ,IAC3B,CAEI,IAAMiC,EAAY,EAAJjC,EAEd,GAAIpB,KAAKwC,aAAe,EACxB,CAEI,IAAMc,EAAKH,EAAK9B,EAAIkB,EAAOnB,GAAGC,EACxBkC,EAAKJ,EAAK7B,EAAIiB,EAAOnB,GAAGE,EACxBkC,EAAWC,KAAKC,KAAMJ,EAAKA,EAAOC,EAAKA,GAE7CJ,EAAOZ,EAAOnB,GACd8B,GAAUM,EAAWJ,OAKrBF,EAAS9B,GAAKR,EAAQ,GAG1BE,EAAIuC,GAASH,EACbpC,EAAIuC,EAAQ,GAAK,EAEjBvC,EAAIuC,EAAQ,GAAKH,EACjBpC,EAAIuC,EAAQ,GAAK,EAGrB,IAAIM,EAAa,EAEjB,IAASvC,EAAI,EAAGA,EAAIR,EAAQ,EAAGQ,IAC/B,CACUiC,EAAY,EAAJjC,EAEdL,EAAQ4C,KAAgBN,EACxBtC,EAAQ4C,KAAgBN,EAAQ,EAChCtC,EAAQ4C,KAAgBN,EAAQ,EAEhCtC,EAAQ4C,KAAgBN,EAAQ,EAChCtC,EAAQ4C,KAAgBN,EAAQ,EAChCtC,EAAQ4C,KAAgBN,EAAQ,EAIpCL,EAASZ,SACTF,EAAYE,SAEZpC,KAAK4D,oBAIFtB,EAAApC,UAAA0D,eAAP,WAEI,IAAMrB,EAASvC,KAAKuC,OAEpB,KAAIA,EAAOG,OAAS,GAApB,CAaA,IARA,IACImB,EADAC,EAAYvB,EAAO,GAEnBwB,EAAQ,EACRC,EAAQ,EAENC,EAAWjE,KAAK+B,QAAQ,GAAGC,KAC3BpB,EAAQ2B,EAAOG,OAEZtB,EAAI,EAAGA,EAAIR,EAAOQ,IAC3B,CACI,IAAM8C,EAAQ3B,EAAOnB,GACfiC,EAAY,EAAJjC,EAWd4C,KAPIH,EAFAzC,EAAImB,EAAOG,OAAS,EAERH,EAAOnB,EAAI,GAIX8C,GAGI7C,EAAIyC,EAAUzC,GAClC0C,EAAQF,EAAUvC,EAAIwC,EAAUxC,EAShC,IAAM6C,EAAaV,KAAKC,KAAMK,EAAQA,EAAUC,EAAQA,GAClDI,EAAMpE,KAAKwC,aAAe,EAAIxC,KAAKwC,aAAexC,KAAK2C,OAAS,EAAI3C,KAAK2C,OAAS,EAExFoB,GAASI,EACTH,GAASG,EAETJ,GAASK,EACTJ,GAASI,EAETH,EAASZ,GAASa,EAAM7C,EAAI0C,EAC5BE,EAASZ,EAAQ,GAAKa,EAAM5C,EAAI0C,EAChCC,EAASZ,EAAQ,GAAKa,EAAM7C,EAAI0C,EAChCE,EAASZ,EAAQ,GAAKa,EAAM5C,EAAI0C,EAEhCF,EAAYI,EAGhBlE,KAAK+B,QAAQ,GAAGK,WAGbE,EAAApC,UAAAkC,OAAP,WAEQpC,KAAKwC,aAAe,EAEpBxC,KAAKW,QAILX,KAAK4D,kBAGhBtB,EAhND,CAAkCD,gBCIlCgC,EAAA,SAAAhE,GAWI,SAAAgE,EAAYC,EAAkB/B,EAAkBC,QAAA,IAAAA,IAAAA,EAAgB,GAAhE,IAiBC9B,EAAAV,KAfSuE,EAAe,IAAIjC,EAAagC,EAAQ/D,OAAQgC,EAAQC,GACxDgC,EAAe,IAAIC,eAAaH,UAElC9B,EAAe,IAGf8B,EAAQI,YAAYC,SAAWC,EAAAA,WAAWC,SAE9CnE,EAAAL,EAAMoC,KAAAzC,KAAAuE,EAAcC,IAAcxE,MAM7B8E,YAAa,IAe1B,OA1CgChF,EAAIuE,EAAAhE,GA8BhCgE,EAAOnE,UAAA6E,QAAP,SAAQC,GAEJ,IAAMC,EAAyBjF,KAAKiF,UAEhCjF,KAAK8E,YAAcG,EAAStC,SAAW3C,KAAKkF,OAAOZ,QAAQ/D,UAE3D0E,EAAStC,OAAS3C,KAAKkF,OAAOZ,QAAQ/D,OACtC0E,EAAS7C,UAGb/B,EAAAH,UAAM6E,QAAOtC,KAAAzC,KAACgF,IAErBX,EA1CD,CAAgCc,QCAhCC,EAAA,SAAA/E,GAYI,SAAA+E,EAAYd,EAAkBe,EAAoBC,GAAlD,IAUC5E,EAAAV,KARSuF,EAAgB,IAAInF,EAAckE,EAAQhE,MAAOgE,EAAQ/D,OAAQ8E,EAAWC,GAC5Ed,EAAe,IAAIC,EAAAA,aAAae,EAAOA,QAACC,cAE9C/E,EAAAL,EAAMoC,KAAAzC,KAAAuF,EAAef,IAAcxE,MAG9BsE,QAAUA,EACf5D,EAAKgF,YAAa,IAkE1B,OAvFiC5F,EAAIsF,EAAA/E,GA4B1B+E,EAAAlF,UAAAyF,eAAP,WAEI3F,KAAK4F,WAAa5F,KAAKkF,OAAOZ,QAAQuB,UAEtC,IAAMZ,EAA0BjF,KAAKiF,SAC/Ba,EAAoB9F,KAAKkF,OAAOZ,QAA9BhE,EAAKwF,EAAAxF,MAAEC,EAAMuF,EAAAvF,QAEjBP,KAAK0F,YAAeT,EAAS3E,QAAUA,GAAS2E,EAAS1E,SAAWA,IAEpE0E,EAAS3E,MAAQN,KAAKkF,OAAOZ,QAAQhE,MACrC2E,EAAS1E,OAASP,KAAKkF,OAAOZ,QAAQ/D,OACtC0E,EAAStE,UAIjBnB,OAAAoD,eAAIwC,EAAOlF,UAAA,UAAA,CAwBX2C,IAAA,WAEI,OAAO7C,KAAKkF,OAAOZ,SA1BvByB,IAAA,SAAYpE,GAMJ3B,KAAKkF,OAAOZ,UAAY3C,IAK5B3B,KAAKkF,OAAOZ,QAAU3C,EACtB3B,KAAK4F,YAAc,EAEfjE,EAAM+C,YAAYsB,MAElBhG,KAAK2F,iBAILhE,EAAMsE,KAAK,SAAUjG,KAAK2F,eAAgB3F,wCASlDoF,EAAOlF,UAAA6E,QAAP,SAAQC,GAEAhF,KAAK4F,aAAe5F,KAAKkF,OAAOZ,QAAQuB,WAExC7F,KAAK2F,iBAGTtF,EAAAH,UAAM6E,QAAOtC,KAAAzC,KAACgF,IAGXI,EAAOlF,UAAAgG,QAAd,SAAeC,GAEXnG,KAAKkF,OAAOZ,QAAQ8B,IAAI,SAAUpG,KAAK2F,eAAgB3F,MACvDK,EAAAH,UAAMgG,QAAOzD,KAAAzC,KAACmG,IAErBf,EAvFD,CAAiCD,QCPjCkB,EAAA,SAAAhG,GAYI,SACIgG,EAAA/B,EACAL,EACAnD,EACAC,EACAuF,QAJA,IAAAhC,IAAAA,EAAmBkB,EAAAA,QAAQe,OAD/B,IAiBC7F,EAAAV,KATSiF,EAAW,IAAI5C,EAAYA,aAAC4B,EAAUnD,EAAKC,GAEjDkE,EAASlC,UAAU,mBAAmByD,QAAS,EAE/C,IAAMhC,EAAe,IAAIC,eAAaH,UAEtC5D,EAAAL,EAAAoC,KAAAzC,KAAMiF,EAAUT,EAAc,KAAM8B,IAAUtG,MAEzC8E,YAAa,IAyB1B,OArDgChF,EAAIuG,EAAAhG,GAmChCb,OAAAoD,eAAIyD,EAAQnG,UAAA,WAAA,CAAZ2C,IAAA,WAEI,OAAO7C,KAAKiF,SAASlC,UAAU,mBAAmBf,MAEtD+D,IAAA,SAAapE,GAET3B,KAAKiF,SAASlC,UAAU,mBAAmBf,KAAOL,mCAGtD0E,EAAOnG,UAAA6E,QAAP,SAAQC,GAEAhF,KAAK8E,YAEL9E,KAAKiF,SAASlC,UAAU,mBAAmBX,SAG/C/B,EAAAH,UAAM6E,QAAOtC,KAAAzC,KAACgF,IAErBqB,EArDD,CAAgClB,QCyBhCsB,EAAA,SAAApG,GAoCI,SACIoG,EAAAnC,EACAoC,EACAC,EACAC,EACAC,QAHA,IAAAH,IAAAA,EArEoB,SAsEpB,IAAAC,IAAAA,EAtEoB,SAuEpB,IAAAC,IAAAA,EAvEoB,SAwEpB,IAAAC,IAAAA,EAxEoB,IAmExB,IAQInG,EAAAL,EAAAoC,KAAAzC,KAAMwF,UAAQC,MAAO,EAAG,IAkB3BzF,YAhBGU,EAAKoG,WAAaxC,EAAQyC,KAAKzG,MAC/BI,EAAKsG,YAAc1C,EAAQyC,KAAKxG,OAGhCG,EAAKiC,OAASjC,EAAKoG,WAGnBpG,EAAKuG,QAAUvG,EAAKsG,YAEpBtG,EAAKwG,WAAaR,EAClBhG,EAAKyG,YAAcP,EACnBlG,EAAK0G,WAAaT,EAClBjG,EAAK2G,cAAgBR,EAGrBnG,EAAK4D,QAAUA,IAiKvB,OA9NoCxE,EAAW2G,EAAApG,GAgEpCoG,EAAAvG,UAAAyF,eAAP,WAEI3F,KAAK4F,WAAa5F,KAAKkF,OAAOZ,QAAQuB,UACtC7F,KAAKsH,YAGT9H,OAAAoD,eAAI6D,EAAQvG,UAAA,WAAA,CAAZ2C,IAAA,WAEI,OAAO7C,KAAKiF,SAASlC,UAAU,mBAAmBf,MAGtD+D,IAAA,SAAapE,GAET3B,KAAKiF,SAASlC,UAAU,mBAAmBf,KAAOL,mCAI/C8E,EAAAvG,UAAAqH,yBAAP,WAEI,IAAMtD,EAAWjE,KAAKiE,SAEhBuD,EAAQxH,KAAKyH,eAEnBxD,EAAS,GAAKA,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMjE,KAAKoH,WAAaI,EAC7EvD,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMjE,KAAKiH,QAAWjH,KAAKqH,cAAgBG,EACjGvD,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMjE,KAAKiH,SAI9DR,EAAAvG,UAAAwH,uBAAP,WAEI,IAAMzD,EAAWjE,KAAKiE,SAEhBuD,EAAQxH,KAAKyH,eAEnBxD,EAAS,GAAKA,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMjE,KAAKkH,WAAaM,EAC7EvD,EAAS,GAAKA,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMjE,KAAK2C,OAAU3C,KAAKmH,YAAcK,EAC7FvD,EAAS,GAAKA,EAAS,IAAMA,EAAS,IAAMA,EAAS,IAAMjE,KAAK2C,QAO5D8D,EAAAvG,UAAAuH,aAAR,WAEI,IAAME,EAAI3H,KAAKkH,WAAalH,KAAKmH,YAC3BS,EAAS5H,KAAK2C,OAASgF,EAAI,EAAM3H,KAAK2C,OAASgF,EAE/CE,EAAI7H,KAAKoH,WAAapH,KAAKqH,cAC3BS,EAAS9H,KAAKiH,QAAUY,EAAI,EAAM7H,KAAKiH,QAAUY,EAIvD,OAFcpE,KAAKsE,IAAIH,EAAQE,IAMnCtI,OAAAoD,eAAI6D,EAAKvG,UAAA,QAAA,CAAT2C,IAAA,WAEI,OAAO7C,KAAK2C,QAGhBoD,IAAA,SAAUpE,GAEN3B,KAAK2C,OAAShB,EACd3B,KAAKsH,4CAIT9H,OAAAoD,eAAI6D,EAAMvG,UAAA,SAAA,CAAV2C,IAAA,WAEI,OAAO7C,KAAKiH,SAGhBlB,IAAA,SAAWpE,GAEP3B,KAAKiH,QAAUtF,EACf3B,KAAKsH,4CAIT9H,OAAAoD,eAAI6D,EAASvG,UAAA,YAAA,CAAb2C,IAAA,WAEI,OAAO7C,KAAKkH,YAGhBnB,IAAA,SAAcpE,GAEV3B,KAAKkH,WAAavF,EAClB3B,KAAKsH,4CAIT9H,OAAAoD,eAAI6D,EAAUvG,UAAA,aAAA,CAAd2C,IAAA,WAEI,OAAO7C,KAAKmH,aAGhBpB,IAAA,SAAepE,GAEX3B,KAAKmH,YAAcxF,EACnB3B,KAAKsH,4CAIT9H,OAAAoD,eAAI6D,EAASvG,UAAA,YAAA,CAAb2C,IAAA,WAEI,OAAO7C,KAAKoH,YAGhBrB,IAAA,SAAcpE,GAEV3B,KAAKoH,WAAazF,EAClB3B,KAAKsH,4CAIT9H,OAAAoD,eAAI6D,EAAYvG,UAAA,eAAA,CAAhB2C,IAAA,WAEI,OAAO7C,KAAKqH,eAGhBtB,IAAA,SAAiBpE,GAEb3B,KAAKqH,cAAgB1F,EACrB3B,KAAKsH,4CAIDb,EAAAvG,UAAAoH,SAAR,WAEI,IAAMhD,EAAUtE,KAAKsE,QAEfxD,EAAMd,KAAKiF,SAASlD,QAAQ,GAAGC,KAErChC,KAAK8G,WAAaxC,EAAQyC,KAAKzG,MAC/BN,KAAKgH,YAAc1C,EAAQyC,KAAKxG,OAEhC,IAAMyH,EAAO,EAAMhI,KAAK8G,WAClBmB,EAAO,EAAMjI,KAAKgH,YAExBlG,EAAI,GAAKA,EAAI,GAAKA,EAAI,IAAMA,EAAI,IAAM,EACtCA,EAAI,GAAKA,EAAI,GAAKA,EAAI,GAAKA,EAAI,GAAK,EACpCA,EAAI,GAAKA,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAM,EACvCA,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAM,EAExCA,EAAI,GAAKA,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAMkH,EAAOhI,KAAKkH,WACnDpG,EAAI,GAAKA,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAM,EAAKkH,EAAOhI,KAAKmH,YACxDrG,EAAI,GAAKA,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAMmH,EAAOjI,KAAKoH,WACnDtG,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAM,EAAKmH,EAAOjI,KAAKqH,cAEzDrH,KAAKuH,2BACLvH,KAAK0H,yBAEL1H,KAAKiF,SAASlD,QAAQ,GAAGK,SACzBpC,KAAKiF,SAASlD,QAAQ,GAAGK,UAEhCqE,EA9ND,CAAoCrB"}