{"version": 3, "file": "display.mjs", "sources": ["../../src/settings.ts", "../../src/Bounds.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/DisplayObject.ts", "../../src/Container.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\n\n/**\n * Sets the default value for the container property 'sortableChildren'.\n * If set to true, the container will sort its children by zIndex value\n * when updateTransform() is called, or manually if sortChildren() is called.\n *\n * This actually changes the order of elements in the array, so should be treated\n * as a basic solution that is not performant compared to other solutions,\n * such as @link https://github.com/pixijs/pixi-display\n *\n * Also be aware of that this may not work nicely with the addChildAt() function,\n * as the zIndex sorting may cause the child to automatically sorted to another position.\n * @static\n * @constant\n * @name SORTABLE_CHILDREN\n * @memberof PIXI.settings\n * @type {boolean}\n * @default false\n */\nsettings.SORTABLE_CHILDREN = false;\n\nexport { settings };\n", "import { Rectangle } from '@pixi/math';\n\nimport type { IPointData, Transform, Matrix } from '@pixi/math';\n\n/**\n * 'Builder' pattern for bounds rectangles.\n *\n * This could be called an Axis-Aligned Bounding Box.\n * It is not an actual shape. It is a mutable thing; no 'EMPTY' or those kind of problems.\n * @memberof PIXI\n */\nexport class Bounds\n{\n    /** @default Infinity */\n    public minX: number;\n\n    /** @default Infinity */\n    public minY: number;\n\n    /** @default -Infinity */\n    public maxX: number;\n\n    /** @default -Infinity */\n    public maxY: number;\n\n    public rect: Rectangle;\n\n    /**\n     * It is updated to _boundsID of corresponding object to keep bounds in sync with content.\n     * Updated from outside, thus public modifier.\n     */\n    public updateID: number;\n\n    constructor()\n    {\n        this.minX = Infinity;\n        this.minY = Infinity;\n        this.maxX = -Infinity;\n        this.maxY = -Infinity;\n\n        this.rect = null;\n        this.updateID = -1;\n    }\n\n    /**\n     * Checks if bounds are empty.\n     * @returns - True if empty.\n     */\n    isEmpty(): boolean\n    {\n        return this.minX > this.maxX || this.minY > this.maxY;\n    }\n\n    /** Clears the bounds and resets. */\n    clear(): void\n    {\n        this.minX = Infinity;\n        this.minY = Infinity;\n        this.maxX = -Infinity;\n        this.maxY = -Infinity;\n    }\n\n    /**\n     * Can return Rectangle.EMPTY constant, either construct new rectangle, either use your rectangle\n     * It is not guaranteed that it will return tempRect\n     * @param rect - Temporary object will be used if AABB is not empty\n     * @returns - A rectangle of the bounds\n     */\n    getRectangle(rect?: Rectangle): Rectangle\n    {\n        if (this.minX > this.maxX || this.minY > this.maxY)\n        {\n            return Rectangle.EMPTY;\n        }\n\n        rect = rect || new Rectangle(0, 0, 1, 1);\n\n        rect.x = this.minX;\n        rect.y = this.minY;\n        rect.width = this.maxX - this.minX;\n        rect.height = this.maxY - this.minY;\n\n        return rect;\n    }\n\n    /**\n     * This function should be inlined when its possible.\n     * @param point - The point to add.\n     */\n    addPoint(point: IPointData): void\n    {\n        this.minX = Math.min(this.minX, point.x);\n        this.maxX = Math.max(this.maxX, point.x);\n        this.minY = Math.min(this.minY, point.y);\n        this.maxY = Math.max(this.maxY, point.y);\n    }\n\n    /**\n     * Adds a point, after transformed. This should be inlined when its possible.\n     * @param matrix\n     * @param point\n     */\n    addPointMatrix(matrix: Matrix, point: IPointData): void\n    {\n        const { a, b, c, d, tx, ty } = matrix;\n\n        const x = (a * point.x) + (c * point.y) + tx;\n        const y = (b * point.x) + (d * point.y) + ty;\n\n        this.minX = Math.min(this.minX, x);\n        this.maxX = Math.max(this.maxX, x);\n        this.minY = Math.min(this.minY, y);\n        this.maxY = Math.max(this.maxY, y);\n    }\n\n    /**\n     * Adds a quad, not transformed\n     * @param vertices - The verts to add.\n     */\n    addQuad(vertices: Float32Array): void\n    {\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        let x = vertices[0];\n        let y = vertices[1];\n\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = vertices[2];\n        y = vertices[3];\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = vertices[4];\n        y = vertices[5];\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = vertices[6];\n        y = vertices[7];\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Adds sprite frame, transformed.\n     * @param transform - transform to apply\n     * @param x0 - left X of frame\n     * @param y0 - top Y of frame\n     * @param x1 - right X of frame\n     * @param y1 - bottom Y of frame\n     */\n    addFrame(transform: Transform, x0: number, y0: number, x1: number, y1: number): void\n    {\n        this.addFrameMatrix(transform.worldTransform, x0, y0, x1, y1);\n    }\n\n    /**\n     * Adds sprite frame, multiplied by matrix\n     * @param matrix - matrix to apply\n     * @param x0 - left X of frame\n     * @param y0 - top Y of frame\n     * @param x1 - right X of frame\n     * @param y1 - bottom Y of frame\n     */\n    addFrameMatrix(matrix: Matrix, x0: number, y0: number, x1: number, y1: number): void\n    {\n        const a = matrix.a;\n        const b = matrix.b;\n        const c = matrix.c;\n        const d = matrix.d;\n        const tx = matrix.tx;\n        const ty = matrix.ty;\n\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        let x = (a * x0) + (c * y0) + tx;\n        let y = (b * x0) + (d * y0) + ty;\n\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = (a * x1) + (c * y0) + tx;\n        y = (b * x1) + (d * y0) + ty;\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = (a * x0) + (c * y1) + tx;\n        y = (b * x0) + (d * y1) + ty;\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = (a * x1) + (c * y1) + tx;\n        y = (b * x1) + (d * y1) + ty;\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Adds screen vertices from array\n     * @param vertexData - calculated vertices\n     * @param beginOffset - begin offset\n     * @param endOffset - end offset, excluded\n     */\n    addVertexData(vertexData: Float32Array, beginOffset: number, endOffset: number): void\n    {\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        for (let i = beginOffset; i < endOffset; i += 2)\n        {\n            const x = vertexData[i];\n            const y = vertexData[i + 1];\n\n            minX = x < minX ? x : minX;\n            minY = y < minY ? y : minY;\n            maxX = x > maxX ? x : maxX;\n            maxY = y > maxY ? y : maxY;\n        }\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Add an array of mesh vertices\n     * @param transform - mesh transform\n     * @param vertices - mesh coordinates in array\n     * @param beginOffset - begin offset\n     * @param endOffset - end offset, excluded\n     */\n    addVertices(transform: Transform, vertices: Float32Array, beginOffset: number, endOffset: number): void\n    {\n        this.addVerticesMatrix(transform.worldTransform, vertices, beginOffset, endOffset);\n    }\n\n    /**\n     * Add an array of mesh vertices.\n     * @param matrix - mesh matrix\n     * @param vertices - mesh coordinates in array\n     * @param beginOffset - begin offset\n     * @param endOffset - end offset, excluded\n     * @param padX - x padding\n     * @param padY - y padding\n     */\n    addVerticesMatrix(matrix: Matrix, vertices: Float32Array, beginOffset: number,\n        endOffset: number, padX = 0, padY = padX): void\n    {\n        const a = matrix.a;\n        const b = matrix.b;\n        const c = matrix.c;\n        const d = matrix.d;\n        const tx = matrix.tx;\n        const ty = matrix.ty;\n\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        for (let i = beginOffset; i < endOffset; i += 2)\n        {\n            const rawX = vertices[i];\n            const rawY = vertices[i + 1];\n            const x = (a * rawX) + (c * rawY) + tx;\n            const y = (d * rawY) + (b * rawX) + ty;\n\n            minX = Math.min(minX, x - padX);\n            maxX = Math.max(maxX, x + padX);\n            minY = Math.min(minY, y - padY);\n            maxY = Math.max(maxY, y + padY);\n        }\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Adds other {@link Bounds}.\n     * @param bounds - The Bounds to be added\n     */\n    addBounds(bounds: Bounds): void\n    {\n        const minX = this.minX;\n        const minY = this.minY;\n        const maxX = this.maxX;\n        const maxY = this.maxY;\n\n        this.minX = bounds.minX < minX ? bounds.minX : minX;\n        this.minY = bounds.minY < minY ? bounds.minY : minY;\n        this.maxX = bounds.maxX > maxX ? bounds.maxX : maxX;\n        this.maxY = bounds.maxY > maxY ? bounds.maxY : maxY;\n    }\n\n    /**\n     * Adds other Bounds, masked with Bounds.\n     * @param bounds - The Bounds to be added.\n     * @param mask - TODO\n     */\n    addBoundsMask(bounds: Bounds, mask: Bounds): void\n    {\n        const _minX = bounds.minX > mask.minX ? bounds.minX : mask.minX;\n        const _minY = bounds.minY > mask.minY ? bounds.minY : mask.minY;\n        const _maxX = bounds.maxX < mask.maxX ? bounds.maxX : mask.maxX;\n        const _maxY = bounds.maxY < mask.maxY ? bounds.maxY : mask.maxY;\n\n        if (_minX <= _maxX && _minY <= _maxY)\n        {\n            const minX = this.minX;\n            const minY = this.minY;\n            const maxX = this.maxX;\n            const maxY = this.maxY;\n\n            this.minX = _minX < minX ? _minX : minX;\n            this.minY = _minY < minY ? _minY : minY;\n            this.maxX = _maxX > maxX ? _maxX : maxX;\n            this.maxY = _maxY > maxY ? _maxY : maxY;\n        }\n    }\n\n    /**\n     * Adds other Bounds, multiplied by matrix. Bounds shouldn't be empty.\n     * @param bounds - other bounds\n     * @param matrix - multiplicator\n     */\n    addBoundsMatrix(bounds: Bounds, matrix: Matrix): void\n    {\n        this.addFrameMatrix(matrix, bounds.minX, bounds.minY, bounds.maxX, bounds.maxY);\n    }\n\n    /**\n     * Adds other Bounds, masked with Rectangle.\n     * @param bounds - TODO\n     * @param area - TODO\n     */\n    addBoundsArea(bounds: Bounds, area: Rectangle): void\n    {\n        const _minX = bounds.minX > area.x ? bounds.minX : area.x;\n        const _minY = bounds.minY > area.y ? bounds.minY : area.y;\n        const _maxX = bounds.maxX < area.x + area.width ? bounds.maxX : (area.x + area.width);\n        const _maxY = bounds.maxY < area.y + area.height ? bounds.maxY : (area.y + area.height);\n\n        if (_minX <= _maxX && _minY <= _maxY)\n        {\n            const minX = this.minX;\n            const minY = this.minY;\n            const maxX = this.maxX;\n            const maxY = this.maxY;\n\n            this.minX = _minX < minX ? _minX : minX;\n            this.minY = _minY < minY ? _minY : minY;\n            this.maxX = _maxX > maxX ? _maxX : maxX;\n            this.maxY = _maxY > maxY ? _maxY : maxY;\n        }\n    }\n\n    /**\n     * Pads bounds object, making it grow in all directions.\n     * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n     * @param paddingX - The horizontal padding amount.\n     * @param paddingY - The vertical padding amount.\n     */\n    pad(paddingX = 0, paddingY = paddingX): void\n    {\n        if (!this.isEmpty())\n        {\n            this.minX -= paddingX;\n            this.maxX += paddingX;\n            this.minY -= paddingY;\n            this.maxY += paddingY;\n        }\n    }\n\n    /**\n     * Adds padded frame. (x0, y0) should be strictly less than (x1, y1)\n     * @param x0 - left X of frame\n     * @param y0 - top Y of frame\n     * @param x1 - right X of frame\n     * @param y1 - bottom Y of frame\n     * @param padX - padding X\n     * @param padY - padding Y\n     */\n    addFramePad(x0: number, y0: number, x1: number, y1: number, padX: number, padY: number): void\n    {\n        x0 -= padX;\n        y0 -= padY;\n        x1 += padX;\n        y1 += padY;\n\n        this.minX = this.minX < x0 ? this.minX : x0;\n        this.maxX = this.maxX > x1 ? this.maxX : x1;\n        this.minY = this.minY < y0 ? this.minY : y0;\n        this.maxY = this.maxY > y1 ? this.maxY : y1;\n    }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { DEG_TO_RAD, RAD_TO_DEG, Rectangle, Transform } from '@pixi/math';\nimport { EventEmitter } from '@pixi/utils';\nimport { Bounds } from './Bounds';\n\nimport type { Container } from './Container';\nimport type { Filter, MaskData, Renderer } from '@pixi/core';\nimport type { IPointData, ObservablePoint, Matrix, Point } from '@pixi/math';\nimport type { Dict } from '@pixi/utils';\n\nexport interface IDestroyOptions\n{\n    children?: boolean;\n    texture?: boolean;\n    baseTexture?: boolean;\n}\n\nexport interface DisplayObject extends Omit<GlobalMixins.DisplayObject, keyof EventEmitter>, EventEmitter {}\n\n/**\n * The base class for all objects that are rendered on the screen.\n *\n * This is an abstract class and can not be used on its own; rather it should be extended.\n *\n * ## Display objects implemented in PixiJS\n *\n * | Display Object                  | Description                                                           |\n * | ------------------------------- | --------------------------------------------------------------------- |\n * | {@link PIXI.Container}          | Adds support for `children` to DisplayObject                          |\n * | {@link PIXI.Graphics}           | Shape-drawing display object similar to the Canvas API                |\n * | {@link PIXI.Sprite}             | Draws textures (i.e. images)                                          |\n * | {@link PIXI.Text}               | Draws text using the Canvas API internally                            |\n * | {@link PIXI.BitmapText}         | More scaleable solution for text rendering, reusing glyph textures    |\n * | {@link PIXI.TilingSprite}       | Draws textures/images in a tiled fashion                              |\n * | {@link PIXI.AnimatedSprite}     | Draws an animation of multiple images                                 |\n * | {@link PIXI.Mesh}               | Provides a lower-level API for drawing meshes with custom data        |\n * | {@link PIXI.NineSlicePlane}     | Mesh-related                                                          |\n * | {@link PIXI.SimpleMesh}         | v4-compatible mesh                                                    |\n * | {@link PIXI.SimplePlane}        | Mesh-related                                                          |\n * | {@link PIXI.SimpleRope}         | Mesh-related                                                          |\n *\n * ## Transforms\n *\n * The [transform]{@link DisplayObject#transform} of a display object describes the projection from its\n * local coordinate space to its parent's local coordinate space. The following properties are derived\n * from the transform:\n *\n * <table>\n *   <thead>\n *     <tr>\n *       <th>Property</th>\n *       <th>Description</th>\n *     </tr>\n *   </thead>\n *   <tbody>\n *     <tr>\n *       <td>[pivot]{@link PIXI.DisplayObject#pivot}</td>\n *       <td>\n *         Invariant under rotation, scaling, and skewing. The projection of into the parent's space of the pivot\n *         is equal to position, regardless of the other three transformations. In other words, It is the center of\n *         rotation, scaling, and skewing.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[position]{@link PIXI.DisplayObject#position}</td>\n *       <td>\n *         Translation. This is the position of the [pivot]{@link PIXI.DisplayObject#pivot} in the parent's local\n *         space. The default value of the pivot is the origin (0,0). If the top-left corner of your display object\n *         is (0,0) in its local space, then the position will be its top-left corner in the parent's local space.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[scale]{@link PIXI.DisplayObject#scale}</td>\n *       <td>\n *         Scaling. This will stretch (or compress) the display object's projection. The scale factors are along the\n *         local coordinate axes. In other words, the display object is scaled before rotated or skewed. The center\n *         of scaling is the [pivot]{@link PIXI.DisplayObject#pivot}.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[rotation]{@link PIXI.DisplayObject#rotation}</td>\n *       <td>\n *          Rotation. This will rotate the display object's projection by this angle (in radians).\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[skew]{@link PIXI.DisplayObject#skew}</td>\n *       <td>\n *         <p>Skewing. This can be used to deform a rectangular display object into a parallelogram.</p>\n *         <p>\n *         In PixiJS, skew has a slightly different behaviour than the conventional meaning. It can be\n *         thought of the net rotation applied to the coordinate axes (separately). For example, if \"skew.x\" is\n *         ⍺ and \"skew.y\" is β, then the line x = 0 will be rotated by ⍺ (y = -x*cot⍺) and the line y = 0 will be\n *         rotated by β (y = x*tanβ). A line y = x*tanϴ (i.e. a line at angle ϴ to the x-axis in local-space) will\n *         be rotated by an angle between ⍺ and β.\n *         </p>\n *         <p>\n *         It can be observed that if skew is applied equally to both axes, then it will be equivalent to applying\n *         a rotation. Indeed, if \"skew.x\" = -ϴ and \"skew.y\" = ϴ, it will produce an equivalent of \"rotation\" = ϴ.\n *         </p>\n *         <p>\n *         Another quite interesting observation is that \"skew.x\", \"skew.y\", rotation are communtative operations. Indeed,\n *         because rotation is essentially a careful combination of the two.\n *         </p>\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>angle</td>\n *       <td>Rotation. This is an alias for [rotation]{@link PIXI.DisplayObject#rotation}, but in degrees.</td>\n *     </tr>\n *     <tr>\n *       <td>x</td>\n *       <td>Translation. This is an alias for position.x!</td>\n *     </tr>\n *     <tr>\n *       <td>y</td>\n *       <td>Translation. This is an alias for position.y!</td>\n *     </tr>\n *     <tr>\n *       <td>width</td>\n *       <td>\n *         Implemented in [Container]{@link PIXI.Container}. Scaling. The width property calculates scale.x by dividing\n *         the \"requested\" width by the local bounding box width. It is indirectly an abstraction over scale.x, and there\n *         is no concept of user-defined width.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>height</td>\n *       <td>\n *         Implemented in [Container]{@link PIXI.Container}. Scaling. The height property calculates scale.y by dividing\n *         the \"requested\" height by the local bounding box height. It is indirectly an abstraction over scale.y, and there\n *         is no concept of user-defined height.\n *       </td>\n *     </tr>\n *   </tbody>\n * </table>\n *\n * ## Bounds\n *\n * The bounds of a display object is defined by the minimum axis-aligned rectangle in world space that can fit\n * around it. The abstract `calculateBounds` method is responsible for providing it (and it should use the\n * `worldTransform` to calculate in world space).\n *\n * There are a few additional types of bounding boxes:\n *\n * | Bounds                | Description                                                                              |\n * | --------------------- | ---------------------------------------------------------------------------------------- |\n * | World Bounds          | This is synonymous is the regular bounds described above. See `getBounds()`.             |\n * | Local Bounds          | This the axis-aligned bounding box in the parent's local space. See `getLocalBounds()`.  |\n * | Render Bounds         | The bounds, but including extra rendering effects like filter padding.                   |\n * | Projected Bounds      | The bounds of the projected display object onto the screen. Usually equals world bounds. |\n * | Relative Bounds       | The bounds of a display object when projected onto a ancestor's (or parent's) space.     |\n * | Natural Bounds        | The bounds of an object in its own local space (not parent's space, like in local bounds)|\n * | Content Bounds        | The natural bounds when excluding all children of a `Container`.                         |\n *\n * ### calculateBounds\n *\n * [Container]{@link Container} already implements `calculateBounds` in a manner that includes children.\n *\n * But for a non-Container display object, the `calculateBounds` method must be overridden in order for `getBounds` and\n * `getLocalBounds` to work. This method must write the bounds into `this._bounds`.\n *\n * Generally, the following technique works for most simple cases: take the list of points\n * forming the \"hull\" of the object (i.e. outline of the object's shape), and then add them\n * using {@link PIXI.Bounds#addPointMatrix}.\n *\n * ```js\n * calculateBounds(): void\n * {\n *     const points = [...];\n *\n *     for (let i = 0, j = points.length; i < j; i++)\n *     {\n *         this._bounds.addPointMatrix(this.worldTransform, points[i]);\n *     }\n * }\n * ```\n *\n * You can optimize this for a large number of points by using {@link PIXI.Bounds#addVerticesMatrix} to pass them\n * in one array together.\n *\n * ## Alpha\n *\n * This alpha sets a display object's **relative opacity** w.r.t its parent. For example, if the alpha of a display\n * object is 0.5 and its parent's alpha is 0.5, then it will be rendered with 25% opacity (assuming alpha is not\n * applied on any ancestor further up the chain).\n *\n * The alpha with which the display object will be rendered is called the [worldAlpha]{@link PIXI.DisplayObject#worldAlpha}.\n *\n * ## Renderable vs Visible\n *\n * The `renderable` and `visible` properties can be used to prevent a display object from being rendered to the\n * screen. However, there is a subtle difference between the two. When using `renderable`, the transforms  of the display\n * object (and its children subtree) will continue to be calculated. When using `visible`, the transforms will not\n * be calculated.\n *\n * It is recommended that applications use the `renderable` property for culling. See\n * [@pixi-essentials/cull]{@link https://www.npmjs.com/package/@pixi-essentials/cull} or\n * [pixi-cull]{@link https://www.npmjs.com/package/pixi-cull} for more details.\n *\n * Otherwise, to prevent an object from rendering in the general-purpose sense - `visible` is the property to use. This\n * one is also better in terms of performance.\n * @memberof PIXI\n */\nexport abstract class DisplayObject extends EventEmitter\n{\n    abstract sortDirty: boolean;\n\n    /** The display object container that contains this display object. */\n    public parent: Container;\n\n    /**\n     * The multiplied alpha of the displayObject.\n     * @readonly\n     */\n    public worldAlpha: number;\n\n    /**\n     * World transform and local transform of this object.\n     * This will become read-only later, please do not assign anything there unless you know what are you doing.\n     */\n    public transform: Transform;\n\n    /** The opacity of the object. */\n    public alpha: number;\n\n    /**\n     * The visibility of the object. If false the object will not be drawn, and\n     * the updateTransform function will not be called.\n     *\n     * Only affects recursive calls from parent. You can ask for bounds or call updateTransform manually.\n     */\n    public visible: boolean;\n\n    /**\n     * Can this object be rendered, if false the object will not be drawn but the updateTransform\n     * methods will still be called.\n     *\n     * Only affects recursive calls from parent. You can ask for bounds manually.\n     */\n    public renderable: boolean;\n\n    /**\n     * Should this object be rendered if the bounds of this object are out of frame?\n     *\n     * Culling has no effect on whether updateTransform is called.\n     */\n    public cullable: boolean;\n\n    /**\n     * If set, this shape is used for culling instead of the bounds of this object.\n     * It can improve the culling performance of objects with many children.\n     * The culling area is defined in local space.\n     */\n    public cullArea: Rectangle;\n\n    /**\n     * The area the filter is applied to. This is used as more of an optimization\n     * rather than figuring out the dimensions of the displayObject each frame you can set this rectangle.\n     *\n     * Also works as an interaction mask.\n     */\n    public filterArea: Rectangle;\n\n    /**\n     * Sets the filters for the displayObject.\n     * IMPORTANT: This is a WebGL only feature and will be ignored by the canvas renderer.\n     * To remove filters simply set this property to `'null'`.\n     */\n    public filters: Filter[] | null;\n\n    /** Used to fast check if a sprite is.. a sprite! */\n    public isSprite: boolean;\n\n    /** Does any other displayObject use this object as a mask? */\n    public isMask: boolean;\n\n    /**\n     * Which index in the children array the display component was before the previous zIndex sort.\n     * Used by containers to help sort objects with the same zIndex, by using previous array index as the decider.\n     * @protected\n     */\n    public _lastSortedIndex: number;\n\n    /**\n     * The original, cached mask of the object.\n     * @protected\n     */\n    public _mask: Container | MaskData;\n\n    /** The bounds object, this is used to calculate and store the bounds of the displayObject. */\n    public _bounds: Bounds;\n\n    /** Local bounds object, swapped with `_bounds` when using `getLocalBounds()`. */\n    public _localBounds: Bounds;\n\n    /**\n     * The zIndex of the displayObject.\n     * A higher value will mean it will be rendered on top of other displayObjects within the same container.\n     * @protected\n     */\n    protected _zIndex: number;\n\n    /**\n     * Currently enabled filters.\n     * @protected\n     */\n    protected _enabledFilters: Filter[];\n\n    /** Flags the cached bounds as dirty. */\n    protected _boundsID: number;\n\n    /** Cache of this display-object's bounds-rectangle. */\n    protected _boundsRect: Rectangle;\n\n    /** Cache of this display-object's local-bounds rectangle. */\n    protected _localBoundsRect: Rectangle;\n\n    /** If the object has been destroyed via destroy(). If true, it should not be used. */\n    protected _destroyed: boolean;\n\n    /** The number of times this object is used as a mask by another object. */\n    private _maskRefCount: number;\n    private tempDisplayObjectParent: TemporaryDisplayObject;\n    public displayObjectUpdateTransform: () => void;\n\n    /**\n     * Mixes all enumerable properties and methods from a source object to DisplayObject.\n     * @param source - The source of properties and methods to mix in.\n     */\n    static mixin(source: Dict<any>): void\n    {\n        // in ES8/ES2017, this would be really easy:\n        // Object.defineProperties(DisplayObject.prototype, Object.getOwnPropertyDescriptors(source));\n\n        // get all the enumerable property keys\n        const keys = Object.keys(source);\n\n        // loop through properties\n        for (let i = 0; i < keys.length; ++i)\n        {\n            const propertyName = keys[i];\n\n            // Set the property using the property descriptor - this works for accessors and normal value properties\n            Object.defineProperty(\n                DisplayObject.prototype,\n                propertyName,\n                Object.getOwnPropertyDescriptor(source, propertyName)\n            );\n        }\n    }\n\n    constructor()\n    {\n        super();\n\n        this.tempDisplayObjectParent = null;\n\n        // TODO: need to create Transform from factory\n        this.transform = new Transform();\n        this.alpha = 1;\n        this.visible = true;\n        this.renderable = true;\n        this.cullable = false;\n        this.cullArea = null;\n\n        this.parent = null;\n        this.worldAlpha = 1;\n\n        this._lastSortedIndex = 0;\n        this._zIndex = 0;\n\n        this.filterArea = null;\n        this.filters = null;\n        this._enabledFilters = null;\n\n        this._bounds = new Bounds();\n        this._localBounds = null;\n        this._boundsID = 0;\n        this._boundsRect = null;\n        this._localBoundsRect = null;\n        this._mask = null;\n        this._maskRefCount = 0;\n        this._destroyed = false;\n\n        this.isSprite = false;\n        this.isMask = false;\n    }\n\n    /**\n     * Fired when this DisplayObject is added to a Container.\n     * @instance\n     * @event added\n     * @param {PIXI.Container} container - The container added to.\n     */\n\n    /**\n     * Fired when this DisplayObject is removed from a Container.\n     * @instance\n     * @event removed\n     * @param {PIXI.Container} container - The container removed from.\n     */\n\n    /**\n     * Fired when this DisplayObject is destroyed. This event is emitted once\n     * destroy is finished.\n     * @instance\n     * @event destroyed\n     */\n\n    /** Readonly flag for destroyed display objects. */\n    get destroyed(): boolean\n    {\n        return this._destroyed;\n    }\n\n    /** Recalculates the bounds of the display object. */\n    abstract calculateBounds(): void;\n\n    abstract removeChild(child: DisplayObject): void;\n\n    /**\n     * Renders the object using the WebGL renderer.\n     * @param renderer - The renderer.\n     */\n    abstract render(renderer: Renderer): void;\n\n    /** Recursively updates transform of all objects from the root to this one internal function for toLocal() */\n    protected _recursivePostUpdateTransform(): void\n    {\n        if (this.parent)\n        {\n            this.parent._recursivePostUpdateTransform();\n            this.transform.updateTransform(this.parent.transform);\n        }\n        else\n        {\n            this.transform.updateTransform(this._tempDisplayObjectParent.transform);\n        }\n    }\n\n    /** Updates the object transform for rendering. TODO - Optimization pass! */\n    updateTransform(): void\n    {\n        this._boundsID++;\n\n        this.transform.updateTransform(this.parent.transform);\n        // multiply the alphas..\n        this.worldAlpha = this.alpha * this.parent.worldAlpha;\n    }\n\n    /**\n     * Calculates and returns the (world) bounds of the display object as a [Rectangle]{@link PIXI.Rectangle}.\n     *\n     * This method is expensive on containers with a large subtree (like the stage). This is because the bounds\n     * of a container depend on its children's bounds, which recursively causes all bounds in the subtree to\n     * be recalculated. The upside, however, is that calling `getBounds` once on a container will indeed update\n     * the bounds of all children (the whole subtree, in fact). This side effect should be exploited by using\n     * `displayObject._bounds.getRectangle()` when traversing through all the bounds in a scene graph. Otherwise,\n     * calling `getBounds` on each object in a subtree will cause the total cost to increase quadratically as\n     * its height increases.\n     *\n     * The transforms of all objects in a container's **subtree** and of all **ancestors** are updated.\n     * The world bounds of all display objects in a container's **subtree** will also be recalculated.\n     *\n     * The `_bounds` object stores the last calculation of the bounds. You can use to entirely skip bounds\n     * calculation if needed.\n     *\n     * ```js\n     * const lastCalculatedBounds = displayObject._bounds.getRectangle(optionalRect);\n     * ```\n     *\n     * Do know that usage of `getLocalBounds` can corrupt the `_bounds` of children (the whole subtree, actually). This\n     * is a known issue that has not been solved. See [getLocalBounds]{@link PIXI.DisplayObject#getLocalBounds} for more\n     * details.\n     *\n     * `getBounds` should be called with `skipUpdate` equal to `true` in a render() call. This is because the transforms\n     * are guaranteed to be update-to-date. In fact, recalculating inside a render() call may cause corruption in certain\n     * cases.\n     * @param skipUpdate - Setting to `true` will stop the transforms of the scene graph from\n     *  being updated. This means the calculation returned MAY be out of date BUT will give you a\n     *  nice performance boost.\n     * @param rect - Optional rectangle to store the result of the bounds calculation.\n     * @returns - The minimum axis-aligned rectangle in world space that fits around this object.\n     */\n    getBounds(skipUpdate?: boolean, rect?: Rectangle): Rectangle\n    {\n        if (!skipUpdate)\n        {\n            if (!this.parent)\n            {\n                this.parent = this._tempDisplayObjectParent as Container;\n                this.updateTransform();\n                this.parent = null;\n            }\n            else\n            {\n                this._recursivePostUpdateTransform();\n                this.updateTransform();\n            }\n        }\n\n        if (this._bounds.updateID !== this._boundsID)\n        {\n            this.calculateBounds();\n            this._bounds.updateID = this._boundsID;\n        }\n\n        if (!rect)\n        {\n            if (!this._boundsRect)\n            {\n                this._boundsRect = new Rectangle();\n            }\n\n            rect = this._boundsRect;\n        }\n\n        return this._bounds.getRectangle(rect);\n    }\n\n    /**\n     * Retrieves the local bounds of the displayObject as a rectangle object.\n     * @param rect - Optional rectangle to store the result of the bounds calculation.\n     * @returns - The rectangular bounding area.\n     */\n    getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        if (!rect)\n        {\n            if (!this._localBoundsRect)\n            {\n                this._localBoundsRect = new Rectangle();\n            }\n\n            rect = this._localBoundsRect;\n        }\n\n        if (!this._localBounds)\n        {\n            this._localBounds = new Bounds();\n        }\n\n        const transformRef = this.transform;\n        const parentRef = this.parent;\n\n        this.parent = null;\n        this.transform = this._tempDisplayObjectParent.transform;\n\n        const worldBounds = this._bounds;\n        const worldBoundsID = this._boundsID;\n\n        this._bounds = this._localBounds;\n\n        const bounds = this.getBounds(false, rect);\n\n        this.parent = parentRef;\n        this.transform = transformRef;\n\n        this._bounds = worldBounds;\n        this._bounds.updateID += this._boundsID - worldBoundsID;// reflect side-effects\n\n        return bounds;\n    }\n\n    /**\n     * Calculates the global position of the display object.\n     * @param position - The world origin to calculate from.\n     * @param point - A Point object in which to store the value, optional\n     *  (otherwise will create a new Point).\n     * @param skipUpdate - Should we skip the update transform.\n     * @returns - A point object representing the position of this object.\n     */\n    toGlobal<P extends IPointData = Point>(position: IPointData, point?: P, skipUpdate = false): P\n    {\n        if (!skipUpdate)\n        {\n            this._recursivePostUpdateTransform();\n\n            // this parent check is for just in case the item is a root object.\n            // If it is we need to give it a temporary parent so that displayObjectUpdateTransform works correctly\n            // this is mainly to avoid a parent check in the main loop. Every little helps for performance :)\n            if (!this.parent)\n            {\n                this.parent = this._tempDisplayObjectParent as Container;\n                this.displayObjectUpdateTransform();\n                this.parent = null;\n            }\n            else\n            {\n                this.displayObjectUpdateTransform();\n            }\n        }\n\n        // don't need to update the lot\n        return this.worldTransform.apply<P>(position, point);\n    }\n\n    /**\n     * Calculates the local position of the display object relative to another point.\n     * @param position - The world origin to calculate from.\n     * @param from - The DisplayObject to calculate the global position from.\n     * @param point - A Point object in which to store the value, optional\n     *  (otherwise will create a new Point).\n     * @param skipUpdate - Should we skip the update transform\n     * @returns - A point object representing the position of this object\n     */\n    toLocal<P extends IPointData = Point>(position: IPointData, from?: DisplayObject, point?: P, skipUpdate?: boolean): P\n    {\n        if (from)\n        {\n            position = from.toGlobal(position, point, skipUpdate);\n        }\n\n        if (!skipUpdate)\n        {\n            this._recursivePostUpdateTransform();\n\n            // this parent check is for just in case the item is a root object.\n            // If it is we need to give it a temporary parent so that displayObjectUpdateTransform works correctly\n            // this is mainly to avoid a parent check in the main loop. Every little helps for performance :)\n            if (!this.parent)\n            {\n                this.parent = this._tempDisplayObjectParent as Container;\n                this.displayObjectUpdateTransform();\n                this.parent = null;\n            }\n            else\n            {\n                this.displayObjectUpdateTransform();\n            }\n        }\n\n        // simply apply the matrix..\n        return this.worldTransform.applyInverse<P>(position, point);\n    }\n\n    /**\n     * Set the parent Container of this DisplayObject.\n     * @param container - The Container to add this DisplayObject to.\n     * @returns - The Container that this DisplayObject was added to.\n     */\n    setParent(container: Container): Container\n    {\n        if (!container || !container.addChild)\n        {\n            throw new Error('setParent: Argument must be a Container');\n        }\n\n        container.addChild(this);\n\n        return container;\n    }\n\n    /**\n     * Convenience function to set the position, scale, skew and pivot at once.\n     * @param x - The X position\n     * @param y - The Y position\n     * @param scaleX - The X scale value\n     * @param scaleY - The Y scale value\n     * @param rotation - The rotation\n     * @param skewX - The X skew value\n     * @param skewY - The Y skew value\n     * @param pivotX - The X pivot value\n     * @param pivotY - The Y pivot value\n     * @returns - The DisplayObject instance\n     */\n    setTransform(x = 0, y = 0, scaleX = 1, scaleY = 1, rotation = 0, skewX = 0, skewY = 0, pivotX = 0, pivotY = 0): this\n    {\n        this.position.x = x;\n        this.position.y = y;\n        this.scale.x = !scaleX ? 1 : scaleX;\n        this.scale.y = !scaleY ? 1 : scaleY;\n        this.rotation = rotation;\n        this.skew.x = skewX;\n        this.skew.y = skewY;\n        this.pivot.x = pivotX;\n        this.pivot.y = pivotY;\n\n        return this;\n    }\n\n    /**\n     * Base destroy method for generic display objects. This will automatically\n     * remove the display object from its parent Container as well as remove\n     * all current event listeners and internal references. Do not use a DisplayObject\n     * after calling `destroy()`.\n     * @param _options\n     */\n    destroy(_options?: IDestroyOptions | boolean): void\n    {\n        if (this.parent)\n        {\n            this.parent.removeChild(this);\n        }\n        this._destroyed = true;\n        this.transform = null;\n\n        this.parent = null;\n        this._bounds = null;\n        this.mask = null;\n\n        this.cullArea = null;\n        this.filters = null;\n        this.filterArea = null;\n        this.hitArea = null;\n\n        this.interactive = false;\n        this.interactiveChildren = false;\n\n        this.emit('destroyed');\n        this.removeAllListeners();\n    }\n\n    /**\n     * @protected\n     * @member {PIXI.Container}\n     */\n    get _tempDisplayObjectParent(): TemporaryDisplayObject\n    {\n        if (this.tempDisplayObjectParent === null)\n        {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            this.tempDisplayObjectParent = new TemporaryDisplayObject();\n        }\n\n        return this.tempDisplayObjectParent;\n    }\n\n    /**\n     * Used in Renderer, cacheAsBitmap and other places where you call an `updateTransform` on root\n     *\n     * ```\n     * const cacheParent = elem.enableTempParent();\n     * elem.updateTransform();\n     * elem.disableTempParent(cacheParent);\n     * ```\n     * @returns - current parent\n     */\n    enableTempParent(): Container\n    {\n        const myParent = this.parent;\n\n        this.parent = this._tempDisplayObjectParent as Container;\n\n        return myParent;\n    }\n\n    /**\n     * Pair method for `enableTempParent`\n     * @param cacheParent - Actual parent of element\n     */\n    disableTempParent(cacheParent: Container): void\n    {\n        this.parent = cacheParent;\n    }\n\n    /**\n     * The position of the displayObject on the x axis relative to the local coordinates of the parent.\n     * An alias to position.x\n     */\n    get x(): number\n    {\n        return this.position.x;\n    }\n\n    set x(value: number)\n    {\n        this.transform.position.x = value;\n    }\n\n    /**\n     * The position of the displayObject on the y axis relative to the local coordinates of the parent.\n     * An alias to position.y\n     */\n    get y(): number\n    {\n        return this.position.y;\n    }\n\n    set y(value: number)\n    {\n        this.transform.position.y = value;\n    }\n\n    /**\n     * Current transform of the object based on world (parent) factors.\n     * @readonly\n     */\n    get worldTransform(): Matrix\n    {\n        return this.transform.worldTransform;\n    }\n\n    /**\n     * Current transform of the object based on local factors: position, scale, other stuff.\n     * @readonly\n     */\n    get localTransform(): Matrix\n    {\n        return this.transform.localTransform;\n    }\n\n    /**\n     * The coordinate of the object relative to the local coordinates of the parent.\n     * @since 4.0.0\n     */\n    get position(): ObservablePoint\n    {\n        return this.transform.position;\n    }\n\n    set position(value: IPointData)\n    {\n        this.transform.position.copyFrom(value);\n    }\n\n    /**\n     * The scale factors of this object along the local coordinate axes.\n     *\n     * The default scale is (1, 1).\n     * @since 4.0.0\n     */\n    get scale(): ObservablePoint\n    {\n        return this.transform.scale;\n    }\n\n    set scale(value: IPointData)\n    {\n        this.transform.scale.copyFrom(value);\n    }\n\n    /**\n     * The center of rotation, scaling, and skewing for this display object in its local space. The `position`\n     * is the projection of `pivot` in the parent's local space.\n     *\n     * By default, the pivot is the origin (0, 0).\n     * @since 4.0.0\n     */\n    get pivot(): ObservablePoint\n    {\n        return this.transform.pivot;\n    }\n\n    set pivot(value: IPointData)\n    {\n        this.transform.pivot.copyFrom(value);\n    }\n\n    /**\n     * The skew factor for the object in radians.\n     * @since 4.0.0\n     */\n    get skew(): ObservablePoint\n    {\n        return this.transform.skew;\n    }\n\n    set skew(value: IPointData)\n    {\n        this.transform.skew.copyFrom(value);\n    }\n\n    /**\n     * The rotation of the object in radians.\n     * 'rotation' and 'angle' have the same effect on a display object; rotation is in radians, angle is in degrees.\n     */\n    get rotation(): number\n    {\n        return this.transform.rotation;\n    }\n\n    set rotation(value: number)\n    {\n        this.transform.rotation = value;\n    }\n\n    /**\n     * The angle of the object in degrees.\n     * 'rotation' and 'angle' have the same effect on a display object; rotation is in radians, angle is in degrees.\n     */\n    get angle(): number\n    {\n        return this.transform.rotation * RAD_TO_DEG;\n    }\n\n    set angle(value: number)\n    {\n        this.transform.rotation = value * DEG_TO_RAD;\n    }\n\n    /**\n     * The zIndex of the displayObject.\n     *\n     * If a container has the sortableChildren property set to true, children will be automatically\n     * sorted by zIndex value; a higher value will mean it will be moved towards the end of the array,\n     * and thus rendered on top of other display objects within the same container.\n     * @see PIXI.Container#sortableChildren\n     */\n    get zIndex(): number\n    {\n        return this._zIndex;\n    }\n\n    set zIndex(value: number)\n    {\n        this._zIndex = value;\n        if (this.parent)\n        {\n            this.parent.sortDirty = true;\n        }\n    }\n\n    /**\n     * Indicates if the object is globally visible.\n     * @readonly\n     */\n    get worldVisible(): boolean\n    {\n        let item = this as DisplayObject;\n\n        do\n        {\n            if (!item.visible)\n            {\n                return false;\n            }\n\n            item = item.parent;\n        } while (item);\n\n        return true;\n    }\n\n    /**\n     * Sets a mask for the displayObject. A mask is an object that limits the visibility of an\n     * object to the shape of the mask applied to it. In PixiJS a regular mask must be a\n     * {@link PIXI.Graphics} or a {@link PIXI.Sprite} object. This allows for much faster masking in canvas as it\n     * utilities shape clipping. Furthermore, a mask of an object must be in the subtree of its parent.\n     * Otherwise, `getLocalBounds` may calculate incorrect bounds, which makes the container's width and height wrong.\n     * To remove a mask, set this property to `null`.\n     *\n     * For sprite mask both alpha and red channel are used. Black mask is the same as transparent mask.\n     * @example\n     * const graphics = new PIXI.Graphics();\n     * graphics.beginFill(0xFF3300);\n     * graphics.drawRect(50, 250, 100, 100);\n     * graphics.endFill();\n     *\n     * const sprite = new PIXI.Sprite(texture);\n     * sprite.mask = graphics;\n     * @todo At the moment, PIXI.CanvasRenderer doesn't support PIXI.Sprite as mask.\n     */\n    get mask(): Container | MaskData | null\n    {\n        return this._mask;\n    }\n\n    set mask(value: Container | MaskData | null)\n    {\n        if (this._mask === value)\n        {\n            return;\n        }\n\n        if (this._mask)\n        {\n            const maskObject = ((this._mask as MaskData).isMaskData\n                ? (this._mask as MaskData).maskObject : this._mask) as Container;\n\n            if (maskObject)\n            {\n                maskObject._maskRefCount--;\n\n                if (maskObject._maskRefCount === 0)\n                {\n                    maskObject.renderable = true;\n                    maskObject.isMask = false;\n                }\n            }\n        }\n\n        this._mask = value;\n\n        if (this._mask)\n        {\n            const maskObject = ((this._mask as MaskData).isMaskData\n                ? (this._mask as MaskData).maskObject : this._mask) as Container;\n\n            if (maskObject)\n            {\n                if (maskObject._maskRefCount === 0)\n                {\n                    maskObject.renderable = false;\n                    maskObject.isMask = true;\n                }\n\n                maskObject._maskRefCount++;\n            }\n        }\n    }\n}\n\n/**\n * @private\n */\nexport class TemporaryDisplayObject extends DisplayObject\n{\n    calculateBounds: () => null;\n    removeChild: (child: DisplayObject) => null;\n    render: (renderer: Renderer) => null;\n    sortDirty: boolean = null;\n}\n\n/**\n * DisplayObject default updateTransform, does not update children of container.\n * Will crash if there's no parent element.\n * @memberof PIXI.DisplayObject#\n * @method displayObjectUpdateTransform\n */\nDisplayObject.prototype.displayObjectUpdateTransform = DisplayObject.prototype.updateTransform;\n", "import { settings } from '@pixi/settings';\nimport { removeItems } from '@pixi/utils';\nimport { DisplayObject } from './DisplayObject';\nimport type { Matrix, Rectangle } from '@pixi/math';\nimport { MASK_TYPES } from '@pixi/constants';\n\nimport type { MaskD<PERSON>, Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from './DisplayObject';\n\nfunction sortChildren(a: DisplayObject, b: DisplayObject): number\n{\n    if (a.zIndex === b.zIndex)\n    {\n        return a._lastSortedIndex - b._lastSortedIndex;\n    }\n\n    return a.zIndex - b.zIndex;\n}\n\nexport interface Container extends GlobalMixins.Container, DisplayObject {}\n\n/**\n * Container is a general-purpose display object that holds children. It also adds built-in support for advanced\n * rendering features like masking and filtering.\n *\n * It is the base class of all display objects that act as a container for other objects, including Graphics\n * and Sprite.\n *\n * ```js\n * import { BlurFilter } from '@pixi/filter-blur';\n * import { Container } from '@pixi/display';\n * import { Graphics } from '@pixi/graphics';\n * import { Sprite } from '@pixi/sprite';\n *\n * let container = new Container();\n * let sprite = Sprite.from(\"https://s3-us-west-2.amazonaws.com/s.cdpn.io/693612/IaUrttj.png\");\n *\n * sprite.width = 512;\n * sprite.height = 512;\n *\n * // Adds a sprite as a child to this container. As a result, the sprite will be rendered whenever the container\n * // is rendered.\n * container.addChild(sprite);\n *\n * // Blurs whatever is rendered by the container\n * container.filters = [new BlurFilter()];\n *\n * // Only the contents within a circle at the center should be rendered onto the screen.\n * container.mask = new Graphics()\n *  .beginFill(0xffffff)\n *  .drawCircle(sprite.width / 2, sprite.height / 2, Math.min(sprite.width, sprite.height) / 2)\n *  .endFill();\n * ```\n * @memberof PIXI\n */\nexport class Container<T extends DisplayObject = DisplayObject> extends DisplayObject\n{\n    /**\n     * The array of children of this container.\n     * @readonly\n     */\n    public readonly children: T[];\n\n    /**\n     * If set to true, the container will sort its children by zIndex value\n     * when updateTransform() is called, or manually if sortChildren() is called.\n     *\n     * This actually changes the order of elements in the array, so should be treated\n     * as a basic solution that is not performant compared to other solutions,\n     * such as @link https://github.com/pixijs/pixi-display\n     *\n     * Also be aware of that this may not work nicely with the addChildAt() function,\n     * as the zIndex sorting may cause the child to automatically sorted to another position.\n     * @see PIXI.settings.SORTABLE_CHILDREN\n     */\n    public sortableChildren: boolean;\n\n    /**\n     * Should children be sorted by zIndex at the next updateTransform call.\n     *\n     * Will get automatically set to true if a new child is added, or if a child's zIndex changes.\n     */\n    public sortDirty: boolean;\n    public parent: Container;\n    public containerUpdateTransform: () => void;\n\n    protected _width: number;\n    protected _height: number;\n\n    constructor()\n    {\n        super();\n\n        this.children = [];\n        this.sortableChildren = settings.SORTABLE_CHILDREN;\n        this.sortDirty = false;\n\n        /**\n         * Fired when a DisplayObject is added to this Container.\n         * @event PIXI.Container#childAdded\n         * @param {PIXI.DisplayObject} child - The child added to the Container.\n         * @param {PIXI.Container} container - The container that added the child.\n         * @param {number} index - The children's index of the added child.\n         */\n\n        /**\n         * Fired when a DisplayObject is removed from this Container.\n         * @event PIXI.DisplayObject#childRemoved\n         * @param {PIXI.DisplayObject} child - The child removed from the Container.\n         * @param {PIXI.Container} container - The container that removed the child.\n         * @param {number} index - The former children's index of the removed child\n         */\n    }\n\n    /**\n     * Overridable method that can be used by Container subclasses whenever the children array is modified.\n     * @param _length\n     */\n    protected onChildrenChange(_length?: number): void\n    {\n        /* empty */\n    }\n\n    /**\n     * Adds one or more children to the container.\n     *\n     * Multiple items can be added like so: `myContainer.addChild(thingOne, thingTwo, thingThree)`\n     * @param {...PIXI.DisplayObject} children - The DisplayObject(s) to add to the container\n     * @returns {PIXI.DisplayObject} - The first child that was added.\n     */\n    addChild<U extends T[]>(...children: U): U[0]\n    {\n        // if there is only one argument we can bypass looping through the them\n        if (children.length > 1)\n        {\n            // loop through the array and add all children\n            for (let i = 0; i < children.length; i++)\n            {\n                // eslint-disable-next-line prefer-rest-params\n                this.addChild(children[i]);\n            }\n        }\n        else\n        {\n            const child = children[0];\n            // if the child has a parent then lets remove it as PixiJS objects can only exist in one place\n\n            if (child.parent)\n            {\n                child.parent.removeChild(child);\n            }\n\n            child.parent = this;\n            this.sortDirty = true;\n\n            // ensure child transform will be recalculated\n            child.transform._parentID = -1;\n\n            this.children.push(child);\n\n            // ensure bounds will be recalculated\n            this._boundsID++;\n\n            // TODO - lets either do all callbacks or all events.. not both!\n            this.onChildrenChange(this.children.length - 1);\n            this.emit('childAdded', child, this, this.children.length - 1);\n            child.emit('added', this);\n        }\n\n        return children[0];\n    }\n\n    /**\n     * Adds a child to the container at a specified index. If the index is out of bounds an error will be thrown\n     * @param {PIXI.DisplayObject} child - The child to add\n     * @param {number} index - The index to place the child in\n     * @returns {PIXI.DisplayObject} The child that was added.\n     */\n    addChildAt<U extends T>(child: U, index: number): U\n    {\n        if (index < 0 || index > this.children.length)\n        {\n            throw new Error(`${child}addChildAt: The index ${index} supplied is out of bounds ${this.children.length}`);\n        }\n\n        if (child.parent)\n        {\n            child.parent.removeChild(child);\n        }\n\n        child.parent = this;\n        this.sortDirty = true;\n\n        // ensure child transform will be recalculated\n        child.transform._parentID = -1;\n\n        this.children.splice(index, 0, child);\n\n        // ensure bounds will be recalculated\n        this._boundsID++;\n\n        // TODO - lets either do all callbacks or all events.. not both!\n        this.onChildrenChange(index);\n        child.emit('added', this);\n        this.emit('childAdded', child, this, index);\n\n        return child;\n    }\n\n    /**\n     * Swaps the position of 2 Display Objects within this container.\n     * @param child - First display object to swap\n     * @param child2 - Second display object to swap\n     */\n    swapChildren(child: T, child2: T): void\n    {\n        if (child === child2)\n        {\n            return;\n        }\n\n        const index1 = this.getChildIndex(child);\n        const index2 = this.getChildIndex(child2);\n\n        this.children[index1] = child2;\n        this.children[index2] = child;\n        this.onChildrenChange(index1 < index2 ? index1 : index2);\n    }\n\n    /**\n     * Returns the index position of a child DisplayObject instance\n     * @param child - The DisplayObject instance to identify\n     * @returns - The index position of the child display object to identify\n     */\n    getChildIndex(child: T): number\n    {\n        const index = this.children.indexOf(child);\n\n        if (index === -1)\n        {\n            throw new Error('The supplied DisplayObject must be a child of the caller');\n        }\n\n        return index;\n    }\n\n    /**\n     * Changes the position of an existing child in the display object container\n     * @param child - The child DisplayObject instance for which you want to change the index number\n     * @param index - The resulting index number for the child display object\n     */\n    setChildIndex(child: T, index: number): void\n    {\n        if (index < 0 || index >= this.children.length)\n        {\n            throw new Error(`The index ${index} supplied is out of bounds ${this.children.length}`);\n        }\n\n        const currentIndex = this.getChildIndex(child);\n\n        removeItems(this.children, currentIndex, 1); // remove from old position\n        this.children.splice(index, 0, child); // add at new position\n\n        this.onChildrenChange(index);\n    }\n\n    /**\n     * Returns the child at the specified index\n     * @param index - The index to get the child at\n     * @returns - The child at the given index, if any.\n     */\n    getChildAt(index: number): T\n    {\n        if (index < 0 || index >= this.children.length)\n        {\n            throw new Error(`getChildAt: Index (${index}) does not exist.`);\n        }\n\n        return this.children[index];\n    }\n\n    /**\n     * Removes one or more children from the container.\n     * @param {...PIXI.DisplayObject} children - The DisplayObject(s) to remove\n     * @returns {PIXI.DisplayObject} The first child that was removed.\n     */\n    removeChild<U extends T[]>(...children: U): U[0]\n    {\n        // if there is only one argument we can bypass looping through the them\n        if (children.length > 1)\n        {\n            // loop through the arguments property and remove all children\n            for (let i = 0; i < children.length; i++)\n            {\n                this.removeChild(children[i]);\n            }\n        }\n        else\n        {\n            const child = children[0];\n            const index = this.children.indexOf(child);\n\n            if (index === -1) return null;\n\n            child.parent = null;\n            // ensure child transform will be recalculated\n            child.transform._parentID = -1;\n            removeItems(this.children, index, 1);\n\n            // ensure bounds will be recalculated\n            this._boundsID++;\n\n            // TODO - lets either do all callbacks or all events.. not both!\n            this.onChildrenChange(index);\n            child.emit('removed', this);\n            this.emit('childRemoved', child, this, index);\n        }\n\n        return children[0];\n    }\n\n    /**\n     * Removes a child from the specified index position.\n     * @param index - The index to get the child from\n     * @returns The child that was removed.\n     */\n    removeChildAt(index: number): T\n    {\n        const child = this.getChildAt(index);\n\n        // ensure child transform will be recalculated..\n        child.parent = null;\n        child.transform._parentID = -1;\n        removeItems(this.children, index, 1);\n\n        // ensure bounds will be recalculated\n        this._boundsID++;\n\n        // TODO - lets either do all callbacks or all events.. not both!\n        this.onChildrenChange(index);\n        child.emit('removed', this);\n        this.emit('childRemoved', child, this, index);\n\n        return child;\n    }\n\n    /**\n     * Removes all children from this container that are within the begin and end indexes.\n     * @param beginIndex - The beginning position.\n     * @param endIndex - The ending position. Default value is size of the container.\n     * @returns - List of removed children\n     */\n    removeChildren(beginIndex = 0, endIndex = this.children.length): T[]\n    {\n        const begin = beginIndex;\n        const end = endIndex;\n        const range = end - begin;\n        let removed;\n\n        if (range > 0 && range <= end)\n        {\n            removed = this.children.splice(begin, range);\n\n            for (let i = 0; i < removed.length; ++i)\n            {\n                removed[i].parent = null;\n                if (removed[i].transform)\n                {\n                    removed[i].transform._parentID = -1;\n                }\n            }\n\n            this._boundsID++;\n\n            this.onChildrenChange(beginIndex);\n\n            for (let i = 0; i < removed.length; ++i)\n            {\n                removed[i].emit('removed', this);\n                this.emit('childRemoved', removed[i], this, i);\n            }\n\n            return removed;\n        }\n        else if (range === 0 && this.children.length === 0)\n        {\n            return [];\n        }\n\n        throw new RangeError('removeChildren: numeric values are outside the acceptable range.');\n    }\n\n    /** Sorts children by zIndex. Previous order is maintained for 2 children with the same zIndex. */\n    sortChildren(): void\n    {\n        let sortRequired = false;\n\n        for (let i = 0, j = this.children.length; i < j; ++i)\n        {\n            const child = this.children[i];\n\n            child._lastSortedIndex = i;\n\n            if (!sortRequired && child.zIndex !== 0)\n            {\n                sortRequired = true;\n            }\n        }\n\n        if (sortRequired && this.children.length > 1)\n        {\n            this.children.sort(sortChildren);\n        }\n\n        this.sortDirty = false;\n    }\n\n    /** Updates the transform on all children of this container for rendering. */\n    updateTransform(): void\n    {\n        if (this.sortableChildren && this.sortDirty)\n        {\n            this.sortChildren();\n        }\n\n        this._boundsID++;\n\n        this.transform.updateTransform(this.parent.transform);\n\n        // TODO: check render flags, how to process stuff here\n        this.worldAlpha = this.alpha * this.parent.worldAlpha;\n\n        for (let i = 0, j = this.children.length; i < j; ++i)\n        {\n            const child = this.children[i];\n\n            if (child.visible)\n            {\n                child.updateTransform();\n            }\n        }\n    }\n\n    /**\n     * Recalculates the bounds of the container.\n     *\n     * This implementation will automatically fit the children's bounds into the calculation. Each child's bounds\n     * is limited to its mask's bounds or filterArea, if any is applied.\n     */\n    calculateBounds(): void\n    {\n        this._bounds.clear();\n\n        this._calculateBounds();\n\n        for (let i = 0; i < this.children.length; i++)\n        {\n            const child = this.children[i];\n\n            if (!child.visible || !child.renderable)\n            {\n                continue;\n            }\n\n            child.calculateBounds();\n\n            // TODO: filter+mask, need to mask both somehow\n            if (child._mask)\n            {\n                const maskObject = ((child._mask as MaskData).isMaskData\n                    ? (child._mask as MaskData).maskObject : child._mask) as Container;\n\n                if (maskObject)\n                {\n                    maskObject.calculateBounds();\n                    this._bounds.addBoundsMask(child._bounds, maskObject._bounds);\n                }\n                else\n                {\n                    this._bounds.addBounds(child._bounds);\n                }\n            }\n            else if (child.filterArea)\n            {\n                this._bounds.addBoundsArea(child._bounds, child.filterArea);\n            }\n            else\n            {\n                this._bounds.addBounds(child._bounds);\n            }\n        }\n\n        this._bounds.updateID = this._boundsID;\n    }\n\n    /**\n     * Retrieves the local bounds of the displayObject as a rectangle object.\n     *\n     * Calling `getLocalBounds` may invalidate the `_bounds` of the whole subtree below. If using it inside a render()\n     * call, it is advised to call `getBounds()` immediately after to recalculate the world bounds of the subtree.\n     * @param rect - Optional rectangle to store the result of the bounds calculation.\n     * @param skipChildrenUpdate - Setting to `true` will stop re-calculation of children transforms,\n     *  it was default behaviour of pixi 4.0-5.2 and caused many problems to users.\n     * @returns - The rectangular bounding area.\n     */\n    public getLocalBounds(rect?: Rectangle, skipChildrenUpdate = false): Rectangle\n    {\n        const result = super.getLocalBounds(rect);\n\n        if (!skipChildrenUpdate)\n        {\n            for (let i = 0, j = this.children.length; i < j; ++i)\n            {\n                const child = this.children[i];\n\n                if (child.visible)\n                {\n                    child.updateTransform();\n                }\n            }\n        }\n\n        return result;\n    }\n\n    /**\n     * Recalculates the content bounds of this object. This should be overriden to\n     * calculate the bounds of this specific object (not including children).\n     * @protected\n     */\n    protected _calculateBounds(): void\n    {\n        // FILL IN//\n    }\n\n    /**\n     * Renders this object and its children with culling.\n     * @protected\n     * @param {PIXI.Renderer} renderer - The renderer\n     */\n    protected _renderWithCulling(renderer: Renderer): void\n    {\n        const sourceFrame = renderer.renderTexture.sourceFrame;\n\n        // If the source frame is empty, stop rendering.\n        if (!(sourceFrame.width > 0 && sourceFrame.height > 0))\n        {\n            return;\n        }\n\n        // Render the content of the container only if its bounds intersect with the source frame.\n        // All filters are on the stack at this point, and the filter source frame is bound:\n        // therefore, even if the bounds to non intersect the filter frame, the filter\n        // is still applied and any filter padding that is in the frame is rendered correctly.\n\n        let bounds: Rectangle;\n        let transform: Matrix;\n\n        // If cullArea is set, we use this rectangle instead of the bounds of the object. The cullArea\n        // rectangle must completely contain the container and its children including filter padding.\n        if (this.cullArea)\n        {\n            bounds = this.cullArea;\n            transform = this.worldTransform;\n        }\n        // If the container doesn't override _render, we can skip the bounds calculation and intersection test.\n        else if (this._render !== Container.prototype._render)\n        {\n            bounds = this.getBounds(true);\n        }\n\n        // Render the container if the source frame intersects the bounds.\n        if (bounds && sourceFrame.intersects(bounds, transform))\n        {\n            this._render(renderer);\n        }\n        // If the bounds are defined by cullArea and do not intersect with the source frame, stop rendering.\n        else if (this.cullArea)\n        {\n            return;\n        }\n\n        // Unless cullArea is set, we cannot skip the children if the bounds of the container do not intersect\n        // the source frame, because the children might have filters with nonzero padding, which may intersect\n        // with the source frame while the bounds do not: filter padding is not included in the bounds.\n\n        // If cullArea is not set, render the children with culling temporarily enabled so that they are not rendered\n        // if they are out of frame; otherwise, render the children normally.\n        for (let i = 0, j = this.children.length; i < j; ++i)\n        {\n            const child = this.children[i];\n            const childCullable = child.cullable;\n\n            child.cullable = childCullable || !this.cullArea;\n            child.render(renderer);\n            child.cullable = childCullable;\n        }\n    }\n\n    /**\n     * Renders the object using the WebGL renderer.\n     *\n     * The [_render]{@link PIXI.Container#_render} method is be overriden for rendering the contents of the\n     * container itself. This `render` method will invoke it, and also invoke the `render` methods of all\n     * children afterward.\n     *\n     * If `renderable` or `visible` is false or if `worldAlpha` is not positive or if `cullable` is true and\n     * the bounds of this object are out of frame, this implementation will entirely skip rendering.\n     * See {@link PIXI.DisplayObject} for choosing between `renderable` or `visible`. Generally,\n     * setting alpha to zero is not recommended for purely skipping rendering.\n     *\n     * When your scene becomes large (especially when it is larger than can be viewed in a single screen), it is\n     * advised to employ **culling** to automatically skip rendering objects outside of the current screen.\n     * See [cullable]{@link PIXI.DisplayObject#cullable} and [cullArea]{@link PIXI.DisplayObject#cullArea}.\n     * Other culling methods might be better suited for a large number static objects; see\n     * [@pixi-essentials/cull]{@link https://www.npmjs.com/package/@pixi-essentials/cull} and\n     * [pixi-cull]{@link https://www.npmjs.com/package/pixi-cull}.\n     *\n     * The [renderAdvanced]{@link PIXI.Container#renderAdvanced} method is internally used when when masking or\n     * filtering is applied on a container. This does, however, break batching and can affect performance when\n     * masking and filtering is applied extensively throughout the scene graph.\n     * @param renderer - The renderer\n     */\n    render(renderer: Renderer): void\n    {\n        // if the object is not visible or the alpha is 0 then no need to render this element\n        if (!this.visible || this.worldAlpha <= 0 || !this.renderable)\n        {\n            return;\n        }\n\n        // do a quick check to see if this element has a mask or a filter.\n        if (this._mask || (this.filters && this.filters.length))\n        {\n            this.renderAdvanced(renderer);\n        }\n        else if (this.cullable)\n        {\n            this._renderWithCulling(renderer);\n        }\n        else\n        {\n            this._render(renderer);\n\n            for (let i = 0, j = this.children.length; i < j; ++i)\n            {\n                this.children[i].render(renderer);\n            }\n        }\n    }\n\n    /**\n     * Render the object using the WebGL renderer and advanced features.\n     * @param renderer - The renderer\n     */\n    protected renderAdvanced(renderer: Renderer): void\n    {\n        const filters = this.filters;\n        const mask = this._mask as MaskData;\n\n        // push filter first as we need to ensure the stencil buffer is correct for any masking\n        if (filters)\n        {\n            if (!this._enabledFilters)\n            {\n                this._enabledFilters = [];\n            }\n\n            this._enabledFilters.length = 0;\n\n            for (let i = 0; i < filters.length; i++)\n            {\n                if (filters[i].enabled)\n                {\n                    this._enabledFilters.push(filters[i]);\n                }\n            }\n        }\n\n        const flush = (filters && this._enabledFilters && this._enabledFilters.length)\n            || (mask && (!mask.isMaskData\n                || (mask.enabled && (mask.autoDetect || mask.type !== MASK_TYPES.NONE))));\n\n        if (flush)\n        {\n            renderer.batch.flush();\n        }\n\n        if (filters && this._enabledFilters && this._enabledFilters.length)\n        {\n            renderer.filter.push(this, this._enabledFilters);\n        }\n\n        if (mask)\n        {\n            renderer.mask.push(this, this._mask);\n        }\n\n        if (this.cullable)\n        {\n            this._renderWithCulling(renderer);\n        }\n        else\n        {\n            this._render(renderer);\n\n            for (let i = 0, j = this.children.length; i < j; ++i)\n            {\n                this.children[i].render(renderer);\n            }\n        }\n\n        if (flush)\n        {\n            renderer.batch.flush();\n        }\n\n        if (mask)\n        {\n            renderer.mask.pop(this);\n        }\n\n        if (filters && this._enabledFilters && this._enabledFilters.length)\n        {\n            renderer.filter.pop();\n        }\n    }\n\n    /**\n     * To be overridden by the subclasses.\n     * @param _renderer - The renderer\n     */\n    protected _render(_renderer: Renderer): void // eslint-disable-line no-unused-vars\n    {\n        // this is where content itself gets rendered...\n    }\n\n    /**\n     * Removes all internal references and listeners as well as removes children from the display list.\n     * Do not use a Container after calling `destroy`.\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n     *  method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy();\n\n        this.sortDirty = false;\n\n        const destroyChildren = typeof options === 'boolean' ? options : options && options.children;\n\n        const oldChildren = this.removeChildren(0, this.children.length);\n\n        if (destroyChildren)\n        {\n            for (let i = 0; i < oldChildren.length; ++i)\n            {\n                oldChildren[i].destroy(options);\n            }\n        }\n    }\n\n    /** The width of the Container, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return this.scale.x * this.getLocalBounds().width;\n    }\n\n    set width(value: number)\n    {\n        const width = this.getLocalBounds().width;\n\n        if (width !== 0)\n        {\n            this.scale.x = value / width;\n        }\n        else\n        {\n            this.scale.x = 1;\n        }\n\n        this._width = value;\n    }\n\n    /** The height of the Container, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return this.scale.y * this.getLocalBounds().height;\n    }\n\n    set height(value: number)\n    {\n        const height = this.getLocalBounds().height;\n\n        if (height !== 0)\n        {\n            this.scale.y = value / height;\n        }\n        else\n        {\n            this.scale.y = 1;\n        }\n\n        this._height = value;\n    }\n}\n\n/**\n * Container default updateTransform, does update children of container.\n * Will crash if there's no parent element.\n * @memberof PIXI.Container#\n * @method containerUpdateTransform\n */\nContainer.prototype.containerUpdateTransform = Container.prototype.updateTransform;\n"], "names": ["arguments"], "mappings": ";;;;;;;;;;;;AAEA;;;;;;;;;;;;;;;;;AAiBG;AACH,QAAQ,CAAC,iBAAiB,GAAG,KAAK;;AChBlC;;;;;;AAMG;AACH,IAAA,MAAA,kBAAA,YAAA;AAsBI,IAAA,SAAA,MAAA,GAAA;AAEI,QAAA,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;AACtB,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;AAEtB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;KACtB;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AAEI,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;KACzD,CAAA;;AAGD,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;AACtB,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;KACzB,CAAA;AAED;;;;;AAKG;IACH,MAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,IAAgB,EAAA;AAEzB,QAAA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAClD;YACI,OAAO,SAAS,CAAC,KAAK,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,GAAG,IAAI,IAAI,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAEzC,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEpC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACH,MAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,KAAiB,EAAA;AAEtB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;KAC5C,CAAA;AAED;;;;AAIG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,MAAc,EAAE,KAAiB,EAAA;AAEpC,QAAA,IAAA,CAAC,GAAsB,MAAM,CAAA,CAA5B,EAAE,CAAC,GAAmB,MAAM,CAAzB,CAAA,EAAE,CAAC,GAAgB,MAAM,CAAA,CAAtB,EAAE,CAAC,GAAa,MAAM,CAAnB,CAAA,EAAE,EAAE,GAAS,MAAM,CAAA,EAAf,EAAE,EAAE,GAAK,MAAM,GAAX,CAAY;AAEtC,QAAA,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC7C,QAAA,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAE7C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KACtC,CAAA;AAED;;;AAGG;IACH,MAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,QAAsB,EAAA;AAE1B,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAErB,QAAA,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAEpB,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB,CAAA;AAED;;;;;;;AAOG;IACH,MAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,SAAoB,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAA;AAEzE,QAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KACjE,CAAA;AAED;;;;;;;AAOG;IACH,MAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,MAAc,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAA;AAEzE,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACrB,QAAA,IAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAErB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAErB,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAEjC,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAA,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAA,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAA,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAE3B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,UAAwB,EAAE,WAAmB,EAAE,SAAiB,EAAA;AAE1E,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAErB,QAAA,KAAK,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,CAAC,EAC/C;AACI,YAAA,IAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YACxB,IAAM,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE5B,YAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,YAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,YAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3B,YAAA,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB,CAAA;AAED;;;;;;AAMG;IACH,MAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,SAAoB,EAAE,QAAsB,EAAE,WAAmB,EAAE,SAAiB,EAAA;AAE5F,QAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;KACtF,CAAA;AAED;;;;;;;;AAQG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UAAkB,MAAc,EAAE,QAAsB,EAAE,WAAmB,EACzE,SAAiB,EAAE,IAAQ,EAAE,IAAW,EAAA;AAArB,QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAQ,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAW,GAAA,IAAA,CAAA,EAAA;AAExC,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,QAAA,IAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACrB,QAAA,IAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAErB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAErB,QAAA,KAAK,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,CAAC,EAC/C;AACI,YAAA,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7B,YAAA,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvC,YAAA,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AACnC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB,CAAA;AAED;;;AAGG;IACH,MAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAAc,EAAA;AAEpB,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEvB,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACpD,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACpD,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACpD,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;KACvD,CAAA;AAED;;;;AAIG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,MAAc,EAAE,IAAY,EAAA;QAEtC,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAChE,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAChE,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAChE,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEhE,QAAA,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EACpC;AACI,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEvB,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AACxC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AACxC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AACxC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AAC3C,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,MAAc,EAAE,MAAc,EAAA;QAE1C,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;KACnF,CAAA;AAED;;;;AAIG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,MAAc,EAAE,IAAe,EAAA;QAEzC,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;QAC1D,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;AAC1D,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AACtF,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAExF,QAAA,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EACpC;AACI,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEvB,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AACxC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AACxC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AACxC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AAC3C,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,QAAY,EAAE,QAAmB,EAAA;AAAjC,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAY,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAmB,GAAA,QAAA,CAAA,EAAA;AAEjC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EACnB;AACI,YAAA,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;AACtB,YAAA,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;AACtB,YAAA,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;AACtB,YAAA,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;AACzB,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UAAY,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,IAAY,EAAE,IAAY,EAAA;QAElF,EAAE,IAAI,IAAI,CAAC;QACX,EAAE,IAAI,IAAI,CAAC;QACX,EAAE,IAAI,IAAI,CAAC;QACX,EAAE,IAAI,IAAI,CAAC;AAEX,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AAC5C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AAC5C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AAC5C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;KAC/C,CAAA;IACL,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA;;ACjbD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;ACTA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwLG;AACH,IAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;IAA4C,SAAY,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;AAoJpD,IAAA,SAAA,aAAA,GAAA;AAAA,QAAA,IAAA,KAAA,GAEI,iBAAO,IAiCV,IAAA,CAAA;AA/BG,QAAA,KAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;;AAGpC,QAAA,KAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;AACjC,QAAA,KAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,KAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAErB,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,KAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAEpB,QAAA,KAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC1B,QAAA,KAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AAEjB,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAE5B,QAAA,KAAI,CAAC,OAAO,GAAG,IAAI,MAAM,EAAE,CAAC;AAC5B,QAAA,KAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACnB,QAAA,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,KAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,KAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AACvB,QAAA,KAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAExB,QAAA,KAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;;KACvB;AA7DD;;;AAGG;IACI,aAAK,CAAA,KAAA,GAAZ,UAAa,MAAiB,EAAA;;;;QAM1B,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;AAGjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EACpC;AACI,YAAA,IAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;;AAG7B,YAAA,MAAM,CAAC,cAAc,CACjB,aAAa,CAAC,SAAS,EACvB,YAAY,EACZ,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,YAAY,CAAC,CACxD,CAAC;AACL,SAAA;KACJ,CAAA;AA6DD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AAtBb;;;;;AAKG;AAEH;;;;;AAKG;AAEH;;;;;AAKG;;AAGH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;;AAcS,IAAA,aAAA,CAAA,SAAA,CAAA,6BAA6B,GAAvC,YAAA;QAEI,IAAI,IAAI,CAAC,MAAM,EACf;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACzD,SAAA;AAED,aAAA;YACI,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;AAC3E,SAAA;KACJ,CAAA;;AAGD,IAAA,aAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;QAEI,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;;AAEtD,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;KACzD,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,UAAoB,EAAE,IAAgB,EAAA;QAE5C,IAAI,CAAC,UAAU,EACf;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAChB;AACI,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAqC,CAAC;gBACzD,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACrC,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,aAAA;AACJ,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAC5C;YACI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AAC1C,SAAA;QAED,IAAI,CAAC,IAAI,EACT;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EACrB;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,SAAS,EAAE,CAAC;AACtC,aAAA;AAED,YAAA,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;AAC3B,SAAA;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KAC1C,CAAA;AAED;;;;AAIG;IACH,aAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,IAAgB,EAAA;QAE3B,IAAI,CAAC,IAAI,EACT;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAC1B;AACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,SAAS,EAAE,CAAC;AAC3C,aAAA;AAED,YAAA,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAChC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EACtB;AACI,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;AACpC,SAAA;AAED,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;AAE9B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC;AAEzD,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,QAAA,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;AAErC,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;QAEjC,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAE3C,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;AAE9B,QAAA,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;AAC3B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;AAExD,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;;;;AAOG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAuC,QAAoB,EAAE,KAAS,EAAE,UAAkB,EAAA;AAAlB,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAkB,GAAA,KAAA,CAAA,EAAA;QAEtF,IAAI,CAAC,UAAU,EACf;YACI,IAAI,CAAC,6BAA6B,EAAE,CAAC;;;;AAKrC,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAChB;AACI,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAqC,CAAC;gBACzD,IAAI,CAAC,4BAA4B,EAAE,CAAC;AACpC,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,4BAA4B,EAAE,CAAC;AACvC,aAAA;AACJ,SAAA;;QAGD,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAI,QAAQ,EAAE,KAAK,CAAC,CAAC;KACxD,CAAA;AAED;;;;;;;;AAQG;IACH,aAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAsC,QAAoB,EAAE,IAAoB,EAAE,KAAS,EAAE,UAAoB,EAAA;AAE7G,QAAA,IAAI,IAAI,EACR;YACI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACzD,SAAA;QAED,IAAI,CAAC,UAAU,EACf;YACI,IAAI,CAAC,6BAA6B,EAAE,CAAC;;;;AAKrC,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAChB;AACI,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAqC,CAAC;gBACzD,IAAI,CAAC,4BAA4B,EAAE,CAAC;AACpC,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,4BAA4B,EAAE,CAAC;AACvC,aAAA;AACJ,SAAA;;QAGD,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAI,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC/D,CAAA;AAED;;;;AAIG;IACH,aAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,SAAoB,EAAA;AAE1B,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,EACrC;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC9D,SAAA;AAED,QAAA,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAEzB,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,CAAK,EAAE,CAAK,EAAE,MAAU,EAAE,MAAU,EAAE,QAAY,EAAE,KAAS,EAAE,KAAS,EAAE,MAAU,EAAE,MAAU,EAAA;AAAhG,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAY,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;AAEzG,QAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC;AACpC,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC;AACpC,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC;AAEtB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;AAMG;IACH,aAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,QAAoC,EAAA;QAExC,IAAI,IAAI,CAAC,MAAM,EACf;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAEtB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAEjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAEpB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AAEjC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAwB,CAAA,SAAA,EAAA,0BAAA,EAAA;AAJ5B;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,IAAI,CAAC,uBAAuB,KAAK,IAAI,EACzC;;AAEI,gBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,sBAAsB,EAAE,CAAC;AAC/D,aAAA;YAED,OAAO,IAAI,CAAC,uBAAuB,CAAC;SACvC;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;;;;;;AASG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;AAE7B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAqC,CAAC;AAEzD,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAED;;;AAGG;IACH,aAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,WAAsB,EAAA;AAEpC,QAAA,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;KAC7B,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAC,CAAA,SAAA,EAAA,GAAA,EAAA;AAJL;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1B;AAED,QAAA,GAAA,EAAA,UAAM,KAAa,EAAA;YAEf,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;SACrC;;;AALA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAC,CAAA,SAAA,EAAA,GAAA,EAAA;AAJL;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1B;AAED,QAAA,GAAA,EAAA,UAAM,KAAa,EAAA;YAEf,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;SACrC;;;AALA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAc,CAAA,SAAA,EAAA,gBAAA,EAAA;AAJlB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;SACxC;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAc,CAAA,SAAA,EAAA,gBAAA,EAAA;AAJlB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;SACxC;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAJZ;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;SAClC;AAED,QAAA,GAAA,EAAA,UAAa,KAAiB,EAAA;YAE1B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SAC3C;;;AALA,KAAA,CAAA,CAAA;AAaD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AANT;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;SAC/B;AAED,QAAA,GAAA,EAAA,UAAU,KAAiB,EAAA;YAEvB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACxC;;;AALA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AAPT;;;;;;AAMG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;SAC/B;AAED,QAAA,GAAA,EAAA,UAAU,KAAiB,EAAA;YAEvB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACxC;;;AALA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAJR;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;SAC9B;AAED,QAAA,GAAA,EAAA,UAAS,KAAiB,EAAA;YAEtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACvC;;;AALA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAJZ;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;SAClC;AAED,QAAA,GAAA,EAAA,UAAa,KAAa,EAAA;AAEtB,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;SACnC;;;AALA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AAJT;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC;SAC/C;AAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;YAEnB,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,UAAU,CAAC;SAChD;;;AALA,KAAA,CAAA,CAAA;AAeD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AARV;;;;;;;AAOG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;AAED,QAAA,GAAA,EAAA,UAAW,KAAa,EAAA;AAEpB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,IAAI,CAAC,MAAM,EACf;AACI,gBAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAChC,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;AAeD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAY,CAAA,SAAA,EAAA,cAAA,EAAA;AAJhB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,IAAI,IAAI,GAAG,IAAqB,CAAC;YAEjC,GACA;AACI,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB;AACI,oBAAA,OAAO,KAAK,CAAC;AAChB,iBAAA;AAED,gBAAA,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AACtB,aAAA,QAAQ,IAAI,EAAE;AAEf,YAAA,OAAO,IAAI,CAAC;SACf;;;AAAA,KAAA,CAAA,CAAA;AAqBD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAnBR;;;;;;;;;;;;;;;;;;AAkBG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AAED,QAAA,GAAA,EAAA,UAAS,KAAkC,EAAA;AAEvC,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EACxB;gBACI,OAAO;AACV,aAAA;YAED,IAAI,IAAI,CAAC,KAAK,EACd;AACI,gBAAA,IAAM,UAAU,IAAK,IAAI,CAAC,KAAkB,CAAC,UAAU;AACnD,sBAAG,IAAI,CAAC,KAAkB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAc,CAAC;AAErE,gBAAA,IAAI,UAAU,EACd;oBACI,UAAU,CAAC,aAAa,EAAE,CAAC;AAE3B,oBAAA,IAAI,UAAU,CAAC,aAAa,KAAK,CAAC,EAClC;AACI,wBAAA,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;AAC7B,wBAAA,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;AAC7B,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YAEnB,IAAI,IAAI,CAAC,KAAK,EACd;AACI,gBAAA,IAAM,UAAU,IAAK,IAAI,CAAC,KAAkB,CAAC,UAAU;AACnD,sBAAG,IAAI,CAAC,KAAkB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAc,CAAC;AAErE,gBAAA,IAAI,UAAU,EACd;AACI,oBAAA,IAAI,UAAU,CAAC,aAAa,KAAK,CAAC,EAClC;AACI,wBAAA,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9B,wBAAA,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;AAC5B,qBAAA;oBAED,UAAU,CAAC,aAAa,EAAE,CAAC;AAC9B,iBAAA;AACJ,aAAA;SACJ;;;AA5CA,KAAA,CAAA,CAAA;IA6CL,OAAC,aAAA,CAAA;AAAD,CA9xBA,CAA4C,YAAY,CA8xBvD,EAAA;AAED;;AAEG;AACH,IAAA,sBAAA,kBAAA,UAAA,MAAA,EAAA;IAA4C,SAAa,CAAA,sBAAA,EAAA,MAAA,CAAA,CAAA;AAAzD,IAAA,SAAA,sBAAA,GAAA;QAAA,IAMC,KAAA,GAAA,MAAA,KAAA,IAAA,IAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,IAAA,CAAA;QADG,KAAS,CAAA,SAAA,GAAY,IAAI,CAAC;;KAC7B;IAAD,OAAC,sBAAA,CAAA;AAAD,CANA,CAA4C,aAAa,CAMxD,EAAA;AAED;;;;;AAKG;AACH,aAAa,CAAC,SAAS,CAAC,4BAA4B,GAAG,aAAa,CAAC,SAAS,CAAC,eAAe;;ACn/B9F,SAAS,YAAY,CAAC,CAAgB,EAAE,CAAgB,EAAA;AAEpD,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EACzB;AACI,QAAA,OAAO,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,CAAC;AAClD,KAAA;AAED,IAAA,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;AAC/B,CAAC;AAID;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;AACH,IAAA,SAAA,kBAAA,UAAA,MAAA,EAAA;IAAwE,SAAa,CAAA,SAAA,EAAA,MAAA,CAAA,CAAA;AAkCjF,IAAA,SAAA,SAAA,GAAA;AAAA,QAAA,IAAA,KAAA,GAEI,iBAAO,IAqBV,IAAA,CAAA;AAnBG,QAAA,KAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACnB,QAAA,KAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;AACnD,QAAA,KAAI,CAAC,SAAS,GAAG,KAAK,CAAC;;AAEvB;;;;;;AAMG;AAEH;;;;;;AAMG;KACN;AAED;;;AAGG;IACO,SAAgB,CAAA,SAAA,CAAA,gBAAA,GAA1B,UAA2B,OAAgB,EAAA;;KAG1C,CAAA;AAED;;;;;;AAMG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;;AAAA;QAAwB,IAAc,QAAA,GAAA,EAAA,CAAA;aAAd,IAAc,EAAA,GAAA,CAAA,EAAd,EAAc,GAAA,SAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAA;YAAd,QAAc,CAAA,EAAA,CAAA,GAAAA,WAAA,CAAA,EAAA,CAAA,CAAA;;;AAGlC,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EACvB;;AAEI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EACxC;;gBAEI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,aAAA;AACJ,SAAA;AAED,aAAA;AACI,YAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;YAG1B,IAAI,KAAK,CAAC,MAAM,EAChB;AACI,gBAAA,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnC,aAAA;AAED,YAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;AAGtB,YAAA,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AAE/B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;YAG1B,IAAI,CAAC,SAAS,EAAE,CAAC;;YAGjB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC/D,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7B,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;KACtB,CAAA;AAED;;;;;AAKG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAwB,KAAQ,EAAE,KAAa,EAAA;QAE3C,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAC7C;AACI,YAAA,MAAM,IAAI,KAAK,CAAI,KAAK,GAAyB,wBAAA,GAAA,KAAK,GAA8B,6BAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,MAAQ,CAAC,CAAC;AAC/G,SAAA;QAED,IAAI,KAAK,CAAC,MAAM,EAChB;AACI,YAAA,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnC,SAAA;AAED,QAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;AAGtB,QAAA,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QAE/B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;;QAGtC,IAAI,CAAC,SAAS,EAAE,CAAC;;AAGjB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC7B,QAAA,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAE5C,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;AAIG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,KAAQ,EAAE,MAAS,EAAA;QAE5B,IAAI,KAAK,KAAK,MAAM,EACpB;YACI,OAAO;AACV,SAAA;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACzC,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAE1C,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAC/B,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AAC9B,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;KAC5D,CAAA;AAED;;;;AAIG;IACH,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,KAAQ,EAAA;QAElB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAE3C,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAChB;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;AAC/E,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;AAIG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,KAAQ,EAAE,KAAa,EAAA;QAEjC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAC9C;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,YAAA,GAAa,KAAK,GAAA,6BAAA,GAA8B,IAAI,CAAC,QAAQ,CAAC,MAAQ,CAAC,CAAC;AAC3F,SAAA;QAED,IAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE/C,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAEtC,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAChC,CAAA;AAED;;;;AAIG;IACH,SAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,KAAa,EAAA;QAEpB,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAC9C;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,wBAAsB,KAAK,GAAA,mBAAmB,CAAC,CAAC;AACnE,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC/B,CAAA;AAED;;;;AAIG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;;AAAA;QAA2B,IAAc,QAAA,GAAA,EAAA,CAAA;aAAd,IAAc,EAAA,GAAA,CAAA,EAAd,EAAc,GAAA,SAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAA;YAAd,QAAc,CAAA,EAAA,CAAA,GAAAA,WAAA,CAAA,EAAA,CAAA,CAAA;;;AAGrC,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EACvB;;AAEI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EACxC;gBACI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,aAAA;AACJ,SAAA;AAED,aAAA;AACI,YAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAE3C,IAAI,KAAK,KAAK,CAAC,CAAC;AAAE,gBAAA,EAAA,OAAO,IAAI,CAAC,EAAA;AAE9B,YAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;;AAEpB,YAAA,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;;YAGrC,IAAI,CAAC,SAAS,EAAE,CAAC;;AAGjB,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC7B,YAAA,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACjD,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;KACtB,CAAA;AAED;;;;AAIG;IACH,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,KAAa,EAAA;QAEvB,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;;AAGrC,QAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,QAAA,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QAC/B,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;;QAGrC,IAAI,CAAC,SAAS,EAAE,CAAC;;AAGjB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC7B,QAAA,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAE9C,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;;AAKG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,UAAc,EAAE,QAA+B,EAAA;AAA/C,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAc,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAW,GAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA,EAAA;QAE1D,IAAM,KAAK,GAAG,UAAU,CAAC;QACzB,IAAM,GAAG,GAAG,QAAQ,CAAC;AACrB,QAAA,IAAM,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC;AAC1B,QAAA,IAAI,OAAO,CAAC;AAEZ,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,EAC7B;YACI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAE7C,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EACvC;AACI,gBAAA,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;AACzB,gBAAA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,EACxB;oBACI,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AACvC,iBAAA;AACJ,aAAA;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;AAEjB,YAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAElC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EACvC;gBACI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACjC,gBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAClD,aAAA;AAED,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;aACI,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAClD;AACI,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;AAED,QAAA,MAAM,IAAI,UAAU,CAAC,kEAAkE,CAAC,CAAC;KAC5F,CAAA;;AAGD,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QAEI,IAAI,YAAY,GAAG,KAAK,CAAC;AAEzB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EACpD;YACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAE/B,YAAA,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAE3B,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EACvC;gBACI,YAAY,GAAG,IAAI,CAAC;AACvB,aAAA;AACJ,SAAA;QAED,IAAI,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAC5C;AACI,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KAC1B,CAAA;;AAGD,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,EAC3C;YACI,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;;AAGtD,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;AAEtD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EACpD;YACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAI,KAAK,CAAC,OAAO,EACjB;gBACI,KAAK,CAAC,eAAe,EAAE,CAAC;AAC3B,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAErB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAC7C;YACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EACvC;gBACI,SAAS;AACZ,aAAA;YAED,KAAK,CAAC,eAAe,EAAE,CAAC;;YAGxB,IAAI,KAAK,CAAC,KAAK,EACf;AACI,gBAAA,IAAM,UAAU,IAAK,KAAK,CAAC,KAAkB,CAAC,UAAU;AACpD,sBAAG,KAAK,CAAC,KAAkB,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAc,CAAC;AAEvE,gBAAA,IAAI,UAAU,EACd;oBACI,UAAU,CAAC,eAAe,EAAE,CAAC;AAC7B,oBAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;AACjE,iBAAA;AAED,qBAAA;oBACI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACzC,iBAAA;AACJ,aAAA;iBACI,IAAI,KAAK,CAAC,UAAU,EACzB;AACI,gBAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AAC/D,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACzC,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;KAC1C,CAAA;AAED;;;;;;;;;AASG;AACI,IAAA,SAAA,CAAA,SAAA,CAAA,cAAc,GAArB,UAAsB,IAAgB,EAAE,kBAA0B,EAAA;AAA1B,QAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAA0B,GAAA,KAAA,CAAA,EAAA;AAE9D,QAAA,IAAM,MAAM,GAAG,MAAA,CAAA,SAAA,CAAM,cAAc,CAAC,IAAA,CAAA,IAAA,EAAA,IAAI,CAAC,CAAC;QAE1C,IAAI,CAAC,kBAAkB,EACvB;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EACpD;gBACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE/B,IAAI,KAAK,CAAC,OAAO,EACjB;oBACI,KAAK,CAAC,eAAe,EAAE,CAAC;AAC3B,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;AAIG;AACO,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;;KAGC,CAAA;AAED;;;;AAIG;IACO,SAAkB,CAAA,SAAA,CAAA,kBAAA,GAA5B,UAA6B,QAAkB,EAAA;AAE3C,QAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;;AAGvD,QAAA,IAAI,EAAE,WAAW,CAAC,KAAK,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,EACtD;YACI,OAAO;AACV,SAAA;;;;;AAOD,QAAA,IAAI,MAAiB,CAAC;AACtB,QAAA,IAAI,SAAiB,CAAC;;;QAItB,IAAI,IAAI,CAAC,QAAQ,EACjB;AACI,YAAA,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvB,YAAA,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,SAAA;;aAEI,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,SAAS,CAAC,OAAO,EACrD;AACI,YAAA,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACjC,SAAA;;QAGD,IAAI,MAAM,IAAI,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,EACvD;AACI,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC1B,SAAA;;aAEI,IAAI,IAAI,CAAC,QAAQ,EACtB;YACI,OAAO;AACV,SAAA;;;;;;AAQD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EACpD;YACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/B,YAAA,IAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC;YAErC,KAAK,CAAC,QAAQ,GAAG,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACjD,YAAA,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvB,YAAA,KAAK,CAAC,QAAQ,GAAG,aAAa,CAAC;AAClC,SAAA;KACJ,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBG;IACH,SAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,QAAkB,EAAA;;AAGrB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAC7D;YACI,OAAO;AACV,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EACvD;AACI,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACjC,SAAA;aACI,IAAI,IAAI,CAAC,QAAQ,EACtB;AACI,YAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACrC,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAEvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EACpD;gBACI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;AAGG;IACO,SAAc,CAAA,SAAA,CAAA,cAAA,GAAxB,UAAyB,QAAkB,EAAA;AAEvC,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7B,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAiB,CAAC;;AAGpC,QAAA,IAAI,OAAO,EACX;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EACzB;AACI,gBAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;AAEhC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EACvC;AACI,gBAAA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EACtB;oBACI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM;AACtE,gBAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU;oBACrB,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAElF,QAAA,IAAI,KAAK,EACT;AACI,YAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAC1B,SAAA;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAClE;YACI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;AACpD,SAAA;AAED,QAAA,IAAI,IAAI,EACR;YACI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EACjB;AACI,YAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACrC,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAEvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EACpD;gBACI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,KAAK,EACT;AACI,YAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,IAAI,EACR;AACI,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,SAAA;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAClE;AACI,YAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AACzB,SAAA;KACJ,CAAA;AAED;;;AAGG;IACO,SAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,SAAmB,EAAA;;KAGpC,CAAA;AAED;;;;;;;;;;;AAWG;IACH,SAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,OAAmC,EAAA;QAEvC,MAAM,CAAA,SAAA,CAAA,OAAO,WAAE,CAAC;AAEhB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAEvB,QAAA,IAAM,eAAe,GAAG,OAAO,OAAO,KAAK,SAAS,GAAG,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC;AAE7F,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAEjE,QAAA,IAAI,eAAe,EACnB;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAC3C;gBACI,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACnC,aAAA;AACJ,SAAA;KACJ,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAK,CAAA,SAAA,EAAA,OAAA,EAAA;;AAAT,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC;SACrD;AAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;YAEnB,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC;YAE1C,IAAI,KAAK,KAAK,CAAC,EACf;gBACI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AAChC,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,aAAA;AAED,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACvB;;;AAhBA,KAAA,CAAA,CAAA;AAmBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAM,CAAA,SAAA,EAAA,QAAA,EAAA;;AAAV,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC;SACtD;AAED,QAAA,GAAA,EAAA,UAAW,KAAa,EAAA;YAEpB,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC;YAE5C,IAAI,MAAM,KAAK,CAAC,EAChB;gBACI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;AACjC,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,aAAA;AAED,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;;;AAhBA,KAAA,CAAA,CAAA;IAiBL,OAAC,SAAA,CAAA;AAAD,CApvBA,CAAwE,aAAa,CAovBpF,EAAA;AAED;;;;;AAKG;AACH,SAAS,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAS,CAAC,SAAS,CAAC,eAAe;;;;"}