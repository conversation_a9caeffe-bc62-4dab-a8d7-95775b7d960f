# 健康检查端点修改说明

## 概述

本文档描述了对 `api_bridge.py` 文件中健康检查端点的修改，以支持应用启动状态管理和在启动期间返回HTTP 503状态码。

## 修改内容

### 1. 添加应用启动状态管理

在全局变量区域添加了应用启动状态跟踪：

```python
# 🔧 应用启动状态管理
app_startup_state = {
    "status": "starting",  # starting, ready, error
    "startup_time": time.time(),
    "initialization_steps": {
        "webui_loaded": False,
        "models_loaded": False,
        "services_ready": False
    }
}
```

### 2. 修改健康检查端点

#### `/api/system/health` 端点
- 在启动状态为 "starting" 或 "error" 时返回 HTTP 503
- 包含启动状态信息和经过的时间
- 在正常运行时包含启动状态作为额外信息

#### `/api/health` 端点  
- 同样支持启动状态检查
- 在启动期间返回HTTP 503
- 保持简化的响应格式适合前端调用

### 3. 修改启动事件处理器

在 `startup_event()` 函数中添加了状态更新逻辑：
- 在WebUI加载完成后更新状态
- 检查模型加载状态
- 在所有服务准备就绪后更新状态
- 在发生错误时设置错误状态

### 4. 新增辅助函数

添加了 `update_app_startup_state()` 函数用于管理启动状态：
- 支持更新整体状态
- 支持更新单个初始化步骤
- 自动检查所有步骤是否完成

### 5. 新增调试端点

#### `POST /api/system/startup-state`
- 手动更新应用启动状态
- 用于调试和测试
- 支持状态验证

#### `GET /api/system/startup-state`
- 获取当前应用启动状态
- 返回详细的状态信息

## 状态说明

### 应用状态
- `starting`: 应用正在启动中
- `ready`: 应用已准备就绪
- `error`: 应用启动失败

### 初始化步骤
- `webui_loaded`: WebUI实例是否已加载
- `models_loaded`: 模型是否已加载
- `services_ready`: 服务是否已准备就绪

## HTTP状态码

| 应用状态 | HTTP状态码 | 说明 |
|---------|-----------|------|
| starting | 503 | 服务不可用，正在启动 |
| error | 503 | 服务不可用，启动失败 |
| ready | 200 | 服务正常运行 |

## 响应格式

### 启动中的响应示例 (HTTP 503)
```json
{
  "status": "starting",
  "message": "应用正在启动中，请稍后重试",
  "timestamp": "2024-01-01T00:00:00",
  "startup_elapsed": 15.5,
  "initialization_steps": {
    "webui_loaded": true,
    "models_loaded": false,
    "services_ready": false
  }
}
```

### 正常运行的响应示例 (HTTP 200)
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00",
    "startup_state": "ready",
    "startup_elapsed": 45.2,
    "checks": {
      "webui": "正常",
      "indextts_service": true
    }
  }
}
```

## 测试方法

使用提供的测试脚本验证功能：

```bash
python test_health_check.py
```

该脚本会：
1. 测试所有健康检查端点
2. 手动设置启动状态为 "starting"
3. 验证返回HTTP 503状态码
4. 恢复状态为 "ready"
5. 验证返回HTTP 200状态码

## 兼容性说明

- 现有的健康检查调用在应用完全启动后仍然正常工作
- 新增的状态信息向后兼容
- 所有修改都在原有功能基础上增强，不影响现有逻辑

## 使用建议

1. 负载均衡器可以通过监控这些端点来判断服务是否准备就绪
2. 前端应用可以通过轮询健康检查端点来显示启动进度
3. 监控系统可以区分服务启动和服务故障的情况