<template>
  <div class="comic-main-panel-container">
    <!-- 🌌 AAA级游戏背景 - 星空粒子系统 -->
    <div class="cosmic-background">
      <div class="particle-layer" ref="particleLayerRef"></div>
      <div class="energy-flow-layer"></div>
      <div class="mystical-overlay"></div>
    </div>

    <!-- 🎯 主布局网格 - 紧凑设计 -->
    <div class="comic-main-grid">
      <!-- 📝🎨 顶部：故事创作和风格配置并排 -->
      <div class="story-style-row">
        <!-- 左侧：故事创作 -->
        <div class="story-section">
          <StoryInputPanel 
            @story-generated="handleStoryGenerated"
            @generation-started="handleGenerationStarted"
            :is-generating="isGenerating"
          />
        </div>

        <!-- 右侧：风格配置 -->
        <div class="style-section">
          <StyleConfigPanel 
            v-model:selectedStyle="selectedStyle"
            v-model:layoutConfig="layoutConfig"
            v-model:characterConfig="characterConfig"
            :disabled="isGenerating"
            @style-selected="handleStyleSelected"
          />
        </div>
      </div>

      <!-- 🎛️ 中部：紧凑参数控制面板 -->
      <div class="control-panel">
        <CompactControlPanel 
          :is-generating="isGenerating"
          @parameter-changed="handleParameterChanged"
        />
      </div>

      <!-- 🖼️ 主要区域：生成展示 -->
      <div class="generation-main-row">
        <ComicGenerationStage
          ref="generationStageRef"
          :story-input="currentStoryInput"
          :generation-params="currentGenerationParams"
          :current-result="currentGenerationResult"
          @progress-update="handleProgressUpdate"
          @generation-complete="handleGenerationComplete"
          @generation-error="handleGenerationError"
          @preview-update="handlePreviewUpdate"
          @start-generation="handleStartGeneration"
        />
      </div>

      <!-- 📚 底部：作品画廊（可折叠） -->
      <div class="gallery-section" :class="{ 'gallery-expanded': showGallery }">
        <div class="gallery-header" @click="toggleGallery">
          <div class="gallery-title">
            <i class="fas fa-images"></i>
            <span>我的漫画作品 ({{ comicStore.savedComics?.length || 0 }})</span>
          </div>
          <div class="gallery-controls">
            <button class="gallery-refresh-btn" @click.stop="refreshGallery" title="刷新作品列表">
              <i class="fas fa-sync-alt"></i>
            </button>
            <button class="gallery-debug-btn" @click.stop="debugDataLoading" title="调试数据加载" style="margin-left: 5px; background: #f39c12;">
              <i class="fas fa-bug"></i>
            </button>
            <button class="gallery-migrate-btn" @click.stop="migrateComfyUIImages" title="迁移ComfyUI图片" style="margin-left: 5px; background: #e74c3c;">
              <i class="fas fa-sync"></i>
            </button>
            <button class="gallery-comfyui-btn" @click.stop="testComfyUIConnection" title="测试ComfyUI连接" style="margin-left: 5px; background: #9b59b6;">
              <i class="fas fa-plug"></i>
            </button>
            <button class="gallery-fix-btn" @click.stop="fixImagePaths" title="修复图片路径" style="margin-left: 5px; background: #27ae60;">
              <i class="fas fa-wrench"></i>
            </button>
            <button class="gallery-cleanup-btn" @click.stop="cleanupInvalidComics" title="清理无效作品" style="margin-left: 5px; background: #e67e22;">
              <i class="fas fa-trash-alt"></i>
            </button>
            <div class="gallery-toggle">
              <i :class="showGallery ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
            </div>
          </div>
        </div>
        <div class="gallery-content" v-show="showGallery">
          <ComicGalleryWrapper 
            @comic-selected="handleComicSelected"
            @comic-deleted="handleComicDeleted"
          />
        </div>
      </div>
    </div>

    <!-- 🌐 局域网调试面板 -->
    <LANDebugPanel />

    <!-- 🔐 私有网络访问权限模态框 -->
    <PNAPermissionModal />
    
    <!-- 🔄 多设备状态同步指示器 (已禁用) -->
    <!-- <div class="device-sync-indicator" v-if="showSyncIndicator" :class="{ 'sync-active': showSyncIndicator }">
      ...
    </div> -->

    <!-- 🌟 浮动操作按钮 -->
    <div class="floating-actions">
      <button class="fab main-fab" @click="toggleQuickActions" :class="{ 'fab-open': showQuickActions }">
        <i class="fas fa-magic"></i>
      </button>
      
      <div class="quick-actions" :class="{ 'actions-open': showQuickActions }">
        <button class="fab action-fab" @click="showGallery = !showGallery" title="作品画廊">
          <i class="fas fa-images"></i>
        </button>
        <button 
          class="fab action-fab" 
          @click="handleDataMigration" 
          title="数据迁移" 
          :style="migrationNeeded ? 'background: linear-gradient(135deg, #ff9800, #f57c00) !important; animation: pulse 2s infinite;' : ''"
        >
          <i class="fas fa-exchange-alt"></i>
        </button>
        <button class="fab action-fab" @click="handleExportSettings" title="导出设置">
          <i class="fas fa-download"></i>
        </button>
        <button class="fab action-fab" @click="handleImportSettings" title="导入设置">
          <i class="fas fa-upload"></i>
        </button>
        <button class="fab action-fab" @click="handleShowHelp" title="帮助">
          <i class="fas fa-question"></i>
        </button>
      </div>
    </div>

    <!-- 🎵 背景音效控制 -->
    <audio ref="ambientAudioRef" loop preload="auto" v-if="false">
      <!-- 音频文件暂时禁用，避免路径错误 -->
    </audio>
    
    <!-- 🔄 设备状态显示 (已禁用) -->
    <!-- <div class="device-status-bar" v-if="deviceInfo">
      ...
    </div> -->
    
    <!-- 🔧 iPad调试工具 -->
    <DebugTestTool />
    
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useStoryStore } from '@/stores/storyStore';
import { useComicStore } from '@/components/modules/漫画生成/stores/comicStore';
import { comfyUIService } from './services/comfyuiApi';
import { comicStorageFixed as comicStorage } from './services/comicStorageFixed';
import { memoryMonitor } from './utils/memoryMonitor';
// 已移除 comicGenerator 导入，使用 WebSocket 系统 (storyStore)
import { memoryCleanupManager } from '@/utils/memoryCleanupManager';
import { DebugHelper } from './utils/debugHelper';
import { StorageDebugger } from './utils/storageDebugger';
import { ComicResultValidator } from './utils/comicResultValidator';
import { BlobUrlManager } from './utils/blobUrlManager';
import { EmergencyBlobFixer } from './utils/emergencyBlobFixer';
import { electronStoreManager } from '@/utils/electronStoreManager';
import { imageUploadHelper, ImageUploadHelper, type FileReference } from '@/utils/imageUploadHelper'; // 🎯 图片上传优化
import './utils/blobFixTest'; // 加载测试工具
import { Base64MigrationScript } from './utils/base64MigrationScript'; // 🎯 Base64迁移脚本
import './utils/performanceTestTool'; // 🎯 性能测试工具
import { memoryOptimizer } from './utils/memoryOptimizer'; // 🎯 内存优化器
import { performanceMonitor } from './utils/performanceMonitor'; // 🚀 性能监控器

// 🚨 IndexedDB重建工具 - 解决665个作品显示问题
const loadIndexedDBRebuildTool = () => {
  if (typeof window !== 'undefined') {
    (window as any).rebuildFromIndexedDB = async function() {
      console.log('🚨 开始从IndexedDB重建ComicResult结构...');
      
      try {
        // 清除错误数据
        localStorage.removeItem('comic-generation-results');
        
        // 扫描IndexedDB
        const databases = await indexedDB.databases();
        console.log('📊 发现数据库:', databases.map(db => ({ name: db.name, version: db.version })));
        
        // 检查所有数据库，不只是名称匹配的
        const imageDBs = databases.filter(db => db.name);
        console.log('🔍 将检查所有数据库:', imageDBs.map(db => db.name));
        
        let allImages: any[] = [];
        
        // 辅助函数
        const openDB = (dbName: string) => new Promise<IDBDatabase>((resolve, reject) => {
          const request = indexedDB.open(dbName);
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
        
        const getAllFromStore = (db: IDBDatabase, storeName: string) => new Promise<any[]>((resolve) => {
          try {
            const transaction = db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => resolve([]);
          } catch {
            resolve([]);
          }
        });
        
        // 获取图片数据
        for (const dbInfo of imageDBs) {
          try {
            const db = await openDB(dbInfo.name!);
            const storeNames = Array.from(db.objectStoreNames);
            
            for (const storeName of storeNames) {
              console.log(`    🔍 检查存储: ${storeName}`);
              const data = await getAllFromStore(db, storeName);
              console.log(`      📊 数据条数: ${data.length}`);
              
              if (data.length > 0) {
                console.log(`      📋 前3条样本:`, data.slice(0, 3).map(item => ({
                  keys: Object.keys(item || {}),
                  hasFilename: !!item?.filename,
                  hasId: !!item?.id
                })));
                
                // 检查所有存储，不只是名称匹配的
                allImages.push(...data);
              }
            }
            db.close();
          } catch (error) {
            console.warn(`无法访问数据库 ${dbInfo.name}:`, error);
          }
        }
        
        console.log(`📸 找到 ${allImages.length} 个图片数据`);
        
        if (allImages.length === 0) {
          throw new Error('IndexedDB中没有找到图片数据');
        }
        
        // 按作品分组 - 增强版本
        const groups: Record<string, any[]> = {};
        for (const image of allImages) {
          let groupId = 'unknown_work';
          
          // 方法1：从文件名提取时间戳
          if (image.filename) {
            const timestampMatch = image.filename.match(/img_(\d+)_/);
            if (timestampMatch) {
              const timestamp = parseInt(timestampMatch[1]);
              const groupTimestamp = Math.floor(timestamp / (15 * 60 * 1000)) * (15 * 60 * 1000);
              groupId = `comic_${groupTimestamp}`;
            } else {
              // 方法2：从文件名其他部分提取
              const parts = image.filename.split('_');
              if (parts.length >= 2) {
                groupId = `comic_${parts[1]}`;
              }
            }
          } 
          // 方法3：从storedAt时间分组
          else if (image.storedAt) {
            const date = new Date(image.storedAt);
            const groupTimestamp = Math.floor(date.getTime() / (15 * 60 * 1000)) * (15 * 60 * 1000);
            groupId = `comic_time_${groupTimestamp}`;
          }
          // 方法4：从id提取
          else if (image.id) {
            const idParts = image.id.split('_');
            if (idParts.length >= 2) {
              groupId = `comic_id_${idParts[1]}`;
            }
          }
          
          if (!groups[groupId]) groups[groupId] = [];
          groups[groupId].push(image);
        }
        
        console.log('📊 分组结果:', Object.entries(groups).map(([id, imgs]) => ({ id, count: imgs.length })));
        
        // 过滤有效组（至少1张图片，降低门槛）
        const validGroups = Object.fromEntries(
          Object.entries(groups).filter(([_, images]) => images.length >= 1)
        );
        
        console.log('📊 有效分组:', Object.entries(validGroups).map(([id, imgs]) => ({ id, count: imgs.length })));
        
        // 重建ComicResult
        const rebuiltComics = Object.entries(validGroups).map(([groupId, images], index) => ({
          id: groupId,
          promptId: groupId,
          storyText: `重建的漫画作品 ${index + 1}`,
          images: images.map((img: any) => `indexeddb:${img.id}`),
          finalComicUrl: images[0] ? `indexeddb:${images[0].id}` : '',
          parameters: {
            workflow: {},
            userInput: {
              storyPrompt: `重建的漫画作品 ${index + 1}`,
              style: { 
                id: 'rebuilt', name: '重建风格', description: '从IndexedDB重建',
                modelConfig: { styleModel: '', strength: 1, strengthType: 'normal' },
                visualStyle: { colorScheme: 'auto', lineStyle: 'auto', shading: 'auto' }
              },
              characterConfig: { enableFaceSwap: false, pulidWeight: 1.0, facialFeatures: { faceRestoreStrength: 1.0, visibility: 1.0 } },
              layoutConfig: { columns: 2, panelWidth: 1024, panelHeight: 1024, separator: '|||~~~|||', spacing: 20 }
            },
            priority: 'normal' as const
          },
          createdAt: new Date(),
          status: 'completed' as const,
          title: `重建的漫画作品 ${index + 1}`,
          style: '重建'
        }));
        
        console.log(`✅ 重建完成: ${rebuiltComics.length} 个ComicResult作品`);
        
        // 保存并刷新
        localStorage.setItem('comic-generation-results', JSON.stringify(rebuiltComics));
        setTimeout(() => window.location.reload(), 2000);
        
        return `✅ 成功重建 ${rebuiltComics.length} 个作品，页面即将刷新`;
        
      } catch (error) {
        console.error('❌ 重建失败:', error);
        return `❌ 失败: ${(error as Error).message}`;
      }
    };
    
    console.log('🚨 IndexedDB重建工具已加载: rebuildFromIndexedDB()');
  }
};

// 立即加载工具
loadIndexedDBRebuildTool();
// 🚀 导入统一WebSocket管理器
import { getMasterWebSocketManager } from '@/services/masterWebSocketManager';
// import { clientIdManager } from './services/clientIdManager'; // 已禁用
import type { 
  ComicStyle, 
  LayoutConfig, 
  CharacterConfig, 
  UserInput, 
  ComicResult,
  ProgressUpdate,
  ConnectionStatus as ConnectionStatusType
} from './types';

// 组件导入
import StoryInputPanel from './modules/StoryInputPanel.vue';
import StyleConfigPanel from './modules/StyleConfigPanel.vue';
import ComicGenerationStage from './modules/ComicGenerationStage.vue';
import ComicGalleryWrapper from './components/ComicGalleryWrapper.vue';
import CompactControlPanel from './components/CompactControlPanel.vue';
import LANDebugPanel from './components/LANDebugPanel.vue';
import DebugTestTool from './components/DebugTestTool.vue';
import PNAPermissionModal from '@/components/common/PNAPermissionModal.vue';
import { simpleStorageBridge } from '@/utils/simpleStorageBridge';
// 🚨 导入数据恢复工具
import '@/utils/comicDataRecovery';
// 🚨 导入IndexedDB紧急恢复工具
import '@/utils/emergencyComicRecovery';

// 🌟 使用各种store
const storyStore = useStoryStore();
const comicStore = useComicStore();
// 🔑 关键修复：只订阅ComicMainPanel真正需要的store属性，避免头像上传触发重新渲染
const { 
  isLoadingComics, // 导入加载状态
  isGenerating, 
  generationProgress, 
  currentStatusText,
  selectedStyle,
  // protagonistName,     // 不需要响应式，需要时直接从store获取
  // protagonistGender,   // 不需要响应式，需要时直接从store获取
  // protagonistAge,      // 不需要响应式，需要时直接从store获取
  // selectedStoryType,   // 不需要响应式，需要时直接从store获取
  // generatedStory,      // 不需要响应式，需要时直接从store获取
  finalComicResult,
  connectionStatus,
  error,
  // 🔄 多设备状态 (已禁用)
  // isDeviceActive,
  // deviceInfo
} = storeToRefs(storyStore);

// 本地状态 - 保留一些UI相关的本地状态
const particleLayerRef = ref<HTMLElement>();
const generationStageRef = ref<InstanceType<typeof ComicGenerationStage>>();
const ambientAudioRef = ref<HTMLAudioElement>();
const currentGenerationResult = ref<ComicResult | null>(null);

// 数据迁移相关状态
const showDataMigration = ref(false);
const syncStatus = ref({ needsSync: false, isLocalhost: false, hasData: false });

// 🔄 多设备状态同步相关 (已禁用)
/*
const deviceSyncStatus = ref({
  isActive: false,
  hasOtherActiveDevices: false,
  deviceCount: 0,
  activeDeviceName: '',
  lastSyncTime: 0
});

// 🔄 生成状态同步指示器
const showSyncIndicator = computed(() => {
  return isGenerating.value && !isDeviceActive.value;
});

// 🔄 设备状态显示文本
const deviceStatusText = computed(() => {
  if (isDeviceActive.value) {
    return '当前设备 (主控)';
  } else if (deviceSyncStatus.value.activeDeviceName) {
    return `观察模式 (${deviceSyncStatus.value.activeDeviceName}控制中)`;
  } else {
    return '就绪';
  }
});
*/

// 🎯 修复：直接在主面板管理风格图片，避免localStorage传递问题
const currentStyleImage = ref<string>('');

// 🆕 新工作流的风格参数
const newWorkflowStyleParams = ref({
  visualStylePreference: '', // 106号节点参数
  globalStyleDescription: '', // 80号节点参数
  styleImage: '', // 51号节点图片
  styleName: ''
});

// 生成参数控制
const generationParameters = ref({
  styleStrength: 0.2,     // 75号节点 - 用户说0.2就有很强效果，设为默认值
  portraitWeight: 1.0,    // 126号节点  
  steps: 20,              // 65号节点
  mixingValue: 0.7,       // 123号节点
  selectedStylePrompt: '' // 85号节点
});

// 🔄 监听参数变化 (状态同步已禁用)
/*
watch(generationParameters, (newParams) => {
  if (isDeviceActive.value) {
    // 只有主控设备才广播参数变化
    broadcastParameterChange(newParams);
  }
}, { deep: true });
*/

const layoutConfig = ref<LayoutConfig>({
  columns: 2,
  panelWidth: 1024,
  panelHeight: 1024,
  separator: '|||~~~|||',
  spacing: 20
});

const characterConfig = ref<CharacterConfig>({
  enableFaceSwap: false,
  pulidWeight: 1.0,
  facialFeatures: {
    faceRestoreStrength: 1.0,
    visibility: 1.0
  }
});

const estimatedTime = ref(0);
const showGallery = ref(false);
const showQuickActions = ref(false);
const currentStoryInput = ref('');
const currentGenerationParams = ref(null);

// 🔑 移除savedComics计算属性 - 通过ComicGalleryWrapper隔离防止头像上传触发重新渲染

// 计算属性
const migrationNeeded = computed(() => syncStatus.value.needsSync);

// 计算属性
const characterStatusClass = computed(() => {
  if (isGenerating.value) return 'status-working';
  if (connectionStatus.value === 'connected') return 'status-ready';
  return 'status-idle';
});

const statusIcon = computed(() => {
  if (isGenerating.value) return 'fas fa-cog fa-spin';
  if (connectionStatus.value === 'connected') return 'fas fa-check-circle';
  return 'fas fa-circle';
});

const statusText = computed(() => {
  if (isGenerating.value) return '创作中...';
  if (connectionStatus.value === 'connected') return '就绪';
  return '待机';
});

// 事件处理器
const handleStoryGenerated = (story: string) => {
  currentStoryInput.value = story;
  // 🌟 更新 storyStore 中的故事内容
  storyStore.generatedStory = story;
  
  // 🔄 激活当前设备 (状态同步已禁用)
  // activateCurrentDevice();
};

const handleParameterChanged = ({ key, value }: { key: string; value: any }) => {
  console.log(`🎛️ 接收参数变更: ${key} = ${value}`);
  
  // 更新本地参数
  if (key in generationParameters.value) {
    const oldValue = (generationParameters.value as any)[key];
    (generationParameters.value as any)[key] = value;
    console.log(`  ✅ 更新 generationParameters.${key}: ${oldValue} → ${value}`);
  } else {
    console.warn(`  ⚠️ 未知参数键: ${key}`);
  }
  
  // 🔍 显示更新后的完整参数状态
  console.log('📊 当前生成参数状态:', generationParameters.value);
  
  // 根据key更新对应的节点参数
  switch (key) {
    case 'styleStrength':
      // 75号节点的strength参数
      console.log(`  🎯 将影响节点75的strength参数: ${value}`);
      break;
    case 'portraitWeight':
      // 126号节点的weight参数
      console.log(`  🎯 将影响节点126的weight参数: ${value}`);
      break;
    case 'steps':
      // 65号节点的steps参数
      console.log(`  🎯 将影响节点65的steps参数: ${value}`);
      break;
    case 'mixingValue':
      // 123号节点的conditioning_to_strength参数
      console.log(`  🎯 将影响节点123的conditioning_to_strength参数: ${value}`);
      break;
    case 'selectedStylePrompt':
      // 85号节点的style_prompt_override参数
      generationParameters.value.selectedStylePrompt = value;
      console.log(`  🎯 将影响节点85的style_prompt_override参数: ${value}`);
      break;
  }
};

// 🎯 优化：处理风格选择事件，使用文件引用 - 解决Base64内存泄漏
const handleStyleSelected = async (style: any) => {
  console.log('🎨 主面板接收风格选择:', style.name);
  console.log('  - 风格ID:', style.id);
  console.log('  - 有参考图片:', !!style.referenceImage);
  
  // 🌟 使用 storyStore 的方法更新风格
  await storyStore.updateSelectedStyle(style);
  
  // 🔧 关键修复：确保currentStyleImage.value始终是字符串，而不是对象
  if (style.referenceImage) {
    if (typeof style.referenceImage === 'object' && style.referenceImage.url) {
      // 文件引用对象格式：提取URL字符串
      currentStyleImage.value = style.referenceImage.url;
      console.log('✅ 从文件引用对象提取URL:', style.referenceImage.url);
    } else if (typeof style.referenceImage === 'string' && style.referenceImage.length > 0) {
      // 字符串URL格式（HTTP URL或Base64）
      currentStyleImage.value = style.referenceImage;
      console.log('✅ 使用字符串URL参考图片:', style.referenceImage.substring(0, 50) + '...');
    } else {
      currentStyleImage.value = '';
      console.log('ℹ️ 风格参考图片为空或无效格式');
    }
  } else {
    currentStyleImage.value = '';
    console.log('ℹ️ 选中风格没有设置参考图片');
  }
};

const handleStartGeneration = async () => {
  console.log('🎬 ComicGenerationStage触发开始生成');
  
  // 🖒 安全检查：防止重复执行和崩溃
  if (isGenerating.value) {
    console.warn('⚠️ 生成已在进行中，忽略新请求');
    return;
  }
  
  // 清空之前的结果
  currentGenerationResult.value = null;
  
  // 🔑 关键修复：直接调用完整的参数构建和生成流程
  await performActualGeneration();
};

// 🔑 处理从StoryInputPanel触发的生成事件
const handleGenerationStarted = async (eventParams?: any) => {
  // 🖒 安全检查：防止重复执行和崩溃
  if (isGenerating.value) {
    console.warn('⚠️ 生成已在进行中，忽略新请求');
    return;
  }
  
  console.log('🎬 [ComicMainPanel] handleGenerationStarted 被调用，来源:', eventParams?.triggerSource || 'ComicGenerationStage');
  console.log('📝 接收到的参数:', eventParams);
  
  // 🔑 关键修复：如果传入了故事内容，先同步到storyStore
  if (eventParams?.generatedStory) {
    console.log('✅ 同步故事内容到storyStore:', eventParams.generatedStory.substring(0, 50) + '...');
    storyStore.generatedStory = eventParams.generatedStory;
    // 直接设置到store的generatedStory即可，不需要调用不存在的方法
  }
  
  // 🔑 关键修复：无论从哪里触发，都使用完整的参数构建
  await performActualGeneration();
};

// 🎯 优化：图片上传处理函数 - 解决Base64内存泄漏
const processImageForGeneration = async (imageData: string | null, imageName: string): Promise<FileReference | null> => {
  if (!imageData) {
    console.log(`⚠️ ${imageName}数据为空，跳过处理`);
    return null;
  }

  // 🔧 修复：支持Base64和HTTP URL两种格式
  if (ImageUploadHelper.isBase64ImageData(imageData)) {
    // Base64格式：上传到服务器获取文件引用
    console.log(`🎯 开始处理${imageName}Base64图片上传...`);
    const result = await imageUploadHelper.uploadBase64Image(imageData, `${imageName}_${Date.now()}.png`);
    
    if (result.success && result.fileReference) {
      console.log(`✅ ${imageName}Base64图片上传成功:`, result.fileReference.filename);
      return result.fileReference;
    } else {
      console.error(`❌ ${imageName}Base64图片上传失败:`, result.error);
      throw new Error(`${imageName}Base64图片上传失败: ${result.error}`);
    }
  } else if (imageData.startsWith('http://') || imageData.startsWith('https://') || imageData.startsWith('/')) {
    // HTTP URL格式：转换为文件引用格式
    console.log(`🔧 ${imageName}是HTTP URL格式，转换为文件引用:`, imageData);
    
    // 提取文件名
    const urlParts = imageData.split('/');
    const filename = urlParts[urlParts.length - 1] || `${imageName}_${Date.now()}.png`;
    
    const fileReference: FileReference = {
      filepath: imageData,
      filename: filename,
      url: imageData,
      size: 0, // HTTP URL无法获取大小
      timestamp: Date.now(),
      originalName: filename
    };
    
    console.log(`✅ ${imageName}HTTP URL转换为文件引用成功:`, fileReference);
    return fileReference;
  } else {
    console.warn(`⚠️ ${imageName}格式不支持:`, imageData.substring(0, 100) + '...');
    return null;
  }
};

// 🔑 内部实际生成函数（重构后的完整参数构建）- 🎯 优化：使用文件引用
const performActualGeneration = async () => {
  // 🌐 移动端环境检测
  const currentHost = window.location.hostname;
  const isLANAccess = currentHost !== 'localhost' && currentHost !== '127.0.0.1';
  const isMobile = typeof navigator !== 'undefined' && navigator.userAgent ?
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) : false;
  
  console.log('🎬 开始创作流程:', {
    环境: isLANAccess ? `局域网(${currentHost})` : '本地',
    设备: isMobile ? '移动设备' : '桌面设备',
    主角名称: storyStore.protagonistName,
    故事类型: storyStore.selectedStoryType?.name
  });
  
  // 🔑 修复：构建完整的生成参数（完全使用用户实际选择，无硬编码！）
  
  // 🔍 详细数据验证和调试
  console.log('🔍 [DEBUG] 开始生成前的数据状态检查:');
  console.log('  📝 故事数据:', {
    protagonistName: storyStore.protagonistName,
    protagonistGender: storyStore.protagonistGender,
    protagonistAge: storyStore.protagonistAge,
    selectedStoryType: storyStore.selectedStoryType?.name,
    generatedStory: storyStore.generatedStory ? `${storyStore.generatedStory.length}字符: ${storyStore.generatedStory.substring(0, 100)}...` : '未生成'
  });
  console.log('  🖼️ 图片数据:', {
    protagonistImage: storyStore.protagonistImage 
      ? (typeof storyStore.protagonistImage === 'string' 
          ? `Base64数据: ${storyStore.protagonistImage.length}字符` 
          : `文件引用: ${storyStore.protagonistImage.filename || '未知文件'}`)
      : '无头像',
    styleImage: currentStyleImage.value 
      ? (typeof currentStyleImage.value === 'string' 
          ? `Base64数据: ${currentStyleImage.value.length}字符` 
          : `文件引用: ${currentStyleImage.value.filename || '未知文件'}`)
      : '无风格图'
  });
  console.log('  🎨 风格数据:', {
    selectedStyleName: selectedStyle.value?.name,
    selectedStyleDescription: selectedStyle.value?.description?.substring(0, 50),
    selectedStyleId: selectedStyle.value?.id
  });
  console.log('  🎛️ 控制参数:', generationParameters.value);

  // 🚨 参数验证：确保所有必需数据都存在
  if (!storyStore.protagonistName) {
    throw new Error('请输入主角姓名');
  }
  if (!storyStore.selectedStoryType) {
    throw new Error('请选择故事类型');
  }
  // 🔧 修复：检查故事内容，支持多种来源
  const storyContent = storyStore.generatedStory || storyStore.currentStory || '';
  if (!storyContent) {
    console.error('❌ 故事内容为空！请检查：');
    console.error('  - storyStore.generatedStory:', storyStore.generatedStory);
    console.error('  - storyStore.currentStory:', storyStore.currentStory);
    console.error('  - 请确保在故事创作面板中生成了故事预览');
    throw new Error('故事内容为空，请在故事创作面板中生成故事预览');
  }
  console.log('✅ 故事内容检查通过:', storyContent.substring(0, 50) + '...');
  if (!selectedStyle.value) {
    throw new Error('请选择漫画风格');
  }
  
  // 🎯 优化：处理图片上传，获取文件引用
  console.log('🎯 开始处理图片上传...');
  const [protagonistImageRef, styleImageRef] = await Promise.all([
    processImageForGeneration(storyStore.protagonistImage, '主角头像'),
    processImageForGeneration(currentStyleImage.value, '风格参考')
  ]);
  
  console.log('✅ 图片处理完成:', {
    protagonistImage: protagonistImageRef ? protagonistImageRef.filename : '无',
    styleImage: styleImageRef ? styleImageRef.filename : '无'
  });
  
  const params = {
    // 🔑 基础信息（确保有默认值，防止undefined）
    protagonistName: storyStore.protagonistName || '主角',
    protagonistGender: storyStore.protagonistGender || 'male',
    protagonistAge: storyStore.protagonistAge || 'young',
    protagonistImage: protagonistImageRef?.url || null, // 🔧 修复：使用URL而不是文件名，以便ComfyUI可以fetch
    
    // 🔑 故事信息（如果没有预生成故事，让ComfyUI自动生成）
    storyType: storyStore.selectedStoryType?.name || '奇幻冒险',
    generatedStory: storyContent, // 使用检查后的故事内容
    
    // 🔑 风格信息（用户实际选择的风格）
    styleImage: styleImageRef?.url || null, // 🔧 修复：使用URL而不是整个引用对象
    visualStyle: selectedStyle.value?.name || '像素风格',
    visualStyleDescription: selectedStyle.value?.description || '复古8位像素艺术风格',
    
    // 🆕 新工作流专用参数
    visualStylePreference: selectedStyle.value?.name || '像素风格', // 使用实际选择的风格名称
    globalStyleDescription: selectedStyle.value?.description || 'masterpiece, best quality',
    
    // 🆕 新工作流支持
    workflowType: 'new',
    
    // 🆕 新工作流控制参数
    newWorkflowParams: {
      fusionStrength: generationParameters.value.styleStrength,     // 风格强度 → 90号节点
      pulidWeight: generationParameters.value.portraitWeight,       // 人像强度 → 38号节点  
      conditioningStrength: generationParameters.value.mixingValue, // 混合数值 → 49号节点
      steps: generationParameters.value.steps                       // 生成步数 → 72号节点
    },
    
    // 旧工作流控制参数（向后兼容）
    controlParameters: generationParameters.value,
    
    // 🌐 移动端和局域网支持参数
    deviceInfo: {
      isLANAccess,
      isMobile,
      currentHost,
      userAgent: typeof navigator !== 'undefined' && navigator.userAgent ?
        navigator.userAgent : 'unknown'
    }
  };
  
  // 🔄 确保当前设备为主控设备 (状态同步已禁用)
  // await activateCurrentDevice();
  
  // 🔑 关键修复：现在用完整参数调用startGeneration
  await storyStore.startGeneration(params);
  
  console.log('🔄 构建完成的完整生成参数（文件引用模式）:');
  console.log('  📋 主角信息:', { 
    name: params.protagonistName, 
    gender: params.protagonistGender, 
    age: params.protagonistAge,
    hasImage: !!params.protagonistImage,
    imageFile: params.protagonistImage?.filename || '无文件'
  });
  console.log('  📖 故事信息:', {
    type: params.storyType,
    storyLength: params.generatedStory?.length || 0,
    storyPreview: params.generatedStory?.substring(0, 100) + '...'
  });
  console.log('  🎨 风格信息:', {
    name: params.visualStyle,
    description: params.visualStyleDescription?.substring(0, 50) + '...',
    hasStyleImage: !!params.styleImage,
    styleImageFile: params.styleImage?.filename || '无文件'
  });
  console.log('  🎛️ 控制参数:', params.newWorkflowParams);
  console.log(`  🌐 设备环境: ${isMobile ? '移动端' : '桌面端'} ${isLANAccess ? '局域网' : '本地'}`);
  
  currentGenerationParams.value = params as any;
  
  // 🔄 增强状态管理
  try {
    console.log('✅ Store状态更新成功');
  } catch (error) {
    console.error('❌ Store状态更新失败:', error);
  }
  
  try {
    // 🌐 检查是否是后端直接处理的请求（通常不会有这种情况）
    if (params.backendResult) {
      console.log('🌐 检测到后端直接处理结果，跳过ComfyUI流程');
      console.log('📋 后端结果:', params.backendResult);
      
      // 模拟生成完成
      setTimeout(() => {
        // 使用storyStore方法重置状态
        storyStore.setGenerationError(''); // 清除错误状态
        console.log('✅ 后端生成流程完成');
      }, 2000);
      
      return; // 直接返回，不执行ComfyUI流程
    }
    
    console.log('🎬 handleGenerationStarted: 开始ComfyUI漫画生成流程...');
    console.log('📋 生成参数:', params);
    
    // ✅ WebSocket系统已通过storyStore.startGeneration启动，会自动处理进度更新和结果
    console.log('🎬 WebSocket生成系统已启动，等待ComfyUI响应...');
    
  } catch (error) {
    console.error('🚨 漫画生成失败:', error);
    
    // 🖒 安全错误处理，防止崩溃
    try {
      // 确保生成状态被重置
      storyStore.setGenerationError(error instanceof Error ? error.message : '生成失败');
      
      // 安全地调用错误处理函数
      if (typeof handleGenerationError === 'function') {
        handleGenerationError(error instanceof Error ? error.message : '生成失败');
      } else {
        console.error('❌ handleGenerationError 函数不存在');
      }
      
      // 清理可能的内存泄漏
      if (currentGenerationParams.value) {
        currentGenerationParams.value = null;
      }
      
    } catch (cleanupError) {
      console.error('🚨 错误清理失败:', cleanupError);
      // 强制重置状态
      storyStore.setGenerationError('系统错误');
    }
  } finally {
    // 🔒 确保无论如何都重置生成状态
    if (isGenerating.value) {
      console.log('🔄 finally 块中强制重置生成状态');
      storyStore.setGenerationError(''); // 清除生成状态
    }
  }
};

const handleProgressUpdate = (progress: ProgressUpdate) => {
  storyStore.updateProgress(progress.progress || 0);
  
  if (progress.type === 'complete') {
    // 🔧 修复：只处理来自ComicGenerator的完成事件（包含ComicResult数据）
    if (progress.data && progress.data.id && progress.data.images) {
      console.log('✅ 收到ComicGenerator处理后的完成数据:', progress.data.id);
      handleGenerationComplete(progress.data);
    } else {
      console.warn('⚠️ 完成事件数据格式不正确，等待ComicGenerator处理:', progress);
    }
  } else if (progress.type === 'comfyui_complete') {
    // 🔧 新增：ComfyUI原始完成事件，仅用于状态更新
    console.log('📡 收到ComfyUI原始完成信号，等待ComicGenerator处理结果...');
    storyStore.updateProgress(80); // 显示80%进度表示正在处理结果
  } else if (progress.type === 'error') {
    handleGenerationError(progress.error || '未知错误');
  } else if (progress.type === 'progress' || progress.type === 'status') {
    // 转发其他进度更新到UI
    console.log('📊 进度更新:', progress.message, progress.progress || '');
  }
};

const handleGenerationComplete = async (result: ComicResult) => {
  // 确保result有必要的字段
  if (!result) {
    console.error('❌ handleGenerationComplete: result为空');
    return;
  }
  
  // 检查是否是ComfyUI原始结果而不是处理后的ComicResult
  if (!result.id && (result as any).outputs) {
    console.warn('⚠️ 收到ComfyUI原始结果而不是处理后的ComicResult，忽略此调用');
    console.log('ComfyUI原始结果结构:', Object.keys(result));
    return;
  }
  
  if (!result.id) {
    console.error('❌ handleGenerationComplete: result缺少id字段', result);
    return;
  }

  console.log('✅ 处理生成完成:', result);
  
  // 🔍 验证和修复ComicResult
  console.log('🔍 验证ComicResult完整性...');
  const validation = ComicResultValidator.validateComicResult(result);
  if (!validation.isValid) {
    console.warn('⚠️ ComicResult验证失败:', validation.errors);
    console.log('📊 完整验证报告:\n' + ComicResultValidator.generateReport(result));
  }
  
  // 标准化数据结构，确保兼容新旧格式
  const normalizedResult: ComicResult = ComicResultValidator.fixComicResult({
    ...result,
    // 确保必需字段存在
    storyText: result.storyText || (result as any).story || '',
    createdAt: result.createdAt || new Date(),
    promptId: result.promptId || '',
    images: result.images || [],
    finalComicUrl: result.finalComicUrl || (result.images && result.images[0]) || '',
    parameters: result.parameters || {
      workflow: {},
      userInput: {
        storyPrompt: '',
        style: { id: '', name: '', description: '', modelConfig: { styleModel: '', strength: 0, strengthType: '' }, visualStyle: { colorScheme: '', lineStyle: '', shading: '' } },
        characterConfig: { enableFaceSwap: false, pulidWeight: 1.0, facialFeatures: { faceRestoreStrength: 1.0, visibility: 1.0 } },
        layoutConfig: { columns: 2, panelWidth: 1024, panelHeight: 1024, separator: '|||~~~|||', spacing: 20 }
      },
      priority: 'normal' as const
    },
    status: 'completed' as const
  });

  // 设置当前生成结果，供ComicGenerationStage显示
  currentGenerationResult.value = normalizedResult;

  // 🔑 修复：确保状态同步完成后再保存
  console.log('🔄 执行生成完成状态同步...');
  storyStore.completeGeneration(normalizedResult);
  
  // 🔧 修复：等待状态同步完成，特别是局域网环境
  await new Promise(resolve => setTimeout(resolve, 200));
  
  // 🔑 强化保存流程：确保数据真正持久化
  console.log('💾 开始保存漫画到本地存储...');
  console.log('📋 保存的漫画数据:', {
    id: normalizedResult.id,
    status: normalizedResult.status,
    imagesCount: normalizedResult.images.length,
    hasStoryText: !!normalizedResult.storyText,
    hasOutputPaths: !!normalizedResult.outputPaths
  });
  
  // 保存前诊断存储状态
  console.log('🔍 保存前存储诊断:');
  StorageDebugger.printDiagnosticReport();
  
  try {
    // 🔑 修复：确保保存和UI更新的可靠性
    console.log('📝 开始保存漫画到存储，ID:', normalizedResult.id);
    console.log('📝 保存的漫画数据:', normalizedResult);
    
    // 💾 正常保存流程
    console.log('💾 开始调用comicStorage.saveComic...');
    console.log('📋 保存的数据结构:', {
      id: normalizedResult.id,
      status: normalizedResult.status,
      有images: !!normalizedResult.images,
      images数量: normalizedResult.images?.length || 0,
      有storyText: !!normalizedResult.storyText,
      storyText长度: normalizedResult.storyText?.length || 0
    });
    
    try {
      console.log('🔄 即将调用 comicStorage.saveComic...');
      console.log('📋 传递给saveComic的数据:', {
        id: normalizedResult.id,
        数据类型: typeof normalizedResult,
        是否为空: normalizedResult === null || normalizedResult === undefined,
        对象键: Object.keys(normalizedResult || {})
      });
      
      // 使用明确的await和错误处理
      const saveResult = await comicStorage.saveComic(normalizedResult);
      console.log('✅ comicStorage.saveComic调用完成', saveResult);
      
      // 🔧 成功保存后立即更新Store
      const currentComics = Array.isArray(comicStore.savedComics) ? [...comicStore.savedComics] : [];
      const existingIndex = currentComics.findIndex(c => c.id === normalizedResult.id);
      if (existingIndex >= 0) {
        currentComics[existingIndex] = normalizedResult;
        console.log('✅ 更新了现有作品');
      } else {
        currentComics.unshift(normalizedResult);
        console.log('✅ 添加了新作品');
      }
      comicStore.savedComics = currentComics;
      comicStore.updateStatistics();
      console.log(`✅ 保存成功后更新Store，当前作品数: ${currentComics.length}`);
    } catch (saveError) {
      console.error('❌ comicStorage.saveComic 抛出异常:', saveError);
      console.error('❌ 异常类型:', saveError?.constructor?.name);
      console.error('❌ 异常消息:', saveError?.message);
      console.error('❌ 异常堆栈:', saveError?.stack);
      
      // 尝试手动保存 - 🔧 关键修复：只保存轻量级数据
      console.log('🔧 尝试手动localStorage保存（轻量级数据）...');
      try {
        // 🔑 只保存路径和基本信息，避免Base64数据造成容量问题
        const lightBackup = {
          id: normalizedResult.id,
          title: normalizedResult.title?.substring(0, 50) || '',
          createdAt: normalizedResult.createdAt,
          // 🚨 关键：确保images数组只包含URL路径，过滤掉Base64数据
          images: normalizedResult.images?.filter(img => 
            typeof img === 'string' && !img.startsWith('data:image/')
          ).slice(0, 3) || [],
          finalComicUrl: (normalizedResult.finalComicUrl && !normalizedResult.finalComicUrl.startsWith('data:image/')) 
            ? normalizedResult.finalComicUrl : ''
        };
        
        const existingBackups = JSON.parse(localStorage.getItem('comic-generation-results') || '[]');
        existingBackups.unshift(lightBackup);
        // 限制数量
        if (existingBackups.length > 20) {
          existingBackups.splice(20);
        }
        
        const manualData = JSON.stringify(existingBackups);
        localStorage.setItem('comic-generation-results', manualData);
        console.log('✅ 手动保存成功（轻量级数据）', lightBackup);
      } catch (manualError) {
        console.error('❌ 手动保存也失败:', manualError);
      }
      
      // 🔧 关键修复：无论保存是否成功，都要确保新作品在当前列表中
      console.log('🔧 强制将新作品添加到当前列表...');
      const currentComics = Array.isArray(comicStore.savedComics) ? [...comicStore.savedComics] : [];
      
      // 检查是否已存在
      const existingIndex = currentComics.findIndex(c => c.id === normalizedResult.id);
      if (existingIndex >= 0) {
        currentComics[existingIndex] = normalizedResult;
        console.log('✅ 更新了现有作品');
      } else {
        currentComics.unshift(normalizedResult);
        console.log('✅ 添加了新作品');
      }
      
      // 直接更新comicStore
      comicStore.savedComics = currentComics;
      comicStore.updateStatistics();
      console.log(`✅ 强制更新后当前作品数: ${currentComics.length}`);
    }
    
    // 🔧 修复局域网环境：延迟广播状态更新，确保状态同步完成
    console.log('📡 强制广播状态更新（解决局域网同步问题）...');
    try {
      // 延迟发出事件，确保 storyStore 状态先完成
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('comic-generation-completed', {
            detail: {
              comic: normalizedResult,
              timestamp: Date.now(),
              source: 'ComicMainPanel'
            }
          }));
          console.log('✅ 已发出comic-generation-completed事件');
          
          // 额外的状态强制刷新
          window.dispatchEvent(new CustomEvent('force-gallery-refresh', {
            detail: { timestamp: Date.now() }
          }));
          console.log('✅ 已发出force-gallery-refresh事件');
        }
      }, 300); // 延迟300ms确保状态完成
    } catch (broadcastError) {
      console.error('❌ 状态广播失败:', broadcastError);
    }
    
    // 🕰️ 等待100ms确保异步操作完成
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 🔍 立即检查localStorage内容
    const immediateCheck = localStorage.getItem('comic-generation-results');
    console.log('🔍 保存后立即检查localStorage:', {
      数据存在: !!immediateCheck,
      数据大小: immediateCheck ? immediateCheck.length : 0,
      数据片段: immediateCheck ? immediateCheck.substring(0, 100) + '...' : 'null'
    });
    
    if (immediateCheck) {
      try {
        const parsed = JSON.parse(immediateCheck);
        console.log('📋 解析后的localStorage数据:', {
          类型: typeof parsed,
          是否数组: Array.isArray(parsed),
          数量: Array.isArray(parsed) ? parsed.length : '\u4e0d是数组',
          ID列表: Array.isArray(parsed) ? parsed.map(c => c.id) : '无法获取'
        });
      } catch (parseError) {
        console.error('❌ localStorage数据解析失败:', parseError);
      }
    } else {
      console.error('❌ 保存后 localStorage仍为空!');
    }
    
    // 立即检查localStorage内容
    const rawData = localStorage.getItem('comic-generation-results');
    console.log('📦 localStorage原始数据:', rawData);
    if (rawData) {
      try {
        const parsed = JSON.parse(rawData);
        console.log('📦 localStorage解析后数据:', parsed);
        console.log('📦 数据类型:', typeof parsed, Array.isArray(parsed) ? 'array' : 'not array');
      } catch (e) {
        console.error('❌ localStorage数据解析失败:', e);
      }
    }
    

  // 🔑 修复验证逻辑：使用保存时的实际ID进行验证
  console.log('🔍 验证保存结果...');
  
  // 🔧 关键修复：从localStorage获取实际保存的数据，而不是依赖传入的ID
  const rawStorageData = localStorage.getItem('comic-generation-results');
  let verificationSuccess = false;
  let actualSavedId = null;
  
  if (rawStorageData) {
    try {
      const storedComics = JSON.parse(rawStorageData);
      if (Array.isArray(storedComics)) {
        console.log('📋 当前localStorage中的作品列表:');
        storedComics.forEach((comic, index) => {
          console.log(`  ${index + 1}. ID: ${comic.id}, 标题: ${comic.title || '未命名'}, 状态: ${comic.status}`);
        });
        
        // 🔍 尝试多种方式查找刚保存的漫画
        // 方式1：通过ID查找
        let foundComic = storedComics.find(c => c.id === normalizedResult.id);
        if (foundComic) {
          actualSavedId = foundComic.id;
          console.log(`✅ 通过ID找到保存的漫画: ${actualSavedId}`);
          verificationSuccess = true;
        } else {
          // 方式2：通过创建时间查找（最新的）
          const sortedComics = storedComics.sort((a, b) => {
            const timeA = new Date(a.createdAt).getTime();
            const timeB = new Date(b.createdAt).getTime();
            return timeB - timeA;
          });
          
          const latestComic = sortedComics[0];
          if (latestComic) {
            const timeDiff = Date.now() - new Date(latestComic.createdAt).getTime();
            // 如果最新漫画是在5秒内创建的，认为是刚保存的
            if (timeDiff < 5000) {
              actualSavedId = latestComic.id;
              console.log(`✅ 通过创建时间找到保存的漫画: ${actualSavedId} (时差: ${timeDiff}ms)`);
              verificationSuccess = true;
            } else {
              console.warn(`⚠️ 最新漫画创建时间过早，时差: ${timeDiff}ms`);
            }
          }
        }
        
        if (!verificationSuccess) {
          console.error('❌ 保存验证失败 - 漫画未在localStorage中找到');
          console.error('🔍 查找的ID:', normalizedResult.id);
          console.error('📋 当前localStorage中的作品ID:', storedComics.map(c => c.id));
          console.error('📊 详细信息:', {
            查找ID: normalizedResult.id,
            ID类型: typeof normalizedResult.id,
            存储的ID列表: storedComics.map(c => ({ id: c.id, type: typeof c.id }))
          });
        }
      } else {
        console.error('❌ localStorage数据格式错误，不是数组');
      }
    } catch (parseError) {
      console.error('❌ localStorage数据解析失败:', parseError);
    }
  } else {
    console.error('❌ localStorage中没有找到数据');
  }
  
  // 如果直接验证失败，尝试通过comicStorage验证（降级方案）
  if (!verificationSuccess) {
    console.log('🔄 尝试通过comicStorage验证...');
    const comicStorageVerification = comicStorage.getComicById(normalizedResult.id);
    if (comicStorageVerification) {
      console.log('✅ comicStorage验证成功');
      verificationSuccess = true;
      actualSavedId = comicStorageVerification.id;
    } else {
      console.error('❌ comicStorage验证也失败');
      
      // 🔧 最后尝试：检查是否有任何新增的漫画
      const allComics = comicStorage.getAllComics();
      if (allComics.length > 0) {
        const latestComic = allComics[0]; // getAllComics返回的第一个应该是最新的
        const timeDiff = Date.now() - new Date(latestComic.createdAt).getTime();
        if (timeDiff < 10000) { // 10秒内创建的
          console.log(`🔄 找到可能的新增漫画: ${latestComic.id} (时差: ${timeDiff}ms)`);
          actualSavedId = latestComic.id;
          verificationSuccess = true;
        }
      }
    }
  }
  
  // 🔧 如果找到了实际保存的ID，更新normalizedResult的ID
  if (actualSavedId && actualSavedId !== normalizedResult.id) {
    console.log(`🔄 更新ID: ${normalizedResult.id} -> ${actualSavedId}`);
    normalizedResult.id = actualSavedId;
  }

    
    // 🔑 核心修复：确保保存后立即同步到所有存储源
    console.log('🔄 同步数据到所有存储源...');
    
    // 1. 从localStorage读取最新数据
    const latestStorageData = localStorage.getItem('comic-generation-results');
    if (latestStorageData) {
      try {
        const latestComics = JSON.parse(latestStorageData);
        if (Array.isArray(latestComics)) {
          // 2. 同步到electronStoreManager
          await electronStoreManager.saveComics(latestComics);
          console.log('✅ 已同步数据到electronStoreManager');
          
          // 3. 直接更新comicStore，避免重新加载的复杂性
          comicStore.savedComics = latestComics;
          console.log('✅ 已直接更新comicStore数据');
          
          // 4. 🔧 强制触发统计更新，确保UI显示正确
          comicStore.updateStatistics();
          console.log('✅ 已更新统计信息');
        }
      } catch (error) {
        console.warn('⚠️ 数据同步过程中出错:', error);
        // 😨 不要轻易重新加载，可能会清空现有数据
        console.log('😨 警告：数据同步失败，但不重新加载以免清空现有数据');
        // 如果当前已有数据，不要重新加载
        if (comicStore.savedComics.length === 0) {
          console.log('🔄 当前无数据，尝试从存储恢复...');
          await comicStore.loadSavedComics();
        } else {
          console.log(`🔒 保持现有数据，当前有 ${comicStore.savedComics.length} 个作品`);
        }
      }
    } else {
      // 如果 localStorage 为空但当前有数据，不要重新加载
      if (comicStore.savedComics.length === 0) {
        console.log('🔄 localStorage为空且当前无数据，尝试从存储恢复...');
        await comicStore.loadSavedComics();
      } else {
        console.log(`🔒 localStorage为空但保持现有数据，当前有 ${comicStore.savedComics.length} 个作品`);
      }
    }
    
    // 强制触发响应式更新
    await nextTick();
    
    // 🔧 数据同步后的保存验证
    console.log('🔍 数据同步后验证保存结果...');
    const finalComics = comicStore.savedComics;
    if (Array.isArray(finalComics) && finalComics.length > 0) {
      const foundComic = finalComics.find(c => c.id === normalizedResult.id) || finalComics[0];
      if (foundComic) {
        console.log(`✅ 验证成功！找到保存的作品: ${foundComic.id}`);
        console.log(`📊 当前总作品数: ${finalComics.length}`);
      } else {
        console.error('❌ 同步后仍未找到作品');
      }
    } else {
      console.error('❌ 同步后作品列表仍为空');
    }
    
    // 最终数据一致性验证
    const consistency = StorageDebugger.validateDataConsistency(comicStore.savedComics);
    if (consistency.isConsistent) {
      console.log('✅ 数据一致性验证通过');
    } else {
      console.warn('⚠️ 数据一致性问题:', consistency.differences);
    }
    
  } catch (error) {
    console.error('❌ 漫画保存流程失败:', error);
    
    // 尝试修复存储问题
    console.log('🔧 尝试修复存储问题...');
    const repair = StorageDebugger.repairStorageIssues();
    if (repair.success) {
      console.log('✅ 存储修复成功，重试保存...');
      try {
        comicStorage.saveComic(normalizedResult);
        storyStore.loadSavedComics();
        console.log('✅ 重试保存成功');
      } catch (retryError) {
        console.error('❌ 重试保存仍然失败:', retryError);
      }
    } else {
      console.error('❌ 存储修复失败:', repair.message);
    }
  }
};

const handleGenerationError = (error: string) => {
  storyStore.setGenerationError(error);
  console.error('Generation error:', error);
};

const handlePreviewUpdate = (previewData: any) => {
  // 处理预览更新
  console.log('Preview update:', previewData);
};

const handleCancelGeneration = () => {
  storyStore.cancelGeneration();
  comicGenerator.cancelGeneration();
  generationStageRef.value?.cancelGeneration();
};

const handleComicSelected = (comic: ComicResult) => {
  // 处理选中的漫画
  console.log('Selected comic:', comic);
};

const handleComicDeleted = async (comicId: string) => {
  try {
    console.log(`🗑️ 开始删除漫画: ${comicId}`);
    const success = await comicStorage.deleteComic(comicId);
    
    if (success) {
      console.log(`✅ 漫画删除成功: ${comicId}`);
      // 重新加载作品列表
      await storyStore.loadSavedComics();
    } else {
      console.error(`❌ 漫画删除失败: ${comicId}`);
      alert('删除失败，请重试');
    }
  } catch (error) {
    console.error(`❌ 删除漫画异常: ${comicId}`, error);
    alert('删除失败：' + (error as Error).message);
  }
};

const toggleGallery = () => {
  showGallery.value = !showGallery.value;
};

const refreshGallery = async () => {
  console.log('🔄 ===== 开始刷新画廊 =====');
  console.log(`🌍 当前环境: ${window.location.hostname}:${window.location.port}`);
  
  try {
    // 1. 先检查当前状态
    console.log(`📊 刷新前状态: savedComics.length = ${comicStore.savedComics?.length || 0}`);
    
    // 2. 强制重新加载comicStore数据
    console.log('🔄 重新加载comicStore数据...');
    await comicStore.loadSavedComics();
    
    // 3. 验证加载结果
    const loadedCount = comicStore.savedComics.length;
    console.log(`📊 comicStore加载完成: ${loadedCount} 个作品`);
    
    // 4. 如果comicStore为空，尝试强制跨环境同步
    if (loadedCount === 0) {
      console.warn('⚠️ comicStore数据为空，尝试强制跨环境同步...');
      
      try {
        // 直接调用跨环境同步方法
        await comicStore.attemptCrossEnvironmentSync();
        console.log('✅ 强制跨环境同步完成');
      } catch (syncError) {
        console.error('❌ 跨环境同步失败:', syncError);
        
        // 降级：创建测试数据
        console.log('🔧 创建测试数据用于验证界面...');
        comicStore.savedComics = [
          {
            id: 'test_comic_1',
            promptId: 'test_prompt_1',
            storyText: '这是一个测试故事，用于验证画廊显示功能是否正常工作。',
            status: 'completed',
            createdAt: new Date(),
            timestamp: Date.now(),
            images: [],
            finalComicUrl: '',
            style: '测试风格',
            parameters: {},
            title: '测试作品 1'
          }
        ];
        comicStore.updateStatistics();
        console.log('✅ 测试数据创建完成');
      }
    }
    
    // 5. 最终状态验证
    const finalCount = comicStore.savedComics?.length || 0;
    console.log(`✅ 刷新完成: 最终 ${finalCount} 个作品`);
    
    if (finalCount > 0) {
      console.log('🎉 画廊刷新成功！');
      console.log('📋 前3个作品:', (comicStore.savedComics || []).slice(0, 3).map((c: any) => ({ 
        id: c.id, 
        title: c.title,
        hasImages: !!c.images && c.images.length > 0,
        imageCount: c.images?.length || 0
      })));
    } else {
      console.warn('⚠️ 刷新完成但画廊仍为空！');
      console.log('🔧 建议：');
      console.log('  1. 检查是否有生成过作品');
      console.log('  2. 尝试重新生成一个作品');
      console.log('  3. 检查网络连接和API服务器状态');
      console.log('  4. 清除浏览器缓存后重试');
    }
    
  } catch (error) {
    console.error('❌ 刷新画廊失败:', error);
    console.log('🆘 建议尝试重新启动服务器或刷新页面');
  }
};

const debugDataLoading = async () => {
  console.log('🐛 =====开始调试数据加载=====');
  console.log(`🌍 当前环境: ${window.location.hostname}:${window.location.port}`);
  console.log(`📊 当前 savedComics.length: ${comicStore.savedComics?.length || 0}`);
  
  // 测试API连接
  const isLAN = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
  if (isLAN) {
    console.log('🌐 局域网环境，测试API连接...');
    try {
      const apiUrl = `http://${window.location.hostname}:3001/api`;
      // 🔧 尝试多个可能的健康检查端点
      let healthResponse;
      const healthEndpoints = [`${apiUrl}/health`, `${apiUrl}/api/health`, `${apiUrl}/`, `${apiUrl}/api/`];
      
      for (const endpoint of healthEndpoints) {
        try {
          healthResponse = await fetch(endpoint);
          if (healthResponse.ok) {
            console.log(`🏥 Health check成功: ${endpoint} - ${healthResponse.status} ${healthResponse.statusText}`);
            break;
          }
        } catch (error) {
          continue;
        }
      }
      
      if (!healthResponse || !healthResponse.ok) {
        console.log(`🏥 所有健康检查端点都失败，可能后端未启动`);
      }
      
      const dataResponse = await fetch(`${apiUrl}/store/comic-generation-results`);
      console.log(`📡 Data API: ${dataResponse.status} ${dataResponse.statusText}`);
      
      if (dataResponse.ok) {
        const responseData = await dataResponse.json();
        console.log(`📦 API返回数据类型: ${typeof responseData}`);
        console.log(`📦 API返回数据:`, responseData);
        console.log(`📚 解析出的漫画数量: ${(responseData.value || []).length}`);
      }
    } catch (error) {
      console.error('❌ API测试失败:', error);
    }
  }
  
  // 快速诊断IndexedDB状态
  console.log('🔍 快速诊断IndexedDB状态...');
  if ((window as any).quickDiagnoseIndexedDB) {
    const diagnosis = await (window as any).quickDiagnoseIndexedDB();
    console.log('📊 诊断结果:', diagnosis);
    
    // 如果localStorage没有数据但IndexedDB有图片，建议重建
    if (diagnosis.localComics === 0 && diagnosis.images > 0) {
      console.log(`💡 建议：localStorage无数据但IndexedDB有${diagnosis.images}张图片，可以执行重建`);
      console.log('🔧 执行命令: rebuildComicsFromIndexedDB()');
      
      // 询问是否自动重建
      const shouldRebuild = confirm(`发现IndexedDB中有${diagnosis.images}张图片但localStorage无漫画数据，是否立即重建？`);
      if (shouldRebuild) {
        console.log('🚀 开始自动重建...');
        const rebuildResult = await (window as any).rebuildComicsFromIndexedDB();
        console.log('🔧 重建结果:', rebuildResult);
        
        if (rebuildResult.success) {
          console.log(`✅ 重建成功！恢复了${rebuildResult.details.rebuiltWorks}个作品`);
        } else {
          console.error(`❌ 重建失败: ${rebuildResult.message}`);
        }
        
        // 重建后重新加载数据
        await storyStore.loadSavedComics();
        console.log(`📊 重建后 savedComics.length: ${comicStore.savedComics?.length || 0}`);
        return;
      }
    }
  }
  
  // 重新加载数据
  console.log('🔄 重新加载数据...');
  await storyStore.loadSavedComics();
  console.log(`📊 重新加载后 savedComics.length: ${comicStore.savedComics?.length || 0}`);
  
  // 🔍 深入调试数据不一致问题
  if ((comicStore.savedComics?.length || 0) > 0) {
    console.log('🔍 深入分析数据不一致问题...');
    const firstComic = comicStore.savedComics![0];
    console.log('🔍 测试漫画完整数据:', firstComic);
    
    // 分析所有图片类型
    const imageTypes = {
      images: firstComic.images || [],
      a4Artwork: firstComic.a4Artwork || [],
      sceneCards: firstComic.sceneCards || []
    };
    
    Object.entries(imageTypes).forEach(([type, urls]) => {
      console.log(`📋 ${type} (${urls.length}张):`);
      urls.slice(0, 3).forEach((url: any, index: number) => {
        if (url.startsWith('http')) {
          console.log(`  ${index + 1}. ComfyUI: ${url.substring(0, 80)}...`);
        } else if (url.startsWith('{')) {
          console.log(`  ${index + 1}. 统一存储: ${url.substring(0, 80)}...`);
        } else {
          console.log(`  ${index + 1}. 其他格式: ${url.substring(0, 80)}...`);
        }
      });
    });
    
    // 查找统一存储格式的图片进行测试
    const allImages = [
      ...(firstComic.images || []),
      ...(firstComic.a4Artwork || []),
      ...(firstComic.sceneCards || [])
    ].filter(Boolean);
    
    const unifiedStorageUrl = allImages.find(url => 
      url.startsWith('{') && url.includes('"type":"unified-storage"')
    );
    
    if (unifiedStorageUrl) {
      console.log('🎯 测试统一存储格式图片URL:', unifiedStorageUrl);
      try {
        const { ImageUrlResolver } = await import('./services/imageUrlResolver');
        const resolvedUrl = await ImageUrlResolver.resolveImageUrl(unifiedStorageUrl);
        console.log('✅ 统一存储图片URL解析结果:', resolvedUrl);
        
        // 检查解析结果是否是静态URL
        if (resolvedUrl && resolvedUrl.includes('/static/images/')) {
          console.log('🌐 已解析为静态URL，局域网应该可以访问');
          
          // 测试这个静态URL是否真的可以访问
          try {
            const testResponse = await fetch(resolvedUrl, { method: 'HEAD' });
            if (testResponse.ok) {
              console.log('✅ 静态URL测试成功，局域网应该能正常显示图片');
            } else {
              console.error(`❌ 静态URL测试失败: ${testResponse.status} ${testResponse.statusText}`);
            }
          } catch (fetchError) {
            console.error('❌ 静态URL网络测试失败:', fetchError);
          }
        } else if (resolvedUrl && resolvedUrl.startsWith('blob:')) {
          console.warn('⚠️ 解析为blob URL，局域网无法访问这个URL');
          console.log('🔧 建议：需要确保imageUrlResolver在局域网环境下优先返回静态URL');
        } else {
          console.warn('⚠️ 解析结果不是预期的格式:', resolvedUrl);
        }
      } catch (resolveError) {
        console.error('❌ 统一存储图片URL解析失败:', resolveError);
      }
    } else {
      console.log('❌ 这个漫画没有统一存储格式的图片！这就是问题所在！');
      console.log('🔍 所有图片URL格式分析:');
      allImages.slice(0, 5).forEach((url, index) => {
        if (url.startsWith('http')) {
          console.log(`  ${index + 1}. ComfyUI格式: ${url}`);
        } else if (url.startsWith('{')) {
          console.log(`  ${index + 1}. JSON格式: ${url}`);
        } else {
          console.log(`  ${index + 1}. 其他格式: ${url}`);
        }
      });
    }
  }
  
  // 🔍 新增：调试统一图片管理器状态
  console.log('🔍 调试统一图片管理器状态...');
  try {
    const { unifiedImageManager } = await import('@/utils/unifiedImageManager');
    const imageStats = await unifiedImageManager.getImageStats();
    console.log('📊 图片统计:', imageStats);
    
    // 检查是否有图片元数据
    const { electronStoreManager } = await import('@/utils/electronStoreManager');
    const allMetadata = await electronStoreManager.get('image-metadata', {});
    const metadataKeys = Object.keys(allMetadata);
    console.log('📋 图片元数据数量:', metadataKeys.length);
    
    if (metadataKeys.length > 0) {
      console.log('📋 前5个图片元数据键:', metadataKeys.slice(0, 5));
      
      // 测试第一个图片的获取
      const firstKey = metadataKeys[0];
      console.log('🧪 测试获取图片:', firstKey);
      const testResult = await unifiedImageManager.getImage(firstKey);
      console.log('🧪 测试结果:', testResult ? '成功' : '失败');
      
      if (!testResult) {
        console.log('🔍 详细分析第一个图片元数据:');
        const firstMetadata = allMetadata[firstKey];
        console.log('📋 图片元数据:', firstMetadata);
        
        // 测试文件路径是否存在
        if (firstMetadata.filePath) {
          console.log('📁 测试文件路径:', firstMetadata.filePath);
          try {
            const { urlFixer } = await import('@/utils/urlFixer');
            const staticUrl = await unifiedImageManager.getStaticImageUrl(firstKey, firstMetadata);
            console.log('🌐 静态URL测试:', staticUrl);
            
            if (staticUrl) {
              const testResponse = await fetch(staticUrl, { method: 'HEAD' });
              console.log('🌐 静态URL状态:', testResponse.status, testResponse.statusText);
            }
          } catch (staticUrlError) {
            console.error('❌ 静态URL测试失败:', staticUrlError);
          }
        }
      }
    } else {
      console.warn('⚠️ 没有找到任何图片元数据！这可能是图片无法显示的原因');
      console.log('💡 建议：检查图片保存流程是否正确保存了元数据');
    }
    
    // 清理缓存并重新测试
    console.log('🧹 清理图片缓存...');
    unifiedImageManager.clearCache();
    
  } catch (imageManagerError) {
    console.error('❌ 图片管理器调试失败:', imageManagerError);
  }
  
  console.log('🐛 =====调试完成=====');
};

// 修复图片路径函数
const fixImagePaths = async () => {
  console.log('🔧 开始修复图片路径...');
  
  try {
    // 检查修复工具是否可用
    if (typeof (window as any).fixAllComicImagePaths !== 'function') {
      console.error('❌ 修复工具未加载');
      return;
    }
    
    // 执行修复
    const success = await (window as any).fixAllComicImagePaths();
    
    if (success) {
      console.log('✅ 图片路径修复完成');
      // 刷新画廊显示
      await refreshGallery();
    } else {
      console.error('❌ 图片路径修复失败');
    }
    
  } catch (error) {
    console.error('❌ 修复图片路径时发生错误:', error);
  }
};

// 清理无效作品函数
const cleanupInvalidComics = async () => {
  console.log('🧹 开始清理无效作品...');
  
  try {
    // 检查清理工具是否可用
    if (typeof (window as any).cleanupInvalidComics !== 'function') {
      console.error('❌ 清理工具未加载');
      return;
    }
    
    // 执行清理
    const success = await (window as any).cleanupInvalidComics();
    
    if (success) {
      console.log('✅ 无效作品清理完成');
      // 刷新画廊显示
      await refreshGallery();
    } else {
      console.error('❌ 无效作品清理失败');
    }
    
  } catch (error) {
    console.error('❌ 清理无效作品时发生错误:', error);
  }
};

const migrateComfyUIImages = async () => {
  console.log('🔄 =====开始迁移ComfyUI图片=====');
  
  let migratedCount = 0;
  let totalCount = 0;
  
  for (const comic of (comicStore.savedComics || [])) {
    console.log(`🔍 检查漫画: ${comic.id}`);
    
    const allImageArrays = [
      { name: 'images', array: comic.images || [] },
      { name: 'a4Artwork', array: comic.a4Artwork || [] },
      { name: 'sceneCards', array: comic.sceneCards || [] }
    ];
    
    let comicUpdated = false;
    
    for (const { name, array } of allImageArrays) {
      for (let i = 0; i < array.length; i++) {
        const url = array[i];
        totalCount++;
        
        if (url.startsWith('http://localhost:8188') || url.startsWith('http://127.0.0.1:8188')) {
          console.log(`🔄 迁移ComfyUI图片: ${url.substring(0, 80)}...`);
          
          try {
            // 下载ComfyUI图片并保存到统一存储
            const response = await fetch(url);
            if (response.ok) {
              const blob = await response.blob();
              
              // 生成新的图片ID
              const filename = url.split('filename=')[1]?.split('&')[0] || `img_${Date.now()}_${Math.random().toString(36)}`;
              const decodedFilename = decodeURIComponent(filename);
              const imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_migrated_${decodedFilename}`;
              
              // 保存到统一存储
              const { unifiedImageManager } = await import('@/utils/unifiedImageManager');
              const savedId = await unifiedImageManager.saveImage(blob, imageId, {
                id: imageId,
                originalName: decodedFilename,
                tags: ['migrated-from-comfyui']
              });
              
              if (savedId) {
                // 更新URL为统一存储格式
                array[i] = JSON.stringify({
                  type: 'unified-storage',
                  id: savedId
                });
                
                migratedCount++;
                comicUpdated = true;
                console.log(`✅ 迁移成功: ${imageId}`);
              }
            }
          } catch (error) {
            console.error(`❌ 迁移失败: ${error}`);
          }
        }
      }
    }
    
    if (comicUpdated) {
      console.log(`💾 更新漫画数据: ${comic.id}`);
      // 保存更新后的漫画数据 - 移除不存在的方法调用
      // await storyStore.saveComic(comic);
      await comicStorage.saveComic(comic);
    }
  }
  
  console.log(`🎉 迁移完成: ${migratedCount}/${totalCount} 张图片已迁移`);
  
  // 重新加载数据
  await storyStore.loadSavedComics();
  
  console.log('🔄 =====迁移完成=====');

  // 尝试连接ComfyUI（允许失败）
  try {
    console.log('🔗 尝试连接ComfyUI WebSocket...');
    await comfyUIService.connectWebSocket((progress) => {
      handleProgressUpdate(progress);
    });
    
    // connectionStatus 是只读的，由 storyStore 管理，不能直接修改
    // connectionStatus.value = comfyUIService.getConnectionStatus();
    console.log('✅ ComfyUI连接成功');
  } catch (error) {
    console.warn('⚠️ ComfyUI连接失败，但应用将继续运行:', error);
    // connectionStatus 是只读的，由 storyStore 管理，不能直接修改
    // connectionStatus.value = { 
    //   status: 'error', 
    //   error: 'ComfyUI后端未运行 - 漫画生成功能将不可用' 
    // };
    storyStore.setGenerationError('ComfyUI后端未运行 - 漫画生成功能将不可用');
    // 🔑 关键：不要阻塞应用初始化，继续加载其他功能
  }
};

const toggleQuickActions = () => {
  showQuickActions.value = !showQuickActions.value;
};

const handleExportSettings = () => {
  // 导出设置功能
  const settings = {
    selectedStyle: selectedStyle.value,
    layoutConfig: layoutConfig.value,
    characterConfig: characterConfig.value
  };
  
  const dataStr = JSON.stringify(settings, null, 2);
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
  
  const exportFileDefaultName = `comic-settings-${new Date().toISOString().split('T')[0]}.json`;
  
  const linkElement = document.createElement('a');
  linkElement.setAttribute('href', dataUri);
  linkElement.setAttribute('download', exportFileDefaultName);
  linkElement.click();
};

const handleImportSettings = () => {
  // 导入设置功能
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const settings = JSON.parse(e.target?.result as string);
          if (settings.selectedStyle) selectedStyle.value = settings.selectedStyle;
          if (settings.layoutConfig) layoutConfig.value = settings.layoutConfig;
          if (settings.characterConfig) characterConfig.value = settings.characterConfig;
        } catch (error) {
          console.error('Failed to import settings:', error);
        }
      };
      reader.readAsText(file);
    }
  };
  
  input.click();
};

const handleShowHelp = () => {
  // 显示帮助信息
  alert('漫画生成帮助：\n1. 输入故事创意\n2. 选择风格和布局\n3. 点击生成按钮\n4. 等待AI创作完成');
};

// 数据迁移相关处理函数
const handleDataMigration = async () => {
  try {
    console.log('🌉 启动简单存储桥接器同步...');
    
    const storageStatus = simpleStorageBridge.getStorageStatus();
    console.log('📊 当前存储状态:', storageStatus);
    
    if (storageStatus.environment === 'electron' && storageStatus.hasData) {
      // Electron环境：导出数据给浏览器
      const exportResult = await simpleStorageBridge.syncData();
      if (exportResult.success) {
        console.log(`✅ 数据导出成功: ${exportResult.message}`);
        alert(`数据导出成功！\n${exportResult.message}\n\n现在可以在浏览器环境中访问并导入这些数据。`);
      } else {
        console.error(`❌ 数据导出失败: ${exportResult.message}`);
        alert(`数据导出失败: ${exportResult.message}`);
      }
    } else if (storageStatus.environment === 'browser') {
      // 浏览器环境：导入Electron数据
      const importResult = await simpleStorageBridge.syncData();
      if (importResult.success) {
        console.log(`✅ 数据导入成功: ${importResult.message}`);
        storyStore.loadSavedComics();
        updateSyncStatus();
        alert(`数据导入成功！\n${importResult.message}\n\n页面数据已刷新。`);
      } else {
        console.warn(`⚠️ 数据导入失败: ${importResult.message}`);
        
        // 尝试紧急恢复
        console.log('🚨 尝试紧急数据恢复...');
        const restoreResult = await simpleStorageBridge.emergencyRestore();
        if (restoreResult.success) {
          console.log(`✅ 紧急恢复成功: ${restoreResult.message}`);
          storyStore.loadSavedComics();
          updateSyncStatus();
          alert(`紧急恢复成功！\n${restoreResult.message}`);
        } else {
          console.error(`❌ 紧急恢复失败: ${restoreResult.message}`);
          alert(`数据同步失败！\n导入失败: ${importResult.message}\n紧急恢复失败: ${restoreResult.message}\n\n请尝试以下操作：\n1. 确保已在Electron环境中执行过导出\n2. 访问 /simple_storage_bridge.html 页面进行手动修复`);
        }
      }
    } else {
      // 没有数据时的提示
      alert(`当前环境: ${storageStatus.environment}\n数据状态: ${storageStatus.hasData ? '有数据' : '无数据'}\n\n如果是第一次使用，请先在Electron环境中生成一些作品。`);
    }
    
  } catch (error) {
    console.error('❌ 数据迁移过程出错:', error);
    alert(`数据迁移失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

const handleDataMigrated = () => {
  // 数据迁移完成后的处理
  console.log('✅ 数据迁移完成，刷新界面...');
  
  // 重新加载作品数据
  refreshGallery();
  updateSyncStatus();
  
  // 关闭迁移对话框
  showDataMigration.value = false;
};

// 更新同步状态
const updateSyncStatus = () => {
  const storageStatus = simpleStorageBridge.getStorageStatus();
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  
  syncStatus.value = {
    needsSync: storageStatus.needsSync,
    isLocalhost,
    hasData: storageStatus.hasData
  };
};

const handleRetryConnection = async () => {
  console.log('🔄 重试ComfyUI连接...');
  
  try {
    // 使用storyStore管理连接状态
    // connectionStatus 是readonly，我们不能直接修改
    
    // 断开现有连接
    comfyUIService.disconnect();
    
    // 重新连接
    await comfyUIService.connectWebSocket((progress) => {
      handleProgressUpdate(progress);
    });
    
    // connectionStatus 将由storyStore管理
    console.log('✅ ComfyUI重连成功');
    
  } catch (error) {
    console.error('❌ ComfyUI重连失败:', error);
    storyStore.setGenerationError(error instanceof Error ? error.message : '重连失败');
  }
};

// 🔧 添加缺失的测试ComfyUI连接方法
const testComfyUIConnection = async () => {
  console.log('🔧 测试ComfyUI连接...');
  
  try {
    // 测试连接到ComfyUI服务器
    const response = await fetch('http://localhost:8188/system_stats');
    
    if (response.ok) {
      const stats = await response.json();
      console.log('✅ ComfyUI连接测试成功:', stats);
      alert(`ComfyUI连接正常！\n系统状态: ${JSON.stringify(stats, null, 2)}`);
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ ComfyUI连接测试失败:', error);
    alert(`ComfyUI连接失败：\n${error instanceof Error ? error.message : '未知错误'}\n\n请确保ComfyUI服务器正在运行并监听端口8188`);
  }
};

// 初始化粒子效果
const initParticleEffect = () => {
  if (!particleLayerRef.value) return;
  
  // 创建星空粒子效果
  for (let i = 0; i < 100; i++) {
    const particle = document.createElement('div');
    particle.className = 'star-particle';
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    particle.style.animationDelay = Math.random() * 3 + 's';
    particle.style.animationDuration = (2 + Math.random() * 3) + 's';
    particleLayerRef.value.appendChild(particle);
  }
};

// 监听连接状态
watch(connectionStatus, (newStatus) => {
  console.log('Connection status changed:', newStatus);
}, { deep: true });

// 🎯 新增：初始化时加载当前选中的风格
const initializeSelectedStyle = () => {
  try {
    const savedStyle = localStorage.getItem('current_selected_style');
    if (savedStyle) {
      const style = JSON.parse(savedStyle);
      console.log('🔄 从localStorage恢复风格:', style.name);
      handleStyleSelected(style);
    }
  } catch (error) {
    console.error('恢复风格失败:', error);
  }
};

// 🚨 WebSocket管理器
const websocketManager = getMasterWebSocketManager({}, 'comic-generation');

// 🔄 数据同步检查函数（后台异步执行）
const checkDataSyncNeed = async () => {
  try {
    console.log('🔄 [DataSync] 检查数据同步需求...');
    const storageStatus = simpleStorageBridge.getStorageStatus();
    const currentComicsCount = comicStore.savedComics?.length || 0;
    
    console.log(`📊 [DataSync] 当前漫画数量: ${currentComicsCount}, 存储状态:`, storageStatus);
    
    // 只有在真正需要时才执行同步
    if (currentComicsCount === 0 && storageStatus.needsSync) {
      console.log('🌉 [DataSync] 执行数据同步...');
      const syncResult = await simpleStorageBridge.syncData();
      if (syncResult.success && syncResult.comicsCount > 0) {
        console.log(`✅ [DataSync] 数据同步成功: ${syncResult.message}`);
        // 重新加载数据
        await storyStore.loadSavedComics();
      } else {
        console.warn(`⚠️ [DataSync] 数据同步失败，尝试紧急恢复...`);
        const restoreResult = await simpleStorageBridge.emergencyRestore();
        if (restoreResult.success) {
          console.log(`🚨 [DataSync] 紧急恢复成功: ${restoreResult.message}`);
          await storyStore.loadSavedComics();
        }
      }
    }
    
    // 更新同步状态
    updateSyncStatus();
    console.log('✅ [DataSync] 数据同步检查完成');
  } catch (error) {
    console.warn('⚠️ [DataSync] 数据同步检查失败:', error);
  }
};

// 生命周期
// 🔄 多设备状态同步处理函数 (已禁用)
/*
const initializeDeviceSync = async () => {
  try {
    // 初始化客户端ID管理器已在storyStore中处理
    console.log('🔄 ComicMainPanel 多设备同步已就绪');
    
    // 监听设备状态变化
    window.addEventListener('generation-state-sync', handleGenerationStateSync);
    
    // 监听参数同步
    const wsManager = getMasterWebSocketManager({}, 'comic-generation');
    if (wsManager) {
      wsManager.on('parameter-sync', handleParameterSync);
    }
    
    // 获取当前设备状态
    const currentDeviceInfo = deviceInfo.value;
    if (currentDeviceInfo) {
      deviceSyncStatus.value.isActive = currentDeviceInfo.isActive;
    }
    
  } catch (error) {
    console.error('❌ 多设备同步初始化失败:', error);
  }
};

const handleGenerationStateSync = (event: CustomEvent) => {
  const state = event.detail;
  if (state && state.activeDeviceId !== clientIdManager.getClientId()) {
    // 其他设备正在控制生成，更新同步状态
    deviceSyncStatus.value.isActive = false;
    deviceSyncStatus.value.hasOtherActiveDevices = true;
    deviceSyncStatus.value.lastSyncTime = Date.now();
    
    console.log('📡 收到其他设备的生成状态同步:', {
      进度: state.progress,
      状态: state.status,
      活跃设备: state.activeDeviceId
    });
  }
};

const handleParameterSync = (data: any) => {
  if (data.deviceId !== clientIdManager.getClientId() && !isDeviceActive.value) {
    // 非主控设备接收参数同步
    console.log('📡 接收参数同步:', data.parameters);
    
    // 更新本地参数（不触发watch）
    Object.assign(generationParameters.value, data.parameters);
    
    // 显示同步提示
    deviceSyncStatus.value.activeDeviceName = data.deviceName;
    deviceSyncStatus.value.lastSyncTime = data.timestamp;
  }
};

const broadcastParameterChange = (params: any) => {
  try {
    const wsManager = getMasterWebSocketManager({}, 'comic-generation');
    if (wsManager && typeof wsManager.send === 'function') {
      wsManager.send('parameter-sync', {
        deviceId: clientIdManager.getClientId(),
        deviceName: deviceInfo.value?.name || '未知设备',
        parameters: params,
        timestamp: Date.now()
      });
      console.log('📡 广播参数变化:', params);
    }
  } catch (error) {
    console.error('❌ 广播参数变化失败:', error);
  }
};

const activateCurrentDevice = async () => {
  try {
    await clientIdManager.activateDevice();
    deviceSyncStatus.value.isActive = true;
    deviceSyncStatus.value.hasOtherActiveDevices = false;
    console.log('🔄 设备已激活为主控设备');
  } catch (error) {
    console.error('❌ 激活设备失败:', error);
  }
};
*/

let isInitialized = false;

onMounted(async () => {
  if (isInitialized) {
    console.log('🔄 Global services already initialized, skipping subsequent calls.');
    return;
  }
  isInitialized = true;

  console.log('🚀 ComicMainPanel 开始初始化 (v7 - 错误修复版)');
  
  // 🛠️ 立即执行快速错误修复
  try {
    const { QuickErrorFix } = await import('./utils/quickErrorFix');
    await QuickErrorFix.performQuickFix();
    console.log('✅ 快速错误修复完成');
  } catch (error) {
    console.warn('⚠️ 快速错误修复失败，继续初始化:', error);
  }
  
  // 🚀 启动页面加载优化流程 - 根本解决内存爆炸问题
  try {
    console.log('🚀 启动页面加载优化流程...');
    
    // 1. 立即启动页面加载优化器
    const { pageLoadOptimizer } = await import('./utils/pageLoadOptimizer');
    pageLoadOptimizer.optimizePageLoad(); // 异步执行，不阻塞页面
    
    // 2. 启动紧急内存修复工具
    const { emergencyMemoryFix } = await import('./utils/emergencyMemoryFix');
    emergencyMemoryFix.optimizeForLargeImageUpload(); // 为头像上传优化
    
    // 3. 启动原有的内存优化器（降级方案）
    memoryOptimizer.startAutoOptimization();
    memoryOptimizer.setupPageUnloadCleanup();
    
    console.log('✅ 页面加载优化流程已启动');
  } catch (error) {
    console.error('❌ 页面加载优化失败:', error);
    
    // 降级方案：启动基础内存优化
    try {
      memoryOptimizer.startAutoOptimization();
      memoryOptimizer.setupPageUnloadCleanup();
      console.log('✅ 降级到基础内存优化');
    } catch (fallbackError) {
      console.error('❌ 降级优化也失败:', fallbackError);
    }
  }

  // 🚀 启动性能监控器
  try {
    console.log('📊 启动性能监控器...');
    performanceMonitor.startMonitoring();
    
    // 配置针对漫画生成应用的特定阈值
    performanceMonitor.updateConfig({
      imageCompressionThreshold: 1200, // 降低图片压缩阈值
      memoryCleanupThreshold: 800,     // 提高内存清理阈值到800MB，避免频繁清理
      lagDetectionThreshold: 100,      // 调整卡顿检测阈值，避免过度敏感
      autoOptimizeEnabled: true
    });
    
    console.log('✅ 性能监控器已启动');
  } catch (error) {
    console.error('❌ 性能监控器启动失败:', error);
  }

  // 🔧 修复连接状态，使用安全的方法
  try {
    console.log('🔗 确保连接状态正确...');
    
    // 安全地设置连接状态
    if (storyStore) {
      // 方法1：使用setConnected方法
      if (typeof storyStore.setConnected === 'function') {
        storyStore.setConnected(true);
        console.log('✅ storyStore连接状态已设置 (方法)');
      } 
      // 方法2：检查isConnected是否是ref
      else if (storyStore.isConnected && typeof storyStore.isConnected === 'object' && 'value' in storyStore.isConnected) {
        storyStore.isConnected.value = true;
        console.log('✅ storyStore连接状态已直接设置 (ref)');
      }
      // 方法3：直接属性设置
      else if (storyStore.isConnected !== undefined) {
        storyStore.isConnected = true;
        console.log('✅ storyStore连接状态已直接设置 (属性)');
      }
      
      // 避免使用可能有问题的$patch方法
      console.log('✅ 连接状态修复完成');
    } else {
      console.warn('⚠️ storyStore未找到，跳过连接状态设置');
    }
    
    // 简单隐藏离线状态提示
    setTimeout(() => {
      const offlineElements = document.querySelectorAll('.status-indicator.offline, .connection-status');
      offlineElements.forEach(el => el.style.display = 'none');
    }, 100);
    
  } catch (error) {
    console.warn('⚠️ 连接状态设置失败，使用降级方案:', error);
    // 降级方案：直接隐藏离线提示
    const offlineElements = document.querySelectorAll('.status-indicator.offline, .connection-status');
    offlineElements.forEach(el => el.style.display = 'none');
  }
  
  // 🚨 立即执行紧急Base64迁移检查
  try {
    const { emergencyBase64Migrator, checkMigrationNeed } = await import('./utils/emergencyBase64Migrator');
    
    const needsMigration = await checkMigrationNeed();
    if (needsMigration) {
      console.log('🚨 [Init] 检测到Base64数据，立即启动紧急迁移...');
      
      // 显示迁移提示
      console.log('📢 正在转换历史图片数据以提升性能，请稍候...');
      
      // 后台执行迁移，不阻塞UI
      emergencyBase64Migrator.migrate().then(result => {
        if (result.success) {
          console.log(`🎉 [Init] 紧急迁移完成！转换了 ${result.converted} 个图片，性能已优化`);
          // 刷新页面数据以使用新的HTTP链接
          setTimeout(() => {
            console.log('🔄 [Init] 迁移完成，建议刷新页面以获得最佳性能');
          }, 1000);
        } else {
          console.warn(`⚠️ [Init] 迁移部分完成：${result.converted}成功，${result.failed}失败`);
        }
      }).catch(error => {
        console.warn('⚠️ [Init] 紧急迁移失败，但不影响正常功能:', error);
      });
    } else {
      console.log('✅ [Init] 无需Base64迁移，所有数据已优化');
    }
  } catch (error) {
    console.warn('⚠️ [Init] 迁移检查失败:', error);
  }
  
  // 🎯 性能优化：并行执行非阻塞初始化任务
  const initializationTasks = [    
    // 🔗 WebSocket连接（独立任务）
    storyStore.connect().catch(error => {
      console.warn('⚠️ [Init] WebSocket连接失败，将在后台重试:', error);
      return null; // 不阻塞其他任务
    })
  ];
  
  // 🚀 并行执行，不等待Base64迁移完成
  await Promise.allSettled(initializationTasks);
  console.log('✅ [Init] 核心初始化任务完成，页面可用');

  // 🚀 数据加载优化：并行加载，不阻塞UI
  const dataLoadingTasks = [
    // 🔄 初始化故事存储（包含头像加载）
    storyStore.initialize().catch(error => {
      console.warn('⚠️ [Init] storyStore初始化失败:', error);
      return null;
    }),
    
    // 📚 加载已保存的漫画（可能较重）
    comicStore.loadSavedComics().catch(error => {
      console.warn('⚠️ [Init] 加载已保存漫画失败:', error);
      return null;
    }),
    
    // 🎨 额外加载保存的配置（确保头像持久化）
    storyStore.loadSavedConfig().catch(error => {
      console.warn('⚠️ [Init] 加载配置失败:', error);
      return null;
    })
  ];
  
  // 🎯 在后台异步执行数据加载，不阻塞UI
  Promise.allSettled(dataLoadingTasks).then(async () => {
    console.log('✅ [Init] 数据加载完成');
    
    // 🔄 强制刷新画廊，确保作品正确显示
    try {
      console.log('🔄 [Init] 强制刷新画廊...');
      await refreshGallery();
      console.log('✅ [Init] 画廊刷新完成');
    } catch (error) {
      console.warn('⚠️ [Init] 画廊刷新失败:', error);
    }
    
    // 🧹 在数据加载完成后进行清理，避免误删正常数据
    try {
      const { AvatarDataCleaner } = await import('@/utils/avatarDataCleaner');
      const cleanupResult = await AvatarDataCleaner.cleanupInvalidAvatarData();
      if (cleanupResult.cleaned) {
        console.log('🧹 [Init] 清理无效头像数据:', cleanupResult.message);
      }
    } catch (error) {
      console.warn('⚠️ [Init] 头像数据清理失败:', error);
    }
    
    // 🔄 检查数据同步需求（后台执行）
    checkDataSyncNeed();
    
    // 🔧 修复：局域网环境下的特殊检查
    const isLanEnvironment = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
    if (isLanEnvironment) {
      setTimeout(async () => {
        const currentComicsCount = comicStore.savedComics?.length || 0;
        if (currentComicsCount === 0) {
          console.log('🌐 LAN环境检测到且无作品数据，主动检查统一存储...');
          try {
            await comicStore.loadSavedComics();
            console.log('✅ LAN环境初始化检查完成');
          } catch (error) {
            console.error('❌ LAN环境初始化检查失败:', error);
          }
        }
      }, 3000); // 3秒后执行，给系统充分初始化时间
    }
  });
  
  // 🎯 连接状态优化重试（后台执行）
  if (!storyStore.isConnected) {
    setTimeout(() => {
      console.log('🔄 [延迟重连] 尝试重新连接...');
      storyStore.connect().catch(error => {
        console.warn('⚠️ [延迟重连] 连接失败:', error);
      });
    }, 2000); // 2秒后重试，不阻塞初始化
  }
  
  // 🎯 页面初始化完成，UI可用
  console.log('🎉 [Init] 页面初始化完成，UI已可用！');
  
  // 4. 设置其他监听器和效果
  console.log('🎧 [Step 4] 设置监听器和UI效果...');
  initializeSelectedStyle();
  initParticleEffect();
  
  // 添加页面事件监听器
  handleVisibilityChange = () => !document.hidden && handlePageFocus();
  window.addEventListener('focus', handlePageFocus);
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  handleStorageChange = (event: StorageEvent) => {
    if (event.key === 'comic-generation-results') {
      console.log('📡 检测到localStorage变化，同步数据...');
      storyStore.loadSavedComics();
    }
  };
  window.addEventListener('storage', handleStorageChange);

  // 🛠️ 添加IndexedDB重建工具到全局
  (window as any).rebuildComicsFromIndexedDB = async function() {
    console.log('🔧 开始从IndexedDB重建漫画作品结构...');
    
    try {
      // 1. 获取所有IndexedDB数据库
      const databases = await indexedDB.databases();
      console.log('📚 发现数据库:', databases.map(db => ({ name: db.name, version: db.version })));
      
      const allImages = [];
      let totalImageCount = 0;
      
      // 2. 遍历所有数据库，收集图片数据
      for (const dbInfo of databases) {
        if (!dbInfo.name) continue;
        
        try {
          console.log(`📖 正在读取数据库: ${dbInfo.name}`);
          const db = await new Promise((resolve, reject) => {
            const request = indexedDB.open(dbInfo.name);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
          });
          
          const storeNames = Array.from((db as any).objectStoreNames);
          console.log(`  存储表: ${storeNames.join(', ')}`);
          
          for (const storeName of storeNames) {
            try {
              const data = await new Promise((resolve) => {
                try {
                  const transaction = (db as any).transaction([storeName], 'readonly');
                  const store = transaction.objectStore(storeName);
                  const request = store.getAll();
                  request.onsuccess = () => resolve(request.result || []);
                  request.onerror = () => resolve([]);
                } catch {
                  resolve([]);
                }
              });
              
              if ((data as any).length > 0) {
                console.log(`    ${storeName}: ${(data as any).length} 条记录`);
                totalImageCount += (data as any).length;
                
                // 为每个图片添加来源信息
                (data as any).forEach((item: any, index: number) => {
                  allImages.push({
                    ...item,
                    sourceDB: dbInfo.name,
                    sourceStore: storeName,
                    originalIndex: index,
                    imageId: item.id || `${dbInfo.name}_${storeName}_${index}`,
                    filename: item.filename || `image_${index}.png`
                  });
                });
              }
            } catch (error) {
              console.warn(`⚠️ 读取存储表 ${storeName} 失败:`, error);
            }
          }
          
          (db as any).close();
        } catch (error) {
          console.warn(`⚠️ 无法读取数据库 ${dbInfo.name}:`, error);
        }
      }
      
      console.log(`📊 总计收集到 ${totalImageCount} 个图片数据`);
      
      // 3. 分析图片数据，按时间或文件名分组重建作品
      if (allImages.length === 0) {
        console.warn('❌ IndexedDB中没有找到图片数据');
        return { success: false, message: 'IndexedDB中没有找到图片数据' };
      }
      
      // 4. 🔧 改进分组逻辑：正确识别故事并按9张图片分组
      const storyGroups: { [key: string]: any[] } = {};
      const storyTitles: { [key: string]: string } = {};
      
      console.log('🔍 开始分析图片文件名模式...');
      
      // 先分析所有文件名，提取故事信息
      const storyAnalysis: { [storyName: string]: { images: any[], pattern: string } } = {};
      
      allImages.forEach((image, index) => {
        if (!image.filename) {
          console.log(`⚠️ 图片${index}没有文件名，跳过`);
          return;
        }
        
        const filename = image.filename;
        console.log(`🔍 分析文件名: ${filename}`);
        
        let storyName = '';
        let sceneInfo = '';
        
        // 模式1: 故事名_场景描述_A4.png 或 故事名_场景描述.png
        if (filename.includes('_')) {
          const parts = filename.split('_');
          
          if (parts.length >= 2) {
            // 第一部分是故事名
            storyName = parts[0];
            // 如果有第二部分，组合作为完整故事标识
            if (parts.length >= 3) {
              sceneInfo = parts[1];
              storyName = `${parts[0]}_${parts[1]}`;
            } else {
              sceneInfo = parts[1].replace(/\.png$/i, '').replace(/A4$/i, '');
            }
          } else {
            storyName = filename.replace(/\.png$/i, '');
          }
        } else {
          // 没有下划线，整个文件名作为故事名
          storyName = filename.replace(/\.png$/i, '');
        }
        
        // 清理故事名
        storyName = storyName.trim();
        
        if (!storyAnalysis[storyName]) {
          storyAnalysis[storyName] = { images: [], pattern: filename };
        }
        
        storyAnalysis[storyName].images.push({
          ...image,
          sceneInfo,
          originalIndex: index
        });
      });
      
      // 分析结果
      console.log('📊 故事分析结果:');
      Object.entries(storyAnalysis).forEach(([story, data]) => {
        console.log(`  ${story}: ${data.images.length}张图片, 示例: ${data.pattern}`);
      });
      
      // 🎯 重新组织：确保每个故事包含所有相关图片
      let storyIndex = 1;
      const finalStories: { [key: string]: any[] } = {};
      
      for (const [storyName, data] of Object.entries(storyAnalysis)) {
        // 如果图片数量合理（1-15张），作为一个完整故事
        if (data.images.length >= 1 && data.images.length <= 15) {
          const storyKey = `story_${storyIndex}_${storyName}`;
          finalStories[storyKey] = data.images;
          storyTitles[storyKey] = storyName;
          
          console.log(`✅ 故事${storyIndex}: ${storyName} (${data.images.length}张图片)`);
          storyIndex++;
        } 
        // 如果图片太多，可能需要进一步细分
        else if (data.images.length > 15) {
          console.log(`⚠️ 故事"${storyName}"图片过多(${data.images.length}张)，按场景细分...`);
          
          // 按场景信息进一步分组
          const sceneGroups: { [scene: string]: any[] } = {};
          data.images.forEach(img => {
            const sceneKey = img.sceneInfo || 'default';
            if (!sceneGroups[sceneKey]) {
              sceneGroups[sceneKey] = [];
            }
            sceneGroups[sceneKey].push(img);
          });
          
          // 为每个场景创建独立故事
          Object.entries(sceneGroups).forEach(([scene, images]) => {
            const storyKey = `story_${storyIndex}_${storyName}_${scene}`;
            finalStories[storyKey] = images;
            storyTitles[storyKey] = `${storyName} - ${scene}`;
            
            console.log(`✅ 细分故事${storyIndex}: ${storyName} - ${scene} (${images.length}张图片)`);
            storyIndex++;
          });
        }
      }
      
      console.log(`🎯 最终分组结果: ${Object.keys(finalStories).length} 个故事`);
      
      // 5. 🔧 使用改进的数据结构和持久化图片URL
      const rebuiltComics = [];
      let comicIndex = 1;
      
      for (const [storyKey, images] of Object.entries(finalStories)) {
        // 🔧 修复1：使用base64 data URL而不是blob URL，避免页面刷新后失效
        const imageUrls = [];
        let processedImages = 0;
        
        console.log(`📸 处理故事 "${storyKey}" 的 ${images.length} 张图片...`);
        
        for (const img of images) {
          try {
            // 如果图片数据存在，转换为base64 data URL
            if (img.data) {
              // 确定MIME类型
              const mimeType = img.type || img.mimeType || 'image/png';
              
              let dataUrl;
              if (img.data instanceof ArrayBuffer) {
                // ArrayBuffer转base64
                const uint8Array = new Uint8Array(img.data);
                const binaryString = Array.from(uint8Array).map(byte => String.fromCharCode(byte)).join('');
                const base64 = btoa(binaryString);
                dataUrl = `data:${mimeType};base64,${base64}`;
              } else if (typeof img.data === 'string') {
                // 假设已经是base64字符串
                dataUrl = img.data.startsWith('data:') ? img.data : `data:${mimeType};base64,${img.data}`;
              } else {
                // 其他类型，尝试转换
                const blob = new Blob([img.data], { type: mimeType });
                const reader = new FileReader();
                dataUrl = await new Promise((resolve) => {
                  reader.onload = () => resolve(reader.result as string);
                  reader.readAsDataURL(blob);
                });
              }
              
              imageUrls.push(dataUrl);
              processedImages++;
              console.log(`✅ 成功转换图片 ${processedImages}/${images.length}`);
            } else if (img.imageId) {
              // 如果没有直接数据，保留indexeddb引用以便后续处理
              imageUrls.push(`indexeddb:${img.imageId}`);
              console.log(`⚠️ 图片 ${img.imageId} 没有数据，使用引用`);
            }
          } catch (error) {
            console.warn(`⚠️ 转换图片失败 ${img.imageId || 'unknown'}:`, error);
            // 后备：使用indexeddb引用
            imageUrls.push(`indexeddb:${img.imageId || 'unknown'}`);
          }
        }
        
        // 🔧 修复2：使用提取的真实故事标题
        const realTitle = storyTitles[storyKey] || `漫画作品 ${comicIndex}`;
        const storyText = storyTitles[storyKey] ? 
          `${storyTitles[storyKey]}的精彩故事` : 
          `从IndexedDB重建的漫画作品 ${comicIndex}`;
        
        const comic = {
          id: `rebuilt_${Date.now()}_${comicIndex}`,
          promptId: `prompt_rebuilt_${comicIndex}`,
          title: realTitle,
          storyText: storyText,
          images: imageUrls,
          finalComicUrl: imageUrls[0] || '',
          status: 'completed',
          createdAt: new Date(),
          
          // 额外的重建信息
          rebuildInfo: {
            originalGroup: storyKey,
            imageCount: images.length,
            processedImages: processedImages,
            sourceDBs: [...new Set(images.map(img => img.sourceDB))],
            rebuildTimestamp: new Date().toISOString(),
            extractedTitle: storyTitles[storyKey] || null
          },
          
          // 兼容性字段
          parameters: {
            userInput: {
              style: { name: '重建风格' },
              prompt: realTitle
            }
          }
        };
        
        console.log(`✅ 创建漫画作品: ${realTitle} (${imageUrls.length}张图片)`);
        
        rebuiltComics.push(comic);
        comicIndex++;
      }
      
      console.log(`✅ 重建完成: ${rebuiltComics.length} 个漫画作品`);
      
      // 6. 直接加载到Vue组件，跳过localStorage保存（数据太大）
      try {
        console.log('🔄 直接加载数据到Vue组件，跳过localStorage保存...');
        
        // 直接设置到comicStore
        comicStore.savedComics = rebuiltComics;
        comicStore.updateStatistics();
        console.log(`✅ 数据已直接恢复到内存: ${rebuiltComics.length} 个作品`);
        
        // 尝试分批保存前10个作品到localStorage作为快速访问
        const quickAccess = rebuiltComics.slice(0, 10);
        try {
          localStorage.setItem('comic-generation-results', JSON.stringify(quickAccess));
          console.log(`💾 已保存前10个作品到localStorage作为快速访问`);
        } catch (quickSaveError) {
          console.warn('⚠️ 快速访问保存失败，但不影响主要功能:', quickSaveError);
          localStorage.setItem('comic-generation-results', JSON.stringify([]));
        }
        
        console.log('🔄 数据恢复完成，跳过重新加载...');
        
        return {
          success: true,
          message: `成功重建${rebuiltComics.length}个漫画作品`,
          comics: rebuiltComics,
          details: {
            totalImages: totalImageCount,
            groups: Object.keys(finalStories).length,
            rebuiltWorks: rebuiltComics.length
          }
        };
        
      } catch (saveError) {
        console.error('❌ 保存重建数据失败:', saveError);
        return {
          success: false,
          message: `重建成功但保存失败: ${(saveError as Error).message}`,
          comics: rebuiltComics
        };
      }
      
    } catch (error) {
      console.error('❌ IndexedDB重建过程失败:', error);
      return {
        success: false,
        message: `重建失败: ${(error as Error).message}`
      };
    }
  };

  // 🔍 添加快速诊断函数
  (window as any).quickDiagnoseIndexedDB = async function() {
    console.log('🔍 快速诊断IndexedDB状态...');
    
    try {
      const databases = await indexedDB.databases();
      let totalImages = 0;
      
      for (const dbInfo of databases) {
        if (!dbInfo.name) continue;
        
        try {
          const db = await new Promise((resolve, reject) => {
            const request = indexedDB.open(dbInfo.name);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
          });
          
          for (const storeName of (db as any).objectStoreNames) {
            try {
              const data = await new Promise((resolve) => {
                const transaction = (db as any).transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);
                const request = store.getAll();
                request.onsuccess = () => resolve(request.result || []);
                request.onerror = () => resolve([]);
              });
              totalImages += (data as any).length;
            } catch {}
          }
          (db as any).close();
        } catch {}
      }
      
      console.log(`📊 IndexedDB状态: ${databases.length}个数据库, ${totalImages}张图片`);
      
      const localData = localStorage.getItem('comic-generation-results');
      const localComics = localData ? JSON.parse(localData).length : 0;
      console.log(`📊 localStorage状态: ${localComics}个作品`);
      
      return { databases: databases.length, images: totalImages, localComics };
    } catch (error) {
      console.error('诊断失败:', error);
      return { error: (error as Error).message };
    }
  };

  console.log('🛠️ IndexedDB重建工具已加载到全局:');
  console.log('  • rebuildComicsFromIndexedDB() - 完整重建');
  console.log('  • quickDiagnoseIndexedDB() - 快速诊断');

  // 🔧 添加数据加载测试工具
  (window as any).testDataLoading = async function() {
    console.log('🔧 ===== 测试数据加载各个环节 =====');
    
    // 1. 测试localStorage
    const localData = localStorage.getItem('comic-generation-results');
    console.log('1️⃣ localStorage测试:');
    console.log(`   数据长度: ${localData ? localData.length : 0}`);
    if (localData) {
      const parsed = JSON.parse(localData);
      console.log(`   解析后数量: ${parsed.length}`);
      console.log(`   第一个作品ID: ${parsed[0]?.id || 'N/A'}`);
    }
    
    // 2. 测试electronStoreManager
    console.log('2️⃣ electronStoreManager测试:');
    try {
      const { electronStoreManager } = await import('@/utils/electronStoreManager');
      const electronComics = await electronStoreManager.getComics();
      console.log(`   electronStoreManager.getComics(): ${electronComics.length}个`);
      if (electronComics.length > 0) {
        console.log(`   第一个作品ID: ${electronComics[0]?.id || 'N/A'}`);
      }
    } catch (error) {
      console.error('   electronStoreManager测试失败:', error);
    }
    
    // 3. 测试comicStore
    console.log('3️⃣ comicStore测试:');
    try {
      console.log(`   当前savedComics数量: ${comicStore.savedComics?.length || 0}`);
      await comicStore.loadSavedComics();
      console.log(`   loadSavedComics后数量: ${comicStore.savedComics?.length || 0}`);
    } catch (error) {
      console.error('   comicStore测试失败:', error);
    }
    
    // 4. 测试storyStore
    console.log('4️⃣ storyStore测试:');
    try {
      const storyComics = await storyStore.loadSavedComics();
      console.log(`   storyStore.loadSavedComics(): ${storyComics.length}个`);
    } catch (error) {
      console.error('   storyStore测试失败:', error);
    }
    
    console.log('🔧 ===== 测试完成 =====');
  };
  
  console.log('🛠️ 数据加载测试工具已加载: testDataLoading()');

  // 🔍 添加原始数据格式分析工具
  (window as any).analyzeOriginalDataFormat = async function() {
    console.log('🔍 ===== 分析原始数据格式 =====');
    
    // 1. 检查localStorage中的数据结构
    const localData = localStorage.getItem('comic-generation-results');
    if (localData) {
      console.log('📊 localStorage中的数据:');
      const parsed = JSON.parse(localData);
      console.log(`  - 总条目数: ${parsed.length}`);
      
      if (parsed.length > 0) {
        console.log('🔍 分析第一个条目的数据结构:');
        const first = parsed[0];
        console.log('  完整对象:', first);
        console.log('  对象字段:', Object.keys(first));
        console.log('  - id:', first.id);
        console.log('  - title:', first.title);
        console.log('  - storyText:', first.storyText);
        console.log('  - images数组长度:', Array.isArray(first.images) ? first.images.length : 'N/A');
        console.log('  - images前3个:', Array.isArray(first.images) ? first.images.slice(0, 3) : 'N/A');
        console.log('  - createdAt:', first.createdAt);
        console.log('  - status:', first.status);
        
        if (first.rebuildInfo) {
          console.log('  - 这是重建的数据，原始分组:', first.rebuildInfo.originalGroup);
        }
        
        // 分析几个样本以了解模式
        console.log('\n🔍 分析更多样本:');
        for (let i = 0; i < Math.min(5, parsed.length); i++) {
          const item = parsed[i];
          console.log(`  样本${i + 1}:`, {
            id: item.id,
            title: item.title,
            imageCount: Array.isArray(item.images) ? item.images.length : 0,
            hasRebuildInfo: !!item.rebuildInfo
          });
        }
      }
    }
    
    // 2. 检查ComicResult类型定义
    console.log('\n📋 ComicResult应该的标准格式:');
    console.log('  参考types文件中的ComicResult接口定义');
    
    // 3. 分析IndexedDB中的原始图片数据
    console.log('\n🗄️ 分析IndexedDB中的原始数据...');
    try {
      const databases = await indexedDB.databases();
      console.log(`  发现${databases.length}个数据库`);
      
      for (const dbInfo of databases.slice(0, 2)) { // 只分析前2个数据库
        if (!dbInfo.name) continue;
        
        console.log(`\n📚 分析数据库: ${dbInfo.name}`);
        try {
          const db = await new Promise((resolve, reject) => {
            const request = indexedDB.open(dbInfo.name);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
          });
          
          for (const storeName of Array.from((db as any).objectStoreNames).slice(0, 2)) {
            console.log(`  📂 存储表: ${storeName}`);
            
            const sampleData = await new Promise((resolve) => {
              const transaction = (db as any).transaction([storeName], 'readonly');
              const store = transaction.objectStore(storeName);
              const request = store.getAll();
              request.onsuccess = () => resolve((request.result || []).slice(0, 3));
              request.onerror = () => resolve([]);
            });
            
            (sampleData as any[]).forEach((item, index) => {
              console.log(`    样本${index + 1}:`, {
                id: item.id,
                filename: item.filename,
                type: item.type || item.mimeType,
                hasData: !!item.data,
                dataSize: item.data ? (item.data.byteLength || item.data.length) : 0,
                allFields: Object.keys(item)
              });
            });
          }
          
          (db as any).close();
        } catch (error) {
          console.error(`    ❌ 读取数据库失败:`, error);
        }
      }
    } catch (error) {
      console.error('❌ IndexedDB分析失败:', error);
    }
    
    console.log('\n🔍 ===== 分析完成 =====');
    console.log('💡 基于分析结果，我们需要:');
    console.log('   1. 确定正确的ComicResult格式');
    console.log('   2. 找到真正的原始数据来源');
    console.log('   3. 按照原有格式重建，而不是创造新格式');
  };
  
  console.log('🔍 原始数据格式分析工具已加载: analyzeOriginalDataFormat()');

  // 🔧 添加紧急生成状态重置工具
  (window as any).emergencyResetGenerationState = function() {
    console.log('🚨 ===== 紧急生成状态重置 =====');
    
    try {
      // 1. 重置store状态
      if (typeof comicStore !== 'undefined') {
        comicStore.isGenerating = false;
        comicStore.generationProgress = 0;
        comicStore.generationStatus = 'idle';
        comicStore.generationError = null;
        comicStore.currentPromptId = null;
        console.log('✅ comicStore状态已重置');
      }
      
      if (typeof storyStore !== 'undefined') {
        storyStore.isGenerating = false;
        storyStore.generationProgress = 0;
        storyStore.currentStatusText = '';
        storyStore.error = null;
        console.log('✅ storyStore状态已重置');
      }
      
      // 2. 清除所有相关的localStorage
      const keysToRemove = [
        'comic-generation-state',
        'comic-store-settings', 
        'story-generation-state',
        'current-generation-id',
        'cosyvoice_realtime_state',
        'cosyvoice_session_data'
      ];
      
      keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`🧹 已清除 localStorage.${key}`);
        }
      });
      
      // 3. 清除sessionStorage
      Object.keys(sessionStorage).forEach(key => {
        if (key.includes('generation') || key.includes('comic') || key.includes('story')) {
          sessionStorage.removeItem(key);
          console.log(`🧹 已清除 sessionStorage.${key}`);
        }
      });
      
      // 4. 强制页面刷新状态
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('generation-state-reset', {
          detail: { source: 'emergency-reset', timestamp: Date.now() }
        }));
      }
      
      console.log('✅ 紧急生成状态重置完成');
      alert('生成状态已紧急重置！页面将在3秒后刷新...');
      
      // 延迟刷新页面
      setTimeout(() => {
        window.location.reload();
      }, 3000);
      
    } catch (error) {
      console.error('❌ 紧急重置失败:', error);
      alert(`重置失败: ${error.message}`);
    }
  };
  
  console.log('🚨 紧急生成状态重置工具已加载: emergencyResetGenerationState()');

  // 🗑️ 添加清除错误重建数据的工具
  (window as any).clearWrongRebuildData = async function() {
    console.log('🗑️ ===== 清除错误的重建数据 =====');
    
    const localData = localStorage.getItem('comic-generation-results');
    if (localData) {
      const parsed = JSON.parse(localData);
      console.log(`📊 当前数据: ${parsed.length} 条目`);
      
      // 检查有多少是重建数据
      const rebuildCount = parsed.filter(item => item.rebuildInfo).length;
      const originalCount = parsed.filter(item => !item.rebuildInfo).length;
      
      console.log(`  - 重建数据: ${rebuildCount} 条目`);
      console.log(`  - 原始数据: ${originalCount} 条目`);
      
      if (rebuildCount > 0) {
        const shouldClear = confirm(`发现${rebuildCount}个错误的重建数据，${originalCount}个可能的原始数据。\n\n确定要清除所有重建数据吗？`);
        
        if (shouldClear) {
          // 只保留非重建数据
          const originalData = parsed.filter(item => !item.rebuildInfo);
          localStorage.setItem('comic-generation-results', JSON.stringify(originalData));
          
          console.log(`✅ 已清除${rebuildCount}个重建数据`);
          console.log(`📊 剩余${originalData.length}个原始数据`);
          
          // 重新加载数据
          await storyStore.loadSavedComics();
          
          return { cleared: rebuildCount, remaining: originalData.length };
        }
      } else {
        console.log('✅ 没有发现重建数据，无需清除');
      }
    }
  };

  // 🔍 寻找原始ComicResult数据的工具
  (window as any).findOriginalComicData = async function() {
    console.log('🔍 ===== 寻找原始ComicResult数据 =====');
    
    // 1. 检查可能的备份位置
    const possibleKeys = [
      'comic-generation-results',
      'comic-generation-results-backup',
      'saved-comics',
      'comics-backup',
      'original-comics'
    ];
    
    console.log('📂 检查localStorage中的所有可能键名...');
    for (const key of possibleKeys) {
      const data = localStorage.getItem(key);
      if (data) {
        try {
          const parsed = JSON.parse(data);
          console.log(`✅ 找到数据 "${key}": ${Array.isArray(parsed) ? parsed.length : 'unknown'} 条目`);
          
          if (Array.isArray(parsed) && parsed.length > 0) {
            const first = parsed[0];
            console.log(`  示例结构:`, {
              id: first.id,
              title: first.title,
              hasRebuildInfo: !!first.rebuildInfo,
              imageCount: Array.isArray(first.images) ? first.images.length : 0
            });
          }
        } catch (e) {
          console.log(`⚠️ "${key}" 数据格式错误:`, e);
        }
      }
    }
    
    // 2. 检查electronStoreManager中是否有原始数据
    console.log('\n🔧 检查electronStoreManager原始存储...');
    try {
      const { electronStoreManager } = await import('@/utils/electronStoreManager');
      
      // 尝试直接获取原始数据，绕过我们的修复逻辑
      const rawData = await electronStoreManager.get('comic-generation-results', null);
      console.log('📦 electronStore原始数据:', rawData);
      
      if (rawData && typeof rawData === 'object') {
        console.log('🔍 分析electronStore数据结构...');
        
        if (Array.isArray(rawData)) {
          console.log(`  📊 直接数组: ${rawData.length} 条目`);
          rawData.slice(0, 3).forEach((item, index) => {
            console.log(`    样本${index + 1}:`, {
              id: item?.id,
              title: item?.title,
              hasRebuildInfo: !!item?.rebuildInfo
            });
          });
        } else {
          console.log('  📊 对象格式，属性:', Object.keys(rawData));
          for (const [key, value] of Object.entries(rawData)) {
            console.log(`    ${key}:`, Array.isArray(value) ? `数组(${value.length})` : typeof value);
          }
        }
      }
    } catch (error) {
      console.error('❌ electronStoreManager检查失败:', error);
    }
    
    // 3. 基于文件名模式重新构建真正的ComicResult
    console.log('\n🎯 基于IndexedDB数据重新构建原始格式...');
    return await this.rebuildFromIndexedDBCorrectly();
  };

  // 🔧 正确的IndexedDB重建工具（基于真实ComicResult格式）
  (window as any).rebuildFromIndexedDBCorrectly = async function() {
    console.log('🔧 ===== 按照真正的ComicResult格式重建 =====');
    
    try {
      // 收集所有图片数据
      const databases = await indexedDB.databases();
      const allImages = [];
      
      for (const dbInfo of databases) {
        if (!dbInfo.name) continue;
        
        const db = await new Promise((resolve, reject) => {
          const request = indexedDB.open(dbInfo.name);
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
        
        for (const storeName of (db as any).objectStoreNames) {
          const data = await new Promise((resolve) => {
            const transaction = (db as any).transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => resolve([]);
          });
          
          allImages.push(...(data as any[]));
        }
        
        (db as any).close();
      }
      
      console.log(`📸 收集到 ${allImages.length} 张图片`);
      
      // 按照故事名称分组（真正的ComicResult应该一个故事一个作品）
      const storyGroups: { [storyName: string]: any[] } = {};
      
      allImages.forEach(img => {
        if (!img.filename) return;
        
        // 从文件名提取故事名：张大猪的江湖行_A5_00001_.png -> 张大猪的江湖行
        const storyName = img.filename.split('_')[0];
        
        if (!storyGroups[storyName]) {
          storyGroups[storyName] = [];
        }
        storyGroups[storyName].push(img);
      });
      
      console.log('📊 故事分组结果:');
      Object.entries(storyGroups).forEach(([story, images]) => {
        console.log(`  ${story}: ${images.length} 张图片`);
      });
      
      // 为每个故事创建一个正确的ComicResult
      const correctComics = [];
      let index = 1;
      
      for (const [storyName, images] of Object.entries(storyGroups)) {
        // 🔧 修复图片转换逻辑
        const imageUrls = [];
        console.log(`🖼️ 开始处理故事"${storyName}"的${images.length}张图片...`);
        
        for (const img of images.slice(0, 12)) { // 限制每个故事最多12张图片
          if (img.data) {
            try {
              const mimeType = img.type || img.mimeType || 'image/png';
              let dataUrl = '';
              
              console.log(`🔍 处理图片 ${img.filename || img.id}:`, {
                dataType: typeof img.data,
                isArrayBuffer: img.data instanceof ArrayBuffer,
                isBlob: img.data instanceof Blob,
                isUint8Array: img.data instanceof Uint8Array,
                mimeType: mimeType
              });
              
              if (img.data instanceof ArrayBuffer) {
                // ArrayBuffer -> base64
                const uint8Array = new Uint8Array(img.data);
                const binaryString = Array.from(uint8Array).map(byte => String.fromCharCode(byte)).join('');
                const base64 = btoa(binaryString);
                dataUrl = `data:${mimeType};base64,${base64}`;
                console.log(`✅ ArrayBuffer转换成功: ${dataUrl.substring(0, 50)}...`);
              } 
              else if (img.data instanceof Uint8Array) {
                // Uint8Array -> base64
                const binaryString = Array.from(img.data).map(byte => String.fromCharCode(byte)).join('');
                const base64 = btoa(binaryString);
                dataUrl = `data:${mimeType};base64,${base64}`;
                console.log(`✅ Uint8Array转换成功: ${dataUrl.substring(0, 50)}...`);
              }
              else if (img.data instanceof Blob) {
                // Blob -> base64 (异步)
                console.log(`🔄 处理Blob对象...`);
                dataUrl = await new Promise((resolve, reject) => {
                  const reader = new FileReader();
                  reader.onload = () => {
                    const result = reader.result as string;
                    console.log(`✅ Blob转换成功: ${result.substring(0, 50)}...`);
                    resolve(result);
                  };
                  reader.onerror = () => {
                    console.error(`❌ Blob转换失败`);
                    reject(new Error('Blob转换失败'));
                  };
                  reader.readAsDataURL(img.data);
                });
              }
              else if (typeof img.data === 'string') {
                // 字符串，检查是否已经是base64
                if (img.data.startsWith('data:')) {
                  dataUrl = img.data;
                  console.log(`✅ 已是dataURL: ${dataUrl.substring(0, 50)}...`);
                } else {
                  // 假设是base64字符串
                  dataUrl = `data:${mimeType};base64,${img.data}`;
                  console.log(`✅ base64字符串转换: ${dataUrl.substring(0, 50)}...`);
                }
              }
              else {
                // 其他类型，尝试转换为Blob再处理
                console.log(`🔄 尝试转换未知类型为Blob...`);
                const blob = new Blob([img.data], { type: mimeType });
                dataUrl = await new Promise((resolve, reject) => {
                  const reader = new FileReader();
                  reader.onload = () => {
                    const result = reader.result as string;
                    console.log(`✅ 未知类型转换成功: ${result.substring(0, 50)}...`);
                    resolve(result);
                  };
                  reader.onerror = () => {
                    console.error(`❌ 未知类型转换失败`);
                    reject(new Error('未知类型转换失败'));
                  };
                  reader.readAsDataURL(blob);
                });
              }
              
              if (dataUrl && dataUrl.startsWith('data:')) {
                imageUrls.push(dataUrl);
                console.log(`✅ 图片${imageUrls.length}添加成功`);
              } else {
                console.warn(`⚠️ 图片转换结果无效: ${dataUrl}`);
              }
              
            } catch (error) {
              console.error(`❌ 图片转换失败 ${img.filename || img.id}:`, error);
            }
          }
        }
        
        console.log(`📊 故事"${storyName}"最终获得${imageUrls.length}张有效图片`);
        
        // 创建标准ComicResult格式
        const comic = {
          id: `story_${Date.now()}_${index}`,
          promptId: `prompt_${Date.now()}_${index}`,
          title: storyName,
          storyText: `${storyName}的精彩故事`,
          images: imageUrls,
          finalComicUrl: imageUrls[0] || '',
          status: 'completed',
          createdAt: new Date(),
          parameters: {
            workflow: {},
            userInput: {
              storyPrompt: storyName,
              style: { id: 'recovered', name: '恢复的风格' },
              characterConfig: { enableFaceSwap: false },
              layoutConfig: { columns: 3, panelWidth: 300, panelHeight: 400 }
            },
            priority: 'normal'
          }
        };
        
        correctComics.push(comic);
        console.log(`✅ 创建故事: ${storyName} (${imageUrls.length}张图片)`);
        index++;
      }
      
      console.log(`🎉 重建完成: ${correctComics.length} 个真正的漫画故事`);
      
      // 保存到localStorage
      localStorage.setItem('comic-generation-results', JSON.stringify(correctComics));
      await storyStore.loadSavedComics();
      
      return {
        success: true,
        storiesCount: correctComics.length,
        totalImages: allImages.length,
        comics: correctComics
      };
      
    } catch (error) {
      console.error('❌ 正确重建失败:', error);
      return { success: false, error: error.message };
    }
  };

  console.log('🗑️ 错误数据清理工具已加载: clearWrongRebuildData()');
  console.log('🔍 原始数据寻找工具已加载: findOriginalComicData()');
  console.log('🔧 正确重建工具已加载: rebuildFromIndexedDBCorrectly()');

  // 🔧 快速修复当前32个作品的图片URL问题
  (window as any).fixCurrentImageUrls = async function() {
    console.log('🔧 ===== 修复当前32个作品的图片URL =====');
    
    const localData = localStorage.getItem('comic-generation-results');
    if (!localData) {
      console.log('❌ 没有找到漫画数据');
      return;
    }
    
    const comics = JSON.parse(localData);
    console.log(`📊 发现 ${comics.length} 个作品`);
    
    let fixedCount = 0;
    for (let i = 0; i < comics.length; i++) {
      const comic = comics[i];
      console.log(`🔧 修复作品 ${i + 1}: ${comic.title}`);
      
      if (comic.images && Array.isArray(comic.images)) {
        const fixedImages = [];
        
        for (let j = 0; j < comic.images.length; j++) {
          const imageUrl = comic.images[j];
          
          // 检查是否是错误的格式
          if (imageUrl.includes('[object Blob]')) {
            console.log(`⚠️ 发现错误URL: ${imageUrl.substring(0, 50)}...`);
            
            // 尝试从IndexedDB重新获取这张图片
            try {
              // 根据作品标题尝试找到对应的图片
              const storyName = comic.title;
              const matchingImages = await this.findImagesByStoryName(storyName);
              
              if (matchingImages && matchingImages[j]) {
                const img = matchingImages[j];
                
                if (img.data) {
                  let fixedUrl = '';
                  const mimeType = img.type || 'image/png';
                  
                  if (img.data instanceof ArrayBuffer) {
                    const uint8Array = new Uint8Array(img.data);
                    const binaryString = Array.from(uint8Array).map(byte => String.fromCharCode(byte)).join('');
                    const base64 = btoa(binaryString);
                    fixedUrl = `data:${mimeType};base64,${base64}`;
                  } else if (img.data instanceof Blob) {
                    fixedUrl = await new Promise((resolve) => {
                      const reader = new FileReader();
                      reader.onload = () => resolve(reader.result as string);
                      reader.onerror = () => resolve('');
                      reader.readAsDataURL(img.data);
                    });
                  }
                  
                  if (fixedUrl && fixedUrl.startsWith('data:')) {
                    fixedImages.push(fixedUrl);
                    console.log(`✅ 图片 ${j + 1} 修复成功`);
                  } else {
                    fixedImages.push(imageUrl); // 保持原URL
                    console.log(`⚠️ 图片 ${j + 1} 修复失败，保持原URL`);
                  }
                } else {
                  fixedImages.push(imageUrl); // 保持原URL
                }
              } else {
                fixedImages.push(imageUrl); // 保持原URL
              }
            } catch (error) {
              console.warn(`❌ 修复图片失败:`, error);
              fixedImages.push(imageUrl); // 保持原URL
            }
          } else {
            // URL格式正常，保持不变
            fixedImages.push(imageUrl);
          }
        }
        
        comic.images = fixedImages;
        comic.finalComicUrl = fixedImages[0] || comic.finalComicUrl;
        fixedCount++;
      }
    }
    
    // 保存修复后的数据
    localStorage.setItem('comic-generation-results', JSON.stringify(comics));
    await storyStore.loadSavedComics();
    
    console.log(`✅ 修复完成: ${fixedCount} 个作品`);
    return { fixed: fixedCount, total: comics.length };
  };

  // 辅助函数：根据故事名称查找图片
  (window as any).findImagesByStoryName = async function(storyName) {
    const databases = await indexedDB.databases();
    const allImages = [];
    
    for (const dbInfo of databases) {
      if (!dbInfo.name) continue;
      
      try {
        const db = await new Promise((resolve, reject) => {
          const request = indexedDB.open(dbInfo.name);
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
        
        for (const storeName of (db as any).objectStoreNames) {
          const data = await new Promise((resolve) => {
            const transaction = (db as any).transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => resolve([]);
          });
          
          // 筛选出匹配故事名的图片
          (data as any[]).forEach(img => {
            if (img.filename && img.filename.startsWith(storyName)) {
              allImages.push(img);
            }
          });
        }
        
        (db as any).close();
      } catch (error) {
        console.warn(`数据库访问失败:`, error);
      }
    }
    
    return allImages;
  };

  console.log('🔧 当前图片URL修复工具已加载: fixCurrentImageUrls()');

  // 🚨 强制重建所有图片URL工具
  (window as any).forceRebuildAllImages = async function() {
    console.log('🚨 ===== 强制重建所有32个作品的图片URL =====');
    
    // 1. 先收集所有IndexedDB图片数据
    console.log('📸 第一步：收集IndexedDB中的所有图片...');
    const databases = await indexedDB.databases();
    const imagesByStory = {};
    
    for (const dbInfo of databases) {
      if (!dbInfo.name) continue;
      
      try {
        const db = await new Promise((resolve, reject) => {
          const request = indexedDB.open(dbInfo.name);
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
        
        for (const storeName of (db as any).objectStoreNames) {
          const images = await new Promise((resolve) => {
            const transaction = (db as any).transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => resolve([]);
          });
          
          (images as any[]).forEach(img => {
            if (img.filename && img.data) {
              const storyName = img.filename.split('_')[0];
              if (!imagesByStory[storyName]) {
                imagesByStory[storyName] = [];
              }
              imagesByStory[storyName].push(img);
            }
          });
        }
        
        (db as any).close();
      } catch (error) {
        console.warn(`数据库访问失败:`, error);
      }
    }
    
    console.log('📊 IndexedDB图片统计:', Object.keys(imagesByStory).map(story => `${story}: ${imagesByStory[story].length}张`));
    
    // 2. 更新localStorage中的漫画数据
    console.log('💾 第二步：更新localStorage中的32个作品...');
    const localData = localStorage.getItem('comic-generation-results');
    if (!localData) {
      console.error('❌ localStorage中没有数据');
      return;
    }
    
    const comics = JSON.parse(localData);
    console.log(`📚 当前有${comics.length}个作品`);
    
    for (let i = 0; i < comics.length; i++) {
      const comic = comics[i];
      const storyName = comic.title;
      
      console.log(`🔧 处理作品 ${i + 1}: ${storyName}`);
      
      // 找到对应的图片数据
      const storyImages = imagesByStory[storyName];
      if (!storyImages || storyImages.length === 0) {
        console.warn(`⚠️ 没有找到故事"${storyName}"的图片数据`);
        continue;
      }
      
      // 转换图片为正确的base64 URL
      const newImageUrls = [];
      for (let j = 0; j < Math.min(storyImages.length, 12); j++) {
        const img = storyImages[j];
        
        try {
          console.log(`🖼️ 转换图片 ${j + 1}/${storyImages.length}: ${img.filename}`);
          
          let dataUrl = '';
          const mimeType = img.type || 'image/png';
          
          // 🔧 关键修复：确保正确处理不同的数据类型
          if (img.data instanceof ArrayBuffer) {
            const uint8Array = new Uint8Array(img.data);
            const binaryString = String.fromCharCode.apply(null, Array.from(uint8Array));
            const base64 = btoa(binaryString);
            dataUrl = `data:${mimeType};base64,${base64}`;
          } else if (img.data instanceof Uint8Array) {
            const binaryString = String.fromCharCode.apply(null, Array.from(img.data));
            const base64 = btoa(binaryString);
            dataUrl = `data:${mimeType};base64,${base64}`;
          } else if (typeof img.data === 'string') {
            // 如果已经是base64字符串
            if (img.data.startsWith('data:')) {
              dataUrl = img.data;
            } else {
              dataUrl = `data:${mimeType};base64,${img.data}`;
            }
          } else {
            // 其他类型，转换为ArrayBuffer再处理
            console.log(`🔄 处理其他类型数据: ${typeof img.data}`);
            const buffer = new Uint8Array(img.data);
            const binaryString = String.fromCharCode.apply(null, Array.from(buffer));
            const base64 = btoa(binaryString);
            dataUrl = `data:${mimeType};base64,${base64}`;
          }
          
          // 验证生成的URL
          if (dataUrl.startsWith('data:image/') && !dataUrl.includes('[object') && dataUrl.length > 100) {
            newImageUrls.push(dataUrl);
            console.log(`✅ 图片 ${j + 1} 转换成功: ${dataUrl.substring(0, 50)}...`);
          } else {
            console.warn(`❌ 图片 ${j + 1} 转换失败，结果: ${dataUrl.substring(0, 100)}`);
          }
          
        } catch (error) {
          console.error(`❌ 图片转换异常:`, error);
        }
      }
      
      if (newImageUrls.length > 0) {
        console.log(`✅ 故事"${storyName}"成功转换${newImageUrls.length}张图片`);
        comic.images = newImageUrls;
        comic.finalComicUrl = newImageUrls[0];
      } else {
        console.warn(`⚠️ 故事"${storyName}"没有成功转换任何图片`);
      }
    }
    
    // 3. 保存并重新加载
    console.log('💾 第三步：保存更新后的数据...');
    localStorage.setItem('comic-generation-results', JSON.stringify(comics));
    
    console.log('🔄 第四步：重新加载到Vue组件...');
    await storyStore.loadSavedComics();
    
    console.log('🎉 强制重建完成！32个作品的图片应该现在可以正常显示了');
    
    return {
      success: true,
      totalComics: comics.length,
      processedStories: Object.keys(imagesByStory).length,
      message: '强制重建完成'
    };
  };

  // 🎯 暴露连接清理工具到全局
  (window as any).emergencyCleanup = async () => {
    console.log('🚨 紧急清理所有WebSocket连接...');
    
    try {
      const { cleanupAllConnections } = await import('@/services/masterWebSocketManager');
      cleanupAllConnections();
      
      // 强制重新连接
      console.log('🔄 等待3秒后重新连接...');
      setTimeout(async () => {
        try {
          await storyStore.connect();
          console.log('✅ 重新连接成功');
        } catch (error) {
          console.warn('⚠️ 重新连接失败:', error);
        }
      }, 3000);
      
      return true;
    } catch (error) {
      console.error('❌ 紧急清理失败:', error);
      return false;
    }
  };
  
  // 🧹 初始化内存清理管理器
  console.log('🧹 初始化内存清理管理器...');
  try {
    // 设置内存阈值为75%（更积极的清理）
    memoryCleanupManager.setMemoryThreshold(0.75);
    
    // 设置清理间隔为90秒（更频繁的检查）
    memoryCleanupManager.setCleanupInterval(90 * 1000);
    
    console.log('✅ 内存清理管理器已初始化');
    
    // 🔧 暴露到全局供调试使用
    (window as any).forceMemoryCleanup = () => {
      console.log('🧹 手动触发内存清理...');
      memoryCleanupManager.forceCleanup();
    };
    
    (window as any).getMemoryReport = () => {
      const report = memoryCleanupManager.getMemoryReport();
      console.log('📊 内存使用报告:', report);
      return report;
    };
    
  } catch (error) {
    console.error('❌ 内存清理管理器初始化失败:', error);
  }
  
  console.log('🎉 ComicMainPanel 初始化完成! 使用 emergencyCleanup() 可紧急清理连接, forceMemoryCleanup() 可手动清理内存');
});

// 🎯 防抖：页面焦点处理器
let lastFocusTime = 0;
const FOCUS_DEBOUNCE_DELAY = 5000; // 5秒内只处理一次

// 定义页面焦点处理函数（在顶层作用域）
const handlePageFocus = () => {
  const now = Date.now();
  
  // 🎯 防抖：避免频繁触发
  if (now - lastFocusTime < FOCUS_DEBOUNCE_DELAY) {
    return; // 静默跳过，减少日志噪音
  }
  lastFocusTime = now;
  
  console.log('👁️ 页面重新获得焦点，检查数据同步...');
  
  // 🔑 关键修复：先检查数据质量，避免错误数据覆盖正确数据
  const rawStorage = localStorage.getItem('comic-generation-results');
  const currentStoreCount = comicStore.savedComics?.length || 0;
  
  console.log('🔍 焦点检查 - localStorage:', rawStorage ? `${rawStorage.length} 字符` : '无数据');
  console.log('🔍 焦点检查 - Store中漫画数量:', currentStoreCount);
  
  // 如果localStorage数据异常短（可能是空数组"[]"），但store中有数据，不要覆盖
  if (rawStorage && rawStorage.length < 10 && currentStoreCount > 0) {
    console.log('⚠️ localStorage数据异常短，但store中有数据，跳过同步避免数据丢失（已防抖优化）');
    return;
  }
  
  // 如果localStorage有合理长度的数据，才进行同步
  if (rawStorage && rawStorage.length > 10) {
    try {
      const parsedData = JSON.parse(rawStorage);
      if (Array.isArray(parsedData) && parsedData.length > 0) {
        console.log('✅ localStorage数据看起来正常，进行同步');
        storyStore.loadSavedComics();
      } else {
        console.warn('⚠️ localStorage包含空数组，跳过同步');
      }
    } catch (error) {
      console.error('❌ localStorage数据解析失败，跳过同步:', error);
    }
  } else {
    console.log('📭 localStorage无数据或数据过短，跳过同步');
  }
};

// 用于存储handleVisibilityChange函数引用，以便清理
let handleVisibilityChange: (() => void) | null = null;
let handleStorageChange: ((event: StorageEvent) => void) | null = null;

// 🎯 组件卸载前强制清理所有WebSocket连接
onBeforeUnmount(async () => {
  console.log('🚨 [ComicMainPanel] 组件即将卸载，清理WebSocket连接和定时器...');
  
  try {
    // 🔑 新增：停止定期状态检查
    storyStore.stopPeriodicStatusCheck();
    console.log('✅ [ComicMainPanel] 定期状态检查已停止');
    
    // 导入清理函数
    const masterWsModule = await import('@/services/masterWebSocketManager');
    
    // 先销毁当前上下文的连接
    if (typeof masterWsModule.destroyMasterWebSocketManager === 'function') {
      masterWsModule.destroyMasterWebSocketManager('comic-generation');
      masterWsModule.destroyMasterWebSocketManager('default');
    }
    
    // 如果连接还多，则强制清理所有连接
    if (typeof masterWsModule.cleanupAllConnections === 'function') {
      masterWsModule.cleanupAllConnections();
    }
    
    console.log('✅ [ComicMainPanel] WebSocket连接清理完成');
  } catch (error) {
    console.error('❌ [ComicMainPanel] WebSocket连接清理失败:', error);
  }
});

onUnmounted(async () => {
  console.log('🧹 [ComicMainPanel] 开始资源清理...');
  
  // 🔧 【关键修复】：重置生成状态，避免页面切换后状态残留
  try {
    console.log('🔄 重置生成状态...');
    
    // 取消正在进行的生成
    if (comicStore.isGenerating) {
      console.log('⏹️ 检测到正在进行的生成，执行取消操作...');
      comicStore.cancelGeneration();
    }
    
    // 强制重置所有生成相关状态
    comicStore.generationProgress = 0;
    comicStore.generationStatus = 'idle';
    comicStore.generationError = null;
    comicStore.currentPromptId = null;
    comicStore.isGenerating = false;
    
    // 重置storyStore的生成状态
    if (storyStore.isGenerating) {
      console.log('⏹️ 重置storyStore生成状态...');
      if (typeof storyStore.cancelGeneration === 'function') {
        storyStore.cancelGeneration();
      }
    }
    
    // 🔑 关键修复：清除localStorage中的持久化生成状态
    console.log('🧹 清除localStorage中的生成状态...');
    localStorage.removeItem('comic-generation-state');
    localStorage.removeItem('comic-store-settings');
    
    // 额外清除可能的其他生成状态键
    const keysToRemove = ['comic-generation-state', 'story-generation-state', 'current-generation-id'];
    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        console.log(`🧹 已清除 localStorage 键: ${key}`);
      }
    });
    
    console.log('✅ 生成状态重置完成');
  } catch (error) {
    console.error('❌ 生成状态重置失败:', error);
  }
  
  // 🚀 停止性能监控器
  try {
    console.log('📊 停止性能监控器...');
    performanceMonitor.stopMonitoring();
    console.log('✅ 性能监控器已停止');
  } catch (error) {
    console.error('❌ 性能监控器停止失败:', error);
  }
  
  // 🔄 清理多设备同步监听器
  try {
    // 只有在handleGenerationStateSync存在时才清理
    if (typeof handleGenerationStateSync !== 'undefined') {
      window.removeEventListener('generation-state-sync', handleGenerationStateSync);
      console.log('✅ 多设备同步监听器已清理');
    }
  } catch (error) {
    console.warn('⚠️ 多设备同步监听器清理失败:', error);
  }
  
  // 🔄 清理WebSocket监听器
  try {
    const wsManager = getMasterWebSocketManager({}, 'comic-generation');
    if (wsManager && typeof wsManager.off === 'function') {
      console.log('🧹 清理WebSocket监听器...');
      // 清理所有可能的监听器
      wsManager.off('progress-update');
      wsManager.off('generation-complete');
      wsManager.off('generation-error');
      wsManager.off('generation-status');
      console.log('✅ WebSocket监听器清理完成');
    }
  } catch (error) {
    console.error('❌ 清理WebSocket监听器失败:', error);
  }
  
  // 🚨 首要任务：清理WebSocket订阅（防止僵尸连接）
  try {
    console.log('🚨 清理WebSocket订阅...');
    // 清理订阅（新的统一管理器会自动处理）
    console.log('🧹 清理WebSocket订阅...');
    
    // 🚨 新增：统一WebSocket管理器清理
    const wsManager = getMasterWebSocketManager({}, 'comic-generation');
    wsManager.send('PAGE_UNLOAD', { pageContext: 'comic-generation' });
    
    console.log('✅ WebSocket订阅清理完成');
  } catch (error) {
    console.error('❌ WebSocket订阅清理失败:', error);
  }
  
  // 🔗 清理所有Blob URLs
  console.log('🧹 清理所有Blob URLs...');
  BlobUrlManager.revokeAllUrls();
  
  // 清理WebSocket连接
  comfyUIService.disconnect();
  
  // 停止背景音效并清理引用
  if (ambientAudioRef.value) {
    ambientAudioRef.value.pause();
    ambientAudioRef.value.removeEventListener('ended', () => {});
    ambientAudioRef.value.src = '';
    ambientAudioRef.value = undefined;
  }
  
  // 清理粒子效果DOM节点
  if (particleLayerRef.value) {
    particleLayerRef.value.innerHTML = '';
    particleLayerRef.value = undefined;
  }
  
  // 清理进度监听器
  
  
  // 停止内存监控
  memoryMonitor.stopMonitoring();
  
  // 清理页面焦点监听器
  window.removeEventListener('focus', handlePageFocus);
  if (handleVisibilityChange) {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  }
  if (handleStorageChange) {
    window.removeEventListener('storage', handleStorageChange);
  }
  
  console.log('✅ [ComicMainPanel] 资源清理完成');
  
  // 开发环境下生成内存报告
  if (process.env.NODE_ENV === 'development') {
    console.log(memoryMonitor.generateReport());
  }
});
</script>

<style scoped src="./comic-main-styles.css"></style>