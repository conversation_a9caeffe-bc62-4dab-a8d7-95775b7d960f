/*!
 * @pixi/filter-noise - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/filter-noise is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/core"),o=function(e,r){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var r in o)o.hasOwnProperty(r)&&(e[r]=o[r])},o(e,r)};var r=function(r){function n(o,n){void 0===o&&(o=.5),void 0===n&&(n=Math.random());var t=r.call(this,e.defaultFilterVertex,"precision highp float;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nuniform float uNoise;\nuniform float uSeed;\nuniform sampler2D uSampler;\n\nfloat rand(vec2 co)\n{\n    return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);\n}\n\nvoid main()\n{\n    vec4 color = texture2D(uSampler, vTextureCoord);\n    float randomValue = rand(gl_FragCoord.xy * uSeed);\n    float diff = (randomValue - 0.5) * uNoise;\n\n    // Un-premultiply alpha before applying the color matrix. See issue #3539.\n    if (color.a > 0.0) {\n        color.rgb /= color.a;\n    }\n\n    color.r += diff;\n    color.g += diff;\n    color.b += diff;\n\n    // Premultiply alpha again.\n    color.rgb *= color.a;\n\n    gl_FragColor = color;\n}\n",{uNoise:0,uSeed:0})||this;return t.noise=o,t.seed=n,t}return function(e,r){function n(){this.constructor=e}o(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}(n,r),Object.defineProperty(n.prototype,"noise",{get:function(){return this.uniforms.uNoise},set:function(e){this.uniforms.uNoise=e},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"seed",{get:function(){return this.uniforms.uSeed},set:function(e){this.uniforms.uSeed=e},enumerable:!1,configurable:!0}),n}(e.Filter);exports.NoiseFilter=r;
//# sourceMappingURL=filter-noise.min.js.map
