{"version": 3, "file": "prepare.js", "sources": ["../../src/settings.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/CountLimiter.ts", "../../src/BasePrepare.ts", "../../src/Prepare.ts", "../../src/TimeLimiter.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\n\n/**\n * Default number of uploads per frame using prepare plugin.\n * @static\n * @memberof PIXI.settings\n * @name UPLOADS_PER_FRAME\n * @type {number}\n * @default 4\n */\nsettings.UPLOADS_PER_FRAME = 4;\n\nexport { settings };\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * CountLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of items per frame.\n * @memberof PIXI\n */\nexport class CountLimiter\n{\n    /** The maximum number of items that can be prepared each frame. */\n    public maxItemsPerFrame: number;\n\n    /** The number of items that can be prepared in the current frame. */\n    public itemsLeft: number;\n\n    /**\n     * @param maxItemsPerFrame - The maximum number of items that can be prepared each frame.\n     */\n    constructor(maxItemsPerFrame: number)\n    {\n        this.maxItemsPerFrame = maxItemsPerFrame;\n        this.itemsLeft = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.itemsLeft = this.maxItemsPerFrame;\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return this.itemsLeft-- > 0;\n    }\n}\n", "import { Texture, BaseTexture } from '@pixi/core';\nimport { Ticker, UPDATE_PRIORITY } from '@pixi/ticker';\nimport { settings } from '@pixi/settings';\nimport type { DisplayObject } from '@pixi/display';\nimport { Container } from '@pixi/display';\nimport { Text, TextStyle, TextMetrics } from '@pixi/text';\nimport { CountLimiter } from './CountLimiter';\n\nimport type { AbstractRenderer } from '@pixi/core';\nimport { deprecation } from '@pixi/utils';\n\ninterface IArrowFunction\n{\n    (): void;\n}\ninterface IUploadHook\n{\n    (helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean;\n}\n\ninterface IFindHook\n{\n    (item: any, queue: Array<any>): boolean;\n}\n\nexport interface IDisplayObjectExtended extends DisplayObject\n{\n    _textures?: Array<Texture>;\n    _texture?: Texture;\n    style?: TextStyle | Partial<TextStyle>;\n}\n\n/**\n * Built-in hook to find multiple textures from objects like AnimatedSprites.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findMultipleBaseTextures(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    let result = false;\n\n    // Objects with multiple textures\n    if (item && item._textures && item._textures.length)\n    {\n        for (let i = 0; i < item._textures.length; i++)\n        {\n            if (item._textures[i] instanceof Texture)\n            {\n                const baseTexture = item._textures[i].baseTexture;\n\n                if (queue.indexOf(baseTexture) === -1)\n                {\n                    queue.push(baseTexture);\n                    result = true;\n                }\n            }\n        }\n    }\n\n    return result;\n}\n\n/**\n * Built-in hook to find BaseTextures from Texture.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findBaseTexture(item: Texture, queue: Array<any>): boolean\n{\n    if (item.baseTexture instanceof BaseTexture)\n    {\n        const texture = item.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find textures from objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findTexture(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item._texture && item._texture instanceof Texture)\n    {\n        const texture = item._texture.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to draw PIXI.Text to its texture.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction drawText(_helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof Text)\n    {\n        // updating text will return early if it is not dirty\n        item.updateText(true);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to calculate a text style for a PIXI.Text object.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction calculateTextStyle(_helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        const font = item.toFontString();\n\n        TextMetrics.measureFont(font);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find Text objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Text object was found.\n */\nfunction findText(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Text)\n    {\n        // push the text style to prepare it - this can be really expensive\n        if (queue.indexOf(item.style) === -1)\n        {\n            queue.push(item.style);\n        }\n        // also push the text object so that we can render it (to canvas/texture) if needed\n        if (queue.indexOf(item) === -1)\n        {\n            queue.push(item);\n        }\n        // also push the Text's texture for upload to GPU\n        const texture = item._texture.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find TextStyle objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.TextStyle object was found.\n */\nfunction findTextStyle(item: TextStyle, queue: Array<any>): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        if (queue.indexOf(item) === -1)\n        {\n            queue.push(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare manager provides functionality to upload content to the GPU.\n *\n * BasePrepare handles basic queuing functionality and is extended by\n * {@link PIXI.Prepare} and {@link PIXI.CanvasPrepare}\n * to provide preparation capabilities specific to their respective renderers.\n * @example\n * // Create a sprite\n * const sprite = PIXI.Sprite.from('something.png');\n *\n * // Load object into GPU\n * app.renderer.plugins.prepare.upload(sprite, () => {\n *\n *     //Texture(s) has been uploaded to GPU\n *     app.stage.addChild(sprite);\n *\n * })\n * @abstract\n * @memberof PIXI\n */\nexport class BasePrepare\n{\n    /**\n     * The limiter to be used to control how quickly items are prepared.\n     * @type {PIXI.CountLimiter|PIXI.TimeLimiter}\n     */\n    private limiter: CountLimiter;\n\n    /** Reference to the renderer. */\n    protected renderer: AbstractRenderer;\n\n    /**\n     * The only real difference between CanvasPrepare and Prepare is what they pass\n     * to upload hooks. That different parameter is stored here.\n     */\n    protected uploadHookHelper: any;\n\n    /** Collection of items to uploads at once. */\n    protected queue: Array<any>;\n\n    /**\n     * Collection of additional hooks for finding assets.\n     * @type {Array<Function>}\n     */\n    public addHooks: Array<any>;\n\n    /**\n     * Collection of additional hooks for processing assets.\n     * @type {Array<Function>}\n     */\n    public uploadHooks: Array<any>;\n\n    /**\n     * Callback to call after completed.\n     * @type {Array<Function>}\n     */\n    public completes: Array<any>;\n\n    /**\n     * If prepare is ticking (running).\n     * @type {boolean}\n     */\n    public ticking: boolean;\n\n    /**\n     * 'bound' call for prepareItems().\n     * @type {Function}\n     */\n    private delayedTick: IArrowFunction;\n\n    /**\n     * @param {PIXI.AbstractRenderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: AbstractRenderer)\n    {\n        this.limiter = new CountLimiter(settings.UPLOADS_PER_FRAME);\n        this.renderer = renderer;\n        this.uploadHookHelper = null;\n        this.queue = [];\n        this.addHooks = [];\n        this.uploadHooks = [];\n        this.completes = [];\n        this.ticking = false;\n        this.delayedTick = (): void =>\n        {\n            // unlikely, but in case we were destroyed between tick() and delayedTick()\n            if (!this.queue)\n            {\n                return;\n            }\n            this.prepareItems();\n        };\n\n        // hooks to find the correct texture\n        this.registerFindHook(findText);\n        this.registerFindHook(findTextStyle);\n        this.registerFindHook(findMultipleBaseTextures);\n        this.registerFindHook(findBaseTexture);\n        this.registerFindHook(findTexture);\n\n        // upload hooks\n        this.registerUploadHook(drawText);\n        this.registerUploadHook(calculateTextStyle);\n    }\n\n    /**\n     * Upload all the textures and graphics to the GPU.\n     * @method PIXI.BasePrepare#upload\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} [item] -\n     *        Container or display object to search for items to upload or the items to upload themselves,\n     *        or optionally ommitted, if items have been added using {@link PIXI.BasePrepare#add `prepare.add`}.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture): Promise<void>;\n\n    /**\n     * Use the Promise-based API instead.\n     * @method PIXI.BasePrepare#upload\n     * @deprecated since version 6.5.0\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} item -\n     *        Item to upload.\n     * @param {Function} [done] - Callback when completed.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture, done?: () => void): void;\n\n    /**\n     * Use the Promise-based API instead.\n     * @method PIXI.BasePrepare#upload\n     * @deprecated since version 6.5.0\n     * @param {Function} [done] - Callback when completed.\n     */\n    upload(done?: () => void): void;\n\n    /** @ignore */\n    upload(\n        item?: IDisplayObjectExtended | Container | BaseTexture | Texture | (() => void),\n        done?: () => void): Promise<void>\n    {\n        if (typeof item === 'function')\n        {\n            done = item as () => void;\n            item = null;\n        }\n\n        // #if _DEBUG\n        if (done)\n        {\n            deprecation('6.5.0', 'BasePrepare.upload callback is deprecated, use the return Promise instead.');\n        }\n        // #endif\n\n        return new Promise((resolve) =>\n        {\n            // If a display object, search for items\n            // that we could upload\n            if (item)\n            {\n                this.add(item as IDisplayObjectExtended | Container | BaseTexture | Texture);\n            }\n\n            // TODO: remove done callback and just use resolve\n            const complete = () =>\n            {\n                done?.();\n                resolve();\n            };\n\n            // Get the items for upload from the display\n            if (this.queue.length)\n            {\n                this.completes.push(complete);\n\n                if (!this.ticking)\n                {\n                    this.ticking = true;\n                    Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n                }\n            }\n            else\n            {\n                complete();\n            }\n        });\n    }\n\n    /**\n     * Handle tick update\n     * @private\n     */\n    tick(): void\n    {\n        setTimeout(this.delayedTick, 0);\n    }\n\n    /**\n     * Actually prepare items. This is handled outside of the tick because it will take a while\n     * and we do NOT want to block the current animation frame from rendering.\n     * @private\n     */\n    prepareItems(): void\n    {\n        this.limiter.beginFrame();\n        // Upload the graphics\n        while (this.queue.length && this.limiter.allowedToUpload())\n        {\n            const item = this.queue[0];\n            let uploaded = false;\n\n            if (item && !item._destroyed)\n            {\n                for (let i = 0, len = this.uploadHooks.length; i < len; i++)\n                {\n                    if (this.uploadHooks[i](this.uploadHookHelper, item))\n                    {\n                        this.queue.shift();\n                        uploaded = true;\n                        break;\n                    }\n                }\n            }\n\n            if (!uploaded)\n            {\n                this.queue.shift();\n            }\n        }\n\n        // We're finished\n        if (!this.queue.length)\n        {\n            this.ticking = false;\n\n            const completes = this.completes.slice(0);\n\n            this.completes.length = 0;\n\n            for (let i = 0, len = completes.length; i < len; i++)\n            {\n                completes[i]();\n            }\n        }\n        else\n        {\n            // if we are not finished, on the next rAF do this again\n            Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n        }\n    }\n\n    /**\n     * Adds hooks for finding items.\n     * @param {Function} addHook - Function call that takes two parameters: `item:*, queue:Array`\n     *          function must return `true` if it was able to add item to the queue.\n     * @returns Instance of plugin for chaining.\n     */\n    registerFindHook(addHook: IFindHook): this\n    {\n        if (addHook)\n        {\n            this.addHooks.push(addHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Adds hooks for uploading items.\n     * @param {Function} uploadHook - Function call that takes two parameters: `prepare:CanvasPrepare, item:*` and\n     *          function must return `true` if it was able to handle upload of item.\n     * @returns Instance of plugin for chaining.\n     */\n    registerUploadHook(uploadHook: IUploadHook): this\n    {\n        if (uploadHook)\n        {\n            this.uploadHooks.push(uploadHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Manually add an item to the uploading queue.\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text|*} item - Object to\n     *        add to the queue\n     * @returns Instance of plugin for chaining.\n     */\n    add(item: IDisplayObjectExtended | Container | BaseTexture | Texture): this\n    {\n        // Add additional hooks for finding elements on special\n        // types of objects that\n        for (let i = 0, len = this.addHooks.length; i < len; i++)\n        {\n            if (this.addHooks[i](item, this.queue))\n            {\n                break;\n            }\n        }\n\n        // Get children recursively\n        if (item instanceof Container)\n        {\n            for (let i = item.children.length - 1; i >= 0; i--)\n            {\n                this.add(item.children[i]);\n            }\n        }\n\n        return this;\n    }\n\n    /** Destroys the plugin, don't use after this. */\n    destroy(): void\n    {\n        if (this.ticking)\n        {\n            Ticker.system.remove(this.tick, this);\n        }\n        this.ticking = false;\n        this.addHooks = null;\n        this.uploadHooks = null;\n        this.renderer = null;\n        this.completes = null;\n        this.queue = null;\n        this.limiter = null;\n        this.uploadHookHelper = null;\n    }\n}\n", "import { BaseTexture, ExtensionType } from '@pixi/core';\nimport { Graphics } from '@pixi/graphics';\nimport type { IDisplayObjectExtended } from './BasePrepare';\nimport { BasePrepare } from './BasePrepare';\n\nimport type { Abstract<PERSON><PERSON><PERSON>, Renderer, ExtensionMetadata } from '@pixi/core';\n\n/**\n * Built-in hook to upload PIXI.Texture objects to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadBaseTextures(renderer: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended | BaseTexture): boolean\n{\n    if (item instanceof BaseTexture)\n    {\n        // if the texture already has a GL texture, then the texture has been prepared or rendered\n        // before now. If the texture changed, then the changer should be calling texture.update() which\n        // reuploads the texture without need for preparing it again\n        if (!item._glTextures[(renderer as Renderer).CONTEXT_UID])\n        {\n            (renderer as Renderer).texture.bind(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to upload PIXI.Graphics to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadGraphics(renderer: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (!(item instanceof Graphics))\n    {\n        return false;\n    }\n\n    const { geometry } = item;\n\n    // update dirty graphics to get batches\n    item.finishPoly();\n    geometry.updateBatches();\n\n    const { batches } = geometry;\n\n    // upload all textures found in styles\n    for (let i = 0; i < batches.length; i++)\n    {\n        const { texture } = batches[i].style;\n\n        if (texture)\n        {\n            uploadBaseTextures(renderer, texture.baseTexture);\n        }\n    }\n\n    // if its not batchable - update vao for particular shader\n    if (!geometry.batchable)\n    {\n        (renderer as Renderer).geometry.bind(geometry, (item as any)._resolveDirectShader((renderer as Renderer)));\n    }\n\n    return true;\n}\n\n/**\n * Built-in hook to find graphics.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Graphics object was found.\n */\nfunction findGraphics(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Graphics)\n    {\n        queue.push(item);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare plugin provides renderer-specific plugins for pre-rendering DisplayObjects. These plugins are useful for\n * asynchronously preparing and uploading to the GPU assets, textures, graphics waiting to be displayed.\n *\n * Do not instantiate this plugin directly. It is available from the `renderer.plugins` property.\n * See {@link PIXI.CanvasRenderer#plugins} or {@link PIXI.Renderer#plugins}.\n * @example\n * // Create a new application\n * const app = new PIXI.Application();\n * document.body.appendChild(app.view);\n *\n * // Don't start rendering right away\n * app.stop();\n *\n * // create a display object\n * const rect = new PIXI.Graphics()\n *     .beginFill(0x00ff00)\n *     .drawRect(40, 40, 200, 200);\n *\n * // Add to the stage\n * app.stage.addChild(rect);\n *\n * // Don't start rendering until the graphic is uploaded to the GPU\n * app.renderer.plugins.prepare.upload(app.stage, () => {\n *     app.start();\n * });\n * @memberof PIXI\n */\nexport class Prepare extends BasePrepare\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'prepare',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /**\n     * @param {PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        this.uploadHookHelper = this.renderer;\n\n        // Add textures and graphics to upload\n        this.registerFindHook(findGraphics);\n        this.registerUploadHook(uploadBaseTextures);\n        this.registerUploadHook(uploadGraphics);\n    }\n}\n", "/**\n * TimeLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of milliseconds per frame.\n * @memberof PIXI\n */\nexport class TimeLimiter\n{\n    /** The maximum milliseconds that can be spent preparing items each frame. */\n    public maxMilliseconds: number;\n\n    /**\n     * The start time of the current frame.\n     * @readonly\n     */\n    public frameStart: number;\n\n    /** @param maxMilliseconds - The maximum milliseconds that can be spent preparing items each frame. */\n    constructor(maxMilliseconds: number)\n    {\n        this.maxMilliseconds = maxMilliseconds;\n        this.frameStart = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.frameStart = Date.now();\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns - If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return Date.now() - this.frameStart < this.maxMilliseconds;\n    }\n}\n"], "names": ["settings", "arguments", "Texture", "BaseTexture", "Text", "TextStyle", "TextMetrics", "deprecation", "Ticker", "UPDATE_PRIORITY", "Container", "Graphics", "ExtensionType"], "mappings": ";;;;;;;;;;;IAEA;;;;;;;IAOG;AACHA,qBAAQ,CAAC,iBAAiB,GAAG,CAAC;;ICV9B;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGC,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;ICzNA;;;;IAIG;AACH,QAAA,YAAA,kBAAA,YAAA;IAQI;;IAEG;IACH,IAAA,SAAA,YAAA,CAAY,gBAAwB,EAAA;IAEhC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IACzC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;SACtB;;IAGD,IAAA,YAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;IAEI,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;SAC1C,CAAA;IAED;;;IAGG;IACH,IAAA,YAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;IAEI,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;SAC/B,CAAA;QACL,OAAC,YAAA,CAAA;IAAD,CAAC,EAAA;;ICJD;;;;;;IAMG;IACH,SAAS,wBAAwB,CAAC,IAA4B,EAAE,KAAiB,EAAA;QAE7E,IAAI,MAAM,GAAG,KAAK,CAAC;;QAGnB,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EACnD;IACI,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAC9C;gBACI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,YAAYC,YAAO,EACxC;oBACI,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;oBAElD,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EACrC;IACI,oBAAA,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBACxB,MAAM,GAAG,IAAI,CAAC;IACjB,iBAAA;IACJ,aAAA;IACJ,SAAA;IACJ,KAAA;IAED,IAAA,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,eAAe,CAAC,IAAa,EAAE,KAAiB,EAAA;IAErD,IAAA,IAAI,IAAI,CAAC,WAAW,YAAYC,gBAAW,EAC3C;IACI,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;YAEjC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjC;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,WAAW,CAAC,IAA4B,EAAE,KAAiB,EAAA;QAEhE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,YAAYD,YAAO,EACrD;IACI,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAE1C,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjC;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,QAAQ,CAAC,OAAuC,EAAE,IAA4B,EAAA;QAEnF,IAAI,IAAI,YAAYE,SAAI,EACxB;;IAEI,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtB,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,kBAAkB,CAAC,OAAuC,EAAE,IAA4B,EAAA;QAE7F,IAAI,IAAI,YAAYC,cAAS,EAC7B;IACI,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;IAEjC,QAAAC,gBAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAE9B,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,QAAQ,CAAC,IAA4B,EAAE,KAAiB,EAAA;QAE7D,IAAI,IAAI,YAAYF,SAAI,EACxB;;YAEI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EACpC;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,SAAA;;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC9B;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,SAAA;;IAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAE1C,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjC;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,aAAa,CAAC,IAAe,EAAE,KAAiB,EAAA;QAErD,IAAI,IAAI,YAAYC,cAAS,EAC7B;YACI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC9B;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;IAmBG;AACH,QAAA,WAAA,kBAAA,YAAA;IAkDI;;IAEG;IACH,IAAA,SAAA,WAAA,CAAY,QAA0B,EAAA;YAAtC,IA8BC,KAAA,GAAA,IAAA,CAAA;YA5BG,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAACL,iBAAQ,CAAC,iBAAiB,CAAC,CAAC;IAC5D,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC7B,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAChB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACnB,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACtB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,WAAW,GAAG,YAAA;;IAGf,YAAA,IAAI,CAAC,KAAI,CAAC,KAAK,EACf;oBACI,OAAO;IACV,aAAA;gBACD,KAAI,CAAC,YAAY,EAAE,CAAC;IACxB,SAAC,CAAC;;IAGF,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;IACrC,QAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;IAChD,QAAA,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACvC,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;;IAGnC,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAClC,QAAA,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;SAC/C;;IA8BD,IAAA,WAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UACI,IAAgF,EAChF,IAAiB,EAAA;YAFrB,IAiDC,KAAA,GAAA,IAAA,CAAA;IA7CG,QAAA,IAAI,OAAO,IAAI,KAAK,UAAU,EAC9B;gBACI,IAAI,GAAG,IAAkB,CAAC;gBAC1B,IAAI,GAAG,IAAI,CAAC;IACf,SAAA;IAGD,QAAA,IAAI,IAAI,EACR;IACI,YAAAO,iBAAW,CAAC,OAAO,EAAE,4EAA4E,CAAC,CAAC;IACtG,SAAA;IAGD,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAA;;;IAIvB,YAAA,IAAI,IAAI,EACR;IACI,gBAAA,KAAI,CAAC,GAAG,CAAC,IAAkE,CAAC,CAAC;IAChF,aAAA;;IAGD,YAAA,IAAM,QAAQ,GAAG,YAAA;IAEb,gBAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,EAAI,CAAC;IACT,gBAAA,OAAO,EAAE,CAAC;IACd,aAAC,CAAC;;IAGF,YAAA,IAAI,KAAI,CAAC,KAAK,CAAC,MAAM,EACrB;IACI,gBAAA,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAE9B,gBAAA,IAAI,CAAC,KAAI,CAAC,OAAO,EACjB;IACI,oBAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,oBAAAC,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAI,CAAC,IAAI,EAAE,KAAI,EAAEC,sBAAe,CAAC,OAAO,CAAC,CAAC;IACnE,iBAAA;IACJ,aAAA;IAED,iBAAA;IACI,gBAAA,QAAQ,EAAE,CAAC;IACd,aAAA;IACL,SAAC,CAAC,CAAC;SACN,CAAA;IAED;;;IAGG;IACH,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;IAEI,QAAA,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;SACnC,CAAA;IAED;;;;IAIG;IACH,IAAA,WAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;IAEI,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;;IAE1B,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,EAC1D;gBACI,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,YAAA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAC5B;IACI,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAC3D;IACI,oBAAA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,EACpD;IACI,wBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;4BACnB,QAAQ,GAAG,IAAI,CAAC;4BAChB,MAAM;IACT,qBAAA;IACJ,iBAAA;IACJ,aAAA;gBAED,IAAI,CAAC,QAAQ,EACb;IACI,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACtB,aAAA;IACJ,SAAA;;IAGD,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EACtB;IACI,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBAErB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE1C,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAE1B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EACpD;IACI,gBAAA,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB,aAAA;IACJ,SAAA;IAED,aAAA;;IAEI,YAAAD,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEC,sBAAe,CAAC,OAAO,CAAC,CAAC;IACnE,SAAA;SACJ,CAAA;IAED;;;;;IAKG;QACH,WAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,OAAkB,EAAA;IAE/B,QAAA,IAAI,OAAO,EACX;IACI,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;IAKG;QACH,WAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,UAAuB,EAAA;IAEtC,QAAA,IAAI,UAAU,EACd;IACI,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACrC,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;IAKG;QACH,WAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,IAAgE,EAAA;;;IAIhE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EACxD;IACI,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EACtC;oBACI,MAAM;IACT,aAAA;IACJ,SAAA;;YAGD,IAAI,IAAI,YAAYC,iBAAS,EAC7B;IACI,YAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAClD;oBACI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;;IAGD,IAAA,WAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YAEI,IAAI,IAAI,CAAC,OAAO,EAChB;gBACIF,aAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACzC,SAAA;IACD,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC,CAAA;QACL,OAAC,WAAA,CAAA;IAAD,CAAC,EAAA;;IChhBD;;;;;;IAMG;IACH,SAAS,kBAAkB,CAAC,QAAwC,EAAE,IAA0C,EAAA;QAE5G,IAAI,IAAI,YAAYL,gBAAW,EAC/B;;;;YAII,IAAI,CAAC,IAAI,CAAC,WAAW,CAAE,QAAqB,CAAC,WAAW,CAAC,EACzD;IACK,YAAA,QAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,cAAc,CAAC,QAAwC,EAAE,IAA4B,EAAA;IAE1F,IAAA,IAAI,EAAE,IAAI,YAAYQ,iBAAQ,CAAC,EAC/B;IACI,QAAA,OAAO,KAAK,CAAC;IAChB,KAAA;IAEO,IAAA,IAAA,QAAQ,GAAK,IAAI,CAAA,QAAT,CAAU;;QAG1B,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,QAAQ,CAAC,aAAa,EAAE,CAAC;IAEjB,IAAA,IAAA,OAAO,GAAK,QAAQ,CAAA,OAAb,CAAc;;IAG7B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EACvC;YACY,IAAA,OAAO,GAAK,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA,OAArB,CAAsB;IAErC,QAAA,IAAI,OAAO,EACX;IACI,YAAA,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;IACrD,SAAA;IACJ,KAAA;;IAGD,IAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB;IACK,QAAA,QAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAG,IAAY,CAAC,oBAAoB,CAAE,QAAqB,CAAC,CAAC,CAAC;IAC9G,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;IAMG;IACH,SAAS,YAAY,CAAC,IAA4B,EAAE,KAAiB,EAAA;QAEjE,IAAI,IAAI,YAAYA,iBAAQ,EAC5B;IACI,QAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,QAAA,OAAO,IAAI,CAAC;IACf,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BG;AACH,QAAA,OAAA,kBAAA,UAAA,MAAA,EAAA;QAA6B,SAAW,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;IAQpC;;IAEG;IACH,IAAA,SAAA,OAAA,CAAY,QAAkB,EAAA;YAA9B,IAEI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,QAAQ,CAAC,IAQlB,IAAA,CAAA;IANG,QAAA,KAAI,CAAC,gBAAgB,GAAG,KAAI,CAAC,QAAQ,CAAC;;IAGtC,QAAA,KAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;IACpC,QAAA,KAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;IAC5C,QAAA,KAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;;SAC3C;;IAlBM,IAAA,OAAA,CAAA,SAAS,GAAsB;IAClC,QAAA,IAAI,EAAE,SAAS;YACf,IAAI,EAAEC,kBAAa,CAAC,cAAc;SACrC,CAAC;QAgBN,OAAC,OAAA,CAAA;KAAA,CAtB4B,WAAW,CAsBvC;;IC/ID;;;;IAIG;AACH,QAAA,WAAA,kBAAA,YAAA;;IAYI,IAAA,SAAA,WAAA,CAAY,eAAuB,EAAA;IAE/B,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACvC,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;SACvB;;IAGD,IAAA,WAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;IAEI,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SAChC,CAAA;IAED;;;IAGG;IACH,IAAA,WAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;IAEI,QAAA,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;SAC9D,CAAA;QACL,OAAC,WAAA,CAAA;IAAD,CAAC,EAAA;;;;;;;;;;;;;;;;;;"}