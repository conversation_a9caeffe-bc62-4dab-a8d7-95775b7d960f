{"version": 3, "file": "math.js", "sources": ["../../src/const.ts", "../../src/Point.ts", "../../src/shapes/Rectangle.ts", "../../src/shapes/Circle.ts", "../../src/shapes/Ellipse.ts", "../../src/shapes/Polygon.ts", "../../src/shapes/RoundedRectangle.ts", "../../src/ObservablePoint.ts", "../../src/Matrix.ts", "../../src/groupD8.ts", "../../src/Transform.ts"], "sourcesContent": ["/**\n * Two Pi.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const PI_2 = Math.PI * 2;\n\n/**\n * Conversion factor for converting radians to degrees.\n * @static\n * @member {number} RAD_TO_DEG\n * @memberof PIXI\n */\nexport const RAD_TO_DEG = 180 / Math.PI;\n\n/**\n * Conversion factor for converting degrees to radians.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const DEG_TO_RAD = Math.PI / 180;\n\n/**\n * Constants that identify shapes, mainly to prevent `instanceof` calls.\n * @static\n * @memberof PIXI\n * @enum {number}\n * @property {number} POLY Polygon\n * @property {number} RECT Rectangle\n * @property {number} CIRC Circle\n * @property {number} ELIP Ellipse\n * @property {number} RREC Rounded Rectangle\n */\nexport enum SHAPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    POLY = 0,\n    RECT = 1,\n    CIRC = 2,\n    ELIP = 3,\n    RREC = 4,\n}\n", "import type { IPoint } from './IPoint';\nimport type { IPointData } from './IPointData';\n\nexport interface Point extends GlobalMixins.Point, IPoint {}\n\n/**\n * The Point object represents a location in a two-dimensional coordinate system, where `x` represents\n * the position on the horizontal axis and `y` represents the position on the vertical axis\n * @class\n * @memberof PIXI\n * @implements {IPoint}\n */\nexport class Point implements IPoint\n{\n    /** Position of the point on the x axis */\n    public x = 0;\n    /** Position of the point on the y axis */\n    public y = 0;\n\n    /**\n     * Creates a new `Point`\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=0] - position of the point on the y axis\n     */\n    constructor(x = 0, y = 0)\n    {\n        this.x = x;\n        this.y = y;\n    }\n\n    /**\n     * Creates a clone of this point\n     * @returns A clone of this point\n     */\n    clone(): Point\n    {\n        return new Point(this.x, this.y);\n    }\n\n    /**\n     * Copies `x` and `y` from the given point into this point\n     * @param p - The point to copy from\n     * @returns The point instance itself\n     */\n    copyFrom(p: IPointData): this\n    {\n        this.set(p.x, p.y);\n\n        return this;\n    }\n\n    /**\n     * Copies this point's x and y into the given point (`p`).\n     * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n     * @returns The point (`p`) with values updated\n     */\n    copyTo<T extends IPoint>(p: T): T\n    {\n        p.set(this.x, this.y);\n\n        return p;\n    }\n\n    /**\n     * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n     * @param p - The point to check\n     * @returns Returns `true` if both `x` and `y` are equal\n     */\n    equals(p: IPointData): boolean\n    {\n        return (p.x === this.x) && (p.y === this.y);\n    }\n\n    /**\n     * Sets the point to a new `x` and `y` position.\n     * If `y` is omitted, both `x` and `y` will be set to `x`.\n     * @param {number} [x=0] - position of the point on the `x` axis\n     * @param {number} [y=x] - position of the point on the `y` axis\n     * @returns The point instance itself\n     */\n    set(x = 0, y = x): this\n    {\n        this.x = x;\n        this.y = y;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Point x=${this.x} y=${this.y}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\nimport type { Matrix } from '../Matrix';\nimport { Point } from '../Point';\n\nconst tempPoints = [new Point(), new Point(), new Point(), new Point()];\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Rectangle extends GlobalMixins.Rectangle {}\n\n/**\n * Size object, contains width and height\n * @memberof PIXI\n * @typedef {object} ISize\n * @property {number} width - Width component\n * @property {number} height - Height component\n */\n\n/**\n * Rectangle object is an area defined by its position, as indicated by its top-left corner\n * point (x, y) and by its width and its height.\n * @memberof PIXI\n */\nexport class Rectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RECT\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RECT;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rectangle\n     * @param width - The overall width of the rectangle\n     * @param height - The overall height of the rectangle\n     */\n    constructor(x: string | number = 0, y: string | number = 0, width: string | number = 0, height: string | number = 0)\n    {\n        this.x = Number(x);\n        this.y = Number(y);\n        this.width = Number(width);\n        this.height = Number(height);\n        this.type = SHAPES.RECT;\n    }\n\n    /** Returns the left edge of the rectangle. */\n    get left(): number\n    {\n        return this.x;\n    }\n\n    /** Returns the right edge of the rectangle. */\n    get right(): number\n    {\n        return this.x + this.width;\n    }\n\n    /** Returns the top edge of the rectangle. */\n    get top(): number\n    {\n        return this.y;\n    }\n\n    /** Returns the bottom edge of the rectangle. */\n    get bottom(): number\n    {\n        return this.y + this.height;\n    }\n\n    /** A constant empty rectangle. */\n    static get EMPTY(): Rectangle\n    {\n        return new Rectangle(0, 0, 0, 0);\n    }\n\n    /**\n     * Creates a clone of this Rectangle\n     * @returns a copy of the rectangle\n     */\n    clone(): Rectangle\n    {\n        return new Rectangle(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Copies another rectangle to this one.\n     * @param rectangle - The rectangle to copy from.\n     * @returns Returns itself.\n     */\n    copyFrom(rectangle: Rectangle): Rectangle\n    {\n        this.x = rectangle.x;\n        this.y = rectangle.y;\n        this.width = rectangle.width;\n        this.height = rectangle.height;\n\n        return this;\n    }\n\n    /**\n     * Copies this rectangle to another one.\n     * @param rectangle - The rectangle to copy to.\n     * @returns Returns given parameter.\n     */\n    copyTo(rectangle: Rectangle): Rectangle\n    {\n        rectangle.x = this.x;\n        rectangle.y = this.y;\n        rectangle.width = this.width;\n        rectangle.height = this.height;\n\n        return rectangle;\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rectangle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Rectangle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        if (x >= this.x && x < this.x + this.width)\n        {\n            if (y >= this.y && y < this.y + this.height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Determines whether the `other` Rectangle transformed by `transform` intersects with `this` Rectangle object.\n     * Returns true only if the area of the intersection is >0, this means that Rectangles\n     * sharing a side are not overlapping. Another side effect is that an arealess rectangle\n     * (width or height equal to zero) can't intersect any other rectangle.\n     * @param {Rectangle} other - The Rectangle to intersect with `this`.\n     * @param {Matrix} transform - The transformation matrix of `other`.\n     * @returns {boolean} A value of `true` if the transformed `other` Rectangle intersects with `this`; otherwise `false`.\n     */\n    intersects(other: Rectangle, transform?: Matrix): boolean\n    {\n        if (!transform)\n        {\n            const x0 = this.x < other.x ? other.x : this.x;\n            const x1 = this.right > other.right ? other.right : this.right;\n\n            if (x1 <= x0)\n            {\n                return false;\n            }\n\n            const y0 = this.y < other.y ? other.y : this.y;\n            const y1 = this.bottom > other.bottom ? other.bottom : this.bottom;\n\n            return y1 > y0;\n        }\n\n        const x0 = this.left;\n        const x1 = this.right;\n        const y0 = this.top;\n        const y1 = this.bottom;\n\n        if (x1 <= x0 || y1 <= y0)\n        {\n            return false;\n        }\n\n        const lt = tempPoints[0].set(other.left, other.top);\n        const lb = tempPoints[1].set(other.left, other.bottom);\n        const rt = tempPoints[2].set(other.right, other.top);\n        const rb = tempPoints[3].set(other.right, other.bottom);\n\n        if (rt.x <= lt.x || lb.y <= lt.y)\n        {\n            return false;\n        }\n\n        const s = Math.sign((transform.a * transform.d) - (transform.b * transform.c));\n\n        if (s === 0)\n        {\n            return false;\n        }\n\n        transform.apply(lt, lt);\n        transform.apply(lb, lb);\n        transform.apply(rt, rt);\n        transform.apply(rb, rb);\n\n        if (Math.max(lt.x, lb.x, rt.x, rb.x) <= x0\n            || Math.min(lt.x, lb.x, rt.x, rb.x) >= x1\n            || Math.max(lt.y, lb.y, rt.y, rb.y) <= y0\n            || Math.min(lt.y, lb.y, rt.y, rb.y) >= y1)\n        {\n            return false;\n        }\n\n        const nx = s * (lb.y - lt.y);\n        const ny = s * (lt.x - lb.x);\n        const n00 = (nx * x0) + (ny * y0);\n        const n10 = (nx * x1) + (ny * y0);\n        const n01 = (nx * x0) + (ny * y1);\n        const n11 = (nx * x1) + (ny * y1);\n\n        if (Math.max(n00, n10, n01, n11) <= (nx * lt.x) + (ny * lt.y)\n            || Math.min(n00, n10, n01, n11) >= (nx * rb.x) + (ny * rb.y))\n        {\n            return false;\n        }\n\n        const mx = s * (lt.y - rt.y);\n        const my = s * (rt.x - lt.x);\n        const m00 = (mx * x0) + (my * y0);\n        const m10 = (mx * x1) + (my * y0);\n        const m01 = (mx * x0) + (my * y1);\n        const m11 = (mx * x1) + (my * y1);\n\n        if (Math.max(m00, m10, m01, m11) <= (mx * lt.x) + (my * lt.y)\n            || Math.min(m00, m10, m01, m11) >= (mx * rb.x) + (my * rb.y))\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * Pads the rectangle making it grow in all directions.\n     * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n     * @param paddingX - The horizontal padding amount.\n     * @param paddingY - The vertical padding amount.\n     * @returns Returns itself.\n     */\n    pad(paddingX = 0, paddingY = paddingX): this\n    {\n        this.x -= paddingX;\n        this.y -= paddingY;\n\n        this.width += paddingX * 2;\n        this.height += paddingY * 2;\n\n        return this;\n    }\n\n    /**\n     * Fits this rectangle around the passed one.\n     * @param rectangle - The rectangle to fit.\n     * @returns Returns itself.\n     */\n    fit(rectangle: Rectangle): this\n    {\n        const x1 = Math.max(this.x, rectangle.x);\n        const x2 = Math.min(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.max(this.y, rectangle.y);\n        const y2 = Math.min(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = Math.max(x2 - x1, 0);\n        this.y = y1;\n        this.height = Math.max(y2 - y1, 0);\n\n        return this;\n    }\n\n    /**\n     * Enlarges rectangle that way its corners lie on grid\n     * @param resolution - resolution\n     * @param eps - precision\n     * @returns Returns itself.\n     */\n    ceil(resolution = 1, eps = 0.001): this\n    {\n        const x2 = Math.ceil((this.x + this.width - eps) * resolution) / resolution;\n        const y2 = Math.ceil((this.y + this.height - eps) * resolution) / resolution;\n\n        this.x = Math.floor((this.x + eps) * resolution) / resolution;\n        this.y = Math.floor((this.y + eps) * resolution) / resolution;\n\n        this.width = x2 - this.x;\n        this.height = y2 - this.y;\n\n        return this;\n    }\n\n    /**\n     * Enlarges this rectangle to include the passed rectangle.\n     * @param rectangle - The rectangle to include.\n     * @returns Returns itself.\n     */\n    enlarge(rectangle: Rectangle): this\n    {\n        const x1 = Math.min(this.x, rectangle.x);\n        const x2 = Math.max(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.min(this.y, rectangle.y);\n        const y2 = Math.max(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = x2 - x1;\n        this.y = y1;\n        this.height = y2 - y1;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from './../const';\nimport { Rectangle } from './Rectangle';\n\n/**\n * The Circle object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Circle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.CIRC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.CIRC;\n\n    /**\n     * @param x - The X coordinate of the center of this circle\n     * @param y - The Y coordinate of the center of this circle\n     * @param radius - The radius of the circle\n     */\n    constructor(x = 0, y = 0, radius = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.radius = radius;\n\n        this.type = SHAPES.CIRC;\n    }\n\n    /**\n     * Creates a clone of this Circle instance\n     * @returns A copy of the Circle\n     */\n    clone(): Circle\n    {\n        return new Circle(this.x, this.y, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this circle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Circle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.radius <= 0)\n        {\n            return false;\n        }\n\n        const r2 = this.radius * this.radius;\n        let dx = (this.x - x);\n        let dy = (this.y - y);\n\n        dx *= dx;\n        dy *= dy;\n\n        return (dx + dy <= r2);\n    }\n\n    /**\n     * Returns the framing rectangle of the circle as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.radius, this.y - this.radius, this.radius * 2, this.radius * 2);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`;\n    }\n    // #endif\n}\n", "import { Rectangle } from './Rectangle';\nimport { SHAPES } from '../const';\n\n/**\n * The Ellipse object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Ellipse\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.ELIP\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.ELIP;\n\n    /**\n     * @param x - The X coordinate of the center of this ellipse\n     * @param y - The Y coordinate of the center of this ellipse\n     * @param halfWidth - The half width of this ellipse\n     * @param halfHeight - The half height of this ellipse\n     */\n    constructor(x = 0, y = 0, halfWidth = 0, halfHeight = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = halfWidth;\n        this.height = halfHeight;\n\n        this.type = SHAPES.ELIP;\n    }\n\n    /**\n     * Creates a clone of this Ellipse instance\n     * @returns {PIXI.Ellipse} A copy of the ellipse\n     */\n    clone(): Ellipse\n    {\n        return new Ellipse(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this ellipse\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coords are within this ellipse\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        // normalize the coords to an ellipse with center 0,0\n        let normx = ((x - this.x) / this.width);\n        let normy = ((y - this.y) / this.height);\n\n        normx *= normx;\n        normy *= normy;\n\n        return (normx + normy <= 1);\n    }\n\n    /**\n     * Returns the framing rectangle of the ellipse as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.width, this.y - this.height, this.width, this.height);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Ellipse x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\nimport type { IPointData } from '../IPointData';\n\n/**\n * A class to define a shape via user defined coordinates.\n * @memberof PIXI\n */\nexport class Polygon\n{\n    /** An array of the points of this polygon. */\n    public points: number[];\n\n    /** `false` after moveTo, `true` after `closePath`. In all other cases it is `true`. */\n    public closeStroke: boolean;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.POLY\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.POLY;\n\n    constructor(points: IPointData[] | number[]);\n    constructor(...points: IPointData[] | number[]);\n\n    /**\n     * @param {PIXI.IPointData[]|number[]} points - This can be an array of Points\n     *  that form the polygon, a flat array of numbers that will be interpreted as [x,y, x,y, ...], or\n     *  the arguments passed can be all the points of the polygon e.g.\n     *  `new PIXI.Polygon(new PIXI.Point(), new PIXI.Point(), ...)`, or the arguments passed can be flat\n     *  x,y values e.g. `new Polygon(x,y, x,y, x,y, ...)` where `x` and `y` are Numbers.\n     */\n    constructor(...points: any[])\n    {\n        let flat: IPointData[] | number[] = Array.isArray(points[0]) ? points[0] : points;\n\n        // if this is an array of points, convert it to a flat array of numbers\n        if (typeof flat[0] !== 'number')\n        {\n            const p: number[] = [];\n\n            for (let i = 0, il = flat.length; i < il; i++)\n            {\n                p.push((flat[i] as IPointData).x, (flat[i] as IPointData).y);\n            }\n\n            flat = p;\n        }\n\n        this.points = flat as number[];\n        this.type = SHAPES.POLY;\n        this.closeStroke = true;\n    }\n\n    /**\n     * Creates a clone of this polygon.\n     * @returns - A copy of the polygon.\n     */\n    clone(): Polygon\n    {\n        const points = this.points.slice();\n        const polygon = new Polygon(points);\n\n        polygon.closeStroke = this.closeStroke;\n\n        return polygon;\n    }\n\n    /**\n     * Checks whether the x and y coordinates passed to this function are contained within this polygon.\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this polygon.\n     */\n    contains(x: number, y: number): boolean\n    {\n        let inside = false;\n\n        // use some raycasting to test hits\n        // https://github.com/substack/point-in-polygon/blob/master/index.js\n        const length = this.points.length / 2;\n\n        for (let i = 0, j = length - 1; i < length; j = i++)\n        {\n            const xi = this.points[i * 2];\n            const yi = this.points[(i * 2) + 1];\n            const xj = this.points[j * 2];\n            const yj = this.points[(j * 2) + 1];\n            const intersect = ((yi > y) !== (yj > y)) && (x < ((xj - xi) * ((y - yi) / (yj - yi))) + xi);\n\n            if (intersect)\n            {\n                inside = !inside;\n            }\n        }\n\n        return inside;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Polygon`\n            + `closeStroke=${this.closeStroke}`\n            + `points=${this.points.reduce((pointsDesc, currentPoint) => `${pointsDesc}, ${currentPoint}`, '')}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\n\n/**\n * The Rounded Rectangle object is an area that has nice rounded corners, as indicated by its\n * top-left corner point (x, y) and by its width and its height and its radius.\n * @memberof PIXI\n */\nexport class RoundedRectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /** @default 20 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RREC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RREC;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rounded rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rounded rectangle\n     * @param width - The overall width of this rounded rectangle\n     * @param height - The overall height of this rounded rectangle\n     * @param radius - Controls the radius of the rounded corners\n     */\n    constructor(x = 0, y = 0, width = 0, height = 0, radius = 20)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n        this.radius = radius;\n        this.type = SHAPES.RREC;\n    }\n\n    /**\n     * Creates a clone of this Rounded Rectangle.\n     * @returns - A copy of the rounded rectangle.\n     */\n    clone(): RoundedRectangle\n    {\n        return new RoundedRectangle(this.x, this.y, this.width, this.height, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rounded Rectangle\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this Rounded Rectangle.\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n        if (x >= this.x && x <= this.x + this.width)\n        {\n            if (y >= this.y && y <= this.y + this.height)\n            {\n                const radius = Math.max(0, Math.min(this.radius, Math.min(this.width, this.height) / 2));\n\n                if ((y >= this.y + radius && y <= this.y + this.height - radius)\n                || (x >= this.x + radius && x <= this.x + this.width - radius))\n                {\n                    return true;\n                }\n                let dx = x - (this.x + radius);\n                let dy = y - (this.y + radius);\n                const radius2 = radius * radius;\n\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + this.width - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dy = y - (this.y + this.height - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:RoundedRectangle x=${this.x} y=${this.y}`\n            + `width=${this.width} height=${this.height} radius=${this.radius}]`;\n    }\n    // #endif\n}\n", "import type { IPointData } from './IPointData';\nimport type { IPoint } from './IPoint';\n\nexport interface ObservablePoint extends GlobalMixins.Point, IPoint {}\n\n/**\n * The ObservablePoint object represents a location in a two-dimensional coordinate system, where `x` represents\n * the position on the horizontal axis and `y` represents the position on the vertical axis.\n *\n * An `ObservablePoint` is a point that triggers a callback when the point's position is changed.\n * @memberof PIXI\n */\nexport class ObservablePoint<T = any> implements IPoint\n{\n    /** The callback function triggered when `x` and/or `y` are changed */\n    public cb: (this: T) => any;\n\n    /** The owner of the callback */\n    public scope: any;\n\n    _x: number;\n    _y: number;\n\n    /**\n     * Creates a new `ObservablePoint`\n     * @param cb - callback function triggered when `x` and/or `y` are changed\n     * @param scope - owner of callback\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=0] - position of the point on the y axis\n     */\n    constructor(cb: (this: T) => any, scope: T, x = 0, y = 0)\n    {\n        this._x = x;\n        this._y = y;\n\n        this.cb = cb;\n        this.scope = scope;\n    }\n\n    /**\n     * Creates a clone of this point.\n     * The callback and scope params can be overridden otherwise they will default\n     * to the clone object's values.\n     * @override\n     * @param cb - The callback function triggered when `x` and/or `y` are changed\n     * @param scope - The owner of the callback\n     * @returns a copy of this observable point\n     */\n    clone(cb = this.cb, scope = this.scope): ObservablePoint\n    {\n        return new ObservablePoint(cb, scope, this._x, this._y);\n    }\n\n    /**\n     * Sets the point to a new `x` and `y` position.\n     * If `y` is omitted, both `x` and `y` will be set to `x`.\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=x] - position of the point on the y axis\n     * @returns The observable point instance itself\n     */\n    set(x = 0, y = x): this\n    {\n        if (this._x !== x || this._y !== y)\n        {\n            this._x = x;\n            this._y = y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies x and y from the given point (`p`)\n     * @param p - The point to copy from. Can be any of type that is or extends `IPointData`\n     * @returns The observable point instance itself\n     */\n    copyFrom(p: IPointData): this\n    {\n        if (this._x !== p.x || this._y !== p.y)\n        {\n            this._x = p.x;\n            this._y = p.y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies this point's x and y into that of the given point (`p`)\n     * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n     * @returns The point (`p`) with values updated\n     */\n    copyTo<T extends IPoint>(p: T): T\n    {\n        p.set(this._x, this._y);\n\n        return p;\n    }\n\n    /**\n     * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n     * @param p - The point to check\n     * @returns Returns `true` if both `x` and `y` are equal\n     */\n    equals(p: IPointData): boolean\n    {\n        return (p.x === this._x) && (p.y === this._y);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:ObservablePoint x=${0} y=${0} scope=${this.scope}]`;\n    }\n    // #endif\n\n    /** Position of the observable point on the x axis. */\n    get x(): number\n    {\n        return this._x;\n    }\n\n    set x(value: number)\n    {\n        if (this._x !== value)\n        {\n            this._x = value;\n            this.cb.call(this.scope);\n        }\n    }\n\n    /** Position of the observable point on the y axis. */\n    get y(): number\n    {\n        return this._y;\n    }\n\n    set y(value: number)\n    {\n        if (this._y !== value)\n        {\n            this._y = value;\n            this.cb.call(this.scope);\n        }\n    }\n}\n", "import { Point } from './Point';\nimport { PI_2 } from './const';\n\nimport type { Transform } from './Transform';\nimport type { IPointData } from './IPointData';\n\n/**\n * The PixiJS Matrix as a class makes it a lot faster.\n *\n * Here is a representation of it:\n * ```js\n * | a | c | tx|\n * | b | d | ty|\n * | 0 | 0 | 1 |\n * ```\n * @memberof PIXI\n */\nexport class Matrix\n{\n    /** @default 1 */\n    public a: number;\n\n    /** @default 0 */\n    public b: number;\n\n    /** @default 0 */\n    public c: number;\n\n    /** @default 1 */\n    public d: number;\n\n    /** @default 0 */\n    public tx: number;\n\n    /** @default 0 */\n    public ty: number;\n\n    public array: Float32Array | null = null;\n\n    /**\n     * @param a - x scale\n     * @param b - y skew\n     * @param c - x skew\n     * @param d - y scale\n     * @param tx - x translation\n     * @param ty - y translation\n     */\n    constructor(a = 1, b = 0, c = 0, d = 1, tx = 0, ty = 0)\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n    }\n\n    /**\n     * Creates a Matrix object based on the given array. The Element to Matrix mapping order is as follows:\n     *\n     * a = array[0]\n     * b = array[1]\n     * c = array[3]\n     * d = array[4]\n     * tx = array[2]\n     * ty = array[5]\n     * @param array - The array that the matrix will be populated from.\n     */\n    fromArray(array: number[]): void\n    {\n        this.a = array[0];\n        this.b = array[1];\n        this.c = array[3];\n        this.d = array[4];\n        this.tx = array[2];\n        this.ty = array[5];\n    }\n\n    /**\n     * Sets the matrix properties.\n     * @param a - Matrix component\n     * @param b - Matrix component\n     * @param c - Matrix component\n     * @param d - Matrix component\n     * @param tx - Matrix component\n     * @param ty - Matrix component\n     * @returns This matrix. Good for chaining method calls.\n     */\n    set(a: number, b: number, c: number, d: number, tx: number, ty: number): this\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n\n        return this;\n    }\n\n    /**\n     * Creates an array from the current Matrix object.\n     * @param transpose - Whether we need to transpose the matrix or not\n     * @param [out=new Float32Array(9)] - If provided the array will be assigned to out\n     * @returns The newly created array which contains the matrix\n     */\n    toArray(transpose: boolean, out?: Float32Array): Float32Array\n    {\n        if (!this.array)\n        {\n            this.array = new Float32Array(9);\n        }\n\n        const array = out || this.array;\n\n        if (transpose)\n        {\n            array[0] = this.a;\n            array[1] = this.b;\n            array[2] = 0;\n            array[3] = this.c;\n            array[4] = this.d;\n            array[5] = 0;\n            array[6] = this.tx;\n            array[7] = this.ty;\n            array[8] = 1;\n        }\n        else\n        {\n            array[0] = this.a;\n            array[1] = this.c;\n            array[2] = this.tx;\n            array[3] = this.b;\n            array[4] = this.d;\n            array[5] = this.ty;\n            array[6] = 0;\n            array[7] = 0;\n            array[8] = 1;\n        }\n\n        return array;\n    }\n\n    /**\n     * Get a new position with the current transformation applied.\n     * Can be used to go from a child's coordinate space to the world coordinate space. (e.g. rendering)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, transformed through this matrix\n     */\n    apply<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.a * x) + (this.c * y) + this.tx;\n        newPos.y = (this.b * x) + (this.d * y) + this.ty;\n\n        return newPos;\n    }\n\n    /**\n     * Get a new position with the inverse of the current transformation applied.\n     * Can be used to go from the world coordinate space to a child's coordinate space. (e.g. input)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, inverse-transformed through this matrix\n     */\n    applyInverse<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const id = 1 / ((this.a * this.d) + (this.c * -this.b));\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.d * id * x) + (-this.c * id * y) + (((this.ty * this.c) - (this.tx * this.d)) * id);\n        newPos.y = (this.a * id * y) + (-this.b * id * x) + (((-this.ty * this.a) + (this.tx * this.b)) * id);\n\n        return newPos;\n    }\n\n    /**\n     * Translates the matrix on the x and y.\n     * @param x - How much to translate x by\n     * @param y - How much to translate y by\n     * @returns This matrix. Good for chaining method calls.\n     */\n    translate(x: number, y: number): this\n    {\n        this.tx += x;\n        this.ty += y;\n\n        return this;\n    }\n\n    /**\n     * Applies a scale transformation to the matrix.\n     * @param x - The amount to scale horizontally\n     * @param y - The amount to scale vertically\n     * @returns This matrix. Good for chaining method calls.\n     */\n    scale(x: number, y: number): this\n    {\n        this.a *= x;\n        this.d *= y;\n        this.c *= x;\n        this.b *= y;\n        this.tx *= x;\n        this.ty *= y;\n\n        return this;\n    }\n\n    /**\n     * Applies a rotation transformation to the matrix.\n     * @param angle - The angle in radians.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    rotate(angle: number): this\n    {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n\n        const a1 = this.a;\n        const c1 = this.c;\n        const tx1 = this.tx;\n\n        this.a = (a1 * cos) - (this.b * sin);\n        this.b = (a1 * sin) + (this.b * cos);\n        this.c = (c1 * cos) - (this.d * sin);\n        this.d = (c1 * sin) + (this.d * cos);\n        this.tx = (tx1 * cos) - (this.ty * sin);\n        this.ty = (tx1 * sin) + (this.ty * cos);\n\n        return this;\n    }\n\n    /**\n     * Appends the given Matrix to this Matrix.\n     * @param matrix - The matrix to append.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    append(matrix: Matrix): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n\n        this.a = (matrix.a * a1) + (matrix.b * c1);\n        this.b = (matrix.a * b1) + (matrix.b * d1);\n        this.c = (matrix.c * a1) + (matrix.d * c1);\n        this.d = (matrix.c * b1) + (matrix.d * d1);\n\n        this.tx = (matrix.tx * a1) + (matrix.ty * c1) + this.tx;\n        this.ty = (matrix.tx * b1) + (matrix.ty * d1) + this.ty;\n\n        return this;\n    }\n\n    /**\n     * Sets the matrix based on all the available properties\n     * @param x - Position on the x axis\n     * @param y - Position on the y axis\n     * @param pivotX - Pivot on the x axis\n     * @param pivotY - Pivot on the y axis\n     * @param scaleX - Scale on the x axis\n     * @param scaleY - Scale on the y axis\n     * @param rotation - Rotation in radians\n     * @param skewX - Skew on the x axis\n     * @param skewY - Skew on the y axis\n     * @returns This matrix. Good for chaining method calls.\n     */\n    setTransform(x: number, y: number, pivotX: number, pivotY: number, scaleX: number,\n        scaleY: number, rotation: number, skewX: number, skewY: number): this\n    {\n        this.a = Math.cos(rotation + skewY) * scaleX;\n        this.b = Math.sin(rotation + skewY) * scaleX;\n        this.c = -Math.sin(rotation - skewX) * scaleY;\n        this.d = Math.cos(rotation - skewX) * scaleY;\n\n        this.tx = x - ((pivotX * this.a) + (pivotY * this.c));\n        this.ty = y - ((pivotX * this.b) + (pivotY * this.d));\n\n        return this;\n    }\n\n    /**\n     * Prepends the given Matrix to this Matrix.\n     * @param matrix - The matrix to prepend\n     * @returns This matrix. Good for chaining method calls.\n     */\n    prepend(matrix: Matrix): this\n    {\n        const tx1 = this.tx;\n\n        if (matrix.a !== 1 || matrix.b !== 0 || matrix.c !== 0 || matrix.d !== 1)\n        {\n            const a1 = this.a;\n            const c1 = this.c;\n\n            this.a = (a1 * matrix.a) + (this.b * matrix.c);\n            this.b = (a1 * matrix.b) + (this.b * matrix.d);\n            this.c = (c1 * matrix.a) + (this.d * matrix.c);\n            this.d = (c1 * matrix.b) + (this.d * matrix.d);\n        }\n\n        this.tx = (tx1 * matrix.a) + (this.ty * matrix.c) + matrix.tx;\n        this.ty = (tx1 * matrix.b) + (this.ty * matrix.d) + matrix.ty;\n\n        return this;\n    }\n\n    /**\n     * Decomposes the matrix (x, y, scaleX, scaleY, and rotation) and sets the properties on to a transform.\n     * @param transform - The transform to apply the properties to.\n     * @returns The transform with the newly applied properties\n     */\n    decompose(transform: Transform): Transform\n    {\n        // sort out rotation / skew..\n        const a = this.a;\n        const b = this.b;\n        const c = this.c;\n        const d = this.d;\n        const pivot = transform.pivot;\n\n        const skewX = -Math.atan2(-c, d);\n        const skewY = Math.atan2(b, a);\n\n        const delta = Math.abs(skewX + skewY);\n\n        if (delta < 0.00001 || Math.abs(PI_2 - delta) < 0.00001)\n        {\n            transform.rotation = skewY;\n            transform.skew.x = transform.skew.y = 0;\n        }\n        else\n        {\n            transform.rotation = 0;\n            transform.skew.x = skewX;\n            transform.skew.y = skewY;\n        }\n\n        // next set scale\n        transform.scale.x = Math.sqrt((a * a) + (b * b));\n        transform.scale.y = Math.sqrt((c * c) + (d * d));\n\n        // next set position\n        transform.position.x = this.tx + ((pivot.x * a) + (pivot.y * c));\n        transform.position.y = this.ty + ((pivot.x * b) + (pivot.y * d));\n\n        return transform;\n    }\n\n    /**\n     * Inverts this matrix\n     * @returns This matrix. Good for chaining method calls.\n     */\n    invert(): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n        const tx1 = this.tx;\n        const n = (a1 * d1) - (b1 * c1);\n\n        this.a = d1 / n;\n        this.b = -b1 / n;\n        this.c = -c1 / n;\n        this.d = a1 / n;\n        this.tx = ((c1 * this.ty) - (d1 * tx1)) / n;\n        this.ty = -((a1 * this.ty) - (b1 * tx1)) / n;\n\n        return this;\n    }\n\n    /**\n     * Resets this Matrix to an identity (default) matrix.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    identity(): this\n    {\n        this.a = 1;\n        this.b = 0;\n        this.c = 0;\n        this.d = 1;\n        this.tx = 0;\n        this.ty = 0;\n\n        return this;\n    }\n\n    /**\n     * Creates a new Matrix object with the same values as this one.\n     * @returns A copy of this matrix. Good for chaining method calls.\n     */\n    clone(): Matrix\n    {\n        const matrix = new Matrix();\n\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the given matrix to be the same as the ones in this matrix\n     * @param matrix - The matrix to copy to.\n     * @returns The matrix given in parameter with its values updated.\n     */\n    copyTo(matrix: Matrix): Matrix\n    {\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the matrix to be the same as the ones in given matrix\n     * @param {PIXI.Matrix} matrix - The matrix to copy from.\n     * @returns {PIXI.Matrix} this\n     */\n    copyFrom(matrix: Matrix): this\n    {\n        this.a = matrix.a;\n        this.b = matrix.b;\n        this.c = matrix.c;\n        this.d = matrix.d;\n        this.tx = matrix.tx;\n        this.ty = matrix.ty;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`;\n    }\n    // #endif\n\n    /**\n     * A default (identity) matrix\n     * @readonly\n     */\n    static get IDENTITY(): Matrix\n    {\n        return new Matrix();\n    }\n\n    /**\n     * A temp matrix\n     * @readonly\n     */\n    static get TEMP_MATRIX(): Matrix\n    {\n        return new Matrix();\n    }\n}\n", "// Your friendly neighbour https://en.wikipedia.org/wiki/Dihedral_group\n//\n// This file implements the dihedral group of order 16, also called\n// of degree 8. That's why its called groupD8.\n\nimport { Matrix } from './Matrix';\n\n/*\n * Transform matrix for operation n is:\n * | ux | vx |\n * | uy | vy |\n */\n\nconst ux = [1, 1, 0, -1, -1, -1, 0, 1, 1, 1, 0, -1, -1, -1, 0, 1];\nconst uy = [0, 1, 1, 1, 0, -1, -1, -1, 0, 1, 1, 1, 0, -1, -1, -1];\nconst vx = [0, -1, -1, -1, 0, 1, 1, 1, 0, 1, 1, 1, 0, -1, -1, -1];\nconst vy = [1, 1, 0, -1, -1, -1, 0, 1, -1, -1, 0, 1, 1, 1, 0, -1];\n\n/**\n * [Cayley Table]{@link https://en.wikipedia.org/wiki/Cayley_table}\n * for the composition of each rotation in the dihederal group D8.\n * @type {number[][]}\n * @private\n */\nconst rotationCayley: number[][] = [];\n\n/**\n * Matrices for each `GD8Symmetry` rotation.\n * @type {PIXI.Matrix[]}\n * @private\n */\nconst rotationMatrices: Matrix[] = [];\n\n/*\n * Alias for {@code Math.sign}.\n */\nconst signum = Math.sign;\n\n/*\n * Initializes `rotationCayley` and `rotationMatrices`. It is called\n * only once below.\n */\nfunction init(): void\n{\n    for (let i = 0; i < 16; i++)\n    {\n        const row: number[] = [];\n\n        rotationCayley.push(row);\n\n        for (let j = 0; j < 16; j++)\n        {\n            /* Multiplies rotation matrices i and j. */\n            const _ux = signum((ux[i] * ux[j]) + (vx[i] * uy[j]));\n            const _uy = signum((uy[i] * ux[j]) + (vy[i] * uy[j]));\n            const _vx = signum((ux[i] * vx[j]) + (vx[i] * vy[j]));\n            const _vy = signum((uy[i] * vx[j]) + (vy[i] * vy[j]));\n\n            /* Finds rotation matrix matching the product and pushes it. */\n            for (let k = 0; k < 16; k++)\n            {\n                if (ux[k] === _ux && uy[k] === _uy\n                      && vx[k] === _vx && vy[k] === _vy)\n                {\n                    row.push(k);\n                    break;\n                }\n            }\n        }\n    }\n\n    for (let i = 0; i < 16; i++)\n    {\n        const mat = new Matrix();\n\n        mat.set(ux[i], uy[i], vx[i], vy[i], 0, 0);\n        rotationMatrices.push(mat);\n    }\n}\n\ninit();\n\ntype GD8Symmetry = number;\n/**\n * @memberof PIXI\n * @typedef {number} GD8Symmetry\n * @see PIXI.groupD8\n */\n\n/**\n * Implements the dihedral group D8, which is similar to\n * [group D4]{@link http://mathworld.wolfram.com/DihedralGroupD4.html};\n * D8 is the same but with diagonals, and it is used for texture\n * rotations.\n *\n * The directions the U- and V- axes after rotation\n * of an angle of `a: GD8Constant` are the vectors `(uX(a), uY(a))`\n * and `(vX(a), vY(a))`. These aren't necessarily unit vectors.\n *\n * **Origin:**<br>\n *  This is the small part of gameofbombs.com portal system. It works.\n * @see PIXI.groupD8.E\n * @see PIXI.groupD8.SE\n * @see PIXI.groupD8.S\n * @see PIXI.groupD8.SW\n * @see PIXI.groupD8.W\n * @see PIXI.groupD8.NW\n * @see PIXI.groupD8.N\n * @see PIXI.groupD8.NE\n * <AUTHOR> @ivanpopelyshev\n * @namespace PIXI.groupD8\n * @memberof PIXI\n */\nexport const groupD8 = {\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 0°       | East      |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    E: 0,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 45°↻     | Southeast |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    SE: 1,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 90°↻     | South     |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    S: 2,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 135°↻    | Southwest |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    SW: 3,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 180°     | West      |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    W: 4,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -135°/225°↻ | Northwest    |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    NW: 5,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -90°/270°↻  | North        |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    N: 6,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -45°/315°↻  | Northeast    |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    NE: 7,\n\n    /**\n     * Reflection about Y-axis.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MIRROR_VERTICAL: 8,\n\n    /**\n     * Reflection about the main diagonal.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MAIN_DIAGONAL: 10,\n\n    /**\n     * Reflection about X-axis.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MIRROR_HORIZONTAL: 12,\n\n    /**\n     * Reflection about reverse diagonal.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    REVERSE_DIAGONAL: 14,\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The X-component of the U-axis\n     *    after rotating the axes.\n     */\n    uX: (ind: GD8Symmetry): GD8Symmetry => ux[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The Y-component of the U-axis\n     *    after rotating the axes.\n     */\n    uY: (ind: GD8Symmetry): GD8Symmetry => uy[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The X-component of the V-axis\n     *    after rotating the axes.\n     */\n    vX: (ind: GD8Symmetry): GD8Symmetry => vx[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The Y-component of the V-axis\n     *    after rotating the axes.\n     */\n    vY: (ind: GD8Symmetry): GD8Symmetry => vy[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotation - symmetry whose opposite\n     *   is needed. Only rotations have opposite symmetries while\n     *   reflections don't.\n     * @returns {PIXI.GD8Symmetry} The opposite symmetry of `rotation`\n     */\n    inv: (rotation: GD8Symmetry): GD8Symmetry =>\n    {\n        if (rotation & 8)// true only if between 8 & 15 (reflections)\n        {\n            return rotation & 15;// or rotation % 16\n        }\n\n        return (-rotation) & 7;// or (8 - rotation) % 8\n    },\n\n    /**\n     * Composes the two D8 operations.\n     *\n     * Taking `^` as reflection:\n     *\n     * |       | E=0 | S=2 | W=4 | N=6 | E^=8 | S^=10 | W^=12 | N^=14 |\n     * |-------|-----|-----|-----|-----|------|-------|-------|-------|\n     * | E=0   | E   | S   | W   | N   | E^   | S^    | W^    | N^    |\n     * | S=2   | S   | W   | N   | E   | S^   | W^    | N^    | E^    |\n     * | W=4   | W   | N   | E   | S   | W^   | N^    | E^    | S^    |\n     * | N=6   | N   | E   | S   | W   | N^   | E^    | S^    | W^    |\n     * | E^=8  | E^  | N^  | W^  | S^  | E    | N     | W     | S     |\n     * | S^=10 | S^  | E^  | N^  | W^  | S    | E     | N     | W     |\n     * | W^=12 | W^  | S^  | E^  | N^  | W    | S     | E     | N     |\n     * | N^=14 | N^  | W^  | S^  | E^  | N    | W     | S     | E     |\n     *\n     * [This is a Cayley table]{@link https://en.wikipedia.org/wiki/Cayley_table}\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotationSecond - Second operation, which\n     *   is the row in the above cayley table.\n     * @param {PIXI.GD8Symmetry} rotationFirst - First operation, which\n     *   is the column in the above cayley table.\n     * @returns {PIXI.GD8Symmetry} Composed operation\n     */\n    add: (rotationSecond: GD8Symmetry, rotationFirst: GD8Symmetry): GD8Symmetry => (\n        rotationCayley[rotationSecond][rotationFirst]\n    ),\n\n    /**\n     * Reverse of `add`.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotationSecond - Second operation\n     * @param {PIXI.GD8Symmetry} rotationFirst - First operation\n     * @returns {PIXI.GD8Symmetry} Result\n     */\n    sub: (rotationSecond: GD8Symmetry, rotationFirst: GD8Symmetry): GD8Symmetry => (\n        rotationCayley[rotationSecond][groupD8.inv(rotationFirst)]\n    ),\n\n    /**\n     * Adds 180 degrees to rotation, which is a commutative\n     * operation.\n     * @memberof PIXI.groupD8\n     * @param {number} rotation - The number to rotate.\n     * @returns {number} Rotated number\n     */\n    rotate180: (rotation: number): number => rotation ^ 4,\n\n    /**\n     * Checks if the rotation angle is vertical, i.e. south\n     * or north. It doesn't work for reflections.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotation - The number to check.\n     * @returns {boolean} Whether or not the direction is vertical\n     */\n    isVertical: (rotation: GD8Symmetry): boolean => (rotation & 3) === 2, // rotation % 4 === 2\n\n    /**\n     * Approximates the vector `V(dx,dy)` into one of the\n     * eight directions provided by `groupD8`.\n     * @memberof PIXI.groupD8\n     * @param {number} dx - X-component of the vector\n     * @param {number} dy - Y-component of the vector\n     * @returns {PIXI.GD8Symmetry} Approximation of the vector into\n     *  one of the eight symmetries.\n     */\n    byDirection: (dx: number, dy: number): GD8Symmetry =>\n    {\n        if (Math.abs(dx) * 2 <= Math.abs(dy))\n        {\n            if (dy >= 0)\n            {\n                return groupD8.S;\n            }\n\n            return groupD8.N;\n        }\n        else if (Math.abs(dy) * 2 <= Math.abs(dx))\n        {\n            if (dx > 0)\n            {\n                return groupD8.E;\n            }\n\n            return groupD8.W;\n        }\n        else if (dy > 0)\n        {\n            if (dx > 0)\n            {\n                return groupD8.SE;\n            }\n\n            return groupD8.SW;\n        }\n        else if (dx > 0)\n        {\n            return groupD8.NE;\n        }\n\n        return groupD8.NW;\n    },\n\n    /**\n     * Helps sprite to compensate texture packer rotation.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.Matrix} matrix - sprite world matrix\n     * @param {PIXI.GD8Symmetry} rotation - The rotation factor to use.\n     * @param {number} tx - sprite anchoring\n     * @param {number} ty - sprite anchoring\n     */\n    matrixAppendRotationInv: (matrix: Matrix, rotation: GD8Symmetry, tx = 0, ty = 0): void =>\n    {\n        // Packer used \"rotation\", we use \"inv(rotation)\"\n        const mat: Matrix = rotationMatrices[groupD8.inv(rotation)];\n\n        mat.tx = tx;\n        mat.ty = ty;\n        matrix.append(mat);\n    },\n};\n", "import { ObservablePoint } from './ObservablePoint';\nimport { Matrix } from './Matrix';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Transform extends GlobalMixins.Transform {}\n\n/**\n * Transform that takes care about its versions.\n * @memberof PIXI\n */\nexport class Transform\n{\n    /** A default (identity) transform. */\n    public static readonly IDENTITY = new Transform();\n\n    /** The world transformation matrix. */\n    public worldTransform: Matrix;\n\n    /** The local transformation matrix. */\n    public localTransform: Matrix;\n\n    /** The coordinate of the object relative to the local coordinates of the parent. */\n    public position: ObservablePoint;\n\n    /** The scale factor of the object. */\n    public scale: ObservablePoint;\n\n    /** The pivot point of the displayObject that it rotates around. */\n    public pivot: ObservablePoint;\n\n    /** The skew amount, on the x and y axis. */\n    public skew: ObservablePoint;\n\n    /** The locally unique ID of the parent's world transform used to calculate the current world transformation matrix. */\n    public _parentID: number;\n\n    /** The locally unique ID of the world transform. */\n    _worldID: number;\n\n    /** The rotation amount. */\n    protected _rotation: number;\n\n    /**\n     * The X-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _cx: number;\n\n    /**\n     * The Y-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _sx: number;\n\n    /**\n     * The X-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _cy: number;\n\n    /**\n     * The Y-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _sy: number;\n\n    /** The locally unique ID of the local transform. */\n    protected _localID: number;\n\n    /** The locally unique ID of the local transform used to calculate the current local transformation matrix. */\n    protected _currentLocalID: number;\n\n    constructor()\n    {\n        this.worldTransform = new Matrix();\n        this.localTransform = new Matrix();\n        this.position = new ObservablePoint(this.onChange, this, 0, 0);\n        this.scale = new ObservablePoint(this.onChange, this, 1, 1);\n        this.pivot = new ObservablePoint(this.onChange, this, 0, 0);\n        this.skew = new ObservablePoint(this.updateSkew, this, 0, 0);\n\n        this._rotation = 0;\n        this._cx = 1;\n        this._sx = 0;\n        this._cy = 0;\n        this._sy = 1;\n        this._localID = 0;\n        this._currentLocalID = 0;\n\n        this._worldID = 0;\n        this._parentID = 0;\n    }\n\n    /** Called when a value changes. */\n    protected onChange(): void\n    {\n        this._localID++;\n    }\n\n    /** Called when the skew or the rotation changes. */\n    protected updateSkew(): void\n    {\n        this._cx = Math.cos(this._rotation + this.skew.y);\n        this._sx = Math.sin(this._rotation + this.skew.y);\n        this._cy = -Math.sin(this._rotation - this.skew.x); // cos, added PI/2\n        this._sy = Math.cos(this._rotation - this.skew.x); // sin, added PI/2\n\n        this._localID++;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Transform `\n            + `position=(${this.position.x}, ${this.position.y}) `\n            + `rotation=${this.rotation} `\n            + `scale=(${this.scale.x}, ${this.scale.y}) `\n            + `skew=(${this.skew.x}, ${this.skew.y}) `\n            + `]`;\n    }\n    // #endif\n\n    /** Updates the local transformation matrix. */\n    updateLocalTransform(): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n    }\n\n    /**\n     * Updates the local and the world transformation matrices.\n     * @param parentTransform - The parent transform\n     */\n    updateTransform(parentTransform: Transform): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n\n        if (this._parentID !== parentTransform._worldID)\n        {\n            // concat the parent matrix with the objects transform.\n            const pt = parentTransform.worldTransform;\n            const wt = this.worldTransform;\n\n            wt.a = (lt.a * pt.a) + (lt.b * pt.c);\n            wt.b = (lt.a * pt.b) + (lt.b * pt.d);\n            wt.c = (lt.c * pt.a) + (lt.d * pt.c);\n            wt.d = (lt.c * pt.b) + (lt.d * pt.d);\n            wt.tx = (lt.tx * pt.a) + (lt.ty * pt.c) + pt.tx;\n            wt.ty = (lt.tx * pt.b) + (lt.ty * pt.d) + pt.ty;\n\n            this._parentID = parentTransform._worldID;\n\n            // update the id of the transform..\n            this._worldID++;\n        }\n    }\n\n    /**\n     * Decomposes a matrix and sets the transforms properties based on it.\n     * @param matrix - The matrix to decompose\n     */\n    setFromMatrix(matrix: Matrix): void\n    {\n        matrix.decompose(this);\n        this._localID++;\n    }\n\n    /** The rotation of the object in radians. */\n    get rotation(): number\n    {\n        return this._rotation;\n    }\n\n    set rotation(value: number)\n    {\n        if (this._rotation !== value)\n        {\n            this._rotation = value;\n            this.updateSkew();\n        }\n    }\n}\n"], "names": ["SHAPES", "arguments"], "mappings": ";;;;;;;;;;;AAAA;;;;;AAKG;AACI,IAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE;AAEhC;;;;;AAKG;AACI,IAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG;AAExC;;;;;AAKG;AACI,IAAM,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI;AAExC;;;;;;;;;;AAUG;AACSA,wBAQX;AARD,CAAA,UAAY,MAAM,EAAA;AAGd,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACZ,CAAC,EARWA,cAAM,KAANA,cAAM,GAQjB,EAAA,CAAA,CAAA;;ACtCD;;;;;;AAMG;AACH,IAAA,KAAA,kBAAA,YAAA;AAOI;;;;AAIG;IACH,SAAY,KAAA,CAAA,CAAK,EAAE,CAAK,EAAA;AAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;;QATjB,IAAC,CAAA,CAAA,GAAG,CAAC,CAAC;;QAEN,IAAC,CAAA,CAAA,GAAG,CAAC,CAAC;AAST,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;KACd;AAED;;;AAGG;AACH,IAAA,KAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QAEI,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;KACpC,CAAA;AAED;;;;AAIG;IACH,KAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,CAAa,EAAA;QAElB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAEnB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,KAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAyB,CAAI,EAAA;QAEzB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAEtB,QAAA,OAAO,CAAC,CAAC;KACZ,CAAA;AAED;;;;AAIG;IACH,KAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,CAAa,EAAA;AAEhB,QAAA,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;KAC/C,CAAA;AAED;;;;;;AAMG;AACH,IAAA,KAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,CAAK,EAAE,CAAK,EAAA;AAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAEZ,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAEX,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAGD,IAAA,KAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QAEI,OAAO,sBAAA,GAAuB,IAAI,CAAC,CAAC,WAAM,IAAI,CAAC,CAAC,GAAA,GAAG,CAAC;KACvD,CAAA;IAEL,OAAC,KAAA,CAAA;AAAD,CAAC,EAAA;;AC1FD,IAAM,UAAU,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,CAAC,CAAC;AAKxE;;;;;;AAMG;AAEH;;;;AAIG;AACH,IAAA,SAAA,kBAAA,YAAA;AAoBI;;;;;AAKG;AACH,IAAA,SAAA,SAAA,CAAY,CAAsB,EAAE,CAAsB,EAAE,KAA0B,EAAE,MAA2B,EAAA;AAAvG,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAsB,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAsB,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAA0B,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAA2B,GAAA,CAAA,CAAA,EAAA;AAE/G,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;KAC3B;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAI,CAAA,SAAA,EAAA,MAAA,EAAA;;AAAR,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,CAAC,CAAC;SACjB;;;AAAA,KAAA,CAAA,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAK,CAAA,SAAA,EAAA,OAAA,EAAA;;AAAT,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;SAC9B;;;AAAA,KAAA,CAAA,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAG,CAAA,SAAA,EAAA,KAAA,EAAA;;AAAP,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,CAAC,CAAC;SACjB;;;AAAA,KAAA,CAAA,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAM,CAAA,SAAA,EAAA,QAAA,EAAA;;AAAV,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;SAC/B;;;AAAA,KAAA,CAAA,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAK,EAAA,OAAA,EAAA;;AAAhB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACpC;;;AAAA,KAAA,CAAA,CAAA;AAED;;;AAGG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACjE,CAAA;AAED;;;;AAIG;IACH,SAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,SAAoB,EAAA;AAEzB,QAAA,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AAE/B,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,SAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,SAAoB,EAAA;AAEvB,QAAA,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACrB,QAAA,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACrB,QAAA,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAE/B,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;AAED;;;;;AAKG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;QAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACvC;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAC1C;AACI,YAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAC3C;AACI,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;;;;;AAQG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,KAAgB,EAAE,SAAkB,EAAA;QAE3C,IAAI,CAAC,SAAS,EACd;YACI,IAAM,IAAE,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAC/C,IAAM,IAAE,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAE/D,IAAI,IAAE,IAAI,IAAE,EACZ;AACI,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;YAED,IAAM,IAAE,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAC/C,IAAM,IAAE,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAEnE,OAAO,IAAE,GAAG,IAAE,CAAC;AAClB,SAAA;AAED,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACtB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACpB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;AAEvB,QAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EACxB;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACpD,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACvD,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACrD,QAAA,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AAExD,QAAA,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAChC;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/E,IAAI,CAAC,KAAK,CAAC,EACX;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACxB,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACxB,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACxB,QAAA,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAExB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;eACnC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;eACtC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;eACtC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,EAC7C;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;eACtD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAChE;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAClC,QAAA,IAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;eACtD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAChE;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;AAMG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,QAAY,EAAE,QAAmB,EAAA;AAAjC,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAY,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAmB,GAAA,QAAA,CAAA,EAAA;AAEjC,QAAA,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;AACnB,QAAA,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;AAEnB,QAAA,IAAI,CAAC,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,CAAC;AAE5B,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,SAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,SAAoB,EAAA;AAEpB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AACxE,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;AAE1E,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AAEnC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,UAAc,EAAE,GAAW,EAAA;AAA3B,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAc,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAW,GAAA,KAAA,CAAA,EAAA;QAE5B,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;QAC5E,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;AAE7E,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;AAC9D,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;QAE9D,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAE1B,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,SAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,SAAoB,EAAA;AAExB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AACxE,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QACzC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;AAE1E,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;AAEtB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAGD,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AAEI,QAAA,OAAO,6BAA2B,IAAI,CAAC,CAAC,GAAA,KAAA,GAAM,IAAI,CAAC,CAAC,GAAU,SAAA,GAAA,IAAI,CAAC,KAAK,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,MAAG,CAAC;KACrG,CAAA;IAEL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;ACvUD;;;AAGG;AACH,IAAA,MAAA,kBAAA,YAAA;AAkBI;;;;AAIG;AACH,IAAA,SAAA,MAAA,CAAY,CAAK,EAAE,CAAK,EAAE,MAAU,EAAA;AAAxB,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;AAEhC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAErB,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;KAC3B;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KAClD,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;AAEzB,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACpB;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtB,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEtB,EAAE,IAAI,EAAE,CAAC;QACT,EAAE,IAAI,EAAE,CAAC;AAET,QAAA,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;KAC1B,CAAA;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;AAEI,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACtG,CAAA;AAGD,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AAEI,QAAA,OAAO,uBAAwB,GAAA,IAAI,CAAC,CAAC,GAAM,KAAA,GAAA,IAAI,CAAC,CAAC,GAAW,UAAA,GAAA,IAAI,CAAC,MAAM,MAAG,CAAC;KAC9E,CAAA;IAEL,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA;;ACnFD;;;AAGG;AACH,IAAA,OAAA,kBAAA,YAAA;AAqBI;;;;;AAKG;AACH,IAAA,SAAA,OAAA,CAAY,CAAK,EAAE,CAAK,EAAE,SAAa,EAAE,UAAc,EAAA;AAA3C,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAa,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAc,GAAA,CAAA,CAAA,EAAA;AAEnD,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;AAEzB,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;KAC3B;AAED;;;AAGG;AACH,IAAA,OAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KAC/D,CAAA;AAED;;;;;AAKG;AACH,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;QAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACvC;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;;AAGD,QAAA,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,QAAA,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzC,KAAK,IAAI,KAAK,CAAC;QACf,KAAK,IAAI,KAAK,CAAC;AAEf,QAAA,QAAQ,KAAK,GAAG,KAAK,IAAI,CAAC,EAAE;KAC/B,CAAA;AAED;;;AAGG;AACH,IAAA,OAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;QAEI,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5F,CAAA;AAGD,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AAEI,QAAA,OAAO,2BAAyB,IAAI,CAAC,CAAC,GAAA,KAAA,GAAM,IAAI,CAAC,CAAC,GAAU,SAAA,GAAA,IAAI,CAAC,KAAK,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,MAAG,CAAC;KACnG,CAAA;IAEL,OAAC,OAAA,CAAA;AAAD,CAAC,EAAA;;ACxFD;;;AAGG;AACH,IAAA,OAAA,kBAAA,YAAA;AAkBI;;;;;;AAMG;AACH,IAAA,SAAA,OAAA,GAAA;;AAAA;QAAY,IAAgB,MAAA,GAAA,EAAA,CAAA;aAAhB,IAAgB,EAAA,GAAA,CAAA,EAAhB,EAAgB,GAAA,SAAA,CAAA,MAAA,EAAhB,EAAgB,EAAA,EAAA;YAAhB,MAAgB,CAAA,EAAA,CAAA,GAAAC,WAAA,CAAA,EAAA,CAAA,CAAA;;QAExB,IAAI,IAAI,GAA4B,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;;AAGlF,QAAA,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC/B;YACI,IAAM,CAAC,GAAa,EAAE,CAAC;AAEvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC7C;AACI,gBAAA,CAAC,CAAC,IAAI,CAAE,IAAI,CAAC,CAAC,CAAgB,CAAC,CAAC,EAAG,IAAI,CAAC,CAAC,CAAgB,CAAC,CAAC,CAAC,CAAC;AAChE,aAAA;YAED,IAAI,GAAG,CAAC,CAAC;AACZ,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,IAAgB,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,GAAGD,cAAM,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;AAED;;;AAGG;AACH,IAAA,OAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QAEI,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACnC,QAAA,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;AAEpC,QAAA,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AAEvC,QAAA,OAAO,OAAO,CAAC;KAClB,CAAA;AAED;;;;;AAKG;AACH,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;QAEzB,IAAI,MAAM,GAAG,KAAK,CAAC;;;QAInB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAEtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EACnD;YACI,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9B,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACpC,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9B,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACpC,YAAA,IAAM,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAE7F,YAAA,IAAI,SAAS,EACb;gBACI,MAAM,GAAG,CAAC,MAAM,CAAC;AACpB,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAGD,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AAEI,QAAA,OAAO,qBAAqB;eACtB,cAAe,GAAA,IAAI,CAAC,WAAa,CAAA;eACjC,SAAU,GAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,UAAU,EAAE,YAAY,EAAA,EAAK,OAAG,UAAU,GAAA,IAAA,GAAK,YAAc,CAAhC,EAAgC,EAAE,EAAE,CAAC,GAAG,GAAA,CAAA,CAAC;KAC7G,CAAA;IAEL,OAAC,OAAA,CAAA;AAAD,CAAC,EAAA;;ACzGD;;;;AAIG;AACH,IAAA,gBAAA,kBAAA,YAAA;AAwBI;;;;;;AAMG;IACH,SAAY,gBAAA,CAAA,CAAK,EAAE,CAAK,EAAE,KAAS,EAAE,MAAU,EAAE,MAAW,EAAA;AAAhD,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAW,GAAA,EAAA,CAAA,EAAA;AAExD,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAGA,cAAM,CAAC,IAAI,CAAC;KAC3B;AAED;;;AAGG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QAEI,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACrF,CAAA;AAED;;;;;AAKG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAA;QAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACvC;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAC3C;AACI,YAAA,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAC5C;AACI,gBAAA,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAEzF,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM;wBAC3D,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,EAC9D;AACI,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;gBACD,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC/B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC/B,gBAAA,IAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;AAEhC,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;AACI,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACD,gBAAA,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;AACxC,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;AACI,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACD,gBAAA,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;AACzC,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;AACI,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;gBACD,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC3B,gBAAA,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,OAAO,EACpC;AACI,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAGD,IAAA,gBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AAEI,QAAA,OAAO,oCAAkC,IAAI,CAAC,CAAC,GAAM,KAAA,GAAA,IAAI,CAAC,CAAG;AACvD,eAAA,QAAA,GAAS,IAAI,CAAC,KAAK,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,GAAA,UAAA,GAAW,IAAI,CAAC,MAAM,GAAA,GAAG,CAAA,CAAC;KAC5E,CAAA;IAEL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;AC/GD;;;;;;AAMG;AACH,IAAA,eAAA,kBAAA,YAAA;AAWI;;;;;;AAMG;AACH,IAAA,SAAA,eAAA,CAAY,EAAoB,EAAE,KAAQ,EAAE,CAAK,EAAE,CAAK,EAAA;AAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAEpD,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AAEZ,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AAED;;;;;;;;AAQG;AACH,IAAA,eAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAM,EAAY,EAAE,KAAkB,EAAA;AAAhC,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAA,GAAK,IAAI,CAAC,EAAE,CAAA,EAAA;AAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAA,GAAQ,IAAI,CAAC,KAAK,CAAA,EAAA;AAElC,QAAA,OAAO,IAAI,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;KAC3D,CAAA;AAED;;;;;;AAMG;AACH,IAAA,eAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,CAAK,EAAE,CAAK,EAAA;AAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;QAEZ,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAClC;AACI,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,eAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,CAAa,EAAA;AAElB,QAAA,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EACtC;AACI,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACd,YAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;YACd,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,eAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAyB,CAAI,EAAA;QAEzB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAExB,QAAA,OAAO,CAAC,CAAC;KACZ,CAAA;AAED;;;;AAIG;IACH,eAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,CAAa,EAAA;AAEhB,QAAA,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;KACjD,CAAA;AAGD,IAAA,eAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QAEI,OAAO,gCAAA,GAAiC,CAAC,GAAM,KAAA,GAAA,CAAC,eAAU,IAAI,CAAC,KAAK,GAAA,GAAG,CAAC;KAC3E,CAAA;AAID,IAAA,MAAA,CAAA,cAAA,CAAI,eAAC,CAAA,SAAA,EAAA,GAAA,EAAA;;AAAL,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,EAAE,CAAC;SAClB;AAED,QAAA,GAAA,EAAA,UAAM,KAAa,EAAA;AAEf,YAAA,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,EACrB;AACI,gBAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;gBAChB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAI,eAAC,CAAA,SAAA,EAAA,GAAA,EAAA;;AAAL,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,EAAE,CAAC;SAClB;AAED,QAAA,GAAA,EAAA,UAAM,KAAa,EAAA;AAEf,YAAA,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,EACrB;AACI,gBAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;gBAChB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;IAUL,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA;;AC7ID;;;;;;;;;;AAUG;AACH,IAAA,MAAA,kBAAA,YAAA;AAsBI;;;;;;;AAOG;IACH,SAAY,MAAA,CAAA,CAAK,EAAE,CAAK,EAAE,CAAK,EAAE,CAAK,EAAE,EAAM,EAAE,EAAM,EAAA;AAA1C,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;QAV/C,IAAK,CAAA,KAAA,GAAwB,IAAI,CAAC;AAYrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;KAChB;AAED;;;;;;;;;;AAUG;IACH,MAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,KAAe,EAAA;AAErB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;KACtB,CAAA;AAED;;;;;;;;;AASG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,EAAU,EAAE,EAAU,EAAA;AAElE,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AAEb,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,SAAkB,EAAE,GAAkB,EAAA;AAE1C,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EACf;YACI,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;AAEhC,QAAA,IAAI,SAAS,EACb;AACI,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAChB,SAAA;AAED,aAAA;AACI,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACnB,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,YAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;;;AAMG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAoC,GAAe,EAAE,MAAU,EAAA;QAE3D,MAAM,IAAI,MAAM,IAAI,IAAI,KAAK,EAAE,CAAM,CAAC;AAEtC,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAChB,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAEhB,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACjD,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAEjD,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;;;AAMG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAA2C,GAAe,EAAE,MAAU,EAAA;QAElE,MAAM,IAAI,MAAM,IAAI,IAAI,KAAK,EAAE,CAAM,CAAC;QAEtC,IAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAExD,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAChB,QAAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAEhB,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACrG,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAEtG,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,CAAS,EAAE,CAAS,EAAA;AAE1B,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAEb,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAM,CAAS,EAAE,CAAS,EAAA;AAEtB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACZ,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACZ,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACZ,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACZ,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAEb,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,MAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,KAAa,EAAA;QAEhB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAE5B,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;AAEpB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AAExC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,MAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,MAAc,EAAA;AAEjB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAElB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QAE3C,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACxD,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAExD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,CAAS,EAAE,CAAS,EAAE,MAAc,EAAE,MAAc,EAAE,MAAc,EAC7E,MAAc,EAAE,QAAgB,EAAE,KAAa,EAAE,KAAa,EAAA;AAE9D,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;AAC7C,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;AAC7C,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;AAC9C,QAAA,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;QAE7C,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAEtD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,MAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,MAAc,EAAA;AAElB,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAEpB,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,EACxE;AACI,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YAElB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAClD,SAAA;QAED,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;QAC9D,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;AAE9D,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,MAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,SAAoB,EAAA;;AAG1B,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACjB,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACjB,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACjB,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACjB,QAAA,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AAE9B,QAAA,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAEtC,QAAA,IAAI,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,EACvD;AACI,YAAA,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC3B,YAAA,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3C,SAAA;AAED,aAAA;AACI,YAAA,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;AACvB,YAAA,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AACzB,YAAA,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;;QAGD,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjD,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAGjD,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjE,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjE,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AAEI,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;AACpB,QAAA,IAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAEhC,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAChB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AACjB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AACjB,QAAA,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAE7C,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AAEI,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AAEZ,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,IAAM,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AAE5B,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACpB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AAEpB,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;AAIG;IACH,MAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,MAAc,EAAA;AAEjB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACpB,QAAA,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AAEpB,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;AAIG;IACH,MAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,MAAc,EAAA;AAEnB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAEpB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAGD,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QAEI,OAAO,uBAAA,GAAwB,IAAI,CAAC,CAAC,GAAA,KAAA,GAAM,IAAI,CAAC,CAAC,GAAM,KAAA,GAAA,IAAI,CAAC,CAAC,WAAM,IAAI,CAAC,CAAC,GAAA,MAAA,GAAO,IAAI,CAAC,EAAE,GAAA,MAAA,GAAO,IAAI,CAAC,EAAE,GAAA,GAAG,CAAC;KAC5G,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAW,MAAQ,EAAA,UAAA,EAAA;AAJnB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,MAAM,EAAE,CAAC;SACvB;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAW,MAAW,EAAA,aAAA,EAAA;AAJtB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,MAAM,EAAE,CAAC;SACvB;;;AAAA,KAAA,CAAA,CAAA;IACL,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA;;AC1dD;AAOA;;;;AAIG;AAEH,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClE,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClE,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClE,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAElE;;;;;AAKG;AACH,IAAM,cAAc,GAAe,EAAE,CAAC;AAEtC;;;;AAIG;AACH,IAAM,gBAAgB,GAAa,EAAE,CAAC;AAEtC;;AAEG;AACH,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;AAEzB;;;AAGG;AACH,SAAS,IAAI,GAAA;IAET,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;QACI,IAAM,GAAG,GAAa,EAAE,CAAC;AAEzB,QAAA,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;;AAEI,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,YAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAGtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;AACI,gBAAA,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG;AACzB,uBAAA,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,EACvC;AACI,oBAAA,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACZ,MAAM;AACT,iBAAA;AACJ,aAAA;AACJ,SAAA;AACJ,KAAA;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;AACI,QAAA,IAAM,GAAG,GAAG,IAAI,MAAM,EAAE,CAAC;QAEzB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,QAAA,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAA;AACL,CAAC;AAED,IAAI,EAAE,CAAC;AAGP;;;;AAIG;AAEH;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACI,IAAM,OAAO,GAAG;AACnB;;;;;;AAMG;AACH,IAAA,CAAC,EAAE,CAAC;AAEJ;;;;;;AAMG;AACH,IAAA,EAAE,EAAE,CAAC;AAEL;;;;;;AAMG;AACH,IAAA,CAAC,EAAE,CAAC;AAEJ;;;;;;AAMG;AACH,IAAA,EAAE,EAAE,CAAC;AAEL;;;;;;AAMG;AACH,IAAA,CAAC,EAAE,CAAC;AAEJ;;;;;;AAMG;AACH,IAAA,EAAE,EAAE,CAAC;AAEL;;;;;;AAMG;AACH,IAAA,CAAC,EAAE,CAAC;AAEJ;;;;;;AAMG;AACH,IAAA,EAAE,EAAE,CAAC;AAEL;;;;AAIG;AACH,IAAA,eAAe,EAAE,CAAC;AAElB;;;;AAIG;AACH,IAAA,aAAa,EAAE,EAAE;AAEjB;;;;AAIG;AACH,IAAA,iBAAiB,EAAE,EAAE;AAErB;;;;AAIG;AACH,IAAA,gBAAgB,EAAE,EAAE;AAEpB;;;;;AAKG;IACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;AAE9C;;;;;AAKG;IACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;AAE9C;;;;;AAKG;IACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;AAE9C;;;;;AAKG;IACH,EAAE,EAAE,UAAC,GAAgB,EAAkB,EAAA,OAAA,EAAE,CAAC,GAAG,CAAC,CAAA,EAAA;AAE9C;;;;;;AAMG;IACH,GAAG,EAAE,UAAC,QAAqB,EAAA;AAEvB,QAAA,IAAI,QAAQ,GAAG,CAAC;AAChB,SAAA;AACI,YAAA,OAAO,QAAQ,GAAG,EAAE,CAAC;AACxB,SAAA;QAED,OAAO,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC1B;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACH,IAAA,GAAG,EAAE,UAAC,cAA2B,EAAE,aAA0B,IAAkB,QAC3E,cAAc,CAAC,cAAc,CAAC,CAAC,aAAa,CAAC,IAChD;AAED;;;;;;AAMG;IACH,GAAG,EAAE,UAAC,cAA2B,EAAE,aAA0B,EAAkB,EAAA,QAC3E,cAAc,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,EAC7D,EAAA;AAED;;;;;;AAMG;IACH,SAAS,EAAE,UAAC,QAAgB,EAAA,EAAa,OAAA,QAAQ,GAAG,CAAC,CAAA,EAAA;AAErD;;;;;;AAMG;AACH,IAAA,UAAU,EAAE,UAAC,QAAqB,EAAA,EAAc,OAAA,CAAC,QAAQ,GAAG,CAAC,MAAM,CAAC,GAAA;AAEpE;;;;;;;;AAQG;AACH,IAAA,WAAW,EAAE,UAAC,EAAU,EAAE,EAAU,EAAA;AAEhC,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EACpC;YACI,IAAI,EAAE,IAAI,CAAC,EACX;gBACI,OAAO,OAAO,CAAC,CAAC,CAAC;AACpB,aAAA;YAED,OAAO,OAAO,CAAC,CAAC,CAAC;AACpB,SAAA;AACI,aAAA,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EACzC;YACI,IAAI,EAAE,GAAG,CAAC,EACV;gBACI,OAAO,OAAO,CAAC,CAAC,CAAC;AACpB,aAAA;YAED,OAAO,OAAO,CAAC,CAAC,CAAC;AACpB,SAAA;aACI,IAAI,EAAE,GAAG,CAAC,EACf;YACI,IAAI,EAAE,GAAG,CAAC,EACV;gBACI,OAAO,OAAO,CAAC,EAAE,CAAC;AACrB,aAAA;YAED,OAAO,OAAO,CAAC,EAAE,CAAC;AACrB,SAAA;aACI,IAAI,EAAE,GAAG,CAAC,EACf;YACI,OAAO,OAAO,CAAC,EAAE,CAAC;AACrB,SAAA;QAED,OAAO,OAAO,CAAC,EAAE,CAAC;KACrB;AAED;;;;;;;AAOG;IACH,uBAAuB,EAAE,UAAC,MAAc,EAAE,QAAqB,EAAE,EAAM,EAAE,EAAM,EAAA;AAAd,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;AAAE,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAM,GAAA,CAAA,CAAA,EAAA;;QAG3E,IAAM,GAAG,GAAW,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE5D,QAAA,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACZ,QAAA,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACZ,QAAA,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACtB;;;ACxXL;;;AAGG;AACH,IAAA,SAAA,kBAAA,YAAA;AA8DI,IAAA,SAAA,SAAA,GAAA;AAEI,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAE7D,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACb,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACb,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACb,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACb,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAEzB,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;KACtB;;AAGS,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAlB,YAAA;QAEI,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB,CAAA;;AAGS,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAApB,YAAA;AAEI,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB,CAAA;AAGD,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AAEI,QAAA,OAAO,wBAAwB;AACzB,eAAA,YAAA,GAAa,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAA,IAAA,GAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAA,IAAI,CAAA;AACpD,eAAA,WAAA,GAAY,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAA;AAC5B,eAAA,SAAA,GAAU,IAAI,CAAC,KAAK,CAAC,CAAC,GAAA,IAAA,GAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAA,IAAI,CAAA;AAC3C,eAAA,QAAA,GAAS,IAAI,CAAC,IAAI,CAAC,CAAC,GAAA,IAAA,GAAK,IAAI,CAAC,IAAI,CAAC,CAAC,GAAA,IAAI,CAAA;AACxC,cAAA,GAAG,CAAC;KACb,CAAA;;AAID,IAAA,SAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;AAEI,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;AAE/B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,EAC1C;;AAEI,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAE/B,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;;AAGrC,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AACvB,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,SAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,eAA0B,EAAA;AAEtC,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;AAE/B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,EAC1C;;AAEI,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAE/B,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;;AAGrC,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,eAAe,CAAC,QAAQ,EAC/C;;AAEI,YAAA,IAAM,EAAE,GAAG,eAAe,CAAC,cAAc,CAAC;AAC1C,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;YAE/B,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACrC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACrC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACrC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACrC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;YAChD,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AAEhD,YAAA,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,QAAQ,CAAC;;YAG1C,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,MAAc,EAAA;AAExB,QAAA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;;AAAZ,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;AAED,QAAA,GAAA,EAAA,UAAa,KAAa,EAAA;AAEtB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAC5B;AACI,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;;AA7LsB,IAAA,SAAA,CAAA,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC;IAuMtD,OAAC,SAAA,CAAA;AAAA,CA1MD,EA0MC;;;;;;;;;;;;;;;;"}