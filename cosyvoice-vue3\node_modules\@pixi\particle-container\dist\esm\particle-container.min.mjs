/*!
 * @pixi/particle-container - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/particle-container is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{BLEND_MODES as t,TYPES as e}from"@pixi/constants";import{Container as i}from"@pixi/display";import{hex2rgb as r,createIndicesForQuads as o,correctBlendMode as a,premultiplyRgba as n,premultiplyTint as s}from"@pixi/utils";import{Geometry as u,Buffer as p,ExtensionType as h,ObjectRenderer as f,Shader as d,State as l}from"@pixi/core";import{Matrix as c}from"@pixi/math";var y=function(t,e){return y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},y(t,e)};function v(t,e){function i(){this.constructor=t}y(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var m=function(e){function i(i,r,o,a){void 0===i&&(i=1500),void 0===o&&(o=16384),void 0===a&&(a=!1);var n=e.call(this)||this;return o>16384&&(o=16384),n._properties=[!1,!0,!1,!1,!1],n._maxSize=i,n._batchSize=o,n._buffers=null,n._bufferUpdateIDs=[],n._updateID=0,n.interactiveChildren=!1,n.blendMode=t.NORMAL,n.autoResize=a,n.roundPixels=!0,n.baseTexture=null,n.setProperties(r),n._tint=0,n.tintRgb=new Float32Array(4),n.tint=16777215,n}return v(i,e),i.prototype.setProperties=function(t){t&&(this._properties[0]="vertices"in t||"scale"in t?!!t.vertices||!!t.scale:this._properties[0],this._properties[1]="position"in t?!!t.position:this._properties[1],this._properties[2]="rotation"in t?!!t.rotation:this._properties[2],this._properties[3]="uvs"in t?!!t.uvs:this._properties[3],this._properties[4]="tint"in t||"alpha"in t?!!t.tint||!!t.alpha:this._properties[4])},i.prototype.updateTransform=function(){this.displayObjectUpdateTransform()},Object.defineProperty(i.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t,r(t,this.tintRgb)},enumerable:!1,configurable:!0}),i.prototype.render=function(t){var e=this;this.visible&&!(this.worldAlpha<=0)&&this.children.length&&this.renderable&&(this.baseTexture||(this.baseTexture=this.children[0]._texture.baseTexture,this.baseTexture.valid||this.baseTexture.once("update",(function(){return e.onChildrenChange(0)}))),t.batch.setObjectRenderer(t.plugins.particle),t.plugins.particle.render(this))},i.prototype.onChildrenChange=function(t){for(var e=Math.floor(t/this._batchSize);this._bufferUpdateIDs.length<e;)this._bufferUpdateIDs.push(0);this._bufferUpdateIDs[e]=++this._updateID},i.prototype.dispose=function(){if(this._buffers){for(var t=0;t<this._buffers.length;++t)this._buffers[t].destroy();this._buffers=null}},i.prototype.destroy=function(t){e.prototype.destroy.call(this,t),this.dispose(),this._properties=null,this._buffers=null,this._bufferUpdateIDs=null},i}(i),_=function(){function t(t,i,r){this.geometry=new u,this.indexBuffer=null,this.size=r,this.dynamicProperties=[],this.staticProperties=[];for(var o=0;o<t.length;++o){var a=t[o];a={attributeName:a.attributeName,size:a.size,uploadFunction:a.uploadFunction,type:a.type||e.FLOAT,offset:a.offset},i[o]?this.dynamicProperties.push(a):this.staticProperties.push(a)}this.staticStride=0,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.dynamicStride=0,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this._updateID=0,this.initBuffers()}return t.prototype.initBuffers=function(){var t=this.geometry,i=0;this.indexBuffer=new p(o(this.size),!0,!0),t.addIndex(this.indexBuffer),this.dynamicStride=0;for(var r=0;r<this.dynamicProperties.length;++r){(u=this.dynamicProperties[r]).offset=i,i+=u.size,this.dynamicStride+=u.size}var a=new ArrayBuffer(this.size*this.dynamicStride*4*4);this.dynamicData=new Float32Array(a),this.dynamicDataUint32=new Uint32Array(a),this.dynamicBuffer=new p(this.dynamicData,!1,!1);var n=0;this.staticStride=0;for(r=0;r<this.staticProperties.length;++r){(u=this.staticProperties[r]).offset=n,n+=u.size,this.staticStride+=u.size}var s=new ArrayBuffer(this.size*this.staticStride*4*4);this.staticData=new Float32Array(s),this.staticDataUint32=new Uint32Array(s),this.staticBuffer=new p(this.staticData,!0,!1);for(r=0;r<this.dynamicProperties.length;++r){var u=this.dynamicProperties[r];t.addAttribute(u.attributeName,this.dynamicBuffer,0,u.type===e.UNSIGNED_BYTE,u.type,4*this.dynamicStride,4*u.offset)}for(r=0;r<this.staticProperties.length;++r){u=this.staticProperties[r];t.addAttribute(u.attributeName,this.staticBuffer,0,u.type===e.UNSIGNED_BYTE,u.type,4*this.staticStride,4*u.offset)}},t.prototype.uploadDynamic=function(t,i,r){for(var o=0;o<this.dynamicProperties.length;o++){var a=this.dynamicProperties[o];a.uploadFunction(t,i,r,a.type===e.UNSIGNED_BYTE?this.dynamicDataUint32:this.dynamicData,this.dynamicStride,a.offset)}this.dynamicBuffer._updateID++},t.prototype.uploadStatic=function(t,i,r){for(var o=0;o<this.staticProperties.length;o++){var a=this.staticProperties[o];a.uploadFunction(t,i,r,a.type===e.UNSIGNED_BYTE?this.staticDataUint32:this.staticData,this.staticStride,a.offset)}this.staticBuffer._updateID++},t.prototype.destroy=function(){this.indexBuffer=null,this.dynamicProperties=null,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this.staticProperties=null,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.geometry.destroy()},t}(),x=function(t){function i(i){var r=t.call(this,i)||this;return r.shader=null,r.properties=null,r.tempMatrix=new c,r.properties=[{attributeName:"aVertexPosition",size:2,uploadFunction:r.uploadVertices,offset:0},{attributeName:"aPositionCoord",size:2,uploadFunction:r.uploadPosition,offset:0},{attributeName:"aRotation",size:1,uploadFunction:r.uploadRotation,offset:0},{attributeName:"aTextureCoord",size:2,uploadFunction:r.uploadUvs,offset:0},{attributeName:"aColor",size:1,type:e.UNSIGNED_BYTE,uploadFunction:r.uploadTint,offset:0}],r.shader=d.from("attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\nattribute vec4 aColor;\n\nattribute vec2 aPositionCoord;\nattribute float aRotation;\n\nuniform mat3 translationMatrix;\nuniform vec4 uColor;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nvoid main(void){\n    float x = (aVertexPosition.x) * cos(aRotation) - (aVertexPosition.y) * sin(aRotation);\n    float y = (aVertexPosition.x) * sin(aRotation) + (aVertexPosition.y) * cos(aRotation);\n\n    vec2 v = vec2(x, y);\n    v = v + aPositionCoord;\n\n    gl_Position = vec4((translationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = aTextureCoord;\n    vColor = aColor * uColor;\n}\n","varying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nuniform sampler2D uSampler;\n\nvoid main(void){\n    vec4 color = texture2D(uSampler, vTextureCoord) * vColor;\n    gl_FragColor = color;\n}",{}),r.state=l.for2d(),r}return v(i,t),i.prototype.render=function(t){var e=t.children,i=t._maxSize,r=t._batchSize,o=this.renderer,s=e.length;if(0!==s){s>i&&!t.autoResize&&(s=i);var u=t._buffers;u||(u=t._buffers=this.generateBuffers(t));var p=e[0]._texture.baseTexture,h=p.alphaMode>0;this.state.blendMode=a(t.blendMode,h),o.state.set(this.state);var f=o.gl,d=t.worldTransform.copyTo(this.tempMatrix);d.prepend(o.globalUniforms.uniforms.projectionMatrix),this.shader.uniforms.translationMatrix=d.toArray(!0),this.shader.uniforms.uColor=n(t.tintRgb,t.worldAlpha,this.shader.uniforms.uColor,h),this.shader.uniforms.uSampler=p,this.renderer.shader.bind(this.shader);for(var l=!1,c=0,y=0;c<s;c+=r,y+=1){var v=s-c;v>r&&(v=r),y>=u.length&&u.push(this._generateOneMoreBuffer(t));var m=u[y];m.uploadDynamic(e,c,v);var _=t._bufferUpdateIDs[y]||0;(l=l||m._updateID<_)&&(m._updateID=t._updateID,m.uploadStatic(e,c,v)),o.geometry.bind(m.geometry),f.drawElements(f.TRIANGLES,6*v,f.UNSIGNED_SHORT,0)}}},i.prototype.generateBuffers=function(t){for(var e=[],i=t._maxSize,r=t._batchSize,o=t._properties,a=0;a<i;a+=r)e.push(new _(this.properties,o,r));return e},i.prototype._generateOneMoreBuffer=function(t){var e=t._batchSize,i=t._properties;return new _(this.properties,i,e)},i.prototype.uploadVertices=function(t,e,i,r,o,a){for(var n=0,s=0,u=0,p=0,h=0;h<i;++h){var f=t[e+h],d=f._texture,l=f.scale.x,c=f.scale.y,y=d.trim,v=d.orig;y?(n=(s=y.x-f.anchor.x*v.width)+y.width,u=(p=y.y-f.anchor.y*v.height)+y.height):(n=v.width*(1-f.anchor.x),s=v.width*-f.anchor.x,u=v.height*(1-f.anchor.y),p=v.height*-f.anchor.y),r[a]=s*l,r[a+1]=p*c,r[a+o]=n*l,r[a+o+1]=p*c,r[a+2*o]=n*l,r[a+2*o+1]=u*c,r[a+3*o]=s*l,r[a+3*o+1]=u*c,a+=4*o}},i.prototype.uploadPosition=function(t,e,i,r,o,a){for(var n=0;n<i;n++){var s=t[e+n].position;r[a]=s.x,r[a+1]=s.y,r[a+o]=s.x,r[a+o+1]=s.y,r[a+2*o]=s.x,r[a+2*o+1]=s.y,r[a+3*o]=s.x,r[a+3*o+1]=s.y,a+=4*o}},i.prototype.uploadRotation=function(t,e,i,r,o,a){for(var n=0;n<i;n++){var s=t[e+n].rotation;r[a]=s,r[a+o]=s,r[a+2*o]=s,r[a+3*o]=s,a+=4*o}},i.prototype.uploadUvs=function(t,e,i,r,o,a){for(var n=0;n<i;++n){var s=t[e+n]._texture._uvs;s?(r[a]=s.x0,r[a+1]=s.y0,r[a+o]=s.x1,r[a+o+1]=s.y1,r[a+2*o]=s.x2,r[a+2*o+1]=s.y2,r[a+3*o]=s.x3,r[a+3*o+1]=s.y3,a+=4*o):(r[a]=0,r[a+1]=0,r[a+o]=0,r[a+o+1]=0,r[a+2*o]=0,r[a+2*o+1]=0,r[a+3*o]=0,r[a+3*o+1]=0,a+=4*o)}},i.prototype.uploadTint=function(t,e,i,r,o,a){for(var n=0;n<i;++n){var u=t[e+n],p=u._texture.baseTexture.alphaMode>0,h=u.alpha,f=h<1&&p?s(u._tintRGB,h):u._tintRGB+(255*h<<24);r[a]=f,r[a+o]=f,r[a+2*o]=f,r[a+3*o]=f,a+=4*o}},i.prototype.destroy=function(){t.prototype.destroy.call(this),this.shader&&(this.shader.destroy(),this.shader=null),this.tempMatrix=null},i.extension={name:"particle",type:h.RendererPlugin},i}(f);export{m as ParticleContainer,x as ParticleRenderer};
//# sourceMappingURL=particle-container.min.mjs.map
