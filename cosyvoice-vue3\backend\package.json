{"name": "cosyvoice-websocket-backend", "version": "1.0.0", "description": "WebSocket backend server for real-time state synchronization", "main": "websocket-server.js", "scripts": {"start": "node websocket-server.js", "dev": "nodemon websocket-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["websocket", "real-time", "state-sync", "cosyvoice"], "author": "CosyVoice Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "node-fetch": "^2.6.7", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.1.7"}, "engines": {"node": ">=14.0.0"}}