{"version": 3, "file": "polyfill.js", "sources": ["../../src/globalThis.ts", "../../src/Promise.ts", "../../src/Object.assign.ts", "../../src/requestAnimationFrame.ts", "../../src/Math.sign.ts", "../../src/Number.isInteger.ts", "../../src/index.ts"], "sourcesContent": ["if (typeof globalThis === 'undefined')\n{\n    if (typeof self !== 'undefined')\n    {\n        // covers browsers\n        // @ts-expect-error not-writable ts(2540) error only on node\n        self.globalThis = self;\n    }\n    else if (typeof global !== 'undefined')\n    {\n        // covers versions of Node < 12\n        // @ts-expect-error not-writable ts(2540) error only on node\n        global.globalThis = global;\n    }\n}\n", "import Polyfill from 'promise-polyfill';\n\n// Support for IE 9 - 11 which does not include Promises\nif (!globalThis.Promise)\n{\n    globalThis.Promise = Polyfill;\n}\n", "// References:\n// https://github.com/sindresorhus/object-assign\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n\nimport objectAssign from 'object-assign';\n\nif (!Object.assign)\n{\n    Object.assign = objectAssign;\n}\n", "// References:\n// http://paulirish.com/2011/requestanimationframe-for-smart-animating/\n// https://gist.github.com/1579671\n// http://updates.html5rocks.com/2012/05/requestAnimationFrame-API-now-with-sub-millisecond-precision\n// https://gist.github.com/timhall/4078614\n// https://github.com/Financial-Times/polyfill-service/tree/master/polyfills/requestAnimationFrame\n\n// Expected to be used with Browserfiy\n// Browserify automatically detects the use of `global` and passes the\n// correct reference of `global`, `globalThis`, and finally `window`\n\nconst ONE_FRAME_TIME = 16;\n\n// Date.now\nif (!(Date.now && Date.prototype.getTime))\n{\n    Date.now = function now(): number\n    {\n        return new Date().getTime();\n    };\n}\n\n// performance.now\nif (!(globalThis.performance && globalThis.performance.now))\n{\n    const startTime = Date.now();\n\n    if (!globalThis.performance)\n    {\n        (globalThis as any).performance = {};\n    }\n\n    globalThis.performance.now = (): number => Date.now() - startTime;\n}\n\n// requestAnimationFrame\nlet lastTime = Date.now();\nconst vendors = ['ms', 'moz', 'webkit', 'o'];\n\nfor (let x = 0; x < vendors.length && !globalThis.requestAnimationFrame; ++x)\n{\n    const p = vendors[x];\n\n    globalThis.requestAnimationFrame = (globalThis as any)[`${p}RequestAnimationFrame`];\n    globalThis.cancelAnimationFrame = (globalThis as any)[`${p}CancelAnimationFrame`]\n        || (globalThis as any)[`${p}CancelRequestAnimationFrame`];\n}\n\nif (!globalThis.requestAnimationFrame)\n{\n    globalThis.requestAnimationFrame = (callback: (...parms: any[]) => void): number =>\n    {\n        if (typeof callback !== 'function')\n        {\n            throw new TypeError(`${callback}is not a function`);\n        }\n\n        const currentTime = Date.now();\n        let delay = ONE_FRAME_TIME + lastTime - currentTime;\n\n        if (delay < 0)\n        {\n            delay = 0;\n        }\n\n        lastTime = currentTime;\n\n        return globalThis.self.setTimeout(() =>\n        {\n            lastTime = Date.now();\n            callback(performance.now());\n        }, delay);\n    };\n}\n\nif (!globalThis.cancelAnimationFrame)\n{\n    globalThis.cancelAnimationFrame = (id: number): void => clearTimeout(id);\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/sign\n\nif (!Math.sign)\n{\n    Math.sign = function mathSign(x): number\n    {\n        x = Number(x);\n\n        if (x === 0 || isNaN(x))\n        {\n            return x;\n        }\n\n        return x > 0 ? 1 : -1;\n    };\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n\nif (!Number.isInteger)\n{\n    Number.isInteger = function numberIsInteger(value): boolean\n    {\n        return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n    };\n}\n", "import './globalThis';\nimport './Promise';\nimport './Object.assign';\nimport './requestAnimationFrame';\nimport './Math.sign';\nimport './Number.isInteger';\n\nif (!globalThis.ArrayBuffer)\n{\n    (globalThis as any).ArrayBuffer = Array;\n}\n\nif (!globalThis.Float32Array)\n{\n    (globalThis as any).Float32Array = Array;\n}\n\nif (!globalThis.Uint32Array)\n{\n    (globalThis as any).Uint32Array = Array;\n}\n\nif (!globalThis.Uint16Array)\n{\n    (globalThis as any).Uint16Array = Array;\n}\n\nif (!globalThis.Uint8Array)\n{\n    (globalThis as any).Uint8Array = Array;\n}\n\nif (!globalThis.Int32Array)\n{\n    (globalThis as any).Int32Array = Array;\n}\n"], "names": ["Polyfill", "objectAssign"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EACrC;AACI,IAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAC/B;;;AAGI,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC1B,KAAA;AACI,SAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EACtC;;;AAGI,QAAA,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC;AAC9B,KAAA;AACJ;;ACZD;AACA,IAAI,CAAC,UAAU,CAAC,OAAO,EACvB;AACI,IAAA,UAAU,CAAC,OAAO,GAAGA,4BAAQ,CAAC;AACjC;;ACND;AAMA,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB;AACI,IAAA,MAAM,CAAC,MAAM,GAAGC,gCAAY,CAAC;AAChC;;ACTD;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA,IAAM,cAAc,GAAG,EAAE,CAAC;AAE1B;AACA,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EACzC;AACI,IAAA,IAAI,CAAC,GAAG,GAAG,SAAS,GAAG,GAAA;AAEnB,QAAA,OAAO,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;AAChC,KAAC,CAAC;AACL,CAAA;AAED;AACA,IAAI,EAAE,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,EAC3D;AACI,IAAA,IAAM,WAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAE7B,IAAA,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;AACK,QAAA,UAAkB,CAAC,WAAW,GAAG,EAAE,CAAC;AACxC,KAAA;AAED,IAAA,UAAU,CAAC,WAAW,CAAC,GAAG,GAAG,YAAc,EAAA,OAAA,IAAI,CAAC,GAAG,EAAE,GAAG,WAAS,CAAA,EAAA,CAAC;AACrE,CAAA;AAED;AACA,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC1B,IAAM,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;AAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAC5E;AACI,IAAA,IAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAErB,UAAU,CAAC,qBAAqB,GAAI,UAAkB,CAAI,CAAC,GAAA,uBAAuB,CAAC,CAAC;AACpF,IAAA,UAAU,CAAC,oBAAoB,GAAI,UAAkB,CAAI,CAAC,yBAAsB,CAAC;AACzE,WAAA,UAAkB,CAAI,CAAC,GAA6B,6BAAA,CAAC,CAAC;AACjE,CAAA;AAED,IAAI,CAAC,UAAU,CAAC,qBAAqB,EACrC;AACI,IAAA,UAAU,CAAC,qBAAqB,GAAG,UAAC,QAAmC,EAAA;AAEnE,QAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAClC;AACI,YAAA,MAAM,IAAI,SAAS,CAAI,QAAQ,GAAA,mBAAmB,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC/B,QAAA,IAAI,KAAK,GAAG,cAAc,GAAG,QAAQ,GAAG,WAAW,CAAC;QAEpD,IAAI,KAAK,GAAG,CAAC,EACb;YACI,KAAK,GAAG,CAAC,CAAC;AACb,SAAA;QAED,QAAQ,GAAG,WAAW,CAAC;AAEvB,QAAA,OAAO,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,YAAA;AAE9B,YAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACtB,YAAA,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;SAC/B,EAAE,KAAK,CAAC,CAAC;AACd,KAAC,CAAC;AACL,CAAA;AAED,IAAI,CAAC,UAAU,CAAC,oBAAoB,EACpC;AACI,IAAA,UAAU,CAAC,oBAAoB,GAAG,UAAC,EAAU,EAAA,EAAW,OAAA,YAAY,CAAC,EAAE,CAAC,CAAA,EAAA,CAAC;AAC5E;;AC9ED;AACA;AAEA,IAAI,CAAC,IAAI,CAAC,IAAI,EACd;AACI,IAAA,IAAI,CAAC,IAAI,GAAG,SAAS,QAAQ,CAAC,CAAC,EAAA;AAE3B,QAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEd,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EACvB;AACI,YAAA,OAAO,CAAC,CAAC;AACZ,SAAA;AAED,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1B,KAAC,CAAC;AACL;;AChBD;AACA;AAEA,IAAI,CAAC,MAAM,CAAC,SAAS,EACrB;AACI,IAAA,MAAM,CAAC,SAAS,GAAG,SAAS,eAAe,CAAC,KAAK,EAAA;AAE7C,QAAA,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AACvF,KAAC,CAAC;AACL;;ACFD,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;AACK,IAAA,UAAkB,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3C,CAAA;AAED,IAAI,CAAC,UAAU,CAAC,YAAY,EAC5B;AACK,IAAA,UAAkB,CAAC,YAAY,GAAG,KAAK,CAAC;AAC5C,CAAA;AAED,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;AACK,IAAA,UAAkB,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3C,CAAA;AAED,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;AACK,IAAA,UAAkB,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3C,CAAA;AAED,IAAI,CAAC,UAAU,CAAC,UAAU,EAC1B;AACK,IAAA,UAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;AAC1C,CAAA;AAED,IAAI,CAAC,UAAU,CAAC,UAAU,EAC1B;AACK,IAAA,UAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;AAC1C;;"}