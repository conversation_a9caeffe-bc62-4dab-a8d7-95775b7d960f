/*!
 * @pixi/runner - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/runner is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=function(){function t(t){this.items=[],this._name=t,this._aliasCount=0}return t.prototype.emit=function(t,e,i,s,n,r,o,u){if(arguments.length>8)throw new Error("max arguments reached");var h=this,a=h.name,m=h.items;this._aliasCount++;for(var p=0,l=m.length;p<l;p++)m[p][a](t,e,i,s,n,r,o,u);return m===this.items&&this._aliasCount--,this},t.prototype.ensureNonAliasedItems=function(){this._aliasCount>0&&this.items.length>1&&(this._aliasCount=0,this.items=this.items.slice(0))},t.prototype.add=function(t){return t[this._name]&&(this.ensureNonAliasedItems(),this.remove(t),this.items.push(t)),this},t.prototype.remove=function(t){var e=this.items.indexOf(t);return-1!==e&&(this.ensureNonAliasedItems(),this.items.splice(e,1)),this},t.prototype.contains=function(t){return-1!==this.items.indexOf(t)},t.prototype.removeAll=function(){return this.ensureNonAliasedItems(),this.items.length=0,this},t.prototype.destroy=function(){this.removeAll(),this.items=null,this._name=null},Object.defineProperty(t.prototype,"empty",{get:function(){return 0===this.items.length},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),t}();Object.defineProperties(t.prototype,{dispatch:{value:t.prototype.emit},run:{value:t.prototype.emit}}),exports.Runner=t;
//# sourceMappingURL=runner.min.js.map
