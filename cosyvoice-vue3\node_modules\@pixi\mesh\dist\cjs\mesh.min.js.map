{"version": 3, "file": "mesh.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/MeshBatchUvs.ts", "../../src/Mesh.ts", "../../src/MeshMaterial.ts", "../../src/MeshGeometry.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import type { TextureMatrix, Buffer } from '@pixi/core';\n\n/**\n * Class controls cache for UV mapping from Texture normal space to BaseTexture normal space.\n * @memberof PIXI\n */\nexport class MeshBatchUvs\n{\n    /** UV Buffer data. */\n    public readonly data: Float32Array;\n\n    /** Buffer with normalized UV's. */\n    public uvBuffer: Buffer;\n\n    /** Material UV matrix. */\n    public uvMatrix: TextureMatrix;\n\n    private _bufferUpdateId: number;\n    private _textureUpdateId: number;\n\n    // Internal-only properties\n    _updateID: number;\n\n    /**\n     * @param uvBuffer - Buffer with normalized uv's\n     * @param uvMatrix - Material UV matrix\n     */\n    constructor(uvBuffer: Buffer, uvMatrix: TextureMatrix)\n    {\n        this.uvBuffer = uvBuffer;\n        this.uvMatrix = uvMatrix;\n        this.data = null;\n\n        this._bufferUpdateId = -1;\n        this._textureUpdateId = -1;\n        this._updateID = 0;\n    }\n\n    /**\n     * Updates\n     * @param forceUpdate - force the update\n     */\n    public update(forceUpdate?: boolean): void\n    {\n        if (!forceUpdate\n            && this._bufferUpdateId === this.uvBuffer._updateID\n            && this._textureUpdateId === this.uvMatrix._updateID\n        )\n        {\n            return;\n        }\n\n        this._bufferUpdateId = this.uvBuffer._updateID;\n        this._textureUpdateId = this.uvMatrix._updateID;\n\n        const data = this.uvBuffer.data as Float32Array;\n\n        if (!this.data || this.data.length !== data.length)\n        {\n            (this.data as any) = new Float32Array(data.length);\n        }\n\n        this.uvMatrix.multiplyUvs(data, this.data);\n\n        this._updateID++;\n    }\n}\n", "import { State } from '@pixi/core';\nimport { Point, Polygon } from '@pixi/math';\nimport type { BLEND_MODES } from '@pixi/constants';\nimport { DRAW_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\nimport { settings } from '@pixi/settings';\nimport { MeshBatchUvs } from './MeshBatchUvs';\nimport type { MeshMaterial } from './MeshMaterial';\n\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Texture, Renderer, Geometry, Buffer, Shader } from '@pixi/core';\nimport type { IPointData } from '@pixi/math';\n\nconst tempPoint = new Point();\nconst tempPolygon = new Polygon();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Mesh extends GlobalMixins.Mesh {}\n\n/**\n * Base mesh class.\n *\n * This class empowers you to have maximum flexibility to render any kind of WebGL visuals you can think of.\n * This class assumes a certain level of WebGL knowledge.\n * If you know a bit this should abstract enough away to make your life easier!\n *\n * Pretty much ALL WebGL can be broken down into the following:\n * - Geometry - The structure and data for the mesh. This can include anything from positions, uvs, normals, colors etc..\n * - Shader - This is the shader that PixiJS will render the geometry with (attributes in the shader must match the geometry)\n * - State - This is the state of WebGL required to render the mesh.\n *\n * Through a combination of the above elements you can render anything you want, 2D or 3D!\n * @memberof PIXI\n */\nexport class Mesh<T extends Shader = MeshMaterial> extends Container\n{\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Mesh objects.\n     * @type {PIXI.Shader|PIXI.MeshMaterial}\n     */\n    public shader: T;\n\n    /**\n     * Represents the WebGL state the Mesh required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    public state: State;\n\n    /** The way the Mesh should be drawn, can be any of the {@link PIXI.DRAW_MODES} constants. */\n    public drawMode: DRAW_MODES;\n\n    /**\n     * Typically the index of the IndexBuffer where to start drawing.\n     * @default 0\n     */\n    public start: number;\n\n    /**\n     * How much of the geometry to draw, by default `0` renders everything.\n     * @default 0\n     */\n    public size: number;\n\n    private _geometry: Geometry;\n\n    /** This is the caching layer used by the batcher. */\n    private vertexData: Float32Array;\n\n    /** If geometry is changed used to decide to re-transform the vertexData. */\n    private vertexDirty: number;\n    private _transformID: number;\n\n    /** Internal roundPixels field. */\n    private _roundPixels: boolean;\n\n    /** Batched UV's are cached for atlas textures. */\n    private batchUvs: MeshBatchUvs;\n\n    // Internal-only properties\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    uvs: Float32Array;\n\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    indices: Uint16Array;\n    _tintRGB: number;\n    _texture: Texture;\n\n    /**\n     * @param geometry - The geometry the mesh will use.\n     * @param {PIXI.MeshMaterial} shader - The shader the mesh will use.\n     * @param state - The state that the WebGL context is required to be in to render the mesh\n     *        if no state is provided, uses {@link PIXI.State.for2d} to create a 2D state for PixiJS.\n     * @param drawMode - The drawMode, can be any of the {@link PIXI.DRAW_MODES} constants.\n     */\n    constructor(geometry: Geometry, shader: T, state?: State, drawMode: DRAW_MODES = DRAW_MODES.TRIANGLES)\n    {\n        super();\n\n        this.geometry = geometry;\n        this.shader = shader;\n        this.state = state || State.for2d();\n        this.drawMode = drawMode;\n        this.start = 0;\n        this.size = 0;\n\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = new Float32Array(1);\n        this.vertexDirty = -1;\n\n        this._transformID = -1;\n        this._roundPixels = settings.ROUND_PIXELS;\n        this.batchUvs = null;\n    }\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh objects.\n     */\n    get geometry(): Geometry\n    {\n        return this._geometry;\n    }\n\n    set geometry(value: Geometry)\n    {\n        if (this._geometry === value)\n        {\n            return;\n        }\n\n        if (this._geometry)\n        {\n            this._geometry.refCount--;\n\n            if (this._geometry.refCount === 0)\n            {\n                this._geometry.dispose();\n            }\n        }\n\n        this._geometry = value;\n\n        if (this._geometry)\n        {\n            this._geometry.refCount++;\n        }\n\n        this.vertexDirty = -1;\n    }\n\n    /**\n     * To change mesh uv's, change its uvBuffer data and increment its _updateID.\n     * @readonly\n     */\n    get uvBuffer(): Buffer\n    {\n        return this.geometry.buffers[1];\n    }\n\n    /**\n     * To change mesh vertices, change its uvBuffer data and increment its _updateID.\n     * Incrementing _updateID is optional because most of Mesh objects do it anyway.\n     * @readonly\n     */\n    get verticesBuffer(): Buffer\n    {\n        return this.geometry.buffers[0];\n    }\n\n    /** Alias for {@link PIXI.Mesh#shader}. */\n    set material(value: T)\n    {\n        this.shader = value;\n    }\n\n    get material(): T\n    {\n        return this.shader;\n    }\n\n    /**\n     * The blend mode to be applied to the Mesh. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL;\n     */\n    set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}\n     * @default false\n     */\n    set roundPixels(value: boolean)\n    {\n        if (this._roundPixels !== value)\n        {\n            this._transformID = -1;\n        }\n        this._roundPixels = value;\n    }\n\n    get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    /**\n     * The multiply tint applied to the Mesh. This is a hex value. A value of\n     * `0xFFFFFF` will remove any tint effect.\n     *\n     * Null for non-MeshMaterial shaders\n     * @default 0xFFFFFF\n     */\n    get tint(): number\n    {\n        return 'tint' in this.shader ? (this.shader as unknown as MeshMaterial).tint : null;\n    }\n\n    set tint(value: number)\n    {\n        (this.shader as unknown as MeshMaterial).tint = value;\n    }\n\n    /** The texture that the Mesh uses. Null for non-MeshMaterial shaders */\n    get texture(): Texture\n    {\n        return 'texture' in this.shader ? (this.shader as unknown as MeshMaterial).texture : null;\n    }\n\n    set texture(value: Texture)\n    {\n        (this.shader as unknown as MeshMaterial).texture = value;\n    }\n\n    /**\n     * Standard renderer draw.\n     * @param renderer - Instance to renderer.\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // set properties for batching..\n        // TODO could use a different way to grab verts?\n        const vertices = this.geometry.buffers[0].data;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        // TODO benchmark check for attribute size..\n        if (\n            shader.batchable\n            && this.drawMode === DRAW_MODES.TRIANGLES\n            && vertices.length < Mesh.BATCHABLE_SIZE * 2\n        )\n        {\n            this._renderToBatch(renderer);\n        }\n        else\n        {\n            this._renderDefault(renderer);\n        }\n    }\n\n    /**\n     * Standard non-batching way of rendering.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderDefault(renderer: Renderer): void\n    {\n        const shader = this.shader as unknown as MeshMaterial;\n\n        shader.alpha = this.worldAlpha;\n        if (shader.update)\n        {\n            shader.update();\n        }\n\n        renderer.batch.flush();\n\n        // bind and sync uniforms..\n        shader.uniforms.translationMatrix = this.transform.worldTransform.toArray(true);\n        renderer.shader.bind(shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // bind the geometry...\n        renderer.geometry.bind(this.geometry, shader);\n\n        // then render it\n        renderer.geometry.draw(this.drawMode, this.size, this.start, this.geometry.instanceCount);\n    }\n\n    /**\n     * Rendering by using the Batch system.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderToBatch(renderer: Renderer): void\n    {\n        const geometry = this.geometry;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (shader.uvMatrix)\n        {\n            shader.uvMatrix.update();\n            this.calculateUvs();\n        }\n\n        // set properties for batching..\n        this.calculateVertices();\n        this.indices = geometry.indexBuffer.data as Uint16Array;\n        this._tintRGB = shader._tintRGB;\n        this._texture = shader.texture;\n\n        const pluginName = (this.material as unknown as MeshMaterial).pluginName;\n\n        renderer.batch.setObjectRenderer(renderer.plugins[pluginName]);\n        renderer.plugins[pluginName].render(this);\n    }\n\n    /** Updates vertexData field based on transform and vertices. */\n    public calculateVertices(): void\n    {\n        const geometry = this.geometry;\n        const verticesBuffer = geometry.buffers[0];\n        const vertices = verticesBuffer.data;\n        const vertexDirtyId = verticesBuffer._updateID;\n\n        if (vertexDirtyId === this.vertexDirty && this._transformID === this.transform._worldID)\n        {\n            return;\n        }\n\n        this._transformID = this.transform._worldID;\n\n        if (this.vertexData.length !== vertices.length)\n        {\n            this.vertexData = new Float32Array(vertices.length);\n        }\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const vertexData = this.vertexData;\n\n        for (let i = 0; i < vertexData.length / 2; i++)\n        {\n            const x = vertices[(i * 2)];\n            const y = vertices[(i * 2) + 1];\n\n            vertexData[(i * 2)] = (a * x) + (c * y) + tx;\n            vertexData[(i * 2) + 1] = (b * x) + (d * y) + ty;\n        }\n\n        if (this._roundPixels)\n        {\n            const resolution = settings.RESOLUTION;\n\n            for (let i = 0; i < vertexData.length; ++i)\n            {\n                vertexData[i] = Math.round((vertexData[i] * resolution | 0) / resolution);\n            }\n        }\n\n        this.vertexDirty = vertexDirtyId;\n    }\n\n    /** Updates uv field based on from geometry uv's or batchUvs. */\n    public calculateUvs(): void\n    {\n        const geomUvs = this.geometry.buffers[1];\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (!shader.uvMatrix.isSimple)\n        {\n            if (!this.batchUvs)\n            {\n                this.batchUvs = new MeshBatchUvs(geomUvs, shader.uvMatrix);\n            }\n            this.batchUvs.update();\n            this.uvs = this.batchUvs.data;\n        }\n        else\n        {\n            this.uvs = geomUvs.data as Float32Array;\n        }\n    }\n\n    /**\n     * Updates the bounds of the mesh as a rectangle. The bounds calculation takes the worldTransform into account.\n     * there must be a aVertexPosition attribute present in the geometry for bounds to be calculated correctly.\n     */\n    protected _calculateBounds(): void\n    {\n        this.calculateVertices();\n\n        this._bounds.addVertexData(this.vertexData, 0, this.vertexData.length);\n    }\n\n    /**\n     * Tests if a point is inside this mesh. Works only for PIXI.DRAW_MODES.TRIANGLES.\n     * @param point - The point to test.\n     * @returns - The result of the test.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        if (!this.getBounds().contains(point.x, point.y))\n        {\n            return false;\n        }\n\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const vertices = this.geometry.getBuffer('aVertexPosition').data;\n\n        const points = tempPolygon.points;\n        const indices =  this.geometry.getIndex().data;\n        const len = indices.length;\n        const step = this.drawMode === 4 ? 3 : 1;\n\n        for (let i = 0; i + 2 < len; i += step)\n        {\n            const ind0 = indices[i] * 2;\n            const ind1 = indices[i + 1] * 2;\n            const ind2 = indices[i + 2] * 2;\n\n            points[0] = vertices[ind0];\n            points[1] = vertices[ind0 + 1];\n            points[2] = vertices[ind1];\n            points[3] = vertices[ind1 + 1];\n            points[4] = vertices[ind2];\n            points[5] = vertices[ind2 + 1];\n\n            if (tempPolygon.contains(tempPoint.x, tempPoint.y))\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        if (this._cachedTexture)\n        {\n            this._cachedTexture.destroy();\n            this._cachedTexture = null;\n        }\n\n        this.geometry = null;\n        this.shader = null;\n        this.state = null;\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = null;\n    }\n\n    /** The maximum number of vertices to consider batchable. Generally, the complexity of the geometry. */\n    public static BATCHABLE_SIZE = 100;\n}\n", "import { Program, Shader, TextureMatrix } from '@pixi/core';\nimport { Matrix } from '@pixi/math';\nimport { premultiplyTintToRgba } from '@pixi/utils';\nimport fragment from './shader/mesh.frag';\nimport vertex from './shader/mesh.vert';\n\nimport type { Texture } from '@pixi/core';\nimport type { Dict } from '@pixi/utils';\n\nexport interface IMeshMaterialOptions\n{\n    alpha?: number;\n    tint?: number;\n    pluginName?: string;\n    program?: Program;\n    uniforms?: Dict<unknown>;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface MeshMaterial extends GlobalMixins.MeshMaterial {}\n\n/**\n * Slightly opinionated default shader for PixiJS 2D objects.\n * @memberof PIXI\n */\nexport class MeshMaterial extends Shader\n{\n    /**\n     * TextureMatrix instance for this Mesh, used to track Texture changes.\n     * @readonly\n     */\n    public readonly uvMatrix: TextureMatrix;\n\n    /**\n     * `true` if shader can be batch with the renderer's batch system.\n     * @default true\n     */\n    public batchable: boolean;\n\n    /**\n     * Renderer plugin for batching.\n     * @default 'batch'\n     */\n    public pluginName: string;\n\n    // Internal-only properties\n    _tintRGB: number;\n\n    /**\n     * Only do update if tint or alpha changes.\n     * @private\n     * @default false\n     */\n    private _colorDirty: boolean;\n    private _alpha: number;\n    private _tint: number;\n\n    /**\n     * @param uSampler - Texture that material uses to render.\n     * @param options - Additional options\n     * @param {number} [options.alpha=1] - Default alpha.\n     * @param {number} [options.tint=0xFFFFFF] - Default tint.\n     * @param {string} [options.pluginName='batch'] - Renderer plugin for batching.\n     * @param {PIXI.Program} [options.program=0xFFFFFF] - Custom program.\n     * @param {object} [options.uniforms] - Custom uniforms.\n     */\n    constructor(uSampler: Texture, options?: IMeshMaterialOptions)\n    {\n        const uniforms = {\n            uSampler,\n            alpha: 1,\n            uTextureMatrix: Matrix.IDENTITY,\n            uColor: new Float32Array([1, 1, 1, 1]),\n        };\n\n        // Set defaults\n        options = Object.assign({\n            tint: 0xFFFFFF,\n            alpha: 1,\n            pluginName: 'batch',\n        }, options);\n\n        if (options.uniforms)\n        {\n            Object.assign(uniforms, options.uniforms);\n        }\n\n        super(options.program || Program.from(vertex, fragment), uniforms);\n\n        this._colorDirty = false;\n\n        this.uvMatrix = new TextureMatrix(uSampler);\n        this.batchable = options.program === undefined;\n        this.pluginName = options.pluginName;\n\n        this.tint = options.tint;\n        this.alpha = options.alpha;\n    }\n\n    /** Reference to the texture being rendered. */\n    get texture(): Texture\n    {\n        return this.uniforms.uSampler;\n    }\n    set texture(value: Texture)\n    {\n        if (this.uniforms.uSampler !== value)\n        {\n            if (!this.uniforms.uSampler.baseTexture.alphaMode !== !value.baseTexture.alphaMode)\n            {\n                this._colorDirty = true;\n            }\n\n            this.uniforms.uSampler = value;\n            this.uvMatrix.texture = value;\n        }\n    }\n\n    /**\n     * This gets automatically set by the object using this.\n     * @default 1\n     */\n    set alpha(value: number)\n    {\n        if (value === this._alpha) return;\n\n        this._alpha = value;\n        this._colorDirty = true;\n    }\n    get alpha(): number\n    {\n        return this._alpha;\n    }\n\n    /**\n     * Multiply tint for the material.\n     * @default 0xFFFFFF\n     */\n    set tint(value: number)\n    {\n        if (value === this._tint) return;\n\n        this._tint = value;\n        this._tintRGB = (value >> 16) + (value & 0xff00) + ((value & 0xff) << 16);\n        this._colorDirty = true;\n    }\n    get tint(): number\n    {\n        return this._tint;\n    }\n\n    /** Gets called automatically by the Mesh. Intended to be overridden for custom {@link MeshMaterial} objects. */\n    public update(): void\n    {\n        if (this._colorDirty)\n        {\n            this._colorDirty = false;\n            const baseTexture = this.texture.baseTexture;\n\n            premultiplyTintToRgba(\n                this._tint, this._alpha, this.uniforms.uColor, (baseTexture.alphaMode as unknown as boolean)\n            );\n        }\n        if (this.uvMatrix.update())\n        {\n            this.uniforms.uTextureMatrix = this.uvMatrix.mapCoord;\n        }\n    }\n}\n", "import { TYPES } from '@pixi/constants';\nimport { <PERSON>uff<PERSON>, Geometry } from '@pixi/core';\n\nimport type { IArrayBuffer } from '@pixi/core';\n\n/**\n * Standard 2D geometry used in PixiJS.\n *\n * Geometry can be defined without passing in a style or data if required.\n *\n * ```js\n * const geometry = new PIXI.Geometry();\n *\n * geometry.addAttribute('positions', [0, 0, 100, 0, 100, 100, 0, 100], 2);\n * geometry.addAttribute('uvs', [0,0,1,0,1,1,0,1], 2);\n * geometry.addIndex([0,1,2,1,3,2]);\n *\n * ```\n * @memberof PIXI\n */\nexport class MeshGeometry extends Geometry\n{\n    // Internal-only properties\n    /**\n     * Dirty flag to limit update calls on Mesh. For example,\n     * limiting updates on a single Mesh instance with a shared Geometry\n     * within the render loop.\n     * @private\n     * @default -1\n     */\n    _updateId: number;\n\n    /**\n     * @param {Float32Array|number[]} [vertices] - Positional data on geometry.\n     * @param {Float32Array|number[]} [uvs] - Texture UVs.\n     * @param {Uint16Array|number[]} [index] - IndexBuffer\n     */\n    constructor(vertices?: IArrayBuffer, uvs?: IArrayBuffer, index?: IArrayBuffer)\n    {\n        super();\n\n        const verticesBuffer = new Buffer(vertices);\n        const uvsBuffer = new Buffer(uvs, true);\n        const indexBuffer = new Buffer(index, true, true);\n\n        this.addAttribute('aVertexPosition', verticesBuffer, 2, false, TYPES.FLOAT)\n            .addAttribute('aTextureCoord', uvsBuffer, 2, false, TYPES.FLOAT)\n            .addIndex(indexBuffer);\n\n        this._updateId = -1;\n    }\n\n    /**\n     * If the vertex position is updated.\n     * @readonly\n     * @private\n     */\n    get vertexDirtyId(): number\n    {\n        return this.buffers[0]._updateID;\n    }\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "this", "constructor", "prototype", "create", "MeshBatchUvs", "uv<PERSON><PERSON><PERSON>", "uvMatrix", "data", "_bufferUpdateId", "_textureUpdateId", "_updateID", "update", "forceUpdate", "length", "Float32Array", "multiplyUvs", "tempPoint", "Point", "tempPolygon", "Polygon", "<PERSON><PERSON>", "_super", "geometry", "shader", "state", "drawMode", "DRAW_MODES", "TRIANGLES", "_this", "State", "for2d", "start", "size", "uvs", "indices", "vertexData", "vertexDirty", "_transformID", "_roundPixels", "settings", "ROUND_PIXELS", "batchUvs", "defineProperty", "get", "_geometry", "set", "value", "refCount", "dispose", "buffers", "blendMode", "tint", "texture", "_render", "renderer", "vertices", "batchable", "BATCHABLE_SIZE", "_renderToBatch", "_renderDefault", "alpha", "worldAlpha", "batch", "flush", "uniforms", "translationMatrix", "transform", "worldTransform", "toArray", "bind", "draw", "instanceCount", "calculateUvs", "calculateVertices", "indexBuffer", "_tintRGB", "_texture", "pluginName", "material", "setObjectR<PERSON><PERSON>", "plugins", "render", "<PERSON><PERSON><PERSON><PERSON>", "vertexDirtyId", "_worldID", "wt", "a", "c", "tx", "ty", "i", "x", "y", "resolution", "RESOLUTION", "Math", "round", "geomUvs", "isSimple", "_calculateBounds", "_bounds", "addVertexData", "containsPoint", "point", "getBounds", "contains", "applyInverse", "<PERSON><PERSON><PERSON><PERSON>", "points", "getIndex", "len", "step", "ind0", "ind1", "ind2", "destroy", "options", "call", "_cachedTexture", "Container", "MeshMaterial", "uSampler", "uTextureMatrix", "Matrix", "IDENTITY", "uColor", "assign", "program", "Program", "from", "_colorDirty", "TextureMatrix", "undefined", "baseTexture", "alphaMode", "_alpha", "_tint", "premultiplyTintToRgba", "mapCoord", "Shader", "MeshGeometry", "index", "<PERSON><PERSON><PERSON>", "uvs<PERSON><PERSON><PERSON>", "addAttribute", "TYPES", "FLOAT", "addIndex", "_updateId", "Geometry"], "mappings": ";;;;;;;qOAgBIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,IAGrB,SAASO,EAAUR,EAAGC,GAEzB,SAASQ,IAAOC,KAAKC,YAAcX,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEY,UAAkB,OAANX,EAAaC,OAAOW,OAAOZ,IAAMQ,EAAGG,UAAYX,EAAEW,UAAW,IAAIH,GCpBnF,IAAAK,EAAA,WAqBI,SAAYA,EAAAC,EAAkBC,GAE1BN,KAAKK,SAAWA,EAChBL,KAAKM,SAAWA,EAChBN,KAAKO,KAAO,KAEZP,KAAKQ,iBAAmB,EACxBR,KAAKS,kBAAoB,EACzBT,KAAKU,UAAY,EA+BzB,OAxBWN,EAAMF,UAAAS,OAAb,SAAcC,GAEV,GAAKA,GACEZ,KAAKQ,kBAAoBR,KAAKK,SAASK,WACvCV,KAAKS,mBAAqBT,KAAKM,SAASI,UAF/C,CAQAV,KAAKQ,gBAAkBR,KAAKK,SAASK,UACrCV,KAAKS,iBAAmBT,KAAKM,SAASI,UAEtC,IAAMH,EAAOP,KAAKK,SAASE,KAEtBP,KAAKO,MAAQP,KAAKO,KAAKM,SAAWN,EAAKM,SAEvCb,KAAKO,KAAe,IAAIO,aAAaP,EAAKM,SAG/Cb,KAAKM,SAASS,YAAYR,EAAMP,KAAKO,MAErCP,KAAKU,cAEZN,KCrDKY,EAAY,IAAIC,EAAAA,MAChBC,EAAc,IAAIC,EAAAA,QAoBxBC,EAAA,SAAAC,GAmEI,SAAAD,EAAYE,EAAoBC,EAAWC,EAAeC,QAAA,IAAAA,IAAAA,EAAuBC,EAAAA,WAAWC,WAA5F,IAAAC,EAEIP,cAiBHrB,YAfG4B,EAAKN,SAAWA,EAChBM,EAAKL,OAASA,EACdK,EAAKJ,MAAQA,GAASK,EAAKA,MAACC,QAC5BF,EAAKH,SAAWA,EAChBG,EAAKG,MAAQ,EACbH,EAAKI,KAAO,EAEZJ,EAAKK,IAAM,KACXL,EAAKM,QAAU,KACfN,EAAKO,WAAa,IAAIrB,aAAa,GACnCc,EAAKQ,aAAe,EAEpBR,EAAKS,cAAgB,EACrBT,EAAKU,aAAeC,EAAQA,SAACC,aAC7BZ,EAAKa,SAAW,OA4WxB,OAjc2D3C,EAASsB,EAAAC,GA6FhE7B,OAAAkD,eAAItB,EAAQlB,UAAA,WAAA,CAAZyC,IAAA,WAEI,OAAO3C,KAAK4C,WAGhBC,IAAA,SAAaC,GAEL9C,KAAK4C,YAAcE,IAKnB9C,KAAK4C,YAEL5C,KAAK4C,UAAUG,WAEiB,IAA5B/C,KAAK4C,UAAUG,UAEf/C,KAAK4C,UAAUI,WAIvBhD,KAAK4C,UAAYE,EAEb9C,KAAK4C,WAEL5C,KAAK4C,UAAUG,WAGnB/C,KAAKoC,aAAe,oCAOxB5C,OAAAkD,eAAItB,EAAQlB,UAAA,WAAA,CAAZyC,IAAA,WAEI,OAAO3C,KAAKsB,SAAS2B,QAAQ,oCAQjCzD,OAAAkD,eAAItB,EAAclB,UAAA,iBAAA,CAAlByC,IAAA,WAEI,OAAO3C,KAAKsB,SAAS2B,QAAQ,oCAIjCzD,OAAAkD,eAAItB,EAAQlB,UAAA,WAAA,CAKZyC,IAAA,WAEI,OAAO3C,KAAKuB,QAPhBsB,IAAA,SAAaC,GAET9C,KAAKuB,OAASuB,mCAalBtD,OAAAkD,eAAItB,EAASlB,UAAA,YAAA,CAKbyC,IAAA,WAEI,OAAO3C,KAAKwB,MAAM0B,WAPtBL,IAAA,SAAcC,GAEV9C,KAAKwB,MAAM0B,UAAYJ,mCAe3BtD,OAAAkD,eAAItB,EAAWlB,UAAA,cAAA,CASfyC,IAAA,WAEI,OAAO3C,KAAKsC,cAXhBO,IAAA,SAAgBC,GAER9C,KAAKsC,eAAiBQ,IAEtB9C,KAAKqC,cAAgB,GAEzBrC,KAAKsC,aAAeQ,mCAexBtD,OAAAkD,eAAItB,EAAIlB,UAAA,OAAA,CAARyC,IAAA,WAEI,MAAO,SAAU3C,KAAKuB,OAAUvB,KAAKuB,OAAmC4B,KAAO,MAGnFN,IAAA,SAASC,GAEJ9C,KAAKuB,OAAmC4B,KAAOL,mCAIpDtD,OAAAkD,eAAItB,EAAOlB,UAAA,UAAA,CAAXyC,IAAA,WAEI,MAAO,YAAa3C,KAAKuB,OAAUvB,KAAKuB,OAAmC6B,QAAU,MAGzFP,IAAA,SAAYC,GAEP9C,KAAKuB,OAAmC6B,QAAUN,mCAO7C1B,EAAOlB,UAAAmD,QAAjB,SAAkBC,GAId,IAAMC,EAAWvD,KAAKsB,SAAS2B,QAAQ,GAAG1C,KAC3BP,KAAKuB,OAITiC,WACJxD,KAAKyB,WAAaC,EAAAA,WAAWC,WAC7B4B,EAAS1C,OAA+B,EAAtBO,EAAKqC,eAG1BzD,KAAK0D,eAAeJ,GAIpBtD,KAAK2D,eAAeL,IAQlBlC,EAAclB,UAAAyD,eAAxB,SAAyBL,GAErB,IAAM/B,EAASvB,KAAKuB,OAEpBA,EAAOqC,MAAQ5D,KAAK6D,WAChBtC,EAAOZ,QAEPY,EAAOZ,SAGX2C,EAASQ,MAAMC,QAGfxC,EAAOyC,SAASC,kBAAoBjE,KAAKkE,UAAUC,eAAeC,SAAQ,GAC1Ed,EAAS/B,OAAO8C,KAAK9C,GAGrB+B,EAAS9B,MAAMqB,IAAI7C,KAAKwB,OAGxB8B,EAAShC,SAAS+C,KAAKrE,KAAKsB,SAAUC,GAGtC+B,EAAShC,SAASgD,KAAKtE,KAAKyB,SAAUzB,KAAKgC,KAAMhC,KAAK+B,MAAO/B,KAAKsB,SAASiD,gBAOrEnD,EAAclB,UAAAwD,eAAxB,SAAyBJ,GAErB,IAAMhC,EAAWtB,KAAKsB,SAChBC,EAASvB,KAAKuB,OAEhBA,EAAOjB,WAEPiB,EAAOjB,SAASK,SAChBX,KAAKwE,gBAITxE,KAAKyE,oBACLzE,KAAKkC,QAAUZ,EAASoD,YAAYnE,KACpCP,KAAK2E,SAAWpD,EAAOoD,SACvB3E,KAAK4E,SAAWrD,EAAO6B,QAEvB,IAAMyB,EAAc7E,KAAK8E,SAAqCD,WAE9DvB,EAASQ,MAAMiB,kBAAkBzB,EAAS0B,QAAQH,IAClDvB,EAAS0B,QAAQH,GAAYI,OAAOjF,OAIjCoB,EAAAlB,UAAAuE,kBAAP,WAEI,IACMS,EADWlF,KAAKsB,SACU2B,QAAQ,GAClCM,EAAW2B,EAAe3E,KAC1B4E,EAAgBD,EAAexE,UAErC,GAAIyE,IAAkBnF,KAAKoC,aAAepC,KAAKqC,eAAiBrC,KAAKkE,UAAUkB,SAA/E,CAKApF,KAAKqC,aAAerC,KAAKkE,UAAUkB,SAE/BpF,KAAKmC,WAAWtB,SAAW0C,EAAS1C,SAEpCb,KAAKmC,WAAa,IAAIrB,aAAayC,EAAS1C,SAahD,IAVA,IAAMwE,EAAKrF,KAAKkE,UAAUC,eACpBmB,EAAID,EAAGC,EACP/F,EAAI8F,EAAG9F,EACPgG,EAAIF,EAAGE,EACPjG,EAAI+F,EAAG/F,EACPkG,EAAKH,EAAGG,GACRC,EAAKJ,EAAGI,GAERtD,EAAanC,KAAKmC,WAEfuD,EAAI,EAAGA,EAAIvD,EAAWtB,OAAS,EAAG6E,IAC3C,CACI,IAAMC,EAAIpC,EAAc,EAAJmC,GACdE,EAAIrC,EAAc,EAAJmC,EAAS,GAE7BvD,EAAgB,EAAJuD,GAAWJ,EAAIK,EAAMJ,EAAIK,EAAKJ,EAC1CrD,EAAgB,EAAJuD,EAAS,GAAMnG,EAAIoG,EAAMrG,EAAIsG,EAAKH,EAGlD,GAAIzF,KAAKsC,aAEL,CAAA,IAAMuD,EAAatD,EAAQA,SAACuD,WAE5B,IAASJ,EAAI,EAAGA,EAAIvD,EAAWtB,SAAU6E,EAErCvD,EAAWuD,GAAKK,KAAKC,OAAO7D,EAAWuD,GAAKG,EAAa,GAAKA,GAItE7F,KAAKoC,YAAc+C,IAIhB/D,EAAAlB,UAAAsE,aAAP,WAEI,IAAMyB,EAAUjG,KAAKsB,SAAS2B,QAAQ,GAChC1B,EAASvB,KAAKuB,OAEfA,EAAOjB,SAAS4F,SAWjBlG,KAAKiC,IAAMgE,EAAQ1F,MATdP,KAAKyC,WAENzC,KAAKyC,SAAW,IAAIrC,EAAa6F,EAAS1E,EAAOjB,WAErDN,KAAKyC,SAAS9B,SACdX,KAAKiC,IAAMjC,KAAKyC,SAASlC,OAYvBa,EAAAlB,UAAAiG,iBAAV,WAEInG,KAAKyE,oBAELzE,KAAKoG,QAAQC,cAAcrG,KAAKmC,WAAY,EAAGnC,KAAKmC,WAAWtB,SAQ5DO,EAAalB,UAAAoG,cAApB,SAAqBC,GAEjB,IAAKvG,KAAKwG,YAAYC,SAASF,EAAMZ,EAAGY,EAAMX,GAE1C,OAAO,EAGX5F,KAAKmE,eAAeuC,aAAaH,EAAOvF,GASxC,IAPA,IAAMuC,EAAWvD,KAAKsB,SAASqF,UAAU,mBAAmBpG,KAEtDqG,EAAS1F,EAAY0F,OACrB1E,EAAWlC,KAAKsB,SAASuF,WAAWtG,KACpCuG,EAAM5E,EAAQrB,OACdkG,EAAyB,IAAlB/G,KAAKyB,SAAiB,EAAI,EAE9BiE,EAAI,EAAGA,EAAI,EAAIoB,EAAKpB,GAAKqB,EAClC,CACI,IAAMC,EAAoB,EAAb9E,EAAQwD,GACfuB,EAAwB,EAAjB/E,EAAQwD,EAAI,GACnBwB,EAAwB,EAAjBhF,EAAQwD,EAAI,GASzB,GAPAkB,EAAO,GAAKrD,EAASyD,GACrBJ,EAAO,GAAKrD,EAASyD,EAAO,GAC5BJ,EAAO,GAAKrD,EAAS0D,GACrBL,EAAO,GAAKrD,EAAS0D,EAAO,GAC5BL,EAAO,GAAKrD,EAAS2D,GACrBN,EAAO,GAAKrD,EAAS2D,EAAO,GAExBhG,EAAYuF,SAASzF,EAAU2E,EAAG3E,EAAU4E,GAE5C,OAAO,EAIf,OAAO,GAGJxE,EAAOlB,UAAAiH,QAAd,SAAeC,GAEX/F,EAAAnB,UAAMiH,QAAOE,KAAArH,KAACoH,GAEVpH,KAAKsH,iBAELtH,KAAKsH,eAAeH,UACpBnH,KAAKsH,eAAiB,MAG1BtH,KAAKsB,SAAW,KAChBtB,KAAKuB,OAAS,KACdvB,KAAKwB,MAAQ,KACbxB,KAAKiC,IAAM,KACXjC,KAAKkC,QAAU,KACflC,KAAKmC,WAAa,MAIRf,EAAcqC,eAAG,IAClCrC,EAjcD,CAA2DmG,aCT3DC,EAAA,SAAAnG,GAyCI,SAAYmG,EAAAC,EAAmBL,GAA/B,IA+BCxF,EAAA5B,KA7BSgE,EAAW,CACbyD,SAAQA,EACR7D,MAAO,EACP8D,eAAgBC,EAAMA,OAACC,SACvBC,OAAQ,IAAI/G,aAAa,CAAC,EAAG,EAAG,EAAG,YAIvCsG,EAAU5H,OAAOsI,OAAO,CACpB3E,KAAM,SACNS,MAAO,EACPiB,WAAY,SACbuC,IAESpD,UAERxE,OAAOsI,OAAO9D,EAAUoD,EAAQpD,WAGpCpC,EAAAP,YAAM+F,EAAQW,SAAWC,UAAQC,mkBAAwBjE,IAAUhE,MAE9DkI,aAAc,EAEnBtG,EAAKtB,SAAW,IAAI6H,EAAaA,cAACV,GAClC7F,EAAK4B,eAAgC4E,IAApBhB,EAAQW,QACzBnG,EAAKiD,WAAauC,EAAQvC,WAE1BjD,EAAKuB,KAAOiE,EAAQjE,KACpBvB,EAAKgC,MAAQwD,EAAQxD,QAwE7B,OA/IkC9D,EAAM0H,EAAAnG,GA2EpC7B,OAAAkD,eAAI8E,EAAOtH,UAAA,UAAA,CAAXyC,IAAA,WAEI,OAAO3C,KAAKgE,SAASyD,UAEzB5E,IAAA,SAAYC,GAEJ9C,KAAKgE,SAASyD,WAAa3E,KAEtB9C,KAAKgE,SAASyD,SAASY,YAAYC,YAAexF,EAAMuF,YAAYC,YAErEtI,KAAKkI,aAAc,GAGvBlI,KAAKgE,SAASyD,SAAW3E,EACzB9C,KAAKM,SAAS8C,QAAUN,oCAQhCtD,OAAAkD,eAAI8E,EAAKtH,UAAA,QAAA,CAOTyC,IAAA,WAEI,OAAO3C,KAAKuI,QAThB1F,IAAA,SAAUC,GAEFA,IAAU9C,KAAKuI,SAEnBvI,KAAKuI,OAASzF,EACd9C,KAAKkI,aAAc,oCAWvB1I,OAAAkD,eAAI8E,EAAItH,UAAA,OAAA,CAQRyC,IAAA,WAEI,OAAO3C,KAAKwI,OAVhB3F,IAAA,SAASC,GAEDA,IAAU9C,KAAKwI,QAEnBxI,KAAKwI,MAAQ1F,EACb9C,KAAK2E,UAAY7B,GAAS,KAAe,MAARA,KAA4B,IAARA,IAAiB,IACtE9C,KAAKkI,aAAc,oCAQhBV,EAAAtH,UAAAS,OAAP,WAEI,GAAIX,KAAKkI,YACT,CACIlI,KAAKkI,aAAc,EACnB,IAAMG,EAAcrI,KAAKoD,QAAQiF,YAEjCI,EAAAA,sBACIzI,KAAKwI,MAAOxI,KAAKuI,OAAQvI,KAAKgE,SAAS6D,OAASQ,EAAYC,WAGhEtI,KAAKM,SAASK,WAEdX,KAAKgE,SAAS0D,eAAiB1H,KAAKM,SAASoI,WAGxDlB,EA/ID,CAAkCmB,UCLlCC,EAAA,SAAAvH,GAiBI,SAAAuH,EAAYrF,EAAyBtB,EAAoB4G,GAAzD,IAAAjH,EAEIP,cAWHrB,KATSkF,EAAiB,IAAI4D,SAAOvF,GAC5BwF,EAAY,IAAID,EAAAA,OAAO7G,GAAK,GAC5ByC,EAAc,IAAIoE,EAAMA,OAACD,GAAO,GAAM,UAE5CjH,EAAKoH,aAAa,kBAAmB9D,EAAgB,GAAG,EAAO+D,EAAKA,MAACC,OAChEF,aAAa,gBAAiBD,EAAW,GAAG,EAAOE,EAAKA,MAACC,OACzDC,SAASzE,GAEd9C,EAAKwH,WAAa,IAY1B,OAzCkCtJ,EAAQ8I,EAAAvH,GAqCtC7B,OAAAkD,eAAIkG,EAAa1I,UAAA,gBAAA,CAAjByC,IAAA,WAEI,OAAO3C,KAAKiD,QAAQ,GAAGvC,2CAE9BkI,EAzCD,CAAkCS"}