{"version": 3, "file": "interaction.js", "sources": ["../../src/InteractionData.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/InteractionEvent.ts", "../../src/InteractionTrackingData.ts", "../../src/TreeSearch.ts", "../../src/interactiveTarget.ts", "../../src/InteractionManager.ts"], "sourcesContent": ["import type { IPointData } from '@pixi/math';\nimport { Point } from '@pixi/math';\n\nimport type { DisplayObject } from '@pixi/display';\n\nexport type InteractivePointerEvent = PointerEvent | TouchEvent | MouseEvent;\n\n/**\n * Holds all information related to an Interaction event\n * @memberof PIXI\n */\nexport class InteractionData\n{\n    /** This point stores the global coords of where the touch/mouse event happened. */\n    public global: Point;\n\n    /** The target Sprite that was interacted with. */\n    public target: DisplayObject;\n\n    /**\n     * When passed to an event handler, this will be the original DOM Event that was captured\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/TouchEvent\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent\n     * @member {MouseEvent|TouchEvent|PointerEvent}\n     */\n    public originalEvent: InteractivePointerEvent;\n\n    /** Unique identifier for this interaction. */\n    public identifier: number;\n\n    /**\n     * Indicates whether or not the pointer device that created the event is the primary pointer.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/isPrimary\n     */\n    public isPrimary: boolean;\n\n    /**\n     * Indicates which button was pressed on the mouse or pointer device to trigger the event.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button\n     */\n    public button: number;\n\n    /**\n     * Indicates which buttons are pressed on the mouse or pointer device when the event is triggered.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n     */\n    public buttons: number;\n\n    /**\n     * The width of the pointer's contact along the x-axis, measured in CSS pixels.\n     * radiusX of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/width\n     */\n    public width: number;\n\n    /**\n     * The height of the pointer's contact along the y-axis, measured in CSS pixels.\n     * radiusY of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/height\n     */\n    public height: number;\n\n    /**\n     * The angle, in degrees, between the pointer device and the screen.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/tiltX\n     */\n    public tiltX: number;\n\n    /**\n     * The angle, in degrees, between the pointer device and the screen.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/tiltY\n     */\n    public tiltY: number;\n\n    /**\n     * The type of pointer that triggered the event.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerType\n     */\n    public pointerType: string;\n\n    /**\n     * Pressure applied by the pointing device during the event. A Touch's force property\n     * will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pressure\n     */\n    public pressure = 0;\n\n    /**\n     * From TouchEvents (not PointerEvents triggered by touches), the rotationAngle of the Touch.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Touch/rotationAngle\n     */\n    public rotationAngle = 0;\n\n    /**\n     * Twist of a stylus pointer.\n     * @see https://w3c.github.io/pointerevents/#pointerevent-interface\n     */\n    public twist = 0;\n\n    /**\n     * Barrel pressure on a stylus pointer.\n     * @see https://w3c.github.io/pointerevents/#pointerevent-interface\n     */\n    public tangentialPressure = 0;\n\n    constructor()\n    {\n        this.global = new Point();\n        this.target = null;\n        this.originalEvent = null;\n        this.identifier = null;\n        this.isPrimary = false;\n        this.button = 0;\n        this.buttons = 0;\n        this.width = 0;\n        this.height = 0;\n        this.tiltX = 0;\n        this.tiltY = 0;\n        this.pointerType = null;\n        this.pressure = 0;\n        this.rotationAngle = 0;\n        this.twist = 0;\n        this.tangentialPressure = 0;\n    }\n\n    /**\n     * The unique identifier of the pointer. It will be the same as `identifier`.\n     * @readonly\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerId\n     */\n    get pointerId(): number\n    {\n        return this.identifier;\n    }\n\n    /**\n     * This will return the local coordinates of the specified displayObject for this InteractionData\n     * @param displayObject - The DisplayObject that you would like the local\n     *  coords off\n     * @param point - A Point object in which to store the value, optional (otherwise\n     *  will create a new point)\n     * @param globalPos - A Point object containing your custom global coords, optional\n     *  (otherwise will use the current global coords)\n     * @returns - A point containing the coordinates of the InteractionData position relative\n     *  to the DisplayObject\n     */\n    public getLocalPosition<P extends IPointData = Point>(displayObject: DisplayObject, point?: P, globalPos?: IPointData): P\n    {\n        return displayObject.worldTransform.applyInverse<P>(globalPos || this.global, point);\n    }\n\n    /**\n     * Copies properties from normalized event data.\n     * @param {Touch|MouseEvent|PointerEvent} event - The normalized event data\n     */\n    public copyEvent(event: Touch | InteractivePointerEvent): void\n    {\n        // isPrimary should only change on touchstart/pointerdown, so we don't want to overwrite\n        // it with \"false\" on later events when our shim for it on touch events might not be\n        // accurate\n        if ('isPrimary' in event && event.isPrimary)\n        {\n            this.isPrimary = true;\n        }\n        this.button = 'button' in event && event.button;\n        // event.buttons is not available in all browsers (ie. Safari), but it does have a non-standard\n        // event.which property instead, which conveys the same information.\n        const buttons = 'buttons' in event && event.buttons;\n\n        this.buttons = Number.isInteger(buttons) ? buttons : 'which' in event && event.which;\n        this.width = 'width' in event && event.width;\n        this.height = 'height' in event && event.height;\n        this.tiltX = 'tiltX' in event && event.tiltX;\n        this.tiltY = 'tiltY' in event && event.tiltY;\n        this.pointerType = 'pointerType' in event && event.pointerType;\n        this.pressure = 'pressure' in event && event.pressure;\n        this.rotationAngle = 'rotationAngle' in event && event.rotationAngle;\n        this.twist = ('twist' in event && event.twist) || 0;\n        this.tangentialPressure = ('tangentialPressure' in event && event.tangentialPressure) || 0;\n    }\n\n    /** Resets the data for pooling. */\n    public reset(): void\n    {\n        // isPrimary is the only property that we really need to reset - everything else is\n        // guaranteed to be overwritten\n        this.isPrimary = false;\n    }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import type { DisplayObject } from '@pixi/display';\nimport type { InteractionData } from './InteractionData';\n\nexport type InteractionCallback = (interactionEvent: InteractionEvent, displayObject: DisplayObject, hit?: boolean) => void;\n\n/**\n * Event class that mimics native DOM events.\n * @memberof PIXI\n */\nexport class InteractionEvent\n{\n    /**\n     * Whether this event will continue propagating in the tree.\n     *\n     * Remaining events for the {@link stopsPropagatingAt} object\n     * will still be dispatched.\n     */\n    public stopped: boolean;\n\n    /**\n     * At which object this event stops propagating.\n     * @private\n     */\n    public stopsPropagatingAt: DisplayObject;\n\n    /**\n     * Whether we already reached the element we want to\n     * stop propagating at. This is important for delayed events,\n     * where we start over deeper in the tree again.\n     * @private\n     */\n    public stopPropagationHint: boolean;\n\n    /**\n     * The object which caused this event to be dispatched.\n     * For listener callback see {@link PIXI.InteractionEvent.currentTarget}.\n     */\n    public target: DisplayObject;\n\n    /** The object whose event listener’s callback is currently being invoked. */\n    public currentTarget: DisplayObject;\n\n    /** Type of the event. */\n    public type: string;\n\n    /** {@link InteractionData} related to this event */\n    public data: InteractionData;\n\n    constructor()\n    {\n        this.stopped = false;\n        this.stopsPropagatingAt = null;\n        this.stopPropagationHint = false;\n        this.target = null;\n        this.currentTarget = null;\n        this.type = null;\n        this.data = null;\n    }\n\n    /** Prevents event from reaching any objects other than the current object. */\n    public stopPropagation(): void\n    {\n        this.stopped = true;\n        this.stopPropagationHint = true;\n        this.stopsPropagatingAt = this.currentTarget;\n    }\n\n    /** Resets the event. */\n    public reset(): void\n    {\n        this.stopped = false;\n        this.stopsPropagatingAt = null;\n        this.stopPropagationHint = false;\n        this.currentTarget = null;\n        this.target = null;\n    }\n}\n", "export interface InteractionTrackingFlags\n{\n    OVER: number;\n    LEFT_DOWN: number;\n    RIGHT_DOWN: number;\n    NONE: number;\n}\n\n/**\n * DisplayObjects with the {@link PIXI.interactiveTarget} mixin use this class to track interactions\n * @class\n * @private\n * @memberof PIXI\n */\nexport class InteractionTrackingData\n{\n    public static FLAGS: Readonly<InteractionTrackingFlags> = Object.freeze({\n        NONE: 0,\n        OVER: 1 << 0,\n        LEFT_DOWN: 1 << 1,\n        RIGHT_DOWN: 1 << 2,\n    });\n\n    private readonly _pointerId: number;\n    private _flags: number;\n\n    /**\n     * @param {number} pointerId - Unique pointer id of the event\n     * @private\n     */\n    constructor(pointerId: number)\n    {\n        this._pointerId = pointerId;\n        this._flags = InteractionTrackingData.FLAGS.NONE;\n    }\n\n    /**\n     *\n     * @private\n     * @param {number} flag - The interaction flag to set\n     * @param {boolean} yn - Should the flag be set or unset\n     */\n    private _doSet(flag: number, yn: boolean): void\n    {\n        if (yn)\n        {\n            this._flags = this._flags | flag;\n        }\n        else\n        {\n            this._flags = this._flags & (~flag);\n        }\n    }\n\n    /**\n     * Unique pointer id of the event\n     * @readonly\n     * @private\n     * @member {number}\n     */\n    get pointerId(): number\n    {\n        return this._pointerId;\n    }\n\n    /**\n     * State of the tracking data, expressed as bit flags\n     * @private\n     * @member {number}\n     */\n    get flags(): number\n    {\n        return this._flags;\n    }\n\n    set flags(flags: number)\n    {\n        this._flags = flags;\n    }\n\n    /**\n     * Is the tracked event inactive (not over or down)?\n     * @private\n     * @member {number}\n     */\n    get none(): boolean\n    {\n        return this._flags === InteractionTrackingData.FLAGS.NONE;\n    }\n\n    /**\n     * Is the tracked event over the DisplayObject?\n     * @private\n     * @member {boolean}\n     */\n    get over(): boolean\n    {\n        return (this._flags & InteractionTrackingData.FLAGS.OVER) !== 0;\n    }\n\n    set over(yn: boolean)\n    {\n        this._doSet(InteractionTrackingData.FLAGS.OVER, yn);\n    }\n\n    /**\n     * Did the right mouse button come down in the DisplayObject?\n     * @private\n     * @member {boolean}\n     */\n    get rightDown(): boolean\n    {\n        return (this._flags & InteractionTrackingData.FLAGS.RIGHT_DOWN) !== 0;\n    }\n\n    set rightDown(yn: boolean)\n    {\n        this._doSet(InteractionTrackingData.FLAGS.RIGHT_DOWN, yn);\n    }\n\n    /**\n     * Did the left mouse button come down in the DisplayObject?\n     * @private\n     * @member {boolean}\n     */\n    get leftDown(): boolean\n    {\n        return (this._flags & InteractionTrackingData.FLAGS.LEFT_DOWN) !== 0;\n    }\n\n    set leftDown(yn: boolean)\n    {\n        this._doSet(InteractionTrackingData.FLAGS.LEFT_DOWN, yn);\n    }\n}\n", "import { Point } from '@pixi/math';\n\nimport type { InteractionEvent, InteractionCallback } from './InteractionEvent';\nimport type { Container, DisplayObject } from '@pixi/display';\n\n/**\n * Strategy how to search through stage tree for interactive objects\n * @memberof PIXI\n */\nexport class TreeSearch\n{\n    private readonly _tempPoint: Point;\n\n    constructor()\n    {\n        this._tempPoint = new Point();\n    }\n\n    /**\n     * Recursive implementation for findHit\n     * @private\n     * @param interactionEvent - event containing the point that\n     *  is tested for collision\n     * @param displayObject - the displayObject\n     *  that will be hit test (recursively crawls its children)\n     * @param func - the function that will be called on each interactive object. The\n     *  interactionEvent, displayObject and hit will be passed to the function\n     * @param hitTest - this indicates if the objects inside should be hit test against the point\n     * @param interactive - Whether the displayObject is interactive\n     * @returns - Returns true if the displayObject hit the point\n     */\n    public recursiveFindHit(interactionEvent: InteractionEvent, displayObject: DisplayObject,\n        func?: InteractionCallback, hitTest?: boolean, interactive?: boolean\n    ): boolean\n    {\n        if (!displayObject || !displayObject.visible)\n        {\n            return false;\n        }\n\n        const point = interactionEvent.data.global;\n\n        // Took a little while to rework this function correctly! But now it is done and nice and optimized! ^_^\n        //\n        // This function will now loop through all objects and then only hit test the objects it HAS\n        // to, not all of them. MUCH faster..\n        // An object will be hit test if the following is true:\n        //\n        // 1: It is interactive.\n        // 2: It belongs to a parent that is interactive AND one of the parents children have not already been hit.\n        //\n        // As another little optimization once an interactive object has been hit we can carry on\n        // through the scenegraph, but we know that there will be no more hits! So we can avoid extra hit tests\n        // A final optimization is that an object is not hit test directly if a child has already been hit.\n\n        interactive = displayObject.interactive || interactive;\n\n        let hit = false;\n        let interactiveParent = interactive;\n\n        // Flag here can set to false if the event is outside the parents hitArea or mask\n        let hitTestChildren = true;\n\n        // If there is a hitArea, no need to test against anything else if the pointer is not within the hitArea\n        // There is also no longer a need to hitTest children.\n        if (displayObject.hitArea)\n        {\n            if (hitTest)\n            {\n                displayObject.worldTransform.applyInverse(point, this._tempPoint);\n                if (!displayObject.hitArea.contains(this._tempPoint.x, this._tempPoint.y))\n                {\n                    hitTest = false;\n                    hitTestChildren = false;\n                }\n                else\n                {\n                    hit = true;\n                }\n            }\n            interactiveParent = false;\n        }\n        // If there is a mask, no need to hitTest against anything else if the pointer is not within the mask.\n        // We still want to hitTestChildren, however, to ensure a mouseout can still be generated.\n        // https://github.com/pixijs/pixi.js/issues/5135\n        else if (displayObject._mask)\n        {\n            if (hitTest)\n            {\n                const maskObject = ((displayObject._mask as any).isMaskData\n                    ? (displayObject._mask as any).maskObject : displayObject._mask) as any;\n\n                if (maskObject && !maskObject.containsPoint?.(point))\n                {\n                    hitTest = false;\n                }\n            }\n        }\n\n        // ** FREE TIP **! If an object is not interactive or has no buttons in it\n        // (such as a game scene!) set interactiveChildren to false for that displayObject.\n        // This will allow PixiJS to completely ignore and bypass checking the displayObjects children.\n        if (hitTestChildren && displayObject.interactiveChildren && (displayObject as Container).children)\n        {\n            const children = (displayObject as Container).children;\n\n            for (let i = children.length - 1; i >= 0; i--)\n            {\n                const child = children[i];\n\n                // time to get recursive.. if this function will return if something is hit..\n                const childHit = this.recursiveFindHit(interactionEvent, child, func, hitTest, interactiveParent);\n\n                if (childHit)\n                {\n                    // its a good idea to check if a child has lost its parent.\n                    // this means it has been removed whilst looping so its best\n                    if (!child.parent)\n                    {\n                        continue;\n                    }\n\n                    // we no longer need to hit test any more objects in this container as we we\n                    // now know the parent has been hit\n                    interactiveParent = false;\n\n                    // If the child is interactive , that means that the object hit was actually\n                    // interactive and not just the child of an interactive object.\n                    // This means we no longer need to hit test anything else. We still need to run\n                    // through all objects, but we don't need to perform any hit tests.\n\n                    if (childHit)\n                    {\n                        if (interactionEvent.target)\n                        {\n                            hitTest = false;\n                        }\n                        hit = true;\n                    }\n                }\n            }\n        }\n\n        // no point running this if the item is not interactive or does not have an interactive parent.\n        if (interactive)\n        {\n            // if we are hit testing (as in we have no hit any objects yet)\n            // We also don't need to worry about hit testing if once of the displayObjects children\n            // has already been hit - but only if it was interactive, otherwise we need to keep\n            // looking for an interactive child, just in case we hit one\n            if (hitTest && !interactionEvent.target)\n            {\n                // already tested against hitArea if it is defined\n                if (!displayObject.hitArea && (displayObject as any).containsPoint)\n                {\n                    if ((displayObject as any).containsPoint(point))\n                    {\n                        hit = true;\n                    }\n                }\n            }\n\n            if (displayObject.interactive)\n            {\n                if (hit && !interactionEvent.target)\n                {\n                    interactionEvent.target = displayObject;\n                }\n\n                if (func)\n                {\n                    func(interactionEvent, displayObject, !!hit);\n                }\n            }\n        }\n\n        return hit;\n    }\n\n    /**\n     * This function is provides a neat way of crawling through the scene graph and running a\n     * specified function on all interactive objects it finds. It will also take care of hit\n     * testing the interactive objects and passes the hit across in the function.\n     * @private\n     * @param interactionEvent - event containing the point that\n     *  is tested for collision\n     * @param displayObject - the displayObject\n     *  that will be hit test (recursively crawls its children)\n     * @param func - the function that will be called on each interactive object. The\n     *  interactionEvent, displayObject and hit will be passed to the function\n     * @param hitTest - this indicates if the objects inside should be hit test against the point\n     * @returns - Returns true if the displayObject hit the point\n     */\n    public findHit(interactionEvent: InteractionEvent, displayObject: DisplayObject,\n        func?: InteractionCallback, hitTest?: boolean\n    ): void\n    {\n        this.recursiveFindHit(interactionEvent, displayObject, func, hitTest, false);\n    }\n}\n", "import type { InteractionTrackingData } from './InteractionTrackingData';\n\ntype Cursor = 'auto'\n| 'default'\n| 'none'\n| 'context-menu'\n| 'help'\n| 'pointer'\n| 'progress'\n| 'wait'\n| 'cell'\n| 'crosshair'\n| 'text'\n| 'vertical-text'\n| 'alias'\n| 'copy'\n| 'move'\n| 'no-drop'\n| 'not-allowed'\n| 'e-resize'\n| 'n-resize'\n| 'ne-resize'\n| 'nw-resize'\n| 's-resize'\n| 'se-resize'\n| 'sw-resize'\n| 'w-resize'\n| 'ns-resize'\n| 'ew-resize'\n| 'nesw-resize'\n| 'col-resize'\n| 'nwse-resize'\n| 'row-resize'\n| 'all-scroll'\n| 'zoom-in'\n| 'zoom-out'\n| 'grab'\n| 'grabbing';\n\nexport interface IHitArea\n{\n    contains(x: number, y: number): boolean;\n}\n\nexport interface InteractiveTarget\n{\n    interactive: boolean;\n    interactiveChildren: boolean;\n    hitArea: IHitArea | null;\n    cursor: Cursor | string;\n    buttonMode: boolean;\n    trackedPointers: {[x: number]: InteractionTrackingData};\n    _trackedPointers: {[x: number]: InteractionTrackingData};\n}\n\n/**\n * Interface for classes that represent a hit area.\n *\n * It is implemented by the following classes:\n * - {@link PIXI.Circle}\n * - {@link PIXI.Ellipse}\n * - {@link PIXI.Polygon}\n * - {@link PIXI.RoundedRectangle}\n * @interface IHitArea\n * @memberof PIXI\n */\n\n/**\n * Checks whether the x and y coordinates given are contained within this area\n * @method\n * @name contains\n * @memberof PIXI.IHitArea#\n * @param {number} x - The X coordinate of the point to test\n * @param {number} y - The Y coordinate of the point to test\n * @returns {boolean} Whether the x/y coordinates are within this area\n */\n\n/**\n * Default property values of interactive objects\n * Used by {@link PIXI.InteractionManager} to automatically give all DisplayObjects these properties\n * @private\n * @name interactiveTarget\n * @type {object}\n * @memberof PIXI\n * @example\n *      function MyObject() {}\n *\n *      Object.assign(\n *          DisplayObject.prototype,\n *          PIXI.interactiveTarget\n *      );\n */\nexport const interactiveTarget: InteractiveTarget = {\n    interactive: false,\n    interactiveChildren: true,\n    hitArea: null,\n\n    /**\n     * If enabled, the mouse cursor use the pointer behavior when hovered over the displayObject if it is interactive\n     * Setting this changes the 'cursor' property to `'pointer'`.\n     * @example\n     * const sprite = new PIXI.Sprite(texture);\n     * sprite.interactive = true;\n     * sprite.buttonMode = true;\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     */\n    get buttonMode(): boolean\n    {\n        return this.cursor === 'pointer';\n    },\n    set buttonMode(value: boolean)\n    {\n        if (value)\n        {\n            this.cursor = 'pointer';\n        }\n        else if (this.cursor === 'pointer')\n        {\n            this.cursor = null;\n        }\n    },\n\n    /**\n     * This defines what cursor mode is used when the mouse cursor\n     * is hovered over the displayObject.\n     * @example\n     * const sprite = new PIXI.Sprite(texture);\n     * sprite.interactive = true;\n     * sprite.cursor = 'wait';\n     * @see https://developer.mozilla.org/en/docs/Web/CSS/cursor\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     */\n    cursor: null,\n\n    /**\n     * Internal set of all active pointers, by identifier\n     * @member {Map<number, InteractionTrackingData>}\n     * @memberof PIXI.DisplayObject#\n     * @private\n     */\n    get trackedPointers()\n    {\n        if (this._trackedPointers === undefined) this._trackedPointers = {};\n\n        return this._trackedPointers;\n    },\n\n    /**\n     * Map of all tracked pointers, by identifier. Use trackedPointers to access.\n     * @private\n     * @type {Map<number, InteractionTrackingData>}\n     */\n    _trackedPointers: undefined,\n};\n", "import { Ticker, UPDATE_PRIORITY } from '@pixi/ticker';\nimport { DisplayObject, TemporaryDisplayObject } from '@pixi/display';\nimport type { InteractivePointerEvent } from './InteractionData';\nimport { InteractionData } from './InteractionData';\nimport type { InteractionCallback } from './InteractionEvent';\nimport { InteractionEvent } from './InteractionEvent';\nimport { InteractionTrackingData } from './InteractionTrackingData';\nimport { TreeSearch } from './TreeSearch';\nimport { EventEmitter } from '@pixi/utils';\nimport { interactiveTarget } from './interactiveTarget';\n\nimport type { AbstractRenderer, ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport type { Point, IPointData } from '@pixi/math';\nimport type { Dict } from '@pixi/utils';\n\n// Mix interactiveTarget into DisplayObject.prototype\nDisplayObject.mixin(interactiveTarget);\n\nconst MOUSE_POINTER_ID = 1;\n\n// Mock interface for hitTestEvent - only used inside hitTest()\ninterface TestInteractionEvent\n{\n    target: DisplayObject;\n    data: {global: Point};\n}\n\n// helpers for hitTest() - only used inside hitTest()\nconst hitTestEvent: TestInteractionEvent = {\n    target: null,\n    data: {\n        global: null,\n    },\n};\n\nexport interface InteractionManagerOptions\n{\n    autoPreventDefault?: boolean;\n    interactionFrequency?: number;\n    useSystemTicker?: boolean;\n}\n\nexport interface DelayedEvent\n{\n    displayObject: DisplayObject;\n    eventString: string;\n    eventData: InteractionEvent;\n}\n\ninterface CrossCSSStyleDeclaration extends CSSStyleDeclaration\n{\n    msContentZooming: string;\n    msTouchAction: string;\n}\n\n/**\n * The interaction manager deals with mouse, touch and pointer events.\n *\n * Any DisplayObject can be interactive if its `interactive` property is set to true.\n *\n * This manager also supports multitouch.\n *\n * An instance of this class is automatically created by default, and can be found at `renderer.plugins.interaction`\n * @memberof PIXI\n */\nexport class InteractionManager extends EventEmitter\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'interaction',\n        type: [\n            ExtensionType.RendererPlugin,\n            ExtensionType.CanvasRendererPlugin,\n        ],\n    };\n\n    /**\n     * Actively tracked InteractionData\n     * @private\n     * @member {Object<number, PIXI.InteractionData>}\n     */\n    public readonly activeInteractionData: { [key: number]: InteractionData };\n\n    /** Does the device support touch events https://www.w3.org/TR/touch-events/ */\n    public readonly supportsTouchEvents: boolean;\n\n    /** Does the device support pointer events https://www.w3.org/Submission/pointer-events/ */\n    public readonly supportsPointerEvents: boolean;\n\n    /**\n     * Pool of unused InteractionData\n     * @private\n     */\n    public interactionDataPool: InteractionData[];\n\n    /**\n     * Internal cached let.\n     * @private\n     */\n    public cursor: string;\n\n    /**\n     * Delayed pointer events. Used to guarantee correct ordering of over/out events.\n     * @private\n     */\n    public delayedEvents: DelayedEvent[];\n\n    /**\n     * TreeSearch component that is used to hitTest stage tree.\n     * @private\n     */\n    public search: TreeSearch;\n\n    /** The renderer this interaction manager works for. */\n    public renderer: AbstractRenderer;\n\n    /**\n     * Should default browser actions automatically be prevented.\n     * Does not apply to pointer events for backwards compatibility as\n     * preventDefault on pointer events stops mouse events from firing.\n     * Thus, for every pointer event, there will always be either a mouse of touch event alongside it.\n     * @default true\n     */\n    public autoPreventDefault: boolean;\n\n    /**\n     * Maximum frequency in milliseconds at which pointer over/out states will be checked by {@link tickerUpdate}.\n     * @default 10\n     */\n    public interactionFrequency: number;\n\n    /** The mouse data. */\n    public mouse: InteractionData;\n\n    /** An event data object to handle all the event tracking/dispatching. */\n    public eventData: InteractionEvent;\n\n    /**\n     * This property determines if mousemove and touchmove events are fired only when the cursor\n     * is over the object.\n     * Setting to true will make things work more in line with how the DOM version works.\n     * Setting to false can make things easier for things like dragging\n     * It is currently set to false as this is how PixiJS used to work. This will be set to true in\n     * future versions of pixi.\n     * @default false\n     */\n    public moveWhenInside: boolean;\n\n    /**\n     * Dictionary of how different cursor modes are handled. Strings are handled as CSS cursor\n     * values, objects are handled as dictionaries of CSS values for interactionDOMElement,\n     * and functions are called instead of changing the CSS.\n     * Default CSS cursor values are provided for 'default' and 'pointer' modes.\n     * @member {Object<string, Object>}\n     */\n    public cursorStyles: Dict<string | ((mode: string) => void) | CSSStyleDeclaration>;\n\n    /** The mode of the cursor that is being used. The value of this is a key from the cursorStyles dictionary. */\n    public currentCursorMode: string;\n\n    /**\n     * The current resolution / device pixel ratio.\n     * @default 1\n     */\n    public resolution: number;\n\n    /** The DOM element to bind to. */\n    protected interactionDOMElement: HTMLElement;\n\n    /** Have events been attached to the dom element? */\n    protected eventsAdded: boolean;\n\n    /** Has the system ticker been added? */\n    protected tickerAdded: boolean;\n\n    /** Is the mouse hovering over the renderer? If working in worker mouse considered to be over renderer by default. */\n    protected mouseOverRenderer: boolean;\n\n    private _useSystemTicker: boolean;\n    private _deltaTime: number;\n    private _didMove: boolean;\n\n    /** Used as a last rendered object in case renderer doesnt have _lastObjectRendered. */\n    private _tempDisplayObject: DisplayObject;\n\n    /**\n     * An options object specifies characteristics about the event listener.\n     * @member {Object<string, boolean>}\n     */\n    private readonly _eventListenerOptions: { capture: true, passive: false };\n\n    /**\n     * @param {PIXI.CanvasRenderer|PIXI.Renderer} renderer - A reference to the current renderer\n     * @param options - The options for the manager.\n     * @param {boolean} [options.autoPreventDefault=true] - Should the manager automatically prevent default browser actions.\n     * @param {number} [options.interactionFrequency=10] - Maximum frequency (ms) at pointer over/out states will be checked.\n     * @param {number} [options.useSystemTicker=true] - Whether to add {@link tickerUpdate} to {@link PIXI.Ticker.system}.\n     */\n    constructor(renderer: AbstractRenderer, options?: InteractionManagerOptions)\n    {\n        super();\n\n        options = options || {};\n\n        this.renderer = renderer;\n        this.autoPreventDefault = options.autoPreventDefault !== undefined ? options.autoPreventDefault : true;\n        this.interactionFrequency = options.interactionFrequency || 10;\n        this.mouse = new InteractionData();\n        this.mouse.identifier = MOUSE_POINTER_ID;\n\n        // setting the mouse to start off far off screen will mean that mouse over does\n        //  not get called before we even move the mouse.\n        this.mouse.global.set(-999999);\n\n        this.activeInteractionData = {};\n        this.activeInteractionData[MOUSE_POINTER_ID] = this.mouse;\n        this.interactionDataPool = [];\n        this.eventData = new InteractionEvent();\n        this.interactionDOMElement = null;\n\n        this.moveWhenInside = false;\n        this.eventsAdded = false;\n        this.tickerAdded = false;\n        this.mouseOverRenderer = !('PointerEvent' in globalThis);\n        this.supportsTouchEvents = 'ontouchstart' in globalThis;\n        this.supportsPointerEvents = !!globalThis.PointerEvent;\n\n        // this will make it so that you don't have to call bind all the time\n\n        this.onPointerUp = this.onPointerUp.bind(this);\n        this.processPointerUp = this.processPointerUp.bind(this);\n\n        this.onPointerCancel = this.onPointerCancel.bind(this);\n        this.processPointerCancel = this.processPointerCancel.bind(this);\n\n        this.onPointerDown = this.onPointerDown.bind(this);\n        this.processPointerDown = this.processPointerDown.bind(this);\n\n        this.onPointerMove = this.onPointerMove.bind(this);\n        this.processPointerMove = this.processPointerMove.bind(this);\n\n        this.onPointerOut = this.onPointerOut.bind(this);\n        this.processPointerOverOut = this.processPointerOverOut.bind(this);\n\n        this.onPointerOver = this.onPointerOver.bind(this);\n\n        this.cursorStyles = {\n            default: 'inherit',\n            pointer: 'pointer',\n        };\n        this.currentCursorMode = null;\n        this.cursor = null;\n\n        this.resolution = 1;\n        this.delayedEvents = [];\n        this.search = new TreeSearch();\n\n        this._tempDisplayObject = new TemporaryDisplayObject();\n        this._eventListenerOptions = { capture: true, passive: false };\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is pressed on the display\n         * object.\n         * @event PIXI.InteractionManager#mousedown\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is pressed\n         * on the display object.\n         * @event PIXI.InteractionManager#rightdown\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is released over the display\n         * object.\n         * @event PIXI.InteractionManager#mouseup\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is released\n         * over the display object.\n         * @event PIXI.InteractionManager#rightup\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is pressed and released on\n         * the display object.\n         * @event PIXI.InteractionManager#click\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is pressed\n         * and released on the display object.\n         * @event PIXI.InteractionManager#rightclick\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is released outside the\n         * display object that initially registered a\n         * [mousedown]{@link PIXI.InteractionManager#event:mousedown}.\n         * @event PIXI.InteractionManager#mouseupoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is released\n         * outside the display object that initially registered a\n         * [rightdown]{@link PIXI.InteractionManager#event:rightdown}.\n         * @event PIXI.InteractionManager#rightupoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device (usually a mouse) is moved while over the display object\n         * @event PIXI.InteractionManager#mousemove\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device (usually a mouse) is moved onto the display object\n         * @event PIXI.InteractionManager#mouseover\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device (usually a mouse) is moved off the display object\n         * @event PIXI.InteractionManager#mouseout\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is pressed on the display object.\n         * @event PIXI.InteractionManager#pointerdown\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is released over the display object.\n         * Not always fired when some buttons are held down while others are released. In those cases,\n         * use [mousedown]{@link PIXI.InteractionManager#event:mousedown} and\n         * [mouseup]{@link PIXI.InteractionManager#event:mouseup} instead.\n         * @event PIXI.InteractionManager#pointerup\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when the operating system cancels a pointer event\n         * @event PIXI.InteractionManager#pointercancel\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is pressed and released on the display object.\n         * @event PIXI.InteractionManager#pointertap\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is released outside the display object that initially\n         * registered a [pointerdown]{@link PIXI.InteractionManager#event:pointerdown}.\n         * @event PIXI.InteractionManager#pointerupoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device is moved while over the display object\n         * @event PIXI.InteractionManager#pointermove\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device is moved onto the display object\n         * @event PIXI.InteractionManager#pointerover\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device is moved off the display object\n         * @event PIXI.InteractionManager#pointerout\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is placed on the display object.\n         * @event PIXI.InteractionManager#touchstart\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is removed from the display object.\n         * @event PIXI.InteractionManager#touchend\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when the operating system cancels a touch\n         * @event PIXI.InteractionManager#touchcancel\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is placed and removed from the display object.\n         * @event PIXI.InteractionManager#tap\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is removed outside of the display object that initially\n         * registered a [touchstart]{@link PIXI.InteractionManager#event:touchstart}.\n         * @event PIXI.InteractionManager#touchendoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is moved along the display object.\n         * @event PIXI.InteractionManager#touchmove\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is pressed on the display.\n         * object. DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#mousedown\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is pressed\n         * on the display object. DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#rightdown\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is released over the display\n         * object. DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#mouseup\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is released\n         * over the display object. DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#rightup\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is pressed and released on\n         * the display object. DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#click\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is pressed\n         * and released on the display object. DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#rightclick\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button (usually a mouse left-button) is released outside the\n         * display object that initially registered a\n         * [mousedown]{@link PIXI.DisplayObject#event:mousedown}.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#mouseupoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device secondary button (usually a mouse right-button) is released\n         * outside the display object that initially registered a\n         * [rightdown]{@link PIXI.DisplayObject#event:rightdown}.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#rightupoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device (usually a mouse) is moved while over the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#mousemove\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device (usually a mouse) is moved onto the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#mouseover\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device (usually a mouse) is moved off the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#mouseout\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is pressed on the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointerdown\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is released over the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointerup\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when the operating system cancels a pointer event.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointercancel\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is pressed and released on the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointertap\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device button is released outside the display object that initially\n         * registered a [pointerdown]{@link PIXI.DisplayObject#event:pointerdown}.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointerupoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device is moved while over the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointermove\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device is moved onto the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointerover\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a pointer device is moved off the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#pointerout\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is placed on the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#touchstart\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is removed from the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#touchend\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when the operating system cancels a touch.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#touchcancel\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is placed and removed from the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#tap\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is removed outside of the display object that initially\n         * registered a [touchstart]{@link PIXI.DisplayObject#event:touchstart}.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#touchendoutside\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        /**\n         * Fired when a touch point is moved along the display object.\n         * DisplayObject's `interactive` property must be set to `true` to fire event.\n         *\n         * This comes from the @pixi/interaction package.\n         * @event PIXI.DisplayObject#touchmove\n         * @param {PIXI.InteractionEvent} event - Interaction event\n         */\n\n        this._useSystemTicker = options.useSystemTicker !== undefined ? options.useSystemTicker : true;\n\n        this.setTargetElement(this.renderer.view, this.renderer.resolution);\n    }\n\n    /**\n     * Should the InteractionManager automatically add {@link tickerUpdate} to {@link PIXI.Ticker.system}.\n     * @default true\n     */\n    get useSystemTicker(): boolean\n    {\n        return this._useSystemTicker;\n    }\n    set useSystemTicker(useSystemTicker: boolean)\n    {\n        this._useSystemTicker = useSystemTicker;\n\n        if (useSystemTicker)\n        {\n            this.addTickerListener();\n        }\n        else\n        {\n            this.removeTickerListener();\n        }\n    }\n\n    /**\n     * Last rendered object or temp object.\n     * @readonly\n     * @protected\n     */\n    get lastObjectRendered(): DisplayObject\n    {\n        return (this.renderer._lastObjectRendered as DisplayObject) || this._tempDisplayObject;\n    }\n\n    /**\n     * Hit tests a point against the display tree, returning the first interactive object that is hit.\n     * @param globalPoint - A point to hit test with, in global space.\n     * @param root - The root display object to start from. If omitted, defaults\n     * to the last rendered root of the associated renderer.\n     * @returns - The hit display object, if any.\n     */\n    public hitTest(globalPoint: Point, root?: DisplayObject): DisplayObject\n    {\n        // clear the target for our hit test\n        hitTestEvent.target = null;\n        // assign the global point\n        hitTestEvent.data.global = globalPoint;\n        // ensure safety of the root\n        if (!root)\n        {\n            root = this.lastObjectRendered;\n        }\n        // run the hit test\n        this.processInteractive(hitTestEvent as InteractionEvent, root, null, true);\n        // return our found object - it'll be null if we didn't hit anything\n\n        return hitTestEvent.target;\n    }\n\n    /**\n     * Sets the DOM element which will receive mouse/touch events. This is useful for when you have\n     * other DOM elements on top of the renderers Canvas element. With this you'll be bale to delegate\n     * another DOM element to receive those events.\n     * @param element - the DOM element which will receive mouse and touch events.\n     * @param resolution - The resolution / device pixel ratio of the new element (relative to the canvas).\n     */\n    public setTargetElement(element: HTMLElement, resolution = 1): void\n    {\n        this.removeTickerListener();\n\n        this.removeEvents();\n\n        this.interactionDOMElement = element;\n\n        this.resolution = resolution;\n\n        this.addEvents();\n\n        this.addTickerListener();\n    }\n\n    /** Adds the ticker listener. */\n    private addTickerListener(): void\n    {\n        if (this.tickerAdded || !this.interactionDOMElement || !this._useSystemTicker)\n        {\n            return;\n        }\n\n        Ticker.system.add(this.tickerUpdate, this, UPDATE_PRIORITY.INTERACTION);\n\n        this.tickerAdded = true;\n    }\n\n    /** Removes the ticker listener. */\n    private removeTickerListener(): void\n    {\n        if (!this.tickerAdded)\n        {\n            return;\n        }\n\n        Ticker.system.remove(this.tickerUpdate, this);\n\n        this.tickerAdded = false;\n    }\n\n    /** Registers all the DOM events. */\n    private addEvents(): void\n    {\n        if (this.eventsAdded || !this.interactionDOMElement)\n        {\n            return;\n        }\n\n        const style = this.interactionDOMElement.style as CrossCSSStyleDeclaration;\n\n        if ((globalThis.navigator as any).msPointerEnabled)\n        {\n            style.msContentZooming = 'none';\n            style.msTouchAction = 'none';\n        }\n        else if (this.supportsPointerEvents)\n        {\n            style.touchAction = 'none';\n        }\n\n        /*\n         * These events are added first, so that if pointer events are normalized, they are fired\n         * in the same order as non-normalized events. ie. pointer event 1st, mouse / touch 2nd\n         */\n        if (this.supportsPointerEvents)\n        {\n            globalThis.document.addEventListener('pointermove', this.onPointerMove, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('pointerdown', this.onPointerDown, this._eventListenerOptions);\n            // pointerout is fired in addition to pointerup (for touch events) and pointercancel\n            // we already handle those, so for the purposes of what we do in onPointerOut, we only\n            // care about the pointerleave event\n            this.interactionDOMElement.addEventListener('pointerleave', this.onPointerOut, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('pointerover', this.onPointerOver, this._eventListenerOptions);\n            globalThis.addEventListener('pointercancel', this.onPointerCancel, this._eventListenerOptions);\n            globalThis.addEventListener('pointerup', this.onPointerUp, this._eventListenerOptions);\n        }\n        else\n        {\n            globalThis.document.addEventListener('mousemove', this.onPointerMove, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('mousedown', this.onPointerDown, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('mouseout', this.onPointerOut, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('mouseover', this.onPointerOver, this._eventListenerOptions);\n            globalThis.addEventListener('mouseup', this.onPointerUp, this._eventListenerOptions);\n        }\n\n        // always look directly for touch events so that we can provide original data\n        // In a future version we should change this to being just a fallback and rely solely on\n        // PointerEvents whenever available\n        if (this.supportsTouchEvents)\n        {\n            this.interactionDOMElement.addEventListener('touchstart', this.onPointerDown, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('touchcancel', this.onPointerCancel, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('touchend', this.onPointerUp, this._eventListenerOptions);\n            this.interactionDOMElement.addEventListener('touchmove', this.onPointerMove, this._eventListenerOptions);\n        }\n\n        this.eventsAdded = true;\n    }\n\n    /** Removes all the DOM events that were previously registered. */\n    private removeEvents(): void\n    {\n        if (!this.eventsAdded || !this.interactionDOMElement)\n        {\n            return;\n        }\n\n        const style = this.interactionDOMElement.style as CrossCSSStyleDeclaration;\n\n        if ((globalThis.navigator as any).msPointerEnabled)\n        {\n            style.msContentZooming = '';\n            style.msTouchAction = '';\n        }\n        else if (this.supportsPointerEvents)\n        {\n            style.touchAction = '';\n        }\n\n        if (this.supportsPointerEvents)\n        {\n            globalThis.document.removeEventListener('pointermove', this.onPointerMove, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('pointerdown', this.onPointerDown, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('pointerleave', this.onPointerOut, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('pointerover', this.onPointerOver, this._eventListenerOptions);\n            globalThis.removeEventListener('pointercancel', this.onPointerCancel, this._eventListenerOptions);\n            globalThis.removeEventListener('pointerup', this.onPointerUp, this._eventListenerOptions);\n        }\n        else\n        {\n            globalThis.document.removeEventListener('mousemove', this.onPointerMove, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('mousedown', this.onPointerDown, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('mouseout', this.onPointerOut, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('mouseover', this.onPointerOver, this._eventListenerOptions);\n            globalThis.removeEventListener('mouseup', this.onPointerUp, this._eventListenerOptions);\n        }\n\n        if (this.supportsTouchEvents)\n        {\n            this.interactionDOMElement.removeEventListener('touchstart', this.onPointerDown, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('touchcancel', this.onPointerCancel, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('touchend', this.onPointerUp, this._eventListenerOptions);\n            this.interactionDOMElement.removeEventListener('touchmove', this.onPointerMove, this._eventListenerOptions);\n        }\n\n        this.interactionDOMElement = null;\n\n        this.eventsAdded = false;\n    }\n\n    /**\n     * Updates the state of interactive objects if at least {@link interactionFrequency}\n     * milliseconds have passed since the last invocation.\n     *\n     * Invoked by a throttled ticker update from {@link PIXI.Ticker.system}.\n     * @param deltaTime - time delta since the last call\n     */\n    public tickerUpdate(deltaTime: number): void\n    {\n        this._deltaTime += deltaTime;\n\n        if (this._deltaTime < this.interactionFrequency)\n        {\n            return;\n        }\n\n        this._deltaTime = 0;\n\n        this.update();\n    }\n\n    /** Updates the state of interactive objects. */\n    public update(): void\n    {\n        if (!this.interactionDOMElement)\n        {\n            return;\n        }\n\n        // if the user move the mouse this check has already been done using the mouse move!\n        if (this._didMove)\n        {\n            this._didMove = false;\n\n            return;\n        }\n\n        this.cursor = null;\n\n        // Resets the flag as set by a stopPropagation call. This flag is usually reset by a user interaction of any kind,\n        // but there was a scenario of a display object moving under a static mouse cursor.\n        // In this case, mouseover and mouseevents would not pass the flag test in dispatchEvent function\n        for (const k in this.activeInteractionData)\n        {\n            // eslint-disable-next-line no-prototype-builtins\n            if (this.activeInteractionData.hasOwnProperty(k))\n            {\n                const interactionData = this.activeInteractionData[k];\n\n                if (interactionData.originalEvent && interactionData.pointerType !== 'touch')\n                {\n                    const interactionEvent = this.configureInteractionEventForDOMEvent(\n                        this.eventData,\n                        interactionData.originalEvent as PointerEvent,\n                        interactionData\n                    );\n\n                    this.processInteractive(\n                        interactionEvent,\n                        this.lastObjectRendered,\n                        this.processPointerOverOut,\n                        true\n                    );\n                }\n            }\n        }\n\n        this.setCursorMode(this.cursor);\n    }\n\n    /**\n     * Sets the current cursor mode, handling any callbacks or CSS style changes.\n     * @param mode - cursor mode, a key from the cursorStyles dictionary\n     */\n    public setCursorMode(mode: string): void\n    {\n        mode = mode || 'default';\n        let applyStyles = true;\n\n        // offscreen canvas does not support setting styles, but cursor modes can be functions,\n        // in order to handle pixi rendered cursors, so we can't bail\n        if (globalThis.OffscreenCanvas && this.interactionDOMElement instanceof OffscreenCanvas)\n        {\n            applyStyles = false;\n        }\n        // if the mode didn't actually change, bail early\n        if (this.currentCursorMode === mode)\n        {\n            return;\n        }\n        this.currentCursorMode = mode;\n        const style = this.cursorStyles[mode];\n\n        // only do things if there is a cursor style for it\n        if (style)\n        {\n            switch (typeof style)\n            {\n                case 'string':\n                    // string styles are handled as cursor CSS\n                    if (applyStyles)\n                    {\n                        this.interactionDOMElement.style.cursor = style;\n                    }\n                    break;\n                case 'function':\n                    // functions are just called, and passed the cursor mode\n                    style(mode);\n                    break;\n                case 'object':\n                    // if it is an object, assume that it is a dictionary of CSS styles,\n                    // apply it to the interactionDOMElement\n                    if (applyStyles)\n                    {\n                        Object.assign(this.interactionDOMElement.style, style);\n                    }\n                    break;\n            }\n        }\n        else if (applyStyles && typeof mode === 'string' && !Object.prototype.hasOwnProperty.call(this.cursorStyles, mode))\n        {\n            // if it mode is a string (not a Symbol) and cursorStyles doesn't have any entry\n            // for the mode, then assume that the dev wants it to be CSS for the cursor.\n            this.interactionDOMElement.style.cursor = mode;\n        }\n    }\n\n    /**\n     * Dispatches an event on the display object that was interacted with.\n     * @param displayObject - the display object in question\n     * @param eventString - the name of the event (e.g, mousedown)\n     * @param eventData - the event data object\n     */\n    private dispatchEvent(displayObject: DisplayObject, eventString: string, eventData: InteractionEvent): void\n    {\n        // Even if the event was stopped, at least dispatch any remaining events\n        // for the same display object.\n        if (!eventData.stopPropagationHint || displayObject === eventData.stopsPropagatingAt)\n        {\n            eventData.currentTarget = displayObject;\n            eventData.type = eventString;\n\n            displayObject.emit(eventString, eventData);\n\n            if ((displayObject as any)[eventString])\n            {\n                (displayObject as any)[eventString](eventData);\n            }\n        }\n    }\n\n    /**\n     * Puts a event on a queue to be dispatched later. This is used to guarantee correct\n     * ordering of over/out events.\n     * @param displayObject - the display object in question\n     * @param eventString - the name of the event (e.g, mousedown)\n     * @param eventData - the event data object\n     */\n    private delayDispatchEvent(displayObject: DisplayObject, eventString: string, eventData: InteractionEvent): void\n    {\n        this.delayedEvents.push({ displayObject, eventString, eventData });\n    }\n\n    /**\n     * Maps x and y coords from a DOM object and maps them correctly to the PixiJS view. The\n     * resulting value is stored in the point. This takes into account the fact that the DOM\n     * element could be scaled and positioned anywhere on the screen.\n     * @param point - the point that the result will be stored in\n     * @param x - the x coord of the position to map\n     * @param y - the y coord of the position to map\n     */\n    public mapPositionToPoint(point: IPointData, x: number, y: number): void\n    {\n        let rect;\n\n        // IE 11 fix\n        if (!this.interactionDOMElement.parentElement)\n        {\n            rect = {\n                x: 0,\n                y: 0,\n                width: (this.interactionDOMElement as any).width,\n                height: (this.interactionDOMElement as any).height,\n                left: 0,\n                top: 0\n            };\n        }\n        else\n        {\n            rect = this.interactionDOMElement.getBoundingClientRect();\n        }\n\n        const resolutionMultiplier = 1.0 / this.resolution;\n\n        point.x = ((x - rect.left) * ((this.interactionDOMElement as any).width / rect.width)) * resolutionMultiplier;\n        point.y = ((y - rect.top) * ((this.interactionDOMElement as any).height / rect.height)) * resolutionMultiplier;\n    }\n\n    /**\n     * This function is provides a neat way of crawling through the scene graph and running a\n     * specified function on all interactive objects it finds. It will also take care of hit\n     * testing the interactive objects and passes the hit across in the function.\n     * @protected\n     * @param interactionEvent - event containing the point that\n     *  is tested for collision\n     * @param displayObject - the displayObject\n     *  that will be hit test (recursively crawls its children)\n     * @param func - the function that will be called on each interactive object. The\n     *  interactionEvent, displayObject and hit will be passed to the function\n     * @param hitTest - indicates whether we want to calculate hits\n     *  or just iterate through all interactive objects\n     */\n    public processInteractive(interactionEvent: InteractionEvent, displayObject: DisplayObject,\n        func?: InteractionCallback, hitTest?: boolean\n    ): void\n    {\n        const hit = this.search.findHit(interactionEvent, displayObject, func, hitTest);\n\n        const delayedEvents = this.delayedEvents;\n\n        if (!delayedEvents.length)\n        {\n            return hit;\n        }\n        // Reset the propagation hint, because we start deeper in the tree again.\n        interactionEvent.stopPropagationHint = false;\n\n        const delayedLen = delayedEvents.length;\n\n        this.delayedEvents = [];\n\n        for (let i = 0; i < delayedLen; i++)\n        {\n            const { displayObject, eventString, eventData } = delayedEvents[i];\n\n            // When we reach the object we wanted to stop propagating at,\n            // set the propagation hint.\n            if (eventData.stopsPropagatingAt === displayObject)\n            {\n                eventData.stopPropagationHint = true;\n            }\n\n            this.dispatchEvent(displayObject, eventString, eventData);\n        }\n\n        return hit;\n    }\n\n    /**\n     * Is called when the pointer button is pressed down on the renderer element\n     * @param originalEvent - The DOM event of a pointer button being pressed down\n     */\n    private onPointerDown(originalEvent: InteractivePointerEvent): void\n    {\n        // if we support touch events, then only use those for touch events, not pointer events\n        if (this.supportsTouchEvents && (originalEvent as PointerEvent).pointerType === 'touch') return;\n\n        const events = this.normalizeToPointerData(originalEvent);\n\n        /*\n         * No need to prevent default on natural pointer events, as there are no side effects\n         * Normalized events, however, may have the double mousedown/touchstart issue on the native android browser,\n         * so still need to be prevented.\n         */\n\n        // Guaranteed that there will be at least one event in events, and all events must have the same pointer type\n\n        if (this.autoPreventDefault && (events[0] as any).isNormalized)\n        {\n            const cancelable = originalEvent.cancelable || !('cancelable' in originalEvent);\n\n            if (cancelable)\n            {\n                originalEvent.preventDefault();\n            }\n        }\n\n        const eventLen = events.length;\n\n        for (let i = 0; i < eventLen; i++)\n        {\n            const event = events[i];\n\n            const interactionData = this.getInteractionDataForPointerId(event);\n\n            const interactionEvent = this.configureInteractionEventForDOMEvent(this.eventData, event, interactionData);\n\n            interactionEvent.data.originalEvent = originalEvent;\n\n            this.processInteractive(interactionEvent, this.lastObjectRendered, this.processPointerDown, true);\n\n            this.emit('pointerdown', interactionEvent);\n            if (event.pointerType === 'touch')\n            {\n                this.emit('touchstart', interactionEvent);\n            }\n            // emit a mouse event for \"pen\" pointers, the way a browser would emit a fallback event\n            else if (event.pointerType === 'mouse' || event.pointerType === 'pen')\n            {\n                const isRightButton = event.button === 2;\n\n                this.emit(isRightButton ? 'rightdown' : 'mousedown', this.eventData);\n            }\n        }\n    }\n\n    /**\n     * Processes the result of the pointer down check and dispatches the event if need be\n     * @param interactionEvent - The interaction event wrapping the DOM event\n     * @param displayObject - The display object that was tested\n     * @param hit - the result of the hit test on the display object\n     */\n    private processPointerDown(interactionEvent: InteractionEvent, displayObject: DisplayObject, hit: boolean): void\n    {\n        const data = interactionEvent.data;\n        const id = interactionEvent.data.identifier;\n\n        if (hit)\n        {\n            if (!displayObject.trackedPointers[id])\n            {\n                displayObject.trackedPointers[id] = new InteractionTrackingData(id);\n            }\n            this.dispatchEvent(displayObject, 'pointerdown', interactionEvent);\n\n            if (data.pointerType === 'touch')\n            {\n                this.dispatchEvent(displayObject, 'touchstart', interactionEvent);\n            }\n            else if (data.pointerType === 'mouse' || data.pointerType === 'pen')\n            {\n                const isRightButton = data.button === 2;\n\n                if (isRightButton)\n                {\n                    displayObject.trackedPointers[id].rightDown = true;\n                }\n                else\n                {\n                    displayObject.trackedPointers[id].leftDown = true;\n                }\n\n                this.dispatchEvent(displayObject, isRightButton ? 'rightdown' : 'mousedown', interactionEvent);\n            }\n        }\n    }\n\n    /**\n     * Is called when the pointer button is released on the renderer element\n     * @param originalEvent - The DOM event of a pointer button being released\n     * @param cancelled - true if the pointer is cancelled\n     * @param func - Function passed to {@link processInteractive}\n     */\n    private onPointerComplete(originalEvent: InteractivePointerEvent, cancelled: boolean, func: InteractionCallback): void\n    {\n        const events = this.normalizeToPointerData(originalEvent);\n\n        const eventLen = events.length;\n\n        // if the event wasn't targeting our canvas, then consider it to be pointerupoutside\n        // in all cases (unless it was a pointercancel)\n        let target = originalEvent.target;\n\n        // if in shadow DOM use composedPath to access target\n        if (originalEvent.composedPath && originalEvent.composedPath().length > 0)\n        {\n            target = originalEvent.composedPath()[0];\n        }\n\n        const eventAppend = target !== this.interactionDOMElement ? 'outside' : '';\n\n        for (let i = 0; i < eventLen; i++)\n        {\n            const event = events[i];\n\n            const interactionData = this.getInteractionDataForPointerId(event);\n\n            const interactionEvent = this.configureInteractionEventForDOMEvent(this.eventData, event, interactionData);\n\n            interactionEvent.data.originalEvent = originalEvent;\n\n            // perform hit testing for events targeting our canvas or cancel events\n            this.processInteractive(interactionEvent, this.lastObjectRendered, func, cancelled || !eventAppend);\n\n            this.emit(cancelled ? 'pointercancel' : `pointerup${eventAppend}`, interactionEvent);\n\n            if (event.pointerType === 'mouse' || event.pointerType === 'pen')\n            {\n                const isRightButton = event.button === 2;\n\n                this.emit(isRightButton ? `rightup${eventAppend}` : `mouseup${eventAppend}`, interactionEvent);\n            }\n            else if (event.pointerType === 'touch')\n            {\n                this.emit(cancelled ? 'touchcancel' : `touchend${eventAppend}`, interactionEvent);\n                this.releaseInteractionDataForPointerId(event.pointerId);\n            }\n        }\n    }\n\n    /**\n     * Is called when the pointer button is cancelled\n     * @param event - The DOM event of a pointer button being released\n     */\n    private onPointerCancel(event: InteractivePointerEvent): void\n    {\n        // if we support touch events, then only use those for touch events, not pointer events\n        if (this.supportsTouchEvents && (event as PointerEvent).pointerType === 'touch') return;\n\n        this.onPointerComplete(event, true, this.processPointerCancel);\n    }\n\n    /**\n     * Processes the result of the pointer cancel check and dispatches the event if need be\n     * @param interactionEvent - The interaction event wrapping the DOM event\n     * @param displayObject - The display object that was tested\n     */\n    private processPointerCancel(interactionEvent: InteractionEvent, displayObject: DisplayObject): void\n    {\n        const data = interactionEvent.data;\n\n        const id = interactionEvent.data.identifier;\n\n        if (displayObject.trackedPointers[id] !== undefined)\n        {\n            delete displayObject.trackedPointers[id];\n            this.dispatchEvent(displayObject, 'pointercancel', interactionEvent);\n\n            if (data.pointerType === 'touch')\n            {\n                this.dispatchEvent(displayObject, 'touchcancel', interactionEvent);\n            }\n        }\n    }\n\n    /**\n     * Is called when the pointer button is released on the renderer element\n     * @param event - The DOM event of a pointer button being released\n     */\n    private onPointerUp(event: InteractivePointerEvent): void\n    {\n        // if we support touch events, then only use those for touch events, not pointer events\n        if (this.supportsTouchEvents && (event as PointerEvent).pointerType === 'touch') return;\n\n        this.onPointerComplete(event, false, this.processPointerUp);\n    }\n\n    /**\n     * Processes the result of the pointer up check and dispatches the event if need be\n     * @param interactionEvent - The interaction event wrapping the DOM event\n     * @param displayObject - The display object that was tested\n     * @param hit - the result of the hit test on the display object\n     */\n    private processPointerUp(interactionEvent: InteractionEvent, displayObject: DisplayObject, hit: boolean): void\n    {\n        const data = interactionEvent.data;\n\n        const id = interactionEvent.data.identifier;\n\n        const trackingData = displayObject.trackedPointers[id];\n\n        const isTouch = data.pointerType === 'touch';\n\n        const isMouse = (data.pointerType === 'mouse' || data.pointerType === 'pen');\n        // need to track mouse down status in the mouse block so that we can emit\n        // event in a later block\n        let isMouseTap = false;\n\n        // Mouse only\n        if (isMouse)\n        {\n            const isRightButton = data.button === 2;\n\n            const flags = InteractionTrackingData.FLAGS;\n\n            const test = isRightButton ? flags.RIGHT_DOWN : flags.LEFT_DOWN;\n\n            const isDown = trackingData !== undefined && (trackingData.flags & test);\n\n            if (hit)\n            {\n                this.dispatchEvent(displayObject, isRightButton ? 'rightup' : 'mouseup', interactionEvent);\n\n                if (isDown)\n                {\n                    this.dispatchEvent(displayObject, isRightButton ? 'rightclick' : 'click', interactionEvent);\n                    // because we can confirm that the mousedown happened on this object, flag for later emit of pointertap\n                    isMouseTap = true;\n                }\n            }\n            else if (isDown)\n            {\n                this.dispatchEvent(displayObject, isRightButton ? 'rightupoutside' : 'mouseupoutside', interactionEvent);\n            }\n            // update the down state of the tracking data\n            if (trackingData)\n            {\n                if (isRightButton)\n                {\n                    trackingData.rightDown = false;\n                }\n                else\n                {\n                    trackingData.leftDown = false;\n                }\n            }\n        }\n\n        // Pointers and Touches, and Mouse\n        if (hit)\n        {\n            this.dispatchEvent(displayObject, 'pointerup', interactionEvent);\n            if (isTouch) this.dispatchEvent(displayObject, 'touchend', interactionEvent);\n\n            if (trackingData)\n            {\n                // emit pointertap if not a mouse, or if the mouse block decided it was a tap\n                if (!isMouse || isMouseTap)\n                {\n                    this.dispatchEvent(displayObject, 'pointertap', interactionEvent);\n                }\n                if (isTouch)\n                {\n                    this.dispatchEvent(displayObject, 'tap', interactionEvent);\n                    // touches are no longer over (if they ever were) when we get the touchend\n                    // so we should ensure that we don't keep pretending that they are\n                    trackingData.over = false;\n                }\n            }\n        }\n        else if (trackingData)\n        {\n            this.dispatchEvent(displayObject, 'pointerupoutside', interactionEvent);\n            if (isTouch) this.dispatchEvent(displayObject, 'touchendoutside', interactionEvent);\n        }\n        // Only remove the tracking data if there is no over/down state still associated with it\n        if (trackingData && trackingData.none)\n        {\n            delete displayObject.trackedPointers[id];\n        }\n    }\n\n    /**\n     * Is called when the pointer moves across the renderer element\n     * @param originalEvent - The DOM event of a pointer moving\n     */\n    private onPointerMove(originalEvent: InteractivePointerEvent): void\n    {\n        // if we support touch events, then only use those for touch events, not pointer events\n        if (this.supportsTouchEvents && (originalEvent as PointerEvent).pointerType === 'touch') return;\n\n        const events = this.normalizeToPointerData(originalEvent);\n\n        if (events[0].pointerType === 'mouse' || events[0].pointerType === 'pen')\n        {\n            this._didMove = true;\n\n            this.cursor = null;\n        }\n\n        const eventLen = events.length;\n\n        for (let i = 0; i < eventLen; i++)\n        {\n            const event = events[i];\n\n            const interactionData = this.getInteractionDataForPointerId(event);\n\n            const interactionEvent = this.configureInteractionEventForDOMEvent(this.eventData, event, interactionData);\n\n            interactionEvent.data.originalEvent = originalEvent;\n\n            this.processInteractive(interactionEvent, this.lastObjectRendered, this.processPointerMove, true);\n\n            this.emit('pointermove', interactionEvent);\n            if (event.pointerType === 'touch') this.emit('touchmove', interactionEvent);\n            if (event.pointerType === 'mouse' || event.pointerType === 'pen') this.emit('mousemove', interactionEvent);\n        }\n\n        if (events[0].pointerType === 'mouse')\n        {\n            this.setCursorMode(this.cursor);\n\n            // TODO BUG for parents interactive object (border order issue)\n        }\n    }\n\n    /**\n     * Processes the result of the pointer move check and dispatches the event if need be\n     * @param interactionEvent - The interaction event wrapping the DOM event\n     * @param displayObject - The display object that was tested\n     * @param hit - the result of the hit test on the display object\n     */\n    private processPointerMove(interactionEvent: InteractionEvent, displayObject: DisplayObject, hit: boolean): void\n    {\n        const data = interactionEvent.data;\n\n        const isTouch = data.pointerType === 'touch';\n\n        const isMouse = (data.pointerType === 'mouse' || data.pointerType === 'pen');\n\n        if (isMouse)\n        {\n            this.processPointerOverOut(interactionEvent, displayObject, hit);\n        }\n\n        if (!this.moveWhenInside || hit)\n        {\n            this.dispatchEvent(displayObject, 'pointermove', interactionEvent);\n            if (isTouch) this.dispatchEvent(displayObject, 'touchmove', interactionEvent);\n            if (isMouse) this.dispatchEvent(displayObject, 'mousemove', interactionEvent);\n        }\n    }\n\n    /**\n     * Is called when the pointer is moved out of the renderer element\n     * @private\n     * @param {PointerEvent} originalEvent - The DOM event of a pointer being moved out\n     */\n    private onPointerOut(originalEvent: InteractivePointerEvent): void\n    {\n        // if we support touch events, then only use those for touch events, not pointer events\n        if (this.supportsTouchEvents && (originalEvent as PointerEvent).pointerType === 'touch') return;\n\n        const events = this.normalizeToPointerData(originalEvent);\n\n        // Only mouse and pointer can call onPointerOut, so events will always be length 1\n        const event = events[0];\n\n        if (event.pointerType === 'mouse')\n        {\n            this.mouseOverRenderer = false;\n            this.setCursorMode(null);\n        }\n\n        const interactionData = this.getInteractionDataForPointerId(event);\n\n        const interactionEvent = this.configureInteractionEventForDOMEvent(this.eventData, event, interactionData);\n\n        interactionEvent.data.originalEvent = event;\n\n        this.processInteractive(interactionEvent, this.lastObjectRendered, this.processPointerOverOut, false);\n\n        this.emit('pointerout', interactionEvent);\n        if (event.pointerType === 'mouse' || event.pointerType === 'pen')\n        {\n            this.emit('mouseout', interactionEvent);\n        }\n        else\n        {\n            // we can get touchleave events after touchend, so we want to make sure we don't\n            // introduce memory leaks\n            this.releaseInteractionDataForPointerId(interactionData.identifier);\n        }\n    }\n\n    /**\n     * Processes the result of the pointer over/out check and dispatches the event if need be.\n     * @param interactionEvent - The interaction event wrapping the DOM event\n     * @param displayObject - The display object that was tested\n     * @param hit - the result of the hit test on the display object\n     */\n    private processPointerOverOut(interactionEvent: InteractionEvent, displayObject: DisplayObject, hit: boolean): void\n    {\n        const data = interactionEvent.data;\n\n        const id = interactionEvent.data.identifier;\n\n        const isMouse = (data.pointerType === 'mouse' || data.pointerType === 'pen');\n\n        let trackingData = displayObject.trackedPointers[id];\n\n        // if we just moused over the display object, then we need to track that state\n        if (hit && !trackingData)\n        {\n            trackingData = displayObject.trackedPointers[id] = new InteractionTrackingData(id);\n        }\n\n        if (trackingData === undefined) return;\n\n        if (hit && this.mouseOverRenderer)\n        {\n            if (!trackingData.over)\n            {\n                trackingData.over = true;\n                this.delayDispatchEvent(displayObject, 'pointerover', interactionEvent);\n                if (isMouse)\n                {\n                    this.delayDispatchEvent(displayObject, 'mouseover', interactionEvent);\n                }\n            }\n\n            // only change the cursor if it has not already been changed (by something deeper in the\n            // display tree)\n            if (isMouse && this.cursor === null)\n            {\n                this.cursor = displayObject.cursor;\n            }\n        }\n        else if (trackingData.over)\n        {\n            trackingData.over = false;\n            this.dispatchEvent(displayObject, 'pointerout', this.eventData);\n            if (isMouse)\n            {\n                this.dispatchEvent(displayObject, 'mouseout', interactionEvent);\n            }\n            // if there is no mouse down information for the pointer, then it is safe to delete\n            if (trackingData.none)\n            {\n                delete displayObject.trackedPointers[id];\n            }\n        }\n    }\n\n    /**\n     * Is called when the pointer is moved into the renderer element.\n     * @param originalEvent - The DOM event of a pointer button being moved into the renderer view.\n     */\n    private onPointerOver(originalEvent: InteractivePointerEvent): void\n    {\n        if (this.supportsTouchEvents && (originalEvent as PointerEvent).pointerType === 'touch') return;\n\n        const events = this.normalizeToPointerData(originalEvent);\n\n        // Only mouse and pointer can call onPointerOver, so events will always be length 1\n        const event = events[0];\n\n        const interactionData = this.getInteractionDataForPointerId(event);\n\n        const interactionEvent = this.configureInteractionEventForDOMEvent(this.eventData, event, interactionData);\n\n        interactionEvent.data.originalEvent = event;\n\n        if (event.pointerType === 'mouse')\n        {\n            this.mouseOverRenderer = true;\n        }\n\n        this.emit('pointerover', interactionEvent);\n        if (event.pointerType === 'mouse' || event.pointerType === 'pen')\n        {\n            this.emit('mouseover', interactionEvent);\n        }\n    }\n\n    /**\n     * Get InteractionData for a given pointerId. Store that data as well.\n     * @param event - Normalized pointer event, output from normalizeToPointerData.\n     * @returns - Interaction data for the given pointer identifier.\n     */\n    private getInteractionDataForPointerId(event: PointerEvent): InteractionData\n    {\n        const pointerId = event.pointerId;\n\n        let interactionData;\n\n        if (pointerId === MOUSE_POINTER_ID || event.pointerType === 'mouse')\n        {\n            interactionData = this.mouse;\n        }\n        else if (this.activeInteractionData[pointerId])\n        {\n            interactionData = this.activeInteractionData[pointerId];\n        }\n        else\n        {\n            interactionData = this.interactionDataPool.pop() || new InteractionData();\n            interactionData.identifier = pointerId;\n            this.activeInteractionData[pointerId] = interactionData;\n        }\n        // copy properties from the event, so that we can make sure that touch/pointer specific\n        // data is available\n        interactionData.copyEvent(event);\n\n        return interactionData;\n    }\n\n    /**\n     * Return unused InteractionData to the pool, for a given pointerId\n     * @param pointerId - Identifier from a pointer event\n     */\n    private releaseInteractionDataForPointerId(pointerId: number): void\n    {\n        const interactionData = this.activeInteractionData[pointerId];\n\n        if (interactionData)\n        {\n            delete this.activeInteractionData[pointerId];\n            interactionData.reset();\n            this.interactionDataPool.push(interactionData);\n        }\n    }\n\n    /**\n     * Configure an InteractionEvent to wrap a DOM PointerEvent and InteractionData\n     * @param interactionEvent - The event to be configured\n     * @param pointerEvent - The DOM event that will be paired with the InteractionEvent\n     * @param interactionData - The InteractionData that will be paired\n     *        with the InteractionEvent\n     * @returns - the interaction event that was passed in\n     */\n    private configureInteractionEventForDOMEvent(interactionEvent: InteractionEvent, pointerEvent: PointerEvent,\n        interactionData: InteractionData\n    ): InteractionEvent\n    {\n        interactionEvent.data = interactionData;\n\n        this.mapPositionToPoint(interactionData.global, pointerEvent.clientX, pointerEvent.clientY);\n\n        // Not really sure why this is happening, but it's how a previous version handled things\n        if (pointerEvent.pointerType === 'touch')\n        {\n            (pointerEvent as any).globalX = interactionData.global.x;\n            (pointerEvent as any).globalY = interactionData.global.y;\n        }\n\n        interactionData.originalEvent = pointerEvent;\n        interactionEvent.reset();\n\n        return interactionEvent;\n    }\n\n    /**\n     * Ensures that the original event object contains all data that a regular pointer event would have\n     * @param {TouchEvent|MouseEvent|PointerEvent} event - The original event data from a touch or mouse event\n     * @returns - An array containing a single normalized pointer event, in the case of a pointer\n     *  or mouse event, or a multiple normalized pointer events if there are multiple changed touches\n     */\n    private normalizeToPointerData(event: InteractivePointerEvent): PointerEvent[]\n    {\n        const normalizedEvents = [];\n\n        if (this.supportsTouchEvents && event instanceof TouchEvent)\n        {\n            for (let i = 0, li = event.changedTouches.length; i < li; i++)\n            {\n                const touch = event.changedTouches[i] as PixiTouch;\n\n                if (typeof touch.button === 'undefined') touch.button = event.touches.length ? 1 : 0;\n                if (typeof touch.buttons === 'undefined') touch.buttons = event.touches.length ? 1 : 0;\n                if (typeof touch.isPrimary === 'undefined')\n                {\n                    touch.isPrimary = event.touches.length === 1 && event.type === 'touchstart';\n                }\n                if (typeof touch.width === 'undefined') touch.width = touch.radiusX || 1;\n                if (typeof touch.height === 'undefined') touch.height = touch.radiusY || 1;\n                if (typeof touch.tiltX === 'undefined') touch.tiltX = 0;\n                if (typeof touch.tiltY === 'undefined') touch.tiltY = 0;\n                if (typeof touch.pointerType === 'undefined') touch.pointerType = 'touch';\n                if (typeof touch.pointerId === 'undefined') touch.pointerId = touch.identifier || 0;\n                if (typeof touch.pressure === 'undefined') touch.pressure = touch.force || 0.5;\n                if (typeof touch.twist === 'undefined') touch.twist = 0;\n                if (typeof touch.tangentialPressure === 'undefined') touch.tangentialPressure = 0;\n                // TODO: Remove these, as layerX/Y is not a standard, is deprecated, has uneven\n                // support, and the fill ins are not quite the same\n                // offsetX/Y might be okay, but is not the same as clientX/Y when the canvas's top\n                // left is not 0,0 on the page\n                if (typeof touch.layerX === 'undefined') touch.layerX = touch.offsetX = touch.clientX;\n                if (typeof touch.layerY === 'undefined') touch.layerY = touch.offsetY = touch.clientY;\n\n                // mark the touch as normalized, just so that we know we did it\n                touch.isNormalized = true;\n\n                normalizedEvents.push(touch);\n            }\n        }\n        // apparently PointerEvent subclasses MouseEvent, so yay\n        else if (!globalThis.MouseEvent\n            || (event instanceof MouseEvent && (!this.supportsPointerEvents || !(event instanceof globalThis.PointerEvent))))\n        {\n            const tempEvent = event as PixiPointerEvent;\n\n            if (typeof tempEvent.isPrimary === 'undefined') tempEvent.isPrimary = true;\n            if (typeof tempEvent.width === 'undefined') tempEvent.width = 1;\n            if (typeof tempEvent.height === 'undefined') tempEvent.height = 1;\n            if (typeof tempEvent.tiltX === 'undefined') tempEvent.tiltX = 0;\n            if (typeof tempEvent.tiltY === 'undefined') tempEvent.tiltY = 0;\n            if (typeof tempEvent.pointerType === 'undefined') tempEvent.pointerType = 'mouse';\n            if (typeof tempEvent.pointerId === 'undefined') tempEvent.pointerId = MOUSE_POINTER_ID;\n            if (typeof tempEvent.pressure === 'undefined') tempEvent.pressure = 0.5;\n            if (typeof tempEvent.twist === 'undefined') tempEvent.twist = 0;\n            if (typeof tempEvent.tangentialPressure === 'undefined') tempEvent.tangentialPressure = 0;\n\n            // mark the mouse event as normalized, just so that we know we did it\n            tempEvent.isNormalized = true;\n\n            normalizedEvents.push(tempEvent);\n        }\n        else\n        {\n            normalizedEvents.push(event);\n        }\n\n        return normalizedEvents as PointerEvent[];\n    }\n\n    /** Destroys the interaction manager. */\n    public destroy(): void\n    {\n        this.removeEvents();\n\n        this.removeTickerListener();\n\n        this.removeAllListeners();\n\n        this.renderer = null;\n\n        this.mouse = null;\n\n        this.eventData = null;\n\n        this.interactionDOMElement = null;\n\n        this.onPointerDown = null;\n        this.processPointerDown = null;\n\n        this.onPointerUp = null;\n        this.processPointerUp = null;\n\n        this.onPointerCancel = null;\n        this.processPointerCancel = null;\n\n        this.onPointerMove = null;\n        this.processPointerMove = null;\n\n        this.onPointerOut = null;\n        this.processPointerOverOut = null;\n\n        this.onPointerOver = null;\n\n        this.search = null;\n    }\n}\n\ninterface PixiPointerEvent extends PointerEvent\n{\n    isPrimary: boolean;\n    width: number;\n    height: number;\n    tiltX: number;\n    tiltY: number;\n    pointerType: string;\n    pointerId: number;\n    pressure: number;\n    twist: number;\n    tangentialPressure: number;\n    isNormalized: boolean;\n}\n\ninterface PixiTouch extends Touch\n{\n    button: number;\n    buttons: number;\n    isPrimary: boolean;\n    width: number;\n    height: number;\n    tiltX: number;\n    tiltY: number;\n    pointerType: string;\n    pointerId: number;\n    pressure: number;\n    twist: number;\n    tangentialPressure: number;\n    layerX: number;\n    layerY: number;\n    offsetX: number;\n    offsetY: number;\n    isNormalized: boolean;\n}\n"], "names": ["Point", "DisplayObject", "TemporaryDisplayObject", "Ticker", "UPDATE_PRIORITY", "ExtensionType", "EventEmitter"], "mappings": ";;;;;;;;;;;;;;;;;AAOA;;;AAGG;AACH,IAAA,eAAA,kBAAA,YAAA;AA+FI,IAAA,SAAA,eAAA,GAAA;AAzBA;;;;AAIG;QACI,IAAQ,CAAA,QAAA,GAAG,CAAC,CAAC;AAEpB;;;AAGG;QACI,IAAa,CAAA,aAAA,GAAG,CAAC,CAAC;AAEzB;;;AAGG;QACI,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;AAEjB;;;AAGG;QACI,IAAkB,CAAA,kBAAA,GAAG,CAAC,CAAC;AAI1B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAIA,UAAK,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAChB,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAChB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;KAC/B;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,eAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AALb;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;AACI,IAAA,eAAA,CAAA,SAAA,CAAA,gBAAgB,GAAvB,UAAsD,aAA4B,EAAE,KAAS,EAAE,SAAsB,EAAA;AAEjH,QAAA,OAAO,aAAa,CAAC,cAAc,CAAC,YAAY,CAAI,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACxF,CAAA;AAED;;;AAGG;IACI,eAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,KAAsC,EAAA;;;;AAKnD,QAAA,IAAI,WAAW,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,EAC3C;AACI,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACzB,SAAA;QACD,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC;;;QAGhD,IAAM,OAAO,GAAG,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;QAEpD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC;QACrF,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC;QAC7C,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,aAAa,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC;QAC/D,IAAI,CAAC,QAAQ,GAAG,UAAU,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,eAAe,IAAI,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC;AACrE,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,oBAAoB,IAAI,KAAK,IAAI,KAAK,CAAC,kBAAkB,KAAK,CAAC,CAAC;KAC9F,CAAA;;AAGM,IAAA,eAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;;;AAII,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KAC1B,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA;;AC7LD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;ACtBA;;;AAGG;AACH,IAAA,gBAAA,kBAAA,YAAA;AAuCI,IAAA,SAAA,gBAAA,GAAA;AAEI,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC/B,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AACjC,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;;AAGM,IAAA,gBAAA,CAAA,SAAA,CAAA,eAAe,GAAtB,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC;KAChD,CAAA;;AAGM,IAAA,gBAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC/B,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AACjC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;ACpED;;;;;AAKG;AACH,IAAA,uBAAA,kBAAA,YAAA;AAYI;;;AAGG;AACH,IAAA,SAAA,uBAAA,CAAY,SAAiB,EAAA;AAEzB,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,uBAAuB,CAAC,KAAK,CAAC,IAAI,CAAC;KACpD;AAED;;;;;AAKG;AACK,IAAA,uBAAA,CAAA,SAAA,CAAA,MAAM,GAAd,UAAe,IAAY,EAAE,EAAW,EAAA;AAEpC,QAAA,IAAI,EAAE,EACN;YACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACpC,SAAA;AAED,aAAA;YACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;AACvC,SAAA;KACJ,CAAA;AAQD,IAAA,MAAA,CAAA,cAAA,CAAI,uBAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AANb;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,uBAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AALT;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,MAAM,CAAC;SACtB;AAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;AAEnB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACvB;;;AALA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAI,uBAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AALR;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,MAAM,KAAK,uBAAuB,CAAC,KAAK,CAAC,IAAI,CAAC;SAC7D;;;AAAA,KAAA,CAAA,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,uBAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AALR;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,uBAAuB,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC;SACnE;AAED,QAAA,GAAA,EAAA,UAAS,EAAW,EAAA;YAEhB,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;SACvD;;;AALA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAI,uBAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AALb;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,uBAAuB,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,CAAC;SACzE;AAED,QAAA,GAAA,EAAA,UAAc,EAAW,EAAA;YAErB,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;SAC7D;;;AALA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAI,uBAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AALZ;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,uBAAuB,CAAC,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC;SACxE;AAED,QAAA,GAAA,EAAA,UAAa,EAAW,EAAA;YAEpB,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;SAC5D;;;AALA,KAAA,CAAA,CAAA;AAhHa,IAAA,uBAAA,CAAA,KAAK,GAAuC,MAAM,CAAC,MAAM,CAAC;AACpE,QAAA,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,SAAS,EAAE,CAAC,IAAI,CAAC;QACjB,UAAU,EAAE,CAAC,IAAI,CAAC;AACrB,KAAA,CAAC,CAAC;IAiHP,OAAC,uBAAA,CAAA;AAAA,CAxHD,EAwHC;;ACjID;;;AAGG;AACH,IAAA,UAAA,kBAAA,YAAA;AAII,IAAA,SAAA,UAAA,GAAA;AAEI,QAAA,IAAI,CAAC,UAAU,GAAG,IAAIA,UAAK,EAAE,CAAC;KACjC;AAED;;;;;;;;;;;;AAYG;IACI,UAAgB,CAAA,SAAA,CAAA,gBAAA,GAAvB,UAAwB,gBAAkC,EAAE,aAA4B,EACpF,IAA0B,EAAE,OAAiB,EAAE,WAAqB,EAAA;;AAGpE,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,OAAO,EAC5C;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;;AAe3C,QAAA,WAAW,GAAG,aAAa,CAAC,WAAW,IAAI,WAAW,CAAC;QAEvD,IAAI,GAAG,GAAG,KAAK,CAAC;QAChB,IAAI,iBAAiB,GAAG,WAAW,CAAC;;QAGpC,IAAI,eAAe,GAAG,IAAI,CAAC;;;QAI3B,IAAI,aAAa,CAAC,OAAO,EACzB;AACI,YAAA,IAAI,OAAO,EACX;gBACI,aAAa,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAClE,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EACzE;oBACI,OAAO,GAAG,KAAK,CAAC;oBAChB,eAAe,GAAG,KAAK,CAAC;AAC3B,iBAAA;AAED,qBAAA;oBACI,GAAG,GAAG,IAAI,CAAC;AACd,iBAAA;AACJ,aAAA;YACD,iBAAiB,GAAG,KAAK,CAAC;AAC7B,SAAA;;;;aAII,IAAI,aAAa,CAAC,KAAK,EAC5B;AACI,YAAA,IAAI,OAAO,EACX;AACI,gBAAA,IAAM,UAAU,IAAK,aAAa,CAAC,KAAa,CAAC,UAAU;AACvD,sBAAG,aAAa,CAAC,KAAa,CAAC,UAAU,GAAG,aAAa,CAAC,KAAK,CAAQ,CAAC;AAE5E,gBAAA,IAAI,UAAU,IAAI,EAAC,MAAA,UAAU,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAxB,UAAU,EAAiB,KAAK,CAAC,CAAA,EACpD;oBACI,OAAO,GAAG,KAAK,CAAC;AACnB,iBAAA;AACJ,aAAA;AACJ,SAAA;;;;QAKD,IAAI,eAAe,IAAI,aAAa,CAAC,mBAAmB,IAAK,aAA2B,CAAC,QAAQ,EACjG;AACI,YAAA,IAAM,QAAQ,GAAI,aAA2B,CAAC,QAAQ,CAAC;AAEvD,YAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAC7C;AACI,gBAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;AAG1B,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;AAElG,gBAAA,IAAI,QAAQ,EACZ;;;AAGI,oBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EACjB;wBACI,SAAS;AACZ,qBAAA;;;oBAID,iBAAiB,GAAG,KAAK,CAAC;;;;;AAO1B,oBAAA,IAAI,QAAQ,EACZ;wBACI,IAAI,gBAAgB,CAAC,MAAM,EAC3B;4BACI,OAAO,GAAG,KAAK,CAAC;AACnB,yBAAA;wBACD,GAAG,GAAG,IAAI,CAAC;AACd,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,WAAW,EACf;;;;;AAKI,YAAA,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EACvC;;gBAEI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAK,aAAqB,CAAC,aAAa,EAClE;AACI,oBAAA,IAAK,aAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,EAC/C;wBACI,GAAG,GAAG,IAAI,CAAC;AACd,qBAAA;AACJ,iBAAA;AACJ,aAAA;YAED,IAAI,aAAa,CAAC,WAAW,EAC7B;AACI,gBAAA,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EACnC;AACI,oBAAA,gBAAgB,CAAC,MAAM,GAAG,aAAa,CAAC;AAC3C,iBAAA;AAED,gBAAA,IAAI,IAAI,EACR;oBACI,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AAChD,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;AAED;;;;;;;;;;;;;AAaG;IACI,UAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,gBAAkC,EAAE,aAA4B,EAC3E,IAA0B,EAAE,OAAiB,EAAA;AAG7C,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;KAChF,CAAA;IACL,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AChJD;;;;;;;;;;AAUG;AAEH;;;;;;;;AAQG;AAEH;;;;;;;;;;;;;;AAcG;AACI,IAAM,iBAAiB,GAAsB;AAChD,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,mBAAmB,EAAE,IAAI;AACzB,IAAA,OAAO,EAAE,IAAI;AAEb;;;;;;;;;AASG;AACH,IAAA,IAAI,UAAU,GAAA;AAEV,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;KACpC;IACD,IAAI,UAAU,CAAC,KAAc,EAAA;AAEzB,QAAA,IAAI,KAAK,EACT;AACI,YAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AAC3B,SAAA;AACI,aAAA,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAClC;AACI,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;KACJ;AAED;;;;;;;;;;AAUG;AACH,IAAA,MAAM,EAAE,IAAI;AAEZ;;;;;AAKG;AACH,IAAA,IAAI,eAAe,GAAA;AAEf,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS;AAAE,YAAA,EAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC,EAAA;QAEpE,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAChC;AAED;;;;AAIG;AACH,IAAA,gBAAgB,EAAE,SAAS;;;AC1I/B;AACAC,qBAAa,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAEvC,IAAM,gBAAgB,GAAG,CAAC,CAAC;AAS3B;AACA,IAAM,YAAY,GAAyB;AACvC,IAAA,MAAM,EAAE,IAAI;AACZ,IAAA,IAAI,EAAE;AACF,QAAA,MAAM,EAAE,IAAI;AACf,KAAA;CACJ,CAAC;AAsBF;;;;;;;;;AASG;AACH,IAAA,kBAAA,kBAAA,UAAA,MAAA,EAAA;IAAwC,SAAY,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;AA8HhD;;;;;;AAMG;IACH,SAAY,kBAAA,CAAA,QAA0B,EAAE,OAAmC,EAAA;AAA3E,QAAA,IAAA,KAAA,GAEI,iBAAO,IA2cV,IAAA,CAAA;AAzcG,QAAA,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAExB,QAAA,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,KAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,KAAK,SAAS,GAAG,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACvG,KAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;AAC/D,QAAA,KAAI,CAAC,KAAK,GAAG,IAAI,eAAe,EAAE,CAAC;AACnC,QAAA,KAAI,CAAC,KAAK,CAAC,UAAU,GAAG,gBAAgB,CAAC;;;QAIzC,KAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;AAE/B,QAAA,KAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;QAChC,KAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,GAAG,KAAI,CAAC,KAAK,CAAC;AAC1D,QAAA,KAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;AAC9B,QAAA,KAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,EAAE,CAAC;AACxC,QAAA,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAElC,QAAA,KAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,KAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,KAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,KAAI,CAAC,iBAAiB,GAAG,EAAE,cAAc,IAAI,UAAU,CAAC,CAAC;AACzD,QAAA,KAAI,CAAC,mBAAmB,GAAG,cAAc,IAAI,UAAU,CAAC;QACxD,KAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC;;QAIvD,KAAI,CAAC,WAAW,GAAG,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAC/C,KAAI,CAAC,gBAAgB,GAAG,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAEzD,KAAI,CAAC,eAAe,GAAG,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QACvD,KAAI,CAAC,oBAAoB,GAAG,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAEjE,KAAI,CAAC,aAAa,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QACnD,KAAI,CAAC,kBAAkB,GAAG,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAE7D,KAAI,CAAC,aAAa,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QACnD,KAAI,CAAC,kBAAkB,GAAG,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAE7D,KAAI,CAAC,YAAY,GAAG,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QACjD,KAAI,CAAC,qBAAqB,GAAG,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAEnE,KAAI,CAAC,aAAa,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAEnD,KAAI,CAAC,YAAY,GAAG;AAChB,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,OAAO,EAAE,SAAS;SACrB,CAAC;AACF,QAAA,KAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC9B,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAEnB,QAAA,KAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACpB,QAAA,KAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AACxB,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAE/B,QAAA,KAAI,CAAC,kBAAkB,GAAG,IAAIC,8BAAsB,EAAE,CAAC;AACvD,QAAA,KAAI,CAAC,qBAAqB,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;AAE/D;;;;;AAKG;AAEH;;;;;AAKG;AAEH;;;;;AAKG;AAEH;;;;;AAKG;AAEH;;;;;AAKG;AAEH;;;;;AAKG;AAEH;;;;;;AAMG;AAEH;;;;;;AAMG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;;;;AAOG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;;AAKG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;;AAKG;AAEH;;;;AAIG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;;;AASG;AAEH;;;;;;;;;AASG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;;AAQG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;AAOG;AAEH;;;;;;;;AAQG;AAEH;;;;;;;AAOG;AAEH,QAAA,KAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,KAAK,SAAS,GAAG,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;AAE/F,QAAA,KAAI,CAAC,gBAAgB,CAAC,KAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;;KACvE;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,kBAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;AAJnB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;AACD,QAAA,GAAA,EAAA,UAAoB,eAAwB,EAAA;AAExC,YAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;AAExC,YAAA,IAAI,eAAe,EACnB;gBACI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,aAAA;SACJ;;;AAbA,KAAA,CAAA,CAAA;AAoBD,IAAA,MAAA,CAAA,cAAA,CAAI,kBAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;AALtB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAQ,IAAI,CAAC,QAAQ,CAAC,mBAAqC,IAAI,IAAI,CAAC,kBAAkB,CAAC;SAC1F;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;;;AAMG;AACI,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,UAAe,WAAkB,EAAE,IAAoB,EAAA;;AAGnD,QAAA,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;;AAE3B,QAAA,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;;QAEvC,IAAI,CAAC,IAAI,EACT;AACI,YAAA,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAClC,SAAA;;QAED,IAAI,CAAC,kBAAkB,CAAC,YAAgC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;;QAG5E,OAAO,YAAY,CAAC,MAAM,CAAC;KAC9B,CAAA;AAED;;;;;;AAMG;AACI,IAAA,kBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAvB,UAAwB,OAAoB,EAAE,UAAc,EAAA;AAAd,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAc,GAAA,CAAA,CAAA,EAAA;QAExD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,YAAY,EAAE,CAAC;AAEpB,QAAA,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC;AAErC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC5B,CAAA;;AAGO,IAAA,kBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAzB,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAC7E;YACI,OAAO;AACV,SAAA;AAED,QAAAC,aAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,EAAEC,sBAAe,CAAC,WAAW,CAAC,CAAC;AAExE,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B,CAAA;;AAGO,IAAA,kBAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;AAEI,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EACrB;YACI,OAAO;AACV,SAAA;QAEDD,aAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAE9C,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B,CAAA;;AAGO,IAAA,kBAAA,CAAA,SAAA,CAAA,SAAS,GAAjB,YAAA;QAEI,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,qBAAqB,EACnD;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAiC,CAAC;AAE3E,QAAA,IAAK,UAAU,CAAC,SAAiB,CAAC,gBAAgB,EAClD;AACI,YAAA,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC;AAChC,YAAA,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;AAChC,SAAA;aACI,IAAI,IAAI,CAAC,qBAAqB,EACnC;AACI,YAAA,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AAC9B,SAAA;AAED;;;AAGG;QACH,IAAI,IAAI,CAAC,qBAAqB,EAC9B;AACI,YAAA,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACpG,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;;;;AAI3G,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC3G,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC3G,YAAA,UAAU,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC/F,YAAA,UAAU,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC1F,SAAA;AAED,aAAA;AACI,YAAA,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAClG,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACzG,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACvG,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACzG,YAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACxF,SAAA;;;;QAKD,IAAI,IAAI,CAAC,mBAAmB,EAC5B;AACI,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC1G,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC7G,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACtG,YAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5G,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B,CAAA;;AAGO,IAAA,kBAAA,CAAA,SAAA,CAAA,YAAY,GAApB,YAAA;QAEI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,qBAAqB,EACpD;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAiC,CAAC;AAE3E,QAAA,IAAK,UAAU,CAAC,SAAiB,CAAC,gBAAgB,EAClD;AACI,YAAA,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC5B,YAAA,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,SAAA;aACI,IAAI,IAAI,CAAC,qBAAqB,EACnC;AACI,YAAA,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;AAC1B,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,EAC9B;AACI,YAAA,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACvG,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC9G,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC9G,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC9G,YAAA,UAAU,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAClG,YAAA,UAAU,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC7F,SAAA;AAED,aAAA;AACI,YAAA,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACrG,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5G,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC1G,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5G,YAAA,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC3F,SAAA;QAED,IAAI,IAAI,CAAC,mBAAmB,EAC5B;AACI,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC7G,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAChH,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACzG,YAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC/G,SAAA;AAED,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAElC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B,CAAA;AAED;;;;;;AAMG;IACI,kBAAY,CAAA,SAAA,CAAA,YAAA,GAAnB,UAAoB,SAAiB,EAAA;AAEjC,QAAA,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC;AAE7B,QAAA,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAC/C;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAEpB,IAAI,CAAC,MAAM,EAAE,CAAC;KACjB,CAAA;;AAGM,IAAA,kBAAA,CAAA,SAAA,CAAA,MAAM,GAAb,YAAA;AAEI,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAC/B;YACI,OAAO;AACV,SAAA;;QAGD,IAAI,IAAI,CAAC,QAAQ,EACjB;AACI,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YAEtB,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;;;;AAKnB,QAAA,KAAK,IAAM,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAC1C;;YAEI,IAAI,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC,EAChD;gBACI,IAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBAEtD,IAAI,eAAe,CAAC,aAAa,IAAI,eAAe,CAAC,WAAW,KAAK,OAAO,EAC5E;AACI,oBAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,oCAAoC,CAC9D,IAAI,CAAC,SAAS,EACd,eAAe,CAAC,aAA6B,EAC7C,eAAe,CAClB,CAAC;AAEF,oBAAA,IAAI,CAAC,kBAAkB,CACnB,gBAAgB,EAChB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CACP,CAAC;AACL,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACnC,CAAA;AAED;;;AAGG;IACI,kBAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,IAAY,EAAA;AAE7B,QAAA,IAAI,GAAG,IAAI,IAAI,SAAS,CAAC;QACzB,IAAI,WAAW,GAAG,IAAI,CAAC;;;QAIvB,IAAI,UAAU,CAAC,eAAe,IAAI,IAAI,CAAC,qBAAqB,YAAY,eAAe,EACvF;YACI,WAAW,GAAG,KAAK,CAAC;AACvB,SAAA;;AAED,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EACnC;YACI,OAAO;AACV,SAAA;AACD,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;;AAGtC,QAAA,IAAI,KAAK,EACT;YACI,QAAQ,OAAO,KAAK;AAEhB,gBAAA,KAAK,QAAQ;;AAET,oBAAA,IAAI,WAAW,EACf;wBACI,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;AACnD,qBAAA;oBACD,MAAM;AACV,gBAAA,KAAK,UAAU;;oBAEX,KAAK,CAAC,IAAI,CAAC,CAAC;oBACZ,MAAM;AACV,gBAAA,KAAK,QAAQ;;;AAGT,oBAAA,IAAI,WAAW,EACf;wBACI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1D,qBAAA;oBACD,MAAM;AACb,aAAA;AACJ,SAAA;aACI,IAAI,WAAW,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,EAClH;;;YAGI,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AAClD,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,aAAa,GAArB,UAAsB,aAA4B,EAAE,WAAmB,EAAE,SAA2B,EAAA;;;QAIhG,IAAI,CAAC,SAAS,CAAC,mBAAmB,IAAI,aAAa,KAAK,SAAS,CAAC,kBAAkB,EACpF;AACI,YAAA,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC;AACxC,YAAA,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC;AAE7B,YAAA,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAE3C,YAAA,IAAK,aAAqB,CAAC,WAAW,CAAC,EACvC;AACK,gBAAA,aAAqB,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC;AAClD,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;;;;AAMG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,UAA2B,aAA4B,EAAE,WAAmB,EAAE,SAA2B,EAAA;AAErG,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,aAAa,EAAA,aAAA,EAAE,WAAW,aAAA,EAAE,SAAS,EAAA,SAAA,EAAE,CAAC,CAAC;KACtE,CAAA;AAED;;;;;;;AAOG;AACI,IAAA,kBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAzB,UAA0B,KAAiB,EAAE,CAAS,EAAE,CAAS,EAAA;AAE7D,QAAA,IAAI,IAAI,CAAC;;AAGT,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAC7C;AACI,YAAA,IAAI,GAAG;AACH,gBAAA,CAAC,EAAE,CAAC;AACJ,gBAAA,CAAC,EAAE,CAAC;AACJ,gBAAA,KAAK,EAAG,IAAI,CAAC,qBAA6B,CAAC,KAAK;AAChD,gBAAA,MAAM,EAAG,IAAI,CAAC,qBAA6B,CAAC,MAAM;AAClD,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,GAAG,EAAE,CAAC;aACT,CAAC;AACL,SAAA;AAED,aAAA;AACI,YAAA,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;AAC7D,SAAA;AAED,QAAA,IAAM,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAEnD,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,KAAM,IAAI,CAAC,qBAA6B,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC;QAC9G,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,KAAM,IAAI,CAAC,qBAA6B,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,oBAAoB,CAAC;KAClH,CAAA;AAED;;;;;;;;;;;;;AAaG;IACI,kBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAzB,UAA0B,gBAAkC,EAAE,aAA4B,EACtF,IAA0B,EAAE,OAAiB,EAAA;AAG7C,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAEhF,QAAA,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AAEzC,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EACzB;AACI,YAAA,OAAO,GAAG,CAAC;AACd,SAAA;;AAED,QAAA,gBAAgB,CAAC,mBAAmB,GAAG,KAAK,CAAC;AAE7C,QAAA,IAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC;AAExC,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EACnC;AACU,YAAA,IAAA,EAA4C,GAAA,aAAa,CAAC,CAAC,CAAC,EAA1D,eAAa,GAAA,EAAA,CAAA,aAAA,EAAE,WAAW,GAAA,EAAA,CAAA,WAAA,EAAE,SAAS,eAAqB,CAAC;;;AAInE,YAAA,IAAI,SAAS,CAAC,kBAAkB,KAAK,eAAa,EAClD;AACI,gBAAA,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACxC,aAAA;YAED,IAAI,CAAC,aAAa,CAAC,eAAa,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AAC7D,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;AAED;;;AAGG;IACK,kBAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,aAAsC,EAAA;;QAGxD,IAAI,IAAI,CAAC,mBAAmB,IAAK,aAA8B,CAAC,WAAW,KAAK,OAAO;cAAE,OAAO,EAAA;QAEhG,IAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;AAE1D;;;;AAIG;;QAIH,IAAI,IAAI,CAAC,kBAAkB,IAAK,MAAM,CAAC,CAAC,CAAS,CAAC,YAAY,EAC9D;AACI,YAAA,IAAM,UAAU,GAAG,aAAa,CAAC,UAAU,IAAI,EAAE,YAAY,IAAI,aAAa,CAAC,CAAC;AAEhF,YAAA,IAAI,UAAU,EACd;gBACI,aAAa,CAAC,cAAc,EAAE,CAAC;AAClC,aAAA;AACJ,SAAA;AAED,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EACjC;AACI,YAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAExB,IAAM,eAAe,GAAG,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;AAEnE,YAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAE3G,YAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAEpD,YAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;AAElG,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAC3C,YAAA,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,EACjC;AACI,gBAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;AAC7C,aAAA;;iBAEI,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC,WAAW,KAAK,KAAK,EACrE;AACI,gBAAA,IAAM,aAAa,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AAEzC,gBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,WAAW,GAAG,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACxE,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,UAA2B,gBAAkC,EAAE,aAA4B,EAAE,GAAY,EAAA;AAErG,QAAA,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACnC,QAAA,IAAM,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;AAE5C,QAAA,IAAI,GAAG,EACP;AACI,YAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,EACtC;gBACI,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,GAAG,IAAI,uBAAuB,CAAC,EAAE,CAAC,CAAC;AACvE,aAAA;YACD,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAEnE,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,EAChC;gBACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACrE,aAAA;iBACI,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EACnE;AACI,gBAAA,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AAExC,gBAAA,IAAI,aAAa,EACjB;oBACI,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;AACtD,iBAAA;AAED,qBAAA;oBACI,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrD,iBAAA;AAED,gBAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,GAAG,WAAW,GAAG,WAAW,EAAE,gBAAgB,CAAC,CAAC;AAClG,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAzB,UAA0B,aAAsC,EAAE,SAAkB,EAAE,IAAyB,EAAA;QAE3G,IAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;AAE1D,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;;;AAI/B,QAAA,IAAI,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;;AAGlC,QAAA,IAAI,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC,MAAM,GAAG,CAAC,EACzE;YACI,MAAM,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5C,SAAA;AAED,QAAA,IAAM,WAAW,GAAG,MAAM,KAAK,IAAI,CAAC,qBAAqB,GAAG,SAAS,GAAG,EAAE,CAAC;QAE3E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EACjC;AACI,YAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAExB,IAAM,eAAe,GAAG,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;AAEnE,YAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAE3G,YAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;;AAGpD,YAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,SAAS,IAAI,CAAC,WAAW,CAAC,CAAC;AAEpG,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,eAAe,GAAG,WAAY,GAAA,WAAa,EAAE,gBAAgB,CAAC,CAAC;YAErF,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC,WAAW,KAAK,KAAK,EAChE;AACI,gBAAA,IAAM,aAAa,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AAEzC,gBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,SAAA,GAAU,WAAa,GAAG,SAAA,GAAU,WAAa,EAAE,gBAAgB,CAAC,CAAC;AAClG,aAAA;AACI,iBAAA,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,EACtC;AACI,gBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,aAAa,GAAG,UAAW,GAAA,WAAa,EAAE,gBAAgB,CAAC,CAAC;AAClF,gBAAA,IAAI,CAAC,kCAAkC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC5D,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;AAGG;IACK,kBAAe,CAAA,SAAA,CAAA,eAAA,GAAvB,UAAwB,KAA8B,EAAA;;QAGlD,IAAI,IAAI,CAAC,mBAAmB,IAAK,KAAsB,CAAC,WAAW,KAAK,OAAO;cAAE,OAAO,EAAA;QAExF,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;KAClE,CAAA;AAED;;;;AAIG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,UAA6B,gBAAkC,EAAE,aAA4B,EAAA;AAEzF,QAAA,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;AAEnC,QAAA,IAAM,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;QAE5C,IAAI,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,SAAS,EACnD;AACI,YAAA,OAAO,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC;AAErE,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,EAChC;gBACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACtE,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;AAGG;IACK,kBAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,KAA8B,EAAA;;QAG9C,IAAI,IAAI,CAAC,mBAAmB,IAAK,KAAsB,CAAC,WAAW,KAAK,OAAO;cAAE,OAAO,EAAA;QAExF,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;KAC/D,CAAA;AAED;;;;;AAKG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAxB,UAAyB,gBAAkC,EAAE,aAA4B,EAAE,GAAY,EAAA;AAEnG,QAAA,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;AAEnC,QAAA,IAAM,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;QAE5C,IAAM,YAAY,GAAG,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAEvD,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,KAAK,OAAO,CAAC;AAE7C,QAAA,IAAM,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC;;;QAG7E,IAAI,UAAU,GAAG,KAAK,CAAC;;AAGvB,QAAA,IAAI,OAAO,EACX;AACI,YAAA,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AAExC,YAAA,IAAM,KAAK,GAAG,uBAAuB,CAAC,KAAK,CAAC;AAE5C,YAAA,IAAM,IAAI,GAAG,aAAa,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;AAEhE,YAAA,IAAM,MAAM,GAAG,YAAY,KAAK,SAAS,KAAK,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAEzE,YAAA,IAAI,GAAG,EACP;AACI,gBAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,GAAG,SAAS,GAAG,SAAS,EAAE,gBAAgB,CAAC,CAAC;AAE3F,gBAAA,IAAI,MAAM,EACV;AACI,oBAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,GAAG,YAAY,GAAG,OAAO,EAAE,gBAAgB,CAAC,CAAC;;oBAE5F,UAAU,GAAG,IAAI,CAAC;AACrB,iBAAA;AACJ,aAAA;AACI,iBAAA,IAAI,MAAM,EACf;AACI,gBAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;AAC5G,aAAA;;AAED,YAAA,IAAI,YAAY,EAChB;AACI,gBAAA,IAAI,aAAa,EACjB;AACI,oBAAA,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;AAClC,iBAAA;AAED,qBAAA;AACI,oBAAA,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC;AACjC,iBAAA;AACJ,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,GAAG,EACP;YACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;AACjE,YAAA,IAAI,OAAO;gBAAE,EAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC,EAAA;AAE7E,YAAA,IAAI,YAAY,EAChB;;AAEI,gBAAA,IAAI,CAAC,OAAO,IAAI,UAAU,EAC1B;oBACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACrE,iBAAA;AACD,gBAAA,IAAI,OAAO,EACX;oBACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;;;AAG3D,oBAAA,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC;AAC7B,iBAAA;AACJ,aAAA;AACJ,SAAA;AACI,aAAA,IAAI,YAAY,EACrB;YACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACxE,YAAA,IAAI,OAAO;gBAAE,EAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC,EAAA;AACvF,SAAA;;AAED,QAAA,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,EACrC;AACI,YAAA,OAAO,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC5C,SAAA;KACJ,CAAA;AAED;;;AAGG;IACK,kBAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,aAAsC,EAAA;;QAGxD,IAAI,IAAI,CAAC,mBAAmB,IAAK,aAA8B,CAAC,WAAW,KAAK,OAAO;cAAE,OAAO,EAAA;QAEhG,IAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;AAE1D,QAAA,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,KAAK,EACxE;AACI,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAErB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;AAED,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EACjC;AACI,YAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAExB,IAAM,eAAe,GAAG,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;AAEnE,YAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAE3G,YAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAEpD,YAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;AAElG,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAC3C,YAAA,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO;AAAE,gBAAA,EAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,EAAA;YAC5E,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC,WAAW,KAAK,KAAK;AAAE,gBAAA,EAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,EAAA;AAC9G,SAAA;QAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,EACrC;AACI,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;AAGnC,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,UAA2B,gBAAkC,EAAE,aAA4B,EAAE,GAAY,EAAA;AAErG,QAAA,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;AAEnC,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,KAAK,OAAO,CAAC;AAE7C,QAAA,IAAM,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC;AAE7E,QAAA,IAAI,OAAO,EACX;YACI,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,GAAG,EAC/B;YACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACnE,YAAA,IAAI,OAAO;gBAAE,EAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC,EAAA;AAC9E,YAAA,IAAI,OAAO;gBAAE,EAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC,EAAA;AACjF,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACK,kBAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,aAAsC,EAAA;;QAGvD,IAAI,IAAI,CAAC,mBAAmB,IAAK,aAA8B,CAAC,WAAW,KAAK,OAAO;cAAE,OAAO,EAAA;QAEhG,IAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;;AAG1D,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAExB,QAAA,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,EACjC;AACI,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;AAC/B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC5B,SAAA;QAED,IAAM,eAAe,GAAG,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;AAEnE,QAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAE3G,QAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAE5C,QAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;AAEtG,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAC1C,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC,WAAW,KAAK,KAAK,EAChE;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AAC3C,SAAA;AAED,aAAA;;;AAGI,YAAA,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACvE,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,qBAAqB,GAA7B,UAA8B,gBAAkC,EAAE,aAA4B,EAAE,GAAY,EAAA;AAExG,QAAA,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;AAEnC,QAAA,IAAM,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;AAE5C,QAAA,IAAM,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC;QAE7E,IAAI,YAAY,GAAG,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;;AAGrD,QAAA,IAAI,GAAG,IAAI,CAAC,YAAY,EACxB;AACI,YAAA,YAAY,GAAG,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,GAAG,IAAI,uBAAuB,CAAC,EAAE,CAAC,CAAC;AACtF,SAAA;QAED,IAAI,YAAY,KAAK,SAAS;cAAE,OAAO,EAAA;AAEvC,QAAA,IAAI,GAAG,IAAI,IAAI,CAAC,iBAAiB,EACjC;AACI,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EACtB;AACI,gBAAA,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;gBACzB,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACxE,gBAAA,IAAI,OAAO,EACX;oBACI,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;AACzE,iBAAA;AACJ,aAAA;;;AAID,YAAA,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EACnC;AACI,gBAAA,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AACtC,aAAA;AACJ,SAAA;aACI,IAAI,YAAY,CAAC,IAAI,EAC1B;AACI,YAAA,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAChE,YAAA,IAAI,OAAO,EACX;gBACI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;AACnE,aAAA;;YAED,IAAI,YAAY,CAAC,IAAI,EACrB;AACI,gBAAA,OAAO,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC5C,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;AAGG;IACK,kBAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,aAAsC,EAAA;QAExD,IAAI,IAAI,CAAC,mBAAmB,IAAK,aAA8B,CAAC,WAAW,KAAK,OAAO;cAAE,OAAO,EAAA;QAEhG,IAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;;AAG1D,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAExB,IAAM,eAAe,GAAG,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;AAEnE,QAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAE3G,QAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAE5C,QAAA,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,EACjC;AACI,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAC3C,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC,WAAW,KAAK,KAAK,EAChE;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AAC5C,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACK,kBAA8B,CAAA,SAAA,CAAA,8BAAA,GAAtC,UAAuC,KAAmB,EAAA;AAEtD,QAAA,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAElC,QAAA,IAAI,eAAe,CAAC;QAEpB,IAAI,SAAS,KAAK,gBAAgB,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,EACnE;AACI,YAAA,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC;AAChC,SAAA;AACI,aAAA,IAAI,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAC9C;AACI,YAAA,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;AAC3D,SAAA;AAED,aAAA;YACI,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,IAAI,IAAI,eAAe,EAAE,CAAC;AAC1E,YAAA,eAAe,CAAC,UAAU,GAAG,SAAS,CAAC;AACvC,YAAA,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,GAAG,eAAe,CAAC;AAC3D,SAAA;;;AAGD,QAAA,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAEjC,QAAA,OAAO,eAAe,CAAC;KAC1B,CAAA;AAED;;;AAGG;IACK,kBAAkC,CAAA,SAAA,CAAA,kCAAA,GAA1C,UAA2C,SAAiB,EAAA;QAExD,IAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;AAE9D,QAAA,IAAI,eAAe,EACnB;AACI,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;YAC7C,eAAe,CAAC,KAAK,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAClD,SAAA;KACJ,CAAA;AAED;;;;;;;AAOG;AACK,IAAA,kBAAA,CAAA,SAAA,CAAA,oCAAoC,GAA5C,UAA6C,gBAAkC,EAAE,YAA0B,EACvG,eAAgC,EAAA;AAGhC,QAAA,gBAAgB,CAAC,IAAI,GAAG,eAAe,CAAC;AAExC,QAAA,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,MAAM,EAAE,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;;AAG5F,QAAA,IAAI,YAAY,CAAC,WAAW,KAAK,OAAO,EACxC;YACK,YAAoB,CAAC,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;YACxD,YAAoB,CAAC,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5D,SAAA;AAED,QAAA,eAAe,CAAC,aAAa,GAAG,YAAY,CAAC;QAC7C,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAEzB,QAAA,OAAO,gBAAgB,CAAC;KAC3B,CAAA;AAED;;;;;AAKG;IACK,kBAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,KAA8B,EAAA;QAEzD,IAAM,gBAAgB,GAAG,EAAE,CAAC;AAE5B,QAAA,IAAI,IAAI,CAAC,mBAAmB,IAAI,KAAK,YAAY,UAAU,EAC3D;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC7D;gBACI,IAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAc,CAAC;AAEnD,gBAAA,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;AAAE,oBAAA,EAAA,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAA;AACrF,gBAAA,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,WAAW;AAAE,oBAAA,EAAA,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAA;AACvF,gBAAA,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,WAAW,EAC1C;AACI,oBAAA,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC;AAC/E,iBAAA;AACD,gBAAA,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;oBAAE,EAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,EAAA;AACzE,gBAAA,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;oBAAE,EAAA,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,EAAA;AAC3E,gBAAA,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;AAAE,oBAAA,EAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;AACxD,gBAAA,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;AAAE,oBAAA,EAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;AACxD,gBAAA,IAAI,OAAO,KAAK,CAAC,WAAW,KAAK,WAAW;AAAE,oBAAA,EAAA,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,EAAA;AAC1E,gBAAA,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,WAAW;oBAAE,EAAA,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAA;AACpF,gBAAA,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,WAAW;oBAAE,EAAA,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,EAAA;AAC/E,gBAAA,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;AAAE,oBAAA,EAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;AACxD,gBAAA,IAAI,OAAO,KAAK,CAAC,kBAAkB,KAAK,WAAW;AAAE,oBAAA,EAAA,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC,EAAA;;;;;AAKlF,gBAAA,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;oBAAE,EAAA,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAA;AACtF,gBAAA,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;oBAAE,EAAA,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAA;;AAGtF,gBAAA,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;AAE1B,gBAAA,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,aAAA;AACJ,SAAA;;aAEI,IAAI,CAAC,UAAU,CAAC,UAAU;gBACvB,KAAK,YAAY,UAAU,KAAK,CAAC,IAAI,CAAC,qBAAqB,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,EACpH;YACI,IAAM,SAAS,GAAG,KAAyB,CAAC;AAE5C,YAAA,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,EAAA;AAC3E,YAAA,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;AAChE,YAAA,IAAI,OAAO,SAAS,CAAC,MAAM,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAA;AAClE,YAAA,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;AAChE,YAAA,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;AAChE,YAAA,IAAI,OAAO,SAAS,CAAC,WAAW,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,EAAA;AAClF,YAAA,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,SAAS,GAAG,gBAAgB,CAAC,EAAA;AACvF,YAAA,IAAI,OAAO,SAAS,CAAC,QAAQ,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC,EAAA;AACxE,YAAA,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;AAChE,YAAA,IAAI,OAAO,SAAS,CAAC,kBAAkB,KAAK,WAAW;AAAE,gBAAA,EAAA,SAAS,CAAC,kBAAkB,GAAG,CAAC,CAAC,EAAA;;AAG1F,YAAA,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;AAE9B,YAAA,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACpC,SAAA;AAED,aAAA;AACI,YAAA,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,OAAO,gBAAkC,CAAC;KAC7C,CAAA;;AAGM,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE1B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAErB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAElB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAEtB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAElC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAE/B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAE7B,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AAEjC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAE/B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAElC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAE1B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB,CAAA;;AA5sDM,IAAA,kBAAA,CAAA,SAAS,GAAsB;AAClC,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,IAAI,EAAE;AACF,YAAAE,kBAAa,CAAC,cAAc;AAC5B,YAAAA,kBAAa,CAAC,oBAAoB,EACrC;KACJ,CAAC;IAusDN,OAAC,kBAAA,CAAA;CAAA,CAhtDuCC,kBAAY,CAgtDnD;;;;;;;;"}