{"name": "@pixi/polyfill", "version": "6.5.10", "main": "dist/cjs/polyfill.js", "module": "dist/esm/polyfill.mjs", "bundle": "dist/browser/polyfill.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/polyfill.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/polyfill.js"}}}, "standalone": true, "description": "Support for legacy browser JavaScript environments", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "dependencies": {"object-assign": "^4.1.1", "promise-polyfill": "^8.2.0"}, "devDependencies": {"@types/object-assign": "^4.0.30", "@types/promise-polyfill": "^6.0.3"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}