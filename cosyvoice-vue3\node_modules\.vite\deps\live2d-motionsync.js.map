{"version": 3, "sources": ["../../live2d-motionsync/dist/index.es.js"], "sourcesContent": ["var h = Object.defineProperty;\nvar f = (s, t, o) => t in s ? h(s, t, { enumerable: !0, configurable: !0, writable: !0, value: o }) : s[t] = o;\nvar n = (s, t, o) => f(s, typeof t != \"symbol\" ? t + \"\" : t, o);\nimport { c, C as u, M as m, f as S } from \"./fallback.motionsync3-1oooNFSa.js\";\nlet a;\nfunction p() {\n  const s = [\"click\", \"keydown\", \"touchstart\", \"mousedown\", \"pointerdown\"];\n  a = new AudioContext();\n  const t = () => {\n    a.state === \"suspended\" && a.resume().then(() => {\n      console.log(\"Audio context resumed\");\n    });\n  };\n  s.forEach((o) => {\n    window.addEventListener(o, t, { capture: !0 });\n  });\n}\nfunction y() {\n  return a;\n}\np();\nconst d = 48e3;\nclass x {\n  constructor(t) {\n    n(this, \"audioBuffer\", null);\n    n(this, \"audioSource\", null);\n    n(this, \"previousSamplePosition\", 0);\n    n(this, \"audioElapsedTime\", 0);\n    n(this, \"audioContextPreviousTime\", 0);\n    n(this, \"_motionSync\", null);\n    n(this, \"_internalModel\");\n    n(this, \"_model\");\n    n(this, \"soundBuffer\", new c());\n    this._internalModel = t, this._model = t.coreModel, u.startUp(new m()), u.initialize();\n  }\n  get audioContext() {\n    return y();\n  }\n  async loadAudio(t) {\n    const e = await (await fetch(t)).arrayBuffer();\n    this.reset(), this.audioBuffer = await this.audioContext.decodeAudioData(e);\n  }\n  async loadAudioBuffer(t) {\n    this.reset(), this.audioBuffer = t;\n  }\n  resetMouthStatus() {\n    try {\n      if (!this._motionSync) return;\n      const t = this._motionSync.getData().getSetting(0);\n      if (!t) return;\n      const o = t.cubismParameterList;\n      if (!o) return;\n      const e = o._ptr.map(\n        (i) => i.parameterIndex\n      );\n      for (const i of e)\n        this._model.setParameterValueByIndex(i, 0);\n    } catch (t) {\n      console.error(t);\n    }\n  }\n  reset() {\n    this.resetMouthStatus(), this.audioSource && (this.audioSource.stop(), this.audioSource.disconnect(), this.audioSource = null), this.audioContextPreviousTime = 0, this.previousSamplePosition = 0, this.audioElapsedTime = 0, this.soundBuffer.clear(), this.soundBuffer = new c();\n  }\n  async play(t) {\n    return new Promise(async (o, e) => {\n      typeof t == \"string\" ? await this.loadAudio(t) : await this.loadAudioBuffer(t), this.audioBuffer ? (this.audioSource = this.audioContext.createBufferSource(), this.audioSource.buffer = this.audioBuffer, this.audioSource.connect(this.audioContext.destination), this.audioSource.start(0), this.audioSource.onended = () => {\n        o();\n      }, this.audioContextPreviousTime = this.audioContext.currentTime) : e(new Error(\"audioBuffer is null\"));\n    });\n  }\n  updateMotionSync() {\n    if (!this.audioBuffer || !this.audioSource)\n      return;\n    const t = this.audioContext.currentTime;\n    t <= this.audioContextPreviousTime && (this.audioContextPreviousTime = t);\n    const o = t - this.audioContextPreviousTime;\n    this.audioElapsedTime += o;\n    const e = Math.floor(\n      this.audioElapsedTime * this.audioBuffer.sampleRate\n    );\n    if (this.previousSamplePosition <= this.audioBuffer.length) {\n      const i = this.audioBuffer.getChannelData(0).slice(this.previousSamplePosition, e);\n      for (let r = 0; r < i.length; r++)\n        this.soundBuffer.pushBack(i[r]);\n      if (!this._motionSync) return;\n      this._motionSync.setSoundBuffer(0, this.soundBuffer, 0), this._motionSync.updateParameters(this._model, o);\n      const l = this._motionSync.getLastTotalProcessedCount(0);\n      this.removeProcessedData(l), this.audioContextPreviousTime = t, this.previousSamplePosition = e;\n    }\n  }\n  modelUpdateWithMotionSync() {\n    if (!this._motionSync) return;\n    const o = this._internalModel, e = o.motionManager.update;\n    o.motionManager.update = (...i) => {\n      e.apply(this._internalModel.motionManager, i), this.updateMotionSync();\n    };\n  }\n  removeProcessedData(t) {\n    const o = this.soundBuffer;\n    if (t < o.getSize())\n      return !(o != null && o.begin()) || (o == null ? void 0 : o._size) <= t || (o._ptr.splice(0, t), o._size -= t), o;\n  }\n  loadMotionSync(t, o = d) {\n    if (t == null || t.byteLength == 0) {\n      console.warn(\"Failed to loadMotionSync().\");\n      return;\n    }\n    this._motionSync = u.create(\n      this._model,\n      t,\n      t.byteLength,\n      o\n    ), this.modelUpdateWithMotionSync();\n  }\n  async loadDefaultMotionSync(t = d) {\n    const e = await new Blob([S], { type: \"application/json\" }).arrayBuffer();\n    this.loadMotionSync(e, t);\n  }\n  async loadMotionSyncFromUrl(t, o = d) {\n    try {\n      const i = await (await fetch(t)).arrayBuffer();\n      this.loadMotionSync(i, o);\n    } catch {\n      console.warn(\"Failed to loadMotionSync(). Use default fallback.\"), await this.loadDefaultMotionSync(o);\n    }\n  }\n}\nexport {\n  x as MotionSync,\n  y as getAudioContext,\n  p as initAudioContext\n};\n"], "mappings": ";;;;;;;;;AAAA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,EAAE,GAAG,GAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAC7G,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,EAAE,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC;AAE9D,IAAI;AACJ,SAAS,IAAI;AACX,QAAM,IAAI,CAAC,SAAS,WAAW,cAAc,aAAa,aAAa;AACvE,MAAI,IAAI,aAAa;AACrB,QAAM,IAAI,MAAM;AACd,MAAE,UAAU,eAAe,EAAE,OAAO,EAAE,KAAK,MAAM;AAC/C,cAAQ,IAAI,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH;AACA,IAAE,QAAQ,CAAC,MAAM;AACf,WAAO,iBAAiB,GAAG,GAAG,EAAE,SAAS,KAAG,CAAC;AAAA,EAC/C,CAAC;AACH;AACA,SAAS,IAAI;AACX,SAAO;AACT;AACA,EAAE;AACF,IAAM,IAAI;AACV,IAAM,IAAN,MAAQ;AAAA,EACN,YAAY,GAAG;AACb,MAAE,MAAM,eAAe,IAAI;AAC3B,MAAE,MAAM,eAAe,IAAI;AAC3B,MAAE,MAAM,0BAA0B,CAAC;AACnC,MAAE,MAAM,oBAAoB,CAAC;AAC7B,MAAE,MAAM,4BAA4B,CAAC;AACrC,MAAE,MAAM,eAAe,IAAI;AAC3B,MAAE,MAAM,gBAAgB;AACxB,MAAE,MAAM,QAAQ;AAChB,MAAE,MAAM,eAAe,IAAI,EAAE,CAAC;AAC9B,SAAK,iBAAiB,GAAG,KAAK,SAAS,EAAE,WAAW,EAAE,QAAQ,IAAI,GAAE,CAAC,GAAG,EAAE,WAAW;AAAA,EACvF;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,EAAE;AAAA,EACX;AAAA,EACA,MAAM,UAAU,GAAG;AACjB,UAAM,IAAI,OAAO,MAAM,MAAM,CAAC,GAAG,YAAY;AAC7C,SAAK,MAAM,GAAG,KAAK,cAAc,MAAM,KAAK,aAAa,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EACA,MAAM,gBAAgB,GAAG;AACvB,SAAK,MAAM,GAAG,KAAK,cAAc;AAAA,EACnC;AAAA,EACA,mBAAmB;AACjB,QAAI;AACF,UAAI,CAAC,KAAK,YAAa;AACvB,YAAM,IAAI,KAAK,YAAY,QAAQ,EAAE,WAAW,CAAC;AACjD,UAAI,CAAC,EAAG;AACR,YAAM,IAAI,EAAE;AACZ,UAAI,CAAC,EAAG;AACR,YAAM,IAAI,EAAE,KAAK;AAAA,QACf,CAAC,MAAM,EAAE;AAAA,MACX;AACA,iBAAW,KAAK;AACd,aAAK,OAAO,yBAAyB,GAAG,CAAC;AAAA,IAC7C,SAAS,GAAG;AACV,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,iBAAiB,GAAG,KAAK,gBAAgB,KAAK,YAAY,KAAK,GAAG,KAAK,YAAY,WAAW,GAAG,KAAK,cAAc,OAAO,KAAK,2BAA2B,GAAG,KAAK,yBAAyB,GAAG,KAAK,mBAAmB,GAAG,KAAK,YAAY,MAAM,GAAG,KAAK,cAAc,IAAI,EAAE;AAAA,EACpR;AAAA,EACA,MAAM,KAAK,GAAG;AACZ,WAAO,IAAI,QAAQ,OAAO,GAAG,MAAM;AACjC,aAAO,KAAK,WAAW,MAAM,KAAK,UAAU,CAAC,IAAI,MAAM,KAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,KAAK,cAAc,KAAK,aAAa,mBAAmB,GAAG,KAAK,YAAY,SAAS,KAAK,aAAa,KAAK,YAAY,QAAQ,KAAK,aAAa,WAAW,GAAG,KAAK,YAAY,MAAM,CAAC,GAAG,KAAK,YAAY,UAAU,MAAM;AAC9T,UAAE;AAAA,MACJ,GAAG,KAAK,2BAA2B,KAAK,aAAa,eAAe,EAAE,IAAI,MAAM,qBAAqB,CAAC;AAAA,IACxG,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AACjB,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK;AAC7B;AACF,UAAM,IAAI,KAAK,aAAa;AAC5B,SAAK,KAAK,6BAA6B,KAAK,2BAA2B;AACvE,UAAM,IAAI,IAAI,KAAK;AACnB,SAAK,oBAAoB;AACzB,UAAM,IAAI,KAAK;AAAA,MACb,KAAK,mBAAmB,KAAK,YAAY;AAAA,IAC3C;AACA,QAAI,KAAK,0BAA0B,KAAK,YAAY,QAAQ;AAC1D,YAAM,IAAI,KAAK,YAAY,eAAe,CAAC,EAAE,MAAM,KAAK,wBAAwB,CAAC;AACjF,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,aAAK,YAAY,SAAS,EAAE,CAAC,CAAC;AAChC,UAAI,CAAC,KAAK,YAAa;AACvB,WAAK,YAAY,eAAe,GAAG,KAAK,aAAa,CAAC,GAAG,KAAK,YAAY,iBAAiB,KAAK,QAAQ,CAAC;AACzG,YAAM,IAAI,KAAK,YAAY,2BAA2B,CAAC;AACvD,WAAK,oBAAoB,CAAC,GAAG,KAAK,2BAA2B,GAAG,KAAK,yBAAyB;AAAA,IAChG;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,QAAI,CAAC,KAAK,YAAa;AACvB,UAAM,IAAI,KAAK,gBAAgB,IAAI,EAAE,cAAc;AACnD,MAAE,cAAc,SAAS,IAAI,MAAM;AACjC,QAAE,MAAM,KAAK,eAAe,eAAe,CAAC,GAAG,KAAK,iBAAiB;AAAA,IACvE;AAAA,EACF;AAAA,EACA,oBAAoB,GAAG;AACrB,UAAM,IAAI,KAAK;AACf,QAAI,IAAI,EAAE,QAAQ;AAChB,aAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,OAAO,KAAK,OAAO,SAAS,EAAE,UAAU,MAAM,EAAE,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE,SAAS,IAAI;AAAA,EACpH;AAAA,EACA,eAAe,GAAG,IAAI,GAAG;AACvB,QAAI,KAAK,QAAQ,EAAE,cAAc,GAAG;AAClC,cAAQ,KAAK,6BAA6B;AAC1C;AAAA,IACF;AACA,SAAK,cAAc,EAAE;AAAA,MACnB,KAAK;AAAA,MACL;AAAA,MACA,EAAE;AAAA,MACF;AAAA,IACF,GAAG,KAAK,0BAA0B;AAAA,EACpC;AAAA,EACA,MAAM,sBAAsB,IAAI,GAAG;AACjC,UAAM,IAAI,MAAM,IAAI,KAAK,CAAC,EAAC,GAAG,EAAE,MAAM,mBAAmB,CAAC,EAAE,YAAY;AACxE,SAAK,eAAe,GAAG,CAAC;AAAA,EAC1B;AAAA,EACA,MAAM,sBAAsB,GAAG,IAAI,GAAG;AACpC,QAAI;AACF,YAAM,IAAI,OAAO,MAAM,MAAM,CAAC,GAAG,YAAY;AAC7C,WAAK,eAAe,GAAG,CAAC;AAAA,IAC1B,QAAQ;AACN,cAAQ,KAAK,mDAAmD,GAAG,MAAM,KAAK,sBAAsB,CAAC;AAAA,IACvG;AAAA,EACF;AACF;", "names": []}