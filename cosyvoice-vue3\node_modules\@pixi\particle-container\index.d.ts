import type { BaseTexture } from '@pixi/core';
import { BLEND_MODES } from '@pixi/constants';
import { Buffer as Buffer_2 } from '@pixi/core';
import { Container } from '@pixi/display';
import type { ExtensionMetadata } from '@pixi/core';
import { Geometry } from '@pixi/core';
import type { IDestroyOptions } from '@pixi/display';
import { Matrix } from '@pixi/math';
import { ObjectRenderer } from '@pixi/core';
import type { Renderer } from '@pixi/core';
import { Shader } from '@pixi/core';
import type { Sprite } from '@pixi/sprite';
import { State } from '@pixi/core';
import { TYPES } from '@pixi/constants';

export declare interface IParticleProperties {
    vertices?: boolean;
    position?: boolean;
    rotation?: boolean;
    uvs?: boolean;
    tint?: boolean;
    alpha?: boolean;
    scale?: boolean;
}

export declare interface IParticleRendererProperty {
    attributeName: string;
    size: number;
    type?: TYPES;
    uploadFunction: (...params: any[]) => any;
    offset: number;
}

/**
 * The particle buffer manages the static and dynamic buffers for a particle container.
 * @private
 * @memberof PIXI
 */
declare class ParticleBuffer {
    geometry: Geometry;
    staticStride: number;
    staticBuffer: Buffer_2;
    staticData: Float32Array;
    staticDataUint32: Uint32Array;
    dynamicStride: number;
    dynamicBuffer: Buffer_2;
    dynamicData: Float32Array;
    dynamicDataUint32: Uint32Array;
    _updateID: number;
    /** Holds the indices of the geometry (quads) to draw. */
    indexBuffer: Buffer_2;
    /** The number of particles the buffer can hold. */
    private size;
    /** A list of the properties that are dynamic. */
    private dynamicProperties;
    /** A list of the properties that are static. */
    private staticProperties;
    /**
     * @param {object} properties - The properties to upload.
     * @param {boolean[]} dynamicPropertyFlags - Flags for which properties are dynamic.
     * @param {number} size - The size of the batch.
     */
    constructor(properties: IParticleRendererProperty[], dynamicPropertyFlags: boolean[], size: number);
    /** Sets up the renderer context and necessary buffers. */
    private initBuffers;
    /**
     * Uploads the dynamic properties.
     * @param children - The children to upload.
     * @param startIndex - The index to start at.
     * @param amount - The number to upload.
     */
    uploadDynamic(children: Sprite[], startIndex: number, amount: number): void;
    /**
     * Uploads the static properties.
     * @param children - The children to upload.
     * @param startIndex - The index to start at.
     * @param amount - The number to upload.
     */
    uploadStatic(children: Sprite[], startIndex: number, amount: number): void;
    /** Destroys the ParticleBuffer. */
    destroy(): void;
}

/**
 * The ParticleContainer class is a really fast version of the Container built solely for speed,
 * so use when you need a lot of sprites or particles.
 *
 * The tradeoff of the ParticleContainer is that most advanced functionality will not work.
 * ParticleContainer implements the basic object transform (position, scale, rotation)
 * and some advanced functionality like tint (as of v4.5.6).
 *
 * Other more advanced functionality like masking, children, filters, etc will not work on sprites in this batch.
 *
 * It's extremely easy to use:
 * ```js
 * let container = new ParticleContainer();
 *
 * for (let i = 0; i < 100; ++i)
 * {
 *     let sprite = PIXI.Sprite.from("myImage.png");
 *     container.addChild(sprite);
 * }
 * ```
 *
 * And here you have a hundred sprites that will be rendered at the speed of light.
 * @memberof PIXI
 */
export declare class ParticleContainer extends Container<Sprite> {
    /**
     * The blend mode to be applied to the sprite. Apply a value of `PIXI.BLEND_MODES.NORMAL`
     * to reset the blend mode.
     * @default PIXI.BLEND_MODES.NORMAL
     */
    blendMode: BLEND_MODES;
    /**
     * If true, container allocates more batches in case there are more than `maxSize` particles.
     * @default false
     */
    autoResize: boolean;
    /**
     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.
     * Advantages can include sharper image quality (like text) and faster rendering on canvas.
     * The main disadvantage is movement of objects may appear less smooth.
     * Default to true here as performance is usually the priority for particles.
     * @default true
     */
    roundPixels: boolean;
    /**
     * The texture used to render the children.
     * @readonly
     */
    baseTexture: BaseTexture;
    tintRgb: Float32Array;
    /** @private */
    _maxSize: number;
    /** @private */
    _buffers: ParticleBuffer[];
    /** @private */
    _batchSize: number;
    /**
     * Set properties to be dynamic (true) / static (false).
     * @private
     */
    _properties: boolean[];
    /**
     * For every batch, stores _updateID corresponding to the last change in that batch.
     * @private
     */
    _bufferUpdateIDs: number[];
    /**
     * When child inserted, removed or changes position this number goes up.
     * @private
     */
    _updateID: number;
    /**
     * The tint applied to the container.
     * This is a hex value. A value of 0xFFFFFF will remove any tint effect.
     * @default 0xFFFFFF
     */
    private _tint;
    /**
     * @param maxSize - The maximum number of particles that can be rendered by the container.
     *  Affects size of allocated buffers.
     * @param properties - The properties of children that should be uploaded to the gpu and applied.
     * @param {boolean} [properties.vertices=false] - When true, vertices be uploaded and applied.
     *                  if sprite's ` scale/anchor/trim/frame/orig` is dynamic, please set `true`.
     * @param {boolean} [properties.position=true] - When true, position be uploaded and applied.
     * @param {boolean} [properties.rotation=false] - When true, rotation be uploaded and applied.
     * @param {boolean} [properties.uvs=false] - When true, uvs be uploaded and applied.
     * @param {boolean} [properties.tint=false] - When true, alpha and tint be uploaded and applied.
     * @param {number} [batchSize=16384] - Number of particles per batch. If less than maxSize, it uses maxSize instead.
     * @param {boolean} [autoResize=false] - If true, container allocates more batches in case
     *  there are more than `maxSize` particles.
     */
    constructor(maxSize?: number, properties?: IParticleProperties, batchSize?: number, autoResize?: boolean);
    /**
     * Sets the private properties array to dynamic / static based on the passed properties object
     * @param properties - The properties to be uploaded
     */
    setProperties(properties: IParticleProperties): void;
    updateTransform(): void;
    /**
     * The tint applied to the container. This is a hex value.
     * A value of 0xFFFFFF will remove any tint effect.
     * IMPORTANT: This is a WebGL only feature and will be ignored by the canvas renderer.
     * @default 0xFFFFFF
     */
    get tint(): number;
    set tint(value: number);
    /**
     * Renders the container using the WebGL renderer.
     * @param renderer - The WebGL renderer.
     */
    render(renderer: Renderer): void;
    /**
     * Set the flag that static data should be updated to true
     * @param smallestChildIndex - The smallest child index.
     */
    protected onChildrenChange(smallestChildIndex: number): void;
    dispose(): void;
    /**
     * Destroys the container
     * @param options - Options parameter. A boolean will act as if all options
     *  have been set to that value
     * @param {boolean} [options.children=false] - if set to true, all the children will have their
     *  destroy method called as well. 'options' will be passed on to those calls.
     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true
     *  Should it destroy the texture of the child sprite
     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true
     *  Should it destroy the base texture of the child sprite
     */
    destroy(options?: IDestroyOptions | boolean): void;
}

/**
 * Renderer for Particles that is designer for speed over feature set.
 * @memberof PIXI
 */
export declare class ParticleRenderer extends ObjectRenderer {
    /** @ignore */
    static extension: ExtensionMetadata;
    /** The WebGL state in which this renderer will work. */
    readonly state: State;
    /** The default shader that is used if a sprite doesn't have a more specific one. */
    shader: Shader;
    tempMatrix: Matrix;
    properties: IParticleRendererProperty[];
    /**
     * @param renderer - The renderer this sprite batch works for.
     */
    constructor(renderer: Renderer);
    /**
     * Renders the particle container object.
     * @param container - The container to render using this ParticleRenderer.
     */
    render(container: ParticleContainer): void;
    /**
     * Creates one particle buffer for each child in the container we want to render and updates internal properties.
     * @param container - The container to render using this ParticleRenderer
     * @returns - The buffers
     */
    private generateBuffers;
    /**
     * Creates one more particle buffer, because container has autoResize feature.
     * @param container - The container to render using this ParticleRenderer
     * @returns - The generated buffer
     */
    private _generateOneMoreBuffer;
    /**
     * Uploads the vertices.
     * @param children - the array of sprites to render
     * @param startIndex - the index to start from in the children array
     * @param amount - the amount of children that will have their vertices uploaded
     * @param array - The vertices to upload.
     * @param stride - Stride to use for iteration.
     * @param offset - Offset to start at.
     */
    uploadVertices(children: Sprite[], startIndex: number, amount: number, array: number[], stride: number, offset: number): void;
    /**
     * Uploads the position.
     * @param children - the array of sprites to render
     * @param startIndex - the index to start from in the children array
     * @param amount - the amount of children that will have their positions uploaded
     * @param array - The vertices to upload.
     * @param stride - Stride to use for iteration.
     * @param offset - Offset to start at.
     */
    uploadPosition(children: Sprite[], startIndex: number, amount: number, array: number[], stride: number, offset: number): void;
    /**
     * Uploads the rotation.
     * @param children - the array of sprites to render
     * @param startIndex - the index to start from in the children array
     * @param amount - the amount of children that will have their rotation uploaded
     * @param array - The vertices to upload.
     * @param stride - Stride to use for iteration.
     * @param offset - Offset to start at.
     */
    uploadRotation(children: Sprite[], startIndex: number, amount: number, array: number[], stride: number, offset: number): void;
    /**
     * Uploads the UVs.
     * @param children - the array of sprites to render
     * @param startIndex - the index to start from in the children array
     * @param amount - the amount of children that will have their rotation uploaded
     * @param array - The vertices to upload.
     * @param stride - Stride to use for iteration.
     * @param offset - Offset to start at.
     */
    uploadUvs(children: Sprite[], startIndex: number, amount: number, array: number[], stride: number, offset: number): void;
    /**
     * Uploads the tint.
     * @param children - the array of sprites to render
     * @param startIndex - the index to start from in the children array
     * @param amount - the amount of children that will have their rotation uploaded
     * @param array - The vertices to upload.
     * @param stride - Stride to use for iteration.
     * @param offset - Offset to start at.
     */
    uploadTint(children: Sprite[], startIndex: number, amount: number, array: number[], stride: number, offset: number): void;
    /** Destroys the ParticleRenderer. */
    destroy(): void;
}

export { }
