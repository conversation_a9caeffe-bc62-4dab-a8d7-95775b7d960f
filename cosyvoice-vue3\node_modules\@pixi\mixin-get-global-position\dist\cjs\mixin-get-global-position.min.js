/*!
 * @pixi/mixin-get-global-position - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-get-global-position is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";var i=require("@pixi/display"),t=require("@pixi/math");i.DisplayObject.prototype.getGlobalPosition=function(i,o){return void 0===i&&(i=new t.Point),void 0===o&&(o=!1),this.parent?this.parent.toGlobal(this.position,i,o):(i.x=this.position.x,i.y=this.position.y),i};
//# sourceMappingURL=mixin-get-global-position.min.js.map
