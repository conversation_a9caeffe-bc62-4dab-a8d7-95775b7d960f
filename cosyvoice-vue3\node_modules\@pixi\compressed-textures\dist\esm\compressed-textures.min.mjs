/*!
 * @pixi/compressed-textures - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/compressed-textures is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{ViewableBuffer as _,BufferResource as R,ExtensionType as e,Texture as t,BaseTexture as T}from"@pixi/core";import{LoaderResource as r}from"@pixi/loaders";import{url as E}from"@pixi/utils";import{settings as G}from"@pixi/settings";import{MIPMAP_MODES as A,ALPHA_MODES as O,TYPES as D,FORMATS as S}from"@pixi/constants";var M,n;!function(_){_[_.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",_[_.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",_[_.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",_[_.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",_[_.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917]="COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT",_[_.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918]="COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT",_[_.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919]="COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT",_[_.COMPRESSED_SRGB_S3TC_DXT1_EXT=35916]="COMPRESSED_SRGB_S3TC_DXT1_EXT",_[_.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",_[_.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",_[_.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",_[_.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",_[_.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",_[_.COMPRESSED_RGBA8_ETC2_EAC=37496]="COMPRESSED_RGBA8_ETC2_EAC",_[_.COMPRESSED_SRGB8_ETC2=37493]="COMPRESSED_SRGB8_ETC2",_[_.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC",_[_.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",_[_.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",_[_.COMPRESSED_RGB_PVRTC_4BPPV1_IMG=35840]="COMPRESSED_RGB_PVRTC_4BPPV1_IMG",_[_.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=35842]="COMPRESSED_RGBA_PVRTC_4BPPV1_IMG",_[_.COMPRESSED_RGB_PVRTC_2BPPV1_IMG=35841]="COMPRESSED_RGB_PVRTC_2BPPV1_IMG",_[_.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=35843]="COMPRESSED_RGBA_PVRTC_2BPPV1_IMG",_[_.COMPRESSED_RGB_ETC1_WEBGL=36196]="COMPRESSED_RGB_ETC1_WEBGL",_[_.COMPRESSED_RGB_ATC_WEBGL=35986]="COMPRESSED_RGB_ATC_WEBGL",_[_.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=35986]="COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL",_[_.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=34798]="COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL",_[_.COMPRESSED_RGBA_ASTC_4x4_KHR=37808]="COMPRESSED_RGBA_ASTC_4x4_KHR"}(n||(n={}));var I=((M={})[n.COMPRESSED_RGB_S3TC_DXT1_EXT]=.5,M[n.COMPRESSED_RGBA_S3TC_DXT1_EXT]=.5,M[n.COMPRESSED_RGBA_S3TC_DXT3_EXT]=1,M[n.COMPRESSED_RGBA_S3TC_DXT5_EXT]=1,M[n.COMPRESSED_SRGB_S3TC_DXT1_EXT]=.5,M[n.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT]=.5,M[n.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT]=1,M[n.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT]=1,M[n.COMPRESSED_R11_EAC]=.5,M[n.COMPRESSED_SIGNED_R11_EAC]=.5,M[n.COMPRESSED_RG11_EAC]=1,M[n.COMPRESSED_SIGNED_RG11_EAC]=1,M[n.COMPRESSED_RGB8_ETC2]=.5,M[n.COMPRESSED_RGBA8_ETC2_EAC]=1,M[n.COMPRESSED_SRGB8_ETC2]=.5,M[n.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC]=1,M[n.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2]=.5,M[n.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2]=.5,M[n.COMPRESSED_RGB_PVRTC_4BPPV1_IMG]=.5,M[n.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG]=.5,M[n.COMPRESSED_RGB_PVRTC_2BPPV1_IMG]=.25,M[n.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG]=.25,M[n.COMPRESSED_RGB_ETC1_WEBGL]=.5,M[n.COMPRESSED_RGB_ATC_WEBGL]=.5,M[n.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL]=1,M[n.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL]=1,M[n.COMPRESSED_RGBA_ASTC_4x4_KHR]=1,M),o=function(_,R){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(_,R){_.__proto__=R}||function(_,R){for(var e in R)R.hasOwnProperty(e)&&(_[e]=R[e])},o(_,R)};function X(_,R){function e(){this.constructor=_}o(_,R),_.prototype=null===R?Object.create(R):(e.prototype=R.prototype,new e)}function a(_,R,e,t){return new(e||(e=Promise))((function(T,r){function E(_){try{A(t.next(_))}catch(_){r(_)}}function G(_){try{A(t.throw(_))}catch(_){r(_)}}function A(_){var R;_.done?T(_.value):(R=_.value,R instanceof e?R:new e((function(_){_(R)}))).then(E,G)}A((t=t.apply(_,R||[])).next())}))}function i(_,R){var e,t,T,r,E={label:0,sent:function(){if(1&T[0])throw T[1];return T[1]},trys:[],ops:[]};return r={next:G(0),throw:G(1),return:G(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function G(r){return function(G){return function(r){if(e)throw new TypeError("Generator is already executing.");for(;E;)try{if(e=1,t&&(T=2&r[0]?t.return:r[0]?t.throw||((T=t.return)&&T.call(t),0):t.next)&&!(T=T.call(t,r[1])).done)return T;switch(t=0,T&&(r=[2&r[0],T.value]),r[0]){case 0:case 1:T=r;break;case 4:return E.label++,{value:r[1],done:!1};case 5:E.label++,t=r[1],r=[0];continue;case 7:r=E.ops.pop(),E.trys.pop();continue;default:if(!(T=E.trys,(T=T.length>0&&T[T.length-1])||6!==r[0]&&2!==r[0])){E=0;continue}if(3===r[0]&&(!T||r[1]>T[0]&&r[1]<T[3])){E.label=r[1];break}if(6===r[0]&&E.label<T[1]){E.label=T[1],T=r;break}if(T&&E.label<T[2]){E.label=T[2],E.ops.push(r);break}T[2]&&E.ops.pop(),E.trys.pop();continue}r=R.call(_,E)}catch(_){r=[6,_],t=0}finally{e=T=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,G])}}}var B,F,P=function(R){function e(e,t){void 0===t&&(t={width:1,height:1,autoLoad:!0});var T,r,E=this;return"string"==typeof e?(T=e,r=new Uint8Array):(T=null,r=e),(E=R.call(this,r,t)||this).origin=T,E.buffer=r?new _(r):null,E.origin&&!1!==t.autoLoad&&E.load(),r&&r.length&&(E.loaded=!0,E.onBlobLoaded(E.buffer.rawBinaryData)),E}return X(e,R),e.prototype.onBlobLoaded=function(_){},e.prototype.load=function(){return a(this,void 0,Promise,(function(){var R;return i(this,(function(e){switch(e.label){case 0:return[4,fetch(this.origin)];case 1:return[4,e.sent().blob()];case 2:return[4,e.sent().arrayBuffer()];case 3:return R=e.sent(),this.data=new Uint32Array(R),this.buffer=new _(R),this.loaded=!0,this.onBlobLoaded(R),this.update(),[2,this]}}))}))},e}(R),u=function(_){function R(e,t){var T=_.call(this,e,t)||this;return T.format=t.format,T.levels=t.levels||1,T._width=t.width,T._height=t.height,T._extension=R._formatToExtension(T.format),(t.levelBuffers||T.buffer)&&(T._levelBuffers=t.levelBuffers||R._createLevelBuffers(e instanceof Uint8Array?e:T.buffer.uint8View,T.format,T.levels,4,4,T.width,T.height)),T}return X(R,_),R.prototype.upload=function(_,R,e){var t=_.gl;if(!_.context.extensions[this._extension])throw new Error(this._extension+" textures are not supported on the current machine");if(!this._levelBuffers)return!1;for(var T=0,r=this.levels;T<r;T++){var E=this._levelBuffers[T],G=E.levelID,A=E.levelWidth,O=E.levelHeight,D=E.levelBuffer;t.compressedTexImage2D(t.TEXTURE_2D,G,this.format,A,O,0,D)}return!0},R.prototype.onBlobLoaded=function(){this._levelBuffers=R._createLevelBuffers(this.buffer.uint8View,this.format,this.levels,4,4,this.width,this.height)},R._formatToExtension=function(_){if(_>=33776&&_<=33779)return"s3tc";if(_>=37488&&_<=37497)return"etc";if(_>=35840&&_<=35843)return"pvrtc";if(_>=36196)return"etc1";if(_>=35986&&_<=34798)return"atc";throw new Error("Invalid (compressed) texture format given!")},R._createLevelBuffers=function(_,R,e,t,T,r,E){for(var G=new Array(e),A=_.byteOffset,O=r,D=E,S=O+t-1&~(t-1),M=D+T-1&~(T-1),n=S*M*I[R],o=0;o<e;o++)G[o]={levelID:o,levelWidth:e>1?O:S,levelHeight:e>1?D:M,levelBuffer:new Uint8Array(_.buffer,A,n)},A+=n,n=(S=(O=O>>1||1)+t-1&~(t-1))*(M=(D=D>>1||1)+T-1&~(T-1))*I[R];return G},R}(P),s=function(){function _(){}return _.use=function(R,e){var t=R.data;if(R.type===r.TYPE.JSON&&t&&t.cacheID&&t.textures){for(var T=t.textures,G=void 0,A=void 0,O=0,D=T.length;O<D;O++){var S=T[O],M=S.src,n=S.format;if(n||(A=M),_.textureFormats[n]){G=M;break}}if(!(G=G||A))return void e(new Error("Cannot load compressed-textures in "+R.url+", make sure you provide a fallback"));if(G===R.url)return void e(new Error("URL of compressed texture cannot be the same as the manifest's URL"));var I={crossOrigin:R.crossOrigin,metadata:R.metadata.imageMetadata,parentResource:R},o=E.resolve(R.url.replace(this.baseUrl,""),G),X=t.cacheID;this.add(X,o,I,(function(_){if(_.error)e(_.error);else{var t=_.texture,T=void 0===t?null:t,r=_.textures,E=void 0===r?{}:r;Object.assign(R,{texture:T,textures:E}),e()}}))}else e()},Object.defineProperty(_,"textureExtensions",{get:function(){if(!_._textureExtensions){var R=G.ADAPTER.createCanvas().getContext("webgl");if(!R)return{};var e={s3tc:R.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:R.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:R.getExtension("WEBGL_compressed_texture_etc"),etc1:R.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:R.getExtension("WEBGL_compressed_texture_pvrtc")||R.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:R.getExtension("WEBGL_compressed_texture_atc"),astc:R.getExtension("WEBGL_compressed_texture_astc")};_._textureExtensions=e}return _._textureExtensions},enumerable:!1,configurable:!0}),Object.defineProperty(_,"textureFormats",{get:function(){if(!_._textureFormats){var R=_.textureExtensions;for(var e in _._textureFormats={},R){var t=R[e];t&&Object.assign(_._textureFormats,Object.getPrototypeOf(t))}}return _._textureFormats},enumerable:!1,configurable:!0}),_.extension=e.Loader,_}();function C(_,R,e){var r={textures:{},texture:null};return R?(R.map((function(_){return new t(new T(_,Object.assign({mipmap:A.OFF,alphaMode:O.NO_PREMULTIPLIED_ALPHA},e)))})).forEach((function(R,e){var E=R.baseTexture,G=_+"-"+(e+1);T.addToCache(E,G),t.addToCache(R,G),0===e&&(T.addToCache(E,_),t.addToCache(R,_),r.texture=R),r.textures[G]=R})),r):r}var f,N,l=3,c=4,U=7,L=19,d=2,h=0,v=1,p=2,x=3;!function(_){_[_.DXGI_FORMAT_UNKNOWN=0]="DXGI_FORMAT_UNKNOWN",_[_.DXGI_FORMAT_R32G32B32A32_TYPELESS=1]="DXGI_FORMAT_R32G32B32A32_TYPELESS",_[_.DXGI_FORMAT_R32G32B32A32_FLOAT=2]="DXGI_FORMAT_R32G32B32A32_FLOAT",_[_.DXGI_FORMAT_R32G32B32A32_UINT=3]="DXGI_FORMAT_R32G32B32A32_UINT",_[_.DXGI_FORMAT_R32G32B32A32_SINT=4]="DXGI_FORMAT_R32G32B32A32_SINT",_[_.DXGI_FORMAT_R32G32B32_TYPELESS=5]="DXGI_FORMAT_R32G32B32_TYPELESS",_[_.DXGI_FORMAT_R32G32B32_FLOAT=6]="DXGI_FORMAT_R32G32B32_FLOAT",_[_.DXGI_FORMAT_R32G32B32_UINT=7]="DXGI_FORMAT_R32G32B32_UINT",_[_.DXGI_FORMAT_R32G32B32_SINT=8]="DXGI_FORMAT_R32G32B32_SINT",_[_.DXGI_FORMAT_R16G16B16A16_TYPELESS=9]="DXGI_FORMAT_R16G16B16A16_TYPELESS",_[_.DXGI_FORMAT_R16G16B16A16_FLOAT=10]="DXGI_FORMAT_R16G16B16A16_FLOAT",_[_.DXGI_FORMAT_R16G16B16A16_UNORM=11]="DXGI_FORMAT_R16G16B16A16_UNORM",_[_.DXGI_FORMAT_R16G16B16A16_UINT=12]="DXGI_FORMAT_R16G16B16A16_UINT",_[_.DXGI_FORMAT_R16G16B16A16_SNORM=13]="DXGI_FORMAT_R16G16B16A16_SNORM",_[_.DXGI_FORMAT_R16G16B16A16_SINT=14]="DXGI_FORMAT_R16G16B16A16_SINT",_[_.DXGI_FORMAT_R32G32_TYPELESS=15]="DXGI_FORMAT_R32G32_TYPELESS",_[_.DXGI_FORMAT_R32G32_FLOAT=16]="DXGI_FORMAT_R32G32_FLOAT",_[_.DXGI_FORMAT_R32G32_UINT=17]="DXGI_FORMAT_R32G32_UINT",_[_.DXGI_FORMAT_R32G32_SINT=18]="DXGI_FORMAT_R32G32_SINT",_[_.DXGI_FORMAT_R32G8X24_TYPELESS=19]="DXGI_FORMAT_R32G8X24_TYPELESS",_[_.DXGI_FORMAT_D32_FLOAT_S8X24_UINT=20]="DXGI_FORMAT_D32_FLOAT_S8X24_UINT",_[_.DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS=21]="DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS",_[_.DXGI_FORMAT_X32_TYPELESS_G8X24_UINT=22]="DXGI_FORMAT_X32_TYPELESS_G8X24_UINT",_[_.DXGI_FORMAT_R10G10B10A2_TYPELESS=23]="DXGI_FORMAT_R10G10B10A2_TYPELESS",_[_.DXGI_FORMAT_R10G10B10A2_UNORM=24]="DXGI_FORMAT_R10G10B10A2_UNORM",_[_.DXGI_FORMAT_R10G10B10A2_UINT=25]="DXGI_FORMAT_R10G10B10A2_UINT",_[_.DXGI_FORMAT_R11G11B10_FLOAT=26]="DXGI_FORMAT_R11G11B10_FLOAT",_[_.DXGI_FORMAT_R8G8B8A8_TYPELESS=27]="DXGI_FORMAT_R8G8B8A8_TYPELESS",_[_.DXGI_FORMAT_R8G8B8A8_UNORM=28]="DXGI_FORMAT_R8G8B8A8_UNORM",_[_.DXGI_FORMAT_R8G8B8A8_UNORM_SRGB=29]="DXGI_FORMAT_R8G8B8A8_UNORM_SRGB",_[_.DXGI_FORMAT_R8G8B8A8_UINT=30]="DXGI_FORMAT_R8G8B8A8_UINT",_[_.DXGI_FORMAT_R8G8B8A8_SNORM=31]="DXGI_FORMAT_R8G8B8A8_SNORM",_[_.DXGI_FORMAT_R8G8B8A8_SINT=32]="DXGI_FORMAT_R8G8B8A8_SINT",_[_.DXGI_FORMAT_R16G16_TYPELESS=33]="DXGI_FORMAT_R16G16_TYPELESS",_[_.DXGI_FORMAT_R16G16_FLOAT=34]="DXGI_FORMAT_R16G16_FLOAT",_[_.DXGI_FORMAT_R16G16_UNORM=35]="DXGI_FORMAT_R16G16_UNORM",_[_.DXGI_FORMAT_R16G16_UINT=36]="DXGI_FORMAT_R16G16_UINT",_[_.DXGI_FORMAT_R16G16_SNORM=37]="DXGI_FORMAT_R16G16_SNORM",_[_.DXGI_FORMAT_R16G16_SINT=38]="DXGI_FORMAT_R16G16_SINT",_[_.DXGI_FORMAT_R32_TYPELESS=39]="DXGI_FORMAT_R32_TYPELESS",_[_.DXGI_FORMAT_D32_FLOAT=40]="DXGI_FORMAT_D32_FLOAT",_[_.DXGI_FORMAT_R32_FLOAT=41]="DXGI_FORMAT_R32_FLOAT",_[_.DXGI_FORMAT_R32_UINT=42]="DXGI_FORMAT_R32_UINT",_[_.DXGI_FORMAT_R32_SINT=43]="DXGI_FORMAT_R32_SINT",_[_.DXGI_FORMAT_R24G8_TYPELESS=44]="DXGI_FORMAT_R24G8_TYPELESS",_[_.DXGI_FORMAT_D24_UNORM_S8_UINT=45]="DXGI_FORMAT_D24_UNORM_S8_UINT",_[_.DXGI_FORMAT_R24_UNORM_X8_TYPELESS=46]="DXGI_FORMAT_R24_UNORM_X8_TYPELESS",_[_.DXGI_FORMAT_X24_TYPELESS_G8_UINT=47]="DXGI_FORMAT_X24_TYPELESS_G8_UINT",_[_.DXGI_FORMAT_R8G8_TYPELESS=48]="DXGI_FORMAT_R8G8_TYPELESS",_[_.DXGI_FORMAT_R8G8_UNORM=49]="DXGI_FORMAT_R8G8_UNORM",_[_.DXGI_FORMAT_R8G8_UINT=50]="DXGI_FORMAT_R8G8_UINT",_[_.DXGI_FORMAT_R8G8_SNORM=51]="DXGI_FORMAT_R8G8_SNORM",_[_.DXGI_FORMAT_R8G8_SINT=52]="DXGI_FORMAT_R8G8_SINT",_[_.DXGI_FORMAT_R16_TYPELESS=53]="DXGI_FORMAT_R16_TYPELESS",_[_.DXGI_FORMAT_R16_FLOAT=54]="DXGI_FORMAT_R16_FLOAT",_[_.DXGI_FORMAT_D16_UNORM=55]="DXGI_FORMAT_D16_UNORM",_[_.DXGI_FORMAT_R16_UNORM=56]="DXGI_FORMAT_R16_UNORM",_[_.DXGI_FORMAT_R16_UINT=57]="DXGI_FORMAT_R16_UINT",_[_.DXGI_FORMAT_R16_SNORM=58]="DXGI_FORMAT_R16_SNORM",_[_.DXGI_FORMAT_R16_SINT=59]="DXGI_FORMAT_R16_SINT",_[_.DXGI_FORMAT_R8_TYPELESS=60]="DXGI_FORMAT_R8_TYPELESS",_[_.DXGI_FORMAT_R8_UNORM=61]="DXGI_FORMAT_R8_UNORM",_[_.DXGI_FORMAT_R8_UINT=62]="DXGI_FORMAT_R8_UINT",_[_.DXGI_FORMAT_R8_SNORM=63]="DXGI_FORMAT_R8_SNORM",_[_.DXGI_FORMAT_R8_SINT=64]="DXGI_FORMAT_R8_SINT",_[_.DXGI_FORMAT_A8_UNORM=65]="DXGI_FORMAT_A8_UNORM",_[_.DXGI_FORMAT_R1_UNORM=66]="DXGI_FORMAT_R1_UNORM",_[_.DXGI_FORMAT_R9G9B9E5_SHAREDEXP=67]="DXGI_FORMAT_R9G9B9E5_SHAREDEXP",_[_.DXGI_FORMAT_R8G8_B8G8_UNORM=68]="DXGI_FORMAT_R8G8_B8G8_UNORM",_[_.DXGI_FORMAT_G8R8_G8B8_UNORM=69]="DXGI_FORMAT_G8R8_G8B8_UNORM",_[_.DXGI_FORMAT_BC1_TYPELESS=70]="DXGI_FORMAT_BC1_TYPELESS",_[_.DXGI_FORMAT_BC1_UNORM=71]="DXGI_FORMAT_BC1_UNORM",_[_.DXGI_FORMAT_BC1_UNORM_SRGB=72]="DXGI_FORMAT_BC1_UNORM_SRGB",_[_.DXGI_FORMAT_BC2_TYPELESS=73]="DXGI_FORMAT_BC2_TYPELESS",_[_.DXGI_FORMAT_BC2_UNORM=74]="DXGI_FORMAT_BC2_UNORM",_[_.DXGI_FORMAT_BC2_UNORM_SRGB=75]="DXGI_FORMAT_BC2_UNORM_SRGB",_[_.DXGI_FORMAT_BC3_TYPELESS=76]="DXGI_FORMAT_BC3_TYPELESS",_[_.DXGI_FORMAT_BC3_UNORM=77]="DXGI_FORMAT_BC3_UNORM",_[_.DXGI_FORMAT_BC3_UNORM_SRGB=78]="DXGI_FORMAT_BC3_UNORM_SRGB",_[_.DXGI_FORMAT_BC4_TYPELESS=79]="DXGI_FORMAT_BC4_TYPELESS",_[_.DXGI_FORMAT_BC4_UNORM=80]="DXGI_FORMAT_BC4_UNORM",_[_.DXGI_FORMAT_BC4_SNORM=81]="DXGI_FORMAT_BC4_SNORM",_[_.DXGI_FORMAT_BC5_TYPELESS=82]="DXGI_FORMAT_BC5_TYPELESS",_[_.DXGI_FORMAT_BC5_UNORM=83]="DXGI_FORMAT_BC5_UNORM",_[_.DXGI_FORMAT_BC5_SNORM=84]="DXGI_FORMAT_BC5_SNORM",_[_.DXGI_FORMAT_B5G6R5_UNORM=85]="DXGI_FORMAT_B5G6R5_UNORM",_[_.DXGI_FORMAT_B5G5R5A1_UNORM=86]="DXGI_FORMAT_B5G5R5A1_UNORM",_[_.DXGI_FORMAT_B8G8R8A8_UNORM=87]="DXGI_FORMAT_B8G8R8A8_UNORM",_[_.DXGI_FORMAT_B8G8R8X8_UNORM=88]="DXGI_FORMAT_B8G8R8X8_UNORM",_[_.DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM=89]="DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM",_[_.DXGI_FORMAT_B8G8R8A8_TYPELESS=90]="DXGI_FORMAT_B8G8R8A8_TYPELESS",_[_.DXGI_FORMAT_B8G8R8A8_UNORM_SRGB=91]="DXGI_FORMAT_B8G8R8A8_UNORM_SRGB",_[_.DXGI_FORMAT_B8G8R8X8_TYPELESS=92]="DXGI_FORMAT_B8G8R8X8_TYPELESS",_[_.DXGI_FORMAT_B8G8R8X8_UNORM_SRGB=93]="DXGI_FORMAT_B8G8R8X8_UNORM_SRGB",_[_.DXGI_FORMAT_BC6H_TYPELESS=94]="DXGI_FORMAT_BC6H_TYPELESS",_[_.DXGI_FORMAT_BC6H_UF16=95]="DXGI_FORMAT_BC6H_UF16",_[_.DXGI_FORMAT_BC6H_SF16=96]="DXGI_FORMAT_BC6H_SF16",_[_.DXGI_FORMAT_BC7_TYPELESS=97]="DXGI_FORMAT_BC7_TYPELESS",_[_.DXGI_FORMAT_BC7_UNORM=98]="DXGI_FORMAT_BC7_UNORM",_[_.DXGI_FORMAT_BC7_UNORM_SRGB=99]="DXGI_FORMAT_BC7_UNORM_SRGB",_[_.DXGI_FORMAT_AYUV=100]="DXGI_FORMAT_AYUV",_[_.DXGI_FORMAT_Y410=101]="DXGI_FORMAT_Y410",_[_.DXGI_FORMAT_Y416=102]="DXGI_FORMAT_Y416",_[_.DXGI_FORMAT_NV12=103]="DXGI_FORMAT_NV12",_[_.DXGI_FORMAT_P010=104]="DXGI_FORMAT_P010",_[_.DXGI_FORMAT_P016=105]="DXGI_FORMAT_P016",_[_.DXGI_FORMAT_420_OPAQUE=106]="DXGI_FORMAT_420_OPAQUE",_[_.DXGI_FORMAT_YUY2=107]="DXGI_FORMAT_YUY2",_[_.DXGI_FORMAT_Y210=108]="DXGI_FORMAT_Y210",_[_.DXGI_FORMAT_Y216=109]="DXGI_FORMAT_Y216",_[_.DXGI_FORMAT_NV11=110]="DXGI_FORMAT_NV11",_[_.DXGI_FORMAT_AI44=111]="DXGI_FORMAT_AI44",_[_.DXGI_FORMAT_IA44=112]="DXGI_FORMAT_IA44",_[_.DXGI_FORMAT_P8=113]="DXGI_FORMAT_P8",_[_.DXGI_FORMAT_A8P8=114]="DXGI_FORMAT_A8P8",_[_.DXGI_FORMAT_B4G4R4A4_UNORM=115]="DXGI_FORMAT_B4G4R4A4_UNORM",_[_.DXGI_FORMAT_P208=116]="DXGI_FORMAT_P208",_[_.DXGI_FORMAT_V208=117]="DXGI_FORMAT_V208",_[_.DXGI_FORMAT_V408=118]="DXGI_FORMAT_V408",_[_.DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE=119]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE",_[_.DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE=120]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE",_[_.DXGI_FORMAT_FORCE_UINT=121]="DXGI_FORMAT_FORCE_UINT"}(f||(f={})),function(_){_[_.DDS_DIMENSION_TEXTURE1D=2]="DDS_DIMENSION_TEXTURE1D",_[_.DDS_DIMENSION_TEXTURE2D=3]="DDS_DIMENSION_TEXTURE2D",_[_.DDS_DIMENSION_TEXTURE3D=6]="DDS_DIMENSION_TEXTURE3D"}(N||(N={}));var w,m,y,Y=((B={})[827611204]=n.COMPRESSED_RGBA_S3TC_DXT1_EXT,B[861165636]=n.COMPRESSED_RGBA_S3TC_DXT3_EXT,B[894720068]=n.COMPRESSED_RGBA_S3TC_DXT5_EXT,B),b=((F={})[f.DXGI_FORMAT_BC1_TYPELESS]=n.COMPRESSED_RGBA_S3TC_DXT1_EXT,F[f.DXGI_FORMAT_BC1_UNORM]=n.COMPRESSED_RGBA_S3TC_DXT1_EXT,F[f.DXGI_FORMAT_BC2_TYPELESS]=n.COMPRESSED_RGBA_S3TC_DXT3_EXT,F[f.DXGI_FORMAT_BC2_UNORM]=n.COMPRESSED_RGBA_S3TC_DXT3_EXT,F[f.DXGI_FORMAT_BC3_TYPELESS]=n.COMPRESSED_RGBA_S3TC_DXT5_EXT,F[f.DXGI_FORMAT_BC3_UNORM]=n.COMPRESSED_RGBA_S3TC_DXT5_EXT,F[f.DXGI_FORMAT_BC1_UNORM_SRGB]=n.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,F[f.DXGI_FORMAT_BC2_UNORM_SRGB]=n.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,F[f.DXGI_FORMAT_BC3_UNORM_SRGB]=n.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,F);function g(_){var R=new Uint32Array(_);if(542327876!==R[0])throw new Error("Invalid DDS file magic word");var e=new Uint32Array(_,0,124/Uint32Array.BYTES_PER_ELEMENT),t=e[l],T=e[c],r=e[U],E=new Uint32Array(_,L*Uint32Array.BYTES_PER_ELEMENT,32/Uint32Array.BYTES_PER_ELEMENT),G=E[1];if(4&G){var A=E[d];if(808540228!==A){var O=Y[A],D=new Uint8Array(_,128);return[new u(D,{format:O,width:T,height:t,levels:r})]}var S=new Uint32Array(R.buffer,128,20/Uint32Array.BYTES_PER_ELEMENT),M=S[h],n=S[v],o=S[p],X=S[x],a=b[M];if(void 0===a)throw new Error("DDSParser cannot parse texture data with DXGI format "+M);if(4===o)throw new Error("DDSParser does not support cubemap textures");if(n===N.DDS_DIMENSION_TEXTURE3D)throw new Error("DDSParser does not supported 3D texture data");var i=new Array;if(1===X)i.push(new Uint8Array(_,148));else{for(var B=I[a],F=0,P=T,s=t,C=0;C<r;C++){F+=Math.max(1,P+3&-4)*Math.max(1,s+3&-4)*B,P>>>=1,s>>>=1}var f=148;for(C=0;C<X;C++)i.push(new Uint8Array(_,f,F)),f+=F}return i.map((function(_){return new u(_,{format:a,width:T,height:t,levels:r})}))}if(64&G)throw new Error("DDSParser does not support uncompressed texture data.");if(512&G)throw new Error("DDSParser does not supported YUV uncompressed texture data.");if(131072&G)throw new Error("DDSParser does not support single-channel (lumninance) texture data!");if(2&G)throw new Error("DDSParser does not support single-channel (alpha) texture data!");throw new Error("DDSParser failed to load a texture file due to an unknown reason!")}var H=[171,75,84,88,32,49,49,187,13,10,26,10],V=12,W=16,k=24,K=28,j=36,Q=40,z=44,J=48,q=52,Z=56,$=60,__=((w={})[D.UNSIGNED_BYTE]=1,w[D.UNSIGNED_SHORT]=2,w[D.INT]=4,w[D.UNSIGNED_INT]=4,w[D.FLOAT]=4,w[D.HALF_FLOAT]=8,w),R_=((m={})[S.RGBA]=4,m[S.RGB]=3,m[S.RG]=2,m[S.RED]=1,m[S.LUMINANCE]=1,m[S.LUMINANCE_ALPHA]=2,m[S.ALPHA]=1,m),e_=((y={})[D.UNSIGNED_SHORT_4_4_4_4]=2,y[D.UNSIGNED_SHORT_5_5_5_1]=2,y[D.UNSIGNED_SHORT_5_6_5]=2,y);function t_(_,e,t){void 0===t&&(t=!1);var T=new DataView(e);if(!function(_,R){for(var e=0;e<H.length;e++)if(R.getUint8(e)!==H[e])return!1;return!0}(0,T))return null;var r=67305985===T.getUint32(V,!0),E=T.getUint32(W,r),G=T.getUint32(k,r),A=T.getUint32(K,r),O=T.getUint32(j,r),S=T.getUint32(Q,r)||1,M=T.getUint32(z,r)||1,n=T.getUint32(J,r)||1,o=T.getUint32(q,r),X=T.getUint32(Z,r),a=T.getUint32($,r);if(0===S||1!==M)throw new Error("Only 2D textures are supported");if(1!==o)throw new Error("CubeTextures are not supported by KTXLoader yet!");if(1!==n)throw new Error("WebGL does not support array textures");var i,B=O+3&-4,F=S+3&-4,P=new Array(n),s=O*S;if(0===E&&(s=B*F),void 0===(i=0!==E?__[E]?__[E]*R_[G]:e_[E]:I[A]))throw new Error("Unable to resolve the pixel format stored in the *.ktx file!");for(var C=t?function(_,R,e){var t=new Map,T=0;for(;T<R;){var r=_.getUint32(64+T,e),E=64+T+4,G=3-(r+3)%4;if(0===r||r>R-T){console.error("KTXLoader: keyAndValueByteSize out of bounds");break}for(var A=0;A<r&&0!==_.getUint8(E+A);A++);if(-1===A){console.error("KTXLoader: Failed to find null byte terminating kvData key");break}var O=(new TextDecoder).decode(new Uint8Array(_.buffer,E,A)),D=new DataView(_.buffer,E+A+1,r-A-1);t.set(O,D),T+=4+r+G}return t}(T,a,r):null,f=s*i,N=O,l=S,c=B,U=F,L=64+a,d=0;d<X;d++){for(var h=T.getUint32(L,r),v=L+4,p=0;p<n;p++){var x=P[p];x||(x=P[p]=new Array(X)),x[d]={levelID:d,levelWidth:X>1||0!==E?N:c,levelHeight:X>1||0!==E?l:U,levelBuffer:new Uint8Array(e,v,f)},v+=f}L=(L+=h+4)%4!=0?L+4-L%4:L,f=(c=(N=N>>1||1)****&-4)*(U=(l=l>>1||1)****&-4)*i}return 0!==E?{uncompressed:P.map((function(_){var e=_[0].levelBuffer,t=!1;return E===D.FLOAT?e=new Float32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4):E===D.UNSIGNED_INT?(t=!0,e=new Uint32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4)):E===D.INT&&(t=!0,e=new Int32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4)),{resource:new R(e,{width:_[0].levelWidth,height:_[0].levelHeight}),type:E,format:t?T_(G):G}})),kvData:C}:{compressed:P.map((function(_){return new u(null,{format:A,width:O,height:S,levels:X,levelBuffers:_})})),kvData:C}}function T_(_){switch(_){case S.RGBA:return S.RGBA_INTEGER;case S.RGB:return S.RGB_INTEGER;case S.RG:return S.RG_INTEGER;case S.RED:return S.RED_INTEGER;default:return _}}r.setExtensionXhrType("dds",r.XHR_RESPONSE_TYPE.BUFFER);var r_=function(){function _(){}return _.use=function(_,R){if("dds"===_.extension&&_.data)try{Object.assign(_,C(_.name||_.url,g(_.data),_.metadata))}catch(_){return void R(_)}R()},_.extension=e.Loader,_}();r.setExtensionXhrType("ktx",r.XHR_RESPONSE_TYPE.BUFFER);var E_=function(){function _(){}return _.use=function(_,R){if("ktx"===_.extension&&_.data)try{var e=_.name||_.url,r=t_(0,_.data,this.loadKeyValueData),E=r.compressed,G=r.uncompressed,D=r.kvData;if(E){var S=C(e,E,_.metadata);if(D&&S.textures)for(var M in S.textures)S.textures[M].baseTexture.ktxKeyValueData=D;Object.assign(_,S)}else if(G){var n={};G.forEach((function(_,R){var r=new t(new T(_.resource,{mipmap:A.OFF,alphaMode:O.NO_PREMULTIPLIED_ALPHA,type:_.type,format:_.format})),E=e+"-"+(R+1);D&&(r.baseTexture.ktxKeyValueData=D),T.addToCache(r.baseTexture,E),t.addToCache(r,E),0===R&&(n[e]=r,T.addToCache(r.baseTexture,e),t.addToCache(r,e)),n[E]=r})),Object.assign(_,{textures:n})}}catch(_){return void R(_)}R()},_.extension=e.Loader,_.loadKeyValueData=!1,_}();export{P as BlobResource,s as CompressedTextureLoader,u as CompressedTextureResource,r_ as DDSLoader,R_ as FORMATS_TO_COMPONENTS,n as INTERNAL_FORMATS,I as INTERNAL_FORMAT_TO_BYTES_PER_PIXEL,E_ as KTXLoader,__ as TYPES_TO_BYTES_PER_COMPONENT,e_ as TYPES_TO_BYTES_PER_PIXEL,g as parseDDS,t_ as parseKTX};
//# sourceMappingURL=compressed-textures.min.mjs.map
