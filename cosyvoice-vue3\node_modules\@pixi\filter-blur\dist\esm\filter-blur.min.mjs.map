{"version": 3, "file": "filter-blur.min.mjs", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/generateBlurFragSource.ts", "../../src/BlurFilterPass.ts", "../../src/generateBlurVertSource.ts", "../../src/BlurFilter.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "interface IGAUSSIAN_VALUES\n{\n    [x: number]: number[];\n}\nconst GAUSSIAN_VALUES: IGAUSSIAN_VALUES = {\n    5: [0.153388, 0.221461, 0.250301],\n    7: [0.071303, 0.131514, 0.189879, 0.214607],\n    9: [0.028532, 0.067234, 0.124009, 0.179044, 0.20236],\n    11: [0.0093, 0.028002, 0.065984, 0.121703, 0.175713, 0.198596],\n    13: [0.002406, 0.009255, 0.027867, 0.065666, 0.121117, 0.174868, 0.197641],\n    15: [0.000489, 0.002403, 0.009246, 0.02784, 0.065602, 0.120999, 0.174697, 0.197448],\n};\n\nconst fragTemplate = [\n    'varying vec2 vBlurTexCoords[%size%];',\n    'uniform sampler2D uSampler;',\n\n    'void main(void)',\n    '{',\n    '    gl_FragColor = vec4(0.0);',\n    '    %blur%',\n    '}',\n\n].join('\\n');\n\nexport function generateBlurFragSource(kernelSize: number): string\n{\n    const kernel = GAUSSIAN_VALUES[kernelSize];\n    const halfLength = kernel.length;\n\n    let fragSource = fragTemplate;\n\n    let blurLoop = '';\n    const template = 'gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;';\n    let value: number;\n\n    for (let i = 0; i < kernelSize; i++)\n    {\n        let blur = template.replace('%index%', i.toString());\n\n        value = i;\n\n        if (i >= halfLength)\n        {\n            value = kernelSize - i - 1;\n        }\n\n        blur = blur.replace('%value%', kernel[value].toString());\n\n        blurLoop += blur;\n        blurLoop += '\\n';\n    }\n\n    fragSource = fragSource.replace('%blur%', blurLoop);\n    fragSource = fragSource.replace('%size%', kernelSize.toString());\n\n    return fragSource;\n}\n", "import { Filter } from '@pixi/core';\nimport { settings } from '@pixi/settings';\nimport { generateBlurVertSource } from './generateBlurVertSource';\nimport { generateBlurFragSource } from './generateBlurFragSource';\nimport { CLEAR_MODES } from '@pixi/constants';\n\nimport type { FilterSystem, RenderTexture } from '@pixi/core';\n\n/**\n * The BlurFilterPass applies a horizontal or vertical Gaussian blur to an object.\n * @memberof PIXI.filters\n */\nexport class BlurFilterPass extends Filter\n{\n    public horizontal: boolean;\n    public strength: number;\n    public passes: number;\n\n    private _quality: number;\n\n    /**\n     * @param horizontal - Do pass along the x-axis (`true`) or y-axis (`false`).\n     * @param strength - The strength of the blur filter.\n     * @param quality - The quality of the blur filter.\n     * @param resolution - The resolution of the blur filter.\n     * @param kernelSize - The kernelSize of the blur filter.Options: 5, 7, 9, 11, 13, 15.\n     */\n    constructor(horizontal: boolean, strength = 8, quality = 4, resolution = settings.FILTER_RESOLUTION, kernelSize = 5)\n    {\n        const vertSrc = generateBlurVertSource(kernelSize, horizontal);\n        const fragSrc = generateBlurFragSource(kernelSize);\n\n        super(\n            // vertex shader\n            vertSrc,\n            // fragment shader\n            fragSrc\n        );\n\n        this.horizontal = horizontal;\n\n        this.resolution = resolution;\n\n        this._quality = 0;\n\n        this.quality = quality;\n\n        this.blur = strength;\n    }\n\n    /**\n     * Applies the filter.\n     * @param filterManager - The manager.\n     * @param input - The input target.\n     * @param output - The output target.\n     * @param clearMode - How to clear\n     */\n    public apply(\n        filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode: CLEAR_MODES\n    ): void\n    {\n        if (output)\n        {\n            if (this.horizontal)\n            {\n                this.uniforms.strength = (1 / output.width) * (output.width / input.width);\n            }\n            else\n            {\n                this.uniforms.strength = (1 / output.height) * (output.height / input.height);\n            }\n        }\n        else\n        {\n            if (this.horizontal) // eslint-disable-line\n            {\n                this.uniforms.strength = (1 / filterManager.renderer.width) * (filterManager.renderer.width / input.width);\n            }\n            else\n            {\n                this.uniforms.strength = (1 / filterManager.renderer.height) * (filterManager.renderer.height / input.height); // eslint-disable-line\n            }\n        }\n\n        // screen space!\n        this.uniforms.strength *= this.strength;\n        this.uniforms.strength /= this.passes;\n\n        if (this.passes === 1)\n        {\n            filterManager.applyFilter(this, input, output, clearMode);\n        }\n        else\n        {\n            const renderTarget = filterManager.getFilterTexture();\n            const renderer = filterManager.renderer;\n\n            let flip = input;\n            let flop = renderTarget;\n\n            this.state.blend = false;\n            filterManager.applyFilter(this, flip, flop, CLEAR_MODES.CLEAR);\n\n            for (let i = 1; i < this.passes - 1; i++)\n            {\n                filterManager.bindAndClear(flip, CLEAR_MODES.BLIT);\n\n                this.uniforms.uSampler = flop;\n\n                const temp = flop;\n\n                flop = flip;\n                flip = temp;\n\n                renderer.shader.bind(this);\n                renderer.geometry.draw(5);\n            }\n\n            this.state.blend = true;\n            filterManager.applyFilter(this, flop, output, clearMode);\n            filterManager.returnFilterTexture(renderTarget);\n        }\n    }\n    /**\n     * Sets the strength of both the blur.\n     * @default 16\n     */\n    get blur(): number\n    {\n        return this.strength;\n    }\n\n    set blur(value: number)\n    {\n        this.padding = 1 + (Math.abs(value) * 2);\n        this.strength = value;\n    }\n\n    /**\n     * Sets the quality of the blur by modifying the number of passes. More passes means higher\n     * quality bluring but the lower the performance.\n     * @default 4\n     */\n    get quality(): number\n    {\n        return this._quality;\n    }\n\n    set quality(value: number)\n    {\n        this._quality = value;\n        this.passes = value;\n    }\n}\n", "const vertTemplate = `\n    attribute vec2 aVertexPosition;\n\n    uniform mat3 projectionMatrix;\n\n    uniform float strength;\n\n    varying vec2 vBlurTexCoords[%size%];\n\n    uniform vec4 inputSize;\n    uniform vec4 outputFrame;\n\n    vec4 filterVertexPosition( void )\n    {\n        vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;\n\n        return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);\n    }\n\n    vec2 filterTextureCoord( void )\n    {\n        return aVertexPosition * (outputFrame.zw * inputSize.zw);\n    }\n\n    void main(void)\n    {\n        gl_Position = filterVertexPosition();\n\n        vec2 textureCoord = filterTextureCoord();\n        %blur%\n    }`;\n\nexport function generateBlurVertSource(kernelSize: number, x: boolean): string\n{\n    const halfLength = Math.ceil(kernelSize / 2);\n\n    let vertSource = vertTemplate;\n\n    let blurLoop = '';\n    let template;\n\n    if (x)\n    {\n        template = 'vBlurTexCoords[%index%] =  textureCoord + vec2(%sampleIndex% * strength, 0.0);';\n    }\n    else\n    {\n        template = 'vBlurTexCoords[%index%] =  textureCoord + vec2(0.0, %sampleIndex% * strength);';\n    }\n\n    for (let i = 0; i < kernelSize; i++)\n    {\n        let blur = template.replace('%index%', i.toString());\n\n        blur = blur.replace('%sampleIndex%', `${i - (halfLength - 1)}.0`);\n\n        blurLoop += blur;\n        blurLoop += '\\n';\n    }\n\n    vertSource = vertSource.replace('%blur%', blurLoop);\n    vertSource = vertSource.replace('%size%', kernelSize.toString());\n\n    return vertSource;\n}\n", "import { Filter } from '@pixi/core';\nimport { settings } from '@pixi/settings';\nimport { BlurFilterPass } from './BlurFilterPass';\nimport { CLEAR_MODES } from '@pixi/constants';\n\nimport type { FilterSystem, RenderTexture } from '@pixi/core';\nimport type { BLEND_MODES } from '@pixi/constants';\n\n/**\n * The BlurFilter applies a Gaussian blur to an object.\n *\n * The strength of the blur can be set for the x-axis and y-axis separately.\n * @memberof PIXI.filters\n */\nexport class BlurFilter extends Filter\n{\n    public blurXFilter: BlurFilterPass;\n    public blurYFilter: BlurFilterPass;\n\n    private _repeatEdgePixels: boolean;\n\n    /**\n     * @param strength - The strength of the blur filter.\n     * @param quality - The quality of the blur filter.\n     * @param [resolution=PIXI.settings.FILTER_RESOLUTION] - The resolution of the blur filter.\n     * @param kernelSize - The kernelSize of the blur filter.Options: 5, 7, 9, 11, 13, 15.\n     */\n    constructor(strength = 8, quality = 4, resolution = settings.FILTER_RESOLUTION, kernelSize = 5)\n    {\n        super();\n\n        this.blurXFilter = new BlurFilterPass(true, strength, quality, resolution, kernelSize);\n        this.blurYFilter = new BlurFilterPass(false, strength, quality, resolution, kernelSize);\n\n        this.resolution = resolution;\n        this.quality = quality;\n        this.blur = strength;\n\n        this.repeatEdgePixels = false;\n    }\n\n    /**\n     * Applies the filter.\n     * @param filterManager - The manager.\n     * @param input - The input target.\n     * @param output - The output target.\n     * @param clearMode - How to clear\n     */\n    apply(filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode: CLEAR_MODES): void\n    {\n        const xStrength = Math.abs(this.blurXFilter.strength);\n        const yStrength = Math.abs(this.blurYFilter.strength);\n\n        if (xStrength && yStrength)\n        {\n            const renderTarget = filterManager.getFilterTexture();\n\n            this.blurXFilter.apply(filterManager, input, renderTarget, CLEAR_MODES.CLEAR);\n            this.blurYFilter.apply(filterManager, renderTarget, output, clearMode);\n\n            filterManager.returnFilterTexture(renderTarget);\n        }\n        else if (yStrength)\n        {\n            this.blurYFilter.apply(filterManager, input, output, clearMode);\n        }\n        else\n        {\n            this.blurXFilter.apply(filterManager, input, output, clearMode);\n        }\n    }\n\n    protected updatePadding(): void\n    {\n        if (this._repeatEdgePixels)\n        {\n            this.padding = 0;\n        }\n        else\n        {\n            this.padding = Math.max(Math.abs(this.blurXFilter.strength), Math.abs(this.blurYFilter.strength)) * 2;\n        }\n    }\n\n    /**\n     * Sets the strength of both the blurX and blurY properties simultaneously\n     * @default 2\n     */\n    get blur(): number\n    {\n        return this.blurXFilter.blur;\n    }\n\n    set blur(value: number)\n    {\n        this.blurXFilter.blur = this.blurYFilter.blur = value;\n        this.updatePadding();\n    }\n\n    /**\n     * Sets the number of passes for blur. More passes means higher quality bluring.\n     * @default 1\n     */\n    get quality(): number\n    {\n        return this.blurXFilter.quality;\n    }\n\n    set quality(value: number)\n    {\n        this.blurXFilter.quality = this.blurYFilter.quality = value;\n    }\n\n    /**\n     * Sets the strength of the blurX property\n     * @default 2\n     */\n    get blurX(): number\n    {\n        return this.blurXFilter.blur;\n    }\n\n    set blurX(value: number)\n    {\n        this.blurXFilter.blur = value;\n        this.updatePadding();\n    }\n\n    /**\n     * Sets the strength of the blurY property\n     * @default 2\n     */\n    get blurY(): number\n    {\n        return this.blurYFilter.blur;\n    }\n\n    set blurY(value: number)\n    {\n        this.blurYFilter.blur = value;\n        this.updatePadding();\n    }\n\n    /**\n     * Sets the blendmode of the filter\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    get blendMode(): BLEND_MODES\n    {\n        return this.blurYFilter.blendMode;\n    }\n\n    set blendMode(value: BLEND_MODES)\n    {\n        this.blurYFilter.blendMode = value;\n    }\n\n    /**\n     * If set to true the edge of the target will be clamped\n     * @default false\n     */\n    get repeatEdgePixels(): boolean\n    {\n        return this._repeatEdgePixels;\n    }\n\n    set repeatEdgePixels(value: boolean)\n    {\n        this._repeatEdgePixels = value;\n        this.updatePadding();\n    }\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "this", "constructor", "prototype", "create", "GAUSSIAN_VALUES", "fragTemplate", "join", "Blur<PERSON>ilterPass", "_super", "horizontal", "strength", "quality", "resolution", "kernelSize", "settings", "FILTER_RESOLUTION", "_this", "vertSrc", "x", "template", "<PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "vertSource", "blurLoop", "i", "blur", "replace", "toString", "generateBlurVertSource", "fragSrc", "value", "kernel", "length", "fragSource", "generateBlurFragSource", "call", "_quality", "apply", "filterManager", "input", "output", "clearMode", "uniforms", "width", "height", "renderer", "passes", "applyFilter", "renderTarget", "getFilterTexture", "flip", "flop", "state", "blend", "CLEAR_MODES", "CLEAR", "bindAndClear", "BLIT", "uSampler", "temp", "shader", "bind", "geometry", "draw", "returnFilterTexture", "defineProperty", "get", "set", "padding", "abs", "Filter", "BlurFilter", "blurXFilter", "blurYFilter", "repeatEdgePixels", "xStrength", "yStrength", "updatePadding", "_repeatEdgePixels", "max", "blendMode"], "mappings": ";;;;;;;4HAgBA,IAAIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,IAGrB,SAASO,EAAUR,EAAGC,GAEzB,SAASQ,IAAOC,KAAKC,YAAcX,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEY,UAAkB,OAANX,EAAaC,OAAOW,OAAOZ,IAAMQ,EAAGG,UAAYX,EAAEW,UAAW,IAAIH,GCtBnF,IAAMK,EAAoC,CACtC,EAAG,CAAC,QAAU,QAAU,SACxB,EAAG,CAAC,QAAU,QAAU,QAAU,SAClC,EAAG,CAAC,QAAU,QAAU,QAAU,QAAU,QAC5C,GAAI,CAAC,MAAQ,QAAU,QAAU,QAAU,QAAU,SACrD,GAAI,CAAC,QAAU,QAAU,QAAU,QAAU,QAAU,QAAU,SACjE,GAAI,CAAC,OAAU,QAAU,QAAU,OAAS,QAAU,QAAU,QAAU,UAGxEC,EAAe,CACjB,uCACA,8BAEA,kBACA,IACA,gCACA,aACA,KAEFC,KAAK,MCXP,IAAAC,EAAA,SAAAC,GAeI,SAAYD,EAAAE,EAAqBC,EAAcC,EAAaC,EAAyCC,QAApE,IAAAH,IAAAA,EAAY,QAAE,IAAAC,IAAAA,EAAW,QAAE,IAAAC,IAAAA,EAAaE,EAASC,wBAAmB,IAAAF,IAAAA,EAAc,GAAnH,IAqBCG,EAAAhB,KAnBSiB,ECGE,SAAuBJ,EAAoBK,GAEvD,IAKIC,EALEC,EAAaC,KAAKC,KAAKT,EAAa,GAEtCU,EApCa,ysBAsCbC,EAAW,GAKXL,EAFAD,EAEW,iFAIA,iFAGf,IAAK,IAAIO,EAAI,EAAGA,EAAIZ,EAAYY,IAChC,CACI,IAAIC,EAAOP,EAASQ,QAAQ,UAAWF,EAAEG,YAIzCJ,GAFAE,EAAOA,EAAKC,QAAQ,gBAAoBF,GAAKL,EAAa,GAAE,MAG5DI,GAAY,KAMhB,OAHAD,EAAaA,EAAWI,QAAQ,SAAUH,IAClBG,QAAQ,SAAUd,EAAWe,YDhCjCC,CAAuBhB,EAAYJ,GAC7CqB,EDLR,SAAiCjB,GAWnC,IATA,IAOIkB,EAPEC,EAAS5B,EAAgBS,GACzBO,EAAaY,EAAOC,OAEtBC,EAAa7B,EAEbmB,EAAW,GAINC,EAAI,EAAGA,EAAIZ,EAAYY,IAChC,CACI,IAAIC,EALS,0EAKOC,QAAQ,UAAWF,EAAEG,YAEzCG,EAAQN,EAEJA,GAAKL,IAELW,EAAQlB,EAAaY,EAAI,GAK7BD,GAFAE,EAAOA,EAAKC,QAAQ,UAAWK,EAAOD,GAAOH,YAG7CJ,GAAY,KAMhB,OAHAU,EAAaA,EAAWP,QAAQ,SAAUH,IAClBG,QAAQ,SAAUd,EAAWe,YCxBjCO,CAAuBtB,UAEvCG,EAAAR,EAAA4B,KAAApC,KAEIiB,EAEAa,IACF9B,MAEGS,WAAaA,EAElBO,EAAKJ,WAAaA,EAElBI,EAAKqB,SAAW,EAEhBrB,EAAKL,QAAUA,EAEfK,EAAKU,KAAOhB,IA0GpB,OA7IoCZ,EAAMS,EAAAC,GA6C/BD,EAAKL,UAAAoC,MAAZ,SACIC,EAA6BC,EAAsBC,EAAuBC,GA8B1E,GA3BID,EAEIzC,KAAKS,WAELT,KAAK2C,SAASjC,SAAY,EAAI+B,EAAOG,OAAUH,EAAOG,MAAQJ,EAAMI,OAIpE5C,KAAK2C,SAASjC,SAAY,EAAI+B,EAAOI,QAAWJ,EAAOI,OAASL,EAAMK,QAKtE7C,KAAKS,WAELT,KAAK2C,SAASjC,SAAY,EAAI6B,EAAcO,SAASF,OAAUL,EAAcO,SAASF,MAAQJ,EAAMI,OAIpG5C,KAAK2C,SAASjC,SAAY,EAAI6B,EAAcO,SAASD,QAAWN,EAAcO,SAASD,OAASL,EAAMK,QAK9G7C,KAAK2C,SAASjC,UAAYV,KAAKU,SAC/BV,KAAK2C,SAASjC,UAAYV,KAAK+C,OAEX,IAAhB/C,KAAK+C,OAELR,EAAcS,YAAYhD,KAAMwC,EAAOC,EAAQC,OAGnD,CACI,IAAMO,EAAeV,EAAcW,mBAC7BJ,EAAWP,EAAcO,SAE3BK,EAAOX,EACPY,EAAOH,EAEXjD,KAAKqD,MAAMC,OAAQ,EACnBf,EAAcS,YAAYhD,KAAMmD,EAAMC,EAAMG,EAAYC,OAExD,IAAK,IAAI/B,EAAI,EAAGA,EAAIzB,KAAK+C,OAAS,EAAGtB,IACrC,CACIc,EAAckB,aAAaN,EAAMI,EAAYG,MAE7C1D,KAAK2C,SAASgB,SAAWP,EAEzB,IAAMQ,EAAOR,EAEbA,EAAOD,EACPA,EAAOS,EAEPd,EAASe,OAAOC,KAAK9D,MACrB8C,EAASiB,SAASC,KAAK,GAG3BhE,KAAKqD,MAAMC,OAAQ,EACnBf,EAAcS,YAAYhD,KAAMoD,EAAMX,EAAQC,GAC9CH,EAAc0B,oBAAoBhB,KAO1CzD,OAAA0E,eAAI3D,EAAIL,UAAA,OAAA,CAARiE,IAAA,WAEI,OAAOnE,KAAKU,UAGhB0D,IAAA,SAASrC,GAEL/B,KAAKqE,QAAU,EAAuB,EAAlBhD,KAAKiD,IAAIvC,GAC7B/B,KAAKU,SAAWqB,mCAQpBvC,OAAA0E,eAAI3D,EAAOL,UAAA,UAAA,CAAXiE,IAAA,WAEI,OAAOnE,KAAKqC,UAGhB+B,IAAA,SAAYrC,GAER/B,KAAKqC,SAAWN,EAChB/B,KAAK+C,OAAShB,mCAErBxB,EA7ID,CAAoCgE,GEEpCC,EAAA,SAAAhE,GAaI,SAAAgE,EAAY9D,EAAcC,EAAaC,EAAyCC,QAApE,IAAAH,IAAAA,EAAY,QAAE,IAAAC,IAAAA,EAAW,QAAE,IAAAC,IAAAA,EAAaE,EAASC,wBAAmB,IAAAF,IAAAA,EAAc,GAA9F,IAAAG,EAEIR,cAUHR,YARGgB,EAAKyD,YAAc,IAAIlE,GAAe,EAAMG,EAAUC,EAASC,EAAYC,GAC3EG,EAAK0D,YAAc,IAAInE,GAAe,EAAOG,EAAUC,EAASC,EAAYC,GAE5EG,EAAKJ,WAAaA,EAClBI,EAAKL,QAAUA,EACfK,EAAKU,KAAOhB,EAEZM,EAAK2D,kBAAmB,IAqIhC,OA7JgC7E,EAAM0E,EAAAhE,GAkClCgE,EAAKtE,UAAAoC,MAAL,SAAMC,EAA6BC,EAAsBC,EAAuBC,GAE5E,IAAMkC,EAAYvD,KAAKiD,IAAItE,KAAKyE,YAAY/D,UACtCmE,EAAYxD,KAAKiD,IAAItE,KAAK0E,YAAYhE,UAE5C,GAAIkE,GAAaC,EACjB,CACI,IAAM5B,EAAeV,EAAcW,mBAEnClD,KAAKyE,YAAYnC,MAAMC,EAAeC,EAAOS,EAAcM,EAAYC,OACvExD,KAAK0E,YAAYpC,MAAMC,EAAeU,EAAcR,EAAQC,GAE5DH,EAAc0B,oBAAoBhB,QAE7B4B,EAEL7E,KAAK0E,YAAYpC,MAAMC,EAAeC,EAAOC,EAAQC,GAIrD1C,KAAKyE,YAAYnC,MAAMC,EAAeC,EAAOC,EAAQC,IAInD8B,EAAAtE,UAAA4E,cAAV,WAEQ9E,KAAK+E,kBAEL/E,KAAKqE,QAAU,EAIfrE,KAAKqE,QAA+F,EAArFhD,KAAK2D,IAAI3D,KAAKiD,IAAItE,KAAKyE,YAAY/D,UAAWW,KAAKiD,IAAItE,KAAK0E,YAAYhE,YAQ/FlB,OAAA0E,eAAIM,EAAItE,UAAA,OAAA,CAARiE,IAAA,WAEI,OAAOnE,KAAKyE,YAAY/C,MAG5B0C,IAAA,SAASrC,GAEL/B,KAAKyE,YAAY/C,KAAO1B,KAAK0E,YAAYhD,KAAOK,EAChD/B,KAAK8E,iDAOTtF,OAAA0E,eAAIM,EAAOtE,UAAA,UAAA,CAAXiE,IAAA,WAEI,OAAOnE,KAAKyE,YAAY9D,SAG5ByD,IAAA,SAAYrC,GAER/B,KAAKyE,YAAY9D,QAAUX,KAAK0E,YAAY/D,QAAUoB,mCAO1DvC,OAAA0E,eAAIM,EAAKtE,UAAA,QAAA,CAATiE,IAAA,WAEI,OAAOnE,KAAKyE,YAAY/C,MAG5B0C,IAAA,SAAUrC,GAEN/B,KAAKyE,YAAY/C,KAAOK,EACxB/B,KAAK8E,iDAOTtF,OAAA0E,eAAIM,EAAKtE,UAAA,QAAA,CAATiE,IAAA,WAEI,OAAOnE,KAAK0E,YAAYhD,MAG5B0C,IAAA,SAAUrC,GAEN/B,KAAK0E,YAAYhD,KAAOK,EACxB/B,KAAK8E,iDAOTtF,OAAA0E,eAAIM,EAAStE,UAAA,YAAA,CAAbiE,IAAA,WAEI,OAAOnE,KAAK0E,YAAYO,WAG5Bb,IAAA,SAAcrC,GAEV/B,KAAK0E,YAAYO,UAAYlD,mCAOjCvC,OAAA0E,eAAIM,EAAgBtE,UAAA,mBAAA,CAApBiE,IAAA,WAEI,OAAOnE,KAAK+E,mBAGhBX,IAAA,SAAqBrC,GAEjB/B,KAAK+E,kBAAoBhD,EACzB/B,KAAK8E,iDAEZN,EA7JD,CAAgCD"}