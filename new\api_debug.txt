[2025-06-11 09:41:04] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:04] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:04] 🚨 webui实例获取成功
[2025-06-11 09:41:04] 🚨 准备调用会话管理器
[2025-06-11 09:41:04] 🚨 会话管理器结果: {'session_id': 'session_1749606064', 'success': True}
[2025-06-11 09:41:04] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:04] 🚨 disable_vad参数值: False
[2025-06-11 09:41:13] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:13] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:13] 🚨 webui实例获取成功
[2025-06-11 09:41:13] 🚨 准备调用会话管理器
[2025-06-11 09:41:13] 🚨 会话管理器结果: {'session_id': 'session_1749606064', 'success': True}
[2025-06-11 09:41:13] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:13] 🚨 disable_vad参数值: False
[2025-06-11 09:41:13] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:13] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:13] 🚨 webui实例获取成功
[2025-06-11 09:41:13] 🚨 准备调用会话管理器
[2025-06-11 09:41:13] 🚨 会话管理器结果: {'session_id': 'session_1749606064', 'success': True}
[2025-06-11 09:41:13] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:13] 🚨 disable_vad参数值: False
[2025-06-11 09:41:24] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:24] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:24] 🚨 webui实例获取成功
[2025-06-11 09:41:24] 🚨 准备调用会话管理器
[2025-06-11 09:41:24] 🚨 会话管理器结果: {'session_id': 'session_1749606084', 'success': True}
[2025-06-11 09:41:24] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:24] 🚨 disable_vad参数值: False
[2025-06-11 09:41:25] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:25] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:25] 🚨 webui实例获取成功
[2025-06-11 09:41:25] 🚨 准备调用会话管理器
[2025-06-11 09:41:25] 🚨 会话管理器结果: {'session_id': 'session_1749606084', 'success': True}
[2025-06-11 09:41:25] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:25] 🚨 disable_vad参数值: False
[2025-06-11 09:41:25] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:25] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:25] 🚨 webui实例获取成功
[2025-06-11 09:41:25] 🚨 准备调用会话管理器
[2025-06-11 09:41:25] 🚨 会话管理器结果: {'session_id': 'session_1749606084', 'success': True}
[2025-06-11 09:41:25] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:25] 🚨 disable_vad参数值: False
[2025-06-11 09:41:35] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:35] 🚨 webui实例获取成功
[2025-06-11 09:41:35] 🚨 准备调用会话管理器
[2025-06-11 09:41:35] 🚨 会话管理器结果: {'session_id': 'session_1749606095', 'success': True}
[2025-06-11 09:41:35] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:35] 🚨 disable_vad参数值: False
[2025-06-11 09:41:36] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:36] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:36] 🚨 webui实例获取成功
[2025-06-11 09:41:36] 🚨 准备调用会话管理器
[2025-06-11 09:41:36] 🚨 会话管理器结果: {'session_id': 'session_1749606095', 'success': True}
[2025-06-11 09:41:36] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:36] 🚨 disable_vad参数值: False
[2025-06-11 09:41:36] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:36] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:36] 🚨 webui实例获取成功
[2025-06-11 09:41:36] 🚨 准备调用会话管理器
[2025-06-11 09:41:36] 🚨 会话管理器结果: {'session_id': 'session_1749606095', 'success': True}
[2025-06-11 09:41:36] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:36] 🚨 disable_vad参数值: False
[2025-06-11 09:41:59] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:41:59] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:41:59] 🚨 webui实例获取成功
[2025-06-11 09:41:59] 🚨 准备调用会话管理器
[2025-06-11 09:41:59] 🚨 会话管理器结果: {'session_id': 'session_1749606119', 'success': True}
[2025-06-11 09:41:59] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:41:59] 🚨 disable_vad参数值: False
[2025-06-11 09:42:01] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:42:01] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:42:01] 🚨 webui实例获取成功
[2025-06-11 09:42:01] 🚨 准备调用会话管理器
[2025-06-11 09:42:01] 🚨 会话管理器结果: {'session_id': 'session_1749606119', 'success': True}
[2025-06-11 09:42:01] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:42:01] 🚨 disable_vad参数值: False
[2025-06-11 09:42:01] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:42:01] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:42:01] 🚨 webui实例获取成功
[2025-06-11 09:42:01] 🚨 准备调用会话管理器
[2025-06-11 09:42:01] 🚨 会话管理器结果: {'session_id': 'session_1749606119', 'success': True}
[2025-06-11 09:42:01] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:42:01] 🚨 disable_vad参数值: False
[2025-06-11 09:42:13] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:42:13] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:42:13] 🚨 webui实例获取成功
[2025-06-11 09:42:13] 🚨 准备调用会话管理器
[2025-06-11 09:42:13] 🚨 会话管理器结果: {'session_id': 'session_1749606133', 'success': True}
[2025-06-11 09:42:13] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:42:13] 🚨 disable_vad参数值: False
[2025-06-11 09:42:15] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:42:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:42:15] 🚨 webui实例获取成功
[2025-06-11 09:42:15] 🚨 准备调用会话管理器
[2025-06-11 09:42:15] 🚨 会话管理器结果: {'session_id': 'session_1749606133', 'success': True}
[2025-06-11 09:42:15] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:42:15] 🚨 disable_vad参数值: False
[2025-06-11 09:42:15] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:42:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:42:15] 🚨 webui实例获取成功
[2025-06-11 09:42:15] 🚨 准备调用会话管理器
[2025-06-11 09:42:15] 🚨 会话管理器结果: {'session_id': 'session_1749606133', 'success': True}
[2025-06-11 09:42:15] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:42:15] 🚨 disable_vad参数值: False
[2025-06-11 09:43:36] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:43:36] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 09:43:36] 🚨 webui实例获取成功
[2025-06-11 09:43:36] 🚨 准备调用会话管理器
[2025-06-11 09:43:36] 🚨 会话管理器结果: {'session_id': 'session_1749606216', 'success': True}
[2025-06-11 09:43:36] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:43:36] 🚨 disable_vad参数值: True
[2025-06-11 09:44:35] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:44:35] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 09:44:35] 🚨 webui实例获取成功
[2025-06-11 09:44:35] 🚨 准备调用会话管理器
[2025-06-11 09:44:35] 🚨 会话管理器结果: {'session_id': 'session_1749606275', 'success': True}
[2025-06-11 09:44:35] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:44:35] 🚨 disable_vad参数值: True
[2025-06-11 09:49:16] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:49:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:49:16] 🚨 webui实例获取成功
[2025-06-11 09:49:16] 🚨 准备调用会话管理器
[2025-06-11 09:49:16] 🚨 会话管理器结果: {'session_id': 'session_1749606556', 'success': True}
[2025-06-11 09:49:16] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:49:16] 🚨 disable_vad参数值: False
[2025-06-11 09:49:33] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:49:33] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 09:49:33] 🚨 webui实例获取成功
[2025-06-11 09:49:33] 🚨 准备调用会话管理器
[2025-06-11 09:49:33] 🚨 会话管理器结果: {'session_id': 'session_1749606573', 'success': True}
[2025-06-11 09:49:33] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:49:33] 🚨 disable_vad参数值: True
[2025-06-11 09:49:45] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:49:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:49:45] 🚨 webui实例获取成功
[2025-06-11 09:49:45] 🚨 准备调用会话管理器
[2025-06-11 09:49:45] 🚨 会话管理器结果: {'session_id': 'session_1749606585', 'success': True}
[2025-06-11 09:49:45] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:49:45] 🚨 disable_vad参数值: False
[2025-06-11 09:50:04] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:50:04] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:50:04] 🚨 webui实例获取成功
[2025-06-11 09:50:04] 🚨 准备调用会话管理器
[2025-06-11 09:50:04] 🚨 会话管理器结果: {'session_id': 'session_1749606604', 'success': True}
[2025-06-11 09:50:04] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:50:04] 🚨 disable_vad参数值: False
[2025-06-11 09:54:24] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:54:24] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:54:24] 🚨 webui实例获取成功
[2025-06-11 09:54:24] 🚨 准备调用会话管理器
[2025-06-11 09:54:24] 🚨 会话管理器结果: {'session_id': 'session_1749606864', 'success': True}
[2025-06-11 09:54:24] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:54:24] 🚨 disable_vad参数值: False
[2025-06-11 09:54:24] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 09:54:24] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 09:54:24] 🚨 进入try语句块
[2025-06-11 09:54:47] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:54:47] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:54:47] 🚨 webui实例获取成功
[2025-06-11 09:54:47] 🚨 准备调用会话管理器
[2025-06-11 09:54:47] 🚨 会话管理器结果: {'session_id': 'session_1749606887', 'success': True}
[2025-06-11 09:54:47] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:54:47] 🚨 disable_vad参数值: False
[2025-06-11 09:54:47] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 09:54:47] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 09:54:47] 🚨 进入try语句块
[2025-06-11 09:59:39] 🚨 start_realtime_dialogue API被调用
[2025-06-11 09:59:39] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 09:59:39] 🚨 webui实例获取成功
[2025-06-11 09:59:39] 🚨 准备调用会话管理器
[2025-06-11 09:59:39] 🚨 会话管理器结果: {'session_id': 'session_1749607179', 'success': True}
[2025-06-11 09:59:39] 🚨 准备调用 start_realtime_conversation
[2025-06-11 09:59:39] 🚨 disable_vad参数值: False
[2025-06-11 09:59:39] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 09:59:39] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 09:59:39] 🚨 进入try语句块
[2025-06-11 09:59:39] 🚨 开始清理队列
[2025-06-11 09:59:39] 🚨 队列清理完成
[2025-06-11 09:59:39] 🚨 开始重置VAD暂停状态
[2025-06-11 09:59:39] 🚨 VAD暂停状态重置完成
[2025-06-11 10:02:16] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:02:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:02:16] 🚨 webui实例获取成功
[2025-06-11 10:02:16] 🚨 准备调用会话管理器
[2025-06-11 10:02:16] 🚨 会话管理器结果: {'session_id': 'session_1749607336', 'success': True}
[2025-06-11 10:02:16] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:02:16] 🚨 disable_vad参数值: False
[2025-06-11 10:02:16] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:02:16] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:02:16] 🚨 进入try语句块
[2025-06-11 10:02:16] 🚨 开始清理队列
[2025-06-11 10:02:16] 🚨 队列清理完成
[2025-06-11 10:02:16] 🚨 开始重置VAD暂停状态
[2025-06-11 10:02:16] 🚨 VAD暂停状态重置完成
[2025-06-11 10:02:50] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:02:50] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:02:50] 🚨 webui实例获取成功
[2025-06-11 10:02:50] 🚨 准备调用会话管理器
[2025-06-11 10:02:50] 🚨 会话管理器结果: {'session_id': 'session_1749607370', 'success': True}
[2025-06-11 10:02:50] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:02:50] 🚨 disable_vad参数值: False
[2025-06-11 10:02:50] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:02:50] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:02:50] 🚨 进入try语句块
[2025-06-11 10:02:50] 🚨 开始清理队列
[2025-06-11 10:02:50] 🚨 队列清理完成
[2025-06-11 10:02:50] 🚨 开始重置VAD暂停状态
[2025-06-11 10:02:50] 🚨 VAD暂停状态重置完成
[2025-06-11 10:02:50] 🚨 准备重置强制更新标记
[2025-06-11 10:02:50] 🚨 强制更新标记重置完成
[2025-06-11 10:05:08] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:05:08] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:05:08] 🚨 webui实例获取成功
[2025-06-11 10:05:08] 🚨 准备调用会话管理器
[2025-06-11 10:05:08] 🚨 会话管理器结果: {'session_id': 'session_1749607508', 'success': True}
[2025-06-11 10:05:08] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:05:08] 🚨 disable_vad参数值: False
[2025-06-11 10:05:08] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:05:08] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:05:08] 🚨 进入try语句块
[2025-06-11 10:05:08] 🚨 开始清理队列
[2025-06-11 10:05:08] 🚨 队列清理完成
[2025-06-11 10:05:08] 🚨 开始重置VAD暂停状态
[2025-06-11 10:05:08] 🚨 VAD暂停状态重置完成
[2025-06-11 10:05:08] 🚨 准备重置强制更新标记
[2025-06-11 10:05:08] 🚨 强制更新标记重置完成
[2025-06-11 10:05:08] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:05:08] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:05:08] 🚨 final_llm_params创建完成
[2025-06-11 10:05:08] 🚨 AI配置处理完成
[2025-06-11 10:05:16] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:05:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:05:16] 🚨 webui实例获取成功
[2025-06-11 10:05:16] 🚨 准备调用会话管理器
[2025-06-11 10:05:16] 🚨 会话管理器结果: {'session_id': 'session_1749607508', 'success': True}
[2025-06-11 10:05:16] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:05:16] 🚨 disable_vad参数值: False
[2025-06-11 10:05:16] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:05:16] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:05:16] ⚠️ 实时对话已在运行
[2025-06-11 10:05:16] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:05:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:05:16] 🚨 webui实例获取成功
[2025-06-11 10:05:16] 🚨 准备调用会话管理器
[2025-06-11 10:05:16] 🚨 会话管理器结果: {'session_id': 'session_1749607508', 'success': True}
[2025-06-11 10:05:16] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:05:16] 🚨 disable_vad参数值: False
[2025-06-11 10:05:16] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:05:16] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:05:16] ⚠️ 实时对话已在运行
[2025-06-11 10:05:17] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:05:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:05:17] 🚨 webui实例获取成功
[2025-06-11 10:05:17] 🚨 准备调用会话管理器
[2025-06-11 10:05:17] 🚨 会话管理器结果: {'session_id': 'session_1749607517', 'success': True}
[2025-06-11 10:05:17] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:05:17] 🚨 disable_vad参数值: False
[2025-06-11 10:05:17] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:05:17] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:05:17] 🚨 进入try语句块
[2025-06-11 10:05:17] 🚨 开始清理队列
[2025-06-11 10:05:17] 🚨 队列清理完成
[2025-06-11 10:05:17] 🚨 开始重置VAD暂停状态
[2025-06-11 10:05:17] 🚨 VAD暂停状态重置完成
[2025-06-11 10:05:17] 🚨 准备重置强制更新标记
[2025-06-11 10:05:17] 🚨 强制更新标记重置完成
[2025-06-11 10:05:17] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:05:17] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:05:17] 🚨 final_llm_params创建完成
[2025-06-11 10:05:17] 🚨 AI配置处理完成
[2025-06-11 10:05:20] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:05:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:05:20] 🚨 webui实例获取成功
[2025-06-11 10:05:20] 🚨 准备调用会话管理器
[2025-06-11 10:05:20] 🚨 会话管理器结果: {'session_id': 'session_1749607520', 'success': True}
[2025-06-11 10:05:20] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:05:20] 🚨 disable_vad参数值: False
[2025-06-11 10:05:20] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:05:20] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:05:20] 🚨 进入try语句块
[2025-06-11 10:05:20] 🚨 开始清理队列
[2025-06-11 10:05:20] 🚨 队列清理完成
[2025-06-11 10:05:20] 🚨 开始重置VAD暂停状态
[2025-06-11 10:05:20] 🚨 VAD暂停状态重置完成
[2025-06-11 10:05:20] 🚨 准备重置强制更新标记
[2025-06-11 10:05:20] 🚨 强制更新标记重置完成
[2025-06-11 10:05:20] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:05:20] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:05:20] 🚨 final_llm_params创建完成
[2025-06-11 10:05:20] 🚨 AI配置处理完成
[2025-06-11 10:06:42] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:06:42] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:06:42] 🚨 webui实例获取成功
[2025-06-11 10:06:42] 🚨 准备调用会话管理器
[2025-06-11 10:06:42] 🚨 会话管理器结果: {'session_id': 'session_1749607602', 'success': True}
[2025-06-11 10:06:42] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:06:42] 🚨 disable_vad参数值: False
[2025-06-11 10:06:42] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:06:42] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:06:42] 🚨 进入try语句块
[2025-06-11 10:06:42] 🚨 开始清理队列
[2025-06-11 10:06:42] 🚨 队列清理完成
[2025-06-11 10:06:42] 🚨 开始重置VAD暂停状态
[2025-06-11 10:06:42] 🚨 VAD暂停状态重置完成
[2025-06-11 10:06:42] 🚨 准备重置强制更新标记
[2025-06-11 10:06:42] 🚨 强制更新标记重置完成
[2025-06-11 10:06:42] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:06:42] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:06:42] 🚨 final_llm_params创建完成
[2025-06-11 10:06:42] 🚨 AI配置处理完成
[2025-06-11 10:08:17] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:08:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:08:17] 🚨 webui实例获取成功
[2025-06-11 10:08:17] 🚨 准备调用会话管理器
[2025-06-11 10:08:17] 🚨 会话管理器结果: {'session_id': 'session_1749607697', 'success': True}
[2025-06-11 10:08:17] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:08:17] 🚨 disable_vad参数值: False
[2025-06-11 10:08:17] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:08:17] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:08:17] 🚨 进入try语句块
[2025-06-11 10:08:17] 🚨 开始清理队列
[2025-06-11 10:08:17] 🚨 队列清理完成
[2025-06-11 10:08:17] 🚨 开始重置VAD暂停状态
[2025-06-11 10:08:17] 🚨 VAD暂停状态重置完成
[2025-06-11 10:08:17] 🚨 准备重置强制更新标记
[2025-06-11 10:08:17] 🚨 强制更新标记重置完成
[2025-06-11 10:08:17] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:08:17] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:08:17] 🚨 final_llm_params创建完成
[2025-06-11 10:08:17] 🚨 AI配置处理完成
[2025-06-11 10:08:17] 🚨 开始保存realtime_config
[2025-06-11 10:08:17] 🚨 realtime_config保存完成
[2025-06-11 10:08:27] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:08:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:08:27] 🚨 webui实例获取成功
[2025-06-11 10:08:27] 🚨 准备调用会话管理器
[2025-06-11 10:08:27] 🚨 会话管理器结果: {'session_id': 'session_1749607707', 'success': True}
[2025-06-11 10:08:27] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:08:27] 🚨 disable_vad参数值: False
[2025-06-11 10:08:27] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:08:27] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:08:27] 🚨 进入try语句块
[2025-06-11 10:08:27] 🚨 开始清理队列
[2025-06-11 10:08:27] 🚨 队列清理完成
[2025-06-11 10:08:27] 🚨 开始重置VAD暂停状态
[2025-06-11 10:08:27] 🚨 VAD暂停状态重置完成
[2025-06-11 10:08:27] 🚨 准备重置强制更新标记
[2025-06-11 10:08:27] 🚨 强制更新标记重置完成
[2025-06-11 10:08:27] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:08:27] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:08:27] 🚨 final_llm_params创建完成
[2025-06-11 10:08:27] 🚨 AI配置处理完成
[2025-06-11 10:08:27] 🚨 开始保存realtime_config
[2025-06-11 10:08:27] 🚨 realtime_config保存完成
[2025-06-11 10:11:23] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:11:23] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:11:23] 🚨 webui实例获取成功
[2025-06-11 10:11:23] 🚨 准备调用会话管理器
[2025-06-11 10:11:23] 🚨 会话管理器结果: {'session_id': 'session_1749607883', 'success': True}
[2025-06-11 10:11:23] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:11:23] 🚨 disable_vad参数值: False
[2025-06-11 10:11:23] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:11:23] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:11:23] 🚨 进入try语句块
[2025-06-11 10:11:23] 🚨 开始清理队列
[2025-06-11 10:11:23] 🚨 队列清理完成
[2025-06-11 10:11:23] 🚨 开始重置VAD暂停状态
[2025-06-11 10:11:23] 🚨 VAD暂停状态重置完成
[2025-06-11 10:11:23] 🚨 准备重置强制更新标记
[2025-06-11 10:11:23] 🚨 强制更新标记重置完成
[2025-06-11 10:11:23] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:11:23] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:11:23] 🚨 final_llm_params创建完成
[2025-06-11 10:11:23] 🚨 AI配置处理完成
[2025-06-11 10:11:23] 🚨 开始保存realtime_config
[2025-06-11 10:11:23] 🚨 realtime_config保存完成
[2025-06-11 10:11:23] 🚨 开始TTS配置检查
[2025-06-11 10:11:33] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:11:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:11:33] 🚨 webui实例获取成功
[2025-06-11 10:11:33] 🚨 准备调用会话管理器
[2025-06-11 10:11:33] 🚨 会话管理器结果: {'session_id': 'session_1749607893', 'success': True}
[2025-06-11 10:11:33] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:11:33] 🚨 disable_vad参数值: False
[2025-06-11 10:11:33] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:11:33] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:11:33] 🚨 进入try语句块
[2025-06-11 10:11:33] 🚨 开始清理队列
[2025-06-11 10:11:33] 🚨 队列清理完成
[2025-06-11 10:11:33] 🚨 开始重置VAD暂停状态
[2025-06-11 10:11:33] 🚨 VAD暂停状态重置完成
[2025-06-11 10:11:33] 🚨 准备重置强制更新标记
[2025-06-11 10:11:33] 🚨 强制更新标记重置完成
[2025-06-11 10:11:33] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:11:33] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:11:33] 🚨 final_llm_params创建完成
[2025-06-11 10:11:33] 🚨 AI配置处理完成
[2025-06-11 10:11:33] 🚨 开始保存realtime_config
[2025-06-11 10:11:33] 🚨 realtime_config保存完成
[2025-06-11 10:11:33] 🚨 开始TTS配置检查
[2025-06-11 10:11:34] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:11:34] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:11:34] 🚨 webui实例获取成功
[2025-06-11 10:11:34] 🚨 准备调用会话管理器
[2025-06-11 10:11:34] 🚨 会话管理器结果: {'session_id': 'session_1749607893', 'success': True}
[2025-06-11 10:11:34] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:11:34] 🚨 disable_vad参数值: False
[2025-06-11 10:11:34] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:11:34] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:11:34] ⚠️ 实时对话已在运行
[2025-06-11 10:11:34] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:11:34] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:11:34] 🚨 webui实例获取成功
[2025-06-11 10:11:34] 🚨 准备调用会话管理器
[2025-06-11 10:11:34] 🚨 会话管理器结果: {'session_id': 'session_1749607893', 'success': True}
[2025-06-11 10:11:34] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:11:34] 🚨 disable_vad参数值: False
[2025-06-11 10:11:34] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:11:34] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:11:34] ⚠️ 实时对话已在运行
[2025-06-11 10:11:34] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:11:34] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:11:34] 🚨 webui实例获取成功
[2025-06-11 10:11:34] 🚨 准备调用会话管理器
[2025-06-11 10:11:34] 🚨 会话管理器结果: {'session_id': 'session_1749607893', 'success': True}
[2025-06-11 10:11:34] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:11:34] 🚨 disable_vad参数值: False
[2025-06-11 10:11:34] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:11:34] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:11:34] ⚠️ 实时对话已在运行
[2025-06-11 10:11:46] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:11:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:11:46] 🚨 webui实例获取成功
[2025-06-11 10:11:46] 🚨 准备调用会话管理器
[2025-06-11 10:11:46] 🚨 会话管理器结果: {'session_id': 'session_1749607906', 'success': True}
[2025-06-11 10:11:46] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:11:46] 🚨 disable_vad参数值: False
[2025-06-11 10:11:46] 🚨 进入 start_realtime_conversation 方法
[2025-06-11 10:11:46] 🚨 参数: disable_vad=False, mode=user-voice
[2025-06-11 10:11:46] 🚨 进入try语句块
[2025-06-11 10:11:46] 🚨 开始清理队列
[2025-06-11 10:11:46] 🚨 队列清理完成
[2025-06-11 10:11:46] 🚨 开始重置VAD暂停状态
[2025-06-11 10:11:46] 🚨 VAD暂停状态重置完成
[2025-06-11 10:11:46] 🚨 准备重置强制更新标记
[2025-06-11 10:11:46] 🚨 强制更新标记重置完成
[2025-06-11 10:11:46] 🚨 开始处理AI配置，llm_params type: <class 'dict'>
[2025-06-11 10:11:46] 🚨 ai_config type: <class 'dict'>
[2025-06-11 10:11:46] 🚨 final_llm_params创建完成
[2025-06-11 10:11:46] 🚨 AI配置处理完成
[2025-06-11 10:11:46] 🚨 开始保存realtime_config
[2025-06-11 10:11:46] 🚨 realtime_config保存完成
[2025-06-11 10:11:46] 🚨 开始TTS配置检查
[2025-06-11 10:16:09] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:16:09] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:16:09] 🚨 webui实例获取成功
[2025-06-11 10:16:09] 🚨 准备调用会话管理器
[2025-06-11 10:16:09] 🚨 会话管理器结果: {'session_id': 'session_1749608169', 'success': True}
[2025-06-11 10:16:09] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:16:09] 🚨 disable_vad参数值: False
[2025-06-11 10:18:03] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:18:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:18:03] 🚨 webui实例获取成功
[2025-06-11 10:18:03] 🚨 准备调用会话管理器
[2025-06-11 10:18:03] 🚨 会话管理器结果: {'session_id': 'session_1749608283', 'success': True}
[2025-06-11 10:18:03] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:18:03] 🚨 disable_vad参数值: False
[2025-06-11 10:24:47] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:24:47] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 10:24:47] 🚨 webui实例获取成功
[2025-06-11 10:24:47] 🚨 准备调用会话管理器
[2025-06-11 10:24:47] 🚨 会话管理器结果: {'session_id': 'session_1749608687', 'success': True}
[2025-06-11 10:24:47] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:24:47] 🚨 disable_vad参数值: True
[2025-06-11 10:27:15] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:27:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:27:15] 🚨 webui实例获取成功
[2025-06-11 10:27:15] 🚨 准备调用会话管理器
[2025-06-11 10:27:15] 🚨 会话管理器结果: {'session_id': 'session_1749608835', 'success': True}
[2025-06-11 10:27:15] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:27:15] 🚨 disable_vad参数值: False
[2025-06-11 10:33:39] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:33:39] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:33:39] 🚨 webui实例获取成功
[2025-06-11 10:33:39] 🚨 准备调用会话管理器
[2025-06-11 10:33:39] 🚨 会话管理器结果: {'session_id': 'session_1749609219', 'success': True}
[2025-06-11 10:33:39] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:33:39] 🚨 disable_vad参数值: False
[2025-06-11 10:40:21] 🚨 start_realtime_dialogue API被调用
[2025-06-11 10:40:21] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 10:40:21] 🚨 webui实例获取成功
[2025-06-11 10:40:21] 🚨 准备调用会话管理器
[2025-06-11 10:40:21] 🚨 会话管理器结果: {'session_id': 'session_1749609621', 'success': True}
[2025-06-11 10:40:21] 🚨 准备调用 start_realtime_conversation
[2025-06-11 10:40:21] 🚨 disable_vad参数值: False
[2025-06-11 11:16:38] 🚨 start_realtime_dialogue API被调用
[2025-06-11 11:16:38] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 11:16:38] 🚨 webui实例获取成功
[2025-06-11 11:16:38] 🚨 准备调用会话管理器
[2025-06-11 11:16:38] 🚨 会话管理器结果: {'session_id': 'session_1749611798', 'success': True}
[2025-06-11 11:16:38] 🚨 准备调用 start_realtime_conversation
[2025-06-11 11:16:38] 🚨 disable_vad参数值: False
[2025-06-11 11:17:24] 🚨 start_realtime_dialogue API被调用
[2025-06-11 11:17:24] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 11:17:24] 🚨 webui实例获取成功
[2025-06-11 11:17:24] 🚨 准备调用会话管理器
[2025-06-11 11:17:24] 🚨 会话管理器结果: {'session_id': 'session_1749611844', 'success': True}
[2025-06-11 11:17:24] 🚨 准备调用 start_realtime_conversation
[2025-06-11 11:17:24] 🚨 disable_vad参数值: False
[2025-06-11 11:47:15] 🚨 start_realtime_dialogue API被调用
[2025-06-11 11:47:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 11:47:15] 🚨 webui实例获取成功
[2025-06-11 11:47:15] 🚨 准备调用会话管理器
[2025-06-11 11:47:15] 🚨 会话管理器结果: {'session_id': 'session_1749613635', 'success': True}
[2025-06-11 11:47:15] 🚨 准备调用 start_realtime_conversation
[2025-06-11 11:47:15] 🚨 disable_vad参数值: False
[2025-06-11 11:59:14] 🚨 start_realtime_dialogue API被调用
[2025-06-11 11:59:14] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 11:59:14] 🚨 webui实例获取成功
[2025-06-11 11:59:14] 🚨 准备调用会话管理器
[2025-06-11 11:59:14] 🚨 会话管理器结果: {'session_id': 'session_1749614354', 'success': True}
[2025-06-11 11:59:14] 🚨 准备调用 start_realtime_conversation
[2025-06-11 11:59:14] 🚨 disable_vad参数值: False
[2025-06-11 12:03:38] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:03:38] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:03:38] 🚨 webui实例获取成功
[2025-06-11 12:03:38] 🚨 准备调用会话管理器
[2025-06-11 12:03:38] 🚨 会话管理器结果: {'session_id': 'session_1749614618', 'success': True}
[2025-06-11 12:03:38] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:03:38] 🚨 disable_vad参数值: False
[2025-06-11 12:04:16] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:04:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:04:16] 🚨 webui实例获取成功
[2025-06-11 12:04:16] 🚨 准备调用会话管理器
[2025-06-11 12:04:16] 🚨 会话管理器结果: {'session_id': 'session_1749614656', 'success': True}
[2025-06-11 12:04:16] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:04:16] 🚨 disable_vad参数值: False
[2025-06-11 12:04:43] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:04:43] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 12:04:43] 🚨 webui实例获取成功
[2025-06-11 12:04:43] 🚨 准备调用会话管理器
[2025-06-11 12:04:43] 🚨 会话管理器结果: {'session_id': 'session_1749614683', 'success': True}
[2025-06-11 12:04:43] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:04:43] 🚨 disable_vad参数值: True
[2025-06-11 12:05:27] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:05:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:05:27] 🚨 webui实例获取成功
[2025-06-11 12:05:27] 🚨 准备调用会话管理器
[2025-06-11 12:05:27] 🚨 会话管理器结果: {'session_id': 'session_1749614727', 'success': True}
[2025-06-11 12:05:27] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:05:27] 🚨 disable_vad参数值: False
[2025-06-11 12:13:35] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:13:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:13:35] 🚨 webui实例获取成功
[2025-06-11 12:13:35] 🚨 准备调用会话管理器
[2025-06-11 12:13:35] 🚨 会话管理器结果: {'session_id': 'session_1749615215', 'success': True}
[2025-06-11 12:13:35] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:13:35] 🚨 disable_vad参数值: False
[2025-06-11 12:21:08] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:21:08] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:21:08] 🚨 webui实例获取成功
[2025-06-11 12:21:08] 🚨 准备调用会话管理器
[2025-06-11 12:21:08] 🚨 会话管理器结果: {'session_id': 'session_1749615668', 'success': True}
[2025-06-11 12:21:08] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:21:08] 🚨 disable_vad参数值: False
[2025-06-11 12:25:45] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:25:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:25:45] 🚨 webui实例获取成功
[2025-06-11 12:25:45] 🚨 准备调用会话管理器
[2025-06-11 12:25:45] 🚨 会话管理器结果: {'session_id': 'session_1749615945', 'success': True}
[2025-06-11 12:25:45] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:25:45] 🚨 disable_vad参数值: False
[2025-06-11 12:29:12] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:29:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:29:12] 🚨 webui实例获取成功
[2025-06-11 12:29:12] 🚨 准备调用会话管理器
[2025-06-11 12:29:12] 🚨 会话管理器结果: {'session_id': 'session_1749616153', 'success': True}
[2025-06-11 12:29:12] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:29:12] 🚨 disable_vad参数值: False
[2025-06-11 12:32:46] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:32:46] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 12:32:46] 🚨 webui实例获取成功
[2025-06-11 12:32:46] 🚨 准备调用会话管理器
[2025-06-11 12:32:46] 🚨 会话管理器结果: {'session_id': 'session_1749616366', 'success': True}
[2025-06-11 12:32:46] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:32:46] 🚨 disable_vad参数值: True
[2025-06-11 12:34:22] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:34:22] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:34:22] 🚨 webui实例获取成功
[2025-06-11 12:34:22] 🚨 准备调用会话管理器
[2025-06-11 12:34:22] 🚨 会话管理器结果: {'session_id': 'session_1749616462', 'success': True}
[2025-06-11 12:34:22] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:34:22] 🚨 disable_vad参数值: False
[2025-06-11 12:41:43] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:41:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:41:43] 🚨 webui实例获取成功
[2025-06-11 12:41:43] 🚨 准备调用会话管理器
[2025-06-11 12:41:43] 🚨 会话管理器结果: {'session_id': 'session_1749616903', 'success': True}
[2025-06-11 12:41:43] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:41:43] 🚨 disable_vad参数值: False
[2025-06-11 12:48:14] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:48:14] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:48:14] 🚨 webui实例获取成功
[2025-06-11 12:48:14] 🚨 准备调用会话管理器
[2025-06-11 12:48:14] 🚨 会话管理器结果: {'session_id': 'session_1749617294', 'success': True}
[2025-06-11 12:48:14] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:48:14] 🚨 disable_vad参数值: False
[2025-06-11 12:52:17] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:52:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:52:17] 🚨 webui实例获取成功
[2025-06-11 12:52:17] 🚨 准备调用会话管理器
[2025-06-11 12:52:17] 🚨 会话管理器结果: {'session_id': 'session_1749617537', 'success': True}
[2025-06-11 12:52:17] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:52:17] 🚨 disable_vad参数值: False
[2025-06-11 12:57:18] 🚨 start_realtime_dialogue API被调用
[2025-06-11 12:57:18] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 12:57:18] 🚨 webui实例获取成功
[2025-06-11 12:57:18] 🚨 准备调用会话管理器
[2025-06-11 12:57:18] 🚨 会话管理器结果: {'session_id': 'session_1749617838', 'success': True}
[2025-06-11 12:57:18] 🚨 准备调用 start_realtime_conversation
[2025-06-11 12:57:18] 🚨 disable_vad参数值: False
[2025-06-11 13:13:47] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:13:47] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:13:47] 🚨 webui实例获取成功
[2025-06-11 13:13:47] 🚨 准备调用会话管理器
[2025-06-11 13:13:47] 🚨 会话管理器结果: {'session_id': 'session_1749618827', 'success': True}
[2025-06-11 13:13:47] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:13:47] 🚨 disable_vad参数值: False
[2025-06-11 13:14:15] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:14:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:14:15] 🚨 webui实例获取成功
[2025-06-11 13:14:15] 🚨 准备调用会话管理器
[2025-06-11 13:14:15] 🚨 会话管理器结果: {'session_id': 'session_1749618855', 'success': True}
[2025-06-11 13:14:15] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:14:15] 🚨 disable_vad参数值: False
[2025-06-11 13:14:24] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:14:24] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:14:24] 🚨 webui实例获取成功
[2025-06-11 13:14:24] 🚨 准备调用会话管理器
[2025-06-11 13:14:24] 🚨 会话管理器结果: {'session_id': 'session_1749618864', 'success': True}
[2025-06-11 13:14:24] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:14:24] 🚨 disable_vad参数值: False
[2025-06-11 13:18:11] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:18:11] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:18:11] 🚨 webui实例获取成功
[2025-06-11 13:18:11] 🚨 准备调用会话管理器
[2025-06-11 13:18:11] 🚨 会话管理器结果: {'session_id': 'session_1749619091', 'success': True}
[2025-06-11 13:18:11] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:18:11] 🚨 disable_vad参数值: False
[2025-06-11 13:24:00] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:24:00] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:24:00] 🚨 webui实例获取成功
[2025-06-11 13:24:00] 🚨 准备调用会话管理器
[2025-06-11 13:24:00] 🚨 会话管理器结果: {'session_id': 'session_1749619440', 'success': True}
[2025-06-11 13:24:00] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:24:00] 🚨 disable_vad参数值: False
[2025-06-11 13:29:32] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:29:32] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:29:32] 🚨 webui实例获取成功
[2025-06-11 13:29:32] 🚨 准备调用会话管理器
[2025-06-11 13:29:32] 🚨 会话管理器结果: {'session_id': 'session_1749619772', 'success': True}
[2025-06-11 13:29:32] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:29:32] 🚨 disable_vad参数值: False
[2025-06-11 13:43:10] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:43:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:43:10] 🚨 webui实例获取成功
[2025-06-11 13:43:10] 🚨 准备调用会话管理器
[2025-06-11 13:43:10] 🚨 会话管理器结果: {'session_id': 'session_1749620590', 'success': True}
[2025-06-11 13:43:10] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:43:10] 🚨 disable_vad参数值: False
[2025-06-11 13:47:33] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:47:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:47:33] 🚨 webui实例获取成功
[2025-06-11 13:47:33] 🚨 准备调用会话管理器
[2025-06-11 13:47:33] 🚨 会话管理器结果: {'session_id': 'session_1749620853', 'success': True}
[2025-06-11 13:47:33] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:47:33] 🚨 disable_vad参数值: False
[2025-06-11 13:57:54] 🚨 start_realtime_dialogue API被调用
[2025-06-11 13:57:54] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 13:57:54] 🚨 webui实例获取成功
[2025-06-11 13:57:54] 🚨 准备调用会话管理器
[2025-06-11 13:57:54] 🚨 会话管理器结果: {'session_id': 'session_1749621474', 'success': True}
[2025-06-11 13:57:54] 🚨 准备调用 start_realtime_conversation
[2025-06-11 13:57:54] 🚨 disable_vad参数值: False
[2025-06-11 14:05:09] 🚨 start_realtime_dialogue API被调用
[2025-06-11 14:05:09] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 14:05:09] 🚨 webui实例获取成功
[2025-06-11 14:05:09] 🚨 准备调用会话管理器
[2025-06-11 14:05:09] 🚨 会话管理器结果: {'session_id': 'session_1749621909', 'success': True}
[2025-06-11 14:05:09] 🚨 准备调用 start_realtime_conversation
[2025-06-11 14:05:09] 🚨 disable_vad参数值: False
[2025-06-11 14:06:55] 🚨 start_realtime_dialogue API被调用
[2025-06-11 14:06:55] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 14:06:55] 🚨 webui实例获取成功
[2025-06-11 14:06:55] 🚨 准备调用会话管理器
[2025-06-11 14:06:55] 🚨 会话管理器结果: {'session_id': 'session_1749622015', 'success': True}
[2025-06-11 14:06:55] 🚨 准备调用 start_realtime_conversation
[2025-06-11 14:06:55] 🚨 disable_vad参数值: False
[2025-06-11 22:13:19] 🚨 start_realtime_dialogue API被调用
[2025-06-11 22:13:19] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 22:13:19] 🚨 webui实例获取成功
[2025-06-11 22:13:19] 🚨 准备调用会话管理器
[2025-06-11 22:13:19] 🚨 会话管理器结果: {'session_id': 'session_1749651199', 'success': True}
[2025-06-11 22:13:19] 🚨 准备调用 start_realtime_conversation
[2025-06-11 22:13:19] 🚨 disable_vad参数值: True
[2025-06-11 22:16:20] 🚨 start_realtime_dialogue API被调用
[2025-06-11 22:16:20] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 22:16:20] 🚨 webui实例获取成功
[2025-06-11 22:16:20] 🚨 准备调用会话管理器
[2025-06-11 22:16:20] 🚨 会话管理器结果: {'session_id': 'session_1749651380', 'success': True}
[2025-06-11 22:16:20] 🚨 准备调用 start_realtime_conversation
[2025-06-11 22:16:20] 🚨 disable_vad参数值: True
[2025-06-11 22:17:16] 🚨 start_realtime_dialogue API被调用
[2025-06-11 22:17:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-11 22:17:16] 🚨 webui实例获取成功
[2025-06-11 22:17:16] 🚨 准备调用会话管理器
[2025-06-11 22:17:16] 🚨 会话管理器结果: {'session_id': 'session_1749651436', 'success': True}
[2025-06-11 22:17:16] 🚨 准备调用 start_realtime_conversation
[2025-06-11 22:17:16] 🚨 disable_vad参数值: False
[2025-06-11 22:43:45] 🚨 start_realtime_dialogue API被调用
[2025-06-11 22:43:45] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-11 22:43:45] 🚨 webui实例获取成功
[2025-06-11 22:43:45] 🚨 准备调用会话管理器
[2025-06-11 22:43:45] 🚨 会话管理器结果: {'session_id': 'session_1749653025', 'success': True}
[2025-06-11 22:43:45] 🚨 准备调用 start_realtime_conversation
[2025-06-11 22:43:45] 🚨 disable_vad参数值: True
[2025-06-12 02:05:28] 🚨 start_realtime_dialogue API被调用
[2025-06-12 02:05:28] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 02:05:28] 🚨 webui实例获取成功
[2025-06-12 02:05:28] 🚨 准备调用会话管理器
[2025-06-12 02:05:28] 🚨 会话管理器结果: {'session_id': 'session_1749665128', 'success': True}
[2025-06-12 02:05:28] 🚨 准备调用 start_realtime_conversation
[2025-06-12 02:05:28] 🚨 disable_vad参数值: True
[2025-06-12 02:06:22] 🚨 start_realtime_dialogue API被调用
[2025-06-12 02:06:22] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 02:06:22] 🚨 webui实例获取成功
[2025-06-12 02:06:22] 🚨 准备调用会话管理器
[2025-06-12 02:06:22] 🚨 会话管理器结果: {'session_id': 'session_1749665182', 'success': True}
[2025-06-12 02:06:22] 🚨 准备调用 start_realtime_conversation
[2025-06-12 02:06:22] 🚨 disable_vad参数值: True
[2025-06-12 02:07:54] 🚨 start_realtime_dialogue API被调用
[2025-06-12 02:07:54] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-12 02:07:54] 🚨 webui实例获取成功
[2025-06-12 02:07:54] 🚨 准备调用会话管理器
[2025-06-12 02:07:54] 🚨 会话管理器结果: {'session_id': 'session_1749665274', 'success': True}
[2025-06-12 02:07:54] 🚨 准备调用 start_realtime_conversation
[2025-06-12 02:07:54] 🚨 disable_vad参数值: False
[2025-06-12 02:08:36] 🚨 start_realtime_dialogue API被调用
[2025-06-12 02:08:36] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 02:08:36] 🚨 webui实例获取成功
[2025-06-12 02:08:36] 🚨 准备调用会话管理器
[2025-06-12 02:08:36] 🚨 会话管理器结果: {'session_id': 'session_1749665316', 'success': True}
[2025-06-12 02:08:36] 🚨 准备调用 start_realtime_conversation
[2025-06-12 02:08:36] 🚨 disable_vad参数值: True
[2025-06-12 02:59:02] 🚨 start_realtime_dialogue API被调用
[2025-06-12 02:59:02] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 02:59:02] 🚨 webui实例获取成功
[2025-06-12 02:59:02] 🚨 准备调用会话管理器
[2025-06-12 02:59:02] 🚨 会话管理器结果: {'session_id': 'session_1749668342', 'success': True}
[2025-06-12 02:59:02] 🚨 准备调用 start_realtime_conversation
[2025-06-12 02:59:02] 🚨 disable_vad参数值: True
[2025-06-12 03:59:03] 🚨 start_realtime_dialogue API被调用
[2025-06-12 03:59:03] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 03:59:03] 🚨 webui实例获取成功
[2025-06-12 03:59:03] 🚨 准备调用会话管理器
[2025-06-12 03:59:03] 🚨 会话管理器结果: {'session_id': 'session_1749671943', 'success': True}
[2025-06-12 03:59:03] 🚨 准备调用 start_realtime_conversation
[2025-06-12 03:59:03] 🚨 disable_vad参数值: True
[2025-06-12 04:45:10] 🚨 start_realtime_dialogue API被调用
[2025-06-12 04:45:10] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 04:45:10] 🚨 webui实例获取成功
[2025-06-12 04:45:10] 🚨 准备调用会话管理器
[2025-06-12 04:45:10] 🚨 会话管理器结果: {'session_id': 'session_1749674710', 'success': True}
[2025-06-12 04:45:10] 🚨 准备调用 start_realtime_conversation
[2025-06-12 04:45:10] 🚨 disable_vad参数值: True
[2025-06-12 05:52:35] 🚨 start_realtime_dialogue API被调用
[2025-06-12 05:52:35] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 05:52:35] 🚨 webui实例获取成功
[2025-06-12 05:52:35] 🚨 准备调用会话管理器
[2025-06-12 05:52:35] 🚨 会话管理器结果: {'session_id': 'session_1749678755', 'success': True}
[2025-06-12 05:52:35] 🚨 准备调用 start_realtime_conversation
[2025-06-12 05:52:35] 🚨 disable_vad参数值: True
[2025-06-12 06:01:26] 🚨 start_realtime_dialogue API被调用
[2025-06-12 06:01:26] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 06:01:26] 🚨 webui实例获取成功
[2025-06-12 06:01:26] 🚨 准备调用会话管理器
[2025-06-12 06:01:26] 🚨 会话管理器结果: {'session_id': 'session_1749679286', 'success': True}
[2025-06-12 06:01:26] 🚨 准备调用 start_realtime_conversation
[2025-06-12 06:01:26] 🚨 disable_vad参数值: True
[2025-06-12 16:15:52] 🚨 start_realtime_dialogue API被调用
[2025-06-12 16:15:52] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 16:15:52] 🚨 webui实例获取成功
[2025-06-12 16:15:52] 🚨 准备调用会话管理器
[2025-06-12 16:15:52] 🚨 会话管理器结果: {'session_id': 'session_1749716152', 'success': True}
[2025-06-12 16:15:52] 🚨 准备调用 start_realtime_conversation
[2025-06-12 16:15:52] 🚨 disable_vad参数值: True
[2025-06-12 16:18:02] 🚨 start_realtime_dialogue API被调用
[2025-06-12 16:18:02] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 16:18:02] 🚨 webui实例获取成功
[2025-06-12 16:18:02] 🚨 准备调用会话管理器
[2025-06-12 16:18:02] 🚨 会话管理器结果: {'session_id': 'session_1749716282', 'success': True}
[2025-06-12 16:18:02] 🚨 准备调用 start_realtime_conversation
[2025-06-12 16:18:02] 🚨 disable_vad参数值: True
[2025-06-12 16:27:39] 🚨 start_realtime_dialogue API被调用
[2025-06-12 16:27:39] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 16:27:39] 🚨 webui实例获取成功
[2025-06-12 16:27:39] 🚨 准备调用会话管理器
[2025-06-12 16:27:39] 🚨 会话管理器结果: {'session_id': 'session_1749716859', 'success': True}
[2025-06-12 16:27:39] 🚨 准备调用 start_realtime_conversation
[2025-06-12 16:27:39] 🚨 disable_vad参数值: True
[2025-06-12 21:04:37] 🚨 start_realtime_dialogue API被调用
[2025-06-12 21:04:37] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-12 21:04:37] 🚨 webui实例获取成功
[2025-06-12 21:04:37] 🚨 准备调用会话管理器
[2025-06-12 21:04:37] 🚨 会话管理器结果: {'session_id': 'session_1749733477', 'success': True}
[2025-06-12 21:04:37] 🚨 准备调用 start_realtime_conversation
[2025-06-12 21:04:37] 🚨 disable_vad参数值: False
[2025-06-12 21:36:42] 🚨 start_realtime_dialogue API被调用
[2025-06-12 21:36:42] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 21:36:42] 🚨 webui实例获取成功
[2025-06-12 21:36:42] 🚨 准备调用会话管理器
[2025-06-12 21:36:42] 🚨 会话管理器结果: {'session_id': 'session_1749735402', 'success': True}
[2025-06-12 21:36:42] 🚨 准备调用 start_realtime_conversation
[2025-06-12 21:36:42] 🚨 disable_vad参数值: True
[2025-06-12 22:09:18] 🚨 start_realtime_dialogue API被调用
[2025-06-12 22:09:18] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 22:09:18] 🚨 webui实例获取成功
[2025-06-12 22:09:18] 🚨 准备调用会话管理器
[2025-06-12 22:09:18] 🚨 会话管理器结果: {'session_id': 'session_1749737358', 'success': True}
[2025-06-12 22:09:18] 🚨 准备调用 start_realtime_conversation
[2025-06-12 22:09:18] 🚨 disable_vad参数值: True
[2025-06-12 22:58:12] 🚨 start_realtime_dialogue API被调用
[2025-06-12 22:58:12] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 22:58:12] 🚨 webui实例获取成功
[2025-06-12 22:58:12] 🚨 准备调用会话管理器
[2025-06-12 22:58:12] 🚨 会话管理器结果: {'session_id': 'session_1749740292', 'success': True}
[2025-06-12 22:58:12] 🚨 准备调用 start_realtime_conversation
[2025-06-12 22:58:12] 🚨 disable_vad参数值: True
[2025-06-12 23:04:14] 🚨 start_realtime_dialogue API被调用
[2025-06-12 23:04:14] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-12 23:04:14] 🚨 webui实例获取成功
[2025-06-12 23:04:14] 🚨 准备调用会话管理器
[2025-06-12 23:04:14] 🚨 会话管理器结果: {'session_id': 'session_1749740654', 'success': True}
[2025-06-12 23:04:14] 🚨 准备调用 start_realtime_conversation
[2025-06-12 23:04:14] 🚨 disable_vad参数值: True
[2025-06-16 07:25:11] 🚨 start_realtime_dialogue API被调用
[2025-06-16 07:25:11] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 07:25:11] 🚨 webui实例获取成功
[2025-06-16 07:25:11] 🚨 准备调用会话管理器
[2025-06-16 07:25:11] 🚨 会话管理器结果: {'session_id': 'session_1750029911', 'success': True}
[2025-06-16 07:25:11] 🚨 准备调用 start_realtime_conversation
[2025-06-16 07:25:11] 🚨 disable_vad参数值: True
[2025-06-16 07:29:21] 🚨 start_realtime_dialogue API被调用
[2025-06-16 07:29:21] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 07:29:21] 🚨 webui实例获取成功
[2025-06-16 07:29:21] 🚨 准备调用会话管理器
[2025-06-16 07:29:21] 🚨 会话管理器结果: {'session_id': 'session_1750030161', 'success': True}
[2025-06-16 07:29:21] 🚨 准备调用 start_realtime_conversation
[2025-06-16 07:29:21] 🚨 disable_vad参数值: True
[2025-06-16 07:51:58] 🚨 start_realtime_dialogue API被调用
[2025-06-16 07:51:58] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 07:51:58] 🚨 webui实例获取成功
[2025-06-16 07:51:58] 🚨 准备调用会话管理器
[2025-06-16 07:51:58] 🚨 会话管理器结果: {'session_id': 'session_1750031518', 'success': True}
[2025-06-16 07:51:58] 🚨 准备调用 start_realtime_conversation
[2025-06-16 07:51:58] 🚨 disable_vad参数值: True
[2025-06-16 08:20:21] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:20:21] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 08:20:21] 🚨 webui实例获取成功
[2025-06-16 08:20:21] 🚨 准备调用会话管理器
[2025-06-16 08:20:21] 🚨 会话管理器结果: {'session_id': 'session_1750033221', 'success': True}
[2025-06-16 08:20:21] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:20:21] 🚨 disable_vad参数值: True
[2025-06-16 08:36:37] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:36:37] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 08:36:37] 🚨 webui实例获取成功
[2025-06-16 08:36:37] 🚨 准备调用会话管理器
[2025-06-16 08:36:37] 🚨 会话管理器结果: {'session_id': 'session_1750034197', 'success': True}
[2025-06-16 08:36:37] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:36:37] 🚨 disable_vad参数值: False
[2025-06-16 08:36:56] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:36:56] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 08:36:56] 🚨 webui实例获取成功
[2025-06-16 08:36:56] 🚨 准备调用会话管理器
[2025-06-16 08:36:56] 🚨 会话管理器结果: {'session_id': 'session_1750034216', 'success': True}
[2025-06-16 08:36:56] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:36:56] 🚨 disable_vad参数值: False
[2025-06-16 08:37:58] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:37:58] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 08:37:58] 🚨 webui实例获取成功
[2025-06-16 08:37:58] 🚨 准备调用会话管理器
[2025-06-16 08:37:58] 🚨 会话管理器结果: {'session_id': 'session_1750034278', 'success': True}
[2025-06-16 08:37:58] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:37:58] 🚨 disable_vad参数值: False
[2025-06-16 08:39:35] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:39:35] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 08:39:35] 🚨 webui实例获取成功
[2025-06-16 08:39:35] 🚨 准备调用会话管理器
[2025-06-16 08:39:35] 🚨 会话管理器结果: {'session_id': 'session_1750034375', 'success': True}
[2025-06-16 08:39:35] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:39:35] 🚨 disable_vad参数值: True
[2025-06-16 08:43:17] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:43:17] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 08:43:17] 🚨 webui实例获取成功
[2025-06-16 08:43:17] 🚨 准备调用会话管理器
[2025-06-16 08:43:17] 🚨 会话管理器结果: {'session_id': 'session_1750034597', 'success': True}
[2025-06-16 08:43:17] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:43:17] 🚨 disable_vad参数值: True
[2025-06-16 08:49:09] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:49:09] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 08:49:09] 🚨 webui实例获取成功
[2025-06-16 08:49:09] 🚨 准备调用会话管理器
[2025-06-16 08:49:09] 🚨 会话管理器结果: {'session_id': 'session_1750034949', 'success': True}
[2025-06-16 08:49:09] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:49:09] 🚨 disable_vad参数值: True
[2025-06-16 08:54:16] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:54:16] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 08:54:16] 🚨 webui实例获取成功
[2025-06-16 08:54:16] 🚨 准备调用会话管理器
[2025-06-16 08:54:16] 🚨 会话管理器结果: {'session_id': 'session_1750035256', 'success': True}
[2025-06-16 08:54:16] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:54:16] 🚨 disable_vad参数值: True
[2025-06-16 08:56:02] 🚨 start_realtime_dialogue API被调用
[2025-06-16 08:56:02] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 08:56:02] 🚨 webui实例获取成功
[2025-06-16 08:56:02] 🚨 准备调用会话管理器
[2025-06-16 08:56:02] 🚨 会话管理器结果: {'session_id': 'session_1750035362', 'success': True}
[2025-06-16 08:56:02] 🚨 准备调用 start_realtime_conversation
[2025-06-16 08:56:02] 🚨 disable_vad参数值: True
[2025-06-16 09:07:28] 🚨 start_realtime_dialogue API被调用
[2025-06-16 09:07:28] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 09:07:28] 🚨 webui实例获取成功
[2025-06-16 09:07:28] 🚨 准备调用会话管理器
[2025-06-16 09:07:28] 🚨 会话管理器结果: {'session_id': 'session_1750036048', 'success': True}
[2025-06-16 09:07:28] 🚨 准备调用 start_realtime_conversation
[2025-06-16 09:07:28] 🚨 disable_vad参数值: False
[2025-06-16 09:28:48] 🚨 start_realtime_dialogue API被调用
[2025-06-16 09:28:48] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 09:28:48] 🚨 webui实例获取成功
[2025-06-16 09:28:48] 🚨 准备调用会话管理器
[2025-06-16 09:28:48] 🚨 会话管理器结果: {'session_id': 'session_1750037328', 'success': True}
[2025-06-16 09:28:48] 🚨 准备调用 start_realtime_conversation
[2025-06-16 09:28:48] 🚨 disable_vad参数值: True
[2025-06-16 10:04:01] 🚨 start_realtime_dialogue API被调用
[2025-06-16 10:04:01] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 10:04:01] 🚨 webui实例获取成功
[2025-06-16 10:04:01] 🚨 准备调用会话管理器
[2025-06-16 10:04:01] 🚨 会话管理器结果: {'session_id': 'session_1750039441', 'success': True}
[2025-06-16 10:04:01] 🚨 准备调用 start_realtime_conversation
[2025-06-16 10:04:01] 🚨 disable_vad参数值: True
[2025-06-16 10:10:46] 🚨 start_realtime_dialogue API被调用
[2025-06-16 10:10:46] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 10:10:46] 🚨 webui实例获取成功
[2025-06-16 10:10:46] 🚨 准备调用会话管理器
[2025-06-16 10:10:46] 🚨 会话管理器结果: {'session_id': 'session_1750039846', 'success': True}
[2025-06-16 10:10:46] 🚨 准备调用 start_realtime_conversation
[2025-06-16 10:10:46] 🚨 disable_vad参数值: True
[2025-06-16 10:11:51] 🚨 start_realtime_dialogue API被调用
[2025-06-16 10:11:51] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 10:11:51] 🚨 webui实例获取成功
[2025-06-16 10:11:51] 🚨 准备调用会话管理器
[2025-06-16 10:11:51] 🚨 会话管理器结果: {'session_id': 'session_1750039911', 'success': True}
[2025-06-16 10:11:51] 🚨 准备调用 start_realtime_conversation
[2025-06-16 10:11:51] 🚨 disable_vad参数值: False
[2025-06-16 10:13:19] 🚨 start_realtime_dialogue API被调用
[2025-06-16 10:13:19] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 10:13:19] 🚨 webui实例获取成功
[2025-06-16 10:13:19] 🚨 准备调用会话管理器
[2025-06-16 10:13:19] 🚨 会话管理器结果: {'session_id': 'session_1750039999', 'success': True}
[2025-06-16 10:13:19] 🚨 准备调用 start_realtime_conversation
[2025-06-16 10:13:19] 🚨 disable_vad参数值: True
[2025-06-16 11:31:52] 🚨 start_realtime_dialogue API被调用
[2025-06-16 11:31:52] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 11:31:52] 🚨 webui实例获取成功
[2025-06-16 11:31:52] 🚨 准备调用会话管理器
[2025-06-16 11:31:52] 🚨 会话管理器结果: {'session_id': 'session_1750044712', 'success': True}
[2025-06-16 11:31:52] 🚨 准备调用 start_realtime_conversation
[2025-06-16 11:31:52] 🚨 disable_vad参数值: True
[2025-06-16 11:33:10] 🚨 start_realtime_dialogue API被调用
[2025-06-16 11:33:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 11:33:10] 🚨 webui实例获取成功
[2025-06-16 11:33:10] 🚨 准备调用会话管理器
[2025-06-16 11:33:10] 🚨 会话管理器结果: {'session_id': 'session_1750044790', 'success': True}
[2025-06-16 11:33:10] 🚨 准备调用 start_realtime_conversation
[2025-06-16 11:33:10] 🚨 disable_vad参数值: False
[2025-06-16 11:33:29] 🚨 start_realtime_dialogue API被调用
[2025-06-16 11:33:29] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 11:33:29] 🚨 webui实例获取成功
[2025-06-16 11:33:29] 🚨 准备调用会话管理器
[2025-06-16 11:33:29] 🚨 会话管理器结果: {'session_id': 'session_1750044809', 'success': True}
[2025-06-16 11:33:29] 🚨 准备调用 start_realtime_conversation
[2025-06-16 11:33:29] 🚨 disable_vad参数值: True
[2025-06-16 13:07:05] 🚨 start_realtime_dialogue API被调用
[2025-06-16 13:07:05] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 13:07:05] 🚨 webui实例获取成功
[2025-06-16 13:07:05] 🚨 准备调用会话管理器
[2025-06-16 13:07:05] 🚨 会话管理器结果: {'session_id': 'session_1750050425', 'success': True}
[2025-06-16 13:07:05] 🚨 准备调用 start_realtime_conversation
[2025-06-16 13:07:05] 🚨 disable_vad参数值: True
[2025-06-16 13:08:20] 🚨 start_realtime_dialogue API被调用
[2025-06-16 13:08:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 13:08:20] 🚨 webui实例获取成功
[2025-06-16 13:08:20] 🚨 准备调用会话管理器
[2025-06-16 13:08:20] 🚨 会话管理器结果: {'session_id': 'session_1750050500', 'success': True}
[2025-06-16 13:08:20] 🚨 准备调用 start_realtime_conversation
[2025-06-16 13:08:20] 🚨 disable_vad参数值: False
[2025-06-16 13:08:47] 🚨 start_realtime_dialogue API被调用
[2025-06-16 13:08:47] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 13:08:47] 🚨 webui实例获取成功
[2025-06-16 13:08:47] 🚨 准备调用会话管理器
[2025-06-16 13:08:47] 🚨 会话管理器结果: {'session_id': 'session_1750050527', 'success': True}
[2025-06-16 13:08:47] 🚨 准备调用 start_realtime_conversation
[2025-06-16 13:08:47] 🚨 disable_vad参数值: True
[2025-06-16 13:12:08] 🚨 start_realtime_dialogue API被调用
[2025-06-16 13:12:08] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 13:12:08] 🚨 webui实例获取成功
[2025-06-16 13:12:08] 🚨 准备调用会话管理器
[2025-06-16 13:12:08] 🚨 会话管理器结果: {'session_id': 'session_1750050728', 'success': True}
[2025-06-16 13:12:08] 🚨 准备调用 start_realtime_conversation
[2025-06-16 13:12:08] 🚨 disable_vad参数值: False
[2025-06-16 13:16:40] 🚨 start_realtime_dialogue API被调用
[2025-06-16 13:16:40] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 13:16:40] 🚨 webui实例获取成功
[2025-06-16 13:16:40] 🚨 准备调用会话管理器
[2025-06-16 13:16:40] 🚨 会话管理器结果: {'session_id': 'session_1750051000', 'success': True}
[2025-06-16 13:16:40] 🚨 准备调用 start_realtime_conversation
[2025-06-16 13:16:40] 🚨 disable_vad参数值: True
[2025-06-16 16:26:45] 🚨 start_realtime_dialogue API被调用
[2025-06-16 16:26:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 16:26:45] 🚨 webui实例获取成功
[2025-06-16 16:26:45] 🚨 准备调用会话管理器
[2025-06-16 16:26:45] 🚨 会话管理器结果: {'session_id': 'session_1750062405', 'success': True}
[2025-06-16 16:26:45] 🚨 准备调用 start_realtime_conversation
[2025-06-16 16:26:45] 🚨 disable_vad参数值: False
[2025-06-16 16:27:54] 🚨 start_realtime_dialogue API被调用
[2025-06-16 16:27:54] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-16 16:27:54] 🚨 webui实例获取成功
[2025-06-16 16:27:54] 🚨 准备调用会话管理器
[2025-06-16 16:27:54] 🚨 会话管理器结果: {'session_id': 'session_1750062474', 'success': True}
[2025-06-16 16:27:54] 🚨 准备调用 start_realtime_conversation
[2025-06-16 16:27:54] 🚨 disable_vad参数值: True
[2025-06-16 16:35:26] 🚨 start_realtime_dialogue API被调用
[2025-06-16 16:35:26] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-16 16:35:26] 🚨 webui实例获取成功
[2025-06-16 16:35:26] 🚨 准备调用会话管理器
[2025-06-16 16:35:26] 🚨 会话管理器结果: {'session_id': 'session_1750062926', 'success': True}
[2025-06-16 16:35:26] 🚨 准备调用 start_realtime_conversation
[2025-06-16 16:35:26] 🚨 disable_vad参数值: False
[2025-06-17 06:19:31] 🚨 start_realtime_dialogue API被调用
[2025-06-17 06:19:31] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 06:19:31] 🚨 webui实例获取成功
[2025-06-17 06:19:31] 🚨 准备调用会话管理器
[2025-06-17 06:19:31] 🚨 会话管理器结果: {'session_id': 'session_1750112371', 'success': True}
[2025-06-17 06:19:31] 🚨 准备调用 start_realtime_conversation
[2025-06-17 06:19:31] 🚨 disable_vad参数值: False
[2025-06-17 06:19:33] 🚨 start_realtime_dialogue API被调用
[2025-06-17 06:19:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 06:19:33] 🚨 webui实例获取成功
[2025-06-17 06:19:33] 🚨 准备调用会话管理器
[2025-06-17 06:19:33] 🚨 会话管理器结果: {'session_id': 'session_1750112371', 'success': True}
[2025-06-17 06:19:33] 🚨 准备调用 start_realtime_conversation
[2025-06-17 06:19:33] 🚨 disable_vad参数值: False
[2025-06-17 06:19:33] 🚨 start_realtime_dialogue API被调用
[2025-06-17 06:19:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 06:19:33] 🚨 webui实例获取成功
[2025-06-17 06:19:33] 🚨 准备调用会话管理器
[2025-06-17 06:19:33] 🚨 会话管理器结果: {'session_id': 'session_1750112371', 'success': True}
[2025-06-17 06:19:33] 🚨 准备调用 start_realtime_conversation
[2025-06-17 06:19:33] 🚨 disable_vad参数值: False
[2025-06-17 06:34:43] 🚨 start_realtime_dialogue API被调用
[2025-06-17 06:34:43] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 06:34:43] 🚨 webui实例获取成功
[2025-06-17 06:34:43] 🚨 准备调用会话管理器
[2025-06-17 06:34:43] 🚨 会话管理器结果: {'session_id': 'session_1750113283', 'success': True}
[2025-06-17 06:34:43] 🚨 准备调用 start_realtime_conversation
[2025-06-17 06:34:43] 🚨 disable_vad参数值: True
[2025-06-17 06:36:23] 🚨 start_realtime_dialogue API被调用
[2025-06-17 06:36:23] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 06:36:23] 🚨 webui实例获取成功
[2025-06-17 06:36:23] 🚨 准备调用会话管理器
[2025-06-17 06:36:23] 🚨 会话管理器结果: {'session_id': 'session_1750113383', 'success': True}
[2025-06-17 06:36:23] 🚨 准备调用 start_realtime_conversation
[2025-06-17 06:36:23] 🚨 disable_vad参数值: False
[2025-06-17 06:37:43] 🚨 start_realtime_dialogue API被调用
[2025-06-17 06:37:43] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 06:37:43] 🚨 webui实例获取成功
[2025-06-17 06:37:43] 🚨 准备调用会话管理器
[2025-06-17 06:37:43] 🚨 会话管理器结果: {'session_id': 'session_1750113463', 'success': True}
[2025-06-17 06:37:43] 🚨 准备调用 start_realtime_conversation
[2025-06-17 06:37:43] 🚨 disable_vad参数值: True
[2025-06-17 06:57:43] 🚨 start_realtime_dialogue API被调用
[2025-06-17 06:57:43] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 06:57:43] 🚨 webui实例获取成功
[2025-06-17 06:57:43] 🚨 准备调用会话管理器
[2025-06-17 06:57:43] 🚨 会话管理器结果: {'session_id': 'session_1750114663', 'success': True}
[2025-06-17 06:57:43] 🚨 准备调用 start_realtime_conversation
[2025-06-17 06:57:43] 🚨 disable_vad参数值: True
[2025-06-17 07:04:41] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:04:41] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 07:04:41] 🚨 webui实例获取成功
[2025-06-17 07:04:41] 🚨 准备调用会话管理器
[2025-06-17 07:04:41] 🚨 会话管理器结果: {'session_id': 'session_1750115081', 'success': True}
[2025-06-17 07:04:41] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:04:41] 🚨 disable_vad参数值: True
[2025-06-17 07:06:04] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:06:04] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 07:06:04] 🚨 webui实例获取成功
[2025-06-17 07:06:04] 🚨 准备调用会话管理器
[2025-06-17 07:06:04] 🚨 会话管理器结果: {'session_id': 'session_1750115164', 'success': True}
[2025-06-17 07:06:04] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:06:04] 🚨 disable_vad参数值: False
[2025-06-17 07:09:08] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:09:08] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 07:09:08] 🚨 webui实例获取成功
[2025-06-17 07:09:08] 🚨 准备调用会话管理器
[2025-06-17 07:09:08] 🚨 会话管理器结果: {'session_id': 'session_1750115348', 'success': True}
[2025-06-17 07:09:08] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:09:08] 🚨 disable_vad参数值: True
[2025-06-17 07:14:05] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:14:05] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 07:14:05] 🚨 webui实例获取成功
[2025-06-17 07:14:05] 🚨 准备调用会话管理器
[2025-06-17 07:14:05] 🚨 会话管理器结果: {'session_id': 'session_1750115645', 'success': True}
[2025-06-17 07:14:05] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:14:05] 🚨 disable_vad参数值: True
[2025-06-17 07:21:18] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:21:18] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 07:21:18] 🚨 webui实例获取成功
[2025-06-17 07:21:18] 🚨 准备调用会话管理器
[2025-06-17 07:21:18] 🚨 会话管理器结果: {'session_id': 'session_1750116078', 'success': True}
[2025-06-17 07:21:18] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:21:18] 🚨 disable_vad参数值: True
[2025-06-17 07:25:28] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:25:28] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 07:25:28] 🚨 webui实例获取成功
[2025-06-17 07:25:28] 🚨 准备调用会话管理器
[2025-06-17 07:25:28] 🚨 会话管理器结果: {'session_id': 'session_1750116328', 'success': True}
[2025-06-17 07:25:28] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:25:28] 🚨 disable_vad参数值: True
[2025-06-17 07:27:14] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:27:14] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 07:27:14] 🚨 webui实例获取成功
[2025-06-17 07:27:14] 🚨 准备调用会话管理器
[2025-06-17 07:27:14] 🚨 会话管理器结果: {'session_id': 'session_1750116434', 'success': True}
[2025-06-17 07:27:14] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:27:14] 🚨 disable_vad参数值: False
[2025-06-17 07:29:37] 🚨 start_realtime_dialogue API被调用
[2025-06-17 07:29:37] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 07:29:37] 🚨 webui实例获取成功
[2025-06-17 07:29:37] 🚨 准备调用会话管理器
[2025-06-17 07:29:37] 🚨 会话管理器结果: {'session_id': 'session_1750116577', 'success': True}
[2025-06-17 07:29:37] 🚨 准备调用 start_realtime_conversation
[2025-06-17 07:29:37] 🚨 disable_vad参数值: True
[2025-06-17 08:45:21] 🚨 start_realtime_dialogue API被调用
[2025-06-17 08:45:21] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 08:45:21] 🚨 webui实例获取成功
[2025-06-17 08:45:21] 🚨 准备调用会话管理器
[2025-06-17 08:45:21] 🚨 会话管理器结果: {'session_id': 'session_1750121121', 'success': True}
[2025-06-17 08:45:21] 🚨 准备调用 start_realtime_conversation
[2025-06-17 08:45:21] 🚨 disable_vad参数值: True
[2025-06-17 09:20:31] 🚨 start_realtime_dialogue API被调用
[2025-06-17 09:20:31] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 09:20:31] 🚨 webui实例获取成功
[2025-06-17 09:20:31] 🚨 准备调用会话管理器
[2025-06-17 09:20:31] 🚨 会话管理器结果: {'session_id': 'session_1750123231', 'success': True}
[2025-06-17 09:20:31] 🚨 准备调用 start_realtime_conversation
[2025-06-17 09:20:31] 🚨 disable_vad参数值: True
[2025-06-17 10:45:03] 🚨 start_realtime_dialogue API被调用
[2025-06-17 10:45:03] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 10:45:03] 🚨 webui实例获取成功
[2025-06-17 10:45:03] 🚨 准备调用会话管理器
[2025-06-17 10:45:03] 🚨 会话管理器结果: {'session_id': 'session_1750128303', 'success': True}
[2025-06-17 10:45:03] 🚨 准备调用 start_realtime_conversation
[2025-06-17 10:45:03] 🚨 disable_vad参数值: True
[2025-06-17 10:55:26] 🚨 start_realtime_dialogue API被调用
[2025-06-17 10:55:26] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 10:55:26] 🚨 webui实例获取成功
[2025-06-17 10:55:26] 🚨 准备调用会话管理器
[2025-06-17 10:55:26] 🚨 会话管理器结果: {'session_id': 'session_1750128926', 'success': True}
[2025-06-17 10:55:26] 🚨 准备调用 start_realtime_conversation
[2025-06-17 10:55:26] 🚨 disable_vad参数值: True
[2025-06-17 11:04:34] 🚨 start_realtime_dialogue API被调用
[2025-06-17 11:04:34] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 11:04:34] 🚨 webui实例获取成功
[2025-06-17 11:04:34] 🚨 准备调用会话管理器
[2025-06-17 11:04:34] 🚨 会话管理器结果: {'session_id': 'session_1750129474', 'success': True}
[2025-06-17 11:04:34] 🚨 准备调用 start_realtime_conversation
[2025-06-17 11:04:34] 🚨 disable_vad参数值: True
[2025-06-17 11:34:52] 🚨 start_realtime_dialogue API被调用
[2025-06-17 11:34:52] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 11:34:52] 🚨 webui实例获取成功
[2025-06-17 11:34:52] 🚨 准备调用会话管理器
[2025-06-17 11:34:52] 🚨 会话管理器结果: {'session_id': 'session_1750131292', 'success': True}
[2025-06-17 11:34:52] 🚨 准备调用 start_realtime_conversation
[2025-06-17 11:34:52] 🚨 disable_vad参数值: True
[2025-06-17 11:51:48] 🚨 start_realtime_dialogue API被调用
[2025-06-17 11:51:48] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 11:51:48] 🚨 webui实例获取成功
[2025-06-17 11:51:48] 🚨 准备调用会话管理器
[2025-06-17 11:51:48] 🚨 会话管理器结果: {'session_id': 'session_1750132308', 'success': True}
[2025-06-17 11:51:48] 🚨 准备调用 start_realtime_conversation
[2025-06-17 11:51:48] 🚨 disable_vad参数值: True
[2025-06-17 12:07:33] 🚨 start_realtime_dialogue API被调用
[2025-06-17 12:07:33] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 12:07:33] 🚨 webui实例获取成功
[2025-06-17 12:07:33] 🚨 准备调用会话管理器
[2025-06-17 12:07:33] 🚨 会话管理器结果: {'session_id': 'session_1750133253', 'success': True}
[2025-06-17 12:07:33] 🚨 准备调用 start_realtime_conversation
[2025-06-17 12:07:33] 🚨 disable_vad参数值: True
[2025-06-17 12:13:09] 🚨 start_realtime_dialogue API被调用
[2025-06-17 12:13:09] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 12:13:09] 🚨 webui实例获取成功
[2025-06-17 12:13:09] 🚨 准备调用会话管理器
[2025-06-17 12:13:09] 🚨 会话管理器结果: {'session_id': 'session_1750133253', 'success': True}
[2025-06-17 12:13:09] 🚨 准备调用 start_realtime_conversation
[2025-06-17 12:13:09] 🚨 disable_vad参数值: True
[2025-06-17 12:26:03] 🚨 start_realtime_dialogue API被调用
[2025-06-17 12:26:03] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 12:26:03] 🚨 webui实例获取成功
[2025-06-17 12:26:03] 🚨 准备调用会话管理器
[2025-06-17 12:26:03] 🚨 会话管理器结果: {'session_id': 'session_1750133253', 'success': True}
[2025-06-17 12:26:03] 🚨 准备调用 start_realtime_conversation
[2025-06-17 12:26:03] 🚨 disable_vad参数值: True
[2025-06-17 12:26:40] 🚨 start_realtime_dialogue API被调用
[2025-06-17 12:26:40] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 12:26:40] 🚨 webui实例获取成功
[2025-06-17 12:26:40] 🚨 准备调用会话管理器
[2025-06-17 12:26:40] 🚨 会话管理器结果: {'session_id': 'session_1750134400', 'success': True}
[2025-06-17 12:26:40] 🚨 准备调用 start_realtime_conversation
[2025-06-17 12:26:40] 🚨 disable_vad参数值: True
[2025-06-17 13:08:44] 🚨 start_realtime_dialogue API被调用
[2025-06-17 13:08:44] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 13:08:44] 🚨 webui实例获取成功
[2025-06-17 13:08:44] 🚨 准备调用会话管理器
[2025-06-17 13:08:44] 🚨 会话管理器结果: {'session_id': 'session_1750136924', 'success': True}
[2025-06-17 13:08:44] 🚨 准备调用 start_realtime_conversation
[2025-06-17 13:08:44] 🚨 disable_vad参数值: True
[2025-06-17 13:10:52] 🚨 start_realtime_dialogue API被调用
[2025-06-17 13:10:52] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 13:10:52] 🚨 webui实例获取成功
[2025-06-17 13:10:52] 🚨 准备调用会话管理器
[2025-06-17 13:10:52] 🚨 会话管理器结果: {'session_id': 'session_1750137052', 'success': True}
[2025-06-17 13:10:52] 🚨 准备调用 start_realtime_conversation
[2025-06-17 13:10:52] 🚨 disable_vad参数值: True
[2025-06-17 13:36:53] 🚨 start_realtime_dialogue API被调用
[2025-06-17 13:36:53] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 13:36:53] 🚨 webui实例获取成功
[2025-06-17 13:36:53] 🚨 准备调用会话管理器
[2025-06-17 13:36:53] 🚨 会话管理器结果: {'session_id': 'session_1750138613', 'success': True}
[2025-06-17 13:36:53] 🚨 准备调用 start_realtime_conversation
[2025-06-17 13:36:53] 🚨 disable_vad参数值: True
[2025-06-17 16:25:40] 🚨 start_realtime_dialogue API被调用
[2025-06-17 16:25:40] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 16:25:40] 🚨 webui实例获取成功
[2025-06-17 16:25:40] 🚨 准备调用会话管理器
[2025-06-17 16:25:40] 🚨 会话管理器结果: {'session_id': 'session_1750148740', 'success': True}
[2025-06-17 16:25:40] 🚨 准备调用 start_realtime_conversation
[2025-06-17 16:25:40] 🚨 disable_vad参数值: True
[2025-06-17 18:31:25] 🚨 start_realtime_dialogue API被调用
[2025-06-17 18:31:25] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 18:31:25] 🚨 webui实例获取成功
[2025-06-17 18:31:25] 🚨 准备调用会话管理器
[2025-06-17 18:31:25] 🚨 会话管理器结果: {'session_id': 'session_1750156285', 'success': True}
[2025-06-17 18:31:25] 🚨 准备调用 start_realtime_conversation
[2025-06-17 18:31:25] 🚨 disable_vad参数值: True
[2025-06-17 19:00:15] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:00:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:00:15] 🚨 webui实例获取成功
[2025-06-17 19:00:15] 🚨 准备调用会话管理器
[2025-06-17 19:00:15] 🚨 会话管理器结果: {'session_id': 'session_1750158015', 'success': True}
[2025-06-17 19:00:15] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:00:15] 🚨 disable_vad参数值: False
[2025-06-17 19:01:15] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:01:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:01:15] 🚨 webui实例获取成功
[2025-06-17 19:01:15] 🚨 准备调用会话管理器
[2025-06-17 19:01:15] 🚨 会话管理器结果: {'session_id': 'session_1750158075', 'success': True}
[2025-06-17 19:01:15] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:01:15] 🚨 disable_vad参数值: False
[2025-06-17 19:01:42] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:01:42] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:01:42] 🚨 webui实例获取成功
[2025-06-17 19:01:42] 🚨 准备调用会话管理器
[2025-06-17 19:01:42] 🚨 会话管理器结果: {'session_id': 'session_1750158102', 'success': True}
[2025-06-17 19:01:42] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:01:42] 🚨 disable_vad参数值: False
[2025-06-17 19:02:20] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:02:20] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 19:02:20] 🚨 webui实例获取成功
[2025-06-17 19:02:20] 🚨 准备调用会话管理器
[2025-06-17 19:02:20] 🚨 会话管理器结果: {'session_id': 'session_1750158140', 'success': True}
[2025-06-17 19:02:20] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:02:20] 🚨 disable_vad参数值: True
[2025-06-17 19:04:03] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:04:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:04:03] 🚨 webui实例获取成功
[2025-06-17 19:04:03] 🚨 准备调用会话管理器
[2025-06-17 19:04:03] 🚨 会话管理器结果: {'session_id': 'session_1750158243', 'success': True}
[2025-06-17 19:04:03] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:04:03] 🚨 disable_vad参数值: False
[2025-06-17 19:08:52] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:08:52] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:08:52] 🚨 webui实例获取成功
[2025-06-17 19:08:52] 🚨 准备调用会话管理器
[2025-06-17 19:08:52] 🚨 会话管理器结果: {'session_id': 'session_1750158532', 'success': True}
[2025-06-17 19:08:52] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:08:52] 🚨 disable_vad参数值: False
[2025-06-17 19:09:46] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:09:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:09:46] 🚨 webui实例获取成功
[2025-06-17 19:09:46] 🚨 准备调用会话管理器
[2025-06-17 19:09:46] 🚨 会话管理器结果: {'session_id': 'session_1750158586', 'success': True}
[2025-06-17 19:09:46] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:09:46] 🚨 disable_vad参数值: False
[2025-06-17 19:11:08] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:11:08] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:11:08] 🚨 webui实例获取成功
[2025-06-17 19:11:08] 🚨 准备调用会话管理器
[2025-06-17 19:11:08] 🚨 会话管理器结果: {'session_id': 'session_1750158668', 'success': True}
[2025-06-17 19:11:08] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:11:08] 🚨 disable_vad参数值: False
[2025-06-17 19:11:15] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:11:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:11:15] 🚨 webui实例获取成功
[2025-06-17 19:11:15] 🚨 准备调用会话管理器
[2025-06-17 19:11:15] 🚨 会话管理器结果: {'session_id': 'session_1750158675', 'success': True}
[2025-06-17 19:11:15] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:11:15] 🚨 disable_vad参数值: False
[2025-06-17 19:11:29] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:11:29] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:11:29] 🚨 webui实例获取成功
[2025-06-17 19:11:29] 🚨 准备调用会话管理器
[2025-06-17 19:11:29] 🚨 会话管理器结果: {'session_id': 'session_1750158689', 'success': True}
[2025-06-17 19:11:29] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:11:29] 🚨 disable_vad参数值: False
[2025-06-17 19:11:33] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:11:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:11:33] 🚨 webui实例获取成功
[2025-06-17 19:11:33] 🚨 准备调用会话管理器
[2025-06-17 19:11:33] 🚨 会话管理器结果: {'session_id': 'session_1750158693', 'success': True}
[2025-06-17 19:11:33] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:11:33] 🚨 disable_vad参数值: False
[2025-06-17 19:11:40] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:11:40] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:11:40] 🚨 webui实例获取成功
[2025-06-17 19:11:40] 🚨 准备调用会话管理器
[2025-06-17 19:11:40] 🚨 会话管理器结果: {'session_id': 'session_1750158700', 'success': True}
[2025-06-17 19:11:40] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:11:40] 🚨 disable_vad参数值: False
[2025-06-17 19:12:00] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:12:00] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:12:00] 🚨 webui实例获取成功
[2025-06-17 19:12:00] 🚨 准备调用会话管理器
[2025-06-17 19:12:00] 🚨 会话管理器结果: {'session_id': 'session_1750158720', 'success': True}
[2025-06-17 19:12:00] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:12:00] 🚨 disable_vad参数值: False
[2025-06-17 19:12:10] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:12:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:12:10] 🚨 webui实例获取成功
[2025-06-17 19:12:10] 🚨 准备调用会话管理器
[2025-06-17 19:12:10] 🚨 会话管理器结果: {'session_id': 'session_1750158730', 'success': True}
[2025-06-17 19:12:10] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:12:10] 🚨 disable_vad参数值: False
[2025-06-17 19:12:20] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:12:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:12:20] 🚨 webui实例获取成功
[2025-06-17 19:12:20] 🚨 准备调用会话管理器
[2025-06-17 19:12:20] 🚨 会话管理器结果: {'session_id': 'session_1750158740', 'success': True}
[2025-06-17 19:12:20] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:12:20] 🚨 disable_vad参数值: False
[2025-06-17 19:13:19] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:13:19] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:13:19] 🚨 webui实例获取成功
[2025-06-17 19:13:19] 🚨 准备调用会话管理器
[2025-06-17 19:13:19] 🚨 会话管理器结果: {'session_id': 'session_1750158799', 'success': True}
[2025-06-17 19:13:19] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:13:19] 🚨 disable_vad参数值: False
[2025-06-17 19:16:12] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:16:12] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 19:16:12] 🚨 webui实例获取成功
[2025-06-17 19:16:12] 🚨 准备调用会话管理器
[2025-06-17 19:16:12] 🚨 会话管理器结果: {'session_id': 'session_1750158972', 'success': True}
[2025-06-17 19:16:12] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:16:12] 🚨 disable_vad参数值: True
[2025-06-17 19:17:56] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:17:56] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:17:56] 🚨 webui实例获取成功
[2025-06-17 19:17:56] 🚨 准备调用会话管理器
[2025-06-17 19:17:56] 🚨 会话管理器结果: {'session_id': 'session_1750159076', 'success': True}
[2025-06-17 19:17:56] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:17:56] 🚨 disable_vad参数值: False
[2025-06-17 19:22:11] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:22:11] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:22:11] 🚨 webui实例获取成功
[2025-06-17 19:22:11] 🚨 准备调用会话管理器
[2025-06-17 19:22:11] 🚨 会话管理器结果: {'session_id': 'session_1750159331', 'success': True}
[2025-06-17 19:22:11] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:22:11] 🚨 disable_vad参数值: False
[2025-06-17 19:22:49] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:22:49] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:22:49] 🚨 webui实例获取成功
[2025-06-17 19:22:49] 🚨 准备调用会话管理器
[2025-06-17 19:22:49] 🚨 会话管理器结果: {'session_id': 'session_1750159369', 'success': True}
[2025-06-17 19:22:49] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:22:49] 🚨 disable_vad参数值: False
[2025-06-17 19:31:29] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:31:29] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:31:29] 🚨 webui实例获取成功
[2025-06-17 19:31:29] 🚨 准备调用会话管理器
[2025-06-17 19:31:29] 🚨 会话管理器结果: {'session_id': 'session_1750159889', 'success': True}
[2025-06-17 19:31:29] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:31:29] 🚨 disable_vad参数值: False
[2025-06-17 19:31:43] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:31:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:31:43] 🚨 webui实例获取成功
[2025-06-17 19:31:43] 🚨 准备调用会话管理器
[2025-06-17 19:31:43] 🚨 会话管理器结果: {'session_id': 'session_1750159903', 'success': True}
[2025-06-17 19:31:43] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:31:43] 🚨 disable_vad参数值: False
[2025-06-17 19:37:27] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:37:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:37:27] 🚨 webui实例获取成功
[2025-06-17 19:37:27] 🚨 准备调用会话管理器
[2025-06-17 19:37:27] 🚨 会话管理器结果: {'session_id': 'session_1750160247', 'success': True}
[2025-06-17 19:37:27] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:37:27] 🚨 disable_vad参数值: False
[2025-06-17 19:45:57] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:45:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:45:57] 🚨 webui实例获取成功
[2025-06-17 19:45:57] 🚨 准备调用会话管理器
[2025-06-17 19:45:57] 🚨 会话管理器结果: {'session_id': 'session_1750160757', 'success': True}
[2025-06-17 19:45:57] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:45:57] 🚨 disable_vad参数值: False
[2025-06-17 19:51:01] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:51:01] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:51:01] 🚨 webui实例获取成功
[2025-06-17 19:51:01] 🚨 准备调用会话管理器
[2025-06-17 19:51:01] 🚨 会话管理器结果: {'session_id': 'session_1750161061', 'success': True}
[2025-06-17 19:51:01] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:51:01] 🚨 disable_vad参数值: False
[2025-06-17 19:53:50] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:53:50] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 19:53:50] 🚨 webui实例获取成功
[2025-06-17 19:53:50] 🚨 准备调用会话管理器
[2025-06-17 19:53:50] 🚨 会话管理器结果: {'session_id': 'session_1750161230', 'success': True}
[2025-06-17 19:53:50] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:53:50] 🚨 disable_vad参数值: True
[2025-06-17 19:55:20] 🚨 start_realtime_dialogue API被调用
[2025-06-17 19:55:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 19:55:20] 🚨 webui实例获取成功
[2025-06-17 19:55:20] 🚨 准备调用会话管理器
[2025-06-17 19:55:20] 🚨 会话管理器结果: {'session_id': 'session_1750161320', 'success': True}
[2025-06-17 19:55:20] 🚨 准备调用 start_realtime_conversation
[2025-06-17 19:55:20] 🚨 disable_vad参数值: False
[2025-06-17 20:00:35] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:00:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:00:35] 🚨 webui实例获取成功
[2025-06-17 20:00:35] 🚨 准备调用会话管理器
[2025-06-17 20:00:35] 🚨 会话管理器结果: {'session_id': 'session_1750161635', 'success': True}
[2025-06-17 20:00:35] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:00:35] 🚨 disable_vad参数值: False
[2025-06-17 20:01:15] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:01:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:01:15] 🚨 webui实例获取成功
[2025-06-17 20:01:15] 🚨 准备调用会话管理器
[2025-06-17 20:01:15] 🚨 会话管理器结果: {'session_id': 'session_1750161675', 'success': True}
[2025-06-17 20:01:15] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:01:15] 🚨 disable_vad参数值: False
[2025-06-17 20:03:04] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:03:04] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:03:04] 🚨 webui实例获取成功
[2025-06-17 20:03:04] 🚨 准备调用会话管理器
[2025-06-17 20:03:04] 🚨 会话管理器结果: {'session_id': 'session_1750161784', 'success': True}
[2025-06-17 20:03:04] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:03:04] 🚨 disable_vad参数值: False
[2025-06-17 20:03:09] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:03:09] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:03:09] 🚨 webui实例获取成功
[2025-06-17 20:03:09] 🚨 准备调用会话管理器
[2025-06-17 20:03:09] 🚨 会话管理器结果: {'session_id': 'session_1750161789', 'success': True}
[2025-06-17 20:03:09] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:03:09] 🚨 disable_vad参数值: False
[2025-06-17 20:04:45] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:04:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:04:45] 🚨 webui实例获取成功
[2025-06-17 20:04:45] 🚨 准备调用会话管理器
[2025-06-17 20:04:45] 🚨 会话管理器结果: {'session_id': 'session_1750161885', 'success': True}
[2025-06-17 20:04:45] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:04:45] 🚨 disable_vad参数值: False
[2025-06-17 20:05:43] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:05:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:05:43] 🚨 webui实例获取成功
[2025-06-17 20:05:43] 🚨 准备调用会话管理器
[2025-06-17 20:05:43] 🚨 会话管理器结果: {'session_id': 'session_1750161943', 'success': True}
[2025-06-17 20:05:43] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:05:43] 🚨 disable_vad参数值: False
[2025-06-17 20:11:19] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:11:19] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:11:19] 🚨 webui实例获取成功
[2025-06-17 20:11:19] 🚨 准备调用会话管理器
[2025-06-17 20:11:19] 🚨 会话管理器结果: {'session_id': 'session_1750162279', 'success': True}
[2025-06-17 20:11:19] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:11:19] 🚨 disable_vad参数值: False
[2025-06-17 20:13:58] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:13:58] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:13:58] 🚨 webui实例获取成功
[2025-06-17 20:13:58] 🚨 准备调用会话管理器
[2025-06-17 20:13:58] 🚨 会话管理器结果: {'session_id': 'session_1750162438', 'success': True}
[2025-06-17 20:13:58] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:13:58] 🚨 disable_vad参数值: False
[2025-06-17 20:19:45] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:19:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:19:45] 🚨 webui实例获取成功
[2025-06-17 20:19:45] 🚨 准备调用会话管理器
[2025-06-17 20:19:45] 🚨 会话管理器结果: {'session_id': 'session_1750162785', 'success': True}
[2025-06-17 20:19:45] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:19:45] 🚨 disable_vad参数值: False
[2025-06-17 20:19:47] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:19:47] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:19:47] 🚨 webui实例获取成功
[2025-06-17 20:19:47] 🚨 准备调用会话管理器
[2025-06-17 20:19:47] 🚨 会话管理器结果: {'session_id': 'session_1750162785', 'success': True}
[2025-06-17 20:19:47] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:19:47] 🚨 disable_vad参数值: False
[2025-06-17 20:19:51] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:19:51] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:19:51] 🚨 webui实例获取成功
[2025-06-17 20:19:51] 🚨 准备调用会话管理器
[2025-06-17 20:19:51] 🚨 会话管理器结果: {'session_id': 'session_1750162785', 'success': True}
[2025-06-17 20:19:51] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:19:51] 🚨 disable_vad参数值: False
[2025-06-17 20:19:57] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:19:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:19:57] 🚨 webui实例获取成功
[2025-06-17 20:19:57] 🚨 准备调用会话管理器
[2025-06-17 20:19:57] 🚨 会话管理器结果: {'session_id': 'session_1750162785', 'success': True}
[2025-06-17 20:19:57] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:19:57] 🚨 disable_vad参数值: False
[2025-06-17 20:19:59] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:19:59] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:19:59] 🚨 webui实例获取成功
[2025-06-17 20:19:59] 🚨 准备调用会话管理器
[2025-06-17 20:19:59] 🚨 会话管理器结果: {'session_id': 'session_1750162785', 'success': True}
[2025-06-17 20:19:59] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:19:59] 🚨 disable_vad参数值: False
[2025-06-17 20:20:03] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:20:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:20:03] 🚨 webui实例获取成功
[2025-06-17 20:20:03] 🚨 准备调用会话管理器
[2025-06-17 20:20:03] 🚨 会话管理器结果: {'session_id': 'session_1750162785', 'success': True}
[2025-06-17 20:20:03] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:20:03] 🚨 disable_vad参数值: False
[2025-06-17 20:22:00] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:22:00] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:22:00] 🚨 webui实例获取成功
[2025-06-17 20:22:00] 🚨 准备调用会话管理器
[2025-06-17 20:22:00] 🚨 会话管理器结果: {'session_id': 'session_1750162920', 'success': True}
[2025-06-17 20:22:00] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:22:00] 🚨 disable_vad参数值: False
[2025-06-17 20:22:02] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:22:02] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:22:02] 🚨 webui实例获取成功
[2025-06-17 20:22:02] 🚨 准备调用会话管理器
[2025-06-17 20:22:02] 🚨 会话管理器结果: {'session_id': 'session_1750162920', 'success': True}
[2025-06-17 20:22:02] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:22:02] 🚨 disable_vad参数值: False
[2025-06-17 20:22:02] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:22:02] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:22:02] 🚨 webui实例获取成功
[2025-06-17 20:22:02] 🚨 准备调用会话管理器
[2025-06-17 20:22:02] 🚨 会话管理器结果: {'session_id': 'session_1750162920', 'success': True}
[2025-06-17 20:22:02] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:22:02] 🚨 disable_vad参数值: False
[2025-06-17 20:22:46] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:22:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:22:46] 🚨 webui实例获取成功
[2025-06-17 20:22:46] 🚨 准备调用会话管理器
[2025-06-17 20:22:46] 🚨 会话管理器结果: {'session_id': 'session_1750162966', 'success': True}
[2025-06-17 20:22:46] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:22:46] 🚨 disable_vad参数值: False
[2025-06-17 20:24:24] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:24:24] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-17 20:24:24] 🚨 webui实例获取成功
[2025-06-17 20:24:24] 🚨 准备调用会话管理器
[2025-06-17 20:24:24] 🚨 会话管理器结果: {'session_id': 'session_1750163064', 'success': True}
[2025-06-17 20:24:24] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:24:24] 🚨 disable_vad参数值: True
[2025-06-17 20:25:49] 🚨 start_realtime_dialogue API被调用
[2025-06-17 20:25:49] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 20:25:49] 🚨 webui实例获取成功
[2025-06-17 20:25:49] 🚨 准备调用会话管理器
[2025-06-17 20:25:49] 🚨 会话管理器结果: {'session_id': 'session_1750163149', 'success': True}
[2025-06-17 20:25:49] 🚨 准备调用 start_realtime_conversation
[2025-06-17 20:25:49] 🚨 disable_vad参数值: False
[2025-06-17 23:55:07] 🚨 start_realtime_dialogue API被调用
[2025-06-17 23:55:07] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 23:55:07] 🚨 webui实例获取成功
[2025-06-17 23:55:07] 🚨 准备调用会话管理器
[2025-06-17 23:55:07] 🚨 会话管理器结果: {'session_id': 'session_1750175707', 'success': True}
[2025-06-17 23:55:07] 🚨 准备调用 start_realtime_conversation
[2025-06-17 23:55:07] 🚨 disable_vad参数值: False
[2025-06-17 23:55:09] 🚨 start_realtime_dialogue API被调用
[2025-06-17 23:55:09] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 23:55:09] 🚨 webui实例获取成功
[2025-06-17 23:55:09] 🚨 准备调用会话管理器
[2025-06-17 23:55:09] 🚨 会话管理器结果: {'session_id': 'session_1750175707', 'success': True}
[2025-06-17 23:55:09] 🚨 准备调用 start_realtime_conversation
[2025-06-17 23:55:09] 🚨 disable_vad参数值: False
[2025-06-17 23:55:09] 🚨 start_realtime_dialogue API被调用
[2025-06-17 23:55:09] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 23:55:09] 🚨 webui实例获取成功
[2025-06-17 23:55:09] 🚨 准备调用会话管理器
[2025-06-17 23:55:09] 🚨 会话管理器结果: {'session_id': 'session_1750175707', 'success': True}
[2025-06-17 23:55:09] 🚨 准备调用 start_realtime_conversation
[2025-06-17 23:55:09] 🚨 disable_vad参数值: False
[2025-06-17 23:55:57] 🚨 start_realtime_dialogue API被调用
[2025-06-17 23:55:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 23:55:57] 🚨 webui实例获取成功
[2025-06-17 23:55:57] 🚨 准备调用会话管理器
[2025-06-17 23:55:57] 🚨 会话管理器结果: {'session_id': 'session_1750175757', 'success': True}
[2025-06-17 23:55:57] 🚨 准备调用 start_realtime_conversation
[2025-06-17 23:55:57] 🚨 disable_vad参数值: False
[2025-06-17 23:55:59] 🚨 start_realtime_dialogue API被调用
[2025-06-17 23:55:59] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 23:55:59] 🚨 webui实例获取成功
[2025-06-17 23:55:59] 🚨 准备调用会话管理器
[2025-06-17 23:55:59] 🚨 会话管理器结果: {'session_id': 'session_1750175757', 'success': True}
[2025-06-17 23:55:59] 🚨 准备调用 start_realtime_conversation
[2025-06-17 23:55:59] 🚨 disable_vad参数值: False
[2025-06-17 23:55:59] 🚨 start_realtime_dialogue API被调用
[2025-06-17 23:55:59] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 23:55:59] 🚨 webui实例获取成功
[2025-06-17 23:55:59] 🚨 准备调用会话管理器
[2025-06-17 23:55:59] 🚨 会话管理器结果: {'session_id': 'session_1750175757', 'success': True}
[2025-06-17 23:55:59] 🚨 准备调用 start_realtime_conversation
[2025-06-17 23:55:59] 🚨 disable_vad参数值: False
[2025-06-17 23:58:02] 🚨 start_realtime_dialogue API被调用
[2025-06-17 23:58:02] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-17 23:58:02] 🚨 webui实例获取成功
[2025-06-17 23:58:02] 🚨 准备调用会话管理器
[2025-06-17 23:58:02] 🚨 会话管理器结果: {'session_id': 'session_1750175882', 'success': True}
[2025-06-17 23:58:02] 🚨 准备调用 start_realtime_conversation
[2025-06-17 23:58:02] 🚨 disable_vad参数值: False
[2025-06-18 11:23:48] 🚨 start_realtime_dialogue API被调用
[2025-06-18 11:23:48] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-18 11:23:48] 🚨 webui实例获取成功
[2025-06-18 11:23:48] 🚨 准备调用会话管理器
[2025-06-18 11:23:48] 🚨 会话管理器结果: {'session_id': 'session_1750217028', 'success': True}
[2025-06-18 11:23:48] 🚨 准备调用 start_realtime_conversation
[2025-06-18 11:23:48] 🚨 disable_vad参数值: False
[2025-06-18 11:24:41] 🚨 start_realtime_dialogue API被调用
[2025-06-18 11:24:41] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-18 11:24:41] 🚨 webui实例获取成功
[2025-06-18 11:24:41] 🚨 准备调用会话管理器
[2025-06-18 11:24:41] 🚨 会话管理器结果: {'session_id': 'session_1750217081', 'success': True}
[2025-06-18 11:24:41] 🚨 准备调用 start_realtime_conversation
[2025-06-18 11:24:41] 🚨 disable_vad参数值: False
[2025-06-18 11:26:46] 🚨 start_realtime_dialogue API被调用
[2025-06-18 11:26:46] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-18 11:26:46] 🚨 webui实例获取成功
[2025-06-18 11:26:46] 🚨 准备调用会话管理器
[2025-06-18 11:26:46] 🚨 会话管理器结果: {'session_id': 'session_1750217206', 'success': True}
[2025-06-18 11:26:46] 🚨 准备调用 start_realtime_conversation
[2025-06-18 11:26:46] 🚨 disable_vad参数值: True
[2025-06-18 11:34:26] 🚨 start_realtime_dialogue API被调用
[2025-06-18 11:34:26] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-18 11:34:26] 🚨 webui实例获取成功
[2025-06-18 11:34:26] 🚨 准备调用会话管理器
[2025-06-18 11:34:26] 🚨 会话管理器结果: {'session_id': 'session_1750217666', 'success': True}
[2025-06-18 11:34:26] 🚨 准备调用 start_realtime_conversation
[2025-06-18 11:34:26] 🚨 disable_vad参数值: False
[2025-06-18 11:34:59] 🚨 start_realtime_dialogue API被调用
[2025-06-18 11:34:59] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-18 11:34:59] 🚨 webui实例获取成功
[2025-06-18 11:34:59] 🚨 准备调用会话管理器
[2025-06-18 11:34:59] 🚨 会话管理器结果: {'session_id': 'session_1750217699', 'success': True}
[2025-06-18 11:34:59] 🚨 准备调用 start_realtime_conversation
[2025-06-18 11:34:59] 🚨 disable_vad参数值: False
[2025-06-18 11:43:43] 🚨 start_realtime_dialogue API被调用
[2025-06-18 11:43:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-18 11:43:43] 🚨 webui实例获取成功
[2025-06-18 11:43:43] 🚨 准备调用会话管理器
[2025-06-18 11:43:43] 🚨 会话管理器结果: {'session_id': 'session_1750218223', 'success': True}
[2025-06-18 11:43:43] 🚨 准备调用 start_realtime_conversation
[2025-06-18 11:43:43] 🚨 disable_vad参数值: False
[2025-06-18 11:46:33] 🚨 start_realtime_dialogue API被调用
[2025-06-18 11:46:33] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-18 11:46:33] 🚨 webui实例获取成功
[2025-06-18 11:46:33] 🚨 准备调用会话管理器
[2025-06-18 11:46:33] 🚨 会话管理器结果: {'session_id': 'session_1750218393', 'success': True}
[2025-06-18 11:46:33] 🚨 准备调用 start_realtime_conversation
[2025-06-18 11:46:33] 🚨 disable_vad参数值: True
[2025-06-18 12:43:20] 🚨 start_realtime_dialogue API被调用
[2025-06-18 12:43:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-18 12:43:20] 🚨 webui实例获取成功
[2025-06-18 12:43:20] 🚨 准备调用会话管理器
[2025-06-18 12:43:20] 🚨 会话管理器结果: {'session_id': 'session_1750221800', 'success': True}
[2025-06-18 12:43:20] 🚨 准备调用 start_realtime_conversation
[2025-06-18 12:43:20] 🚨 disable_vad参数值: False
[2025-06-28 20:50:53] 🚨 start_realtime_dialogue API被调用
[2025-06-28 20:50:53] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-28 20:50:53] 🚨 webui实例获取成功
[2025-06-28 20:50:53] 🚨 准备调用会话管理器
[2025-06-28 20:50:53] 🚨 会话管理器结果: {'session_id': 'session_1751115053', 'success': True}
[2025-06-28 20:50:53] 🚨 准备调用 start_realtime_conversation
[2025-06-28 20:50:53] 🚨 disable_vad参数值: True
[2025-06-30 00:26:20] 🚨 start_realtime_dialogue API被调用
[2025-06-30 00:26:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-30 00:26:20] 🚨 webui实例获取成功
[2025-06-30 00:26:20] 🚨 准备调用会话管理器
[2025-06-30 00:26:20] 🚨 会话管理器结果: {'session_id': 'session_1751214380', 'success': True}
[2025-06-30 00:26:20] 🚨 准备调用 start_realtime_conversation
[2025-06-30 00:26:20] 🚨 disable_vad参数值: False
[2025-06-30 00:27:18] 🚨 start_realtime_dialogue API被调用
[2025-06-30 00:27:18] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-30 00:27:18] 🚨 webui实例获取成功
[2025-06-30 00:27:18] 🚨 准备调用会话管理器
[2025-06-30 00:27:18] 🚨 会话管理器结果: {'session_id': 'session_1751214438', 'success': True}
[2025-06-30 00:27:18] 🚨 准备调用 start_realtime_conversation
[2025-06-30 00:27:18] 🚨 disable_vad参数值: False
[2025-06-30 00:29:39] 🚨 start_realtime_dialogue API被调用
[2025-06-30 00:29:39] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-30 00:29:39] 🚨 webui实例获取成功
[2025-06-30 00:29:39] 🚨 准备调用会话管理器
[2025-06-30 00:29:39] 🚨 会话管理器结果: {'session_id': 'session_1751214579', 'success': True}
[2025-06-30 00:29:39] 🚨 准备调用 start_realtime_conversation
[2025-06-30 00:29:39] 🚨 disable_vad参数值: True
[2025-06-30 00:34:18] 🚨 start_realtime_dialogue API被调用
[2025-06-30 00:34:18] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-30 00:34:18] 🚨 webui实例获取成功
[2025-06-30 00:34:18] 🚨 准备调用会话管理器
[2025-06-30 00:34:18] 🚨 会话管理器结果: {'session_id': 'session_1751214858', 'success': True}
[2025-06-30 00:34:18] 🚨 准备调用 start_realtime_conversation
[2025-06-30 00:34:18] 🚨 disable_vad参数值: False
[2025-06-30 00:34:56] 🚨 start_realtime_dialogue API被调用
[2025-06-30 00:34:56] 🚨 参数: disableVAD=True, mode=realtime
[2025-06-30 00:34:56] 🚨 webui实例获取成功
[2025-06-30 00:34:56] 🚨 准备调用会话管理器
[2025-06-30 00:34:56] 🚨 会话管理器结果: {'session_id': 'session_1751214896', 'success': True}
[2025-06-30 00:34:56] 🚨 准备调用 start_realtime_conversation
[2025-06-30 00:34:56] 🚨 disable_vad参数值: True
[2025-06-30 00:37:46] 🚨 start_realtime_dialogue API被调用
[2025-06-30 00:37:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-30 00:37:46] 🚨 webui实例获取成功
[2025-06-30 00:37:46] 🚨 准备调用会话管理器
[2025-06-30 00:37:46] 🚨 会话管理器结果: {'session_id': 'session_1751215066', 'success': True}
[2025-06-30 00:37:46] 🚨 准备调用 start_realtime_conversation
[2025-06-30 00:37:46] 🚨 disable_vad参数值: False
[2025-06-30 01:14:53] 🚨 start_realtime_dialogue API被调用
[2025-06-30 01:14:53] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-30 01:14:53] 🚨 webui实例获取成功
[2025-06-30 01:14:53] 🚨 准备调用会话管理器
[2025-06-30 01:14:53] 🚨 会话管理器结果: {'session_id': 'session_1751217293', 'success': True}
[2025-06-30 01:14:53] 🚨 准备调用 start_realtime_conversation
[2025-06-30 01:14:53] 🚨 disable_vad参数值: False
[2025-06-30 01:27:36] 🚨 start_realtime_dialogue API被调用
[2025-06-30 01:27:36] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-30 01:27:36] 🚨 webui实例获取成功
[2025-06-30 01:27:36] 🚨 准备调用会话管理器
[2025-06-30 01:27:36] 🚨 会话管理器结果: {'session_id': 'session_1751218056', 'success': True}
[2025-06-30 01:27:36] 🚨 准备调用 start_realtime_conversation
[2025-06-30 01:27:36] 🚨 disable_vad参数值: False
[2025-06-30 02:07:44] 🚨 start_realtime_dialogue API被调用
[2025-06-30 02:07:44] 🚨 参数: disableVAD=False, mode=user-voice
[2025-06-30 02:07:44] 🚨 webui实例获取成功
[2025-06-30 02:07:44] 🚨 准备调用会话管理器
[2025-06-30 02:07:44] 🚨 会话管理器结果: {'session_id': 'session_1751220464', 'success': True}
[2025-06-30 02:07:44] 🚨 准备调用 start_realtime_conversation
[2025-06-30 02:07:44] 🚨 disable_vad参数值: False
[2025-07-03 03:10:17] 🚨 start_realtime_dialogue API被调用
[2025-07-03 03:10:17] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-03 03:10:17] 🚨 webui实例获取成功
[2025-07-03 03:10:17] 🚨 准备调用会话管理器
[2025-07-03 03:10:17] 🚨 会话管理器结果: {'session_id': 'session_1751483417', 'success': True}
[2025-07-03 03:10:17] 🚨 准备调用 start_realtime_conversation
[2025-07-03 03:10:17] 🚨 disable_vad参数值: True
[2025-07-03 16:29:39] 🚨 start_realtime_dialogue API被调用
[2025-07-03 16:29:39] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-03 16:29:39] 🚨 webui实例获取成功
[2025-07-03 16:29:39] 🚨 准备调用会话管理器
[2025-07-03 16:29:39] 🚨 会话管理器结果: {'session_id': 'session_1751531379', 'success': True}
[2025-07-03 16:29:39] 🚨 准备调用 start_realtime_conversation
[2025-07-03 16:29:39] 🚨 disable_vad参数值: True
[2025-07-04 08:38:26] 🚨 start_realtime_dialogue API被调用
[2025-07-04 08:38:26] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 08:38:26] 🚨 webui实例获取成功
[2025-07-04 08:38:26] 🚨 准备调用会话管理器
[2025-07-04 08:38:26] 🚨 会话管理器结果: {'session_id': 'session_1751589506', 'success': True}
[2025-07-04 08:38:26] 🚨 准备调用 start_realtime_conversation
[2025-07-04 08:38:26] 🚨 disable_vad参数值: True
[2025-07-04 08:48:37] 🚨 start_realtime_dialogue API被调用
[2025-07-04 08:48:37] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 08:48:37] 🚨 webui实例获取成功
[2025-07-04 08:48:37] 🚨 准备调用会话管理器
[2025-07-04 08:48:37] 🚨 会话管理器结果: {'session_id': 'session_1751590117', 'success': True}
[2025-07-04 08:48:37] 🚨 准备调用 start_realtime_conversation
[2025-07-04 08:48:37] 🚨 disable_vad参数值: True
[2025-07-04 09:22:40] 🚨 start_realtime_dialogue API被调用
[2025-07-04 09:22:40] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 09:22:40] 🚨 webui实例获取成功
[2025-07-04 09:22:40] 🚨 准备调用会话管理器
[2025-07-04 09:22:40] 🚨 会话管理器结果: {'session_id': 'session_1751592160', 'success': True}
[2025-07-04 09:22:40] 🚨 准备调用 start_realtime_conversation
[2025-07-04 09:22:40] 🚨 disable_vad参数值: True
[2025-07-04 09:34:59] 🚨 start_realtime_dialogue API被调用
[2025-07-04 09:34:59] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 09:34:59] 🚨 webui实例获取成功
[2025-07-04 09:34:59] 🚨 准备调用会话管理器
[2025-07-04 09:34:59] 🚨 会话管理器结果: {'session_id': 'session_1751592899', 'success': True}
[2025-07-04 09:34:59] 🚨 准备调用 start_realtime_conversation
[2025-07-04 09:34:59] 🚨 disable_vad参数值: True
[2025-07-04 09:55:55] 🚨 start_realtime_dialogue API被调用
[2025-07-04 09:55:55] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 09:55:55] 🚨 webui实例获取成功
[2025-07-04 09:55:55] 🚨 准备调用会话管理器
[2025-07-04 09:55:55] 🚨 会话管理器结果: {'session_id': 'session_1751594155', 'success': True}
[2025-07-04 09:55:55] 🚨 准备调用 start_realtime_conversation
[2025-07-04 09:55:55] 🚨 disable_vad参数值: True
[2025-07-04 10:04:12] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:04:12] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 10:04:12] 🚨 webui实例获取成功
[2025-07-04 10:04:12] 🚨 准备调用会话管理器
[2025-07-04 10:04:12] 🚨 会话管理器结果: {'session_id': 'session_1751594652', 'success': True}
[2025-07-04 10:04:12] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:04:12] 🚨 disable_vad参数值: True
[2025-07-04 10:19:35] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:19:35] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 10:19:35] 🚨 webui实例获取成功
[2025-07-04 10:19:35] 🚨 准备调用会话管理器
[2025-07-04 10:19:35] 🚨 会话管理器结果: {'session_id': 'session_1751595575', 'success': True}
[2025-07-04 10:19:35] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:19:35] 🚨 disable_vad参数值: True
[2025-07-04 10:25:45] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:25:45] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 10:25:45] 🚨 webui实例获取成功
[2025-07-04 10:25:45] 🚨 准备调用会话管理器
[2025-07-04 10:25:45] 🚨 会话管理器结果: {'session_id': 'session_1751595945', 'success': True}
[2025-07-04 10:25:45] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:25:45] 🚨 disable_vad参数值: True
[2025-07-04 10:30:35] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:30:35] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 10:30:35] 🚨 webui实例获取成功
[2025-07-04 10:30:35] 🚨 准备调用会话管理器
[2025-07-04 10:30:35] 🚨 会话管理器结果: {'session_id': 'session_1751596235', 'success': True}
[2025-07-04 10:30:35] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:30:35] 🚨 disable_vad参数值: True
[2025-07-04 10:44:16] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:44:16] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 10:44:16] 🚨 webui实例获取成功
[2025-07-04 10:44:16] 🚨 准备调用会话管理器
[2025-07-04 10:44:16] 🚨 会话管理器结果: {'session_id': 'session_1751597056', 'success': True}
[2025-07-04 10:44:16] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:44:16] 🚨 disable_vad参数值: True
[2025-07-04 10:50:53] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:50:53] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 10:50:53] 🚨 webui实例获取成功
[2025-07-04 10:50:53] 🚨 准备调用会话管理器
[2025-07-04 10:50:53] 🚨 会话管理器结果: {'session_id': 'session_1751597453', 'success': True}
[2025-07-04 10:50:53] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:50:53] 🚨 disable_vad参数值: False
[2025-07-04 10:50:59] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:50:59] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 10:50:59] 🚨 webui实例获取成功
[2025-07-04 10:50:59] 🚨 准备调用会话管理器
[2025-07-04 10:50:59] 🚨 会话管理器结果: {'session_id': 'session_1751597459', 'success': True}
[2025-07-04 10:50:59] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:50:59] 🚨 disable_vad参数值: False
[2025-07-04 10:51:52] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:51:52] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 10:51:52] 🚨 webui实例获取成功
[2025-07-04 10:51:52] 🚨 准备调用会话管理器
[2025-07-04 10:51:52] 🚨 会话管理器结果: {'session_id': 'session_1751597512', 'success': True}
[2025-07-04 10:51:52] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:51:52] 🚨 disable_vad参数值: False
[2025-07-04 10:52:35] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:52:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 10:52:35] 🚨 webui实例获取成功
[2025-07-04 10:52:35] 🚨 准备调用会话管理器
[2025-07-04 10:52:35] 🚨 会话管理器结果: {'session_id': 'session_1751597555', 'success': True}
[2025-07-04 10:52:35] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:52:35] 🚨 disable_vad参数值: False
[2025-07-04 10:57:53] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:57:53] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 10:57:53] 🚨 webui实例获取成功
[2025-07-04 10:57:53] 🚨 准备调用会话管理器
[2025-07-04 10:57:53] 🚨 会话管理器结果: {'session_id': 'session_1751597873', 'success': True}
[2025-07-04 10:57:53] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:57:53] 🚨 disable_vad参数值: False
[2025-07-04 10:58:41] 🚨 start_realtime_dialogue API被调用
[2025-07-04 10:58:41] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 10:58:41] 🚨 webui实例获取成功
[2025-07-04 10:58:41] 🚨 准备调用会话管理器
[2025-07-04 10:58:41] 🚨 会话管理器结果: {'session_id': 'session_1751597921', 'success': True}
[2025-07-04 10:58:41] 🚨 准备调用 start_realtime_conversation
[2025-07-04 10:58:41] 🚨 disable_vad参数值: False
[2025-07-04 11:27:15] 🚨 start_realtime_dialogue API被调用
[2025-07-04 11:27:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 11:27:15] 🚨 webui实例获取成功
[2025-07-04 11:27:15] 🚨 准备调用会话管理器
[2025-07-04 11:27:15] 🚨 会话管理器结果: {'session_id': 'session_1751599635', 'success': True}
[2025-07-04 11:27:15] 🚨 准备调用 start_realtime_conversation
[2025-07-04 11:27:15] 🚨 disable_vad参数值: False
[2025-07-04 11:28:00] 🚨 start_realtime_dialogue API被调用
[2025-07-04 11:28:00] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 11:28:00] 🚨 webui实例获取成功
[2025-07-04 11:28:00] 🚨 准备调用会话管理器
[2025-07-04 11:28:00] 🚨 会话管理器结果: {'session_id': 'session_1751599680', 'success': True}
[2025-07-04 11:28:00] 🚨 准备调用 start_realtime_conversation
[2025-07-04 11:28:00] 🚨 disable_vad参数值: False
[2025-07-04 11:29:20] 🚨 start_realtime_dialogue API被调用
[2025-07-04 11:29:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-04 11:29:20] 🚨 webui实例获取成功
[2025-07-04 11:29:20] 🚨 准备调用会话管理器
[2025-07-04 11:29:20] 🚨 会话管理器结果: {'session_id': 'session_1751599760', 'success': True}
[2025-07-04 11:29:20] 🚨 准备调用 start_realtime_conversation
[2025-07-04 11:29:20] 🚨 disable_vad参数值: False
[2025-07-04 11:46:36] 🚨 start_realtime_dialogue API被调用
[2025-07-04 11:46:36] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 11:46:36] 🚨 webui实例获取成功
[2025-07-04 11:46:36] 🚨 准备调用会话管理器
[2025-07-04 11:46:36] 🚨 会话管理器结果: {'session_id': 'session_1751600796', 'success': True}
[2025-07-04 11:46:36] 🚨 准备调用 start_realtime_conversation
[2025-07-04 11:46:36] 🚨 disable_vad参数值: True
[2025-07-04 12:10:32] 🚨 start_realtime_dialogue API被调用
[2025-07-04 12:10:32] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 12:10:32] 🚨 webui实例获取成功
[2025-07-04 12:10:32] 🚨 准备调用会话管理器
[2025-07-04 12:10:32] 🚨 会话管理器结果: {'session_id': 'session_1751602232', 'success': True}
[2025-07-04 12:10:32] 🚨 准备调用 start_realtime_conversation
[2025-07-04 12:10:32] 🚨 disable_vad参数值: True
[2025-07-04 12:14:45] 🚨 start_realtime_dialogue API被调用
[2025-07-04 12:14:45] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 12:14:45] 🚨 webui实例获取成功
[2025-07-04 12:14:45] 🚨 准备调用会话管理器
[2025-07-04 12:14:45] 🚨 会话管理器结果: {'session_id': 'session_1751602485', 'success': True}
[2025-07-04 12:14:45] 🚨 准备调用 start_realtime_conversation
[2025-07-04 12:14:45] 🚨 disable_vad参数值: True
[2025-07-04 12:17:47] 🚨 start_realtime_dialogue API被调用
[2025-07-04 12:17:47] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 12:17:47] 🚨 webui实例获取成功
[2025-07-04 12:17:47] 🚨 准备调用会话管理器
[2025-07-04 12:17:47] 🚨 会话管理器结果: {'session_id': 'session_1751602667', 'success': True}
[2025-07-04 12:17:47] 🚨 准备调用 start_realtime_conversation
[2025-07-04 12:17:47] 🚨 disable_vad参数值: True
[2025-07-04 12:20:58] 🚨 start_realtime_dialogue API被调用
[2025-07-04 12:20:58] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 12:20:58] 🚨 webui实例获取成功
[2025-07-04 12:20:58] 🚨 准备调用会话管理器
[2025-07-04 12:20:58] 🚨 会话管理器结果: {'session_id': 'session_1751602858', 'success': True}
[2025-07-04 12:20:58] 🚨 准备调用 start_realtime_conversation
[2025-07-04 12:20:58] 🚨 disable_vad参数值: True
[2025-07-04 12:27:53] 🚨 start_realtime_dialogue API被调用
[2025-07-04 12:27:53] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 12:27:53] 🚨 webui实例获取成功
[2025-07-04 12:27:53] 🚨 准备调用会话管理器
[2025-07-04 12:27:53] 🚨 会话管理器结果: {'session_id': 'session_1751603273', 'success': True}
[2025-07-04 12:27:53] 🚨 准备调用 start_realtime_conversation
[2025-07-04 12:27:53] 🚨 disable_vad参数值: True
[2025-07-04 12:31:27] 🚨 start_realtime_dialogue API被调用
[2025-07-04 12:31:27] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 12:31:27] 🚨 webui实例获取成功
[2025-07-04 12:31:27] 🚨 准备调用会话管理器
[2025-07-04 12:31:27] 🚨 会话管理器结果: {'session_id': 'session_1751603487', 'success': True}
[2025-07-04 12:31:27] 🚨 准备调用 start_realtime_conversation
[2025-07-04 12:31:27] 🚨 disable_vad参数值: True
[2025-07-04 13:39:00] 🚨 start_realtime_dialogue API被调用
[2025-07-04 13:39:00] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-04 13:39:00] 🚨 webui实例获取成功
[2025-07-04 13:39:00] 🚨 准备调用会话管理器
[2025-07-04 13:39:00] 🚨 会话管理器结果: {'session_id': 'session_1751607540', 'success': True}
[2025-07-04 13:39:00] 🚨 准备调用 start_realtime_conversation
[2025-07-04 13:39:00] 🚨 disable_vad参数值: True
[2025-07-12 17:14:33] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:14:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:14:33] 🚨 webui实例获取成功
[2025-07-12 17:14:33] 🚨 准备调用会话管理器
[2025-07-12 17:14:33] 🚨 会话管理器结果: {'session_id': 'session_1752311673', 'success': True}
[2025-07-12 17:14:33] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:14:33] 🚨 disable_vad参数值: False
[2025-07-12 17:14:33] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:14:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:14:33] 🚨 webui实例获取成功
[2025-07-12 17:14:33] 🚨 准备调用会话管理器
[2025-07-12 17:14:33] 🚨 会话管理器结果: {'session_id': 'session_1752311673', 'success': True}
[2025-07-12 17:14:33] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:14:33] 🚨 disable_vad参数值: False
[2025-07-12 17:14:33] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:14:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:14:33] 🚨 webui实例获取成功
[2025-07-12 17:14:33] 🚨 准备调用会话管理器
[2025-07-12 17:14:33] 🚨 会话管理器结果: {'session_id': 'session_1752311673', 'success': True}
[2025-07-12 17:14:33] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:14:33] 🚨 disable_vad参数值: False
[2025-07-12 17:15:10] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:15:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:15:10] 🚨 webui实例获取成功
[2025-07-12 17:15:10] 🚨 准备调用会话管理器
[2025-07-12 17:15:10] 🚨 会话管理器结果: {'session_id': 'session_1752311710', 'success': True}
[2025-07-12 17:15:10] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:15:10] 🚨 disable_vad参数值: False
[2025-07-12 17:15:10] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:15:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:15:10] 🚨 webui实例获取成功
[2025-07-12 17:15:10] 🚨 准备调用会话管理器
[2025-07-12 17:15:10] 🚨 会话管理器结果: {'session_id': 'session_1752311710', 'success': True}
[2025-07-12 17:15:10] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:15:10] 🚨 disable_vad参数值: False
[2025-07-12 17:15:10] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:15:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:15:10] 🚨 webui实例获取成功
[2025-07-12 17:15:10] 🚨 准备调用会话管理器
[2025-07-12 17:15:10] 🚨 会话管理器结果: {'session_id': 'session_1752311710', 'success': True}
[2025-07-12 17:15:10] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:15:10] 🚨 disable_vad参数值: False
[2025-07-12 17:28:22] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:28:22] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:28:22] 🚨 webui实例获取成功
[2025-07-12 17:28:22] 🚨 准备调用会话管理器
[2025-07-12 17:28:22] 🚨 会话管理器结果: {'session_id': 'session_1752312502', 'success': True}
[2025-07-12 17:28:22] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:28:22] 🚨 disable_vad参数值: False
[2025-07-12 17:28:22] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:28:22] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:28:22] 🚨 webui实例获取成功
[2025-07-12 17:28:22] 🚨 准备调用会话管理器
[2025-07-12 17:28:22] 🚨 会话管理器结果: {'session_id': 'session_1752312502', 'success': True}
[2025-07-12 17:28:22] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:28:22] 🚨 disable_vad参数值: False
[2025-07-12 17:28:22] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:28:22] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:28:22] 🚨 webui实例获取成功
[2025-07-12 17:28:22] 🚨 准备调用会话管理器
[2025-07-12 17:28:22] 🚨 会话管理器结果: {'session_id': 'session_1752312502', 'success': True}
[2025-07-12 17:28:22] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:28:22] 🚨 disable_vad参数值: False
[2025-07-12 17:39:07] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:39:07] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:39:07] 🚨 webui实例获取成功
[2025-07-12 17:39:07] 🚨 准备调用会话管理器
[2025-07-12 17:39:07] 🚨 会话管理器结果: {'session_id': 'session_1752313147', 'success': True}
[2025-07-12 17:39:07] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:39:07] 🚨 disable_vad参数值: False
[2025-07-12 17:39:07] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:39:07] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:39:07] 🚨 webui实例获取成功
[2025-07-12 17:39:07] 🚨 准备调用会话管理器
[2025-07-12 17:39:07] 🚨 会话管理器结果: {'session_id': 'session_1752313147', 'success': True}
[2025-07-12 17:39:07] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:39:07] 🚨 disable_vad参数值: False
[2025-07-12 17:39:07] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:39:07] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:39:07] 🚨 webui实例获取成功
[2025-07-12 17:39:07] 🚨 准备调用会话管理器
[2025-07-12 17:39:07] 🚨 会话管理器结果: {'session_id': 'session_1752313147', 'success': True}
[2025-07-12 17:39:07] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:39:07] 🚨 disable_vad参数值: False
[2025-07-12 17:40:12] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:40:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:40:12] 🚨 webui实例获取成功
[2025-07-12 17:40:12] 🚨 准备调用会话管理器
[2025-07-12 17:40:12] 🚨 会话管理器结果: {'session_id': 'session_1752313212', 'success': True}
[2025-07-12 17:40:12] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:40:12] 🚨 disable_vad参数值: False
[2025-07-12 17:40:12] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:40:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:40:12] 🚨 webui实例获取成功
[2025-07-12 17:40:12] 🚨 准备调用会话管理器
[2025-07-12 17:40:12] 🚨 会话管理器结果: {'session_id': 'session_1752313212', 'success': True}
[2025-07-12 17:40:12] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:40:12] 🚨 disable_vad参数值: False
[2025-07-12 17:40:12] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:40:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:40:12] 🚨 webui实例获取成功
[2025-07-12 17:40:12] 🚨 准备调用会话管理器
[2025-07-12 17:40:12] 🚨 会话管理器结果: {'session_id': 'session_1752313212', 'success': True}
[2025-07-12 17:40:12] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:40:12] 🚨 disable_vad参数值: False
[2025-07-12 17:40:17] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:40:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:40:17] 🚨 webui实例获取成功
[2025-07-12 17:40:17] 🚨 准备调用会话管理器
[2025-07-12 17:40:17] 🚨 会话管理器结果: {'session_id': 'session_1752313217', 'success': True}
[2025-07-12 17:40:17] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:40:17] 🚨 disable_vad参数值: False
[2025-07-12 17:40:17] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:40:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:40:17] 🚨 webui实例获取成功
[2025-07-12 17:40:17] 🚨 准备调用会话管理器
[2025-07-12 17:40:17] 🚨 会话管理器结果: {'session_id': 'session_1752313217', 'success': True}
[2025-07-12 17:40:17] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:40:17] 🚨 disable_vad参数值: False
[2025-07-12 17:40:17] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:40:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:40:17] 🚨 webui实例获取成功
[2025-07-12 17:40:17] 🚨 准备调用会话管理器
[2025-07-12 17:40:17] 🚨 会话管理器结果: {'session_id': 'session_1752313217', 'success': True}
[2025-07-12 17:40:17] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:40:17] 🚨 disable_vad参数值: False
[2025-07-12 17:48:17] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:48:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:48:17] 🚨 webui实例获取成功
[2025-07-12 17:48:17] 🚨 准备调用会话管理器
[2025-07-12 17:48:17] 🚨 会话管理器结果: {'session_id': 'session_1752313697', 'success': True}
[2025-07-12 17:48:17] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:48:17] 🚨 disable_vad参数值: False
[2025-07-12 17:48:17] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:48:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:48:17] 🚨 webui实例获取成功
[2025-07-12 17:48:17] 🚨 准备调用会话管理器
[2025-07-12 17:48:17] 🚨 会话管理器结果: {'session_id': 'session_1752313697', 'success': True}
[2025-07-12 17:48:17] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:48:17] 🚨 disable_vad参数值: False
[2025-07-12 17:48:18] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:48:18] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:48:18] 🚨 webui实例获取成功
[2025-07-12 17:48:18] 🚨 准备调用会话管理器
[2025-07-12 17:48:18] 🚨 会话管理器结果: {'session_id': 'session_1752313697', 'success': True}
[2025-07-12 17:48:18] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:48:18] 🚨 disable_vad参数值: False
[2025-07-12 17:50:16] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:50:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:50:16] 🚨 webui实例获取成功
[2025-07-12 17:50:16] 🚨 准备调用会话管理器
[2025-07-12 17:50:16] 🚨 会话管理器结果: {'session_id': 'session_1752313816', 'success': True}
[2025-07-12 17:50:16] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:50:16] 🚨 disable_vad参数值: False
[2025-07-12 17:50:16] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:50:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:50:16] 🚨 webui实例获取成功
[2025-07-12 17:50:16] 🚨 准备调用会话管理器
[2025-07-12 17:50:16] 🚨 会话管理器结果: {'session_id': 'session_1752313816', 'success': True}
[2025-07-12 17:50:16] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:50:16] 🚨 disable_vad参数值: False
[2025-07-12 17:50:16] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:50:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:50:16] 🚨 webui实例获取成功
[2025-07-12 17:50:16] 🚨 准备调用会话管理器
[2025-07-12 17:50:16] 🚨 会话管理器结果: {'session_id': 'session_1752313816', 'success': True}
[2025-07-12 17:50:16] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:50:16] 🚨 disable_vad参数值: False
[2025-07-12 17:55:15] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:55:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:55:15] 🚨 webui实例获取成功
[2025-07-12 17:55:15] 🚨 准备调用会话管理器
[2025-07-12 17:55:15] 🚨 会话管理器结果: {'session_id': 'session_1752314115', 'success': True}
[2025-07-12 17:55:15] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:55:15] 🚨 disable_vad参数值: False
[2025-07-12 17:55:15] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:55:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:55:15] 🚨 webui实例获取成功
[2025-07-12 17:55:15] 🚨 准备调用会话管理器
[2025-07-12 17:55:15] 🚨 会话管理器结果: {'session_id': 'session_1752314115', 'success': True}
[2025-07-12 17:55:15] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:55:15] 🚨 disable_vad参数值: False
[2025-07-12 17:55:15] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:55:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:55:15] 🚨 webui实例获取成功
[2025-07-12 17:55:15] 🚨 准备调用会话管理器
[2025-07-12 17:55:15] 🚨 会话管理器结果: {'session_id': 'session_1752314115', 'success': True}
[2025-07-12 17:55:15] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:55:15] 🚨 disable_vad参数值: False
[2025-07-12 17:56:03] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:56:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:56:03] 🚨 webui实例获取成功
[2025-07-12 17:56:03] 🚨 准备调用会话管理器
[2025-07-12 17:56:03] 🚨 会话管理器结果: {'session_id': 'session_1752314163', 'success': True}
[2025-07-12 17:56:03] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:56:03] 🚨 disable_vad参数值: False
[2025-07-12 17:56:03] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:56:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:56:03] 🚨 webui实例获取成功
[2025-07-12 17:56:03] 🚨 准备调用会话管理器
[2025-07-12 17:56:03] 🚨 会话管理器结果: {'session_id': 'session_1752314163', 'success': True}
[2025-07-12 17:56:03] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:56:03] 🚨 disable_vad参数值: False
[2025-07-12 17:56:03] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:56:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:56:03] 🚨 webui实例获取成功
[2025-07-12 17:56:03] 🚨 准备调用会话管理器
[2025-07-12 17:56:03] 🚨 会话管理器结果: {'session_id': 'session_1752314163', 'success': True}
[2025-07-12 17:56:03] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:56:03] 🚨 disable_vad参数值: False
[2025-07-12 17:56:46] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:56:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:56:46] 🚨 webui实例获取成功
[2025-07-12 17:56:46] 🚨 准备调用会话管理器
[2025-07-12 17:56:46] 🚨 会话管理器结果: {'session_id': 'session_1752314206', 'success': True}
[2025-07-12 17:56:46] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:56:46] 🚨 disable_vad参数值: False
[2025-07-12 17:56:46] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:56:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:56:46] 🚨 webui实例获取成功
[2025-07-12 17:56:46] 🚨 准备调用会话管理器
[2025-07-12 17:56:46] 🚨 会话管理器结果: {'session_id': 'session_1752314206', 'success': True}
[2025-07-12 17:56:46] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:56:46] 🚨 disable_vad参数值: False
[2025-07-12 17:56:46] 🚨 start_realtime_dialogue API被调用
[2025-07-12 17:56:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 17:56:46] 🚨 webui实例获取成功
[2025-07-12 17:56:46] 🚨 准备调用会话管理器
[2025-07-12 17:56:46] 🚨 会话管理器结果: {'session_id': 'session_1752314206', 'success': True}
[2025-07-12 17:56:46] 🚨 准备调用 start_realtime_conversation
[2025-07-12 17:56:46] 🚨 disable_vad参数值: False
[2025-07-12 18:23:32] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:23:32] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:23:32] 🚨 webui实例获取成功
[2025-07-12 18:23:32] 🚨 准备调用会话管理器
[2025-07-12 18:23:32] 🚨 会话管理器结果: {'session_id': 'session_1752315812', 'success': True}
[2025-07-12 18:23:32] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:23:32] 🚨 disable_vad参数值: False
[2025-07-12 18:23:32] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:23:32] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:23:32] 🚨 webui实例获取成功
[2025-07-12 18:23:32] 🚨 准备调用会话管理器
[2025-07-12 18:23:32] 🚨 会话管理器结果: {'session_id': 'session_1752315812', 'success': True}
[2025-07-12 18:23:32] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:23:32] 🚨 disable_vad参数值: False
[2025-07-12 18:23:32] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:23:32] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:23:32] 🚨 webui实例获取成功
[2025-07-12 18:23:32] 🚨 准备调用会话管理器
[2025-07-12 18:23:32] 🚨 会话管理器结果: {'session_id': 'session_1752315812', 'success': True}
[2025-07-12 18:23:32] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:23:32] 🚨 disable_vad参数值: False
[2025-07-12 18:25:45] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:25:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:25:45] 🚨 webui实例获取成功
[2025-07-12 18:25:45] 🚨 准备调用会话管理器
[2025-07-12 18:25:45] 🚨 会话管理器结果: {'session_id': 'session_1752315945', 'success': True}
[2025-07-12 18:25:45] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:25:45] 🚨 disable_vad参数值: False
[2025-07-12 18:25:45] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:25:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:25:45] 🚨 webui实例获取成功
[2025-07-12 18:25:45] 🚨 准备调用会话管理器
[2025-07-12 18:25:45] 🚨 会话管理器结果: {'session_id': 'session_1752315945', 'success': True}
[2025-07-12 18:25:45] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:25:45] 🚨 disable_vad参数值: False
[2025-07-12 18:25:45] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:25:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:25:45] 🚨 webui实例获取成功
[2025-07-12 18:25:45] 🚨 准备调用会话管理器
[2025-07-12 18:25:45] 🚨 会话管理器结果: {'session_id': 'session_1752315945', 'success': True}
[2025-07-12 18:25:45] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:25:45] 🚨 disable_vad参数值: False
[2025-07-12 18:26:27] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:26:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:26:27] 🚨 webui实例获取成功
[2025-07-12 18:26:27] 🚨 准备调用会话管理器
[2025-07-12 18:26:27] 🚨 会话管理器结果: {'session_id': 'session_1752315987', 'success': True}
[2025-07-12 18:26:27] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:26:27] 🚨 disable_vad参数值: False
[2025-07-12 18:26:27] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:26:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:26:27] 🚨 webui实例获取成功
[2025-07-12 18:26:27] 🚨 准备调用会话管理器
[2025-07-12 18:26:27] 🚨 会话管理器结果: {'session_id': 'session_1752315987', 'success': True}
[2025-07-12 18:26:27] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:26:27] 🚨 disable_vad参数值: False
[2025-07-12 18:26:27] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:26:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:26:27] 🚨 webui实例获取成功
[2025-07-12 18:26:27] 🚨 准备调用会话管理器
[2025-07-12 18:26:27] 🚨 会话管理器结果: {'session_id': 'session_1752315987', 'success': True}
[2025-07-12 18:26:27] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:26:27] 🚨 disable_vad参数值: False
[2025-07-12 18:29:01] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:29:01] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:29:01] 🚨 webui实例获取成功
[2025-07-12 18:29:01] 🚨 准备调用会话管理器
[2025-07-12 18:29:01] 🚨 会话管理器结果: {'session_id': 'session_1752315987', 'success': True}
[2025-07-12 18:29:01] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:29:01] 🚨 disable_vad参数值: False
[2025-07-12 18:40:53] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:40:53] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:40:53] 🚨 webui实例获取成功
[2025-07-12 18:40:53] 🚨 准备调用会话管理器
[2025-07-12 18:40:53] 🚨 会话管理器结果: {'session_id': 'session_1752316853', 'success': True}
[2025-07-12 18:40:53] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:40:53] 🚨 disable_vad参数值: False
[2025-07-12 18:40:53] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:40:53] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:40:53] 🚨 webui实例获取成功
[2025-07-12 18:40:53] 🚨 准备调用会话管理器
[2025-07-12 18:40:53] 🚨 会话管理器结果: {'session_id': 'session_1752316853', 'success': True}
[2025-07-12 18:40:53] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:40:53] 🚨 disable_vad参数值: False
[2025-07-12 18:40:53] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:40:53] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:40:53] 🚨 webui实例获取成功
[2025-07-12 18:40:53] 🚨 准备调用会话管理器
[2025-07-12 18:40:53] 🚨 会话管理器结果: {'session_id': 'session_1752316853', 'success': True}
[2025-07-12 18:40:53] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:40:53] 🚨 disable_vad参数值: False
[2025-07-12 18:58:04] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:58:04] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:58:04] 🚨 webui实例获取成功
[2025-07-12 18:58:04] 🚨 准备调用会话管理器
[2025-07-12 18:58:04] 🚨 会话管理器结果: {'session_id': 'session_1752317884', 'success': True}
[2025-07-12 18:58:04] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:58:04] 🚨 disable_vad参数值: False
[2025-07-12 18:58:04] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:58:04] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:58:04] 🚨 webui实例获取成功
[2025-07-12 18:58:04] 🚨 准备调用会话管理器
[2025-07-12 18:58:04] 🚨 会话管理器结果: {'session_id': 'session_1752317884', 'success': True}
[2025-07-12 18:58:04] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:58:04] 🚨 disable_vad参数值: False
[2025-07-12 18:58:04] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:58:04] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:58:04] 🚨 webui实例获取成功
[2025-07-12 18:58:04] 🚨 准备调用会话管理器
[2025-07-12 18:58:04] 🚨 会话管理器结果: {'session_id': 'session_1752317884', 'success': True}
[2025-07-12 18:58:04] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:58:04] 🚨 disable_vad参数值: False
[2025-07-12 18:59:12] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:59:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:59:12] 🚨 webui实例获取成功
[2025-07-12 18:59:12] 🚨 准备调用会话管理器
[2025-07-12 18:59:12] 🚨 会话管理器结果: {'session_id': 'session_1752317952', 'success': True}
[2025-07-12 18:59:12] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:59:12] 🚨 disable_vad参数值: False
[2025-07-12 18:59:12] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:59:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:59:12] 🚨 webui实例获取成功
[2025-07-12 18:59:12] 🚨 准备调用会话管理器
[2025-07-12 18:59:12] 🚨 会话管理器结果: {'session_id': 'session_1752317952', 'success': True}
[2025-07-12 18:59:12] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:59:12] 🚨 disable_vad参数值: False
[2025-07-12 18:59:12] 🚨 start_realtime_dialogue API被调用
[2025-07-12 18:59:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 18:59:12] 🚨 webui实例获取成功
[2025-07-12 18:59:12] 🚨 准备调用会话管理器
[2025-07-12 18:59:12] 🚨 会话管理器结果: {'session_id': 'session_1752317952', 'success': True}
[2025-07-12 18:59:12] 🚨 准备调用 start_realtime_conversation
[2025-07-12 18:59:12] 🚨 disable_vad参数值: False
[2025-07-12 19:00:57] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:00:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:00:57] 🚨 webui实例获取成功
[2025-07-12 19:00:57] 🚨 准备调用会话管理器
[2025-07-12 19:00:57] 🚨 会话管理器结果: {'session_id': 'session_1752318057', 'success': True}
[2025-07-12 19:00:57] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:00:57] 🚨 disable_vad参数值: False
[2025-07-12 19:00:57] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:00:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:00:57] 🚨 webui实例获取成功
[2025-07-12 19:00:57] 🚨 准备调用会话管理器
[2025-07-12 19:00:57] 🚨 会话管理器结果: {'session_id': 'session_1752318057', 'success': True}
[2025-07-12 19:00:57] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:00:57] 🚨 disable_vad参数值: False
[2025-07-12 19:00:57] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:00:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:00:57] 🚨 webui实例获取成功
[2025-07-12 19:00:57] 🚨 准备调用会话管理器
[2025-07-12 19:00:57] 🚨 会话管理器结果: {'session_id': 'session_1752318057', 'success': True}
[2025-07-12 19:00:57] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:00:57] 🚨 disable_vad参数值: False
[2025-07-12 19:03:50] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:03:50] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:03:50] 🚨 webui实例获取成功
[2025-07-12 19:03:50] 🚨 准备调用会话管理器
[2025-07-12 19:03:50] 🚨 会话管理器结果: {'session_id': 'session_1752318057', 'success': True}
[2025-07-12 19:03:50] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:03:50] 🚨 disable_vad参数值: False
[2025-07-12 19:08:48] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:08:48] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:08:48] 🚨 webui实例获取成功
[2025-07-12 19:08:48] 🚨 准备调用会话管理器
[2025-07-12 19:08:48] 🚨 会话管理器结果: {'session_id': 'session_1752318528', 'success': True}
[2025-07-12 19:08:48] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:08:48] 🚨 disable_vad参数值: False
[2025-07-12 19:08:48] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:08:48] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:08:48] 🚨 webui实例获取成功
[2025-07-12 19:08:48] 🚨 准备调用会话管理器
[2025-07-12 19:08:48] 🚨 会话管理器结果: {'session_id': 'session_1752318528', 'success': True}
[2025-07-12 19:08:48] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:08:48] 🚨 disable_vad参数值: False
[2025-07-12 19:08:48] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:08:48] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:08:48] 🚨 webui实例获取成功
[2025-07-12 19:08:48] 🚨 准备调用会话管理器
[2025-07-12 19:08:48] 🚨 会话管理器结果: {'session_id': 'session_1752318528', 'success': True}
[2025-07-12 19:08:48] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:08:48] 🚨 disable_vad参数值: False
[2025-07-12 19:23:46] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:23:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:23:46] 🚨 webui实例获取成功
[2025-07-12 19:23:46] 🚨 准备调用会话管理器
[2025-07-12 19:23:46] 🚨 会话管理器结果: {'session_id': 'session_1752319426', 'success': True}
[2025-07-12 19:23:46] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:23:46] 🚨 disable_vad参数值: False
[2025-07-12 19:23:46] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:23:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:23:46] 🚨 webui实例获取成功
[2025-07-12 19:23:46] 🚨 准备调用会话管理器
[2025-07-12 19:23:46] 🚨 会话管理器结果: {'session_id': 'session_1752319426', 'success': True}
[2025-07-12 19:23:46] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:23:46] 🚨 disable_vad参数值: False
[2025-07-12 19:23:46] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:23:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:23:46] 🚨 webui实例获取成功
[2025-07-12 19:23:46] 🚨 准备调用会话管理器
[2025-07-12 19:23:46] 🚨 会话管理器结果: {'session_id': 'session_1752319426', 'success': True}
[2025-07-12 19:23:46] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:23:46] 🚨 disable_vad参数值: False
[2025-07-12 19:29:10] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:29:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:29:10] 🚨 webui实例获取成功
[2025-07-12 19:29:10] 🚨 准备调用会话管理器
[2025-07-12 19:29:10] 🚨 会话管理器结果: {'session_id': 'session_1752319750', 'success': True}
[2025-07-12 19:29:10] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:29:10] 🚨 disable_vad参数值: False
[2025-07-12 19:29:10] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:29:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:29:10] 🚨 webui实例获取成功
[2025-07-12 19:29:10] 🚨 准备调用会话管理器
[2025-07-12 19:29:10] 🚨 会话管理器结果: {'session_id': 'session_1752319750', 'success': True}
[2025-07-12 19:29:10] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:29:10] 🚨 disable_vad参数值: False
[2025-07-12 19:29:10] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:29:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:29:10] 🚨 webui实例获取成功
[2025-07-12 19:29:10] 🚨 准备调用会话管理器
[2025-07-12 19:29:10] 🚨 会话管理器结果: {'session_id': 'session_1752319750', 'success': True}
[2025-07-12 19:29:10] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:29:10] 🚨 disable_vad参数值: False
[2025-07-12 19:35:36] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:35:36] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:35:36] 🚨 webui实例获取成功
[2025-07-12 19:35:36] 🚨 准备调用会话管理器
[2025-07-12 19:35:36] 🚨 会话管理器结果: {'session_id': 'session_1752320136', 'success': True}
[2025-07-12 19:35:36] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:35:36] 🚨 disable_vad参数值: False
[2025-07-12 19:42:31] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:42:31] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:42:31] 🚨 webui实例获取成功
[2025-07-12 19:42:31] 🚨 准备调用会话管理器
[2025-07-12 19:42:31] 🚨 会话管理器结果: {'session_id': 'session_1752320551', 'success': True}
[2025-07-12 19:42:31] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:42:31] 🚨 disable_vad参数值: False
[2025-07-12 19:44:20] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:44:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:44:20] 🚨 webui实例获取成功
[2025-07-12 19:44:20] 🚨 准备调用会话管理器
[2025-07-12 19:44:20] 🚨 会话管理器结果: {'session_id': 'session_1752320660', 'success': True}
[2025-07-12 19:44:20] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:44:20] 🚨 disable_vad参数值: False
[2025-07-12 19:44:58] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:44:58] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:44:58] 🚨 webui实例获取成功
[2025-07-12 19:44:58] 🚨 准备调用会话管理器
[2025-07-12 19:44:58] 🚨 会话管理器结果: {'session_id': 'session_1752320698', 'success': True}
[2025-07-12 19:44:58] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:44:58] 🚨 disable_vad参数值: False
[2025-07-12 19:46:08] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:46:08] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:46:08] 🚨 webui实例获取成功
[2025-07-12 19:46:08] 🚨 准备调用会话管理器
[2025-07-12 19:46:08] 🚨 会话管理器结果: {'session_id': 'session_1752320768', 'success': True}
[2025-07-12 19:46:08] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:46:08] 🚨 disable_vad参数值: False
[2025-07-12 19:53:24] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:53:24] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:53:24] 🚨 webui实例获取成功
[2025-07-12 19:53:24] 🚨 准备调用会话管理器
[2025-07-12 19:53:24] 🚨 会话管理器结果: {'session_id': 'session_1752321204', 'success': True}
[2025-07-12 19:53:24] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:53:24] 🚨 disable_vad参数值: False
[2025-07-12 19:53:49] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:53:49] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:53:49] 🚨 webui实例获取成功
[2025-07-12 19:53:49] 🚨 准备调用会话管理器
[2025-07-12 19:53:49] 🚨 会话管理器结果: {'session_id': 'session_1752321229', 'success': True}
[2025-07-12 19:53:49] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:53:49] 🚨 disable_vad参数值: False
[2025-07-12 19:54:21] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:54:21] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:54:21] 🚨 webui实例获取成功
[2025-07-12 19:54:21] 🚨 准备调用会话管理器
[2025-07-12 19:54:21] 🚨 会话管理器结果: {'session_id': 'session_1752321261', 'success': True}
[2025-07-12 19:54:21] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:54:21] 🚨 disable_vad参数值: False
[2025-07-12 19:55:06] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:55:06] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:55:06] 🚨 webui实例获取成功
[2025-07-12 19:55:06] 🚨 准备调用会话管理器
[2025-07-12 19:55:06] 🚨 会话管理器结果: {'session_id': 'session_1752321306', 'success': True}
[2025-07-12 19:55:06] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:55:06] 🚨 disable_vad参数值: False
[2025-07-12 19:56:03] 🚨 start_realtime_dialogue API被调用
[2025-07-12 19:56:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 19:56:03] 🚨 webui实例获取成功
[2025-07-12 19:56:03] 🚨 准备调用会话管理器
[2025-07-12 19:56:03] 🚨 会话管理器结果: {'session_id': 'session_1752321363', 'success': True}
[2025-07-12 19:56:03] 🚨 准备调用 start_realtime_conversation
[2025-07-12 19:56:03] 🚨 disable_vad参数值: False
[2025-07-12 20:03:41] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:03:41] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:03:41] 🚨 webui实例获取成功
[2025-07-12 20:03:41] 🚨 准备调用会话管理器
[2025-07-12 20:03:41] 🚨 会话管理器结果: {'session_id': 'session_1752321821', 'success': True}
[2025-07-12 20:03:41] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:03:41] 🚨 disable_vad参数值: False
[2025-07-12 20:03:44] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:03:44] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:03:44] 🚨 webui实例获取成功
[2025-07-12 20:03:44] 🚨 准备调用会话管理器
[2025-07-12 20:03:44] 🚨 会话管理器结果: {'session_id': 'session_1752321821', 'success': True}
[2025-07-12 20:03:44] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:03:44] 🚨 disable_vad参数值: False
[2025-07-12 20:03:44] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:03:44] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:03:44] 🚨 webui实例获取成功
[2025-07-12 20:03:44] 🚨 准备调用会话管理器
[2025-07-12 20:03:44] 🚨 会话管理器结果: {'session_id': 'session_1752321821', 'success': True}
[2025-07-12 20:03:44] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:03:44] 🚨 disable_vad参数值: False
[2025-07-12 20:03:44] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:03:44] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:03:44] 🚨 webui实例获取成功
[2025-07-12 20:03:44] 🚨 准备调用会话管理器
[2025-07-12 20:03:44] 🚨 会话管理器结果: {'session_id': 'session_1752321821', 'success': True}
[2025-07-12 20:03:44] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:03:44] 🚨 disable_vad参数值: False
[2025-07-12 20:04:03] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:04:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:04:03] 🚨 webui实例获取成功
[2025-07-12 20:04:03] 🚨 准备调用会话管理器
[2025-07-12 20:04:03] 🚨 会话管理器结果: {'session_id': 'session_1752321843', 'success': True}
[2025-07-12 20:04:03] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:04:03] 🚨 disable_vad参数值: False
[2025-07-12 20:09:37] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:09:37] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:09:37] 🚨 webui实例获取成功
[2025-07-12 20:09:37] 🚨 准备调用会话管理器
[2025-07-12 20:09:37] 🚨 会话管理器结果: {'session_id': 'session_1752322177', 'success': True}
[2025-07-12 20:09:37] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:09:37] 🚨 disable_vad参数值: False
[2025-07-12 20:17:48] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:17:48] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:17:48] 🚨 webui实例获取成功
[2025-07-12 20:17:48] 🚨 准备调用会话管理器
[2025-07-12 20:17:48] 🚨 会话管理器结果: {'session_id': 'session_1752322668', 'success': True}
[2025-07-12 20:17:48] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:17:48] 🚨 disable_vad参数值: False
[2025-07-12 20:19:11] 🚨 start_realtime_dialogue API被调用
[2025-07-12 20:19:11] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 20:19:11] 🚨 webui实例获取成功
[2025-07-12 20:19:11] 🚨 准备调用会话管理器
[2025-07-12 20:19:11] 🚨 会话管理器结果: {'session_id': 'session_1752322751', 'success': True}
[2025-07-12 20:19:11] 🚨 准备调用 start_realtime_conversation
[2025-07-12 20:19:11] 🚨 disable_vad参数值: False
[2025-07-12 22:00:10] 🚨 start_realtime_dialogue API被调用
[2025-07-12 22:00:10] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 22:00:10] 🚨 webui实例获取成功
[2025-07-12 22:00:10] 🚨 准备调用会话管理器
[2025-07-12 22:00:10] 🚨 会话管理器结果: {'session_id': 'session_1752328810', 'success': True}
[2025-07-12 22:00:10] 🚨 准备调用 start_realtime_conversation
[2025-07-12 22:00:10] 🚨 disable_vad参数值: False
[2025-07-12 22:01:03] 🚨 start_realtime_dialogue API被调用
[2025-07-12 22:01:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 22:01:03] 🚨 webui实例获取成功
[2025-07-12 22:01:03] 🚨 准备调用会话管理器
[2025-07-12 22:01:03] 🚨 会话管理器结果: {'session_id': 'session_1752328863', 'success': True}
[2025-07-12 22:01:03] 🚨 准备调用 start_realtime_conversation
[2025-07-12 22:01:03] 🚨 disable_vad参数值: False
[2025-07-12 22:08:44] 🚨 start_realtime_dialogue API被调用
[2025-07-12 22:08:44] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-12 22:08:44] 🚨 webui实例获取成功
[2025-07-12 22:08:44] 🚨 准备调用会话管理器
[2025-07-12 22:08:44] 🚨 会话管理器结果: {'session_id': 'session_1752329324', 'success': True}
[2025-07-12 22:08:44] 🚨 准备调用 start_realtime_conversation
[2025-07-12 22:08:44] 🚨 disable_vad参数值: False
[2025-07-13 06:53:17] 🚨 start_realtime_dialogue API被调用
[2025-07-13 06:53:17] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 06:53:17] 🚨 webui实例获取成功
[2025-07-13 06:53:17] 🚨 准备调用会话管理器
[2025-07-13 06:53:17] 🚨 会话管理器结果: {'session_id': 'session_1752360797', 'success': True}
[2025-07-13 06:53:17] 🚨 准备调用 start_realtime_conversation
[2025-07-13 06:53:17] 🚨 disable_vad参数值: False
[2025-07-13 06:53:19] 🚨 start_realtime_dialogue API被调用
[2025-07-13 06:53:19] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 06:53:19] 🚨 webui实例获取成功
[2025-07-13 06:53:19] 🚨 准备调用会话管理器
[2025-07-13 06:53:19] 🚨 会话管理器结果: {'session_id': 'session_1752360797', 'success': True}
[2025-07-13 06:53:19] 🚨 准备调用 start_realtime_conversation
[2025-07-13 06:53:19] 🚨 disable_vad参数值: False
[2025-07-13 06:53:19] 🚨 start_realtime_dialogue API被调用
[2025-07-13 06:53:19] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 06:53:19] 🚨 webui实例获取成功
[2025-07-13 06:53:19] 🚨 准备调用会话管理器
[2025-07-13 06:53:19] 🚨 会话管理器结果: {'session_id': 'session_1752360797', 'success': True}
[2025-07-13 06:53:19] 🚨 准备调用 start_realtime_conversation
[2025-07-13 06:53:19] 🚨 disable_vad参数值: False
[2025-07-13 06:55:34] 🚨 start_realtime_dialogue API被调用
[2025-07-13 06:55:34] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 06:55:34] 🚨 webui实例获取成功
[2025-07-13 06:55:34] 🚨 准备调用会话管理器
[2025-07-13 06:55:34] 🚨 会话管理器结果: {'session_id': 'session_1752360934', 'success': True}
[2025-07-13 06:55:34] 🚨 准备调用 start_realtime_conversation
[2025-07-13 06:55:34] 🚨 disable_vad参数值: False
[2025-07-13 06:57:45] 🚨 start_realtime_dialogue API被调用
[2025-07-13 06:57:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 06:57:45] 🚨 webui实例获取成功
[2025-07-13 06:57:45] 🚨 准备调用会话管理器
[2025-07-13 06:57:45] 🚨 会话管理器结果: {'session_id': 'session_1752361065', 'success': True}
[2025-07-13 06:57:45] 🚨 准备调用 start_realtime_conversation
[2025-07-13 06:57:45] 🚨 disable_vad参数值: False
[2025-07-13 07:12:40] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:12:40] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:12:40] 🚨 webui实例获取成功
[2025-07-13 07:12:40] 🚨 准备调用会话管理器
[2025-07-13 07:12:40] 🚨 会话管理器结果: {'session_id': 'session_1752361960', 'success': True}
[2025-07-13 07:12:40] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:12:40] 🚨 disable_vad参数值: False
[2025-07-13 07:25:43] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:25:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:25:43] 🚨 webui实例获取成功
[2025-07-13 07:25:43] 🚨 准备调用会话管理器
[2025-07-13 07:25:43] 🚨 会话管理器结果: {'session_id': 'session_1752362743', 'success': True}
[2025-07-13 07:25:43] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:25:43] 🚨 disable_vad参数值: False
[2025-07-13 07:25:54] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:25:54] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:25:54] 🚨 webui实例获取成功
[2025-07-13 07:25:54] 🚨 准备调用会话管理器
[2025-07-13 07:25:54] 🚨 会话管理器结果: {'session_id': 'session_1752362754', 'success': True}
[2025-07-13 07:25:54] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:25:54] 🚨 disable_vad参数值: False
[2025-07-13 07:26:30] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:26:30] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:26:30] 🚨 webui实例获取成功
[2025-07-13 07:26:30] 🚨 准备调用会话管理器
[2025-07-13 07:26:30] 🚨 会话管理器结果: {'session_id': 'session_1752362790', 'success': True}
[2025-07-13 07:26:30] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:26:30] 🚨 disable_vad参数值: False
[2025-07-13 07:27:02] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:27:02] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:27:02] 🚨 webui实例获取成功
[2025-07-13 07:27:02] 🚨 准备调用会话管理器
[2025-07-13 07:27:02] 🚨 会话管理器结果: {'session_id': 'session_1752362822', 'success': True}
[2025-07-13 07:27:02] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:27:02] 🚨 disable_vad参数值: False
[2025-07-13 07:27:12] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:27:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:27:12] 🚨 webui实例获取成功
[2025-07-13 07:27:12] 🚨 准备调用会话管理器
[2025-07-13 07:27:12] 🚨 会话管理器结果: {'session_id': 'session_1752362832', 'success': True}
[2025-07-13 07:27:12] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:27:12] 🚨 disable_vad参数值: False
[2025-07-13 07:27:20] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:27:20] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:27:20] 🚨 webui实例获取成功
[2025-07-13 07:27:20] 🚨 准备调用会话管理器
[2025-07-13 07:27:20] 🚨 会话管理器结果: {'session_id': 'session_1752362840', 'success': True}
[2025-07-13 07:27:20] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:27:20] 🚨 disable_vad参数值: False
[2025-07-13 07:27:35] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:27:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:27:35] 🚨 webui实例获取成功
[2025-07-13 07:27:35] 🚨 准备调用会话管理器
[2025-07-13 07:27:35] 🚨 会话管理器结果: {'session_id': 'session_1752362855', 'success': True}
[2025-07-13 07:27:35] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:27:35] 🚨 disable_vad参数值: False
[2025-07-13 07:28:00] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:28:00] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:28:00] 🚨 webui实例获取成功
[2025-07-13 07:28:00] 🚨 准备调用会话管理器
[2025-07-13 07:28:00] 🚨 会话管理器结果: {'session_id': 'session_1752362880', 'success': True}
[2025-07-13 07:28:00] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:28:00] 🚨 disable_vad参数值: False
[2025-07-13 07:28:35] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:28:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:28:35] 🚨 webui实例获取成功
[2025-07-13 07:28:35] 🚨 准备调用会话管理器
[2025-07-13 07:28:35] 🚨 会话管理器结果: {'session_id': 'session_1752362915', 'success': True}
[2025-07-13 07:28:35] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:28:35] 🚨 disable_vad参数值: False
[2025-07-13 07:28:43] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:28:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:28:43] 🚨 webui实例获取成功
[2025-07-13 07:28:43] 🚨 准备调用会话管理器
[2025-07-13 07:28:43] 🚨 会话管理器结果: {'session_id': 'session_1752362923', 'success': True}
[2025-07-13 07:28:43] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:28:43] 🚨 disable_vad参数值: False
[2025-07-13 07:28:52] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:28:52] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:28:52] 🚨 webui实例获取成功
[2025-07-13 07:28:52] 🚨 准备调用会话管理器
[2025-07-13 07:28:52] 🚨 会话管理器结果: {'session_id': 'session_1752362932', 'success': True}
[2025-07-13 07:28:52] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:28:52] 🚨 disable_vad参数值: False
[2025-07-13 07:35:27] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:35:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:35:27] 🚨 webui实例获取成功
[2025-07-13 07:35:27] 🚨 准备调用会话管理器
[2025-07-13 07:35:27] 🚨 会话管理器结果: {'session_id': 'session_1752363327', 'success': True}
[2025-07-13 07:35:27] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:35:27] 🚨 disable_vad参数值: False
[2025-07-13 07:38:37] 🚨 start_realtime_dialogue API被调用
[2025-07-13 07:38:37] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 07:38:37] 🚨 webui实例获取成功
[2025-07-13 07:38:37] 🚨 准备调用会话管理器
[2025-07-13 07:38:37] 🚨 会话管理器结果: {'session_id': 'session_1752363517', 'success': True}
[2025-07-13 07:38:37] 🚨 准备调用 start_realtime_conversation
[2025-07-13 07:38:37] 🚨 disable_vad参数值: False
[2025-07-13 08:08:45] 🚨 start_realtime_dialogue API被调用
[2025-07-13 08:08:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 08:08:45] 🚨 webui实例获取成功
[2025-07-13 08:08:45] 🚨 准备调用会话管理器
[2025-07-13 08:08:45] 🚨 会话管理器结果: {'session_id': 'session_1752365325', 'success': True}
[2025-07-13 08:08:45] 🚨 准备调用 start_realtime_conversation
[2025-07-13 08:08:45] 🚨 disable_vad参数值: False
[2025-07-13 08:47:33] 🚨 start_realtime_dialogue API被调用
[2025-07-13 08:47:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 08:47:33] 🚨 webui实例获取成功
[2025-07-13 08:47:33] 🚨 准备调用会话管理器
[2025-07-13 08:47:33] 🚨 会话管理器结果: {'session_id': 'session_1752367653', 'success': True}
[2025-07-13 08:47:33] 🚨 准备调用 start_realtime_conversation
[2025-07-13 08:47:33] 🚨 disable_vad参数值: False
[2025-07-13 08:49:47] 🚨 start_realtime_dialogue API被调用
[2025-07-13 08:49:47] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 08:49:47] 🚨 webui实例获取成功
[2025-07-13 08:49:47] 🚨 准备调用会话管理器
[2025-07-13 08:49:47] 🚨 会话管理器结果: {'session_id': 'session_1752367787', 'success': True}
[2025-07-13 08:49:47] 🚨 准备调用 start_realtime_conversation
[2025-07-13 08:49:47] 🚨 disable_vad参数值: False
[2025-07-13 08:52:22] 🚨 start_realtime_dialogue API被调用
[2025-07-13 08:52:22] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 08:52:22] 🚨 webui实例获取成功
[2025-07-13 08:52:22] 🚨 准备调用会话管理器
[2025-07-13 08:52:22] 🚨 会话管理器结果: {'session_id': 'session_1752367942', 'success': True}
[2025-07-13 08:52:22] 🚨 准备调用 start_realtime_conversation
[2025-07-13 08:52:22] 🚨 disable_vad参数值: False
[2025-07-13 08:54:24] 🚨 start_realtime_dialogue API被调用
[2025-07-13 08:54:24] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 08:54:24] 🚨 webui实例获取成功
[2025-07-13 08:54:24] 🚨 准备调用会话管理器
[2025-07-13 08:54:24] 🚨 会话管理器结果: {'session_id': 'session_1752368064', 'success': True}
[2025-07-13 08:54:24] 🚨 准备调用 start_realtime_conversation
[2025-07-13 08:54:24] 🚨 disable_vad参数值: False
[2025-07-13 09:03:58] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:03:58] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:03:58] 🚨 webui实例获取成功
[2025-07-13 09:03:58] 🚨 准备调用会话管理器
[2025-07-13 09:03:58] 🚨 会话管理器结果: {'session_id': 'session_1752368638', 'success': True}
[2025-07-13 09:03:58] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:03:58] 🚨 disable_vad参数值: False
[2025-07-13 09:04:35] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:04:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:04:35] 🚨 webui实例获取成功
[2025-07-13 09:04:35] 🚨 准备调用会话管理器
[2025-07-13 09:04:35] 🚨 会话管理器结果: {'session_id': 'session_1752368675', 'success': True}
[2025-07-13 09:04:35] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:04:35] 🚨 disable_vad参数值: False
[2025-07-13 09:07:11] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:07:11] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:07:11] 🚨 webui实例获取成功
[2025-07-13 09:07:11] 🚨 准备调用会话管理器
[2025-07-13 09:07:11] 🚨 会话管理器结果: {'session_id': 'session_1752368831', 'success': True}
[2025-07-13 09:07:11] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:07:11] 🚨 disable_vad参数值: False
[2025-07-13 09:09:16] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:09:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:09:16] 🚨 webui实例获取成功
[2025-07-13 09:09:16] 🚨 准备调用会话管理器
[2025-07-13 09:09:16] 🚨 会话管理器结果: {'session_id': 'session_1752368956', 'success': True}
[2025-07-13 09:09:16] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:09:16] 🚨 disable_vad参数值: False
[2025-07-13 09:09:46] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:09:46] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:09:46] 🚨 webui实例获取成功
[2025-07-13 09:09:46] 🚨 准备调用会话管理器
[2025-07-13 09:09:46] 🚨 会话管理器结果: {'session_id': 'session_1752368986', 'success': True}
[2025-07-13 09:09:46] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:09:46] 🚨 disable_vad参数值: False
[2025-07-13 09:12:39] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:12:39] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:12:39] 🚨 webui实例获取成功
[2025-07-13 09:12:39] 🚨 准备调用会话管理器
[2025-07-13 09:12:39] 🚨 会话管理器结果: {'session_id': 'session_1752369159', 'success': True}
[2025-07-13 09:12:39] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:12:39] 🚨 disable_vad参数值: False
[2025-07-13 09:12:50] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:12:50] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:12:50] 🚨 webui实例获取成功
[2025-07-13 09:12:50] 🚨 准备调用会话管理器
[2025-07-13 09:12:50] 🚨 会话管理器结果: {'session_id': 'session_1752369170', 'success': True}
[2025-07-13 09:12:50] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:12:50] 🚨 disable_vad参数值: False
[2025-07-13 09:13:33] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:13:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:13:33] 🚨 webui实例获取成功
[2025-07-13 09:13:33] 🚨 准备调用会话管理器
[2025-07-13 09:13:33] 🚨 会话管理器结果: {'session_id': 'session_1752369213', 'success': True}
[2025-07-13 09:13:33] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:13:33] 🚨 disable_vad参数值: False
[2025-07-13 09:14:35] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:14:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:14:35] 🚨 webui实例获取成功
[2025-07-13 09:14:35] 🚨 准备调用会话管理器
[2025-07-13 09:14:35] 🚨 会话管理器结果: {'session_id': 'session_1752369275', 'success': True}
[2025-07-13 09:14:35] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:14:35] 🚨 disable_vad参数值: False
[2025-07-13 09:16:44] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:16:44] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:16:44] 🚨 webui实例获取成功
[2025-07-13 09:16:44] 🚨 准备调用会话管理器
[2025-07-13 09:16:44] 🚨 会话管理器结果: {'session_id': 'session_1752369404', 'success': True}
[2025-07-13 09:16:44] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:16:44] 🚨 disable_vad参数值: False
[2025-07-13 09:21:02] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:21:02] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:21:02] 🚨 webui实例获取成功
[2025-07-13 09:21:02] 🚨 准备调用会话管理器
[2025-07-13 09:21:02] 🚨 会话管理器结果: {'session_id': 'session_1752369662', 'success': True}
[2025-07-13 09:21:02] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:21:02] 🚨 disable_vad参数值: False
[2025-07-13 09:26:12] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:26:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:26:12] 🚨 webui实例获取成功
[2025-07-13 09:26:12] 🚨 准备调用会话管理器
[2025-07-13 09:26:12] 🚨 会话管理器结果: {'session_id': 'session_1752369972', 'success': True}
[2025-07-13 09:26:12] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:26:12] 🚨 disable_vad参数值: False
[2025-07-13 09:26:26] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:26:26] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:26:26] 🚨 webui实例获取成功
[2025-07-13 09:26:26] 🚨 准备调用会话管理器
[2025-07-13 09:26:26] 🚨 会话管理器结果: {'session_id': 'session_1752369986', 'success': True}
[2025-07-13 09:26:26] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:26:26] 🚨 disable_vad参数值: False
[2025-07-13 09:26:50] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:26:50] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:26:50] 🚨 webui实例获取成功
[2025-07-13 09:26:50] 🚨 准备调用会话管理器
[2025-07-13 09:26:50] 🚨 会话管理器结果: {'session_id': 'session_1752370010', 'success': True}
[2025-07-13 09:26:50] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:26:50] 🚨 disable_vad参数值: False
[2025-07-13 09:28:02] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:28:02] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:28:02] 🚨 webui实例获取成功
[2025-07-13 09:28:02] 🚨 准备调用会话管理器
[2025-07-13 09:28:02] 🚨 会话管理器结果: {'session_id': 'session_1752370082', 'success': True}
[2025-07-13 09:28:02] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:28:02] 🚨 disable_vad参数值: False
[2025-07-13 09:30:57] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:30:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:30:57] 🚨 webui实例获取成功
[2025-07-13 09:30:57] 🚨 准备调用会话管理器
[2025-07-13 09:30:57] 🚨 会话管理器结果: {'session_id': 'session_1752370257', 'success': True}
[2025-07-13 09:30:57] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:30:57] 🚨 disable_vad参数值: False
[2025-07-13 09:42:13] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:42:13] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:42:13] 🚨 webui实例获取成功
[2025-07-13 09:42:13] 🚨 准备调用会话管理器
[2025-07-13 09:42:13] 🚨 会话管理器结果: {'session_id': 'session_1752370933', 'success': True}
[2025-07-13 09:42:13] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:42:13] 🚨 disable_vad参数值: False
[2025-07-13 09:42:39] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:42:39] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:42:39] 🚨 webui实例获取成功
[2025-07-13 09:42:39] 🚨 准备调用会话管理器
[2025-07-13 09:42:39] 🚨 会话管理器结果: {'session_id': 'session_1752370959', 'success': True}
[2025-07-13 09:42:39] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:42:39] 🚨 disable_vad参数值: False
[2025-07-13 09:43:39] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:43:39] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:43:39] 🚨 webui实例获取成功
[2025-07-13 09:43:39] 🚨 准备调用会话管理器
[2025-07-13 09:43:39] 🚨 会话管理器结果: {'session_id': 'session_1752371019', 'success': True}
[2025-07-13 09:43:39] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:43:39] 🚨 disable_vad参数值: False
[2025-07-13 09:44:28] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:44:28] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:44:28] 🚨 webui实例获取成功
[2025-07-13 09:44:28] 🚨 准备调用会话管理器
[2025-07-13 09:44:28] 🚨 会话管理器结果: {'session_id': 'session_1752371068', 'success': True}
[2025-07-13 09:44:28] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:44:28] 🚨 disable_vad参数值: False
[2025-07-13 09:44:53] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:44:53] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:44:53] 🚨 webui实例获取成功
[2025-07-13 09:44:53] 🚨 准备调用会话管理器
[2025-07-13 09:44:53] 🚨 会话管理器结果: {'session_id': 'session_1752371093', 'success': True}
[2025-07-13 09:44:53] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:44:53] 🚨 disable_vad参数值: False
[2025-07-13 09:45:06] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:45:06] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:45:06] 🚨 webui实例获取成功
[2025-07-13 09:45:06] 🚨 准备调用会话管理器
[2025-07-13 09:45:06] 🚨 会话管理器结果: {'session_id': 'session_1752371106', 'success': True}
[2025-07-13 09:45:06] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:45:06] 🚨 disable_vad参数值: False
[2025-07-13 09:51:16] 🚨 start_realtime_dialogue API被调用
[2025-07-13 09:51:16] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 09:51:16] 🚨 webui实例获取成功
[2025-07-13 09:51:16] 🚨 准备调用会话管理器
[2025-07-13 09:51:16] 🚨 会话管理器结果: {'session_id': 'session_1752371476', 'success': True}
[2025-07-13 09:51:16] 🚨 准备调用 start_realtime_conversation
[2025-07-13 09:51:16] 🚨 disable_vad参数值: False
[2025-07-13 10:00:12] 🚨 start_realtime_dialogue API被调用
[2025-07-13 10:00:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 10:00:12] 🚨 webui实例获取成功
[2025-07-13 10:00:12] 🚨 准备调用会话管理器
[2025-07-13 10:00:12] 🚨 会话管理器结果: {'session_id': 'session_1752372012', 'success': True}
[2025-07-13 10:00:12] 🚨 准备调用 start_realtime_conversation
[2025-07-13 10:00:12] 🚨 disable_vad参数值: False
[2025-07-13 10:09:40] 🚨 start_realtime_dialogue API被调用
[2025-07-13 10:09:40] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 10:09:40] 🚨 webui实例获取成功
[2025-07-13 10:09:40] 🚨 准备调用会话管理器
[2025-07-13 10:09:40] 🚨 会话管理器结果: {'session_id': 'session_1752372580', 'success': True}
[2025-07-13 10:09:40] 🚨 准备调用 start_realtime_conversation
[2025-07-13 10:09:40] 🚨 disable_vad参数值: False
[2025-07-13 10:11:58] 🚨 start_realtime_dialogue API被调用
[2025-07-13 10:11:58] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 10:11:58] 🚨 webui实例获取成功
[2025-07-13 10:11:58] 🚨 准备调用会话管理器
[2025-07-13 10:11:58] 🚨 会话管理器结果: {'session_id': 'session_1752372718', 'success': True}
[2025-07-13 10:11:58] 🚨 准备调用 start_realtime_conversation
[2025-07-13 10:11:58] 🚨 disable_vad参数值: False
[2025-07-13 10:20:11] 🚨 start_realtime_dialogue API被调用
[2025-07-13 10:20:11] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-13 10:20:11] 🚨 webui实例获取成功
[2025-07-13 10:20:11] 🚨 准备调用会话管理器
[2025-07-13 10:20:11] 🚨 会话管理器结果: {'session_id': 'session_1752373211', 'success': True}
[2025-07-13 10:20:11] 🚨 准备调用 start_realtime_conversation
[2025-07-13 10:20:11] 🚨 disable_vad参数值: False
[2025-07-14 00:23:37] 🚨 start_realtime_dialogue API被调用
[2025-07-14 00:23:37] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 00:23:37] 🚨 webui实例获取成功
[2025-07-14 00:23:37] 🚨 准备调用会话管理器
[2025-07-14 00:23:37] 🚨 会话管理器结果: {'session_id': 'session_1752423817', 'success': True}
[2025-07-14 00:23:37] 🚨 准备调用 start_realtime_conversation
[2025-07-14 00:23:37] 🚨 disable_vad参数值: True
[2025-07-14 00:24:51] 🚨 start_realtime_dialogue API被调用
[2025-07-14 00:24:51] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-14 00:24:51] 🚨 webui实例获取成功
[2025-07-14 00:24:51] 🚨 准备调用会话管理器
[2025-07-14 00:24:51] 🚨 会话管理器结果: {'session_id': 'session_1752423891', 'success': True}
[2025-07-14 00:24:51] 🚨 准备调用 start_realtime_conversation
[2025-07-14 00:24:51] 🚨 disable_vad参数值: False
[2025-07-14 03:08:47] 🚨 start_realtime_dialogue API被调用
[2025-07-14 03:08:47] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 03:08:47] 🚨 webui实例获取成功
[2025-07-14 03:08:47] 🚨 准备调用会话管理器
[2025-07-14 03:08:47] 🚨 会话管理器结果: {'session_id': 'session_1752433727', 'success': True}
[2025-07-14 03:08:47] 🚨 准备调用 start_realtime_conversation
[2025-07-14 03:08:47] 🚨 disable_vad参数值: True
[2025-07-14 04:06:36] 🚨 start_realtime_dialogue API被调用
[2025-07-14 04:06:36] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 04:06:36] 🚨 webui实例获取成功
[2025-07-14 04:06:36] 🚨 准备调用会话管理器
[2025-07-14 04:06:36] 🚨 会话管理器结果: {'session_id': 'session_1752437196', 'success': True}
[2025-07-14 04:06:36] 🚨 准备调用 start_realtime_conversation
[2025-07-14 04:06:36] 🚨 disable_vad参数值: True
[2025-07-14 04:12:51] 🚨 start_realtime_dialogue API被调用
[2025-07-14 04:12:51] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 04:12:51] 🚨 webui实例获取成功
[2025-07-14 04:12:51] 🚨 准备调用会话管理器
[2025-07-14 04:12:51] 🚨 会话管理器结果: {'session_id': 'session_1752437571', 'success': True}
[2025-07-14 04:12:51] 🚨 准备调用 start_realtime_conversation
[2025-07-14 04:12:51] 🚨 disable_vad参数值: True
[2025-07-14 04:20:33] 🚨 start_realtime_dialogue API被调用
[2025-07-14 04:20:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-14 04:20:33] 🚨 webui实例获取成功
[2025-07-14 04:20:33] 🚨 准备调用会话管理器
[2025-07-14 04:20:33] 🚨 会话管理器结果: {'session_id': 'session_1752438033', 'success': True}
[2025-07-14 04:20:33] 🚨 准备调用 start_realtime_conversation
[2025-07-14 04:20:33] 🚨 disable_vad参数值: False
[2025-07-14 05:45:19] 🚨 start_realtime_dialogue API被调用
[2025-07-14 05:45:19] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 05:45:19] 🚨 webui实例获取成功
[2025-07-14 05:45:19] 🚨 准备调用会话管理器
[2025-07-14 05:45:19] 🚨 会话管理器结果: {'session_id': 'session_1752443119', 'success': True}
[2025-07-14 05:45:19] 🚨 准备调用 start_realtime_conversation
[2025-07-14 05:45:19] 🚨 disable_vad参数值: True
[2025-07-14 05:59:02] 🚨 start_realtime_dialogue API被调用
[2025-07-14 05:59:02] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 05:59:02] 🚨 webui实例获取成功
[2025-07-14 05:59:02] 🚨 准备调用会话管理器
[2025-07-14 05:59:02] 🚨 会话管理器结果: {'session_id': 'session_1752443942', 'success': True}
[2025-07-14 05:59:02] 🚨 准备调用 start_realtime_conversation
[2025-07-14 05:59:02] 🚨 disable_vad参数值: True
[2025-07-14 06:39:54] 🚨 start_realtime_dialogue API被调用
[2025-07-14 06:39:54] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 06:39:54] 🚨 webui实例获取成功
[2025-07-14 06:39:54] 🚨 准备调用会话管理器
[2025-07-14 06:39:54] 🚨 会话管理器结果: {'session_id': 'session_1752446394', 'success': True}
[2025-07-14 06:39:54] 🚨 准备调用 start_realtime_conversation
[2025-07-14 06:39:54] 🚨 disable_vad参数值: True
[2025-07-14 06:40:28] 🚨 start_realtime_dialogue API被调用
[2025-07-14 06:40:28] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 06:40:28] 🚨 webui实例获取成功
[2025-07-14 06:40:28] 🚨 准备调用会话管理器
[2025-07-14 06:40:28] 🚨 会话管理器结果: {'session_id': 'session_1752446394', 'success': True}
[2025-07-14 06:40:28] 🚨 准备调用 start_realtime_conversation
[2025-07-14 06:40:28] 🚨 disable_vad参数值: True
[2025-07-14 06:40:28] 🚨 start_realtime_dialogue API被调用
[2025-07-14 06:40:28] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 06:40:28] 🚨 webui实例获取成功
[2025-07-14 06:40:28] 🚨 准备调用会话管理器
[2025-07-14 06:40:28] 🚨 会话管理器结果: {'session_id': 'session_1752446394', 'success': True}
[2025-07-14 06:40:28] 🚨 准备调用 start_realtime_conversation
[2025-07-14 06:40:28] 🚨 disable_vad参数值: True
[2025-07-14 06:41:15] 🚨 start_realtime_dialogue API被调用
[2025-07-14 06:41:15] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 06:41:15] 🚨 webui实例获取成功
[2025-07-14 06:41:15] 🚨 准备调用会话管理器
[2025-07-14 06:41:15] 🚨 会话管理器结果: {'session_id': 'session_1752446475', 'success': True}
[2025-07-14 06:41:15] 🚨 准备调用 start_realtime_conversation
[2025-07-14 06:41:15] 🚨 disable_vad参数值: True
[2025-07-14 07:47:09] 🚨 start_realtime_dialogue API被调用
[2025-07-14 07:47:09] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 07:47:09] 🚨 webui实例获取成功
[2025-07-14 07:47:09] 🚨 准备调用会话管理器
[2025-07-14 07:47:09] 🚨 会话管理器结果: {'session_id': 'session_1752450429', 'success': True}
[2025-07-14 07:47:09] 🚨 准备调用 start_realtime_conversation
[2025-07-14 07:47:09] 🚨 disable_vad参数值: True
[2025-07-14 09:32:27] 🚨 start_realtime_dialogue API被调用
[2025-07-14 09:32:27] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 09:32:27] 🚨 webui实例获取成功
[2025-07-14 09:32:27] 🚨 准备调用会话管理器
[2025-07-14 09:32:27] 🚨 会话管理器结果: {'session_id': 'session_1752456747', 'success': True}
[2025-07-14 09:32:27] 🚨 准备调用 start_realtime_conversation
[2025-07-14 09:32:27] 🚨 disable_vad参数值: True
[2025-07-14 10:42:49] 🚨 start_realtime_dialogue API被调用
[2025-07-14 10:42:49] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 10:42:49] 🚨 webui实例获取成功
[2025-07-14 10:42:49] 🚨 准备调用会话管理器
[2025-07-14 10:42:49] 🚨 会话管理器结果: {'session_id': 'session_1752460969', 'success': True}
[2025-07-14 10:42:49] 🚨 准备调用 start_realtime_conversation
[2025-07-14 10:42:49] 🚨 disable_vad参数值: True
[2025-07-14 10:42:55] 🚨 start_realtime_dialogue API被调用
[2025-07-14 10:42:55] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 10:42:55] 🚨 webui实例获取成功
[2025-07-14 10:42:55] 🚨 准备调用会话管理器
[2025-07-14 10:42:55] 🚨 会话管理器结果: {'session_id': 'session_1752460969', 'success': True}
[2025-07-14 10:42:55] 🚨 准备调用 start_realtime_conversation
[2025-07-14 10:42:55] 🚨 disable_vad参数值: True
[2025-07-14 10:42:56] 🚨 start_realtime_dialogue API被调用
[2025-07-14 10:42:56] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 10:42:56] 🚨 webui实例获取成功
[2025-07-14 10:42:56] 🚨 准备调用会话管理器
[2025-07-14 10:42:56] 🚨 会话管理器结果: {'session_id': 'session_1752460969', 'success': True}
[2025-07-14 10:42:56] 🚨 准备调用 start_realtime_conversation
[2025-07-14 10:42:56] 🚨 disable_vad参数值: True
[2025-07-14 10:42:56] 🚨 start_realtime_dialogue API被调用
[2025-07-14 10:42:56] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 10:42:56] 🚨 webui实例获取成功
[2025-07-14 10:42:56] 🚨 准备调用会话管理器
[2025-07-14 10:42:56] 🚨 会话管理器结果: {'session_id': 'session_1752460969', 'success': True}
[2025-07-14 10:42:56] 🚨 准备调用 start_realtime_conversation
[2025-07-14 10:42:56] 🚨 disable_vad参数值: True
[2025-07-14 11:29:18] 🚨 start_realtime_dialogue API被调用
[2025-07-14 11:29:18] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-14 11:29:18] 🚨 webui实例获取成功
[2025-07-14 11:29:18] 🚨 准备调用会话管理器
[2025-07-14 11:29:18] 🚨 会话管理器结果: {'session_id': 'session_1752463758', 'success': True}
[2025-07-14 11:29:18] 🚨 准备调用 start_realtime_conversation
[2025-07-14 11:29:18] 🚨 disable_vad参数值: False
[2025-07-14 11:31:12] 🚨 start_realtime_dialogue API被调用
[2025-07-14 11:31:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-14 11:31:12] 🚨 webui实例获取成功
[2025-07-14 11:31:12] 🚨 准备调用会话管理器
[2025-07-14 11:31:12] 🚨 会话管理器结果: {'session_id': 'session_1752463872', 'success': True}
[2025-07-14 11:31:12] 🚨 准备调用 start_realtime_conversation
[2025-07-14 11:31:12] 🚨 disable_vad参数值: False
[2025-07-14 11:32:17] 🚨 start_realtime_dialogue API被调用
[2025-07-14 11:32:17] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 11:32:17] 🚨 webui实例获取成功
[2025-07-14 11:32:17] 🚨 准备调用会话管理器
[2025-07-14 11:32:17] 🚨 会话管理器结果: {'session_id': 'session_1752463937', 'success': True}
[2025-07-14 11:32:17] 🚨 准备调用 start_realtime_conversation
[2025-07-14 11:32:17] 🚨 disable_vad参数值: True
[2025-07-14 11:35:48] 🚨 start_realtime_dialogue API被调用
[2025-07-14 11:35:48] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 11:35:48] 🚨 webui实例获取成功
[2025-07-14 11:35:48] 🚨 准备调用会话管理器
[2025-07-14 11:35:48] 🚨 会话管理器结果: {'session_id': 'session_1752464148', 'success': True}
[2025-07-14 11:35:48] 🚨 准备调用 start_realtime_conversation
[2025-07-14 11:35:48] 🚨 disable_vad参数值: True
[2025-07-14 11:41:05] 🚨 start_realtime_dialogue API被调用
[2025-07-14 11:41:05] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-14 11:41:05] 🚨 webui实例获取成功
[2025-07-14 11:41:05] 🚨 准备调用会话管理器
[2025-07-14 11:41:05] 🚨 会话管理器结果: {'session_id': 'session_1752464465', 'success': True}
[2025-07-14 11:41:05] 🚨 准备调用 start_realtime_conversation
[2025-07-14 11:41:05] 🚨 disable_vad参数值: True
[2025-07-14 12:11:43] 🚨 start_realtime_dialogue API被调用
[2025-07-14 12:11:43] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 12:11:43] 🚨 webui实例获取成功
[2025-07-14 12:11:43] 🚨 准备调用会话管理器
[2025-07-14 12:11:43] 🚨 会话管理器结果: {'session_id': 'session_1752466303', 'success': True}
[2025-07-14 12:11:43] 🚨 准备调用 start_realtime_conversation
[2025-07-14 12:11:43] 🚨 disable_vad参数值: True
[2025-07-14 12:33:27] 🚨 start_realtime_dialogue API被调用
[2025-07-14 12:33:27] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-14 12:33:27] 🚨 webui实例获取成功
[2025-07-14 12:33:27] 🚨 准备调用会话管理器
[2025-07-14 12:33:27] 🚨 会话管理器结果: {'session_id': 'session_1752467607', 'success': True}
[2025-07-14 12:33:27] 🚨 准备调用 start_realtime_conversation
[2025-07-14 12:33:27] 🚨 disable_vad参数值: True
[2025-07-14 12:44:54] 🚨 start_realtime_dialogue API被调用
[2025-07-14 12:44:54] 🚨 参数: disableVAD=False, mode=realtime
[2025-07-14 12:44:54] 🚨 webui实例获取成功
[2025-07-14 12:44:54] 🚨 准备调用会话管理器
[2025-07-14 12:44:54] 🚨 会话管理器结果: {'session_id': 'session_1752468294', 'success': True}
[2025-07-14 12:44:54] 🚨 准备调用 start_realtime_conversation
[2025-07-14 12:44:54] 🚨 disable_vad参数值: False
[2025-07-14 12:51:00] 🚨 start_realtime_dialogue API被调用
[2025-07-14 12:51:00] 🚨 参数: disableVAD=False, mode=realtime
[2025-07-14 12:51:00] 🚨 webui实例获取成功
[2025-07-14 12:51:00] 🚨 准备调用会话管理器
[2025-07-14 12:51:00] 🚨 会话管理器结果: {'session_id': 'session_1752468660', 'success': True}
[2025-07-14 12:51:00] 🚨 准备调用 start_realtime_conversation
[2025-07-14 12:51:00] 🚨 disable_vad参数值: False
[2025-07-14 13:08:35] 🚨 start_realtime_dialogue API被调用
[2025-07-14 13:08:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-14 13:08:35] 🚨 webui实例获取成功
[2025-07-14 13:08:35] 🚨 准备调用会话管理器
[2025-07-14 13:08:35] 🚨 会话管理器结果: {'session_id': 'session_1752469715', 'success': True}
[2025-07-14 13:08:35] 🚨 准备调用 start_realtime_conversation
[2025-07-14 13:08:35] 🚨 disable_vad参数值: False
[2025-07-14 13:09:23] 🚨 start_realtime_dialogue API被调用
[2025-07-14 13:09:23] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-14 13:09:23] 🚨 webui实例获取成功
[2025-07-14 13:09:23] 🚨 准备调用会话管理器
[2025-07-14 13:09:23] 🚨 会话管理器结果: {'session_id': 'session_1752469763', 'success': True}
[2025-07-14 13:09:23] 🚨 准备调用 start_realtime_conversation
[2025-07-14 13:09:23] 🚨 disable_vad参数值: False
[2025-07-14 13:10:35] 🚨 start_realtime_dialogue API被调用
[2025-07-14 13:10:35] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-14 13:10:35] 🚨 webui实例获取成功
[2025-07-14 13:10:35] 🚨 准备调用会话管理器
[2025-07-14 13:10:35] 🚨 会话管理器结果: {'session_id': 'session_1752469835', 'success': True}
[2025-07-14 13:10:35] 🚨 准备调用 start_realtime_conversation
[2025-07-14 13:10:35] 🚨 disable_vad参数值: False
[2025-07-14 13:11:42] 🚨 start_realtime_dialogue API被调用
[2025-07-14 13:11:42] 🚨 参数: disableVAD=False, mode=realtime
[2025-07-14 13:11:42] 🚨 webui实例获取成功
[2025-07-14 13:11:42] 🚨 准备调用会话管理器
[2025-07-14 13:11:42] 🚨 会话管理器结果: {'session_id': 'session_1752469902', 'success': True}
[2025-07-14 13:11:42] 🚨 准备调用 start_realtime_conversation
[2025-07-14 13:11:42] 🚨 disable_vad参数值: False
[2025-07-19 11:09:27] 🚨 start_realtime_dialogue API被调用
[2025-07-19 11:09:27] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 11:09:27] 🚨 webui实例获取成功
[2025-07-19 11:09:27] 🚨 准备调用会话管理器
[2025-07-19 11:09:27] 🚨 会话管理器结果: {'session_id': 'session_1752894567', 'success': True}
[2025-07-19 11:09:27] 🚨 准备调用 start_realtime_conversation
[2025-07-19 11:09:27] 🚨 disable_vad参数值: False
[2025-07-19 11:12:43] 🚨 start_realtime_dialogue API被调用
[2025-07-19 11:12:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 11:12:43] 🚨 webui实例获取成功
[2025-07-19 11:12:43] 🚨 准备调用会话管理器
[2025-07-19 11:12:43] 🚨 会话管理器结果: {'session_id': 'session_1752894763', 'success': True}
[2025-07-19 11:12:43] 🚨 准备调用 start_realtime_conversation
[2025-07-19 11:12:43] 🚨 disable_vad参数值: False
[2025-07-19 12:28:26] 🚨 start_realtime_dialogue API被调用
[2025-07-19 12:28:26] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-19 12:28:26] 🚨 webui实例获取成功
[2025-07-19 12:28:26] 🚨 准备调用会话管理器
[2025-07-19 12:28:26] 🚨 会话管理器结果: {'session_id': 'session_1752899306', 'success': True}
[2025-07-19 12:28:26] 🚨 准备调用 start_realtime_conversation
[2025-07-19 12:28:26] 🚨 disable_vad参数值: True
[2025-07-19 12:41:22] 🚨 start_realtime_dialogue API被调用
[2025-07-19 12:41:22] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-19 12:41:22] 🚨 webui实例获取成功
[2025-07-19 12:41:22] 🚨 准备调用会话管理器
[2025-07-19 12:41:22] 🚨 会话管理器结果: {'session_id': 'session_1752900082', 'success': True}
[2025-07-19 12:41:22] 🚨 准备调用 start_realtime_conversation
[2025-07-19 12:41:22] 🚨 disable_vad参数值: True
[2025-07-19 13:22:19] 🚨 start_realtime_dialogue API被调用
[2025-07-19 13:22:19] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-19 13:22:19] 🚨 webui实例获取成功
[2025-07-19 13:22:19] 🚨 准备调用会话管理器
[2025-07-19 13:22:19] 🚨 会话管理器结果: {'session_id': 'session_1752902539', 'success': True}
[2025-07-19 13:22:19] 🚨 准备调用 start_realtime_conversation
[2025-07-19 13:22:19] 🚨 disable_vad参数值: True
[2025-07-19 16:21:03] 🚨 start_realtime_dialogue API被调用
[2025-07-19 16:21:03] 🚨 参数: disableVAD=True, mode=realtime
[2025-07-19 16:21:03] 🚨 webui实例获取成功
[2025-07-19 16:21:03] 🚨 准备调用会话管理器
[2025-07-19 16:21:03] 🚨 会话管理器结果: {'session_id': 'session_1752913263', 'success': True}
[2025-07-19 16:21:03] 🚨 准备调用 start_realtime_conversation
[2025-07-19 16:21:03] 🚨 disable_vad参数值: True
[2025-07-19 17:40:57] 🚨 start_realtime_dialogue API被调用
[2025-07-19 17:40:57] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 17:40:57] 🚨 webui实例获取成功
[2025-07-19 17:40:57] 🚨 准备调用会话管理器
[2025-07-19 17:40:57] 🚨 会话管理器结果: {'session_id': 'session_1752918057', 'success': True}
[2025-07-19 17:40:57] 🚨 准备调用 start_realtime_conversation
[2025-07-19 17:40:57] 🚨 disable_vad参数值: False
[2025-07-19 17:43:15] 🚨 start_realtime_dialogue API被调用
[2025-07-19 17:43:15] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 17:43:15] 🚨 webui实例获取成功
[2025-07-19 17:43:15] 🚨 准备调用会话管理器
[2025-07-19 17:43:15] 🚨 会话管理器结果: {'session_id': 'session_1752918195', 'success': True}
[2025-07-19 17:43:15] 🚨 准备调用 start_realtime_conversation
[2025-07-19 17:43:15] 🚨 disable_vad参数值: False
[2025-07-19 19:03:28] 🚨 start_realtime_dialogue API被调用
[2025-07-19 19:03:28] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 19:03:28] 🚨 webui实例获取成功
[2025-07-19 19:03:28] 🚨 准备调用会话管理器
[2025-07-19 19:03:28] 🚨 会话管理器结果: {'session_id': 'session_1752923008', 'success': True}
[2025-07-19 19:03:28] 🚨 准备调用 start_realtime_conversation
[2025-07-19 19:03:28] 🚨 disable_vad参数值: False
[2025-07-19 19:03:38] 🚨 start_realtime_dialogue API被调用
[2025-07-19 19:03:38] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 19:03:38] 🚨 webui实例获取成功
[2025-07-19 19:03:38] 🚨 准备调用会话管理器
[2025-07-19 19:03:38] 🚨 会话管理器结果: {'session_id': 'session_1752923008', 'success': True}
[2025-07-19 19:03:38] 🚨 准备调用 start_realtime_conversation
[2025-07-19 19:03:38] 🚨 disable_vad参数值: False
[2025-07-19 21:04:59] 🚨 start_realtime_dialogue API被调用
[2025-07-19 21:04:59] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 21:04:59] 🚨 webui实例获取成功
[2025-07-19 21:04:59] 🚨 准备调用会话管理器
[2025-07-19 21:04:59] 🚨 会话管理器结果: {'session_id': 'session_1752930299', 'success': True}
[2025-07-19 21:04:59] 🚨 准备调用 start_realtime_conversation
[2025-07-19 21:04:59] 🚨 disable_vad参数值: False
[2025-07-19 21:05:23] 🚨 start_realtime_dialogue API被调用
[2025-07-19 21:05:23] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 21:05:23] 🚨 webui实例获取成功
[2025-07-19 21:05:23] 🚨 准备调用会话管理器
[2025-07-19 21:05:23] 🚨 会话管理器结果: {'session_id': 'session_1752930323', 'success': True}
[2025-07-19 21:05:23] 🚨 准备调用 start_realtime_conversation
[2025-07-19 21:05:23] 🚨 disable_vad参数值: False
[2025-07-19 21:11:49] 🚨 start_realtime_dialogue API被调用
[2025-07-19 21:11:49] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 21:11:49] 🚨 webui实例获取成功
[2025-07-19 21:11:49] 🚨 准备调用会话管理器
[2025-07-19 21:11:49] 🚨 会话管理器结果: {'session_id': 'session_1752930709', 'success': True}
[2025-07-19 21:11:49] 🚨 准备调用 start_realtime_conversation
[2025-07-19 21:11:49] 🚨 disable_vad参数值: False
[2025-07-19 21:56:30] 🚨 start_realtime_dialogue API被调用
[2025-07-19 21:56:30] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 21:56:30] 🚨 webui实例获取成功
[2025-07-19 21:56:30] 🚨 准备调用会话管理器
[2025-07-19 21:56:30] 🚨 会话管理器结果: {'session_id': 'session_1752933390', 'success': True}
[2025-07-19 21:56:30] 🚨 准备调用 start_realtime_conversation
[2025-07-19 21:56:30] 🚨 disable_vad参数值: False
[2025-07-19 23:15:56] 🚨 start_realtime_dialogue API被调用
[2025-07-19 23:15:56] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-19 23:15:56] 🚨 webui实例获取成功
[2025-07-19 23:15:56] 🚨 准备调用会话管理器
[2025-07-19 23:15:56] 🚨 会话管理器结果: {'session_id': 'session_1752938156', 'success': True}
[2025-07-19 23:15:56] 🚨 准备调用 start_realtime_conversation
[2025-07-19 23:15:56] 🚨 disable_vad参数值: False
[2025-07-20 00:41:33] 🚨 start_realtime_dialogue API被调用
[2025-07-20 00:41:33] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 00:41:33] 🚨 webui实例获取成功
[2025-07-20 00:41:33] 🚨 准备调用会话管理器
[2025-07-20 00:41:33] 🚨 会话管理器结果: {'session_id': 'session_1752943293', 'success': True}
[2025-07-20 00:41:33] 🚨 准备调用 start_realtime_conversation
[2025-07-20 00:41:33] 🚨 disable_vad参数值: False
[2025-07-20 02:18:11] 🚨 start_realtime_dialogue API被调用
[2025-07-20 02:18:11] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-20 02:18:11] 🚨 webui实例获取成功
[2025-07-20 02:18:11] 🚨 准备调用会话管理器
[2025-07-20 02:18:11] 🚨 会话管理器结果: {'session_id': 'session_1752949091', 'success': True}
[2025-07-20 02:18:11] 🚨 准备调用 start_realtime_conversation
[2025-07-20 02:18:11] 🚨 disable_vad参数值: True
[2025-07-20 02:18:21] 🚨 start_realtime_dialogue API被调用
[2025-07-20 02:18:21] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-20 02:18:21] 🚨 webui实例获取成功
[2025-07-20 02:18:21] 🚨 准备调用会话管理器
[2025-07-20 02:18:21] 🚨 会话管理器结果: {'session_id': 'session_1752949091', 'success': True}
[2025-07-20 02:18:21] 🚨 准备调用 start_realtime_conversation
[2025-07-20 02:18:21] 🚨 disable_vad参数值: True
[2025-07-20 03:37:01] 🚨 start_realtime_dialogue API被调用
[2025-07-20 03:37:01] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-20 03:37:01] 🚨 webui实例获取成功
[2025-07-20 03:37:01] 🚨 准备调用会话管理器
[2025-07-20 03:37:01] 🚨 会话管理器结果: {'session_id': 'session_1752953821', 'success': True}
[2025-07-20 03:37:01] 🚨 准备调用 start_realtime_conversation
[2025-07-20 03:37:01] 🚨 disable_vad参数值: True
[2025-07-20 03:37:10] 🚨 start_realtime_dialogue API被调用
[2025-07-20 03:37:10] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-20 03:37:10] 🚨 webui实例获取成功
[2025-07-20 03:37:10] 🚨 准备调用会话管理器
[2025-07-20 03:37:10] 🚨 会话管理器结果: {'session_id': 'session_1752953821', 'success': True}
[2025-07-20 03:37:10] 🚨 准备调用 start_realtime_conversation
[2025-07-20 03:37:10] 🚨 disable_vad参数值: True
[2025-07-20 03:42:30] 🚨 start_realtime_dialogue API被调用
[2025-07-20 03:42:30] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 03:42:30] 🚨 webui实例获取成功
[2025-07-20 03:42:30] 🚨 准备调用会话管理器
[2025-07-20 03:42:30] 🚨 会话管理器结果: {'session_id': 'session_1752954150', 'success': True}
[2025-07-20 03:42:30] 🚨 准备调用 start_realtime_conversation
[2025-07-20 03:42:30] 🚨 disable_vad参数值: False
[2025-07-20 03:48:50] 🚨 start_realtime_dialogue API被调用
[2025-07-20 03:48:50] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 03:48:50] 🚨 webui实例获取成功
[2025-07-20 03:48:50] 🚨 准备调用会话管理器
[2025-07-20 03:48:50] 🚨 会话管理器结果: {'session_id': 'session_1752954530', 'success': True}
[2025-07-20 03:48:50] 🚨 准备调用 start_realtime_conversation
[2025-07-20 03:48:50] 🚨 disable_vad参数值: False
[2025-07-20 11:52:32] 🚨 start_realtime_dialogue API被调用
[2025-07-20 11:52:32] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-20 11:52:32] 🚨 webui实例获取成功
[2025-07-20 11:52:32] 🚨 准备调用会话管理器
[2025-07-20 11:52:32] 🚨 会话管理器结果: {'session_id': 'session_1752983552', 'success': True}
[2025-07-20 11:52:32] 🚨 准备调用 start_realtime_conversation
[2025-07-20 11:52:32] 🚨 disable_vad参数值: True
[2025-07-20 11:52:43] 🚨 start_realtime_dialogue API被调用
[2025-07-20 11:52:43] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-20 11:52:43] 🚨 webui实例获取成功
[2025-07-20 11:52:43] 🚨 准备调用会话管理器
[2025-07-20 11:52:43] 🚨 会话管理器结果: {'session_id': 'session_1752983552', 'success': True}
[2025-07-20 11:52:43] 🚨 准备调用 start_realtime_conversation
[2025-07-20 11:52:43] 🚨 disable_vad参数值: True
[2025-07-20 12:12:19] 🚨 start_realtime_dialogue API被调用
[2025-07-20 12:12:19] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 12:12:19] 🚨 webui实例获取成功
[2025-07-20 12:12:19] 🚨 准备调用会话管理器
[2025-07-20 12:12:19] 🚨 会话管理器结果: {'session_id': 'session_1752984739', 'success': True}
[2025-07-20 12:12:19] 🚨 准备调用 start_realtime_conversation
[2025-07-20 12:12:19] 🚨 disable_vad参数值: False
[2025-07-20 12:12:45] 🚨 start_realtime_dialogue API被调用
[2025-07-20 12:12:45] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 12:12:45] 🚨 webui实例获取成功
[2025-07-20 12:12:45] 🚨 准备调用会话管理器
[2025-07-20 12:12:45] 🚨 会话管理器结果: {'session_id': 'session_1752984765', 'success': True}
[2025-07-20 12:12:45] 🚨 准备调用 start_realtime_conversation
[2025-07-20 12:12:45] 🚨 disable_vad参数值: False
[2025-07-20 12:16:05] 🚨 start_realtime_dialogue API被调用
[2025-07-20 12:16:05] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 12:16:05] 🚨 webui实例获取成功
[2025-07-20 12:16:05] 🚨 准备调用会话管理器
[2025-07-20 12:16:05] 🚨 会话管理器结果: {'session_id': 'session_1752984965', 'success': True}
[2025-07-20 12:16:05] 🚨 准备调用 start_realtime_conversation
[2025-07-20 12:16:05] 🚨 disable_vad参数值: False
[2025-07-20 12:19:43] 🚨 start_realtime_dialogue API被调用
[2025-07-20 12:19:43] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 12:19:43] 🚨 webui实例获取成功
[2025-07-20 12:19:43] 🚨 准备调用会话管理器
[2025-07-20 12:19:43] 🚨 会话管理器结果: {'session_id': 'session_1752985183', 'success': True}
[2025-07-20 12:19:43] 🚨 准备调用 start_realtime_conversation
[2025-07-20 12:19:43] 🚨 disable_vad参数值: False
[2025-07-20 22:39:31] 🚨 start_realtime_dialogue API被调用
[2025-07-20 22:39:31] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-20 22:39:31] 🚨 webui实例获取成功
[2025-07-20 22:39:31] 🚨 准备调用会话管理器
[2025-07-20 22:39:31] 🚨 会话管理器结果: {'session_id': 'session_1753022371', 'success': True}
[2025-07-20 22:39:31] 🚨 准备调用 start_realtime_conversation
[2025-07-20 22:39:31] 🚨 disable_vad参数值: False
[2025-07-21 00:35:02] 🚨 start_realtime_dialogue API被调用
[2025-07-21 00:35:02] 🚨 参数: disableVAD=True, mode=user-voice
[2025-07-21 00:35:02] 🚨 webui实例获取成功
[2025-07-21 00:35:02] 🚨 准备调用会话管理器
[2025-07-21 00:35:02] 🚨 会话管理器结果: {'session_id': 'session_1753029302', 'success': True}
[2025-07-21 00:35:02] 🚨 准备调用 start_realtime_conversation
[2025-07-21 00:35:02] 🚨 disable_vad参数值: True
[2025-07-21 01:23:37] 🚨 start_realtime_dialogue API被调用
[2025-07-21 01:23:37] 🚨 参数: disableVAD=True, mode=user-voice
[2025-07-21 01:23:37] 🚨 webui实例获取成功
[2025-07-21 01:23:37] 🚨 准备调用会话管理器
[2025-07-21 01:23:37] 🚨 会话管理器结果: {'session_id': 'session_1753032217', 'success': True}
[2025-07-21 01:23:37] 🚨 准备调用 start_realtime_conversation
[2025-07-21 01:23:37] 🚨 disable_vad参数值: True
[2025-07-21 01:28:03] 🚨 start_realtime_dialogue API被调用
[2025-07-21 01:28:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-21 01:28:03] 🚨 webui实例获取成功
[2025-07-21 01:28:03] 🚨 准备调用会话管理器
[2025-07-21 01:28:03] 🚨 会话管理器结果: {'session_id': 'session_1753032483', 'success': True}
[2025-07-21 01:28:03] 🚨 准备调用 start_realtime_conversation
[2025-07-21 01:28:03] 🚨 disable_vad参数值: False
[2025-07-21 03:46:34] 🚨 start_realtime_dialogue API被调用
[2025-07-21 03:46:34] 🚨 参数: disableVAD=True, mode=user-voice
[2025-07-21 03:46:34] 🚨 webui实例获取成功
[2025-07-21 03:46:34] 🚨 准备调用会话管理器
[2025-07-21 03:46:34] 🚨 会话管理器结果: {'session_id': 'session_1753040794', 'success': True}
[2025-07-21 03:46:34] 🚨 准备调用 start_realtime_conversation
[2025-07-21 03:46:34] 🚨 disable_vad参数值: True
[2025-07-21 05:22:12] 🚨 start_realtime_dialogue API被调用
[2025-07-21 05:22:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-21 05:22:12] 🚨 webui实例获取成功
[2025-07-21 05:22:12] 🚨 准备调用会话管理器
[2025-07-21 05:22:12] 🚨 会话管理器结果: {'session_id': 'session_1753046532', 'success': True}
[2025-07-21 05:22:12] 🚨 准备调用 start_realtime_conversation
[2025-07-21 05:22:12] 🚨 disable_vad参数值: False
[2025-07-21 09:48:23] 🚨 start_realtime_dialogue API被调用
[2025-07-21 09:48:23] 🚨 参数: disableVAD=True, mode=user-voice
[2025-07-21 09:48:23] 🚨 webui实例获取成功
[2025-07-21 09:48:23] 🚨 准备调用会话管理器
[2025-07-21 09:48:23] 🚨 会话管理器结果: {'session_id': 'session_1753062503', 'success': True}
[2025-07-21 09:48:23] 🚨 准备调用 start_realtime_conversation
[2025-07-21 09:48:23] 🚨 disable_vad参数值: True
[2025-07-21 09:50:05] 🚨 start_realtime_dialogue API被调用
[2025-07-21 09:50:05] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-21 09:50:05] 🚨 webui实例获取成功
[2025-07-21 09:50:05] 🚨 准备调用会话管理器
[2025-07-21 09:50:05] 🚨 会话管理器结果: {'session_id': 'session_1753062605', 'success': True}
[2025-07-21 09:50:05] 🚨 准备调用 start_realtime_conversation
[2025-07-21 09:50:05] 🚨 disable_vad参数值: False
[2025-07-21 10:37:51] 🚨 start_realtime_dialogue API被调用
[2025-07-21 10:37:51] 🚨 参数: disableVAD=True, mode=user-voice
[2025-07-21 10:37:51] 🚨 webui实例获取成功
[2025-07-21 10:37:51] 🚨 准备调用会话管理器
[2025-07-21 10:37:51] 🚨 会话管理器结果: {'session_id': 'session_1753065471', 'success': True}
[2025-07-21 10:37:51] 🚨 准备调用 start_realtime_conversation
[2025-07-21 10:37:51] 🚨 disable_vad参数值: True
[2025-07-21 10:42:03] 🚨 start_realtime_dialogue API被调用
[2025-07-21 10:42:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-21 10:42:03] 🚨 webui实例获取成功
[2025-07-21 10:42:03] 🚨 准备调用会话管理器
[2025-07-21 10:42:03] 🚨 会话管理器结果: {'session_id': 'session_1753065723', 'success': True}
[2025-07-21 10:42:03] 🚨 准备调用 start_realtime_conversation
[2025-07-21 10:42:03] 🚨 disable_vad参数值: False
[2025-07-21 11:46:28] 🚨 start_realtime_dialogue API被调用
[2025-07-21 11:46:28] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-21 11:46:28] 🚨 webui实例获取成功
[2025-07-21 11:46:28] 🚨 准备调用会话管理器
[2025-07-21 11:46:28] 🚨 会话管理器结果: {'session_id': 'session_1753069588', 'success': True}
[2025-07-21 11:46:28] 🚨 准备调用 start_realtime_conversation
[2025-07-21 11:46:28] 🚨 disable_vad参数值: True
[2025-07-21 13:04:24] 🚨 start_realtime_dialogue API被调用
[2025-07-21 13:04:24] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-21 13:04:24] 🚨 webui实例获取成功
[2025-07-21 13:04:24] 🚨 准备调用会话管理器
[2025-07-21 13:04:24] 🚨 会话管理器结果: {'session_id': 'session_1753074264', 'success': True}
[2025-07-21 13:04:24] 🚨 准备调用 start_realtime_conversation
[2025-07-21 13:04:24] 🚨 disable_vad参数值: True
[2025-07-21 13:09:39] 🚨 start_realtime_dialogue API被调用
[2025-07-21 13:09:39] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-21 13:09:39] 🚨 webui实例获取成功
[2025-07-21 13:09:39] 🚨 准备调用会话管理器
[2025-07-21 13:09:39] 🚨 会话管理器结果: {'session_id': 'session_1753074579', 'success': True}
[2025-07-21 13:09:39] 🚨 准备调用 start_realtime_conversation
[2025-07-21 13:09:39] 🚨 disable_vad参数值: True
[2025-07-21 13:14:19] 🚨 start_realtime_dialogue API被调用
[2025-07-21 13:14:19] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-21 13:14:19] 🚨 webui实例获取成功
[2025-07-21 13:14:19] 🚨 准备调用会话管理器
[2025-07-21 13:14:19] 🚨 会话管理器结果: {'session_id': 'session_1753074859', 'success': True}
[2025-07-21 13:14:19] 🚨 准备调用 start_realtime_conversation
[2025-07-21 13:14:19] 🚨 disable_vad参数值: False
[2025-07-21 13:35:20] 🚨 start_realtime_dialogue API被调用
[2025-07-21 13:35:20] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-21 13:35:20] 🚨 webui实例获取成功
[2025-07-21 13:35:20] 🚨 准备调用会话管理器
[2025-07-21 13:35:20] 🚨 会话管理器结果: {'session_id': 'session_1753076120', 'success': True}
[2025-07-21 13:35:20] 🚨 准备调用 start_realtime_conversation
[2025-07-21 13:35:20] 🚨 disable_vad参数值: True
[2025-07-21 13:43:29] 🚨 start_realtime_dialogue API被调用
[2025-07-21 13:43:29] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-21 13:43:29] 🚨 webui实例获取成功
[2025-07-21 13:43:29] 🚨 准备调用会话管理器
[2025-07-21 13:43:29] 🚨 会话管理器结果: {'session_id': 'session_1753076609', 'success': True}
[2025-07-21 13:43:29] 🚨 准备调用 start_realtime_conversation
[2025-07-21 13:43:29] 🚨 disable_vad参数值: False
[2025-07-21 14:51:39] 🚨 start_realtime_dialogue API被调用
[2025-07-21 14:51:39] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-21 14:51:39] 🚨 webui实例获取成功
[2025-07-21 14:51:39] 🚨 准备调用会话管理器
[2025-07-21 14:51:39] 🚨 会话管理器结果: {'session_id': 'session_1753080699', 'success': True}
[2025-07-21 14:51:39] 🚨 准备调用 start_realtime_conversation
[2025-07-21 14:51:39] 🚨 disable_vad参数值: True
[2025-07-21 18:04:17] 🚨 start_realtime_dialogue API被调用
[2025-07-21 18:04:17] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-21 18:04:17] 🚨 webui实例获取成功
[2025-07-21 18:04:17] 🚨 准备调用会话管理器
[2025-07-21 18:04:17] 🚨 会话管理器结果: {'session_id': 'session_1753092257', 'success': True}
[2025-07-21 18:04:17] 🚨 准备调用 start_realtime_conversation
[2025-07-21 18:04:17] 🚨 disable_vad参数值: True
[2025-07-24 03:04:55] 🚨 start_realtime_dialogue API被调用
[2025-07-24 03:04:55] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 03:04:55] 🚨 webui实例获取成功
[2025-07-24 03:04:55] 🚨 准备调用会话管理器
[2025-07-24 03:04:55] 🚨 会话管理器结果: {'session_id': 'session_1753297495', 'success': True}
[2025-07-24 03:04:55] 🚨 准备调用 start_realtime_conversation
[2025-07-24 03:04:55] 🚨 disable_vad参数值: True
[2025-07-24 03:09:51] 🚨 start_realtime_dialogue API被调用
[2025-07-24 03:09:51] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 03:09:51] 🚨 webui实例获取成功
[2025-07-24 03:09:51] 🚨 准备调用会话管理器
[2025-07-24 03:09:51] 🚨 会话管理器结果: {'session_id': 'session_1753297791', 'success': True}
[2025-07-24 03:09:51] 🚨 准备调用 start_realtime_conversation
[2025-07-24 03:09:51] 🚨 disable_vad参数值: True
[2025-07-24 03:12:44] 🚨 start_realtime_dialogue API被调用
[2025-07-24 03:12:44] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-24 03:12:44] 🚨 webui实例获取成功
[2025-07-24 03:12:44] 🚨 准备调用会话管理器
[2025-07-24 03:12:44] 🚨 会话管理器结果: {'session_id': 'session_1753297964', 'success': True}
[2025-07-24 03:12:44] 🚨 准备调用 start_realtime_conversation
[2025-07-24 03:12:44] 🚨 disable_vad参数值: False
[2025-07-24 03:14:38] 🚨 start_realtime_dialogue API被调用
[2025-07-24 03:14:38] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 03:14:38] 🚨 webui实例获取成功
[2025-07-24 03:14:38] 🚨 准备调用会话管理器
[2025-07-24 03:14:38] 🚨 会话管理器结果: {'session_id': 'session_1753298078', 'success': True}
[2025-07-24 03:14:38] 🚨 准备调用 start_realtime_conversation
[2025-07-24 03:14:38] 🚨 disable_vad参数值: True
[2025-07-24 03:24:12] 🚨 start_realtime_dialogue API被调用
[2025-07-24 03:24:12] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 03:24:12] 🚨 webui实例获取成功
[2025-07-24 03:24:12] 🚨 准备调用会话管理器
[2025-07-24 03:24:12] 🚨 会话管理器结果: {'session_id': 'session_1753298652', 'success': True}
[2025-07-24 03:24:12] 🚨 准备调用 start_realtime_conversation
[2025-07-24 03:24:12] 🚨 disable_vad参数值: True
[2025-07-24 16:19:23] 🚨 start_realtime_dialogue API被调用
[2025-07-24 16:19:23] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 16:19:23] 🚨 webui实例获取成功
[2025-07-24 16:19:23] 🚨 准备调用会话管理器
[2025-07-24 16:19:23] 🚨 会话管理器结果: {'session_id': 'session_1753345163', 'success': True}
[2025-07-24 16:19:23] 🚨 准备调用 start_realtime_conversation
[2025-07-24 16:19:23] 🚨 disable_vad参数值: True
[2025-07-24 16:22:53] 🚨 start_realtime_dialogue API被调用
[2025-07-24 16:22:53] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 16:22:53] 🚨 webui实例获取成功
[2025-07-24 16:22:53] 🚨 准备调用会话管理器
[2025-07-24 16:22:53] 🚨 会话管理器结果: {'session_id': 'session_1753345373', 'success': True}
[2025-07-24 16:22:53] 🚨 准备调用 start_realtime_conversation
[2025-07-24 16:22:53] 🚨 disable_vad参数值: True
[2025-07-24 16:33:22] 🚨 start_realtime_dialogue API被调用
[2025-07-24 16:33:22] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 16:33:22] 🚨 webui实例获取成功
[2025-07-24 16:33:22] 🚨 准备调用会话管理器
[2025-07-24 16:33:22] 🚨 会话管理器结果: {'session_id': 'session_1753346002', 'success': True}
[2025-07-24 16:33:22] 🚨 准备调用 start_realtime_conversation
[2025-07-24 16:33:22] 🚨 disable_vad参数值: True
[2025-07-24 16:42:19] 🚨 start_realtime_dialogue API被调用
[2025-07-24 16:42:19] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 16:42:19] 🚨 webui实例获取成功
[2025-07-24 16:42:19] 🚨 准备调用会话管理器
[2025-07-24 16:42:19] 🚨 会话管理器结果: {'session_id': 'session_1753346539', 'success': True}
[2025-07-24 16:42:19] 🚨 准备调用 start_realtime_conversation
[2025-07-24 16:42:19] 🚨 disable_vad参数值: True
[2025-07-24 17:00:08] 🚨 start_realtime_dialogue API被调用
[2025-07-24 17:00:08] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 17:00:08] 🚨 webui实例获取成功
[2025-07-24 17:00:08] 🚨 准备调用会话管理器
[2025-07-24 17:00:08] 🚨 会话管理器结果: {'session_id': 'session_1753347608', 'success': True}
[2025-07-24 17:00:08] 🚨 准备调用 start_realtime_conversation
[2025-07-24 17:00:08] 🚨 disable_vad参数值: True
[2025-07-24 17:08:01] 🚨 start_realtime_dialogue API被调用
[2025-07-24 17:08:01] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 17:08:01] 🚨 webui实例获取成功
[2025-07-24 17:08:01] 🚨 准备调用会话管理器
[2025-07-24 17:08:01] 🚨 会话管理器结果: {'session_id': 'session_1753348081', 'success': True}
[2025-07-24 17:08:01] 🚨 准备调用 start_realtime_conversation
[2025-07-24 17:08:01] 🚨 disable_vad参数值: True
[2025-07-24 17:16:56] 🚨 start_realtime_dialogue API被调用
[2025-07-24 17:16:56] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-24 17:16:56] 🚨 webui实例获取成功
[2025-07-24 17:16:56] 🚨 准备调用会话管理器
[2025-07-24 17:16:56] 🚨 会话管理器结果: {'session_id': 'session_1753348616', 'success': True}
[2025-07-24 17:16:56] 🚨 准备调用 start_realtime_conversation
[2025-07-24 17:16:56] 🚨 disable_vad参数值: False
[2025-07-24 17:19:28] 🚨 start_realtime_dialogue API被调用
[2025-07-24 17:19:28] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 17:19:28] 🚨 webui实例获取成功
[2025-07-24 17:19:28] 🚨 准备调用会话管理器
[2025-07-24 17:19:28] 🚨 会话管理器结果: {'session_id': 'session_1753348768', 'success': True}
[2025-07-24 17:19:28] 🚨 准备调用 start_realtime_conversation
[2025-07-24 17:19:28] 🚨 disable_vad参数值: True
[2025-07-24 20:48:52] 🚨 start_realtime_dialogue API被调用
[2025-07-24 20:48:52] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 20:48:52] 🚨 webui实例获取成功
[2025-07-24 20:48:52] 🚨 准备调用会话管理器
[2025-07-24 20:48:52] 🚨 会话管理器结果: {'session_id': 'session_1753361332', 'success': True}
[2025-07-24 20:48:52] 🚨 准备调用 start_realtime_conversation
[2025-07-24 20:48:52] 🚨 disable_vad参数值: True
[2025-07-24 23:53:24] 🚨 start_realtime_dialogue API被调用
[2025-07-24 23:53:24] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 23:53:24] 🚨 webui实例获取成功
[2025-07-24 23:53:24] 🚨 准备调用会话管理器
[2025-07-24 23:53:24] 🚨 会话管理器结果: {'session_id': 'session_1753372404', 'success': True}
[2025-07-24 23:53:24] 🚨 准备调用 start_realtime_conversation
[2025-07-24 23:53:24] 🚨 disable_vad参数值: True
[2025-07-24 23:58:00] 🚨 start_realtime_dialogue API被调用
[2025-07-24 23:58:00] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-24 23:58:00] 🚨 webui实例获取成功
[2025-07-24 23:58:00] 🚨 准备调用会话管理器
[2025-07-24 23:58:00] 🚨 会话管理器结果: {'session_id': 'session_1753372680', 'success': True}
[2025-07-24 23:58:00] 🚨 准备调用 start_realtime_conversation
[2025-07-24 23:58:00] 🚨 disable_vad参数值: True
[2025-07-25 00:14:47] 🚨 start_realtime_dialogue API被调用
[2025-07-25 00:14:47] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 00:14:47] 🚨 webui实例获取成功
[2025-07-25 00:14:47] 🚨 准备调用会话管理器
[2025-07-25 00:14:47] 🚨 会话管理器结果: {'session_id': 'session_1753373687', 'success': True}
[2025-07-25 00:14:47] 🚨 准备调用 start_realtime_conversation
[2025-07-25 00:14:47] 🚨 disable_vad参数值: True
[2025-07-25 00:26:03] 🚨 start_realtime_dialogue API被调用
[2025-07-25 00:26:03] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-25 00:26:03] 🚨 webui实例获取成功
[2025-07-25 00:26:03] 🚨 准备调用会话管理器
[2025-07-25 00:26:03] 🚨 会话管理器结果: {'session_id': 'session_1753374363', 'success': True}
[2025-07-25 00:26:03] 🚨 准备调用 start_realtime_conversation
[2025-07-25 00:26:03] 🚨 disable_vad参数值: False
[2025-07-25 00:28:43] 🚨 start_realtime_dialogue API被调用
[2025-07-25 00:28:43] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 00:28:43] 🚨 webui实例获取成功
[2025-07-25 00:28:43] 🚨 准备调用会话管理器
[2025-07-25 00:28:43] 🚨 会话管理器结果: {'session_id': 'session_1753374523', 'success': True}
[2025-07-25 00:28:43] 🚨 准备调用 start_realtime_conversation
[2025-07-25 00:28:43] 🚨 disable_vad参数值: True
[2025-07-25 00:33:18] 🚨 start_realtime_dialogue API被调用
[2025-07-25 00:33:18] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 00:33:18] 🚨 webui实例获取成功
[2025-07-25 00:33:18] 🚨 准备调用会话管理器
[2025-07-25 00:33:18] 🚨 会话管理器结果: {'session_id': 'session_1753374798', 'success': True}
[2025-07-25 00:33:18] 🚨 准备调用 start_realtime_conversation
[2025-07-25 00:33:18] 🚨 disable_vad参数值: True
[2025-07-25 16:02:05] 🚨 start_realtime_dialogue API被调用
[2025-07-25 16:02:05] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 16:02:05] 🚨 webui实例获取成功
[2025-07-25 16:02:05] 🚨 准备调用会话管理器
[2025-07-25 16:02:05] 🚨 会话管理器结果: {'session_id': 'session_1753430525', 'success': True}
[2025-07-25 16:02:05] 🚨 准备调用 start_realtime_conversation
[2025-07-25 16:02:05] 🚨 disable_vad参数值: True
[2025-07-25 16:09:23] 🚨 start_realtime_dialogue API被调用
[2025-07-25 16:09:23] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 16:09:23] 🚨 webui实例获取成功
[2025-07-25 16:09:23] 🚨 准备调用会话管理器
[2025-07-25 16:09:23] 🚨 会话管理器结果: {'session_id': 'session_1753430963', 'success': True}
[2025-07-25 16:09:23] 🚨 准备调用 start_realtime_conversation
[2025-07-25 16:09:23] 🚨 disable_vad参数值: True
[2025-07-25 16:40:26] 🚨 start_realtime_dialogue API被调用
[2025-07-25 16:40:26] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 16:40:26] 🚨 webui实例获取成功
[2025-07-25 16:40:26] 🚨 准备调用会话管理器
[2025-07-25 16:40:26] 🚨 会话管理器结果: {'session_id': 'session_1753432826', 'success': True}
[2025-07-25 16:40:26] 🚨 准备调用 start_realtime_conversation
[2025-07-25 16:40:26] 🚨 disable_vad参数值: True
[2025-07-25 16:53:16] 🚨 start_realtime_dialogue API被调用
[2025-07-25 16:53:16] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 16:53:16] 🚨 webui实例获取成功
[2025-07-25 16:53:16] 🚨 准备调用会话管理器
[2025-07-25 16:53:16] 🚨 会话管理器结果: {'session_id': 'session_1753433596', 'success': True}
[2025-07-25 16:53:16] 🚨 准备调用 start_realtime_conversation
[2025-07-25 16:53:16] 🚨 disable_vad参数值: True
[2025-07-25 17:02:46] 🚨 start_realtime_dialogue API被调用
[2025-07-25 17:02:46] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 17:02:46] 🚨 webui实例获取成功
[2025-07-25 17:02:46] 🚨 准备调用会话管理器
[2025-07-25 17:02:46] 🚨 会话管理器结果: {'session_id': 'session_1753434166', 'success': True}
[2025-07-25 17:02:46] 🚨 准备调用 start_realtime_conversation
[2025-07-25 17:02:46] 🚨 disable_vad参数值: True
[2025-07-25 17:10:52] 🚨 start_realtime_dialogue API被调用
[2025-07-25 17:10:52] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 17:10:52] 🚨 webui实例获取成功
[2025-07-25 17:10:52] 🚨 准备调用会话管理器
[2025-07-25 17:10:52] 🚨 会话管理器结果: {'session_id': 'session_1753434652', 'success': True}
[2025-07-25 17:10:52] 🚨 准备调用 start_realtime_conversation
[2025-07-25 17:10:52] 🚨 disable_vad参数值: True
[2025-07-25 17:31:28] 🚨 start_realtime_dialogue API被调用
[2025-07-25 17:31:28] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-25 17:31:28] 🚨 webui实例获取成功
[2025-07-25 17:31:28] 🚨 准备调用会话管理器
[2025-07-25 17:31:28] 🚨 会话管理器结果: {'session_id': 'session_1753435888', 'success': True}
[2025-07-25 17:31:28] 🚨 准备调用 start_realtime_conversation
[2025-07-25 17:31:28] 🚨 disable_vad参数值: False
[2025-07-25 22:41:14] 🚨 start_realtime_dialogue API被调用
[2025-07-25 22:41:14] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-25 22:41:14] 🚨 webui实例获取成功
[2025-07-25 22:41:14] 🚨 准备调用会话管理器
[2025-07-25 22:41:14] 🚨 会话管理器结果: {'session_id': 'session_1753454474', 'success': True}
[2025-07-25 22:41:14] 🚨 准备调用 start_realtime_conversation
[2025-07-25 22:41:14] 🚨 disable_vad参数值: False
[2025-07-25 22:42:12] 🚨 start_realtime_dialogue API被调用
[2025-07-25 22:42:12] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-25 22:42:12] 🚨 webui实例获取成功
[2025-07-25 22:42:12] 🚨 准备调用会话管理器
[2025-07-25 22:42:12] 🚨 会话管理器结果: {'session_id': 'session_1753454532', 'success': True}
[2025-07-25 22:42:12] 🚨 准备调用 start_realtime_conversation
[2025-07-25 22:42:12] 🚨 disable_vad参数值: False
[2025-07-25 22:42:52] 🚨 start_realtime_dialogue API被调用
[2025-07-25 22:42:52] 🚨 参数: disableVAD=False, mode=user-voice
[2025-07-25 22:42:52] 🚨 webui实例获取成功
[2025-07-25 22:42:52] 🚨 准备调用会话管理器
[2025-07-25 22:42:52] 🚨 会话管理器结果: {'session_id': 'session_1753454572', 'success': True}
[2025-07-25 22:42:52] 🚨 准备调用 start_realtime_conversation
[2025-07-25 22:42:52] 🚨 disable_vad参数值: False
[2025-07-25 22:44:55] 🚨 start_realtime_dialogue API被调用
[2025-07-25 22:44:55] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 22:44:55] 🚨 webui实例获取成功
[2025-07-25 22:44:55] 🚨 准备调用会话管理器
[2025-07-25 22:44:55] 🚨 会话管理器结果: {'session_id': 'session_1753454695', 'success': True}
[2025-07-25 22:44:55] 🚨 准备调用 start_realtime_conversation
[2025-07-25 22:44:55] 🚨 disable_vad参数值: True
[2025-07-25 22:50:36] 🚨 start_realtime_dialogue API被调用
[2025-07-25 22:50:36] 🚨 参数: disableVAD=True, mode=oracle
[2025-07-25 22:50:36] 🚨 webui实例获取成功
[2025-07-25 22:50:36] 🚨 准备调用会话管理器
[2025-07-25 22:50:36] 🚨 会话管理器结果: {'session_id': 'session_1753455036', 'success': True}
[2025-07-25 22:50:36] 🚨 准备调用 start_realtime_conversation
[2025-07-25 22:50:36] 🚨 disable_vad参数值: True
