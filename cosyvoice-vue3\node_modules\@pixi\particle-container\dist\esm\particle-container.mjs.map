{"version": 3, "file": "particle-container.mjs", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/ParticleContainer.ts", "../../src/ParticleBuffer.ts", "../../src/ParticleRenderer.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { BLEND_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\nimport { hex2rgb } from '@pixi/utils';\n\nimport type { BaseTexture, Renderer } from '@pixi/core';\nimport type { ParticleBuffer } from './ParticleBuffer';\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Sprite } from '@pixi/sprite';\n\nexport interface IParticleProperties\n{\n    vertices?: boolean;\n    position?: boolean;\n    rotation?: boolean;\n    uvs?: boolean;\n    tint?: boolean;\n    alpha?: boolean;\n    scale?: boolean;\n}\n\n/**\n * The ParticleContainer class is a really fast version of the Container built solely for speed,\n * so use when you need a lot of sprites or particles.\n *\n * The tradeoff of the ParticleContainer is that most advanced functionality will not work.\n * ParticleContainer implements the basic object transform (position, scale, rotation)\n * and some advanced functionality like tint (as of v4.5.6).\n *\n * Other more advanced functionality like masking, children, filters, etc will not work on sprites in this batch.\n *\n * It's extremely easy to use:\n * ```js\n * let container = new ParticleContainer();\n *\n * for (let i = 0; i < 100; ++i)\n * {\n *     let sprite = PIXI.Sprite.from(\"myImage.png\");\n *     container.addChild(sprite);\n * }\n * ```\n *\n * And here you have a hundred sprites that will be rendered at the speed of light.\n * @memberof PIXI\n */\nexport class ParticleContainer extends Container<Sprite>\n{\n    /**\n     * The blend mode to be applied to the sprite. Apply a value of `PIXI.BLEND_MODES.NORMAL`\n     * to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public blendMode: BLEND_MODES;\n\n    /**\n     * If true, container allocates more batches in case there are more than `maxSize` particles.\n     * @default false\n     */\n    public autoResize: boolean;\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * Default to true here as performance is usually the priority for particles.\n     * @default true\n     */\n    public roundPixels: boolean;\n\n    /**\n     * The texture used to render the children.\n     * @readonly\n     */\n    public baseTexture: BaseTexture;\n    public tintRgb: Float32Array;\n\n    /** @private */\n    _maxSize: number;\n\n    /** @private */\n    _buffers: ParticleBuffer[];\n\n    /** @private */\n    _batchSize: number;\n\n    /**\n     * Set properties to be dynamic (true) / static (false).\n     * @private\n     */\n    _properties: boolean[];\n\n    /**\n     * For every batch, stores _updateID corresponding to the last change in that batch.\n     * @private\n     */\n    _bufferUpdateIDs: number[];\n\n    /**\n     * When child inserted, removed or changes position this number goes up.\n     * @private\n     */\n    _updateID: number;\n\n    /**\n     * The tint applied to the container.\n     * This is a hex value. A value of 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    private _tint: number;\n\n    /**\n     * @param maxSize - The maximum number of particles that can be rendered by the container.\n     *  Affects size of allocated buffers.\n     * @param properties - The properties of children that should be uploaded to the gpu and applied.\n     * @param {boolean} [properties.vertices=false] - When true, vertices be uploaded and applied.\n     *                  if sprite's ` scale/anchor/trim/frame/orig` is dynamic, please set `true`.\n     * @param {boolean} [properties.position=true] - When true, position be uploaded and applied.\n     * @param {boolean} [properties.rotation=false] - When true, rotation be uploaded and applied.\n     * @param {boolean} [properties.uvs=false] - When true, uvs be uploaded and applied.\n     * @param {boolean} [properties.tint=false] - When true, alpha and tint be uploaded and applied.\n     * @param {number} [batchSize=16384] - Number of particles per batch. If less than maxSize, it uses maxSize instead.\n     * @param {boolean} [autoResize=false] - If true, container allocates more batches in case\n     *  there are more than `maxSize` particles.\n     */\n    constructor(maxSize = 1500, properties?: IParticleProperties, batchSize = 16384, autoResize = false)\n    {\n        super();\n\n        // Making sure the batch size is valid\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        const maxBatchSize = 16384;\n\n        if (batchSize > maxBatchSize)\n        {\n            batchSize = maxBatchSize;\n        }\n\n        this._properties = [false, true, false, false, false];\n        this._maxSize = maxSize;\n        this._batchSize = batchSize;\n        this._buffers = null;\n        this._bufferUpdateIDs = [];\n        this._updateID = 0;\n\n        this.interactiveChildren = false;\n        this.blendMode = BLEND_MODES.NORMAL;\n        this.autoResize = autoResize;\n        this.roundPixels = true;\n        this.baseTexture = null;\n\n        this.setProperties(properties);\n\n        this._tint = 0;\n        this.tintRgb = new Float32Array(4);\n        this.tint = 0xFFFFFF;\n    }\n\n    /**\n     * Sets the private properties array to dynamic / static based on the passed properties object\n     * @param properties - The properties to be uploaded\n     */\n    public setProperties(properties: IParticleProperties): void\n    {\n        if (properties)\n        {\n            this._properties[0] = 'vertices' in properties || 'scale' in properties\n                ? !!properties.vertices || !!properties.scale : this._properties[0];\n            this._properties[1] = 'position' in properties ? !!properties.position : this._properties[1];\n            this._properties[2] = 'rotation' in properties ? !!properties.rotation : this._properties[2];\n            this._properties[3] = 'uvs' in properties ? !!properties.uvs : this._properties[3];\n            this._properties[4] = 'tint' in properties || 'alpha' in properties\n                ? !!properties.tint || !!properties.alpha : this._properties[4];\n        }\n    }\n\n    updateTransform(): void\n    {\n        // TODO don't need to!\n        this.displayObjectUpdateTransform();\n    }\n\n    /**\n     * The tint applied to the container. This is a hex value.\n     * A value of 0xFFFFFF will remove any tint effect.\n     * IMPORTANT: This is a WebGL only feature and will be ignored by the canvas renderer.\n     * @default 0xFFFFFF\n     */\n    get tint(): number\n    {\n        return this._tint;\n    }\n\n    set tint(value: number)\n    {\n        this._tint = value;\n        hex2rgb(value, this.tintRgb);\n    }\n\n    /**\n     * Renders the container using the WebGL renderer.\n     * @param renderer - The WebGL renderer.\n     */\n    public render(renderer: Renderer): void\n    {\n        if (!this.visible || this.worldAlpha <= 0 || !this.children.length || !this.renderable)\n        {\n            return;\n        }\n\n        if (!this.baseTexture)\n        {\n            this.baseTexture = this.children[0]._texture.baseTexture;\n            if (!this.baseTexture.valid)\n            {\n                this.baseTexture.once('update', () => this.onChildrenChange(0));\n            }\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins.particle);\n        renderer.plugins.particle.render(this);\n    }\n\n    /**\n     * Set the flag that static data should be updated to true\n     * @param smallestChildIndex - The smallest child index.\n     */\n    protected onChildrenChange(smallestChildIndex: number): void\n    {\n        const bufferIndex = Math.floor(smallestChildIndex / this._batchSize);\n\n        while (this._bufferUpdateIDs.length < bufferIndex)\n        {\n            this._bufferUpdateIDs.push(0);\n        }\n        this._bufferUpdateIDs[bufferIndex] = ++this._updateID;\n    }\n\n    public dispose(): void\n    {\n        if (this._buffers)\n        {\n            for (let i = 0; i < this._buffers.length; ++i)\n            {\n                this._buffers[i].destroy();\n            }\n\n            this._buffers = null;\n        }\n    }\n\n    /**\n     * Destroys the container\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their\n     *  destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this.dispose();\n\n        this._properties = null;\n        this._buffers = null;\n        this._bufferUpdateIDs = null;\n    }\n}\n", "import { createIndicesForQuads } from '@pixi/utils';\nimport { Geo<PERSON>, Buffer } from '@pixi/core';\nimport { TYPES } from '@pixi/constants';\n\nimport type { Sprite } from '@pixi/sprite';\nimport type { IParticleRendererProperty } from './ParticleRenderer';\n\n/*\n * <AUTHOR>\n *\n * Big thanks to the very clever <PERSON> <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that\n * they now share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleBuffer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleBuffer.java\n */\n\n/**\n * The particle buffer manages the static and dynamic buffers for a particle container.\n * @private\n * @memberof PIXI\n */\nexport class ParticleBuffer\n{\n    public geometry: Geometry;\n    public staticStride: number;\n    public staticBuffer: Buffer;\n    public staticData: Float32Array;\n    public staticDataUint32: Uint32Array;\n    public dynamicStride: number;\n    public dynamicBuffer: Buffer;\n    public dynamicData: Float32Array;\n    public dynamicDataUint32: Uint32Array;\n    public _updateID: number;\n\n    /** Holds the indices of the geometry (quads) to draw. */\n    indexBuffer: Buffer;\n\n    /** The number of particles the buffer can hold. */\n    private size: number;\n\n    /** A list of the properties that are dynamic. */\n    private dynamicProperties: IParticleRendererProperty[];\n\n    /** A list of the properties that are static. */\n    private staticProperties: IParticleRendererProperty[];\n\n    /**\n     * @param {object} properties - The properties to upload.\n     * @param {boolean[]} dynamicPropertyFlags - Flags for which properties are dynamic.\n     * @param {number} size - The size of the batch.\n     */\n    constructor(properties: IParticleRendererProperty[], dynamicPropertyFlags: boolean[], size: number)\n    {\n        this.geometry = new Geometry();\n\n        this.indexBuffer = null;\n\n        this.size = size;\n        this.dynamicProperties = [];\n        this.staticProperties = [];\n\n        for (let i = 0; i < properties.length; ++i)\n        {\n            let property = properties[i];\n\n            // Make copy of properties object so that when we edit the offset it doesn't\n            // change all other instances of the object literal\n            property = {\n                attributeName: property.attributeName,\n                size: property.size,\n                uploadFunction: property.uploadFunction,\n                type: property.type || TYPES.FLOAT,\n                offset: property.offset,\n            };\n\n            if (dynamicPropertyFlags[i])\n            {\n                this.dynamicProperties.push(property);\n            }\n            else\n            {\n                this.staticProperties.push(property);\n            }\n        }\n\n        this.staticStride = 0;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n\n        this.dynamicStride = 0;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this._updateID = 0;\n\n        this.initBuffers();\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    private initBuffers(): void\n    {\n        const geometry = this.geometry;\n\n        let dynamicOffset = 0;\n\n        this.indexBuffer = new Buffer(createIndicesForQuads(this.size), true, true);\n        geometry.addIndex(this.indexBuffer);\n\n        this.dynamicStride = 0;\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.offset = dynamicOffset;\n            dynamicOffset += property.size;\n            this.dynamicStride += property.size;\n        }\n\n        const dynBuffer = new ArrayBuffer(this.size * this.dynamicStride * 4 * 4);\n\n        this.dynamicData = new Float32Array(dynBuffer);\n        this.dynamicDataUint32 = new Uint32Array(dynBuffer);\n        this.dynamicBuffer = new Buffer(this.dynamicData, false, false);\n\n        // static //\n        let staticOffset = 0;\n\n        this.staticStride = 0;\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            property.offset = staticOffset;\n            staticOffset += property.size;\n            this.staticStride += property.size;\n        }\n\n        const statBuffer = new ArrayBuffer(this.size * this.staticStride * 4 * 4);\n\n        this.staticData = new Float32Array(statBuffer);\n        this.staticDataUint32 = new Uint32Array(statBuffer);\n        this.staticBuffer = new Buffer(this.staticData, true, false);\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.dynamicBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.dynamicStride * 4,\n                property.offset * 4\n            );\n        }\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.staticBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.staticStride * 4,\n                property.offset * 4\n            );\n        }\n    }\n\n    /**\n     * Uploads the dynamic properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadDynamic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.dynamicProperties.length; i++)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.dynamicDataUint32 : this.dynamicData,\n                this.dynamicStride, property.offset);\n        }\n\n        this.dynamicBuffer._updateID++;\n    }\n\n    /**\n     * Uploads the static properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadStatic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.staticProperties.length; i++)\n        {\n            const property = this.staticProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.staticDataUint32 : this.staticData,\n                this.staticStride, property.offset);\n        }\n\n        this.staticBuffer._updateID++;\n    }\n\n    /** Destroys the ParticleBuffer. */\n    destroy(): void\n    {\n        this.indexBuffer = null;\n\n        this.dynamicProperties = null;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this.staticProperties = null;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n        // all buffers are destroyed inside geometry\n        this.geometry.destroy();\n    }\n}\n", "import { TYPES } from '@pixi/constants';\nimport { ExtensionType, ObjectRenderer, Shader, State } from '@pixi/core';\nimport { Matrix } from '@pixi/math';\nimport { correctBlendMode, premultiplyRgba, premultiplyTint } from '@pixi/utils';\nimport { ParticleBuffer } from './ParticleBuffer';\nimport fragment from './particles.frag';\nimport vertex from './particles.vert';\n\nimport type { ParticleContainer } from './ParticleContainer';\nimport type { Renderer, ExtensionMetadata } from '@pixi/core';\nimport type { Sprite } from '@pixi/sprite';\n\nexport interface IParticleRendererProperty\n{\n    attributeName: string;\n    size: number;\n    type?: TYPES;\n    uploadFunction: (...params: any[]) => any;\n    offset: number;\n}\n\n/*\n * <AUTHOR> <PERSON>\n *\n * Big thanks to the very clever <PERSON>riers <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that they now\n * share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleRenderer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleRenderer.java\n */\n\n/**\n * Renderer for Particles that is designer for speed over feature set.\n * @memberof PIXI\n */\nexport class ParticleRenderer extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'particle',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /** The WebGL state in which this renderer will work. */\n    public readonly state: State;\n\n    /** The default shader that is used if a sprite doesn't have a more specific one. */\n    public shader: Shader;\n    public tempMatrix: Matrix;\n    public properties: IParticleRendererProperty[];\n\n    /**\n     * @param renderer - The renderer this sprite batch works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        // and max number of element in the index buffer is 16384 * 6 = 98304\n        // Creating a full index buffer, overhead is 98304 * 2 = 196Ko\n        // let numIndices = 98304;\n\n        this.shader = null;\n\n        this.properties = null;\n\n        this.tempMatrix = new Matrix();\n\n        this.properties = [\n            // verticesData\n            {\n                attributeName: 'aVertexPosition',\n                size: 2,\n                uploadFunction: this.uploadVertices,\n                offset: 0,\n            },\n            // positionData\n            {\n                attributeName: 'aPositionCoord',\n                size: 2,\n                uploadFunction: this.uploadPosition,\n                offset: 0,\n            },\n            // rotationData\n            {\n                attributeName: 'aRotation',\n                size: 1,\n                uploadFunction: this.uploadRotation,\n                offset: 0,\n            },\n            // uvsData\n            {\n                attributeName: 'aTextureCoord',\n                size: 2,\n                uploadFunction: this.uploadUvs,\n                offset: 0,\n            },\n            // tintData\n            {\n                attributeName: 'aColor',\n                size: 1,\n                type: TYPES.UNSIGNED_BYTE,\n                uploadFunction: this.uploadTint,\n                offset: 0,\n            },\n        ];\n\n        this.shader = Shader.from(vertex, fragment, {});\n        this.state = State.for2d();\n    }\n\n    /**\n     * Renders the particle container object.\n     * @param container - The container to render using this ParticleRenderer.\n     */\n    public render(container: ParticleContainer): void\n    {\n        const children = container.children;\n        const maxSize = container._maxSize;\n        const batchSize = container._batchSize;\n        const renderer = this.renderer;\n        let totalChildren = children.length;\n\n        if (totalChildren === 0)\n        {\n            return;\n        }\n        else if (totalChildren > maxSize && !container.autoResize)\n        {\n            totalChildren = maxSize;\n        }\n\n        let buffers = container._buffers;\n\n        if (!buffers)\n        {\n            buffers = container._buffers = this.generateBuffers(container);\n        }\n\n        const baseTexture = children[0]._texture.baseTexture;\n        const premultiplied = baseTexture.alphaMode > 0;\n\n        // if the uvs have not updated then no point rendering just yet!\n        this.state.blendMode = correctBlendMode(container.blendMode, premultiplied);\n        renderer.state.set(this.state);\n\n        const gl = renderer.gl;\n\n        const m = container.worldTransform.copyTo(this.tempMatrix);\n\n        m.prepend(renderer.globalUniforms.uniforms.projectionMatrix);\n\n        this.shader.uniforms.translationMatrix = m.toArray(true);\n\n        this.shader.uniforms.uColor = premultiplyRgba(container.tintRgb,\n            container.worldAlpha, this.shader.uniforms.uColor, premultiplied);\n\n        this.shader.uniforms.uSampler = baseTexture;\n\n        this.renderer.shader.bind(this.shader);\n\n        let updateStatic = false;\n\n        // now lets upload and render the buffers..\n        for (let i = 0, j = 0; i < totalChildren; i += batchSize, j += 1)\n        {\n            let amount = (totalChildren - i);\n\n            if (amount > batchSize)\n            {\n                amount = batchSize;\n            }\n\n            if (j >= buffers.length)\n            {\n                buffers.push(this._generateOneMoreBuffer(container));\n            }\n\n            const buffer = buffers[j];\n\n            // we always upload the dynamic\n            buffer.uploadDynamic(children, i, amount);\n\n            const bid = container._bufferUpdateIDs[j] || 0;\n\n            updateStatic = updateStatic || (buffer._updateID < bid);\n            // we only upload the static content when we have to!\n            if (updateStatic)\n            {\n                buffer._updateID = container._updateID;\n                buffer.uploadStatic(children, i, amount);\n            }\n\n            // bind the buffer\n            renderer.geometry.bind(buffer.geometry);\n            gl.drawElements(gl.TRIANGLES, amount * 6, gl.UNSIGNED_SHORT, 0);\n        }\n    }\n\n    /**\n     * Creates one particle buffer for each child in the container we want to render and updates internal properties.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The buffers\n     */\n    private generateBuffers(container: ParticleContainer): ParticleBuffer[]\n    {\n        const buffers = [];\n        const size = container._maxSize;\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        for (let i = 0; i < size; i += batchSize)\n        {\n            buffers.push(new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize));\n        }\n\n        return buffers;\n    }\n\n    /**\n     * Creates one more particle buffer, because container has autoResize feature.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The generated buffer\n     */\n    private _generateOneMoreBuffer(container: ParticleContainer): ParticleBuffer\n    {\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        return new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize);\n    }\n\n    /**\n     * Uploads the vertices.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their vertices uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadVertices(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        let w0 = 0;\n        let w1 = 0;\n        let h0 = 0;\n        let h1 = 0;\n\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const texture = sprite._texture;\n            const sx = sprite.scale.x;\n            const sy = sprite.scale.y;\n            const trim = texture.trim;\n            const orig = texture.orig;\n\n            if (trim)\n            {\n                // if the sprite is trimmed and is not a tilingsprite then we need to add the\n                // extra space before transforming the sprite coords..\n                w1 = trim.x - (sprite.anchor.x * orig.width);\n                w0 = w1 + trim.width;\n\n                h1 = trim.y - (sprite.anchor.y * orig.height);\n                h0 = h1 + trim.height;\n            }\n            else\n            {\n                w0 = (orig.width) * (1 - sprite.anchor.x);\n                w1 = (orig.width) * -sprite.anchor.x;\n\n                h0 = orig.height * (1 - sprite.anchor.y);\n                h1 = orig.height * -sprite.anchor.y;\n            }\n\n            array[offset] = w1 * sx;\n            array[offset + 1] = h1 * sy;\n\n            array[offset + stride] = w0 * sx;\n            array[offset + stride + 1] = h1 * sy;\n\n            array[offset + (stride * 2)] = w0 * sx;\n            array[offset + (stride * 2) + 1] = h0 * sy;\n\n            array[offset + (stride * 3)] = w1 * sx;\n            array[offset + (stride * 3) + 1] = h0 * sy;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the position.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their positions uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadPosition(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spritePosition = children[startIndex + i].position;\n\n            array[offset] = spritePosition.x;\n            array[offset + 1] = spritePosition.y;\n\n            array[offset + stride] = spritePosition.x;\n            array[offset + stride + 1] = spritePosition.y;\n\n            array[offset + (stride * 2)] = spritePosition.x;\n            array[offset + (stride * 2) + 1] = spritePosition.y;\n\n            array[offset + (stride * 3)] = spritePosition.x;\n            array[offset + (stride * 3) + 1] = spritePosition.y;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the rotation.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadRotation(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spriteRotation = children[startIndex + i].rotation;\n\n            array[offset] = spriteRotation;\n            array[offset + stride] = spriteRotation;\n            array[offset + (stride * 2)] = spriteRotation;\n            array[offset + (stride * 3)] = spriteRotation;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the UVs.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadUvs(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const textureUvs = children[startIndex + i]._texture._uvs;\n\n            if (textureUvs)\n            {\n                array[offset] = textureUvs.x0;\n                array[offset + 1] = textureUvs.y0;\n\n                array[offset + stride] = textureUvs.x1;\n                array[offset + stride + 1] = textureUvs.y1;\n\n                array[offset + (stride * 2)] = textureUvs.x2;\n                array[offset + (stride * 2) + 1] = textureUvs.y2;\n\n                array[offset + (stride * 3)] = textureUvs.x3;\n                array[offset + (stride * 3) + 1] = textureUvs.y3;\n\n                offset += stride * 4;\n            }\n            else\n            {\n                // TODO you know this can be easier!\n                array[offset] = 0;\n                array[offset + 1] = 0;\n\n                array[offset + stride] = 0;\n                array[offset + stride + 1] = 0;\n\n                array[offset + (stride * 2)] = 0;\n                array[offset + (stride * 2) + 1] = 0;\n\n                array[offset + (stride * 3)] = 0;\n                array[offset + (stride * 3) + 1] = 0;\n\n                offset += stride * 4;\n            }\n        }\n    }\n\n    /**\n     * Uploads the tint.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadTint(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const premultiplied = sprite._texture.baseTexture.alphaMode > 0;\n            const alpha = sprite.alpha;\n\n            // we dont call extra function if alpha is 1.0, that's faster\n            const argb = alpha < 1.0 && premultiplied\n                ? premultiplyTint(sprite._tintRGB, alpha) : sprite._tintRGB + (alpha * 255 << 24);\n\n            array[offset] = argb;\n            array[offset + stride] = argb;\n            array[offset + (stride * 2)] = argb;\n            array[offset + (stride * 3)] = argb;\n\n            offset += stride * 4;\n        }\n    }\n\n    /** Destroys the ParticleRenderer. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        if (this.shader)\n        {\n            this.shader.destroy();\n            this.shader = null;\n        }\n\n        this.tempMatrix = null;\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;ACPA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACH,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;IAAuC,SAAiB,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;AAiEpD;;;;;;;;;;;;;AAaG;AACH,IAAA,SAAA,iBAAA,CAAY,OAAc,EAAE,UAAgC,EAAE,SAAiB,EAAE,UAAkB,EAAA;AAAvF,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAc,GAAA,IAAA,CAAA,EAAA;AAAoC,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAiB,GAAA,KAAA,CAAA,EAAA;AAAE,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAkB,GAAA,KAAA,CAAA,EAAA;AAAnG,QAAA,IAAA,KAAA,GAEI,iBAAO,IA8BV,IAAA,CAAA;;;;QAzBG,IAAM,YAAY,GAAG,KAAK,CAAC;QAE3B,IAAI,SAAS,GAAG,YAAY,EAC5B;YACI,SAAS,GAAG,YAAY,CAAC;AAC5B,SAAA;AAED,QAAA,KAAI,CAAC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACtD,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AACxB,QAAA,KAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,KAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC3B,QAAA,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AAEnB,QAAA,KAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AACjC,QAAA,KAAI,CAAC,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC;AACpC,QAAA,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAExB,QAAA,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAE/B,QAAA,KAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,KAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AACnC,QAAA,KAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;;KACxB;AAED;;;AAGG;IACI,iBAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,UAA+B,EAAA;AAEhD,QAAA,IAAI,UAAU,EACd;AACI,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,UAAU,IAAI,OAAO,IAAI,UAAU;kBACjE,CAAC,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC7F,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC7F,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACnF,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,OAAO,IAAI,UAAU;kBAC7D,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACvE,SAAA;KACJ,CAAA;AAED,IAAA,iBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;;QAGI,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACvC,CAAA;AAQD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AANR;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AAED,QAAA,GAAA,EAAA,UAAS,KAAa,EAAA;AAElB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,YAAA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAChC;;;AANA,KAAA,CAAA,CAAA;AAQD;;;AAGG;IACI,iBAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,QAAkB,EAAA;QAAhC,IAkBC,KAAA,GAAA,IAAA,CAAA;QAhBG,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EACtF;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EACrB;AACI,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;AACzD,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAC3B;AACI,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAM,EAAA,OAAA,KAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAxB,EAAwB,CAAC,CAAC;AACnE,aAAA;AACJ,SAAA;QAED,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5D,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC1C,CAAA;AAED;;;AAGG;IACO,iBAAgB,CAAA,SAAA,CAAA,gBAAA,GAA1B,UAA2B,kBAA0B,EAAA;AAEjD,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;AAErE,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,WAAW,EACjD;AACI,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjC,SAAA;QACD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC;KACzD,CAAA;AAEM,IAAA,iBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,IAAI,IAAI,CAAC,QAAQ,EACjB;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAC7C;gBACI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC9B,aAAA;AAED,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACxB,SAAA;KACJ,CAAA;AAED;;;;;;;;;;AAUG;IACI,iBAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;AAE9C,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;QAEvB,IAAI,CAAC,OAAO,EAAE,CAAC;AAEf,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;KAChC,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAnOA,CAAuC,SAAS,CAmO/C;;ACxQD;;;;;;;;;;AAUG;AAEH;;;;AAIG;AACH,IAAA,cAAA,kBAAA,YAAA;AAyBI;;;;AAIG;AACH,IAAA,SAAA,cAAA,CAAY,UAAuC,EAAE,oBAA+B,EAAE,IAAY,EAAA;AAE9F,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAExB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAE3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAC1C;AACI,YAAA,IAAI,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;;;AAI7B,YAAA,QAAQ,GAAG;gBACP,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,cAAc,EAAE,QAAQ,CAAC,cAAc;AACvC,gBAAA,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK;gBAClC,MAAM,EAAE,QAAQ,CAAC,MAAM;aAC1B,CAAC;AAEF,YAAA,IAAI,oBAAoB,CAAC,CAAC,CAAC,EAC3B;AACI,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AACtB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAE7B,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAE9B,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC,WAAW,EAAE,CAAC;KACtB;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;AAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,IAAI,aAAa,GAAG,CAAC,CAAC;AAEtB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5E,QAAA,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAEpC,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAEvB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,EACtD;YACI,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAE3C,YAAA,QAAQ,CAAC,MAAM,GAAG,aAAa,CAAC;AAChC,YAAA,aAAa,IAAI,QAAQ,CAAC,IAAI,CAAC;AAC/B,YAAA,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC,IAAI,CAAC;AACvC,SAAA;AAED,QAAA,IAAM,SAAS,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1E,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;;QAGhE,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AAEtB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EACrD;YACI,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAE1C,YAAA,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC;AAC/B,YAAA,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC;AAC9B,YAAA,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC;AACtC,SAAA;AAED,QAAA,IAAM,UAAU,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1E,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAE7D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,EACtD;YACI,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAE3C,YAAA,QAAQ,CAAC,YAAY,CACjB,QAAQ,CAAC,aAAa,EACtB,IAAI,CAAC,aAAa,EAClB,CAAC,EACD,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,aAAa,EACrC,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,aAAa,GAAG,CAAC,EACtB,QAAQ,CAAC,MAAM,GAAG,CAAC,CACtB,CAAC;AACL,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EACrD;YACI,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAE1C,YAAA,QAAQ,CAAC,YAAY,CACjB,QAAQ,CAAC,aAAa,EACtB,IAAI,CAAC,YAAY,EACjB,CAAC,EACD,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,aAAa,EACrC,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,YAAY,GAAG,CAAC,EACrB,QAAQ,CAAC,MAAM,GAAG,CAAC,CACtB,CAAC;AACL,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACH,IAAA,cAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,QAAkB,EAAE,UAAkB,EAAE,MAAc,EAAA;AAEhE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EACtD;YACI,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAE3C,YAAA,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAChD,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,EACjF,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;KAClC,CAAA;AAED;;;;;AAKG;AACH,IAAA,cAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,QAAkB,EAAE,UAAkB,EAAE,MAAc,EAAA;AAE/D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EACrD;YACI,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAE1C,YAAA,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAChD,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,EAC/E,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3C,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;KACjC,CAAA;;AAGD,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AAEI,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAExB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC9B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAE9B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;;AAE7B,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;KAC3B,CAAA;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;;;;;ACzND;;;;;;;;;;AAUG;AAEH;;;AAGG;AACH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;IAAsC,SAAc,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;AAgBhD;;AAEG;AACH,IAAA,SAAA,gBAAA,CAAY,QAAkB,EAAA;QAA9B,IAEI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,QAAQ,CAAC,IAuDlB,IAAA,CAAA;;;;;;AA/CG,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAEnB,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAEvB,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;QAE/B,KAAI,CAAC,UAAU,GAAG;;AAEd,YAAA;AACI,gBAAA,aAAa,EAAE,iBAAiB;AAChC,gBAAA,IAAI,EAAE,CAAC;gBACP,cAAc,EAAE,KAAI,CAAC,cAAc;AACnC,gBAAA,MAAM,EAAE,CAAC;AACZ,aAAA;;AAED,YAAA;AACI,gBAAA,aAAa,EAAE,gBAAgB;AAC/B,gBAAA,IAAI,EAAE,CAAC;gBACP,cAAc,EAAE,KAAI,CAAC,cAAc;AACnC,gBAAA,MAAM,EAAE,CAAC;AACZ,aAAA;;AAED,YAAA;AACI,gBAAA,aAAa,EAAE,WAAW;AAC1B,gBAAA,IAAI,EAAE,CAAC;gBACP,cAAc,EAAE,KAAI,CAAC,cAAc;AACnC,gBAAA,MAAM,EAAE,CAAC;AACZ,aAAA;;AAED,YAAA;AACI,gBAAA,aAAa,EAAE,eAAe;AAC9B,gBAAA,IAAI,EAAE,CAAC;gBACP,cAAc,EAAE,KAAI,CAAC,SAAS;AAC9B,gBAAA,MAAM,EAAE,CAAC;AACZ,aAAA;;AAED,YAAA;AACI,gBAAA,aAAa,EAAE,QAAQ;AACvB,gBAAA,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,KAAK,CAAC,aAAa;gBACzB,cAAc,EAAE,KAAI,CAAC,UAAU;AAC/B,gBAAA,MAAM,EAAE,CAAC;AACZ,aAAA,EACJ,CAAC;AAEF,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;AAChD,QAAA,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;;KAC9B;AAED;;;AAGG;IACI,gBAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,SAA4B,EAAA;AAEtC,QAAA,IAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AACpC,QAAA,IAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;AACnC,QAAA,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;AACvC,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,QAAA,IAAI,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEpC,IAAI,aAAa,KAAK,CAAC,EACvB;YACI,OAAO;AACV,SAAA;aACI,IAAI,aAAa,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EACzD;YACI,aAAa,GAAG,OAAO,CAAC;AAC3B,SAAA;AAED,QAAA,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;QAEjC,IAAI,CAAC,OAAO,EACZ;YACI,OAAO,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAClE,SAAA;QAED,IAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;AACrD,QAAA,IAAM,aAAa,GAAG,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;;AAGhD,QAAA,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAC5E,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAE/B,QAAA,IAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AAEvB,QAAA,IAAM,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3D,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AAE7D,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC,OAAO,EAC3D,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC;QAE5C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvC,IAAI,YAAY,GAAG,KAAK,CAAC;;QAGzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,IAAI,CAAC,EAChE;AACI,YAAA,IAAI,MAAM,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;YAEjC,IAAI,MAAM,GAAG,SAAS,EACtB;gBACI,MAAM,GAAG,SAAS,CAAC;AACtB,aAAA;AAED,YAAA,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EACvB;gBACI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC;AACxD,aAAA;AAED,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;;YAG1B,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAE1C,IAAM,GAAG,GAAG,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAE/C,YAAY,GAAG,YAAY,KAAK,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;;AAExD,YAAA,IAAI,YAAY,EAChB;AACI,gBAAA,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;gBACvC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC5C,aAAA;;YAGD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACxC,YAAA,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;AACnE,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACK,gBAAe,CAAA,SAAA,CAAA,eAAA,GAAvB,UAAwB,SAA4B,EAAA;QAEhD,IAAM,OAAO,GAAG,EAAE,CAAC;AACnB,QAAA,IAAM,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;AAChC,QAAA,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;AACvC,QAAA,IAAM,oBAAoB,GAAG,SAAS,CAAC,WAAW,CAAC;AAEnD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,SAAS,EACxC;AACI,YAAA,OAAO,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE,SAAS,CAAC,CAAC,CAAC;AACtF,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB,CAAA;AAED;;;;AAIG;IACK,gBAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,SAA4B,EAAA;AAEvD,QAAA,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;AACvC,QAAA,IAAM,oBAAoB,GAAG,SAAS,CAAC,WAAW,CAAC;QAEnD,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE,SAAS,CAAC,CAAC;KAC/E,CAAA;AAED;;;;;;;;AAQG;AACI,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAArB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;QAG/C,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;QAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAC/B;YACI,IAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AACxC,YAAA,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC;AAChC,YAAA,IAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1B,YAAA,IAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1B,YAAA,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC1B,YAAA,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAE1B,YAAA,IAAI,IAAI,EACR;;;AAGI,gBAAA,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7C,gBAAA,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AAErB,gBAAA,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9C,gBAAA,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,aAAA;AAED,iBAAA;AACI,gBAAA,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1C,gBAAA,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAErC,gBAAA,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACzC,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AACvC,aAAA;AAED,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;YACxB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;YAE5B,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;YACjC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAErC,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACvC,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAE3C,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACvC,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAE3C,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;AACxB,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;AACI,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAArB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;QAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAC/B;YACI,IAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;AAEzD,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;YACjC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;YAErC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;YAC1C,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;AAE9C,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;AAChD,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;AAEpD,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;AAChD,YAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;AAEpD,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;AACxB,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;AACI,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAArB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;QAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAC/B;YACI,IAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;AAEzD,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC;AAC/B,YAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,cAAc,CAAC;YACxC,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;YAC9C,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;AAE9C,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;AACxB,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;AACI,IAAA,gBAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;QAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAC/B;AACI,YAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;AAE1D,YAAA,IAAI,UAAU,EACd;AACI,gBAAA,KAAK,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;gBAC9B,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;gBAElC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;gBACvC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAE3C,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAC7C,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAEjD,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAC7C,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAEjD,gBAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;AACxB,aAAA;AAED,iBAAA;;AAEI,gBAAA,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAClB,gBAAA,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAEtB,gBAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC3B,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBAE/B,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACjC,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBAErC,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACjC,gBAAA,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAErC,gBAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;AACxB,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;AACI,IAAA,gBAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,UACI,QAAkB,EAAE,UAAkB,EAAE,MAAc,EACtD,KAAe,EAAE,MAAc,EAAE,MAAc,EAAA;QAG/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAC/B;YACI,IAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;AAChE,YAAA,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;;AAG3B,YAAA,IAAM,IAAI,GAAG,KAAK,GAAG,GAAG,IAAI,aAAa;kBACnC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,IAAI,KAAK,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;AAEtF,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACrB,YAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC;YAC9B,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YACpC,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAEpC,YAAA,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC;AACxB,SAAA;KACJ,CAAA;;AAGM,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,MAAM,CAAA,SAAA,CAAA,OAAO,WAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,MAAM,EACf;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B,CAAA;;AAnaM,IAAA,gBAAA,CAAA,SAAS,GAAsB;AAClC,QAAA,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,aAAa,CAAC,cAAc;KACrC,CAAC;IAiaN,OAAC,gBAAA,CAAA;CAAA,CAvaqC,cAAc,CAuanD;;;;"}