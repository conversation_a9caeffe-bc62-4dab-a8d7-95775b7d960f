/*!
 * @pixi/constants - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/constants is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";var E,_,T,R,N,A,I,S,O,L,P,U,D,M,o,G,C,e,p,r;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ENV=void 0,(E=exports.ENV||(exports.ENV={}))[E.WEBGL_LEGACY=0]="WEBGL_LEGACY",E[E.WEBGL=1]="WEBGL",E[E.WEBGL2=2]="WEBGL2",exports.RENDERER_TYPE=void 0,(_=exports.RENDERER_TYPE||(exports.RENDERER_TYPE={}))[_.UNKNOWN=0]="UNKNOWN",_[_.WEBGL=1]="WEBGL",_[_.CANVAS=2]="CANVAS",exports.BUFFER_BITS=void 0,(T=exports.BUFFER_BITS||(exports.BUFFER_BITS={}))[T.COLOR=16384]="COLOR",T[T.DEPTH=256]="DEPTH",T[T.STENCIL=1024]="STENCIL",exports.BLEND_MODES=void 0,(R=exports.BLEND_MODES||(exports.BLEND_MODES={}))[R.NORMAL=0]="NORMAL",R[R.ADD=1]="ADD",R[R.MULTIPLY=2]="MULTIPLY",R[R.SCREEN=3]="SCREEN",R[R.OVERLAY=4]="OVERLAY",R[R.DARKEN=5]="DARKEN",R[R.LIGHTEN=6]="LIGHTEN",R[R.COLOR_DODGE=7]="COLOR_DODGE",R[R.COLOR_BURN=8]="COLOR_BURN",R[R.HARD_LIGHT=9]="HARD_LIGHT",R[R.SOFT_LIGHT=10]="SOFT_LIGHT",R[R.DIFFERENCE=11]="DIFFERENCE",R[R.EXCLUSION=12]="EXCLUSION",R[R.HUE=13]="HUE",R[R.SATURATION=14]="SATURATION",R[R.COLOR=15]="COLOR",R[R.LUMINOSITY=16]="LUMINOSITY",R[R.NORMAL_NPM=17]="NORMAL_NPM",R[R.ADD_NPM=18]="ADD_NPM",R[R.SCREEN_NPM=19]="SCREEN_NPM",R[R.NONE=20]="NONE",R[R.SRC_OVER=0]="SRC_OVER",R[R.SRC_IN=21]="SRC_IN",R[R.SRC_OUT=22]="SRC_OUT",R[R.SRC_ATOP=23]="SRC_ATOP",R[R.DST_OVER=24]="DST_OVER",R[R.DST_IN=25]="DST_IN",R[R.DST_OUT=26]="DST_OUT",R[R.DST_ATOP=27]="DST_ATOP",R[R.ERASE=26]="ERASE",R[R.SUBTRACT=28]="SUBTRACT",R[R.XOR=29]="XOR",exports.DRAW_MODES=void 0,(N=exports.DRAW_MODES||(exports.DRAW_MODES={}))[N.POINTS=0]="POINTS",N[N.LINES=1]="LINES",N[N.LINE_LOOP=2]="LINE_LOOP",N[N.LINE_STRIP=3]="LINE_STRIP",N[N.TRIANGLES=4]="TRIANGLES",N[N.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",N[N.TRIANGLE_FAN=6]="TRIANGLE_FAN",exports.FORMATS=void 0,(A=exports.FORMATS||(exports.FORMATS={}))[A.RGBA=6408]="RGBA",A[A.RGB=6407]="RGB",A[A.RG=33319]="RG",A[A.RED=6403]="RED",A[A.RGBA_INTEGER=36249]="RGBA_INTEGER",A[A.RGB_INTEGER=36248]="RGB_INTEGER",A[A.RG_INTEGER=33320]="RG_INTEGER",A[A.RED_INTEGER=36244]="RED_INTEGER",A[A.ALPHA=6406]="ALPHA",A[A.LUMINANCE=6409]="LUMINANCE",A[A.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",A[A.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",A[A.DEPTH_STENCIL=34041]="DEPTH_STENCIL",exports.TARGETS=void 0,(I=exports.TARGETS||(exports.TARGETS={}))[I.TEXTURE_2D=3553]="TEXTURE_2D",I[I.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",I[I.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY",I[I.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",I[I.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",I[I.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",I[I.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",I[I.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",I[I.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",exports.TYPES=void 0,(S=exports.TYPES||(exports.TYPES={}))[S.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",S[S.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",S[S.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",S[S.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",S[S.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",S[S.UNSIGNED_INT=5125]="UNSIGNED_INT",S[S.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",S[S.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",S[S.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",S[S.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",S[S.BYTE=5120]="BYTE",S[S.SHORT=5122]="SHORT",S[S.INT=5124]="INT",S[S.FLOAT=5126]="FLOAT",S[S.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV",S[S.HALF_FLOAT=36193]="HALF_FLOAT",exports.SAMPLER_TYPES=void 0,(O=exports.SAMPLER_TYPES||(exports.SAMPLER_TYPES={}))[O.FLOAT=0]="FLOAT",O[O.INT=1]="INT",O[O.UINT=2]="UINT",exports.SCALE_MODES=void 0,(L=exports.SCALE_MODES||(exports.SCALE_MODES={}))[L.NEAREST=0]="NEAREST",L[L.LINEAR=1]="LINEAR",exports.WRAP_MODES=void 0,(P=exports.WRAP_MODES||(exports.WRAP_MODES={}))[P.CLAMP=33071]="CLAMP",P[P.REPEAT=10497]="REPEAT",P[P.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT",exports.MIPMAP_MODES=void 0,(U=exports.MIPMAP_MODES||(exports.MIPMAP_MODES={}))[U.OFF=0]="OFF",U[U.POW2=1]="POW2",U[U.ON=2]="ON",U[U.ON_MANUAL=3]="ON_MANUAL",exports.ALPHA_MODES=void 0,(D=exports.ALPHA_MODES||(exports.ALPHA_MODES={}))[D.NPM=0]="NPM",D[D.UNPACK=1]="UNPACK",D[D.PMA=2]="PMA",D[D.NO_PREMULTIPLIED_ALPHA=0]="NO_PREMULTIPLIED_ALPHA",D[D.PREMULTIPLY_ON_UPLOAD=1]="PREMULTIPLY_ON_UPLOAD",D[D.PREMULTIPLY_ALPHA=2]="PREMULTIPLY_ALPHA",D[D.PREMULTIPLIED_ALPHA=2]="PREMULTIPLIED_ALPHA",exports.CLEAR_MODES=void 0,(M=exports.CLEAR_MODES||(exports.CLEAR_MODES={}))[M.NO=0]="NO",M[M.YES=1]="YES",M[M.AUTO=2]="AUTO",M[M.BLEND=0]="BLEND",M[M.CLEAR=1]="CLEAR",M[M.BLIT=2]="BLIT",exports.GC_MODES=void 0,(o=exports.GC_MODES||(exports.GC_MODES={}))[o.AUTO=0]="AUTO",o[o.MANUAL=1]="MANUAL",exports.PRECISION=void 0,(G=exports.PRECISION||(exports.PRECISION={})).LOW="lowp",G.MEDIUM="mediump",G.HIGH="highp",exports.MASK_TYPES=void 0,(C=exports.MASK_TYPES||(exports.MASK_TYPES={}))[C.NONE=0]="NONE",C[C.SCISSOR=1]="SCISSOR",C[C.STENCIL=2]="STENCIL",C[C.SPRITE=3]="SPRITE",C[C.COLOR=4]="COLOR",exports.COLOR_MASK_BITS=void 0,(e=exports.COLOR_MASK_BITS||(exports.COLOR_MASK_BITS={}))[e.RED=1]="RED",e[e.GREEN=2]="GREEN",e[e.BLUE=4]="BLUE",e[e.ALPHA=8]="ALPHA",exports.MSAA_QUALITY=void 0,(p=exports.MSAA_QUALITY||(exports.MSAA_QUALITY={}))[p.NONE=0]="NONE",p[p.LOW=2]="LOW",p[p.MEDIUM=4]="MEDIUM",p[p.HIGH=8]="HIGH",exports.BUFFER_TYPE=void 0,(r=exports.BUFFER_TYPE||(exports.BUFFER_TYPE={}))[r.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",r[r.ARRAY_BUFFER=34962]="ARRAY_BUFFER",r[r.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER";
//# sourceMappingURL=constants.min.js.map
