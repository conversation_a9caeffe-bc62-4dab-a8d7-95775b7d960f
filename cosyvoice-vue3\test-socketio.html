<!DOCTYPE html>
<html>
<head>
    <title>Socket.IO连接测试</title>
    <script src="https://cdn.socket.io/4.6.1/socket.io.min.js"></script>
</head>
<body>
    <h1>Socket.IO连接测试</h1>
    <div id="status">正在连接...</div>
    <div id="log"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        
        function log(message) {
            console.log(message);
            logDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
        }
        
        // 测试连接到后端Socket.IO服务器
        log('尝试连接到后端Socket.IO服务器 (localhost:7860)...');
        
        const socket = io('http://localhost:7860', {
            transports: ['websocket', 'polling'],
            forceNew: true
        });
        
        socket.on('connect', () => {
            statusDiv.textContent = '✅ 已连接到后端Socket.IO服务器';
            statusDiv.style.color = 'green';
            log('✅ Socket.IO连接成功，连接ID: ' + socket.id);
        });
        
        socket.on('disconnect', (reason) => {
            statusDiv.textContent = '❌ 连接已断开: ' + reason;
            statusDiv.style.color = 'red';
            log('❌ Socket.IO连接断开: ' + reason);
        });
        
        socket.on('connect_error', (error) => {
            statusDiv.textContent = '❌ 连接失败: ' + error.message;
            statusDiv.style.color = 'red';
            log('❌ Socket.IO连接错误: ' + error.message);
        });
        
        socket.on('connected', (data) => {
            log('📡 收到connected事件: ' + JSON.stringify(data));
        });
        
        // 10秒后尝试发送测试消息
        setTimeout(() => {
            if (socket.connected) {
                log('📤 发送测试消息...');
                socket.emit('test_message', { message: 'Hello from test page' });
            }
        }, 10000);
    </script>
</body>
</html>