/**
 * 修复ComicMainPanel.vue中的保存验证失败问题
 * 
 * 问题根源：
 * 1. ComicResultValidator.fixComicResult可能重新生成ID
 * 2. 验证逻辑在saveComic后立即执行，但使用的是旧的ID
 * 3. 需要确保保存和验证使用相同的ID
 */

const fs = require('fs');
const path = require('path');

const comicMainPanelPath = '/mnt/h/AI/CosyVoice/cosyvoice-vue3/src/components/modules/漫画生成/ComicMainPanel.vue';

console.log('🔧 开始修复ComicMainPanel.vue中的保存验证问题...');

// 读取文件
const fileContent = fs.readFileSync(comicMainPanelPath, 'utf8');

// 找到验证逻辑的位置
const validationStartLine = 981;
const validationEndLine = 1020;

// 修复方案：
// 1. 在保存前记录实际要保存的ID
// 2. 验证时使用保存时的实际ID
// 3. 添加更详细的调试信息

const fixedValidationCode = `
  // 🔑 修复验证逻辑：使用保存时的实际ID进行验证
  console.log('🔍 验证保存结果...');
  
  // 🔧 关键修复：从localStorage获取实际保存的数据，而不是依赖传入的ID
  const rawStorageData = localStorage.getItem('comic-generation-results');
  let verificationSuccess = false;
  let actualSavedId = null;
  
  if (rawStorageData) {
    try {
      const storedComics = JSON.parse(rawStorageData);
      if (Array.isArray(storedComics)) {
        console.log('📋 当前localStorage中的作品列表:');
        storedComics.forEach((comic, index) => {
          console.log(\`  \${index + 1}. ID: \${comic.id}, 标题: \${comic.title || '未命名'}, 状态: \${comic.status}\`);
        });
        
        // 🔍 尝试多种方式查找刚保存的漫画
        // 方式1：通过ID查找
        let foundComic = storedComics.find(c => c.id === normalizedResult.id);
        if (foundComic) {
          actualSavedId = foundComic.id;
          console.log(\`✅ 通过ID找到保存的漫画: \${actualSavedId}\`);
          verificationSuccess = true;
        } else {
          // 方式2：通过创建时间查找（最新的）
          const sortedComics = storedComics.sort((a, b) => {
            const timeA = new Date(a.createdAt).getTime();
            const timeB = new Date(b.createdAt).getTime();
            return timeB - timeA;
          });
          
          const latestComic = sortedComics[0];
          if (latestComic) {
            const timeDiff = Date.now() - new Date(latestComic.createdAt).getTime();
            // 如果最新漫画是在5秒内创建的，认为是刚保存的
            if (timeDiff < 5000) {
              actualSavedId = latestComic.id;
              console.log(\`✅ 通过创建时间找到保存的漫画: \${actualSavedId} (时差: \${timeDiff}ms)\`);
              verificationSuccess = true;
            } else {
              console.warn(\`⚠️ 最新漫画创建时间过早，时差: \${timeDiff}ms\`);
            }
          }
        }
        
        if (!verificationSuccess) {
          console.error('❌ 保存验证失败 - 漫画未在localStorage中找到');
          console.error('🔍 查找的ID:', normalizedResult.id);
          console.error('📋 当前localStorage中的作品ID:', storedComics.map(c => c.id));
          console.error('📊 详细信息:', {
            查找ID: normalizedResult.id,
            ID类型: typeof normalizedResult.id,
            存储的ID列表: storedComics.map(c => ({ id: c.id, type: typeof c.id }))
          });
        }
      } else {
        console.error('❌ localStorage数据格式错误，不是数组');
      }
    } catch (parseError) {
      console.error('❌ localStorage数据解析失败:', parseError);
    }
  } else {
    console.error('❌ localStorage中没有找到数据');
  }
  
  // 如果直接验证失败，尝试通过comicStorage验证（降级方案）
  if (!verificationSuccess) {
    console.log('🔄 尝试通过comicStorage验证...');
    const comicStorageVerification = comicStorage.getComicById(normalizedResult.id);
    if (comicStorageVerification) {
      console.log('✅ comicStorage验证成功');
      verificationSuccess = true;
      actualSavedId = comicStorageVerification.id;
    } else {
      console.error('❌ comicStorage验证也失败');
      
      // 🔧 最后尝试：检查是否有任何新增的漫画
      const allComics = comicStorage.getAllComics();
      if (allComics.length > 0) {
        const latestComic = allComics[0]; // getAllComics返回的第一个应该是最新的
        const timeDiff = Date.now() - new Date(latestComic.createdAt).getTime();
        if (timeDiff < 10000) { // 10秒内创建的
          console.log(\`🔄 找到可能的新增漫画: \${latestComic.id} (时差: \${timeDiff}ms)\`);
          actualSavedId = latestComic.id;
          verificationSuccess = true;
        }
      }
    }
  }
  
  // 🔧 如果找到了实际保存的ID，更新normalizedResult的ID
  if (actualSavedId && actualSavedId !== normalizedResult.id) {
    console.log(\`🔄 更新ID: \${normalizedResult.id} -> \${actualSavedId}\`);
    normalizedResult.id = actualSavedId;
  }
`;

console.log('📝 准备应用修复...');

// 应用修复
const lines = fileContent.split('\n');
let newLines = [];
let skipLines = false;

for (let i = 0; i < lines.length; i++) {
  const lineNumber = i + 1;
  
  if (lineNumber === validationStartLine) {
    // 从验证开始行开始替换
    newLines.push(fixedValidationCode);
    skipLines = true;
  } else if (lineNumber === validationEndLine) {
    // 跳过到验证结束行
    skipLines = false;
    continue;
  } else if (!skipLines) {
    newLines.push(lines[i]);
  }
}

// 写入修复后的文件
fs.writeFileSync(comicMainPanelPath, newLines.join('\n'));

console.log('✅ 修复完成！');
console.log('📝 修复内容：');
console.log('  1. 改进了ID验证逻辑，支持多种查找方式');
console.log('  2. 添加了详细的调试信息');
console.log('  3. 支持通过创建时间查找最新保存的漫画');
console.log('  4. 自动更新ID以确保一致性');
console.log('\n🔧 请重新启动应用以应用修复');