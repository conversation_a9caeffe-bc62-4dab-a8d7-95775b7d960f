[1] 2025-07-14 13:12:34,680 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-14 13:12:34,680 - api_bridge - INFO -   - 连接ID列表: ['conn_1', 'conn_3']
[1] 2025-07-14 13:12:34,680 - api_bridge - INFO - 🎵 流式音频片段: chunk 16/20
[1] 2025-07-14 13:12:34,680 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-14 13:12:34,745 - api_bridge - INFO - ✅ TTS音频WebSocket事件发送完成: None
[1] 2025-07-14 13:12:34,746 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 16/20 (8.84s)
[1] 2025-07-14 13:12:34,746 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-14 13:12:34,820 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-14 13:12:34,950 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2501, realtime_enabled: True
[1] 2025-07-14 13:12:34,950 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2501)
[1]
[1] [后端错误] 2025-07-14 13:12:35,330 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] 2025-07-14 13:12:36,550 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2551, realtime_enabled: True
[1] 2025-07-14 13:12:36,550 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2551)
[1]
[1] [后端错误] 2025-07-14 13:12:36,698 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=1.952s, 准备=0.000s, 网络传输=1.952s
[1] 2025-07-14 13:12:36,698 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=1.95秒, 响应大小=0.49MB, 传输速度=0.25MB/s
[1]
[1] [后端错误] 2025-07-14 13:12:36,699 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=10.74s, RTF=0.18, 解析=0.001s, 最终处理=0.000s
[1] 2025-07-14 13:12:36,699 - integrations.indextts_manager - INFO - 🎵 后续片段 17 生成完成，发送用于衔接 (10.74秒)
[1] 2025-07-14 13:12:36,699 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-14 13:12:36,699 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-14 13:12:36,699 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-14 13:12:36,699 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-14 13:12:36,699 - api_bridge - INFO -   - 连接ID列表: ['conn_3']
[1] 2025-07-14 13:12:36,699 - api_bridge - INFO - 🎵 流式音频片段: chunk 17/20
[1]
[1] [后端错误] 2025-07-14 13:12:36,699 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-14 13:12:36,742 - api_bridge - INFO - ✅ TTS音频WebSocket事件发送完成: None
[1]
[1] [后端错误] 2025-07-14 13:12:36,742 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 17/20 (10.74s)
[1]
[1] [后端错误] 2025-07-14 13:12:36,742 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-14 13:12:36,786 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 48320) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1] 2025-07-14 13:12:37,340 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-14 13:12:37,832 - integrations.indextts_manager - WARNING - 🔇 音频队列持续为空 (3600 次检查)
[1]
[1] [后端错误] 2025-07-14 13:12:37,913 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=1.170s, 准备=0.000s, 网络传输=1.170s
[1] 2025-07-14 13:12:37,913 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=1.17秒, 响应大小=0.28MB, 传输速度=0.24MB/s
[1]
[1] [后端错误] 2025-07-14 13:12:37,914 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=6.03s, RTF=0.19, 解析=0.000s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-14 13:12:37,914 - integrations.indextts_manager - INFO - 🎵 后续片段 18 生成完成，发送用于衔接 (6.03秒)
[1] 2025-07-14 13:12:37,914 - api_bridge - INFO - 🔊 开始播放音频
[1]
[1] [后端错误] 2025-07-14 13:12:37,914 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-14 13:12:37,914 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-14 13:12:37,914 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-14 13:12:37,914 - api_bridge - INFO -   - 连接ID列表: ['conn_3', 'conn_1']
[1]
[1] [后端错误] 2025-07-14 13:12:37,915 - api_bridge - INFO - 🎵 流式音频片段: chunk 18/20
[1] 2025-07-14 13:12:37,915 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-14 13:12:37,957 - api_bridge - INFO - ✅ TTS音频WebSocket事件发送完成: None
[1] 2025-07-14 13:12:37,958 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 18/20 (6.03s)
[1]
[1] [后端错误] 2025-07-14 13:12:37,958 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-14 13:12:38,005 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-14 13:12:38,150 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2601, realtime_enabled: True
[1] 2025-07-14 13:12:38,150 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2601)
[1]
[1] [后端错误] 2025-07-14 13:12:39,749 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2651, realtime_enabled: True
[1] 2025-07-14 13:12:39,749 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2651)
[1]
[1] [后端错误] 2025-07-14 13:12:40,646 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=2.689s, 准备=0.000s, 网络传输=2.689s
[1] 2025-07-14 13:12:40,646 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=2.69秒, 响应大小=0.67MB, 传输速度=0.25MB/s
[1]
[1] [后端错误] 2025-07-14 13:12:40,647 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=14.72s, RTF=0.18, 解析=0.001s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-14 13:12:40,647 - integrations.indextts_manager - INFO - 🎵 后续片段 19 生成完成，发送用于衔接 (14.72秒)
[1] 2025-07-14 13:12:40,647 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-14 13:12:40,647 - api_bridge - INFO - 🎵 接收到TTS音频
[1]
[1] [后端错误] 2025-07-14 13:12:40,647 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-14 13:12:40,647 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-14 13:12:40,647 - api_bridge - INFO -   - 连接ID列表: ['conn_3', 'conn_1']
[1] 2025-07-14 13:12:40,647 - api_bridge - INFO - 🎵 流式音频片段: chunk 19/20
[1]
[1] [后端错误] 2025-07-14 13:12:40,647 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-14 13:12:40,755 - api_bridge - INFO - ✅ TTS音频WebSocket事件发送完成: None
[1]
[1] [后端错误] 2025-07-14 13:12:40,755 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 19/20 (14.72s)
[1]
[1] [后端错误] 2025-07-14 13:12:40,755 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-14 13:12:40,878 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-14 13:12:41,349 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2701, realtime_enabled: True
[1] 2025-07-14 13:12:41,349 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2701)
[1]
[1] [后端错误] 2025-07-14 13:12:42,949 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2751, realtime_enabled: True
[1] 2025-07-14 13:12:42,950 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2751)
[1]
[1] [后端错误] 2025-07-14 13:12:43,071 - integrations.indextts_manager - WARNING - 🔇 音频队列持续为空 (4100 次检查)
[1]
[1] [后端错误] 2025-07-14 13:12:43,861 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=3.105s, 准备=0.000s, 网络传输=3.105s
[1] 2025-07-14 13:12:43,861 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=3.10秒, 响应大小=0.82MB, 传输速度=0.27MB/s
[1]
[1] [后端错误] 2025-07-14 13:12:43,862 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=18.00s, RTF=0.17, 解析=0.001s, 最终处理=0.000s
[1] 2025-07-14 13:12:43,862 - integrations.indextts_manager - INFO - 🎵 后续片段 20 生成完成，发送用于衔接 (18.00秒)
[1] 2025-07-14 13:12:43,862 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-14 13:12:43,862 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-14 13:12:43,862 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-14 13:12:43,862 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-14 13:12:43,862 - api_bridge - INFO -   - 连接ID列表: ['conn_3', 'conn_1']
[1] 2025-07-14 13:12:43,862 - api_bridge - INFO - 🎵 流式音频片段: chunk 20/20
[1] 2025-07-14 13:12:43,862 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-14 13:12:44,018 - api_bridge - INFO - ✅ TTS音频WebSocket事件发送完成: None
[1]
[1] [后端错误] 2025-07-14 13:12:44,019 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 20/20 (18.00s)
[1]
[1] [后端错误] 2025-07-14 13:12:44,019 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-14 13:12:44,019 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 20个片段, 总时长229.48秒
[1]
[1] [后端错误] 2025-07-14 13:12:44,020 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1] 2025-07-14 13:12:44,020 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1]
[1] [后端错误] 2025-07-14 13:12:44,161 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-14 13:12:44,550 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2801, realtime_enabled: True
[1] 2025-07-14 13:12:44,550 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2801)
[1]
[1] [后端错误] 2025-07-14 13:12:46,150 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2851, realtime_enabled: True
[1] 2025-07-14 13:12:46,150 - integrations.indextts_manager - WARNING - 🔇 VAD暂停中，丢弃音频数据 (#2851)
[1]
[1] [后端错误] 2025-07-14 13:12:47,750 - integrations.indextts_manager - INFO - 📞 音频回调调用 #2901, realtime_enabled: True