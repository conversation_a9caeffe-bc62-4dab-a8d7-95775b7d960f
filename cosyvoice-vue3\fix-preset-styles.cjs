#!/usr/bin/env node

/**
 * 预设风格数据迁移脚本
 * 将复杂文件名正确映射到预设风格ID
 */

const fs = require('fs');
const path = require('path');

// 预设风格列表
const presetStyles = [
  'anime', 'realistic', 'cartoon', 'sketch', 'watercolor', 
  'chinese', 'pixel', 'noir', 'cyberpunk', 'vintage',
  'manga', 'western', 'minimalist', 'fantasy', 'steampunk'
];

// 文件目录
const stylesDir = path.join(__dirname, 'public/styles');
const storeFile = path.join(__dirname, 'data/store.json');

console.log('🔧 开始修复预设风格数据映射...');

try {
  // 1. 扫描实际文件
  const files = fs.readdirSync(stylesDir)
    .filter(file => file.startsWith('style_style_') && file.endsWith('.png'))
    .filter(file => !file.includes('showcase')); // 排除展示图片

  console.log(`📁 找到 ${files.length} 个风格文件:`);
  files.forEach(file => console.log(`   - ${file}`));

  // 2. 建立映射关系
  const styleMapping = {};
  
  for (const file of files) {
    // 从文件名中提取风格ID
    const match = file.match(/style_style_([a-zA-Z0-9]+)_\d+_[a-zA-Z0-9]+\.png(?:\.png)?/);
    if (match && match[1]) {
      const styleId = match[1];
      if (presetStyles.includes(styleId)) {
        styleMapping[styleId] = `/styles/${file}`;
        console.log(`✅ 映射: ${styleId} → ${file}`);
      }
    }
  }

  console.log(`\n📦 创建预设风格数据结构:`);
  
  // 3. 创建正确的存储数据结构
  const presetStylesData = {};
  
  for (const styleId of Object.keys(styleMapping)) {
    presetStylesData[styleId] = {
      referenceImage: styleMapping[styleId],
      timestamp: Date.now()
    };
    console.log(`   - ${styleId}: ${styleMapping[styleId]}`);
  }

  // 4. 输出修复脚本
  console.log(`\n📝 生成的数据结构 (可在浏览器开发者工具中执行):`);
  console.log(`\n// 修复预设风格数据的JavaScript代码:`);
  console.log(`window.fixPresetStyles = async () => {`);
  console.log(`  const data = ${JSON.stringify(presetStylesData, null, 2)};`);
  console.log(`  if (window.electronAPI) {`);
  console.log(`    await window.electronAPI.setStore('preset-style-images', data);`);
  console.log(`    console.log('✅ Electron环境: 预设风格数据已修复');`);
  console.log(`  } else {`);
  console.log(`    console.log('⚠️ 非Electron环境，请在Electron中执行');`);
  console.log(`  }`);
  console.log(`  window.location.reload(); // 重新加载页面`);
  console.log(`};`);
  console.log(`\n// 执行修复:`);
  console.log(`window.fixPresetStyles();`);

  console.log(`\n✅ 修复脚本已生成，请在浏览器开发者工具中执行上述代码`);

} catch (error) {
  console.error('❌ 修复失败:', error);
}