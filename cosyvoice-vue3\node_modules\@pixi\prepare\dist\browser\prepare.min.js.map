{"version": 3, "file": "prepare.min.js", "sources": ["../../src/settings.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/CountLimiter.ts", "../../src/BasePrepare.ts", "../../src/Prepare.ts", "../../src/TimeLimiter.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\n\n/**\n * Default number of uploads per frame using prepare plugin.\n * @static\n * @memberof PIXI.settings\n * @name UPLOADS_PER_FRAME\n * @type {number}\n * @default 4\n */\nsettings.UPLOADS_PER_FRAME = 4;\n\nexport { settings };\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * CountLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of items per frame.\n * @memberof PIXI\n */\nexport class CountLimiter\n{\n    /** The maximum number of items that can be prepared each frame. */\n    public maxItemsPerFrame: number;\n\n    /** The number of items that can be prepared in the current frame. */\n    public itemsLeft: number;\n\n    /**\n     * @param maxItemsPerFrame - The maximum number of items that can be prepared each frame.\n     */\n    constructor(maxItemsPerFrame: number)\n    {\n        this.maxItemsPerFrame = maxItemsPerFrame;\n        this.itemsLeft = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.itemsLeft = this.maxItemsPerFrame;\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return this.itemsLeft-- > 0;\n    }\n}\n", "import { Texture, BaseTexture } from '@pixi/core';\nimport { Ticker, UPDATE_PRIORITY } from '@pixi/ticker';\nimport { settings } from '@pixi/settings';\nimport type { DisplayObject } from '@pixi/display';\nimport { Container } from '@pixi/display';\nimport { Text, TextStyle, TextMetrics } from '@pixi/text';\nimport { CountLimiter } from './CountLimiter';\n\nimport type { AbstractRenderer } from '@pixi/core';\nimport { deprecation } from '@pixi/utils';\n\ninterface IArrowFunction\n{\n    (): void;\n}\ninterface IUploadHook\n{\n    (helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean;\n}\n\ninterface IFindHook\n{\n    (item: any, queue: Array<any>): boolean;\n}\n\nexport interface IDisplayObjectExtended extends DisplayObject\n{\n    _textures?: Array<Texture>;\n    _texture?: Texture;\n    style?: TextStyle | Partial<TextStyle>;\n}\n\n/**\n * Built-in hook to find multiple textures from objects like AnimatedSprites.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findMultipleBaseTextures(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    let result = false;\n\n    // Objects with multiple textures\n    if (item && item._textures && item._textures.length)\n    {\n        for (let i = 0; i < item._textures.length; i++)\n        {\n            if (item._textures[i] instanceof Texture)\n            {\n                const baseTexture = item._textures[i].baseTexture;\n\n                if (queue.indexOf(baseTexture) === -1)\n                {\n                    queue.push(baseTexture);\n                    result = true;\n                }\n            }\n        }\n    }\n\n    return result;\n}\n\n/**\n * Built-in hook to find BaseTextures from Texture.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findBaseTexture(item: Texture, queue: Array<any>): boolean\n{\n    if (item.baseTexture instanceof BaseTexture)\n    {\n        const texture = item.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find textures from objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findTexture(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item._texture && item._texture instanceof Texture)\n    {\n        const texture = item._texture.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to draw PIXI.Text to its texture.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction drawText(_helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof Text)\n    {\n        // updating text will return early if it is not dirty\n        item.updateText(true);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to calculate a text style for a PIXI.Text object.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction calculateTextStyle(_helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        const font = item.toFontString();\n\n        TextMetrics.measureFont(font);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find Text objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Text object was found.\n */\nfunction findText(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Text)\n    {\n        // push the text style to prepare it - this can be really expensive\n        if (queue.indexOf(item.style) === -1)\n        {\n            queue.push(item.style);\n        }\n        // also push the text object so that we can render it (to canvas/texture) if needed\n        if (queue.indexOf(item) === -1)\n        {\n            queue.push(item);\n        }\n        // also push the Text's texture for upload to GPU\n        const texture = item._texture.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find TextStyle objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.TextStyle object was found.\n */\nfunction findTextStyle(item: TextStyle, queue: Array<any>): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        if (queue.indexOf(item) === -1)\n        {\n            queue.push(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare manager provides functionality to upload content to the GPU.\n *\n * BasePrepare handles basic queuing functionality and is extended by\n * {@link PIXI.Prepare} and {@link PIXI.CanvasPrepare}\n * to provide preparation capabilities specific to their respective renderers.\n * @example\n * // Create a sprite\n * const sprite = PIXI.Sprite.from('something.png');\n *\n * // Load object into GPU\n * app.renderer.plugins.prepare.upload(sprite, () => {\n *\n *     //Texture(s) has been uploaded to GPU\n *     app.stage.addChild(sprite);\n *\n * })\n * @abstract\n * @memberof PIXI\n */\nexport class BasePrepare\n{\n    /**\n     * The limiter to be used to control how quickly items are prepared.\n     * @type {PIXI.CountLimiter|PIXI.TimeLimiter}\n     */\n    private limiter: CountLimiter;\n\n    /** Reference to the renderer. */\n    protected renderer: AbstractRenderer;\n\n    /**\n     * The only real difference between CanvasPrepare and Prepare is what they pass\n     * to upload hooks. That different parameter is stored here.\n     */\n    protected uploadHookHelper: any;\n\n    /** Collection of items to uploads at once. */\n    protected queue: Array<any>;\n\n    /**\n     * Collection of additional hooks for finding assets.\n     * @type {Array<Function>}\n     */\n    public addHooks: Array<any>;\n\n    /**\n     * Collection of additional hooks for processing assets.\n     * @type {Array<Function>}\n     */\n    public uploadHooks: Array<any>;\n\n    /**\n     * Callback to call after completed.\n     * @type {Array<Function>}\n     */\n    public completes: Array<any>;\n\n    /**\n     * If prepare is ticking (running).\n     * @type {boolean}\n     */\n    public ticking: boolean;\n\n    /**\n     * 'bound' call for prepareItems().\n     * @type {Function}\n     */\n    private delayedTick: IArrowFunction;\n\n    /**\n     * @param {PIXI.AbstractRenderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: AbstractRenderer)\n    {\n        this.limiter = new CountLimiter(settings.UPLOADS_PER_FRAME);\n        this.renderer = renderer;\n        this.uploadHookHelper = null;\n        this.queue = [];\n        this.addHooks = [];\n        this.uploadHooks = [];\n        this.completes = [];\n        this.ticking = false;\n        this.delayedTick = (): void =>\n        {\n            // unlikely, but in case we were destroyed between tick() and delayedTick()\n            if (!this.queue)\n            {\n                return;\n            }\n            this.prepareItems();\n        };\n\n        // hooks to find the correct texture\n        this.registerFindHook(findText);\n        this.registerFindHook(findTextStyle);\n        this.registerFindHook(findMultipleBaseTextures);\n        this.registerFindHook(findBaseTexture);\n        this.registerFindHook(findTexture);\n\n        // upload hooks\n        this.registerUploadHook(drawText);\n        this.registerUploadHook(calculateTextStyle);\n    }\n\n    /**\n     * Upload all the textures and graphics to the GPU.\n     * @method PIXI.BasePrepare#upload\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} [item] -\n     *        Container or display object to search for items to upload or the items to upload themselves,\n     *        or optionally ommitted, if items have been added using {@link PIXI.BasePrepare#add `prepare.add`}.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture): Promise<void>;\n\n    /**\n     * Use the Promise-based API instead.\n     * @method PIXI.BasePrepare#upload\n     * @deprecated since version 6.5.0\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} item -\n     *        Item to upload.\n     * @param {Function} [done] - Callback when completed.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture, done?: () => void): void;\n\n    /**\n     * Use the Promise-based API instead.\n     * @method PIXI.BasePrepare#upload\n     * @deprecated since version 6.5.0\n     * @param {Function} [done] - Callback when completed.\n     */\n    upload(done?: () => void): void;\n\n    /** @ignore */\n    upload(\n        item?: IDisplayObjectExtended | Container | BaseTexture | Texture | (() => void),\n        done?: () => void): Promise<void>\n    {\n        if (typeof item === 'function')\n        {\n            done = item as () => void;\n            item = null;\n        }\n\n        // #if _DEBUG\n        if (done)\n        {\n            deprecation('6.5.0', 'BasePrepare.upload callback is deprecated, use the return Promise instead.');\n        }\n        // #endif\n\n        return new Promise((resolve) =>\n        {\n            // If a display object, search for items\n            // that we could upload\n            if (item)\n            {\n                this.add(item as IDisplayObjectExtended | Container | BaseTexture | Texture);\n            }\n\n            // TODO: remove done callback and just use resolve\n            const complete = () =>\n            {\n                done?.();\n                resolve();\n            };\n\n            // Get the items for upload from the display\n            if (this.queue.length)\n            {\n                this.completes.push(complete);\n\n                if (!this.ticking)\n                {\n                    this.ticking = true;\n                    Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n                }\n            }\n            else\n            {\n                complete();\n            }\n        });\n    }\n\n    /**\n     * Handle tick update\n     * @private\n     */\n    tick(): void\n    {\n        setTimeout(this.delayedTick, 0);\n    }\n\n    /**\n     * Actually prepare items. This is handled outside of the tick because it will take a while\n     * and we do NOT want to block the current animation frame from rendering.\n     * @private\n     */\n    prepareItems(): void\n    {\n        this.limiter.beginFrame();\n        // Upload the graphics\n        while (this.queue.length && this.limiter.allowedToUpload())\n        {\n            const item = this.queue[0];\n            let uploaded = false;\n\n            if (item && !item._destroyed)\n            {\n                for (let i = 0, len = this.uploadHooks.length; i < len; i++)\n                {\n                    if (this.uploadHooks[i](this.uploadHookHelper, item))\n                    {\n                        this.queue.shift();\n                        uploaded = true;\n                        break;\n                    }\n                }\n            }\n\n            if (!uploaded)\n            {\n                this.queue.shift();\n            }\n        }\n\n        // We're finished\n        if (!this.queue.length)\n        {\n            this.ticking = false;\n\n            const completes = this.completes.slice(0);\n\n            this.completes.length = 0;\n\n            for (let i = 0, len = completes.length; i < len; i++)\n            {\n                completes[i]();\n            }\n        }\n        else\n        {\n            // if we are not finished, on the next rAF do this again\n            Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n        }\n    }\n\n    /**\n     * Adds hooks for finding items.\n     * @param {Function} addHook - Function call that takes two parameters: `item:*, queue:Array`\n     *          function must return `true` if it was able to add item to the queue.\n     * @returns Instance of plugin for chaining.\n     */\n    registerFindHook(addHook: IFindHook): this\n    {\n        if (addHook)\n        {\n            this.addHooks.push(addHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Adds hooks for uploading items.\n     * @param {Function} uploadHook - Function call that takes two parameters: `prepare:CanvasPrepare, item:*` and\n     *          function must return `true` if it was able to handle upload of item.\n     * @returns Instance of plugin for chaining.\n     */\n    registerUploadHook(uploadHook: IUploadHook): this\n    {\n        if (uploadHook)\n        {\n            this.uploadHooks.push(uploadHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Manually add an item to the uploading queue.\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text|*} item - Object to\n     *        add to the queue\n     * @returns Instance of plugin for chaining.\n     */\n    add(item: IDisplayObjectExtended | Container | BaseTexture | Texture): this\n    {\n        // Add additional hooks for finding elements on special\n        // types of objects that\n        for (let i = 0, len = this.addHooks.length; i < len; i++)\n        {\n            if (this.addHooks[i](item, this.queue))\n            {\n                break;\n            }\n        }\n\n        // Get children recursively\n        if (item instanceof Container)\n        {\n            for (let i = item.children.length - 1; i >= 0; i--)\n            {\n                this.add(item.children[i]);\n            }\n        }\n\n        return this;\n    }\n\n    /** Destroys the plugin, don't use after this. */\n    destroy(): void\n    {\n        if (this.ticking)\n        {\n            Ticker.system.remove(this.tick, this);\n        }\n        this.ticking = false;\n        this.addHooks = null;\n        this.uploadHooks = null;\n        this.renderer = null;\n        this.completes = null;\n        this.queue = null;\n        this.limiter = null;\n        this.uploadHookHelper = null;\n    }\n}\n", "import { BaseTexture, ExtensionType } from '@pixi/core';\nimport { Graphics } from '@pixi/graphics';\nimport type { IDisplayObjectExtended } from './BasePrepare';\nimport { BasePrepare } from './BasePrepare';\n\nimport type { Abstract<PERSON><PERSON><PERSON>, Renderer, ExtensionMetadata } from '@pixi/core';\n\n/**\n * Built-in hook to upload PIXI.Texture objects to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadBaseTextures(renderer: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended | BaseTexture): boolean\n{\n    if (item instanceof BaseTexture)\n    {\n        // if the texture already has a GL texture, then the texture has been prepared or rendered\n        // before now. If the texture changed, then the changer should be calling texture.update() which\n        // reuploads the texture without need for preparing it again\n        if (!item._glTextures[(renderer as Renderer).CONTEXT_UID])\n        {\n            (renderer as Renderer).texture.bind(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to upload PIXI.Graphics to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadGraphics(renderer: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (!(item instanceof Graphics))\n    {\n        return false;\n    }\n\n    const { geometry } = item;\n\n    // update dirty graphics to get batches\n    item.finishPoly();\n    geometry.updateBatches();\n\n    const { batches } = geometry;\n\n    // upload all textures found in styles\n    for (let i = 0; i < batches.length; i++)\n    {\n        const { texture } = batches[i].style;\n\n        if (texture)\n        {\n            uploadBaseTextures(renderer, texture.baseTexture);\n        }\n    }\n\n    // if its not batchable - update vao for particular shader\n    if (!geometry.batchable)\n    {\n        (renderer as Renderer).geometry.bind(geometry, (item as any)._resolveDirectShader((renderer as Renderer)));\n    }\n\n    return true;\n}\n\n/**\n * Built-in hook to find graphics.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Graphics object was found.\n */\nfunction findGraphics(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Graphics)\n    {\n        queue.push(item);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare plugin provides renderer-specific plugins for pre-rendering DisplayObjects. These plugins are useful for\n * asynchronously preparing and uploading to the GPU assets, textures, graphics waiting to be displayed.\n *\n * Do not instantiate this plugin directly. It is available from the `renderer.plugins` property.\n * See {@link PIXI.CanvasRenderer#plugins} or {@link PIXI.Renderer#plugins}.\n * @example\n * // Create a new application\n * const app = new PIXI.Application();\n * document.body.appendChild(app.view);\n *\n * // Don't start rendering right away\n * app.stop();\n *\n * // create a display object\n * const rect = new PIXI.Graphics()\n *     .beginFill(0x00ff00)\n *     .drawRect(40, 40, 200, 200);\n *\n * // Add to the stage\n * app.stage.addChild(rect);\n *\n * // Don't start rendering until the graphic is uploaded to the GPU\n * app.renderer.plugins.prepare.upload(app.stage, () => {\n *     app.start();\n * });\n * @memberof PIXI\n */\nexport class Prepare extends BasePrepare\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'prepare',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /**\n     * @param {PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        this.uploadHookHelper = this.renderer;\n\n        // Add textures and graphics to upload\n        this.registerFindHook(findGraphics);\n        this.registerUploadHook(uploadBaseTextures);\n        this.registerUploadHook(uploadGraphics);\n    }\n}\n", "/**\n * TimeLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of milliseconds per frame.\n * @memberof PIXI\n */\nexport class TimeLimiter\n{\n    /** The maximum milliseconds that can be spent preparing items each frame. */\n    public maxMilliseconds: number;\n\n    /**\n     * The start time of the current frame.\n     * @readonly\n     */\n    public frameStart: number;\n\n    /** @param maxMilliseconds - The maximum milliseconds that can be spent preparing items each frame. */\n    constructor(maxMilliseconds: number)\n    {\n        this.maxMilliseconds = maxMilliseconds;\n        this.frameStart = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.frameStart = Date.now();\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns - If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return Date.now() - this.frameStart < this.maxMilliseconds;\n    }\n}\n"], "names": ["settings", "UPLOADS_PER_FRAME", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "CountLimiter", "maxItemsPerFrame", "this", "itemsLeft", "prototype", "beginFrame", "allowedToUpload", "findMultipleBaseTextures", "item", "queue", "result", "_textures", "length", "i", "Texture", "baseTexture", "indexOf", "push", "findBaseTexture", "BaseTexture", "texture", "findTexture", "_texture", "drawText", "_helper", "Text", "updateText", "calculateTextStyle", "TextStyle", "font", "toFontString", "TextMetrics", "measureFont", "findText", "style", "findTextStyle", "BasePrepare", "renderer", "_this", "limiter", "uploadHookHelper", "add<PERSON>ooks", "uploadHooks", "completes", "ticking", "delayedTick", "prepareItems", "registerFindHook", "registerUploadHook", "upload", "done", "Promise", "resolve", "add", "complete", "Ticker", "system", "addOnce", "tick", "UPDATE_PRIORITY", "UTILITY", "setTimeout", "uploaded", "_destroyed", "len", "shift", "slice", "addHook", "uploadHook", "Container", "children", "destroy", "remove", "uploadBaseTextures", "_glTextures", "CONTEXT_UID", "bind", "uploadGraphics", "Graphics", "geometry", "finishPoly", "updateBatches", "batches", "batchable", "_resolveDirectShader", "findGraphics", "Prepare", "_super", "call", "__", "constructor", "create", "__extends", "extension", "name", "type", "ExtensionType", "RendererPlugin", "TimeLimiter", "maxMilliseconds", "frameStart", "Date", "now"], "mappings": ";;;;;;;+EAUQA,EAAAA,SAACC,kBAAoB,ECM7B,IAAIC,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,ICf5B,IAAAO,EAAA,WAWI,SAAAA,EAAYC,GAERC,KAAKD,iBAAmBA,EACxBC,KAAKC,UAAY,EAiBzB,OAbIH,EAAAI,UAAAC,WAAA,WAEIH,KAAKC,UAAYD,KAAKD,kBAO1BD,EAAAI,UAAAE,gBAAA,WAEI,OAAOJ,KAAKC,aAAc,GAEjCH,KCGD,SAASO,EAAyBC,EAA8BC,GAE5D,IAAIC,GAAS,EAGb,GAAIF,GAAQA,EAAKG,WAAaH,EAAKG,UAAUC,OAEzC,IAAK,IAAIC,EAAI,EAAGA,EAAIL,EAAKG,UAAUC,OAAQC,IAEvC,GAAIL,EAAKG,UAAUE,aAAcC,EAAAA,QACjC,CACI,IAAMC,EAAcP,EAAKG,UAAUE,GAAGE,aAEF,IAAhCN,EAAMO,QAAQD,KAEdN,EAAMQ,KAAKF,GACXL,GAAS,GAMzB,OAAOA,EAUX,SAASQ,EAAgBV,EAAeC,GAEpC,GAAID,EAAKO,uBAAuBI,cAChC,CACI,IAAMC,EAAUZ,EAAKO,YAOrB,OALgC,IAA5BN,EAAMO,QAAQI,IAEdX,EAAMQ,KAAKG,IAGR,EAGX,OAAO,EAUX,SAASC,EAAYb,EAA8BC,GAE/C,GAAID,EAAKc,UAAYd,EAAKc,oBAAoBR,EAAAA,QAC9C,CACI,IAAMM,EAAUZ,EAAKc,SAASP,YAO9B,OALgC,IAA5BN,EAAMO,QAAQI,IAEdX,EAAMQ,KAAKG,IAGR,EAGX,OAAO,EAUX,SAASG,EAASC,EAAyChB,GAEvD,OAAIA,aAAgBiB,EAAAA,OAGhBjB,EAAKkB,YAAW,IAET,GAaf,SAASC,EAAmBH,EAAyChB,GAEjE,GAAIA,aAAgBoB,EAAAA,UACpB,CACI,IAAMC,EAAOrB,EAAKsB,eAIlB,OAFAC,cAAYC,YAAYH,IAEjB,EAGX,OAAO,EAUX,SAASI,EAASzB,EAA8BC,GAE5C,GAAID,aAAgBiB,EAAAA,KACpB,EAEuC,IAA/BhB,EAAMO,QAAQR,EAAK0B,QAEnBzB,EAAMQ,KAAKT,EAAK0B,QAGS,IAAzBzB,EAAMO,QAAQR,IAEdC,EAAMQ,KAAKT,GAGf,IAAMY,EAAUZ,EAAKc,SAASP,YAO9B,OALgC,IAA5BN,EAAMO,QAAQI,IAEdX,EAAMQ,KAAKG,IAGR,EAGX,OAAO,EAUX,SAASe,EAAc3B,EAAiBC,GAEpC,OAAID,aAAgBoB,EAAAA,aAEa,IAAzBnB,EAAMO,QAAQR,IAEdC,EAAMQ,KAAKT,IAGR,GA0Bf,IAAA4B,EAAA,WAqDI,SAAAA,EAAYC,GAAZ,IA8BCC,EAAApC,KA5BGA,KAAKqC,QAAU,IAAIvC,EAAaX,EAAQA,SAACC,mBACzCY,KAAKmC,SAAWA,EAChBnC,KAAKsC,iBAAmB,KACxBtC,KAAKO,MAAQ,GACbP,KAAKuC,SAAW,GAChBvC,KAAKwC,YAAc,GACnBxC,KAAKyC,UAAY,GACjBzC,KAAK0C,SAAU,EACf1C,KAAK2C,YAAc,WAGVP,EAAK7B,OAIV6B,EAAKQ,gBAIT5C,KAAK6C,iBAAiBd,GACtB/B,KAAK6C,iBAAiBZ,GACtBjC,KAAK6C,iBAAiBxC,GACtBL,KAAK6C,iBAAiB7B,GACtBhB,KAAK6C,iBAAiB1B,GAGtBnB,KAAK8C,mBAAmBzB,GACxBrB,KAAK8C,mBAAmBrB,GA+NhC,OAhMIS,EAAAhC,UAAA6C,OAAA,SACIzC,EACA0C,GAFJ,IAiDCZ,EAAApC,KAhCG,MAboB,mBAATM,IAEP0C,EAAO1C,EACPA,EAAO,MAUJ,IAAI2C,SAAQ,SAACC,GAIZ5C,GAEA8B,EAAKe,IAAI7C,GAIb,IAAM8C,EAAW,WAEbJ,MAAAA,GAAAA,IACAE,KAIAd,EAAK7B,MAAMG,QAEX0B,EAAKK,UAAU1B,KAAKqC,GAEfhB,EAAKM,UAENN,EAAKM,SAAU,EACfW,SAAOC,OAAOC,QAAQnB,EAAKoB,KAAMpB,EAAMqB,EAAAA,gBAAgBC,WAK3DN,QASZlB,EAAAhC,UAAAsD,KAAA,WAEIG,WAAW3D,KAAK2C,YAAa,IAQjCT,EAAAhC,UAAA0C,aAAA,WAII,IAFA5C,KAAKqC,QAAQlC,aAENH,KAAKO,MAAMG,QAAUV,KAAKqC,QAAQjC,mBACzC,CACI,IAAME,EAAON,KAAKO,MAAM,GACpBqD,GAAW,EAEf,GAAItD,IAASA,EAAKuD,WAEd,IAAK,IAAIlD,EAAI,EAAGmD,EAAM9D,KAAKwC,YAAY9B,OAAQC,EAAImD,EAAKnD,IAEpD,GAAIX,KAAKwC,YAAY7B,GAAGX,KAAKsC,iBAAkBhC,GAC/C,CACIN,KAAKO,MAAMwD,QACXH,GAAW,EACX,MAKPA,GAED5D,KAAKO,MAAMwD,QAKnB,GAAK/D,KAAKO,MAAMG,OAgBZ2C,SAAOC,OAAOC,QAAQvD,KAAKwD,KAAMxD,KAAMyD,EAAAA,gBAAgBC,aAf3D,CACI1D,KAAK0C,SAAU,EAEf,IAAMD,EAAYzC,KAAKyC,UAAUuB,MAAM,GAEvChE,KAAKyC,UAAU/B,OAAS,EAExB,IAASC,EAAI,EAAGmD,EAAMrB,EAAU/B,OAAQC,EAAImD,EAAKnD,IAE7C8B,EAAU9B,OAgBtBuB,EAAgBhC,UAAA2C,iBAAhB,SAAiBoB,GAOb,OALIA,GAEAjE,KAAKuC,SAASxB,KAAKkD,GAGhBjE,MASXkC,EAAkBhC,UAAA4C,mBAAlB,SAAmBoB,GAOf,OALIA,GAEAlE,KAAKwC,YAAYzB,KAAKmD,GAGnBlE,MASXkC,EAAGhC,UAAAiD,IAAH,SAAI7C,GAIA,IAAK,IAAIK,EAAI,EAAGmD,EAAM9D,KAAKuC,SAAS7B,OAAQC,EAAImD,IAExC9D,KAAKuC,SAAS5B,GAAGL,EAAMN,KAAKO,OAFiBI,KASrD,GAAIL,aAAgB6D,EAAAA,UAEhB,IAASxD,EAAIL,EAAK8D,SAAS1D,OAAS,EAAGC,GAAK,EAAGA,IAE3CX,KAAKmD,IAAI7C,EAAK8D,SAASzD,IAI/B,OAAOX,MAIXkC,EAAAhC,UAAAmE,QAAA,WAEQrE,KAAK0C,SAELW,EAAMA,OAACC,OAAOgB,OAAOtE,KAAKwD,KAAMxD,MAEpCA,KAAK0C,SAAU,EACf1C,KAAKuC,SAAW,KAChBvC,KAAKwC,YAAc,KACnBxC,KAAKmC,SAAW,KAChBnC,KAAKyC,UAAY,KACjBzC,KAAKO,MAAQ,KACbP,KAAKqC,QAAU,KACfrC,KAAKsC,iBAAmB,MAE/BJ,KCzgBD,SAASqC,EAAmBpC,EAA0C7B,GAElE,OAAIA,aAAgBW,EAAAA,cAKXX,EAAKkE,YAAarC,EAAsBsC,cAExCtC,EAAsBjB,QAAQwD,KAAKpE,IAGjC,GAaf,SAASqE,EAAexC,EAA0C7B,GAE9D,KAAMA,aAAgBsE,EAAAA,UAElB,OAAO,EAGH,IAAAC,EAAavE,EAAIuE,SAGzBvE,EAAKwE,aACLD,EAASE,gBAKT,IAHQ,IAAAC,EAAYH,EAAQG,QAGnBrE,EAAI,EAAGA,EAAIqE,EAAQtE,OAAQC,IACpC,CACY,IAAAO,EAAY8D,EAAQrE,GAAGqB,MAAKd,QAEhCA,GAEAqD,EAAmBpC,EAAUjB,EAAQL,aAU7C,OALKgE,EAASI,WAET9C,EAAsB0C,SAASH,KAAKG,EAAWvE,EAAa4E,qBAAsB/C,KAGhF,EAUX,SAASgD,EAAa7E,EAA8BC,GAEhD,OAAID,aAAgBsE,EAAAA,WAEhBrE,EAAMQ,KAAKT,IAEJ,GAkCf,IAAA8E,EAAA,SAAAC,GAWI,SAAAD,EAAYjD,GAAZ,IAEIC,EAAAiD,EAAAC,KAAAtF,KAAMmC,IAQTnC,YANGoC,EAAKE,iBAAmBF,EAAKD,SAG7BC,EAAKS,iBAAiBsC,GACtB/C,EAAKU,mBAAmByB,GACxBnC,EAAKU,mBAAmB6B,KAEhC,OHxHO,SAAmBrF,EAAGC,GAEzB,SAASgG,IAAOvF,KAAKwF,YAAclG,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEY,UAAkB,OAANX,EAAaC,OAAOiG,OAAOlG,IAAMgG,EAAGrF,UAAYX,EAAEW,UAAW,IAAIqF,GG+FtDG,CAAWN,EAAAC,GAG7BD,EAAAO,UAA+B,CAClCC,KAAM,UACNC,KAAMC,EAAaA,cAACC,gBAiB3BX,EAtBD,CAA6BlD,GCpH7B8D,EAAA,WAYI,SAAAA,EAAYC,GAERjG,KAAKiG,gBAAkBA,EACvBjG,KAAKkG,WAAa,EAiB1B,OAbIF,EAAA9F,UAAAC,WAAA,WAEIH,KAAKkG,WAAaC,KAAKC,OAO3BJ,EAAA9F,UAAAE,gBAAA,WAEI,OAAO+F,KAAKC,MAAQpG,KAAKkG,WAAalG,KAAKiG,iBAElDD"}