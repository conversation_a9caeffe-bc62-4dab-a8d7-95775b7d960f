{"version": 3, "file": "spritesheet.js", "sources": ["../../src/Spritesheet.ts", "../../src/SpritesheetLoader.ts"], "sourcesContent": ["import { Rectangle } from '@pixi/math';\nimport { Texture, BaseTexture } from '@pixi/core';\nimport { deprecation, getResolutionOfUrl } from '@pixi/utils';\nimport type { Dict } from '@pixi/utils';\nimport type { ImageResource } from '@pixi/core';\nimport type { IPointData } from '@pixi/math';\n\n/** Represents the JSON data for a spritesheet atlas. */\nexport interface ISpritesheetFrameData\n{\n    frame: {\n        x: number;\n        y: number;\n        w: number;\n        h: number;\n    };\n    trimmed?: boolean;\n    rotated?: boolean;\n    sourceSize?: {\n        w: number;\n        h: number;\n    };\n    spriteSourceSize?: {\n        x: number;\n        y: number;\n    };\n    anchor?: IPointData;\n}\n\n/** Atlas format. */\nexport interface ISpritesheetData\n{\n    frames: Dict<ISpritesheetFrameData>;\n    animations?: Dict<string[]>;\n    meta: {\n        scale: string;\n        // eslint-disable-next-line camelcase\n        related_multi_packs?: string[];\n    };\n}\n\n/**\n * Utility class for maintaining reference to a collection\n * of Textures on a single Spritesheet.\n *\n * To access a sprite sheet from your code you may pass its JSON data file to <PERSON><PERSON>'s loader:\n *\n * ```js\n * PIXI.Loader.shared.add(\"images/spritesheet.json\").load(setup);\n *\n * function setup() {\n *   let sheet = PIXI.Loader.shared.resources[\"images/spritesheet.json\"].spritesheet;\n *   ...\n * }\n * ```\n *\n * Alternately, you may circumvent the loader by instantiating the Spritesheet directly:\n * ```js\n * const sheet = new PIXI.Spritesheet(texture, spritesheetData);\n * await sheet.parse();\n * console.log('Spritesheet ready to use!');\n * ```\n *\n * With the `sheet.textures` you can create Sprite objects,`sheet.animations` can be used to create an AnimatedSprite.\n *\n * Sprite sheets can be packed using tools like {@link https://codeandweb.com/texturepacker|TexturePacker},\n * {@link https://renderhjs.net/shoebox/|Shoebox} or {@link https://github.com/krzysztof-o/spritesheet.js|Spritesheet.js}.\n * Default anchor points (see {@link PIXI.Texture#defaultAnchor}) and grouping of animation sprites are currently only\n * supported by TexturePacker.\n * @memberof PIXI\n */\nexport class Spritesheet\n{\n    /** The maximum number of Textures to build per process. */\n    static readonly BATCH_SIZE = 1000;\n\n    /** For multi-packed spritesheets, this contains a reference to all the other spritesheets it depends on. */\n    public linkedSheets: Spritesheet[] = [];\n\n    /** Reference to ths source texture. */\n    public baseTexture: BaseTexture;\n\n    /**\n     * A map containing all textures of the sprite sheet.\n     * Can be used to create a {@link PIXI.Sprite|Sprite}:\n     * ```js\n     * new PIXI.Sprite(sheet.textures[\"image.png\"]);\n     * ```\n     */\n    public textures: Dict<Texture>;\n\n    /**\n     * A map containing the textures for each animation.\n     * Can be used to create an {@link PIXI.AnimatedSprite|AnimatedSprite}:\n     * ```js\n     * new PIXI.AnimatedSprite(sheet.animations[\"anim_name\"])\n     * ```\n     */\n    public animations: Dict<Texture[]>;\n\n    /**\n     * Reference to the original JSON data.\n     * @type {object}\n     */\n    public data: ISpritesheetData;\n\n    /** The resolution of the spritesheet. */\n    public resolution: number;\n\n    /**\n     * Reference to original source image from the Loader. This reference is retained so we\n     * can destroy the Texture later on. It is never used internally.\n     */\n    private _texture: Texture;\n\n    /**\n     * Map of spritesheet frames.\n     * @type {object}\n     */\n    private _frames: Dict<ISpritesheetFrameData>;\n\n    /** Collection of frame names. */\n    private _frameKeys: string[];\n\n    /** Current batch index being processed. */\n    private _batchIndex: number;\n\n    /**\n     * Callback when parse is completed.\n     * @type {Function}\n     */\n    private _callback: (textures: Dict<Texture>) => void;\n\n    /**\n     * @param texture - Reference to the source BaseTexture object.\n     * @param {object} data - Spritesheet image data.\n     * @param resolutionFilename - The filename to consider when determining\n     *        the resolution of the spritesheet. If not provided, the imageUrl will\n     *        be used on the BaseTexture.\n     */\n    constructor(texture: BaseTexture | Texture, data: ISpritesheetData, resolutionFilename: string = null)\n    {\n        this._texture = texture instanceof Texture ? texture : null;\n        this.baseTexture = texture instanceof BaseTexture ? texture : this._texture.baseTexture;\n        this.textures = {};\n        this.animations = {};\n        this.data = data;\n\n        const resource = this.baseTexture.resource as ImageResource;\n\n        this.resolution = this._updateResolution(resolutionFilename || (resource ? resource.url : null));\n        this._frames = this.data.frames;\n        this._frameKeys = Object.keys(this._frames);\n        this._batchIndex = 0;\n        this._callback = null;\n    }\n\n    /**\n     * Generate the resolution from the filename or fallback\n     * to the meta.scale field of the JSON data.\n     * @param resolutionFilename - The filename to use for resolving\n     *        the default resolution.\n     * @returns Resolution to use for spritesheet.\n     */\n    private _updateResolution(resolutionFilename: string = null): number\n    {\n        const { scale } = this.data.meta;\n\n        // Use a defaultValue of `null` to check if a url-based resolution is set\n        let resolution = getResolutionOfUrl(resolutionFilename, null);\n\n        // No resolution found via URL\n        if (resolution === null)\n        {\n            // Use the scale value or default to 1\n            resolution = scale !== undefined ? parseFloat(scale) : 1;\n        }\n\n        // For non-1 resolutions, update baseTexture\n        if (resolution !== 1)\n        {\n            this.baseTexture.setResolution(resolution);\n        }\n\n        return resolution;\n    }\n\n    /**\n     * Parser spritesheet from loaded data. This is done asynchronously\n     * to prevent creating too many Texture within a single process.\n     * @method PIXI.Spritesheet#parse\n     */\n    public parse(): Promise<Dict<Texture>>;\n\n    /**\n     * Please use the Promise-based version of this function.\n     * @method PIXI.Spritesheet#parse\n     * @deprecated since version 6.5.0\n     * @param {Function} callback - Callback when complete returns\n     *        a map of the Textures for this spritesheet.\n     */\n    public parse(callback?: (textures?: Dict<Texture>) => void): void;\n\n    /** @ignore */\n    public parse(callback?: (textures?: Dict<Texture>) => void): Promise<Dict<Texture>>\n    {\n        // #if _DEBUG\n        if (callback)\n        {\n            deprecation('6.5.0', 'Spritesheet.parse callback is deprecated, use the return Promise instead.');\n        }\n        // #endif\n\n        return new Promise((resolve) =>\n        {\n            this._callback = (textures: Dict<Texture>) =>\n            {\n                callback?.(textures);\n                resolve(textures);\n            };\n            this._batchIndex = 0;\n\n            if (this._frameKeys.length <= Spritesheet.BATCH_SIZE)\n            {\n                this._processFrames(0);\n                this._processAnimations();\n                this._parseComplete();\n            }\n            else\n            {\n                this._nextBatch();\n            }\n        });\n    }\n\n    /**\n     * Process a batch of frames\n     * @param initialFrameIndex - The index of frame to start.\n     */\n    private _processFrames(initialFrameIndex: number): void\n    {\n        let frameIndex = initialFrameIndex;\n        const maxFrames = Spritesheet.BATCH_SIZE;\n\n        while (frameIndex - initialFrameIndex < maxFrames && frameIndex < this._frameKeys.length)\n        {\n            const i = this._frameKeys[frameIndex];\n            const data = this._frames[i];\n            const rect = data.frame;\n\n            if (rect)\n            {\n                let frame = null;\n                let trim = null;\n                const sourceSize = data.trimmed !== false && data.sourceSize\n                    ? data.sourceSize : data.frame;\n\n                const orig = new Rectangle(\n                    0,\n                    0,\n                    Math.floor(sourceSize.w) / this.resolution,\n                    Math.floor(sourceSize.h) / this.resolution\n                );\n\n                if (data.rotated)\n                {\n                    frame = new Rectangle(\n                        Math.floor(rect.x) / this.resolution,\n                        Math.floor(rect.y) / this.resolution,\n                        Math.floor(rect.h) / this.resolution,\n                        Math.floor(rect.w) / this.resolution\n                    );\n                }\n                else\n                {\n                    frame = new Rectangle(\n                        Math.floor(rect.x) / this.resolution,\n                        Math.floor(rect.y) / this.resolution,\n                        Math.floor(rect.w) / this.resolution,\n                        Math.floor(rect.h) / this.resolution\n                    );\n                }\n\n                //  Check to see if the sprite is trimmed\n                if (data.trimmed !== false && data.spriteSourceSize)\n                {\n                    trim = new Rectangle(\n                        Math.floor(data.spriteSourceSize.x) / this.resolution,\n                        Math.floor(data.spriteSourceSize.y) / this.resolution,\n                        Math.floor(rect.w) / this.resolution,\n                        Math.floor(rect.h) / this.resolution\n                    );\n                }\n\n                this.textures[i] = new Texture(\n                    this.baseTexture,\n                    frame,\n                    orig,\n                    trim,\n                    data.rotated ? 2 : 0,\n                    data.anchor\n                );\n\n                // lets also add the frame to pixi's global cache for 'from' and 'fromLoader' functions\n                Texture.addToCache(this.textures[i], i);\n            }\n\n            frameIndex++;\n        }\n    }\n\n    /** Parse animations config. */\n    private _processAnimations(): void\n    {\n        const animations = this.data.animations || {};\n\n        for (const animName in animations)\n        {\n            this.animations[animName] = [];\n            for (let i = 0; i < animations[animName].length; i++)\n            {\n                const frameName = animations[animName][i];\n\n                this.animations[animName].push(this.textures[frameName]);\n            }\n        }\n    }\n\n    /** The parse has completed. */\n    private _parseComplete(): void\n    {\n        const callback = this._callback;\n\n        this._callback = null;\n        this._batchIndex = 0;\n        callback.call(this, this.textures);\n    }\n\n    /** Begin the next batch of textures. */\n    private _nextBatch(): void\n    {\n        this._processFrames(this._batchIndex * Spritesheet.BATCH_SIZE);\n        this._batchIndex++;\n        setTimeout(() =>\n        {\n            if (this._batchIndex * Spritesheet.BATCH_SIZE < this._frameKeys.length)\n            {\n                this._nextBatch();\n            }\n            else\n            {\n                this._processAnimations();\n                this._parseComplete();\n            }\n        }, 0);\n    }\n\n    /**\n     * Destroy Spritesheet and don't use after this.\n     * @param {boolean} [destroyBase=false] - Whether to destroy the base texture as well\n     */\n    public destroy(destroyBase = false): void\n    {\n        for (const i in this.textures)\n        {\n            this.textures[i].destroy();\n        }\n        this._frames = null;\n        this._frameKeys = null;\n        this.data = null;\n        this.textures = null;\n        if (destroyBase)\n        {\n            this._texture?.destroy();\n            this.baseTexture.destroy();\n        }\n        this._texture = null;\n        this.baseTexture = null;\n        this.linkedSheets = [];\n    }\n}\n\n/**\n * Reference to Spritesheet object created.\n * @member {PIXI.Spritesheet} spritesheet\n * @memberof PIXI.LoaderResource\n * @instance\n */\n\n/**\n * Dictionary of textures from Spritesheet.\n * @member {Object<string, PIXI.Texture>} textures\n * @memberof PIXI.LoaderResource\n * @instance\n */\n", "import { url } from '@pixi/utils';\nimport { Spritesheet } from './Spritesheet';\nimport { LoaderResource } from '@pixi/loaders';\nimport type { Loader } from '@pixi/loaders';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * {@link PIXI.Loader} middleware for loading texture atlases that have been created with\n * TexturePacker or similar JSON-based spritesheet.\n *\n * This middleware automatically generates Texture resources.\n *\n * If you're using Webpack or other bundlers and plan on bundling the atlas' JSON,\n * use the {@link PIXI.Spritesheet} class to directly parse the JSON.\n *\n * The Loader's image Resource name is automatically appended with `\"_image\"`.\n * If a Resource with this name is already loaded, the Loader will skip parsing the\n * Spritesheet. The code below will generate an internal Loader Resource called `\"myatlas_image\"`.\n * @example\n * loader.add('myatlas', 'path/to/myatlas.json');\n * loader.load(() => {\n *   loader.resources.myatlas; // atlas JSON resource\n *   loader.resources.myatlas_image; // atlas Image resource\n * });\n * @memberof PIXI\n */\nexport class SpritesheetLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * Called after a resource is loaded.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource\n     * @param next\n     */\n    static use(resource: LoaderResource, next: (...args: unknown[]) => void): void\n    {\n        // because this is middleware, it execute in loader context. `this` = loader\n        const loader = (this as any) as Loader;\n        const imageResourceName = `${resource.name}_image`;\n\n        // skip if no data, its not json, it isn't spritesheet data, or the image resource already exists\n        if (!resource.data\n            || resource.type !== LoaderResource.TYPE.JSON\n            || !resource.data.frames\n            || loader.resources[imageResourceName]\n        )\n        {\n            next();\n\n            return;\n        }\n\n        // Check and add the multi atlas\n        // Heavily influenced and based on https://github.com/rocket-ua/pixi-tps-loader/blob/master/src/ResourceLoader.js\n        // eslint-disable-next-line camelcase\n        const multiPacks = resource.data?.meta?.related_multi_packs;\n\n        if (Array.isArray(multiPacks))\n        {\n            for (const item of multiPacks)\n            {\n                if (typeof item !== 'string')\n                {\n                    continue;\n                }\n\n                const itemName = item.replace('.json', '');\n                const itemUrl = url.resolve(resource.url.replace(loader.baseUrl, ''), item);\n\n                // Check if the file wasn't already added as multipacks are redundant\n                if (loader.resources[itemName]\n                    || Object.values(loader.resources).some((r) => url.format(url.parse(r.url)) === itemUrl))\n                {\n                    continue;\n                }\n\n                const options = {\n                    crossOrigin: resource.crossOrigin,\n                    loadType: LoaderResource.LOAD_TYPE.XHR,\n                    xhrType: LoaderResource.XHR_RESPONSE_TYPE.JSON,\n                    parentResource: resource,\n                    metadata: resource.metadata\n                };\n\n                loader.add(itemName, itemUrl, options);\n            }\n        }\n\n        const loadOptions = {\n            crossOrigin: resource.crossOrigin,\n            metadata: resource.metadata.imageMetadata,\n            parentResource: resource,\n        };\n\n        const resourcePath = SpritesheetLoader.getResourcePath(resource, loader.baseUrl);\n\n        // load the image for this sheet\n        loader.add(imageResourceName, resourcePath, loadOptions, function onImageLoad(res: LoaderResource)\n        {\n            if (res.error)\n            {\n                next(res.error);\n\n                return;\n            }\n\n            const spritesheet = new Spritesheet(\n                res.texture,\n                resource.data,\n                resource.url\n            );\n\n            spritesheet.parse().then(() =>\n            {\n                resource.spritesheet = spritesheet;\n                resource.textures = spritesheet.textures;\n                next();\n            });\n        });\n    }\n\n    /**\n     * Get the spritesheets root path\n     * @param resource - Resource to check path\n     * @param baseUrl - Base root url\n     */\n    static getResourcePath(resource: LoaderResource, baseUrl: string): string\n    {\n        // Prepend url path unless the resource image is a data url\n        if (resource.isDataUrl)\n        {\n            return resource.data.meta.image;\n        }\n\n        return url.resolve(resource.url.replace(baseUrl, ''), resource.data.meta.image);\n    }\n}\n"], "names": ["Texture", "BaseTexture", "getResolutionOfUrl", "deprecation", "Rectangle", "LoaderResource", "url", "ExtensionType"], "mappings": ";;;;;;;;;;;IAyCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BG;AACH,QAAA,WAAA,kBAAA,YAAA;IA8DI;;;;;;IAMG;IACH,IAAA,SAAA,WAAA,CAAY,OAA8B,EAAE,IAAsB,EAAE,kBAAiC,EAAA;IAAjC,QAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAAiC,GAAA,IAAA,CAAA,EAAA;;YA/D9F,IAAY,CAAA,YAAA,GAAkB,EAAE,CAAC;IAiEpC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,YAAYA,YAAO,GAAG,OAAO,GAAG,IAAI,CAAC;IAC5D,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,YAAYC,gBAAW,GAAG,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IACxF,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACnB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACrB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAEjB,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAyB,CAAC;YAE5D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;YACjG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAChC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACrB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB;IAED;;;;;;IAMG;QACK,WAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UAA0B,kBAAiC,EAAA;IAAjC,QAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAAiC,GAAA,IAAA,CAAA,EAAA;YAE/C,IAAA,KAAK,GAAK,IAAI,CAAC,IAAI,CAAC,IAAI,MAAnB,CAAoB;;YAGjC,IAAI,UAAU,GAAGC,wBAAkB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;;YAG9D,IAAI,UAAU,KAAK,IAAI,EACvB;;IAEI,YAAA,UAAU,GAAG,KAAK,KAAK,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC5D,SAAA;;YAGD,IAAI,UAAU,KAAK,CAAC,EACpB;IACI,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAC9C,SAAA;IAED,QAAA,OAAO,UAAU,CAAC;SACrB,CAAA;;QAmBM,WAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,QAA6C,EAAA;YAA1D,IA6BC,KAAA,GAAA,IAAA,CAAA;IA1BG,QAAA,IAAI,QAAQ,EACZ;IACI,YAAAC,iBAAW,CAAC,OAAO,EAAE,2EAA2E,CAAC,CAAC;IACrG,SAAA;IAGD,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAA;IAEvB,YAAA,KAAI,CAAC,SAAS,GAAG,UAAC,QAAuB,EAAA;IAErC,gBAAA,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAG,QAAQ,CAAC,CAAC;oBACrB,OAAO,CAAC,QAAQ,CAAC,CAAC;IACtB,aAAC,CAAC;IACF,YAAA,KAAI,CAAC,WAAW,GAAG,CAAC,CAAC;gBAErB,IAAI,KAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,CAAC,UAAU,EACpD;IACI,gBAAA,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBACvB,KAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,KAAI,CAAC,cAAc,EAAE,CAAC;IACzB,aAAA;IAED,iBAAA;oBACI,KAAI,CAAC,UAAU,EAAE,CAAC;IACrB,aAAA;IACL,SAAC,CAAC,CAAC;SACN,CAAA;IAED;;;IAGG;QACK,WAAc,CAAA,SAAA,CAAA,cAAA,GAAtB,UAAuB,iBAAyB,EAAA;YAE5C,IAAI,UAAU,GAAG,iBAAiB,CAAC;IACnC,QAAA,IAAM,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC;IAEzC,QAAA,OAAO,UAAU,GAAG,iBAAiB,GAAG,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EACxF;gBACI,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBACtC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;IAExB,YAAA,IAAI,IAAI,EACR;oBACI,IAAI,KAAK,GAAG,IAAI,CAAC;oBACjB,IAAI,IAAI,GAAG,IAAI,CAAC;oBAChB,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,UAAU;0BACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;IAEnC,gBAAA,IAAM,IAAI,GAAG,IAAIC,cAAS,CACtB,CAAC,EACD,CAAC,EACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EAC1C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAC7C,CAAC;oBAEF,IAAI,IAAI,CAAC,OAAO,EAChB;wBACI,KAAK,GAAG,IAAIA,cAAS,CACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CACvC,CAAC;IACL,iBAAA;IAED,qBAAA;wBACI,KAAK,GAAG,IAAIA,cAAS,CACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CACvC,CAAC;IACL,iBAAA;;oBAGD,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,gBAAgB,EACnD;IACI,oBAAA,IAAI,GAAG,IAAIA,cAAS,CAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CACvC,CAAC;IACL,iBAAA;IAED,gBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAIJ,YAAO,CAC1B,IAAI,CAAC,WAAW,EAChB,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,EACpB,IAAI,CAAC,MAAM,CACd,CAAC;;IAGF,gBAAAA,YAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3C,aAAA;IAED,YAAA,UAAU,EAAE,CAAC;IAChB,SAAA;SACJ,CAAA;;IAGO,IAAA,WAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,YAAA;YAEI,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;IAE9C,QAAA,KAAK,IAAM,QAAQ,IAAI,UAAU,EACjC;IACI,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;IAC/B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EACpD;oBACI,IAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1C,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;IAC5D,aAAA;IACJ,SAAA;SACJ,CAAA;;IAGO,IAAA,WAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,YAAA;IAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IAEhC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtC,CAAA;;IAGO,IAAA,WAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,YAAA;YAAA,IAgBC,KAAA,GAAA,IAAA,CAAA;YAdG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,EAAE,CAAC;IACnB,QAAA,UAAU,CAAC,YAAA;IAEP,YAAA,IAAI,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC,UAAU,GAAG,KAAI,CAAC,UAAU,CAAC,MAAM,EACtE;oBACI,KAAI,CAAC,UAAU,EAAE,CAAC;IACrB,aAAA;IAED,iBAAA;oBACI,KAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,KAAI,CAAC,cAAc,EAAE,CAAC;IACzB,aAAA;aACJ,EAAE,CAAC,CAAC,CAAC;SACT,CAAA;IAED;;;IAGG;QACI,WAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,WAAmB,EAAA;;IAAnB,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAmB,GAAA,KAAA,CAAA,EAAA;IAE9B,QAAA,KAAK,IAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAC7B;gBACI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAC9B,SAAA;IACD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,IAAI,WAAW,EACf;IACI,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EAAE,CAAC;IACzB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;IAC9B,SAAA;IACD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;SAC1B,CAAA;;QAjTe,WAAU,CAAA,UAAA,GAAG,IAAI,CAAC;QAkTtC,OAAC,WAAA,CAAA;IAAA,CArTD,EAqTC,EAAA;IAED;;;;;IAKG;IAEH;;;;;IAKG;;ICnYH;;;;;;;;;;;;;;;;;;;IAmBG;AACH,QAAA,iBAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,iBAAA,GAAA;SAiHC;IA5GG;;;;;IAKG;IACI,IAAA,iBAAA,CAAA,GAAG,GAAV,UAAW,QAAwB,EAAE,IAAkC,EAAA;;;YAGnE,IAAM,MAAM,GAAI,IAAsB,CAAC;IACvC,QAAA,IAAM,iBAAiB,GAAM,QAAQ,CAAC,IAAI,WAAQ,CAAC;;YAGnD,IAAI,CAAC,QAAQ,CAAC,IAAI;IACX,eAAA,QAAQ,CAAC,IAAI,KAAKK,sBAAc,CAAC,IAAI,CAAC,IAAI;IAC1C,eAAA,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM;IACrB,eAAA,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAE1C;IACI,YAAA,IAAI,EAAE,CAAC;gBAEP,OAAO;IACV,SAAA;;;;YAKD,IAAM,UAAU,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAmB,CAAC;IAE5D,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAC7B;wCACe,IAAI,EAAA;IAEX,gBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;;IAEC,iBAAA;oBAED,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;oBAC3C,IAAM,OAAO,GAAGC,SAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;;IAG5E,gBAAA,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;IACvB,uBAAA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAA,EAAK,OAAAA,SAAG,CAAC,MAAM,CAACA,SAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,OAAO,CAAxC,EAAwC,CAAC,EAC5F;;IAEC,iBAAA;IAED,gBAAA,IAAM,OAAO,GAAG;wBACZ,WAAW,EAAE,QAAQ,CAAC,WAAW;IACjC,oBAAA,QAAQ,EAAED,sBAAc,CAAC,SAAS,CAAC,GAAG;IACtC,oBAAA,OAAO,EAAEA,sBAAc,CAAC,iBAAiB,CAAC,IAAI;IAC9C,oBAAA,cAAc,EAAE,QAAQ;wBACxB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;qBAC9B,CAAC;oBAEF,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;;IAzB3C,YAAA,KAAmB,UAAU,EAAV,YAAA,GAAA,UAAU,EAAV,EAAA,GAAA,YAAA,CAAA,MAAU,EAAV,EAAU,EAAA,EAAA;IAAxB,gBAAA,IAAM,IAAI,GAAA,YAAA,CAAA,EAAA,CAAA,CAAA;4BAAJ,IAAI,CAAA,CAAA;IA0Bd,aAAA;IACJ,SAAA;IAED,QAAA,IAAM,WAAW,GAAG;gBAChB,WAAW,EAAE,QAAQ,CAAC,WAAW;IACjC,YAAA,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,aAAa;IACzC,YAAA,cAAc,EAAE,QAAQ;aAC3B,CAAC;IAEF,QAAA,IAAM,YAAY,GAAG,iBAAiB,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;;IAGjF,QAAA,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,WAAW,CAAC,GAAmB,EAAA;gBAE7F,IAAI,GAAG,CAAC,KAAK,EACb;IACI,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAEhB,OAAO;IACV,aAAA;IAED,YAAA,IAAM,WAAW,GAAG,IAAI,WAAW,CAC/B,GAAG,CAAC,OAAO,EACX,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,GAAG,CACf,CAAC;IAEF,YAAA,WAAW,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,YAAA;IAErB,gBAAA,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;IACnC,gBAAA,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;IACzC,gBAAA,IAAI,EAAE,CAAC;IACX,aAAC,CAAC,CAAC;IACP,SAAC,CAAC,CAAC;SACN,CAAA;IAED;;;;IAIG;IACI,IAAA,iBAAA,CAAA,eAAe,GAAtB,UAAuB,QAAwB,EAAE,OAAe,EAAA;;YAG5D,IAAI,QAAQ,CAAC,SAAS,EACtB;IACI,YAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IACnC,SAAA;YAED,OAAOC,SAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnF,CAAA;;IA7GM,IAAA,iBAAA,CAAA,SAAS,GAAsBC,kBAAa,CAAC,MAAM,CAAC;QA8G/D,OAAC,iBAAA,CAAA;IAAA,CAjHD,EAiHC;;;;;;;;;;;;;;;;"}