/**
 * ============================================================================
 * RealtimeView.vue - 实时对话页面
 * ============================================================================
 * 
 * 🎯 核心功能：
 * - 实时语音对话
 * - Live2D模型嘴型同步
 * - AI配置管理
 * - 多模态交互
 * 
 * 🔧 Web Audio API 嘴型同步优化方案：
 * 
 * 【问题背景】
 * - Web Audio API限制：同一个HTMLAudioElement只能创建一次MediaElementAudioSource
 * - 原问题：第一段话嘴型正常，第二段开始变成简单张合，无法对应真实音频
 * - 根本原因：每次TTS音频播放都尝试重新创建MediaElementAudioSource，第二次及以后会失败
 * 
 * 【解决方案】
 * 1. 全局音频分析器管理 (globalAudioManager)：
 *    - 只在首次播放时创建一次AudioContext和MediaElementAudioSource
 *    - 后续所有TTS音频播放复用同一个分析器连接
 *    - 组件卸载时才清理资源
 * 
 * 2. 共享分析器方法 (startTTSMotionSyncWithSharedAnalyzer)：
 *    - Live2DDisplay新增方法，接收预创建的AnalyserNode
 *    - 避免重复创建Web Audio API连接
 *    - 确保所有音频片段都能正确分析和同步
 * 
 * 3. 初始化时机优化：
 *    - 在startRealtimeDialogue时预先初始化全局分析器
 *    - 确保首次TTS播放前分析器已就绪
 *    - 减少播放时的初始化延迟
 * 
 * 【技术细节】
 * - AudioContext: 全局唯一，会话期间保持活跃
 * - MediaElementAudioSource: 与audioPlayerRef绑定，只创建一次
 * - AnalyserNode: 配置用于人声频率分析，所有TTS音频共享
 * - 频率分析: 实时分析音频特征，驱动Live2D嘴型和元音参数
 * 
 * 【预期效果】
 * - 所有TTS音频段都能正确进行嘴型同步
 * - 嘴型动作与真实音频内容匹配
 * - 不再出现第二段后退化为简单张合的问题
 * ============================================================================
 */

<template>
  <div class="aaa-realtime-view" :class="{ 'fullscreen-mode': isFullscreen, 'dark-theme': isDarkTheme }">
    <!-- 🎯 背景现在由App.vue统一管理，这里不再需要重复的背景系统 -->

    <!-- 🎯 主布局网格系统（长屏设计） -->
    <div class="main-layout-grid">
      
      <!-- 🎭 顶部：Live2D舞台区域（完整宽度，大屏设计） -->
      <section class="live2d-stage-section" :class="{ 'stage-fullscreen': isFullscreen }">
        <!-- 🌀 炫酷的漩涡全屏切换按钮 -->
        <button 
          class="vortex-fullscreen-btn" 
          @click="toggleFullscreen"
          :title="isFullscreen ? '退出全屏' : '进入全屏'"
        >
          <div class="vortex-container">
            <div class="vortex-spiral"></div>
            <div class="vortex-glow"></div>
            <div class="vortex-center">
              <i class="btn-icon" :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
            </div>
          </div>
        </button>

        <!-- 🎭 浮动角色控制按钮组 -->
        <div v-if="!isFullscreen" class="floating-avatar-controls">
          <div class="avatar-btn" @click="onLive2DClick" title="与角色互动">
            <div class="avatar-glow-ring"></div>
            <i class="fas fa-hand-pointer"></i>
          </div>
          <div class="avatar-btn" @click="resetLive2DPose" title="重置姿态">
            <div class="avatar-glow-ring"></div>
            <i class="fas fa-undo"></i>
          </div>
          <div class="avatar-btn" @click="switchExpression" title="切换表情">
            <div class="avatar-glow-ring"></div>
            <i class="fas fa-smile"></i>
          </div>
        </div>

        <!-- 🎭 纯净的舞台容器 -->
        <div class="stage-container">
          <!-- Live2D角色视窗 -->
          <div class="live2d-character-viewport">
            <div class="character-stage">
              <!-- Live2D模型展示 - 使用Live2DDisplay组件 -->
              <Live2DDisplay
                ref="live2dDisplayRef"
                :selected-model="selectedLive2DModel"
                :show-debug="false"
                @model-loaded="handleLive2DModelLoaded"
                @model-error="handleLive2DModelError"
                @model-interact="handleLive2DInteract"
                @status-change="handleLive2DStatusChange"
              />
            </div>
          </div>
          
          <!-- 🎭 实时对话气泡叠加层 -->
          <div v-if="isTranscribing || isThinking" class="speech-bubble-overlay">
            <!-- 用户语音识别气泡 -->
            <div v-if="isTranscribing && currentTranscript" class="speech-bubble user-speech">
              <div class="bubble-content">
                <i class="speech-icon fas fa-microphone"></i>
                <span class="speech-text">{{ currentTranscript }}</span>
              </div>
            </div>
            
            <!-- AI思考气泡 -->
            <div v-if="isThinking" class="speech-bubble ai-thinking">
              <div class="bubble-content">
                <i class="speech-icon fas fa-brain"></i>
                <div class="thinking-animation">
                  <span class="speech-text">AI正在思考</span>
                  <div class="thinking-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 🎛️ 底部左侧：配置面板区域 (40%) -->
      <section class="configuration-panel-section">
        <div class="config-container">
          <div class="config-header">
            <h3 class="section-title">
              <span class="title-icon">🎛️</span>
              配置面板
            </h3>
              </div>

          <!-- 手风琴式配置面板 -->
          <div class="accordion-panels">
            
            <!-- 角色配置面板 -->
            <div class="config-panel character-panel" :class="{ expanded: panelStates.character }">
              <div class="panel-header" @click="togglePanel('character')">
                <div class="header-content">
                  <div class="panel-icon">🎭</div>
                  <h4>角色设定</h4>
                </div>
                <div class="collapse-indicator" :class="{ expanded: panelStates.character }">▼</div>
              </div>
              
              <div class="panel-body" :class="{ collapsed: !panelStates.character }">
                <!-- 角色配置组件 -->
                <CharacterConfigPanel 
                  @character-select="onCharacterSelect"
                  @character-edit="onCharacterEdit"
                  @character-refresh="onCharacterRefresh"
                  @character-create="onCharacterCreate"
                />
              </div>
            </div>
            
            <!-- 语音配置面板 -->
            <div class="config-panel voice-panel" :class="{ expanded: panelStates.voice }">
              <div class="panel-header" @click="togglePanel('voice')">
                <div class="header-content">
                  <div class="panel-icon">🎵</div>
                  <h4>语音配置</h4>
                </div>
                <div class="collapse-indicator" :class="{ expanded: panelStates.voice }">▼</div>
              </div>
              
              <div class="panel-body" :class="{ collapsed: !panelStates.voice }">
                <!-- 语音配置组件 -->
                <VoiceConfigPanel 
                  @voice-select="onVoiceSelect"
                  @voice-settings-change="onVoiceSettingsChange"
                />
              </div>
            </div>
            
            <!-- 🧩 AI配置面板 (动态适配不同的AI服务) -->
            <div class="config-panel ai-panel" :class="{ expanded: panelStates.ai }">
              <div class="panel-header" @click="togglePanel('ai')">
                <div class="header-content">
                  <div class="panel-icon">🤖</div>
                  <h4>AI配置</h4>
                </div>
                <div class="collapse-indicator" :class="{ expanded: panelStates.ai }">▼</div>
              </div>
              
              <div class="panel-body" :class="{ collapsed: !panelStates.ai }">
                <AIConfigPanel @config-change="handleAIConfigChange" />
              </div>
            </div>

            <!-- 🎭 Live2D模型配置面板 -->
            <div class="config-panel live2d-panel" :class="{ expanded: panelStates.live2d }">
              <div class="panel-header" @click="togglePanel('live2d')">
                <div class="header-content">
                  <div class="panel-icon">🎭</div>
                  <h4>Live2D模型</h4>
                </div>
                <div class="collapse-indicator" :class="{ expanded: panelStates.live2d }">▼</div>
              </div>
              
              <div class="panel-body" :class="{ collapsed: !panelStates.live2d }">
                <Live2DModelConfigPanel 
                  @model-select="handleLive2DModelSelect"
                  @model-loaded="handleLive2DModelLoaded"
                  @expression-change="handleLive2DExpression"
                  @motion-play="handleLive2DMotion"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 💬 底部右侧：对话记录区域 (60%) -->
      <section class="conversation-panel-section">
        <ConversationPanel 
          :is-conversation-active="isConversationActive"
          :realtime-status="realtimeStatus"
          @conversation-export="onConversationExport"
          @conversation-save="onConversationSave"
          @conversation-clear="onConversationClear"
          @message-replay="onMessageReplay"
          @conversation-toggle="onConversationToggle"
          @tts-audio-start="handleTTSAudioStart"
          @tts-audio-end="handleTTSAudioEnd"
          @tts-audio-error="handleTTSAudioError"
          @tts-sync-status-change="handleTTSSyncStatusChange"
        />
      </section>
    </div>

    <!-- 🚨 错误提示 -->
    <div v-if="hasError" class="error-overlay">
      <div class="error-card">
        <i class="fas fa-exclamation-triangle error-icon"></i>
        <h3>发生错误</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <button class="btn retry-btn" @click="retryLastOperation">
            <i class="fas fa-redo"></i> 重试
          </button>
          <button class="btn dismiss-btn" @click="dismissError">
            <i class="fas fa-times"></i> 忽略
          </button>
    </div>
      </div>
    </div>

    <!-- 🎵 隐藏的音频播放器 -->
    <audio ref="audioPlayerRef" style="display: none;" preload="auto"></audio>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineAsyncComponent, nextTick, watch } from 'vue'
import { useRealtimeStore } from '@/stores/modules/realtimeStore'
import { useLive2DStore } from '@/stores/modules/live2dStore'

// 导入Live2D模型配置类型
import { type ModelConfig } from '@/data/ModelDatabase'

// 导入API服务 - 添加真实的API调用
import * as API from '@/services/api'
// 🔧 修复：使用统一的WebSocket管理器替代原有的realtimeWS
// 🔧 修复：只使用useWebSocket，移除直接访问globalWebSocketManager
import { useWebSocket } from '@/composables/useWebSocket'

// 导入音频管理服务
import { voiceProfileAPI, type VoiceProfile } from '@/services/audioManager'

// 导入时间工具
import { getFormattedTimeForLLM } from '@/utils/timeUtils'

// 🔧 新增：导入实时对话状态管理器
import realtimeStateManager from '@/utils/realtimeStateManager'

// 消息提示系统 - 🔧 移除所有弹窗提示
// import { useMessage } from 'naive-ui'
// const message = useMessage()

// 🔧 替换为新的Live2DDisplay组件
import Live2DDisplay from '@/components/modules/realtime/Live2DDisplay.vue'
// 🔧 临时改为同步导入Live2DModelConfigPanel以解决事件绑定问题
import Live2DModelConfigPanel from '@/components/modules/realtime/Live2DModelConfigPanel.vue'
// 异步导入其他子组件以提高性能
// const Live2DStage = defineAsyncComponent(() => import('@/components/modules/realtime/Live2DStage.vue'))
const CharacterConfigPanel = defineAsyncComponent(() => import('@/components/modules/realtime/CharacterConfigPanel.vue'))
const VoiceConfigPanel = defineAsyncComponent(() => import('@/components/modules/realtime/VoiceConfigPanel.vue'))
const AIConfigPanel = defineAsyncComponent(() => import('@/components/modules/realtime/AIConfigPanel.vue'))
// const Live2DModelConfigPanel = defineAsyncComponent(() => import('@/components/modules/realtime/Live2DModelConfigPanel.vue'))
const ConversationPanel = defineAsyncComponent(() => import('@/components/modules/realtime/ConversationPanel.vue'))

// ===================== Store 状态管理 =====================
const realtimeStore = useRealtimeStore()
const live2dStore = useLive2DStore()  // 🔧 添加Live2D Store

// ===================== 响应式状态 =====================
// 布局相关状态
const isFullscreen = ref(false)
const isDarkTheme = ref(false)

// 面板状态管理
const panelStates = ref<Record<string, boolean>>({
  character: false,  // 关闭角色面板
  voice: false,
  ai: false,
  live2d: true       // 默认展开Live2D面板，方便用户选择模型
})

// 🧩 AI配置面板状态
const aiProvider = computed(() => realtimeStore.aiConfig.provider || 'OpenAI')

// 🎭 Live2D模型选择
const selectedLive2DModel = ref<any | null>(null)

// 对话控制状态
const isConversationActive = ref(false)
const isMuted = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const lastOperation = ref<(() => Promise<void>) | null>(null)

// 简化的状态管理（暂时不依赖stores）
const connectionStatus = ref<'connected' | 'connecting' | 'disconnected'>('disconnected')
const canStartConversation = ref(true)
const isTranscribing = ref(false)
const isThinking = ref(false)
const currentTranscript = ref('')

// Live2D相关状态
const selectedCharacter = ref('')
const live2dModel = ref({
  loading: false,
  loaded: false,
  error: '',
  loadProgress: 0
})
const canvasSize = ref({
  width: 800,
  height: 700
})

// 对话相关状态
const realtimeHistory = ref<any[]>([])
const conversationDuration = ref(0)
const latency = ref(120)

// 音频播放器引用
const audioPlayerRef = ref<HTMLAudioElement>()
const conversationContainer = ref<HTMLElement>()

// 🎯 新增：页面级WebSocket管理器实例
// 🔧 修复：使用专门的实时对话上下文，避免与其他模块冲突
const websocket = useWebSocket({
  pageName: '实时对话',
  context: 'realtime-dialogue', // 🔑 指定专门的上下文
  autoConnect: false, // 🔧 关闭自动连接，手动控制连接时机
  events: ['transcription', 'llm_response', 'tts_audio', 'tts_error', 'status_update', 'error', 'connected', 'disconnected']
})

// WebSocket管理器已创建

// 🎯 新增：全局音频分析器管理，解决Web Audio API重复创建问题
const globalAudioManager = ref<{
  audioContext: AudioContext | null
  mediaSource: MediaElementAudioSourceNode | null
  analyser: AnalyserNode | null
  isInitialized: boolean
}>({
  audioContext: null,
  mediaSource: null,
  analyser: null,
  isInitialized: false
})

// 🎯 初始化全局音频分析器（只执行一次）
const initializeGlobalAudioAnalyzer = async (): Promise<boolean> => {
  if (globalAudioManager.value.isInitialized) {
    console.log('🔄 全局音频分析器已初始化，复用现有连接')
    return true
  }

  if (!audioPlayerRef.value) {
    console.error('❌ 音频播放器元素不存在')
    return false
  }

  try {
    console.log('🎵 初始化全局音频分析器...')
    
    // 创建音频上下文
    const AudioCtx = window.AudioContext || (window as any).webkitAudioContext
    const audioContext = new AudioCtx()
    
    if (audioContext.state === 'suspended') {
      await audioContext.resume()
    }
    
    // ⚡ 关键：只创建一次 MediaElementAudioSource
    const mediaSource = audioContext.createMediaElementSource(audioPlayerRef.value)
    const analyser = audioContext.createAnalyser()
    
    // 配置分析器参数
    analyser.fftSize = 1024
    analyser.smoothingTimeConstant = 0.1
    analyser.minDecibels = -100
    analyser.maxDecibels = -3
    
    // 连接音频图
    mediaSource.connect(analyser)
    analyser.connect(audioContext.destination)
    
    // 保存到全局管理器
    globalAudioManager.value = {
      audioContext,
      mediaSource,
      analyser,
      isInitialized: true
    }
    
    console.log('✅ 全局音频分析器初始化成功')
    return true
    
  } catch (error) {
    console.error('❌ 全局音频分析器初始化失败:', error)
    return false
  }
}

// 🎯 清理全局音频分析器
const cleanupGlobalAudioAnalyzer = async () => {
  if (!globalAudioManager.value.isInitialized) return
  
  try {
    if (globalAudioManager.value.audioContext && globalAudioManager.value.audioContext.state !== 'closed') {
      await globalAudioManager.value.audioContext.close()
    }
    
    globalAudioManager.value = {
      audioContext: null,
      mediaSource: null,
      analyser: null,
      isInitialized: false
    }
    
    console.log('✅ 全局音频分析器已清理')
  } catch (error) {
    console.error('❌ 清理全局音频分析器失败:', error)
  }
}

// 添加完整的实时对话配置状态
const realtimeStatus = ref<'inactive' | 'active' | 'connecting'>('inactive')
const isStarting = ref(false)

// 角色和语音配置
const selectedCharacterCard = ref<any>(null)
const systemPrompt = ref('')
const temperature = ref(0.8)
const topP = ref(0.95)
const topK = ref(40)
const maxTokens = ref(512)
const useHistory = ref(true)

// 语音合成配置
const synthesisMode = ref('user-voice') // 'user-voice' | 'audio-path'
const selectedUserVoice = ref('')
const selectedProjectVoice = ref('')
const audioPathsText = ref('')
const userVoiceProfiles = ref<VoiceProfile[]>([])
const speed = ref(1.0)
const volume = ref(1.0)
const audioQuality = ref('高质量')

// 🎵 流式音频处理状态
const audioChunks = ref<string[]>([])
const currentChunkIndex = ref(0)
const isWaitingForAudioChunks = ref(false)
const audioChunkTimeout = ref<NodeJS.Timeout | null>(null)
const isAudioPlaying = ref(false)
const lastPlayedAudioUrls = ref<{url: string, timestamp: number}[]>([])

// 🔧 新增：音频播放开始时间，用于验证播放时长
const audioPlayStartTime = ref(0)

// AI配置
const aiConfig = computed(() => realtimeStore.aiConfig)

// === LLM模型管理 ===
const availableLLMModels = ref<string[]>([])
const currentLLMModel = ref<string | null>('LMstudio: csxl0.6')
const isLoadingLLMModels = ref(false)
const isSwitchingLLMModel = ref(false)

// 定时器
let durationTimer: NodeJS.Timeout | null = null

// ===================== 计算属性 =====================
const showRealtimeStatus = computed(() => isTranscribing.value || isThinking.value)

// ===================== 方法定义 =====================

// 🎯 粒子和能量流样式现在由App.vue统一管理

// 角色相关事件处理
const onCharacterSelect = (character: any) => {
  console.log('🎭 角色选择事件:', character.name)
  console.log('🎭 角色头像:', character.avatar)
  console.log('🎭 角色系统提示词长度:', character.systemPrompt?.length || 0)
  
  // 更新选中的角色
  selectedCharacter.value = character.name
  selectedCharacterCard.value = character
  
  // 更新系统提示词和相关配置
  systemPrompt.value = character.systemPrompt || ''
  
  // 同步到store - 这是关键！
  realtimeStore.setSelectedCharacter(character)
  
  // 更新Live2D相关状态
  if (character.live2d_model) {
    loadLive2DModel()
  } else {
    // 重置Live2D状态
    live2dModel.value.loaded = false
    live2dModel.value.error = ''
  }
  
  console.log('✅ 角色选择完成:', character.name)
  console.log('✅ Store中的角色:', realtimeStore.currentCharacter?.name)
}

const onCharacterEdit = (character: any) => {
  console.log('✏️ 角色编辑事件:', character.name)
  // 角色编辑完成后的逻辑
}

const onCharacterRefresh = () => {
  console.log('🔄 角色列表刷新事件')
  // 刷新完成后的逻辑
}

const onCharacterCreate = () => {
  console.log('➕ 创建新角色事件')
  // 打开角色管理页面
  const url = window.location.origin + '/character-manager'
  window.open(url, '_blank')
}

// 语音相关事件处理
const onVoiceSelect = (voiceId: string) => {
  console.log('🎵 语音选择事件:', voiceId)
  
  // 验证选择的音色配置
  const selectedVoiceProfile = userVoiceProfiles.value.find(profile => profile.id === voiceId)
  if (selectedVoiceProfile) {
    console.log('🎵 选择的音色配置:', selectedVoiceProfile.name, selectedVoiceProfile.audioFiles)
    
    // 检查音频文件配置
    if (!selectedVoiceProfile.audioFiles || selectedVoiceProfile.audioFiles.length === 0) {
      console.warn('⚠️ 选择的音色没有配置音频文件，可能会导致TTS失败')
      // 🔧 移除弹窗: message.warning(`音色 "${selectedVoiceProfile.name}" 没有配置音频文件，建议选择其他音色`)
    }
  }
  
  selectedUserVoice.value = voiceId
  
  // 如果对话正在进行，实时更新音色配置
  if (realtimeStatus.value === 'active') {
    updateRealtimeTTSConfig({
      userVoiceProfile: voiceId,
      mode: synthesisMode.value
    })
  }
}

const onVoiceSettingsChange = (settings: any) => {
  console.log('🔧 语音设置变更:', settings)
  
  // 更新语音设置
  if (settings.speed !== undefined) speed.value = settings.speed
  if (settings.volume !== undefined) volume.value = settings.volume
  if (settings.quality !== undefined) audioQuality.value = settings.quality
  if (settings.synthesisMode !== undefined) synthesisMode.value = settings.synthesisMode
  
  // 如果对话正在进行，实时更新配置
  if (realtimeStatus.value === 'active') {
    updateRealtimeTTSConfig({
      speed: speed.value,
      volume: volume.value,
      audioQuality: audioQuality.value,
      mode: synthesisMode.value
    })
  }
}

// AI配置相关事件处理
const onModelSelect = (modelName: string) => {
  console.log('🤖 AI模型选择事件:', modelName)
  
  // 直接调用模型切换方法
  handleLLMModelChange(modelName)
  
  // 更新store中的配置
  realtimeStore.updateAIConfig({
    ...aiConfig.value,
    modelName: modelName
  })
  
  console.log('✅ 模型选择完成，当前配置:', aiConfig.value)
}

const onAIConfigChange = (config: any) => {
  console.log('⚙️ AI配置变更:', config)
  
  // 更新AI配置到store，而不是直接赋值
  const newConfig = { ...aiConfig.value, ...config }
  realtimeStore.updateAIConfig(newConfig)
  
  // 如果对话正在进行，实时更新后端配置
  if (realtimeStatus.value === 'active') {
    updateRealtimeAIConfig(newConfig)
  }
}

const onAIPresetApply = (preset: any) => {
  console.log('📋 AI预设应用:', preset)
  
  // 构建新的配置对象
  const newConfig = {
    ...aiConfig.value,
    temperature: preset.temperature || 0.8,
    topP: preset.topP || 0.95,
    topK: preset.topK || 40,
    maxTokens: preset.maxTokens || 512
  }
  
  // 同步到store
  realtimeStore.updateAIConfig(newConfig)
  
  // 如果对话正在进行，实时更新后端配置
  if (realtimeStatus.value === 'active') {
    updateRealtimeAIConfig(newConfig)
  }
  
  console.log('✅ AI预设应用完成，当前配置:', aiConfig.value)
}

// 🧩 AI配置面板相关方法
const handleAIConfigChange = (config: any) => {
  console.log('🧩 AI配置面板配置变更:', config)
  onAIConfigChange(config)
}

// 🎭 Live2D模型配置面板相关方法

const handleLive2DModelSelect = (model: any) => {
  console.log('🎭 RealtimeView - handleLive2DModelSelect 被调用')
  console.log('🎭 接收到的模型数据:', {
    id: model.id,
    displayName: model.displayName,
    type: model.type,
    originalModel: model
  })
  console.log('🎭 当前 selectedLive2DModel.value:', selectedLive2DModel.value)
  
  selectedLive2DModel.value = model
  console.log('🎭 已设置 selectedLive2DModel.value 为:', selectedLive2DModel.value)
  
  // 清理之前的模型状态
  live2dModel.value.loaded = false
  live2dModel.value.loading = false
  live2dModel.value.error = ''
  console.log('✅ Live2D模型已设置，等待Live2DStage加载')
}

const handleLive2DModelLoaded = (model: any) => {
  console.log('🎭 Live2D模型加载完成:', model)
  // 更新模型状态
  live2dModel.value.loaded = true
  live2dModel.value.loading = false
}

const handleLive2DModelError = (error: string) => {
  console.log('❌ Live2D模型加载失败:', error)
  live2dModel.value.error = error
  live2dModel.value.loading = false
  live2dModel.value.loaded = false
}

const handleLive2DInteract = (data: { x: number; y: number; type: string }) => {
  // Live2D交互事件日志已移除，避免控制台刷屏
  // 这里可以添加交互反馈逻辑
}

const handleLive2DExpression = (expression: string) => {
  console.log('🎭 Live2D表情变化:', expression)
  // 这里可以添加表情变化的额外处理
}

const handleLive2DMotion = (group: string, index?: number) => {
  console.log('🎭 Live2D动作播放:', group, index)
  // 这里可以添加动作播放的额外处理
}

const handleLive2DStatusChange = (status: string) => {
  console.log('📊 Live2D状态变化:', status)
  // 这里可以添加状态变化的额外处理
}

const handleFullscreenToggle = (isFullscreen: boolean) => {
  console.log('🖥️ Live2D全屏切换:', isFullscreen)
  // 这里可以添加全屏状态的额外处理
}

// 全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  console.log('🖥️ 全屏模式:', isFullscreen.value ? '开启' : '关闭')
}

// 面板展开/收缩
const togglePanel = (panelKey: string) => {
  panelStates.value[panelKey] = !panelStates.value[panelKey]
  console.log('🎛️ 面板状态:', panelKey, panelStates.value[panelKey] ? '展开' : '收缩')
}

// 状态文本获取
const getStatusText = () => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中...'
    default: return '未连接'
  }
}

// Live2D相关方法
const onLive2DClick = () => {
  console.log('👆 Live2D交互触发')
}

const resetLive2DPose = () => {
  console.log('🔄 重置Live2D姿态')
}

const switchExpression = () => {
  console.log('😊 切换Live2D表情')
}

const loadLive2DModel = () => {
  console.log('📥 加载Live2D模型')
    live2dModel.value.loading = true
  // 模拟加载过程
  setTimeout(() => {
    live2dModel.value.loading = false
    live2dModel.value.loaded = true
  }, 2000)
}

const retryLoadLive2D = () => {
  console.log('🔄 重试加载Live2D模型')
  live2dModel.value.error = ''
  loadLive2DModel()
}

// 对话控制
const toggleConversation = async () => {
  const operation = async () => {
    if (isConversationActive.value) {
      // 停止对话
      isConversationActive.value = false
      isTranscribing.value = false
      isThinking.value = false
      connectionStatus.value = 'disconnected'
      console.log('🛑 停止对话')
      } else {
      // 开始对话
      isConversationActive.value = true
      connectionStatus.value = 'connected'
      console.log('▶️ 开始对话')
    }
  }
  
  lastOperation.value = operation
  await safeExecute(operation)
}

// 静音切换
const toggleMute = () => {
  isMuted.value = !isMuted.value
  console.log('🔇 静音状态:', isMuted.value ? '开启' : '关闭')
}

// 对话操作
const exportConversation = () => {
  console.log('📤 导出对话')
  // 实现导出逻辑
}

const clearRealtimeHistory = () => {
  console.log('🗑️ 清空对话历史')
  realtimeHistory.value = []
}

// 工具方法
const formatTime = (timestamp: Date | string): string => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp
  if (isNaN(date.getTime())) {
    return '无效时间'
  }
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 安全执行方法
const safeExecute = async (operation: () => Promise<void>) => {
  try {
    await operation()
  } catch (error) {
    console.error('❌ 操作执行失败:', error)
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : '未知错误'
  }
}

// 错误处理
const retryLastOperation = async () => {
  if (lastOperation.value) {
    hasError.value = false
    await safeExecute(lastOperation.value)
  }
}

const dismissError = () => {
  hasError.value = false
  errorMessage.value = ''
}

// ===================== ConversationPanel 事件处理 =====================
const onConversationExport = (format: string, data: any) => {
  console.log('📤 对话导出事件:', format, data)
  // 这里可以添加导出后的额外处理逻辑
}

const onConversationSave = (data: any) => {
  console.log('💾 对话保存事件:', data)
  // 这里可以添加保存后的额外处理逻辑
}

const onConversationClear = () => {
  console.log('🗑️ 对话清空事件')
  // 清空本地状态
  realtimeHistory.value = []
  conversationDuration.value = 0
  // 这里可以添加清空后的额外处理逻辑
}

const onMessageReplay = (message: any) => {
  console.log('🔊 消息重播事件:', message.content)
  // 这里可以添加语音重播逻辑
  if (audioPlayerRef.value && message.audioUrl) {
    audioPlayerRef.value.src = message.audioUrl
    audioPlayerRef.value.play()
  }
}

const onConversationToggle = async (isActive: boolean) => {
  console.log('💬 对话状态切换:', isActive ? '开始对话' : '停止对话')
  
  if (isActive) {
    // 开始对话
    await startRealtimeDialogue()
  } else {
    // 停止对话
    await stopRealtimeDialogue()
  }
}

// ===================== TTS音频事件处理（驱动Live2D嘴型同步）=====================
const live2dDisplayRef = ref<InstanceType<typeof Live2DDisplay> | null>(null)

/**
 * 处理TTS音频开始播放事件
 * 启动Live2D模型的嘴型同步功能 - 使用复用的Web Audio API连接
 */
const handleTTSAudioStart = async (data: { message: any, audioElement: HTMLAudioElement }) => {
  console.log('🎤 TTS音频开始播放 - 启动Live2D嘴型同步', {
    messageContent: data.message.content,
    audioSrc: data.audioElement.src,
    audioDuration: data.audioElement.duration
  })
  
  // 🎯 确保全局音频分析器已初始化
  if (!globalAudioManager.value.isInitialized) {
    console.log('🔧 首次播放，初始化全局音频分析器...')
    const initSuccess = await initializeGlobalAudioAnalyzer()
    if (!initSuccess) {
      console.error('❌ 全局音频分析器初始化失败，无法启动精确嘴型同步')
      return
    }
  }
  
  if (live2dDisplayRef.value && globalAudioManager.value.isInitialized) {
    // 🎯 使用复用的音频分析器启动嘴型同步
    live2dDisplayRef.value.startTTSMotionSyncWithSharedAnalyzer(
      data.audioElement,
      globalAudioManager.value.analyser!
    )
      .then(() => {
        console.log('✅ Live2D TTS嘴型同步启动成功 (复用连接)')
      })
      .catch((error: any) => {
        console.error('❌ Live2D TTS嘴型同步启动失败:', error)
      })
  } else {
    console.warn('⚠️ Live2DDisplay组件或音频分析器不可用，无法启动嘴型同步')
  }
}

/**
 * 处理TTS音频播放结束事件
 * 停止Live2D模型的嘴型同步功能
 */
const handleTTSAudioEnd = (data: { message: any, audioElement: HTMLAudioElement }) => {
  console.log('🔇 TTS音频播放结束 - 停止Live2D嘴型同步', {
    messageContent: data.message.content,
    playedDuration: data.audioElement.currentTime
  })
  
  if (live2dDisplayRef.value) {
    // 停止Live2D模型的TTS嘴型同步
    live2dDisplayRef.value.stopTTSMotionSync()
      .then(() => {
        console.log('✅ Live2D TTS嘴型同步停止成功')
      })
      .catch((error: any) => {
        console.error('❌ Live2D TTS嘴型同步停止失败:', error)
      })
  }
}

/**
 * 处理TTS音频播放错误事件
 */
const handleTTSAudioError = (data: { message: any, error: string }) => {
  console.error('❌ TTS音频播放错误:', {
    messageContent: data.message.content,
    error: data.error
  })
  
  // 确保在音频错误时停止嘴型同步
  if (live2dDisplayRef.value) {
    live2dDisplayRef.value.stopTTSMotionSync()
      .catch((error: any) => {
        console.error('❌ 音频错误时停止嘴型同步失败:', error)
      })
  }
}

/**
 * 处理TTS同步状态变化事件
 */
const handleTTSSyncStatusChange = (data: { isActive: boolean, message?: any }) => {
  console.log('🔄 TTS同步状态变化:', {
    isActive: data.isActive,
    hasMessage: !!data.message
  })
  
  // 可以在这里添加UI状态更新逻辑
  // 例如显示/隐藏嘴型同步指示器
}

// === 构建系统提示词（包含时间信息）===
const buildRealtimeSystemPrompt = (basePrompt: string): string => {
  let prompt = basePrompt
  
  // 🔧 修改：将时间信息添加到最后
  try {
    const timeInfo = getFormattedTimeForLLM()
    prompt += '\n\n' + timeInfo
  } catch (error) {
    console.warn('⚠️ RealtimeView: 获取时间信息失败，使用基础时间信息:', error)
    // 如果时间工具失败，添加基础时间信息作为后备方案
    const now = new Date()
    const timeStr = now.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit',
      weekday: 'long'
    })
    prompt += `\n\n## 当前时间信息（不是用户生辰的时间）\n**现在的时间是，${timeStr}\n\n*给用户指导时可以结合当下的时间*\n\n`
  }
  
  return prompt
}

// === 实时对话核心方法（真实API调用） ===
const startRealtimeDialogue = async () => {
  isStarting.value = true
  realtimeStatus.value = 'connecting'
  connectionStatus.value = 'connecting'
  
  try {
    console.log('🎭 开始启动实时对话...')
    
    // 简化：只在第一次启动时连接WebSocket和注册事件处理器
    console.log('🔌 连接WebSocket监听器...')
    
    if (!websocket.isConnected.value) {
      await websocket.connect()
    }
    
    await connectRealtimeWebSocket()
    
    // 🔧 修复：实时对话期间暂停健康检查，避免误断开连接
    console.log('⏸️ 暂停WebSocket健康检查（实时对话期间）')
    websocket.manager.pauseHealthCheck()
    
    // 🎵 预激活音频上下文，确保后续音频能自动播放
    console.log('🎵 预激活音频上下文...')
    if (audioPlayerRef.value) {
      // 创建并播放一个静音音频来激活音频上下文
      const silentAudio = new Audio('data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA')
      silentAudio.volume = 0
      try {
        await silentAudio.play()
        console.log('✅ 音频上下文预激活成功')
      } catch (e) {
        console.log('⚠️ 音频上下文预激活失败，但继续启动:', e)
      }
      
      // 🎯 初始化全局音频分析器
      console.log('🎵 初始化全局音频分析器...')
      const initSuccess = await initializeGlobalAudioAnalyzer()
      if (initSuccess) {
        console.log('✅ 全局音频分析器初始化成功')
      } else {
        console.warn('⚠️ 全局音频分析器初始化失败，将使用备用嘴型同步方案')
      }
    }
    
    // 从store获取配置或使用默认值
    const currentCharacter = realtimeStore.selectedCharacter || selectedCharacterCard.value
    console.log('🎭 当前角色:', currentCharacter?.name || '默认助手')
    
    // 🔧 构建启动参数
    const startParams = {
      // 合成模式配置（用于TTS）
      mode: synthesisMode.value,
      synthesisMode: synthesisMode.value,
      disableVAD: false, // 🔧 关键：RealtimeView启用VAD进行实时对话
      
      // TTS语音配置
      ttsConfig: synthesisMode.value === 'user-voice' ? {
        mode: 'user-voice',
        userVoiceProfile: selectedUserVoice.value || 'default',
        speed: speed.value,
        volume: volume.value,
        audioQuality: audioQuality.value
      } : {
        mode: 'audio-path',
        audioPathsText: audioPathsText.value || 'default',
        speed: speed.value,
        volume: volume.value,
        audioQuality: audioQuality.value
      },
      
      // LLM角色配置
      llmConfig: {
        characterName: currentCharacter?.name || selectedCharacter.value || '默认助手',
        systemPrompt: buildRealtimeSystemPrompt(currentCharacter?.systemPrompt || systemPrompt.value || '你是一个友善的AI助手。'),
        temperature: aiConfig.value.temperature,
        topP: aiConfig.value.topP,
        topK: aiConfig.value.topK,
        maxTokens: aiConfig.value.maxTokens,
        useHistory: aiConfig.value.useHistory,
        modelName: aiConfig.value.modelName || currentLLMModel.value || undefined,
        provider: aiConfig.value.provider || 'lmstudio'
      }
    }
    
    console.log('📋 启动配置:', JSON.stringify(startParams, null, 2))
    console.log('🔍 关键配置检查:')
    console.log('  - AI服务商 (aiConfig.provider):', aiConfig.value.provider)
    console.log('  - LLM模型名称 (aiConfig.modelName):', aiConfig.value.modelName)
    console.log('  - 当前LLM模型:', currentLLMModel.value)
    console.log('  - Store中的配置:', JSON.stringify(realtimeStore.aiConfig, null, 2))
    
    // 🔧 参数验证
    if (!startParams.llmConfig.modelName) {
      console.warn('⚠️ 缺少模型名称，使用默认模型')
      startParams.llmConfig.modelName = 'LMstudio: csxl0.6'
    }
    
    if (!startParams.llmConfig.provider) {
      console.warn('⚠️ 缺少AI服务商，使用默认值')
      startParams.llmConfig.provider = 'lmstudio'
    }
    
    console.log('🎯 最终启动参数验证通过，开始调用API...')
    
    // 🔧 会话冲突检测和处理
    try {
      console.log('🔍 检查现有会话状态...')
      const statusCheck = await API.getRealtimeStatus()
      if (statusCheck.success && statusCheck.data?.sessionInfo?.hasActiveSession) {
        const currentSession = statusCheck.data.sessionInfo.currentSession
        console.warn('🚨 检测到活跃会话冲突:', currentSession)
        
        // 强制停止现有会话
        console.log('🛑 强制停止冲突的会话...')
        await API.stopRealtimeDialogue()
        
        // 等待一段时间确保清理完成
        await new Promise(resolve => setTimeout(resolve, 1000))
        console.log('✅ 冲突会话已清理')
      }
    } catch (statusError: any) {
      console.warn('⚠️ 会话状态检查失败:', statusError)
      // 继续启动流程
    }
    
    // 🔧 新增：LMstudio服务状态预检
    try {
      console.log('🔍 检查LMstudio服务状态...')
      const lmStudioStatus = await API.checkLMStudioStatus()
      if (!lmStudioStatus.isAvailable) {
        console.warn('⚠️ LMstudio服务不可用，但继续尝试启动')
        // 不阻止启动流程，只是警告
      } else {
        console.log('✅ LMstudio服务预检通过')
      }
    } catch (lmStudioError: any) {
      console.warn('⚠️ LMstudio状态检查失败:', lmStudioError)
      // 不阻止启动流程
    }
    
    // 🔧 快速健康检查
    try {
      console.log('🏥 检查后端服务健康状态...')
      const healthCheck = await fetch('/api/health')
      if (!healthCheck.ok) {
        throw new Error(`后端服务不可用 (HTTP ${healthCheck.status})`)
      }
      console.log('✅ 后端服务健康检查通过')
    } catch (healthError: any) {
      console.error('❌ 后端服务健康检查失败:', healthError)
      // 🔧 不抛出错误，允许继续尝试启动实时对话
      console.warn('⚠️ 健康检查失败，但继续尝试启动实时对话...')
    }
    
    // 🔧 使用改进的LMstudio错误处理和重试机制
    const result = await API.retryWithLMStudioFallback(
      () => API.startRealtimeDialogue(startParams),
      2, // 最多重试2次
      1500 // 重试间隔1.5秒
    )
    
    if (result.success) {
      realtimeStatus.value = 'active'
      connectionStatus.value = 'connected'
      isConversationActive.value = true
      
      // 开始计时和监听
      startConversationTimer()
      startRealtimeListening()
      
      // 🔧 WebSocket已在启动前连接，无需重复连接
      
      // 添加欢迎消息
      addRealtimeMessage('assistant', '你好！我已经准备好进行实时对话了。你可以直接开始说话。', 0.95)
      
      console.log('✅ 实时对话启动成功')
      console.log('📊 启动结果详情:', {
        success: result.success,
        data: result.data,
        message: result.message
      })
      // 🔧 移除弹窗: message.success('实时对话已启动')
    } else {
      console.error('❌ 后端API返回失败:', {
        success: result.success,
        message: result.message,
        data: result.data,
        error: result.error
      })
      throw new Error(result.message || '实时对话启动失败')
    }
    
  } catch (error: any) {
    realtimeStatus.value = 'inactive'
    connectionStatus.value = 'disconnected'
    isConversationActive.value = false
    
    // 🔧 改进错误处理：识别LMstudio相关错误
    let errorMessage = error.message || '实时对话启动失败'
    let userFriendlyMessage = ''
    
    if (errorMessage.includes('404') || errorMessage.includes('LMstudio响应失败')) {
      userFriendlyMessage = '⚠️ AI服务暂时不可用，可能是页面切换导致的状态冲突。请尝试：\n' +
                           '1. 刷新页面重试\n' +
                           '2. 检查是否在其他页面（如周易测算）使用了AI服务\n' +
                           '3. 等待片刻后再试'
      
      console.error('🚨 LMstudio连接失败详情:', {
        error: errorMessage,
        suggestion: '这通常是页面间AI服务冲突导致的，建议刷新页面'
      })
    } else if (errorMessage.includes('连接失败') || errorMessage.includes('网络')) {
      userFriendlyMessage = '🌐 网络连接问题，请检查网络状态后重试'
    } else {
      userFriendlyMessage = `❌ ${errorMessage}`
    }
    
    // 显示用户友好的错误提示（可以考虑用notification代替console）
    if (import.meta.env.DEV) {
      console.error('🔧 开发模式错误详情:', {
        original: error,
        userMessage: userFriendlyMessage,
        stack: error.stack
      })
    }
    
    // 可以在这里添加用户通知，例如：
    // message.error(userFriendlyMessage)
    
    console.error('启动错误:', error)
  } finally {
    isStarting.value = false
  }
}

// 🔧 新增：防重复停止保护
let isStoppingDialogue = false;

const stopRealtimeDialogue = async () => {
  // 🔧 新增：防重复调用保护
  if (isStoppingDialogue) {
    console.log('⏭️ [RealtimeView] 停止实时对话正在进行中，跳过重复调用');
    return;
  }

  isStoppingDialogue = true;

  try {
    console.log('🛑 停止实时对话...')

    // 🔧 先更新状态，防止重连
    isConversationActive.value = false
    realtimeStatus.value = 'inactive'
    isTranscribing.value = false
    isThinking.value = false
    
    // 先保存历史记录（如果有）
    if (realtimeHistory.value.length > 0) {
      // 可以添加自动保存逻辑
      console.log('💾 保存对话历史...')
    }
    
    // 清空后端LLM上下文
    try {
      const clearResult = await API.clearRealtimeHistory()
      if (clearResult.success) {
        console.log('✅ LLM上下文已清空')
      } else {
        console.warn('⚠️ 清空LLM上下文失败:', clearResult.message)
      }
    } catch (clearError) {
      console.warn('⚠️ 清空LLM上下文异常:', clearError)
    }
    
    // 调用后端API停止实时对话
    const result = await API.stopRealtimeDialogue()
    
    if (!result.success) {
      console.warn('停止API调用失败:', result.message)
    }
    
    stopConversationTimer()
    stopRealtimeListening()
    
    // 🔧 修复：恢复WebSocket健康检查
    console.log('▶️ 恢复WebSocket健康检查')
    websocket.manager.resumeHealthCheck()
    
    // 🔧 关键修复：停止对话时不断开WebSocket，只更新状态
    connectionStatus.value = 'disconnected'
    console.log('🔌 [RealtimeView] 停止对话，但保持WebSocket连接以便快速重启')
    
    // 🔧 验证WebSocket连接状态，确保下次启动时连接可用
    if (!websocket.isConnected.value) {
      console.warn('⚠️ [RealtimeView] WebSocket连接已断开，下次启动时需要重连')
    } else {
      console.log('✅ [RealtimeView] WebSocket连接保持正常，下次启动将立即可用')
    }
    
    // 🎯 注意：不在这里清理全局音频分析器，因为它需要在整个会话期间保持活跃
    // 只在组件卸载时清理
    
    console.log('✅ 实时对话已停止')
    // 🔧 移除弹窗: message.info('实时对话已停止')
  } catch (error: any) {
    console.error('停止实时对话失败:', error)
    // 🔧 移除弹窗: message.error(`停止失败: ${error.message}`)
  } finally {
    // 🔧 新增：重置防重复调用标记
    setTimeout(() => {
      isStoppingDialogue = false;
    }, 1000); // 1秒后允许再次调用
  }
}

// === 实时对话辅助方法 ===
const startConversationTimer = () => {
  durationTimer = setInterval(() => {
    conversationDuration.value++
  }, 1000)
}

const stopConversationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
}

const startRealtimeListening = () => {
  console.log('🎤 启动实时语音监听...')
  // 这里可以添加语音识别相关的逻辑
}

const stopRealtimeListening = () => {
  console.log('🎤 停止实时语音监听...')
  // 这里可以添加停止语音识别的逻辑
}

// === WebSocket实时监听 ===
// 标记是否已经注册过事件处理器
let isEventHandlersRegistered = false

const connectRealtimeWebSocket = async () => {
  console.log('🔌 [RealtimeView] 连接WebSocket实时监听...')
  
  // 防止重复注册：只在第一次或连接断开后重新注册
  if (isEventHandlersRegistered && websocket.isConnected.value) {
    console.log('✅ [RealtimeView] 事件处理器已注册且连接正常，跳过重复注册')
    return
  }
  
  const handlersConfig = {
    onConnected: (data: any) => {
      console.log('✅ [RealtimeView] WebSocket连接成功', data)
      connectionStatus.value = 'connected'
    },
    onDisconnected: (data: any) => {
      console.log('❌ [RealtimeView] WebSocket连接断开', data)
      connectionStatus.value = 'disconnected'
      isEventHandlersRegistered = false // 连接断开时重置标记
    },
    onError: (error: any) => {
      console.error('❌ [RealtimeView] WebSocket错误', error)
      connectionStatus.value = 'disconnected'
      isEventHandlersRegistered = false // 错误时重置标记
    },
    onTranscription: (data: any) => {
      console.log('🎤 [RealtimeView] 收到转录事件', data)
      handleTranscriptionUpdate(data)
    },
    onLLMResponse: (data: any) => {
      console.log('🤖 [RealtimeView] 收到LLM回复事件', data)
      handleLLMResponse(data)
    },
    onTTSAudio: (data: any) => {
      console.log('🎵 [RealtimeView] 收到TTS音频事件', data)
      handleTTSAudio(data)
    },
    onTTSError: (error: any) => {
      console.error('🎵 [RealtimeView] TTS错误事件', error)
      handleTTSError(error)
    },
    onStatusUpdate: (data: any) => {
      console.log('📊 [RealtimeView] 收到状态更新事件', data)
      handleStatusUpdate(data)
    }
  }
  
  try {
    await websocket.registerHandlers(handlersConfig)
    isEventHandlersRegistered = true
    console.log('✅ [RealtimeView] WebSocket事件注册完成')
  } catch (error) {
    console.error('❌ [RealtimeView] WebSocket事件注册失败:', error)
    isEventHandlersRegistered = false
  }
}

const disconnectRealtimeWebSocket = () => {
  console.log('🔌 [RealtimeView] 断开WebSocket连接')
  websocket.disconnect()
  connectionStatus.value = 'disconnected'
  isEventHandlersRegistered = false // 断开连接时重置标记
}

// === WebSocket事件处理器 ===
const handleTranscriptionUpdate = (data: any) => {
  console.log('🎤 [RealtimeView] 收到转录原始数据:', JSON.stringify(data, null, 2))
  
  // 🔧 数据格式修复：提取真正的payload数据
  const actualData = data.payload || data
  console.log('🔍 [DEBUG] 转录提取的实际数据:', JSON.stringify(actualData, null, 2))
  
  console.log('🎤 [RealtimeView] 收到转录更新:', {
    hasCurrent: !!actualData.currentTranscript,
    currentText: actualData.currentTranscript ? `"${actualData.currentTranscript.substring(0, 50)}..."` : 'null',
    hasFinal: !!actualData.finalTranscript,
    finalText: actualData.finalTranscript ? `"${actualData.finalTranscript.substring(0, 50)}..."` : 'null',
    confidence: actualData.confidence
  })

  if (actualData.currentTranscript) {
    isTranscribing.value = true
    currentTranscript.value = actualData.currentTranscript
  }
  
  if (actualData.finalTranscript) {
    isTranscribing.value = false
    currentTranscript.value = ''
    
    console.log('🎯 最终转录结果:', actualData.finalTranscript)
    
    // 避免重复添加转录结果
    const isDuplicate = realtimeHistory.value.some(msg => 
      msg.role === 'user' && 
      msg.content === actualData.finalTranscript &&
      Math.abs(new Date().getTime() - new Date(msg.timestamp).getTime()) < 5000 // 5秒内重复
    )
    
    if (!isDuplicate) {
      console.log('📝 添加用户转录消息，启用打字机效果')
      addRealtimeMessage('user', actualData.finalTranscript, actualData.confidence || 0.9)
    } else {
      console.log('⏭️ 跳过重复的转录结果')
    }
  }
}

const handleLLMResponse = (data: any) => {
  console.log('🤖 [RealtimeView] 收到LLM回复原始数据:', JSON.stringify(data, null, 2))
  
  // 🔧 数据格式修复：提取真正的payload数据
  const actualData = data.payload || data
  console.log('🔍 [DEBUG] LLM提取的实际数据:', JSON.stringify(actualData, null, 2))
  
  console.log('🤖 [RealtimeView] 收到LLM回复:', {
    thinking: !!actualData.thinking,
    response: actualData.response ? `"${actualData.response.substring(0, 50)}..."` : 'null',
    responseLength: actualData.response?.length || 0,
    hasCharacterInfo: !!(actualData.character_name || actualData.character_avatar)
  })

  if (actualData.thinking) {
    isThinking.value = true
    return
  } 
  
  if (actualData.response) {
    isThinking.value = false
    
    // 🔧 关键修复：简化流式更新逻辑，确保完整内容能够显示
    const lastAssistantMsg = [...realtimeHistory.value].reverse().find(msg => msg.role === 'assistant')
    
    // 检查是否为流式更新（时间窗口内的消息）
    if (lastAssistantMsg) {
      const timeDiff = Math.abs(new Date().getTime() - new Date(lastAssistantMsg.timestamp).getTime())
      console.log('🔄 检查流式更新，时间差:', timeDiff, 'ms')
      
      if (timeDiff < 10000) { // 扩大时间窗口到10秒
        const oldContent = lastAssistantMsg.content || ''
        const newContent = actualData.response || ''
        
        console.log('🔍 内容比较:', {
          oldLength: oldContent.length,
          newLength: newContent.length,
          oldContent: oldContent.substring(0, 50) + '...',
          newContent: newContent.substring(0, 50) + '...'
        })
        
        // 🔧 重要修复：只要新内容比旧内容长，就认为是流式更新
        if (newContent.length > oldContent.length) {
          console.log('✅ 确认为流式更新，更新现有消息')
          lastAssistantMsg.content = newContent
          lastAssistantMsg.timestamp = new Date()
          
          // 🎭 确保角色信息正确传递
          if (data.character_name) lastAssistantMsg.character_name = data.character_name
          if (data.character_avatar) lastAssistantMsg.character_avatar = data.character_avatar
          
          // 同步更新到store
          realtimeStore.updateMessage(lastAssistantMsg.id, {
            content: newContent,
            timestamp: new Date(),
            character_name: data.character_name,
            character_avatar: data.character_avatar
          })
          
          return // 不添加新消息，只更新现有消息
        }
        
        // 🔧 如果内容完全相同，跳过重复
        if (newContent === oldContent) {
          console.log('⏭️ 跳过重复内容')
          return
        }
      }
    }
    
    // 🎯 添加新的完整消息
    console.log('📝 [RealtimeView] 准备添加新的AI回复:', {
      内容长度: actualData.response.length,
      内容预览: actualData.response.substring(0, 100) + '...',
      角色名称: actualData.character_name,
      有头像: !!actualData.character_avatar
    })
    
    addRealtimeMessage('assistant', actualData.response, 0.95, {
      character_name: actualData.character_name,
      character_avatar: actualData.character_avatar
    })
    
    console.log('📝 [RealtimeView] AI回复已添加，当前历史消息数:', realtimeHistory.value.length)
    console.log('📝 [RealtimeView] Store中的消息数:', realtimeStore.conversationHistory.length)
  }
}

const handleTTSAudio = (data: any) => {
  console.log('🎵 [实时对话] 收到TTS音频回调')
  
  // 🔧 数据格式修复：提取真正的payload数据
  const actualData = data.payload || data
  
  // 🔧 修复：只记录关键信息，不打印Base64数据
  const audioUrl = actualData.audioUrl || actualData.audio_url
  const hasAudio = !!(audioUrl && audioUrl.length > 0)
  console.log('🔍 [DEBUG] TTS音频信息:', {
    hasAudioUrl: hasAudio,
    audioUrlType: audioUrl ? (audioUrl.startsWith('data:') ? 'base64' : 'url') : 'none',
    audioSize: audioUrl ? `${Math.round(audioUrl.length / 1024)}KB` : '0KB',
    dataKeys: Object.keys(actualData)
  })
  
  // 🔧 关键修复：添加页面活跃性和DOM安全检查
  if (!websocket.isConnected.value) {
    console.warn('⚠️ [实时对话] WebSocket未连接，跳过TTS音频处理')
    return
  }
  
  // 验证audio元素引用是否有效
  if (!audioPlayerRef.value) {
    console.error('❌ [实时对话] audioPlayerRef未找到，无法播放TTS音频')
    return
  }
  
  // 🔧 修复：移除错误的页面活跃性检查，因为getStatus()没有currentActivePage属性
  // 改为检查对话状态和WebSocket连接状态
  if (!isConversationActive.value) {
    console.warn('⚠️ [实时对话] 对话未激活，跳过TTS音频处理')
    return
  }
  if (audioUrl) {
    console.log('🎵 [实时对话] 准备播放TTS音频:', {
      audioSize: `${Math.round(audioUrl.length / 1024)}KB`,
      audioType: audioUrl.startsWith('data:audio/') ? 'base64' : 'url',
      isConversationActive: isConversationActive.value,
      hasAudioPlayerRef: !!audioPlayerRef.value
    })
    
    // 🎯 记录WebSocket播放的音频，避免轮询重复
    const currentTime = new Date().getTime()
    lastPlayedAudioUrls.value.push({
      url: audioUrl,
      timestamp: currentTime
    })
    
    // 🎯 清理老旧记录，避免内存泄漏
    lastPlayedAudioUrls.value = lastPlayedAudioUrls.value.filter(audioInfo => 
      (currentTime - audioInfo.timestamp) < 30000 // 只保留30秒内的记录
    )
    
    // 🚀 支持流式音频
    const isContinuation = actualData.is_continuation || actualData.isContinuation || false
    const isFirstChunk = actualData.is_first_chunk || actualData.isFirstChunk || (!isContinuation && (actualData.chunk_index === 0 || actualData.chunk_index === undefined))
    const isLastChunk = actualData.is_last_chunk || actualData.isLastChunk || false
    const chunkIndex = actualData.chunk_index || actualData.chunkIndex || 0
    const totalChunks = actualData.total_chunks || actualData.totalChunks || 1
    
    if (isContinuation || totalChunks > 1) {
      // 流式音频片段播放日志已移除，避免控制台刷屏
    }
    
    if (totalChunks > 1) {
      console.log('🎵 [流式音频]:', `片段 ${chunkIndex + 1}/${totalChunks}`)
    }
    
    // 🚀 流式音频处理逻辑
    if (totalChunks > 1) {
      // 初始化流式播放状态
      if (isFirstChunk && !isContinuation) {
        console.log('🎵 开始流式音频播放序列')
        audioChunks.value = new Array(totalChunks).fill(null) // 预分配数组空间
        currentChunkIndex.value = 0
        isWaitingForAudioChunks.value = true
        
        // 减少超时时间，提高响应性
        if (audioChunkTimeout.value) {
          clearTimeout(audioChunkTimeout.value)
        }
        audioChunkTimeout.value = setTimeout(() => {
          console.warn('⚠️ 流式音频超时，开始播放已接收的片段')
          isWaitingForAudioChunks.value = false
          playNextAudioChunk()
        }, 6000) // 减少到6秒超时
      }
      
      // 存储音频片段
      audioChunks.value[chunkIndex] = audioUrl
      console.log(`📦 存储音频片段 ${chunkIndex + 1}/${totalChunks}`)
      
      // 🎯 立即播放第一个片段
      if (chunkIndex === 0 && audioPlayerRef.value) {
        console.log('🚀 立即播放第一个音频片段')
        playAudioChunkWithQuickTransition(audioUrl, 0, totalChunks)
      }
      
      // ⚡ 检查是否收到最后一个片段，提前准备VAD恢复
      if (isLastChunk) {
        console.log('✅ 收到最后一个音频片段，准备连续播放')
        isWaitingForAudioChunks.value = false
        if (audioChunkTimeout.value) {
          clearTimeout(audioChunkTimeout.value)
          audioChunkTimeout.value = null
        }
        
        // ⚡ 预估最后片段播放时间，提前通知后端准备恢复VAD
        const estimatedDuration = actualData.duration || 3 // 默认3秒
        setTimeout(() => {
          console.log('⚡ 预计音频即将结束，准备恢复VAD监听')
          notifyAudioPlaybackNearEnd()
        }, Math.max(0, (estimatedDuration - 0.5) * 1000)) // 提前0.5秒通知
      }
    } else {
      // 🎯 单个音频直接播放
      console.log('🎵 单个音频直接播放')
      playAudioChunkWithQuickTransition(audioUrl, 0, 1)
    }
    
    // 🎯 只为第一个片段设置事件监听器，避免重复设置
    if (isFirstChunk && !isContinuation) {
      nextTick(() => {
        if (audioPlayerRef.value) {
          // 移除之前的监听器，防止重复绑定
          audioPlayerRef.value.removeEventListener('play', audioPlayHandler)
          audioPlayerRef.value.removeEventListener('ended', audioEndedHandler)
          audioPlayerRef.value.removeEventListener('error', audioErrorHandler)
          audioPlayerRef.value.removeEventListener('canplay', audioCanPlayHandler)
          
          // 重新绑定监听器
          audioPlayerRef.value.addEventListener('play', audioPlayHandler)
          audioPlayerRef.value.addEventListener('ended', audioEndedHandler)
          audioPlayerRef.value.addEventListener('error', audioErrorHandler)
          audioPlayerRef.value.addEventListener('canplay', audioCanPlayHandler) // 添加预加载监听
        }
      })
    }
  }
}

// ⚡ 优化的音频播放方法，减少切换延迟
const playAudioChunkWithQuickTransition = async (audioUrl: string, chunkIndex: number, totalChunks: number) => {
  console.log('🎵 [播放] 音频片段:', `${chunkIndex + 1}/${totalChunks} (${Math.round(audioUrl.length / 1024)}KB)`)
  
  if (!audioPlayerRef.value) {
    console.error('❌ 音频播放器引用不存在')
    return
  }
  
  // 🔧 关键修复：添加音频有效性检查
  if (!audioUrl || audioUrl.trim() === '') {
    console.error('❌ 音频URL无效，跳过播放')
    handleAllAudioComplete()
    return
  }
  
  console.log('🔧 设置音频源...')
  
  // 🔍 验证Base64音频数据质量（简化日志）
  if (audioUrl.startsWith('data:audio/')) {
    const base64Data = audioUrl.split(',')[1]
    if (base64Data) {
      const repetitionRatio = 1 - (new Set(base64Data).size / base64Data.length)
      const isValidWAV = base64Data.startsWith('UklGR')
      
      if (repetitionRatio > 0.8) {
        console.warn('⚠️ 音频数据重复率过高，可能有质量问题')
      }
      if (!isValidWAV) {
        console.warn('⚠️ 音频数据格式异常')
      }
    }
  }
  
  // 🔧 关键修复：在修改src之前先暂停当前播放，避免AbortError
  try {
    if (!audioPlayerRef.value.paused) {
      audioPlayerRef.value.pause()
      // 等待短暂时间确保暂停完成
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  } catch (pauseError) {
    console.warn('⚠️ 暂停当前音频失败:', pauseError)
  }
  
  // 设置音频源
  audioPlayerRef.value.src = audioUrl
  
  // 预加载音频以减少播放延迟
  audioPlayerRef.value.preload = 'auto'
  
  // 🔧 关键修复：在播放前记录开始时间
  audioPlayStartTime.value = Date.now()
  
  // 等待音频加载完成后再播放
  const playPromise = new Promise((resolve, reject) => {
    const handleCanPlay = () => {
      audioPlayerRef.value?.removeEventListener('canplay', handleCanPlay)
      audioPlayerRef.value?.removeEventListener('error', handleError)
      resolve(true)
    }
    
    const handleError = (error: any) => {
      audioPlayerRef.value?.removeEventListener('canplay', handleCanPlay)
      audioPlayerRef.value?.removeEventListener('error', handleError)
      reject(error)
    }
    
    audioPlayerRef.value?.addEventListener('canplay', handleCanPlay)
    audioPlayerRef.value?.addEventListener('error', handleError)
    
    // 开始加载
    audioPlayerRef.value?.load()
  }).then(() => {
    // 加载完成后开始播放
    return audioPlayerRef.value?.play()
  })
  
  playPromise.then(() => {
    console.log(`✅ 音频片段 ${chunkIndex + 1}/${totalChunks} 开始播放`)
    isAudioPlaying.value = true
    
    // 🎤 启动Live2D嘴型同步（使用复用连接）
    if (live2dDisplayRef.value && audioPlayerRef.value && globalAudioManager.value.isInitialized) {
      live2dDisplayRef.value.startTTSMotionSyncWithSharedAnalyzer(
        audioPlayerRef.value,
        globalAudioManager.value.analyser!
      )
        .then(() => {
          console.log('✅ Live2D TTS嘴型同步启动成功 (WebSocket音频-复用连接)')
        })
        .catch((error: any) => {
          console.error('❌ Live2D TTS嘴型同步启动失败:', error)
        })
    }
  }).catch(error => {
    console.error(`❌ 音频片段 ${chunkIndex + 1}/${totalChunks} 播放失败:`, error)
    isAudioPlaying.value = false
    
    // 🔧 关键修复：移除弹窗提示，避免影响用户体验
    console.warn('⚠️ 音频播放失败，等待后端自动恢复VAD')
    // 🔧 移除弹窗: message.warning('音频播放失败，系统将自动恢复')
    
    // 🔧 关键修复：音频播放失败时也需要恢复VAD
    // 如果是多片段音频，尝试播放下一个片段
    if (chunkIndex < totalChunks - 1) {
      console.log('🔄 播放失败，尝试播放下一个音频片段')
      setTimeout(() => playNextAudioChunk(), 100) // 只等待100ms
    } else {
      // 如果是最后一个片段或单个音频播放失败，也需要恢复VAD
      console.log('❌ 最后音频片段播放失败，但仍需要恢复VAD监听')
      handleAllAudioComplete()
    }
  })
}

// ⚡ 新增：预加载监听器，减少播放间隙
const audioCanPlayHandler = () => {
  // 音频预加载完成日志已移除，避免控制台刷屏
}

// 🎯 音频事件处理器（避免重复定义）
const audioPlayHandler = () => {
  // 音频播放开始日志已移除，避免控制台刷屏
  isAudioPlaying.value = true
  // 🔧 关键修复：记录音频播放开始时间
  audioPlayStartTime.value = Date.now()
}

const audioEndedHandler = () => {
  // 音频播放结束日志已移除，避免控制台刷屏
  isAudioPlaying.value = false
  
  // 🔇 停止Live2D嘴型同步
  if (live2dDisplayRef.value) {
    live2dDisplayRef.value.stopTTSMotionSync()
      .then(() => {
        console.log('✅ Live2D TTS嘴型同步停止成功 (WebSocket音频)')
      })
      .catch((error: any) => {
        console.error('❌ Live2D TTS嘴型同步停止失败:', error)
      })
  }
  
  // 🔧 关键修复：检查播放时长，防止误判
  const playDuration = (Date.now() - audioPlayStartTime.value) / 1000
  const minValidDuration = 0.1 // 进一步降低到0.1秒，适应各种音频长度
  
  // 🔧 新增：获取音频实际时长进行对比验证
  const audioDuration = audioPlayerRef.value?.duration || 0
  const playRatio = audioDuration > 0 ? playDuration / audioDuration : 1 // 默认为1，表示正常播放
  
  // 🚀 流式音频检测：对于流式音频采用更宽松的策略
  const isStreamingAudio = audioChunks.value && audioChunks.value.length > 1
  
  console.log(`🔍 播放验证: 播放时长=${playDuration.toFixed(2)}s, 音频时长=${audioDuration.toFixed(2)}s, 播放比例=${(playRatio * 100).toFixed(1)}%, 流式音频=${isStreamingAudio}`)
  
  // 🔧 关键修复：进一步放宽音频播放完成的检测条件
  if (isStreamingAudio) {
    // 流式音频：非常宽松的检测，几乎不拦截
    if (playDuration < 0.02) { // 极短播放时间才认为异常（降低到20ms）
      console.warn(`⚠️ 流式音频片段播放时间极短 (${playDuration.toFixed(3)}秒)，可能异常`)
      console.warn('   但仍允许继续播放下一段，VAD由后端计时器管理')
    }
    console.log(`✅ 流式音频片段播放完成 (${playDuration.toFixed(2)}秒)`)
  } else {
    // 单个音频：也使用相对宽松的检测
    if (playDuration < 0.05) { // 降低最小播放时间要求到50ms
      console.warn(`⚠️ 单个音频播放时间很短 (${playDuration.toFixed(3)}秒)，但仍允许继续`)
      console.warn('   VAD恢复由后端计时器统一管理')
    }
    
    // 🔧 进一步放宽长音频检测条件
    if (audioDuration > 3.0 && playRatio < 0.2) { // 放宽到3秒以上且播放比例低于20%才警告
      console.warn(`⚠️ 长音频播放比例偏低 (${(playRatio * 100).toFixed(1)}%)，但仍允许继续`)
      console.warn('   VAD恢复由后端计时器管理，无需前端干预')
    }
    
    console.log(`✅ 单个音频播放处理完成 (${playDuration.toFixed(2)}秒)`)
  }
  
  // ⚡ 快速检查是否有下一个片段需要播放
  if (audioChunks.value && audioChunks.value.length > 1) {
    // 流式音频，快速切换到下一个片段
    setTimeout(() => playNextAudioChunk(), 50) // 减少延迟到50ms
  } else {
    // 单个音频或最后一个片段完成
    handleAllAudioComplete()
  }
}

const audioErrorHandler = (e: Event) => {
  console.error('🎵 音频加载错误:', e)
  isAudioPlaying.value = false
  
  // 🔇 确保在音频错误时停止嘴型同步
  if (live2dDisplayRef.value) {
    live2dDisplayRef.value.stopTTSMotionSync()
      .catch((error: any) => {
        console.error('❌ 音频错误时停止嘴型同步失败:', error)
      })
  }
  
  // 🔧 关键修复：移除弹窗提示，避免影响用户体验
  console.warn('⚠️ 音频播放错误，等待后端自动恢复VAD')
  // 🔧 移除弹窗: message.warning('音频播放出现问题，系统将自动恢复')
}

// ⚡ 优化的播放下一个音频片段方法
const playNextAudioChunk = () => {
  if (!audioChunks.value || audioChunks.value.length === 0) {
    console.log('🏁 没有更多音频片段')
    handleAllAudioComplete()
    return
  }
  
  currentChunkIndex.value++
  
  if (currentChunkIndex.value < audioChunks.value.length) {
    const nextChunkUrl = audioChunks.value[currentChunkIndex.value]
    if (nextChunkUrl && audioPlayerRef.value) {
      console.log(`🔗 播放下一个音频片段 ${currentChunkIndex.value + 1}/${audioChunks.value.length}`)
      
      // ⚡ 使用优化的播放方法
      playAudioChunkWithQuickTransition(nextChunkUrl, currentChunkIndex.value, audioChunks.value.length)
    } else {
      console.log('⚠️ 下一个片段URL无效，尝试播放后续片段')
      // 快速尝试下一个片段
      setTimeout(() => playNextAudioChunk(), 50)
    }
  } else {
    console.log('🏁 所有音频片段播放完成')
    handleAllAudioComplete()
  }
}

// ⚡ 新增：音频播放完成处理，立即恢复VAD
const handleAllAudioComplete = () => {
  console.log('🎵 音频播放序列完成，等待后端自动恢复VAD监听')
  
  // 🔇 确保停止Live2D嘴型同步
  if (live2dDisplayRef.value) {
    live2dDisplayRef.value.stopTTSMotionSync()
      .then(() => {
        console.log('✅ Live2D TTS嘴型同步已停止 (音频序列完成)')
      })
      .catch((error: any) => {
        console.error('❌ 音频序列完成时停止嘴型同步失败:', error)
      })
  }
  
  // 清理状态
  audioChunks.value = []
  currentChunkIndex.value = 0
  isAudioPlaying.value = false
  
  if (audioChunkTimeout.value) {
    clearTimeout(audioChunkTimeout.value)
    audioChunkTimeout.value = null
  }
  
  // ⚡ 立即通知后端音频播放完成，恢复VAD监听
  console.log('📡 前端检测到音频播放完成，立即请求恢复VAD监听')
  notifyAudioPlaybackComplete()
}

// ⚡ 新增：通知后端音频即将结束
const notifyAudioPlaybackNearEnd = async () => {
  try {
    console.log('📡 通知后端音频即将结束')
    // 这里可以调用API通知后端准备恢复VAD
    // await API.notifyAudioNearEnd()
  } catch (error) {
    console.warn('⚠️ 通知音频即将结束失败:', error)
  }
}

// ⚡ 简化：音频播放完成后立即恢复VAD（回到最初的正确逻辑）
const notifyAudioPlaybackComplete = async () => {
  try {
    console.log('📡 音频播放完成，立即恢复VAD监听')
    
    const result = await API.resumeVADListening()
    
    if (result.success) {
      console.log('✅ VAD恢复请求发送成功')
    } else {
      console.warn('⚠️ VAD恢复请求失败:', result.message)
      // 简单重试一次
      setTimeout(async () => {
        try {
          const retryResult = await API.resumeVADListening()
          console.log('🔄 VAD恢复重试结果:', retryResult.success ? '成功' : '失败')
        } catch (retryError) {
          console.error('❌ VAD恢复重试失败:', retryError)
        }
      }, 500)
    }
    
  } catch (error) {
    console.error('❌ VAD恢复异常:', error)
  }
}

const handleTTSError = (data: any) => {
  console.error('TTS错误:', data.error)
  // 🔧 移除弹窗: message.error(`语音合成失败: ${data.error}`)
}

const handleStatusUpdate = (data: any) => {
  if (data.latency !== undefined) {
    latency.value = data.latency
  }
}

const handleWebSocketError = (error: any) => {
  console.error('WebSocket错误:', error)
  // 🔧 移除弹窗: message.error('连接异常，请检查网络')
}

// === 消息管理 ===
const addRealtimeMessage = (role: 'user' | 'assistant', content: string, confidence?: number, extras?: any) => {
  const message = {
    id: Date.now().toString(),
    role,
    content,
    timestamp: new Date(),
    confidence,
    ...extras
  }
  
  realtimeHistory.value.push(message)
  
  // 同步到store
  realtimeStore.addMessage(message)
  
  // 自动滚动到底部
  nextTick(() => {
    if (conversationContainer.value) {
      conversationContainer.value.scrollTop = conversationContainer.value.scrollHeight
    }
  })
}

// ===================== 生命周期 =====================
onMounted(async () => {
  console.log('🎮 RealtimeView 主协调组件已挂载')
  
  // 强制清空所有数据，确保干净的开始状态
  realtimeStore.forceReset()
  
  // 🔧 新增：强制重置AI模型配置为实时对话默认配置
  console.log('🎯 强制重置AI配置为实时对话默认值...')
  realtimeStore.updateAIConfig({
    provider: 'lmstudio' as const,
    modelName: 'csxl0.6', // 实时对话默认模型
    temperature: 1.0,
    topP: 0.95,
    topK: 64,
    maxTokens: 8000,
    useHistory: true
  })
  console.log('✅ AI配置已重置为实时对话默认值')
  
  // 🔧 修复：将所有阻塞性初始化改为非阻塞，确保页面立即加载
  console.log('✅ RealtimeView 页面已加载，正在后台初始化各项功能...')
  
  // 🔧 添加页面可见性监控，防止僵尸连接
  document.addEventListener('visibilitychange', handleVisibilityChange)
  console.log('📱 [RealtimeView] 页面可见性监听已启用')
  
  // 🎭 优先初始化Live2D模型（同步执行，确保在组件挂载前完成）
  try {
    console.log('🎭 通过Live2D Store设置默认Kei模型...')
    
    // 🔧 **修复：使用Live2D Store的新方法设置默认Kei模型**
    const success = await live2dStore.setDefaultKeiModel()
    
    if (success) {
      console.log('✅ 默认Kei模型已通过Store设置成功')
      
      // 🔧 强制触发响应式更新
      await nextTick()
      console.log('🔄 强制触发响应式更新完成，Live2DStage应该能接收到模型数据')
      
      // 🔧 额外确保组件能接收到更新
      await nextTick()
      const currentModel = live2dStore.selectedModel
      console.log('🔍 二次确认Store模型设置状态:', {
        'live2dStore.selectedModel': currentModel?.displayName || 'null',
        'modelExists': !!currentModel,
        'timestamp': new Date().toLocaleTimeString()
      })
    } else {
      console.log('⚠️ Store默认模型设置失败，使用备用方案...')
      
      // 备用方案：直接使用ModelDatabase
      const { getAvailableModels, initializeModelDatabase } = await import('@/data/ModelDatabase')
      await initializeModelDatabase()
      const availableModels = getAvailableModels()
      
      if (availableModels.length > 0) {
        const defaultModel = availableModels[0]
        live2dStore.selectModel(defaultModel)
        console.log('✅ 备用方案成功，使用第一个可用模型:', defaultModel.displayName)
      }
    }
  } catch (error: any) {
    console.error('❌ 设置默认Live2D模型失败:', error)
    // 不阻塞页面加载，继续执行其他初始化
  }
  
  // 继续后台初始化其他功能
  Promise.resolve().then(async () => {
    try {
      console.log('🔄 后台初始化AI配置...')
      const success = await realtimeStore.initializeAIConfig()
      if (success) {
        console.log('✅ AI配置已从预设文件加载')
      } else {
        console.warn('⚠️ AI配置使用默认值')
      }
    } catch (error: any) {
      console.error('❌ AI配置初始化失败:', error)
      // 不阻塞页面加载
    }
  })
  
  // 🎭 非阻塞加载默认角色
  Promise.resolve().then(async () => {
    try {
      console.log('🎭 后台开始从预设加载默认角色...')
      
      // 添加超时控制
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('角色加载超时')), 8000)
      )
      
      const charactersResult = await Promise.race([
        API.getCharacterList(),
        timeoutPromise
      ]) as any // 临时类型断言，等待后续优化
      
      if (charactersResult.success && charactersResult.data) {
        console.log('📋 获取到角色列表:', charactersResult.data.length)
        
        // 查找"哪托"角色
        const targetCharacterName = '哪托'
        const targetPreset = charactersResult.data.find((char: any) => char.name === targetCharacterName)
        
        if (targetPreset) {
          console.log('✅ 找到预设角色:', targetCharacterName)
          console.log('🎭 角色详细信息:', {
            name: targetPreset.name,
            description: targetPreset.description,
            hasAvatar: !!targetPreset.avatar,
            hasSystemPrompt: !!(targetPreset.systemPrompt),
            hasCharacterCard: !!targetPreset.character_card,
            basicInfo: targetPreset.character_card?.basic_info
          })
          
          // 转换CharacterPreset到CharacterCard格式
          const characterCard = {
            name: targetPreset.name,
            description: targetPreset.description,
            avatar: targetPreset.avatar,
            systemPrompt: targetPreset.systemPrompt,
            character_card: targetPreset.character_card
          }
          
          // 设置默认角色到store
          realtimeStore.setSelectedCharacter(characterCard)
          console.log('🎭 默认角色已设置到store')
        } else {
          console.warn('⚠️ 未找到"哪托"角色，可用角色列表:', charactersResult.data.map((c: any) => c.name))
          
          // 🔧 修复：再次尝试更宽松的匹配
          const alternativeNatoCharacter = charactersResult.data.find((char: any) => 
            char.name.includes('哪') || 
            char.name.includes('托') ||
            char.name.includes('吒') ||
            char.description?.includes('藏识')
          )
          
          if (alternativeNatoCharacter) {
            console.log('🔄 找到可能的哪托角色变体:', alternativeNatoCharacter.name)
            
            const characterCard = {
              name: alternativeNatoCharacter.name,
              description: alternativeNatoCharacter.description,
              avatar: alternativeNatoCharacter.avatar,
              systemPrompt: alternativeNatoCharacter.systemPrompt,
              character_card: alternativeNatoCharacter.character_card
            }
            
            realtimeStore.setSelectedCharacter(characterCard)
          } else if (charactersResult.data.length > 0) {
            // 如果还是找不到，使用第一个角色作为默认值
            const firstPreset = charactersResult.data[0]
            console.log('⚠️ 使用第一个可用角色作为默认值:', firstPreset.name)
            console.log('📋 详细角色列表：')
            charactersResult.data.forEach((char: any, index: number) => {
              console.log(`  ${index + 1}. ${char.name} - ${char.description || '无描述'}`)
            })
            
            // 转换CharacterPreset到CharacterCard格式
            const characterCard = {
              name: firstPreset.name,
              description: firstPreset.description,
              avatar: firstPreset.avatar,
              systemPrompt: firstPreset.systemPrompt,
              character_card: firstPreset.character_card
            }
            
            realtimeStore.setSelectedCharacter(characterCard)
          } else {
            throw new Error('没有可用的角色预设')
          }
        }
      } else {
        throw new Error(charactersResult.message || '获取角色列表失败')
      }
    } catch (error: any) {
      console.error('❌ 从预设加载默认角色失败:', error)
      console.warn('⚠️ 将使用基础默认值')
      
      // 如果预设加载失败，使用最基础的默认值（但不硬编码详细信息）
      const fallbackCharacter = {
        name: '默认助手',
        description: '友善的AI助手',
        systemPrompt: '你是一个友善的AI助手，请用自然、亲切的语调回答用户的问题。',
        character_card: {
          basic_info: {
            gender: '中性',
            age: '未知',
            occupation: 'AI助手'
          }
        }
      }
      
      realtimeStore.setSelectedCharacter(fallbackCharacter)
      console.log('🔄 使用fallback角色:', fallbackCharacter.name)
    }
  })
  
  // 🗣️ 立即设置默认语音配置（不需要等待API）
  const defaultVoice = '哪托'
  realtimeStore.setSelectedVoice(defaultVoice)
  console.log('🗣️ 设置默认语音:', defaultVoice)
  
  // 🎵 立即设置默认语音配置
  realtimeStore.updateVoiceSettings({
    selectedVoice: '哪托',
    speed: 1.0,
    volume: 1.0,
    quality: 'high',
    synthesisMode: 'user-voice',
    selectedUserVoice: '哪托',
    selectedProjectVoice: ''
  })
  console.log('🎵 设置默认语音为哪托')
  
  // 🎵 非阻塞加载用户语音配置
  Promise.resolve().then(async () => {
    try {
      console.log('🔄 后台加载用户语音配置...')
      await loadUserVoiceProfiles()
      console.log('✅ 用户语音配置加载完成')
      
      // 🎵 在加载完成后，查找哪托音色并设置为默认
      if (userVoiceProfiles.value.length > 0) {
        const natuoVoice = userVoiceProfiles.value.find(profile => 
          profile.name.includes('哪托') || profile.name.includes('哪吒')
        )
        
        if (natuoVoice) {
          selectedUserVoice.value = natuoVoice.id
          realtimeStore.updateVoiceSettings({
            ...realtimeStore.voiceSettings,
            selectedUserVoice: natuoVoice.id
          })
          console.log('🎵 找到哪托音色，设置为默认:', natuoVoice.name)
        } else {
          // 如果没有哪托音色，作为备选优先选择纳西妲音色（ID 52）
          const nazidaVoice = userVoiceProfiles.value.find(profile => profile.id === '52')
          if (nazidaVoice) {
            selectedUserVoice.value = nazidaVoice.id
            realtimeStore.updateVoiceSettings({
              ...realtimeStore.voiceSettings,
              selectedUserVoice: nazidaVoice.id
            })
            console.log('🎵 未找到哪托音色，设置默认音色为纳西妲 (ID: 52)')
          } else {
            // 否则选择第一个可用的音色
            selectedUserVoice.value = userVoiceProfiles.value[0].id
            realtimeStore.updateVoiceSettings({
              ...realtimeStore.voiceSettings,
              selectedUserVoice: userVoiceProfiles.value[0].id
            })
            console.log('🎵 设置默认音色为第一个可用音色:', userVoiceProfiles.value[0].name)
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ 加载用户语音配置失败:', error)
      // 🔧 移除弹窗：不显示错误提示，避免影响初始化体验
    }
  })
  
  // 🤖 非阻塞加载LLM模型列表
  Promise.resolve().then(async () => {
    try {
      console.log('🔄 后台加载LLM模型列表...')
      await loadLLMModels()
      console.log('✅ LLM模型列表加载完成')
      
      // 同步模型列表到store
      realtimeStore.setAvailableModels(availableLLMModels.value)
      
      // 🔧 强制确保实时对话使用正确的默认模型
      const finalModel = currentLLMModel.value || 'csxl0.6'
      console.log('🎯 最终确认实时对话模型:', finalModel)
      
      // 确保store中的配置与当前模型一致
      realtimeStore.updateAIConfig({
        ...realtimeStore.aiConfig,
        modelName: finalModel
      })
      
      console.log('🤖 当前激活的LLM模型:', currentLLMModel.value)
    } catch (error) {
      console.warn('⚠️ 加载LLM模型列表失败:', error)
      // 🔧 修复：设置fallback值，但不强制覆盖loadLLMModels的结果
      if (!currentLLMModel.value) {
        currentLLMModel.value = 'LMstudio: csxl0.6'
        realtimeStore.updateAIConfig({
          ...realtimeStore.aiConfig,
          modelName: 'LMstudio: csxl0.6'
        })
        console.log('🤖 使用fallback LLM模型: LMstudio: csxl0.6')
      }
    }
  })
  
  console.log('✅ RealtimeView 页面加载完成，各项功能正在后台初始化：')
  console.log('   - 页面立即可用，不会被阻塞')
  console.log('   - AI配置、角色、语音等在后台加载')
  console.log('   - 初始化失败不影响页面使用')
  
  // 🎯 新需求：自动启动实时对话
  console.log('🚀 自动启动实时对话功能...')
  
  // 等待一段时间确保基础配置加载完成
  setTimeout(async () => {
    try {
      console.log('🔄 开始自动启动实时对话...')
      await startRealtimeDialogue()
      console.log('✅ 自动启动实时对话成功')
    } catch (error) {
      console.error('❌ 自动启动实时对话失败:', error)
      // 不显示错误提示，让用户手动启动
    }
  }, 3000) // 等待3秒后自动启动
})

// === Watch监听器 ===
// 监听用户音色选择变化，自动切换模式
watch(selectedUserVoice, (newVoice, oldVoice) => {
  console.log('🎵 用户音色选择变化:', { from: oldVoice, to: newVoice })
  
  if (newVoice && newVoice !== oldVoice) {
    // 用户选择了音色，自动切换到用户音色模式
    if (synthesisMode.value !== 'user-voice') {
      console.log('🔄 自动切换到用户音色模式')
      synthesisMode.value = 'user-voice'
      // 🔧 移除弹窗: message.info(`已切换到用户音色模式`)
    }
    
    // 如果对话正在进行，实时更新音色
    if (realtimeStatus.value === 'active') {
      updateRealtimeTTSConfig({
        userVoiceProfile: newVoice,
        mode: 'user-voice'
      })
    }
  }
})

// 监听合成模式变化
watch(synthesisMode, (newMode, oldMode) => {
  console.log('🔄 合成模式切换:', { from: oldMode, to: newMode })
  
  if (newMode === 'audio-path' && oldMode === 'user-voice') {
    // 切换到音频路径模式时，如果没有选择项目音色，设置默认值
    if (!selectedProjectVoice.value) {
      selectedProjectVoice.value = 'default_female'
    }
  }
  
  // 如果对话正在进行，实时更新模式
  if (realtimeStatus.value === 'active') {
    updateRealtimeTTSConfig({
      mode: newMode,
      userVoiceProfile: selectedUserVoice.value,
      selectedVoice: selectedProjectVoice.value
    })
  }
})

// 🔧 添加页面可见性监控，防止僵尸连接
const handleVisibilityChange = () => {
  if (document.hidden) {
    console.log('📱 [RealtimeView] 页面已隐藏')
    // 页面隐藏时不断开连接，但记录状态
  } else {
    console.log('📱 [RealtimeView] 页面已显示')
    // 页面显示时检查连接状态
    if (isConversationActive.value && !websocket.isConnected.value) {
      console.log('🔄 [RealtimeView] 页面恢复显示，检测到连接断开，尝试重连...')
      websocket.activate() // 激活连接，如果断开会自动重连
    }
  }
}

// 页面可见性监听将在主onMounted中添加

// 🎭 监听Live2D模型变化 - 用于调试
watch(selectedLive2DModel, (newModel, oldModel) => {
  console.log('🎭 RealtimeView中selectedLive2DModel变化:', {
    newModel: newModel ? {
      id: newModel.id,
      displayName: newModel.displayName,
      status: newModel.status
    } : null,
    oldModel: oldModel ? {
      id: oldModel.id,
      displayName: oldModel.displayName,
      status: oldModel.status
    } : null,
    hasChanged: newModel !== oldModel
  })
  
  if (newModel) {
    console.log('🎭 RealtimeView: 新的Live2D模型已设置，应该会传递给Live2DStage组件')
  } else {
    console.log('🎭 RealtimeView: Live2D模型被清空或初始化为null')
  }
}, { immediate: true })

onUnmounted(async () => {
  console.log('🎮 RealtimeView 主协调组件开始卸载')
  
  // 🔧 先标记页面为非活跃状态，停止所有自动重连
  isConversationActive.value = false
  
  // 🔧 移除页面可见性监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  console.log('📱 [RealtimeView] 页面可见性监听已移除')
  
  // 🔧 修复：恢复健康检查（以防对话进行中用户直接离开页面）
  try {
    websocket.manager.resumeHealthCheck()
    console.log('▶️ [RealtimeView] 页面卸载时恢复健康检查')
  } catch (error) {
    console.warn('⚠️ [RealtimeView] 恢复健康检查失败:', error)
  }
  
  // 清理资源
  if (realtimeStatus.value === 'active') {
    console.log('🛑 [RealtimeView] 页面卸载时检测到活跃对话，执行停止...')
    await stopRealtimeDialogue()
  }
  
  // 清理定时器
  stopConversationTimer()
  
  // 🎵 清理流式音频状态
  if (audioChunkTimeout.value) {
    clearTimeout(audioChunkTimeout.value)
    audioChunkTimeout.value = null
  }
  audioChunks.value = []
  currentChunkIndex.value = 0
  isWaitingForAudioChunks.value = false
  isAudioPlaying.value = false
  lastPlayedAudioUrls.value = []
  
  // 🎯 清理全局音频分析器
  await cleanupGlobalAudioAnalyzer()
  
  // 🔧 生产级页面卸载：只取消当前页面的WebSocket事件订阅
  console.log('🔌 [RealtimeView] 页面卸载，取消WebSocket事件订阅')
  try {
    websocket.disconnect() // 只取消当前页面的订阅，不断开全局连接
    console.log('✅ [RealtimeView] WebSocket事件订阅已清理')
  } catch (error) {
    console.error('❌ [RealtimeView] WebSocket清理失败:', error)
  }
  
  console.log('✅ [RealtimeView] 页面清理完成')
})

// === LLM模型管理 ===
const loadLLMModels = async () => {
  try {
    isLoadingLLMModels.value = true
    console.log('🔄 开始加载 LLM 模型列表...')
    
    // 🔧 修复：首先获取当前模型状态
    let backendCurrentModel = null
    try {
      console.log('1️⃣ 获取后端当前模型状态...')
      const currentModelResult = await API.getCurrentModel()
      if (currentModelResult.success && currentModelResult.data) {
        backendCurrentModel = currentModelResult.data.model
        console.log('✅ 后端当前模型:', backendCurrentModel)
      }
    } catch (error) {
      console.warn('⚠️ 获取后端当前模型失败:', error)
    }
    
    // 🔧 修复：然后获取可用模型列表
    console.log('2️⃣ 获取可用模型列表...')
    const result = await API.getAvailableModels()
    console.log('📡 API 响应:', result)
    
    if (result.success && result.data) {
      console.log('✅ API 调用成功，返回数据:', result.data)
      availableLLMModels.value = result.data.models || []
      
      // 🔧 关键修复：强制使用实时对话的默认模型
      let targetModel = null
      
      // 优先选择实时对话默认的LMstudio csxl0.6模型，兼容gemma模型
      const realtimeDefaultModel = availableLLMModels.value.find(model => 
        model === 'LMstudio: csxl0.6' ||
        model === 'csxl0.6' ||
        (model.toLowerCase().includes('lmstudio') && 
         model.toLowerCase().includes('gemma') && 
         model.toLowerCase().includes('4b'))
      )
      
      if (realtimeDefaultModel) {
        targetModel = realtimeDefaultModel
        console.log('🎯 RealtimeView强制使用默认模型:', targetModel)
      } else if (availableLLMModels.value.length > 0) {
        // 如果没有找到默认模型，选择第一个可用模型
        targetModel = availableLLMModels.value[0]
        console.log('🎯 未找到默认模型，选择第一个可用模型:', targetModel)
      }
      
      // 🔧 修复：如果前端当前模型与目标模型不一致，执行同步
      if (targetModel !== currentLLMModel.value) {
        console.log('🔄 检测到模型不一致，执行同步:', {
          frontend: currentLLMModel.value,
          backend: targetModel
        })
        
        currentLLMModel.value = targetModel
        
        // 🔧 更新store中的AI配置
        realtimeStore.updateAIConfig({
          ...aiConfig.value,
          modelName: targetModel || undefined
        })
        
        // 🔧 如果目标模型与后端不一致，执行切换
        if (targetModel !== backendCurrentModel) {
          console.log('🔄 后端模型需要同步，执行切换API调用...')
          try {
            await handleLLMModelChange(targetModel, true) // silent模式
          } catch (error) {
            console.warn('⚠️ 后端模型同步失败:', error)
          }
        }
      } else {
        console.log('✅ 前后端模型已同步:', currentLLMModel.value)
      }
      
      console.log(`📊 加载了 ${availableLLMModels.value.length} 个模型，当前模型: ${currentLLMModel.value}`)
      // 🔧 移除弹窗: message.success(`LLM模型列表已更新 (${availableLLMModels.value.length} 个模型)`)
    } else {
      console.warn('⚠️ API 调用失败或无数据:', result.message)
      // 🔧 移除弹窗: message.warning(result.message || 'API服务异常，无法获取模型列表')
      availableLLMModels.value = []
      currentLLMModel.value = null
    }
  } catch (error: any) {
    console.error('❌ 加载LLM模型列表失败:', error)
    // 🔧 移除弹窗: message.error(`加载LLM模型列表失败: ${error.message}`)
    availableLLMModels.value = []
    currentLLMModel.value = null
  } finally {
    isLoadingLLMModels.value = false
  }
}

const handleLLMModelChange = async (newModel: string | null, silent: boolean = false) => {
  console.log('🔄 处理模型切换:', { newModel, currentModel: currentLLMModel.value, silent })
  
  if (!newModel || newModel === currentLLMModel.value && !silent) {
    console.log('⏭️ 跳过切换: 模型相同或为空')
    return
  }
  
  if (isSwitchingLLMModel.value) {
    console.log('⚠️ 正在切换中，跳过重复请求')
    // 🔧 移除弹窗: message.warning('正在切换LLM模型，请稍候...')
    return
  }

  try {
    isSwitchingLLMModel.value = true
    console.log(`🎯 开始切换到模型: ${newModel}`)
    currentLLMModel.value = newModel // 立即更新UI显示
    
    const result = await API.switchChatModel(newModel)
    console.log('📡 切换模型 API 响应:', result)
    
    if (result.success) {
      if (!silent) console.log(`LLM模型已切换至: ${newModel}`)
      currentLLMModel.value = newModel
      
      // 更新aiConfig中的模型名称
      realtimeStore.updateAIConfig({
        ...aiConfig.value,
        modelName: newModel
      })
      
      console.log('✅ 模型切换成功')
    } else {
      console.error('❌ 模型切换失败:', result.message)
      // 🔧 移除弹窗: message.error(result.message || `切换LLM模型 ${newModel} 失败`)
      // 切换失败，重新加载以同步状态
      await loadLLMModels()
    }
  } catch (error: any) {
    console.error('❌ 切换LLM模型失败:', error)
    // 🔧 移除弹窗: message.error(`切换LLM模型失败: ${error.message}`)
    await loadLLMModels()
  } finally {
    isSwitchingLLMModel.value = false
    console.log('🏁 模型切换处理完成，当前模型:', currentLLMModel.value)
  }
}

// === 实时配置更新方法 ===
const updateRealtimeAIConfig = async (config: any) => {
  if (realtimeStatus.value !== 'active') {
    console.log('⚠️ 对话未激活，跳过AI配置更新')
    return
  }
  
  try {
    console.log('🔧 更新实时AI配置:', config)
    // TODO: 实现API.updateRealtimeAIConfig方法
    // const result = await API.updateRealtimeAIConfig(config)
    console.log('✅ 实时AI配置更新成功 (模拟)')
    // if (result.success) {
    //   console.log('✅ 实时AI配置更新成功')
    // } else {
    //   console.warn('⚠️ 实时AI配置更新失败:', result.message)
    // }
  } catch (error: any) {
    console.error('❌ 实时AI配置更新异常:', error)
  }
}

const updateRealtimeTTSConfig = async (config: any) => {
  if (realtimeStatus.value !== 'active') {
    console.log('⚠️ 对话未激活，跳过TTS配置更新')
    return
  }
  
  try {
    console.log('🔧 更新实时TTS配置:', config)
    
    // 构建完整的TTS配置
    const ttsConfig = {
      mode: config.mode || synthesisMode.value,
      userVoiceProfile: config.userVoiceProfile || selectedUserVoice.value,
      speed: config.speed || speed.value,
      volume: config.volume || volume.value,
      audioQuality: config.audioQuality || audioQuality.value,
      selectedVoice: config.selectedVoice || selectedProjectVoice.value
    }
    
    console.log('📡 发送TTS配置更新请求:', ttsConfig)
    
    // 调用真实的API更新TTS配置
    const result = await API.updateRealtimeTTSConfig(ttsConfig)
    
    if (result.success) {
      console.log('✅ 实时TTS配置更新成功')
      // 🔧 移除弹窗: message.success('音色配置已更新')
    } else {
      console.warn('⚠️ 实时TTS配置更新失败:', result.message)
      // 🔧 移除弹窗: message.warning(`音色配置更新失败: ${result.message}`)
    }
  } catch (error: any) {
    console.error('❌ 实时TTS配置更新异常:', error)
    // 🔧 移除弹窗: message.error('音色配置更新异常')
  }
}

// === 头像和角色管理方法 ===
const getMessageAvatarEmoji = (message: any): string => {
  // 如果有角色名称和头像信息，优先使用
  if (message.character_avatar) {
    return message.character_avatar
  }
  
  // 如果有角色名称，尝试从当前选中角色获取头像
  if (message.character_name && selectedCharacterCard.value) {
    return getCharacterAvatarEmoji(selectedCharacterCard.value)
  }
  
  // 如果有选中的角色卡，使用角色卡的头像
  if (selectedCharacterCard.value) {
    return getCharacterAvatarEmoji(selectedCharacterCard.value)
  }
  
  // 默认AI头像
  return '🤖'
}

const getCharacterAvatarEmoji = (character: any): string => {
  if (character.avatar) {
    return character.avatar
  }
  
  // 根据角色名称返回默认头像
  const name = character.name?.toLowerCase() || ''
  if (name.includes('助手') || name.includes('assistant')) return '🤖'
  if (name.includes('老师') || name.includes('teacher')) return '👨‍🏫'
  if (name.includes('医生') || name.includes('doctor')) return '👩‍⚕️'
  if (name.includes('程序员') || name.includes('developer')) return '👨‍💻'
  
  return '🎭' // 默认角色头像
}

// 加载用户语音配置
const loadUserVoiceProfiles = async () => {
  try {
    const result = await voiceProfileAPI.getVoiceProfiles()
    if (result.success) {
      userVoiceProfiles.value = result.data || []
      
      // 如果当前选中的音色不存在，清空选择
      if (selectedUserVoice.value && !userVoiceProfiles.value.find(p => p.id === selectedUserVoice.value)) {
        selectedUserVoice.value = ''
      }
    }
  } catch (error: any) {
    console.warn('加载用户音色配置失败:', error)
  }
}

// === 实时对话辅助方法 ===

/**
 * 监听selectedLive2DModel变化（调试用）
 */
watch(selectedLive2DModel, (newModel, oldModel) => {
  console.log('🎭 RealtimeView中selectedLive2DModel变化:', {
    newModel: newModel,
    oldModel: oldModel,
    timestamp: new Date().toLocaleTimeString()
  })
  console.log('🎭 RealtimeView: 新的Live2D模型已设置，应该会传递给Live2DStage组件')
}, { deep: true, immediate: true })

// ==================== LLM模型管理 ====================

// ==================== 页面生命周期处理 ====================
import { onUnmounted, onBeforeUnmount } from 'vue'

// 🔧 关键：页面卸载时清理实时对话状态，防止与其他页面冲突
onBeforeUnmount(async () => {
  if (import.meta.env.DEV) {
    console.log('🧹 [RealtimeView] 页面即将卸载，开始清理...')
  }
  
  // 🔧 关键修复：检查实时对话状态，决定是否清理连接
  const isActiveConversation = isConversationActive.value || realtimeStatus.value === 'active'
  
  if (isActiveConversation) {
    if (import.meta.env.DEV) {
      console.log('🔄 [RealtimeView] 实时对话进行中，跳过所有清理操作以保持连接稳定')
    }
    return // 直接返回，不做任何清理
  }
  
  // 只在没有活跃对话时才清理
  try {
    disconnectRealtimeWebSocket()
    if (import.meta.env.DEV) {
      console.log('✅ [RealtimeView] WebSocket订阅已清理')
    }
  } catch (error) {
    console.error('❌ [RealtimeView] 清理WebSocket订阅失败:', error)
  }
  
  if (import.meta.env.DEV) {
    console.log('✅ [RealtimeView] 页面清理完成')
  }
})

onUnmounted(() => {
  if (import.meta.env.DEV) {
    console.log('🧹 RealtimeView组件已卸载')
  }
  
  // 最后的清理工作
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
  
  // 清理状态
  realtimeStatus.value = 'inactive'
  connectionStatus.value = 'disconnected'
  isConversationActive.value = false
  isTranscribing.value = false
  isThinking.value = false
})
</script>

<style scoped>
/* ===================== CSS变量系统 ===================== */
.aaa-realtime-view {
  --primary-color: #00d4aa;
  --secondary-color: #667eea;
  --accent-color: #764ba2;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --success-color: #10b981;
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --border-color: #475569;
}

/* ===================== 基础布局 ===================== */
.aaa-realtime-view {
  position: relative;
  width: 100%;
  min-height: 94vh; /* 缩小Live2D展示区域四分之一 */
  height: auto; /* 支持长屏设计 */
  overflow-x: hidden; /* 只允许垂直滚动 */
  overflow-y: auto;
  background: transparent; /* 🎯 透明背景，使用App.vue的游戏风格背景 */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text-primary);
}

/* 🎯 背景系统现在由App.vue统一管理，不再需要重复的样式 */

/* ===================== 主布局网格系统 ===================== */
  .main-layout-grid {
  position: relative;
  z-index: 2;
  display: grid;
  /* 改为大屏Live2D + 下方配置区域的长屏设计 */
  grid-template-rows: minmax(800px, 1.25fr) auto; /* Live2D区域增加到800px最小，支持125vh */
  grid-template-columns: 40% 60%;
  grid-template-areas:
    "stage stage"
    "config conversation";
  gap: 1rem;
  padding: 1rem;
  transition: all 0.3s ease;
  min-height: calc(94vh - 80px); /* 缩小配置面板区域四分之一 */
}

.aaa-realtime-view.fullscreen-mode .main-layout-grid {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  grid-template-areas: "stage";
  padding: 0;
  gap: 0;
}

/* ===================== Live2D舞台区域（大屏设计） ===================== */
.live2d-stage-section {
  grid-area: stage;
  position: relative;
  min-height: 600px; /* 缩小最小高度四分之一 */
  height: 94vh; /* 缩小高度为94vh */
  max-height: 1050px; /* 对应缩小最大高度限制 */
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
  border-radius: 24px;
  border: 2px solid rgba(0, 212, 170, 0.3);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.live2d-stage-section:hover {
  border-color: rgba(0, 212, 170, 0.6);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.6),
    0 0 30px rgba(0, 212, 170, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.live2d-stage-section.stage-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
  grid-area: unset;
}

/* 🌀 炫酷的漩涡全屏切换按钮 */
.vortex-fullscreen-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  width: 60px;
  height: 60px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  outline: none;
  display: flex;
}

.vortex-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vortex-spiral {
  position: absolute;
  width: 50px;
  height: 50px;
  border: 2px solid transparent;
  border-top: 2px solid var(--primary-color);
  border-right: 2px solid var(--secondary-color);
  border-radius: 50%;
  animation: vortexSpin 1.5s linear infinite;
}

.vortex-spiral::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 1px solid transparent;
  border-top: 1px solid rgba(0, 212, 170, 0.5);
  border-right: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 50%;
  animation: vortexSpin 2s linear infinite reverse;
}

.vortex-glow {
  position: absolute;
  width: 60px;
  height: 60px;
  background: radial-gradient(
    circle,
    rgba(0, 212, 170, 0.3) 0%,
    rgba(102, 126, 234, 0.2) 50%,
    transparent 70%
  );
  border-radius: 50%;
  animation: vortexPulse 2s ease-in-out infinite;
}

.vortex-center {
  position: relative;
  z-index: 10;
  width: 30px;
  height: 30px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 16px;
  border: 1px solid rgba(0, 212, 170, 0.6);
  box-shadow: 
    0 0 10px rgba(0, 212, 170, 0.4),
    inset 0 0 10px rgba(0, 212, 170, 0.1);
  transition: all 0.3s ease;
}

.vortex-fullscreen-btn:hover .vortex-center {
  background: rgba(0, 212, 170, 0.2);
  color: #ffffff;
  transform: scale(1.1);
  box-shadow: 
    0 0 20px rgba(0, 212, 170, 0.6),
    inset 0 0 15px rgba(0, 212, 170, 0.2);
}

.vortex-fullscreen-btn:hover .vortex-spiral {
  border-top-color: #ffffff;
  border-right-color: #00f5cc;
  animation-duration: 1s;
}

.vortex-fullscreen-btn:hover .vortex-glow {
  background: radial-gradient(
    circle,
    rgba(0, 212, 170, 0.5) 0%,
    rgba(102, 126, 234, 0.3) 50%,
    transparent 70%
  );
}

/* 全屏模式下的按钮样式 */
.aaa-realtime-view.fullscreen-mode .vortex-fullscreen-btn {
  position: fixed;
  top: 30px;
  right: 30px;
  z-index: 10000;
}

.aaa-realtime-view.fullscreen-mode .vortex-fullscreen-btn .vortex-center {
  background: rgba(0, 0, 0, 0.95);
  border-color: rgba(0, 212, 170, 0.8);
}

/* 浮动角色控制按钮组 - 相对于舞台区域定位 */
.floating-avatar-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 100; /* 提高层级确保在Live2D组件之上 */
}

.avatar-btn {
  position: relative;
  width: 44px;
  height: 44px;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(0, 212, 170, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.avatar-btn:hover {
  background: rgba(0, 212, 170, 0.2);
  border-color: var(--primary-color);
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(0, 212, 170, 0.4);
}

.avatar-glow-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), transparent, var(--primary-color));
  opacity: 0;
  animation: pulse 2s infinite;
}

.avatar-btn:hover .avatar-glow-ring {
  opacity: 0.6;
}

/* 舞台容器 - 扩展高度以适配Live2D组件 */
.stage-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 94vh; /* 缩小高度四分之一 */
  min-height: 600px; /* 缩小最小高度四分之一 */
  max-height: 1050px; /* 缩小最大高度四分之一 */
  display: flex;
  flex-direction: column;
  padding: 0; /* 移除所有内边距 */
  z-index: 2;
  overflow: hidden; /* 防止内容溢出 */
}

/* Live2D角色视窗 - 扩展高度以适配Live2D组件 */
.live2d-character-viewport {
  flex: 1;
  width: 100%;
  height: 94vh; /* 缩小高度四分之一 */
  min-height: 600px; /* 缩小最小高度四分之一 */
  max-height: 1050px; /* 缩小最大高度四分之一 */
  position: relative;
  z-index: 3;
  overflow: hidden; /* 防止溢出 */
}

.character-stage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; /* 完全填充宽度 */
  height: 100%; /* 完全填充高度 */
}

.live2d-model-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Live2D Canvas */
.live2d-canvas {
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
  transition: transform 0.3s ease;
  object-fit: contain;
}

.live2d-canvas:hover {
  transform: scale(1.02);
}

/* Live2D加载和错误状态 */
.live2d-loading,
.live2d-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

.loading-text,
.error-text {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.loading-progress {
  width: 200px;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transition: width 0.3s ease;
  animation: progressGlow 2s infinite;
}

.error-icon {
  font-size: 48px;
  color: var(--danger-color);
  margin-bottom: 15px;
}

.error-detail {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 15px;
}

.retry-btn,
.load-live2d-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover,
.load-live2d-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);
}

/* 角色占位符 */
.character-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.placeholder-avatar {
  margin-bottom: 20px;
}

.avatar-glow {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  animation: pulse 2s infinite;
}

.placeholder-text h3 {
  color: var(--text-primary);
  font-size: 18px;
  margin-bottom: 10px;
}

.placeholder-text p {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}

/* 对话气泡叠加层 */
.speech-bubble-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none;
}

.speech-bubble {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 15px 20px;
  max-width: 80%;
  animation: bubbleSlideIn 0.3s ease-out;
}

.user-speech {
  align-self: flex-end;
  background: rgba(0, 212, 170, 0.2);
  border: 1px solid rgba(0, 212, 170, 0.3);
}

.ai-thinking {
  align-self: flex-start;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.bubble-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.speech-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.speech-text {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

.thinking-animation {
  display: flex;
  align-items: center;
  gap: 10px;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dots span {
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: thinkingPulse 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

/* ===================== 响应式断点 ===================== */
@media (max-width: 1200px) {
  .main-layout-grid {
    grid-template-columns: 45% 55%;
  }
}

@media (max-width: 768px) {
  .main-layout-grid {
    grid-template-rows: minmax(300px, auto) auto auto;
    grid-template-columns: 1fr;
    grid-template-areas:
      "stage"
      "config"
      "conversation";
    gap: 0.5rem;
    padding: 0.5rem;
  }
}

/* 🎯 背景动画现在由App.vue统一管理 */

@keyframes bubbleSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes progressGlow {
  0%, 100% { 
    box-shadow: 0 0 10px rgba(0, 255, 200, 0.3);
  }
  50% { 
    box-shadow: 0 0 20px rgba(0, 255, 200, 0.6);
  }
}

@keyframes thinkingPulse {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* layerFadeIn 动画已移至App.vue */

/* ===================== 漩涡动画和其他动画 ===================== */
@keyframes vortexSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes vortexPulse {
  0%, 100% { 
    opacity: 0.8;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.1);
  }
}

/* ===================== 配置面板区域 ===================== */
.configuration-panel-section {
  grid-area: config;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.config-container {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(0, 212, 170, 0.2);
  overflow: hidden;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  background: rgba(0, 212, 170, 0.05);
  border-bottom: 1px solid rgba(0, 212, 170, 0.2);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #00d4aa;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.title-icon {
  font-size: 20px;
}

.accordion-panels {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 212, 170, 0.3) transparent;
}

.accordion-panels::-webkit-scrollbar {
  width: 6px;
}

.accordion-panels::-webkit-scrollbar-track {
  background: transparent;
}

.accordion-panels::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 170, 0.3);
  border-radius: 3px;
}

.accordion-panels::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 170, 0.5);
}

.config-panel {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 170, 0.15);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
}

.config-panel:hover {
  border-color: rgba(0, 212, 170, 0.3);
  background: rgba(0, 0, 0, 0.25);
}

.config-panel.expanded {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 212, 170, 0.4);
  box-shadow: 0 4px 16px rgba(0, 212, 170, 0.1);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.1);
  user-select: none;
}

.panel-header:hover {
  background: rgba(0, 212, 170, 0.05);
}

.config-panel.expanded .panel-header {
  background: rgba(0, 212, 170, 0.08);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-icon {
  font-size: 20px;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.panel-header h4 {
  color: #e2e8f0;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  transition: color 0.3s ease;
}

.config-panel.expanded .panel-header h4 {
  color: #00d4aa;
}

.collapse-indicator {
  color: #64748b;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: rotate(0deg);
  width: 20px;
  text-align: center;
}

.collapse-indicator.expanded {
  transform: rotate(180deg);
  color: #00d4aa;
}

.panel-body {
  max-height: 400px;
  overflow-y: auto;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

.panel-body.collapsed {
  max-height: 0;
  overflow: hidden;
    opacity: 0;
  padding: 0 20px;
}

.panel-body:not(.collapsed) {
  padding: 20px;
  border-top: 1px solid rgba(0, 212, 170, 0.1);
}

/* ===================== 对话记录区域 ===================== */
.conversation-panel-section {
  grid-area: conversation;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.conversation-container {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.conversation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: rgba(255, 255, 255, 0.2);
}

.conversation-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-count {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.conversation-status-bar {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  color: var(--primary-color);
  font-size: 14px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.status-label {
  color: var(--text-secondary);
  font-size: 11px;
  font-weight: 500;
}

.status-value {
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 600;
}

.conversation-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.empty-conversation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: var(--text-secondary);
  margin-bottom: 15px;
}

.empty-conversation p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-text {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.3;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-time {
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 400;
}

.message-confidence {
  color: var(--success-color);
  font-size: 12px;
  font-weight: 500;
}

.conversation-actions {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.conversation-controls {
  padding: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.main-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn.active {
  background: linear-gradient(135deg, var(--danger-color), #f97316);
}

.control-btn.muted {
  background: linear-gradient(135deg, var(--warning-color), #f59e0b);
}

.realtime-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.transcribing-status,
.thinking-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 13px;
}

.status-icon {
  color: var(--primary-color);
  font-size: 14px;
}
</style> 