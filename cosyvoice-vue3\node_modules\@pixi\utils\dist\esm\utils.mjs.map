{"version": 3, "file": "utils.mjs", "sources": ["../../src/url.ts", "../../src/path.ts", "../../src/settings.ts", "../../src/browser/hello.ts", "../../src/browser/isWebGLSupported.ts", "../../src/color/hex.ts", "../../src/color/premultiply.ts", "../../src/data/createIndicesForQuads.ts", "../../src/data/getBufferType.ts", "../../src/data/interleaveTypedArrays.ts", "../../src/data/pow2.ts", "../../src/data/removeItems.ts", "../../src/data/sign.ts", "../../src/data/uid.ts", "../../src/logging/deprecation.ts", "../../src/media/caches.ts", "../../src/media/CanvasRenderTarget.ts", "../../src/media/trimCanvas.ts", "../../src/const.ts", "../../src/network/decomposeDataUri.ts", "../../src/network/determineCrossOrigin.ts", "../../src/network/getResolutionOfUrl.ts"], "sourcesContent": ["/**\n * This file contains redeclared types for Node `url` and `querystring` modules. These modules\n * don't provide their own typings but instead are a part of the full Node typings. The purpose of\n * this file is to redeclare the required types to avoid having the whole Node types as a\n * dependency.\n */\n\nimport { parse as _parse, format as _format, resolve as _resolve } from 'url';\n\ninterface ParsedUrlQuery\n{\n    [key: string]: string | string[];\n}\n\ninterface ParsedUrlQueryInput\n{\n    [key: string]: unknown;\n}\n\ninterface UrlObjectCommon\n{\n    auth?: string;\n    hash?: string;\n    host?: string;\n    hostname?: string;\n    href?: string;\n    path?: string;\n    pathname?: string;\n    protocol?: string;\n    search?: string;\n    slashes?: boolean;\n}\n\n// Input to `url.format`\ninterface UrlObject extends UrlObjectCommon\n{\n    port?: string | number;\n    query?: string | null | ParsedUrlQueryInput;\n}\n\n// Output of `url.parse`\ninterface Url extends UrlObjectCommon\n{\n    port?: string;\n    query?: string | null | ParsedUrlQuery;\n}\n\ninterface UrlWithParsedQuery extends Url\n{\n    query: ParsedUrlQuery;\n}\n\ninterface UrlWithStringQuery extends Url\n{\n    query: string | null;\n}\n\ninterface URLFormatOptions\n{\n    auth?: boolean;\n    fragment?: boolean;\n    search?: boolean;\n    unicode?: boolean;\n}\n\ntype ParseFunction = {\n    (urlStr: string): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: false | undefined, slashesDenoteHost?: boolean): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: true, slashesDenoteHost?: boolean): UrlWithParsedQuery;\n    (urlStr: string, parseQueryString: boolean, slashesDenoteHost?: boolean): Url;\n};\n\ntype FormatFunction = {\n    (URL: URL, options?: URLFormatOptions): string;\n    (urlObject: UrlObject | string): string;\n};\n\ntype ResolveFunction = {\n    (from: string, to: string): string;\n};\n\nexport const url = {\n    parse: _parse as ParseFunction,\n    format: _format as FormatFunction,\n    resolve: _resolve as ResolveFunction,\n};\n", "import { settings } from '@pixi/settings';\n\nfunction assertPath(path: string)\n{\n    if (typeof path !== 'string')\n    {\n        throw new TypeError(`Path must be a string. Received ${JSON.stringify(path)}`);\n    }\n}\n\nfunction removeUrlParams(url: string): string\n{\n    const re = url.split('?')[0];\n\n    return re.split('#')[0];\n}\n\nfunction escapeRegExp(string: string)\n{\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n}\n\nfunction replaceAll(str: string, find: string, replace: string)\n{\n    return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path: string, allowAboveRoot: boolean)\n{\n    let res = '';\n    let lastSegmentLength = 0;\n    let lastSlash = -1;\n    let dots = 0;\n    let code: number;\n\n    for (let i = 0; i <= path.length; ++i)\n    {\n        if (i < path.length)\n        {\n            code = path.charCodeAt(i);\n        }\n        else if (code === 47)\n        {\n            break;\n        }\n        else\n        {\n            code = 47;\n        }\n        if (code === 47)\n        {\n            if (lastSlash === i - 1 || dots === 1)\n            {\n                // NOOP\n            }\n            else if (lastSlash !== i - 1 && dots === 2)\n            {\n                if (\n                    res.length < 2\n                    || lastSegmentLength !== 2\n                    || res.charCodeAt(res.length - 1) !== 46\n                    || res.charCodeAt(res.length - 2) !== 46\n                )\n                {\n                    if (res.length > 2)\n                    {\n                        const lastSlashIndex = res.lastIndexOf('/');\n\n                        if (lastSlashIndex !== res.length - 1)\n                        {\n                            if (lastSlashIndex === -1)\n                            {\n                                res = '';\n                                lastSegmentLength = 0;\n                            }\n                            else\n                            {\n                                res = res.slice(0, lastSlashIndex);\n                                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n                            }\n                            lastSlash = i;\n                            dots = 0;\n                            continue;\n                        }\n                    }\n                    else if (res.length === 2 || res.length === 1)\n                    {\n                        res = '';\n                        lastSegmentLength = 0;\n                        lastSlash = i;\n                        dots = 0;\n                        continue;\n                    }\n                }\n                if (allowAboveRoot)\n                {\n                    if (res.length > 0)\n                    { res += '/..'; }\n                    else\n                    { res = '..'; }\n                    lastSegmentLength = 2;\n                }\n            }\n            else\n            {\n                if (res.length > 0)\n                {\n                    res += `/${path.slice(lastSlash + 1, i)}`;\n                }\n                else\n                {\n                    res = path.slice(lastSlash + 1, i);\n                }\n                lastSegmentLength = i - lastSlash - 1;\n            }\n            lastSlash = i;\n            dots = 0;\n        }\n        else if (code === 46 && dots !== -1)\n        {\n            ++dots;\n        }\n        else\n        {\n            dots = -1;\n        }\n    }\n\n    return res;\n}\n\nexport interface Path\n{\n    toPosix: (path: string) => string;\n    toAbsolute: (url: string, baseUrl?: string, rootUrl?: string) => string;\n    isUrl: (path: string) => boolean;\n    isDataUrl: (path: string) => boolean;\n    hasProtocol: (path: string) => boolean;\n    getProtocol: (path: string) => string;\n    normalize: (path: string) => string;\n    join: (...paths: string[]) => string;\n    isAbsolute: (path: string) => boolean;\n    dirname: (path: string) => string;\n    rootname: (path: string) => string;\n    basename: (path: string, ext?: string) => string;\n    extname: (path: string) => string;\n    parse: (path: string) => { root?: string, dir?: string, base?: string, ext?: string, name?: string };\n    sep: string,\n    delimiter: string\n}\n\nexport const path: Path = {\n    /**\n     * Converts a path to posix format.\n     * @param path - The path to convert to posix\n     */\n    toPosix(path: string) { return replaceAll(path, '\\\\', '/'); },\n    /**\n     * Checks if the path is a URL\n     * @param path - The path to check\n     */\n    isUrl(path: string) { return (/^https?:/).test(this.toPosix(path)); },\n    /**\n     * Checks if the path is a data URL\n     * @param path - The path to check\n     */\n    isDataUrl(path: string)\n    {\n        // eslint-disable-next-line max-len\n        return (/^data:([a-z]+\\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s<>]*?)$/i)\n            .test(path);\n    },\n    /**\n     * Checks if the path has a protocol e.g. http://\n     * This will return true for windows file paths\n     * @param path - The path to check\n     */\n    hasProtocol(path: string) { return (/^[^/:]+:\\//).test(this.toPosix(path)); },\n    /**\n     * Returns the protocol of the path e.g. http://, C:/, file:///\n     * @param path - The path to get the protocol from\n     */\n    getProtocol(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let protocol = '';\n\n        const isFile = (/^file:\\/\\/\\//).exec(path);\n        const isHttp = (/^[^/:]+:\\/\\//).exec(path);\n        const isWindows = (/^[^/:]+:\\//).exec(path);\n\n        if (isFile || isHttp || isWindows)\n        {\n            const arr = isFile?.[0] || isHttp?.[0] || isWindows?.[0];\n\n            protocol = arr;\n            path = path.slice(arr.length);\n        }\n\n        return protocol;\n    },\n\n    /**\n     * Converts URL to an absolute path.\n     * When loading from a Web Worker, we must use absolute paths.\n     * If the URL is already absolute we return it as is\n     * If it's not, we convert it\n     * @param url - The URL to test\n     * @param customBaseUrl - The base URL to use\n     * @param customRootUrl - The root URL to use\n     */\n    toAbsolute(url: string, customBaseUrl?: string, customRootUrl?: string)\n    {\n        if (this.isDataUrl(url)) return url;\n\n        const baseUrl = removeUrlParams(this.toPosix(customBaseUrl ?? settings.ADAPTER.getBaseUrl()));\n        const rootUrl = removeUrlParams(this.toPosix(customRootUrl ?? this.rootname(baseUrl)));\n\n        assertPath(url);\n        url = this.toPosix(url);\n\n        // root relative url\n        if (url.startsWith('/'))\n        {\n            return path.join(rootUrl, url.slice(1));\n        }\n\n        const absolutePath = this.isAbsolute(url) ? url : this.join(baseUrl, url);\n\n        return absolutePath;\n    },\n\n    /**\n     * Normalizes the given path, resolving '..' and '.' segments\n     * @param path - The path to normalize\n     */\n    normalize(path: string)\n    {\n        path = this.toPosix(path);\n        assertPath(path);\n\n        if (path.length === 0) return '.';\n\n        let protocol = '';\n        const isAbsolute = path.startsWith('/');\n\n        if (this.hasProtocol(path))\n        {\n            protocol = this.rootname(path);\n            path = path.slice(protocol.length);\n        }\n\n        const trailingSeparator = path.endsWith('/');\n\n        // Normalize the path\n        path = normalizeStringPosix(path, false);\n\n        if (path.length > 0 && trailingSeparator) path += '/';\n        if (isAbsolute) return `/${path}`;\n\n        return protocol + path;\n    },\n\n    /**\n     * Determines if path is an absolute path.\n     * Absolute paths can be urls, data urls, or paths on disk\n     * @param path - The path to test\n     */\n    isAbsolute(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        if (this.hasProtocol(path)) return true;\n\n        return path.startsWith('/');\n    },\n\n    /**\n     * Joins all given path segments together using the platform-specific separator as a delimiter,\n     * then normalizes the resulting path\n     * @param segments - The segments of the path to join\n     */\n    join(...segments: string[])\n    {\n        if (segments.length === 0)\n        { return '.'; }\n        let joined;\n\n        for (let i = 0; i < segments.length; ++i)\n        {\n            const arg = segments[i];\n\n            assertPath(arg);\n            if (arg.length > 0)\n            {\n                if (joined === undefined) joined = arg;\n                else\n                {\n                    const prevArg = segments[i - 1] ?? '';\n\n                    if (this.extname(prevArg))\n                    {\n                        joined += `/../${arg}`;\n                    }\n                    else\n                    {\n                        joined += `/${arg}`;\n                    }\n                }\n            }\n        }\n        if (joined === undefined) { return '.'; }\n\n        return this.normalize(joined);\n    },\n\n    /**\n     * Returns the directory name of a path\n     * @param path - The path to parse\n     */\n    dirname(path: string)\n    {\n        assertPath(path);\n        if (path.length === 0) return '.';\n        path = this.toPosix(path);\n        let code = path.charCodeAt(0);\n        const hasRoot = code === 47;\n        let end = -1;\n        let matchedSlash = true;\n\n        const proto = this.getProtocol(path);\n        const origpath = path;\n\n        path = path.slice(proto.length);\n\n        for (let i = path.length - 1; i >= 1; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                if (!matchedSlash)\n                {\n                    end = i;\n                    break;\n                }\n            }\n            else\n            {\n                // We saw the first non-path separator\n                matchedSlash = false;\n            }\n        }\n\n        // if end is -1 and its a url then we need to add the path back\n        // eslint-disable-next-line no-nested-ternary\n        if (end === -1) return hasRoot ? '/' : this.isUrl(origpath) ? proto + path : proto;\n        if (hasRoot && end === 1) return '//';\n\n        return proto + path.slice(0, end);\n    },\n\n    /**\n     * Returns the root of the path e.g. /, C:/, file:///, http://domain.com/\n     * @param path - The path to parse\n     */\n    rootname(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let root = '';\n\n        if (path.startsWith('/')) root = '/';\n        else\n        {\n            root = this.getProtocol(path);\n        }\n\n        if (this.isUrl(path))\n        {\n            // need to find the first path separator\n            const index = path.indexOf('/', root.length);\n\n            if (index !== -1)\n            {\n                root = path.slice(0, index);\n            }\n            else root = path;\n\n            if (!root.endsWith('/')) root += '/';\n        }\n\n        return root;\n    },\n\n    /**\n     * Returns the last portion of a path\n     * @param path - The path to test\n     * @param ext - Optional extension to remove\n     */\n    basename(path: string, ext?: string)\n    {\n        assertPath(path);\n        if (ext) assertPath(ext);\n\n        path = this.toPosix(path);\n\n        let start = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i: number;\n\n        if (ext !== undefined && ext.length > 0 && ext.length <= path.length)\n        {\n            if (ext.length === path.length && ext === path) return '';\n            let extIdx = ext.length - 1;\n            let firstNonSlashEnd = -1;\n\n            for (i = path.length - 1; i >= 0; --i)\n            {\n                const code = path.charCodeAt(i);\n\n                if (code === 47)\n                {\n                    // If we reached a path separator that was not part of a set of path\n                    // separators at the end of the string, stop now\n                    if (!matchedSlash)\n                    {\n                        start = i + 1;\n                        break;\n                    }\n                }\n                else\n                {\n                    if (firstNonSlashEnd === -1)\n                    {\n                        // We saw the first non-path separator, remember this index in case\n                        // we need it if the extension ends up not matching\n                        matchedSlash = false;\n                        firstNonSlashEnd = i + 1;\n                    }\n                    if (extIdx >= 0)\n                    {\n                        // Try to match the explicit extension\n                        if (code === ext.charCodeAt(extIdx))\n                        {\n                            if (--extIdx === -1)\n                            {\n                                // We matched the extension, so mark this as the end of our path\n                                // component\n                                end = i;\n                            }\n                        }\n                        else\n                        {\n                            // Extension does not match, so our result is the entire path\n                            // component\n                            extIdx = -1;\n                            end = firstNonSlashEnd;\n                        }\n                    }\n                }\n            }\n\n            if (start === end) end = firstNonSlashEnd; else if (end === -1) end = path.length;\n\n            return path.slice(start, end);\n        }\n        for (i = path.length - 1; i >= 0; --i)\n        {\n            if (path.charCodeAt(i) === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    start = i + 1;\n                    break;\n                }\n            }\n            else if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // path component\n                matchedSlash = false;\n                end = i + 1;\n            }\n        }\n\n        if (end === -1) return '';\n\n        return path.slice(start, end);\n    },\n\n    /**\n     * Returns the extension of the path, from the last occurrence of the . (period) character to end of string in the last\n     * portion of the path. If there is no . in the last portion of the path, or if there are no . characters other than\n     * the first character of the basename of path, an empty string is returned.\n     * @param path - The path to parse\n     */\n    extname(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        for (let i = path.length - 1; i >= 0; --i)\n        {\n            const code = path.charCodeAt(i);\n\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            return '';\n        }\n\n        return path.slice(startDot, end);\n    },\n\n    /**\n     * Parses a path into an object containing the 'root', `dir`, `base`, `ext`, and `name` properties.\n     * @param path - The path to parse\n     */\n    parse(path: string)\n    {\n        assertPath(path);\n\n        const ret = { root: '', dir: '', base: '', ext: '', name: '' };\n\n        if (path.length === 0) return ret;\n        path = this.toPosix(path);\n\n        let code = path.charCodeAt(0);\n        const isAbsolute = this.isAbsolute(path);\n        let start: number;\n        const protocol = '';\n\n        ret.root = this.rootname(path);\n\n        if (isAbsolute || this.hasProtocol(path))\n        {\n            start = 1;\n        }\n        else\n        {\n            start = 0;\n        }\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i = path.length - 1;\n\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        // Get non-dir info\n        for (; i >= start; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            if (end !== -1)\n            {\n                if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);\n                else ret.base = ret.name = path.slice(startPart, end);\n            }\n        }\n        else\n        {\n            if (startPart === 0 && isAbsolute)\n            {\n                ret.name = path.slice(1, startDot);\n                ret.base = path.slice(1, end);\n            }\n            else\n            {\n                ret.name = path.slice(startPart, startDot);\n                ret.base = path.slice(startPart, end);\n            }\n            ret.ext = path.slice(startDot, end);\n        }\n\n        ret.dir = this.dirname(path);\n        if (protocol) ret.dir = protocol + ret.dir;\n\n        return ret;\n    },\n\n    sep: '/',\n    delimiter: ':'\n} as Path;\n", "import { settings } from '@pixi/settings';\n\n/**\n * The prefix that denotes a URL is for a retina asset.\n * @static\n * @name RETINA_PREFIX\n * @memberof PIXI.settings\n * @type {RegExp}\n * @default /@([0-9\\.]+)x/\n * @example `@2x`\n */\nsettings.RETINA_PREFIX = /@([0-9\\.]+)x/;\n\n/**\n * Should the `failIfMajorPerformanceCaveat` flag be enabled as a context option used in the `isWebGLSupported` function.\n * If set to true, a WebGL renderer can fail to be created if the browser thinks there could be performance issues when\n * using WebGL.\n *\n * In PixiJS v6 this has changed from true to false by default, to allow WebGL to work in as many scenarios as possible.\n * However, some users may have a poor experience, for example, if a user has a gpu or driver version blacklisted by the\n * browser.\n *\n * If your application requires high performance rendering, you may wish to set this to false.\n * We recommend one of two options if you decide to set this flag to false:\n *\n * 1: Use the `pixi.js-legacy` package, which includes a Canvas renderer as a fallback in case high performance WebGL is\n *    not supported.\n *\n * 2: Call `isWebGLSupported` (which if found in the PIXI.utils package) in your code before attempting to create a PixiJS\n *    renderer, and show an error message to the user if the function returns false, explaining that their device & browser\n *    combination does not support high performance WebGL.\n *    This is a much better strategy than trying to create a PixiJS renderer and finding it then fails.\n * @static\n * @name FAIL_IF_MAJOR_PERFORMANCE_CAVEAT\n * @memberof PIXI.settings\n * @type {boolean}\n * @default false\n */\nsettings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT = false;\n\nexport { settings };\n", "import { settings } from '@pixi/settings';\n\nlet saidHello = false;\nconst VERSION = '$_VERSION';\n\n/**\n * Skips the hello message of renderers that are created after this is run.\n * @function skipHello\n * @memberof PIXI.utils\n */\nexport function skipHello(): void\n{\n    saidHello = true;\n}\n\n/**\n * Logs out the version and renderer information for this running instance of PIXI.\n * If you don't want to see this message you can run `PIXI.utils.skipHello()` before\n * creating your renderer. Keep in mind that doing that will forever make you a jerk face.\n * @static\n * @function sayHello\n * @memberof PIXI.utils\n * @param {string} type - The string renderer type to log.\n */\nexport function sayHello(type: string): void\n{\n    if (saidHello)\n    {\n        return;\n    }\n\n    if (settings.ADAPTER.getNavigator().userAgent.toLowerCase().indexOf('chrome') > -1)\n    {\n        const args = [\n            `\\n %c %c %c PixiJS ${VERSION} - ✰ ${type} ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \\n\\n`,\n            'background: #ff66a5; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'color: #ff66a5; background: #030307; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'background: #ffc3dc; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n        ];\n\n        globalThis.console.log(...args);\n    }\n    else if (globalThis.console)\n    {\n        globalThis.console.log(`PixiJS ${VERSION} - ${type} - http://www.pixijs.com/`);\n    }\n\n    saidHello = true;\n}\n", "import { settings } from '../settings';\n\nlet supported: boolean | undefined;\n\n/**\n * Helper for checking for WebGL support.\n * @memberof PIXI.utils\n * @function isWebGLSupported\n * @returns {boolean} Is WebGL supported.\n */\nexport function isWebGLSupported(): boolean\n{\n    if (typeof supported === 'undefined')\n    {\n        supported = (function supported(): boolean\n        {\n            const contextOptions = {\n                stencil: true,\n                failIfMajorPerformanceCaveat: settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT,\n            };\n\n            try\n            {\n                if (!settings.ADAPTER.getWebGLRenderingContext())\n                {\n                    return false;\n                }\n\n                const canvas = settings.ADAPTER.createCanvas();\n                let gl = (\n                    canvas.getContext('webgl', contextOptions)\n                    || canvas.getContext('experimental-webgl', contextOptions)\n                ) as WebGLRenderingContext;\n\n                const success = !!(gl && gl.getContextAttributes().stencil);\n\n                if (gl)\n                {\n                    const loseContext = gl.getExtension('WEBGL_lose_context');\n\n                    if (loseContext)\n                    {\n                        loseContext.loseContext();\n                    }\n                }\n\n                gl = null;\n\n                return success;\n            }\n            catch (e)\n            {\n                return false;\n            }\n        })();\n    }\n\n    return supported;\n}\n", "import { default as cssColorNames } from 'css-color-names';\n\n/**\n * Converts a hexadecimal color number to an [R, G, B] array of normalized floats (numbers from 0.0 to 1.0).\n * @example\n * PIXI.utils.hex2rgb(0xffffff); // returns [1, 1, 1]\n * @memberof PIXI.utils\n * @function hex2rgb\n * @param {number} hex - The hexadecimal number to convert\n * @param  {number[]} [out=[]] - If supplied, this array will be used rather than returning a new one\n * @returns {number[]} An array representing the [R, G, B] of the color where all values are floats.\n */\nexport function hex2rgb(hex: number, out: Array<number> | Float32Array = []): Array<number> | Float32Array\n{\n    out[0] = ((hex >> 16) & 0xFF) / 255;\n    out[1] = ((hex >> 8) & 0xFF) / 255;\n    out[2] = (hex & 0xFF) / 255;\n\n    return out;\n}\n\n/**\n * Converts a hexadecimal color number to a string.\n * @example\n * PIXI.utils.hex2string(0xffffff); // returns \"#ffffff\"\n * @memberof PIXI.utils\n * @function hex2string\n * @param {number} hex - Number in hex (e.g., `0xffffff`)\n * @returns {string} The string color (e.g., `\"#ffffff\"`).\n */\nexport function hex2string(hex: number): string\n{\n    let hexString = hex.toString(16);\n\n    hexString = '000000'.substring(0, 6 - hexString.length) + hexString;\n\n    return `#${hexString}`;\n}\n\n/**\n * Converts a string to a hexadecimal color number.\n * It can handle:\n *  hex strings starting with #: \"#ffffff\"\n *  hex strings starting with 0x: \"0xffffff\"\n *  hex strings without prefix: \"ffffff\"\n *  css colors: \"black\"\n * @example\n * PIXI.utils.string2hex(\"#ffffff\"); // returns 0xffffff, which is ******** as an integer\n * @memberof PIXI.utils\n * @function string2hex\n * @param {string} string - The string color (e.g., `\"#ffffff\"`)\n * @returns {number} Number in hexadecimal.\n */\nexport function string2hex(string: string): number\n{\n    if (typeof string === 'string')\n    {\n        string = (cssColorNames as {[key: string]: string})[string.toLowerCase()] || string;\n\n        if (string[0] === '#')\n        {\n            string = string.slice(1);\n        }\n    }\n\n    return parseInt(string, 16);\n}\n\n/**\n * Converts a color as an [R, G, B] array of normalized floats to a hexadecimal number.\n * @example\n * PIXI.utils.rgb2hex([1, 1, 1]); // returns 0xffffff, which is ******** as an integer\n * @memberof PIXI.utils\n * @function rgb2hex\n * @param {number[]} rgb - Array of numbers where all values are normalized floats from 0.0 to 1.0.\n * @returns {number} Number in hexadecimal.\n */\nexport function rgb2hex(rgb: number[] | Float32Array): number\n{\n    return (((rgb[0] * 255) << 16) + ((rgb[1] * 255) << 8) + (rgb[2] * 255 | 0));\n}\n", "import { BLEND_MODES } from '@pixi/constants';\n\n/**\n * Corrects PixiJS blend, takes premultiplied alpha into account\n * @memberof PIXI.utils\n * @function mapPremultipliedBlendModes\n * @private\n * @returns {Array<number[]>} Mapped modes.\n */\nfunction mapPremultipliedBlendModes(): number[][]\n{\n    const pm = [];\n    const npm = [];\n\n    for (let i = 0; i < 32; i++)\n    {\n        pm[i] = i;\n        npm[i] = i;\n    }\n\n    pm[BLEND_MODES.NORMAL_NPM] = BLEND_MODES.NORMAL;\n    pm[BLEND_MODES.ADD_NPM] = BLEND_MODES.ADD;\n    pm[BLEND_MODES.SCREEN_NPM] = BLEND_MODES.SCREEN;\n\n    npm[BLEND_MODES.NORMAL] = BLEND_MODES.NORMAL_NPM;\n    npm[BLEND_MODES.ADD] = BLEND_MODES.ADD_NPM;\n    npm[BLEND_MODES.SCREEN] = BLEND_MODES.SCREEN_NPM;\n\n    const array: number[][] = [];\n\n    array.push(npm);\n    array.push(pm);\n\n    return array;\n}\n\n/**\n * maps premultiply flag and blendMode to adjusted blendMode\n * @memberof PIXI.utils\n * @constant premultiplyBlendMode\n * @type {Array<number[]>}\n */\nexport const premultiplyBlendMode = mapPremultipliedBlendModes();\n\n/**\n * changes blendMode according to texture format\n * @memberof PIXI.utils\n * @function correctBlendMode\n * @param {number} blendMode - supposed blend mode\n * @param {boolean} premultiplied - whether source is premultiplied\n * @returns {number} true blend mode for this texture\n */\nexport function correctBlendMode(blendMode: number, premultiplied: boolean): number\n{\n    return premultiplyBlendMode[premultiplied ? 1 : 0][blendMode];\n}\n\n/**\n * combines rgb and alpha to out array\n * @memberof PIXI.utils\n * @function premultiplyRgba\n * @param {Float32Array|number[]} rgb - input rgb\n * @param {number} alpha - alpha param\n * @param {Float32Array} [out] - output\n * @param {boolean} [premultiply=true] - do premultiply it\n * @returns {Float32Array} vec4 rgba\n */\nexport function premultiplyRgba(\n    rgb: Float32Array | number[],\n    alpha: number,\n    out?: Float32Array,\n    premultiply?: boolean\n): Float32Array\n{\n    out = out || new Float32Array(4);\n    if (premultiply || premultiply === undefined)\n    {\n        out[0] = rgb[0] * alpha;\n        out[1] = rgb[1] * alpha;\n        out[2] = rgb[2] * alpha;\n    }\n    else\n    {\n        out[0] = rgb[0];\n        out[1] = rgb[1];\n        out[2] = rgb[2];\n    }\n    out[3] = alpha;\n\n    return out;\n}\n\n/**\n * premultiplies tint\n * @memberof PIXI.utils\n * @function premultiplyTint\n * @param {number} tint - integer RGB\n * @param {number} alpha - floating point alpha (0.0-1.0)\n * @returns {number} tint multiplied by alpha\n */\nexport function premultiplyTint(tint: number, alpha: number): number\n{\n    if (alpha === 1.0)\n    {\n        return (alpha * 255 << 24) + tint;\n    }\n    if (alpha === 0.0)\n    {\n        return 0;\n    }\n    let R = ((tint >> 16) & 0xFF);\n    let G = ((tint >> 8) & 0xFF);\n    let B = (tint & 0xFF);\n\n    R = ((R * alpha) + 0.5) | 0;\n    G = ((G * alpha) + 0.5) | 0;\n    B = ((B * alpha) + 0.5) | 0;\n\n    return (alpha * 255 << 24) + (R << 16) + (G << 8) + B;\n}\n\n/**\n * converts integer tint and float alpha to vec4 form, premultiplies by default\n * @memberof PIXI.utils\n * @function premultiplyTintToRgba\n * @param {number} tint - input tint\n * @param {number} alpha - alpha param\n * @param {Float32Array} [out] - output\n * @param {boolean} [premultiply=true] - do premultiply it\n * @returns {Float32Array} vec4 rgba\n */\nexport function premultiplyTintToRgba(tint: number, alpha: number, out: Float32Array, premultiply?: boolean): Float32Array\n{\n    out = out || new Float32Array(4);\n    out[0] = ((tint >> 16) & 0xFF) / 255.0;\n    out[1] = ((tint >> 8) & 0xFF) / 255.0;\n    out[2] = (tint & 0xFF) / 255.0;\n    if (premultiply || premultiply === undefined)\n    {\n        out[0] *= alpha;\n        out[1] *= alpha;\n        out[2] *= alpha;\n    }\n    out[3] = alpha;\n\n    return out;\n}\n", "/**\n * Generic Mask Stack data structure\n * @memberof PIXI.utils\n * @function createIndicesForQuads\n * @param {number} size - Number of quads\n * @param {Uint16Array|Uint32Array} [outBuffer] - Buffer for output, length has to be `6 * size`\n * @returns {Uint16Array|Uint32Array} - Resulting index buffer\n */\nexport function createIndicesForQuads(size: number, outBuffer: Uint16Array | Uint32Array = null): Uint16Array | Uint32Array\n{\n    // the total number of indices in our array, there are 6 points per quad.\n    const totalIndices = size * 6;\n\n    outBuffer = outBuffer || new Uint16Array(totalIndices);\n\n    if (outBuffer.length !== totalIndices)\n    {\n        throw new Error(`Out buffer length is incorrect, got ${outBuffer.length} and expected ${totalIndices}`);\n    }\n\n    // fill the indices with the quads to draw\n    for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4)\n    {\n        outBuffer[i + 0] = j + 0;\n        outBuffer[i + 1] = j + 1;\n        outBuffer[i + 2] = j + 2;\n        outBuffer[i + 3] = j + 0;\n        outBuffer[i + 4] = j + 2;\n        outBuffer[i + 5] = j + 3;\n    }\n\n    return outBuffer;\n}\n", "import type { ITypedArray } from '@pixi/core';\n\nexport function getBufferType(\n    array: ITypedArray\n): 'Float32Array' | 'Uint32Array' | 'Int32Array' | 'Uint16Array' | 'Uint8Array' | null\n{\n    if (array.BYTES_PER_ELEMENT === 4)\n    {\n        if (array instanceof Float32Array)\n        {\n            return 'Float32Array';\n        }\n        else if (array instanceof Uint32Array)\n        {\n            return 'Uint32Array';\n        }\n\n        return 'Int32Array';\n    }\n    else if (array.BYTES_PER_ELEMENT === 2)\n    {\n        if (array instanceof Uint16Array)\n        {\n            return 'Uint16Array';\n        }\n    }\n    else if (array.BYTES_PER_ELEMENT === 1)\n    {\n        if (array instanceof Uint8Array)\n        {\n            return 'Uint8Array';\n        }\n    }\n\n    // TODO map out the rest of the array elements!\n    return null;\n}\n", "import { getBufferType } from './getBufferType';\n\n/* eslint-disable object-shorthand */\nconst map = { Float32Array: Float32Array, Uint32Array: Uint32Array, Int32Array: Int32Array, Uint8Array: Uint8Array };\n\ntype PackedArray = Float32Array | Uint32Array | Int32Array | Uint8Array;\n\nexport function interleaveTypedArrays(arrays: PackedArray[], sizes: number[]): Float32Array\n{\n    let outSize = 0;\n    let stride = 0;\n    const views: {[key: string]: PackedArray} = {};\n\n    for (let i = 0; i < arrays.length; i++)\n    {\n        stride += sizes[i];\n        outSize += arrays[i].length;\n    }\n\n    const buffer = new ArrayBuffer(outSize * 4);\n\n    let out = null;\n    let littleOffset = 0;\n\n    for (let i = 0; i < arrays.length; i++)\n    {\n        const size = sizes[i];\n        const array = arrays[i];\n\n        /*\n        @todo This is unsafe casting but consistent with how the code worked previously. Should it stay this way\n              or should and `getBufferTypeUnsafe` function be exposed that throws an Error if unsupported type is passed?\n         */\n        const type = getBufferType(array) as keyof typeof map;\n\n        if (!views[type])\n        {\n            views[type] = new map[type](buffer);\n        }\n\n        out = views[type];\n\n        for (let j = 0; j < array.length; j++)\n        {\n            const indexStart = ((j / size | 0) * stride) + littleOffset;\n            const index = j % size;\n\n            out[indexStart + index] = array[j];\n        }\n\n        littleOffset += size;\n    }\n\n    return new Float32Array(buffer);\n}\n", "// Taken from the bit-twiddle package\n\n/**\n * Rounds to next power of two.\n * @function nextPow2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {number} - next rounded power of two\n */\nexport function nextPow2(v: number): number\n{\n    v += v === 0 ? 1 : 0;\n    --v;\n    v |= v >>> 1;\n    v |= v >>> 2;\n    v |= v >>> 4;\n    v |= v >>> 8;\n    v |= v >>> 16;\n\n    return v + 1;\n}\n\n/**\n * Checks if a number is a power of two.\n * @function isPow2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {boolean} `true` if value is power of two\n */\nexport function isPow2(v: number): boolean\n{\n    return !(v & (v - 1)) && (!!v);\n}\n\n/**\n * Computes ceil of log base 2\n * @function log2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {number} logarithm base 2\n */\nexport function log2(v: number): number\n{\n    let r = (v > 0xFFFF ? 1 : 0) << 4;\n\n    v >>>= r;\n\n    let shift = (v > 0xFF ? 1 : 0) << 3;\n\n    v >>>= shift; r |= shift;\n    shift = (v > 0xF ? 1 : 0) << 2;\n    v >>>= shift; r |= shift;\n    shift = (v > 0x3 ? 1 : 0) << 1;\n    v >>>= shift; r |= shift;\n\n    return r | (v >> 1);\n}\n", "/**\n * Remove items from a javascript array without generating garbage\n * @function removeItems\n * @memberof PIXI.utils\n * @param {Array<any>} arr - Array to remove elements from\n * @param {number} startIdx - starting index\n * @param {number} removeCount - how many to remove\n */\nexport function removeItems(arr: any[], startIdx: number, removeCount: number): void\n{\n    const length = arr.length;\n    let i;\n\n    if (startIdx >= length || removeCount === 0)\n    {\n        return;\n    }\n\n    removeCount = (startIdx + removeCount > length ? length - startIdx : removeCount);\n\n    const len = length - removeCount;\n\n    for (i = startIdx; i < len; ++i)\n    {\n        arr[i] = arr[i + removeCount];\n    }\n\n    arr.length = len;\n}\n", "/**\n * Returns sign of number\n * @memberof PIXI.utils\n * @function sign\n * @param {number} n - the number to check the sign of\n * @returns {number} 0 if `n` is 0, -1 if `n` is negative, 1 if `n` is positive\n */\nexport function sign(n: number): -1 | 0 | 1\n{\n    if (n === 0) return 0;\n\n    return n < 0 ? -1 : 1;\n}\n", "let nextUid = 0;\n\n/**\n * Gets the next unique identifier\n * @memberof PIXI.utils\n * @function uid\n * @returns {number} The next unique identifier to use.\n */\nexport function uid(): number\n{\n    return ++nextUid;\n}\n", "import type { Dict } from '../types';\n\n// A map of warning messages already fired\nconst warnings: Dict<boolean> = {};\n\n/**\n * Helper for warning developers about deprecated features & settings.\n * A stack track for warnings is given; useful for tracking-down where\n * deprecated methods/properties/classes are being used within the code.\n * @memberof PIXI.utils\n * @function deprecation\n * @param {string} version - The version where the feature became deprecated\n * @param {string} message - Message should include what is deprecated, where, and the new solution\n * @param {number} [ignoreDepth=3] - The number of steps to ignore at the top of the error stack\n *        this is mostly to ignore internal deprecation calls.\n */\nexport function deprecation(version: string, message: string, ignoreDepth = 3): void\n{\n    // Ignore duplicat\n    if (warnings[message])\n    {\n        return;\n    }\n\n    /* eslint-disable no-console */\n    let stack = new Error().stack;\n\n    // Handle IE < 10 and Safari < 6\n    if (typeof stack === 'undefined')\n    {\n        console.warn('PixiJS Deprecation Warning: ', `${message}\\nDeprecated since v${version}`);\n    }\n    else\n    {\n        // chop off the stack trace which includes PixiJS internal calls\n        stack = stack.split('\\n').splice(ignoreDepth).join('\\n');\n\n        if (console.groupCollapsed)\n        {\n            console.groupCollapsed(\n                '%cPixiJS Deprecation Warning: %c%s',\n                'color:#614108;background:#fffbe6',\n                'font-weight:normal;color:#614108;background:#fffbe6',\n                `${message}\\nDeprecated since v${version}`\n            );\n            console.warn(stack);\n            console.groupEnd();\n        }\n        else\n        {\n            console.warn('PixiJS Deprecation Warning: ', `${message}\\nDeprecated since v${version}`);\n            console.warn(stack);\n        }\n    }\n    /* eslint-enable no-console */\n\n    warnings[message] = true;\n}\n", "import type { Program, Texture, BaseTexture } from '@pixi/core';\n\n/**\n * @todo Describe property usage\n * @static\n * @name ProgramCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const ProgramCache: {[key: string]: Program} = {};\n\n/**\n * @todo Describe property usage\n * @static\n * @name TextureCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const TextureCache: {[key: string]: Texture} = Object.create(null);\n\n/**\n * @todo Describe property usage\n * @static\n * @name BaseTextureCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const BaseTextureCache: {[key: string]: BaseTexture} = Object.create(null);\n\n/**\n * Destroys all texture in the cache\n * @memberof PIXI.utils\n * @function destroyTextureCache\n */\nexport function destroyTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        TextureCache[key].destroy();\n    }\n    for (key in BaseTextureCache)\n    {\n        BaseTextureCache[key].destroy();\n    }\n}\n\n/**\n * Removes all textures from cache, but does not destroy them\n * @memberof PIXI.utils\n * @function clearTextureCache\n */\nexport function clearTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        delete TextureCache[key];\n    }\n    for (key in BaseTextureCache)\n    {\n        delete BaseTextureCache[key];\n    }\n}\n", "import { settings } from '@pixi/settings';\n\n/**\n * Creates a Canvas element of the given size to be used as a target for rendering to.\n * @class\n * @memberof PIXI.utils\n */\nexport class CanvasRenderTarget\n{\n    /** The Canvas object that belongs to this CanvasRenderTarget. */\n    public canvas: HTMLCanvasElement;\n\n    /** A CanvasRenderingContext2D object representing a two-dimensional rendering context. */\n    public context: CanvasRenderingContext2D;\n\n    /**\n     * The resolution / device pixel ratio of the canvas\n     * @default 1\n     */\n    public resolution: number;\n\n    /**\n     * @param width - the width for the newly created canvas\n     * @param height - the height for the newly created canvas\n     * @param {number} [resolution=PIXI.settings.RESOLUTION] - The resolution / device pixel ratio of the canvas\n     */\n    constructor(width: number, height: number, resolution?: number)\n    {\n        this.canvas = settings.ADAPTER.createCanvas();\n\n        this.context = this.canvas.getContext('2d');\n\n        this.resolution = resolution || settings.RESOLUTION;\n\n        this.resize(width, height);\n    }\n\n    /**\n     * Clears the canvas that was created by the CanvasRenderTarget class.\n     * @private\n     */\n    clear(): void\n    {\n        this.context.setTransform(1, 0, 0, 1, 0, 0);\n        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    }\n\n    /**\n     * Resizes the canvas to the specified width and height.\n     * @param desiredWidth - the desired width of the canvas\n     * @param desiredHeight - the desired height of the canvas\n     */\n    resize(desiredWidth: number, desiredHeight: number): void\n    {\n        this.canvas.width = Math.round(desiredWidth * this.resolution);\n        this.canvas.height = Math.round(desiredHeight * this.resolution);\n    }\n\n    /** Destroys this canvas. */\n    destroy(): void\n    {\n        this.context = null;\n        this.canvas = null;\n    }\n\n    /**\n     * The width of the canvas buffer in pixels.\n     * @member {number}\n     */\n    get width(): number\n    {\n        return this.canvas.width;\n    }\n\n    set width(val: number)\n    {\n        this.canvas.width = Math.round(val);\n    }\n\n    /**\n     * The height of the canvas buffer in pixels.\n     * @member {number}\n     */\n    get height(): number\n    {\n        return this.canvas.height;\n    }\n\n    set height(val: number)\n    {\n        this.canvas.height = Math.round(val);\n    }\n}\n", "interface Inset\n{\n    top?: number;\n    left?: number;\n    right?: number;\n    bottom?: number;\n}\n\n/**\n * Trim transparent borders from a canvas\n * @memberof PIXI.utils\n * @function trimCanvas\n * @param {HTMLCanvasElement} canvas - the canvas to trim\n * @returns {object} Trim data\n */\nexport function trimCanvas(canvas: HTMLCanvasElement): {width: number; height: number; data?: ImageData}\n{\n    // https://gist.github.com/remy/784508\n\n    let width = canvas.width;\n    let height = canvas.height;\n\n    const context = canvas.getContext('2d', {\n        willReadFrequently: true,\n    } as CanvasRenderingContext2DSettings);\n    const imageData = context.getImageData(0, 0, width, height);\n    const pixels = imageData.data;\n    const len = pixels.length;\n\n    const bound: Inset = {\n        top: null,\n        left: null,\n        right: null,\n        bottom: null,\n    };\n    let data = null;\n    let i;\n    let x;\n    let y;\n\n    for (i = 0; i < len; i += 4)\n    {\n        if (pixels[i + 3] !== 0)\n        {\n            x = (i / 4) % width;\n            y = ~~((i / 4) / width);\n\n            if (bound.top === null)\n            {\n                bound.top = y;\n            }\n\n            if (bound.left === null)\n            {\n                bound.left = x;\n            }\n            else if (x < bound.left)\n            {\n                bound.left = x;\n            }\n\n            if (bound.right === null)\n            {\n                bound.right = x + 1;\n            }\n            else if (bound.right < x)\n            {\n                bound.right = x + 1;\n            }\n\n            if (bound.bottom === null)\n            {\n                bound.bottom = y;\n            }\n            else if (bound.bottom < y)\n            {\n                bound.bottom = y;\n            }\n        }\n    }\n\n    if (bound.top !== null)\n    {\n        width = bound.right - bound.left;\n        height = bound.bottom - bound.top + 1;\n        data = context.getImageData(bound.left, bound.top, width, height);\n    }\n\n    return {\n        height,\n        width,\n        data,\n    };\n}\n", "/**\n * Regexp for data URI.\n * Based on: {@link https://github.com/ragingwind/data-uri-regex}\n * @static\n * @constant {RegExp|string} DATA_URI\n * @memberof PIXI\n * @example data:image/png;base64\n */\nexport const DATA_URI = /^\\s*data:(?:([\\w-]+)\\/([\\w+.-]+))?(?:;charset=([\\w-]+))?(?:;(base64))?,(.*)/i;\n", "import { DATA_URI } from '../const';\n\nexport interface DecomposedDataUri\n{\n    mediaType: string;\n    subType: string;\n    charset: string;\n    encoding: string;\n    data: string;\n}\n\n/**\n * @memberof PIXI.utils\n * @interface DecomposedDataUri\n */\n\n/**\n * type, eg. `image`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} mediaType\n */\n\n/**\n * Sub type, eg. `png`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} subType\n */\n\n/**\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} charset\n */\n\n/**\n * Data encoding, eg. `base64`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} encoding\n */\n\n/**\n * The actual data\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} data\n */\n\n/**\n * Split a data URI into components. Returns undefined if\n * parameter `dataUri` is not a valid data URI.\n * @memberof PIXI.utils\n * @function decomposeDataUri\n * @param {string} dataUri - the data URI to check\n * @returns {PIXI.utils.DecomposedDataUri|undefined} The decomposed data uri or undefined\n */\nexport function decomposeDataUri(dataUri: string): DecomposedDataUri\n{\n    const dataUriMatch = DATA_URI.exec(dataUri);\n\n    if (dataUriMatch)\n    {\n        return {\n            mediaType: dataUriMatch[1] ? dataUriMatch[1].toLowerCase() : undefined,\n            subType: dataUriMatch[2] ? dataUriMatch[2].toLowerCase() : undefined,\n            charset: dataUriMatch[3] ? dataUriMatch[3].toLowerCase() : undefined,\n            encoding: dataUriMatch[4] ? dataUriMatch[4].toLowerCase() : undefined,\n            data: dataUriMatch[5],\n        };\n    }\n\n    return undefined;\n}\n", "import { url as _url } from '../url';\n\nlet tempAnchor: HTMLAnchorElement | undefined;\n\n/**\n * Sets the `crossOrigin` property for this resource based on if the url\n * for this resource is cross-origin. If crossOrigin was manually set, this\n * function does nothing.\n * Nipped from the resource loader!\n * @ignore\n * @param {string} url - The url to test.\n * @param {object} [loc=window.location] - The location object to test against.\n * @returns {string} The crossOrigin value to use (or empty string for none).\n */\nexport function determineCrossOrigin(url: string, loc: Location = globalThis.location): string\n{\n    // data: and javascript: urls are considered same-origin\n    if (url.indexOf('data:') === 0)\n    {\n        return '';\n    }\n\n    // default is window.location\n    loc = loc || globalThis.location;\n\n    if (!tempAnchor)\n    {\n        tempAnchor = document.createElement('a');\n    }\n\n    // let the browser determine the full href for the url of this resource and then\n    // parse with the node url lib, we can't use the properties of the anchor element\n    // because they don't work in IE9 :(\n    tempAnchor.href = url;\n    const parsedUrl = _url.parse(tempAnchor.href);\n\n    const samePort = (!parsedUrl.port && loc.port === '') || (parsedUrl.port === loc.port);\n\n    // if cross origin\n    if (parsedUrl.hostname !== loc.hostname || !samePort || parsedUrl.protocol !== loc.protocol)\n    {\n        return 'anonymous';\n    }\n\n    return '';\n}\n", "import { settings } from '../settings';\n\n/**\n * get the resolution / device pixel ratio of an asset by looking for the prefix\n * used by spritesheets and image urls\n * @memberof PIXI.utils\n * @function getResolutionOfUrl\n * @param {string} url - the image path\n * @param {number} [defaultValue=1] - the defaultValue if no filename prefix is set.\n * @returns {number} resolution / device pixel ratio of an asset\n */\nexport function getResolutionOfUrl(url: string, defaultValue?: number): number\n{\n    const resolution = settings.RETINA_PREFIX.exec(url);\n\n    if (resolution)\n    {\n        return parseFloat(resolution[1]);\n    }\n\n    return defaultValue !== undefined ? defaultValue : 1;\n}\n"], "names": ["_parse", "_format", "_resolve", "arguments", "url", "_url"], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;AAKG;AA4EI,IAAM,GAAG,GAAG;AACf,IAAA,KAAK,EAAEA,KAAuB;AAC9B,IAAA,MAAM,EAAEC,MAAyB;AACjC,IAAA,OAAO,EAAEC,OAA2B;;;AClFxC,SAAS,UAAU,CAAC,IAAY,EAAA;AAE5B,IAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;QACI,MAAM,IAAI,SAAS,CAAC,kCAAmC,GAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAG,CAAC,CAAC;AAClF,KAAA;AACL,CAAC;AAED,SAAS,eAAe,CAAC,GAAW,EAAA;IAEhC,IAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE7B,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,YAAY,CAAC,MAAc,EAAA;IAEhC,OAAO,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,IAAY,EAAE,OAAe,EAAA;AAE1D,IAAA,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC;AAED;AACA,SAAS,oBAAoB,CAAC,IAAY,EAAE,cAAuB,EAAA;IAE/D,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAC1B,IAAA,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;IACnB,IAAI,IAAI,GAAG,CAAC,CAAC;AACb,IAAA,IAAI,IAAY,CAAC;AAEjB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EACrC;AACI,QAAA,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EACnB;AACI,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,SAAA;aACI,IAAI,IAAI,KAAK,EAAE,EACpB;YACI,MAAM;AACT,SAAA;AAED,aAAA;YACI,IAAI,GAAG,EAAE,CAAC;AACb,SAAA;QACD,IAAI,IAAI,KAAK,EAAE,EACf;YACI,IAAI,SAAS,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,EACrC,CAEC;iBACI,IAAI,SAAS,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,EAC1C;AACI,gBAAA,IACI,GAAG,CAAC,MAAM,GAAG,CAAC;AACX,uBAAA,iBAAiB,KAAK,CAAC;uBACvB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE;uBACrC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAE5C;AACI,oBAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAClB;wBACI,IAAM,cAAc,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAE5C,wBAAA,IAAI,cAAc,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,EACrC;AACI,4BAAA,IAAI,cAAc,KAAK,CAAC,CAAC,EACzB;gCACI,GAAG,GAAG,EAAE,CAAC;gCACT,iBAAiB,GAAG,CAAC,CAAC;AACzB,6BAAA;AAED,iCAAA;gCACI,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;AACnC,gCAAA,iBAAiB,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC7D,6BAAA;4BACD,SAAS,GAAG,CAAC,CAAC;4BACd,IAAI,GAAG,CAAC,CAAC;4BACT,SAAS;AACZ,yBAAA;AACJ,qBAAA;yBACI,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAC7C;wBACI,GAAG,GAAG,EAAE,CAAC;wBACT,iBAAiB,GAAG,CAAC,CAAC;wBACtB,SAAS,GAAG,CAAC,CAAC;wBACd,IAAI,GAAG,CAAC,CAAC;wBACT,SAAS;AACZ,qBAAA;AACJ,iBAAA;AACD,gBAAA,IAAI,cAAc,EAClB;AACI,oBAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAClB;wBAAE,GAAG,IAAI,KAAK,CAAC;AAAE,qBAAA;AAEjB,yBAAA;wBAAE,GAAG,GAAG,IAAI,CAAC;AAAE,qBAAA;oBACf,iBAAiB,GAAG,CAAC,CAAC;AACzB,iBAAA;AACJ,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAClB;AACI,oBAAA,GAAG,IAAI,GAAA,GAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAG,CAAC;AAC7C,iBAAA;AAED,qBAAA;oBACI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC,iBAAA;AACD,gBAAA,iBAAiB,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;AACzC,aAAA;YACD,SAAS,GAAG,CAAC,CAAC;YACd,IAAI,GAAG,CAAC,CAAC;AACZ,SAAA;aACI,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,CAAC,CAAC,EACnC;AACI,YAAA,EAAE,IAAI,CAAC;AACV,SAAA;AAED,aAAA;YACI,IAAI,GAAG,CAAC,CAAC,CAAC;AACb,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,GAAG,CAAC;AACf,CAAC;AAsBM,IAAM,IAAI,GAAS;AACtB;;;AAGG;AACH,IAAA,OAAO,EAAP,UAAQ,IAAY,EAAA,EAAI,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;AAC7D;;;AAGG;IACH,KAAK,EAAL,UAAM,IAAY,EAAA,EAAI,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACrE;;;AAGG;IACH,SAAS,EAAT,UAAU,IAAY,EAAA;;QAGlB,OAAO,CAAC,wIAAwI;aAC3I,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;AACD;;;;AAIG;IACH,WAAW,EAAX,UAAY,IAAY,EAAA,EAAI,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AAC7E;;;AAGG;IACH,WAAW,EAAX,UAAY,IAAY,EAAA;QAEpB,UAAU,CAAC,IAAI,CAAC,CAAC;AACjB,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,QAAQ,GAAG,EAAE,CAAC;QAElB,IAAM,MAAM,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAM,MAAM,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAM,SAAS,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAE5C,QAAA,IAAI,MAAM,IAAI,MAAM,IAAI,SAAS,EACjC;AACI,YAAA,IAAM,GAAG,GAAG,CAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAG,CAAC,CAAC,MAAI,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAG,CAAC,CAAC,CAAA,KAAI,SAAS,KAAA,IAAA,IAAT,SAAS,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAT,SAAS,CAAG,CAAC,CAAC,CAAA,CAAC;YAEzD,QAAQ,GAAG,GAAG,CAAC;YACf,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACjC,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB;AAED;;;;;;;;AAQG;AACH,IAAA,UAAU,EAAV,UAAW,GAAW,EAAE,aAAsB,EAAE,aAAsB,EAAA;AAElE,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AAAE,YAAA,EAAA,OAAO,GAAG,CAAC,EAAA;QAEpC,IAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,KAAb,IAAA,IAAA,aAAa,cAAb,aAAa,GAAI,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC9F,IAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,KAAb,IAAA,IAAA,aAAa,cAAb,aAAa,GAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEvF,UAAU,CAAC,GAAG,CAAC,CAAC;AAChB,QAAA,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;AAGxB,QAAA,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EACvB;AACI,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,SAAA;QAED,IAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAE1E,QAAA,OAAO,YAAY,CAAC;KACvB;AAED;;;AAGG;IACH,SAAS,EAAT,UAAU,IAAY,EAAA;AAElB,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1B,UAAU,CAAC,IAAI,CAAC,CAAC;AAEjB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAAE,YAAA,EAAA,OAAO,GAAG,CAAC,EAAA;QAElC,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAExC,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAC1B;AACI,YAAA,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtC,SAAA;QAED,IAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;;AAG7C,QAAA,IAAI,GAAG,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAEzC,QAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB;YAAE,EAAA,IAAI,IAAI,GAAG,CAAC,EAAA;AACtD,QAAA,IAAI,UAAU;cAAE,OAAO,GAAA,GAAI,IAAM,CAAC,EAAA;QAElC,OAAO,QAAQ,GAAG,IAAI,CAAC;KAC1B;AAED;;;;AAIG;IACH,UAAU,EAAV,UAAW,IAAY,EAAA;QAEnB,UAAU,CAAC,IAAI,CAAC,CAAC;AACjB,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AAAE,YAAA,EAAA,OAAO,IAAI,CAAC,EAAA;AAExC,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KAC/B;AAED;;;;AAIG;AACH,IAAA,IAAI,EAAJ,YAAA;;AAAA;;QAAK,IAAqB,QAAA,GAAA,EAAA,CAAA;aAArB,IAAqB,EAAA,GAAA,CAAA,EAArB,EAAqB,GAAA,SAAA,CAAA,MAAA,EAArB,EAAqB,EAAA,EAAA;YAArB,QAAqB,CAAA,EAAA,CAAA,GAAAC,WAAA,CAAA,EAAA,CAAA,CAAA;;AAEtB,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EACzB;AAAE,YAAA,OAAO,GAAG,CAAC;AAAE,SAAA;AACf,QAAA,IAAI,MAAM,CAAC;AAEX,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EACxC;AACI,YAAA,IAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAExB,UAAU,CAAC,GAAG,CAAC,CAAC;AAChB,YAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAClB;gBACI,IAAI,MAAM,KAAK,SAAS;oBAAE,EAAA,MAAM,GAAG,GAAG,CAAC,EAAA;AAEvC,qBAAA;oBACI,IAAM,OAAO,GAAG,CAAA,EAAA,GAAA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,EAAE,CAAC;AAEtC,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EACzB;wBACI,MAAM,IAAI,MAAO,GAAA,GAAK,CAAC;AAC1B,qBAAA;AAED,yBAAA;wBACI,MAAM,IAAI,GAAI,GAAA,GAAK,CAAC;AACvB,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;QACD,IAAI,MAAM,KAAK,SAAS,EAAE;AAAE,YAAA,OAAO,GAAG,CAAC;AAAE,SAAA;AAEzC,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACjC;AAED;;;AAGG;IACH,OAAO,EAAP,UAAQ,IAAY,EAAA;QAEhB,UAAU,CAAC,IAAI,CAAC,CAAC;AACjB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAAE,YAAA,EAAA,OAAO,GAAG,CAAC,EAAA;AAClC,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,QAAA,IAAM,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC;AAC5B,QAAA,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACb,IAAI,YAAY,GAAG,IAAI,CAAC;QAExB,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,IAAM,QAAQ,GAAG,IAAI,CAAC;QAEtB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEhC,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EACzC;AACI,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,IAAI,KAAK,EAAE,EACf;gBACI,IAAI,CAAC,YAAY,EACjB;oBACI,GAAG,GAAG,CAAC,CAAC;oBACR,MAAM;AACT,iBAAA;AACJ,aAAA;AAED,iBAAA;;gBAEI,YAAY,GAAG,KAAK,CAAC;AACxB,aAAA;AACJ,SAAA;;;QAID,IAAI,GAAG,KAAK,CAAC,CAAC;cAAE,OAAO,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,EAAA;AACnF,QAAA,IAAI,OAAO,IAAI,GAAG,KAAK,CAAC;AAAE,YAAA,EAAA,OAAO,IAAI,CAAC,EAAA;QAEtC,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;KACrC;AAED;;;AAGG;IACH,QAAQ,EAAR,UAAS,IAAY,EAAA;QAEjB,UAAU,CAAC,IAAI,CAAC,CAAC;AACjB,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,IAAI,GAAG,EAAE,CAAC;AAEd,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YAAE,EAAA,IAAI,GAAG,GAAG,CAAC,EAAA;AAErC,aAAA;AACI,YAAA,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACjC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EACpB;;AAEI,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAE7C,YAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAChB;gBACI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/B,aAAA;;gBACI,EAAA,IAAI,GAAG,IAAI,CAAC,EAAA;AAEjB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAAE,EAAA,IAAI,IAAI,GAAG,CAAC,EAAA;AACxC,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;AACH,IAAA,QAAQ,EAAR,UAAS,IAAY,EAAE,GAAY,EAAA;QAE/B,UAAU,CAAC,IAAI,CAAC,CAAC;AACjB,QAAA,IAAI,GAAG;cAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAA;AAEzB,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,QAAA,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACb,IAAI,YAAY,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAS,CAAC;AAEd,QAAA,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EACpE;YACI,IAAI,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,GAAG,KAAK,IAAI;AAAE,gBAAA,EAAA,OAAO,EAAE,CAAC,EAAA;AAC1D,YAAA,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5B,YAAA,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;AAE1B,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EACrC;gBACI,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAEhC,IAAI,IAAI,KAAK,EAAE,EACf;;;oBAGI,IAAI,CAAC,YAAY,EACjB;AACI,wBAAA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;wBACd,MAAM;AACT,qBAAA;AACJ,iBAAA;AAED,qBAAA;AACI,oBAAA,IAAI,gBAAgB,KAAK,CAAC,CAAC,EAC3B;;;wBAGI,YAAY,GAAG,KAAK,CAAC;AACrB,wBAAA,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,qBAAA;oBACD,IAAI,MAAM,IAAI,CAAC,EACf;;wBAEI,IAAI,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EACnC;AACI,4BAAA,IAAI,EAAE,MAAM,KAAK,CAAC,CAAC,EACnB;;;gCAGI,GAAG,GAAG,CAAC,CAAC;AACX,6BAAA;AACJ,yBAAA;AAED,6BAAA;;;4BAGI,MAAM,GAAG,CAAC,CAAC,CAAC;4BACZ,GAAG,GAAG,gBAAgB,CAAC;AAC1B,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AACJ,aAAA;YAED,IAAI,KAAK,KAAK,GAAG;gBAAE,EAAA,GAAG,GAAG,gBAAgB,CAAC,EAAA;iBAAM,IAAI,GAAG,KAAK,CAAC,CAAC;AAAE,gBAAA,EAAA,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,EAAA;YAElF,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EACrC;YACI,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAC7B;;;gBAGI,IAAI,CAAC,YAAY,EACjB;AACI,oBAAA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;oBACd,MAAM;AACT,iBAAA;AACJ,aAAA;AACI,iBAAA,IAAI,GAAG,KAAK,CAAC,CAAC,EACnB;;;gBAGI,YAAY,GAAG,KAAK,CAAC;AACrB,gBAAA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,aAAA;AACJ,SAAA;QAED,IAAI,GAAG,KAAK,CAAC,CAAC;AAAE,YAAA,EAAA,OAAO,EAAE,CAAC,EAAA;QAE1B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACjC;AAED;;;;;AAKG;IACH,OAAO,EAAP,UAAQ,IAAY,EAAA;QAEhB,UAAU,CAAC,IAAI,CAAC,CAAC;AACjB,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B,QAAA,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACb,IAAI,YAAY,GAAG,IAAI,CAAC;;;QAGxB,IAAI,WAAW,GAAG,CAAC,CAAC;AAEpB,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EACzC;YACI,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,IAAI,KAAK,EAAE,EACf;;;gBAGI,IAAI,CAAC,YAAY,EACjB;AACI,oBAAA,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;oBAClB,MAAM;AACT,iBAAA;gBACD,SAAS;AACZ,aAAA;AACD,YAAA,IAAI,GAAG,KAAK,CAAC,CAAC,EACd;;;gBAGI,YAAY,GAAG,KAAK,CAAC;AACrB,gBAAA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,aAAA;YACD,IAAI,IAAI,KAAK,EAAE,EACf;;gBAEI,IAAI,QAAQ,KAAK,CAAC,CAAC;oBAAE,EAAA,QAAQ,GAAG,CAAC,CAAC,EAAA;qBAC7B,IAAI,WAAW,KAAK,CAAC;oBAAE,EAAA,WAAW,GAAG,CAAC,CAAC,EAAA;AAC/C,aAAA;AACI,iBAAA,IAAI,QAAQ,KAAK,CAAC,CAAC,EACxB;;;gBAGI,WAAW,GAAG,CAAC,CAAC,CAAC;AACpB,aAAA;AACJ,SAAA;QAED,IACI,QAAQ,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;;AAE1B,eAAA,WAAW,KAAK,CAAC;;;AAGjB,eAAA,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,GAAG,GAAG,CAAC,IAAI,QAAQ,KAAK,SAAS,GAAG,CAAC,EAE9E;AACI,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;KACpC;AAED;;;AAGG;IACH,KAAK,EAAL,UAAM,IAAY,EAAA;QAEd,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjB,IAAM,GAAG,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAE/D,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAAE,YAAA,EAAA,OAAO,GAAG,CAAC,EAAA;AAClC,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACzC,QAAA,IAAI,KAAa,CAAC;QAGlB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EACxC;YACI,KAAK,GAAG,CAAC,CAAC;AACb,SAAA;AAED,aAAA;YACI,KAAK,GAAG,CAAC,CAAC;AACb,SAAA;AACD,QAAA,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACb,IAAI,YAAY,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;;;QAIxB,IAAI,WAAW,GAAG,CAAC,CAAC;;AAGpB,QAAA,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC,EACtB;AACI,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,IAAI,KAAK,EAAE,EACf;;;gBAGI,IAAI,CAAC,YAAY,EACjB;AACI,oBAAA,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;oBAClB,MAAM;AACT,iBAAA;gBACD,SAAS;AACZ,aAAA;AACD,YAAA,IAAI,GAAG,KAAK,CAAC,CAAC,EACd;;;gBAGI,YAAY,GAAG,KAAK,CAAC;AACrB,gBAAA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,aAAA;YACD,IAAI,IAAI,KAAK,EAAE,EACf;;gBAEI,IAAI,QAAQ,KAAK,CAAC,CAAC;oBAAE,EAAA,QAAQ,GAAG,CAAC,CAAC,EAAA;qBAC7B,IAAI,WAAW,KAAK,CAAC;oBAAE,EAAA,WAAW,GAAG,CAAC,CAAC,EAAA;AAC/C,aAAA;AACI,iBAAA,IAAI,QAAQ,KAAK,CAAC,CAAC,EACxB;;;gBAGI,WAAW,GAAG,CAAC,CAAC,CAAC;AACpB,aAAA;AACJ,SAAA;QAED,IACI,QAAQ,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;;AAE1B,eAAA,WAAW,KAAK,CAAC;;;AAGjB,eAAA,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,GAAG,GAAG,CAAC,IAAI,QAAQ,KAAK,SAAS,GAAG,CAAC,EAE9E;AACI,YAAA,IAAI,GAAG,KAAK,CAAC,CAAC,EACd;AACI,gBAAA,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU;AAAE,oBAAA,EAAA,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAA;;AACvE,oBAAA,EAAA,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,EAAA;AACzD,aAAA;AACJ,SAAA;AAED,aAAA;AACI,YAAA,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU,EACjC;gBACI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACnC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACjC,aAAA;AAED,iBAAA;gBACI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC3C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AACzC,aAAA;YACD,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AACvC,SAAA;QAED,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAG7B,QAAA,OAAO,GAAG,CAAC;KACd;AAED,IAAA,GAAG,EAAE,GAAG;AACR,IAAA,SAAS,EAAE,GAAG;;;ACrqBlB;;;;;;;;AAQG;AACH,QAAQ,CAAC,aAAa,GAAG,cAAc,CAAC;AAExC;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACH,QAAQ,CAAC,gCAAgC,GAAG,KAAK;;ACpCjD,IAAI,SAAS,GAAG,KAAK,CAAC;AACtB,IAAM,OAAO,GAAG,QAAW,CAAC;AAE5B;;;;AAIG;SACa,SAAS,GAAA;IAErB,SAAS,GAAG,IAAI,CAAC;AACrB,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,QAAQ,CAAC,IAAY,EAAA;;AAEjC,IAAA,IAAI,SAAS,EACb;QACI,OAAO;AACV,KAAA;AAED,IAAA,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAClF;AACI,QAAA,IAAM,IAAI,GAAG;YACT,qBAAsB,GAAA,OAAO,GAAQ,YAAA,GAAA,IAAI,GAAwD,4EAAA;YACjG,qCAAqC;YACrC,qCAAqC;YACrC,qDAAqD;YACrD,qCAAqC;YACrC,qCAAqC;YACrC,qCAAqC;YACrC,kDAAkD;YAClD,kDAAkD;YAClD,kDAAkD,EACrD,CAAC;QAEF,CAAA,EAAA,GAAA,UAAU,CAAC,OAAO,EAAC,GAAG,CAAI,KAAA,CAAA,EAAA,EAAA,IAAI,CAAE,CAAA;AACnC,KAAA;SACI,IAAI,UAAU,CAAC,OAAO,EAC3B;QACI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,SAAU,GAAA,OAAO,GAAM,KAAA,GAAA,IAAI,GAA2B,2BAAA,CAAC,CAAC;AAClF,KAAA;IAED,SAAS,GAAG,IAAI,CAAC;AACrB;;ACpDA,IAAI,SAA8B,CAAC;AAEnC;;;;;AAKG;SACa,gBAAgB,GAAA;AAE5B,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EACpC;QACI,SAAS,GAAG,CAAC,SAAS,SAAS,GAAA;AAE3B,YAAA,IAAM,cAAc,GAAG;AACnB,gBAAA,OAAO,EAAE,IAAI;gBACb,4BAA4B,EAAE,QAAQ,CAAC,gCAAgC;aAC1E,CAAC;YAEF,IACA;AACI,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,wBAAwB,EAAE,EAChD;AACI,oBAAA,OAAO,KAAK,CAAC;AAChB,iBAAA;gBAED,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC/C,IAAI,EAAE,IACF,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,CAAC;uBACvC,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,cAAc,CAAC,CACpC,CAAC;AAE3B,gBAAA,IAAM,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,CAAC;AAE5D,gBAAA,IAAI,EAAE,EACN;oBACI,IAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;AAE1D,oBAAA,IAAI,WAAW,EACf;wBACI,WAAW,CAAC,WAAW,EAAE,CAAC;AAC7B,qBAAA;AACJ,iBAAA;gBAED,EAAE,GAAG,IAAI,CAAC;AAEV,gBAAA,OAAO,OAAO,CAAC;AAClB,aAAA;AACD,YAAA,OAAO,CAAC,EACR;AACI,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;SACJ,GAAG,CAAC;AACR,KAAA;AAED,IAAA,OAAO,SAAS,CAAC;AACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxDA;;;;;;;;;AASG;AACa,SAAA,OAAO,CAAC,GAAW,EAAE,GAAsC,EAAA;AAAtC,IAAA,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAsC,GAAA,EAAA,CAAA,EAAA;AAEvE,IAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC;AACpC,IAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;IACnC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC;AAE5B,IAAA,OAAO,GAAG,CAAC;AACf,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,UAAU,CAAC,GAAW,EAAA;IAElC,IAAI,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAEjC,IAAA,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;IAEpE,OAAO,GAAA,GAAI,SAAW,CAAC;AAC3B,CAAC;AAED;;;;;;;;;;;;;AAaG;AACG,SAAU,UAAU,CAAC,MAAc,EAAA;AAErC,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAC9B;QACI,MAAM,GAAI,aAAyC,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,MAAM,CAAC;AAEpF,QAAA,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EACrB;AACI,YAAA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,OAAO,CAAC,GAA4B,EAAA;AAEhD,IAAA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE;AACjF;;AC9EA;;;;;;AAMG;AACH,SAAS,0BAA0B,GAAA;IAE/B,IAAM,EAAE,GAAG,EAAE,CAAC;IACd,IAAM,GAAG,GAAG,EAAE,CAAC;IAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAC3B;AACI,QAAA,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACV,QAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACd,KAAA;IAED,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;IAChD,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC;IAC1C,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;IAEhD,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC;IACjD,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;IAC3C,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC;IAEjD,IAAM,KAAK,GAAe,EAAE,CAAC;AAE7B,IAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,IAAA,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAEf,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;AAKG;AACU,IAAA,oBAAoB,GAAG,0BAA0B,GAAG;AAEjE;;;;;;;AAOG;AACa,SAAA,gBAAgB,CAAC,SAAiB,EAAE,aAAsB,EAAA;AAEtE,IAAA,OAAO,oBAAoB,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,eAAe,CAC3B,GAA4B,EAC5B,KAAa,EACb,GAAkB,EAClB,WAAqB,EAAA;IAGrB,GAAG,GAAG,GAAG,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AACjC,IAAA,IAAI,WAAW,IAAI,WAAW,KAAK,SAAS,EAC5C;QACI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QACxB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QACxB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC3B,KAAA;AAED,SAAA;QACI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnB,KAAA;AACD,IAAA,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAEf,IAAA,OAAO,GAAG,CAAC;AACf,CAAC;AAED;;;;;;;AAOG;AACa,SAAA,eAAe,CAAC,IAAY,EAAE,KAAa,EAAA;IAEvD,IAAI,KAAK,KAAK,GAAG,EACjB;QACI,OAAO,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC;AACrC,KAAA;IACD,IAAI,KAAK,KAAK,GAAG,EACjB;AACI,QAAA,OAAO,CAAC,CAAC;AACZ,KAAA;IACD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;AAC7B,IAAA,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;AAEtB,IAAA,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAA,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAA,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAE5B,OAAO,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,qBAAqB,CAAC,IAAY,EAAE,KAAa,EAAE,GAAiB,EAAE,WAAqB,EAAA;IAEvG,GAAG,GAAG,GAAG,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AACjC,IAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,KAAK,CAAC;AACvC,IAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC;IACtC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC;AAC/B,IAAA,IAAI,WAAW,IAAI,WAAW,KAAK,SAAS,EAC5C;AACI,QAAA,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;AAChB,QAAA,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;AAChB,QAAA,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;AACnB,KAAA;AACD,IAAA,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAEf,IAAA,OAAO,GAAG,CAAC;AACf;;AClJA;;;;;;;AAOG;AACa,SAAA,qBAAqB,CAAC,IAAY,EAAE,SAA2C,EAAA;AAA3C,IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2C,GAAA,IAAA,CAAA,EAAA;;AAG3F,IAAA,IAAM,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;IAE9B,SAAS,GAAG,SAAS,IAAI,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC;AAEvD,IAAA,IAAI,SAAS,CAAC,MAAM,KAAK,YAAY,EACrC;QACI,MAAM,IAAI,KAAK,CAAC,sCAAuC,GAAA,SAAS,CAAC,MAAM,GAAA,gBAAA,GAAiB,YAAc,CAAC,CAAC;AAC3G,KAAA;;IAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EACvD;QACI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,KAAA;AAED,IAAA,OAAO,SAAS,CAAC;AACrB;;AC9BM,SAAU,aAAa,CACzB,KAAkB,EAAA;AAGlB,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,CAAC,EACjC;QACI,IAAI,KAAK,YAAY,YAAY,EACjC;AACI,YAAA,OAAO,cAAc,CAAC;AACzB,SAAA;aACI,IAAI,KAAK,YAAY,WAAW,EACrC;AACI,YAAA,OAAO,aAAa,CAAC;AACxB,SAAA;AAED,QAAA,OAAO,YAAY,CAAC;AACvB,KAAA;AACI,SAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,CAAC,EACtC;QACI,IAAI,KAAK,YAAY,WAAW,EAChC;AACI,YAAA,OAAO,aAAa,CAAC;AACxB,SAAA;AACJ,KAAA;AACI,SAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,CAAC,EACtC;QACI,IAAI,KAAK,YAAY,UAAU,EAC/B;AACI,YAAA,OAAO,YAAY,CAAC;AACvB,SAAA;AACJ,KAAA;;AAGD,IAAA,OAAO,IAAI,CAAC;AAChB;;AClCA;AACA,IAAM,GAAG,GAAG,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;AAIrG,SAAA,qBAAqB,CAAC,MAAqB,EAAE,KAAe,EAAA;IAExE,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAM,KAAK,GAAiC,EAAE,CAAC;AAE/C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EACtC;AACI,QAAA,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC/B,KAAA;IAED,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IAE5C,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EACtC;AACI,QAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAExB;;;AAGG;AACH,QAAA,IAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAqB,CAAC;AAEtD,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAChB;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAElB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;AACI,YAAA,IAAM,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,IAAI,YAAY,CAAC;AAC5D,YAAA,IAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;YAEvB,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtC,SAAA;QAED,YAAY,IAAI,IAAI,CAAC;AACxB,KAAA;AAED,IAAA,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC;;ACtDA;AAEA;;;;;;AAMG;AACG,SAAU,QAAQ,CAAC,CAAS,EAAA;AAE9B,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,IAAA,EAAE,CAAC,CAAC;AACJ,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACb,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACb,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACb,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACb,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAEd,OAAO,CAAC,GAAG,CAAC,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACG,SAAU,MAAM,CAAC,CAAS,EAAA;AAE5B,IAAA,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAED;;;;;;AAMG;AACG,SAAU,IAAI,CAAC,CAAS,EAAA;AAE1B,IAAA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAElC,CAAC,MAAM,CAAC,CAAC;AAET,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAEpC,CAAC,MAAM,KAAK,CAAC;IAAC,CAAC,IAAI,KAAK,CAAC;AACzB,IAAA,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC,MAAM,KAAK,CAAC;IAAC,CAAC,IAAI,KAAK,CAAC;AACzB,IAAA,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC,MAAM,KAAK,CAAC;IAAC,CAAC,IAAI,KAAK,CAAC;AAEzB,IAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxB;;ACxDA;;;;;;;AAOG;SACa,WAAW,CAAC,GAAU,EAAE,QAAgB,EAAE,WAAmB,EAAA;AAEzE,IAAA,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AAC1B,IAAA,IAAI,CAAC,CAAC;AAEN,IAAA,IAAI,QAAQ,IAAI,MAAM,IAAI,WAAW,KAAK,CAAC,EAC3C;QACI,OAAO;AACV,KAAA;AAED,IAAA,WAAW,IAAI,QAAQ,GAAG,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC;AAElF,IAAA,IAAM,GAAG,GAAG,MAAM,GAAG,WAAW,CAAC;IAEjC,KAAK,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAC/B;QACI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;AACjC,KAAA;AAED,IAAA,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC;AACrB;;AC5BA;;;;;;AAMG;AACG,SAAU,IAAI,CAAC,CAAS,EAAA;IAE1B,IAAI,CAAC,KAAK,CAAC;AAAE,QAAA,EAAA,OAAO,CAAC,CAAC,EAAA;AAEtB,IAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B;;ACZA,IAAI,OAAO,GAAG,CAAC,CAAC;AAEhB;;;;;AAKG;SACa,GAAG,GAAA;IAEf,OAAO,EAAE,OAAO,CAAC;AACrB;;ACTA;AACA,IAAM,QAAQ,GAAkB,EAAE,CAAC;AAEnC;;;;;;;;;;AAUG;SACa,WAAW,CAAC,OAAe,EAAE,OAAe,EAAE,WAAe,EAAA;AAAf,IAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAe,GAAA,CAAA,CAAA,EAAA;;AAGzE,IAAA,IAAI,QAAQ,CAAC,OAAO,CAAC,EACrB;QACI,OAAO;AACV,KAAA;;AAGD,IAAA,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC;;AAG9B,IAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAChC;QACI,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAK,OAAO,GAAA,sBAAA,GAAuB,OAAS,CAAC,CAAC;AAC5F,KAAA;AAED,SAAA;;AAEI,QAAA,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,OAAO,CAAC,cAAc,EAC1B;AACI,YAAA,OAAO,CAAC,cAAc,CAClB,oCAAoC,EACpC,kCAAkC,EAClC,qDAAqD,EAClD,OAAO,GAAuB,sBAAA,GAAA,OAAS,CAC7C,CAAC;AACF,YAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACtB,SAAA;AAED,aAAA;YACI,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAK,OAAO,GAAA,sBAAA,GAAuB,OAAS,CAAC,CAAC;AACzF,YAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB,SAAA;AACJ,KAAA;;AAGD,IAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AAC7B;;ACvDA;;;;;;AAMG;AACU,IAAA,YAAY,GAA6B,GAAG;AAEzD;;;;;;AAMG;AACI,IAAM,YAAY,GAA6B,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;AAE1E;;;;;;AAMG;AACI,IAAM,gBAAgB,GAAiC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;AAElF;;;;AAIG;SACa,mBAAmB,GAAA;AAE/B,IAAA,IAAI,GAAG,CAAC;IAER,KAAK,GAAG,IAAI,YAAY,EACxB;AACI,QAAA,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;AAC/B,KAAA;IACD,KAAK,GAAG,IAAI,gBAAgB,EAC5B;AACI,QAAA,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;AACnC,KAAA;AACL,CAAC;AAED;;;;AAIG;SACa,iBAAiB,GAAA;AAE7B,IAAA,IAAI,GAAG,CAAC;IAER,KAAK,GAAG,IAAI,YAAY,EACxB;AACI,QAAA,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5B,KAAA;IACD,KAAK,GAAG,IAAI,gBAAgB,EAC5B;AACI,QAAA,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAChC,KAAA;AACL;;AC/DA;;;;AAIG;AACH,IAAA,kBAAA,kBAAA,YAAA;AAcI;;;;AAIG;AACH,IAAA,SAAA,kBAAA,CAAY,KAAa,EAAE,MAAc,EAAE,UAAmB,EAAA;QAE1D,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;QAE9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC;AAEpD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;KAC9B;AAED;;;AAGG;AACH,IAAA,kBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KACvE,CAAA;AAED;;;;AAIG;AACH,IAAA,kBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAO,YAAoB,EAAE,aAAqB,EAAA;AAE9C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;KACpE,CAAA;;AAGD,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,kBAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AAJT;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;SAC5B;AAED,QAAA,GAAA,EAAA,UAAU,GAAW,EAAA;YAEjB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACvC;;;AALA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,kBAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAJV;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;SAC7B;AAED,QAAA,GAAA,EAAA,UAAW,GAAW,EAAA;YAElB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxC;;;AALA,KAAA,CAAA,CAAA;IAML,OAAC,kBAAA,CAAA;AAAD,CAAC,EAAA;;ACpFD;;;;;;AAMG;AACG,SAAU,UAAU,CAAC,MAAyB,EAAA;;AAIhD,IAAA,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACzB,IAAA,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAE3B,IAAA,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE;AACpC,QAAA,kBAAkB,EAAE,IAAI;AACS,KAAA,CAAC,CAAC;AACvC,IAAA,IAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5D,IAAA,IAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC;AAC9B,IAAA,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AAE1B,IAAA,IAAM,KAAK,GAAU;AACjB,QAAA,GAAG,EAAE,IAAI;AACT,QAAA,IAAI,EAAE,IAAI;AACV,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,MAAM,EAAE,IAAI;KACf,CAAC;IACF,IAAI,IAAI,GAAG,IAAI,CAAC;AAChB,IAAA,IAAI,CAAC,CAAC;AACN,IAAA,IAAI,CAAC,CAAC;AACN,IAAA,IAAI,CAAC,CAAC;IAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAC3B;QACI,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EACvB;YACI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;AACpB,YAAA,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC;AAExB,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,EACtB;AACI,gBAAA,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACjB,aAAA;AAED,YAAA,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EACvB;AACI,gBAAA,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,aAAA;AACI,iBAAA,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EACvB;AACI,gBAAA,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,aAAA;AAED,YAAA,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,EACxB;AACI,gBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,aAAA;AACI,iBAAA,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,EACxB;AACI,gBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,aAAA;AAED,YAAA,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,EACzB;AACI,gBAAA,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,aAAA;AACI,iBAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EACzB;AACI,gBAAA,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,aAAA;AACJ,SAAA;AACJ,KAAA;AAED,IAAA,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,EACtB;QACI,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACjC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACtC,QAAA,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACrE,KAAA;IAED,OAAO;AACH,QAAA,MAAM,EAAA,MAAA;AACN,QAAA,KAAK,EAAA,KAAA;AACL,QAAA,IAAI,EAAA,IAAA;KACP,CAAC;AACN;;AC7FA;;;;;;;AAOG;AACU,IAAA,QAAQ,GAAG;;ACGxB;;;AAGG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;AAGG;AAEH;;;;AAIG;AAEH;;;;AAIG;AAEH;;;;;;;AAOG;AACG,SAAU,gBAAgB,CAAC,OAAe,EAAA;IAE5C,IAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAE5C,IAAA,IAAI,YAAY,EAChB;QACI,OAAO;AACH,YAAA,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS;AACtE,YAAA,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS;AACpE,YAAA,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS;AACpE,YAAA,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS;AACrE,YAAA,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;SACxB,CAAC;AACL,KAAA;AAED,IAAA,OAAO,SAAS,CAAC;AACrB;;ACnEA,IAAI,UAAyC,CAAC;AAE9C;;;;;;;;;AASG;AACa,SAAA,oBAAoB,CAACC,KAAW,EAAE,GAAmC,EAAA;AAAnC,IAAA,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAA,GAAgB,UAAU,CAAC,QAAQ,CAAA,EAAA;;IAGjF,IAAIA,KAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAC9B;AACI,QAAA,OAAO,EAAE,CAAC;AACb,KAAA;;AAGD,IAAA,GAAG,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC;IAEjC,IAAI,CAAC,UAAU,EACf;AACI,QAAA,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC5C,KAAA;;;;AAKD,IAAA,UAAU,CAAC,IAAI,GAAGA,KAAG,CAAC;IACtB,IAAM,SAAS,GAAGC,GAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAM,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,MAAM,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;;AAGvF,IAAA,IAAI,SAAS,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,EAC3F;AACI,QAAA,OAAO,WAAW,CAAC;AACtB,KAAA;AAED,IAAA,OAAO,EAAE,CAAC;AACd;;AC3CA;;;;;;;;AAQG;AACa,SAAA,kBAAkB,CAAC,GAAW,EAAE,YAAqB,EAAA;IAEjE,IAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAEpD,IAAA,IAAI,UAAU,EACd;AACI,QAAA,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,KAAA;IAED,OAAO,YAAY,KAAK,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC;AACzD;;;;"}