[1]   warnings.warn(
[1]
[1] [后端错误] INFO:__main__:✅ 智能离线配置已应用
[1] INFO:__main__:📁 保持工作目录: H:\AI\CosyVoice
[1] INFO:__main__:📦 已添加模块搜索路径: H:\AI\CosyVoice\new
[1] INFO:__main__:🌐 启动支持原生WebSocket的FastAPI服务器...
[1]
[1] [后端错误] INFO:__main__:🚀 FastAPI服务器已启动，PID: 47908
[1] INFO:__main__:💡 生产模式：已禁用自动重载，避免僵尸进程
[1]
[1] 🔍 立即检查后端服务状态...
[1] ⚠️ 后端服务检查失败: fetch failed
[1] 📡 后端服务启动中，请等待...
[1] [后端错误] INFO:__main__:✅ FastAPI服务器进程启动成功
[1] INFO:__main__:🏥 等待服务完全启动并通过健康检查...
[1] INFO:__main__:🏥 开始健康检查，最多等待 120 秒...
[1]
[1] [69528:0724/204822.091:ERROR:CONSOLE:1] "Request Autofill.enable failed. {"code":-32601,"message":"'Autofill.enable' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[1] [69528:0724/204822.091:ERROR:CONSOLE:1] "Request Autofill.setAddresses failed. {"code":-32601,"message":"'Autofill.setAddresses' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[1] [后端错误] INFO:__main__:🔄 连接中... 已等待 2 秒
[1]
[1] [后端错误] 2025-07-24 20:48:23,842 - httpx - INFO - HTTP Request: GET https://api.gradio.app/gradio-messaging/en "HTTP/1.1 200 OK"
[1]
[1] [后端错误] 2025-07-24 20:48:25,695 - backend_stability_fix - INFO - 🔧 资源管理器修复已初始化
[1] 2025-07-24 20:48:25,696 - backend_stability_fix - INFO - 🚀 后端稳定性管理器已启动
[1] 2025-07-24 20:48:25,696 - core.model_manager - INFO - ✅ 稳定性修复模块已加载
[1]
[1] [后端错误] 2025-07-24 20:48:25,831 - modelscope - INFO - PyTorch version 2.7.0+cu128 Found.
[1]
[1] [后端错误] 2025-07-24 20:48:25,832 - modelscope - INFO - Loading ast index from H:\AI\CosyVoice\new\data\cache\modelscope\ast_indexer
[1]
[1] [后端错误] 2025-07-24 20:48:25,932 - modelscope - INFO - Loading done! Current index file version is 1.15.0, with md5 505082b8f06222c04baf834d50621ddc and a total number of 980 components indexed
[1]
[1] [后端错误] H:\AI\CosyVoice\env\Lib\site-packages\transformers\utils\hub.py:124: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
[1]   warnings.warn(
[1]
[0] 20:48:26 [vite] http proxy error: /api/voice/profiles
[0] AggregateError [ECONNREFUSED]:
[0]     at internalConnectMultiple (node:net:1139:18)
[0]     at afterConnectMultiple (node:net:1714:7)
[1] 🔍 延长等待后开始检查后端服务状态...
[1] ⚠️ 后端服务检查失败: fetch failed
[1] 📡 后端服务启动中，请等待...
[1] ⚠️ 后端服务检查失败: 第1次尝试 (最多15次)
[1] 📥 Store GET [protagonist-avatars]: object
[1] 📥 Store GET [current-story-config]: object
[1] 📥 Store GET [current-selected-style]: object
[1] [后端错误] INFO:__main__:🔄 连接中... 已等待 9 秒
[1]
[1] [后端错误] 2025-07-24 20:48:31,503 - root - INFO - 文本预处理器初始化完成
[1]
[1] [后端错误] 2025-07-24 20:48:32,137 - root - INFO - 性能监控线程已启动
[1]
[1] [后端错误] 2025-07-24 20:48:32,165 - root - INFO - 模型管理器初始化完成
[1]
[1] [后端错误] 2025-07-24 20:48:32,196 - root - INFO - ✅ Silero VAD模块检测成功
[1]
[1] [后端错误] H:\AI\CosyVoice\env\Lib\site-packages\pydantic\_internal\_fields.py:160: UserWarning: Field "model_id" has conflict with protected namespace "model_".
[1]
[1] You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
[1]   warnings.warn(
[1]
[1] [后端错误] INFO:     Started server process [47908]
[1] INFO:     Waiting for application startup.
[1] 2025-07-24 20:48:32,273 - api_bridge - INFO - 🚀 正在启动API桥接服务...
[1] 2025-07-24 20:48:32,273 - webui5_enhanced - INFO - ============================================================
[1]
[1] [后端错误] 2025-07-24 20:48:32,273 - webui5_enhanced - INFO - 🚀 启动 CosyVoice WebUI5 Enhanced - 藏识仙灵AI系统 2.0
[1] 2025-07-24 20:48:32,273 - webui5_enhanced - INFO - ============================================================
[1]
[1] [后端错误] 2025-07-24 20:48:32,274 - root - INFO - 数据库初始化完成
[1] 2025-07-24 20:48:32,274 - root - INFO - 对话历史管理器初始化完成
[1]
[1] [后端错误] 2025-07-24 20:48:32,275 - root - INFO - LLM参数预设管理器初始化完成
[1]
[1] [后端错误] 2025-07-24 20:48:32,276 - root - WARNING - 未找到可用的音频库，音乐播放功能将被禁用
[1] 2025-07-24 20:48:32,276 - root - INFO - 播放列表已刷新，共 0 首音乐
[1] 2025-07-24 20:48:32,276 - root - INFO - 音乐播放器初始化完成
[1]
[1] [后端错误] 2025-07-24 20:48:32,276 - integrations.indextts_manager - INFO - 🔗 建立IndexTTS持久连接池...
[1]
[1] [后端错误] 2025-07-24 20:48:32,279 - integrations.indextts_manager - INFO - ✅ 持久连接池建立中...
[1]
[1] [后端错误] 2025-07-24 20:48:32,279 - integrations.indextts_manager - INFO - 🚀 IndexTTS终极HTTP优化已启用 - 超激进连接保持
[1] 2025-07-24 20:48:32,279 - integrations.indextts_manager - INFO - 🎯 IndexTTS管理器初始化完成
[1]
[1] [后端错误] Building prefix dict from the default dictionary ...
[1] 2025-07-24 20:48:32,279 - jieba - DEBUG - Building prefix dict from the default dictionary ...
[1] Loading model from cache C:\Users\<USER>\AppData\Local\Temp\jieba.cache
[1] 2025-07-24 20:48:32,279 - jieba - DEBUG - Loading model from cache C:\Users\<USER>\AppData\Local\Temp\jieba.cache
[1]
[1] [后端错误] Loading model cost 0.857 seconds.
[1] 2025-07-24 20:48:33,136 - jieba - DEBUG - Loading model cost 0.857 seconds.
[1] Prefix dict has been built successfully.
[1] 2025-07-24 20:48:33,136 - jieba - DEBUG - Prefix dict has been built successfully.
[1]
[1] [后端错误] 2025-07-24 20:48:33,136 - utils.asr_postprocessor - INFO - 🧹 ASR后处理器初始化完成
[1] 2025-07-24 20:48:33,136 - utils.asr_postprocessor - INFO -    - 同音字纠错词典: 94 条
[1] 2025-07-24 20:48:33,136 - utils.asr_postprocessor - INFO -    - 上下文模式: 3 个
[1] 2025-07-24 20:48:33,137 - utils.asr_postprocessor - INFO -    - 专业术语: 20 个
[1] 2025-07-24 20:48:33,137 - integrations.indextts_manager - INFO - ✅ IndexTTS ASR后处理器已启用
[1] 2025-07-24 20:48:33,137 - integrations.indextts_enhanced_error_handler - INFO - 🛡️ IndexTTS错误处理器已初始化
[1] 2025-07-24 20:48:33,137 - integrations.indextts_manager - INFO - 🛡️ 增强错误处理器已启用
[1] 2025-07-24 20:48:33,137 - integrations.indextts_manager - INFO - 🎙️ VAD系统配置已初始化
[1] 2025-07-24 20:48:33,137 - integrations.indextts_manager - INFO -    - Silero配置: threshold=0.3
[1] 2025-07-24 20:48:33,137 - integrations.indextts_manager - INFO -    - 重复检测配置: threshold=0.999
[1] 2025-07-24 20:48:33,137 - integrations.indextts_manager - INFO - 🧪 测试Silero VAD可用性...
[1]
[1] [后端错误] 2025-07-24 20:48:34,850 - integrations.indextts_manager - INFO - 🔄 IndexTTS超级保活线程已启动
[1]
[1] [后端错误] Using cache found in /tmp\snakers4_silero-vad_master
[1]
[1] [后端错误] 2025-07-24 20:48:35,042 - integrations.indextts_manager - INFO - ✅ Silero VAD测试成功
[1]
[1] [后端错误] 2025-07-24 20:48:35,043 - integrations.indextts_manager - INFO - ✅ VAD系统可用性测试通过: silero
[1]
[1] [后端错误] 2025-07-24 20:48:35,045 - integrations.indextts_manager - INFO - 🗂️ 模型缓存目录设置为: H:\AI\CosyVoice\new\new\data\models
[1] 2025-07-24 20:48:35,045 - integrations.indextts_manager - INFO - 🚀 尝试现场加载Silero VAD...
[1] 2025-07-24 20:48:35,045 - integrations.indextts_manager - INFO - 📥 开始下载Silero VAD模型...
[1]
[1] 📡 后端服务启动中，请等待... (模型加载可能需要较长时间)
[1] ⚠️ 后端服务检查失败: fetch failed
[1] 📡 后端服务启动中，请等待...
[1] ⚠️ 后端服务检查失败: 第2次尝试 (最多15次)
[1] [后端错误] Using cache found in H:\AI\CosyVoice\new\new\data\models\snakers4_silero-vad_master
[1]
[1] [后端错误] 2025-07-24 20:48:37,299 - integrations.indextts_manager - INFO - 🔧 Silero VAD配置: window_size_samples=512, threshold=0.3
[1] 2025-07-24 20:48:37,299 - integrations.indextts_manager - INFO - ✅ Silero VAD现场加载成功
[1] 2025-07-24 20:48:37,299 - integrations.indextts_manager - INFO - ✅ VAD系统强制初始化成功: silero
[1] 2025-07-24 20:48:37,299 - integrations.indextts_manager - INFO - ✅ VAD已成功初始化为Silero类型，使用现有模型
[1]
[1] [后端错误] 2025-07-24 20:48:37,300 - data.character_presets - INFO - 正在从目录加载独立角色文件: H:\AI\CosyVoice\new\data\characters
[1]
[1] [后端错误] 2025-07-24 20:48:37,300 - data.character_presets - INFO - 已从独立文件加载角色: 周易取名大师
[1] 2025-07-24 20:48:37,300 - data.character_presets - INFO - 已从独立文件加载角色: 哪托
[1]
[1] [后端错误] 2025-07-24 20:48:37,300 - data.character_presets - INFO - 已从独立文件加载角色: 藏识仙灵
[1]
[1] [后端错误] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 角色 周易取名大师 在独立文件中存在，跳过批量文件版本
[1] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 角色 哪托 在独立文件中存在，跳过批量文件版本
[1] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 角色 藏识仙灵 在独立文件中存在，跳过批量文件版本
[1] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 已从批量文件加载角色: 通用助手
[1] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 已从批量文件加载角色: 俏皮女店员小莉
[1] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 已从批量文件加载角色: 技术专家
[1] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 已从批量文件加载角色: 创意写手
[1] 2025-07-24 20:48:37,301 - data.character_presets - INFO - 已从批量文件加载角色: 学习导师
[1] 2025-07-24 20:48:37,302 - data.character_presets - INFO - 角色预设加载完成，共 8 个角色
[1]
[1] [后端错误] 2025-07-24 20:48:37,303 - data.character_presets - INFO - 角色预设已保存: 8 个角色
[1] 2025-07-24 20:48:37,303 - data.character_presets - INFO - 角色设定管理器初始化完成，已加载 8 个角色
[1] 2025-07-24 20:48:37,304 - webui5_enhanced - INFO - ⏩ 跳过CosyVoice TTS预加载，使用IndexTTS
[1]
[1] [后端错误] 2025-07-24 20:48:37,304 - webui5_enhanced - INFO - 🎯 WebUI5 Enhanced 初始化完成
[1] 2025-07-24 20:48:37,304 - utils.asr_postprocessor - INFO - 🧹 ASR后处理器初始化完成
[1] 2025-07-24 20:48:37,304 - utils.asr_postprocessor - INFO -    - 同音字纠错词典: 94 条
[1] 2025-07-24 20:48:37,304 - utils.asr_postprocessor - INFO -    - 上下文模式: 3 个
[1] 2025-07-24 20:48:37,304 - utils.asr_postprocessor - INFO -    - 专业术语: 20 个
[1] 2025-07-24 20:48:37,304 - webui5_enhanced - INFO - ✅ WebUI ASR后处理器已启用
[1] 2025-07-24 20:48:37,304 - api_bridge - INFO - ✅ WebUI实例初始化成功
[1] 2025-07-24 20:48:37,304 - api_bridge - INFO - ⚠️ 未找到模型管理器，跳过模型状态检查
[1]
[1] [后端错误] 2025-07-24 20:48:37,304 - api_bridge - INFO - 🧪 WebSocket集成测试消息已发送
[1] 2025-07-24 20:48:37,304 - api_bridge - INFO - 🎉 应用启动完成，状态更新为ready
[1] 2025-07-24 20:48:37,304 - api_bridge - INFO - 🌐 API桥接服务启动完成
[1] INFO:     Application startup complete.
[1]
[1] [后端错误] INFO:     Uvicorn running on http://0.0.0.0:7860 (Press CTRL+C to quit)
[1]
[1] [后端] failed to import ttsfrd, use WeTextProcessing instead
[1] 🚀 应用性能优化配置
[1] 🚀 WebSocket后台任务已启动
[1] INFO:     127.0.0.1:49625 - "GET /api/health HTTP/1.1" 200 OK
[1]
[1] [后端错误] INFO:__main__:✅ 后端服务健康检查通过！
[1]
[1] [后端错误] INFO:__main__:🎉 服务器已完全启动并可以响应请求
[1] INFO:__main__:🔓 现在可以安全释放服务器锁
[1]
[1] [后端错误] INFO:__main__:🔓 服务器锁已释放: H:\AI\CosyVoice\server.lock
[1] INFO:__main__:✅ 启动完成，脚本退出
[1]
[1] [后端] 本地目录结构已创建
[1] 模型缓存已重定向到项目内，API功能保留
[1] 本地模型状态:
[1]   [√] whisper_base: H:\AI\CosyVoice\new\data\models\base.pt
[1]   [X] whisper_large: H:\AI\CosyVoice\new\data\models\large-v3.pt (缺失)
[1] 应用智能离线配置...
[1] ModelScope已智能修补（保留其他功能）
[1] 部署指南已创建: OFFLINE_DEPLOYMENT.md
[1] 智能离线配置完成！
[1] 现在可以正常使用本地API，同时避免在线模型下载
[1]
[1] 📡 后端服务启动中，请等待... (模型加载可能需要较长时间)
[1] [后端] INFO:     127.0.0.1:49676 - "GET /api/health HTTP/1.1" 200 OK
[1]
[1] 🔍 [DEBUG] 健康检查响应数据: {
[1]   "success": true,
[1]   "data": {
[1]     "status": "healthy",
[1]     "timestamp": "2025-07-24T20:48:46.680368",
[1]     "webui_available": true,
[1]     "api_version": "1.0",
[1]     "startup_state": "ready",
[1]     "startup_elapsed": 14.482109785079956,
[1]     "socketio_available": false,
[1]     "socketio_connections": 0,
[1]     "websocket_connections": 0,
[1]     "webui_status": "ok"
[1]   },
[1]   "message": "Success"
[1] }
[1] 🔍 [DEBUG] 提取的status值: healthy
[1] ✅ 后端API服务正常运行
[1] ✅ 后端服务检查成功
[1] [后端错误] 2025-07-24 20:48:52,741 - api_bridge - WARNING - ⚠️ 没有活跃的会话需要停止
[1]
[1] [后端] INFO:     127.0.0.1:49685 - "POST /api/realtime/stop HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 20:48:52,965 - api_bridge - INFO - 🚨 [API] start_realtime_dialogue API被调用
[1] 2025-07-24 20:48:52,965 - api_bridge - INFO - 🚨 [API] 接收到的参数: disableVAD=True, mode=oracle
[1] 2025-07-24 20:48:52,965 - api_bridge - INFO - 🚨 [API] webui实例获取成功
[1]
[1] [后端错误] 2025-07-24 20:48:52,966 - api_bridge - INFO - ✅ 新实时对话会话已创建: session_1753361332
[1]
[1] [后端错误] 2025-07-24 20:48:52,966 - api_bridge - INFO - 🎤 启动实时对话 [会话ID: session_1753361332] - 合成模式: user-voice, 禁用VAD: True
[1]
[1] [后端错误] 2025-07-24 20:48:52,966 - api_bridge - INFO - 🎵 TTS配置: {'mode': 'user-voice', 'audioPathsText': None, 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 20:48:52,966 - api_bridge - INFO - 🤖 LLM配置: characterName=藏识仙灵, systemPrompt前50字符=你是一位深谙周易玄学的神谕大师，拥有深厚的易学功底和人生智慧。以古雅而亲切的语调，结合周易哲学为求问...
[1] 2025-07-24 20:48:52,966 - api_bridge - INFO - 🎭 获取到角色头像: 藏识仙灵 ->
[1] 2025-07-24 20:48:52,966 - api_bridge - INFO - 🚨 [API] 准备调用 webui.indextts_manager.start_realtime_conversation
[1] 2025-07-24 20:48:52,966 - api_bridge - INFO - 🚨 [API] disable_vad参数值: True
[1]
[1] [后端错误] 2025-07-24 20:48:52,967 - integrations.indextts_manager - INFO - 🔇 VAD已禁用，跳过音频库检查（TTS-only模式）
[1]
[1] [后端错误] 2025-07-24 20:48:52,971 - integrations.indextts_manager - INFO - 🎤 启动IndexTTS实时对话... (VAD禁用: True)
[1] 2025-07-24 20:48:52,971 - integrations.indextts_manager - INFO - 🔍 音频库状态检查: AUDIO_LIBS_AVAILABLE=True, SILERO_AVAILABLE=True, WEBRTC_AVAILABLE=True
[1]
[1] [后端错误] 2025-07-24 20:48:52,971 - integrations.indextts_manager - INFO - 🤖 LLM角色配置: character=藏识仙灵, systemPrompt前50字符=你是一位深谙周易玄学的神谕大师，拥有深厚的易学功底和人生智慧。以古雅而亲切的语调，结合周易哲学为求问...
[1] 2025-07-24 20:48:52,971 - integrations.indextts_manager - INFO - 🎵 TTS语音配置: mode=oracle, tts_config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 20:48:52,971 - integrations.indextts_manager - INFO - 🔧 VAD控制: 禁用
[1]
[1] [后端错误] 2025-07-24 20:48:52,972 - chat_engine - INFO - 🌐 ChatEngine HTTP会话池已优化 - 低延迟配置
[1] 2025-07-24 20:48:52,972 - root - WARNING - 未找到系统提示词文件: CSXL\藏识仙灵.txt
[1]
[1] [后端错误] 2025-07-24 20:48:55,023 - root - INFO - 发现Ollama模型: ['gemma3:12b']
[1]
[1] [后端错误] 2025-07-24 20:48:57,076 - root - INFO - 发现LMstudio模型: ['csxl0.6', 'qwen3-235b-a22b-instruct-2507', 'text-embedding-nomic-embed-text-v1.5', 'text-embedding-qwen3-embedding-0.6b', 'deepseek-r1-0528-qwen3-8b', 'devstral-small-2507', 'hunyuan-a13b-instruct', 'sicariussicariistuff_impish_llama_4b', 'deepswe-preview', 'ernie-4.5-0.3b-pt', 'dreamgen_lucid-v1-nemo', 'rwkv7-goose-world3-2.9b-hf', 'qwen3-53b-a3b-total-recall-v1.4-128k', 'thudm_glm-z1-32b-0414', 'mistralai/mistral-nemo-instruct-2407', 'unsloth/mistral-nemo-instruct-2407', 'gemma-3n-e4b-it', 'jan-nano-128k', 'dots.llm1.inst', 'unsloth/qwen3-30b-a3b', 'glm-4-32b-0414', 'polaris-4b-preview', 'polaris-project_polaris-4b-preview', 'kimi-dev-72b@q3_k_xl', 'kimi-dev-72b@q5_k_xl', 'devstral-small-2505', 'qwen3-32b', 'mistral-small-3.2-24b-instruct-2506@q4_k_xl', 'mistral-small-3.2-24b-instruct-2506@q8_k_xl', 'qwq-32b', 'magistral-small-2506', 'mistral-small-3.1-24b-instruct-2503', 'qwen2.5-coder-0.5b-instruct', 'llama-4-scout-17b-16e-instruct', 'qwen3-235b-a22b', 'jan-nano', 'qwen2.5-coder-32b-instruct', 'monkeyocr-recognition', 'midnight-miqu-70b-v1.0', 'qwq-32b-abliterated-yarn', 'qwen3-8b', 'qwen3-32b-ud', 'qwen3-30b-a3b-ud', 'qwen3-30b-a3b-128k', 'qwen3-14b-ud', 'glm-4-9b-0414-ud', 'glm-4-32b-0414-ud', 'gemma-3-4b-it', 'qwen3-30b-a3b-gguf/qwen3-30b-a3b', 'internlm3-8b-instruct', 'gemma-3-27b-it.gguf', 'gemma-3-12b-it.gguf', 'ui-tars-7b-dpo']
[1]
[1] [后端错误] 2025-07-24 20:48:57,128 - root - INFO - 🌐 连接预热成功: http://localhost:11434 (0.051秒, 5/5成功)
[1]
[1] [后端错误] 2025-07-24 20:48:57,146 - root - INFO - 🌐 连接预热成功: http://localhost:1234 (0.017秒, 5/5成功)
[1] 2025-07-24 20:48:57,149 - root - INFO - 🔄 连接保活线程已启动
[1] 2025-07-24 20:48:57,149 - chat_engine - INFO - 🌐 常用连接预热完成
[1] 2025-07-24 20:48:57,150 - chat_engine - INFO - 对话引擎初始化完成 - 默认提供者: lmstudio, 默认模型: gemma-3-4b-it
[1] 2025-07-24 20:48:57,150 - root - INFO - Chat引擎加载完成，耗时: 4.18秒
[1] 2025-07-24 20:48:57,150 - root - INFO - 对话历史已清空
[1] 2025-07-24 20:48:57,150 - integrations.indextts_manager - INFO - 🔄 LLM对话历史已清空
[1] 2025-07-24 20:48:57,150 - integrations.indextts_manager - INFO - 🤖 AI配置已合并: provider=lmstudio, model_name=gemma3
[1] 2025-07-24 20:48:57,150 - integrations.indextts_manager - INFO - ✅ TTS将使用用户音色: 21
[1] 2025-07-24 20:48:57,150 - integrations.indextts_manager - INFO - 🔇 VAD已禁用，跳过音频初始化 (TTS-only模式)
[1] 2025-07-24 20:48:57,150 - integrations.indextts_manager - INFO - 🌐 预热IndexTTS连接...
[1]
[1] [后端错误] 2025-07-24 20:48:57,157 - integrations.indextts_manager - INFO - ✅ HTTP连接预热完成: 0.007秒, 平均请求: 0.002秒, 连接数: 3
[1] 2025-07-24 20:48:57,157 - root - INFO - 🚀 预加载ASR模型...
[1] 2025-07-24 20:48:57,157 - root - INFO - 开始加载语音识别模型...
[1] 2025-07-24 20:48:57,157 - root - INFO - 🇨🇳 中文优先模式：优先尝试FunASR中文模型...
[1]
[1] [后端错误] 2025-07-24 20:49:02,366 - root - INFO - 🇨🇳 发现中文流式模型: H:\AI\CosyVoice\new\data\models\paraformer-zh-streaming
[1]
[1] [后端错误] 2025-07-24 20:49:04,508 - root - INFO - download models from model hub: ms
[1]
[1] [后端错误] 2025-07-24 20:49:06,515 - root - INFO - Loading pretrained params from H:\AI\CosyVoice\new\data\models\paraformer-zh-streaming\model.pt
[1]
[1] [后端错误] 2025-07-24 20:49:06,526 - root - INFO - ckpt: H:\AI\CosyVoice\new\data\models\paraformer-zh-streaming\model.pt
[1]
[1] [后端错误] 2025-07-24 20:49:07,948 - root - INFO - scope_map: ['module.', 'None']
[1] 2025-07-24 20:49:07,948 - root - INFO - excludes: None
[1]
[1] [后端错误] 2025-07-24 20:49:08,091 - root - INFO - Loading ckpt: H:\AI\CosyVoice\new\data\models\paraformer-zh-streaming\model.pt, status: <All keys matched successfully>
[1]
[1] [后端错误] 2025-07-24 20:49:08,588 - root - INFO - ✅ 成功加载中文流式模型: H:\AI\CosyVoice\new\data\models\paraformer-zh-streaming
[1] 2025-07-24 20:49:08,588 - root - INFO - ✅ FunASR中文模型加载成功
[1] 2025-07-24 20:49:08,588 - root - INFO - ✅ ASR模型预加载完成，耗时: 11.43秒
[1]
[1] [后端错误] 2025-07-24 20:49:08,588 - utils.asr_postprocessor - INFO - 🧹 ASR后处理器初始化完成
[1] 2025-07-24 20:49:08,588 - utils.asr_postprocessor - INFO -    - 同音字纠错词典: 94 条
[1] 2025-07-24 20:49:08,589 - utils.asr_postprocessor - INFO -    - 上下文模式: 3 个
[1] 2025-07-24 20:49:08,589 - utils.asr_postprocessor - INFO -    - 专业术语: 20 个
[1] 2025-07-24 20:49:08,589 - root - INFO - ✅ ASR后处理器已启用
[1] 2025-07-24 20:49:08,589 - root - INFO - ASR引擎初始化完成
[1] 2025-07-24 20:49:08,589 - root - INFO - ASR引擎加载完成，耗时: 11.43秒
[1] 2025-07-24 20:49:08,589 - root - INFO - 🎯 ASR引擎已启用持久模式，模型将常驻内存
[1]
[1] [后端错误] 2025-07-24 20:49:08,589 - root - INFO - 🚀 预加载ASR模型...
[1] 2025-07-24 20:49:08,589 - root - INFO - 开始加载语音识别模型...
[1] 2025-07-24 20:49:08,589 - root - INFO - 🇨🇳 中文优先模式：优先尝试FunASR中文模型...
[1] 2025-07-24 20:49:08,590 - root - INFO - ✅ FunASR中文模型加载成功
[1] 2025-07-24 20:49:08,590 - root - INFO - ✅ ASR模型预加载完成，耗时: 0.00秒
[1] 2025-07-24 20:49:08,590 - integrations.indextts_manager - INFO - 🎯 已启用ASR持久模式，模型将常驻内存
[1]
[1] [后端错误] 2025-07-24 20:49:08,590 - integrations.indextts_manager - INFO - 🔧 实时对话配置检查:
[1] 2025-07-24 20:49:08,590 - integrations.indextts_manager - INFO -    - realtime_config: {'use_history': False, 'audio_paths': [], 'character': '藏识仙灵', 'mode': 'oracle', 'llm_params': {'temperature': 0.7, 'top_p': 0.9, 'top_k': 40, 'max_tokens': 2000, 'provider': 'lmstudio', 'model_name': 'gemma3'}, 'system_prompt': '你是一位深谙周易玄学的神谕大师，拥有深厚的易学功底和人生智慧。以古雅而亲切的语调，结合周易哲学为求问者提供精准指导。\n\n基于以下信息为求问者提供专业的解答：\n\n## 叩问主题\n卜学业 - 金榜题名，学而时习\n\n## 神机妙卦\n- 卦名：蒙（第4卦）\n- 卦辞：亨。匪我求童蒙，童蒙求我。初筮告，再三渎，渎则不告。利贞。\n- 象辞：山下出泉蒙，君子以果行育德。\n- 上卦：艮（山，静止止住）\n- 下卦：坎（水，陷险流动）\n- 综合解释：蒙卦象征启蒙，需要正确的教育引导。\n\n### 六爻详解\n- 初六：初六：发蒙，利用刑人，用说桎梏，以往吝。（启发蒙昧，利于用刑法警示人，用来解除桎梏，继续前往会有困难。）\n- 二九：九二：包蒙吉，纳妇吉；子克家。（包容蒙昧吉利，纳取妇人吉利；儿子能治家。）\n- 三六：六三：勿用取女；见金夫，不有躬，无攸利。（勿用取女；见金夫，不有躬，无攸利。）\n- 四六：六四：困蒙，吝。（困于蒙昧，有困难。）\n- 五六：六五：童蒙，吉。（童蒙，吉利。）\n- 上九：上九：击蒙；不利为寇，利御寇。（打击蒙昧；不利于做盗寇，利于防御盗寇。）\n\n## 天人感应\n【天人感应分析结果】\n姓名：王琳\n性别：女\n生辰：1999年12月13日\n\n命理分析：\n施主降生于兔年，今已25春秋，正值青年期。命格以比肩为主导，您的年命为城头土，日命为平地木。具有木的特质：生机勃勃、有创造力、向上发展，但有时缺乏耐心。主要性格特征体现为：温和包容、协调之能强、有亲和力、善于调解和温和包容、滋养大地、孕育万物、生命力强的结合。。施主人生特质乃温文尔雅与心思细腻，值此而立之前之际，宜发挥自身所长，追求个人修为与人生目标。\n\n整体运势：总分85分\n性格特点：据名讳、八字与生肖之综合推演，施主之禀性特征：姓名格局很好，五格搭配合理，整体运势良好，人生发展较为顺畅。。施主命格以比肩为主导，比肩代表兄弟朋友、合作伙伴、竞争对手，象征平等的关系。主要禀性特征体现为：重视友情、合作意识强、有竞争意识、自尊心强。施主所长在于团队合作好、朋友多、有原则、坚持己见，当留心之处乃固执己见、不善妥协、容易争执、财运一般。。身为兔相者，更兼具温和善良、心思细腻、有艺术感之品格。诸般特质融汇，成就施主独特之人格魅力。\n\n建议：缺乏正官，建议多培养责任心强的品质；发挥兔相之优势：温和善良；青年阶段当多修学，积累阅历与人脉\n\n这是根据用户的姓名五格和生辰八字进行的命理分析，请结合此信息进行卦象解读。\n\n## 当前时间信息（不是用户生辰的时间）\n**现在的时间是，2025年7月24日 20:48:52 星期四\n**当前时辰**：戌时（19:00-21:00）- 黄昏时分，家人团聚\n**下个节气**：立秋\n\n*给求问者指导时可以结合当下的时间*\n\n', 'tts_config': {'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}, 'ai_config': {'provider': 'lmstudio', 'model_name': 'gemma3', 'temperature': 0.7, 'top_p': 0.9, 'top_k': 40, 'max_tokens': 2000}, 'disable_vad': True}
[1] 2025-07-24 20:49:08,590 - integrations.indextts_manager - INFO -    - disable_vad值: True
[1] 2025-07-24 20:49:08,590 - integrations.indextts_manager - INFO -    - VAD类型: silero
[1] 2025-07-24 20:49:08,590 - integrations.indextts_manager - INFO -    - 音频流状态: None
[1]
[1] [后端错误] 2025-07-24 20:49:08,590 - integrations.indextts_manager - INFO - 🔇 VAD线程已跳过 (TTS-only模式)
[1]
[1] [后端错误] 2025-07-24 20:49:08,591 - integrations.indextts_manager - INFO - 🤖 LLM线程启动
[1]
[1] [后端错误] 2025-07-24 20:49:08,592 - integrations.indextts_manager - INFO - 🎵 TTS线程启动
[1] 2025-07-24 20:49:08,593 - integrations.indextts_manager - INFO - ✅ 实时对话处理线程已启动，活跃线程: ['llm', 'tts']
[1]
[1] [后端错误] 2025-07-24 20:49:08,593 - integrations.indextts_manager - INFO - ✅ IndexTTS实时对话启动成功 - TTS与LLM配置已正确分离
[1] 2025-07-24 20:49:08,593 - integrations.indextts_manager - INFO - ============================================================
[1] 2025-07-24 20:49:08,593 - integrations.indextts_manager - INFO - 🔇 TTS-only模式：VAD已禁用，等待手动文本输入...
[1] 2025-07-24 20:49:08,593 - integrations.indextts_manager - INFO - 📝 可通过WebSocket发送文本消息进行LLM对话
[1] 2025-07-24 20:49:08,593 - integrations.indextts_manager - INFO - 🎵 AI回复将自动转换为TTS音频
[1] 2025-07-24 20:49:08,593 - integrations.indextts_manager - INFO - ============================================================
[1] 2025-07-24 20:49:08,593 - api_bridge - INFO - ✅ 实时对话启动成功，TTS将使用语音配置而非角色名称
[1]
[1] [后端] funasr version: 1.2.0.
[1] Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[1] New version is available: 1.2.6.
[1] Please use the command "pip install -U funasr" to upgrade.
[1] INFO:     127.0.0.1:49685 - "POST /api/realtime/start HTTP/1.1" 200 OK
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 49755) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1] 2025-07-24 20:49:08,617 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-24 20:49:09,621 - api_bridge - INFO - 📤 收到手动消息请求: 请为我解读刚刚抽取的卦象：蒙卦，关于卜学业方面的问题。...
[1]
[1] [后端错误] 2025-07-24 20:49:09,623 - integrations.indextts_manager - INFO - 📝 接收直接文本输入: 请为我解读刚刚抽取的卦象：蒙卦，关于卜学业方面的问题。
[1] 2025-07-24 20:49:09,623 - api_bridge - INFO - 📝 用户说: 请为我解读刚刚抽取的卦象：蒙卦，关于卜学业方面的问题。
[1]
[1] [后端错误] 2025-07-24 20:49:09,623 - integrations.indextts_manager - INFO - ✅ 文本消息已加入处理队列: 请为我解读刚刚抽取的卦象：蒙卦，关于卜学业方面的问题。
[1]
[1] [后端错误] 2025-07-24 20:49:09,624 - integrations.indextts_manager - INFO - 🤖 初始化chat引擎...
[1] 2025-07-24 20:49:09,624 - integrations.indextts_manager - INFO - 🔄 切换到指定模型: LMstudio: gemma3
[1] 2025-07-24 20:49:09,624 - root - INFO - 🔄 模型切换: LMstudio -> lmstudio, 模型: gemma3
[1]
[1] [后端错误] 2025-07-24 20:49:09,624 - root - INFO - 对话历史已清空
[1] 2025-07-24 20:49:09,624 - root - INFO - ✅ 已切换到模型: lmstudio - gemma3
[1] 2025-07-24 20:49:09,624 - integrations.indextts_manager - INFO - ✅ 模型切换成功: lmstudio -> gemma3
[1] 2025-07-24 20:49:09,624 - integrations.indextts_manager - INFO - 🎭 更新系统提示词: 你是一位深谙周易玄学的神谕大师，拥有深厚的易学功底和人生智慧。以古雅而亲切的语调，结合周易哲学为求问者提供精准指导。
[1]
[1] 基于以下信息为求问者提供专业的解答：
[1]
[1] ## 叩问主题
[1] 卜学业 - 金榜题名，学...
[1] 2025-07-24 20:49:09,624 - root - INFO - 系统提示词已更新
[1] 2025-07-24 20:49:09,624 - integrations.indextts_manager - INFO - ✅ 系统提示词已更新，对话状态已重置
[1] 2025-07-24 20:49:09,624 - integrations.indextts_manager - INFO - 🤖 开始LLM生成 #1: '请为我解读刚刚抽取的卦象：蒙卦，关于卜学业方面的问题。...'
[1] 2025-07-24 20:49:09,624 - integrations.indextts_manager - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: gemma3
[1] 2025-07-24 20:49:09,624 - root - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: gemma3
[1] 2025-07-24 20:49:09,624 - root - INFO - 📡 使用LMstudio API - 端点: http://localhost:1234
[1] 2025-07-24 20:49:09,624 - root - INFO - 🔄 调用LMstudio API - 模型: gemma3, 流式: True
[1]
[1] [后端错误] 2025-07-24 20:49:09,626 - api_bridge - INFO - ✅ 手动消息已发送到实时对话系统
[1]
[1] [后端] 🔗 建立主WebSocket连接: conn_1
[1] ✅ WebSocket连接建立: conn_1 (总连接数: 1)
[1] 📨 收到客户端消息: connect
[1] INFO:     127.0.0.1:49685 - "POST /api/realtime/manual-input HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 20:49:09,627 - api_bridge - INFO - ✅ 转录事件WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:09,637 - root - INFO - 🌐 HTTP请求发送完成: 0.012秒
[1]
[1] [后端错误] 2025-07-24 20:49:10,550 - root - INFO - 🚀 首个token: TTFT=0.926秒 (HTTP=0.012s + 等待=0.914s)
[1] 2025-07-24 20:49:10,550 - integrations.indextts_manager - INFO - 🚀 首个token: TTFT=0.926秒 (HTTP=0.000s + 等待=0.926s)
[1] 2025-07-24 20:49:10,550 - api_bridge - INFO - 🤖 AI回复: 王
[1]
[1] [后端错误] 2025-07-24 20:49:10,554 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:11,052 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机
[1]
[1] [后端错误] 2025-07-24 20:49:11,055 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:11,567 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周
[1]
[1] [后端错误] 2025-07-24 20:49:11,569 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:12,105 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:12,107 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:12,637 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:12,640 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:13,157 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:13,160 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:13,666 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:13,669 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:14,204 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:14,208 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:14,715 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:14,718 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:15,241 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:15,244 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:15,785 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:15,789 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:16,308 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1] 2025-07-24 20:49:16,310 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:16,858 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:16,861 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:17,379 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:17,382 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:17,881 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:17,885 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:18,439 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:18,441 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:18,996 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:18,998 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:19,496 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:19,499 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:19,999 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:20,002 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:20,562 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:20,565 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:21,071 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:21,074 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:21,574 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:21,578 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:22,100 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:22,104 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:22,623 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:22,627 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:23,183 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1] 2025-07-24 20:49:23,185 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:23,683 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1] 2025-07-24 20:49:23,687 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:24,202 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:24,205 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:24,705 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:24,708 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:25,226 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:25,228 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:25,733 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:25,736 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:26,246 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:26,249 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:26,755 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:26,759 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:27,278 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:27,281 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:27,789 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1] 2025-07-24 20:49:27,791 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:28,364 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:28,369 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:28,872 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:28,877 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:29,394 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:29,396 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:29,916 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:29,919 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:30,439 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:30,441 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:30,957 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:30,959 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:31,495 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:31,499 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:31,998 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:32,000 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:32,499 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:32,503 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:33,012 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:33,014 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:33,536 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:33,539 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:34,043 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:34,046 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:34,547 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:34,549 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:35,067 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:35,069 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 20:49:35,279 - root - INFO - 📊 LLM详细性能: 总耗时=25.65s, HTTP建立=0.012s, 字符数=900, Token数≈770, Chunk数=772
[1] 2025-07-24 20:49:35,279 - root - INFO - 📊 生成速度: 35.1字符/s, 30.0Token/s
[1] 2025-07-24 20:49:35,279 - root - INFO - ✅ LMstudio响应完成 - 长度: 900 字符
[1] 2025-07-24 20:49:35,279 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...
[1]
[1] [后端错误] 2025-07-24 20:49:35,280 - integrations.indextts_manager - INFO - 📊 LLM详细性能: 总耗时=25.66s, HTTP建立=0.000s, 字符数=900, Token数≈600, Chunk数=770
[1] 2025-07-24 20:49:35,281 - integrations.indextts_manager - INFO - 📊 生成速度: 35.1字符/s, 23.4Token/s
[1] 2025-07-24 20:49:35,281 - integrations.indextts_manager - INFO - ✅ lmstudio响应完成 - 长度: 900 字符
[1] 2025-07-24 20:49:35,281 - integrations.indextts_manager - INFO - 📊 LLM性能统计: 耗时=25.66秒, 字符数=900, 速度=35.1字符/秒
[1] 2025-07-24 20:49:35,281 - integrations.indextts_manager - INFO - ✅ AI回复完成: '王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同...'
[1] 2025-07-24 20:49:35,281 - integrations.indextts_manager - INFO - ✅ AI回复已放入TTS队列 (队列大小: 0 -> 1)
[1] 2025-07-24 20:49:35,281 - integrations.indextts_manager - INFO - 🏁 LLM线程结束，总共处理了 1 个LLM请求
[1] 2025-07-24 20:49:35,281 - integrations.indextts_manager - INFO - 🎵 开始TTS合成 #1: '王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如《周易》所言："同声相应，同气相求"，人多因其而友爱也。今值戌时，火土交融，正合"冬暖春来"之象。
[1]
[1] 蒙卦为启蒙之卦，象征破晓前...'
[1]
[1] [后端错误] 2025-07-24 20:49:35,284 - integrations.indextts_manager - INFO - 🧹 文本预处理完成: 原长度900 -> 处理后838
[1] 2025-07-24 20:49:35,284 - integrations.indextts_manager - INFO - 🔇 TTS开始前暂停VAD监听
[1] 2025-07-24 20:49:35,284 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1] 2025-07-24 20:49:35,284 - integrations.indextts_manager - INFO - 🔄 长文本流式处理 (838字符)
[1]
[1] [后端错误] 2025-07-24 20:49:35,286 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1] 2025-07-24 20:49:35,288 - integrations.indextts_manager - INFO - 🎵 使用TTS模式: oracle
[1] 2025-07-24 20:49:35,288 - integrations.indextts_manager - INFO - 🎵 TTS配置解析: mode=user-voice, config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 20:49:35,288 - integrations.indextts_manager - INFO - 🎵 使用用户音色模式: 21
[1]
[1] [后端错误] 2025-07-24 20:49:35,289 - integrations.indextts_manager - INFO - ✅ 在目录 H:/AI/CosyVoice/audios 中找到音频文件: 洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav
[1] 2025-07-24 20:49:35,289 - integrations.indextts_manager - INFO - ✅ 找到用户音色 '洪钟响狼蛇藏，烈火熊熊双目盲 藏识仙灵 2' (ID: 21)
[1] 2025-07-24 20:49:35,290 - integrations.indextts_manager - INFO -    原始配置: ['洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 20:49:35,290 - integrations.indextts_manager - INFO -    WSL2路径: ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1]
[1] [后端错误] 2025-07-24 20:49:35,290 - integrations.indextts_manager - INFO - ✅ 用户音色映射成功: ID=21 -> ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 20:49:35,290 - integrations.indextts_manager - INFO - 🚀 用户音色文本较长(838字符)，使用智能切分处理
[1] 2025-07-24 20:49:35,290 - integrations.indextts_manager - INFO - 🎵 神谕之音jieba智能切分开始: 总长度838字
[1]
[1] [后端错误] 2025-07-24 20:49:35,347 - integrations.indextts_manager - INFO - 🎵 神谕检测到语义完整的第一片段(60字): '王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如周易所言，同声相应，同气相求，人多因其而友爱也。'
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕之音智能切分完成: 总计7片段，原文838字→切分后830字
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - ✅ 神谕第1片段: 60字 - '王琳施主，且听老道为你细解此蒙卦之玄机。你生于兔年，命格以比肩为主，正如周易所言，同声相应，同气相求，人多因其而友爱也。'
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕第2片段: 144字 - 今值戌时，火土交融，正合冬暖春来之象。蒙卦为启蒙之卦，象征破晓前透出的第一缕光芒...
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕第3片段: 131字 - 初六爻曰，发蒙，利用刑人，用说桎梏，以往吝。此指启蒙之时，尚需教导引导，如种子破...
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕第4片段: 133字 - 此指包容释疑之象，若能虚心求教，必有所得。正如论语所言，三人行，其或有耻，当善于...
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕第5片段: 125字 - 黄帝内经云，上古之人，其知道者，法于阴阳，应从根本入手，方能见效。六四爻言，困蒙...
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕第6片段: 120字 - 此指破除无知谬误之时，不宜恶意攻击他人，但亦当防范虚伪言论，方能学有所成。老道观...
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕第7片段: 117字 - 今年你二十五岁，正值青年之际，中庸曰，博学之盛，终不穷也，当珍惜此学习黄金期，厚...
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🎵 神谕之音模式：使用标准切分策略
[1] 2025-07-24 20:49:35,348 - integrations.indextts_manager - INFO - 🔄 文本分成 7 个片段流式处理
[1]
[1] [后端错误] 2025-07-24 20:49:38,676 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 49969) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-24 20:49:40,690 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-24 20:49:42,643 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.295s, 准备=0.000s, 网络传输=7.295s
[1]
[1] [后端错误] 2025-07-24 20:49:42,643 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.29秒, 响应大小=0.79MB, 传输速度=0.11MB/s
[1]
[1] [后端错误] 2025-07-24 20:49:42,650 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=17.28s, RTF=0.42, 解析=0.007s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 20:49:42,650 - integrations.indextts_manager - INFO - 🎵 第一片段生成完成，立即播放 (17.28秒)
[1] 2025-07-24 20:49:42,650 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1]
[1] [后端错误] 2025-07-24 20:49:42,652 - integrations.indextts_manager - INFO - 🔊 立即播放第一个音频片段
[1] 2025-07-24 20:49:42,652 - api_bridge - INFO - 🔊 开始播放音频
[1]
[1] [后端错误] 2025-07-24 20:49:42,652 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 20:49:42,653 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 20:49:42,653 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 20:49:42,653 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 20:49:42,653 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 20:49:42,692 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-24 20:49:42,692 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 完整音频 (17.28s)
[1]
[1] [后端错误] 2025-07-24 20:49:42,891 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 20:49:54,347 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=11.654s, 准备=0.000s, 网络传输=11.654s
[1] 2025-07-24 20:49:54,347 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=11.65秒, 响应大小=2.32MB, 传输速度=0.20MB/s
[1]
[1] [后端错误] 2025-07-24 20:49:54,352 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=50.58s, RTF=0.23, 解析=0.005s, 最终处理=0.000s
[1] 2025-07-24 20:49:54,353 - integrations.indextts_manager - INFO - 🎵 后续片段 2 生成完成，发送用于衔接 (50.58秒)
[1] 2025-07-24 20:49:54,353 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 20:49:54,353 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 20:49:54,353 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 20:49:54,353 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 20:49:54,353 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 20:49:54,353 - api_bridge - INFO - 🎵 流式音频片段: chunk 2/7
[1] 2025-07-24 20:49:54,353 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 20:49:54,382 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 2/7 (50.58s)
[1] 2025-07-24 20:49:54,383 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 20:49:54,622 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 20:50:04,333 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=9.950s, 准备=0.000s, 网络传输=9.950s
[1] 2025-07-24 20:50:04,334 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=9.95秒, 响应大小=1.98MB, 传输速度=0.20MB/s
[1]
[1] [后端错误] 2025-07-24 20:50:04,337 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=43.28s, RTF=0.23, 解析=0.003s, 最终处理=0.001s
[1]
[1] [后端错误] 2025-07-24 20:50:04,339 - integrations.indextts_manager - INFO - 🎵 后续片段 3 生成完成，发送用于衔接 (43.28秒)
[1] 2025-07-24 20:50:04,339 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 20:50:04,339 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 20:50:04,339 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 20:50:04,339 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 20:50:04,339 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 20:50:04,339 - api_bridge - INFO - 🎵 流式音频片段: chunk 3/7
[1] 2025-07-24 20:50:04,339 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 20:50:04,360 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 3/7 (43.28s)
[1]
[1] [后端错误] 2025-07-24 20:50:04,360 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 20:50:04,556 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 20:50:12,694 - integrations.indextts_manager - WARNING - ⚠️ 使用估算VAD恢复时间: 估算总时长=259.32s + 缓冲=3.0s = 262.32s
[1]
[1] [后端错误] 2025-07-24 20:50:14,536 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=10.176s, 准备=0.000s, 网络传输=10.176s
[1] 2025-07-24 20:50:14,537 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=10.18秒, 响应大小=2.05MB, 传输速度=0.20MB/s
[1]
[1] [后端错误] 2025-07-24 20:50:14,542 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=44.79s, RTF=0.23, 解析=0.005s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 20:50:14,543 - integrations.indextts_manager - INFO - 🎵 后续片段 4 生成完成，发送用于衔接 (44.79秒)
[1] 2025-07-24 20:50:14,543 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 20:50:14,543 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 20:50:14,543 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1]
[1] [后端错误] 2025-07-24 20:50:14,543 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 20:50:14,543 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 20:50:14,543 - api_bridge - INFO - 🎵 流式音频片段: chunk 4/7
[1] 2025-07-24 20:50:14,543 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 20:50:14,568 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 4/7 (44.79s)
[1] 2025-07-24 20:50:14,570 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 20:50:14,752 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 20:50:24,844 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=10.275s, 准备=0.000s, 网络传输=10.275s
[1] 2025-07-24 20:50:24,845 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=10.27秒, 响应大小=2.06MB, 传输速度=0.20MB/s
[1]
[1] [后端错误] 2025-07-24 20:50:24,850 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=44.95s, RTF=0.23, 解析=0.005s, 最终处理=0.000s
[1] 2025-07-24 20:50:24,852 - integrations.indextts_manager - INFO - 🎵 后续片段 5 生成完成，发送用于衔接 (44.95秒)
[1] 2025-07-24 20:50:24,852 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 20:50:24,852 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 20:50:24,852 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 20:50:24,852 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 20:50:24,852 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 20:50:24,852 - api_bridge - INFO - 🎵 流式音频片段: chunk 5/7
[1] 2025-07-24 20:50:24,852 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 20:50:24,878 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 5/7 (44.95s)
[1]
[1] [后端错误] 2025-07-24 20:50:24,878 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 20:50:25,083 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 20:50:33,604 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.725s, 准备=0.000s, 网络传输=8.725s
[1] 2025-07-24 20:50:33,604 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.72秒, 响应大小=1.83MB, 传输速度=0.21MB/s
[1]
[1] [后端错误] 2025-07-24 20:50:33,607 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=40.08s, RTF=0.22, 解析=0.003s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 20:50:33,609 - integrations.indextts_manager - INFO - 🎵 后续片段 6 生成完成，发送用于衔接 (40.08秒)
[1] 2025-07-24 20:50:33,609 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 20:50:33,609 - api_bridge - INFO - 🎵 接收到TTS音频
[1]
[1] [后端错误] 2025-07-24 20:50:33,609 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 20:50:33,609 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 20:50:33,609 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 20:50:33,609 - api_bridge - INFO - 🎵 流式音频片段: chunk 6/7
[1] 2025-07-24 20:50:33,609 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 20:50:33,626 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 6/7 (40.08s)
[1]
[1] [后端错误] 2025-07-24 20:50:33,626 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 20:50:33,797 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 20:50:40,704 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] 2025-07-24 20:50:42,423 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.797s, 准备=0.000s, 网络传输=8.797s
[1] 2025-07-24 20:50:42,423 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.80秒, 响应大小=1.82MB, 传输速度=0.21MB/s
[1] 2025-07-24 20:50:42,428 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=39.81s, RTF=0.22, 解析=0.005s, 最终处理=0.000s
[1] 2025-07-24 20:50:42,429 - integrations.indextts_manager - INFO - 🎵 后续片段 7 生成完成，发送用于衔接 (39.81秒)
[1] 2025-07-24 20:50:42,429 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 20:50:42,429 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 20:50:42,429 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 20:50:42,429 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 20:50:42,429 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 20:50:42,429 - api_bridge - INFO - 🎵 流式音频片段: chunk 7/7
[1] 2025-07-24 20:50:42,429 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 20:50:42,449 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 7/7 (39.81s)
[1] 2025-07-24 20:50:42,449 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-24 20:50:42,450 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 7个片段, 总时长280.77秒
[1] 2025-07-24 20:50:42,451 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1] 2025-07-24 20:50:42,451 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1] 2025-07-24 20:50:42,451 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 50866) - "WebSocket /ws/realtime" [accepted]
[1] INFO:     connection open
[1] 2025-07-24 20:50:42,716 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-24 20:51:12,724 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1]
[1] [后端错误] INFO:     connection closed
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 51352) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-24 20:51:14,733 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-24 20:51:44,761 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 51473) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-24 20:51:46,770 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1