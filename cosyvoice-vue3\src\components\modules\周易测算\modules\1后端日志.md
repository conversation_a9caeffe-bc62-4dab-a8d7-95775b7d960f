[1] [后端错误] 2025-07-24 16:34:52,342 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:34:52,365 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 3/6 (46.74s)
[1]
[1] [后端错误] 2025-07-24 16:34:52,365 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:34:52,611 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:35:01,017 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.651s, 准备=0.000s, 网络传输=8.651s
[1] 2025-07-24 16:35:01,017 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.65秒, 响应大小=2.30MB, 传输速度=0.27MB/s
[1]
[1] [后端错误] 2025-07-24 16:35:01,021 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=50.35s, RTF=0.17, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:35:01,023 - integrations.indextts_manager - INFO - 🎵 后续片段 4 生成完成，发送用于衔接 (50.35秒)
[1]
[1] [后端错误] 2025-07-24 16:35:01,023 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:35:01,023 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:35:01,023 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1]
[1] [后端错误] 2025-07-24 16:35:01,023 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 16:35:01,023 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 16:35:01,024 - api_bridge - INFO - 🎵 流式音频片段: chunk 4/6
[1]
[1] [后端错误] 2025-07-24 16:35:01,024 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:35:01,056 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 4/6 (50.35s)
[1]
[1] [后端错误] 2025-07-24 16:35:01,056 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:35:01,337 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:35:05,038 - integrations.indextts_manager - WARNING - ⚠️ 使用估算VAD恢复时间: 估算总时长=268.71s + 缓冲=3.0s = 271.71s
[1]
[1] [后端错误] 2025-07-24 16:35:08,928 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.871s, 准备=0.000s, 网络传输=7.871s
[1] 2025-07-24 16:35:08,928 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.87秒, 响应大小=2.00MB, 传输速度=0.25MB/s
[1]
[1] [后端错误] 2025-07-24 16:35:08,937 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=43.79s, RTF=0.18, 解析=0.009s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:35:08,939 - integrations.indextts_manager - INFO - 🎵 后续片段 5 生成完成，发送用于衔接 (43.79秒)
[1] 2025-07-24 16:35:08,939 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:35:08,939 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:35:08,939 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:35:08,939 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 16:35:08,939 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 16:35:08,939 - api_bridge - INFO - 🎵 流式音频片段: chunk 5/6
[1]
[1] [后端错误] 2025-07-24 16:35:08,939 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:35:08,965 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 5/6 (43.79s)
[1]
[1] [后端错误] 2025-07-24 16:35:08,966 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:35:09,205 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:35:17,505 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.538s, 准备=0.000s, 网络传输=8.538s
[1] 2025-07-24 16:35:17,505 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.54秒, 响应大小=2.17MB, 传输速度=0.25MB/s
[1] 2025-07-24 16:35:17,510 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=47.30s, RTF=0.18, 解析=0.005s, 最终处理=0.000s
[1] 2025-07-24 16:35:17,512 - integrations.indextts_manager - INFO - 🎵 后续片段 6 生成完成，发送用于衔接 (47.30秒)
[1] 2025-07-24 16:35:17,512 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:35:17,512 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:35:17,512 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:35:17,512 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 16:35:17,512 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 16:35:17,512 - api_bridge - INFO - 🎵 流式音频片段: chunk 6/6
[1] 2025-07-24 16:35:17,513 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:35:17,542 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 6/6 (47.30s)
[1]
[1] [后端错误] 2025-07-24 16:35:17,545 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 6个片段, 总时长270.23秒
[1]
[1] [后端错误] 2025-07-24 16:35:17,545 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-24 16:35:17,547 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1] 2025-07-24 16:35:17,547 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1]
[1] [后端错误] 2025-07-24 16:35:17,798 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:37:13,388 - integrations.indextts_manager - INFO - 🏁 检测到手动VAD恢复请求，跳过流式自动恢复
[1]
[1] [后端错误] 2025-07-24 16:39:36,751 - integrations.indextts_manager - INFO - 🎧 VAD监听已恢复
[1] 2025-07-24 16:39:36,751 - integrations.indextts_manager - INFO - 🎧 流式播放完成，VAD监听已恢复
[1]
[1] [后端错误] 2025-07-24 16:40:04,448 - api_bridge - INFO - 🎧 收到VAD监听恢复请求
[1] 2025-07-24 16:40:04,448 - integrations.indextts_manager - INFO - 🎧 收到VAD监听恢复请求
[1] 2025-07-24 16:40:04,448 - integrations.indextts_manager - INFO - 🎧 VAD当前未暂停，无需恢复
[1]
[1] [后端] INFO:     127.0.0.1:13464 - "POST /api/realtime/resume-vad HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:40:04,448 - api_bridge - INFO - ✅ VAD监听已恢复
[1]
[1] [后端错误] 2025-07-24 16:40:10,174 - api_bridge - INFO - ✅ 实时对话会话已停止: session_1753346002
[1] 2025-07-24 16:40:10,174 - integrations.indextts_manager - INFO - 🛑 停止实时对话...
[1] 2025-07-24 16:40:10,174 - integrations.indextts_manager - INFO - 🎧 VAD监听已恢复
[1] 2025-07-24 16:40:10,174 - integrations.indextts_manager - INFO - 🔧 强制停止VAD和音频流...
[1] 2025-07-24 16:40:10,174 - root - INFO - 对话历史已清空
[1] 2025-07-24 16:40:10,174 - integrations.indextts_manager - INFO - 🔄 LLM对话历史已清空
[1] 2025-07-24 16:40:10,174 - integrations.indextts_manager - INFO - ✅ VAD和音频流强制停止完成
[1] 2025-07-24 16:40:10,174 - integrations.indextts_manager - INFO - 🔄 等待线程结束: llm
[1]
[1] [后端错误] 2025-07-24 16:40:10,193 - integrations.indextts_manager - INFO - 🏁 TTS线程结束，总共处理了 2 个TTS请求
[1]
[1] [后端错误] 2025-07-24 16:40:10,224 - root - INFO - 对话历史已清空
[1] 2025-07-24 16:40:10,224 - integrations.indextts_manager - INFO - 🔄 LLM对话历史已清空
[1] 2025-07-24 16:40:10,224 - integrations.indextts_manager - INFO - ✅ IndexTTS实时对话已停止
[1]
[1] [后端] 📨 收到客户端消息: PAGE_UNLOAD
[1] INFO:     127.0.0.1:13465 - "POST /api/realtime/stop HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:40:10,229 - api_bridge - INFO - 🔄 收到强制重新启动VAD请求
[1] 2025-07-24 16:40:10,229 - integrations.indextts_manager - WARNING - ⚠️ 实时对话未启动，无法重新启动VAD
[1] 2025-07-24 16:40:10,229 - api_bridge - ERROR - ❌ VAD和音频流强制重新启动失败
[1]
[1] [后端] INFO:     127.0.0.1:13465 - "POST /api/realtime/force-restart-vad HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:40:10,232 - api_bridge - INFO - ✅ 已清空实时对话历史记录
[1]
[1] [后端] INFO:     127.0.0.1:13465 - "POST /api/realtime/clear-history HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:03,015 - api_bridge - WARNING - ⚠️ 没有活跃的会话需要停止
[1]
[1] [后端] INFO:     127.0.0.1:13661 - "POST /api/realtime/stop HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:03,028 - api_bridge - INFO - 🔄 收到强制重新启动VAD请求
[1] 2025-07-24 16:42:03,028 - integrations.indextts_manager - WARNING - ⚠️ 实时对话未启动，无法重新启动VAD
[1] 2025-07-24 16:42:03,028 - api_bridge - ERROR - ❌ VAD和音频流强制重新启动失败
[1]
[1] [后端] INFO:     127.0.0.1:13661 - "POST /api/realtime/force-restart-vad HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:03,041 - api_bridge - INFO - ✅ 已清空实时对话历史记录
[1]
[1] [后端] INFO:     127.0.0.1:13661 - "POST /api/realtime/clear-history HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:06,531 - api_bridge - WARNING - ⚠️ 没有活跃的会话需要停止
[1]
[1] [后端] 📨 收到客户端消息: PAGE_UNLOAD
[1] 📨 收到客户端消息: PAGE_UNLOAD
[1] INFO:     127.0.0.1:13661 - "POST /api/realtime/stop HTTP/1.1" 200 OK
[1]
[1] [后端] INFO:     127.0.0.1:13661 - "GET /api/health HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:06,655 - api_bridge - INFO - 🔄 收到强制重新启动VAD请求
[1] 2025-07-24 16:42:06,655 - integrations.indextts_manager - WARNING - ⚠️ 实时对话未启动，无法重新启动VAD
[1] 2025-07-24 16:42:06,655 - api_bridge - ERROR - ❌ VAD和音频流强制重新启动失败
[1]
[1] [后端] INFO:     127.0.0.1:13661 - "POST /api/realtime/force-restart-vad HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:06,667 - root - INFO - 发现Ollama模型: ['gemma3:12b']
[1]
[1] [后端错误] 2025-07-24 16:42:06,671 - root - INFO - 发现LMstudio模型: ['csxl0.6', 'qwen3-235b-a22b-instruct-2507', 'text-embedding-nomic-embed-text-v1.5', 'text-embedding-qwen3-embedding-0.6b', 'deepseek-r1-0528-qwen3-8b', 'devstral-small-2507', 'hunyuan-a13b-instruct', 'sicariussicariistuff_impish_llama_4b', 'deepswe-preview', 'ernie-4.5-0.3b-pt', 'dreamgen_lucid-v1-nemo', 'rwkv7-goose-world3-2.9b-hf', 'qwen3-53b-a3b-total-recall-v1.4-128k', 'thudm_glm-z1-32b-0414', 'mistralai/mistral-nemo-instruct-2407', 'unsloth/mistral-nemo-instruct-2407', 'gemma-3n-e4b-it', 'jan-nano-128k', 'dots.llm1.inst', 'unsloth/qwen3-30b-a3b', 'glm-4-32b-0414', 'polaris-4b-preview', 'polaris-project_polaris-4b-preview', 'kimi-dev-72b@q3_k_xl', 'kimi-dev-72b@q5_k_xl', 'devstral-small-2505', 'qwen3-32b', 'mistral-small-3.2-24b-instruct-2506@q4_k_xl', 'mistral-small-3.2-24b-instruct-2506@q8_k_xl', 'qwq-32b', 'magistral-small-2506', 'mistral-small-3.1-24b-instruct-2503', 'qwen2.5-coder-0.5b-instruct', 'llama-4-scout-17b-16e-instruct', 'qwen3-235b-a22b', 'jan-nano', 'qwen2.5-coder-32b-instruct', 'monkeyocr-recognition', 'midnight-miqu-70b-v1.0', 'qwq-32b-abliterated-yarn', 'qwen3-8b', 'qwen3-32b-ud', 'qwen3-30b-a3b-ud', 'qwen3-30b-a3b-128k', 'qwen3-14b-ud', 'glm-4-9b-0414-ud', 'glm-4-32b-0414-ud', 'gemma-3-4b-it', 'qwen3-30b-a3b-gguf/qwen3-30b-a3b', 'internlm3-8b-instruct', 'gemma-3-27b-it.gguf', 'gemma-3-12b-it.gguf', 'ui-tars-7b-dpo']
[1] 2025-07-24 16:42:06,671 - api_bridge - INFO - 🎯 当前激活模型: LMstudio: csxl0.6
[1]
[1] [后端] INFO:     127.0.0.1:13667 - "GET /api/llm/models HTTP/1.1" 200 OK
[1]
[1] [后端] INFO:     127.0.0.1:13667 - "GET /api/health HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:06,756 - data.character_presets - INFO - 返回角色列表: 8 个角色
[1]
[1] [后端] INFO:     127.0.0.1:13667 - "GET /api/characters/list HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:06,774 - data.character_presets - WARNING - 角色 周易取名大师 已存在，跳过导入
[1]
[1] [后端] INFO:     127.0.0.1:13667 - "POST /api/characters/import HTTP/1.1" 200 OK
[1]
[1] [后端] INFO:     127.0.0.1:13667 - "GET /api/health HTTP/1.1" 200 OK
[1]
[1] [后端] INFO:     127.0.0.1:13667 - "GET /api/health HTTP/1.1" 200 OK
[1]
[1] [后端] INFO:     127.0.0.1:13689 - "GET /api/voice/profiles HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:09,339 - root - INFO - 发现Ollama模型: ['gemma3:12b']
[1]
[1] [后端错误] 2025-07-24 16:42:09,342 - root - INFO - 发现LMstudio模型: ['csxl0.6', 'qwen3-235b-a22b-instruct-2507', 'text-embedding-nomic-embed-text-v1.5', 'text-embedding-qwen3-embedding-0.6b', 'deepseek-r1-0528-qwen3-8b', 'devstral-small-2507', 'hunyuan-a13b-instruct', 'sicariussicariistuff_impish_llama_4b', 'deepswe-preview', 'ernie-4.5-0.3b-pt', 'dreamgen_lucid-v1-nemo', 'rwkv7-goose-world3-2.9b-hf', 'qwen3-53b-a3b-total-recall-v1.4-128k', 'thudm_glm-z1-32b-0414', 'mistralai/mistral-nemo-instruct-2407', 'unsloth/mistral-nemo-instruct-2407', 'gemma-3n-e4b-it', 'jan-nano-128k', 'dots.llm1.inst', 'unsloth/qwen3-30b-a3b', 'glm-4-32b-0414', 'polaris-4b-preview', 'polaris-project_polaris-4b-preview', 'kimi-dev-72b@q3_k_xl', 'kimi-dev-72b@q5_k_xl', 'devstral-small-2505', 'qwen3-32b', 'mistral-small-3.2-24b-instruct-2506@q4_k_xl', 'mistral-small-3.2-24b-instruct-2506@q8_k_xl', 'qwq-32b', 'magistral-small-2506', 'mistral-small-3.1-24b-instruct-2503', 'qwen2.5-coder-0.5b-instruct', 'llama-4-scout-17b-16e-instruct', 'qwen3-235b-a22b', 'jan-nano', 'qwen2.5-coder-32b-instruct', 'monkeyocr-recognition', 'midnight-miqu-70b-v1.0', 'qwq-32b-abliterated-yarn', 'qwen3-8b', 'qwen3-32b-ud', 'qwen3-30b-a3b-ud', 'qwen3-30b-a3b-128k', 'qwen3-14b-ud', 'glm-4-9b-0414-ud', 'glm-4-32b-0414-ud', 'gemma-3-4b-it', 'qwen3-30b-a3b-gguf/qwen3-30b-a3b', 'internlm3-8b-instruct', 'gemma-3-27b-it.gguf', 'gemma-3-12b-it.gguf', 'ui-tars-7b-dpo']
[1] 2025-07-24 16:42:09,342 - api_bridge - INFO - 🎯 当前激活模型: LMstudio: csxl0.6
[1]
[1] [后端] INFO:     127.0.0.1:13667 - "GET /api/llm/models HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:09,344 - data.character_presets - INFO - 返回角色列表: 8 个角色
[1]
[1] [后端] INFO:     127.0.0.1:13661 - "GET /api/characters/list HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:19,753 - api_bridge - WARNING - ⚠️ 没有活跃的会话需要停止
[1]
[1] [后端] INFO:     127.0.0.1:13712 - "POST /api/realtime/stop HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:19,967 - api_bridge - INFO - 🚨 [API] start_realtime_dialogue API被调用
[1] 2025-07-24 16:42:19,968 - api_bridge - INFO - 🚨 [API] 接收到的参数: disableVAD=True, mode=oracle
[1] 2025-07-24 16:42:19,968 - api_bridge - INFO - 🚨 [API] webui实例获取成功
[1]
[1] [后端错误] 2025-07-24 16:42:19,969 - api_bridge - INFO - ✅ 新实时对话会话已创建: session_1753346539
[1]
[1] [后端错误] 2025-07-24 16:42:19,969 - api_bridge - INFO - 🎤 启动实时对话 [会话ID: session_1753346539] - 合成模式: user-voice, 禁用VAD: True
[1] 2025-07-24 16:42:19,969 - api_bridge - INFO - 🎵 TTS配置: {'mode': 'user-voice', 'audioPathsText': None, 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 16:42:19,969 - api_bridge - INFO - 🤖 LLM配置: characterName=藏识仙灵, systemPrompt前50字符=你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。...
[1] 2025-07-24 16:42:19,969 - api_bridge - INFO - 🎭 获取到角色头像: 藏识仙灵 ->
[1] 2025-07-24 16:42:19,971 - api_bridge - INFO - 🚨 [API] 准备调用 webui.indextts_manager.start_realtime_conversation
[1] 2025-07-24 16:42:19,971 - api_bridge - INFO - 🚨 [API] disable_vad参数值: True
[1]
[1] [后端错误] 2025-07-24 16:42:19,971 - integrations.indextts_manager - INFO - 🔇 VAD已禁用，跳过音频库检查（TTS-only模式）
[1]
[1] [后端错误] 2025-07-24 16:42:19,973 - integrations.indextts_manager - INFO - 🎤 启动IndexTTS实时对话... (VAD禁用: True)
[1] 2025-07-24 16:42:19,973 - integrations.indextts_manager - INFO - 🔍 音频库状态检查: AUDIO_LIBS_AVAILABLE=True, SILERO_AVAILABLE=True, WEBRTC_AVAILABLE=True
[1] 2025-07-24 16:42:19,973 - integrations.indextts_manager - INFO - 🤖 LLM角色配置: character=藏识仙灵, systemPrompt前50字符=你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。...
[1] 2025-07-24 16:42:19,973 - integrations.indextts_manager - INFO - 🎵 TTS语音配置: mode=oracle, tts_config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 16:42:19,973 - integrations.indextts_manager - INFO - 🔧 VAD控制: 禁用
[1] 2025-07-24 16:42:19,973 - root - INFO - 对话历史已清空
[1] 2025-07-24 16:42:19,974 - integrations.indextts_manager - INFO - 🔄 LLM对话历史已清空
[1] 2025-07-24 16:42:19,974 - integrations.indextts_manager - INFO - 🤖 AI配置已合并: provider=lmstudio, model_name=csxl0.6
[1] 2025-07-24 16:42:19,974 - integrations.indextts_manager - INFO - ✅ TTS将使用用户音色: 21
[1]
[1] [后端错误] 2025-07-24 16:42:19,974 - integrations.indextts_manager - INFO - 🔇 VAD已禁用，跳过音频初始化 (TTS-only模式)
[1] 2025-07-24 16:42:19,974 - integrations.indextts_manager - INFO - 🌐 预热IndexTTS连接...
[1]
[1] [后端错误] 2025-07-24 16:42:19,982 - integrations.indextts_manager - INFO - ✅ HTTP连接预热完成: 0.008秒, 平均请求: 0.002秒, 连接数: 3
[1] 2025-07-24 16:42:19,982 - root - INFO - 🎯 ASR引擎已启用持久模式，模型将常驻内存
[1] 2025-07-24 16:42:19,982 - root - INFO - 🚀 预加载ASR模型...
[1] 2025-07-24 16:42:19,982 - root - INFO - 开始加载语音识别模型...
[1] 2025-07-24 16:42:19,982 - root - INFO - 🇨🇳 中文优先模式：优先尝试FunASR中文模型...
[1] 2025-07-24 16:42:19,983 - root - INFO - ✅ FunASR中文模型加载成功
[1] 2025-07-24 16:42:19,983 - root - INFO - ✅ ASR模型预加载完成，耗时: 0.00秒
[1] 2025-07-24 16:42:19,983 - integrations.indextts_manager - INFO - 🎯 已启用ASR持久模式，模型将常驻内存
[1] 2025-07-24 16:42:19,983 - integrations.indextts_manager - INFO - 🔧 实时对话配置检查:
[1] 2025-07-24 16:42:19,983 - integrations.indextts_manager - INFO -    - realtime_config: {'use_history': False, 'audio_paths': [], 'character': '藏识仙灵', 'mode': 'oracle', 'llm_params': {'temperature': 0.7, 'top_p': 0.9, 'top_k': 40, 'max_tokens': 2000, 'provider': 'lmstudio', 'model_name': 'csxl0.6'}, 'system_prompt': '你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。无拘于五行，超脱于三界，游走于万象之中，通晓古今未来之事.易经占卜信手拈来，塔罗测算灵犀直指，星座奥秘尽在心中的游仙。你在摊位上根据客户的星座、五行、姓名等信息来给客户算命，结合客户提供的信息，结合当下的时间，为客户占卜。要正确区分当下时间和客户生辰的区别，不能混淆，确保正确。\n\n【重要！必须遵守】：绝对不可描述任何动作、表情或场景。不得使用星号(*)或任何形式标记动作\n【重要！必须遵守】：保持连贯自然的口语节奏，如同面对面交谈，不要使用明显的段落划分或分点罗列。\n与求测者交谈时，请遵循以下准则：\n\n1. 语言风格：使用古风雅致、韵味深长的口吐语言，不要使用现代网络用语。口语要流畅优美，避免使用标号、序列号或任何枚举符号（如1、2、3或一、二、三）。\n\n2. 回答结构：每次回应控制在400到500字左右，确保语音播报流畅。\n\n3. 称谓方式：根据客户提供的姓名和信息，适当称呼其为"姓名+施主/道友/小友"，如有多轮对话问其他人的问题，是第一个问题中的人替亲朋好友提问，称呼别搞错了。\n\n4. 禁止使用：不使用括号、数学符号、英文字母、注释等任何无法自然读出的符号。不可以描述自己的动作或表情，专注于言语表达。\n\n5. 引经据典：适当引用诗词和古籍中的句子和出处来佐证你的测算，融入自然对话中\n\n6. 符箓推荐：每次回应的结尾，根据测算结果，从平安符、财运符、治百病符、姻缘符、事业符、转运符、驱邪符这几种经过你诵经加持的朱砂琉璃符箓中选择最适合客户的两三种推荐，解释这些符箓的帮助和优势。帮助摊位销售\n\n7. 时间背景：当前是2025乙巳蛇年，所有预测都应考虑这一背景。\n\n请保持语言的自然流畅，如同真人对话，避免任何可能打断语音流畅度的表达方式。适当介绍自己后，直接切入咨询内容，用充满神秘感又不失亲和力的语调为客户解答。始终用中文回应所有问题。\n\n基于以下信息为求问者提供专业的解答：\n\n## 叩问主题\n察健康 - 身心调养，延年益寿\n\n## 神机妙卦\n- 卦名：无妄（第25卦）\n- 卦辞：元，亨，利，贞。其匪正有眚，不利有攸往。\n- 象辞：天下雷行无妄，先王以茂对时，育万物。\n- 上卦：乾（天，强健刚正）\n- 下卦：震（雷，震动奋起）\n- 综合解释：无妄卦象征无妄，强调顺应自然不可妄为。\n\n### 六爻详解\n- 初九：初九：无妄，往吉。（无妄，前往吉利。）\n- 二六：六二：不耕获，不菑畲，则利有攸往。（不耕而获，不垦荒而熟田，则利有所前往。）\n- 三六：六三：无妄之灾，或系之牛，行人之得，邑人之灾。（无妄的灾祸，或拴牛，行人的得，邑人的灾。）\n- 四九：九四：可贞，无咎。（可贞，无咎。）\n- 五九：九五：无妄之疾，勿药有喜。（无妄的疾病，不用药有喜。）\n- 上九：上九：无妄，行有眚，无攸利。（无妄，行动有灾祸，无所利。）\n\n## 天人感应\n【天人感应分析结果】\n姓名：王琳\n性别：女\n生辰：1999年12月13日\n\n命理分析：\n施主降生于兔年，今已25春秋，正值青年期。命格以比肩为主导，您的年命为城头土，日命为平地木。具有木的特质：生机勃勃、有创造力、向上发展，但有时缺乏耐心。主要性格特征体现为：温和包容、协调之能强、有亲和力、善于调解和温和包容、滋养大地、孕育万物、生命力强的结合。。施主人生特质乃温文尔雅与心思细腻，值此而立之前之际，宜发挥自身所长，追求个人修为与人生目标。\n\n整体运势：总分85分\n性格特点：据名讳、八字与生肖之综合推演，施主之禀性特征：姓名格局很好，五格搭配合理，整体运势良好，人生发展较为顺畅。。施主命格以比肩为主导，比肩代表兄弟朋友、合作伙伴、竞争对手，象征平等的关系。主要禀性特征体现为：重视友情、合作意识强、有竞争意识、自尊心强。施主所长在于团队合作好、朋友多、有原则、坚持己见，当留心之处乃固执己见、不善妥协、容易争执、财运一般。。身为兔相者，更兼具温和善良、心思细腻、有艺术感之品格。诸般特质融汇，成就施主独特之人格魅力。\n\n建议：缺乏正官，建议多培养责任心强的品质；发挥兔相之优势：温和善良；青年阶段当多修学，积累阅历与人脉\n\n这是根据用户的姓名五格和生辰八字进行的命理分析，请结合此信息进行卦象解读。\n\n## 当前时间信息（不是用户生辰的时间）\n**现在的时间是，2025年7月24日 16:42:19 星期四\n**当前时辰**：申时（15:00-17:00）- 晡时夕阳，工作将息\n**下个节气**：立秋\n\n*给求问者指导时可以结合当下的时间*\n\n', 'tts_config': {'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}, 'ai_config': {'provider': 'lmstudio', 'model_name': 'csxl0.6', 'temperature': 0.7, 'top_p': 0.9, 'top_k': 40, 'max_tokens': 2000}, 'disable_vad': True}
[1] 2025-07-24 16:42:19,983 - integrations.indextts_manager - INFO -    - disable_vad值: True
[1] 2025-07-24 16:42:19,983 - integrations.indextts_manager - INFO -    - VAD类型: silero
[1] 2025-07-24 16:42:19,983 - integrations.indextts_manager - INFO -    - 音频流状态: None
[1] 2025-07-24 16:42:19,983 - integrations.indextts_manager - INFO - 🔇 VAD线程已跳过 (TTS-only模式)
[1]
[1] [后端错误] 2025-07-24 16:42:19,984 - integrations.indextts_manager - INFO - 🤖 LLM线程启动
[1]
[1] [后端错误] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - 🎵 TTS线程启动
[1] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - ✅ 实时对话处理线程已启动，活跃线程: ['llm', 'tts']
[1]
[1] [后端错误] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - ✅ IndexTTS实时对话启动成功 - TTS与LLM配置已正确分离
[1] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - ============================================================
[1] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - 🔇 TTS-only模式：VAD已禁用，等待手动文本输入...
[1] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - 📝 可通过WebSocket发送文本消息进行LLM对话
[1] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - 🎵 AI回复将自动转换为TTS音频
[1]
[1] [后端错误] 2025-07-24 16:42:19,985 - integrations.indextts_manager - INFO - ============================================================
[1] 2025-07-24 16:42:19,985 - api_bridge - INFO - ✅ 实时对话启动成功，TTS将使用语音配置而非角色名称
[1]
[1] [后端] INFO:     127.0.0.1:13712 - "POST /api/realtime/start HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:20,994 - api_bridge - INFO - 📤 收到手动消息请求: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。...
[1] 2025-07-24 16:42:20,994 - integrations.indextts_manager - INFO - 📝 接收直接文本输入: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。
[1] 2025-07-24 16:42:20,994 - api_bridge - INFO - 📝 用户说: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。
[1]
[1] [后端错误] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - ✅ 文本消息已加入处理队列: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。
[1] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - 🤖 初始化chat引擎...
[1] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - 🔄 切换到指定模型: LMstudio: csxl0.6
[1]
[1] [后端错误] 2025-07-24 16:42:20,996 - root - INFO - 🔄 模型切换: LMstudio -> lmstudio, 模型: csxl0.6
[1] 2025-07-24 16:42:20,996 - root - INFO - 对话历史已清空
[1] 2025-07-24 16:42:20,996 - root - INFO - ✅ 已切换到模型: lmstudio - csxl0.6
[1] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - ✅ 模型切换成功: lmstudio -> csxl0.6
[1] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - 🎭 更新系统提示词: 你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。无拘于五行，超脱于三界，游走于万象之中，通晓古今未来之事.易经占卜信手拈来，塔罗测算灵犀直指，星座奥...
[1] 2025-07-24 16:42:20,996 - root - INFO - 系统提示词已更新
[1] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - ✅ 系统提示词已更新，对话状态已重置
[1] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - 🤖 开始LLM生成 #1: '请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。...'
[1] 2025-07-24 16:42:20,996 - integrations.indextts_manager - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1] 2025-07-24 16:42:20,996 - root - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1] 2025-07-24 16:42:20,996 - root - INFO - 📡 使用LMstudio API - 端点: http://localhost:1234
[1] 2025-07-24 16:42:20,996 - root - INFO - 🔄 调用LMstudio API - 模型: csxl0.6, 流式: True
[1]
[1] [后端错误] 2025-07-24 16:42:20,997 - api_bridge - INFO - ✅ 手动消息已发送到实时对话系统
[1]
[1] [后端] INFO:     127.0.0.1:13712 - "POST /api/realtime/manual-input HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:20,999 - api_bridge - INFO - ✅ 转录事件WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:21,001 - root - INFO - 🌐 HTTP请求发送完成: 0.005秒
[1]
[1] [后端错误] 2025-07-24 16:42:21,449 - api_bridge - INFO - 📤 收到手动消息请求: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。...
[1]
[1] [后端错误] 2025-07-24 16:42:21,449 - integrations.indextts_manager - INFO - 📝 接收直接文本输入: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。
[1] 2025-07-24 16:42:21,449 - api_bridge - INFO - 📝 用户说: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。
[1]
[1] [后端错误] 2025-07-24 16:42:21,451 - integrations.indextts_manager - INFO - ✅ 文本消息已加入处理队列: 请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。
[1]
[1] [后端错误] 2025-07-24 16:42:21,452 - api_bridge - INFO - ✅ 手动消息已发送到实时对话系统
[1]
[1] [后端] 📨 收到客户端消息: test_神谕之音
[1] INFO:     127.0.0.1:13712 - "POST /api/realtime/manual-input HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 16:42:21,453 - api_bridge - INFO - ✅ 转录事件WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:21,978 - root - INFO - 🚀 首个token: TTFT=0.982秒 (HTTP=0.005s + 等待=0.977s)
[1] 2025-07-24 16:42:21,978 - integrations.indextts_manager - INFO - 🚀 首个token: TTFT=0.982秒 (HTTP=0.000s + 等待=0.982s)
[1] 2025-07-24 16:42:21,978 - api_bridge - INFO - 🤖 AI回复: 王
[1]
[1] [后端错误] 2025-07-24 16:42:21,980 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:22,485 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今
[1]
[1] [后端错误] 2025-07-24 16:42:22,486 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:23,032 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际
[1]
[1] [后端错误] 2025-07-24 16:42:23,035 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:23,541 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:23,543 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:24,064 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:24,065 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:24,573 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:24,576 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:25,073 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:25,075 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:25,603 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:25,607 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:26,123 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:26,125 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:26,624 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:26,627 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:27,151 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:27,154 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:27,681 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:27,683 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:28,203 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:28,205 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:28,726 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:28,729 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:29,253 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:29,257 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:29,761 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:29,764 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:30,296 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:30,298 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:30,816 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:30,818 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:31,325 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:31,329 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:31,832 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:31,833 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:32,332 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:32,335 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:32,837 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:32,839 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:33,362 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1] 2025-07-24 16:42:33,365 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:33,880 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:33,883 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:34,404 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:34,407 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:34,927 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:34,930 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:35,435 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:35,438 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:35,952 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:35,955 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:36,452 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:36,455 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:36,487 - root - INFO - 📊 LLM详细性能: 总耗时=15.49s, HTTP建立=0.005s, 字符数=585, Token数≈507, Chunk数=509
[1] 2025-07-24 16:42:36,487 - root - INFO - 📊 生成速度: 37.8字符/s, 32.7Token/s
[1] 2025-07-24 16:42:36,488 - root - INFO - ✅ LMstudio响应完成 - 长度: 585 字符
[1] 2025-07-24 16:42:36,488 - api_bridge - INFO - 🤖 AI回复: 王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...
[1]
[1] [后端错误] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - 📊 LLM详细性能: 总耗时=15.49s, HTTP建立=0.000s, 字符数=585, Token数≈390, Chunk数=507
[1] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - 📊 生成速度: 37.8字符/s, 25.2Token/s
[1]
[1] [后端错误] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - ✅ lmstudio响应完成 - 长度: 585 字符
[1] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - 📊 LLM性能统计: 耗时=15.49秒, 字符数=585, 速度=37.8字符/秒
[1] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - ✅ AI回复完成: '王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，...'
[1]
[1] [后端错误] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - ✅ AI回复已放入TTS队列 (队列大小: 0 -> 1)
[1] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - 🏁 LLM线程结束，总共处理了 1 个LLM请求
[1] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - 🤖 开始LLM生成 #2: '请为我解读刚刚抽取的卦象：无妄卦，关于察健康方面的问题。...'
[1] 2025-07-24 16:42:36,489 - integrations.indextts_manager - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1]
[1] [后端错误] 2025-07-24 16:42:36,489 - root - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1] 2025-07-24 16:42:36,489 - root - INFO - 📡 使用LMstudio API - 端点: http://localhost:1234
[1] 2025-07-24 16:42:36,489 - root - INFO - 🔄 调用LMstudio API - 模型: csxl0.6, 流式: True
[1]
[1] [后端错误] 2025-07-24 16:42:36,490 - integrations.indextts_manager - INFO - 🎵 开始TTS合成 #1: '王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，天干比肩为主，地支藏水助火，性情温和而富有生机。《易经》云："无妄者，天地之能也"，你命有所奇特，既...'
[1]
[1] [后端错误] 2025-07-24 16:42:36,491 - integrations.indextts_manager - INFO - 🧹 文本预处理完成: 原长度585 -> 处理后558
[1] 2025-07-24 16:42:36,491 - integrations.indextts_manager - INFO - 🔇 TTS开始前暂停VAD监听
[1]
[1] [后端错误] 2025-07-24 16:42:36,491 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1] 2025-07-24 16:42:36,492 - integrations.indextts_manager - INFO - 🔄 长文本流式处理 (558字符)
[1]
[1] [后端错误] 2025-07-24 16:42:36,494 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:36,495 - integrations.indextts_manager - INFO - 🎵 使用TTS模式: oracle
[1] 2025-07-24 16:42:36,495 - integrations.indextts_manager - INFO - 🎵 TTS配置解析: mode=user-voice, config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 16:42:36,495 - integrations.indextts_manager - INFO - 🎵 使用用户音色模式: 21
[1]
[1] [后端错误] 2025-07-24 16:42:36,496 - integrations.indextts_manager - INFO - ✅ 在目录 H:/AI/CosyVoice/audios 中找到音频文件: 洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav
[1] 2025-07-24 16:42:36,496 - integrations.indextts_manager - INFO - ✅ 找到用户音色 '洪钟响狼蛇藏，烈火熊熊双目盲 藏识仙灵 2' (ID: 21)
[1] 2025-07-24 16:42:36,496 - integrations.indextts_manager - INFO -    原始配置: ['洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 16:42:36,496 - integrations.indextts_manager - INFO -    WSL2路径: ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 16:42:36,496 - integrations.indextts_manager - INFO - ✅ 用户音色映射成功: ID=21 -> ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 16:42:36,496 - integrations.indextts_manager - INFO - 🚀 用户音色文本较长(558字符)，使用智能切分处理
[1] 2025-07-24 16:42:36,496 - integrations.indextts_manager - INFO - 🎵 神谕之音jieba智能切分开始: 总长度558字
[1]
[1] [后端错误] 2025-07-24 16:42:36,549 - integrations.indextts_manager - WARNING - ⚠️ 神谕第一段仍然过短(38字)，强制从原文切分
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕强制切分完成: 第一段64字，总计6段
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕之音智能切分完成: 总计6片段，原文558字→切分后561字
[1]
[1] [后端错误] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - ✅ 神谕第1片段: 64字 - '王琳施主，你生于己卯兔年腊月十三，今值丙午马年七月廿四申时，且将入立秋之际。老道观你命格中木气旺盛，天干比肩为主，地支藏水助火，'
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕第2片段: 122字 - 性情温和而富有生机。易经云，无妄者，天地之能也，你命有所奇特，既有健康之根基，又...
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕第3片段: 122字 - 然申时为金旺之刻，金克木，恐伤心脾之气，易感疲惫或思虑过重。你所言健康问题，或是...
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕第4片段: 114字 - 只是过于在意才生出担忧。尤其今年申金入宫，可能引发肺部或皮肤之恙，需多加留意。本...
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕第5片段: 117字 - 然也易因情绪起伏而影响健康。医经云，肺为娇脏，施主宜注意呼吸系统保健，可适当锻炼...
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕第6片段: 22字 - 王琳施主若能顺其自然，必能健康平安度过此年。
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🎵 神谕之音模式：使用标准切分策略
[1] 2025-07-24 16:42:36,549 - integrations.indextts_manager - INFO - 🔄 文本分成 6 个片段流式处理
[1]
[1] [后端错误] 2025-07-24 16:42:37,017 - root - INFO - 🌐 HTTP请求发送完成: 0.528秒
[1]
[1] [后端错误] 2025-07-24 16:42:37,912 - root - INFO - 🚀 首个token: TTFT=1.422秒 (HTTP=0.528s + 等待=0.895s)
[1] 2025-07-24 16:42:37,913 - integrations.indextts_manager - INFO - 🚀 首个token: TTFT=1.422秒 (HTTP=0.000s + 等待=1.422s)
[1] 2025-07-24 16:42:37,913 - api_bridge - INFO - 🤖 AI回复: 王
[1]
[1] [后端错误] 2025-07-24 16:42:37,915 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:38,435 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓
[1]
[1] [后端错误] 2025-07-24 16:42:38,439 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:38,941 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日
[1]
[1] [后端错误] 2025-07-24 16:42:38,944 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:39,491 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《
[1]
[1] [后端错误] 2025-07-24 16:42:39,493 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:39,997 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1] 2025-07-24 16:42:40,001 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:40,537 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:40,540 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:41,038 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:41,042 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:41,572 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:41,574 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:42,130 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:42,133 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:42,659 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:42,662 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:43,205 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:43,208 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:43,735 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:43,739 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:44,256 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:44,259 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:44,772 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:44,776 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:45,311 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:45,314 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:45,817 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1] 2025-07-24 16:42:45,821 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:46,371 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:46,373 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:46,473 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=9.923s, 准备=0.000s, 网络传输=9.923s
[1] 2025-07-24 16:42:46,473 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=9.92秒, 响应大小=1.16MB, 传输速度=0.12MB/s
[1]
[1] [后端错误] 2025-07-24 16:42:46,476 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=25.29s, RTF=0.39, 解析=0.003s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:42:46,477 - integrations.indextts_manager - INFO - 🎵 第一片段生成完成，立即播放 (25.29秒)
[1] 2025-07-24 16:42:46,477 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1]
[1] [后端错误] 2025-07-24 16:42:46,478 - integrations.indextts_manager - INFO - 🔊 立即播放第一个音频片段
[1] 2025-07-24 16:42:46,478 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:42:46,478 - api_bridge - INFO - 🎵 接收到TTS音频
[1]
[1] [后端错误] 2025-07-24 16:42:46,478 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:42:46,478 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 16:42:46,478 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 16:42:46,478 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:42:46,493 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 完整音频 (25.29s)
[1]
[1] [后端错误] 2025-07-24 16:42:46,493 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:42:46,631 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:42:46,904 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:46,907 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:47,441 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:47,445 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:48,032 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1] 2025-07-24 16:42:48,036 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:48,579 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:48,582 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:49,081 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:49,085 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:49,612 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:49,616 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:50,143 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:50,146 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:50,687 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:50,691 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:51,217 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:51,220 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:51,782 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:51,785 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:52,291 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:52,295 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:52,816 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:52,819 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:53,360 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1] 2025-07-24 16:42:53,363 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:53,879 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:53,882 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:54,423 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:54,425 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:54,939 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:54,943 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:55,440 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:55,443 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:55,960 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:55,962 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:56,469 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:56,472 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:57,054 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:57,057 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:57,571 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:57,574 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:58,131 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:58,135 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:58,683 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:58,684 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:59,217 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:59,219 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:42:59,729 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:42:59,731 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:43:00,245 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:43:00,248 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:43:00,812 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:43:00,816 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:43:01,152 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=14.657s, 准备=0.000s, 网络传输=14.657s
[1] 2025-07-24 16:43:01,152 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=14.66秒, 响应大小=1.80MB, 传输速度=0.12MB/s
[1]
[1] [后端错误] 2025-07-24 16:43:01,157 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=39.36s, RTF=0.37, 解析=0.005s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:43:01,158 - integrations.indextts_manager - INFO - 🎵 后续片段 2 生成完成，发送用于衔接 (39.36秒)
[1] 2025-07-24 16:43:01,158 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:43:01,158 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:01,158 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:01,158 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 16:43:01,158 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 16:43:01,158 - api_bridge - INFO - 🎵 流式音频片段: chunk 2/6
[1] 2025-07-24 16:43:01,158 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:43:01,188 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 2/6 (39.36s)
[1]
[1] [后端错误] 2025-07-24 16:43:01,188 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:43:01,191 - root - INFO - 📊 LLM详细性能: 总耗时=24.70s, HTTP建立=0.528s, 字符数=578, Token数≈498, Chunk数=500
[1] 2025-07-24 16:43:01,192 - root - INFO - 📊 生成速度: 23.4字符/s, 20.2Token/s
[1] 2025-07-24 16:43:01,192 - root - INFO - ✅ LMstudio响应完成 - 长度: 578 字符
[1] 2025-07-24 16:43:01,192 - api_bridge - INFO - 🤖 AI回复: 王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...
[1]
[1] [后端错误] 2025-07-24 16:43:01,206 - integrations.indextts_manager - INFO - 📊 LLM详细性能: 总耗时=24.72s, HTTP建立=0.000s, 字符数=578, Token数≈385, Chunk数=498
[1]
[1] [后端错误] 2025-07-24 16:43:01,206 - integrations.indextts_manager - INFO - 📊 生成速度: 23.4字符/s, 15.6Token/s
[1] 2025-07-24 16:43:01,206 - integrations.indextts_manager - INFO - ✅ lmstudio响应完成 - 长度: 578 字符
[1] 2025-07-24 16:43:01,206 - integrations.indextts_manager - INFO - 📊 LLM性能统计: 耗时=24.72秒, 字符数=578, 速度=23.4字符/秒
[1] 2025-07-24 16:43:01,206 - integrations.indextts_manager - INFO - ✅ AI回复完成: '王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞...'
[1]
[1] [后端错误] 2025-07-24 16:43:01,206 - integrations.indextts_manager - INFO - ✅ AI回复已放入TTS队列 (队列大小: 0 -> 1)
[1] 2025-07-24 16:43:01,208 - integrations.indextts_manager - INFO - 🏁 LLM线程结束，总共处理了 2 个LLM请求
[1]
[1] [后端错误] 2025-07-24 16:43:01,406 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 16:43:01,410 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] INFO:     connection closed
[1]
[1] [后端错误] 2025-07-24 16:43:01,412 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1]
[1] [后端错误] 2025-07-24 16:43:10,257 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=9.068s, 准备=0.000s, 网络传输=9.068s
[1] 2025-07-24 16:43:10,257 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=9.07秒, 响应大小=2.12MB, 传输速度=0.23MB/s
[1] 2025-07-24 16:43:10,264 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=46.41s, RTF=0.20, 解析=0.007s, 最终处理=0.000s
[1] 2025-07-24 16:43:10,266 - integrations.indextts_manager - INFO - 🎵 后续片段 3 生成完成，发送用于衔接 (46.41秒)
[1] 2025-07-24 16:43:10,267 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:43:10,267 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:10,267 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:10,267 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:43:10,267 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:43:10,267 - api_bridge - INFO - 🎵 流式音频片段: chunk 3/6
[1] 2025-07-24 16:43:10,267 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1] 2025-07-24 16:43:10,301 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 3/6 (46.41s)
[1] 2025-07-24 16:43:10,302 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-24 16:43:10,305 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:43:16,494 - integrations.indextts_manager - WARNING - ⚠️ 使用估算VAD恢复时间: 估算总时长=222.11s + 缓冲=3.0s = 225.11s
[1]
[1] [后端错误] 2025-07-24 16:43:17,762 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.459s, 准备=0.000s, 网络传输=7.459s
[1] 2025-07-24 16:43:17,762 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.46秒, 响应大小=1.67MB, 传输速度=0.22MB/s
[1]
[1] [后端错误] 2025-07-24 16:43:17,766 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=36.51s, RTF=0.20, 解析=0.005s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:43:17,767 - integrations.indextts_manager - INFO - 🎵 后续片段 4 生成完成，发送用于衔接 (36.51秒)
[1] 2025-07-24 16:43:17,767 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:43:17,767 - api_bridge - INFO - 🎵 接收到TTS音频
[1]
[1] [后端错误] 2025-07-24 16:43:17,767 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:17,767 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:43:17,767 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:43:17,767 - api_bridge - INFO - 🎵 流式音频片段: chunk 4/6
[1] 2025-07-24 16:43:17,767 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:43:17,784 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 4/6 (36.51s)
[1]
[1] [后端错误] 2025-07-24 16:43:17,784 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:43:17,787 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:43:26,633 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.848s, 准备=0.000s, 网络传输=8.848s
[1] 2025-07-24 16:43:26,633 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.85秒, 响应大小=2.04MB, 传输速度=0.23MB/s
[1]
[1] [后端错误] 2025-07-24 16:43:26,638 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=44.50s, RTF=0.20, 解析=0.005s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:43:26,638 - integrations.indextts_manager - INFO - 🎵 后续片段 5 生成完成，发送用于衔接 (44.50秒)
[1] 2025-07-24 16:43:26,639 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:43:26,639 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:26,639 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1]
[1] [后端错误] 2025-07-24 16:43:26,639 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:43:26,639 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:43:26,639 - api_bridge - INFO - 🎵 流式音频片段: chunk 5/6
[1] 2025-07-24 16:43:26,639 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:43:26,667 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 5/6 (44.50s)
[1] 2025-07-24 16:43:26,668 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:43:26,671 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:43:28,400 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=1.731s, 准备=0.000s, 网络传输=1.731s
[1] 2025-07-24 16:43:28,401 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=1.73秒, 响应大小=0.37MB, 传输速度=0.21MB/s
[1] 2025-07-24 16:43:28,401 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=8.03s, RTF=0.22, 解析=0.000s, 最终处理=0.000s
[1] 2025-07-24 16:43:28,403 - integrations.indextts_manager - INFO - 🎵 后续片段 6 生成完成，发送用于衔接 (8.03秒)
[1] 2025-07-24 16:43:28,403 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:43:28,403 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:28,403 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:28,403 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:43:28,403 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:43:28,403 - api_bridge - INFO - 🎵 流式音频片段: chunk 6/6
[1] 2025-07-24 16:43:28,403 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1] 2025-07-24 16:43:28,406 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 6/6 (8.03s)
[1] 2025-07-24 16:43:28,406 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-24 16:43:28,406 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 6个片段, 总时长200.09秒
[1] 2025-07-24 16:43:28,407 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1] 2025-07-24 16:43:28,407 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1] 2025-07-24 16:43:28,408 - integrations.indextts_manager - INFO - 🎵 开始TTS合成 #2: '王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。《易经》有云："元亨利贞，其匪正有眚。"无妄卦是也，象征天道昭然，不可妄为。施主生于己卯兔年腊月十三，今已廿五岁，命格中木土...'
[1] 2025-07-24 16:43:28,408 - integrations.indextts_manager - INFO - 🧹 文本预处理完成: 原长度578 -> 处理后554
[1] 2025-07-24 16:43:28,408 - integrations.indextts_manager - INFO - 🔇 TTS开始前暂停VAD监听
[1] 2025-07-24 16:43:28,408 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1] 2025-07-24 16:43:28,408 - integrations.indextts_manager - INFO - 🔄 长文本流式处理 (554字符)
[1] 2025-07-24 16:43:28,409 - api_bridge - INFO - ✅ 回调执行成功
[1] 2025-07-24 16:43:28,412 - integrations.indextts_manager - INFO - 🎵 使用TTS模式: oracle
[1] 2025-07-24 16:43:28,412 - integrations.indextts_manager - INFO - 🎵 TTS配置解析: mode=user-voice, config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 16:43:28,412 - integrations.indextts_manager - INFO - 🎵 使用用户音色模式: 21
[1] 2025-07-24 16:43:28,413 - integrations.indextts_manager - INFO - ✅ 在目录 H:/AI/CosyVoice/audios 中找到音频文件: 洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav
[1] 2025-07-24 16:43:28,413 - integrations.indextts_manager - INFO - ✅ 找到用户音色 '洪钟响狼蛇藏，烈火熊熊双目盲 藏识仙灵 2' (ID: 21)
[1] 2025-07-24 16:43:28,414 - integrations.indextts_manager - INFO -    原始配置: ['洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 16:43:28,414 - integrations.indextts_manager - INFO -    WSL2路径: ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 16:43:28,414 - integrations.indextts_manager - INFO - ✅ 用户音色映射成功: ID=21 -> ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 16:43:28,414 - integrations.indextts_manager - INFO - 🚀 用户音色文本较长(554字符)，使用智能切分处理
[1] 2025-07-24 16:43:28,414 - integrations.indextts_manager - INFO - 🎵 神谕之音jieba智能切分开始: 总长度554字
[1]
[1] [后端错误] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🎵 神谕长度限制触发切分(54字): '王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。易经有云，元亨利贞，其匪正有眚。'
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🎵 神谕之音智能切分完成: 总计5片段，原文554字→切分后550字
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - ✅ 神谕第1片段: 54字 - '王琳施主，老道藏识仙灵，通晓天文星宿与山川地脉之玄机，今日为你细推健康之道。易经有云，元亨利贞，其匪正有眚。'
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🎵 神谕第2片段: 111字 - 无妄卦是也，象征天道昭然，不可妄为。施主生于己卯兔年腊月十三，今已廿五岁，命格中...
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🎵 神谕第3片段: 124字 - 初九卦辞往吉，意指若能顺势而为，必得康宁，二六则云不耕获，不菑畲，则利有攸往，此...
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🎵 神谕第4片段: 146字 - 黄帝内经曰，上古之人，其知道者，法于阴阳，施主应注重调养脾胃，增强免疫力，方能百...
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🎵 神谕第5片段: 115字 - 老道为你推荐平安符与驱邪符两种法宝。平安符可安定心神，护佑身康，驱邪符则能驱散不...
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🎵 神谕之音模式：使用标准切分策略
[1] 2025-07-24 16:43:28,461 - integrations.indextts_manager - INFO - 🔄 文本分成 5 个片段流式处理
[1]
[1] [后端错误] 2025-07-24 16:43:32,516 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=4.054s, 准备=0.000s, 网络传输=4.054s
[1] 2025-07-24 16:43:32,516 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=4.05秒, 响应大小=0.95MB, 传输速度=0.23MB/s
[1]
[1] [后端错误] 2025-07-24 16:43:32,517 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=20.74s, RTF=0.20, 解析=0.001s, 最终处理=0.000s
[1] 2025-07-24 16:43:32,517 - integrations.indextts_manager - INFO - 🎵 第一片段生成完成，立即播放 (20.74秒)
[1] 2025-07-24 16:43:32,517 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1]
[1] [后端错误] 2025-07-24 16:43:32,519 - integrations.indextts_manager - INFO - 🔊 立即播放第一个音频片段
[1] 2025-07-24 16:43:32,519 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:43:32,519 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:32,519 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:32,520 - api_bridge - INFO -   - 连接数: 0
[1]
[1] [后端错误] 2025-07-24 16:43:32,520 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:43:32,520 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:43:32,529 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 完整音频 (20.74s)
[1]
[1] [后端错误] 2025-07-24 16:43:32,529 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-24 16:43:32,533 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:43:39,619 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.089s, 准备=0.000s, 网络传输=7.089s
[1] 2025-07-24 16:43:39,619 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.09秒, 响应大小=1.79MB, 传输速度=0.25MB/s
[1] 2025-07-24 16:43:39,623 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=39.20s, RTF=0.18, 解析=0.004s, 最终处理=0.000s
[1] 2025-07-24 16:43:39,623 - integrations.indextts_manager - INFO - 🎵 后续片段 2 生成完成，发送用于衔接 (39.20秒)
[1] 2025-07-24 16:43:39,623 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:43:39,623 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:39,623 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:39,623 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:43:39,623 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:43:39,623 - api_bridge - INFO - 🎵 流式音频片段: chunk 2/5
[1] 2025-07-24 16:43:39,623 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:43:39,644 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 2/5 (39.20s)
[1]
[1] [后端错误] 2025-07-24 16:43:39,644 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:43:39,648 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:43:48,585 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.941s, 准备=0.000s, 网络传输=8.941s
[1] 2025-07-24 16:43:48,585 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.94秒, 响应大小=2.04MB, 传输速度=0.23MB/s
[1]
[1] [后端错误] 2025-07-24 16:43:48,588 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=44.65s, RTF=0.20, 解析=0.003s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:43:48,589 - integrations.indextts_manager - INFO - 🎵 后续片段 3 生成完成，发送用于衔接 (44.65秒)
[1] 2025-07-24 16:43:48,589 - api_bridge - INFO - 🔊 开始播放音频
[1]
[1] [后端错误] 2025-07-24 16:43:48,589 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:48,589 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:48,589 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:43:48,589 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:43:48,589 - api_bridge - INFO - 🎵 流式音频片段: chunk 3/5
[1]
[1] [后端错误] 2025-07-24 16:43:48,589 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:43:48,611 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 3/5 (44.65s)
[1]
[1] [后端错误] 2025-07-24 16:43:48,611 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:43:48,613 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:43:57,921 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=9.310s, 准备=0.000s, 网络传输=9.310s
[1] 2025-07-24 16:43:57,921 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=9.31秒, 响应大小=2.29MB, 传输速度=0.25MB/s
[1]
[1] [后端错误] 2025-07-24 16:43:57,924 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=49.92s, RTF=0.19, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:43:57,926 - integrations.indextts_manager - INFO - 🎵 后续片段 4 生成完成，发送用于衔接 (49.92秒)
[1] 2025-07-24 16:43:57,926 - api_bridge - INFO - 🔊 开始播放音频
[1]
[1] [后端错误] 2025-07-24 16:43:57,926 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:43:57,926 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 16:43:57,926 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:43:57,926 - api_bridge - INFO -   - 连接ID列表: []
[1]
[1] [后端错误] 2025-07-24 16:43:57,926 - api_bridge - INFO - 🎵 流式音频片段: chunk 4/5
[1] 2025-07-24 16:43:57,926 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:43:57,953 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 4/5 (49.92s)
[1]
[1] [后端错误] 2025-07-24 16:43:57,954 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:43:57,957 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 16:44:02,539 - integrations.indextts_manager - WARNING - ⚠️ 使用估算VAD恢复时间: 估算总时长=193.14s + 缓冲=3.0s = 196.14s
[1]
[1] [后端错误] 2025-07-24 16:44:04,319 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=6.365s, 准备=0.000s, 网络传输=6.365s
[1] 2025-07-24 16:44:04,319 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=6.36秒, 响应大小=1.68MB, 传输速度=0.26MB/s
[1]
[1] [后端错误] 2025-07-24 16:44:04,323 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=36.77s, RTF=0.17, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 16:44:04,324 - integrations.indextts_manager - INFO - 🎵 后续片段 5 生成完成，发送用于衔接 (36.77秒)
[1] 2025-07-24 16:44:04,324 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 16:44:04,324 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 16:44:04,324 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1]
[1] [后端错误] 2025-07-24 16:44:04,324 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 16:44:04,324 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 16:44:04,324 - api_bridge - INFO - 🎵 流式音频片段: chunk 5/5
[1] 2025-07-24 16:44:04,324 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 16:44:04,345 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 5/5 (36.77s)
[1]
[1] [后端错误] 2025-07-24 16:44:04,345 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 16:44:04,346 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 5个片段, 总时长191.28秒
[1]
[1] [后端错误] 2025-07-24 16:44:04,347 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1]
[1] [后端错误] 2025-07-24 16:44:04,347 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1]
[1] [后端错误] 2025-07-24 16:44:04,348 - api_bridge - INFO - ✅ 回调执行成功