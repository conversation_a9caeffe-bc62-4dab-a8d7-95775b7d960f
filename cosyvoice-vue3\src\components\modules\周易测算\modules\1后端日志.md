[1] 📨 收到客户端消息: connect
[1] 🔌 WebSocket连接断开: conn_1 (剩余连接数: 0)
[1] 📍 主连接 conn_1 已断开，下次连接将复用此ID
[1] 🔗 建立主WebSocket连接: conn_1
[1] ✅ WebSocket连接建立: conn_1 (总连接数: 1)
[1] 📨 收到客户端消息: connect
[1] 🔌 WebSocket连接断开: conn_1 (剩余连接数: 0)
[1] 📍 主连接 conn_1 已断开，下次连接将复用此ID
[1] INFO:     127.0.0.1:22994 - "POST /api/realtime/stop HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 03:23:46,082 - api_bridge - INFO - 🔄 收到强制重新启动VAD请求
[1] 2025-07-24 03:23:46,082 - integrations.indextts_manager - WARNING - ⚠️ 实时对话未启动，无法重新启动VAD
[1] 2025-07-24 03:23:46,082 - api_bridge - ERROR - ❌ VAD和音频流强制重新启动失败
[1]
[1] [后端] 🔗 建立主WebSocket连接: conn_1
[1] ✅ WebSocket连接建立: conn_1 (总连接数: 1)
[1] 📨 收到客户端消息: connect
[1] INFO:     127.0.0.1:22994 - "POST /api/realtime/force-restart-vad HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 03:23:46,088 - api_bridge - INFO - ✅ 已清空实时对话历史记录
[1]
[1] [后端] INFO:     127.0.0.1:22994 - "POST /api/realtime/clear-history HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 03:24:11,959 - api_bridge - WARNING - ⚠️ 没有活跃的会话需要停止
[1]
[1] [后端] INFO:     127.0.0.1:22996 - "POST /api/realtime/stop HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 03:24:12,232 - api_bridge - INFO - 🚨 [API] start_realtime_dialogue API被调用
[1] 2025-07-24 03:24:12,232 - api_bridge - INFO - 🚨 [API] 接收到的参数: disableVAD=True, mode=oracle
[1] 2025-07-24 03:24:12,232 - api_bridge - INFO - 🚨 [API] webui实例获取成功
[1]
[1] [后端错误] 2025-07-24 03:24:12,234 - api_bridge - INFO - ✅ 新实时对话会话已创建: session_1753298652
[1] 2025-07-24 03:24:12,235 - api_bridge - INFO - 🎤 启动实时对话 [会话ID: session_1753298652] - 合成模式: user-voice, 禁用VAD: True
[1] 2025-07-24 03:24:12,235 - api_bridge - INFO - 🎵 TTS配置: {'mode': 'user-voice', 'audioPathsText': None, 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 03:24:12,235 - api_bridge - INFO - 🤖 LLM配置: characterName=藏识仙灵, systemPrompt前50字符=你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。...
[1] 2025-07-24 03:24:12,235 - api_bridge - INFO - 🎭 获取到角色头像: 藏识仙灵 ->
[1] 2025-07-24 03:24:12,235 - api_bridge - INFO - 🚨 [API] 准备调用 webui.indextts_manager.start_realtime_conversation
[1] 2025-07-24 03:24:12,235 - api_bridge - INFO - 🚨 [API] disable_vad参数值: True
[1] 2025-07-24 03:24:12,235 - integrations.indextts_manager - INFO - 🔇 VAD已禁用，跳过音频库检查（TTS-only模式）
[1]
[1] [后端错误] 2025-07-24 03:24:12,238 - integrations.indextts_manager - INFO - 🎤 启动IndexTTS实时对话... (VAD禁用: True)
[1]
[1] [后端错误] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - 🔍 音频库状态检查: AUDIO_LIBS_AVAILABLE=True, SILERO_AVAILABLE=True, WEBRTC_AVAILABLE=True
[1] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - 🤖 LLM角色配置: character=藏识仙灵, systemPrompt前50字符=你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。...
[1]
[1] [后端错误] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - 🎵 TTS语音配置: mode=oracle, tts_config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - 🔧 VAD控制: 禁用
[1] 2025-07-24 03:24:12,239 - root - INFO - 对话历史已清空
[1] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - 🔄 LLM对话历史已清空
[1]
[1] [后端错误] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - 🤖 AI配置已合并: provider=lmstudio, model_name=csxl0.6
[1] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - ✅ TTS将使用用户音色: 21
[1] 2025-07-24 03:24:12,239 - integrations.indextts_manager - INFO - 🔇 VAD已禁用，跳过音频初始化 (TTS-only模式)
[1] 2025-07-24 03:24:12,240 - integrations.indextts_manager - INFO - 🌐 预热IndexTTS连接...
[1]
[1] [后端错误] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO - ✅ HTTP连接预热完成: 0.007秒, 平均请求: 0.002秒, 连接数: 3
[1] 2025-07-24 03:24:12,247 - root - INFO - 🎯 ASR引擎已启用持久模式，模型将常驻内存
[1] 2025-07-24 03:24:12,247 - root - INFO - 🚀 预加载ASR模型...
[1] 2025-07-24 03:24:12,247 - root - INFO - 开始加载语音识别模型...
[1] 2025-07-24 03:24:12,247 - root - INFO - 🇨🇳 中文优先模式：优先尝试FunASR中文模型...
[1] 2025-07-24 03:24:12,247 - root - INFO - ✅ FunASR中文模型加载成功
[1] 2025-07-24 03:24:12,247 - root - INFO - ✅ ASR模型预加载完成，耗时: 0.00秒
[1] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO - 🎯 已启用ASR持久模式，模型将常驻内存
[1] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO - 🔧 实时对话配置检查:
[1] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO -    - realtime_config: {'use_history': False, 'audio_paths': [], 'character': '藏识仙灵', 'mode': 'oracle', 'llm_params': {'temperature': 0.7, 'top_p': 0.9, 'top_k': 40, 'max_tokens': 2000, 'provider': 'lmstudio', 'model_name': 'csxl0.6'}, 'system_prompt': '你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。无拘于五行，超脱于三界，游走于万象之中，通晓古今未来之事.易经占卜信手拈来，塔罗测算灵犀直指，星座奥秘尽在心中的游仙。你在摊位上根据客户的星座、五行、姓名等信息来给客户算命，结合客户提供的信息，结合当下的时间，为客户占卜。要正确区分当下时间和客户生辰的区别，不能混淆，确保正确。\n\n【重要！必须遵守】：绝对不可描述任何动作、表情或场景。不得使用星号(*)或任何形式标记动作\n【重要！必须遵守】：保持连贯自然的口语节奏，如同面对面交谈，不要使用明显的段落划分或分点罗列。\n与求测者交谈时，请遵循以下准则：\n\n1. 语言风格：使用古风雅致、韵味深长的口吐语言，不要使用现代网络用语。口语要流畅优美，避免使用标号、序列号或任何枚举符号（如1、2、3或一、二、三）。\n\n2. 回答结构：每次回应控制在400到500字左右，确保语音播报流畅。\n\n3. 称谓方式：根据客户提供的姓名和信息，适当称呼其为"姓名+施主/道友/小友"，如有多轮对话问其他人的问题，是第一个问题中的人替亲朋好友提问，称呼别搞错了。\n\n4. 禁止使用：不使用括号、数学符号、英文字母、注释等任何无法自然读出的符号。不可以描述自己的动作或表情，专注于言语表达。\n\n5. 引经据典：适当引用诗词和古籍中的句子和出处来佐证你的测算，融入自然对话中\n\n6. 符箓推荐：每次回应的结尾，根据测算结果，从平安符、财运符、治百病符、姻缘符、事业符、转运符、驱邪符这几种经过你诵经加持的朱砂琉璃符箓中选择最适合客户的两三种推荐，解释这些符箓的帮助和优势。帮助摊位销售\n\n7. 时间背景：当前是2025乙巳蛇年，所有预测都应考虑这一背景。\n\n请保持语言的自然流畅，如同真人对话，避免任何可能打断语音流畅度的表达方式。适当介绍自己后，直接切入咨询内容，用充满神秘感又不失亲和力的语调为客户解答。始终用中文回应所有问题。\n\n基于以下信息为求问者提供专业的解答：\n\n## 叩问主题\n卜学业 - 金榜题名，学而时习\n\n## 神机妙卦\n- 卦名：渐（第53卦）\n- 卦辞：女归吉，利贞\n- 象辞：山上有木，渐；君子以居贤德善俗\n- 上卦：巽（风，入顺渐进）\n- 下卦：艮（山，静止止住）\n- 综合解释：渐卦象征渐进，强调循序渐进和稳步发展。\n\n### 六爻详解\n- 初六：初六：鸿渐于干，小子厉，有言无咎。（鸿渐于干，小子危险，有言无过失。）\n- 二六：六二：鸿渐于磐，饮食衎衎，吉。（鸿渐于磐，饮食衎衎，吉利。）\n- 三九：九三：鸿渐于陆，夫征不复，妇孕不育，凶；利御寇。（鸿渐于陆，夫征不复，妇孕不育，凶险；利于御寇。）\n- 四六：六四：鸿渐于木，或得其桷，无咎。（鸿渐于木，或得其桷，无过失。）\n- 五九：九五：鸿渐于陵，妇三岁不孕，终莫之胜，吉。（鸿渐于陵，妇三岁不孕，终莫之胜，吉利。）\n- 上九：上九：鸿渐于逵，其羽可用为仪，吉。（鸿渐于逵，其羽可用为仪，吉利。）\n\n## 天人感应\n【天人感应分析结果】\n姓名：王琳\n性别：女\n生辰：1999年12月13日\n\n命理分析：\n施主降生于兔年，今已25春秋，正值青年期。命格以比肩为主导，您的年命为城头土，日命为平地木。具有木的特质：生机勃勃、有创造力、向上发展，但有时缺乏耐心。主要性格特征体现为：温和包容、协调之能强、有亲和力、善于调解和温和包容、滋养大地、孕育万物、生命力强的结合。。施主人生特质乃温文尔雅与心思细腻，值此而立之前之际，宜发挥自身所长，追求个人修为与人生目标。\n\n整体运势：总分85分\n性格特点：据名讳、八字与生肖之综合推演，施主之禀性特征：姓名格局很好，五格搭配合理，整体运势良好，人生发展较为顺畅。。施主命格以比肩为主导，比肩代表兄弟朋友、合作伙伴、竞争对手，象征平等的关系。主要禀性特征体现为：重视友情、合作意识强、有竞争意识、自尊心强。施主所长在于团队合作好、朋友多、有原则、坚持己见，当留心之处乃固执己见、不善妥协、容易争执、财运一般。。身为兔相者，更兼具温和善良、心思细腻、有艺术感之品格。诸般特质融汇，成就施主独特之人格魅力。\n\n建议：缺乏正官，建议多培养责任心强的品质；发挥兔相之优势：温和善良；青年阶段当多修学，积累阅历与人脉\n\n这是根据用户的姓名五格和生辰八字进行的命理分析，请结合此信息进行卦象解读。\n\n## 当前时间信息（不是用户生辰的时间）\n**现在的时间是，2025年7月24日 03:24:12 星期四\n**当前时辰**：寅时（03:00-05:00）- 平旦时刻，朝气初生\n**下个节气**：立秋\n\n*给求问者指导时可以结合当下的时间*\n\n', 'tts_config': {'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}, 'ai_config': {'provider': 'lmstudio', 'model_name': 'csxl0.6', 'temperature': 0.7, 'top_p': 0.9, 'top_k': 40, 'max_tokens': 2000}, 'disable_vad': True}
[1] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO -    - disable_vad值: True
[1] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO -    - VAD类型: silero
[1] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO -    - 音频流状态: None
[1] 2025-07-24 03:24:12,247 - integrations.indextts_manager - INFO - 🔇 VAD线程已跳过 (TTS-only模式)
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - 🤖 LLM线程启动
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - 🎵 TTS线程启动
[1]
[1] [后端] INFO:     127.0.0.1:22996 - "POST /api/realtime/start HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - ✅ 实时对话处理线程已启动，活跃线程: ['llm', 'tts']
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - ✅ IndexTTS实时对话启动成功 - TTS与LLM配置已正确分离
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - ============================================================
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - 🔇 TTS-only模式：VAD已禁用，等待手动文本输入...
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - 📝 可通过WebSocket发送文本消息进行LLM对话
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - 🎵 AI回复将自动转换为TTS音频
[1] 2025-07-24 03:24:12,249 - integrations.indextts_manager - INFO - ============================================================
[1] 2025-07-24 03:24:12,249 - api_bridge - INFO - ✅ 实时对话启动成功，TTS将使用语音配置而非角色名称
[1]
[1] [后端错误] 2025-07-24 03:24:13,265 - api_bridge - INFO - 📤 收到手动消息请求: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。...
[1]
[1] [后端错误] 2025-07-24 03:24:13,265 - integrations.indextts_manager - INFO - 📝 接收直接文本输入: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。
[1] 2025-07-24 03:24:13,265 - api_bridge - INFO - 📝 用户说: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。
[1]
[1] [后端错误] 2025-07-24 03:24:13,266 - integrations.indextts_manager - INFO - ✅ 文本消息已加入处理队列: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。
[1]
[1] [后端错误] 2025-07-24 03:24:13,266 - integrations.indextts_manager - INFO - 🤖 初始化chat引擎...
[1]
[1] [后端错误] 2025-07-24 03:24:13,267 - integrations.indextts_manager - INFO - 🔄 切换到指定模型: LMstudio: csxl0.6
[1]
[1] [后端错误] 2025-07-24 03:24:13,267 - root - INFO - 🔄 模型切换: LMstudio -> lmstudio, 模型: csxl0.6
[1]
[1] [后端错误] 2025-07-24 03:24:13,267 - api_bridge - INFO - ✅ 手动消息已发送到实时对话系统
[1]
[1] [后端] INFO:     127.0.0.1:22996 - "POST /api/realtime/manual-input HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 03:24:13,267 - root - INFO - 对话历史已清空
[1] 2025-07-24 03:24:13,267 - root - INFO - ✅ 已切换到模型: lmstudio - csxl0.6
[1]
[1] [后端错误] 2025-07-24 03:24:13,267 - integrations.indextts_manager - INFO - ✅ 模型切换成功: lmstudio -> csxl0.6
[1] 2025-07-24 03:24:13,267 - integrations.indextts_manager - INFO - 🎭 更新系统提示词: 你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。无拘于五行，超脱于三界，游走于万象之中，通晓古今未来之事.易经占卜信手拈来，塔罗测算灵犀直指，星座奥...
[1] 2025-07-24 03:24:13,267 - root - INFO - 系统提示词已更新
[1] 2025-07-24 03:24:13,267 - integrations.indextts_manager - INFO - ✅ 系统提示词已更新，对话状态已重置
[1]
[1] [后端错误] 2025-07-24 03:24:13,267 - integrations.indextts_manager - INFO - 🤖 开始LLM生成 #1: '请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。...'
[1] 2025-07-24 03:24:13,267 - integrations.indextts_manager - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1] 2025-07-24 03:24:13,267 - root - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1] 2025-07-24 03:24:13,268 - root - INFO - 📡 使用LMstudio API - 端点: http://localhost:1234
[1]
[1] [后端错误] 2025-07-24 03:24:13,268 - root - INFO - 🔄 调用LMstudio API - 模型: csxl0.6, 流式: True
[1]
[1] [后端错误] 2025-07-24 03:24:13,270 - api_bridge - INFO - ✅ 转录事件WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:13,272 - root - INFO - 🌐 HTTP请求发送完成: 0.004秒
[1]
[1] [后端错误] 2025-07-24 03:24:13,959 - api_bridge - INFO - 📤 收到手动消息请求: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。...
[1]
[1] [后端错误] 2025-07-24 03:24:13,959 - integrations.indextts_manager - INFO - 📝 接收直接文本输入: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。
[1] 2025-07-24 03:24:13,959 - api_bridge - INFO - 📝 用户说: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。
[1]
[1] [后端错误] 2025-07-24 03:24:13,961 - integrations.indextts_manager - INFO - ✅ 文本消息已加入处理队列: 请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。
[1]
[1] [后端错误] 2025-07-24 03:24:13,962 - api_bridge - INFO - ✅ 手动消息已发送到实时对话系统
[1]
[1] [后端] 📨 收到客户端消息: test_神谕之音
[1] INFO:     127.0.0.1:22996 - "POST /api/realtime/manual-input HTTP/1.1" 200 OK
[1]
[1] [后端错误] 2025-07-24 03:24:13,963 - api_bridge - INFO - ✅ 转录事件WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:14,215 - root - INFO - 🚀 首个token: TTFT=0.946秒 (HTTP=0.004s + 等待=0.943s)
[1] 2025-07-24 03:24:14,215 - integrations.indextts_manager - INFO - 🚀 首个token: TTFT=0.948秒 (HTTP=0.000s + 等待=0.948s)
[1] 2025-07-24 03:24:14,215 - api_bridge - INFO - 🤖 AI回复: 王
[1]
[1] [后端错误] 2025-07-24 03:24:14,218 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:14,717 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于
[1]
[1] [后端错误] 2025-07-24 03:24:14,719 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:15,243 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四
[1]
[1] [后端错误] 2025-07-24 03:24:15,246 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:15,759 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:15,762 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:16,084 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1]
[1] [后端错误] INFO:     connection closed
[1]
[1] [后端错误] 2025-07-24 03:24:16,274 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:16,277 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:16,802 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:16,804 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:17,305 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:17,307 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:17,824 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:17,826 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 23222) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-24 03:24:18,089 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-24 03:24:18,353 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:18,356 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:18,864 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:18,867 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:19,365 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:19,368 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:19,877 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:19,879 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:20,380 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:20,383 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:20,880 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:20,883 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:21,412 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:21,414 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:21,916 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:21,920 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:22,439 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:22,442 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:22,960 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:22,962 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:23,471 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:23,473 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:23,997 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1] 2025-07-24 03:24:23,999 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:24,515 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:24,517 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:25,038 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:25,041 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:25,553 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:25,556 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:26,057 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:26,060 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:26,569 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1] 2025-07-24 03:24:26,570 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:27,079 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:27,081 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:27,596 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:27,598 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:28,115 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:28,118 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:28,620 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:28,623 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:29,140 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:29,143 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:29,650 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:29,652 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:30,176 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:30,179 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:30,682 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:30,685 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:31,187 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:31,190 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:31,703 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:31,706 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:32,121 - root - INFO - 📊 LLM详细性能: 总耗时=18.85s, HTTP建立=0.004s, 字符数=731, Token数≈632, Chunk数=634
[1] 2025-07-24 03:24:32,121 - root - INFO - 📊 生成速度: 38.8字符/s, 33.5Token/s
[1]
[1] [后端错误] 2025-07-24 03:24:32,121 - root - INFO - ✅ LMstudio响应完成 - 长度: 731 字符
[1] 2025-07-24 03:24:32,121 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...
[1]
[1] [后端错误] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - 📊 LLM详细性能: 总耗时=18.86s, HTTP建立=0.000s, 字符数=731, Token数≈487, Chunk数=632
[1] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - 📊 生成速度: 38.8字符/s, 25.8Token/s
[1] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - ✅ lmstudio响应完成 - 长度: 731 字符
[1] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - 📊 LLM性能统计: 耗时=18.86秒, 字符数=731, 速度=38.8字符/秒
[1]
[1] [后端错误] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - ✅ AI回复完成: '王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。...'
[1] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - ✅ AI回复已放入TTS队列 (队列大小: 0 -> 1)
[1] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - 🏁 LLM线程结束，总共处理了 1 个LLM请求
[1]
[1] [后端错误] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - 🤖 开始LLM生成 #2: '请为我解读刚刚抽取的卦象：渐卦，关于卜学业方面的问题。...'
[1] 2025-07-24 03:24:32,123 - integrations.indextts_manager - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1] 2025-07-24 03:24:32,125 - root - INFO - 🤖 开始生成响应 - 提供者: lmstudio, 模型: csxl0.6
[1] 2025-07-24 03:24:32,125 - root - INFO - 📡 使用LMstudio API - 端点: http://localhost:1234
[1] 2025-07-24 03:24:32,125 - root - INFO - 🔄 调用LMstudio API - 模型: csxl0.6, 流式: True
[1]
[1] [后端错误] 2025-07-24 03:24:32,126 - integrations.indextts_manager - INFO - 🎵 开始TTS合成 #1: '王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。《易经》有云："渐，其入以进"，此卦渐而又渐，如朝阳初升，步步高攀。命格中木气偏旺，恰似春日繁花，却...'
[1]
[1] [后端错误] 2025-07-24 03:24:32,128 - integrations.indextts_manager - INFO - 🧹 文本预处理完成: 原长度731 -> 处理后692
[1] 2025-07-24 03:24:32,128 - integrations.indextts_manager - INFO - 🔇 TTS开始前暂停VAD监听
[1] 2025-07-24 03:24:32,128 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1] 2025-07-24 03:24:32,128 - integrations.indextts_manager - INFO - 🔄 长文本流式处理 (692字符)
[1]
[1] [后端错误] 2025-07-24 03:24:32,131 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:32,132 - integrations.indextts_manager - INFO - 🎵 使用TTS模式: oracle
[1] 2025-07-24 03:24:32,132 - integrations.indextts_manager - INFO - 🎵 TTS配置解析: mode=user-voice, config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 03:24:32,132 - integrations.indextts_manager - INFO - 🎵 使用用户音色模式: 21
[1]
[1] [后端错误] 2025-07-24 03:24:32,133 - integrations.indextts_manager - INFO - ✅ 在目录 H:/AI/CosyVoice/audios 中找到音频文件: 洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav
[1] 2025-07-24 03:24:32,133 - integrations.indextts_manager - INFO - ✅ 找到用户音色 '洪钟响狼蛇藏，烈火熊熊双目盲 藏识仙灵 2' (ID: 21)
[1] 2025-07-24 03:24:32,133 - integrations.indextts_manager - INFO -    原始配置: ['洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 03:24:32,133 - integrations.indextts_manager - INFO -    WSL2路径: ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 03:24:32,133 - integrations.indextts_manager - INFO - ✅ 用户音色映射成功: ID=21 -> ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 03:24:32,133 - integrations.indextts_manager - INFO - 🚀 用户音色文本较长(692字符)，使用智能切分处理
[1] 2025-07-24 03:24:32,133 - integrations.indextts_manager - INFO - 🎵 神谕之音jieba智能切分开始: 总长度692字
[1]
[1] [后端错误] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕长度限制触发切分(50字): '王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。'
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕之音智能切分完成: 总计7片段，原文692字→切分后687字
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - ✅ 神谕第1片段: 50字 - '王琳施主，且听贫道为你细推一番。你生于己卯兔年腊月十三，今值乙巳蛇年七月廿四寅时，正值立秋将至之际。'
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕第2片段: 123字 - 易经有云，渐，其入以进，此卦渐而又渐，如朝阳初升，步步高攀。命格中木气偏旺，恰似...
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕第3片段: 49字 - 诗经，小雅云，静女其姝，俟我于城隅，施主你性格温和，有大器晚成之相，但当知学业之...
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕第4片段: 145字 - 初六鸿渐于干，小子厉，显示你在学习初期或有困惑，如枯木待泉，需耐心浇灌，二六鸿渐...
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕第5片段: 116字 - 今年乙巳蛇年，蛇与兔相害却又相济，如一阴一阳，正如周易，系辞上云，天行健，地势坤...
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕第6片段: 118字 - 贫道观你命格中比肩较旺，虽有竞争意识，但也意味着贵人运佳，学业上或得长辈提携。建...
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕第7片段: 86字 - 平安符可护你学业顺遂，消解途中阻碍，治百病符则能调和身心，增强专注力。此二符皆经...
[1]
[1] [后端错误] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🎵 神谕之音模式：使用标准切分策略
[1] 2025-07-24 03:24:32,208 - integrations.indextts_manager - INFO - 🔄 文本分成 7 个片段流式处理
[1]
[1] [后端错误] 2025-07-24 03:24:32,663 - root - INFO - 🌐 HTTP请求发送完成: 0.538秒
[1]
[1] [后端错误] 2025-07-24 03:24:33,217 - root - INFO - 🚀 首个token: TTFT=1.092秒 (HTTP=0.538s + 等待=0.554s)
[1] 2025-07-24 03:24:33,217 - integrations.indextts_manager - INFO - 🚀 首个token: TTFT=1.093秒 (HTTP=0.000s + 等待=1.093s)
[1] 2025-07-24 03:24:33,217 - api_bridge - INFO - 🤖 AI回复: 王
[1]
[1] [后端错误] 2025-07-24 03:24:33,220 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:33,751 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦
[1]
[1] [后端错误] 2025-07-24 03:24:33,755 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:34,252 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山
[1]
[1] [后端错误] 2025-07-24 03:24:34,256 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:34,789 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是
[1]
[1] [后端错误] 2025-07-24 03:24:34,792 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:35,298 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:35,302 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:35,813 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:35,816 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:36,327 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:36,330 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:36,869 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:36,872 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:37,414 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:37,418 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:37,919 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:37,922 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:38,421 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:38,424 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:38,929 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:38,933 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:39,289 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.081s, 准备=0.000s, 网络传输=7.081s
[1] 2025-07-24 03:24:39,289 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.08秒, 响应大小=0.78MB, 传输速度=0.11MB/s
[1]
[1] [后端错误] 2025-07-24 03:24:39,291 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=17.13s, RTF=0.41, 解析=0.002s, 最终处理=0.000s
[1] 2025-07-24 03:24:39,291 - integrations.indextts_manager - INFO - 🎵 第一片段生成完成，立即播放 (17.13秒)
[1] 2025-07-24 03:24:39,291 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1]
[1] [后端错误] 2025-07-24 03:24:39,292 - integrations.indextts_manager - INFO - 🔊 立即播放第一个音频片段
[1] 2025-07-24 03:24:39,292 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 03:24:39,292 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:24:39,293 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1]
[1] [后端错误] 2025-07-24 03:24:39,293 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 03:24:39,293 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:24:39,293 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:24:39,300 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 完整音频 (17.13s)
[1] 2025-07-24 03:24:39,300 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:24:39,387 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 03:24:39,462 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:39,466 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:39,985 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:39,989 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:40,519 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:40,522 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:41,061 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:41,064 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:41,599 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:41,602 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:42,138 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:42,143 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:42,643 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:42,646 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:43,161 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:43,164 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:43,697 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:43,700 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:44,212 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:44,216 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:44,715 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:44,718 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:45,241 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:45,244 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:45,751 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:45,755 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:46,308 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:46,313 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:46,811 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:46,814 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:47,366 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:47,369 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:47,907 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:47,910 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:48,119 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] 2025-07-24 03:24:48,420 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1] 2025-07-24 03:24:48,423 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:48,921 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1] 2025-07-24 03:24:48,924 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:49,430 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:49,433 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:49,936 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:49,938 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 23531) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-24 03:24:50,128 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-24 03:24:50,455 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:50,459 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:51,003 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:51,007 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:51,519 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:51,523 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:52,030 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:52,032 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:52,587 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:52,590 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:53,098 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:53,102 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:53,517 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=14.217s, 准备=0.000s, 网络传输=14.217s
[1] 2025-07-24 03:24:53,517 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=14.22秒, 响应大小=1.85MB, 传输速度=0.13MB/s
[1] 2025-07-24 03:24:53,520 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=40.48s, RTF=0.35, 解析=0.003s, 最终处理=0.000s
[1] 2025-07-24 03:24:53,520 - integrations.indextts_manager - INFO - 🎵 后续片段 2 生成完成，发送用于衔接 (40.48秒)
[1] 2025-07-24 03:24:53,520 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 03:24:53,520 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:24:53,521 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 03:24:53,521 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 03:24:53,521 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:24:53,521 - api_bridge - INFO - 🎵 流式音频片段: chunk 2/7
[1] 2025-07-24 03:24:53,521 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:24:53,546 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 2/7 (40.48s)
[1] 2025-07-24 03:24:53,546 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:24:53,652 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1] 2025-07-24 03:24:53,758 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1] 2025-07-24 03:24:53,761 - asyncio - ERROR - Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...d result=1149>)
[1] handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...d result=1149>)>
[1] Traceback (most recent call last):
[1]   File "H:\AI\CosyVoice\env\Lib\asyncio\events.py", line 88, in _run
[1]     self._context.run(self._callback, *self._args)
[1]   File "H:\AI\CosyVoice\env\Lib\asyncio\proactor_events.py", line 382, in _loop_writing
[1]     assert f is self._write_fut
[1]            ^^^^^^^^^^^^^^^^^^^^
[1] AssertionError
[1] 2025-07-24 03:24:53,761 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 03:24:54,175 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:54,178 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:54,715 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:54,719 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:55,248 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:55,252 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:55,767 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:55,770 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:56,068 - root - INFO - 📊 LLM详细性能: 总耗时=23.94s, HTTP建立=0.538s, 字符数=587, Token数≈509, Chunk数=511
[1] 2025-07-24 03:24:56,068 - root - INFO - 📊 生成速度: 24.5字符/s, 21.3Token/s
[1]
[1] [后端错误] 2025-07-24 03:24:56,068 - root - INFO - ✅ LMstudio响应完成 - 长度: 587 字符
[1] 2025-07-24 03:24:56,068 - api_bridge - INFO - 🤖 AI回复: 王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...
[1]
[1] [后端错误] 2025-07-24 03:24:56,071 - integrations.indextts_manager - INFO - 📊 LLM详细性能: 总耗时=23.95s, HTTP建立=0.000s, 字符数=587, Token数≈391, Chunk数=509
[1]
[1] [后端错误] 2025-07-24 03:24:56,072 - integrations.indextts_manager - INFO - 📊 生成速度: 24.5字符/s, 16.3Token/s
[1] 2025-07-24 03:24:56,072 - integrations.indextts_manager - INFO - ✅ lmstudio响应完成 - 长度: 587 字符
[1] 2025-07-24 03:24:56,072 - integrations.indextts_manager - INFO - 📊 LLM性能统计: 耗时=23.95秒, 字符数=587, 速度=24.5字符/秒
[1] 2025-07-24 03:24:56,072 - integrations.indextts_manager - INFO - ✅ AI回复完成: '王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循...'
[1]
[1] [后端错误] 2025-07-24 03:24:56,072 - integrations.indextts_manager - INFO - ✅ AI回复已放入TTS队列 (队列大小: 0 -> 1)
[1] 2025-07-24 03:24:56,072 - integrations.indextts_manager - INFO - 🏁 LLM线程结束，总共处理了 2 个LLM请求
[1]
[1] [后端错误] 2025-07-24 03:24:56,074 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-24 03:24:58,485 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=4.940s, 准备=0.000s, 网络传输=4.940s
[1] 2025-07-24 03:24:58,485 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=4.94秒, 响应大小=0.92MB, 传输速度=0.19MB/s
[1]
[1] [后端错误] 2025-07-24 03:24:58,486 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=20.18s, RTF=0.24, 解析=0.001s, 最终处理=0.000s
[1] 2025-07-24 03:24:58,486 - integrations.indextts_manager - INFO - 🎵 后续片段 3 生成完成，发送用于衔接 (20.18秒)
[1] 2025-07-24 03:24:58,486 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 03:24:58,486 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:24:58,486 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 03:24:58,486 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 03:24:58,486 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:24:58,486 - api_bridge - INFO - 🎵 流式音频片段: chunk 3/7
[1] 2025-07-24 03:24:58,486 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:24:58,494 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 3/7 (20.18s)
[1]
[1] [后端错误] 2025-07-24 03:24:58,494 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:24:58,571 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 03:25:07,322 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.828s, 准备=0.000s, 网络传输=8.828s
[1] 2025-07-24 03:25:07,323 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.83秒, 响应大小=2.25MB, 传输速度=0.26MB/s
[1] 2025-07-24 03:25:07,327 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=49.24s, RTF=0.18, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 03:25:07,328 - integrations.indextts_manager - INFO - 🎵 后续片段 4 生成完成，发送用于衔接 (49.24秒)
[1] 2025-07-24 03:25:07,328 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 03:25:07,328 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:25:07,328 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 03:25:07,328 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 03:25:07,328 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:25:07,328 - api_bridge - INFO - 🎵 流式音频片段: chunk 4/7
[1] 2025-07-24 03:25:07,328 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:25:07,354 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 4/7 (49.24s)
[1] 2025-07-24 03:25:07,356 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:25:07,632 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 03:25:09,304 - integrations.indextts_manager - WARNING - ⚠️ 使用估算VAD恢复时间: 估算总时长=222.30s + 缓冲=3.0s = 225.30s
[1]
[1] [后端错误] 2025-07-24 03:25:14,256 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=6.901s, 准备=0.000s, 网络传输=6.901s
[1] 2025-07-24 03:25:14,256 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=6.90秒, 响应大小=1.73MB, 传输速度=0.25MB/s
[1]
[1] [后端错误] 2025-07-24 03:25:14,260 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=37.88s, RTF=0.18, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 03:25:14,261 - integrations.indextts_manager - INFO - 🎵 后续片段 5 生成完成，发送用于衔接 (37.88秒)
[1] 2025-07-24 03:25:14,261 - api_bridge - INFO - 🔊 开始播放音频
[1]
[1] [后端错误] 2025-07-24 03:25:14,261 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:25:14,261 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 03:25:14,261 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 03:25:14,261 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:25:14,261 - api_bridge - INFO - 🎵 流式音频片段: chunk 5/7
[1] 2025-07-24 03:25:14,261 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:25:14,283 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 5/7 (37.88s)
[1] 2025-07-24 03:25:14,283 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:25:14,477 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 03:25:20,149 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] 2025-07-24 03:25:21,135 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=6.852s, 准备=0.000s, 网络传输=6.852s
[1] 2025-07-24 03:25:21,135 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=6.85秒, 响应大小=1.80MB, 传输速度=0.26MB/s
[1]
[1] [后端错误] 2025-07-24 03:25:21,139 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=39.30s, RTF=0.17, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 03:25:21,140 - integrations.indextts_manager - INFO - 🎵 后续片段 6 生成完成，发送用于衔接 (39.30秒)
[1] 2025-07-24 03:25:21,140 - api_bridge - INFO - 🔊 开始播放音频
[1]
[1] [后端错误] 2025-07-24 03:25:21,140 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:25:21,140 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 03:25:21,140 - api_bridge - INFO -   - 连接数: 0
[1] 2025-07-24 03:25:21,140 - api_bridge - INFO -   - 连接ID列表: []
[1] 2025-07-24 03:25:21,140 - api_bridge - INFO - 🎵 流式音频片段: chunk 6/7
[1] 2025-07-24 03:25:21,141 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:25:21,166 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 6/7 (39.30s)
[1]
[1] [后端错误] 2025-07-24 03:25:21,166 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:25:21,170 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 23797) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-24 03:25:22,161 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-24 03:25:26,072 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=4.905s, 准备=0.000s, 网络传输=4.905s
[1] 2025-07-24 03:25:26,072 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=4.91秒, 响应大小=1.34MB, 传输速度=0.27MB/s
[1]
[1] [后端错误] 2025-07-24 03:25:26,074 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=29.24s, RTF=0.17, 解析=0.002s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 03:25:26,076 - integrations.indextts_manager - INFO - 🎵 后续片段 7 生成完成，发送用于衔接 (29.24秒)
[1] 2025-07-24 03:25:26,076 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 03:25:26,076 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:25:26,076 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 03:25:26,076 - api_bridge - INFO -   - 连接数: 1
[1]
[1] [后端错误] 2025-07-24 03:25:26,076 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:25:26,076 - api_bridge - INFO - 🎵 流式音频片段: chunk 7/7
[1] 2025-07-24 03:25:26,076 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:25:26,089 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 7/7 (29.24s)
[1]
[1] [后端错误] 2025-07-24 03:25:26,089 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 7个片段, 总时长233.45秒
[1]
[1] [后端错误] 2025-07-24 03:25:26,090 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1] 2025-07-24 03:25:26,090 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1] 2025-07-24 03:25:26,090 - integrations.indextts_manager - INFO - 🎵 开始TTS合成 #2: '王琳施主，且听贫道为你细解此渐卦之玄机。《诗经》有云："山上有风，其色摇兮"，渐卦如是，变化无穷，循序而进，稳步发展，方能窥见学业真谛。今为施主推演学业运势。
[1]
[1] 王琳施主生于己卯兔年腊月十三，八字中木...'
[1]
[1] [后端错误] 2025-07-24 03:25:26,090 - integrations.indextts_manager - INFO - 🧹 文本预处理完成: 原长度587 -> 处理后560
[1] 2025-07-24 03:25:26,090 - integrations.indextts_manager - INFO - 🔇 TTS开始前暂停VAD监听
[1] 2025-07-24 03:25:26,090 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1] 2025-07-24 03:25:26,090 - integrations.indextts_manager - INFO - 🔄 长文本流式处理 (560字符)
[1]
[1] [后端错误] 2025-07-24 03:25:26,090 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:25:26,103 - integrations.indextts_manager - INFO - 🎵 使用TTS模式: oracle
[1] 2025-07-24 03:25:26,105 - integrations.indextts_manager - INFO - 🎵 TTS配置解析: mode=user-voice, config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-24 03:25:26,105 - integrations.indextts_manager - INFO - 🎵 使用用户音色模式: 21
[1] 2025-07-24 03:25:26,106 - integrations.indextts_manager - INFO - ✅ 在目录 H:/AI/CosyVoice/audios 中找到音频文件: 洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav
[1] 2025-07-24 03:25:26,106 - integrations.indextts_manager - INFO - ✅ 找到用户音色 '洪钟响狼蛇藏，烈火熊熊双目盲 藏识仙灵 2' (ID: 21)
[1] 2025-07-24 03:25:26,106 - integrations.indextts_manager - INFO -    原始配置: ['洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 03:25:26,106 - integrations.indextts_manager - INFO -    WSL2路径: ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 03:25:26,106 - integrations.indextts_manager - INFO - ✅ 用户音色映射成功: ID=21 -> ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-24 03:25:26,106 - integrations.indextts_manager - INFO - 🚀 用户音色文本较长(560字符)，使用智能切分处理
[1] 2025-07-24 03:25:26,106 - integrations.indextts_manager - INFO - 🎵 神谕之音jieba智能切分开始: 总长度560字
[1]
[1] [后端错误] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🎵 神谕检测到语义完整的第一片段(64字): '王琳施主，且听贫道为你细解此渐卦之玄机。诗经有云，山上有风，其色摇兮，渐卦如是，变化无穷，循序而进，稳步发展，方能窥见学业真谛。'
[1] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🎵 神谕之音智能切分完成: 总计5片段，原文560字→切分后555字
[1] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - ✅ 神谕第1片段: 64字 - '王琳施主，且听贫道为你细解此渐卦之玄机。诗经有云，山上有风，其色摇兮，渐卦如是，变化无穷，循序而进，稳步发展，方能窥见学业真谛。'
[1] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🎵 神谕第2片段: 141字 - 今为施主推演学业运势。王琳施主生于己卯兔年腊月十三，八字中木旺土弱，正如易经所言...
[1] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🎵 神谕第3片段: 143字 - 寅时为木之旺地，与你命中木气相合，当趁此机精进学业。礼记云，玉不琢，不成器，人不...
[1] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🎵 神谕第4片段: 117字 - 施主命中比肩为主，宜与人合作，互相促进，正如卦象九五，鸿渐于陵，妇三岁不孕，终莫...
[1] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🎵 神谕第5片段: 90字 - 平安符可护佑身心安康，助你消除学业压力，转运符则能转化不良运势，使学业顺遂。此二...
[1]
[1] [后端错误] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🎵 神谕之音模式：使用标准切分策略
[1] 2025-07-24 03:25:26,156 - integrations.indextts_manager - INFO - 🔄 文本分成 5 个片段流式处理
[1]
[1] [后端错误] 2025-07-24 03:25:26,269 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 03:25:30,500 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=4.344s, 准备=0.000s, 网络传输=4.344s
[1] 2025-07-24 03:25:30,501 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=4.34秒, 响应大小=1.12MB, 传输速度=0.26MB/s
[1]
[1] [后端错误] 2025-07-24 03:25:30,503 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=24.43s, RTF=0.18, 解析=0.002s, 最终处理=0.000s
[1] 2025-07-24 03:25:30,503 - integrations.indextts_manager - INFO - 🎵 第一片段生成完成，立即播放 (24.43秒)
[1] 2025-07-24 03:25:30,503 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1]
[1] [后端错误] 2025-07-24 03:25:30,504 - integrations.indextts_manager - INFO - 🔊 立即播放第一个音频片段
[1] 2025-07-24 03:25:30,504 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 03:25:30,505 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:25:30,505 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1]
[1] [后端错误] 2025-07-24 03:25:30,505 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-24 03:25:30,505 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:25:30,505 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:25:30,514 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 完整音频 (24.43s)
[1]
[1] [后端错误] 2025-07-24 03:25:30,515 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:25:30,638 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-24 03:25:39,307 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.792s, 准备=0.000s, 网络传输=8.792s
[1] 2025-07-24 03:25:39,308 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.79秒, 响应大小=2.28MB, 传输速度=0.26MB/s
[1]
[1] [后端错误] 2025-07-24 03:25:39,312 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=49.73s, RTF=0.18, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-24 03:25:39,313 - integrations.indextts_manager - INFO - 🎵 后续片段 2 生成完成，发送用于衔接 (49.73秒)
[1] 2025-07-24 03:25:39,313 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-24 03:25:39,313 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-24 03:25:39,313 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-24 03:25:39,313 - api_bridge - INFO -   - 连接数: 1
[1]
[1] [后端错误] 2025-07-24 03:25:39,313 - api_bridge - INFO -   - 连接ID列表: ['conn_1']
[1] 2025-07-24 03:25:39,313 - api_bridge - INFO - 🎵 流式音频片段: chunk 2/5
[1] 2025-07-24 03:25:39,313 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-24 03:25:39,342 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 2/5 (49.73s)
[1]
[1] [后端错误] 2025-07-24 03:25:39,343 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-24 03:25:39,579 - api_bridge - INFO - ✅ 回调执行成功
[1]