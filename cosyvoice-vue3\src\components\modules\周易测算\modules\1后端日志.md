[1] 2025-07-25 22:51:11,015 - integrations.indextts_manager - INFO - ✅ AI回复完成: '王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛...'
[1] 2025-07-25 22:51:11,015 - integrations.indextts_manager - INFO - ✅ AI回复已放入TTS队列 (队列大小: 0 -> 1)
[1] 2025-07-25 22:51:11,015 - integrations.indextts_manager - INFO - 🏁 LLM线程结束，总共处理了 2 个LLM请求
[1]
[1] [后端错误] 2025-07-25 22:51:11,017 - api_bridge - INFO - ✅ LLM回复WebSocket发送成功
[1]
[1] [后端错误] 2025-07-25 22:51:14,813 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=14.120s, 准备=0.000s, 网络传输=14.120s
[1] 2025-07-25 22:51:14,813 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=14.12秒, 响应大小=2.10MB, 传输速度=0.15MB/s
[1]
[1] [后端错误] 2025-07-25 22:51:14,817 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=45.78s, RTF=0.31, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-25 22:51:14,818 - integrations.indextts_manager - INFO - 🎵 后续片段 2 生成完成，发送用于衔接 (45.78秒)
[1] 2025-07-25 22:51:14,818 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-25 22:51:14,818 - api_bridge - INFO - 🎵 接收到TTS音频
[1]
[1] [后端错误] 2025-07-25 22:51:14,818 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-25 22:51:14,818 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-25 22:51:14,818 - api_bridge - INFO -   - 连接ID列表: ['conn_2', 'conn_1']
[1] 2025-07-25 22:51:14,818 - api_bridge - INFO - 🎵 流式音频片段: chunk 2/4
[1]
[1] [后端错误] 2025-07-25 22:51:14,818 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:51:14,848 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 2/4 (45.78s)
[1]
[1] [后端错误] 2025-07-25 22:51:14,848 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-25 22:51:15,391 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-25 22:51:22,130 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.281s, 准备=0.000s, 网络传输=7.281s
[1] 2025-07-25 22:51:22,130 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.28秒, 响应大小=1.72MB, 传输速度=0.24MB/s
[1]
[1] [后端错误] 2025-07-25 22:51:22,133 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=37.49s, RTF=0.19, 解析=0.003s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-25 22:51:22,134 - integrations.indextts_manager - INFO - 🎵 后续片段 3 生成完成，发送用于衔接 (37.49秒)
[1] 2025-07-25 22:51:22,134 - api_bridge - INFO - 🔊 开始播放音频
[1]
[1] [后端错误] 2025-07-25 22:51:22,134 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-25 22:51:22,134 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-25 22:51:22,134 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-25 22:51:22,134 - api_bridge - INFO -   - 连接ID列表: ['conn_2', 'conn_1']
[1] 2025-07-25 22:51:22,135 - api_bridge - INFO - 🎵 流式音频片段: chunk 3/4
[1]
[1] [后端错误] 2025-07-25 22:51:22,135 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:51:22,158 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 3/4 (37.49s)
[1]
[1] [后端错误] 2025-07-25 22:51:22,158 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-25 22:51:22,572 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-25 22:51:29,606 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.448s, 准备=0.000s, 网络传输=7.448s
[1] 2025-07-25 22:51:29,606 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.45秒, 响应大小=1.65MB, 传输速度=0.22MB/s
[1]
[1] [后端错误] 2025-07-25 22:51:29,608 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=36.09s, RTF=0.21, 解析=0.003s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-25 22:51:29,608 - integrations.indextts_manager - INFO - 🎵 后续片段 4 生成完成，发送用于衔接 (36.09秒)
[1] 2025-07-25 22:51:29,608 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-25 22:51:29,610 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-25 22:51:29,610 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-25 22:51:29,610 - api_bridge - INFO -   - 连接数: 2
[1]
[1] [后端错误] 2025-07-25 22:51:29,610 - api_bridge - INFO -   - 连接ID列表: ['conn_2', 'conn_1']
[1] 2025-07-25 22:51:29,610 - api_bridge - INFO - 🎵 流式音频片段: chunk 4/4
[1] 2025-07-25 22:51:29,610 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:51:29,628 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 4/4 (36.09s)
[1]
[1] [后端错误] 2025-07-25 22:51:29,628 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-25 22:51:29,628 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 4个片段, 总时长142.74秒
[1]
[1] [后端错误] 2025-07-25 22:51:29,631 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1] 2025-07-25 22:51:29,631 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1] 2025-07-25 22:51:29,632 - integrations.indextts_manager - INFO - 🎵 开始TTS合成 #2: '王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛，如《黄帝内经》所言："上古之人，其知道者，法于阴阳"，你性情温和，却藏着一分不耐烦与急躁。小过卦对...'
[1]
[1] [后端错误] 2025-07-25 22:51:29,633 - integrations.indextts_manager - INFO - 🧹 文本预处理完成: 原长度571 -> 处理后548
[1] 2025-07-25 22:51:29,633 - integrations.indextts_manager - INFO - 🔇 TTS开始前暂停VAD监听
[1] 2025-07-25 22:51:29,633 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1]
[1] [后端错误] 2025-07-25 22:51:29,633 - integrations.indextts_manager - INFO - 🔄 长文本流式处理 (548字符)
[1]
[1] [后端错误] 2025-07-25 22:51:29,647 - integrations.indextts_manager - INFO - 🎵 使用TTS模式: oracle
[1]
[1] [后端错误] 2025-07-25 22:51:29,648 - integrations.indextts_manager - INFO - 🎵 TTS配置解析: mode=user-voice, config={'mode': 'user-voice', 'userVoiceProfile': '21', 'speed': 1.0, 'volume': 0.8, 'audioQuality': 'high'}
[1] 2025-07-25 22:51:29,648 - integrations.indextts_manager - INFO - 🎵 使用用户音色模式: 21
[1]
[1] [后端错误] 2025-07-25 22:51:29,650 - integrations.indextts_manager - INFO - ✅ 在目录 H:/AI/CosyVoice/audios 中找到音频文件: 洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav
[1] 2025-07-25 22:51:29,651 - integrations.indextts_manager - INFO - ✅ 找到用户音色 '洪钟响狼蛇藏，烈火熊熊双目盲 藏识仙灵 2' (ID: 21)
[1]
[1] [后端错误] 2025-07-25 22:51:29,651 - integrations.indextts_manager - INFO -    原始配置: ['洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-25 22:51:29,651 - integrations.indextts_manager - INFO -    WSL2路径: ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-25 22:51:29,651 - integrations.indextts_manager - INFO - ✅ 用户音色映射成功: ID=21 -> ['/mnt/h/AI/CosyVoice/audios/洪钟响狼蛇藏，烈火熊熊双目盲-藏识仙灵_2.wav']
[1] 2025-07-25 22:51:29,651 - integrations.indextts_manager - INFO - 🚀 用户音色文本较长(548字符)，使用智能切分处理
[1] 2025-07-25 22:51:29,651 - integrations.indextts_manager - INFO - 🎵 神谕之音jieba智能切分开始: 总长度548字
[1]
[1] [后端错误] 2025-07-25 22:51:29,715 - integrations.indextts_manager - WARNING - ⚠️ 神谕第一段仍然过短(39字)，强制从原文切分
[1]
[1] [后端错误] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - ⏰ 动态计算VAD恢复时间: 实际总时长=142.74s + 缓冲=2.0s = 144.74s
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🎵 神谕强制切分完成: 第一段69字，总计5段
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🎵 神谕之音智能切分完成: 总计5片段，原文548字→切分后550字
[1]
[1] [后端错误] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - ✅ 神谕第1片段: 69字 - '王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛，如黄帝内经所言，上古之人，其知道者，'
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🎵 神谕第2片段: 122字 - 法于阴阳，你性情温和，却藏着一分不耐烦与急躁。小过卦对你而言，恰似清秋之夜星光点...
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🎵 神谕第3片段: 123字 - 脾主运化，王琳施主当注意养脾调理，避免过度劳累。亥时为阴水之气最盛之时，此时再述...
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🎵 神谕第4片段: 115字 - 不可强求于事。尤其在立秋将至之际，天气渐凉，当注意保暖，防止风寒侵体。 六爻详解...
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🎵 神谕第5片段: 121字 - 老道为你推荐平安符与治百病符两种灵物。平安符可安定心神，驱散忧虑，治百病符则能调...
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🎵 神谕之音模式：使用标准切分策略
[1] 2025-07-25 22:51:29,716 - integrations.indextts_manager - INFO - 🔄 文本分成 5 个片段流式处理
[1]
[1] [后端错误] 2025-07-25 22:51:30,061 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-25 22:51:34,753 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=5.037s, 准备=0.000s, 网络传输=5.037s
[1] 2025-07-25 22:51:34,753 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=5.04秒, 响应大小=1.17MB, 传输速度=0.23MB/s
[1]
[1] [后端错误] 2025-07-25 22:51:34,756 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=25.56s, RTF=0.20, 解析=0.002s, 最终处理=0.000s
[1] 2025-07-25 22:51:34,757 - integrations.indextts_manager - INFO - 🎵 第一片段生成完成，立即播放 (25.56秒)
[1] 2025-07-25 22:51:34,757 - integrations.indextts_manager - INFO - 🔇 VAD监听已暂停（TTS播放中）
[1] 2025-07-25 22:51:34,758 - integrations.indextts_manager - INFO - 🔊 立即播放第一个音频片段
[1] 2025-07-25 22:51:34,758 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-25 22:51:34,758 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-25 22:51:34,758 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-25 22:51:34,758 - api_bridge - INFO -   - 连接数: 2
[1]
[1] [后端错误] 2025-07-25 22:51:34,758 - api_bridge - INFO -   - 连接ID列表: ['conn_2', 'conn_1']
[1] 2025-07-25 22:51:34,759 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:51:34,773 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 完整音频 (25.56s)
[1] 2025-07-25 22:51:34,774 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-25 22:51:35,063 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-25 22:51:35,850 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 39242) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-25 22:51:37,864 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-25 22:51:43,654 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.881s, 准备=0.000s, 网络传输=8.881s
[1] 2025-07-25 22:51:43,654 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=8.88秒, 响应大小=2.13MB, 传输速度=0.24MB/s
[1]
[1] [后端错误] 2025-07-25 22:51:43,657 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=46.53s, RTF=0.19, 解析=0.004s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-25 22:51:43,657 - integrations.indextts_manager - INFO - 🎵 后续片段 2 生成完成，发送用于衔接 (46.53秒)
[1] 2025-07-25 22:51:43,658 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-25 22:51:43,658 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-25 22:51:43,658 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-25 22:51:43,658 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-25 22:51:43,658 - api_bridge - INFO -   - 连接ID列表: ['conn_2', 'conn_1']
[1] 2025-07-25 22:51:43,658 - api_bridge - INFO - 🎵 流式音频片段: chunk 2/5
[1] 2025-07-25 22:51:43,658 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:51:43,686 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 2/5 (46.53s)
[1]
[1] [后端错误] 2025-07-25 22:51:43,687 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-25 22:51:44,220 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-25 22:51:51,486 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=7.799s, 准备=0.000s, 网络传输=7.799s
[1] 2025-07-25 22:51:51,487 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=7.80秒, 响应大小=1.88MB, 传输速度=0.24MB/s
[1] 2025-07-25 22:51:51,490 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=41.16s, RTF=0.19, 解析=0.003s, 最终处理=0.000s
[1] 2025-07-25 22:51:51,491 - integrations.indextts_manager - INFO - 🎵 后续片段 3 生成完成，发送用于衔接 (41.16秒)
[1] 2025-07-25 22:51:51,492 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-25 22:51:51,492 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-25 22:51:51,492 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-25 22:51:51,492 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-25 22:51:51,492 - api_bridge - INFO -   - 连接ID列表: ['conn_2', 'conn_1']
[1] 2025-07-25 22:51:51,492 - api_bridge - INFO - 🎵 流式音频片段: chunk 3/5
[1] 2025-07-25 22:51:51,492 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:51:51,514 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 3/5 (41.16s)
[1] 2025-07-25 22:51:51,515 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-25 22:51:51,986 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-25 22:52:00,528 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=9.013s, 准备=0.000s, 网络传输=9.013s
[1] 2025-07-25 22:52:00,529 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=9.01秒, 响应大小=1.90MB, 传输速度=0.21MB/s
[1] 2025-07-25 22:52:00,531 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=41.56s, RTF=0.22, 解析=0.003s, 最终处理=0.000s
[1] 2025-07-25 22:52:00,532 - integrations.indextts_manager - INFO - 🎵 后续片段 4 生成完成，发送用于衔接 (41.56秒)
[1] 2025-07-25 22:52:00,532 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-25 22:52:00,532 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-25 22:52:00,532 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1] 2025-07-25 22:52:00,532 - api_bridge - INFO -   - 连接数: 2
[1] 2025-07-25 22:52:00,532 - api_bridge - INFO -   - 连接ID列表: ['conn_2', 'conn_1']
[1] 2025-07-25 22:52:00,532 - api_bridge - INFO - 🎵 流式音频片段: chunk 4/5
[1] 2025-07-25 22:52:00,532 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:52:00,557 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 4/5 (41.56s)
[1] 2025-07-25 22:52:00,558 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1]
[1] [后端错误] 2025-07-25 22:52:01,027 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] 2025-07-25 22:52:04,795 - integrations.indextts_manager - WARNING - ⚠️ 使用估算VAD恢复时间: 估算总时长=193.50s + 缓冲=3.0s = 196.50s
[1]
[1] [后端错误] 2025-07-25 22:52:07,870 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] 2025-07-25 22:52:09,554 - integrations.indextts_manager - INFO - 📊 HTTP详细计时: 总耗时=8.996s, 准备=0.000s, 网络传输=8.996s
[1] 2025-07-25 22:52:09,554 - integrations.indextts_manager - WARNING - 🐌 TTS响应较慢: 请求耗时=9.00秒, 响应大小=1.95MB, 传输速度=0.22MB/s
[1]
[1] [后端错误] 2025-07-25 22:52:09,558 - integrations.indextts_manager - INFO - 🎵 TTS处理完成: 音频时长=42.55s, RTF=0.21, 解析=0.003s, 最终处理=0.000s
[1]
[1] [后端错误] 2025-07-25 22:52:09,559 - integrations.indextts_manager - INFO - 🎵 后续片段 5 生成完成，发送用于衔接 (42.55秒)
[1] 2025-07-25 22:52:09,559 - api_bridge - INFO - 🔊 开始播放音频
[1] 2025-07-25 22:52:09,559 - api_bridge - INFO - 🎵 接收到TTS音频
[1] 2025-07-25 22:52:09,559 - api_bridge - INFO - 📊 WebSocket管理器状态:
[1]
[1] [后端错误] 2025-07-25 22:52:09,559 - api_bridge - INFO -   - 连接数: 1
[1] 2025-07-25 22:52:09,559 - api_bridge - INFO -   - 连接ID列表: ['conn_2']
[1] 2025-07-25 22:52:09,559 - api_bridge - INFO - 🎵 流式音频片段: chunk 5/5
[1] 2025-07-25 22:52:09,559 - api_bridge - INFO - 🎵 通过WebSocket发送音频给前端播放
[1]
[1] [后端错误] 2025-07-25 22:52:09,581 - api_bridge - INFO - ✅ 音频发送到WebSocket成功 - 片段 5/5 (42.55s)
[1]
[1] [后端错误] 2025-07-25 22:52:09,581 - api_bridge - INFO - 📞 开始执行回调: send_tts_audio_stream
[1] 2025-07-25 22:52:09,582 - integrations.indextts_manager - INFO - ✅ 流式文本分片处理完成: 5个片段, 总时长197.35秒
[1]
[1] [后端错误] 2025-07-25 22:52:09,583 - integrations.indextts_manager - INFO - ✅ 用户音色TTS生成成功 - 采样率: 0, 音频长度: 0
[1]
[1] [后端错误] 2025-07-25 22:52:09,583 - integrations.indextts_manager - INFO - ✅ 检测到流式TTS处理完成标记，跳过播放（已在流式处理中播放）
[1]
[1] [后端错误] 2025-07-25 22:52:09,815 - api_bridge - INFO - ✅ 回调执行成功
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 39577) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-25 22:52:09,884 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-25 22:52:39,900 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1]
[1] [后端错误] INFO:     connection closed
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 39729) - "WebSocket /ws/realtime" [accepted]
[1]
[1] [后端错误] INFO:     connection open
[1]
[1] [后端错误] 2025-07-25 22:52:41,909 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-25 22:53:11,945 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 39864) - "WebSocket /ws/realtime" [accepted]
[1] INFO:     connection open
[1] 2025-07-25 22:53:13,958 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]
[1] [后端错误] 2025-07-25 22:53:43,960 - api_bridge - INFO - 🔌 WebSocket客户端主动断开: conn_1
[1] INFO:     connection closed
[1]
[1] [后端错误] INFO:     ('127.0.0.1', 39983) - "WebSocket /ws/realtime" [accepted]
[1] INFO:     connection open
[1] 2025-07-25 22:53:45,968 - api_bridge - INFO - 🔗 WebSocket连接建立: conn_1
[1]