/*!
 * @pixi/compressed-textures - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/compressed-textures is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var _,e,R=require("@pixi/core"),T=require("@pixi/loaders"),t=require("@pixi/utils"),r=require("@pixi/settings"),A=require("@pixi/constants");exports.INTERNAL_FORMATS=void 0,(e=exports.INTERNAL_FORMATS||(exports.INTERNAL_FORMATS={}))[e.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",e[e.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",e[e.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",e[e.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",e[e.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917]="COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT",e[e.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918]="COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT",e[e.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919]="COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT",e[e.COMPRESSED_SRGB_S3TC_DXT1_EXT=35916]="COMPRESSED_SRGB_S3TC_DXT1_EXT",e[e.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",e[e.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",e[e.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",e[e.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",e[e.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",e[e.COMPRESSED_RGBA8_ETC2_EAC=37496]="COMPRESSED_RGBA8_ETC2_EAC",e[e.COMPRESSED_SRGB8_ETC2=37493]="COMPRESSED_SRGB8_ETC2",e[e.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC",e[e.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",e[e.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",e[e.COMPRESSED_RGB_PVRTC_4BPPV1_IMG=35840]="COMPRESSED_RGB_PVRTC_4BPPV1_IMG",e[e.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=35842]="COMPRESSED_RGBA_PVRTC_4BPPV1_IMG",e[e.COMPRESSED_RGB_PVRTC_2BPPV1_IMG=35841]="COMPRESSED_RGB_PVRTC_2BPPV1_IMG",e[e.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=35843]="COMPRESSED_RGBA_PVRTC_2BPPV1_IMG",e[e.COMPRESSED_RGB_ETC1_WEBGL=36196]="COMPRESSED_RGB_ETC1_WEBGL",e[e.COMPRESSED_RGB_ATC_WEBGL=35986]="COMPRESSED_RGB_ATC_WEBGL",e[e.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=35986]="COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL",e[e.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=34798]="COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL",e[e.COMPRESSED_RGBA_ASTC_4x4_KHR=37808]="COMPRESSED_RGBA_ASTC_4x4_KHR";var E=((_={})[exports.INTERNAL_FORMATS.COMPRESSED_RGB_S3TC_DXT1_EXT]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_SRGB_S3TC_DXT1_EXT]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_R11_EAC]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_SIGNED_R11_EAC]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RG11_EAC]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_SIGNED_RG11_EAC]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_RGB8_ETC2]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA8_ETC2_EAC]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_SRGB8_ETC2]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_2BPPV1_IMG]=.25,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG]=.25,_[exports.INTERNAL_FORMATS.COMPRESSED_RGB_ETC1_WEBGL]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGB_ATC_WEBGL]=.5,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL]=1,_[exports.INTERNAL_FORMATS.COMPRESSED_RGBA_ASTC_4x4_KHR]=1,_),O=function(_,e){return O=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(_,e){_.__proto__=e}||function(_,e){for(var R in e)e.hasOwnProperty(R)&&(_[R]=e[R])},O(_,e)};function S(_,e){function R(){this.constructor=_}O(_,e),_.prototype=null===e?Object.create(e):(R.prototype=e.prototype,new R)}function M(_,e,R,T){return new(R||(R=Promise))((function(t,r){function A(_){try{O(T.next(_))}catch(_){r(_)}}function E(_){try{O(T.throw(_))}catch(_){r(_)}}function O(_){var e;_.done?t(_.value):(e=_.value,e instanceof R?e:new R((function(_){_(e)}))).then(A,E)}O((T=T.apply(_,e||[])).next())}))}function G(_,e){var R,T,t,r,A={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return r={next:E(0),throw:E(1),return:E(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function E(r){return function(E){return function(r){if(R)throw new TypeError("Generator is already executing.");for(;A;)try{if(R=1,T&&(t=2&r[0]?T.return:r[0]?T.throw||((t=T.return)&&t.call(T),0):T.next)&&!(t=t.call(T,r[1])).done)return t;switch(T=0,t&&(r=[2&r[0],t.value]),r[0]){case 0:case 1:t=r;break;case 4:return A.label++,{value:r[1],done:!1};case 5:A.label++,T=r[1],r=[0];continue;case 7:r=A.ops.pop(),A.trys.pop();continue;default:if(!(t=A.trys,(t=t.length>0&&t[t.length-1])||6!==r[0]&&2!==r[0])){A=0;continue}if(3===r[0]&&(!t||r[1]>t[0]&&r[1]<t[3])){A.label=r[1];break}if(6===r[0]&&A.label<t[1]){A.label=t[1],t=r;break}if(t&&A.label<t[2]){A.label=t[2],A.ops.push(r);break}t[2]&&A.ops.pop(),A.trys.pop();continue}r=e.call(_,A)}catch(_){r=[6,_],T=0}finally{R=t=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,E])}}}var D,n,o=function(_){function e(e,T){void 0===T&&(T={width:1,height:1,autoLoad:!0});var t,r,A=this;return"string"==typeof e?(t=e,r=new Uint8Array):(t=null,r=e),(A=_.call(this,r,T)||this).origin=t,A.buffer=r?new R.ViewableBuffer(r):null,A.origin&&!1!==T.autoLoad&&A.load(),r&&r.length&&(A.loaded=!0,A.onBlobLoaded(A.buffer.rawBinaryData)),A}return S(e,_),e.prototype.onBlobLoaded=function(_){},e.prototype.load=function(){return M(this,void 0,Promise,(function(){var _;return G(this,(function(e){switch(e.label){case 0:return[4,fetch(this.origin)];case 1:return[4,e.sent().blob()];case 2:return[4,e.sent().arrayBuffer()];case 3:return _=e.sent(),this.data=new Uint32Array(_),this.buffer=new R.ViewableBuffer(_),this.loaded=!0,this.onBlobLoaded(_),this.update(),[2,this]}}))}))},e}(R.BufferResource),I=function(_){function e(R,T){var t=_.call(this,R,T)||this;return t.format=T.format,t.levels=T.levels||1,t._width=T.width,t._height=T.height,t._extension=e._formatToExtension(t.format),(T.levelBuffers||t.buffer)&&(t._levelBuffers=T.levelBuffers||e._createLevelBuffers(R instanceof Uint8Array?R:t.buffer.uint8View,t.format,t.levels,4,4,t.width,t.height)),t}return S(e,_),e.prototype.upload=function(_,e,R){var T=_.gl;if(!_.context.extensions[this._extension])throw new Error(this._extension+" textures are not supported on the current machine");if(!this._levelBuffers)return!1;for(var t=0,r=this.levels;t<r;t++){var A=this._levelBuffers[t],E=A.levelID,O=A.levelWidth,S=A.levelHeight,M=A.levelBuffer;T.compressedTexImage2D(T.TEXTURE_2D,E,this.format,O,S,0,M)}return!0},e.prototype.onBlobLoaded=function(){this._levelBuffers=e._createLevelBuffers(this.buffer.uint8View,this.format,this.levels,4,4,this.width,this.height)},e._formatToExtension=function(_){if(_>=33776&&_<=33779)return"s3tc";if(_>=37488&&_<=37497)return"etc";if(_>=35840&&_<=35843)return"pvrtc";if(_>=36196)return"etc1";if(_>=35986&&_<=34798)return"atc";throw new Error("Invalid (compressed) texture format given!")},e._createLevelBuffers=function(_,e,R,T,t,r,A){for(var O=new Array(R),S=_.byteOffset,M=r,G=A,D=M+T-1&~(T-1),n=G+t-1&~(t-1),o=D*n*E[e],I=0;I<R;I++)O[I]={levelID:I,levelWidth:R>1?M:D,levelHeight:R>1?G:n,levelBuffer:new Uint8Array(_.buffer,S,o)},S+=o,o=(D=(M=M>>1||1)+T-1&~(T-1))*(n=(G=G>>1||1)+t-1&~(t-1))*E[e];return O},e}(o),a=function(){function _(){}return _.use=function(e,R){var r=e.data;if(e.type===T.LoaderResource.TYPE.JSON&&r&&r.cacheID&&r.textures){for(var A=r.textures,E=void 0,O=void 0,S=0,M=A.length;S<M;S++){var G=A[S],D=G.src,n=G.format;if(n||(O=D),_.textureFormats[n]){E=D;break}}if(!(E=E||O))return void R(new Error("Cannot load compressed-textures in "+e.url+", make sure you provide a fallback"));if(E===e.url)return void R(new Error("URL of compressed texture cannot be the same as the manifest's URL"));var o={crossOrigin:e.crossOrigin,metadata:e.metadata.imageMetadata,parentResource:e},I=t.url.resolve(e.url.replace(this.baseUrl,""),E),a=r.cacheID;this.add(a,I,o,(function(_){if(_.error)R(_.error);else{var T=_.texture,t=void 0===T?null:T,r=_.textures,A=void 0===r?{}:r;Object.assign(e,{texture:t,textures:A}),R()}}))}else R()},Object.defineProperty(_,"textureExtensions",{get:function(){if(!_._textureExtensions){var e=r.settings.ADAPTER.createCanvas().getContext("webgl");if(!e)return{};var R={s3tc:e.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:e.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:e.getExtension("WEBGL_compressed_texture_etc"),etc1:e.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:e.getExtension("WEBGL_compressed_texture_pvrtc")||e.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:e.getExtension("WEBGL_compressed_texture_atc"),astc:e.getExtension("WEBGL_compressed_texture_astc")};_._textureExtensions=R}return _._textureExtensions},enumerable:!1,configurable:!0}),Object.defineProperty(_,"textureFormats",{get:function(){if(!_._textureFormats){var e=_.textureExtensions;for(var R in _._textureFormats={},e){var T=e[R];T&&Object.assign(_._textureFormats,Object.getPrototypeOf(T))}}return _._textureFormats},enumerable:!1,configurable:!0}),_.extension=R.ExtensionType.Loader,_}();function X(_,e,T){var t={textures:{},texture:null};return e?(e.map((function(_){return new R.Texture(new R.BaseTexture(_,Object.assign({mipmap:A.MIPMAP_MODES.OFF,alphaMode:A.ALPHA_MODES.NO_PREMULTIPLIED_ALPHA},T)))})).forEach((function(e,T){var r=e.baseTexture,A=_+"-"+(T+1);R.BaseTexture.addToCache(r,A),R.Texture.addToCache(e,A),0===T&&(R.BaseTexture.addToCache(r,_),R.Texture.addToCache(e,_),t.texture=e),t.textures[A]=e})),t):t}var F,s,i=3,B=4,u=7,P=19,N=2,C=0,f=1,L=2,l=3;!function(_){_[_.DXGI_FORMAT_UNKNOWN=0]="DXGI_FORMAT_UNKNOWN",_[_.DXGI_FORMAT_R32G32B32A32_TYPELESS=1]="DXGI_FORMAT_R32G32B32A32_TYPELESS",_[_.DXGI_FORMAT_R32G32B32A32_FLOAT=2]="DXGI_FORMAT_R32G32B32A32_FLOAT",_[_.DXGI_FORMAT_R32G32B32A32_UINT=3]="DXGI_FORMAT_R32G32B32A32_UINT",_[_.DXGI_FORMAT_R32G32B32A32_SINT=4]="DXGI_FORMAT_R32G32B32A32_SINT",_[_.DXGI_FORMAT_R32G32B32_TYPELESS=5]="DXGI_FORMAT_R32G32B32_TYPELESS",_[_.DXGI_FORMAT_R32G32B32_FLOAT=6]="DXGI_FORMAT_R32G32B32_FLOAT",_[_.DXGI_FORMAT_R32G32B32_UINT=7]="DXGI_FORMAT_R32G32B32_UINT",_[_.DXGI_FORMAT_R32G32B32_SINT=8]="DXGI_FORMAT_R32G32B32_SINT",_[_.DXGI_FORMAT_R16G16B16A16_TYPELESS=9]="DXGI_FORMAT_R16G16B16A16_TYPELESS",_[_.DXGI_FORMAT_R16G16B16A16_FLOAT=10]="DXGI_FORMAT_R16G16B16A16_FLOAT",_[_.DXGI_FORMAT_R16G16B16A16_UNORM=11]="DXGI_FORMAT_R16G16B16A16_UNORM",_[_.DXGI_FORMAT_R16G16B16A16_UINT=12]="DXGI_FORMAT_R16G16B16A16_UINT",_[_.DXGI_FORMAT_R16G16B16A16_SNORM=13]="DXGI_FORMAT_R16G16B16A16_SNORM",_[_.DXGI_FORMAT_R16G16B16A16_SINT=14]="DXGI_FORMAT_R16G16B16A16_SINT",_[_.DXGI_FORMAT_R32G32_TYPELESS=15]="DXGI_FORMAT_R32G32_TYPELESS",_[_.DXGI_FORMAT_R32G32_FLOAT=16]="DXGI_FORMAT_R32G32_FLOAT",_[_.DXGI_FORMAT_R32G32_UINT=17]="DXGI_FORMAT_R32G32_UINT",_[_.DXGI_FORMAT_R32G32_SINT=18]="DXGI_FORMAT_R32G32_SINT",_[_.DXGI_FORMAT_R32G8X24_TYPELESS=19]="DXGI_FORMAT_R32G8X24_TYPELESS",_[_.DXGI_FORMAT_D32_FLOAT_S8X24_UINT=20]="DXGI_FORMAT_D32_FLOAT_S8X24_UINT",_[_.DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS=21]="DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS",_[_.DXGI_FORMAT_X32_TYPELESS_G8X24_UINT=22]="DXGI_FORMAT_X32_TYPELESS_G8X24_UINT",_[_.DXGI_FORMAT_R10G10B10A2_TYPELESS=23]="DXGI_FORMAT_R10G10B10A2_TYPELESS",_[_.DXGI_FORMAT_R10G10B10A2_UNORM=24]="DXGI_FORMAT_R10G10B10A2_UNORM",_[_.DXGI_FORMAT_R10G10B10A2_UINT=25]="DXGI_FORMAT_R10G10B10A2_UINT",_[_.DXGI_FORMAT_R11G11B10_FLOAT=26]="DXGI_FORMAT_R11G11B10_FLOAT",_[_.DXGI_FORMAT_R8G8B8A8_TYPELESS=27]="DXGI_FORMAT_R8G8B8A8_TYPELESS",_[_.DXGI_FORMAT_R8G8B8A8_UNORM=28]="DXGI_FORMAT_R8G8B8A8_UNORM",_[_.DXGI_FORMAT_R8G8B8A8_UNORM_SRGB=29]="DXGI_FORMAT_R8G8B8A8_UNORM_SRGB",_[_.DXGI_FORMAT_R8G8B8A8_UINT=30]="DXGI_FORMAT_R8G8B8A8_UINT",_[_.DXGI_FORMAT_R8G8B8A8_SNORM=31]="DXGI_FORMAT_R8G8B8A8_SNORM",_[_.DXGI_FORMAT_R8G8B8A8_SINT=32]="DXGI_FORMAT_R8G8B8A8_SINT",_[_.DXGI_FORMAT_R16G16_TYPELESS=33]="DXGI_FORMAT_R16G16_TYPELESS",_[_.DXGI_FORMAT_R16G16_FLOAT=34]="DXGI_FORMAT_R16G16_FLOAT",_[_.DXGI_FORMAT_R16G16_UNORM=35]="DXGI_FORMAT_R16G16_UNORM",_[_.DXGI_FORMAT_R16G16_UINT=36]="DXGI_FORMAT_R16G16_UINT",_[_.DXGI_FORMAT_R16G16_SNORM=37]="DXGI_FORMAT_R16G16_SNORM",_[_.DXGI_FORMAT_R16G16_SINT=38]="DXGI_FORMAT_R16G16_SINT",_[_.DXGI_FORMAT_R32_TYPELESS=39]="DXGI_FORMAT_R32_TYPELESS",_[_.DXGI_FORMAT_D32_FLOAT=40]="DXGI_FORMAT_D32_FLOAT",_[_.DXGI_FORMAT_R32_FLOAT=41]="DXGI_FORMAT_R32_FLOAT",_[_.DXGI_FORMAT_R32_UINT=42]="DXGI_FORMAT_R32_UINT",_[_.DXGI_FORMAT_R32_SINT=43]="DXGI_FORMAT_R32_SINT",_[_.DXGI_FORMAT_R24G8_TYPELESS=44]="DXGI_FORMAT_R24G8_TYPELESS",_[_.DXGI_FORMAT_D24_UNORM_S8_UINT=45]="DXGI_FORMAT_D24_UNORM_S8_UINT",_[_.DXGI_FORMAT_R24_UNORM_X8_TYPELESS=46]="DXGI_FORMAT_R24_UNORM_X8_TYPELESS",_[_.DXGI_FORMAT_X24_TYPELESS_G8_UINT=47]="DXGI_FORMAT_X24_TYPELESS_G8_UINT",_[_.DXGI_FORMAT_R8G8_TYPELESS=48]="DXGI_FORMAT_R8G8_TYPELESS",_[_.DXGI_FORMAT_R8G8_UNORM=49]="DXGI_FORMAT_R8G8_UNORM",_[_.DXGI_FORMAT_R8G8_UINT=50]="DXGI_FORMAT_R8G8_UINT",_[_.DXGI_FORMAT_R8G8_SNORM=51]="DXGI_FORMAT_R8G8_SNORM",_[_.DXGI_FORMAT_R8G8_SINT=52]="DXGI_FORMAT_R8G8_SINT",_[_.DXGI_FORMAT_R16_TYPELESS=53]="DXGI_FORMAT_R16_TYPELESS",_[_.DXGI_FORMAT_R16_FLOAT=54]="DXGI_FORMAT_R16_FLOAT",_[_.DXGI_FORMAT_D16_UNORM=55]="DXGI_FORMAT_D16_UNORM",_[_.DXGI_FORMAT_R16_UNORM=56]="DXGI_FORMAT_R16_UNORM",_[_.DXGI_FORMAT_R16_UINT=57]="DXGI_FORMAT_R16_UINT",_[_.DXGI_FORMAT_R16_SNORM=58]="DXGI_FORMAT_R16_SNORM",_[_.DXGI_FORMAT_R16_SINT=59]="DXGI_FORMAT_R16_SINT",_[_.DXGI_FORMAT_R8_TYPELESS=60]="DXGI_FORMAT_R8_TYPELESS",_[_.DXGI_FORMAT_R8_UNORM=61]="DXGI_FORMAT_R8_UNORM",_[_.DXGI_FORMAT_R8_UINT=62]="DXGI_FORMAT_R8_UINT",_[_.DXGI_FORMAT_R8_SNORM=63]="DXGI_FORMAT_R8_SNORM",_[_.DXGI_FORMAT_R8_SINT=64]="DXGI_FORMAT_R8_SINT",_[_.DXGI_FORMAT_A8_UNORM=65]="DXGI_FORMAT_A8_UNORM",_[_.DXGI_FORMAT_R1_UNORM=66]="DXGI_FORMAT_R1_UNORM",_[_.DXGI_FORMAT_R9G9B9E5_SHAREDEXP=67]="DXGI_FORMAT_R9G9B9E5_SHAREDEXP",_[_.DXGI_FORMAT_R8G8_B8G8_UNORM=68]="DXGI_FORMAT_R8G8_B8G8_UNORM",_[_.DXGI_FORMAT_G8R8_G8B8_UNORM=69]="DXGI_FORMAT_G8R8_G8B8_UNORM",_[_.DXGI_FORMAT_BC1_TYPELESS=70]="DXGI_FORMAT_BC1_TYPELESS",_[_.DXGI_FORMAT_BC1_UNORM=71]="DXGI_FORMAT_BC1_UNORM",_[_.DXGI_FORMAT_BC1_UNORM_SRGB=72]="DXGI_FORMAT_BC1_UNORM_SRGB",_[_.DXGI_FORMAT_BC2_TYPELESS=73]="DXGI_FORMAT_BC2_TYPELESS",_[_.DXGI_FORMAT_BC2_UNORM=74]="DXGI_FORMAT_BC2_UNORM",_[_.DXGI_FORMAT_BC2_UNORM_SRGB=75]="DXGI_FORMAT_BC2_UNORM_SRGB",_[_.DXGI_FORMAT_BC3_TYPELESS=76]="DXGI_FORMAT_BC3_TYPELESS",_[_.DXGI_FORMAT_BC3_UNORM=77]="DXGI_FORMAT_BC3_UNORM",_[_.DXGI_FORMAT_BC3_UNORM_SRGB=78]="DXGI_FORMAT_BC3_UNORM_SRGB",_[_.DXGI_FORMAT_BC4_TYPELESS=79]="DXGI_FORMAT_BC4_TYPELESS",_[_.DXGI_FORMAT_BC4_UNORM=80]="DXGI_FORMAT_BC4_UNORM",_[_.DXGI_FORMAT_BC4_SNORM=81]="DXGI_FORMAT_BC4_SNORM",_[_.DXGI_FORMAT_BC5_TYPELESS=82]="DXGI_FORMAT_BC5_TYPELESS",_[_.DXGI_FORMAT_BC5_UNORM=83]="DXGI_FORMAT_BC5_UNORM",_[_.DXGI_FORMAT_BC5_SNORM=84]="DXGI_FORMAT_BC5_SNORM",_[_.DXGI_FORMAT_B5G6R5_UNORM=85]="DXGI_FORMAT_B5G6R5_UNORM",_[_.DXGI_FORMAT_B5G5R5A1_UNORM=86]="DXGI_FORMAT_B5G5R5A1_UNORM",_[_.DXGI_FORMAT_B8G8R8A8_UNORM=87]="DXGI_FORMAT_B8G8R8A8_UNORM",_[_.DXGI_FORMAT_B8G8R8X8_UNORM=88]="DXGI_FORMAT_B8G8R8X8_UNORM",_[_.DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM=89]="DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM",_[_.DXGI_FORMAT_B8G8R8A8_TYPELESS=90]="DXGI_FORMAT_B8G8R8A8_TYPELESS",_[_.DXGI_FORMAT_B8G8R8A8_UNORM_SRGB=91]="DXGI_FORMAT_B8G8R8A8_UNORM_SRGB",_[_.DXGI_FORMAT_B8G8R8X8_TYPELESS=92]="DXGI_FORMAT_B8G8R8X8_TYPELESS",_[_.DXGI_FORMAT_B8G8R8X8_UNORM_SRGB=93]="DXGI_FORMAT_B8G8R8X8_UNORM_SRGB",_[_.DXGI_FORMAT_BC6H_TYPELESS=94]="DXGI_FORMAT_BC6H_TYPELESS",_[_.DXGI_FORMAT_BC6H_UF16=95]="DXGI_FORMAT_BC6H_UF16",_[_.DXGI_FORMAT_BC6H_SF16=96]="DXGI_FORMAT_BC6H_SF16",_[_.DXGI_FORMAT_BC7_TYPELESS=97]="DXGI_FORMAT_BC7_TYPELESS",_[_.DXGI_FORMAT_BC7_UNORM=98]="DXGI_FORMAT_BC7_UNORM",_[_.DXGI_FORMAT_BC7_UNORM_SRGB=99]="DXGI_FORMAT_BC7_UNORM_SRGB",_[_.DXGI_FORMAT_AYUV=100]="DXGI_FORMAT_AYUV",_[_.DXGI_FORMAT_Y410=101]="DXGI_FORMAT_Y410",_[_.DXGI_FORMAT_Y416=102]="DXGI_FORMAT_Y416",_[_.DXGI_FORMAT_NV12=103]="DXGI_FORMAT_NV12",_[_.DXGI_FORMAT_P010=104]="DXGI_FORMAT_P010",_[_.DXGI_FORMAT_P016=105]="DXGI_FORMAT_P016",_[_.DXGI_FORMAT_420_OPAQUE=106]="DXGI_FORMAT_420_OPAQUE",_[_.DXGI_FORMAT_YUY2=107]="DXGI_FORMAT_YUY2",_[_.DXGI_FORMAT_Y210=108]="DXGI_FORMAT_Y210",_[_.DXGI_FORMAT_Y216=109]="DXGI_FORMAT_Y216",_[_.DXGI_FORMAT_NV11=110]="DXGI_FORMAT_NV11",_[_.DXGI_FORMAT_AI44=111]="DXGI_FORMAT_AI44",_[_.DXGI_FORMAT_IA44=112]="DXGI_FORMAT_IA44",_[_.DXGI_FORMAT_P8=113]="DXGI_FORMAT_P8",_[_.DXGI_FORMAT_A8P8=114]="DXGI_FORMAT_A8P8",_[_.DXGI_FORMAT_B4G4R4A4_UNORM=115]="DXGI_FORMAT_B4G4R4A4_UNORM",_[_.DXGI_FORMAT_P208=116]="DXGI_FORMAT_P208",_[_.DXGI_FORMAT_V208=117]="DXGI_FORMAT_V208",_[_.DXGI_FORMAT_V408=118]="DXGI_FORMAT_V408",_[_.DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE=119]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE",_[_.DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE=120]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE",_[_.DXGI_FORMAT_FORCE_UINT=121]="DXGI_FORMAT_FORCE_UINT"}(F||(F={})),function(_){_[_.DDS_DIMENSION_TEXTURE1D=2]="DDS_DIMENSION_TEXTURE1D",_[_.DDS_DIMENSION_TEXTURE2D=3]="DDS_DIMENSION_TEXTURE2D",_[_.DDS_DIMENSION_TEXTURE3D=6]="DDS_DIMENSION_TEXTURE3D"}(s||(s={}));var c,U,p,x=((D={})[827611204]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,D[861165636]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,D[894720068]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,D),d=((n={})[F.DXGI_FORMAT_BC1_TYPELESS]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,n[F.DXGI_FORMAT_BC1_UNORM]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,n[F.DXGI_FORMAT_BC2_TYPELESS]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,n[F.DXGI_FORMAT_BC2_UNORM]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,n[F.DXGI_FORMAT_BC3_TYPELESS]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,n[F.DXGI_FORMAT_BC3_UNORM]=exports.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,n[F.DXGI_FORMAT_BC1_UNORM_SRGB]=exports.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,n[F.DXGI_FORMAT_BC2_UNORM_SRGB]=exports.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,n[F.DXGI_FORMAT_BC3_UNORM_SRGB]=exports.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,n);function h(_){var e=new Uint32Array(_);if(542327876!==e[0])throw new Error("Invalid DDS file magic word");var R=new Uint32Array(_,0,124/Uint32Array.BYTES_PER_ELEMENT),T=R[i],t=R[B],r=R[u],A=new Uint32Array(_,P*Uint32Array.BYTES_PER_ELEMENT,32/Uint32Array.BYTES_PER_ELEMENT),O=A[1];if(4&O){var S=A[N];if(808540228!==S){var M=x[S],G=new Uint8Array(_,128);return[new I(G,{format:M,width:t,height:T,levels:r})]}var D=new Uint32Array(e.buffer,128,20/Uint32Array.BYTES_PER_ELEMENT),n=D[C],o=D[f],a=D[L],X=D[l],F=d[n];if(void 0===F)throw new Error("DDSParser cannot parse texture data with DXGI format "+n);if(4===a)throw new Error("DDSParser does not support cubemap textures");if(o===s.DDS_DIMENSION_TEXTURE3D)throw new Error("DDSParser does not supported 3D texture data");var c=new Array;if(1===X)c.push(new Uint8Array(_,148));else{for(var U=E[F],p=0,h=t,v=T,w=0;w<r;w++){p+=Math.max(1,h+3&-4)*Math.max(1,v+3&-4)*U,h>>>=1,v>>>=1}var Y=148;for(w=0;w<X;w++)c.push(new Uint8Array(_,Y,p)),Y+=p}return c.map((function(_){return new I(_,{format:F,width:t,height:T,levels:r})}))}if(64&O)throw new Error("DDSParser does not support uncompressed texture data.");if(512&O)throw new Error("DDSParser does not supported YUV uncompressed texture data.");if(131072&O)throw new Error("DDSParser does not support single-channel (lumninance) texture data!");if(2&O)throw new Error("DDSParser does not support single-channel (alpha) texture data!");throw new Error("DDSParser failed to load a texture file due to an unknown reason!")}var v=[171,75,84,88,32,49,49,187,13,10,26,10],w=12,Y=16,y=24,b=28,m=36,g=40,H=44,V=48,W=52,k=56,K=60,j=((c={})[A.TYPES.UNSIGNED_BYTE]=1,c[A.TYPES.UNSIGNED_SHORT]=2,c[A.TYPES.INT]=4,c[A.TYPES.UNSIGNED_INT]=4,c[A.TYPES.FLOAT]=4,c[A.TYPES.HALF_FLOAT]=8,c),Q=((U={})[A.FORMATS.RGBA]=4,U[A.FORMATS.RGB]=3,U[A.FORMATS.RG]=2,U[A.FORMATS.RED]=1,U[A.FORMATS.LUMINANCE]=1,U[A.FORMATS.LUMINANCE_ALPHA]=2,U[A.FORMATS.ALPHA]=1,U),q=((p={})[A.TYPES.UNSIGNED_SHORT_4_4_4_4]=2,p[A.TYPES.UNSIGNED_SHORT_5_5_5_1]=2,p[A.TYPES.UNSIGNED_SHORT_5_6_5]=2,p);function z(_,e,T){void 0===T&&(T=!1);var t=new DataView(e);if(!function(_,e){for(var R=0;R<v.length;R++)if(e.getUint8(R)!==v[R])return!1;return!0}(0,t))return null;var r=67305985===t.getUint32(w,!0),O=t.getUint32(Y,r),S=t.getUint32(y,r),M=t.getUint32(b,r),G=t.getUint32(m,r),D=t.getUint32(g,r)||1,n=t.getUint32(H,r)||1,o=t.getUint32(V,r)||1,a=t.getUint32(W,r),X=t.getUint32(k,r),F=t.getUint32(K,r);if(0===D||1!==n)throw new Error("Only 2D textures are supported");if(1!==a)throw new Error("CubeTextures are not supported by KTXLoader yet!");if(1!==o)throw new Error("WebGL does not support array textures");var s,i=G+3&-4,B=D+3&-4,u=new Array(o),P=G*D;if(0===O&&(P=i*B),void 0===(s=0!==O?j[O]?j[O]*Q[S]:q[O]:E[M]))throw new Error("Unable to resolve the pixel format stored in the *.ktx file!");for(var N=T?function(_,e,R){var T=new Map,t=0;for(;t<e;){var r=_.getUint32(64+t,R),A=64+t+4,E=3-(r+3)%4;if(0===r||r>e-t){console.error("KTXLoader: keyAndValueByteSize out of bounds");break}for(var O=0;O<r&&0!==_.getUint8(A+O);O++);if(-1===O){console.error("KTXLoader: Failed to find null byte terminating kvData key");break}var S=(new TextDecoder).decode(new Uint8Array(_.buffer,A,O)),M=new DataView(_.buffer,A+O+1,r-O-1);T.set(S,M),t+=4+r+E}return T}(t,F,r):null,C=P*s,f=G,L=D,l=i,c=B,U=64+F,p=0;p<X;p++){for(var x=t.getUint32(U,r),d=U+4,h=0;h<o;h++){var z=u[h];z||(z=u[h]=new Array(X)),z[p]={levelID:p,levelWidth:X>1||0!==O?f:l,levelHeight:X>1||0!==O?L:c,levelBuffer:new Uint8Array(e,d,C)},d+=C}U=(U+=x+4)%4!=0?U+4-U%4:U,C=(l=(f=f>>1||1)****&-4)*(c=(L=L>>1||1)****&-4)*s}return 0!==O?{uncompressed:u.map((function(_){var e=_[0].levelBuffer,T=!1;return O===A.TYPES.FLOAT?e=new Float32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4):O===A.TYPES.UNSIGNED_INT?(T=!0,e=new Uint32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4)):O===A.TYPES.INT&&(T=!0,e=new Int32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4)),{resource:new R.BufferResource(e,{width:_[0].levelWidth,height:_[0].levelHeight}),type:O,format:T?J(S):S}})),kvData:N}:{compressed:u.map((function(_){return new I(null,{format:M,width:G,height:D,levels:X,levelBuffers:_})})),kvData:N}}function J(_){switch(_){case A.FORMATS.RGBA:return A.FORMATS.RGBA_INTEGER;case A.FORMATS.RGB:return A.FORMATS.RGB_INTEGER;case A.FORMATS.RG:return A.FORMATS.RG_INTEGER;case A.FORMATS.RED:return A.FORMATS.RED_INTEGER;default:return _}}T.LoaderResource.setExtensionXhrType("dds",T.LoaderResource.XHR_RESPONSE_TYPE.BUFFER);var Z=function(){function _(){}return _.use=function(_,e){if("dds"===_.extension&&_.data)try{Object.assign(_,X(_.name||_.url,h(_.data),_.metadata))}catch(_){return void e(_)}e()},_.extension=R.ExtensionType.Loader,_}();T.LoaderResource.setExtensionXhrType("ktx",T.LoaderResource.XHR_RESPONSE_TYPE.BUFFER);var $=function(){function _(){}return _.use=function(_,e){if("ktx"===_.extension&&_.data)try{var T=_.name||_.url,t=z(0,_.data,this.loadKeyValueData),r=t.compressed,E=t.uncompressed,O=t.kvData;if(r){var S=X(T,r,_.metadata);if(O&&S.textures)for(var M in S.textures)S.textures[M].baseTexture.ktxKeyValueData=O;Object.assign(_,S)}else if(E){var G={};E.forEach((function(_,e){var t=new R.Texture(new R.BaseTexture(_.resource,{mipmap:A.MIPMAP_MODES.OFF,alphaMode:A.ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,type:_.type,format:_.format})),r=T+"-"+(e+1);O&&(t.baseTexture.ktxKeyValueData=O),R.BaseTexture.addToCache(t.baseTexture,r),R.Texture.addToCache(t,r),0===e&&(G[T]=t,R.BaseTexture.addToCache(t.baseTexture,T),R.Texture.addToCache(t,T)),G[r]=t})),Object.assign(_,{textures:G})}}catch(_){return void e(_)}e()},_.extension=R.ExtensionType.Loader,_.loadKeyValueData=!1,_}();exports.BlobResource=o,exports.CompressedTextureLoader=a,exports.CompressedTextureResource=I,exports.DDSLoader=Z,exports.FORMATS_TO_COMPONENTS=Q,exports.INTERNAL_FORMAT_TO_BYTES_PER_PIXEL=E,exports.KTXLoader=$,exports.TYPES_TO_BYTES_PER_COMPONENT=j,exports.TYPES_TO_BYTES_PER_PIXEL=q,exports.parseDDS=h,exports.parseKTX=z;
//# sourceMappingURL=compressed-textures.min.js.map
