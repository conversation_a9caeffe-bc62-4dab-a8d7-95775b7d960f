{"name": "@pixi/compressed-textures", "version": "6.5.10", "description": "Loaders for compressed texture file formats", "keywords": ["pixi.js", "pixi", "loaders", "ktx", "dds", "compressed-textures"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/pixijs/pixi.js#readme", "license": "MIT", "main": "dist/cjs/compressed-textures.js", "module": "dist/esm/compressed-textures.mjs", "bundle": "dist/browser/compressed-textures.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/compressed-textures.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/compressed-textures.js"}}}, "publishConfig": {"access": "public"}, "files": ["dist", "*.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/pixijs/pixi.js.git"}, "scripts": {"test": "echo \"Error: run tests from root\" && exit 1"}, "sideEffects": true, "bugs": {"url": "https://github.com/pixijs/pixi.js/issues"}, "peerDependencies": {"@pixi/constants": "6.5.10", "@pixi/core": "6.5.10", "@pixi/loaders": "6.5.10", "@pixi/settings": "6.5.10", "@pixi/utils": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}