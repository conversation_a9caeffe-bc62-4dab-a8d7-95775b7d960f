<!DOCTYPE html>
<html>
<head>
    <title>最终头像存储调试</title>
</head>
<body>
    <h1>最终头像存储调试</h1>
    <div>
        <button onclick="testDirectAccess()">直接访问Electron Store</button>
        <button onclick="testUnifiedManager()">测试UnifiedConfigManager</button>
        <button onclick="saveTestAvatar()">保存测试头像</button>
        <button onclick="clearAvatars()">清空头像存储</button>
    </div>
    <div id="output" style="margin-top: 20px; font-family: monospace; white-space: pre-wrap;"></div>

    <script>
        function log(msg) {
            const output = document.getElementById('output');
            output.textContent += new Date().toISOString() + ': ' + msg + '\n';
        }

        async function testDirectAccess() {
            try {
                log('=== 直接访问Electron Store ===');
                
                if (!window.electronAPI) {
                    log('❌ 没有检测到Electron API');
                    return;
                }

                // 检查所有相关键
                const keys = ['protagonist-avatars', 'protagonist-avatar', 'preset-style-images'];
                for (const key of keys) {
                    const value = await window.electronAPI.store.get(key);
                    log(`${key}:`);
                    if (value) {
                        log(`  类型: ${typeof value}`);
                        log(`  内容: ${JSON.stringify(value, null, 2).substring(0, 200)}...`);
                        if (typeof value === 'object') {
                            log(`  键: ${Object.keys(value).join(', ')}`);
                        }
                    } else {
                        log(`  值: null/undefined`);
                    }
                    log('');
                }
                
            } catch (error) {
                log('❌ 测试失败: ' + error.message);
            }
        }

        async function testUnifiedManager() {
            try {
                log('=== 测试UnifiedConfigManager ===');
                
                // 这里需要模拟unifiedConfigManager的逻辑
                if (!window.electronAPI) {
                    log('❌ 没有检测到Electron API');
                    return;
                }

                const avatars = await window.electronAPI.store.get('protagonist-avatars') || {};
                log('avatars对象: ' + JSON.stringify(avatars));
                
                const avatarObject = avatars['current'];
                log('current键的值:');
                log('  类型: ' + typeof avatarObject);
                log('  是否为对象: ' + (typeof avatarObject === 'object' && avatarObject !== null));
                if (avatarObject) {
                    log('  结构: ' + JSON.stringify(avatarObject));
                    if (avatarObject.url) {
                        log('  URL: ' + avatarObject.url);
                    }
                }
                
            } catch (error) {
                log('❌ 测试失败: ' + error.message);
            }
        }

        async function saveTestAvatar() {
            try {
                log('=== 保存测试头像 ===');
                
                if (!window.electronAPI) {
                    log('❌ 没有检测到Electron API');
                    return;
                }

                const testUrl = 'http://localhost:3001/avatars/test_final_' + Date.now() + '.png';
                
                // 按照新的格式保存
                const avatars = await window.electronAPI.store.get('protagonist-avatars') || {};
                const avatarObject = {
                    url: testUrl,
                    timestamp: Date.now(),
                    type: 'protagonist-avatar'
                };
                
                avatars['current'] = avatarObject;
                await window.electronAPI.store.set('protagonist-avatars', avatars);
                
                log('✅ 保存成功: ' + testUrl);
                
                // 立即验证
                const verification = await window.electronAPI.store.get('protagonist-avatars') || {};
                log('验证结果: ' + JSON.stringify(verification));
                
            } catch (error) {
                log('❌ 保存失败: ' + error.message);
            }
        }

        async function clearAvatars() {
            try {
                log('=== 清空头像存储 ===');
                
                if (!window.electronAPI) {
                    log('❌ 没有检测到Electron API');
                    return;
                }

                await window.electronAPI.store.set('protagonist-avatars', {});
                log('✅ 头像存储已清空');
                
            } catch (error) {
                log('❌ 清空失败: ' + error.message);
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成，环境: ' + (window.electronAPI ? 'Electron' : 'Browser'));
            testDirectAccess();
        };
    </script>
</body>
</html>