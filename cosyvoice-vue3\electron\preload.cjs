const { contextBridge, ipc<PERSON>enderer } = require('electron')

// 暴露受保护的方法给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 获取后端配置
  getBackendConfig: () => ipcRenderer.invoke('get-backend-config'),
  
  // 重启后端服务
  restartBackend: () => ipcRenderer.invoke('restart-backend'),
  
  // 监听后端状态变化
  onBackendStatus: (callback) => {
    ipcRenderer.on('backend-status', (event, data) => callback(data))
  },
  
  // 移除后端状态监听器
  removeBackendStatusListener: () => {
    ipcRenderer.removeAllListeners('backend-status')
  },
  
  // 检查是否在Electron环境中
  isElectron: true,
  
  // 获取平台信息
  platform: process.platform,
  
  // 版本信息
  versions: {
    node: process.versions.node,
    electron: process.versions.electron,
    chrome: process.versions.chrome
  },

  // 🔄 Electron Store API
  store: {
    get: (key, defaultValue) => ipcRenderer.invoke('store-get', key, defaultValue),
    set: (key, value) => ipcRenderer.invoke('store-set', key, value),
    has: (key) => ipcRenderer.invoke('store-has', key),
    delete: (key) => ipcRenderer.invoke('store-delete', key),
    clear: () => ipcRenderer.invoke('store-clear'),
    
    // 监听数据变化
    onDidChange: (key, callback) => {
      ipcRenderer.on(`store-changed-${key}`, (event, newValue, oldValue) => {
        callback(newValue, oldValue);
      });
    },
    
    // 移除变化监听器
    removeChangeListener: (key) => {
      ipcRenderer.removeAllListeners(`store-changed-${key}`);
    }
  },

  // 🖼️ 图片管理API
  images: {
    save: (imageId, base64Data, filename) => ipcRenderer.invoke('save-image-file', imageId, base64Data, filename),
    load: (filePath) => ipcRenderer.invoke('load-image-file', filePath)
  },

  // 🔑 新增：头像和风格图片HTTP服务API
  saveAvatarImage: (avatarId, base64Data) => ipcRenderer.invoke('save-avatar-image', avatarId, base64Data),
  saveStyleImage: (styleId, base64Data) => ipcRenderer.invoke('save-style-image', styleId, base64Data)
})

// 安全的控制台日志
contextBridge.exposeInMainWorld('electronLog', {
  info: (message) => console.log(`[Electron] ${message}`),
  warn: (message) => console.warn(`[Electron] ${message}`),
  error: (message) => console.error(`[Electron] ${message}`)
})

// 在 'window' 对象上暴露一个安全的 'electron' API
contextBridge.exposeInMainWorld('electron', {
  /**
   * 请求主进程将文件保存到 public 目录下的指定相对路径
   * @param relativePath - 例如 'assets/images/cards/hexagram/my-image.png'
   * @param data - 文件的 ArrayBuffer 数据
   * @returns {Promise<string>} - 保存成功后返回最终的相对路径
   */
  saveFileToPublic: (relativePath, data) => 
    ipcRenderer.invoke('save-file-to-public', { relativePath, data }),

  /**
   * 请求主进程从 public 目录删除文件
   * @param relativePath - 文件的相对路径
   * @returns {Promise<void>}
   */
  deleteFileFromPublic: (relativePath) =>
    ipcRenderer.invoke('delete-file-from-public', relativePath),
  
  // 这里可以继续暴露其他主进程的API
}) 