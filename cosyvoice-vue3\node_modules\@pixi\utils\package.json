{"name": "@pixi/utils", "version": "6.5.10", "main": "dist/cjs/utils.js", "module": "dist/esm/utils.mjs", "bundle": "dist/browser/utils.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/utils.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/utils.js"}}}, "namespace": "PIXI.utils", "description": "Collection of utilities used by PixiJS", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "dependencies": {"@types/earcut": "^2.1.0", "earcut": "^2.2.4", "eventemitter3": "^3.1.0", "url": "^0.11.0"}, "peerDependencies": {"@pixi/constants": "6.5.10", "@pixi/settings": "6.5.10"}, "devDependencies": {"css-color-names": "^1.0.1"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}