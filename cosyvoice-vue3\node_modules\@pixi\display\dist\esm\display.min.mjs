/*!
 * @pixi/display - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/display is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{settings as t}from"@pixi/settings";import{Rectangle as i,RAD_TO_DEG as e,DEG_TO_RAD as n,Transform as r}from"@pixi/math";import{EventEmitter as s,removeItems as o}from"@pixi/utils";import{MASK_TYPES as a}from"@pixi/constants";t.SORTABLE_CHILDREN=!1;var h=function(){function t(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.rect=null,this.updateID=-1}return t.prototype.isEmpty=function(){return this.minX>this.maxX||this.minY>this.maxY},t.prototype.clear=function(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0},t.prototype.getRectangle=function(t){return this.minX>this.maxX||this.minY>this.maxY?i.EMPTY:((t=t||new i(0,0,1,1)).x=this.minX,t.y=this.minY,t.width=this.maxX-this.minX,t.height=this.maxY-this.minY,t)},t.prototype.addPoint=function(t){this.minX=Math.min(this.minX,t.x),this.maxX=Math.max(this.maxX,t.x),this.minY=Math.min(this.minY,t.y),this.maxY=Math.max(this.maxY,t.y)},t.prototype.addPointMatrix=function(t,i){var e=t.a,n=t.b,r=t.c,s=t.d,o=t.tx,a=t.ty,h=e*i.x+r*i.y+o,l=n*i.x+s*i.y+a;this.minX=Math.min(this.minX,h),this.maxX=Math.max(this.maxX,h),this.minY=Math.min(this.minY,l),this.maxY=Math.max(this.maxY,l)},t.prototype.addQuad=function(t){var i=this.minX,e=this.minY,n=this.maxX,r=this.maxY,s=t[0],o=t[1];i=s<i?s:i,e=o<e?o:e,n=s>n?s:n,r=o>r?o:r,i=(s=t[2])<i?s:i,e=(o=t[3])<e?o:e,n=s>n?s:n,r=o>r?o:r,i=(s=t[4])<i?s:i,e=(o=t[5])<e?o:e,n=s>n?s:n,r=o>r?o:r,i=(s=t[6])<i?s:i,e=(o=t[7])<e?o:e,n=s>n?s:n,r=o>r?o:r,this.minX=i,this.minY=e,this.maxX=n,this.maxY=r},t.prototype.addFrame=function(t,i,e,n,r){this.addFrameMatrix(t.worldTransform,i,e,n,r)},t.prototype.addFrameMatrix=function(t,i,e,n,r){var s=t.a,o=t.b,a=t.c,h=t.d,l=t.tx,d=t.ty,u=this.minX,p=this.minY,m=this.maxX,c=this.maxY,f=s*i+a*e+l,y=o*i+h*e+d;u=f<u?f:u,p=y<p?y:p,m=f>m?f:m,c=y>c?y:c,u=(f=s*n+a*e+l)<u?f:u,p=(y=o*n+h*e+d)<p?y:p,m=f>m?f:m,c=y>c?y:c,u=(f=s*i+a*r+l)<u?f:u,p=(y=o*i+h*r+d)<p?y:p,m=f>m?f:m,c=y>c?y:c,u=(f=s*n+a*r+l)<u?f:u,p=(y=o*n+h*r+d)<p?y:p,m=f>m?f:m,c=y>c?y:c,this.minX=u,this.minY=p,this.maxX=m,this.maxY=c},t.prototype.addVertexData=function(t,i,e){for(var n=this.minX,r=this.minY,s=this.maxX,o=this.maxY,a=i;a<e;a+=2){var h=t[a],l=t[a+1];n=h<n?h:n,r=l<r?l:r,s=h>s?h:s,o=l>o?l:o}this.minX=n,this.minY=r,this.maxX=s,this.maxY=o},t.prototype.addVertices=function(t,i,e,n){this.addVerticesMatrix(t.worldTransform,i,e,n)},t.prototype.addVerticesMatrix=function(t,i,e,n,r,s){void 0===r&&(r=0),void 0===s&&(s=r);for(var o=t.a,a=t.b,h=t.c,l=t.d,d=t.tx,u=t.ty,p=this.minX,m=this.minY,c=this.maxX,f=this.maxY,y=e;y<n;y+=2){var b=i[y],x=i[y+1],_=o*b+h*x+d,v=l*x+a*b+u;p=Math.min(p,_-r),c=Math.max(c,_+r),m=Math.min(m,v-s),f=Math.max(f,v+s)}this.minX=p,this.minY=m,this.maxX=c,this.maxY=f},t.prototype.addBounds=function(t){var i=this.minX,e=this.minY,n=this.maxX,r=this.maxY;this.minX=t.minX<i?t.minX:i,this.minY=t.minY<e?t.minY:e,this.maxX=t.maxX>n?t.maxX:n,this.maxY=t.maxY>r?t.maxY:r},t.prototype.addBoundsMask=function(t,i){var e=t.minX>i.minX?t.minX:i.minX,n=t.minY>i.minY?t.minY:i.minY,r=t.maxX<i.maxX?t.maxX:i.maxX,s=t.maxY<i.maxY?t.maxY:i.maxY;if(e<=r&&n<=s){var o=this.minX,a=this.minY,h=this.maxX,l=this.maxY;this.minX=e<o?e:o,this.minY=n<a?n:a,this.maxX=r>h?r:h,this.maxY=s>l?s:l}},t.prototype.addBoundsMatrix=function(t,i){this.addFrameMatrix(i,t.minX,t.minY,t.maxX,t.maxY)},t.prototype.addBoundsArea=function(t,i){var e=t.minX>i.x?t.minX:i.x,n=t.minY>i.y?t.minY:i.y,r=t.maxX<i.x+i.width?t.maxX:i.x+i.width,s=t.maxY<i.y+i.height?t.maxY:i.y+i.height;if(e<=r&&n<=s){var o=this.minX,a=this.minY,h=this.maxX,l=this.maxY;this.minX=e<o?e:o,this.minY=n<a?n:a,this.maxX=r>h?r:h,this.maxY=s>l?s:l}},t.prototype.pad=function(t,i){void 0===t&&(t=0),void 0===i&&(i=t),this.isEmpty()||(this.minX-=t,this.maxX+=t,this.minY-=i,this.maxY+=i)},t.prototype.addFramePad=function(t,i,e,n,r,s){t-=r,i-=s,e+=r,n+=s,this.minX=this.minX<t?this.minX:t,this.maxX=this.maxX>e?this.maxX:e,this.minY=this.minY<i?this.minY:i,this.maxY=this.maxY>n?this.maxY:n},t}(),l=function(t,i){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},l(t,i)};function d(t,i){function e(){this.constructor=t}l(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}var u=function(t){function s(){var i=t.call(this)||this;return i.tempDisplayObjectParent=null,i.transform=new r,i.alpha=1,i.visible=!0,i.renderable=!0,i.cullable=!1,i.cullArea=null,i.parent=null,i.worldAlpha=1,i._lastSortedIndex=0,i._zIndex=0,i.filterArea=null,i.filters=null,i._enabledFilters=null,i._bounds=new h,i._localBounds=null,i._boundsID=0,i._boundsRect=null,i._localBoundsRect=null,i._mask=null,i._maskRefCount=0,i._destroyed=!1,i.isSprite=!1,i.isMask=!1,i}return d(s,t),s.mixin=function(t){for(var i=Object.keys(t),e=0;e<i.length;++e){var n=i[e];Object.defineProperty(s.prototype,n,Object.getOwnPropertyDescriptor(t,n))}},Object.defineProperty(s.prototype,"destroyed",{get:function(){return this._destroyed},enumerable:!1,configurable:!0}),s.prototype._recursivePostUpdateTransform=function(){this.parent?(this.parent._recursivePostUpdateTransform(),this.transform.updateTransform(this.parent.transform)):this.transform.updateTransform(this._tempDisplayObjectParent.transform)},s.prototype.updateTransform=function(){this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha},s.prototype.getBounds=function(t,e){return t||(this.parent?(this._recursivePostUpdateTransform(),this.updateTransform()):(this.parent=this._tempDisplayObjectParent,this.updateTransform(),this.parent=null)),this._bounds.updateID!==this._boundsID&&(this.calculateBounds(),this._bounds.updateID=this._boundsID),e||(this._boundsRect||(this._boundsRect=new i),e=this._boundsRect),this._bounds.getRectangle(e)},s.prototype.getLocalBounds=function(t){t||(this._localBoundsRect||(this._localBoundsRect=new i),t=this._localBoundsRect),this._localBounds||(this._localBounds=new h);var e=this.transform,n=this.parent;this.parent=null,this.transform=this._tempDisplayObjectParent.transform;var r=this._bounds,s=this._boundsID;this._bounds=this._localBounds;var o=this.getBounds(!1,t);return this.parent=n,this.transform=e,this._bounds=r,this._bounds.updateID+=this._boundsID-s,o},s.prototype.toGlobal=function(t,i,e){return void 0===e&&(e=!1),e||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.apply(t,i)},s.prototype.toLocal=function(t,i,e,n){return i&&(t=i.toGlobal(t,e,n)),n||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.applyInverse(t,e)},s.prototype.setParent=function(t){if(!t||!t.addChild)throw new Error("setParent: Argument must be a Container");return t.addChild(this),t},s.prototype.setTransform=function(t,i,e,n,r,s,o,a,h){return void 0===t&&(t=0),void 0===i&&(i=0),void 0===e&&(e=1),void 0===n&&(n=1),void 0===r&&(r=0),void 0===s&&(s=0),void 0===o&&(o=0),void 0===a&&(a=0),void 0===h&&(h=0),this.position.x=t,this.position.y=i,this.scale.x=e||1,this.scale.y=n||1,this.rotation=r,this.skew.x=s,this.skew.y=o,this.pivot.x=a,this.pivot.y=h,this},s.prototype.destroy=function(t){this.parent&&this.parent.removeChild(this),this._destroyed=!0,this.transform=null,this.parent=null,this._bounds=null,this.mask=null,this.cullArea=null,this.filters=null,this.filterArea=null,this.hitArea=null,this.interactive=!1,this.interactiveChildren=!1,this.emit("destroyed"),this.removeAllListeners()},Object.defineProperty(s.prototype,"_tempDisplayObjectParent",{get:function(){return null===this.tempDisplayObjectParent&&(this.tempDisplayObjectParent=new p),this.tempDisplayObjectParent},enumerable:!1,configurable:!0}),s.prototype.enableTempParent=function(){var t=this.parent;return this.parent=this._tempDisplayObjectParent,t},s.prototype.disableTempParent=function(t){this.parent=t},Object.defineProperty(s.prototype,"x",{get:function(){return this.position.x},set:function(t){this.transform.position.x=t},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"y",{get:function(){return this.position.y},set:function(t){this.transform.position.y=t},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"worldTransform",{get:function(){return this.transform.worldTransform},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"localTransform",{get:function(){return this.transform.localTransform},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"position",{get:function(){return this.transform.position},set:function(t){this.transform.position.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"scale",{get:function(){return this.transform.scale},set:function(t){this.transform.scale.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"pivot",{get:function(){return this.transform.pivot},set:function(t){this.transform.pivot.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"skew",{get:function(){return this.transform.skew},set:function(t){this.transform.skew.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"rotation",{get:function(){return this.transform.rotation},set:function(t){this.transform.rotation=t},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"angle",{get:function(){return this.transform.rotation*e},set:function(t){this.transform.rotation=t*n},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"zIndex",{get:function(){return this._zIndex},set:function(t){this._zIndex=t,this.parent&&(this.parent.sortDirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"worldVisible",{get:function(){var t=this;do{if(!t.visible)return!1;t=t.parent}while(t);return!0},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"mask",{get:function(){return this._mask},set:function(t){if(this._mask!==t){var i;if(this._mask)(i=this._mask.isMaskData?this._mask.maskObject:this._mask)&&(i._maskRefCount--,0===i._maskRefCount&&(i.renderable=!0,i.isMask=!1));if(this._mask=t,this._mask)(i=this._mask.isMaskData?this._mask.maskObject:this._mask)&&(0===i._maskRefCount&&(i.renderable=!1,i.isMask=!0),i._maskRefCount++)}},enumerable:!1,configurable:!0}),s}(s),p=function(t){function i(){var i=null!==t&&t.apply(this,arguments)||this;return i.sortDirty=null,i}return d(i,t),i}(u);function m(t,i){return t.zIndex===i.zIndex?t._lastSortedIndex-i._lastSortedIndex:t.zIndex-i.zIndex}u.prototype.displayObjectUpdateTransform=u.prototype.updateTransform;var c=function(i){function e(){var e=i.call(this)||this;return e.children=[],e.sortableChildren=t.SORTABLE_CHILDREN,e.sortDirty=!1,e}return d(e,i),e.prototype.onChildrenChange=function(t){},e.prototype.addChild=function(){for(var t=arguments,i=[],e=0;e<arguments.length;e++)i[e]=t[e];if(i.length>1)for(var n=0;n<i.length;n++)this.addChild(i[n]);else{var r=i[0];r.parent&&r.parent.removeChild(r),r.parent=this,this.sortDirty=!0,r.transform._parentID=-1,this.children.push(r),this._boundsID++,this.onChildrenChange(this.children.length-1),this.emit("childAdded",r,this,this.children.length-1),r.emit("added",this)}return i[0]},e.prototype.addChildAt=function(t,i){if(i<0||i>this.children.length)throw new Error(t+"addChildAt: The index "+i+" supplied is out of bounds "+this.children.length);return t.parent&&t.parent.removeChild(t),t.parent=this,this.sortDirty=!0,t.transform._parentID=-1,this.children.splice(i,0,t),this._boundsID++,this.onChildrenChange(i),t.emit("added",this),this.emit("childAdded",t,this,i),t},e.prototype.swapChildren=function(t,i){if(t!==i){var e=this.getChildIndex(t),n=this.getChildIndex(i);this.children[e]=i,this.children[n]=t,this.onChildrenChange(e<n?e:n)}},e.prototype.getChildIndex=function(t){var i=this.children.indexOf(t);if(-1===i)throw new Error("The supplied DisplayObject must be a child of the caller");return i},e.prototype.setChildIndex=function(t,i){if(i<0||i>=this.children.length)throw new Error("The index "+i+" supplied is out of bounds "+this.children.length);var e=this.getChildIndex(t);o(this.children,e,1),this.children.splice(i,0,t),this.onChildrenChange(i)},e.prototype.getChildAt=function(t){if(t<0||t>=this.children.length)throw new Error("getChildAt: Index ("+t+") does not exist.");return this.children[t]},e.prototype.removeChild=function(){for(var t=arguments,i=[],e=0;e<arguments.length;e++)i[e]=t[e];if(i.length>1)for(var n=0;n<i.length;n++)this.removeChild(i[n]);else{var r=i[0],s=this.children.indexOf(r);if(-1===s)return null;r.parent=null,r.transform._parentID=-1,o(this.children,s,1),this._boundsID++,this.onChildrenChange(s),r.emit("removed",this),this.emit("childRemoved",r,this,s)}return i[0]},e.prototype.removeChildAt=function(t){var i=this.getChildAt(t);return i.parent=null,i.transform._parentID=-1,o(this.children,t,1),this._boundsID++,this.onChildrenChange(t),i.emit("removed",this),this.emit("childRemoved",i,this,t),i},e.prototype.removeChildren=function(t,i){void 0===t&&(t=0),void 0===i&&(i=this.children.length);var e,n=t,r=i-n;if(r>0&&r<=i){e=this.children.splice(n,r);for(var s=0;s<e.length;++s)e[s].parent=null,e[s].transform&&(e[s].transform._parentID=-1);this._boundsID++,this.onChildrenChange(t);for(s=0;s<e.length;++s)e[s].emit("removed",this),this.emit("childRemoved",e[s],this,s);return e}if(0===r&&0===this.children.length)return[];throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},e.prototype.sortChildren=function(){for(var t=!1,i=0,e=this.children.length;i<e;++i){var n=this.children[i];n._lastSortedIndex=i,t||0===n.zIndex||(t=!0)}t&&this.children.length>1&&this.children.sort(m),this.sortDirty=!1},e.prototype.updateTransform=function(){this.sortableChildren&&this.sortDirty&&this.sortChildren(),this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha;for(var t=0,i=this.children.length;t<i;++t){var e=this.children[t];e.visible&&e.updateTransform()}},e.prototype.calculateBounds=function(){this._bounds.clear(),this._calculateBounds();for(var t=0;t<this.children.length;t++){var i=this.children[t];if(i.visible&&i.renderable)if(i.calculateBounds(),i._mask){var e=i._mask.isMaskData?i._mask.maskObject:i._mask;e?(e.calculateBounds(),this._bounds.addBoundsMask(i._bounds,e._bounds)):this._bounds.addBounds(i._bounds)}else i.filterArea?this._bounds.addBoundsArea(i._bounds,i.filterArea):this._bounds.addBounds(i._bounds)}this._bounds.updateID=this._boundsID},e.prototype.getLocalBounds=function(t,e){void 0===e&&(e=!1);var n=i.prototype.getLocalBounds.call(this,t);if(!e)for(var r=0,s=this.children.length;r<s;++r){var o=this.children[r];o.visible&&o.updateTransform()}return n},e.prototype._calculateBounds=function(){},e.prototype._renderWithCulling=function(t){var i=t.renderTexture.sourceFrame;if(i.width>0&&i.height>0){var n,r;if(this.cullArea?(n=this.cullArea,r=this.worldTransform):this._render!==e.prototype._render&&(n=this.getBounds(!0)),n&&i.intersects(n,r))this._render(t);else if(this.cullArea)return;for(var s=0,o=this.children.length;s<o;++s){var a=this.children[s],h=a.cullable;a.cullable=h||!this.cullArea,a.render(t),a.cullable=h}}},e.prototype.render=function(t){if(this.visible&&!(this.worldAlpha<=0)&&this.renderable)if(this._mask||this.filters&&this.filters.length)this.renderAdvanced(t);else if(this.cullable)this._renderWithCulling(t);else{this._render(t);for(var i=0,e=this.children.length;i<e;++i)this.children[i].render(t)}},e.prototype.renderAdvanced=function(t){var i=this.filters,e=this._mask;if(i){this._enabledFilters||(this._enabledFilters=[]),this._enabledFilters.length=0;for(var n=0;n<i.length;n++)i[n].enabled&&this._enabledFilters.push(i[n])}var r=i&&this._enabledFilters&&this._enabledFilters.length||e&&(!e.isMaskData||e.enabled&&(e.autoDetect||e.type!==a.NONE));if(r&&t.batch.flush(),i&&this._enabledFilters&&this._enabledFilters.length&&t.filter.push(this,this._enabledFilters),e&&t.mask.push(this,this._mask),this.cullable)this._renderWithCulling(t);else{this._render(t);n=0;for(var s=this.children.length;n<s;++n)this.children[n].render(t)}r&&t.batch.flush(),e&&t.mask.pop(this),i&&this._enabledFilters&&this._enabledFilters.length&&t.filter.pop()},e.prototype._render=function(t){},e.prototype.destroy=function(t){i.prototype.destroy.call(this),this.sortDirty=!1;var e="boolean"==typeof t?t:t&&t.children,n=this.removeChildren(0,this.children.length);if(e)for(var r=0;r<n.length;++r)n[r].destroy(t)},Object.defineProperty(e.prototype,"width",{get:function(){return this.scale.x*this.getLocalBounds().width},set:function(t){var i=this.getLocalBounds().width;this.scale.x=0!==i?t/i:1,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this.scale.y*this.getLocalBounds().height},set:function(t){var i=this.getLocalBounds().height;this.scale.y=0!==i?t/i:1,this._height=t},enumerable:!1,configurable:!0}),e}(u);c.prototype.containerUpdateTransform=c.prototype.updateTransform;export{h as Bounds,c as Container,u as DisplayObject,p as TemporaryDisplayObject};
//# sourceMappingURL=display.min.mjs.map
