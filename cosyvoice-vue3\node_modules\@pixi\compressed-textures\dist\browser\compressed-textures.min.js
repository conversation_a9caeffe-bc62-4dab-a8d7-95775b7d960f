/*!
 * @pixi/compressed-textures - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/compressed-textures is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_compressed_textures=function(_,R,e,T,t,r){"use strict";var A,E;_.INTERNAL_FORMATS=void 0,(E=_.INTERNAL_FORMATS||(_.INTERNAL_FORMATS={}))[E.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",E[E.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",E[E.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",E[E.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",E[E.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917]="COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT",E[E.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918]="COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT",E[E.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919]="COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT",E[E.COMPRESSED_SRGB_S3TC_DXT1_EXT=35916]="COMPRESSED_SRGB_S3TC_DXT1_EXT",E[E.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",E[E.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",E[E.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",E[E.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",E[E.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",E[E.COMPRESSED_RGBA8_ETC2_EAC=37496]="COMPRESSED_RGBA8_ETC2_EAC",E[E.COMPRESSED_SRGB8_ETC2=37493]="COMPRESSED_SRGB8_ETC2",E[E.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC",E[E.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",E[E.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",E[E.COMPRESSED_RGB_PVRTC_4BPPV1_IMG=35840]="COMPRESSED_RGB_PVRTC_4BPPV1_IMG",E[E.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=35842]="COMPRESSED_RGBA_PVRTC_4BPPV1_IMG",E[E.COMPRESSED_RGB_PVRTC_2BPPV1_IMG=35841]="COMPRESSED_RGB_PVRTC_2BPPV1_IMG",E[E.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=35843]="COMPRESSED_RGBA_PVRTC_2BPPV1_IMG",E[E.COMPRESSED_RGB_ETC1_WEBGL=36196]="COMPRESSED_RGB_ETC1_WEBGL",E[E.COMPRESSED_RGB_ATC_WEBGL=35986]="COMPRESSED_RGB_ATC_WEBGL",E[E.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=35986]="COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL",E[E.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=34798]="COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL",E[E.COMPRESSED_RGBA_ASTC_4x4_KHR=37808]="COMPRESSED_RGBA_ASTC_4x4_KHR";var O=((A={})[_.INTERNAL_FORMATS.COMPRESSED_RGB_S3TC_DXT1_EXT]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT]=1,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT]=1,A[_.INTERNAL_FORMATS.COMPRESSED_SRGB_S3TC_DXT1_EXT]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT]=1,A[_.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT]=1,A[_.INTERNAL_FORMATS.COMPRESSED_R11_EAC]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_SIGNED_R11_EAC]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RG11_EAC]=1,A[_.INTERNAL_FORMATS.COMPRESSED_SIGNED_RG11_EAC]=1,A[_.INTERNAL_FORMATS.COMPRESSED_RGB8_ETC2]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA8_ETC2_EAC]=1,A[_.INTERNAL_FORMATS.COMPRESSED_SRGB8_ETC2]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC]=1,A[_.INTERNAL_FORMATS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGB_PVRTC_2BPPV1_IMG]=.25,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG]=.25,A[_.INTERNAL_FORMATS.COMPRESSED_RGB_ETC1_WEBGL]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGB_ATC_WEBGL]=.5,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL]=1,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL]=1,A[_.INTERNAL_FORMATS.COMPRESSED_RGBA_ASTC_4x4_KHR]=1,A),S=function(_,R){return S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(_,R){_.__proto__=R}||function(_,R){for(var e in R)R.hasOwnProperty(e)&&(_[e]=R[e])},S(_,R)};function M(_,R){function e(){this.constructor=_}S(_,R),_.prototype=null===R?Object.create(R):(e.prototype=R.prototype,new e)}function G(_,R,e,T){return new(e||(e=Promise))((function(t,r){function A(_){try{O(T.next(_))}catch(_){r(_)}}function E(_){try{O(T.throw(_))}catch(_){r(_)}}function O(_){var R;_.done?t(_.value):(R=_.value,R instanceof e?R:new e((function(_){_(R)}))).then(A,E)}O((T=T.apply(_,R||[])).next())}))}function D(_,R){var e,T,t,r,A={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return r={next:E(0),throw:E(1),return:E(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function E(r){return function(E){return function(r){if(e)throw new TypeError("Generator is already executing.");for(;A;)try{if(e=1,T&&(t=2&r[0]?T.return:r[0]?T.throw||((t=T.return)&&t.call(T),0):T.next)&&!(t=t.call(T,r[1])).done)return t;switch(T=0,t&&(r=[2&r[0],t.value]),r[0]){case 0:case 1:t=r;break;case 4:return A.label++,{value:r[1],done:!1};case 5:A.label++,T=r[1],r=[0];continue;case 7:r=A.ops.pop(),A.trys.pop();continue;default:if(!(t=A.trys,(t=t.length>0&&t[t.length-1])||6!==r[0]&&2!==r[0])){A=0;continue}if(3===r[0]&&(!t||r[1]>t[0]&&r[1]<t[3])){A.label=r[1];break}if(6===r[0]&&A.label<t[1]){A.label=t[1],t=r;break}if(t&&A.label<t[2]){A.label=t[2],A.ops.push(r);break}t[2]&&A.ops.pop(),A.trys.pop();continue}r=R.call(_,A)}catch(_){r=[6,_],T=0}finally{e=t=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,E])}}}var n,I,o=function(_){function e(e,T){void 0===T&&(T={width:1,height:1,autoLoad:!0});var t,r,A=this;return"string"==typeof e?(t=e,r=new Uint8Array):(t=null,r=e),(A=_.call(this,r,T)||this).origin=t,A.buffer=r?new R.ViewableBuffer(r):null,A.origin&&!1!==T.autoLoad&&A.load(),r&&r.length&&(A.loaded=!0,A.onBlobLoaded(A.buffer.rawBinaryData)),A}return M(e,_),e.prototype.onBlobLoaded=function(_){},e.prototype.load=function(){return G(this,void 0,Promise,(function(){var _;return D(this,(function(e){switch(e.label){case 0:return[4,fetch(this.origin)];case 1:return[4,e.sent().blob()];case 2:return[4,e.sent().arrayBuffer()];case 3:return _=e.sent(),this.data=new Uint32Array(_),this.buffer=new R.ViewableBuffer(_),this.loaded=!0,this.onBlobLoaded(_),this.update(),[2,this]}}))}))},e}(R.BufferResource),a=function(_){function R(e,T){var t=_.call(this,e,T)||this;return t.format=T.format,t.levels=T.levels||1,t._width=T.width,t._height=T.height,t._extension=R._formatToExtension(t.format),(T.levelBuffers||t.buffer)&&(t._levelBuffers=T.levelBuffers||R._createLevelBuffers(e instanceof Uint8Array?e:t.buffer.uint8View,t.format,t.levels,4,4,t.width,t.height)),t}return M(R,_),R.prototype.upload=function(_,R,e){var T=_.gl;if(!_.context.extensions[this._extension])throw new Error(this._extension+" textures are not supported on the current machine");if(!this._levelBuffers)return!1;for(var t=0,r=this.levels;t<r;t++){var A=this._levelBuffers[t],E=A.levelID,O=A.levelWidth,S=A.levelHeight,M=A.levelBuffer;T.compressedTexImage2D(T.TEXTURE_2D,E,this.format,O,S,0,M)}return!0},R.prototype.onBlobLoaded=function(){this._levelBuffers=R._createLevelBuffers(this.buffer.uint8View,this.format,this.levels,4,4,this.width,this.height)},R._formatToExtension=function(_){if(_>=33776&&_<=33779)return"s3tc";if(_>=37488&&_<=37497)return"etc";if(_>=35840&&_<=35843)return"pvrtc";if(_>=36196)return"etc1";if(_>=35986&&_<=34798)return"atc";throw new Error("Invalid (compressed) texture format given!")},R._createLevelBuffers=function(_,R,e,T,t,r,A){for(var E=new Array(e),S=_.byteOffset,M=r,G=A,D=M+T-1&~(T-1),n=G+t-1&~(t-1),I=D*n*O[R],o=0;o<e;o++)E[o]={levelID:o,levelWidth:e>1?M:D,levelHeight:e>1?G:n,levelBuffer:new Uint8Array(_.buffer,S,I)},S+=I,I=(D=(M=M>>1||1)+T-1&~(T-1))*(n=(G=G>>1||1)+t-1&~(t-1))*O[R];return E},R}(o),X=function(){function _(){}return _.use=function(R,t){var r=R.data;if(R.type===e.LoaderResource.TYPE.JSON&&r&&r.cacheID&&r.textures){for(var A=r.textures,E=void 0,O=void 0,S=0,M=A.length;S<M;S++){var G=A[S],D=G.src,n=G.format;if(n||(O=D),_.textureFormats[n]){E=D;break}}if(!(E=E||O))return void t(new Error("Cannot load compressed-textures in "+R.url+", make sure you provide a fallback"));if(E===R.url)return void t(new Error("URL of compressed texture cannot be the same as the manifest's URL"));var I={crossOrigin:R.crossOrigin,metadata:R.metadata.imageMetadata,parentResource:R},o=T.url.resolve(R.url.replace(this.baseUrl,""),E),a=r.cacheID;this.add(a,o,I,(function(_){if(_.error)t(_.error);else{var e=_.texture,T=void 0===e?null:e,r=_.textures,A=void 0===r?{}:r;Object.assign(R,{texture:T,textures:A}),t()}}))}else t()},Object.defineProperty(_,"textureExtensions",{get:function(){if(!_._textureExtensions){var R=t.settings.ADAPTER.createCanvas().getContext("webgl");if(!R)return{};var e={s3tc:R.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:R.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:R.getExtension("WEBGL_compressed_texture_etc"),etc1:R.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:R.getExtension("WEBGL_compressed_texture_pvrtc")||R.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:R.getExtension("WEBGL_compressed_texture_atc"),astc:R.getExtension("WEBGL_compressed_texture_astc")};_._textureExtensions=e}return _._textureExtensions},enumerable:!1,configurable:!0}),Object.defineProperty(_,"textureFormats",{get:function(){if(!_._textureFormats){var R=_.textureExtensions;for(var e in _._textureFormats={},R){var T=R[e];T&&Object.assign(_._textureFormats,Object.getPrototypeOf(T))}}return _._textureFormats},enumerable:!1,configurable:!0}),_.extension=R.ExtensionType.Loader,_}();function F(_,e,T){var t={textures:{},texture:null};return e?(e.map((function(_){return new R.Texture(new R.BaseTexture(_,Object.assign({mipmap:r.MIPMAP_MODES.OFF,alphaMode:r.ALPHA_MODES.NO_PREMULTIPLIED_ALPHA},T)))})).forEach((function(e,T){var r=e.baseTexture,A=_+"-"+(T+1);R.BaseTexture.addToCache(r,A),R.Texture.addToCache(e,A),0===T&&(R.BaseTexture.addToCache(r,_),R.Texture.addToCache(e,_),t.texture=e),t.textures[A]=e})),t):t}var i,B,P=124,u=3,s=4,N=7,C=19,f=2,L=0,l=1,c=2,U=3;!function(_){_[_.DXGI_FORMAT_UNKNOWN=0]="DXGI_FORMAT_UNKNOWN",_[_.DXGI_FORMAT_R32G32B32A32_TYPELESS=1]="DXGI_FORMAT_R32G32B32A32_TYPELESS",_[_.DXGI_FORMAT_R32G32B32A32_FLOAT=2]="DXGI_FORMAT_R32G32B32A32_FLOAT",_[_.DXGI_FORMAT_R32G32B32A32_UINT=3]="DXGI_FORMAT_R32G32B32A32_UINT",_[_.DXGI_FORMAT_R32G32B32A32_SINT=4]="DXGI_FORMAT_R32G32B32A32_SINT",_[_.DXGI_FORMAT_R32G32B32_TYPELESS=5]="DXGI_FORMAT_R32G32B32_TYPELESS",_[_.DXGI_FORMAT_R32G32B32_FLOAT=6]="DXGI_FORMAT_R32G32B32_FLOAT",_[_.DXGI_FORMAT_R32G32B32_UINT=7]="DXGI_FORMAT_R32G32B32_UINT",_[_.DXGI_FORMAT_R32G32B32_SINT=8]="DXGI_FORMAT_R32G32B32_SINT",_[_.DXGI_FORMAT_R16G16B16A16_TYPELESS=9]="DXGI_FORMAT_R16G16B16A16_TYPELESS",_[_.DXGI_FORMAT_R16G16B16A16_FLOAT=10]="DXGI_FORMAT_R16G16B16A16_FLOAT",_[_.DXGI_FORMAT_R16G16B16A16_UNORM=11]="DXGI_FORMAT_R16G16B16A16_UNORM",_[_.DXGI_FORMAT_R16G16B16A16_UINT=12]="DXGI_FORMAT_R16G16B16A16_UINT",_[_.DXGI_FORMAT_R16G16B16A16_SNORM=13]="DXGI_FORMAT_R16G16B16A16_SNORM",_[_.DXGI_FORMAT_R16G16B16A16_SINT=14]="DXGI_FORMAT_R16G16B16A16_SINT",_[_.DXGI_FORMAT_R32G32_TYPELESS=15]="DXGI_FORMAT_R32G32_TYPELESS",_[_.DXGI_FORMAT_R32G32_FLOAT=16]="DXGI_FORMAT_R32G32_FLOAT",_[_.DXGI_FORMAT_R32G32_UINT=17]="DXGI_FORMAT_R32G32_UINT",_[_.DXGI_FORMAT_R32G32_SINT=18]="DXGI_FORMAT_R32G32_SINT",_[_.DXGI_FORMAT_R32G8X24_TYPELESS=19]="DXGI_FORMAT_R32G8X24_TYPELESS",_[_.DXGI_FORMAT_D32_FLOAT_S8X24_UINT=20]="DXGI_FORMAT_D32_FLOAT_S8X24_UINT",_[_.DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS=21]="DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS",_[_.DXGI_FORMAT_X32_TYPELESS_G8X24_UINT=22]="DXGI_FORMAT_X32_TYPELESS_G8X24_UINT",_[_.DXGI_FORMAT_R10G10B10A2_TYPELESS=23]="DXGI_FORMAT_R10G10B10A2_TYPELESS",_[_.DXGI_FORMAT_R10G10B10A2_UNORM=24]="DXGI_FORMAT_R10G10B10A2_UNORM",_[_.DXGI_FORMAT_R10G10B10A2_UINT=25]="DXGI_FORMAT_R10G10B10A2_UINT",_[_.DXGI_FORMAT_R11G11B10_FLOAT=26]="DXGI_FORMAT_R11G11B10_FLOAT",_[_.DXGI_FORMAT_R8G8B8A8_TYPELESS=27]="DXGI_FORMAT_R8G8B8A8_TYPELESS",_[_.DXGI_FORMAT_R8G8B8A8_UNORM=28]="DXGI_FORMAT_R8G8B8A8_UNORM",_[_.DXGI_FORMAT_R8G8B8A8_UNORM_SRGB=29]="DXGI_FORMAT_R8G8B8A8_UNORM_SRGB",_[_.DXGI_FORMAT_R8G8B8A8_UINT=30]="DXGI_FORMAT_R8G8B8A8_UINT",_[_.DXGI_FORMAT_R8G8B8A8_SNORM=31]="DXGI_FORMAT_R8G8B8A8_SNORM",_[_.DXGI_FORMAT_R8G8B8A8_SINT=32]="DXGI_FORMAT_R8G8B8A8_SINT",_[_.DXGI_FORMAT_R16G16_TYPELESS=33]="DXGI_FORMAT_R16G16_TYPELESS",_[_.DXGI_FORMAT_R16G16_FLOAT=34]="DXGI_FORMAT_R16G16_FLOAT",_[_.DXGI_FORMAT_R16G16_UNORM=35]="DXGI_FORMAT_R16G16_UNORM",_[_.DXGI_FORMAT_R16G16_UINT=36]="DXGI_FORMAT_R16G16_UINT",_[_.DXGI_FORMAT_R16G16_SNORM=37]="DXGI_FORMAT_R16G16_SNORM",_[_.DXGI_FORMAT_R16G16_SINT=38]="DXGI_FORMAT_R16G16_SINT",_[_.DXGI_FORMAT_R32_TYPELESS=39]="DXGI_FORMAT_R32_TYPELESS",_[_.DXGI_FORMAT_D32_FLOAT=40]="DXGI_FORMAT_D32_FLOAT",_[_.DXGI_FORMAT_R32_FLOAT=41]="DXGI_FORMAT_R32_FLOAT",_[_.DXGI_FORMAT_R32_UINT=42]="DXGI_FORMAT_R32_UINT",_[_.DXGI_FORMAT_R32_SINT=43]="DXGI_FORMAT_R32_SINT",_[_.DXGI_FORMAT_R24G8_TYPELESS=44]="DXGI_FORMAT_R24G8_TYPELESS",_[_.DXGI_FORMAT_D24_UNORM_S8_UINT=45]="DXGI_FORMAT_D24_UNORM_S8_UINT",_[_.DXGI_FORMAT_R24_UNORM_X8_TYPELESS=46]="DXGI_FORMAT_R24_UNORM_X8_TYPELESS",_[_.DXGI_FORMAT_X24_TYPELESS_G8_UINT=47]="DXGI_FORMAT_X24_TYPELESS_G8_UINT",_[_.DXGI_FORMAT_R8G8_TYPELESS=48]="DXGI_FORMAT_R8G8_TYPELESS",_[_.DXGI_FORMAT_R8G8_UNORM=49]="DXGI_FORMAT_R8G8_UNORM",_[_.DXGI_FORMAT_R8G8_UINT=50]="DXGI_FORMAT_R8G8_UINT",_[_.DXGI_FORMAT_R8G8_SNORM=51]="DXGI_FORMAT_R8G8_SNORM",_[_.DXGI_FORMAT_R8G8_SINT=52]="DXGI_FORMAT_R8G8_SINT",_[_.DXGI_FORMAT_R16_TYPELESS=53]="DXGI_FORMAT_R16_TYPELESS",_[_.DXGI_FORMAT_R16_FLOAT=54]="DXGI_FORMAT_R16_FLOAT",_[_.DXGI_FORMAT_D16_UNORM=55]="DXGI_FORMAT_D16_UNORM",_[_.DXGI_FORMAT_R16_UNORM=56]="DXGI_FORMAT_R16_UNORM",_[_.DXGI_FORMAT_R16_UINT=57]="DXGI_FORMAT_R16_UINT",_[_.DXGI_FORMAT_R16_SNORM=58]="DXGI_FORMAT_R16_SNORM",_[_.DXGI_FORMAT_R16_SINT=59]="DXGI_FORMAT_R16_SINT",_[_.DXGI_FORMAT_R8_TYPELESS=60]="DXGI_FORMAT_R8_TYPELESS",_[_.DXGI_FORMAT_R8_UNORM=61]="DXGI_FORMAT_R8_UNORM",_[_.DXGI_FORMAT_R8_UINT=62]="DXGI_FORMAT_R8_UINT",_[_.DXGI_FORMAT_R8_SNORM=63]="DXGI_FORMAT_R8_SNORM",_[_.DXGI_FORMAT_R8_SINT=64]="DXGI_FORMAT_R8_SINT",_[_.DXGI_FORMAT_A8_UNORM=65]="DXGI_FORMAT_A8_UNORM",_[_.DXGI_FORMAT_R1_UNORM=66]="DXGI_FORMAT_R1_UNORM",_[_.DXGI_FORMAT_R9G9B9E5_SHAREDEXP=67]="DXGI_FORMAT_R9G9B9E5_SHAREDEXP",_[_.DXGI_FORMAT_R8G8_B8G8_UNORM=68]="DXGI_FORMAT_R8G8_B8G8_UNORM",_[_.DXGI_FORMAT_G8R8_G8B8_UNORM=69]="DXGI_FORMAT_G8R8_G8B8_UNORM",_[_.DXGI_FORMAT_BC1_TYPELESS=70]="DXGI_FORMAT_BC1_TYPELESS",_[_.DXGI_FORMAT_BC1_UNORM=71]="DXGI_FORMAT_BC1_UNORM",_[_.DXGI_FORMAT_BC1_UNORM_SRGB=72]="DXGI_FORMAT_BC1_UNORM_SRGB",_[_.DXGI_FORMAT_BC2_TYPELESS=73]="DXGI_FORMAT_BC2_TYPELESS",_[_.DXGI_FORMAT_BC2_UNORM=74]="DXGI_FORMAT_BC2_UNORM",_[_.DXGI_FORMAT_BC2_UNORM_SRGB=75]="DXGI_FORMAT_BC2_UNORM_SRGB",_[_.DXGI_FORMAT_BC3_TYPELESS=76]="DXGI_FORMAT_BC3_TYPELESS",_[_.DXGI_FORMAT_BC3_UNORM=77]="DXGI_FORMAT_BC3_UNORM",_[_.DXGI_FORMAT_BC3_UNORM_SRGB=78]="DXGI_FORMAT_BC3_UNORM_SRGB",_[_.DXGI_FORMAT_BC4_TYPELESS=79]="DXGI_FORMAT_BC4_TYPELESS",_[_.DXGI_FORMAT_BC4_UNORM=80]="DXGI_FORMAT_BC4_UNORM",_[_.DXGI_FORMAT_BC4_SNORM=81]="DXGI_FORMAT_BC4_SNORM",_[_.DXGI_FORMAT_BC5_TYPELESS=82]="DXGI_FORMAT_BC5_TYPELESS",_[_.DXGI_FORMAT_BC5_UNORM=83]="DXGI_FORMAT_BC5_UNORM",_[_.DXGI_FORMAT_BC5_SNORM=84]="DXGI_FORMAT_BC5_SNORM",_[_.DXGI_FORMAT_B5G6R5_UNORM=85]="DXGI_FORMAT_B5G6R5_UNORM",_[_.DXGI_FORMAT_B5G5R5A1_UNORM=86]="DXGI_FORMAT_B5G5R5A1_UNORM",_[_.DXGI_FORMAT_B8G8R8A8_UNORM=87]="DXGI_FORMAT_B8G8R8A8_UNORM",_[_.DXGI_FORMAT_B8G8R8X8_UNORM=88]="DXGI_FORMAT_B8G8R8X8_UNORM",_[_.DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM=89]="DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM",_[_.DXGI_FORMAT_B8G8R8A8_TYPELESS=90]="DXGI_FORMAT_B8G8R8A8_TYPELESS",_[_.DXGI_FORMAT_B8G8R8A8_UNORM_SRGB=91]="DXGI_FORMAT_B8G8R8A8_UNORM_SRGB",_[_.DXGI_FORMAT_B8G8R8X8_TYPELESS=92]="DXGI_FORMAT_B8G8R8X8_TYPELESS",_[_.DXGI_FORMAT_B8G8R8X8_UNORM_SRGB=93]="DXGI_FORMAT_B8G8R8X8_UNORM_SRGB",_[_.DXGI_FORMAT_BC6H_TYPELESS=94]="DXGI_FORMAT_BC6H_TYPELESS",_[_.DXGI_FORMAT_BC6H_UF16=95]="DXGI_FORMAT_BC6H_UF16",_[_.DXGI_FORMAT_BC6H_SF16=96]="DXGI_FORMAT_BC6H_SF16",_[_.DXGI_FORMAT_BC7_TYPELESS=97]="DXGI_FORMAT_BC7_TYPELESS",_[_.DXGI_FORMAT_BC7_UNORM=98]="DXGI_FORMAT_BC7_UNORM",_[_.DXGI_FORMAT_BC7_UNORM_SRGB=99]="DXGI_FORMAT_BC7_UNORM_SRGB",_[_.DXGI_FORMAT_AYUV=100]="DXGI_FORMAT_AYUV",_[_.DXGI_FORMAT_Y410=101]="DXGI_FORMAT_Y410",_[_.DXGI_FORMAT_Y416=102]="DXGI_FORMAT_Y416",_[_.DXGI_FORMAT_NV12=103]="DXGI_FORMAT_NV12",_[_.DXGI_FORMAT_P010=104]="DXGI_FORMAT_P010",_[_.DXGI_FORMAT_P016=105]="DXGI_FORMAT_P016",_[_.DXGI_FORMAT_420_OPAQUE=106]="DXGI_FORMAT_420_OPAQUE",_[_.DXGI_FORMAT_YUY2=107]="DXGI_FORMAT_YUY2",_[_.DXGI_FORMAT_Y210=108]="DXGI_FORMAT_Y210",_[_.DXGI_FORMAT_Y216=109]="DXGI_FORMAT_Y216",_[_.DXGI_FORMAT_NV11=110]="DXGI_FORMAT_NV11",_[_.DXGI_FORMAT_AI44=111]="DXGI_FORMAT_AI44",_[_.DXGI_FORMAT_IA44=112]="DXGI_FORMAT_IA44",_[_.DXGI_FORMAT_P8=113]="DXGI_FORMAT_P8",_[_.DXGI_FORMAT_A8P8=114]="DXGI_FORMAT_A8P8",_[_.DXGI_FORMAT_B4G4R4A4_UNORM=115]="DXGI_FORMAT_B4G4R4A4_UNORM",_[_.DXGI_FORMAT_P208=116]="DXGI_FORMAT_P208",_[_.DXGI_FORMAT_V208=117]="DXGI_FORMAT_V208",_[_.DXGI_FORMAT_V408=118]="DXGI_FORMAT_V408",_[_.DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE=119]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE",_[_.DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE=120]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE",_[_.DXGI_FORMAT_FORCE_UINT=121]="DXGI_FORMAT_FORCE_UINT"}(i||(i={})),function(_){_[_.DDS_DIMENSION_TEXTURE1D=2]="DDS_DIMENSION_TEXTURE1D",_[_.DDS_DIMENSION_TEXTURE2D=3]="DDS_DIMENSION_TEXTURE2D",_[_.DDS_DIMENSION_TEXTURE3D=6]="DDS_DIMENSION_TEXTURE3D"}(B||(B={}));var d,h,v,p=((n={})[827611204]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,n[861165636]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,n[894720068]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,n),x=((I={})[i.DXGI_FORMAT_BC1_TYPELESS]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,I[i.DXGI_FORMAT_BC1_UNORM]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT1_EXT,I[i.DXGI_FORMAT_BC2_TYPELESS]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,I[i.DXGI_FORMAT_BC2_UNORM]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT3_EXT,I[i.DXGI_FORMAT_BC3_TYPELESS]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,I[i.DXGI_FORMAT_BC3_UNORM]=_.INTERNAL_FORMATS.COMPRESSED_RGBA_S3TC_DXT5_EXT,I[i.DXGI_FORMAT_BC1_UNORM_SRGB]=_.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,I[i.DXGI_FORMAT_BC2_UNORM_SRGB]=_.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,I[i.DXGI_FORMAT_BC3_UNORM_SRGB]=_.INTERNAL_FORMATS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,I);function w(_){var R=new Uint32Array(_);if(542327876!==R[0])throw new Error("Invalid DDS file magic word");var e=new Uint32Array(_,0,P/Uint32Array.BYTES_PER_ELEMENT),T=e[u],t=e[s],r=e[N],A=new Uint32Array(_,C*Uint32Array.BYTES_PER_ELEMENT,32/Uint32Array.BYTES_PER_ELEMENT),E=A[1];if(4&E){var S=A[f];if(808540228!==S){var M=p[S],G=new Uint8Array(_,128);return[new a(G,{format:M,width:t,height:T,levels:r})]}var D=new Uint32Array(R.buffer,128,20/Uint32Array.BYTES_PER_ELEMENT),n=D[L],I=D[l],o=D[c],X=D[U],F=x[n];if(void 0===F)throw new Error("DDSParser cannot parse texture data with DXGI format "+n);if(4===o)throw new Error("DDSParser does not support cubemap textures");if(I===B.DDS_DIMENSION_TEXTURE3D)throw new Error("DDSParser does not supported 3D texture data");var i=new Array;if(1===X)i.push(new Uint8Array(_,148));else{for(var d=O[F],h=0,v=t,w=T,Y=0;Y<r;Y++){h+=Math.max(1,v+3&-4)*Math.max(1,w+3&-4)*d,v>>>=1,w>>>=1}var y=148;for(Y=0;Y<X;Y++)i.push(new Uint8Array(_,y,h)),y+=h}return i.map((function(_){return new a(_,{format:F,width:t,height:T,levels:r})}))}if(64&E)throw new Error("DDSParser does not support uncompressed texture data.");if(512&E)throw new Error("DDSParser does not supported YUV uncompressed texture data.");if(131072&E)throw new Error("DDSParser does not support single-channel (lumninance) texture data!");if(2&E)throw new Error("DDSParser does not support single-channel (alpha) texture data!");throw new Error("DDSParser failed to load a texture file due to an unknown reason!")}var Y=[171,75,84,88,32,49,49,187,13,10,26,10],y=12,b=16,m=24,g=28,H=36,V=40,W=44,k=48,K=52,j=56,Q=60,z=((d={})[r.TYPES.UNSIGNED_BYTE]=1,d[r.TYPES.UNSIGNED_SHORT]=2,d[r.TYPES.INT]=4,d[r.TYPES.UNSIGNED_INT]=4,d[r.TYPES.FLOAT]=4,d[r.TYPES.HALF_FLOAT]=8,d),J=((h={})[r.FORMATS.RGBA]=4,h[r.FORMATS.RGB]=3,h[r.FORMATS.RG]=2,h[r.FORMATS.RED]=1,h[r.FORMATS.LUMINANCE]=1,h[r.FORMATS.LUMINANCE_ALPHA]=2,h[r.FORMATS.ALPHA]=1,h),q=((v={})[r.TYPES.UNSIGNED_SHORT_4_4_4_4]=2,v[r.TYPES.UNSIGNED_SHORT_5_5_5_1]=2,v[r.TYPES.UNSIGNED_SHORT_5_6_5]=2,v);function Z(_,e,T){void 0===T&&(T=!1);var t=new DataView(e);if(!function(_,R){for(var e=0;e<Y.length;e++)if(R.getUint8(e)!==Y[e])return!1;return!0}(0,t))return null;var A=67305985===t.getUint32(y,!0),E=t.getUint32(b,A),S=t.getUint32(m,A),M=t.getUint32(g,A),G=t.getUint32(H,A),D=t.getUint32(V,A)||1,n=t.getUint32(W,A)||1,I=t.getUint32(k,A)||1,o=t.getUint32(K,A),X=t.getUint32(j,A),F=t.getUint32(Q,A);if(0===D||1!==n)throw new Error("Only 2D textures are supported");if(1!==o)throw new Error("CubeTextures are not supported by KTXLoader yet!");if(1!==I)throw new Error("WebGL does not support array textures");var i,B=G+3&-4,P=D+3&-4,u=new Array(I),s=G*D;if(0===E&&(s=B*P),void 0===(i=0!==E?z[E]?z[E]*J[S]:q[E]:O[M]))throw new Error("Unable to resolve the pixel format stored in the *.ktx file!");for(var N=T?function(_,R,e){var T=new Map,t=0;for(;t<R;){var r=_.getUint32(64+t,e),A=64+t+4,E=3-(r+3)%4;if(0===r||r>R-t){console.error("KTXLoader: keyAndValueByteSize out of bounds");break}for(var O=0;O<r&&0!==_.getUint8(A+O);O++);if(-1===O){console.error("KTXLoader: Failed to find null byte terminating kvData key");break}var S=(new TextDecoder).decode(new Uint8Array(_.buffer,A,O)),M=new DataView(_.buffer,A+O+1,r-O-1);T.set(S,M),t+=4+r+E}return T}(t,F,A):null,C=s*i,f=G,L=D,l=B,c=P,U=64+F,d=0;d<X;d++){for(var h=t.getUint32(U,A),v=U+4,p=0;p<I;p++){var x=u[p];x||(x=u[p]=new Array(X)),x[d]={levelID:d,levelWidth:X>1||0!==E?f:l,levelHeight:X>1||0!==E?L:c,levelBuffer:new Uint8Array(e,v,C)},v+=C}U=(U+=h+4)%4!=0?U+4-U%4:U,C=(l=(f=f>>1||1)****&-4)*(c=(L=L>>1||1)****&-4)*i}return 0!==E?{uncompressed:u.map((function(_){var e=_[0].levelBuffer,T=!1;return E===r.TYPES.FLOAT?e=new Float32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4):E===r.TYPES.UNSIGNED_INT?(T=!0,e=new Uint32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4)):E===r.TYPES.INT&&(T=!0,e=new Int32Array(_[0].levelBuffer.buffer,_[0].levelBuffer.byteOffset,_[0].levelBuffer.byteLength/4)),{resource:new R.BufferResource(e,{width:_[0].levelWidth,height:_[0].levelHeight}),type:E,format:T?$(S):S}})),kvData:N}:{compressed:u.map((function(_){return new a(null,{format:M,width:G,height:D,levels:X,levelBuffers:_})})),kvData:N}}function $(_){switch(_){case r.FORMATS.RGBA:return r.FORMATS.RGBA_INTEGER;case r.FORMATS.RGB:return r.FORMATS.RGB_INTEGER;case r.FORMATS.RG:return r.FORMATS.RG_INTEGER;case r.FORMATS.RED:return r.FORMATS.RED_INTEGER;default:return _}}e.LoaderResource.setExtensionXhrType("dds",e.LoaderResource.XHR_RESPONSE_TYPE.BUFFER);var __=function(){function _(){}return _.use=function(_,R){if("dds"===_.extension&&_.data)try{Object.assign(_,F(_.name||_.url,w(_.data),_.metadata))}catch(_){return void R(_)}R()},_.extension=R.ExtensionType.Loader,_}();e.LoaderResource.setExtensionXhrType("ktx",e.LoaderResource.XHR_RESPONSE_TYPE.BUFFER);var R_=function(){function _(){}return _.use=function(_,e){if("ktx"===_.extension&&_.data)try{var T=_.name||_.url,t=Z(0,_.data,this.loadKeyValueData),A=t.compressed,E=t.uncompressed,O=t.kvData;if(A){var S=F(T,A,_.metadata);if(O&&S.textures)for(var M in S.textures)S.textures[M].baseTexture.ktxKeyValueData=O;Object.assign(_,S)}else if(E){var G={};E.forEach((function(_,e){var t=new R.Texture(new R.BaseTexture(_.resource,{mipmap:r.MIPMAP_MODES.OFF,alphaMode:r.ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,type:_.type,format:_.format})),A=T+"-"+(e+1);O&&(t.baseTexture.ktxKeyValueData=O),R.BaseTexture.addToCache(t.baseTexture,A),R.Texture.addToCache(t,A),0===e&&(G[T]=t,R.BaseTexture.addToCache(t.baseTexture,T),R.Texture.addToCache(t,T)),G[A]=t})),Object.assign(_,{textures:G})}}catch(_){return void e(_)}e()},_.extension=R.ExtensionType.Loader,_.loadKeyValueData=!1,_}();return _.BlobResource=o,_.CompressedTextureLoader=X,_.CompressedTextureResource=a,_.DDSLoader=__,_.FORMATS_TO_COMPONENTS=J,_.INTERNAL_FORMAT_TO_BYTES_PER_PIXEL=O,_.KTXLoader=R_,_.TYPES_TO_BYTES_PER_COMPONENT=z,_.TYPES_TO_BYTES_PER_PIXEL=q,_.parseDDS=w,_.parseKTX=Z,Object.defineProperty(_,"__esModule",{value:!0}),_}({},PIXI,PIXI,PIXI.utils,PIXI,PIXI);Object.assign(this.PIXI,_pixi_compressed_textures);
//# sourceMappingURL=compressed-textures.min.js.map
