{"version": 3, "file": "mesh.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/MeshBatchUvs.ts", "../../src/Mesh.ts", "../../src/MeshMaterial.ts", "../../src/MeshGeometry.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import type { TextureMatrix, Buffer } from '@pixi/core';\n\n/**\n * Class controls cache for UV mapping from Texture normal space to BaseTexture normal space.\n * @memberof PIXI\n */\nexport class MeshBatchUvs\n{\n    /** UV Buffer data. */\n    public readonly data: Float32Array;\n\n    /** Buffer with normalized UV's. */\n    public uvBuffer: Buffer;\n\n    /** Material UV matrix. */\n    public uvMatrix: TextureMatrix;\n\n    private _bufferUpdateId: number;\n    private _textureUpdateId: number;\n\n    // Internal-only properties\n    _updateID: number;\n\n    /**\n     * @param uvBuffer - Buffer with normalized uv's\n     * @param uvMatrix - Material UV matrix\n     */\n    constructor(uvBuffer: Buffer, uvMatrix: TextureMatrix)\n    {\n        this.uvBuffer = uvBuffer;\n        this.uvMatrix = uvMatrix;\n        this.data = null;\n\n        this._bufferUpdateId = -1;\n        this._textureUpdateId = -1;\n        this._updateID = 0;\n    }\n\n    /**\n     * Updates\n     * @param forceUpdate - force the update\n     */\n    public update(forceUpdate?: boolean): void\n    {\n        if (!forceUpdate\n            && this._bufferUpdateId === this.uvBuffer._updateID\n            && this._textureUpdateId === this.uvMatrix._updateID\n        )\n        {\n            return;\n        }\n\n        this._bufferUpdateId = this.uvBuffer._updateID;\n        this._textureUpdateId = this.uvMatrix._updateID;\n\n        const data = this.uvBuffer.data as Float32Array;\n\n        if (!this.data || this.data.length !== data.length)\n        {\n            (this.data as any) = new Float32Array(data.length);\n        }\n\n        this.uvMatrix.multiplyUvs(data, this.data);\n\n        this._updateID++;\n    }\n}\n", "import { State } from '@pixi/core';\nimport { Point, Polygon } from '@pixi/math';\nimport type { BLEND_MODES } from '@pixi/constants';\nimport { DRAW_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\nimport { settings } from '@pixi/settings';\nimport { MeshBatchUvs } from './MeshBatchUvs';\nimport type { MeshMaterial } from './MeshMaterial';\n\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Texture, Renderer, Geometry, Buffer, Shader } from '@pixi/core';\nimport type { IPointData } from '@pixi/math';\n\nconst tempPoint = new Point();\nconst tempPolygon = new Polygon();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Mesh extends GlobalMixins.Mesh {}\n\n/**\n * Base mesh class.\n *\n * This class empowers you to have maximum flexibility to render any kind of WebGL visuals you can think of.\n * This class assumes a certain level of WebGL knowledge.\n * If you know a bit this should abstract enough away to make your life easier!\n *\n * Pretty much ALL WebGL can be broken down into the following:\n * - Geometry - The structure and data for the mesh. This can include anything from positions, uvs, normals, colors etc..\n * - Shader - This is the shader that PixiJS will render the geometry with (attributes in the shader must match the geometry)\n * - State - This is the state of WebGL required to render the mesh.\n *\n * Through a combination of the above elements you can render anything you want, 2D or 3D!\n * @memberof PIXI\n */\nexport class Mesh<T extends Shader = MeshMaterial> extends Container\n{\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Mesh objects.\n     * @type {PIXI.Shader|PIXI.MeshMaterial}\n     */\n    public shader: T;\n\n    /**\n     * Represents the WebGL state the Mesh required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    public state: State;\n\n    /** The way the Mesh should be drawn, can be any of the {@link PIXI.DRAW_MODES} constants. */\n    public drawMode: DRAW_MODES;\n\n    /**\n     * Typically the index of the IndexBuffer where to start drawing.\n     * @default 0\n     */\n    public start: number;\n\n    /**\n     * How much of the geometry to draw, by default `0` renders everything.\n     * @default 0\n     */\n    public size: number;\n\n    private _geometry: Geometry;\n\n    /** This is the caching layer used by the batcher. */\n    private vertexData: Float32Array;\n\n    /** If geometry is changed used to decide to re-transform the vertexData. */\n    private vertexDirty: number;\n    private _transformID: number;\n\n    /** Internal roundPixels field. */\n    private _roundPixels: boolean;\n\n    /** Batched UV's are cached for atlas textures. */\n    private batchUvs: MeshBatchUvs;\n\n    // Internal-only properties\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    uvs: Float32Array;\n\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    indices: Uint16Array;\n    _tintRGB: number;\n    _texture: Texture;\n\n    /**\n     * @param geometry - The geometry the mesh will use.\n     * @param {PIXI.MeshMaterial} shader - The shader the mesh will use.\n     * @param state - The state that the WebGL context is required to be in to render the mesh\n     *        if no state is provided, uses {@link PIXI.State.for2d} to create a 2D state for PixiJS.\n     * @param drawMode - The drawMode, can be any of the {@link PIXI.DRAW_MODES} constants.\n     */\n    constructor(geometry: Geometry, shader: T, state?: State, drawMode: DRAW_MODES = DRAW_MODES.TRIANGLES)\n    {\n        super();\n\n        this.geometry = geometry;\n        this.shader = shader;\n        this.state = state || State.for2d();\n        this.drawMode = drawMode;\n        this.start = 0;\n        this.size = 0;\n\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = new Float32Array(1);\n        this.vertexDirty = -1;\n\n        this._transformID = -1;\n        this._roundPixels = settings.ROUND_PIXELS;\n        this.batchUvs = null;\n    }\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh objects.\n     */\n    get geometry(): Geometry\n    {\n        return this._geometry;\n    }\n\n    set geometry(value: Geometry)\n    {\n        if (this._geometry === value)\n        {\n            return;\n        }\n\n        if (this._geometry)\n        {\n            this._geometry.refCount--;\n\n            if (this._geometry.refCount === 0)\n            {\n                this._geometry.dispose();\n            }\n        }\n\n        this._geometry = value;\n\n        if (this._geometry)\n        {\n            this._geometry.refCount++;\n        }\n\n        this.vertexDirty = -1;\n    }\n\n    /**\n     * To change mesh uv's, change its uvBuffer data and increment its _updateID.\n     * @readonly\n     */\n    get uvBuffer(): Buffer\n    {\n        return this.geometry.buffers[1];\n    }\n\n    /**\n     * To change mesh vertices, change its uvBuffer data and increment its _updateID.\n     * Incrementing _updateID is optional because most of Mesh objects do it anyway.\n     * @readonly\n     */\n    get verticesBuffer(): Buffer\n    {\n        return this.geometry.buffers[0];\n    }\n\n    /** Alias for {@link PIXI.Mesh#shader}. */\n    set material(value: T)\n    {\n        this.shader = value;\n    }\n\n    get material(): T\n    {\n        return this.shader;\n    }\n\n    /**\n     * The blend mode to be applied to the Mesh. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL;\n     */\n    set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}\n     * @default false\n     */\n    set roundPixels(value: boolean)\n    {\n        if (this._roundPixels !== value)\n        {\n            this._transformID = -1;\n        }\n        this._roundPixels = value;\n    }\n\n    get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    /**\n     * The multiply tint applied to the Mesh. This is a hex value. A value of\n     * `0xFFFFFF` will remove any tint effect.\n     *\n     * Null for non-MeshMaterial shaders\n     * @default 0xFFFFFF\n     */\n    get tint(): number\n    {\n        return 'tint' in this.shader ? (this.shader as unknown as MeshMaterial).tint : null;\n    }\n\n    set tint(value: number)\n    {\n        (this.shader as unknown as MeshMaterial).tint = value;\n    }\n\n    /** The texture that the Mesh uses. Null for non-MeshMaterial shaders */\n    get texture(): Texture\n    {\n        return 'texture' in this.shader ? (this.shader as unknown as MeshMaterial).texture : null;\n    }\n\n    set texture(value: Texture)\n    {\n        (this.shader as unknown as MeshMaterial).texture = value;\n    }\n\n    /**\n     * Standard renderer draw.\n     * @param renderer - Instance to renderer.\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // set properties for batching..\n        // TODO could use a different way to grab verts?\n        const vertices = this.geometry.buffers[0].data;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        // TODO benchmark check for attribute size..\n        if (\n            shader.batchable\n            && this.drawMode === DRAW_MODES.TRIANGLES\n            && vertices.length < Mesh.BATCHABLE_SIZE * 2\n        )\n        {\n            this._renderToBatch(renderer);\n        }\n        else\n        {\n            this._renderDefault(renderer);\n        }\n    }\n\n    /**\n     * Standard non-batching way of rendering.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderDefault(renderer: Renderer): void\n    {\n        const shader = this.shader as unknown as MeshMaterial;\n\n        shader.alpha = this.worldAlpha;\n        if (shader.update)\n        {\n            shader.update();\n        }\n\n        renderer.batch.flush();\n\n        // bind and sync uniforms..\n        shader.uniforms.translationMatrix = this.transform.worldTransform.toArray(true);\n        renderer.shader.bind(shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // bind the geometry...\n        renderer.geometry.bind(this.geometry, shader);\n\n        // then render it\n        renderer.geometry.draw(this.drawMode, this.size, this.start, this.geometry.instanceCount);\n    }\n\n    /**\n     * Rendering by using the Batch system.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderToBatch(renderer: Renderer): void\n    {\n        const geometry = this.geometry;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (shader.uvMatrix)\n        {\n            shader.uvMatrix.update();\n            this.calculateUvs();\n        }\n\n        // set properties for batching..\n        this.calculateVertices();\n        this.indices = geometry.indexBuffer.data as Uint16Array;\n        this._tintRGB = shader._tintRGB;\n        this._texture = shader.texture;\n\n        const pluginName = (this.material as unknown as MeshMaterial).pluginName;\n\n        renderer.batch.setObjectRenderer(renderer.plugins[pluginName]);\n        renderer.plugins[pluginName].render(this);\n    }\n\n    /** Updates vertexData field based on transform and vertices. */\n    public calculateVertices(): void\n    {\n        const geometry = this.geometry;\n        const verticesBuffer = geometry.buffers[0];\n        const vertices = verticesBuffer.data;\n        const vertexDirtyId = verticesBuffer._updateID;\n\n        if (vertexDirtyId === this.vertexDirty && this._transformID === this.transform._worldID)\n        {\n            return;\n        }\n\n        this._transformID = this.transform._worldID;\n\n        if (this.vertexData.length !== vertices.length)\n        {\n            this.vertexData = new Float32Array(vertices.length);\n        }\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const vertexData = this.vertexData;\n\n        for (let i = 0; i < vertexData.length / 2; i++)\n        {\n            const x = vertices[(i * 2)];\n            const y = vertices[(i * 2) + 1];\n\n            vertexData[(i * 2)] = (a * x) + (c * y) + tx;\n            vertexData[(i * 2) + 1] = (b * x) + (d * y) + ty;\n        }\n\n        if (this._roundPixels)\n        {\n            const resolution = settings.RESOLUTION;\n\n            for (let i = 0; i < vertexData.length; ++i)\n            {\n                vertexData[i] = Math.round((vertexData[i] * resolution | 0) / resolution);\n            }\n        }\n\n        this.vertexDirty = vertexDirtyId;\n    }\n\n    /** Updates uv field based on from geometry uv's or batchUvs. */\n    public calculateUvs(): void\n    {\n        const geomUvs = this.geometry.buffers[1];\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (!shader.uvMatrix.isSimple)\n        {\n            if (!this.batchUvs)\n            {\n                this.batchUvs = new MeshBatchUvs(geomUvs, shader.uvMatrix);\n            }\n            this.batchUvs.update();\n            this.uvs = this.batchUvs.data;\n        }\n        else\n        {\n            this.uvs = geomUvs.data as Float32Array;\n        }\n    }\n\n    /**\n     * Updates the bounds of the mesh as a rectangle. The bounds calculation takes the worldTransform into account.\n     * there must be a aVertexPosition attribute present in the geometry for bounds to be calculated correctly.\n     */\n    protected _calculateBounds(): void\n    {\n        this.calculateVertices();\n\n        this._bounds.addVertexData(this.vertexData, 0, this.vertexData.length);\n    }\n\n    /**\n     * Tests if a point is inside this mesh. Works only for PIXI.DRAW_MODES.TRIANGLES.\n     * @param point - The point to test.\n     * @returns - The result of the test.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        if (!this.getBounds().contains(point.x, point.y))\n        {\n            return false;\n        }\n\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const vertices = this.geometry.getBuffer('aVertexPosition').data;\n\n        const points = tempPolygon.points;\n        const indices =  this.geometry.getIndex().data;\n        const len = indices.length;\n        const step = this.drawMode === 4 ? 3 : 1;\n\n        for (let i = 0; i + 2 < len; i += step)\n        {\n            const ind0 = indices[i] * 2;\n            const ind1 = indices[i + 1] * 2;\n            const ind2 = indices[i + 2] * 2;\n\n            points[0] = vertices[ind0];\n            points[1] = vertices[ind0 + 1];\n            points[2] = vertices[ind1];\n            points[3] = vertices[ind1 + 1];\n            points[4] = vertices[ind2];\n            points[5] = vertices[ind2 + 1];\n\n            if (tempPolygon.contains(tempPoint.x, tempPoint.y))\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        if (this._cachedTexture)\n        {\n            this._cachedTexture.destroy();\n            this._cachedTexture = null;\n        }\n\n        this.geometry = null;\n        this.shader = null;\n        this.state = null;\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = null;\n    }\n\n    /** The maximum number of vertices to consider batchable. Generally, the complexity of the geometry. */\n    public static BATCHABLE_SIZE = 100;\n}\n", "import { Program, Shader, TextureMatrix } from '@pixi/core';\nimport { Matrix } from '@pixi/math';\nimport { premultiplyTintToRgba } from '@pixi/utils';\nimport fragment from './shader/mesh.frag';\nimport vertex from './shader/mesh.vert';\n\nimport type { Texture } from '@pixi/core';\nimport type { Dict } from '@pixi/utils';\n\nexport interface IMeshMaterialOptions\n{\n    alpha?: number;\n    tint?: number;\n    pluginName?: string;\n    program?: Program;\n    uniforms?: Dict<unknown>;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface MeshMaterial extends GlobalMixins.MeshMaterial {}\n\n/**\n * Slightly opinionated default shader for PixiJS 2D objects.\n * @memberof PIXI\n */\nexport class MeshMaterial extends Shader\n{\n    /**\n     * TextureMatrix instance for this Mesh, used to track Texture changes.\n     * @readonly\n     */\n    public readonly uvMatrix: TextureMatrix;\n\n    /**\n     * `true` if shader can be batch with the renderer's batch system.\n     * @default true\n     */\n    public batchable: boolean;\n\n    /**\n     * Renderer plugin for batching.\n     * @default 'batch'\n     */\n    public pluginName: string;\n\n    // Internal-only properties\n    _tintRGB: number;\n\n    /**\n     * Only do update if tint or alpha changes.\n     * @private\n     * @default false\n     */\n    private _colorDirty: boolean;\n    private _alpha: number;\n    private _tint: number;\n\n    /**\n     * @param uSampler - Texture that material uses to render.\n     * @param options - Additional options\n     * @param {number} [options.alpha=1] - Default alpha.\n     * @param {number} [options.tint=0xFFFFFF] - Default tint.\n     * @param {string} [options.pluginName='batch'] - Renderer plugin for batching.\n     * @param {PIXI.Program} [options.program=0xFFFFFF] - Custom program.\n     * @param {object} [options.uniforms] - Custom uniforms.\n     */\n    constructor(uSampler: Texture, options?: IMeshMaterialOptions)\n    {\n        const uniforms = {\n            uSampler,\n            alpha: 1,\n            uTextureMatrix: Matrix.IDENTITY,\n            uColor: new Float32Array([1, 1, 1, 1]),\n        };\n\n        // Set defaults\n        options = Object.assign({\n            tint: 0xFFFFFF,\n            alpha: 1,\n            pluginName: 'batch',\n        }, options);\n\n        if (options.uniforms)\n        {\n            Object.assign(uniforms, options.uniforms);\n        }\n\n        super(options.program || Program.from(vertex, fragment), uniforms);\n\n        this._colorDirty = false;\n\n        this.uvMatrix = new TextureMatrix(uSampler);\n        this.batchable = options.program === undefined;\n        this.pluginName = options.pluginName;\n\n        this.tint = options.tint;\n        this.alpha = options.alpha;\n    }\n\n    /** Reference to the texture being rendered. */\n    get texture(): Texture\n    {\n        return this.uniforms.uSampler;\n    }\n    set texture(value: Texture)\n    {\n        if (this.uniforms.uSampler !== value)\n        {\n            if (!this.uniforms.uSampler.baseTexture.alphaMode !== !value.baseTexture.alphaMode)\n            {\n                this._colorDirty = true;\n            }\n\n            this.uniforms.uSampler = value;\n            this.uvMatrix.texture = value;\n        }\n    }\n\n    /**\n     * This gets automatically set by the object using this.\n     * @default 1\n     */\n    set alpha(value: number)\n    {\n        if (value === this._alpha) return;\n\n        this._alpha = value;\n        this._colorDirty = true;\n    }\n    get alpha(): number\n    {\n        return this._alpha;\n    }\n\n    /**\n     * Multiply tint for the material.\n     * @default 0xFFFFFF\n     */\n    set tint(value: number)\n    {\n        if (value === this._tint) return;\n\n        this._tint = value;\n        this._tintRGB = (value >> 16) + (value & 0xff00) + ((value & 0xff) << 16);\n        this._colorDirty = true;\n    }\n    get tint(): number\n    {\n        return this._tint;\n    }\n\n    /** Gets called automatically by the Mesh. Intended to be overridden for custom {@link MeshMaterial} objects. */\n    public update(): void\n    {\n        if (this._colorDirty)\n        {\n            this._colorDirty = false;\n            const baseTexture = this.texture.baseTexture;\n\n            premultiplyTintToRgba(\n                this._tint, this._alpha, this.uniforms.uColor, (baseTexture.alphaMode as unknown as boolean)\n            );\n        }\n        if (this.uvMatrix.update())\n        {\n            this.uniforms.uTextureMatrix = this.uvMatrix.mapCoord;\n        }\n    }\n}\n", "import { TYPES } from '@pixi/constants';\nimport { <PERSON>uff<PERSON>, Geometry } from '@pixi/core';\n\nimport type { IArrayBuffer } from '@pixi/core';\n\n/**\n * Standard 2D geometry used in PixiJS.\n *\n * Geometry can be defined without passing in a style or data if required.\n *\n * ```js\n * const geometry = new PIXI.Geometry();\n *\n * geometry.addAttribute('positions', [0, 0, 100, 0, 100, 100, 0, 100], 2);\n * geometry.addAttribute('uvs', [0,0,1,0,1,1,0,1], 2);\n * geometry.addIndex([0,1,2,1,3,2]);\n *\n * ```\n * @memberof PIXI\n */\nexport class MeshGeometry extends Geometry\n{\n    // Internal-only properties\n    /**\n     * Dirty flag to limit update calls on Mesh. For example,\n     * limiting updates on a single Mesh instance with a shared Geometry\n     * within the render loop.\n     * @private\n     * @default -1\n     */\n    _updateId: number;\n\n    /**\n     * @param {Float32Array|number[]} [vertices] - Positional data on geometry.\n     * @param {Float32Array|number[]} [uvs] - Texture UVs.\n     * @param {Uint16Array|number[]} [index] - IndexBuffer\n     */\n    constructor(vertices?: IArrayBuffer, uvs?: IArrayBuffer, index?: IArrayBuffer)\n    {\n        super();\n\n        const verticesBuffer = new Buffer(vertices);\n        const uvsBuffer = new Buffer(uvs, true);\n        const indexBuffer = new Buffer(index, true, true);\n\n        this.addAttribute('aVertexPosition', verticesBuffer, 2, false, TYPES.FLOAT)\n            .addAttribute('aTextureCoord', uvsBuffer, 2, false, TYPES.FLOAT)\n            .addIndex(indexBuffer);\n\n        this._updateId = -1;\n    }\n\n    /**\n     * If the vertex position is updated.\n     * @readonly\n     * @private\n     */\n    get vertexDirtyId(): number\n    {\n        return this.buffers[0]._updateID;\n    }\n}\n"], "names": ["Point", "Polygon", "DRAW_MODES", "State", "settings", "Container", "Matrix", "Program", "TextureMatrix", "premultiplyTintToRgba", "Shader", "<PERSON><PERSON><PERSON>", "TYPES", "Geometry"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;ACzBA;;;AAGG;AACH,IAAA,YAAA,kBAAA,YAAA;AAiBI;;;AAGG;IACH,SAAY,YAAA,CAAA,QAAgB,EAAE,QAAuB,EAAA;AAEjD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAEjB,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;KACtB;AAED;;;AAGG;IACI,YAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,WAAqB,EAAA;AAE/B,QAAA,IAAI,CAAC,WAAW;AACT,eAAA,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;eAChD,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,EAExD;YACI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;AAEhD,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAoB,CAAC;AAEhD,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAClD;YACK,IAAI,CAAC,IAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtD,SAAA;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,EAAE,CAAC;KACpB,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA;;ACrDD,IAAM,SAAS,GAAG,IAAIA,UAAK,EAAE,CAAC;AAC9B,IAAM,WAAW,GAAG,IAAIC,YAAO,EAAE,CAAC;AAKlC;;;;;;;;;;;;;;AAcG;AACH,IAAA,IAAA,kBAAA,UAAA,MAAA,EAAA;IAA2D,SAAS,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AA4DhE;;;;;;AAMG;AACH,IAAA,SAAA,IAAA,CAAY,QAAkB,EAAE,MAAS,EAAE,KAAa,EAAE,QAA2C,EAAA;AAA3C,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAA,GAAuBC,oBAAU,CAAC,SAAS,CAAA,EAAA;AAArG,QAAA,IAAA,KAAA,GAEI,iBAAO,IAiBV,IAAA,CAAA;AAfG,QAAA,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,KAAK,GAAG,KAAK,IAAIC,UAAK,CAAC,KAAK,EAAE,CAAC;AACpC,QAAA,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,KAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,KAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAEd,QAAA,KAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AAChB,QAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,KAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AACtC,QAAA,KAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AAEtB,QAAA,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AACvB,QAAA,KAAI,CAAC,YAAY,GAAGC,iBAAQ,CAAC,YAAY,CAAC;AAC1C,QAAA,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;;KACxB;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AALZ;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;AAED,QAAA,GAAA,EAAA,UAAa,KAAe,EAAA;AAExB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAC5B;gBACI,OAAO;AACV,aAAA;YAED,IAAI,IAAI,CAAC,SAAS,EAClB;AACI,gBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAE1B,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,CAAC,EACjC;AACI,oBAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;AAC5B,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAEvB,IAAI,IAAI,CAAC,SAAS,EAClB;AACI,gBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;SACzB;;;AA3BA,KAAA,CAAA,CAAA;AAiCD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAJZ;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACnC;;;AAAA,KAAA,CAAA,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAc,CAAA,SAAA,EAAA,gBAAA,EAAA;AALlB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACnC;;;AAAA,KAAA,CAAA,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAKZ,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,MAAM,CAAC;SACtB;;AARD,QAAA,GAAA,EAAA,UAAa,KAAQ,EAAA;AAEjB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACvB;;;AAAA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AAKb,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;SAC/B;AAbD;;;;AAIG;AACH,QAAA,GAAA,EAAA,UAAc,KAAkB,EAAA;AAE5B,YAAA,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;SAChC;;;AAAA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AASf,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;AAnBD;;;;;;AAMG;AACH,QAAA,GAAA,EAAA,UAAgB,KAAc,EAAA;AAE1B,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAC/B;AACI,gBAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AAC1B,aAAA;AACD,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;SAC7B;;;AAAA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAPR;;;;;;AAMG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,MAAM,IAAI,IAAI,CAAC,MAAM,GAAI,IAAI,CAAC,MAAkC,CAAC,IAAI,GAAG,IAAI,CAAC;SACvF;AAED,QAAA,GAAA,EAAA,UAAS,KAAa,EAAA;AAEjB,YAAA,IAAI,CAAC,MAAkC,CAAC,IAAI,GAAG,KAAK,CAAC;SACzD;;;AALA,KAAA,CAAA,CAAA;AAQD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAO,CAAA,SAAA,EAAA,SAAA,EAAA;;AAAX,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,SAAS,IAAI,IAAI,CAAC,MAAM,GAAI,IAAI,CAAC,MAAkC,CAAC,OAAO,GAAG,IAAI,CAAC;SAC7F;AAED,QAAA,GAAA,EAAA,UAAY,KAAc,EAAA;AAErB,YAAA,IAAI,CAAC,MAAkC,CAAC,OAAO,GAAG,KAAK,CAAC;SAC5D;;;AALA,KAAA,CAAA,CAAA;AAOD;;;AAGG;IACO,IAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,QAAkB,EAAA;;;AAIhC,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC/C,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;;QAGtD,IACI,MAAM,CAAC,SAAS;AACb,eAAA,IAAI,CAAC,QAAQ,KAAKF,oBAAU,CAAC,SAAS;eACtC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,EAEhD;AACI,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACjC,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACjC,SAAA;KACJ,CAAA;AAED;;;AAGG;IACO,IAAc,CAAA,SAAA,CAAA,cAAA,GAAxB,UAAyB,QAAkB,EAAA;AAEvC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;AAEtD,QAAA,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/B,IAAI,MAAM,CAAC,MAAM,EACjB;YACI,MAAM,CAAC,MAAM,EAAE,CAAC;AACnB,SAAA;AAED,QAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;;AAGvB,QAAA,MAAM,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAChF,QAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;QAG7B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;QAG/B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;;QAG9C,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;KAC7F,CAAA;AAED;;;AAGG;IACO,IAAc,CAAA,SAAA,CAAA,cAAA,GAAxB,UAAyB,QAAkB,EAAA;AAEvC,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;QAEtD,IAAI,MAAM,CAAC,QAAQ,EACnB;AACI,YAAA,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACzB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;;QAGD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAmB,CAAC;AACxD,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAChC,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AAE/B,QAAA,IAAM,UAAU,GAAI,IAAI,CAAC,QAAoC,CAAC,UAAU,CAAC;AAEzE,QAAA,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC/D,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC7C,CAAA;;AAGM,IAAA,IAAA,CAAA,SAAA,CAAA,iBAAiB,GAAxB,YAAA;AAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC3C,QAAA,IAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC;AACrC,QAAA,IAAM,aAAa,GAAG,cAAc,CAAC,SAAS,CAAC;AAE/C,QAAA,IAAI,aAAa,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,EACvF;YACI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAE5C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAC9C;YACI,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;AACzC,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACf,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACjB,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAEjB,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAEnC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAC9C;YACI,IAAM,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAC5B,YAAA,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YAEhC,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC7C,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACpD,SAAA;QAED,IAAI,IAAI,CAAC,YAAY,EACrB;AACI,YAAA,IAAM,UAAU,GAAGE,iBAAQ,CAAC,UAAU,CAAC;AAEvC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAC1C;gBACI,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC;AAC7E,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC;KACpC,CAAA;;AAGM,IAAA,IAAA,CAAA,SAAA,CAAA,YAAY,GAAnB,YAAA;QAEI,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACzC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;AAEtD,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAC7B;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAClB;AACI,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC9D,aAAA;AACD,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjC,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAoB,CAAC;AAC3C,SAAA;KACJ,CAAA;AAED;;;AAGG;AACO,IAAA,IAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;QAEI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAEzB,QAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KAC1E,CAAA;AAED;;;;AAIG;IACI,IAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,KAAiB,EAAA;AAElC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAChD;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAEnD,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;AAEjE,QAAA,IAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAClC,IAAM,OAAO,GAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC;AAC/C,QAAA,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;AAC3B,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEzC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,IAAI,EACtC;YACI,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAChC,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAEhC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAE/B,YAAA,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAClD;AACI,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;IAEM,IAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;AAE9C,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,cAAc,EACvB;AACI,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AAChB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B,CAAA;;IAGa,IAAc,CAAA,cAAA,GAAG,GAAG,CAAC;IACvC,OAAC,IAAA,CAAA;CAAA,CAjc0DC,iBAAS,CAicnE;;;;;;AC9cD;;;AAGG;AACH,IAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;IAAkC,SAAM,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;AAgCpC;;;;;;;;AAQG;IACH,SAAY,YAAA,CAAA,QAAiB,EAAE,OAA8B,EAAA;QAA7D,IA+BC,KAAA,GAAA,IAAA,CAAA;AA7BG,QAAA,IAAM,QAAQ,GAAG;AACb,YAAA,QAAQ,EAAA,QAAA;AACR,YAAA,KAAK,EAAE,CAAC;YACR,cAAc,EAAEC,WAAM,CAAC,QAAQ;AAC/B,YAAA,MAAM,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACzC,CAAC;;AAGF,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACpB,YAAA,IAAI,EAAE,QAAQ;AACd,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,UAAU,EAAE,OAAO;SACtB,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,OAAO,CAAC,QAAQ,EACpB;YACI,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC7C,SAAA;AAED,QAAA,KAAA,GAAA,kBAAM,OAAO,CAAC,OAAO,IAAIC,YAAO,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,IAAC,IAAA,CAAA;AAEnE,QAAA,KAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,KAAI,CAAC,QAAQ,GAAG,IAAIC,kBAAa,CAAC,QAAQ,CAAC,CAAC;QAC5C,KAAI,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC;AAC/C,QAAA,KAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AAErC,QAAA,KAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACzB,QAAA,KAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;;KAC9B;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAO,CAAA,SAAA,EAAA,SAAA,EAAA;;AAAX,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;SACjC;AACD,QAAA,GAAA,EAAA,UAAY,KAAc,EAAA;AAEtB,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,EACpC;AACI,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,EAClF;AACI,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,iBAAA;AAED,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC/B,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;AACjC,aAAA;SACJ;;;AAbA,KAAA,CAAA,CAAA;AAmBD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AAOT,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,MAAM,CAAC;SACtB;AAdD;;;AAGG;AACH,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;AAEnB,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM;kBAAE,OAAO,EAAA;AAElC,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;;;AAAA,KAAA,CAAA,CAAA;AAUD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAQR,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AAfD;;;AAGG;AACH,QAAA,GAAA,EAAA,UAAS,KAAa,EAAA;AAElB,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK;kBAAE,OAAO,EAAA;AAEjC,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;;;AAAA,KAAA,CAAA,CAAA;;AAOM,IAAA,YAAA,CAAA,SAAA,CAAA,MAAM,GAAb,YAAA;QAEI,IAAI,IAAI,CAAC,WAAW,EACpB;AACI,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,YAAA,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;AAE7C,YAAAC,2BAAqB,CACjB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAG,WAAW,CAAC,SAAgC,CAC/F,CAAC;AACL,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAC1B;YACI,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACzD,SAAA;KACJ,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CA/IA,CAAkCC,WAAM,CA+IvC;;ACnKD;;;;;;;;;;;;;;AAcG;AACH,IAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;IAAkC,SAAQ,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;AAYtC;;;;AAIG;AACH,IAAA,SAAA,YAAA,CAAY,QAAuB,EAAE,GAAkB,EAAE,KAAoB,EAAA;AAA7E,QAAA,IAAA,KAAA,GAEI,iBAAO,IAWV,IAAA,CAAA;AATG,QAAA,IAAM,cAAc,GAAG,IAAIC,WAAM,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAM,SAAS,GAAG,IAAIA,WAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACxC,IAAM,WAAW,GAAG,IAAIA,WAAM,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAElD,QAAA,KAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,cAAc,EAAE,CAAC,EAAE,KAAK,EAAEC,eAAK,CAAC,KAAK,CAAC;AACtE,aAAA,YAAY,CAAC,eAAe,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAEA,eAAK,CAAC,KAAK,CAAC;aAC/D,QAAQ,CAAC,WAAW,CAAC,CAAC;AAE3B,QAAA,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;;KACvB;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAa,CAAA,SAAA,EAAA,eAAA,EAAA;AALjB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;SACpC;;;AAAA,KAAA,CAAA,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAzCA,CAAkCC,aAAQ,CAyCzC;;;;;;;"}