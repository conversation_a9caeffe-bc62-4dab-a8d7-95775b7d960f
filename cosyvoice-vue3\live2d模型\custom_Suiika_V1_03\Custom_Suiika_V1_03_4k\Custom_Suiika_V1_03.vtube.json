{"Version": 1, "Name": "Custom_Suiika_V1_03", "ModelID": "c0fe7d5b56c4454ba6d85e3128df5747", "FileReferences": {"Icon": "icon4k.png", "Model": "Custom_Suiika_V1_03.model3.json", "IdleAnimation": "Idle.motion3.json", "IdleAnimationWhenTrackingLost": ""}, "ModelSaveMetadata": {"LastSavedVTubeStudioVersion": "1.31.7", "LastSavedPlatform": "Steam", "LastSavedDateUTC": "Saturday, 01 March 2025, 16:23:39", "LastSavedDateLocalTime": "Saturday, 01 March 2025, 16:23:39", "LastSavedDateUnixMillisecondTimestamp": "1740846219780"}, "SavedModelPosition": {"Position": {"x": 8.444067001342773, "y": -342.3276062011719, "z": 0.0}, "Rotation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}, "Scale": {"x": 5.89064884185791, "y": 5.89064884185791, "z": 1.0}}, "ModelPositionMovement": {"Use": false, "X": 0, "Y": 0, "Z": -8, "SmoothingX": 10, "SmoothingY": 10, "SmoothingZ": 10}, "ItemSettings": {"OnlyMoveWhenPinned": false, "AllowNormalHotkeyTriggers": true, "Multiplier_HeadAngleX": 1.0, "Multiplier_HeadAngleY": 1.0, "Multiplier_HeadAngleZ": 1.0, "Shift_HeadAngleX": 0.0, "Shift_HeadAngleY": 0.0, "Smoothing_HeadAngleX": 15.0, "Smoothing_HeadAngleY": 15.0, "Smoothing_HeadAngleZ": 15.0}, "PhysicsSettings": {"Use": true, "UseLegacyPhysics": false, "Live2DPhysicsFPS": 1, "PhysicsStrength": 50, "WindStrength": 0, "DraggingPhysicsStrength": 0}, "GeneralSettings": {"TimeUntilTrackingLostIdleAnimation": 0.0, "WorkshopSharingForbidden": true, "EnableExpressionSaving": true}, "ParameterSettings": [{"Folder": "", "Name": "Auto Breath", "Input": "", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": true, "OutputLive2D": "ParamBreath", "Smoothing": 0, "Minimized": true}, {"Folder": "", "Name": "Eyes Smile", "Input": "MouthSmile", "InputRangeLower": 0.5299999713897705, "InputRangeUpper": 0.8999999761581421, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.5, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeLSmile", "Smoothing": 10, "Minimized": true}, {"Folder": "", "Name": "Eye X", "Input": "EyeRightX", "InputRangeLower": -0.30000001192092896, "InputRangeUpper": 0.30000001192092896, "OutputRangeLower": 1.0, "OutputRangeUpper": -1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeBallX", "Smoothing": 8, "Minimized": true}, {"Folder": "", "Name": "Eye Y", "Input": "EyeLeftY", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeBallY", "Smoothing": 8, "Minimized": true}, {"Folder": "", "Name": "Brow Height Left", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -2.0, "OutputRangeUpper": 2.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowLY", "Smoothing": 8, "Minimized": true}, {"Folder": "MOUTH", "Name": "Mouth Smile", "Input": "MouthSmile", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -3.0, "OutputRangeUpper": 3.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamMouthForm", "Smoothing": 0, "Minimized": true}, {"Folder": "MOUTH", "Name": "Mouth Open", "Input": "MouthOpen", "InputRangeLower": 0.029999999329447746, "InputRangeUpper": 0.800000011920929, "OutputRangeLower": 0.0, "OutputRangeUpper": 2.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamMouthOpenY", "Smoothing": 0, "Minimized": false}, {"Folder": "", "Name": "Face Left/Right Rotation", "Input": "FaceAngleX", "InputRangeLower": -20.0, "InputRangeUpper": 20.0, "OutputRangeLower": -40.0, "OutputRangeUpper": 40.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "AngleX", "Smoothing": 15, "Minimized": true}, {"Folder": "", "Name": "Face Up/Down Rotation", "Input": "FaceAngleY", "InputRangeLower": -15.0, "InputRangeUpper": 15.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "AngleY", "Smoothing": 15, "Minimized": true}, {"Folder": "", "Name": "Face Lean Rotation", "Input": "FaceAngleZ", "InputRangeLower": -13.0, "InputRangeUpper": 13.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "AngleZ", "Smoothing": 30, "Minimized": true}, {"Folder": "", "Name": "Body Rotation X", "Input": "FaceAngleX", "InputRangeLower": -25.0, "InputRangeUpper": 25.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBodyAngleX", "Smoothing": 40, "Minimized": true}, {"Folder": "", "Name": "Body Rotation Y", "Input": "FaceAngleY", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param93", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "Body Rotation Z", "Input": "FaceAngleZ", "InputRangeLower": -20.0, "InputRangeUpper": 20.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param94", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "Tongue", "Input": "TongueOut", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param100", "Smoothing": 30, "Minimized": true}, {"Folder": "", "Name": "Eye Open L", "Input": "EyeOpenLeft", "InputRangeLower": 0.0, "InputRangeUpper": 0.699999988079071, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.5, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeLOpen", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "Eye Open R", "Input": "EyeOpenRight", "InputRangeLower": 0.0, "InputRangeUpper": 0.699999988079071, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.5, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeROpen", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "<PERSON><PERSON>", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowLAngle", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "Brow Form", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowLForm", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "Jaw Open", "Input": "JawOpen", "InputRangeLower": 0.029999999329447746, "InputRangeUpper": 0.800000011920929, "OutputRangeLower": 0.0, "OutputRangeUpper": 2.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param97", "Smoothing": 0, "Minimized": true}, {"Folder": "", "Name": "Mouth Funnel", "Input": "MouthFunnel", "InputRangeLower": 0.0, "InputRangeUpper": 0.5, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.5, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param95", "Smoothing": 10, "Minimized": true}, {"Folder": "", "Name": "<PERSON> Pucker <PERSON>n", "Input": "<PERSON><PERSON>ucker", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param99", "Smoothing": 10, "Minimized": true}, {"Folder": "", "Name": "Lip Press Open", "Input": "MouthPressLipOpen", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.5, "OutputRangeUpper": 1.5, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param98", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "<PERSON>hrug", "Input": "MouthShrug", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 2.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param96", "Smoothing": 30, "Minimized": true}, {"Folder": "", "Name": "Cheek <PERSON>", "Input": "<PERSON><PERSON><PERSON><PERSON>", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 2.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamCheek", "Smoothing": 20, "Minimized": true}, {"Folder": "", "Name": "Mouth X", "Input": "MouthX", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param101", "Smoothing": 20, "Minimized": true}], "Hotkeys": [{"HotkeyID": "75c8cec12550470fb40990664926b9ba", "Name": "Pleading", "Action": "ToggleExpression", "File": "Pleading.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N1", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "f37898bfc81540828ed18b096a82bce9", "Name": "Spirals", "Action": "ToggleExpression", "File": "Spirals.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N2", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.10000000149011612, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "b86fd2524c2f461aaaccf5056f32623a", "Name": "Stars", "Action": "ToggleExpression", "File": "Stars.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N3", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "3ce33a3951cb4058a781aeb8cffe0871", "Name": "Hearts", "Action": "ToggleExpression", "File": "Hearts.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N4", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "d2590d5c1fdb4ea8b35897262535e4e8", "Name": "Face Shadow", "Action": "ToggleExpression", "File": "Face Shadow.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N5", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 1.0, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "d8fd9d1949ab4899a93240534892bbdc", "Name": "Remove All Expressions", "Action": "RemoveAllExpressions", "File": "", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "Numpad0", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "ec9a7ed33aa04d54a43677bc8e4601c6", "Name": "Back Hair Short", "Action": "ToggleExpression", "File": "Short Hair.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N6", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "6d544c5658e841e1966919e7ec77fe10", "Name": "Ponytail", "Action": "ToggleExpression", "File": "Ponytail.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N7", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "a75eac36691f48fb844c819878c73774", "Name": "Back Hair Wavy", "Action": "ToggleExpression", "File": "Back Hair Wavy.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N8", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "2d2d04559a38493e823a2763881ac98a", "Name": "<PERSON><PERSON><PERSON>", "Action": "ToggleExpression", "File": "Braid.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N9", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "f5c66222074240a1be4701fe0f697c9d", "Name": "Over Body Twintails", "Action": "ToggleExpression", "File": "Over Body Twintails.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F1", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "49a9b1825b7246b7905729bada9469c6", "Name": "Sideswept Over Body twintail", "Action": "ToggleExpression", "File": "Over Body Sideswept Twintail.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "Subtract", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "3119a52c82a44cb68f0bdc6e2f761dda", "Name": "Hairbuns (high)", "Action": "ToggleExpression", "File": "Hairbuns (high).exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F2", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "cba962415f794d08a7f668ace4c5f03e", "Name": "Hairbuns (low)", "Action": "ToggleExpression", "File": "Hairbuns (low).exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F3", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "13dbd85d528e42a88c35b0986d762917", "Name": "Twintails (short & straight)", "Action": "ToggleExpression", "File": "Twintails (short & straight).exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "F1", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "5a9c650fc34e43d5bef137376c51a1a6", "Name": "Twintails (long & wavy)", "Action": "ToggleExpression", "File": "Twintails (long & wavy).exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F4", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "bda5125aa81e42a6929afb2992655387", "Name": "<PERSON><PERSON><PERSON> (sprout)", "Action": "ToggleExpression", "File": "<PERSON><PERSON><PERSON> (sprout).exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F5", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "6c529738cedb420bb2c35f51b24ff2c4", "Name": "<PERSON> Ears", "Action": "ToggleExpression", "File": "Cat Ears.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F6", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "db7db55f9bae42638053ec6d26504f63", "Name": "<PERSON>", "Action": "ToggleExpression", "File": "<PERSON> Ears.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F7", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "93dddb5ce52b42a480e7438cd098cc31", "Name": "<PERSON> Ears", "Action": "ToggleExpression", "File": "Fox Ears.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F8", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "eecedbcc28804f26a7c8214ac2ecff6f", "Name": "Dog Ears", "Action": "ToggleExpression", "File": "Dog Ears.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F9", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "3b0985ba09124cdaa7521b4c5e80e85b", "Name": "Glasses (round)", "Action": "ToggleExpression", "File": "Glasses (round).exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F10", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "4d3977ffa70d4bc2a0d0df542696d274", "Name": "Earrings", "Action": "ToggleExpression", "File": "Earrings.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F11", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "981145a803df46659457c0972ebf1258", "Name": "<PERSON><PERSON><PERSON>", "Action": "ToggleExpression", "File": "F<PERSON>ffy <PERSON>l.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "F12", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "fb12abd149f943babad530369bf99948", "Name": "<PERSON>", "Action": "ToggleExpression", "File": "Thin Tail.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "RightControl", "Trigger2": "N1", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "ffc277b92e674722a8e4b078e46a2675", "Name": "Tail Up", "Action": "ToggleExpression", "File": "Tail Up.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "RightControl", "Trigger2": "N2", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 2.0, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "3d1e64332995407cbf0a535f1aec47d9", "Name": "Wave Arm L", "Action": "ToggleExpression", "File": "Wave Arm L.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "RightControl", "Trigger2": "N3", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "00f38d62cb1a4602bfb02987520280fa", "Name": "Wave Arm R", "Action": "ToggleExpression", "File": "Wave Arm R.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "RightControl", "Trigger2": "N4", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "c68f61fac7594794b1201a01b9fcdb50", "Name": "<PERSON><PERSON>", "Action": "ToggleExpression", "File": "Blush.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "RightControl", "Trigger2": "N5", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "3540b39ab4194a73a24537aecbe6e73e", "Name": "suiika", "Action": "ToggleExpression", "File": "suiika.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "TwitchTriggers": {"Active": false, "CooldownActive": false, "CooldownSeconds": 30.0, "ResetActive": false, "HideModelResetDialog": false, "Active_GiftSubTrigger": false, "Active_BitTrigger": false, "Active_RedeemTrigger": false, "Active_TextCommandTrigger": false, "Active_SubTrigger": false, "Active_FollowTrigger": false, "Active_RaidTrigger": false, "Active_ShoutoutTrigger": false, "Active_AdbreakTrigger": false, "Trigger_TextCommandTrigger": "", "Trigger_Redeem_Name": "", "Trigger_Redeem_ID": "", "UseTextTriggerUserTypeRestriction": false, "RestrictTextTriggerTo": {"Allowed_You": true, "Allowed_Subs": false, "Allowed_Mods": false, "Allowed_VIPs": false, "Allowed_Artists": false, "Allowed_UserNames": false, "UserNames": [], "Allowed_UserIDs": false, "UserIDs": []}, "AllowRepeatedFollow": false, "Trigger_GiftSubTrigger": {"Min": 1.0, "Max": 100.0}, "Trigger_BitTrigger": {"Min": 100.0, "Max": 3000.0}}, "Triggers": {"Trigger1": "Numpad1", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": true, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.009999999776482582, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "HotkeySettings": {"UseOnScreenHotkeys": false, "UseKeyboardHotkeys": true, "SendOnScreenHotkeysToPC": true, "OnScreenHotkeyAlpha": 75}, "ArtMeshDetails": {"ArtMeshesExcludedFromPinning": [], "ArtMeshesThatDeleteItemsOnDrop": [], "ArtMeshSceneLightingMultipliers": [{"ID": "ArtMesh25", "Value": 0.7593201398849487}, {"ID": "ArtMesh26", "Value": 0.7628516554832458}, {"ID": "ArtMesh27", "Value": 0.7593199610710144}, {"ID": "ArtMesh28", "Value": 0.762852132320404}, {"ID": "ArtMesh29", "Value": 0.7628518342971802}, {"ID": "ArtMesh30", "Value": 0.7628528475761414}, {"ID": "ArtMesh231", "Value": 0.0}, {"ID": "ArtMesh234", "Value": 0.0}, {"ID": "ArtMesh235", "Value": 0.0}, {"ID": "ArtMesh236", "Value": 0.0}, {"ID": "ArtMesh237", "Value": 0.0}, {"ID": "ponytail_3_5", "Value": 0.0}, {"ID": "ponytail_3_6", "Value": 0.0}, {"ID": "ponytail_3_7", "Value": 0.0}, {"ID": "ArtMesh285", "Value": 0.0}, {"ID": "ArtMesh282", "Value": 0.5512391924858093}, {"ID": "ArtMesh278", "Value": 0.0}, {"ID": "ArtMesh276", "Value": 0.0}, {"ID": "ArtMesh32", "Value": 0.0}, {"ID": "ArtMesh34", "Value": 0.0}, {"ID": "ArtMesh35", "Value": 0.0}, {"ID": "ArtMesh37", "Value": 0.0}, {"ID": "ponytail_3_4", "Value": 0.0}, {"ID": "ArtMesh109", "Value": 0.0}, {"ID": "ArtMesh385", "Value": 0.0}, {"ID": "ArtMesh113", "Value": 0.0}, {"ID": "ArtMesh117", "Value": 0.0}, {"ID": "ArtMesh386", "Value": 0.0}, {"ID": "ArtMesh387", "Value": 0.0}, {"ID": "ArtMesh421", "Value": 0.0}, {"ID": "ArtMesh422", "Value": 0.0}, {"ID": "ArtMesh423", "Value": 0.0}, {"ID": "ArtMesh424", "Value": 0.0}, {"ID": "ArtMesh425", "Value": 0.0}, {"ID": "ArtMesh426", "Value": 0.0}, {"ID": "ArtMesh432", "Value": 0.0}, {"ID": "ArtMesh431", "Value": 0.0}, {"ID": "ArtMesh430", "Value": 0.0}, {"ID": "ArtMesh429", "Value": 0.0}, {"ID": "ArtMesh428", "Value": 0.0}, {"ID": "ArtMesh433", "Value": 0.0}, {"ID": "ArtMesh394", "Value": 0.6995079517364502}, {"ID": "ArtMesh382", "Value": 0.0}, {"ID": "flower_33", "Value": 0.0}, {"ID": "flower_32", "Value": 0.0}, {"ID": "flower_31", "Value": 0.0}, {"ID": "flower_30", "Value": 0.0}, {"ID": "flower_27", "Value": 0.0}, {"ID": "flower_26", "Value": 0.0}, {"ID": "flower_25", "Value": 0.0}, {"ID": "flower_24", "Value": 0.0}, {"ID": "flower_23", "Value": 0.0}, {"ID": "flower_22", "Value": 0.0}, {"ID": "flower_21", "Value": 0.0}, {"ID": "flower_20", "Value": 0.0}, {"ID": "flower_19", "Value": 0.0}, {"ID": "flower_18", "Value": 0.0}, {"ID": "flower_17", "Value": 0.0}, {"ID": "move4", "Value": 0.0}, {"ID": "ArtMesh130", "Value": 0.7402359247207642}, {"ID": "ArtMesh449", "Value": 0.0}, {"ID": "ArtMesh448", "Value": 0.0}, {"ID": "ArtMesh447", "Value": 0.0}, {"ID": "ArtMesh446", "Value": 0.0}, {"ID": "ArtMesh445", "Value": 0.0}, {"ID": "ArtMesh454", "Value": 0.0}, {"ID": "ArtMesh453", "Value": 0.0}, {"ID": "ArtMesh452", "Value": 0.0}, {"ID": "ArtMesh451", "Value": 0.0}, {"ID": "ArtMesh450", "Value": 0.0}, {"ID": "move2", "Value": 0.0}, {"ID": "ArtMesh475", "Value": 0.0}, {"ID": "ArtMesh474", "Value": 0.25127527117729187}, {"ID": "ArtMesh370", "Value": 0.0}, {"ID": "ArtMesh274", "Value": 0.0}, {"ID": "ArtMesh520", "Value": 0.0}, {"ID": "ArtMesh517", "Value": 0.5756704807281494}, {"ID": "ArtMesh513", "Value": 0.0}, {"ID": "ArtMesh510", "Value": 0.0}, {"ID": "ArtMesh509", "Value": 0.0}, {"ID": "ArtMesh508", "Value": 0.0}], "ArtMeshMultiplyAndScreenColors": []}, "ParameterCustomization": {"ParametersExcludedFromVNetSmoothing": []}, "PhysicsCustomizationSettings": {"PhysicsMultipliersPerPhysicsGroup": [], "WindMultipliersPerPhysicsGroup": [], "DraggingPhysicsMultipliersPerPhysicsGroup": []}, "FolderInfo": {"HotkeyFolders": [], "ConfigItemFolders": [{"Name": "MOUTH", "FileCount": 2, "Color": {"r": 1.0, "g": 0.988235354423523, "b": 0.9333333969116211, "a": 1.0}}]}, "SavedActiveExpressions": []}