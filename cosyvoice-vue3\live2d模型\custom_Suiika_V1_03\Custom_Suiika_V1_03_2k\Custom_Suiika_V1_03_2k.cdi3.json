{"Version": 3, "Parameters": [{"Id": "Param22", "GroupId": "ParamGroup6", "Name": "---------FACE--------"}, {"Id": "Param15", "GroupId": "ParamGroup6", "Name": "FACE Maturity"}, {"Id": "Param37", "GroupId": "ParamGroup6", "Name": "---------SKIN COLOR--------"}, {"Id": "Param125", "GroupId": "ParamGroup6", "Name": "<PERSON>"}, {"Id": "Param126", "GroupId": "ParamGroup6", "Name": "Skin Saturation"}, {"Id": "Param127", "GroupId": "ParamGroup6", "Name": "Skin Color Darkness"}, {"Id": "Param487", "GroupId": "ParamGroup6", "Name": "Skin Shine Intensity"}, {"Id": "Param20", "GroupId": "ParamGroup6", "Name": "---------EYES--------"}, {"Id": "Param16", "GroupId": "ParamGroup6", "Name": "EYES Size Vertical"}, {"Id": "Param345", "GroupId": "ParamGroup6", "Name": "EYES Size Horizontal"}, {"Id": "Param346", "GroupId": "ParamGroup6", "Name": "EYES Shape"}, {"Id": "Param303", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param477", "GroupId": "ParamGroup6", "Name": "Reactive Pupils (linked to brows)"}, {"Id": "Param116", "GroupId": "ParamGroup6", "Name": "---------EYE L COLOR--------"}, {"Id": "Param117", "GroupId": "ParamGroup6", "Name": "<PERSON> L"}, {"Id": "Param122", "GroupId": "ParamGroup6", "Name": "Iris Color Saturation L"}, {"Id": "Param123", "GroupId": "ParamGroup6", "Name": "Iris Color Darkness L"}, {"Id": "Param143", "GroupId": "ParamGroup6", "Name": "Pupil Hue L"}, {"Id": "Param478", "GroupId": "ParamGroup6", "Name": "Pupil Saturation L"}, {"Id": "Param486", "GroupId": "ParamGroup6", "Name": "Pupil Darkness L"}, {"Id": "Param119", "GroupId": "ParamGroup6", "Name": "Pupil Opacity L"}, {"Id": "Param151", "GroupId": "ParamGroup6", "Name": "Eyewhite Hue L"}, {"Id": "Param152", "GroupId": "ParamGroup6", "Name": "Eyewhite Saturation L"}, {"Id": "Param153", "GroupId": "ParamGroup6", "Name": "Eyewhite Darkness L"}, {"Id": "Param118", "GroupId": "ParamGroup6", "Name": "---------EYE R COLOR--------"}, {"Id": "Param124", "GroupId": "ParamGroup6", "Name": "<PERSON>"}, {"Id": "Param242", "GroupId": "ParamGroup6", "Name": "Iris Color Saturation R"}, {"Id": "Param243", "GroupId": "ParamGroup6", "Name": "Iris Color Darkness R"}, {"Id": "Param249", "GroupId": "ParamGroup6", "Name": "Pupil Opacity R"}, {"Id": "Param547", "GroupId": "ParamGroup6", "Name": "Pupil hue R"}, {"Id": "Param548", "GroupId": "ParamGroup6", "Name": "Pupil Saturation R"}, {"Id": "Param549", "GroupId": "ParamGroup6", "Name": "Pupil Darkness R"}, {"Id": "Param250", "GroupId": "ParamGroup6", "Name": "Eyewhite Hue R"}, {"Id": "Param251", "GroupId": "ParamGroup6", "Name": "Eyewhite Saturation R"}, {"Id": "Param252", "GroupId": "ParamGroup6", "Name": "Eyewhite Darkness R"}, {"Id": "Param21", "GroupId": "ParamGroup6", "Name": "---------BROWS--------"}, {"Id": "Param17", "GroupId": "ParamGroup6", "Name": "BROW thickness"}, {"Id": "Param18", "GroupId": "ParamGroup6", "Name": "BROW length"}, {"Id": "Param209", "GroupId": "ParamGroup6", "Name": "--------BROWS COLOR--------"}, {"Id": "Param211", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON>"}, {"Id": "Param210", "GroupId": "ParamGroup6", "Name": "Brows Saturation"}, {"Id": "Param212", "GroupId": "ParamGroup6", "Name": "Brows Darkness"}, {"Id": "Param24", "GroupId": "ParamGroup6", "Name": "---------DETAILS--------"}, {"Id": "Param295", "GroupId": "ParamGroup6", "Name": "<PERSON>le Eye"}, {"Id": "Param296", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON>"}, {"Id": "Param277", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param102", "GroupId": "ParamGroup6", "Name": "Whiskers"}, {"Id": "Param481", "GroupId": "ParamGroup6", "Name": "---------DETAILS COLOR--------"}, {"Id": "Param88", "GroupId": "ParamGroup6", "Name": "Details Hue"}, {"Id": "Param479", "GroupId": "ParamGroup6", "Name": "Details Saturation"}, {"Id": "Param480", "GroupId": "ParamGroup6", "Name": "Details Darkness"}, {"Id": "Param482", "GroupId": "ParamGroup6", "Name": "---------INNER MOUTH--------"}, {"Id": "Param308", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON>"}, {"Id": "Param483", "GroupId": "ParamGroup6", "Name": "Inner Mouth Hue"}, {"Id": "Param484", "GroupId": "ParamGroup6", "Name": "Inner Mouth Saturation"}, {"Id": "Param485", "GroupId": "ParamGroup6", "Name": "Inner Mouth Darkness"}, {"Id": "Param130", "GroupId": "ParamGroup6", "Name": "---------MAKEUP-------"}, {"Id": "Param131", "GroupId": "ParamGroup6", "Name": "Eye Shadow Upper Hue"}, {"Id": "Param134", "GroupId": "ParamGroup6", "Name": "Eye Shadow Upper Saturation"}, {"Id": "Param135", "GroupId": "ParamGroup6", "Name": "Eye Shadow Upper Darkness"}, {"Id": "Param136", "GroupId": "ParamGroup6", "Name": "----------------"}, {"Id": "Param138", "GroupId": "ParamGroup6", "Name": "Eye Shadow Lower Hue"}, {"Id": "Param140", "GroupId": "ParamGroup6", "Name": "Eye Shadow Lower Saturation"}, {"Id": "Param141", "GroupId": "ParamGroup6", "Name": "Eye Shadow Lower Darkness"}, {"Id": "Param139", "GroupId": "ParamGroup6", "Name": "----------------"}, {"Id": "Param120", "GroupId": "ParamGroup6", "Name": "Eyeliner Hue"}, {"Id": "Param132", "GroupId": "ParamGroup6", "Name": "Eyeliner Saturation"}, {"Id": "Param133", "GroupId": "ParamGroup6", "Name": "Eyeliner Darkness"}, {"Id": "Param137", "GroupId": "ParamGroup6", "Name": "----------------"}, {"Id": "Param115", "GroupId": "ParamGroup6", "Name": "Eyelashes Darkness/Brightness"}, {"Id": "Param142", "GroupId": "ParamGroup6", "Name": "----------------"}, {"Id": "Param145", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON>"}, {"Id": "Param146", "GroupId": "ParamGroup6", "Name": "Blush Saturation"}, {"Id": "Param147", "GroupId": "ParamGroup6", "Name": "Blush Darkness"}, {"Id": "Param144", "GroupId": "ParamGroup6", "Name": "----------------"}, {"Id": "Param113", "GroupId": "ParamGroup6", "Name": "Lipstick Hue"}, {"Id": "Param84", "GroupId": "ParamGroup6", "Name": "Lipstick Saturation"}, {"Id": "Param114", "GroupId": "ParamGroup6", "Name": "Lipstick Darkness"}, {"Id": "Param32", "GroupId": "ParamGroup6", "Name": "---------EARS--------"}, {"Id": "Param33", "GroupId": "ParamGroup6", "Name": "Human Ears"}, {"Id": "Param34", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON>"}, {"Id": "Param289", "GroupId": "ParamGroup6", "Name": "<PERSON> Ears"}, {"Id": "Param292", "GroupId": "ParamGroup6", "Name": "<PERSON>"}, {"Id": "Param293", "GroupId": "ParamGroup6", "Name": "Dog Ears"}, {"Id": "Param294", "GroupId": "ParamGroup6", "Name": "<PERSON> Ears"}, {"Id": "Param351", "GroupId": "ParamGroup6", "Name": "------ANIMAL EARS COLOR L-----"}, {"Id": "Param80", "GroupId": "ParamGroup6", "Name": "Animal Ear Inner Hue L"}, {"Id": "Param82", "GroupId": "ParamGroup6", "Name": "Animal Inner Saturation L"}, {"Id": "Param83", "GroupId": "ParamGroup6", "Name": "Animal Inner Darkness L"}, {"Id": "Param444", "GroupId": "ParamGroup6", "Name": "------ANIMAL EARS COLOR R-----"}, {"Id": "Param81", "GroupId": "ParamGroup6", "Name": "Animal Ear Inner Hue R"}, {"Id": "Param353", "GroupId": "ParamGroup6", "Name": "Animal Inner Saturation R"}, {"Id": "Param354", "GroupId": "ParamGroup6", "Name": "Animal Inner Darkness R"}, {"Id": "Param350", "GroupId": "ParamGroup6", "Name": "---------HORNS--------"}, {"Id": "Param275", "GroupId": "ParamGroup6", "Name": "Horns Small"}, {"Id": "Param276", "GroupId": "ParamGroup6", "Name": "Horns Big"}, {"Id": "Param443", "GroupId": "ParamGroup6", "Name": "---------HORNS COLOR--------"}, {"Id": "Param53", "GroupId": "ParamGroup6", "Name": "<PERSON><PERSON> Hue"}, {"Id": "Param54", "GroupId": "ParamGroup6", "Name": "Horns Saturation"}, {"Id": "Param59", "GroupId": "ParamGroup6", "Name": "Horns Darkness"}, {"Id": "Param78", "GroupId": "ParamGroup6", "Name": "Horns Contrast"}, {"Id": "Param323", "GroupId": "ParamGroup8", "Name": "---------BODY EDITOR--------"}, {"Id": "Param57", "GroupId": "ParamGroup8", "Name": "<PERSON><PERSON>"}, {"Id": "Param322", "GroupId": "ParamGroup8", "Name": "---------MOLES BODY--------"}, {"Id": "Param320", "GroupId": "ParamGroup8", "Name": "<PERSON><PERSON> Chest"}, {"Id": "Param324", "GroupId": "ParamGroup8", "Name": "<PERSON><PERSON>"}, {"Id": "Param325", "GroupId": "ParamGroup8", "Name": "<PERSON>le Thigh"}, {"Id": "Param121", "GroupId": "ParamGroup8", "Name": "---------NAILS--------"}, {"Id": "Param128", "GroupId": "ParamGroup8", "Name": "<PERSON><PERSON>"}, {"Id": "Param148", "GroupId": "ParamGroup8", "Name": "Nail Saturation"}, {"Id": "Param149", "GroupId": "ParamGroup8", "Name": "Nail <PERSON>"}, {"Id": "Param103", "GroupId": "ParamGroup3", "Name": "---------HAIRSTYLES--------"}, {"Id": "Param64", "GroupId": "ParamGroup3", "Name": "---------BANGS--------"}, {"Id": "Param52", "GroupId": "ParamGroup3", "Name": "Bang Middle (1 +2)"}, {"Id": "Param55", "GroupId": "ParamGroup3", "Name": "Bangs Straight across"}, {"Id": "Param56", "GroupId": "ParamGroup3", "Name": "<PERSON>s Middle Parting"}, {"Id": "Param71", "GroupId": "ParamGroup3", "Name": "<PERSON>s Sideswept"}, {"Id": "Param58", "GroupId": "ParamGroup3", "Name": "<PERSON>s Over Eyes"}, {"Id": "Param491", "GroupId": "ParamGroup3", "Name": "Bangs Over Eyes V2"}, {"Id": "Param65", "GroupId": "ParamGroup3", "Name": "---------FRAME HAIR--------"}, {"Id": "Param72", "GroupId": "ParamGroup3", "Name": "Frame HAIR 1"}, {"Id": "Param340", "GroupId": "ParamGroup3", "Name": "Frame Hair Straight"}, {"Id": "Param6", "GroupId": "ParamGroup3", "Name": "Frame Hair Cheeks"}, {"Id": "Param46", "GroupId": "ParamGroup3", "Name": "Frame HAIR Wavy"}, {"Id": "Param67", "GroupId": "ParamGroup3", "Name": "---------SIDE HAIR--------"}, {"Id": "Param48", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param240", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param241", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param489", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param524", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param66", "GroupId": "ParamGroup3", "Name": "---------BACK HAIR--------"}, {"Id": "Param61", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param47", "GroupId": "ParamGroup3", "Name": "Backhead"}, {"Id": "Param60", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param63", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param75", "GroupId": "ParamGroup3", "Name": "Ponytail"}, {"Id": "ParamBraid", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "ParamBraid3", "GroupId": "ParamGroup3", "Name": "Over Body Twintails"}, {"Id": "ParamBraid4", "GroupId": "ParamGroup3", "Name": "Side Swept Twintail"}, {"Id": "Param533", "GroupId": "ParamGroup3", "Name": "---------HAIR ADD ONS--------"}, {"Id": "Param68", "GroupId": "ParamGroup3", "Name": "Hairbuns (high)"}, {"Id": "Param79", "GroupId": "ParamGroup3", "Name": "Hairbuns (low)"}, {"Id": "Param76", "GroupId": "ParamGroup3", "Name": "Twintails (short + straight)"}, {"Id": "ParamBraid2", "GroupId": "ParamGroup3", "Name": "Twintail (long + wavy)"}, {"Id": "Param129", "GroupId": "ParamGroup3", "Name": "---------AHOGES--------"}, {"Id": "Param176", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param326", "GroupId": "ParamGroup3", "Name": "<PERSON>og<PERSON> Heart"}, {"Id": "Param327", "GroupId": "ParamGroup3", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param26", "GroupId": "ParamGroup3", "Name": "---------HAIR COLOR L--------"}, {"Id": "Param", "GroupId": "ParamGroup3", "Name": "hair hue L"}, {"Id": "Param4", "GroupId": "ParamGroup3", "Name": "hair color saturation L"}, {"Id": "Param5", "GroupId": "ParamGroup3", "Name": "hair color darkness L"}, {"Id": "Param2", "GroupId": "ParamGroup3", "Name": "hair contrast L"}, {"Id": "Param344", "GroupId": "ParamGroup3", "Name": "---------HAIR COLOR R--------"}, {"Id": "Param3", "GroupId": "ParamGroup3", "Name": "hair hue R"}, {"Id": "Param341", "GroupId": "ParamGroup3", "Name": "hair color saturation R"}, {"Id": "Param342", "GroupId": "ParamGroup3", "Name": "hair color darkness R"}, {"Id": "Param343", "GroupId": "ParamGroup3", "Name": "hair contrast R"}, {"Id": "Param453", "GroupId": "ParamGroup3", "Name": "---------HAIR BOW COLOR --------"}, {"Id": "Param19", "GroupId": "ParamGroup3", "Name": "Hair Bow Hue"}, {"Id": "Param348", "GroupId": "ParamGroup3", "Name": "Hair Bow Saturation"}, {"Id": "Param454", "GroupId": "ParamGroup3", "Name": "Hair Bow Darkness"}, {"Id": "Param38", "GroupId": "ParamGroup5", "Name": "---------CLOTHING-------"}, {"Id": "Param309", "GroupId": "ParamGroup5", "Name": "-----------TOPS----------"}, {"Id": "Param7", "GroupId": "ParamGroup5", "Name": "<PERSON>"}, {"Id": "Param8", "GroupId": "ParamGroup5", "Name": "Lowcut Top"}, {"Id": "Paramchestbelt3", "GroupId": "ParamGroup5", "Name": "Lowcut Top Lace"}, {"Id": "Param9", "GroupId": "ParamGroup5", "Name": "Tube Top (Chest)"}, {"Id": "Paramchestbelt2", "GroupId": "ParamGroup5", "Name": "Tube Top (Torso)"}, {"Id": "Param10", "GroupId": "ParamGroup5", "Name": "Tube Top Ruffles"}, {"Id": "Paramchestbelt4", "GroupId": "ParamGroup5", "Name": "Corset"}, {"Id": "Param36", "GroupId": "ParamGroup5", "Name": "<PERSON>igan"}, {"Id": "Param522", "GroupId": "ParamGroup5", "Name": "Cardigan Fluffy"}, {"Id": "Param534", "GroupId": "ParamGroup5", "Name": "-----------TOPS ADD ONS----------"}, {"Id": "Param29", "GroupId": "ParamGroup5", "Name": "Spaghetti Straps"}, {"Id": "Param523", "GroupId": "ParamGroup5", "Name": "Spaghetti Straps Cross"}, {"Id": "Param30", "GroupId": "ParamGroup5", "Name": "Shoulder Belt Straps"}, {"Id": "Param31", "GroupId": "ParamGroup5", "Name": "Belt Strap Ruffle"}, {"Id": "Paramchestbelt", "GroupId": "ParamGroup5", "Name": "Chest Belt"}, {"Id": "Param62", "GroupId": "ParamGroup5", "Name": "<PERSON><PERSON>"}, {"Id": "Param150", "GroupId": "ParamGroup5", "Name": "-------TOPS COLOR------"}, {"Id": "Param244", "GroupId": "ParamGroup5", "Name": "Top Hue"}, {"Id": "Param245", "GroupId": "ParamGroup5", "Name": "Top Saturation"}, {"Id": "Param246", "GroupId": "ParamGroup5", "Name": "Top Darkness"}, {"Id": "Param543", "GroupId": "ParamGroup5", "Name": "-------TOP STRAPS COLOR------"}, {"Id": "Param544", "GroupId": "ParamGroup5", "Name": "Top Straps Hue"}, {"Id": "Param545", "GroupId": "ParamGroup5", "Name": "Top Straps Saturation"}, {"Id": "Param546", "GroupId": "ParamGroup5", "Name": "Top Straps Darkness"}, {"Id": "Param459", "GroupId": "ParamGroup5", "Name": "-------TOP DETAILS COLOR------"}, {"Id": "Param460", "GroupId": "ParamGroup5", "Name": "Top Detail Hue"}, {"Id": "Param461", "GroupId": "ParamGroup5", "Name": "Top Detail Saturation"}, {"Id": "Param462", "GroupId": "ParamGroup5", "Name": "Top Detail Darkness"}, {"Id": "Param525", "GroupId": "ParamGroup5", "Name": "-------CARDIGAN COLOR------"}, {"Id": "Param526", "GroupId": "ParamGroup5", "Name": "Cardigan hue"}, {"Id": "Param527", "GroupId": "ParamGroup5", "Name": "Cardigan Saturation"}, {"Id": "Param528", "GroupId": "ParamGroup5", "Name": "Cardigan Darkness"}, {"Id": "Param532", "GroupId": "ParamGroup5", "Name": "-------CARDIGAN FLUFF COLOR------"}, {"Id": "Param529", "GroupId": "ParamGroup5", "Name": "Cardigan Flu<PERSON>"}, {"Id": "Param530", "GroupId": "ParamGroup5", "Name": "Cardigan Fluff Saturation"}, {"Id": "Param531", "GroupId": "ParamGroup5", "Name": "Cardigan Fluff Darkness"}, {"Id": "Param28", "GroupId": "ParamGroup5", "Name": "-----------BOTTOMS----------"}, {"Id": "Param495", "GroupId": "ParamGroup5", "Name": "Ruffled Skirt"}, {"Id": "Param177", "GroupId": "ParamGroup5", "Name": "Flared Skirt"}, {"Id": "Paramchestbelt5", "GroupId": "ParamGroup5", "Name": "<PERSON><PERSON><PERSON> Skirt"}, {"Id": "Param90", "GroupId": "ParamGroup5", "Name": "Shorts"}, {"Id": "Param321", "GroupId": "ParamGroup5", "Name": "Shorts Cross Stitching"}, {"Id": "Param112", "GroupId": "ParamGroup5", "Name": "Shorts Sporty"}, {"Id": "Param175", "GroupId": "ParamGroup5", "Name": "<PERSON>s <PERSON>"}, {"Id": "Param111", "GroupId": "ParamGroup5", "Name": "Leotard"}, {"Id": "Param476", "GroupId": "ParamGroup5", "Name": "<PERSON><PERSON><PERSON> (dont turn off when on twitch)"}, {"Id": "Param247", "GroupId": "ParamGroup5", "Name": "-------BOTTOMS COLOR------"}, {"Id": "Param248", "GroupId": "ParamGroup5", "Name": "Bottom Hue"}, {"Id": "Param253", "GroupId": "ParamGroup5", "Name": "Bottom Saturation"}, {"Id": "Param254", "GroupId": "ParamGroup5", "Name": "Bottom Darkness"}, {"Id": "Param455", "GroupId": "ParamGroup5", "Name": "-------BOTTOMS DETAILS COLOR------"}, {"Id": "Param456", "GroupId": "ParamGroup5", "Name": "Bottom Detail <PERSON>"}, {"Id": "Param457", "GroupId": "ParamGroup5", "Name": "Bottom Detail Saturation"}, {"Id": "Param458", "GroupId": "ParamGroup5", "Name": "Bottom Detail Darkness"}, {"Id": "Param89", "GroupId": "ParamGroup5", "Name": "-----------CHOKERS----------"}, {"Id": "Param40", "GroupId": "ParamGroup5", "Name": "<PERSON>"}, {"Id": "Param44", "GroupId": "ParamGroup5", "Name": "Neck Bow"}, {"Id": "Param41", "GroupId": "ParamGroup5", "Name": "Heart Choker"}, {"Id": "Param45", "GroupId": "ParamGroup5", "Name": "Heart Choker Chain"}, {"Id": "Param42", "GroupId": "ParamGroup5", "Name": "Bell Choker"}, {"Id": "Param43", "GroupId": "ParamGroup5", "Name": "<PERSON>"}, {"Id": "Param255", "GroupId": "ParamGroup5", "Name": "-------CHOKERS COLOR------"}, {"Id": "Param256", "GroupId": "ParamGroup5", "Name": "Chokers Hue"}, {"Id": "Param288", "GroupId": "ParamGroup5", "Name": "Chokers Saturation"}, {"Id": "Param347", "GroupId": "ParamGroup5", "Name": "Chokers Darkness"}, {"Id": "Param39", "GroupId": "ParamGroup5", "Name": "-------SOCKS------"}, {"Id": "Param12", "GroupId": "ParamGroup5", "Name": "Thigh High Socks"}, {"Id": "Param13", "GroupId": "ParamGroup5", "Name": "Tights"}, {"Id": "Param14", "GroupId": "ParamGroup5", "Name": "Fishnet"}, {"Id": "Param11", "GroupId": "ParamGroup5", "Name": "Sock Holes"}, {"Id": "Param449", "GroupId": "ParamGroup5", "Name": "-------SOCKS COLOR------"}, {"Id": "Param446", "GroupId": "ParamGroup5", "Name": "Socks Hue"}, {"Id": "Param447", "GroupId": "ParamGroup5", "Name": "Socks Saturation"}, {"Id": "Param448", "GroupId": "ParamGroup5", "Name": "Socks Darkness"}, {"Id": "Param504", "GroupId": "ParamGroup5", "Name": "-------<PERSON><PERSON> WARMERS------"}, {"Id": "Param501", "GroupId": "ParamGroup5", "Name": "Leg Warmers"}, {"Id": "Param502", "GroupId": "ParamGroup5", "Name": "Leg Warmers Bow"}, {"Id": "Param503", "GroupId": "ParamGroup5", "Name": "Leg Warmers Bow Charm"}, {"Id": "Param352", "GroupId": "ParamGroup5", "Name": "-------<PERSON><PERSON> WARMER COLOR------"}, {"Id": "Param505", "GroupId": "ParamGroup5", "Name": "<PERSON><PERSON>"}, {"Id": "Param506", "GroupId": "ParamGroup5", "Name": "Leg Warmer Saturation"}, {"Id": "Param507", "GroupId": "ParamGroup5", "Name": "Leg Warmer Darkness"}, {"Id": "Param511", "GroupId": "ParamGroup5", "Name": "-------<PERSON><PERSON> WARMER BOW COLOR------"}, {"Id": "Param509", "GroupId": "ParamGroup5", "Name": "<PERSON><PERSON>er <PERSON>"}, {"Id": "Param508", "GroupId": "ParamGroup5", "Name": "Leg Warmer Bow Saturation"}, {"Id": "Param510", "GroupId": "ParamGroup5", "Name": "Leg Warmer <PERSON>"}, {"Id": "Param392", "GroupId": "ParamGroup5", "Name": "-------SHOES------"}, {"Id": "Param355", "GroupId": "ParamGroup5", "Name": "Boots"}, {"Id": "Param356", "GroupId": "ParamGroup5", "Name": "Pumps"}, {"Id": "Param403", "GroupId": "ParamGroup5", "Name": "-------SHOES COLOR------"}, {"Id": "Param450", "GroupId": "ParamGroup5", "Name": "Shoes Hue"}, {"Id": "Param451", "GroupId": "ParamGroup5", "Name": "Shoes Saturation"}, {"Id": "Param452", "GroupId": "ParamGroup5", "Name": "Shoes Darkness"}, {"Id": "Param35", "GroupId": "ParamGroup10", "Name": "---------ACCESSORIES--------"}, {"Id": "Param73", "GroupId": "ParamGroup10", "Name": "Glasses Round"}, {"Id": "Param490", "GroupId": "ParamGroup10", "Name": "Glasses Frame Sides"}, {"Id": "Param463", "GroupId": "ParamGroup10", "Name": "-------GLASSES  COLOR------"}, {"Id": "Param464", "GroupId": "ParamGroup10", "Name": "<PERSON><PERSON>"}, {"Id": "Param465", "GroupId": "ParamGroup10", "Name": "Glasses Saturation"}, {"Id": "Param466", "GroupId": "ParamGroup10", "Name": "Glasses Darkness"}, {"Id": "Param77", "GroupId": "ParamGroup10", "Name": "-----------------------------"}, {"Id": "Param85", "GroupId": "ParamGroup10", "Name": "Hairband"}, {"Id": "Param86", "GroupId": "ParamGroup10", "Name": "Hairband Bow"}, {"Id": "Param87", "GroupId": "ParamGroup10", "Name": "Hairband Dangly Charm"}, {"Id": "Param468", "GroupId": "ParamGroup10", "Name": "-------HAIRBAND  COLOR------"}, {"Id": "Param469", "GroupId": "ParamGroup10", "Name": "Hairband Hue"}, {"Id": "Param470", "GroupId": "ParamGroup10", "Name": "Hairband Saturation"}, {"Id": "Param471", "GroupId": "ParamGroup10", "Name": "Hairband Darkness"}, {"Id": "Param472", "GroupId": "ParamGroup10", "Name": "-------HAIRBAND  BOW COLOR------"}, {"Id": "Param473", "GroupId": "ParamGroup10", "Name": "Hairband Bow Hue"}, {"Id": "Param474", "GroupId": "ParamGroup10", "Name": "Hairband Bow Saturation"}, {"Id": "Param475", "GroupId": "ParamGroup10", "Name": "Hairband Bow Darkness"}, {"Id": "Param467", "GroupId": "ParamGroup10", "Name": "-------EAR RINGS-----"}, {"Id": "Param384", "GroupId": "ParamGroup10", "Name": "Star Earrings"}, {"Id": "Param395", "GroupId": "ParamGroup10", "Name": "---------TAILS---------"}, {"Id": "Param426", "GroupId": "ParamGroup10", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param427", "GroupId": "ParamGroup10", "Name": "<PERSON>"}, {"Id": "Param429", "GroupId": "ParamGroup10", "Name": "Tail Up"}, {"Id": "Param422", "GroupId": "ParamGroup10", "Name": "---------TAILS COLOR--------"}, {"Id": "Param423", "GroupId": "ParamGroup10", "Name": "Tails hue"}, {"Id": "Param424", "GroupId": "ParamGroup10", "Name": "Tails Saturation"}, {"Id": "Param428", "GroupId": "ParamGroup10", "Name": "Tails Darkness"}, {"Id": "Param425", "GroupId": "ParamGroup10", "Name": "Tails Contrast"}, {"Id": "Param445", "GroupId": "ParamGroup10", "Name": "---------WINGS--------"}, {"Id": "Param394", "GroupId": "ParamGroup10", "Name": "Bat Wings"}, {"Id": "Param393", "GroupId": "ParamGroup10", "Name": "Wings Contrast"}, {"Id": "Param366", "GroupId": "ParamGroup10", "Name": "---------ARMS---------"}, {"Id": "Param367", "GroupId": "ParamGroup10", "Name": "Wave Arm L"}, {"Id": "Param368", "GroupId": "ParamGroup10", "Name": "Wave Arm R"}, {"Id": "Param371", "GroupId": "ParamGroup10", "Name": "Shy <PERSON> L"}, {"Id": "Param372", "GroupId": "ParamGroup10", "Name": "<PERSON><PERSON> <PERSON>"}, {"Id": "Param442", "GroupId": "ParamGroup9", "Name": "---------EXPRESSIONS--------"}, {"Id": "Param69", "GroupId": "ParamGroup9", "Name": "Pleading"}, {"Id": "Param74", "GroupId": "ParamGroup9", "Name": "Blank Eyes"}, {"Id": "Param70", "GroupId": "ParamGroup9", "Name": "Dark Circles"}, {"Id": "Param297", "GroupId": "ParamGroup9", "Name": "Face Shadow"}, {"Id": "Param290", "GroupId": "ParamGroup9", "Name": "Stars"}, {"Id": "Param304", "GroupId": "ParamGroup9", "Name": "Hearts"}, {"Id": "Param305", "GroupId": "ParamGroup9", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param430", "GroupId": "ParamGroup9", "Name": "<PERSON><PERSON>"}, {"Id": "Param23", "GroupId": "", "Name": "-------RIGGING-------"}, {"Id": "Param50", "GroupId": "", "Name": "DO NOT EDIT FROM THIS POINT PLEASE"}, {"Id": "Param51", "GroupId": "", "Name": "---------------------"}, {"Id": "AngleX", "GroupId": "", "Name": "Angle X"}, {"Id": "AngleY", "GroupId": "", "Name": "Angle Y"}, {"Id": "AngleZ", "GroupId": "", "Name": "<PERSON><PERSON> Z"}, {"Id": "Param27", "GroupId": "", "Name": "---------------------"}, {"Id": "Param92", "GroupId": "", "Name": "Body X"}, {"Id": "ParamBodyAngleY2", "GroupId": "", "Name": "Body Y"}, {"Id": "ParamBodyAngleZ", "GroupId": "", "Name": "Body Z"}, {"Id": "ParamBreath", "GroupId": "", "Name": "Breath"}, {"Id": "Param91", "GroupId": "", "Name": "----------INPUTS-----------"}, {"Id": "ParamBodyAngleX", "GroupId": "", "Name": "Body X"}, {"Id": "Param93", "GroupId": "", "Name": "Body Y"}, {"Id": "Param94", "GroupId": "", "Name": "Body Z"}, {"Id": "Param154", "GroupId": "", "Name": "Body Height"}, {"Id": "Param550", "GroupId": "", "Name": "FACE Mesh"}, {"Id": "Param370", "GroupId": "ParamGroup13", "Name": "---------------------"}, {"Id": "ParamBodyAngleZ2", "GroupId": "ParamGroup13", "Name": "Hip Sway"}, {"Id": "ParamBodyAngleZ3", "GroupId": "ParamGroup13", "Name": "Shoudler<PERSON>way"}, {"Id": "ParamBodyAngleX3", "GroupId": "ParamGroup13", "Name": "Upper Body Position X"}, {"Id": "Param369", "GroupId": "ParamGroup13", "Name": "---------------------"}, {"Id": "Param107", "GroupId": "ParamGroup13", "Name": "Boobs Y1"}, {"Id": "Param108", "GroupId": "ParamGroup13", "Name": "Boobs Y2"}, {"Id": "Param109", "GroupId": "ParamGroup13", "Name": "Boobs X1"}, {"Id": "Param110", "GroupId": "ParamGroup13", "Name": "Boobs X2"}, {"Id": "Param213", "GroupId": "ParamGroup13", "Name": "---------------------"}, {"Id": "Param214", "GroupId": "ParamGroup13", "Name": "Elf Ear 1"}, {"Id": "Param215", "GroupId": "ParamGroup13", "Name": "Elf Ear 2"}, {"Id": "Param216", "GroupId": "ParamGroup13", "Name": "Elf Ear 3"}, {"Id": "Param217", "GroupId": "ParamGroup13", "Name": "---------------------"}, {"Id": "Param357", "GroupId": "ParamGroup12", "Name": "---------------------"}, {"Id": "Param104", "GroupId": "ParamGroup12", "Name": "Upper Arm L"}, {"Id": "Param105", "GroupId": "ParamGroup12", "Name": "Lower Arm L"}, {"Id": "Param106", "GroupId": "ParamGroup12", "Name": "Hand L"}, {"Id": "Param358", "GroupId": "ParamGroup12", "Name": "---------------------"}, {"Id": "Param431", "GroupId": "ParamGroup12", "Name": "Upper Arm R"}, {"Id": "Param432", "GroupId": "ParamGroup12", "Name": "Lower Arm R"}, {"Id": "Param433", "GroupId": "ParamGroup12", "Name": "Hand R"}, {"Id": "Param434", "GroupId": "ParamGroup12", "Name": "---------------------"}, {"Id": "Param359", "GroupId": "ParamGroup12", "Name": "Thumb"}, {"Id": "Param360", "GroupId": "ParamGroup12", "Name": "Thumb2"}, {"Id": "Param364", "GroupId": "ParamGroup12", "Name": "Index1"}, {"Id": "Param365", "GroupId": "ParamGroup12", "Name": "Index2"}, {"Id": "Param361", "GroupId": "ParamGroup12", "Name": "Finger 1"}, {"Id": "Param362", "GroupId": "ParamGroup12", "Name": "Finger 2"}, {"Id": "Param363", "GroupId": "ParamGroup12", "Name": "Finger 3"}, {"Id": "Param400", "GroupId": "ParamGroup12", "Name": "---------------------"}, {"Id": "Param401", "GroupId": "ParamGroup12", "Name": "Wave Arm 1"}, {"Id": "Param402", "GroupId": "ParamGroup12", "Name": "Wave Arm 2"}, {"Id": "Param412", "GroupId": "ParamGroup12", "Name": "---------------------"}, {"Id": "Param413", "GroupId": "ParamGroup12", "Name": "Shy Arm 1 L"}, {"Id": "Param414", "GroupId": "ParamGroup12", "Name": "Shy Arm 2 L"}, {"Id": "Param415", "GroupId": "ParamGroup12", "Name": "Shy Arm 1R"}, {"Id": "Param416", "GroupId": "ParamGroup12", "Name": "Shy Arm 2R"}, {"Id": "ParamEyeLOpen", "GroupId": "ParamGroup4", "Name": "EyeL Open"}, {"Id": "ParamEyeLSmile", "GroupId": "ParamGroup4", "Name": "Eyes Smile"}, {"Id": "ParamEyeROpen", "GroupId": "ParamGroup4", "Name": "EyeR Open"}, {"Id": "ParamEyeBallX", "GroupId": "ParamGroup4", "Name": "Eyeball X"}, {"Id": "ParamEyeBallY", "GroupId": "ParamGroup4", "Name": "Eyeball Y"}, {"Id": "ParamBrowLY", "GroupId": "ParamGroup14", "Name": "BrowL Y"}, {"Id": "ParamBrowRY", "GroupId": "ParamGroup14", "Name": "BrowR Y"}, {"Id": "ParamBrowLAngle", "GroupId": "ParamGroup14", "Name": "BrowL Angle"}, {"Id": "ParamBrowRAngle", "GroupId": "ParamGroup14", "Name": "BrowR Angle"}, {"Id": "ParamBrowLForm", "GroupId": "ParamGroup14", "Name": "BrowL Form"}, {"Id": "ParamBrowRForm", "GroupId": "ParamGroup14", "Name": "BrowR Form"}, {"Id": "ParamMouthForm", "GroupId": "ParamGroup11", "Name": "Mouth Form"}, {"Id": "ParamMouthOpenY", "GroupId": "ParamGroup11", "Name": "Mouth Open"}, {"Id": "Param95", "GroupId": "ParamGroup11", "Name": "Mouth Funnel"}, {"Id": "Param98", "GroupId": "ParamGroup11", "Name": "Mouth Press Lip Open"}, {"Id": "Param96", "GroupId": "ParamGroup11", "Name": "Mouth Shrug"}, {"Id": "Param99", "GroupId": "ParamGroup11", "Name": "<PERSON> Pucker <PERSON>n"}, {"Id": "Param97", "GroupId": "ParamGroup11", "Name": "Jaw Open"}, {"Id": "Param101", "GroupId": "ParamGroup11", "Name": "Mouth X"}, {"Id": "ParamCheek", "GroupId": "ParamGroup11", "Name": "Cheek"}, {"Id": "Param100", "GroupId": "ParamGroup11", "Name": "Tongue Out"}, {"Id": "Param291", "GroupId": "ParamGroup16", "Name": "<PERSON> <PERSON><PERSON>"}, {"Id": "Param306", "GroupId": "ParamGroup16", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "Param310", "GroupId": "ParamGroup16", "Name": "Star"}, {"Id": "Param311", "GroupId": "ParamGroup16", "Name": "Hearts 1"}, {"Id": "Param312", "GroupId": "ParamGroup16", "Name": "Hearts 2"}, {"Id": "Param313", "GroupId": "ParamGroup16", "Name": "Hearts 3"}, {"Id": "Param224", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param49", "GroupId": "ParamGroup7", "Name": "<PERSON><PERSON> Flicker"}, {"Id": "Param300", "GroupId": "ParamGroup7", "Name": "Eyeshine Sparkle"}, {"Id": "Param301", "GroupId": "ParamGroup7", "Name": "Eyeshine Sparkle2"}, {"Id": "Param302", "GroupId": "ParamGroup7", "Name": "Eyeshine Sparkle3"}, {"Id": "Param287", "GroupId": "ParamGroup7", "Name": "Pupils Cat"}, {"Id": "Param298", "GroupId": "ParamGroup7", "Name": "Pleading Shine 1"}, {"Id": "Param299", "GroupId": "ParamGroup7", "Name": "Pleading Shine 2"}, {"Id": "Param307", "GroupId": "ParamGroup7", "Name": "Starry Eye"}, {"Id": "Param226", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param218", "GroupId": "ParamGroup7", "Name": "Hair Bow L1"}, {"Id": "Param219", "GroupId": "ParamGroup7", "Name": "Hair Bow L2"}, {"Id": "Param220", "GroupId": "ParamGroup7", "Name": "Hair Bow L3"}, {"Id": "Param225", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param221", "GroupId": "ParamGroup7", "Name": "Hair Bow R1"}, {"Id": "Param222", "GroupId": "ParamGroup7", "Name": "Hair Bow R2"}, {"Id": "Param223", "GroupId": "ParamGroup7", "Name": "Hair Bow R3"}, {"Id": "Param265", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param268", "GroupId": "ParamGroup7", "Name": "Bow L1"}, {"Id": "Param271", "GroupId": "ParamGroup7", "Name": "Bow L2"}, {"Id": "Param272", "GroupId": "ParamGroup7", "Name": "Bow L3"}, {"Id": "Param270", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param267", "GroupId": "ParamGroup7", "Name": "Bow R1"}, {"Id": "Param273", "GroupId": "ParamGroup7", "Name": "Bow R2"}, {"Id": "Param274", "GroupId": "ParamGroup7", "Name": "Bow R3"}, {"Id": "Param269", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_hairband_chain", "GroupId": "ParamGroup7", "Name": "[0]hairband_chain"}, {"Id": "Param_Angle_Rotation_2_hairband_chain", "GroupId": "ParamGroup7", "Name": "[1]hairband_chain"}, {"Id": "Param_Angle_Rotation_3_hairband_chain", "GroupId": "ParamGroup7", "Name": "[2]hairband_chain"}, {"Id": "Param266", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_corset_pendant", "GroupId": "ParamGroup7", "Name": "[0]corset_pendant"}, {"Id": "Param_Angle_Rotation_2_corset_pendant", "GroupId": "ParamGroup7", "Name": "[1]corset_pendant"}, {"Id": "Param_Angle_Rotation_3_corset_pendant", "GroupId": "ParamGroup7", "Name": "[2]corset_pendant"}, {"Id": "Param282", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param283", "GroupId": "ParamGroup7", "Name": "Cat Ear 1"}, {"Id": "Param284", "GroupId": "ParamGroup7", "Name": "Cat Ear 2"}, {"Id": "Param285", "GroupId": "ParamGroup7", "Name": "Cat Ear 3"}, {"Id": "Param286", "GroupId": "ParamGroup7", "Name": "<PERSON>"}, {"Id": "Param314", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param315", "GroupId": "ParamGroup7", "Name": "Bell X"}, {"Id": "Param316", "GroupId": "ParamGroup7", "Name": "Bell Y"}, {"Id": "Param317", "GroupId": "ParamGroup7", "Name": "Choker Pendant 1"}, {"Id": "Param318", "GroupId": "ParamGroup7", "Name": "Choker Pendant 2"}, {"Id": "Param319", "GroupId": "ParamGroup7", "Name": "Choker Pendant 3"}, {"Id": "Param333", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param334", "GroupId": "ParamGroup7", "Name": "Sprout 1"}, {"Id": "Param335", "GroupId": "ParamGroup7", "Name": "Sprout 2"}, {"Id": "Param336", "GroupId": "ParamGroup7", "Name": "Sprout 3"}, {"Id": "Param339", "GroupId": "ParamGroup7", "Name": "Sprout 4"}, {"Id": "Param337", "GroupId": "ParamGroup7", "Name": "Waterdrop 1"}, {"Id": "Param338", "GroupId": "ParamGroup7", "Name": "Waterdrop 2"}, {"Id": "Param373", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param377", "GroupId": "ParamGroup7", "Name": "SkirtX"}, {"Id": "Param496", "GroupId": "ParamGroup7", "Name": "SkirtX2"}, {"Id": "Param374", "GroupId": "ParamGroup7", "Name": "Skirt1"}, {"Id": "Param375", "GroupId": "ParamGroup7", "Name": "Skirt2"}, {"Id": "Param376", "GroupId": "ParamGroup7", "Name": "Skirt3"}, {"Id": "Param378", "GroupId": "ParamGroup7", "Name": "Skirt4"}, {"Id": "Param379", "GroupId": "ParamGroup7", "Name": "Skirt5"}, {"Id": "Param380", "GroupId": "ParamGroup7", "Name": "Skirt6"}, {"Id": "Param382", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_chain3", "GroupId": "ParamGroup7", "Name": "Earring L1"}, {"Id": "Param_Angle_Rotation_1_chain2", "GroupId": "ParamGroup7", "Name": "Earring L2"}, {"Id": "Param_Angle_Rotation_2_chain2", "GroupId": "ParamGroup7", "Name": "Earring L3"}, {"Id": "Param_Angle_Rotation_3_chain2", "GroupId": "ParamGroup7", "Name": "Earring L4"}, {"Id": "Param_Angle_Rotation_3_chain3", "GroupId": "ParamGroup7", "Name": "Earring L5"}, {"Id": "Param383", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_chain4", "GroupId": "ParamGroup7", "Name": "Earring R1"}, {"Id": "Param_Angle_Rotation_1_chain5", "GroupId": "ParamGroup7", "Name": "Earring R2"}, {"Id": "Param_Angle_Rotation_1_chain6", "GroupId": "ParamGroup7", "Name": "Earring R3"}, {"Id": "Param_Angle_Rotation_1_chain7", "GroupId": "ParamGroup7", "Name": "Earring R4"}, {"Id": "Param_Angle_Rotation_1_chain8", "GroupId": "ParamGroup7", "Name": "Earring R5"}, {"Id": "Param385", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param386", "GroupId": "ParamGroup7", "Name": "Shoulder Ruffle 1"}, {"Id": "Param387", "GroupId": "ParamGroup7", "Name": "Shoulder Ruffle 2"}, {"Id": "Param488", "GroupId": "ParamGroup7", "Name": "Shoulder Ruffle 3"}, {"Id": "Param388", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param389", "GroupId": "ParamGroup7", "Name": "Wing 1"}, {"Id": "Param390", "GroupId": "ParamGroup7", "Name": "Wing 2"}, {"Id": "Param391", "GroupId": "ParamGroup7", "Name": "Wing 3"}, {"Id": "Param396", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param397", "GroupId": "ParamGroup7", "Name": "Necklace 1"}, {"Id": "Param398", "GroupId": "ParamGroup7", "Name": "Necklace 2"}, {"Id": "Param399", "GroupId": "ParamGroup7", "Name": "Necklace 3"}, {"Id": "Param404", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param405", "GroupId": "ParamGroup7", "Name": "Babydoll Skirt X1"}, {"Id": "Param406", "GroupId": "ParamGroup7", "Name": "Babydoll Skirt X2"}, {"Id": "Param407", "GroupId": "ParamGroup7", "Name": "Babydoll Skirt X3"}, {"Id": "Param408", "GroupId": "ParamGroup7", "Name": "Babydoll Skirt X4"}, {"Id": "Param409", "GroupId": "ParamGroup7", "Name": "Babydoll Skirt Y1"}, {"Id": "Param410", "GroupId": "ParamGroup7", "Name": "Babydoll Skirt Y2"}, {"Id": "Param411", "GroupId": "ParamGroup7", "Name": "Babydoll Skirt Y3"}, {"Id": "Param417", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param418", "GroupId": "ParamGroup7", "Name": "Neck Bow1"}, {"Id": "Param419", "GroupId": "ParamGroup7", "Name": "Neck Bow2"}, {"Id": "Param436", "GroupId": "ParamGroup7", "Name": "Neck Bow3"}, {"Id": "Param435", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_shorts_stringL", "GroupId": "ParamGroup7", "Name": "[0]shorts_stringL"}, {"Id": "Param_Angle_Rotation_2_shorts_stringL", "GroupId": "ParamGroup7", "Name": "[1]shorts_stringL"}, {"Id": "Param_Angle_Rotation_3_shorts_stringL", "GroupId": "ParamGroup7", "Name": "[2]shorts_stringL"}, {"Id": "Param_Angle_Rotation_4_shorts_stringL", "GroupId": "ParamGroup7", "Name": "[3]shorts_stringL"}, {"Id": "Param_Angle_Rotation_5_shorts_stringL", "GroupId": "ParamGroup7", "Name": "[4]shorts_stringL"}, {"Id": "Param437", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param438", "GroupId": "ParamGroup7", "Name": "Stars X"}, {"Id": "Param439", "GroupId": "ParamGroup7", "Name": "Stars Y"}, {"Id": "Param440", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_tail_fluffy", "GroupId": "ParamGroup7", "Name": "[0]tail_fluffy"}, {"Id": "Param_Angle_Rotation_2_tail_fluffy", "GroupId": "ParamGroup7", "Name": "[1]tail_fluffy"}, {"Id": "Param_Angle_Rotation_3_tail_fluffy", "GroupId": "ParamGroup7", "Name": "[2]tail_fluffy"}, {"Id": "Param_Angle_Rotation_4_tail_fluffy", "GroupId": "ParamGroup7", "Name": "[3]tail_fluffy"}, {"Id": "Param_Angle_Rotation_5_tail_fluffy", "GroupId": "ParamGroup7", "Name": "[4]tail_fluffy"}, {"Id": "Param_Angle_Rotation_6_tail_fluffy", "GroupId": "ParamGroup7", "Name": "[5]tail_fluffy"}, {"Id": "Param_Angle_Rotation_7_tail_fluffy", "GroupId": "ParamGroup7", "Name": "[6]tail_fluffy"}, {"Id": "Param441", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_tail_straight", "GroupId": "ParamGroup7", "Name": "[0]tail_straight"}, {"Id": "Param_Angle_Rotation_2_tail_straight", "GroupId": "ParamGroup7", "Name": "[1]tail_straight"}, {"Id": "Param_Angle_Rotation_3_tail_straight", "GroupId": "ParamGroup7", "Name": "[2]tail_straight"}, {"Id": "Param_Angle_Rotation_4_tail_straight", "GroupId": "ParamGroup7", "Name": "[3]tail_straight"}, {"Id": "Param_Angle_Rotation_5_tail_straight", "GroupId": "ParamGroup7", "Name": "[4]tail_straight"}, {"Id": "Param_Angle_Rotation_6_tail_straight", "GroupId": "ParamGroup7", "Name": "[5]tail_straight"}, {"Id": "Param_Angle_Rotation_7_tail_straight", "GroupId": "ParamGroup7", "Name": "[6]tail_straight"}, {"Id": "Param492", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param493", "GroupId": "ParamGroup7", "Name": "Shoe Zipper 1"}, {"Id": "Param494", "GroupId": "ParamGroup7", "Name": "S<PERSON> Flap"}, {"Id": "Param497", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param498", "GroupId": "ParamGroup7", "Name": "Skirt Bow 1"}, {"Id": "Param499", "GroupId": "ParamGroup7", "Name": "Skirt Bow 2"}, {"Id": "Param500", "GroupId": "ParamGroup7", "Name": "Skirt Bow 3"}, {"Id": "Param512", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param513", "GroupId": "ParamGroup7", "Name": "Leg Warmers X"}, {"Id": "Param514", "GroupId": "ParamGroup7", "Name": "Leg Warmers Y"}, {"Id": "Param515", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param516", "GroupId": "ParamGroup7", "Name": "Wave Sleeve 1"}, {"Id": "Param517", "GroupId": "ParamGroup7", "Name": "Wave Sleeve 2"}, {"Id": "Param519", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param518", "GroupId": "ParamGroup7", "Name": "Shy Sleeve 1"}, {"Id": "Param520", "GroupId": "ParamGroup7", "Name": "Shy Sleeve 2"}, {"Id": "Param521", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_cardigan_string2", "GroupId": "ParamGroup7", "Name": "[0]cardigan_string"}, {"Id": "Param_Angle_Rotation_2_cardigan_string2", "GroupId": "ParamGroup7", "Name": "[1]cardigan_string"}, {"Id": "Param_Angle_Rotation_3_cardigan_string2", "GroupId": "ParamGroup7", "Name": "[2]cardigan_string"}, {"Id": "Param_Angle_Rotation_3_cardigan_string3", "GroupId": "ParamGroup7", "Name": "[2]cardigan_string2"}, {"Id": "Param536", "GroupId": "ParamGroup7", "Name": "---------------------"}, {"Id": "Param535", "GroupId": "ParamGroup7", "Name": "Cardigan 1"}, {"Id": "Param537", "GroupId": "ParamGroup7", "Name": "Cardigan 2"}, {"Id": "Param538", "GroupId": "ParamGroup7", "Name": "Cardigan 3"}, {"Id": "Param539", "GroupId": "ParamGroup7", "Name": "Cardigan 4"}, {"Id": "Param540", "GroupId": "ParamGroup7", "Name": "Cardigan 5"}, {"Id": "Param541", "GroupId": "ParamGroup7", "Name": "Cardigan 6"}, {"Id": "Param542", "GroupId": "ParamGroup7", "Name": "Cardigan 7"}, {"Id": "Param155", "GroupId": "ParamGroup", "Name": "Bang Mid X1"}, {"Id": "Param156", "GroupId": "ParamGroup", "Name": "Bang Mid X2"}, {"Id": "Param157", "GroupId": "ParamGroup", "Name": "Bang Mid X3"}, {"Id": "Param161", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param206", "GroupId": "ParamGroup", "Name": "Small Strand 1"}, {"Id": "Param207", "GroupId": "ParamGroup", "Name": "Small Strand 2"}, {"Id": "Param208", "GroupId": "ParamGroup", "Name": "Small Strand 3"}, {"Id": "Param205", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param168", "GroupId": "ParamGroup", "Name": "Bang R1"}, {"Id": "Param169", "GroupId": "ParamGroup", "Name": "Bang R2"}, {"Id": "Param170", "GroupId": "ParamGroup", "Name": "Bang R3"}, {"Id": "Param167", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param172", "GroupId": "ParamGroup", "Name": "Bang L1"}, {"Id": "Param173", "GroupId": "ParamGroup", "Name": "Bang L2"}, {"Id": "Param174", "GroupId": "ParamGroup", "Name": "Bang L3"}, {"Id": "Param260", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param257", "GroupId": "ParamGroup", "Name": "Bang L1 (weaker)"}, {"Id": "Param258", "GroupId": "ParamGroup", "Name": "Bang L2 (weaker)"}, {"Id": "Param259", "GroupId": "ParamGroup", "Name": "Bang L3 (weaker)"}, {"Id": "Param261", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param262", "GroupId": "ParamGroup", "Name": "Bang R1 (weaker)2"}, {"Id": "Param263", "GroupId": "ParamGroup", "Name": "Bang R2 (weaker)2"}, {"Id": "Param264", "GroupId": "ParamGroup", "Name": "Bang R3 (weaker)2"}, {"Id": "Param171", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param197", "GroupId": "ParamGroup", "Name": "Bang L1 (stronger)"}, {"Id": "Param198", "GroupId": "ParamGroup", "Name": "Bang L2 (stronger)"}, {"Id": "Param199", "GroupId": "ParamGroup", "Name": "Bang L3 (stronger)"}, {"Id": "Param238", "GroupId": "ParamGroup", "Name": "Bang L4 (stronger)"}, {"Id": "Param204", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param201", "GroupId": "ParamGroup", "Name": "Bang R1 (stronger)"}, {"Id": "Param202", "GroupId": "ParamGroup", "Name": "Bang R2 (stronger)"}, {"Id": "Param203", "GroupId": "ParamGroup", "Name": "Bang R3 (stronger)"}, {"Id": "Param239", "GroupId": "ParamGroup", "Name": "Bang R4 (stronger)"}, {"Id": "Param200", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param158", "GroupId": "ParamGroup", "Name": "Side Hair L1"}, {"Id": "Param159", "GroupId": "ParamGroup", "Name": "Side Hair L2"}, {"Id": "Param160", "GroupId": "ParamGroup", "Name": "Side Hair L3"}, {"Id": "Param193", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param194", "GroupId": "ParamGroup", "Name": "Side Hair R1"}, {"Id": "Param195", "GroupId": "ParamGroup", "Name": "Side Hair R2"}, {"Id": "Param196", "GroupId": "ParamGroup", "Name": "Side Hair R3"}, {"Id": "Param162", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param178", "GroupId": "ParamGroup", "Name": "Back Hair L (front1)"}, {"Id": "Param179", "GroupId": "ParamGroup", "Name": "Back Hair L (front2)"}, {"Id": "Param180", "GroupId": "ParamGroup", "Name": "Back Hair L (front3)"}, {"Id": "Param181", "GroupId": "ParamGroup", "Name": "Back Hair L (front4)"}, {"Id": "Param182", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param163", "GroupId": "ParamGroup", "Name": "Back Hair L1"}, {"Id": "Param164", "GroupId": "ParamGroup", "Name": "Back Hair L2"}, {"Id": "Param165", "GroupId": "ParamGroup", "Name": "Back Hair L3"}, {"Id": "Param166", "GroupId": "ParamGroup", "Name": "Back Hair L4"}, {"Id": "Param183", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param184", "GroupId": "ParamGroup", "Name": "Back Hair R (front1)"}, {"Id": "Param185", "GroupId": "ParamGroup", "Name": "Back Hair R (front2)"}, {"Id": "Param186", "GroupId": "ParamGroup", "Name": "Back Hair R (front3)"}, {"Id": "Param187", "GroupId": "ParamGroup", "Name": "Back Hair R (front4)"}, {"Id": "Param188", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param189", "GroupId": "ParamGroup", "Name": "Back Hair R1"}, {"Id": "Param190", "GroupId": "ParamGroup", "Name": "Back Hair R2"}, {"Id": "Param191", "GroupId": "ParamGroup", "Name": "Back Hair R3"}, {"Id": "Param192", "GroupId": "ParamGroup", "Name": "Back Hair R4"}, {"Id": "Param227", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param228", "GroupId": "ParamGroup", "Name": "Twintail (long) L1"}, {"Id": "Param229", "GroupId": "ParamGroup", "Name": "Twintail (long) L2"}, {"Id": "Param230", "GroupId": "ParamGroup", "Name": "Twintail (long) L3"}, {"Id": "Param231", "GroupId": "ParamGroup", "Name": "Twintail (long) L4"}, {"Id": "Param232", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param233", "GroupId": "ParamGroup", "Name": "Twintail (long) R1"}, {"Id": "Param234", "GroupId": "ParamGroup", "Name": "Twintail (long) R2"}, {"Id": "Param235", "GroupId": "ParamGroup", "Name": "Twintail (long) R3"}, {"Id": "Param236", "GroupId": "ParamGroup", "Name": "Twintail (long) R4"}, {"Id": "Param278", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param25", "GroupId": "ParamGroup", "Name": "Ponytail L1"}, {"Id": "Param279", "GroupId": "ParamGroup", "Name": "Ponytail L2"}, {"Id": "Param280", "GroupId": "ParamGroup", "Name": "Ponytail L3"}, {"Id": "Param281", "GroupId": "ParamGroup", "Name": "Ponytail L4"}, {"Id": "Param329", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param330", "GroupId": "ParamGroup", "Name": "Ahoge 1"}, {"Id": "Param331", "GroupId": "ParamGroup", "Name": "Ahoge 2"}, {"Id": "Param332", "GroupId": "ParamGroup", "Name": "Ahoge 3"}, {"Id": "Param328", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy1", "GroupId": "ParamGroup", "Name": "[0]twintail_wavy1L"}, {"Id": "Param_Angle_Rotation_2_twintail_wavy1", "GroupId": "ParamGroup", "Name": "[1]twintail_wavy1L"}, {"Id": "Param_Angle_Rotation_3_twintail_wavy1", "GroupId": "ParamGroup", "Name": "[2]twintail_wavy1L"}, {"Id": "Param_Angle_Rotation_4_twintail_wavy1", "GroupId": "ParamGroup", "Name": "[3]twintail_wavy1L"}, {"Id": "Param_Angle_Rotation_5_twintail_wavy1", "GroupId": "ParamGroup", "Name": "[4]twintail_wavy1L"}, {"Id": "Param_Angle_Rotation_6_twintail_wavy1", "GroupId": "ParamGroup", "Name": "[5]twintail_wavy1L"}, {"Id": "Param_Angle_Rotation_7_twintail_wavy1", "GroupId": "ParamGroup", "Name": "[6]twintail_wavy1L"}, {"Id": "Param237", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_2_twintail_wavy3", "GroupId": "ParamGroup", "Name": "[0]twintail_wavy2L"}, {"Id": "Param_Angle_Rotation_2_twintail_wavy2", "GroupId": "ParamGroup", "Name": "[1]twintail_wavy2L"}, {"Id": "Param_Angle_Rotation_3_twintail_wavy2", "GroupId": "ParamGroup", "Name": "[2]twintail_wavy2L"}, {"Id": "Param_Angle_Rotation_4_twintail_wavy2", "GroupId": "ParamGroup", "Name": "[3]twintail_wavy2L"}, {"Id": "Param_Angle_Rotation_5_twintail_wavy2", "GroupId": "ParamGroup", "Name": "[4]twintail_wavy2L"}, {"Id": "Param_Angle_Rotation_6_twintail_wavy2", "GroupId": "ParamGroup", "Name": "[5]twintail_wavy2L"}, {"Id": "Param_Angle_Rotation_7_twintail_wavy2", "GroupId": "ParamGroup", "Name": "[6]twintail_wavy2L"}, {"Id": "Param420", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy2", "GroupId": "ParamGroup", "Name": "[0]twintail_wavy1R"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy3", "GroupId": "ParamGroup", "Name": "[1]twintail_wavy1R"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy4", "GroupId": "ParamGroup", "Name": "[2]twintail_wavy1R"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy5", "GroupId": "ParamGroup", "Name": "[3]twintail_wavy1R"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy6", "GroupId": "ParamGroup", "Name": "[4]twintail_wavy1R"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy7", "GroupId": "ParamGroup", "Name": "[5]twintail_wavy1R"}, {"Id": "Param_Angle_Rotation_1_twintail_wavy8", "GroupId": "ParamGroup", "Name": "[6]twintail_wavy1R"}, {"Id": "Param349", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_2_twintail_wavy4", "GroupId": "ParamGroup", "Name": "[0]twintail_wavy2R"}, {"Id": "Param_Angle_Rotation_2_twintail_wavy5", "GroupId": "ParamGroup", "Name": "[1]twintail_wavy2R"}, {"Id": "Param_Angle_Rotation_3_twintail_wavy3", "GroupId": "ParamGroup", "Name": "[2]twintail_wavy2R"}, {"Id": "Param_Angle_Rotation_4_twintail_wavy3", "GroupId": "ParamGroup", "Name": "[3]twintail_wavy2R"}, {"Id": "Param_Angle_Rotation_5_twintail_wavy3", "GroupId": "ParamGroup", "Name": "[4]twintail_wavy2R"}, {"Id": "Param_Angle_Rotation_6_twintail_wavy3", "GroupId": "ParamGroup", "Name": "[5]twintail_wavy2R"}, {"Id": "Param_Angle_Rotation_7_twintail_wavy3", "GroupId": "ParamGroup", "Name": "[6]twintail_wavy2R"}, {"Id": "Param421", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_ponytail5", "GroupId": "ParamGroup", "Name": "[0]ponytailL"}, {"Id": "Param_Angle_Rotation_2_ponytail5", "GroupId": "ParamGroup", "Name": "[1]ponytailL"}, {"Id": "Param_Angle_Rotation_3_ponytail5", "GroupId": "ParamGroup", "Name": "[2]ponytailL"}, {"Id": "Param_Angle_Rotation_4_ponytail5", "GroupId": "ParamGroup", "Name": "[3]ponytailL"}, {"Id": "Param_Angle_Rotation_5_ponytail5", "GroupId": "ParamGroup", "Name": "[4]ponytailL"}, {"Id": "Param_Angle_Rotation_6_ponytail5", "GroupId": "ParamGroup", "Name": "[5]ponytailL"}, {"Id": "Param_Angle_Rotation_7_ponytail5", "GroupId": "ParamGroup", "Name": "[6]ponytailL"}, {"Id": "Param381", "GroupId": "ParamGroup", "Name": "---------------------"}, {"Id": "Param_Angle_Rotation_1_ponytail9", "GroupId": "ParamGroup", "Name": "[0]ponytailR"}, {"Id": "Param_Angle_Rotation_2_ponytail9", "GroupId": "ParamGroup", "Name": "[1]ponytailR"}, {"Id": "Param_Angle_Rotation_3_ponytail9", "GroupId": "ParamGroup", "Name": "[2]ponytailR"}, {"Id": "Param_Angle_Rotation_4_ponytail9", "GroupId": "ParamGroup", "Name": "[3]ponytailR"}, {"Id": "Param_Angle_Rotation_5_ponytail9", "GroupId": "ParamGroup", "Name": "[4]ponytailR"}, {"Id": "Param_Angle_Rotation_6_ponytail9", "GroupId": "ParamGroup", "Name": "[5]ponytailR"}, {"Id": "Param_Angle_Rotation_7_ponytail9", "GroupId": "ParamGroup", "Name": "[6]ponytail<PERSON>"}], "ParameterGroups": [{"Id": "ParamGroup6", "GroupId": "", "Name": "FACE EDITOR"}, {"Id": "ParamGroup8", "GroupId": "", "Name": "BODY EDITOR"}, {"Id": "ParamGroup3", "GroupId": "", "Name": "HAIR TOGGLES"}, {"Id": "ParamGroup5", "GroupId": "", "Name": "OUTFIT TOGGLES"}, {"Id": "ParamGroup10", "GroupId": "", "Name": "TOGGLES"}, {"Id": "ParamGroup9", "GroupId": "", "Name": "EXPRESSIONS"}, {"Id": "ParamGroup13", "GroupId": "", "Name": "BODY PHYSICS"}, {"Id": "ParamGroup12", "GroupId": "", "Name": "ARMS"}, {"Id": "ParamGroup4", "GroupId": "", "Name": "EYES"}, {"Id": "ParamGroup14", "GroupId": "", "Name": "BROWS"}, {"Id": "ParamGroup11", "GroupId": "", "Name": "MOUTH"}, {"Id": "ParamGroup16", "GroupId": "", "Name": "ANIMATIONS"}, {"Id": "ParamGroup7", "GroupId": "", "Name": "PHYSICS"}, {"Id": "ParamGroup", "GroupId": "", "Name": "HAIR PHYSICS"}], "Parts": [{"Id": "Part59", "Name": "customizable vtuber model changes.psd"}, {"Id": "Part235", "Name": "customizable vtuber model changes.psd"}, {"Id": "Part225", "Name": "customizable vtuber model changes.psd"}, {"Id": "Part215", "Name": "customizable vtuber model changes.psd (Corresponding layer not found)"}, {"Id": "Part203", "Name": "customizable vtuber model changes.psd"}, {"Id": "Part192", "Name": "HEARTS"}, {"Id": "Part183", "Name": "mouth forms.psd"}, {"Id": "Part190", "Name": "SPARKLES"}, {"Id": "Part", "Name": "HAIRBAND BOW"}, {"Id": "Part28", "Name": "MID BANGS OVER FRAME HAIR OPTIONS"}, {"Id": "Part61", "Name": "ROUND GLASSES"}, {"Id": "Part42", "Name": "customizable vtuber model changes.psd"}, {"Id": "Part55", "Name": "sideburns, neck hair"}, {"Id": "Part329", "Name": "customizable vtuber model ref.psd"}, {"Id": "Part2", "Name": "DOG EAR FRONT"}, {"Id": "Part25", "Name": "EYEBROW L"}, {"Id": "Part86", "Name": "FRAME HAIR 1 (small strands)"}, {"Id": "Part188", "Name": "ABOVE EYES"}, {"Id": "Part4", "Name": "SMALL HORNS"}, {"Id": "Part7", "Name": "HAIR CLIPS"}, {"Id": "Part18", "Name": "ARM OPTIONS"}, {"Id": "Part27", "Name": "BOW TWINTAIL HIGH"}, {"Id": "Part31", "Name": "FRAME HAIR OPTIONS"}, {"Id": "HAIRBAND", "Name": "HAIRBAND"}, {"Id": "Part52", "Name": "BANG OPTIONS"}, {"Id": "Part40", "Name": "BOB SIDE HAIR"}, {"Id": "Part48", "Name": "AHOGE OPTIONS"}, {"Id": "MOLES2", "Name": "MOLES"}, {"Id": "FACE", "Name": "FACE"}, {"Id": "Part174", "Name": "BOW TWINTAIL HIGH"}, {"Id": "Part32", "Name": "HAIR ROOT SPLIT"}, {"Id": "Part331", "Name": "UNDER HAIR LAYERS"}, {"Id": "Part87", "Name": "SIDE HAIR OPTIONS"}, {"Id": "Part103", "Name": "RAM HORN"}, {"Id": "Part107", "Name": "OVERBODY TWINTAIL BOW L"}, {"Id": "Part104", "Name": "OVER BODY HAIRSTYLES"}, {"Id": "Part231", "Name": "CARDIGAN FRONT"}, {"Id": "Part350", "Name": "COQUETTE OUTFIT"}, {"Id": "Part109", "Name": "BOOBS BIG OUTFIT OPTIONS"}, {"Id": "Part115", "Name": "BOOBS FLAT OUTFIT OPTIONS"}, {"Id": "Part122", "Name": "NECKLACES FRONT"}, {"Id": "Part129", "Name": "STRAP OPTIONS"}, {"Id": "Part134", "Name": "BOOBS BIG SKIN"}, {"Id": "Part137", "Name": "BOOBS FLAT SKIN"}, {"Id": "ARMPIT", "Name": "ARMPIT"}, {"Id": "SHORTS", "Name": "SHORTS"}, {"Id": "Part146", "Name": "HAND ON HIP BACK"}, {"Id": "Part44", "Name": "HAIR BUNS LOW"}, {"Id": "Part147", "Name": "UPPER TORSO SKIN"}, {"Id": "NECK", "Name": "NECK"}, {"Id": "Part148", "Name": "TORSO"}, {"Id": "LEGS", "Name": "LEGS"}, {"Id": "Part155", "Name": "BACK SKIRT"}, {"Id": "Part158", "Name": "BODY SKIN FILL"}, {"Id": "Part204", "Name": "ARM R"}, {"Id": "Part156", "Name": "ARM L"}, {"Id": "ARMPITS", "Name": "ARMPITS"}, {"Id": "Part159", "Name": "NECKLACES BACK"}, {"Id": "Part330", "Name": "BELT STRAP BACK"}, {"Id": "Part162", "Name": "BAT WINGS"}, {"Id": "Part171", "Name": "BACK HAIR OPTIONS"}, {"Id": "PONYTAIL", "Name": "PONYTAIL"}, {"Id": "TAILS", "Name": "TAILS"}, {"Id": "Part224", "Name": "SHOUDLER RUFFLES"}, {"Id": "pleading2", "Name": "pleading"}, {"Id": "hairband_chain_Skinning", "Name": "hairband_chain(Skinning)"}, {"Id": "Part26", "Name": "hairband_chain(Rotation)"}, {"Id": "Part29", "Name": "middle bang straight"}, {"Id": "Part30", "Name": "middle bang long"}, {"Id": "PartSketch0", "Name": "[ Guide Image]"}, {"Id": "Part3", "Name": "DOG EAR CONTRAST"}, {"Id": "Part5", "Name": "small horn L"}, {"Id": "Part8", "Name": "BIG FLOWER CLIP"}, {"Id": "Part9", "Name": "FLOWER CLIP 1"}, {"Id": "Part10", "Name": "FLOWER CLIP 2"}, {"Id": "Part11", "Name": "FLOWER CLIP 3"}, {"Id": "Part12", "Name": "HEART CLIP"}, {"Id": "Part13", "Name": "BOW CLIP"}, {"Id": "Part14", "Name": "CLIP 2"}, {"Id": "Part15", "Name": "CLIP 1"}, {"Id": "Part16", "Name": "ROUND CLIP 1"}, {"Id": "Part17", "Name": "ROUND CLIP 2"}, {"Id": "Part19", "Name": "WAVE ARM L"}, {"Id": "Part20", "Name": "PEN ARM L"}, {"Id": "Part22", "Name": "HIP ARM L"}, {"Id": "Part24", "Name": "SHY ARM L"}, {"Id": "Part34", "Name": "FRONT HAIR OVER EYES"}, {"Id": "Part227", "Name": "GLASSES ROUND (BANGS OVER EYES)"}, {"Id": "Part228", "Name": "GLASSES SHINE (BANGS OVER EYES)"}, {"Id": "Part198", "Name": "FRAME HAIR LONG"}, {"Id": "Part35", "Name": "FRAME HAIR WAVY"}, {"Id": "Part46", "Name": "FRAME HAIR STRAIGHT"}, {"Id": "Part38", "Name": "FRAME HAIR 1"}, {"Id": "Part39", "Name": "FRAME HAIR CHEEKS"}, {"Id": "Part237", "Name": "SIDE HAIR RBAID"}, {"Id": "Part53", "Name": "BANG MIDDLE"}, {"Id": "Part390", "Name": "BANGS STRAIGHT"}, {"Id": "Part391", "Name": "BANGS SIDE"}, {"Id": "Part58", "Name": "BANG MIDDLE PARTING"}, {"Id": "SPROUT", "Name": "SPROUT"}, {"Id": "Part49", "Name": "AHOGE STRAIGHT"}, {"Id": "Part50", "Name": "AHOGE HEART"}, {"Id": "Part51", "Name": "AHOGE SMALL"}, {"Id": "Part163", "Name": "ANIMAL EARS"}, {"Id": "Part196", "Name": "cheek whiskers"}, {"Id": "EXPRESSIONS", "Name": "EXPRESSIONS"}, {"Id": "Part221", "Name": "EYE R"}, {"Id": "Part62", "Name": "EYE L"}, {"Id": "NOSE", "Name": "NOSE"}, {"Id": "Part54", "Name": "<PERSON><PERSON> (tuck)"}, {"Id": "Part88", "Name": "STRAIGHT BANGS BACK HAIR"}, {"Id": "Part334", "Name": "BOB SIDE HAIR"}, {"Id": "Part80", "Name": "FACE SKIN"}, {"Id": "Part45", "Name": "HAIR ROOT SPLIT"}, {"Id": "EARS", "Name": "EARS"}, {"Id": "Part89", "Name": "BOB SIDE STRANDS"}, {"Id": "Part90", "Name": "SHORT SIDE HAIR"}, {"Id": "Part336", "Name": "WAVY SIDE HAIR"}, {"Id": "Part91", "Name": "WAVY SIDE HAIR"}, {"Id": "Part92", "Name": "MEDIUM SIDE HAIR"}, {"Id": "hair", "Name": "hair"}, {"Id": "Part105", "Name": "OVER BODY BRAID"}, {"Id": "Part106", "Name": "FRONT TWINTAIL"}, {"Id": "cardigan_string2_Skinning", "Name": "cardigan_string(Skinning)"}, {"Id": "Part236", "Name": "cardigan_string(Rotation)"}, {"Id": "bow", "Name": "bow"}, {"Id": "Part110", "Name": "CHEST STRAP"}, {"Id": "Part111", "Name": "BUNNY CUPS BIG"}, {"Id": "Part113", "Name": "TUBE TOP BIG"}, {"Id": "Part114", "Name": "LOW CUT TOP BIG"}, {"Id": "Part116", "Name": "CHEST STRAP"}, {"Id": "Part117", "Name": "BUNNY CUPS FLAT"}, {"Id": "Part120", "Name": "TUBE TOP FLAT"}, {"Id": "Part121", "Name": "LOWCUT TOP FLAT"}, {"Id": "Part123", "Name": "NECK BOW"}, {"Id": "Part124", "Name": "THIN CHOKER"}, {"Id": "Part125", "Name": "HEART CHOKER"}, {"Id": "Part127", "Name": "Choker Bell"}, {"Id": "Part128", "Name": "PEARL NECKLACE FRONT"}, {"Id": "Part130", "Name": "SHOULDER BELT STRAP L"}, {"Id": "Part133", "Name": "SPAGHETTI STRAPS"}, {"Id": "Part135", "Name": "BIG BOOB LACES"}, {"Id": "Part136", "Name": "big boob skin"}, {"Id": "CORSET", "Name": "CORSET"}, {"Id": "Part141", "Name": "BABYDOLL DRESS"}, {"Id": "Part138", "Name": "flat boob skin"}, {"Id": "Part182", "Name": "shorts bow"}, {"Id": "Part47", "Name": "BUN BOWS LOW"}, {"Id": "COLLARBONES", "Name": "COLLARBONES"}, {"Id": "Part56", "Name": "NECK HAIR"}, {"Id": "Part226", "Name": "SOCKS"}, {"Id": "Part149", "Name": "LEG R"}, {"Id": "Part151", "Name": "LEG L"}, {"Id": "Part153", "Name": "BUTT FANGS"}, {"Id": "UNDEFILLS", "Name": "UNDEFILLS"}, {"Id": "Part157", "Name": "PUFF SLEEVE"}, {"Id": "Part354", "Name": "Cardigan Sleeve L"}, {"Id": "HAND", "Name": "HAND"}, {"Id": "Part191", "Name": "ARM L"}, {"Id": "Part160", "Name": "CHOKER BACK"}, {"Id": "Part161", "Name": "PEARLS BACK"}, {"Id": "Part172", "Name": "HIGH BUN"}, {"Id": "Part181", "Name": "TWINTAIL SHORT L"}, {"Id": "Part177", "Name": "TWINTAILS WAVY"}, {"Id": "Part37", "Name": "BRAID R"}, {"Id": "Part180", "Name": "BRAID SHORT L"}, {"Id": "Part175", "Name": "BACK HAIR STRAIGHT"}, {"Id": "Part178", "Name": "BACK HEAD"}, {"Id": "Part57", "Name": "BACK NECK HAIR"}, {"Id": "Part179", "Name": "BACK HAIR WAVY L"}, {"Id": "Part185", "Name": "BOB HAIR L"}, {"Id": "Part186", "Name": "FRONT TWINTAILS BACK"}, {"Id": "Part187", "Name": "SIDE SWEPT BACK HAIR"}, {"Id": "BOW3", "Name": "BOW"}, {"Id": "Part189", "Name": "PONYTAIL HAIR"}, {"Id": "tail_fluffy_contrast_Skinning", "Name": "tail_fluffy_contrast(Skinning)"}, {"Id": "tail_fluffy_Skinning", "Name": "tail_fluffy(Skinning)"}, {"Id": "Part217", "Name": "tail_fluffy(Rotation)"}, {"Id": "SUCCUBUS", "Name": "SUCCUBUS"}, {"Id": "tail_straight_contrast_Skinning", "Name": "tail_straight_contrast(Skinning)"}, {"Id": "tail_straight_Skinning", "Name": "tail_straight(Skin<PERSON>)"}, {"Id": "Part218", "Name": "tail_straight(Rotation)"}, {"Id": "Part6", "Name": "SMALL HORN CONTRAST"}, {"Id": "Part21", "Name": "PEN NAILS"}, {"Id": "hand", "Name": "hand"}, {"Id": "Part23", "Name": "INDEX FINGER"}, {"Id": "Part36", "Name": "FRAME HAIR WAVY CONTRAST"}, {"Id": "Part333", "Name": "FRAME HAIR L"}, {"Id": "Part332", "Name": "FRAME HAIR R"}, {"Id": "Part199", "Name": "CAT EAR"}, {"Id": "Part164", "Name": "DOG EAR BACK"}, {"Id": "Part165", "Name": "FOX EAR"}, {"Id": "Part167", "Name": "BUNNY EAR"}, {"Id": "Part169", "Name": "CAT EAR"}, {"Id": "pleading", "Name": "pleading"}, {"Id": "Part63", "Name": "UPPER LASHES"}, {"Id": "Part64", "Name": "UPPER LASHLINE"}, {"Id": "Part65", "Name": "LOWER LASHES"}, {"Id": "Part66", "Name": "LOWER EYERIM"}, {"Id": "HIGHLIGHTS", "Name": "HIGHLIGHTS"}, {"Id": "Part68", "Name": "ALT PUPILS"}, {"Id": "IRIS", "Name": "IRIS"}, {"Id": "Part70", "Name": "UPPER EYELID"}, {"Id": "makeup", "Name": "makeup"}, {"Id": "EARRINGS", "Name": "EARRINGS"}, {"Id": "freckles", "Name": "freckles"}, {"Id": "Part219", "Name": "blush lines"}, {"Id": "MOUTH", "Name": "MOUTH"}, {"Id": "Part73", "Name": "MOUTH OPEN REF"}, {"Id": "Part83", "Name": "HUMAN EAR"}, {"Id": "Part85", "Name": "Elf ear L"}, {"Id": "Part335", "Name": "SIDEBURN"}, {"Id": "sidehair_wavy2_Skinning", "Name": "sidehair_wavy2(Skinning)"}, {"Id": "Part184", "Name": "sidehair_wavy2(Rotation)"}, {"Id": "Part108", "Name": "OVERBODY TWINTAIL CONTRAST"}, {"Id": "Part112", "Name": "Bunny Cup Big L"}, {"Id": "ruffles", "Name": "ruffles"}, {"Id": "BOW", "Name": "BOW"}, {"Id": "Part118", "Name": "BUNNY CUP FLAT"}, {"Id": "Part119", "Name": "CROSS STRAPS"}, {"Id": "ruffles2", "Name": "ruffles"}, {"Id": "Part126", "Name": "lace choker"}, {"Id": "BELL", "Name": "BELL"}, {"Id": "Part131", "Name": "BELT STRAP"}, {"Id": "Part132", "Name": "SHOUDLER RUFFLES"}, {"Id": "Part139", "Name": "CORSET BOW 1"}, {"Id": "Part140", "Name": "CORSET CLOTH"}, {"Id": "Part142", "Name": "BUNNY BOOB MASK"}, {"Id": "Part232", "Name": "RUFFLED SKIRT FRONT"}, {"Id": "SKIRT", "Name": "FLARED SKIRT"}, {"Id": "LEOTARD", "Name": "LEOTARD"}, {"Id": "Part143", "Name": "CASUAL TOP"}, {"Id": "shorts_stringL_Skinning", "Name": "shorts_stringL(Skinning)"}, {"Id": "Part220", "Name": "shorts_stringL(Rotation)"}, {"Id": "Part222", "Name": "LEG WARMER BOW"}, {"Id": "Part150", "Name": "SHOE R"}, {"Id": "fishnet", "Name": "fishnet"}, {"Id": "Part152", "Name": "SHOE L"}, {"Id": "fishnet3", "Name": "fishnet"}, {"Id": "Part154", "Name": "flat boob underfill"}, {"Id": "HAND2", "Name": "HAND"}, {"Id": "Part202", "Name": "arm skin"}, {"Id": "Part173", "Name": "HEART BOW"}, {"Id": "twintail_wavy1_contrast_Skinning", "Name": "twintail_wavy1_contrast(Skinning)"}, {"Id": "twintail_wavy1_Skinning", "Name": "twintail_wavy_1(<PERSON><PERSON>)"}, {"Id": "Part33", "Name": "twintail_wavy_1(Rotation)"}, {"Id": "twintail_wavy2_contrast_Skinning", "Name": "twintail_wavy2_contrast(Skinning)"}, {"Id": "twintail_wavy2_Skinning", "Name": "twintail_wavy2(Skin<PERSON>)"}, {"Id": "Part193", "Name": "twintail_wavy2(Rotation)"}, {"Id": "Part176", "Name": "BACK HAIR STRAIGHT L"}, {"Id": "Part43", "Name": "customizable vtuber model changes.psd"}, {"Id": "Part358", "Name": "BOB HAIR L"}, {"Id": "Part210", "Name": "PONYTAIL R"}, {"Id": "ponytail1_contrast2_Skinning", "Name": "ponytail1_contrast(Skinning)"}, {"Id": "ponytail5_Skinning", "Name": "ponytail1(Skinning)"}, {"Id": "Part207", "Name": "ponytail1(Rotation)"}, {"Id": "ponytail2_contrast2_Skinning", "Name": "ponytail2_contrast(Skinning)"}, {"Id": "ponytail6_Skinning", "Name": "ponytail2(Skinning)"}, {"Id": "Part208", "Name": "ponytail2(Rotation)"}, {"Id": "ponytial3_contrast2_Skinning", "Name": "ponytial3_contrast(Skinning)"}, {"Id": "ponytail7_Skinning2", "Name": "ponytail3(Skinning)"}, {"Id": "Part206", "Name": "ponytail3(Rotation)"}, {"Id": "ponytail7_Skinning", "Name": "ponytail3(Skinning)"}, {"Id": "Part205", "Name": "ponytail3(Rotation)"}, {"Id": "ponytail4_contrast2_Skinning", "Name": "ponytail4_contrast(Skinning)"}, {"Id": "ponytail8_Skinning", "Name": "ponytail4(Skin<PERSON>)"}, {"Id": "Part209", "Name": "ponytail4(Rotation)"}, {"Id": "Part200", "Name": "CAT EAR CONTRAST"}, {"Id": "Part166", "Name": "FOX CONTRAST"}, {"Id": "Part168", "Name": "BUNNY CONTRAST"}, {"Id": "Part170", "Name": "CAT EAR CONTRAST"}, {"Id": "Part338", "Name": "PLEADING HIGHLIGHT"}, {"Id": "Part67", "Name": "BIG HIGHLIGHT"}, {"Id": "Part69", "Name": "IRIS RIM"}, {"Id": "iris", "Name": "iris"}, {"Id": "Part71", "Name": "UPPER EYE MAKEUP"}, {"Id": "Part72", "Name": "LOWER EYE MAKEUP"}, {"Id": "Part81", "Name": "STAR EARRING"}, {"Id": "Part82", "Name": "HOOP earring"}, {"Id": "Part74", "Name": "INNER MOUTH"}, {"Id": "Part84", "Name": "Folder 59"}, {"Id": "corset_pendant_Skinning", "Name": "corset_pendant(Skinning)"}, {"Id": "Part41", "Name": "corset_pendant(Rotation)"}, {"Id": "BOW2", "Name": "BOW"}, {"Id": "Part233", "Name": "ruffled skirt bow"}, {"Id": "Part234", "Name": "ruffled skirt bow"}, {"Id": "Part144", "Name": "SKIRT WAIST"}, {"Id": "SKIRT2", "Name": "SKIRT"}, {"Id": "Part145", "Name": "LEOTARD RUFFLES"}, {"Id": "thumb2", "Name": "thumb"}, {"Id": "index2", "Name": "index"}, {"Id": "Part194", "Name": "middle 1"}, {"Id": "Ring1", "Name": "Ring1"}, {"Id": "Pinky1", "Name": "Pinky1"}, {"Id": "Part201", "Name": "Hand base"}, {"Id": "pinky2", "Name": "pinky2"}, {"Id": "Part344", "Name": "additonal hair straight"}, {"Id": "ponytail1_contrast2_Skinning2", "Name": "ponytail1_contrast(Skinning)"}, {"Id": "ponytail5_Skinning2", "Name": "ponytail1(Skinning)"}, {"Id": "Part211", "Name": "ponytail1(Rotation)"}, {"Id": "ponytail2_contrast2_Skinning2", "Name": "ponytail2_contrast(Skinning)"}, {"Id": "ponytail6_Skinning2", "Name": "ponytail2(Skinning)"}, {"Id": "Part212", "Name": "ponytail2(Rotation)"}, {"Id": "ponytial3_contrast2_Skinning2", "Name": "ponytial3_contrast(Skinning)"}, {"Id": "ponytail7_Skinning3", "Name": "ponytail3(Skinning)"}, {"Id": "Part213", "Name": "ponytail3(Rotation)"}, {"Id": "chain2_Skinning", "Name": "chain2(Skinning)"}, {"Id": "Part214", "Name": "chain2(Rotation)"}, {"Id": "Part75", "Name": "TOP TEETH (FANGS)"}, {"Id": "Part77", "Name": "lower teeth"}, {"Id": "Part197", "Name": "LEOTARD RUFFLES"}, {"Id": "Part76", "Name": "TOP FANG L"}, {"Id": "Part78", "Name": "FANG L"}, {"Id": "Part79", "Name": "FANG"}], "CombinedParameters": [["ParamMouthForm", "ParamMouthOpenY"], ["Param315", "Param316"]]}