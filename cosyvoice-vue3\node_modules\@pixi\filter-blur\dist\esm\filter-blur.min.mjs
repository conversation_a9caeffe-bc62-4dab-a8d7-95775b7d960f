/*!
 * @pixi/filter-blur - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/filter-blur is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{Filter as t}from"@pixi/core";import{settings as e}from"@pixi/settings";import{CLEAR_MODES as r}from"@pixi/constants";var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},i(t,e)};function n(t,e){function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var o={5:[.153388,.221461,.250301],7:[.071303,.131514,.189879,.214607],9:[.028532,.067234,.124009,.179044,.20236],11:[.0093,.028002,.065984,.121703,.175713,.198596],13:[.002406,.009255,.027867,.065666,.121117,.174868,.197641],15:[489e-6,.002403,.009246,.02784,.065602,.120999,.174697,.197448]},u=["varying vec2 vBlurTexCoords[%size%];","uniform sampler2D uSampler;","void main(void)","{","    gl_FragColor = vec4(0.0);","    %blur%","}"].join("\n");var l=function(t){function i(r,i,n,l,s){void 0===i&&(i=8),void 0===n&&(n=4),void 0===l&&(l=e.FILTER_RESOLUTION),void 0===s&&(s=5);var a=this,h=function(t,e){var r,i=Math.ceil(t/2),n="\n    attribute vec2 aVertexPosition;\n\n    uniform mat3 projectionMatrix;\n\n    uniform float strength;\n\n    varying vec2 vBlurTexCoords[%size%];\n\n    uniform vec4 inputSize;\n    uniform vec4 outputFrame;\n\n    vec4 filterVertexPosition( void )\n    {\n        vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;\n\n        return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);\n    }\n\n    vec2 filterTextureCoord( void )\n    {\n        return aVertexPosition * (outputFrame.zw * inputSize.zw);\n    }\n\n    void main(void)\n    {\n        gl_Position = filterVertexPosition();\n\n        vec2 textureCoord = filterTextureCoord();\n        %blur%\n    }",o="";r=e?"vBlurTexCoords[%index%] =  textureCoord + vec2(%sampleIndex% * strength, 0.0);":"vBlurTexCoords[%index%] =  textureCoord + vec2(0.0, %sampleIndex% * strength);";for(var u=0;u<t;u++){var l=r.replace("%index%",u.toString());o+=l=l.replace("%sampleIndex%",u-(i-1)+".0"),o+="\n"}return(n=n.replace("%blur%",o)).replace("%size%",t.toString())}(s,r),p=function(t){for(var e,r=o[t],i=r.length,n=u,l="",s=0;s<t;s++){var a="gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;".replace("%index%",s.toString());e=s,s>=i&&(e=t-s-1),l+=a=a.replace("%value%",r[e].toString()),l+="\n"}return(n=n.replace("%blur%",l)).replace("%size%",t.toString())}(s);return(a=t.call(this,h,p)||this).horizontal=r,a.resolution=l,a._quality=0,a.quality=n,a.blur=i,a}return n(i,t),i.prototype.apply=function(t,e,i,n){if(i?this.horizontal?this.uniforms.strength=1/i.width*(i.width/e.width):this.uniforms.strength=1/i.height*(i.height/e.height):this.horizontal?this.uniforms.strength=1/t.renderer.width*(t.renderer.width/e.width):this.uniforms.strength=1/t.renderer.height*(t.renderer.height/e.height),this.uniforms.strength*=this.strength,this.uniforms.strength/=this.passes,1===this.passes)t.applyFilter(this,e,i,n);else{var o=t.getFilterTexture(),u=t.renderer,l=e,s=o;this.state.blend=!1,t.applyFilter(this,l,s,r.CLEAR);for(var a=1;a<this.passes-1;a++){t.bindAndClear(l,r.BLIT),this.uniforms.uSampler=s;var h=s;s=l,l=h,u.shader.bind(this),u.geometry.draw(5)}this.state.blend=!0,t.applyFilter(this,s,i,n),t.returnFilterTexture(o)}},Object.defineProperty(i.prototype,"blur",{get:function(){return this.strength},set:function(t){this.padding=1+2*Math.abs(t),this.strength=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"quality",{get:function(){return this._quality},set:function(t){this._quality=t,this.passes=t},enumerable:!1,configurable:!0}),i}(t),s=function(t){function i(r,i,n,o){void 0===r&&(r=8),void 0===i&&(i=4),void 0===n&&(n=e.FILTER_RESOLUTION),void 0===o&&(o=5);var u=t.call(this)||this;return u.blurXFilter=new l(!0,r,i,n,o),u.blurYFilter=new l(!1,r,i,n,o),u.resolution=n,u.quality=i,u.blur=r,u.repeatEdgePixels=!1,u}return n(i,t),i.prototype.apply=function(t,e,i,n){var o=Math.abs(this.blurXFilter.strength),u=Math.abs(this.blurYFilter.strength);if(o&&u){var l=t.getFilterTexture();this.blurXFilter.apply(t,e,l,r.CLEAR),this.blurYFilter.apply(t,l,i,n),t.returnFilterTexture(l)}else u?this.blurYFilter.apply(t,e,i,n):this.blurXFilter.apply(t,e,i,n)},i.prototype.updatePadding=function(){this._repeatEdgePixels?this.padding=0:this.padding=2*Math.max(Math.abs(this.blurXFilter.strength),Math.abs(this.blurYFilter.strength))},Object.defineProperty(i.prototype,"blur",{get:function(){return this.blurXFilter.blur},set:function(t){this.blurXFilter.blur=this.blurYFilter.blur=t,this.updatePadding()},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"quality",{get:function(){return this.blurXFilter.quality},set:function(t){this.blurXFilter.quality=this.blurYFilter.quality=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"blurX",{get:function(){return this.blurXFilter.blur},set:function(t){this.blurXFilter.blur=t,this.updatePadding()},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"blurY",{get:function(){return this.blurYFilter.blur},set:function(t){this.blurYFilter.blur=t,this.updatePadding()},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"blendMode",{get:function(){return this.blurYFilter.blendMode},set:function(t){this.blurYFilter.blendMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"repeatEdgePixels",{get:function(){return this._repeatEdgePixels},set:function(t){this._repeatEdgePixels=t,this.updatePadding()},enumerable:!1,configurable:!0}),i}(t);export{s as BlurFilter,l as BlurFilterPass};
//# sourceMappingURL=filter-blur.min.mjs.map
