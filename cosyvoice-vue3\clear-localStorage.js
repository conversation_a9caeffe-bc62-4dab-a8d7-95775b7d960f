// 清理localStorage中的大量数据
// 在浏览器控制台中运行此脚本

console.log('🧹 开始清理localStorage...');

// 检查当前localStorage大小
let totalSize = 0;
for (let key in localStorage) {
  if (localStorage.hasOwnProperty(key)) {
    totalSize += localStorage[key].length;
  }
}
console.log(`📊 清理前localStorage大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);

// 清理漫画相关数据
const comicKeys = [
  'comic-generation-results',
  'comic-generation-results_backup',
  'storyStore',
  'comicStore'
];

comicKeys.forEach(key => {
  const item = localStorage.getItem(key);
  if (item) {
    const sizeMB = (item.length / 1024 / 1024).toFixed(2);
    console.log(`🗑️ 清理 ${key}: ${sizeMB}MB`);
    localStorage.removeItem(key);
  }
});

// 清理后大小
totalSize = 0;
for (let key in localStorage) {
  if (localStorage.hasOwnProperty(key)) {
    totalSize += localStorage[key].length;
  }
}
console.log(`✅ 清理后localStorage大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);

// 重新加载页面
console.log('🔄 3秒后重新加载页面...');
setTimeout(() => {
  location.reload();
}, 3000);