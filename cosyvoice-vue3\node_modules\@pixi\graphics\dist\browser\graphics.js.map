{"version": 3, "file": "graphics.js", "sources": ["../../src/const.ts", "../../src/styles/FillStyle.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/utils/buildPoly.ts", "../../src/utils/buildCircle.ts", "../../src/utils/buildRectangle.ts", "../../src/utils/buildRoundedRectangle.ts", "../../src/utils/buildLine.ts", "../../src/utils/ArcUtils.ts", "../../src/utils/BezierUtils.ts", "../../src/utils/QuadraticUtils.ts", "../../src/utils/BatchPart.ts", "../../src/utils/index.ts", "../../src/GraphicsData.ts", "../../src/GraphicsGeometry.ts", "../../src/styles/LineStyle.ts", "../../src/Graphics.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * Supported line joints in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @see https://graphicdesign.stackexchange.com/questions/59018/what-is-a-bevel-join-of-two-lines-exactly-illustrator\n * @name LINE_JOIN\n * @memberof PIXI\n * @static\n * @enum {string}\n * @property {string} MITER - 'miter': make a sharp corner where outer part of lines meet\n * @property {string} BEVEL - 'bevel': add a square butt at each end of line segment and fill the triangle at turn\n * @property {string} ROUND - 'round': add an arc at the joint\n */\nexport enum LINE_JOIN\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    MITER = 'miter',\n    BEVEL = 'bevel',\n    ROUND = 'round'\n}\n\n/**\n * Support line caps in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @name LINE_CAP\n * @memberof PIXI\n * @static\n * @enum {string}\n * @property {string} BUTT - 'butt': don't add any cap at line ends (leaves orthogonal edges)\n * @property {string} ROUND - 'round': add semicircle at ends\n * @property {string} SQUARE - 'square': add square at end (like `BUTT` except more length at end)\n */\nexport enum LINE_CAP\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    BUTT = 'butt',\n    ROUND = 'round',\n    SQUARE = 'square'\n}\n\nexport interface IGraphicsCurvesSettings\n{\n    adaptive: boolean;\n    maxLength: number;\n    minSegments: number;\n    maxSegments: number;\n\n    epsilon: number;\n\n    _segmentsCount(length: number, defaultSegments?: number): number;\n}\n\n/**\n * Graphics curves resolution settings. If `adaptive` flag is set to `true`,\n * the resolution is calculated based on the curve's length to ensure better visual quality.\n * Adaptive draw works with `bezierCurveTo` and `quadraticCurveTo`.\n * @static\n * @constant\n * @memberof PIXI\n * @name GRAPHICS_CURVES\n * @type {object}\n * @property {boolean} [adaptive=true] - flag indicating if the resolution should be adaptive\n * @property {number} [maxLength=10] - maximal length of a single segment of the curve (if adaptive = false, ignored)\n * @property {number} [minSegments=8] - minimal number of segments in the curve (if adaptive = false, ignored)\n * @property {number} [maxSegments=2048] - maximal number of segments in the curve (if adaptive = false, ignored)\n */\nexport const GRAPHICS_CURVES: IGraphicsCurvesSettings = {\n    adaptive: true,\n    maxLength: 10,\n    minSegments: 8,\n    maxSegments:  2048,\n\n    epsilon: 0.0001,\n\n    _segmentsCount(length: number, defaultSegments = 20)\n    {\n        if (!this.adaptive || !length || isNaN(length))\n        {\n            return defaultSegments;\n        }\n\n        let result = Math.ceil(length / this.maxLength);\n\n        if (result < this.minSegments)\n        {\n            result = this.minSegments;\n        }\n        else if (result > this.maxSegments)\n        {\n            result = this.maxSegments;\n        }\n\n        return result;\n    },\n};\n", "import { Texture } from '@pixi/core';\nimport type { Matrix } from '@pixi/math';\n\n/**\n * Fill style object for Graphics.\n * @memberof PIXI\n */\nexport class FillStyle\n{\n    /**\n     * The hex color value used when coloring the Graphics object.\n     * @default 0xFFFFFF\n     */\n    public color = 0xFFFFFF;\n\n    /** The alpha value used when filling the Graphics object. */\n    public alpha = 1.0;\n\n    /**\n     * The texture to be used for the fill.\n     * @default 0\n     */\n    public texture: Texture = Texture.WHITE;\n\n    /**\n     * The transform applied to the texture.\n     * @default null\n     */\n    public matrix: Matrix = null;\n\n    /** If the current fill is visible. */\n    public visible = false;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /** Clones the object */\n    public clone(): FillStyle\n    {\n        const obj = new FillStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n\n        return obj;\n    }\n\n    /** Reset */\n    public reset(): void\n    {\n        this.color = 0xFFFFFF;\n        this.alpha = 1;\n        this.texture = Texture.WHITE;\n        this.matrix = null;\n        this.visible = false;\n    }\n\n    /** Destroy and don't use after this. */\n    public destroy(): void\n    {\n        this.texture = null;\n        this.matrix = null;\n    }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { earcut } from '@pixi/utils';\n\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { Polygon } from '@pixi/math';\n\nfunction fixOrientation(points: number[], hole = false)\n{\n    const m = points.length;\n\n    if (m < 6)\n    {\n        return;\n    }\n\n    let area = 0;\n\n    for (let i = 0, x1 = points[m - 2], y1 = points[m - 1]; i < m; i += 2)\n    {\n        const x2 = points[i];\n        const y2 = points[i + 1];\n\n        area += (x2 - x1) * (y2 + y1);\n\n        x1 = x2;\n        y1 = y2;\n    }\n\n    if ((!hole && area > 0) || (hole && area <= 0))\n    {\n        const n = m / 2;\n\n        for (let i = n + (n % 2); i < m; i += 2)\n        {\n            const i1 = m - i - 2;\n            const i2 = m - i - 1;\n            const i3 = i;\n            const i4 = i + 1;\n\n            [points[i1], points[i3]] = [points[i3], points[i1]];\n            [points[i2], points[i4]] = [points[i4], points[i2]];\n        }\n    }\n}\n/**\n * Builds a polygon to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildPoly: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        graphicsData.points = (graphicsData.shape as Polygon).points.slice();\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        let points = graphicsData.points;\n        const holes = graphicsData.holes;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length >= 6)\n        {\n            fixOrientation(points, false);\n\n            const holeArray = [];\n            // Process holes..\n\n            for (let i = 0; i < holes.length; i++)\n            {\n                const hole = holes[i];\n\n                fixOrientation(hole.points, true);\n\n                holeArray.push(points.length / 2);\n                points = points.concat(hole.points);\n            }\n\n            // sort color\n            const triangles = earcut(points, holeArray, 2);\n\n            if (!triangles)\n            {\n                return;\n            }\n\n            const vertPos = verts.length / 2;\n\n            for (let i = 0; i < triangles.length; i += 3)\n            {\n                indices.push(triangles[i] + vertPos);\n                indices.push(triangles[i + 1] + vertPos);\n                indices.push(triangles[i + 2] + vertPos);\n            }\n\n            for (let i = 0; i < points.length; i++)\n            {\n                verts.push(points[i]);\n            }\n        }\n    },\n};\n", "// for type only\nimport { SHAPES } from '@pixi/math';\n\nimport type { Circle, Ellipse, RoundedRectangle } from '@pixi/math';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Builds a circle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object to draw\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildCircle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        // need to convert points to a nice regular data\n        const points = graphicsData.points;\n\n        let x;\n        let y;\n        let dx;\n        let dy;\n        let rx;\n        let ry;\n\n        if (graphicsData.type === SHAPES.CIRC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n            rx = ry = circle.radius;\n            dx = dy = 0;\n        }\n        else if (graphicsData.type === SHAPES.ELIP)\n        {\n            const ellipse = graphicsData.shape as Ellipse;\n\n            x = ellipse.x;\n            y = ellipse.y;\n            rx = ellipse.width;\n            ry = ellipse.height;\n            dx = dy = 0;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n            const halfWidth = roundedRect.width / 2;\n            const halfHeight = roundedRect.height / 2;\n\n            x = roundedRect.x + halfWidth;\n            y = roundedRect.y + halfHeight;\n            rx = ry = Math.max(0, Math.min(roundedRect.radius, Math.min(halfWidth, halfHeight)));\n            dx = halfWidth - rx;\n            dy = halfHeight - ry;\n        }\n\n        if (!(rx >= 0 && ry >= 0 && dx >= 0 && dy >= 0))\n        {\n            points.length = 0;\n\n            return;\n        }\n\n        // Choose a number of segments such that the maximum absolute deviation from the circle is approximately 0.029\n        const n = Math.ceil(2.3 * Math.sqrt(rx + ry));\n        const m = (n * 8) + (dx ? 4 : 0) + (dy ? 4 : 0);\n\n        points.length = m;\n\n        if (m === 0)\n        {\n            return;\n        }\n\n        if (n === 0)\n        {\n            points.length = 8;\n            points[0] = points[6] = x + dx;\n            points[1] = points[3] = y + dy;\n            points[2] = points[4] = x - dx;\n            points[5] = points[7] = y - dy;\n\n            return;\n        }\n\n        let j1 = 0;\n        let j2 = (n * 4) + (dx ? 2 : 0) + 2;\n        let j3 = j2;\n        let j4 = m;\n\n        {\n            const x0 = dx + rx;\n            const y0 = dy;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n\n            if (dy)\n            {\n                const y2 = y - y0;\n\n                points[j3++] = x2;\n                points[j3++] = y2;\n                points[--j4] = y2;\n                points[--j4] = x1;\n            }\n        }\n\n        for (let i = 1; i < n; i++)\n        {\n            const a = Math.PI / 2 * (i / n);\n            const x0 = dx + (Math.cos(a) * rx);\n            const y0 = dy + (Math.sin(a) * ry);\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n            points[j3++] = x2;\n            points[j3++] = y2;\n            points[--j4] = y2;\n            points[--j4] = x1;\n        }\n\n        {\n            const x0 = dx;\n            const y0 = dy + ry;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j4] = y2;\n            points[--j4] = x1;\n\n            if (dx)\n            {\n                points[j1++] = x2;\n                points[j1++] = y1;\n                points[--j4] = y2;\n                points[--j4] = x2;\n            }\n        }\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        const points = graphicsData.points;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length === 0)\n        {\n            return;\n        }\n\n        let vertPos = verts.length / 2;\n        const center = vertPos;\n\n        let x;\n        let y;\n\n        if (graphicsData.type !== SHAPES.RREC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n\n            x = roundedRect.x + (roundedRect.width / 2);\n            y = roundedRect.y + (roundedRect.height / 2);\n        }\n\n        const matrix = graphicsData.matrix;\n\n        // Push center (special point)\n        verts.push(\n            graphicsData.matrix ? (matrix.a * x) + (matrix.c * y) + matrix.tx : x,\n            graphicsData.matrix ? (matrix.b * x) + (matrix.d * y) + matrix.ty : y);\n\n        vertPos++;\n\n        verts.push(points[0], points[1]);\n\n        for (let i = 2; i < points.length; i += 2)\n        {\n            verts.push(points[i], points[i + 1]);\n\n            // add some uvs\n            indices.push(vertPos++, center, vertPos);\n        }\n\n        indices.push(center + 1, center, vertPos);\n    },\n};\n", "import type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { Rectangle } from '@pixi/math';\n\n/**\n * Builds a rectangle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildRectangle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        // --- //\n        // need to convert points to a nice regular data\n        //\n        const rectData = graphicsData.shape as Rectangle;\n        const x = rectData.x;\n        const y = rectData.y;\n        const width = rectData.width;\n        const height = rectData.height;\n\n        const points = graphicsData.points;\n\n        points.length = 0;\n\n        points.push(x, y,\n            x + width, y,\n            x + width, y + height,\n            x, y + height);\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        const points = graphicsData.points;\n        const verts = graphicsGeometry.points;\n\n        const vertPos = verts.length / 2;\n\n        verts.push(points[0], points[1],\n            points[2], points[3],\n            points[6], points[7],\n            points[4], points[5]);\n\n        graphicsGeometry.indices.push(vertPos, vertPos + 1, vertPos + 2,\n            vertPos + 1, vertPos + 2, vertPos + 3);\n    },\n};\n", "import { earcut } from '@pixi/utils';\n\n// for type only\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\nimport type { RoundedRectangle } from '@pixi/math';\nimport { Graphics } from '../Graphics';\nimport { buildCircle } from './buildCircle';\n\n/**\n * Calculate a single point for a quadratic bezier curve.\n * Utility function used by quadraticBezierCurve.\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} n1 - first number\n * @param {number} n2 - second number\n * @param {number} perc - percentage\n * @returns {number} the result\n */\nfunction getPt(n1: number, n2: number, perc: number): number\n{\n    const diff = n2 - n1;\n\n    return n1 + (diff * perc);\n}\n\n/**\n * Calculate the points for a quadratic bezier curve. (helper function..)\n * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} fromX - Origin point x\n * @param {number} fromY - Origin point x\n * @param {number} cpX - Control point x\n * @param {number} cpY - Control point y\n * @param {number} toX - Destination point x\n * @param {number} toY - Destination point y\n * @param {number[]} [out=[]] - The output array to add points into. If not passed, a new array is created.\n * @returns {number[]} an array of points\n */\nfunction quadraticBezierCurve(\n    fromX: number, fromY: number,\n    cpX: number, cpY: number,\n    toX: number, toY: number,\n    out: Array<number> = []): Array<number>\n{\n    const n = 20;\n    const points = out;\n\n    let xa = 0;\n    let ya = 0;\n    let xb = 0;\n    let yb = 0;\n    let x = 0;\n    let y = 0;\n\n    for (let i = 0, j = 0; i <= n; ++i)\n    {\n        j = i / n;\n\n        // The Green Line\n        xa = getPt(fromX, cpX, j);\n        ya = getPt(fromY, cpY, j);\n        xb = getPt(cpX, toX, j);\n        yb = getPt(cpY, toY, j);\n\n        // The Black Dot\n        x = getPt(xa, xb, j);\n        y = getPt(ya, yb, j);\n\n        // Handle case when first curve points overlaps and earcut fails to triangulate\n        if (i === 0 && points[points.length - 2] === x && points[points.length - 1] === y)\n        {\n            continue;\n        }\n\n        points.push(x, y);\n    }\n\n    return points;\n}\n\n/**\n * Builds a rounded rectangle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildRoundedRectangle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        if (Graphics.nextRoundedRectBehavior)\n        {\n            buildCircle.build(graphicsData);\n\n            return;\n        }\n\n        const rrectData = graphicsData.shape as RoundedRectangle;\n        const points = graphicsData.points;\n        const x = rrectData.x;\n        const y = rrectData.y;\n        const width = rrectData.width;\n        const height = rrectData.height;\n\n        // Don't allow negative radius or greater than half the smallest width\n        const radius = Math.max(0, Math.min(rrectData.radius, Math.min(width, height) / 2));\n\n        points.length = 0;\n\n        // No radius, do a simple rectangle\n        if (!radius)\n        {\n            points.push(x, y,\n                x + width, y,\n                x + width, y + height,\n                x, y + height);\n        }\n        else\n        {\n            quadraticBezierCurve(x, y + radius,\n                x, y,\n                x + radius, y,\n                points);\n            quadraticBezierCurve(x + width - radius,\n                y, x + width, y,\n                x + width, y + radius,\n                points);\n            quadraticBezierCurve(x + width, y + height - radius,\n                x + width, y + height,\n                x + width - radius, y + height,\n                points);\n            quadraticBezierCurve(x + radius, y + height,\n                x, y + height,\n                x, y + height - radius,\n                points);\n        }\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        if (Graphics.nextRoundedRectBehavior)\n        {\n            buildCircle.triangulate(graphicsData, graphicsGeometry);\n\n            return;\n        }\n\n        const points = graphicsData.points;\n\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        const vecPos = verts.length / 2;\n\n        const triangles = earcut(points, null, 2);\n\n        for (let i = 0, j = triangles.length; i < j; i += 3)\n        {\n            indices.push(triangles[i] + vecPos);\n            //     indices.push(triangles[i] + vecPos);\n            indices.push(triangles[i + 1] + vecPos);\n            //   indices.push(triangles[i + 2] + vecPos);\n            indices.push(triangles[i + 2] + vecPos);\n        }\n\n        for (let i = 0, j = points.length; i < j; i++)\n        {\n            verts.push(points[i], points[++i]);\n        }\n    },\n};\n", "import { Point, SHAPES } from '@pixi/math';\n\nimport type { Polygon } from '@pixi/math';\nimport type { GraphicsData } from '../GraphicsData';\nimport type { GraphicsGeometry } from '../GraphicsGeometry';\nimport { LINE_JOIN, LINE_CAP, GRAPHICS_CURVES } from '../const';\n\n/**\n * Buffers vertices to draw a square cap.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} x - X-coord of end point\n * @param {number} y - Y-coord of end point\n * @param {number} nx - X-coord of line normal pointing inside\n * @param {number} ny - Y-coord of line normal pointing inside\n * @param {number} innerWeight - Weight of inner points\n * @param {number} outerWeight - Weight of outer points\n * @param {boolean} clockwise - Whether the cap is drawn clockwise\n * @param {Array<number>} verts - vertex buffer\n * @returns {number} - no. of vertices pushed\n */\nfunction square(\n    x: number,\n    y: number,\n    nx: number,\n    ny: number,\n    innerWeight: number,\n    outerWeight: number,\n    clockwise: boolean, /* rotation for square (true at left end, false at right end) */\n    verts: Array<number>\n): number\n{\n    const ix = x - (nx * innerWeight);\n    const iy = y - (ny * innerWeight);\n    const ox = x + (nx * outerWeight);\n    const oy = y + (ny * outerWeight);\n\n    /* Rotate nx,ny for extension vector */\n    let exx; let\n        eyy;\n\n    if (clockwise)\n    {\n        exx = ny;\n        eyy = -nx;\n    }\n    else\n    {\n        exx = -ny;\n        eyy = nx;\n    }\n\n    /* [i|0]x,y extended at cap */\n    const eix = ix + exx;\n    const eiy = iy + eyy;\n    const eox = ox + exx;\n    const eoy = oy + eyy;\n\n    /* Square itself must be inserted clockwise*/\n    verts.push(eix, eiy);\n    verts.push(eox, eoy);\n\n    return 2;\n}\n\n/**\n * Buffers vertices to draw an arc at the line joint or cap.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {number} cx - X-coord of center\n * @param {number} cy - Y-coord of center\n * @param {number} sx - X-coord of arc start\n * @param {number} sy - Y-coord of arc start\n * @param {number} ex - X-coord of arc end\n * @param {number} ey - Y-coord of arc end\n * @param {Array<number>} verts - buffer of vertices\n * @param {boolean} clockwise - orientation of vertices\n * @returns {number} - no. of vertices pushed\n */\nfunction round(\n    cx: number,\n    cy: number,\n    sx: number,\n    sy: number,\n    ex: number,\n    ey: number,\n    verts: Array<number>,\n    clockwise: boolean, /* if not cap, then clockwise is turn of joint, otherwise rotation from angle0 to angle1 */\n): number\n{\n    const cx2p0x = sx - cx;\n    const cy2p0y = sy - cy;\n\n    let angle0 = Math.atan2(cx2p0x, cy2p0y);\n    let angle1 = Math.atan2(ex - cx, ey - cy);\n\n    if (clockwise && angle0 < angle1)\n    {\n        angle0 += Math.PI * 2;\n    }\n    else if (!clockwise && angle0 > angle1)\n    {\n        angle1 += Math.PI * 2;\n    }\n\n    let startAngle = angle0;\n    const angleDiff = angle1 - angle0;\n    const absAngleDiff = Math.abs(angleDiff);\n\n    /* if (absAngleDiff >= PI_LBOUND && absAngleDiff <= PI_UBOUND)\n    {\n        const r1x = cx - nxtPx;\n        const r1y = cy - nxtPy;\n\n        if (r1x === 0)\n        {\n            if (r1y > 0)\n            {\n                angleDiff = -angleDiff;\n            }\n        }\n        else if (r1x >= -GRAPHICS_CURVES.epsilon)\n        {\n            angleDiff = -angleDiff;\n        }\n    }*/\n\n    const radius = Math.sqrt((cx2p0x * cx2p0x) + (cy2p0y * cy2p0y));\n    const segCount = ((15 * absAngleDiff * Math.sqrt(radius) / Math.PI) >> 0) + 1;\n    const angleInc = angleDiff / segCount;\n\n    startAngle += angleInc;\n\n    if (clockwise)\n    {\n        verts.push(cx, cy);\n        verts.push(sx, sy);\n\n        for (let i = 1, angle = startAngle; i < segCount; i++, angle += angleInc)\n        {\n            verts.push(cx, cy);\n            verts.push(cx + ((Math.sin(angle) * radius)),\n                cy + ((Math.cos(angle) * radius)));\n        }\n\n        verts.push(cx, cy);\n        verts.push(ex, ey);\n    }\n    else\n    {\n        verts.push(sx, sy);\n        verts.push(cx, cy);\n\n        for (let i = 1, angle = startAngle; i < segCount; i++, angle += angleInc)\n        {\n            verts.push(cx + ((Math.sin(angle) * radius)),\n                cy + ((Math.cos(angle) * radius)));\n            verts.push(cx, cy);\n        }\n\n        verts.push(ex, ey);\n        verts.push(cx, cy);\n    }\n\n    return segCount * 2;\n}\n\n/**\n * Builds a line to draw using the polygon method.\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nfunction buildNonNativeLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    const shape = graphicsData.shape as Polygon;\n    let points = graphicsData.points || shape.points.slice();\n    const eps = graphicsGeometry.closePointEps;\n\n    if (points.length === 0)\n    {\n        return;\n    }\n    // if the line width is an odd number add 0.5 to align to a whole pixel\n    // commenting this out fixes #711 and #1620\n    // if (graphicsData.lineWidth%2)\n    // {\n    //     for (i = 0; i < points.length; i++)\n    //     {\n    //         points[i] += 0.5;\n    //     }\n    // }\n\n    const style = graphicsData.lineStyle;\n\n    // get first and last point.. figure out the middle!\n    const firstPoint = new Point(points[0], points[1]);\n    const lastPoint = new Point(points[points.length - 2], points[points.length - 1]);\n    const closedShape = shape.type !== SHAPES.POLY || shape.closeStroke;\n    const closedPath = Math.abs(firstPoint.x - lastPoint.x) < eps\n        && Math.abs(firstPoint.y - lastPoint.y) < eps;\n\n    // if the first point is the last point - gonna have issues :)\n    if (closedShape)\n    {\n        // need to clone as we are going to slightly modify the shape..\n        points = points.slice();\n\n        if (closedPath)\n        {\n            points.pop();\n            points.pop();\n            lastPoint.set(points[points.length - 2], points[points.length - 1]);\n        }\n\n        const midPointX = (firstPoint.x + lastPoint.x) * 0.5;\n        const midPointY = (lastPoint.y + firstPoint.y) * 0.5;\n\n        points.unshift(midPointX, midPointY);\n        points.push(midPointX, midPointY);\n    }\n\n    const verts = graphicsGeometry.points;\n    const length = points.length / 2;\n    let indexCount = points.length;\n    const indexStart = verts.length / 2;\n\n    // Max. inner and outer width\n    const width = style.width / 2;\n    const widthSquared = width * width;\n    const miterLimitSquared = style.miterLimit * style.miterLimit;\n\n    /* Line segments of interest where (x1,y1) forms the corner. */\n    let x0 = points[0];\n    let y0 = points[1];\n    let x1 = points[2];\n    let y1 = points[3];\n    let x2 = 0;\n    let y2 = 0;\n\n    /* perp[?](x|y) = the line normal with magnitude lineWidth. */\n    let perpx = -(y0 - y1);\n    let perpy = x0 - x1;\n    let perp1x = 0;\n    let perp1y = 0;\n\n    let dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n\n    perpx /= dist;\n    perpy /= dist;\n    perpx *= width;\n    perpy *= width;\n\n    const ratio = style.alignment;// 0.5;\n    const innerWeight = (1 - ratio) * 2;\n    const outerWeight = ratio * 2;\n\n    if (!closedShape)\n    {\n        if (style.cap === LINE_CAP.ROUND)\n        {\n            indexCount += round(\n                x0 - (perpx * (innerWeight - outerWeight) * 0.5),\n                y0 - (perpy * (innerWeight - outerWeight) * 0.5),\n                x0 - (perpx * innerWeight),\n                y0 - (perpy * innerWeight),\n                x0 + (perpx * outerWeight),\n                y0 + (perpy * outerWeight),\n                verts,\n                true,\n            ) + 2;\n        }\n        else if (style.cap === LINE_CAP.SQUARE)\n        {\n            indexCount += square(x0, y0, perpx, perpy, innerWeight, outerWeight, true, verts);\n        }\n    }\n\n    // Push first point (below & above vertices)\n    verts.push(\n        x0 - (perpx * innerWeight),\n        y0 - (perpy * innerWeight));\n    verts.push(\n        x0 + (perpx * outerWeight),\n        y0 + (perpy * outerWeight));\n\n    for (let i = 1; i < length - 1; ++i)\n    {\n        x0 = points[(i - 1) * 2];\n        y0 = points[((i - 1) * 2) + 1];\n\n        x1 = points[i * 2];\n        y1 = points[(i * 2) + 1];\n\n        x2 = points[(i + 1) * 2];\n        y2 = points[((i + 1) * 2) + 1];\n\n        perpx = -(y0 - y1);\n        perpy = x0 - x1;\n\n        dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n        perpx /= dist;\n        perpy /= dist;\n        perpx *= width;\n        perpy *= width;\n\n        perp1x = -(y1 - y2);\n        perp1y = x1 - x2;\n\n        dist = Math.sqrt((perp1x * perp1x) + (perp1y * perp1y));\n        perp1x /= dist;\n        perp1y /= dist;\n        perp1x *= width;\n        perp1y *= width;\n\n        /* d[x|y](0|1) = the component displacement between points p(0,1|1,2) */\n        const dx0 = x1 - x0;\n        const dy0 = y0 - y1;\n        const dx1 = x1 - x2;\n        const dy1 = y2 - y1;\n\n        /* +ve if internal angle < 90 degree, -ve if internal angle > 90 degree. */\n        const dot = (dx0 * dx1) + (dy0 * dy1);\n        /* +ve if internal angle counterclockwise, -ve if internal angle clockwise. */\n        const cross = (dy0 * dx1) - (dy1 * dx0);\n        const clockwise = (cross < 0);\n\n        /* Going nearly parallel? */\n        /* atan(0.001) ~= 0.001 rad ~= 0.057 degree */\n        if (Math.abs(cross) < 0.001 * Math.abs(dot))\n        {\n            verts.push(\n                x1 - (perpx * innerWeight),\n                y1 - (perpy * innerWeight));\n            verts.push(\n                x1 + (perpx * outerWeight),\n                y1 + (perpy * outerWeight));\n\n            /* 180 degree corner? */\n            if (dot >= 0)\n            {\n                if (style.join === LINE_JOIN.ROUND)\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false) + 4;\n                }\n                else\n                {\n                    indexCount += 2;\n                }\n\n                verts.push(\n                    x1 - (perp1x * outerWeight),\n                    y1 - (perp1y * outerWeight));\n                verts.push(\n                    x1 + (perp1x * innerWeight),\n                    y1 + (perp1y * innerWeight));\n            }\n\n            continue;\n        }\n\n        /* p[x|y] is the miter point. pdist is the distance between miter point and p1. */\n        const c1 = ((-perpx + x0) * (-perpy + y1)) - ((-perpx + x1) * (-perpy + y0));\n        const c2 = ((-perp1x + x2) * (-perp1y + y1)) - ((-perp1x + x1) * (-perp1y + y2));\n        const px = ((dx0 * c2) - (dx1 * c1)) / cross;\n        const py = ((dy1 * c1) - (dy0 * c2)) / cross;\n        const pdist = ((px - x1) * (px - x1)) + ((py - y1) * (py - y1));\n\n        /* Inner miter point */\n        const imx = x1 + ((px - x1) * innerWeight);\n        const imy = y1 + ((py - y1) * innerWeight);\n        /* Outer miter point */\n        const omx = x1 - ((px - x1) * outerWeight);\n        const omy = y1 - ((py - y1) * outerWeight);\n\n        /* Is the inside miter point too far away, creating a spike? */\n        const smallerInsideSegmentSq = Math.min((dx0 * dx0) + (dy0 * dy0), (dx1 * dx1) + (dy1 * dy1));\n        const insideWeight = clockwise ? innerWeight : outerWeight;\n        const smallerInsideDiagonalSq = smallerInsideSegmentSq + (insideWeight * insideWeight * widthSquared);\n        const insideMiterOk = pdist <= smallerInsideDiagonalSq;\n\n        if (insideMiterOk)\n        {\n            if (style.join === LINE_JOIN.BEVEL || pdist / widthSquared > miterLimitSquared)\n            {\n                if (clockwise) /* rotating at inner angle */\n                {\n                    verts.push(imx, imy); // inner miter point\n                    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight)); // first segment's outer vertex\n                    verts.push(imx, imy); // inner miter point\n                    verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight)); // second segment's outer vertex\n                }\n                else /* rotating at outer angle */\n                {\n                    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight)); // first segment's inner vertex\n                    verts.push(omx, omy); // outer miter point\n                    verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight)); // second segment's outer vertex\n                    verts.push(omx, omy); // outer miter point\n                }\n\n                indexCount += 2;\n            }\n            else if (style.join === LINE_JOIN.ROUND)\n            {\n                if (clockwise) /* arc is outside */\n                {\n                    verts.push(imx, imy);\n                    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight));\n\n                    indexCount += round(\n                        x1, y1,\n                        x1 + (perpx * outerWeight), y1 + (perpy * outerWeight),\n                        x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight),\n                        verts, true\n                    ) + 4;\n\n                    verts.push(imx, imy);\n                    verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight));\n                }\n                else /* arc is inside */\n                {\n                    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight));\n                    verts.push(omx, omy);\n\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false\n                    ) + 4;\n\n                    verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight));\n                    verts.push(omx, omy);\n                }\n            }\n            else\n            {\n                verts.push(imx, imy);\n                verts.push(omx, omy);\n            }\n        }\n        else // inside miter is NOT ok\n        {\n            verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight)); // first segment's inner vertex\n            verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight)); // first segment's outer vertex\n            if (style.join === LINE_JOIN.ROUND)\n            {\n                if (clockwise) /* arc is outside */\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 + (perpx * outerWeight), y1 + (perpy * outerWeight),\n                        x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight),\n                        verts, true\n                    ) + 2;\n                }\n                else /* arc is inside */\n                {\n                    indexCount += round(\n                        x1, y1,\n                        x1 - (perpx * innerWeight), y1 - (perpy * innerWeight),\n                        x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight),\n                        verts, false\n                    ) + 2;\n                }\n            }\n            else if (style.join === LINE_JOIN.MITER && pdist / widthSquared <= miterLimitSquared)\n            {\n                if (clockwise)\n                {\n                    verts.push(omx, omy); // inner miter point\n                    verts.push(omx, omy); // inner miter point\n                }\n                else\n                {\n                    verts.push(imx, imy); // outer miter point\n                    verts.push(imx, imy); // outer miter point\n                }\n                indexCount += 2;\n            }\n            verts.push(x1 - (perp1x * innerWeight), y1 - (perp1y * innerWeight)); // second segment's inner vertex\n            verts.push(x1 + (perp1x * outerWeight), y1 + (perp1y * outerWeight)); // second segment's outer vertex\n            indexCount += 2;\n        }\n    }\n\n    x0 = points[(length - 2) * 2];\n    y0 = points[((length - 2) * 2) + 1];\n\n    x1 = points[(length - 1) * 2];\n    y1 = points[((length - 1) * 2) + 1];\n\n    perpx = -(y0 - y1);\n    perpy = x0 - x1;\n\n    dist = Math.sqrt((perpx * perpx) + (perpy * perpy));\n    perpx /= dist;\n    perpy /= dist;\n    perpx *= width;\n    perpy *= width;\n\n    verts.push(x1 - (perpx * innerWeight), y1 - (perpy * innerWeight));\n    verts.push(x1 + (perpx * outerWeight), y1 + (perpy * outerWeight));\n\n    if (!closedShape)\n    {\n        if (style.cap === LINE_CAP.ROUND)\n        {\n            indexCount += round(\n                x1 - (perpx * (innerWeight - outerWeight) * 0.5),\n                y1 - (perpy * (innerWeight - outerWeight) * 0.5),\n                x1 - (perpx * innerWeight),\n                y1 - (perpy * innerWeight),\n                x1 + (perpx * outerWeight),\n                y1 + (perpy * outerWeight),\n                verts,\n                false\n            ) + 2;\n        }\n        else if (style.cap === LINE_CAP.SQUARE)\n        {\n            indexCount += square(x1, y1, perpx, perpy, innerWeight, outerWeight, false, verts);\n        }\n    }\n\n    const indices = graphicsGeometry.indices;\n    const eps2 = GRAPHICS_CURVES.epsilon * GRAPHICS_CURVES.epsilon;\n\n    // indices.push(indexStart);\n    for (let i = indexStart; i < indexCount + indexStart - 2; ++i)\n    {\n        x0 = verts[(i * 2)];\n        y0 = verts[(i * 2) + 1];\n\n        x1 = verts[(i + 1) * 2];\n        y1 = verts[((i + 1) * 2) + 1];\n\n        x2 = verts[(i + 2) * 2];\n        y2 = verts[((i + 2) * 2) + 1];\n\n        /* Skip zero area triangles */\n        if (Math.abs((x0 * (y1 - y2)) + (x1 * (y2 - y0)) + (x2 * (y0 - y1))) < eps2)\n        {\n            continue;\n        }\n\n        indices.push(i, i + 1, i + 2);\n    }\n}\n\n/**\n * Builds a line to draw using the gl.drawArrays(gl.LINES) method\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nfunction buildNativeLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    let i = 0;\n\n    const shape = graphicsData.shape as Polygon;\n    const points = graphicsData.points || shape.points;\n    const closedShape = shape.type !== SHAPES.POLY || shape.closeStroke;\n\n    if (points.length === 0) return;\n\n    const verts = graphicsGeometry.points;\n    const indices = graphicsGeometry.indices;\n    const length = points.length / 2;\n\n    const startIndex = verts.length / 2;\n    let currentIndex = startIndex;\n\n    verts.push(points[0], points[1]);\n\n    for (i = 1; i < length; i++)\n    {\n        verts.push(points[i * 2], points[(i * 2) + 1]);\n        indices.push(currentIndex, currentIndex + 1);\n\n        currentIndex++;\n    }\n\n    if (closedShape)\n    {\n        indices.push(currentIndex, startIndex);\n    }\n}\n\n/**\n * Builds a line to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.GraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {PIXI.GraphicsGeometry} graphicsGeometry - Geometry where to append output\n */\nexport function buildLine(graphicsData: GraphicsData, graphicsGeometry: GraphicsGeometry): void\n{\n    if (graphicsData.lineStyle.native)\n    {\n        buildNativeLine(graphicsData, graphicsGeometry);\n    }\n    else\n    {\n        buildNonNativeLine(graphicsData, graphicsGeometry);\n    }\n}\n", "import { GRAPHICS_CURVES } from '../const';\nimport { PI_2 } from '@pixi/math';\n\ninterface IArcLikeShape\n{\n    cx: number;\n    cy: number;\n    radius: number;\n    startAngle: number;\n    endAngle: number;\n    anticlockwise: boolean;\n}\n\n/**\n * Utilities for arc curves.\n * @private\n */\nexport class ArcUtils\n{\n    /**\n     * The arcTo() method creates an arc/curve between two tangents on the canvas.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @private\n     * @param x1 - The x-coordinate of the beginning of the arc\n     * @param y1 - The y-coordinate of the beginning of the arc\n     * @param x2 - The x-coordinate of the end of the arc\n     * @param y2 - The y-coordinate of the end of the arc\n     * @param radius - The radius of the arc\n     * @param points -\n     * @returns - If the arc length is valid, return center of circle, radius and other info otherwise `null`.\n     */\n    static curveTo(x1: number, y1: number, x2: number, y2: number, radius: number, points: Array<number>): IArcLikeShape\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        const a1 = fromY - y1;\n        const b1 = fromX - x1;\n        const a2 = y2 - y1;\n        const b2 = x2 - x1;\n        const mm = Math.abs((a1 * b2) - (b1 * a2));\n\n        if (mm < 1.0e-8 || radius === 0)\n        {\n            if (points[points.length - 2] !== x1 || points[points.length - 1] !== y1)\n            {\n                points.push(x1, y1);\n            }\n\n            return null;\n        }\n\n        const dd = (a1 * a1) + (b1 * b1);\n        const cc = (a2 * a2) + (b2 * b2);\n        const tt = (a1 * a2) + (b1 * b2);\n        const k1 = radius * Math.sqrt(dd) / mm;\n        const k2 = radius * Math.sqrt(cc) / mm;\n        const j1 = k1 * tt / dd;\n        const j2 = k2 * tt / cc;\n        const cx = (k1 * b2) + (k2 * b1);\n        const cy = (k1 * a2) + (k2 * a1);\n        const px = b1 * (k2 + j1);\n        const py = a1 * (k2 + j1);\n        const qx = b2 * (k1 + j2);\n        const qy = a2 * (k1 + j2);\n        const startAngle = Math.atan2(py - cy, px - cx);\n        const endAngle = Math.atan2(qy - cy, qx - cx);\n\n        return {\n            cx: (cx + x1),\n            cy: (cy + y1),\n            radius,\n            startAngle,\n            endAngle,\n            anticlockwise: (b1 * a2 > b2 * a1),\n        };\n    }\n\n    /* eslint-disable max-len */\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @private\n     * @param _startX - Start x location of arc\n     * @param _startY - Start y location of arc\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param _anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @param points - Collection of points to add to\n     */\n    static arc(_startX: number, _startY: number, cx: number, cy: number, radius: number,\n        startAngle: number, endAngle: number, _anticlockwise: boolean, points: Array<number>): void\n    {\n        const sweep = endAngle - startAngle;\n        const n = GRAPHICS_CURVES._segmentsCount(\n            Math.abs(sweep) * radius,\n            Math.ceil(Math.abs(sweep) / PI_2) * 40\n        );\n\n        const theta = (sweep) / (n * 2);\n        const theta2 = theta * 2;\n        const cTheta = Math.cos(theta);\n        const sTheta = Math.sin(theta);\n        const segMinus = n - 1;\n        const remainder = (segMinus % 1) / segMinus;\n\n        for (let i = 0; i <= segMinus; ++i)\n        {\n            const real = i + (remainder * i);\n            const angle = ((theta) + startAngle + (theta2 * real));\n            const c = Math.cos(angle);\n            const s = -Math.sin(angle);\n\n            points.push(\n                (((cTheta * c) + (sTheta * s)) * radius) + cx,\n                (((cTheta * -s) + (sTheta * c)) * radius) + cy\n            );\n        }\n    }\n    /* eslint-enable max-len */\n}\n", "import { GRAPHICS_CURVES } from '../const';\n\n/**\n * Utilities for bezier curves\n * @private\n */\nexport class BezierUtils\n{\n    /**\n     * Calculate length of bezier curve.\n     * Analytical solution is impossible, since it involves an integral that does not integrate in general.\n     * Therefore numerical solution is used.\n     * @private\n     * @param fromX - Starting point x\n     * @param fromY - Starting point y\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - Length of bezier curve\n     */\n    static curveLength(\n        fromX: number, fromY: number,\n        cpX: number, cpY: number,\n        cpX2: number, cpY2: number,\n        toX: number, toY: number): number\n    {\n        const n = 10;\n        let result = 0.0;\n        let t = 0.0;\n        let t2 = 0.0;\n        let t3 = 0.0;\n        let nt = 0.0;\n        let nt2 = 0.0;\n        let nt3 = 0.0;\n        let x = 0.0;\n        let y = 0.0;\n        let dx = 0.0;\n        let dy = 0.0;\n        let prevX = fromX;\n        let prevY = fromY;\n\n        for (let i = 1; i <= n; ++i)\n        {\n            t = i / n;\n            t2 = t * t;\n            t3 = t2 * t;\n            nt = (1.0 - t);\n            nt2 = nt * nt;\n            nt3 = nt2 * nt;\n\n            x = (nt3 * fromX) + (3.0 * nt2 * t * cpX) + (3.0 * nt * t2 * cpX2) + (t3 * toX);\n            y = (nt3 * fromY) + (3.0 * nt2 * t * cpY) + (3 * nt * t2 * cpY2) + (t3 * toY);\n            dx = prevX - x;\n            dy = prevY - y;\n            prevX = x;\n            prevY = y;\n\n            result += Math.sqrt((dx * dx) + (dy * dy));\n        }\n\n        return result;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     *\n     * Ignored from docs since it is not directly exposed.\n     * @ignore\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @param points - Path array to push points into\n     */\n    static curveTo(\n        cpX: number, cpY: number,\n        cpX2: number, cpY2: number,\n        toX: number, toY: number,\n        points: Array<number>): void\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        points.length -= 2;\n\n        const n = GRAPHICS_CURVES._segmentsCount(\n            BezierUtils.curveLength(fromX, fromY, cpX, cpY, cpX2, cpY2, toX, toY)\n        );\n\n        let dt = 0;\n        let dt2 = 0;\n        let dt3 = 0;\n        let t2 = 0;\n        let t3 = 0;\n\n        points.push(fromX, fromY);\n\n        for (let i = 1, j = 0; i <= n; ++i)\n        {\n            j = i / n;\n\n            dt = (1 - j);\n            dt2 = dt * dt;\n            dt3 = dt2 * dt;\n\n            t2 = j * j;\n            t3 = t2 * j;\n\n            points.push(\n                (dt3 * fromX) + (3 * dt2 * j * cpX) + (3 * dt * t2 * cpX2) + (t3 * toX),\n                (dt3 * fromY) + (3 * dt2 * j * cpY) + (3 * dt * t2 * cpY2) + (t3 * toY)\n            );\n        }\n    }\n}\n", "import { GRAPHICS_CURVES } from '../const';\n\n/**\n * Utilities for quadratic curves.\n * @private\n */\nexport class QuadraticUtils\n{\n    /**\n     * Calculate length of quadratic curve\n     * @see {@link http://www.malczak.linuxpl.com/blog/quadratic-bezier-curve-length/}\n     * for the detailed explanation of math behind this.\n     * @private\n     * @param fromX - x-coordinate of curve start point\n     * @param fromY - y-coordinate of curve start point\n     * @param cpX - x-coordinate of curve control point\n     * @param cpY - y-coordinate of curve control point\n     * @param toX - x-coordinate of curve end point\n     * @param toY - y-coordinate of curve end point\n     * @returns - Length of quadratic curve\n     */\n    static curveLength(\n        fromX: number, fromY: number,\n        cpX: number, cpY: number,\n        toX: number, toY: number): number\n    {\n        const ax = fromX - (2.0 * cpX) + toX;\n        const ay = fromY - (2.0 * cpY) + toY;\n        const bx = (2.0 * cpX) - (2.0 * fromX);\n        const by = (2.0 * cpY) - (2.0 * fromY);\n        const a = 4.0 * ((ax * ax) + (ay * ay));\n        const b = 4.0 * ((ax * bx) + (ay * by));\n        const c = (bx * bx) + (by * by);\n\n        const s = 2.0 * Math.sqrt(a + b + c);\n        const a2 = Math.sqrt(a);\n        const a32 = 2.0 * a * a2;\n        const c2 = 2.0 * Math.sqrt(c);\n        const ba = b / a2;\n\n        return (\n            (a32 * s)\n                + (a2 * b * (s - c2))\n                + (\n                    ((4.0 * c * a) - (b * b))\n                   * Math.log(((2.0 * a2) + ba + s) / (ba + c2))\n                )\n        ) / (4.0 * a32);\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @private\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @param points - Points to add segments to.\n     */\n    static curveTo(cpX: number, cpY: number, toX: number, toY: number, points: Array<number>): void\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        const n = GRAPHICS_CURVES._segmentsCount(\n            QuadraticUtils.curveLength(fromX, fromY, cpX, cpY, toX, toY)\n        );\n\n        let xa = 0;\n        let ya = 0;\n\n        for (let i = 1; i <= n; ++i)\n        {\n            const j = i / n;\n\n            xa = fromX + ((cpX - fromX) * j);\n            ya = fromY + ((cpY - fromY) * j);\n\n            points.push(xa + (((cpX + ((toX - cpX) * j)) - xa) * j),\n                ya + (((cpY + ((toY - cpY) * j)) - ya) * j));\n        }\n    }\n}\n", "import type { LineStyle } from '../styles/LineStyle';\nimport type { FillStyle } from '../styles/FillStyle';\n\n/**\n * A structure to hold interim batch objects for Graphics.\n * @memberof PIXI.graphicsUtils\n */\nexport class BatchPart\n{\n    public style: LineStyle | FillStyle;\n    public start: number;\n    public size: number;\n    public attribStart: number;\n    public attribSize: number;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /**\n     * Begin batch part.\n     * @param style\n     * @param startIndex\n     * @param attribStart\n     */\n    public begin(style: LineStyle | FillStyle, startIndex: number, attribStart: number): void\n    {\n        this.reset();\n        this.style = style;\n        this.start = startIndex;\n        this.attribStart = attribStart;\n    }\n\n    /**\n     * End batch part.\n     * @param endIndex\n     * @param endAttrib\n     */\n    public end(endIndex: number, endAttrib: number): void\n    {\n        this.attribSize = endAttrib - this.attribStart;\n        this.size = endIndex - this.start;\n    }\n\n    public reset(): void\n    {\n        this.style = null;\n        this.size = 0;\n        this.start = 0;\n        this.attribStart = 0;\n        this.attribSize = 0;\n    }\n}\n", "/**\n * Generalized convenience utilities for Graphics.\n * @namespace graphicsUtils\n * @memberof PIXI\n */\n\nimport { buildPoly } from './buildPoly';\nexport { buildPoly };\n\nimport { buildCircle } from './buildCircle';\nexport { buildCircle };\n\nimport { buildRectangle } from './buildRectangle';\nexport { buildRectangle };\n\nimport { buildRoundedRectangle } from './buildRoundedRectangle';\nexport { buildRoundedRectangle };\n\nexport * from './buildLine';\nexport * from './ArcUtils';\nexport * from './BezierUtils';\nexport * from './QuadraticUtils';\nexport * from './BatchPart';\n\n// for type only\nimport type { BatchPart } from './BatchPart';\nimport { SHAPES } from '@pixi/math';\nimport type { BatchDrawCall } from '@pixi/core';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Map of fill commands for each shape type.\n * @memberof PIXI.graphicsUtils\n * @member {object} FILL_COMMANDS\n */\nexport const FILL_COMMANDS: Record<SHAPES, IShapeBuildCommand> = {\n    [SHAPES.POLY]: buildPoly,\n    [SHAPES.CIRC]: buildCircle,\n    [SHAPES.ELIP]: buildCircle,\n    [SHAPES.RECT]: buildRectangle,\n    [SHAPES.RREC]: buildRoundedRectangle,\n};\n\n/**\n * Batch pool, stores unused batches for preventing allocations.\n * @memberof PIXI.graphicsUtils\n * @member {Array<PIXI.graphicsUtils.BatchPart>} BATCH_POOL\n */\nexport const BATCH_POOL: Array<BatchPart> = [];\n\n/**\n * Draw call pool, stores unused draw calls for preventing allocations.\n * @memberof PIXI.graphicsUtils\n * @member {Array<PIXI.BatchDrawCall>} DRAW_CALL_POOL\n */\nexport const DRAW_CALL_POOL: Array<BatchDrawCall> = [];\n", "import type { Matrix, SHAPES, IShape } from '@pixi/math';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/**\n * A class to contain data useful for Graphics objects\n * @memberof PIXI\n */\nexport class GraphicsData\n{\n    /**\n     * The shape object to draw.\n     * @member {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle}\n     */\n    shape: IShape;\n\n    /** The style of the line. */\n    lineStyle: LineStyle;\n\n    /** The style of the fill. */\n    fillStyle: FillStyle;\n\n    /** The transform matrix. */\n    matrix: Matrix;\n\n    /** The type of the shape, see the Const.Shapes file for all the existing types, */\n    type: SHAPES;\n\n    /** The collection of points. */\n    points: number[] = [];\n\n    /** The collection of holes. */\n\n    holes: Array<GraphicsData> = [];\n\n    /**\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - the width of the line to draw\n     * @param lineStyle - the color of the line to draw\n     * @param matrix - Transform matrix\n     */\n    constructor(shape: IShape, fillStyle: FillStyle = null, lineStyle: LineStyle = null, matrix: Matrix = null)\n    {\n        this.shape = shape;\n        this.lineStyle = lineStyle;\n        this.fillStyle = fillStyle;\n        this.matrix = matrix;\n        this.type = shape.type;\n    }\n\n    /**\n     * Creates a new GraphicsData object with the same values as this one.\n     * @returns - Cloned GraphicsData object\n     */\n    public clone(): GraphicsData\n    {\n        return new GraphicsData(\n            this.shape,\n            this.fillStyle,\n            this.lineStyle,\n            this.matrix\n        );\n    }\n\n    /** Destroys the Graphics data. */\n    public destroy(): void\n    {\n        this.shape = null;\n        this.holes.length = 0;\n        this.holes = null;\n        this.points.length = 0;\n        this.points = null;\n        this.lineStyle = null;\n        this.fillStyle = null;\n    }\n}\n", "import {\n    buildLine,\n    buildP<PERSON>,\n    <PERSON>ch<PERSON><PERSON>,\n    FILL_COMMANDS,\n    BATCH_POOL,\n    DRAW_CALL_POOL,\n} from './utils';\n\nimport type {\n    Texture } from '@pixi/core';\nimport {\n    BatchGeometry,\n    BatchDrawCall,\n    BatchTextureArray,\n    BaseTexture\n} from '@pixi/core';\n\nimport { DRAW_MODES, WRAP_MODES } from '@pixi/constants';\nimport { Point } from '@pixi/math';\nimport { GraphicsData } from './GraphicsData';\nimport { premultiplyTint } from '@pixi/utils';\nimport { Bounds } from '@pixi/display';\n\nimport type { Circle, Ellipse, Polygon, Rectangle, RoundedRectangle, IPointData, Matrix } from '@pixi/math';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/*\n * Complex shape type\n * @todo Move to Math shapes\n */\ntype IShape = Circle | Ellipse | Polygon | Rectangle | RoundedRectangle;\n\nconst tmpPoint = new Point();\n\n/**\n * The Graphics class contains methods used to draw primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.\n *\n * GraphicsGeometry is designed to not be continually updating the geometry since it's expensive\n * to re-tesselate using **earcut**. Consider using {@link PIXI.Mesh} for this use-case, it's much faster.\n * @memberof PIXI\n */\nexport class GraphicsGeometry extends BatchGeometry\n{\n    /**\n     * The maximum number of points to consider an object \"batchable\",\n     * able to be batched by the renderer's batch system.\n\\\n     */\n    public static BATCHABLE_SIZE = 100;\n\n    /** Minimal distance between points that are considered different. Affects line tesselation. */\n    public closePointEps = 1e-4;\n\n    /** Padding to add to the bounds. */\n    public boundsPadding = 0;\n\n    uvsFloat32: Float32Array = null;\n    indicesUint16: Uint16Array | Uint32Array = null;\n    batchable = false;\n\n    /** An array of points to draw, 2 numbers per point */\n    points: number[] = [];\n\n    /** The collection of colors */\n    colors: number[] = [];\n\n    /** The UVs collection */\n    uvs: number[] = [];\n\n    /** The indices of the vertices */\n    indices: number[] = [];\n\n    /** Reference to the texture IDs. */\n    textureIds: number[] = [];\n\n    /**\n     * The collection of drawn shapes.\n     * @member {PIXI.GraphicsData[]}\n     */\n    graphicsData: Array<GraphicsData> = [];\n\n    /**\n     * List of current draw calls drived from the batches.\n     * @member {PIXI.BatchDrawCall[]}\n     */\n    drawCalls: Array<BatchDrawCall> = [];\n\n    /** Batches need to regenerated if the geometry is updated. */\n    batchDirty = -1;\n\n    /**\n     * Intermediate abstract format sent to batch system.\n     * Can be converted to drawCalls or to batchable objects.\n     * @member {PIXI.graphicsUtils.BatchPart[]}\n     */\n    batches: Array<BatchPart> = [];\n\n    /** Used to detect if the graphics object has changed. */\n    protected dirty = 0;\n\n    /** Used to check if the cache is dirty. */\n    protected cacheDirty = -1;\n\n    /** Used to detect if we cleared the graphicsData. */\n    protected clearDirty = 0;\n\n    /** Index of the last batched shape in the stack of calls. */\n    protected shapeIndex = 0;\n\n    /** Cached bounds. */\n    protected _bounds: Bounds = new Bounds();\n\n    /** The bounds dirty flag. */\n    protected boundsDirty = -1;\n\n    // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n    constructor()\n    {\n        super();\n    }\n\n    /**\n     * Get the current bounds of the graphic geometry.\n     * @readonly\n     */\n    public get bounds(): Bounds\n    {\n        this.updateBatches();\n\n        if (this.boundsDirty !== this.dirty)\n        {\n            this.boundsDirty = this.dirty;\n            this.calculateBounds();\n        }\n\n        return this._bounds;\n    }\n\n    /** Call if you changed graphicsData manually. Empties all batch buffers. */\n    protected invalidate(): void\n    {\n        this.boundsDirty = -1;\n        this.dirty++;\n        this.batchDirty++;\n        this.shapeIndex = 0;\n\n        this.points.length = 0;\n        this.colors.length = 0;\n        this.uvs.length = 0;\n        this.indices.length = 0;\n        this.textureIds.length = 0;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const batchPart = this.batches[i];\n\n            batchPart.reset();\n            BATCH_POOL.push(batchPart);\n        }\n\n        this.batches.length = 0;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This GraphicsGeometry object. Good for chaining method calls\n     */\n    public clear(): GraphicsGeometry\n    {\n        if (this.graphicsData.length > 0)\n        {\n            this.invalidate();\n            this.clearDirty++;\n            this.graphicsData.length = 0;\n        }\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - Defines style of the fill.\n     * @param lineStyle - Defines style of the lines.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawShape(\n        shape: IShape,\n        fillStyle: FillStyle = null,\n        lineStyle: LineStyle = null,\n        matrix: Matrix = null): GraphicsGeometry\n    {\n        const data = new GraphicsData(shape, fillStyle, lineStyle, matrix);\n\n        this.graphicsData.push(data);\n        this.dirty++;\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawHole(shape: IShape, matrix: Matrix = null): GraphicsGeometry\n    {\n        if (!this.graphicsData.length)\n        {\n            return null;\n        }\n\n        const data = new GraphicsData(shape, null, null, matrix);\n\n        const lastShape = this.graphicsData[this.graphicsData.length - 1];\n\n        data.lineStyle = lastShape.lineStyle;\n\n        lastShape.holes.push(data);\n\n        this.dirty++;\n\n        return this;\n    }\n\n    /** Destroys the GraphicsGeometry object. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        // destroy each of the GraphicsData objects\n        for (let i = 0; i < this.graphicsData.length; ++i)\n        {\n            this.graphicsData[i].destroy();\n        }\n\n        this.points.length = 0;\n        this.points = null;\n        this.colors.length = 0;\n        this.colors = null;\n        this.uvs.length = 0;\n        this.uvs = null;\n        this.indices.length = 0;\n        this.indices = null;\n        this.indexBuffer.destroy();\n        this.indexBuffer = null;\n        this.graphicsData.length = 0;\n        this.graphicsData = null;\n        this.drawCalls.length = 0;\n        this.drawCalls = null;\n        this.batches.length = 0;\n        this.batches = null;\n        this._bounds = null;\n    }\n\n    /**\n     * Check to see if a point is contained within this geometry.\n     * @param point - Point to check if it's contained.\n     * @returns {boolean} `true` if the point is contained within geometry.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        const graphicsData = this.graphicsData;\n\n        for (let i = 0; i < graphicsData.length; ++i)\n        {\n            const data = graphicsData[i];\n\n            if (!data.fillStyle.visible)\n            {\n                continue;\n            }\n\n            // only deal with fills..\n            if (data.shape)\n            {\n                if (data.matrix)\n                {\n                    data.matrix.applyInverse(point, tmpPoint);\n                }\n                else\n                {\n                    tmpPoint.copyFrom(point);\n                }\n\n                if (data.shape.contains(tmpPoint.x, tmpPoint.y))\n                {\n                    let hitHole = false;\n\n                    if (data.holes)\n                    {\n                        for (let i = 0; i < data.holes.length; i++)\n                        {\n                            const hole = data.holes[i];\n\n                            if (hole.shape.contains(tmpPoint.x, tmpPoint.y))\n                            {\n                                hitHole = true;\n                                break;\n                            }\n                        }\n                    }\n\n                    if (!hitHole)\n                    {\n                        return true;\n                    }\n                }\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Generates intermediate batch data. Either gets converted to drawCalls\n     * or used to convert to batch objects directly by the Graphics object.\n     */\n    updateBatches(): void\n    {\n        if (!this.graphicsData.length)\n        {\n            this.batchable = true;\n\n            return;\n        }\n\n        if (!this.validateBatching())\n        {\n            return;\n        }\n\n        this.cacheDirty = this.dirty;\n\n        const uvs = this.uvs;\n        const graphicsData = this.graphicsData;\n\n        let batchPart: BatchPart = null;\n\n        let currentStyle = null;\n\n        if (this.batches.length > 0)\n        {\n            batchPart = this.batches[this.batches.length - 1];\n            currentStyle = batchPart.style;\n        }\n\n        for (let i = this.shapeIndex; i < graphicsData.length; i++)\n        {\n            this.shapeIndex++;\n\n            const data = graphicsData[i];\n            const fillStyle = data.fillStyle;\n            const lineStyle = data.lineStyle;\n            const command = FILL_COMMANDS[data.type];\n\n            // build out the shapes points..\n            command.build(data);\n\n            if (data.matrix)\n            {\n                this.transformPoints(data.points, data.matrix);\n            }\n\n            if (fillStyle.visible || lineStyle.visible)\n            {\n                this.processHoles(data.holes);\n            }\n\n            for (let j = 0; j < 2; j++)\n            {\n                const style = (j === 0) ? fillStyle : lineStyle;\n\n                if (!style.visible) continue;\n\n                const nextTexture = style.texture.baseTexture;\n                const index = this.indices.length;\n                const attribIndex = this.points.length / 2;\n\n                nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                if (j === 0)\n                {\n                    this.processFill(data);\n                }\n                else\n                {\n                    this.processLine(data);\n                }\n\n                const size = (this.points.length / 2) - attribIndex;\n\n                if (size === 0) continue;\n                // close batch if style is different\n                if (batchPart && !this._compareStyles(currentStyle, style))\n                {\n                    batchPart.end(index, attribIndex);\n                    batchPart = null;\n                }\n                // spawn new batch if its first batch or previous was closed\n                if (!batchPart)\n                {\n                    batchPart = BATCH_POOL.pop() || new BatchPart();\n                    batchPart.begin(style, index, attribIndex);\n                    this.batches.push(batchPart);\n                    currentStyle = style;\n                }\n\n                this.addUvs(this.points, uvs, style.texture, attribIndex, size, style.matrix);\n            }\n        }\n\n        const index = this.indices.length;\n        const attrib = this.points.length / 2;\n\n        if (batchPart)\n        {\n            batchPart.end(index, attrib);\n        }\n\n        if (this.batches.length === 0)\n        {\n            // there are no visible styles in GraphicsData\n            // its possible that someone wants Graphics just for the bounds\n            this.batchable = true;\n\n            return;\n        }\n\n        const need32 = attrib > 0xffff;\n\n        // prevent allocation when length is same as buffer\n        if (this.indicesUint16 && this.indices.length === this.indicesUint16.length\n            && need32 === (this.indicesUint16.BYTES_PER_ELEMENT > 2))\n        {\n            this.indicesUint16.set(this.indices);\n        }\n        else\n        {\n            this.indicesUint16 = need32 ? new Uint32Array(this.indices) : new Uint16Array(this.indices);\n        }\n\n        // TODO make this a const..\n        this.batchable = this.isBatchable();\n\n        if (this.batchable)\n        {\n            this.packBatches();\n        }\n        else\n        {\n            this.buildDrawCalls();\n        }\n    }\n\n    /**\n     * Affinity check\n     * @param styleA\n     * @param styleB\n     */\n    protected _compareStyles(styleA: FillStyle | LineStyle, styleB: FillStyle | LineStyle): boolean\n    {\n        if (!styleA || !styleB)\n        {\n            return false;\n        }\n\n        if (styleA.texture.baseTexture !== styleB.texture.baseTexture)\n        {\n            return false;\n        }\n\n        if (styleA.color + styleA.alpha !== styleB.color + styleB.alpha)\n        {\n            return false;\n        }\n\n        if (!!(styleA as LineStyle).native !== !!(styleB as LineStyle).native)\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /** Test geometry for batching process. */\n    protected validateBatching(): boolean\n    {\n        if (this.dirty === this.cacheDirty || !this.graphicsData.length)\n        {\n            return false;\n        }\n\n        for (let i = 0, l = this.graphicsData.length; i < l; i++)\n        {\n            const data = this.graphicsData[i];\n            const fill = data.fillStyle;\n            const line = data.lineStyle;\n\n            if (fill && !fill.texture.baseTexture.valid) return false;\n            if (line && !line.texture.baseTexture.valid) return false;\n        }\n\n        return true;\n    }\n\n    /** Offset the indices so that it works with the batcher. */\n    protected packBatches(): void\n    {\n        this.batchDirty++;\n        this.uvsFloat32 = new Float32Array(this.uvs);\n\n        const batches = this.batches;\n\n        for (let i = 0, l = batches.length; i < l; i++)\n        {\n            const batch = batches[i];\n\n            for (let j = 0; j < batch.size; j++)\n            {\n                const index = batch.start + j;\n\n                this.indicesUint16[index] = this.indicesUint16[index] - batch.attribStart;\n            }\n        }\n    }\n\n    /**\n     * Checks to see if this graphics geometry can be batched.\n     * Currently it needs to be small enough and not contain any native lines.\n     */\n    protected isBatchable(): boolean\n    {\n        // prevent heavy mesh batching\n        if (this.points.length > 0xffff * 2)\n        {\n            return false;\n        }\n\n        const batches = this.batches;\n\n        for (let i = 0; i < batches.length; i++)\n        {\n            if ((batches[i].style as LineStyle).native)\n            {\n                return false;\n            }\n        }\n\n        return (this.points.length < GraphicsGeometry.BATCHABLE_SIZE * 2);\n    }\n\n    /** Converts intermediate batches data to drawCalls. */\n    protected buildDrawCalls(): void\n    {\n        let TICK = ++BaseTexture._globalBatch;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        let currentGroup: BatchDrawCall =  DRAW_CALL_POOL.pop();\n\n        if (!currentGroup)\n        {\n            currentGroup = new BatchDrawCall();\n            currentGroup.texArray = new BatchTextureArray();\n        }\n        currentGroup.texArray.count = 0;\n        currentGroup.start = 0;\n        currentGroup.size = 0;\n        currentGroup.type = DRAW_MODES.TRIANGLES;\n\n        let textureCount = 0;\n        let currentTexture = null;\n        let textureId = 0;\n        let native = false;\n        let drawMode = DRAW_MODES.TRIANGLES;\n\n        let index = 0;\n\n        this.drawCalls.push(currentGroup);\n\n        // TODO - this can be simplified\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const data = this.batches[i];\n\n            // TODO add some full on MAX_TEXTURE CODE..\n            const MAX_TEXTURES = 8;\n\n            // Forced cast for checking `native` without errors\n            const style = data.style as LineStyle;\n\n            const nextTexture = style.texture.baseTexture;\n\n            if (native !== !!style.native)\n            {\n                native = !!style.native;\n                drawMode = native ? DRAW_MODES.LINES : DRAW_MODES.TRIANGLES;\n\n                // force the batch to break!\n                currentTexture = null;\n                textureCount = MAX_TEXTURES;\n                TICK++;\n            }\n\n            if (currentTexture !== nextTexture)\n            {\n                currentTexture = nextTexture;\n\n                if (nextTexture._batchEnabled !== TICK)\n                {\n                    if (textureCount === MAX_TEXTURES)\n                    {\n                        TICK++;\n\n                        textureCount = 0;\n\n                        if (currentGroup.size > 0)\n                        {\n                            currentGroup = DRAW_CALL_POOL.pop();\n                            if (!currentGroup)\n                            {\n                                currentGroup = new BatchDrawCall();\n                                currentGroup.texArray = new BatchTextureArray();\n                            }\n                            this.drawCalls.push(currentGroup);\n                        }\n\n                        currentGroup.start = index;\n                        currentGroup.size = 0;\n                        currentGroup.texArray.count = 0;\n                        currentGroup.type = drawMode;\n                    }\n\n                    // TODO add this to the render part..\n                    // Hack! Because texture has protected `touched`\n                    nextTexture.touched = 1;// touch;\n\n                    nextTexture._batchEnabled = TICK;\n                    nextTexture._batchLocation = textureCount;\n                    nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                    currentGroup.texArray.elements[currentGroup.texArray.count++] = nextTexture;\n                    textureCount++;\n                }\n            }\n\n            currentGroup.size += data.size;\n            index += data.size;\n\n            textureId = nextTexture._batchLocation;\n\n            this.addColors(colors, style.color, style.alpha, data.attribSize, data.attribStart);\n            this.addTextureIds(textureIds, textureId, data.attribSize, data.attribStart);\n        }\n\n        BaseTexture._globalBatch = TICK;\n\n        // upload..\n        // merge for now!\n        this.packAttributes();\n    }\n\n    /** Packs attributes to single buffer. */\n    protected packAttributes(): void\n    {\n        const verts = this.points;\n        const uvs = this.uvs;\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        // verts are 2 positions.. so we * by 3 as there are 6 properties.. then 4 cos its bytes\n        const glPoints = new ArrayBuffer(verts.length * 3 * 4);\n        const f32 = new Float32Array(glPoints);\n        const u32 = new Uint32Array(glPoints);\n\n        let p = 0;\n\n        for (let i = 0; i < verts.length / 2; i++)\n        {\n            f32[p++] = verts[i * 2];\n            f32[p++] = verts[(i * 2) + 1];\n\n            f32[p++] = uvs[i * 2];\n            f32[p++] = uvs[(i * 2) + 1];\n\n            u32[p++] = colors[i];\n\n            f32[p++] = textureIds[i];\n        }\n\n        this._buffer.update(glPoints);\n        this._indexBuffer.update(this.indicesUint16);\n    }\n\n    /**\n     * Process fill part of Graphics.\n     * @param data\n     */\n    protected processFill(data: GraphicsData): void\n    {\n        if (data.holes.length)\n        {\n            buildPoly.triangulate(data, this);\n        }\n        else\n        {\n            const command = FILL_COMMANDS[data.type];\n\n            command.triangulate(data, this);\n        }\n    }\n\n    /**\n     * Process line part of Graphics.\n     * @param data\n     */\n    protected processLine(data: GraphicsData): void\n    {\n        buildLine(data, this);\n\n        for (let i = 0; i < data.holes.length; i++)\n        {\n            buildLine(data.holes[i], this);\n        }\n    }\n\n    /**\n     * Process the holes data.\n     * @param holes\n     */\n    protected processHoles(holes: Array<GraphicsData>): void\n    {\n        for (let i = 0; i < holes.length; i++)\n        {\n            const hole = holes[i];\n            const command = FILL_COMMANDS[hole.type];\n\n            command.build(hole);\n\n            if (hole.matrix)\n            {\n                this.transformPoints(hole.points, hole.matrix);\n            }\n        }\n    }\n\n    /** Update the local bounds of the object. Expensive to use performance-wise. */\n    protected calculateBounds(): void\n    {\n        const bounds = this._bounds;\n\n        bounds.clear();\n        bounds.addVertexData((this.points as any), 0, this.points.length);\n        bounds.pad(this.boundsPadding, this.boundsPadding);\n    }\n\n    /**\n     * Transform points using matrix.\n     * @param points - Points to transform\n     * @param matrix - Transform matrix\n     */\n    protected transformPoints(points: Array<number>, matrix: Matrix): void\n    {\n        for (let i = 0; i < points.length / 2; i++)\n        {\n            const x = points[(i * 2)];\n            const y = points[(i * 2) + 1];\n\n            points[(i * 2)] = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n            points[(i * 2) + 1] = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n        }\n    }\n\n    /**\n     * Add colors.\n     * @param colors - List of colors to add to\n     * @param color - Color to add\n     * @param alpha - Alpha to use\n     * @param size - Number of colors to add\n     * @param offset\n     */\n    protected addColors(\n        colors: Array<number>,\n        color: number,\n        alpha: number,\n        size: number,\n        offset = 0): void\n    {\n        // TODO use the premultiply bits Ivan added\n        const rgb = (color >> 16) + (color & 0xff00) + ((color & 0xff) << 16);\n\n        const rgba =  premultiplyTint(rgb, alpha);\n\n        colors.length = Math.max(colors.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            colors[offset + i] = rgba;\n        }\n    }\n\n    /**\n     * Add texture id that the shader/fragment wants to use.\n     * @param textureIds\n     * @param id\n     * @param size\n     * @param offset\n     */\n    protected addTextureIds(\n        textureIds: Array<number>,\n        id: number,\n        size: number,\n        offset = 0): void\n    {\n        textureIds.length = Math.max(textureIds.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            textureIds[offset + i] = id;\n        }\n    }\n\n    /**\n     * Generates the UVs for a shape.\n     * @param verts - Vertices\n     * @param uvs - UVs\n     * @param texture - Reference to Texture\n     * @param start - Index buffer start index.\n     * @param size - The size/length for index buffer.\n     * @param matrix - Optional transform for all points.\n     */\n    protected addUvs(\n        verts: Array<number>,\n        uvs: Array<number>,\n        texture: Texture,\n        start: number,\n        size: number,\n        matrix: Matrix = null): void\n    {\n        let index = 0;\n        const uvsStart = uvs.length;\n        const frame = texture.frame;\n\n        while (index < size)\n        {\n            let x = verts[(start + index) * 2];\n            let y = verts[((start + index) * 2) + 1];\n\n            if (matrix)\n            {\n                const nx = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n\n                y = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n                x = nx;\n            }\n\n            index++;\n\n            uvs.push(x / frame.width, y / frame.height);\n        }\n\n        const baseTexture = texture.baseTexture;\n\n        if (frame.width < baseTexture.width\n            || frame.height < baseTexture.height)\n        {\n            this.adjustUvs(uvs, texture, uvsStart, size);\n        }\n    }\n\n    /**\n     * Modify uvs array according to position of texture region\n     * Does not work with rotated or trimmed textures\n     * @param uvs - array\n     * @param texture - region\n     * @param start - starting index for uvs\n     * @param size - how many points to adjust\n     */\n    protected adjustUvs(uvs: Array<number>, texture: Texture, start: number, size: number): void\n    {\n        const baseTexture = texture.baseTexture;\n        const eps = 1e-6;\n        const finish = start + (size * 2);\n        const frame = texture.frame;\n        const scaleX = frame.width / baseTexture.width;\n        const scaleY = frame.height / baseTexture.height;\n        let offsetX = frame.x / frame.width;\n        let offsetY = frame.y / frame.height;\n        let minX = Math.floor(uvs[start] + eps);\n        let minY = Math.floor(uvs[start + 1] + eps);\n\n        for (let i = start + 2; i < finish; i += 2)\n        {\n            minX = Math.min(minX, Math.floor(uvs[i] + eps));\n            minY = Math.min(minY, Math.floor(uvs[i + 1] + eps));\n        }\n        offsetX -= minX;\n        offsetY -= minY;\n        for (let i = start; i < finish; i += 2)\n        {\n            uvs[i] = (uvs[i] + offsetX) * scaleX;\n            uvs[i + 1] = (uvs[i + 1] + offsetY) * scaleY;\n        }\n    }\n}\n", "import { FillStyle } from './FillStyle';\nimport { LINE_JOIN, LINE_CAP } from '../const';\n\n/**\n * Represents the line style for Graphics.\n * @memberof PIXI\n */\nexport class LineStyle extends FillStyle\n{\n    /** The width (thickness) of any lines drawn. */\n    public width = 0;\n\n    /** The alignment of any lines drawn (0.5 = middle, 1 = outer, 0 = inner). WebGL only. */\n    public alignment = 0.5;\n\n    /** If true the lines will be draw using LINES instead of TRIANGLE_STRIP. */\n    public native = false;\n\n    /**\n     * Line cap style.\n     * @member {PIXI.LINE_CAP}\n     * @default PIXI.LINE_CAP.BUTT\n     */\n    public cap = LINE_CAP.BUTT;\n\n    /**\n     * Line join style.\n     * @member {PIXI.LINE_JOIN}\n     * @default PIXI.LINE_JOIN.MITER\n     */\n    public join = LINE_JOIN.MITER;\n\n    /** Miter limit. */\n    public miterLimit = 10;\n\n    /** Clones the object. */\n    public clone(): LineStyle\n    {\n        const obj = new LineStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n        obj.width = this.width;\n        obj.alignment = this.alignment;\n        obj.native = this.native;\n        obj.cap = this.cap;\n        obj.join = this.join;\n        obj.miterLimit = this.miterLimit;\n\n        return obj;\n    }\n\n    /** Reset the line style to default. */\n    public reset(): void\n    {\n        super.reset();\n\n        // Override default line style color\n        this.color = 0x0;\n\n        this.alignment = 0.5;\n        this.width = 0;\n        this.native = false;\n    }\n}\n", "import {\n    Circle,\n    Ellipse,\n    PI_2,\n    Point,\n    Polygon,\n    Rectangle,\n    RoundedRectangle,\n    Matrix,\n    SHAPES,\n} from '@pixi/math';\n\nimport type { <PERSON><PERSON><PERSON>, BatchDrawCall } from '@pixi/core';\nimport { Texture, UniformGroup, State, Shader } from '@pixi/core';\nimport { BezierUtils, QuadraticUtils, ArcUtils } from './utils';\nimport { hex2rgb } from '@pixi/utils';\nimport { GraphicsGeometry } from './GraphicsGeometry';\nimport { FillStyle } from './styles/FillStyle';\nimport { LineStyle } from './styles/LineStyle';\nimport { BLEND_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\n\nimport type { IShape, IPointData } from '@pixi/math';\nimport type { IDestroyOptions } from '@pixi/display';\nimport { LINE_JOIN, LINE_CAP } from './const';\n\n/** Batch element computed from Graphics geometry */\nexport interface IGraphicsBatchElement\n{\n    vertexData: Float32Array;\n    blendMode: BLEND_MODES;\n    indices: Uint16Array | Uint32Array;\n    uvs: Float32Array;\n    alpha: number;\n    worldAlpha: number;\n    _batchRGB: number[];\n    _tintRGB: number;\n    _texture: Texture;\n}\n\nexport interface IFillStyleOptions\n{\n    color?: number;\n    alpha?: number;\n    texture?: Texture;\n    matrix?: Matrix;\n}\n\nexport interface ILineStyleOptions extends IFillStyleOptions\n{\n    width?: number;\n    alignment?: number;\n    native?: boolean;\n    cap?: LINE_CAP;\n    join?: LINE_JOIN;\n    miterLimit?: number;\n}\n\nconst temp = new Float32Array(3);\n\n// a default shaders map used by graphics..\nconst DEFAULT_SHADERS: {[key: string]: Shader} = {};\n\nexport interface Graphics extends GlobalMixins.Graphics, Container {}\n\n/**\n * The Graphics class is primarily used to render primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.  However, you can also use a Graphics\n * object to build a list of primitives to use as a mask, or as a complex hitArea.\n *\n * Please note that due to legacy naming conventions, the behavior of some functions in this class\n * can be confusing.  Each call to `drawRect()`, `drawPolygon()`, etc. actually stores that primitive\n * in the Geometry class's GraphicsGeometry object for later use in rendering or hit testing - the\n * functions do not directly draw anything to the screen.  Similarly, the `clear()` function doesn't\n * change the screen, it simply resets the list of primitives, which can be useful if you want to\n * rebuild the contents of an existing Graphics object.\n *\n * Once a GraphicsGeometry list is built, you can re-use it in other Geometry objects as\n * an optimization, by passing it into a new Geometry object's constructor.  Because of this\n * ability, it's important to call `destroy()` on Geometry objects once you are done with them, to\n * properly dereference each GraphicsGeometry and prevent memory leaks.\n * @memberof PIXI\n */\nexport class Graphics extends Container\n{\n    /**\n     * New rendering behavior for rounded rectangles: circular arcs instead of quadratic bezier curves.\n     * In the next major release, we'll enable this by default.\n     */\n    public static nextRoundedRectBehavior = false;\n\n    /**\n     * Temporary point to use for containsPoint.\n     * @private\n     */\n    static _TEMP_POINT = new Point();\n\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Graphics objects.\n     */\n    public shader: Shader = null;\n\n    /** Renderer plugin for batching */\n    public pluginName = 'batch';\n\n    /**\n     * Current path\n     * @readonly\n     */\n    public currentPath: Polygon = null;\n\n    /** A collections of batches! These can be drawn by the renderer batch system. */\n    protected batches: Array<IGraphicsBatchElement> = [];\n\n    /** Update dirty for limiting calculating tints for batches. */\n    protected batchTint = -1;\n\n    /** Update dirty for limiting calculating batches.*/\n    protected batchDirty = -1;\n\n    /** Copy of the object vertex data. */\n    protected vertexData: Float32Array = null;\n\n    /** Current fill style. */\n    protected _fillStyle: FillStyle = new FillStyle();\n\n    /** Current line style. */\n    protected _lineStyle: LineStyle = new LineStyle();\n\n    /** Current shape transform matrix. */\n    protected _matrix: Matrix = null;\n\n    /** Current hole mode is enabled. */\n    protected _holeMode = false;\n    protected _transformID: number;\n    protected _tint: number;\n\n    /**\n     * Represents the WebGL state the Graphics required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    private state: State = State.for2d();\n    private _geometry: GraphicsGeometry;\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh or Graphics objects.\n     * @readonly\n     */\n    public get geometry(): GraphicsGeometry\n    {\n        return this._geometry;\n    }\n\n    /**\n     * @param geometry - Geometry to use, if omitted will create a new GraphicsGeometry instance.\n     */\n    constructor(geometry: GraphicsGeometry = null)\n    {\n        super();\n\n        this._geometry = geometry || new GraphicsGeometry();\n        this._geometry.refCount++;\n\n        /**\n         * When cacheAsBitmap is set to true the graphics object will be rendered as if it was a sprite.\n         * This is useful if your graphics element does not change often, as it will speed up the rendering\n         * of the object in exchange for taking up texture memory. It is also useful if you need the graphics\n         * object to be anti-aliased, because it will be rendered using canvas. This is not recommended if\n         * you are constantly redrawing the graphics element.\n         * @name cacheAsBitmap\n         * @member {boolean}\n         * @memberof PIXI.Graphics#\n         * @default false\n         */\n\n        this._transformID = -1;\n\n        // Set default\n        this.tint = 0xFFFFFF;\n        this.blendMode = BLEND_MODES.NORMAL;\n    }\n\n    /**\n     * Creates a new Graphics object with the same values as this one.\n     * Note that only the geometry of the object is cloned, not its transform (position,scale,etc)\n     * @returns - A clone of the graphics object\n     */\n    public clone(): Graphics\n    {\n        this.finishPoly();\n\n        return new Graphics(this._geometry);\n    }\n\n    /**\n     * The blend mode to be applied to the graphic shape. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.  Note that, since each\n     * primitive in the GraphicsGeometry list is rendered sequentially, modes\n     * such as `PIXI.BLEND_MODES.ADD` and `PIXI.BLEND_MODES.MULTIPLY` will\n     * be applied per-primitive.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    public get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * The tint applied to each graphic shape. This is a hex value. A value of\n     * 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    public get tint(): number\n    {\n        return this._tint;\n    }\n\n    public set tint(value: number)\n    {\n        this._tint = value;\n    }\n\n    /**\n     * The current fill style.\n     * @readonly\n     */\n    public get fill(): FillStyle\n    {\n        return this._fillStyle;\n    }\n\n    /**\n     * The current line style.\n     * @readonly\n     */\n    public get line(): LineStyle\n    {\n        return this._lineStyle;\n    }\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param [width=0] - width of the line to draw, will update the objects stored style\n     * @param [color=0x0] - color of the line to draw, will update the objects stored style\n     * @param [alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param [alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param [native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(width: number, color?: number, alpha?: number, alignment?: number, native?: boolean): this;\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param options - Line style options\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {number} [options.color=0x0] - color of the line to draw, will update the objects stored style\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(options?: ILineStyleOptions): this;\n\n    public lineStyle(options: ILineStyleOptions | number = null,\n        color = 0x0, alpha = 1, alignment = 0.5, native = false): this\n    {\n        // Support non-object params: (width, color, alpha, alignment, native)\n        if (typeof options === 'number')\n        {\n            options = { width: options, color, alpha, alignment, native } as ILineStyleOptions;\n        }\n\n        return this.lineTextureStyle(options);\n    }\n\n    /**\n     * Like line style but support texture for line fill.\n     * @param [options] - Collection of options for setting line style.\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to use\n     * @param {number} [options.color=0x0] - color of the line to draw, will update the objects stored style.\n     *  Default 0xFFFFFF if texture present.\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {PIXI.Matrix} [options.matrix=null] - Texture matrix to transform texture\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineTextureStyle(options?: ILineStyleOptions): this\n    {\n        // Apply defaults\n        options = Object.assign({\n            width: 0,\n            texture: Texture.WHITE,\n            color: (options && options.texture) ? 0xFFFFFF : 0x0,\n            alpha: 1,\n            matrix: null,\n            alignment: 0.5,\n            native: false,\n            cap: LINE_CAP.BUTT,\n            join: LINE_JOIN.MITER,\n            miterLimit: 10,\n        }, options);\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.width > 0 && options.alpha > 0;\n\n        if (!visible)\n        {\n            this._lineStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._lineStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Start a polygon object internally.\n     * @protected\n     */\n    protected startPoly(): void\n    {\n        if (this.currentPath)\n        {\n            const points = this.currentPath.points;\n            const len = this.currentPath.points.length;\n\n            if (len > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = new Polygon();\n                this.currentPath.closeStroke = false;\n                this.currentPath.points.push(points[len - 2], points[len - 1]);\n            }\n        }\n        else\n        {\n            this.currentPath = new Polygon();\n            this.currentPath.closeStroke = false;\n        }\n    }\n\n    /**\n     * Finish the polygon object.\n     * @protected\n     */\n    finishPoly(): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = null;\n            }\n            else\n            {\n                this.currentPath.points.length = 0;\n            }\n        }\n    }\n\n    /**\n     * Moves the current drawing position to x, y.\n     * @param x - the X coordinate to move to\n     * @param y - the Y coordinate to move to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public moveTo(x: number, y: number): this\n    {\n        this.startPoly();\n        this.currentPath.points[0] = x;\n        this.currentPath.points[1] = y;\n\n        return this;\n    }\n\n    /**\n     * Draws a line using the current line style from the current drawing position to (x, y);\n     * The current drawing position is then set to (x, y).\n     * @param x - the X coordinate to draw to\n     * @param y - the Y coordinate to draw to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineTo(x: number, y: number): this\n    {\n        if (!this.currentPath)\n        {\n            this.moveTo(0, 0);\n        }\n\n        // remove duplicates..\n        const points = this.currentPath.points;\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        if (fromX !== x || fromY !== y)\n        {\n            points.push(x, y);\n        }\n\n        return this;\n    }\n\n    /**\n     * Initialize the curve\n     * @param x\n     * @param y\n     */\n    protected _initCurve(x = 0, y = 0): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length === 0)\n            {\n                this.currentPath.points = [x, y];\n            }\n        }\n        else\n        {\n            this.moveTo(x, y);\n        }\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public quadraticCurveTo(cpX: number, cpY: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        const points = this.currentPath.points;\n\n        if (points.length === 0)\n        {\n            this.moveTo(0, 0);\n        }\n\n        QuadraticUtils.curveTo(cpX, cpY, toX, toY, points);\n\n        return this;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns This Graphics object. Good for chaining method calls\n     */\n    public bezierCurveTo(cpX: number, cpY: number, cpX2: number, cpY2: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        BezierUtils.curveTo(cpX, cpY, cpX2, cpY2, toX, toY, this.currentPath.points);\n\n        return this;\n    }\n\n    /**\n     * The arcTo() method creates an arc/curve between two tangents on the canvas.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @param x1 - The x-coordinate of the first tangent point of the arc\n     * @param y1 - The y-coordinate of the first tangent point of the arc\n     * @param x2 - The x-coordinate of the end of the arc\n     * @param y2 - The y-coordinate of the end of the arc\n     * @param radius - The radius of the arc\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this\n    {\n        this._initCurve(x1, y1);\n\n        const points = this.currentPath.points;\n\n        const result = ArcUtils.curveTo(x1, y1, x2, y2, radius, points);\n\n        if (result)\n        {\n            const { cx, cy, radius, startAngle, endAngle, anticlockwise } = result;\n\n            this.arc(cx, cy, radius, startAngle, endAngle, anticlockwise);\n        }\n\n        return this;\n    }\n\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arc(cx: number, cy: number, radius: number, startAngle: number, endAngle: number, anticlockwise = false): this\n    {\n        if (startAngle === endAngle)\n        {\n            return this;\n        }\n\n        if (!anticlockwise && endAngle <= startAngle)\n        {\n            endAngle += PI_2;\n        }\n        else if (anticlockwise && startAngle <= endAngle)\n        {\n            startAngle += PI_2;\n        }\n\n        const sweep = endAngle - startAngle;\n\n        if (sweep === 0)\n        {\n            return this;\n        }\n\n        const startX = cx + (Math.cos(startAngle) * radius);\n        const startY = cy + (Math.sin(startAngle) * radius);\n        const eps = this._geometry.closePointEps;\n\n        // If the currentPath exists, take its points. Otherwise call `moveTo` to start a path.\n        let points = this.currentPath ? this.currentPath.points : null;\n\n        if (points)\n        {\n            // TODO: make a better fix.\n\n            // We check how far our start is from the last existing point\n            const xDiff = Math.abs(points[points.length - 2] - startX);\n            const yDiff = Math.abs(points[points.length - 1] - startY);\n\n            if (xDiff < eps && yDiff < eps)\n            {\n                // If the point is very close, we don't add it, since this would lead to artifacts\n                // during tessellation due to floating point imprecision.\n            }\n            else\n            {\n                points.push(startX, startY);\n            }\n        }\n        else\n        {\n            this.moveTo(startX, startY);\n            points = this.currentPath.points;\n        }\n\n        ArcUtils.arc(startX, startY, cx, cy, radius, startAngle, endAngle, anticlockwise, points);\n\n        return this;\n    }\n\n    /**\n     * Specifies a simple one-color fill that subsequent calls to other Graphics methods\n     * (such as lineTo() or drawCircle()) use when drawing.\n     * @param color - the color of the fill\n     * @param alpha - the alpha of the fill\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public beginFill(color = 0, alpha = 1): this\n    {\n        return this.beginTextureFill({ texture: Texture.WHITE, color, alpha });\n    }\n\n    /**\n     * Begin the texture fill\n     * @param options - Object object.\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to fill\n     * @param {number} [options.color=0xffffff] - Background to fill behind texture\n     * @param {number} [options.alpha=1] - Alpha of fill\n     * @param {PIXI.Matrix} [options.matrix=null] - Transform matrix\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    beginTextureFill(options?: IFillStyleOptions): this\n    {\n        // Apply defaults\n        options = Object.assign({\n            texture: Texture.WHITE,\n            color: 0xFFFFFF,\n            alpha: 1,\n            matrix: null,\n        }, options) as IFillStyleOptions;\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.alpha > 0;\n\n        if (!visible)\n        {\n            this._fillStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._fillStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Applies a fill to the lines and shapes that were added since the last call to the beginFill() method.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public endFill(): this\n    {\n        this.finishPoly();\n\n        this._fillStyle.reset();\n\n        return this;\n    }\n\n    /**\n     * Draws a rectangle shape.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRect(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Rectangle(x, y, width, height));\n    }\n\n    /**\n     * Draw a rectangle shape with rounded/beveled corners.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @param radius - Radius of the rectangle corners\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRoundedRect(x: number, y: number, width: number, height: number, radius: number): this\n    {\n        return this.drawShape(new RoundedRectangle(x, y, width, height, radius));\n    }\n\n    /**\n     * Draws a circle.\n     * @param x - The X coordinate of the center of the circle\n     * @param y - The Y coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawCircle(x: number, y: number, radius: number): this\n    {\n        return this.drawShape(new Circle(x, y, radius));\n    }\n\n    /**\n     * Draws an ellipse.\n     * @param x - The X coordinate of the center of the ellipse\n     * @param y - The Y coordinate of the center of the ellipse\n     * @param width - The half width of the ellipse\n     * @param height - The half height of the ellipse\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawEllipse(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Ellipse(x, y, width, height));\n    }\n\n    public drawPolygon(...path: Array<number> | Array<IPointData>): this;\n    public drawPolygon(path: Array<number> | Array<IPointData> | Polygon): this;\n\n    /**\n     * Draws a polygon using the given path.\n     * @param {number[]|PIXI.IPointData[]|PIXI.Polygon} path - The path data used to construct the polygon.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawPolygon(...path: any[]): this\n    {\n        let points: Array<number> | Array<IPointData>;\n        let closeStroke = true;// !!this._fillStyle;\n\n        const poly = path[0] as Polygon;\n\n        // check if data has points..\n        if (poly.points)\n        {\n            closeStroke = poly.closeStroke;\n            points = poly.points;\n        }\n        else\n        if (Array.isArray(path[0]))\n        {\n            points = path[0];\n        }\n        else\n        {\n            points = path;\n        }\n\n        const shape = new Polygon(points);\n\n        shape.closeStroke = closeStroke;\n\n        this.drawShape(shape);\n\n        return this;\n    }\n\n    /**\n     * Draw any shape.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - Shape to draw\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawShape(shape: IShape): this\n    {\n        if (!this._holeMode)\n        {\n            this._geometry.drawShape(\n                shape,\n                this._fillStyle.clone(),\n                this._lineStyle.clone(),\n                this._matrix\n            );\n        }\n        else\n        {\n            this._geometry.drawHole(shape, this._matrix);\n        }\n\n        return this;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public clear(): this\n    {\n        this._geometry.clear();\n        this._lineStyle.reset();\n        this._fillStyle.reset();\n\n        this._boundsID++;\n        this._matrix = null;\n        this._holeMode = false;\n        this.currentPath = null;\n\n        return this;\n    }\n\n    /**\n     * True if graphics consists of one rectangle, and thus, can be drawn like a Sprite and\n     * masked with gl.scissor.\n     * @returns - True if only 1 rect.\n     */\n    public isFastRect(): boolean\n    {\n        const data = this._geometry.graphicsData;\n\n        return data.length === 1\n            && data[0].shape.type === SHAPES.RECT\n            && !data[0].matrix\n            && !data[0].holes.length\n            && !(data[0].lineStyle.visible && data[0].lineStyle.width);\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n        // batch part..\n        // batch it!\n\n        geometry.updateBatches();\n\n        if (geometry.batchable)\n        {\n            if (this.batchDirty !== geometry.batchDirty)\n            {\n                this._populateBatches();\n            }\n\n            this._renderBatched(renderer);\n        }\n        else\n        {\n            // no batching...\n            renderer.batch.flush();\n\n            this._renderDirect(renderer);\n        }\n    }\n\n    /** Populating batches for rendering. */\n    protected _populateBatches(): void\n    {\n        const geometry = this._geometry;\n        const blendMode = this.blendMode;\n        const len = geometry.batches.length;\n\n        this.batchTint = -1;\n        this._transformID = -1;\n        this.batchDirty = geometry.batchDirty;\n        this.batches.length = len;\n\n        this.vertexData = new Float32Array(geometry.points);\n\n        for (let i = 0; i < len; i++)\n        {\n            const gI = geometry.batches[i];\n            const color = gI.style.color;\n            const vertexData = new Float32Array(this.vertexData.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const uvs = new Float32Array(geometry.uvsFloat32.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const indices = new Uint16Array(geometry.indicesUint16.buffer,\n                gI.start * 2,\n                gI.size);\n\n            const batch = {\n                vertexData,\n                blendMode,\n                indices,\n                uvs,\n                _batchRGB: hex2rgb(color) as Array<number>,\n                _tintRGB: color,\n                _texture: gI.style.texture,\n                alpha: gI.style.alpha,\n                worldAlpha: 1 };\n\n            this.batches[i] = batch;\n        }\n    }\n\n    /**\n     * Renders the batches using the BathedRenderer plugin\n     * @param renderer - The renderer\n     */\n    protected _renderBatched(renderer: Renderer): void\n    {\n        if (!this.batches.length)\n        {\n            return;\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n\n        this.calculateVertices();\n        this.calculateTints();\n\n        for (let i = 0, l = this.batches.length; i < l; i++)\n        {\n            const batch = this.batches[i];\n\n            batch.worldAlpha = this.worldAlpha * batch.alpha;\n\n            renderer.plugins[this.pluginName].render(batch);\n        }\n    }\n\n    /**\n     * Renders the graphics direct\n     * @param renderer - The renderer\n     */\n    protected _renderDirect(renderer: Renderer): void\n    {\n        const shader = this._resolveDirectShader(renderer);\n\n        const geometry = this._geometry;\n        const tint = this.tint;\n        const worldAlpha = this.worldAlpha;\n        const uniforms = shader.uniforms;\n        const drawCalls = geometry.drawCalls;\n\n        // lets set the transfomr\n        uniforms.translationMatrix = this.transform.worldTransform;\n\n        // and then lets set the tint..\n        uniforms.tint[0] = (((tint >> 16) & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[1] = (((tint >> 8) & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[2] = ((tint & 0xFF) / 255) * worldAlpha;\n        uniforms.tint[3] = worldAlpha;\n\n        // the first draw call, we can set the uniforms of the shader directly here.\n\n        // this means that we can tack advantage of the sync function of pixi!\n        // bind and sync uniforms..\n        // there is a way to optimise this..\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(geometry, shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // then render the rest of them...\n        for (let i = 0, l = drawCalls.length; i < l; i++)\n        {\n            this._renderDrawCallDirect(renderer, geometry.drawCalls[i]);\n        }\n    }\n\n    /**\n     * Renders specific DrawCall\n     * @param renderer\n     * @param drawCall\n     */\n    protected _renderDrawCallDirect(renderer: Renderer, drawCall: BatchDrawCall): void\n    {\n        const { texArray, type, size, start } = drawCall;\n        const groupTextureCount = texArray.count;\n\n        for (let j = 0; j < groupTextureCount; j++)\n        {\n            renderer.texture.bind(texArray.elements[j], j);\n        }\n\n        renderer.geometry.draw(type, size, start);\n    }\n\n    /**\n     * Resolves shader for direct rendering\n     * @param renderer - The renderer\n     */\n    protected _resolveDirectShader(renderer: Renderer): Shader\n    {\n        let shader = this.shader;\n\n        const pluginName = this.pluginName;\n\n        if (!shader)\n        {\n            // if there is no shader here, we can use the default shader.\n            // and that only gets created if we actually need it..\n            // but may be more than one plugins for graphics\n            if (!DEFAULT_SHADERS[pluginName])\n            {\n                const { MAX_TEXTURES } = renderer.plugins[pluginName];\n                const sampleValues = new Int32Array(MAX_TEXTURES);\n\n                for (let i = 0; i < MAX_TEXTURES; i++)\n                {\n                    sampleValues[i] = i;\n                }\n\n                const uniforms = {\n                    tint: new Float32Array([1, 1, 1, 1]),\n                    translationMatrix: new Matrix(),\n                    default: UniformGroup.from({ uSamplers: sampleValues }, true),\n                };\n\n                const program = renderer.plugins[pluginName]._shader.program;\n\n                DEFAULT_SHADERS[pluginName] = new Shader(program, uniforms);\n            }\n\n            shader = DEFAULT_SHADERS[pluginName];\n        }\n\n        return shader;\n    }\n\n    /** Retrieves the bounds of the graphic shape as a rectangle object. */\n    protected _calculateBounds(): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n\n        // skipping when graphics is empty, like a container\n        if (!geometry.graphicsData.length)\n        {\n            return;\n        }\n\n        const { minX, minY, maxX, maxY } = geometry.bounds;\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Tests if a point is inside this graphics object\n     * @param point - the point to test\n     * @returns - the result of the test\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, Graphics._TEMP_POINT);\n\n        return this._geometry.containsPoint(Graphics._TEMP_POINT);\n    }\n\n    /** Recalculate the tint by applying tint to batches using Graphics tint. */\n    protected calculateTints(): void\n    {\n        if (this.batchTint !== this.tint)\n        {\n            this.batchTint = this.tint;\n\n            const tintRGB = hex2rgb(this.tint, temp);\n\n            for (let i = 0; i < this.batches.length; i++)\n            {\n                const batch = this.batches[i];\n\n                const batchTint = batch._batchRGB;\n\n                const r = (tintRGB[0] * batchTint[0]) * 255;\n                const g = (tintRGB[1] * batchTint[1]) * 255;\n                const b = (tintRGB[2] * batchTint[2]) * 255;\n\n                // TODO Ivan, can this be done in one go?\n                const color = (r << 16) + (g << 8) + (b | 0);\n\n                batch._tintRGB = (color >> 16)\n                        + (color & 0xff00)\n                        + ((color & 0xff) << 16);\n            }\n        }\n    }\n\n    /** If there's a transform update or a change to the shape of the geometry, recalculate the vertices. */\n    protected calculateVertices(): void\n    {\n        const wtID = this.transform._worldID;\n\n        if (this._transformID === wtID)\n        {\n            return;\n        }\n\n        this._transformID = wtID;\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const data = this._geometry.points;// batch.vertexDataOriginal;\n        const vertexData = this.vertexData;\n\n        let count = 0;\n\n        for (let i = 0; i < data.length; i += 2)\n        {\n            const x = data[i];\n            const y = data[i + 1];\n\n            vertexData[count++] = (a * x) + (c * y) + tx;\n            vertexData[count++] = (d * y) + (b * x) + ty;\n        }\n    }\n\n    /**\n     * Closes the current path.\n     * @returns - Returns itself.\n     */\n    public closePath(): this\n    {\n        const currentPath = this.currentPath;\n\n        if (currentPath)\n        {\n            // we don't need to add extra point in the end because buildLine will take care of that\n            currentPath.closeStroke = true;\n            // ensure that the polygon is completed, and is available for hit detection\n            // (even if the graphics is not rendered yet)\n            this.finishPoly();\n        }\n\n        return this;\n    }\n\n    /**\n     * Apply a matrix to the positional data.\n     * @param matrix - Matrix to use for transform current shape.\n     * @returns - Returns itself.\n     */\n    public setMatrix(matrix: Matrix): this\n    {\n        this._matrix = matrix;\n\n        return this;\n    }\n\n    /**\n     * Begin adding holes to the last draw shape\n     * IMPORTANT: holes must be fully inside a shape to work\n     * Also weirdness ensues if holes overlap!\n     * Ellipses, Circles, Rectangles and Rounded Rectangles cannot be holes or host for holes in CanvasRenderer,\n     * please use `moveTo` `lineTo`, `quadraticCurveTo` if you rely on pixi-legacy bundle.\n     * @returns - Returns itself.\n     */\n    public beginHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = true;\n\n        return this;\n    }\n\n    /**\n     * End adding holes to the last draw shape.\n     * @returns - Returns itself.\n     */\n    public endHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = false;\n\n        return this;\n    }\n\n    /**\n     * Destroys the Graphics object.\n     * @param options - Options parameter. A boolean will act as if all\n     *  options have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have\n     *  their destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this._geometry.refCount--;\n        if (this._geometry.refCount === 0)\n        {\n            this._geometry.dispose();\n        }\n\n        this._matrix = null;\n        this.currentPath = null;\n        this._lineStyle.destroy();\n        this._lineStyle = null;\n        this._fillStyle.destroy();\n        this._fillStyle = null;\n        this._geometry = null;\n        this.shader = null;\n        this.vertexData = null;\n        this.batches.length = 0;\n        this.batches = null;\n\n        super.destroy(options);\n    }\n}\n", "export * from './const';\nexport * from './styles/FillStyle';\nexport * from './Graphics';\nexport * from './GraphicsData';\nexport * from './GraphicsGeometry';\nexport * from './styles/LineStyle';\n\nimport {\n    buildPoly,\n    buildCircle,\n    buildRectangle,\n    buildRoundedRectangle,\n    buildLine,\n    ArcUtils,\n    BezierUtils,\n    QuadraticUtils,\n    BatchPart,\n    FILL_COMMANDS,\n    BATCH_POOL,\n    DRAW_CALL_POOL\n} from './utils';\nimport type { BatchDrawCall } from '@pixi/core/';\nimport type { IShapeBuildCommand } from './utils/IShapeBuildCommand';\nimport type { SHAPES } from '@pixi/math';\n\nexport const graphicsUtils = {\n    buildPoly: buildPoly as IShapeBuildCommand,\n    buildCircle: buildCircle as IShapeBuildCommand,\n    buildRectangle: buildRectangle as IShapeBuildCommand,\n    buildRoundedRectangle: buildRoundedRectangle as IShapeBuildCommand,\n    buildLine,\n    ArcUtils,\n    BezierUtils,\n    QuadraticUtils,\n    BatchPart,\n    FILL_COMMANDS: FILL_COMMANDS as Record<SHAPES, IShapeBuildCommand>,\n    BATCH_POOL: BATCH_POOL as Array<BatchPart>,\n    DRAW_CALL_POOL: DRAW_CALL_POOL as Array<BatchDrawCall>\n};\n"], "names": ["LINE_JOIN", "LINE_CAP", "Texture", "arguments", "earcut", "SHAPES", "Point", "PI_2", "Bounds", "WRAP_MODES", "BaseTexture", "BatchDrawCall", "BatchTextureArray", "DRAW_MODES", "premultiplyTint", "BatchGeometry", "State", "BLEND_MODES", "Polygon", "Rectangle", "RoundedRectangle", "Circle", "Ellipse", "hex2rgb", "Matrix", "UniformGroup", "Shader", "Container"], "mappings": ";;;;;;;;;;;IAAA;;;;;;;;;;;IAWG;AACSA,+BAMX;IAND,CAAA,UAAY,SAAS,EAAA;IAGjB,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACnB,CAAC,EANWA,iBAAS,KAATA,iBAAS,GAMpB,EAAA,CAAA,CAAA,CAAA;IAED;;;;;;;;;;IAUG;AACSC,8BAMX;IAND,CAAA,UAAY,QAAQ,EAAA;IAGhB,IAAA,QAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;IACb,IAAA,QAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,IAAA,QAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;IACrB,CAAC,EANWA,gBAAQ,KAARA,gBAAQ,GAMnB,EAAA,CAAA,CAAA,CAAA;IAcD;;;;;;;;;;;;;IAaG;AACI,QAAM,eAAe,GAA4B;IACpD,IAAA,QAAQ,EAAE,IAAI;IACd,IAAA,SAAS,EAAE,EAAE;IACb,IAAA,WAAW,EAAE,CAAC;IACd,IAAA,WAAW,EAAG,IAAI;IAElB,IAAA,OAAO,EAAE,MAAM;IAEf,IAAA,cAAc,EAAd,UAAe,MAAc,EAAE,eAAoB,EAAA;IAApB,QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAoB,GAAA,EAAA,CAAA,EAAA;IAE/C,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,EAC9C;IACI,YAAA,OAAO,eAAe,CAAC;IAC1B,SAAA;IAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;IAEhD,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,EAC7B;IACI,YAAA,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;IAC7B,SAAA;IACI,aAAA,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,EAClC;IACI,YAAA,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;IAC7B,SAAA;IAED,QAAA,OAAO,MAAM,CAAC;SACjB;;;ICzFL;;;IAGG;AACH,QAAA,SAAA,kBAAA,YAAA;IA0BI,IAAA,SAAA,SAAA,GAAA;IAxBA;;;IAGG;YACI,IAAK,CAAA,KAAA,GAAG,QAAQ,CAAC;;YAGjB,IAAK,CAAA,KAAA,GAAG,GAAG,CAAC;IAEnB;;;IAGG;IACI,QAAA,IAAA,CAAA,OAAO,GAAYC,YAAO,CAAC,KAAK,CAAC;IAExC;;;IAGG;YACI,IAAM,CAAA,MAAA,GAAW,IAAI,CAAC;;YAGtB,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;YAInB,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;;IAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,IAAM,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;IAE5B,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC3B,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IACzB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAE3B,QAAA,OAAO,GAAG,CAAC;SACd,CAAA;;IAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACtB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACf,QAAA,IAAI,CAAC,OAAO,GAAGA,YAAO,CAAC,KAAK,CAAC;IAC7B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB,CAAA;;IAGM,IAAA,SAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;IAEI,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB,CAAA;QACL,OAAC,SAAA,CAAA;IAAD,CAAC,EAAA;;ICpED;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGC,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;ICpNA,SAAS,cAAc,CAAC,MAAgB,EAAE,IAAY,EAAA;;IAAZ,IAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAY,GAAA,KAAA,CAAA,EAAA;IAElD,IAAA,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAExB,IAAI,CAAC,GAAG,CAAC,EACT;YACI,OAAO;IACV,KAAA;QAED,IAAI,IAAI,GAAG,CAAC,CAAC;IAEb,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EACrE;IACI,QAAA,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEzB,QAAA,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YAE9B,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;IACX,KAAA;IAED,IAAA,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,EAC9C;IACI,QAAA,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEhB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EACvC;IACI,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrB,IAAM,EAAE,GAAG,CAAC,CAAC;IACb,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBAEjB,EAA2B,GAAA,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAlD,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,CAA6B;gBACpD,EAA2B,GAAA,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAlD,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,MAAM,CAAC,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,CAAA,CAA6B;IACvD,SAAA;IACJ,KAAA;IACL,CAAC;IACD;;;;;;;;;IASG;IACI,IAAM,SAAS,GAAuB;QAEzC,KAAK,EAAL,UAAM,YAAY,EAAA;YAEd,YAAY,CAAC,MAAM,GAAI,YAAY,CAAC,KAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SACxE;QAED,WAAW,EAAA,UAAC,YAAY,EAAE,gBAAgB,EAAA;IAEtC,QAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACjC,QAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;IACjC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACtC,QAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;IAEzC,QAAA,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EACtB;IACI,YAAA,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBAE9B,IAAM,SAAS,GAAG,EAAE,CAAC;;IAGrB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;IACI,gBAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAEtB,gBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBAElC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAClC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACvC,aAAA;;gBAGD,IAAM,SAAS,GAAGC,YAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;gBAE/C,IAAI,CAAC,SAAS,EACd;oBACI,OAAO;IACV,aAAA;IAED,YAAA,IAAM,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAEjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAC5C;oBACI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;IACrC,gBAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;IACzC,gBAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;IAC5C,aAAA;IAED,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EACtC;oBACI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,aAAA;IACJ,SAAA;SACJ;KACJ;;IC3GD;IAMA;;;;;;;;;IASG;IACI,IAAM,WAAW,GAAuB;QAE3C,KAAK,EAAL,UAAM,YAAY,EAAA;;IAGd,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IAEnC,QAAA,IAAI,CAAC,CAAC;IACN,QAAA,IAAI,CAAC,CAAC;IACN,QAAA,IAAI,EAAE,CAAC;IACP,QAAA,IAAI,EAAE,CAAC;IACP,QAAA,IAAI,EAAE,CAAC;IACP,QAAA,IAAI,EAAE,CAAC;IAEP,QAAA,IAAI,YAAY,CAAC,IAAI,KAAKC,WAAM,CAAC,IAAI,EACrC;IACI,YAAA,IAAM,MAAM,GAAG,YAAY,CAAC,KAAe,CAAC;IAE5C,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACb,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACb,YAAA,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC;IACxB,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACf,SAAA;IACI,aAAA,IAAI,YAAY,CAAC,IAAI,KAAKA,WAAM,CAAC,IAAI,EAC1C;IACI,YAAA,IAAM,OAAO,GAAG,YAAY,CAAC,KAAgB,CAAC;IAE9C,YAAA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IACd,YAAA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IACd,YAAA,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;IACnB,YAAA,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;IACpB,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACf,SAAA;IAED,aAAA;IACI,YAAA,IAAM,WAAW,GAAG,YAAY,CAAC,KAAyB,CAAC;IAC3D,YAAA,IAAM,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;IACxC,YAAA,IAAM,UAAU,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAE1C,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,SAAS,CAAC;IAC9B,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,UAAU,CAAC;gBAC/B,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;IACrF,YAAA,EAAE,GAAG,SAAS,GAAG,EAAE,CAAC;IACpB,YAAA,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;IACxB,SAAA;IAED,QAAA,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAC/C;IACI,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;gBAElB,OAAO;IACV,SAAA;;IAGD,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9C,QAAA,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAEhD,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAElB,IAAI,CAAC,KAAK,CAAC,EACX;gBACI,OAAO;IACV,SAAA;YAED,IAAI,CAAC,KAAK,CAAC,EACX;IACI,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAC/B,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAC/B,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAC/B,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gBAE/B,OAAO;IACV,SAAA;YAED,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,EAAE,GAAG,EAAE,CAAC;YACZ,IAAI,EAAE,GAAG,CAAC,CAAC;IAEX,QAAA;IACI,YAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;gBACnB,IAAM,EAAE,GAAG,EAAE,CAAC;IACd,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAElB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAElB,YAAA,IAAI,EAAE,EACN;IACI,gBAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAElB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IACrB,aAAA;IACJ,SAAA;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC1B;IACI,YAAA,IAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAChC,YAAA,IAAM,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IACnC,YAAA,IAAM,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IACnC,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAElB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IACrB,SAAA;IAED,QAAA;gBACI,IAAM,EAAE,GAAG,EAAE,CAAC;IACd,YAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAElB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,YAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAElB,YAAA,IAAI,EAAE,EACN;IACI,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,gBAAA,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;IACrB,aAAA;IACJ,SAAA;SACJ;IAED,IAAA,WAAW,EAAX,UAAY,YAAY,EAAE,gBAAgB,EAAA;IAEtC,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACnC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACtC,QAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;IAEzC,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EACvB;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YAC/B,IAAM,MAAM,GAAG,OAAO,CAAC;IAEvB,QAAA,IAAI,CAAC,CAAC;IACN,QAAA,IAAI,CAAC,CAAC;IAEN,QAAA,IAAI,YAAY,CAAC,IAAI,KAAKA,WAAM,CAAC,IAAI,EACrC;IACI,YAAA,IAAM,MAAM,GAAG,YAAY,CAAC,KAAe,CAAC;IAE5C,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACb,YAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAChB,SAAA;IAED,aAAA;IACI,YAAA,IAAM,WAAW,GAAG,YAAY,CAAC,KAAyB,CAAC;IAE3D,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IAC5C,YAAA,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAChD,SAAA;IAED,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;;IAGnC,QAAA,KAAK,CAAC,IAAI,CACN,YAAY,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,EACrE,YAAY,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAE3E,QAAA,OAAO,EAAE,CAAC;IAEV,QAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EACzC;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;gBAGrC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC5C,SAAA;YAED,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;SAC7C;KACJ;;ICpND;;;;;;;;;IASG;IACI,IAAM,cAAc,GAAuB;QAE9C,KAAK,EAAL,UAAM,YAAY,EAAA;;;;IAKd,QAAA,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAkB,CAAC;IACjD,QAAA,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IACrB,QAAA,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IACrB,QAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC7B,QAAA,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAE/B,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IAEnC,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAElB,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;SACtB;QAED,WAAW,EAAA,UAAC,YAAY,EAAE,gBAAgB,EAAA;IAEtC,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACnC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAEtC,QAAA,IAAM,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAEjC,QAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAC3B,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EACpB,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EACpB,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1B,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAC3D,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;SAC9C;KACJ;;IC3CD;;;;;;;;;;IAUG;IACH,SAAS,KAAK,CAAC,EAAU,EAAE,EAAU,EAAE,IAAY,EAAA;IAE/C,IAAA,IAAM,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAErB,IAAA,OAAO,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;;;;;;;;;;;IAeG;IACH,SAAS,oBAAoB,CACzB,KAAa,EAAE,KAAa,EAC5B,GAAW,EAAE,GAAW,EACxB,GAAW,EAAE,GAAW,EACxB,GAAuB,EAAA;IAAvB,IAAA,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAuB,GAAA,EAAA,CAAA,EAAA;QAEvB,IAAM,CAAC,GAAG,EAAE,CAAC;QACb,IAAM,MAAM,GAAG,GAAG,CAAC;QAEnB,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAClC;IACI,QAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;YAGV,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC1B,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC1B,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACxB,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;;YAGxB,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YACrB,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;;YAGrB,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EACjF;gBACI,SAAS;IACZ,SAAA;IAED,QAAA,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,KAAA;IAED,IAAA,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;;;IASG;IACI,IAAM,qBAAqB,GAAuB;QAErD,KAAK,EAAL,UAAM,YAAY,EAAA;YAEd,IAAI,QAAQ,CAAC,uBAAuB,EACpC;IACI,YAAA,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAEhC,OAAO;IACV,SAAA;IAED,QAAA,IAAM,SAAS,GAAG,YAAY,CAAC,KAAyB,CAAC;IACzD,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACnC,QAAA,IAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;IACtB,QAAA,IAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;IACtB,QAAA,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;IAC9B,QAAA,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;;IAGhC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEpF,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;;YAGlB,IAAI,CAAC,MAAM,EACX;gBACI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,EACZ,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;IACtB,SAAA;IAED,aAAA;IACI,YAAA,oBAAoB,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,EAC9B,CAAC,EAAE,CAAC,EACJ,CAAC,GAAG,MAAM,EAAE,CAAC,EACb,MAAM,CAAC,CAAC;gBACZ,oBAAoB,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,EACnC,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EACf,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,MAAM,CAAC,CAAC;IACZ,YAAA,oBAAoB,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,GAAG,MAAM,EAC/C,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EACrB,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAC9B,MAAM,CAAC,CAAC;gBACZ,oBAAoB,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EACvC,CAAC,EAAE,CAAC,GAAG,MAAM,EACb,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,MAAM,EACtB,MAAM,CAAC,CAAC;IACf,SAAA;SACJ;QAED,WAAW,EAAA,UAAC,YAAY,EAAE,gBAAgB,EAAA;YAEtC,IAAI,QAAQ,CAAC,uBAAuB,EACpC;IACI,YAAA,WAAW,CAAC,WAAW,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;gBAExD,OAAO;IACV,SAAA;IAED,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IAEnC,QAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACtC,QAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;IAEzC,QAAA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YAEhC,IAAM,SAAS,GAAGD,YAAM,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAE1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EACnD;gBACI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;;IAEpC,YAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;;IAExC,YAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IAC3C,SAAA;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC7C;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtC,SAAA;SACJ;KACJ;;IC3KD;;;;;;;;;;;;;;;IAeG;IACH,SAAS,MAAM,CACX,CAAS,EACT,CAAS,EACT,EAAU,EACV,EAAU,EACV,WAAmB,EACnB,WAAmB,EACnB,SAAkB,mEAClB,KAAoB,EAAA;QAGpB,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;QAClC,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;QAClC,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;QAClC,IAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC;;IAGlC,IAAA,IAAI,GAAG,CAAC;IAAC,IAAA,IACL,GAAG,CAAC;IAER,IAAA,IAAI,SAAS,EACb;YACI,GAAG,GAAG,EAAE,CAAC;YACT,GAAG,GAAG,CAAC,EAAE,CAAC;IACb,KAAA;IAED,SAAA;YACI,GAAG,GAAG,CAAC,EAAE,CAAC;YACV,GAAG,GAAG,EAAE,CAAC;IACZ,KAAA;;IAGD,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;IACrB,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;IACrB,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;IACrB,IAAA,IAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;;IAGrB,IAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACrB,IAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAErB,IAAA,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;;;;;;;;;;;IAeG;IACH,SAAS,KAAK,CACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,KAAoB,EACpB,SAAkB,EAAA;IAGlB,IAAA,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;IACvB,IAAA,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;QAEvB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACxC,IAAA,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAE1C,IAAA,IAAI,SAAS,IAAI,MAAM,GAAG,MAAM,EAChC;IACI,QAAA,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACzB,KAAA;IACI,SAAA,IAAI,CAAC,SAAS,IAAI,MAAM,GAAG,MAAM,EACtC;IACI,QAAA,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACzB,KAAA;QAED,IAAI,UAAU,GAAG,MAAM,CAAC;IACxB,IAAA,IAAM,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC;QAClC,IAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAEzC;;;;;;;;;;;;;;;;IAgBG;IAEH,IAAA,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;QAChE,IAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9E,IAAA,IAAM,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;QAEtC,UAAU,IAAI,QAAQ,CAAC;IAEvB,IAAA,IAAI,SAAS,EACb;IACI,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,QAAQ,EACxE;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACnB,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,EACxC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC;IAC1C,SAAA;IAED,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACtB,KAAA;IAED,SAAA;IACI,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,QAAQ,EACxE;IACI,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,EACxC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC;IACvC,YAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACtB,SAAA;IAED,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACnB,QAAA,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACtB,KAAA;QAED,OAAO,QAAQ,GAAG,CAAC,CAAC;IACxB,CAAC;IAED;;;;;;;;IAQG;IACH,SAAS,kBAAkB,CAAC,YAA0B,EAAE,gBAAkC,EAAA;IAEtF,IAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAgB,CAAC;IAC5C,IAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACzD,IAAA,IAAM,GAAG,GAAG,gBAAgB,CAAC,aAAa,CAAC;IAE3C,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EACvB;YACI,OAAO;IACV,KAAA;;;;;;;;;;IAWD,IAAA,IAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;;IAGrC,IAAA,IAAM,UAAU,GAAG,IAAIE,UAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,IAAM,SAAS,GAAG,IAAIA,UAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAClF,IAAA,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAKD,WAAM,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC;IACpE,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;IACtD,WAAA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;;IAGlD,IAAA,IAAI,WAAW,EACf;;IAEI,QAAA,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAExB,QAAA,IAAI,UAAU,EACd;gBACI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACb,MAAM,CAAC,GAAG,EAAE,CAAC;gBACb,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACvE,SAAA;IAED,QAAA,IAAM,SAAS,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,GAAG,CAAC;IACrD,QAAA,IAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,GAAG,CAAC;IAErD,QAAA,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACrC,QAAA,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACrC,KAAA;IAED,IAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACtC,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACjC,IAAA,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;IAC/B,IAAA,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;;IAGpC,IAAA,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;IAC9B,IAAA,IAAM,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;QACnC,IAAM,iBAAiB,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;;IAG9D,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;;QAGX,IAAI,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACvB,IAAA,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;QACpB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,IAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;QAExD,KAAK,IAAI,IAAI,CAAC;QACd,KAAK,IAAI,IAAI,CAAC;QACd,KAAK,IAAI,KAAK,CAAC;QACf,KAAK,IAAI,KAAK,CAAC;IAEf,IAAA,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC;QAC9B,IAAM,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;IACpC,IAAA,IAAM,WAAW,GAAG,KAAK,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,WAAW,EAChB;IACI,QAAA,IAAI,KAAK,CAAC,GAAG,KAAKJ,gBAAQ,CAAC,KAAK,EAChC;IACI,YAAA,UAAU,IAAI,KAAK,CACf,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,KAAK,EACL,IAAI,CACP,GAAG,CAAC,CAAC;IACT,SAAA;IACI,aAAA,IAAI,KAAK,CAAC,GAAG,KAAKA,gBAAQ,CAAC,MAAM,EACtC;IACI,YAAA,UAAU,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACrF,SAAA;IACJ,KAAA;;IAGD,IAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;IAChC,IAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;IAEhC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EACnC;YACI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,QAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAE/B,QAAA,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACnB,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YAEzB,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,QAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAE/B,QAAA,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACnB,QAAA,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;IAEhB,QAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;YACpD,KAAK,IAAI,IAAI,CAAC;YACd,KAAK,IAAI,IAAI,CAAC;YACd,KAAK,IAAI,KAAK,CAAC;YACf,KAAK,IAAI,KAAK,CAAC;IAEf,QAAA,MAAM,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACpB,QAAA,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;IAEjB,QAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;YACxD,MAAM,IAAI,IAAI,CAAC;YACf,MAAM,IAAI,IAAI,CAAC;YACf,MAAM,IAAI,KAAK,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC;;IAGhB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACpB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACpB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACpB,QAAA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;;IAGpB,QAAA,IAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;;IAEtC,QAAA,IAAM,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;IACxC,QAAA,IAAM,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;;;IAI9B,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAC3C;IACI,YAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;IAChC,YAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;;gBAGhC,IAAI,GAAG,IAAI,CAAC,EACZ;IACI,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAKD,iBAAS,CAAC,KAAK,EAClC;wBACI,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IACzB,iBAAA;IAED,qBAAA;wBACI,UAAU,IAAI,CAAC,CAAC;IACnB,iBAAA;IAED,gBAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAC3B,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;IACjC,gBAAA,KAAK,CAAC,IAAI,CACN,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAC3B,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;IACpC,aAAA;gBAED,SAAS;IACZ,SAAA;;IAGD,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;IAC7E,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;IACjF,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;IAC7C,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;IAC7C,QAAA,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;;IAGhE,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;IAC3C,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;;IAE3C,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;IAC3C,QAAA,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,CAAC,CAAC;;IAG3C,QAAA,IAAM,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC9F,IAAM,YAAY,GAAG,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC;YAC3D,IAAM,uBAAuB,GAAG,sBAAsB,IAAI,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC,CAAC;IACtG,QAAA,IAAM,aAAa,GAAG,KAAK,IAAI,uBAAuB,CAAC;IAEvD,QAAA,IAAI,aAAa,EACjB;IACI,YAAA,IAAI,KAAK,CAAC,IAAI,KAAKA,iBAAS,CAAC,KAAK,IAAI,KAAK,GAAG,YAAY,GAAG,iBAAiB,EAC9E;oBACI,IAAI,SAAS,gCACb;wBACI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBACrB,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;wBACnE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBACrB,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;IACxE,iBAAA;IACI,mDACL;wBACI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;wBACnE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBACrB,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;wBACrE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACxB,iBAAA;oBAED,UAAU,IAAI,CAAC,CAAC;IACnB,aAAA;IACI,iBAAA,IAAI,KAAK,CAAC,IAAI,KAAKA,iBAAS,CAAC,KAAK,EACvC;oBACI,IAAI,SAAS,uBACb;IACI,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACrB,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;wBAEnE,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,IAAI,CACd,GAAG,CAAC,CAAC;IAEN,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACrB,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;IACxE,iBAAA;IACI,yCACL;IACI,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;IACnE,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBAErB,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,KAAK,CACf,GAAG,CAAC,CAAC;IAEN,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;IACrE,oBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACxB,iBAAA;IACJ,aAAA;IAED,iBAAA;IACI,gBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACrB,gBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACxB,aAAA;IACJ,SAAA;;IAED,SAAA;gBACI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBACnE,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;IACnE,YAAA,IAAI,KAAK,CAAC,IAAI,KAAKA,iBAAS,CAAC,KAAK,EAClC;oBACI,IAAI,SAAS,uBACb;wBACI,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,IAAI,CACd,GAAG,CAAC,CAAC;IACT,iBAAA;IACI,yCACL;wBACI,UAAU,IAAI,KAAK,CACf,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EACtD,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EACxD,KAAK,EAAE,KAAK,CACf,GAAG,CAAC,CAAC;IACT,iBAAA;IACJ,aAAA;IACI,iBAAA,IAAI,KAAK,CAAC,IAAI,KAAKA,iBAAS,CAAC,KAAK,IAAI,KAAK,GAAG,YAAY,IAAI,iBAAiB,EACpF;IACI,gBAAA,IAAI,SAAS,EACb;wBACI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBACrB,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACxB,iBAAA;IAED,qBAAA;wBACI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBACrB,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACxB,iBAAA;oBACD,UAAU,IAAI,CAAC,CAAC;IACnB,aAAA;gBACD,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;gBACrE,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;gBACrE,UAAU,IAAI,CAAC,CAAC;IACnB,SAAA;IACJ,KAAA;QAED,EAAE,GAAG,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9B,IAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEpC,EAAE,GAAG,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9B,IAAA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAEpC,IAAA,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACnB,IAAA,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;IAEhB,IAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;QACpD,KAAK,IAAI,IAAI,CAAC;QACd,KAAK,IAAI,IAAI,CAAC;QACd,KAAK,IAAI,KAAK,CAAC;QACf,KAAK,IAAI,KAAK,CAAC;IAEf,IAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;IACnE,IAAA,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;QAEnE,IAAI,CAAC,WAAW,EAChB;IACI,QAAA,IAAI,KAAK,CAAC,GAAG,KAAKC,gBAAQ,CAAC,KAAK,EAChC;IACI,YAAA,UAAU,IAAI,KAAK,CACf,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EAChD,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,EAC1B,KAAK,EACL,KAAK,CACR,GAAG,CAAC,CAAC;IACT,SAAA;IACI,aAAA,IAAI,KAAK,CAAC,GAAG,KAAKA,gBAAQ,CAAC,MAAM,EACtC;IACI,YAAA,UAAU,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACtF,SAAA;IACJ,KAAA;IAED,IAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;QACzC,IAAM,IAAI,GAAG,eAAe,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;;IAG/D,IAAA,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,GAAG,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,EAC7D;YACI,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;YACpB,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YAExB,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACxB,QAAA,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAE9B,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACxB,QAAA,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;IAG9B,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,EAC3E;gBACI,SAAS;IACZ,SAAA;IAED,QAAA,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACjC,KAAA;IACL,CAAC;IAED;;;;;;;;IAQG;IACH,SAAS,eAAe,CAAC,YAA0B,EAAE,gBAAkC,EAAA;QAEnF,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,IAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAgB,CAAC;QAC5C,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;IACnD,IAAA,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAKI,WAAM,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC;IAEpE,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;cAAE,OAAO,EAAA;IAEhC,IAAA,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACtC,IAAA,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;IACzC,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAEjC,IAAA,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,YAAY,GAAG,UAAU,CAAC;IAE9B,IAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAC3B;YACI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;IAE7C,QAAA,YAAY,EAAE,CAAC;IAClB,KAAA;IAED,IAAA,IAAI,WAAW,EACf;IACI,QAAA,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAC1C,KAAA;IACL,CAAC;IAED;;;;;;;;IAQG;IACa,SAAA,SAAS,CAAC,YAA0B,EAAE,gBAAkC,EAAA;IAEpF,IAAA,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,EACjC;IACI,QAAA,eAAe,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;IACnD,KAAA;IAED,SAAA;IACI,QAAA,kBAAkB,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;IACtD,KAAA;IACL;;ICjmBA;;;IAGG;IACH,IAAA,QAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,QAAA,GAAA;SA6GC;IA3GG;;;;;;;;;;;;IAYG;IACI,IAAA,QAAA,CAAA,OAAO,GAAd,UAAe,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,MAAc,EAAE,MAAqB,EAAA;YAEhG,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAExC,QAAA,IAAM,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;IACtB,QAAA,IAAM,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;IACtB,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAE3C,QAAA,IAAI,EAAE,GAAG,MAAM,IAAI,MAAM,KAAK,CAAC,EAC/B;gBACI,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EACxE;IACI,gBAAA,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,aAAA;IAED,YAAA,OAAO,IAAI,CAAC;IACf,SAAA;IAED,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IACjC,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IACjC,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IACjC,QAAA,IAAM,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IACvC,QAAA,IAAM,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IACvC,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACxB,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACxB,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IACjC,QAAA,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YACjC,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YAC1B,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YAC1B,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YAC1B,IAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IAC1B,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAChD,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAE9C,OAAO;IACH,YAAA,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACb,YAAA,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACb,YAAA,MAAM,EAAA,MAAA;IACN,YAAA,UAAU,EAAA,UAAA;IACV,YAAA,QAAQ,EAAA,QAAA;gBACR,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;aACrC,CAAC;SACL,CAAA;;IAGD;;;;;;;;;;;;;;;IAeG;IACI,IAAA,QAAA,CAAA,GAAG,GAAV,UAAW,OAAe,EAAE,OAAe,EAAE,EAAU,EAAE,EAAU,EAAE,MAAc,EAC/E,UAAkB,EAAE,QAAgB,EAAE,cAAuB,EAAE,MAAqB,EAAA;IAEpF,QAAA,IAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,CAAC;IACpC,QAAA,IAAM,CAAC,GAAG,eAAe,CAAC,cAAc,CACpC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGE,SAAI,CAAC,GAAG,EAAE,CACzC,CAAC;YAEF,IAAM,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAChC,QAAA,IAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;YACzB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,QAAA,IAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;YACvB,IAAM,SAAS,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,QAAQ,CAAC;YAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,QAAQ,EAAE,EAAE,CAAC,EAClC;gBACI,IAAM,IAAI,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;IACjC,YAAA,IAAM,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;gBACvD,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC1B,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAE3B,MAAM,CAAC,IAAI,CACP,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,EAAE,EAC7C,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,EAAE,CACjD,CAAC;IACL,SAAA;SACJ,CAAA;QAEL,OAAC,QAAA,CAAA;IAAD,CAAC,EAAA,CAAA;;IC5HD;;;IAGG;IACH,IAAA,WAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,WAAA,GAAA;SAiHC;IA/GG;;;;;;;;;;;;;;IAcG;IACI,IAAA,WAAA,CAAA,WAAW,GAAlB,UACI,KAAa,EAAE,KAAa,EAC5B,GAAW,EAAE,GAAW,EACxB,IAAY,EAAE,IAAY,EAC1B,GAAW,EAAE,GAAW,EAAA;YAExB,IAAM,CAAC,GAAG,EAAE,CAAC;YACb,IAAI,MAAM,GAAG,GAAG,CAAC;YACjB,IAAI,CAAC,GAAG,GAAG,CAAC;YACZ,IAAI,EAAE,GAAG,GAAG,CAAC;YACb,IAAI,EAAE,GAAG,GAAG,CAAC;YACb,IAAI,EAAE,GAAG,GAAG,CAAC;YACb,IAAI,GAAG,GAAG,GAAG,CAAC;YACd,IAAI,GAAG,GAAG,GAAG,CAAC;YACd,IAAI,CAAC,GAAG,GAAG,CAAC;YACZ,IAAI,CAAC,GAAG,GAAG,CAAC;YACZ,IAAI,EAAE,GAAG,GAAG,CAAC;YACb,IAAI,EAAE,GAAG,GAAG,CAAC;YACb,IAAI,KAAK,GAAG,KAAK,CAAC;YAClB,IAAI,KAAK,GAAG,KAAK,CAAC;YAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAC3B;IACI,YAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACV,YAAA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACX,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACZ,YAAA,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;IACf,YAAA,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACd,YAAA,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;IAEf,YAAA,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC;IAChF,YAAA,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC;IAC9E,YAAA,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;IACf,YAAA,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;gBACf,KAAK,GAAG,CAAC,CAAC;gBACV,KAAK,GAAG,CAAC,CAAC;IAEV,YAAA,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9C,SAAA;IAED,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;IAED;;;;;;;;;;;;IAYG;IACI,IAAA,WAAA,CAAA,OAAO,GAAd,UACI,GAAW,EAAE,GAAW,EACxB,IAAY,EAAE,IAAY,EAC1B,GAAW,EAAE,GAAW,EACxB,MAAqB,EAAA;YAErB,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAExC,QAAA,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;YAEnB,IAAM,CAAC,GAAG,eAAe,CAAC,cAAc,CACpC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CACxE,CAAC;YAEF,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,EAAE,GAAG,CAAC,CAAC;IAEX,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAE1B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAClC;IACI,YAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEV,YAAA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACb,YAAA,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACd,YAAA,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;IAEf,YAAA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACX,YAAA,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAEZ,YAAA,MAAM,CAAC,IAAI,CACP,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,EACvE,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAC1E,CAAC;IACL,SAAA;SACJ,CAAA;QACL,OAAC,WAAA,CAAA;IAAD,CAAC,EAAA,CAAA;;ICrHD;;;IAGG;IACH,IAAA,cAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,cAAA,GAAA;SA6EC;IA3EG;;;;;;;;;;;;IAYG;IACI,IAAA,cAAA,CAAA,WAAW,GAAlB,UACI,KAAa,EAAE,KAAa,EAC5B,GAAW,EAAE,GAAW,EACxB,GAAW,EAAE,GAAW,EAAA;YAExB,IAAM,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;YACrC,IAAM,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACrC,QAAA,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;IACvC,QAAA,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;IACvC,QAAA,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACxC,QAAA,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACxC,QAAA,IAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAEhC,QAAA,IAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACrC,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,QAAA,IAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;YACzB,IAAM,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9B,QAAA,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAElB,QAAA,OAAO,CACH,CAAC,GAAG,GAAG,CAAC;mBACD,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IACnB,eACE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACvB,kBAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAC/C,KACJ,GAAG,GAAG,GAAG,CAAC,CAAC;SACnB,CAAA;IAED;;;;;;;;;IASG;QACI,cAAO,CAAA,OAAA,GAAd,UAAe,GAAW,EAAE,GAAW,EAAE,GAAW,EAAE,GAAW,EAAE,MAAqB,EAAA;YAEpF,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAExC,IAAM,CAAC,GAAG,eAAe,CAAC,cAAc,CACpC,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAC/D,CAAC;YAEF,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,EAAE,GAAG,CAAC,CAAC;YAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAC3B;IACI,YAAA,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEhB,YAAA,EAAE,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;IACjC,YAAA,EAAE,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;gBAEjC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EACnD,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACpD,SAAA;SACJ,CAAA;QACL,OAAC,cAAA,CAAA;IAAD,CAAC,EAAA,CAAA;;IChFD;;;IAGG;IACH,IAAA,SAAA,kBAAA,YAAA;IAQI,IAAA,SAAA,SAAA,GAAA;YAEI,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;IAED;;;;;IAKG;IACI,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,UAAa,KAA4B,EAAE,UAAkB,EAAE,WAAmB,EAAA;YAE9E,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;IACxB,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;SAClC,CAAA;IAED;;;;IAIG;IACI,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAV,UAAW,QAAgB,EAAE,SAAiB,EAAA;YAE1C,IAAI,CAAC,UAAU,GAAG,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;YAC/C,IAAI,CAAC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;SACrC,CAAA;IAEM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IACd,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACf,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACrB,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;SACvB,CAAA;QACL,OAAC,SAAA,CAAA;IAAD,CAAC,EAAA,CAAA;;ICrDD;;;;IAIG;;IA0BH;;;;IAIG;IACI,IAAM,aAAa,IAAA,EAAA,GAAA,EAAA;IACtB,IAAA,EAAA,CAACF,WAAM,CAAC,IAAI,CAAA,GAAG,SAAS;IACxB,IAAA,EAAA,CAACA,WAAM,CAAC,IAAI,CAAA,GAAG,WAAW;IAC1B,IAAA,EAAA,CAACA,WAAM,CAAC,IAAI,CAAA,GAAG,WAAW;IAC1B,IAAA,EAAA,CAACA,WAAM,CAAC,IAAI,CAAA,GAAG,cAAc;IAC7B,IAAA,EAAA,CAACA,WAAM,CAAC,IAAI,CAAA,GAAG,qBAAqB;WACvC,CAAC;IAEF;;;;IAIG;IACI,IAAM,UAAU,GAAqB,EAAE,CAAC;IAE/C;;;;IAIG;IACI,IAAM,cAAc,GAAyB,EAAE;;ICnDtD;;;IAGG;AACH,QAAA,YAAA,kBAAA,YAAA;IA2BI;;;;;IAKG;IACH,IAAA,SAAA,YAAA,CAAY,KAAa,EAAE,SAA2B,EAAE,SAA2B,EAAE,MAAqB,EAAA;IAA/E,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;IAAE,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;;YAZ1G,IAAM,CAAA,MAAA,GAAa,EAAE,CAAC;;YAItB,IAAK,CAAA,KAAA,GAAwB,EAAE,CAAC;IAU5B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;SAC1B;IAED;;;IAGG;IACI,IAAA,YAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,OAAO,IAAI,YAAY,CACnB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,CACd,CAAC;SACL,CAAA;;IAGM,IAAA,YAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;IAEI,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IACtB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB,CAAA;QACL,OAAC,YAAA,CAAA;IAAD,CAAC,EAAA;;ICzCD,IAAM,QAAQ,GAAG,IAAIC,UAAK,EAAE,CAAC;IAE7B;;;;;;;IAOG;AACH,QAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;QAAsC,SAAa,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;;IA2E/C,IAAA,SAAA,gBAAA,GAAA;IAAA,QAAA,IAAA,KAAA,GAEI,iBAAO,IACV,IAAA,CAAA;;YApEM,KAAa,CAAA,aAAA,GAAG,IAAI,CAAC;;YAGrB,KAAa,CAAA,aAAA,GAAG,CAAC,CAAC;YAEzB,KAAU,CAAA,UAAA,GAAiB,IAAI,CAAC;YAChC,KAAa,CAAA,aAAA,GAA8B,IAAI,CAAC;YAChD,KAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;YAGlB,KAAM,CAAA,MAAA,GAAa,EAAE,CAAC;;YAGtB,KAAM,CAAA,MAAA,GAAa,EAAE,CAAC;;YAGtB,KAAG,CAAA,GAAA,GAAa,EAAE,CAAC;;YAGnB,KAAO,CAAA,OAAA,GAAa,EAAE,CAAC;;YAGvB,KAAU,CAAA,UAAA,GAAa,EAAE,CAAC;IAE1B;;;IAGG;YACH,KAAY,CAAA,YAAA,GAAwB,EAAE,CAAC;IAEvC;;;IAGG;YACH,KAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;;YAGrC,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC,CAAC;IAEhB;;;;IAIG;YACH,KAAO,CAAA,OAAA,GAAqB,EAAE,CAAC;;YAGrB,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;;YAGV,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC,CAAC;;YAGhB,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC;;YAGf,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC;;IAGf,QAAA,KAAA,CAAA,OAAO,GAAW,IAAIE,cAAM,EAAE,CAAC;;YAG/B,KAAW,CAAA,WAAA,GAAG,CAAC,CAAC,CAAC;;SAM1B;IAMD,IAAA,MAAA,CAAA,cAAA,CAAW,gBAAM,CAAA,SAAA,EAAA,QAAA,EAAA;IAJjB;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,IAAI,CAAC,aAAa,EAAE,CAAC;IAErB,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,KAAK,EACnC;IACI,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;oBAC9B,IAAI,CAAC,eAAe,EAAE,CAAC;IAC1B,aAAA;gBAED,OAAO,IAAI,CAAC,OAAO,CAAC;aACvB;;;IAAA,KAAA,CAAA,CAAA;;IAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,UAAU,GAApB,YAAA;IAEI,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,UAAU,EAAE,CAAC;IAClB,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IAEpB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACvB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACvB,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,QAAA,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAE3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAC9C;gBACI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,SAAA;IAED,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAE1B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAC5C;gBACI,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAElC,SAAS,CAAC,KAAK,EAAE,CAAC;IAClB,YAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9B,SAAA;IAED,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;SAC3B,CAAA;IAED;;;IAGG;IACI,IAAA,gBAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAChC;gBACI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,EAAE,CAAC;IAClB,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IAChC,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;IAOG;QACI,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UACI,KAAa,EACb,SAA2B,EAC3B,SAA2B,EAC3B,MAAqB,EAAA;IAFrB,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;IAC3B,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA2B,GAAA,IAAA,CAAA,EAAA;IAC3B,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;IAErB,QAAA,IAAM,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAEnE,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;IAEb,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;IAKG;IACI,IAAA,gBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,KAAa,EAAE,MAAqB,EAAA;IAArB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;IAEhD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAC7B;IACI,YAAA,OAAO,IAAI,CAAC;IACf,SAAA;IAED,QAAA,IAAM,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAEzD,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAElE,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;IAErC,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3B,IAAI,CAAC,KAAK,EAAE,CAAC;IAEb,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;;IAGM,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;YAEI,MAAM,CAAA,SAAA,CAAA,OAAO,WAAE,CAAC;;IAGhB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EACjD;gBACI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAClC,SAAA;IAED,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IAChB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;IAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IACzB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB,CAAA;IAED;;;;IAIG;QACI,gBAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,KAAiB,EAAA;IAElC,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;IAEvC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAC5C;IACI,YAAA,IAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAE7B,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAC3B;oBACI,SAAS;IACZ,aAAA;;gBAGD,IAAI,IAAI,CAAC,KAAK,EACd;oBACI,IAAI,IAAI,CAAC,MAAM,EACf;wBACI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC7C,iBAAA;IAED,qBAAA;IACI,oBAAA,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5B,iBAAA;IAED,gBAAA,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAC/C;wBACI,IAAI,OAAO,GAAG,KAAK,CAAC;wBAEpB,IAAI,IAAI,CAAC,KAAK,EACd;IACI,wBAAA,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAC,EAAE,EAC1C;gCACI,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAC,CAAC,CAAC;IAE3B,4BAAA,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAC/C;oCACI,OAAO,GAAG,IAAI,CAAC;oCACf,MAAM;IACT,6BAAA;IACJ,yBAAA;IACJ,qBAAA;wBAED,IAAI,CAAC,OAAO,EACZ;IACI,wBAAA,OAAO,IAAI,CAAC;IACf,qBAAA;IACJ,iBAAA;IACJ,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAED;;;IAGG;IACH,IAAA,gBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;IAEI,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAC7B;IACI,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBAEtB,OAAO;IACV,SAAA;IAED,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAC5B;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;IAE7B,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IACrB,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;YAEvC,IAAI,SAAS,GAAc,IAAI,CAAC;YAEhC,IAAI,YAAY,GAAG,IAAI,CAAC;IAExB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAC3B;IACI,YAAA,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAClD,YAAA,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;IAClC,SAAA;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAC1D;gBACI,IAAI,CAAC,UAAU,EAAE,CAAC;IAElB,YAAA,IAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IACjC,YAAA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;gBACjC,IAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;IAGzC,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAEpB,IAAI,IAAI,CAAC,MAAM,EACf;oBACI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAClD,aAAA;IAED,YAAA,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,EAC1C;IACI,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,aAAA;gBAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC1B;IACI,gBAAA,IAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC;oBAEhD,IAAI,CAAC,KAAK,CAAC,OAAO;0BAAE,SAAS,EAAA;IAE7B,gBAAA,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;IAC9C,gBAAA,IAAM,OAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;oBAClC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAE3C,gBAAA,WAAW,CAAC,QAAQ,GAAGC,oBAAU,CAAC,MAAM,CAAC;oBAEzC,IAAI,CAAC,KAAK,CAAC,EACX;IACI,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC1B,iBAAA;IAED,qBAAA;IACI,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC1B,iBAAA;IAED,gBAAA,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC;oBAEpD,IAAI,IAAI,KAAK,CAAC;0BAAE,SAAS,EAAA;;oBAEzB,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC,EAC1D;IACI,oBAAA,SAAS,CAAC,GAAG,CAAC,OAAK,EAAE,WAAW,CAAC,CAAC;wBAClC,SAAS,GAAG,IAAI,CAAC;IACpB,iBAAA;;oBAED,IAAI,CAAC,SAAS,EACd;wBACI,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,IAAI,SAAS,EAAE,CAAC;wBAChD,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,OAAK,EAAE,WAAW,CAAC,CAAC;IAC3C,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC7B,YAAY,GAAG,KAAK,CAAC;IACxB,iBAAA;oBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACjF,aAAA;IACJ,SAAA;IAED,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAClC,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAEtC,QAAA,IAAI,SAAS,EACb;IACI,YAAA,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAChC,SAAA;IAED,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAC7B;;;IAGI,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBAEtB,OAAO;IACV,SAAA;IAED,QAAA,IAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;;IAG/B,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM;mBACpE,MAAM,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,CAAC,CAAC,EAC5D;gBACI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxC,SAAA;IAED,aAAA;gBACI,IAAI,CAAC,aAAa,GAAG,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/F,SAAA;;IAGD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAEpC,IAAI,IAAI,CAAC,SAAS,EAClB;gBACI,IAAI,CAAC,WAAW,EAAE,CAAC;IACtB,SAAA;IAED,aAAA;gBACI,IAAI,CAAC,cAAc,EAAE,CAAC;IACzB,SAAA;SACJ,CAAA;IAED;;;;IAIG;IACO,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,UAAyB,MAA6B,EAAE,MAA6B,EAAA;IAEjF,QAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EACtB;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;YAED,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,MAAM,CAAC,OAAO,CAAC,WAAW,EAC7D;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAC/D;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;YAED,IAAI,CAAC,CAAE,MAAoB,CAAC,MAAM,KAAK,CAAC,CAAE,MAAoB,CAAC,MAAM,EACrE;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;;IAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;IAEI,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAC/D;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACxD;gBACI,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAClC,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;IAC5B,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;gBAE5B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK;IAAE,gBAAA,EAAA,OAAO,KAAK,CAAC,EAAA;gBAC1D,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK;IAAE,gBAAA,EAAA,OAAO,KAAK,CAAC,EAAA;IAC7D,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;;IAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,WAAW,GAArB,YAAA;YAEI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE7C,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAE7B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC9C;IACI,YAAA,IAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAEzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EACnC;IACI,gBAAA,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;IAE9B,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;IAC7E,aAAA;IACJ,SAAA;SACJ,CAAA;IAED;;;IAGG;IACO,IAAA,gBAAA,CAAA,SAAA,CAAA,WAAW,GAArB,YAAA;;YAGI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,EACnC;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;IAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAE7B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EACvC;gBACI,IAAK,OAAO,CAAC,CAAC,CAAC,CAAC,KAAmB,CAAC,MAAM,EAC1C;IACI,gBAAA,OAAO,KAAK,CAAC;IAChB,aAAA;IACJ,SAAA;IAED,QAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,gBAAgB,CAAC,cAAc,GAAG,CAAC,EAAE;SACrE,CAAA;;IAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;IAEI,QAAA,IAAI,IAAI,GAAG,EAAEC,gBAAW,CAAC,YAAY,CAAC;IAEtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAC9C;gBACI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,SAAA;IAED,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAE1B,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IAEnC,QAAA,IAAI,YAAY,GAAmB,cAAc,CAAC,GAAG,EAAE,CAAC;YAExD,IAAI,CAAC,YAAY,EACjB;IACI,YAAA,YAAY,GAAG,IAAIC,kBAAa,EAAE,CAAC;IACnC,YAAA,YAAY,CAAC,QAAQ,GAAG,IAAIC,sBAAiB,EAAE,CAAC;IACnD,SAAA;IACD,QAAA,YAAY,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;IAChC,QAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;IACvB,QAAA,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;IACtB,QAAA,YAAY,CAAC,IAAI,GAAGC,oBAAU,CAAC,SAAS,CAAC;YAEzC,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,cAAc,GAAG,IAAI,CAAC;YAC1B,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,QAAA,IAAI,QAAQ,GAAGA,oBAAU,CAAC,SAAS,CAAC;YAEpC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;IAGlC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAC5C;gBACI,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;gBAG7B,IAAM,YAAY,GAAG,CAAC,CAAC;;IAGvB,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAkB,CAAC;IAEtC,YAAA,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;IAE9C,YAAA,IAAI,MAAM,KAAK,CAAC,CAAC,KAAK,CAAC,MAAM,EAC7B;IACI,gBAAA,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;IACxB,gBAAA,QAAQ,GAAG,MAAM,GAAGA,oBAAU,CAAC,KAAK,GAAGA,oBAAU,CAAC,SAAS,CAAC;;oBAG5D,cAAc,GAAG,IAAI,CAAC;oBACtB,YAAY,GAAG,YAAY,CAAC;IAC5B,gBAAA,IAAI,EAAE,CAAC;IACV,aAAA;gBAED,IAAI,cAAc,KAAK,WAAW,EAClC;oBACI,cAAc,GAAG,WAAW,CAAC;IAE7B,gBAAA,IAAI,WAAW,CAAC,aAAa,KAAK,IAAI,EACtC;wBACI,IAAI,YAAY,KAAK,YAAY,EACjC;IACI,wBAAA,IAAI,EAAE,CAAC;4BAEP,YAAY,GAAG,CAAC,CAAC;IAEjB,wBAAA,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,EACzB;IACI,4BAAA,YAAY,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC;gCACpC,IAAI,CAAC,YAAY,EACjB;IACI,gCAAA,YAAY,GAAG,IAAIF,kBAAa,EAAE,CAAC;IACnC,gCAAA,YAAY,CAAC,QAAQ,GAAG,IAAIC,sBAAiB,EAAE,CAAC;IACnD,6BAAA;IACD,4BAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACrC,yBAAA;IAED,wBAAA,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;IAC3B,wBAAA,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;IACtB,wBAAA,YAAY,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;IAChC,wBAAA,YAAY,CAAC,IAAI,GAAG,QAAQ,CAAC;IAChC,qBAAA;;;IAID,oBAAA,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC;IAExB,oBAAA,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC;IACjC,oBAAA,WAAW,CAAC,cAAc,GAAG,YAAY,CAAC;IAC1C,oBAAA,WAAW,CAAC,QAAQ,GAAGH,oBAAU,CAAC,MAAM,CAAC;IAEzC,oBAAA,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,CAAC;IAC5E,oBAAA,YAAY,EAAE,CAAC;IAClB,iBAAA;IACJ,aAAA;IAED,YAAA,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;IAC/B,YAAA,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;IAEnB,YAAA,SAAS,GAAG,WAAW,CAAC,cAAc,CAAC;gBAEvC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACpF,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAChF,SAAA;IAED,QAAAC,gBAAW,CAAC,YAAY,GAAG,IAAI,CAAC;;;YAIhC,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB,CAAA;;IAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;IAEI,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IACrB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;;IAGnC,QAAA,IAAM,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,QAAA,IAAM,GAAG,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;IACvC,QAAA,IAAM,GAAG,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;YAEtC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EACzC;gBACI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACxB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE9B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACtB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE5B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAErB,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAC5B,SAAA;IAED,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAChD,CAAA;IAED;;;IAGG;QACO,gBAAW,CAAA,SAAA,CAAA,WAAA,GAArB,UAAsB,IAAkB,EAAA;IAEpC,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EACrB;IACI,YAAA,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,SAAA;IAED,aAAA;gBACI,IAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEzC,YAAA,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACnC,SAAA;SACJ,CAAA;IAED;;;IAGG;QACO,gBAAW,CAAA,SAAA,CAAA,WAAA,GAArB,UAAsB,IAAkB,EAAA;IAEpC,QAAA,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAEtB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAC1C;gBACI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAClC,SAAA;SACJ,CAAA;IAED;;;IAGG;QACO,gBAAY,CAAA,SAAA,CAAA,YAAA,GAAtB,UAAuB,KAA0B,EAAA;IAE7C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;IACI,YAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEzC,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAEpB,IAAI,IAAI,CAAC,MAAM,EACf;oBACI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAClD,aAAA;IACJ,SAAA;SACJ,CAAA;;IAGS,IAAA,gBAAA,CAAA,SAAA,CAAA,eAAe,GAAzB,YAAA;IAEI,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAE5B,MAAM,CAAC,KAAK,EAAE,CAAC;IACf,QAAA,MAAM,CAAC,aAAa,CAAE,IAAI,CAAC,MAAc,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SACtD,CAAA;IAED;;;;IAIG;IACO,IAAA,gBAAA,CAAA,SAAA,CAAA,eAAe,GAAzB,UAA0B,MAAqB,EAAE,MAAc,EAAA;IAE3D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAC1C;gBACI,IAAM,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;IAC1B,YAAA,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE9B,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;IAC9D,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;IACrE,SAAA;SACJ,CAAA;IAED;;;;;;;IAOG;QACO,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAnB,UACI,MAAqB,EACrB,KAAa,EACb,KAAa,EACb,IAAY,EACZ,MAAU,EAAA;IAAV,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;;YAGV,IAAM,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;YAEtE,IAAM,IAAI,GAAII,qBAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAE1C,QAAA,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;YAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAC7B;IACI,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAC7B,SAAA;SACJ,CAAA;IAED;;;;;;IAMG;QACO,gBAAa,CAAA,SAAA,CAAA,aAAA,GAAvB,UACI,UAAyB,EACzB,EAAU,EACV,IAAY,EACZ,MAAU,EAAA;IAAV,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;IAEV,QAAA,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;YAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAC7B;IACI,YAAA,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IAC/B,SAAA;SACJ,CAAA;IAED;;;;;;;;IAQG;IACO,IAAA,gBAAA,CAAA,SAAA,CAAA,MAAM,GAAhB,UACI,KAAoB,EACpB,GAAkB,EAClB,OAAgB,EAChB,KAAa,EACb,IAAY,EACZ,MAAqB,EAAA;IAArB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAqB,GAAA,IAAA,CAAA,EAAA;YAErB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,QAAA,IAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;IAC5B,QAAA,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAE5B,OAAO,KAAK,GAAG,IAAI,EACnB;IACI,YAAA,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;IACnC,YAAA,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAEzC,YAAA,IAAI,MAAM,EACV;oBACI,IAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;oBAEvD,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;oBAChD,CAAC,GAAG,EAAE,CAAC;IACV,aAAA;IAED,YAAA,KAAK,EAAE,CAAC;IAER,YAAA,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAC/C,SAAA;IAED,QAAA,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;IAExC,QAAA,IAAI,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK;IAC5B,eAAA,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,EACxC;gBACI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;IAChD,SAAA;SACJ,CAAA;IAED;;;;;;;IAOG;QACO,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAnB,UAAoB,GAAkB,EAAE,OAAgB,EAAE,KAAa,EAAE,IAAY,EAAA;IAEjF,QAAA,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YACxC,IAAM,GAAG,GAAG,IAAI,CAAC;YACjB,IAAM,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;IAClC,QAAA,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;YAC/C,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;YACjD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;YACpC,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;IACrC,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;IACxC,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IAE5C,QAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAC1C;IACI,YAAA,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAChD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACvD,SAAA;YACD,OAAO,IAAI,IAAI,CAAC;YAChB,OAAO,IAAI,IAAI,CAAC;IAChB,QAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EACtC;IACI,YAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC;IACrC,YAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC;IAChD,SAAA;SACJ,CAAA;IAh3BD;;;;IAIG;QACW,gBAAc,CAAA,cAAA,GAAG,GAAG,CAAC;QA42BvC,OAAC,gBAAA,CAAA;KAAA,CAn3BqCC,kBAAa,CAm3BlD;;IC55BD;;;IAGG;AACH,QAAA,SAAA,kBAAA,UAAA,MAAA,EAAA;QAA+B,SAAS,CAAA,SAAA,EAAA,MAAA,CAAA,CAAA;IAAxC,IAAA,SAAA,SAAA,GAAA;YAAA,IA4DC,KAAA,GAAA,MAAA,KAAA,IAAA,IAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,IAAA,CAAA;;YAzDU,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;;YAGV,KAAS,CAAA,SAAA,GAAG,GAAG,CAAC;;YAGhB,KAAM,CAAA,MAAA,GAAG,KAAK,CAAC;IAEtB;;;;IAIG;IACI,QAAA,KAAA,CAAA,GAAG,GAAGd,gBAAQ,CAAC,IAAI,CAAC;IAE3B;;;;IAIG;IACI,QAAA,KAAA,CAAA,IAAI,GAAGD,iBAAS,CAAC,KAAK,CAAC;;YAGvB,KAAU,CAAA,UAAA,GAAG,EAAE,CAAC;;SAkC1B;;IA/BU,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,IAAM,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;IAE5B,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC3B,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IACzB,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC3B,QAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,QAAA,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IAC/B,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IACzB,QAAA,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IACnB,QAAA,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACrB,QAAA,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IAEjC,QAAA,OAAO,GAAG,CAAC;SACd,CAAA;;IAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;YAEI,MAAM,CAAA,SAAA,CAAA,KAAK,WAAE,CAAC;;IAGd,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IAEjB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;IACrB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACf,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACvB,CAAA;QACL,OAAC,SAAA,CAAA;IAAD,CA5DA,CAA+B,SAAS,CA4DvC;;ICTD,IAAM,IAAI,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;IAEjC;IACA,IAAM,eAAe,GAA4B,EAAE,CAAC;IAIpD;;;;;;;;;;;;;;;;;IAiBG;AACH,QAAA,QAAA,kBAAA,UAAA,MAAA,EAAA;QAA8B,SAAS,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;IAyEnC;;IAEG;IACH,IAAA,SAAA,QAAA,CAAY,QAAiC,EAAA;IAAjC,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAiC,GAAA,IAAA,CAAA,EAAA;IAA7C,QAAA,IAAA,KAAA,GAEI,iBAAO,IAsBV,IAAA,CAAA;IAtFD;;;IAGG;YACI,KAAM,CAAA,MAAA,GAAW,IAAI,CAAC;;YAGtB,KAAU,CAAA,UAAA,GAAG,OAAO,CAAC;IAE5B;;;IAGG;YACI,KAAW,CAAA,WAAA,GAAY,IAAI,CAAC;;YAGzB,KAAO,CAAA,OAAA,GAAiC,EAAE,CAAC;;YAG3C,KAAS,CAAA,SAAA,GAAG,CAAC,CAAC,CAAC;;YAGf,KAAU,CAAA,UAAA,GAAG,CAAC,CAAC,CAAC;;YAGhB,KAAU,CAAA,UAAA,GAAiB,IAAI,CAAC;;IAGhC,QAAA,KAAA,CAAA,UAAU,GAAc,IAAI,SAAS,EAAE,CAAC;;IAGxC,QAAA,KAAA,CAAA,UAAU,GAAc,IAAI,SAAS,EAAE,CAAC;;YAGxC,KAAO,CAAA,OAAA,GAAW,IAAI,CAAC;;YAGvB,KAAS,CAAA,SAAA,GAAG,KAAK,CAAC;IAI5B;;;IAGG;IACK,QAAA,KAAA,CAAA,KAAK,GAAUgB,UAAK,CAAC,KAAK,EAAE,CAAC;YAqBjC,KAAI,CAAC,SAAS,GAAG,QAAQ,IAAI,IAAI,gBAAgB,EAAE,CAAC;IACpD,QAAA,KAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAE1B;;;;;;;;;;IAUG;IAEH,QAAA,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;;IAGvB,QAAA,KAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;IACrB,QAAA,KAAI,CAAC,SAAS,GAAGC,qBAAW,CAAC,MAAM,CAAC;;SACvC;IAhCD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IANnB;;;;;IAKG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;;;IAAA,KAAA,CAAA,CAAA;IA+BD;;;;IAIG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;YAEI,IAAI,CAAC,UAAU,EAAE,CAAC;IAElB,QAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACvC,CAAA;IAUD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAS,CAAA,SAAA,EAAA,WAAA,EAAA;IAKpB,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;aAC/B;IAhBD;;;;;;;IAOG;IACH,QAAA,GAAA,EAAA,UAAqB,KAAkB,EAAA;IAEnC,YAAA,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;aAChC;;;IAAA,KAAA,CAAA,CAAA;IAYD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAI,CAAA,SAAA,EAAA,MAAA,EAAA;IALf;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,KAAK,CAAC;aACrB;IAED,QAAA,GAAA,EAAA,UAAgB,KAAa,EAAA;IAEzB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACtB;;;IALA,KAAA,CAAA,CAAA;IAWD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAI,CAAA,SAAA,EAAA,MAAA,EAAA;IAJf;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,UAAU,CAAC;aAC1B;;;IAAA,KAAA,CAAA,CAAA;IAMD,IAAA,MAAA,CAAA,cAAA,CAAW,QAAI,CAAA,SAAA,EAAA,MAAA,EAAA;IAJf;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,UAAU,CAAC;aAC1B;;;IAAA,KAAA,CAAA,CAAA;QAgCM,QAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,OAA0C,EACvD,KAAW,EAAE,KAAS,EAAE,SAAe,EAAE,MAAc,EAAA;IAD1C,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA0C,GAAA,IAAA,CAAA,EAAA;IACvD,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAW,GAAA,GAAA,CAAA,EAAA;IAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAe,GAAA,GAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAc,GAAA,KAAA,CAAA,EAAA;;IAGvD,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAC/B;IACI,YAAA,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAA,KAAA,EAAE,KAAK,EAAA,KAAA,EAAE,SAAS,EAAA,SAAA,EAAE,MAAM,EAAA,MAAA,EAAuB,CAAC;IACtF,SAAA;IAED,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SACzC,CAAA;IAED;;;;;;;;;;;;;;;;IAgBG;QACI,QAAgB,CAAA,SAAA,CAAA,gBAAA,GAAvB,UAAwB,OAA2B,EAAA;;IAG/C,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IACpB,YAAA,KAAK,EAAE,CAAC;gBACR,OAAO,EAAEf,YAAO,CAAC,KAAK;IACtB,YAAA,KAAK,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,QAAQ,GAAG,GAAG;IACpD,YAAA,KAAK,EAAE,CAAC;IACR,YAAA,MAAM,EAAE,IAAI;IACZ,YAAA,SAAS,EAAE,GAAG;IACd,YAAA,MAAM,EAAE,KAAK;gBACb,GAAG,EAAED,gBAAQ,CAAC,IAAI;gBAClB,IAAI,EAAED,iBAAS,CAAC,KAAK;IACrB,YAAA,UAAU,EAAE,EAAE;aACjB,EAAE,OAAO,CAAC,CAAC;YAEZ,IAAI,IAAI,CAAC,WAAW,EACpB;gBACI,IAAI,CAAC,SAAS,EAAE,CAAC;IACpB,SAAA;IAED,QAAA,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;YAEvD,IAAI,CAAC,OAAO,EACZ;IACI,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC3B,SAAA;IAED,aAAA;gBACI,IAAI,OAAO,CAAC,MAAM,EAClB;oBACI,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACxC,gBAAA,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IAC3B,aAAA;IAED,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,EAAA,OAAA,EAAE,EAAE,OAAO,CAAC,CAAC;IACxD,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;IAGG;IACO,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAnB,YAAA;YAEI,IAAI,IAAI,CAAC,WAAW,EACpB;IACI,YAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvC,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;gBAE3C,IAAI,GAAG,GAAG,CAAC,EACX;IACI,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACjC,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAIkB,YAAO,EAAE,CAAC;IACjC,gBAAA,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,KAAK,CAAC;oBACrC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAClE,aAAA;IACJ,SAAA;IAED,aAAA;IACI,YAAA,IAAI,CAAC,WAAW,GAAG,IAAIA,YAAO,EAAE,CAAC;IACjC,YAAA,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,KAAK,CAAC;IACxC,SAAA;SACJ,CAAA;IAED;;;IAGG;IACH,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;YAEI,IAAI,IAAI,CAAC,WAAW,EACpB;gBACI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EACtC;IACI,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACjC,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC3B,aAAA;IAED,iBAAA;oBACI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACtC,aAAA;IACJ,SAAA;SACJ,CAAA;IAED;;;;;IAKG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,MAAM,GAAb,UAAc,CAAS,EAAE,CAAS,EAAA;YAE9B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAE/B,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;IAMG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,MAAM,GAAb,UAAc,CAAS,EAAE,CAAS,EAAA;IAE9B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EACrB;IACI,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,SAAA;;IAGD,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACvC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAExC,QAAA,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAC9B;IACI,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;IACO,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAApB,UAAqB,CAAK,EAAE,CAAK,EAAA;IAAZ,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAK,GAAA,CAAA,CAAA,EAAA;YAE7B,IAAI,IAAI,CAAC,WAAW,EACpB;gBACI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxC;oBACI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,aAAA;IACJ,SAAA;IAED,aAAA;IACI,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,SAAA;SACJ,CAAA;IAED;;;;;;;;IAQG;QACI,QAAgB,CAAA,SAAA,CAAA,gBAAA,GAAvB,UAAwB,GAAW,EAAE,GAAW,EAAE,GAAW,EAAE,GAAW,EAAA;YAEtE,IAAI,CAAC,UAAU,EAAE,CAAC;IAElB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAEvC,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EACvB;IACI,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,SAAA;IAED,QAAA,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;IAEnD,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;;;IASG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,aAAa,GAApB,UAAqB,GAAW,EAAE,GAAW,EAAE,IAAY,EAAE,IAAY,EAAE,GAAW,EAAE,GAAW,EAAA;YAE/F,IAAI,CAAC,UAAU,EAAE,CAAC;YAElB,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAE7E,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;;;;IAUG;QACI,QAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,MAAc,EAAA;IAEvE,QAAA,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAExB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAEvC,QAAA,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAEhE,QAAA,IAAI,MAAM,EACV;IACY,YAAA,IAAA,EAAE,GAAsD,MAAM,CAAA,EAA5D,EAAE,EAAE,GAAkD,MAAM,CAAxD,EAAA,EAAE,QAAM,GAA0C,MAAM,CAAA,MAAhD,EAAE,UAAU,GAA8B,MAAM,CAApC,UAAA,EAAE,QAAQ,GAAoB,MAAM,CAAA,QAA1B,EAAE,aAAa,GAAK,MAAM,cAAX,CAAY;IAEvE,YAAA,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,QAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;IACjE,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;;;;;;IAYG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,GAAG,GAAV,UAAW,EAAU,EAAE,EAAU,EAAE,MAAc,EAAE,UAAkB,EAAE,QAAgB,EAAE,aAAqB,EAAA;IAArB,QAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAAqB,GAAA,KAAA,CAAA,EAAA;YAE1G,IAAI,UAAU,KAAK,QAAQ,EAC3B;IACI,YAAA,OAAO,IAAI,CAAC;IACf,SAAA;IAED,QAAA,IAAI,CAAC,aAAa,IAAI,QAAQ,IAAI,UAAU,EAC5C;gBACI,QAAQ,IAAIX,SAAI,CAAC;IACpB,SAAA;IACI,aAAA,IAAI,aAAa,IAAI,UAAU,IAAI,QAAQ,EAChD;gBACI,UAAU,IAAIA,SAAI,CAAC;IACtB,SAAA;IAED,QAAA,IAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,CAAC;YAEpC,IAAI,KAAK,KAAK,CAAC,EACf;IACI,YAAA,OAAO,IAAI,CAAC;IACf,SAAA;IAED,QAAA,IAAM,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;IACpD,QAAA,IAAM,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;IACpD,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;;IAGzC,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;IAE/D,QAAA,IAAI,MAAM,EACV;;;IAII,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IAC3D,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IAE3D,YAAA,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,EAC9B;;;IAGC,aAAA;IAED,iBAAA;IACI,gBAAA,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC/B,aAAA;IACJ,SAAA;IAED,aAAA;IACI,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC5B,YAAA,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACpC,SAAA;YAED,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;IAE1F,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;IAMG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UAAiB,KAAS,EAAE,KAAS,EAAA;IAApB,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;IAAE,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAS,GAAA,CAAA,CAAA,EAAA;IAEjC,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,OAAO,EAAEL,YAAO,CAAC,KAAK,EAAE,KAAK,EAAA,KAAA,EAAE,KAAK,EAAA,KAAA,EAAE,CAAC,CAAC;SAC1E,CAAA;IAED;;;;;;;;IAQG;QACH,QAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,OAA2B,EAAA;;IAGxC,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBACpB,OAAO,EAAEA,YAAO,CAAC,KAAK;IACtB,YAAA,KAAK,EAAE,QAAQ;IACf,YAAA,KAAK,EAAE,CAAC;IACR,YAAA,MAAM,EAAE,IAAI;aACf,EAAE,OAAO,CAAsB,CAAC;YAEjC,IAAI,IAAI,CAAC,WAAW,EACpB;gBACI,IAAI,CAAC,SAAS,EAAE,CAAC;IACpB,SAAA;IAED,QAAA,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;YAElC,IAAI,CAAC,OAAO,EACZ;IACI,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC3B,SAAA;IAED,aAAA;gBACI,IAAI,OAAO,CAAC,MAAM,EAClB;oBACI,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACxC,gBAAA,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IAC3B,aAAA;IAED,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,EAAA,OAAA,EAAE,EAAE,OAAO,CAAC,CAAC;IACxD,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;IAGG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;YAEI,IAAI,CAAC,UAAU,EAAE,CAAC;IAElB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAExB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;IAOG;QACI,QAAQ,CAAA,SAAA,CAAA,QAAA,GAAf,UAAgB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAA;IAE/D,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAIiB,cAAS,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;SAC7D,CAAA;IAED;;;;;;;;IAQG;QACI,QAAe,CAAA,SAAA,CAAA,eAAA,GAAtB,UAAuB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,MAAc,EAAA;IAEtF,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAIC,qBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;SAC5E,CAAA;IAED;;;;;;IAMG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,UAAkB,CAAS,EAAE,CAAS,EAAE,MAAc,EAAA;IAElD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAIC,WAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;SACnD,CAAA;IAED;;;;;;;IAOG;QACI,QAAW,CAAA,SAAA,CAAA,WAAA,GAAlB,UAAmB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAA;IAElE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAIC,YAAO,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;SAC3D,CAAA;IAKD;;;;IAIG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,WAAW,GAAlB,YAAA;;AAAA;YAAmB,IAAc,IAAA,GAAA,EAAA,CAAA;iBAAd,IAAc,EAAA,GAAA,CAAA,EAAd,EAAc,GAAA,SAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAA;gBAAd,IAAc,CAAA,EAAA,CAAA,GAAAnB,WAAA,CAAA,EAAA,CAAA,CAAA;;IAE7B,QAAA,IAAI,MAAyC,CAAC;IAC9C,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAY,CAAC;;YAGhC,IAAI,IAAI,CAAC,MAAM,EACf;IACI,YAAA,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IAC/B,YAAA,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IACxB,SAAA;iBAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAC1B;IACI,YAAA,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,SAAA;IAED,aAAA;gBACI,MAAM,GAAG,IAAI,CAAC;IACjB,SAAA;IAED,QAAA,IAAM,KAAK,GAAG,IAAIe,YAAO,CAAC,MAAM,CAAC,CAAC;IAElC,QAAA,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;IAEhC,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAEtB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACI,QAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,KAAa,EAAA;IAE1B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EACnB;gBACI,IAAI,CAAC,SAAS,CAAC,SAAS,CACpB,KAAK,EACL,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EACvB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EACvB,IAAI,CAAC,OAAO,CACf,CAAC;IACL,SAAA;IAED,aAAA;gBACI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAChD,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;IAGG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;IAEI,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACvB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IACxB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAExB,IAAI,CAAC,SAAS,EAAE,CAAC;IACjB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACvB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAExB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,YAAA;IAEI,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IAEzC,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC;mBACjB,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAKb,WAAM,CAAC,IAAI;IAClC,eAAA,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;IACf,eAAA,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM;IACrB,eAAA,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAClE,CAAA;IAED;;;IAGG;QACO,QAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,QAAkB,EAAA;YAEhC,IAAI,CAAC,UAAU,EAAE,CAAC;IAElB,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;;;YAIhC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAEzB,IAAI,QAAQ,CAAC,SAAS,EACtB;IACI,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,UAAU,EAC3C;oBACI,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC3B,aAAA;IAED,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACjC,SAAA;IAED,aAAA;;IAEI,YAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IAEvB,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChC,SAAA;SACJ,CAAA;;IAGS,IAAA,QAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;IAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IAChC,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IACjC,QAAA,IAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;IAEpC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IACpB,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IACvB,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;IACtC,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC;YAE1B,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAC5B;gBACI,IAAM,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC/B,YAAA,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC7B,IAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtD,EAAE,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,EACtB,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAEvB,IAAM,GAAG,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EACnD,EAAE,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,EACtB,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAEvB,IAAM,OAAO,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EACzD,EAAE,CAAC,KAAK,GAAG,CAAC,EACZ,EAAE,CAAC,IAAI,CAAC,CAAC;IAEb,YAAA,IAAM,KAAK,GAAG;IACV,gBAAA,UAAU,EAAA,UAAA;IACV,gBAAA,SAAS,EAAA,SAAA;IACT,gBAAA,OAAO,EAAA,OAAA;IACP,gBAAA,GAAG,EAAA,GAAA;IACH,gBAAA,SAAS,EAAEkB,aAAO,CAAC,KAAK,CAAkB;IAC1C,gBAAA,QAAQ,EAAE,KAAK;IACf,gBAAA,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO;IAC1B,gBAAA,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK;IACrB,gBAAA,UAAU,EAAE,CAAC;iBAAE,CAAC;IAEpB,YAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAC3B,SAAA;SACJ,CAAA;IAED;;;IAGG;QACO,QAAc,CAAA,SAAA,CAAA,cAAA,GAAxB,UAAyB,QAAkB,EAAA;IAEvC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EACxB;gBACI,OAAO;IACV,SAAA;IAED,QAAA,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YAEpE,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,EAAE,CAAC;IAEtB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACnD;gBACI,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE9B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;IAEjD,YAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnD,SAAA;SACJ,CAAA;IAED;;;IAGG;QACO,QAAa,CAAA,SAAA,CAAA,aAAA,GAAvB,UAAwB,QAAkB,EAAA;YAEtC,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAEnD,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IAChC,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACvB,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IACjC,QAAA,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;;YAGrC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;;YAG3D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC;YAC9D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC;IAC7D,QAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC;IACtD,QAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;;;;;IAO9B,QAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;;YAGzC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;IAG/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAChD;IACI,YAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,SAAA;SACJ,CAAA;IAED;;;;IAIG;IACO,IAAA,QAAA,CAAA,SAAA,CAAA,qBAAqB,GAA/B,UAAgC,QAAkB,EAAE,QAAuB,EAAA;IAE/D,QAAA,IAAA,QAAQ,GAAwB,QAAQ,SAAhC,EAAE,IAAI,GAAkB,QAAQ,CAAA,IAA1B,EAAE,IAAI,GAAY,QAAQ,CAApB,IAAA,EAAE,KAAK,GAAK,QAAQ,MAAb,CAAc;IACjD,QAAA,IAAM,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAAC;YAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAC1C;IACI,YAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClD,SAAA;YAED,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC7C,CAAA;IAED;;;IAGG;QACO,QAAoB,CAAA,SAAA,CAAA,oBAAA,GAA9B,UAA+B,QAAkB,EAAA;IAE7C,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAEzB,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YAEnC,IAAI,CAAC,MAAM,EACX;;;;IAII,YAAA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAChC;oBACY,IAAA,YAAY,GAAK,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,YAAjC,CAAkC;IACtD,gBAAA,IAAM,YAAY,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC;oBAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EACrC;IACI,oBAAA,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvB,iBAAA;IAED,gBAAA,IAAM,QAAQ,GAAG;IACb,oBAAA,IAAI,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;wBACpC,iBAAiB,EAAE,IAAIC,WAAM,EAAE;IAC/B,oBAAA,OAAO,EAAEC,iBAAY,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE,IAAI,CAAC;qBAChE,CAAC;IAEF,gBAAA,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;oBAE7D,eAAe,CAAC,UAAU,CAAC,GAAG,IAAIC,WAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/D,aAAA;IAED,YAAA,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;IACxC,SAAA;IAED,QAAA,OAAO,MAAM,CAAC;SACjB,CAAA;;IAGS,IAAA,QAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;YAEI,IAAI,CAAC,UAAU,EAAE,CAAC;IAElB,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;;IAGhC,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,EACjC;gBACI,OAAO;IACV,SAAA;IAEK,QAAA,IAAA,KAA6B,QAAQ,CAAC,MAAM,EAA1C,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,IAAI,UAAoB,CAAC;IAEnD,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SACjE,CAAA;IAED;;;;IAIG;QACI,QAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,KAAiB,EAAA;YAElC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;YAE9D,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SAC7D,CAAA;;IAGS,IAAA,QAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;IAEI,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI,EAChC;IACI,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;gBAE3B,IAAM,OAAO,GAAGH,aAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAEzC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAC5C;oBACI,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE9B,gBAAA,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IAElC,gBAAA,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;IAC5C,gBAAA,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;IAC5C,gBAAA,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;;IAG5C,gBAAA,IAAM,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,gBAAA,KAAK,CAAC,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE;2BAClB,KAAK,GAAG,MAAM,CAAC;2BACf,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;IACpC,aAAA;IACJ,SAAA;SACJ,CAAA;;IAGS,IAAA,QAAA,CAAA,SAAA,CAAA,iBAAiB,GAA3B,YAAA;IAEI,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IAErC,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAC9B;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAEzB,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;IACzC,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACjB,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YAEjB,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IACnC,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YAEnC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EACvC;IACI,YAAA,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClB,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEtB,YAAA,UAAU,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IAC7C,YAAA,UAAU,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IAChD,SAAA;SACJ,CAAA;IAED;;;IAGG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,YAAA;IAEI,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IAErC,QAAA,IAAI,WAAW,EACf;;IAEI,YAAA,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;;;gBAG/B,IAAI,CAAC,UAAU,EAAE,CAAC;IACrB,SAAA;IAED,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;IAIG;QACI,QAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,MAAc,EAAA;IAE3B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAEtB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;IAOG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,YAAA;YAEI,IAAI,CAAC,UAAU,EAAE,CAAC;IAClB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAEtB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;IAGG;IACI,IAAA,QAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;YAEI,IAAI,CAAC,UAAU,EAAE,CAAC;IAClB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAEvB,QAAA,OAAO,IAAI,CAAC;SACf,CAAA;IAED;;;;;;;;;;IAUG;QACI,QAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;IAE9C,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,CAAC,EACjC;IACI,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC5B,SAAA;IAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IAEpB,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;SAC1B,CAAA;IArmCD;;;IAGG;QACW,QAAuB,CAAA,uBAAA,GAAG,KAAK,CAAC;IAE9C;;;IAGG;IACI,IAAA,QAAA,CAAA,WAAW,GAAG,IAAIjB,UAAK,EAAE,CAAC;QA4lCrC,OAAC,QAAA,CAAA;KAAA,CAxmC6BqB,iBAAS,CAwmCtC;;AClqCM,QAAM,aAAa,GAAG;IACzB,IAAA,SAAS,EAAE,SAA+B;IAC1C,IAAA,WAAW,EAAE,WAAiC;IAC9C,IAAA,cAAc,EAAE,cAAoC;IACpD,IAAA,qBAAqB,EAAE,qBAA2C;IAClE,IAAA,SAAS,EAAA,SAAA;IACT,IAAA,QAAQ,EAAA,QAAA;IACR,IAAA,WAAW,EAAA,WAAA;IACX,IAAA,cAAc,EAAA,cAAA;IACd,IAAA,SAAS,EAAA,SAAA;IACT,IAAA,aAAa,EAAE,aAAmD;IAClE,IAAA,UAAU,EAAE,UAA8B;IAC1C,IAAA,cAAc,EAAE,cAAsC;;;;;;;;;;;;;;;;;;;;;;"}