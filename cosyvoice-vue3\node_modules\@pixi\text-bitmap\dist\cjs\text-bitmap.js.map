{"version": 3, "file": "text-bitmap.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/BitmapFontData.ts", "../../src/formats/TextFormat.ts", "../../src/formats/XMLFormat.ts", "../../src/formats/XMLStringFormat.ts", "../../src/formats/index.ts", "../../src/utils/generateFillStyle.ts", "../../src/utils/drawGlyph.ts", "../../src/utils/splitTextToCharacters.ts", "../../src/utils/resolveCharacters.ts", "../../src/utils/extractCharCode.ts", "../../src/BitmapFont.ts", "../../src/BitmapText.ts", "../../src/BitmapFontLoader.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/* eslint-disable max-len */\n\n/**\n * Normalized parsed data from .fnt files.\n * @memberof PIXI\n */\nexport class BitmapFontData\n{\n    /** @readonly */\n    public info: IBitmapFontDataInfo[];\n\n    /** @readonly */\n    public common: IBitmapFontDataCommon[];\n\n    /** @readonly */\n    public page: IBitmapFontDataPage[];\n\n    /** @readonly */\n    public char: IBitmapFontDataChar[];\n\n    /** @readonly */\n    public kerning: IBitmapFontDataKerning[];\n\n    /** @readonly */\n    public distanceField: IBitmapFontDataDistanceField[];\n\n    constructor()\n    {\n        this.info = [];\n        this.common = [];\n        this.page = [];\n        this.char = [];\n        this.kerning = [];\n        this.distanceField = [];\n    }\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataInfo\n{\n    /** Font face */\n    face: string;\n\n    /** Font size */\n    size: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataCommon\n{\n    /** Line height, in pixels. */\n    lineHeight: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataPage\n{\n    /** Unique id for bitmap texture */\n    id: number;\n\n    /** File name */\n    file: string;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataChar\n{\n    /** Unique id of character */\n    id: number;\n\n    /** {@link PIXI.IBitmapFontDataPage} id */\n    page: number;\n\n    /** x-position of character in page. */\n    x: number;\n\n    /** y-position of character in page. */\n    y: number;\n\n    /** Width of character in page. */\n    width: number;\n\n    /** Height of character in page. */\n    height: number;\n\n    /** x-offset to apply when rendering character */\n    xoffset: number;\n\n    /** y-offset to apply when rendering character. */\n    yoffset: number;\n\n    /** Advancement to apply to next character. */\n    xadvance: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataKerning\n{\n    /** First character of pair */\n    first: number;\n\n    /** Second character of pair */\n    second: number;\n\n    /** x-offset to apply between first & second characters when they are next to each other. */\n    amount: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataDistanceField\n{\n    /** Type of distance field */\n    fieldType: string;\n\n    /** Range of distance */\n    distanceRange: number;\n}\n", "import { BitmapFontData } from '../BitmapFontData';\n\n/**\n * Internal data format used to convert to BitmapFontData.\n * @private\n */\nexport interface IBitmapFontRawData\n{\n    info: {\n        face: string;\n        size: string;\n    }[];\n    common: { lineHeight: string }[];\n    page: {\n        id: string;\n        file: string;\n    }[];\n    chars: {\n        count: number;\n    }[];\n    char: {\n        id: string;\n        page: string;\n        x: string;\n        y: string;\n        width: string;\n        height: string;\n        xoffset: string;\n        yoffset: string;\n        xadvance: string;\n    }[];\n    kernings?: {\n        count: number;\n    }[];\n    kerning?: {\n        first: string;\n        second: string;\n        amount: string;\n    }[];\n    distanceField?: {\n        fieldType: string;\n        distanceRange: string;\n    }[]\n}\n\n/**\n * BitmapFont format that's Text-based.\n * @private\n */\nexport class TextFormat\n{\n    /**\n     * Check if resource refers to txt font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: unknown): boolean\n    {\n        return typeof data === 'string' && data.indexOf('info face=') === 0;\n    }\n\n    /**\n     * Convert text font data to a javascript object.\n     * @param txt - Raw string data to be converted\n     * @returns - Parsed font data\n     */\n    static parse(txt: string): BitmapFontData\n    {\n        // Retrieve data item\n        const items = txt.match(/^[a-z]+\\s+.+$/gm);\n        const rawData: IBitmapFontRawData = {\n            info: [],\n            common: [],\n            page: [],\n            char: [],\n            chars: [],\n            kerning: [],\n            kernings: [],\n            distanceField: [],\n        };\n\n        for (const i in items)\n        {\n            // Extract item name\n            const name = items[i].match(/^[a-z]+/gm)[0] as keyof BitmapFontData;\n\n            // Extract item attribute list as string ex.: \"width=10\"\n            const attributeList = items[i].match(/[a-zA-Z]+=([^\\s\"']+|\"([^\"]*)\")/gm);\n\n            // Convert attribute list into an object\n            const itemData: any = {};\n\n            for (const i in attributeList)\n            {\n                // Split key-value pairs\n                const split = attributeList[i].split('=');\n                const key = split[0];\n\n                // Remove eventual quotes from value\n                const strValue = split[1].replace(/\"/gm, '');\n\n                // Try to convert value into float\n                const floatValue = parseFloat(strValue);\n\n                // Use string value case float value is NaN\n                const value = isNaN(floatValue) ? strValue : floatValue;\n\n                itemData[key] = value;\n            }\n\n            // Push current item to the resulting data\n            rawData[name].push(itemData);\n        }\n\n        const font = new BitmapFontData();\n\n        rawData.info.forEach((info) => font.info.push({\n            face: info.face,\n            size: parseInt(info.size, 10),\n        }));\n\n        rawData.common.forEach((common) => font.common.push({\n            lineHeight: parseInt(common.lineHeight, 10),\n        }));\n\n        rawData.page.forEach((page) => font.page.push({\n            id: parseInt(page.id, 10),\n            file: page.file,\n        }));\n\n        rawData.char.forEach((char) => font.char.push({\n            id: parseInt(char.id, 10),\n            page: parseInt(char.page, 10),\n            x: parseInt(char.x, 10),\n            y: parseInt(char.y, 10),\n            width: parseInt(char.width, 10),\n            height: parseInt(char.height, 10),\n            xoffset: parseInt(char.xoffset, 10),\n            yoffset: parseInt(char.yoffset, 10),\n            xadvance: parseInt(char.xadvance, 10),\n        }));\n\n        rawData.kerning.forEach((kerning) => font.kerning.push({\n            first: parseInt(kerning.first, 10),\n            second: parseInt(kerning.second, 10),\n            amount: parseInt(kerning.amount, 10),\n        }));\n\n        rawData.distanceField.forEach((df) => font.distanceField.push({\n            distanceRange: parseInt(df.distanceRange, 10),\n            fieldType: df.fieldType,\n        }));\n\n        return font;\n    }\n}\n", "import { BitmapFontData } from '../BitmapFontData';\n\n/**\n * BitmapFont format that's XML-based.\n * @private\n */\nexport class XMLFormat\n{\n    /**\n     * Check if resource refers to xml font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: unknown): boolean\n    {\n        return data instanceof XMLDocument\n            && data.getElementsByTagName('page').length\n            && data.getElementsByTagName('info')[0].getAttribute('face') !== null;\n    }\n\n    /**\n     * Convert the XML into BitmapFontData that we can use.\n     * @param xml\n     * @returns - Data to use for BitmapFont\n     */\n    static parse(xml: XMLDocument): BitmapFontData\n    {\n        const data = new BitmapFontData();\n        const info = xml.getElementsByTagName('info');\n        const common = xml.getElementsByTagName('common');\n        const page = xml.getElementsByTagName('page');\n        const char = xml.getElementsByTagName('char');\n        const kerning = xml.getElementsByTagName('kerning');\n        const distanceField = xml.getElementsByTagName('distanceField');\n\n        for (let i = 0; i < info.length; i++)\n        {\n            data.info.push({\n                face: info[i].getAttribute('face'),\n                size: parseInt(info[i].getAttribute('size'), 10),\n            });\n        }\n\n        for (let i = 0; i < common.length; i++)\n        {\n            data.common.push({\n                lineHeight: parseInt(common[i].getAttribute('lineHeight'), 10),\n            });\n        }\n\n        for (let i = 0; i < page.length; i++)\n        {\n            data.page.push({\n                id: parseInt(page[i].getAttribute('id'), 10) || 0,\n                file: page[i].getAttribute('file'),\n            });\n        }\n\n        for (let i = 0; i < char.length; i++)\n        {\n            const letter = char[i];\n\n            data.char.push({\n                id: parseInt(letter.getAttribute('id'), 10),\n                page: parseInt(letter.getAttribute('page'), 10) || 0,\n                x: parseInt(letter.getAttribute('x'), 10),\n                y: parseInt(letter.getAttribute('y'), 10),\n                width: parseInt(letter.getAttribute('width'), 10),\n                height: parseInt(letter.getAttribute('height'), 10),\n                xoffset: parseInt(letter.getAttribute('xoffset'), 10),\n                yoffset: parseInt(letter.getAttribute('yoffset'), 10),\n                xadvance: parseInt(letter.getAttribute('xadvance'), 10),\n            });\n        }\n\n        for (let i = 0; i < kerning.length; i++)\n        {\n            data.kerning.push({\n                first: parseInt(kerning[i].getAttribute('first'), 10),\n                second: parseInt(kerning[i].getAttribute('second'), 10),\n                amount: parseInt(kerning[i].getAttribute('amount'), 10),\n            });\n        }\n\n        for (let i = 0; i < distanceField.length; i++)\n        {\n            data.distanceField.push({\n                fieldType: distanceField[i].getAttribute('fieldType'),\n                distanceRange: parseInt(distanceField[i].getAttribute('distanceRange'), 10),\n            });\n        }\n\n        return data;\n    }\n}\n", "import type { BitmapFontData } from '../BitmapFontData';\nimport { XMLFormat } from './XMLFormat';\n\n/**\n * BitmapFont format that's XML-based.\n * @private\n */\nexport class XMLStringFormat\n{\n    /**\n     * Check if resource refers to text xml font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: unknown): boolean\n    {\n        if (typeof data === 'string' && data.indexOf('<font>') > -1)\n        {\n            const xml = new globalThis.DOMParser().parseFromString(data, 'text/xml');\n\n            return XMLFormat.test(xml);\n        }\n\n        return false;\n    }\n\n    /**\n     * Convert the text XML into BitmapFontData that we can use.\n     * @param xmlTxt\n     * @returns - Data to use for BitmapFont\n     */\n    static parse(xmlTxt: string): BitmapFontData\n    {\n        const xml = new globalThis.DOMParser().parseFromString(xmlTxt, 'text/xml');\n\n        return XMLFormat.parse(xml);\n    }\n}\n", "import { TextFormat } from './TextFormat';\nimport { XMLFormat } from './XMLFormat';\nimport { XMLStringFormat } from './XMLStringFormat';\n\n// Registered formats, maybe make this extensible in the future?\nconst formats = [\n    TextFormat,\n    XMLFormat,\n    XMLStringFormat,\n] as const;\n\n/**\n * Auto-detect BitmapFont parsing format based on data.\n * @private\n * @param {any} data - Data to detect format\n * @returns {any} Format or null\n */\nexport function autoDetectFormat(data: unknown): typeof formats[number] | null\n{\n    for (let i = 0; i < formats.length; i++)\n    {\n        if (formats[i].test(data))\n        {\n            return formats[i];\n        }\n    }\n\n    return null;\n}\n\nexport type { IBitmapFontRawData } from './TextFormat';\nexport { TextFormat, XMLFormat, XMLStringFormat };\n", "import type { TextStyle, TextMetrics } from '@pixi/text';\nimport { TEXT_GRADIENT } from '@pixi/text';\n\n// TODO: Prevent code duplication b/w generateFillStyle & Text#generateFillStyle\n\n/**\n * Generates the fill style. Can automatically generate a gradient based on the fill style being an array\n * @private\n * @param canvas\n * @param context\n * @param {object} style - The style.\n * @param resolution\n * @param {string[]} lines - The lines of text.\n * @param metrics\n * @returns {string|number|CanvasGradient} The fill style\n */\nexport function generateFillStyle(\n    canvas: HTMLCanvasElement,\n    context: CanvasRenderingContext2D,\n    style: TextStyle,\n    resolution: number,\n    lines: string[],\n    metrics: TextMetrics\n): string | CanvasGradient | CanvasPattern\n{\n    // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n    //       the setter converts to string. See this thread for more details:\n    //       https://github.com/microsoft/TypeScript/issues/2521\n    const fillStyle: string | string[] | CanvasGradient | CanvasPattern = style.fill as any;\n\n    if (!Array.isArray(fillStyle))\n    {\n        return fillStyle;\n    }\n    else if (fillStyle.length === 1)\n    {\n        return fillStyle[0];\n    }\n\n    // the gradient will be evenly spaced out according to how large the array is.\n    // ['#FF0000', '#00FF00', '#0000FF'] would created stops at 0.25, 0.5 and 0.75\n    let gradient: string[] | CanvasGradient;\n\n    // a dropshadow will enlarge the canvas and result in the gradient being\n    // generated with the incorrect dimensions\n    const dropShadowCorrection = (style.dropShadow) ? style.dropShadowDistance : 0;\n\n    // should also take padding into account, padding can offset the gradient\n    const padding = style.padding || 0;\n\n    const width = (canvas.width / resolution) - dropShadowCorrection - (padding * 2);\n    const height = (canvas.height / resolution) - dropShadowCorrection - (padding * 2);\n\n    // make a copy of the style settings, so we can manipulate them later\n    const fill = fillStyle.slice();\n    const fillGradientStops = style.fillGradientStops.slice();\n\n    // wanting to evenly distribute the fills. So an array of 4 colours should give fills of 0.25, 0.5 and 0.75\n    if (!fillGradientStops.length)\n    {\n        const lengthPlus1 = fill.length + 1;\n\n        for (let i = 1; i < lengthPlus1; ++i)\n        {\n            fillGradientStops.push(i / lengthPlus1);\n        }\n    }\n\n    // stop the bleeding of the last gradient on the line above to the top gradient of the this line\n    // by hard defining the first gradient colour at point 0, and last gradient colour at point 1\n    fill.unshift(fillStyle[0]);\n    fillGradientStops.unshift(0);\n\n    fill.push(fillStyle[fillStyle.length - 1]);\n    fillGradientStops.push(1);\n\n    if (style.fillGradientType === TEXT_GRADIENT.LINEAR_VERTICAL)\n    {\n        // start the gradient at the top center of the canvas, and end at the bottom middle of the canvas\n        gradient = context.createLinearGradient(width / 2, padding, width / 2, height + padding);\n\n        // we need to repeat the gradient so that each individual line of text has the same vertical gradient effect\n        // ['#FF0000', '#00FF00', '#0000FF'] over 2 lines would create stops at 0.125, 0.25, 0.375, 0.625, 0.75, 0.875\n\n        // There's potential for floating point precision issues at the seams between gradient repeats.\n        // The loop below generates the stops in order, so track the last generated one to prevent\n        // floating point precision from making us go the teeniest bit backwards, resulting in\n        // the first and last colors getting swapped.\n        let lastIterationStop = 0;\n\n        // Actual height of the text itself, not counting spacing for lineHeight/leading/dropShadow etc\n        const textHeight = metrics.fontProperties.fontSize + style.strokeThickness;\n\n        // textHeight, but as a 0-1 size in global gradient stop space\n        const gradStopLineHeight = textHeight / height;\n\n        for (let i = 0; i < lines.length; i++)\n        {\n            const thisLineTop = metrics.lineHeight * i;\n\n            for (let j = 0; j < fill.length; j++)\n            {\n                // 0-1 stop point for the current line, multiplied to global space afterwards\n                let lineStop = 0;\n\n                if (typeof fillGradientStops[j] === 'number')\n                {\n                    lineStop = fillGradientStops[j];\n                }\n                else\n                {\n                    lineStop = j / fill.length;\n                }\n\n                const globalStop = (thisLineTop / height) + (lineStop * gradStopLineHeight);\n\n                // Prevent color stop generation going backwards from floating point imprecision\n                let clampedStop = Math.max(lastIterationStop, globalStop);\n\n                clampedStop = Math.min(clampedStop, 1); // Cap at 1 as well for safety's sake to avoid a possible throw.\n                gradient.addColorStop(clampedStop, fill[j]);\n                lastIterationStop = clampedStop;\n            }\n        }\n    }\n    else\n    {\n        // start the gradient at the center left of the canvas, and end at the center right of the canvas\n        gradient = context.createLinearGradient(padding, height / 2, width + padding, height / 2);\n\n        // can just evenly space out the gradients in this case, as multiple lines makes no difference\n        // to an even left to right gradient\n        const totalIterations = fill.length + 1;\n        let currentIteration = 1;\n\n        for (let i = 0; i < fill.length; i++)\n        {\n            let stop: number;\n\n            if (typeof fillGradientStops[i] === 'number')\n            {\n                stop = fillGradientStops[i];\n            }\n            else\n            {\n                stop = currentIteration / totalIterations;\n            }\n            gradient.addColorStop(stop, fill[i]);\n            currentIteration++;\n        }\n    }\n\n    return gradient;\n}\n", "import { generateFillStyle } from './generateFillStyle';\nimport { hex2rgb, string2hex } from '@pixi/utils';\nimport type { TextMetrics, TextStyle } from '@pixi/text';\n\n// TODO: Prevent code duplication b/w drawGlyph & Text#updateText\n\n/**\n * Draws the glyph `metrics.text` on the given canvas.\n *\n * Ignored because not directly exposed.\n * @ignore\n * @param {HTMLCanvasElement} canvas\n * @param {CanvasRenderingContext2D} context\n * @param {TextMetrics} metrics\n * @param {number} x\n * @param {number} y\n * @param {number} resolution\n * @param {TextStyle} style\n */\nexport function drawGlyph(\n    canvas: HTMLCanvasElement,\n    context: CanvasRenderingContext2D,\n    metrics: TextMetrics,\n    x: number,\n    y: number,\n    resolution: number,\n    style: TextStyle\n): void\n{\n    const char = metrics.text;\n    const fontProperties = metrics.fontProperties;\n\n    context.translate(x, y);\n    context.scale(resolution, resolution);\n\n    const tx = style.strokeThickness / 2;\n    const ty = -(style.strokeThickness / 2);\n\n    context.font = style.toFontString();\n    context.lineWidth = style.strokeThickness;\n    context.textBaseline = style.textBaseline;\n    context.lineJoin = style.lineJoin;\n    context.miterLimit = style.miterLimit;\n\n    // set canvas text styles\n    context.fillStyle = generateFillStyle(canvas, context, style, resolution, [char], metrics);\n    context.strokeStyle = style.stroke as string;\n\n    if (style.dropShadow)\n    {\n        const dropShadowColor = style.dropShadowColor;\n        const rgb = hex2rgb(typeof dropShadowColor === 'number' ? dropShadowColor : string2hex(dropShadowColor));\n        const dropShadowBlur = style.dropShadowBlur * resolution;\n        const dropShadowDistance = style.dropShadowDistance * resolution;\n\n        context.shadowColor = `rgba(${rgb[0] * 255},${rgb[1] * 255},${rgb[2] * 255},${style.dropShadowAlpha})`;\n        context.shadowBlur = dropShadowBlur;\n        context.shadowOffsetX = Math.cos(style.dropShadowAngle) * dropShadowDistance;\n        context.shadowOffsetY = Math.sin(style.dropShadowAngle) * dropShadowDistance;\n    }\n    else\n    {\n        context.shadowColor = 'black';\n        context.shadowBlur = 0;\n        context.shadowOffsetX = 0;\n        context.shadowOffsetY = 0;\n    }\n\n    if (style.stroke && style.strokeThickness)\n    {\n        context.strokeText(char, tx, ty + metrics.lineHeight - fontProperties.descent);\n    }\n    if (style.fill)\n    {\n        context.fillText(char, tx, ty + metrics.lineHeight - fontProperties.descent);\n    }\n\n    context.setTransform(1, 0, 0, 1, 0, 0); // defaults needed for older browsers (e.g. Opera 29)\n\n    context.fillStyle = 'rgba(0, 0, 0, 0)';\n}\n", "/**\n * Ponyfill for IE because it doesn't support `Array.from`\n * @param text\n * @private\n */\nexport function splitTextToCharacters(text: string): string[]\n{\n    return Array.from ? Array.from(text) : text.split('');\n}\n", "import { splitTextToCharacters } from './splitTextToCharacters';\n\n/**\n * Processes the passed character set data and returns a flattened array of all the characters.\n *\n * Ignored because not directly exposed.\n * @ignore\n * @param {string | string[] | string[][] } chars\n * @returns {string[]} the flattened array of characters\n */\nexport function resolveCharacters(chars: string | (string | string[])[]): string[]\n{\n    // Split the chars string into individual characters\n    if (typeof chars === 'string')\n    {\n        chars = [chars];\n    }\n\n    // Handle an array of characters+ranges\n    const result: string[] = [];\n\n    for (let i = 0, j = chars.length; i < j; i++)\n    {\n        const item = chars[i];\n\n        // Handle range delimited by start/end chars\n        if (Array.isArray(item))\n        {\n            if (item.length !== 2)\n            {\n                throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${item.length}.`);\n            }\n\n            const startCode = item[0].charCodeAt(0);\n            const endCode = item[1].charCodeAt(0);\n\n            if (endCode < startCode)\n            {\n                throw new Error('[BitmapFont]: Invalid character range.');\n            }\n\n            for (let i = startCode, j = endCode; i <= j; i++)\n            {\n                result.push(String.fromCharCode(i));\n            }\n        }\n        // Handle a character set string\n        else\n        {\n            result.push(...splitTextToCharacters(item));\n        }\n    }\n\n    if (result.length === 0)\n    {\n        throw new Error('[BitmapFont]: Empty set when resolving characters.');\n    }\n\n    return result;\n}\n", "/**\n * Ponyfill for IE because it doesn't support `codePointAt`\n * @param str\n * @private\n */\nexport function extractCharCode(str: string): number\n{\n    return str.codePointAt ? str.codePointAt(0) : str.charCodeAt(0);\n}\n", "import { getResolutionOfUrl } from '@pixi/utils';\nimport { Rectangle } from '@pixi/math';\nimport { Texture, BaseTexture } from '@pixi/core';\nimport { TextStyle, TextMetrics } from '@pixi/text';\nimport { autoDetectFormat } from './formats';\nimport { BitmapFontData } from './BitmapFontData';\nimport { resolveCharacters, drawGlyph, extractCharCode } from './utils';\n\nimport type { Dict } from '@pixi/utils';\nimport type { ITextStyle } from '@pixi/text';\nimport { ALPHA_MODES, MIPMAP_MODES } from '@pixi/constants';\nimport { settings } from '@pixi/settings';\n\nexport interface IBitmapFontCharacter\n{\n    xOffset: number;\n    yOffset: number;\n    xAdvance: number;\n    texture: Texture;\n    page: number;\n    kerning: Dict<number>;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontOptions\n{\n    /**\n     * The character set to generate.\n     * @default PIXI.BitmapFont.ALPHANUMERIC\n     */\n    chars?: string | (string | string[])[];\n\n    /**\n     * The resolution for rendering.\n     * @default 1\n     */\n    resolution?: number;\n\n    /**\n     * The padding between glyphs in the atlas.\n     * @default 4\n     */\n    padding?: number;\n\n    /**\n     * The width of the texture atlas.\n     * @default 512\n     */\n    textureWidth?: number;\n\n    /**\n     * The height of the texture atlas.\n     * @default 512\n     */\n    textureHeight?: number;\n\n    /**\n     * Skip generation of kerning information for the BitmapFont.\n     * If true, this could potentially increase the performance, but may impact the rendered text appearance.\n     * @default false\n     */\n    skipKerning?: boolean;\n}\n\n/**\n * BitmapFont represents a typeface available for use with the BitmapText class. Use the `install`\n * method for adding a font to be used.\n * @memberof PIXI\n */\nexport class BitmapFont\n{\n    /**\n     * This character set includes all the letters in the alphabet (both lower- and upper- case).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from(\"ExampleFont\", style, { chars: BitmapFont.ALPHA })\n     */\n    public static readonly ALPHA = [['a', 'z'], ['A', 'Z'], ' '];\n\n    /**\n     * This character set includes all decimal digits (from 0 to 9).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from(\"ExampleFont\", style, { chars: BitmapFont.NUMERIC })\n     */\n    public static readonly NUMERIC = [['0', '9']];\n\n    /**\n     * This character set is the union of `BitmapFont.ALPHA` and `BitmapFont.NUMERIC`.\n     * @type {string[][]}\n     */\n    public static readonly ALPHANUMERIC = [['a', 'z'], ['A', 'Z'], ['0', '9'], ' '];\n\n    /**\n     * This character set consists of all the ASCII table.\n     * @member {string[][]}\n     * @see http://www.asciitable.com/\n     */\n    public static readonly ASCII = [[' ', '~']];\n\n    /**\n     * Collection of default options when using `BitmapFont.from`.\n     * @property {number} [resolution=1] -\n     * @property {number} [textureWidth=512] -\n     * @property {number} [textureHeight=512] -\n     * @property {number} [padding=4] -\n     * @property {string|string[]|string[][]} chars = PIXI.BitmapFont.ALPHANUMERIC\n     */\n    public static readonly defaultOptions: IBitmapFontOptions = {\n        resolution: 1,\n        textureWidth: 512,\n        textureHeight: 512,\n        padding: 4,\n        chars: BitmapFont.ALPHANUMERIC,\n    };\n\n    /** Collection of available/installed fonts. */\n    public static readonly available: Dict<BitmapFont> = {};\n\n    /** The name of the font face. */\n    public readonly font: string;\n\n    /** The size of the font face in pixels. */\n    public readonly size: number;\n\n    /** The line-height of the font face in pixels. */\n    public readonly lineHeight: number;\n\n    /** The map of characters by character code. */\n    public readonly chars: Dict<IBitmapFontCharacter>;\n\n    /** The map of base page textures (i.e., sheets of glyphs). */\n    public readonly pageTextures: Dict<Texture>;\n\n    /** The range of the distance field in pixels. */\n    public readonly distanceFieldRange: number;\n\n    /** The kind of distance field for this font or \"none\". */\n    public readonly distanceFieldType: string;\n\n    private _ownsTextures: boolean;\n\n    /**\n     * @param data\n     * @param textures\n     * @param ownsTextures - Setting to `true` will destroy page textures\n     *        when the font is uninstalled.\n     */\n    constructor(data: BitmapFontData, textures: Texture[] | Dict<Texture>, ownsTextures?: boolean)\n    {\n        const [info] = data.info;\n        const [common] = data.common;\n        const [page] = data.page;\n        const [distanceField] = data.distanceField;\n        const res = getResolutionOfUrl(page.file);\n        const pageTextures: Dict<Texture> = {};\n\n        this._ownsTextures = ownsTextures;\n        this.font = info.face;\n        this.size = info.size;\n        this.lineHeight = common.lineHeight / res;\n        this.chars = {};\n        this.pageTextures = pageTextures;\n\n        // Convert the input Texture, Textures or object\n        // into a page Texture lookup by \"id\"\n        for (let i = 0; i < data.page.length; i++)\n        {\n            const { id, file } = data.page[i];\n\n            pageTextures[id] = textures instanceof Array\n                ? textures[i] : textures[file];\n\n            // only MSDF and SDF fonts need no-premultiplied-alpha\n            if (distanceField?.fieldType && distanceField.fieldType !== 'none')\n            {\n                pageTextures[id].baseTexture.alphaMode = ALPHA_MODES.NO_PREMULTIPLIED_ALPHA;\n                pageTextures[id].baseTexture.mipmap = MIPMAP_MODES.OFF;\n            }\n        }\n\n        // parse letters\n        for (let i = 0; i < data.char.length; i++)\n        {\n            const { id, page } = data.char[i];\n            let { x, y, width, height, xoffset, yoffset, xadvance } = data.char[i];\n\n            x /= res;\n            y /= res;\n            width /= res;\n            height /= res;\n            xoffset /= res;\n            yoffset /= res;\n            xadvance /= res;\n\n            const rect = new Rectangle(\n                x + (pageTextures[page].frame.x / res),\n                y + (pageTextures[page].frame.y / res),\n                width,\n                height\n            );\n\n            this.chars[id] = {\n                xOffset: xoffset,\n                yOffset: yoffset,\n                xAdvance: xadvance,\n                kerning: {},\n                texture: new Texture(\n                    pageTextures[page].baseTexture,\n                    rect\n                ),\n                page,\n            };\n        }\n\n        // parse kernings\n        for (let i = 0; i < data.kerning.length; i++)\n        {\n            let { first, second, amount } = data.kerning[i];\n\n            first /= res;\n            second /= res;\n            amount /= res;\n\n            if (this.chars[second])\n            {\n                this.chars[second].kerning[first] = amount;\n            }\n        }\n\n        // Store distance field information\n        this.distanceFieldRange = distanceField?.distanceRange;\n        this.distanceFieldType = distanceField?.fieldType?.toLowerCase() ?? 'none';\n    }\n\n    /** Remove references to created glyph textures. */\n    public destroy(): void\n    {\n        for (const id in this.chars)\n        {\n            this.chars[id].texture.destroy();\n            this.chars[id].texture = null;\n        }\n\n        for (const id in this.pageTextures)\n        {\n            if (this._ownsTextures)\n            {\n                this.pageTextures[id].destroy(true);\n            }\n\n            this.pageTextures[id] = null;\n        }\n\n        // Set readonly null.\n        (this as any).chars = null;\n        (this as any).pageTextures = null;\n    }\n\n    /**\n     * Register a new bitmap font.\n     * @param data - The\n     *        characters map that could be provided as xml or raw string.\n     * @param textures - List of textures for each page.\n     * @param ownsTextures - Set to `true` to destroy page textures\n     *        when the font is uninstalled. By default fonts created with\n     *        `BitmapFont.from` or from the `BitmapFontLoader` are `true`.\n     * @returns {PIXI.BitmapFont} Result font object with font, size, lineHeight\n     *         and char fields.\n     */\n    public static install(\n        data: string | XMLDocument | BitmapFontData,\n        textures: Texture | Texture[] | Dict<Texture>,\n        ownsTextures?: boolean\n    ): BitmapFont\n    {\n        let fontData;\n\n        if (data instanceof BitmapFontData)\n        {\n            fontData = data;\n        }\n        else\n        {\n            const format = autoDetectFormat(data);\n\n            if (!format)\n            {\n                throw new Error('Unrecognized data format for font.');\n            }\n\n            fontData = format.parse(data as any);\n        }\n\n        // Single texture, convert to list\n        if (textures instanceof Texture)\n        {\n            textures = [textures];\n        }\n\n        const font = new BitmapFont(fontData, textures, ownsTextures);\n\n        BitmapFont.available[font.font] = font;\n\n        return font;\n    }\n\n    /**\n     * Remove bitmap font by name.\n     * @param name - Name of the font to uninstall.\n     */\n    public static uninstall(name: string): void\n    {\n        const font = BitmapFont.available[name];\n\n        if (!font)\n        {\n            throw new Error(`No font found named '${name}'`);\n        }\n\n        font.destroy();\n        delete BitmapFont.available[name];\n    }\n\n    /**\n     * Generates a bitmap-font for the given style and character set. This does not support\n     * kernings yet. With `style` properties, only the following non-layout properties are used:\n     *\n     * - {@link PIXI.TextStyle#dropShadow|dropShadow}\n     * - {@link PIXI.TextStyle#dropShadowDistance|dropShadowDistance}\n     * - {@link PIXI.TextStyle#dropShadowColor|dropShadowColor}\n     * - {@link PIXI.TextStyle#dropShadowBlur|dropShadowBlur}\n     * - {@link PIXI.TextStyle#dropShadowAngle|dropShadowAngle}\n     * - {@link PIXI.TextStyle#fill|fill}\n     * - {@link PIXI.TextStyle#fillGradientStops|fillGradientStops}\n     * - {@link PIXI.TextStyle#fillGradientType|fillGradientType}\n     * - {@link PIXI.TextStyle#fontFamily|fontFamily}\n     * - {@link PIXI.TextStyle#fontSize|fontSize}\n     * - {@link PIXI.TextStyle#fontVariant|fontVariant}\n     * - {@link PIXI.TextStyle#fontWeight|fontWeight}\n     * - {@link PIXI.TextStyle#lineJoin|lineJoin}\n     * - {@link PIXI.TextStyle#miterLimit|miterLimit}\n     * - {@link PIXI.TextStyle#stroke|stroke}\n     * - {@link PIXI.TextStyle#strokeThickness|strokeThickness}\n     * - {@link PIXI.TextStyle#textBaseline|textBaseline}\n     * @param name - The name of the custom font to use with BitmapText.\n     * @param textStyle - Style options to render with BitmapFont.\n     * @param options - Setup options for font or name of the font.\n     * @param {string|string[]|string[][]} [options.chars=PIXI.BitmapFont.ALPHANUMERIC] - characters included\n     *      in the font set. You can also use ranges. For example, `[['a', 'z'], ['A', 'Z'], \"!@#$%^&*()~{}[] \"]`.\n     *      Don't forget to include spaces ' ' in your character set!\n     * @param {number} [options.resolution=1] - Render resolution for glyphs.\n     * @param {number} [options.textureWidth=512] - Optional width of atlas, smaller values to reduce memory.\n     * @param {number} [options.textureHeight=512] - Optional height of atlas, smaller values to reduce memory.\n     * @param {number} [options.padding=4] - Padding between glyphs on texture atlas.\n     * @returns Font generated by style options.\n     * @example\n     * PIXI.BitmapFont.from(\"TitleFont\", {\n     *     fontFamily: \"Arial\",\n     *     fontSize: 12,\n     *     strokeThickness: 2,\n     *     fill: \"purple\"\n     * });\n     *\n     * const title = new PIXI.BitmapText(\"This is the title\", { fontName: \"TitleFont\" });\n     */\n    public static from(name: string, textStyle?: TextStyle | Partial<ITextStyle>, options?: IBitmapFontOptions): BitmapFont\n    {\n        if (!name)\n        {\n            throw new Error('[BitmapFont] Property `name` is required.');\n        }\n\n        const {\n            chars,\n            padding,\n            resolution,\n            textureWidth,\n            textureHeight } = Object.assign(\n            {}, BitmapFont.defaultOptions, options);\n\n        const charsList = resolveCharacters(chars);\n        const style = textStyle instanceof TextStyle ? textStyle : new TextStyle(textStyle);\n        const lineWidth = textureWidth;\n        const fontData = new BitmapFontData();\n\n        fontData.info[0] = {\n            face: style.fontFamily as string,\n            size: style.fontSize as number,\n        };\n        fontData.common[0] = {\n            lineHeight: style.fontSize as number,\n        };\n\n        let positionX = 0;\n        let positionY = 0;\n\n        let canvas: HTMLCanvasElement;\n        let context: CanvasRenderingContext2D;\n        let baseTexture: BaseTexture;\n        let maxCharHeight = 0;\n        const baseTextures: BaseTexture[] = [];\n        const textures: Texture[] = [];\n\n        for (let i = 0; i < charsList.length; i++)\n        {\n            if (!canvas)\n            {\n                canvas = settings.ADAPTER.createCanvas();\n                canvas.width = textureWidth;\n                canvas.height = textureHeight;\n\n                context = canvas.getContext('2d');\n                baseTexture = new BaseTexture(canvas, { resolution });\n\n                baseTextures.push(baseTexture);\n                textures.push(new Texture(baseTexture));\n\n                fontData.page.push({\n                    id: textures.length - 1,\n                    file: '',\n                });\n            }\n\n            // Measure glyph dimensions\n            const character = charsList[i];\n            const metrics = TextMetrics.measureText(character, style, false, canvas);\n            const width = metrics.width;\n            const height = Math.ceil(metrics.height);\n\n            // This is ugly - but italics are given more space so they don't overlap\n            const textureGlyphWidth = Math.ceil((style.fontStyle === 'italic' ? 2 : 1) * width);\n\n            // Can't fit char anymore: next canvas please!\n            if (positionY >= textureHeight - (height * resolution))\n            {\n                if (positionY === 0)\n                {\n                    // We don't want user debugging an infinite loop (or do we? :)\n                    throw new Error(`[BitmapFont] textureHeight ${textureHeight}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n\n                // Create new atlas once current has filled up\n                canvas = null;\n                context = null;\n                baseTexture = null;\n                positionY = 0;\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            maxCharHeight = Math.max(height + metrics.fontProperties.descent, maxCharHeight);\n\n            // Wrap line once full row has been rendered\n            if ((textureGlyphWidth * resolution) + positionX >= lineWidth)\n            {\n                if (positionX === 0)\n                {\n                    // Avoid infinite loop (There can be some very wide char like '\\uFDFD'!)\n                    throw new Error(`[BitmapFont] textureWidth ${textureWidth}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n                positionY += maxCharHeight * resolution;\n                positionY = Math.ceil(positionY);\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            drawGlyph(canvas, context, metrics, positionX, positionY, resolution, style);\n\n            // Unique (numeric) ID mapping to this glyph\n            const id = extractCharCode(metrics.text);\n\n            // Create a texture holding just the glyph\n            fontData.char.push({\n                id,\n                page: textures.length - 1,\n                x: positionX / resolution,\n                y: positionY / resolution,\n                width: textureGlyphWidth,\n                height,\n                xoffset: 0,\n                yoffset: 0,\n                xadvance: Math.ceil(width\n                        - (style.dropShadow ? style.dropShadowDistance : 0)\n                        - (style.stroke ? style.strokeThickness : 0)),\n            });\n\n            positionX += (textureGlyphWidth + (2 * padding)) * resolution;\n            positionX = Math.ceil(positionX);\n        }\n\n        if (!options?.skipKerning)\n        {\n            // Brute-force kerning info, this can be expensive b/c it's an O(n²),\n            // but we're using measureText which is native and fast.\n            for (let i = 0, len = charsList.length; i < len; i++)\n            {\n                const first = charsList[i];\n\n                for (let j = 0; j < len; j++)\n                {\n                    const second = charsList[j];\n                    const c1 = context.measureText(first).width;\n                    const c2 = context.measureText(second).width;\n                    const total = context.measureText(first + second).width;\n                    const amount = total - (c1 + c2);\n\n                    if (amount)\n                    {\n                        fontData.kerning.push({\n                            first: extractCharCode(first),\n                            second: extractCharCode(second),\n                            amount,\n                        });\n                    }\n                }\n            }\n        }\n\n        const font = new BitmapFont(fontData, textures, true);\n\n        // Make it easier to replace a font\n        if (BitmapFont.available[name] !== undefined)\n        {\n            BitmapFont.uninstall(name);\n        }\n\n        BitmapFont.available[name] = font;\n\n        return font;\n    }\n}\n", "import { ObservablePoint, Point } from '@pixi/math';\nimport { settings } from '@pixi/settings';\nimport { Mesh, MeshGeometry, MeshMaterial } from '@pixi/mesh';\nimport { removeItems } from '@pixi/utils';\nimport { BitmapFont } from './BitmapFont';\nimport { splitTextToCharacters, extractCharCode } from './utils';\nimport msdfFrag from './shader/msdf.frag';\nimport msdfVert from './shader/msdf.vert';\nimport type { Rectangle } from '@pixi/math';\nimport type { Renderer } from '@pixi/core';\nimport { Program, Texture } from '@pixi/core';\nimport type { IBitmapTextStyle } from './BitmapTextStyle';\nimport type { TextStyleAlign } from '@pixi/text';\nimport { Container } from '@pixi/display';\nimport type { IDestroyOptions } from '@pixi/display';\nimport { BLEND_MODES } from '@pixi/constants';\n\ninterface PageMeshData\n{\n    index: number;\n    indexCount: number;\n    vertexCount: number;\n    uvsCount: number;\n    total: number;\n    mesh: Mesh;\n    vertices?: Float32Array;\n    uvs?: Float32Array;\n    indices?: Uint16Array;\n}\ninterface CharRenderData\n{\n    texture: Texture;\n    line: number;\n    charCode: number;\n    position: Point;\n    prevSpaces: number;\n}\n\n// If we ever need more than two pools, please make a Dict or something better.\nconst pageMeshDataDefaultPageMeshData: PageMeshData[] = [];\nconst pageMeshDataMSDFPageMeshData: PageMeshData[] = [];\nconst charRenderDataPool: CharRenderData[] = [];\n\n/**\n * A BitmapText object will create a line or multiple lines of text using bitmap font.\n *\n * The primary advantage of this class over Text is that all of your textures are pre-generated and loading,\n * meaning that rendering is fast, and changing text has no performance implications.\n *\n * Supporting character sets other than latin, such as CJK languages, may be impractical due to the number of characters.\n *\n * To split a line you can use '\\n', '\\r' or '\\r\\n' in your string.\n *\n * PixiJS can auto-generate fonts on-the-fly using BitmapFont or use fnt files provided by:\n * http://www.angelcode.com/products/bmfont/ for Windows or\n * http://www.bmglyph.com/ for Mac.\n *\n * You can also use SDF, MSDF and MTSDF BitmapFonts for vector-like scaling appearance provided by:\n * https://github.com/soimy/msdf-bmfont-xml for SDF and MSDF fnt files or\n * https://github.com/Chlumsky/msdf-atlas-gen for SDF, MSDF and MTSDF json files\n *\n * A BitmapText can only be created when the font is loaded.\n *\n * ```js\n * // in this case the font is in a file called 'desyrel.fnt'\n * let bitmapText = new PIXI.BitmapText(\"text using a fancy font!\", {\n *   fontName: \"Desyrel\",\n *   fontSize: 35,\n *   align: \"right\"\n * });\n * ```\n * @memberof PIXI\n */\nexport class BitmapText extends Container\n{\n    public static styleDefaults: Partial<IBitmapTextStyle> = {\n        align: 'left',\n        tint: 0xFFFFFF,\n        maxWidth: 0,\n        letterSpacing: 0,\n    };\n\n    /** Set to `true` if the BitmapText needs to be redrawn. */\n    public dirty: boolean;\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default PIXI.settings.RESOLUTION\n     */\n    _resolution: number;\n    _autoResolution: boolean;\n\n    /**\n     * Private tracker for the width of the overall text.\n     * @private\n     */\n    protected _textWidth: number;\n\n    /**\n     * Private tracker for the height of the overall text.\n     * @private\n     */\n    protected _textHeight: number;\n\n    /**\n     * Private tracker for the current text.\n     * @private\n     */\n    protected _text: string;\n\n    /**\n     * The max width of this bitmap text in pixels. If the text provided is longer than the\n     * value provided, line breaks will be automatically inserted in the last whitespace.\n     * Disable by setting value to 0\n     * @private\n     */\n    protected _maxWidth: number;\n\n    /**\n     * The max line height. This is useful when trying to use the total height of the Text,\n     * ie: when trying to vertically align. (Internally used)\n     * @private\n     */\n    protected _maxLineHeight: number;\n\n    /**\n     * Letter spacing. This is useful for setting the space between characters.\n     * @private\n     */\n    protected _letterSpacing: number;\n\n    /**\n     * Text anchor.\n     * @readonly\n     * @private\n     */\n    protected _anchor: ObservablePoint;\n\n    /**\n     * Private tracker for the current font.\n     * @private\n     */\n    protected _font?: BitmapFont;\n\n    /**\n     * Private tracker for the current font name.\n     * @private\n     */\n    protected _fontName: string;\n\n    /**\n     * Private tracker for the current font size.\n     * @private\n     */\n    protected _fontSize?: number;\n\n    /**\n     * Private tracker for the current text align.\n     * @type {string}\n     * @private\n     */\n    protected _align: TextStyleAlign;\n\n    /** Collection of page mesh data. */\n    protected _activePagesMeshData: PageMeshData[];\n\n    /**\n     * Private tracker for the current tint.\n     * @private\n     */\n    protected _tint = 0xFFFFFF;\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering.\n     * @default PIXI.settings.ROUND_PIXELS\n     */\n    protected _roundPixels: boolean;\n\n    /** Cached char texture is destroyed when BitmapText is destroyed. */\n    private _textureCache: Record<number, Texture>;\n\n    /**\n     * @param text - A string that you would like the text to display.\n     * @param style - The style parameters.\n     * @param {string} style.fontName - The installed BitmapFont name.\n     * @param {number} [style.fontSize] - The size of the font in pixels, e.g. 24. If undefined,\n     *.     this will default to the BitmapFont size.\n     * @param {string} [style.align='left'] - Alignment for multiline text ('left', 'center', 'right' or 'justify'),\n     *      does not affect single line text.\n     * @param {number} [style.tint=0xFFFFFF] - The tint color.\n     * @param {number} [style.letterSpacing=0] - The amount of spacing between letters.\n     * @param {number} [style.maxWidth=0] - The max width of the text before line wrapping.\n     */\n    constructor(text: string, style: Partial<IBitmapTextStyle> = {})\n    {\n        super();\n\n        // Apply the defaults\n        const { align, tint, maxWidth, letterSpacing, fontName, fontSize } = Object.assign(\n            {}, BitmapText.styleDefaults, style);\n\n        if (!BitmapFont.available[fontName])\n        {\n            throw new Error(`Missing BitmapFont \"${fontName}\"`);\n        }\n\n        this._activePagesMeshData = [];\n        this._textWidth = 0;\n        this._textHeight = 0;\n        this._align = align;\n        this._tint = tint;\n        this._font = undefined;\n        this._fontName = fontName;\n        this._fontSize = fontSize;\n        this.text = text;\n        this._maxWidth = maxWidth;\n        this._maxLineHeight = 0;\n        this._letterSpacing = letterSpacing;\n        this._anchor = new ObservablePoint((): void => { this.dirty = true; }, this, 0, 0);\n        this._roundPixels = settings.ROUND_PIXELS;\n        this.dirty = true;\n        this._resolution = settings.RESOLUTION;\n        this._autoResolution = true;\n        this._textureCache = {};\n    }\n\n    /** Renders text and updates it when needed. This should only be called if the BitmapFont is regenerated. */\n    public updateText(): void\n    {\n        const data = BitmapFont.available[this._fontName];\n        const fontSize = this.fontSize;\n        const scale = fontSize / data.size;\n        const pos = new Point();\n        const chars: CharRenderData[] = [];\n        const lineWidths = [];\n        const lineSpaces = [];\n        const text = this._text.replace(/(?:\\r\\n|\\r)/g, '\\n') || ' ';\n        const charsInput = splitTextToCharacters(text);\n        const maxWidth = this._maxWidth * data.size / fontSize;\n        const pageMeshDataPool = data.distanceFieldType === 'none'\n            ? pageMeshDataDefaultPageMeshData : pageMeshDataMSDFPageMeshData;\n\n        let prevCharCode = null;\n        let lastLineWidth = 0;\n        let maxLineWidth = 0;\n        let line = 0;\n        let lastBreakPos = -1;\n        let lastBreakWidth = 0;\n        let spacesRemoved = 0;\n        let maxLineHeight = 0;\n        let spaceCount = 0;\n\n        for (let i = 0; i < charsInput.length; i++)\n        {\n            const char = charsInput[i];\n            const charCode = extractCharCode(char);\n\n            if ((/(?:\\s)/).test(char))\n            {\n                lastBreakPos = i;\n                lastBreakWidth = lastLineWidth;\n                spaceCount++;\n            }\n\n            if (char === '\\r' || char === '\\n')\n            {\n                lineWidths.push(lastLineWidth);\n                lineSpaces.push(-1);\n                maxLineWidth = Math.max(maxLineWidth, lastLineWidth);\n                ++line;\n                ++spacesRemoved;\n\n                pos.x = 0;\n                pos.y += data.lineHeight;\n                prevCharCode = null;\n                spaceCount = 0;\n                continue;\n            }\n\n            const charData = data.chars[charCode];\n\n            if (!charData)\n            {\n                continue;\n            }\n\n            if (prevCharCode && charData.kerning[prevCharCode])\n            {\n                pos.x += charData.kerning[prevCharCode];\n            }\n\n            const charRenderData: CharRenderData = charRenderDataPool.pop() || {\n                texture: Texture.EMPTY,\n                line: 0,\n                charCode: 0,\n                prevSpaces: 0,\n                position: new Point(),\n            };\n\n            charRenderData.texture = charData.texture;\n            charRenderData.line = line;\n            charRenderData.charCode = charCode;\n            charRenderData.position.x = pos.x + charData.xOffset + (this._letterSpacing / 2);\n            charRenderData.position.y = pos.y + charData.yOffset;\n            charRenderData.prevSpaces = spaceCount;\n\n            chars.push(charRenderData);\n\n            lastLineWidth = charRenderData.position.x\n                + Math.max(charData.xAdvance - charData.xOffset, charData.texture.orig.width);\n            pos.x += charData.xAdvance + this._letterSpacing;\n            maxLineHeight = Math.max(maxLineHeight, (charData.yOffset + charData.texture.height));\n            prevCharCode = charCode;\n\n            if (lastBreakPos !== -1 && maxWidth > 0 && pos.x > maxWidth)\n            {\n                ++spacesRemoved;\n                removeItems(chars, 1 + lastBreakPos - spacesRemoved, 1 + i - lastBreakPos);\n                i = lastBreakPos;\n                lastBreakPos = -1;\n\n                lineWidths.push(lastBreakWidth);\n                lineSpaces.push(chars.length > 0 ? chars[chars.length - 1].prevSpaces : 0);\n                maxLineWidth = Math.max(maxLineWidth, lastBreakWidth);\n                line++;\n\n                pos.x = 0;\n                pos.y += data.lineHeight;\n                prevCharCode = null;\n                spaceCount = 0;\n            }\n        }\n\n        const lastChar = charsInput[charsInput.length - 1];\n\n        if (lastChar !== '\\r' && lastChar !== '\\n')\n        {\n            if ((/(?:\\s)/).test(lastChar))\n            {\n                lastLineWidth = lastBreakWidth;\n            }\n\n            lineWidths.push(lastLineWidth);\n            maxLineWidth = Math.max(maxLineWidth, lastLineWidth);\n            lineSpaces.push(-1);\n        }\n\n        const lineAlignOffsets = [];\n\n        for (let i = 0; i <= line; i++)\n        {\n            let alignOffset = 0;\n\n            if (this._align === 'right')\n            {\n                alignOffset = maxLineWidth - lineWidths[i];\n            }\n            else if (this._align === 'center')\n            {\n                alignOffset = (maxLineWidth - lineWidths[i]) / 2;\n            }\n            else if (this._align === 'justify')\n            {\n                alignOffset = lineSpaces[i] < 0 ? 0 : (maxLineWidth - lineWidths[i]) / lineSpaces[i];\n            }\n\n            lineAlignOffsets.push(alignOffset);\n        }\n\n        const lenChars = chars.length;\n\n        const pagesMeshData: Record<number, PageMeshData> = {};\n\n        const newPagesMeshData: PageMeshData[] = [];\n\n        const activePagesMeshData = this._activePagesMeshData;\n\n        pageMeshDataPool.push(...activePagesMeshData);\n\n        for (let i = 0; i < lenChars; i++)\n        {\n            const texture = chars[i].texture;\n            const baseTextureUid = texture.baseTexture.uid;\n\n            if (!pagesMeshData[baseTextureUid])\n            {\n                let pageMeshData = pageMeshDataPool.pop();\n\n                if (!pageMeshData)\n                {\n                    const geometry = new MeshGeometry();\n                    let material: MeshMaterial;\n                    let meshBlendMode: BLEND_MODES;\n\n                    if (data.distanceFieldType === 'none')\n                    {\n                        material = new MeshMaterial(Texture.EMPTY);\n                        meshBlendMode = BLEND_MODES.NORMAL;\n                    }\n                    else\n                    {\n                        material = new MeshMaterial(Texture.EMPTY,\n                            { program: Program.from(msdfVert, msdfFrag), uniforms: { uFWidth: 0 } });\n                        meshBlendMode = BLEND_MODES.NORMAL_NPM;\n                    }\n\n                    const mesh = new Mesh(geometry, material);\n\n                    mesh.blendMode = meshBlendMode;\n\n                    pageMeshData = {\n                        index: 0,\n                        indexCount: 0,\n                        vertexCount: 0,\n                        uvsCount: 0,\n                        total: 0,\n                        mesh,\n                        vertices: null,\n                        uvs: null,\n                        indices: null,\n                    };\n                }\n\n                // reset data..\n                pageMeshData.index = 0;\n                pageMeshData.indexCount = 0;\n                pageMeshData.vertexCount = 0;\n                pageMeshData.uvsCount = 0;\n                pageMeshData.total = 0;\n\n                // TODO need to get page texture here somehow..\n                const { _textureCache } = this;\n\n                _textureCache[baseTextureUid] = _textureCache[baseTextureUid] || new Texture(texture.baseTexture);\n                pageMeshData.mesh.texture = _textureCache[baseTextureUid];\n\n                pageMeshData.mesh.tint = this._tint;\n\n                newPagesMeshData.push(pageMeshData);\n\n                pagesMeshData[baseTextureUid] = pageMeshData;\n            }\n\n            pagesMeshData[baseTextureUid].total++;\n        }\n\n        // lets find any previously active pageMeshDatas that are no longer required for\n        // the updated text (if any), removed and return them to the pool.\n        for (let i = 0; i < activePagesMeshData.length; i++)\n        {\n            if (newPagesMeshData.indexOf(activePagesMeshData[i]) === -1)\n            {\n                this.removeChild(activePagesMeshData[i].mesh);\n            }\n        }\n\n        // next lets add any new meshes, that have not yet been added to this BitmapText\n        // we only add if its not already a child of this BitmapObject\n        for (let i = 0; i < newPagesMeshData.length; i++)\n        {\n            if (newPagesMeshData[i].mesh.parent !== this)\n            {\n                this.addChild(newPagesMeshData[i].mesh);\n            }\n        }\n\n        // active page mesh datas are set to be the new pages added.\n        this._activePagesMeshData = newPagesMeshData;\n\n        for (const i in pagesMeshData)\n        {\n            const pageMeshData = pagesMeshData[i];\n            const total = pageMeshData.total;\n\n            // lets only allocate new buffers if we can fit the new text in the current ones..\n            // unless that is, we will be batching. Currently batching dose not respect the size property of mesh\n            if (!(pageMeshData.indices?.length > 6 * total) || pageMeshData.vertices.length < Mesh.BATCHABLE_SIZE * 2)\n            {\n                pageMeshData.vertices = new Float32Array(4 * 2 * total);\n                pageMeshData.uvs = new Float32Array(4 * 2 * total);\n                pageMeshData.indices = new Uint16Array(6 * total);\n            }\n            else\n            {\n                const total = pageMeshData.total;\n                const vertices = pageMeshData.vertices;\n\n                // Clear the garbage at the end of the vertices buffer. This will prevent the bounds miscalculation.\n                for (let i = total * 4 * 2; i < vertices.length; i++)\n                {\n                    vertices[i] = 0;\n                }\n            }\n\n            // as a buffer maybe bigger than the current word, we set the size of the meshMaterial\n            // to match the number of letters needed\n            pageMeshData.mesh.size = 6 * total;\n        }\n\n        for (let i = 0; i < lenChars; i++)\n        {\n            const char = chars[i];\n            let offset = char.position.x + (lineAlignOffsets[char.line] * (this._align === 'justify' ? char.prevSpaces : 1));\n\n            if (this._roundPixels)\n            {\n                offset = Math.round(offset);\n            }\n\n            const xPos = offset * scale;\n            const yPos = char.position.y * scale;\n            const texture = char.texture;\n\n            const pageMesh = pagesMeshData[texture.baseTexture.uid];\n\n            const textureFrame = texture.frame;\n            const textureUvs = texture._uvs;\n\n            const index = pageMesh.index++;\n\n            pageMesh.indices[(index * 6) + 0] = 0 + (index * 4);\n            pageMesh.indices[(index * 6) + 1] = 1 + (index * 4);\n            pageMesh.indices[(index * 6) + 2] = 2 + (index * 4);\n            pageMesh.indices[(index * 6) + 3] = 0 + (index * 4);\n            pageMesh.indices[(index * 6) + 4] = 2 + (index * 4);\n            pageMesh.indices[(index * 6) + 5] = 3 + (index * 4);\n\n            pageMesh.vertices[(index * 8) + 0] = xPos;\n            pageMesh.vertices[(index * 8) + 1] = yPos;\n\n            pageMesh.vertices[(index * 8) + 2] = xPos + (textureFrame.width * scale);\n            pageMesh.vertices[(index * 8) + 3] = yPos;\n\n            pageMesh.vertices[(index * 8) + 4] = xPos + (textureFrame.width * scale);\n            pageMesh.vertices[(index * 8) + 5] = yPos + (textureFrame.height * scale);\n\n            pageMesh.vertices[(index * 8) + 6] = xPos;\n            pageMesh.vertices[(index * 8) + 7] = yPos + (textureFrame.height * scale);\n\n            pageMesh.uvs[(index * 8) + 0] = textureUvs.x0;\n            pageMesh.uvs[(index * 8) + 1] = textureUvs.y0;\n\n            pageMesh.uvs[(index * 8) + 2] = textureUvs.x1;\n            pageMesh.uvs[(index * 8) + 3] = textureUvs.y1;\n\n            pageMesh.uvs[(index * 8) + 4] = textureUvs.x2;\n            pageMesh.uvs[(index * 8) + 5] = textureUvs.y2;\n\n            pageMesh.uvs[(index * 8) + 6] = textureUvs.x3;\n            pageMesh.uvs[(index * 8) + 7] = textureUvs.y3;\n        }\n\n        this._textWidth = maxLineWidth * scale;\n        this._textHeight = (pos.y + data.lineHeight) * scale;\n\n        for (const i in pagesMeshData)\n        {\n            const pageMeshData = pagesMeshData[i];\n\n            // apply anchor\n            if (this.anchor.x !== 0 || this.anchor.y !== 0)\n            {\n                let vertexCount = 0;\n\n                const anchorOffsetX = this._textWidth * this.anchor.x;\n                const anchorOffsetY = this._textHeight * this.anchor.y;\n\n                for (let i = 0; i < pageMeshData.total; i++)\n                {\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetX;\n                    pageMeshData.vertices[vertexCount++] -= anchorOffsetY;\n                }\n            }\n\n            this._maxLineHeight = maxLineHeight * scale;\n\n            const vertexBuffer = pageMeshData.mesh.geometry.getBuffer('aVertexPosition');\n            const textureBuffer = pageMeshData.mesh.geometry.getBuffer('aTextureCoord');\n            const indexBuffer = pageMeshData.mesh.geometry.getIndex();\n\n            vertexBuffer.data = pageMeshData.vertices;\n            textureBuffer.data = pageMeshData.uvs;\n            indexBuffer.data = pageMeshData.indices;\n\n            vertexBuffer.update();\n            textureBuffer.update();\n            indexBuffer.update();\n        }\n\n        for (let i = 0; i < chars.length; i++)\n        {\n            charRenderDataPool.push(chars[i]);\n        }\n\n        this._font = data;\n        this.dirty = false;\n    }\n\n    updateTransform(): void\n    {\n        this.validate();\n        this.containerUpdateTransform();\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this._autoResolution && this._resolution !== renderer.resolution)\n        {\n            this._resolution = renderer.resolution;\n            this.dirty = true;\n        }\n\n        // Update the uniform\n        const { distanceFieldRange, distanceFieldType, size } = BitmapFont.available[this._fontName];\n\n        if (distanceFieldType !== 'none')\n        {\n            // Inject the shader code with the correct value\n            const { a, b, c, d } = this.worldTransform;\n\n            const dx = Math.sqrt((a * a) + (b * b));\n            const dy = Math.sqrt((c * c) + (d * d));\n            const worldScale = (Math.abs(dx) + Math.abs(dy)) / 2;\n\n            const fontScale = this.fontSize / size;\n\n            for (const mesh of this._activePagesMeshData)\n            {\n                mesh.mesh.shader.uniforms.uFWidth = worldScale * distanceFieldRange * fontScale * this._resolution;\n            }\n        }\n\n        super._render(renderer);\n    }\n\n    /**\n     * Validates text before calling parent's getLocalBounds\n     * @returns - The rectangular bounding area\n     */\n    public getLocalBounds(): Rectangle\n    {\n        this.validate();\n\n        return super.getLocalBounds();\n    }\n\n    /**\n     * Updates text when needed\n     * @private\n     */\n    protected validate(): void\n    {\n        const font = BitmapFont.available[this._fontName];\n\n        if (!font)\n        {\n            throw new Error(`Missing BitmapFont \"${this._fontName}\"`);\n        }\n        if (this._font !== font)\n        {\n            this.dirty = true;\n        }\n\n        if (this.dirty)\n        {\n            this.updateText();\n        }\n    }\n\n    /**\n     * The tint of the BitmapText object.\n     * @default 0xffffff\n     */\n    public get tint(): number\n    {\n        return this._tint;\n    }\n\n    public set tint(value: number)\n    {\n        if (this._tint === value) return;\n\n        this._tint = value;\n\n        for (let i = 0; i < this._activePagesMeshData.length; i++)\n        {\n            this._activePagesMeshData[i].mesh.tint = value;\n        }\n    }\n\n    /**\n     * The alignment of the BitmapText object.\n     * @member {string}\n     * @default 'left'\n     */\n    public get align(): TextStyleAlign\n    {\n        return this._align;\n    }\n\n    public set align(value: TextStyleAlign)\n    {\n        if (this._align !== value)\n        {\n            this._align = value;\n            this.dirty = true;\n        }\n    }\n\n    /** The name of the BitmapFont. */\n    public get fontName(): string\n    {\n        return this._fontName;\n    }\n\n    public set fontName(value: string)\n    {\n        if (!BitmapFont.available[value])\n        {\n            throw new Error(`Missing BitmapFont \"${value}\"`);\n        }\n\n        if (this._fontName !== value)\n        {\n            this._fontName = value;\n            this.dirty = true;\n        }\n    }\n\n    /** The size of the font to display. */\n    public get fontSize(): number\n    {\n        return this._fontSize ?? BitmapFont.available[this._fontName].size;\n    }\n\n    public set fontSize(value: number | undefined)\n    {\n        if (this._fontSize !== value)\n        {\n            this._fontSize = value;\n            this.dirty = true;\n        }\n    }\n\n    /**\n     * The anchor sets the origin point of the text.\n     *\n     * The default is `(0,0)`, this means the text's origin is the top left.\n     *\n     * Setting the anchor to `(0.5,0.5)` means the text's origin is centered.\n     *\n     * Setting the anchor to `(1,1)` would mean the text's origin point will be the bottom right corner.\n     */\n    public get anchor(): ObservablePoint\n    {\n        return this._anchor;\n    }\n\n    public set anchor(value: ObservablePoint)\n    {\n        if (typeof value === 'number')\n        {\n            this._anchor.set(value);\n        }\n        else\n        {\n            this._anchor.copyFrom(value);\n        }\n    }\n\n    /** The text of the BitmapText object. */\n    public get text(): string\n    {\n        return this._text;\n    }\n\n    public set text(text: string)\n    {\n        text = String(text === null || text === undefined ? '' : text);\n\n        if (this._text === text)\n        {\n            return;\n        }\n        this._text = text;\n        this.dirty = true;\n    }\n\n    /**\n     * The max width of this bitmap text in pixels. If the text provided is longer than the\n     * value provided, line breaks will be automatically inserted in the last whitespace.\n     * Disable by setting the value to 0.\n     */\n    public get maxWidth(): number\n    {\n        return this._maxWidth;\n    }\n\n    public set maxWidth(value: number)\n    {\n        if (this._maxWidth === value)\n        {\n            return;\n        }\n        this._maxWidth = value;\n        this.dirty = true;\n    }\n\n    /**\n     * The max line height. This is useful when trying to use the total height of the Text,\n     * i.e. when trying to vertically align.\n     * @readonly\n     */\n    public get maxLineHeight(): number\n    {\n        this.validate();\n\n        return this._maxLineHeight;\n    }\n\n    /**\n     * The width of the overall text, different from fontSize,\n     * which is defined in the style object.\n     * @readonly\n     */\n    public get textWidth(): number\n    {\n        this.validate();\n\n        return this._textWidth;\n    }\n\n    /** Additional space between characters. */\n    public get letterSpacing(): number\n    {\n        return this._letterSpacing;\n    }\n\n    public set letterSpacing(value: number)\n    {\n        if (this._letterSpacing !== value)\n        {\n            this._letterSpacing = value;\n            this.dirty = true;\n        }\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}\n     * @default PIXI.settings.ROUND_PIXELS\n     */\n    public get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    public set roundPixels(value: boolean)\n    {\n        if (value !== this._roundPixels)\n        {\n            this._roundPixels = value;\n            this.dirty = true;\n        }\n    }\n\n    /**\n     * The height of the overall text, different from fontSize,\n     * which is defined in the style object.\n     * @readonly\n     */\n    public get textHeight(): number\n    {\n        this.validate();\n\n        return this._textHeight;\n    }\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default 1\n     */\n    get resolution(): number\n    {\n        return this._resolution;\n    }\n\n    set resolution(value: number)\n    {\n        this._autoResolution = false;\n\n        if (this._resolution === value)\n        {\n            return;\n        }\n\n        this._resolution = value;\n        this.dirty = true;\n    }\n\n    destroy(options?: boolean | IDestroyOptions): void\n    {\n        const { _textureCache } = this;\n        const data = BitmapFont.available[this._fontName];\n        const pageMeshDataPool = data.distanceFieldType === 'none'\n            ? pageMeshDataDefaultPageMeshData : pageMeshDataMSDFPageMeshData;\n\n        pageMeshDataPool.push(...this._activePagesMeshData);\n        for (const pageMeshData of this._activePagesMeshData)\n        {\n            this.removeChild(pageMeshData.mesh);\n        }\n        this._activePagesMeshData = [];\n\n        // Release references to any cached textures in page pool\n        pageMeshDataPool\n            .filter((page) => _textureCache[page.mesh.texture.baseTexture.uid])\n            .forEach((page) =>\n            {\n                page.mesh.texture = Texture.EMPTY;\n            });\n\n        for (const id in _textureCache)\n        {\n            const texture = _textureCache[id];\n\n            texture.destroy();\n            delete _textureCache[id];\n        }\n\n        this._font = null;\n        this._textureCache = null;\n\n        super.destroy(options);\n    }\n}\n", "import { LoaderResource } from '@pixi/loaders';\nimport { autoDetectFormat } from './formats';\nimport { BitmapFont } from './BitmapFont';\n\nimport type { Loader } from '@pixi/loaders';\nimport type { Dict } from '@pixi/utils';\nimport type { ExtensionMetadata, Texture } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * {@link PIXI.Loader Loader} middleware for loading\n * bitmap-based fonts suitable for using with {@link PIXI.BitmapText}.\n * @memberof PIXI\n */\nexport class BitmapFontLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * Called when the plugin is installed.\n     * @see PIXI.extensions.add\n     */\n    public static add(): void\n    {\n        LoaderResource.setExtensionXhrType('fnt', LoaderResource.XHR_RESPONSE_TYPE.TEXT);\n    }\n\n    /**\n     * Called after a resource is loaded.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param this\n     * @param {PIXI.LoaderResource} resource\n     * @param {Function} next\n     */\n    static use(this: Loader, resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        const format = autoDetectFormat(resource.data);\n\n        // Resource was not recognised as any of the expected font data format\n        if (!format)\n        {\n            next();\n\n            return;\n        }\n\n        const baseUrl = BitmapFontLoader.getBaseUrl(this, resource);\n        const data = format.parse(resource.data);\n        const textures: Dict<Texture> = {};\n\n        // Handle completed, when the number of textures\n        // load is the same number as references in the fnt file\n        const completed = (page: LoaderResource): void =>\n        {\n            textures[page.metadata.pageFile] = page.texture;\n\n            if (Object.keys(textures).length === data.page.length)\n            {\n                resource.bitmapFont = BitmapFont.install(data, textures, true);\n                next();\n            }\n        };\n\n        for (let i = 0; i < data.page.length; ++i)\n        {\n            const pageFile = data.page[i].file;\n            const url = baseUrl + pageFile;\n            let exists = false;\n\n            // incase the image is loaded outside\n            // using the same loader, resource will be available\n            for (const name in this.resources)\n            {\n                const bitmapResource: LoaderResource = this.resources[name];\n\n                if (bitmapResource.url === url)\n                {\n                    bitmapResource.metadata.pageFile = pageFile;\n                    if (bitmapResource.texture)\n                    {\n                        completed(bitmapResource);\n                    }\n                    else\n                    {\n                        bitmapResource.onAfterMiddleware.add(completed);\n                    }\n                    exists = true;\n                    break;\n                }\n            }\n\n            // texture is not loaded, we'll attempt to add\n            // it to the load and add the texture to the list\n            if (!exists)\n            {\n                // Standard loading options for images\n                const options = {\n                    crossOrigin: resource.crossOrigin,\n                    loadType: LoaderResource.LOAD_TYPE.IMAGE,\n                    metadata: Object.assign(\n                        { pageFile },\n                        resource.metadata.imageMetadata\n                    ),\n                    parentResource: resource,\n                };\n\n                this.add(url, options, completed);\n            }\n        }\n    }\n\n    /**\n     * Get folder path from a resource.\n     * @param loader\n     * @param resource\n     */\n    private static getBaseUrl(loader: Loader, resource: LoaderResource): string\n    {\n        let resUrl = !resource.isDataUrl ? BitmapFontLoader.dirname(resource.url) : '';\n\n        if (resource.isDataUrl)\n        {\n            if (resUrl === '.')\n            {\n                resUrl = '';\n            }\n\n            if (loader.baseUrl && resUrl)\n            {\n                // if baseurl has a trailing slash then add one to resUrl so the replace works below\n                if (loader.baseUrl.charAt(loader.baseUrl.length - 1) === '/')\n                {\n                    resUrl += '/';\n                }\n            }\n        }\n\n        // remove baseUrl from resUrl\n        resUrl = resUrl.replace(loader.baseUrl, '');\n\n        // if there is an resUrl now, it needs a trailing slash. Ensure that it does if the string isn't empty.\n        if (resUrl && resUrl.charAt(resUrl.length - 1) !== '/')\n        {\n            resUrl += '/';\n        }\n\n        return resUrl;\n    }\n\n    /**\n     * Replacement for NodeJS's path.dirname\n     * @param {string} url - Path to get directory for\n     */\n    private static dirname(url: string): string\n    {\n        const dir = url\n            .replace(/\\\\/g, '/') // convert windows notation to UNIX notation, URL-safe because it's a forbidden character\n            .replace(/\\/$/, '') // replace trailing slash\n            .replace(/\\/[^\\/]*$/, ''); // remove everything after the last\n\n        // File request is relative, use current directory\n        if (dir === url)\n        {\n            return '.';\n        }\n        // Started with a slash\n        else if (dir === '')\n        {\n            return '/';\n        }\n\n        return dir;\n    }\n}\n"], "names": ["TEXT_GRADIENT", "hex2rgb", "string2hex", "getResolutionOfUrl", "ALPHA_MODES", "MIPMAP_MODES", "Rectangle", "Texture", "TextStyle", "settings", "BaseTexture", "TextMetrics", "ObservablePoint", "Point", "removeItems", "MeshGeometry", "MeshMaterial", "BLEND_MODES", "Program", "mesh", "<PERSON><PERSON>", "Container", "LoaderResource", "ExtensionType"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;AC3BA;AAEA;;;AAGG;AACH,IAAA,cAAA,kBAAA,YAAA;AAoBI,IAAA,SAAA,cAAA,GAAA;AAEI,QAAA,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA;;ACUD;;;AAGG;AACH,IAAA,UAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,UAAA,GAAA;KA0GC;AAxGG;;;;AAIG;IACI,UAAI,CAAA,IAAA,GAAX,UAAY,IAAa,EAAA;AAErB,QAAA,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KACvE,CAAA;AAED;;;;AAIG;IACI,UAAK,CAAA,KAAA,GAAZ,UAAa,GAAW,EAAA;;QAGpB,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC3C,QAAA,IAAM,OAAO,GAAuB;AAChC,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,OAAO,EAAE,EAAE;AACX,YAAA,QAAQ,EAAE,EAAE;AACZ,YAAA,aAAa,EAAE,EAAE;SACpB,CAAC;AAEF,QAAA,KAAK,IAAM,CAAC,IAAI,KAAK,EACrB;;AAEI,YAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAyB,CAAC;;YAGpE,IAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;;YAGzE,IAAM,QAAQ,GAAQ,EAAE,CAAC;AAEzB,YAAA,KAAK,IAAM,GAAC,IAAI,aAAa,EAC7B;;gBAEI,IAAM,KAAK,GAAG,aAAa,CAAC,GAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,gBAAA,IAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;AAGrB,gBAAA,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;;AAG7C,gBAAA,IAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;;AAGxC,gBAAA,IAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,UAAU,CAAC;AAExD,gBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACzB,aAAA;;YAGD,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,IAAM,IAAI,GAAG,IAAI,cAAc,EAAE,CAAC;AAElC,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,IAAI,EAAK,EAAA,OAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1C,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;SAChC,CAAC,CAAA,EAAA,CAAC,CAAC;AAEJ,QAAA,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,MAAM,EAAK,EAAA,OAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YAChD,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC;SAC9C,CAAC,CAAA,EAAA,CAAC,CAAC;AAEJ,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,IAAI,EAAK,EAAA,OAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1C,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI;SAClB,CAAC,CAAA,EAAA,CAAC,CAAC;AAEJ,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,IAAI,EAAK,EAAA,OAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1C,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;YACzB,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;YAC7B,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;YACvB,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;YACvB,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;YAC/B,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACjC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;SACxC,CAAC,CAAA,EAAA,CAAC,CAAC;AAEJ,QAAA,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,OAAO,EAAK,EAAA,OAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACnD,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;YAClC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACpC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;SACvC,CAAC,CAAA,EAAA,CAAC,CAAC;AAEJ,QAAA,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,EAAE,EAAK,EAAA,OAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YAC1D,aAAa,EAAE,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC;YAC7C,SAAS,EAAE,EAAE,CAAC,SAAS;SAC1B,CAAC,CAAA,EAAA,CAAC,CAAC;AAEJ,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IACL,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA;;ACzJD;;;AAGG;AACH,IAAA,SAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,SAAA,GAAA;KAwFC;AAtFG;;;;AAIG;IACI,SAAI,CAAA,IAAA,GAAX,UAAY,IAAa,EAAA;QAErB,OAAO,IAAI,YAAY,WAAW;AAC3B,eAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,MAAM;AACxC,eAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;KAC7E,CAAA;AAED;;;;AAIG;IACI,SAAK,CAAA,KAAA,GAAZ,UAAa,GAAgB,EAAA;AAEzB,QAAA,IAAM,IAAI,GAAG,IAAI,cAAc,EAAE,CAAC;QAClC,IAAM,IAAI,GAAG,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAM,MAAM,GAAG,GAAG,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAM,IAAI,GAAG,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAM,IAAI,GAAG,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAM,OAAO,GAAG,GAAG,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACpD,IAAM,aAAa,GAAG,GAAG,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;AAEhE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;AAClC,gBAAA,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;AACnD,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EACtC;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACb,gBAAA,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;AACjE,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACX,gBAAA,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;gBACjD,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;AACI,YAAA,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAEvB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;AAC3C,gBAAA,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;gBACpD,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;gBACzC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;gBACzC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;gBACjD,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACnD,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;gBACrD,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;gBACrD,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;AAC1D,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EACvC;AACI,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACd,gBAAA,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;AACrD,gBAAA,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;AACvD,gBAAA,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;AAC1D,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAC7C;AACI,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC;AACrD,gBAAA,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC;AAC9E,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;AC3FD;;;AAGG;AACH,IAAA,eAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,eAAA,GAAA;KA8BC;AA5BG;;;;AAIG;IACI,eAAI,CAAA,IAAA,GAAX,UAAY,IAAa,EAAA;AAErB,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAC3D;AACI,YAAA,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAEzE,YAAA,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;AAIG;IACI,eAAK,CAAA,KAAA,GAAZ,UAAa,MAAc,EAAA;AAEvB,QAAA,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAE3E,QAAA,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC/B,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA;;ACjCD;AACA,IAAM,OAAO,GAAG;IACZ,UAAU;IACV,SAAS;IACT,eAAe,EACT,CAAC;AAEX;;;;;AAKG;AACG,SAAU,gBAAgB,CAAC,IAAa,EAAA;AAE1C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EACvC;QACI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACzB;AACI,YAAA,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;AACrB,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AAChB;;ACzBA;AAEA;;;;;;;;;;AAUG;AACa,SAAA,iBAAiB,CAC7B,MAAyB,EACzB,OAAiC,EACjC,KAAgB,EAChB,UAAkB,EAClB,KAAe,EACf,OAAoB,EAAA;;;;AAMpB,IAAA,IAAM,SAAS,GAAuD,KAAK,CAAC,IAAW,CAAC;AAExF,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAC7B;AACI,QAAA,OAAO,SAAS,CAAC;AACpB,KAAA;AACI,SAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAC/B;AACI,QAAA,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;AACvB,KAAA;;;AAID,IAAA,IAAI,QAAmC,CAAC;;;AAIxC,IAAA,IAAM,oBAAoB,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC;;AAG/E,IAAA,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;AAEnC,IAAA,IAAM,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,UAAU,IAAI,oBAAoB,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;AACjF,IAAA,IAAM,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,IAAI,oBAAoB,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;;AAGnF,IAAA,IAAM,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;IAC/B,IAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;;AAG1D,IAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAC7B;AACI,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EACpC;AACI,YAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;AAC3C,SAAA;AACJ,KAAA;;;IAID,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAA,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAE7B,IAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE1B,IAAA,IAAI,KAAK,CAAC,gBAAgB,KAAKA,kBAAa,CAAC,eAAe,EAC5D;;AAEI,QAAA,QAAQ,GAAG,OAAO,CAAC,oBAAoB,CAAC,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC;;;;;;;QASzF,IAAI,iBAAiB,GAAG,CAAC,CAAC;;QAG1B,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,CAAC;;AAG3E,QAAA,IAAM,kBAAkB,GAAG,UAAU,GAAG,MAAM,CAAC;AAE/C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;AACI,YAAA,IAAM,WAAW,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;AAE3C,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;;gBAEI,IAAI,QAAQ,GAAG,CAAC,CAAC;AAEjB,gBAAA,IAAI,OAAO,iBAAiB,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC5C;AACI,oBAAA,QAAQ,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AACnC,iBAAA;AAED,qBAAA;AACI,oBAAA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC9B,iBAAA;AAED,gBAAA,IAAM,UAAU,GAAG,CAAC,WAAW,GAAG,MAAM,KAAK,QAAQ,GAAG,kBAAkB,CAAC,CAAC;;gBAG5E,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;gBAE1D,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBACvC,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,iBAAiB,GAAG,WAAW,CAAC;AACnC,aAAA;AACJ,SAAA;AACJ,KAAA;AAED,SAAA;;AAEI,QAAA,QAAQ,GAAG,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;;;AAI1F,QAAA,IAAM,eAAe,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;YACI,IAAI,IAAI,SAAQ,CAAC;AAEjB,YAAA,IAAI,OAAO,iBAAiB,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC5C;AACI,gBAAA,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC/B,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,GAAG,gBAAgB,GAAG,eAAe,CAAC;AAC7C,aAAA;YACD,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,YAAA,gBAAgB,EAAE,CAAC;AACtB,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AACpB;;ACrJA;AAEA;;;;;;;;;;;;AAYG;AACa,SAAA,SAAS,CACrB,MAAyB,EACzB,OAAiC,EACjC,OAAoB,EACpB,CAAS,EACT,CAAS,EACT,UAAkB,EAClB,KAAgB,EAAA;AAGhB,IAAA,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC1B,IAAA,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AAE9C,IAAA,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAA,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAEtC,IAAA,IAAM,EAAE,GAAG,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC;IACrC,IAAM,EAAE,GAAG,EAAE,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;AAExC,IAAA,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;AACpC,IAAA,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,eAAe,CAAC;AAC1C,IAAA,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;AAC1C,IAAA,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AAClC,IAAA,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;;AAGtC,IAAA,OAAO,CAAC,SAAS,GAAG,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3F,IAAA,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,MAAgB,CAAC;IAE7C,IAAI,KAAK,CAAC,UAAU,EACpB;AACI,QAAA,IAAM,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC;QAC9C,IAAM,GAAG,GAAGC,aAAO,CAAC,OAAO,eAAe,KAAK,QAAQ,GAAG,eAAe,GAAGC,gBAAU,CAAC,eAAe,CAAC,CAAC,CAAC;AACzG,QAAA,IAAM,cAAc,GAAG,KAAK,CAAC,cAAc,GAAG,UAAU,CAAC;AACzD,QAAA,IAAM,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,GAAG,UAAU,CAAC;AAEjE,QAAA,OAAO,CAAC,WAAW,GAAG,OAAA,GAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,GAAA,GAAA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAA,GAAA,GAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,GAAA,GAAA,KAAK,CAAC,eAAe,MAAG,CAAC;AACvG,QAAA,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC;AACpC,QAAA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,kBAAkB,CAAC;AAC7E,QAAA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,kBAAkB,CAAC;AAChF,KAAA;AAED,SAAA;AACI,QAAA,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;AAC9B,QAAA,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;AACvB,QAAA,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;AAC1B,QAAA,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;AAC7B,KAAA;AAED,IAAA,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,EACzC;AACI,QAAA,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;AAClF,KAAA;IACD,IAAI,KAAK,CAAC,IAAI,EACd;AACI,QAAA,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;AAChF,KAAA;AAED,IAAA,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAEvC,IAAA,OAAO,CAAC,SAAS,GAAG,kBAAkB,CAAC;AAC3C;;AChFA;;;;AAIG;AACG,SAAU,qBAAqB,CAAC,IAAY,EAAA;IAE9C,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC1D;;ACNA;;;;;;;AAOG;AACG,SAAU,iBAAiB,CAAC,KAAqC,EAAA;;AAGnE,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAC7B;AACI,QAAA,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;AACnB,KAAA;;IAGD,IAAM,MAAM,GAAa,EAAE,CAAC;AAE5B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAC5C;AACI,QAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;AAGtB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EACvB;AACI,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EACrB;gBACI,MAAM,IAAI,KAAK,CAAC,gEAAA,GAAiE,IAAI,CAAC,MAAM,GAAG,GAAA,CAAC,CAAC;AACpG,aAAA;YAED,IAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxC,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,OAAO,GAAG,SAAS,EACvB;AACI,gBAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC7D,aAAA;AAED,YAAA,KAAK,IAAI,GAAC,GAAG,SAAS,EAAE,GAAC,GAAG,OAAO,EAAE,GAAC,IAAI,GAAC,EAAE,GAAC,EAAE,EAChD;gBACI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAC,CAAC,CAAC,CAAC;AACvC,aAAA;AACJ,SAAA;;AAGD,aAAA;YACI,MAAM,CAAC,IAAI,CAAX,KAAA,CAAA,MAAM,EAAS,qBAAqB,CAAC,IAAI,CAAC,CAAE,CAAA;AAC/C,SAAA;AACJ,KAAA;AAED,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EACvB;AACI,QAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;AACzE,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAClB;;AC3DA;;;;AAIG;AACG,SAAU,eAAe,CAAC,GAAW,EAAA;IAEvC,OAAO,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACpE;;ACwDA;;;;AAIG;AACH,IAAA,UAAA,kBAAA,YAAA;AAyEI;;;;;AAKG;AACH,IAAA,SAAA,UAAA,CAAY,IAAoB,EAAE,QAAmC,EAAE,YAAsB,EAAA;;AAElF,QAAA,IAAA,IAAI,GAAI,IAAI,CAAC,IAAI,GAAb,CAAc;AAClB,QAAA,IAAA,MAAM,GAAI,IAAI,CAAC,MAAM,GAAf,CAAgB;AACtB,QAAA,IAAA,IAAI,GAAI,IAAI,CAAC,IAAI,GAAb,CAAc;AAClB,QAAA,IAAA,aAAa,GAAI,IAAI,CAAC,aAAa,GAAtB,CAAuB;QAC3C,IAAM,GAAG,GAAGC,wBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAM,YAAY,GAAkB,EAAE,CAAC;AAEvC,QAAA,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;AAClC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;;;AAIjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACzC;AACU,YAAA,IAAA,EAAe,GAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAzB,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,UAAiB,CAAC;AAElC,YAAA,YAAY,CAAC,EAAE,CAAC,GAAG,QAAQ,YAAY,KAAK;AACxC,kBAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;;AAGnC,YAAA,IAAI,CAAA,aAAa,KAAb,IAAA,IAAA,aAAa,uBAAb,aAAa,CAAE,SAAS,KAAI,aAAa,CAAC,SAAS,KAAK,MAAM,EAClE;gBACI,YAAY,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS,GAAGC,qBAAW,CAAC,sBAAsB,CAAC;gBAC5E,YAAY,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,GAAGC,sBAAY,CAAC,GAAG,CAAC;AAC1D,aAAA;AACJ,SAAA;;AAGD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACzC;AACU,YAAA,IAAA,EAAe,GAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAzB,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,MAAI,UAAiB,CAAC;AAC9B,YAAA,IAAA,EAAsD,GAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAhE,CAAC,GAAA,EAAA,CAAA,CAAA,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,OAAO,GAAA,EAAA,CAAA,OAAA,EAAE,OAAO,GAAA,EAAA,CAAA,OAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,QAAiB,CAAC;YAEvE,CAAC,IAAI,GAAG,CAAC;YACT,CAAC,IAAI,GAAG,CAAC;YACT,KAAK,IAAI,GAAG,CAAC;YACb,MAAM,IAAI,GAAG,CAAC;YACd,OAAO,IAAI,GAAG,CAAC;YACf,OAAO,IAAI,GAAG,CAAC;YACf,QAAQ,IAAI,GAAG,CAAC;AAEhB,YAAA,IAAM,IAAI,GAAG,IAAIC,cAAS,CACtB,CAAC,IAAI,YAAY,CAAC,MAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,EACtC,CAAC,IAAI,YAAY,CAAC,MAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,EACtC,KAAK,EACL,MAAM,CACT,CAAC;AAEF,YAAA,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;AACb,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,QAAQ,EAAE,QAAQ;AAClB,gBAAA,OAAO,EAAE,EAAE;AACX,gBAAA,OAAO,EAAE,IAAIC,YAAO,CAChB,YAAY,CAAC,MAAI,CAAC,CAAC,WAAW,EAC9B,IAAI,CACP;AACD,gBAAA,IAAI,EAAA,MAAA;aACP,CAAC;AACL,SAAA;;AAGD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAC5C;AACQ,YAAA,IAAA,KAA4B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAzC,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,MAAM,YAAoB,CAAC;YAEhD,KAAK,IAAI,GAAG,CAAC;YACb,MAAM,IAAI,GAAG,CAAC;YACd,MAAM,IAAI,GAAG,CAAC;AAEd,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EACtB;AACI,gBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;AAC9C,aAAA;AACJ,SAAA;;QAGD,IAAI,CAAC,kBAAkB,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,aAAa,CAAC;AACvD,QAAA,IAAI,CAAC,iBAAiB,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,MAAM,CAAC;KAC9E;;AAGM,IAAA,UAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;AAEI,QAAA,KAAK,IAAM,EAAE,IAAI,IAAI,CAAC,KAAK,EAC3B;YACI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;AACjC,SAAA;AAED,QAAA,KAAK,IAAM,EAAE,IAAI,IAAI,CAAC,YAAY,EAClC;YACI,IAAI,IAAI,CAAC,aAAa,EACtB;gBACI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACvC,aAAA;AAED,YAAA,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AAChC,SAAA;;AAGA,QAAA,IAAY,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAY,CAAC,YAAY,GAAG,IAAI,CAAC;KACrC,CAAA;AAED;;;;;;;;;;AAUG;AACW,IAAA,UAAA,CAAA,OAAO,GAArB,UACI,IAA2C,EAC3C,QAA6C,EAC7C,YAAsB,EAAA;AAGtB,QAAA,IAAI,QAAQ,CAAC;QAEb,IAAI,IAAI,YAAY,cAAc,EAClC;YACI,QAAQ,GAAG,IAAI,CAAC;AACnB,SAAA;AAED,aAAA;AACI,YAAA,IAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAEtC,IAAI,CAAC,MAAM,EACX;AACI,gBAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACzD,aAAA;AAED,YAAA,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,IAAW,CAAC,CAAC;AACxC,SAAA;;QAGD,IAAI,QAAQ,YAAYA,YAAO,EAC/B;AACI,YAAA,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;AACzB,SAAA;QAED,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE9D,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAEvC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACW,UAAS,CAAA,SAAA,GAAvB,UAAwB,IAAY,EAAA;QAEhC,IAAM,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,EACT;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,0BAAwB,IAAI,GAAA,GAAG,CAAC,CAAC;AACpD,SAAA;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,QAAA,OAAO,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KACrC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACW,IAAA,UAAA,CAAA,IAAI,GAAlB,UAAmB,IAAY,EAAE,SAA2C,EAAE,OAA4B,EAAA;QAEtG,IAAI,CAAC,IAAI,EACT;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;AAChE,SAAA;AAEK,QAAA,IAAA,EAKgB,GAAA,MAAM,CAAC,MAAM,CAC/B,EAAE,EAAE,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,EALvC,KAAK,GAAA,EAAA,CAAA,KAAA,EACL,OAAO,GAAA,EAAA,CAAA,OAAA,EACP,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,aAAa,GAAA,EAAA,CAAA,aAC0B,CAAC;AAE5C,QAAA,IAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAA,IAAM,KAAK,GAAG,SAAS,YAAYC,cAAS,GAAG,SAAS,GAAG,IAAIA,cAAS,CAAC,SAAS,CAAC,CAAC;QACpF,IAAM,SAAS,GAAG,YAAY,CAAC;AAC/B,QAAA,IAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;AAEtC,QAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;YACf,IAAI,EAAE,KAAK,CAAC,UAAoB;YAChC,IAAI,EAAE,KAAK,CAAC,QAAkB;SACjC,CAAC;AACF,QAAA,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG;YACjB,UAAU,EAAE,KAAK,CAAC,QAAkB;SACvC,CAAC;QAEF,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;AAElB,QAAA,IAAI,MAAyB,CAAC;AAC9B,QAAA,IAAI,OAAiC,CAAC;AACtC,QAAA,IAAI,WAAwB,CAAC;QAC7B,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,IAAM,QAAQ,GAAc,EAAE,CAAC;AAE/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EACzC;YACI,IAAI,CAAC,MAAM,EACX;AACI,gBAAA,MAAM,GAAGC,iBAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;AACzC,gBAAA,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC;AAC5B,gBAAA,MAAM,CAAC,MAAM,GAAG,aAAa,CAAC;AAE9B,gBAAA,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAClC,WAAW,GAAG,IAAIC,gBAAW,CAAC,MAAM,EAAE,EAAE,UAAU,EAAA,UAAA,EAAE,CAAC,CAAC;gBAGtD,QAAQ,CAAC,IAAI,CAAC,IAAIH,YAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AAExC,gBAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACf,oBAAA,EAAE,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC;AACvB,oBAAA,IAAI,EAAE,EAAE;AACX,iBAAA,CAAC,CAAC;AACN,aAAA;;AAGD,YAAA,IAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC/B,YAAA,IAAM,OAAO,GAAGI,gBAAW,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACzE,YAAA,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAC5B,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;YAGzC,IAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC;;YAGpF,IAAI,SAAS,IAAI,aAAa,IAAI,MAAM,GAAG,UAAU,CAAC,EACtD;gBACI,IAAI,SAAS,KAAK,CAAC,EACnB;;AAEI,oBAAA,MAAM,IAAI,KAAK,CAAC,6BAAA,GAA8B,aAAa,GAAkB,kBAAA;AACvE,2BAAA,gBAAA,GAAiB,KAAK,CAAC,UAAU,GAAA,eAAA,GAAgB,KAAK,CAAC,QAAQ,GAAA,aAAA,GAAc,SAAS,GAAA,IAAI,CAAA,CAAC,CAAC;AACrG,iBAAA;AAED,gBAAA,EAAE,CAAC,CAAC;;gBAGJ,MAAM,GAAG,IAAI,CAAC;gBACd,OAAO,GAAG,IAAI,CAAC;gBACf,WAAW,GAAG,IAAI,CAAC;gBACnB,SAAS,GAAG,CAAC,CAAC;gBACd,SAAS,GAAG,CAAC,CAAC;gBACd,aAAa,GAAG,CAAC,CAAC;gBAElB,SAAS;AACZ,aAAA;AAED,YAAA,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;;YAGjF,IAAI,CAAC,iBAAiB,GAAG,UAAU,IAAI,SAAS,IAAI,SAAS,EAC7D;gBACI,IAAI,SAAS,KAAK,CAAC,EACnB;;AAEI,oBAAA,MAAM,IAAI,KAAK,CAAC,4BAAA,GAA6B,YAAY,GAAkB,kBAAA;AACrE,2BAAA,gBAAA,GAAiB,KAAK,CAAC,UAAU,GAAA,eAAA,GAAgB,KAAK,CAAC,QAAQ,GAAA,aAAA,GAAc,SAAS,GAAA,IAAI,CAAA,CAAC,CAAC;AACrG,iBAAA;AAED,gBAAA,EAAE,CAAC,CAAC;AACJ,gBAAA,SAAS,IAAI,aAAa,GAAG,UAAU,CAAC;AACxC,gBAAA,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACjC,SAAS,GAAG,CAAC,CAAC;gBACd,aAAa,GAAG,CAAC,CAAC;gBAElB,SAAS;AACZ,aAAA;AAED,YAAA,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;;YAG7E,IAAM,EAAE,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;AAGzC,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACf,gBAAA,EAAE,EAAA,EAAA;AACF,gBAAA,IAAI,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC;gBACzB,CAAC,EAAE,SAAS,GAAG,UAAU;gBACzB,CAAC,EAAE,SAAS,GAAG,UAAU;AACzB,gBAAA,KAAK,EAAE,iBAAiB;AACxB,gBAAA,MAAM,EAAA,MAAA;AACN,gBAAA,OAAO,EAAE,CAAC;AACV,gBAAA,OAAO,EAAE,CAAC;AACV,gBAAA,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;AACf,uBAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC;AACjD,uBAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;AACxD,aAAA,CAAC,CAAC;AAEH,YAAA,SAAS,IAAI,CAAC,iBAAiB,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,UAAU,CAAC;AAC9D,YAAA,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACpC,SAAA;QAED,IAAI,EAAC,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAA,EACzB;;;AAGI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EACpD;AACI,gBAAA,IAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAC5B;AACI,oBAAA,IAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC5B,IAAM,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;oBAC5C,IAAM,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;AAC7C,oBAAA,IAAM,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC;oBACxD,IAAM,MAAM,GAAG,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;AAEjC,oBAAA,IAAI,MAAM,EACV;AACI,wBAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;AAClB,4BAAA,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC;AAC7B,4BAAA,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC;AAC/B,4BAAA,MAAM,EAAA,MAAA;AACT,yBAAA,CAAC,CAAC;AACN,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;;QAGtD,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,SAAS,EAC5C;AACI,YAAA,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAA;AAED,QAAA,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAElC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AArdD;;;;;AAKG;AACoB,IAAA,UAAA,CAAA,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAE7D;;;;;AAKG;IACoB,UAAO,CAAA,OAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAE9C;;;AAGG;IACoB,UAAY,CAAA,YAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAEhF;;;;AAIG;IACoB,UAAK,CAAA,KAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAE5C;;;;;;;AAOG;AACoB,IAAA,UAAA,CAAA,cAAc,GAAuB;AACxD,QAAA,UAAU,EAAE,CAAC;AACb,QAAA,YAAY,EAAE,GAAG;AACjB,QAAA,aAAa,EAAE,GAAG;AAClB,QAAA,OAAO,EAAE,CAAC;QACV,KAAK,EAAE,UAAU,CAAC,YAAY;KACjC,CAAC;;IAGqB,UAAS,CAAA,SAAA,GAAqB,EAAE,CAAC;IAwa5D,OAAC,UAAA,CAAA;AAAA,CAxdD,EAwdC;;;;;;ACvfD;AACA,IAAM,+BAA+B,GAAmB,EAAE,CAAC;AAC3D,IAAM,4BAA4B,GAAmB,EAAE,CAAC;AACxD,IAAM,kBAAkB,GAAqB,EAAE,CAAC;AAEhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,IAAA,UAAA,kBAAA,UAAA,MAAA,EAAA;IAAgC,SAAS,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA;AA8GrC;;;;;;;;;;;AAWG;IACH,SAAY,UAAA,CAAA,IAAY,EAAE,KAAqC,EAAA;AAArC,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAqC,GAAA,EAAA,CAAA,EAAA;AAA/D,QAAA,IAAA,KAAA,GAEI,iBAAO,IA6BV,IAAA,CAAA;AA1DD;;;AAGG;QACO,KAAK,CAAA,KAAA,GAAG,QAAQ,CAAC;;AA4BjB,QAAA,IAAA,EAA+D,GAAA,MAAM,CAAC,MAAM,CAC9E,EAAE,EAAE,UAAU,CAAC,aAAa,EAAE,KAAK,CAAC,EADhC,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,QAAQ,cAAA,EAAE,aAAa,GAAA,EAAA,CAAA,aAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,QAAA,EAAE,QAAQ,cACxB,CAAC;AAEzC,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,EACnC;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,0BAAuB,QAAQ,GAAA,IAAG,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,KAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;AAC/B,QAAA,KAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACpB,QAAA,KAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACrB,QAAA,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,KAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACvB,QAAA,KAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,KAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,KAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,KAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AACxB,QAAA,KAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,KAAI,CAAC,OAAO,GAAG,IAAIC,oBAAe,CAAC,YAAA,EAAc,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,KAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF,QAAA,KAAI,CAAC,YAAY,GAAGH,iBAAQ,CAAC,YAAY,CAAC;AAC1C,QAAA,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,KAAI,CAAC,WAAW,GAAGA,iBAAQ,CAAC,UAAU,CAAC;AACvC,QAAA,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,QAAA,KAAI,CAAC,aAAa,GAAG,EAAE,CAAC;;KAC3B;;AAGM,IAAA,UAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,YAAA;;QAEI,IAAM,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAClD,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,QAAA,IAAM,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AACnC,QAAA,IAAM,GAAG,GAAG,IAAII,UAAK,EAAE,CAAC;QACxB,IAAM,KAAK,GAAqB,EAAE,CAAC;QACnC,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAM,UAAU,GAAG,EAAE,CAAC;AACtB,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AAC7D,QAAA,IAAM,UAAU,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACvD,QAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,KAAK,MAAM;AACtD,cAAE,+BAA+B,GAAG,4BAA4B,CAAC;QAErE,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,IAAI,GAAG,CAAC,CAAC;AACb,QAAA,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;QACtB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAC1C;AACI,YAAA,IAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3B,YAAA,IAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;YAEvC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,EACzB;gBACI,YAAY,GAAG,CAAC,CAAC;gBACjB,cAAc,GAAG,aAAa,CAAC;AAC/B,gBAAA,UAAU,EAAE,CAAC;AAChB,aAAA;AAED,YAAA,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAClC;AACI,gBAAA,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC/B,gBAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AACrD,gBAAA,EAAE,IAAI,CAAC;AACP,gBAAA,EAAE,aAAa,CAAC;AAEhB,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACV,gBAAA,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC;gBACzB,YAAY,GAAG,IAAI,CAAC;gBACpB,UAAU,GAAG,CAAC,CAAC;gBACf,SAAS;AACZ,aAAA;YAED,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEtC,IAAI,CAAC,QAAQ,EACb;gBACI,SAAS;AACZ,aAAA;YAED,IAAI,YAAY,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAClD;gBACI,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC3C,aAAA;AAED,YAAA,IAAM,cAAc,GAAmB,kBAAkB,CAAC,GAAG,EAAE,IAAI;gBAC/D,OAAO,EAAEN,YAAO,CAAC,KAAK;AACtB,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,QAAQ,EAAE,CAAC;AACX,gBAAA,UAAU,EAAE,CAAC;gBACb,QAAQ,EAAE,IAAIM,UAAK,EAAE;aACxB,CAAC;AAEF,YAAA,cAAc,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AAC1C,YAAA,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3B,YAAA,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACnC,cAAc,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AACjF,YAAA,cAAc,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrD,YAAA,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;AAEvC,YAAA,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAE3B,YAAA,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;AACnC,kBAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClF,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC;AACjD,YAAA,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtF,YAAY,GAAG,QAAQ,CAAC;AAExB,YAAA,IAAI,YAAY,KAAK,CAAC,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,EAC3D;AACI,gBAAA,EAAE,aAAa,CAAC;AAChB,gBAAAC,iBAAW,CAAC,KAAK,EAAE,CAAC,GAAG,YAAY,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;gBAC3E,CAAC,GAAG,YAAY,CAAC;gBACjB,YAAY,GAAG,CAAC,CAAC,CAAC;AAElB,gBAAA,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAChC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC3E,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACtD,gBAAA,IAAI,EAAE,CAAC;AAEP,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACV,gBAAA,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC;gBACzB,YAAY,GAAG,IAAI,CAAC;gBACpB,UAAU,GAAG,CAAC,CAAC;AAClB,aAAA;AACJ,SAAA;QAED,IAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAEnD,QAAA,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAC1C;YACI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,EAC7B;gBACI,aAAa,GAAG,cAAc,CAAC;AAClC,aAAA;AAED,YAAA,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC/B,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AACrD,YAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,SAAA;QAED,IAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,EAC9B;YACI,IAAI,WAAW,GAAG,CAAC,CAAC;AAEpB,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAC3B;AACI,gBAAA,WAAW,GAAG,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9C,aAAA;AACI,iBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EACjC;gBACI,WAAW,GAAG,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACpD,aAAA;AACI,iBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAClC;gBACI,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AACxF,aAAA;AAED,YAAA,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACtC,SAAA;AAED,QAAA,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;QAE9B,IAAM,aAAa,GAAiC,EAAE,CAAC;QAEvD,IAAM,gBAAgB,GAAmB,EAAE,CAAC;AAE5C,QAAA,IAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAEtD,QAAA,gBAAgB,CAAC,IAAI,CAAA,KAAA,CAArB,gBAAgB,EAAS,mBAAmB,CAAE,CAAA;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EACjC;YACI,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACjC,YAAA,IAAM,cAAc,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC;AAE/C,YAAA,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,EAClC;AACI,gBAAA,IAAI,YAAY,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC;gBAE1C,IAAI,CAAC,YAAY,EACjB;AACI,oBAAA,IAAM,QAAQ,GAAG,IAAIC,iBAAY,EAAE,CAAC;oBACpC,IAAI,QAAQ,SAAc,CAAC;oBAC3B,IAAI,aAAa,SAAa,CAAC;AAE/B,oBAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,MAAM,EACrC;wBACI,QAAQ,GAAG,IAAIC,iBAAY,CAACT,YAAO,CAAC,KAAK,CAAC,CAAC;AAC3C,wBAAA,aAAa,GAAGU,qBAAW,CAAC,MAAM,CAAC;AACtC,qBAAA;AAED,yBAAA;AACI,wBAAA,QAAQ,GAAG,IAAID,iBAAY,CAACT,YAAO,CAAC,KAAK,EACrC,EAAE,OAAO,EAAEW,YAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7E,wBAAA,aAAa,GAAGD,qBAAW,CAAC,UAAU,CAAC;AAC1C,qBAAA;oBAED,IAAME,MAAI,GAAG,IAAIC,SAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAE1C,oBAAAD,MAAI,CAAC,SAAS,GAAG,aAAa,CAAC;AAE/B,oBAAA,YAAY,GAAG;AACX,wBAAA,KAAK,EAAE,CAAC;AACR,wBAAA,UAAU,EAAE,CAAC;AACb,wBAAA,WAAW,EAAE,CAAC;AACd,wBAAA,QAAQ,EAAE,CAAC;AACX,wBAAA,KAAK,EAAE,CAAC;AACR,wBAAA,IAAI,EAAAA,MAAA;AACJ,wBAAA,QAAQ,EAAE,IAAI;AACd,wBAAA,GAAG,EAAE,IAAI;AACT,wBAAA,OAAO,EAAE,IAAI;qBAChB,CAAC;AACL,iBAAA;;AAGD,gBAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;AACvB,gBAAA,YAAY,CAAC,UAAU,GAAG,CAAC,CAAC;AAC5B,gBAAA,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC;AAC7B,gBAAA,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC1B,gBAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;;AAGf,gBAAA,IAAA,aAAa,GAAK,IAAI,CAAA,aAAT,CAAU;AAE/B,gBAAA,aAAa,CAAC,cAAc,CAAC,GAAG,aAAa,CAAC,cAAc,CAAC,IAAI,IAAIZ,YAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBAClG,YAAY,CAAC,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC;gBAE1D,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAEpC,gBAAA,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAEpC,gBAAA,aAAa,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;AAChD,aAAA;AAED,YAAA,aAAa,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;AACzC,SAAA;;;AAID,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EACnD;AACI,YAAA,IAAI,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAC3D;gBACI,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;;;AAID,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAChD;YACI,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,EAC5C;gBACI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3C,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,CAAC,oBAAoB,GAAG,gBAAgB,CAAC;AAE7C,QAAA,KAAK,IAAM,CAAC,IAAI,aAAa,EAC7B;AACI,YAAA,IAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AACtC,YAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;;;YAIjC,IAAI,EAAE,CAAA,CAAA,EAAA,GAAA,YAAY,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC,GAAG,KAAK,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAGa,SAAI,CAAC,cAAc,GAAG,CAAC,EACzG;AACI,gBAAA,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AACxD,gBAAA,YAAY,CAAC,GAAG,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;gBACnD,YAAY,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACrD,aAAA;AAED,iBAAA;AACI,gBAAA,IAAM,OAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AACjC,gBAAA,IAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;;AAGvC,gBAAA,KAAK,IAAI,GAAC,GAAG,OAAK,GAAG,CAAC,GAAG,CAAC,EAAE,GAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAC,EAAE,EACpD;AACI,oBAAA,QAAQ,CAAC,GAAC,CAAC,GAAG,CAAC,CAAC;AACnB,iBAAA;AACJ,aAAA;;;YAID,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;AACtC,SAAA;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EACjC;AACI,YAAA,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,YAAA,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;YAEjH,IAAI,IAAI,CAAC,YAAY,EACrB;AACI,gBAAA,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC/B,aAAA;AAED,YAAA,IAAM,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;YAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;AACrC,YAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAE7B,IAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAExD,YAAA,IAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;AACnC,YAAA,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;AAEhC,YAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;AAE/B,YAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACpD,YAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACpD,YAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACpD,YAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACpD,YAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACpD,YAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AAEpD,YAAA,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AAC1C,YAAA,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YAE1C,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AACzE,YAAA,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YAE1C,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;YACzE,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AAE1E,YAAA,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YAC1C,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AAE1E,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAC9C,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAE9C,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAC9C,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAE9C,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAC9C,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAE9C,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AAC9C,YAAA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;AACjD,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,GAAG,KAAK,CAAC;AACvC,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC;AAErD,QAAA,KAAK,IAAM,CAAC,IAAI,aAAa,EAC7B;AACI,YAAA,IAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;;AAGtC,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,EAC9C;gBACI,IAAI,WAAW,GAAG,CAAC,CAAC;gBAEpB,IAAM,aAAa,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtD,IAAM,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAEvD,gBAAA,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,YAAY,CAAC,KAAK,EAAE,GAAC,EAAE,EAC3C;oBACI,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;oBACtD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;oBAEtD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;oBACtD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;oBAEtD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;oBACtD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;oBAEtD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;oBACtD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC;AACzD,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa,GAAG,KAAK,CAAC;AAE5C,YAAA,IAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AAC7E,YAAA,IAAM,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC5E,IAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAE1D,YAAA,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC;AAC1C,YAAA,aAAa,CAAC,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC;AACtC,YAAA,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC,OAAO,CAAC;YAExC,YAAY,CAAC,MAAM,EAAE,CAAC;YACtB,aAAa,CAAC,MAAM,EAAE,CAAC;YACvB,WAAW,CAAC,MAAM,EAAE,CAAC;AACxB,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;YACI,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;QAEI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACnC,CAAA;IAED,UAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,QAAkB,EAAA;QAEtB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,EACpE;AACI,YAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,SAAA;;AAGK,QAAA,IAAA,KAAkD,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAApF,kBAAkB,wBAAA,EAAE,iBAAiB,uBAAA,EAAE,IAAI,UAAyC,CAAC;QAE7F,IAAI,iBAAiB,KAAK,MAAM,EAChC;;AAEU,YAAA,IAAA,KAAiB,IAAI,CAAC,cAAc,EAAlC,CAAC,GAAA,EAAA,CAAA,CAAA,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,EAAE,CAAC,GAAA,EAAA,CAAA,CAAA,EAAE,CAAC,OAAwB,CAAC;AAE3C,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,YAAA,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,YAAA,IAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAErD,YAAA,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YAEvC,KAAmB,IAAA,EAAA,GAAA,CAAyB,EAAzB,EAAA,GAAA,IAAI,CAAC,oBAAoB,EAAzB,EAAA,GAAA,EAAA,CAAA,MAAyB,EAAzB,EAAA,EAAyB,EAC5C;AADK,gBAAA,IAAM,IAAI,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AAEX,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,GAAG,kBAAkB,GAAG,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;AACtG,aAAA;AACJ,SAAA;AAED,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;KAC3B,CAAA;AAED;;;AAGG;AACI,IAAA,UAAA,CAAA,SAAA,CAAA,cAAc,GAArB,YAAA;QAEI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,MAAA,CAAA,SAAA,CAAM,cAAc,CAAA,IAAA,CAAA,IAAA,CAAE,CAAC;KACjC,CAAA;AAED;;;AAGG;AACO,IAAA,UAAA,CAAA,SAAA,CAAA,QAAQ,GAAlB,YAAA;QAEI,IAAM,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,CAAC,IAAI,EACT;YACI,MAAM,IAAI,KAAK,CAAC,uBAAA,GAAuB,IAAI,CAAC,SAAS,GAAG,IAAA,CAAC,CAAC;AAC7D,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EACvB;AACI,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EACd;YACI,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAJf;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AAED,QAAA,GAAA,EAAA,UAAgB,KAAa,EAAA;AAEzB,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK;kBAAE,OAAO,EAAA;AAEjC,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAEnB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EACzD;gBACI,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAClD,aAAA;SACJ;;;AAZA,KAAA,CAAA,CAAA;AAmBD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AALhB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,MAAM,CAAC;SACtB;AAED,QAAA,GAAA,EAAA,UAAiB,KAAqB,EAAA;AAElC,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EACzB;AACI,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;;AAAnB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;AAED,QAAA,GAAA,EAAA,UAAoB,KAAa,EAAA;AAE7B,YAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,EAChC;AACI,gBAAA,MAAM,IAAI,KAAK,CAAC,0BAAuB,KAAK,GAAA,IAAG,CAAC,CAAC;AACpD,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAC5B;AACI,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,aAAA;SACJ;;;AAdA,KAAA,CAAA,CAAA;AAiBD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;;AAAnB,QAAA,GAAA,EAAA,YAAA;;AAEI,YAAA,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,mCAAI,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;SACtE;AAED,QAAA,GAAA,EAAA,UAAoB,KAAyB,EAAA;AAEzC,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAC5B;AACI,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;AAoBD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AATjB;;;;;;;;AAQG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;AAED,QAAA,GAAA,EAAA,UAAkB,KAAsB,EAAA;AAEpC,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAC7B;AACI,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChC,aAAA;SACJ;;;AAZA,KAAA,CAAA,CAAA;AAeD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAI,CAAA,SAAA,EAAA,MAAA,EAAA;;AAAf,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AAED,QAAA,GAAA,EAAA,UAAgB,IAAY,EAAA;AAExB,YAAA,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAE/D,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EACvB;gBACI,OAAO;AACV,aAAA;AACD,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;;;AAZA,KAAA,CAAA,CAAA;AAmBD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AALnB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;AAED,QAAA,GAAA,EAAA,UAAoB,KAAa,EAAA;AAE7B,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAC5B;gBACI,OAAO;AACV,aAAA;AACD,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;;;AAVA,KAAA,CAAA,CAAA;AAiBD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAa,CAAA,SAAA,EAAA,eAAA,EAAA;AALxB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEhB,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;;;AAAA,KAAA,CAAA,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AALpB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEhB,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAa,CAAA,SAAA,EAAA,eAAA,EAAA;;AAAxB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;AAED,QAAA,GAAA,EAAA,UAAyB,KAAa,EAAA;AAElC,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EACjC;AACI,gBAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;AAkBD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAPtB;;;;;;AAMG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;AAED,QAAA,GAAA,EAAA,UAAuB,KAAc,EAAA;AAEjC,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAC/B;AACI,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;AAgBD,IAAA,MAAA,CAAA,cAAA,CAAW,UAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AALrB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEhB,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;;;AAAA,KAAA,CAAA,CAAA;AAQD,IAAA,MAAA,CAAA,cAAA,CAAI,UAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AANd;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AAED,QAAA,GAAA,EAAA,UAAe,KAAa,EAAA;AAExB,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAE7B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAC9B;gBACI,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;;;AAbA,KAAA,CAAA,CAAA;IAeD,UAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,OAAmC,EAAA;AAE/B,QAAA,IAAA,aAAa,GAAK,IAAI,CAAA,aAAT,CAAU;QAC/B,IAAM,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAClD,QAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,KAAK,MAAM;AACtD,cAAE,+BAA+B,GAAG,4BAA4B,CAAC;QAErE,gBAAgB,CAAC,IAAI,CAArB,KAAA,CAAA,gBAAgB,EAAS,IAAI,CAAC,oBAAoB,CAAE,CAAA;QACpD,KAA2B,IAAA,EAAA,GAAA,CAAyB,EAAzB,EAAA,GAAA,IAAI,CAAC,oBAAoB,EAAzB,EAAA,GAAA,EAAA,CAAA,MAAyB,EAAzB,EAAA,EAAyB,EACpD;AADK,YAAA,IAAM,YAAY,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AAEnB,YAAA,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACvC,SAAA;AACD,QAAA,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;;QAG/B,gBAAgB;AACX,aAAA,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,EAAA,CAAC;aAClE,OAAO,CAAC,UAAC,IAAI,EAAA;YAEV,IAAI,CAAC,IAAI,CAAC,OAAO,GAAGb,YAAO,CAAC,KAAK,CAAC;AACtC,SAAC,CAAC,CAAC;AAEP,QAAA,KAAK,IAAM,EAAE,IAAI,aAAa,EAC9B;AACI,YAAA,IAAM,OAAO,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;YAElC,OAAO,CAAC,OAAO,EAAE,CAAC;AAClB,YAAA,OAAO,aAAa,CAAC,EAAE,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAE1B,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;KAC1B,CAAA;AA12Ba,IAAA,UAAA,CAAA,aAAa,GAA8B;AACrD,QAAA,KAAK,EAAE,MAAM;AACb,QAAA,IAAI,EAAE,QAAQ;AACd,QAAA,QAAQ,EAAE,CAAC;AACX,QAAA,aAAa,EAAE,CAAC;KACnB,CAAC;IAs2BN,OAAC,UAAA,CAAA;CAAA,CA72B+Bc,iBAAS,CA62BxC;;AC76BD;;;;AAIG;AACH,IAAA,gBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,gBAAA,GAAA;KAgKC;AA3JG;;;AAGG;AACW,IAAA,gBAAA,CAAA,GAAG,GAAjB,YAAA;QAEIC,sBAAc,CAAC,mBAAmB,CAAC,KAAK,EAAEA,sBAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KACpF,CAAA;AAED;;;;;;AAMG;AACI,IAAA,gBAAA,CAAA,GAAG,GAAV,UAAyB,QAAwB,EAAE,IAA8B,EAAA;QAE7E,IAAM,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;QAG/C,IAAI,CAAC,MAAM,EACX;AACI,YAAA,IAAI,EAAE,CAAC;YAEP,OAAO;AACV,SAAA;QAED,IAAM,OAAO,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC5D,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACzC,IAAM,QAAQ,GAAkB,EAAE,CAAC;;;QAInC,IAAM,SAAS,GAAG,UAAC,IAAoB,EAAA;YAEnC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAEhD,YAAA,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EACrD;AACI,gBAAA,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC/D,gBAAA,IAAI,EAAE,CAAC;AACV,aAAA;AACL,SAAC,CAAC;AAEF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EACzC;YACI,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACnC,YAAA,IAAM,GAAG,GAAG,OAAO,GAAG,QAAQ,CAAC;YAC/B,IAAI,MAAM,GAAG,KAAK,CAAC;;;AAInB,YAAA,KAAK,IAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EACjC;gBACI,IAAM,cAAc,GAAmB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAE5D,gBAAA,IAAI,cAAc,CAAC,GAAG,KAAK,GAAG,EAC9B;AACI,oBAAA,cAAc,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;oBAC5C,IAAI,cAAc,CAAC,OAAO,EAC1B;wBACI,SAAS,CAAC,cAAc,CAAC,CAAC;AAC7B,qBAAA;AAED,yBAAA;AACI,wBAAA,cAAc,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACnD,qBAAA;oBACD,MAAM,GAAG,IAAI,CAAC;oBACd,MAAM;AACT,iBAAA;AACJ,aAAA;;;YAID,IAAI,CAAC,MAAM,EACX;;AAEI,gBAAA,IAAM,OAAO,GAAG;oBACZ,WAAW,EAAE,QAAQ,CAAC,WAAW;AACjC,oBAAA,QAAQ,EAAEA,sBAAc,CAAC,SAAS,CAAC,KAAK;AACxC,oBAAA,QAAQ,EAAE,MAAM,CAAC,MAAM,CACnB,EAAE,QAAQ,EAAA,QAAA,EAAE,EACZ,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAClC;AACD,oBAAA,cAAc,EAAE,QAAQ;iBAC3B,CAAC;gBAEF,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AACrC,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACY,IAAA,gBAAA,CAAA,UAAU,GAAzB,UAA0B,MAAc,EAAE,QAAwB,EAAA;QAE9D,IAAI,MAAM,GAAG,CAAC,QAAQ,CAAC,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAE/E,IAAI,QAAQ,CAAC,SAAS,EACtB;YACI,IAAI,MAAM,KAAK,GAAG,EAClB;gBACI,MAAM,GAAG,EAAE,CAAC;AACf,aAAA;AAED,YAAA,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,EAC5B;;AAEI,gBAAA,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAC5D;oBACI,MAAM,IAAI,GAAG,CAAC;AACjB,iBAAA;AACJ,aAAA;AACJ,SAAA;;QAGD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;;AAG5C,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EACtD;YACI,MAAM,IAAI,GAAG,CAAC;AACjB,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;AAGG;IACY,gBAAO,CAAA,OAAA,GAAtB,UAAuB,GAAW,EAAA;QAE9B,IAAM,GAAG,GAAG,GAAG;AACV,aAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACnB,aAAA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAClB,aAAA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;;QAG9B,IAAI,GAAG,KAAK,GAAG,EACf;AACI,YAAA,OAAO,GAAG,CAAC;AACd,SAAA;;aAEI,IAAI,GAAG,KAAK,EAAE,EACnB;AACI,YAAA,OAAO,GAAG,CAAC;AACd,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;;AA5JM,IAAA,gBAAA,CAAA,SAAS,GAAsBC,kBAAa,CAAC,MAAM,CAAC;IA6J/D,OAAC,gBAAA,CAAA;AAAA,CAhKD,EAgKC;;;;;;;;;;;"}