<template>
  <div class="simple-safe-image">
    <img 
      v-if="!showError && imageUrl"
      :src="imageUrl" 
      :alt="alt"
      :class="imgClass"
      :style="computedStyle"
      @load="handleLoad"
      @error="handleError"
      loading="lazy"
    />
    <div v-else class="image-placeholder">
      <i class="fas fa-image"></i>
      <span>图片加载失败</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';

interface Props {
  src: string;
  alt?: string;
  imgClass?: string;
  imgStyle?: any;
  maxWidth?: number;
  maxHeight?: number;
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  imgClass: '',
  imgStyle: undefined,
  maxWidth: 400,
  maxHeight: 300
});

const emit = defineEmits<{
  load: [event: Event];
  error: [event: Event];
}>();

const imageUrl = ref<string>('');
const showError = ref(false);

// 🚀 计算样式 - 限制图片大小防止内存爆炸
const computedStyle = computed(() => {
  const baseStyle = {
    maxWidth: `${props.maxWidth}px`,
    maxHeight: `${props.maxHeight}px`,
    objectFit: 'cover' as const,
    display: 'block',
    ...props.imgStyle
  };
  
  return baseStyle;
});

// 🚀 简化的URL处理 - 只处理必要的格式
const processImageUrl = (src: string): string => {
  if (!src) return '';
  
  // 直接返回HTTP URL
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }
  
  // 直接返回相对路径
  if (src.startsWith('/')) {
    return src;
  }
  
  // 只允许小于100KB的Base64图片
  if (src.startsWith('data:image/')) {
    if (src.length > 100 * 1024) { // 100KB限制
      console.warn('🚫 SimpleSafeImage: Base64图片过大，拒绝加载:', src.length);
      return '';
    }
    return src;
  }
  
  // 其他格式都拒绝
  console.warn('🚫 SimpleSafeImage: 不支持的图片格式:', src.substring(0, 50));
  return '';
};

// 🚀 处理图片加载
const handleLoad = (event: Event) => {
  const img = event.target as HTMLImageElement;
  console.log('✅ SimpleSafeImage加载成功:', {
    url: imageUrl.value.substring(0, 50) + '...',
    size: `${img.naturalWidth}x${img.naturalHeight}`
  });
  emit('load', event);
};

const handleError = (event: Event) => {
  console.warn('❌ SimpleSafeImage加载失败:', imageUrl.value.substring(0, 50) + '...');
  showError.value = true;
  emit('error', event);
};

// 监听src变化
watch(() => props.src, (newSrc) => {
  showError.value = false;
  imageUrl.value = processImageUrl(newSrc);
}, { immediate: true });

onMounted(() => {
  imageUrl.value = processImageUrl(props.src);
});
</script>

<style scoped>
.simple-safe-image {
  display: inline-block;
  width: 100%;
  height: 100%;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 4px;
  color: #666;
  min-height: 100px;
}

.image-placeholder i {
  font-size: 2rem;
  margin-bottom: 8px;
}

.image-placeholder span {
  font-size: 0.9rem;
}
</style>