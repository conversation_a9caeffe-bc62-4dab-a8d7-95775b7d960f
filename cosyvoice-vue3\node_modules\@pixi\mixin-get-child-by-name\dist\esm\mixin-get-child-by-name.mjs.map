{"version": 3, "file": "mixin-get-child-by-name.mjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import { DisplayObject, Container } from '@pixi/display';\n\n/**\n * The instance name of the object.\n * @memberof PIXI.DisplayObject#\n * @member {string} name\n */\nDisplayObject.prototype.name = null;\n\n/**\n * Returns the display object in the container.\n *\n * Recursive searches are done in a preorder traversal.\n * @method getChildByName\n * @memberof PIXI.Container#\n * @param {string} name - Instance name.\n * @param {boolean}[deep=false] - Whether to search recursively\n * @returns {PIXI.DisplayObject} The child with the specified name.\n */\nContainer.prototype.getChildByName = function getChildByName<T extends DisplayObject = DisplayObject>(\n    name: string,\n    deep?: boolean,\n): T\n{\n    for (let i = 0, j = this.children.length; i < j; i++)\n    {\n        if (this.children[i].name === name)\n        {\n            return this.children[i];\n        }\n    }\n\n    if (deep)\n    {\n        for (let i = 0, j = this.children.length; i < j; i++)\n        {\n            const child = (this.children[i] as Container);\n\n            if (!child.getChildByName)\n            {\n                continue;\n            }\n\n            const target = child.getChildByName<T>(name, true);\n\n            if (target)\n            {\n                return target;\n            }\n        }\n    }\n\n    return null;\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;;;;AAIG;AACH,aAAa,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;AAEpC;;;;;;;;;AASG;AACH,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,cAAc,CACxD,IAAY,EACZ,IAAc,EAAA;AAGd,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACpD;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAClC;AACI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3B,SAAA;AACJ,KAAA;AAED,IAAA,IAAI,IAAI,EACR;AACI,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACpD;YACI,IAAM,KAAK,GAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAe,CAAC;AAE9C,YAAA,IAAI,CAAC,KAAK,CAAC,cAAc,EACzB;gBACI,SAAS;AACZ,aAAA;YAED,IAAM,MAAM,GAAG,KAAK,CAAC,cAAc,CAAI,IAAI,EAAE,IAAI,CAAC,CAAC;AAEnD,YAAA,IAAI,MAAM,EACV;AACI,gBAAA,OAAO,MAAM,CAAC;AACjB,aAAA;AACJ,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC"}