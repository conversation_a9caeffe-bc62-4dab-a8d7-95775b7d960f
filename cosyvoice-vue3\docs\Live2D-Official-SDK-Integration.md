# Live2D 官方SDK集成指南

## 📋 概述

本指南说明如何在CosyVoice Vue3项目中集成官方的Cubism SDK for Web 5-r.4。

## 🎯 已完成的设置

1. ✅ Core文件复制到 `public/live2d-sdk/Core/`
2. ✅ Framework源码复制到 `public/live2d-sdk/Framework/`
3. ✅ 示例模型复制到 `public/live2d-sdk/Models/`
4. ✅ 创建HTML测试页面 `public/LD.html` (简化地址)
5. ✅ 添加到测试导航页面 (`/#/nav`)

## 🚀 快速开始

### 1. 访问测试页面

**简化地址**: `http://localhost:5173/LD.html` 或 `http://localhost:5173/LD`

**从导航访问**: 
1. 访问 `http://localhost:5173/#/nav`
2. 在"Live2D模型测试"分类中点击"Live2D官方SDK测试"

### 2. 功能特性

- ✅ 官方Cubism SDK Core加载检测
- ✅ WebGL兼容性检测  
- ✅ 模型文件存在性验证
- ✅ 基础WebGL渲染测试
- ✅ 7个示例模型选择 (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ko)
- ✅ 实时状态日志
- ✅ 返回测试导航的快捷链接

### 3. 在HTML中直接使用

```html
<!-- 引入Core -->
<script src="./live2d-sdk/Core/live2dcubismcore.js"></script>

<script>
// 检查SDK状态
if (typeof Live2DCubismCore !== 'undefined') {
  console.log('✅ Live2D Core已加载');
}
</script>
```

## 📦 目录结构

```
public/
├── live2d-sdk/
│   ├── Core/                   # Core库文件
│   │   ├── live2dcubismcore.js
│   │   ├── live2dcubismcore.min.js
│   │   └── live2dcubismcore.d.ts
│   ├── Framework/              # Framework源码
│   │   ├── src/
│   │   ├── package.json
│   │   └── tsconfig.json
│   └── Models/                 # 示例模型
│       ├── Haru/
│       ├── Hiyori/
│       ├── Mark/
│       ├── Natori/
│       ├── Rice/
│       ├── Mao/
│       └── Wanko/
└── LD.html                     # 简化测试页面
```

## 🎮 使用说明

### 基础测试流程

1. **访问页面**: 打开 `http://localhost:5173/LD.html`
2. **检查SDK**: 点击"检查SDK状态"验证环境
3. **选择模型**: 在下拉框中选择要测试的模型
4. **加载模型**: 点击"加载模型"开始测试
5. **查看结果**: 观察状态日志和画布显示

### 功能按钮说明

- **检查SDK状态**: 验证Live2D Core和WebGL是否正常
- **加载模型**: 加载选中的模型并验证文件
- **测试动画**: 测试模型动画功能 (需Framework)
- **清空画布**: 清空画布内容

## ⚙️ Framework编译 (可选)

要使用完整功能，需要编译Framework:

```bash
cd public/live2d-sdk/Framework
npm install
npm run build
```

## 🐛 故障排除

### 1. SDK未加载
- 检查 `live2dcubismcore.js` 文件路径
- 确认浏览器控制台无错误

### 2. 模型加载失败  
- 验证模型文件是否存在
- 检查网络请求状态
- 确认服务器运行正常

### 3. WebGL问题
- 检查浏览器WebGL支持
- 启用硬件加速
- 更新显卡驱动

### 4. 画布无显示
- 检查Canvas元素尺寸
- 验证WebGL上下文创建
- 查看浏览器控制台错误

## 📋 当前限制

- ❌ Framework未完全集成 (需编译)
- ❌ 无法加载实际.moc文件
- ❌ 无动画和表情控制
- ✅ Core SDK功能验证
- ✅ WebGL基础渲染
- ✅ 文件存在性检查

---

*文档更新时间：2025年1月22日*