{"name": "@pixi/accessibility", "version": "6.5.10", "main": "dist/cjs/accessibility.js", "module": "dist/esm/accessibility.mjs", "bundle": "dist/browser/accessibility.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/accessibility.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/accessibility.js"}}}, "description": "Accessibility Plugin for visually impaired users", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/core": "6.5.10", "@pixi/display": "6.5.10", "@pixi/utils": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}