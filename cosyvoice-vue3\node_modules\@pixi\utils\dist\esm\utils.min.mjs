/*!
 * @pixi/utils - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/utils is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{settings as e}from"@pixi/settings";export{isMobile}from"@pixi/settings";export{default as EventEmitter}from"eventemitter3";export{default as earcut}from"earcut";import{parse as t,format as r,resolve as n}from"url";import{BLEND_MODES as a}from"@pixi/constants";var i={parse:t,format:r,resolve:n};function o(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function f(e){return e.split("?")[0].split("#")[0]}var l={toPosix:function(e){return t="\\",r="/",e.replace(new RegExp(t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g"),r);var t,r},isUrl:function(e){return/^https?:/.test(this.toPosix(e))},isDataUrl:function(e){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(e)},hasProtocol:function(e){return/^[^/:]+:\//.test(this.toPosix(e))},getProtocol:function(e){o(e),e=this.toPosix(e);var t="",r=/^file:\/\/\//.exec(e),n=/^[^/:]+:\/\//.exec(e),a=/^[^/:]+:\//.exec(e);if(r||n||a){var i=(null==r?void 0:r[0])||(null==n?void 0:n[0])||(null==a?void 0:a[0]);t=i,e=e.slice(i.length)}return t},toAbsolute:function(t,r,n){if(this.isDataUrl(t))return t;var a=f(this.toPosix(null!=r?r:e.ADAPTER.getBaseUrl())),i=f(this.toPosix(null!=n?n:this.rootname(a)));return o(t),(t=this.toPosix(t)).startsWith("/")?l.join(i,t.slice(1)):this.isAbsolute(t)?t:this.join(a,t)},normalize:function(e){if(o(e=this.toPosix(e)),0===e.length)return".";var t="",r=e.startsWith("/");this.hasProtocol(e)&&(t=this.rootname(e),e=e.slice(t.length));var n=e.endsWith("/");return(e=function(e,t){for(var r,n="",a=0,i=-1,o=0,f=0;f<=e.length;++f){if(f<e.length)r=e.charCodeAt(f);else{if(47===r)break;r=47}if(47===r){if(i===f-1||1===o);else if(i!==f-1&&2===o){if(n.length<2||2!==a||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",a=0):a=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),i=f,o=0;continue}}else if(2===n.length||1===n.length){n="",a=0,i=f,o=0;continue}t&&(n.length>0?n+="/..":n="..",a=2)}else n.length>0?n+="/"+e.slice(i+1,f):n=e.slice(i+1,f),a=f-i-1;i=f,o=0}else 46===r&&-1!==o?++o:o=-1}return n}(e,!1)).length>0&&n&&(e+="/"),r?"/"+e:t+e},isAbsolute:function(e){return o(e),e=this.toPosix(e),!!this.hasProtocol(e)||e.startsWith("/")},join:function(){for(var e,t,r=arguments,n=[],a=0;a<arguments.length;a++)n[a]=r[a];if(0===n.length)return".";for(var i=0;i<n.length;++i){var f=n[i];if(o(f),f.length>0)if(void 0===t)t=f;else{var l=null!==(e=n[i-1])&&void 0!==e?e:"";this.extname(l)?t+="/../"+f:t+="/"+f}}return void 0===t?".":this.normalize(t)},dirname:function(e){if(o(e),0===e.length)return".";for(var t=(e=this.toPosix(e)).charCodeAt(0),r=47===t,n=-1,a=!0,i=this.getProtocol(e),f=e,l=(e=e.slice(i.length)).length-1;l>=1;--l)if(47===(t=e.charCodeAt(l))){if(!a){n=l;break}}else a=!1;return-1===n?r?"/":this.isUrl(f)?i+e:i:r&&1===n?"//":i+e.slice(0,n)},rootname:function(e){o(e);var t="";if(t=(e=this.toPosix(e)).startsWith("/")?"/":this.getProtocol(e),this.isUrl(e)){var r=e.indexOf("/",t.length);(t=-1!==r?e.slice(0,r):e).endsWith("/")||(t+="/")}return t},basename:function(e,t){o(e),t&&o(t),e=this.toPosix(e);var r,n=0,a=-1,i=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var f=t.length-1,l=-1;for(r=e.length-1;r>=0;--r){var s=e.charCodeAt(r);if(47===s){if(!i){n=r+1;break}}else-1===l&&(i=!1,l=r+1),f>=0&&(s===t.charCodeAt(f)?-1==--f&&(a=r):(f=-1,a=l))}return n===a?a=l:-1===a&&(a=e.length),e.slice(n,a)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!i){n=r+1;break}}else-1===a&&(i=!1,a=r+1);return-1===a?"":e.slice(n,a)},extname:function(e){o(e);for(var t=-1,r=0,n=-1,a=!0,i=0,f=(e=this.toPosix(e)).length-1;f>=0;--f){var l=e.charCodeAt(f);if(47!==l)-1===n&&(a=!1,n=f+1),46===l?-1===t?t=f:1!==i&&(i=1):-1!==t&&(i=-1);else if(!a){r=f+1;break}}return-1===t||-1===n||0===i||1===i&&t===n-1&&t===r+1?"":e.slice(t,n)},parse:function(e){o(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var r,n=(e=this.toPosix(e)).charCodeAt(0),a=this.isAbsolute(e);t.root=this.rootname(e),r=a||this.hasProtocol(e)?1:0;for(var i=-1,f=0,l=-1,s=!0,c=e.length-1,d=0;c>=r;--c)if(47!==(n=e.charCodeAt(c)))-1===l&&(s=!1,l=c+1),46===n?-1===i?i=c:1!==d&&(d=1):-1!==i&&(d=-1);else if(!s){f=c+1;break}return-1===i||-1===l||0===d||1===d&&i===l-1&&i===f+1?-1!==l&&(t.base=t.name=0===f&&a?e.slice(1,l):e.slice(f,l)):(0===f&&a?(t.name=e.slice(1,i),t.base=e.slice(1,l)):(t.name=e.slice(f,i),t.base=e.slice(f,l)),t.ext=e.slice(i,l)),t.dir=this.dirname(e),t},sep:"/",delimiter:":"};e.RETINA_PREFIX=/@([0-9\.]+)x/,e.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT=!1;var s,c=!1;function d(){c=!0}function u(t){var r;if(!c){if(e.ADAPTER.getNavigator().userAgent.toLowerCase().indexOf("chrome")>-1){var n=["\n %c %c %c PixiJS 6.5.10 - ✰ "+t+" ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \n\n","background: #ff66a5; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff66a5; background: #030307; padding:5px 0;","background: #ff66a5; padding:5px 0;","background: #ffc3dc; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;"];(r=globalThis.console).log.apply(r,n)}else globalThis.console&&globalThis.console.log("PixiJS 6.5.10 - "+t+" - http://www.pixijs.com/");c=!0}}function h(){return void 0===s&&(s=function(){var t={stencil:!0,failIfMajorPerformanceCaveat:e.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT};try{if(!e.ADAPTER.getWebGLRenderingContext())return!1;var r=e.ADAPTER.createCanvas(),n=r.getContext("webgl",t)||r.getContext("experimental-webgl",t),a=!(!n||!n.getContextAttributes().stencil);if(n){var i=n.getExtension("WEBGL_lose_context");i&&i.loseContext()}return n=null,a}catch(e){return!1}}()),s}var g={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function b(e,t){return void 0===t&&(t=[]),t[0]=(e>>16&255)/255,t[1]=(e>>8&255)/255,t[2]=(255&e)/255,t}function p(e){var t=e.toString(16);return"#"+(t="000000".substring(0,6-t.length)+t)}function v(e){return"string"==typeof e&&"#"===(e=g[e.toLowerCase()]||e)[0]&&(e=e.slice(1)),parseInt(e,16)}function m(e){return(255*e[0]<<16)+(255*e[1]<<8)+(255*e[2]|0)}var y=function(){for(var e=[],t=[],r=0;r<32;r++)e[r]=r,t[r]=r;e[a.NORMAL_NPM]=a.NORMAL,e[a.ADD_NPM]=a.ADD,e[a.SCREEN_NPM]=a.SCREEN,t[a.NORMAL]=a.NORMAL_NPM,t[a.ADD]=a.ADD_NPM,t[a.SCREEN]=a.SCREEN_NPM;var n=[];return n.push(t),n.push(e),n}();function A(e,t){return y[t?1:0][e]}function x(e,t,r,n){return r=r||new Float32Array(4),n||void 0===n?(r[0]=e[0]*t,r[1]=e[1]*t,r[2]=e[2]*t):(r[0]=e[0],r[1]=e[1],r[2]=e[2]),r[3]=t,r}function w(e,t){if(1===t)return(255*t<<24)+e;if(0===t)return 0;var r=e>>16&255,n=e>>8&255,a=255&e;return(255*t<<24)+((r=r*t+.5|0)<<16)+((n=n*t+.5|0)<<8)+(a=a*t+.5|0)}function k(e,t,r,n){return(r=r||new Float32Array(4))[0]=(e>>16&255)/255,r[1]=(e>>8&255)/255,r[2]=(255&e)/255,(n||void 0===n)&&(r[0]*=t,r[1]*=t,r[2]*=t),r[3]=t,r}function E(e,t){void 0===t&&(t=null);var r=6*e;if((t=t||new Uint16Array(r)).length!==r)throw new Error("Out buffer length is incorrect, got "+t.length+" and expected "+r);for(var n=0,a=0;n<r;n+=6,a+=4)t[n+0]=a+0,t[n+1]=a+1,t[n+2]=a+2,t[n+3]=a+0,t[n+4]=a+2,t[n+5]=a+3;return t}function P(e){if(4===e.BYTES_PER_ELEMENT)return e instanceof Float32Array?"Float32Array":e instanceof Uint32Array?"Uint32Array":"Int32Array";if(2===e.BYTES_PER_ELEMENT){if(e instanceof Uint16Array)return"Uint16Array"}else if(1===e.BYTES_PER_ELEMENT&&e instanceof Uint8Array)return"Uint8Array";return null}var C={Float32Array:Float32Array,Uint32Array:Uint32Array,Int32Array:Int32Array,Uint8Array:Uint8Array};function R(e,t){for(var r=0,n=0,a={},i=0;i<e.length;i++)n+=t[i],r+=e[i].length;var o=new ArrayBuffer(4*r),f=null,l=0;for(i=0;i<e.length;i++){var s=t[i],c=e[i],d=P(c);a[d]||(a[d]=new C[d](o)),f=a[d];for(var u=0;u<c.length;u++){f[(u/s|0)*n+l+u%s]=c[u]}l+=s}return new Float32Array(o)}function _(e){return e+=0===e?1:0,--e,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,(e|=e>>>16)+1}function T(e){return!(e&e-1||!e)}function N(e){var t=(e>65535?1:0)<<4,r=((e>>>=t)>255?1:0)<<3;return t|=r,t|=r=((e>>>=r)>15?1:0)<<2,(t|=r=((e>>>=r)>3?1:0)<<1)|(e>>>=r)>>1}function D(e,t,r){var n,a=e.length;if(!(t>=a||0===r)){var i=a-(r=t+r>a?a-t:r);for(n=t;n<i;++n)e[n]=e[n+r];e.length=i}}function M(e){return 0===e?0:e<0?-1:1}var O=0;function I(){return++O}var L={};function U(e,t,r){if(void 0===r&&(r=3),!L[t]){var n=(new Error).stack;void 0===n?console.warn("PixiJS Deprecation Warning: ",t+"\nDeprecated since v"+e):(n=n.split("\n").splice(r).join("\n"),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",t+"\nDeprecated since v"+e),console.warn(n),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",t+"\nDeprecated since v"+e),console.warn(n))),L[t]=!0}}var F={},S=Object.create(null),j=Object.create(null);function W(){var e;for(e in S)S[e].destroy();for(e in j)j[e].destroy()}function q(){var e;for(e in S)delete S[e];for(e in j)delete j[e]}var z=function(){function t(t,r,n){this.canvas=e.ADAPTER.createCanvas(),this.context=this.canvas.getContext("2d"),this.resolution=n||e.RESOLUTION,this.resize(t,r)}return t.prototype.clear=function(){this.context.setTransform(1,0,0,1,0,0),this.context.clearRect(0,0,this.canvas.width,this.canvas.height)},t.prototype.resize=function(e,t){this.canvas.width=Math.round(e*this.resolution),this.canvas.height=Math.round(t*this.resolution)},t.prototype.destroy=function(){this.context=null,this.canvas=null},Object.defineProperty(t.prototype,"width",{get:function(){return this.canvas.width},set:function(e){this.canvas.width=Math.round(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"height",{get:function(){return this.canvas.height},set:function(e){this.canvas.height=Math.round(e)},enumerable:!1,configurable:!0}),t}();function J(e){var t,r,n,a=e.width,i=e.height,o=e.getContext("2d",{willReadFrequently:!0}),f=o.getImageData(0,0,a,i).data,l=f.length,s={top:null,left:null,right:null,bottom:null},c=null;for(t=0;t<l;t+=4)0!==f[t+3]&&(r=t/4%a,n=~~(t/4/a),null===s.top&&(s.top=n),(null===s.left||r<s.left)&&(s.left=r),(null===s.right||s.right<r)&&(s.right=r+1),(null===s.bottom||s.bottom<n)&&(s.bottom=n));return null!==s.top&&(a=s.right-s.left,i=s.bottom-s.top+1,c=o.getImageData(s.left,s.top,a,i)),{height:i,width:a,data:c}}var B,$=/^\s*data:(?:([\w-]+)\/([\w+.-]+))?(?:;charset=([\w-]+))?(?:;(base64))?,(.*)/i;function Y(e){var t=$.exec(e);if(t)return{mediaType:t[1]?t[1].toLowerCase():void 0,subType:t[2]?t[2].toLowerCase():void 0,charset:t[3]?t[3].toLowerCase():void 0,encoding:t[4]?t[4].toLowerCase():void 0,data:t[5]}}function G(e,t){if(void 0===t&&(t=globalThis.location),0===e.indexOf("data:"))return"";t=t||globalThis.location,B||(B=document.createElement("a")),B.href=e;var r=i.parse(B.href),n=!r.port&&""===t.port||r.port===t.port;return r.hostname===t.hostname&&n&&r.protocol===t.protocol?"":"anonymous"}function V(t,r){var n=e.RETINA_PREFIX.exec(t);return n?parseFloat(n[1]):void 0!==r?r:1}export{j as BaseTextureCache,z as CanvasRenderTarget,$ as DATA_URI,F as ProgramCache,S as TextureCache,q as clearTextureCache,A as correctBlendMode,E as createIndicesForQuads,Y as decomposeDataUri,U as deprecation,W as destroyTextureCache,G as determineCrossOrigin,P as getBufferType,V as getResolutionOfUrl,b as hex2rgb,p as hex2string,R as interleaveTypedArrays,T as isPow2,h as isWebGLSupported,N as log2,_ as nextPow2,l as path,y as premultiplyBlendMode,x as premultiplyRgba,w as premultiplyTint,k as premultiplyTintToRgba,D as removeItems,m as rgb2hex,u as sayHello,M as sign,d as skipHello,v as string2hex,J as trimCanvas,I as uid,i as url};
//# sourceMappingURL=utils.min.mjs.map
