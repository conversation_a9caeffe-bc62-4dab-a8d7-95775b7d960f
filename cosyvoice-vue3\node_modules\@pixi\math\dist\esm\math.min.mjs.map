{"version": 3, "file": "math.min.mjs", "sources": ["../../src/const.ts", "../../src/Point.ts", "../../src/shapes/Rectangle.ts", "../../src/shapes/Circle.ts", "../../src/shapes/Ellipse.ts", "../../src/shapes/Polygon.ts", "../../src/shapes/RoundedRectangle.ts", "../../src/ObservablePoint.ts", "../../src/Matrix.ts", "../../src/groupD8.ts", "../../src/Transform.ts"], "sourcesContent": ["/**\n * Two Pi.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const PI_2 = Math.PI * 2;\n\n/**\n * Conversion factor for converting radians to degrees.\n * @static\n * @member {number} RAD_TO_DEG\n * @memberof PIXI\n */\nexport const RAD_TO_DEG = 180 / Math.PI;\n\n/**\n * Conversion factor for converting degrees to radians.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const DEG_TO_RAD = Math.PI / 180;\n\n/**\n * Constants that identify shapes, mainly to prevent `instanceof` calls.\n * @static\n * @memberof PIXI\n * @enum {number}\n * @property {number} POLY Polygon\n * @property {number} RECT Rectangle\n * @property {number} CIRC Circle\n * @property {number} ELIP Ellipse\n * @property {number} RREC Rounded Rectangle\n */\nexport enum SHAPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    POLY = 0,\n    RECT = 1,\n    CIRC = 2,\n    ELIP = 3,\n    RREC = 4,\n}\n", "import type { IPoint } from './IPoint';\nimport type { IPointData } from './IPointData';\n\nexport interface Point extends GlobalMixins.Point, IPoint {}\n\n/**\n * The Point object represents a location in a two-dimensional coordinate system, where `x` represents\n * the position on the horizontal axis and `y` represents the position on the vertical axis\n * @class\n * @memberof PIXI\n * @implements {IPoint}\n */\nexport class Point implements IPoint\n{\n    /** Position of the point on the x axis */\n    public x = 0;\n    /** Position of the point on the y axis */\n    public y = 0;\n\n    /**\n     * Creates a new `Point`\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=0] - position of the point on the y axis\n     */\n    constructor(x = 0, y = 0)\n    {\n        this.x = x;\n        this.y = y;\n    }\n\n    /**\n     * Creates a clone of this point\n     * @returns A clone of this point\n     */\n    clone(): Point\n    {\n        return new Point(this.x, this.y);\n    }\n\n    /**\n     * Copies `x` and `y` from the given point into this point\n     * @param p - The point to copy from\n     * @returns The point instance itself\n     */\n    copyFrom(p: IPointData): this\n    {\n        this.set(p.x, p.y);\n\n        return this;\n    }\n\n    /**\n     * Copies this point's x and y into the given point (`p`).\n     * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n     * @returns The point (`p`) with values updated\n     */\n    copyTo<T extends IPoint>(p: T): T\n    {\n        p.set(this.x, this.y);\n\n        return p;\n    }\n\n    /**\n     * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n     * @param p - The point to check\n     * @returns Returns `true` if both `x` and `y` are equal\n     */\n    equals(p: IPointData): boolean\n    {\n        return (p.x === this.x) && (p.y === this.y);\n    }\n\n    /**\n     * Sets the point to a new `x` and `y` position.\n     * If `y` is omitted, both `x` and `y` will be set to `x`.\n     * @param {number} [x=0] - position of the point on the `x` axis\n     * @param {number} [y=x] - position of the point on the `y` axis\n     * @returns The point instance itself\n     */\n    set(x = 0, y = x): this\n    {\n        this.x = x;\n        this.y = y;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Point x=${this.x} y=${this.y}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\nimport type { Matrix } from '../Matrix';\nimport { Point } from '../Point';\n\nconst tempPoints = [new Point(), new Point(), new Point(), new Point()];\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Rectangle extends GlobalMixins.Rectangle {}\n\n/**\n * Size object, contains width and height\n * @memberof PIXI\n * @typedef {object} ISize\n * @property {number} width - Width component\n * @property {number} height - Height component\n */\n\n/**\n * Rectangle object is an area defined by its position, as indicated by its top-left corner\n * point (x, y) and by its width and its height.\n * @memberof PIXI\n */\nexport class Rectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RECT\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RECT;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rectangle\n     * @param width - The overall width of the rectangle\n     * @param height - The overall height of the rectangle\n     */\n    constructor(x: string | number = 0, y: string | number = 0, width: string | number = 0, height: string | number = 0)\n    {\n        this.x = Number(x);\n        this.y = Number(y);\n        this.width = Number(width);\n        this.height = Number(height);\n        this.type = SHAPES.RECT;\n    }\n\n    /** Returns the left edge of the rectangle. */\n    get left(): number\n    {\n        return this.x;\n    }\n\n    /** Returns the right edge of the rectangle. */\n    get right(): number\n    {\n        return this.x + this.width;\n    }\n\n    /** Returns the top edge of the rectangle. */\n    get top(): number\n    {\n        return this.y;\n    }\n\n    /** Returns the bottom edge of the rectangle. */\n    get bottom(): number\n    {\n        return this.y + this.height;\n    }\n\n    /** A constant empty rectangle. */\n    static get EMPTY(): Rectangle\n    {\n        return new Rectangle(0, 0, 0, 0);\n    }\n\n    /**\n     * Creates a clone of this Rectangle\n     * @returns a copy of the rectangle\n     */\n    clone(): Rectangle\n    {\n        return new Rectangle(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Copies another rectangle to this one.\n     * @param rectangle - The rectangle to copy from.\n     * @returns Returns itself.\n     */\n    copyFrom(rectangle: Rectangle): Rectangle\n    {\n        this.x = rectangle.x;\n        this.y = rectangle.y;\n        this.width = rectangle.width;\n        this.height = rectangle.height;\n\n        return this;\n    }\n\n    /**\n     * Copies this rectangle to another one.\n     * @param rectangle - The rectangle to copy to.\n     * @returns Returns given parameter.\n     */\n    copyTo(rectangle: Rectangle): Rectangle\n    {\n        rectangle.x = this.x;\n        rectangle.y = this.y;\n        rectangle.width = this.width;\n        rectangle.height = this.height;\n\n        return rectangle;\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rectangle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Rectangle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        if (x >= this.x && x < this.x + this.width)\n        {\n            if (y >= this.y && y < this.y + this.height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Determines whether the `other` Rectangle transformed by `transform` intersects with `this` Rectangle object.\n     * Returns true only if the area of the intersection is >0, this means that Rectangles\n     * sharing a side are not overlapping. Another side effect is that an arealess rectangle\n     * (width or height equal to zero) can't intersect any other rectangle.\n     * @param {Rectangle} other - The Rectangle to intersect with `this`.\n     * @param {Matrix} transform - The transformation matrix of `other`.\n     * @returns {boolean} A value of `true` if the transformed `other` Rectangle intersects with `this`; otherwise `false`.\n     */\n    intersects(other: Rectangle, transform?: Matrix): boolean\n    {\n        if (!transform)\n        {\n            const x0 = this.x < other.x ? other.x : this.x;\n            const x1 = this.right > other.right ? other.right : this.right;\n\n            if (x1 <= x0)\n            {\n                return false;\n            }\n\n            const y0 = this.y < other.y ? other.y : this.y;\n            const y1 = this.bottom > other.bottom ? other.bottom : this.bottom;\n\n            return y1 > y0;\n        }\n\n        const x0 = this.left;\n        const x1 = this.right;\n        const y0 = this.top;\n        const y1 = this.bottom;\n\n        if (x1 <= x0 || y1 <= y0)\n        {\n            return false;\n        }\n\n        const lt = tempPoints[0].set(other.left, other.top);\n        const lb = tempPoints[1].set(other.left, other.bottom);\n        const rt = tempPoints[2].set(other.right, other.top);\n        const rb = tempPoints[3].set(other.right, other.bottom);\n\n        if (rt.x <= lt.x || lb.y <= lt.y)\n        {\n            return false;\n        }\n\n        const s = Math.sign((transform.a * transform.d) - (transform.b * transform.c));\n\n        if (s === 0)\n        {\n            return false;\n        }\n\n        transform.apply(lt, lt);\n        transform.apply(lb, lb);\n        transform.apply(rt, rt);\n        transform.apply(rb, rb);\n\n        if (Math.max(lt.x, lb.x, rt.x, rb.x) <= x0\n            || Math.min(lt.x, lb.x, rt.x, rb.x) >= x1\n            || Math.max(lt.y, lb.y, rt.y, rb.y) <= y0\n            || Math.min(lt.y, lb.y, rt.y, rb.y) >= y1)\n        {\n            return false;\n        }\n\n        const nx = s * (lb.y - lt.y);\n        const ny = s * (lt.x - lb.x);\n        const n00 = (nx * x0) + (ny * y0);\n        const n10 = (nx * x1) + (ny * y0);\n        const n01 = (nx * x0) + (ny * y1);\n        const n11 = (nx * x1) + (ny * y1);\n\n        if (Math.max(n00, n10, n01, n11) <= (nx * lt.x) + (ny * lt.y)\n            || Math.min(n00, n10, n01, n11) >= (nx * rb.x) + (ny * rb.y))\n        {\n            return false;\n        }\n\n        const mx = s * (lt.y - rt.y);\n        const my = s * (rt.x - lt.x);\n        const m00 = (mx * x0) + (my * y0);\n        const m10 = (mx * x1) + (my * y0);\n        const m01 = (mx * x0) + (my * y1);\n        const m11 = (mx * x1) + (my * y1);\n\n        if (Math.max(m00, m10, m01, m11) <= (mx * lt.x) + (my * lt.y)\n            || Math.min(m00, m10, m01, m11) >= (mx * rb.x) + (my * rb.y))\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * Pads the rectangle making it grow in all directions.\n     * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n     * @param paddingX - The horizontal padding amount.\n     * @param paddingY - The vertical padding amount.\n     * @returns Returns itself.\n     */\n    pad(paddingX = 0, paddingY = paddingX): this\n    {\n        this.x -= paddingX;\n        this.y -= paddingY;\n\n        this.width += paddingX * 2;\n        this.height += paddingY * 2;\n\n        return this;\n    }\n\n    /**\n     * Fits this rectangle around the passed one.\n     * @param rectangle - The rectangle to fit.\n     * @returns Returns itself.\n     */\n    fit(rectangle: Rectangle): this\n    {\n        const x1 = Math.max(this.x, rectangle.x);\n        const x2 = Math.min(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.max(this.y, rectangle.y);\n        const y2 = Math.min(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = Math.max(x2 - x1, 0);\n        this.y = y1;\n        this.height = Math.max(y2 - y1, 0);\n\n        return this;\n    }\n\n    /**\n     * Enlarges rectangle that way its corners lie on grid\n     * @param resolution - resolution\n     * @param eps - precision\n     * @returns Returns itself.\n     */\n    ceil(resolution = 1, eps = 0.001): this\n    {\n        const x2 = Math.ceil((this.x + this.width - eps) * resolution) / resolution;\n        const y2 = Math.ceil((this.y + this.height - eps) * resolution) / resolution;\n\n        this.x = Math.floor((this.x + eps) * resolution) / resolution;\n        this.y = Math.floor((this.y + eps) * resolution) / resolution;\n\n        this.width = x2 - this.x;\n        this.height = y2 - this.y;\n\n        return this;\n    }\n\n    /**\n     * Enlarges this rectangle to include the passed rectangle.\n     * @param rectangle - The rectangle to include.\n     * @returns Returns itself.\n     */\n    enlarge(rectangle: Rectangle): this\n    {\n        const x1 = Math.min(this.x, rectangle.x);\n        const x2 = Math.max(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.min(this.y, rectangle.y);\n        const y2 = Math.max(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = x2 - x1;\n        this.y = y1;\n        this.height = y2 - y1;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from './../const';\nimport { Rectangle } from './Rectangle';\n\n/**\n * The Circle object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Circle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.CIRC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.CIRC;\n\n    /**\n     * @param x - The X coordinate of the center of this circle\n     * @param y - The Y coordinate of the center of this circle\n     * @param radius - The radius of the circle\n     */\n    constructor(x = 0, y = 0, radius = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.radius = radius;\n\n        this.type = SHAPES.CIRC;\n    }\n\n    /**\n     * Creates a clone of this Circle instance\n     * @returns A copy of the Circle\n     */\n    clone(): Circle\n    {\n        return new Circle(this.x, this.y, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this circle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Circle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.radius <= 0)\n        {\n            return false;\n        }\n\n        const r2 = this.radius * this.radius;\n        let dx = (this.x - x);\n        let dy = (this.y - y);\n\n        dx *= dx;\n        dy *= dy;\n\n        return (dx + dy <= r2);\n    }\n\n    /**\n     * Returns the framing rectangle of the circle as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.radius, this.y - this.radius, this.radius * 2, this.radius * 2);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`;\n    }\n    // #endif\n}\n", "import { Rectangle } from './Rectangle';\nimport { SHAPES } from '../const';\n\n/**\n * The Ellipse object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Ellipse\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.ELIP\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.ELIP;\n\n    /**\n     * @param x - The X coordinate of the center of this ellipse\n     * @param y - The Y coordinate of the center of this ellipse\n     * @param halfWidth - The half width of this ellipse\n     * @param halfHeight - The half height of this ellipse\n     */\n    constructor(x = 0, y = 0, halfWidth = 0, halfHeight = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = halfWidth;\n        this.height = halfHeight;\n\n        this.type = SHAPES.ELIP;\n    }\n\n    /**\n     * Creates a clone of this Ellipse instance\n     * @returns {PIXI.Ellipse} A copy of the ellipse\n     */\n    clone(): Ellipse\n    {\n        return new Ellipse(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this ellipse\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coords are within this ellipse\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        // normalize the coords to an ellipse with center 0,0\n        let normx = ((x - this.x) / this.width);\n        let normy = ((y - this.y) / this.height);\n\n        normx *= normx;\n        normy *= normy;\n\n        return (normx + normy <= 1);\n    }\n\n    /**\n     * Returns the framing rectangle of the ellipse as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.width, this.y - this.height, this.width, this.height);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Ellipse x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\nimport type { IPointData } from '../IPointData';\n\n/**\n * A class to define a shape via user defined coordinates.\n * @memberof PIXI\n */\nexport class Polygon\n{\n    /** An array of the points of this polygon. */\n    public points: number[];\n\n    /** `false` after moveTo, `true` after `closePath`. In all other cases it is `true`. */\n    public closeStroke: boolean;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.POLY\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.POLY;\n\n    constructor(points: IPointData[] | number[]);\n    constructor(...points: IPointData[] | number[]);\n\n    /**\n     * @param {PIXI.IPointData[]|number[]} points - This can be an array of Points\n     *  that form the polygon, a flat array of numbers that will be interpreted as [x,y, x,y, ...], or\n     *  the arguments passed can be all the points of the polygon e.g.\n     *  `new PIXI.Polygon(new PIXI.Point(), new PIXI.Point(), ...)`, or the arguments passed can be flat\n     *  x,y values e.g. `new Polygon(x,y, x,y, x,y, ...)` where `x` and `y` are Numbers.\n     */\n    constructor(...points: any[])\n    {\n        let flat: IPointData[] | number[] = Array.isArray(points[0]) ? points[0] : points;\n\n        // if this is an array of points, convert it to a flat array of numbers\n        if (typeof flat[0] !== 'number')\n        {\n            const p: number[] = [];\n\n            for (let i = 0, il = flat.length; i < il; i++)\n            {\n                p.push((flat[i] as IPointData).x, (flat[i] as IPointData).y);\n            }\n\n            flat = p;\n        }\n\n        this.points = flat as number[];\n        this.type = SHAPES.POLY;\n        this.closeStroke = true;\n    }\n\n    /**\n     * Creates a clone of this polygon.\n     * @returns - A copy of the polygon.\n     */\n    clone(): Polygon\n    {\n        const points = this.points.slice();\n        const polygon = new Polygon(points);\n\n        polygon.closeStroke = this.closeStroke;\n\n        return polygon;\n    }\n\n    /**\n     * Checks whether the x and y coordinates passed to this function are contained within this polygon.\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this polygon.\n     */\n    contains(x: number, y: number): boolean\n    {\n        let inside = false;\n\n        // use some raycasting to test hits\n        // https://github.com/substack/point-in-polygon/blob/master/index.js\n        const length = this.points.length / 2;\n\n        for (let i = 0, j = length - 1; i < length; j = i++)\n        {\n            const xi = this.points[i * 2];\n            const yi = this.points[(i * 2) + 1];\n            const xj = this.points[j * 2];\n            const yj = this.points[(j * 2) + 1];\n            const intersect = ((yi > y) !== (yj > y)) && (x < ((xj - xi) * ((y - yi) / (yj - yi))) + xi);\n\n            if (intersect)\n            {\n                inside = !inside;\n            }\n        }\n\n        return inside;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Polygon`\n            + `closeStroke=${this.closeStroke}`\n            + `points=${this.points.reduce((pointsDesc, currentPoint) => `${pointsDesc}, ${currentPoint}`, '')}]`;\n    }\n    // #endif\n}\n", "import { SHAPES } from '../const';\n\n/**\n * The Rounded Rectangle object is an area that has nice rounded corners, as indicated by its\n * top-left corner point (x, y) and by its width and its height and its radius.\n * @memberof PIXI\n */\nexport class RoundedRectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /** @default 20 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RREC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RREC;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rounded rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rounded rectangle\n     * @param width - The overall width of this rounded rectangle\n     * @param height - The overall height of this rounded rectangle\n     * @param radius - Controls the radius of the rounded corners\n     */\n    constructor(x = 0, y = 0, width = 0, height = 0, radius = 20)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n        this.radius = radius;\n        this.type = SHAPES.RREC;\n    }\n\n    /**\n     * Creates a clone of this Rounded Rectangle.\n     * @returns - A copy of the rounded rectangle.\n     */\n    clone(): RoundedRectangle\n    {\n        return new RoundedRectangle(this.x, this.y, this.width, this.height, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rounded Rectangle\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this Rounded Rectangle.\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n        if (x >= this.x && x <= this.x + this.width)\n        {\n            if (y >= this.y && y <= this.y + this.height)\n            {\n                const radius = Math.max(0, Math.min(this.radius, Math.min(this.width, this.height) / 2));\n\n                if ((y >= this.y + radius && y <= this.y + this.height - radius)\n                || (x >= this.x + radius && x <= this.x + this.width - radius))\n                {\n                    return true;\n                }\n                let dx = x - (this.x + radius);\n                let dy = y - (this.y + radius);\n                const radius2 = radius * radius;\n\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + this.width - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dy = y - (this.y + this.height - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:RoundedRectangle x=${this.x} y=${this.y}`\n            + `width=${this.width} height=${this.height} radius=${this.radius}]`;\n    }\n    // #endif\n}\n", "import type { IPointData } from './IPointData';\nimport type { IPoint } from './IPoint';\n\nexport interface ObservablePoint extends GlobalMixins.Point, IPoint {}\n\n/**\n * The ObservablePoint object represents a location in a two-dimensional coordinate system, where `x` represents\n * the position on the horizontal axis and `y` represents the position on the vertical axis.\n *\n * An `ObservablePoint` is a point that triggers a callback when the point's position is changed.\n * @memberof PIXI\n */\nexport class ObservablePoint<T = any> implements IPoint\n{\n    /** The callback function triggered when `x` and/or `y` are changed */\n    public cb: (this: T) => any;\n\n    /** The owner of the callback */\n    public scope: any;\n\n    _x: number;\n    _y: number;\n\n    /**\n     * Creates a new `ObservablePoint`\n     * @param cb - callback function triggered when `x` and/or `y` are changed\n     * @param scope - owner of callback\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=0] - position of the point on the y axis\n     */\n    constructor(cb: (this: T) => any, scope: T, x = 0, y = 0)\n    {\n        this._x = x;\n        this._y = y;\n\n        this.cb = cb;\n        this.scope = scope;\n    }\n\n    /**\n     * Creates a clone of this point.\n     * The callback and scope params can be overridden otherwise they will default\n     * to the clone object's values.\n     * @override\n     * @param cb - The callback function triggered when `x` and/or `y` are changed\n     * @param scope - The owner of the callback\n     * @returns a copy of this observable point\n     */\n    clone(cb = this.cb, scope = this.scope): ObservablePoint\n    {\n        return new ObservablePoint(cb, scope, this._x, this._y);\n    }\n\n    /**\n     * Sets the point to a new `x` and `y` position.\n     * If `y` is omitted, both `x` and `y` will be set to `x`.\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=x] - position of the point on the y axis\n     * @returns The observable point instance itself\n     */\n    set(x = 0, y = x): this\n    {\n        if (this._x !== x || this._y !== y)\n        {\n            this._x = x;\n            this._y = y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies x and y from the given point (`p`)\n     * @param p - The point to copy from. Can be any of type that is or extends `IPointData`\n     * @returns The observable point instance itself\n     */\n    copyFrom(p: IPointData): this\n    {\n        if (this._x !== p.x || this._y !== p.y)\n        {\n            this._x = p.x;\n            this._y = p.y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies this point's x and y into that of the given point (`p`)\n     * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n     * @returns The point (`p`) with values updated\n     */\n    copyTo<T extends IPoint>(p: T): T\n    {\n        p.set(this._x, this._y);\n\n        return p;\n    }\n\n    /**\n     * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n     * @param p - The point to check\n     * @returns Returns `true` if both `x` and `y` are equal\n     */\n    equals(p: IPointData): boolean\n    {\n        return (p.x === this._x) && (p.y === this._y);\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:ObservablePoint x=${0} y=${0} scope=${this.scope}]`;\n    }\n    // #endif\n\n    /** Position of the observable point on the x axis. */\n    get x(): number\n    {\n        return this._x;\n    }\n\n    set x(value: number)\n    {\n        if (this._x !== value)\n        {\n            this._x = value;\n            this.cb.call(this.scope);\n        }\n    }\n\n    /** Position of the observable point on the y axis. */\n    get y(): number\n    {\n        return this._y;\n    }\n\n    set y(value: number)\n    {\n        if (this._y !== value)\n        {\n            this._y = value;\n            this.cb.call(this.scope);\n        }\n    }\n}\n", "import { Point } from './Point';\nimport { PI_2 } from './const';\n\nimport type { Transform } from './Transform';\nimport type { IPointData } from './IPointData';\n\n/**\n * The PixiJS Matrix as a class makes it a lot faster.\n *\n * Here is a representation of it:\n * ```js\n * | a | c | tx|\n * | b | d | ty|\n * | 0 | 0 | 1 |\n * ```\n * @memberof PIXI\n */\nexport class Matrix\n{\n    /** @default 1 */\n    public a: number;\n\n    /** @default 0 */\n    public b: number;\n\n    /** @default 0 */\n    public c: number;\n\n    /** @default 1 */\n    public d: number;\n\n    /** @default 0 */\n    public tx: number;\n\n    /** @default 0 */\n    public ty: number;\n\n    public array: Float32Array | null = null;\n\n    /**\n     * @param a - x scale\n     * @param b - y skew\n     * @param c - x skew\n     * @param d - y scale\n     * @param tx - x translation\n     * @param ty - y translation\n     */\n    constructor(a = 1, b = 0, c = 0, d = 1, tx = 0, ty = 0)\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n    }\n\n    /**\n     * Creates a Matrix object based on the given array. The Element to Matrix mapping order is as follows:\n     *\n     * a = array[0]\n     * b = array[1]\n     * c = array[3]\n     * d = array[4]\n     * tx = array[2]\n     * ty = array[5]\n     * @param array - The array that the matrix will be populated from.\n     */\n    fromArray(array: number[]): void\n    {\n        this.a = array[0];\n        this.b = array[1];\n        this.c = array[3];\n        this.d = array[4];\n        this.tx = array[2];\n        this.ty = array[5];\n    }\n\n    /**\n     * Sets the matrix properties.\n     * @param a - Matrix component\n     * @param b - Matrix component\n     * @param c - Matrix component\n     * @param d - Matrix component\n     * @param tx - Matrix component\n     * @param ty - Matrix component\n     * @returns This matrix. Good for chaining method calls.\n     */\n    set(a: number, b: number, c: number, d: number, tx: number, ty: number): this\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n\n        return this;\n    }\n\n    /**\n     * Creates an array from the current Matrix object.\n     * @param transpose - Whether we need to transpose the matrix or not\n     * @param [out=new Float32Array(9)] - If provided the array will be assigned to out\n     * @returns The newly created array which contains the matrix\n     */\n    toArray(transpose: boolean, out?: Float32Array): Float32Array\n    {\n        if (!this.array)\n        {\n            this.array = new Float32Array(9);\n        }\n\n        const array = out || this.array;\n\n        if (transpose)\n        {\n            array[0] = this.a;\n            array[1] = this.b;\n            array[2] = 0;\n            array[3] = this.c;\n            array[4] = this.d;\n            array[5] = 0;\n            array[6] = this.tx;\n            array[7] = this.ty;\n            array[8] = 1;\n        }\n        else\n        {\n            array[0] = this.a;\n            array[1] = this.c;\n            array[2] = this.tx;\n            array[3] = this.b;\n            array[4] = this.d;\n            array[5] = this.ty;\n            array[6] = 0;\n            array[7] = 0;\n            array[8] = 1;\n        }\n\n        return array;\n    }\n\n    /**\n     * Get a new position with the current transformation applied.\n     * Can be used to go from a child's coordinate space to the world coordinate space. (e.g. rendering)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, transformed through this matrix\n     */\n    apply<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.a * x) + (this.c * y) + this.tx;\n        newPos.y = (this.b * x) + (this.d * y) + this.ty;\n\n        return newPos;\n    }\n\n    /**\n     * Get a new position with the inverse of the current transformation applied.\n     * Can be used to go from the world coordinate space to a child's coordinate space. (e.g. input)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, inverse-transformed through this matrix\n     */\n    applyInverse<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const id = 1 / ((this.a * this.d) + (this.c * -this.b));\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.d * id * x) + (-this.c * id * y) + (((this.ty * this.c) - (this.tx * this.d)) * id);\n        newPos.y = (this.a * id * y) + (-this.b * id * x) + (((-this.ty * this.a) + (this.tx * this.b)) * id);\n\n        return newPos;\n    }\n\n    /**\n     * Translates the matrix on the x and y.\n     * @param x - How much to translate x by\n     * @param y - How much to translate y by\n     * @returns This matrix. Good for chaining method calls.\n     */\n    translate(x: number, y: number): this\n    {\n        this.tx += x;\n        this.ty += y;\n\n        return this;\n    }\n\n    /**\n     * Applies a scale transformation to the matrix.\n     * @param x - The amount to scale horizontally\n     * @param y - The amount to scale vertically\n     * @returns This matrix. Good for chaining method calls.\n     */\n    scale(x: number, y: number): this\n    {\n        this.a *= x;\n        this.d *= y;\n        this.c *= x;\n        this.b *= y;\n        this.tx *= x;\n        this.ty *= y;\n\n        return this;\n    }\n\n    /**\n     * Applies a rotation transformation to the matrix.\n     * @param angle - The angle in radians.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    rotate(angle: number): this\n    {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n\n        const a1 = this.a;\n        const c1 = this.c;\n        const tx1 = this.tx;\n\n        this.a = (a1 * cos) - (this.b * sin);\n        this.b = (a1 * sin) + (this.b * cos);\n        this.c = (c1 * cos) - (this.d * sin);\n        this.d = (c1 * sin) + (this.d * cos);\n        this.tx = (tx1 * cos) - (this.ty * sin);\n        this.ty = (tx1 * sin) + (this.ty * cos);\n\n        return this;\n    }\n\n    /**\n     * Appends the given Matrix to this Matrix.\n     * @param matrix - The matrix to append.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    append(matrix: Matrix): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n\n        this.a = (matrix.a * a1) + (matrix.b * c1);\n        this.b = (matrix.a * b1) + (matrix.b * d1);\n        this.c = (matrix.c * a1) + (matrix.d * c1);\n        this.d = (matrix.c * b1) + (matrix.d * d1);\n\n        this.tx = (matrix.tx * a1) + (matrix.ty * c1) + this.tx;\n        this.ty = (matrix.tx * b1) + (matrix.ty * d1) + this.ty;\n\n        return this;\n    }\n\n    /**\n     * Sets the matrix based on all the available properties\n     * @param x - Position on the x axis\n     * @param y - Position on the y axis\n     * @param pivotX - Pivot on the x axis\n     * @param pivotY - Pivot on the y axis\n     * @param scaleX - Scale on the x axis\n     * @param scaleY - Scale on the y axis\n     * @param rotation - Rotation in radians\n     * @param skewX - Skew on the x axis\n     * @param skewY - Skew on the y axis\n     * @returns This matrix. Good for chaining method calls.\n     */\n    setTransform(x: number, y: number, pivotX: number, pivotY: number, scaleX: number,\n        scaleY: number, rotation: number, skewX: number, skewY: number): this\n    {\n        this.a = Math.cos(rotation + skewY) * scaleX;\n        this.b = Math.sin(rotation + skewY) * scaleX;\n        this.c = -Math.sin(rotation - skewX) * scaleY;\n        this.d = Math.cos(rotation - skewX) * scaleY;\n\n        this.tx = x - ((pivotX * this.a) + (pivotY * this.c));\n        this.ty = y - ((pivotX * this.b) + (pivotY * this.d));\n\n        return this;\n    }\n\n    /**\n     * Prepends the given Matrix to this Matrix.\n     * @param matrix - The matrix to prepend\n     * @returns This matrix. Good for chaining method calls.\n     */\n    prepend(matrix: Matrix): this\n    {\n        const tx1 = this.tx;\n\n        if (matrix.a !== 1 || matrix.b !== 0 || matrix.c !== 0 || matrix.d !== 1)\n        {\n            const a1 = this.a;\n            const c1 = this.c;\n\n            this.a = (a1 * matrix.a) + (this.b * matrix.c);\n            this.b = (a1 * matrix.b) + (this.b * matrix.d);\n            this.c = (c1 * matrix.a) + (this.d * matrix.c);\n            this.d = (c1 * matrix.b) + (this.d * matrix.d);\n        }\n\n        this.tx = (tx1 * matrix.a) + (this.ty * matrix.c) + matrix.tx;\n        this.ty = (tx1 * matrix.b) + (this.ty * matrix.d) + matrix.ty;\n\n        return this;\n    }\n\n    /**\n     * Decomposes the matrix (x, y, scaleX, scaleY, and rotation) and sets the properties on to a transform.\n     * @param transform - The transform to apply the properties to.\n     * @returns The transform with the newly applied properties\n     */\n    decompose(transform: Transform): Transform\n    {\n        // sort out rotation / skew..\n        const a = this.a;\n        const b = this.b;\n        const c = this.c;\n        const d = this.d;\n        const pivot = transform.pivot;\n\n        const skewX = -Math.atan2(-c, d);\n        const skewY = Math.atan2(b, a);\n\n        const delta = Math.abs(skewX + skewY);\n\n        if (delta < 0.00001 || Math.abs(PI_2 - delta) < 0.00001)\n        {\n            transform.rotation = skewY;\n            transform.skew.x = transform.skew.y = 0;\n        }\n        else\n        {\n            transform.rotation = 0;\n            transform.skew.x = skewX;\n            transform.skew.y = skewY;\n        }\n\n        // next set scale\n        transform.scale.x = Math.sqrt((a * a) + (b * b));\n        transform.scale.y = Math.sqrt((c * c) + (d * d));\n\n        // next set position\n        transform.position.x = this.tx + ((pivot.x * a) + (pivot.y * c));\n        transform.position.y = this.ty + ((pivot.x * b) + (pivot.y * d));\n\n        return transform;\n    }\n\n    /**\n     * Inverts this matrix\n     * @returns This matrix. Good for chaining method calls.\n     */\n    invert(): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n        const tx1 = this.tx;\n        const n = (a1 * d1) - (b1 * c1);\n\n        this.a = d1 / n;\n        this.b = -b1 / n;\n        this.c = -c1 / n;\n        this.d = a1 / n;\n        this.tx = ((c1 * this.ty) - (d1 * tx1)) / n;\n        this.ty = -((a1 * this.ty) - (b1 * tx1)) / n;\n\n        return this;\n    }\n\n    /**\n     * Resets this Matrix to an identity (default) matrix.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    identity(): this\n    {\n        this.a = 1;\n        this.b = 0;\n        this.c = 0;\n        this.d = 1;\n        this.tx = 0;\n        this.ty = 0;\n\n        return this;\n    }\n\n    /**\n     * Creates a new Matrix object with the same values as this one.\n     * @returns A copy of this matrix. Good for chaining method calls.\n     */\n    clone(): Matrix\n    {\n        const matrix = new Matrix();\n\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the given matrix to be the same as the ones in this matrix\n     * @param matrix - The matrix to copy to.\n     * @returns The matrix given in parameter with its values updated.\n     */\n    copyTo(matrix: Matrix): Matrix\n    {\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the matrix to be the same as the ones in given matrix\n     * @param {PIXI.Matrix} matrix - The matrix to copy from.\n     * @returns {PIXI.Matrix} this\n     */\n    copyFrom(matrix: Matrix): this\n    {\n        this.a = matrix.a;\n        this.b = matrix.b;\n        this.c = matrix.c;\n        this.d = matrix.d;\n        this.tx = matrix.tx;\n        this.ty = matrix.ty;\n\n        return this;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`;\n    }\n    // #endif\n\n    /**\n     * A default (identity) matrix\n     * @readonly\n     */\n    static get IDENTITY(): Matrix\n    {\n        return new Matrix();\n    }\n\n    /**\n     * A temp matrix\n     * @readonly\n     */\n    static get TEMP_MATRIX(): Matrix\n    {\n        return new Matrix();\n    }\n}\n", "// Your friendly neighbour https://en.wikipedia.org/wiki/Dihedral_group\n//\n// This file implements the dihedral group of order 16, also called\n// of degree 8. That's why its called groupD8.\n\nimport { Matrix } from './Matrix';\n\n/*\n * Transform matrix for operation n is:\n * | ux | vx |\n * | uy | vy |\n */\n\nconst ux = [1, 1, 0, -1, -1, -1, 0, 1, 1, 1, 0, -1, -1, -1, 0, 1];\nconst uy = [0, 1, 1, 1, 0, -1, -1, -1, 0, 1, 1, 1, 0, -1, -1, -1];\nconst vx = [0, -1, -1, -1, 0, 1, 1, 1, 0, 1, 1, 1, 0, -1, -1, -1];\nconst vy = [1, 1, 0, -1, -1, -1, 0, 1, -1, -1, 0, 1, 1, 1, 0, -1];\n\n/**\n * [Cayley Table]{@link https://en.wikipedia.org/wiki/Cayley_table}\n * for the composition of each rotation in the dihederal group D8.\n * @type {number[][]}\n * @private\n */\nconst rotationCayley: number[][] = [];\n\n/**\n * Matrices for each `GD8Symmetry` rotation.\n * @type {PIXI.Matrix[]}\n * @private\n */\nconst rotationMatrices: Matrix[] = [];\n\n/*\n * Alias for {@code Math.sign}.\n */\nconst signum = Math.sign;\n\n/*\n * Initializes `rotationCayley` and `rotationMatrices`. It is called\n * only once below.\n */\nfunction init(): void\n{\n    for (let i = 0; i < 16; i++)\n    {\n        const row: number[] = [];\n\n        rotationCayley.push(row);\n\n        for (let j = 0; j < 16; j++)\n        {\n            /* Multiplies rotation matrices i and j. */\n            const _ux = signum((ux[i] * ux[j]) + (vx[i] * uy[j]));\n            const _uy = signum((uy[i] * ux[j]) + (vy[i] * uy[j]));\n            const _vx = signum((ux[i] * vx[j]) + (vx[i] * vy[j]));\n            const _vy = signum((uy[i] * vx[j]) + (vy[i] * vy[j]));\n\n            /* Finds rotation matrix matching the product and pushes it. */\n            for (let k = 0; k < 16; k++)\n            {\n                if (ux[k] === _ux && uy[k] === _uy\n                      && vx[k] === _vx && vy[k] === _vy)\n                {\n                    row.push(k);\n                    break;\n                }\n            }\n        }\n    }\n\n    for (let i = 0; i < 16; i++)\n    {\n        const mat = new Matrix();\n\n        mat.set(ux[i], uy[i], vx[i], vy[i], 0, 0);\n        rotationMatrices.push(mat);\n    }\n}\n\ninit();\n\ntype GD8Symmetry = number;\n/**\n * @memberof PIXI\n * @typedef {number} GD8Symmetry\n * @see PIXI.groupD8\n */\n\n/**\n * Implements the dihedral group D8, which is similar to\n * [group D4]{@link http://mathworld.wolfram.com/DihedralGroupD4.html};\n * D8 is the same but with diagonals, and it is used for texture\n * rotations.\n *\n * The directions the U- and V- axes after rotation\n * of an angle of `a: GD8Constant` are the vectors `(uX(a), uY(a))`\n * and `(vX(a), vY(a))`. These aren't necessarily unit vectors.\n *\n * **Origin:**<br>\n *  This is the small part of gameofbombs.com portal system. It works.\n * @see PIXI.groupD8.E\n * @see PIXI.groupD8.SE\n * @see PIXI.groupD8.S\n * @see PIXI.groupD8.SW\n * @see PIXI.groupD8.W\n * @see PIXI.groupD8.NW\n * @see PIXI.groupD8.N\n * @see PIXI.groupD8.NE\n * <AUTHOR> @ivanpopelyshev\n * @namespace PIXI.groupD8\n * @memberof PIXI\n */\nexport const groupD8 = {\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 0°       | East      |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    E: 0,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 45°↻     | Southeast |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    SE: 1,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 90°↻     | South     |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    S: 2,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 135°↻    | Southwest |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    SW: 3,\n\n    /**\n     * | Rotation | Direction |\n     * |----------|-----------|\n     * | 180°     | West      |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    W: 4,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -135°/225°↻ | Northwest    |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    NW: 5,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -90°/270°↻  | North        |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    N: 6,\n\n    /**\n     * | Rotation    | Direction    |\n     * |-------------|--------------|\n     * | -45°/315°↻  | Northeast    |\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    NE: 7,\n\n    /**\n     * Reflection about Y-axis.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MIRROR_VERTICAL: 8,\n\n    /**\n     * Reflection about the main diagonal.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MAIN_DIAGONAL: 10,\n\n    /**\n     * Reflection about X-axis.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    MIRROR_HORIZONTAL: 12,\n\n    /**\n     * Reflection about reverse diagonal.\n     * @memberof PIXI.groupD8\n     * @constant {PIXI.GD8Symmetry}\n     */\n    REVERSE_DIAGONAL: 14,\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The X-component of the U-axis\n     *    after rotating the axes.\n     */\n    uX: (ind: GD8Symmetry): GD8Symmetry => ux[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The Y-component of the U-axis\n     *    after rotating the axes.\n     */\n    uY: (ind: GD8Symmetry): GD8Symmetry => uy[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The X-component of the V-axis\n     *    after rotating the axes.\n     */\n    vX: (ind: GD8Symmetry): GD8Symmetry => vx[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} ind - sprite rotation angle.\n     * @returns {PIXI.GD8Symmetry} The Y-component of the V-axis\n     *    after rotating the axes.\n     */\n    vY: (ind: GD8Symmetry): GD8Symmetry => vy[ind],\n\n    /**\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotation - symmetry whose opposite\n     *   is needed. Only rotations have opposite symmetries while\n     *   reflections don't.\n     * @returns {PIXI.GD8Symmetry} The opposite symmetry of `rotation`\n     */\n    inv: (rotation: GD8Symmetry): GD8Symmetry =>\n    {\n        if (rotation & 8)// true only if between 8 & 15 (reflections)\n        {\n            return rotation & 15;// or rotation % 16\n        }\n\n        return (-rotation) & 7;// or (8 - rotation) % 8\n    },\n\n    /**\n     * Composes the two D8 operations.\n     *\n     * Taking `^` as reflection:\n     *\n     * |       | E=0 | S=2 | W=4 | N=6 | E^=8 | S^=10 | W^=12 | N^=14 |\n     * |-------|-----|-----|-----|-----|------|-------|-------|-------|\n     * | E=0   | E   | S   | W   | N   | E^   | S^    | W^    | N^    |\n     * | S=2   | S   | W   | N   | E   | S^   | W^    | N^    | E^    |\n     * | W=4   | W   | N   | E   | S   | W^   | N^    | E^    | S^    |\n     * | N=6   | N   | E   | S   | W   | N^   | E^    | S^    | W^    |\n     * | E^=8  | E^  | N^  | W^  | S^  | E    | N     | W     | S     |\n     * | S^=10 | S^  | E^  | N^  | W^  | S    | E     | N     | W     |\n     * | W^=12 | W^  | S^  | E^  | N^  | W    | S     | E     | N     |\n     * | N^=14 | N^  | W^  | S^  | E^  | N    | W     | S     | E     |\n     *\n     * [This is a Cayley table]{@link https://en.wikipedia.org/wiki/Cayley_table}\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotationSecond - Second operation, which\n     *   is the row in the above cayley table.\n     * @param {PIXI.GD8Symmetry} rotationFirst - First operation, which\n     *   is the column in the above cayley table.\n     * @returns {PIXI.GD8Symmetry} Composed operation\n     */\n    add: (rotationSecond: GD8Symmetry, rotationFirst: GD8Symmetry): GD8Symmetry => (\n        rotationCayley[rotationSecond][rotationFirst]\n    ),\n\n    /**\n     * Reverse of `add`.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotationSecond - Second operation\n     * @param {PIXI.GD8Symmetry} rotationFirst - First operation\n     * @returns {PIXI.GD8Symmetry} Result\n     */\n    sub: (rotationSecond: GD8Symmetry, rotationFirst: GD8Symmetry): GD8Symmetry => (\n        rotationCayley[rotationSecond][groupD8.inv(rotationFirst)]\n    ),\n\n    /**\n     * Adds 180 degrees to rotation, which is a commutative\n     * operation.\n     * @memberof PIXI.groupD8\n     * @param {number} rotation - The number to rotate.\n     * @returns {number} Rotated number\n     */\n    rotate180: (rotation: number): number => rotation ^ 4,\n\n    /**\n     * Checks if the rotation angle is vertical, i.e. south\n     * or north. It doesn't work for reflections.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.GD8Symmetry} rotation - The number to check.\n     * @returns {boolean} Whether or not the direction is vertical\n     */\n    isVertical: (rotation: GD8Symmetry): boolean => (rotation & 3) === 2, // rotation % 4 === 2\n\n    /**\n     * Approximates the vector `V(dx,dy)` into one of the\n     * eight directions provided by `groupD8`.\n     * @memberof PIXI.groupD8\n     * @param {number} dx - X-component of the vector\n     * @param {number} dy - Y-component of the vector\n     * @returns {PIXI.GD8Symmetry} Approximation of the vector into\n     *  one of the eight symmetries.\n     */\n    byDirection: (dx: number, dy: number): GD8Symmetry =>\n    {\n        if (Math.abs(dx) * 2 <= Math.abs(dy))\n        {\n            if (dy >= 0)\n            {\n                return groupD8.S;\n            }\n\n            return groupD8.N;\n        }\n        else if (Math.abs(dy) * 2 <= Math.abs(dx))\n        {\n            if (dx > 0)\n            {\n                return groupD8.E;\n            }\n\n            return groupD8.W;\n        }\n        else if (dy > 0)\n        {\n            if (dx > 0)\n            {\n                return groupD8.SE;\n            }\n\n            return groupD8.SW;\n        }\n        else if (dx > 0)\n        {\n            return groupD8.NE;\n        }\n\n        return groupD8.NW;\n    },\n\n    /**\n     * Helps sprite to compensate texture packer rotation.\n     * @memberof PIXI.groupD8\n     * @param {PIXI.Matrix} matrix - sprite world matrix\n     * @param {PIXI.GD8Symmetry} rotation - The rotation factor to use.\n     * @param {number} tx - sprite anchoring\n     * @param {number} ty - sprite anchoring\n     */\n    matrixAppendRotationInv: (matrix: Matrix, rotation: GD8Symmetry, tx = 0, ty = 0): void =>\n    {\n        // Packer used \"rotation\", we use \"inv(rotation)\"\n        const mat: Matrix = rotationMatrices[groupD8.inv(rotation)];\n\n        mat.tx = tx;\n        mat.ty = ty;\n        matrix.append(mat);\n    },\n};\n", "import { ObservablePoint } from './ObservablePoint';\nimport { Matrix } from './Matrix';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Transform extends GlobalMixins.Transform {}\n\n/**\n * Transform that takes care about its versions.\n * @memberof PIXI\n */\nexport class Transform\n{\n    /** A default (identity) transform. */\n    public static readonly IDENTITY = new Transform();\n\n    /** The world transformation matrix. */\n    public worldTransform: Matrix;\n\n    /** The local transformation matrix. */\n    public localTransform: Matrix;\n\n    /** The coordinate of the object relative to the local coordinates of the parent. */\n    public position: ObservablePoint;\n\n    /** The scale factor of the object. */\n    public scale: ObservablePoint;\n\n    /** The pivot point of the displayObject that it rotates around. */\n    public pivot: ObservablePoint;\n\n    /** The skew amount, on the x and y axis. */\n    public skew: ObservablePoint;\n\n    /** The locally unique ID of the parent's world transform used to calculate the current world transformation matrix. */\n    public _parentID: number;\n\n    /** The locally unique ID of the world transform. */\n    _worldID: number;\n\n    /** The rotation amount. */\n    protected _rotation: number;\n\n    /**\n     * The X-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _cx: number;\n\n    /**\n     * The Y-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _sx: number;\n\n    /**\n     * The X-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _cy: number;\n\n    /**\n     * The Y-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _sy: number;\n\n    /** The locally unique ID of the local transform. */\n    protected _localID: number;\n\n    /** The locally unique ID of the local transform used to calculate the current local transformation matrix. */\n    protected _currentLocalID: number;\n\n    constructor()\n    {\n        this.worldTransform = new Matrix();\n        this.localTransform = new Matrix();\n        this.position = new ObservablePoint(this.onChange, this, 0, 0);\n        this.scale = new ObservablePoint(this.onChange, this, 1, 1);\n        this.pivot = new ObservablePoint(this.onChange, this, 0, 0);\n        this.skew = new ObservablePoint(this.updateSkew, this, 0, 0);\n\n        this._rotation = 0;\n        this._cx = 1;\n        this._sx = 0;\n        this._cy = 0;\n        this._sy = 1;\n        this._localID = 0;\n        this._currentLocalID = 0;\n\n        this._worldID = 0;\n        this._parentID = 0;\n    }\n\n    /** Called when a value changes. */\n    protected onChange(): void\n    {\n        this._localID++;\n    }\n\n    /** Called when the skew or the rotation changes. */\n    protected updateSkew(): void\n    {\n        this._cx = Math.cos(this._rotation + this.skew.y);\n        this._sx = Math.sin(this._rotation + this.skew.y);\n        this._cy = -Math.sin(this._rotation - this.skew.x); // cos, added PI/2\n        this._sy = Math.cos(this._rotation - this.skew.x); // sin, added PI/2\n\n        this._localID++;\n    }\n\n    // #if _DEBUG\n    toString(): string\n    {\n        return `[@pixi/math:Transform `\n            + `position=(${this.position.x}, ${this.position.y}) `\n            + `rotation=${this.rotation} `\n            + `scale=(${this.scale.x}, ${this.scale.y}) `\n            + `skew=(${this.skew.x}, ${this.skew.y}) `\n            + `]`;\n    }\n    // #endif\n\n    /** Updates the local transformation matrix. */\n    updateLocalTransform(): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n    }\n\n    /**\n     * Updates the local and the world transformation matrices.\n     * @param parentTransform - The parent transform\n     */\n    updateTransform(parentTransform: Transform): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n\n        if (this._parentID !== parentTransform._worldID)\n        {\n            // concat the parent matrix with the objects transform.\n            const pt = parentTransform.worldTransform;\n            const wt = this.worldTransform;\n\n            wt.a = (lt.a * pt.a) + (lt.b * pt.c);\n            wt.b = (lt.a * pt.b) + (lt.b * pt.d);\n            wt.c = (lt.c * pt.a) + (lt.d * pt.c);\n            wt.d = (lt.c * pt.b) + (lt.d * pt.d);\n            wt.tx = (lt.tx * pt.a) + (lt.ty * pt.c) + pt.tx;\n            wt.ty = (lt.tx * pt.b) + (lt.ty * pt.d) + pt.ty;\n\n            this._parentID = parentTransform._worldID;\n\n            // update the id of the transform..\n            this._worldID++;\n        }\n    }\n\n    /**\n     * Decomposes a matrix and sets the transforms properties based on it.\n     * @param matrix - The matrix to decompose\n     */\n    setFromMatrix(matrix: Matrix): void\n    {\n        matrix.decompose(this);\n        this._localID++;\n    }\n\n    /** The rotation of the object in radians. */\n    get rotation(): number\n    {\n        return this._rotation;\n    }\n\n    set rotation(value: number)\n    {\n        if (this._rotation !== value)\n        {\n            this._rotation = value;\n            this.updateSkew();\n        }\n    }\n}\n"], "names": ["SHAPES", "PI_2", "Math", "PI", "RAD_TO_DEG", "DEG_TO_RAD", "Point", "x", "y", "this", "prototype", "clone", "copyFrom", "p", "set", "copyTo", "equals", "tempPoints", "Rectangle", "width", "height", "Number", "type", "RECT", "Object", "defineProperty", "get", "rectangle", "contains", "intersects", "other", "transform", "x0_1", "right", "y0_1", "bottom", "x0", "left", "x1", "y0", "top", "y1", "lt", "lb", "rt", "rb", "s", "sign", "a", "d", "b", "c", "apply", "max", "min", "nx", "ny", "n00", "n10", "n01", "n11", "mx", "my", "m00", "m10", "m01", "m11", "pad", "paddingX", "paddingY", "fit", "x2", "y2", "ceil", "resolution", "eps", "floor", "enlarge", "Circle", "radius", "CIRC", "r2", "dx", "dy", "getBounds", "Ellipse", "halfWidth", "halfHeight", "ELIP", "normx", "normy", "Polygon", "points", "_i", "arguments", "length", "flat", "Array", "isArray", "i", "il", "push", "POLY", "closeStroke", "polygon", "slice", "inside", "j", "xi", "yi", "xj", "yj", "RoundedRectangle", "RREC", "radius2", "ObservablePoint", "cb", "scope", "_x", "_y", "call", "value", "Matrix", "tx", "ty", "array", "fromArray", "toArray", "transpose", "out", "Float32Array", "pos", "newPos", "applyInverse", "id", "translate", "scale", "rotate", "angle", "cos", "sin", "a1", "c1", "tx1", "append", "matrix", "b1", "d1", "setTransform", "pivotX", "pivotY", "scaleX", "scaleY", "rotation", "skewX", "skewY", "prepend", "decompose", "pivot", "atan2", "delta", "abs", "skew", "sqrt", "position", "invert", "n", "identity", "ux", "uy", "vx", "vy", "<PERSON><PERSON><PERSON><PERSON>", "rotationMatrices", "signum", "row", "_ux", "_uy", "_vx", "_vy", "k", "mat", "init", "groupD8", "E", "SE", "S", "SW", "W", "NW", "N", "NE", "MIRROR_VERTICAL", "MAIN_DIAGONAL", "MIRROR_HORIZONTAL", "REVERSE_DIAGONAL", "uX", "ind", "uY", "vX", "vY", "inv", "add", "rotationSecond", "<PERSON><PERSON><PERSON><PERSON>", "sub", "rotate180", "isVertical", "byDirection", "matrixAppendRotationInv", "Transform", "worldTransform", "localTransform", "onChange", "updateSkew", "_rotation", "_cx", "_sx", "_cy", "_sy", "_localID", "_currentLocalID", "_worldID", "_parentID", "updateLocalTransform", "updateTransform", "parentTransform", "pt", "wt", "setFromMatrix", "IDENTITY"], "mappings": ";;;;;;;AAMO,IA6BKA,EA7BCC,EAAiB,EAAVC,KAAKC,GAQZC,EAAa,IAAMF,KAAKC,GAQxBE,EAAaH,KAAKC,GAAK,KAapC,SAAYH,GAGRA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OAPJ,CAAYA,IAAAA,EAQX,KC/BD,IAAAM,EAAA,WAYI,SAAYA,EAAAC,EAAOC,QAAP,IAAAD,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAK,GATjBC,KAACF,EAAG,EAEJE,KAACD,EAAG,EASPC,KAAKF,EAAIA,EACTE,KAAKD,EAAIA,EAmEjB,OA5DIF,EAAAI,UAAAC,MAAA,WAEI,OAAO,IAAIL,EAAMG,KAAKF,EAAGE,KAAKD,IAQlCF,EAAQI,UAAAE,SAAR,SAASC,GAIL,OAFAJ,KAAKK,IAAID,EAAEN,EAAGM,EAAEL,GAETC,MAQXH,EAAMI,UAAAK,OAAN,SAAyBF,GAIrB,OAFAA,EAAEC,IAAIL,KAAKF,EAAGE,KAAKD,GAEZK,GAQXP,EAAMI,UAAAM,OAAN,SAAOH,GAEH,OAAQA,EAAEN,IAAME,KAAKF,GAAOM,EAAEL,IAAMC,KAAKD,GAU7CF,EAAAI,UAAAI,IAAA,SAAIP,EAAOC,GAKP,YALA,IAAAD,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAKD,GAEZE,KAAKF,EAAIA,EACTE,KAAKD,EAAIA,EAEFC,MASdH,KC1FKW,EAAa,CAAC,IAAIX,EAAS,IAAIA,EAAS,IAAIA,EAAS,IAAIA,GAkB/DY,EAAA,WA0BI,SAAAA,EAAYX,EAAwBC,EAAwBW,EAA4BC,QAA5E,IAAAb,IAAAA,EAAsB,QAAE,IAAAC,IAAAA,EAAsB,QAAE,IAAAW,IAAAA,EAA0B,QAAE,IAAAC,IAAAA,EAA2B,GAE/GX,KAAKF,EAAIc,OAAOd,GAChBE,KAAKD,EAAIa,OAAOb,GAChBC,KAAKU,MAAQE,OAAOF,GACpBV,KAAKW,OAASC,OAAOD,GACrBX,KAAKa,KAAOtB,EAAOuB,KAoR3B,OAhRIC,OAAAC,eAAIP,EAAIR,UAAA,OAAA,CAARgB,IAAA,WAEI,OAAOjB,KAAKF,mCAIhBiB,OAAAC,eAAIP,EAAKR,UAAA,QAAA,CAATgB,IAAA,WAEI,OAAOjB,KAAKF,EAAIE,KAAKU,uCAIzBK,OAAAC,eAAIP,EAAGR,UAAA,MAAA,CAAPgB,IAAA,WAEI,OAAOjB,KAAKD,mCAIhBgB,OAAAC,eAAIP,EAAMR,UAAA,SAAA,CAAVgB,IAAA,WAEI,OAAOjB,KAAKD,EAAIC,KAAKW,wCAIzBI,OAAAC,eAAWP,EAAK,QAAA,CAAhBQ,IAAA,WAEI,OAAO,IAAIR,EAAU,EAAG,EAAG,EAAG,oCAOlCA,EAAAR,UAAAC,MAAA,WAEI,OAAO,IAAIO,EAAUT,KAAKF,EAAGE,KAAKD,EAAGC,KAAKU,MAAOV,KAAKW,SAQ1DF,EAAQR,UAAAE,SAAR,SAASe,GAOL,OALAlB,KAAKF,EAAIoB,EAAUpB,EACnBE,KAAKD,EAAImB,EAAUnB,EACnBC,KAAKU,MAAQQ,EAAUR,MACvBV,KAAKW,OAASO,EAAUP,OAEjBX,MAQXS,EAAMR,UAAAK,OAAN,SAAOY,GAOH,OALAA,EAAUpB,EAAIE,KAAKF,EACnBoB,EAAUnB,EAAIC,KAAKD,EACnBmB,EAAUR,MAAQV,KAAKU,MACvBQ,EAAUP,OAASX,KAAKW,OAEjBO,GASXT,EAAAR,UAAAkB,SAAA,SAASrB,EAAWC,GAEhB,QAAIC,KAAKU,OAAS,GAAKV,KAAKW,QAAU,KAKlCb,GAAKE,KAAKF,GAAKA,EAAIE,KAAKF,EAAIE,KAAKU,OAE7BX,GAAKC,KAAKD,GAAKA,EAAIC,KAAKD,EAAIC,KAAKW,SAkB7CF,EAAAR,UAAAmB,WAAA,SAAWC,EAAkBC,GAEzB,IAAKA,EACL,CACI,IAAMC,EAAKvB,KAAKF,EAAIuB,EAAMvB,EAAIuB,EAAMvB,EAAIE,KAAKF,EAG7C,IAFWE,KAAKwB,MAAQH,EAAMG,MAAQH,EAAMG,MAAQxB,KAAKwB,QAE/CD,EAEN,OAAO,EAGX,IAAME,EAAKzB,KAAKD,EAAIsB,EAAMtB,EAAIsB,EAAMtB,EAAIC,KAAKD,EAG7C,OAFWC,KAAK0B,OAASL,EAAMK,OAASL,EAAMK,OAAS1B,KAAK0B,QAEhDD,EAGhB,IAAME,EAAK3B,KAAK4B,KACVC,EAAK7B,KAAKwB,MACVM,EAAK9B,KAAK+B,IACVC,EAAKhC,KAAK0B,OAEhB,GAAIG,GAAMF,GAAMK,GAAMF,EAElB,OAAO,EAGX,IAAMG,EAAKzB,EAAW,GAAGH,IAAIgB,EAAMO,KAAMP,EAAMU,KACzCG,EAAK1B,EAAW,GAAGH,IAAIgB,EAAMO,KAAMP,EAAMK,QACzCS,EAAK3B,EAAW,GAAGH,IAAIgB,EAAMG,MAAOH,EAAMU,KAC1CK,EAAK5B,EAAW,GAAGH,IAAIgB,EAAMG,MAAOH,EAAMK,QAEhD,GAAIS,EAAGrC,GAAKmC,EAAGnC,GAAKoC,EAAGnC,GAAKkC,EAAGlC,EAE3B,OAAO,EAGX,IAAMsC,EAAI5C,KAAK6C,KAAMhB,EAAUiB,EAAIjB,EAAUkB,EAAMlB,EAAUmB,EAAInB,EAAUoB,GAE3E,GAAU,IAANL,EAEA,OAAO,EAQX,GALAf,EAAUqB,MAAMV,EAAIA,GACpBX,EAAUqB,MAAMT,EAAIA,GACpBZ,EAAUqB,MAAMR,EAAIA,GACpBb,EAAUqB,MAAMP,EAAIA,GAEhB3C,KAAKmD,IAAIX,EAAGnC,EAAGoC,EAAGpC,EAAGqC,EAAGrC,EAAGsC,EAAGtC,IAAM6B,GACjClC,KAAKoD,IAAIZ,EAAGnC,EAAGoC,EAAGpC,EAAGqC,EAAGrC,EAAGsC,EAAGtC,IAAM+B,GACpCpC,KAAKmD,IAAIX,EAAGlC,EAAGmC,EAAGnC,EAAGoC,EAAGpC,EAAGqC,EAAGrC,IAAM+B,GACpCrC,KAAKoD,IAAIZ,EAAGlC,EAAGmC,EAAGnC,EAAGoC,EAAGpC,EAAGqC,EAAGrC,IAAMiC,EAEvC,OAAO,EAGX,IAAMc,EAAKT,GAAKH,EAAGnC,EAAIkC,EAAGlC,GACpBgD,EAAKV,GAAKJ,EAAGnC,EAAIoC,EAAGpC,GACpBkD,EAAOF,EAAKnB,EAAOoB,EAAKjB,EACxBmB,EAAOH,EAAKjB,EAAOkB,EAAKjB,EACxBoB,EAAOJ,EAAKnB,EAAOoB,EAAKf,EACxBmB,EAAOL,EAAKjB,EAAOkB,EAAKf,EAE9B,GAAIvC,KAAKmD,IAAII,EAAKC,EAAKC,EAAKC,IAASL,EAAKb,EAAGnC,EAAMiD,EAAKd,EAAGlC,GACpDN,KAAKoD,IAAIG,EAAKC,EAAKC,EAAKC,IAASL,EAAKV,EAAGtC,EAAMiD,EAAKX,EAAGrC,EAE1D,OAAO,EAGX,IAAMqD,EAAKf,GAAKJ,EAAGlC,EAAIoC,EAAGpC,GACpBsD,EAAKhB,GAAKF,EAAGrC,EAAImC,EAAGnC,GACpBwD,EAAOF,EAAKzB,EAAO0B,EAAKvB,EACxByB,EAAOH,EAAKvB,EAAOwB,EAAKvB,EACxB0B,EAAOJ,EAAKzB,EAAO0B,EAAKrB,EACxByB,EAAOL,EAAKvB,EAAOwB,EAAKrB,EAE9B,QAAIvC,KAAKmD,IAAIU,EAAKC,EAAKC,EAAKC,IAASL,EAAKnB,EAAGnC,EAAMuD,EAAKpB,EAAGlC,GACpDN,KAAKoD,IAAIS,EAAKC,EAAKC,EAAKC,IAASL,EAAKhB,EAAGtC,EAAMuD,EAAKjB,EAAGrC,IAelEU,EAAAR,UAAAyD,IAAA,SAAIC,EAAcC,GAQd,YARA,IAAAD,IAAAA,EAAY,QAAE,IAAAC,IAAAA,EAAmBD,GAEjC3D,KAAKF,GAAK6D,EACV3D,KAAKD,GAAK6D,EAEV5D,KAAKU,OAAoB,EAAXiD,EACd3D,KAAKW,QAAqB,EAAXiD,EAER5D,MAQXS,EAAGR,UAAA4D,IAAH,SAAI3C,GAEA,IAAMW,EAAKpC,KAAKmD,IAAI5C,KAAKF,EAAGoB,EAAUpB,GAChCgE,EAAKrE,KAAKoD,IAAI7C,KAAKF,EAAIE,KAAKU,MAAOQ,EAAUpB,EAAIoB,EAAUR,OAC3DsB,EAAKvC,KAAKmD,IAAI5C,KAAKD,EAAGmB,EAAUnB,GAChCgE,EAAKtE,KAAKoD,IAAI7C,KAAKD,EAAIC,KAAKW,OAAQO,EAAUnB,EAAImB,EAAUP,QAOlE,OALAX,KAAKF,EAAI+B,EACT7B,KAAKU,MAAQjB,KAAKmD,IAAIkB,EAAKjC,EAAI,GAC/B7B,KAAKD,EAAIiC,EACThC,KAAKW,OAASlB,KAAKmD,IAAImB,EAAK/B,EAAI,GAEzBhC,MASXS,EAAAR,UAAA+D,KAAA,SAAKC,EAAgBC,QAAhB,IAAAD,IAAAA,EAAc,QAAE,IAAAC,IAAAA,EAAW,MAE5B,IAAMJ,EAAKrE,KAAKuE,MAAMhE,KAAKF,EAAIE,KAAKU,MAAQwD,GAAOD,GAAcA,EAC3DF,EAAKtE,KAAKuE,MAAMhE,KAAKD,EAAIC,KAAKW,OAASuD,GAAOD,GAAcA,EAQlE,OANAjE,KAAKF,EAAIL,KAAK0E,OAAOnE,KAAKF,EAAIoE,GAAOD,GAAcA,EACnDjE,KAAKD,EAAIN,KAAK0E,OAAOnE,KAAKD,EAAImE,GAAOD,GAAcA,EAEnDjE,KAAKU,MAAQoD,EAAK9D,KAAKF,EACvBE,KAAKW,OAASoD,EAAK/D,KAAKD,EAEjBC,MAQXS,EAAOR,UAAAmE,QAAP,SAAQlD,GAEJ,IAAMW,EAAKpC,KAAKoD,IAAI7C,KAAKF,EAAGoB,EAAUpB,GAChCgE,EAAKrE,KAAKmD,IAAI5C,KAAKF,EAAIE,KAAKU,MAAOQ,EAAUpB,EAAIoB,EAAUR,OAC3DsB,EAAKvC,KAAKoD,IAAI7C,KAAKD,EAAGmB,EAAUnB,GAChCgE,EAAKtE,KAAKmD,IAAI5C,KAAKD,EAAIC,KAAKW,OAAQO,EAAUnB,EAAImB,EAAUP,QAOlE,OALAX,KAAKF,EAAI+B,EACT7B,KAAKU,MAAQoD,EAAKjC,EAClB7B,KAAKD,EAAIiC,EACThC,KAAKW,OAASoD,EAAK/B,EAEZhC,MASdS,KCnUD4D,EAAA,WAuBI,SAAAA,EAAYvE,EAAOC,EAAOuE,QAAd,IAAAxE,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAK,QAAE,IAAAuE,IAAAA,EAAU,GAEhCtE,KAAKF,EAAIA,EACTE,KAAKD,EAAIA,EACTC,KAAKsE,OAASA,EAEdtE,KAAKa,KAAOtB,EAAOgF,KAkD3B,OA3CIF,EAAApE,UAAAC,MAAA,WAEI,OAAO,IAAImE,EAAOrE,KAAKF,EAAGE,KAAKD,EAAGC,KAAKsE,SAS3CD,EAAApE,UAAAkB,SAAA,SAASrB,EAAWC,GAEhB,GAAIC,KAAKsE,QAAU,EAEf,OAAO,EAGX,IAAME,EAAKxE,KAAKsE,OAAStE,KAAKsE,OAC1BG,EAAMzE,KAAKF,EAAIA,EACf4E,EAAM1E,KAAKD,EAAIA,EAKnB,OAHA0E,GAAMA,IACNC,GAAMA,IAEaF,GAOvBH,EAAApE,UAAA0E,UAAA,WAEI,OAAO,IAAIlE,EAAUT,KAAKF,EAAIE,KAAKsE,OAAQtE,KAAKD,EAAIC,KAAKsE,OAAsB,EAAdtE,KAAKsE,OAA0B,EAAdtE,KAAKsE,SAS9FD,KC/EDO,EAAA,WA2BI,SAAAA,EAAY9E,EAAOC,EAAO8E,EAAeC,QAA7B,IAAAhF,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAK,QAAE,IAAA8E,IAAAA,EAAa,QAAE,IAAAC,IAAAA,EAAc,GAEnD9E,KAAKF,EAAIA,EACTE,KAAKD,EAAIA,EACTC,KAAKU,MAAQmE,EACb7E,KAAKW,OAASmE,EAEd9E,KAAKa,KAAOtB,EAAOwF,KAkD3B,OA3CIH,EAAA3E,UAAAC,MAAA,WAEI,OAAO,IAAI0E,EAAQ5E,KAAKF,EAAGE,KAAKD,EAAGC,KAAKU,MAAOV,KAAKW,SASxDiE,EAAA3E,UAAAkB,SAAA,SAASrB,EAAWC,GAEhB,GAAIC,KAAKU,OAAS,GAAKV,KAAKW,QAAU,EAElC,OAAO,EAIX,IAAIqE,GAAUlF,EAAIE,KAAKF,GAAKE,KAAKU,MAC7BuE,GAAUlF,EAAIC,KAAKD,GAAKC,KAAKW,OAKjC,OAHAqE,GAASA,IACTC,GAASA,IAEgB,GAO7BL,EAAA3E,UAAA0E,UAAA,WAEI,OAAO,IAAIlE,EAAUT,KAAKF,EAAIE,KAAKU,MAAOV,KAAKD,EAAIC,KAAKW,OAAQX,KAAKU,MAAOV,KAAKW,SASxFiE,KCpFDM,EAAA,WAyBI,SAAAA,wBAA4BC,EAAA,GAAAC,EAAA,EAAhBA,EAAgBC,UAAAC,OAAhBF,IAAAD,EAAgBC,GAAAC,EAAAD,GAExB,IAAIG,EAAgCC,MAAMC,QAAQN,EAAO,IAAMA,EAAO,GAAKA,EAG3E,GAAuB,iBAAZI,EAAK,GAChB,CAGI,IAFA,IAAMnF,EAAc,GAEXsF,EAAI,EAAGC,EAAKJ,EAAKD,OAAQI,EAAIC,EAAID,IAEtCtF,EAAEwF,KAAML,EAAKG,GAAkB5F,EAAIyF,EAAKG,GAAkB3F,GAG9DwF,EAAOnF,EAGXJ,KAAKmF,OAASI,EACdvF,KAAKa,KAAOtB,EAAOsG,KACnB7F,KAAK8F,aAAc,EAwD3B,OAjDIZ,EAAAjF,UAAAC,MAAA,WAEI,IACM6F,EAAU,IAAIb,EADLlF,KAAKmF,OAAOa,SAK3B,OAFAD,EAAQD,YAAc9F,KAAK8F,YAEpBC,GASXb,EAAAjF,UAAAkB,SAAA,SAASrB,EAAWC,GAQhB,IANA,IAAIkG,GAAS,EAIPX,EAAStF,KAAKmF,OAAOG,OAAS,EAE3BI,EAAI,EAAGQ,EAAIZ,EAAS,EAAGI,EAAIJ,EAAQY,EAAIR,IAChD,CACI,IAAMS,EAAKnG,KAAKmF,OAAW,EAAJO,GACjBU,EAAKpG,KAAKmF,OAAY,EAAJO,EAAS,GAC3BW,EAAKrG,KAAKmF,OAAW,EAAJe,GACjBI,EAAKtG,KAAKmF,OAAY,EAAJe,EAAS,GACbE,EAAKrG,GAAQuG,EAAKvG,GAAQD,GAAmBC,EAAIqG,IAAOE,EAAKF,IAA7BC,EAAKF,GAAgCA,IAIrFF,GAAUA,GAIlB,OAAOA,GAWdf,KCpGDqB,EAAA,WA+BI,SAAYA,EAAAzG,EAAOC,EAAOW,EAAWC,EAAY2D,QAArC,IAAAxE,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAK,QAAE,IAAAW,IAAAA,EAAS,QAAE,IAAAC,IAAAA,EAAU,QAAE,IAAA2D,IAAAA,EAAW,IAExDtE,KAAKF,EAAIA,EACTE,KAAKD,EAAIA,EACTC,KAAKU,MAAQA,EACbV,KAAKW,OAASA,EACdX,KAAKsE,OAASA,EACdtE,KAAKa,KAAOtB,EAAOiH,KAuE3B,OAhEID,EAAAtG,UAAAC,MAAA,WAEI,OAAO,IAAIqG,EAAiBvG,KAAKF,EAAGE,KAAKD,EAAGC,KAAKU,MAAOV,KAAKW,OAAQX,KAAKsE,SAS9EiC,EAAAtG,UAAAkB,SAAA,SAASrB,EAAWC,GAEhB,GAAIC,KAAKU,OAAS,GAAKV,KAAKW,QAAU,EAElC,OAAO,EAEX,GAAIb,GAAKE,KAAKF,GAAKA,GAAKE,KAAKF,EAAIE,KAAKU,OAE9BX,GAAKC,KAAKD,GAAKA,GAAKC,KAAKD,EAAIC,KAAKW,OACtC,CACI,IAAM2D,EAAS7E,KAAKmD,IAAI,EAAGnD,KAAKoD,IAAI7C,KAAKsE,OAAQ7E,KAAKoD,IAAI7C,KAAKU,MAAOV,KAAKW,QAAU,IAErF,GAAKZ,GAAKC,KAAKD,EAAIuE,GAAUvE,GAAKC,KAAKD,EAAIC,KAAKW,OAAS2D,GACrDxE,GAAKE,KAAKF,EAAIwE,GAAUxE,GAAKE,KAAKF,EAAIE,KAAKU,MAAQ4D,EAEnD,OAAO,EAEX,IAAIG,EAAK3E,GAAKE,KAAKF,EAAIwE,GACnBI,EAAK3E,GAAKC,KAAKD,EAAIuE,GACjBmC,EAAUnC,EAASA,EAEzB,GAAKG,EAAKA,EAAOC,EAAKA,GAAO+B,EAEzB,OAAO,EAGX,IADAhC,EAAK3E,GAAKE,KAAKF,EAAIE,KAAKU,MAAQ4D,IACtBG,EAAOC,EAAKA,GAAO+B,EAEzB,OAAO,EAGX,GAAKhC,EAAKA,GADVC,EAAK3E,GAAKC,KAAKD,EAAIC,KAAKW,OAAS2D,IACXI,GAAO+B,EAEzB,OAAO,EAGX,IADAhC,EAAK3E,GAAKE,KAAKF,EAAIwE,IACTG,EAAOC,EAAKA,GAAO+B,EAEzB,OAAO,EAKnB,OAAO,GAUdF,KCxGDG,EAAA,WAkBI,SAAAA,EAAYC,EAAsBC,EAAU9G,EAAOC,QAAP,IAAAD,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAK,GAEpDC,KAAK6G,GAAK/G,EACVE,KAAK8G,GAAK/G,EAEVC,KAAK2G,GAAKA,EACV3G,KAAK4G,MAAQA,EA+GrB,OAnGIF,EAAAzG,UAAAC,MAAA,SAAMyG,EAAcC,GAEhB,YAFE,IAAAD,IAAAA,EAAK3G,KAAK2G,SAAI,IAAAC,IAAAA,EAAQ5G,KAAK4G,OAEtB,IAAIF,EAAgBC,EAAIC,EAAO5G,KAAK6G,GAAI7G,KAAK8G,KAUxDJ,EAAAzG,UAAAI,IAAA,SAAIP,EAAOC,GASP,YATA,IAAAD,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAKD,GAERE,KAAK6G,KAAO/G,GAAKE,KAAK8G,KAAO/G,IAE7BC,KAAK6G,GAAK/G,EACVE,KAAK8G,GAAK/G,EACVC,KAAK2G,GAAGI,KAAK/G,KAAK4G,QAGf5G,MAQX0G,EAAQzG,UAAAE,SAAR,SAASC,GASL,OAPIJ,KAAK6G,KAAOzG,EAAEN,GAAKE,KAAK8G,KAAO1G,EAAEL,IAEjCC,KAAK6G,GAAKzG,EAAEN,EACZE,KAAK8G,GAAK1G,EAAEL,EACZC,KAAK2G,GAAGI,KAAK/G,KAAK4G,QAGf5G,MAQX0G,EAAMzG,UAAAK,OAAN,SAAyBF,GAIrB,OAFAA,EAAEC,IAAIL,KAAK6G,GAAI7G,KAAK8G,IAEb1G,GAQXsG,EAAMzG,UAAAM,OAAN,SAAOH,GAEH,OAAQA,EAAEN,IAAME,KAAK6G,IAAQzG,EAAEL,IAAMC,KAAK8G,IAW9C/F,OAAAC,eAAI0F,EAACzG,UAAA,IAAA,CAALgB,IAAA,WAEI,OAAOjB,KAAK6G,IAGhBxG,IAAA,SAAM2G,GAEEhH,KAAK6G,KAAOG,IAEZhH,KAAK6G,GAAKG,EACVhH,KAAK2G,GAAGI,KAAK/G,KAAK4G,yCAK1B7F,OAAAC,eAAI0F,EAACzG,UAAA,IAAA,CAALgB,IAAA,WAEI,OAAOjB,KAAK8G,IAGhBzG,IAAA,SAAM2G,GAEEhH,KAAK8G,KAAOE,IAEZhH,KAAK8G,GAAKE,EACVhH,KAAK2G,GAAGI,KAAK/G,KAAK4G,yCAG7BF,KClIDO,EAAA,WA8BI,SAAYA,EAAA1E,EAAOE,EAAOC,EAAOF,EAAO0E,EAAQC,QAApC,IAAA5E,IAAAA,EAAK,QAAE,IAAAE,IAAAA,EAAK,QAAE,IAAAC,IAAAA,EAAK,QAAE,IAAAF,IAAAA,EAAK,QAAE,IAAA0E,IAAAA,EAAM,QAAE,IAAAC,IAAAA,EAAM,GAV/CnH,KAAKoH,MAAwB,KAYhCpH,KAAKuC,EAAIA,EACTvC,KAAKyC,EAAIA,EACTzC,KAAK0C,EAAIA,EACT1C,KAAKwC,EAAIA,EACTxC,KAAKkH,GAAKA,EACVlH,KAAKmH,GAAKA,EAoalB,OAtZIF,EAAShH,UAAAoH,UAAT,SAAUD,GAENpH,KAAKuC,EAAI6E,EAAM,GACfpH,KAAKyC,EAAI2E,EAAM,GACfpH,KAAK0C,EAAI0E,EAAM,GACfpH,KAAKwC,EAAI4E,EAAM,GACfpH,KAAKkH,GAAKE,EAAM,GAChBpH,KAAKmH,GAAKC,EAAM,IAapBH,EAAAhH,UAAAI,IAAA,SAAIkC,EAAWE,EAAWC,EAAWF,EAAW0E,EAAYC,GASxD,OAPAnH,KAAKuC,EAAIA,EACTvC,KAAKyC,EAAIA,EACTzC,KAAK0C,EAAIA,EACT1C,KAAKwC,EAAIA,EACTxC,KAAKkH,GAAKA,EACVlH,KAAKmH,GAAKA,EAEHnH,MASXiH,EAAAhH,UAAAqH,QAAA,SAAQC,EAAoBC,GAEnBxH,KAAKoH,QAENpH,KAAKoH,MAAQ,IAAIK,aAAa,IAGlC,IAAML,EAAQI,GAAOxH,KAAKoH,MA2B1B,OAzBIG,GAEAH,EAAM,GAAKpH,KAAKuC,EAChB6E,EAAM,GAAKpH,KAAKyC,EAChB2E,EAAM,GAAK,EACXA,EAAM,GAAKpH,KAAK0C,EAChB0E,EAAM,GAAKpH,KAAKwC,EAChB4E,EAAM,GAAK,EACXA,EAAM,GAAKpH,KAAKkH,GAChBE,EAAM,GAAKpH,KAAKmH,GAChBC,EAAM,GAAK,IAIXA,EAAM,GAAKpH,KAAKuC,EAChB6E,EAAM,GAAKpH,KAAK0C,EAChB0E,EAAM,GAAKpH,KAAKkH,GAChBE,EAAM,GAAKpH,KAAKyC,EAChB2E,EAAM,GAAKpH,KAAKwC,EAChB4E,EAAM,GAAKpH,KAAKmH,GAChBC,EAAM,GAAK,EACXA,EAAM,GAAK,EACXA,EAAM,GAAK,GAGRA,GAUXH,EAAAhH,UAAA0C,MAAA,SAAoC+E,EAAiBC,GAEjDA,EAAUA,GAAU,IAAI9H,EAExB,IAAMC,EAAI4H,EAAI5H,EACRC,EAAI2H,EAAI3H,EAKd,OAHA4H,EAAO7H,EAAKE,KAAKuC,EAAIzC,EAAME,KAAK0C,EAAI3C,EAAKC,KAAKkH,GAC9CS,EAAO5H,EAAKC,KAAKyC,EAAI3C,EAAME,KAAKwC,EAAIzC,EAAKC,KAAKmH,GAEvCQ,GAUXV,EAAAhH,UAAA2H,aAAA,SAA2CF,EAAiBC,GAExDA,EAAUA,GAAU,IAAI9H,EAExB,IAAMgI,EAAK,GAAM7H,KAAKuC,EAAIvC,KAAKwC,EAAMxC,KAAK0C,GAAK1C,KAAKyC,GAE9C3C,EAAI4H,EAAI5H,EACRC,EAAI2H,EAAI3H,EAKd,OAHA4H,EAAO7H,EAAKE,KAAKwC,EAAIqF,EAAK/H,GAAOE,KAAK0C,EAAImF,EAAK9H,GAAQC,KAAKmH,GAAKnH,KAAK0C,EAAM1C,KAAKkH,GAAKlH,KAAKwC,GAAMqF,EACjGF,EAAO5H,EAAKC,KAAKuC,EAAIsF,EAAK9H,GAAOC,KAAKyC,EAAIoF,EAAK/H,IAASE,KAAKmH,GAAKnH,KAAKuC,EAAMvC,KAAKkH,GAAKlH,KAAKyC,GAAMoF,EAE3FF,GASXV,EAAAhH,UAAA6H,UAAA,SAAUhI,EAAWC,GAKjB,OAHAC,KAAKkH,IAAMpH,EACXE,KAAKmH,IAAMpH,EAEJC,MASXiH,EAAAhH,UAAA8H,MAAA,SAAMjI,EAAWC,GASb,OAPAC,KAAKuC,GAAKzC,EACVE,KAAKwC,GAAKzC,EACVC,KAAK0C,GAAK5C,EACVE,KAAKyC,GAAK1C,EACVC,KAAKkH,IAAMpH,EACXE,KAAKmH,IAAMpH,EAEJC,MAQXiH,EAAMhH,UAAA+H,OAAN,SAAOC,GAEH,IAAMC,EAAMzI,KAAKyI,IAAID,GACfE,EAAM1I,KAAK0I,IAAIF,GAEfG,EAAKpI,KAAKuC,EACV8F,EAAKrI,KAAK0C,EACV4F,EAAMtI,KAAKkH,GASjB,OAPAlH,KAAKuC,EAAK6F,EAAKF,EAAQlI,KAAKyC,EAAI0F,EAChCnI,KAAKyC,EAAK2F,EAAKD,EAAQnI,KAAKyC,EAAIyF,EAChClI,KAAK0C,EAAK2F,EAAKH,EAAQlI,KAAKwC,EAAI2F,EAChCnI,KAAKwC,EAAK6F,EAAKF,EAAQnI,KAAKwC,EAAI0F,EAChClI,KAAKkH,GAAMoB,EAAMJ,EAAQlI,KAAKmH,GAAKgB,EACnCnI,KAAKmH,GAAMmB,EAAMH,EAAQnI,KAAKmH,GAAKe,EAE5BlI,MAQXiH,EAAMhH,UAAAsI,OAAN,SAAOC,GAEH,IAAMJ,EAAKpI,KAAKuC,EACVkG,EAAKzI,KAAKyC,EACV4F,EAAKrI,KAAK0C,EACVgG,EAAK1I,KAAKwC,EAUhB,OARAxC,KAAKuC,EAAKiG,EAAOjG,EAAI6F,EAAOI,EAAO/F,EAAI4F,EACvCrI,KAAKyC,EAAK+F,EAAOjG,EAAIkG,EAAOD,EAAO/F,EAAIiG,EACvC1I,KAAK0C,EAAK8F,EAAO9F,EAAI0F,EAAOI,EAAOhG,EAAI6F,EACvCrI,KAAKwC,EAAKgG,EAAO9F,EAAI+F,EAAOD,EAAOhG,EAAIkG,EAEvC1I,KAAKkH,GAAMsB,EAAOtB,GAAKkB,EAAOI,EAAOrB,GAAKkB,EAAMrI,KAAKkH,GACrDlH,KAAKmH,GAAMqB,EAAOtB,GAAKuB,EAAOD,EAAOrB,GAAKuB,EAAM1I,KAAKmH,GAE9CnH,MAgBXiH,EAAAhH,UAAA0I,aAAA,SAAa7I,EAAWC,EAAW6I,EAAgBC,EAAgBC,EAC/DC,EAAgBC,EAAkBC,EAAeC,GAUjD,OARAlJ,KAAKuC,EAAI9C,KAAKyI,IAAIc,EAAWE,GAASJ,EACtC9I,KAAKyC,EAAIhD,KAAK0I,IAAIa,EAAWE,GAASJ,EACtC9I,KAAK0C,GAAKjD,KAAK0I,IAAIa,EAAWC,GAASF,EACvC/I,KAAKwC,EAAI/C,KAAKyI,IAAIc,EAAWC,GAASF,EAEtC/I,KAAKkH,GAAKpH,GAAM8I,EAAS5I,KAAKuC,EAAMsG,EAAS7I,KAAK0C,GAClD1C,KAAKmH,GAAKpH,GAAM6I,EAAS5I,KAAKyC,EAAMoG,EAAS7I,KAAKwC,GAE3CxC,MAQXiH,EAAOhH,UAAAkJ,QAAP,SAAQX,GAEJ,IAAMF,EAAMtI,KAAKkH,GAEjB,GAAiB,IAAbsB,EAAOjG,GAAwB,IAAbiG,EAAO/F,GAAwB,IAAb+F,EAAO9F,GAAwB,IAAb8F,EAAOhG,EACjE,CACI,IAAM4F,EAAKpI,KAAKuC,EACV8F,EAAKrI,KAAK0C,EAEhB1C,KAAKuC,EAAK6F,EAAKI,EAAOjG,EAAMvC,KAAKyC,EAAI+F,EAAO9F,EAC5C1C,KAAKyC,EAAK2F,EAAKI,EAAO/F,EAAMzC,KAAKyC,EAAI+F,EAAOhG,EAC5CxC,KAAK0C,EAAK2F,EAAKG,EAAOjG,EAAMvC,KAAKwC,EAAIgG,EAAO9F,EAC5C1C,KAAKwC,EAAK6F,EAAKG,EAAO/F,EAAMzC,KAAKwC,EAAIgG,EAAOhG,EAMhD,OAHAxC,KAAKkH,GAAMoB,EAAME,EAAOjG,EAAMvC,KAAKmH,GAAKqB,EAAO9F,EAAK8F,EAAOtB,GAC3DlH,KAAKmH,GAAMmB,EAAME,EAAO/F,EAAMzC,KAAKmH,GAAKqB,EAAOhG,EAAKgG,EAAOrB,GAEpDnH,MAQXiH,EAAShH,UAAAmJ,UAAT,SAAU9H,GAGN,IAAMiB,EAAIvC,KAAKuC,EACTE,EAAIzC,KAAKyC,EACTC,EAAI1C,KAAK0C,EACTF,EAAIxC,KAAKwC,EACT6G,EAAQ/H,EAAU+H,MAElBJ,GAASxJ,KAAK6J,OAAO5G,EAAGF,GACxB0G,EAAQzJ,KAAK6J,MAAM7G,EAAGF,GAEtBgH,EAAQ9J,KAAK+J,IAAIP,EAAQC,GAsB/B,OApBIK,EAAQ,MAAW9J,KAAK+J,IAAIhK,EAAO+J,GAAS,MAE5CjI,EAAU0H,SAAWE,EACrB5H,EAAUmI,KAAK3J,EAAIwB,EAAUmI,KAAK1J,EAAI,IAItCuB,EAAU0H,SAAW,EACrB1H,EAAUmI,KAAK3J,EAAImJ,EACnB3H,EAAUmI,KAAK1J,EAAImJ,GAIvB5H,EAAUyG,MAAMjI,EAAIL,KAAKiK,KAAMnH,EAAIA,EAAME,EAAIA,GAC7CnB,EAAUyG,MAAMhI,EAAIN,KAAKiK,KAAMhH,EAAIA,EAAMF,EAAIA,GAG7ClB,EAAUqI,SAAS7J,EAAIE,KAAKkH,IAAOmC,EAAMvJ,EAAIyC,EAAM8G,EAAMtJ,EAAI2C,GAC7DpB,EAAUqI,SAAS5J,EAAIC,KAAKmH,IAAOkC,EAAMvJ,EAAI2C,EAAM4G,EAAMtJ,EAAIyC,GAEtDlB,GAOX2F,EAAAhH,UAAA2J,OAAA,WAEI,IAAMxB,EAAKpI,KAAKuC,EACVkG,EAAKzI,KAAKyC,EACV4F,EAAKrI,KAAK0C,EACVgG,EAAK1I,KAAKwC,EACV8F,EAAMtI,KAAKkH,GACX2C,EAAKzB,EAAKM,EAAOD,EAAKJ,EAS5B,OAPArI,KAAKuC,EAAImG,EAAKmB,EACd7J,KAAKyC,GAAKgG,EAAKoB,EACf7J,KAAK0C,GAAK2F,EAAKwB,EACf7J,KAAKwC,EAAI4F,EAAKyB,EACd7J,KAAKkH,IAAOmB,EAAKrI,KAAKmH,GAAOuB,EAAKJ,GAAQuB,EAC1C7J,KAAKmH,KAAQiB,EAAKpI,KAAKmH,GAAOsB,EAAKH,GAAQuB,EAEpC7J,MAOXiH,EAAAhH,UAAA6J,SAAA,WASI,OAPA9J,KAAKuC,EAAI,EACTvC,KAAKyC,EAAI,EACTzC,KAAK0C,EAAI,EACT1C,KAAKwC,EAAI,EACTxC,KAAKkH,GAAK,EACVlH,KAAKmH,GAAK,EAEHnH,MAOXiH,EAAAhH,UAAAC,MAAA,WAEI,IAAMsI,EAAS,IAAIvB,EASnB,OAPAuB,EAAOjG,EAAIvC,KAAKuC,EAChBiG,EAAO/F,EAAIzC,KAAKyC,EAChB+F,EAAO9F,EAAI1C,KAAK0C,EAChB8F,EAAOhG,EAAIxC,KAAKwC,EAChBgG,EAAOtB,GAAKlH,KAAKkH,GACjBsB,EAAOrB,GAAKnH,KAAKmH,GAEVqB,GAQXvB,EAAMhH,UAAAK,OAAN,SAAOkI,GASH,OAPAA,EAAOjG,EAAIvC,KAAKuC,EAChBiG,EAAO/F,EAAIzC,KAAKyC,EAChB+F,EAAO9F,EAAI1C,KAAK0C,EAChB8F,EAAOhG,EAAIxC,KAAKwC,EAChBgG,EAAOtB,GAAKlH,KAAKkH,GACjBsB,EAAOrB,GAAKnH,KAAKmH,GAEVqB,GAQXvB,EAAQhH,UAAAE,SAAR,SAASqI,GASL,OAPAxI,KAAKuC,EAAIiG,EAAOjG,EAChBvC,KAAKyC,EAAI+F,EAAO/F,EAChBzC,KAAK0C,EAAI8F,EAAO9F,EAChB1C,KAAKwC,EAAIgG,EAAOhG,EAChBxC,KAAKkH,GAAKsB,EAAOtB,GACjBlH,KAAKmH,GAAKqB,EAAOrB,GAEVnH,MAcXe,OAAAC,eAAWiG,EAAQ,WAAA,CAAnBhG,IAAA,WAEI,OAAO,IAAIgG,mCAOflG,OAAAC,eAAWiG,EAAW,cAAA,CAAtBhG,IAAA,WAEI,OAAO,IAAIgG,mCAElBA,KC7cK8C,EAAK,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,EAAG,EAAG,GACzDC,EAAK,CAAC,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GACzDC,EAAK,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GACzDC,EAAK,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,EAAG,EAAG,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAQzDC,EAA6B,GAO7BC,EAA6B,GAK7BC,EAAS5K,KAAK6C,MAMpB,WAEI,IAAK,IAAIoD,EAAI,EAAGA,EAAI,GAAIA,IACxB,CACI,IAAM4E,EAAgB,GAEtBH,EAAevE,KAAK0E,GAEpB,IAAK,IAAIpE,EAAI,EAAGA,EAAI,GAAIA,IASpB,IANA,IAAMqE,EAAMF,EAAQN,EAAGrE,GAAKqE,EAAG7D,GAAO+D,EAAGvE,GAAKsE,EAAG9D,IAC3CsE,EAAMH,EAAQL,EAAGtE,GAAKqE,EAAG7D,GAAOgE,EAAGxE,GAAKsE,EAAG9D,IAC3CuE,EAAMJ,EAAQN,EAAGrE,GAAKuE,EAAG/D,GAAO+D,EAAGvE,GAAKwE,EAAGhE,IAC3CwE,EAAML,EAAQL,EAAGtE,GAAKuE,EAAG/D,GAAOgE,EAAGxE,GAAKwE,EAAGhE,IAGxCyE,EAAI,EAAGA,EAAI,GAAIA,IAEpB,GAAIZ,EAAGY,KAAOJ,GAAOP,EAAGW,KAAOH,GACtBP,EAAGU,KAAOF,GAAOP,EAAGS,KAAOD,EACpC,CACIJ,EAAI1E,KAAK+E,GACT,OAMhB,IAASjF,EAAI,EAAGA,EAAI,GAAIA,IACxB,CACI,IAAMkF,EAAM,IAAI3D,EAEhB2D,EAAIvK,IAAI0J,EAAGrE,GAAIsE,EAAGtE,GAAIuE,EAAGvE,GAAIwE,EAAGxE,GAAI,EAAG,GACvC0E,EAAiBxE,KAAKgF,IAI9BC,GAiCO,IAAMC,EAAU,CAQnBC,EAAG,EASHC,GAAI,EASJC,EAAG,EASHC,GAAI,EASJC,EAAG,EASHC,GAAI,EASJC,EAAG,EASHC,GAAI,EAOJC,gBAAiB,EAOjBC,cAAe,GAOfC,kBAAmB,GAOnBC,iBAAkB,GAQlBC,GAAI,SAACC,GAAkC,OAAA7B,EAAG6B,IAQ1CC,GAAI,SAACD,GAAkC,OAAA5B,EAAG4B,IAQ1CE,GAAI,SAACF,GAAkC,OAAA3B,EAAG2B,IAQ1CG,GAAI,SAACH,GAAkC,OAAA1B,EAAG0B,IAS1CI,IAAK,SAAChD,GAEF,OAAe,EAAXA,EAEkB,GAAXA,EAGU,GAAZA,GA2BbiD,IAAK,SAACC,EAA6BC,GAA4C,OAC3EhC,EAAe+B,GAAgBC,IAUnCC,IAAK,SAACF,EAA6BC,GAA4C,OAC3EhC,EAAe+B,GAAgBpB,EAAQkB,IAAIG,KAU/CE,UAAW,SAACrD,GAA6B,OAAW,EAAXA,GASzCsD,WAAY,SAACtD,GAAmC,OAAmB,IAAP,EAAXA,IAWjDuD,YAAa,SAAC9H,EAAYC,GAEtB,OAAmB,EAAfjF,KAAK+J,IAAI/E,IAAWhF,KAAK+J,IAAI9E,GAEzBA,GAAM,EAECoG,EAAQG,EAGZH,EAAQO,EAEK,EAAf5L,KAAK+J,IAAI9E,IAAWjF,KAAK+J,IAAI/E,GAE9BA,EAAK,EAEEqG,EAAQC,EAGZD,EAAQK,EAEVzG,EAAK,EAEND,EAAK,EAEEqG,EAAQE,GAGZF,EAAQI,GAEVzG,EAAK,EAEHqG,EAAQQ,GAGZR,EAAQM,IAWnBoB,wBAAyB,SAAChE,EAAgBQ,EAAuB9B,EAAQC,QAAR,IAAAD,IAAAA,EAAM,QAAE,IAAAC,IAAAA,EAAM,GAG3E,IAAMyD,EAAcR,EAAiBU,EAAQkB,IAAIhD,IAEjD4B,EAAI1D,GAAKA,EACT0D,EAAIzD,GAAKA,EACTqB,EAAOD,OAAOqC,KCnXtB6B,EAAA,WA8DI,SAAAA,IAEIzM,KAAK0M,eAAiB,IAAIzF,EAC1BjH,KAAK2M,eAAiB,IAAI1F,EAC1BjH,KAAK2J,SAAW,IAAIjD,EAAgB1G,KAAK4M,SAAU5M,KAAM,EAAG,GAC5DA,KAAK+H,MAAQ,IAAIrB,EAAgB1G,KAAK4M,SAAU5M,KAAM,EAAG,GACzDA,KAAKqJ,MAAQ,IAAI3C,EAAgB1G,KAAK4M,SAAU5M,KAAM,EAAG,GACzDA,KAAKyJ,KAAO,IAAI/C,EAAgB1G,KAAK6M,WAAY7M,KAAM,EAAG,GAE1DA,KAAK8M,UAAY,EACjB9M,KAAK+M,IAAM,EACX/M,KAAKgN,IAAM,EACXhN,KAAKiN,IAAM,EACXjN,KAAKkN,IAAM,EACXlN,KAAKmN,SAAW,EAChBnN,KAAKoN,gBAAkB,EAEvBpN,KAAKqN,SAAW,EAChBrN,KAAKsN,UAAY,EA0HzB,OAtHcb,EAAAxM,UAAA2M,SAAV,WAEI5M,KAAKmN,YAICV,EAAAxM,UAAA4M,WAAV,WAEI7M,KAAK+M,IAAMtN,KAAKyI,IAAIlI,KAAK8M,UAAY9M,KAAKyJ,KAAK1J,GAC/CC,KAAKgN,IAAMvN,KAAK0I,IAAInI,KAAK8M,UAAY9M,KAAKyJ,KAAK1J,GAC/CC,KAAKiN,KAAOxN,KAAK0I,IAAInI,KAAK8M,UAAY9M,KAAKyJ,KAAK3J,GAChDE,KAAKkN,IAAMzN,KAAKyI,IAAIlI,KAAK8M,UAAY9M,KAAKyJ,KAAK3J,GAE/CE,KAAKmN,YAgBTV,EAAAxM,UAAAsN,qBAAA,WAEI,IAAMtL,EAAKjC,KAAK2M,eAEZ3M,KAAKmN,WAAanN,KAAKoN,kBAGvBnL,EAAGM,EAAIvC,KAAK+M,IAAM/M,KAAK+H,MAAMjI,EAC7BmC,EAAGQ,EAAIzC,KAAKgN,IAAMhN,KAAK+H,MAAMjI,EAC7BmC,EAAGS,EAAI1C,KAAKiN,IAAMjN,KAAK+H,MAAMhI,EAC7BkC,EAAGO,EAAIxC,KAAKkN,IAAMlN,KAAK+H,MAAMhI,EAE7BkC,EAAGiF,GAAKlH,KAAK2J,SAAS7J,GAAME,KAAKqJ,MAAMvJ,EAAImC,EAAGM,EAAMvC,KAAKqJ,MAAMtJ,EAAIkC,EAAGS,GACtET,EAAGkF,GAAKnH,KAAK2J,SAAS5J,GAAMC,KAAKqJ,MAAMvJ,EAAImC,EAAGQ,EAAMzC,KAAKqJ,MAAMtJ,EAAIkC,EAAGO,GACtExC,KAAKoN,gBAAkBpN,KAAKmN,SAG5BnN,KAAKsN,WAAa,IAQ1Bb,EAAexM,UAAAuN,gBAAf,SAAgBC,GAEZ,IAAMxL,EAAKjC,KAAK2M,eAkBhB,GAhBI3M,KAAKmN,WAAanN,KAAKoN,kBAGvBnL,EAAGM,EAAIvC,KAAK+M,IAAM/M,KAAK+H,MAAMjI,EAC7BmC,EAAGQ,EAAIzC,KAAKgN,IAAMhN,KAAK+H,MAAMjI,EAC7BmC,EAAGS,EAAI1C,KAAKiN,IAAMjN,KAAK+H,MAAMhI,EAC7BkC,EAAGO,EAAIxC,KAAKkN,IAAMlN,KAAK+H,MAAMhI,EAE7BkC,EAAGiF,GAAKlH,KAAK2J,SAAS7J,GAAME,KAAKqJ,MAAMvJ,EAAImC,EAAGM,EAAMvC,KAAKqJ,MAAMtJ,EAAIkC,EAAGS,GACtET,EAAGkF,GAAKnH,KAAK2J,SAAS5J,GAAMC,KAAKqJ,MAAMvJ,EAAImC,EAAGQ,EAAMzC,KAAKqJ,MAAMtJ,EAAIkC,EAAGO,GACtExC,KAAKoN,gBAAkBpN,KAAKmN,SAG5BnN,KAAKsN,WAAa,GAGlBtN,KAAKsN,YAAcG,EAAgBJ,SACvC,CAEI,IAAMK,EAAKD,EAAgBf,eACrBiB,EAAK3N,KAAK0M,eAEhBiB,EAAGpL,EAAKN,EAAGM,EAAImL,EAAGnL,EAAMN,EAAGQ,EAAIiL,EAAGhL,EAClCiL,EAAGlL,EAAKR,EAAGM,EAAImL,EAAGjL,EAAMR,EAAGQ,EAAIiL,EAAGlL,EAClCmL,EAAGjL,EAAKT,EAAGS,EAAIgL,EAAGnL,EAAMN,EAAGO,EAAIkL,EAAGhL,EAClCiL,EAAGnL,EAAKP,EAAGS,EAAIgL,EAAGjL,EAAMR,EAAGO,EAAIkL,EAAGlL,EAClCmL,EAAGzG,GAAMjF,EAAGiF,GAAKwG,EAAGnL,EAAMN,EAAGkF,GAAKuG,EAAGhL,EAAKgL,EAAGxG,GAC7CyG,EAAGxG,GAAMlF,EAAGiF,GAAKwG,EAAGjL,EAAMR,EAAGkF,GAAKuG,EAAGlL,EAAKkL,EAAGvG,GAE7CnH,KAAKsN,UAAYG,EAAgBJ,SAGjCrN,KAAKqN,aAQbZ,EAAaxM,UAAA2N,cAAb,SAAcpF,GAEVA,EAAOY,UAAUpJ,MACjBA,KAAKmN,YAITpM,OAAAC,eAAIyL,EAAQxM,UAAA,WAAA,CAAZgB,IAAA,WAEI,OAAOjB,KAAK8M,WAGhBzM,IAAA,SAAa2G,GAELhH,KAAK8M,YAAc9F,IAEnBhH,KAAK8M,UAAY9F,EACjBhH,KAAK6M,+CApMUJ,EAAAoB,SAAW,IAAIpB,EAuMzCA"}