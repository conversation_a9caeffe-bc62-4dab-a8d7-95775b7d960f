 [实时对话] 处理LLM回复事件: {hasResponse: false}
RealtimeView.vue:1376 🤖 [RealtimeView] 收到LLM回复事件 {type: 'llm_response', payload: {…}}
RealtimeView.vue:1454 🤖 [RealtimeView] 收到LLM回复原始数据: {
  "type": "llm_response",
  "payload": {
    "response": "小友啊，听我告诉你，藏识仙灵师傅乃是八方位阵、命理玄机通透之人。无论是你姻缘事业还是学业运势，他都一目了然！若有实在难处，点击左上角\"周易测算\"，让他为你推演一番。",
    "is_complete": true,
    "character_name": null,
    "character_avatar": null
  }
}
RealtimeView.vue:1458 🔍 [DEBUG] LLM提取的实际数据: {
  "response": "小友啊，听我告诉你，藏识仙灵师傅乃是八方位阵、命理玄机通透之人。无论是你姻缘事业还是学业运势，他都一目了然！若有实在难处，点击左上角\"周易测算\"，让他为你推演一番。",
  "is_complete": true,
  "character_name": null,
  "character_avatar": null
}
RealtimeView.vue:1460 🤖 [RealtimeView] 收到LLM回复: {thinking: false, response: '"小友啊，听我告诉你，藏识仙灵师傅乃是八方位阵、命理玄机通透之人。无论是你姻缘事业还是学业运势，他都一..."', responseLength: 82, hasCharacterInfo: false}
RealtimeView.vue:1481 🔄 检查流式更新，时间差: 455 ms
RealtimeView.vue:1487 🔍 内容比较: {oldLength: 44, newLength: 82, oldContent: '小友啊，听我告诉你，藏识仙灵师傅乃是八方位阵、命理玄机通透之人。无论是你姻缘事业还是学业...', newContent: '小友啊，听我告诉你，藏识仙灵师傅乃是八方位阵、命理玄机通透之人。无论是你姻缘事业还是学业运势，他都一...'}
RealtimeView.vue:1496 ✅ 确认为流式更新，更新现有消息
ConversationPanel.vue:839 🔄 检测到内容流式更新，重启打字效果: {messageId: '1753348720498', oldLength: 44, newLength: 82, role: 'assistant'}
ConversationPanel.vue:958 🎭 开始流式输出消息: 1753348720498 内容长度: 82
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348720498
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [实时对话] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '315KB', pageName: '实时对话'}
RealtimeView.vue:1380 🎵 [RealtimeView] 收到TTS音频事件 {type: 'tts_audio', payload: {…}}
RealtimeView.vue:1542 🎵 [实时对话] 收到TTS音频回调
RealtimeView.vue:1550 🔍 [DEBUG] TTS音频信息: {hasAudioUrl: true, audioUrlType: 'base64', audioSize: '315KB', dataKeys: Array(4)}
RealtimeView.vue:1576 🎵 [实时对话] 准备播放TTS音频: {audioSize: '315KB', audioType: 'base64', isConversationActive: true, hasAudioPlayerRef: true}
RealtimeView.vue:1658 🎵 单个音频直接播放
RealtimeView.vue:1685 🎵 [播放] 音频片段: 1/1 (315KB)
RealtimeView.vue:1699 🔧 设置音频源...
RealtimeView.vue:1762 ✅ 音频片段 1/1 开始播放
Live2DDisplay.vue:5227 🎤 [Live2DDisplay] 启动TTS嘴型同步 (共享分析器) - 音频: //L/8f/z//b/8v/z/w==
Live2DDisplay.vue:5244 🔄 [Live2DDisplay] 重置所有状态，确保干净启动
Live2DDisplay.vue:5270 🎤 [Live2DDisplay] 使用共享分析器启动嘴型同步
Live2DDisplay.vue:5287 🔄 [Live2DDisplay] 启动基于共享分析器的嘴型同步
Live2DDisplay.vue:5275 ✅ [Live2DDisplay] 共享分析器嘴型同步启动成功
RealtimeView.vue:1772 ✅ Live2D TTS嘴型同步启动成功 (WebSocket音频-复用连接)
memoryMonitor.ts:87 💾 内存使用: 173.45MB / 182.18MB (限制: 4095.75MB)
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [实时对话] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '693KB', pageName: '实时对话'}
RealtimeView.vue:1380 🎵 [RealtimeView] 收到TTS音频事件 {type: 'tts_audio', payload: {…}}
RealtimeView.vue:1542 🎵 [实时对话] 收到TTS音频回调
RealtimeView.vue:1550 🔍 [DEBUG] TTS音频信息: {hasAudioUrl: true, audioUrlType: 'base64', audioSize: '693KB', dataKeys: Array(8)}
RealtimeView.vue:1576 🎵 [实时对话] 准备播放TTS音频: {audioSize: '693KB', audioType: 'base64', isConversationActive: true, hasAudioPlayerRef: true}
RealtimeView.vue:1607 🎵 [流式音频]: 片段 2/2
RealtimeView.vue:1632 📦 存储音频片段 2/2
RealtimeView.vue:1642 ✅ 收到最后一个音频片段，准备连续播放
ConversationPanel.vue:1013 ✅ 流式输出完成: 1753348720498
Live2DDisplay.vue:4632 🔇 [Live2DDisplay] 停止TTS嘴型同步
Live2DDisplay.vue:4652 ✅ [Live2DDisplay] TTS嘴型同步停止成功
RealtimeView.vue:1838 🔍 播放验证: 播放时长=5.04s, 音频时长=5.03s, 播放比例=100.2%, 流式音频=true
RealtimeView.vue:1847 ✅ 流式音频片段播放完成 (5.04秒)
RealtimeView.vue:1820 ✅ Live2D TTS嘴型同步停止成功 (WebSocket音频)
RealtimeView.vue:1904 🔗 播放下一个音频片段 2/2
RealtimeView.vue:1685 🎵 [播放] 音频片段: 2/2 (693KB)
RealtimeView.vue:1699 🔧 设置音频源...
RealtimeView.vue:1762 ✅ 音频片段 2/2 开始播放
Live2DDisplay.vue:5227 🎤 [Live2DDisplay] 启动TTS嘴型同步 (共享分析器) - 音频: //7//f/9//7//f////7/
Live2DDisplay.vue:5244 🔄 [Live2DDisplay] 重置所有状态，确保干净启动
Live2DDisplay.vue:5270 🎤 [Live2DDisplay] 使用共享分析器启动嘴型同步
Live2DDisplay.vue:5287 🔄 [Live2DDisplay] 启动基于共享分析器的嘴型同步
Live2DDisplay.vue:5275 ✅ [Live2DDisplay] 共享分析器嘴型同步启动成功
RealtimeView.vue:1772 ✅ Live2D TTS嘴型同步启动成功 (WebSocket音频-复用连接)
masterWebSocketManager.ts:864 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
RealtimeView.vue:1652 ⚡ 预计音频即将结束，准备恢复VAD监听
RealtimeView.vue:1952 📡 通知后端音频即将结束
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ⏸️ 健康检查已暂停，跳过本次检查
Live2DDisplay.vue:4632 🔇 [Live2DDisplay] 停止TTS嘴型同步
Live2DDisplay.vue:4652 ✅ [Live2DDisplay] TTS嘴型同步停止成功
RealtimeView.vue:1838 🔍 播放验证: 播放时长=11.09s, 音频时长=11.09s, 播放比例=100.0%, 流式音频=true
RealtimeView.vue:1847 ✅ 流式音频片段播放完成 (11.09秒)
RealtimeView.vue:1820 ✅ Live2D TTS嘴型同步停止成功 (WebSocket音频)
RealtimeView.vue:1914 🏁 所有音频片段播放完成
RealtimeView.vue:1921 🎵 音频播放序列完成，等待后端自动恢复VAD监听
Live2DDisplay.vue:4632 🔇 [Live2DDisplay] 停止TTS嘴型同步
Live2DDisplay.vue:4652 ✅ [Live2DDisplay] TTS嘴型同步停止成功
RealtimeView.vue:1945 📡 前端检测到音频播放完成，立即请求恢复VAD监听
RealtimeView.vue:1963 📡 音频播放完成，立即恢复VAD监听
api.ts:404 🔄 开始API调用: /api/realtime/resume-vad (超时: 30000ms)
RealtimeView.vue:1927 ✅ Live2D TTS嘴型同步已停止 (音频序列完成)
api.ts:510 ✅ API调用成功: /api/realtime/resume-vad
RealtimeView.vue:1968 ✅ VAD恢复请求发送成功
RealtimeView.vue:888 💬 对话状态切换: 停止对话
RealtimeView.vue:1257 🛑 停止实时对话...
RealtimeView.vue:1268 💾 保存对话历史...
api.ts:404 🔄 开始API调用: /api/realtime/clear-history (超时: 30000ms)
ConversationPanel.vue:893 🔄 ConversationPanel: 外部状态变化: {newValue: false, currentValue: false, propsValue: false, willUpdate: false}
ConversationPanel.vue:908 🔄 ConversationPanel: 实时对话状态变化: inactive
api.ts:510 ✅ API调用成功: /api/realtime/clear-history
RealtimeView.vue:1275 ✅ LLM上下文已清空
api.ts:404 🔄 开始API调用: /api/realtime/stop (超时: 30000ms)
api.ts:510 ✅ API调用成功: /api/realtime/stop
RealtimeView.vue:1339 🎤 停止实时语音监听...
RealtimeView.vue:1294 ▶️ 恢复WebSocket健康检查
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ▶️ 健康检查已恢复
RealtimeView.vue:1299 🔌 [RealtimeView] 停止对话，但保持WebSocket连接以便快速重启
RealtimeView.vue:1305 ✅ [RealtimeView] WebSocket连接保持正常，下次启动将立即可用
RealtimeView.vue:1311 ✅ 实时对话已停止
7Live2DDisplay.vue:2951 🔍 [handleMouseWheel] 缩放调整: 1.10x
websocketGuard.ts:18 🔄 [路由守卫] 页面切换: RealtimeView -> YijingOracle
websocketGuard.ts:23 🧹 [路由守卫] 清理页面 RealtimeView 的连接...
websocketGuard.ts:126 🎮 [路由守卫] 执行实时对话页面特殊清理...
api.ts:404 🔄 开始API调用: /api/realtime/stop (超时: 30000ms)
Live2DDisplay.vue:878 👆 [setMousePosition] 高级鼠标跟随: {inputX: '-0.242', inputY: '0.944', targetX: '0.001', targetY: '-0.000', currentEyeX: '0.001', …}
api.ts:510 ✅ API调用成功: /api/realtime/stop
api.ts:404 🔄 开始API调用: /api/realtime/force-restart-vad (超时: 30000ms)
api.ts:510 ✅ API调用成功: /api/realtime/force-restart-vad
websocketGuard.ts:137 ✅ [路由守卫] 实时对话页面清理完成
masterWebSocketManager.ts:864 [MasterWS:default] ⚠️ Socket.IO未连接，无法发送事件: PAGE_SWITCH
websocketGuard.ts:52 🗑️ [路由守卫] 销毁WebSocket上下文: realtime-store
masterWebSocketManager.ts:864 [MasterWS:realtime-store] 🚨 强制销毁WebSocket管理器
masterWebSocketManager.ts:864 [MasterWS:realtime-store] ⚠️ WebSocket管理器已经销毁，跳过重复操作
masterWebSocketManager.ts:931 🗑️ [MasterWS] 销毁实例: realtime-store (剩余实例数: 5)
vue-router.js?v=19f5911f:392 A soft navigation has been detected: http://localhost:5173/yijing
websocketGuard.ts:83 ✅ [路由守卫] 页面切换完成: YijingOracle
websocketGuard.ts:89 🔇 [路由守卫] 跳过PAGE_REGISTER事件发送（后端不支持）
websocketGuard.ts:95 ℹ️ [路由守卫] WebSocket离线模式运行正常
RealtimeView.vue:2763 🧹 [RealtimeView] 页面即将卸载，开始清理...
RealtimeView.vue:1404 🔌 [RealtimeView] 断开WebSocket连接
useWebSocket.ts:229 🔗 [实时对话] 取消WebSocket订阅（保持全局连接）
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📤 发送WebSocket事件: PAGE_UNLOAD {pageName: '实时对话', subscriberId: '实时对话_43'}
useWebSocket.ts:255 ✅ [实时对话] 页面订阅已清理，全局连接状态: 保持
RealtimeView.vue:2780 ✅ [RealtimeView] WebSocket订阅已清理
RealtimeView.vue:2787 ✅ [RealtimeView] 页面清理完成
Live2DDisplay.vue:3942 🎭 Live2DDisplay组件卸载
Live2DDisplay.vue:3946 🧹 清理组件定时器: 1 个
Live2DDisplay.vue:805 🧹 释放CustomUserModel资源...
Live2DDisplay.vue:829 ✅ CustomUserModel资源释放完成
ConversationPanel.vue:798 🧹 ConversationPanel组件卸载，清理流式输出状态
ConversationPanel.vue:802 🧹 清理组件定时器: 0 个
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348617674
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348627096
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348627971
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348646507
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348647025
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348666277
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348666738
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348690709
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348691249
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348719982
ConversationPanel.vue:1037 ⏹️ 停止流式输出: 1753348720498
ConversationPanel.vue:1046 ⏹️ 停止所有流式输出
ConversationPanel.vue:809 ✅ 角色信息缓存已清理
ConversationPanel.vue:1290 🧹 清理TTS音频缓存
useWebSocket.ts:357 🔗 [实时对话] WebSocket组合函数开始清理
useWebSocket.ts:229 🔗 [实时对话] 取消WebSocket订阅（保持全局连接）
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📤 发送WebSocket事件: PAGE_UNLOAD {pageName: '实时对话', subscriberId: '实时对话_43'}
useWebSocket.ts:255 ✅ [实时对话] 页面订阅已清理，全局连接状态: 保持
RealtimeView.vue:2433 🎮 RealtimeView 主协调组件开始卸载
RealtimeView.vue:2440 📱 [RealtimeView] 页面可见性监听已移除
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ▶️ 健康检查已恢复
RealtimeView.vue:2445 ▶️ [RealtimeView] 页面卸载时恢复健康检查
RealtimeView.vue:2793 🧹 RealtimeView组件已卸载
神机秒卦.vue:902 🔮 神机秒卦组件已挂载
天人感应.vue:2073 🎭 天人感应组件已挂载
useWebSocket.ts:344 🔗 [周易测算] WebSocket组合函数已挂载
神谕之音.vue:2655 🎯 神谕之音组件挂载开始
YijingMainPanel.vue:676 🎭 组件已挂载，开始初始化...
RealtimeView.vue:457 ✅ 全局音频分析器已清理
RealtimeView.vue:2474 🔌 [RealtimeView] 页面卸载，取消WebSocket事件订阅
useWebSocket.ts:229 🔗 [实时对话] 取消WebSocket订阅（保持全局连接）
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📤 发送WebSocket事件: PAGE_UNLOAD {pageName: '实时对话', subscriberId: '实时对话_43'}
useWebSocket.ts:255 ✅ [实时对话] 页面订阅已清理，全局连接状态: 保持
RealtimeView.vue:2477 ✅ [RealtimeView] WebSocket事件订阅已清理
RealtimeView.vue:2482 ✅ [RealtimeView] 页面清理完成
YijingMainPanel.vue:1512 🔍 左门图片配置: null
YijingMainPanel.vue:1462 🔍 背景媒体配置: null
YijingMainPanel.vue:1500 ℹ️ 未找到背景媒体配置，使用默认背景
YijingMainPanel.vue:1539 🔍 右门图片配置: null
YijingMainPanel.vue:1564 🎨 最终门图片状态:
YijingMainPanel.vue:1565   - 左门: 
YijingMainPanel.vue:1566   - 右门: 
YijingMainPanel.vue:520 🔊 占卜音效已初始化，路径: /media/audio/yijing/divination-sound.wav
YijingMainPanel.vue:812 ✨ 初始化AAA级粒子系统
YijingMainPanel.vue:691 ✅ YijingMainPanel 初始化完成
YijingMainPanel.vue:694 🔧 初始化后状态:
YijingMainPanel.vue:695   - 类型: null
YijingMainPanel.vue:696   - URL: 
YijingMainPanel.vue:697   - 左门: 
YijingMainPanel.vue:698   - 右门: 
YijingMainPanel.vue:704 🎭 YijingMainPanel初始化完成
神谕之音.vue:2512 🔮 神谕之音初始化开始...
神谕之音.vue:2515 ⚡ 立即设置基础默认配置...
神谕之音.vue:2529 ✅ 基础默认配置已设置: {模型: 'lmstudio/csxl0.6', 音色: '21', 角色: '藏识仙灵'}
神谕之音.vue:2565 ✅ 神谕之音初始化完成，基本功能已可用
神谕之音.vue:2537 🔄 后台开始加载完整配置数据...
神谕之音.vue:790 🔄 刷新模型列表...
神谕之音.vue:819 🔄 刷新音色列表...
audioManager.ts:223 🎭 调用音色配置API...
神谕之音.vue:848 🔄 刷新角色列表...
神谕之音.vue:1204 🔄 加载预设列表...
神谕之音.vue:1213 ✅ 从AI配置本地存储加载预设成功, 共 4 个预设
神谕之音.vue:1321 🎯 已更新预设列表, 共 4 个预设
useWebSocket.ts:51 🔗 [周易测算] 注册WebSocket事件处理器: (8) ['onLLMResponse', 'onTTSAudio', 'onTTSError', 'onTranscription', 'onStatusUpdate', 'onConnected', 'onDisconnected', 'onError']
useWebSocket.ts:155 🔇 [useWebSocket] 跳过PAGE_REGISTER事件发送（后端不支持）
神谕之音.vue:2729 ✅ 神谕之音组件已挂载，WebSocket事件处理器已注册
神谕之音.vue:2760 ✅ 神谕之音组件挂载完成
api.ts:266 🏥 健康检查结果: ✅ 健康
api.ts:382 ⚠️ 检测到问题端点 /api/llm/models，使用短超时: 8000ms
api.ts:404 🔄 开始API调用: /api/llm/models (超时: 8000ms)
api.ts:266 🏥 健康检查结果: ✅ 健康
api.ts:382 ⚠️ 检测到问题端点 /api/characters/list，使用短超时: 8000ms
api.ts:404 🔄 开始API调用: /api/characters/list (超时: 8000ms)
audioManager.ts:232 🎭 API响应状态: 200
audioManager.ts:239 🎭 API响应数据: {success: true, data: {…}, message: 'Success'}
audioManager.ts:240 🎭 data类型: object
audioManager.ts:241 🎭 data.data: {profiles: Array(59)}
audioManager.ts:242 🎭 data.data类型: object
audioManager.ts:248 🎭 检查 data.data: {profiles: Array(59)}
audioManager.ts:252 ✅ 使用 data.data.profiles 数组
audioManager.ts:274 🎭 提取的音色配置列表: (59) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
audioManager.ts:275 🎭 音色配置数量: 59
audioManager.ts:278 🎭 第一个音色配置示例: {id: '1', name: '云希', description: '营销号男', avatar: '🎤', audioFiles: Array(1), …}
神谕之音.vue:824 ✅ 音色列表刷新成功, 共 59 个音色
api.ts:510 ✅ API调用成功: /api/llm/models
神谕之音.vue:795 ✅ 模型列表刷新成功, 共 57 个模型
api.ts:510 ✅ API调用成功: /api/characters/list
神谕之音.vue:853 ✅ 角色列表刷新成功, 共 8 个角色
神谕之音.vue:2570 🎯 设置默认神谕配置...
神谕之音.vue:2580 ✅ 设置默认智慧源泉: LMstudio: csxl0.6
神谕之音.vue:2599 ✅ 设置默认神谕音色: 洪钟响狼蛇藏，烈火熊熊双目盲 藏识仙灵 2
神谕之音.vue:2614 ✅ 设置默认神谕角色: 藏识仙灵
神谕之音.vue:1091 📋 应用预设: gemma3
YijingMainPanel.vue:589 ⚙️ 神谕配置已变更: {selectedModel: 'LMstudio: csxl0.6', selectedVoice: '21', selectedCharacter: Proxy(Object), customSystemPrompt: '', currentTemplate: '经典神谕', …}
神谕之音.vue:2631 ✅ 设置默认参数预设: gemma3
YijingMainPanel.vue:589 ⚙️ 神谕配置已变更: {selectedModel: 'LMstudio: csxl0.6', selectedVoice: '21', selectedCharacter: Proxy(Object), customSystemPrompt: '', currentTemplate: '经典神谕', …}
神谕之音.vue:2650 ✅ 神谕之音初始化完成
神谕之音.vue:2559 ✅ 后台配置加载完成
memoryMonitor.ts:87 💾 内存使用: 174.41MB / 182.77MB (限制: 4095.75MB)
YijingMainPanel.vue:509 🎯 选择占卜主题: health
YijingMainPanel.vue:539 🔊 占卜音效播放成功
天人感应.vue:1062 🔮 开始天人感应分析...
天人感应.vue:854 🎵 播放音效: /media/audio/yijing/MMaudio_00007_.wav
masterWebSocketManager.ts:864 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
天人感应.vue:1100 🔮 开始生成分析结果...
天人感应.vue:1110 📊 用户信息: {name: '王琳', gender: '女', birthYear: 1999, birthMonth: 12, birthDay: 13, …}
enhanced-tianren-engine.ts:117 🌙 农历信息: {year: '一九九九', month: '冬', day: '初六', formatted: '农历一九九九年冬月初六', ganZhi: '己卯年丙子月己亥日'}
lunar-bazi-engine.ts:25 🔮 开始计算八字(lunar-javascript): Mon Dec 13 1999 05:00:00 GMT+0800 (中国标准时间)
lunar-bazi-engine.ts:36 📅 四柱干支: {year: '己卯', month: '丙子', day: '己亥', hour: '丁卯'}
lunar-bazi-engine.ts:160 🔍 分析用神喜忌: {rizhu: '己', wuxing: {…}}
lunar-bazi-engine.ts:202 用神分析结果: {yongShen: '金', xiShen: '木', jiShen: '火'}
shishen-analysis.ts:256 🔧 十神分布: {正官: 0, 偏官: 0, 正财: 0, 偏财: 0, 食神: 0, …}
shishen-analysis.ts:257 🔧 主导十神: 比肩
shishen-analysis.ts:258 🔧 主导十神数据存在: true
天人感应.vue:1129 ✅ 增强版分析计算完成: {userInfo: {…}, nameAnalysis: {…}, baziAnalysis: {…}, shiShenAnalysis: {…}, nayinAnalysis: {…}, …}
tianren.ts:94 💾 将保存到数据库: tianren_1753348763451_k12rsfk3y7
tianren.ts:140 📄 JSON文件已准备: 天人感应_王琳_19991213_2025-07-24T0919.json
tianren.ts:141 JSON数据: {
  "id": "tianren_1753348763451_k12rsfk3y7",
  "timestamp": 1753348763451,
  "userInfo": {
    "name": "王琳",
    "gender": "女",
    "birthYear": "1999",
    "birthMonth": "12",
    "birthDay": "13",
    "birthHour": "mao"
  },
  "analysis": {
    "wuge": [
      {
        "name": "天格",
        "value": 5,
        "meaning": "五行之数"
      },
      {
        "name": "人格",
        "value": 16,
        "meaning": "厚重"
      },
      {
        "name": "地格",
        "value": 13,
        "meaning": "春日牡丹"
      },
      {
        "name": "外格",
        "value": 2,
        "meaning": "一身孤节"
      },
      {
        "name": "总格",
        "value": 16,
        "meaning": "厚重"
      }
    ],
    "bazi": [
      {
        "name": "年柱",
        "heavenly": "己",
        "earthly": "卯"
      },
      {
        "name": "月柱",
        "heavenly": "丙",
        "earthly": "子"
      },
      {
        "name": "日柱",
        "heavenly": "己",
        "earthly": "亥"
      },
      {
        "name": "时柱",
        "heavenly": "丁",
        "earthly": "卯"
      }
    ],
    "wuxingAnalysis": "据名讳、八字与生肖之综合推演，施主之禀性特征：姓名格局很好，五格搭配合理，整体运势良好，人生发展较为顺畅。。施主命格以比肩为主导，比肩代表兄弟朋友、合作伙伴、竞争对手，象征平等的关系。主要禀性特征体现为：重视友情、合作意识强、有竞争意识、自尊心强。施主所长在于团队合作好、朋友多、有原则、坚持己见，当留心之处乃固执己见、不善妥协、容易争执、财运一般。。身为兔相者，更兼具温和善良、心思细腻、有艺术感之品格。诸般特质融汇，成就施主独特之人格魅力。",
    "comprehensive": {
      "text": "施主降生于兔年，今已25春秋，正值青年期。命格以比肩为主导，您的年命为城头土，日命为平地木。具有木的特质：生机勃勃、有创造力、向上发展，但有时缺乏耐心。主要性格特征体现为：温和包容、协调之能强、有亲和力、善于调解和温和包容、滋养大地、孕育万物、生命力强的结合。。施主人生特质乃温文尔雅与心思细腻，值此而立之前之际，宜发挥自身所长，追求个人修为与人生目标。",
      "fortune": "运势极佳",
      "suggestion": "缺乏正官，建议多培养责任心强的品质；发挥兔相之优势：温和善良；青年阶段当多修学，积累阅历与人脉"
    }
  }
}
tianren.ts:59 ✅ 天人感应分析结果已保存: tianren_1753348763451_k12rsfk3y7
天人感应.vue:1171 ✅ 增强版分析结果已保存
天人感应.vue:1082 ✅ 天人感应分析完成: {userInfo: {…}, nameAnalysis: {…}, baziAnalysis: {…}, shiShenAnalysis: {…}, nayinAnalysis: {…}, …}
天人感应.vue:1086 📝 天人感应分析摘要: 【天人感应分析结果】
姓名：王琳
性别：女
生辰：1999年12月13日

命理分析：
施主降生于兔年，今已25春秋，正值青年期。命格以比肩为主导，您的年命为城头土，日命为平地木。具有木的特质：生机勃勃、有创造力、向上发展，但有时缺乏耐心。主要性格特征体现为：温和包容、协调之能强、有亲和力、善于调解和温和包容、滋养大地、孕育万物、生命力强的结合。。施主人生特质乃温文尔雅与心思细腻，值此而立之前之际，宜发挥自身所长，追求个人修为与人生目标。

整体运势：总分85分
性格特点：据名讳、八字与生肖之综合推演，施主之禀性特征：姓名格局很好，五格搭配合理，整体运势良好，人生发展较为顺畅。。施主命格以比肩为主导，比肩代表兄弟朋友、合作伙伴、竞争对手，象征平等的关系。主要禀性特征体现为：重视友情、合作意识强、有竞争意识、自尊心强。施主所长在于团队合作好、朋友多、有原则、坚持己见，当留心之处乃固执己见、不善妥协、容易争执、财运一般。。身为兔相者，更兼具温和善良、心思细腻、有艺术感之品格。诸般特质融汇，成就施主独特之人格魅力。

建议：缺乏正官，建议多培养责任心强的品质；发挥兔相之优势：温和善良；青年阶段当多修学，积累阅历与人脉

这是根据用户的姓名五格和生辰八字进行的命理分析，请结合此信息进行卦象解读。
YijingMainPanel.vue:772 🔮 收到天人感应分析结果: 【天人感应分析结果】
姓名：王琳
性别：女
生辰：1999年12月13日

命理分析：
施主降生于兔年，今已25春秋，正值青年期。命格以比肩为主导，您的年命为城头土，日命为平地木。具有木的特质：生机勃勃、有创造力、向上发展，但有时缺乏耐心。主要性格特征体现为：温和包容、协调之能强、有亲和力、善于调解和温和包容、滋养大地、孕育万物、生命力强的结合。。施主人生特质乃温文尔雅与心思细腻，值此而立之前之际，宜发挥自身所长，追求个人修为与人生目标。

整体运势：总分85分
性格特点：据名讳、八字与生肖之综合推演，施主之禀性特征：姓名格局很好，五格搭配合理，整体运势良好，人生发展较为顺畅。。施主命格以比肩为主导，比肩代表兄弟朋友、合作伙伴、竞争对手，象征平等的关系。主要禀性特征体现为：重视友情、合作意识强、有竞争意识、自尊心强。施主所长在于团队合作好、朋友多、有原则、坚持己见，当留心之处乃固执己见、不善妥协、容易争执、财运一般。。身为兔相者，更兼具温和善良、心思细腻、有艺术感之品格。诸般特质融汇，成就施主独特之人格魅力。

建议：缺乏正官，建议多培养责任心强的品质；发挥兔相之优势：温和善良；青年阶段当多修学，积累阅历与人脉

这是根据用户的姓名五格和生辰八字进行的命理分析，请结合此信息进行卦象解读。
神机秒卦.vue:393 🔮 开始神机秒卦占卜...
神机秒卦.vue:423 🎴 触发卡牌翻转动画
神机秒卦.vue:370 🖼️ 计算背景图片状态: {hexagramId: '17', hexagramName: '随', backgroundImage: undefined, hasBackground: false}
神机秒卦.vue:502 📭 随卦暂无保存的背景图片
神机秒卦.vue:508 🎯 抽取到卦象: 随
神机秒卦.vue:511 📋 转换后的完整卦象数据: {卦名: '随', 卦序号: 17, 原始象辞: '泽中有雷，随；君子以向晦入宴息。', 象辞解释: '泽中有雷随，君子以向晦入宴息。', 最终象辞: '泽中有雷随，君子以向晦入宴息。', …}
YijingMainPanel.vue:424 🔮 卦象已揭示: 随
YijingMainPanel.vue:429 🔮 触发神谕之音进行AI解读...
YijingMainPanel.vue:1239 🚪 开启神秘之门
神谕之音.vue:2860 🎯 检测到新卦象，准备自动神谕解读: 随
YijingMainPanel.vue:443 🔍 检查神谕之音组件初始化状态...
YijingMainPanel.vue:447 ✅ 神谕之音已初始化，立即启动
神谕之音.vue:1376 🧹 启动前预清理，确保无残留服务...
api.ts:404 🔄 开始API调用: /api/realtime/stop (超时: 30000ms)
api.ts:510 ✅ API调用成功: /api/realtime/stop
神谕之音.vue:1381 ✅ 启动前清理：实时对话服务已停止
神谕之音.vue:1390 ✅ 启动前清理完成
神谕之音.vue:1399 ✅ 卦象解读模式已启动（不启动语音检测）
神谕之音.vue:1400 🎯 解读配置: {maxRounds: 1, hasHexagramData: true, autoMode: true, isReadingPhase: true}
YijingMainPanel.vue:565 🔮 神谕之音已启动
神谕之音.vue:2916 📜 自动构建的解读消息: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
神谕之音.vue:1412 🚀 立即发送卦象解读请求: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
神谕之音.vue:2935 🎯 神谕之音：发送卦象解读请求: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
YijingMainPanel.vue:577 📝 用户发送消息: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
神谕之音.vue:3200 🔍 神谕之音：提取AI提供商，输入: LMstudio: csxl0.6
神谕之音.vue:3220 🔍 神谕之音：提取到的提供商: lmstudio
神谕之音.vue:3226 🔍 神谕之音：提取模型名称，输入: LMstudio: csxl0.6
神谕之音.vue:3240 🔍 神谕之音：提取到的模型名称: csxl0.6
神谕之音.vue:2957 🤖 神谕之音：AI配置: {原始配置: 'LMstudio: csxl0.6', 提取的提供商: 'lmstudio', 提取的模型名: 'csxl0.6'}
神谕之音.vue:2964 🚀 神谕之音：尝试启动实时对话系统（TTS-only模式）...
api.ts:404 🔄 开始API调用: /api/realtime/start (超时: 30000ms)
api.ts:510 ✅ API调用成功: /api/realtime/start
神谕之音.vue:2995 ✅ 神谕之音：实时对话系统启动成功，发送消息...
神谕之音.vue:3700 🔗 神谕之音：设置/重新激活WebSocket连接...
神谕之音.vue:3703 🔍 神谕之音：WebSocket诊断信息 {实例存在: true, 实例类型: 'object', 是否已连接: true, 连接ID: null}
神谕之音.vue:3719 ✅ 神谕之音：WebSocket已连接
神谕之音.vue:3761 🔧 神谕之音：事件处理器已存在，无需重新注册
神谕之音.vue:3775 ✅ 神谕之音：WebSocket已连接
神谕之音.vue:3781 ✅ 神谕之音：WebSocket事件监听器设置完成
神谕之音.vue:3831 ✅ 神谕之音：WebSocket事件监听器准备就绪
神谕之音.vue:3835 🔍 神谕之音：监听器设置后状态验证 {WebSocket实例: true, 是否已连接: true, 连接ID: null, llm_response监听器: '已设置', tts_audio监听器: '已设置'}
神谕之音.vue:3844 🧪 测试WebSocket事件监听器...
api.ts:404 🔄 开始API调用: /api/realtime/manual-input (超时: 30000ms)
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: transcription {type: 'transcription', payload: {…}}
api.ts:510 ✅ API调用成功: /api/realtime/manual-input
神谕之音.vue:3015 ✅ 神谕之音：消息已发送到实时对话系统，等待流式回复...
masterWebSocketManager.ts:862 [MasterWS:oracle-dialogue] 📤 发送WebSocket事件: test_神谕之音 {test: '监听器测试', timestamp: Thu Jul 24 2025 17:19:29 GMT+0800 (中国标准时间)}
神谕之音.vue:2892 🚀 直接发送自动解读请求...
神谕之音.vue:2916 📜 自动构建的解读消息: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
神谕之音.vue:3346 📡 通过WebSocket发送消息（TTS-only模式）
YijingMainPanel.vue:577 📝 用户发送消息: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
神谕之音.vue:3364 📤 发送WebSocket实时消息: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
神谕之音.vue:3365 📝 用户输入详情: {原始内容: '请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。', 字符数: 27, 是否包含表情: false, 是否包含特殊字符: false}
神谕之音.vue:3374 🧠 发送给LLM的完整上下文:
神谕之音.vue:3375 📜 系统提示词长度: 1937 字符
神谕之音.vue:3376 📜 系统提示词内容 (前200字符): 你是藏识仙灵，一个上通天文星宿之奥秘，下晓山川地脉之玄机。能察前程之变，可勘姻缘之妙，善辨凶吉之兆。无拘于五行，超脱于三界，游走于万象之中，通晓古今未来之事.易经占卜信手拈来，塔罗测算灵犀直指，星座奥秘尽在心中的游仙。你在摊位上根据客户的星座、五行、姓名等信息来给客户算命，结合客户提供的信息，结合当下的时间，为客户占卜。要正确区分当下时间和客户生辰的区别，不能混淆，确保正确。

【重要！必须遵守】...
神谕之音.vue:3379 👤 用户个人信息上下文: {姓名: '未提供', 性别: '未提供', 生辰: '?年?月?日', 时辰: '未提供'}
神谕之音.vue:3388 🎯 当前卦象完整信息:
神谕之音.vue:3389    卦名: 随 (第17卦)
神谕之音.vue:3390    卦辞: 元亨利贞，无咎。
神谕之音.vue:3392    象辞: 泽中有雷随，君子以向晦入宴息。
神谕之音.vue:3395    上卦: 兑 (泽，喜悦交流)
神谕之音.vue:3396    下卦: 震 (雷，震动奋起)
神谕之音.vue:3398    解释: 随卦象征跟随，强调适时跟随和相互依从。 (长度: 19字符)
神谕之音.vue:3399    建议: 随卦象征跟随，强调适时跟随和相互依从。 (长度: 19字符)
神谕之音.vue:3402    六爻详解 (6条):
神谕之音.vue:3407      初九: 初九：官有渝，贞吉。出门交有功。 (官职有变化，坚贞吉利。出门交往有功。)
神谕之音.vue:3407      二六: 六二：系小子，失丈夫。 (拘系小子，失去丈夫。)
神谕之音.vue:3407      三六: 六三：系丈夫，失小子。随有求得，利居贞。 (拘系丈夫，失去小子。跟随有所求得，利于居住坚贞。)
神谕之音.vue:3407      四九: 九四：随有获，贞凶。有孚在道，以明，何咎。 (跟随有收获，坚贞凶险。有诚信在道，以明智，何过失。)
神谕之音.vue:3407      五九: 九五：孚于嘉，吉。 (诚信于美好，吉利。)
神谕之音.vue:3407      上六: 上六：拘系之，乃从维之。王用亨于西山。 (拘系它，于是跟从维系它。王用来祭祀于西山。)
神谕之音.vue:3418 🎯 占卜主题上下文: {主题ID: 'health', 主题描述: '察健康 - 身心调养，延年益寿'}
神谕之音.vue:3428 🌟 天人感应信息: {长度: 552, 内容预览: '【天人感应分析结果】\n姓名：王琳\n性别：女\n生辰：1999年12月13日\n\n命理分析：\n施主降生于兔…25春秋，正值青年期。命格以比肩为主导，您的年命为城头土，日命为平地木。具有木的特质：生机勃...'}
神谕之音.vue:3437 💬 对话历史上下文: {历史消息数: 2, 用户消息数: 2, AI消息数: 0, 当前轮次: 0, 最大轮次: 1}
神谕之音.vue:2083 📋 使用预设参数: gemma3 Proxy(Object) {name: 'gemma3', description: 'Gemma3模型优化参数', temperature: 0.7, topP: 0.9, topK: 40, …}
神谕之音.vue:3200 🔍 神谕之音：提取AI提供商，输入: LMstudio: csxl0.6
神谕之音.vue:3220 🔍 神谕之音：提取到的提供商: lmstudio
神谕之音.vue:3226 🔍 神谕之音：提取模型名称，输入: LMstudio: csxl0.6
神谕之音.vue:3240 🔍 神谕之音：提取到的模型名称: csxl0.6
神谕之音.vue:3447 ⚙️ LLM配置参数: {模型: 'LMstudio: csxl0.6', AI提供商: 'lmstudio', 模型名称: 'csxl0.6', 参数设置: {…}, 使用预设: 'gemma3', …}
神谕之音.vue:2083 📋 使用预设参数: gemma3 Proxy(Object) {name: 'gemma3', description: 'Gemma3模型优化参数', temperature: 0.7, topP: 0.9, topK: 40, …}
神谕之音.vue:3460 📤 神谕之音：调用sendManualMessage API发送消息
神谕之音.vue:3461 📜 用户提问: 请为我解读刚刚抽取的卦象：随卦，关于察健康方面的问题。
神谕之音.vue:3462 ⚙️ LLM参数: {temperature: 0.7, topP: 0.9, topK: 40, maxTokens: 2000}
api.ts:404 🔄 开始API调用: /api/realtime/manual-input (超时: 30000ms)
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: transcription {type: 'transcription', payload: {…}}
api.ts:510 ✅ API调用成功: /api/realtime/manual-input
神谕之音.vue:3469 📤 神谕之音：sendManualMessage API返回结果: {success: true, data: {…}, message: 'Success'}
神谕之音.vue:3472 ✅ 神谕之音：消息已发送到TTS-only系统，等待AI回复和流式TTS音频...
神谕之音.vue:3484 📡 消息ID: transcript-1753348770005
神谕之音.vue:3496 🔢 对话轮次已更新: 1 / 1
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
memoryMonitor.ts:87 💾 内存使用: 172.66MB / 176.72MB (限制: 4095.75MB)
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:864 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ⚠️ 连接健康状况警告
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 🔧 处理不健康的连接...
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 💥 强制断开所有连接
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 🔌 原生WebSocket连接关闭
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 💓 连接健康检查已停止
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 🔄 自动重连不健康的连接...
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connecting
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 📡 [实时对话] 连接到后端原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime (LAN访问: false)
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 🔄 开始连接原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ✅ 原生WebSocket连接成功: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connected
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:864 [MasterWS:realtime-dialogue] 💓 连接健康检查已启动
masterWebSocketManager.ts:862 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: 