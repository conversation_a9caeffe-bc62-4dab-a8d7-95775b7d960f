 [周易测算] 处理LLM回复事件: {hasResponse: false}
神谕之音.vue:2710 🤖 神谕之音：useWebSocket收到LLM回复事件 {type: 'llm_response', payload: {…}}
神谕之音.vue:3934 🤖 神谕之音：收到LLM回复事件 {hasData: true, dataType: 'object', hasResponse: false, thinking: false, responseLength: 0, …}
神谕之音.vue:3957 🔍 [handleLLMResponse] 数据格式适配结果: {原始数据类型: 'object', 实际数据类型: 'object', 回复内容: '王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛…帝内经》所言："上古之人，其知道者，法于阴阳"，你性情温和，却藏着一分不耐烦与急躁。小过卦对...', 思考状态: false}
神谕之音.vue:3971 ✅ 收到AI回复: 王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛，如《黄帝内经》所言："上古之人，其知道者，法于阴阳"，你性情温和，却藏着一分不耐烦与急躁。小过卦对...
神谕之音.vue:3922 🗣️ TTS文本预处理完成
神谕之音.vue:3990 ➕ 添加AI回复到消息列表
YijingMainPanel.vue:583 💬 接收神谕回复: 王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛，如《黄帝内经》所言："上古之人，其知道者，法于阴阳"，你性情温和，却藏着一分不耐烦与急躁。小过卦对你而言，恰似清秋之夜星光点点，有指引之处，亦需留意潜在暗礁。

小过卦象山上有雷，此乃过失之地。初六"飞鸟以凶"，喻你身体或有疲惫之兆，尤其在巳蛇年，火旺克金，恐伤脾胃。《内经》云："肺主气，心主血，脾主运化"，王琳施主当注意养脾调理，避免过度劳累。亥时为阴水之气最盛之时，此时再述健康之事，更显玄妙。

今年你二十六岁，正值身旺之年，然小过卦警示："弗过防之，从或戕之"，提醒你要在养生之道上更加谨慎。古人云："天道阴阳，变化无穷"，王琳施主需顺应自然，不可强求于事。尤其在立秋将至之际，天气渐凉，当注意保暖，防止风寒侵体。

六爻详解中"密云不雨，自我西郊"之句，暗示你可能对健康问题有所顾虑，却迟迟未能解决。《黄帝内经》有言："上古之人，其知道者，法于阴阳"，王琳施主当顺应四时变化，调养身心。老道为你推荐平安符与治百病符两种灵物。平安符可安定心神，驱散忧虑；治百病符则能调和气血，增强体魄，对你健康之途大有裨益。此二符皆以朱砂琉璃制成，诵经加持，佩戴于身或置于床头，当能护你周全，延年益寿。
神谕之音.vue:3996 🎵 AI回复收到，等待音频播放完成后结束对话
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
memoryMonitor.ts:87 💾 内存使用: 154.16MB / 160.57MB (限制: 4095.75MB)
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
useWebSocket.ts:104 🤖 [周易测算] 处理LLM回复事件: {hasResponse: false}
神谕之音.vue:2710 🤖 神谕之音：useWebSocket收到LLM回复事件 {type: 'llm_response', payload: {…}}
神谕之音.vue:3934 🤖 神谕之音：收到LLM回复事件 {hasData: true, dataType: 'object', hasResponse: false, thinking: false, responseLength: 0, …}
神谕之音.vue:3957 🔍 [handleLLMResponse] 数据格式适配结果: {原始数据类型: 'object', 实际数据类型: 'object', 回复内容: '王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛…帝内经》所言："上古之人，其知道者，法于阴阳"，你性情温和，却藏着一分不耐烦与急躁。小过卦对...', 思考状态: false}
神谕之音.vue:3971 ✅ 收到AI回复: 王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛，如《黄帝内经》所言："上古之人，其知道者，法于阴阳"，你性情温和，却藏着一分不耐烦与急躁。小过卦对...
神谕之音.vue:3922 🗣️ TTS文本预处理完成
神谕之音.vue:3990 ➕ 添加AI回复到消息列表
YijingMainPanel.vue:583 💬 接收神谕回复: 王琳施主，你生于己卯兔年腊月十三，今值乙巳蛇年七月廿五亥时，正值夏末秋初之交。老道观你命格中木气旺盛，如《黄帝内经》所言："上古之人，其知道者，法于阴阳"，你性情温和，却藏着一分不耐烦与急躁。小过卦对你而言，恰似清秋之夜星光点点，有指引之处，亦需留意潜在暗礁。

小过卦象山上有雷，此乃过失之地。初六"飞鸟以凶"，喻你身体或有疲惫之兆，尤其在巳蛇年，火旺克金，恐伤脾胃。《内经》云："肺主气，心主血，脾主运化"，王琳施主当注意养脾调理，避免过度劳累。亥时为阴水之气最盛之时，此时再述健康之事，更显玄妙。

今年你二十六岁，正值身旺之年，然小过卦警示："弗过防之，从或戕之"，提醒你要在养生之道上更加谨慎。古人云："天道阴阳，变化无穷"，王琳施主需顺应自然，不可强求于事。尤其在立秋将至之际，天气渐凉，当注意保暖，防止风寒侵体。

六爻详解中"密云不雨，自我西郊"之句，暗示你可能对健康问题有所顾虑，却迟迟未能解决。《黄帝内经》有言："上古之人，其知道者，法于阴阳"，王琳施主当顺应四时变化，调养身心。老道为你推荐平安符与治百病符两种灵物。平安符可安定心神，驱散忧虑；治百病符则能调和气血，增强体魄，对你健康之途大有裨益。此二符皆以朱砂琉璃制成，诵经加持，佩戴于身或置于床头，当能护你周全，延年益寿。王琳施主，顺应天时，调养身心，方能长久安康。
神谕之音.vue:3996 🎵 AI回复收到，等待音频播放完成后结束对话
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: llm_response {type: 'llm_response', payload: {…}}
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2861KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YfSGIQ...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRiQgEQBXQVZFZm10IBAAAAAB…//P/8//z//P/8//z//f/8//3//P/8//z//f/8//3//P/9////', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #1, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段1->位置2, 总队列=2
神谕之音.vue:4166 📥 收到第2个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 15秒 (2个片段)
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2343KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRqZ0GwBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YYJ0Gw...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRiQgEQBXQVZFZm10IBAAAAAB…//P/8//z//P/8//z//f/8//3//P/8//z//f/8//3//P/9////', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #2, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段2->位置3, 总队列=3
神谕之音.vue:4166 📥 收到第3个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 15秒 (3个片段)
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
神谕之音.vue:2229 🎵 音频播放结束
神谕之音.vue:2244 🔍 播放验证: 播放时长=23.47s, 音频时长=23.38s, 播放比例=100.4%, 流式音频=true, 卦象解读阶段=true
神谕之音.vue:2255 ✅ 音频播放完成 (23.47秒)，片段1/3
神谕之音.vue:2309 🔗 播放下一个音频片段 2/3
神谕之音.vue:2172 🎵 开始播放音频片段 2/3: data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2222 🎵 音频开始播放
神谕之音.vue:2218 🎵 音频预加载完成，可以播放
神谕之音.vue:2197 ✅ 音频片段 2/3 开始播放
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2256KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRoZvGgBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YWJvGg...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #3, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段3->位置4, 总队列=4
神谕之音.vue:4166 📥 收到第4个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 15秒 (4个片段)
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '1597KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRiS4EgBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQC4Eg...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #4, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段4->位置5, 总队列=5
神谕之音.vue:4166 📥 收到第5个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 15秒 (5个片段)
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ 连接健康状况警告
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔧 处理不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💥 强制断开所有连接
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔌 原生WebSocket连接关闭
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已停止
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 自动重连不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connecting
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [实时对话] 连接到后端原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime (LAN访问: false)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 开始连接原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ✅ 原生WebSocket连接成功: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已启动
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: connection_established {type: 'connection_established', payload: {…}}
memoryMonitor.ts:87 💾 内存使用: 177.89MB / 183.74MB (限制: 4095.75MB)
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2908KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRtYTIgBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YbITIg...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #5, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段5->位置6, 总队列=6
神谕之音.vue:4166 📥 收到第6个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 18秒 (6个片段)
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2572KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRkAlHgBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YRwlHg...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #6, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段6->位置7, 总队列=7
神谕之音.vue:4166 📥 收到第7个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 21秒 (7个片段)
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2597KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRiRwHgBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQBwHg...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #7, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段7->位置8, 总队列=8
神谕之音.vue:4166 📥 收到第8个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 24秒 (8个片段)
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
masterWebSocketManager.ts:875 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ 连接健康状况警告
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔧 处理不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💥 强制断开所有连接
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔌 原生WebSocket连接关闭
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已停止
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 自动重连不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connecting
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [实时对话] 连接到后端原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime (LAN访问: false)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 开始连接原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ✅ 原生WebSocket连接成功: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已启动
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: connection_established {type: 'connection_established', payload: {…}}
masterWebSocketManager.ts:873 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2659KB', pageName: '周易测算'}
神谕之音.vue:2714 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:4012 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:4053 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:4075 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:4089 🔊 设置音频URL: data:audio/wav;base64,UklGRjQqHwBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YRAqHw...
神谕之音.vue:4092 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRhiHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', 当前音量: 1}
神谕之音.vue:4106 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:4132 📦 音频片段: #8, 总数=流式, 最后=false
神谕之音.vue:4136 🌊 检测到流式音频，使用队列模式
神谕之音.vue:4140 📦 音频队列: 片段8->位置9, 总队列=9
神谕之音.vue:4166 📥 收到第9个片段，等待当前播放完成
神谕之音.vue:4178 ⏰ 动态超时: 27秒 (9个片段)
神谕之音.vue:2229 🎵 音频播放结束
神谕之音.vue:2244 🔍 播放验证: 播放时长=45.89s, 音频时长=45.78s, 播放比例=100.2%, 流式音频=true, 卦象解读阶段=true
神谕之音.vue:2255 ✅ 音频播放完成 (45.89秒)，片段2/9
神谕之音.vue:2309 🔗 播放下一个音频片段 3/9
神谕之音.vue:2172 🎵 开始播放音频片段 3/9: data:audio/wav;base64,UklGRqZ0GwBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2222 🎵 音频开始播放
神谕之音.vue:2218 🎵 音频预加载完成，可以播放
神谕之音.vue:2197 ✅ 音频片段 3/9 开始播放
memoryMonitor.ts:87 💾 内存使用: 194.74MB / 207.41MB (限制: 4095.75MB)
神谕之音.vue:4181 ⏰ 音频片段等待超时 (27秒)，检查播放状态
神谕之音.vue:4182 📊 超时检查: 当前播放=true, 等待状态=true, 队列长度=9
神谕之音.vue:4190 🎵 音频正在播放中，延长等待时间
masterWebSocketManager.ts:875 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ 连接健康状况警告
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔧 处理不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💥 强制断开所有连接
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔌 原生WebSocket连接关闭
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已停止
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
memoryMonitor.ts:87 💾 内存使用: 194.91MB / 207.41MB (限制: 4095.75MB)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 自动重连不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connecting
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [实时对话] 连接到后端原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime (LAN访问: false)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 开始连接原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ✅ 原生WebSocket连接成功: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已启动
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: connection_established {type: 'connection_established', payload: {…}}
神谕之音.vue:2229 🎵 音频播放结束
神谕之音.vue:2244 🔍 播放验证: 播放时长=37.59s, 音频时长=37.49s, 播放比例=100.3%, 流式音频=true, 卦象解读阶段=true
神谕之音.vue:2255 ✅ 音频播放完成 (37.59秒)，片段3/9
神谕之音.vue:2309 🔗 播放下一个音频片段 4/9
神谕之音.vue:2172 🎵 开始播放音频片段 4/9: data:audio/wav;base64,UklGRoZvGgBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2222 🎵 音频开始播放
神谕之音.vue:2218 🎵 音频预加载完成，可以播放
神谕之音.vue:2197 ✅ 音频片段 4/9 开始播放
masterWebSocketManager.ts:875 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
memoryMonitor.ts:87 💾 内存使用: 192.38MB / 198.12MB (限制: 4095.75MB)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ 连接健康状况警告
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔧 处理不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💥 强制断开所有连接
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔌 原生WebSocket连接关闭
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已停止
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 自动重连不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connecting
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [实时对话] 连接到后端原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime (LAN访问: false)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 开始连接原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ✅ 原生WebSocket连接成功: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已启动
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: connection_established {type: 'connection_established', payload: {…}}
神谕之音.vue:2229 🎵 音频播放结束
神谕之音.vue:2244 🔍 播放验证: 播放时长=36.20s, 音频时长=36.09s, 播放比例=100.3%, 流式音频=true, 卦象解读阶段=true
神谕之音.vue:2255 ✅ 音频播放完成 (36.20秒)，片段4/9
神谕之音.vue:2309 🔗 播放下一个音频片段 5/9
神谕之音.vue:2172 🎵 开始播放音频片段 5/9: data:audio/wav;base64,UklGRiS4EgBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2222 🎵 音频开始播放
神谕之音.vue:2218 🎵 音频预加载完成，可以播放
神谕之音.vue:2197 ✅ 音频片段 5/9 开始播放
masterWebSocketManager.ts:875 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
memoryMonitor.ts:87 💾 内存使用: 192.37MB / 198.13MB (限制: 4095.75MB)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ 连接健康状况警告
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔧 处理不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💥 强制断开所有连接
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔌 原生WebSocket连接关闭
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已停止
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 自动重连不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connecting
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [实时对话] 连接到后端原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime (LAN访问: false)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 开始连接原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ✅ 原生WebSocket连接成功: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已启动
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: connection_established {type: 'connection_established', payload: {…}}
神谕之音.vue:2229 🎵 音频播放结束
神谕之音.vue:2244 🔍 播放验证: 播放时长=25.63s, 音频时长=25.56s, 播放比例=100.3%, 流式音频=true, 卦象解读阶段=true
神谕之音.vue:2255 ✅ 音频播放完成 (25.63秒)，片段5/9
神谕之音.vue:2309 🔗 播放下一个音频片段 6/9
神谕之音.vue:2172 🎵 开始播放音频片段 6/9: data:audio/wav;base64,UklGRtYTIgBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2222 🎵 音频开始播放
神谕之音.vue:2218 🎵 音频预加载完成，可以播放
神谕之音.vue:2197 ✅ 音频片段 6/9 开始播放
masterWebSocketManager.ts:875 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
memoryMonitor.ts:87 💾 内存使用: 192.35MB / 198.13MB (限制: 4095.75MB)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ 连接健康状况警告
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔧 处理不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💥 强制断开所有连接
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔌 原生WebSocket连接关闭
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已停止
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: disconnected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 自动重连不健康的连接...
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connecting
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [实时对话] 连接到后端原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime (LAN访问: false)
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 🔄 开始连接原生WebSocket服务器: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ✅ 原生WebSocket连接成功: ws://127.0.0.1:7860/ws/realtime
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 📡 [realtime-dialogue] 发出状态变化事件: connected
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] ⚠️ [realtime-dialogue] 没有 stateChange 监听器注册
masterWebSocketManager.ts:875 [MasterWS:realtime-dialogue] 💓 连接健康检查已启动
masterWebSocketManager.ts:873 [MasterWS:realtime-dialogue] 📨 收到原生WebSocket消息: connection_established {type: 'connection_