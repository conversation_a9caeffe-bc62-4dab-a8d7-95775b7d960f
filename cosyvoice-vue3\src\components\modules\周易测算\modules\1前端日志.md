 [周易测算] 处理LLM回复事件: {hasResponse: false}
神谕之音.vue:2606 🤖 神谕之音：useWebSocket收到LLM回复事件 {type: 'llm_response', payload: {…}}
神谕之音.vue:3797 🤖 神谕之音：收到LLM回复事件 {hasData: true, dataType: 'object', hasResponse: false, thinking: false, responseLength: 0, …}
神谕之音.vue:3820 🔍 [handleLLMResponse] 数据格式适配结果: {原始数据类型: 'object', 实际数据类型: 'object', 回复内容: '王琳施主啊，你生于己卯兔年、辛丑牛月、丁酉鸡日，命局中金木交织，水气暗藏，土为主导。《易经》有云："…匪我求童蒙，童蒙求我。"此卦正合你意。蒙卦形象如山下泉涌，象征启蒙之时，正是学习探索的黄金岁...', 思考状态: false}
神谕之音.vue:3834 ✅ 收到AI回复: 王琳施主啊，你生于己卯兔年、辛丑牛月、丁酉鸡日，命局中金木交织，水气暗藏，土为主导。《易经》有云："蒙，亨，匪我求童蒙，童蒙求我。"此卦正合你意。蒙卦形象如山下泉涌，象征启蒙之时，正是学习探索的黄金岁...
神谕之音.vue:3785 🗣️ TTS文本预处理完成
神谕之音.vue:3853 ➕ 添加AI回复到消息列表
YijingMainPanel.vue:583 💬 接收神谕回复: 王琳施主啊，你生于己卯兔年、辛丑牛月、丁酉鸡日，命局中金木交织，水气暗藏，土为主导。《易经》有云："蒙，亨，匪我求童蒙，童蒙求我。"此卦正合你意。蒙卦形象如山下泉涌，象征启蒙之时，正是学习探索的黄金岁月。今年乙巳蛇年，蛇为火，与木相克，然未时金旺，恰是助力学业之时。

老道观你命格中比肩透出，聪明伶俐却缺乏耐心，正如《大学》所言："人不可不思善恶。"蒙卦上坎下艮，暗示学习之途需循序渐进，切莫急功近利。初六"发蒙"，此为刚开始之时，宜放下骄傲自满之心，虚怀若谷。《礼记·学记》云："玉不琢，不成器；人不教，不知道。"施主今年二十五岁，正值青年，当以谦逊求知为本。

九二"包蒙吉"，此是包容与理解之时，学习过程中或有挫折，需见贤思齐，方能进步。六三"勿用取女；见金夫"，暗示不可因情感所困扰学业，专注才是关键。《论语》云："知之者不如好之者，好之者不如乐之者。"若能以快乐心境学习，必有所成。

难处在四六"困蒙"，或有知识困顿之时，宜多向师长请教，不可自暴自弃。五六"童蒙，吉"，提醒你当如孩童般求知若昧，方可破除迷障。《孟子·告子上》曰："彼既为之，而我独不为也？是其所为乎？非其所为也。"不必拘泥于形式，关键在于真正理解。上九"击蒙"，则警示不可因一朝得志而骄傲自满，仍需不断精进。

老道观你命中木旺金衰，今年火蛇相生，学习虽有助力，但仍需加倍努力。《黄帝内经》曰："上古之人，其知道者，法于阴阳"，调和身心，方能学业有成。尤其在未时此段，金气当令，最宜专注思考与规划未来学业方向。

王琳施主啊，老道为你推荐平安符与转运符两种朱砂琉璃符箓。平安符可安定心神，助你化解学习过程中的焦虑；转运符则能转化不良运势，使学业之路更为顺畅。此二符皆经老道诵经加持，佩之可助你学业有成，金榜题名。
神谕之音.vue:3859 🎵 AI回复收到，等待音频播放完成后结束对话
masterWebSocketManager.ts:824 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
masterWebSocketManager.ts:822 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '1452KB', pageName: '周易测算'}
神谕之音.vue:2610 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:3875 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:3910 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:3932 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:3946 🔊 设置音频URL: data:audio/wav;base64,UklGRigEEQBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQQEEQ...
神谕之音.vue:3949 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 0, 当前src: '', 当前音量: 1}
神谕之音.vue:3960 🧪 神谕之音：立即播放音频（无其他音频）
神谕之音.vue:2427 🧪 开始直接音频播放测试
神谕之音.vue:2158 🎵 开始播放音频片段 1/1: data:audio/wav;base64,UklGRigEEQBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2162 🎵 神谕之音：音频开始播放，立即暂停VAD监听
神谕之音.vue:2378 🔇 神谕之音：暂停VAD监听用于音频播放
神谕之音.vue:3989 📦 音频片段: #0, 总数=流式, 最后=false
神谕之音.vue:3993 🌊 检测到流式音频，使用队列模式
神谕之音.vue:3997 📦 音频队列: 片段0->位置1, 总队列=1
神谕之音.vue:4035 ⏰ 动态超时: 15秒 (1个片段)
神谕之音.vue:4102 🎵 为第一个音频片段设置事件监听器
神谕之音.vue:4116 ✅ 音频事件监听器已设置（首个片段）
神谕之音.vue:2210 🎵 音频开始播放
神谕之音.vue:2206 🎵 音频预加载完成，可以播放
神谕之音.vue:2185 ✅ 音频片段 1/1 开始播放
masterWebSocketManager.ts:822 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2818KB', pageName: '周易测算'}
神谕之音.vue:2610 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:3875 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:3910 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:3932 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:3946 🔊 设置音频URL: data:audio/wav;base64,UklGRlgHIQBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YTQHIQ...
神谕之音.vue:3949 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRigEEQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', 当前音量: 1}
神谕之音.vue:3963 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:3989 📦 音频片段: #1, 总数=流式, 最后=false
神谕之音.vue:3993 🌊 检测到流式音频，使用队列模式
神谕之音.vue:3997 📦 音频队列: 片段1->位置2, 总队列=2
神谕之音.vue:4023 📥 收到第2个片段，等待当前播放完成
神谕之音.vue:4035 ⏰ 动态超时: 15秒 (2个片段)
masterWebSocketManager.ts:822 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2708KB', pageName: '周易测算'}
神谕之音.vue:2610 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:3875 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:3910 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:3932 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:3946 🔊 设置音频URL: data:audio/wav;base64,UklGRgy9HwBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0Yei8Hw...
神谕之音.vue:3949 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRigEEQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', 当前音量: 1}
神谕之音.vue:3963 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:3989 📦 音频片段: #2, 总数=流式, 最后=false
神谕之音.vue:3993 🌊 检测到流式音频，使用队列模式
神谕之音.vue:3997 📦 音频队列: 片段2->位置3, 总队列=3
神谕之音.vue:4023 📥 收到第3个片段，等待当前播放完成
神谕之音.vue:4035 ⏰ 动态超时: 15秒 (3个片段)
memoryMonitor.ts:87 💾 内存使用: 88.11MB / 92.96MB (限制: 4095.75MB)
神谕之音.vue:2216 🎵 音频播放结束
神谕之音.vue:2231 🔍 播放验证: 播放时长=23.32s, 音频时长=23.23s, 播放比例=100.4%, 流式音频=true, 卦象解读阶段=true
神谕之音.vue:2242 ✅ 音频播放完成 (23.32秒)，片段1/3
神谕之音.vue:2296 🔗 播放下一个音频片段 2/3
神谕之音.vue:2158 🎵 开始播放音频片段 2/3: data:audio/wav;base64,UklGRlgHIQBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2210 🎵 音频开始播放
神谕之音.vue:2206 🎵 音频预加载完成，可以播放
神谕之音.vue:2185 ✅ 音频片段 2/3 开始播放
masterWebSocketManager.ts:822 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '3206KB', pageName: '周易测算'}
神谕之音.vue:2610 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:3875 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:3910 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:3932 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:3946 🔊 设置音频URL: data:audio/wav;base64,UklGRi6SJQBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQqSJQ...
神谕之音.vue:3949 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRlgHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==', 当前音量: 1}
神谕之音.vue:3963 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:3989 📦 音频片段: #3, 总数=流式, 最后=false
神谕之音.vue:3993 🌊 检测到流式音频，使用队列模式
神谕之音.vue:3997 📦 音频队列: 片段3->位置4, 总队列=4
神谕之音.vue:4023 📥 收到第4个片段，等待当前播放完成
神谕之音.vue:4035 ⏰ 动态超时: 15秒 (4个片段)
masterWebSocketManager.ts:824 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
masterWebSocketManager.ts:822 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2403KB', pageName: '周易测算'}
神谕之音.vue:2610 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:3875 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:3910 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:3932 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:3946 🔊 设置音频URL: data:audio/wav;base64,UklGRiwqHABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQgqHA...
神谕之音.vue:3949 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRlgHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==', 当前音量: 1}
神谕之音.vue:3963 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:3989 📦 音频片段: #4, 总数=流式, 最后=false
神谕之音.vue:3993 🌊 检测到流式音频，使用队列模式
神谕之音.vue:3997 📦 音频队列: 片段4->位置5, 总队列=5
神谕之音.vue:4023 📥 收到第5个片段，等待当前播放完成
神谕之音.vue:4035 ⏰ 动态超时: 15秒 (5个片段)
masterWebSocketManager.ts:822 [MasterWS:oracle-dialogue] 📨 收到原生WebSocket消息: tts_audio {type: 'tts_audio', payload: {…}}
useWebSocket.ts:117 🎵 [周易测算] 处理TTS音频事件: {hasAudioUrl: true, audioSize: '2446KB', pageName: '周易测算'}
神谕之音.vue:2610 🎵 神谕之音：useWebSocket收到TTS音频事件 {type: 'tts_audio', payload: {…}}
神谕之音.vue:3875 🎵 [神谕之音] 收到TTS音频回调 {hasData: true, dataType: 'object', hasAudioUrl: false, audioUrlLength: 0, isStreaming: false, …}
神谕之音.vue:3910 🔍 神谕之音：TTS音频数据详细分析: {data类型: 'object', data内容: {…}, audioUrl字段: undefined, audio_url字段: undefined, payload字段: {…}, …}
神谕之音.vue:3932 ✅ 从data.payload.audioUrl获取音频URL
神谕之音.vue:3946 🔊 设置音频URL: data:audio/wav;base64,UklGRhyqHABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YfipHA...
神谕之音.vue:3949 🎵 神谕之音：音频播放器状态检查 {音频播放器存在: true, 播放器类型: 'HTMLAudioElement', 播放器状态: 4, 当前src: 'data:audio/wav;base64,UklGRlgHIQBXQVZFZm10IBAAAAAB…AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==', 当前音量: 1}
神谕之音.vue:3963 🔄 神谕之音：有音频正在播放，将当前音频加入队列
神谕之音.vue:3989 📦 音频片段: #5, 总数=流式, 最后=false
神谕之音.vue:3993 🌊 检测到流式音频，使用队列模式
神谕之音.vue:3997 📦 音频队列: 片段5->位置6, 总队列=6
神谕之音.vue:4023 📥 收到第6个片段，等待当前播放完成
神谕之音.vue:4035 ⏰ 动态超时: 18秒 (6个片段)
memoryMonitor.ts:87 💾 内存使用: 98.46MB / 103.33MB (限制: 4095.75MB)
神谕之音.vue:4038 ⏰ 音频片段等待超时 (18秒)，检查播放状态
神谕之音.vue:4039 📊 超时检查: 当前播放=true, 等待状态=true, 队列长度=6
神谕之音.vue:4047 🎵 音频正在播放中，延长等待时间
masterWebSocketManager.ts:824 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
神谕之音.vue:2216 🎵 音频播放结束
神谕之音.vue:2231 🔍 播放验证: 播放时长=45.20s, 音频时长=45.09s, 播放比例=100.2%, 流式音频=true, 卦象解读阶段=true
神谕之音.vue:2242 ✅ 音频播放完成 (45.20秒)，片段2/6
神谕之音.vue:2296 🔗 播放下一个音频片段 3/6
神谕之音.vue:2158 🎵 开始播放音频片段 3/6: data:audio/wav;base64,UklGRgy9HwBXQVZFZm10IBAAAAABAAEAwF0AAI...
神谕之音.vue:2210 🎵 音频开始播放
神谕之音.vue:2206 🎵 音频预加载完成，可以播放
神谕之音.vue:2185 ✅ 音频片段 3/6 开始播放
神谕之音.vue:2338 🎵 神谕之音：音频播放序列完成，立即恢复VAD监听
神谕之音.vue:2339 📊 播放完成统计: 音频片段数=6, 当前片段=2
神谕之音.vue:2390 📡 神谕之音：通知后端音频播放完成，请求恢复VAD监听
神谕之音.vue:2401 📖 卦象解读阶段或WebSocket未连接，跳过VAD恢复
神谕之音.vue:2359 🎵 音频播放完成，检查当前阶段...
神谕之音.vue:2360 📊 当前状态: isInHexagramReadingPhase=true, connectionStatus=connected
神谕之音.vue:2363 📖 卦象解读音频播放完成，结束解读阶段
神谕之音.vue:2367 ✅ 卦象解读流程完成
memoryMonitor.ts:87 💾 内存使用: 96.07MB / 99.94MB (限制: 4095.75MB)
masterWebSocketManager.ts:824 [MasterWS:oracle-dialogue] 🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）
memoryMonitor.ts:87 💾 内存使用: 96.07MB / 99.94MB (限制: 4095.75MB)
神谕之音.vue:2216 🎵 音频播放结束
神谕之音.vue:2231 🔍 播放验证: 播放时长=43.40s, 音频时长=43.33s, 播放比例=100.2%, 流式音频=false, 卦象解读阶段=false
神谕之音.vue:2264 ✅ 音频正常播放完成 (43.40秒)
神谕之音.vue:2338 🎵 神谕之音：音频播放序列完成，立即恢复VAD监听
神谕之音.vue:2339 📊 播放完成统计: 音频片段数=0, 当前片段=0
神谕之音.vue:2390 📡 神谕之音：通知后端音频播放完成，请求恢复VAD监听
api.ts:404 🔄 开始API调用: /api/realtime/resume-vad (超时: 30000ms)
api.ts:510 ✅ API调用成功: /api/realtime/resume-vad
神谕之音.vue:2396 ✅ VAD监听已恢复
神谕之音.vue:2359 🎵 音频播放完成，检查当前阶段...
神谕之音.vue:2360 📊 当前状态: isInHexagramReadingPhase=false, connectionStatus=connected
神谕之音.vue:2369 💬 实时对话音频播放完成，检查是否需要结束对话
神谕之音.vue:3582 📊 对话继续，当前轮次: 0 / 1