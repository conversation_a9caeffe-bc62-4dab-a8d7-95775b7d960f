{"version": 3, "file": "settings.min.js", "sources": ["../../src/adapter.ts", "../../../../node_modules/ismobilejs/esm/isMobile.js", "../../src/utils/isMobile.ts", "../../src/settings.ts", "../../src/utils/maxRecommendedTextures.ts", "../../src/utils/canUploadSameBuffer.ts"], "sourcesContent": ["export type ContextIds = '2d' | 'webgl' | 'experimental-webgl' | 'webgl2';\n\n/**\n * This interface describes all the DOM dependent calls that Pixi makes throughout its codebase\n * Implementations of this interface can be used to make sure <PERSON><PERSON> will work in any environment\n * such as browser, web workers, and node\n */\nexport interface IAdapter\n{\n    /** Returns a canvas object that can be used to create a webgl context. */\n    createCanvas: (width?: number, height?: number) => HTMLCanvasElement;\n    /** Returns a webgl rendering context. */\n    getWebGLRenderingContext: () => typeof WebGLRenderingContext;\n    /** Returns a partial implementation of the browsers window.navigator */\n    getNavigator: () => { userAgent: string };\n    /** Returns the current base URL For browser environments this is either the document.baseURI or window.location.href */\n    getBaseUrl: () => string;\n    fetch: (url: RequestInfo, options?: RequestInit) => Promise<Response>;\n}\n\nexport const BrowserAdapter = {\n    /**\n     * Creates a canvas element of the given size.\n     * This canvas is created using the browser's native canvas element.\n     * @param width - width of the canvas\n     * @param height - height of the canvas\n     */\n    createCanvas: (width: number, height: number): HTMLCanvasElement =>\n    {\n        const canvas = document.createElement('canvas');\n\n        canvas.width = width;\n        canvas.height = height;\n\n        return canvas;\n    },\n    getWebGLRenderingContext: () => WebGLRenderingContext,\n    getNavigator: () => navigator,\n    getBaseUrl: () => (document.baseURI ?? window.location.href),\n    fetch: (url: RequestInfo, options?: RequestInit) => fetch(url, options),\n} as IAdapter;\n", "var appleIphone = /iPhone/i;\nvar appleIpod = /iPod/i;\nvar appleTablet = /iPad/i;\nvar appleUniversal = /\\biOS-universal(?:.+)Mac\\b/i;\nvar androidPhone = /\\bAndroid(?:.+)Mobile\\b/i;\nvar androidTablet = /Android/i;\nvar amazonPhone = /(?:SD4930UR|\\bSilk(?:.+)Mobile\\b)/i;\nvar amazonTablet = /Silk/i;\nvar windowsPhone = /Windows Phone/i;\nvar windowsTablet = /\\bWindows(?:.+)ARM\\b/i;\nvar otherBlackBerry = /BlackBerry/i;\nvar otherBlackBerry10 = /BB10/i;\nvar otherOpera = /Opera Mini/i;\nvar otherChrome = /\\b(CriOS|Chrome)(?:.+)Mobile/i;\nvar otherFirefox = /Mobile(?:.+)Firefox\\b/i;\nvar isAppleTabletOnIos13 = function (navigator) {\n    return (typeof navigator !== 'undefined' &&\n        navigator.platform === 'MacIntel' &&\n        typeof navigator.maxTouchPoints === 'number' &&\n        navigator.maxTouchPoints > 1 &&\n        typeof MSStream === 'undefined');\n};\nfunction createMatch(userAgent) {\n    return function (regex) { return regex.test(userAgent); };\n}\nexport default function isMobile(param) {\n    var nav = {\n        userAgent: '',\n        platform: '',\n        maxTouchPoints: 0\n    };\n    if (!param && typeof navigator !== 'undefined') {\n        nav = {\n            userAgent: navigator.userAgent,\n            platform: navigator.platform,\n            maxTouchPoints: navigator.maxTouchPoints || 0\n        };\n    }\n    else if (typeof param === 'string') {\n        nav.userAgent = param;\n    }\n    else if (param && param.userAgent) {\n        nav = {\n            userAgent: param.userAgent,\n            platform: param.platform,\n            maxTouchPoints: param.maxTouchPoints || 0\n        };\n    }\n    var userAgent = nav.userAgent;\n    var tmp = userAgent.split('[FBAN');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    tmp = userAgent.split('Twitter');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    var match = createMatch(userAgent);\n    var result = {\n        apple: {\n            phone: match(appleIphone) && !match(windowsPhone),\n            ipod: match(appleIpod),\n            tablet: !match(appleIphone) &&\n                (match(appleTablet) || isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone),\n            universal: match(appleUniversal),\n            device: (match(appleIphone) ||\n                match(appleIpod) ||\n                match(appleTablet) ||\n                match(appleUniversal) ||\n                isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone)\n        },\n        amazon: {\n            phone: match(amazonPhone),\n            tablet: !match(amazonPhone) && match(amazonTablet),\n            device: match(amazonPhone) || match(amazonTablet)\n        },\n        android: {\n            phone: (!match(windowsPhone) && match(amazonPhone)) ||\n                (!match(windowsPhone) && match(androidPhone)),\n            tablet: !match(windowsPhone) &&\n                !match(amazonPhone) &&\n                !match(androidPhone) &&\n                (match(amazonTablet) || match(androidTablet)),\n            device: (!match(windowsPhone) &&\n                (match(amazonPhone) ||\n                    match(amazonTablet) ||\n                    match(androidPhone) ||\n                    match(androidTablet))) ||\n                match(/\\bokhttp\\b/i)\n        },\n        windows: {\n            phone: match(windowsPhone),\n            tablet: match(windowsTablet),\n            device: match(windowsPhone) || match(windowsTablet)\n        },\n        other: {\n            blackberry: match(otherBlackBerry),\n            blackberry10: match(otherBlackBerry10),\n            opera: match(otherOpera),\n            firefox: match(otherFirefox),\n            chrome: match(otherChrome),\n            device: match(otherBlackBerry) ||\n                match(otherBlackBerry10) ||\n                match(otherOpera) ||\n                match(otherFirefox) ||\n                match(otherChrome)\n        },\n        any: false,\n        phone: false,\n        tablet: false\n    };\n    result.any =\n        result.apple.device ||\n            result.android.device ||\n            result.windows.device ||\n            result.other.device;\n    result.phone =\n        result.apple.phone || result.android.phone || result.windows.phone;\n    result.tablet =\n        result.apple.tablet || result.android.tablet || result.windows.tablet;\n    return result;\n}\n//# sourceMappingURL=isMobile.js.map", "import isMobileCall from 'ismobilejs';\n\ntype isMobileResult = {\n    apple: {\n        phone: boolean;\n        ipod: boolean;\n        tablet: boolean;\n        universal: boolean;\n        device: boolean;\n    };\n    amazon: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    android: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    windows: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    other: {\n        blackberry: boolean;\n        blackberry10: boolean;\n        opera: boolean;\n        firefox: boolean;\n        chrome: boolean;\n        device: boolean;\n    };\n    phone: boolean;\n    tablet: boolean;\n    any: boolean;\n};\n\nexport const isMobile: isMobileResult = isMobileCall(globalThis.navigator);\n", "import type { ENV } from '@pixi/constants';\nimport { GC_MODES, MIPMAP_MODES, MSAA_QUALITY, PRECISION, SCALE_MODES, WRAP_MODES } from '@pixi/constants';\nimport type { IAdapter } from './adapter';\nimport { BrowserAdapter } from './adapter';\nimport { canUploadSameBuffer } from './utils/canUploadSameBuffer';\nimport { isMobile } from './utils/isMobile';\nimport { maxRecommendedTextures } from './utils/maxRecommendedTextures';\n\nexport interface IRenderOptions\n{\n    view: HTMLCanvasElement;\n    width: number;\n    height: number;\n    autoDensity: boolean;\n    backgroundColor: number;\n    backgroundAlpha: number;\n    useContextAlpha: boolean | 'notMultiplied';\n    clearBeforeRender: boolean;\n    antialias: boolean;\n    preserveDrawingBuffer: boolean;\n}\n\nexport interface ISettings\n{\n    ADAPTER: IAdapter;\n    MIPMAP_TEXTURES: MIPMAP_MODES;\n    ANISOTROPIC_LEVEL: number;\n    RESOLUTION: number;\n    FILTER_RESOLUTION: number;\n    FILTER_MULTISAMPLE: MSAA_QUALITY;\n    SPRITE_MAX_TEXTURES: number;\n    SPRITE_BATCH_SIZE: number;\n    RENDER_OPTIONS: IRenderOptions;\n    GC_MODE: GC_MODES;\n    GC_MAX_IDLE: number;\n    GC_MAX_CHECK_COUNT: number;\n    WRAP_MODE: WRAP_MODES;\n    SCALE_MODE: SCALE_MODES;\n    PRECISION_VERTEX: PRECISION;\n    PRECISION_FRAGMENT: PRECISION;\n    CAN_UPLOAD_SAME_BUFFER: boolean;\n    CREATE_IMAGE_BITMAP: boolean;\n    ROUND_PIXELS: boolean;\n    RETINA_PREFIX?: RegExp;\n    FAIL_IF_MAJOR_PERFORMANCE_CAVEAT?: boolean;\n    UPLOADS_PER_FRAME?: number;\n    SORTABLE_CHILDREN?: boolean;\n    PREFER_ENV?: ENV;\n    STRICT_TEXTURE_CACHE?: boolean;\n    MESH_CANVAS_PADDING?: number;\n    TARGET_FPMS?: number;\n}\n\n/**\n * User's customizable globals for overriding the default PIXI settings, such\n * as a renderer's default resolution, framerate, float precision, etc.\n * @example\n * // Use the native window resolution as the default resolution\n * // will support high-density displays when rendering\n * PIXI.settings.RESOLUTION = window.devicePixelRatio;\n *\n * // Disable interpolation when scaling, will make texture be pixelated\n * PIXI.settings.SCALE_MODE = PIXI.SCALE_MODES.NEAREST;\n * @namespace PIXI.settings\n */\nexport const settings: ISettings = {\n\n    /**\n     * This adapter is used to call methods that are platform dependent.\n     * For example `document.createElement` only runs on the web but fails in node environments.\n     * This allows us to support more platforms by abstracting away specific implementations per platform.\n     *\n     * By default the adapter is set to work in the browser. However you can create your own\n     * by implementing the `IAdapter` interface. See `IAdapter` for more information.\n     * @name ADAPTER\n     * @memberof PIXI.settings\n     * @type {PIXI.IAdapter}\n     * @default PIXI.BrowserAdapter\n     */\n    ADAPTER: BrowserAdapter,\n    /**\n     * If set to true WebGL will attempt make textures mimpaped by default.\n     * Mipmapping will only succeed if the base texture uploaded has power of two dimensions.\n     * @static\n     * @name MIPMAP_TEXTURES\n     * @memberof PIXI.settings\n     * @type {PIXI.MIPMAP_MODES}\n     * @default PIXI.MIPMAP_MODES.POW2\n     */\n    MIPMAP_TEXTURES: MIPMAP_MODES.POW2,\n\n    /**\n     * Default anisotropic filtering level of textures.\n     * Usually from 0 to 16\n     * @static\n     * @name ANISOTROPIC_LEVEL\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 0\n     */\n    ANISOTROPIC_LEVEL: 0,\n\n    /**\n     * Default resolution / device pixel ratio of the renderer.\n     * @static\n     * @name RESOLUTION\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 1\n     */\n    RESOLUTION: 1,\n\n    /**\n     * Default filter resolution.\n     * @static\n     * @name FILTER_RESOLUTION\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 1\n     */\n    FILTER_RESOLUTION: 1,\n\n    /**\n     * Default filter samples.\n     * @static\n     * @name FILTER_MULTISAMPLE\n     * @memberof PIXI.settings\n     * @type {PIXI.MSAA_QUALITY}\n     * @default PIXI.MSAA_QUALITY.NONE\n     */\n    FILTER_MULTISAMPLE: MSAA_QUALITY.NONE,\n\n    /**\n     * The maximum textures that this device supports.\n     * @static\n     * @name SPRITE_MAX_TEXTURES\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 32\n     */\n    SPRITE_MAX_TEXTURES: maxRecommendedTextures(32),\n\n    // TODO: maybe change to SPRITE.BATCH_SIZE: 2000\n    // TODO: maybe add PARTICLE.BATCH_SIZE: 15000\n\n    /**\n     * The default sprite batch size.\n     *\n     * The default aims to balance desktop and mobile devices.\n     * @static\n     * @name SPRITE_BATCH_SIZE\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 4096\n     */\n    SPRITE_BATCH_SIZE: 4096,\n\n    /**\n     * The default render options if none are supplied to {@link PIXI.Renderer}\n     * or {@link PIXI.CanvasRenderer}.\n     * @static\n     * @name RENDER_OPTIONS\n     * @memberof PIXI.settings\n     * @type {object}\n     * @property {boolean} [antialias=false] - {@link PIXI.IRendererOptions.antialias}\n     * @property {boolean} [autoDensity=false] - {@link PIXI.IRendererOptions.autoDensity}\n     * @property {number} [backgroundAlpha=1] - {@link PIXI.IRendererOptions.backgroundAlpha}\n     * @property {number} [backgroundColor=0x000000] - {@link PIXI.IRendererOptions.backgroundColor}\n     * @property {boolean} [clearBeforeRender=true] - {@link PIXI.IRendererOptions.clearBeforeRender}\n     * @property {number} [height=600] - {@link PIXI.IRendererOptions.height}\n     * @property {boolean} [preserveDrawingBuffer=false] - {@link PIXI.IRendererOptions.preserveDrawingBuffer}\n     * @property {boolean|'notMultiplied'} [useContextAlpha=true] - {@link PIXI.IRendererOptions.useContextAlpha}\n     * @property {HTMLCanvasElement} [view=null] - {@link PIXI.IRendererOptions.view}\n     * @property {number} [width=800] - {@link PIXI.IRendererOptions.width}\n     */\n    RENDER_OPTIONS: {\n        view: null,\n        width: 800,\n        height: 600,\n        autoDensity: false,\n        backgroundColor: 0x000000,\n        backgroundAlpha: 1,\n        useContextAlpha: true,\n        clearBeforeRender: true,\n        antialias: false,\n        preserveDrawingBuffer: false,\n    },\n\n    /**\n     * Default Garbage Collection mode.\n     * @static\n     * @name GC_MODE\n     * @memberof PIXI.settings\n     * @type {PIXI.GC_MODES}\n     * @default PIXI.GC_MODES.AUTO\n     */\n    GC_MODE: GC_MODES.AUTO,\n\n    /**\n     * Default Garbage Collection max idle.\n     * @static\n     * @name GC_MAX_IDLE\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 3600\n     */\n    GC_MAX_IDLE: 60 * 60,\n\n    /**\n     * Default Garbage Collection maximum check count.\n     * @static\n     * @name GC_MAX_CHECK_COUNT\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 600\n     */\n    GC_MAX_CHECK_COUNT: 60 * 10,\n\n    /**\n     * Default wrap modes that are supported by pixi.\n     * @static\n     * @name WRAP_MODE\n     * @memberof PIXI.settings\n     * @type {PIXI.WRAP_MODES}\n     * @default PIXI.WRAP_MODES.CLAMP\n     */\n    WRAP_MODE: WRAP_MODES.CLAMP,\n\n    /**\n     * Default scale mode for textures.\n     * @static\n     * @name SCALE_MODE\n     * @memberof PIXI.settings\n     * @type {PIXI.SCALE_MODES}\n     * @default PIXI.SCALE_MODES.LINEAR\n     */\n    SCALE_MODE: SCALE_MODES.LINEAR,\n\n    /**\n     * Default specify float precision in vertex shader.\n     * @static\n     * @name PRECISION_VERTEX\n     * @memberof PIXI.settings\n     * @type {PIXI.PRECISION}\n     * @default PIXI.PRECISION.HIGH\n     */\n    PRECISION_VERTEX: PRECISION.HIGH,\n\n    /**\n     * Default specify float precision in fragment shader.\n     * iOS is best set at highp due to https://github.com/pixijs/pixi.js/issues/3742\n     * @static\n     * @name PRECISION_FRAGMENT\n     * @memberof PIXI.settings\n     * @type {PIXI.PRECISION}\n     * @default PIXI.PRECISION.MEDIUM\n     */\n    PRECISION_FRAGMENT: isMobile.apple.device ? PRECISION.HIGH : PRECISION.MEDIUM,\n\n    /**\n     * Can we upload the same buffer in a single frame?\n     * @static\n     * @name CAN_UPLOAD_SAME_BUFFER\n     * @memberof PIXI.settings\n     * @type {boolean}\n     */\n    CAN_UPLOAD_SAME_BUFFER: canUploadSameBuffer(),\n\n    /**\n     * Enables bitmap creation before image load. This feature is experimental.\n     * @static\n     * @name CREATE_IMAGE_BITMAP\n     * @memberof PIXI.settings\n     * @type {boolean}\n     * @default false\n     */\n    CREATE_IMAGE_BITMAP: false,\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * @static\n     * @constant\n     * @memberof PIXI.settings\n     * @type {boolean}\n     * @default false\n     */\n    ROUND_PIXELS: false,\n};\n", "import { isMobile } from './isMobile';\n\n/**\n * The maximum recommended texture units to use.\n * In theory the bigger the better, and for desktop we'll use as many as we can.\n * But some mobile devices slow down if there is to many branches in the shader.\n * So in practice there seems to be a sweet spot size that varies depending on the device.\n *\n * In v4, all mobile devices were limited to 4 texture units because for this.\n * In v5, we allow all texture units to be used on modern Apple or Android devices.\n * @private\n * @param {number} max\n * @returns {number} The maximum recommended texture units to use.\n */\nexport function maxRecommendedTextures(max: number): number\n{\n    let allowMax = true;\n\n    if (isMobile.tablet || isMobile.phone)\n    {\n        if (isMobile.apple.device)\n        {\n            const match = (navigator.userAgent).match(/OS (\\d+)_(\\d+)?/);\n\n            if (match)\n            {\n                const majorVersion = parseInt(match[1], 10);\n\n                // Limit texture units on devices below iOS 11, which will be older hardware\n                if (majorVersion < 11)\n                {\n                    allowMax = false;\n                }\n            }\n        }\n        if (isMobile.android.device)\n        {\n            const match = (navigator.userAgent).match(/Android\\s([0-9.]*)/);\n\n            if (match)\n            {\n                const majorVersion = parseInt(match[1], 10);\n\n                // Limit texture units on devices below Android 7 (Nougat), which will be older hardware\n                if (majorVersion < 7)\n                {\n                    allowMax = false;\n                }\n            }\n        }\n    }\n\n    return allowMax ? max : 4;\n}\n", "import { isMobile } from './isMobile';\n\n/**\n * Uploading the same buffer multiple times in a single frame can cause performance issues.\n * Apparent on iOS so only check for that at the moment\n * This check may become more complex if this issue pops up elsewhere.\n * @private\n * @returns {boolean} `true` if the same buffer may be uploaded more than once.\n */\nexport function canUploadSameBuffer(): boolean\n{\n    return !isMobile.apple.device;\n}\n"], "names": ["BrowserAdapter", "createCanvas", "width", "height", "canvas", "document", "createElement", "getWebGLRenderingContext", "WebGLRenderingContext", "getNavigator", "navigator", "getBaseUrl", "_a", "baseURI", "window", "location", "href", "fetch", "url", "options", "appleIphone", "appleIpod", "appleTablet", "appleUniversal", "androidPhone", "androidTablet", "amazonPhone", "amazonTablet", "windowsPhone", "windowsTablet", "otherBlackBerry", "otherBlackBerry10", "otherOpera", "otherChrome", "otherFirefox", "isAppleTabletOnIos13", "platform", "maxTouchPoints", "MSStream", "isMobile", "param", "nav", "userAgent", "tmp", "split", "match", "regex", "test", "createMatch", "result", "apple", "phone", "ipod", "tablet", "universal", "device", "amazon", "android", "windows", "other", "blackberry", "blackberry10", "opera", "firefox", "chrome", "any", "isMobileCall", "globalThis", "settings", "ADAPTER", "MIPMAP_TEXTURES", "MIPMAP_MODES", "POW2", "ANISOTROPIC_LEVEL", "RESOLUTION", "FILTER_RESOLUTION", "FILTER_MULTISAMPLE", "MSAA_QUALITY", "NONE", "SPRITE_MAX_TEXTURES", "max", "allowMax", "parseInt", "maxRecommendedTextures", "SPRITE_BATCH_SIZE", "RENDER_OPTIONS", "view", "autoDensity", "backgroundColor", "backgroundAlpha", "useContextAlpha", "clearBeforeRender", "antialias", "preserveDrawingBuffer", "GC_MODE", "GC_MODES", "AUTO", "GC_MAX_IDLE", "GC_MAX_CHECK_COUNT", "WRAP_MODE", "WRAP_MODES", "CLAMP", "SCALE_MODE", "SCALE_MODES", "LINEAR", "PRECISION_VERTEX", "PRECISION", "HIGH", "PRECISION_FRAGMENT", "MEDIUM", "CAN_UPLOAD_SAME_BUFFER", "CREATE_IMAGE_BITMAP", "ROUND_PIXELS"], "mappings": ";;;;;;;qGAoBaA,EAAiB,CAO1BC,aAAc,SAACC,EAAeC,GAE1B,IAAMC,EAASC,SAASC,cAAc,UAKtC,OAHAF,EAAOF,MAAQA,EACfE,EAAOD,OAASA,EAETC,GAEXG,yBAA0B,WAAM,OAAAC,uBAChCC,aAAc,WAAM,OAAAC,WACpBC,WAAY,WAAM,IAAAC,EAAA,OAAqB,QAApBA,EAAAP,SAASQ,eAAW,IAAAD,EAAAA,EAAAE,OAAOC,SAASC,MACvDC,MAAO,SAACC,EAAkBC,GAA0B,OAAAF,MAAMC,EAAKC,KCvC/DC,EAAc,UACdC,EAAY,QACZC,EAAc,QACdC,EAAiB,8BACjBC,EAAe,2BACfC,EAAgB,WAChBC,EAAc,qCACdC,EAAe,QACfC,EAAe,iBACfC,EAAgB,wBAChBC,EAAkB,cAClBC,EAAoB,QACpBC,EAAa,cACbC,EAAc,gCACdC,EAAe,yBACfC,EAAuB,SAAUzB,GACjC,YAA6B,IAAdA,GACY,aAAvBA,EAAU0B,UAC0B,iBAA7B1B,EAAU2B,gBACjB3B,EAAU2B,eAAiB,GACP,oBAAbC,UCkBR,IAAMC,EDbE,SAAkBC,GAC7B,IAAIC,EAAM,CACNC,UAAW,GACXN,SAAU,GACVC,eAAgB,GAEfG,GAA8B,oBAAd9B,UAOK,iBAAV8B,EACZC,EAAIC,UAAYF,EAEXA,GAASA,EAAME,YACpBD,EAAM,CACFC,UAAWF,EAAME,UACjBN,SAAUI,EAAMJ,SAChBC,eAAgBG,EAAMH,gBAAkB,IAb5CI,EAAM,CACFC,UAAWhC,UAAUgC,UACrBN,SAAU1B,UAAU0B,SACpBC,eAAgB3B,UAAU2B,gBAAkB,GAapD,IAAIK,EAAYD,EAAIC,UAChBC,EAAMD,EAAUE,MAAM,cACJ,IAAXD,EAAI,KACXD,EAAYC,EAAI,SAGE,KADtBA,EAAMD,EAAUE,MAAM,YACP,KACXF,EAAYC,EAAI,IAEpB,IAAIE,EAnCR,SAAqBH,GACjB,OAAO,SAAUI,GAAS,OAAOA,EAAMC,KAAKL,IAkChCM,CAAYN,GACpBO,EAAS,CACTC,MAAO,CACHC,MAAON,EAAMzB,KAAiByB,EAAMjB,GACpCwB,KAAMP,EAAMxB,GACZgC,QAASR,EAAMzB,KACVyB,EAAMvB,IAAgBa,EAAqBM,MAC3CI,EAAMjB,GACX0B,UAAWT,EAAMtB,GACjBgC,QAASV,EAAMzB,IACXyB,EAAMxB,IACNwB,EAAMvB,IACNuB,EAAMtB,IACNY,EAAqBM,MACpBI,EAAMjB,IAEf4B,OAAQ,CACJL,MAAON,EAAMnB,GACb2B,QAASR,EAAMnB,IAAgBmB,EAAMlB,GACrC4B,OAAQV,EAAMnB,IAAgBmB,EAAMlB,IAExC8B,QAAS,CACLN,OAASN,EAAMjB,IAAiBiB,EAAMnB,KAChCmB,EAAMjB,IAAiBiB,EAAMrB,GACnC6B,QAASR,EAAMjB,KACViB,EAAMnB,KACNmB,EAAMrB,KACNqB,EAAMlB,IAAiBkB,EAAMpB,IAClC8B,QAAUV,EAAMjB,KACXiB,EAAMnB,IACHmB,EAAMlB,IACNkB,EAAMrB,IACNqB,EAAMpB,KACVoB,EAAM,gBAEda,QAAS,CACLP,MAAON,EAAMjB,GACbyB,OAAQR,EAAMhB,GACd0B,OAAQV,EAAMjB,IAAiBiB,EAAMhB,IAEzC8B,MAAO,CACHC,WAAYf,EAAMf,GAClB+B,aAAchB,EAAMd,GACpB+B,MAAOjB,EAAMb,GACb+B,QAASlB,EAAMX,GACf8B,OAAQnB,EAAMZ,GACdsB,OAAQV,EAAMf,IACVe,EAAMd,IACNc,EAAMb,IACNa,EAAMX,IACNW,EAAMZ,IAEdgC,KAAK,EACLd,OAAO,EACPE,QAAQ,GAWZ,OATAJ,EAAOgB,IACHhB,EAAOC,MAAMK,QACTN,EAAOQ,QAAQF,QACfN,EAAOS,QAAQH,QACfN,EAAOU,MAAMJ,OACrBN,EAAOE,MACHF,EAAOC,MAAMC,OAASF,EAAOQ,QAAQN,OAASF,EAAOS,QAAQP,MACjEF,EAAOI,OACHJ,EAAOC,MAAMG,QAAUJ,EAAOQ,QAAQJ,QAAUJ,EAAOS,QAAQL,OAC5DJ,ECpF6BiB,CAAaC,WAAWzD,WC2BzD,IAAM0D,EAAsB,CAc/BC,QAASrE,EAUTsE,gBAAiBC,EAAYA,aAACC,KAW9BC,kBAAmB,EAUnBC,WAAY,EAUZC,kBAAmB,EAUnBC,mBAAoBC,EAAYA,aAACC,KAUjCC,oBC9HE,SAAiCC,GAEnC,IAAIC,GAAW,EAEf,GAAI1C,EAASc,QAAUd,EAASY,MAChC,CAGQ,IAeMN,EAjBV,GAAIN,EAASW,MAAMK,OAIf,GAFMV,EAASnC,UAAmB,UAAEmC,MAAM,mBAIjBqC,SAASrC,EAAM,GAAI,IAGrB,KAEfoC,GAAW,GAIvB,GAAI1C,EAASkB,QAAQF,OAIjB,GAFMV,EAASnC,UAAmB,UAAEmC,MAAM,sBAIjBqC,SAASrC,EAAM,GAAI,IAGrB,IAEfoC,GAAW,GAM3B,OAAOA,EAAWD,EAAM,EDwFHG,CAAuB,IAe5CC,kBAAmB,KAoBnBC,eAAgB,CACZC,KAAM,KACNpF,MAAO,IACPC,OAAQ,IACRoF,aAAa,EACbC,gBAAiB,EACjBC,gBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmB,EACnBC,WAAW,EACXC,uBAAuB,GAW3BC,QAASC,EAAQA,SAACC,KAUlBC,YAAa,KAUbC,mBAAoB,IAUpBC,UAAWC,EAAUA,WAACC,MAUtBC,WAAYC,EAAWA,YAACC,OAUxBC,iBAAkBC,EAASA,UAACC,KAW5BC,mBAAoBrE,EAASW,MAAMK,OAASmD,EAAAA,UAAUC,KAAOD,EAAAA,UAAUG,OASvEC,wBE/PQvE,EAASW,MAAMK,OFyQvBwD,qBAAqB,EAYrBC,cAAc"}