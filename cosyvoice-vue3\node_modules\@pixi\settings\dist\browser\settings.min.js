/*!
 * @pixi/settings - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/settings is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_settings=function(e,t){"use strict";var i={createCanvas:function(e,t){var i=document.createElement("canvas");return i.width=e,i.height=t,i},getWebGLRenderingContext:function(){return WebGLRenderingContext},getNavigator:function(){return navigator},getBaseUrl:function(){var e;return null!==(e=document.baseURI)&&void 0!==e?e:window.location.href},fetch:function(e,t){return fetch(e,t)}},n=/iPhone/i,r=/iPod/i,o=/iPad/i,a=/\biOS-universal(?:.+)Mac\b/i,d=/\bAndroid(?:.+)Mobile\b/i,s=/Android/i,u=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,l=/Silk/i,c=/Windows Phone/i,p=/\bWindows(?:.+)ARM\b/i,E=/BlackBerry/i,A=/BB10/i,v=/Opera Mini/i,I=/\b(CriOS|Chrome)(?:.+)Mobile/i,h=/Mobile(?:.+)Firefox\b/i,b=function(e){return void 0!==e&&"MacIntel"===e.platform&&"number"==typeof e.maxTouchPoints&&e.maxTouchPoints>1&&"undefined"==typeof MSStream};var f=function(e){var t={userAgent:"",platform:"",maxTouchPoints:0};e||"undefined"==typeof navigator?"string"==typeof e?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0}):t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0};var i=t.userAgent,f=i.split("[FBAN");void 0!==f[1]&&(i=f[0]),void 0!==(f=i.split("Twitter"))[1]&&(i=f[0]);var P=function(e){return function(t){return t.test(e)}}(i),_={apple:{phone:P(n)&&!P(c),ipod:P(r),tablet:!P(n)&&(P(o)||b(t))&&!P(c),universal:P(a),device:(P(n)||P(r)||P(o)||P(a)||b(t))&&!P(c)},amazon:{phone:P(u),tablet:!P(u)&&P(l),device:P(u)||P(l)},android:{phone:!P(c)&&P(u)||!P(c)&&P(d),tablet:!P(c)&&!P(u)&&!P(d)&&(P(l)||P(s)),device:!P(c)&&(P(u)||P(l)||P(d)||P(s))||P(/\bokhttp\b/i)},windows:{phone:P(c),tablet:P(p),device:P(c)||P(p)},other:{blackberry:P(E),blackberry10:P(A),opera:P(v),firefox:P(h),chrome:P(I),device:P(E)||P(A)||P(v)||P(h)||P(I)},any:!1,phone:!1,tablet:!1};return _.any=_.apple.device||_.android.device||_.windows.device||_.other.device,_.phone=_.apple.phone||_.android.phone||_.windows.phone,_.tablet=_.apple.tablet||_.android.tablet||_.windows.tablet,_}(globalThis.navigator);var P={ADAPTER:i,MIPMAP_TEXTURES:t.MIPMAP_MODES.POW2,ANISOTROPIC_LEVEL:0,RESOLUTION:1,FILTER_RESOLUTION:1,FILTER_MULTISAMPLE:t.MSAA_QUALITY.NONE,SPRITE_MAX_TEXTURES:function(e){var t=!0;if(f.tablet||f.phone){var i;if(f.apple.device)if(i=navigator.userAgent.match(/OS (\d+)_(\d+)?/))parseInt(i[1],10)<11&&(t=!1);if(f.android.device)if(i=navigator.userAgent.match(/Android\s([0-9.]*)/))parseInt(i[1],10)<7&&(t=!1)}return t?e:4}(32),SPRITE_BATCH_SIZE:4096,RENDER_OPTIONS:{view:null,width:800,height:600,autoDensity:!1,backgroundColor:0,backgroundAlpha:1,useContextAlpha:!0,clearBeforeRender:!0,antialias:!1,preserveDrawingBuffer:!1},GC_MODE:t.GC_MODES.AUTO,GC_MAX_IDLE:3600,GC_MAX_CHECK_COUNT:600,WRAP_MODE:t.WRAP_MODES.CLAMP,SCALE_MODE:t.SCALE_MODES.LINEAR,PRECISION_VERTEX:t.PRECISION.HIGH,PRECISION_FRAGMENT:f.apple.device?t.PRECISION.HIGH:t.PRECISION.MEDIUM,CAN_UPLOAD_SAME_BUFFER:!f.apple.device,CREATE_IMAGE_BITMAP:!1,ROUND_PIXELS:!1};return e.BrowserAdapter=i,e.isMobile=f,e.settings=P,Object.defineProperty(e,"__esModule",{value:!0}),e}({},PIXI);Object.assign(this.PIXI,_pixi_settings);
//# sourceMappingURL=settings.min.js.map
