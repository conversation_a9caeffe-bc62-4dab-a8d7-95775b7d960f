/*!
 * @pixi/utils - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/utils is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/settings"),t=require("eventemitter3"),r=require("earcut"),n=require("url"),o=require("@pixi/constants");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=i(t),f=i(r),l={parse:n.parse,format:n.format,resolve:n.resolve};function s(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function c(e){return e.split("?")[0].split("#")[0]}var u={toPosix:function(e){return t="\\",r="/",e.replace(new RegExp(t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g"),r);var t,r},isUrl:function(e){return/^https?:/.test(this.toPosix(e))},isDataUrl:function(e){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(e)},hasProtocol:function(e){return/^[^/:]+:\//.test(this.toPosix(e))},getProtocol:function(e){s(e),e=this.toPosix(e);var t="",r=/^file:\/\/\//.exec(e),n=/^[^/:]+:\/\//.exec(e),o=/^[^/:]+:\//.exec(e);if(r||n||o){var i=(null==r?void 0:r[0])||(null==n?void 0:n[0])||(null==o?void 0:o[0]);t=i,e=e.slice(i.length)}return t},toAbsolute:function(t,r,n){if(this.isDataUrl(t))return t;var o=c(this.toPosix(null!=r?r:e.settings.ADAPTER.getBaseUrl())),i=c(this.toPosix(null!=n?n:this.rootname(o)));return s(t),(t=this.toPosix(t)).startsWith("/")?u.join(i,t.slice(1)):this.isAbsolute(t)?t:this.join(o,t)},normalize:function(e){if(s(e=this.toPosix(e)),0===e.length)return".";var t="",r=e.startsWith("/");this.hasProtocol(e)&&(t=this.rootname(e),e=e.slice(t.length));var n=e.endsWith("/");return(e=function(e,t){for(var r,n="",o=0,i=-1,a=0,f=0;f<=e.length;++f){if(f<e.length)r=e.charCodeAt(f);else{if(47===r)break;r=47}if(47===r){if(i===f-1||1===a);else if(i!==f-1&&2===a){if(n.length<2||2!==o||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",o=0):o=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),i=f,a=0;continue}}else if(2===n.length||1===n.length){n="",o=0,i=f,a=0;continue}t&&(n.length>0?n+="/..":n="..",o=2)}else n.length>0?n+="/"+e.slice(i+1,f):n=e.slice(i+1,f),o=f-i-1;i=f,a=0}else 46===r&&-1!==a?++a:a=-1}return n}(e,!1)).length>0&&n&&(e+="/"),r?"/"+e:t+e},isAbsolute:function(e){return s(e),e=this.toPosix(e),!!this.hasProtocol(e)||e.startsWith("/")},join:function(){for(var e,t,r=arguments,n=[],o=0;o<arguments.length;o++)n[o]=r[o];if(0===n.length)return".";for(var i=0;i<n.length;++i){var a=n[i];if(s(a),a.length>0)if(void 0===t)t=a;else{var f=null!==(e=n[i-1])&&void 0!==e?e:"";this.extname(f)?t+="/../"+a:t+="/"+a}}return void 0===t?".":this.normalize(t)},dirname:function(e){if(s(e),0===e.length)return".";for(var t=(e=this.toPosix(e)).charCodeAt(0),r=47===t,n=-1,o=!0,i=this.getProtocol(e),a=e,f=(e=e.slice(i.length)).length-1;f>=1;--f)if(47===(t=e.charCodeAt(f))){if(!o){n=f;break}}else o=!1;return-1===n?r?"/":this.isUrl(a)?i+e:i:r&&1===n?"//":i+e.slice(0,n)},rootname:function(e){s(e);var t="";if(t=(e=this.toPosix(e)).startsWith("/")?"/":this.getProtocol(e),this.isUrl(e)){var r=e.indexOf("/",t.length);(t=-1!==r?e.slice(0,r):e).endsWith("/")||(t+="/")}return t},basename:function(e,t){s(e),t&&s(t),e=this.toPosix(e);var r,n=0,o=-1,i=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var a=t.length-1,f=-1;for(r=e.length-1;r>=0;--r){var l=e.charCodeAt(r);if(47===l){if(!i){n=r+1;break}}else-1===f&&(i=!1,f=r+1),a>=0&&(l===t.charCodeAt(a)?-1==--a&&(o=r):(a=-1,o=f))}return n===o?o=f:-1===o&&(o=e.length),e.slice(n,o)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!i){n=r+1;break}}else-1===o&&(i=!1,o=r+1);return-1===o?"":e.slice(n,o)},extname:function(e){s(e);for(var t=-1,r=0,n=-1,o=!0,i=0,a=(e=this.toPosix(e)).length-1;a>=0;--a){var f=e.charCodeAt(a);if(47!==f)-1===n&&(o=!1,n=a+1),46===f?-1===t?t=a:1!==i&&(i=1):-1!==t&&(i=-1);else if(!o){r=a+1;break}}return-1===t||-1===n||0===i||1===i&&t===n-1&&t===r+1?"":e.slice(t,n)},parse:function(e){s(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var r,n=(e=this.toPosix(e)).charCodeAt(0),o=this.isAbsolute(e);t.root=this.rootname(e),r=o||this.hasProtocol(e)?1:0;for(var i=-1,a=0,f=-1,l=!0,c=e.length-1,u=0;c>=r;--c)if(47!==(n=e.charCodeAt(c)))-1===f&&(l=!1,f=c+1),46===n?-1===i?i=c:1!==u&&(u=1):-1!==i&&(u=-1);else if(!l){a=c+1;break}return-1===i||-1===f||0===u||1===u&&i===f-1&&i===a+1?-1!==f&&(t.base=t.name=0===a&&o?e.slice(1,f):e.slice(a,f)):(0===a&&o?(t.name=e.slice(1,i),t.base=e.slice(1,f)):(t.name=e.slice(a,i),t.base=e.slice(a,f)),t.ext=e.slice(i,f)),t.dir=this.dirname(e),t},sep:"/",delimiter:":"};e.settings.RETINA_PREFIX=/@([0-9\.]+)x/,e.settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT=!1;var d,h=!1;var g={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};var p=function(){for(var e=[],t=[],r=0;r<32;r++)e[r]=r,t[r]=r;e[o.BLEND_MODES.NORMAL_NPM]=o.BLEND_MODES.NORMAL,e[o.BLEND_MODES.ADD_NPM]=o.BLEND_MODES.ADD,e[o.BLEND_MODES.SCREEN_NPM]=o.BLEND_MODES.SCREEN,t[o.BLEND_MODES.NORMAL]=o.BLEND_MODES.NORMAL_NPM,t[o.BLEND_MODES.ADD]=o.BLEND_MODES.ADD_NPM,t[o.BLEND_MODES.SCREEN]=o.BLEND_MODES.SCREEN_NPM;var n=[];return n.push(t),n.push(e),n}();function b(e){if(4===e.BYTES_PER_ELEMENT)return e instanceof Float32Array?"Float32Array":e instanceof Uint32Array?"Uint32Array":"Int32Array";if(2===e.BYTES_PER_ELEMENT){if(e instanceof Uint16Array)return"Uint16Array"}else if(1===e.BYTES_PER_ELEMENT&&e instanceof Uint8Array)return"Uint8Array";return null}var v={Float32Array:Float32Array,Uint32Array:Uint32Array,Int32Array:Int32Array,Uint8Array:Uint8Array};var x=0;var m={};var y=Object.create(null),E=Object.create(null);var A=function(){function t(t,r,n){this.canvas=e.settings.ADAPTER.createCanvas(),this.context=this.canvas.getContext("2d"),this.resolution=n||e.settings.RESOLUTION,this.resize(t,r)}return t.prototype.clear=function(){this.context.setTransform(1,0,0,1,0,0),this.context.clearRect(0,0,this.canvas.width,this.canvas.height)},t.prototype.resize=function(e,t){this.canvas.width=Math.round(e*this.resolution),this.canvas.height=Math.round(t*this.resolution)},t.prototype.destroy=function(){this.context=null,this.canvas=null},Object.defineProperty(t.prototype,"width",{get:function(){return this.canvas.width},set:function(e){this.canvas.width=Math.round(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"height",{get:function(){return this.canvas.height},set:function(e){this.canvas.height=Math.round(e)},enumerable:!1,configurable:!0}),t}();var w,P=/^\s*data:(?:([\w-]+)\/([\w+.-]+))?(?:;charset=([\w-]+))?(?:;(base64))?,(.*)/i;Object.defineProperty(exports,"isMobile",{enumerable:!0,get:function(){return e.isMobile}}),Object.defineProperty(exports,"EventEmitter",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(exports,"earcut",{enumerable:!0,get:function(){return f.default}}),exports.BaseTextureCache=E,exports.CanvasRenderTarget=A,exports.DATA_URI=P,exports.ProgramCache={},exports.TextureCache=y,exports.clearTextureCache=function(){var e;for(e in y)delete y[e];for(e in E)delete E[e]},exports.correctBlendMode=function(e,t){return p[t?1:0][e]},exports.createIndicesForQuads=function(e,t){void 0===t&&(t=null);var r=6*e;if((t=t||new Uint16Array(r)).length!==r)throw new Error("Out buffer length is incorrect, got "+t.length+" and expected "+r);for(var n=0,o=0;n<r;n+=6,o+=4)t[n+0]=o+0,t[n+1]=o+1,t[n+2]=o+2,t[n+3]=o+0,t[n+4]=o+2,t[n+5]=o+3;return t},exports.decomposeDataUri=function(e){var t=P.exec(e);if(t)return{mediaType:t[1]?t[1].toLowerCase():void 0,subType:t[2]?t[2].toLowerCase():void 0,charset:t[3]?t[3].toLowerCase():void 0,encoding:t[4]?t[4].toLowerCase():void 0,data:t[5]}},exports.deprecation=function(e,t,r){if(void 0===r&&(r=3),!m[t]){var n=(new Error).stack;void 0===n?console.warn("PixiJS Deprecation Warning: ",t+"\nDeprecated since v"+e):(n=n.split("\n").splice(r).join("\n"),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",t+"\nDeprecated since v"+e),console.warn(n),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",t+"\nDeprecated since v"+e),console.warn(n))),m[t]=!0}},exports.destroyTextureCache=function(){var e;for(e in y)y[e].destroy();for(e in E)E[e].destroy()},exports.determineCrossOrigin=function(e,t){if(void 0===t&&(t=globalThis.location),0===e.indexOf("data:"))return"";t=t||globalThis.location,w||(w=document.createElement("a")),w.href=e;var r=l.parse(w.href),n=!r.port&&""===t.port||r.port===t.port;return r.hostname===t.hostname&&n&&r.protocol===t.protocol?"":"anonymous"},exports.getBufferType=b,exports.getResolutionOfUrl=function(t,r){var n=e.settings.RETINA_PREFIX.exec(t);return n?parseFloat(n[1]):void 0!==r?r:1},exports.hex2rgb=function(e,t){return void 0===t&&(t=[]),t[0]=(e>>16&255)/255,t[1]=(e>>8&255)/255,t[2]=(255&e)/255,t},exports.hex2string=function(e){var t=e.toString(16);return"#"+(t="000000".substring(0,6-t.length)+t)},exports.interleaveTypedArrays=function(e,t){for(var r=0,n=0,o={},i=0;i<e.length;i++)n+=t[i],r+=e[i].length;var a=new ArrayBuffer(4*r),f=null,l=0;for(i=0;i<e.length;i++){var s=t[i],c=e[i],u=b(c);o[u]||(o[u]=new v[u](a)),f=o[u];for(var d=0;d<c.length;d++){f[(d/s|0)*n+l+d%s]=c[d]}l+=s}return new Float32Array(a)},exports.isPow2=function(e){return!(e&e-1||!e)},exports.isWebGLSupported=function(){return void 0===d&&(d=function(){var t={stencil:!0,failIfMajorPerformanceCaveat:e.settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT};try{if(!e.settings.ADAPTER.getWebGLRenderingContext())return!1;var r=e.settings.ADAPTER.createCanvas(),n=r.getContext("webgl",t)||r.getContext("experimental-webgl",t),o=!(!n||!n.getContextAttributes().stencil);if(n){var i=n.getExtension("WEBGL_lose_context");i&&i.loseContext()}return n=null,o}catch(e){return!1}}()),d},exports.log2=function(e){var t=(e>65535?1:0)<<4,r=((e>>>=t)>255?1:0)<<3;return t|=r,t|=r=((e>>>=r)>15?1:0)<<2,(t|=r=((e>>>=r)>3?1:0)<<1)|(e>>>=r)>>1},exports.nextPow2=function(e){return e+=0===e?1:0,--e,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,(e|=e>>>16)+1},exports.path=u,exports.premultiplyBlendMode=p,exports.premultiplyRgba=function(e,t,r,n){return r=r||new Float32Array(4),n||void 0===n?(r[0]=e[0]*t,r[1]=e[1]*t,r[2]=e[2]*t):(r[0]=e[0],r[1]=e[1],r[2]=e[2]),r[3]=t,r},exports.premultiplyTint=function(e,t){if(1===t)return(255*t<<24)+e;if(0===t)return 0;var r=e>>16&255,n=e>>8&255,o=255&e;return(255*t<<24)+((r=r*t+.5|0)<<16)+((n=n*t+.5|0)<<8)+(o=o*t+.5|0)},exports.premultiplyTintToRgba=function(e,t,r,n){return(r=r||new Float32Array(4))[0]=(e>>16&255)/255,r[1]=(e>>8&255)/255,r[2]=(255&e)/255,(n||void 0===n)&&(r[0]*=t,r[1]*=t,r[2]*=t),r[3]=t,r},exports.removeItems=function(e,t,r){var n,o=e.length;if(!(t>=o||0===r)){var i=o-(r=t+r>o?o-t:r);for(n=t;n<i;++n)e[n]=e[n+r];e.length=i}},exports.rgb2hex=function(e){return(255*e[0]<<16)+(255*e[1]<<8)+(255*e[2]|0)},exports.sayHello=function(t){var r;if(!h){if(e.settings.ADAPTER.getNavigator().userAgent.toLowerCase().indexOf("chrome")>-1){var n=["\n %c %c %c PixiJS 6.5.10 - ✰ "+t+" ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \n\n","background: #ff66a5; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff66a5; background: #030307; padding:5px 0;","background: #ff66a5; padding:5px 0;","background: #ffc3dc; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;"];(r=globalThis.console).log.apply(r,n)}else globalThis.console&&globalThis.console.log("PixiJS 6.5.10 - "+t+" - http://www.pixijs.com/");h=!0}},exports.sign=function(e){return 0===e?0:e<0?-1:1},exports.skipHello=function(){h=!0},exports.string2hex=function(e){return"string"==typeof e&&"#"===(e=g[e.toLowerCase()]||e)[0]&&(e=e.slice(1)),parseInt(e,16)},exports.trimCanvas=function(e){var t,r,n,o=e.width,i=e.height,a=e.getContext("2d",{willReadFrequently:!0}),f=a.getImageData(0,0,o,i).data,l=f.length,s={top:null,left:null,right:null,bottom:null},c=null;for(t=0;t<l;t+=4)0!==f[t+3]&&(r=t/4%o,n=~~(t/4/o),null===s.top&&(s.top=n),(null===s.left||r<s.left)&&(s.left=r),(null===s.right||s.right<r)&&(s.right=r+1),(null===s.bottom||s.bottom<n)&&(s.bottom=n));return null!==s.top&&(o=s.right-s.left,i=s.bottom-s.top+1,c=a.getImageData(s.left,s.top,o,i)),{height:i,width:o,data:c}},exports.uid=function(){return++x},exports.url=l;
//# sourceMappingURL=utils.min.js.map
