{"version": 3, "file": "filter-color-matrix.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/ColorMatrixFilter.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { Filter, defaultFilterVertex } from '@pixi/core';\nimport fragment from './colorMatrix.frag';\n\nimport type { ArrayFixed } from '@pixi/utils';\n\nexport type ColorMatrix = ArrayFixed<number, 20>;\n\n/**\n * The ColorMatrixFilter class lets you apply a 5x4 matrix transformation on the RGBA\n * color and alpha values of every pixel on your displayObject to produce a result\n * with a new set of RGBA color and alpha values. It's pretty powerful!\n *\n * ```js\n *  let colorMatrix = new PIXI.filters.ColorMatrixFilter();\n *  container.filters = [colorMatrix];\n *  colorMatrix.contrast(2);\n * ```\n * <AUTHOR> <<EMAIL>>\n * @memberof PIXI.filters\n */\nexport class ColorMatrixFilter extends Filter\n{\n    public grayscale: (scale: number, multiply: boolean) => void;\n\n    constructor()\n    {\n        const uniforms = {\n            m: new Float32Array([1, 0, 0, 0, 0,\n                0, 1, 0, 0, 0,\n                0, 0, 1, 0, 0,\n                0, 0, 0, 1, 0]),\n            uAlpha: 1,\n        };\n\n        super(defaultFilterVertex, fragment, uniforms);\n\n        this.alpha = 1;\n    }\n\n    /**\n     * Transforms current matrix and set the new one\n     * @param {number[]} matrix - 5x4 matrix\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    private _loadMatrix(matrix: ColorMatrix, multiply = false): void\n    {\n        let newMatrix = matrix;\n\n        if (multiply)\n        {\n            this._multiply(newMatrix, this.uniforms.m, matrix);\n            newMatrix = this._colorMatrix(newMatrix) as any;\n        }\n\n        // set the new matrix\n        this.uniforms.m = newMatrix;\n    }\n\n    /**\n     * Multiplies two mat5's\n     * @private\n     * @param out - 5x4 matrix the receiving matrix\n     * @param a - 5x4 matrix the first operand\n     * @param b - 5x4 matrix the second operand\n     * @returns {number[]} 5x4 matrix\n     */\n    private _multiply(out: ColorMatrix, a: ColorMatrix, b: ColorMatrix): ColorMatrix\n    {\n        // Red Channel\n        out[0] = (a[0] * b[0]) + (a[1] * b[5]) + (a[2] * b[10]) + (a[3] * b[15]);\n        out[1] = (a[0] * b[1]) + (a[1] * b[6]) + (a[2] * b[11]) + (a[3] * b[16]);\n        out[2] = (a[0] * b[2]) + (a[1] * b[7]) + (a[2] * b[12]) + (a[3] * b[17]);\n        out[3] = (a[0] * b[3]) + (a[1] * b[8]) + (a[2] * b[13]) + (a[3] * b[18]);\n        out[4] = (a[0] * b[4]) + (a[1] * b[9]) + (a[2] * b[14]) + (a[3] * b[19]) + a[4];\n\n        // Green Channel\n        out[5] = (a[5] * b[0]) + (a[6] * b[5]) + (a[7] * b[10]) + (a[8] * b[15]);\n        out[6] = (a[5] * b[1]) + (a[6] * b[6]) + (a[7] * b[11]) + (a[8] * b[16]);\n        out[7] = (a[5] * b[2]) + (a[6] * b[7]) + (a[7] * b[12]) + (a[8] * b[17]);\n        out[8] = (a[5] * b[3]) + (a[6] * b[8]) + (a[7] * b[13]) + (a[8] * b[18]);\n        out[9] = (a[5] * b[4]) + (a[6] * b[9]) + (a[7] * b[14]) + (a[8] * b[19]) + a[9];\n\n        // Blue Channel\n        out[10] = (a[10] * b[0]) + (a[11] * b[5]) + (a[12] * b[10]) + (a[13] * b[15]);\n        out[11] = (a[10] * b[1]) + (a[11] * b[6]) + (a[12] * b[11]) + (a[13] * b[16]);\n        out[12] = (a[10] * b[2]) + (a[11] * b[7]) + (a[12] * b[12]) + (a[13] * b[17]);\n        out[13] = (a[10] * b[3]) + (a[11] * b[8]) + (a[12] * b[13]) + (a[13] * b[18]);\n        out[14] = (a[10] * b[4]) + (a[11] * b[9]) + (a[12] * b[14]) + (a[13] * b[19]) + a[14];\n\n        // Alpha Channel\n        out[15] = (a[15] * b[0]) + (a[16] * b[5]) + (a[17] * b[10]) + (a[18] * b[15]);\n        out[16] = (a[15] * b[1]) + (a[16] * b[6]) + (a[17] * b[11]) + (a[18] * b[16]);\n        out[17] = (a[15] * b[2]) + (a[16] * b[7]) + (a[17] * b[12]) + (a[18] * b[17]);\n        out[18] = (a[15] * b[3]) + (a[16] * b[8]) + (a[17] * b[13]) + (a[18] * b[18]);\n        out[19] = (a[15] * b[4]) + (a[16] * b[9]) + (a[17] * b[14]) + (a[18] * b[19]) + a[19];\n\n        return out;\n    }\n\n    /**\n     * Create a Float32 Array and normalize the offset component to 0-1\n     * @param {number[]} matrix - 5x4 matrix\n     * @returns {number[]} 5x4 matrix with all values between 0-1\n     */\n    private _colorMatrix(matrix: ColorMatrix): ColorMatrix\n    {\n        // Create a Float32 Array and normalize the offset component to 0-1\n        const m = new Float32Array(matrix);\n\n        m[4] /= 255;\n        m[9] /= 255;\n        m[14] /= 255;\n        m[19] /= 255;\n\n        return m as any;\n    }\n\n    /**\n     * Adjusts brightness\n     * @param b - value of the brigthness (0-1, where 0 is black)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public brightness(b: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            b, 0, 0, 0, 0,\n            0, b, 0, 0, 0,\n            0, 0, b, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Sets each channel on the diagonal of the color matrix.\n     * This can be used to achieve a tinting effect on Containers similar to the tint field of some\n     * display objects like Sprite, Text, Graphics, and Mesh.\n     * @param color - Color of the tint. This is a hex value.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public tint(color: number, multiply?: boolean): void\n    {\n        const r = (color >> 16) & 0xff;\n        const g = (color >> 8) & 0xff;\n        const b = color & 0xff;\n\n        const matrix: ColorMatrix = [\n            r / 255, 0, 0, 0, 0,\n            0, g / 255, 0, 0, 0,\n            0, 0, b / 255, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the matrices in grey scales\n     * @param scale - value of the grey (0-1, where 0 is black)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public greyscale(scale: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            scale, scale, scale, 0, 0,\n            scale, scale, scale, 0, 0,\n            scale, scale, scale, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the black and white matrice.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public blackAndWhite(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.3, 0.6, 0.1, 0, 0,\n            0.3, 0.6, 0.1, 0, 0,\n            0.3, 0.6, 0.1, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the hue property of the color\n     * @param rotation - in degrees\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public hue(rotation: number, multiply: boolean): void\n    {\n        rotation = (rotation || 0) / 180 * Math.PI;\n\n        const cosR = Math.cos(rotation);\n        const sinR = Math.sin(rotation);\n        const sqrt = Math.sqrt;\n\n        /* a good approximation for hue rotation\n         This matrix is far better than the versions with magic luminance constants\n         formerly used here, but also used in the starling framework (flash) and known from this\n         old part of the internet: quasimondo.com/archives/000565.php\n\n         This new matrix is based on rgb cube rotation in space. Look here for a more descriptive\n         implementation as a shader not a general matrix:\n         https://github.com/evanw/glfx.js/blob/58841c23919bd59787effc0333a4897b43835412/src/filters/adjust/huesaturation.js\n\n         This is the source for the code:\n         see http://stackoverflow.com/questions/8507885/shift-hue-of-an-rgb-color/8510751#8510751\n         */\n\n        const w = 1 / 3;\n        const sqrW = sqrt(w); // weight is\n\n        const a00 = cosR + ((1.0 - cosR) * w);\n        const a01 = (w * (1.0 - cosR)) - (sqrW * sinR);\n        const a02 = (w * (1.0 - cosR)) + (sqrW * sinR);\n\n        const a10 = (w * (1.0 - cosR)) + (sqrW * sinR);\n        const a11 = cosR + (w * (1.0 - cosR));\n        const a12 = (w * (1.0 - cosR)) - (sqrW * sinR);\n\n        const a20 = (w * (1.0 - cosR)) - (sqrW * sinR);\n        const a21 = (w * (1.0 - cosR)) + (sqrW * sinR);\n        const a22 = cosR + (w * (1.0 - cosR));\n\n        const matrix: ColorMatrix = [\n            a00, a01, a02, 0, 0,\n            a10, a11, a12, 0, 0,\n            a20, a21, a22, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the contrast matrix, increase the separation between dark and bright\n     * Increase contrast : shadows darker and highlights brighter\n     * Decrease contrast : bring the shadows up and the highlights down\n     * @param amount - value of the contrast (0-1)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public contrast(amount: number, multiply: boolean): void\n    {\n        const v = (amount || 0) + 1;\n        const o = -0.5 * (v - 1);\n\n        const matrix: ColorMatrix = [\n            v, 0, 0, 0, o,\n            0, v, 0, 0, o,\n            0, 0, v, 0, o,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the saturation matrix, increase the separation between colors\n     * Increase saturation : increase contrast, brightness, and sharpness\n     * @param amount - The saturation amount (0-1)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public saturate(amount = 0, multiply?: boolean): void\n    {\n        const x = (amount * 2 / 3) + 1;\n        const y = ((x - 1) * -0.5);\n\n        const matrix: ColorMatrix = [\n            x, y, y, 0, 0,\n            y, x, y, 0, 0,\n            y, y, x, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /** Desaturate image (remove color) Call the saturate function */\n    public desaturate(): void // eslint-disable-line no-unused-vars\n    {\n        this.saturate(-1);\n    }\n\n    /**\n     * Negative image (inverse of classic rgb matrix)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public negative(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            -1, 0, 0, 1, 0,\n            0, -1, 0, 1, 0,\n            0, 0, -1, 1, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Sepia image\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public sepia(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.393, 0.7689999, 0.18899999, 0, 0,\n            0.349, 0.6859999, 0.16799999, 0, 0,\n            0.272, 0.5339999, 0.13099999, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Color motion picture process invented in 1916 (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public technicolor(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.9125277891456083, -0.8545344976951645, -0.09155508482755585, 0, 11.793603434377337,\n            -0.3087833385928097, 1.7658908555458428, -0.10601743074722245, 0, -70.35205161461398,\n            -0.231103377548616, -0.7501899197440212, 1.847597816108189, 0, 30.950940869491138,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Polaroid filter\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public polaroid(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.438, -0.062, -0.062, 0, 0,\n            -0.122, 1.378, -0.122, 0, 0,\n            -0.016, -0.016, 1.483, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Filter who transforms : Red -> Blue and Blue -> Red\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public toBGR(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0, 0, 1, 0, 0,\n            0, 1, 0, 0, 0,\n            1, 0, 0, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Color reversal film introduced by Eastman Kodak in 1935. (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public kodachrome(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.1285582396593525, -0.3967382283601348, -0.03992559172921793, 0, 63.72958762196502,\n            -0.16404339962244616, 1.0835251566291304, -0.05498805115633132, 0, 24.732407896706203,\n            -0.16786010706155763, -0.5603416277695248, 1.6014850761964943, 0, 35.62982807460946,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Brown delicious browni filter (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public browni(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.5997023498159715, 0.34553243048391263, -0.2708298674538042, 0, 47.43192855600873,\n            -0.037703249837783157, 0.8609577587992641, 0.15059552388459913, 0, -36.96841498319127,\n            0.24113635128153335, -0.07441037908422492, 0.44972182064877153, 0, -7.562075277591283,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Vintage filter (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public vintage(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.6279345635605994, 0.3202183420819367, -0.03965408211312453, 0, 9.651285835294123,\n            0.02578397704808868, 0.6441188644374771, 0.03259127616149294, 0, 7.462829176470591,\n            0.0466055556782719, -0.0851232987247891, 0.5241648018700465, 0, 5.159190588235296,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * We don't know exactly what it does, kind of gradient map, but funny to play with!\n     * @param desaturation - Tone values.\n     * @param toned - Tone values.\n     * @param lightColor - Tone values, example: `0xFFE580`\n     * @param darkColor - Tone values, example: `0xFFE580`\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public colorTone(desaturation: number, toned: number, lightColor: number, darkColor: number, multiply: boolean): void\n    {\n        desaturation = desaturation || 0.2;\n        toned = toned || 0.15;\n        lightColor = lightColor || 0xFFE580;\n        darkColor = darkColor || 0x338000;\n\n        const lR = ((lightColor >> 16) & 0xFF) / 255;\n        const lG = ((lightColor >> 8) & 0xFF) / 255;\n        const lB = (lightColor & 0xFF) / 255;\n\n        const dR = ((darkColor >> 16) & 0xFF) / 255;\n        const dG = ((darkColor >> 8) & 0xFF) / 255;\n        const dB = (darkColor & 0xFF) / 255;\n\n        const matrix: ColorMatrix = [\n            0.3, 0.59, 0.11, 0, 0,\n            lR, lG, lB, desaturation, 0,\n            dR, dG, dB, toned, 0,\n            lR - dR, lG - dG, lB - dB, 0, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Night effect\n     * @param intensity - The intensity of the night effect.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public night(intensity: number, multiply: boolean): void\n    {\n        intensity = intensity || 0.1;\n\n        const matrix: ColorMatrix = [\n            intensity * (-2.0), -intensity, 0, 0, 0,\n            -intensity, 0, intensity, 0, 0,\n            0, intensity, intensity * 2.0, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Predator effect\n     *\n     * Erase the current matrix by setting a new indepent one\n     * @param amount - how much the predator feels his future victim\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public predator(amount: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            // row 1\n            11.224130630493164 * amount,\n            -4.794486999511719 * amount,\n            -2.8746118545532227 * amount,\n            0 * amount,\n            0.40342438220977783 * amount,\n            // row 2\n            -3.6330697536468506 * amount,\n            9.193157196044922 * amount,\n            -2.951810836791992 * amount,\n            0 * amount,\n            -1.316135048866272 * amount,\n            // row 3\n            -3.2184197902679443 * amount,\n            -4.2375030517578125 * amount,\n            7.476448059082031 * amount,\n            0 * amount,\n            0.8044459223747253 * amount,\n            // row 4\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * LSD effect\n     *\n     * Multiply the current matrix\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public lsd(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            2, -0.4, 0.5, 0, 0,\n            -0.5, 2, -0.4, 0, 0,\n            -0.4, -0.5, 3, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /** Erase the current matrix by setting the default one. */\n    public reset(): void\n    {\n        const matrix: ColorMatrix = [\n            1, 0, 0, 0, 0,\n            0, 1, 0, 0, 0,\n            0, 0, 1, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, false);\n    }\n\n    /**\n     * The matrix of the color matrix filter\n     * @member {number[]}\n     * @default [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0]\n     */\n    get matrix(): ColorMatrix\n    {\n        return this.uniforms.m;\n    }\n\n    set matrix(value: ColorMatrix)\n    {\n        this.uniforms.m = value;\n    }\n\n    /**\n     * The opacity value to use when mixing the original and resultant colors.\n     *\n     * When the value is 0, the original color is used without modification.\n     * When the value is 1, the result color is used.\n     * When in the range (0, 1) the color is interpolated between the original and result by this amount.\n     * @default 1\n     */\n    get alpha(): number\n    {\n        return this.uniforms.uAlpha;\n    }\n\n    set alpha(value: number)\n    {\n        this.uniforms.uAlpha = value;\n    }\n}\n\n// Americanized alias\nColorMatrixFilter.prototype.grayscale = ColorMatrixFilter.prototype.greyscale;\n"], "names": ["defaultFilterVertex", "Filter"], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;;;ACpBA;;;;;;;;;;;;AAYG;AACH,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;IAAuC,SAAM,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;AAIzC,IAAA,SAAA,iBAAA,GAAA;QAAA,IAaC,KAAA,GAAA,IAAA,CAAA;AAXG,QAAA,IAAM,QAAQ,GAAG;AACb,YAAA,CAAC,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC9B,gBAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,gBAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACb,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,YAAA,MAAM,EAAE,CAAC;SACZ,CAAC;AAEF,QAAA,KAAA,GAAA,kBAAMA,wBAAmB,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAC,IAAA,CAAA;AAE/C,QAAA,KAAI,CAAC,KAAK,GAAG,CAAC,CAAC;;KAClB;AAED;;;;;AAKG;AACK,IAAA,iBAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,UAAoB,MAAmB,EAAE,QAAgB,EAAA;AAAhB,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAgB,GAAA,KAAA,CAAA,EAAA;QAErD,IAAI,SAAS,GAAG,MAAM,CAAC;AAEvB,QAAA,IAAI,QAAQ,EACZ;AACI,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACnD,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAQ,CAAC;AACnD,SAAA;;AAGD,QAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;KAC/B,CAAA;AAED;;;;;;;AAOG;AACK,IAAA,iBAAA,CAAA,SAAA,CAAA,SAAS,GAAjB,UAAkB,GAAgB,EAAE,CAAc,EAAE,CAAc,EAAA;;QAG9D,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;QAGhF,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;QAGhF,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;QAGtF,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAEtF,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;AAED;;;;AAIG;IACK,iBAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,MAAmB,EAAA;;AAGpC,QAAA,IAAM,CAAC,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;AAEnC,QAAA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AACZ,QAAA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AACZ,QAAA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;AACb,QAAA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;AAEb,QAAA,OAAO,CAAQ,CAAC;KACnB,CAAA;AAED;;;;;AAKG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,UAAkB,CAAS,EAAE,QAAiB,EAAA;AAE1C,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;;;AAOG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,IAAI,GAAX,UAAY,KAAa,EAAE,QAAkB,EAAA;QAEzC,IAAM,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC;QAC/B,IAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9B,QAAA,IAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;AAEvB,QAAA,IAAM,MAAM,GAAgB;YACxB,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;AAKG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UAAiB,KAAa,EAAE,QAAiB,EAAA;AAE7C,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACzB,YAAA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACzB,YAAA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACzB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,QAAiB,EAAA;AAElC,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;AAKG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAG,GAAV,UAAW,QAAgB,EAAE,QAAiB,EAAA;AAE1C,QAAA,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAE3C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAChC,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEvB;;;;;;;;;;;AAWG;AAEH,QAAA,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAErB,QAAA,IAAM,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;AACtC,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAC/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAE/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAC/C,QAAA,IAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AACtC,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAE/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAC/C,QAAA,IAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAC/C,QAAA,IAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AAEtC,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;;;AAOG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,MAAc,EAAE,QAAiB,EAAA;QAE7C,IAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAEzB,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;;AAMG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,MAAU,EAAE,QAAkB,EAAA;AAA9B,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAU,GAAA,CAAA,CAAA,EAAA;QAEtB,IAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,QAAA,IAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAE3B,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;;AAGM,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAjB,YAAA;AAEI,QAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KACrB,CAAA;AAED;;;;AAIG;IACI,iBAAQ,CAAA,SAAA,CAAA,QAAA,GAAf,UAAgB,QAAiB,EAAA;AAE7B,QAAA,IAAM,MAAM,GAAgB;YACxB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACd,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACd,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AACd,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,QAAiB,EAAA;AAE1B,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;AAClC,YAAA,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;AAClC,YAAA,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;AAClC,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAW,CAAA,SAAA,CAAA,WAAA,GAAlB,UAAmB,QAAiB,EAAA;AAEhC,QAAA,IAAM,MAAM,GAAgB;YACxB,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,kBAAkB;YACpF,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAC,iBAAiB;YACpF,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,CAAC,EAAE,kBAAkB;AACjF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAQ,CAAA,SAAA,CAAA,QAAA,GAAf,UAAgB,QAAiB,EAAA;AAE7B,QAAA,IAAM,MAAM,GAAgB;YACxB,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YAC3B,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YAC3B,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AAC3B,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,QAAiB,EAAA;AAE1B,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAU,CAAA,SAAA,CAAA,UAAA,GAAjB,UAAkB,QAAiB,EAAA;AAE/B,QAAA,IAAM,MAAM,GAAgB;YACxB,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,iBAAiB;YACnF,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,kBAAkB;YACrF,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,EAAE,iBAAiB;AACnF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,QAAiB,EAAA;AAE3B,QAAA,IAAM,MAAM,GAAgB;YACxB,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,CAAC,EAAE,iBAAiB;YAClF,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC,iBAAiB;YACrF,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC,iBAAiB;AACrF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;AAIG;IACI,iBAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,QAAiB,EAAA;AAE5B,QAAA,IAAM,MAAM,GAAgB;YACxB,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,iBAAiB;AAClF,YAAA,mBAAmB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,EAAE,iBAAiB;YAClF,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,EAAE,iBAAiB;AACjF,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;;;;AAQG;IACI,iBAAS,CAAA,SAAA,CAAA,SAAA,GAAhB,UAAiB,YAAoB,EAAE,KAAa,EAAE,UAAkB,EAAE,SAAiB,EAAE,QAAiB,EAAA;AAE1G,QAAA,YAAY,GAAG,YAAY,IAAI,GAAG,CAAC;AACnC,QAAA,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC;AACtB,QAAA,UAAU,GAAG,UAAU,IAAI,QAAQ,CAAC;AACpC,QAAA,SAAS,GAAG,SAAS,IAAI,QAAQ,CAAC;AAElC,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,UAAU,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC;AAC7C,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;QAC5C,IAAM,EAAE,GAAG,CAAC,UAAU,GAAG,IAAI,IAAI,GAAG,CAAC;AAErC,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC;AAC5C,QAAA,IAAM,EAAE,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;QAC3C,IAAM,EAAE,GAAG,CAAC,SAAS,GAAG,IAAI,IAAI,GAAG,CAAC;AAEpC,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AACrB,YAAA,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;AAC3B,YAAA,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC;AACpB,YAAA,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAClC,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;AAKG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,UAAa,SAAiB,EAAE,QAAiB,EAAA;AAE7C,QAAA,SAAS,GAAG,SAAS,IAAI,GAAG,CAAC;AAE7B,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,SAAS,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACvC,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;YAC9B,CAAC,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;AACnC,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;;;AAOG;AACI,IAAA,iBAAA,CAAA,SAAA,CAAA,QAAQ,GAAf,UAAgB,MAAc,EAAE,QAAiB,EAAA;AAE7C,QAAA,IAAM,MAAM,GAAgB;;AAExB,YAAA,kBAAkB,GAAG,MAAM;YAC3B,CAAC,iBAAiB,GAAG,MAAM;YAC3B,CAAC,kBAAkB,GAAG,MAAM;AAC5B,YAAA,CAAC,GAAG,MAAM;AACV,YAAA,mBAAmB,GAAG,MAAM;;YAE5B,CAAC,kBAAkB,GAAG,MAAM;AAC5B,YAAA,iBAAiB,GAAG,MAAM;YAC1B,CAAC,iBAAiB,GAAG,MAAM;AAC3B,YAAA,CAAC,GAAG,MAAM;YACV,CAAC,iBAAiB,GAAG,MAAM;;YAE3B,CAAC,kBAAkB,GAAG,MAAM;YAC5B,CAAC,kBAAkB,GAAG,MAAM;AAC5B,YAAA,iBAAiB,GAAG,MAAM;AAC1B,YAAA,CAAC,GAAG,MAAM;AACV,YAAA,kBAAkB,GAAG,MAAM;;AAE3B,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED;;;;;;AAMG;IACI,iBAAG,CAAA,SAAA,CAAA,GAAA,GAAV,UAAW,QAAiB,EAAA;AAExB,QAAA,IAAM,MAAM,GAAgB;YACxB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YAClB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;YACnB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACnB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;;AAGM,IAAA,iBAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,IAAM,MAAM,GAAgB;AACxB,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,YAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAChB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACnC,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AALV;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1B;AAED,QAAA,GAAA,EAAA,UAAW,KAAkB,EAAA;AAEzB,YAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;SAC3B;;;AALA,KAAA,CAAA,CAAA;AAeD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AART;;;;;;;AAOG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;SAC/B;AAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;AAEnB,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;SAChC;;;AALA,KAAA,CAAA,CAAA;IAML,OAAC,iBAAA,CAAA;AAAD,CAxjBA,CAAuCC,WAAM,CAwjB5C,EAAA;AAED;AACA,iBAAiB,CAAC,SAAS,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC,SAAS;;;;"}