/*!
 * @pixi/app - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/app is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{ExtensionType as e,extensions as i,autoDetect<PERSON>enderer as r}from"@pixi/core";import{Container as n}from"@pixi/display";var t=function(){function i(){}return i.init=function(e){var i=this;Object.defineProperty(this,"resizeTo",{set:function(e){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=e,e&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get:function(){return this._resizeTo}}),this.queueResize=function(){i._resizeTo&&(i.cancelResize(),i._resizeId=requestAnimationFrame((function(){return i.resize()})))},this.cancelResize=function(){i._resizeId&&(cancelAnimationFrame(i._resizeId),i._resizeId=null)},this.resize=function(){if(i._resizeTo){var e,r;if(i.cancelResize(),i._resizeTo===globalThis.window)e=globalThis.innerWidth,r=globalThis.innerHeight;else{var n=i._resizeTo;e=n.clientWidth,r=n.clientHeight}i.renderer.resize(e,r)}},this._resizeId=null,this._resizeTo=null,this.resizeTo=e.resizeTo||null},i.destroy=function(){globalThis.removeEventListener("resize",this.queueResize),this.cancelResize(),this.cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null},i.extension=e.Application,i}(),s=function(){function t(e){var i=this;this.stage=new n,e=Object.assign({forceCanvas:!1},e),this.renderer=r(e),t._plugins.forEach((function(r){r.init.call(i,e)}))}return t.registerPlugin=function(r){i.add({type:e.Application,ref:r})},t.prototype.render=function(){this.renderer.render(this.stage)},Object.defineProperty(t.prototype,"view",{get:function(){return this.renderer.view},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"screen",{get:function(){return this.renderer.screen},enumerable:!1,configurable:!0}),t.prototype.destroy=function(e,i){var r=this,n=t._plugins.slice(0);n.reverse(),n.forEach((function(e){e.destroy.call(r)})),this.stage.destroy(i),this.stage=null,this.renderer.destroy(e),this.renderer=null},t._plugins=[],t}();i.handleByList(e.Application,s._plugins),i.add(t);export{s as Application,t as ResizePlugin};
//# sourceMappingURL=app.min.mjs.map
