{"version": 3, "file": "extensions.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/index.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * Collection of valid extension types.\n * @memberof PIXI\n * @property {string} Application - Application plugins\n * @property {string} RendererPlugin - Plugins for Renderer\n * @property {string} CanvasRendererPlugin - Plugins for CanvasRenderer\n * @property {string} Loader - Plugins to use with Loader\n * @property {string} LoadParser - Parsers for Assets loader.\n * @property {string} ResolveParser - Parsers for Assets resolvers.\n * @property {string} CacheParser - Parsers for Assets cache.\n */\nenum ExtensionType\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    Application = 'application',\n    RendererPlugin = 'renderer-webgl-plugin',\n    CanvasRendererPlugin = 'renderer-canvas-plugin',\n    Loader = 'loader',\n    LoadParser = 'load-parser',\n    ResolveParser = 'resolve-parser',\n    CacheParser = 'cache-parser',\n    DetectionParser = 'detection-parser',\n}\n\ninterface ExtensionMetadataDetails\n{\n    type: ExtensionType | ExtensionType[];\n    name?: string;\n}\n\ntype ExtensionMetadata = ExtensionType | ExtensionMetadataDetails;\n\n/**\n * Format when registering an extension. Generally, the extension\n * should have these values as `extension` static property,\n * but you can override name or type by providing an object.\n * @memberof PIXI\n */\ninterface ExtensionFormatLoose\n{\n    /** The extension type, can be multiple types */\n    type: ExtensionType | ExtensionType[];\n    /** Optional. Some plugins provide an API name/property, such as Renderer plugins */\n    name?: string;\n    /** Reference to the plugin object/class */\n    ref: any;\n}\n\n/**\n * Strict extension format that is used internally for registrations.\n * @memberof PIXI\n */\ninterface ExtensionFormat extends ExtensionFormatLoose\n{\n    /** The extension type, always expressed as multiple, even if a single */\n    type: ExtensionType[];\n}\n\ntype ExtensionHandler = (extension: ExtensionFormat) => void;\n\n/**\n * Convert input into extension format data.\n * @ignore\n */\nconst normalizeExtension = (ext: ExtensionFormatLoose | any): ExtensionFormat =>\n{\n    // Class/Object submission, use extension object\n    if (typeof ext === 'function' || (typeof ext === 'object' && ext.extension))\n    {\n        // #if _DEBUG\n        if (!ext.extension)\n        {\n            throw new Error('Extension class must have an extension object');\n        }\n        // #endif\n        const metadata: ExtensionMetadataDetails = (typeof ext.extension !== 'object')\n            ? { type: ext.extension }\n            : ext.extension;\n\n        ext = { ...metadata, ref: ext };\n    }\n    if (typeof ext === 'object')\n    {\n        ext = { ...ext };\n    }\n    else\n    {\n        throw new Error('Invalid extension type');\n    }\n\n    if (typeof ext.type === 'string')\n    {\n        ext.type = [ext.type];\n    }\n\n    return ext;\n};\n\n/**\n * Global registration of all PixiJS extensions. One-stop-shop for extensibility.\n * @memberof PIXI\n * @namespace extensions\n */\nconst extensions = {\n\n    /** @ignore */\n    _addHandlers: null as Record<ExtensionType, ExtensionHandler>,\n\n    /** @ignore */\n    _removeHandlers: null as Record<ExtensionType, ExtensionHandler>,\n\n    /** @ignore */\n    _queue: {} as Record<ExtensionType, ExtensionFormat[]>,\n\n    /**\n     * Remove extensions from PixiJS.\n     * @param extensions - Extensions to be removed.\n     * @returns {PIXI.extensions} For chaining.\n     */\n    remove(...extensions: Array<ExtensionFormatLoose | any>)\n    {\n        extensions.map(normalizeExtension).forEach((ext) =>\n        {\n            ext.type.forEach((type) => this._removeHandlers[type]?.(ext));\n        });\n\n        return this;\n    },\n\n    /**\n     * Register new extensions with PixiJS.\n     * @param extensions - The spread of extensions to add to PixiJS.\n     * @returns {PIXI.extensions} For chaining.\n     */\n    add(...extensions: Array<ExtensionFormatLoose | any>)\n    {\n        // Handle any extensions either passed as class w/ data or as data\n        extensions.map(normalizeExtension).forEach((ext) =>\n        {\n            ext.type.forEach((type) =>\n            {\n                const handlers = this._addHandlers;\n                const queue = this._queue;\n\n                if (!handlers[type])\n                {\n                    queue[type] = queue[type] || [];\n                    queue[type].push(ext);\n                }\n                else\n                {\n                    handlers[type](ext);\n                }\n            });\n        });\n\n        return this;\n    },\n\n    /**\n     * Internal method to handle extensions by name.\n     * @param type - The extension type.\n     * @param onAdd  - Function for handling when extensions are added/registered passes {@link PIXI.ExtensionFormat}.\n     * @param onRemove  - Function for handling when extensions are removed/unregistered passes {@link PIXI.ExtensionFormat}.\n     * @returns {PIXI.extensions} For chaining.\n     */\n    handle(type: ExtensionType, onAdd: ExtensionHandler, onRemove: ExtensionHandler)\n    {\n        const addHandlers = this._addHandlers = this._addHandlers || {} as Record<ExtensionType, ExtensionHandler>;\n        const removeHandlers = this._removeHandlers = this._removeHandlers || {} as Record<ExtensionType, ExtensionHandler>;\n\n        // #if _DEBUG\n        if (addHandlers[type] || removeHandlers[type])\n        {\n            throw new Error(`Extension type ${type} already has a handler`);\n        }\n        // #endif\n\n        addHandlers[type] = onAdd;\n        removeHandlers[type] = onRemove;\n\n        // Process the queue\n        const queue = this._queue;\n\n        // Process any plugins that have been registered before the handler\n        if (queue[type])\n        {\n            queue[type].forEach((ext) => onAdd(ext));\n            delete queue[type];\n        }\n\n        return this;\n    },\n\n    /**\n     * Handle a type, but using a map by `name` property.\n     * @param type - Type of extension to handle.\n     * @param map - The object map of named extensions.\n     * @returns {PIXI.extensions} For chaining.\n     */\n    handleByMap(type: ExtensionType, map: Record<string, any>)\n    {\n        return this.handle(type,\n            (extension) =>\n            {\n                map[extension.name] = extension.ref;\n            },\n            (extension) =>\n            {\n                delete map[extension.name];\n            }\n        );\n    },\n\n    /**\n     * Handle a type, but using a list of extensions.\n     * @param type - Type of extension to handle.\n     * @param list - The list of extensions.\n     * @returns {PIXI.extensions} For chaining.\n     */\n    handleByList(type: ExtensionType, list: any[])\n    {\n        return this.handle(\n            type,\n            (extension) =>\n            {\n                if (list.includes(extension.ref))\n                {\n                    return;\n                }\n\n                list.push(extension.ref);\n                // TODO: remove me later, only added for @pixi/loaders\n                if (type === ExtensionType.Loader)\n                {\n                    extension.ref.add?.();\n                }\n            },\n            (extension) =>\n            {\n                const index = list.indexOf(extension.ref);\n\n                if (index !== -1)\n                {\n                    list.splice(index, 1);\n                }\n            }\n        );\n    },\n};\n\nexport {\n    extensions,\n    ExtensionType,\n};\nexport type {\n    ExtensionHandler,\n    ExtensionMetadata,\n    ExtensionFormatLoose,\n    ExtensionFormat,\n};\n"], "names": ["ExtensionType", "__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "exports", "normalizeExtension", "ext", "extension", "metadata", "type", "ref", "Error", "extensions", "_addHandlers", "_removeHandlers", "_queue", "remove", "_this", "_i", "map", "for<PERSON>ach", "_a", "_b", "add", "handlers", "queue", "push", "handle", "onAdd", "onRemove", "addHandlers", "removeHandlers", "handleByMap", "name", "handleByList", "list", "includes", "Loader", "index", "indexOf", "splice"], "mappings": ";;;;;;;sEA6BO,IClBFA,EDkBMC,EAAW,WAQlB,OAPAA,EAAWC,OAAOC,QAAU,SAAkBC,GAC1C,QAASC,cAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAII,KADTL,EAAIG,EAAUF,GACOJ,OAAOS,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE9E,OAAON,GAEJH,EAASa,MAAMC,KAAMP,YCf/BQ,EAAAhB,mBAAA,GAXIA,EAAAA,kBAAAA,EAAAA,cAWJ,KARG,YAAA,cACAA,EAAA,eAAA,wBACAA,EAAA,qBAAA,yBACAA,EAAA,OAAA,SACAA,EAAA,WAAA,cACAA,EAAA,cAAA,iBACAA,EAAA,YAAA,eACAA,EAAA,gBAAA,mBA2CJ,IAAMiB,EAAqB,SAACC,GAGxB,GAAmB,mBAARA,GAAsC,iBAARA,GAAoBA,EAAIC,UACjE,CAOI,IAAMC,EAA+D,iBAAlBF,EAAIC,UACjD,CAAEE,KAAMH,EAAIC,WACZD,EAAIC,UAEVD,SAAWE,GAAQ,CAAEE,IAAKJ,IAE9B,GAAmB,iBAARA,EAMP,MAAM,IAAIK,MAAM,0BAQpB,MALwB,iBAPpBL,EAAGjB,EAAA,GAAQiB,IAOAG,OAEXH,EAAIG,KAAO,CAACH,EAAIG,OAGbH,GAQLM,EAAa,CAGfC,aAAc,KAGdC,gBAAiB,KAGjBC,OAAQ,GAORC,OAAA,+BAQCC,EAAAd,KARsDS,EAAA,GAAAM,EAAA,EAAhDA,EAAgDtB,UAAAC,OAAhDqB,IAAAN,EAAgDM,GAAAtB,EAAAsB,GAOnD,OALAN,EAAWO,IAAId,GAAoBe,SAAQ,SAACd,GAExCA,EAAIG,KAAKW,SAAQ,SAACX,GAAS,IAAAY,EAAAC,EAAA,OAA0B,QAA1BA,GAAAD,EAAAJ,EAAKH,iBAAgBL,UAAK,IAAAa,OAAA,EAAAA,EAAArB,KAAAoB,EAAGf,SAGrDH,MAQXoB,IAAA,+BAuBCN,EAAAd,KAvBmDS,EAAA,GAAAM,EAAA,EAAhDA,EAAgDtB,UAAAC,OAAhDqB,IAAAN,EAAgDM,GAAAtB,EAAAsB,GAsBhD,OAnBAN,EAAWO,IAAId,GAAoBe,SAAQ,SAACd,GAExCA,EAAIG,KAAKW,SAAQ,SAACX,GAEd,IAAMe,EAAWP,EAAKJ,aAChBY,EAAQR,EAAKF,OAEdS,EAASf,GAOVe,EAASf,GAAMH,IALfmB,EAAMhB,GAAQgB,EAAMhB,IAAS,GAC7BgB,EAAMhB,GAAMiB,KAAKpB,UAStBH,MAUXwB,OAAA,SAAOlB,EAAqBmB,EAAyBC,GAEjD,IAAMC,EAAc3B,KAAKU,aAAeV,KAAKU,cAAgB,GACvDkB,EAAiB5B,KAAKW,gBAAkBX,KAAKW,iBAAmB,GAStEgB,EAAYrB,GAAQmB,EACpBG,EAAetB,GAAQoB,EAGvB,IAAMJ,EAAQtB,KAAKY,OASnB,OANIU,EAAMhB,KAENgB,EAAMhB,GAAMW,SAAQ,SAACd,GAAQ,OAAAsB,EAAMtB,aAC5BmB,EAAMhB,IAGVN,MASX6B,YAAA,SAAYvB,EAAqBU,GAE7B,OAAOhB,KAAKwB,OAAOlB,GACf,SAACF,GAEGY,EAAIZ,EAAU0B,MAAQ1B,EAAUG,OAEpC,SAACH,UAEUY,EAAIZ,EAAU0B,UAWjCC,aAAA,SAAazB,EAAqB0B,GAE9B,OAAOhC,KAAKwB,OACRlB,GACA,SAACF,WAEO4B,EAAKC,SAAS7B,EAAUG,OAK5ByB,EAAKT,KAAKnB,EAAUG,KAEhBD,IAASrB,EAAaA,cAACiD,iBAEvBf,KAAAf,EAAUG,KAAIa,iCAGtB,SAAChB,GAEG,IAAM+B,EAAQH,EAAKI,QAAQhC,EAAUG,MAEtB,IAAX4B,GAEAH,EAAKK,OAAOF,EAAO"}