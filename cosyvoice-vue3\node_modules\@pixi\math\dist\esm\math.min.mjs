/*!
 * @pixi/math - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/math is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
var t,i=2*Math.PI,h=180/Math.PI,s=Math.PI/180;!function(t){t[t.POLY=0]="POLY",t[t.RECT=1]="RECT",t[t.CIRC=2]="CIRC",t[t.ELIP=3]="ELIP",t[t.RREC=4]="RREC"}(t||(t={}));var o=function(){function t(t,i){void 0===t&&(t=0),void 0===i&&(i=0),this.x=0,this.y=0,this.x=t,this.y=i}return t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.copyFrom=function(t){return this.set(t.x,t.y),this},t.prototype.copyTo=function(t){return t.set(this.x,this.y),t},t.prototype.equals=function(t){return t.x===this.x&&t.y===this.y},t.prototype.set=function(t,i){return void 0===t&&(t=0),void 0===i&&(i=t),this.x=t,this.y=i,this},t}(),n=[new o,new o,new o,new o],r=function(){function i(i,h,s,o){void 0===i&&(i=0),void 0===h&&(h=0),void 0===s&&(s=0),void 0===o&&(o=0),this.x=Number(i),this.y=Number(h),this.width=Number(s),this.height=Number(o),this.type=t.RECT}return Object.defineProperty(i.prototype,"left",{get:function(){return this.x},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"right",{get:function(){return this.x+this.width},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"top",{get:function(){return this.y},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"bottom",{get:function(){return this.y+this.height},enumerable:!1,configurable:!0}),Object.defineProperty(i,"EMPTY",{get:function(){return new i(0,0,0,0)},enumerable:!1,configurable:!0}),i.prototype.clone=function(){return new i(this.x,this.y,this.width,this.height)},i.prototype.copyFrom=function(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this},i.prototype.copyTo=function(t){return t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t},i.prototype.contains=function(t,i){return!(this.width<=0||this.height<=0)&&(t>=this.x&&t<this.x+this.width&&i>=this.y&&i<this.y+this.height)},i.prototype.intersects=function(t,i){if(!i){var h=this.x<t.x?t.x:this.x;if((this.right>t.right?t.right:this.right)<=h)return!1;var s=this.y<t.y?t.y:this.y;return(this.bottom>t.bottom?t.bottom:this.bottom)>s}var o=this.left,r=this.right,e=this.top,a=this.bottom;if(r<=o||a<=e)return!1;var c=n[0].set(t.left,t.top),y=n[1].set(t.left,t.bottom),u=n[2].set(t.right,t.top),p=n[3].set(t.right,t.bottom);if(u.x<=c.x||y.y<=c.y)return!1;var x=Math.sign(i.a*i.d-i.b*i.c);if(0===x)return!1;if(i.apply(c,c),i.apply(y,y),i.apply(u,u),i.apply(p,p),Math.max(c.x,y.x,u.x,p.x)<=o||Math.min(c.x,y.x,u.x,p.x)>=r||Math.max(c.y,y.y,u.y,p.y)<=e||Math.min(c.y,y.y,u.y,p.y)>=a)return!1;var d=x*(y.y-c.y),f=x*(c.x-y.x),l=d*o+f*e,b=d*r+f*e,v=d*o+f*a,w=d*r+f*a;if(Math.max(l,b,v,w)<=d*c.x+f*c.y||Math.min(l,b,v,w)>=d*p.x+f*p.y)return!1;var _=x*(c.y-u.y),g=x*(u.x-c.x),m=_*o+g*e,M=_*r+g*e,I=_*o+g*a,D=_*r+g*a;return!(Math.max(m,M,I,D)<=_*c.x+g*c.y||Math.min(m,M,I,D)>=_*p.x+g*p.y)},i.prototype.pad=function(t,i){return void 0===t&&(t=0),void 0===i&&(i=t),this.x-=t,this.y-=i,this.width+=2*t,this.height+=2*i,this},i.prototype.fit=function(t){var i=Math.max(this.x,t.x),h=Math.min(this.x+this.width,t.x+t.width),s=Math.max(this.y,t.y),o=Math.min(this.y+this.height,t.y+t.height);return this.x=i,this.width=Math.max(h-i,0),this.y=s,this.height=Math.max(o-s,0),this},i.prototype.ceil=function(t,i){void 0===t&&(t=1),void 0===i&&(i=.001);var h=Math.ceil((this.x+this.width-i)*t)/t,s=Math.ceil((this.y+this.height-i)*t)/t;return this.x=Math.floor((this.x+i)*t)/t,this.y=Math.floor((this.y+i)*t)/t,this.width=h-this.x,this.height=s-this.y,this},i.prototype.enlarge=function(t){var i=Math.min(this.x,t.x),h=Math.max(this.x+this.width,t.x+t.width),s=Math.min(this.y,t.y),o=Math.max(this.y+this.height,t.y+t.height);return this.x=i,this.width=h-i,this.y=s,this.height=o-s,this},i}(),e=function(){function i(i,h,s){void 0===i&&(i=0),void 0===h&&(h=0),void 0===s&&(s=0),this.x=i,this.y=h,this.radius=s,this.type=t.CIRC}return i.prototype.clone=function(){return new i(this.x,this.y,this.radius)},i.prototype.contains=function(t,i){if(this.radius<=0)return!1;var h=this.radius*this.radius,s=this.x-t,o=this.y-i;return(s*=s)+(o*=o)<=h},i.prototype.getBounds=function(){return new r(this.x-this.radius,this.y-this.radius,2*this.radius,2*this.radius)},i}(),a=function(){function i(i,h,s,o){void 0===i&&(i=0),void 0===h&&(h=0),void 0===s&&(s=0),void 0===o&&(o=0),this.x=i,this.y=h,this.width=s,this.height=o,this.type=t.ELIP}return i.prototype.clone=function(){return new i(this.x,this.y,this.width,this.height)},i.prototype.contains=function(t,i){if(this.width<=0||this.height<=0)return!1;var h=(t-this.x)/this.width,s=(i-this.y)/this.height;return(h*=h)+(s*=s)<=1},i.prototype.getBounds=function(){return new r(this.x-this.width,this.y-this.height,this.width,this.height)},i}(),c=function(){function i(){for(var i=arguments,h=[],s=0;s<arguments.length;s++)h[s]=i[s];var o=Array.isArray(h[0])?h[0]:h;if("number"!=typeof o[0]){for(var n=[],r=0,e=o.length;r<e;r++)n.push(o[r].x,o[r].y);o=n}this.points=o,this.type=t.POLY,this.closeStroke=!0}return i.prototype.clone=function(){var t=new i(this.points.slice());return t.closeStroke=this.closeStroke,t},i.prototype.contains=function(t,i){for(var h=!1,s=this.points.length/2,o=0,n=s-1;o<s;n=o++){var r=this.points[2*o],e=this.points[2*o+1],a=this.points[2*n],c=this.points[2*n+1];e>i!=c>i&&t<(i-e)/(c-e)*(a-r)+r&&(h=!h)}return h},i}(),y=function(){function i(i,h,s,o,n){void 0===i&&(i=0),void 0===h&&(h=0),void 0===s&&(s=0),void 0===o&&(o=0),void 0===n&&(n=20),this.x=i,this.y=h,this.width=s,this.height=o,this.radius=n,this.type=t.RREC}return i.prototype.clone=function(){return new i(this.x,this.y,this.width,this.height,this.radius)},i.prototype.contains=function(t,i){if(this.width<=0||this.height<=0)return!1;if(t>=this.x&&t<=this.x+this.width&&i>=this.y&&i<=this.y+this.height){var h=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(i>=this.y+h&&i<=this.y+this.height-h||t>=this.x+h&&t<=this.x+this.width-h)return!0;var s=t-(this.x+h),o=i-(this.y+h),n=h*h;if(s*s+o*o<=n)return!0;if((s=t-(this.x+this.width-h))*s+o*o<=n)return!0;if(s*s+(o=i-(this.y+this.height-h))*o<=n)return!0;if((s=t-(this.x+h))*s+o*o<=n)return!0}return!1},i}(),u=function(){function t(t,i,h,s){void 0===h&&(h=0),void 0===s&&(s=0),this._x=h,this._y=s,this.cb=t,this.scope=i}return t.prototype.clone=function(i,h){return void 0===i&&(i=this.cb),void 0===h&&(h=this.scope),new t(i,h,this._x,this._y)},t.prototype.set=function(t,i){return void 0===t&&(t=0),void 0===i&&(i=t),this._x===t&&this._y===i||(this._x=t,this._y=i,this.cb.call(this.scope)),this},t.prototype.copyFrom=function(t){return this._x===t.x&&this._y===t.y||(this._x=t.x,this._y=t.y,this.cb.call(this.scope)),this},t.prototype.copyTo=function(t){return t.set(this._x,this._y),t},t.prototype.equals=function(t){return t.x===this._x&&t.y===this._y},Object.defineProperty(t.prototype,"x",{get:function(){return this._x},set:function(t){this._x!==t&&(this._x=t,this.cb.call(this.scope))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"y",{get:function(){return this._y},set:function(t){this._y!==t&&(this._y=t,this.cb.call(this.scope))},enumerable:!1,configurable:!0}),t}(),p=function(){function t(t,i,h,s,o,n){void 0===t&&(t=1),void 0===i&&(i=0),void 0===h&&(h=0),void 0===s&&(s=1),void 0===o&&(o=0),void 0===n&&(n=0),this.array=null,this.a=t,this.b=i,this.c=h,this.d=s,this.tx=o,this.ty=n}return t.prototype.fromArray=function(t){this.a=t[0],this.b=t[1],this.c=t[3],this.d=t[4],this.tx=t[2],this.ty=t[5]},t.prototype.set=function(t,i,h,s,o,n){return this.a=t,this.b=i,this.c=h,this.d=s,this.tx=o,this.ty=n,this},t.prototype.toArray=function(t,i){this.array||(this.array=new Float32Array(9));var h=i||this.array;return t?(h[0]=this.a,h[1]=this.b,h[2]=0,h[3]=this.c,h[4]=this.d,h[5]=0,h[6]=this.tx,h[7]=this.ty,h[8]=1):(h[0]=this.a,h[1]=this.c,h[2]=this.tx,h[3]=this.b,h[4]=this.d,h[5]=this.ty,h[6]=0,h[7]=0,h[8]=1),h},t.prototype.apply=function(t,i){i=i||new o;var h=t.x,s=t.y;return i.x=this.a*h+this.c*s+this.tx,i.y=this.b*h+this.d*s+this.ty,i},t.prototype.applyInverse=function(t,i){i=i||new o;var h=1/(this.a*this.d+this.c*-this.b),s=t.x,n=t.y;return i.x=this.d*h*s+-this.c*h*n+(this.ty*this.c-this.tx*this.d)*h,i.y=this.a*h*n+-this.b*h*s+(-this.ty*this.a+this.tx*this.b)*h,i},t.prototype.translate=function(t,i){return this.tx+=t,this.ty+=i,this},t.prototype.scale=function(t,i){return this.a*=t,this.d*=i,this.c*=t,this.b*=i,this.tx*=t,this.ty*=i,this},t.prototype.rotate=function(t){var i=Math.cos(t),h=Math.sin(t),s=this.a,o=this.c,n=this.tx;return this.a=s*i-this.b*h,this.b=s*h+this.b*i,this.c=o*i-this.d*h,this.d=o*h+this.d*i,this.tx=n*i-this.ty*h,this.ty=n*h+this.ty*i,this},t.prototype.append=function(t){var i=this.a,h=this.b,s=this.c,o=this.d;return this.a=t.a*i+t.b*s,this.b=t.a*h+t.b*o,this.c=t.c*i+t.d*s,this.d=t.c*h+t.d*o,this.tx=t.tx*i+t.ty*s+this.tx,this.ty=t.tx*h+t.ty*o+this.ty,this},t.prototype.setTransform=function(t,i,h,s,o,n,r,e,a){return this.a=Math.cos(r+a)*o,this.b=Math.sin(r+a)*o,this.c=-Math.sin(r-e)*n,this.d=Math.cos(r-e)*n,this.tx=t-(h*this.a+s*this.c),this.ty=i-(h*this.b+s*this.d),this},t.prototype.prepend=function(t){var i=this.tx;if(1!==t.a||0!==t.b||0!==t.c||1!==t.d){var h=this.a,s=this.c;this.a=h*t.a+this.b*t.c,this.b=h*t.b+this.b*t.d,this.c=s*t.a+this.d*t.c,this.d=s*t.b+this.d*t.d}return this.tx=i*t.a+this.ty*t.c+t.tx,this.ty=i*t.b+this.ty*t.d+t.ty,this},t.prototype.decompose=function(t){var h=this.a,s=this.b,o=this.c,n=this.d,r=t.pivot,e=-Math.atan2(-o,n),a=Math.atan2(s,h),c=Math.abs(e+a);return c<1e-5||Math.abs(i-c)<1e-5?(t.rotation=a,t.skew.x=t.skew.y=0):(t.rotation=0,t.skew.x=e,t.skew.y=a),t.scale.x=Math.sqrt(h*h+s*s),t.scale.y=Math.sqrt(o*o+n*n),t.position.x=this.tx+(r.x*h+r.y*o),t.position.y=this.ty+(r.x*s+r.y*n),t},t.prototype.invert=function(){var t=this.a,i=this.b,h=this.c,s=this.d,o=this.tx,n=t*s-i*h;return this.a=s/n,this.b=-i/n,this.c=-h/n,this.d=t/n,this.tx=(h*this.ty-s*o)/n,this.ty=-(t*this.ty-i*o)/n,this},t.prototype.identity=function(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this},t.prototype.clone=function(){var i=new t;return i.a=this.a,i.b=this.b,i.c=this.c,i.d=this.d,i.tx=this.tx,i.ty=this.ty,i},t.prototype.copyTo=function(t){return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t},t.prototype.copyFrom=function(t){return this.a=t.a,this.b=t.b,this.c=t.c,this.d=t.d,this.tx=t.tx,this.ty=t.ty,this},Object.defineProperty(t,"IDENTITY",{get:function(){return new t},enumerable:!1,configurable:!0}),Object.defineProperty(t,"TEMP_MATRIX",{get:function(){return new t},enumerable:!1,configurable:!0}),t}(),x=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],d=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],f=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],l=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],b=[],v=[],w=Math.sign;!function(){for(var t=0;t<16;t++){var i=[];b.push(i);for(var h=0;h<16;h++)for(var s=w(x[t]*x[h]+f[t]*d[h]),o=w(d[t]*x[h]+l[t]*d[h]),n=w(x[t]*f[h]+f[t]*l[h]),r=w(d[t]*f[h]+l[t]*l[h]),e=0;e<16;e++)if(x[e]===s&&d[e]===o&&f[e]===n&&l[e]===r){i.push(e);break}}for(t=0;t<16;t++){var a=new p;a.set(x[t],d[t],f[t],l[t],0,0),v.push(a)}}();var _={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:function(t){return x[t]},uY:function(t){return d[t]},vX:function(t){return f[t]},vY:function(t){return l[t]},inv:function(t){return 8&t?15&t:7&-t},add:function(t,i){return b[t][i]},sub:function(t,i){return b[t][_.inv(i)]},rotate180:function(t){return 4^t},isVertical:function(t){return 2==(3&t)},byDirection:function(t,i){return 2*Math.abs(t)<=Math.abs(i)?i>=0?_.S:_.N:2*Math.abs(i)<=Math.abs(t)?t>0?_.E:_.W:i>0?t>0?_.SE:_.SW:t>0?_.NE:_.NW},matrixAppendRotationInv:function(t,i,h,s){void 0===h&&(h=0),void 0===s&&(s=0);var o=v[_.inv(i)];o.tx=h,o.ty=s,t.append(o)}},g=function(){function t(){this.worldTransform=new p,this.localTransform=new p,this.position=new u(this.onChange,this,0,0),this.scale=new u(this.onChange,this,1,1),this.pivot=new u(this.onChange,this,0,0),this.skew=new u(this.updateSkew,this,0,0),this._rotation=0,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._localID=0,this._currentLocalID=0,this._worldID=0,this._parentID=0}return t.prototype.onChange=function(){this._localID++},t.prototype.updateSkew=function(){this._cx=Math.cos(this._rotation+this.skew.y),this._sx=Math.sin(this._rotation+this.skew.y),this._cy=-Math.sin(this._rotation-this.skew.x),this._sy=Math.cos(this._rotation-this.skew.x),this._localID++},t.prototype.updateLocalTransform=function(){var t=this.localTransform;this._localID!==this._currentLocalID&&(t.a=this._cx*this.scale.x,t.b=this._sx*this.scale.x,t.c=this._cy*this.scale.y,t.d=this._sy*this.scale.y,t.tx=this.position.x-(this.pivot.x*t.a+this.pivot.y*t.c),t.ty=this.position.y-(this.pivot.x*t.b+this.pivot.y*t.d),this._currentLocalID=this._localID,this._parentID=-1)},t.prototype.updateTransform=function(t){var i=this.localTransform;if(this._localID!==this._currentLocalID&&(i.a=this._cx*this.scale.x,i.b=this._sx*this.scale.x,i.c=this._cy*this.scale.y,i.d=this._sy*this.scale.y,i.tx=this.position.x-(this.pivot.x*i.a+this.pivot.y*i.c),i.ty=this.position.y-(this.pivot.x*i.b+this.pivot.y*i.d),this._currentLocalID=this._localID,this._parentID=-1),this._parentID!==t._worldID){var h=t.worldTransform,s=this.worldTransform;s.a=i.a*h.a+i.b*h.c,s.b=i.a*h.b+i.b*h.d,s.c=i.c*h.a+i.d*h.c,s.d=i.c*h.b+i.d*h.d,s.tx=i.tx*h.a+i.ty*h.c+h.tx,s.ty=i.tx*h.b+i.ty*h.d+h.ty,this._parentID=t._worldID,this._worldID++}},t.prototype.setFromMatrix=function(t){t.decompose(this),this._localID++},Object.defineProperty(t.prototype,"rotation",{get:function(){return this._rotation},set:function(t){this._rotation!==t&&(this._rotation=t,this.updateSkew())},enumerable:!1,configurable:!0}),t.IDENTITY=new t,t}();export{e as Circle,s as DEG_TO_RAD,a as Ellipse,p as Matrix,u as ObservablePoint,i as PI_2,o as Point,c as Polygon,h as RAD_TO_DEG,r as Rectangle,y as RoundedRectangle,t as SHAPES,g as Transform,_ as groupD8};
//# sourceMappingURL=math.min.mjs.map
