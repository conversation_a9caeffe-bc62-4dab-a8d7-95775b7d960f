/*!
 * @pixi/graphics - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/graphics is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t,e,i=require("@pixi/core"),r=require("@pixi/math"),s=require("@pixi/utils"),n=require("@pixi/constants"),h=require("@pixi/display");exports.LINE_JOIN=void 0,(t=exports.LINE_JOIN||(exports.LINE_JOIN={})).MITER="miter",t.BEVEL="bevel",t.ROUND="round",exports.LINE_CAP=void 0,(e=exports.LINE_CAP||(exports.LINE_CAP={})).BUTT="butt",e.ROUND="round",e.SQUARE="square";var a={adaptive:!0,maxLength:10,minSegments:8,maxSegments:2048,epsilon:1e-4,_segmentsCount:function(t,e){if(void 0===e&&(e=20),!this.adaptive||!t||isNaN(t))return e;var i=Math.ceil(t/this.maxLength);return i<this.minSegments?i=this.minSegments:i>this.maxSegments&&(i=this.maxSegments),i}},o=function(){function t(){this.color=16777215,this.alpha=1,this.texture=i.Texture.WHITE,this.matrix=null,this.visible=!1,this.reset()}return t.prototype.clone=function(){var e=new t;return e.color=this.color,e.alpha=this.alpha,e.texture=this.texture,e.matrix=this.matrix,e.visible=this.visible,e},t.prototype.reset=function(){this.color=16777215,this.alpha=1,this.texture=i.Texture.WHITE,this.matrix=null,this.visible=!1},t.prototype.destroy=function(){this.texture=null,this.matrix=null},t}(),l=function(t,e){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},l(t,e)};function u(t,e){function i(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}function p(t,e){var i,r;void 0===e&&(e=!1);var s=t.length;if(!(s<6)){for(var n=0,h=0,a=t[s-2],o=t[s-1];h<s;h+=2){var l=t[h],u=t[h+1];n+=(l-a)*(u+o),a=l,o=u}if(!e&&n>0||e&&n<=0){var p=s/2;for(h=p+p%2;h<s;h+=2){var c=s-h-2,f=s-h-1,d=h,y=h+1;i=[t[d],t[c]],t[c]=i[0],t[d]=i[1],r=[t[y],t[f]],t[f]=r[0],t[y]=r[1]}}}}var c={build:function(t){t.points=t.shape.points.slice()},triangulate:function(t,e){var i=t.points,r=t.holes,n=e.points,h=e.indices;if(i.length>=6){p(i,!1);for(var a=[],o=0;o<r.length;o++){var l=r[o];p(l.points,!0),a.push(i.length/2),i=i.concat(l.points)}var u=s.earcut(i,a,2);if(!u)return;var c=n.length/2;for(o=0;o<u.length;o+=3)h.push(u[o]+c),h.push(u[o+1]+c),h.push(u[o+2]+c);for(o=0;o<i.length;o++)n.push(i[o])}}},f={build:function(t){var e,i,s,n,h,a,o=t.points;if(t.type===r.SHAPES.CIRC){var l=t.shape;e=l.x,i=l.y,h=a=l.radius,s=n=0}else if(t.type===r.SHAPES.ELIP){var u=t.shape;e=u.x,i=u.y,h=u.width,a=u.height,s=n=0}else{var p=t.shape,c=p.width/2,f=p.height/2;e=p.x+c,i=p.y+f,s=c-(h=a=Math.max(0,Math.min(p.radius,Math.min(c,f)))),n=f-a}if(h>=0&&a>=0&&s>=0&&n>=0){var d=Math.ceil(2.3*Math.sqrt(h+a)),y=8*d+(s?4:0)+(n?4:0);if(o.length=y,0!==y){if(0===d)return o.length=8,o[0]=o[6]=e+s,o[1]=o[3]=i+n,o[2]=o[4]=e-s,void(o[5]=o[7]=i-n);var v=0,g=4*d+(s?2:0)+2,b=g,x=y,m=e+(E=s+h),_=e-E,S=i+(A=n);if(o[v++]=m,o[v++]=S,o[--g]=S,o[--g]=_,n){var P=i-A;o[b++]=_,o[b++]=P,o[--x]=P,o[--x]=m}for(var w=1;w<d;w++){var M=Math.PI/2*(w/d);m=e+(E=s+Math.cos(M)*h),_=e-E,S=i+(A=n+Math.sin(M)*a),P=i-A;o[v++]=m,o[v++]=S,o[--g]=S,o[--g]=_,o[b++]=_,o[b++]=P,o[--x]=P,o[--x]=m}var E,A;m=e+(E=s),_=e-E,S=i+(A=n+a),P=i-A;o[v++]=m,o[v++]=S,o[--x]=P,o[--x]=m,s&&(o[v++]=_,o[v++]=S,o[--x]=P,o[--x]=_)}}else o.length=0},triangulate:function(t,e){var i=t.points,s=e.points,n=e.indices;if(0!==i.length){var h,a,o=s.length/2,l=o;if(t.type!==r.SHAPES.RREC){var u=t.shape;h=u.x,a=u.y}else{var p=t.shape;h=p.x+p.width/2,a=p.y+p.height/2}var c=t.matrix;s.push(t.matrix?c.a*h+c.c*a+c.tx:h,t.matrix?c.b*h+c.d*a+c.ty:a),o++,s.push(i[0],i[1]);for(var f=2;f<i.length;f+=2)s.push(i[f],i[f+1]),n.push(o++,l,o);n.push(l+1,l,o)}}},d={build:function(t){var e=t.shape,i=e.x,r=e.y,s=e.width,n=e.height,h=t.points;h.length=0,h.push(i,r,i+s,r,i+s,r+n,i,r+n)},triangulate:function(t,e){var i=t.points,r=e.points,s=r.length/2;r.push(i[0],i[1],i[2],i[3],i[6],i[7],i[4],i[5]),e.indices.push(s,s+1,s+2,s+1,s+2,s+3)}};function y(t,e,i){return t+(e-t)*i}function v(t,e,i,r,s,n,h){void 0===h&&(h=[]);for(var a=h,o=0,l=0,u=0,p=0,c=0,f=0,d=0,v=0;d<=20;++d)o=y(t,i,v=d/20),l=y(e,r,v),u=y(i,s,v),p=y(r,n,v),c=y(o,u,v),f=y(l,p,v),0===d&&a[a.length-2]===c&&a[a.length-1]===f||a.push(c,f);return a}var g={build:function(t){if(O.nextRoundedRectBehavior)f.build(t);else{var e=t.shape,i=t.points,r=e.x,s=e.y,n=e.width,h=e.height,a=Math.max(0,Math.min(e.radius,Math.min(n,h)/2));i.length=0,a?(v(r,s+a,r,s,r+a,s,i),v(r+n-a,s,r+n,s,r+n,s+a,i),v(r+n,s+h-a,r+n,s+h,r+n-a,s+h,i),v(r+a,s+h,r,s+h,r,s+h-a,i)):i.push(r,s,r+n,s,r+n,s+h,r,s+h)}},triangulate:function(t,e){if(O.nextRoundedRectBehavior)f.triangulate(t,e);else{for(var i=t.points,r=e.points,n=e.indices,h=r.length/2,a=s.earcut(i,null,2),o=0,l=a.length;o<l;o+=3)n.push(a[o]+h),n.push(a[o+1]+h),n.push(a[o+2]+h);for(o=0,l=i.length;o<l;o++)r.push(i[o],i[++o])}}};function b(t,e,i,r,s,n,h,a){var o,l;h?(o=r,l=-i):(o=-r,l=i);var u=t-i*s+o,p=e-r*s+l,c=t+i*n+o,f=e+r*n+l;return a.push(u,p),a.push(c,f),2}function x(t,e,i,r,s,n,h,a){var o=i-t,l=r-e,u=Math.atan2(o,l),p=Math.atan2(s-t,n-e);a&&u<p?u+=2*Math.PI:!a&&u>p&&(p+=2*Math.PI);var c=u,f=p-u,d=Math.abs(f),y=Math.sqrt(o*o+l*l),v=1+(15*d*Math.sqrt(y)/Math.PI>>0),g=f/v;if(c+=g,a){h.push(t,e),h.push(i,r);for(var b=1,x=c;b<v;b++,x+=g)h.push(t,e),h.push(t+Math.sin(x)*y,e+Math.cos(x)*y);h.push(t,e),h.push(s,n)}else{h.push(i,r),h.push(t,e);for(b=1,x=c;b<v;b++,x+=g)h.push(t+Math.sin(x)*y,e+Math.cos(x)*y),h.push(t,e);h.push(s,n),h.push(t,e)}return 2*v}function m(t,e){t.lineStyle.native?function(t,e){var i=0,s=t.shape,n=t.points||s.points,h=s.type!==r.SHAPES.POLY||s.closeStroke;if(0!==n.length){var a=e.points,o=e.indices,l=n.length/2,u=a.length/2,p=u;for(a.push(n[0],n[1]),i=1;i<l;i++)a.push(n[2*i],n[2*i+1]),o.push(p,p+1),p++;h&&o.push(p,u)}}(t,e):function(t,e){var i=t.shape,s=t.points||i.points.slice(),n=e.closePointEps;if(0!==s.length){var h=t.lineStyle,o=new r.Point(s[0],s[1]),l=new r.Point(s[s.length-2],s[s.length-1]),u=i.type!==r.SHAPES.POLY||i.closeStroke,p=Math.abs(o.x-l.x)<n&&Math.abs(o.y-l.y)<n;if(u){s=s.slice(),p&&(s.pop(),s.pop(),l.set(s[s.length-2],s[s.length-1]));var c=.5*(o.x+l.x),f=.5*(l.y+o.y);s.unshift(c,f),s.push(c,f)}var d=e.points,y=s.length/2,v=s.length,g=d.length/2,m=h.width/2,_=m*m,S=h.miterLimit*h.miterLimit,P=s[0],w=s[1],M=s[2],E=s[3],A=0,D=0,T=-(w-E),I=P-M,C=0,R=0,L=Math.sqrt(T*T+I*I);T/=L,I/=L,T*=m,I*=m;var N=h.alignment,O=2*(1-N),B=2*N;u||(h.cap===exports.LINE_CAP.ROUND?v+=x(P-T*(O-B)*.5,w-I*(O-B)*.5,P-T*O,w-I*O,P+T*B,w+I*B,d,!0)+2:h.cap===exports.LINE_CAP.SQUARE&&(v+=b(P,w,T,I,O,B,!0,d))),d.push(P-T*O,w-I*O),d.push(P+T*B,w+I*B);for(var U=1;U<y-1;++U){P=s[2*(U-1)],w=s[2*(U-1)+1],M=s[2*U],E=s[2*U+1],A=s[2*(U+1)],D=s[2*(U+1)+1],T=-(w-E),I=P-M,T/=L=Math.sqrt(T*T+I*I),I/=L,T*=m,I*=m,C=-(E-D),R=M-A,C/=L=Math.sqrt(C*C+R*R),R/=L,C*=m,R*=m;var H=M-P,j=w-E,F=M-A,q=D-E,z=H*F+j*q,k=j*F-q*H,G=k<0;if(Math.abs(k)<.001*Math.abs(z))d.push(M-T*O,E-I*O),d.push(M+T*B,E+I*B),z>=0&&(h.join===exports.LINE_JOIN.ROUND?v+=x(M,E,M-T*O,E-I*O,M-C*O,E-R*O,d,!1)+4:v+=2,d.push(M-C*B,E-R*B),d.push(M+C*O,E+R*O));else{var W=(-T+P)*(-I+E)-(-T+M)*(-I+w),J=(-C+A)*(-R+E)-(-C+M)*(-R+D),V=(H*J-F*W)/k,Y=(q*W-j*J)/k,Q=(V-M)*(V-M)+(Y-E)*(Y-E),X=M+(V-M)*O,Z=E+(Y-E)*O,K=M-(V-M)*B,$=E-(Y-E)*B,tt=G?O:B;Q<=Math.min(H*H+j*j,F*F+q*q)+tt*tt*_?h.join===exports.LINE_JOIN.BEVEL||Q/_>S?(G?(d.push(X,Z),d.push(M+T*B,E+I*B),d.push(X,Z),d.push(M+C*B,E+R*B)):(d.push(M-T*O,E-I*O),d.push(K,$),d.push(M-C*O,E-R*O),d.push(K,$)),v+=2):h.join===exports.LINE_JOIN.ROUND?G?(d.push(X,Z),d.push(M+T*B,E+I*B),v+=x(M,E,M+T*B,E+I*B,M+C*B,E+R*B,d,!0)+4,d.push(X,Z),d.push(M+C*B,E+R*B)):(d.push(M-T*O,E-I*O),d.push(K,$),v+=x(M,E,M-T*O,E-I*O,M-C*O,E-R*O,d,!1)+4,d.push(M-C*O,E-R*O),d.push(K,$)):(d.push(X,Z),d.push(K,$)):(d.push(M-T*O,E-I*O),d.push(M+T*B,E+I*B),h.join===exports.LINE_JOIN.ROUND?v+=G?x(M,E,M+T*B,E+I*B,M+C*B,E+R*B,d,!0)+2:x(M,E,M-T*O,E-I*O,M-C*O,E-R*O,d,!1)+2:h.join===exports.LINE_JOIN.MITER&&Q/_<=S&&(G?(d.push(K,$),d.push(K,$)):(d.push(X,Z),d.push(X,Z)),v+=2),d.push(M-C*O,E-R*O),d.push(M+C*B,E+R*B),v+=2)}}P=s[2*(y-2)],w=s[2*(y-2)+1],M=s[2*(y-1)],T=-(w-(E=s[2*(y-1)+1])),I=P-M,T/=L=Math.sqrt(T*T+I*I),I/=L,T*=m,I*=m,d.push(M-T*O,E-I*O),d.push(M+T*B,E+I*B),u||(h.cap===exports.LINE_CAP.ROUND?v+=x(M-T*(O-B)*.5,E-I*(O-B)*.5,M-T*O,E-I*O,M+T*B,E+I*B,d,!1)+2:h.cap===exports.LINE_CAP.SQUARE&&(v+=b(M,E,T,I,O,B,!1,d)));var et=e.indices,it=a.epsilon*a.epsilon;for(U=g;U<v+g-2;++U)P=d[2*U],w=d[2*U+1],M=d[2*(U+1)],E=d[2*(U+1)+1],A=d[2*(U+2)],D=d[2*(U+2)+1],Math.abs(P*(E-D)+M*(D-w)+A*(w-E))<it||et.push(U,U+1,U+2)}}(t,e)}var _,S=function(){function t(){}return t.curveTo=function(t,e,i,r,s,n){var h=n[n.length-2],a=n[n.length-1]-e,o=h-t,l=r-e,u=i-t,p=Math.abs(a*u-o*l);if(p<1e-8||0===s)return n[n.length-2]===t&&n[n.length-1]===e||n.push(t,e),null;var c=a*a+o*o,f=l*l+u*u,d=a*l+o*u,y=s*Math.sqrt(c)/p,v=s*Math.sqrt(f)/p,g=y*d/c,b=v*d/f,x=y*u+v*o,m=y*l+v*a,_=o*(v+g),S=a*(v+g),P=u*(y+b),w=l*(y+b);return{cx:x+t,cy:m+e,radius:s,startAngle:Math.atan2(S-m,_-x),endAngle:Math.atan2(w-m,P-x),anticlockwise:o*l>u*a}},t.arc=function(t,e,i,s,n,h,o,l,u){for(var p=o-h,c=a._segmentsCount(Math.abs(p)*n,40*Math.ceil(Math.abs(p)/r.PI_2)),f=p/(2*c),d=2*f,y=Math.cos(f),v=Math.sin(f),g=c-1,b=g%1/g,x=0;x<=g;++x){var m=f+h+d*(x+b*x),_=Math.cos(m),S=-Math.sin(m);u.push((y*_+v*S)*n+i,(y*-S+v*_)*n+s)}},t}(),P=function(){function t(){}return t.curveLength=function(t,e,i,r,s,n,h,a){for(var o=0,l=0,u=0,p=0,c=0,f=0,d=0,y=0,v=0,g=0,b=0,x=t,m=e,_=1;_<=10;++_)g=x-(y=(d=(f=(c=1-(l=_/10))*c)*c)*t+3*f*l*i+3*c*(u=l*l)*s+(p=u*l)*h),b=m-(v=d*e+3*f*l*r+3*c*u*n+p*a),x=y,m=v,o+=Math.sqrt(g*g+b*b);return o},t.curveTo=function(e,i,r,s,n,h,o){var l=o[o.length-2],u=o[o.length-1];o.length-=2;var p=a._segmentsCount(t.curveLength(l,u,e,i,r,s,n,h)),c=0,f=0,d=0,y=0,v=0;o.push(l,u);for(var g=1,b=0;g<=p;++g)d=(f=(c=1-(b=g/p))*c)*c,v=(y=b*b)*b,o.push(d*l+3*f*b*e+3*c*y*r+v*n,d*u+3*f*b*i+3*c*y*s+v*h)},t}(),w=function(){function t(){}return t.curveLength=function(t,e,i,r,s,n){var h=t-2*i+s,a=e-2*r+n,o=2*i-2*t,l=2*r-2*e,u=4*(h*h+a*a),p=4*(h*o+a*l),c=o*o+l*l,f=2*Math.sqrt(u+p+c),d=Math.sqrt(u),y=2*u*d,v=2*Math.sqrt(c),g=p/d;return(y*f+d*p*(f-v)+(4*c*u-p*p)*Math.log((2*d+g+f)/(g+v)))/(4*y)},t.curveTo=function(e,i,r,s,n){for(var h=n[n.length-2],o=n[n.length-1],l=a._segmentsCount(t.curveLength(h,o,e,i,r,s)),u=0,p=0,c=1;c<=l;++c){var f=c/l;u=h+(e-h)*f,p=o+(i-o)*f,n.push(u+(e+(r-e)*f-u)*f,p+(i+(s-i)*f-p)*f)}},t}(),M=function(){function t(){this.reset()}return t.prototype.begin=function(t,e,i){this.reset(),this.style=t,this.start=e,this.attribStart=i},t.prototype.end=function(t,e){this.attribSize=e-this.attribStart,this.size=t-this.start},t.prototype.reset=function(){this.style=null,this.size=0,this.start=0,this.attribStart=0,this.attribSize=0},t}(),E=((_={})[r.SHAPES.POLY]=c,_[r.SHAPES.CIRC]=f,_[r.SHAPES.ELIP]=f,_[r.SHAPES.RECT]=d,_[r.SHAPES.RREC]=g,_),A=[],D=[],T=function(){function t(t,e,i,r){void 0===e&&(e=null),void 0===i&&(i=null),void 0===r&&(r=null),this.points=[],this.holes=[],this.shape=t,this.lineStyle=i,this.fillStyle=e,this.matrix=r,this.type=t.type}return t.prototype.clone=function(){return new t(this.shape,this.fillStyle,this.lineStyle,this.matrix)},t.prototype.destroy=function(){this.shape=null,this.holes.length=0,this.holes=null,this.points.length=0,this.points=null,this.lineStyle=null,this.fillStyle=null},t}(),I=new r.Point,C=function(t){function e(){var e=t.call(this)||this;return e.closePointEps=1e-4,e.boundsPadding=0,e.uvsFloat32=null,e.indicesUint16=null,e.batchable=!1,e.points=[],e.colors=[],e.uvs=[],e.indices=[],e.textureIds=[],e.graphicsData=[],e.drawCalls=[],e.batchDirty=-1,e.batches=[],e.dirty=0,e.cacheDirty=-1,e.clearDirty=0,e.shapeIndex=0,e._bounds=new h.Bounds,e.boundsDirty=-1,e}return u(e,t),Object.defineProperty(e.prototype,"bounds",{get:function(){return this.updateBatches(),this.boundsDirty!==this.dirty&&(this.boundsDirty=this.dirty,this.calculateBounds()),this._bounds},enumerable:!1,configurable:!0}),e.prototype.invalidate=function(){this.boundsDirty=-1,this.dirty++,this.batchDirty++,this.shapeIndex=0,this.points.length=0,this.colors.length=0,this.uvs.length=0,this.indices.length=0,this.textureIds.length=0;for(var t=0;t<this.drawCalls.length;t++)this.drawCalls[t].texArray.clear(),D.push(this.drawCalls[t]);this.drawCalls.length=0;for(t=0;t<this.batches.length;t++){var e=this.batches[t];e.reset(),A.push(e)}this.batches.length=0},e.prototype.clear=function(){return this.graphicsData.length>0&&(this.invalidate(),this.clearDirty++,this.graphicsData.length=0),this},e.prototype.drawShape=function(t,e,i,r){void 0===e&&(e=null),void 0===i&&(i=null),void 0===r&&(r=null);var s=new T(t,e,i,r);return this.graphicsData.push(s),this.dirty++,this},e.prototype.drawHole=function(t,e){if(void 0===e&&(e=null),!this.graphicsData.length)return null;var i=new T(t,null,null,e),r=this.graphicsData[this.graphicsData.length-1];return i.lineStyle=r.lineStyle,r.holes.push(i),this.dirty++,this},e.prototype.destroy=function(){t.prototype.destroy.call(this);for(var e=0;e<this.graphicsData.length;++e)this.graphicsData[e].destroy();this.points.length=0,this.points=null,this.colors.length=0,this.colors=null,this.uvs.length=0,this.uvs=null,this.indices.length=0,this.indices=null,this.indexBuffer.destroy(),this.indexBuffer=null,this.graphicsData.length=0,this.graphicsData=null,this.drawCalls.length=0,this.drawCalls=null,this.batches.length=0,this.batches=null,this._bounds=null},e.prototype.containsPoint=function(t){for(var e=this.graphicsData,i=0;i<e.length;++i){var r=e[i];if(r.fillStyle.visible&&(r.shape&&(r.matrix?r.matrix.applyInverse(t,I):I.copyFrom(t),r.shape.contains(I.x,I.y)))){var s=!1;if(r.holes)for(var n=0;n<r.holes.length;n++){if(r.holes[n].shape.contains(I.x,I.y)){s=!0;break}}if(!s)return!0}}return!1},e.prototype.updateBatches=function(){if(this.graphicsData.length){if(this.validateBatching()){this.cacheDirty=this.dirty;var t=this.uvs,e=this.graphicsData,i=null,r=null;this.batches.length>0&&(r=(i=this.batches[this.batches.length-1]).style);for(var s=this.shapeIndex;s<e.length;s++){this.shapeIndex++;var h=e[s],a=h.fillStyle,o=h.lineStyle;E[h.type].build(h),h.matrix&&this.transformPoints(h.points,h.matrix),(a.visible||o.visible)&&this.processHoles(h.holes);for(var l=0;l<2;l++){var u=0===l?a:o;if(u.visible){var p=u.texture.baseTexture,c=this.indices.length,f=this.points.length/2;p.wrapMode=n.WRAP_MODES.REPEAT,0===l?this.processFill(h):this.processLine(h);var d=this.points.length/2-f;0!==d&&(i&&!this._compareStyles(r,u)&&(i.end(c,f),i=null),i||((i=A.pop()||new M).begin(u,c,f),this.batches.push(i),r=u),this.addUvs(this.points,t,u.texture,f,d,u.matrix))}}}var y=this.indices.length,v=this.points.length/2;if(i&&i.end(y,v),0!==this.batches.length){var g=v>65535;this.indicesUint16&&this.indices.length===this.indicesUint16.length&&g===this.indicesUint16.BYTES_PER_ELEMENT>2?this.indicesUint16.set(this.indices):this.indicesUint16=g?new Uint32Array(this.indices):new Uint16Array(this.indices),this.batchable=this.isBatchable(),this.batchable?this.packBatches():this.buildDrawCalls()}else this.batchable=!0}}else this.batchable=!0},e.prototype._compareStyles=function(t,e){return!(!t||!e)&&(t.texture.baseTexture===e.texture.baseTexture&&(t.color+t.alpha===e.color+e.alpha&&!!t.native==!!e.native))},e.prototype.validateBatching=function(){if(this.dirty===this.cacheDirty||!this.graphicsData.length)return!1;for(var t=0,e=this.graphicsData.length;t<e;t++){var i=this.graphicsData[t],r=i.fillStyle,s=i.lineStyle;if(r&&!r.texture.baseTexture.valid)return!1;if(s&&!s.texture.baseTexture.valid)return!1}return!0},e.prototype.packBatches=function(){this.batchDirty++,this.uvsFloat32=new Float32Array(this.uvs);for(var t=this.batches,e=0,i=t.length;e<i;e++)for(var r=t[e],s=0;s<r.size;s++){var n=r.start+s;this.indicesUint16[n]=this.indicesUint16[n]-r.attribStart}},e.prototype.isBatchable=function(){if(this.points.length>131070)return!1;for(var t=this.batches,i=0;i<t.length;i++)if(t[i].style.native)return!1;return this.points.length<2*e.BATCHABLE_SIZE},e.prototype.buildDrawCalls=function(){for(var t=++i.BaseTexture._globalBatch,e=0;e<this.drawCalls.length;e++)this.drawCalls[e].texArray.clear(),D.push(this.drawCalls[e]);this.drawCalls.length=0;var r=this.colors,s=this.textureIds,h=D.pop();h||((h=new i.BatchDrawCall).texArray=new i.BatchTextureArray),h.texArray.count=0,h.start=0,h.size=0,h.type=n.DRAW_MODES.TRIANGLES;var a=0,o=null,l=0,u=!1,p=n.DRAW_MODES.TRIANGLES,c=0;this.drawCalls.push(h);for(e=0;e<this.batches.length;e++){var f=this.batches[e],d=f.style,y=d.texture.baseTexture;u!==!!d.native&&(p=(u=!!d.native)?n.DRAW_MODES.LINES:n.DRAW_MODES.TRIANGLES,o=null,a=8,t++),o!==y&&(o=y,y._batchEnabled!==t&&(8===a&&(t++,a=0,h.size>0&&((h=D.pop())||((h=new i.BatchDrawCall).texArray=new i.BatchTextureArray),this.drawCalls.push(h)),h.start=c,h.size=0,h.texArray.count=0,h.type=p),y.touched=1,y._batchEnabled=t,y._batchLocation=a,y.wrapMode=n.WRAP_MODES.REPEAT,h.texArray.elements[h.texArray.count++]=y,a++)),h.size+=f.size,c+=f.size,l=y._batchLocation,this.addColors(r,d.color,d.alpha,f.attribSize,f.attribStart),this.addTextureIds(s,l,f.attribSize,f.attribStart)}i.BaseTexture._globalBatch=t,this.packAttributes()},e.prototype.packAttributes=function(){for(var t=this.points,e=this.uvs,i=this.colors,r=this.textureIds,s=new ArrayBuffer(3*t.length*4),n=new Float32Array(s),h=new Uint32Array(s),a=0,o=0;o<t.length/2;o++)n[a++]=t[2*o],n[a++]=t[2*o+1],n[a++]=e[2*o],n[a++]=e[2*o+1],h[a++]=i[o],n[a++]=r[o];this._buffer.update(s),this._indexBuffer.update(this.indicesUint16)},e.prototype.processFill=function(t){t.holes.length?c.triangulate(t,this):E[t.type].triangulate(t,this)},e.prototype.processLine=function(t){m(t,this);for(var e=0;e<t.holes.length;e++)m(t.holes[e],this)},e.prototype.processHoles=function(t){for(var e=0;e<t.length;e++){var i=t[e];E[i.type].build(i),i.matrix&&this.transformPoints(i.points,i.matrix)}},e.prototype.calculateBounds=function(){var t=this._bounds;t.clear(),t.addVertexData(this.points,0,this.points.length),t.pad(this.boundsPadding,this.boundsPadding)},e.prototype.transformPoints=function(t,e){for(var i=0;i<t.length/2;i++){var r=t[2*i],s=t[2*i+1];t[2*i]=e.a*r+e.c*s+e.tx,t[2*i+1]=e.b*r+e.d*s+e.ty}},e.prototype.addColors=function(t,e,i,r,n){void 0===n&&(n=0);var h=(e>>16)+(65280&e)+((255&e)<<16),a=s.premultiplyTint(h,i);t.length=Math.max(t.length,n+r);for(var o=0;o<r;o++)t[n+o]=a},e.prototype.addTextureIds=function(t,e,i,r){void 0===r&&(r=0),t.length=Math.max(t.length,r+i);for(var s=0;s<i;s++)t[r+s]=e},e.prototype.addUvs=function(t,e,i,r,s,n){void 0===n&&(n=null);for(var h=0,a=e.length,o=i.frame;h<s;){var l=t[2*(r+h)],u=t[2*(r+h)+1];if(n){var p=n.a*l+n.c*u+n.tx;u=n.b*l+n.d*u+n.ty,l=p}h++,e.push(l/o.width,u/o.height)}var c=i.baseTexture;(o.width<c.width||o.height<c.height)&&this.adjustUvs(e,i,a,s)},e.prototype.adjustUvs=function(t,e,i,r){for(var s=e.baseTexture,n=1e-6,h=i+2*r,a=e.frame,o=a.width/s.width,l=a.height/s.height,u=a.x/a.width,p=a.y/a.height,c=Math.floor(t[i]+n),f=Math.floor(t[i+1]+n),d=i+2;d<h;d+=2)c=Math.min(c,Math.floor(t[d]+n)),f=Math.min(f,Math.floor(t[d+1]+n));u-=c,p-=f;for(d=i;d<h;d+=2)t[d]=(t[d]+u)*o,t[d+1]=(t[d+1]+p)*l},e.BATCHABLE_SIZE=100,e}(i.BatchGeometry),R=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.width=0,e.alignment=.5,e.native=!1,e.cap=exports.LINE_CAP.BUTT,e.join=exports.LINE_JOIN.MITER,e.miterLimit=10,e}return u(e,t),e.prototype.clone=function(){var t=new e;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t.width=this.width,t.alignment=this.alignment,t.native=this.native,t.cap=this.cap,t.join=this.join,t.miterLimit=this.miterLimit,t},e.prototype.reset=function(){t.prototype.reset.call(this),this.color=0,this.alignment=.5,this.width=0,this.native=!1},e}(o),L=new Float32Array(3),N={},O=function(t){function e(e){void 0===e&&(e=null);var r=t.call(this)||this;return r.shader=null,r.pluginName="batch",r.currentPath=null,r.batches=[],r.batchTint=-1,r.batchDirty=-1,r.vertexData=null,r._fillStyle=new o,r._lineStyle=new R,r._matrix=null,r._holeMode=!1,r.state=i.State.for2d(),r._geometry=e||new C,r._geometry.refCount++,r._transformID=-1,r.tint=16777215,r.blendMode=n.BLEND_MODES.NORMAL,r}return u(e,t),Object.defineProperty(e.prototype,"geometry",{get:function(){return this._geometry},enumerable:!1,configurable:!0}),e.prototype.clone=function(){return this.finishPoly(),new e(this._geometry)},Object.defineProperty(e.prototype,"blendMode",{get:function(){return this.state.blendMode},set:function(t){this.state.blendMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fill",{get:function(){return this._fillStyle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"line",{get:function(){return this._lineStyle},enumerable:!1,configurable:!0}),e.prototype.lineStyle=function(t,e,i,r,s){return void 0===t&&(t=null),void 0===e&&(e=0),void 0===i&&(i=1),void 0===r&&(r=.5),void 0===s&&(s=!1),"number"==typeof t&&(t={width:t,color:e,alpha:i,alignment:r,native:s}),this.lineTextureStyle(t)},e.prototype.lineTextureStyle=function(t){t=Object.assign({width:0,texture:i.Texture.WHITE,color:t&&t.texture?16777215:0,alpha:1,matrix:null,alignment:.5,native:!1,cap:exports.LINE_CAP.BUTT,join:exports.LINE_JOIN.MITER,miterLimit:10},t),this.currentPath&&this.startPoly();var e=t.width>0&&t.alpha>0;return e?(t.matrix&&(t.matrix=t.matrix.clone(),t.matrix.invert()),Object.assign(this._lineStyle,{visible:e},t)):this._lineStyle.reset(),this},e.prototype.startPoly=function(){if(this.currentPath){var t=this.currentPath.points,e=this.currentPath.points.length;e>2&&(this.drawShape(this.currentPath),this.currentPath=new r.Polygon,this.currentPath.closeStroke=!1,this.currentPath.points.push(t[e-2],t[e-1]))}else this.currentPath=new r.Polygon,this.currentPath.closeStroke=!1},e.prototype.finishPoly=function(){this.currentPath&&(this.currentPath.points.length>2?(this.drawShape(this.currentPath),this.currentPath=null):this.currentPath.points.length=0)},e.prototype.moveTo=function(t,e){return this.startPoly(),this.currentPath.points[0]=t,this.currentPath.points[1]=e,this},e.prototype.lineTo=function(t,e){this.currentPath||this.moveTo(0,0);var i=this.currentPath.points,r=i[i.length-2],s=i[i.length-1];return r===t&&s===e||i.push(t,e),this},e.prototype._initCurve=function(t,e){void 0===t&&(t=0),void 0===e&&(e=0),this.currentPath?0===this.currentPath.points.length&&(this.currentPath.points=[t,e]):this.moveTo(t,e)},e.prototype.quadraticCurveTo=function(t,e,i,r){this._initCurve();var s=this.currentPath.points;return 0===s.length&&this.moveTo(0,0),w.curveTo(t,e,i,r,s),this},e.prototype.bezierCurveTo=function(t,e,i,r,s,n){return this._initCurve(),P.curveTo(t,e,i,r,s,n,this.currentPath.points),this},e.prototype.arcTo=function(t,e,i,r,s){this._initCurve(t,e);var n=this.currentPath.points,h=S.curveTo(t,e,i,r,s,n);if(h){var a=h.cx,o=h.cy,l=h.radius,u=h.startAngle,p=h.endAngle,c=h.anticlockwise;this.arc(a,o,l,u,p,c)}return this},e.prototype.arc=function(t,e,i,s,n,h){if(void 0===h&&(h=!1),s===n)return this;if(!h&&n<=s?n+=r.PI_2:h&&s<=n&&(s+=r.PI_2),0===n-s)return this;var a=t+Math.cos(s)*i,o=e+Math.sin(s)*i,l=this._geometry.closePointEps,u=this.currentPath?this.currentPath.points:null;if(u){var p=Math.abs(u[u.length-2]-a),c=Math.abs(u[u.length-1]-o);p<l&&c<l||u.push(a,o)}else this.moveTo(a,o),u=this.currentPath.points;return S.arc(a,o,t,e,i,s,n,h,u),this},e.prototype.beginFill=function(t,e){return void 0===t&&(t=0),void 0===e&&(e=1),this.beginTextureFill({texture:i.Texture.WHITE,color:t,alpha:e})},e.prototype.beginTextureFill=function(t){t=Object.assign({texture:i.Texture.WHITE,color:16777215,alpha:1,matrix:null},t),this.currentPath&&this.startPoly();var e=t.alpha>0;return e?(t.matrix&&(t.matrix=t.matrix.clone(),t.matrix.invert()),Object.assign(this._fillStyle,{visible:e},t)):this._fillStyle.reset(),this},e.prototype.endFill=function(){return this.finishPoly(),this._fillStyle.reset(),this},e.prototype.drawRect=function(t,e,i,s){return this.drawShape(new r.Rectangle(t,e,i,s))},e.prototype.drawRoundedRect=function(t,e,i,s,n){return this.drawShape(new r.RoundedRectangle(t,e,i,s,n))},e.prototype.drawCircle=function(t,e,i){return this.drawShape(new r.Circle(t,e,i))},e.prototype.drawEllipse=function(t,e,i,s){return this.drawShape(new r.Ellipse(t,e,i,s))},e.prototype.drawPolygon=function(){for(var t,e=arguments,i=[],s=0;s<arguments.length;s++)i[s]=e[s];var n=!0,h=i[0];h.points?(n=h.closeStroke,t=h.points):t=Array.isArray(i[0])?i[0]:i;var a=new r.Polygon(t);return a.closeStroke=n,this.drawShape(a),this},e.prototype.drawShape=function(t){return this._holeMode?this._geometry.drawHole(t,this._matrix):this._geometry.drawShape(t,this._fillStyle.clone(),this._lineStyle.clone(),this._matrix),this},e.prototype.clear=function(){return this._geometry.clear(),this._lineStyle.reset(),this._fillStyle.reset(),this._boundsID++,this._matrix=null,this._holeMode=!1,this.currentPath=null,this},e.prototype.isFastRect=function(){var t=this._geometry.graphicsData;return!(1!==t.length||t[0].shape.type!==r.SHAPES.RECT||t[0].matrix||t[0].holes.length||t[0].lineStyle.visible&&t[0].lineStyle.width)},e.prototype._render=function(t){this.finishPoly();var e=this._geometry;e.updateBatches(),e.batchable?(this.batchDirty!==e.batchDirty&&this._populateBatches(),this._renderBatched(t)):(t.batch.flush(),this._renderDirect(t))},e.prototype._populateBatches=function(){var t=this._geometry,e=this.blendMode,i=t.batches.length;this.batchTint=-1,this._transformID=-1,this.batchDirty=t.batchDirty,this.batches.length=i,this.vertexData=new Float32Array(t.points);for(var r=0;r<i;r++){var n=t.batches[r],h=n.style.color,a=new Float32Array(this.vertexData.buffer,4*n.attribStart*2,2*n.attribSize),o=new Float32Array(t.uvsFloat32.buffer,4*n.attribStart*2,2*n.attribSize),l={vertexData:a,blendMode:e,indices:new Uint16Array(t.indicesUint16.buffer,2*n.start,n.size),uvs:o,_batchRGB:s.hex2rgb(h),_tintRGB:h,_texture:n.style.texture,alpha:n.style.alpha,worldAlpha:1};this.batches[r]=l}},e.prototype._renderBatched=function(t){if(this.batches.length){t.batch.setObjectRenderer(t.plugins[this.pluginName]),this.calculateVertices(),this.calculateTints();for(var e=0,i=this.batches.length;e<i;e++){var r=this.batches[e];r.worldAlpha=this.worldAlpha*r.alpha,t.plugins[this.pluginName].render(r)}}},e.prototype._renderDirect=function(t){var e=this._resolveDirectShader(t),i=this._geometry,r=this.tint,s=this.worldAlpha,n=e.uniforms,h=i.drawCalls;n.translationMatrix=this.transform.worldTransform,n.tint[0]=(r>>16&255)/255*s,n.tint[1]=(r>>8&255)/255*s,n.tint[2]=(255&r)/255*s,n.tint[3]=s,t.shader.bind(e),t.geometry.bind(i,e),t.state.set(this.state);for(var a=0,o=h.length;a<o;a++)this._renderDrawCallDirect(t,i.drawCalls[a])},e.prototype._renderDrawCallDirect=function(t,e){for(var i=e.texArray,r=e.type,s=e.size,n=e.start,h=i.count,a=0;a<h;a++)t.texture.bind(i.elements[a],a);t.geometry.draw(r,s,n)},e.prototype._resolveDirectShader=function(t){var e=this.shader,s=this.pluginName;if(!e){if(!N[s]){for(var n=t.plugins[s].MAX_TEXTURES,h=new Int32Array(n),a=0;a<n;a++)h[a]=a;var o={tint:new Float32Array([1,1,1,1]),translationMatrix:new r.Matrix,default:i.UniformGroup.from({uSamplers:h},!0)},l=t.plugins[s]._shader.program;N[s]=new i.Shader(l,o)}e=N[s]}return e},e.prototype._calculateBounds=function(){this.finishPoly();var t=this._geometry;if(t.graphicsData.length){var e=t.bounds,i=e.minX,r=e.minY,s=e.maxX,n=e.maxY;this._bounds.addFrame(this.transform,i,r,s,n)}},e.prototype.containsPoint=function(t){return this.worldTransform.applyInverse(t,e._TEMP_POINT),this._geometry.containsPoint(e._TEMP_POINT)},e.prototype.calculateTints=function(){if(this.batchTint!==this.tint){this.batchTint=this.tint;for(var t=s.hex2rgb(this.tint,L),e=0;e<this.batches.length;e++){var i=this.batches[e],r=i._batchRGB,n=(t[0]*r[0]*255<<16)+(t[1]*r[1]*255<<8)+(0|t[2]*r[2]*255);i._tintRGB=(n>>16)+(65280&n)+((255&n)<<16)}}},e.prototype.calculateVertices=function(){var t=this.transform._worldID;if(this._transformID!==t){this._transformID=t;for(var e=this.transform.worldTransform,i=e.a,r=e.b,s=e.c,n=e.d,h=e.tx,a=e.ty,o=this._geometry.points,l=this.vertexData,u=0,p=0;p<o.length;p+=2){var c=o[p],f=o[p+1];l[u++]=i*c+s*f+h,l[u++]=n*f+r*c+a}}},e.prototype.closePath=function(){var t=this.currentPath;return t&&(t.closeStroke=!0,this.finishPoly()),this},e.prototype.setMatrix=function(t){return this._matrix=t,this},e.prototype.beginHole=function(){return this.finishPoly(),this._holeMode=!0,this},e.prototype.endHole=function(){return this.finishPoly(),this._holeMode=!1,this},e.prototype.destroy=function(e){this._geometry.refCount--,0===this._geometry.refCount&&this._geometry.dispose(),this._matrix=null,this.currentPath=null,this._lineStyle.destroy(),this._lineStyle=null,this._fillStyle.destroy(),this._fillStyle=null,this._geometry=null,this.shader=null,this.vertexData=null,this.batches.length=0,this.batches=null,t.prototype.destroy.call(this,e)},e.nextRoundedRectBehavior=!1,e._TEMP_POINT=new r.Point,e}(h.Container),B={buildPoly:c,buildCircle:f,buildRectangle:d,buildRoundedRectangle:g,buildLine:m,ArcUtils:S,BezierUtils:P,QuadraticUtils:w,BatchPart:M,FILL_COMMANDS:E,BATCH_POOL:A,DRAW_CALL_POOL:D};exports.FillStyle=o,exports.GRAPHICS_CURVES=a,exports.Graphics=O,exports.GraphicsData=T,exports.GraphicsGeometry=C,exports.LineStyle=R,exports.graphicsUtils=B;
//# sourceMappingURL=graphics.min.js.map
