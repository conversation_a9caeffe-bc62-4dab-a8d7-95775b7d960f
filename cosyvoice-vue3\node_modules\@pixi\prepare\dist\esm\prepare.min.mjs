/*!
 * @pixi/prepare - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/prepare is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{settings as t}from"@pixi/settings";import{Texture as e,BaseTexture as i,ExtensionType as r}from"@pixi/core";import{Graphics as o}from"@pixi/graphics";import{Ticker as n,UPDATE_PRIORITY as s}from"@pixi/ticker";import{Container as u}from"@pixi/display";import{Text as a,TextStyle as h,TextMetrics as p}from"@pixi/text";t.UPLOADS_PER_FRAME=4;var f=function(t,e){return f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},f(t,e)};var l=function(){function t(t){this.maxItemsPerFrame=t,this.itemsLeft=0}return t.prototype.beginFrame=function(){this.itemsLeft=this.maxItemsPerFrame},t.prototype.allowedToUpload=function(){return this.itemsLeft-- >0},t}();function c(t,i){var r=!1;if(t&&t._textures&&t._textures.length)for(var o=0;o<t._textures.length;o++)if(t._textures[o]instanceof e){var n=t._textures[o].baseTexture;-1===i.indexOf(n)&&(i.push(n),r=!0)}return r}function d(t,e){if(t.baseTexture instanceof i){var r=t.baseTexture;return-1===e.indexOf(r)&&e.push(r),!0}return!1}function m(t,i){if(t._texture&&t._texture instanceof e){var r=t._texture.baseTexture;return-1===i.indexOf(r)&&i.push(r),!0}return!1}function k(t,e){return e instanceof a&&(e.updateText(!0),!0)}function g(t,e){if(e instanceof h){var i=e.toFontString();return p.measureFont(i),!0}return!1}function x(t,e){if(t instanceof a){-1===e.indexOf(t.style)&&e.push(t.style),-1===e.indexOf(t)&&e.push(t);var i=t._texture.baseTexture;return-1===e.indexOf(i)&&e.push(i),!0}return!1}function y(t,e){return t instanceof h&&(-1===e.indexOf(t)&&e.push(t),!0)}var H=function(){function e(e){var i=this;this.limiter=new l(t.UPLOADS_PER_FRAME),this.renderer=e,this.uploadHookHelper=null,this.queue=[],this.addHooks=[],this.uploadHooks=[],this.completes=[],this.ticking=!1,this.delayedTick=function(){i.queue&&i.prepareItems()},this.registerFindHook(x),this.registerFindHook(y),this.registerFindHook(c),this.registerFindHook(d),this.registerFindHook(m),this.registerUploadHook(k),this.registerUploadHook(g)}return e.prototype.upload=function(t,e){var i=this;return"function"==typeof t&&(e=t,t=null),new Promise((function(r){t&&i.add(t);var o=function(){null==e||e(),r()};i.queue.length?(i.completes.push(o),i.ticking||(i.ticking=!0,n.system.addOnce(i.tick,i,s.UTILITY))):o()}))},e.prototype.tick=function(){setTimeout(this.delayedTick,0)},e.prototype.prepareItems=function(){for(this.limiter.beginFrame();this.queue.length&&this.limiter.allowedToUpload();){var t=this.queue[0],e=!1;if(t&&!t._destroyed)for(var i=0,r=this.uploadHooks.length;i<r;i++)if(this.uploadHooks[i](this.uploadHookHelper,t)){this.queue.shift(),e=!0;break}e||this.queue.shift()}if(this.queue.length)n.system.addOnce(this.tick,this,s.UTILITY);else{this.ticking=!1;var o=this.completes.slice(0);this.completes.length=0;for(i=0,r=o.length;i<r;i++)o[i]()}},e.prototype.registerFindHook=function(t){return t&&this.addHooks.push(t),this},e.prototype.registerUploadHook=function(t){return t&&this.uploadHooks.push(t),this},e.prototype.add=function(t){for(var e=0,i=this.addHooks.length;e<i&&!this.addHooks[e](t,this.queue);e++);if(t instanceof u)for(e=t.children.length-1;e>=0;e--)this.add(t.children[e]);return this},e.prototype.destroy=function(){this.ticking&&n.system.remove(this.tick,this),this.ticking=!1,this.addHooks=null,this.uploadHooks=null,this.renderer=null,this.completes=null,this.queue=null,this.limiter=null,this.uploadHookHelper=null},e}();function v(t,e){return e instanceof i&&(e._glTextures[t.CONTEXT_UID]||t.texture.bind(e),!0)}function _(t,e){if(!(e instanceof o))return!1;var i=e.geometry;e.finishPoly(),i.updateBatches();for(var r=i.batches,n=0;n<r.length;n++){var s=r[n].style.texture;s&&v(t,s.baseTexture)}return i.batchable||t.geometry.bind(i,e._resolveDirectShader(t)),!0}function T(t,e){return t instanceof o&&(e.push(t),!0)}var b=function(t){function e(e){var i=t.call(this,e)||this;return i.uploadHookHelper=i.renderer,i.registerFindHook(T),i.registerUploadHook(v),i.registerUploadHook(_),i}return function(t,e){function i(){this.constructor=t}f(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(e,t),e.extension={name:"prepare",type:r.RendererPlugin},e}(H),F=function(){function t(t){this.maxMilliseconds=t,this.frameStart=0}return t.prototype.beginFrame=function(){this.frameStart=Date.now()},t.prototype.allowedToUpload=function(){return Date.now()-this.frameStart<this.maxMilliseconds},t}();export{H as BasePrepare,l as CountLimiter,b as Prepare,F as TimeLimiter};
//# sourceMappingURL=prepare.min.mjs.map
