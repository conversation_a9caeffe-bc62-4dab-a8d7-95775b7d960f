/**
 * 🚨 紧急内存修复工具
 * 针对日志分析发现的内存爆炸问题的紧急修复
 */

export class EmergencyMemoryFix {
  private static instance: EmergencyMemoryFix;
  private isProcessing = false;

  private constructor() {}

  public static getInstance(): EmergencyMemoryFix {
    if (!EmergencyMemoryFix.instance) {
      EmergencyMemoryFix.instance = new EmergencyMemoryFix();
    }
    return EmergencyMemoryFix.instance;
  }

  /**
   * 🚨 立即执行紧急内存清理（减少过度清理频率）
   */
  public async emergencyCleanup(): Promise<void> {
    if (this.isProcessing) {
      console.log('⚠️ 紧急清理正在进行中，跳过重复调用');
      return;
    }

    // 🔑 优化：增强防重复机制，设置15秒冷却期，并检查上传状态
    const lastCleanupTime = (window as any)._lastEmergencyCleanup || 0;
    const now = Date.now();
    
    // 检查是否在上传状态
    const isUploading = (window as any)._isUploadingFile || false;
    
    if (now - lastCleanupTime < 15000) { // 15秒内不重复清理
      console.log('⚠️ 距离上次清理不足15秒，跳过以避免重复清理循环');
      return;
    }
    
    if (isUploading) {
      console.log('⚠️ 检测到文件上传中，延迟内存清理避免影响上传性能');
      return;
    }
    
    (window as any)._lastEmergencyCleanup = now;

    this.isProcessing = true;
    console.log('🚨 开始紧急内存清理...');

    try {
      // 1. 立即清理所有图片缓存
      this.clearAllImageCaches();

      // 2. 强制清理DOM中的大图片
      this.forceClearLargeImages();

      // 3. 清理WebSocket连接
      this.cleanupWebSocketConnections();

      // 4. 强制垃圾回收
      this.forceGarbageCollection();

      // 5. 延迟检查内存情况
      setTimeout(() => {
        this.checkMemoryAfterCleanup();
      }, 2000);

      console.log('✅ 紧急内存清理完成');
    } catch (error) {
      console.error('❌ 紧急内存清理失败:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 🧹 清理所有图片缓存
   */
  private clearAllImageCaches(): void {
    try {
      // 清理SafeImage全局缓存
      const globalCache = (window as any).globalUrlCache;
      if (globalCache && typeof globalCache.clear === 'function') {
        globalCache.clear();
        console.log('✅ SafeImage全局缓存已清理');
      }

      // 清理时间戳缓存
      const timestampCache = (window as any).cacheTimestamps;
      if (timestampCache && typeof timestampCache.clear === 'function') {
        timestampCache.clear();
      }

      // 发送全局清理事件
      window.dispatchEvent(new CustomEvent('clear-image-cache'));
      window.dispatchEvent(new CustomEvent('clear-comic-gallery-cache'));

      console.log('✅ 所有图片缓存已清理');
    } catch (error) {
      console.warn('⚠️ 图片缓存清理失败:', error);
    }
  }

  /**
   * 🖼️ 强制清理DOM中的大图片
   */
  private forceClearLargeImages(): void {
    try {
      let cleanedCount = 0;
      
      // 查找所有图片元素
      const images = document.querySelectorAll('img') as NodeListOf<HTMLImageElement>;
      
      images.forEach(img => {
        // 检查图片尺寸，清理大图片
        if (img.naturalWidth > 1000 || img.naturalHeight > 1000) {
          // 不直接移除，而是替换为小尺寸版本
          if (img.src.startsWith('data:image/')) {
            // 🔧 对于Base64图片，使用CSS占位符替代（避免生成新的Base64数据）
            img.src = ''; // 清空src释放内存
            img.className += ' memory-optimized-placeholder';
            cleanedCount++;
          } else {
            // 对于HTTP图片，设置最大尺寸
            img.style.maxWidth = '400px';
            img.style.maxHeight = '300px';
            img.style.objectFit = 'cover';
            cleanedCount++;
          }
        }
      });

      if (cleanedCount > 0) {
        console.log(`🖼️ 强制优化了${cleanedCount}张大图片`);
      }
    } catch (error) {
      console.warn('⚠️ 大图片清理失败:', error);
    }
  }

  /**
   * 🔌 清理WebSocket连接
   */
  private cleanupWebSocketConnections(): void {
    try {
      const masterWS = (window as any).masterWebSocketManager;
      if (masterWS && typeof masterWS.cleanupAllConnections === 'function') {
        masterWS.cleanupAllConnections();
        console.log('✅ WebSocket连接已清理');
      }

      // 清理僵尸连接
      const cleanupZombies = (window as any).cleanupAllWebSocketConnections;
      if (cleanupZombies && typeof cleanupZombies === 'function') {
        cleanupZombies();
      }
    } catch (error) {
      console.warn('⚠️ WebSocket清理失败:', error);
    }
  }

  /**
   * 🗑️ 强制垃圾回收
   */
  private forceGarbageCollection(): void {
    try {
      // 尝试手动垃圾回收
      if ((window as any).gc) {
        (window as any).gc();
        console.log('✅ 强制垃圾回收执行');
      }

      // 清理可能的内存泄漏
      if (performance && (performance as any).measureUserAgentSpecificMemory) {
        (performance as any).measureUserAgentSpecificMemory().then((result: any) => {
          console.log('📊 内存测量结果:', result);
        }).catch(() => {
          // 忽略错误
        });
      }
    } catch (error) {
      console.warn('⚠️ 垃圾回收失败:', error);
    }
  }

  /**
   * 📊 检查清理后的内存情况
   */
  private checkMemoryAfterCleanup(): void {
    try {
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const totalMB = memory.totalJSHeapSize / 1024 / 1024;
        
        console.log('📊 清理后内存状况:', {
          使用内存: `${usedMB.toFixed(1)}MB`,
          总内存: `${totalMB.toFixed(1)}MB`,
          使用率: `${(usedMB/totalMB*100).toFixed(1)}%`
        });

        // 如果内存仍然过高，发出警告
        if (usedMB > 500) {
          console.warn('⚠️ 清理后内存仍然过高，建议刷新页面');
          this.suggestPageRefresh();
        } else {
          console.log('✅ 内存使用已恢复正常');
        }
      }
    } catch (error) {
      console.warn('⚠️ 内存检查失败:', error);
    }
  }

  /**
   * 🔄 建议页面刷新
   */
  private suggestPageRefresh(): void {
    const suggestion = '内存使用仍然过高，建议刷新页面以获得最佳性能。是否刷新？';
    
    // 在控制台显示建议
    console.log('💡 优化建议:', suggestion);
    
    // 可以通过事件通知组件
    window.dispatchEvent(new CustomEvent('memory-refresh-suggested', {
      detail: { message: suggestion }
    }));
  }

  /**
   * 📈 获取当前内存使用情况
   */
  public getCurrentMemoryUsage(): { used: number; total: number } | null {
    try {
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        const memory = (performance as any).memory;
        return {
          used: memory.usedJSHeapSize / 1024 / 1024,
          total: memory.totalJSHeapSize / 1024 / 1024
        };
      }
    } catch (error) {
      console.warn('⚠️ 内存使用情况获取失败:', error);
    }
    return null;
  }

  /**
   * 🎯 针对大图片上传的优化
   */
  public optimizeForLargeImageUpload(): void {
    console.log('🎯 为大图片上传优化内存...');
    
    // 预先清理缓存
    this.clearAllImageCaches();
    
    // 限制并发图片处理
    this.limitConcurrentImageProcessing();
    
    // 设置图片尺寸限制
    this.setImageSizeLimit();
  }

  /**
   * 🚦 限制并发图片处理
   */
  private limitConcurrentImageProcessing(): void {
    // 如果有正在处理的图片，暂停新的处理
    const processingImages = document.querySelectorAll('img[src^="data:image/"]:not([data-optimized])');
    
    if (processingImages.length > 5) {
      console.log('🚦 检测到过多并发图片处理，进行优化...');
      
      // 延迟处理部分图片（避免Base64重复生成）
      processingImages.forEach((img, index) => {
        if (index > 3) { // 只保留前4张图片立即处理
          const imgElement = img as HTMLImageElement;
          const originalSrc = imgElement.src;
          imgElement.src = ''; // 暂时清空释放内存
          imgElement.className += ' memory-optimized-placeholder'; // 使用CSS占位符
          
          // 延迟加载
          setTimeout(() => {
            imgElement.src = originalSrc;
            imgElement.className = imgElement.className.replace('memory-optimized-placeholder', '');
            imgElement.setAttribute('data-optimized', 'true');
          }, index * 200); // 每张图片延迟200ms
        }
      });
    }
  }

  /**
   * 📏 设置图片尺寸限制
   */
  private setImageSizeLimit(): void {
    // 为所有SafeImage组件设置尺寸限制
    const images = document.querySelectorAll('.safe-image-container img');
    
    images.forEach(img => {
      const imgElement = img as HTMLImageElement;
      imgElement.style.maxWidth = '400px';
      imgElement.style.maxHeight = '300px';
      imgElement.style.objectFit = 'cover';
    });
    
    console.log(`📏 为${images.length}张图片设置了尺寸限制`);
  }
}

// 导出单例实例
export const emergencyMemoryFix = EmergencyMemoryFix.getInstance();

// 🔑 页面加载时的内存初始化监控
if (typeof window !== 'undefined') {
  (window as any).emergencyMemoryFix = emergencyMemoryFix;
  (window as any).emergencyCleanup = () => emergencyMemoryFix.emergencyCleanup();
  (window as any).getMemoryUsage = () => emergencyMemoryFix.getCurrentMemoryUsage();
  (window as any).optimizeForUpload = () => emergencyMemoryFix.optimizeForLargeImageUpload();
  
  // 🚫 禁用自动内存监控 - 发现监控本身消耗内存
  console.log('🚫 EmergencyMemoryFix 自动监控已禁用 - 减少初始内存占用');
  console.log('💡 如需内存检查，请手动调用: emergencyMemoryFix.getCurrentMemoryUsage()');
  
  // 注释掉自动监控逻辑，避免初始化时的内存消耗
  // let initialMemory: number | null = null;
  // 
  // // 记录初始内存
  // setTimeout(() => {
  //   const memory = emergencyMemoryFix.getCurrentMemoryUsage();
  //   if (memory) {
  //     initialMemory = memory.used;
  //     console.log(`📊 [内存监控] 页面初始化后内存: ${initialMemory.toFixed(1)}MB`);
  //     
  //     if (initialMemory > 400) {
  //       console.warn(`⚠️ [内存警告] 初始内存过高: ${initialMemory.toFixed(1)}MB > 400MB`);
  //       console.log('💡 建议检查：');
  //       console.log('  1. ImageRestoreService是否正确延迟初始化');
  //       console.log('  2. 是否有大型数据结构在页面加载时创建');
  //       console.log('  3. WebSocket连接是否正确管理');
  //     }
  //   }
  // }, 2000);
  // 
  // // 5秒后再次检查，看内存是否稳定
  // setTimeout(() => {
  //   const memory = emergencyMemoryFix.getCurrentMemoryUsage();
  //   if (memory && initialMemory) {
  //     const memoryChange = memory.used - initialMemory;
  //     console.log(`📊 [内存监控] 5秒后内存: ${memory.used.toFixed(1)}MB (变化: ${memoryChange > 0 ? '+' : ''}${memoryChange.toFixed(1)}MB)`);
  //     
  //     if (memory.used > 300) {
  //       console.warn(`⚠️ [内存警告] 5秒后内存仍然过高: ${memory.used.toFixed(1)}MB`);
  //     } else {
  //       console.log(`✅ [内存优化] 内存使用正常: ${memory.used.toFixed(1)}MB`);
  //     }
  //   }
  // }, 7000);
  
  // 暴露手动内存检查函数
  (window as any).checkMemoryStatus = () => {
    const memory = emergencyMemoryFix.getCurrentMemoryUsage();
    if (memory) {
      console.log(`📊 当前内存使用: ${memory.used.toFixed(1)}MB / ${memory.total.toFixed(1)}MB`);
      return memory;
    }
    return null;
  };
}