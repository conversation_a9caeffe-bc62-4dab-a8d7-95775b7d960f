/*!
 * @pixi/mixin-get-child-by-name - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-get-child-by-name is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{DisplayObject as i,Container as e}from"@pixi/display";i.prototype.name=null,e.prototype.getChildByName=function(i,e){for(var r=0,t=this.children.length;r<t;r++)if(this.children[r].name===i)return this.children[r];if(e)for(r=0,t=this.children.length;r<t;r++){var n=this.children[r];if(n.getChildByName){var h=n.getChildByName(i,!0);if(h)return h}}return null};
//# sourceMappingURL=mixin-get-child-by-name.min.mjs.map
