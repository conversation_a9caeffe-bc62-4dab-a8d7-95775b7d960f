{"version": 3, "file": "polyfill.min.js", "sources": ["../../src/globalThis.ts", "../../../../node_modules/promise-polyfill/src/index.js", "../../../../node_modules/promise-polyfill/src/finally.js", "../../../../node_modules/promise-polyfill/src/allSettled.js", "../../src/Promise.ts", "../../../../node_modules/object-assign/index.js", "../../src/Object.assign.ts", "../../src/requestAnimationFrame.ts", "../../src/Math.sign.ts", "../../src/Number.isInteger.ts", "../../src/index.ts"], "sourcesContent": ["if (typeof globalThis === 'undefined')\n{\n    if (typeof self !== 'undefined')\n    {\n        // covers browsers\n        // @ts-expect-error not-writable ts(2540) error only on node\n        self.globalThis = self;\n    }\n    else if (typeof global !== 'undefined')\n    {\n        // covers versions of Node < 12\n        // @ts-expect-error not-writable ts(2540) error only on node\n        global.globalThis = global;\n    }\n}\n", "import promiseFinally from './finally';\nimport allSettled from './allSettled';\n\n// Store setTimeout reference so promise-polyfill will be unaffected by\n// other code modifying setTimeout (like sinon.useFakeTimers())\nvar setTimeoutFunc = setTimeout;\n\nfunction isArray(x) {\n  return Boolean(x && typeof x.length !== 'undefined');\n}\n\nfunction noop() {}\n\n// Polyfill for Function.prototype.bind\nfunction bind(fn, thisArg) {\n  return function() {\n    fn.apply(thisArg, arguments);\n  };\n}\n\n/**\n * @constructor\n * @param {Function} fn\n */\nfunction Promise(fn) {\n  if (!(this instanceof Promise))\n    throw new TypeError('Promises must be constructed via new');\n  if (typeof fn !== 'function') throw new TypeError('not a function');\n  /** @type {!number} */\n  this._state = 0;\n  /** @type {!boolean} */\n  this._handled = false;\n  /** @type {Promise|undefined} */\n  this._value = undefined;\n  /** @type {!Array<!Function>} */\n  this._deferreds = [];\n\n  doResolve(fn, this);\n}\n\nfunction handle(self, deferred) {\n  while (self._state === 3) {\n    self = self._value;\n  }\n  if (self._state === 0) {\n    self._deferreds.push(deferred);\n    return;\n  }\n  self._handled = true;\n  Promise._immediateFn(function() {\n    var cb = self._state === 1 ? deferred.onFulfilled : deferred.onRejected;\n    if (cb === null) {\n      (self._state === 1 ? resolve : reject)(deferred.promise, self._value);\n      return;\n    }\n    var ret;\n    try {\n      ret = cb(self._value);\n    } catch (e) {\n      reject(deferred.promise, e);\n      return;\n    }\n    resolve(deferred.promise, ret);\n  });\n}\n\nfunction resolve(self, newValue) {\n  try {\n    // Promise Resolution Procedure: https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    if (newValue === self)\n      throw new TypeError('A promise cannot be resolved with itself.');\n    if (\n      newValue &&\n      (typeof newValue === 'object' || typeof newValue === 'function')\n    ) {\n      var then = newValue.then;\n      if (newValue instanceof Promise) {\n        self._state = 3;\n        self._value = newValue;\n        finale(self);\n        return;\n      } else if (typeof then === 'function') {\n        doResolve(bind(then, newValue), self);\n        return;\n      }\n    }\n    self._state = 1;\n    self._value = newValue;\n    finale(self);\n  } catch (e) {\n    reject(self, e);\n  }\n}\n\nfunction reject(self, newValue) {\n  self._state = 2;\n  self._value = newValue;\n  finale(self);\n}\n\nfunction finale(self) {\n  if (self._state === 2 && self._deferreds.length === 0) {\n    Promise._immediateFn(function() {\n      if (!self._handled) {\n        Promise._unhandledRejectionFn(self._value);\n      }\n    });\n  }\n\n  for (var i = 0, len = self._deferreds.length; i < len; i++) {\n    handle(self, self._deferreds[i]);\n  }\n  self._deferreds = null;\n}\n\n/**\n * @constructor\n */\nfunction Handler(onFulfilled, onRejected, promise) {\n  this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n  this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n  this.promise = promise;\n}\n\n/**\n * Take a potentially misbehaving resolver function and make sure\n * onFulfilled and onRejected are only called once.\n *\n * Makes no guarantees about asynchrony.\n */\nfunction doResolve(fn, self) {\n  var done = false;\n  try {\n    fn(\n      function(value) {\n        if (done) return;\n        done = true;\n        resolve(self, value);\n      },\n      function(reason) {\n        if (done) return;\n        done = true;\n        reject(self, reason);\n      }\n    );\n  } catch (ex) {\n    if (done) return;\n    done = true;\n    reject(self, ex);\n  }\n}\n\nPromise.prototype['catch'] = function(onRejected) {\n  return this.then(null, onRejected);\n};\n\nPromise.prototype.then = function(onFulfilled, onRejected) {\n  // @ts-ignore\n  var prom = new this.constructor(noop);\n\n  handle(this, new Handler(onFulfilled, onRejected, prom));\n  return prom;\n};\n\nPromise.prototype['finally'] = promiseFinally;\n\nPromise.all = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.all accepts an array'));\n    }\n\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      try {\n        if (val && (typeof val === 'object' || typeof val === 'function')) {\n          var then = val.then;\n          if (typeof then === 'function') {\n            then.call(\n              val,\n              function(val) {\n                res(i, val);\n              },\n              reject\n            );\n            return;\n          }\n        }\n        args[i] = val;\n        if (--remaining === 0) {\n          resolve(args);\n        }\n      } catch (ex) {\n        reject(ex);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n};\n\nPromise.allSettled = allSettled;\n\nPromise.resolve = function(value) {\n  if (value && typeof value === 'object' && value.constructor === Promise) {\n    return value;\n  }\n\n  return new Promise(function(resolve) {\n    resolve(value);\n  });\n};\n\nPromise.reject = function(value) {\n  return new Promise(function(resolve, reject) {\n    reject(value);\n  });\n};\n\nPromise.race = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.race accepts an array'));\n    }\n\n    for (var i = 0, len = arr.length; i < len; i++) {\n      Promise.resolve(arr[i]).then(resolve, reject);\n    }\n  });\n};\n\n// Use polyfill for setImmediate for performance gains\nPromise._immediateFn =\n  // @ts-ignore\n  (typeof setImmediate === 'function' &&\n    function(fn) {\n      // @ts-ignore\n      setImmediate(fn);\n    }) ||\n  function(fn) {\n    setTimeoutFunc(fn, 0);\n  };\n\nPromise._unhandledRejectionFn = function _unhandledRejectionFn(err) {\n  if (typeof console !== 'undefined' && console) {\n    console.warn('Possible Unhandled Promise Rejection:', err); // eslint-disable-line no-console\n  }\n};\n\nexport default Promise;\n", "/**\n * @this {Promise}\n */\nfunction finallyConstructor(callback) {\n  var constructor = this.constructor;\n  return this.then(\n    function(value) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        return value;\n      });\n    },\n    function(reason) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        // @ts-ignore\n        return constructor.reject(reason);\n      });\n    }\n  );\n}\n\nexport default finallyConstructor;\n", "function allSettled(arr) {\n  var P = this;\n  return new P(function(resolve, reject) {\n    if (!(arr && typeof arr.length !== 'undefined')) {\n      return reject(\n        new TypeError(\n          typeof arr +\n            ' ' +\n            arr +\n            ' is not iterable(cannot read property Symbol(Symbol.iterator))'\n        )\n      );\n    }\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      if (val && (typeof val === 'object' || typeof val === 'function')) {\n        var then = val.then;\n        if (typeof then === 'function') {\n          then.call(\n            val,\n            function(val) {\n              res(i, val);\n            },\n            function(e) {\n              args[i] = { status: 'rejected', reason: e };\n              if (--remaining === 0) {\n                resolve(args);\n              }\n            }\n          );\n          return;\n        }\n      }\n      args[i] = { status: 'fulfilled', value: val };\n      if (--remaining === 0) {\n        resolve(args);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n}\n\nexport default allSettled;\n", "import Polyfill from 'promise-polyfill';\n\n// Support for IE 9 - 11 which does not include Promises\nif (!globalThis.Promise)\n{\n    globalThis.Promise = Polyfill;\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "// References:\n// https://github.com/sindresorhus/object-assign\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n\nimport objectAssign from 'object-assign';\n\nif (!Object.assign)\n{\n    Object.assign = objectAssign;\n}\n", "// References:\n// http://paulirish.com/2011/requestanimationframe-for-smart-animating/\n// https://gist.github.com/1579671\n// http://updates.html5rocks.com/2012/05/requestAnimationFrame-API-now-with-sub-millisecond-precision\n// https://gist.github.com/timhall/4078614\n// https://github.com/Financial-Times/polyfill-service/tree/master/polyfills/requestAnimationFrame\n\n// Expected to be used with Browserfiy\n// Browserify automatically detects the use of `global` and passes the\n// correct reference of `global`, `globalThis`, and finally `window`\n\nconst ONE_FRAME_TIME = 16;\n\n// Date.now\nif (!(Date.now && Date.prototype.getTime))\n{\n    Date.now = function now(): number\n    {\n        return new Date().getTime();\n    };\n}\n\n// performance.now\nif (!(globalThis.performance && globalThis.performance.now))\n{\n    const startTime = Date.now();\n\n    if (!globalThis.performance)\n    {\n        (globalThis as any).performance = {};\n    }\n\n    globalThis.performance.now = (): number => Date.now() - startTime;\n}\n\n// requestAnimationFrame\nlet lastTime = Date.now();\nconst vendors = ['ms', 'moz', 'webkit', 'o'];\n\nfor (let x = 0; x < vendors.length && !globalThis.requestAnimationFrame; ++x)\n{\n    const p = vendors[x];\n\n    globalThis.requestAnimationFrame = (globalThis as any)[`${p}RequestAnimationFrame`];\n    globalThis.cancelAnimationFrame = (globalThis as any)[`${p}CancelAnimationFrame`]\n        || (globalThis as any)[`${p}CancelRequestAnimationFrame`];\n}\n\nif (!globalThis.requestAnimationFrame)\n{\n    globalThis.requestAnimationFrame = (callback: (...parms: any[]) => void): number =>\n    {\n        if (typeof callback !== 'function')\n        {\n            throw new TypeError(`${callback}is not a function`);\n        }\n\n        const currentTime = Date.now();\n        let delay = ONE_FRAME_TIME + lastTime - currentTime;\n\n        if (delay < 0)\n        {\n            delay = 0;\n        }\n\n        lastTime = currentTime;\n\n        return globalThis.self.setTimeout(() =>\n        {\n            lastTime = Date.now();\n            callback(performance.now());\n        }, delay);\n    };\n}\n\nif (!globalThis.cancelAnimationFrame)\n{\n    globalThis.cancelAnimationFrame = (id: number): void => clearTimeout(id);\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/sign\n\nif (!Math.sign)\n{\n    Math.sign = function mathSign(x): number\n    {\n        x = Number(x);\n\n        if (x === 0 || isNaN(x))\n        {\n            return x;\n        }\n\n        return x > 0 ? 1 : -1;\n    };\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n\nif (!Number.isInteger)\n{\n    Number.isInteger = function numberIsInteger(value): boolean\n    {\n        return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n    };\n}\n", "import './globalThis';\nimport './Promise';\nimport './Object.assign';\nimport './requestAnimationFrame';\nimport './Math.sign';\nimport './Number.isInteger';\n\nif (!globalThis.ArrayBuffer)\n{\n    (globalThis as any).ArrayBuffer = Array;\n}\n\nif (!globalThis.Float32Array)\n{\n    (globalThis as any).Float32Array = Array;\n}\n\nif (!globalThis.Uint32Array)\n{\n    (globalThis as any).Uint32Array = Array;\n}\n\nif (!globalThis.Uint16Array)\n{\n    (globalThis as any).Uint16Array = Array;\n}\n\nif (!globalThis.Uint8Array)\n{\n    (globalThis as any).Uint8Array = Array;\n}\n\nif (!globalThis.Int32Array)\n{\n    (globalThis as any).Int32Array = Array;\n}\n"], "names": ["globalThis", "self", "global", "setTimeoutFunc", "setTimeout", "isArray", "x", "Boolean", "length", "noop", "Promise", "fn", "this", "TypeError", "_state", "_handled", "_value", "undefined", "_deferreds", "doResolve", "handle", "deferred", "_immediateFn", "cb", "onFulfilled", "onRejected", "ret", "e", "reject", "promise", "resolve", "push", "newValue", "then", "finale", "thisArg", "apply", "arguments", "_unhandledRejectionFn", "i", "len", "Handler", "done", "value", "reason", "ex", "Promise$1", "prototype", "prom", "constructor", "callback", "all", "arr", "args", "Array", "slice", "call", "remaining", "res", "val", "allSettled", "status", "race", "setImmediate", "err", "console", "warn", "Polyfill", "getOwnPropertySymbols", "Object", "hasOwnProperty", "propIsEnumerable", "propertyIsEnumerable", "toObject", "objectAssign", "assign", "test1", "String", "getOwnPropertyNames", "test2", "fromCharCode", "map", "n", "join", "test3", "split", "for<PERSON>ach", "letter", "keys", "shouldUseNative", "target", "source", "from", "symbols", "to", "s", "key", "Date", "now", "getTime", "performance", "startTime_1", "lastTime", "vendors", "requestAnimationFrame", "p", "cancelAnimationFrame", "currentTime", "delay", "id", "clearTimeout", "Math", "sign", "Number", "isNaN", "isInteger", "isFinite", "floor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Float32Array", "Uint32Array", "Uint16Array", "Uint8Array", "Int32Array"], "mappings": ";;;;;;;yBAA0B,oBAAfA,aAEa,oBAATC,KAIPA,KAAKD,WAAaC,KAEK,oBAAXC,SAIZA,OAAOF,WAAaE,SCP5B,IAAIC,EAAiBC,WAErB,SAASC,EAAQC,GACf,OAAOC,QAAQD,QAAyB,IAAbA,EAAEE,QAG/B,SAASC,KAaT,SAASC,EAAQC,GACf,KAAMC,gBAAgBF,GACpB,MAAM,IAAIG,UAAU,wCACtB,GAAkB,mBAAPF,EAAmB,MAAM,IAAIE,UAAU,kBAElDD,KAAKE,OAAS,EAEdF,KAAKG,UAAW,EAEhBH,KAAKI,YAASC,EAEdL,KAAKM,WAAa,GAElBC,EAAUR,EAAIC,MAGhB,SAASQ,EAAOnB,EAAMoB,GACpB,KAAuB,IAAhBpB,EAAKa,QACVb,EAAOA,EAAKe,OAEM,IAAhBf,EAAKa,QAITb,EAAKc,UAAW,EAChBL,EAAQY,cAAa,WACnB,IAAIC,EAAqB,IAAhBtB,EAAKa,OAAeO,EAASG,YAAcH,EAASI,WAC7D,GAAW,OAAPF,EAAJ,CAIA,IAAIG,EACJ,IACEA,EAAMH,EAAGtB,EAAKe,QACd,MAAOW,GAEP,YADAC,EAAOP,EAASQ,QAASF,GAG3BG,EAAQT,EAASQ,QAASH,QAVP,IAAhBzB,EAAKa,OAAegB,EAAUF,GAAQP,EAASQ,QAAS5B,EAAKe,YAPhEf,EAAKiB,WAAWa,KAAKV,GAqBzB,SAASS,EAAQ7B,EAAM+B,GACrB,IAEE,GAAIA,IAAa/B,EACf,MAAM,IAAIY,UAAU,6CACtB,GACEmB,IACqB,iBAAbA,GAA6C,mBAAbA,GACxC,CACA,IAAIC,EAAOD,EAASC,KACpB,GAAID,aAAoBtB,EAItB,OAHAT,EAAKa,OAAS,EACdb,EAAKe,OAASgB,OACdE,EAAOjC,GAEF,GAAoB,mBAATgC,EAEhB,YADAd,GApEMR,EAoESsB,EApELE,EAoEWH,EAnEpB,WACLrB,EAAGyB,MAAMD,EAASE,aAkEkBpC,GAIpCA,EAAKa,OAAS,EACdb,EAAKe,OAASgB,EACdE,EAAOjC,GACP,MAAO0B,GACPC,EAAO3B,EAAM0B,GA5EjB,IAAchB,EAAIwB,EAgFlB,SAASP,EAAO3B,EAAM+B,GACpB/B,EAAKa,OAAS,EACdb,EAAKe,OAASgB,EACdE,EAAOjC,GAGT,SAASiC,EAAOjC,GACM,IAAhBA,EAAKa,QAA2C,IAA3Bb,EAAKiB,WAAWV,QACvCE,EAAQY,cAAa,WACdrB,EAAKc,UACRL,EAAQ4B,sBAAsBrC,EAAKe,WAKzC,IAAK,IAAIuB,EAAI,EAAGC,EAAMvC,EAAKiB,WAAWV,OAAQ+B,EAAIC,EAAKD,IACrDnB,EAAOnB,EAAMA,EAAKiB,WAAWqB,IAE/BtC,EAAKiB,WAAa,KAMpB,SAASuB,EAAQjB,EAAaC,EAAYI,GACxCjB,KAAKY,YAAqC,mBAAhBA,EAA6BA,EAAc,KACrEZ,KAAKa,WAAmC,mBAAfA,EAA4BA,EAAa,KAClEb,KAAKiB,QAAUA,EASjB,SAASV,EAAUR,EAAIV,GACrB,IAAIyC,GAAO,EACX,IACE/B,GACE,SAASgC,GACHD,IACJA,GAAO,EACPZ,EAAQ7B,EAAM0C,OAEhB,SAASC,GACHF,IACJA,GAAO,EACPd,EAAO3B,EAAM2C,OAGjB,MAAOC,GACP,GAAIH,EAAM,OACVA,GAAO,EACPd,EAAO3B,EAAM4C,IAIVC,EAACC,UAAiB,MAAI,SAAStB,GACpC,OAAOb,KAAKqB,KAAK,KAAMR,IAGlBqB,EAACC,UAAUd,KAAO,SAAST,EAAaC,GAE7C,IAAIuB,EAAO,IAAIpC,KAAKqC,YAAYxC,GAGhC,OADAW,EAAOR,KAAM,IAAI6B,EAAQjB,EAAaC,EAAYuB,IAC3CA,GAGTtC,EAAQqC,UAAmB,QCjK3B,SAA4BG,GAC1B,IAAID,EAAcrC,KAAKqC,YACvB,OAAOrC,KAAKqB,MACV,SAASU,GAEP,OAAOM,EAAYnB,QAAQoB,KAAYjB,MAAK,WAC1C,OAAOU,QAGX,SAASC,GAEP,OAAOK,EAAYnB,QAAQoB,KAAYjB,MAAK,WAE1C,OAAOgB,EAAYrB,OAAOgB,UDsJlClC,EAAQyC,IAAM,SAASC,GACrB,OAAO,IAAI1C,GAAQ,SAASoB,EAASF,GACnC,IAAKvB,EAAQ+C,GACX,OAAOxB,EAAO,IAAIf,UAAU,iCAG9B,IAAIwC,EAAOC,MAAMP,UAAUQ,MAAMC,KAAKJ,GACtC,GAAoB,IAAhBC,EAAK7C,OAAc,OAAOsB,EAAQ,IACtC,IAAI2B,EAAYJ,EAAK7C,OAErB,SAASkD,EAAInB,EAAGoB,GACd,IACE,GAAIA,IAAuB,iBAARA,GAAmC,mBAARA,GAAqB,CACjE,IAAI1B,EAAO0B,EAAI1B,KACf,GAAoB,mBAATA,EAQT,YAPAA,EAAKuB,KACHG,GACA,SAASA,GACPD,EAAInB,EAAGoB,KAET/B,GAKNyB,EAAKd,GAAKoB,EACU,KAAdF,GACJ3B,EAAQuB,GAEV,MAAOR,GACPjB,EAAOiB,IAIX,IAAK,IAAIN,EAAI,EAAGA,EAAIc,EAAK7C,OAAQ+B,IAC/BmB,EAAInB,EAAGc,EAAKd,QAKlB7B,EAAQkD,WE9MR,SAAoBR,GAElB,OAAO,IADCxC,MACK,SAASkB,EAASF,GAC7B,IAAMwB,QAA6B,IAAfA,EAAI5C,OACtB,OAAOoB,EACL,IAAIf,iBACKuC,EACL,IACAA,EACA,mEAIR,IAAIC,EAAOC,MAAMP,UAAUQ,MAAMC,KAAKJ,GACtC,GAAoB,IAAhBC,EAAK7C,OAAc,OAAOsB,EAAQ,IACtC,IAAI2B,EAAYJ,EAAK7C,OAErB,SAASkD,EAAInB,EAAGoB,GACd,GAAIA,IAAuB,iBAARA,GAAmC,mBAARA,GAAqB,CACjE,IAAI1B,EAAO0B,EAAI1B,KACf,GAAoB,mBAATA,EAaT,YAZAA,EAAKuB,KACHG,GACA,SAASA,GACPD,EAAInB,EAAGoB,MAET,SAAShC,GACP0B,EAAKd,GAAK,CAAEsB,OAAQ,WAAYjB,OAAQjB,GACpB,KAAd8B,GACJ3B,EAAQuB,MAOlBA,EAAKd,GAAK,CAAEsB,OAAQ,YAAalB,MAAOgB,GACpB,KAAdF,GACJ3B,EAAQuB,GAIZ,IAAK,IAAId,EAAI,EAAGA,EAAIc,EAAK7C,OAAQ+B,IAC/BmB,EAAInB,EAAGc,EAAKd,QFqKlB7B,EAAQoB,QAAU,SAASa,GACzB,OAAIA,GAA0B,iBAAVA,GAAsBA,EAAMM,cAAgBvC,EACvDiC,EAGF,IAAIjC,GAAQ,SAASoB,GAC1BA,EAAQa,OAIZjC,EAAQkB,OAAS,SAASe,GACxB,OAAO,IAAIjC,GAAQ,SAASoB,EAASF,GACnCA,EAAOe,OAIXjC,EAAQoD,KAAO,SAASV,GACtB,OAAO,IAAI1C,GAAQ,SAASoB,EAASF,GACnC,IAAKvB,EAAQ+C,GACX,OAAOxB,EAAO,IAAIf,UAAU,kCAG9B,IAAK,IAAI0B,EAAI,EAAGC,EAAMY,EAAI5C,OAAQ+B,EAAIC,EAAKD,IACzC7B,EAAQoB,QAAQsB,EAAIb,IAAIN,KAAKH,EAASF,OAM5ClB,EAAQY,aAEmB,mBAAjByC,cACN,SAASpD,GAEPoD,aAAapD,KAEjB,SAASA,GACPR,EAAeQ,EAAI,IAGvBD,EAAQ4B,sBAAwB,SAA+B0B,GACtC,oBAAZC,SAA2BA,SACpCA,QAAQC,KAAK,wCAAyCF,IGvPrDhE,WAAWU,UAEZV,WAAWU,QAAUyD,GCGzB,IAAIC,EAAwBC,OAAOD,sBAC/BE,EAAiBD,OAAOtB,UAAUuB,eAClCC,EAAmBF,OAAOtB,UAAUyB,qBAExC,SAASC,EAASd,GACjB,GAAIA,MAAAA,EACH,MAAM,IAAI9C,UAAU,yDAGrB,OAAOwD,OAAOV,GA+Cf,IAAAe,EA5CA,WACC,IACC,IAAKL,OAAOM,OACX,OAAO,EAMR,IAAIC,EAAQ,IAAIC,OAAO,OAEvB,GADAD,EAAM,GAAK,KACkC,MAAzCP,OAAOS,oBAAoBF,GAAO,GACrC,OAAO,EAKR,IADA,IAAIG,EAAQ,GACHxC,EAAI,EAAGA,EAAI,GAAIA,IACvBwC,EAAM,IAAMF,OAAOG,aAAazC,IAAMA,EAKvC,GAAwB,eAHX8B,OAAOS,oBAAoBC,GAAOE,KAAI,SAAUC,GAC5D,OAAOH,EAAMG,MAEHC,KAAK,IACf,OAAO,EAIR,IAAIC,EAAQ,GAIZ,MAHA,uBAAuBC,MAAM,IAAIC,SAAQ,SAAUC,GAClDH,EAAMG,GAAUA,KAGf,yBADElB,OAAOmB,KAAKnB,OAAOM,OAAO,GAAIS,IAAQD,KAAK,IAM9C,MAAOnB,GAER,OAAO,GAIQyB,GAAoBpB,OAAOM,OAAS,SAAUe,EAAQC,GAKtE,QAJIC,EAEAC,cADAC,EAAKrB,EAASiB,GAGTK,EAAI,EAAGA,EAAI1D,UAAU7B,OAAQuF,IAAK,CAG1C,IAAK,IAAIC,KAFTJ,EAAOvB,OAAOhC,EAAU0D,IAGnBzB,EAAed,KAAKoC,EAAMI,KAC7BF,EAAGE,GAAOJ,EAAKI,IAIjB,GAAI5B,EAAuB,CAC1ByB,EAAUzB,EAAsBwB,GAChC,IAAK,IAAIrD,EAAI,EAAGA,EAAIsD,EAAQrF,OAAQ+B,IAC/BgC,EAAiBf,KAAKoC,EAAMC,EAAQtD,MACvCuD,EAAGD,EAAQtD,IAAMqD,EAAKC,EAAQtD,MAMlC,OAAOuD,GClFHzB,OAAOM,SAERN,OAAOM,OAASD,GCepB,GATMuB,KAAKC,KAAOD,KAAKlD,UAAUoD,UAE7BF,KAAKC,IAAM,WAEP,OAAO,IAAID,MAAOE,aAKpBnG,WAAWoG,cAAepG,WAAWoG,YAAYF,IACvD,CACI,IAAMG,EAAYJ,KAAKC,MAElBlG,WAAWoG,cAEXpG,WAAmBoG,YAAc,IAGtCpG,WAAWoG,YAAYF,IAAM,WAAc,OAAAD,KAAKC,MAAQG,GAO5D,IAHA,IAAIC,EAAWL,KAAKC,MACdK,EAAU,CAAC,KAAM,MAAO,SAAU,KAE/BjG,EAAI,EAAGA,EAAIiG,EAAQ/F,SAAWR,WAAWwG,wBAAyBlG,EAC3E,CACI,IAAMmG,EAAIF,EAAQjG,GAElBN,WAAWwG,sBAAyBxG,WAAsByG,EAAC,yBAC3DzG,WAAW0G,qBAAwB1G,WAAsByG,2BACjDzG,WAAsByG,EAA8B,+BAG3DzG,WAAWwG,wBAEZxG,WAAWwG,sBAAwB,SAACtD,GAEhC,GAAwB,mBAAbA,EAEP,MAAM,IAAIrC,UAAaqC,EAAQ,qBAGnC,IAAMyD,EAAcV,KAAKC,MACrBU,EA/CW,GA+CcN,EAAWK,EASxC,OAPIC,EAAQ,IAERA,EAAQ,GAGZN,EAAWK,EAEJ3G,WAAWC,KAAKG,YAAW,WAE9BkG,EAAWL,KAAKC,MAChBhD,EAASkD,YAAYF,SACtBU,KAIN5G,WAAW0G,uBAEZ1G,WAAW0G,qBAAuB,SAACG,GAAqB,OAAAC,aAAaD,KC1EpEE,KAAKC,OAEND,KAAKC,KAAO,SAAkB1G,GAI1B,OAAU,KAFVA,EAAI2G,OAAO3G,KAEI4G,MAAM5G,GAEVA,EAGJA,EAAI,EAAI,GAAK,ICXvB2G,OAAOE,YAERF,OAAOE,UAAY,SAAyBxE,GAExC,MAAwB,iBAAVA,GAAsByE,SAASzE,IAAUoE,KAAKM,MAAM1E,KAAWA,ICAhF3C,WAAWsH,cAEXtH,WAAmBsH,YAAchE,OAGjCtD,WAAWuH,eAEXvH,WAAmBuH,aAAejE,OAGlCtD,WAAWwH,cAEXxH,WAAmBwH,YAAclE,OAGjCtD,WAAWyH,cAEXzH,WAAmByH,YAAcnE,OAGjCtD,WAAW0H,aAEX1H,WAAmB0H,WAAapE,OAGhCtD,WAAW2H,aAEX3H,WAAmB2H,WAAarE"}