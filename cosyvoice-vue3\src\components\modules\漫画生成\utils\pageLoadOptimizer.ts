/**
 * 📱 页面加载流程优化器
 * 解决漫画生成页面初始700MB内存占用和卡顿问题
 */

import { styleImageOptimizer } from './styleImageOptimizer';
import { emergencyMemoryFix } from './emergencyMemoryFix';

interface LoadPhase {
  name: string;
  priority: number;
  execute: () => Promise<void>;
  memoryBudget: number; // MB
}

export class PageLoadOptimizer {
  private static instance: PageLoadOptimizer;
  private currentPhase = 0;
  private phases: LoadPhase[] = [];
  private startTime = 0;
  private memoryBaseline = 0;

  private constructor() {
    this.initializePhases();
  }

  public static getInstance(): PageLoadOptimizer {
    if (!PageLoadOptimizer.instance) {
      PageLoadOptimizer.instance = new PageLoadOptimizer();
    }
    return PageLoadOptimizer.instance;
  }

  /**
   * 🚀 初始化加载阶段
   */
  private initializePhases(): void {
    this.phases = [
      {
        name: '核心组件初始化',
        priority: 1,
        memoryBudget: 100,
        execute: async () => {
          console.log('🎯 阶段1: 核心组件初始化...');
          // 等待基础组件加载
          await this.waitForElement('.comic-main-panel-container');
          await this.optimizeInitialRender();
        }
      },
      {
        name: '风格配置优化',
        priority: 2,
        memoryBudget: 150,
        execute: async () => {
          console.log('🎨 阶段2: 风格配置优化...');
          // 优化风格图片加载
          await styleImageOptimizer.optimizeStyleImages();
          await this.limitStyleImageConcurrency();
        }
      },
      {
        name: '作品画廊延迟加载',
        priority: 3,
        memoryBudget: 200,
        execute: async () => {
          console.log('🖼️ 阶段3: 作品画廊延迟加载...');
          await this.optimizeComicGallery();
          await this.implementImageLazyLoading();
        }
      },
      {
        name: '内存监控启动',
        priority: 4,
        memoryBudget: 220,
        execute: async () => {
          console.log('📊 阶段4: 内存监控启动...');
          await this.startMemoryMonitoring();
          await this.setupMemoryAlerts();
        }
      }
    ];
  }

  /**
   * 🚀 开始优化加载流程
   */
  public async optimizePageLoad(): Promise<void> {
    this.startTime = Date.now();
    this.memoryBaseline = this.getCurrentMemory();
    
    console.log('🚀 开始页面加载优化流程...', {
      phases: this.phases.length,
      memoryBaseline: `${this.memoryBaseline.toFixed(1)}MB`
    });

    for (let i = 0; i < this.phases.length; i++) {
      const phase = this.phases[i];
      this.currentPhase = i + 1;
      
      const phaseStart = Date.now();
      const memoryBefore = this.getCurrentMemory();

      try {
        console.log(`📍 [阶段${this.currentPhase}/${this.phases.length}] ${phase.name}`, {
          内存预算: `${phase.memoryBudget}MB`,
          当前内存: `${memoryBefore.toFixed(1)}MB`
        });

        await phase.execute();

        const memoryAfter = this.getCurrentMemory();
        const phaseTime = Date.now() - phaseStart;
        const memoryIncrease = memoryAfter - memoryBefore;

        console.log(`✅ [阶段${this.currentPhase}] 完成`, {
          耗时: `${phaseTime}ms`,
          内存变化: `${memoryIncrease > 0 ? '+' : ''}${memoryIncrease.toFixed(1)}MB`,
          当前内存: `${memoryAfter.toFixed(1)}MB`
        });

        // 检查内存是否超出预算
        if (memoryAfter > phase.memoryBudget) {
          console.warn(`⚠️ [阶段${this.currentPhase}] 内存超出预算`, {
            实际: `${memoryAfter.toFixed(1)}MB`,
            预算: `${phase.memoryBudget}MB`,
            超出: `${(memoryAfter - phase.memoryBudget).toFixed(1)}MB`
          });
          
          // 执行紧急清理
          await emergencyMemoryFix.emergencyCleanup();
        }

        // 阶段间隔，让系统喘息
        if (i < this.phases.length - 1) {
          await this.sleep(100);
        }

      } catch (error) {
        console.error(`❌ [阶段${this.currentPhase}] 失败:`, error);
        // 继续下一阶段，不因单个阶段失败而中断
      }
    }

    const totalTime = Date.now() - this.startTime;
    const finalMemory = this.getCurrentMemory();
    const totalIncrease = finalMemory - this.memoryBaseline;

    console.log('🎉 页面加载优化完成!', {
      总耗时: `${totalTime}ms`,
      内存变化: `${this.memoryBaseline.toFixed(1)}MB → ${finalMemory.toFixed(1)}MB`,
      净增长: `${totalIncrease > 0 ? '+' : ''}${totalIncrease.toFixed(1)}MB`,
      状态: finalMemory < 300 ? '✅ 优秀' : finalMemory < 500 ? '⚠️ 可接受' : '❌ 需要优化'
    });
  }

  /**
   * 🎯 优化初始渲染
   */
  private async optimizeInitialRender(): Promise<void> {
    // 隐藏非关键元素，减少初始渲染压力
    const nonCriticalElements = document.querySelectorAll('.gallery-section, .debug-panel');
    nonCriticalElements.forEach(el => {
      (el as HTMLElement).style.visibility = 'hidden';
    });

    // 延迟显示
    setTimeout(() => {
      nonCriticalElements.forEach(el => {
        (el as HTMLElement).style.visibility = 'visible';
      });
    }, 1000);
  }

  /**
   * 🎨 限制风格图片并发
   */
  private async limitStyleImageConcurrency(): Promise<void> {
    const styleImages = document.querySelectorAll('.style-preset img');
    const maxConcurrent = 2; // 最多同时加载2张风格图片

    if (styleImages.length > maxConcurrent) {
      console.log(`🚦 限制风格图片并发: ${styleImages.length} → ${maxConcurrent}`);
      
      // 暂停除前2张外的所有图片加载
      styleImages.forEach((img, index) => {
        if (index >= maxConcurrent) {
          const imgElement = img as HTMLImageElement;
          const originalSrc = imgElement.src;
          imgElement.src = ''; // 暂停加载
          imgElement.setAttribute('data-delayed-src', originalSrc);
          
          // 延迟加载
          setTimeout(() => {
            imgElement.src = originalSrc;
            imgElement.removeAttribute('data-delayed-src');
          }, index * 500); // 每张延迟500ms
        }
      });
    }
  }

  /**
   * 🖼️ 优化作品画廊
   */
  private async optimizeComicGallery(): Promise<void> {
    // 等待ComicGallery加载
    await this.waitForElement('.comic-gallery');
    
    // 限制初始显示数量
    const comicItems = document.querySelectorAll('.comic-item');
    const initialLimit = 6; // 初始只显示6个作品

    if (comicItems.length > initialLimit) {
      console.log(`📚 限制画廊初始显示: ${comicItems.length} → ${initialLimit}`);
      
      comicItems.forEach((item, index) => {
        if (index >= initialLimit) {
          (item as HTMLElement).style.display = 'none';
          item.setAttribute('data-lazy-comic', 'true');
        }
      });

      // 添加"显示更多"功能
      this.addShowMoreButton();
    }
  }

  /**
   * 👁️ 实施图片懒加载
   */
  private async implementImageLazyLoading(): Promise<void> {
    const images = document.querySelectorAll('img:not([data-lazy-optimized])');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          
          // 设置图片尺寸限制
          img.style.maxWidth = '400px';
          img.style.maxHeight = '300px';
          img.style.objectFit = 'cover';
          
          img.setAttribute('data-lazy-optimized', 'true');
          observer.unobserve(img);
        }
      });
    });

    images.forEach(img => observer.observe(img));
  }

  /**
   * 📊 启动内存监控
   */
  private async startMemoryMonitoring(): Promise<void> {
    // 设置内存监控间隔
    setInterval(() => {
      const currentMemory = this.getCurrentMemory();
      
      if (currentMemory > 500) {
        console.warn('⚠️ 内存使用过高:', `${currentMemory.toFixed(1)}MB`);
        emergencyMemoryFix.emergencyCleanup();
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 🚨 设置内存告警
   */
  private async setupMemoryAlerts(): Promise<void> {
    // 监听内存相关事件
    window.addEventListener('beforeunload', () => {
      console.log('📊 页面卸载时内存:', `${this.getCurrentMemory().toFixed(1)}MB`);
    });

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时清理缓存
        emergencyMemoryFix.emergencyCleanup();
      }
    });
  }

  /**
   * 🔘 添加"显示更多"按钮
   */
  private addShowMoreButton(): void {
    const gallery = document.querySelector('.comic-gallery .gallery-content');
    if (!gallery) return;

    const showMoreBtn = document.createElement('button');
    showMoreBtn.textContent = '显示更多作品';
    showMoreBtn.className = 'show-more-btn';
    showMoreBtn.style.cssText = `
      width: 100%;
      padding: 10px;
      margin: 10px 0;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
    `;

    showMoreBtn.onclick = () => {
      const hiddenItems = document.querySelectorAll('[data-lazy-comic="true"]');
      const showCount = Math.min(6, hiddenItems.length);
      
      for (let i = 0; i < showCount; i++) {
        (hiddenItems[i] as HTMLElement).style.display = 'block';
        hiddenItems[i].removeAttribute('data-lazy-comic');
      }

      if (hiddenItems.length <= showCount) {
        showMoreBtn.remove();
      }
    };

    gallery.appendChild(showMoreBtn);
  }

  /**
   * ⏱️ 等待元素出现
   */
  private waitForElement(selector: string, timeout = 5000): Promise<Element> {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * 😴 延迟执行
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 📊 获取当前内存使用
   */
  private getCurrentMemory(): number {
    try {
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        return (performance as any).memory.usedJSHeapSize / 1024 / 1024;
      }
    } catch (error) {
      console.warn('⚠️ 无法获取内存信息:', error);
    }
    return 0;
  }

  /**
   * 📈 获取优化进度
   */
  public getProgress(): {
    currentPhase: number;
    totalPhases: number;
    phaseName: string;
    progress: number;
  } {
    return {
      currentPhase: this.currentPhase,
      totalPhases: this.phases.length,
      phaseName: this.phases[this.currentPhase - 1]?.name || '',
      progress: (this.currentPhase / this.phases.length) * 100
    };
  }
}

// 导出单例实例
export const pageLoadOptimizer = PageLoadOptimizer.getInstance();

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).pageLoadOptimizer = pageLoadOptimizer;
  (window as any).optimizePageLoad = () => pageLoadOptimizer.optimizePageLoad();
}