{"version": 3, "file": "mesh.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/MeshBatchUvs.ts", "../../src/Mesh.ts", "../../src/MeshMaterial.ts", "../../src/MeshGeometry.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import type { TextureMatrix, Buffer } from '@pixi/core';\n\n/**\n * Class controls cache for UV mapping from Texture normal space to BaseTexture normal space.\n * @memberof PIXI\n */\nexport class MeshBatchUvs\n{\n    /** UV Buffer data. */\n    public readonly data: Float32Array;\n\n    /** Buffer with normalized UV's. */\n    public uvBuffer: Buffer;\n\n    /** Material UV matrix. */\n    public uvMatrix: TextureMatrix;\n\n    private _bufferUpdateId: number;\n    private _textureUpdateId: number;\n\n    // Internal-only properties\n    _updateID: number;\n\n    /**\n     * @param uvBuffer - Buffer with normalized uv's\n     * @param uvMatrix - Material UV matrix\n     */\n    constructor(uvBuffer: Buffer, uvMatrix: TextureMatrix)\n    {\n        this.uvBuffer = uvBuffer;\n        this.uvMatrix = uvMatrix;\n        this.data = null;\n\n        this._bufferUpdateId = -1;\n        this._textureUpdateId = -1;\n        this._updateID = 0;\n    }\n\n    /**\n     * Updates\n     * @param forceUpdate - force the update\n     */\n    public update(forceUpdate?: boolean): void\n    {\n        if (!forceUpdate\n            && this._bufferUpdateId === this.uvBuffer._updateID\n            && this._textureUpdateId === this.uvMatrix._updateID\n        )\n        {\n            return;\n        }\n\n        this._bufferUpdateId = this.uvBuffer._updateID;\n        this._textureUpdateId = this.uvMatrix._updateID;\n\n        const data = this.uvBuffer.data as Float32Array;\n\n        if (!this.data || this.data.length !== data.length)\n        {\n            (this.data as any) = new Float32Array(data.length);\n        }\n\n        this.uvMatrix.multiplyUvs(data, this.data);\n\n        this._updateID++;\n    }\n}\n", "import { State } from '@pixi/core';\nimport { Point, Polygon } from '@pixi/math';\nimport type { BLEND_MODES } from '@pixi/constants';\nimport { DRAW_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\nimport { settings } from '@pixi/settings';\nimport { MeshBatchUvs } from './MeshBatchUvs';\nimport type { MeshMaterial } from './MeshMaterial';\n\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Texture, Renderer, Geometry, Buffer, Shader } from '@pixi/core';\nimport type { IPointData } from '@pixi/math';\n\nconst tempPoint = new Point();\nconst tempPolygon = new Polygon();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Mesh extends GlobalMixins.Mesh {}\n\n/**\n * Base mesh class.\n *\n * This class empowers you to have maximum flexibility to render any kind of WebGL visuals you can think of.\n * This class assumes a certain level of WebGL knowledge.\n * If you know a bit this should abstract enough away to make your life easier!\n *\n * Pretty much ALL WebGL can be broken down into the following:\n * - Geometry - The structure and data for the mesh. This can include anything from positions, uvs, normals, colors etc..\n * - Shader - This is the shader that PixiJS will render the geometry with (attributes in the shader must match the geometry)\n * - State - This is the state of WebGL required to render the mesh.\n *\n * Through a combination of the above elements you can render anything you want, 2D or 3D!\n * @memberof PIXI\n */\nexport class Mesh<T extends Shader = MeshMaterial> extends Container\n{\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Mesh objects.\n     * @type {PIXI.Shader|PIXI.MeshMaterial}\n     */\n    public shader: T;\n\n    /**\n     * Represents the WebGL state the Mesh required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    public state: State;\n\n    /** The way the Mesh should be drawn, can be any of the {@link PIXI.DRAW_MODES} constants. */\n    public drawMode: DRAW_MODES;\n\n    /**\n     * Typically the index of the IndexBuffer where to start drawing.\n     * @default 0\n     */\n    public start: number;\n\n    /**\n     * How much of the geometry to draw, by default `0` renders everything.\n     * @default 0\n     */\n    public size: number;\n\n    private _geometry: Geometry;\n\n    /** This is the caching layer used by the batcher. */\n    private vertexData: Float32Array;\n\n    /** If geometry is changed used to decide to re-transform the vertexData. */\n    private vertexDirty: number;\n    private _transformID: number;\n\n    /** Internal roundPixels field. */\n    private _roundPixels: boolean;\n\n    /** Batched UV's are cached for atlas textures. */\n    private batchUvs: MeshBatchUvs;\n\n    // Internal-only properties\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    uvs: Float32Array;\n\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    indices: Uint16Array;\n    _tintRGB: number;\n    _texture: Texture;\n\n    /**\n     * @param geometry - The geometry the mesh will use.\n     * @param {PIXI.MeshMaterial} shader - The shader the mesh will use.\n     * @param state - The state that the WebGL context is required to be in to render the mesh\n     *        if no state is provided, uses {@link PIXI.State.for2d} to create a 2D state for PixiJS.\n     * @param drawMode - The drawMode, can be any of the {@link PIXI.DRAW_MODES} constants.\n     */\n    constructor(geometry: Geometry, shader: T, state?: State, drawMode: DRAW_MODES = DRAW_MODES.TRIANGLES)\n    {\n        super();\n\n        this.geometry = geometry;\n        this.shader = shader;\n        this.state = state || State.for2d();\n        this.drawMode = drawMode;\n        this.start = 0;\n        this.size = 0;\n\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = new Float32Array(1);\n        this.vertexDirty = -1;\n\n        this._transformID = -1;\n        this._roundPixels = settings.ROUND_PIXELS;\n        this.batchUvs = null;\n    }\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh objects.\n     */\n    get geometry(): Geometry\n    {\n        return this._geometry;\n    }\n\n    set geometry(value: Geometry)\n    {\n        if (this._geometry === value)\n        {\n            return;\n        }\n\n        if (this._geometry)\n        {\n            this._geometry.refCount--;\n\n            if (this._geometry.refCount === 0)\n            {\n                this._geometry.dispose();\n            }\n        }\n\n        this._geometry = value;\n\n        if (this._geometry)\n        {\n            this._geometry.refCount++;\n        }\n\n        this.vertexDirty = -1;\n    }\n\n    /**\n     * To change mesh uv's, change its uvBuffer data and increment its _updateID.\n     * @readonly\n     */\n    get uvBuffer(): Buffer\n    {\n        return this.geometry.buffers[1];\n    }\n\n    /**\n     * To change mesh vertices, change its uvBuffer data and increment its _updateID.\n     * Incrementing _updateID is optional because most of Mesh objects do it anyway.\n     * @readonly\n     */\n    get verticesBuffer(): Buffer\n    {\n        return this.geometry.buffers[0];\n    }\n\n    /** Alias for {@link PIXI.Mesh#shader}. */\n    set material(value: T)\n    {\n        this.shader = value;\n    }\n\n    get material(): T\n    {\n        return this.shader;\n    }\n\n    /**\n     * The blend mode to be applied to the Mesh. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL;\n     */\n    set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}\n     * @default false\n     */\n    set roundPixels(value: boolean)\n    {\n        if (this._roundPixels !== value)\n        {\n            this._transformID = -1;\n        }\n        this._roundPixels = value;\n    }\n\n    get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    /**\n     * The multiply tint applied to the Mesh. This is a hex value. A value of\n     * `0xFFFFFF` will remove any tint effect.\n     *\n     * Null for non-MeshMaterial shaders\n     * @default 0xFFFFFF\n     */\n    get tint(): number\n    {\n        return 'tint' in this.shader ? (this.shader as unknown as MeshMaterial).tint : null;\n    }\n\n    set tint(value: number)\n    {\n        (this.shader as unknown as MeshMaterial).tint = value;\n    }\n\n    /** The texture that the Mesh uses. Null for non-MeshMaterial shaders */\n    get texture(): Texture\n    {\n        return 'texture' in this.shader ? (this.shader as unknown as MeshMaterial).texture : null;\n    }\n\n    set texture(value: Texture)\n    {\n        (this.shader as unknown as MeshMaterial).texture = value;\n    }\n\n    /**\n     * Standard renderer draw.\n     * @param renderer - Instance to renderer.\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // set properties for batching..\n        // TODO could use a different way to grab verts?\n        const vertices = this.geometry.buffers[0].data;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        // TODO benchmark check for attribute size..\n        if (\n            shader.batchable\n            && this.drawMode === DRAW_MODES.TRIANGLES\n            && vertices.length < Mesh.BATCHABLE_SIZE * 2\n        )\n        {\n            this._renderToBatch(renderer);\n        }\n        else\n        {\n            this._renderDefault(renderer);\n        }\n    }\n\n    /**\n     * Standard non-batching way of rendering.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderDefault(renderer: Renderer): void\n    {\n        const shader = this.shader as unknown as MeshMaterial;\n\n        shader.alpha = this.worldAlpha;\n        if (shader.update)\n        {\n            shader.update();\n        }\n\n        renderer.batch.flush();\n\n        // bind and sync uniforms..\n        shader.uniforms.translationMatrix = this.transform.worldTransform.toArray(true);\n        renderer.shader.bind(shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // bind the geometry...\n        renderer.geometry.bind(this.geometry, shader);\n\n        // then render it\n        renderer.geometry.draw(this.drawMode, this.size, this.start, this.geometry.instanceCount);\n    }\n\n    /**\n     * Rendering by using the Batch system.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderToBatch(renderer: Renderer): void\n    {\n        const geometry = this.geometry;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (shader.uvMatrix)\n        {\n            shader.uvMatrix.update();\n            this.calculateUvs();\n        }\n\n        // set properties for batching..\n        this.calculateVertices();\n        this.indices = geometry.indexBuffer.data as Uint16Array;\n        this._tintRGB = shader._tintRGB;\n        this._texture = shader.texture;\n\n        const pluginName = (this.material as unknown as MeshMaterial).pluginName;\n\n        renderer.batch.setObjectRenderer(renderer.plugins[pluginName]);\n        renderer.plugins[pluginName].render(this);\n    }\n\n    /** Updates vertexData field based on transform and vertices. */\n    public calculateVertices(): void\n    {\n        const geometry = this.geometry;\n        const verticesBuffer = geometry.buffers[0];\n        const vertices = verticesBuffer.data;\n        const vertexDirtyId = verticesBuffer._updateID;\n\n        if (vertexDirtyId === this.vertexDirty && this._transformID === this.transform._worldID)\n        {\n            return;\n        }\n\n        this._transformID = this.transform._worldID;\n\n        if (this.vertexData.length !== vertices.length)\n        {\n            this.vertexData = new Float32Array(vertices.length);\n        }\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const vertexData = this.vertexData;\n\n        for (let i = 0; i < vertexData.length / 2; i++)\n        {\n            const x = vertices[(i * 2)];\n            const y = vertices[(i * 2) + 1];\n\n            vertexData[(i * 2)] = (a * x) + (c * y) + tx;\n            vertexData[(i * 2) + 1] = (b * x) + (d * y) + ty;\n        }\n\n        if (this._roundPixels)\n        {\n            const resolution = settings.RESOLUTION;\n\n            for (let i = 0; i < vertexData.length; ++i)\n            {\n                vertexData[i] = Math.round((vertexData[i] * resolution | 0) / resolution);\n            }\n        }\n\n        this.vertexDirty = vertexDirtyId;\n    }\n\n    /** Updates uv field based on from geometry uv's or batchUvs. */\n    public calculateUvs(): void\n    {\n        const geomUvs = this.geometry.buffers[1];\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (!shader.uvMatrix.isSimple)\n        {\n            if (!this.batchUvs)\n            {\n                this.batchUvs = new MeshBatchUvs(geomUvs, shader.uvMatrix);\n            }\n            this.batchUvs.update();\n            this.uvs = this.batchUvs.data;\n        }\n        else\n        {\n            this.uvs = geomUvs.data as Float32Array;\n        }\n    }\n\n    /**\n     * Updates the bounds of the mesh as a rectangle. The bounds calculation takes the worldTransform into account.\n     * there must be a aVertexPosition attribute present in the geometry for bounds to be calculated correctly.\n     */\n    protected _calculateBounds(): void\n    {\n        this.calculateVertices();\n\n        this._bounds.addVertexData(this.vertexData, 0, this.vertexData.length);\n    }\n\n    /**\n     * Tests if a point is inside this mesh. Works only for PIXI.DRAW_MODES.TRIANGLES.\n     * @param point - The point to test.\n     * @returns - The result of the test.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        if (!this.getBounds().contains(point.x, point.y))\n        {\n            return false;\n        }\n\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const vertices = this.geometry.getBuffer('aVertexPosition').data;\n\n        const points = tempPolygon.points;\n        const indices =  this.geometry.getIndex().data;\n        const len = indices.length;\n        const step = this.drawMode === 4 ? 3 : 1;\n\n        for (let i = 0; i + 2 < len; i += step)\n        {\n            const ind0 = indices[i] * 2;\n            const ind1 = indices[i + 1] * 2;\n            const ind2 = indices[i + 2] * 2;\n\n            points[0] = vertices[ind0];\n            points[1] = vertices[ind0 + 1];\n            points[2] = vertices[ind1];\n            points[3] = vertices[ind1 + 1];\n            points[4] = vertices[ind2];\n            points[5] = vertices[ind2 + 1];\n\n            if (tempPolygon.contains(tempPoint.x, tempPoint.y))\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        if (this._cachedTexture)\n        {\n            this._cachedTexture.destroy();\n            this._cachedTexture = null;\n        }\n\n        this.geometry = null;\n        this.shader = null;\n        this.state = null;\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = null;\n    }\n\n    /** The maximum number of vertices to consider batchable. Generally, the complexity of the geometry. */\n    public static BATCHABLE_SIZE = 100;\n}\n", "import { Program, Shader, TextureMatrix } from '@pixi/core';\nimport { Matrix } from '@pixi/math';\nimport { premultiplyTintToRgba } from '@pixi/utils';\nimport fragment from './shader/mesh.frag';\nimport vertex from './shader/mesh.vert';\n\nimport type { Texture } from '@pixi/core';\nimport type { Dict } from '@pixi/utils';\n\nexport interface IMeshMaterialOptions\n{\n    alpha?: number;\n    tint?: number;\n    pluginName?: string;\n    program?: Program;\n    uniforms?: Dict<unknown>;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface MeshMaterial extends GlobalMixins.MeshMaterial {}\n\n/**\n * Slightly opinionated default shader for PixiJS 2D objects.\n * @memberof PIXI\n */\nexport class MeshMaterial extends Shader\n{\n    /**\n     * TextureMatrix instance for this Mesh, used to track Texture changes.\n     * @readonly\n     */\n    public readonly uvMatrix: TextureMatrix;\n\n    /**\n     * `true` if shader can be batch with the renderer's batch system.\n     * @default true\n     */\n    public batchable: boolean;\n\n    /**\n     * Renderer plugin for batching.\n     * @default 'batch'\n     */\n    public pluginName: string;\n\n    // Internal-only properties\n    _tintRGB: number;\n\n    /**\n     * Only do update if tint or alpha changes.\n     * @private\n     * @default false\n     */\n    private _colorDirty: boolean;\n    private _alpha: number;\n    private _tint: number;\n\n    /**\n     * @param uSampler - Texture that material uses to render.\n     * @param options - Additional options\n     * @param {number} [options.alpha=1] - Default alpha.\n     * @param {number} [options.tint=0xFFFFFF] - Default tint.\n     * @param {string} [options.pluginName='batch'] - Renderer plugin for batching.\n     * @param {PIXI.Program} [options.program=0xFFFFFF] - Custom program.\n     * @param {object} [options.uniforms] - Custom uniforms.\n     */\n    constructor(uSampler: Texture, options?: IMeshMaterialOptions)\n    {\n        const uniforms = {\n            uSampler,\n            alpha: 1,\n            uTextureMatrix: Matrix.IDENTITY,\n            uColor: new Float32Array([1, 1, 1, 1]),\n        };\n\n        // Set defaults\n        options = Object.assign({\n            tint: 0xFFFFFF,\n            alpha: 1,\n            pluginName: 'batch',\n        }, options);\n\n        if (options.uniforms)\n        {\n            Object.assign(uniforms, options.uniforms);\n        }\n\n        super(options.program || Program.from(vertex, fragment), uniforms);\n\n        this._colorDirty = false;\n\n        this.uvMatrix = new TextureMatrix(uSampler);\n        this.batchable = options.program === undefined;\n        this.pluginName = options.pluginName;\n\n        this.tint = options.tint;\n        this.alpha = options.alpha;\n    }\n\n    /** Reference to the texture being rendered. */\n    get texture(): Texture\n    {\n        return this.uniforms.uSampler;\n    }\n    set texture(value: Texture)\n    {\n        if (this.uniforms.uSampler !== value)\n        {\n            if (!this.uniforms.uSampler.baseTexture.alphaMode !== !value.baseTexture.alphaMode)\n            {\n                this._colorDirty = true;\n            }\n\n            this.uniforms.uSampler = value;\n            this.uvMatrix.texture = value;\n        }\n    }\n\n    /**\n     * This gets automatically set by the object using this.\n     * @default 1\n     */\n    set alpha(value: number)\n    {\n        if (value === this._alpha) return;\n\n        this._alpha = value;\n        this._colorDirty = true;\n    }\n    get alpha(): number\n    {\n        return this._alpha;\n    }\n\n    /**\n     * Multiply tint for the material.\n     * @default 0xFFFFFF\n     */\n    set tint(value: number)\n    {\n        if (value === this._tint) return;\n\n        this._tint = value;\n        this._tintRGB = (value >> 16) + (value & 0xff00) + ((value & 0xff) << 16);\n        this._colorDirty = true;\n    }\n    get tint(): number\n    {\n        return this._tint;\n    }\n\n    /** Gets called automatically by the Mesh. Intended to be overridden for custom {@link MeshMaterial} objects. */\n    public update(): void\n    {\n        if (this._colorDirty)\n        {\n            this._colorDirty = false;\n            const baseTexture = this.texture.baseTexture;\n\n            premultiplyTintToRgba(\n                this._tint, this._alpha, this.uniforms.uColor, (baseTexture.alphaMode as unknown as boolean)\n            );\n        }\n        if (this.uvMatrix.update())\n        {\n            this.uniforms.uTextureMatrix = this.uvMatrix.mapCoord;\n        }\n    }\n}\n", "import { TYPES } from '@pixi/constants';\nimport { <PERSON>uff<PERSON>, Geometry } from '@pixi/core';\n\nimport type { IArrayBuffer } from '@pixi/core';\n\n/**\n * Standard 2D geometry used in PixiJS.\n *\n * Geometry can be defined without passing in a style or data if required.\n *\n * ```js\n * const geometry = new PIXI.Geometry();\n *\n * geometry.addAttribute('positions', [0, 0, 100, 0, 100, 100, 0, 100], 2);\n * geometry.addAttribute('uvs', [0,0,1,0,1,1,0,1], 2);\n * geometry.addIndex([0,1,2,1,3,2]);\n *\n * ```\n * @memberof PIXI\n */\nexport class MeshGeometry extends Geometry\n{\n    // Internal-only properties\n    /**\n     * Dirty flag to limit update calls on Mesh. For example,\n     * limiting updates on a single Mesh instance with a shared Geometry\n     * within the render loop.\n     * @private\n     * @default -1\n     */\n    _updateId: number;\n\n    /**\n     * @param {Float32Array|number[]} [vertices] - Positional data on geometry.\n     * @param {Float32Array|number[]} [uvs] - Texture UVs.\n     * @param {Uint16Array|number[]} [index] - IndexBuffer\n     */\n    constructor(vertices?: IArrayBuffer, uvs?: IArrayBuffer, index?: IArrayBuffer)\n    {\n        super();\n\n        const verticesBuffer = new Buffer(vertices);\n        const uvsBuffer = new Buffer(uvs, true);\n        const indexBuffer = new Buffer(index, true, true);\n\n        this.addAttribute('aVertexPosition', verticesBuffer, 2, false, TYPES.FLOAT)\n            .addAttribute('aTextureCoord', uvsBuffer, 2, false, TYPES.FLOAT)\n            .addIndex(indexBuffer);\n\n        this._updateId = -1;\n    }\n\n    /**\n     * If the vertex position is updated.\n     * @readonly\n     * @private\n     */\n    get vertexDirtyId(): number\n    {\n        return this.buffers[0]._updateID;\n    }\n}\n"], "names": ["arguments", "Point", "Polygon", "DRAW_MODES", "State", "settings", "Container", "Matrix", "Program", "TextureMatrix", "premultiplyTintToRgba", "Shader", "<PERSON><PERSON><PERSON>", "TYPES", "Geometry"], "mappings": ";;;;;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;ICvNA;;;IAGG;AACH,QAAA,YAAA,kBAAA,YAAA;IAiBI;;;IAGG;QACH,SAAY,YAAA,CAAA,QAAgB,EAAE,QAAuB,EAAA;IAEjD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAEjB,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;IAC1B,QAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;SACtB;IAED;;;IAGG;QACI,YAAM,CAAA,SAAA,CAAA,MAAA,GAAb,UAAc,WAAqB,EAAA;IAE/B,QAAA,IAAI,CAAC,WAAW;IACT,eAAA,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;mBAChD,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,EAExD;gBACI,OAAO;IACV,SAAA;YAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IAEhD,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAoB,CAAC;IAEhD,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAClD;gBACK,IAAI,CAAC,IAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtD,SAAA;YAED,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,CAAC,SAAS,EAAE,CAAC;SACpB,CAAA;QACL,OAAC,YAAA,CAAA;IAAD,CAAC,EAAA;;ICrDD,IAAM,SAAS,GAAG,IAAIC,UAAK,EAAE,CAAC;IAC9B,IAAM,WAAW,GAAG,IAAIC,YAAO,EAAE,CAAC;IAKlC;;;;;;;;;;;;;;IAcG;AACH,QAAA,IAAA,kBAAA,UAAA,MAAA,EAAA;QAA2D,SAAS,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;IA4DhE;;;;;;IAMG;IACH,IAAA,SAAA,IAAA,CAAY,QAAkB,EAAE,MAAS,EAAE,KAAa,EAAE,QAA2C,EAAA;IAA3C,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAA,GAAuBC,oBAAU,CAAC,SAAS,CAAA,EAAA;IAArG,QAAA,IAAA,KAAA,GAEI,iBAAO,IAiBV,IAAA,CAAA;IAfG,QAAA,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,KAAI,CAAC,KAAK,GAAG,KAAK,IAAIC,UAAK,CAAC,KAAK,EAAE,CAAC;IACpC,QAAA,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,QAAA,KAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACf,QAAA,KAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IAEd,QAAA,KAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IAChB,QAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,KAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;IACtC,QAAA,KAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IAEtB,QAAA,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IACvB,QAAA,KAAI,CAAC,YAAY,GAAGC,iBAAQ,CAAC,YAAY,CAAC;IAC1C,QAAA,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;;SACxB;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IALZ;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;IAED,QAAA,GAAA,EAAA,UAAa,KAAe,EAAA;IAExB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAC5B;oBACI,OAAO;IACV,aAAA;gBAED,IAAI,IAAI,CAAC,SAAS,EAClB;IACI,gBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAE1B,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,CAAC,EACjC;IACI,oBAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC5B,iBAAA;IACJ,aAAA;IAED,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBAEvB,IAAI,IAAI,CAAC,SAAS,EAClB;IACI,gBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAC7B,aAAA;IAED,YAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;aACzB;;;IA3BA,KAAA,CAAA,CAAA;IAiCD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IAJZ;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACnC;;;IAAA,KAAA,CAAA,CAAA;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAc,CAAA,SAAA,EAAA,gBAAA,EAAA;IALlB;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACnC;;;IAAA,KAAA,CAAA,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IAKZ,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,MAAM,CAAC;aACtB;;IARD,QAAA,GAAA,EAAA,UAAa,KAAQ,EAAA;IAEjB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;aACvB;;;IAAA,KAAA,CAAA,CAAA;IAYD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAS,CAAA,SAAA,EAAA,WAAA,EAAA;IAKb,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;aAC/B;IAbD;;;;IAIG;IACH,QAAA,GAAA,EAAA,UAAc,KAAkB,EAAA;IAE5B,YAAA,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;aAChC;;;IAAA,KAAA,CAAA,CAAA;IAcD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAW,CAAA,SAAA,EAAA,aAAA,EAAA;IASf,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,YAAY,CAAC;aAC5B;IAnBD;;;;;;IAMG;IACH,QAAA,GAAA,EAAA,UAAgB,KAAc,EAAA;IAE1B,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAC/B;IACI,gBAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAC1B,aAAA;IACD,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;aAC7B;;;IAAA,KAAA,CAAA,CAAA;IAcD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAI,CAAA,SAAA,EAAA,MAAA,EAAA;IAPR;;;;;;IAMG;IACH,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,MAAM,IAAI,IAAI,CAAC,MAAM,GAAI,IAAI,CAAC,MAAkC,CAAC,IAAI,GAAG,IAAI,CAAC;aACvF;IAED,QAAA,GAAA,EAAA,UAAS,KAAa,EAAA;IAEjB,YAAA,IAAI,CAAC,MAAkC,CAAC,IAAI,GAAG,KAAK,CAAC;aACzD;;;IALA,KAAA,CAAA,CAAA;IAQD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAO,CAAA,SAAA,EAAA,SAAA,EAAA;;IAAX,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,SAAS,IAAI,IAAI,CAAC,MAAM,GAAI,IAAI,CAAC,MAAkC,CAAC,OAAO,GAAG,IAAI,CAAC;aAC7F;IAED,QAAA,GAAA,EAAA,UAAY,KAAc,EAAA;IAErB,YAAA,IAAI,CAAC,MAAkC,CAAC,OAAO,GAAG,KAAK,CAAC;aAC5D;;;IALA,KAAA,CAAA,CAAA;IAOD;;;IAGG;QACO,IAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,QAAkB,EAAA;;;IAIhC,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/C,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;;YAGtD,IACI,MAAM,CAAC,SAAS;IACb,eAAA,IAAI,CAAC,QAAQ,KAAKF,oBAAU,CAAC,SAAS;mBACtC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,EAEhD;IACI,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACjC,SAAA;IAED,aAAA;IACI,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACjC,SAAA;SACJ,CAAA;IAED;;;IAGG;QACO,IAAc,CAAA,SAAA,CAAA,cAAA,GAAxB,UAAyB,QAAkB,EAAA;IAEvC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;IAEtD,QAAA,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/B,IAAI,MAAM,CAAC,MAAM,EACjB;gBACI,MAAM,CAAC,MAAM,EAAE,CAAC;IACnB,SAAA;IAED,QAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;;IAGvB,QAAA,MAAM,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChF,QAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;YAG7B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;YAG/B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;;YAG9C,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;SAC7F,CAAA;IAED;;;IAGG;QACO,IAAc,CAAA,SAAA,CAAA,cAAA,GAAxB,UAAyB,QAAkB,EAAA;IAEvC,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/B,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;YAEtD,IAAI,MAAM,CAAC,QAAQ,EACnB;IACI,YAAA,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACzB,IAAI,CAAC,YAAY,EAAE,CAAC;IACvB,SAAA;;YAGD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAmB,CAAC;IACxD,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IAChC,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;IAE/B,QAAA,IAAM,UAAU,GAAI,IAAI,CAAC,QAAoC,CAAC,UAAU,CAAC;IAEzE,QAAA,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;YAC/D,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC7C,CAAA;;IAGM,IAAA,IAAA,CAAA,SAAA,CAAA,iBAAiB,GAAxB,YAAA;IAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,IAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3C,QAAA,IAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC;IACrC,QAAA,IAAM,aAAa,GAAG,cAAc,CAAC,SAAS,CAAC;IAE/C,QAAA,IAAI,aAAa,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,EACvF;gBACI,OAAO;IACV,SAAA;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YAE5C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAC9C;gBACI,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACvD,SAAA;IAED,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;IACzC,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACf,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACjB,QAAA,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IAEjB,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IAEnC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAC9C;gBACI,IAAM,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;IAC5B,YAAA,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEhC,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC7C,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IACpD,SAAA;YAED,IAAI,IAAI,CAAC,YAAY,EACrB;IACI,YAAA,IAAM,UAAU,GAAGE,iBAAQ,CAAC,UAAU,CAAC;IAEvC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAC1C;oBACI,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC;IAC7E,aAAA;IACJ,SAAA;IAED,QAAA,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC;SACpC,CAAA;;IAGM,IAAA,IAAA,CAAA,SAAA,CAAA,YAAY,GAAnB,YAAA;YAEI,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;IAEtD,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAC7B;IACI,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAClB;IACI,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC9D,aAAA;IACD,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACvB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACjC,SAAA;IAED,aAAA;IACI,YAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAoB,CAAC;IAC3C,SAAA;SACJ,CAAA;IAED;;;IAGG;IACO,IAAA,IAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;YAEI,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAEzB,QAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;SAC1E,CAAA;IAED;;;;IAIG;QACI,IAAa,CAAA,SAAA,CAAA,aAAA,GAApB,UAAqB,KAAiB,EAAA;IAElC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAChD;IACI,YAAA,OAAO,KAAK,CAAC;IAChB,SAAA;YAED,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAEnD,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;IAEjE,QAAA,IAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;YAClC,IAAM,OAAO,GAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC;IAC/C,QAAA,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;IAC3B,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEzC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,IAAI,EACtC;gBACI,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC5B,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBAChC,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBAEhC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;gBAC/B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;gBAC/B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAE/B,YAAA,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAClD;IACI,gBAAA,OAAO,IAAI,CAAC;IACf,aAAA;IACJ,SAAA;IAED,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;QAEM,IAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;IAE9C,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;YAEvB,IAAI,IAAI,CAAC,cAAc,EACvB;IACI,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC9B,SAAA;IAED,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IAChB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B,CAAA;;QAGa,IAAc,CAAA,cAAA,GAAG,GAAG,CAAC;QACvC,OAAC,IAAA,CAAA;KAAA,CAjc0DC,iBAAS,CAicnE;;;;;;IC9cD;;;IAGG;AACH,QAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;QAAkC,SAAM,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;IAgCpC;;;;;;;;IAQG;QACH,SAAY,YAAA,CAAA,QAAiB,EAAE,OAA8B,EAAA;YAA7D,IA+BC,KAAA,GAAA,IAAA,CAAA;IA7BG,QAAA,IAAM,QAAQ,GAAG;IACb,YAAA,QAAQ,EAAA,QAAA;IACR,YAAA,KAAK,EAAE,CAAC;gBACR,cAAc,EAAEC,WAAM,CAAC,QAAQ;IAC/B,YAAA,MAAM,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACzC,CAAC;;IAGF,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IACpB,YAAA,IAAI,EAAE,QAAQ;IACd,YAAA,KAAK,EAAE,CAAC;IACR,YAAA,UAAU,EAAE,OAAO;aACtB,EAAE,OAAO,CAAC,CAAC;YAEZ,IAAI,OAAO,CAAC,QAAQ,EACpB;gBACI,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC7C,SAAA;IAED,QAAA,KAAA,GAAA,kBAAM,OAAO,CAAC,OAAO,IAAIC,YAAO,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,IAAC,IAAA,CAAA;IAEnE,QAAA,KAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YAEzB,KAAI,CAAC,QAAQ,GAAG,IAAIC,kBAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,KAAI,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC;IAC/C,QAAA,KAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAErC,QAAA,KAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IACzB,QAAA,KAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;;SAC9B;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAO,CAAA,SAAA,EAAA,SAAA,EAAA;;IAAX,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;aACjC;IACD,QAAA,GAAA,EAAA,UAAY,KAAc,EAAA;IAEtB,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,EACpC;IACI,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,EAClF;IACI,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC3B,iBAAA;IAED,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC/B,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;IACjC,aAAA;aACJ;;;IAbA,KAAA,CAAA,CAAA;IAmBD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAK,CAAA,SAAA,EAAA,OAAA,EAAA;IAOT,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,MAAM,CAAC;aACtB;IAdD;;;IAGG;IACH,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;IAEnB,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM;sBAAE,OAAO,EAAA;IAElC,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;aAC3B;;;IAAA,KAAA,CAAA,CAAA;IAUD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAI,CAAA,SAAA,EAAA,MAAA,EAAA;IAQR,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,KAAK,CAAC;aACrB;IAfD;;;IAGG;IACH,QAAA,GAAA,EAAA,UAAS,KAAa,EAAA;IAElB,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK;sBAAE,OAAO,EAAA;IAEjC,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;IAC1E,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;aAC3B;;;IAAA,KAAA,CAAA,CAAA;;IAOM,IAAA,YAAA,CAAA,SAAA,CAAA,MAAM,GAAb,YAAA;YAEI,IAAI,IAAI,CAAC,WAAW,EACpB;IACI,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IACzB,YAAA,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C,YAAAC,2BAAqB,CACjB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAG,WAAW,CAAC,SAAgC,CAC/F,CAAC;IACL,SAAA;IACD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAC1B;gBACI,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IACzD,SAAA;SACJ,CAAA;QACL,OAAC,YAAA,CAAA;IAAD,CA/IA,CAAkCC,WAAM,CA+IvC;;ICnKD;;;;;;;;;;;;;;IAcG;AACH,QAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;QAAkC,SAAQ,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;IAYtC;;;;IAIG;IACH,IAAA,SAAA,YAAA,CAAY,QAAuB,EAAE,GAAkB,EAAE,KAAoB,EAAA;IAA7E,QAAA,IAAA,KAAA,GAEI,iBAAO,IAWV,IAAA,CAAA;IATG,QAAA,IAAM,cAAc,GAAG,IAAIC,WAAM,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAM,SAAS,GAAG,IAAIA,WAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACxC,IAAM,WAAW,GAAG,IAAIA,WAAM,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAElD,QAAA,KAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,cAAc,EAAE,CAAC,EAAE,KAAK,EAAEC,eAAK,CAAC,KAAK,CAAC;IACtE,aAAA,YAAY,CAAC,eAAe,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAEA,eAAK,CAAC,KAAK,CAAC;iBAC/D,QAAQ,CAAC,WAAW,CAAC,CAAC;IAE3B,QAAA,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;;SACvB;IAOD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAa,CAAA,SAAA,EAAA,eAAA,EAAA;IALjB;;;;IAIG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aACpC;;;IAAA,KAAA,CAAA,CAAA;QACL,OAAC,YAAA,CAAA;IAAD,CAzCA,CAAkCC,aAAQ,CAyCzC;;;;;;;;;;;;;;;;;;"}