{"name": "@pixi/filter-color-matrix", "version": "6.5.10", "main": "dist/cjs/filter-color-matrix.js", "module": "dist/esm/filter-color-matrix.mjs", "bundle": "dist/browser/filter-color-matrix.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/filter-color-matrix.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/filter-color-matrix.js"}}}, "namespace": "PIXI.filters", "description": "Filter that lets you change RGBA via a 5x4 matrix transformation", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/core": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}