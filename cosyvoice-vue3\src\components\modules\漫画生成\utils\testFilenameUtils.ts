/**
 * FilenameUtils测试工具
 * 用于验证文件名处理逻辑的正确性
 */

import { FilenameUtils } from './filenameUtils';

// 测试案例
const testCases = [
  'img_1751216521783_w42i32qfl__________SceneCard_04_00001_.png.png.png.png',
  'SceneCard_01_00001_.png.png',
  'A4_artwork.png.png.png',
  'comic_1234567890_abcdef.png',
  'style_image.jpg.png',
  'normal_image.png',
  'no_extension_file',
  'ComfyUI_temp_xyz_00001_.png.png.png.png.png'
];

export function runFilenameTests(): void {
  console.log('🧪 开始FilenameUtils测试...');
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📋 测试案例 ${index + 1}:`);
    console.log(`  原始: ${testCase}`);
    
    const validation = FilenameUtils.validateFilename(testCase);
    console.log(`  有问题: ${validation.hasIssue}`);
    if (validation.hasIssue) {
      console.log(`  问题: ${validation.issues.join(', ')}`);
    }
    console.log(`  清理后: ${validation.cleaned}`);
    
    const safeExtension = FilenameUtils.ensureExtension(validation.cleaned);
    console.log(`  安全扩展名: ${safeExtension}`);
    
    const coreFilename = FilenameUtils.extractCoreFromComfyUIFilename(testCase);
    console.log(`  核心文件名: ${coreFilename}`);
    
    const safeId = FilenameUtils.generateSafeImageId(testCase);
    console.log(`  生成的ID: ${safeId}`);
  });
  
  console.log('\n✅ FilenameUtils测试完成!');
}

// 暴露到全局
if (typeof window !== 'undefined') {
  (window as any).runFilenameTests = runFilenameTests;
  console.log('🧪 测试函数已暴露: window.runFilenameTests()');
}