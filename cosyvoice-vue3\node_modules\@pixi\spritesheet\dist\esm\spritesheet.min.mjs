/*!
 * @pixi/spritesheet - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/spritesheet is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{Rectangle as t}from"@pixi/math";import{Texture as e,BaseTexture as r,ExtensionType as s}from"@pixi/core";import{getResolutionOfUrl as i,url as o}from"@pixi/utils";import{LoaderResource as a}from"@pixi/loaders";var n=function(){function s(t,s,i){void 0===i&&(i=null),this.linkedSheets=[],this._texture=t instanceof e?t:null,this.baseTexture=t instanceof r?t:this._texture.baseTexture,this.textures={},this.animations={},this.data=s;var o=this.baseTexture.resource;this.resolution=this._updateResolution(i||(o?o.url:null)),this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}return s.prototype._updateResolution=function(t){void 0===t&&(t=null);var e=this.data.meta.scale,r=i(t,null);return null===r&&(r=void 0!==e?parseFloat(e):1),1!==r&&this.baseTexture.setResolution(r),r},s.prototype.parse=function(t){var e=this;return new Promise((function(r){e._callback=function(e){null==t||t(e),r(e)},e._batchIndex=0,e._frameKeys.length<=s.BATCH_SIZE?(e._processFrames(0),e._processAnimations(),e._parseComplete()):e._nextBatch()}))},s.prototype._processFrames=function(r){for(var i=r,o=s.BATCH_SIZE;i-r<o&&i<this._frameKeys.length;){var a=this._frameKeys[i],n=this._frames[a],u=n.frame;if(u){var l=null,h=null,c=!1!==n.trimmed&&n.sourceSize?n.sourceSize:n.frame,f=new t(0,0,Math.floor(c.w)/this.resolution,Math.floor(c.h)/this.resolution);l=n.rotated?new t(Math.floor(u.x)/this.resolution,Math.floor(u.y)/this.resolution,Math.floor(u.h)/this.resolution,Math.floor(u.w)/this.resolution):new t(Math.floor(u.x)/this.resolution,Math.floor(u.y)/this.resolution,Math.floor(u.w)/this.resolution,Math.floor(u.h)/this.resolution),!1!==n.trimmed&&n.spriteSourceSize&&(h=new t(Math.floor(n.spriteSourceSize.x)/this.resolution,Math.floor(n.spriteSourceSize.y)/this.resolution,Math.floor(u.w)/this.resolution,Math.floor(u.h)/this.resolution)),this.textures[a]=new e(this.baseTexture,l,f,h,n.rotated?2:0,n.anchor),e.addToCache(this.textures[a],a)}i++}},s.prototype._processAnimations=function(){var t=this.data.animations||{};for(var e in t){this.animations[e]=[];for(var r=0;r<t[e].length;r++){var s=t[e][r];this.animations[e].push(this.textures[s])}}},s.prototype._parseComplete=function(){var t=this._callback;this._callback=null,this._batchIndex=0,t.call(this,this.textures)},s.prototype._nextBatch=function(){var t=this;this._processFrames(this._batchIndex*s.BATCH_SIZE),this._batchIndex++,setTimeout((function(){t._batchIndex*s.BATCH_SIZE<t._frameKeys.length?t._nextBatch():(t._processAnimations(),t._parseComplete())}),0)},s.prototype.destroy=function(t){var e;for(var r in void 0===t&&(t=!1),this.textures)this.textures[r].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,t&&(null===(e=this._texture)||void 0===e||e.destroy(),this.baseTexture.destroy()),this._texture=null,this.baseTexture=null,this.linkedSheets=[]},s.BATCH_SIZE=1e3,s}(),u=function(){function t(){}return t.use=function(e,r){var s,i,u=this,l=e.name+"_image";if(e.data&&e.type===a.TYPE.JSON&&e.data.frames&&!u.resources[l]){var h=null===(i=null===(s=e.data)||void 0===s?void 0:s.meta)||void 0===i?void 0:i.related_multi_packs;if(Array.isArray(h))for(var c=function(t){if("string"!=typeof t)return"continue";var r=t.replace(".json",""),s=o.resolve(e.url.replace(u.baseUrl,""),t);if(u.resources[r]||Object.values(u.resources).some((function(t){return o.format(o.parse(t.url))===s})))return"continue";var i={crossOrigin:e.crossOrigin,loadType:a.LOAD_TYPE.XHR,xhrType:a.XHR_RESPONSE_TYPE.JSON,parentResource:e,metadata:e.metadata};u.add(r,s,i)},f=0,m=h;f<m.length;f++){c(m[f])}var p={crossOrigin:e.crossOrigin,metadata:e.metadata.imageMetadata,parentResource:e},d=t.getResourcePath(e,u.baseUrl);u.add(l,d,p,(function(t){if(t.error)r(t.error);else{var s=new n(t.texture,e.data,e.url);s.parse().then((function(){e.spritesheet=s,e.textures=s.textures,r()}))}}))}else r()},t.getResourcePath=function(t,e){return t.isDataUrl?t.data.meta.image:o.resolve(t.url.replace(e,""),t.data.meta.image)},t.extension=s.Loader,t}();export{n as Spritesheet,u as SpritesheetLoader};
//# sourceMappingURL=spritesheet.min.mjs.map
