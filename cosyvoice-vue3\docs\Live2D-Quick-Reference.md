# Live2D 快速参考指南

## 🚀 快速修复动作和表情问题

### 核心API调用

```typescript
// ✅ 正确的动作播放
const success = model.motion('动作名')
// 或者
const success = model.motion('组名', 索引, 优先级)

// ✅ 正确的表情播放  
const success = model.expression('表情名')
// 或者
const success = model.expression(索引)
```

### 常见问题排查

#### 1. 动作播放失败
```bash
# 症状：控制台显示 "所有方法都返回false"
# 解决：检查模型实际支持的动作组

console.log(model.internalModel.settings.motions)     // Cubism 2.x
console.log(model.internalModel.settings.Motions)     // Cubism 4.x
```

#### 2. 获取模型真实动作列表
```typescript
const getActualMotions = (model) => {
  const settings = model.internalModel.settings
  const motions = []
  
  // Cubism 2.x
  if (settings.motions) {
    motions.push(...Object.keys(settings.motions))
  }
  
  // Cubism 4.x  
  if (settings.Motions) {
    motions.push(...Object.keys(settings.Motions))
  }
  
  return motions
}
```

#### 3. 批量测试动作
```typescript
const testMotions = ['idle', 'main', 'tap_body', 'touch']
for (const motion of testMotions) {
  const result = model.motion(motion)
  console.log(`${motion}: ${result ? '✅' : '❌'}`)
}
```

## 🔧 调试技巧

### 检查模型状态
```typescript
console.log('模型类型:', model.constructor.name)
console.log('有motion方法:', !!model.motion)
console.log('内部设置:', model.internalModel.settings)
```

### 监控API调用
```typescript
// 包装原始方法进行调试
const originalMotion = model.motion
model.motion = function(...args) {
  console.log('动作调用:', args)
  const result = originalMotion.apply(this, args)
  console.log('调用结果:', result)
  return result
}
```

## ⚡ 常用动作名称

根据大多数Live2D模型的约定：

```typescript
// 常见动作组名
const commonMotions = [
  'idle',       // 待机动作
  'main',       // 主要动作
  'tap_body',   // 点击身体
  'flick_head', // 轻抚头部
  'shake',      // 摇晃
  'touch'       // 触摸
]

// 测试所有常见动作
commonMotions.forEach(motion => {
  try {
    const success = model.motion(motion)
    if (success) console.log(`✅ ${motion} 可用`)
  } catch (e) {
    console.log(`❌ ${motion} 不可用`)
  }
})
```

## 📱 UI集成示例

### Vue组件中的使用
```vue
<template>
  <div>
    <button 
      v-for="motion in availableMotions" 
      :key="motion"
      @click="playMotion(motion)"
    >
      {{ motion }}
    </button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const availableMotions = ref([])

// 从模型中提取真实的动作列表
const extractMotions = (model) => {
  const settings = model.internalModel.settings
  const motions = []
  
  if (settings.motions) {
    motions.push(...Object.keys(settings.motions))
  }
  if (settings.Motions) {
    motions.push(...Object.keys(settings.Motions))
  }
  
  availableMotions.value = motions
}

const playMotion = (motionName) => {
  const success = model.motion(motionName)
  console.log(`播放 ${motionName}: ${success ? '成功' : '失败'}`)
}
</script>
```

## 🛠️ 故障排除

### 问题：动作不播放
1. **检查模型是否正确加载**
   ```typescript
   console.log('模型已加载:', !!model)
   console.log('内部模型:', !!model.internalModel)
   ```

2. **检查动作方法是否存在**
   ```typescript
   console.log('motion方法:', typeof model.motion)
   ```

3. **检查实际动作组**
   ```typescript
   console.log('可用动作组:', Object.keys(model.internalModel.settings.motions || {}))
   ```

### 问题：表情不显示
```typescript
// 检查表情配置
const settings = model.internalModel.settings
console.log('Cubism 2.x 表情:', settings.expressions)
console.log('Cubism 4.x 表情:', settings.Expressions)

// 测试表情播放
try {
  const result = model.expression(0) // 使用索引
  console.log('表情播放结果:', result)
} catch (error) {
  console.error('表情播放错误:', error)
}
```

## 📚 相关资源

- [详细技术文档](./Live2D-Motion-Expression-Fix.md)
- [pixi-live2d-display 官方文档](https://guansss.github.io/pixi-live2d-display/)
- [Live2D Cubism 文档](https://docs.live2d.com/)

---

**最后更新**: 2025-01-30 