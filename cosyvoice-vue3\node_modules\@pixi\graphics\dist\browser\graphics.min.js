/*!
 * @pixi/graphics - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/graphics is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_graphics=function(t,e,i,r,n,s){"use strict";var h,a;t.LINE_JOIN=void 0,(h=t.LINE_JOIN||(t.LINE_JOIN={})).MITER="miter",h.BEVEL="bevel",h.ROUND="round",t.LINE_CAP=void 0,(a=t.LINE_CAP||(t.LINE_CAP={})).BUTT="butt",a.ROUND="round",a.SQUARE="square";var o={adaptive:!0,maxLength:10,minSegments:8,maxSegments:2048,epsilon:1e-4,_segmentsCount:function(t,e){if(void 0===e&&(e=20),!this.adaptive||!t||isNaN(t))return e;var i=Math.ceil(t/this.maxLength);return i<this.minSegments?i=this.minSegments:i>this.maxSegments&&(i=this.maxSegments),i}},l=function(){function t(){this.color=16777215,this.alpha=1,this.texture=e.Texture.WHITE,this.matrix=null,this.visible=!1,this.reset()}return t.prototype.clone=function(){var e=new t;return e.color=this.color,e.alpha=this.alpha,e.texture=this.texture,e.matrix=this.matrix,e.visible=this.visible,e},t.prototype.reset=function(){this.color=16777215,this.alpha=1,this.texture=e.Texture.WHITE,this.matrix=null,this.visible=!1},t.prototype.destroy=function(){this.texture=null,this.matrix=null},t}(),u=function(t,e){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},u(t,e)};function p(t,e){function i(){this.constructor=t}u(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}function c(t,e){var i,r;void 0===e&&(e=!1);var n=t.length;if(!(n<6)){for(var s=0,h=0,a=t[n-2],o=t[n-1];h<n;h+=2){var l=t[h],u=t[h+1];s+=(l-a)*(u+o),a=l,o=u}if(!e&&s>0||e&&s<=0){var p=n/2;for(h=p+p%2;h<n;h+=2){var c=n-h-2,f=n-h-1,d=h,y=h+1;i=[t[d],t[c]],t[c]=i[0],t[d]=i[1],r=[t[y],t[f]],t[f]=r[0],t[y]=r[1]}}}}var f={build:function(t){t.points=t.shape.points.slice()},triangulate:function(t,e){var i=t.points,n=t.holes,s=e.points,h=e.indices;if(i.length>=6){c(i,!1);for(var a=[],o=0;o<n.length;o++){var l=n[o];c(l.points,!0),a.push(i.length/2),i=i.concat(l.points)}var u=r.earcut(i,a,2);if(!u)return;var p=s.length/2;for(o=0;o<u.length;o+=3)h.push(u[o]+p),h.push(u[o+1]+p),h.push(u[o+2]+p);for(o=0;o<i.length;o++)s.push(i[o])}}},d={build:function(t){var e,r,n,s,h,a,o=t.points;if(t.type===i.SHAPES.CIRC){var l=t.shape;e=l.x,r=l.y,h=a=l.radius,n=s=0}else if(t.type===i.SHAPES.ELIP){var u=t.shape;e=u.x,r=u.y,h=u.width,a=u.height,n=s=0}else{var p=t.shape,c=p.width/2,f=p.height/2;e=p.x+c,r=p.y+f,n=c-(h=a=Math.max(0,Math.min(p.radius,Math.min(c,f)))),s=f-a}if(h>=0&&a>=0&&n>=0&&s>=0){var d=Math.ceil(2.3*Math.sqrt(h+a)),y=8*d+(n?4:0)+(s?4:0);if(o.length=y,0!==y){if(0===d)return o.length=8,o[0]=o[6]=e+n,o[1]=o[3]=r+s,o[2]=o[4]=e-n,void(o[5]=o[7]=r-s);var v=0,g=4*d+(n?2:0)+2,b=g,m=y,_=e+(I=n+h),x=e-I,P=r+(E=s);if(o[v++]=_,o[v++]=P,o[--g]=P,o[--g]=x,s){var S=r-E;o[b++]=x,o[b++]=S,o[--m]=S,o[--m]=_}for(var w=1;w<d;w++){var M=Math.PI/2*(w/d);_=e+(I=n+Math.cos(M)*h),x=e-I,P=r+(E=s+Math.sin(M)*a),S=r-E;o[v++]=_,o[v++]=P,o[--g]=P,o[--g]=x,o[b++]=x,o[b++]=S,o[--m]=S,o[--m]=_}var I,E;_=e+(I=n),x=e-I,P=r+(E=s+a),S=r-E;o[v++]=_,o[v++]=P,o[--m]=S,o[--m]=_,n&&(o[v++]=x,o[v++]=P,o[--m]=S,o[--m]=x)}}else o.length=0},triangulate:function(t,e){var r=t.points,n=e.points,s=e.indices;if(0!==r.length){var h,a,o=n.length/2,l=o;if(t.type!==i.SHAPES.RREC){var u=t.shape;h=u.x,a=u.y}else{var p=t.shape;h=p.x+p.width/2,a=p.y+p.height/2}var c=t.matrix;n.push(t.matrix?c.a*h+c.c*a+c.tx:h,t.matrix?c.b*h+c.d*a+c.ty:a),o++,n.push(r[0],r[1]);for(var f=2;f<r.length;f+=2)n.push(r[f],r[f+1]),s.push(o++,l,o);s.push(l+1,l,o)}}},y={build:function(t){var e=t.shape,i=e.x,r=e.y,n=e.width,s=e.height,h=t.points;h.length=0,h.push(i,r,i+n,r,i+n,r+s,i,r+s)},triangulate:function(t,e){var i=t.points,r=e.points,n=r.length/2;r.push(i[0],i[1],i[2],i[3],i[6],i[7],i[4],i[5]),e.indices.push(n,n+1,n+2,n+1,n+2,n+3)}};function v(t,e,i){return t+(e-t)*i}function g(t,e,i,r,n,s,h){void 0===h&&(h=[]);for(var a=h,o=0,l=0,u=0,p=0,c=0,f=0,d=0,y=0;d<=20;++d)o=v(t,i,y=d/20),l=v(e,r,y),u=v(i,n,y),p=v(r,s,y),c=v(o,u,y),f=v(l,p,y),0===d&&a[a.length-2]===c&&a[a.length-1]===f||a.push(c,f);return a}var b={build:function(t){if(B.nextRoundedRectBehavior)d.build(t);else{var e=t.shape,i=t.points,r=e.x,n=e.y,s=e.width,h=e.height,a=Math.max(0,Math.min(e.radius,Math.min(s,h)/2));i.length=0,a?(g(r,n+a,r,n,r+a,n,i),g(r+s-a,n,r+s,n,r+s,n+a,i),g(r+s,n+h-a,r+s,n+h,r+s-a,n+h,i),g(r+a,n+h,r,n+h,r,n+h-a,i)):i.push(r,n,r+s,n,r+s,n+h,r,n+h)}},triangulate:function(t,e){if(B.nextRoundedRectBehavior)d.triangulate(t,e);else{for(var i=t.points,n=e.points,s=e.indices,h=n.length/2,a=r.earcut(i,null,2),o=0,l=a.length;o<l;o+=3)s.push(a[o]+h),s.push(a[o+1]+h),s.push(a[o+2]+h);for(o=0,l=i.length;o<l;o++)n.push(i[o],i[++o])}}};function m(t,e,i,r,n,s,h,a){var o,l;h?(o=r,l=-i):(o=-r,l=i);var u=t-i*n+o,p=e-r*n+l,c=t+i*s+o,f=e+r*s+l;return a.push(u,p),a.push(c,f),2}function _(t,e,i,r,n,s,h,a){var o=i-t,l=r-e,u=Math.atan2(o,l),p=Math.atan2(n-t,s-e);a&&u<p?u+=2*Math.PI:!a&&u>p&&(p+=2*Math.PI);var c=u,f=p-u,d=Math.abs(f),y=Math.sqrt(o*o+l*l),v=1+(15*d*Math.sqrt(y)/Math.PI>>0),g=f/v;if(c+=g,a){h.push(t,e),h.push(i,r);for(var b=1,m=c;b<v;b++,m+=g)h.push(t,e),h.push(t+Math.sin(m)*y,e+Math.cos(m)*y);h.push(t,e),h.push(n,s)}else{h.push(i,r),h.push(t,e);for(b=1,m=c;b<v;b++,m+=g)h.push(t+Math.sin(m)*y,e+Math.cos(m)*y),h.push(t,e);h.push(n,s),h.push(t,e)}return 2*v}function x(e,r){e.lineStyle.native?function(t,e){var r=0,n=t.shape,s=t.points||n.points,h=n.type!==i.SHAPES.POLY||n.closeStroke;if(0!==s.length){var a=e.points,o=e.indices,l=s.length/2,u=a.length/2,p=u;for(a.push(s[0],s[1]),r=1;r<l;r++)a.push(s[2*r],s[2*r+1]),o.push(p,p+1),p++;h&&o.push(p,u)}}(e,r):function(e,r){var n=e.shape,s=e.points||n.points.slice(),h=r.closePointEps;if(0!==s.length){var a=e.lineStyle,l=new i.Point(s[0],s[1]),u=new i.Point(s[s.length-2],s[s.length-1]),p=n.type!==i.SHAPES.POLY||n.closeStroke,c=Math.abs(l.x-u.x)<h&&Math.abs(l.y-u.y)<h;if(p){s=s.slice(),c&&(s.pop(),s.pop(),u.set(s[s.length-2],s[s.length-1]));var f=.5*(l.x+u.x),d=.5*(u.y+l.y);s.unshift(f,d),s.push(f,d)}var y=r.points,v=s.length/2,g=s.length,b=y.length/2,x=a.width/2,P=x*x,S=a.miterLimit*a.miterLimit,w=s[0],M=s[1],I=s[2],E=s[3],A=0,D=0,T=-(M-E),C=w-I,R=0,L=0,N=Math.sqrt(T*T+C*C);T/=N,C/=N,T*=x,C*=x;var O=a.alignment,B=2*(1-O),U=2*O;p||(a.cap===t.LINE_CAP.ROUND?g+=_(w-T*(B-U)*.5,M-C*(B-U)*.5,w-T*B,M-C*B,w+T*U,M+C*U,y,!0)+2:a.cap===t.LINE_CAP.SQUARE&&(g+=m(w,M,T,C,B,U,!0,y))),y.push(w-T*B,M-C*B),y.push(w+T*U,M+C*U);for(var j=1;j<v-1;++j){w=s[2*(j-1)],M=s[2*(j-1)+1],I=s[2*j],E=s[2*j+1],A=s[2*(j+1)],D=s[2*(j+1)+1],T=-(M-E),C=w-I,T/=N=Math.sqrt(T*T+C*C),C/=N,T*=x,C*=x,R=-(E-D),L=I-A,R/=N=Math.sqrt(R*R+L*L),L/=N,R*=x,L*=x;var H=I-w,F=M-E,z=I-A,q=D-E,k=H*z+F*q,G=F*z-q*H,W=G<0;if(Math.abs(G)<.001*Math.abs(k))y.push(I-T*B,E-C*B),y.push(I+T*U,E+C*U),k>=0&&(a.join===t.LINE_JOIN.ROUND?g+=_(I,E,I-T*B,E-C*B,I-R*B,E-L*B,y,!1)+4:g+=2,y.push(I-R*U,E-L*U),y.push(I+R*B,E+L*B));else{var X=(-T+w)*(-C+E)-(-T+I)*(-C+M),J=(-R+A)*(-L+E)-(-R+I)*(-L+D),V=(H*J-z*X)/G,Y=(q*X-F*J)/G,Q=(V-I)*(V-I)+(Y-E)*(Y-E),Z=I+(V-I)*B,K=E+(Y-E)*B,$=I-(V-I)*U,tt=E-(Y-E)*U,et=W?B:U;Q<=Math.min(H*H+F*F,z*z+q*q)+et*et*P?a.join===t.LINE_JOIN.BEVEL||Q/P>S?(W?(y.push(Z,K),y.push(I+T*U,E+C*U),y.push(Z,K),y.push(I+R*U,E+L*U)):(y.push(I-T*B,E-C*B),y.push($,tt),y.push(I-R*B,E-L*B),y.push($,tt)),g+=2):a.join===t.LINE_JOIN.ROUND?W?(y.push(Z,K),y.push(I+T*U,E+C*U),g+=_(I,E,I+T*U,E+C*U,I+R*U,E+L*U,y,!0)+4,y.push(Z,K),y.push(I+R*U,E+L*U)):(y.push(I-T*B,E-C*B),y.push($,tt),g+=_(I,E,I-T*B,E-C*B,I-R*B,E-L*B,y,!1)+4,y.push(I-R*B,E-L*B),y.push($,tt)):(y.push(Z,K),y.push($,tt)):(y.push(I-T*B,E-C*B),y.push(I+T*U,E+C*U),a.join===t.LINE_JOIN.ROUND?g+=W?_(I,E,I+T*U,E+C*U,I+R*U,E+L*U,y,!0)+2:_(I,E,I-T*B,E-C*B,I-R*B,E-L*B,y,!1)+2:a.join===t.LINE_JOIN.MITER&&Q/P<=S&&(W?(y.push($,tt),y.push($,tt)):(y.push(Z,K),y.push(Z,K)),g+=2),y.push(I-R*B,E-L*B),y.push(I+R*U,E+L*U),g+=2)}}w=s[2*(v-2)],M=s[2*(v-2)+1],I=s[2*(v-1)],T=-(M-(E=s[2*(v-1)+1])),C=w-I,T/=N=Math.sqrt(T*T+C*C),C/=N,T*=x,C*=x,y.push(I-T*B,E-C*B),y.push(I+T*U,E+C*U),p||(a.cap===t.LINE_CAP.ROUND?g+=_(I-T*(B-U)*.5,E-C*(B-U)*.5,I-T*B,E-C*B,I+T*U,E+C*U,y,!1)+2:a.cap===t.LINE_CAP.SQUARE&&(g+=m(I,E,T,C,B,U,!1,y)));var it=r.indices,rt=o.epsilon*o.epsilon;for(j=b;j<g+b-2;++j)w=y[2*j],M=y[2*j+1],I=y[2*(j+1)],E=y[2*(j+1)+1],A=y[2*(j+2)],D=y[2*(j+2)+1],Math.abs(w*(E-D)+I*(D-M)+A*(M-E))<rt||it.push(j,j+1,j+2)}}(e,r)}var P,S=function(){function t(){}return t.curveTo=function(t,e,i,r,n,s){var h=s[s.length-2],a=s[s.length-1]-e,o=h-t,l=r-e,u=i-t,p=Math.abs(a*u-o*l);if(p<1e-8||0===n)return s[s.length-2]===t&&s[s.length-1]===e||s.push(t,e),null;var c=a*a+o*o,f=l*l+u*u,d=a*l+o*u,y=n*Math.sqrt(c)/p,v=n*Math.sqrt(f)/p,g=y*d/c,b=v*d/f,m=y*u+v*o,_=y*l+v*a,x=o*(v+g),P=a*(v+g),S=u*(y+b),w=l*(y+b);return{cx:m+t,cy:_+e,radius:n,startAngle:Math.atan2(P-_,x-m),endAngle:Math.atan2(w-_,S-m),anticlockwise:o*l>u*a}},t.arc=function(t,e,r,n,s,h,a,l,u){for(var p=a-h,c=o._segmentsCount(Math.abs(p)*s,40*Math.ceil(Math.abs(p)/i.PI_2)),f=p/(2*c),d=2*f,y=Math.cos(f),v=Math.sin(f),g=c-1,b=g%1/g,m=0;m<=g;++m){var _=f+h+d*(m+b*m),x=Math.cos(_),P=-Math.sin(_);u.push((y*x+v*P)*s+r,(y*-P+v*x)*s+n)}},t}(),w=function(){function t(){}return t.curveLength=function(t,e,i,r,n,s,h,a){for(var o=0,l=0,u=0,p=0,c=0,f=0,d=0,y=0,v=0,g=0,b=0,m=t,_=e,x=1;x<=10;++x)g=m-(y=(d=(f=(c=1-(l=x/10))*c)*c)*t+3*f*l*i+3*c*(u=l*l)*n+(p=u*l)*h),b=_-(v=d*e+3*f*l*r+3*c*u*s+p*a),m=y,_=v,o+=Math.sqrt(g*g+b*b);return o},t.curveTo=function(e,i,r,n,s,h,a){var l=a[a.length-2],u=a[a.length-1];a.length-=2;var p=o._segmentsCount(t.curveLength(l,u,e,i,r,n,s,h)),c=0,f=0,d=0,y=0,v=0;a.push(l,u);for(var g=1,b=0;g<=p;++g)d=(f=(c=1-(b=g/p))*c)*c,v=(y=b*b)*b,a.push(d*l+3*f*b*e+3*c*y*r+v*s,d*u+3*f*b*i+3*c*y*n+v*h)},t}(),M=function(){function t(){}return t.curveLength=function(t,e,i,r,n,s){var h=t-2*i+n,a=e-2*r+s,o=2*i-2*t,l=2*r-2*e,u=4*(h*h+a*a),p=4*(h*o+a*l),c=o*o+l*l,f=2*Math.sqrt(u+p+c),d=Math.sqrt(u),y=2*u*d,v=2*Math.sqrt(c),g=p/d;return(y*f+d*p*(f-v)+(4*c*u-p*p)*Math.log((2*d+g+f)/(g+v)))/(4*y)},t.curveTo=function(e,i,r,n,s){for(var h=s[s.length-2],a=s[s.length-1],l=o._segmentsCount(t.curveLength(h,a,e,i,r,n)),u=0,p=0,c=1;c<=l;++c){var f=c/l;u=h+(e-h)*f,p=a+(i-a)*f,s.push(u+(e+(r-e)*f-u)*f,p+(i+(n-i)*f-p)*f)}},t}(),I=function(){function t(){this.reset()}return t.prototype.begin=function(t,e,i){this.reset(),this.style=t,this.start=e,this.attribStart=i},t.prototype.end=function(t,e){this.attribSize=e-this.attribStart,this.size=t-this.start},t.prototype.reset=function(){this.style=null,this.size=0,this.start=0,this.attribStart=0,this.attribSize=0},t}(),E=((P={})[i.SHAPES.POLY]=f,P[i.SHAPES.CIRC]=d,P[i.SHAPES.ELIP]=d,P[i.SHAPES.RECT]=y,P[i.SHAPES.RREC]=b,P),A=[],D=[],T=function(){function t(t,e,i,r){void 0===e&&(e=null),void 0===i&&(i=null),void 0===r&&(r=null),this.points=[],this.holes=[],this.shape=t,this.lineStyle=i,this.fillStyle=e,this.matrix=r,this.type=t.type}return t.prototype.clone=function(){return new t(this.shape,this.fillStyle,this.lineStyle,this.matrix)},t.prototype.destroy=function(){this.shape=null,this.holes.length=0,this.holes=null,this.points.length=0,this.points=null,this.lineStyle=null,this.fillStyle=null},t}(),C=new i.Point,R=function(t){function i(){var e=t.call(this)||this;return e.closePointEps=1e-4,e.boundsPadding=0,e.uvsFloat32=null,e.indicesUint16=null,e.batchable=!1,e.points=[],e.colors=[],e.uvs=[],e.indices=[],e.textureIds=[],e.graphicsData=[],e.drawCalls=[],e.batchDirty=-1,e.batches=[],e.dirty=0,e.cacheDirty=-1,e.clearDirty=0,e.shapeIndex=0,e._bounds=new s.Bounds,e.boundsDirty=-1,e}return p(i,t),Object.defineProperty(i.prototype,"bounds",{get:function(){return this.updateBatches(),this.boundsDirty!==this.dirty&&(this.boundsDirty=this.dirty,this.calculateBounds()),this._bounds},enumerable:!1,configurable:!0}),i.prototype.invalidate=function(){this.boundsDirty=-1,this.dirty++,this.batchDirty++,this.shapeIndex=0,this.points.length=0,this.colors.length=0,this.uvs.length=0,this.indices.length=0,this.textureIds.length=0;for(var t=0;t<this.drawCalls.length;t++)this.drawCalls[t].texArray.clear(),D.push(this.drawCalls[t]);this.drawCalls.length=0;for(t=0;t<this.batches.length;t++){var e=this.batches[t];e.reset(),A.push(e)}this.batches.length=0},i.prototype.clear=function(){return this.graphicsData.length>0&&(this.invalidate(),this.clearDirty++,this.graphicsData.length=0),this},i.prototype.drawShape=function(t,e,i,r){void 0===e&&(e=null),void 0===i&&(i=null),void 0===r&&(r=null);var n=new T(t,e,i,r);return this.graphicsData.push(n),this.dirty++,this},i.prototype.drawHole=function(t,e){if(void 0===e&&(e=null),!this.graphicsData.length)return null;var i=new T(t,null,null,e),r=this.graphicsData[this.graphicsData.length-1];return i.lineStyle=r.lineStyle,r.holes.push(i),this.dirty++,this},i.prototype.destroy=function(){t.prototype.destroy.call(this);for(var e=0;e<this.graphicsData.length;++e)this.graphicsData[e].destroy();this.points.length=0,this.points=null,this.colors.length=0,this.colors=null,this.uvs.length=0,this.uvs=null,this.indices.length=0,this.indices=null,this.indexBuffer.destroy(),this.indexBuffer=null,this.graphicsData.length=0,this.graphicsData=null,this.drawCalls.length=0,this.drawCalls=null,this.batches.length=0,this.batches=null,this._bounds=null},i.prototype.containsPoint=function(t){for(var e=this.graphicsData,i=0;i<e.length;++i){var r=e[i];if(r.fillStyle.visible&&(r.shape&&(r.matrix?r.matrix.applyInverse(t,C):C.copyFrom(t),r.shape.contains(C.x,C.y)))){var n=!1;if(r.holes)for(var s=0;s<r.holes.length;s++){if(r.holes[s].shape.contains(C.x,C.y)){n=!0;break}}if(!n)return!0}}return!1},i.prototype.updateBatches=function(){if(this.graphicsData.length){if(this.validateBatching()){this.cacheDirty=this.dirty;var t=this.uvs,e=this.graphicsData,i=null,r=null;this.batches.length>0&&(r=(i=this.batches[this.batches.length-1]).style);for(var s=this.shapeIndex;s<e.length;s++){this.shapeIndex++;var h=e[s],a=h.fillStyle,o=h.lineStyle;E[h.type].build(h),h.matrix&&this.transformPoints(h.points,h.matrix),(a.visible||o.visible)&&this.processHoles(h.holes);for(var l=0;l<2;l++){var u=0===l?a:o;if(u.visible){var p=u.texture.baseTexture,c=this.indices.length,f=this.points.length/2;p.wrapMode=n.WRAP_MODES.REPEAT,0===l?this.processFill(h):this.processLine(h);var d=this.points.length/2-f;0!==d&&(i&&!this._compareStyles(r,u)&&(i.end(c,f),i=null),i||((i=A.pop()||new I).begin(u,c,f),this.batches.push(i),r=u),this.addUvs(this.points,t,u.texture,f,d,u.matrix))}}}var y=this.indices.length,v=this.points.length/2;if(i&&i.end(y,v),0!==this.batches.length){var g=v>65535;this.indicesUint16&&this.indices.length===this.indicesUint16.length&&g===this.indicesUint16.BYTES_PER_ELEMENT>2?this.indicesUint16.set(this.indices):this.indicesUint16=g?new Uint32Array(this.indices):new Uint16Array(this.indices),this.batchable=this.isBatchable(),this.batchable?this.packBatches():this.buildDrawCalls()}else this.batchable=!0}}else this.batchable=!0},i.prototype._compareStyles=function(t,e){return!(!t||!e)&&(t.texture.baseTexture===e.texture.baseTexture&&(t.color+t.alpha===e.color+e.alpha&&!!t.native==!!e.native))},i.prototype.validateBatching=function(){if(this.dirty===this.cacheDirty||!this.graphicsData.length)return!1;for(var t=0,e=this.graphicsData.length;t<e;t++){var i=this.graphicsData[t],r=i.fillStyle,n=i.lineStyle;if(r&&!r.texture.baseTexture.valid)return!1;if(n&&!n.texture.baseTexture.valid)return!1}return!0},i.prototype.packBatches=function(){this.batchDirty++,this.uvsFloat32=new Float32Array(this.uvs);for(var t=this.batches,e=0,i=t.length;e<i;e++)for(var r=t[e],n=0;n<r.size;n++){var s=r.start+n;this.indicesUint16[s]=this.indicesUint16[s]-r.attribStart}},i.prototype.isBatchable=function(){if(this.points.length>131070)return!1;for(var t=this.batches,e=0;e<t.length;e++)if(t[e].style.native)return!1;return this.points.length<2*i.BATCHABLE_SIZE},i.prototype.buildDrawCalls=function(){for(var t=++e.BaseTexture._globalBatch,i=0;i<this.drawCalls.length;i++)this.drawCalls[i].texArray.clear(),D.push(this.drawCalls[i]);this.drawCalls.length=0;var r=this.colors,s=this.textureIds,h=D.pop();h||((h=new e.BatchDrawCall).texArray=new e.BatchTextureArray),h.texArray.count=0,h.start=0,h.size=0,h.type=n.DRAW_MODES.TRIANGLES;var a=0,o=null,l=0,u=!1,p=n.DRAW_MODES.TRIANGLES,c=0;this.drawCalls.push(h);for(i=0;i<this.batches.length;i++){var f=this.batches[i],d=f.style,y=d.texture.baseTexture;u!==!!d.native&&(p=(u=!!d.native)?n.DRAW_MODES.LINES:n.DRAW_MODES.TRIANGLES,o=null,a=8,t++),o!==y&&(o=y,y._batchEnabled!==t&&(8===a&&(t++,a=0,h.size>0&&((h=D.pop())||((h=new e.BatchDrawCall).texArray=new e.BatchTextureArray),this.drawCalls.push(h)),h.start=c,h.size=0,h.texArray.count=0,h.type=p),y.touched=1,y._batchEnabled=t,y._batchLocation=a,y.wrapMode=n.WRAP_MODES.REPEAT,h.texArray.elements[h.texArray.count++]=y,a++)),h.size+=f.size,c+=f.size,l=y._batchLocation,this.addColors(r,d.color,d.alpha,f.attribSize,f.attribStart),this.addTextureIds(s,l,f.attribSize,f.attribStart)}e.BaseTexture._globalBatch=t,this.packAttributes()},i.prototype.packAttributes=function(){for(var t=this.points,e=this.uvs,i=this.colors,r=this.textureIds,n=new ArrayBuffer(3*t.length*4),s=new Float32Array(n),h=new Uint32Array(n),a=0,o=0;o<t.length/2;o++)s[a++]=t[2*o],s[a++]=t[2*o+1],s[a++]=e[2*o],s[a++]=e[2*o+1],h[a++]=i[o],s[a++]=r[o];this._buffer.update(n),this._indexBuffer.update(this.indicesUint16)},i.prototype.processFill=function(t){t.holes.length?f.triangulate(t,this):E[t.type].triangulate(t,this)},i.prototype.processLine=function(t){x(t,this);for(var e=0;e<t.holes.length;e++)x(t.holes[e],this)},i.prototype.processHoles=function(t){for(var e=0;e<t.length;e++){var i=t[e];E[i.type].build(i),i.matrix&&this.transformPoints(i.points,i.matrix)}},i.prototype.calculateBounds=function(){var t=this._bounds;t.clear(),t.addVertexData(this.points,0,this.points.length),t.pad(this.boundsPadding,this.boundsPadding)},i.prototype.transformPoints=function(t,e){for(var i=0;i<t.length/2;i++){var r=t[2*i],n=t[2*i+1];t[2*i]=e.a*r+e.c*n+e.tx,t[2*i+1]=e.b*r+e.d*n+e.ty}},i.prototype.addColors=function(t,e,i,n,s){void 0===s&&(s=0);var h=(e>>16)+(65280&e)+((255&e)<<16),a=r.premultiplyTint(h,i);t.length=Math.max(t.length,s+n);for(var o=0;o<n;o++)t[s+o]=a},i.prototype.addTextureIds=function(t,e,i,r){void 0===r&&(r=0),t.length=Math.max(t.length,r+i);for(var n=0;n<i;n++)t[r+n]=e},i.prototype.addUvs=function(t,e,i,r,n,s){void 0===s&&(s=null);for(var h=0,a=e.length,o=i.frame;h<n;){var l=t[2*(r+h)],u=t[2*(r+h)+1];if(s){var p=s.a*l+s.c*u+s.tx;u=s.b*l+s.d*u+s.ty,l=p}h++,e.push(l/o.width,u/o.height)}var c=i.baseTexture;(o.width<c.width||o.height<c.height)&&this.adjustUvs(e,i,a,n)},i.prototype.adjustUvs=function(t,e,i,r){for(var n=e.baseTexture,s=1e-6,h=i+2*r,a=e.frame,o=a.width/n.width,l=a.height/n.height,u=a.x/a.width,p=a.y/a.height,c=Math.floor(t[i]+s),f=Math.floor(t[i+1]+s),d=i+2;d<h;d+=2)c=Math.min(c,Math.floor(t[d]+s)),f=Math.min(f,Math.floor(t[d+1]+s));u-=c,p-=f;for(d=i;d<h;d+=2)t[d]=(t[d]+u)*o,t[d+1]=(t[d+1]+p)*l},i.BATCHABLE_SIZE=100,i}(e.BatchGeometry),L=function(e){function i(){var i=null!==e&&e.apply(this,arguments)||this;return i.width=0,i.alignment=.5,i.native=!1,i.cap=t.LINE_CAP.BUTT,i.join=t.LINE_JOIN.MITER,i.miterLimit=10,i}return p(i,e),i.prototype.clone=function(){var t=new i;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t.width=this.width,t.alignment=this.alignment,t.native=this.native,t.cap=this.cap,t.join=this.join,t.miterLimit=this.miterLimit,t},i.prototype.reset=function(){e.prototype.reset.call(this),this.color=0,this.alignment=.5,this.width=0,this.native=!1},i}(l),N=new Float32Array(3),O={},B=function(s){function h(t){void 0===t&&(t=null);var i=s.call(this)||this;return i.shader=null,i.pluginName="batch",i.currentPath=null,i.batches=[],i.batchTint=-1,i.batchDirty=-1,i.vertexData=null,i._fillStyle=new l,i._lineStyle=new L,i._matrix=null,i._holeMode=!1,i.state=e.State.for2d(),i._geometry=t||new R,i._geometry.refCount++,i._transformID=-1,i.tint=16777215,i.blendMode=n.BLEND_MODES.NORMAL,i}return p(h,s),Object.defineProperty(h.prototype,"geometry",{get:function(){return this._geometry},enumerable:!1,configurable:!0}),h.prototype.clone=function(){return this.finishPoly(),new h(this._geometry)},Object.defineProperty(h.prototype,"blendMode",{get:function(){return this.state.blendMode},set:function(t){this.state.blendMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"fill",{get:function(){return this._fillStyle},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"line",{get:function(){return this._lineStyle},enumerable:!1,configurable:!0}),h.prototype.lineStyle=function(t,e,i,r,n){return void 0===t&&(t=null),void 0===e&&(e=0),void 0===i&&(i=1),void 0===r&&(r=.5),void 0===n&&(n=!1),"number"==typeof t&&(t={width:t,color:e,alpha:i,alignment:r,native:n}),this.lineTextureStyle(t)},h.prototype.lineTextureStyle=function(i){i=Object.assign({width:0,texture:e.Texture.WHITE,color:i&&i.texture?16777215:0,alpha:1,matrix:null,alignment:.5,native:!1,cap:t.LINE_CAP.BUTT,join:t.LINE_JOIN.MITER,miterLimit:10},i),this.currentPath&&this.startPoly();var r=i.width>0&&i.alpha>0;return r?(i.matrix&&(i.matrix=i.matrix.clone(),i.matrix.invert()),Object.assign(this._lineStyle,{visible:r},i)):this._lineStyle.reset(),this},h.prototype.startPoly=function(){if(this.currentPath){var t=this.currentPath.points,e=this.currentPath.points.length;e>2&&(this.drawShape(this.currentPath),this.currentPath=new i.Polygon,this.currentPath.closeStroke=!1,this.currentPath.points.push(t[e-2],t[e-1]))}else this.currentPath=new i.Polygon,this.currentPath.closeStroke=!1},h.prototype.finishPoly=function(){this.currentPath&&(this.currentPath.points.length>2?(this.drawShape(this.currentPath),this.currentPath=null):this.currentPath.points.length=0)},h.prototype.moveTo=function(t,e){return this.startPoly(),this.currentPath.points[0]=t,this.currentPath.points[1]=e,this},h.prototype.lineTo=function(t,e){this.currentPath||this.moveTo(0,0);var i=this.currentPath.points,r=i[i.length-2],n=i[i.length-1];return r===t&&n===e||i.push(t,e),this},h.prototype._initCurve=function(t,e){void 0===t&&(t=0),void 0===e&&(e=0),this.currentPath?0===this.currentPath.points.length&&(this.currentPath.points=[t,e]):this.moveTo(t,e)},h.prototype.quadraticCurveTo=function(t,e,i,r){this._initCurve();var n=this.currentPath.points;return 0===n.length&&this.moveTo(0,0),M.curveTo(t,e,i,r,n),this},h.prototype.bezierCurveTo=function(t,e,i,r,n,s){return this._initCurve(),w.curveTo(t,e,i,r,n,s,this.currentPath.points),this},h.prototype.arcTo=function(t,e,i,r,n){this._initCurve(t,e);var s=this.currentPath.points,h=S.curveTo(t,e,i,r,n,s);if(h){var a=h.cx,o=h.cy,l=h.radius,u=h.startAngle,p=h.endAngle,c=h.anticlockwise;this.arc(a,o,l,u,p,c)}return this},h.prototype.arc=function(t,e,r,n,s,h){if(void 0===h&&(h=!1),n===s)return this;if(!h&&s<=n?s+=i.PI_2:h&&n<=s&&(n+=i.PI_2),0===s-n)return this;var a=t+Math.cos(n)*r,o=e+Math.sin(n)*r,l=this._geometry.closePointEps,u=this.currentPath?this.currentPath.points:null;if(u){var p=Math.abs(u[u.length-2]-a),c=Math.abs(u[u.length-1]-o);p<l&&c<l||u.push(a,o)}else this.moveTo(a,o),u=this.currentPath.points;return S.arc(a,o,t,e,r,n,s,h,u),this},h.prototype.beginFill=function(t,i){return void 0===t&&(t=0),void 0===i&&(i=1),this.beginTextureFill({texture:e.Texture.WHITE,color:t,alpha:i})},h.prototype.beginTextureFill=function(t){t=Object.assign({texture:e.Texture.WHITE,color:16777215,alpha:1,matrix:null},t),this.currentPath&&this.startPoly();var i=t.alpha>0;return i?(t.matrix&&(t.matrix=t.matrix.clone(),t.matrix.invert()),Object.assign(this._fillStyle,{visible:i},t)):this._fillStyle.reset(),this},h.prototype.endFill=function(){return this.finishPoly(),this._fillStyle.reset(),this},h.prototype.drawRect=function(t,e,r,n){return this.drawShape(new i.Rectangle(t,e,r,n))},h.prototype.drawRoundedRect=function(t,e,r,n,s){return this.drawShape(new i.RoundedRectangle(t,e,r,n,s))},h.prototype.drawCircle=function(t,e,r){return this.drawShape(new i.Circle(t,e,r))},h.prototype.drawEllipse=function(t,e,r,n){return this.drawShape(new i.Ellipse(t,e,r,n))},h.prototype.drawPolygon=function(){for(var t,e=arguments,r=[],n=0;n<arguments.length;n++)r[n]=e[n];var s=!0,h=r[0];h.points?(s=h.closeStroke,t=h.points):t=Array.isArray(r[0])?r[0]:r;var a=new i.Polygon(t);return a.closeStroke=s,this.drawShape(a),this},h.prototype.drawShape=function(t){return this._holeMode?this._geometry.drawHole(t,this._matrix):this._geometry.drawShape(t,this._fillStyle.clone(),this._lineStyle.clone(),this._matrix),this},h.prototype.clear=function(){return this._geometry.clear(),this._lineStyle.reset(),this._fillStyle.reset(),this._boundsID++,this._matrix=null,this._holeMode=!1,this.currentPath=null,this},h.prototype.isFastRect=function(){var t=this._geometry.graphicsData;return!(1!==t.length||t[0].shape.type!==i.SHAPES.RECT||t[0].matrix||t[0].holes.length||t[0].lineStyle.visible&&t[0].lineStyle.width)},h.prototype._render=function(t){this.finishPoly();var e=this._geometry;e.updateBatches(),e.batchable?(this.batchDirty!==e.batchDirty&&this._populateBatches(),this._renderBatched(t)):(t.batch.flush(),this._renderDirect(t))},h.prototype._populateBatches=function(){var t=this._geometry,e=this.blendMode,i=t.batches.length;this.batchTint=-1,this._transformID=-1,this.batchDirty=t.batchDirty,this.batches.length=i,this.vertexData=new Float32Array(t.points);for(var n=0;n<i;n++){var s=t.batches[n],h=s.style.color,a=new Float32Array(this.vertexData.buffer,4*s.attribStart*2,2*s.attribSize),o=new Float32Array(t.uvsFloat32.buffer,4*s.attribStart*2,2*s.attribSize),l={vertexData:a,blendMode:e,indices:new Uint16Array(t.indicesUint16.buffer,2*s.start,s.size),uvs:o,_batchRGB:r.hex2rgb(h),_tintRGB:h,_texture:s.style.texture,alpha:s.style.alpha,worldAlpha:1};this.batches[n]=l}},h.prototype._renderBatched=function(t){if(this.batches.length){t.batch.setObjectRenderer(t.plugins[this.pluginName]),this.calculateVertices(),this.calculateTints();for(var e=0,i=this.batches.length;e<i;e++){var r=this.batches[e];r.worldAlpha=this.worldAlpha*r.alpha,t.plugins[this.pluginName].render(r)}}},h.prototype._renderDirect=function(t){var e=this._resolveDirectShader(t),i=this._geometry,r=this.tint,n=this.worldAlpha,s=e.uniforms,h=i.drawCalls;s.translationMatrix=this.transform.worldTransform,s.tint[0]=(r>>16&255)/255*n,s.tint[1]=(r>>8&255)/255*n,s.tint[2]=(255&r)/255*n,s.tint[3]=n,t.shader.bind(e),t.geometry.bind(i,e),t.state.set(this.state);for(var a=0,o=h.length;a<o;a++)this._renderDrawCallDirect(t,i.drawCalls[a])},h.prototype._renderDrawCallDirect=function(t,e){for(var i=e.texArray,r=e.type,n=e.size,s=e.start,h=i.count,a=0;a<h;a++)t.texture.bind(i.elements[a],a);t.geometry.draw(r,n,s)},h.prototype._resolveDirectShader=function(t){var r=this.shader,n=this.pluginName;if(!r){if(!O[n]){for(var s=t.plugins[n].MAX_TEXTURES,h=new Int32Array(s),a=0;a<s;a++)h[a]=a;var o={tint:new Float32Array([1,1,1,1]),translationMatrix:new i.Matrix,default:e.UniformGroup.from({uSamplers:h},!0)},l=t.plugins[n]._shader.program;O[n]=new e.Shader(l,o)}r=O[n]}return r},h.prototype._calculateBounds=function(){this.finishPoly();var t=this._geometry;if(t.graphicsData.length){var e=t.bounds,i=e.minX,r=e.minY,n=e.maxX,s=e.maxY;this._bounds.addFrame(this.transform,i,r,n,s)}},h.prototype.containsPoint=function(t){return this.worldTransform.applyInverse(t,h._TEMP_POINT),this._geometry.containsPoint(h._TEMP_POINT)},h.prototype.calculateTints=function(){if(this.batchTint!==this.tint){this.batchTint=this.tint;for(var t=r.hex2rgb(this.tint,N),e=0;e<this.batches.length;e++){var i=this.batches[e],n=i._batchRGB,s=(t[0]*n[0]*255<<16)+(t[1]*n[1]*255<<8)+(0|t[2]*n[2]*255);i._tintRGB=(s>>16)+(65280&s)+((255&s)<<16)}}},h.prototype.calculateVertices=function(){var t=this.transform._worldID;if(this._transformID!==t){this._transformID=t;for(var e=this.transform.worldTransform,i=e.a,r=e.b,n=e.c,s=e.d,h=e.tx,a=e.ty,o=this._geometry.points,l=this.vertexData,u=0,p=0;p<o.length;p+=2){var c=o[p],f=o[p+1];l[u++]=i*c+n*f+h,l[u++]=s*f+r*c+a}}},h.prototype.closePath=function(){var t=this.currentPath;return t&&(t.closeStroke=!0,this.finishPoly()),this},h.prototype.setMatrix=function(t){return this._matrix=t,this},h.prototype.beginHole=function(){return this.finishPoly(),this._holeMode=!0,this},h.prototype.endHole=function(){return this.finishPoly(),this._holeMode=!1,this},h.prototype.destroy=function(t){this._geometry.refCount--,0===this._geometry.refCount&&this._geometry.dispose(),this._matrix=null,this.currentPath=null,this._lineStyle.destroy(),this._lineStyle=null,this._fillStyle.destroy(),this._fillStyle=null,this._geometry=null,this.shader=null,this.vertexData=null,this.batches.length=0,this.batches=null,s.prototype.destroy.call(this,t)},h.nextRoundedRectBehavior=!1,h._TEMP_POINT=new i.Point,h}(s.Container),U={buildPoly:f,buildCircle:d,buildRectangle:y,buildRoundedRectangle:b,buildLine:x,ArcUtils:S,BezierUtils:w,QuadraticUtils:M,BatchPart:I,FILL_COMMANDS:E,BATCH_POOL:A,DRAW_CALL_POOL:D};return t.FillStyle=l,t.GRAPHICS_CURVES=o,t.Graphics=B,t.GraphicsData=T,t.GraphicsGeometry=R,t.LineStyle=L,t.graphicsUtils=U,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI,PIXI.utils,PIXI,PIXI);Object.assign(this.PIXI,_pixi_graphics);
//# sourceMappingURL=graphics.min.js.map
