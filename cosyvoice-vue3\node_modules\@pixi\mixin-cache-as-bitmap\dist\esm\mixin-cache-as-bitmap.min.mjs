/*!
 * @pixi/mixin-cache-as-bitmap - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-cache-as-bitmap is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{RenderTexture as t,BaseTexture as a,Texture as i}from"@pixi/core";import{Sprite as e}from"@pixi/sprite";import{DisplayObject as s}from"@pixi/display";import{Matrix as r}from"@pixi/math";import{uid as n}from"@pixi/utils";import{settings as h}from"@pixi/settings";import{MSAA_QUALITY as o}from"@pixi/constants";var c=new r;s.prototype._cacheAsBitmap=!1,s.prototype._cacheData=null,s.prototype._cacheAsBitmapResolution=null,s.prototype._cacheAsBitmapMultisample=o.NONE;var l=function(){this.textureCacheId=null,this.originalRender=null,this.originalRenderCanvas=null,this.originalCalculateBounds=null,this.originalGetLocalBounds=null,this.originalUpdateTransform=null,this.originalDestroy=null,this.originalMask=null,this.originalFilterArea=null,this.originalContainsPoint=null,this.sprite=null};Object.defineProperties(s.prototype,{cacheAsBitmapResolution:{get:function(){return this._cacheAsBitmapResolution},set:function(t){t!==this._cacheAsBitmapResolution&&(this._cacheAsBitmapResolution=t,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmapMultisample:{get:function(){return this._cacheAsBitmapMultisample},set:function(t){t!==this._cacheAsBitmapMultisample&&(this._cacheAsBitmapMultisample=t,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmap:{get:function(){return this._cacheAsBitmap},set:function(t){var a;this._cacheAsBitmap!==t&&(this._cacheAsBitmap=t,t?(this._cacheData||(this._cacheData=new l),(a=this._cacheData).originalRender=this.render,a.originalRenderCanvas=this.renderCanvas,a.originalUpdateTransform=this.updateTransform,a.originalCalculateBounds=this.calculateBounds,a.originalGetLocalBounds=this.getLocalBounds,a.originalDestroy=this.destroy,a.originalContainsPoint=this.containsPoint,a.originalMask=this._mask,a.originalFilterArea=this.filterArea,this.render=this._renderCached,this.renderCanvas=this._renderCachedCanvas,this.destroy=this._cacheAsBitmapDestroy):((a=this._cacheData).sprite&&this._destroyCachedDisplayObject(),this.render=a.originalRender,this.renderCanvas=a.originalRenderCanvas,this.calculateBounds=a.originalCalculateBounds,this.getLocalBounds=a.originalGetLocalBounds,this.destroy=a.originalDestroy,this.updateTransform=a.originalUpdateTransform,this.containsPoint=a.originalContainsPoint,this._mask=a.originalMask,this.filterArea=a.originalFilterArea))}}}),s.prototype._renderCached=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObject(t),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._render(t))},s.prototype._initCachedDisplayObject=function(s){var r;if(!this._cacheData||!this._cacheData.sprite){var o=this.alpha;this.alpha=1,s.batch.flush();var l=this.getLocalBounds(null,!0).clone();if(this.filters&&this.filters.length){var d=this.filters[0].padding;l.pad(d)}l.ceil(h.RESOLUTION);var p=s.renderTexture.current,u=s.renderTexture.sourceFrame.clone(),m=s.renderTexture.destinationFrame.clone(),_=s.projection.transform,f=t.create({width:l.width,height:l.height,resolution:this.cacheAsBitmapResolution||s.resolution,multisample:null!==(r=this.cacheAsBitmapMultisample)&&void 0!==r?r:s.multisample}),B="cacheAsBitmap_"+n();this._cacheData.textureCacheId=B,a.addToCache(f.baseTexture,B),i.addToCache(f,B);var g=this.transform.localTransform.copyTo(c).invert().translate(-l.x,-l.y);this.render=this._cacheData.originalRender,s.render(this,{renderTexture:f,clear:!0,transform:g,skipUpdateTransform:!1}),s.framebuffer.blit(),s.projection.transform=_,s.renderTexture.bind(p,u,m),this.render=this._renderCached,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=o;var C=new e(f);C.transform.worldTransform=this.transform.worldTransform,C.anchor.x=-l.x/l.width,C.anchor.y=-l.y/l.height,C.alpha=o,C._bounds=this._bounds,this._cacheData.sprite=C,this.transform._parentID=-1,this.parent?this.updateTransform():(this.enableTempParent(),this.updateTransform(),this.disableTempParent(null)),this.containsPoint=C.containsPoint.bind(C)}},s.prototype._renderCachedCanvas=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObjectCanvas(t),this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._renderCanvas(t))},s.prototype._initCachedDisplayObjectCanvas=function(s){if(!this._cacheData||!this._cacheData.sprite){var r=this.getLocalBounds(null,!0),o=this.alpha;this.alpha=1;var l=s.context,d=s._projTransform;r.ceil(h.RESOLUTION);var p=t.create({width:r.width,height:r.height}),u="cacheAsBitmap_"+n();this._cacheData.textureCacheId=u,a.addToCache(p.baseTexture,u),i.addToCache(p,u);var m=c;this.transform.localTransform.copyTo(m),m.invert(),m.tx-=r.x,m.ty-=r.y,this.renderCanvas=this._cacheData.originalRenderCanvas,s.render(this,{renderTexture:p,clear:!0,transform:m,skipUpdateTransform:!1}),s.context=l,s._projTransform=d,this.renderCanvas=this._renderCachedCanvas,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=o;var _=new e(p);_.transform.worldTransform=this.transform.worldTransform,_.anchor.x=-r.x/r.width,_.anchor.y=-r.y/r.height,_.alpha=o,_._bounds=this._bounds,this._cacheData.sprite=_,this.transform._parentID=-1,this.parent?this.updateTransform():(this.parent=s._tempDisplayObjectParent,this.updateTransform(),this.parent=null),this.containsPoint=_.containsPoint.bind(_)}},s.prototype._calculateCachedBounds=function(){this._bounds.clear(),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite._calculateBounds(),this._bounds.updateID=this._boundsID},s.prototype._getCachedLocalBounds=function(){return this._cacheData.sprite.getLocalBounds(null)},s.prototype._destroyCachedDisplayObject=function(){this._cacheData.sprite._texture.destroy(!0),this._cacheData.sprite=null,a.removeFromCache(this._cacheData.textureCacheId),i.removeFromCache(this._cacheData.textureCacheId),this._cacheData.textureCacheId=null},s.prototype._cacheAsBitmapDestroy=function(t){this.cacheAsBitmap=!1,this.destroy(t)};export{l as CacheData};
//# sourceMappingURL=mixin-cache-as-bitmap.min.mjs.map
