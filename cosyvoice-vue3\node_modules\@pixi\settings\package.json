{"name": "@pixi/settings", "version": "6.5.10", "main": "dist/cjs/settings.js", "module": "dist/esm/settings.mjs", "bundle": "dist/browser/settings.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/settings.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/settings.js"}}}, "description": "Collecting of user configurable settings used throughout PixiJS", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/constants": "6.5.10"}, "devDependencies": {"ismobilejs": "^1.1.0"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}