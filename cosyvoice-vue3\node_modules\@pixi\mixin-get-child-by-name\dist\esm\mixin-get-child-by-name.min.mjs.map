{"version": 3, "file": "mixin-get-child-by-name.min.mjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import { DisplayObject, Container } from '@pixi/display';\n\n/**\n * The instance name of the object.\n * @memberof PIXI.DisplayObject#\n * @member {string} name\n */\nDisplayObject.prototype.name = null;\n\n/**\n * Returns the display object in the container.\n *\n * Recursive searches are done in a preorder traversal.\n * @method getChildByName\n * @memberof PIXI.Container#\n * @param {string} name - Instance name.\n * @param {boolean}[deep=false] - Whether to search recursively\n * @returns {PIXI.DisplayObject} The child with the specified name.\n */\nContainer.prototype.getChildByName = function getChildByName<T extends DisplayObject = DisplayObject>(\n    name: string,\n    deep?: boolean,\n): T\n{\n    for (let i = 0, j = this.children.length; i < j; i++)\n    {\n        if (this.children[i].name === name)\n        {\n            return this.children[i];\n        }\n    }\n\n    if (deep)\n    {\n        for (let i = 0, j = this.children.length; i < j; i++)\n        {\n            const child = (this.children[i] as Container);\n\n            if (!child.getChildByName)\n            {\n                continue;\n            }\n\n            const target = child.getChildByName<T>(name, true);\n\n            if (target)\n            {\n                return target;\n            }\n        }\n    }\n\n    return null;\n};\n"], "names": ["DisplayObject", "prototype", "name", "Container", "getChildByName", "deep", "i", "j", "this", "children", "length", "child", "target"], "mappings": ";;;;;;;6DAOAA,EAAcC,UAAUC,KAAO,KAY/BC,EAAUF,UAAUG,eAAiB,SACjCF,EACAG,GAGA,IAAK,IAAIC,EAAI,EAAGC,EAAIC,KAAKC,SAASC,OAAQJ,EAAIC,EAAGD,IAE7C,GAAIE,KAAKC,SAASH,GAAGJ,OAASA,EAE1B,OAAOM,KAAKC,SAASH,GAI7B,GAAID,EAEA,IAASC,EAAI,EAAGC,EAAIC,KAAKC,SAASC,OAAQJ,EAAIC,EAAGD,IACjD,CACI,IAAMK,EAASH,KAAKC,SAASH,GAE7B,GAAKK,EAAMP,eAAX,CAKA,IAAMQ,EAASD,EAAMP,eAAkBF,GAAM,GAE7C,GAAIU,EAEA,OAAOA,GAKnB,OAAO"}