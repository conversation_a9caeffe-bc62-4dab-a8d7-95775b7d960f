/*!
 * @pixi/sprite-tiling - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/sprite-tiling is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/core"),t=require("@pixi/math"),r=require("@pixi/sprite"),n=require("@pixi/constants"),o=require("@pixi/utils"),i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},i(e,t)};function a(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var u=new t.Point,s=function(r){function n(n,o,i){void 0===o&&(o=100),void 0===i&&(i=100);var a=r.call(this,n)||this;return a.tileTransform=new t.Transform,a._width=o,a._height=i,a.uvMatrix=a.texture.uvMatrix||new e.TextureMatrix(n),a.pluginName="tilingSprite",a.uvRespectAnchor=!1,a}return a(n,r),Object.defineProperty(n.prototype,"clampMargin",{get:function(){return this.uvMatrix.clampMargin},set:function(e){this.uvMatrix.clampMargin=e,this.uvMatrix.update(!0)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"tileScale",{get:function(){return this.tileTransform.scale},set:function(e){this.tileTransform.scale.copyFrom(e)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"tilePosition",{get:function(){return this.tileTransform.position},set:function(e){this.tileTransform.position.copyFrom(e)},enumerable:!1,configurable:!0}),n.prototype._onTextureUpdate=function(){this.uvMatrix&&(this.uvMatrix.texture=this._texture),this._cachedTint=16777215},n.prototype._render=function(e){var t=this._texture;t&&t.valid&&(this.tileTransform.updateLocalTransform(),this.uvMatrix.update(),e.batch.setObjectRenderer(e.plugins[this.pluginName]),e.plugins[this.pluginName].render(this))},n.prototype._calculateBounds=function(){var e=this._width*-this._anchor._x,t=this._height*-this._anchor._y,r=this._width*(1-this._anchor._x),n=this._height*(1-this._anchor._y);this._bounds.addFrame(this.transform,e,t,r,n)},n.prototype.getLocalBounds=function(e){return 0===this.children.length?(this._bounds.minX=this._width*-this._anchor._x,this._bounds.minY=this._height*-this._anchor._y,this._bounds.maxX=this._width*(1-this._anchor._x),this._bounds.maxY=this._height*(1-this._anchor._y),e||(this._localBoundsRect||(this._localBoundsRect=new t.Rectangle),e=this._localBoundsRect),this._bounds.getRectangle(e)):r.prototype.getLocalBounds.call(this,e)},n.prototype.containsPoint=function(e){this.worldTransform.applyInverse(e,u);var t=this._width,r=this._height,n=-t*this.anchor._x;if(u.x>=n&&u.x<n+t){var o=-r*this.anchor._y;if(u.y>=o&&u.y<o+r)return!0}return!1},n.prototype.destroy=function(e){r.prototype.destroy.call(this,e),this.tileTransform=null,this.uvMatrix=null},n.from=function(t,r){return new n(t instanceof e.Texture?t:e.Texture.from(t,r),r.width,r.height)},Object.defineProperty(n.prototype,"width",{get:function(){return this._width},set:function(e){this._width=e},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"height",{get:function(){return this._height},set:function(e){this._height=e},enumerable:!1,configurable:!0}),n}(r.Sprite),c="#version 100\n#define SHADER_NAME Tiling-Sprite-100\n\nprecision lowp float;\n\nattribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTransform;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;\n}\n",l=new t.Matrix,d=function(t){function r(r){var n=t.call(this,r)||this;return r.runners.contextChange.add(n),n.quad=new e.QuadUv,n.state=e.State.for2d(),n}return a(r,t),r.prototype.contextChange=function(){var t=this.renderer,r={globals:t.globalUniforms};this.simpleShader=e.Shader.from(c,"#version 100\n#define SHADER_NAME Tiling-Sprite-Simple-100\n\nprecision lowp float;\n\nvarying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\n\nvoid main(void)\n{\n    vec4 texSample = texture2D(uSampler, vTextureCoord);\n    gl_FragColor = texSample * uColor;\n}\n",r),this.shader=t.context.webGLVersion>1?e.Shader.from("#version 300 es\n#define SHADER_NAME Tiling-Sprite-300\n\nprecision lowp float;\n\nin vec2 aVertexPosition;\nin vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTransform;\n\nout vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;\n}\n","#version 300 es\n#define SHADER_NAME Tiling-Sprite-100\n\nprecision lowp float;\n\nin vec2 vTextureCoord;\n\nout vec4 fragmentColor;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\nuniform mat3 uMapCoord;\nuniform vec4 uClampFrame;\nuniform vec2 uClampOffset;\n\nvoid main(void)\n{\n    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);\n    coord = (uMapCoord * vec3(coord, 1.0)).xy;\n    vec2 unclamped = coord;\n    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);\n\n    vec4 texSample = texture(uSampler, coord, unclamped == coord ? 0.0f : -32.0f);// lod-bias very negative to force lod 0\n\n    fragmentColor = texSample * uColor;\n}\n",r):e.Shader.from(c,"#version 100\n#ifdef GL_EXT_shader_texture_lod\n    #extension GL_EXT_shader_texture_lod : enable\n#endif\n#define SHADER_NAME Tiling-Sprite-100\n\nprecision lowp float;\n\nvarying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\nuniform mat3 uMapCoord;\nuniform vec4 uClampFrame;\nuniform vec2 uClampOffset;\n\nvoid main(void)\n{\n    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);\n    coord = (uMapCoord * vec3(coord, 1.0)).xy;\n    vec2 unclamped = coord;\n    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);\n\n    #ifdef GL_EXT_shader_texture_lod\n        vec4 texSample = unclamped == coord\n            ? texture2D(uSampler, coord) \n            : texture2DLodEXT(uSampler, coord, 0);\n    #else\n        vec4 texSample = texture2D(uSampler, coord);\n    #endif\n\n    gl_FragColor = texSample * uColor;\n}\n",r)},r.prototype.render=function(e){var t=this.renderer,r=this.quad,i=r.vertices;i[0]=i[6]=e._width*-e.anchor.x,i[1]=i[3]=e._height*-e.anchor.y,i[2]=i[4]=e._width*(1-e.anchor.x),i[5]=i[7]=e._height*(1-e.anchor.y);var a=e.uvRespectAnchor?e.anchor.x:0,u=e.uvRespectAnchor?e.anchor.y:0;(i=r.uvs)[0]=i[6]=-a,i[1]=i[3]=-u,i[2]=i[4]=1-a,i[5]=i[7]=1-u,r.invalidate();var s=e._texture,c=s.baseTexture,d=c.alphaMode>0,h=e.tileTransform.localTransform,p=e.uvMatrix,m=c.isPowerOfTwo&&s.frame.width===c.width&&s.frame.height===c.height;m&&(c._glTextures[t.CONTEXT_UID]?m=c.wrapMode!==n.WRAP_MODES.CLAMP:c.wrapMode===n.WRAP_MODES.CLAMP&&(c.wrapMode=n.WRAP_MODES.REPEAT));var f=m?this.simpleShader:this.shader,v=s.width,x=s.height,_=e._width,g=e._height;l.set(h.a*v/_,h.b*v/g,h.c*x/_,h.d*x/g,h.tx/_,h.ty/g),l.invert(),m?l.prepend(p.mapCoord):(f.uniforms.uMapCoord=p.mapCoord.toArray(!0),f.uniforms.uClampFrame=p.uClampFrame,f.uniforms.uClampOffset=p.uClampOffset),f.uniforms.uTransform=l.toArray(!0),f.uniforms.uColor=o.premultiplyTintToRgba(e.tint,e.worldAlpha,f.uniforms.uColor,d),f.uniforms.translationMatrix=e.transform.worldTransform.toArray(!0),f.uniforms.uSampler=s,t.shader.bind(f),t.geometry.bind(r),this.state.blendMode=o.correctBlendMode(e.blendMode,d),t.state.set(this.state),t.geometry.draw(this.renderer.gl.TRIANGLES,6,0)},r.extension={name:"tilingSprite",type:e.ExtensionType.RendererPlugin},r}(e.ObjectRenderer);exports.TilingSprite=s,exports.TilingSpriteRenderer=d;
//# sourceMappingURL=sprite-tiling.min.js.map
