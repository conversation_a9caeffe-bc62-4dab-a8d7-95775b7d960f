{"version": 3, "file": "polyfill.min.mjs", "sources": ["../../src/globalThis.ts", "../../src/Promise.ts", "../../src/Object.assign.ts", "../../src/requestAnimationFrame.ts", "../../src/Math.sign.ts", "../../src/Number.isInteger.ts", "../../src/index.ts"], "sourcesContent": ["if (typeof globalThis === 'undefined')\n{\n    if (typeof self !== 'undefined')\n    {\n        // covers browsers\n        // @ts-expect-error not-writable ts(2540) error only on node\n        self.globalThis = self;\n    }\n    else if (typeof global !== 'undefined')\n    {\n        // covers versions of Node < 12\n        // @ts-expect-error not-writable ts(2540) error only on node\n        global.globalThis = global;\n    }\n}\n", "import Polyfill from 'promise-polyfill';\n\n// Support for IE 9 - 11 which does not include Promises\nif (!globalThis.Promise)\n{\n    globalThis.Promise = Polyfill;\n}\n", "// References:\n// https://github.com/sindresorhus/object-assign\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n\nimport objectAssign from 'object-assign';\n\nif (!Object.assign)\n{\n    Object.assign = objectAssign;\n}\n", "// References:\n// http://paulirish.com/2011/requestanimationframe-for-smart-animating/\n// https://gist.github.com/1579671\n// http://updates.html5rocks.com/2012/05/requestAnimationFrame-API-now-with-sub-millisecond-precision\n// https://gist.github.com/timhall/4078614\n// https://github.com/Financial-Times/polyfill-service/tree/master/polyfills/requestAnimationFrame\n\n// Expected to be used with Browserfiy\n// Browserify automatically detects the use of `global` and passes the\n// correct reference of `global`, `globalThis`, and finally `window`\n\nconst ONE_FRAME_TIME = 16;\n\n// Date.now\nif (!(Date.now && Date.prototype.getTime))\n{\n    Date.now = function now(): number\n    {\n        return new Date().getTime();\n    };\n}\n\n// performance.now\nif (!(globalThis.performance && globalThis.performance.now))\n{\n    const startTime = Date.now();\n\n    if (!globalThis.performance)\n    {\n        (globalThis as any).performance = {};\n    }\n\n    globalThis.performance.now = (): number => Date.now() - startTime;\n}\n\n// requestAnimationFrame\nlet lastTime = Date.now();\nconst vendors = ['ms', 'moz', 'webkit', 'o'];\n\nfor (let x = 0; x < vendors.length && !globalThis.requestAnimationFrame; ++x)\n{\n    const p = vendors[x];\n\n    globalThis.requestAnimationFrame = (globalThis as any)[`${p}RequestAnimationFrame`];\n    globalThis.cancelAnimationFrame = (globalThis as any)[`${p}CancelAnimationFrame`]\n        || (globalThis as any)[`${p}CancelRequestAnimationFrame`];\n}\n\nif (!globalThis.requestAnimationFrame)\n{\n    globalThis.requestAnimationFrame = (callback: (...parms: any[]) => void): number =>\n    {\n        if (typeof callback !== 'function')\n        {\n            throw new TypeError(`${callback}is not a function`);\n        }\n\n        const currentTime = Date.now();\n        let delay = ONE_FRAME_TIME + lastTime - currentTime;\n\n        if (delay < 0)\n        {\n            delay = 0;\n        }\n\n        lastTime = currentTime;\n\n        return globalThis.self.setTimeout(() =>\n        {\n            lastTime = Date.now();\n            callback(performance.now());\n        }, delay);\n    };\n}\n\nif (!globalThis.cancelAnimationFrame)\n{\n    globalThis.cancelAnimationFrame = (id: number): void => clearTimeout(id);\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/sign\n\nif (!Math.sign)\n{\n    Math.sign = function mathSign(x): number\n    {\n        x = Number(x);\n\n        if (x === 0 || isNaN(x))\n        {\n            return x;\n        }\n\n        return x > 0 ? 1 : -1;\n    };\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n\nif (!Number.isInteger)\n{\n    Number.isInteger = function numberIsInteger(value): boolean\n    {\n        return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n    };\n}\n", "import './globalThis';\nimport './Promise';\nimport './Object.assign';\nimport './requestAnimationFrame';\nimport './Math.sign';\nimport './Number.isInteger';\n\nif (!globalThis.ArrayBuffer)\n{\n    (globalThis as any).ArrayBuffer = Array;\n}\n\nif (!globalThis.Float32Array)\n{\n    (globalThis as any).Float32Array = Array;\n}\n\nif (!globalThis.Uint32Array)\n{\n    (globalThis as any).Uint32Array = Array;\n}\n\nif (!globalThis.Uint16Array)\n{\n    (globalThis as any).Uint16Array = Array;\n}\n\nif (!globalThis.Uint8Array)\n{\n    (globalThis as any).Uint8Array = Array;\n}\n\nif (!globalThis.Int32Array)\n{\n    (globalThis as any).Int32Array = Array;\n}\n"], "names": ["globalThis", "self", "global", "Promise", "Polyfill", "Object", "assign", "objectAssign", "Date", "now", "prototype", "getTime", "performance", "startTime_1", "lastTime", "vendors", "x", "length", "requestAnimationFrame", "p", "cancelAnimationFrame", "callback", "TypeError", "currentTime", "delay", "setTimeout", "id", "clearTimeout", "Math", "sign", "Number", "isNaN", "isInteger", "value", "isFinite", "floor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "Float32Array", "Uint32Array", "Uint16Array", "Uint8Array", "Int32Array"], "mappings": ";;;;;;;6DAA0B,oBAAfA,aAEa,oBAATC,KAIPA,KAAKD,WAAaC,KAEK,oBAAXC,SAIZA,OAAOF,WAAaE,SCTvBF,WAAWG,UAEZH,WAAWG,QAAUC,GCCpBC,OAAOC,SAERD,OAAOC,OAASC,GCepB,GATMC,KAAKC,KAAOD,KAAKE,UAAUC,UAE7BH,KAAKC,IAAM,WAEP,OAAO,IAAID,MAAOG,aAKpBX,WAAWY,cAAeZ,WAAWY,YAAYH,IACvD,CACI,IAAMI,EAAYL,KAAKC,MAElBT,WAAWY,cAEXZ,WAAmBY,YAAc,IAGtCZ,WAAWY,YAAYH,IAAM,WAAc,OAAAD,KAAKC,MAAQI,GAO5D,IAHA,IAAIC,EAAWN,KAAKC,MACdM,EAAU,CAAC,KAAM,MAAO,SAAU,KAE/BC,EAAI,EAAGA,EAAID,EAAQE,SAAWjB,WAAWkB,wBAAyBF,EAC3E,CACI,IAAMG,EAAIJ,EAAQC,GAElBhB,WAAWkB,sBAAyBlB,WAAsBmB,EAAC,yBAC3DnB,WAAWoB,qBAAwBpB,WAAsBmB,2BACjDnB,WAAsBmB,EAA8B,+BAG3DnB,WAAWkB,wBAEZlB,WAAWkB,sBAAwB,SAACG,GAEhC,GAAwB,mBAAbA,EAEP,MAAM,IAAIC,UAAaD,EAAQ,qBAGnC,IAAME,EAAcf,KAAKC,MACrBe,EA/CW,GA+CcV,EAAWS,EASxC,OAPIC,EAAQ,IAERA,EAAQ,GAGZV,EAAWS,EAEJvB,WAAWC,KAAKwB,YAAW,WAE9BX,EAAWN,KAAKC,MAChBY,EAAST,YAAYH,SACtBe,KAINxB,WAAWoB,uBAEZpB,WAAWoB,qBAAuB,SAACM,GAAqB,OAAAC,aAAaD,KC1EpEE,KAAKC,OAEND,KAAKC,KAAO,SAAkBb,GAI1B,OAAU,KAFVA,EAAIc,OAAOd,KAEIe,MAAMf,GAEVA,EAGJA,EAAI,EAAI,GAAK,ICXvBc,OAAOE,YAERF,OAAOE,UAAY,SAAyBC,GAExC,MAAwB,iBAAVA,GAAsBC,SAASD,IAAUL,KAAKO,MAAMF,KAAWA,ICAhFjC,WAAWoC,cAEXpC,WAAmBoC,YAAcC,OAGjCrC,WAAWsC,eAEXtC,WAAmBsC,aAAeD,OAGlCrC,WAAWuC,cAEXvC,WAAmBuC,YAAcF,OAGjCrC,WAAWwC,cAEXxC,WAAmBwC,YAAcH,OAGjCrC,WAAWyC,aAEXzC,WAAmByC,WAAaJ,OAGhCrC,WAAW0C,aAEX1C,WAAmB0C,WAAaL"}