{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(python3:*)", "Bash(ss:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "Bash(grep:*)", "Bash(rg:*)", "WebFetch(domain:lmstudio.ai)", "Bash(npm run dev:*)", "Bash(ls:*)", "Bash(node:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx vue-tsc:*)", "Bash(rm:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:docs.comfy.org)", "WebFetch(domain:github.com)", "WebFetch(domain:huggingface.co)", "WebFetch(domain:9elements.com)", "Bash(awk:*)", "Bash(npx vue-tts:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(jq:*)", "Bash(/usr/bin/grep -n \"a4_tensor\\|A4.*tensor\" /mnt/f/AI-NEW/ComfyUI_windows_portable/ComfyUI/custom_nodes/ComfyUI_sloppy-comic/nodes/visual_novel_synthesizer.py)", "Bash(python test_lan_access.py:*)", "Bash(python test_webui_startup.py:*)", "<PERSON><PERSON>(python:*)", "Bash(cp:*)", "Bash(npm install:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm cache clean:*)", "<PERSON><PERSON>(touch:*)", "Bash(cd /mnt/h/AI/CosyVoice/cosyvoice-vue3)", "Bash(rg \"placeholder-comic\\.png\" --line-number)", "mcp__context7__resolve-library-id", "Bash(ln:*)", "Bash(grep -n \"getVADDebugInfo\\|enableVADListening\" /mnt/h/AI/CosyVoice/cosyvoice-vue3/src/services/api.ts)", "mcp__claudepoint__set_changelog"], "deny": []}}