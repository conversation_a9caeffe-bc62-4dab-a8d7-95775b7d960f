{"version": 3, "file": "particle-container.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/ParticleContainer.ts", "../../src/ParticleBuffer.ts", "../../src/ParticleRenderer.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { BLEND_MODES } from '@pixi/constants';\nimport { Container } from '@pixi/display';\nimport { hex2rgb } from '@pixi/utils';\n\nimport type { BaseTexture, Renderer } from '@pixi/core';\nimport type { ParticleBuffer } from './ParticleBuffer';\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Sprite } from '@pixi/sprite';\n\nexport interface IParticleProperties\n{\n    vertices?: boolean;\n    position?: boolean;\n    rotation?: boolean;\n    uvs?: boolean;\n    tint?: boolean;\n    alpha?: boolean;\n    scale?: boolean;\n}\n\n/**\n * The ParticleContainer class is a really fast version of the Container built solely for speed,\n * so use when you need a lot of sprites or particles.\n *\n * The tradeoff of the ParticleContainer is that most advanced functionality will not work.\n * ParticleContainer implements the basic object transform (position, scale, rotation)\n * and some advanced functionality like tint (as of v4.5.6).\n *\n * Other more advanced functionality like masking, children, filters, etc will not work on sprites in this batch.\n *\n * It's extremely easy to use:\n * ```js\n * let container = new ParticleContainer();\n *\n * for (let i = 0; i < 100; ++i)\n * {\n *     let sprite = PIXI.Sprite.from(\"myImage.png\");\n *     container.addChild(sprite);\n * }\n * ```\n *\n * And here you have a hundred sprites that will be rendered at the speed of light.\n * @memberof PIXI\n */\nexport class ParticleContainer extends Container<Sprite>\n{\n    /**\n     * The blend mode to be applied to the sprite. Apply a value of `PIXI.BLEND_MODES.NORMAL`\n     * to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public blendMode: BLEND_MODES;\n\n    /**\n     * If true, container allocates more batches in case there are more than `maxSize` particles.\n     * @default false\n     */\n    public autoResize: boolean;\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * Default to true here as performance is usually the priority for particles.\n     * @default true\n     */\n    public roundPixels: boolean;\n\n    /**\n     * The texture used to render the children.\n     * @readonly\n     */\n    public baseTexture: BaseTexture;\n    public tintRgb: Float32Array;\n\n    /** @private */\n    _maxSize: number;\n\n    /** @private */\n    _buffers: ParticleBuffer[];\n\n    /** @private */\n    _batchSize: number;\n\n    /**\n     * Set properties to be dynamic (true) / static (false).\n     * @private\n     */\n    _properties: boolean[];\n\n    /**\n     * For every batch, stores _updateID corresponding to the last change in that batch.\n     * @private\n     */\n    _bufferUpdateIDs: number[];\n\n    /**\n     * When child inserted, removed or changes position this number goes up.\n     * @private\n     */\n    _updateID: number;\n\n    /**\n     * The tint applied to the container.\n     * This is a hex value. A value of 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    private _tint: number;\n\n    /**\n     * @param maxSize - The maximum number of particles that can be rendered by the container.\n     *  Affects size of allocated buffers.\n     * @param properties - The properties of children that should be uploaded to the gpu and applied.\n     * @param {boolean} [properties.vertices=false] - When true, vertices be uploaded and applied.\n     *                  if sprite's ` scale/anchor/trim/frame/orig` is dynamic, please set `true`.\n     * @param {boolean} [properties.position=true] - When true, position be uploaded and applied.\n     * @param {boolean} [properties.rotation=false] - When true, rotation be uploaded and applied.\n     * @param {boolean} [properties.uvs=false] - When true, uvs be uploaded and applied.\n     * @param {boolean} [properties.tint=false] - When true, alpha and tint be uploaded and applied.\n     * @param {number} [batchSize=16384] - Number of particles per batch. If less than maxSize, it uses maxSize instead.\n     * @param {boolean} [autoResize=false] - If true, container allocates more batches in case\n     *  there are more than `maxSize` particles.\n     */\n    constructor(maxSize = 1500, properties?: IParticleProperties, batchSize = 16384, autoResize = false)\n    {\n        super();\n\n        // Making sure the batch size is valid\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        const maxBatchSize = 16384;\n\n        if (batchSize > maxBatchSize)\n        {\n            batchSize = maxBatchSize;\n        }\n\n        this._properties = [false, true, false, false, false];\n        this._maxSize = maxSize;\n        this._batchSize = batchSize;\n        this._buffers = null;\n        this._bufferUpdateIDs = [];\n        this._updateID = 0;\n\n        this.interactiveChildren = false;\n        this.blendMode = BLEND_MODES.NORMAL;\n        this.autoResize = autoResize;\n        this.roundPixels = true;\n        this.baseTexture = null;\n\n        this.setProperties(properties);\n\n        this._tint = 0;\n        this.tintRgb = new Float32Array(4);\n        this.tint = 0xFFFFFF;\n    }\n\n    /**\n     * Sets the private properties array to dynamic / static based on the passed properties object\n     * @param properties - The properties to be uploaded\n     */\n    public setProperties(properties: IParticleProperties): void\n    {\n        if (properties)\n        {\n            this._properties[0] = 'vertices' in properties || 'scale' in properties\n                ? !!properties.vertices || !!properties.scale : this._properties[0];\n            this._properties[1] = 'position' in properties ? !!properties.position : this._properties[1];\n            this._properties[2] = 'rotation' in properties ? !!properties.rotation : this._properties[2];\n            this._properties[3] = 'uvs' in properties ? !!properties.uvs : this._properties[3];\n            this._properties[4] = 'tint' in properties || 'alpha' in properties\n                ? !!properties.tint || !!properties.alpha : this._properties[4];\n        }\n    }\n\n    updateTransform(): void\n    {\n        // TODO don't need to!\n        this.displayObjectUpdateTransform();\n    }\n\n    /**\n     * The tint applied to the container. This is a hex value.\n     * A value of 0xFFFFFF will remove any tint effect.\n     * IMPORTANT: This is a WebGL only feature and will be ignored by the canvas renderer.\n     * @default 0xFFFFFF\n     */\n    get tint(): number\n    {\n        return this._tint;\n    }\n\n    set tint(value: number)\n    {\n        this._tint = value;\n        hex2rgb(value, this.tintRgb);\n    }\n\n    /**\n     * Renders the container using the WebGL renderer.\n     * @param renderer - The WebGL renderer.\n     */\n    public render(renderer: Renderer): void\n    {\n        if (!this.visible || this.worldAlpha <= 0 || !this.children.length || !this.renderable)\n        {\n            return;\n        }\n\n        if (!this.baseTexture)\n        {\n            this.baseTexture = this.children[0]._texture.baseTexture;\n            if (!this.baseTexture.valid)\n            {\n                this.baseTexture.once('update', () => this.onChildrenChange(0));\n            }\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins.particle);\n        renderer.plugins.particle.render(this);\n    }\n\n    /**\n     * Set the flag that static data should be updated to true\n     * @param smallestChildIndex - The smallest child index.\n     */\n    protected onChildrenChange(smallestChildIndex: number): void\n    {\n        const bufferIndex = Math.floor(smallestChildIndex / this._batchSize);\n\n        while (this._bufferUpdateIDs.length < bufferIndex)\n        {\n            this._bufferUpdateIDs.push(0);\n        }\n        this._bufferUpdateIDs[bufferIndex] = ++this._updateID;\n    }\n\n    public dispose(): void\n    {\n        if (this._buffers)\n        {\n            for (let i = 0; i < this._buffers.length; ++i)\n            {\n                this._buffers[i].destroy();\n            }\n\n            this._buffers = null;\n        }\n    }\n\n    /**\n     * Destroys the container\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their\n     *  destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this.dispose();\n\n        this._properties = null;\n        this._buffers = null;\n        this._bufferUpdateIDs = null;\n    }\n}\n", "import { createIndicesForQuads } from '@pixi/utils';\nimport { Geo<PERSON>, Buffer } from '@pixi/core';\nimport { TYPES } from '@pixi/constants';\n\nimport type { Sprite } from '@pixi/sprite';\nimport type { IParticleRendererProperty } from './ParticleRenderer';\n\n/*\n * <AUTHOR>\n *\n * Big thanks to the very clever <PERSON> <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that\n * they now share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleBuffer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleBuffer.java\n */\n\n/**\n * The particle buffer manages the static and dynamic buffers for a particle container.\n * @private\n * @memberof PIXI\n */\nexport class ParticleBuffer\n{\n    public geometry: Geometry;\n    public staticStride: number;\n    public staticBuffer: Buffer;\n    public staticData: Float32Array;\n    public staticDataUint32: Uint32Array;\n    public dynamicStride: number;\n    public dynamicBuffer: Buffer;\n    public dynamicData: Float32Array;\n    public dynamicDataUint32: Uint32Array;\n    public _updateID: number;\n\n    /** Holds the indices of the geometry (quads) to draw. */\n    indexBuffer: Buffer;\n\n    /** The number of particles the buffer can hold. */\n    private size: number;\n\n    /** A list of the properties that are dynamic. */\n    private dynamicProperties: IParticleRendererProperty[];\n\n    /** A list of the properties that are static. */\n    private staticProperties: IParticleRendererProperty[];\n\n    /**\n     * @param {object} properties - The properties to upload.\n     * @param {boolean[]} dynamicPropertyFlags - Flags for which properties are dynamic.\n     * @param {number} size - The size of the batch.\n     */\n    constructor(properties: IParticleRendererProperty[], dynamicPropertyFlags: boolean[], size: number)\n    {\n        this.geometry = new Geometry();\n\n        this.indexBuffer = null;\n\n        this.size = size;\n        this.dynamicProperties = [];\n        this.staticProperties = [];\n\n        for (let i = 0; i < properties.length; ++i)\n        {\n            let property = properties[i];\n\n            // Make copy of properties object so that when we edit the offset it doesn't\n            // change all other instances of the object literal\n            property = {\n                attributeName: property.attributeName,\n                size: property.size,\n                uploadFunction: property.uploadFunction,\n                type: property.type || TYPES.FLOAT,\n                offset: property.offset,\n            };\n\n            if (dynamicPropertyFlags[i])\n            {\n                this.dynamicProperties.push(property);\n            }\n            else\n            {\n                this.staticProperties.push(property);\n            }\n        }\n\n        this.staticStride = 0;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n\n        this.dynamicStride = 0;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this._updateID = 0;\n\n        this.initBuffers();\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    private initBuffers(): void\n    {\n        const geometry = this.geometry;\n\n        let dynamicOffset = 0;\n\n        this.indexBuffer = new Buffer(createIndicesForQuads(this.size), true, true);\n        geometry.addIndex(this.indexBuffer);\n\n        this.dynamicStride = 0;\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.offset = dynamicOffset;\n            dynamicOffset += property.size;\n            this.dynamicStride += property.size;\n        }\n\n        const dynBuffer = new ArrayBuffer(this.size * this.dynamicStride * 4 * 4);\n\n        this.dynamicData = new Float32Array(dynBuffer);\n        this.dynamicDataUint32 = new Uint32Array(dynBuffer);\n        this.dynamicBuffer = new Buffer(this.dynamicData, false, false);\n\n        // static //\n        let staticOffset = 0;\n\n        this.staticStride = 0;\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            property.offset = staticOffset;\n            staticOffset += property.size;\n            this.staticStride += property.size;\n        }\n\n        const statBuffer = new ArrayBuffer(this.size * this.staticStride * 4 * 4);\n\n        this.staticData = new Float32Array(statBuffer);\n        this.staticDataUint32 = new Uint32Array(statBuffer);\n        this.staticBuffer = new Buffer(this.staticData, true, false);\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.dynamicBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.dynamicStride * 4,\n                property.offset * 4\n            );\n        }\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.staticBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.staticStride * 4,\n                property.offset * 4\n            );\n        }\n    }\n\n    /**\n     * Uploads the dynamic properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadDynamic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.dynamicProperties.length; i++)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.dynamicDataUint32 : this.dynamicData,\n                this.dynamicStride, property.offset);\n        }\n\n        this.dynamicBuffer._updateID++;\n    }\n\n    /**\n     * Uploads the static properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadStatic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.staticProperties.length; i++)\n        {\n            const property = this.staticProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.staticDataUint32 : this.staticData,\n                this.staticStride, property.offset);\n        }\n\n        this.staticBuffer._updateID++;\n    }\n\n    /** Destroys the ParticleBuffer. */\n    destroy(): void\n    {\n        this.indexBuffer = null;\n\n        this.dynamicProperties = null;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this.staticProperties = null;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n        // all buffers are destroyed inside geometry\n        this.geometry.destroy();\n    }\n}\n", "import { TYPES } from '@pixi/constants';\nimport { ExtensionType, ObjectRenderer, Shader, State } from '@pixi/core';\nimport { Matrix } from '@pixi/math';\nimport { correctBlendMode, premultiplyRgba, premultiplyTint } from '@pixi/utils';\nimport { ParticleBuffer } from './ParticleBuffer';\nimport fragment from './particles.frag';\nimport vertex from './particles.vert';\n\nimport type { ParticleContainer } from './ParticleContainer';\nimport type { Renderer, ExtensionMetadata } from '@pixi/core';\nimport type { Sprite } from '@pixi/sprite';\n\nexport interface IParticleRendererProperty\n{\n    attributeName: string;\n    size: number;\n    type?: TYPES;\n    uploadFunction: (...params: any[]) => any;\n    offset: number;\n}\n\n/*\n * <AUTHOR> <PERSON>\n *\n * Big thanks to the very clever <PERSON>riers <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that they now\n * share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleRenderer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleRenderer.java\n */\n\n/**\n * Renderer for Particles that is designer for speed over feature set.\n * @memberof PIXI\n */\nexport class ParticleRenderer extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'particle',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /** The WebGL state in which this renderer will work. */\n    public readonly state: State;\n\n    /** The default shader that is used if a sprite doesn't have a more specific one. */\n    public shader: Shader;\n    public tempMatrix: Matrix;\n    public properties: IParticleRendererProperty[];\n\n    /**\n     * @param renderer - The renderer this sprite batch works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        // and max number of element in the index buffer is 16384 * 6 = 98304\n        // Creating a full index buffer, overhead is 98304 * 2 = 196Ko\n        // let numIndices = 98304;\n\n        this.shader = null;\n\n        this.properties = null;\n\n        this.tempMatrix = new Matrix();\n\n        this.properties = [\n            // verticesData\n            {\n                attributeName: 'aVertexPosition',\n                size: 2,\n                uploadFunction: this.uploadVertices,\n                offset: 0,\n            },\n            // positionData\n            {\n                attributeName: 'aPositionCoord',\n                size: 2,\n                uploadFunction: this.uploadPosition,\n                offset: 0,\n            },\n            // rotationData\n            {\n                attributeName: 'aRotation',\n                size: 1,\n                uploadFunction: this.uploadRotation,\n                offset: 0,\n            },\n            // uvsData\n            {\n                attributeName: 'aTextureCoord',\n                size: 2,\n                uploadFunction: this.uploadUvs,\n                offset: 0,\n            },\n            // tintData\n            {\n                attributeName: 'aColor',\n                size: 1,\n                type: TYPES.UNSIGNED_BYTE,\n                uploadFunction: this.uploadTint,\n                offset: 0,\n            },\n        ];\n\n        this.shader = Shader.from(vertex, fragment, {});\n        this.state = State.for2d();\n    }\n\n    /**\n     * Renders the particle container object.\n     * @param container - The container to render using this ParticleRenderer.\n     */\n    public render(container: ParticleContainer): void\n    {\n        const children = container.children;\n        const maxSize = container._maxSize;\n        const batchSize = container._batchSize;\n        const renderer = this.renderer;\n        let totalChildren = children.length;\n\n        if (totalChildren === 0)\n        {\n            return;\n        }\n        else if (totalChildren > maxSize && !container.autoResize)\n        {\n            totalChildren = maxSize;\n        }\n\n        let buffers = container._buffers;\n\n        if (!buffers)\n        {\n            buffers = container._buffers = this.generateBuffers(container);\n        }\n\n        const baseTexture = children[0]._texture.baseTexture;\n        const premultiplied = baseTexture.alphaMode > 0;\n\n        // if the uvs have not updated then no point rendering just yet!\n        this.state.blendMode = correctBlendMode(container.blendMode, premultiplied);\n        renderer.state.set(this.state);\n\n        const gl = renderer.gl;\n\n        const m = container.worldTransform.copyTo(this.tempMatrix);\n\n        m.prepend(renderer.globalUniforms.uniforms.projectionMatrix);\n\n        this.shader.uniforms.translationMatrix = m.toArray(true);\n\n        this.shader.uniforms.uColor = premultiplyRgba(container.tintRgb,\n            container.worldAlpha, this.shader.uniforms.uColor, premultiplied);\n\n        this.shader.uniforms.uSampler = baseTexture;\n\n        this.renderer.shader.bind(this.shader);\n\n        let updateStatic = false;\n\n        // now lets upload and render the buffers..\n        for (let i = 0, j = 0; i < totalChildren; i += batchSize, j += 1)\n        {\n            let amount = (totalChildren - i);\n\n            if (amount > batchSize)\n            {\n                amount = batchSize;\n            }\n\n            if (j >= buffers.length)\n            {\n                buffers.push(this._generateOneMoreBuffer(container));\n            }\n\n            const buffer = buffers[j];\n\n            // we always upload the dynamic\n            buffer.uploadDynamic(children, i, amount);\n\n            const bid = container._bufferUpdateIDs[j] || 0;\n\n            updateStatic = updateStatic || (buffer._updateID < bid);\n            // we only upload the static content when we have to!\n            if (updateStatic)\n            {\n                buffer._updateID = container._updateID;\n                buffer.uploadStatic(children, i, amount);\n            }\n\n            // bind the buffer\n            renderer.geometry.bind(buffer.geometry);\n            gl.drawElements(gl.TRIANGLES, amount * 6, gl.UNSIGNED_SHORT, 0);\n        }\n    }\n\n    /**\n     * Creates one particle buffer for each child in the container we want to render and updates internal properties.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The buffers\n     */\n    private generateBuffers(container: ParticleContainer): ParticleBuffer[]\n    {\n        const buffers = [];\n        const size = container._maxSize;\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        for (let i = 0; i < size; i += batchSize)\n        {\n            buffers.push(new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize));\n        }\n\n        return buffers;\n    }\n\n    /**\n     * Creates one more particle buffer, because container has autoResize feature.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The generated buffer\n     */\n    private _generateOneMoreBuffer(container: ParticleContainer): ParticleBuffer\n    {\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        return new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize);\n    }\n\n    /**\n     * Uploads the vertices.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their vertices uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadVertices(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        let w0 = 0;\n        let w1 = 0;\n        let h0 = 0;\n        let h1 = 0;\n\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const texture = sprite._texture;\n            const sx = sprite.scale.x;\n            const sy = sprite.scale.y;\n            const trim = texture.trim;\n            const orig = texture.orig;\n\n            if (trim)\n            {\n                // if the sprite is trimmed and is not a tilingsprite then we need to add the\n                // extra space before transforming the sprite coords..\n                w1 = trim.x - (sprite.anchor.x * orig.width);\n                w0 = w1 + trim.width;\n\n                h1 = trim.y - (sprite.anchor.y * orig.height);\n                h0 = h1 + trim.height;\n            }\n            else\n            {\n                w0 = (orig.width) * (1 - sprite.anchor.x);\n                w1 = (orig.width) * -sprite.anchor.x;\n\n                h0 = orig.height * (1 - sprite.anchor.y);\n                h1 = orig.height * -sprite.anchor.y;\n            }\n\n            array[offset] = w1 * sx;\n            array[offset + 1] = h1 * sy;\n\n            array[offset + stride] = w0 * sx;\n            array[offset + stride + 1] = h1 * sy;\n\n            array[offset + (stride * 2)] = w0 * sx;\n            array[offset + (stride * 2) + 1] = h0 * sy;\n\n            array[offset + (stride * 3)] = w1 * sx;\n            array[offset + (stride * 3) + 1] = h0 * sy;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the position.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their positions uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadPosition(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spritePosition = children[startIndex + i].position;\n\n            array[offset] = spritePosition.x;\n            array[offset + 1] = spritePosition.y;\n\n            array[offset + stride] = spritePosition.x;\n            array[offset + stride + 1] = spritePosition.y;\n\n            array[offset + (stride * 2)] = spritePosition.x;\n            array[offset + (stride * 2) + 1] = spritePosition.y;\n\n            array[offset + (stride * 3)] = spritePosition.x;\n            array[offset + (stride * 3) + 1] = spritePosition.y;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the rotation.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadRotation(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spriteRotation = children[startIndex + i].rotation;\n\n            array[offset] = spriteRotation;\n            array[offset + stride] = spriteRotation;\n            array[offset + (stride * 2)] = spriteRotation;\n            array[offset + (stride * 3)] = spriteRotation;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the UVs.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadUvs(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const textureUvs = children[startIndex + i]._texture._uvs;\n\n            if (textureUvs)\n            {\n                array[offset] = textureUvs.x0;\n                array[offset + 1] = textureUvs.y0;\n\n                array[offset + stride] = textureUvs.x1;\n                array[offset + stride + 1] = textureUvs.y1;\n\n                array[offset + (stride * 2)] = textureUvs.x2;\n                array[offset + (stride * 2) + 1] = textureUvs.y2;\n\n                array[offset + (stride * 3)] = textureUvs.x3;\n                array[offset + (stride * 3) + 1] = textureUvs.y3;\n\n                offset += stride * 4;\n            }\n            else\n            {\n                // TODO you know this can be easier!\n                array[offset] = 0;\n                array[offset + 1] = 0;\n\n                array[offset + stride] = 0;\n                array[offset + stride + 1] = 0;\n\n                array[offset + (stride * 2)] = 0;\n                array[offset + (stride * 2) + 1] = 0;\n\n                array[offset + (stride * 3)] = 0;\n                array[offset + (stride * 3) + 1] = 0;\n\n                offset += stride * 4;\n            }\n        }\n    }\n\n    /**\n     * Uploads the tint.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadTint(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const premultiplied = sprite._texture.baseTexture.alphaMode > 0;\n            const alpha = sprite.alpha;\n\n            // we dont call extra function if alpha is 1.0, that's faster\n            const argb = alpha < 1.0 && premultiplied\n                ? premultiplyTint(sprite._tintRGB, alpha) : sprite._tintRGB + (alpha * 255 << 24);\n\n            array[offset] = argb;\n            array[offset + stride] = argb;\n            array[offset + (stride * 2)] = argb;\n            array[offset + (stride * 3)] = argb;\n\n            offset += stride * 4;\n        }\n    }\n\n    /** Destroys the ParticleRenderer. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        if (this.shader)\n        {\n            this.shader.destroy();\n            this.shader = null;\n        }\n\n        this.tempMatrix = null;\n    }\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "this", "constructor", "prototype", "create", "ParticleContainer", "_super", "maxSize", "properties", "batchSize", "autoResize", "_this", "_properties", "_maxSize", "_batchSize", "_buffers", "_bufferUpdateIDs", "_updateID", "interactiveChildren", "blendMode", "BLEND_MODES", "NORMAL", "roundPixels", "baseTexture", "setProperties", "_tint", "tintRgb", "Float32Array", "tint", "vertices", "scale", "position", "rotation", "uvs", "alpha", "updateTransform", "displayObjectUpdateTransform", "defineProperty", "get", "set", "value", "hex2rgb", "render", "renderer", "visible", "worldAlpha", "children", "length", "renderable", "_texture", "valid", "once", "onChildrenChange", "batch", "setObjectR<PERSON><PERSON>", "plugins", "particle", "smallestChildIndex", "bufferIndex", "Math", "floor", "push", "dispose", "i", "destroy", "options", "call", "Container", "ParticleBuffer", "dynamicPropertyFlags", "size", "geometry", "Geometry", "indexBuffer", "dynamicProperties", "staticProperties", "property", "attributeName", "uploadFunction", "type", "TYPES", "FLOAT", "offset", "staticStride", "staticBuffer", "staticData", "staticDataUint32", "dynamicStride", "dynamicBuffer", "dynamicData", "dynamicDataUint32", "initBuffers", "dynamicOffset", "<PERSON><PERSON><PERSON>", "createIndicesForQuads", "addIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint32Array", "staticOffset", "statBuffer", "addAttribute", "UNSIGNED_BYTE", "uploadDynamic", "startIndex", "amount", "uploadStatic", "Particle<PERSON><PERSON><PERSON>", "shader", "tempMatrix", "Matrix", "uploadVertices", "uploadPosition", "uploadRotation", "uploadUvs", "uploadTint", "Shader", "from", "state", "State", "for2d", "container", "totalChildren", "buffers", "generateBuffers", "premultiplied", "alphaMode", "correctBlendMode", "gl", "m", "worldTransform", "copyTo", "prepend", "globalUniforms", "uniforms", "projectionMatrix", "translationMatrix", "toArray", "uColor", "premultiplyRgba", "uSampler", "bind", "updateStatic", "j", "_generateOneMoreBuffer", "buffer", "bid", "drawElements", "TRIANGLES", "UNSIGNED_SHORT", "array", "stride", "w0", "w1", "h0", "h1", "sprite", "texture", "sx", "x", "sy", "y", "trim", "orig", "anchor", "width", "height", "spritePosition", "spriteRotation", "textureUvs", "_uvs", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "argb", "premultiplyTint", "_tintRGB", "extension", "name", "ExtensionType", "RendererPlugin", "O<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;wFAgBA,IAAIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,IAGrB,SAASO,EAAUR,EAAGC,GAEzB,SAASQ,IAAOC,KAAKC,YAAcX,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEY,UAAkB,OAANX,EAAaC,OAAOW,OAAOZ,IAAMQ,EAAGG,UAAYX,EAAEW,UAAW,IAAIH,GCkBnF,IAAAK,EAAA,SAAAC,GA+EI,SAAAD,EAAYE,EAAgBC,EAAkCC,EAAmBC,QAArE,IAAAH,IAAAA,EAAc,WAAoC,IAAAE,IAAAA,EAAiB,YAAE,IAAAC,IAAAA,GAAkB,GAAnG,IAAAC,EAEIL,cA8BHL,YAvBOQ,EAFiB,QAIjBA,EAJiB,OAOrBE,EAAKC,YAAc,EAAC,GAAO,GAAM,GAAO,GAAO,GAC/CD,EAAKE,SAAWN,EAChBI,EAAKG,WAAaL,EAClBE,EAAKI,SAAW,KAChBJ,EAAKK,iBAAmB,GACxBL,EAAKM,UAAY,EAEjBN,EAAKO,qBAAsB,EAC3BP,EAAKQ,UAAYC,EAAWA,YAACC,OAC7BV,EAAKD,WAAaA,EAClBC,EAAKW,aAAc,EACnBX,EAAKY,YAAc,KAEnBZ,EAAKa,cAAchB,GAEnBG,EAAKc,MAAQ,EACbd,EAAKe,QAAU,IAAIC,aAAa,GAChChB,EAAKiB,KAAO,WAqHpB,OAnOuC7B,EAAiBM,EAAAC,GAqH7CD,EAAaF,UAAAqB,cAApB,SAAqBhB,GAEbA,IAEAP,KAAKW,YAAY,GAAK,aAAcJ,GAAc,UAAWA,IACrDA,EAAWqB,YAAcrB,EAAWsB,MAAQ7B,KAAKW,YAAY,GACrEX,KAAKW,YAAY,GAAK,aAAcJ,IAAeA,EAAWuB,SAAW9B,KAAKW,YAAY,GAC1FX,KAAKW,YAAY,GAAK,aAAcJ,IAAeA,EAAWwB,SAAW/B,KAAKW,YAAY,GAC1FX,KAAKW,YAAY,GAAK,QAASJ,IAAeA,EAAWyB,IAAMhC,KAAKW,YAAY,GAChFX,KAAKW,YAAY,GAAK,SAAUJ,GAAc,UAAWA,IACjDA,EAAWoB,QAAUpB,EAAW0B,MAAQjC,KAAKW,YAAY,KAIzEP,EAAAF,UAAAgC,gBAAA,WAGIlC,KAAKmC,gCAST3C,OAAA4C,eAAIhC,EAAIF,UAAA,OAAA,CAARmC,IAAA,WAEI,OAAOrC,KAAKwB,OAGhBc,IAAA,SAASC,GAELvC,KAAKwB,MAAQe,EACbC,EAAAA,QAAQD,EAAOvC,KAAKyB,0CAOjBrB,EAAMF,UAAAuC,OAAb,SAAcC,GAAd,IAkBChC,EAAAV,KAhBQA,KAAK2C,WAAW3C,KAAK4C,YAAc,IAAM5C,KAAK6C,SAASC,QAAW9C,KAAK+C,aAKvE/C,KAAKsB,cAENtB,KAAKsB,YAActB,KAAK6C,SAAS,GAAGG,SAAS1B,YACxCtB,KAAKsB,YAAY2B,OAElBjD,KAAKsB,YAAY4B,KAAK,UAAU,WAAM,OAAAxC,EAAKyC,iBAAiB,OAIpET,EAASU,MAAMC,kBAAkBX,EAASY,QAAQC,UAClDb,EAASY,QAAQC,SAASd,OAAOzC,QAO3BI,EAAgBF,UAAAiD,iBAA1B,SAA2BK,GAIvB,IAFA,IAAMC,EAAcC,KAAKC,MAAMH,EAAqBxD,KAAKa,YAElDb,KAAKe,iBAAiB+B,OAASW,GAElCzD,KAAKe,iBAAiB6C,KAAK,GAE/B5D,KAAKe,iBAAiB0C,KAAiBzD,KAAKgB,WAGzCZ,EAAAF,UAAA2D,QAAP,WAEI,GAAI7D,KAAKc,SACT,CACI,IAAK,IAAIgD,EAAI,EAAGA,EAAI9D,KAAKc,SAASgC,SAAUgB,EAExC9D,KAAKc,SAASgD,GAAGC,UAGrB/D,KAAKc,SAAW,OAejBV,EAAOF,UAAA6D,QAAd,SAAeC,GAEX3D,EAAAH,UAAM6D,QAAOE,KAAAjE,KAACgE,GAEdhE,KAAK6D,UAEL7D,KAAKW,YAAc,KACnBX,KAAKc,SAAW,KAChBd,KAAKe,iBAAmB,MAE/BX,EAnOD,CAAuC8D,aCpBvCC,EAAA,WA8BI,SAAAA,EAAY5D,EAAyC6D,EAAiCC,GAElFrE,KAAKsE,SAAW,IAAIC,EAAAA,SAEpBvE,KAAKwE,YAAc,KAEnBxE,KAAKqE,KAAOA,EACZrE,KAAKyE,kBAAoB,GACzBzE,KAAK0E,iBAAmB,GAExB,IAAK,IAAIZ,EAAI,EAAGA,EAAIvD,EAAWuC,SAAUgB,EACzC,CACI,IAAIa,EAAWpE,EAAWuD,GAI1Ba,EAAW,CACPC,cAAeD,EAASC,cACxBP,KAAMM,EAASN,KACfQ,eAAgBF,EAASE,eACzBC,KAAMH,EAASG,MAAQC,EAAAA,MAAMC,MAC7BC,OAAQN,EAASM,QAGjBb,EAAqBN,GAErB9D,KAAKyE,kBAAkBb,KAAKe,GAI5B3E,KAAK0E,iBAAiBd,KAAKe,GAInC3E,KAAKkF,aAAe,EACpBlF,KAAKmF,aAAe,KACpBnF,KAAKoF,WAAa,KAClBpF,KAAKqF,iBAAmB,KAExBrF,KAAKsF,cAAgB,EACrBtF,KAAKuF,cAAgB,KACrBvF,KAAKwF,YAAc,KACnBxF,KAAKyF,kBAAoB,KAEzBzF,KAAKgB,UAAY,EAEjBhB,KAAK0F,cA0Ib,OAtIYvB,EAAAjE,UAAAwF,YAAR,WAEI,IAAMpB,EAAWtE,KAAKsE,SAElBqB,EAAgB,EAEpB3F,KAAKwE,YAAc,IAAIoB,EAAAA,OAAOC,EAAqBA,sBAAC7F,KAAKqE,OAAO,GAAM,GACtEC,EAASwB,SAAS9F,KAAKwE,aAEvBxE,KAAKsF,cAAgB,EAErB,IAAK,IAAIxB,EAAI,EAAGA,EAAI9D,KAAKyE,kBAAkB3B,SAAUgB,EACrD,EACUa,EAAW3E,KAAKyE,kBAAkBX,IAE/BmB,OAASU,EAClBA,GAAiBhB,EAASN,KAC1BrE,KAAKsF,eAAiBX,EAASN,KAGnC,IAAM0B,EAAY,IAAIC,YAAYhG,KAAKqE,KAAOrE,KAAKsF,cAAgB,EAAI,GAEvEtF,KAAKwF,YAAc,IAAI9D,aAAaqE,GACpC/F,KAAKyF,kBAAoB,IAAIQ,YAAYF,GACzC/F,KAAKuF,cAAgB,IAAIK,EAAMA,OAAC5F,KAAKwF,aAAa,GAAO,GAGzD,IAAIU,EAAe,EAEnBlG,KAAKkF,aAAe,EAEpB,IAASpB,EAAI,EAAGA,EAAI9D,KAAK0E,iBAAiB5B,SAAUgB,EACpD,EACUa,EAAW3E,KAAK0E,iBAAiBZ,IAE9BmB,OAASiB,EAClBA,GAAgBvB,EAASN,KACzBrE,KAAKkF,cAAgBP,EAASN,KAGlC,IAAM8B,EAAa,IAAIH,YAAYhG,KAAKqE,KAAOrE,KAAKkF,aAAe,EAAI,GAEvElF,KAAKoF,WAAa,IAAI1D,aAAayE,GACnCnG,KAAKqF,iBAAmB,IAAIY,YAAYE,GACxCnG,KAAKmF,aAAe,IAAIS,EAAMA,OAAC5F,KAAKoF,YAAY,GAAM,GAEtD,IAAStB,EAAI,EAAGA,EAAI9D,KAAKyE,kBAAkB3B,SAAUgB,EACrD,CACI,IAAMa,EAAW3E,KAAKyE,kBAAkBX,GAExCQ,EAAS8B,aACLzB,EAASC,cACT5E,KAAKuF,cACL,EACAZ,EAASG,OAASC,EAAAA,MAAMsB,cACxB1B,EAASG,KACY,EAArB9E,KAAKsF,cACa,EAAlBX,EAASM,QAIjB,IAASnB,EAAI,EAAGA,EAAI9D,KAAK0E,iBAAiB5B,SAAUgB,EACpD,CACUa,EAAW3E,KAAK0E,iBAAiBZ,GAEvCQ,EAAS8B,aACLzB,EAASC,cACT5E,KAAKmF,aACL,EACAR,EAASG,OAASC,EAAAA,MAAMsB,cACxB1B,EAASG,KACW,EAApB9E,KAAKkF,aACa,EAAlBP,EAASM,UAWrBd,EAAAjE,UAAAoG,cAAA,SAAczD,EAAoB0D,EAAoBC,GAElD,IAAK,IAAI1C,EAAI,EAAGA,EAAI9D,KAAKyE,kBAAkB3B,OAAQgB,IACnD,CACI,IAAMa,EAAW3E,KAAKyE,kBAAkBX,GAExCa,EAASE,eAAehC,EAAU0D,EAAYC,EAC1C7B,EAASG,OAASC,QAAMsB,cAAgBrG,KAAKyF,kBAAoBzF,KAAKwF,YACtExF,KAAKsF,cAAeX,EAASM,QAGrCjF,KAAKuF,cAAcvE,aASvBmD,EAAAjE,UAAAuG,aAAA,SAAa5D,EAAoB0D,EAAoBC,GAEjD,IAAK,IAAI1C,EAAI,EAAGA,EAAI9D,KAAK0E,iBAAiB5B,OAAQgB,IAClD,CACI,IAAMa,EAAW3E,KAAK0E,iBAAiBZ,GAEvCa,EAASE,eAAehC,EAAU0D,EAAYC,EAC1C7B,EAASG,OAASC,QAAMsB,cAAgBrG,KAAKqF,iBAAmBrF,KAAKoF,WACrEpF,KAAKkF,aAAcP,EAASM,QAGpCjF,KAAKmF,aAAanE,aAItBmD,EAAAjE,UAAA6D,QAAA,WAEI/D,KAAKwE,YAAc,KAEnBxE,KAAKyE,kBAAoB,KACzBzE,KAAKuF,cAAgB,KACrBvF,KAAKwF,YAAc,KACnBxF,KAAKyF,kBAAoB,KAEzBzF,KAAK0E,iBAAmB,KACxB1E,KAAKmF,aAAe,KACpBnF,KAAKoF,WAAa,KAClBpF,KAAKqF,iBAAmB,KAExBrF,KAAKsE,SAASP,WAErBI,KCzMDuC,EAAA,SAAArG,GAmBI,SAAAqG,EAAYhE,GAAZ,IAEIhC,EAAAL,EAAA4D,KAAAjE,KAAM0C,IAuDT1C,YA/CGU,EAAKiG,OAAS,KAEdjG,EAAKH,WAAa,KAElBG,EAAKkG,WAAa,IAAIC,EAAAA,OAEtBnG,EAAKH,WAAa,CAEd,CACIqE,cAAe,kBACfP,KAAM,EACNQ,eAAgBnE,EAAKoG,eACrB7B,OAAQ,GAGZ,CACIL,cAAe,iBACfP,KAAM,EACNQ,eAAgBnE,EAAKqG,eACrB9B,OAAQ,GAGZ,CACIL,cAAe,YACfP,KAAM,EACNQ,eAAgBnE,EAAKsG,eACrB/B,OAAQ,GAGZ,CACIL,cAAe,gBACfP,KAAM,EACNQ,eAAgBnE,EAAKuG,UACrBhC,OAAQ,GAGZ,CACIL,cAAe,SACfP,KAAM,EACNS,KAAMC,EAAKA,MAACsB,cACZxB,eAAgBnE,EAAKwG,WACrBjC,OAAQ,IAIhBvE,EAAKiG,OAASQ,SAAOC,y2BAAuB,IAC5C1G,EAAK2G,MAAQC,QAAMC,UA4V3B,OAvasCzH,EAAc4G,EAAArG,GAkFzCqG,EAAMxG,UAAAuC,OAAb,SAAc+E,GAEV,IAAM3E,EAAW2E,EAAU3E,SACrBvC,EAAUkH,EAAU5G,SACpBJ,EAAYgH,EAAU3G,WACtB6B,EAAW1C,KAAK0C,SAClB+E,EAAgB5E,EAASC,OAE7B,GAAsB,IAAlB2E,EAAJ,CAISA,EAAgBnH,IAAYkH,EAAU/G,aAE3CgH,EAAgBnH,GAGpB,IAAIoH,EAAUF,EAAU1G,SAEnB4G,IAEDA,EAAUF,EAAU1G,SAAWd,KAAK2H,gBAAgBH,IAGxD,IAAMlG,EAAcuB,EAAS,GAAGG,SAAS1B,YACnCsG,EAAgBtG,EAAYuG,UAAY,EAG9C7H,KAAKqH,MAAMnG,UAAY4G,EAAAA,iBAAiBN,EAAUtG,UAAW0G,GAC7DlF,EAAS2E,MAAM/E,IAAItC,KAAKqH,OAExB,IAAMU,EAAKrF,EAASqF,GAEdC,EAAIR,EAAUS,eAAeC,OAAOlI,KAAK4G,YAE/CoB,EAAEG,QAAQzF,EAAS0F,eAAeC,SAASC,kBAE3CtI,KAAK2G,OAAO0B,SAASE,kBAAoBP,EAAEQ,SAAQ,GAEnDxI,KAAK2G,OAAO0B,SAASI,OAASC,EAAeA,gBAAClB,EAAU/F,QACpD+F,EAAU5E,WAAY5C,KAAK2G,OAAO0B,SAASI,OAAQb,GAEvD5H,KAAK2G,OAAO0B,SAASM,SAAWrH,EAEhCtB,KAAK0C,SAASiE,OAAOiC,KAAK5I,KAAK2G,QAK/B,IAHA,IAAIkC,GAAe,EAGV/E,EAAI,EAAGgF,EAAI,EAAGhF,EAAI2D,EAAe3D,GAAKtD,EAAWsI,GAAK,EAC/D,CACI,IAAItC,EAAUiB,EAAgB3D,EAE1B0C,EAAShG,IAETgG,EAAShG,GAGTsI,GAAKpB,EAAQ5E,QAEb4E,EAAQ9D,KAAK5D,KAAK+I,uBAAuBvB,IAG7C,IAAMwB,EAAStB,EAAQoB,GAGvBE,EAAO1C,cAAczD,EAAUiB,EAAG0C,GAElC,IAAMyC,EAAMzB,EAAUzG,iBAAiB+H,IAAM,GAE7CD,EAAeA,GAAiBG,EAAOhI,UAAYiI,KAI/CD,EAAOhI,UAAYwG,EAAUxG,UAC7BgI,EAAOvC,aAAa5D,EAAUiB,EAAG0C,IAIrC9D,EAAS4B,SAASsE,KAAKI,EAAO1E,UAC9ByD,EAAGmB,aAAanB,EAAGoB,UAAoB,EAAT3C,EAAYuB,EAAGqB,eAAgB,MAS7D1C,EAAexG,UAAAyH,gBAAvB,SAAwBH,GAOpB,IALA,IAAME,EAAU,GACVrD,EAAOmD,EAAU5G,SACjBJ,EAAYgH,EAAU3G,WACtBuD,EAAuBoD,EAAU7G,YAE9BmD,EAAI,EAAGA,EAAIO,EAAMP,GAAKtD,EAE3BkH,EAAQ9D,KAAK,IAAIO,EAAenE,KAAKO,WAAY6D,EAAsB5D,IAG3E,OAAOkH,GAQHhB,EAAsBxG,UAAA6I,uBAA9B,SAA+BvB,GAE3B,IAAMhH,EAAYgH,EAAU3G,WACtBuD,EAAuBoD,EAAU7G,YAEvC,OAAO,IAAIwD,EAAenE,KAAKO,WAAY6D,EAAsB5D,IAY9DkG,EAAAxG,UAAA4G,eAAP,SACIjE,EAAoB0D,EAAoBC,EACxC6C,EAAiBC,EAAgBrE,GAQjC,IALA,IAAIsE,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAEA5F,EAAI,EAAGA,EAAI0C,IAAU1C,EAC9B,CACI,IAAM6F,EAAS9G,EAAS0D,EAAazC,GAC/B8F,EAAUD,EAAO3G,SACjB6G,EAAKF,EAAO9H,MAAMiI,EAClBC,EAAKJ,EAAO9H,MAAMmI,EAClBC,EAAOL,EAAQK,KACfC,EAAON,EAAQM,KAEjBD,GAKAV,GADAC,EAAKS,EAAKH,EAAKH,EAAOQ,OAAOL,EAAII,EAAKE,OAC5BH,EAAKG,MAGfX,GADAC,EAAKO,EAAKD,EAAKL,EAAOQ,OAAOH,EAAIE,EAAKG,QAC5BJ,EAAKI,SAIfd,EAAMW,EAAU,OAAK,EAAIP,EAAOQ,OAAOL,GACvCN,EAAMU,EAAU,OAAKP,EAAOQ,OAAOL,EAEnCL,EAAKS,EAAKG,QAAU,EAAIV,EAAOQ,OAAOH,GACtCN,EAAKQ,EAAKG,QAAUV,EAAOQ,OAAOH,GAGtCX,EAAMpE,GAAUuE,EAAKK,EACrBR,EAAMpE,EAAS,GAAKyE,EAAKK,EAEzBV,EAAMpE,EAASqE,GAAUC,EAAKM,EAC9BR,EAAMpE,EAASqE,EAAS,GAAKI,EAAKK,EAElCV,EAAMpE,EAAmB,EAATqE,GAAeC,EAAKM,EACpCR,EAAMpE,EAAmB,EAATqE,EAAc,GAAKG,EAAKM,EAExCV,EAAMpE,EAAmB,EAATqE,GAAeE,EAAKK,EACpCR,EAAMpE,EAAmB,EAATqE,EAAc,GAAKG,EAAKM,EAExC9E,GAAmB,EAATqE,IAaX5C,EAAAxG,UAAA6G,eAAP,SACIlE,EAAoB0D,EAAoBC,EACxC6C,EAAiBC,EAAgBrE,GAGjC,IAAK,IAAInB,EAAI,EAAGA,EAAI0C,EAAQ1C,IAC5B,CACI,IAAMwG,EAAiBzH,EAAS0D,EAAazC,GAAGhC,SAEhDuH,EAAMpE,GAAUqF,EAAeR,EAC/BT,EAAMpE,EAAS,GAAKqF,EAAeN,EAEnCX,EAAMpE,EAASqE,GAAUgB,EAAeR,EACxCT,EAAMpE,EAASqE,EAAS,GAAKgB,EAAeN,EAE5CX,EAAMpE,EAAmB,EAATqE,GAAegB,EAAeR,EAC9CT,EAAMpE,EAAmB,EAATqE,EAAc,GAAKgB,EAAeN,EAElDX,EAAMpE,EAAmB,EAATqE,GAAegB,EAAeR,EAC9CT,EAAMpE,EAAmB,EAATqE,EAAc,GAAKgB,EAAeN,EAElD/E,GAAmB,EAATqE,IAaX5C,EAAAxG,UAAA8G,eAAP,SACInE,EAAoB0D,EAAoBC,EACxC6C,EAAiBC,EAAgBrE,GAGjC,IAAK,IAAInB,EAAI,EAAGA,EAAI0C,EAAQ1C,IAC5B,CACI,IAAMyG,EAAiB1H,EAAS0D,EAAazC,GAAG/B,SAEhDsH,EAAMpE,GAAUsF,EAChBlB,EAAMpE,EAASqE,GAAUiB,EACzBlB,EAAMpE,EAAmB,EAATqE,GAAeiB,EAC/BlB,EAAMpE,EAAmB,EAATqE,GAAeiB,EAE/BtF,GAAmB,EAATqE,IAaX5C,EAAAxG,UAAA+G,UAAP,SACIpE,EAAoB0D,EAAoBC,EACxC6C,EAAiBC,EAAgBrE,GAGjC,IAAK,IAAInB,EAAI,EAAGA,EAAI0C,IAAU1C,EAC9B,CACI,IAAM0G,EAAa3H,EAAS0D,EAAazC,GAAGd,SAASyH,KAEjDD,GAEAnB,EAAMpE,GAAUuF,EAAWE,GAC3BrB,EAAMpE,EAAS,GAAKuF,EAAWG,GAE/BtB,EAAMpE,EAASqE,GAAUkB,EAAWI,GACpCvB,EAAMpE,EAASqE,EAAS,GAAKkB,EAAWK,GAExCxB,EAAMpE,EAAmB,EAATqE,GAAekB,EAAWM,GAC1CzB,EAAMpE,EAAmB,EAATqE,EAAc,GAAKkB,EAAWO,GAE9C1B,EAAMpE,EAAmB,EAATqE,GAAekB,EAAWQ,GAC1C3B,EAAMpE,EAAmB,EAATqE,EAAc,GAAKkB,EAAWS,GAE9ChG,GAAmB,EAATqE,IAKVD,EAAMpE,GAAU,EAChBoE,EAAMpE,EAAS,GAAK,EAEpBoE,EAAMpE,EAASqE,GAAU,EACzBD,EAAMpE,EAASqE,EAAS,GAAK,EAE7BD,EAAMpE,EAAmB,EAATqE,GAAe,EAC/BD,EAAMpE,EAAmB,EAATqE,EAAc,GAAK,EAEnCD,EAAMpE,EAAmB,EAATqE,GAAe,EAC/BD,EAAMpE,EAAmB,EAATqE,EAAc,GAAK,EAEnCrE,GAAmB,EAATqE,KAcf5C,EAAAxG,UAAAgH,WAAP,SACIrE,EAAoB0D,EAAoBC,EACxC6C,EAAiBC,EAAgBrE,GAGjC,IAAK,IAAInB,EAAI,EAAGA,EAAI0C,IAAU1C,EAC9B,CACI,IAAM6F,EAAS9G,EAAS0D,EAAazC,GAC/B8D,EAAgB+B,EAAO3G,SAAS1B,YAAYuG,UAAY,EACxD5F,EAAQ0H,EAAO1H,MAGfiJ,EAAOjJ,EAAQ,GAAO2F,EACtBuD,kBAAgBxB,EAAOyB,SAAUnJ,GAAS0H,EAAOyB,UAAoB,IAARnJ,GAAe,IAElFoH,EAAMpE,GAAUiG,EAChB7B,EAAMpE,EAASqE,GAAU4B,EACzB7B,EAAMpE,EAAmB,EAATqE,GAAe4B,EAC/B7B,EAAMpE,EAAmB,EAATqE,GAAe4B,EAE/BjG,GAAmB,EAATqE,IAKX5C,EAAAxG,UAAA6D,QAAP,WAEI1D,EAAMH,UAAA6D,mBAEF/D,KAAK2G,SAEL3G,KAAK2G,OAAO5C,UACZ/D,KAAK2G,OAAS,MAGlB3G,KAAK4G,WAAa,MAlafF,EAAA2E,UAA+B,CAClCC,KAAM,WACNxG,KAAMyG,EAAaA,cAACC,gBAka3B9E,EAvaD,CAAsC+E"}