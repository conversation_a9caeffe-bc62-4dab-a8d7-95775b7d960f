/*!
 * @pixi/accessibility - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/accessibility is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/display"),t=require("@pixi/utils"),i=require("@pixi/core"),s={accessible:!1,accessibleTitle:null,accessibleHint:null,tabIndex:0,_accessibleActive:!1,_accessibleDiv:null,accessibleType:"button",accessiblePointerEvents:"auto",accessibleChildren:!0,renderId:-1};e.DisplayObject.mixin(s);var n=function(){function e(e){this.debug=!1,this._isActive=!1,this._isMobileAccessibility=!1,this.pool=[],this.renderId=0,this.children=[],this.androidUpdateCount=0,this.androidUpdateFrequency=500,this._hookDiv=null,(t.isMobile.tablet||t.isMobile.phone)&&this.createTouchHook();var i=document.createElement("div");i.style.width="100px",i.style.height="100px",i.style.position="absolute",i.style.top="0px",i.style.left="0px",i.style.zIndex=2..toString(),this.div=i,this.renderer=e,this._onKeyDown=this._onKeyDown.bind(this),this._onMouseMove=this._onMouseMove.bind(this),globalThis.addEventListener("keydown",this._onKeyDown,!1)}return Object.defineProperty(e.prototype,"isActive",{get:function(){return this._isActive},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isMobileAccessibility",{get:function(){return this._isMobileAccessibility},enumerable:!1,configurable:!0}),e.prototype.createTouchHook=function(){var e=this,t=document.createElement("button");t.style.width="1px",t.style.height="1px",t.style.position="absolute",t.style.top="-1000px",t.style.left="-1000px",t.style.zIndex=2..toString(),t.style.backgroundColor="#FF0000",t.title="select to enable accessibility for this content",t.addEventListener("focus",(function(){e._isMobileAccessibility=!0,e.activate(),e.destroyTouchHook()})),document.body.appendChild(t),this._hookDiv=t},e.prototype.destroyTouchHook=function(){this._hookDiv&&(document.body.removeChild(this._hookDiv),this._hookDiv=null)},e.prototype.activate=function(){var e;this._isActive||(this._isActive=!0,globalThis.document.addEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown,!1),this.renderer.on("postrender",this.update,this),null===(e=this.renderer.view.parentNode)||void 0===e||e.appendChild(this.div))},e.prototype.deactivate=function(){var e;this._isActive&&!this._isMobileAccessibility&&(this._isActive=!1,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.addEventListener("keydown",this._onKeyDown,!1),this.renderer.off("postrender",this.update),null===(e=this.div.parentNode)||void 0===e||e.removeChild(this.div))},e.prototype.updateAccessibleObjects=function(e){if(e.visible&&e.accessibleChildren){e.accessible&&e.interactive&&(e._accessibleActive||this.addChild(e),e.renderId=this.renderId);var t=e.children;if(t)for(var i=0;i<t.length;i++)this.updateAccessibleObjects(t[i])}},e.prototype.update=function(){var e=performance.now();if(!(t.isMobile.android.device&&e<this.androidUpdateCount)&&(this.androidUpdateCount=e+this.androidUpdateFrequency,this.renderer.renderingToScreen)){this.renderer._lastObjectRendered&&this.updateAccessibleObjects(this.renderer._lastObjectRendered);var i=this.renderer.view.getBoundingClientRect(),s=i.left,n=i.top,o=i.width,r=i.height,l=this.renderer,a=l.width,c=l.height,d=l.resolution,h=o/a*d,p=r/c*d,u=this.div;u.style.left=s+"px",u.style.top=n+"px",u.style.width=a+"px",u.style.height=c+"px";for(var b=0;b<this.children.length;b++){var v=this.children[b];if(v.renderId!==this.renderId)v._accessibleActive=!1,t.removeItems(this.children,b,1),this.div.removeChild(v._accessibleDiv),this.pool.push(v._accessibleDiv),v._accessibleDiv=null,b--;else{u=v._accessibleDiv;var y=v.hitArea,g=v.worldTransform;v.hitArea?(u.style.left=(g.tx+y.x*g.a)*h+"px",u.style.top=(g.ty+y.y*g.d)*p+"px",u.style.width=y.width*g.a*h+"px",u.style.height=y.height*g.d*p+"px"):(y=v.getBounds(),this.capHitArea(y),u.style.left=y.x*h+"px",u.style.top=y.y*p+"px",u.style.width=y.width*h+"px",u.style.height=y.height*p+"px",u.title!==v.accessibleTitle&&null!==v.accessibleTitle&&(u.title=v.accessibleTitle),u.getAttribute("aria-label")!==v.accessibleHint&&null!==v.accessibleHint&&u.setAttribute("aria-label",v.accessibleHint)),v.accessibleTitle===u.title&&v.tabIndex===u.tabIndex||(u.title=v.accessibleTitle,u.tabIndex=v.tabIndex,this.debug&&this.updateDebugHTML(u))}}this.renderId++}},e.prototype.updateDebugHTML=function(e){e.innerHTML="type: "+e.type+"</br> title : "+e.title+"</br> tabIndex: "+e.tabIndex},e.prototype.capHitArea=function(e){e.x<0&&(e.width+=e.x,e.x=0),e.y<0&&(e.height+=e.y,e.y=0);var t=this.renderer,i=t.width,s=t.height;e.x+e.width>i&&(e.width=i-e.x),e.y+e.height>s&&(e.height=s-e.y)},e.prototype.addChild=function(e){var t=this.pool.pop();t||((t=document.createElement("button")).style.width="100px",t.style.height="100px",t.style.backgroundColor=this.debug?"rgba(255,255,255,0.5)":"transparent",t.style.position="absolute",t.style.zIndex=2..toString(),t.style.borderStyle="none",navigator.userAgent.toLowerCase().indexOf("chrome")>-1?t.setAttribute("aria-live","off"):t.setAttribute("aria-live","polite"),navigator.userAgent.match(/rv:.*Gecko\//)?t.setAttribute("aria-relevant","additions"):t.setAttribute("aria-relevant","text"),t.addEventListener("click",this._onClick.bind(this)),t.addEventListener("focus",this._onFocus.bind(this)),t.addEventListener("focusout",this._onFocusOut.bind(this))),t.style.pointerEvents=e.accessiblePointerEvents,t.type=e.accessibleType,e.accessibleTitle&&null!==e.accessibleTitle?t.title=e.accessibleTitle:e.accessibleHint&&null!==e.accessibleHint||(t.title="displayObject "+e.tabIndex),e.accessibleHint&&null!==e.accessibleHint&&t.setAttribute("aria-label",e.accessibleHint),this.debug&&this.updateDebugHTML(t),e._accessibleActive=!0,e._accessibleDiv=t,t.displayObject=e,this.children.push(e),this.div.appendChild(e._accessibleDiv),e._accessibleDiv.tabIndex=e.tabIndex},e.prototype._onClick=function(e){var t=this.renderer.plugins.interaction,i=e.target.displayObject,s=t.eventData;t.dispatchEvent(i,"click",s),t.dispatchEvent(i,"pointertap",s),t.dispatchEvent(i,"tap",s)},e.prototype._onFocus=function(e){e.target.getAttribute("aria-live")||e.target.setAttribute("aria-live","assertive");var t=this.renderer.plugins.interaction,i=e.target.displayObject,s=t.eventData;t.dispatchEvent(i,"mouseover",s)},e.prototype._onFocusOut=function(e){e.target.getAttribute("aria-live")||e.target.setAttribute("aria-live","polite");var t=this.renderer.plugins.interaction,i=e.target.displayObject,s=t.eventData;t.dispatchEvent(i,"mouseout",s)},e.prototype._onKeyDown=function(e){9===e.keyCode&&this.activate()},e.prototype._onMouseMove=function(e){0===e.movementX&&0===e.movementY||this.deactivate()},e.prototype.destroy=function(){this.destroyTouchHook(),this.div=null,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown),this.pool=null,this.children=null,this.renderer=null},e.extension={name:"accessibility",type:[i.ExtensionType.RendererPlugin,i.ExtensionType.CanvasRendererPlugin]},e}();exports.AccessibilityManager=n,exports.accessibleTarget=s;
//# sourceMappingURL=accessibility.min.js.map
