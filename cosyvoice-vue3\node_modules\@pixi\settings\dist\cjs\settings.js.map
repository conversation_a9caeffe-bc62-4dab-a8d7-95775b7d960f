{"version": 3, "file": "settings.js", "sources": ["../../src/adapter.ts", "../../../../node_modules/ismobilejs/esm/isMobile.js", "../../src/utils/isMobile.ts", "../../src/utils/canUploadSameBuffer.ts", "../../src/utils/maxRecommendedTextures.ts", "../../src/settings.ts"], "sourcesContent": ["export type ContextIds = '2d' | 'webgl' | 'experimental-webgl' | 'webgl2';\n\n/**\n * This interface describes all the DOM dependent calls that Pixi makes throughout its codebase\n * Implementations of this interface can be used to make sure <PERSON><PERSON> will work in any environment\n * such as browser, web workers, and node\n */\nexport interface IAdapter\n{\n    /** Returns a canvas object that can be used to create a webgl context. */\n    createCanvas: (width?: number, height?: number) => HTMLCanvasElement;\n    /** Returns a webgl rendering context. */\n    getWebGLRenderingContext: () => typeof WebGLRenderingContext;\n    /** Returns a partial implementation of the browsers window.navigator */\n    getNavigator: () => { userAgent: string };\n    /** Returns the current base URL For browser environments this is either the document.baseURI or window.location.href */\n    getBaseUrl: () => string;\n    fetch: (url: RequestInfo, options?: RequestInit) => Promise<Response>;\n}\n\nexport const BrowserAdapter = {\n    /**\n     * Creates a canvas element of the given size.\n     * This canvas is created using the browser's native canvas element.\n     * @param width - width of the canvas\n     * @param height - height of the canvas\n     */\n    createCanvas: (width: number, height: number): HTMLCanvasElement =>\n    {\n        const canvas = document.createElement('canvas');\n\n        canvas.width = width;\n        canvas.height = height;\n\n        return canvas;\n    },\n    getWebGLRenderingContext: () => WebGLRenderingContext,\n    getNavigator: () => navigator,\n    getBaseUrl: () => (document.baseURI ?? window.location.href),\n    fetch: (url: RequestInfo, options?: RequestInit) => fetch(url, options),\n} as IAdapter;\n", "var appleIphone = /iPhone/i;\nvar appleIpod = /iPod/i;\nvar appleTablet = /iPad/i;\nvar appleUniversal = /\\biOS-universal(?:.+)Mac\\b/i;\nvar androidPhone = /\\bAndroid(?:.+)Mobile\\b/i;\nvar androidTablet = /Android/i;\nvar amazonPhone = /(?:SD4930UR|\\bSilk(?:.+)Mobile\\b)/i;\nvar amazonTablet = /Silk/i;\nvar windowsPhone = /Windows Phone/i;\nvar windowsTablet = /\\bWindows(?:.+)ARM\\b/i;\nvar otherBlackBerry = /BlackBerry/i;\nvar otherBlackBerry10 = /BB10/i;\nvar otherOpera = /Opera Mini/i;\nvar otherChrome = /\\b(CriOS|Chrome)(?:.+)Mobile/i;\nvar otherFirefox = /Mobile(?:.+)Firefox\\b/i;\nvar isAppleTabletOnIos13 = function (navigator) {\n    return (typeof navigator !== 'undefined' &&\n        navigator.platform === 'MacIntel' &&\n        typeof navigator.maxTouchPoints === 'number' &&\n        navigator.maxTouchPoints > 1 &&\n        typeof MSStream === 'undefined');\n};\nfunction createMatch(userAgent) {\n    return function (regex) { return regex.test(userAgent); };\n}\nexport default function isMobile(param) {\n    var nav = {\n        userAgent: '',\n        platform: '',\n        maxTouchPoints: 0\n    };\n    if (!param && typeof navigator !== 'undefined') {\n        nav = {\n            userAgent: navigator.userAgent,\n            platform: navigator.platform,\n            maxTouchPoints: navigator.maxTouchPoints || 0\n        };\n    }\n    else if (typeof param === 'string') {\n        nav.userAgent = param;\n    }\n    else if (param && param.userAgent) {\n        nav = {\n            userAgent: param.userAgent,\n            platform: param.platform,\n            maxTouchPoints: param.maxTouchPoints || 0\n        };\n    }\n    var userAgent = nav.userAgent;\n    var tmp = userAgent.split('[FBAN');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    tmp = userAgent.split('Twitter');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    var match = createMatch(userAgent);\n    var result = {\n        apple: {\n            phone: match(appleIphone) && !match(windowsPhone),\n            ipod: match(appleIpod),\n            tablet: !match(appleIphone) &&\n                (match(appleTablet) || isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone),\n            universal: match(appleUniversal),\n            device: (match(appleIphone) ||\n                match(appleIpod) ||\n                match(appleTablet) ||\n                match(appleUniversal) ||\n                isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone)\n        },\n        amazon: {\n            phone: match(amazonPhone),\n            tablet: !match(amazonPhone) && match(amazonTablet),\n            device: match(amazonPhone) || match(amazonTablet)\n        },\n        android: {\n            phone: (!match(windowsPhone) && match(amazonPhone)) ||\n                (!match(windowsPhone) && match(androidPhone)),\n            tablet: !match(windowsPhone) &&\n                !match(amazonPhone) &&\n                !match(androidPhone) &&\n                (match(amazonTablet) || match(androidTablet)),\n            device: (!match(windowsPhone) &&\n                (match(amazonPhone) ||\n                    match(amazonTablet) ||\n                    match(androidPhone) ||\n                    match(androidTablet))) ||\n                match(/\\bokhttp\\b/i)\n        },\n        windows: {\n            phone: match(windowsPhone),\n            tablet: match(windowsTablet),\n            device: match(windowsPhone) || match(windowsTablet)\n        },\n        other: {\n            blackberry: match(otherBlackBerry),\n            blackberry10: match(otherBlackBerry10),\n            opera: match(otherOpera),\n            firefox: match(otherFirefox),\n            chrome: match(otherChrome),\n            device: match(otherBlackBerry) ||\n                match(otherBlackBerry10) ||\n                match(otherOpera) ||\n                match(otherFirefox) ||\n                match(otherChrome)\n        },\n        any: false,\n        phone: false,\n        tablet: false\n    };\n    result.any =\n        result.apple.device ||\n            result.android.device ||\n            result.windows.device ||\n            result.other.device;\n    result.phone =\n        result.apple.phone || result.android.phone || result.windows.phone;\n    result.tablet =\n        result.apple.tablet || result.android.tablet || result.windows.tablet;\n    return result;\n}\n//# sourceMappingURL=isMobile.js.map", "import isMobileCall from 'ismobilejs';\n\ntype isMobileResult = {\n    apple: {\n        phone: boolean;\n        ipod: boolean;\n        tablet: boolean;\n        universal: boolean;\n        device: boolean;\n    };\n    amazon: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    android: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    windows: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    other: {\n        blackberry: boolean;\n        blackberry10: boolean;\n        opera: boolean;\n        firefox: boolean;\n        chrome: boolean;\n        device: boolean;\n    };\n    phone: boolean;\n    tablet: boolean;\n    any: boolean;\n};\n\nexport const isMobile: isMobileResult = isMobileCall(globalThis.navigator);\n", "import { isMobile } from './isMobile';\n\n/**\n * Uploading the same buffer multiple times in a single frame can cause performance issues.\n * Apparent on iOS so only check for that at the moment\n * This check may become more complex if this issue pops up elsewhere.\n * @private\n * @returns {boolean} `true` if the same buffer may be uploaded more than once.\n */\nexport function canUploadSameBuffer(): boolean\n{\n    return !isMobile.apple.device;\n}\n", "import { isMobile } from './isMobile';\n\n/**\n * The maximum recommended texture units to use.\n * In theory the bigger the better, and for desktop we'll use as many as we can.\n * But some mobile devices slow down if there is to many branches in the shader.\n * So in practice there seems to be a sweet spot size that varies depending on the device.\n *\n * In v4, all mobile devices were limited to 4 texture units because for this.\n * In v5, we allow all texture units to be used on modern Apple or Android devices.\n * @private\n * @param {number} max\n * @returns {number} The maximum recommended texture units to use.\n */\nexport function maxRecommendedTextures(max: number): number\n{\n    let allowMax = true;\n\n    if (isMobile.tablet || isMobile.phone)\n    {\n        if (isMobile.apple.device)\n        {\n            const match = (navigator.userAgent).match(/OS (\\d+)_(\\d+)?/);\n\n            if (match)\n            {\n                const majorVersion = parseInt(match[1], 10);\n\n                // Limit texture units on devices below iOS 11, which will be older hardware\n                if (majorVersion < 11)\n                {\n                    allowMax = false;\n                }\n            }\n        }\n        if (isMobile.android.device)\n        {\n            const match = (navigator.userAgent).match(/Android\\s([0-9.]*)/);\n\n            if (match)\n            {\n                const majorVersion = parseInt(match[1], 10);\n\n                // Limit texture units on devices below Android 7 (Nougat), which will be older hardware\n                if (majorVersion < 7)\n                {\n                    allowMax = false;\n                }\n            }\n        }\n    }\n\n    return allowMax ? max : 4;\n}\n", "import type { ENV } from '@pixi/constants';\nimport { GC_MODES, MIPMAP_MODES, MSAA_QUALITY, PRECISION, SCALE_MODES, WRAP_MODES } from '@pixi/constants';\nimport type { IAdapter } from './adapter';\nimport { BrowserAdapter } from './adapter';\nimport { canUploadSameBuffer } from './utils/canUploadSameBuffer';\nimport { isMobile } from './utils/isMobile';\nimport { maxRecommendedTextures } from './utils/maxRecommendedTextures';\n\nexport interface IRenderOptions\n{\n    view: HTMLCanvasElement;\n    width: number;\n    height: number;\n    autoDensity: boolean;\n    backgroundColor: number;\n    backgroundAlpha: number;\n    useContextAlpha: boolean | 'notMultiplied';\n    clearBeforeRender: boolean;\n    antialias: boolean;\n    preserveDrawingBuffer: boolean;\n}\n\nexport interface ISettings\n{\n    ADAPTER: IAdapter;\n    MIPMAP_TEXTURES: MIPMAP_MODES;\n    ANISOTROPIC_LEVEL: number;\n    RESOLUTION: number;\n    FILTER_RESOLUTION: number;\n    FILTER_MULTISAMPLE: MSAA_QUALITY;\n    SPRITE_MAX_TEXTURES: number;\n    SPRITE_BATCH_SIZE: number;\n    RENDER_OPTIONS: IRenderOptions;\n    GC_MODE: GC_MODES;\n    GC_MAX_IDLE: number;\n    GC_MAX_CHECK_COUNT: number;\n    WRAP_MODE: WRAP_MODES;\n    SCALE_MODE: SCALE_MODES;\n    PRECISION_VERTEX: PRECISION;\n    PRECISION_FRAGMENT: PRECISION;\n    CAN_UPLOAD_SAME_BUFFER: boolean;\n    CREATE_IMAGE_BITMAP: boolean;\n    ROUND_PIXELS: boolean;\n    RETINA_PREFIX?: RegExp;\n    FAIL_IF_MAJOR_PERFORMANCE_CAVEAT?: boolean;\n    UPLOADS_PER_FRAME?: number;\n    SORTABLE_CHILDREN?: boolean;\n    PREFER_ENV?: ENV;\n    STRICT_TEXTURE_CACHE?: boolean;\n    MESH_CANVAS_PADDING?: number;\n    TARGET_FPMS?: number;\n}\n\n/**\n * User's customizable globals for overriding the default PIXI settings, such\n * as a renderer's default resolution, framerate, float precision, etc.\n * @example\n * // Use the native window resolution as the default resolution\n * // will support high-density displays when rendering\n * PIXI.settings.RESOLUTION = window.devicePixelRatio;\n *\n * // Disable interpolation when scaling, will make texture be pixelated\n * PIXI.settings.SCALE_MODE = PIXI.SCALE_MODES.NEAREST;\n * @namespace PIXI.settings\n */\nexport const settings: ISettings = {\n\n    /**\n     * This adapter is used to call methods that are platform dependent.\n     * For example `document.createElement` only runs on the web but fails in node environments.\n     * This allows us to support more platforms by abstracting away specific implementations per platform.\n     *\n     * By default the adapter is set to work in the browser. However you can create your own\n     * by implementing the `IAdapter` interface. See `IAdapter` for more information.\n     * @name ADAPTER\n     * @memberof PIXI.settings\n     * @type {PIXI.IAdapter}\n     * @default PIXI.BrowserAdapter\n     */\n    ADAPTER: BrowserAdapter,\n    /**\n     * If set to true WebGL will attempt make textures mimpaped by default.\n     * Mipmapping will only succeed if the base texture uploaded has power of two dimensions.\n     * @static\n     * @name MIPMAP_TEXTURES\n     * @memberof PIXI.settings\n     * @type {PIXI.MIPMAP_MODES}\n     * @default PIXI.MIPMAP_MODES.POW2\n     */\n    MIPMAP_TEXTURES: MIPMAP_MODES.POW2,\n\n    /**\n     * Default anisotropic filtering level of textures.\n     * Usually from 0 to 16\n     * @static\n     * @name ANISOTROPIC_LEVEL\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 0\n     */\n    ANISOTROPIC_LEVEL: 0,\n\n    /**\n     * Default resolution / device pixel ratio of the renderer.\n     * @static\n     * @name RESOLUTION\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 1\n     */\n    RESOLUTION: 1,\n\n    /**\n     * Default filter resolution.\n     * @static\n     * @name FILTER_RESOLUTION\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 1\n     */\n    FILTER_RESOLUTION: 1,\n\n    /**\n     * Default filter samples.\n     * @static\n     * @name FILTER_MULTISAMPLE\n     * @memberof PIXI.settings\n     * @type {PIXI.MSAA_QUALITY}\n     * @default PIXI.MSAA_QUALITY.NONE\n     */\n    FILTER_MULTISAMPLE: MSAA_QUALITY.NONE,\n\n    /**\n     * The maximum textures that this device supports.\n     * @static\n     * @name SPRITE_MAX_TEXTURES\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 32\n     */\n    SPRITE_MAX_TEXTURES: maxRecommendedTextures(32),\n\n    // TODO: maybe change to SPRITE.BATCH_SIZE: 2000\n    // TODO: maybe add PARTICLE.BATCH_SIZE: 15000\n\n    /**\n     * The default sprite batch size.\n     *\n     * The default aims to balance desktop and mobile devices.\n     * @static\n     * @name SPRITE_BATCH_SIZE\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 4096\n     */\n    SPRITE_BATCH_SIZE: 4096,\n\n    /**\n     * The default render options if none are supplied to {@link PIXI.Renderer}\n     * or {@link PIXI.CanvasRenderer}.\n     * @static\n     * @name RENDER_OPTIONS\n     * @memberof PIXI.settings\n     * @type {object}\n     * @property {boolean} [antialias=false] - {@link PIXI.IRendererOptions.antialias}\n     * @property {boolean} [autoDensity=false] - {@link PIXI.IRendererOptions.autoDensity}\n     * @property {number} [backgroundAlpha=1] - {@link PIXI.IRendererOptions.backgroundAlpha}\n     * @property {number} [backgroundColor=0x000000] - {@link PIXI.IRendererOptions.backgroundColor}\n     * @property {boolean} [clearBeforeRender=true] - {@link PIXI.IRendererOptions.clearBeforeRender}\n     * @property {number} [height=600] - {@link PIXI.IRendererOptions.height}\n     * @property {boolean} [preserveDrawingBuffer=false] - {@link PIXI.IRendererOptions.preserveDrawingBuffer}\n     * @property {boolean|'notMultiplied'} [useContextAlpha=true] - {@link PIXI.IRendererOptions.useContextAlpha}\n     * @property {HTMLCanvasElement} [view=null] - {@link PIXI.IRendererOptions.view}\n     * @property {number} [width=800] - {@link PIXI.IRendererOptions.width}\n     */\n    RENDER_OPTIONS: {\n        view: null,\n        width: 800,\n        height: 600,\n        autoDensity: false,\n        backgroundColor: 0x000000,\n        backgroundAlpha: 1,\n        useContextAlpha: true,\n        clearBeforeRender: true,\n        antialias: false,\n        preserveDrawingBuffer: false,\n    },\n\n    /**\n     * Default Garbage Collection mode.\n     * @static\n     * @name GC_MODE\n     * @memberof PIXI.settings\n     * @type {PIXI.GC_MODES}\n     * @default PIXI.GC_MODES.AUTO\n     */\n    GC_MODE: GC_MODES.AUTO,\n\n    /**\n     * Default Garbage Collection max idle.\n     * @static\n     * @name GC_MAX_IDLE\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 3600\n     */\n    GC_MAX_IDLE: 60 * 60,\n\n    /**\n     * Default Garbage Collection maximum check count.\n     * @static\n     * @name GC_MAX_CHECK_COUNT\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 600\n     */\n    GC_MAX_CHECK_COUNT: 60 * 10,\n\n    /**\n     * Default wrap modes that are supported by pixi.\n     * @static\n     * @name WRAP_MODE\n     * @memberof PIXI.settings\n     * @type {PIXI.WRAP_MODES}\n     * @default PIXI.WRAP_MODES.CLAMP\n     */\n    WRAP_MODE: WRAP_MODES.CLAMP,\n\n    /**\n     * Default scale mode for textures.\n     * @static\n     * @name SCALE_MODE\n     * @memberof PIXI.settings\n     * @type {PIXI.SCALE_MODES}\n     * @default PIXI.SCALE_MODES.LINEAR\n     */\n    SCALE_MODE: SCALE_MODES.LINEAR,\n\n    /**\n     * Default specify float precision in vertex shader.\n     * @static\n     * @name PRECISION_VERTEX\n     * @memberof PIXI.settings\n     * @type {PIXI.PRECISION}\n     * @default PIXI.PRECISION.HIGH\n     */\n    PRECISION_VERTEX: PRECISION.HIGH,\n\n    /**\n     * Default specify float precision in fragment shader.\n     * iOS is best set at highp due to https://github.com/pixijs/pixi.js/issues/3742\n     * @static\n     * @name PRECISION_FRAGMENT\n     * @memberof PIXI.settings\n     * @type {PIXI.PRECISION}\n     * @default PIXI.PRECISION.MEDIUM\n     */\n    PRECISION_FRAGMENT: isMobile.apple.device ? PRECISION.HIGH : PRECISION.MEDIUM,\n\n    /**\n     * Can we upload the same buffer in a single frame?\n     * @static\n     * @name CAN_UPLOAD_SAME_BUFFER\n     * @memberof PIXI.settings\n     * @type {boolean}\n     */\n    CAN_UPLOAD_SAME_BUFFER: canUploadSameBuffer(),\n\n    /**\n     * Enables bitmap creation before image load. This feature is experimental.\n     * @static\n     * @name CREATE_IMAGE_BITMAP\n     * @memberof PIXI.settings\n     * @type {boolean}\n     * @default false\n     */\n    CREATE_IMAGE_BITMAP: false,\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * @static\n     * @constant\n     * @memberof PIXI.settings\n     * @type {boolean}\n     * @default false\n     */\n    ROUND_PIXELS: false,\n};\n"], "names": ["isMobile", "isMobileCall", "MIPMAP_MODES", "MSAA_QUALITY", "GC_MODES", "WRAP_MODES", "SCALE_MODES", "PRECISION"], "mappings": ";;;;;;;;;;;;;AAoBO,IAAM,cAAc,GAAG;AAC1B;;;;;AAKG;AACH,IAAA,YAAY,EAAE,UAAC,KAAa,EAAE,MAAc,EAAA;QAExC,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAEhD,QAAA,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,QAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AAEvB,QAAA,OAAO,MAAM,CAAC;KACjB;AACD,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,qBAAqB,GAAA;AACrD,IAAA,YAAY,EAAE,YAAA,EAAM,OAAA,SAAS,GAAA;AAC7B,IAAA,UAAU,EAAE,YAAM,EAAA,IAAA,EAAA,CAAA,CAAA,QAAC,CAAA,EAAA,GAAA,QAAQ,CAAC,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAC,EAAA;AAC5D,IAAA,KAAK,EAAE,UAAC,GAAgB,EAAE,OAAqB,EAAK,EAAA,OAAA,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,GAAA;;;ACvC3E,IAAI,WAAW,GAAG,SAAS,CAAC;AAC5B,IAAI,SAAS,GAAG,OAAO,CAAC;AACxB,IAAI,WAAW,GAAG,OAAO,CAAC;AAC1B,IAAI,cAAc,GAAG,6BAA6B,CAAC;AACnD,IAAI,YAAY,GAAG,0BAA0B,CAAC;AAC9C,IAAI,aAAa,GAAG,UAAU,CAAC;AAC/B,IAAI,WAAW,GAAG,oCAAoC,CAAC;AACvD,IAAI,YAAY,GAAG,OAAO,CAAC;AAC3B,IAAI,YAAY,GAAG,gBAAgB,CAAC;AACpC,IAAI,aAAa,GAAG,uBAAuB,CAAC;AAC5C,IAAI,eAAe,GAAG,aAAa,CAAC;AACpC,IAAI,iBAAiB,GAAG,OAAO,CAAC;AAChC,IAAI,UAAU,GAAG,aAAa,CAAC;AAC/B,IAAI,WAAW,GAAG,+BAA+B,CAAC;AAClD,IAAI,YAAY,GAAG,wBAAwB,CAAC;AAC5C,IAAI,oBAAoB,GAAG,UAAU,SAAS,EAAE;AAChD,IAAI,QAAQ,OAAO,SAAS,KAAK,WAAW;AAC5C,QAAQ,SAAS,CAAC,QAAQ,KAAK,UAAU;AACzC,QAAQ,OAAO,SAAS,CAAC,cAAc,KAAK,QAAQ;AACpD,QAAQ,SAAS,CAAC,cAAc,GAAG,CAAC;AACpC,QAAQ,OAAO,QAAQ,KAAK,WAAW,EAAE;AACzC,CAAC,CAAC;AACF,SAAS,WAAW,CAAC,SAAS,EAAE;AAChC,IAAI,OAAO,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;AAC9D,CAAC;AACc,SAASA,UAAQ,CAAC,KAAK,EAAE;AACxC,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,SAAS,EAAE,EAAE;AACrB,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,cAAc,EAAE,CAAC;AACzB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AACpD,QAAQ,GAAG,GAAG;AACd,YAAY,SAAS,EAAE,SAAS,CAAC,SAAS;AAC1C,YAAY,QAAQ,EAAE,SAAS,CAAC,QAAQ;AACxC,YAAY,cAAc,EAAE,SAAS,CAAC,cAAc,IAAI,CAAC;AACzD,SAAS,CAAC;AACV,KAAK;AACL,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACxC,QAAQ,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;AAC9B,KAAK;AACL,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE;AACvC,QAAQ,GAAG,GAAG;AACd,YAAY,SAAS,EAAE,KAAK,CAAC,SAAS;AACtC,YAAY,QAAQ,EAAE,KAAK,CAAC,QAAQ;AACpC,YAAY,cAAc,EAAE,KAAK,CAAC,cAAc,IAAI,CAAC;AACrD,SAAS,CAAC;AACV,KAAK;AACL,IAAI,IAAI,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AAClC,IAAI,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AACvC,QAAQ,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACrC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AACvC,QAAQ,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;AACvC,IAAI,IAAI,MAAM,GAAG;AACjB,QAAQ,KAAK,EAAE;AACf,YAAY,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AAC7D,YAAY,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC;AAClC,YAAY,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC;AACvC,iBAAiB,KAAK,CAAC,WAAW,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;AACjE,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC;AACpC,YAAY,SAAS,EAAE,KAAK,CAAC,cAAc,CAAC;AAC5C,YAAY,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC;AACvC,gBAAgB,KAAK,CAAC,SAAS,CAAC;AAChC,gBAAgB,KAAK,CAAC,WAAW,CAAC;AAClC,gBAAgB,KAAK,CAAC,cAAc,CAAC;AACrC,gBAAgB,oBAAoB,CAAC,GAAG,CAAC;AACzC,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC;AACpC,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,YAAY,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC;AACrC,YAAY,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC;AAC9D,YAAY,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC;AAC7D,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC;AAC9D,iBAAiB,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7D,YAAY,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC;AACxC,gBAAgB,CAAC,KAAK,CAAC,WAAW,CAAC;AACnC,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC;AACpC,iBAAiB,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;AAC7D,YAAY,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC;AACzC,iBAAiB,KAAK,CAAC,WAAW,CAAC;AACnC,oBAAoB,KAAK,CAAC,YAAY,CAAC;AACvC,oBAAoB,KAAK,CAAC,YAAY,CAAC;AACvC,oBAAoB,KAAK,CAAC,aAAa,CAAC,CAAC;AACzC,gBAAgB,KAAK,CAAC,aAAa,CAAC;AACpC,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC;AACtC,YAAY,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC;AACxC,YAAY,MAAM,EAAE,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC;AAC/D,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,YAAY,UAAU,EAAE,KAAK,CAAC,eAAe,CAAC;AAC9C,YAAY,YAAY,EAAE,KAAK,CAAC,iBAAiB,CAAC;AAClD,YAAY,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC;AACpC,YAAY,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC;AACxC,YAAY,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC;AACtC,YAAY,MAAM,EAAE,KAAK,CAAC,eAAe,CAAC;AAC1C,gBAAgB,KAAK,CAAC,iBAAiB,CAAC;AACxC,gBAAgB,KAAK,CAAC,UAAU,CAAC;AACjC,gBAAgB,KAAK,CAAC,YAAY,CAAC;AACnC,gBAAgB,KAAK,CAAC,WAAW,CAAC;AAClC,SAAS;AACT,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,MAAM,EAAE,KAAK;AACrB,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,GAAG;AACd,QAAQ,MAAM,CAAC,KAAK,CAAC,MAAM;AAC3B,YAAY,MAAM,CAAC,OAAO,CAAC,MAAM;AACjC,YAAY,MAAM,CAAC,OAAO,CAAC,MAAM;AACjC,YAAY,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,CAAC,KAAK;AAChB,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC3E,IAAI,MAAM,CAAC,MAAM;AACjB,QAAQ,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AAC9E,IAAI,OAAO,MAAM,CAAC;AAClB;;ACrFO,IAAM,QAAQ,GAAmBC,UAAY,CAAC,UAAU,CAAC,SAAS;;ACpCzE;;;;;;AAMG;SACa,mBAAmB,GAAA;AAE/B,IAAA,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;AAClC;;ACVA;;;;;;;;;;;AAWG;AACG,SAAU,sBAAsB,CAAC,GAAW,EAAA;IAE9C,IAAI,QAAQ,GAAG,IAAI,CAAC;AAEpB,IAAA,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,KAAK,EACrC;AACI,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EACzB;AACI,YAAA,IAAM,KAAK,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAE7D,YAAA,IAAI,KAAK,EACT;gBACI,IAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;gBAG5C,IAAI,YAAY,GAAG,EAAE,EACrB;oBACI,QAAQ,GAAG,KAAK,CAAC;AACpB,iBAAA;AACJ,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,EAC3B;AACI,YAAA,IAAM,KAAK,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAEhE,YAAA,IAAI,KAAK,EACT;gBACI,IAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;gBAG5C,IAAI,YAAY,GAAG,CAAC,EACpB;oBACI,QAAQ,GAAG,KAAK,CAAC;AACpB,iBAAA;AACJ,aAAA;AACJ,SAAA;AACJ,KAAA;IAED,OAAO,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;AAC9B;;ACAA;;;;;;;;;;;AAWG;AACI,IAAM,QAAQ,GAAc;AAE/B;;;;;;;;;;;AAWG;AACH,IAAA,OAAO,EAAE,cAAc;AACvB;;;;;;;;AAQG;IACH,eAAe,EAAEC,sBAAY,CAAC,IAAI;AAElC;;;;;;;;AAQG;AACH,IAAA,iBAAiB,EAAE,CAAC;AAEpB;;;;;;;AAOG;AACH,IAAA,UAAU,EAAE,CAAC;AAEb;;;;;;;AAOG;AACH,IAAA,iBAAiB,EAAE,CAAC;AAEpB;;;;;;;AAOG;IACH,kBAAkB,EAAEC,sBAAY,CAAC,IAAI;AAErC;;;;;;;AAOG;AACH,IAAA,mBAAmB,EAAE,sBAAsB,CAAC,EAAE,CAAC;;;AAK/C;;;;;;;;;AASG;AACH,IAAA,iBAAiB,EAAE,IAAI;AAEvB;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,cAAc,EAAE;AACZ,QAAA,IAAI,EAAE,IAAI;AACV,QAAA,KAAK,EAAE,GAAG;AACV,QAAA,MAAM,EAAE,GAAG;AACX,QAAA,WAAW,EAAE,KAAK;AAClB,QAAA,eAAe,EAAE,QAAQ;AACzB,QAAA,eAAe,EAAE,CAAC;AAClB,QAAA,eAAe,EAAE,IAAI;AACrB,QAAA,iBAAiB,EAAE,IAAI;AACvB,QAAA,SAAS,EAAE,KAAK;AAChB,QAAA,qBAAqB,EAAE,KAAK;AAC/B,KAAA;AAED;;;;;;;AAOG;IACH,OAAO,EAAEC,kBAAQ,CAAC,IAAI;AAEtB;;;;;;;AAOG;IACH,WAAW,EAAE,EAAE,GAAG,EAAE;AAEpB;;;;;;;AAOG;IACH,kBAAkB,EAAE,EAAE,GAAG,EAAE;AAE3B;;;;;;;AAOG;IACH,SAAS,EAAEC,oBAAU,CAAC,KAAK;AAE3B;;;;;;;AAOG;IACH,UAAU,EAAEC,qBAAW,CAAC,MAAM;AAE9B;;;;;;;AAOG;IACH,gBAAgB,EAAEC,mBAAS,CAAC,IAAI;AAEhC;;;;;;;;AAQG;AACH,IAAA,kBAAkB,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAGA,mBAAS,CAAC,IAAI,GAAGA,mBAAS,CAAC,MAAM;AAE7E;;;;;;AAMG;IACH,sBAAsB,EAAE,mBAAmB,EAAE;AAE7C;;;;;;;AAOG;AACH,IAAA,mBAAmB,EAAE,KAAK;AAE1B;;;;;;;;;AASG;AACH,IAAA,YAAY,EAAE,KAAK;;;;;;;"}