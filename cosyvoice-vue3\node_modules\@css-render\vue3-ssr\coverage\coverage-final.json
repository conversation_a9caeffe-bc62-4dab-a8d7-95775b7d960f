{"/Users/<USER>/dev/css-render/packages/vue3-ssr/src/index.ts": {"path": "/Users/<USER>/dev/css-render/packages/vue3-ssr/src/index.ts", "statementMap": {"0": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 67}}, "1": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 54}}, "2": {"start": {"line": 20, "column": 26}, "end": {"line": 20, "column": 36}}, "3": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 25}}, "4": {"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 25}}, "5": {"start": {"line": 23, "column": 2}, "end": {"line": 26, "column": 3}}, "6": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 15}}, "7": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 45}}, "8": {"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": 49}}, "9": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 33}}, "10": {"start": {"line": 37, "column": 17}, "end": {"line": 37, "column": 33}}, "11": {"start": {"line": 38, "column": 18}, "end": {"line": 38, "column": 45}}, "12": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 40}}, "13": {"start": {"line": 39, "column": 24}, "end": {"line": 39, "column": 40}}, "14": {"start": {"line": 40, "column": 2}, "end": {"line": 43, "column": 3}}, "15": {"start": {"line": 41, "column": 28}, "end": {"line": 41, "column": 58}}, "16": {"start": {"line": 51, "column": 27}, "end": {"line": 51, "column": 29}}, "17": {"start": {"line": 52, "column": 37}, "end": {"line": 55, "column": 3}}, "18": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 40}}, "19": {"start": {"line": 57, "column": 2}, "end": {"line": 63, "column": 3}}, "20": {"start": {"line": 59, "column": 18}, "end": {"line": 59, "column": 35}}, "21": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 23}}, "22": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 16}}}, "fnMap": {"0": {"name": "createStyleString", "decl": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 26}}, "loc": {"start": {"line": 11, "column": 63}, "end": {"line": 13, "column": 1}}, "line": 11}, "1": {"name": "ssrAdapter", "decl": {"start": {"line": 15, "column": 9}, "end": {"line": 15, "column": 19}}, "loc": {"start": {"line": 19, "column": 8}, "end": {"line": 27, "column": 1}}, "line": 19}, "2": {"name": "useSsrAdapter", "decl": {"start": {"line": 31, "column": 16}, "end": {"line": 31, "column": 29}}, "loc": {"start": {"line": 36, "column": 12}, "end": {"line": 44, "column": 1}}, "line": 36}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 41, "column": 13}, "end": {"line": 41, "column": 14}}, "loc": {"start": {"line": 41, "column": 28}, "end": {"line": 41, "column": 58}}, "line": 41}, "4": {"name": "setup", "decl": {"start": {"line": 50, "column": 16}, "end": {"line": 50, "column": 21}}, "loc": {"start": {"line": 50, "column": 44}, "end": {"line": 64, "column": 1}}, "line": 50}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 5}}, "loc": {"start": {"line": 58, "column": 15}, "end": {"line": 62, "column": 5}}, "line": 58}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 25}}, "type": "if", "locations": [{"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 25}}, {"start": {}, "end": {}}], "line": 22}, "1": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 26, "column": 3}}, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 26, "column": 3}}, {"start": {}, "end": {}}], "line": 23}, "2": {"loc": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 33}}, "type": "if", "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 33}}, {"start": {}, "end": {}}], "line": 37}, "3": {"loc": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 40}}, "type": "if", "locations": [{"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 40}}, {"start": {}, "end": {}}], "line": 39}}, "s": {"0": 1, "1": 2, "2": 2, "3": 2, "4": 0, "5": 2, "6": 2, "7": 2, "8": 1, "9": 2, "10": 0, "11": 2, "12": 2, "13": 0, "14": 2, "15": 2, "16": 2, "17": 2, "18": 2, "19": 2, "20": 2, "21": 2, "22": 2}, "f": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2}, "b": {"0": [0, 2], "1": [2, 0], "2": [0, 2], "3": [0, 2]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "34780b71dea6a39f6d07f974dbd5f9269403407a"}}