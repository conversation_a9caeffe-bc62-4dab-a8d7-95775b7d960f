/*!
 * @pixi/mixin-get-global-position - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-get-global-position is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{DisplayObject as i}from"@pixi/display";import{Point as o}from"@pixi/math";i.prototype.getGlobalPosition=function(i,t){return void 0===i&&(i=new o),void 0===t&&(t=!1),this.parent?this.parent.toGlobal(this.position,i,t):(i.x=this.position.x,i.y=this.position.y),i};
//# sourceMappingURL=mixin-get-global-position.min.mjs.map
