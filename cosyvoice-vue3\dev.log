
> cosyvoice-vue3@0.0.0 dev
> vite

/mnt/h/AI/CosyVoice/cosyvoice-vue3/node_modules/rollup/dist/native.js:64
		throw new Error(
		      ^

Error: Cannot find module @rollup/rollup-linux-x64-gnu. npm has a bug related to optional dependencies (https://github.com/npm/cli/issues/4828). Please try `npm i` again after removing both package-lock.json and node_modules directory.
    at requireWithFriendlyError (/mnt/h/AI/CosyVoice/cosyvoice-vue3/node_modules/rollup/dist/native.js:64:9)
    at Object.<anonymous> (/mnt/h/AI/CosyVoice/cosyvoice-vue3/node_modules/rollup/dist/native.js:73:76)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at cjsLoader (node:internal/modules/esm/translators:262:5)
    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:196:7) {
  [cause]: Error: Cannot find module '@rollup/rollup-linux-x64-gnu'
  Require stack:
  - /mnt/h/AI/CosyVoice/cosyvoice-vue3/node_modules/rollup/dist/native.js
      at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
      at Function._load (node:internal/modules/cjs/loader:1055:27)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Module.require (node:internal/modules/cjs/loader:1311:12)
      at require (node:internal/modules/helpers:136:16)
      at requireWithFriendlyError (/mnt/h/AI/CosyVoice/cosyvoice-vue3/node_modules/rollup/dist/native.js:46:10)
      at Object.<anonymous> (/mnt/h/AI/CosyVoice/cosyvoice-vue3/node_modules/rollup/dist/native.js:73:76)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object..js (node:internal/modules/cjs/loader:1706:10) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      '/mnt/h/AI/CosyVoice/cosyvoice-vue3/node_modules/rollup/dist/native.js'
    ]
  }
}

Node.js v22.14.0
