import type { DisplayObject } from '@pixi/display';
import type { ExtensionMetadata } from '@pixi/core';
import type { IRendererPlugin } from '@pixi/core';
import { Rectangle } from '@pixi/math';
import type { Renderer } from '@pixi/core';
import { RenderTexture } from '@pixi/core';

/**
 * This class provides renderer-specific plugins for exporting content from a renderer.
 * For instance, these plugins can be used for saving an Image, Canvas element or for exporting the raw image data (pixels).
 *
 * Do not instantiate these plugins directly. It is available from the `renderer.plugins` property.
 * See {@link PIXI.CanvasRenderer#plugins} or {@link PIXI.Renderer#plugins}.
 * @example
 * // Create a new app (will auto-add extract plugin to renderer)
 * const app = new PIXI.Application();
 *
 * // Draw a red circle
 * const graphics = new PIXI.Graphics()
 *     .beginFill(0xFF0000)
 *     .drawCircle(0, 0, 50);
 *
 * // Render the graphics as an HTMLImageElement
 * const image = app.renderer.plugins.extract.image(graphics);
 * document.body.appendChild(image);
 * @memberof PIXI
 */
declare class Extract_2 implements IRendererPlugin {
    /** @ignore */
    static extension: ExtensionMetadata;
    private renderer;
    /**
     * @param renderer - A reference to the current renderer
     */
    constructor(renderer: Renderer);
    /**
     * Will return a HTML Image of the target
     * @param target - A displayObject or renderTexture
     *  to convert. If left empty will use the main renderer
     * @param format - Image format, e.g. "image/jpeg" or "image/webp".
     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.
     * @returns - HTML Image of the target
     */
    image(target: DisplayObject | RenderTexture, format?: string, quality?: number): HTMLImageElement;
    /**
     * Will return a base64 encoded string of this target. It works by calling
     *  `Extract.getCanvas` and then running toDataURL on that.
     * @param target - A displayObject or renderTexture
     *  to convert. If left empty will use the main renderer
     * @param format - Image format, e.g. "image/jpeg" or "image/webp".
     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.
     * @returns - A base64 encoded string of the texture.
     */
    base64(target: DisplayObject | RenderTexture, format?: string, quality?: number): string;
    /**
     * Creates a Canvas element, renders this target to it and then returns it.
     * @param target - A displayObject or renderTexture
     *  to convert. If left empty will use the main renderer
     * @param frame - The frame the extraction is restricted to.
     * @returns - A Canvas element with the texture rendered on.
     */
    canvas(target?: DisplayObject | RenderTexture, frame?: Rectangle): HTMLCanvasElement;
    /**
     * Will return a one-dimensional array containing the pixel data of the entire texture in RGBA
     * order, with integer values between 0 and 255 (included).
     * @param target - A displayObject or renderTexture
     *  to convert. If left empty will use the main renderer
     * @param frame - The frame the extraction is restricted to.
     * @returns - One-dimensional array containing the pixel data of the entire texture
     */
    pixels(target?: DisplayObject | RenderTexture, frame?: Rectangle | PixelExtractOptions): Uint8Array;
    private _rawPixels;
    /** Destroys the extract. */
    destroy(): void;
    /**
     * Takes premultiplied pixel data and produces regular pixel data
     * @private
     * @param pixels - array of pixel data
     * @param out - output array
     */
    static arrayPostDivide(pixels: number[] | Uint8Array | Uint8ClampedArray, out: number[] | Uint8Array | Uint8ClampedArray): void;
}
export { Extract_2 as Extract }

/**
 * this interface is used to extract only  a single pixel of Render Texture or Display Object
 * if you use this Interface all fields is required
 * @deprecated
 * @example
 * test: PixelExtractOptions = { x: 15, y: 20, resolution: 4, width: 10, height: 10 }
 */
export declare interface PixelExtractOptions {
    x: number;
    y: number;
    height: number;
    resolution: number;
    width: number;
}

export { }
