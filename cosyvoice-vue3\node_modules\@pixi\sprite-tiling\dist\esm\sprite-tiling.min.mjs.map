{"version": 3, "file": "sprite-tiling.min.mjs", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/TilingSprite.ts", "../../src/TilingSpriteRenderer.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { Texture, TextureMatrix } from '@pixi/core';\nimport { Point, Rectangle, Transform  } from '@pixi/math';\nimport { Sprite } from '@pixi/sprite';\nimport type { Renderer, IBaseTextureOptions, TextureSource } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { IPoint, IPointData, ISize, ObservablePoint } from '@pixi/math';\n\nconst tempPoint = new Point();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface TilingSprite extends GlobalMixins.TilingSprite {}\n\n/**\n * A tiling sprite is a fast way of rendering a tiling image.\n * @memberof PIXI\n */\nexport class TilingSprite extends Sprite\n{\n    /** Tile transform */\n    public tileTransform: Transform;\n\n    /** Matrix that is applied to UV to get the coords in Texture normalized space to coords in BaseTexture space. */\n    public uvMatrix: TextureMatrix;\n\n    /**\n     * Flags whether the tiling pattern should originate from the origin instead of the top-left corner in\n     * local space.\n     *\n     * This will make the texture coordinates assigned to each vertex dependent on the value of the anchor. Without\n     * this, the top-left corner always gets the (0, 0) texture coordinate.\n     * @default false\n     */\n    public uvRespectAnchor: boolean;\n\n    /**\n     * @param texture - The texture of the tiling sprite.\n     * @param width - The width of the tiling sprite.\n     * @param height - The height of the tiling sprite.\n     */\n    constructor(texture: Texture, width = 100, height = 100)\n    {\n        super(texture);\n\n        this.tileTransform = new Transform();\n\n        // The width of the tiling sprite\n        this._width = width;\n\n        // The height of the tiling sprite\n        this._height = height;\n\n        this.uvMatrix = this.texture.uvMatrix || new TextureMatrix(texture);\n\n        /**\n         * Plugin that is responsible for rendering this element.\n         * Allows to customize the rendering process without overriding '_render' method.\n         * @default 'tilingSprite'\n         */\n        this.pluginName = 'tilingSprite';\n\n        this.uvRespectAnchor = false;\n    }\n    /**\n     * Changes frame clamping in corresponding textureTransform, shortcut\n     * Change to -0.5 to add a pixel to the edge, recommended for transparent trimmed textures in atlas\n     * @default 0.5\n     * @member {number}\n     */\n    get clampMargin(): number\n    {\n        return this.uvMatrix.clampMargin;\n    }\n\n    set clampMargin(value: number)\n    {\n        this.uvMatrix.clampMargin = value;\n        this.uvMatrix.update(true);\n    }\n\n    /** The scaling of the image that is being tiled. */\n    get tileScale(): ObservablePoint\n    {\n        return this.tileTransform.scale;\n    }\n\n    set tileScale(value: IPointData)\n    {\n        this.tileTransform.scale.copyFrom(value as IPoint);\n    }\n\n    /** The offset of the image that is being tiled. */\n    get tilePosition(): ObservablePoint\n    {\n        return this.tileTransform.position;\n    }\n\n    set tilePosition(value: ObservablePoint)\n    {\n        this.tileTransform.position.copyFrom(value as IPoint);\n    }\n\n    /**\n     * @protected\n     */\n    protected _onTextureUpdate(): void\n    {\n        if (this.uvMatrix)\n        {\n            this.uvMatrix.texture = this._texture;\n        }\n        this._cachedTint = 0xFFFFFF;\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // tweak our texture temporarily..\n        const texture = this._texture;\n\n        if (!texture || !texture.valid)\n        {\n            return;\n        }\n\n        this.tileTransform.updateLocalTransform();\n        this.uvMatrix.update();\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n        renderer.plugins[this.pluginName].render(this);\n    }\n\n    /** Updates the bounds of the tiling sprite. */\n    protected _calculateBounds(): void\n    {\n        const minX = this._width * -this._anchor._x;\n        const minY = this._height * -this._anchor._y;\n        const maxX = this._width * (1 - this._anchor._x);\n        const maxY = this._height * (1 - this._anchor._y);\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Gets the local bounds of the sprite object.\n     * @param rect - Optional output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        // we can do a fast local bounds if the sprite has no children!\n        if (this.children.length === 0)\n        {\n            this._bounds.minX = this._width * -this._anchor._x;\n            this._bounds.minY = this._height * -this._anchor._y;\n            this._bounds.maxX = this._width * (1 - this._anchor._x);\n            this._bounds.maxY = this._height * (1 - this._anchor._y);\n\n            if (!rect)\n            {\n                if (!this._localBoundsRect)\n                {\n                    this._localBoundsRect = new Rectangle();\n                }\n\n                rect = this._localBoundsRect;\n            }\n\n            return this._bounds.getRectangle(rect);\n        }\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /**\n     * Checks if a point is inside this tiling sprite.\n     * @param point - The point to check.\n     * @returns Whether or not the sprite contains the point.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const width = this._width;\n        const height = this._height;\n        const x1 = -width * this.anchor._x;\n\n        if (tempPoint.x >= x1 && tempPoint.x < x1 + width)\n        {\n            const y1 = -height * this.anchor._y;\n\n            if (tempPoint.y >= y1 && tempPoint.y < y1 + height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Destroys this sprite and optionally its texture and children\n     * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well\n     * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this.tileTransform = null;\n        this.uvMatrix = null;\n    }\n\n    /**\n     * Helper function that creates a new tiling sprite based on the source you provide.\n     * The source can be - frame id, image url, video url, canvas element, video element, base texture\n     * @static\n     * @param {string|PIXI.Texture|HTMLCanvasElement|HTMLVideoElement} source - Source to create texture from\n     * @param {object} options - See {@link PIXI.BaseTexture}'s constructor for options.\n     * @param {number} options.width - required width of the tiling sprite\n     * @param {number} options.height - required height of the tiling sprite\n     * @returns {PIXI.TilingSprite} The newly created texture\n     */\n    static from(source: TextureSource | Texture, options: ISize & IBaseTextureOptions): TilingSprite\n    {\n        const texture = (source instanceof Texture)\n            ? source\n            : Texture.from(source, options);\n\n        return new TilingSprite(\n            texture,\n            options.width,\n            options.height\n        );\n    }\n\n    /** The width of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    set width(value: number)\n    {\n        this._width = value;\n    }\n\n    /** The height of the TilingSprite, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    set height(value: number)\n    {\n        this._height = value;\n    }\n}\n", "import { ObjectRenderer, Shader, State, QuadUv, ExtensionType } from '@pixi/core';\nimport { WRAP_MODES } from '@pixi/constants';\nimport { Matrix } from '@pixi/math';\nimport { premultiplyTintToRgba, correctBlendMode } from '@pixi/utils';\n\nimport fragmentSimpleSrc from './sprite-tiling-simple.frag';\nimport gl1VertexSrc from './sprite-tiling-fallback.vert';\nimport gl1FragmentSrc from './sprite-tiling-fallback.frag';\nimport gl2VertexSrc from './sprite-tiling.vert';\nimport gl2FragmentSrc from './sprite-tiling.frag';\n\nimport type { Renderer, ExtensionMetadata } from '@pixi/core';\nimport type { TilingSprite } from './TilingSprite';\n\nconst tempMat = new Matrix();\n\n/**\n * WebGL renderer plugin for tiling sprites\n * @class\n * @memberof PIXI\n * @extends PIXI.ObjectRenderer\n */\nexport class TilingSprite<PERSON>ender<PERSON> extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'tilingSprite',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    public shader: Shader;\n    public simpleShader: Shader;\n    public quad: QuadUv;\n    public readonly state: State;\n\n    /**\n     * constructor for renderer\n     * @param {PIXI.Renderer} renderer - The renderer this tiling awesomeness works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // WebGL version is not available during initialization!\n        renderer.runners.contextChange.add(this);\n\n        this.quad = new QuadUv();\n\n        /**\n         * The WebGL state in which this renderer will work.\n         * @member {PIXI.State}\n         * @readonly\n         */\n        this.state = State.for2d();\n    }\n\n    /** Creates shaders when context is initialized. */\n    contextChange(): void\n    {\n        const renderer = this.renderer;\n        const uniforms = { globals: renderer.globalUniforms };\n\n        this.simpleShader = Shader.from(gl1VertexSrc, fragmentSimpleSrc, uniforms);\n        this.shader = renderer.context.webGLVersion > 1\n            ? Shader.from(gl2VertexSrc, gl2FragmentSrc, uniforms)\n            : Shader.from(gl1VertexSrc, gl1FragmentSrc, uniforms);\n    }\n\n    /**\n     * @param {PIXI.TilingSprite} ts - tilingSprite to be rendered\n     */\n    public render(ts: TilingSprite): void\n    {\n        const renderer = this.renderer;\n        const quad = this.quad;\n\n        let vertices = quad.vertices;\n\n        vertices[0] = vertices[6] = (ts._width) * -ts.anchor.x;\n        vertices[1] = vertices[3] = ts._height * -ts.anchor.y;\n\n        vertices[2] = vertices[4] = (ts._width) * (1.0 - ts.anchor.x);\n        vertices[5] = vertices[7] = ts._height * (1.0 - ts.anchor.y);\n\n        const anchorX = ts.uvRespectAnchor ? ts.anchor.x : 0;\n        const anchorY = ts.uvRespectAnchor ? ts.anchor.y : 0;\n\n        vertices = quad.uvs;\n\n        vertices[0] = vertices[6] = -anchorX;\n        vertices[1] = vertices[3] = -anchorY;\n\n        vertices[2] = vertices[4] = 1.0 - anchorX;\n        vertices[5] = vertices[7] = 1.0 - anchorY;\n\n        quad.invalidate();\n\n        const tex = ts._texture;\n        const baseTex = tex.baseTexture;\n        const premultiplied = baseTex.alphaMode > 0;\n        const lt = ts.tileTransform.localTransform;\n        const uv = ts.uvMatrix;\n        let isSimple = baseTex.isPowerOfTwo\n            && tex.frame.width === baseTex.width && tex.frame.height === baseTex.height;\n\n        // auto, force repeat wrapMode for big tiling textures\n        if (isSimple)\n        {\n            if (!baseTex._glTextures[renderer.CONTEXT_UID])\n            {\n                if (baseTex.wrapMode === WRAP_MODES.CLAMP)\n                {\n                    baseTex.wrapMode = WRAP_MODES.REPEAT;\n                }\n            }\n            else\n            {\n                isSimple = baseTex.wrapMode !== WRAP_MODES.CLAMP;\n            }\n        }\n\n        const shader = isSimple ? this.simpleShader : this.shader;\n\n        const w = tex.width;\n        const h = tex.height;\n        const W = ts._width;\n        const H = ts._height;\n\n        tempMat.set(lt.a * w / W,\n            lt.b * w / H,\n            lt.c * h / W,\n            lt.d * h / H,\n            lt.tx / W,\n            lt.ty / H);\n\n        // that part is the same as above:\n        // tempMat.identity();\n        // tempMat.scale(tex.width, tex.height);\n        // tempMat.prepend(lt);\n        // tempMat.scale(1.0 / ts._width, 1.0 / ts._height);\n\n        tempMat.invert();\n        if (isSimple)\n        {\n            tempMat.prepend(uv.mapCoord);\n        }\n        else\n        {\n            shader.uniforms.uMapCoord = uv.mapCoord.toArray(true);\n            shader.uniforms.uClampFrame = uv.uClampFrame;\n            shader.uniforms.uClampOffset = uv.uClampOffset;\n        }\n\n        shader.uniforms.uTransform = tempMat.toArray(true);\n        shader.uniforms.uColor = premultiplyTintToRgba(ts.tint, ts.worldAlpha,\n            shader.uniforms.uColor, premultiplied);\n        shader.uniforms.translationMatrix = ts.transform.worldTransform.toArray(true);\n        shader.uniforms.uSampler = tex;\n\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(quad);\n\n        this.state.blendMode = correctBlendMode(ts.blendMode, premultiplied);\n        renderer.state.set(this.state);\n        renderer.geometry.draw(this.renderer.gl.TRIANGLES, 6, 0);\n    }\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "this", "constructor", "prototype", "create", "tempPoint", "Point", "TilingSprite", "_super", "texture", "width", "height", "_this", "call", "tileTransform", "Transform", "_width", "_height", "uvMatrix", "TextureMatrix", "pluginName", "uvRespectAnchor", "defineProperty", "get", "clamp<PERSON><PERSON><PERSON>", "set", "value", "update", "scale", "copyFrom", "position", "_onTextureUpdate", "_texture", "_cachedTint", "_render", "renderer", "valid", "updateLocalTransform", "batch", "setObjectR<PERSON><PERSON>", "plugins", "render", "_calculateBounds", "minX", "_anchor", "_x", "minY", "_y", "maxX", "maxY", "_bounds", "addFrame", "transform", "getLocalBounds", "rect", "children", "length", "_localBoundsRect", "Rectangle", "getRectangle", "containsPoint", "point", "worldTransform", "applyInverse", "x1", "anchor", "x", "y1", "y", "destroy", "options", "from", "source", "Texture", "Sprite", "tempMat", "Matrix", "TilingSprite<PERSON><PERSON><PERSON>", "runners", "contextChange", "add", "quad", "QuadUv", "state", "State", "for2d", "uniforms", "globals", "globalUniforms", "simpleShader", "Shader", "gl1VertexSrc", "shader", "context", "webGLVersion", "ts", "vertices", "anchorX", "anchorY", "uvs", "invalidate", "tex", "baseTex", "baseTexture", "premultiplied", "alphaMode", "lt", "localTransform", "uv", "isSimple", "isPowerOfTwo", "frame", "_glTextures", "CONTEXT_UID", "wrapMode", "WRAP_MODES", "CLAMP", "REPEAT", "w", "h", "W", "H", "a", "c", "tx", "ty", "invert", "prepend", "mapCoord", "uMapCoord", "toArray", "uClampFrame", "uClampOffset", "uTransform", "uColor", "premultiplyTintToRgba", "tint", "worldAlpha", "translationMatrix", "uSampler", "bind", "geometry", "blendMode", "correctBlendMode", "draw", "gl", "TRIANGLES", "extension", "name", "type", "ExtensionType", "RendererPlugin", "O<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;4WAgBA,IAAIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,IAGrB,SAASO,EAAUR,EAAGC,GAEzB,SAASQ,IAAOC,KAAKC,YAAcX,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEY,UAAkB,OAANX,EAAaC,OAAOW,OAAOZ,IAAMQ,EAAGG,UAAYX,EAAEW,UAAW,IAAIH,GCnBnF,IAAMK,EAAY,IAAIC,EAStBC,EAAA,SAAAC,GAuBI,SAAAD,EAAYE,EAAkBC,EAAaC,QAAb,IAAAD,IAAAA,EAAW,UAAE,IAAAC,IAAAA,EAAY,KAAvD,IAEIC,EAAAJ,EAAAK,KAAAZ,KAAMQ,IAoBTR,YAlBGW,EAAKE,cAAgB,IAAIC,EAGzBH,EAAKI,OAASN,EAGdE,EAAKK,QAAUN,EAEfC,EAAKM,SAAWN,EAAKH,QAAQS,UAAY,IAAIC,EAAcV,GAO3DG,EAAKQ,WAAa,eAElBR,EAAKS,iBAAkB,IA2M/B,OAvPkCtB,EAAMQ,EAAAC,GAoDpCf,OAAA6B,eAAIf,EAAWJ,UAAA,cAAA,CAAfoB,IAAA,WAEI,OAAOtB,KAAKiB,SAASM,aAGzBC,IAAA,SAAgBC,GAEZzB,KAAKiB,SAASM,YAAcE,EAC5BzB,KAAKiB,SAASS,QAAO,oCAIzBlC,OAAA6B,eAAIf,EAASJ,UAAA,YAAA,CAAboB,IAAA,WAEI,OAAOtB,KAAKa,cAAcc,OAG9BH,IAAA,SAAcC,GAEVzB,KAAKa,cAAcc,MAAMC,SAASH,oCAItCjC,OAAA6B,eAAIf,EAAYJ,UAAA,eAAA,CAAhBoB,IAAA,WAEI,OAAOtB,KAAKa,cAAcgB,UAG9BL,IAAA,SAAiBC,GAEbzB,KAAKa,cAAcgB,SAASD,SAASH,oCAM/BnB,EAAAJ,UAAA4B,iBAAV,WAEQ9B,KAAKiB,WAELjB,KAAKiB,SAAST,QAAUR,KAAK+B,UAEjC/B,KAAKgC,YAAc,UAOb1B,EAAOJ,UAAA+B,QAAjB,SAAkBC,GAGd,IAAM1B,EAAUR,KAAK+B,SAEhBvB,GAAYA,EAAQ2B,QAKzBnC,KAAKa,cAAcuB,uBACnBpC,KAAKiB,SAASS,SAEdQ,EAASG,MAAMC,kBAAkBJ,EAASK,QAAQvC,KAAKmB,aACvDe,EAASK,QAAQvC,KAAKmB,YAAYqB,OAAOxC,QAInCM,EAAAJ,UAAAuC,iBAAV,WAEI,IAAMC,EAAO1C,KAAKe,QAAUf,KAAK2C,QAAQC,GACnCC,EAAO7C,KAAKgB,SAAWhB,KAAK2C,QAAQG,GACpCC,EAAO/C,KAAKe,QAAU,EAAIf,KAAK2C,QAAQC,IACvCI,EAAOhD,KAAKgB,SAAW,EAAIhB,KAAK2C,QAAQG,IAE9C9C,KAAKiD,QAAQC,SAASlD,KAAKmD,UAAWT,EAAMG,EAAME,EAAMC,IAQrD1C,EAAcJ,UAAAkD,eAArB,SAAsBC,GAGlB,OAA6B,IAAzBrD,KAAKsD,SAASC,QAEdvD,KAAKiD,QAAQP,KAAO1C,KAAKe,QAAUf,KAAK2C,QAAQC,GAChD5C,KAAKiD,QAAQJ,KAAO7C,KAAKgB,SAAWhB,KAAK2C,QAAQG,GACjD9C,KAAKiD,QAAQF,KAAO/C,KAAKe,QAAU,EAAIf,KAAK2C,QAAQC,IACpD5C,KAAKiD,QAAQD,KAAOhD,KAAKgB,SAAW,EAAIhB,KAAK2C,QAAQG,IAEhDO,IAEIrD,KAAKwD,mBAENxD,KAAKwD,iBAAmB,IAAIC,GAGhCJ,EAAOrD,KAAKwD,kBAGTxD,KAAKiD,QAAQS,aAAaL,IAG9B9C,EAAAL,UAAMkD,eAAexC,KAAKZ,KAAMqD,IAQpC/C,EAAaJ,UAAAyD,cAApB,SAAqBC,GAEjB5D,KAAK6D,eAAeC,aAAaF,EAAOxD,GAExC,IAAMK,EAAQT,KAAKe,OACbL,EAASV,KAAKgB,QACd+C,GAAMtD,EAAQT,KAAKgE,OAAOpB,GAEhC,GAAIxC,EAAU6D,GAAKF,GAAM3D,EAAU6D,EAAIF,EAAKtD,EAC5C,CACI,IAAMyD,GAAMxD,EAASV,KAAKgE,OAAOlB,GAEjC,GAAI1C,EAAU+D,GAAKD,GAAM9D,EAAU+D,EAAID,EAAKxD,EAExC,OAAO,EAIf,OAAO,GAYJJ,EAAOJ,UAAAkE,QAAd,SAAeC,GAEX9D,EAAAL,UAAMkE,QAAOxD,KAAAZ,KAACqE,GAEdrE,KAAKa,cAAgB,KACrBb,KAAKiB,SAAW,MAabX,EAAAgE,KAAP,SAAYC,EAAiCF,GAMzC,OAAO,IAAI/D,EAJMiE,aAAkBC,EAC7BD,EACAC,EAAQF,KAAKC,EAAQF,GAIvBA,EAAQ5D,MACR4D,EAAQ3D,SAKhBlB,OAAA6B,eAAIf,EAAKJ,UAAA,QAAA,CAAToB,IAAA,WAEI,OAAOtB,KAAKe,QAGhBS,IAAA,SAAUC,GAENzB,KAAKe,OAASU,mCAIlBjC,OAAA6B,eAAIf,EAAMJ,UAAA,SAAA,CAAVoB,IAAA,WAEI,OAAOtB,KAAKgB,SAGhBQ,IAAA,SAAWC,GAEPzB,KAAKgB,QAAUS,mCAEtBnB,EAvPD,CAAkCmE,2dCF5BC,EAAU,IAAIC,EAQpBC,EAAA,SAAArE,GAiBI,SAAAqE,EAAY1C,GAAZ,IAEIvB,EAAAJ,EAAAK,KAAAZ,KAAMkC,IAaTlC,YAVGkC,EAAS2C,QAAQC,cAAcC,IAAIpE,GAEnCA,EAAKqE,KAAO,IAAIC,EAOhBtE,EAAKuE,MAAQC,EAAMC,UAiH3B,OAhJ0CtF,EAAc8E,EAAArE,GAmCpDqE,EAAA1E,UAAA4E,cAAA,WAEI,IAAM5C,EAAWlC,KAAKkC,SAChBmD,EAAW,CAAEC,QAASpD,EAASqD,gBAErCvF,KAAKwF,aAAeC,EAAOnB,KAAKoB,ySAAiCL,GACjErF,KAAK2F,OAASzD,EAAS0D,QAAQC,aAAe,EACxCJ,EAAOnB,0mCAAmCe,GAC1CI,EAAOnB,KAAKoB,s2BAA8BL,IAM7CT,EAAM1E,UAAAsC,OAAb,SAAcsD,GAEV,IAAM5D,EAAWlC,KAAKkC,SAChB8C,EAAOhF,KAAKgF,KAEde,EAAWf,EAAKe,SAEpBA,EAAS,GAAKA,EAAS,GAAMD,EAAS,QAAKA,EAAG9B,OAAOC,EACrD8B,EAAS,GAAKA,EAAS,GAAKD,EAAG9E,SAAW8E,EAAG9B,OAAOG,EAEpD4B,EAAS,GAAKA,EAAS,GAAMD,EAAS,QAAK,EAAMA,EAAG9B,OAAOC,GAC3D8B,EAAS,GAAKA,EAAS,GAAKD,EAAG9E,SAAW,EAAM8E,EAAG9B,OAAOG,GAE1D,IAAM6B,EAAUF,EAAG1E,gBAAkB0E,EAAG9B,OAAOC,EAAI,EAC7CgC,EAAUH,EAAG1E,gBAAkB0E,EAAG9B,OAAOG,EAAI,GAEnD4B,EAAWf,EAAKkB,KAEP,GAAKH,EAAS,IAAMC,EAC7BD,EAAS,GAAKA,EAAS,IAAME,EAE7BF,EAAS,GAAKA,EAAS,GAAK,EAAMC,EAClCD,EAAS,GAAKA,EAAS,GAAK,EAAME,EAElCjB,EAAKmB,aAEL,IAAMC,EAAMN,EAAG/D,SACTsE,EAAUD,EAAIE,YACdC,EAAgBF,EAAQG,UAAY,EACpCC,EAAKX,EAAGjF,cAAc6F,eACtBC,EAAKb,EAAG7E,SACV2F,EAAWP,EAAQQ,cAChBT,EAAIU,MAAMrG,QAAU4F,EAAQ5F,OAAS2F,EAAIU,MAAMpG,SAAW2F,EAAQ3F,OAGrEkG,IAEKP,EAAQU,YAAY7E,EAAS8E,aAS9BJ,EAAWP,EAAQY,WAAaC,EAAWC,MAPvCd,EAAQY,WAAaC,EAAWC,QAEhCd,EAAQY,SAAWC,EAAWE,SAS1C,IAAMzB,EAASiB,EAAW5G,KAAKwF,aAAexF,KAAK2F,OAE7C0B,EAAIjB,EAAI3F,MACR6G,EAAIlB,EAAI1F,OACR6G,EAAIzB,EAAG/E,OACPyG,EAAI1B,EAAG9E,QAEb0D,EAAQlD,IAAIiF,EAAGgB,EAAIJ,EAAIE,EACnBd,EAAGlH,EAAI8H,EAAIG,EACXf,EAAGiB,EAAIJ,EAAIC,EACXd,EAAGnH,EAAIgI,EAAIE,EACXf,EAAGkB,GAAKJ,EACRd,EAAGmB,GAAKJ,GAQZ9C,EAAQmD,SACJjB,EAEAlC,EAAQoD,QAAQnB,EAAGoB,WAInBpC,EAAON,SAAS2C,UAAYrB,EAAGoB,SAASE,SAAQ,GAChDtC,EAAON,SAAS6C,YAAcvB,EAAGuB,YACjCvC,EAAON,SAAS8C,aAAexB,EAAGwB,cAGtCxC,EAAON,SAAS+C,WAAa1D,EAAQuD,SAAQ,GAC7CtC,EAAON,SAASgD,OAASC,EAAsBxC,EAAGyC,KAAMzC,EAAG0C,WACvD7C,EAAON,SAASgD,OAAQ9B,GAC5BZ,EAAON,SAASoD,kBAAoB3C,EAAG3C,UAAUU,eAAeoE,SAAQ,GACxEtC,EAAON,SAASqD,SAAWtC,EAE3BlE,EAASyD,OAAOgD,KAAKhD,GACrBzD,EAAS0G,SAASD,KAAK3D,GAEvBhF,KAAKkF,MAAM2D,UAAYC,EAAiBhD,EAAG+C,UAAWtC,GACtDrE,EAASgD,MAAM1D,IAAIxB,KAAKkF,OACxBhD,EAAS0G,SAASG,KAAK/I,KAAKkC,SAAS8G,GAAGC,UAAW,EAAG,IA3InDrE,EAAAsE,UAA+B,CAClCC,KAAM,eACNC,KAAMC,EAAcC,gBA2I3B1E,EAhJD,CAA0C2E"}