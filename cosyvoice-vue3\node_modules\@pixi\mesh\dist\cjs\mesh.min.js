/*!
 * @pixi/mesh - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mesh is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("@pixi/core"),e=require("@pixi/math"),r=require("@pixi/constants"),i=require("@pixi/display"),n=require("@pixi/settings"),a=require("@pixi/utils"),o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},o(t,e)};function s(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var u=function(){function t(t,e){this.uvBuffer=t,this.uvMatrix=e,this.data=null,this._bufferUpdateId=-1,this._textureUpdateId=-1,this._updateID=0}return t.prototype.update=function(t){if(t||this._bufferUpdateId!==this.uvBuffer._updateID||this._textureUpdateId!==this.uvMatrix._updateID){this._bufferUpdateId=this.uvBuffer._updateID,this._textureUpdateId=this.uvMatrix._updateID;var e=this.uvBuffer.data;this.data&&this.data.length===e.length||(this.data=new Float32Array(e.length)),this.uvMatrix.multiplyUvs(e,this.data),this._updateID++}},t}(),h=new e.Point,l=new e.Polygon,d=function(e){function i(i,a,o,s){void 0===s&&(s=r.DRAW_MODES.TRIANGLES);var u=e.call(this)||this;return u.geometry=i,u.shader=a,u.state=o||t.State.for2d(),u.drawMode=s,u.start=0,u.size=0,u.uvs=null,u.indices=null,u.vertexData=new Float32Array(1),u.vertexDirty=-1,u._transformID=-1,u._roundPixels=n.settings.ROUND_PIXELS,u.batchUvs=null,u}return s(i,e),Object.defineProperty(i.prototype,"geometry",{get:function(){return this._geometry},set:function(t){this._geometry!==t&&(this._geometry&&(this._geometry.refCount--,0===this._geometry.refCount&&this._geometry.dispose()),this._geometry=t,this._geometry&&this._geometry.refCount++,this.vertexDirty=-1)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"uvBuffer",{get:function(){return this.geometry.buffers[1]},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"verticesBuffer",{get:function(){return this.geometry.buffers[0]},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"material",{get:function(){return this.shader},set:function(t){this.shader=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"blendMode",{get:function(){return this.state.blendMode},set:function(t){this.state.blendMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(t){this._roundPixels!==t&&(this._transformID=-1),this._roundPixels=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"tint",{get:function(){return"tint"in this.shader?this.shader.tint:null},set:function(t){this.shader.tint=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"texture",{get:function(){return"texture"in this.shader?this.shader.texture:null},set:function(t){this.shader.texture=t},enumerable:!1,configurable:!0}),i.prototype._render=function(t){var e=this.geometry.buffers[0].data;this.shader.batchable&&this.drawMode===r.DRAW_MODES.TRIANGLES&&e.length<2*i.BATCHABLE_SIZE?this._renderToBatch(t):this._renderDefault(t)},i.prototype._renderDefault=function(t){var e=this.shader;e.alpha=this.worldAlpha,e.update&&e.update(),t.batch.flush(),e.uniforms.translationMatrix=this.transform.worldTransform.toArray(!0),t.shader.bind(e),t.state.set(this.state),t.geometry.bind(this.geometry,e),t.geometry.draw(this.drawMode,this.size,this.start,this.geometry.instanceCount)},i.prototype._renderToBatch=function(t){var e=this.geometry,r=this.shader;r.uvMatrix&&(r.uvMatrix.update(),this.calculateUvs()),this.calculateVertices(),this.indices=e.indexBuffer.data,this._tintRGB=r._tintRGB,this._texture=r.texture;var i=this.material.pluginName;t.batch.setObjectRenderer(t.plugins[i]),t.plugins[i].render(this)},i.prototype.calculateVertices=function(){var t=this.geometry.buffers[0],e=t.data,r=t._updateID;if(r!==this.vertexDirty||this._transformID!==this.transform._worldID){this._transformID=this.transform._worldID,this.vertexData.length!==e.length&&(this.vertexData=new Float32Array(e.length));for(var i=this.transform.worldTransform,a=i.a,o=i.b,s=i.c,u=i.d,h=i.tx,l=i.ty,d=this.vertexData,f=0;f<d.length/2;f++){var c=e[2*f],p=e[2*f+1];d[2*f]=a*c+s*p+h,d[2*f+1]=o*c+u*p+l}if(this._roundPixels){var y=n.settings.RESOLUTION;for(f=0;f<d.length;++f)d[f]=Math.round((d[f]*y|0)/y)}this.vertexDirty=r}},i.prototype.calculateUvs=function(){var t=this.geometry.buffers[1],e=this.shader;e.uvMatrix.isSimple?this.uvs=t.data:(this.batchUvs||(this.batchUvs=new u(t,e.uvMatrix)),this.batchUvs.update(),this.uvs=this.batchUvs.data)},i.prototype._calculateBounds=function(){this.calculateVertices(),this._bounds.addVertexData(this.vertexData,0,this.vertexData.length)},i.prototype.containsPoint=function(t){if(!this.getBounds().contains(t.x,t.y))return!1;this.worldTransform.applyInverse(t,h);for(var e=this.geometry.getBuffer("aVertexPosition").data,r=l.points,i=this.geometry.getIndex().data,n=i.length,a=4===this.drawMode?3:1,o=0;o+2<n;o+=a){var s=2*i[o],u=2*i[o+1],d=2*i[o+2];if(r[0]=e[s],r[1]=e[s+1],r[2]=e[u],r[3]=e[u+1],r[4]=e[d],r[5]=e[d+1],l.contains(h.x,h.y))return!0}return!1},i.prototype.destroy=function(t){e.prototype.destroy.call(this,t),this._cachedTexture&&(this._cachedTexture.destroy(),this._cachedTexture=null),this.geometry=null,this.shader=null,this.state=null,this.uvs=null,this.indices=null,this.vertexData=null},i.BATCHABLE_SIZE=100,i}(i.Container),f=function(r){function i(i,n){var a=this,o={uSampler:i,alpha:1,uTextureMatrix:e.Matrix.IDENTITY,uColor:new Float32Array([1,1,1,1])};return(n=Object.assign({tint:16777215,alpha:1,pluginName:"batch"},n)).uniforms&&Object.assign(o,n.uniforms),(a=r.call(this,n.program||t.Program.from("attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTextureMatrix;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;\n}\n","varying vec2 vTextureCoord;\nuniform vec4 uColor;\n\nuniform sampler2D uSampler;\n\nvoid main(void)\n{\n    gl_FragColor = texture2D(uSampler, vTextureCoord) * uColor;\n}\n"),o)||this)._colorDirty=!1,a.uvMatrix=new t.TextureMatrix(i),a.batchable=void 0===n.program,a.pluginName=n.pluginName,a.tint=n.tint,a.alpha=n.alpha,a}return s(i,r),Object.defineProperty(i.prototype,"texture",{get:function(){return this.uniforms.uSampler},set:function(t){this.uniforms.uSampler!==t&&(!this.uniforms.uSampler.baseTexture.alphaMode!=!t.baseTexture.alphaMode&&(this._colorDirty=!0),this.uniforms.uSampler=t,this.uvMatrix.texture=t)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"alpha",{get:function(){return this._alpha},set:function(t){t!==this._alpha&&(this._alpha=t,this._colorDirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"tint",{get:function(){return this._tint},set:function(t){t!==this._tint&&(this._tint=t,this._tintRGB=(t>>16)+(65280&t)+((255&t)<<16),this._colorDirty=!0)},enumerable:!1,configurable:!0}),i.prototype.update=function(){if(this._colorDirty){this._colorDirty=!1;var t=this.texture.baseTexture;a.premultiplyTintToRgba(this._tint,this._alpha,this.uniforms.uColor,t.alphaMode)}this.uvMatrix.update()&&(this.uniforms.uTextureMatrix=this.uvMatrix.mapCoord)},i}(t.Shader),c=function(e){function i(i,n,a){var o=e.call(this)||this,s=new t.Buffer(i),u=new t.Buffer(n,!0),h=new t.Buffer(a,!0,!0);return o.addAttribute("aVertexPosition",s,2,!1,r.TYPES.FLOAT).addAttribute("aTextureCoord",u,2,!1,r.TYPES.FLOAT).addIndex(h),o._updateId=-1,o}return s(i,e),Object.defineProperty(i.prototype,"vertexDirtyId",{get:function(){return this.buffers[0]._updateID},enumerable:!1,configurable:!0}),i}(t.Geometry);exports.Mesh=d,exports.MeshBatchUvs=u,exports.MeshGeometry=c,exports.MeshMaterial=f;
//# sourceMappingURL=mesh.min.js.map
