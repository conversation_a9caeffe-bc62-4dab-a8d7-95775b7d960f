/*!
 * @pixi/particle-container - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/particle-container is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("@pixi/constants"),e=require("@pixi/display"),i=require("@pixi/utils"),r=require("@pixi/core"),o=require("@pixi/math"),a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},a(t,e)};function n(t,e){function i(){this.constructor=t}a(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var s=function(e){function r(i,r,o,a){void 0===i&&(i=1500),void 0===o&&(o=16384),void 0===a&&(a=!1);var n=e.call(this)||this;return o>16384&&(o=16384),n._properties=[!1,!0,!1,!1,!1],n._maxSize=i,n._batchSize=o,n._buffers=null,n._bufferUpdateIDs=[],n._updateID=0,n.interactiveChildren=!1,n.blendMode=t.BLEND_MODES.NORMAL,n.autoResize=a,n.roundPixels=!0,n.baseTexture=null,n.setProperties(r),n._tint=0,n.tintRgb=new Float32Array(4),n.tint=16777215,n}return n(r,e),r.prototype.setProperties=function(t){t&&(this._properties[0]="vertices"in t||"scale"in t?!!t.vertices||!!t.scale:this._properties[0],this._properties[1]="position"in t?!!t.position:this._properties[1],this._properties[2]="rotation"in t?!!t.rotation:this._properties[2],this._properties[3]="uvs"in t?!!t.uvs:this._properties[3],this._properties[4]="tint"in t||"alpha"in t?!!t.tint||!!t.alpha:this._properties[4])},r.prototype.updateTransform=function(){this.displayObjectUpdateTransform()},Object.defineProperty(r.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t,i.hex2rgb(t,this.tintRgb)},enumerable:!1,configurable:!0}),r.prototype.render=function(t){var e=this;this.visible&&!(this.worldAlpha<=0)&&this.children.length&&this.renderable&&(this.baseTexture||(this.baseTexture=this.children[0]._texture.baseTexture,this.baseTexture.valid||this.baseTexture.once("update",(function(){return e.onChildrenChange(0)}))),t.batch.setObjectRenderer(t.plugins.particle),t.plugins.particle.render(this))},r.prototype.onChildrenChange=function(t){for(var e=Math.floor(t/this._batchSize);this._bufferUpdateIDs.length<e;)this._bufferUpdateIDs.push(0);this._bufferUpdateIDs[e]=++this._updateID},r.prototype.dispose=function(){if(this._buffers){for(var t=0;t<this._buffers.length;++t)this._buffers[t].destroy();this._buffers=null}},r.prototype.destroy=function(t){e.prototype.destroy.call(this,t),this.dispose(),this._properties=null,this._buffers=null,this._bufferUpdateIDs=null},r}(e.Container),u=function(){function e(e,i,o){this.geometry=new r.Geometry,this.indexBuffer=null,this.size=o,this.dynamicProperties=[],this.staticProperties=[];for(var a=0;a<e.length;++a){var n=e[a];n={attributeName:n.attributeName,size:n.size,uploadFunction:n.uploadFunction,type:n.type||t.TYPES.FLOAT,offset:n.offset},i[a]?this.dynamicProperties.push(n):this.staticProperties.push(n)}this.staticStride=0,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.dynamicStride=0,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this._updateID=0,this.initBuffers()}return e.prototype.initBuffers=function(){var e=this.geometry,o=0;this.indexBuffer=new r.Buffer(i.createIndicesForQuads(this.size),!0,!0),e.addIndex(this.indexBuffer),this.dynamicStride=0;for(var a=0;a<this.dynamicProperties.length;++a){(p=this.dynamicProperties[a]).offset=o,o+=p.size,this.dynamicStride+=p.size}var n=new ArrayBuffer(this.size*this.dynamicStride*4*4);this.dynamicData=new Float32Array(n),this.dynamicDataUint32=new Uint32Array(n),this.dynamicBuffer=new r.Buffer(this.dynamicData,!1,!1);var s=0;this.staticStride=0;for(a=0;a<this.staticProperties.length;++a){(p=this.staticProperties[a]).offset=s,s+=p.size,this.staticStride+=p.size}var u=new ArrayBuffer(this.size*this.staticStride*4*4);this.staticData=new Float32Array(u),this.staticDataUint32=new Uint32Array(u),this.staticBuffer=new r.Buffer(this.staticData,!0,!1);for(a=0;a<this.dynamicProperties.length;++a){var p=this.dynamicProperties[a];e.addAttribute(p.attributeName,this.dynamicBuffer,0,p.type===t.TYPES.UNSIGNED_BYTE,p.type,4*this.dynamicStride,4*p.offset)}for(a=0;a<this.staticProperties.length;++a){p=this.staticProperties[a];e.addAttribute(p.attributeName,this.staticBuffer,0,p.type===t.TYPES.UNSIGNED_BYTE,p.type,4*this.staticStride,4*p.offset)}},e.prototype.uploadDynamic=function(e,i,r){for(var o=0;o<this.dynamicProperties.length;o++){var a=this.dynamicProperties[o];a.uploadFunction(e,i,r,a.type===t.TYPES.UNSIGNED_BYTE?this.dynamicDataUint32:this.dynamicData,this.dynamicStride,a.offset)}this.dynamicBuffer._updateID++},e.prototype.uploadStatic=function(e,i,r){for(var o=0;o<this.staticProperties.length;o++){var a=this.staticProperties[o];a.uploadFunction(e,i,r,a.type===t.TYPES.UNSIGNED_BYTE?this.staticDataUint32:this.staticData,this.staticStride,a.offset)}this.staticBuffer._updateID++},e.prototype.destroy=function(){this.indexBuffer=null,this.dynamicProperties=null,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this.staticProperties=null,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.geometry.destroy()},e}(),p=function(e){function a(i){var a=e.call(this,i)||this;return a.shader=null,a.properties=null,a.tempMatrix=new o.Matrix,a.properties=[{attributeName:"aVertexPosition",size:2,uploadFunction:a.uploadVertices,offset:0},{attributeName:"aPositionCoord",size:2,uploadFunction:a.uploadPosition,offset:0},{attributeName:"aRotation",size:1,uploadFunction:a.uploadRotation,offset:0},{attributeName:"aTextureCoord",size:2,uploadFunction:a.uploadUvs,offset:0},{attributeName:"aColor",size:1,type:t.TYPES.UNSIGNED_BYTE,uploadFunction:a.uploadTint,offset:0}],a.shader=r.Shader.from("attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\nattribute vec4 aColor;\n\nattribute vec2 aPositionCoord;\nattribute float aRotation;\n\nuniform mat3 translationMatrix;\nuniform vec4 uColor;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nvoid main(void){\n    float x = (aVertexPosition.x) * cos(aRotation) - (aVertexPosition.y) * sin(aRotation);\n    float y = (aVertexPosition.x) * sin(aRotation) + (aVertexPosition.y) * cos(aRotation);\n\n    vec2 v = vec2(x, y);\n    v = v + aPositionCoord;\n\n    gl_Position = vec4((translationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = aTextureCoord;\n    vColor = aColor * uColor;\n}\n","varying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nuniform sampler2D uSampler;\n\nvoid main(void){\n    vec4 color = texture2D(uSampler, vTextureCoord) * vColor;\n    gl_FragColor = color;\n}",{}),a.state=r.State.for2d(),a}return n(a,e),a.prototype.render=function(t){var e=t.children,r=t._maxSize,o=t._batchSize,a=this.renderer,n=e.length;if(0!==n){n>r&&!t.autoResize&&(n=r);var s=t._buffers;s||(s=t._buffers=this.generateBuffers(t));var u=e[0]._texture.baseTexture,p=u.alphaMode>0;this.state.blendMode=i.correctBlendMode(t.blendMode,p),a.state.set(this.state);var h=a.gl,f=t.worldTransform.copyTo(this.tempMatrix);f.prepend(a.globalUniforms.uniforms.projectionMatrix),this.shader.uniforms.translationMatrix=f.toArray(!0),this.shader.uniforms.uColor=i.premultiplyRgba(t.tintRgb,t.worldAlpha,this.shader.uniforms.uColor,p),this.shader.uniforms.uSampler=u,this.renderer.shader.bind(this.shader);for(var d=!1,l=0,c=0;l<n;l+=o,c+=1){var y=n-l;y>o&&(y=o),c>=s.length&&s.push(this._generateOneMoreBuffer(t));var v=s[c];v.uploadDynamic(e,l,y);var m=t._bufferUpdateIDs[c]||0;(d=d||v._updateID<m)&&(v._updateID=t._updateID,v.uploadStatic(e,l,y)),a.geometry.bind(v.geometry),h.drawElements(h.TRIANGLES,6*y,h.UNSIGNED_SHORT,0)}}},a.prototype.generateBuffers=function(t){for(var e=[],i=t._maxSize,r=t._batchSize,o=t._properties,a=0;a<i;a+=r)e.push(new u(this.properties,o,r));return e},a.prototype._generateOneMoreBuffer=function(t){var e=t._batchSize,i=t._properties;return new u(this.properties,i,e)},a.prototype.uploadVertices=function(t,e,i,r,o,a){for(var n=0,s=0,u=0,p=0,h=0;h<i;++h){var f=t[e+h],d=f._texture,l=f.scale.x,c=f.scale.y,y=d.trim,v=d.orig;y?(n=(s=y.x-f.anchor.x*v.width)+y.width,u=(p=y.y-f.anchor.y*v.height)+y.height):(n=v.width*(1-f.anchor.x),s=v.width*-f.anchor.x,u=v.height*(1-f.anchor.y),p=v.height*-f.anchor.y),r[a]=s*l,r[a+1]=p*c,r[a+o]=n*l,r[a+o+1]=p*c,r[a+2*o]=n*l,r[a+2*o+1]=u*c,r[a+3*o]=s*l,r[a+3*o+1]=u*c,a+=4*o}},a.prototype.uploadPosition=function(t,e,i,r,o,a){for(var n=0;n<i;n++){var s=t[e+n].position;r[a]=s.x,r[a+1]=s.y,r[a+o]=s.x,r[a+o+1]=s.y,r[a+2*o]=s.x,r[a+2*o+1]=s.y,r[a+3*o]=s.x,r[a+3*o+1]=s.y,a+=4*o}},a.prototype.uploadRotation=function(t,e,i,r,o,a){for(var n=0;n<i;n++){var s=t[e+n].rotation;r[a]=s,r[a+o]=s,r[a+2*o]=s,r[a+3*o]=s,a+=4*o}},a.prototype.uploadUvs=function(t,e,i,r,o,a){for(var n=0;n<i;++n){var s=t[e+n]._texture._uvs;s?(r[a]=s.x0,r[a+1]=s.y0,r[a+o]=s.x1,r[a+o+1]=s.y1,r[a+2*o]=s.x2,r[a+2*o+1]=s.y2,r[a+3*o]=s.x3,r[a+3*o+1]=s.y3,a+=4*o):(r[a]=0,r[a+1]=0,r[a+o]=0,r[a+o+1]=0,r[a+2*o]=0,r[a+2*o+1]=0,r[a+3*o]=0,r[a+3*o+1]=0,a+=4*o)}},a.prototype.uploadTint=function(t,e,r,o,a,n){for(var s=0;s<r;++s){var u=t[e+s],p=u._texture.baseTexture.alphaMode>0,h=u.alpha,f=h<1&&p?i.premultiplyTint(u._tintRGB,h):u._tintRGB+(255*h<<24);o[n]=f,o[n+a]=f,o[n+2*a]=f,o[n+3*a]=f,n+=4*a}},a.prototype.destroy=function(){e.prototype.destroy.call(this),this.shader&&(this.shader.destroy(),this.shader=null),this.tempMatrix=null},a.extension={name:"particle",type:r.ExtensionType.RendererPlugin},a}(r.ObjectRenderer);exports.ParticleContainer=s,exports.ParticleRenderer=p;
//# sourceMappingURL=particle-container.min.js.map
