// 简单的头像存储测试
console.log('开始测试头像存储...');

async function testStorage() {
  try {
    // 导入模块
    const { electronStoreManager } = await import('./cosyvoice-vue3/src/utils/electronStoreManager.ts');
    
    console.log('1. 测试保存头像...');
    
    // 模拟新的保存方式
    const testUrl = 'http://localhost:3001/avatars/test_avatar.png';
    const existingAvatars = await electronStoreManager.get('protagonist-avatars', {});
    console.log('当前avatars对象:', existingAvatars);
    
    existingAvatars['current'] = testUrl;
    await electronStoreManager.set('protagonist-avatars', existingAvatars);
    console.log('保存完成');
    
    console.log('2. 测试读取头像...');
    
    // 读取
    const avatars = await electronStoreManager.get('protagonist-avatars', {});
    const avatarData = avatars['current'];
    console.log('读取到的avatars对象:', avatars);
    console.log('头像数据:', avatarData);
    
    console.log('3. 测试完成');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testStorage();