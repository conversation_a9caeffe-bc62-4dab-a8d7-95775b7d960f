/*!
 * @pixi/filter-color-matrix - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/filter-color-matrix is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{},this.PIXI.filters=this.PIXI.filters||{};var _pixi_filter_color_matrix=function(t,r){"use strict";var o=function(t,r){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var o in r)r.hasOwnProperty(o)&&(t[o]=r[o])},o(t,r)};var n=function(t){function n(){var o=this,n={m:new Float32Array([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0]),uAlpha:1};return(o=t.call(this,r.defaultFilterVertex,"varying vec2 vTextureCoord;\nuniform sampler2D uSampler;\nuniform float m[20];\nuniform float uAlpha;\n\nvoid main(void)\n{\n    vec4 c = texture2D(uSampler, vTextureCoord);\n\n    if (uAlpha == 0.0) {\n        gl_FragColor = c;\n        return;\n    }\n\n    // Un-premultiply alpha before applying the color matrix. See issue #3539.\n    if (c.a > 0.0) {\n      c.rgb /= c.a;\n    }\n\n    vec4 result;\n\n    result.r = (m[0] * c.r);\n        result.r += (m[1] * c.g);\n        result.r += (m[2] * c.b);\n        result.r += (m[3] * c.a);\n        result.r += m[4];\n\n    result.g = (m[5] * c.r);\n        result.g += (m[6] * c.g);\n        result.g += (m[7] * c.b);\n        result.g += (m[8] * c.a);\n        result.g += m[9];\n\n    result.b = (m[10] * c.r);\n       result.b += (m[11] * c.g);\n       result.b += (m[12] * c.b);\n       result.b += (m[13] * c.a);\n       result.b += m[14];\n\n    result.a = (m[15] * c.r);\n       result.a += (m[16] * c.g);\n       result.a += (m[17] * c.b);\n       result.a += (m[18] * c.a);\n       result.a += m[19];\n\n    vec3 rgb = mix(c.rgb, result.rgb, uAlpha);\n\n    // Premultiply alpha again.\n    rgb *= result.a;\n\n    gl_FragColor = vec4(rgb, result.a);\n}\n",n)||this).alpha=1,o}return function(t,r){function n(){this.constructor=t}o(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}(n,t),n.prototype._loadMatrix=function(t,r){void 0===r&&(r=!1);var o=t;r&&(this._multiply(o,this.uniforms.m,t),o=this._colorMatrix(o)),this.uniforms.m=o},n.prototype._multiply=function(t,r,o){return t[0]=r[0]*o[0]+r[1]*o[5]+r[2]*o[10]+r[3]*o[15],t[1]=r[0]*o[1]+r[1]*o[6]+r[2]*o[11]+r[3]*o[16],t[2]=r[0]*o[2]+r[1]*o[7]+r[2]*o[12]+r[3]*o[17],t[3]=r[0]*o[3]+r[1]*o[8]+r[2]*o[13]+r[3]*o[18],t[4]=r[0]*o[4]+r[1]*o[9]+r[2]*o[14]+r[3]*o[19]+r[4],t[5]=r[5]*o[0]+r[6]*o[5]+r[7]*o[10]+r[8]*o[15],t[6]=r[5]*o[1]+r[6]*o[6]+r[7]*o[11]+r[8]*o[16],t[7]=r[5]*o[2]+r[6]*o[7]+r[7]*o[12]+r[8]*o[17],t[8]=r[5]*o[3]+r[6]*o[8]+r[7]*o[13]+r[8]*o[18],t[9]=r[5]*o[4]+r[6]*o[9]+r[7]*o[14]+r[8]*o[19]+r[9],t[10]=r[10]*o[0]+r[11]*o[5]+r[12]*o[10]+r[13]*o[15],t[11]=r[10]*o[1]+r[11]*o[6]+r[12]*o[11]+r[13]*o[16],t[12]=r[10]*o[2]+r[11]*o[7]+r[12]*o[12]+r[13]*o[17],t[13]=r[10]*o[3]+r[11]*o[8]+r[12]*o[13]+r[13]*o[18],t[14]=r[10]*o[4]+r[11]*o[9]+r[12]*o[14]+r[13]*o[19]+r[14],t[15]=r[15]*o[0]+r[16]*o[5]+r[17]*o[10]+r[18]*o[15],t[16]=r[15]*o[1]+r[16]*o[6]+r[17]*o[11]+r[18]*o[16],t[17]=r[15]*o[2]+r[16]*o[7]+r[17]*o[12]+r[18]*o[17],t[18]=r[15]*o[3]+r[16]*o[8]+r[17]*o[13]+r[18]*o[18],t[19]=r[15]*o[4]+r[16]*o[9]+r[17]*o[14]+r[18]*o[19]+r[19],t},n.prototype._colorMatrix=function(t){var r=new Float32Array(t);return r[4]/=255,r[9]/=255,r[14]/=255,r[19]/=255,r},n.prototype.brightness=function(t,r){var o=[t,0,0,0,0,0,t,0,0,0,0,0,t,0,0,0,0,0,1,0];this._loadMatrix(o,r)},n.prototype.tint=function(t,r){var o=[(t>>16&255)/255,0,0,0,0,0,(t>>8&255)/255,0,0,0,0,0,(255&t)/255,0,0,0,0,0,1,0];this._loadMatrix(o,r)},n.prototype.greyscale=function(t,r){var o=[t,t,t,0,0,t,t,t,0,0,t,t,t,0,0,0,0,0,1,0];this._loadMatrix(o,r)},n.prototype.blackAndWhite=function(t){this._loadMatrix([.3,.6,.1,0,0,.3,.6,.1,0,0,.3,.6,.1,0,0,0,0,0,1,0],t)},n.prototype.hue=function(t,r){t=(t||0)/180*Math.PI;var o=Math.cos(t),n=Math.sin(t),e=1/3,i=(0,Math.sqrt)(e),a=[o+(1-o)*e,e*(1-o)-i*n,e*(1-o)+i*n,0,0,e*(1-o)+i*n,o+e*(1-o),e*(1-o)-i*n,0,0,e*(1-o)-i*n,e*(1-o)+i*n,o+e*(1-o),0,0,0,0,0,1,0];this._loadMatrix(a,r)},n.prototype.contrast=function(t,r){var o=(t||0)+1,n=-.5*(o-1),e=[o,0,0,0,n,0,o,0,0,n,0,0,o,0,n,0,0,0,1,0];this._loadMatrix(e,r)},n.prototype.saturate=function(t,r){void 0===t&&(t=0);var o=2*t/3+1,n=-.5*(o-1),e=[o,n,n,0,0,n,o,n,0,0,n,n,o,0,0,0,0,0,1,0];this._loadMatrix(e,r)},n.prototype.desaturate=function(){this.saturate(-1)},n.prototype.negative=function(t){this._loadMatrix([-1,0,0,1,0,0,-1,0,1,0,0,0,-1,1,0,0,0,0,1,0],t)},n.prototype.sepia=function(t){this._loadMatrix([.393,.7689999,.18899999,0,0,.349,.6859999,.16799999,0,0,.272,.5339999,.13099999,0,0,0,0,0,1,0],t)},n.prototype.technicolor=function(t){this._loadMatrix([1.9125277891456083,-.8545344976951645,-.09155508482755585,0,11.793603434377337,-.3087833385928097,1.7658908555458428,-.10601743074722245,0,-70.35205161461398,-.231103377548616,-.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0],t)},n.prototype.polaroid=function(t){this._loadMatrix([1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],t)},n.prototype.toBGR=function(t){this._loadMatrix([0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0],t)},n.prototype.kodachrome=function(t){this._loadMatrix([1.1285582396593525,-.3967382283601348,-.03992559172921793,0,63.72958762196502,-.16404339962244616,1.0835251566291304,-.05498805115633132,0,24.732407896706203,-.16786010706155763,-.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0],t)},n.prototype.browni=function(t){this._loadMatrix([.5997023498159715,.34553243048391263,-.2708298674538042,0,47.43192855600873,-.037703249837783157,.8609577587992641,.15059552388459913,0,-36.96841498319127,.24113635128153335,-.07441037908422492,.44972182064877153,0,-7.562075277591283,0,0,0,1,0],t)},n.prototype.vintage=function(t){this._loadMatrix([.6279345635605994,.3202183420819367,-.03965408211312453,0,9.651285835294123,.02578397704808868,.6441188644374771,.03259127616149294,0,7.462829176470591,.0466055556782719,-.0851232987247891,.5241648018700465,0,5.159190588235296,0,0,0,1,0],t)},n.prototype.colorTone=function(t,r,o,n,e){var i=((o=o||16770432)>>16&255)/255,a=(o>>8&255)/255,l=(255&o)/255,u=((n=n||3375104)>>16&255)/255,s=(n>>8&255)/255,p=(255&n)/255,c=[.3,.59,.11,0,0,i,a,l,t=t||.2,0,u,s,p,r=r||.15,0,i-u,a-s,l-p,0,0];this._loadMatrix(c,e)},n.prototype.night=function(t,r){var o=[-2*(t=t||.1),-t,0,0,0,-t,0,t,0,0,0,t,2*t,0,0,0,0,0,1,0];this._loadMatrix(o,r)},n.prototype.predator=function(t,r){var o=[11.224130630493164*t,-4.794486999511719*t,-2.8746118545532227*t,0*t,.40342438220977783*t,-3.6330697536468506*t,9.193157196044922*t,-2.951810836791992*t,0*t,-1.316135048866272*t,-3.2184197902679443*t,-4.2375030517578125*t,7.476448059082031*t,0*t,.8044459223747253*t,0,0,0,1,0];this._loadMatrix(o,r)},n.prototype.lsd=function(t){this._loadMatrix([2,-.4,.5,0,0,-.5,2,-.4,0,0,-.4,-.5,3,0,0,0,0,0,1,0],t)},n.prototype.reset=function(){this._loadMatrix([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],!1)},Object.defineProperty(n.prototype,"matrix",{get:function(){return this.uniforms.m},set:function(t){this.uniforms.m=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"alpha",{get:function(){return this.uniforms.uAlpha},set:function(t){this.uniforms.uAlpha=t},enumerable:!1,configurable:!0}),n}(r.Filter);return n.prototype.grayscale=n.prototype.greyscale,t.ColorMatrixFilter=n,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI);Object.assign(this.PIXI.filters,_pixi_filter_color_matrix);
//# sourceMappingURL=filter-color-matrix.min.js.map
