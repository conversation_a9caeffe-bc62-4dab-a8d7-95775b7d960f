import { FlatOptions } from './types';
/**
 * Generates a flat `.pkg` installer for a packaged Electron `.app` bundle.
 * @returns A void Promise once the flattening operation is complete.
 *
 * @category Flat
 */
export declare function buildPkg(_opts: FlatOptions): Promise<void>;
/**
 * This function is exported with normal callback implementation.
 *
 * @deprecated Please use the Promise-based {@link flatAsync} method instead.
 * @category Flat
 */
export declare const flat: (opts: FlatOptions, cb?: ((error?: Error) => void) | undefined) => void;
