/*!
 * @pixi/text - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/text is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_text=function(t,e,i,n,r,o){"use strict";var s=function(t,e){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},s(t,e)};var a;t.TEXT_GRADIENT=void 0,(a=t.TEXT_GRADIENT||(t.TEXT_GRADIENT={}))[a.LINEAR_VERTICAL=0]="LINEAR_VERTICAL",a[a.LINEAR_HORIZONTAL=1]="LINEAR_HORIZONTAL";var h={align:"left",breakWords:!1,dropShadow:!1,dropShadowAlpha:1,dropShadowAngle:Math.PI/6,dropShadowBlur:0,dropShadowColor:"black",dropShadowDistance:5,fill:"black",fillGradientType:t.TEXT_GRADIENT.LINEAR_VERTICAL,fillGradientStops:[],fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",letterSpacing:0,lineHeight:0,lineJoin:"miter",miterLimit:10,padding:0,stroke:"black",strokeThickness:0,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100,leading:0},l=["serif","sans-serif","monospace","cursive","fantasy","system-ui"],c=function(){function t(t){this.styleID=0,this.reset(),p(this,t,t)}return t.prototype.clone=function(){var e={};return p(e,this,h),new t(e)},t.prototype.reset=function(){p(this,h,h)},Object.defineProperty(t.prototype,"align",{get:function(){return this._align},set:function(t){this._align!==t&&(this._align=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"breakWords",{get:function(){return this._breakWords},set:function(t){this._breakWords!==t&&(this._breakWords=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadow",{get:function(){return this._dropShadow},set:function(t){this._dropShadow!==t&&(this._dropShadow=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowAlpha",{get:function(){return this._dropShadowAlpha},set:function(t){this._dropShadowAlpha!==t&&(this._dropShadowAlpha=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowAngle",{get:function(){return this._dropShadowAngle},set:function(t){this._dropShadowAngle!==t&&(this._dropShadowAngle=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowBlur",{get:function(){return this._dropShadowBlur},set:function(t){this._dropShadowBlur!==t&&(this._dropShadowBlur=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowColor",{get:function(){return this._dropShadowColor},set:function(t){var e=f(t);this._dropShadowColor!==e&&(this._dropShadowColor=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dropShadowDistance",{get:function(){return this._dropShadowDistance},set:function(t){this._dropShadowDistance!==t&&(this._dropShadowDistance=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fill",{get:function(){return this._fill},set:function(t){var e=f(t);this._fill!==e&&(this._fill=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fillGradientType",{get:function(){return this._fillGradientType},set:function(t){this._fillGradientType!==t&&(this._fillGradientType=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fillGradientStops",{get:function(){return this._fillGradientStops},set:function(t){(function(t,e){if(!Array.isArray(t)||!Array.isArray(e))return!1;if(t.length!==e.length)return!1;for(var i=0;i<t.length;++i)if(t[i]!==e[i])return!1;return!0})(this._fillGradientStops,t)||(this._fillGradientStops=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontFamily",{get:function(){return this._fontFamily},set:function(t){this.fontFamily!==t&&(this._fontFamily=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontSize",{get:function(){return this._fontSize},set:function(t){this._fontSize!==t&&(this._fontSize=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontStyle",{get:function(){return this._fontStyle},set:function(t){this._fontStyle!==t&&(this._fontStyle=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontVariant",{get:function(){return this._fontVariant},set:function(t){this._fontVariant!==t&&(this._fontVariant=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontWeight",{get:function(){return this._fontWeight},set:function(t){this._fontWeight!==t&&(this._fontWeight=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"letterSpacing",{get:function(){return this._letterSpacing},set:function(t){this._letterSpacing!==t&&(this._letterSpacing=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lineHeight",{get:function(){return this._lineHeight},set:function(t){this._lineHeight!==t&&(this._lineHeight=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"leading",{get:function(){return this._leading},set:function(t){this._leading!==t&&(this._leading=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lineJoin",{get:function(){return this._lineJoin},set:function(t){this._lineJoin!==t&&(this._lineJoin=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"miterLimit",{get:function(){return this._miterLimit},set:function(t){this._miterLimit!==t&&(this._miterLimit=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"padding",{get:function(){return this._padding},set:function(t){this._padding!==t&&(this._padding=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"stroke",{get:function(){return this._stroke},set:function(t){var e=f(t);this._stroke!==e&&(this._stroke=e,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"strokeThickness",{get:function(){return this._strokeThickness},set:function(t){this._strokeThickness!==t&&(this._strokeThickness=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"textBaseline",{get:function(){return this._textBaseline},set:function(t){this._textBaseline!==t&&(this._textBaseline=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"trim",{get:function(){return this._trim},set:function(t){this._trim!==t&&(this._trim=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"whiteSpace",{get:function(){return this._whiteSpace},set:function(t){this._whiteSpace!==t&&(this._whiteSpace=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"wordWrap",{get:function(){return this._wordWrap},set:function(t){this._wordWrap!==t&&(this._wordWrap=t,this.styleID++)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"wordWrapWidth",{get:function(){return this._wordWrapWidth},set:function(t){this._wordWrapWidth!==t&&(this._wordWrapWidth=t,this.styleID++)},enumerable:!1,configurable:!0}),t.prototype.toFontString=function(){var t="number"==typeof this.fontSize?this.fontSize+"px":this.fontSize,e=this.fontFamily;Array.isArray(this.fontFamily)||(e=this.fontFamily.split(","));for(var i=e.length-1;i>=0;i--){var n=e[i].trim();!/([\"\'])[^\'\"]+\1/.test(n)&&l.indexOf(n)<0&&(n='"'+n+'"'),e[i]=n}return this.fontStyle+" "+this.fontVariant+" "+this.fontWeight+" "+t+" "+e.join(",")},t}();function u(t){return"number"==typeof t?o.hex2string(t):("string"==typeof t&&0===t.indexOf("0x")&&(t=t.replace("0x","#")),t)}function f(t){if(Array.isArray(t)){for(var e=0;e<t.length;++e)t[e]=u(t[e]);return t}return u(t)}function p(t,e,i){for(var n in i)Array.isArray(e[n])?t[n]=e[n].slice():t[n]=e[n]}var d={willReadFrequently:!0},g=function(){function t(t,e,i,n,r,o,s,a,h){this.text=t,this.style=e,this.width=i,this.height=n,this.lines=r,this.lineWidths=o,this.lineHeight=s,this.maxLineWidth=a,this.fontProperties=h}return t.measureText=function(e,i,n,r){void 0===r&&(r=t._canvas),n=null==n?i.wordWrap:n;var o=i.toFontString(),s=t.measureFont(o);0===s.fontSize&&(s.fontSize=i.fontSize,s.ascent=i.fontSize);var a=r.getContext("2d",d);a.font=o;for(var h=(n?t.wordWrap(e,i,r):e).split(/(?:\r\n|\r|\n)/),l=new Array(h.length),c=0,u=0;u<h.length;u++){var f=a.measureText(h[u]).width+(h[u].length-1)*i.letterSpacing;l[u]=f,c=Math.max(c,f)}var p=c+i.strokeThickness;i.dropShadow&&(p+=i.dropShadowDistance);var g=i.lineHeight||s.fontSize+i.strokeThickness,y=Math.max(g,s.fontSize+i.strokeThickness)+(h.length-1)*(g+i.leading);return i.dropShadow&&(y+=i.dropShadowDistance),new t(e,i,p,y,h,l,g+i.leading,c,s)},t.wordWrap=function(e,i,n){void 0===n&&(n=t._canvas);for(var r=n.getContext("2d",d),o=0,s="",a="",h=Object.create(null),l=i.letterSpacing,c=i.whiteSpace,u=t.collapseSpaces(c),f=t.collapseNewlines(c),p=!u,g=i.wordWrapWidth+l,y=t.tokenize(e),_=0;_<y.length;_++){var b=y[_];if(t.isNewline(b)){if(!f){a+=t.addLine(s),p=!u,s="",o=0;continue}b=" "}if(u){var S=t.isBreakingSpace(b),m=t.isBreakingSpace(s[s.length-1]);if(S&&m)continue}var w=t.getFromCache(b,l,h,r);if(w>g)if(""!==s&&(a+=t.addLine(s),s="",o=0),t.canBreakWords(b,i.breakWords))for(var v=t.wordWrapSplit(b),x=0;x<v.length;x++){for(var I=v[x],T=1;v[x+T];){var k=v[x+T],O=I[I.length-1];if(t.canBreakChars(O,k,b,x,i.breakWords))break;I+=k,T++}x+=I.length-1;var A=t.getFromCache(I,l,h,r);A+o>g&&(a+=t.addLine(s),p=!1,s="",o=0),s+=I,o+=A}else{s.length>0&&(a+=t.addLine(s),s="",o=0);var L=_===y.length-1;a+=t.addLine(b,!L),p=!1,s="",o=0}else w+o>g&&(p=!1,a+=t.addLine(s),s="",o=0),(s.length>0||!t.isBreakingSpace(b)||p)&&(s+=b,o+=w)}return a+=t.addLine(s,!1)},t.addLine=function(e,i){return void 0===i&&(i=!0),e=t.trimRight(e),e=i?e+"\n":e},t.getFromCache=function(t,e,i,n){var r=i[t];if("number"!=typeof r){var o=t.length*e;r=n.measureText(t).width+o,i[t]=r}return r},t.collapseSpaces=function(t){return"normal"===t||"pre-line"===t},t.collapseNewlines=function(t){return"normal"===t},t.trimRight=function(e){if("string"!=typeof e)return"";for(var i=e.length-1;i>=0;i--){var n=e[i];if(!t.isBreakingSpace(n))break;e=e.slice(0,-1)}return e},t.isNewline=function(e){return"string"==typeof e&&t._newlines.indexOf(e.charCodeAt(0))>=0},t.isBreakingSpace=function(e,i){return"string"==typeof e&&t._breakingSpaces.indexOf(e.charCodeAt(0))>=0},t.tokenize=function(e){var i=[],n="";if("string"!=typeof e)return i;for(var r=0;r<e.length;r++){var o=e[r],s=e[r+1];t.isBreakingSpace(o,s)||t.isNewline(o)?(""!==n&&(i.push(n),n=""),i.push(o)):n+=o}return""!==n&&i.push(n),i},t.canBreakWords=function(t,e){return e},t.canBreakChars=function(t,e,i,n,r){return!0},t.wordWrapSplit=function(t){return t.split("")},t.measureFont=function(e){if(t._fonts[e])return t._fonts[e];var i={ascent:0,descent:0,fontSize:0},n=t._canvas,r=t._context;r.font=e;var o=t.METRICS_STRING+t.BASELINE_SYMBOL,s=Math.ceil(r.measureText(o).width),a=Math.ceil(r.measureText(t.BASELINE_SYMBOL).width),h=Math.ceil(t.HEIGHT_MULTIPLIER*a);a=a*t.BASELINE_MULTIPLIER|0,n.width=s,n.height=h,r.fillStyle="#f00",r.fillRect(0,0,s,h),r.font=e,r.textBaseline="alphabetic",r.fillStyle="#000",r.fillText(o,0,a);var l=r.getImageData(0,0,s,h).data,c=l.length,u=4*s,f=0,p=0,d=!1;for(f=0;f<a;++f){for(var g=0;g<u;g+=4)if(255!==l[p+g]){d=!0;break}if(d)break;p+=u}for(i.ascent=a-f,p=c-u,d=!1,f=h;f>a;--f){for(g=0;g<u;g+=4)if(255!==l[p+g]){d=!0;break}if(d)break;p-=u}return i.descent=f-a,i.fontSize=i.ascent+i.descent,t._fonts[e]=i,i},t.clearMetrics=function(e){void 0===e&&(e=""),e?delete t._fonts[e]:t._fonts={}},Object.defineProperty(t,"_canvas",{get:function(){if(!t.__canvas){var e=void 0;try{var i=new OffscreenCanvas(0,0),r=i.getContext("2d",d);if(r&&r.measureText)return t.__canvas=i,i;e=n.settings.ADAPTER.createCanvas()}catch(t){e=n.settings.ADAPTER.createCanvas()}e.width=e.height=10,t.__canvas=e}return t.__canvas},enumerable:!1,configurable:!0}),Object.defineProperty(t,"_context",{get:function(){return t.__context||(t.__context=t._canvas.getContext("2d",d)),t.__context},enumerable:!1,configurable:!0}),t}();g._fonts={},g.METRICS_STRING="|ÉqÅ",g.BASELINE_SYMBOL="M",g.BASELINE_MULTIPLIER=1.4,g.HEIGHT_MULTIPLIER=2,g._newlines=[10,13],g._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];var y={texture:!0,children:!1,baseTexture:!0},_=function(e){function a(t,o,s){var a=this,h=!1;s||(s=n.settings.ADAPTER.createCanvas(),h=!0),s.width=3,s.height=3;var l=i.Texture.from(s);return l.orig=new r.Rectangle,l.trim=new r.Rectangle,(a=e.call(this,l)||this)._ownCanvas=h,a.canvas=s,a.context=s.getContext("2d",{willReadFrequently:!0}),a._resolution=n.settings.RESOLUTION,a._autoResolution=!0,a._text=null,a._style=null,a._styleListener=null,a._font="",a.text=t,a.style=o,a.localStyleID=-1,a}return function(t,e){function i(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(a,e),a.prototype.updateText=function(t){var e=this._style;if(this.localStyleID!==e.styleID&&(this.dirty=!0,this.localStyleID=e.styleID),this.dirty||!t){this._font=this._style.toFontString();var i,n,r=this.context,s=g.measureText(this._text||" ",this._style,this._style.wordWrap,this.canvas),h=s.width,l=s.height,c=s.lines,u=s.lineHeight,f=s.lineWidths,p=s.maxLineWidth,d=s.fontProperties;this.canvas.width=Math.ceil(Math.ceil(Math.max(1,h)+2*e.padding)*this._resolution),this.canvas.height=Math.ceil(Math.ceil(Math.max(1,l)+2*e.padding)*this._resolution),r.scale(this._resolution,this._resolution),r.clearRect(0,0,this.canvas.width,this.canvas.height),r.font=this._font,r.lineWidth=e.strokeThickness,r.textBaseline=e.textBaseline,r.lineJoin=e.lineJoin,r.miterLimit=e.miterLimit;for(var y=e.dropShadow?2:1,_=0;_<y;++_){var b=e.dropShadow&&0===_,S=b?Math.ceil(Math.max(1,l)+2*e.padding):0,m=S*this._resolution;if(b){r.fillStyle="black",r.strokeStyle="black";var w=e.dropShadowColor,v=o.hex2rgb("number"==typeof w?w:o.string2hex(w)),x=e.dropShadowBlur*this._resolution,I=e.dropShadowDistance*this._resolution;r.shadowColor="rgba("+255*v[0]+","+255*v[1]+","+255*v[2]+","+e.dropShadowAlpha+")",r.shadowBlur=x,r.shadowOffsetX=Math.cos(e.dropShadowAngle)*I,r.shadowOffsetY=Math.sin(e.dropShadowAngle)*I+m}else r.fillStyle=this._generateFillStyle(e,c,s),r.strokeStyle=e.stroke,r.shadowColor="black",r.shadowBlur=0,r.shadowOffsetX=0,r.shadowOffsetY=0;var T=(u-d.fontSize)/2;(!a.nextLineHeightBehavior||u-d.fontSize<0)&&(T=0);for(var k=0;k<c.length;k++)i=e.strokeThickness/2,n=e.strokeThickness/2+k*u+d.ascent+T,"right"===e.align?i+=p-f[k]:"center"===e.align&&(i+=(p-f[k])/2),e.stroke&&e.strokeThickness&&this.drawLetterSpacing(c[k],i+e.padding,n+e.padding-S,!0),e.fill&&this.drawLetterSpacing(c[k],i+e.padding,n+e.padding-S)}this.updateTexture()}},a.prototype.drawLetterSpacing=function(t,e,i,n){void 0===n&&(n=!1);var r=this._style.letterSpacing,o=a.experimentalLetterSpacing&&("letterSpacing"in CanvasRenderingContext2D.prototype||"textLetterSpacing"in CanvasRenderingContext2D.prototype);if(0===r||o)return o&&(this.context.letterSpacing=r,this.context.textLetterSpacing=r),void(n?this.context.strokeText(t,e,i):this.context.fillText(t,e,i));for(var s=e,h=Array.from?Array.from(t):t.split(""),l=this.context.measureText(t).width,c=0,u=0;u<h.length;++u){var f=h[u];n?this.context.strokeText(f,s,i):this.context.fillText(f,s,i);for(var p="",d=u+1;d<h.length;++d)p+=h[d];s+=l-(c=this.context.measureText(p).width)+r,l=c}},a.prototype.updateTexture=function(){var t=this.canvas;if(this._style.trim){var e=o.trimCanvas(t);e.data&&(t.width=e.width,t.height=e.height,this.context.putImageData(e.data,0,0))}var i=this._texture,n=this._style,r=n.trim?0:n.padding,s=i.baseTexture;i.trim.width=i._frame.width=t.width/this._resolution,i.trim.height=i._frame.height=t.height/this._resolution,i.trim.x=-r,i.trim.y=-r,i.orig.width=i._frame.width-2*r,i.orig.height=i._frame.height-2*r,this._onTextureUpdate(),s.setRealSize(t.width,t.height,this._resolution),i.updateUvs(),this.dirty=!1},a.prototype._render=function(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0),this.updateText(!0),e.prototype._render.call(this,t)},a.prototype.updateTransform=function(){this.updateText(!0),e.prototype.updateTransform.call(this)},a.prototype.getBounds=function(t,i){return this.updateText(!0),-1===this._textureID&&(t=!1),e.prototype.getBounds.call(this,t,i)},a.prototype.getLocalBounds=function(t){return this.updateText(!0),e.prototype.getLocalBounds.call(this,t)},a.prototype._calculateBounds=function(){this.calculateVertices(),this._bounds.addQuad(this.vertexData)},a.prototype._generateFillStyle=function(e,i,n){var r,o=e.fill;if(!Array.isArray(o))return o;if(1===o.length)return o[0];var s=e.dropShadow?e.dropShadowDistance:0,a=e.padding||0,h=this.canvas.width/this._resolution-s-2*a,l=this.canvas.height/this._resolution-s-2*a,c=o.slice(),u=e.fillGradientStops.slice();if(!u.length)for(var f=c.length+1,p=1;p<f;++p)u.push(p/f);if(c.unshift(o[0]),u.unshift(0),c.push(o[o.length-1]),u.push(1),e.fillGradientType===t.TEXT_GRADIENT.LINEAR_VERTICAL){r=this.context.createLinearGradient(h/2,a,h/2,l+a);var d=n.fontProperties.fontSize+e.strokeThickness;for(p=0;p<i.length;p++){var g=n.lineHeight*(p-1)+d,y=n.lineHeight*p,_=y;p>0&&g>y&&(_=(y+g)/2);var b=y+d,S=n.lineHeight*(p+1),m=b;p+1<i.length&&S<b&&(m=(b+S)/2);for(var w=(m-_)/l,v=0;v<c.length;v++){var x=0;x="number"==typeof u[v]?u[v]:v/c.length;var I=Math.min(1,Math.max(0,_/l+x*w));I=Number(I.toFixed(5)),r.addColorStop(I,c[v])}}}else{r=this.context.createLinearGradient(a,l/2,h+a,l/2);var T=c.length+1,k=1;for(p=0;p<c.length;p++){var O=void 0;O="number"==typeof u[p]?u[p]:k/T,r.addColorStop(O,c[p]),k++}}return r},a.prototype.destroy=function(t){"boolean"==typeof t&&(t={children:t}),t=Object.assign({},y,t),e.prototype.destroy.call(this,t),this._ownCanvas&&(this.canvas.height=this.canvas.width=0),this.context=null,this.canvas=null,this._style=null},Object.defineProperty(a.prototype,"width",{get:function(){return this.updateText(!0),Math.abs(this.scale.x)*this._texture.orig.width},set:function(t){this.updateText(!0);var e=o.sign(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"height",{get:function(){return this.updateText(!0),Math.abs(this.scale.y)*this._texture.orig.height},set:function(t){this.updateText(!0);var e=o.sign(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"style",{get:function(){return this._style},set:function(t){t=t||{},this._style=t instanceof c?t:new c(t),this.localStyleID=-1,this.dirty=!0},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"text",{get:function(){return this._text},set:function(t){t=String(null==t?"":t),this._text!==t&&(this._text=t,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"resolution",{get:function(){return this._resolution},set:function(t){this._autoResolution=!1,this._resolution!==t&&(this._resolution=t,this.dirty=!0)},enumerable:!1,configurable:!0}),a.nextLineHeightBehavior=!1,a.experimentalLetterSpacing=!1,a}(e.Sprite);return t.Text=_,t.TextMetrics=g,t.TextStyle=c,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI,PIXI,PIXI,PIXI.utils);Object.assign(this.PIXI,_pixi_text);
//# sourceMappingURL=text.min.js.map
