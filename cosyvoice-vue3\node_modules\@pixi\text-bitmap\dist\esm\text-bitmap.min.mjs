/*!
 * @pixi/text-bitmap - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/text-bitmap is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{Rectangle as e,Point as t,ObservablePoint as r}from"@pixi/math";import{settings as i}from"@pixi/settings";import{MeshGeometry as n,MeshMaterial as a,Mesh as o}from"@pixi/mesh";import{hex2rgb as s,string2hex as h,getResolutionOfUrl as l,removeItems as u}from"@pixi/utils";import{BaseTexture as f,Texture as c,Program as p,ExtensionType as d}from"@pixi/core";import{TEXT_GRADIENT as g,TextStyle as m,TextMetrics as v}from"@pixi/text";import{ALPHA_MODES as x,MIPMAP_MODES as y,BLEND_MODES as _}from"@pixi/constants";import{Container as b}from"@pixi/display";import{LoaderResource as w}from"@pixi/loaders";var T=function(e,t){return T=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},T(e,t)};var A=function(){this.info=[],this.common=[],this.page=[],this.char=[],this.kerning=[],this.distanceField=[]},S=function(){function e(){}return e.test=function(e){return"string"==typeof e&&0===e.indexOf("info face=")},e.parse=function(e){var t=e.match(/^[a-z]+\s+.+$/gm),r={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(var i in t){var n=t[i].match(/^[a-z]+/gm)[0],a=t[i].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),o={};for(var s in a){var h=a[s].split("="),l=h[0],u=h[1].replace(/"/gm,""),f=parseFloat(u),c=isNaN(f)?u:f;o[l]=c}r[n].push(o)}var p=new A;return r.info.forEach((function(e){return p.info.push({face:e.face,size:parseInt(e.size,10)})})),r.common.forEach((function(e){return p.common.push({lineHeight:parseInt(e.lineHeight,10)})})),r.page.forEach((function(e){return p.page.push({id:parseInt(e.id,10),file:e.file})})),r.char.forEach((function(e){return p.char.push({id:parseInt(e.id,10),page:parseInt(e.page,10),x:parseInt(e.x,10),y:parseInt(e.y,10),width:parseInt(e.width,10),height:parseInt(e.height,10),xoffset:parseInt(e.xoffset,10),yoffset:parseInt(e.yoffset,10),xadvance:parseInt(e.xadvance,10)})})),r.kerning.forEach((function(e){return p.kerning.push({first:parseInt(e.first,10),second:parseInt(e.second,10),amount:parseInt(e.amount,10)})})),r.distanceField.forEach((function(e){return p.distanceField.push({distanceRange:parseInt(e.distanceRange,10),fieldType:e.fieldType})})),p},e}(),P=function(){function e(){}return e.test=function(e){return e instanceof XMLDocument&&e.getElementsByTagName("page").length&&null!==e.getElementsByTagName("info")[0].getAttribute("face")},e.parse=function(e){for(var t=new A,r=e.getElementsByTagName("info"),i=e.getElementsByTagName("common"),n=e.getElementsByTagName("page"),a=e.getElementsByTagName("char"),o=e.getElementsByTagName("kerning"),s=e.getElementsByTagName("distanceField"),h=0;h<r.length;h++)t.info.push({face:r[h].getAttribute("face"),size:parseInt(r[h].getAttribute("size"),10)});for(h=0;h<i.length;h++)t.common.push({lineHeight:parseInt(i[h].getAttribute("lineHeight"),10)});for(h=0;h<n.length;h++)t.page.push({id:parseInt(n[h].getAttribute("id"),10)||0,file:n[h].getAttribute("file")});for(h=0;h<a.length;h++){var l=a[h];t.char.push({id:parseInt(l.getAttribute("id"),10),page:parseInt(l.getAttribute("page"),10)||0,x:parseInt(l.getAttribute("x"),10),y:parseInt(l.getAttribute("y"),10),width:parseInt(l.getAttribute("width"),10),height:parseInt(l.getAttribute("height"),10),xoffset:parseInt(l.getAttribute("xoffset"),10),yoffset:parseInt(l.getAttribute("yoffset"),10),xadvance:parseInt(l.getAttribute("xadvance"),10)})}for(h=0;h<o.length;h++)t.kerning.push({first:parseInt(o[h].getAttribute("first"),10),second:parseInt(o[h].getAttribute("second"),10),amount:parseInt(o[h].getAttribute("amount"),10)});for(h=0;h<s.length;h++)t.distanceField.push({fieldType:s[h].getAttribute("fieldType"),distanceRange:parseInt(s[h].getAttribute("distanceRange"),10)});return t},e}(),M=function(){function e(){}return e.test=function(e){if("string"==typeof e&&e.indexOf("<font>")>-1){var t=(new globalThis.DOMParser).parseFromString(e,"text/xml");return P.test(t)}return!1},e.parse=function(e){var t=(new globalThis.DOMParser).parseFromString(e,"text/xml");return P.parse(t)},e}(),C=[S,P,M];function F(e){for(var t=0;t<C.length;t++)if(C[t].test(e))return C[t];return null}function E(e,t,r,i,n,a,o){var l=r.text,u=r.fontProperties;t.translate(i,n),t.scale(a,a);var f=o.strokeThickness/2,c=-o.strokeThickness/2;if(t.font=o.toFontString(),t.lineWidth=o.strokeThickness,t.textBaseline=o.textBaseline,t.lineJoin=o.lineJoin,t.miterLimit=o.miterLimit,t.fillStyle=function(e,t,r,i,n,a){var o,s=r.fill;if(!Array.isArray(s))return s;if(1===s.length)return s[0];var h=r.dropShadow?r.dropShadowDistance:0,l=r.padding||0,u=e.width/i-h-2*l,f=e.height/i-h-2*l,c=s.slice(),p=r.fillGradientStops.slice();if(!p.length)for(var d=c.length+1,m=1;m<d;++m)p.push(m/d);if(c.unshift(s[0]),p.unshift(0),c.push(s[s.length-1]),p.push(1),r.fillGradientType===g.LINEAR_VERTICAL){o=t.createLinearGradient(u/2,l,u/2,f+l);var v=0,x=(a.fontProperties.fontSize+r.strokeThickness)/f;for(m=0;m<n.length;m++)for(var y=a.lineHeight*m,_=0;_<c.length;_++){var b=y/f+("number"==typeof p[_]?p[_]:_/c.length)*x,w=Math.max(v,b);w=Math.min(w,1),o.addColorStop(w,c[_]),v=w}}else{o=t.createLinearGradient(l,f/2,u+l,f/2);var T=c.length+1,A=1;for(m=0;m<c.length;m++){var S=void 0;S="number"==typeof p[m]?p[m]:A/T,o.addColorStop(S,c[m]),A++}}return o}(e,t,o,a,[l],r),t.strokeStyle=o.stroke,o.dropShadow){var p=o.dropShadowColor,d=s("number"==typeof p?p:h(p)),m=o.dropShadowBlur*a,v=o.dropShadowDistance*a;t.shadowColor="rgba("+255*d[0]+","+255*d[1]+","+255*d[2]+","+o.dropShadowAlpha+")",t.shadowBlur=m,t.shadowOffsetX=Math.cos(o.dropShadowAngle)*v,t.shadowOffsetY=Math.sin(o.dropShadowAngle)*v}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0;o.stroke&&o.strokeThickness&&t.strokeText(l,f,c+r.lineHeight-u.descent),o.fill&&t.fillText(l,f,c+r.lineHeight-u.descent),t.setTransform(1,0,0,1,0,0),t.fillStyle="rgba(0, 0, 0, 0)"}function O(e){return Array.from?Array.from(e):e.split("")}function I(e){return e.codePointAt?e.codePointAt(0):e.charCodeAt(0)}var k=function(){function t(t,r,i){var n,a,o=t.info[0],s=t.common[0],h=t.page[0],u=t.distanceField[0],f=l(h.file),p={};this._ownsTextures=i,this.font=o.face,this.size=o.size,this.lineHeight=s.lineHeight/f,this.chars={},this.pageTextures=p;for(var d=0;d<t.page.length;d++){var g=t.page[d],m=g.id,v=g.file;p[m]=r instanceof Array?r[d]:r[v],(null==u?void 0:u.fieldType)&&"none"!==u.fieldType&&(p[m].baseTexture.alphaMode=x.NO_PREMULTIPLIED_ALPHA,p[m].baseTexture.mipmap=y.OFF)}for(d=0;d<t.char.length;d++){var _=t.char[d],b=(m=_.id,_.page),w=t.char[d],T=w.x,A=w.y,S=w.width,P=w.height,M=w.xoffset,C=w.yoffset,F=w.xadvance;A/=f,S/=f,P/=f,M/=f,C/=f,F/=f;var E=new e((T/=f)+p[b].frame.x/f,A+p[b].frame.y/f,S,P);this.chars[m]={xOffset:M,yOffset:C,xAdvance:F,kerning:{},texture:new c(p[b].baseTexture,E),page:b}}for(d=0;d<t.kerning.length;d++){var O=t.kerning[d],I=O.first,k=O.second,N=O.amount;I/=f,k/=f,N/=f,this.chars[k]&&(this.chars[k].kerning[I]=N)}this.distanceFieldRange=null==u?void 0:u.distanceRange,this.distanceFieldType=null!==(a=null===(n=null==u?void 0:u.fieldType)||void 0===n?void 0:n.toLowerCase())&&void 0!==a?a:"none"}return t.prototype.destroy=function(){for(var e in this.chars)this.chars[e].texture.destroy(),this.chars[e].texture=null;for(var e in this.pageTextures)this._ownsTextures&&this.pageTextures[e].destroy(!0),this.pageTextures[e]=null;this.chars=null,this.pageTextures=null},t.install=function(e,r,i){var n;if(e instanceof A)n=e;else{var a=F(e);if(!a)throw new Error("Unrecognized data format for font.");n=a.parse(e)}r instanceof c&&(r=[r]);var o=new t(n,r,i);return t.available[o.font]=o,o},t.uninstall=function(e){var r=t.available[e];if(!r)throw new Error("No font found named '"+e+"'");r.destroy(),delete t.available[e]},t.from=function(e,r,n){if(!e)throw new Error("[BitmapFont] Property `name` is required.");var a=Object.assign({},t.defaultOptions,n),o=a.chars,s=a.padding,h=a.resolution,l=a.textureWidth,u=a.textureHeight,p=function(e){"string"==typeof e&&(e=[e]);for(var t=[],r=0,i=e.length;r<i;r++){var n=e[r];if(Array.isArray(n)){if(2!==n.length)throw new Error("[BitmapFont]: Invalid character range length, expecting 2 got "+n.length+".");var a=n[0].charCodeAt(0),o=n[1].charCodeAt(0);if(o<a)throw new Error("[BitmapFont]: Invalid character range.");for(var s=a,h=o;s<=h;s++)t.push(String.fromCharCode(s))}else t.push.apply(t,O(n))}if(0===t.length)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}(o),d=r instanceof m?r:new m(r),g=l,x=new A;x.info[0]={face:d.fontFamily,size:d.fontSize},x.common[0]={lineHeight:d.fontSize};for(var y,_,b,w=0,T=0,S=0,P=[],M=0;M<p.length;M++){y||((y=i.ADAPTER.createCanvas()).width=l,y.height=u,_=y.getContext("2d"),b=new f(y,{resolution:h}),P.push(new c(b)),x.page.push({id:P.length-1,file:""}));var C=p[M],F=v.measureText(C,d,!1,y),k=F.width,N=Math.ceil(F.height),z=Math.ceil(("italic"===d.fontStyle?2:1)*k);if(T>=u-N*h){if(0===T)throw new Error("[BitmapFont] textureHeight "+u+"px is too small (fontFamily: '"+d.fontFamily+"', fontSize: "+d.fontSize+"px, char: '"+C+"')");--M,y=null,_=null,b=null,T=0,w=0,S=0}else if(S=Math.max(N+F.fontProperties.descent,S),z*h+w>=g){if(0===w)throw new Error("[BitmapFont] textureWidth "+l+"px is too small (fontFamily: '"+d.fontFamily+"', fontSize: "+d.fontSize+"px, char: '"+C+"')");--M,T+=S*h,T=Math.ceil(T),w=0,S=0}else{E(y,_,F,w,T,h,d);var D=I(F.text);x.char.push({id:D,page:P.length-1,x:w/h,y:T/h,width:z,height:N,xoffset:0,yoffset:0,xadvance:Math.ceil(k-(d.dropShadow?d.dropShadowDistance:0)-(d.stroke?d.strokeThickness:0))}),w+=(z+2*s)*h,w=Math.ceil(w)}}if(!(null==n?void 0:n.skipKerning)){M=0;for(var H=p.length;M<H;M++)for(var B=p[M],L=0;L<H;L++){var R=p[L],j=_.measureText(B).width,W=_.measureText(R).width,U=_.measureText(B+R).width-(j+W);U&&x.kerning.push({first:I(B),second:I(R),amount:U})}}var Y=new t(x,P,!0);return void 0!==t.available[e]&&t.uninstall(e),t.available[e]=Y,Y},t.ALPHA=[["a","z"],["A","Z"]," "],t.NUMERIC=[["0","9"]],t.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],t.ASCII=[[" ","~"]],t.defaultOptions={resolution:1,textureWidth:512,textureHeight:512,padding:4,chars:t.ALPHANUMERIC},t.available={},t}(),N=[],z=[],D=[],H=function(e){function s(t,n){void 0===n&&(n={});var a=e.call(this)||this;a._tint=16777215;var o=Object.assign({},s.styleDefaults,n),h=o.align,l=o.tint,u=o.maxWidth,f=o.letterSpacing,c=o.fontName,p=o.fontSize;if(!k.available[c])throw new Error('Missing BitmapFont "'+c+'"');return a._activePagesMeshData=[],a._textWidth=0,a._textHeight=0,a._align=h,a._tint=l,a._font=void 0,a._fontName=c,a._fontSize=p,a.text=t,a._maxWidth=u,a._maxLineHeight=0,a._letterSpacing=f,a._anchor=new r((function(){a.dirty=!0}),a,0,0),a._roundPixels=i.ROUND_PIXELS,a.dirty=!0,a._resolution=i.RESOLUTION,a._autoResolution=!0,a._textureCache={},a}return function(e,t){function r(){this.constructor=e}T(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(s,e),s.prototype.updateText=function(){for(var e,r=k.available[this._fontName],i=this.fontSize,s=i/r.size,h=new t,l=[],f=[],d=[],g=O(this._text.replace(/(?:\r\n|\r)/g,"\n")||" "),m=this._maxWidth*r.size/i,v="none"===r.distanceFieldType?N:z,x=null,y=0,b=0,w=0,T=-1,A=0,S=0,P=0,M=0,C=0;C<g.length;C++){var F=I(te=g[C]);if(/(?:\s)/.test(te)&&(T=C,A=y,M++),"\r"!==te&&"\n"!==te){var E=r.chars[F];if(E){x&&E.kerning[x]&&(h.x+=E.kerning[x]);var H=D.pop()||{texture:c.EMPTY,line:0,charCode:0,prevSpaces:0,position:new t};H.texture=E.texture,H.line=w,H.charCode=F,H.position.x=h.x+E.xOffset+this._letterSpacing/2,H.position.y=h.y+E.yOffset,H.prevSpaces=M,l.push(H),y=H.position.x+Math.max(E.xAdvance-E.xOffset,E.texture.orig.width),h.x+=E.xAdvance+this._letterSpacing,P=Math.max(P,E.yOffset+E.texture.height),x=F,-1!==T&&m>0&&h.x>m&&(++S,u(l,1+T-S,1+C-T),C=T,T=-1,f.push(A),d.push(l.length>0?l[l.length-1].prevSpaces:0),b=Math.max(b,A),w++,h.x=0,h.y+=r.lineHeight,x=null,M=0)}}else f.push(y),d.push(-1),b=Math.max(b,y),++w,++S,h.x=0,h.y+=r.lineHeight,x=null,M=0}var B=g[g.length-1];"\r"!==B&&"\n"!==B&&(/(?:\s)/.test(B)&&(y=A),f.push(y),b=Math.max(b,y),d.push(-1));var L=[];for(C=0;C<=w;C++){var R=0;"right"===this._align?R=b-f[C]:"center"===this._align?R=(b-f[C])/2:"justify"===this._align&&(R=d[C]<0?0:(b-f[C])/d[C]),L.push(R)}var j=l.length,W={},U=[],Y=this._activePagesMeshData;v.push.apply(v,Y);for(C=0;C<j;C++){var X=(ie=l[C].texture).baseTexture.uid;if(!W[X]){if(!(ue=v.pop())){var G=new n,V=void 0,Z=void 0;"none"===r.distanceFieldType?(V=new a(c.EMPTY),Z=_.NORMAL):(V=new a(c.EMPTY,{program:p.from("// Mesh material default fragment\r\nattribute vec2 aVertexPosition;\r\nattribute vec2 aTextureCoord;\r\n\r\nuniform mat3 projectionMatrix;\r\nuniform mat3 translationMatrix;\r\nuniform mat3 uTextureMatrix;\r\n\r\nvarying vec2 vTextureCoord;\r\n\r\nvoid main(void)\r\n{\r\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\r\n\r\n    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;\r\n}\r\n","// Pixi texture info\r\nvarying vec2 vTextureCoord;\r\nuniform sampler2D uSampler;\r\n\r\n// Tint\r\nuniform vec4 uColor;\r\n\r\n// on 2D applications fwidth is screenScale / glyphAtlasScale * distanceFieldRange\r\nuniform float uFWidth;\r\n\r\nvoid main(void) {\r\n\r\n  // To stack MSDF and SDF we need a non-pre-multiplied-alpha texture.\r\n  vec4 texColor = texture2D(uSampler, vTextureCoord);\r\n\r\n  // MSDF\r\n  float median = texColor.r + texColor.g + texColor.b -\r\n                  min(texColor.r, min(texColor.g, texColor.b)) -\r\n                  max(texColor.r, max(texColor.g, texColor.b));\r\n  // SDF\r\n  median = min(median, texColor.a);\r\n\r\n  float screenPxDistance = uFWidth * (median - 0.5);\r\n  float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\r\n  if (median < 0.01) {\r\n    alpha = 0.0;\r\n  } else if (median > 0.99) {\r\n    alpha = 1.0;\r\n  }\r\n\r\n  // NPM Textures, NPM outputs\r\n  gl_FragColor = vec4(uColor.rgb, uColor.a * alpha);\r\n\r\n}\r\n"),uniforms:{uFWidth:0}}),Z=_.NORMAL_NPM);var q=new o(G,V);q.blendMode=Z,ue={index:0,indexCount:0,vertexCount:0,uvsCount:0,total:0,mesh:q,vertices:null,uvs:null,indices:null}}ue.index=0,ue.indexCount=0,ue.vertexCount=0,ue.uvsCount=0,ue.total=0;var $=this._textureCache;$[X]=$[X]||new c(ie.baseTexture),ue.mesh.texture=$[X],ue.mesh.tint=this._tint,U.push(ue),W[X]=ue}W[X].total++}for(C=0;C<Y.length;C++)-1===U.indexOf(Y[C])&&this.removeChild(Y[C].mesh);for(C=0;C<U.length;C++)U[C].mesh.parent!==this&&this.addChild(U[C].mesh);for(var C in this._activePagesMeshData=U,W){var J=(ue=W[C]).total;if(!((null===(e=ue.indices)||void 0===e?void 0:e.length)>6*J)||ue.vertices.length<2*o.BATCHABLE_SIZE)ue.vertices=new Float32Array(8*J),ue.uvs=new Float32Array(8*J),ue.indices=new Uint16Array(6*J);else for(var K=ue.total,Q=ue.vertices,ee=4*K*2;ee<Q.length;ee++)Q[ee]=0;ue.mesh.size=6*J}for(C=0;C<j;C++){var te,re=(te=l[C]).position.x+L[te.line]*("justify"===this._align?te.prevSpaces:1);this._roundPixels&&(re=Math.round(re));var ie,ne=re*s,ae=te.position.y*s,oe=W[(ie=te.texture).baseTexture.uid],se=ie.frame,he=ie._uvs,le=oe.index++;oe.indices[6*le+0]=0+4*le,oe.indices[6*le+1]=1+4*le,oe.indices[6*le+2]=2+4*le,oe.indices[6*le+3]=0+4*le,oe.indices[6*le+4]=2+4*le,oe.indices[6*le+5]=3+4*le,oe.vertices[8*le+0]=ne,oe.vertices[8*le+1]=ae,oe.vertices[8*le+2]=ne+se.width*s,oe.vertices[8*le+3]=ae,oe.vertices[8*le+4]=ne+se.width*s,oe.vertices[8*le+5]=ae+se.height*s,oe.vertices[8*le+6]=ne,oe.vertices[8*le+7]=ae+se.height*s,oe.uvs[8*le+0]=he.x0,oe.uvs[8*le+1]=he.y0,oe.uvs[8*le+2]=he.x1,oe.uvs[8*le+3]=he.y1,oe.uvs[8*le+4]=he.x2,oe.uvs[8*le+5]=he.y2,oe.uvs[8*le+6]=he.x3,oe.uvs[8*le+7]=he.y3}for(var C in this._textWidth=b*s,this._textHeight=(h.y+r.lineHeight)*s,W){var ue=W[C];if(0!==this.anchor.x||0!==this.anchor.y)for(var fe=0,ce=this._textWidth*this.anchor.x,pe=this._textHeight*this.anchor.y,de=0;de<ue.total;de++)ue.vertices[fe++]-=ce,ue.vertices[fe++]-=pe,ue.vertices[fe++]-=ce,ue.vertices[fe++]-=pe,ue.vertices[fe++]-=ce,ue.vertices[fe++]-=pe,ue.vertices[fe++]-=ce,ue.vertices[fe++]-=pe;this._maxLineHeight=P*s;var ge=ue.mesh.geometry.getBuffer("aVertexPosition"),me=ue.mesh.geometry.getBuffer("aTextureCoord"),ve=ue.mesh.geometry.getIndex();ge.data=ue.vertices,me.data=ue.uvs,ve.data=ue.indices,ge.update(),me.update(),ve.update()}for(C=0;C<l.length;C++)D.push(l[C]);this._font=r,this.dirty=!1},s.prototype.updateTransform=function(){this.validate(),this.containerUpdateTransform()},s.prototype._render=function(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0);var r=k.available[this._fontName],i=r.distanceFieldRange,n=r.distanceFieldType,a=r.size;if("none"!==n)for(var o=this.worldTransform,s=o.a,h=o.b,l=o.c,u=o.d,f=Math.sqrt(s*s+h*h),c=Math.sqrt(l*l+u*u),p=(Math.abs(f)+Math.abs(c))/2,d=this.fontSize/a,g=0,m=this._activePagesMeshData;g<m.length;g++){m[g].mesh.shader.uniforms.uFWidth=p*i*d*this._resolution}e.prototype._render.call(this,t)},s.prototype.getLocalBounds=function(){return this.validate(),e.prototype.getLocalBounds.call(this)},s.prototype.validate=function(){var e=k.available[this._fontName];if(!e)throw new Error('Missing BitmapFont "'+this._fontName+'"');this._font!==e&&(this.dirty=!0),this.dirty&&this.updateText()},Object.defineProperty(s.prototype,"tint",{get:function(){return this._tint},set:function(e){if(this._tint!==e){this._tint=e;for(var t=0;t<this._activePagesMeshData.length;t++)this._activePagesMeshData[t].mesh.tint=e}},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"align",{get:function(){return this._align},set:function(e){this._align!==e&&(this._align=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"fontName",{get:function(){return this._fontName},set:function(e){if(!k.available[e])throw new Error('Missing BitmapFont "'+e+'"');this._fontName!==e&&(this._fontName=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"fontSize",{get:function(){var e;return null!==(e=this._fontSize)&&void 0!==e?e:k.available[this._fontName].size},set:function(e){this._fontSize!==e&&(this._fontSize=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"anchor",{get:function(){return this._anchor},set:function(e){"number"==typeof e?this._anchor.set(e):this._anchor.copyFrom(e)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"text",{get:function(){return this._text},set:function(e){e=String(null==e?"":e),this._text!==e&&(this._text=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"maxWidth",{get:function(){return this._maxWidth},set:function(e){this._maxWidth!==e&&(this._maxWidth=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"maxLineHeight",{get:function(){return this.validate(),this._maxLineHeight},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"textWidth",{get:function(){return this.validate(),this._textWidth},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"letterSpacing",{get:function(){return this._letterSpacing},set:function(e){this._letterSpacing!==e&&(this._letterSpacing=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(e){e!==this._roundPixels&&(this._roundPixels=e,this.dirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"textHeight",{get:function(){return this.validate(),this._textHeight},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"resolution",{get:function(){return this._resolution},set:function(e){this._autoResolution=!1,this._resolution!==e&&(this._resolution=e,this.dirty=!0)},enumerable:!1,configurable:!0}),s.prototype.destroy=function(t){var r=this._textureCache,i="none"===k.available[this._fontName].distanceFieldType?N:z;i.push.apply(i,this._activePagesMeshData);for(var n=0,a=this._activePagesMeshData;n<a.length;n++){var o=a[n];this.removeChild(o.mesh)}for(var s in this._activePagesMeshData=[],i.filter((function(e){return r[e.mesh.texture.baseTexture.uid]})).forEach((function(e){e.mesh.texture=c.EMPTY})),r){r[s].destroy(),delete r[s]}this._font=null,this._textureCache=null,e.prototype.destroy.call(this,t)},s.styleDefaults={align:"left",tint:16777215,maxWidth:0,letterSpacing:0},s}(b),B=function(){function e(){}return e.add=function(){w.setExtensionXhrType("fnt",w.XHR_RESPONSE_TYPE.TEXT)},e.use=function(t,r){var i=F(t.data);if(i)for(var n=e.getBaseUrl(this,t),a=i.parse(t.data),o={},s=function(e){o[e.metadata.pageFile]=e.texture,Object.keys(o).length===a.page.length&&(t.bitmapFont=k.install(a,o,!0),r())},h=0;h<a.page.length;++h){var l=a.page[h].file,u=n+l,f=!1;for(var c in this.resources){var p=this.resources[c];if(p.url===u){p.metadata.pageFile=l,p.texture?s(p):p.onAfterMiddleware.add(s),f=!0;break}}if(!f){var d={crossOrigin:t.crossOrigin,loadType:w.LOAD_TYPE.IMAGE,metadata:Object.assign({pageFile:l},t.metadata.imageMetadata),parentResource:t};this.add(u,d,s)}}else r()},e.getBaseUrl=function(t,r){var i=r.isDataUrl?"":e.dirname(r.url);return r.isDataUrl&&("."===i&&(i=""),t.baseUrl&&i&&"/"===t.baseUrl.charAt(t.baseUrl.length-1)&&(i+="/")),(i=i.replace(t.baseUrl,""))&&"/"!==i.charAt(i.length-1)&&(i+="/"),i},e.dirname=function(e){var t=e.replace(/\\/g,"/").replace(/\/$/,"").replace(/\/[^\/]*$/,"");return t===e?".":""===t?"/":t},e.extension=d.Loader,e}();export{k as BitmapFont,A as BitmapFontData,B as BitmapFontLoader,H as BitmapText,S as TextFormat,P as XMLFormat,M as XMLStringFormat,F as autoDetectFormat};
//# sourceMappingURL=text-bitmap.min.mjs.map
