/*!
 * @pixi/mixin-get-child-by-name - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-get-child-by-name is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{},function(t){"use strict";t.DisplayObject.prototype.name=null,t.Container.prototype.getChildByName=function(t,i){for(var e=0,n=this.children.length;e<n;e++)if(this.children[e].name===t)return this.children[e];if(i)for(e=0,n=this.children.length;e<n;e++){var r=this.children[e];if(r.getChildByName){var h=r.getChildByName(t,!0);if(h)return h}}return null}}(PIXI);
//# sourceMappingURL=mixin-get-child-by-name.min.js.map
