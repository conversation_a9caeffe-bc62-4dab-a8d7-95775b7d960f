#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
缩略图API服务器
为CosyVoice项目提供图片缩略图生成功能，减少内存占用
"""

import os
import sys
from flask import Flask, request, send_file, jsonify
from flask_cors import CORS
from PIL import Image, ImageOps
import io
import base64
from urllib.parse import unquote
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 配置
THUMBNAIL_CACHE_DIR = os.path.join(os.path.dirname(__file__), 'cache', 'thumbnails')
MAX_THUMBNAIL_SIZE = 800  # 最大缩略图尺寸
DEFAULT_THUMBNAIL_SIZE = 300
SUPPORTED_FORMATS = {'jpg', 'jpeg', 'png', 'webp', 'bmp', 'gif'}

# 确保缓存目录存在
os.makedirs(THUMBNAIL_CACHE_DIR, exist_ok=True)

def get_image_path_from_url(image_path):
    """从URL路径获取实际文件路径"""
    # 移除URL编码
    image_path = unquote(image_path)
    
    # 处理相对路径
    if image_path.startswith('/'):
        image_path = image_path[1:]
    
    # 支持的图片目录
    possible_dirs = [
        os.path.join(os.path.dirname(__file__), 'public'),
        os.path.join(os.path.dirname(__file__), 'generated-images'),
        os.path.join(os.path.dirname(__file__), 'ComfyUI', 'output'),
        os.path.join(os.path.dirname(__file__), 'audio'),
        os.path.join(os.path.dirname(__file__), 'audios'),
    ]
    
    for base_dir in possible_dirs:
        full_path = os.path.join(base_dir, image_path)
        if os.path.exists(full_path):
            return full_path
    
    # 如果没找到，尝试直接路径
    if os.path.exists(image_path):
        return image_path
    
    return None

def generate_thumbnail_filename(original_path, size):
    """生成缩略图文件名"""
    name, ext = os.path.splitext(os.path.basename(original_path))
    return f"{name}_{size}x{size}{ext}"

def create_thumbnail(image_path, size=DEFAULT_THUMBNAIL_SIZE, quality=85):
    """创建缩略图"""
    try:
        with Image.open(image_path) as img:
            # 转换为RGB以确保兼容性
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 计算缩略图尺寸，保持比例
            img.thumbnail((size, size), Image.LANCZOS)
            
            # 创建内存中的图片
            img_io = io.BytesIO()
            img.save(img_io, format='JPEG', quality=quality, optimize=True)
            img_io.seek(0)
            
            return img_io
    except Exception as e:
        logger.error(f"创建缩略图失败 {image_path}: {e}")
        return None

@app.route('/thumbnail')
def get_thumbnail():
    """获取图片缩略图"""
    try:
        # 获取参数
        image_path = request.args.get('path')
        size = min(int(request.args.get('size', DEFAULT_THUMBNAIL_SIZE)), MAX_THUMBNAIL_SIZE)
        quality = min(int(request.args.get('quality', 85)), 100)
        
        if not image_path:
            return jsonify({'error': '缺少path参数'}), 400
        
        # 获取实际文件路径
        actual_path = get_image_path_from_url(image_path)
        if not actual_path:
            return jsonify({'error': f'图片文件不存在: {image_path}'}), 404
        
        # 检查文件扩展名
        ext = os.path.splitext(actual_path)[1].lower().replace('.', '')
        if ext not in SUPPORTED_FORMATS:
            return jsonify({'error': f'不支持的图片格式: {ext}'}), 400
        
        # 检查缓存
        thumbnail_filename = generate_thumbnail_filename(actual_path, size)
        cache_path = os.path.join(THUMBNAIL_CACHE_DIR, thumbnail_filename)
        
        # 如果缓存存在且比原文件新，直接返回缓存
        if (os.path.exists(cache_path) and 
            os.path.getmtime(cache_path) >= os.path.getmtime(actual_path)):
            return send_file(cache_path, mimetype='image/jpeg')
        
        # 创建缩略图
        thumbnail_io = create_thumbnail(actual_path, size, quality)
        if not thumbnail_io:
            return jsonify({'error': '缩略图生成失败'}), 500
        
        # 保存到缓存
        try:
            with open(cache_path, 'wb') as f:
                f.write(thumbnail_io.getvalue())
            thumbnail_io.seek(0)
        except Exception as e:
            logger.warning(f"缓存保存失败: {e}")
        
        return send_file(thumbnail_io, mimetype='image/jpeg')
        
    except Exception as e:
        logger.error(f"缩略图API错误: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@app.route('/thumbnail/clear-cache')
def clear_cache():
    """清理缩略图缓存"""
    try:
        import shutil
        if os.path.exists(THUMBNAIL_CACHE_DIR):
            shutil.rmtree(THUMBNAIL_CACHE_DIR)
            os.makedirs(THUMBNAIL_CACHE_DIR, exist_ok=True)
        return jsonify({'message': '缓存已清理'})
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        return jsonify({'error': '清理缓存失败'}), 500

@app.route('/thumbnail/status')
def get_status():
    """获取缩略图服务状态"""
    try:
        cache_files = len(os.listdir(THUMBNAIL_CACHE_DIR)) if os.path.exists(THUMBNAIL_CACHE_DIR) else 0
        cache_size = 0
        
        if os.path.exists(THUMBNAIL_CACHE_DIR):
            for filename in os.listdir(THUMBNAIL_CACHE_DIR):
                filepath = os.path.join(THUMBNAIL_CACHE_DIR, filename)
                if os.path.isfile(filepath):
                    cache_size += os.path.getsize(filepath)
        
        return jsonify({
            'status': 'running',
            'cache_files': cache_files,
            'cache_size_mb': round(cache_size / 1024 / 1024, 2),
            'max_size': MAX_THUMBNAIL_SIZE,
            'default_size': DEFAULT_THUMBNAIL_SIZE,
            'supported_formats': list(SUPPORTED_FORMATS)
        })
    except Exception as e:
        logger.error(f"状态查询失败: {e}")
        return jsonify({'error': '状态查询失败'}), 500

if __name__ == '__main__':
    port = int(os.environ.get('THUMBNAIL_PORT', 3002))
    print(f"🖼️ 缩略图API服务器启动在端口 {port}")
    print(f"📁 缓存目录: {THUMBNAIL_CACHE_DIR}")
    print(f"🔧 API端点:")
    print(f"   - GET /thumbnail?path=<image_path>&size=<size>")
    print(f"   - GET /thumbnail/status")
    print(f"   - GET /thumbnail/clear-cache")
    
    app.run(host='0.0.0.0', port=port, debug=False)