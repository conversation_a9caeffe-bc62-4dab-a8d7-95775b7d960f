{"version": 3, "file": "constants.min.js", "sources": ["../../src/index.ts"], "sourcesContent": ["/**\n * Different types of environments for WebGL.\n * @static\n * @memberof PIXI\n * @name ENV\n * @enum {number}\n * @property {number} WEBGL_LEGACY - Used for older v1 WebGL devices. PixiJS will aim to ensure compatibility\n *  with older / less advanced devices. If you experience unexplained flickering prefer this environment.\n * @property {number} WEBGL - Version 1 of WebGL\n * @property {number} WEBGL2 - Version 2 of WebGL\n */\nexport enum ENV\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    WEBGL_LEGACY,\n    WEBGL,\n    WEBGL2,\n}\n\n/**\n * Constant to identify the Renderer Type.\n * @static\n * @memberof PIXI\n * @name RENDERER_TYPE\n * @enum {number}\n * @property {number} UNKNOWN - Unknown render type.\n * @property {number} WEBGL - WebGL render type.\n * @property {number} CANVAS - Canvas render type.\n */\nexport enum RENDERER_TYPE\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    UNKNOWN,\n    WEBGL,\n    CANVAS,\n}\n\n/**\n * Bitwise OR of masks that indicate the buffers to be cleared.\n * @static\n * @memberof PIXI\n * @name BUFFER_BITS\n * @enum {number}\n * @property {number} COLOR - Indicates the buffers currently enabled for color writing.\n * @property {number} DEPTH - Indicates the depth buffer.\n * @property {number} STENCIL - Indicates the stencil buffer.\n */\nexport enum BUFFER_BITS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    COLOR = 0x00004000,\n    DEPTH = 0x00000100,\n    STENCIL = 0x00000400\n}\n\n/**\n * Various blend modes supported by PIXI.\n *\n * IMPORTANT - The WebGL renderer only supports the NORMAL, ADD, MULTIPLY and SCREEN blend modes.\n * Anything else will silently act like NORMAL.\n * @memberof PIXI\n * @name BLEND_MODES\n * @enum {number}\n * @property {number} NORMAL -\n * @property {number} ADD -\n * @property {number} MULTIPLY -\n * @property {number} SCREEN -\n * @property {number} OVERLAY -\n * @property {number} DARKEN -\n * @property {number} LIGHTEN -\n * @property {number} COLOR_DODGE -\n * @property {number} COLOR_BURN -\n * @property {number} HARD_LIGHT -\n * @property {number} SOFT_LIGHT -\n * @property {number} DIFFERENCE -\n * @property {number} EXCLUSION -\n * @property {number} HUE -\n * @property {number} SATURATION -\n * @property {number} COLOR -\n * @property {number} LUMINOSITY -\n * @property {number} NORMAL_NPM -\n * @property {number} ADD_NPM -\n * @property {number} SCREEN_NPM -\n * @property {number} NONE -\n * @property {number} SRC_IN -\n * @property {number} SRC_OUT -\n * @property {number} SRC_ATOP -\n * @property {number} DST_OVER -\n * @property {number} DST_IN -\n * @property {number} DST_OUT -\n * @property {number} DST_ATOP -\n * @property {number} SUBTRACT -\n * @property {number} SRC_OVER -\n * @property {number} ERASE -\n * @property {number} XOR -\n */\nexport enum BLEND_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NORMAL = 0,\n    ADD = 1,\n    MULTIPLY = 2,\n    SCREEN = 3,\n    OVERLAY = 4,\n    DARKEN = 5,\n    LIGHTEN = 6,\n    COLOR_DODGE = 7,\n    COLOR_BURN = 8,\n    HARD_LIGHT = 9,\n    SOFT_LIGHT = 10,\n    DIFFERENCE = 11,\n    EXCLUSION = 12,\n    HUE = 13,\n    SATURATION = 14,\n    COLOR = 15,\n    LUMINOSITY = 16,\n    NORMAL_NPM = 17,\n    ADD_NPM = 18,\n    SCREEN_NPM = 19,\n    NONE = 20,\n\n    SRC_OVER = 0,\n    SRC_IN = 21,\n    SRC_OUT = 22,\n    SRC_ATOP = 23,\n    DST_OVER = 24,\n    DST_IN = 25,\n    DST_OUT = 26,\n    DST_ATOP = 27,\n    ERASE = 26,\n    SUBTRACT = 28,\n    XOR = 29,\n}\n\n/**\n * Various webgl draw modes. These can be used to specify which GL drawMode to use\n * under certain situations and renderers.\n * @memberof PIXI\n * @static\n * @name DRAW_MODES\n * @enum {number}\n * @property {number} POINTS -\n * @property {number} LINES -\n * @property {number} LINE_LOOP -\n * @property {number} LINE_STRIP -\n * @property {number} TRIANGLES -\n * @property {number} TRIANGLE_STRIP -\n * @property {number} TRIANGLE_FAN -\n */\nexport enum DRAW_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    POINTS,\n    LINES,\n    LINE_LOOP,\n    LINE_STRIP,\n    TRIANGLES,\n    TRIANGLE_STRIP,\n    TRIANGLE_FAN,\n}\n\n/**\n * Various GL texture/resources formats.\n * @memberof PIXI\n * @static\n * @name FORMATS\n * @enum {number}\n * @property {number} [RGBA=6408] -\n * @property {number} [RGB=6407] -\n * @property {number} [RG=33319] -\n * @property {number} [RED=6403] -\n * @property {number} [RGBA_INTEGER=36249] -\n * @property {number} [RGB_INTEGER=36248] -\n * @property {number} [RG_INTEGER=33320] -\n * @property {number} [RED_INTEGER=36244] -\n * @property {number} [ALPHA=6406] -\n * @property {number} [LUMINANCE=6409] -\n * @property {number} [LUMINANCE_ALPHA=6410] -\n * @property {number} [DEPTH_COMPONENT=6402] -\n * @property {number} [DEPTH_STENCIL=34041] -\n */\nexport enum FORMATS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    RGBA = 6408,\n    RGB = 6407,\n    RG = 33319,\n    RED = 6403,\n    RGBA_INTEGER = 36249,\n    RGB_INTEGER = 36248,\n    RG_INTEGER = 33320,\n    RED_INTEGER = 36244,\n    ALPHA = 6406,\n    LUMINANCE = 6409,\n    LUMINANCE_ALPHA = 6410,\n    DEPTH_COMPONENT = 6402,\n    DEPTH_STENCIL = 34041,\n}\n\n/**\n * Various GL target types.\n * @memberof PIXI\n * @static\n * @name TARGETS\n * @enum {number}\n * @property {number} [TEXTURE_2D=3553] -\n * @property {number} [TEXTURE_CUBE_MAP=34067] -\n * @property {number} [TEXTURE_2D_ARRAY=35866] -\n * @property {number} [TEXTURE_CUBE_MAP_POSITIVE_X=34069] -\n * @property {number} [TEXTURE_CUBE_MAP_NEGATIVE_X=34070] -\n * @property {number} [TEXTURE_CUBE_MAP_POSITIVE_Y=34071] -\n * @property {number} [TEXTURE_CUBE_MAP_NEGATIVE_Y=34072] -\n * @property {number} [TEXTURE_CUBE_MAP_POSITIVE_Z=34073] -\n * @property {number} [TEXTURE_CUBE_MAP_NEGATIVE_Z=34074] -\n */\nexport enum TARGETS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    TEXTURE_2D = 3553,\n    TEXTURE_CUBE_MAP = 34067,\n    TEXTURE_2D_ARRAY = 35866,\n    TEXTURE_CUBE_MAP_POSITIVE_X = 34069,\n    TEXTURE_CUBE_MAP_NEGATIVE_X = 34070,\n    TEXTURE_CUBE_MAP_POSITIVE_Y = 34071,\n    TEXTURE_CUBE_MAP_NEGATIVE_Y = 34072,\n    TEXTURE_CUBE_MAP_POSITIVE_Z = 34073,\n    TEXTURE_CUBE_MAP_NEGATIVE_Z = 34074,\n}\n\n/**\n * Various GL data format types.\n * @memberof PIXI\n * @static\n * @name TYPES\n * @enum {number}\n * @property {number} [UNSIGNED_BYTE=5121] -\n * @property {number} [UNSIGNED_SHORT=5123] -\n * @property {number} [UNSIGNED_SHORT_5_6_5=33635] -\n * @property {number} [UNSIGNED_SHORT_4_4_4_4=32819] -\n * @property {number} [UNSIGNED_SHORT_5_5_5_1=32820] -\n * @property {number} [UNSIGNED_INT=5125] -\n * @property {number} [UNSIGNED_INT_10F_11F_11F_REV=35899] -\n * @property {number} [UNSIGNED_INT_2_10_10_10_REV=33640] -\n * @property {number} [UNSIGNED_INT_24_8=34042] -\n * @property {number} [UNSIGNED_INT_5_9_9_9_REV=35902] -\n * @property {number} [BYTE=5120] -\n * @property {number} [SHORT=5122] -\n * @property {number} [INT=5124] -\n * @property {number} [FLOAT=5126] -\n * @property {number} [FLOAT_32_UNSIGNED_INT_24_8_REV=36269] -\n * @property {number} [HALF_FLOAT=36193] -\n */\nexport enum TYPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    UNSIGNED_BYTE = 5121,\n    UNSIGNED_SHORT = 5123,\n    UNSIGNED_SHORT_5_6_5 = 33635,\n    UNSIGNED_SHORT_4_4_4_4 = 32819,\n    UNSIGNED_SHORT_5_5_5_1 = 32820,\n    UNSIGNED_INT = 5125,\n    UNSIGNED_INT_10F_11F_11F_REV = 35899,\n    UNSIGNED_INT_2_10_10_10_REV = 33640,\n    UNSIGNED_INT_24_8 = 34042,\n    UNSIGNED_INT_5_9_9_9_REV = 35902,\n    BYTE = 5120,\n    SHORT = 5122,\n    INT = 5124,\n    FLOAT = 5126,\n    FLOAT_32_UNSIGNED_INT_24_8_REV = 36269,\n    HALF_FLOAT = 36193,\n}\n\n/**\n * Various sampler types. Correspond to `sampler`, `isampler`, `usampler` GLSL types respectively.\n * WebGL1 works only with FLOAT.\n * @memberof PIXI\n * @static\n * @name SAMPLER_TYPES\n * @enum {number}\n * @property {number} [FLOAT=0] -\n * @property {number} [INT=1] -\n * @property {number} [UINT=2] -\n */\nexport enum SAMPLER_TYPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    FLOAT = 0,\n    INT = 1,\n    UINT = 2,\n}\n\n/**\n * The scale modes that are supported by pixi.\n *\n * The {@link PIXI.settings.SCALE_MODE} scale mode affects the default scaling mode of future operations.\n * It can be re-assigned to either LINEAR or NEAREST, depending upon suitability.\n * @memberof PIXI\n * @static\n * @name SCALE_MODES\n * @enum {number}\n * @property {number} LINEAR Smooth scaling\n * @property {number} NEAREST Pixelating scaling\n */\nexport enum SCALE_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NEAREST,\n    LINEAR,\n}\n\n/**\n * The wrap modes that are supported by pixi.\n *\n * The {@link PIXI.settings.WRAP_MODE} wrap mode affects the default wrapping mode of future operations.\n * It can be re-assigned to either CLAMP or REPEAT, depending upon suitability.\n * If the texture is non power of two then clamp will be used regardless as WebGL can\n * only use REPEAT if the texture is po2.\n *\n * This property only affects WebGL.\n * @name WRAP_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} CLAMP - The textures uvs are clamped\n * @property {number} REPEAT - The texture uvs tile and repeat\n * @property {number} MIRRORED_REPEAT - The texture uvs tile and repeat with mirroring\n */\nexport enum WRAP_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    CLAMP = 33071,\n    REPEAT = 10497,\n    MIRRORED_REPEAT = 33648,\n}\n\n/**\n * Mipmap filtering modes that are supported by pixi.\n *\n * The {@link PIXI.settings.MIPMAP_TEXTURES} affects default texture filtering.\n * Mipmaps are generated for a baseTexture if its `mipmap` field is `ON`,\n * or its `POW2` and texture dimensions are powers of 2.\n * Due to platform restriction, `ON` option will work like `POW2` for webgl-1.\n *\n * This property only affects WebGL.\n * @name MIPMAP_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} OFF - No mipmaps\n * @property {number} POW2 - Generate mipmaps if texture dimensions are pow2\n * @property {number} ON - Always generate mipmaps\n * @property {number} ON_MANUAL - Use mipmaps, but do not auto-generate them; this is used with a resource\n *   that supports buffering each level-of-detail.\n */\nexport enum MIPMAP_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    OFF,\n    POW2,\n    ON,\n    ON_MANUAL\n}\n\n/**\n * How to treat textures with premultiplied alpha\n * @name ALPHA_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} NO_PREMULTIPLIED_ALPHA - Source is not premultiplied, leave it like that.\n *  Option for compressed and data textures that are created from typed arrays.\n * @property {number} PREMULTIPLY_ON_UPLOAD - Source is not premultiplied, premultiply on upload.\n *  Default option, used for all loaded images.\n * @property {number} PREMULTIPLIED_ALPHA - Source is already premultiplied\n *  Example: spine atlases with `_pma` suffix.\n * @property {number} NPM - Alias for NO_PREMULTIPLIED_ALPHA.\n * @property {number} UNPACK - Default option, alias for PREMULTIPLY_ON_UPLOAD.\n * @property {number} PMA - Alias for PREMULTIPLIED_ALPHA.\n */\nexport enum ALPHA_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NPM = 0,\n    UNPACK = 1,\n    PMA = 2,\n    NO_PREMULTIPLIED_ALPHA = 0,\n    PREMULTIPLY_ON_UPLOAD = 1,\n    PREMULTIPLY_ALPHA = 2, // deprecated, undocumented\n    PREMULTIPLIED_ALPHA = 2,\n}\n\n/**\n * Configure whether filter textures are cleared after binding.\n *\n * Filter textures need not be cleared if the filter does not use pixel blending. {@link CLEAR_MODES.BLIT} will detect\n * this and skip clearing as an optimization.\n * @name CLEAR_MODES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} BLEND - Do not clear the filter texture. The filter's output will blend on top of the output texture.\n * @property {number} CLEAR - Always clear the filter texture.\n * @property {number} BLIT - Clear only if {@link FilterSystem.forceClear} is set or if the filter uses pixel blending.\n * @property {number} NO - Alias for BLEND, same as `false` in earlier versions\n * @property {number} YES - Alias for CLEAR, same as `true` in earlier versions\n * @property {number} AUTO - Alias for BLIT\n */\nexport enum CLEAR_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NO = 0,\n    YES = 1,\n    AUTO = 2,\n    BLEND = 0,\n    CLEAR = 1,\n    BLIT = 2,\n}\n\n/**\n * The gc modes that are supported by pixi.\n *\n * The {@link PIXI.settings.GC_MODE} Garbage Collection mode for PixiJS textures is AUTO\n * If set to GC_MODE, the renderer will occasionally check textures usage. If they are not\n * used for a specified period of time they will be removed from the GPU. They will of course\n * be uploaded again when they are required. This is a silent behind the scenes process that\n * should ensure that the GPU does not  get filled up.\n *\n * Handy for mobile devices!\n * This property only affects WebGL.\n * @name GC_MODES\n * @enum {number}\n * @static\n * @memberof PIXI\n * @property {number} AUTO - Garbage collection will happen periodically automatically\n * @property {number} MANUAL - Garbage collection will need to be called manually\n */\nexport enum GC_MODES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    AUTO,\n    MANUAL,\n}\n\n/**\n * Constants that specify float precision in shaders.\n * @name PRECISION\n * @memberof PIXI\n * @constant\n * @static\n * @enum {string}\n * @property {string} [LOW='lowp'] -\n * @property {string} [MEDIUM='mediump'] -\n * @property {string} [HIGH='highp'] -\n */\nexport enum PRECISION\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    LOW = 'lowp',\n    MEDIUM = 'mediump',\n    HIGH = 'highp',\n}\n\n/**\n * Constants for mask implementations.\n * We use `type` suffix because it leads to very different behaviours\n * @name MASK_TYPES\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} NONE - Mask is ignored\n * @property {number} SCISSOR - Scissor mask, rectangle on screen, cheap\n * @property {number} STENCIL - Stencil mask, 1-bit, medium, works only if renderer supports stencil\n * @property {number} SPRITE - Mask that uses SpriteMaskFilter, uses temporary RenderTexture\n * @property {number} COLOR - Color mask (RGBA)\n */\nexport enum MASK_TYPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NONE = 0,\n    SCISSOR = 1,\n    STENCIL = 2,\n    SPRITE = 3,\n    COLOR = 4,\n}\n\n/**\n * Bitwise OR of masks that indicate the color channels that are rendered to.\n * @static\n * @memberof PIXI\n * @name COLOR_MASK_BITS\n * @enum {number}\n * @property {number} RED - Red channel.\n * @property {number} GREEN - Green channel\n * @property {number} BLUE - Blue channel.\n * @property {number} ALPHA - Alpha channel.\n */\nexport enum COLOR_MASK_BITS\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    RED = 0x1,\n    GREEN = 0x2,\n    BLUE = 0x4,\n    ALPHA = 0x8\n}\n\n/**\n * Constants for multi-sampling antialiasing.\n * @see PIXI.Framebuffer#multisample\n * @name MSAA_QUALITY\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} NONE - No multisampling for this renderTexture\n * @property {number} LOW - Try 2 samples\n * @property {number} MEDIUM - Try 4 samples\n * @property {number} HIGH - Try 8 samples\n */\nexport enum MSAA_QUALITY\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    NONE = 0,\n    LOW = 2,\n    MEDIUM = 4,\n    HIGH = 8\n}\n\n/**\n * Constants for various buffer types in Pixi\n * @see PIXI.BUFFER_TYPE\n * @name BUFFER_TYPE\n * @memberof PIXI\n * @static\n * @enum {number}\n * @property {number} ELEMENT_ARRAY_BUFFER - buffer type for using as an index buffer\n * @property {number} ARRAY_BUFFER - buffer type for using attribute data\n * @property {number} UNIFORM_BUFFER - the buffer type is for uniform buffer objects\n */\nexport enum BUFFER_TYPE\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    ELEMENT_ARRAY_BUFFER = 34963,\n    ARRAY_BUFFER = 34962,\n    // NOT YET SUPPORTED\n    UNIFORM_BUFFER = 35345,\n}\n"], "names": ["ENV", "RENDERER_TYPE", "BUFFER_BITS", "BLEND_MODES", "DRAW_MODES", "FORMATS", "TARGETS", "TYPES", "SAMPLER_TYPES", "SCALE_MODES", "WRAP_MODES", "MIPMAP_MODES", "ALPHA_MODES", "CLEAR_MODES", "GC_MODES", "PRECISION", "MASK_TYPES", "COLOR_MASK_BITS", "MSAA_QUALITY", "BUFFER_TYPE"], "mappings": ";;;;;;;aAWA,IAAYA,EAkBAC,EAkBAC,EAiDAC,EAqDAC,EAgCAC,EAkCAC,EAqCAC,EAgCAC,EAoBAC,EAwBAC,EA2BAC,EAyBAC,EA4BAC,EA6BAC,EAkBAC,EAqBAC,EAqBAC,EAqBAC,EAoBAC,yDA/gBAnB,QAMXA,SAAA,GANWA,EAAAA,cAAAA,QAAAA,IAMX,KAHGA,EAAA,aAAA,GAAA,eACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SAaQC,QAMXA,mBAAA,GANWA,EAAAA,wBAAAA,QAAAA,cAMX,KAHGA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SAaQC,QAMXA,iBAAA,GANWA,EAAAA,sBAAAA,QAAAA,YAMX,KAHGA,EAAA,MAAA,OAAA,QACAA,EAAAA,EAAA,MAAA,KAAA,QACAA,EAAAA,EAAA,QAAA,MAAA,UA4CQC,QAoCXA,iBAAA,GApCWA,EAAAA,sBAAAA,QAAAA,YAoCX,KAjCGA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,YAAA,GAAA,cACAA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,WAAA,IAAA,aACAA,EAAAA,EAAA,WAAA,IAAA,aACAA,EAAAA,EAAA,UAAA,IAAA,YACAA,EAAAA,EAAA,IAAA,IAAA,MACAA,EAAAA,EAAA,WAAA,IAAA,aACAA,EAAAA,EAAA,MAAA,IAAA,QACAA,EAAAA,EAAA,WAAA,IAAA,aACAA,EAAAA,EAAA,WAAA,IAAA,aACAA,EAAAA,EAAA,QAAA,IAAA,UACAA,EAAAA,EAAA,WAAA,IAAA,aACAA,EAAAA,EAAA,KAAA,IAAA,OAEAA,EAAAA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,OAAA,IAAA,SACAA,EAAAA,EAAA,QAAA,IAAA,UACAA,EAAAA,EAAA,SAAA,IAAA,WACAA,EAAAA,EAAA,SAAA,IAAA,WACAA,EAAAA,EAAA,OAAA,IAAA,SACAA,EAAAA,EAAA,QAAA,IAAA,UACAA,EAAAA,EAAA,SAAA,IAAA,WACAA,EAAAA,EAAA,MAAA,IAAA,QACAA,EAAAA,EAAA,SAAA,IAAA,WACAA,EAAAA,EAAA,IAAA,IAAA,MAkBQC,QAUXA,gBAAA,GAVWA,EAAAA,qBAAAA,QAAAA,WAUX,KAPGA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,UAAA,GAAA,YACAA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,UAAA,GAAA,YACAA,EAAAA,EAAA,eAAA,GAAA,iBACAA,EAAAA,EAAA,aAAA,GAAA,eAuBQC,QAgBXA,aAAA,GAhBWA,EAAAA,kBAAAA,QAAAA,QAgBX,KAbGA,EAAA,KAAA,MAAA,OACAA,EAAAA,EAAA,IAAA,MAAA,MACAA,EAAAA,EAAA,GAAA,OAAA,KACAA,EAAAA,EAAA,IAAA,MAAA,MACAA,EAAAA,EAAA,aAAA,OAAA,eACAA,EAAAA,EAAA,YAAA,OAAA,cACAA,EAAAA,EAAA,WAAA,OAAA,aACAA,EAAAA,EAAA,YAAA,OAAA,cACAA,EAAAA,EAAA,MAAA,MAAA,QACAA,EAAAA,EAAA,UAAA,MAAA,YACAA,EAAAA,EAAA,gBAAA,MAAA,kBACAA,EAAAA,EAAA,gBAAA,MAAA,kBACAA,EAAAA,EAAA,cAAA,OAAA,gBAmBQC,QAYXA,aAAA,GAZWA,EAAAA,kBAAAA,QAAAA,QAYX,KATGA,EAAA,WAAA,MAAA,aACAA,EAAAA,EAAA,iBAAA,OAAA,mBACAA,EAAAA,EAAA,iBAAA,OAAA,mBACAA,EAAAA,EAAA,4BAAA,OAAA,8BACAA,EAAAA,EAAA,4BAAA,OAAA,8BACAA,EAAAA,EAAA,4BAAA,OAAA,8BACAA,EAAAA,EAAA,4BAAA,OAAA,8BACAA,EAAAA,EAAA,4BAAA,OAAA,8BACAA,EAAAA,EAAA,4BAAA,OAAA,8BA0BQC,QAmBXA,WAAA,GAnBWA,EAAAA,gBAAAA,QAAAA,MAmBX,KAhBGA,EAAA,cAAA,MAAA,gBACAA,EAAAA,EAAA,eAAA,MAAA,iBACAA,EAAAA,EAAA,qBAAA,OAAA,uBACAA,EAAAA,EAAA,uBAAA,OAAA,yBACAA,EAAAA,EAAA,uBAAA,OAAA,yBACAA,EAAAA,EAAA,aAAA,MAAA,eACAA,EAAAA,EAAA,6BAAA,OAAA,+BACAA,EAAAA,EAAA,4BAAA,OAAA,8BACAA,EAAAA,EAAA,kBAAA,OAAA,oBACAA,EAAAA,EAAA,yBAAA,OAAA,2BACAA,EAAAA,EAAA,KAAA,MAAA,OACAA,EAAAA,EAAA,MAAA,MAAA,QACAA,EAAAA,EAAA,IAAA,MAAA,MACAA,EAAAA,EAAA,MAAA,MAAA,QACAA,EAAAA,EAAA,+BAAA,OAAA,iCACAA,EAAAA,EAAA,WAAA,OAAA,aAcQC,QAMXA,mBAAA,GANWA,EAAAA,wBAAAA,QAAAA,cAMX,KAHGA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,KAAA,GAAA,OAeQC,QAKXA,iBAAA,GALWA,EAAAA,sBAAAA,QAAAA,YAKX,KAFGA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,OAAA,GAAA,SAoBQC,QAMXA,gBAAA,GANWA,EAAAA,qBAAAA,QAAAA,WAMX,KAHGA,EAAA,MAAA,OAAA,QACAA,EAAAA,EAAA,OAAA,OAAA,SACAA,EAAAA,EAAA,gBAAA,OAAA,kBAsBQC,QAOXA,kBAAA,GAPWA,EAAAA,uBAAAA,QAAAA,aAOX,KAJGA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,GAAA,GAAA,KACAA,EAAAA,EAAA,UAAA,GAAA,YAmBQC,QAUXA,iBAAA,GAVWA,EAAAA,sBAAAA,QAAAA,YAUX,KAPGA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,uBAAA,GAAA,yBACAA,EAAAA,EAAA,sBAAA,GAAA,wBACAA,EAAAA,EAAA,kBAAA,GAAA,oBACAA,EAAAA,EAAA,oBAAA,GAAA,sBAmBQC,QASXA,iBAAA,GATWA,EAAAA,sBAAAA,QAAAA,YASX,KANGA,EAAA,GAAA,GAAA,KACAA,EAAAA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,KAAA,GAAA,OAqBQC,QAKXA,cAAA,GALWA,EAAAA,mBAAAA,QAAAA,SAKX,KAFGA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,OAAA,GAAA,SAcQC,QAMXA,eAAA,GANWA,EAAAA,oBAAAA,QAAAA,UAMX,KAHG,IAAA,OACAA,EAAA,OAAA,UACAA,EAAA,KAAA,QAgBQC,QAQXA,gBAAA,GARWA,EAAAA,qBAAAA,QAAAA,WAQX,KALGA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,MAAA,GAAA,QAcQC,QAOXA,qBAAA,GAPWA,EAAAA,0BAAAA,QAAAA,gBAOX,KAJGA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QAeQC,QAOXA,kBAAA,GAPWA,EAAAA,uBAAAA,QAAAA,aAOX,KAJGA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,KAAA,GAAA,OAcQC,QAOXA,iBAAA,GAPWA,EAAAA,QAAWA,cAAXA,oBAOX,KAJGA,EAAA,qBAAA,OAAA,uBACAA,EAAAA,EAAA,aAAA,OAAA,eAEAA,EAAAA,EAAA,eAAA,OAAA"}