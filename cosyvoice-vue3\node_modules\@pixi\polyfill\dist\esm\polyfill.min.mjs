/*!
 * @pixi/polyfill - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/polyfill is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import a from"promise-polyfill";import r from"object-assign";"undefined"==typeof globalThis&&("undefined"!=typeof self?self.globalThis=self:"undefined"!=typeof global&&(global.globalThis=global)),globalThis.Promise||(globalThis.Promise=a),Object.assign||(Object.assign=r);if(Date.now&&Date.prototype.getTime||(Date.now=function(){return(new Date).getTime()}),!globalThis.performance||!globalThis.performance.now){var e=Date.now();globalThis.performance||(globalThis.performance={}),globalThis.performance.now=function(){return Date.now()-e}}for(var o=Date.now(),i=["ms","moz","webkit","o"],n=0;n<i.length&&!globalThis.requestAnimationFrame;++n){var l=i[n];globalThis.requestAnimationFrame=globalThis[l+"RequestAnimationFrame"],globalThis.cancelAnimationFrame=globalThis[l+"CancelAnimationFrame"]||globalThis[l+"CancelRequestAnimationFrame"]}globalThis.requestAnimationFrame||(globalThis.requestAnimationFrame=function(a){if("function"!=typeof a)throw new TypeError(a+"is not a function");var r=Date.now(),e=16+o-r;return e<0&&(e=0),o=r,globalThis.self.setTimeout((function(){o=Date.now(),a(performance.now())}),e)}),globalThis.cancelAnimationFrame||(globalThis.cancelAnimationFrame=function(a){return clearTimeout(a)}),Math.sign||(Math.sign=function(a){return 0===(a=Number(a))||isNaN(a)?a:a>0?1:-1}),Number.isInteger||(Number.isInteger=function(a){return"number"==typeof a&&isFinite(a)&&Math.floor(a)===a}),globalThis.ArrayBuffer||(globalThis.ArrayBuffer=Array),globalThis.Float32Array||(globalThis.Float32Array=Array),globalThis.Uint32Array||(globalThis.Uint32Array=Array),globalThis.Uint16Array||(globalThis.Uint16Array=Array),globalThis.Uint8Array||(globalThis.Uint8Array=Array),globalThis.Int32Array||(globalThis.Int32Array=Array);
//# sourceMappingURL=polyfill.min.mjs.map
