---
description: 
globs: 
alwaysApply: false
---
# 服务器启动指南

## 关键注意事项
⚠️ **重要**: 服务器必须在正确的目录中启动！

## 正确的启动位置
- **项目位置**: `H:\AI\CosyVoice\cosyvoice-vue3`
- **package.json位置**: [package.json](mdc:CosyVoice/package.json)

## 启动步骤

### 1. 确认目录
```bash
cd H:\AI\CosyVoice\cosyvoice-vue3
pwd  # 确认当前目录
```

### 2. 检查package.json
确保当前目录包含 [package.json](mdc:CosyVoice/package.json) 文件

### 3. 启动开发服务器
```bash
npm run dev
```

## 常见错误及解决方案

### 错误: "Could not read package.json"
**原因**: 在错误的目录运行命令
**解决**: 切换到 `H:\AI\CosyVoice\cosyvoice-vue3` 目录

### 错误: "'vite' 不是内部或外部命令"
**原因**: 依赖未正确安装
**解决**: 
```bash
npm install
npm run dev
```

### 错误: 端口被占用
**解决**: 
```bash
# 检查端口5173使用情况
netstat -an | findstr :5173
# 或使用不同端口
npm run dev -- --port 5174
```

## 验证服务器运行
- 服务器正常启动后，访问: `http://localhost:5173`
- 应该看到CosyVoice主页

## 开发服务器配置
参考 [vite.config.ts](mdc:CosyVoice/vite.config.ts):
- 默认端口: 5173
- API代理: `/api` → `http://localhost:8000`
- WebSocket代理: `/ws` → `ws://localhost:8000`

