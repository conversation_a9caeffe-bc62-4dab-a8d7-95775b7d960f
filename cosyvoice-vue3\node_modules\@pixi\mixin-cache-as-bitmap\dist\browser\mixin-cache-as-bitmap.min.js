/*!
 * @pixi/mixin-cache-as-bitmap - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-cache-as-bitmap is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_mixin_cache_as_bitmap=function(t,a,e,i,s,r,n,h){"use strict";var c=new s.Matrix;i.DisplayObject.prototype._cacheAsBitmap=!1,i.DisplayObject.prototype._cacheData=null,i.DisplayObject.prototype._cacheAsBitmapResolution=null,i.DisplayObject.prototype._cacheAsBitmapMultisample=h.MSAA_QUALITY.NONE;var o=function(){this.textureCacheId=null,this.originalRender=null,this.originalRenderCanvas=null,this.originalCalculateBounds=null,this.originalGetLocalBounds=null,this.originalUpdateTransform=null,this.originalDestroy=null,this.originalMask=null,this.originalFilterArea=null,this.originalContainsPoint=null,this.sprite=null};return Object.defineProperties(i.DisplayObject.prototype,{cacheAsBitmapResolution:{get:function(){return this._cacheAsBitmapResolution},set:function(t){t!==this._cacheAsBitmapResolution&&(this._cacheAsBitmapResolution=t,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmapMultisample:{get:function(){return this._cacheAsBitmapMultisample},set:function(t){t!==this._cacheAsBitmapMultisample&&(this._cacheAsBitmapMultisample=t,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmap:{get:function(){return this._cacheAsBitmap},set:function(t){var a;this._cacheAsBitmap!==t&&(this._cacheAsBitmap=t,t?(this._cacheData||(this._cacheData=new o),(a=this._cacheData).originalRender=this.render,a.originalRenderCanvas=this.renderCanvas,a.originalUpdateTransform=this.updateTransform,a.originalCalculateBounds=this.calculateBounds,a.originalGetLocalBounds=this.getLocalBounds,a.originalDestroy=this.destroy,a.originalContainsPoint=this.containsPoint,a.originalMask=this._mask,a.originalFilterArea=this.filterArea,this.render=this._renderCached,this.renderCanvas=this._renderCachedCanvas,this.destroy=this._cacheAsBitmapDestroy):((a=this._cacheData).sprite&&this._destroyCachedDisplayObject(),this.render=a.originalRender,this.renderCanvas=a.originalRenderCanvas,this.calculateBounds=a.originalCalculateBounds,this.getLocalBounds=a.originalGetLocalBounds,this.destroy=a.originalDestroy,this.updateTransform=a.originalUpdateTransform,this.containsPoint=a.originalContainsPoint,this._mask=a.originalMask,this.filterArea=a.originalFilterArea))}}}),i.DisplayObject.prototype._renderCached=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObject(t),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._render(t))},i.DisplayObject.prototype._initCachedDisplayObject=function(t){var i;if(!this._cacheData||!this._cacheData.sprite){var s=this.alpha;this.alpha=1,t.batch.flush();var h=this.getLocalBounds(null,!0).clone();if(this.filters&&this.filters.length){var o=this.filters[0].padding;h.pad(o)}h.ceil(n.settings.RESOLUTION);var l=t.renderTexture.current,d=t.renderTexture.sourceFrame.clone(),p=t.renderTexture.destinationFrame.clone(),u=t.projection.transform,_=a.RenderTexture.create({width:h.width,height:h.height,resolution:this.cacheAsBitmapResolution||t.resolution,multisample:null!==(i=this.cacheAsBitmapMultisample)&&void 0!==i?i:t.multisample}),m="cacheAsBitmap_"+r.uid();this._cacheData.textureCacheId=m,a.BaseTexture.addToCache(_.baseTexture,m),a.Texture.addToCache(_,m);var f=this.transform.localTransform.copyTo(c).invert().translate(-h.x,-h.y);this.render=this._cacheData.originalRender,t.render(this,{renderTexture:_,clear:!0,transform:f,skipUpdateTransform:!1}),t.framebuffer.blit(),t.projection.transform=u,t.renderTexture.bind(l,d,p),this.render=this._renderCached,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=s;var D=new e.Sprite(_);D.transform.worldTransform=this.transform.worldTransform,D.anchor.x=-h.x/h.width,D.anchor.y=-h.y/h.height,D.alpha=s,D._bounds=this._bounds,this._cacheData.sprite=D,this.transform._parentID=-1,this.parent?this.updateTransform():(this.enableTempParent(),this.updateTransform(),this.disableTempParent(null)),this.containsPoint=D.containsPoint.bind(D)}},i.DisplayObject.prototype._renderCachedCanvas=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObjectCanvas(t),this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._renderCanvas(t))},i.DisplayObject.prototype._initCachedDisplayObjectCanvas=function(t){if(!this._cacheData||!this._cacheData.sprite){var i=this.getLocalBounds(null,!0),s=this.alpha;this.alpha=1;var h=t.context,o=t._projTransform;i.ceil(n.settings.RESOLUTION);var l=a.RenderTexture.create({width:i.width,height:i.height}),d="cacheAsBitmap_"+r.uid();this._cacheData.textureCacheId=d,a.BaseTexture.addToCache(l.baseTexture,d),a.Texture.addToCache(l,d);var p=c;this.transform.localTransform.copyTo(p),p.invert(),p.tx-=i.x,p.ty-=i.y,this.renderCanvas=this._cacheData.originalRenderCanvas,t.render(this,{renderTexture:l,clear:!0,transform:p,skipUpdateTransform:!1}),t.context=h,t._projTransform=o,this.renderCanvas=this._renderCachedCanvas,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=s;var u=new e.Sprite(l);u.transform.worldTransform=this.transform.worldTransform,u.anchor.x=-i.x/i.width,u.anchor.y=-i.y/i.height,u.alpha=s,u._bounds=this._bounds,this._cacheData.sprite=u,this.transform._parentID=-1,this.parent?this.updateTransform():(this.parent=t._tempDisplayObjectParent,this.updateTransform(),this.parent=null),this.containsPoint=u.containsPoint.bind(u)}},i.DisplayObject.prototype._calculateCachedBounds=function(){this._bounds.clear(),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite._calculateBounds(),this._bounds.updateID=this._boundsID},i.DisplayObject.prototype._getCachedLocalBounds=function(){return this._cacheData.sprite.getLocalBounds(null)},i.DisplayObject.prototype._destroyCachedDisplayObject=function(){this._cacheData.sprite._texture.destroy(!0),this._cacheData.sprite=null,a.BaseTexture.removeFromCache(this._cacheData.textureCacheId),a.Texture.removeFromCache(this._cacheData.textureCacheId),this._cacheData.textureCacheId=null},i.DisplayObject.prototype._cacheAsBitmapDestroy=function(t){this.cacheAsBitmap=!1,this.destroy(t)},t.CacheData=o,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI,PIXI,PIXI,PIXI.utils,PIXI,PIXI);
//# sourceMappingURL=mixin-cache-as-bitmap.min.js.map
