/**
 * 🚨 内存使用阈值告警系统
 * 监控内存使用并在达到阈值时触发告警和自动清理
 */

export interface MemoryAlert {
  level: 'info' | 'warning' | 'critical' | 'emergency';
  message: string;
  currentMemory: number;
  threshold: number;
  timestamp: number;
  actions: string[];
}

export class MemoryAlertSystem {
  private static instance: MemoryAlertSystem;
  private alerts: MemoryAlert[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  
  // 内存阈值配置（字节）
  private thresholds = {
    info: 300 * 1024 * 1024,      // 300MB - 信息级别
    warning: 500 * 1024 * 1024,   // 500MB - 警告级别
    critical: 700 * 1024 * 1024,  // 700MB - 严重级别
    emergency: 900 * 1024 * 1024  // 900MB - 紧急级别
  };

  private constructor() {}

  public static getInstance(): MemoryAlertSystem {
    if (!MemoryAlertSystem.instance) {
      MemoryAlertSystem.instance = new MemoryAlertSystem();
    }
    return MemoryAlertSystem.instance;
  }

  /**
   * 🚀 启动内存监控
   */
  public startMonitoring(): void {
    if (this.monitoringInterval) {
      console.log('⚠️ 内存监控已在运行');
      return;
    }

    this.monitoringInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, 15000); // 每15秒检查一次

    console.log('🚨 内存告警系统已启动');
  }

  /**
   * 🛑 停止内存监控
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('🛑 内存告警系统已停止');
    }
  }

  /**
   * 🔍 检查内存使用情况
   */
  private checkMemoryUsage(): void {
    try {
      const memoryInfo = this.getMemoryInfo();
      if (!memoryInfo) return;

      const { usedMemory } = memoryInfo;
      const alertLevel = this.determineAlertLevel(usedMemory);

      if (alertLevel) {
        const alert = this.createAlert(alertLevel, usedMemory);
        this.handleAlert(alert);
      }

    } catch (error) {
      console.warn('⚠️ 内存检查失败:', error);
    }
  }

  /**
   * 📊 获取内存信息
   */
  private getMemoryInfo(): { usedMemory: number; totalMemory: number } | null {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      return {
        usedMemory: memory.usedJSHeapSize,
        totalMemory: memory.totalJSHeapSize
      };
    }
    return null;
  }

  /**
   * 🎯 确定告警级别
   */
  private determineAlertLevel(usedMemory: number): keyof typeof this.thresholds | null {
    if (usedMemory >= this.thresholds.emergency) {
      return 'emergency';
    } else if (usedMemory >= this.thresholds.critical) {
      return 'critical';
    } else if (usedMemory >= this.thresholds.warning) {
      return 'warning';
    } else if (usedMemory >= this.thresholds.info) {
      return 'info';
    }
    return null;
  }

  /**
   * 🚨 创建告警
   */
  private createAlert(level: keyof typeof this.thresholds, currentMemory: number): MemoryAlert {
    const threshold = this.thresholds[level];
    const actions: string[] = [];
    
    let message = '';
    switch (level) {
      case 'info':
        message = `内存使用达到信息级别`;
        actions.push('开始监控');
        break;
      case 'warning':
        message = `内存使用达到警告级别`;
        actions.push('清理过期缓存', '优化图片显示');
        break;
      case 'critical':
        message = `内存使用达到严重级别`;
        actions.push('强制清理缓存', '压缩Base64图片', '限制并发加载');
        break;
      case 'emergency':
        message = `内存使用达到紧急级别`;
        actions.push('紧急内存清理', '关闭非必要连接', '强制垃圾回收');
        break;
    }

    return {
      level,
      message: `${message}: ${(currentMemory / 1024 / 1024).toFixed(1)}MB`,
      currentMemory,
      threshold,
      timestamp: Date.now(),
      actions
    };
  }

  /**
   * 🔧 处理告警
   */
  private async handleAlert(alert: MemoryAlert): Promise<void> {
    // 避免重复告警（5分钟内相同级别）
    const recentSimilarAlert = this.alerts.find(a => 
      a.level === alert.level && 
      alert.timestamp - a.timestamp < 5 * 60 * 1000
    );

    if (recentSimilarAlert) {
      return; // 跳过重复告警
    }

    // 记录告警
    this.alerts.push(alert);
    this.trimAlertHistory();

    // 输出告警信息
    this.logAlert(alert);

    // 执行自动清理动作
    await this.executeAutoActions(alert);

    // 发送全局事件
    this.broadcastAlert(alert);
  }

  /**
   * 📝 记录告警信息
   */
  private logAlert(alert: MemoryAlert): void {
    const emoji = {
      info: 'ℹ️',
      warning: '⚠️',
      critical: '🚨',
      emergency: '🆘'
    }[alert.level];

    console.log(`${emoji} [内存告警] ${alert.message}`);
    console.log(`📊 当前内存: ${(alert.currentMemory / 1024 / 1024).toFixed(1)}MB / 阈值: ${(alert.threshold / 1024 / 1024).toFixed(1)}MB`);
    console.log(`🔧 自动执行: ${alert.actions.join(', ')}`);
  }

  /**
   * 🤖 执行自动清理动作
   */
  private async executeAutoActions(alert: MemoryAlert): Promise<void> {
    try {
      switch (alert.level) {
        case 'warning':
          await this.executeWarningActions();
          break;
        case 'critical':
          await this.executeCriticalActions();
          break;
        case 'emergency':
          await this.executeEmergencyActions();
          break;
      }
    } catch (error) {
      console.error('❌ 自动清理动作执行失败:', error);
    }
  }

  /**
   * ⚠️ 执行警告级别动作
   */
  private async executeWarningActions(): Promise<void> {
    // 清理SafeImage缓存
    window.dispatchEvent(new CustomEvent('clear-image-cache'));
    
    // 清理ComicGallery缓存
    window.dispatchEvent(new CustomEvent('clear-comic-gallery-cache'));
    
    console.log('⚠️ 执行警告级别清理: 清理图片缓存');
  }

  /**
   * 🚨 执行严重级别动作
   */
  private async executeCriticalActions(): Promise<void> {
    // 执行警告级别动作
    await this.executeWarningActions();
    
    // 触发高级内存优化器
    const advancedOptimizer = (window as any).advancedMemoryOptimizer;
    if (advancedOptimizer && typeof advancedOptimizer.performDeepCleanup === 'function') {
      await advancedOptimizer.performDeepCleanup();
    }
    
    // 强制垃圾回收
    if ((window as any).gc) {
      (window as any).gc();
    }
    
    console.log('🚨 执行严重级别清理: 深度内存清理');
  }

  /**
   * 🆘 执行紧急级别动作
   */
  private async executeEmergencyActions(): Promise<void> {
    // 执行严重级别动作
    await this.executeCriticalActions();
    
    // 清理所有WebSocket连接
    const cleanupWS = (window as any).cleanupAllWebSocketConnections;
    if (cleanupWS && typeof cleanupWS === 'function') {
      cleanupWS();
    }
    
    // 压缩所有Base64图片
    this.compressAllBase64Images();
    
    console.log('🆘 执行紧急级别清理: 全面内存回收');
  }

  /**
   * 🗜️ 压缩所有Base64图片
   */
  private compressAllBase64Images(): void {
    const images = document.querySelectorAll('img[src^="data:image"]');
    let compressedCount = 0;
    
    images.forEach((img: Element) => {
      const imgElement = img as HTMLImageElement;
      if (imgElement.src.length > 100 * 1024) { // 100KB以上才压缩
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (ctx) {
            canvas.width = Math.min(imgElement.naturalWidth, 800);
            canvas.height = Math.min(imgElement.naturalHeight, 600);
            ctx.drawImage(imgElement, 0, 0, canvas.width, canvas.height);
            const compressed = canvas.toDataURL('image/jpeg', 0.6);
            
            if (compressed.length < imgElement.src.length) {
              imgElement.src = compressed;
              compressedCount++;
            }
          }
        } catch (error) {
          console.warn('⚠️ 图片压缩失败:', error);
        }
      }
    });
    
    if (compressedCount > 0) {
      console.log(`🗜️ 紧急压缩了${compressedCount}张Base64图片`);
    }
  }

  /**
   * 📻 广播告警事件
   */
  private broadcastAlert(alert: MemoryAlert): void {
    window.dispatchEvent(new CustomEvent('memory-alert', {
      detail: alert
    }));
  }

  /**
   * 🗂️ 整理告警历史
   */
  private trimAlertHistory(): void {
    // 只保留最近50个告警
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(-50);
    }
    
    // 清理1小时前的告警
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.alerts = this.alerts.filter(alert => alert.timestamp > oneHourAgo);
  }

  /**
   * 📊 获取告警统计
   */
  public getAlertStats(): {
    total: number;
    byLevel: Record<string, number>;
    recent: MemoryAlert[];
  } {
    const stats = {
      total: this.alerts.length,
      byLevel: { info: 0, warning: 0, critical: 0, emergency: 0 },
      recent: this.alerts.slice(-10)
    };

    this.alerts.forEach(alert => {
      stats.byLevel[alert.level]++;
    });

    return stats;
  }

  /**
   * 🔧 更新阈值配置
   */
  public updateThresholds(newThresholds: Partial<typeof this.thresholds>): void {
    Object.assign(this.thresholds, newThresholds);
    console.log('🔧 内存阈值已更新:', this.thresholds);
  }
}

// 导出单例实例
export const memoryAlertSystem = MemoryAlertSystem.getInstance();

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).memoryAlertSystem = memoryAlertSystem;
  (window as any).getMemoryAlerts = () => memoryAlertSystem.getAlertStats();
}