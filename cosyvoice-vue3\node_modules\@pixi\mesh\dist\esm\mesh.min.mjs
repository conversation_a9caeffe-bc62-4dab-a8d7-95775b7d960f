/*!
 * @pixi/mesh - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mesh is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{State as t,Program as e,TextureMatrix as r,Shader as i,<PERSON>uff<PERSON> as n,Geometry as o}from"@pixi/core";import{Point as a,Polygon as s,Matrix as u}from"@pixi/math";import{DRAW_MODES as h,TYPES as l}from"@pixi/constants";import{Container as f}from"@pixi/display";import{settings as d}from"@pixi/settings";import{premultiplyTintToRgba as p}from"@pixi/utils";var c=function(t,e){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},c(t,e)};function m(t,e){function r(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var y=function(){function t(t,e){this.uvBuffer=t,this.uvMatrix=e,this.data=null,this._bufferUpdateId=-1,this._textureUpdateId=-1,this._updateID=0}return t.prototype.update=function(t){if(t||this._bufferUpdateId!==this.uvBuffer._updateID||this._textureUpdateId!==this.uvMatrix._updateID){this._bufferUpdateId=this.uvBuffer._updateID,this._textureUpdateId=this.uvMatrix._updateID;var e=this.uvBuffer.data;this.data&&this.data.length===e.length||(this.data=new Float32Array(e.length)),this.uvMatrix.multiplyUvs(e,this.data),this._updateID++}},t}(),x=new a,v=new s,g=function(e){function r(r,i,n,o){void 0===o&&(o=h.TRIANGLES);var a=e.call(this)||this;return a.geometry=r,a.shader=i,a.state=n||t.for2d(),a.drawMode=o,a.start=0,a.size=0,a.uvs=null,a.indices=null,a.vertexData=new Float32Array(1),a.vertexDirty=-1,a._transformID=-1,a._roundPixels=d.ROUND_PIXELS,a.batchUvs=null,a}return m(r,e),Object.defineProperty(r.prototype,"geometry",{get:function(){return this._geometry},set:function(t){this._geometry!==t&&(this._geometry&&(this._geometry.refCount--,0===this._geometry.refCount&&this._geometry.dispose()),this._geometry=t,this._geometry&&this._geometry.refCount++,this.vertexDirty=-1)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"uvBuffer",{get:function(){return this.geometry.buffers[1]},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"verticesBuffer",{get:function(){return this.geometry.buffers[0]},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"material",{get:function(){return this.shader},set:function(t){this.shader=t},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"blendMode",{get:function(){return this.state.blendMode},set:function(t){this.state.blendMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(t){this._roundPixels!==t&&(this._transformID=-1),this._roundPixels=t},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"tint",{get:function(){return"tint"in this.shader?this.shader.tint:null},set:function(t){this.shader.tint=t},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"texture",{get:function(){return"texture"in this.shader?this.shader.texture:null},set:function(t){this.shader.texture=t},enumerable:!1,configurable:!0}),r.prototype._render=function(t){var e=this.geometry.buffers[0].data;this.shader.batchable&&this.drawMode===h.TRIANGLES&&e.length<2*r.BATCHABLE_SIZE?this._renderToBatch(t):this._renderDefault(t)},r.prototype._renderDefault=function(t){var e=this.shader;e.alpha=this.worldAlpha,e.update&&e.update(),t.batch.flush(),e.uniforms.translationMatrix=this.transform.worldTransform.toArray(!0),t.shader.bind(e),t.state.set(this.state),t.geometry.bind(this.geometry,e),t.geometry.draw(this.drawMode,this.size,this.start,this.geometry.instanceCount)},r.prototype._renderToBatch=function(t){var e=this.geometry,r=this.shader;r.uvMatrix&&(r.uvMatrix.update(),this.calculateUvs()),this.calculateVertices(),this.indices=e.indexBuffer.data,this._tintRGB=r._tintRGB,this._texture=r.texture;var i=this.material.pluginName;t.batch.setObjectRenderer(t.plugins[i]),t.plugins[i].render(this)},r.prototype.calculateVertices=function(){var t=this.geometry.buffers[0],e=t.data,r=t._updateID;if(r!==this.vertexDirty||this._transformID!==this.transform._worldID){this._transformID=this.transform._worldID,this.vertexData.length!==e.length&&(this.vertexData=new Float32Array(e.length));for(var i=this.transform.worldTransform,n=i.a,o=i.b,a=i.c,s=i.d,u=i.tx,h=i.ty,l=this.vertexData,f=0;f<l.length/2;f++){var p=e[2*f],c=e[2*f+1];l[2*f]=n*p+a*c+u,l[2*f+1]=o*p+s*c+h}if(this._roundPixels){var m=d.RESOLUTION;for(f=0;f<l.length;++f)l[f]=Math.round((l[f]*m|0)/m)}this.vertexDirty=r}},r.prototype.calculateUvs=function(){var t=this.geometry.buffers[1],e=this.shader;e.uvMatrix.isSimple?this.uvs=t.data:(this.batchUvs||(this.batchUvs=new y(t,e.uvMatrix)),this.batchUvs.update(),this.uvs=this.batchUvs.data)},r.prototype._calculateBounds=function(){this.calculateVertices(),this._bounds.addVertexData(this.vertexData,0,this.vertexData.length)},r.prototype.containsPoint=function(t){if(!this.getBounds().contains(t.x,t.y))return!1;this.worldTransform.applyInverse(t,x);for(var e=this.geometry.getBuffer("aVertexPosition").data,r=v.points,i=this.geometry.getIndex().data,n=i.length,o=4===this.drawMode?3:1,a=0;a+2<n;a+=o){var s=2*i[a],u=2*i[a+1],h=2*i[a+2];if(r[0]=e[s],r[1]=e[s+1],r[2]=e[u],r[3]=e[u+1],r[4]=e[h],r[5]=e[h+1],v.contains(x.x,x.y))return!0}return!1},r.prototype.destroy=function(t){e.prototype.destroy.call(this,t),this._cachedTexture&&(this._cachedTexture.destroy(),this._cachedTexture=null),this.geometry=null,this.shader=null,this.state=null,this.uvs=null,this.indices=null,this.vertexData=null},r.BATCHABLE_SIZE=100,r}(f),b=function(t){function i(i,n){var o=this,a={uSampler:i,alpha:1,uTextureMatrix:u.IDENTITY,uColor:new Float32Array([1,1,1,1])};return(n=Object.assign({tint:16777215,alpha:1,pluginName:"batch"},n)).uniforms&&Object.assign(a,n.uniforms),(o=t.call(this,n.program||e.from("attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTextureMatrix;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;\n}\n","varying vec2 vTextureCoord;\nuniform vec4 uColor;\n\nuniform sampler2D uSampler;\n\nvoid main(void)\n{\n    gl_FragColor = texture2D(uSampler, vTextureCoord) * uColor;\n}\n"),a)||this)._colorDirty=!1,o.uvMatrix=new r(i),o.batchable=void 0===n.program,o.pluginName=n.pluginName,o.tint=n.tint,o.alpha=n.alpha,o}return m(i,t),Object.defineProperty(i.prototype,"texture",{get:function(){return this.uniforms.uSampler},set:function(t){this.uniforms.uSampler!==t&&(!this.uniforms.uSampler.baseTexture.alphaMode!=!t.baseTexture.alphaMode&&(this._colorDirty=!0),this.uniforms.uSampler=t,this.uvMatrix.texture=t)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"alpha",{get:function(){return this._alpha},set:function(t){t!==this._alpha&&(this._alpha=t,this._colorDirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"tint",{get:function(){return this._tint},set:function(t){t!==this._tint&&(this._tint=t,this._tintRGB=(t>>16)+(65280&t)+((255&t)<<16),this._colorDirty=!0)},enumerable:!1,configurable:!0}),i.prototype.update=function(){if(this._colorDirty){this._colorDirty=!1;var t=this.texture.baseTexture;p(this._tint,this._alpha,this.uniforms.uColor,t.alphaMode)}this.uvMatrix.update()&&(this.uniforms.uTextureMatrix=this.uvMatrix.mapCoord)},i}(i),_=function(t){function e(e,r,i){var o=t.call(this)||this,a=new n(e),s=new n(r,!0),u=new n(i,!0,!0);return o.addAttribute("aVertexPosition",a,2,!1,l.FLOAT).addAttribute("aTextureCoord",s,2,!1,l.FLOAT).addIndex(u),o._updateId=-1,o}return m(e,t),Object.defineProperty(e.prototype,"vertexDirtyId",{get:function(){return this.buffers[0]._updateID},enumerable:!1,configurable:!0}),e}(o);export{g as Mesh,y as MeshBatchUvs,_ as MeshGeometry,b as MeshMaterial};
//# sourceMappingURL=mesh.min.mjs.map
