{"version": 3, "file": "polyfill.js", "sources": ["../../src/globalThis.ts", "../../../../node_modules/promise-polyfill/src/finally.js", "../../../../node_modules/promise-polyfill/src/allSettled.js", "../../../../node_modules/promise-polyfill/src/index.js", "../../src/Promise.ts", "../../../../node_modules/object-assign/index.js", "../../src/Object.assign.ts", "../../src/requestAnimationFrame.ts", "../../src/Math.sign.ts", "../../src/Number.isInteger.ts", "../../src/index.ts"], "sourcesContent": ["if (typeof globalThis === 'undefined')\n{\n    if (typeof self !== 'undefined')\n    {\n        // covers browsers\n        // @ts-expect-error not-writable ts(2540) error only on node\n        self.globalThis = self;\n    }\n    else if (typeof global !== 'undefined')\n    {\n        // covers versions of Node < 12\n        // @ts-expect-error not-writable ts(2540) error only on node\n        global.globalThis = global;\n    }\n}\n", "/**\n * @this {Promise}\n */\nfunction finallyConstructor(callback) {\n  var constructor = this.constructor;\n  return this.then(\n    function(value) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        return value;\n      });\n    },\n    function(reason) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        // @ts-ignore\n        return constructor.reject(reason);\n      });\n    }\n  );\n}\n\nexport default finallyConstructor;\n", "function allSettled(arr) {\n  var P = this;\n  return new P(function(resolve, reject) {\n    if (!(arr && typeof arr.length !== 'undefined')) {\n      return reject(\n        new TypeError(\n          typeof arr +\n            ' ' +\n            arr +\n            ' is not iterable(cannot read property Symbol(Symbol.iterator))'\n        )\n      );\n    }\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      if (val && (typeof val === 'object' || typeof val === 'function')) {\n        var then = val.then;\n        if (typeof then === 'function') {\n          then.call(\n            val,\n            function(val) {\n              res(i, val);\n            },\n            function(e) {\n              args[i] = { status: 'rejected', reason: e };\n              if (--remaining === 0) {\n                resolve(args);\n              }\n            }\n          );\n          return;\n        }\n      }\n      args[i] = { status: 'fulfilled', value: val };\n      if (--remaining === 0) {\n        resolve(args);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n}\n\nexport default allSettled;\n", "import promiseFinally from './finally';\nimport allSettled from './allSettled';\n\n// Store setTimeout reference so promise-polyfill will be unaffected by\n// other code modifying setTimeout (like sinon.useFakeTimers())\nvar setTimeoutFunc = setTimeout;\n\nfunction isArray(x) {\n  return Boolean(x && typeof x.length !== 'undefined');\n}\n\nfunction noop() {}\n\n// Polyfill for Function.prototype.bind\nfunction bind(fn, thisArg) {\n  return function() {\n    fn.apply(thisArg, arguments);\n  };\n}\n\n/**\n * @constructor\n * @param {Function} fn\n */\nfunction Promise(fn) {\n  if (!(this instanceof Promise))\n    throw new TypeError('Promises must be constructed via new');\n  if (typeof fn !== 'function') throw new TypeError('not a function');\n  /** @type {!number} */\n  this._state = 0;\n  /** @type {!boolean} */\n  this._handled = false;\n  /** @type {Promise|undefined} */\n  this._value = undefined;\n  /** @type {!Array<!Function>} */\n  this._deferreds = [];\n\n  doResolve(fn, this);\n}\n\nfunction handle(self, deferred) {\n  while (self._state === 3) {\n    self = self._value;\n  }\n  if (self._state === 0) {\n    self._deferreds.push(deferred);\n    return;\n  }\n  self._handled = true;\n  Promise._immediateFn(function() {\n    var cb = self._state === 1 ? deferred.onFulfilled : deferred.onRejected;\n    if (cb === null) {\n      (self._state === 1 ? resolve : reject)(deferred.promise, self._value);\n      return;\n    }\n    var ret;\n    try {\n      ret = cb(self._value);\n    } catch (e) {\n      reject(deferred.promise, e);\n      return;\n    }\n    resolve(deferred.promise, ret);\n  });\n}\n\nfunction resolve(self, newValue) {\n  try {\n    // Promise Resolution Procedure: https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    if (newValue === self)\n      throw new TypeError('A promise cannot be resolved with itself.');\n    if (\n      newValue &&\n      (typeof newValue === 'object' || typeof newValue === 'function')\n    ) {\n      var then = newValue.then;\n      if (newValue instanceof Promise) {\n        self._state = 3;\n        self._value = newValue;\n        finale(self);\n        return;\n      } else if (typeof then === 'function') {\n        doResolve(bind(then, newValue), self);\n        return;\n      }\n    }\n    self._state = 1;\n    self._value = newValue;\n    finale(self);\n  } catch (e) {\n    reject(self, e);\n  }\n}\n\nfunction reject(self, newValue) {\n  self._state = 2;\n  self._value = newValue;\n  finale(self);\n}\n\nfunction finale(self) {\n  if (self._state === 2 && self._deferreds.length === 0) {\n    Promise._immediateFn(function() {\n      if (!self._handled) {\n        Promise._unhandledRejectionFn(self._value);\n      }\n    });\n  }\n\n  for (var i = 0, len = self._deferreds.length; i < len; i++) {\n    handle(self, self._deferreds[i]);\n  }\n  self._deferreds = null;\n}\n\n/**\n * @constructor\n */\nfunction Handler(onFulfilled, onRejected, promise) {\n  this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n  this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n  this.promise = promise;\n}\n\n/**\n * Take a potentially misbehaving resolver function and make sure\n * onFulfilled and onRejected are only called once.\n *\n * Makes no guarantees about asynchrony.\n */\nfunction doResolve(fn, self) {\n  var done = false;\n  try {\n    fn(\n      function(value) {\n        if (done) return;\n        done = true;\n        resolve(self, value);\n      },\n      function(reason) {\n        if (done) return;\n        done = true;\n        reject(self, reason);\n      }\n    );\n  } catch (ex) {\n    if (done) return;\n    done = true;\n    reject(self, ex);\n  }\n}\n\nPromise.prototype['catch'] = function(onRejected) {\n  return this.then(null, onRejected);\n};\n\nPromise.prototype.then = function(onFulfilled, onRejected) {\n  // @ts-ignore\n  var prom = new this.constructor(noop);\n\n  handle(this, new Handler(onFulfilled, onRejected, prom));\n  return prom;\n};\n\nPromise.prototype['finally'] = promiseFinally;\n\nPromise.all = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.all accepts an array'));\n    }\n\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      try {\n        if (val && (typeof val === 'object' || typeof val === 'function')) {\n          var then = val.then;\n          if (typeof then === 'function') {\n            then.call(\n              val,\n              function(val) {\n                res(i, val);\n              },\n              reject\n            );\n            return;\n          }\n        }\n        args[i] = val;\n        if (--remaining === 0) {\n          resolve(args);\n        }\n      } catch (ex) {\n        reject(ex);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n};\n\nPromise.allSettled = allSettled;\n\nPromise.resolve = function(value) {\n  if (value && typeof value === 'object' && value.constructor === Promise) {\n    return value;\n  }\n\n  return new Promise(function(resolve) {\n    resolve(value);\n  });\n};\n\nPromise.reject = function(value) {\n  return new Promise(function(resolve, reject) {\n    reject(value);\n  });\n};\n\nPromise.race = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.race accepts an array'));\n    }\n\n    for (var i = 0, len = arr.length; i < len; i++) {\n      Promise.resolve(arr[i]).then(resolve, reject);\n    }\n  });\n};\n\n// Use polyfill for setImmediate for performance gains\nPromise._immediateFn =\n  // @ts-ignore\n  (typeof setImmediate === 'function' &&\n    function(fn) {\n      // @ts-ignore\n      setImmediate(fn);\n    }) ||\n  function(fn) {\n    setTimeoutFunc(fn, 0);\n  };\n\nPromise._unhandledRejectionFn = function _unhandledRejectionFn(err) {\n  if (typeof console !== 'undefined' && console) {\n    console.warn('Possible Unhandled Promise Rejection:', err); // eslint-disable-line no-console\n  }\n};\n\nexport default Promise;\n", "import Polyfill from 'promise-polyfill';\n\n// Support for IE 9 - 11 which does not include Promises\nif (!globalThis.Promise)\n{\n    globalThis.Promise = Polyfill;\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "// References:\n// https://github.com/sindresorhus/object-assign\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n\nimport objectAssign from 'object-assign';\n\nif (!Object.assign)\n{\n    Object.assign = objectAssign;\n}\n", "// References:\n// http://paulirish.com/2011/requestanimationframe-for-smart-animating/\n// https://gist.github.com/1579671\n// http://updates.html5rocks.com/2012/05/requestAnimationFrame-API-now-with-sub-millisecond-precision\n// https://gist.github.com/timhall/4078614\n// https://github.com/Financial-Times/polyfill-service/tree/master/polyfills/requestAnimationFrame\n\n// Expected to be used with Browserfiy\n// Browserify automatically detects the use of `global` and passes the\n// correct reference of `global`, `globalThis`, and finally `window`\n\nconst ONE_FRAME_TIME = 16;\n\n// Date.now\nif (!(Date.now && Date.prototype.getTime))\n{\n    Date.now = function now(): number\n    {\n        return new Date().getTime();\n    };\n}\n\n// performance.now\nif (!(globalThis.performance && globalThis.performance.now))\n{\n    const startTime = Date.now();\n\n    if (!globalThis.performance)\n    {\n        (globalThis as any).performance = {};\n    }\n\n    globalThis.performance.now = (): number => Date.now() - startTime;\n}\n\n// requestAnimationFrame\nlet lastTime = Date.now();\nconst vendors = ['ms', 'moz', 'webkit', 'o'];\n\nfor (let x = 0; x < vendors.length && !globalThis.requestAnimationFrame; ++x)\n{\n    const p = vendors[x];\n\n    globalThis.requestAnimationFrame = (globalThis as any)[`${p}RequestAnimationFrame`];\n    globalThis.cancelAnimationFrame = (globalThis as any)[`${p}CancelAnimationFrame`]\n        || (globalThis as any)[`${p}CancelRequestAnimationFrame`];\n}\n\nif (!globalThis.requestAnimationFrame)\n{\n    globalThis.requestAnimationFrame = (callback: (...parms: any[]) => void): number =>\n    {\n        if (typeof callback !== 'function')\n        {\n            throw new TypeError(`${callback}is not a function`);\n        }\n\n        const currentTime = Date.now();\n        let delay = ONE_FRAME_TIME + lastTime - currentTime;\n\n        if (delay < 0)\n        {\n            delay = 0;\n        }\n\n        lastTime = currentTime;\n\n        return globalThis.self.setTimeout(() =>\n        {\n            lastTime = Date.now();\n            callback(performance.now());\n        }, delay);\n    };\n}\n\nif (!globalThis.cancelAnimationFrame)\n{\n    globalThis.cancelAnimationFrame = (id: number): void => clearTimeout(id);\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/sign\n\nif (!Math.sign)\n{\n    Math.sign = function mathSign(x): number\n    {\n        x = Number(x);\n\n        if (x === 0 || isNaN(x))\n        {\n            return x;\n        }\n\n        return x > 0 ? 1 : -1;\n    };\n}\n", "// References:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n\nif (!Number.isInteger)\n{\n    Number.isInteger = function numberIsInteger(value): boolean\n    {\n        return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n    };\n}\n", "import './globalThis';\nimport './Promise';\nimport './Object.assign';\nimport './requestAnimationFrame';\nimport './Math.sign';\nimport './Number.isInteger';\n\nif (!globalThis.ArrayBuffer)\n{\n    (globalThis as any).ArrayBuffer = Array;\n}\n\nif (!globalThis.Float32Array)\n{\n    (globalThis as any).Float32Array = Array;\n}\n\nif (!globalThis.Uint32Array)\n{\n    (globalThis as any).Uint32Array = Array;\n}\n\nif (!globalThis.Uint16Array)\n{\n    (globalThis as any).Uint16Array = Array;\n}\n\nif (!globalThis.Uint8Array)\n{\n    (globalThis as any).Uint8Array = Array;\n}\n\nif (!globalThis.Int32Array)\n{\n    (globalThis as any).Int32Array = Array;\n}\n"], "names": ["Promise", "promiseFinally", "Polyfill", "arguments"], "mappings": ";;;;;;;;;;;IAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EACrC;IACI,IAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAC/B;;;IAGI,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC1B,KAAA;IACI,SAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EACtC;;;IAGI,QAAA,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC;IAC9B,KAAA;IACJ;;ICdD;IACA;IACA;IACA,SAAS,kBAAkB,CAAC,QAAQ,EAAE;IACtC,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IACrC,EAAE,OAAO,IAAI,CAAC,IAAI;IAClB,IAAI,SAAS,KAAK,EAAE;IACpB;IACA,MAAM,OAAO,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW;IAC7D,QAAQ,OAAO,KAAK,CAAC;IACrB,OAAO,CAAC,CAAC;IACT,KAAK;IACL,IAAI,SAAS,MAAM,EAAE;IACrB;IACA,MAAM,OAAO,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW;IAC7D;IACA,QAAQ,OAAO,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC1C,OAAO,CAAC,CAAC;IACT,KAAK;IACL,GAAG,CAAC;IACJ;;ICpBA,SAAS,UAAU,CAAC,GAAG,EAAE;IACzB,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;IACf,EAAE,OAAO,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;IACzC,IAAI,IAAI,EAAE,GAAG,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE;IACrD,MAAM,OAAO,MAAM;IACnB,QAAQ,IAAI,SAAS;IACrB,UAAU,OAAO,GAAG;IACpB,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,gEAAgE;IAC5E,SAAS;IACT,OAAO,CAAC;IACR,KAAK;IACL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,EAAA,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC,EAAA;IAC9C,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC;IACA,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE;IACzB,MAAM,IAAI,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,EAAE;IACzE,QAAQ,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IAC5B,QAAQ,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;IACxC,UAAU,IAAI,CAAC,IAAI;IACnB,YAAY,GAAG;IACf,YAAY,SAAS,GAAG,EAAE;IAC1B,cAAc,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1B,aAAa;IACb,YAAY,SAAS,CAAC,EAAE;IACxB,cAAc,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;IAC1D,cAAc,IAAI,EAAE,SAAS,KAAK,CAAC,EAAE;IACrC,gBAAgB,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,eAAe;IACf,aAAa;IACb,WAAW,CAAC;IACZ,UAAU,OAAO;IACjB,SAAS;IACT,OAAO;IACP,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;IACpD,MAAM,IAAI,EAAE,SAAS,KAAK,CAAC,EAAE;IAC7B,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC;IACtB,OAAO;IACP,KAAK;AACL;IACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC1C,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,KAAK;IACL,GAAG,CAAC,CAAC;IACL;;IC3CA;IACA;IACA,IAAI,cAAc,GAAG,UAAU,CAAC;AAChC;IACA,SAAS,OAAO,CAAC,CAAC,EAAE;IACpB,EAAE,OAAO,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;IACvD,CAAC;AACD;IACA,SAAS,IAAI,GAAG,EAAE;AAClB;IACA;IACA,SAAS,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE;IAC3B,EAAE,OAAO,WAAW;IACpB,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACjC,GAAG,CAAC;IACJ,CAAC;AACD;IACA;IACA;IACA;IACA;IACA,SAASA,SAAO,CAAC,EAAE,EAAE;IACrB,EAAE,IAAI,EAAE,IAAI,YAAYA,SAAO,CAAC;IAChC,IAAA,EAAI,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAChE,EAAE,IAAI,OAAO,EAAE,KAAK,UAAU,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC,EAAA;IACtE;IACA,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB;IACA,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB;IACA,EAAE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B;IACA,EAAE,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACvB;IACA,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACtB,CAAC;AACD;IACA,SAAS,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE;IAChC,EAAE,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;IAC5B,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;IACvB,GAAG;IACH,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;IACzB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,OAAO;IACX,GAAG;IACH,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,EAAEA,SAAO,CAAC,YAAY,CAAC,WAAW;IAClC,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;IAC5E,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE;IACrB,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM,EAAE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5E,MAAM,OAAO;IACb,KAAK;IACL,IAAI,IAAI,GAAG,CAAC;IACZ,IAAI,IAAI;IACR,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,KAAK,CAAC,OAAO,CAAC,EAAE;IAChB,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAClC,MAAM,OAAO;IACb,KAAK;IACL,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,GAAG,CAAC,CAAC;IACL,CAAC;AACD;IACA,SAAS,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE;IACjC,EAAE,IAAI;IACN;IACA,IAAI,IAAI,QAAQ,KAAK,IAAI;IACzB,MAAA,EAAM,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAC,EAAA;IACvE,IAAI;IACJ,MAAM,QAAQ;IACd,OAAO,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,UAAU,CAAC;IACtE,MAAM;IACN,MAAM,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;IAC/B,MAAM,IAAI,QAAQ,YAAYA,SAAO,EAAE;IACvC,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,QAAQ,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;IACrB,QAAQ,OAAO;IACf,OAAO,MAAM,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;IAC7C,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9C,QAAQ,OAAO;IACf,OAAO;IACP,KAAK;IACL,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;IACjB,GAAG,CAAC,OAAO,CAAC,EAAE;IACd,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACpB,GAAG;IACH,CAAC;AACD;IACA,SAAS,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE;IAChC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,EAAE,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;IACzB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;AACD;IACA,SAAS,MAAM,CAAC,IAAI,EAAE;IACtB,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;IACzD,IAAIA,SAAO,CAAC,YAAY,CAAC,WAAW;IACpC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IAC1B,QAAQA,SAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO;IACP,KAAK,CAAC,CAAC;IACP,GAAG;AACH;IACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC9D,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,GAAG;IACH,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;AACD;IACA;IACA;IACA;IACA,SAAS,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;IACnD,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG,IAAI,CAAC;IAC5E,EAAE,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC;IACzE,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;AACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE;IAC7B,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC;IACnB,EAAE,IAAI;IACN,IAAI,EAAE;IACN,MAAM,SAAS,KAAK,EAAE;IACtB,QAAQ,IAAI,IAAI,EAAA,EAAE,OAAO,EAAA;IACzB,QAAQ,IAAI,GAAG,IAAI,CAAC;IACpB,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7B,OAAO;IACP,MAAM,SAAS,MAAM,EAAE;IACvB,QAAQ,IAAI,IAAI,EAAA,EAAE,OAAO,EAAA;IACzB,QAAQ,IAAI,GAAG,IAAI,CAAC;IACpB,QAAQ,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7B,OAAO;IACP,KAAK,CAAC;IACN,GAAG,CAAC,OAAO,EAAE,EAAE;IACf,IAAI,IAAI,IAAI,EAAA,EAAE,OAAO,EAAA;IACrB,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACrB,GAAG;IACH,CAAC;AACD;AACAA,aAAO,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,SAAS,UAAU,EAAE;IAClD,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACrC,CAAC,CAAC;AACF;AACAA,aAAO,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,WAAW,EAAE,UAAU,EAAE;IAC3D;IACA,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACxC;IACA,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3D,EAAE,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACF;AACAA,aAAO,CAAC,SAAS,CAAC,SAAS,CAAC,GAAGC,kBAAc,CAAC;AAC9C;AACAD,aAAO,CAAC,GAAG,GAAG,SAAS,GAAG,EAAE;IAC5B,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;IAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;IACvB,MAAM,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC;IACnE,KAAK;AACL;IACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,EAAA,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC,EAAA;IAC9C,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC;IACA,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE;IACzB,MAAM,IAAI;IACV,QAAQ,IAAI,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,EAAE;IAC3E,UAAU,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,UAAU,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;IAC1C,YAAY,IAAI,CAAC,IAAI;IACrB,cAAc,GAAG;IACjB,cAAc,SAAS,GAAG,EAAE;IAC5B,gBAAgB,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC5B,eAAe;IACf,cAAc,MAAM;IACpB,aAAa,CAAC;IACd,YAAY,OAAO;IACnB,WAAW;IACX,SAAS;IACT,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IACtB,QAAQ,IAAI,EAAE,SAAS,KAAK,CAAC,EAAE;IAC/B,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC;IACxB,SAAS;IACT,OAAO,CAAC,OAAO,EAAE,EAAE;IACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;IACnB,OAAO;IACP,KAAK;AACL;IACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC1C,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,KAAK;IACL,GAAG,CAAC,CAAC;IACL,CAAC,CAAC;AACF;AACAA,aAAO,CAAC,UAAU,GAAG,UAAU,CAAC;AAChC;AACAA,aAAO,CAAC,OAAO,GAAG,SAAS,KAAK,EAAE;IAClC,EAAE,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,KAAKA,SAAO,EAAE;IAC3E,IAAI,OAAO,KAAK,CAAC;IACjB,GAAG;AACH;IACA,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE;IACvC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;IACnB,GAAG,CAAC,CAAC;IACL,CAAC,CAAC;AACF;AACAA,aAAO,CAAC,MAAM,GAAG,SAAS,KAAK,EAAE;IACjC,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;IAC/C,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IAClB,GAAG,CAAC,CAAC;IACL,CAAC,CAAC;AACF;AACAA,aAAO,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE;IAC7B,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;IAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;IACvB,MAAM,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,+BAA+B,CAAC,CAAC,CAAC;IACpE,KAAK;AACL;IACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IACpD,MAAMA,SAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACpD,KAAK;IACL,GAAG,CAAC,CAAC;IACL,CAAC,CAAC;AACF;IACA;AACAA,aAAO,CAAC,YAAY;IACpB;IACA,EAAE,CAAC,OAAO,YAAY,KAAK,UAAU;IACrC,IAAI,SAAS,EAAE,EAAE;IACjB;IACA,MAAM,YAAY,CAAC,EAAE,CAAC,CAAC;IACvB,KAAK;IACL,EAAE,SAAS,EAAE,EAAE;IACf,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1B,GAAG,CAAC;AACJ;AACAA,aAAO,CAAC,qBAAqB,GAAG,SAAS,qBAAqB,CAAC,GAAG,EAAE;IACpE,EAAE,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,EAAE;IACjD,IAAI,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC;IAC/D,GAAG;IACH,CAAC;;IC1PD;IACA,IAAI,CAAC,UAAU,CAAC,OAAO,EACvB;IACI,IAAA,UAAU,CAAC,OAAO,GAAGE,SAAQ,CAAC;IACjC;;ICND;IACA;IACA;IACA;IACA;AACA;IACA,YAAY,CAAC;IACb;IACA,IAAI,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,CAAC;IACzD,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;IACrD,IAAI,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC;AAC7D;IACA,SAAS,QAAQ,CAAC,GAAG,EAAE;IACvB,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;IACxC,EAAE,MAAM,IAAI,SAAS,CAAC,uDAAuD,CAAC,CAAC;IAC/E,EAAE;AACF;IACA,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;AACD;IACA,SAAS,eAAe,GAAG;IAC3B,CAAC,IAAI;IACL,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;IACtB,GAAG,OAAO,KAAK,CAAC;IAChB,GAAG;AACH;IACA;AACA;IACA;IACA,EAAE,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IAChC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAClB,EAAE,IAAI,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACpD,GAAG,OAAO,KAAK,CAAC;IAChB,GAAG;AACH;IACA;IACA,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;IACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;IAC/B,GAAG,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3C,GAAG;IACH,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;IAClE,GAAG,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IACnB,GAAG,CAAC,CAAC;IACL,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,YAAY,EAAE;IACxC,GAAG,OAAO,KAAK,CAAC;IAChB,GAAG;AACH;IACA;IACA,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;IACjB,EAAE,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;IAC7D,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC1B,GAAG,CAAC,CAAC;IACL,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;IACpD,IAAI,sBAAsB,EAAE;IAC5B,GAAG,OAAO,KAAK,CAAC;IAChB,GAAG;AACH;IACA,EAAE,OAAO,IAAI,CAAC;IACd,EAAE,CAAC,OAAO,GAAG,EAAE;IACf;IACA,EAAE,OAAO,KAAK,CAAC;IACf,EAAE;IACF,CAAC;AACD;IACA,IAAA,YAAc,GAAG,eAAe,EAAE,GAAG,MAAM,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE;;AAAA;IAC/E,CAAC,IAAI,IAAI,CAAC;IACV,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC,IAAI,OAAO,CAAC;AACb;IACA,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC5C,EAAE,IAAI,GAAG,MAAM,CAACC,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B;IACA,EAAE,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;IACxB,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;IACvC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IACxB,IAAI;IACJ,GAAG;AACH;IACA,EAAE,IAAI,qBAAqB,EAAE;IAC7B,GAAG,OAAO,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACzC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC5C,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,KAAK;IACL,IAAI;IACJ,GAAG;IACH,EAAE;AACF;IACA,CAAC,OAAO,EAAE,CAAC;IACX,CAAC;;ICzFD;IAMA,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB;IACI,IAAA,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC;IAChC;;;ICTD;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA,IAAM,cAAc,GAAG,EAAE,CAAC;IAE1B;IACA,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EACzC;IACI,IAAA,IAAI,CAAC,GAAG,GAAG,SAAS,GAAG,GAAA;IAEnB,QAAA,OAAO,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;IAChC,KAAC,CAAC;IACL,CAAA;IAED;IACA,IAAI,EAAE,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,EAC3D;IACI,IAAA,IAAM,WAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAA,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;IACK,QAAA,UAAkB,CAAC,WAAW,GAAG,EAAE,CAAC;IACxC,KAAA;IAED,IAAA,UAAU,CAAC,WAAW,CAAC,GAAG,GAAG,YAAc,EAAA,OAAA,IAAI,CAAC,GAAG,EAAE,GAAG,WAAS,CAAA,EAAA,CAAC;IACrE,CAAA;IAED;IACA,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC1B,IAAM,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;IAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAC5E;IACI,IAAA,IAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAErB,UAAU,CAAC,qBAAqB,GAAI,UAAkB,CAAI,CAAC,GAAA,uBAAuB,CAAC,CAAC;IACpF,IAAA,UAAU,CAAC,oBAAoB,GAAI,UAAkB,CAAI,CAAC,yBAAsB,CAAC;IACzE,WAAA,UAAkB,CAAI,CAAC,GAA6B,6BAAA,CAAC,CAAC;IACjE,CAAA;IAED,IAAI,CAAC,UAAU,CAAC,qBAAqB,EACrC;IACI,IAAA,UAAU,CAAC,qBAAqB,GAAG,UAAC,QAAmC,EAAA;IAEnE,QAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAClC;IACI,YAAA,MAAM,IAAI,SAAS,CAAI,QAAQ,GAAA,mBAAmB,CAAC,CAAC;IACvD,SAAA;IAED,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,QAAA,IAAI,KAAK,GAAG,cAAc,GAAG,QAAQ,GAAG,WAAW,CAAC;YAEpD,IAAI,KAAK,GAAG,CAAC,EACb;gBACI,KAAK,GAAG,CAAC,CAAC;IACb,SAAA;YAED,QAAQ,GAAG,WAAW,CAAC;IAEvB,QAAA,OAAO,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,YAAA;IAE9B,YAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACtB,YAAA,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;aAC/B,EAAE,KAAK,CAAC,CAAC;IACd,KAAC,CAAC;IACL,CAAA;IAED,IAAI,CAAC,UAAU,CAAC,oBAAoB,EACpC;IACI,IAAA,UAAU,CAAC,oBAAoB,GAAG,UAAC,EAAU,EAAA,EAAW,OAAA,YAAY,CAAC,EAAE,CAAC,CAAA,EAAA,CAAC;IAC5E;;;IC9ED;IACA;IAEA,IAAI,CAAC,IAAI,CAAC,IAAI,EACd;IACI,IAAA,IAAI,CAAC,IAAI,GAAG,SAAS,QAAQ,CAAC,CAAC,EAAA;IAE3B,QAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEd,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EACvB;IACI,YAAA,OAAO,CAAC,CAAC;IACZ,SAAA;IAED,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1B,KAAC,CAAC;IACL;;;IChBD;IACA;IAEA,IAAI,CAAC,MAAM,CAAC,SAAS,EACrB;IACI,IAAA,MAAM,CAAC,SAAS,GAAG,SAAS,eAAe,CAAC,KAAK,EAAA;IAE7C,QAAA,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;IACvF,KAAC,CAAC;IACL;;ICFD,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;IACK,IAAA,UAAkB,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3C,CAAA;IAED,IAAI,CAAC,UAAU,CAAC,YAAY,EAC5B;IACK,IAAA,UAAkB,CAAC,YAAY,GAAG,KAAK,CAAC;IAC5C,CAAA;IAED,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;IACK,IAAA,UAAkB,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3C,CAAA;IAED,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B;IACK,IAAA,UAAkB,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3C,CAAA;IAED,IAAI,CAAC,UAAU,CAAC,UAAU,EAC1B;IACK,IAAA,UAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;IAC1C,CAAA;IAED,IAAI,CAAC,UAAU,CAAC,UAAU,EAC1B;IACK,IAAA,UAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;IAC1C;;;;;;"}