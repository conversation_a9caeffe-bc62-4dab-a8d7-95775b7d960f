/*!
 * @pixi/filter-blur - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/filter-blur is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{},this.PIXI.filters=this.PIXI.filters||{};var _pixi_filter_blur=function(t,e,r,i){"use strict";var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},n(t,e)};function o(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var l={5:[.153388,.221461,.250301],7:[.071303,.131514,.189879,.214607],9:[.028532,.067234,.124009,.179044,.20236],11:[.0093,.028002,.065984,.121703,.175713,.198596],13:[.002406,.009255,.027867,.065666,.121117,.174868,.197641],15:[489e-6,.002403,.009246,.02784,.065602,.120999,.174697,.197448]},u=["varying vec2 vBlurTexCoords[%size%];","uniform sampler2D uSampler;","void main(void)","{","    gl_FragColor = vec4(0.0);","    %blur%","}"].join("\n");var s=function(t){function e(e,i,n,o,s){void 0===i&&(i=8),void 0===n&&(n=4),void 0===o&&(o=r.settings.FILTER_RESOLUTION),void 0===s&&(s=5);var a=this,h=function(t,e){var r,i=Math.ceil(t/2),n="\n    attribute vec2 aVertexPosition;\n\n    uniform mat3 projectionMatrix;\n\n    uniform float strength;\n\n    varying vec2 vBlurTexCoords[%size%];\n\n    uniform vec4 inputSize;\n    uniform vec4 outputFrame;\n\n    vec4 filterVertexPosition( void )\n    {\n        vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;\n\n        return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);\n    }\n\n    vec2 filterTextureCoord( void )\n    {\n        return aVertexPosition * (outputFrame.zw * inputSize.zw);\n    }\n\n    void main(void)\n    {\n        gl_Position = filterVertexPosition();\n\n        vec2 textureCoord = filterTextureCoord();\n        %blur%\n    }",o="";r=e?"vBlurTexCoords[%index%] =  textureCoord + vec2(%sampleIndex% * strength, 0.0);":"vBlurTexCoords[%index%] =  textureCoord + vec2(0.0, %sampleIndex% * strength);";for(var l=0;l<t;l++){var u=r.replace("%index%",l.toString());o+=u=u.replace("%sampleIndex%",l-(i-1)+".0"),o+="\n"}return(n=n.replace("%blur%",o)).replace("%size%",t.toString())}(s,e),p=function(t){for(var e,r=l[t],i=r.length,n=u,o="",s=0;s<t;s++){var a="gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;".replace("%index%",s.toString());e=s,s>=i&&(e=t-s-1),o+=a=a.replace("%value%",r[e].toString()),o+="\n"}return(n=n.replace("%blur%",o)).replace("%size%",t.toString())}(s);return(a=t.call(this,h,p)||this).horizontal=e,a.resolution=o,a._quality=0,a.quality=n,a.blur=i,a}return o(e,t),e.prototype.apply=function(t,e,r,n){if(r?this.horizontal?this.uniforms.strength=1/r.width*(r.width/e.width):this.uniforms.strength=1/r.height*(r.height/e.height):this.horizontal?this.uniforms.strength=1/t.renderer.width*(t.renderer.width/e.width):this.uniforms.strength=1/t.renderer.height*(t.renderer.height/e.height),this.uniforms.strength*=this.strength,this.uniforms.strength/=this.passes,1===this.passes)t.applyFilter(this,e,r,n);else{var o=t.getFilterTexture(),l=t.renderer,u=e,s=o;this.state.blend=!1,t.applyFilter(this,u,s,i.CLEAR_MODES.CLEAR);for(var a=1;a<this.passes-1;a++){t.bindAndClear(u,i.CLEAR_MODES.BLIT),this.uniforms.uSampler=s;var h=s;s=u,u=h,l.shader.bind(this),l.geometry.draw(5)}this.state.blend=!0,t.applyFilter(this,s,r,n),t.returnFilterTexture(o)}},Object.defineProperty(e.prototype,"blur",{get:function(){return this.strength},set:function(t){this.padding=1+2*Math.abs(t),this.strength=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"quality",{get:function(){return this._quality},set:function(t){this._quality=t,this.passes=t},enumerable:!1,configurable:!0}),e}(e.Filter),a=function(t){function e(e,i,n,o){void 0===e&&(e=8),void 0===i&&(i=4),void 0===n&&(n=r.settings.FILTER_RESOLUTION),void 0===o&&(o=5);var l=t.call(this)||this;return l.blurXFilter=new s(!0,e,i,n,o),l.blurYFilter=new s(!1,e,i,n,o),l.resolution=n,l.quality=i,l.blur=e,l.repeatEdgePixels=!1,l}return o(e,t),e.prototype.apply=function(t,e,r,n){var o=Math.abs(this.blurXFilter.strength),l=Math.abs(this.blurYFilter.strength);if(o&&l){var u=t.getFilterTexture();this.blurXFilter.apply(t,e,u,i.CLEAR_MODES.CLEAR),this.blurYFilter.apply(t,u,r,n),t.returnFilterTexture(u)}else l?this.blurYFilter.apply(t,e,r,n):this.blurXFilter.apply(t,e,r,n)},e.prototype.updatePadding=function(){this._repeatEdgePixels?this.padding=0:this.padding=2*Math.max(Math.abs(this.blurXFilter.strength),Math.abs(this.blurYFilter.strength))},Object.defineProperty(e.prototype,"blur",{get:function(){return this.blurXFilter.blur},set:function(t){this.blurXFilter.blur=this.blurYFilter.blur=t,this.updatePadding()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"quality",{get:function(){return this.blurXFilter.quality},set:function(t){this.blurXFilter.quality=this.blurYFilter.quality=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"blurX",{get:function(){return this.blurXFilter.blur},set:function(t){this.blurXFilter.blur=t,this.updatePadding()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"blurY",{get:function(){return this.blurYFilter.blur},set:function(t){this.blurYFilter.blur=t,this.updatePadding()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"blendMode",{get:function(){return this.blurYFilter.blendMode},set:function(t){this.blurYFilter.blendMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"repeatEdgePixels",{get:function(){return this._repeatEdgePixels},set:function(t){this._repeatEdgePixels=t,this.updatePadding()},enumerable:!1,configurable:!0}),e}(e.Filter);return t.BlurFilter=a,t.BlurFilterPass=s,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI,PIXI);Object.assign(this.PIXI.filters,_pixi_filter_blur);
//# sourceMappingURL=filter-blur.min.js.map
