/*!
 * @pixi/loaders - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/loaders is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("@pixi/core"),e=function(){function t(t,e,r){void 0===e&&(e=!1),this._fn=t,this._once=e,this._thisArg=r,this._next=this._prev=this._owner=null}return t.prototype.detach=function(){return null!==this._owner&&(this._owner.detach(this),!0)},t}();function r(t,e){return t._head?(t._tail._next=e,e._prev=t._tail,t._tail=e):(t._head=e,t._tail=e),e._owner=t,e}var i,s=function(){function t(){this._head=this._tail=void 0}return t.prototype.handlers=function(t){void 0===t&&(t=!1);var e=this._head;if(t)return!!e;for(var r=[];e;)r.push(e),e=e._next;return r},t.prototype.has=function(t){if(!(t instanceof e))throw new Error("MiniSignal#has(): First arg must be a SignalBinding object.");return t._owner===this},t.prototype.dispatch=function(){for(var t=arguments,e=[],r=0;r<arguments.length;r++)e[r]=t[r];var i=this._head;if(!i)return!1;for(;i;)i._once&&this.detach(i),i._fn.apply(i._thisArg,e),i=i._next;return!0},t.prototype.add=function(t,i){if(void 0===i&&(i=null),"function"!=typeof t)throw new Error("MiniSignal#add(): First arg must be a Function.");return r(this,new e(t,!1,i))},t.prototype.once=function(t,i){if(void 0===i&&(i=null),"function"!=typeof t)throw new Error("MiniSignal#once(): First arg must be a Function.");return r(this,new e(t,!0,i))},t.prototype.detach=function(t){if(!(t instanceof e))throw new Error("MiniSignal#detach(): First arg must be a SignalBinding object.");return t._owner!==this||(t._prev&&(t._prev._next=t._next),t._next&&(t._next._prev=t._prev),t===this._head?(this._head=t._next,null===t._next&&(this._tail=null)):t===this._tail&&(this._tail=t._prev,this._tail._next=null),t._owner=null),this},t.prototype.detachAll=function(){var t=this._head;if(!t)return this;for(this._head=this._tail=null;t;)t._owner=null,t=t._next;return this},t}();function o(t,e){e=e||{};for(var r={key:["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}},i=r.parser[e.strictMode?"strict":"loose"].exec(t),s={},o=14;o--;)s[r.key[o]]=i[o]||"";return s[r.q.name]={},s[r.key[12]].replace(r.q.parser,(function(t,e,i){e&&(s[r.q.name][e]=i)})),s}var n,a,h,u,d,l=null;function p(){}function c(t,e,r){e&&0===e.indexOf(".")&&(e=e.substring(1)),e&&(t[e]=r)}function _(t){return t.toString().replace("object ","")}function E(){}function f(t){return function(){for(var e=arguments,r=[],i=0;i<arguments.length;i++)r[i]=e[i];if(null===t)throw new Error("Callback was already called.");var s=t;t=null,s.apply(this,r)}}exports.LoaderResource=function(){function t(e,r,i){if(this._dequeue=p,this._onLoadBinding=null,this._elementTimer=0,this._boundComplete=null,this._boundOnError=null,this._boundOnProgress=null,this._boundOnTimeout=null,this._boundXhrOnError=null,this._boundXhrOnTimeout=null,this._boundXhrOnAbort=null,this._boundXhrOnLoad=null,"string"!=typeof e||"string"!=typeof r)throw new Error("Both name and url are required for constructing a resource.");i=i||{},this._flags=0,this._setFlag(t.STATUS_FLAGS.DATA_URL,0===r.indexOf("data:")),this.name=e,this.url=r,this.extension=this._getExtension(),this.data=null,this.crossOrigin=!0===i.crossOrigin?"anonymous":i.crossOrigin,this.timeout=i.timeout||0,this.loadType=i.loadType||this._determineLoadType(),this.xhrType=i.xhrType,this.metadata=i.metadata||{},this.error=null,this.xhr=null,this.children=[],this.type=t.TYPE.UNKNOWN,this.progressChunk=0,this._dequeue=p,this._onLoadBinding=null,this._elementTimer=0,this._boundComplete=this.complete.bind(this),this._boundOnError=this._onError.bind(this),this._boundOnProgress=this._onProgress.bind(this),this._boundOnTimeout=this._onTimeout.bind(this),this._boundXhrOnError=this._xhrOnError.bind(this),this._boundXhrOnTimeout=this._xhrOnTimeout.bind(this),this._boundXhrOnAbort=this._xhrOnAbort.bind(this),this._boundXhrOnLoad=this._xhrOnLoad.bind(this),this.onStart=new s,this.onProgress=new s,this.onComplete=new s,this.onAfterMiddleware=new s}return t.setExtensionLoadType=function(e,r){c(t._loadTypeMap,e,r)},t.setExtensionXhrType=function(e,r){c(t._xhrTypeMap,e,r)},Object.defineProperty(t.prototype,"isDataUrl",{get:function(){return this._hasFlag(t.STATUS_FLAGS.DATA_URL)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isComplete",{get:function(){return this._hasFlag(t.STATUS_FLAGS.COMPLETE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isLoading",{get:function(){return this._hasFlag(t.STATUS_FLAGS.LOADING)},enumerable:!1,configurable:!0}),t.prototype.complete=function(){this._clearEvents(),this._finish()},t.prototype.abort=function(e){if(!this.error){if(this.error=new Error(e),this._clearEvents(),this.xhr)this.xhr.abort();else if(this.xdr)this.xdr.abort();else if(this.data)if(this.data.src)this.data.src=t.EMPTY_GIF;else for(;this.data.firstChild;)this.data.removeChild(this.data.firstChild);this._finish()}},t.prototype.load=function(e){var r=this;if(!this.isLoading)if(this.isComplete)e&&setTimeout((function(){return e(r)}),1);else switch(e&&this.onComplete.once(e),this._setFlag(t.STATUS_FLAGS.LOADING,!0),this.onStart.dispatch(this),!1!==this.crossOrigin&&"string"==typeof this.crossOrigin||(this.crossOrigin=this._determineCrossOrigin(this.url)),this.loadType){case t.LOAD_TYPE.IMAGE:this.type=t.TYPE.IMAGE,this._loadElement("image");break;case t.LOAD_TYPE.AUDIO:this.type=t.TYPE.AUDIO,this._loadSourceElement("audio");break;case t.LOAD_TYPE.VIDEO:this.type=t.TYPE.VIDEO,this._loadSourceElement("video");break;case t.LOAD_TYPE.XHR:default:void 0===i&&(i=!(!globalThis.XDomainRequest||"withCredentials"in new XMLHttpRequest)),i&&this.crossOrigin?this._loadXdr():this._loadXhr()}},t.prototype._hasFlag=function(t){return 0!=(this._flags&t)},t.prototype._setFlag=function(t,e){this._flags=e?this._flags|t:this._flags&~t},t.prototype._clearEvents=function(){clearTimeout(this._elementTimer),this.data&&this.data.removeEventListener&&(this.data.removeEventListener("error",this._boundOnError,!1),this.data.removeEventListener("load",this._boundComplete,!1),this.data.removeEventListener("progress",this._boundOnProgress,!1),this.data.removeEventListener("canplaythrough",this._boundComplete,!1)),this.xhr&&(this.xhr.removeEventListener?(this.xhr.removeEventListener("error",this._boundXhrOnError,!1),this.xhr.removeEventListener("timeout",this._boundXhrOnTimeout,!1),this.xhr.removeEventListener("abort",this._boundXhrOnAbort,!1),this.xhr.removeEventListener("progress",this._boundOnProgress,!1),this.xhr.removeEventListener("load",this._boundXhrOnLoad,!1)):(this.xhr.onerror=null,this.xhr.ontimeout=null,this.xhr.onprogress=null,this.xhr.onload=null))},t.prototype._finish=function(){if(this.isComplete)throw new Error("Complete called again for an already completed resource.");this._setFlag(t.STATUS_FLAGS.COMPLETE,!0),this._setFlag(t.STATUS_FLAGS.LOADING,!1),this.onComplete.dispatch(this)},t.prototype._loadElement=function(t){this.metadata.loadElement?this.data=this.metadata.loadElement:"image"===t&&void 0!==globalThis.Image?this.data=new Image:this.data=document.createElement(t),this.crossOrigin&&(this.data.crossOrigin=this.crossOrigin),this.metadata.skipSource||(this.data.src=this.url),this.data.addEventListener("error",this._boundOnError,!1),this.data.addEventListener("load",this._boundComplete,!1),this.data.addEventListener("progress",this._boundOnProgress,!1),this.timeout&&(this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout))},t.prototype._loadSourceElement=function(t){if(this.metadata.loadElement?this.data=this.metadata.loadElement:"audio"===t&&void 0!==globalThis.Audio?this.data=new Audio:this.data=document.createElement(t),null!==this.data){if(this.crossOrigin&&(this.data.crossOrigin=this.crossOrigin),!this.metadata.skipSource)if(navigator.isCocoonJS)this.data.src=Array.isArray(this.url)?this.url[0]:this.url;else if(Array.isArray(this.url))for(var e=this.metadata.mimeType,r=0;r<this.url.length;++r)this.data.appendChild(this._createSource(t,this.url[r],Array.isArray(e)?e[r]:e));else{e=this.metadata.mimeType;this.data.appendChild(this._createSource(t,this.url,Array.isArray(e)?e[0]:e))}this.data.addEventListener("error",this._boundOnError,!1),this.data.addEventListener("load",this._boundComplete,!1),this.data.addEventListener("progress",this._boundOnProgress,!1),this.data.addEventListener("canplaythrough",this._boundComplete,!1),this.data.load(),this.timeout&&(this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout))}else this.abort("Unsupported element: "+t)},t.prototype._loadXhr=function(){"string"!=typeof this.xhrType&&(this.xhrType=this._determineXhrType());var e=this.xhr=new XMLHttpRequest;"use-credentials"===this.crossOrigin&&(e.withCredentials=!0),e.open("GET",this.url,!0),e.timeout=this.timeout,this.xhrType===t.XHR_RESPONSE_TYPE.JSON||this.xhrType===t.XHR_RESPONSE_TYPE.DOCUMENT?e.responseType=t.XHR_RESPONSE_TYPE.TEXT:e.responseType=this.xhrType,e.addEventListener("error",this._boundXhrOnError,!1),e.addEventListener("timeout",this._boundXhrOnTimeout,!1),e.addEventListener("abort",this._boundXhrOnAbort,!1),e.addEventListener("progress",this._boundOnProgress,!1),e.addEventListener("load",this._boundXhrOnLoad,!1),e.send()},t.prototype._loadXdr=function(){"string"!=typeof this.xhrType&&(this.xhrType=this._determineXhrType());var t=this.xhr=new globalThis.XDomainRequest;t.timeout=this.timeout||5e3,t.onerror=this._boundXhrOnError,t.ontimeout=this._boundXhrOnTimeout,t.onprogress=this._boundOnProgress,t.onload=this._boundXhrOnLoad,t.open("GET",this.url,!0),setTimeout((function(){return t.send()}),1)},t.prototype._createSource=function(t,e,r){r||(r=t+"/"+this._getExtension(e));var i=document.createElement("source");return i.src=e,i.type=r,i},t.prototype._onError=function(t){this.abort("Failed to load element using: "+t.target.nodeName)},t.prototype._onProgress=function(t){t&&t.lengthComputable&&this.onProgress.dispatch(this,t.loaded/t.total)},t.prototype._onTimeout=function(){this.abort("Load timed out.")},t.prototype._xhrOnError=function(){var t=this.xhr;this.abort(_(t)+" Request failed. Status: "+t.status+', text: "'+t.statusText+'"')},t.prototype._xhrOnTimeout=function(){var t=this.xhr;this.abort(_(t)+" Request timed out.")},t.prototype._xhrOnAbort=function(){var t=this.xhr;this.abort(_(t)+" Request was aborted by the user.")},t.prototype._xhrOnLoad=function(){var e=this.xhr,r="",i=void 0===e.status?200:e.status;if(""!==e.responseType&&"text"!==e.responseType&&void 0!==e.responseType||(r=e.responseText),0===i&&(r.length>0||e.responseType===t.XHR_RESPONSE_TYPE.BUFFER)?i=200:1223===i&&(i=204),2===(i/100|0)){if(this.xhrType===t.XHR_RESPONSE_TYPE.TEXT)this.data=r,this.type=t.TYPE.TEXT;else if(this.xhrType===t.XHR_RESPONSE_TYPE.JSON)try{this.data=JSON.parse(r),this.type=t.TYPE.JSON}catch(t){return void this.abort("Error trying to parse loaded json: "+t)}else if(this.xhrType===t.XHR_RESPONSE_TYPE.DOCUMENT)try{if(globalThis.DOMParser){var s=new DOMParser;this.data=s.parseFromString(r,"text/xml")}else{var o=document.createElement("div");o.innerHTML=r,this.data=o}this.type=t.TYPE.XML}catch(t){return void this.abort("Error trying to parse loaded xml: "+t)}else this.data=e.response||r;this.complete()}else this.abort("["+e.status+"] "+e.statusText+": "+e.responseURL)},t.prototype._determineCrossOrigin=function(t,e){if(0===t.indexOf("data:"))return"";if(globalThis.origin!==globalThis.location.origin)return"anonymous";e=e||globalThis.location,l||(l=document.createElement("a")),l.href=t;var r=o(l.href,{strictMode:!0}),i=!r.port&&""===e.port||r.port===e.port,s=r.protocol?r.protocol+":":"";return r.host===e.hostname&&i&&s===e.protocol?"":"anonymous"},t.prototype._determineXhrType=function(){return t._xhrTypeMap[this.extension]||t.XHR_RESPONSE_TYPE.TEXT},t.prototype._determineLoadType=function(){return t._loadTypeMap[this.extension]||t.LOAD_TYPE.XHR},t.prototype._getExtension=function(t){void 0===t&&(t=this.url);var e="";if(this.isDataUrl){var r=t.indexOf("/");e=t.substring(r+1,t.indexOf(";",r))}else{var i=t.indexOf("?"),s=t.indexOf("#"),o=Math.min(i>-1?i:t.length,s>-1?s:t.length);e=(t=t.substring(0,o)).substring(t.lastIndexOf(".")+1)}return e.toLowerCase()},t.prototype._getMimeFromXhrType=function(e){switch(e){case t.XHR_RESPONSE_TYPE.BUFFER:return"application/octet-binary";case t.XHR_RESPONSE_TYPE.BLOB:return"application/blob";case t.XHR_RESPONSE_TYPE.DOCUMENT:return"application/xml";case t.XHR_RESPONSE_TYPE.JSON:return"application/json";case t.XHR_RESPONSE_TYPE.DEFAULT:case t.XHR_RESPONSE_TYPE.TEXT:default:return"text/plain"}},t}(),n=exports.LoaderResource||(exports.LoaderResource={}),(a=n.STATUS_FLAGS||(n.STATUS_FLAGS={}))[a.NONE=0]="NONE",a[a.DATA_URL=1]="DATA_URL",a[a.COMPLETE=2]="COMPLETE",a[a.LOADING=4]="LOADING",(h=n.TYPE||(n.TYPE={}))[h.UNKNOWN=0]="UNKNOWN",h[h.JSON=1]="JSON",h[h.XML=2]="XML",h[h.IMAGE=3]="IMAGE",h[h.AUDIO=4]="AUDIO",h[h.VIDEO=5]="VIDEO",h[h.TEXT=6]="TEXT",(u=n.LOAD_TYPE||(n.LOAD_TYPE={}))[u.XHR=1]="XHR",u[u.IMAGE=2]="IMAGE",u[u.AUDIO=3]="AUDIO",u[u.VIDEO=4]="VIDEO",(d=n.XHR_RESPONSE_TYPE||(n.XHR_RESPONSE_TYPE={})).DEFAULT="text",d.BUFFER="arraybuffer",d.BLOB="blob",d.DOCUMENT="document",d.JSON="json",d.TEXT="text",n._loadTypeMap={gif:n.LOAD_TYPE.IMAGE,png:n.LOAD_TYPE.IMAGE,bmp:n.LOAD_TYPE.IMAGE,jpg:n.LOAD_TYPE.IMAGE,jpeg:n.LOAD_TYPE.IMAGE,tif:n.LOAD_TYPE.IMAGE,tiff:n.LOAD_TYPE.IMAGE,webp:n.LOAD_TYPE.IMAGE,tga:n.LOAD_TYPE.IMAGE,avif:n.LOAD_TYPE.IMAGE,svg:n.LOAD_TYPE.IMAGE,"svg+xml":n.LOAD_TYPE.IMAGE,mp3:n.LOAD_TYPE.AUDIO,ogg:n.LOAD_TYPE.AUDIO,wav:n.LOAD_TYPE.AUDIO,mp4:n.LOAD_TYPE.VIDEO,webm:n.LOAD_TYPE.VIDEO},n._xhrTypeMap={xhtml:n.XHR_RESPONSE_TYPE.DOCUMENT,html:n.XHR_RESPONSE_TYPE.DOCUMENT,htm:n.XHR_RESPONSE_TYPE.DOCUMENT,xml:n.XHR_RESPONSE_TYPE.DOCUMENT,tmx:n.XHR_RESPONSE_TYPE.DOCUMENT,svg:n.XHR_RESPONSE_TYPE.DOCUMENT,tsx:n.XHR_RESPONSE_TYPE.DOCUMENT,gif:n.XHR_RESPONSE_TYPE.BLOB,png:n.XHR_RESPONSE_TYPE.BLOB,bmp:n.XHR_RESPONSE_TYPE.BLOB,jpg:n.XHR_RESPONSE_TYPE.BLOB,jpeg:n.XHR_RESPONSE_TYPE.BLOB,tif:n.XHR_RESPONSE_TYPE.BLOB,tiff:n.XHR_RESPONSE_TYPE.BLOB,webp:n.XHR_RESPONSE_TYPE.BLOB,tga:n.XHR_RESPONSE_TYPE.BLOB,avif:n.XHR_RESPONSE_TYPE.BLOB,json:n.XHR_RESPONSE_TYPE.JSON,text:n.XHR_RESPONSE_TYPE.TEXT,txt:n.XHR_RESPONSE_TYPE.TEXT,ttf:n.XHR_RESPONSE_TYPE.BUFFER,otf:n.XHR_RESPONSE_TYPE.BUFFER},n.EMPTY_GIF="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==";var T=function(t,e){this.data=t,this.callback=e},g=function(){function t(t,e){var r=this;if(void 0===e&&(e=1),this.workers=0,this.saturated=E,this.unsaturated=E,this.empty=E,this.drain=E,this.error=E,this.started=!1,this.paused=!1,this._tasks=[],this._insert=function(t,e,i){if(i&&"function"!=typeof i)throw new Error("task callback must be a function");if(r.started=!0,null==t&&r.idle())setTimeout((function(){return r.drain()}),1);else{var s=new T(t,"function"==typeof i?i:E);e?r._tasks.unshift(s):r._tasks.push(s),setTimeout(r.process,1)}},this.process=function(){for(;!r.paused&&r.workers<r.concurrency&&r._tasks.length;){var t=r._tasks.shift();0===r._tasks.length&&r.empty(),r.workers+=1,r.workers===r.concurrency&&r.saturated(),r._worker(t.data,f(r._next(t)))}},this._worker=t,0===e)throw new Error("Concurrency must not be zero");this.concurrency=e,this.buffer=e/4}return t.prototype._next=function(t){var e=this;return function(){for(var r=arguments,i=[],s=0;s<arguments.length;s++)i[s]=r[s];e.workers-=1,t.callback.apply(t,i),null!=i[0]&&e.error(i[0],t.data),e.workers<=e.concurrency-e.buffer&&e.unsaturated(),e.idle()&&e.drain(),e.process()}},t.prototype.push=function(t,e){this._insert(t,!1,e)},t.prototype.kill=function(){this.workers=0,this.drain=E,this.started=!1,this._tasks=[]},t.prototype.unshift=function(t,e){this._insert(t,!0,e)},t.prototype.length=function(){return this._tasks.length},t.prototype.running=function(){return this.workers},t.prototype.idle=function(){return this._tasks.length+this.workers===0},t.prototype.pause=function(){!0!==this.paused&&(this.paused=!0)},t.prototype.resume=function(){if(!1!==this.paused){this.paused=!1;for(var t=1;t<=this.concurrency;t++)this.process()}},t.eachSeries=function(t,e,r,i){var s=0,o=t.length;!function n(a){a||s===o?r&&r(a):i?setTimeout((function(){e(t[s++],n)}),1):e(t[s++],n)}()},t.queue=function(e,r){return new t(e,r)},t}(),O=/(#[\w-]+)?$/,y=function(){function e(t,r){var i=this;void 0===t&&(t=""),void 0===r&&(r=10),this.progress=0,this.loading=!1,this.defaultQueryString="",this._beforeMiddleware=[],this._afterMiddleware=[],this._resourcesParsing=[],this._boundLoadResource=function(t,e){return i._loadResource(t,e)},this.resources={},this.baseUrl=t,this._beforeMiddleware=[],this._afterMiddleware=[],this._resourcesParsing=[],this._boundLoadResource=function(t,e){return i._loadResource(t,e)},this._queue=g.queue(this._boundLoadResource,r),this._queue.pause(),this.resources={},this.onProgress=new s,this.onError=new s,this.onLoad=new s,this.onStart=new s,this.onComplete=new s;for(var o=0;o<e._plugins.length;++o){var n=e._plugins[o],a=n.pre,h=n.use;a&&this.pre(a),h&&this.use(h)}this._protected=!1}return e.prototype._add=function(t,e,r,i){if(this.loading&&(!r||!r.parentResource))throw new Error("Cannot add resources while the loader is running.");if(this.resources[t])throw new Error('Resource named "'+t+'" already exists.');if(e=this._prepareUrl(e),this.resources[t]=new exports.LoaderResource(t,e,r),"function"==typeof i&&this.resources[t].onAfterMiddleware.once(i),this.loading){for(var s=r.parentResource,o=[],n=0;n<s.children.length;++n)s.children[n].isComplete||o.push(s.children[n]);var a=s.progressChunk*(o.length+1)/(o.length+2);s.children.push(this.resources[t]),s.progressChunk=a;for(n=0;n<o.length;++n)o[n].progressChunk=a;this.resources[t].progressChunk=a}return this._queue.push(this.resources[t]),this},e.prototype.pre=function(t){return this._beforeMiddleware.push(t),this},e.prototype.use=function(t){return this._afterMiddleware.push(t),this},e.prototype.reset=function(){for(var t in this.progress=0,this.loading=!1,this._queue.kill(),this._queue.pause(),this.resources){var e=this.resources[t];e._onLoadBinding&&e._onLoadBinding.detach(),e.isLoading&&e.abort("loader reset")}return this.resources={},this},e.prototype.load=function(t){if("function"==typeof t&&this.onComplete.once(t),this.loading)return this;if(this._queue.idle())this._onStart(),this._onComplete();else{for(var e=100/this._queue._tasks.length,r=0;r<this._queue._tasks.length;++r)this._queue._tasks[r].data.progressChunk=e;this._onStart(),this._queue.resume()}return this},Object.defineProperty(e.prototype,"concurrency",{get:function(){return this._queue.concurrency},set:function(t){this._queue.concurrency=t},enumerable:!1,configurable:!0}),e.prototype._prepareUrl=function(t){var e,r=o(t,{strictMode:!0});if(e=r.protocol||!r.path||0===t.indexOf("//")?t:this.baseUrl.length&&this.baseUrl.lastIndexOf("/")!==this.baseUrl.length-1&&"/"!==t.charAt(0)?this.baseUrl+"/"+t:this.baseUrl+t,this.defaultQueryString){var i=O.exec(e)[0];-1!==(e=e.slice(0,e.length-i.length)).indexOf("?")?e+="&"+this.defaultQueryString:e+="?"+this.defaultQueryString,e+=i}return e},e.prototype._loadResource=function(t,e){var r=this;t._dequeue=e,g.eachSeries(this._beforeMiddleware,(function(e,i){e.call(r,t,(function(){i(t.isComplete?{}:null)}))}),(function(){t.isComplete?r._onLoad(t):(t._onLoadBinding=t.onComplete.once(r._onLoad,r),t.load())}),!0)},e.prototype._onStart=function(){this.progress=0,this.loading=!0,this.onStart.dispatch(this)},e.prototype._onComplete=function(){this.progress=100,this.loading=!1,this.onComplete.dispatch(this,this.resources)},e.prototype._onLoad=function(t){var e=this;t._onLoadBinding=null,this._resourcesParsing.push(t),t._dequeue(),g.eachSeries(this._afterMiddleware,(function(r,i){r.call(e,t,i)}),(function(){t.onAfterMiddleware.dispatch(t),e.progress=Math.min(100,e.progress+t.progressChunk),e.onProgress.dispatch(e,t),t.error?e.onError.dispatch(t.error,e,t):e.onLoad.dispatch(e,t),e._resourcesParsing.splice(e._resourcesParsing.indexOf(t),1),e._queue.idle()&&0===e._resourcesParsing.length&&e._onComplete()}),!0)},e.prototype.destroy=function(){this._protected||this.reset()},Object.defineProperty(e,"shared",{get:function(){var t=e._shared;return t||((t=new e)._protected=!0,e._shared=t),t},enumerable:!1,configurable:!0}),e.registerPlugin=function(r){return t.extensions.add({type:t.ExtensionType.Loader,ref:r}),e},e._plugins=[],e}();t.extensions.handleByList(t.ExtensionType.Loader,y._plugins),y.prototype.add=function(t,e,r,i){if(Array.isArray(t)){for(var s=0;s<t.length;++s)this.add(t[s]);return this}if("object"==typeof t&&(r=t,i=e||r.callback||r.onComplete,e=r.url,t=r.name||r.key||r.url),"string"!=typeof e&&(i=r,r=e,e=t),"string"!=typeof e)throw new Error("No url passed to add resource to loader.");return"function"==typeof r&&(i=r,r=null),this._add(t,e,r,i)};var m=function(){function e(){}return e.init=function(t){t=Object.assign({sharedLoader:!1},t),this.loader=t.sharedLoader?y.shared:new y},e.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},e.extension=t.ExtensionType.Application,e}(),b=function(){function e(){}return e.add=function(){exports.LoaderResource.setExtensionLoadType("svg",exports.LoaderResource.LOAD_TYPE.XHR),exports.LoaderResource.setExtensionXhrType("svg",exports.LoaderResource.XHR_RESPONSE_TYPE.TEXT)},e.use=function(e,r){if(!e.data||e.type!==exports.LoaderResource.TYPE.IMAGE&&"svg"!==e.extension)r();else{var i=e.data,s=e.url,o=e.name,n=e.metadata;t.Texture.fromLoader(i,s,o,n).then((function(t){e.texture=t,r()})).catch(r)}},e.extension=t.ExtensionType.Loader,e}();function P(t,e){if(t.data){if(t.xhr&&t.xhrType===exports.LoaderResource.XHR_RESPONSE_TYPE.BLOB)if(self.Blob&&"string"!=typeof t.data){if(0===t.data.type.indexOf("image")){var r=globalThis.URL||globalThis.webkitURL,i=r.createObjectURL(t.data);return t.blob=t.data,t.data=new Image,t.data.src=i,t.type=exports.LoaderResource.TYPE.IMAGE,void(t.data.onload=function(){r.revokeObjectURL(i),t.data.onload=null,e()})}}else{var s=t.xhr.getResponseHeader("content-type");if(s&&0===s.indexOf("image"))return t.data=new Image,t.data.src="data:"+s+";base64,"+function(t){for(var e="",r=0;r<t.length;){for(var i=[0,0,0],s=[0,0,0,0],o=0;o<i.length;++o)r<t.length?i[o]=255&t.charCodeAt(r++):i[o]=0;switch(s[0]=i[0]>>2,s[1]=(3&i[0])<<4|i[1]>>4,s[2]=(15&i[1])<<2|i[2]>>6,s[3]=63&i[2],r-(t.length-1)){case 2:s[3]=64,s[2]=64;break;case 1:s[3]=64}for(o=0;o<s.length;++o)e+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(s[o])}return e}(t.xhr.responseText),t.type=exports.LoaderResource.TYPE.IMAGE,void(t.data.onload=function(){t.data.onload=null,e()})}e()}else e()}var L=function(){function e(){}return e.extension=t.ExtensionType.Loader,e.use=P,e}();t.extensions.add(b,L),exports.AppLoaderPlugin=m,exports.Loader=y,exports.TextureLoader=b;
//# sourceMappingURL=loaders.min.js.map
