/*!
 * @pixi/ticker - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/ticker is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_ticker=function(t,e,i){"use strict";var s;e.settings.TARGET_FPMS=.06,t.UPDATE_PRIORITY=void 0,(s=t.UPDATE_PRIORITY||(t.UPDATE_PRIORITY={}))[s.INTERACTION=50]="INTERACTION",s[s.HIGH=25]="HIGH",s[s.NORMAL=0]="NORMAL",s[s.LOW=-25]="LOW",s[s.UTILITY=-50]="UTILITY";var n=function(){function t(t,e,i,s){void 0===e&&(e=null),void 0===i&&(i=0),void 0===s&&(s=!1),this.next=null,this.previous=null,this._destroyed=!1,this.fn=t,this.context=e,this.priority=i,this.once=s}return t.prototype.match=function(t,e){return void 0===e&&(e=null),this.fn===t&&this.context===e},t.prototype.emit=function(t){this.fn&&(this.context?this.fn.call(this.context,t):this.fn(t));var e=this.next;return this.once&&this.destroy(!0),this._destroyed&&(this.next=null),e},t.prototype.connect=function(t){this.previous=t,t.next&&(t.next.previous=this),this.next=t.next,t.next=this},t.prototype.destroy=function(t){void 0===t&&(t=!1),this._destroyed=!0,this.fn=null,this.context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);var e=this.next;return this.next=t?null:e,this.previous=null,e},t}(),r=function(){function i(){var t=this;this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new n(null,null,1/0),this.deltaMS=1/e.settings.TARGET_FPMS,this.elapsedMS=1/e.settings.TARGET_FPMS,this._tick=function(e){t._requestId=null,t.started&&(t.update(e),t.started&&null===t._requestId&&t._head.next&&(t._requestId=requestAnimationFrame(t._tick)))}}return i.prototype._requestIfNeeded=function(){null===this._requestId&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))},i.prototype._cancelIfNeeded=function(){null!==this._requestId&&(cancelAnimationFrame(this._requestId),this._requestId=null)},i.prototype._startIfPossible=function(){this.started?this._requestIfNeeded():this.autoStart&&this.start()},i.prototype.add=function(e,i,s){return void 0===s&&(s=t.UPDATE_PRIORITY.NORMAL),this._addListener(new n(e,i,s))},i.prototype.addOnce=function(e,i,s){return void 0===s&&(s=t.UPDATE_PRIORITY.NORMAL),this._addListener(new n(e,i,s,!0))},i.prototype._addListener=function(t){var e=this._head.next,i=this._head;if(e){for(;e;){if(t.priority>e.priority){t.connect(i);break}i=e,e=e.next}t.previous||t.connect(i)}else t.connect(i);return this._startIfPossible(),this},i.prototype.remove=function(t,e){for(var i=this._head.next;i;)i=i.match(t,e)?i.destroy():i.next;return this._head.next||this._cancelIfNeeded(),this},Object.defineProperty(i.prototype,"count",{get:function(){if(!this._head)return 0;for(var t=0,e=this._head;e=e.next;)t++;return t},enumerable:!1,configurable:!0}),i.prototype.start=function(){this.started||(this.started=!0,this._requestIfNeeded())},i.prototype.stop=function(){this.started&&(this.started=!1,this._cancelIfNeeded())},i.prototype.destroy=function(){if(!this._protected){this.stop();for(var t=this._head.next;t;)t=t.destroy(!0);this._head.destroy(),this._head=null}},i.prototype.update=function(t){var i;if(void 0===t&&(t=performance.now()),t>this.lastTime){if((i=this.elapsedMS=t-this.lastTime)>this._maxElapsedMS&&(i=this._maxElapsedMS),i*=this.speed,this._minElapsedMS){var s=t-this._lastFrame|0;if(s<this._minElapsedMS)return;this._lastFrame=t-s%this._minElapsedMS}this.deltaMS=i,this.deltaTime=this.deltaMS*e.settings.TARGET_FPMS;for(var n=this._head,r=n.next;r;)r=r.emit(this.deltaTime);n.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=t},Object.defineProperty(i.prototype,"FPS",{get:function(){return 1e3/this.elapsedMS},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"minFPS",{get:function(){return 1e3/this._maxElapsedMS},set:function(t){var i=Math.min(this.maxFPS,t),s=Math.min(Math.max(0,i)/1e3,e.settings.TARGET_FPMS);this._maxElapsedMS=1/s},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"maxFPS",{get:function(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0},set:function(t){if(0===t)this._minElapsedMS=0;else{var e=Math.max(this.minFPS,t);this._minElapsedMS=1/(e/1e3)}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"shared",{get:function(){if(!i._shared){var t=i._shared=new i;t.autoStart=!0,t._protected=!0}return i._shared},enumerable:!1,configurable:!0}),Object.defineProperty(i,"system",{get:function(){if(!i._system){var t=i._system=new i;t.autoStart=!0,t._protected=!0}return i._system},enumerable:!1,configurable:!0}),i}(),o=function(){function e(){}return e.init=function(e){var i=this;e=Object.assign({autoStart:!0,sharedTicker:!1},e),Object.defineProperty(this,"ticker",{set:function(e){this._ticker&&this._ticker.remove(this.render,this),this._ticker=e,e&&e.add(this.render,this,t.UPDATE_PRIORITY.LOW)},get:function(){return this._ticker}}),this.stop=function(){i._ticker.stop()},this.start=function(){i._ticker.start()},this._ticker=null,this.ticker=e.sharedTicker?r.shared:new r,e.autoStart&&this.start()},e.destroy=function(){if(this._ticker){var t=this._ticker;this.ticker=null,t.destroy()}},e.extension=i.ExtensionType.Application,e}();return t.Ticker=r,t.TickerPlugin=o,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI);Object.assign(this.PIXI,_pixi_ticker);
//# sourceMappingURL=ticker.min.js.map
