/**
 * WebSocket路由守卫
 * 管理页面切换时的WebSocket连接清理
 */

import type { Router } from 'vue-router';
import { getWebSocketManager } from '@/services/websocketManager';
import { destroyMasterWebSocketManager, cleanupAllWebSocketConnections, getMasterWebSocketManager } from '@/services/masterWebSocketManager';

// 🔧 新增：音频播放状态检查和延迟清理
let delayedCleanupTimers: Map<string, NodeJS.Timeout> = new Map();

// 🔧 新增：防重复执行保护
let isNavigating = false;
let lastNavigationKey = '';
let navigationDebounceTimer: NodeJS.Timeout | null = null;

/**
 * 检查指定页面是否有正在播放的音频
 */
async function checkAudioPlayingStatus(pageName: string): Promise<boolean> {
  try {
    // 检查神谕之音页面的音频播放状态
    if (pageName === 'YijingOracle' || pageName === 'Oracle') {
      // 通过全局状态或DOM检查音频播放状态
      const audioElements = document.querySelectorAll('audio');
      for (const audio of audioElements) {
        if (!audio.paused && !audio.ended) {
          console.log(`🎵 [路由守卫] 检测到音频正在播放: ${audio.src}`);
          return true;
        }
      }

      // 检查全局音频播放状态（如果有的话）
      if (window.isPlayingAudio || (window as any).isPlayingAudio) {
        console.log(`🎵 [路由守卫] 检测到全局音频播放状态`);
        return true;
      }
    }

    // 检查实时对话页面的音频播放状态
    if (pageName === 'RealtimeView') {
      const audioElements = document.querySelectorAll('audio');
      for (const audio of audioElements) {
        if (!audio.paused && !audio.ended) {
          console.log(`🎵 [路由守卫] 检测到实时对话音频播放: ${audio.src}`);
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    console.warn(`⚠️ [路由守卫] 检查音频播放状态失败:`, error);
    return false;
  }
}

/**
 * 安排延迟清理
 */
function scheduleDelayedCleanup(pageName: string, delay: number) {
  // 清理之前的定时器
  const existingTimer = delayedCleanupTimers.get(pageName);
  if (existingTimer) {
    clearTimeout(existingTimer);
  }

  // 设置新的延迟清理定时器
  const timer = setTimeout(async () => {
    console.log(`⏰ [路由守卫] 执行延迟清理: ${pageName}`);
    try {
      const cleanupStrategy = pageCleanupStrategies[pageName];
      if (cleanupStrategy) {
        await cleanupStrategy();
      }
    } catch (error) {
      console.error(`❌ [路由守卫] 延迟清理失败:`, error);
    } finally {
      delayedCleanupTimers.delete(pageName);
    }
  }, delay);

  delayedCleanupTimers.set(pageName, timer);
  console.log(`⏰ [路由守卫] 已安排 ${pageName} 的延迟清理，${delay}ms后执行`);
}

export function setupWebSocketGuards(router: Router) {
  const wsManager = getWebSocketManager('default');
  
  // 页面切换前的清理
  router.beforeEach(async (to, from, next) => {
    const fromPageName = from.name as string;
    const toPageName = to.name as string;
    const navigationKey = `${fromPageName}->${toPageName}`;

    // 🔧 新增：防重复执行保护
    if (isNavigating && lastNavigationKey === navigationKey) {
      console.log(`⏭️ [路由守卫] 跳过重复的页面切换: ${navigationKey}`);
      next();
      return;
    }

    // 🔧 新增：防抖保护，避免快速连续触发
    if (navigationDebounceTimer) {
      clearTimeout(navigationDebounceTimer);
    }

    isNavigating = true;
    lastNavigationKey = navigationKey;

    console.log(`🔄 [路由守卫] 页面切换: ${fromPageName} -> ${toPageName}`);

    try {
      // 清理上一个页面的WebSocket连接
      if (fromPageName && fromPageName !== toPageName) {
        console.log(`🧹 [路由守卫] 清理页面 ${fromPageName} 的连接...`);

        // 🔧 修复：检查是否有正在进行的音频播放，如果有则延迟清理
        const isAudioPlaying = await checkAudioPlayingStatus(fromPageName);
        if (isAudioPlaying) {
          console.log(`🎵 [路由守卫] 检测到音频播放中，延迟清理WebSocket连接`);
          // 标记需要延迟清理，但允许页面切换继续
          scheduleDelayedCleanup(fromPageName, 30000); // 30秒后强制清理
          next();
          return;
        }

        // 执行特定页面的清理策略
        const cleanupStrategy = pageCleanupStrategies[fromPageName];
        if (cleanupStrategy) {
          await cleanupStrategy();
        }
        
        // 通知WebSocket管理器页面切换
        try {
          wsManager.send('PAGE_SWITCH', { 
            from: fromPageName, 
            to: toPageName,
            timestamp: Date.now()
          });
        } catch (error) {
          console.log(`[MasterWS:default] ⚠️ Socket.IO未连接，无法发送事件: PAGE_SWITCH`);
        }
        
        // 🔧 新增：清理特定上下文的MasterWebSocket实例
        const contextMap: Record<string, string> = {
          'RealtimeView': 'realtime-store',     // 实时对话页面
          'YijingOracle': 'yijing-oracle',      // 周易测算页面（正确的路由名称）
          'ComicGeneration': 'comic-generation', // 漫画生成页面
          'TarotDivination': 'tarot-oracle'     // 塔罗测算页面
        };
        
        const contextToDestroy = contextMap[fromPageName];
        if (contextToDestroy) {
          console.log(`🗑️ [路由守卫] 销毁WebSocket上下文: ${contextToDestroy}`);
          destroyMasterWebSocketManager(contextToDestroy);
        }
        
        // 清理该页面的命名空间（兼容旧WebSocket管理器）
        if (typeof wsManager.clearNamespace === 'function') {
          // 为不同页面使用不同的命名空间
          const namespaceMap: Record<string, string> = {
            'RealtimeView': 'realtime-dialogue',
            'YijingDivination': 'yijing-oracle'
          };
          
          const namespace = namespaceMap[fromPageName];
          if (namespace) {
            wsManager.clearNamespace(namespace);
            console.log(`🧹 [路由守卫] 清理命名空间: ${namespace}`);
          }
        }
      }

      // 🔧 新增：设置防抖定时器，重置导航状态
      navigationDebounceTimer = setTimeout(() => {
        isNavigating = false;
        lastNavigationKey = '';
        navigationDebounceTimer = null;
      }, 1000); // 1秒后重置状态

      next();
    } catch (error) {
      console.error('❌ [路由守卫] 页面切换时WebSocket清理失败:', error);

      // 🔧 新增：错误时也要重置导航状态
      isNavigating = false;
      lastNavigationKey = '';
      if (navigationDebounceTimer) {
        clearTimeout(navigationDebounceTimer);
        navigationDebounceTimer = null;
      }

      // 即使清理失败也要继续导航，避免阻塞用户
      next();
    }
  });
  
  // 页面切换完成后的激活
  router.afterEach((to, from) => {
    const toPageName = to.name as string;
    console.log(`✅ [路由守卫] 页面切换完成: ${toPageName}`);
    
    // 注册新页面到统一管理器
    if (toPageName) {
      // 🔧 临时禁用：后端不支持此事件类型
      // wsManager.send('PAGE_REGISTER', { pageName: toPageName });
      console.log('🔇 [路由守卫] 跳过PAGE_REGISTER事件发送（后端不支持）');
    }
    
    // 执行健康检查
    const healthCheck = wsManager.getStats();
    if (!wsManager.isConnected()) {
      console.log('ℹ️ [路由守卫] WebSocket离线模式运行正常');
    } else {
      console.log('✅ [路由守卫] WebSocket连接正常:', healthCheck);
    }
  });
  
  // 浏览器关闭/刷新时的清理
  window.addEventListener('beforeunload', async () => {
    console.log('🌍 [路由守卫] 页面即将离开，执行最终清理...');
    try {
      // 清理所有MasterWebSocket实例
      cleanupAllWebSocketConnections();
      
      // 快速清理旧WebSocket管理器
      await Promise.race([
        Promise.resolve(wsManager.gracefulDisconnect()),
        new Promise(resolve => setTimeout(resolve, 1000)) // 最多等1秒
      ]);
      console.log('✅ [路由守卫] 页面离开清理完成');
    } catch (error) {
      console.warn('⚠️ [路由守卫] 页面离开清理失败:', error);
    }
  });
  
  console.log('✅ [路由守卫] WebSocket守卫已设置');
}

// 🔧 新增：清理状态跟踪，防止重复清理
let cleanupInProgress: Set<string> = new Set();

// 为不同页面定义特殊的清理策略
export const pageCleanupStrategies: Record<string, () => Promise<void>> = {
  'RealtimeView': async () => {
    // 🔧 新增：防重复清理保护
    if (cleanupInProgress.has('RealtimeView')) {
      console.log('⏭️ [路由守卫] 实时对话页面清理正在进行中，跳过重复清理');
      return;
    }

    cleanupInProgress.add('RealtimeView');

    try {
      // 实时对话页面特殊清理
      console.log('🎮 [路由守卫] 执行实时对话页面特殊清理...');

      // 动态导入API模块以避免循环依赖
      const { default: API } = await import('@/services/api');

      // 🔧 修复：只在必要时调用停止API，避免重复调用
      try {
        // 检查是否有活跃的实时对话会话
        const statusResponse = await API.getRealtimeStatus();
        if (statusResponse.success && statusResponse.data?.active) {
          console.log('🛑 [路由守卫] 检测到活跃的实时对话会话，执行停止...');
          await API.stopRealtimeDialogue();
        } else {
          console.log('ℹ️ [路由守卫] 实时对话会话已停止，跳过停止调用');
        }
      } catch (statusError) {
        console.log('ℹ️ [路由守卫] 无法获取实时对话状态，尝试停止...');
        // 如果无法获取状态，尝试停止（可能会失败，但不影响清理流程）
        try {
          await API.stopRealtimeDialogue();
        } catch (stopError) {
          console.log('ℹ️ [路由守卫] 停止实时对话失败（可能已停止）:', stopError);
        }
      }

      console.log('✅ [路由守卫] 实时对话页面清理完成');
    } catch (error) {
      console.warn('⚠️ [路由守卫] 实时对话页面清理失败:', error);
    } finally {
      // 🔧 新增：清理完成后移除标记
      setTimeout(() => {
        cleanupInProgress.delete('RealtimeView');
      }, 2000); // 2秒后允许再次清理
    }
  },
  
  'YijingDivination': async () => {
    // 神谕之音页面特殊清理
    console.log('🔮 [路由守卫] 执行神谕之音页面特殊清理...');
    try {
      // 动态导入API模块
      const { default: API } = await import('@/services/api');
      
      // 停止任何进行中的AI对话
      await API.stopRealtimeDialogue();
      
      console.log('✅ [路由守卫] 神谕之音页面清理完成');
    } catch (error) {
      console.warn('⚠️ [路由守卫] 神谕之音页面清理失败:', error);
    }
  },
  
  'comic-generation': async () => {
    // 漫画生成页面特殊清理
    console.log('🎨 [路由守卫] 执行漫画生成页面特殊清理...');
    try {
      // 停止所有生成任务
      const comicGenerator = await import('@/components/modules/漫画生成/services/comicGenerator');
      comicGenerator.comicGenerator.cancelGeneration();
      console.log('✅ [路由守卫] 漫画生成页面清理完成');
    } catch (error) {
      console.warn('⚠️ [路由守卫] 漫画生成页面清理失败:', error);
    }
  },
  
  'yijing': async () => {
    // 周易测算页面特殊清理
    console.log('🔮 [路由守卫] 执行周易测算页面特殊清理...');
    try {
      // 停止神谕之音相关服务
      const API = await import('@/api');
      await API.default.stopRealtimeDialogue();
      console.log('✅ [路由守卫] 周易测算页面清理完成');
    } catch (error) {
      console.warn('⚠️ [路由守卫] 周易测算页面清理失败:', error);
    }
  }
};