{"name": "@pixi/runner", "version": "6.5.10", "main": "dist/cjs/runner.js", "module": "dist/esm/runner.mjs", "bundle": "dist/browser/runner.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/runner.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/runner.js"}}}, "description": "A simple alternative to events and signals with an emphasis on performance.", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "keywords": ["runner", "signals", "event", "messaging", "publish", "subscribe", "observer", "pub/sub", "fast"], "publishConfig": {"access": "public"}, "scripts": {"test": "floss --path test", "benchmark": "cd benchmark && npm start"}, "files": ["lib", "dist", "*.d.ts"], "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}