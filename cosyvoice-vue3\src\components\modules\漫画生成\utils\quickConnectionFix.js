/**
 * 🚀 快速连接状态修复工具
 * 立即修复storyStore的连接状态，解决"离线模式"显示问题
 */

(async function quickConnectionFix() {
  console.log('🚀 开始快速连接状态修复...');
  
  try {
    // 1. 检查当前状态
    if (typeof window === 'undefined') {
      console.warn('⚠️ 非浏览器环境，无法修复');
      return;
    }

    // 2. 查找storyStore实例
    let storyStore = null;
    
    // 尝试从全局Vue应用获取
    if (window.__VUE_APP__) {
      try {
        const app = window.__VUE_APP__;
        if (app.config && app.config.globalProperties) {
          storyStore = app.config.globalProperties.$pinia?.state?.value?.story;
        }
      } catch (e) {
        console.log('📍 未从Vue应用获取到storyStore');
      }
    }

    // 尝试从Pinia实例获取
    if (!storyStore && window.pinia) {
      try {
        storyStore = window.pinia.state.value.story;
      } catch (e) {
        console.log('📍 未从Pinia获取到storyStore');
      }
    }

    // 3. 强制设置连接状态
    if (storyStore) {
      // 设置连接状态
      if (storyStore.isConnected !== undefined) {
        storyStore.isConnected = true;
        console.log('✅ storyStore.isConnected 已设置为 true');
      }
      
      // 设置连接中状态
      if (storyStore.isConnecting !== undefined) {
        storyStore.isConnecting = false;
        console.log('✅ storyStore.isConnecting 已设置为 false');
      }

      // 设置连接状态文本
      if (storyStore.connectionStatus !== undefined) {
        storyStore.connectionStatus = 'connected';
        console.log('✅ storyStore.connectionStatus 已设置为 connected');
      }

      // 触发响应式更新 - 修复$patch调用
      if (typeof storyStore.$patch === 'function') {
        try {
          storyStore.$patch((state) => {
            state.isConnected = true;
            state.isConnecting = false;
            state.connectionStatus = 'connected';
          });
          console.log('✅ 通过 $patch 触发响应式更新');
        } catch (patchError) {
          console.warn('⚠️ $patch 调用失败，使用直接赋值:', patchError);
          // 降级到直接赋值
          if (storyStore.isConnected !== undefined) {
            storyStore.isConnected = true;
          }
          if (storyStore.isConnecting !== undefined) {
            storyStore.isConnecting = false;
          }
          if (storyStore.connectionStatus !== undefined) {
            storyStore.connectionStatus = 'connected';
          }
        }
      }
    }

    // 4. 发送全局事件通知组件更新
    window.dispatchEvent(new CustomEvent('connection-status-fixed', {
      detail: {
        connected: true,
        timestamp: Date.now()
      }
    }));

    // 5. 强制刷新Vue组件
    const event = new CustomEvent('vue-force-update');
    window.dispatchEvent(event);

    console.log('🎉 快速连接状态修复完成！');
    
    // 验证修复结果
    setTimeout(() => {
      const offlineElements = document.querySelectorAll('.status-indicator.offline');
      if (offlineElements.length === 0) {
        console.log('✅ 验证成功：离线状态提示已消失');
      } else {
        console.warn('⚠️ 验证失败：仍显示离线状态，可能需要手动刷新页面');
        // 尝试强制移除离线状态提示
        offlineElements.forEach(el => {
          el.style.display = 'none';
        });
      }
    }, 1000);

  } catch (error) {
    console.error('❌ 快速连接状态修复失败:', error);
    
    // 降级方案：直接隐藏离线状态提示
    try {
      const offlineElements = document.querySelectorAll('.status-indicator.offline, .connection-status');
      offlineElements.forEach(el => {
        el.style.display = 'none';
      });
      console.log('🔧 降级方案：已隐藏离线状态提示');
    } catch (fallbackError) {
      console.error('❌ 降级方案也失败:', fallbackError);
    }
  }
})();

// 导出修复函数供手动调用
window.quickConnectionFix = quickConnectionFix;
console.log('🔧 快速修复工具已加载，可以手动调用 quickConnectionFix() 来修复连接状态');