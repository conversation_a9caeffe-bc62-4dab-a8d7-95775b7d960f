/**
 * 连接状态修复工具
 * 解决storyStore中isConnected状态设置逻辑问题，确保正确显示在线状态
 */

export class ConnectionStatusFix {
  private static instance: ConnectionStatusFix;
  private debugMode = true;

  public static getInstance(): ConnectionStatusFix {
    if (!ConnectionStatusFix.instance) {
      ConnectionStatusFix.instance = new ConnectionStatusFix();
    }
    return ConnectionStatusFix.instance;
  }

  /**
   * 诊断当前连接状态问题
   */
  public async diagnoseConnectionIssues(): Promise<{
    issue: string;
    description: string;
    solution: string;
    severity: 'low' | 'medium' | 'high';
  }[]> {
    const issues: Array<{
      issue: string;
      description: string;
      solution: string;
      severity: 'low' | 'medium' | 'high';
    }> = [];

    try {
      // 动态导入storyStore
      const { useStoryStore } = await import('@/stores/storyStore');
      const storyStore = useStoryStore();

      this.log('📊 开始诊断连接状态问题...');

      // 检查1：WebSocket管理器状态
      try {
        const { getMasterWebSocketManager } = await import('@/services/masterWebSocketManager');
        const manager = getMasterWebSocketManager({}, 'comic-generation');
        
        const managerConnected = manager.isConnected();
        const storeConnected = storyStore.isConnected;
        const storeConnecting = storyStore.isConnecting;

        this.log(`🔍 状态对比: 
          - WebSocket管理器: ${managerConnected ? '已连接' : '未连接'}
          - storyStore.isConnected: ${storeConnected}
          - storyStore.isConnecting: ${storeConnecting}
          - storyStore.connectionStatus: ${storyStore.connectionStatus}`);

        // 状态不一致问题
        if (managerConnected && !storeConnected) {
          issues.push({
            issue: 'WebSocket管理器已连接但storyStore显示未连接',
            description: 'WebSocket连接成功，但storyStore的isConnected状态未正确更新',
            solution: '强制同步storyStore状态到WebSocket管理器状态',
            severity: 'high'
          });
        }

        // 卡在连接中状态
        if (storeConnecting && !managerConnected) {
          issues.push({
            issue: 'storyStore卡在连接中状态',
            description: 'isConnecting为true但WebSocket管理器未连接',
            solution: '重置连接状态并重新尝试连接',
            severity: 'medium'
          });
        }

      } catch (managerError) {
        this.log(`❌ 无法访问WebSocket管理器: ${managerError}`);
        issues.push({
          issue: 'WebSocket管理器不可访问',
          description: 'getMasterWebSocketManager导入或实例化失败',
          solution: '检查masterWebSocketManager服务是否正常',
          severity: 'high'
        });
      }

      // 检查2：网络连接状态
      try {
        const response = await fetch('/api/health', { 
          method: 'GET',
          timeout: 5000 
        } as RequestInit);
        
        if (!response.ok) {
          issues.push({
            issue: '后端服务器连接异常',
            description: `健康检查返回 ${response.status} ${response.statusText}`,
            solution: '检查后端服务器是否正常运行',
            severity: 'high'
          });
        }
      } catch (networkError) {
        issues.push({
          issue: '网络连接失败',
          description: '无法连接到后端API服务器',
          solution: '检查网络连接和后端服务器状态',
          severity: 'high'
        });
      }

      // 检查3：ComfyUI连接状态
      try {
        const comfyResponse = await fetch('http://localhost:8188/system_stats', {
          timeout: 3000
        } as RequestInit);
        
        if (!comfyResponse.ok) {
          issues.push({
            issue: 'ComfyUI服务器连接失败',
            description: 'ComfyUI后端服务不可访问',
            solution: '启动ComfyUI服务器: python main.py --listen 0.0.0.0 --port 8188 --enable-cors-header',
            severity: 'medium'
          });
        }
      } catch (comfyError) {
        issues.push({
          issue: 'ComfyUI服务器未运行',
          description: 'ComfyUI后端服务不可访问',
          solution: '启动ComfyUI服务器',
          severity: 'medium'
        });
      }

      this.log(`🔍 诊断完成，发现 ${issues.length} 个问题`);
      return issues;

    } catch (error) {
      this.log(`❌ 诊断过程出错: ${error}`);
      return [{
        issue: '诊断工具异常',
        description: `诊断过程中发生错误: ${error}`,
        solution: '检查诊断工具代码或手动检查连接状态',
        severity: 'low'
      }];
    }
  }

  /**
   * 强制修复连接状态
   */
  public async forceFixConnectionStatus(): Promise<{
    success: boolean;
    message: string;
    actions: string[];
  }> {
    const actions: string[] = [];

    try {
      this.log('🔧 开始强制修复连接状态...');

      // 1. 重置storyStore状态
      const { useStoryStore } = await import('@/stores/storyStore');
      const storyStore = useStoryStore();

      actions.push('重置storyStore连接状态');
      
      // 2. 重新初始化WebSocket连接
      try {
        const { getMasterWebSocketManager } = await import('@/services/masterWebSocketManager');
        const manager = getMasterWebSocketManager({}, 'comic-generation');
        
        // 检查当前状态
        if (manager.isConnected()) {
          this.log('✅ WebSocket管理器已连接，强制同步storyStore状态');
          
          // 直接设置storyStore状态为已连接
          // 注意：这是一个hack方法，仅用于修复状态不一致问题
          (storyStore as any).isConnected = true;
          (storyStore as any).isConnecting = false;
          
          actions.push('强制同步storyStore状态为已连接');
          
          // 手动触发状态变化事件
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('storystore-connection-change', {
              detail: { 
                connected: true, 
                connecting: false,
                timestamp: Date.now()
              }
            }));
          }
          
          actions.push('触发状态变化事件');
          
        } else {
          this.log('🔄 WebSocket管理器未连接，尝试重新连接...');
          
          // 重新连接
          await storyStore.connect();
          actions.push('执行重新连接');
        }

      } catch (managerError) {
        this.log(`❌ WebSocket管理器操作失败: ${managerError}`);
        actions.push(`WebSocket管理器操作失败: ${managerError}`);
        return {
          success: false,
          message: 'WebSocket管理器操作失败',
          actions
        };
      }

      // 3. 验证修复结果
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const finalState = storyStore.connectionStatus;
      this.log(`🔍 修复后状态: ${finalState}`);
      
      if (finalState === 'connected') {
        actions.push('修复成功，状态已更新为connected');
        return {
          success: true,
          message: '连接状态修复成功',
          actions
        };
      } else {
        actions.push(`修复后状态仍为: ${finalState}`);
        return {
          success: false,
          message: `修复未完全成功，当前状态: ${finalState}`,
          actions
        };
      }

    } catch (error) {
      this.log(`❌ 修复过程出错: ${error}`);
      actions.push(`修复过程出错: ${error}`);
      return {
        success: false,
        message: `修复失败: ${error}`,
        actions
      };
    }
  }

  /**
   * 安装连接状态监控器
   */
  public installConnectionMonitor(): void {
    this.log('📡 安装连接状态监控器...');

    // 监控storyStore状态变化
    if (typeof window !== 'undefined') {
      window.addEventListener('storystore-connection-change', (event: CustomEvent) => {
        this.log(`📊 连接状态变化: ${JSON.stringify(event.detail)}`);
      });

      // 定期检查状态一致性
      setInterval(async () => {
        try {
          const { useStoryStore } = await import('@/stores/storyStore');
          const { getMasterWebSocketManager } = await import('@/services/masterWebSocketManager');
          
          const storyStore = useStoryStore();
          const manager = getMasterWebSocketManager({}, 'comic-generation');
          
          const managerConnected = manager.isConnected();
          const storeConnected = storyStore.isConnected;
          
          if (managerConnected !== storeConnected) {
            this.log(`⚠️ 检测到状态不一致: 管理器=${managerConnected}, Store=${storeConnected}`);
            
            // 自动修复轻微的状态不一致
            if (managerConnected && !storeConnected) {
              this.log('🔧 自动修复状态不一致...');
              await this.forceFixConnectionStatus();
            }
          }
        } catch (error) {
          // 静默处理监控错误，避免干扰正常功能
        }
      }, 10000); // 每10秒检查一次

      this.log('✅ 连接状态监控器已安装');
    }
  }

  /**
   * 提供用户友好的修复建议
   */
  public async getFixSuggestions(): Promise<string[]> {
    const suggestions: string[] = [];
    const issues = await this.diagnoseConnectionIssues();

    if (issues.length === 0) {
      suggestions.push('✅ 连接状态正常，无需修复');
      return suggestions;
    }

    suggestions.push('🔧 检测到连接问题，建议按以下步骤修复：');
    suggestions.push('');

    // 按严重程度排序
    const sortedIssues = issues.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });

    sortedIssues.forEach((issue, index) => {
      const severityEmoji = {
        high: '🚨',
        medium: '⚠️',
        low: 'ℹ️'
      };

      suggestions.push(`${index + 1}. ${severityEmoji[issue.severity]} ${issue.issue}`);
      suggestions.push(`   问题描述: ${issue.description}`);
      suggestions.push(`   解决方法: ${issue.solution}`);
      suggestions.push('');
    });

    suggestions.push('📞 如果问题持续存在，请尝试：');
    suggestions.push('   1. 刷新页面');
    suggestions.push('   2. 重启后端服务器');
    suggestions.push('   3. 检查网络连接');
    suggestions.push('   4. 运行连接状态修复工具');

    return suggestions;
  }

  private log(message: string): void {
    if (this.debugMode) {
      console.log(`[ConnectionStatusFix] ${message}`);
    }
  }
}

// 全局导出实例和便捷方法
export const connectionStatusFix = ConnectionStatusFix.getInstance();

// 全局暴露修复工具（用于控制台调试）
if (typeof window !== 'undefined') {
  (window as any).fixConnectionStatus = async () => {
    console.log('🔧 开始修复连接状态...');
    const result = await connectionStatusFix.forceFixConnectionStatus();
    console.log('📊 修复结果:', result);
    return result;
  };

  (window as any).diagnoseConnection = async () => {
    console.log('🔍 开始诊断连接问题...');
    const issues = await connectionStatusFix.diagnoseConnectionIssues();
    console.log('📊 诊断结果:', issues);
    
    const suggestions = await connectionStatusFix.getFixSuggestions();
    console.log('💡 修复建议:');
    suggestions.forEach(suggestion => console.log(suggestion));
    
    return { issues, suggestions };
  };

  console.log('🛠️ 连接状态修复工具已加载');
  console.log('💡 使用方法：');
  console.log('   - fixConnectionStatus() - 强制修复连接状态');
  console.log('   - diagnoseConnection() - 诊断连接问题');
}