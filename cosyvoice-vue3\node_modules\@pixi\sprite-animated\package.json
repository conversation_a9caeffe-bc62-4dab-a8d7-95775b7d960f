{"name": "@pixi/sprite-animated", "version": "6.5.10", "main": "dist/cjs/sprite-animated.js", "module": "dist/esm/sprite-animated.mjs", "bundle": "dist/browser/sprite-animated.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/sprite-animated.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/sprite-animated.js"}}}, "description": "Sprite Animations as depicted by playing a series of Textures", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/core": "6.5.10", "@pixi/sprite": "6.5.10", "@pixi/ticker": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}