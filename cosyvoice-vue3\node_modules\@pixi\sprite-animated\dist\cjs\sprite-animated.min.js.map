{"version": 3, "file": "sprite-animated.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/AnimatedSprite.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { Texture } from '@pixi/core';\nimport { Sprite } from '@pixi/sprite';\nimport { Ticker, UPDATE_PRIORITY } from '@pixi/ticker';\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * An AnimatedSprite is a simple way to display an animation depicted by a list of textures.\n *\n * ```js\n * let alienImages = [\"image_sequence_01.png\",\"image_sequence_02.png\",\"image_sequence_03.png\",\"image_sequence_04.png\"];\n * let textureArray = [];\n *\n * for (let i=0; i < 4; i++)\n * {\n *      let texture = PIXI.Texture.from(alienImages[i]);\n *      textureArray.push(texture);\n * };\n *\n * let animatedSprite = new PIXI.AnimatedSprite(textureArray);\n * ```\n *\n * The more efficient and simpler way to create an animated sprite is using a {@link PIXI.Spritesheet}\n * containing the animation definitions:\n *\n * ```js\n * PIXI.Loader.shared.add(\"assets/spritesheet.json\").load(setup);\n *\n * function setup() {\n *   let sheet = PIXI.Loader.shared.resources[\"assets/spritesheet.json\"].spritesheet;\n *   animatedSprite = new PIXI.AnimatedSprite(sheet.animations[\"image_sequence\"]);\n *   ...\n * }\n * ```\n * @memberof PIXI\n */\nexport class AnimatedSprite extends Sprite\n{\n    /**\n     * The speed that the AnimatedSprite will play at. Higher is faster, lower is slower.\n     * @default 1\n     */\n    public animationSpeed: number;\n\n    /**\n     * Whether or not the animate sprite repeats after playing.\n     * @default true\n     */\n    public loop: boolean;\n\n    /**\n     * Update anchor to [Texture's defaultAnchor]{@link PIXI.Texture#defaultAnchor} when frame changes.\n     *\n     * Useful with [sprite sheet animations]{@link PIXI.Spritesheet#animations} created with tools.\n     * Changing anchor for each frame allows to pin sprite origin to certain moving feature\n     * of the frame (e.g. left foot).\n     *\n     * Note: Enabling this will override any previously set `anchor` on each frame change.\n     * @default false\n     */\n    public updateAnchor: boolean;\n\n    /**\n     * User-assigned function to call when an AnimatedSprite finishes playing.\n     * @example\n     * animation.onComplete = function () {\n     *   // finished!\n     * };\n     */\n    public onComplete?: () => void;\n\n    /**\n     * User-assigned function to call when an AnimatedSprite changes which texture is being rendered.\n     * @example\n     * animation.onFrameChange = function () {\n     *   // updated!\n     * };\n     */\n    public onFrameChange?: (currentFrame: number) => void;\n\n    /**\n     * User-assigned function to call when `loop` is true, and an AnimatedSprite is played and\n     * loops around to start again.\n     * @example\n     * animation.onLoop = function () {\n     *   // looped!\n     * };\n     */\n    public onLoop?: () => void;\n\n    private _playing: boolean;\n    private _textures: Texture[];\n    private _durations: number[];\n\n    /**\n     * `true` uses PIXI.Ticker.shared to auto update animation time.\n     * @default true\n     */\n    private _autoUpdate: boolean;\n\n    /**\n     * `true` if the instance is currently connected to PIXI.Ticker.shared to auto update animation time.\n     * @default false\n     */\n    private _isConnectedToTicker: boolean;\n\n    /** Elapsed time since animation has been started, used internally to display current texture. */\n    private _currentTime: number;\n\n    /** The texture index that was displayed last time. */\n    private _previousFrame: number;\n\n    /**\n     * @param textures - An array of {@link PIXI.Texture} or frame\n     *  objects that make up the animation.\n     * @param {boolean} [autoUpdate=true] - Whether to use PIXI.Ticker.shared to auto update animation time.\n     */\n    constructor(textures: Texture[] | FrameObject[], autoUpdate = true)\n    {\n        super(textures[0] instanceof Texture ? textures[0] : textures[0].texture);\n\n        this._textures = null;\n        this._durations = null;\n        this._autoUpdate = autoUpdate;\n        this._isConnectedToTicker = false;\n\n        this.animationSpeed = 1;\n        this.loop = true;\n        this.updateAnchor = false;\n        this.onComplete = null;\n        this.onFrameChange = null;\n        this.onLoop = null;\n\n        this._currentTime = 0;\n\n        this._playing = false;\n        this._previousFrame = null;\n\n        this.textures = textures;\n    }\n\n    /** Stops the AnimatedSprite. */\n    public stop(): void\n    {\n        if (!this._playing)\n        {\n            return;\n        }\n\n        this._playing = false;\n        if (this._autoUpdate && this._isConnectedToTicker)\n        {\n            Ticker.shared.remove(this.update, this);\n            this._isConnectedToTicker = false;\n        }\n    }\n\n    /** Plays the AnimatedSprite. */\n    public play(): void\n    {\n        if (this._playing)\n        {\n            return;\n        }\n\n        this._playing = true;\n        if (this._autoUpdate && !this._isConnectedToTicker)\n        {\n            Ticker.shared.add(this.update, this, UPDATE_PRIORITY.HIGH);\n            this._isConnectedToTicker = true;\n        }\n    }\n\n    /**\n     * Stops the AnimatedSprite and goes to a specific frame.\n     * @param frameNumber - Frame index to stop at.\n     */\n    public gotoAndStop(frameNumber: number): void\n    {\n        this.stop();\n\n        const previousFrame = this.currentFrame;\n\n        this._currentTime = frameNumber;\n\n        if (previousFrame !== this.currentFrame)\n        {\n            this.updateTexture();\n        }\n    }\n\n    /**\n     * Goes to a specific frame and begins playing the AnimatedSprite.\n     * @param frameNumber - Frame index to start at.\n     */\n    public gotoAndPlay(frameNumber: number): void\n    {\n        const previousFrame = this.currentFrame;\n\n        this._currentTime = frameNumber;\n\n        if (previousFrame !== this.currentFrame)\n        {\n            this.updateTexture();\n        }\n\n        this.play();\n    }\n\n    /**\n     * Updates the object transform for rendering.\n     * @param deltaTime - Time since last tick.\n     */\n    update(deltaTime: number): void\n    {\n        if (!this._playing)\n        {\n            return;\n        }\n\n        const elapsed = this.animationSpeed * deltaTime;\n        const previousFrame = this.currentFrame;\n\n        if (this._durations !== null)\n        {\n            let lag = this._currentTime % 1 * this._durations[this.currentFrame];\n\n            lag += elapsed / 60 * 1000;\n\n            while (lag < 0)\n            {\n                this._currentTime--;\n                lag += this._durations[this.currentFrame];\n            }\n\n            const sign = Math.sign(this.animationSpeed * deltaTime);\n\n            this._currentTime = Math.floor(this._currentTime);\n\n            while (lag >= this._durations[this.currentFrame])\n            {\n                lag -= this._durations[this.currentFrame] * sign;\n                this._currentTime += sign;\n            }\n\n            this._currentTime += lag / this._durations[this.currentFrame];\n        }\n        else\n        {\n            this._currentTime += elapsed;\n        }\n\n        if (this._currentTime < 0 && !this.loop)\n        {\n            this.gotoAndStop(0);\n\n            if (this.onComplete)\n            {\n                this.onComplete();\n            }\n        }\n        else if (this._currentTime >= this._textures.length && !this.loop)\n        {\n            this.gotoAndStop(this._textures.length - 1);\n\n            if (this.onComplete)\n            {\n                this.onComplete();\n            }\n        }\n        else if (previousFrame !== this.currentFrame)\n        {\n            if (this.loop && this.onLoop)\n            {\n                if (this.animationSpeed > 0 && this.currentFrame < previousFrame)\n                {\n                    this.onLoop();\n                }\n                else if (this.animationSpeed < 0 && this.currentFrame > previousFrame)\n                {\n                    this.onLoop();\n                }\n            }\n\n            this.updateTexture();\n        }\n    }\n\n    /** Updates the displayed texture to match the current frame index. */\n    private updateTexture(): void\n    {\n        const currentFrame = this.currentFrame;\n\n        if (this._previousFrame === currentFrame)\n        {\n            return;\n        }\n\n        this._previousFrame = currentFrame;\n\n        this._texture = this._textures[currentFrame];\n        this._textureID = -1;\n        this._textureTrimmedID = -1;\n        this._cachedTint = 0xFFFFFF;\n        this.uvs = this._texture._uvs.uvsFloat32;\n\n        if (this.updateAnchor)\n        {\n            this._anchor.copyFrom(this._texture.defaultAnchor);\n        }\n\n        if (this.onFrameChange)\n        {\n            this.onFrameChange(this.currentFrame);\n        }\n    }\n\n    /**\n     * Stops the AnimatedSprite and destroys it.\n     * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n     *  have been set to that value.\n     * @param {boolean} [options.children=false] - If set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well.\n     * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well.\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this.stop();\n        super.destroy(options);\n\n        this.onComplete = null;\n        this.onFrameChange = null;\n        this.onLoop = null;\n    }\n\n    /**\n     * A short hand way of creating an AnimatedSprite from an array of frame ids.\n     * @param frames - The array of frames ids the AnimatedSprite will use as its texture frames.\n     * @returns - The new animated sprite with the specified frames.\n     */\n    public static fromFrames(frames: string[]): AnimatedSprite\n    {\n        const textures = [];\n\n        for (let i = 0; i < frames.length; ++i)\n        {\n            textures.push(Texture.from(frames[i]));\n        }\n\n        return new AnimatedSprite(textures);\n    }\n\n    /**\n     * A short hand way of creating an AnimatedSprite from an array of image ids.\n     * @param images - The array of image urls the AnimatedSprite will use as its texture frames.\n     * @returns The new animate sprite with the specified images as frames.\n     */\n    public static fromImages(images: string[]): AnimatedSprite\n    {\n        const textures = [];\n\n        for (let i = 0; i < images.length; ++i)\n        {\n            textures.push(Texture.from(images[i]));\n        }\n\n        return new AnimatedSprite(textures);\n    }\n\n    /**\n     * The total number of frames in the AnimatedSprite. This is the same as number of textures\n     * assigned to the AnimatedSprite.\n     * @readonly\n     * @default 0\n     */\n    get totalFrames(): number\n    {\n        return this._textures.length;\n    }\n\n    /** The array of textures used for this AnimatedSprite. */\n    get textures(): Texture[] | FrameObject[]\n    {\n        return this._textures;\n    }\n\n    set textures(value: Texture[] | FrameObject[])\n    {\n        if (value[0] instanceof Texture)\n        {\n            this._textures = value as Texture[];\n            this._durations = null;\n        }\n        else\n        {\n            this._textures = [];\n            this._durations = [];\n\n            for (let i = 0; i < value.length; i++)\n            {\n                this._textures.push((value[i] as FrameObject).texture);\n                this._durations.push((value[i] as FrameObject).time);\n            }\n        }\n        this._previousFrame = null;\n        this.gotoAndStop(0);\n        this.updateTexture();\n    }\n\n    /**\n     * The AnimatedSprites current frame index.\n     * @readonly\n     */\n    get currentFrame(): number\n    {\n        let currentFrame = Math.floor(this._currentTime) % this._textures.length;\n\n        if (currentFrame < 0)\n        {\n            currentFrame += this._textures.length;\n        }\n\n        return currentFrame;\n    }\n\n    /**\n     * Indicates if the AnimatedSprite is currently playing.\n     * @readonly\n     */\n    get playing(): boolean\n    {\n        return this._playing;\n    }\n\n    /** Whether to use PIXI.Ticker.shared to auto update animation time. */\n    get autoUpdate(): boolean\n    {\n        return this._autoUpdate;\n    }\n\n    set autoUpdate(value: boolean)\n    {\n        if (value !== this._autoUpdate)\n        {\n            this._autoUpdate = value;\n\n            if (!this._autoUpdate && this._isConnectedToTicker)\n            {\n                Ticker.shared.remove(this.update, this);\n                this._isConnectedToTicker = false;\n            }\n            else if (this._autoUpdate && !this._isConnectedToTicker && this._playing)\n            {\n                Ticker.shared.add(this.update, this);\n                this._isConnectedToTicker = true;\n            }\n        }\n    }\n}\n\n/** @memberof PIXI.AnimatedSprite */\nexport interface FrameObject\n{\n    /** The {@link PIXI.Texture} of the frame. */\n    texture: Texture;\n\n    /** The duration of the frame, in milliseconds. */\n    time: number;\n}\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "AnimatedSprite", "_super", "textures", "autoUpdate", "_this", "call", "this", "Texture", "texture", "_textures", "_durations", "_autoUpdate", "_isConnectedToTicker", "animationSpeed", "loop", "updateAnchor", "onComplete", "onFrameChange", "onLoop", "_currentTime", "_playing", "_previousFrame", "__", "constructor", "prototype", "create", "__extends", "stop", "Ticker", "shared", "remove", "update", "play", "add", "UPDATE_PRIORITY", "HIGH", "gotoAndStop", "frameNumber", "previousFrame", "currentFrame", "updateTexture", "gotoAndPlay", "deltaTime", "elapsed", "lag", "sign", "Math", "floor", "length", "_texture", "_textureID", "_textureTrimmedID", "_cachedTint", "uvs", "_uvs", "uvsFloat32", "_anchor", "copyFrom", "defaultAnchor", "destroy", "options", "fromFrames", "frames", "i", "push", "from", "fromImages", "images", "defineProperty", "get", "set", "value", "time", "Sprite"], "mappings": ";;;;;;;oJAgBIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,KAClEP,EAAcC,EAAGC,ICe5B,IAAAO,EAAA,SAAAC,GAiFI,SAAYD,EAAAE,EAAqCC,QAAA,IAAAA,IAAAA,GAAiB,GAAlE,IAEIC,EAAAH,EAAAI,KAAAC,KAAMJ,EAAS,aAAcK,EAAAA,QAAUL,EAAS,GAAKA,EAAS,GAAGM,UAoBpEF,YAlBGF,EAAKK,UAAY,KACjBL,EAAKM,WAAa,KAClBN,EAAKO,YAAcR,EACnBC,EAAKQ,sBAAuB,EAE5BR,EAAKS,eAAiB,EACtBT,EAAKU,MAAO,EACZV,EAAKW,cAAe,EACpBX,EAAKY,WAAa,KAClBZ,EAAKa,cAAgB,KACrBb,EAAKc,OAAS,KAEdd,EAAKe,aAAe,EAEpBf,EAAKgB,UAAW,EAChBhB,EAAKiB,eAAiB,KAEtBjB,EAAKF,SAAWA,IAiUxB,ODnbO,SAAmBV,EAAGC,GAEzB,SAAS6B,IAAOhB,KAAKiB,YAAc/B,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEgC,UAAkB,OAAN/B,EAAaC,OAAO+B,OAAOhC,IAAM6B,EAAGE,UAAY/B,EAAE+B,UAAW,IAAIF,GCS/CI,CAAM1B,EAAAC,GA0G/BD,EAAAwB,UAAAG,KAAP,WAESrB,KAAKc,WAKVd,KAAKc,UAAW,EACZd,KAAKK,aAAeL,KAAKM,uBAEzBgB,EAAMA,OAACC,OAAOC,OAAOxB,KAAKyB,OAAQzB,MAClCA,KAAKM,sBAAuB,KAK7BZ,EAAAwB,UAAAQ,KAAP,WAEQ1B,KAAKc,WAKTd,KAAKc,UAAW,EACZd,KAAKK,cAAgBL,KAAKM,uBAE1BgB,SAAOC,OAAOI,IAAI3B,KAAKyB,OAAQzB,KAAM4B,EAAAA,gBAAgBC,MACrD7B,KAAKM,sBAAuB,KAQ7BZ,EAAWwB,UAAAY,YAAlB,SAAmBC,GAEf/B,KAAKqB,OAEL,IAAMW,EAAgBhC,KAAKiC,aAE3BjC,KAAKa,aAAekB,EAEhBC,IAAkBhC,KAAKiC,cAEvBjC,KAAKkC,iBAQNxC,EAAWwB,UAAAiB,YAAlB,SAAmBJ,GAEf,IAAMC,EAAgBhC,KAAKiC,aAE3BjC,KAAKa,aAAekB,EAEhBC,IAAkBhC,KAAKiC,cAEvBjC,KAAKkC,gBAGTlC,KAAK0B,QAOThC,EAAMwB,UAAAO,OAAN,SAAOW,GAEH,GAAKpC,KAAKc,SAAV,CAKA,IAAMuB,EAAUrC,KAAKO,eAAiB6B,EAChCJ,EAAgBhC,KAAKiC,aAE3B,GAAwB,OAApBjC,KAAKI,WACT,CACI,IAAIkC,EAAMtC,KAAKa,aAAe,EAAIb,KAAKI,WAAWJ,KAAKiC,cAIvD,IAFAK,GAAOD,EAAU,GAAK,IAEfC,EAAM,GAETtC,KAAKa,eACLyB,GAAOtC,KAAKI,WAAWJ,KAAKiC,cAGhC,IAAMM,EAAOC,KAAKD,KAAKvC,KAAKO,eAAiB6B,GAI7C,IAFApC,KAAKa,aAAe2B,KAAKC,MAAMzC,KAAKa,cAE7ByB,GAAOtC,KAAKI,WAAWJ,KAAKiC,eAE/BK,GAAOtC,KAAKI,WAAWJ,KAAKiC,cAAgBM,EAC5CvC,KAAKa,cAAgB0B,EAGzBvC,KAAKa,cAAgByB,EAAMtC,KAAKI,WAAWJ,KAAKiC,mBAIhDjC,KAAKa,cAAgBwB,EAGrBrC,KAAKa,aAAe,IAAMb,KAAKQ,MAE/BR,KAAK8B,YAAY,GAEb9B,KAAKU,YAELV,KAAKU,cAGJV,KAAKa,cAAgBb,KAAKG,UAAUuC,SAAW1C,KAAKQ,MAEzDR,KAAK8B,YAAY9B,KAAKG,UAAUuC,OAAS,GAErC1C,KAAKU,YAELV,KAAKU,cAGJsB,IAAkBhC,KAAKiC,eAExBjC,KAAKQ,MAAQR,KAAKY,SAEdZ,KAAKO,eAAiB,GAAKP,KAAKiC,aAAeD,GAI1ChC,KAAKO,eAAiB,GAAKP,KAAKiC,aAAeD,IAFpDhC,KAAKY,SAQbZ,KAAKkC,mBAKLxC,EAAAwB,UAAAgB,cAAR,WAEI,IAAMD,EAAejC,KAAKiC,aAEtBjC,KAAKe,iBAAmBkB,IAK5BjC,KAAKe,eAAiBkB,EAEtBjC,KAAK2C,SAAW3C,KAAKG,UAAU8B,GAC/BjC,KAAK4C,YAAc,EACnB5C,KAAK6C,mBAAqB,EAC1B7C,KAAK8C,YAAc,SACnB9C,KAAK+C,IAAM/C,KAAK2C,SAASK,KAAKC,WAE1BjD,KAAKS,cAELT,KAAKkD,QAAQC,SAASnD,KAAK2C,SAASS,eAGpCpD,KAAKW,eAELX,KAAKW,cAAcX,KAAKiC,gBAazBvC,EAAOwB,UAAAmC,QAAd,SAAeC,GAEXtD,KAAKqB,OACL1B,EAAAuB,UAAMmC,QAAOtD,KAAAC,KAACsD,GAEdtD,KAAKU,WAAa,KAClBV,KAAKW,cAAgB,KACrBX,KAAKY,OAAS,MAQJlB,EAAU6D,WAAxB,SAAyBC,GAIrB,IAFA,IAAM5D,EAAW,GAER6D,EAAI,EAAGA,EAAID,EAAOd,SAAUe,EAEjC7D,EAAS8D,KAAKzD,UAAQ0D,KAAKH,EAAOC,KAGtC,OAAO,IAAI/D,EAAeE,IAQhBF,EAAUkE,WAAxB,SAAyBC,GAIrB,IAFA,IAAMjE,EAAW,GAER6D,EAAI,EAAGA,EAAII,EAAOnB,SAAUe,EAEjC7D,EAAS8D,KAAKzD,UAAQ0D,KAAKE,EAAOJ,KAGtC,OAAO,IAAI/D,EAAeE,IAS9BR,OAAA0E,eAAIpE,EAAWwB,UAAA,cAAA,CAAf6C,IAAA,WAEI,OAAO/D,KAAKG,UAAUuC,wCAI1BtD,OAAA0E,eAAIpE,EAAQwB,UAAA,WAAA,CAAZ6C,IAAA,WAEI,OAAO/D,KAAKG,WAGhB6D,IAAA,SAAaC,GAET,GAAIA,EAAM,aAAchE,UAEpBD,KAAKG,UAAY8D,EACjBjE,KAAKI,WAAa,SAGtB,CACIJ,KAAKG,UAAY,GACjBH,KAAKI,WAAa,GAElB,IAAK,IAAIqD,EAAI,EAAGA,EAAIQ,EAAMvB,OAAQe,IAE9BzD,KAAKG,UAAUuD,KAAMO,EAAMR,GAAmBvD,SAC9CF,KAAKI,WAAWsD,KAAMO,EAAMR,GAAmBS,MAGvDlE,KAAKe,eAAiB,KACtBf,KAAK8B,YAAY,GACjB9B,KAAKkC,iDAOT9C,OAAA0E,eAAIpE,EAAYwB,UAAA,eAAA,CAAhB6C,IAAA,WAEI,IAAI9B,EAAeO,KAAKC,MAAMzC,KAAKa,cAAgBb,KAAKG,UAAUuC,OAOlE,OALIT,EAAe,IAEfA,GAAgBjC,KAAKG,UAAUuC,QAG5BT,mCAOX7C,OAAA0E,eAAIpE,EAAOwB,UAAA,UAAA,CAAX6C,IAAA,WAEI,OAAO/D,KAAKc,0CAIhB1B,OAAA0E,eAAIpE,EAAUwB,UAAA,aAAA,CAAd6C,IAAA,WAEI,OAAO/D,KAAKK,aAGhB2D,IAAA,SAAeC,GAEPA,IAAUjE,KAAKK,cAEfL,KAAKK,YAAc4D,GAEdjE,KAAKK,aAAeL,KAAKM,sBAE1BgB,EAAMA,OAACC,OAAOC,OAAOxB,KAAKyB,OAAQzB,MAClCA,KAAKM,sBAAuB,GAEvBN,KAAKK,cAAgBL,KAAKM,sBAAwBN,KAAKc,WAE5DQ,EAAMA,OAACC,OAAOI,IAAI3B,KAAKyB,OAAQzB,MAC/BA,KAAKM,sBAAuB,qCAI3CZ,EAvaD,CAAoCyE"}