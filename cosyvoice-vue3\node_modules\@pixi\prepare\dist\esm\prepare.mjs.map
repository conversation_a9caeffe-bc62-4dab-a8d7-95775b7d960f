{"version": 3, "file": "prepare.mjs", "sources": ["../../src/settings.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../src/CountLimiter.ts", "../../src/BasePrepare.ts", "../../src/Prepare.ts", "../../src/TimeLimiter.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\n\n/**\n * Default number of uploads per frame using prepare plugin.\n * @static\n * @memberof PIXI.settings\n * @name UPLOADS_PER_FRAME\n * @type {number}\n * @default 4\n */\nsettings.UPLOADS_PER_FRAME = 4;\n\nexport { settings };\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * CountLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of items per frame.\n * @memberof PIXI\n */\nexport class CountLimiter\n{\n    /** The maximum number of items that can be prepared each frame. */\n    public maxItemsPerFrame: number;\n\n    /** The number of items that can be prepared in the current frame. */\n    public itemsLeft: number;\n\n    /**\n     * @param maxItemsPerFrame - The maximum number of items that can be prepared each frame.\n     */\n    constructor(maxItemsPerFrame: number)\n    {\n        this.maxItemsPerFrame = maxItemsPerFrame;\n        this.itemsLeft = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.itemsLeft = this.maxItemsPerFrame;\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return this.itemsLeft-- > 0;\n    }\n}\n", "import { Texture, BaseTexture } from '@pixi/core';\nimport { Ticker, UPDATE_PRIORITY } from '@pixi/ticker';\nimport { settings } from '@pixi/settings';\nimport type { DisplayObject } from '@pixi/display';\nimport { Container } from '@pixi/display';\nimport { Text, TextStyle, TextMetrics } from '@pixi/text';\nimport { CountLimiter } from './CountLimiter';\n\nimport type { AbstractRenderer } from '@pixi/core';\nimport { deprecation } from '@pixi/utils';\n\ninterface IArrowFunction\n{\n    (): void;\n}\ninterface IUploadHook\n{\n    (helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean;\n}\n\ninterface IFindHook\n{\n    (item: any, queue: Array<any>): boolean;\n}\n\nexport interface IDisplayObjectExtended extends DisplayObject\n{\n    _textures?: Array<Texture>;\n    _texture?: Texture;\n    style?: TextStyle | Partial<TextStyle>;\n}\n\n/**\n * Built-in hook to find multiple textures from objects like AnimatedSprites.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findMultipleBaseTextures(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    let result = false;\n\n    // Objects with multiple textures\n    if (item && item._textures && item._textures.length)\n    {\n        for (let i = 0; i < item._textures.length; i++)\n        {\n            if (item._textures[i] instanceof Texture)\n            {\n                const baseTexture = item._textures[i].baseTexture;\n\n                if (queue.indexOf(baseTexture) === -1)\n                {\n                    queue.push(baseTexture);\n                    result = true;\n                }\n            }\n        }\n    }\n\n    return result;\n}\n\n/**\n * Built-in hook to find BaseTextures from Texture.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findBaseTexture(item: Texture, queue: Array<any>): boolean\n{\n    if (item.baseTexture instanceof BaseTexture)\n    {\n        const texture = item.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find textures from objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findTexture(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item._texture && item._texture instanceof Texture)\n    {\n        const texture = item._texture.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to draw PIXI.Text to its texture.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction drawText(_helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof Text)\n    {\n        // updating text will return early if it is not dirty\n        item.updateText(true);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to calculate a text style for a PIXI.Text object.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction calculateTextStyle(_helper: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        const font = item.toFontString();\n\n        TextMetrics.measureFont(font);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find Text objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Text object was found.\n */\nfunction findText(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Text)\n    {\n        // push the text style to prepare it - this can be really expensive\n        if (queue.indexOf(item.style) === -1)\n        {\n            queue.push(item.style);\n        }\n        // also push the text object so that we can render it (to canvas/texture) if needed\n        if (queue.indexOf(item) === -1)\n        {\n            queue.push(item);\n        }\n        // also push the Text's texture for upload to GPU\n        const texture = item._texture.baseTexture;\n\n        if (queue.indexOf(texture) === -1)\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find TextStyle objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.TextStyle object was found.\n */\nfunction findTextStyle(item: TextStyle, queue: Array<any>): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        if (queue.indexOf(item) === -1)\n        {\n            queue.push(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare manager provides functionality to upload content to the GPU.\n *\n * BasePrepare handles basic queuing functionality and is extended by\n * {@link PIXI.Prepare} and {@link PIXI.CanvasPrepare}\n * to provide preparation capabilities specific to their respective renderers.\n * @example\n * // Create a sprite\n * const sprite = PIXI.Sprite.from('something.png');\n *\n * // Load object into GPU\n * app.renderer.plugins.prepare.upload(sprite, () => {\n *\n *     //Texture(s) has been uploaded to GPU\n *     app.stage.addChild(sprite);\n *\n * })\n * @abstract\n * @memberof PIXI\n */\nexport class BasePrepare\n{\n    /**\n     * The limiter to be used to control how quickly items are prepared.\n     * @type {PIXI.CountLimiter|PIXI.TimeLimiter}\n     */\n    private limiter: CountLimiter;\n\n    /** Reference to the renderer. */\n    protected renderer: AbstractRenderer;\n\n    /**\n     * The only real difference between CanvasPrepare and Prepare is what they pass\n     * to upload hooks. That different parameter is stored here.\n     */\n    protected uploadHookHelper: any;\n\n    /** Collection of items to uploads at once. */\n    protected queue: Array<any>;\n\n    /**\n     * Collection of additional hooks for finding assets.\n     * @type {Array<Function>}\n     */\n    public addHooks: Array<any>;\n\n    /**\n     * Collection of additional hooks for processing assets.\n     * @type {Array<Function>}\n     */\n    public uploadHooks: Array<any>;\n\n    /**\n     * Callback to call after completed.\n     * @type {Array<Function>}\n     */\n    public completes: Array<any>;\n\n    /**\n     * If prepare is ticking (running).\n     * @type {boolean}\n     */\n    public ticking: boolean;\n\n    /**\n     * 'bound' call for prepareItems().\n     * @type {Function}\n     */\n    private delayedTick: IArrowFunction;\n\n    /**\n     * @param {PIXI.AbstractRenderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: AbstractRenderer)\n    {\n        this.limiter = new CountLimiter(settings.UPLOADS_PER_FRAME);\n        this.renderer = renderer;\n        this.uploadHookHelper = null;\n        this.queue = [];\n        this.addHooks = [];\n        this.uploadHooks = [];\n        this.completes = [];\n        this.ticking = false;\n        this.delayedTick = (): void =>\n        {\n            // unlikely, but in case we were destroyed between tick() and delayedTick()\n            if (!this.queue)\n            {\n                return;\n            }\n            this.prepareItems();\n        };\n\n        // hooks to find the correct texture\n        this.registerFindHook(findText);\n        this.registerFindHook(findTextStyle);\n        this.registerFindHook(findMultipleBaseTextures);\n        this.registerFindHook(findBaseTexture);\n        this.registerFindHook(findTexture);\n\n        // upload hooks\n        this.registerUploadHook(drawText);\n        this.registerUploadHook(calculateTextStyle);\n    }\n\n    /**\n     * Upload all the textures and graphics to the GPU.\n     * @method PIXI.BasePrepare#upload\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} [item] -\n     *        Container or display object to search for items to upload or the items to upload themselves,\n     *        or optionally ommitted, if items have been added using {@link PIXI.BasePrepare#add `prepare.add`}.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture): Promise<void>;\n\n    /**\n     * Use the Promise-based API instead.\n     * @method PIXI.BasePrepare#upload\n     * @deprecated since version 6.5.0\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} item -\n     *        Item to upload.\n     * @param {Function} [done] - Callback when completed.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture, done?: () => void): void;\n\n    /**\n     * Use the Promise-based API instead.\n     * @method PIXI.BasePrepare#upload\n     * @deprecated since version 6.5.0\n     * @param {Function} [done] - Callback when completed.\n     */\n    upload(done?: () => void): void;\n\n    /** @ignore */\n    upload(\n        item?: IDisplayObjectExtended | Container | BaseTexture | Texture | (() => void),\n        done?: () => void): Promise<void>\n    {\n        if (typeof item === 'function')\n        {\n            done = item as () => void;\n            item = null;\n        }\n\n        // #if _DEBUG\n        if (done)\n        {\n            deprecation('6.5.0', 'BasePrepare.upload callback is deprecated, use the return Promise instead.');\n        }\n        // #endif\n\n        return new Promise((resolve) =>\n        {\n            // If a display object, search for items\n            // that we could upload\n            if (item)\n            {\n                this.add(item as IDisplayObjectExtended | Container | BaseTexture | Texture);\n            }\n\n            // TODO: remove done callback and just use resolve\n            const complete = () =>\n            {\n                done?.();\n                resolve();\n            };\n\n            // Get the items for upload from the display\n            if (this.queue.length)\n            {\n                this.completes.push(complete);\n\n                if (!this.ticking)\n                {\n                    this.ticking = true;\n                    Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n                }\n            }\n            else\n            {\n                complete();\n            }\n        });\n    }\n\n    /**\n     * Handle tick update\n     * @private\n     */\n    tick(): void\n    {\n        setTimeout(this.delayedTick, 0);\n    }\n\n    /**\n     * Actually prepare items. This is handled outside of the tick because it will take a while\n     * and we do NOT want to block the current animation frame from rendering.\n     * @private\n     */\n    prepareItems(): void\n    {\n        this.limiter.beginFrame();\n        // Upload the graphics\n        while (this.queue.length && this.limiter.allowedToUpload())\n        {\n            const item = this.queue[0];\n            let uploaded = false;\n\n            if (item && !item._destroyed)\n            {\n                for (let i = 0, len = this.uploadHooks.length; i < len; i++)\n                {\n                    if (this.uploadHooks[i](this.uploadHookHelper, item))\n                    {\n                        this.queue.shift();\n                        uploaded = true;\n                        break;\n                    }\n                }\n            }\n\n            if (!uploaded)\n            {\n                this.queue.shift();\n            }\n        }\n\n        // We're finished\n        if (!this.queue.length)\n        {\n            this.ticking = false;\n\n            const completes = this.completes.slice(0);\n\n            this.completes.length = 0;\n\n            for (let i = 0, len = completes.length; i < len; i++)\n            {\n                completes[i]();\n            }\n        }\n        else\n        {\n            // if we are not finished, on the next rAF do this again\n            Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n        }\n    }\n\n    /**\n     * Adds hooks for finding items.\n     * @param {Function} addHook - Function call that takes two parameters: `item:*, queue:Array`\n     *          function must return `true` if it was able to add item to the queue.\n     * @returns Instance of plugin for chaining.\n     */\n    registerFindHook(addHook: IFindHook): this\n    {\n        if (addHook)\n        {\n            this.addHooks.push(addHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Adds hooks for uploading items.\n     * @param {Function} uploadHook - Function call that takes two parameters: `prepare:CanvasPrepare, item:*` and\n     *          function must return `true` if it was able to handle upload of item.\n     * @returns Instance of plugin for chaining.\n     */\n    registerUploadHook(uploadHook: IUploadHook): this\n    {\n        if (uploadHook)\n        {\n            this.uploadHooks.push(uploadHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Manually add an item to the uploading queue.\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text|*} item - Object to\n     *        add to the queue\n     * @returns Instance of plugin for chaining.\n     */\n    add(item: IDisplayObjectExtended | Container | BaseTexture | Texture): this\n    {\n        // Add additional hooks for finding elements on special\n        // types of objects that\n        for (let i = 0, len = this.addHooks.length; i < len; i++)\n        {\n            if (this.addHooks[i](item, this.queue))\n            {\n                break;\n            }\n        }\n\n        // Get children recursively\n        if (item instanceof Container)\n        {\n            for (let i = item.children.length - 1; i >= 0; i--)\n            {\n                this.add(item.children[i]);\n            }\n        }\n\n        return this;\n    }\n\n    /** Destroys the plugin, don't use after this. */\n    destroy(): void\n    {\n        if (this.ticking)\n        {\n            Ticker.system.remove(this.tick, this);\n        }\n        this.ticking = false;\n        this.addHooks = null;\n        this.uploadHooks = null;\n        this.renderer = null;\n        this.completes = null;\n        this.queue = null;\n        this.limiter = null;\n        this.uploadHookHelper = null;\n    }\n}\n", "import { BaseTexture, ExtensionType } from '@pixi/core';\nimport { Graphics } from '@pixi/graphics';\nimport type { IDisplayObjectExtended } from './BasePrepare';\nimport { BasePrepare } from './BasePrepare';\n\nimport type { Abstract<PERSON><PERSON><PERSON>, Renderer, ExtensionMetadata } from '@pixi/core';\n\n/**\n * Built-in hook to upload PIXI.Texture objects to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadBaseTextures(renderer: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended | BaseTexture): boolean\n{\n    if (item instanceof BaseTexture)\n    {\n        // if the texture already has a GL texture, then the texture has been prepared or rendered\n        // before now. If the texture changed, then the changer should be calling texture.update() which\n        // reuploads the texture without need for preparing it again\n        if (!item._glTextures[(renderer as Renderer).CONTEXT_UID])\n        {\n            (renderer as Renderer).texture.bind(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to upload PIXI.Graphics to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadGraphics(renderer: AbstractRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (!(item instanceof Graphics))\n    {\n        return false;\n    }\n\n    const { geometry } = item;\n\n    // update dirty graphics to get batches\n    item.finishPoly();\n    geometry.updateBatches();\n\n    const { batches } = geometry;\n\n    // upload all textures found in styles\n    for (let i = 0; i < batches.length; i++)\n    {\n        const { texture } = batches[i].style;\n\n        if (texture)\n        {\n            uploadBaseTextures(renderer, texture.baseTexture);\n        }\n    }\n\n    // if its not batchable - update vao for particular shader\n    if (!geometry.batchable)\n    {\n        (renderer as Renderer).geometry.bind(geometry, (item as any)._resolveDirectShader((renderer as Renderer)));\n    }\n\n    return true;\n}\n\n/**\n * Built-in hook to find graphics.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Graphics object was found.\n */\nfunction findGraphics(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Graphics)\n    {\n        queue.push(item);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare plugin provides renderer-specific plugins for pre-rendering DisplayObjects. These plugins are useful for\n * asynchronously preparing and uploading to the GPU assets, textures, graphics waiting to be displayed.\n *\n * Do not instantiate this plugin directly. It is available from the `renderer.plugins` property.\n * See {@link PIXI.CanvasRenderer#plugins} or {@link PIXI.Renderer#plugins}.\n * @example\n * // Create a new application\n * const app = new PIXI.Application();\n * document.body.appendChild(app.view);\n *\n * // Don't start rendering right away\n * app.stop();\n *\n * // create a display object\n * const rect = new PIXI.Graphics()\n *     .beginFill(0x00ff00)\n *     .drawRect(40, 40, 200, 200);\n *\n * // Add to the stage\n * app.stage.addChild(rect);\n *\n * // Don't start rendering until the graphic is uploaded to the GPU\n * app.renderer.plugins.prepare.upload(app.stage, () => {\n *     app.start();\n * });\n * @memberof PIXI\n */\nexport class Prepare extends BasePrepare\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'prepare',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /**\n     * @param {PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        this.uploadHookHelper = this.renderer;\n\n        // Add textures and graphics to upload\n        this.registerFindHook(findGraphics);\n        this.registerUploadHook(uploadBaseTextures);\n        this.registerUploadHook(uploadGraphics);\n    }\n}\n", "/**\n * TimeLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of milliseconds per frame.\n * @memberof PIXI\n */\nexport class TimeLimiter\n{\n    /** The maximum milliseconds that can be spent preparing items each frame. */\n    public maxMilliseconds: number;\n\n    /**\n     * The start time of the current frame.\n     * @readonly\n     */\n    public frameStart: number;\n\n    /** @param maxMilliseconds - The maximum milliseconds that can be spent preparing items each frame. */\n    constructor(maxMilliseconds: number)\n    {\n        this.maxMilliseconds = maxMilliseconds;\n        this.frameStart = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.frameStart = Date.now();\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns - If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return Date.now() - this.frameStart < this.maxMilliseconds;\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;;;;;;;AAOG;AACH,QAAQ,CAAC,iBAAiB,GAAG,CAAC;;ACV9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;AC3BA;;;;AAIG;AACH,IAAA,YAAA,kBAAA,YAAA;AAQI;;AAEG;AACH,IAAA,SAAA,YAAA,CAAY,gBAAwB,EAAA;AAEhC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;KACtB;;AAGD,IAAA,YAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;AAEI,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;KAC1C,CAAA;AAED;;;AAGG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AAEI,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;KAC/B,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA;;ACJD;;;;;;AAMG;AACH,SAAS,wBAAwB,CAAC,IAA4B,EAAE,KAAiB,EAAA;IAE7E,IAAI,MAAM,GAAG,KAAK,CAAC;;IAGnB,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EACnD;AACI,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAC9C;YACI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,OAAO,EACxC;gBACI,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;gBAElD,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EACrC;AACI,oBAAA,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACxB,MAAM,GAAG,IAAI,CAAC;AACjB,iBAAA;AACJ,aAAA;AACJ,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,eAAe,CAAC,IAAa,EAAE,KAAiB,EAAA;AAErD,IAAA,IAAI,IAAI,CAAC,WAAW,YAAY,WAAW,EAC3C;AACI,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;QAEjC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjC;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,WAAW,CAAC,IAA4B,EAAE,KAAiB,EAAA;IAEhE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,YAAY,OAAO,EACrD;AACI,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAE1C,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjC;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,QAAQ,CAAC,OAAuC,EAAE,IAA4B,EAAA;IAEnF,IAAI,IAAI,YAAY,IAAI,EACxB;;AAEI,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAEtB,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,kBAAkB,CAAC,OAAuC,EAAE,IAA4B,EAAA;IAE7F,IAAI,IAAI,YAAY,SAAS,EAC7B;AACI,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAEjC,QAAA,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAE9B,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,QAAQ,CAAC,IAA4B,EAAE,KAAiB,EAAA;IAE7D,IAAI,IAAI,YAAY,IAAI,EACxB;;QAEI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EACpC;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC9B;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,SAAA;;AAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAE1C,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjC;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,aAAa,CAAC,IAAe,EAAE,KAAiB,EAAA;IAErD,IAAI,IAAI,YAAY,SAAS,EAC7B;QACI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC9B;AACI,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;;;;;;;;AAmBG;AACH,IAAA,WAAA,kBAAA,YAAA;AAkDI;;AAEG;AACH,IAAA,SAAA,WAAA,CAAY,QAA0B,EAAA;QAAtC,IA8BC,KAAA,GAAA,IAAA,CAAA;QA5BG,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,YAAA;;AAGf,YAAA,IAAI,CAAC,KAAI,CAAC,KAAK,EACf;gBACI,OAAO;AACV,aAAA;YACD,KAAI,CAAC,YAAY,EAAE,CAAC;AACxB,SAAC,CAAC;;AAGF,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;;AAGnC,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;KAC/C;;AA8BD,IAAA,WAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UACI,IAAgF,EAChF,IAAiB,EAAA;QAFrB,IAiDC,KAAA,GAAA,IAAA,CAAA;AA7CG,QAAA,IAAI,OAAO,IAAI,KAAK,UAAU,EAC9B;YACI,IAAI,GAAG,IAAkB,CAAC;YAC1B,IAAI,GAAG,IAAI,CAAC;AACf,SAAA;AAGD,QAAA,IAAI,IAAI,EACR;AACI,YAAA,WAAW,CAAC,OAAO,EAAE,4EAA4E,CAAC,CAAC;AACtG,SAAA;AAGD,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAA;;;AAIvB,YAAA,IAAI,IAAI,EACR;AACI,gBAAA,KAAI,CAAC,GAAG,CAAC,IAAkE,CAAC,CAAC;AAChF,aAAA;;AAGD,YAAA,IAAM,QAAQ,GAAG,YAAA;AAEb,gBAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,EAAI,CAAC;AACT,gBAAA,OAAO,EAAE,CAAC;AACd,aAAC,CAAC;;AAGF,YAAA,IAAI,KAAI,CAAC,KAAK,CAAC,MAAM,EACrB;AACI,gBAAA,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAE9B,gBAAA,IAAI,CAAC,KAAI,CAAC,OAAO,EACjB;AACI,oBAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,oBAAA,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAI,CAAC,IAAI,EAAE,KAAI,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;AACnE,iBAAA;AACJ,aAAA;AAED,iBAAA;AACI,gBAAA,QAAQ,EAAE,CAAC;AACd,aAAA;AACL,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;AAGG;AACH,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AAEI,QAAA,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KACnC,CAAA;AAED;;;;AAIG;AACH,IAAA,WAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;;AAE1B,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,EAC1D;YACI,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,QAAQ,GAAG,KAAK,CAAC;AAErB,YAAA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAC5B;AACI,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAC3D;AACI,oBAAA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,EACpD;AACI,wBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;wBACnB,QAAQ,GAAG,IAAI,CAAC;wBAChB,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;YAED,IAAI,CAAC,QAAQ,EACb;AACI,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACtB,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EACtB;AACI,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YAErB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAE1C,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAE1B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EACpD;AACI,gBAAA,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB,aAAA;AACJ,SAAA;AAED,aAAA;;AAEI,YAAA,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;AACnE,SAAA;KACJ,CAAA;AAED;;;;;AAKG;IACH,WAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,OAAkB,EAAA;AAE/B,QAAA,IAAI,OAAO,EACX;AACI,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/B,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;IACH,WAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,UAAuB,EAAA;AAEtC,QAAA,IAAI,UAAU,EACd;AACI,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;IACH,WAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,IAAgE,EAAA;;;AAIhE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EACxD;AACI,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EACtC;gBACI,MAAM;AACT,aAAA;AACJ,SAAA;;QAGD,IAAI,IAAI,YAAY,SAAS,EAC7B;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAClD;gBACI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;;AAGD,IAAA,WAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QAEI,IAAI,IAAI,CAAC,OAAO,EAChB;YACI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACzC,SAAA;AACD,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;KAChC,CAAA;IACL,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA;;AChhBD;;;;;;AAMG;AACH,SAAS,kBAAkB,CAAC,QAAwC,EAAE,IAA0C,EAAA;IAE5G,IAAI,IAAI,YAAY,WAAW,EAC/B;;;;QAII,IAAI,CAAC,IAAI,CAAC,WAAW,CAAE,QAAqB,CAAC,WAAW,CAAC,EACzD;AACK,YAAA,QAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,cAAc,CAAC,QAAwC,EAAE,IAA4B,EAAA;AAE1F,IAAA,IAAI,EAAE,IAAI,YAAY,QAAQ,CAAC,EAC/B;AACI,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAEO,IAAA,IAAA,QAAQ,GAAK,IAAI,CAAA,QAAT,CAAU;;IAG1B,IAAI,CAAC,UAAU,EAAE,CAAC;IAClB,QAAQ,CAAC,aAAa,EAAE,CAAC;AAEjB,IAAA,IAAA,OAAO,GAAK,QAAQ,CAAA,OAAb,CAAc;;AAG7B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EACvC;QACY,IAAA,OAAO,GAAK,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA,OAArB,CAAsB;AAErC,QAAA,IAAI,OAAO,EACX;AACI,YAAA,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;AACrD,SAAA;AACJ,KAAA;;AAGD,IAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB;AACK,QAAA,QAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAG,IAAY,CAAC,oBAAoB,CAAE,QAAqB,CAAC,CAAC,CAAC;AAC9G,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,YAAY,CAAC,IAA4B,EAAE,KAAiB,EAAA;IAEjE,IAAI,IAAI,YAAY,QAAQ,EAC5B;AACI,QAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEjB,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACH,IAAA,OAAA,kBAAA,UAAA,MAAA,EAAA;IAA6B,SAAW,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;AAQpC;;AAEG;AACH,IAAA,SAAA,OAAA,CAAY,QAAkB,EAAA;QAA9B,IAEI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,QAAQ,CAAC,IAQlB,IAAA,CAAA;AANG,QAAA,KAAI,CAAC,gBAAgB,GAAG,KAAI,CAAC,QAAQ,CAAC;;AAGtC,QAAA,KAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACpC,QAAA,KAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AAC5C,QAAA,KAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;;KAC3C;;AAlBM,IAAA,OAAA,CAAA,SAAS,GAAsB;AAClC,QAAA,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,aAAa,CAAC,cAAc;KACrC,CAAC;IAgBN,OAAC,OAAA,CAAA;CAAA,CAtB4B,WAAW,CAsBvC;;AC/ID;;;;AAIG;AACH,IAAA,WAAA,kBAAA,YAAA;;AAYI,IAAA,SAAA,WAAA,CAAY,eAAuB,EAAA;AAE/B,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;AACvC,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;KACvB;;AAGD,IAAA,WAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;AAEI,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAChC,CAAA;AAED;;;AAGG;AACH,IAAA,WAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AAEI,QAAA,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;KAC9D,CAAA;IACL,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA;;;;"}