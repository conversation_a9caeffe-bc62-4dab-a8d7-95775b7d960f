{"name": "@pixi/prepare", "version": "6.5.10", "main": "dist/cjs/prepare.js", "module": "dist/esm/prepare.mjs", "bundle": "dist/browser/prepare.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/prepare.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/prepare.js"}}}, "description": "Plugin to allow uploading textures to the GPU", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/core": "6.5.10", "@pixi/display": "6.5.10", "@pixi/graphics": "6.5.10", "@pixi/settings": "6.5.10", "@pixi/text": "6.5.10", "@pixi/ticker": "6.5.10", "@pixi/utils": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}