/*!
 * @pixi/accessibility - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/accessibility is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{DisplayObject as e}from"@pixi/display";import{isMobile as t,removeItems as i}from"@pixi/utils";import{ExtensionType as s}from"@pixi/core";var n={accessible:!1,accessibleTitle:null,accessibleHint:null,tabIndex:0,_accessibleActive:!1,_accessibleDiv:null,accessibleType:"button",accessiblePointerEvents:"auto",accessibleChildren:!0,renderId:-1};e.mixin(n);var o=function(){function e(e){this.debug=!1,this._isActive=!1,this._isMobileAccessibility=!1,this.pool=[],this.renderId=0,this.children=[],this.androidUpdateCount=0,this.androidUpdateFrequency=500,this._hookDiv=null,(t.tablet||t.phone)&&this.createTouchHook();var i=document.createElement("div");i.style.width="100px",i.style.height="100px",i.style.position="absolute",i.style.top="0px",i.style.left="0px",i.style.zIndex=2..toString(),this.div=i,this.renderer=e,this._onKeyDown=this._onKeyDown.bind(this),this._onMouseMove=this._onMouseMove.bind(this),globalThis.addEventListener("keydown",this._onKeyDown,!1)}return Object.defineProperty(e.prototype,"isActive",{get:function(){return this._isActive},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isMobileAccessibility",{get:function(){return this._isMobileAccessibility},enumerable:!1,configurable:!0}),e.prototype.createTouchHook=function(){var e=this,t=document.createElement("button");t.style.width="1px",t.style.height="1px",t.style.position="absolute",t.style.top="-1000px",t.style.left="-1000px",t.style.zIndex=2..toString(),t.style.backgroundColor="#FF0000",t.title="select to enable accessibility for this content",t.addEventListener("focus",(function(){e._isMobileAccessibility=!0,e.activate(),e.destroyTouchHook()})),document.body.appendChild(t),this._hookDiv=t},e.prototype.destroyTouchHook=function(){this._hookDiv&&(document.body.removeChild(this._hookDiv),this._hookDiv=null)},e.prototype.activate=function(){var e;this._isActive||(this._isActive=!0,globalThis.document.addEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown,!1),this.renderer.on("postrender",this.update,this),null===(e=this.renderer.view.parentNode)||void 0===e||e.appendChild(this.div))},e.prototype.deactivate=function(){var e;this._isActive&&!this._isMobileAccessibility&&(this._isActive=!1,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.addEventListener("keydown",this._onKeyDown,!1),this.renderer.off("postrender",this.update),null===(e=this.div.parentNode)||void 0===e||e.removeChild(this.div))},e.prototype.updateAccessibleObjects=function(e){if(e.visible&&e.accessibleChildren){e.accessible&&e.interactive&&(e._accessibleActive||this.addChild(e),e.renderId=this.renderId);var t=e.children;if(t)for(var i=0;i<t.length;i++)this.updateAccessibleObjects(t[i])}},e.prototype.update=function(){var e=performance.now();if(!(t.android.device&&e<this.androidUpdateCount)&&(this.androidUpdateCount=e+this.androidUpdateFrequency,this.renderer.renderingToScreen)){this.renderer._lastObjectRendered&&this.updateAccessibleObjects(this.renderer._lastObjectRendered);var s=this.renderer.view.getBoundingClientRect(),n=s.left,o=s.top,r=s.width,l=s.height,a=this.renderer,c=a.width,d=a.height,h=a.resolution,p=r/c*h,u=l/d*h,b=this.div;b.style.left=n+"px",b.style.top=o+"px",b.style.width=c+"px",b.style.height=d+"px";for(var v=0;v<this.children.length;v++){var y=this.children[v];if(y.renderId!==this.renderId)y._accessibleActive=!1,i(this.children,v,1),this.div.removeChild(y._accessibleDiv),this.pool.push(y._accessibleDiv),y._accessibleDiv=null,v--;else{b=y._accessibleDiv;var g=y.hitArea,x=y.worldTransform;y.hitArea?(b.style.left=(x.tx+g.x*x.a)*p+"px",b.style.top=(x.ty+g.y*x.d)*u+"px",b.style.width=g.width*x.a*p+"px",b.style.height=g.height*x.d*u+"px"):(g=y.getBounds(),this.capHitArea(g),b.style.left=g.x*p+"px",b.style.top=g.y*u+"px",b.style.width=g.width*p+"px",b.style.height=g.height*u+"px",b.title!==y.accessibleTitle&&null!==y.accessibleTitle&&(b.title=y.accessibleTitle),b.getAttribute("aria-label")!==y.accessibleHint&&null!==y.accessibleHint&&b.setAttribute("aria-label",y.accessibleHint)),y.accessibleTitle===b.title&&y.tabIndex===b.tabIndex||(b.title=y.accessibleTitle,b.tabIndex=y.tabIndex,this.debug&&this.updateDebugHTML(b))}}this.renderId++}},e.prototype.updateDebugHTML=function(e){e.innerHTML="type: "+e.type+"</br> title : "+e.title+"</br> tabIndex: "+e.tabIndex},e.prototype.capHitArea=function(e){e.x<0&&(e.width+=e.x,e.x=0),e.y<0&&(e.height+=e.y,e.y=0);var t=this.renderer,i=t.width,s=t.height;e.x+e.width>i&&(e.width=i-e.x),e.y+e.height>s&&(e.height=s-e.y)},e.prototype.addChild=function(e){var t=this.pool.pop();t||((t=document.createElement("button")).style.width="100px",t.style.height="100px",t.style.backgroundColor=this.debug?"rgba(255,255,255,0.5)":"transparent",t.style.position="absolute",t.style.zIndex=2..toString(),t.style.borderStyle="none",navigator.userAgent.toLowerCase().indexOf("chrome")>-1?t.setAttribute("aria-live","off"):t.setAttribute("aria-live","polite"),navigator.userAgent.match(/rv:.*Gecko\//)?t.setAttribute("aria-relevant","additions"):t.setAttribute("aria-relevant","text"),t.addEventListener("click",this._onClick.bind(this)),t.addEventListener("focus",this._onFocus.bind(this)),t.addEventListener("focusout",this._onFocusOut.bind(this))),t.style.pointerEvents=e.accessiblePointerEvents,t.type=e.accessibleType,e.accessibleTitle&&null!==e.accessibleTitle?t.title=e.accessibleTitle:e.accessibleHint&&null!==e.accessibleHint||(t.title="displayObject "+e.tabIndex),e.accessibleHint&&null!==e.accessibleHint&&t.setAttribute("aria-label",e.accessibleHint),this.debug&&this.updateDebugHTML(t),e._accessibleActive=!0,e._accessibleDiv=t,t.displayObject=e,this.children.push(e),this.div.appendChild(e._accessibleDiv),e._accessibleDiv.tabIndex=e.tabIndex},e.prototype._onClick=function(e){var t=this.renderer.plugins.interaction,i=e.target.displayObject,s=t.eventData;t.dispatchEvent(i,"click",s),t.dispatchEvent(i,"pointertap",s),t.dispatchEvent(i,"tap",s)},e.prototype._onFocus=function(e){e.target.getAttribute("aria-live")||e.target.setAttribute("aria-live","assertive");var t=this.renderer.plugins.interaction,i=e.target.displayObject,s=t.eventData;t.dispatchEvent(i,"mouseover",s)},e.prototype._onFocusOut=function(e){e.target.getAttribute("aria-live")||e.target.setAttribute("aria-live","polite");var t=this.renderer.plugins.interaction,i=e.target.displayObject,s=t.eventData;t.dispatchEvent(i,"mouseout",s)},e.prototype._onKeyDown=function(e){9===e.keyCode&&this.activate()},e.prototype._onMouseMove=function(e){0===e.movementX&&0===e.movementY||this.deactivate()},e.prototype.destroy=function(){this.destroyTouchHook(),this.div=null,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown),this.pool=null,this.children=null,this.renderer=null},e.extension={name:"accessibility",type:[s.RendererPlugin,s.CanvasRendererPlugin]},e}();export{o as AccessibilityManager,n as accessibleTarget};
//# sourceMappingURL=accessibility.min.mjs.map
