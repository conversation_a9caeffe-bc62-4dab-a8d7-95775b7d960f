/*!
 * @pixi/mixin-get-global-position - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-get-global-position is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{},function(t,i){"use strict";t.DisplayObject.prototype.getGlobalPosition=function(t,o){return void 0===t&&(t=new i.Point),void 0===o&&(o=!1),this.parent?this.parent.toGlobal(this.position,t,o):(t.x=this.position.x,t.y=this.position.y),t}}(PIXI,PIXI);
//# sourceMappingURL=mixin-get-global-position.min.js.map
