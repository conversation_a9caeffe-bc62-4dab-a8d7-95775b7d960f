/*!
 * @pixi/sprite - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/sprite is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_sprite=function(t,e,i,r,s,n,o){"use strict";var h=function(t,e){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},h(t,e)};var a=new s.Point,u=new Uint16Array([0,1,2,0,2,3]),_=function(t){function _(r){var o=t.call(this)||this;return o._anchor=new s.ObservablePoint(o._onAnchorUpdate,o,r?r.defaultAnchor.x:0,r?r.defaultAnchor.y:0),o._texture=null,o._width=0,o._height=0,o._tint=null,o._tintRGB=null,o.tint=16777215,o.blendMode=e.BLEND_MODES.NORMAL,o._cachedTint=16777215,o.uvs=null,o.texture=r||i.Texture.EMPTY,o.vertexData=new Float32Array(8),o.vertexTrimmedData=null,o._transformID=-1,o._textureID=-1,o._transformTrimmedID=-1,o._textureTrimmedID=-1,o.indices=u,o.pluginName="batch",o.isSprite=!0,o._roundPixels=n.settings.ROUND_PIXELS,o}return function(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(_,t),_.prototype._onTextureUpdate=function(){this._textureID=-1,this._textureTrimmedID=-1,this._cachedTint=16777215,this._width&&(this.scale.x=o.sign(this.scale.x)*this._width/this._texture.orig.width),this._height&&(this.scale.y=o.sign(this.scale.y)*this._height/this._texture.orig.height)},_.prototype._onAnchorUpdate=function(){this._transformID=-1,this._transformTrimmedID=-1},_.prototype.calculateVertices=function(){var t=this._texture;if(this._transformID!==this.transform._worldID||this._textureID!==t._updateID){this._textureID!==t._updateID&&(this.uvs=this._texture._uvs.uvsFloat32),this._transformID=this.transform._worldID,this._textureID=t._updateID;var e=this.transform.worldTransform,i=e.a,r=e.b,s=e.c,o=e.d,h=e.tx,a=e.ty,u=this.vertexData,_=t.trim,c=t.orig,l=this._anchor,d=0,x=0,p=0,f=0;if(_?(d=(x=_.x-l._x*c.width)+_.width,p=(f=_.y-l._y*c.height)+_.height):(d=(x=-l._x*c.width)+c.width,p=(f=-l._y*c.height)+c.height),u[0]=i*x+s*f+h,u[1]=o*f+r*x+a,u[2]=i*d+s*f+h,u[3]=o*f+r*d+a,u[4]=i*d+s*p+h,u[5]=o*p+r*d+a,u[6]=i*x+s*p+h,u[7]=o*p+r*x+a,this._roundPixels)for(var m=n.settings.RESOLUTION,g=0;g<u.length;++g)u[g]=Math.round((u[g]*m|0)/m)}},_.prototype.calculateTrimmedVertices=function(){if(this.vertexTrimmedData){if(this._transformTrimmedID===this.transform._worldID&&this._textureTrimmedID===this._texture._updateID)return}else this.vertexTrimmedData=new Float32Array(8);this._transformTrimmedID=this.transform._worldID,this._textureTrimmedID=this._texture._updateID;var t=this._texture,e=this.vertexTrimmedData,i=t.orig,r=this._anchor,s=this.transform.worldTransform,n=s.a,o=s.b,h=s.c,a=s.d,u=s.tx,_=s.ty,c=-r._x*i.width,l=c+i.width,d=-r._y*i.height,x=d+i.height;e[0]=n*c+h*d+u,e[1]=a*d+o*c+_,e[2]=n*l+h*d+u,e[3]=a*d+o*l+_,e[4]=n*l+h*x+u,e[5]=a*x+o*l+_,e[6]=n*c+h*x+u,e[7]=a*x+o*c+_},_.prototype._render=function(t){this.calculateVertices(),t.batch.setObjectRenderer(t.plugins[this.pluginName]),t.plugins[this.pluginName].render(this)},_.prototype._calculateBounds=function(){var t=this._texture.trim,e=this._texture.orig;!t||t.width===e.width&&t.height===e.height?(this.calculateVertices(),this._bounds.addQuad(this.vertexData)):(this.calculateTrimmedVertices(),this._bounds.addQuad(this.vertexTrimmedData))},_.prototype.getLocalBounds=function(e){return 0===this.children.length?(this._localBounds||(this._localBounds=new r.Bounds),this._localBounds.minX=this._texture.orig.width*-this._anchor._x,this._localBounds.minY=this._texture.orig.height*-this._anchor._y,this._localBounds.maxX=this._texture.orig.width*(1-this._anchor._x),this._localBounds.maxY=this._texture.orig.height*(1-this._anchor._y),e||(this._localBoundsRect||(this._localBoundsRect=new s.Rectangle),e=this._localBoundsRect),this._localBounds.getRectangle(e)):t.prototype.getLocalBounds.call(this,e)},_.prototype.containsPoint=function(t){this.worldTransform.applyInverse(t,a);var e=this._texture.orig.width,i=this._texture.orig.height,r=-e*this.anchor.x,s=0;return a.x>=r&&a.x<r+e&&(s=-i*this.anchor.y,a.y>=s&&a.y<s+i)},_.prototype.destroy=function(e){if(t.prototype.destroy.call(this,e),this._texture.off("update",this._onTextureUpdate,this),this._anchor=null,"boolean"==typeof e?e:e&&e.texture){var i="boolean"==typeof e?e:e&&e.baseTexture;this._texture.destroy(!!i)}this._texture=null},_.from=function(t,e){return new _(t instanceof i.Texture?t:i.Texture.from(t,e))},Object.defineProperty(_.prototype,"roundPixels",{get:function(){return this._roundPixels},set:function(t){this._roundPixels!==t&&(this._transformID=-1),this._roundPixels=t},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"width",{get:function(){return Math.abs(this.scale.x)*this._texture.orig.width},set:function(t){var e=o.sign(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"height",{get:function(){return Math.abs(this.scale.y)*this._texture.orig.height},set:function(t){var e=o.sign(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"anchor",{get:function(){return this._anchor},set:function(t){this._anchor.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t,this._tintRGB=(t>>16)+(65280&t)+((255&t)<<16)},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"texture",{get:function(){return this._texture},set:function(t){this._texture!==t&&(this._texture&&this._texture.off("update",this._onTextureUpdate,this),this._texture=t||i.Texture.EMPTY,this._cachedTint=16777215,this._textureID=-1,this._textureTrimmedID=-1,t&&(t.baseTexture.valid?this._onTextureUpdate():t.once("update",this._onTextureUpdate,this)))},enumerable:!1,configurable:!0}),_}(r.Container);return t.Sprite=_,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI,PIXI,PIXI,PIXI,PIXI.utils);Object.assign(this.PIXI,_pixi_sprite);
//# sourceMappingURL=sprite.min.js.map
