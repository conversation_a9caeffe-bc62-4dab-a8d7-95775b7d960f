/*!
 * @pixi/prepare - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/prepare is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_prepare=function(e,t,i,r,n,o,s){"use strict";t.settings.UPLOADS_PER_FRAME=4;var u=function(e,t){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},u(e,t)};var a=function(){function e(e){this.maxItemsPerFrame=e,this.itemsLeft=0}return e.prototype.beginFrame=function(){this.itemsLeft=this.maxItemsPerFrame},e.prototype.allowedToUpload=function(){return this.itemsLeft-- >0},e}();function h(e,t){var r=!1;if(e&&e._textures&&e._textures.length)for(var n=0;n<e._textures.length;n++)if(e._textures[n]instanceof i.Texture){var o=e._textures[n].baseTexture;-1===t.indexOf(o)&&(t.push(o),r=!0)}return r}function c(e,t){if(e.baseTexture instanceof i.BaseTexture){var r=e.baseTexture;return-1===t.indexOf(r)&&t.push(r),!0}return!1}function l(e,t){if(e._texture&&e._texture instanceof i.Texture){var r=e._texture.baseTexture;return-1===t.indexOf(r)&&t.push(r),!0}return!1}function p(e,t){return t instanceof s.Text&&(t.updateText(!0),!0)}function f(e,t){if(t instanceof s.TextStyle){var i=t.toFontString();return s.TextMetrics.measureFont(i),!0}return!1}function d(e,t){if(e instanceof s.Text){-1===t.indexOf(e.style)&&t.push(e.style),-1===t.indexOf(e)&&t.push(e);var i=e._texture.baseTexture;return-1===t.indexOf(i)&&t.push(i),!0}return!1}function x(e,t){return e instanceof s.TextStyle&&(-1===t.indexOf(e)&&t.push(e),!0)}var k=function(){function e(e){var i=this;this.limiter=new a(t.settings.UPLOADS_PER_FRAME),this.renderer=e,this.uploadHookHelper=null,this.queue=[],this.addHooks=[],this.uploadHooks=[],this.completes=[],this.ticking=!1,this.delayedTick=function(){i.queue&&i.prepareItems()},this.registerFindHook(d),this.registerFindHook(x),this.registerFindHook(h),this.registerFindHook(c),this.registerFindHook(l),this.registerUploadHook(p),this.registerUploadHook(f)}return e.prototype.upload=function(e,t){var i=this;return"function"==typeof e&&(t=e,e=null),new Promise((function(r){e&&i.add(e);var o=function(){null==t||t(),r()};i.queue.length?(i.completes.push(o),i.ticking||(i.ticking=!0,n.Ticker.system.addOnce(i.tick,i,n.UPDATE_PRIORITY.UTILITY))):o()}))},e.prototype.tick=function(){setTimeout(this.delayedTick,0)},e.prototype.prepareItems=function(){for(this.limiter.beginFrame();this.queue.length&&this.limiter.allowedToUpload();){var e=this.queue[0],t=!1;if(e&&!e._destroyed)for(var i=0,r=this.uploadHooks.length;i<r;i++)if(this.uploadHooks[i](this.uploadHookHelper,e)){this.queue.shift(),t=!0;break}t||this.queue.shift()}if(this.queue.length)n.Ticker.system.addOnce(this.tick,this,n.UPDATE_PRIORITY.UTILITY);else{this.ticking=!1;var o=this.completes.slice(0);this.completes.length=0;for(i=0,r=o.length;i<r;i++)o[i]()}},e.prototype.registerFindHook=function(e){return e&&this.addHooks.push(e),this},e.prototype.registerUploadHook=function(e){return e&&this.uploadHooks.push(e),this},e.prototype.add=function(e){for(var t=0,i=this.addHooks.length;t<i&&!this.addHooks[t](e,this.queue);t++);if(e instanceof o.Container)for(t=e.children.length-1;t>=0;t--)this.add(e.children[t]);return this},e.prototype.destroy=function(){this.ticking&&n.Ticker.system.remove(this.tick,this),this.ticking=!1,this.addHooks=null,this.uploadHooks=null,this.renderer=null,this.completes=null,this.queue=null,this.limiter=null,this.uploadHookHelper=null},e}();function m(e,t){return t instanceof i.BaseTexture&&(t._glTextures[e.CONTEXT_UID]||e.texture.bind(t),!0)}function g(e,t){if(!(t instanceof r.Graphics))return!1;var i=t.geometry;t.finishPoly(),i.updateBatches();for(var n=i.batches,o=0;o<n.length;o++){var s=n[o].style.texture;s&&m(e,s.baseTexture)}return i.batchable||e.geometry.bind(i,t._resolveDirectShader(e)),!0}function T(e,t){return e instanceof r.Graphics&&(t.push(e),!0)}var y=function(e){function t(t){var i=e.call(this,t)||this;return i.uploadHookHelper=i.renderer,i.registerFindHook(T),i.registerUploadHook(m),i.registerUploadHook(g),i}return function(e,t){function i(){this.constructor=e}u(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}(t,e),t.extension={name:"prepare",type:i.ExtensionType.RendererPlugin},t}(k),_=function(){function e(e){this.maxMilliseconds=e,this.frameStart=0}return e.prototype.beginFrame=function(){this.frameStart=Date.now()},e.prototype.allowedToUpload=function(){return Date.now()-this.frameStart<this.maxMilliseconds},e}();return e.BasePrepare=k,e.CountLimiter=a,e.Prepare=y,e.TimeLimiter=_,Object.defineProperty(e,"__esModule",{value:!0}),e}({},PIXI,PIXI,PIXI,PIXI,PIXI,PIXI);Object.assign(this.PIXI,_pixi_prepare);
//# sourceMappingURL=prepare.min.js.map
