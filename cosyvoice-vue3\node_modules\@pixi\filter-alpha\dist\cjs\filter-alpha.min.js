/*!
 * @pixi/filter-alpha - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/filter-alpha is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var r=require("@pixi/core"),t=function(r,e){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])},t(r,e)};var e=function(e){function o(t){void 0===t&&(t=1);var o=e.call(this,r.defaultVertex,"varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform float uAlpha;\n\nvoid main(void)\n{\n   gl_FragColor = texture2D(uSampler, vTextureCoord) * uAlpha;\n}\n",{uAlpha:1})||this;return o.alpha=t,o}return function(r,e){function o(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}(o,e),Object.defineProperty(o.prototype,"alpha",{get:function(){return this.uniforms.uAlpha},set:function(r){this.uniforms.uAlpha=r},enumerable:!1,configurable:!0}),o}(r.Filter);exports.AlphaFilter=e;
//# sourceMappingURL=filter-alpha.min.js.map
