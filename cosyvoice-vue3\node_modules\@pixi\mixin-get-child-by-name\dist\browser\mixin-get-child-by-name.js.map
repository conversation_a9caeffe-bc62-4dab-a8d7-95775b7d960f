{"version": 3, "file": "mixin-get-child-by-name.js", "sources": ["../../src/index.ts"], "sourcesContent": ["import { DisplayObject, Container } from '@pixi/display';\n\n/**\n * The instance name of the object.\n * @memberof PIXI.DisplayObject#\n * @member {string} name\n */\nDisplayObject.prototype.name = null;\n\n/**\n * Returns the display object in the container.\n *\n * Recursive searches are done in a preorder traversal.\n * @method getChildByName\n * @memberof PIXI.Container#\n * @param {string} name - Instance name.\n * @param {boolean}[deep=false] - Whether to search recursively\n * @returns {PIXI.DisplayObject} The child with the specified name.\n */\nContainer.prototype.getChildByName = function getChildByName<T extends DisplayObject = DisplayObject>(\n    name: string,\n    deep?: boolean,\n): T\n{\n    for (let i = 0, j = this.children.length; i < j; i++)\n    {\n        if (this.children[i].name === name)\n        {\n            return this.children[i];\n        }\n    }\n\n    if (deep)\n    {\n        for (let i = 0, j = this.children.length; i < j; i++)\n        {\n            const child = (this.children[i] as Container);\n\n            if (!child.getChildByName)\n            {\n                continue;\n            }\n\n            const target = child.getChildByName<T>(name, true);\n\n            if (target)\n            {\n                return target;\n            }\n        }\n    }\n\n    return null;\n};\n"], "names": ["DisplayObject", "Container"], "mappings": ";;;;;;;;;;;IAEA;;;;IAIG;AACHA,yBAAa,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;IAEpC;;;;;;;;;IASG;AACHC,qBAAS,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,cAAc,CACxD,IAAY,EACZ,IAAc,EAAA;IAGd,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACpD;YACI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAClC;IACI,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3B,SAAA;IACJ,KAAA;IAED,IAAA,IAAI,IAAI,EACR;IACI,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EACpD;gBACI,IAAM,KAAK,GAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAe,CAAC;IAE9C,YAAA,IAAI,CAAC,KAAK,CAAC,cAAc,EACzB;oBACI,SAAS;IACZ,aAAA;gBAED,IAAM,MAAM,GAAG,KAAK,CAAC,cAAc,CAAI,IAAI,EAAE,IAAI,CAAC,CAAC;IAEnD,YAAA,IAAI,MAAM,EACV;IACI,gBAAA,OAAO,MAAM,CAAC;IACjB,aAAA;IACJ,SAAA;IACJ,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IAChB,CAAC;;;;;;"}