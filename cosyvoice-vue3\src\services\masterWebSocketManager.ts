/**
 * 🚀 Master WebSocket Manager - 简化版Socket.IO客户端管理器 (V4.0)
 * 智能连接到对应的Socket.IO服务器：
 * - 漫画生成模块：连接Electron主进程 (localhost:3001)
 * - 其他模块：连接后端服务器 (localhost:7860)
 */

import { io, Socket } from 'socket.io-client';

// ==================== 基础接口定义 ====================

export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

export enum DeviceType { 
    ELECTRON = 'electron',
    BROWSER = 'browser',
    MOBILE = 'mobile',
    TABLET = 'tablet'
}

export interface ConnectionConfig {
    url?: string;
    maxReconnectAttempts?: number;
    reconnectInterval?: number;
    enableLogging?: boolean;
}

export interface WebSocketPlugin {
  name: string;
  initialize(manager: MasterWebSocketManager): Promise<void>;
  onConnect?(socket: Socket): void;
  onDisconnect?(reason: string): void;
  onMessage?(event: string, data: any): void;
  cleanup?(): void;
}

// ==================== 设备检测工具 ====================

const detectDeviceType = (): DeviceType => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (window.electronAPI || userAgent.includes('electron')) {
    return DeviceType.ELECTRON;
  }
  if (/iPad|Android.*tablet/i.test(userAgent)) {
    return DeviceType.TABLET;
  }
  
  if (/iPhone|Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
    return DeviceType.MOBILE;
  }
  
  return DeviceType.BROWSER;
};

const generateClientId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substr(2, 9);
  return `client-${timestamp}-${randomStr}`;
};

// ==================== 主管理器类 ====================

export class MasterWebSocketManager {
  private socket: Socket | null = null;
  private nativeWebSocket: WebSocket | null = null; // 🔧 新增：原生WebSocket支持
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private context: string = 'default';
  private clientId: string;
  private plugins: Map<string, WebSocketPlugin> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private enableLogging: boolean = true;
  private isNativeWebSocket: boolean = false; // 🔧 新增：标记当前连接类型

  // 🚀 连接防抖机制
  private connectDebounceTimer: NodeJS.Timeout | null = null;
  private lastConnectAttempt: number = 0;
  private minConnectInterval: number = 1000; // 最小连接间隔1秒
  
  // 🔧 连接健康检查
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private lastPongTime: number = 0;
  private healthCheckInterval: number = 30000; // 30秒检查一次
  private connectionTimeout: number = 10000; // 10秒连接超时
  private healthCheckPaused: boolean = false; // 🔧 新增：健康检查暂停标记
  private audioPlayingCallback: (() => boolean) | null = null; // 🔧 新增：音频播放状态回调

  constructor(config?: Partial<ConnectionConfig>, context?: string) {
    this.clientId = generateClientId();
    this.context = context || 'default';
    
    if (config) {
      this.maxReconnectAttempts = config.maxReconnectAttempts || 5;
      this.enableLogging = config.enableLogging !== false;
    }
    
    this.log(`🚀 MasterWebSocketManager 初始化 (上下文: ${this.context})`);
  }

  // ==================== 连接管理 ====================

  /**
   * 初始化WebSocket管理器 (如果未连接则自动连接)
   */
  public async initialize(): Promise<void> {
    if (!this.isConnected()) {
      await this.connect();
    }
  }

  public async connect(): Promise<void> {
    if (this.socket?.connected) {
      this.log('⚠️ Socket已连接，跳过重复连接');
      return;
    }

    // 🚀 防抖机制：避免频繁连接
    const now = Date.now();
    if (now - this.lastConnectAttempt < this.minConnectInterval) {
      this.log(`⏸️ 连接防抖：距离上次连接仅${now - this.lastConnectAttempt}ms，跳过`);
      return;
    }

    // 清理之前的防抖定时器
    if (this.connectDebounceTimer) {
      clearTimeout(this.connectDebounceTimer);
      this.connectDebounceTimer = null;
    }

    this.lastConnectAttempt = now;

    try {
      this.connectionState = ConnectionState.CONNECTING;
      this.emitStateChange('connecting');
      
      // 根据上下文选择连接端口和协议
      const url = this.getSocketUrl();
      const useNativeWebSocket = this.shouldUseNativeWebSocket();
      
      if (useNativeWebSocket) {
        this.log(`🔄 开始连接原生WebSocket服务器: ${url}`);
        await this.connectNativeWebSocket(url);
      } else {
        this.log(`🔄 开始连接Socket.IO服务器: ${url}`);
        await this.connectSocketIO(url);
      }

      // Socket.IO连接时设置事件监听器
      if (!this.isNativeWebSocket) {
        this.setupSocketEventListeners();
      }

    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emitStateChange('error');
      this.log(`❌ 连接失败: ${error}`);
      throw error;
    }
  }

  public disconnect(): void {
    this.log('🔌 请求断开连接');
    
    // 🚀 清理防抖定时器
    if (this.connectDebounceTimer) {
      clearTimeout(this.connectDebounceTimer);
      this.connectDebounceTimer = null;
    }

    // 🔧 生产级修复：检查是否有其他页面在使用连接
    const hasActiveListeners = this.eventListeners.size > 0;
    if (hasActiveListeners) {
      this.log('⚠️ 检测到其他页面仍在使用连接，仅清理当前上下文的监听器');
      // 不断开物理连接，只清理特定上下文的监听器
      return;
    }

    if (this.socket) {
      this.log('🔌 断开Socket.IO连接');
      this.socket.disconnect();
      this.socket = null;
    }
    
    if (this.nativeWebSocket) {
      this.log('🔌 断开原生WebSocket连接');
      this.nativeWebSocket.close();
      this.nativeWebSocket = null;
    }
    
    this.isNativeWebSocket = false;
    this.connectionState = ConnectionState.DISCONNECTED;
    this.emitStateChange('disconnected');
  }
  
  /**
   * 🔧 新增：强制断开连接（忽略其他页面状态）
   */
  public forceDisconnect(): void {
    this.log('💥 强制断开所有连接');
    
    // 清理防抖定时器
    if (this.connectDebounceTimer) {
      clearTimeout(this.connectDebounceTimer);
      this.connectDebounceTimer = null;
    }

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    if (this.nativeWebSocket) {
      this.nativeWebSocket.close();
      this.nativeWebSocket = null;
    }
    
    this.isNativeWebSocket = false;
    this.connectionState = ConnectionState.DISCONNECTED;
    this.emitStateChange('disconnected');
    
    // 清理所有监听器
    this.eventListeners.clear();
  }

  // 🔧 新增：设置音频播放状态回调
  public setAudioPlayingCallback(callback: (() => boolean) | null): void {
    this.audioPlayingCallback = callback;
  }

  // 🔧 新增：检查是否正在播放音频
  private isAudioPlaying(): boolean {
    return this.audioPlayingCallback ? this.audioPlayingCallback() : false;
  }

  // 🔧 新增：尝试重连
  private async attemptReconnect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTING) {
      this.log('⏳ 已在重连中，跳过重复尝试');
      return;
    }

    this.log('🔄 尝试重新连接...');
    try {
      await this.connect();
      this.log('✅ 重连成功');
    } catch (error) {
      this.log(`❌ 重连失败: ${error}`);
      // 如果仍在播放音频，继续尝试重连
      if (this.isAudioPlaying()) {
        setTimeout(() => {
          this.attemptReconnect();
        }, 1000); // 1秒后再次尝试
      }
    }
  }

  // ==================== 协议检测和连接方法 ====================

  /**
   * 判断是否应该使用原生WebSocket
   */
  private shouldUseNativeWebSocket(): boolean {
    return this.context === 'realtime-dialogue' || this.context === 'default' || this.context === 'yijing-oracle' || this.context === 'oracle-dialogue';
  }

  /**
   * 连接原生WebSocket
   */
  private async connectNativeWebSocket(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.nativeWebSocket = new WebSocket(url);
        this.isNativeWebSocket = true;

        const timeout = setTimeout(() => {
          reject(new Error(`WebSocket连接超时: 无法在10秒内连接到 ${url}`));
        }, 10000);

        this.nativeWebSocket.onopen = () => {
          clearTimeout(timeout);
          this.connectionState = ConnectionState.CONNECTED;
          this.reconnectAttempts = 0;
          this.log(`✅ 原生WebSocket连接成功: ${url}`);
          this.emitStateChange('connected');
          
          // 🔧 启动健康检查
          this.startHealthCheck();
          
          // 发送连接消息
          this.sendNativeWebSocketMessage({
            type: 'connect',
            clientId: this.clientId,
            context: this.context,
            deviceType: detectDeviceType()
          });
          
          resolve();
        };

        this.nativeWebSocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            
            // 🔧 处理pong消息更新健康状态
            if (message.type === 'pong') {
              this.lastPongTime = Date.now();
              return;
            }
            
            this.handleNativeWebSocketMessage(message);
          } catch (error) {
            this.log(`WebSocket消息解析失败: ${error}`);
          }
        };

        this.nativeWebSocket.onclose = () => {
          this.connectionState = ConnectionState.DISCONNECTED;
          this.log('🔌 原生WebSocket连接关闭');
          
          // 🔧 停止健康检查
          this.stopHealthCheck();
          
          this.emitStateChange('disconnected');
        };

        this.nativeWebSocket.onerror = (error) => {
          clearTimeout(timeout);
          this.connectionState = ConnectionState.ERROR;

          // 🔧 修复：提供更详细的错误信息
          let errorMessage = 'Unknown WebSocket error';
          if (error instanceof ErrorEvent) {
            errorMessage = error.message || error.type || 'ErrorEvent';
          } else if (error instanceof Event) {
            errorMessage = `${error.type} event`;
          } else {
            errorMessage = String(error);
          }

          this.log(`❌ 原生WebSocket连接错误: ${errorMessage}`);
          this.emitStateChange('error');

          // 🔧 修复：在音频播放期间立即尝试重连
          if (this.isAudioPlaying()) {
            this.log('🎵 音频播放中检测到连接错误，立即尝试重连');
            setTimeout(() => {
              this.attemptReconnect();
            }, 100); // 立即重连，不等待
          }

          reject(new Error(`WebSocket连接失败: ${errorMessage}`));
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 连接Socket.IO
   */
  private async connectSocketIO(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Socket.IO连接选项
        const socketOptions: any = {
          query: {
            deviceType: detectDeviceType(),
            context: this.context,
            clientId: this.clientId,
            originHost: window.location.hostname
          },
          transports: ['websocket', 'polling'],
          forceNew: true
        };

        this.socket = io(url, socketOptions);
        this.isNativeWebSocket = false;

        const timeout = setTimeout(() => {
          reject(new Error(`Socket.IO连接超时: 无法在10秒内连接到 ${url}`));
        }, 10000);

        this.socket.on('connect', () => {
          clearTimeout(timeout);
          this.log(`🎉 成功连接到Socket.IO服务器: ${url} (上下文: ${this.context})`);
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          clearTimeout(timeout);
          this.log(`❌ Socket.IO连接错误: ${error.message || error}`);
          reject(new Error(`连接失败: ${error.message || error}`));
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 处理原生WebSocket消息
   */
  private handleNativeWebSocketMessage(message: any): void {
    const eventType = message.type;
    const data = message.data || message;

    this.log(`📨 收到原生WebSocket消息: ${eventType}`, data);

    // 触发对应的事件监听器
    const listeners = this.eventListeners.get(eventType);
    if (listeners && listeners.length > 0) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          this.log(`事件监听器执行失败: ${error}`);
        }
      });
    }

    // 通知插件
    for (const plugin of this.plugins.values()) {
      plugin.onMessage?.(eventType, data);
    }
  }

  /**
   * 发送原生WebSocket消息
   */
  private sendNativeWebSocketMessage(message: any): void {
    if (this.nativeWebSocket && this.nativeWebSocket.readyState === WebSocket.OPEN) {
      this.nativeWebSocket.send(JSON.stringify(message));
    }
  }

  public isConnected(): boolean {
    if (this.isNativeWebSocket) {
      return this.nativeWebSocket?.readyState === WebSocket.OPEN || false;
    }
    return this.socket?.connected || false;
  }

  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  public getCurrentContext(): string {
    return this.context;
  }

  public setContext(context: string): void {
    this.context = context;
  }

  public getSocket(): Socket | WebSocket | null {
    if (this.isNativeWebSocket) {
      return this.nativeWebSocket;
    }
    return this.socket;
  }

  // ==================== 消息发送和接收 ====================

  public send(event: string, data?: any): void {
    if (this.isNativeWebSocket) {
      if (!this.nativeWebSocket || this.nativeWebSocket.readyState !== WebSocket.OPEN) {
        this.log(`⚠️ WebSocket未连接，无法发送事件: ${event}`);
        return;
      }
      
      this.log(`📤 发送WebSocket事件: ${event}`, data);
      this.sendNativeWebSocketMessage({ type: event, data });
    } else {
      if (!this.socket?.connected) {
        this.log(`⚠️ Socket.IO未连接，无法发送事件: ${event}`);
        return;
      }

      this.log(`📤 发送Socket.IO事件: ${event}`, data);
      this.socket.emit(event, data);
    }
  }

  public on(event: string, handler: Function): () => void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(handler);

    // 如果是Socket.IO且已连接，立即注册监听器
    if (!this.isNativeWebSocket && this.socket) {
      this.socket.on(event, handler as any);
    }
    // 对于原生WebSocket，事件在handleNativeWebSocketMessage中统一处理

    // 返回取消订阅函数
    return () => {
      this.off(event, handler);
    };
  }

  public off(event: string, handler?: Function): void {
    if (handler) {
      const listeners = this.eventListeners.get(event) || [];
      const index = listeners.indexOf(handler);
      if (index >= 0) {
        listeners.splice(index, 1);
      }
      // 只对Socket.IO取消监听器
      if (!this.isNativeWebSocket) {
        this.socket?.off(event, handler as any);
      }
    } else {
      this.eventListeners.delete(event);
      // 只对Socket.IO取消监听器
      if (!this.isNativeWebSocket) {
        this.socket?.off(event);
      }
    }
  }

  // ==================== 插件系统 ====================

  public async registerPlugin(plugin: WebSocketPlugin): Promise<void> {
    try {
      await plugin.initialize(this);
      this.plugins.set(plugin.name, plugin);
      this.log(`🔌 插件注册成功: ${plugin.name}`);
    } catch (error) {
      this.log(`❌ 插件注册失败: ${plugin.name} - ${error}`);
      throw error;
    }
  }

  public unregisterPlugin(name: string): void {
    const plugin = this.plugins.get(name);
    if (plugin) {
      plugin.cleanup?.();
      this.plugins.delete(name);
      this.log(`🔌 插件卸载: ${name}`);
    }
  }

  // ==================== 统计和调试 ====================

  public getStats(): any {
    return {
      clientId: this.clientId,
      context: this.context,
      connectionState: this.connectionState,
      isConnected: this.isConnected(),
      reconnectAttempts: this.reconnectAttempts,
      pluginCount: this.plugins.size,
      eventListenerCount: Array.from(this.eventListeners.values())
        .reduce((total, listeners) => total + listeners.length, 0),
      lastPongTime: this.lastPongTime,
      healthStatus: this.getHealthStatus()
    };
  }
  
  /**
   * 🔧 获取连接健康状态
   */
  public getHealthStatus(): 'healthy' | 'warning' | 'error' {
    if (!this.isConnected()) return 'error';
    
    const now = Date.now();
    const timeSinceLastPong = now - this.lastPongTime;
    
    if (timeSinceLastPong > this.healthCheckInterval * 2) {
      return 'error'; // 超过2个检查周期没有响应
    } else if (timeSinceLastPong > this.healthCheckInterval) {
      return 'warning'; // 超过1个检查周期
    }
    
    return 'healthy';
  }
  
  /**
   * 🔧 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    
    this.lastPongTime = Date.now();
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckInterval);
    
    this.log('💓 连接健康检查已启动');
  }
  
  /**
   * 🔧 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
      this.log('💓 连接健康检查已停止');
    }
  }
  
  /**
   * 🔧 执行健康检查
   */
  private performHealthCheck(): void {
    // 🔧 修复：如果健康检查被暂停，跳过执行
    if (this.healthCheckPaused) {
      this.log('⏸️ 健康检查已暂停，跳过本次检查');
      return;
    }

    // 🔧 神谕之音模式：在TTS播放期间跳过健康检查
    if (this.context === 'oracle-dialogue') {
      this.log('🎵 神谕之音模式：跳过健康检查（避免TTS播放中断）');
      return;
    }

    if (!this.isConnected()) {
      this.stopHealthCheck();
      return;
    }

    const healthStatus = this.getHealthStatus();

    if (healthStatus === 'error') {
      this.log('💔 连接健康检查失败，尝试重连...');
      this.handleUnhealthyConnection();
    } else if (healthStatus === 'warning') {
      this.log('⚠️ 连接健康状况警告');
      // 🔧 神谕之音模式：不因警告断开连接
      if (this.context !== 'oracle-dialogue') {
        this.handleUnhealthyConnection();
      }
    }

    // 发送ping
    this.sendPing();
  }
  
  /**
   * 🔧 发送ping
   */
  private sendPing(): void {
    try {
      if (this.isNativeWebSocket && this.nativeWebSocket) {
        this.nativeWebSocket.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
      } else if (this.socket) {
        this.socket.emit('ping', { timestamp: Date.now() });
      }
    } catch (error) {
      this.log('❌ 发送ping失败:', error);
    }
  }
  
  /**
   * 🔧 处理不健康的连接
   */
  private handleUnhealthyConnection(): void {
    this.log('🔧 处理不健康的连接...');
    
    // 先尝试重新建立连接
    this.forceDisconnect();
    
    // 延迟重连，避免立即重连
    setTimeout(() => {
      if (this.connectionState === ConnectionState.DISCONNECTED) {
        this.log('🔄 自动重连不健康的连接...');
        this.connect().catch(error => {
          this.log('❌ 自动重连失败:', error);
        });
      }
    }, 2000);
  }
  
  /**
   * 🔧 暂停健康检查（实时对话期间使用）
   */
  public pauseHealthCheck(): void {
    this.healthCheckPaused = true;
    this.log('⏸️ 健康检查已暂停（实时对话期间）');
  }
  
  /**
   * 🔧 恢复健康检查
   */
  public resumeHealthCheck(): void {
    this.healthCheckPaused = false;
    this.lastPongTime = Date.now(); // 重置pong时间，避免误判
    this.log('▶️ 健康检查已恢复');
  }

  public forceDestroy(): void {
    this.log('🚨 强制销毁WebSocket管理器');

    // 🚀 防止重复销毁
    if (this.connectionState === ConnectionState.DISCONNECTED && !this.socket) {
      this.log('⚠️ WebSocket管理器已经销毁，跳过重复操作');
      return;
    }

    // 🔧 停止健康检查
    this.stopHealthCheck();

    // 清理所有插件
    for (const plugin of this.plugins.values()) {
      plugin.cleanup?.();
    }
    this.plugins.clear();

    // 清理事件监听器
    this.eventListeners.clear();

    // 断开连接
    this.forceDisconnect();
  }

  // ==================== 私有方法 ====================

  /**
   * 发出状态变化事件
   */
  private emitStateChange(state: string): void {
    this.log(`📡 [${this.context}] 发出状态变化事件: ${state}`);
    
    // 🔧 兼容性修复：同时发出stateChange和具体状态事件
    // 触发传统的stateChange监听器
    const stateChangeListeners = this.eventListeners.get('stateChange');
    if (stateChangeListeners && stateChangeListeners.length > 0) {
      this.log(`📡 [${this.context}] 通知 ${stateChangeListeners.length} 个 stateChange 监听器`);
      stateChangeListeners.forEach((listener, index) => {
        try {
          listener(state);
          this.log(`✅ [${this.context}] stateChange 监听器 ${index + 1} 处理成功`);
        } catch (error) {
          this.log(`❌ [${this.context}] stateChange 监听器 ${index + 1} 处理失败: ${error}`);
        }
      });
    } else {
      this.log(`⚠️ [${this.context}] 没有 stateChange 监听器注册`);
    }
    
    // 🔧 新增：同时发出具体的状态事件，兼容useWebSocket的期望
    const specificListeners = this.eventListeners.get(state);
    if (specificListeners && specificListeners.length > 0) {
      this.log(`📡 [${this.context}] 通知 ${specificListeners.length} 个 ${state} 监听器`);
      specificListeners.forEach((listener, index) => {
        try {
          listener({ state, context: this.context, timestamp: Date.now() });
          this.log(`✅ [${this.context}] ${state} 监听器 ${index + 1} 处理成功`);
        } catch (error) {
          this.log(`❌ [${this.context}] ${state} 监听器 ${index + 1} 处理失败: ${error}`);
        }
      });
    }
  }

  /**
   * 根据上下文获取WebSocket服务器URL - 支持局域网访问和混合协议
   */
  private getSocketUrl(): string {
    // 🔑 局域网访问修复：动态检测当前访问的host
    const currentHost = window.location.hostname;
    const isLanAccess = currentHost !== 'localhost' && currentHost !== '127.0.0.1';
    
    // 漫画生成模块连接Electron的Socket.IO服务器(3001)
    if (this.context === 'comic-generation') {
      const socketHost = isLanAccess ? currentHost : 'localhost';
      const url = `http://${socketHost}:3001`;
      this.log(`📡 [漫画生成] 连接到Electron Socket.IO服务器: ${url} (LAN访问: ${isLanAccess})`);
      return url;
    }
    
    // 实时对话模块和神谕之音模块连接后端的原生WebSocket服务器(7860/ws/realtime)
    if (this.context === 'realtime-dialogue' || this.context === 'default' || this.context === 'yijing-oracle' || this.context === 'oracle-dialogue') {
      const socketHost = isLanAccess ? currentHost : '127.0.0.1';
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const url = `${protocol}//${socketHost}:7860/ws/realtime`;
      const contextName = (this.context === 'yijing-oracle' || this.context === 'oracle-dialogue') ? '神谕之音' : '实时对话';
      this.log(`📡 [${contextName}] 连接到后端原生WebSocket服务器: ${url} (LAN访问: ${isLanAccess})`);
      return url;
    }
    
    // 其他模块默认连接Electron Socket.IO服务器(3001)
    const socketHost = isLanAccess ? currentHost : 'localhost';
    const url = `http://${socketHost}:3001`;
    this.log(`📡 [其他服务] 连接到Electron Socket.IO服务器: ${url} (LAN访问: ${isLanAccess})`);
    return url;
  }

  private setupSocketEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      this.connectionState = ConnectionState.CONNECTED;
      this.reconnectAttempts = 0;
      this.log(`✅ Socket.IO连接成功 (ID: ${this.socket!.id})`);
      
      // 发出状态变化事件
      this.emitStateChange('connected');
      
      // 通知所有插件
      for (const plugin of this.plugins.values()) {
        plugin.onConnect?.(this.socket!);
      }
    });

    this.socket.on('disconnect', (reason) => {
      this.connectionState = ConnectionState.DISCONNECTED;
      this.log(`🔌 Socket.IO连接断开: ${reason}`);
      
      // 发出状态变化事件
      this.emitStateChange('disconnected');
      
      // 通知所有插件
      for (const plugin of this.plugins.values()) {
        plugin.onDisconnect?.(reason);
      }
    });

    this.socket.on('connect_error', (error) => {
      this.connectionState = ConnectionState.ERROR;
      this.log(`❌ Socket.IO连接错误: ${error}`);
      
      // 发出状态变化事件
      this.emitStateChange('error');
    });

    this.socket.on('reconnect_attempt', (attempt) => {
      this.connectionState = ConnectionState.RECONNECTING;
      this.emitStateChange('reconnecting');
      this.reconnectAttempts = attempt;
      this.log(`🔄 重连尝试 ${attempt}/${this.maxReconnectAttempts}`);
    });

    // 注册所有已存储的事件监听器
    for (const [event, handlers] of this.eventListeners) {
      for (const handler of handlers) {
        this.socket.on(event, handler as any);
      }
    }
  }

  private log(message: string, data?: any): void {
    if (!this.enableLogging) return;
    
    if (data) {
      console.log(`[MasterWS:${this.context}] ${message}`, data);
    } else {
      console.log(`[MasterWS:${this.context}] ${message}`);
    }
  }
}

// ==================== 全局实例管理 ====================

// 全局单例映射 (按上下文区分)
const globalInstances: Map<string, MasterWebSocketManager> = new Map();

// 实例创建时间戳，用于防重复创建
const instanceCreationTimestamps: Map<string, number> = new Map();

/**
 * 获取WebSocket管理器实例 (单例模式)
 */
export function getMasterWebSocketManager(
  config?: Partial<ConnectionConfig>,
  context: string = 'default'
): MasterWebSocketManager {
  const key = context;

  if (!globalInstances.has(key)) {
    // 🔧 防重复创建检查：如果最近1秒内已经尝试创建过，阻止重复创建
    const now = Date.now();
    const lastCreationTime = instanceCreationTimestamps.get(key);
    
    if (lastCreationTime && (now - lastCreationTime) < 1000) {
      console.warn(`⚠️ [MasterWS] 防重复创建: ${context} 在1秒内已尝试创建，返回现有实例或等待`);
      
      // 🔧 关键修复：简化防重复逻辑，如果有现有实例直接返回
      if (globalInstances.has(key)) {
        console.log(`✅ [MasterWS] 返回现有实例: ${context}`);
        return globalInstances.get(key)!;
      }
      
      // 🔧 如果没有现有实例但时间间隔太短，强制等待并使用默认实例
      console.warn(`⚠️ [MasterWS] 频繁创建检测，强制返回任意可用实例或创建默认实例`);
      
      // 寻找任何可用的实例
      const anyAvailableInstance = Array.from(globalInstances.values())[0];
      if (anyAvailableInstance) {
        console.log(`✅ [MasterWS] 使用任意可用实例代替: ${anyAvailableInstance.getCurrentContext()}`);
        return anyAvailableInstance;
      }
    }
    
    // 记录创建时间戳
    instanceCreationTimestamps.set(key, now);
    
    const instance = new MasterWebSocketManager(config, context);
    globalInstances.set(key, instance);
    console.log(`🎯 [MasterWS] 创建新实例: ${context} (全局实例数: ${globalInstances.size})`);
  }

  return globalInstances.get(key)!;
}

/**
 * 销毁指定上下文的实例
 */
export function destroyMasterWebSocketManager(context: string = 'default'): void {
  const instance = globalInstances.get(context);
  if (instance) {
    instance.forceDestroy();
    globalInstances.delete(context);
    instanceCreationTimestamps.delete(context); // 🔧 清理创建时间戳
    console.log(`🗑️ [MasterWS] 销毁实例: ${context} (剩余实例数: ${globalInstances.size})`);
  }
}

/**
 * 🧹 清理所有WebSocket连接（内存优化专用）
 */
export function cleanupAllWebSocketConnections(): void {
  console.log(`🧹 清理所有WebSocket连接 (${globalInstances.size}个实例)`);

  for (const [context, manager] of globalInstances) {
    console.log(`🗑️ 清理连接: ${context}`);
    manager.forceDestroy();
  }

  globalInstances.clear();
  instanceCreationTimestamps.clear(); // 🔧 清理所有创建时间戳
  console.log('✅ 所有WebSocket连接已清理');
}

/**
 * 📊 获取WebSocket内存诊断信息
 */
export function getWebSocketMemoryDiagnostics(): any {
  const totalInstances = globalInstances.size;
  let activeConnections = 0;
  let inactiveConnections = 0;
  const instances: any[] = [];

  for (const [context, manager] of globalInstances) {
    const isConnected = manager.isConnected();
    if (isConnected) {
      activeConnections++;
    } else {
      inactiveConnections++;
    }

    instances.push({
      context,
      isConnected,
      state: manager.getConnectionState(),
      stats: manager.getStats()
    });
  }

  return {
    totalInstances,
    activeConnections,
    inactiveConnections,
    memoryEstimate: totalInstances * 1500, // 估算每个实例1.5KB
    instances
  };
}

/**
 * 获取所有实例的状态
 */
export function getAllManagerStats(): Record<string, any> {
  const stats: Record<string, any> = {};
  for (const [context, manager] of globalInstances) {
    stats[context] = manager.getStats();
  }
  return stats;
}