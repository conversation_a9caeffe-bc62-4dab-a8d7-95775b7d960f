{"version": 3, "file": "spritesheet.mjs", "sources": ["../../src/Spritesheet.ts", "../../src/SpritesheetLoader.ts"], "sourcesContent": ["import { Rectangle } from '@pixi/math';\nimport { Texture, BaseTexture } from '@pixi/core';\nimport { deprecation, getResolutionOfUrl } from '@pixi/utils';\nimport type { Dict } from '@pixi/utils';\nimport type { ImageResource } from '@pixi/core';\nimport type { IPointData } from '@pixi/math';\n\n/** Represents the JSON data for a spritesheet atlas. */\nexport interface ISpritesheetFrameData\n{\n    frame: {\n        x: number;\n        y: number;\n        w: number;\n        h: number;\n    };\n    trimmed?: boolean;\n    rotated?: boolean;\n    sourceSize?: {\n        w: number;\n        h: number;\n    };\n    spriteSourceSize?: {\n        x: number;\n        y: number;\n    };\n    anchor?: IPointData;\n}\n\n/** Atlas format. */\nexport interface ISpritesheetData\n{\n    frames: Dict<ISpritesheetFrameData>;\n    animations?: Dict<string[]>;\n    meta: {\n        scale: string;\n        // eslint-disable-next-line camelcase\n        related_multi_packs?: string[];\n    };\n}\n\n/**\n * Utility class for maintaining reference to a collection\n * of Textures on a single Spritesheet.\n *\n * To access a sprite sheet from your code you may pass its JSON data file to <PERSON><PERSON>'s loader:\n *\n * ```js\n * PIXI.Loader.shared.add(\"images/spritesheet.json\").load(setup);\n *\n * function setup() {\n *   let sheet = PIXI.Loader.shared.resources[\"images/spritesheet.json\"].spritesheet;\n *   ...\n * }\n * ```\n *\n * Alternately, you may circumvent the loader by instantiating the Spritesheet directly:\n * ```js\n * const sheet = new PIXI.Spritesheet(texture, spritesheetData);\n * await sheet.parse();\n * console.log('Spritesheet ready to use!');\n * ```\n *\n * With the `sheet.textures` you can create Sprite objects,`sheet.animations` can be used to create an AnimatedSprite.\n *\n * Sprite sheets can be packed using tools like {@link https://codeandweb.com/texturepacker|TexturePacker},\n * {@link https://renderhjs.net/shoebox/|Shoebox} or {@link https://github.com/krzysztof-o/spritesheet.js|Spritesheet.js}.\n * Default anchor points (see {@link PIXI.Texture#defaultAnchor}) and grouping of animation sprites are currently only\n * supported by TexturePacker.\n * @memberof PIXI\n */\nexport class Spritesheet\n{\n    /** The maximum number of Textures to build per process. */\n    static readonly BATCH_SIZE = 1000;\n\n    /** For multi-packed spritesheets, this contains a reference to all the other spritesheets it depends on. */\n    public linkedSheets: Spritesheet[] = [];\n\n    /** Reference to ths source texture. */\n    public baseTexture: BaseTexture;\n\n    /**\n     * A map containing all textures of the sprite sheet.\n     * Can be used to create a {@link PIXI.Sprite|Sprite}:\n     * ```js\n     * new PIXI.Sprite(sheet.textures[\"image.png\"]);\n     * ```\n     */\n    public textures: Dict<Texture>;\n\n    /**\n     * A map containing the textures for each animation.\n     * Can be used to create an {@link PIXI.AnimatedSprite|AnimatedSprite}:\n     * ```js\n     * new PIXI.AnimatedSprite(sheet.animations[\"anim_name\"])\n     * ```\n     */\n    public animations: Dict<Texture[]>;\n\n    /**\n     * Reference to the original JSON data.\n     * @type {object}\n     */\n    public data: ISpritesheetData;\n\n    /** The resolution of the spritesheet. */\n    public resolution: number;\n\n    /**\n     * Reference to original source image from the Loader. This reference is retained so we\n     * can destroy the Texture later on. It is never used internally.\n     */\n    private _texture: Texture;\n\n    /**\n     * Map of spritesheet frames.\n     * @type {object}\n     */\n    private _frames: Dict<ISpritesheetFrameData>;\n\n    /** Collection of frame names. */\n    private _frameKeys: string[];\n\n    /** Current batch index being processed. */\n    private _batchIndex: number;\n\n    /**\n     * Callback when parse is completed.\n     * @type {Function}\n     */\n    private _callback: (textures: Dict<Texture>) => void;\n\n    /**\n     * @param texture - Reference to the source BaseTexture object.\n     * @param {object} data - Spritesheet image data.\n     * @param resolutionFilename - The filename to consider when determining\n     *        the resolution of the spritesheet. If not provided, the imageUrl will\n     *        be used on the BaseTexture.\n     */\n    constructor(texture: BaseTexture | Texture, data: ISpritesheetData, resolutionFilename: string = null)\n    {\n        this._texture = texture instanceof Texture ? texture : null;\n        this.baseTexture = texture instanceof BaseTexture ? texture : this._texture.baseTexture;\n        this.textures = {};\n        this.animations = {};\n        this.data = data;\n\n        const resource = this.baseTexture.resource as ImageResource;\n\n        this.resolution = this._updateResolution(resolutionFilename || (resource ? resource.url : null));\n        this._frames = this.data.frames;\n        this._frameKeys = Object.keys(this._frames);\n        this._batchIndex = 0;\n        this._callback = null;\n    }\n\n    /**\n     * Generate the resolution from the filename or fallback\n     * to the meta.scale field of the JSON data.\n     * @param resolutionFilename - The filename to use for resolving\n     *        the default resolution.\n     * @returns Resolution to use for spritesheet.\n     */\n    private _updateResolution(resolutionFilename: string = null): number\n    {\n        const { scale } = this.data.meta;\n\n        // Use a defaultValue of `null` to check if a url-based resolution is set\n        let resolution = getResolutionOfUrl(resolutionFilename, null);\n\n        // No resolution found via URL\n        if (resolution === null)\n        {\n            // Use the scale value or default to 1\n            resolution = scale !== undefined ? parseFloat(scale) : 1;\n        }\n\n        // For non-1 resolutions, update baseTexture\n        if (resolution !== 1)\n        {\n            this.baseTexture.setResolution(resolution);\n        }\n\n        return resolution;\n    }\n\n    /**\n     * Parser spritesheet from loaded data. This is done asynchronously\n     * to prevent creating too many Texture within a single process.\n     * @method PIXI.Spritesheet#parse\n     */\n    public parse(): Promise<Dict<Texture>>;\n\n    /**\n     * Please use the Promise-based version of this function.\n     * @method PIXI.Spritesheet#parse\n     * @deprecated since version 6.5.0\n     * @param {Function} callback - Callback when complete returns\n     *        a map of the Textures for this spritesheet.\n     */\n    public parse(callback?: (textures?: Dict<Texture>) => void): void;\n\n    /** @ignore */\n    public parse(callback?: (textures?: Dict<Texture>) => void): Promise<Dict<Texture>>\n    {\n        // #if _DEBUG\n        if (callback)\n        {\n            deprecation('6.5.0', 'Spritesheet.parse callback is deprecated, use the return Promise instead.');\n        }\n        // #endif\n\n        return new Promise((resolve) =>\n        {\n            this._callback = (textures: Dict<Texture>) =>\n            {\n                callback?.(textures);\n                resolve(textures);\n            };\n            this._batchIndex = 0;\n\n            if (this._frameKeys.length <= Spritesheet.BATCH_SIZE)\n            {\n                this._processFrames(0);\n                this._processAnimations();\n                this._parseComplete();\n            }\n            else\n            {\n                this._nextBatch();\n            }\n        });\n    }\n\n    /**\n     * Process a batch of frames\n     * @param initialFrameIndex - The index of frame to start.\n     */\n    private _processFrames(initialFrameIndex: number): void\n    {\n        let frameIndex = initialFrameIndex;\n        const maxFrames = Spritesheet.BATCH_SIZE;\n\n        while (frameIndex - initialFrameIndex < maxFrames && frameIndex < this._frameKeys.length)\n        {\n            const i = this._frameKeys[frameIndex];\n            const data = this._frames[i];\n            const rect = data.frame;\n\n            if (rect)\n            {\n                let frame = null;\n                let trim = null;\n                const sourceSize = data.trimmed !== false && data.sourceSize\n                    ? data.sourceSize : data.frame;\n\n                const orig = new Rectangle(\n                    0,\n                    0,\n                    Math.floor(sourceSize.w) / this.resolution,\n                    Math.floor(sourceSize.h) / this.resolution\n                );\n\n                if (data.rotated)\n                {\n                    frame = new Rectangle(\n                        Math.floor(rect.x) / this.resolution,\n                        Math.floor(rect.y) / this.resolution,\n                        Math.floor(rect.h) / this.resolution,\n                        Math.floor(rect.w) / this.resolution\n                    );\n                }\n                else\n                {\n                    frame = new Rectangle(\n                        Math.floor(rect.x) / this.resolution,\n                        Math.floor(rect.y) / this.resolution,\n                        Math.floor(rect.w) / this.resolution,\n                        Math.floor(rect.h) / this.resolution\n                    );\n                }\n\n                //  Check to see if the sprite is trimmed\n                if (data.trimmed !== false && data.spriteSourceSize)\n                {\n                    trim = new Rectangle(\n                        Math.floor(data.spriteSourceSize.x) / this.resolution,\n                        Math.floor(data.spriteSourceSize.y) / this.resolution,\n                        Math.floor(rect.w) / this.resolution,\n                        Math.floor(rect.h) / this.resolution\n                    );\n                }\n\n                this.textures[i] = new Texture(\n                    this.baseTexture,\n                    frame,\n                    orig,\n                    trim,\n                    data.rotated ? 2 : 0,\n                    data.anchor\n                );\n\n                // lets also add the frame to pixi's global cache for 'from' and 'fromLoader' functions\n                Texture.addToCache(this.textures[i], i);\n            }\n\n            frameIndex++;\n        }\n    }\n\n    /** Parse animations config. */\n    private _processAnimations(): void\n    {\n        const animations = this.data.animations || {};\n\n        for (const animName in animations)\n        {\n            this.animations[animName] = [];\n            for (let i = 0; i < animations[animName].length; i++)\n            {\n                const frameName = animations[animName][i];\n\n                this.animations[animName].push(this.textures[frameName]);\n            }\n        }\n    }\n\n    /** The parse has completed. */\n    private _parseComplete(): void\n    {\n        const callback = this._callback;\n\n        this._callback = null;\n        this._batchIndex = 0;\n        callback.call(this, this.textures);\n    }\n\n    /** Begin the next batch of textures. */\n    private _nextBatch(): void\n    {\n        this._processFrames(this._batchIndex * Spritesheet.BATCH_SIZE);\n        this._batchIndex++;\n        setTimeout(() =>\n        {\n            if (this._batchIndex * Spritesheet.BATCH_SIZE < this._frameKeys.length)\n            {\n                this._nextBatch();\n            }\n            else\n            {\n                this._processAnimations();\n                this._parseComplete();\n            }\n        }, 0);\n    }\n\n    /**\n     * Destroy Spritesheet and don't use after this.\n     * @param {boolean} [destroyBase=false] - Whether to destroy the base texture as well\n     */\n    public destroy(destroyBase = false): void\n    {\n        for (const i in this.textures)\n        {\n            this.textures[i].destroy();\n        }\n        this._frames = null;\n        this._frameKeys = null;\n        this.data = null;\n        this.textures = null;\n        if (destroyBase)\n        {\n            this._texture?.destroy();\n            this.baseTexture.destroy();\n        }\n        this._texture = null;\n        this.baseTexture = null;\n        this.linkedSheets = [];\n    }\n}\n\n/**\n * Reference to Spritesheet object created.\n * @member {PIXI.Spritesheet} spritesheet\n * @memberof PIXI.LoaderResource\n * @instance\n */\n\n/**\n * Dictionary of textures from Spritesheet.\n * @member {Object<string, PIXI.Texture>} textures\n * @memberof PIXI.LoaderResource\n * @instance\n */\n", "import { url } from '@pixi/utils';\nimport { Spritesheet } from './Spritesheet';\nimport { LoaderResource } from '@pixi/loaders';\nimport type { Loader } from '@pixi/loaders';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * {@link PIXI.Loader} middleware for loading texture atlases that have been created with\n * TexturePacker or similar JSON-based spritesheet.\n *\n * This middleware automatically generates Texture resources.\n *\n * If you're using Webpack or other bundlers and plan on bundling the atlas' JSON,\n * use the {@link PIXI.Spritesheet} class to directly parse the JSON.\n *\n * The Loader's image Resource name is automatically appended with `\"_image\"`.\n * If a Resource with this name is already loaded, the Loader will skip parsing the\n * Spritesheet. The code below will generate an internal Loader Resource called `\"myatlas_image\"`.\n * @example\n * loader.add('myatlas', 'path/to/myatlas.json');\n * loader.load(() => {\n *   loader.resources.myatlas; // atlas JSON resource\n *   loader.resources.myatlas_image; // atlas Image resource\n * });\n * @memberof PIXI\n */\nexport class SpritesheetLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /**\n     * Called after a resource is loaded.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource\n     * @param next\n     */\n    static use(resource: LoaderResource, next: (...args: unknown[]) => void): void\n    {\n        // because this is middleware, it execute in loader context. `this` = loader\n        const loader = (this as any) as Loader;\n        const imageResourceName = `${resource.name}_image`;\n\n        // skip if no data, its not json, it isn't spritesheet data, or the image resource already exists\n        if (!resource.data\n            || resource.type !== LoaderResource.TYPE.JSON\n            || !resource.data.frames\n            || loader.resources[imageResourceName]\n        )\n        {\n            next();\n\n            return;\n        }\n\n        // Check and add the multi atlas\n        // Heavily influenced and based on https://github.com/rocket-ua/pixi-tps-loader/blob/master/src/ResourceLoader.js\n        // eslint-disable-next-line camelcase\n        const multiPacks = resource.data?.meta?.related_multi_packs;\n\n        if (Array.isArray(multiPacks))\n        {\n            for (const item of multiPacks)\n            {\n                if (typeof item !== 'string')\n                {\n                    continue;\n                }\n\n                const itemName = item.replace('.json', '');\n                const itemUrl = url.resolve(resource.url.replace(loader.baseUrl, ''), item);\n\n                // Check if the file wasn't already added as multipacks are redundant\n                if (loader.resources[itemName]\n                    || Object.values(loader.resources).some((r) => url.format(url.parse(r.url)) === itemUrl))\n                {\n                    continue;\n                }\n\n                const options = {\n                    crossOrigin: resource.crossOrigin,\n                    loadType: LoaderResource.LOAD_TYPE.XHR,\n                    xhrType: LoaderResource.XHR_RESPONSE_TYPE.JSON,\n                    parentResource: resource,\n                    metadata: resource.metadata\n                };\n\n                loader.add(itemName, itemUrl, options);\n            }\n        }\n\n        const loadOptions = {\n            crossOrigin: resource.crossOrigin,\n            metadata: resource.metadata.imageMetadata,\n            parentResource: resource,\n        };\n\n        const resourcePath = SpritesheetLoader.getResourcePath(resource, loader.baseUrl);\n\n        // load the image for this sheet\n        loader.add(imageResourceName, resourcePath, loadOptions, function onImageLoad(res: LoaderResource)\n        {\n            if (res.error)\n            {\n                next(res.error);\n\n                return;\n            }\n\n            const spritesheet = new Spritesheet(\n                res.texture,\n                resource.data,\n                resource.url\n            );\n\n            spritesheet.parse().then(() =>\n            {\n                resource.spritesheet = spritesheet;\n                resource.textures = spritesheet.textures;\n                next();\n            });\n        });\n    }\n\n    /**\n     * Get the spritesheets root path\n     * @param resource - Resource to check path\n     * @param baseUrl - Base root url\n     */\n    static getResourcePath(resource: LoaderResource, baseUrl: string): string\n    {\n        // Prepend url path unless the resource image is a data url\n        if (resource.isDataUrl)\n        {\n            return resource.data.meta.image;\n        }\n\n        return url.resolve(resource.url.replace(baseUrl, ''), resource.data.meta.image);\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAyCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,IAAA,WAAA,kBAAA,YAAA;AA8DI;;;;;;AAMG;AACH,IAAA,SAAA,WAAA,CAAY,OAA8B,EAAE,IAAsB,EAAE,kBAAiC,EAAA;AAAjC,QAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAAiC,GAAA,IAAA,CAAA,EAAA;;QA/D9F,IAAY,CAAA,YAAA,GAAkB,EAAE,CAAC;AAiEpC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,YAAY,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC;AAC5D,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,YAAY,WAAW,GAAG,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;AACxF,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAEjB,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAyB,CAAC;QAE5D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACjG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB;AAED;;;;;;AAMG;IACK,WAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UAA0B,kBAAiC,EAAA;AAAjC,QAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAAiC,GAAA,IAAA,CAAA,EAAA;QAE/C,IAAA,KAAK,GAAK,IAAI,CAAC,IAAI,CAAC,IAAI,MAAnB,CAAoB;;QAGjC,IAAI,UAAU,GAAG,kBAAkB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;;QAG9D,IAAI,UAAU,KAAK,IAAI,EACvB;;AAEI,YAAA,UAAU,GAAG,KAAK,KAAK,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5D,SAAA;;QAGD,IAAI,UAAU,KAAK,CAAC,EACpB;AACI,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAC9C,SAAA;AAED,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;;IAmBM,WAAK,CAAA,SAAA,CAAA,KAAA,GAAZ,UAAa,QAA6C,EAAA;QAA1D,IA6BC,KAAA,GAAA,IAAA,CAAA;AA1BG,QAAA,IAAI,QAAQ,EACZ;AACI,YAAA,WAAW,CAAC,OAAO,EAAE,2EAA2E,CAAC,CAAC;AACrG,SAAA;AAGD,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAA;AAEvB,YAAA,KAAI,CAAC,SAAS,GAAG,UAAC,QAAuB,EAAA;AAErC,gBAAA,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAG,QAAQ,CAAC,CAAC;gBACrB,OAAO,CAAC,QAAQ,CAAC,CAAC;AACtB,aAAC,CAAC;AACF,YAAA,KAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YAErB,IAAI,KAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,CAAC,UAAU,EACpD;AACI,gBAAA,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBACvB,KAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,KAAI,CAAC,cAAc,EAAE,CAAC;AACzB,aAAA;AAED,iBAAA;gBACI,KAAI,CAAC,UAAU,EAAE,CAAC;AACrB,aAAA;AACL,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;AAGG;IACK,WAAc,CAAA,SAAA,CAAA,cAAA,GAAtB,UAAuB,iBAAyB,EAAA;QAE5C,IAAI,UAAU,GAAG,iBAAiB,CAAC;AACnC,QAAA,IAAM,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC;AAEzC,QAAA,OAAO,UAAU,GAAG,iBAAiB,GAAG,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EACxF;YACI,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACtC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7B,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAExB,YAAA,IAAI,IAAI,EACR;gBACI,IAAI,KAAK,GAAG,IAAI,CAAC;gBACjB,IAAI,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,UAAU;sBACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;AAEnC,gBAAA,IAAM,IAAI,GAAG,IAAI,SAAS,CACtB,CAAC,EACD,CAAC,EACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EAC1C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAC7C,CAAC;gBAEF,IAAI,IAAI,CAAC,OAAO,EAChB;oBACI,KAAK,GAAG,IAAI,SAAS,CACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CACvC,CAAC;AACL,iBAAA;AAED,qBAAA;oBACI,KAAK,GAAG,IAAI,SAAS,CACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CACvC,CAAC;AACL,iBAAA;;gBAGD,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,gBAAgB,EACnD;AACI,oBAAA,IAAI,GAAG,IAAI,SAAS,CAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CACvC,CAAC;AACL,iBAAA;AAED,gBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAC1B,IAAI,CAAC,WAAW,EAChB,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,EACpB,IAAI,CAAC,MAAM,CACd,CAAC;;AAGF,gBAAA,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,aAAA;AAED,YAAA,UAAU,EAAE,CAAC;AAChB,SAAA;KACJ,CAAA;;AAGO,IAAA,WAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,YAAA;QAEI,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;AAE9C,QAAA,KAAK,IAAM,QAAQ,IAAI,UAAU,EACjC;AACI,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AAC/B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EACpD;gBACI,IAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1C,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;AAC5D,aAAA;AACJ,SAAA;KACJ,CAAA;;AAGO,IAAA,WAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,YAAA;AAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AAEhC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACtC,CAAA;;AAGO,IAAA,WAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,YAAA;QAAA,IAgBC,KAAA,GAAA,IAAA,CAAA;QAdG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,QAAA,UAAU,CAAC,YAAA;AAEP,YAAA,IAAI,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC,UAAU,GAAG,KAAI,CAAC,UAAU,CAAC,MAAM,EACtE;gBACI,KAAI,CAAC,UAAU,EAAE,CAAC;AACrB,aAAA;AAED,iBAAA;gBACI,KAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,KAAI,CAAC,cAAc,EAAE,CAAC;AACzB,aAAA;SACJ,EAAE,CAAC,CAAC,CAAC;KACT,CAAA;AAED;;;AAGG;IACI,WAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,WAAmB,EAAA;;AAAnB,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAmB,GAAA,KAAA,CAAA,EAAA;AAE9B,QAAA,KAAK,IAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAC7B;YACI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,WAAW,EACf;AACI,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;KAC1B,CAAA;;IAjTe,WAAU,CAAA,UAAA,GAAG,IAAI,CAAC;IAkTtC,OAAC,WAAA,CAAA;AAAA,CArTD,EAqTC,EAAA;AAED;;;;;AAKG;AAEH;;;;;AAKG;;ACnYH;;;;;;;;;;;;;;;;;;;AAmBG;AACH,IAAA,iBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,iBAAA,GAAA;KAiHC;AA5GG;;;;;AAKG;AACI,IAAA,iBAAA,CAAA,GAAG,GAAV,UAAW,QAAwB,EAAE,IAAkC,EAAA;;;QAGnE,IAAM,MAAM,GAAI,IAAsB,CAAC;AACvC,QAAA,IAAM,iBAAiB,GAAM,QAAQ,CAAC,IAAI,WAAQ,CAAC;;QAGnD,IAAI,CAAC,QAAQ,CAAC,IAAI;AACX,eAAA,QAAQ,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,CAAC,IAAI;AAC1C,eAAA,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM;AACrB,eAAA,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAE1C;AACI,YAAA,IAAI,EAAE,CAAC;YAEP,OAAO;AACV,SAAA;;;;QAKD,IAAM,UAAU,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAmB,CAAC;AAE5D,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAC7B;oCACe,IAAI,EAAA;AAEX,gBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;;AAEC,iBAAA;gBAED,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC3C,IAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;;AAG5E,gBAAA,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AACvB,uBAAA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAA,EAAK,OAAA,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,OAAO,CAAxC,EAAwC,CAAC,EAC5F;;AAEC,iBAAA;AAED,gBAAA,IAAM,OAAO,GAAG;oBACZ,WAAW,EAAE,QAAQ,CAAC,WAAW;AACjC,oBAAA,QAAQ,EAAE,cAAc,CAAC,SAAS,CAAC,GAAG;AACtC,oBAAA,OAAO,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC9C,oBAAA,cAAc,EAAE,QAAQ;oBACxB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC9B,CAAC;gBAEF,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;;AAzB3C,YAAA,KAAmB,UAAU,EAAV,YAAA,GAAA,UAAU,EAAV,EAAA,GAAA,YAAA,CAAA,MAAU,EAAV,EAAU,EAAA,EAAA;AAAxB,gBAAA,IAAM,IAAI,GAAA,YAAA,CAAA,EAAA,CAAA,CAAA;wBAAJ,IAAI,CAAA,CAAA;AA0Bd,aAAA;AACJ,SAAA;AAED,QAAA,IAAM,WAAW,GAAG;YAChB,WAAW,EAAE,QAAQ,CAAC,WAAW;AACjC,YAAA,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,aAAa;AACzC,YAAA,cAAc,EAAE,QAAQ;SAC3B,CAAC;AAEF,QAAA,IAAM,YAAY,GAAG,iBAAiB,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;;AAGjF,QAAA,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,WAAW,CAAC,GAAmB,EAAA;YAE7F,IAAI,GAAG,CAAC,KAAK,EACb;AACI,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAEhB,OAAO;AACV,aAAA;AAED,YAAA,IAAM,WAAW,GAAG,IAAI,WAAW,CAC/B,GAAG,CAAC,OAAO,EACX,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,GAAG,CACf,CAAC;AAEF,YAAA,WAAW,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,YAAA;AAErB,gBAAA,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,gBAAA,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;AACzC,gBAAA,IAAI,EAAE,CAAC;AACX,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;AAIG;AACI,IAAA,iBAAA,CAAA,eAAe,GAAtB,UAAuB,QAAwB,EAAE,OAAe,EAAA;;QAG5D,IAAI,QAAQ,CAAC,SAAS,EACtB;AACI,YAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AACnC,SAAA;QAED,OAAO,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnF,CAAA;;AA7GM,IAAA,iBAAA,CAAA,SAAS,GAAsB,aAAa,CAAC,MAAM,CAAC;IA8G/D,OAAC,iBAAA,CAAA;AAAA,CAjHD,EAiHC;;;;"}