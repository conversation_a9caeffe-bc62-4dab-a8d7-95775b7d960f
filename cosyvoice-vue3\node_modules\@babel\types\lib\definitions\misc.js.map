{"version": 3, "names": ["_utils", "require", "_placeholders", "_core", "defineType", "defineAliasedType", "visitor", "builder", "fields", "Object", "assign", "name", "validate", "assertNodeType", "expectedNode", "assertOneOf", "PLACEHOLDERS", "patternLikeCommon", "assertValueType"], "sources": ["../../src/definitions/misc.ts"], "sourcesContent": ["import {\n  defineAliasedType,\n  assertNodeType,\n  assertOneOf,\n  assertValueType,\n} from \"./utils.ts\";\nimport { PLACEHOLDERS } from \"./placeholders.ts\";\nimport { patternLikeCommon } from \"./core.ts\";\n\nconst defineType = defineAliasedType(\"Miscellaneous\");\n\nif (!process.env.BABEL_8_BREAKING) {\n  defineType(\"Noop\", {\n    visitor: [],\n  });\n}\n\ndefineType(\"Placeholder\", {\n  visitor: [],\n  builder: [\"expectedNode\", \"name\"],\n  // aliases: [], defined in placeholders.js\n  fields: {\n    name: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    expectedNode: {\n      validate: assertOneOf(...PLACEHOLDERS),\n    },\n    ...patternLikeCommon(),\n  },\n});\n\ndefineType(\"V8IntrinsicIdentifier\", {\n  builder: [\"name\"],\n  fields: {\n    name: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n"], "mappings": ";;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAEA,MAAMG,UAAU,GAAG,IAAAC,wBAAiB,EAAC,eAAe,CAAC;AAElB;EACjCD,UAAU,CAAC,MAAM,EAAE;IACjBE,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEAF,UAAU,CAAC,aAAa,EAAE;EACxBE,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,CAAC,cAAc,EAAE,MAAM,CAAC;EAEjCC,MAAM,EAAAC,MAAA,CAAAC,MAAA;IACJC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDC,YAAY,EAAE;MACZF,QAAQ,EAAE,IAAAG,kBAAW,EAAC,GAAGC,0BAAY;IACvC;EAAC,GACE,IAAAC,uBAAiB,EAAC,CAAC;AAE1B,CAAC,CAAC;AAEFb,UAAU,CAAC,uBAAuB,EAAE;EAClCG,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBC,MAAM,EAAE;IACNG,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAM,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC", "ignoreList": []}