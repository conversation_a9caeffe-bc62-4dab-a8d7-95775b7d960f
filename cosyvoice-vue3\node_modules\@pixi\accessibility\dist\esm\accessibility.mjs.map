{"version": 3, "file": "accessibility.mjs", "sources": ["../../src/accessibleTarget.ts", "../../src/AccessibilityManager.ts"], "sourcesContent": ["import type { DisplayObject } from '@pixi/display';\n\nexport type PointerEvents = 'auto'\n| 'none'\n| 'visiblePainted'\n| 'visibleFill'\n| 'visibleStroke'\n| 'visible'\n| 'painted'\n| 'fill'\n| 'stroke'\n| 'all'\n| 'inherit';\n\nexport interface IAccessibleTarget\n{\n    accessible: boolean;\n    accessibleTitle: string;\n    accessibleHint: string;\n    tabIndex: number;\n    _accessibleActive: boolean;\n    _accessibleDiv: IAccessibleHTMLElement;\n    accessibleType: string;\n    accessiblePointerEvents: PointerEvents;\n    accessibleChildren: boolean;\n    renderId: number;\n}\n\nexport interface IAccessibleHTMLElement extends HTMLElement\n{\n    type?: string;\n    displayObject?: DisplayObject;\n}\n\n/**\n * Default property values of accessible objects\n * used by {@link PIXI.AccessibilityManager}.\n * @private\n * @function accessibleTarget\n * @memberof PIXI\n * @type {object}\n * @example\n *      function MyObject() {}\n *\n *      Object.assign(\n *          MyObject.prototype,\n *          PIXI.accessibleTarget\n *      );\n */\nexport const accessibleTarget: IAccessibleTarget = {\n    /**\n     *  Flag for if the object is accessible. If true AccessibilityManager will overlay a\n     *   shadow div with attributes set\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessible: false,\n\n    /**\n     * Sets the title attribute of the shadow div\n     * If accessibleTitle AND accessibleHint has not been this will default to 'displayObject [tabIndex]'\n     * @member {?string}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessibleTitle: null,\n\n    /**\n     * Sets the aria-label attribute of the shadow div\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessibleHint: null,\n\n    /**\n     * @member {number}\n     * @memberof PIXI.DisplayObject#\n     * @private\n     * @todo Needs docs.\n     */\n    tabIndex: 0,\n\n    /**\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @todo Needs docs.\n     */\n    _accessibleActive: false,\n\n    /**\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @todo Needs docs.\n     */\n    _accessibleDiv: null,\n\n    /**\n     * Specify the type of div the accessible layer is. Screen readers treat the element differently\n     * depending on this type. Defaults to button.\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     * @default 'button'\n     */\n    accessibleType: 'button',\n\n    /**\n     * Specify the pointer-events the accessible div will use\n     * Defaults to auto.\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     * @default 'auto'\n     */\n    accessiblePointerEvents: 'auto',\n\n    /**\n     * Setting to false will prevent any children inside this container to\n     * be accessible. Defaults to true.\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @default true\n     */\n    accessibleChildren: true,\n\n    renderId: -1,\n};\n", "import { DisplayObject } from '@pixi/display';\nimport { isMobile, removeItems } from '@pixi/utils';\nimport { accessibleTarget } from './accessibleTarget';\n\nimport type { Rectangle } from '@pixi/math';\nimport type { Container } from '@pixi/display';\nimport type { Ren<PERSON><PERSON>, AbstractRenderer, ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport type { IAccessibleHTMLElement } from './accessibleTarget';\n\n// add some extra variables to the container..\nDisplayObject.mixin(accessibleTarget);\n\nconst KEY_CODE_TAB = 9;\n\nconst DIV_TOUCH_SIZE = 100;\nconst DIV_TOUCH_POS_X = 0;\nconst DIV_TOUCH_POS_Y = 0;\nconst DIV_TOUCH_ZINDEX = 2;\n\nconst DIV_HOOK_SIZE = 1;\nconst DIV_HOOK_POS_X = -1000;\nconst DIV_HOOK_POS_Y = -1000;\nconst DIV_HOOK_ZINDEX = 2;\n\n/**\n * The Accessibility manager recreates the ability to tab and have content read by screen readers.\n * This is very important as it can possibly help people with disabilities access PixiJS content.\n *\n * A DisplayObject can be made accessible just like it can be made interactive. This manager will map the\n * events as if the mouse was being used, minimizing the effort required to implement.\n *\n * An instance of this class is automatically created by default, and can be found at `renderer.plugins.accessibility`\n * @class\n * @memberof PIXI\n */\nexport class AccessibilityManager\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'accessibility',\n        type: [\n            ExtensionType.RendererPlugin,\n            ExtensionType.CanvasRendererPlugin,\n        ],\n    };\n\n    /** Setting this to true will visually show the divs. */\n    public debug = false;\n\n    /**\n     * The renderer this accessibility manager works for.\n     * @type {PIXI.CanvasRenderer|PIXI.Renderer}\n     */\n    public renderer: AbstractRenderer | Renderer;\n\n    /** Internal variable, see isActive getter. */\n    private _isActive = false;\n\n    /** Internal variable, see isMobileAccessibility getter. */\n    private _isMobileAccessibility = false;\n\n    /** Button element for handling touch hooks. */\n    private _hookDiv: HTMLElement;\n\n    /** This is the dom element that will sit over the PixiJS element. This is where the div overlays will go. */\n    private div: HTMLElement;\n\n    /** A simple pool for storing divs. */\n    private pool: IAccessibleHTMLElement[] = [];\n\n    /** This is a tick used to check if an object is no longer being rendered. */\n    private renderId = 0;\n\n    /** The array of currently active accessible items. */\n    private children: DisplayObject[] = [];\n\n    /** Count to throttle div updates on android devices. */\n    private androidUpdateCount = 0;\n\n    /**  The frequency to update the div elements. */\n    private androidUpdateFrequency = 500; // 2fps\n\n    /**\n     * @param {PIXI.CanvasRenderer|PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: AbstractRenderer | Renderer)\n    {\n        this._hookDiv = null;\n\n        if (isMobile.tablet || isMobile.phone)\n        {\n            this.createTouchHook();\n        }\n\n        // first we create a div that will sit over the PixiJS element. This is where the div overlays will go.\n        const div = document.createElement('div');\n\n        div.style.width = `${DIV_TOUCH_SIZE}px`;\n        div.style.height = `${DIV_TOUCH_SIZE}px`;\n        div.style.position = 'absolute';\n        div.style.top = `${DIV_TOUCH_POS_X}px`;\n        div.style.left = `${DIV_TOUCH_POS_Y}px`;\n        div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n\n        this.div = div;\n        this.renderer = renderer;\n\n        /**\n         * pre-bind the functions\n         * @type {Function}\n         * @private\n         */\n        this._onKeyDown = this._onKeyDown.bind(this);\n\n        /**\n         * pre-bind the functions\n         * @type {Function}\n         * @private\n         */\n        this._onMouseMove = this._onMouseMove.bind(this);\n\n        // let listen for tab.. once pressed we can fire up and show the accessibility layer\n        globalThis.addEventListener('keydown', this._onKeyDown, false);\n    }\n\n    /**\n     * Value of `true` if accessibility is currently active and accessibility layers are showing.\n     * @member {boolean}\n     * @readonly\n     */\n    get isActive(): boolean\n    {\n        return this._isActive;\n    }\n\n    /**\n     * Value of `true` if accessibility is enabled for touch devices.\n     * @member {boolean}\n     * @readonly\n     */\n    get isMobileAccessibility(): boolean\n    {\n        return this._isMobileAccessibility;\n    }\n\n    /**\n     * Creates the touch hooks.\n     * @private\n     */\n    private createTouchHook(): void\n    {\n        const hookDiv = document.createElement('button');\n\n        hookDiv.style.width = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.height = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.position = 'absolute';\n        hookDiv.style.top = `${DIV_HOOK_POS_X}px`;\n        hookDiv.style.left = `${DIV_HOOK_POS_Y}px`;\n        hookDiv.style.zIndex = DIV_HOOK_ZINDEX.toString();\n        hookDiv.style.backgroundColor = '#FF0000';\n        hookDiv.title = 'select to enable accessibility for this content';\n\n        hookDiv.addEventListener('focus', () =>\n        {\n            this._isMobileAccessibility = true;\n            this.activate();\n            this.destroyTouchHook();\n        });\n\n        document.body.appendChild(hookDiv);\n        this._hookDiv = hookDiv;\n    }\n\n    /**\n     * Destroys the touch hooks.\n     * @private\n     */\n    private destroyTouchHook(): void\n    {\n        if (!this._hookDiv)\n        {\n            return;\n        }\n        document.body.removeChild(this._hookDiv);\n        this._hookDiv = null;\n    }\n\n    /**\n     * Activating will cause the Accessibility layer to be shown.\n     * This is called when a user presses the tab key.\n     * @private\n     */\n    private activate(): void\n    {\n        if (this._isActive)\n        {\n            return;\n        }\n\n        this._isActive = true;\n\n        globalThis.document.addEventListener('mousemove', this._onMouseMove, true);\n        globalThis.removeEventListener('keydown', this._onKeyDown, false);\n\n        this.renderer.on('postrender', this.update, this);\n        this.renderer.view.parentNode?.appendChild(this.div);\n    }\n\n    /**\n     * Deactivating will cause the Accessibility layer to be hidden.\n     * This is called when a user moves the mouse.\n     * @private\n     */\n    private deactivate(): void\n    {\n        if (!this._isActive || this._isMobileAccessibility)\n        {\n            return;\n        }\n\n        this._isActive = false;\n\n        globalThis.document.removeEventListener('mousemove', this._onMouseMove, true);\n        globalThis.addEventListener('keydown', this._onKeyDown, false);\n\n        this.renderer.off('postrender', this.update);\n        this.div.parentNode?.removeChild(this.div);\n    }\n\n    /**\n     * This recursive function will run through the scene graph and add any new accessible objects to the DOM layer.\n     * @private\n     * @param {PIXI.Container} displayObject - The DisplayObject to check.\n     */\n    private updateAccessibleObjects(displayObject: Container): void\n    {\n        if (!displayObject.visible || !displayObject.accessibleChildren)\n        {\n            return;\n        }\n\n        if (displayObject.accessible && displayObject.interactive)\n        {\n            if (!displayObject._accessibleActive)\n            {\n                this.addChild(displayObject);\n            }\n\n            displayObject.renderId = this.renderId;\n        }\n\n        const children = displayObject.children;\n\n        if (children)\n        {\n            for (let i = 0; i < children.length; i++)\n            {\n                this.updateAccessibleObjects(children[i] as Container);\n            }\n        }\n    }\n\n    /**\n     * Before each render this function will ensure that all divs are mapped correctly to their DisplayObjects.\n     * @private\n     */\n    private update(): void\n    {\n        /* On Android default web browser, tab order seems to be calculated by position rather than tabIndex,\n        *  moving buttons can cause focus to flicker between two buttons making it hard/impossible to navigate,\n        *  so I am just running update every half a second, seems to fix it.\n        */\n        const now = performance.now();\n\n        if (isMobile.android.device && now < this.androidUpdateCount)\n        {\n            return;\n        }\n\n        this.androidUpdateCount = now + this.androidUpdateFrequency;\n\n        if (!(this.renderer as Renderer).renderingToScreen)\n        {\n            return;\n        }\n\n        // update children...\n        if (this.renderer._lastObjectRendered)\n        {\n            this.updateAccessibleObjects(this.renderer._lastObjectRendered as Container);\n        }\n\n        const { left, top, width, height } = this.renderer.view.getBoundingClientRect();\n        const { width: viewWidth, height: viewHeight, resolution } = this.renderer;\n\n        const sx = (width / viewWidth) * resolution;\n        const sy = (height / viewHeight) * resolution;\n\n        let div = this.div;\n\n        div.style.left = `${left}px`;\n        div.style.top = `${top}px`;\n        div.style.width = `${viewWidth}px`;\n        div.style.height = `${viewHeight}px`;\n\n        for (let i = 0; i < this.children.length; i++)\n        {\n            const child = this.children[i];\n\n            if (child.renderId !== this.renderId)\n            {\n                child._accessibleActive = false;\n\n                removeItems(this.children, i, 1);\n                this.div.removeChild(child._accessibleDiv);\n                this.pool.push(child._accessibleDiv);\n                child._accessibleDiv = null;\n\n                i--;\n            }\n            else\n            {\n                // map div to display..\n                div = child._accessibleDiv;\n                let hitArea = child.hitArea as Rectangle;\n                const wt = child.worldTransform;\n\n                if (child.hitArea)\n                {\n                    div.style.left = `${(wt.tx + (hitArea.x * wt.a)) * sx}px`;\n                    div.style.top = `${(wt.ty + (hitArea.y * wt.d)) * sy}px`;\n\n                    div.style.width = `${hitArea.width * wt.a * sx}px`;\n                    div.style.height = `${hitArea.height * wt.d * sy}px`;\n                }\n                else\n                {\n                    hitArea = child.getBounds();\n\n                    this.capHitArea(hitArea);\n\n                    div.style.left = `${hitArea.x * sx}px`;\n                    div.style.top = `${hitArea.y * sy}px`;\n\n                    div.style.width = `${hitArea.width * sx}px`;\n                    div.style.height = `${hitArea.height * sy}px`;\n\n                    // update button titles and hints if they exist and they've changed\n                    if (div.title !== child.accessibleTitle && child.accessibleTitle !== null)\n                    {\n                        div.title = child.accessibleTitle;\n                    }\n                    if (div.getAttribute('aria-label') !== child.accessibleHint\n                        && child.accessibleHint !== null)\n                    {\n                        div.setAttribute('aria-label', child.accessibleHint);\n                    }\n                }\n\n                // the title or index may have changed, if so lets update it!\n                if (child.accessibleTitle !== div.title || child.tabIndex !== div.tabIndex)\n                {\n                    div.title = child.accessibleTitle;\n                    div.tabIndex = child.tabIndex;\n                    if (this.debug) this.updateDebugHTML(div);\n                }\n            }\n        }\n\n        // increment the render id..\n        this.renderId++;\n    }\n\n    /**\n     * private function that will visually add the information to the\n     * accessability div\n     * @param {HTMLElement} div -\n     */\n    public updateDebugHTML(div: IAccessibleHTMLElement): void\n    {\n        div.innerHTML = `type: ${div.type}</br> title : ${div.title}</br> tabIndex: ${div.tabIndex}`;\n    }\n\n    /**\n     * Adjust the hit area based on the bounds of a display object\n     * @param {PIXI.Rectangle} hitArea - Bounds of the child\n     */\n    public capHitArea(hitArea: Rectangle): void\n    {\n        if (hitArea.x < 0)\n        {\n            hitArea.width += hitArea.x;\n            hitArea.x = 0;\n        }\n\n        if (hitArea.y < 0)\n        {\n            hitArea.height += hitArea.y;\n            hitArea.y = 0;\n        }\n\n        const { width: viewWidth, height: viewHeight } = this.renderer;\n\n        if (hitArea.x + hitArea.width > viewWidth)\n        {\n            hitArea.width = viewWidth - hitArea.x;\n        }\n\n        if (hitArea.y + hitArea.height > viewHeight)\n        {\n            hitArea.height = viewHeight - hitArea.y;\n        }\n    }\n\n    /**\n     * Adds a DisplayObject to the accessibility manager\n     * @private\n     * @param {PIXI.DisplayObject} displayObject - The child to make accessible.\n     */\n    private addChild<T extends DisplayObject>(displayObject: T): void\n    {\n        //    this.activate();\n\n        let div = this.pool.pop();\n\n        if (!div)\n        {\n            div = document.createElement('button');\n\n            div.style.width = `${DIV_TOUCH_SIZE}px`;\n            div.style.height = `${DIV_TOUCH_SIZE}px`;\n            div.style.backgroundColor = this.debug ? 'rgba(255,255,255,0.5)' : 'transparent';\n            div.style.position = 'absolute';\n            div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n            div.style.borderStyle = 'none';\n\n            // ARIA attributes ensure that button title and hint updates are announced properly\n            if (navigator.userAgent.toLowerCase().indexOf('chrome') > -1)\n            {\n                // Chrome doesn't need aria-live to work as intended; in fact it just gets more confused.\n                div.setAttribute('aria-live', 'off');\n            }\n            else\n            {\n                div.setAttribute('aria-live', 'polite');\n            }\n\n            if (navigator.userAgent.match(/rv:.*Gecko\\//))\n            {\n                // FireFox needs this to announce only the new button name\n                div.setAttribute('aria-relevant', 'additions');\n            }\n            else\n            {\n                // required by IE, other browsers don't much care\n                div.setAttribute('aria-relevant', 'text');\n            }\n\n            div.addEventListener('click', this._onClick.bind(this));\n            div.addEventListener('focus', this._onFocus.bind(this));\n            div.addEventListener('focusout', this._onFocusOut.bind(this));\n        }\n\n        // set pointer events\n        div.style.pointerEvents = displayObject.accessiblePointerEvents;\n        // set the type, this defaults to button!\n        div.type = displayObject.accessibleType;\n\n        if (displayObject.accessibleTitle && displayObject.accessibleTitle !== null)\n        {\n            div.title = displayObject.accessibleTitle;\n        }\n        else if (!displayObject.accessibleHint\n                 || displayObject.accessibleHint === null)\n        {\n            div.title = `displayObject ${displayObject.tabIndex}`;\n        }\n\n        if (displayObject.accessibleHint\n            && displayObject.accessibleHint !== null)\n        {\n            div.setAttribute('aria-label', displayObject.accessibleHint);\n        }\n\n        if (this.debug) this.updateDebugHTML(div);\n\n        displayObject._accessibleActive = true;\n        displayObject._accessibleDiv = div;\n        div.displayObject = displayObject;\n\n        this.children.push(displayObject);\n        this.div.appendChild(displayObject._accessibleDiv);\n        displayObject._accessibleDiv.tabIndex = displayObject.tabIndex;\n    }\n\n    /**\n     * Maps the div button press to pixi's InteractionManager (click)\n     * @private\n     * @param {MouseEvent} e - The click event.\n     */\n    private _onClick(e: MouseEvent): void\n    {\n        const interactionManager = this.renderer.plugins.interaction;\n        const { displayObject } = e.target as IAccessibleHTMLElement;\n        const { eventData } = interactionManager;\n\n        interactionManager.dispatchEvent(displayObject, 'click', eventData);\n        interactionManager.dispatchEvent(displayObject, 'pointertap', eventData);\n        interactionManager.dispatchEvent(displayObject, 'tap', eventData);\n    }\n\n    /**\n     * Maps the div focus events to pixi's InteractionManager (mouseover)\n     * @private\n     * @param {FocusEvent} e - The focus event.\n     */\n    private _onFocus(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'assertive');\n        }\n\n        const interactionManager = this.renderer.plugins.interaction;\n        const { displayObject } = e.target as IAccessibleHTMLElement;\n        const { eventData } = interactionManager;\n\n        interactionManager.dispatchEvent(displayObject, 'mouseover', eventData);\n    }\n\n    /**\n     * Maps the div focus events to pixi's InteractionManager (mouseout)\n     * @private\n     * @param {FocusEvent} e - The focusout event.\n     */\n    private _onFocusOut(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'polite');\n        }\n\n        const interactionManager = this.renderer.plugins.interaction;\n        const { displayObject } = e.target as IAccessibleHTMLElement;\n        const { eventData } = interactionManager;\n\n        interactionManager.dispatchEvent(displayObject, 'mouseout', eventData);\n    }\n\n    /**\n     * Is called when a key is pressed\n     * @private\n     * @param {KeyboardEvent} e - The keydown event.\n     */\n    private _onKeyDown(e: KeyboardEvent): void\n    {\n        if (e.keyCode !== KEY_CODE_TAB)\n        {\n            return;\n        }\n\n        this.activate();\n    }\n\n    /**\n     * Is called when the mouse moves across the renderer element\n     * @private\n     * @param {MouseEvent} e - The mouse event.\n     */\n    private _onMouseMove(e: MouseEvent): void\n    {\n        if (e.movementX === 0 && e.movementY === 0)\n        {\n            return;\n        }\n\n        this.deactivate();\n    }\n\n    /** Destroys the accessibility manager */\n    public destroy(): void\n    {\n        this.destroyTouchHook();\n        this.div = null;\n\n        globalThis.document.removeEventListener('mousemove', this._onMouseMove, true);\n        globalThis.removeEventListener('keydown', this._onKeyDown);\n\n        this.pool = null;\n        this.children = null;\n        this.renderer = null;\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAkCA;;;;;;;;;;;;;;AAcG;AACI,IAAM,gBAAgB,GAAsB;AAC/C;;;;;AAKG;AACH,IAAA,UAAU,EAAE,KAAK;AAEjB;;;;;AAKG;AACH,IAAA,eAAe,EAAE,IAAI;AAErB;;;;AAIG;AACH,IAAA,cAAc,EAAE,IAAI;AAEpB;;;;;AAKG;AACH,IAAA,QAAQ,EAAE,CAAC;AAEX;;;;AAIG;AACH,IAAA,iBAAiB,EAAE,KAAK;AAExB;;;;AAIG;AACH,IAAA,cAAc,EAAE,IAAI;AAEpB;;;;;;AAMG;AACH,IAAA,cAAc,EAAE,QAAQ;AAExB;;;;;;AAMG;AACH,IAAA,uBAAuB,EAAE,MAAM;AAE/B;;;;;;AAMG;AACH,IAAA,kBAAkB,EAAE,IAAI;IAExB,QAAQ,EAAE,CAAC,CAAC;;;AChHhB;AACA,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAEtC,IAAM,YAAY,GAAG,CAAC,CAAC;AAEvB,IAAM,cAAc,GAAG,GAAG,CAAC;AAC3B,IAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,IAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,IAAM,gBAAgB,GAAG,CAAC,CAAC;AAE3B,IAAM,aAAa,GAAG,CAAC,CAAC;AACxB,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC;AAC7B,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC;AAC7B,IAAM,eAAe,GAAG,CAAC,CAAC;AAE1B;;;;;;;;;;AAUG;AACH,IAAA,oBAAA,kBAAA,YAAA;AA+CI;;AAEG;AACH,IAAA,SAAA,oBAAA,CAAY,QAAqC,EAAA;;QAtC1C,IAAK,CAAA,KAAA,GAAG,KAAK,CAAC;;QASb,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;QAGlB,IAAsB,CAAA,sBAAA,GAAG,KAAK,CAAC;;QAS/B,IAAI,CAAA,IAAA,GAA6B,EAAE,CAAC;;QAGpC,IAAQ,CAAA,QAAA,GAAG,CAAC,CAAC;;QAGb,IAAQ,CAAA,QAAA,GAAoB,EAAE,CAAC;;QAG/B,IAAkB,CAAA,kBAAA,GAAG,CAAC,CAAC;;AAGvB,QAAA,IAAA,CAAA,sBAAsB,GAAG,GAAG,CAAC;AAOjC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAErB,QAAA,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,KAAK,EACrC;YACI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;;QAGD,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAE1C,QAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,cAAc,OAAI,CAAC;AACxC,QAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,cAAc,OAAI,CAAC;AACzC,QAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AAChC,QAAA,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,eAAe,OAAI,CAAC;AACvC,QAAA,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,eAAe,OAAI,CAAC;QACxC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AAE/C,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACf,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAEzB;;;;AAIG;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE7C;;;;AAIG;QACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;QAGjD,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KAClE;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,oBAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AALZ;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;;;AAAA,KAAA,CAAA,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,oBAAqB,CAAA,SAAA,EAAA,uBAAA,EAAA;AALzB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,sBAAsB,CAAC;SACtC;;;AAAA,KAAA,CAAA,CAAA;AAED;;;AAGG;AACK,IAAA,oBAAA,CAAA,SAAA,CAAA,eAAe,GAAvB,YAAA;QAAA,IAsBC,KAAA,GAAA,IAAA,CAAA;QApBG,IAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAEjD,QAAA,OAAO,CAAC,KAAK,CAAC,KAAK,GAAM,aAAa,OAAI,CAAC;AAC3C,QAAA,OAAO,CAAC,KAAK,CAAC,MAAM,GAAM,aAAa,OAAI,CAAC;AAC5C,QAAA,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACpC,QAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAM,cAAc,OAAI,CAAC;AAC1C,QAAA,OAAO,CAAC,KAAK,CAAC,IAAI,GAAM,cAAc,OAAI,CAAC;QAC3C,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC;AAClD,QAAA,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;AAC1C,QAAA,OAAO,CAAC,KAAK,GAAG,iDAAiD,CAAC;AAElE,QAAA,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAA;AAE9B,YAAA,KAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,KAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,KAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5B,SAAC,CAAC,CAAC;AAEH,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;KAC3B,CAAA;AAED;;;AAGG;AACK,IAAA,oBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAxB,YAAA;AAEI,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAClB;YACI,OAAO;AACV,SAAA;QACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACxB,CAAA;AAED;;;;AAIG;AACK,IAAA,oBAAA,CAAA,SAAA,CAAA,QAAQ,GAAhB,YAAA;;QAEI,IAAI,IAAI,CAAC,SAAS,EAClB;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAEtB,QAAA,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC3E,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAElE,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAClD,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACxD,CAAA;AAED;;;;AAIG;AACK,IAAA,oBAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,YAAA;;QAEI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,sBAAsB,EAClD;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAEvB,QAAA,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC9E,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAE/D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7C,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC9C,CAAA;AAED;;;;AAIG;IACK,oBAAuB,CAAA,SAAA,CAAA,uBAAA,GAA/B,UAAgC,aAAwB,EAAA;QAEpD,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAC/D;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,WAAW,EACzD;AACI,YAAA,IAAI,CAAC,aAAa,CAAC,iBAAiB,EACpC;AACI,gBAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AAChC,aAAA;AAED,YAAA,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1C,SAAA;AAED,QAAA,IAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AAExC,QAAA,IAAI,QAAQ,EACZ;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EACxC;gBACI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAc,CAAC,CAAC;AAC1D,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;AAGG;AACK,IAAA,oBAAA,CAAA,SAAA,CAAA,MAAM,GAAd,YAAA;AAEI;;;AAGE;AACF,QAAA,IAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAE9B,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAC5D;YACI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,kBAAkB,GAAG,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAE5D,QAAA,IAAI,CAAE,IAAI,CAAC,QAAqB,CAAC,iBAAiB,EAClD;YACI,OAAO;AACV,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EACrC;YACI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAgC,CAAC,CAAC;AAChF,SAAA;QAEK,IAAA,EAAA,GAA+B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAvE,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,GAAG,GAAA,EAAA,CAAA,GAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAA+C,CAAC;AAC1E,QAAA,IAAA,EAAuD,GAAA,IAAI,CAAC,QAAQ,EAA3D,SAAS,GAAA,EAAA,CAAA,KAAA,EAAU,UAAU,GAAA,EAAA,CAAA,MAAA,EAAE,UAAU,gBAAkB,CAAC;QAE3E,IAAM,EAAE,GAAG,CAAC,KAAK,GAAG,SAAS,IAAI,UAAU,CAAC;QAC5C,IAAM,EAAE,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,UAAU,CAAC;AAE9C,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAEnB,QAAA,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,IAAI,OAAI,CAAC;AAC7B,QAAA,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,GAAG,OAAI,CAAC;AAC3B,QAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,SAAS,OAAI,CAAC;AACnC,QAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,UAAU,OAAI,CAAC;AAErC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAC7C;YACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAE/B,YAAA,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EACpC;AACI,gBAAA,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBAEhC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrC,gBAAA,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;AAE5B,gBAAA,CAAC,EAAE,CAAC;AACP,aAAA;AAED,iBAAA;;AAEI,gBAAA,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC;AAC3B,gBAAA,IAAI,OAAO,GAAG,KAAK,CAAC,OAAoB,CAAC;AACzC,gBAAA,IAAM,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC;gBAEhC,IAAI,KAAK,CAAC,OAAO,EACjB;oBACI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,GAAA,IAAI,CAAC;oBAC1D,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,GAAA,IAAI,CAAC;AAEzD,oBAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,OAAI,CAAC;AACnD,oBAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,OAAI,CAAC;AACxD,iBAAA;AAED,qBAAA;AACI,oBAAA,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;AAE5B,oBAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAEzB,GAAG,CAAC,KAAK,CAAC,IAAI,GAAM,OAAO,CAAC,CAAC,GAAG,EAAE,GAAA,IAAI,CAAC;oBACvC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAM,OAAO,CAAC,CAAC,GAAG,EAAE,GAAA,IAAI,CAAC;oBAEtC,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,OAAO,CAAC,KAAK,GAAG,EAAE,GAAA,IAAI,CAAC;oBAC5C,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,OAAO,CAAC,MAAM,GAAG,EAAE,GAAA,IAAI,CAAC;;AAG9C,oBAAA,IAAI,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,eAAe,KAAK,IAAI,EACzE;AACI,wBAAA,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;AACrC,qBAAA;oBACD,IAAI,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,cAAc;AACpD,2BAAA,KAAK,CAAC,cAAc,KAAK,IAAI,EACpC;wBACI,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;AACxD,qBAAA;AACJ,iBAAA;;AAGD,gBAAA,IAAI,KAAK,CAAC,eAAe,KAAK,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,EAC1E;AACI,oBAAA,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;AAClC,oBAAA,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;oBAC9B,IAAI,IAAI,CAAC,KAAK;AAAE,wBAAA,EAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAA;AAC7C,iBAAA;AACJ,aAAA;AACJ,SAAA;;QAGD,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB,CAAA;AAED;;;;AAIG;IACI,oBAAe,CAAA,SAAA,CAAA,eAAA,GAAtB,UAAuB,GAA2B,EAAA;AAE9C,QAAA,GAAG,CAAC,SAAS,GAAG,QAAA,GAAS,GAAG,CAAC,IAAI,GAAiB,gBAAA,GAAA,GAAG,CAAC,KAAK,GAAA,kBAAA,GAAmB,GAAG,CAAC,QAAU,CAAC;KAChG,CAAA;AAED;;;AAGG;IACI,oBAAU,CAAA,SAAA,CAAA,UAAA,GAAjB,UAAkB,OAAkB,EAAA;AAEhC,QAAA,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EACjB;AACI,YAAA,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC;AAC3B,YAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACjB,SAAA;AAED,QAAA,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EACjB;AACI,YAAA,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;AAC5B,YAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACjB,SAAA;QAEK,IAAA,EAAA,GAA2C,IAAI,CAAC,QAAQ,EAA/C,SAAS,GAAA,EAAA,CAAA,KAAA,EAAU,UAAU,GAAA,EAAA,CAAA,MAAkB,CAAC;QAE/D,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,SAAS,EACzC;YACI,OAAO,CAAC,KAAK,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC;AACzC,SAAA;QAED,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,UAAU,EAC3C;YACI,OAAO,CAAC,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC;AAC3C,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACK,oBAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAA0C,aAAgB,EAAA;;QAItD,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAE1B,IAAI,CAAC,GAAG,EACR;AACI,YAAA,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAEvC,YAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAM,cAAc,OAAI,CAAC;AACxC,YAAA,GAAG,CAAC,KAAK,CAAC,MAAM,GAAM,cAAc,OAAI,CAAC;AACzC,YAAA,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,GAAG,uBAAuB,GAAG,aAAa,CAAC;AACjF,YAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;YAChC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AAC/C,YAAA,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;;AAG/B,YAAA,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAC5D;;AAEI,gBAAA,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AACxC,aAAA;AAED,iBAAA;AACI,gBAAA,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC3C,aAAA;YAED,IAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,EAC7C;;AAEI,gBAAA,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;AAClD,aAAA;AAED,iBAAA;;AAEI,gBAAA,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AAC7C,aAAA;AAED,YAAA,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxD,YAAA,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxD,YAAA,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACjE,SAAA;;QAGD,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC,uBAAuB,CAAC;;AAEhE,QAAA,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC,cAAc,CAAC;QAExC,IAAI,aAAa,CAAC,eAAe,IAAI,aAAa,CAAC,eAAe,KAAK,IAAI,EAC3E;AACI,YAAA,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC,eAAe,CAAC;AAC7C,SAAA;aACI,IAAI,CAAC,aAAa,CAAC,cAAc;AAC1B,eAAA,aAAa,CAAC,cAAc,KAAK,IAAI,EACjD;AACI,YAAA,GAAG,CAAC,KAAK,GAAG,mBAAiB,aAAa,CAAC,QAAU,CAAC;AACzD,SAAA;QAED,IAAI,aAAa,CAAC,cAAc;AACzB,eAAA,aAAa,CAAC,cAAc,KAAK,IAAI,EAC5C;YACI,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC;AAChE,SAAA;QAED,IAAI,IAAI,CAAC,KAAK;AAAE,YAAA,EAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAA;AAE1C,QAAA,aAAa,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACvC,QAAA,aAAa,CAAC,cAAc,GAAG,GAAG,CAAC;AACnC,QAAA,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC;AAElC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACnD,aAAa,CAAC,cAAc,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;KAClE,CAAA;AAED;;;;AAIG;IACK,oBAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAAiB,CAAa,EAAA;QAE1B,IAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;AACrD,QAAA,IAAA,aAAa,GAAK,CAAC,CAAC,MAAgC,cAAvC,CAAwC;AACrD,QAAA,IAAA,SAAS,GAAK,kBAAkB,CAAA,SAAvB,CAAwB;QAEzC,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QACpE,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QACzE,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;KACrE,CAAA;AAED;;;;AAIG;IACK,oBAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAAiB,CAAa,EAAA;QAE1B,IAAI,CAAE,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,CAAC,EACpD;YACK,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAChE,SAAA;QAED,IAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;AACrD,QAAA,IAAA,aAAa,GAAK,CAAC,CAAC,MAAgC,cAAvC,CAAwC;AACrD,QAAA,IAAA,SAAS,GAAK,kBAAkB,CAAA,SAAvB,CAAwB;QAEzC,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;KAC3E,CAAA;AAED;;;;AAIG;IACK,oBAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,CAAa,EAAA;QAE7B,IAAI,CAAE,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,CAAC,EACpD;YACK,CAAC,CAAC,MAAkB,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC7D,SAAA;QAED,IAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;AACrD,QAAA,IAAA,aAAa,GAAK,CAAC,CAAC,MAAgC,cAAvC,CAAwC;AACrD,QAAA,IAAA,SAAS,GAAK,kBAAkB,CAAA,SAAvB,CAAwB;QAEzC,kBAAkB,CAAC,aAAa,CAAC,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;KAC1E,CAAA;AAED;;;;AAIG;IACK,oBAAU,CAAA,SAAA,CAAA,UAAA,GAAlB,UAAmB,CAAgB,EAAA;AAE/B,QAAA,IAAI,CAAC,CAAC,OAAO,KAAK,YAAY,EAC9B;YACI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB,CAAA;AAED;;;;AAIG;IACK,oBAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,CAAa,EAAA;QAE9B,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,EAC1C;YACI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB,CAAA;;AAGM,IAAA,oBAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;QAEI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AAEhB,QAAA,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC9E,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAE3D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACxB,CAAA;;AAziBM,IAAA,oBAAA,CAAA,SAAS,GAAsB;AAClC,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,IAAI,EAAE;AACF,YAAA,aAAa,CAAC,cAAc;AAC5B,YAAA,aAAa,CAAC,oBAAoB,EACrC;KACJ,CAAC;IAoiBN,OAAC,oBAAA,CAAA;AAAA,CA7iBD,EA6iBC;;;;"}