{"version": 3, "file": "mixin-get-global-position.js", "sources": ["../../src/index.ts"], "sourcesContent": ["import { DisplayObject } from '@pixi/display';\nimport { Point } from '@pixi/math';\n\n/**\n * Returns the global position of the displayObject. Does not depend on object scale, rotation and pivot.\n * @method getGlobalPosition\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.Point} [point=new PIXI.Point()] - The point to write the global value to.\n * @param {boolean} [skipUpdate=false] - Setting to true will stop the transforms of the scene graph from\n *  being updated. This means the calculation returned MAY be out of date BUT will give you a\n *  nice performance boost.\n * @returns {PIXI.Point} The updated point.\n */\nDisplayObject.prototype.getGlobalPosition = function getGlobalPosition(point: Point = new Point(), skipUpdate = false): Point\n{\n    if (this.parent)\n    {\n        this.parent.toGlobal(this.position, point, skipUpdate);\n    }\n    else\n    {\n        point.x = this.position.x;\n        point.y = this.position.y;\n    }\n\n    return point;\n};\n"], "names": ["DisplayObject", "Point"], "mappings": ";;;;;;;;;;;;AAGA;;;;;;;;;AASG;AACHA,qBAAa,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAS,iBAAiB,CAAC,KAA0B,EAAE,UAAkB,EAAA;IAA9C,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAmB,GAAA,IAAAC,UAAK,EAAE,CAAA,EAAA;AAAE,IAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAkB,GAAA,KAAA,CAAA,EAAA;IAEjH,IAAI,IAAI,CAAC,MAAM,EACf;AACI,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC1D,KAAA;AAED,SAAA;QACI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1B,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC7B,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;;"}