{"version": 3, "file": "mixin-cache-as-bitmap.mjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>, MaskD<PERSON>, AbstractRenderer } from '@pixi/core';\nimport { Texture, BaseTexture, RenderTexture } from '@pixi/core';\nimport { Sprite } from '@pixi/sprite';\nimport type { Container, IDestroyOptions } from '@pixi/display';\nimport { DisplayObject } from '@pixi/display';\nimport type { IPointData, Rectangle } from '@pixi/math';\nimport { Matrix } from '@pixi/math';\nimport { uid } from '@pixi/utils';\nimport { settings } from '@pixi/settings';\nimport { MSAA_QUALITY } from '@pixi/constants';\n\n// Don't import CanvasRender to remove dependency on this optional package\n// this type should satisify these requirements for cacheAsBitmap types\ninterface CanvasRender<PERSON> extends AbstractRenderer\n{\n    context: CanvasRenderingContext2D;\n}\n\nconst _tempMatrix = new Matrix();\n\nDisplayObject.prototype._cacheAsBitmap = false;\nDisplayObject.prototype._cacheData = null;\nDisplayObject.prototype._cacheAsBitmapResolution = null;\nDisplayObject.prototype._cacheAsBitmapMultisample = MSAA_QUALITY.NONE;\n\n// figured there's no point adding ALL the extra variables to prototype.\n// this model can hold the information needed. This can also be generated on demand as\n// most objects are not cached as bitmaps.\n/**\n * @class\n * @ignore\n * @private\n */\nexport class CacheData\n{\n    public textureCacheId: string;\n    public originalRender: (renderer: Renderer) => void;\n    public originalRenderCanvas: (renderer: AbstractRenderer) => void;\n    public originalCalculateBounds: () => void;\n    public originalGetLocalBounds: (rect?: Rectangle) => Rectangle;\n    public originalUpdateTransform: () => void;\n    public originalDestroy: (options?: IDestroyOptions | boolean) => void;\n    public originalMask: Container | MaskData;\n    public originalFilterArea: Rectangle;\n    public originalContainsPoint: (point: IPointData) => boolean;\n    public sprite: Sprite;\n\n    constructor()\n    {\n        this.textureCacheId = null;\n\n        this.originalRender = null;\n        this.originalRenderCanvas = null;\n        this.originalCalculateBounds = null;\n        this.originalGetLocalBounds = null;\n\n        this.originalUpdateTransform = null;\n        this.originalDestroy = null;\n        this.originalMask = null;\n        this.originalFilterArea = null;\n        this.originalContainsPoint = null;\n        this.sprite = null;\n    }\n}\n\nObject.defineProperties(DisplayObject.prototype, {\n    /**\n     * The resolution to use for cacheAsBitmap. By default this will use the renderer's resolution\n     * but can be overriden for performance. Lower values will reduce memory usage at the expense\n     * of render quality. A falsey value of `null` or `0` will default to the renderer's resolution.\n     * If `cacheAsBitmap` is set to `true`, this will re-render with the new resolution.\n     * @member {number} cacheAsBitmapResolution\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     */\n    cacheAsBitmapResolution: {\n        get(): number\n        {\n            return this._cacheAsBitmapResolution;\n        },\n        set(resolution: number): void\n        {\n            if (resolution === this._cacheAsBitmapResolution)\n            {\n                return;\n            }\n\n            this._cacheAsBitmapResolution = resolution;\n\n            if (this.cacheAsBitmap)\n            {\n                // Toggle to re-render at the new resolution\n                this.cacheAsBitmap = false;\n                this.cacheAsBitmap = true;\n            }\n        },\n    },\n\n    /**\n     * The number of samples to use for cacheAsBitmap. If set to `null`, the renderer's\n     * sample count is used.\n     * If `cacheAsBitmap` is set to `true`, this will re-render with the new number of samples.\n     * @member {number} cacheAsBitmapMultisample\n     * @memberof PIXI.DisplayObject#\n     * @default PIXI.MSAA_QUALITY.NONE\n     */\n    cacheAsBitmapMultisample: {\n        get(): MSAA_QUALITY\n        {\n            return this._cacheAsBitmapMultisample;\n        },\n        set(multisample: MSAA_QUALITY): void\n        {\n            if (multisample === this._cacheAsBitmapMultisample)\n            {\n                return;\n            }\n\n            this._cacheAsBitmapMultisample = multisample;\n\n            if (this.cacheAsBitmap)\n            {\n                // Toggle to re-render with new multisample\n                this.cacheAsBitmap = false;\n                this.cacheAsBitmap = true;\n            }\n        },\n    },\n\n    /**\n     * Set this to true if you want this display object to be cached as a bitmap.\n     * This basically takes a snap shot of the display object as it is at that moment. It can\n     * provide a performance benefit for complex static displayObjects.\n     * To remove simply set this property to `false`\n     *\n     * IMPORTANT GOTCHA - Make sure that all your textures are preloaded BEFORE setting this property to true\n     * as it will take a snapshot of what is currently there. If the textures have not loaded then they will not appear.\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     */\n    cacheAsBitmap: {\n        get(): CacheData\n        {\n            return this._cacheAsBitmap;\n        },\n        set(value: CacheData): void\n        {\n            if (this._cacheAsBitmap === value)\n            {\n                return;\n            }\n\n            this._cacheAsBitmap = value;\n\n            let data: CacheData;\n\n            if (value)\n            {\n                if (!this._cacheData)\n                {\n                    this._cacheData = new CacheData();\n                }\n\n                data = this._cacheData;\n\n                data.originalRender = this.render;\n                data.originalRenderCanvas = this.renderCanvas;\n\n                data.originalUpdateTransform = this.updateTransform;\n                data.originalCalculateBounds = this.calculateBounds;\n                data.originalGetLocalBounds = this.getLocalBounds;\n\n                data.originalDestroy = this.destroy;\n\n                data.originalContainsPoint = this.containsPoint;\n\n                data.originalMask = this._mask;\n                data.originalFilterArea = this.filterArea;\n\n                this.render = this._renderCached;\n                this.renderCanvas = this._renderCachedCanvas;\n\n                this.destroy = this._cacheAsBitmapDestroy;\n            }\n            else\n            {\n                data = this._cacheData;\n\n                if (data.sprite)\n                {\n                    this._destroyCachedDisplayObject();\n                }\n\n                this.render = data.originalRender;\n                this.renderCanvas = data.originalRenderCanvas;\n                this.calculateBounds = data.originalCalculateBounds;\n                this.getLocalBounds = data.originalGetLocalBounds;\n\n                this.destroy = data.originalDestroy;\n\n                this.updateTransform = data.originalUpdateTransform;\n                this.containsPoint = data.originalContainsPoint;\n\n                this._mask = data.originalMask;\n                this.filterArea = data.originalFilterArea;\n            }\n        },\n    },\n});\n\n/**\n * Renders a cached version of the sprite with WebGL\n * @private\n * @method _renderCached\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.Renderer} renderer - the WebGL renderer\n */\nDisplayObject.prototype._renderCached = function _renderCached(renderer: Renderer): void\n{\n    if (!this.visible || this.worldAlpha <= 0 || !this.renderable)\n    {\n        return;\n    }\n\n    this._initCachedDisplayObject(renderer);\n\n    this._cacheData.sprite.transform._worldID = this.transform._worldID;\n    this._cacheData.sprite.worldAlpha = this.worldAlpha;\n    (this._cacheData.sprite as any)._render(renderer);\n};\n\n/**\n * Prepares the WebGL renderer to cache the sprite\n * @private\n * @method _initCachedDisplayObject\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.Renderer} renderer - the WebGL renderer\n */\nDisplayObject.prototype._initCachedDisplayObject = function _initCachedDisplayObject(renderer: Renderer): void\n{\n    if (this._cacheData && this._cacheData.sprite)\n    {\n        return;\n    }\n\n    // make sure alpha is set to 1 otherwise it will get rendered as invisible!\n    const cacheAlpha = this.alpha;\n\n    this.alpha = 1;\n\n    // first we flush anything left in the renderer (otherwise it would get rendered to the cached texture)\n    renderer.batch.flush();\n    // this.filters= [];\n\n    // next we find the dimensions of the untransformed object\n    // this function also calls updatetransform on all its children as part of the measuring.\n    // This means we don't need to update the transform again in this function\n    // TODO pass an object to clone too? saves having to create a new one each time!\n    const bounds = (this as Container).getLocalBounds(null, true).clone();\n\n    // add some padding!\n    if (this.filters && this.filters.length)\n    {\n        const padding = this.filters[0].padding;\n\n        bounds.pad(padding);\n    }\n\n    bounds.ceil(settings.RESOLUTION);\n\n    // for now we cache the current renderTarget that the WebGL renderer is currently using.\n    // this could be more elegant..\n    const cachedRenderTexture = renderer.renderTexture.current;\n    const cachedSourceFrame = renderer.renderTexture.sourceFrame.clone();\n    const cachedDestinationFrame = renderer.renderTexture.destinationFrame.clone();\n    const cachedProjectionTransform = renderer.projection.transform;\n\n    // We also store the filter stack - I will definitely look to change how this works a little later down the line.\n    // const stack = renderer.filterManager.filterStack;\n\n    // this renderTexture will be used to store the cached DisplayObject\n    const renderTexture = RenderTexture.create({\n        width: bounds.width,\n        height: bounds.height,\n        resolution: this.cacheAsBitmapResolution || renderer.resolution,\n        multisample: this.cacheAsBitmapMultisample ?? renderer.multisample,\n    });\n\n    const textureCacheId = `cacheAsBitmap_${uid()}`;\n\n    this._cacheData.textureCacheId = textureCacheId;\n\n    BaseTexture.addToCache(renderTexture.baseTexture, textureCacheId);\n    Texture.addToCache(renderTexture, textureCacheId);\n\n    // need to set //\n    const m = this.transform.localTransform.copyTo(_tempMatrix).invert().translate(-bounds.x, -bounds.y);\n\n    // set all properties to there original so we can render to a texture\n    this.render = this._cacheData.originalRender;\n\n    renderer.render(this, { renderTexture, clear: true, transform: m, skipUpdateTransform: false });\n    renderer.framebuffer.blit();\n\n    // now restore the state be setting the new properties\n    renderer.projection.transform = cachedProjectionTransform;\n    renderer.renderTexture.bind(cachedRenderTexture, cachedSourceFrame, cachedDestinationFrame);\n\n    // renderer.filterManager.filterStack = stack;\n\n    this.render = this._renderCached;\n    // the rest is the same as for Canvas\n    this.updateTransform = this.displayObjectUpdateTransform;\n    this.calculateBounds = this._calculateCachedBounds;\n    this.getLocalBounds = this._getCachedLocalBounds;\n\n    this._mask = null;\n    this.filterArea = null;\n    this.alpha = cacheAlpha;\n\n    // create our cached sprite\n    const cachedSprite = new Sprite(renderTexture);\n\n    cachedSprite.transform.worldTransform = this.transform.worldTransform;\n    cachedSprite.anchor.x = -(bounds.x / bounds.width);\n    cachedSprite.anchor.y = -(bounds.y / bounds.height);\n    cachedSprite.alpha = cacheAlpha;\n    cachedSprite._bounds = this._bounds;\n\n    this._cacheData.sprite = cachedSprite;\n\n    this.transform._parentID = -1;\n    // restore the transform of the cached sprite to avoid the nasty flicker..\n    if (!this.parent)\n    {\n        this.enableTempParent();\n        this.updateTransform();\n        this.disableTempParent(null);\n    }\n    else\n    {\n        this.updateTransform();\n    }\n\n    // map the hit test..\n    (this as Sprite).containsPoint = cachedSprite.containsPoint.bind(cachedSprite);\n};\n\n/**\n * Renders a cached version of the sprite with canvas\n * @private\n * @method _renderCachedCanvas\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.CanvasRenderer} renderer - The canvas renderer\n */\nDisplayObject.prototype._renderCachedCanvas = function _renderCachedCanvas(renderer: AbstractRenderer): void\n{\n    if (!this.visible || this.worldAlpha <= 0 || !this.renderable)\n    {\n        return;\n    }\n\n    this._initCachedDisplayObjectCanvas(renderer);\n\n    this._cacheData.sprite.worldAlpha = this.worldAlpha;\n    (this._cacheData.sprite as any)._renderCanvas(renderer);\n};\n\n// TODO this can be the same as the WebGL version.. will need to do a little tweaking first though..\n/**\n * Prepares the Canvas renderer to cache the sprite\n * @private\n * @method _initCachedDisplayObjectCanvas\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.CanvasRenderer} renderer - The canvas renderer\n */\nDisplayObject.prototype._initCachedDisplayObjectCanvas = function _initCachedDisplayObjectCanvas(\n    renderer: CanvasRenderer\n): void\n{\n    if (this._cacheData && this._cacheData.sprite)\n    {\n        return;\n    }\n\n    // get bounds actually transforms the object for us already!\n    const bounds = (this as Container).getLocalBounds(null, true);\n\n    const cacheAlpha = this.alpha;\n\n    this.alpha = 1;\n\n    const cachedRenderTarget = renderer.context;\n    const cachedProjectionTransform = (renderer as any)._projTransform;\n\n    bounds.ceil(settings.RESOLUTION);\n\n    const renderTexture = RenderTexture.create({ width: bounds.width, height: bounds.height });\n\n    const textureCacheId = `cacheAsBitmap_${uid()}`;\n\n    this._cacheData.textureCacheId = textureCacheId;\n\n    BaseTexture.addToCache(renderTexture.baseTexture, textureCacheId);\n    Texture.addToCache(renderTexture, textureCacheId);\n\n    // need to set //\n    const m = _tempMatrix;\n\n    this.transform.localTransform.copyTo(m);\n    m.invert();\n\n    m.tx -= bounds.x;\n    m.ty -= bounds.y;\n\n    // m.append(this.transform.worldTransform.)\n    // set all properties to there original so we can render to a texture\n    this.renderCanvas = this._cacheData.originalRenderCanvas;\n\n    renderer.render(this, { renderTexture, clear: true, transform: m, skipUpdateTransform: false });\n    // now restore the state be setting the new properties\n    renderer.context = cachedRenderTarget;\n    (renderer as any)._projTransform = cachedProjectionTransform;\n\n    this.renderCanvas = this._renderCachedCanvas;\n    // the rest is the same as for WebGL\n    this.updateTransform = this.displayObjectUpdateTransform;\n    this.calculateBounds = this._calculateCachedBounds;\n    this.getLocalBounds = this._getCachedLocalBounds;\n\n    this._mask = null;\n    this.filterArea = null;\n    this.alpha = cacheAlpha;\n\n    // create our cached sprite\n    const cachedSprite = new Sprite(renderTexture);\n\n    cachedSprite.transform.worldTransform = this.transform.worldTransform;\n    cachedSprite.anchor.x = -(bounds.x / bounds.width);\n    cachedSprite.anchor.y = -(bounds.y / bounds.height);\n    cachedSprite.alpha = cacheAlpha;\n    cachedSprite._bounds = this._bounds;\n\n    this._cacheData.sprite = cachedSprite;\n\n    this.transform._parentID = -1;\n    // restore the transform of the cached sprite to avoid the nasty flicker..\n    if (!this.parent)\n    {\n        this.parent = (renderer as any)._tempDisplayObjectParent;\n        this.updateTransform();\n        this.parent = null;\n    }\n    else\n    {\n        this.updateTransform();\n    }\n\n    // map the hit test..\n    (this as Sprite).containsPoint = cachedSprite.containsPoint.bind(cachedSprite);\n};\n\n/**\n * Calculates the bounds of the cached sprite\n * @private\n * @method\n */\nDisplayObject.prototype._calculateCachedBounds = function _calculateCachedBounds(): void\n{\n    this._bounds.clear();\n    this._cacheData.sprite.transform._worldID = this.transform._worldID;\n    (this._cacheData.sprite as any)._calculateBounds();\n    this._bounds.updateID = (this as any)._boundsID;\n};\n\n/**\n * Gets the bounds of the cached sprite.\n * @private\n * @method\n * @returns {Rectangle} The local bounds.\n */\nDisplayObject.prototype._getCachedLocalBounds = function _getCachedLocalBounds(): Rectangle\n{\n    return this._cacheData.sprite.getLocalBounds(null);\n};\n\n/**\n * Destroys the cached sprite.\n * @private\n * @method\n */\nDisplayObject.prototype._destroyCachedDisplayObject = function _destroyCachedDisplayObject(): void\n{\n    this._cacheData.sprite._texture.destroy(true);\n    this._cacheData.sprite = null;\n\n    BaseTexture.removeFromCache(this._cacheData.textureCacheId);\n    Texture.removeFromCache(this._cacheData.textureCacheId);\n\n    this._cacheData.textureCacheId = null;\n};\n\n/**\n * Destroys the cached object.\n * @private\n * @method\n * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n *  have been set to that value.\n *  Used when destroying containers, see the Container.destroy method.\n */\nDisplayObject.prototype._cacheAsBitmapDestroy = function _cacheAsBitmapDestroy(options?: IDestroyOptions | boolean): void\n{\n    this.cacheAsBitmap = false;\n    this.destroy(options);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAkBA,IAAM,WAAW,GAAG,IAAI,MAAM,EAAE,CAAC;AAEjC,aAAa,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC;AAC/C,aAAa,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC;AAC1C,aAAa,CAAC,SAAS,CAAC,wBAAwB,GAAG,IAAI,CAAC;AACxD,aAAa,CAAC,SAAS,CAAC,yBAAyB,GAAG,YAAY,CAAC,IAAI,CAAC;AAEtE;AACA;AACA;AACA;;;;AAIG;AACH,IAAA,SAAA,kBAAA,YAAA;AAcI,IAAA,SAAA,SAAA,GAAA;AAEI,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAE3B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACpC,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AAEnC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACpC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC/B,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAClC,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,SAAS,EAAE;AAC7C;;;;;;;;AAQG;AACH,IAAA,uBAAuB,EAAE;AACrB,QAAA,GAAG,EAAH,YAAA;YAEI,OAAO,IAAI,CAAC,wBAAwB,CAAC;SACxC;QACD,GAAG,EAAH,UAAI,UAAkB,EAAA;AAElB,YAAA,IAAI,UAAU,KAAK,IAAI,CAAC,wBAAwB,EAChD;gBACI,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC;YAE3C,IAAI,IAAI,CAAC,aAAa,EACtB;;AAEI,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,aAAA;SACJ;AACJ,KAAA;AAED;;;;;;;AAOG;AACH,IAAA,wBAAwB,EAAE;AACtB,QAAA,GAAG,EAAH,YAAA;YAEI,OAAO,IAAI,CAAC,yBAAyB,CAAC;SACzC;QACD,GAAG,EAAH,UAAI,WAAyB,EAAA;AAEzB,YAAA,IAAI,WAAW,KAAK,IAAI,CAAC,yBAAyB,EAClD;gBACI,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,yBAAyB,GAAG,WAAW,CAAC;YAE7C,IAAI,IAAI,CAAC,aAAa,EACtB;;AAEI,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,aAAA;SACJ;AACJ,KAAA;AAED;;;;;;;;;;AAUG;AACH,IAAA,aAAa,EAAE;AACX,QAAA,GAAG,EAAH,YAAA;YAEI,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;QACD,GAAG,EAAH,UAAI,KAAgB,EAAA;AAEhB,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EACjC;gBACI,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAE5B,YAAA,IAAI,IAAe,CAAC;AAEpB,YAAA,IAAI,KAAK,EACT;AACI,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EACpB;AACI,oBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,SAAS,EAAE,CAAC;AACrC,iBAAA;AAED,gBAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;AAEvB,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;AAClC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC;AAE9C,gBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,eAAe,CAAC;AACpD,gBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,eAAe,CAAC;AACpD,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC;AAElD,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;AAEpC,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC;AAEhD,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/B,gBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC;AAE1C,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;AACjC,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAE7C,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC7C,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;gBAEvB,IAAI,IAAI,CAAC,MAAM,EACf;oBACI,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,iBAAA;AAED,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;AAClC,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC9C,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACpD,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAElD,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;AAEpC,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACpD,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAEhD,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;AAC/B,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC7C,aAAA;SACJ;AACJ,KAAA;AACJ,CAAA,CAAC,CAAC;AAEH;;;;;;AAMG;AACH,aAAa,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,QAAkB,EAAA;AAE7E,IAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAC7D;QACI,OAAO;AACV,KAAA;AAED,IAAA,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;AAExC,IAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACpE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnD,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACtD,CAAC,CAAC;AAEF;;;;;;AAMG;AACH,aAAa,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAS,wBAAwB,CAAC,QAAkB,EAAA;;IAEnG,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAC7C;QACI,OAAO;AACV,KAAA;;AAGD,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;AAE9B,IAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;;AAGf,IAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;;;;;;AAOvB,IAAA,IAAM,MAAM,GAAI,IAAkB,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;;IAGtE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EACvC;QACI,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAExC,QAAA,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACvB,KAAA;AAED,IAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;;;AAIjC,IAAA,IAAM,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;IAC3D,IAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACrE,IAAM,sBAAsB,GAAG,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC/E,IAAA,IAAM,yBAAyB,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;;;;AAMhE,IAAA,IAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;QACvC,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,MAAM,EAAE,MAAM,CAAC,MAAM;AACrB,QAAA,UAAU,EAAE,IAAI,CAAC,uBAAuB,IAAI,QAAQ,CAAC,UAAU;QAC/D,WAAW,EAAE,MAAA,IAAI,CAAC,wBAAwB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAQ,CAAC,WAAW;AACrE,KAAA,CAAC,CAAC;AAEH,IAAA,IAAM,cAAc,GAAG,gBAAiB,GAAA,GAAG,EAAI,CAAC;AAEhD,IAAA,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,cAAc,CAAC;IAEhD,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AAClE,IAAA,OAAO,CAAC,UAAU,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;;AAGlD,IAAA,IAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;;IAGrG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAE7C,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,aAAa,EAAA,aAAA,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,CAAC;AAChG,IAAA,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;;AAG5B,IAAA,QAAQ,CAAC,UAAU,CAAC,SAAS,GAAG,yBAAyB,CAAC;IAC1D,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;;AAI5F,IAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;;AAEjC,IAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,4BAA4B,CAAC;AACzD,IAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC;AACnD,IAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAEjD,IAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,IAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,IAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;;AAGxB,IAAA,IAAM,YAAY,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;IAE/C,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;AACtE,IAAA,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACnD,IAAA,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACpD,IAAA,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC;AAChC,IAAA,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAEpC,IAAA,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;AAEtC,IAAA,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;;AAE9B,IAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAChB;QACI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAChC,KAAA;AAED,SAAA;QACI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,KAAA;;IAGA,IAAe,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACnF,CAAC,CAAC;AAEF;;;;;;AAMG;AACH,aAAa,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAS,mBAAmB,CAAC,QAA0B,EAAA;AAEjG,IAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAC7D;QACI,OAAO;AACV,KAAA;AAED,IAAA,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC;IAE9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnD,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5D,CAAC,CAAC;AAEF;AACA;;;;;;AAMG;AACH,aAAa,CAAC,SAAS,CAAC,8BAA8B,GAAG,SAAS,8BAA8B,CAC5F,QAAwB,EAAA;IAGxB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAC7C;QACI,OAAO;AACV,KAAA;;IAGD,IAAM,MAAM,GAAI,IAAkB,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE9D,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;AAE9B,IAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AAEf,IAAA,IAAM,kBAAkB,GAAG,QAAQ,CAAC,OAAO,CAAC;AAC5C,IAAA,IAAM,yBAAyB,GAAI,QAAgB,CAAC,cAAc,CAAC;AAEnE,IAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAEjC,IAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;AAE3F,IAAA,IAAM,cAAc,GAAG,gBAAiB,GAAA,GAAG,EAAI,CAAC;AAEhD,IAAA,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,cAAc,CAAC;IAEhD,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AAClE,IAAA,OAAO,CAAC,UAAU,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;;IAGlD,IAAM,CAAC,GAAG,WAAW,CAAC;IAEtB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,MAAM,EAAE,CAAC;AAEX,IAAA,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC;AACjB,IAAA,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC;;;IAIjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;IAEzD,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,aAAa,EAAA,aAAA,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,CAAC;;AAEhG,IAAA,QAAQ,CAAC,OAAO,GAAG,kBAAkB,CAAC;AACrC,IAAA,QAAgB,CAAC,cAAc,GAAG,yBAAyB,CAAC;AAE7D,IAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC;;AAE7C,IAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,4BAA4B,CAAC;AACzD,IAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC;AACnD,IAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAEjD,IAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,IAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,IAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;;AAGxB,IAAA,IAAM,YAAY,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;IAE/C,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;AACtE,IAAA,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACnD,IAAA,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACpD,IAAA,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC;AAChC,IAAA,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAEpC,IAAA,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;AAEtC,IAAA,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;;AAE9B,IAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAChB;AACI,QAAA,IAAI,CAAC,MAAM,GAAI,QAAgB,CAAC,wBAAwB,CAAC;QACzD,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,KAAA;AAED,SAAA;QACI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,KAAA;;IAGA,IAAe,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACnF,CAAC,CAAC;AAEF;;;;AAIG;AACH,aAAa,CAAC,SAAS,CAAC,sBAAsB,GAAG,SAAS,sBAAsB,GAAA;AAE5E,IAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AACrB,IAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AACnE,IAAA,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,gBAAgB,EAAE,CAAC;IACnD,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAI,IAAY,CAAC,SAAS,CAAC;AACpD,CAAC,CAAC;AAEF;;;;;AAKG;AACH,aAAa,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAS,qBAAqB,GAAA;IAE1E,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AACvD,CAAC,CAAC;AAEF;;;;AAIG;AACH,aAAa,CAAC,SAAS,CAAC,2BAA2B,GAAG,SAAS,2BAA2B,GAAA;IAEtF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAA,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;IAE9B,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;IAC5D,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;AAExD,IAAA,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC;AAC1C,CAAC,CAAC;AAEF;;;;;;;AAOG;AACH,aAAa,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAS,qBAAqB,CAAC,OAAmC,EAAA;AAE9G,IAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,IAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1B,CAAC;;;;"}