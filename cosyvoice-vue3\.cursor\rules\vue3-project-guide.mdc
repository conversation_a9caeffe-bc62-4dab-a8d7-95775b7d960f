---
description: 
globs: 
alwaysApply: false
---
# CosyVoice Vue3 项目开发指南

## 项目概述
CosyVoice是一个基于Vue3的智能语音AI系统，提供智能对话、语音合成和实时对话功能。

## 项目结构
- **项目根目录**: `H:\AI\CosyVoice\cosyvoice-vue3`
- **开发服务器**: 运行在 `http://localhost:5173`
- **启动命令**: 在 `cosyvoice-vue3` 目录中运行 `npm run dev`

## 核心文件说明

### 主要配置文件
- [package.json](mdc:CosyVoice/package.json) - 项目依赖和脚本配置
- [vite.config.ts](mdc:CosyVoice/vite.config.ts) - Vite构建工具配置，包含代理设置
- [index.html](mdc:CosyVoice/index.html) - HTML入口文件，包含CSP安全配置
- [src/main.ts](mdc:CosyVoice/src/main.ts) - Vue应用主入口，包含路由配置

### 核心源码结构
- [src/App.vue](mdc:CosyVoice/src/App.vue) - 根组件，包含导航栏和布局
- [src/views/HomeView.vue](mdc:CosyVoice/src/views/HomeView.vue) - 首页组件（直接导入，快速加载）
- [src/views/ChatView.vue](mdc:CosyVoice/src/views/ChatView.vue) - 智能对话页面（直接导入）
- [src/views/TTSView.vue](mdc:CosyVoice/src/views/TTSView.vue) - 语音合成页面（懒加载）
- [src/views/RealtimeView.vue](mdc:CosyVoice/src/views/RealtimeView.vue) - 实时对话页面（懒加载）

### API相关文件
- [src/api/index.ts](mdc:CosyVoice/src/api/index.ts) - Axios配置和拦截器
- [src/api/tts.ts](mdc:CosyVoice/src/api/tts.ts) - 语音合成API接口

## 技术栈
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Naive UI
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **样式**: CSS3 + 渐变背景动画

## 开发规范

### 路由配置
- 首页和聊天页面使用直接导入，避免懒加载延迟
- 其他页面使用懒加载优化首次加载时间
- 所有路由配置在 [src/main.ts](mdc:CosyVoice/src/main.ts) 中

### 组件开发
- 使用 `<script setup>` 语法
- TypeScript类型定义要完整
- 表单元素必须包含 `id` 和 `name` 属性
- 按钮元素要指定 `type` 属性

### 样式规范
- 使用scoped样式避免污染
- 主题色调：渐变背景 `#667eea → #764ba2 → #f093fb`
- 玻璃拟态效果：`backdrop-filter: blur()` + 半透明背景
- 响应式设计：支持移动端和桌面端

### API调用
- 统一使用 [src/api/index.ts](mdc:CosyVoice/src/api/index.ts) 中的axios实例
- API基础路径：`/api`，代理到 `http://localhost:8000`
- 错误处理统一在拦截器中处理

## 服务器配置
- **开发端口**: 5173
- **API代理**: `/api` → `http://localhost:8000`
- **WebSocket代理**: `/ws` → `ws://localhost:8000`
- **CSP配置**: 允许开发环境必要的eval使用

## 常见问题解决

### 启动服务器
确保在正确目录 `H:\AI\CosyVoice\cosyvoice-vue3` 中运行：
```bash
cd H:\AI\CosyVoice\cosyvoice-vue3
npm run dev
```

### 页面加载问题
- 首页和聊天页面已优化为直接导入
- 如遇加载慢问题，检查开发服务器是否正常运行
- 浏览器强制刷新：`Ctrl+Shift+R`

### CSP错误
项目已在 [index.html](mdc:CosyVoice/index.html) 中配置了开发环境友好的CSP规则，允许必要的脚本执行。

