/*!
 * @pixi/settings - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/settings is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
import{MIPMAP_MODES as e,MSAA_QUALITY as t,GC_MODES as n,WRAP_MODES as i,SCALE_MODES as o,PRECISION as r}from"@pixi/constants";var a={createCanvas:function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,n},getWebGLRenderingContext:function(){return WebGLRenderingContext},getNavigator:function(){return navigator},getBaseUrl:function(){var e;return null!==(e=document.baseURI)&&void 0!==e?e:window.location.href},fetch:function(e,t){return fetch(e,t)}},d=/iPhone/i,l=/iPod/i,u=/iPad/i,c=/\biOS-universal(?:.+)Mac\b/i,p=/\bAndroid(?:.+)Mobile\b/i,s=/Android/i,v=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,h=/Silk/i,A=/Windows Phone/i,E=/\bWindows(?:.+)ARM\b/i,b=/BlackBerry/i,f=/BB10/i,g=/Opera Mini/i,T=/\b(CriOS|Chrome)(?:.+)Mobile/i,I=/Mobile(?:.+)Firefox\b/i,P=function(e){return void 0!==e&&"MacIntel"===e.platform&&"number"==typeof e.maxTouchPoints&&e.maxTouchPoints>1&&"undefined"==typeof MSStream};var R=function(e){var t={userAgent:"",platform:"",maxTouchPoints:0};e||"undefined"==typeof navigator?"string"==typeof e?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0}):t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0};var n=t.userAgent,i=n.split("[FBAN");void 0!==i[1]&&(n=i[0]),void 0!==(i=n.split("Twitter"))[1]&&(n=i[0]);var o=function(e){return function(t){return t.test(e)}}(n),r={apple:{phone:o(d)&&!o(A),ipod:o(l),tablet:!o(d)&&(o(u)||P(t))&&!o(A),universal:o(c),device:(o(d)||o(l)||o(u)||o(c)||P(t))&&!o(A)},amazon:{phone:o(v),tablet:!o(v)&&o(h),device:o(v)||o(h)},android:{phone:!o(A)&&o(v)||!o(A)&&o(p),tablet:!o(A)&&!o(v)&&!o(p)&&(o(h)||o(s)),device:!o(A)&&(o(v)||o(h)||o(p)||o(s))||o(/\bokhttp\b/i)},windows:{phone:o(A),tablet:o(E),device:o(A)||o(E)},other:{blackberry:o(b),blackberry10:o(f),opera:o(g),firefox:o(I),chrome:o(T),device:o(b)||o(f)||o(g)||o(I)||o(T)},any:!1,phone:!1,tablet:!1};return r.any=r.apple.device||r.android.device||r.windows.device||r.other.device,r.phone=r.apple.phone||r.android.phone||r.windows.phone,r.tablet=r.apple.tablet||r.android.tablet||r.windows.tablet,r}(globalThis.navigator);var M={ADAPTER:a,MIPMAP_TEXTURES:e.POW2,ANISOTROPIC_LEVEL:0,RESOLUTION:1,FILTER_RESOLUTION:1,FILTER_MULTISAMPLE:t.NONE,SPRITE_MAX_TEXTURES:function(e){var t=!0;if(R.tablet||R.phone){var n;if(R.apple.device)if(n=navigator.userAgent.match(/OS (\d+)_(\d+)?/))parseInt(n[1],10)<11&&(t=!1);if(R.android.device)if(n=navigator.userAgent.match(/Android\s([0-9.]*)/))parseInt(n[1],10)<7&&(t=!1)}return t?e:4}(32),SPRITE_BATCH_SIZE:4096,RENDER_OPTIONS:{view:null,width:800,height:600,autoDensity:!1,backgroundColor:0,backgroundAlpha:1,useContextAlpha:!0,clearBeforeRender:!0,antialias:!1,preserveDrawingBuffer:!1},GC_MODE:n.AUTO,GC_MAX_IDLE:3600,GC_MAX_CHECK_COUNT:600,WRAP_MODE:i.CLAMP,SCALE_MODE:o.LINEAR,PRECISION_VERTEX:r.HIGH,PRECISION_FRAGMENT:R.apple.device?r.HIGH:r.MEDIUM,CAN_UPLOAD_SAME_BUFFER:!R.apple.device,CREATE_IMAGE_BITMAP:!1,ROUND_PIXELS:!1};export{a as BrowserAdapter,R as isMobile,M as settings};
//# sourceMappingURL=settings.min.mjs.map
