#!/usr/bin/env node

/**
 * 风格图片文件名标准化脚本
 * 解决双前缀和双后缀问题
 */

const fs = require('fs');
const path = require('path');

// 配置
const STYLES_DIR = path.join(__dirname, 'public/styles');
const BACKUP_DIR = path.join(__dirname, 'backup-styles');

console.log('🔧 开始风格图片文件名标准化...');

// 文件名清理函数
function cleanFilename(filename) {
  let cleaned = filename;
  
  // 移除双前缀：style_style_ → style_
  if (cleaned.startsWith('style_style_')) {
    cleaned = cleaned.replace('style_style_', 'style_');
    console.log(`  🔧 移除双前缀: ${filename} → ${cleaned}`);
  }
  
  // 移除双后缀：.png.png → .png
  if (cleaned.endsWith('.png.png')) {
    cleaned = cleaned.replace('.png.png', '.png');
    console.log(`  🔧 移除双后缀: ${filename} → ${cleaned}`);
  }
  
  return cleaned;
}

async function standardizeFilenames() {
  try {
    // 1. 创建备份目录
    if (!fs.existsSync(BACKUP_DIR)) {
      fs.mkdirSync(BACKUP_DIR, { recursive: true });
      console.log(`📁 创建备份目录: ${BACKUP_DIR}`);
    }
    
    // 2. 扫描风格目录
    const files = fs.readdirSync(STYLES_DIR);
    const styleFiles = files.filter(file => 
      file.startsWith('style_') && 
      (file.endsWith('.png') || file.endsWith('.png.png'))
    );
    
    console.log(`📁 找到 ${styleFiles.length} 个风格文件需要处理`);
    
    const renameMap = {};
    let renamedCount = 0;
    
    // 3. 处理每个文件
    for (const filename of styleFiles) {
      const oldPath = path.join(STYLES_DIR, filename);
      const cleanedName = cleanFilename(filename);
      const newPath = path.join(STYLES_DIR, cleanedName);
      
      // 如果文件名需要修改
      if (filename !== cleanedName) {
        try {
          // 备份原文件
          const backupPath = path.join(BACKUP_DIR, filename);
          fs.copyFileSync(oldPath, backupPath);
          
          // 检查目标文件是否已存在
          if (fs.existsSync(newPath) && newPath !== oldPath) {
            console.warn(`⚠️ 目标文件已存在，跳过: ${cleanedName}`);
            continue;
          }
          
          // 重命名文件
          fs.renameSync(oldPath, newPath);
          
          // 记录映射关系
          renameMap[filename] = cleanedName;
          renamedCount++;
          
          console.log(`✅ 重命名成功: ${filename} → ${cleanedName}`);
          
          // 验证新文件是否可读
          try {
            const stats = fs.statSync(newPath);
            if (stats.size === 0) {
              console.warn(`⚠️ 警告: 重命名后的文件大小为0: ${cleanedName}`);
            }
          } catch (verifyError) {
            console.error(`❌ 验证失败: ${cleanedName}`, verifyError.message);
          }
          
        } catch (error) {
          console.error(`❌ 重命名失败: ${filename}`, error.message);
        }
      } else {
        console.log(`✓ 文件名已标准化: ${filename}`);
      }
    }
    
    // 4. 生成映射报告
    console.log(`\n📊 处理完成统计:`);
    console.log(`  - 总文件数: ${styleFiles.length}`);
    console.log(`  - 重命名文件数: ${renamedCount}`);
    console.log(`  - 备份位置: ${BACKUP_DIR}`);
    
    if (Object.keys(renameMap).length > 0) {
      console.log(`\n📝 文件名映射关系:`);
      Object.entries(renameMap).forEach(([oldName, newName]) => {
        console.log(`  ${oldName} → ${newName}`);
      });
      
      // 保存映射关系到文件
      const mapFile = path.join(__dirname, 'filename-mapping.json');
      fs.writeFileSync(mapFile, JSON.stringify(renameMap, null, 2));
      console.log(`\n💾 映射关系已保存到: ${mapFile}`);
    }
    
    // 5. 生成StyleConfigPanel更新代码
    generateUpdateCode(renameMap);
    
    console.log('\n✅ 文件名标准化完成！');
    return renameMap;
    
  } catch (error) {
    console.error('❌ 标准化过程出错:', error);
    throw error;
  }
}

function generateUpdateCode(renameMap) {
  if (Object.keys(renameMap).length === 0) {
    console.log('ℹ️ 没有需要更新的文件路径');
    return;
  }
  
  console.log('\n📝 生成StyleConfigPanel更新代码:');
  console.log('// 在StyleConfigPanel.vue中执行以下替换:');
  
  Object.entries(renameMap).forEach(([oldName, newName]) => {
    console.log(`// ${oldName} → ${newName}`);
    console.log(`// 将 "/styles/${oldName}" 替换为 "/styles/${newName}"`);
  });
  
  // 生成具体的替换脚本
  const updateScript = Object.entries(renameMap).map(([oldName, newName]) => {
    return `sed -i 's|/styles/${oldName}|/styles/${newName}|g' src/components/modules/漫画生成/modules/StyleConfigPanel.vue`;
  }).join('\n');
  
  const scriptFile = path.join(__dirname, 'update-style-paths.sh');
  fs.writeFileSync(scriptFile, updateScript);
  console.log(`\n🔧 更新脚本已生成: ${scriptFile}`);
}

// 执行标准化
if (require.main === module) {
  standardizeFilenames()
    .then((renameMap) => {
      console.log('\n🎉 所有操作完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 操作失败:', error);
      process.exit(1);
    });
}

module.exports = { standardizeFilenames, cleanFilename };