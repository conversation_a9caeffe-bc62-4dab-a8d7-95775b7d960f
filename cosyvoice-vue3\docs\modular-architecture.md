# RealtimeView 模块化架构设计

## 🎯 架构设计原则

### 核心设计理念
- **单一职责原则**：每个组件只负责一个特定功能
- **高内聚低耦合**：组件内部功能紧密相关，组件间依赖最小化
- **可复用性**：组件可在其他场景中复用
- **AAA级游戏体验**：流畅的动画、响应式交互、视觉冲击

### 技术架构原则
- **TypeScript类型安全**：所有组件和状态都有明确类型定义
- **Composition API**：使用Vue 3的Composition API实现逻辑复用
- **Pinia状态管理**：全局状态统一管理，响应式更新
- **事件驱动通信**：组件间通过事件进行松耦合通信

## 🏗️ 组件拆分架构

### 1. 主容器组件：RealtimeView.vue
**职责**：主协调器，负责布局和组件编排
```typescript
// 只负责：
- 整体布局管理
- 组件间数据流协调
- 全局状态初始化
- 路由和生命周期管理
```

### 2. Live2D舞台组件：Live2DStage.vue
**职责**：Live2D模型展示和交互
```typescript
// 功能模块：
- 模型加载和渲染管理
- 用户交互响应（点击、手势）
- 表情和动作控制
- 语音驱动的唇形同步
- 粒子效果和背景动画
- 全屏模式切换
```

### 3. 角色配置面板：CharacterConfigPanel.vue
**职责**：角色选择和管理
```typescript
// 功能模块：
- 角色卡片列表展示
- 角色信息详情展示
- 角色编辑和创建
- 角色切换逻辑
- 角色数据加载状态
```

### 4. 语音配置面板：VoiceConfigPanel.vue
**职责**：语音设置和音色管理
```typescript
// 功能模块：
- 动态音色列表获取
- 音色预览和试听
- 语音参数调节（速度、音调、音量）
- 音色质量设置
- 实时语音测试
```

### 5. AI配置面板：AIConfigPanel.vue
**职责**：AI模型配置和管理
```typescript
// 功能模块：
- 服务提供商选择（Ollama/LMStudio）
- 动态模型列表获取
- 模型参数调节
- 连接状态监控
- 预设管理和应用
```

### 6. 对话历史组件：ConversationPanel.vue
**职责**：对话记录展示和管理
```typescript
// 功能模块：
- 消息列表展示
- 实时消息追加
- 消息搜索和过滤
- 对话导出功能
- 状态指示器（连接、转录、思考）
```

### 7. 控制按钮组件：ControlPanel.vue
**职责**：对话控制和操作
```typescript
// 功能模块：
- 开始/停止对话控制
- 清空历史记录
- 快捷键绑定
- 操作状态反馈
```

## 📊 数据流设计

### 全局状态管理（Pinia Store）
```typescript
// stores/modules/realtimeStore.ts
interface GlobalState {
  // 对话状态
  conversationHistory: RealtimeMessage[]
  realtimeState: RealtimeState
  
  // 角色状态
  selectedCharacter: CharacterCard | null
  availableCharacters: CharacterCard[]
  
  // 语音状态
  selectedVoice: string
  voiceProfiles: VoiceProfile[]
  voiceSettings: VoiceSettings
  
  // AI状态
  aiConfig: AIModelConfig
  availableModels: string[]
  
  // Live2D状态
  live2dModel: Live2DModelState
  
  // UI状态
  panelStates: PanelStates
  isFullscreen: boolean
}
```

### 组件间通信事件
```typescript
// 使用Vue 3的事件系统进行组件通信
interface ComponentEvents {
  // 角色事件
  'character:select': CharacterCard
  'character:update': CharacterCard
  
  // 语音事件
  'voice:select': string
  'voice:settings-change': VoiceSettings
  
  // AI事件
  'ai:config-change': AIModelConfig
  'ai:model-change': string
  
  // Live2D事件
  'live2d:load': string
  'live2d:interact': InteractionData
  
  // 对话事件
  'conversation:start': void
  'conversation:stop': void
  'conversation:clear': void
}
```

## 🎮 AAA级交互体验设计

### 视觉层次设计
```scss
// 深度层级系统
.depth-layer-1 { z-index: 1000; } // 背景层
.depth-layer-2 { z-index: 2000; } // Live2D舞台
.depth-layer-3 { z-index: 3000; } // 配置面板
.depth-layer-4 { z-index: 4000; } // 对话面板
.depth-layer-5 { z-index: 5000; } // 控制按钮
.depth-layer-6 { z-index: 6000; } // 弹窗层
.depth-layer-7 { z-index: 9000; } // 全屏层
```

### 动画系统设计
```typescript
// 统一的动画配置
interface AnimationConfig {
  // 缓动函数
  easing: 'ease-in-out' | 'cubic-bezier(0.4, 0, 0.2, 1)'
  
  // 动画时长
  duration: {
    fast: '200ms',
    normal: '300ms',
    slow: '500ms'
  }
  
  // 转换效果
  transitions: {
    fadeIn: 'opacity 300ms ease-in-out',
    slideUp: 'transform 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    scale: 'transform 200ms ease-in-out'
  }
}
```

### 响应式布局系统
```scss
// 断点系统
$breakpoints: (
  mobile: 320px,
  tablet: 768px,
  desktop: 1024px,
  large: 1440px,
  ultra: 1920px
);

// 网格系统
.main-layout-grid {
  display: grid;
  grid-template-areas: 
    "stage stage"
    "config conversation";
  grid-template-columns: 40% 60%;
  grid-template-rows: 60% 40%;
  
  @media (max-width: 768px) {
    grid-template-areas: 
      "stage"
      "config"
      "conversation";
    grid-template-columns: 1fr;
    grid-template-rows: 50% 25% 25%;
  }
}
```

## 🔧 技术实现细节

### 1. 组件自动导入配置
```typescript
// vite.config.ts 已配置
Components({
  dirs: ['src/components/modules/realtime'],
  extensions: ['vue'],
  resolvers: [NaiveUiResolver()]
})
```

### 2. TypeScript类型安全
```typescript
// 所有组件都有严格的Props和Emits类型
interface ComponentProps {
  // 明确的属性类型
}

interface ComponentEmits {
  // 严格的事件类型
}
```

### 3. Composition API抽象
```typescript
// 可复用的组合式函数
export function useRealtimeConnection() {
  // WebSocket连接逻辑
}

export function useAudioPlayer() {
  // 音频播放逻辑
}

export function useLive2D() {
  // Live2D控制逻辑
}
```

### 4. 错误处理和性能优化
```typescript
// 统一错误处理
interface ErrorHandling {
  // 网络错误重试机制
  // 模型加载失败回退
  // 音频播放异常处理
  // WebSocket断线重连
}

// 性能优化策略
interface PerformanceOptimization {
  // 组件懒加载
  // 虚拟滚动
  // 内存清理
  // 事件防抖节流
}
```

## 📁 文件结构规划

```
src/
├── components/modules/realtime/
│   ├── Live2DStage.vue              # Live2D舞台组件
│   ├── CharacterConfigPanel.vue    # 角色配置面板
│   ├── VoiceConfigPanel.vue        # 语音配置面板
│   ├── AIConfigPanel.vue           # AI配置面板
│   ├── ConversationPanel.vue       # 对话历史面板
│   ├── ControlPanel.vue            # 控制按钮面板
│   └── components/                  # 子组件
│       ├── CharacterCard.vue       # 角色卡片
│       ├── VoiceSelector.vue       # 音色选择器
│       ├── ModelSelector.vue       # 模型选择器
│       ├── MessageBubble.vue       # 消息气泡
│       └── StatusIndicator.vue     # 状态指示器
├── types/modules/
│   └── realtime.ts                 # 类型定义
├── stores/modules/
│   └── realtimeStore.ts            # 状态管理
├── composables/
│   ├── useRealtimeConnection.ts    # 连接管理
│   ├── useAudioPlayer.ts          # 音频播放
│   ├── useLive2D.ts               # Live2D控制
│   └── useVoiceProfile.ts         # 语音管理
├── services/
│   ├── realtimeAPI.ts             # 实时对话API
│   ├── voiceAPI.ts                # 语音服务API
│   └── modelAPI.ts                # 模型服务API
└── views/
    └── RealtimeView.vue            # 主容器组件
```

## 🚀 实施步骤

### 阶段1：基础组件拆分（优先级：高）
1. ✅ 环境配置和类型定义
2. ⏳ Live2DStage组件拆分
3. ⏳ CharacterConfigPanel组件拆分
4. ⏳ ConversationPanel组件拆分

### 阶段2：配置面板拆分（优先级：高）
1. ⏳ VoiceConfigPanel组件拆分
2. ⏳ AIConfigPanel组件拆分
3. ⏳ ControlPanel组件拆分

### 阶段3：主容器重构（优先级：中）
1. ⏳ RealtimeView主组件简化
2. ⏳ 组件集成和测试
3. ⏳ 数据流优化

### 阶段4：体验优化（优先级：中）
1. ⏳ 动画效果优化
2. ⏳ 响应式布局调整
3. ⏳ 性能监控和优化

## 🎯 成功指标

### 代码质量指标
- [ ] 单个组件文件行数 < 500行
- [ ] TypeScript覆盖率 > 95%
- [ ] 组件复用率 > 80%
- [ ] 耦合度评分 < 3

### 用户体验指标
- [ ] 组件加载时间 < 200ms
- [ ] 动画流畅度 60fps
- [ ] 内存使用 < 100MB
- [ ] 响应时间 < 100ms

### 开发体验指标
- [ ] 热重载时间 < 2s
- [ ] 构建时间 < 30s
- [ ] 新功能开发效率提升 50%
- [ ] Bug修复时间减少 60% 