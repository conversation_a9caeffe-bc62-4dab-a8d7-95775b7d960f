/// <reference path="./global.d.ts" />

import { BaseTexture } from '@pixi/core';
import type { Dict } from '@pixi/utils';
import type { ExtensionMetadata } from '@pixi/core';
import type { IPointData } from '@pixi/math';
import { LoaderResource } from '@pixi/loaders';
import { Texture } from '@pixi/core';

/** Atlas format. */
export declare interface ISpritesheetData {
    frames: Dict<ISpritesheetFrameData>;
    animations?: Dict<string[]>;
    meta: {
        scale: string;
        related_multi_packs?: string[];
    };
}

/** Represents the JSON data for a spritesheet atlas. */
export declare interface ISpritesheetFrameData {
    frame: {
        x: number;
        y: number;
        w: number;
        h: number;
    };
    trimmed?: boolean;
    rotated?: boolean;
    sourceSize?: {
        w: number;
        h: number;
    };
    spriteSourceSize?: {
        x: number;
        y: number;
    };
    anchor?: IPointData;
}

/**
 * Utility class for maintaining reference to a collection
 * of Textures on a single Spritesheet.
 *
 * To access a sprite sheet from your code you may pass its JSON data file to <PERSON><PERSON>'s loader:
 *
 * ```js
 * PIXI.Loader.shared.add("images/spritesheet.json").load(setup);
 *
 * function setup() {
 *   let sheet = PIXI.Loader.shared.resources["images/spritesheet.json"].spritesheet;
 *   ...
 * }
 * ```
 *
 * Alternately, you may circumvent the loader by instantiating the Spritesheet directly:
 * ```js
 * const sheet = new PIXI.Spritesheet(texture, spritesheetData);
 * await sheet.parse();
 * console.log('Spritesheet ready to use!');
 * ```
 *
 * With the `sheet.textures` you can create Sprite objects,`sheet.animations` can be used to create an AnimatedSprite.
 *
 * Sprite sheets can be packed using tools like {@link https://codeandweb.com/texturepacker|TexturePacker},
 * {@link https://renderhjs.net/shoebox/|Shoebox} or {@link https://github.com/krzysztof-o/spritesheet.js|Spritesheet.js}.
 * Default anchor points (see {@link PIXI.Texture#defaultAnchor}) and grouping of animation sprites are currently only
 * supported by TexturePacker.
 * @memberof PIXI
 */
export declare class Spritesheet {
    /** The maximum number of Textures to build per process. */
    static readonly BATCH_SIZE = 1000;
    /** For multi-packed spritesheets, this contains a reference to all the other spritesheets it depends on. */
    linkedSheets: Spritesheet[];
    /** Reference to ths source texture. */
    baseTexture: BaseTexture;
    /**
     * A map containing all textures of the sprite sheet.
     * Can be used to create a {@link PIXI.Sprite|Sprite}:
     * ```js
     * new PIXI.Sprite(sheet.textures["image.png"]);
     * ```
     */
    textures: Dict<Texture>;
    /**
     * A map containing the textures for each animation.
     * Can be used to create an {@link PIXI.AnimatedSprite|AnimatedSprite}:
     * ```js
     * new PIXI.AnimatedSprite(sheet.animations["anim_name"])
     * ```
     */
    animations: Dict<Texture[]>;
    /**
     * Reference to the original JSON data.
     * @type {object}
     */
    data: ISpritesheetData;
    /** The resolution of the spritesheet. */
    resolution: number;
    /**
     * Reference to original source image from the Loader. This reference is retained so we
     * can destroy the Texture later on. It is never used internally.
     */
    private _texture;
    /**
     * Map of spritesheet frames.
     * @type {object}
     */
    private _frames;
    /** Collection of frame names. */
    private _frameKeys;
    /** Current batch index being processed. */
    private _batchIndex;
    /**
     * Callback when parse is completed.
     * @type {Function}
     */
    private _callback;
    /**
     * @param texture - Reference to the source BaseTexture object.
     * @param {object} data - Spritesheet image data.
     * @param resolutionFilename - The filename to consider when determining
     *        the resolution of the spritesheet. If not provided, the imageUrl will
     *        be used on the BaseTexture.
     */
    constructor(texture: BaseTexture | Texture, data: ISpritesheetData, resolutionFilename?: string);
    /**
     * Generate the resolution from the filename or fallback
     * to the meta.scale field of the JSON data.
     * @param resolutionFilename - The filename to use for resolving
     *        the default resolution.
     * @returns Resolution to use for spritesheet.
     */
    private _updateResolution;
    /**
     * Parser spritesheet from loaded data. This is done asynchronously
     * to prevent creating too many Texture within a single process.
     * @method PIXI.Spritesheet#parse
     */
    parse(): Promise<Dict<Texture>>;
    /**
     * Please use the Promise-based version of this function.
     * @method PIXI.Spritesheet#parse
     * @deprecated since version 6.5.0
     * @param {Function} callback - Callback when complete returns
     *        a map of the Textures for this spritesheet.
     */
    parse(callback?: (textures?: Dict<Texture>) => void): void;
    /**
     * Process a batch of frames
     * @param initialFrameIndex - The index of frame to start.
     */
    private _processFrames;
    /** Parse animations config. */
    private _processAnimations;
    /** The parse has completed. */
    private _parseComplete;
    /** Begin the next batch of textures. */
    private _nextBatch;
    /**
     * Destroy Spritesheet and don't use after this.
     * @param {boolean} [destroyBase=false] - Whether to destroy the base texture as well
     */
    destroy(destroyBase?: boolean): void;
}

/**
 * {@link PIXI.Loader} middleware for loading texture atlases that have been created with
 * TexturePacker or similar JSON-based spritesheet.
 *
 * This middleware automatically generates Texture resources.
 *
 * If you're using Webpack or other bundlers and plan on bundling the atlas' JSON,
 * use the {@link PIXI.Spritesheet} class to directly parse the JSON.
 *
 * The Loader's image Resource name is automatically appended with `"_image"`.
 * If a Resource with this name is already loaded, the Loader will skip parsing the
 * Spritesheet. The code below will generate an internal Loader Resource called `"myatlas_image"`.
 * @example
 * loader.add('myatlas', 'path/to/myatlas.json');
 * loader.load(() => {
 *   loader.resources.myatlas; // atlas JSON resource
 *   loader.resources.myatlas_image; // atlas Image resource
 * });
 * @memberof PIXI
 */
export declare class SpritesheetLoader {
    /** @ignore */
    static extension: ExtensionMetadata;
    /**
     * Called after a resource is loaded.
     * @see PIXI.Loader.loaderMiddleware
     * @param resource
     * @param next
     */
    static use(resource: LoaderResource, next: (...args: unknown[]) => void): void;
    /**
     * Get the spritesheets root path
     * @param resource - Resource to check path
     * @param baseUrl - Base root url
     */
    static getResourcePath(resource: LoaderResource, baseUrl: string): string;
}

export { }
