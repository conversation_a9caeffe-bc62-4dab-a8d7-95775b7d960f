/*!
 * @pixi/particle-container - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/particle-container is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_particle_container=function(t,e,i,r,o,a){"use strict";var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},n(t,e)};function s(t,e){function i(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var u=function(t){function i(i,r,o,a){void 0===i&&(i=1500),void 0===o&&(o=16384),void 0===a&&(a=!1);var n=t.call(this)||this;return o>16384&&(o=16384),n._properties=[!1,!0,!1,!1,!1],n._maxSize=i,n._batchSize=o,n._buffers=null,n._bufferUpdateIDs=[],n._updateID=0,n.interactiveChildren=!1,n.blendMode=e.BLEND_MODES.NORMAL,n.autoResize=a,n.roundPixels=!0,n.baseTexture=null,n.setProperties(r),n._tint=0,n.tintRgb=new Float32Array(4),n.tint=16777215,n}return s(i,t),i.prototype.setProperties=function(t){t&&(this._properties[0]="vertices"in t||"scale"in t?!!t.vertices||!!t.scale:this._properties[0],this._properties[1]="position"in t?!!t.position:this._properties[1],this._properties[2]="rotation"in t?!!t.rotation:this._properties[2],this._properties[3]="uvs"in t?!!t.uvs:this._properties[3],this._properties[4]="tint"in t||"alpha"in t?!!t.tint||!!t.alpha:this._properties[4])},i.prototype.updateTransform=function(){this.displayObjectUpdateTransform()},Object.defineProperty(i.prototype,"tint",{get:function(){return this._tint},set:function(t){this._tint=t,r.hex2rgb(t,this.tintRgb)},enumerable:!1,configurable:!0}),i.prototype.render=function(t){var e=this;this.visible&&!(this.worldAlpha<=0)&&this.children.length&&this.renderable&&(this.baseTexture||(this.baseTexture=this.children[0]._texture.baseTexture,this.baseTexture.valid||this.baseTexture.once("update",(function(){return e.onChildrenChange(0)}))),t.batch.setObjectRenderer(t.plugins.particle),t.plugins.particle.render(this))},i.prototype.onChildrenChange=function(t){for(var e=Math.floor(t/this._batchSize);this._bufferUpdateIDs.length<e;)this._bufferUpdateIDs.push(0);this._bufferUpdateIDs[e]=++this._updateID},i.prototype.dispose=function(){if(this._buffers){for(var t=0;t<this._buffers.length;++t)this._buffers[t].destroy();this._buffers=null}},i.prototype.destroy=function(e){t.prototype.destroy.call(this,e),this.dispose(),this._properties=null,this._buffers=null,this._bufferUpdateIDs=null},i}(i.Container),h=function(){function t(t,i,r){this.geometry=new o.Geometry,this.indexBuffer=null,this.size=r,this.dynamicProperties=[],this.staticProperties=[];for(var a=0;a<t.length;++a){var n=t[a];n={attributeName:n.attributeName,size:n.size,uploadFunction:n.uploadFunction,type:n.type||e.TYPES.FLOAT,offset:n.offset},i[a]?this.dynamicProperties.push(n):this.staticProperties.push(n)}this.staticStride=0,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.dynamicStride=0,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this._updateID=0,this.initBuffers()}return t.prototype.initBuffers=function(){var t=this.geometry,i=0;this.indexBuffer=new o.Buffer(r.createIndicesForQuads(this.size),!0,!0),t.addIndex(this.indexBuffer),this.dynamicStride=0;for(var a=0;a<this.dynamicProperties.length;++a){(h=this.dynamicProperties[a]).offset=i,i+=h.size,this.dynamicStride+=h.size}var n=new ArrayBuffer(this.size*this.dynamicStride*4*4);this.dynamicData=new Float32Array(n),this.dynamicDataUint32=new Uint32Array(n),this.dynamicBuffer=new o.Buffer(this.dynamicData,!1,!1);var s=0;this.staticStride=0;for(a=0;a<this.staticProperties.length;++a){(h=this.staticProperties[a]).offset=s,s+=h.size,this.staticStride+=h.size}var u=new ArrayBuffer(this.size*this.staticStride*4*4);this.staticData=new Float32Array(u),this.staticDataUint32=new Uint32Array(u),this.staticBuffer=new o.Buffer(this.staticData,!0,!1);for(a=0;a<this.dynamicProperties.length;++a){var h=this.dynamicProperties[a];t.addAttribute(h.attributeName,this.dynamicBuffer,0,h.type===e.TYPES.UNSIGNED_BYTE,h.type,4*this.dynamicStride,4*h.offset)}for(a=0;a<this.staticProperties.length;++a){h=this.staticProperties[a];t.addAttribute(h.attributeName,this.staticBuffer,0,h.type===e.TYPES.UNSIGNED_BYTE,h.type,4*this.staticStride,4*h.offset)}},t.prototype.uploadDynamic=function(t,i,r){for(var o=0;o<this.dynamicProperties.length;o++){var a=this.dynamicProperties[o];a.uploadFunction(t,i,r,a.type===e.TYPES.UNSIGNED_BYTE?this.dynamicDataUint32:this.dynamicData,this.dynamicStride,a.offset)}this.dynamicBuffer._updateID++},t.prototype.uploadStatic=function(t,i,r){for(var o=0;o<this.staticProperties.length;o++){var a=this.staticProperties[o];a.uploadFunction(t,i,r,a.type===e.TYPES.UNSIGNED_BYTE?this.staticDataUint32:this.staticData,this.staticStride,a.offset)}this.staticBuffer._updateID++},t.prototype.destroy=function(){this.indexBuffer=null,this.dynamicProperties=null,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this.staticProperties=null,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.geometry.destroy()},t}(),p=function(t){function i(i){var r=t.call(this,i)||this;return r.shader=null,r.properties=null,r.tempMatrix=new a.Matrix,r.properties=[{attributeName:"aVertexPosition",size:2,uploadFunction:r.uploadVertices,offset:0},{attributeName:"aPositionCoord",size:2,uploadFunction:r.uploadPosition,offset:0},{attributeName:"aRotation",size:1,uploadFunction:r.uploadRotation,offset:0},{attributeName:"aTextureCoord",size:2,uploadFunction:r.uploadUvs,offset:0},{attributeName:"aColor",size:1,type:e.TYPES.UNSIGNED_BYTE,uploadFunction:r.uploadTint,offset:0}],r.shader=o.Shader.from("attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\nattribute vec4 aColor;\n\nattribute vec2 aPositionCoord;\nattribute float aRotation;\n\nuniform mat3 translationMatrix;\nuniform vec4 uColor;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nvoid main(void){\n    float x = (aVertexPosition.x) * cos(aRotation) - (aVertexPosition.y) * sin(aRotation);\n    float y = (aVertexPosition.x) * sin(aRotation) + (aVertexPosition.y) * cos(aRotation);\n\n    vec2 v = vec2(x, y);\n    v = v + aPositionCoord;\n\n    gl_Position = vec4((translationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = aTextureCoord;\n    vColor = aColor * uColor;\n}\n","varying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nuniform sampler2D uSampler;\n\nvoid main(void){\n    vec4 color = texture2D(uSampler, vTextureCoord) * vColor;\n    gl_FragColor = color;\n}",{}),r.state=o.State.for2d(),r}return s(i,t),i.prototype.render=function(t){var e=t.children,i=t._maxSize,o=t._batchSize,a=this.renderer,n=e.length;if(0!==n){n>i&&!t.autoResize&&(n=i);var s=t._buffers;s||(s=t._buffers=this.generateBuffers(t));var u=e[0]._texture.baseTexture,h=u.alphaMode>0;this.state.blendMode=r.correctBlendMode(t.blendMode,h),a.state.set(this.state);var p=a.gl,c=t.worldTransform.copyTo(this.tempMatrix);c.prepend(a.globalUniforms.uniforms.projectionMatrix),this.shader.uniforms.translationMatrix=c.toArray(!0),this.shader.uniforms.uColor=r.premultiplyRgba(t.tintRgb,t.worldAlpha,this.shader.uniforms.uColor,h),this.shader.uniforms.uSampler=u,this.renderer.shader.bind(this.shader);for(var f=!1,l=0,d=0;l<n;l+=o,d+=1){var y=n-l;y>o&&(y=o),d>=s.length&&s.push(this._generateOneMoreBuffer(t));var v=s[d];v.uploadDynamic(e,l,y);var _=t._bufferUpdateIDs[d]||0;(f=f||v._updateID<_)&&(v._updateID=t._updateID,v.uploadStatic(e,l,y)),a.geometry.bind(v.geometry),p.drawElements(p.TRIANGLES,6*y,p.UNSIGNED_SHORT,0)}}},i.prototype.generateBuffers=function(t){for(var e=[],i=t._maxSize,r=t._batchSize,o=t._properties,a=0;a<i;a+=r)e.push(new h(this.properties,o,r));return e},i.prototype._generateOneMoreBuffer=function(t){var e=t._batchSize,i=t._properties;return new h(this.properties,i,e)},i.prototype.uploadVertices=function(t,e,i,r,o,a){for(var n=0,s=0,u=0,h=0,p=0;p<i;++p){var c=t[e+p],f=c._texture,l=c.scale.x,d=c.scale.y,y=f.trim,v=f.orig;y?(n=(s=y.x-c.anchor.x*v.width)+y.width,u=(h=y.y-c.anchor.y*v.height)+y.height):(n=v.width*(1-c.anchor.x),s=v.width*-c.anchor.x,u=v.height*(1-c.anchor.y),h=v.height*-c.anchor.y),r[a]=s*l,r[a+1]=h*d,r[a+o]=n*l,r[a+o+1]=h*d,r[a+2*o]=n*l,r[a+2*o+1]=u*d,r[a+3*o]=s*l,r[a+3*o+1]=u*d,a+=4*o}},i.prototype.uploadPosition=function(t,e,i,r,o,a){for(var n=0;n<i;n++){var s=t[e+n].position;r[a]=s.x,r[a+1]=s.y,r[a+o]=s.x,r[a+o+1]=s.y,r[a+2*o]=s.x,r[a+2*o+1]=s.y,r[a+3*o]=s.x,r[a+3*o+1]=s.y,a+=4*o}},i.prototype.uploadRotation=function(t,e,i,r,o,a){for(var n=0;n<i;n++){var s=t[e+n].rotation;r[a]=s,r[a+o]=s,r[a+2*o]=s,r[a+3*o]=s,a+=4*o}},i.prototype.uploadUvs=function(t,e,i,r,o,a){for(var n=0;n<i;++n){var s=t[e+n]._texture._uvs;s?(r[a]=s.x0,r[a+1]=s.y0,r[a+o]=s.x1,r[a+o+1]=s.y1,r[a+2*o]=s.x2,r[a+2*o+1]=s.y2,r[a+3*o]=s.x3,r[a+3*o+1]=s.y3,a+=4*o):(r[a]=0,r[a+1]=0,r[a+o]=0,r[a+o+1]=0,r[a+2*o]=0,r[a+2*o+1]=0,r[a+3*o]=0,r[a+3*o+1]=0,a+=4*o)}},i.prototype.uploadTint=function(t,e,i,o,a,n){for(var s=0;s<i;++s){var u=t[e+s],h=u._texture.baseTexture.alphaMode>0,p=u.alpha,c=p<1&&h?r.premultiplyTint(u._tintRGB,p):u._tintRGB+(255*p<<24);o[n]=c,o[n+a]=c,o[n+2*a]=c,o[n+3*a]=c,n+=4*a}},i.prototype.destroy=function(){t.prototype.destroy.call(this),this.shader&&(this.shader.destroy(),this.shader=null),this.tempMatrix=null},i.extension={name:"particle",type:o.ExtensionType.RendererPlugin},i}(o.ObjectRenderer);return t.ParticleContainer=u,t.ParticleRenderer=p,Object.defineProperty(t,"__esModule",{value:!0}),t}({},PIXI,PIXI,PIXI.utils,PIXI,PIXI);Object.assign(this.PIXI,_pixi_particle_container);
//# sourceMappingURL=particle-container.min.js.map
