/*!
 * @pixi/utils - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/utils is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{},this.PIXI.utils=this.PIXI.utils||{};var _pixi_utils=function(e,t,n){"use strict";var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var i=o((function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function o(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function i(e,t,r,i,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new o(r,i||e,a),h=n?n+t:t;return e._events[h]?e._events[h].fn?e._events[h]=[e._events[h],s]:e._events[h].push(s):(e._events[h]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,o=[];if(0===this._eventsCount)return o;for(r in e=this._events)t.call(e,r)&&o.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,a=new Array(i);o<i;o++)a[o]=r[o].fn;return a},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,o,i,a){var s=arguments,h=n?n+e:e;if(!this._events[h])return!1;var f,l,u=this._events[h],c=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),c){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,r),!0;case 4:return u.fn.call(u.context,t,r,o),!0;case 5:return u.fn.call(u.context,t,r,o,i),!0;case 6:return u.fn.call(u.context,t,r,o,i,a),!0}for(l=1,f=new Array(c-1);l<c;l++)f[l-1]=s[l];u.fn.apply(u.context,f)}else{var p,d=u.length;for(l=0;l<d;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),c){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,r);break;case 4:u[l].fn.call(u[l].context,t,r,o);break;default:if(!f)for(p=1,f=new Array(c-1);p<c;p++)f[p-1]=s[p];u[l].fn.apply(u[l].context,f)}}return!0},s.prototype.on=function(e,t,n){return i(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return i(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,o){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var s=this._events[i];if(s.fn)s.fn!==t||o&&!s.once||r&&s.context!==r||a(this,i);else{for(var h=0,f=[],l=s.length;h<l;h++)(s[h].fn!==t||o&&!s[h].once||r&&s[h].context!==r)&&f.push(s[h]);f.length?this._events[i]=1===f.length?f[0]:f:a(this,i)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s})),a=h,s=h;function h(e,t,n){n=n||2;var r,o,i,a,s,h,l,c=t&&t.length,p=c?t[0]*n:e.length,d=f(e,0,p,n,!0),v=[];if(!d||d.next===d.prev)return v;if(c&&(d=function(e,t,n,r){var o,i,a,s=[];for(o=0,i=t.length;o<i;o++)(a=f(e,t[o]*r,o<i-1?t[o+1]*r:e.length,r,!1))===a.next&&(a.steiner=!0),s.push(b(a));for(s.sort(g),o=0;o<s.length;o++)n=x(s[o],n);return n}(e,t,d,n)),e.length>80*n){r=i=e[0],o=a=e[1];for(var y=n;y<p;y+=n)(s=e[y])<r&&(r=s),(h=e[y+1])<o&&(o=h),s>i&&(i=s),h>a&&(a=h);l=0!==(l=Math.max(i-r,a-o))?32767/l:0}return u(d,v,n,r,o,l,0),v}function f(e,t,n,r,o){var i,a;if(o===j(e,t,n,r)>0)for(i=t;i<n;i+=r)a=D(i,e[i],e[i+1],a);else for(i=n-r;i>=t;i-=r)a=D(i,e[i],e[i+1],a);return a&&C(a,a.next)&&(M(a),a=a.next),a}function l(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!C(r,r.next)&&0!==E(r.prev,r,r.next))r=r.next;else{if(M(r),(r=t=r.prev)===r.next)break;n=!0}}while(n||r!==t);return t}function u(e,t,n,r,o,i,a){if(e){!a&&i&&function(e,t,n,r){var o=e;do{0===o.z&&(o.z=m(o.x,o.y,t,n,r)),o.prevZ=o.prev,o.nextZ=o.next,o=o.next}while(o!==e);o.prevZ.nextZ=null,o.prevZ=null,function(e){var t,n,r,o,i,a,s,h,f=1;do{for(n=e,e=null,i=null,a=0;n;){for(a++,r=n,s=0,t=0;t<f&&(s++,r=r.nextZ);t++);for(h=f;s>0||h>0&&r;)0!==s&&(0===h||!r||n.z<=r.z)?(o=n,n=n.nextZ,s--):(o=r,r=r.nextZ,h--),i?i.nextZ=o:e=o,o.prevZ=i,i=o;n=r}i.nextZ=null,f*=2}while(a>1)}(o)}(e,r,o,i);for(var s,h,f=e;e.prev!==e.next;)if(s=e.prev,h=e.next,i?p(e,r,o,i):c(e))t.push(s.i/n|0),t.push(e.i/n|0),t.push(h.i/n|0),M(e),e=h.next,f=h.next;else if((e=h)===f){a?1===a?u(e=d(l(e),t,n),t,n,r,o,i,2):2===a&&v(e,t,n,r,o,i):u(l(e),t,n,r,o,i,1);break}}}function c(e){var t=e.prev,n=e,r=e.next;if(E(t,n,r)>=0)return!1;for(var o=t.x,i=n.x,a=r.x,s=t.y,h=n.y,f=r.y,l=o<i?o<a?o:a:i<a?i:a,u=s<h?s<f?s:f:h<f?h:f,c=o>i?o>a?o:a:i>a?i:a,p=s>h?s>f?s:f:h>f?h:f,d=r.next;d!==t;){if(d.x>=l&&d.x<=c&&d.y>=u&&d.y<=p&&w(o,s,i,h,a,f,d.x,d.y)&&E(d.prev,d,d.next)>=0)return!1;d=d.next}return!0}function p(e,t,n,r){var o=e.prev,i=e,a=e.next;if(E(o,i,a)>=0)return!1;for(var s=o.x,h=i.x,f=a.x,l=o.y,u=i.y,c=a.y,p=s<h?s<f?s:f:h<f?h:f,d=l<u?l<c?l:c:u<c?u:c,v=s>h?s>f?s:f:h>f?h:f,g=l>u?l>c?l:c:u>c?u:c,x=m(p,d,t,n,r),y=m(v,g,t,n,r),b=e.prevZ,A=e.nextZ;b&&b.z>=x&&A&&A.z<=y;){if(b.x>=p&&b.x<=v&&b.y>=d&&b.y<=g&&b!==o&&b!==a&&w(s,l,h,u,f,c,b.x,b.y)&&E(b.prev,b,b.next)>=0)return!1;if(b=b.prevZ,A.x>=p&&A.x<=v&&A.y>=d&&A.y<=g&&A!==o&&A!==a&&w(s,l,h,u,f,c,A.x,A.y)&&E(A.prev,A,A.next)>=0)return!1;A=A.nextZ}for(;b&&b.z>=x;){if(b.x>=p&&b.x<=v&&b.y>=d&&b.y<=g&&b!==o&&b!==a&&w(s,l,h,u,f,c,b.x,b.y)&&E(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;A&&A.z<=y;){if(A.x>=p&&A.x<=v&&A.y>=d&&A.y<=g&&A!==o&&A!==a&&w(s,l,h,u,f,c,A.x,A.y)&&E(A.prev,A,A.next)>=0)return!1;A=A.nextZ}return!0}function d(e,t,n){var r=e;do{var o=r.prev,i=r.next.next;!C(o,i)&&_(o,r,r.next,i)&&k(o,i)&&k(i,o)&&(t.push(o.i/n|0),t.push(r.i/n|0),t.push(i.i/n|0),M(r),M(r.next),r=e=i),r=r.next}while(r!==e);return l(r)}function v(e,t,n,r,o,i){var a=e;do{for(var s=a.next.next;s!==a.prev;){if(a.i!==s.i&&A(a,s)){var h=I(a,s);return a=l(a,a.next),h=l(h,h.next),u(a,t,n,r,o,i,0),void u(h,t,n,r,o,i,0)}s=s.next}a=a.next}while(a!==e)}function g(e,t){return e.x-t.x}function x(e,t){var n=function(e,t){var n,r=t,o=e.x,i=e.y,a=-1/0;do{if(i<=r.y&&i>=r.next.y&&r.next.y!==r.y){var s=r.x+(i-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(s<=o&&s>a&&(a=s,n=r.x<r.next.x?r:r.next,s===o))return n}r=r.next}while(r!==t);if(!n)return null;var h,f=n,l=n.x,u=n.y,c=1/0;r=n;do{o>=r.x&&r.x>=l&&o!==r.x&&w(i<u?o:a,i,l,u,i<u?a:o,i,r.x,r.y)&&(h=Math.abs(i-r.y)/(o-r.x),k(r,e)&&(h<c||h===c&&(r.x>n.x||r.x===n.x&&y(n,r)))&&(n=r,c=h)),r=r.next}while(r!==f);return n}(e,t);if(!n)return t;var r=I(n,e);return l(r,r.next),l(n,n.next)}function y(e,t){return E(e.prev,e,t.prev)<0&&E(t.next,e,e.next)<0}function m(e,t,n,r,o){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-n)*o|0)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-r)*o|0)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function b(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function w(e,t,n,r,o,i,a,s){return(o-a)*(t-s)>=(e-a)*(i-s)&&(e-a)*(r-s)>=(n-a)*(t-s)&&(n-a)*(i-s)>=(o-a)*(r-s)}function A(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&_(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(k(e,t)&&k(t,e)&&function(e,t){var n=e,r=!1,o=(e.x+t.x)/2,i=(e.y+t.y)/2;do{n.y>i!=n.next.y>i&&n.next.y!==n.y&&o<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(E(e.prev,e,t.prev)||E(e,t.prev,t))||C(e,t)&&E(e.prev,e,e.next)>0&&E(t.prev,t,t.next)>0)}function E(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function C(e,t){return e.x===t.x&&e.y===t.y}function _(e,t,n,r){var o=P(E(e,t,n)),i=P(E(e,t,r)),a=P(E(n,r,e)),s=P(E(n,r,t));return o!==i&&a!==s||(!(0!==o||!O(e,n,t))||(!(0!==i||!O(e,r,t))||(!(0!==a||!O(n,e,r))||!(0!==s||!O(n,t,r)))))}function O(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function P(e){return e>0?1:e<0?-1:0}function k(e,t){return E(e.prev,e,e.next)<0?E(e,t,e.next)>=0&&E(e,e.prev,t)>=0:E(e,t,e.prev)<0||E(e,e.next,t)<0}function I(e,t){var n=new R(e.i,e.x,e.y),r=new R(t.i,t.x,t.y),o=e.next,i=t.prev;return e.next=t,t.prev=e,n.next=o,o.prev=n,r.next=n,n.prev=r,i.next=r,r.prev=i,r}function D(e,t,n,r){var o=new R(e,t,n);return r?(o.next=r.next,o.prev=r,r.next.prev=o,r.next=o):(o.prev=o,o.next=o),o}function M(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function R(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}function j(e,t,n,r){for(var o=0,i=t,a=n-r;i<n;i+=r)o+=(e[a]-e[i])*(e[i+1]+e[a+1]),a=i;return o}h.deviation=function(e,t,n,r){var o=t&&t.length,i=o?t[0]*n:e.length,a=Math.abs(j(e,0,i,n));if(o)for(var s=0,h=t.length;s<h;s++){var f=t[s]*n,l=s<h-1?t[s+1]*n:e.length;a-=Math.abs(j(e,f,l,n))}var u=0;for(s=0;s<r.length;s+=3){var c=r[s]*n,p=r[s+1]*n,d=r[s+2]*n;u+=Math.abs((e[c]-e[d])*(e[p+1]-e[c+1])-(e[c]-e[p])*(e[d+1]-e[c+1]))}return 0===a&&0===u?0:Math.abs((u-a)/a)},h.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,o=0;o<e.length;o++){for(var i=0;i<e[o].length;i++)for(var a=0;a<t;a++)n.vertices.push(e[o][i][a]);o>0&&(r+=e[o-1].length,n.holes.push(r))}return n},a.default=s;var T=o((function(e,t){!function(n){var o=t&&!t.nodeType&&t,i=e&&!e.nodeType&&e,a="object"==typeof r&&r;a.global!==a&&a.window!==a&&a.self!==a||(n=a);var s,h,f=2147483647,l=36,u=/^xn--/,c=/[^\x20-\x7E]/,p=/[\x2E\u3002\uFF0E\uFF61]/g,d={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},v=Math.floor,g=String.fromCharCode;function x(e){throw RangeError(d[e])}function y(e,t){for(var n=e.length,r=[];n--;)r[n]=t(e[n]);return r}function m(e,t){var n=e.split("@"),r="";return n.length>1&&(r=n[0]+"@",e=n[1]),r+y((e=e.replace(p,".")).split("."),t).join(".")}function b(e){for(var t,n,r=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(n=e.charCodeAt(o++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),o--):r.push(t);return r}function w(e){return y(e,(function(e){var t="";return e>65535&&(t+=g((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=g(e)})).join("")}function A(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function E(e,t,n){var r=0;for(e=n?v(e/700):e>>1,e+=v(e/t);e>455;r+=l)e=v(e/35);return v(r+36*e/(e+38))}function C(e){var t,n,r,o,i,a,s,h,u,c,p,d=[],g=e.length,y=0,m=128,b=72;for((n=e.lastIndexOf("-"))<0&&(n=0),r=0;r<n;++r)e.charCodeAt(r)>=128&&x("not-basic"),d.push(e.charCodeAt(r));for(o=n>0?n+1:0;o<g;){for(i=y,a=1,s=l;o>=g&&x("invalid-input"),((h=(p=e.charCodeAt(o++))-48<10?p-22:p-65<26?p-65:p-97<26?p-97:l)>=l||h>v((f-y)/a))&&x("overflow"),y+=h*a,!(h<(u=s<=b?1:s>=b+26?26:s-b));s+=l)a>v(f/(c=l-u))&&x("overflow"),a*=c;b=E(y-i,t=d.length+1,0==i),v(y/t)>f-m&&x("overflow"),m+=v(y/t),y%=t,d.splice(y++,0,m)}return w(d)}function _(e){var t,n,r,o,i,a,s,h,u,c,p,d,y,m,w,C=[];for(d=(e=b(e)).length,t=128,n=0,i=72,a=0;a<d;++a)(p=e[a])<128&&C.push(g(p));for(r=o=C.length,o&&C.push("-");r<d;){for(s=f,a=0;a<d;++a)(p=e[a])>=t&&p<s&&(s=p);for(s-t>v((f-n)/(y=r+1))&&x("overflow"),n+=(s-t)*y,t=s,a=0;a<d;++a)if((p=e[a])<t&&++n>f&&x("overflow"),p==t){for(h=n,u=l;!(h<(c=u<=i?1:u>=i+26?26:u-i));u+=l)w=h-c,m=l-c,C.push(g(A(c+w%m,0))),h=v(w/m);C.push(g(A(h,0))),i=E(n,y,r==o),n=0,++r}++n,++t}return C.join("")}if(s={version:"1.3.2",ucs2:{decode:b,encode:w},decode:C,encode:_,toASCII:function(e){return m(e,(function(e){return c.test(e)?"xn--"+_(e):e}))},toUnicode:function(e){return m(e,(function(e){return u.test(e)?C(e.slice(4).toLowerCase()):e}))}},o&&i)if(e.exports==o)i.exports=s;else for(h in s)s.hasOwnProperty(h)&&(o[h]=s[h]);else n.punycode=s}(r)})),L=function(e){return"string"==typeof e},N=function(e){return"object"==typeof e&&null!==e},S=function(e){return null===e},U=function(e){return null==e};function q(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var Z=function(e,t,n,r){t=t||"&",n=n||"=";var o={};if("string"!=typeof e||0===e.length)return o;var i=/\+/g;e=e.split(t);var a=1e3;r&&"number"==typeof r.maxKeys&&(a=r.maxKeys);var s=e.length;a>0&&s>a&&(s=a);for(var h=0;h<s;++h){var f,l,u,c,p=e[h].replace(i,"%20"),d=p.indexOf(n);d>=0?(f=p.substr(0,d),l=p.substr(d+1)):(f=p,l=""),u=decodeURIComponent(f),c=decodeURIComponent(l),q(o,u)?Array.isArray(o[u])?o[u].push(c):o[u]=[o[u],c]:o[u]=c}return o},F=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}},z=function(e,t,n,r){return t=t||"&",n=n||"=",null===e&&(e=void 0),"object"==typeof e?Object.keys(e).map((function(r){var o=encodeURIComponent(F(r))+n;return Array.isArray(e[r])?e[r].map((function(e){return o+encodeURIComponent(F(e))})).join(t):o+encodeURIComponent(F(e[r]))})).join(t):r?encodeURIComponent(F(r))+n+encodeURIComponent(F(e)):""},B=o((function(e,t){t.decode=t.parse=Z,t.encode=t.stringify=z})),W=ae,$=function(e,t){return ae(e,!1,!0).resolve(t)},X=function(e){L(e)&&(e=ae(e));if(!(e instanceof J))return J.prototype.format.call(e);return e.format()};function J(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var H=/^([a-z0-9.+-]+:)/i,G=/:[0-9]*$/,Y=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,K=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),V=["'"].concat(K),Q=["%","/","?",";","#"].concat(V),ee=["/","?","#"],te=/^[+a-z0-9A-Z_-]{0,63}$/,ne=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,re={javascript:!0,"javascript:":!0},oe={javascript:!0,"javascript:":!0},ie={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function ae(e,t,n){if(e&&N(e)&&e instanceof J)return e;var r=new J;return r.parse(e,t,n),r}J.prototype.parse=function(e,t,n){if(!L(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var r=e.indexOf("?"),o=-1!==r&&r<e.indexOf("#")?"?":"#",i=e.split(o);i[0]=i[0].replace(/\\/g,"/");var a=e=i.join(o);if(a=a.trim(),!n&&1===e.split("#").length){var s=Y.exec(a);if(s)return this.path=a,this.href=a,this.pathname=s[1],s[2]?(this.search=s[2],this.query=t?B.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var h=H.exec(a);if(h){var f=(h=h[0]).toLowerCase();this.protocol=f,a=a.substr(h.length)}if(n||h||a.match(/^\/\/[^@\/]+@[^@\/]+/)){var l="//"===a.substr(0,2);!l||h&&oe[h]||(a=a.substr(2),this.slashes=!0)}if(!oe[h]&&(l||h&&!ie[h])){for(var u,c,p=-1,d=0;d<ee.length;d++){-1!==(v=a.indexOf(ee[d]))&&(-1===p||v<p)&&(p=v)}-1!==(c=-1===p?a.lastIndexOf("@"):a.lastIndexOf("@",p))&&(u=a.slice(0,c),a=a.slice(c+1),this.auth=decodeURIComponent(u)),p=-1;for(d=0;d<Q.length;d++){var v;-1!==(v=a.indexOf(Q[d]))&&(-1===p||v<p)&&(p=v)}-1===p&&(p=a.length),this.host=a.slice(0,p),a=a.slice(p),this.parseHost(),this.hostname=this.hostname||"";var g="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!g)for(var x=this.hostname.split(/\./),y=(d=0,x.length);d<y;d++){var m=x[d];if(m&&!m.match(te)){for(var b="",w=0,A=m.length;w<A;w++)m.charCodeAt(w)>127?b+="x":b+=m[w];if(!b.match(te)){var E=x.slice(0,d),C=x.slice(d+1),_=m.match(ne);_&&(E.push(_[1]),C.unshift(_[2])),C.length&&(a="/"+C.join(".")+a),this.hostname=E.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),g||(this.hostname=T.toASCII(this.hostname));var O=this.port?":"+this.port:"",P=this.hostname||"";this.host=P+O,this.href+=this.host,g&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==a[0]&&(a="/"+a))}if(!re[f])for(d=0,y=V.length;d<y;d++){var k=V[d];if(-1!==a.indexOf(k)){var I=encodeURIComponent(k);I===k&&(I=escape(k)),a=a.split(k).join(I)}}var D=a.indexOf("#");-1!==D&&(this.hash=a.substr(D),a=a.slice(0,D));var M=a.indexOf("?");if(-1!==M?(this.search=a.substr(M),this.query=a.substr(M+1),t&&(this.query=B.parse(this.query)),a=a.slice(0,M)):t&&(this.search="",this.query={}),a&&(this.pathname=a),ie[f]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){O=this.pathname||"";var R=this.search||"";this.path=O+R}return this.href=this.format(),this},J.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",n=this.pathname||"",r=this.hash||"",o=!1,i="";this.host?o=e+this.host:this.hostname&&(o=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(o+=":"+this.port)),this.query&&N(this.query)&&Object.keys(this.query).length&&(i=B.stringify(this.query));var a=this.search||i&&"?"+i||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||ie[t])&&!1!==o?(o="//"+(o||""),n&&"/"!==n.charAt(0)&&(n="/"+n)):o||(o=""),r&&"#"!==r.charAt(0)&&(r="#"+r),a&&"?"!==a.charAt(0)&&(a="?"+a),t+o+(n=n.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(a=a.replace("#","%23"))+r},J.prototype.resolve=function(e){return this.resolveObject(ae(e,!1,!0)).format()},J.prototype.resolveObject=function(e){if(L(e)){var t=new J;t.parse(e,!1,!0),e=t}for(var n=new J,r=Object.keys(this),o=0;o<r.length;o++){var i=r[o];n[i]=this[i]}if(n.hash=e.hash,""===e.href)return n.href=n.format(),n;if(e.slashes&&!e.protocol){for(var a=Object.keys(e),s=0;s<a.length;s++){var h=a[s];"protocol"!==h&&(n[h]=e[h])}return ie[n.protocol]&&n.hostname&&!n.pathname&&(n.path=n.pathname="/"),n.href=n.format(),n}if(e.protocol&&e.protocol!==n.protocol){if(!ie[e.protocol]){for(var f=Object.keys(e),l=0;l<f.length;l++){var u=f[l];n[u]=e[u]}return n.href=n.format(),n}if(n.protocol=e.protocol,e.host||oe[e.protocol])n.pathname=e.pathname;else{for(var c=(e.pathname||"").split("/");c.length&&!(e.host=c.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==c[0]&&c.unshift(""),c.length<2&&c.unshift(""),n.pathname=c.join("/")}if(n.search=e.search,n.query=e.query,n.host=e.host||"",n.auth=e.auth,n.hostname=e.hostname||e.host,n.port=e.port,n.pathname||n.search){var p=n.pathname||"",d=n.search||"";n.path=p+d}return n.slashes=n.slashes||e.slashes,n.href=n.format(),n}var v=n.pathname&&"/"===n.pathname.charAt(0),g=e.host||e.pathname&&"/"===e.pathname.charAt(0),x=g||v||n.host&&e.pathname,y=x,m=n.pathname&&n.pathname.split("/")||[],b=(c=e.pathname&&e.pathname.split("/")||[],n.protocol&&!ie[n.protocol]);if(b&&(n.hostname="",n.port=null,n.host&&(""===m[0]?m[0]=n.host:m.unshift(n.host)),n.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===c[0]?c[0]=e.host:c.unshift(e.host)),e.host=null),x=x&&(""===c[0]||""===m[0])),g)n.host=e.host||""===e.host?e.host:n.host,n.hostname=e.hostname||""===e.hostname?e.hostname:n.hostname,n.search=e.search,n.query=e.query,m=c;else if(c.length)m||(m=[]),m.pop(),m=m.concat(c),n.search=e.search,n.query=e.query;else if(!U(e.search)){if(b)n.hostname=n.host=m.shift(),(_=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=_.shift(),n.host=n.hostname=_.shift());return n.search=e.search,n.query=e.query,S(n.pathname)&&S(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.href=n.format(),n}if(!m.length)return n.pathname=null,n.search?n.path="/"+n.search:n.path=null,n.href=n.format(),n;for(var w=m.slice(-1)[0],A=(n.host||e.host||m.length>1)&&("."===w||".."===w)||""===w,E=0,C=m.length;C>=0;C--)"."===(w=m[C])?m.splice(C,1):".."===w?(m.splice(C,1),E++):E&&(m.splice(C,1),E--);if(!x&&!y)for(;E--;E)m.unshift("..");!x||""===m[0]||m[0]&&"/"===m[0].charAt(0)||m.unshift(""),A&&"/"!==m.join("/").substr(-1)&&m.push("");var _,O=""===m[0]||m[0]&&"/"===m[0].charAt(0);b&&(n.hostname=n.host=O?"":m.length?m.shift():"",(_=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=_.shift(),n.host=n.hostname=_.shift()));return(x=x||n.host&&m.length)&&!O&&m.unshift(""),m.length?n.pathname=m.join("/"):(n.pathname=null,n.path=null),S(n.pathname)&&S(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.auth=e.auth||n.auth,n.slashes=n.slashes||e.slashes,n.href=n.format(),n},J.prototype.parseHost=function(){var e=this.host,t=G.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};var se={parse:W,format:X,resolve:$};function he(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function fe(e){return e.split("?")[0].split("#")[0]}var le={toPosix:function(e){return t="\\",n="/",e.replace(new RegExp(t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g"),n);var t,n},isUrl:function(e){return/^https?:/.test(this.toPosix(e))},isDataUrl:function(e){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(e)},hasProtocol:function(e){return/^[^/:]+:\//.test(this.toPosix(e))},getProtocol:function(e){he(e),e=this.toPosix(e);var t="",n=/^file:\/\/\//.exec(e),r=/^[^/:]+:\/\//.exec(e),o=/^[^/:]+:\//.exec(e);if(n||r||o){var i=(null==n?void 0:n[0])||(null==r?void 0:r[0])||(null==o?void 0:o[0]);t=i,e=e.slice(i.length)}return t},toAbsolute:function(e,n,r){if(this.isDataUrl(e))return e;var o=fe(this.toPosix(null!=n?n:t.settings.ADAPTER.getBaseUrl())),i=fe(this.toPosix(null!=r?r:this.rootname(o)));return he(e),(e=this.toPosix(e)).startsWith("/")?le.join(i,e.slice(1)):this.isAbsolute(e)?e:this.join(o,e)},normalize:function(e){if(he(e=this.toPosix(e)),0===e.length)return".";var t="",n=e.startsWith("/");this.hasProtocol(e)&&(t=this.rootname(e),e=e.slice(t.length));var r=e.endsWith("/");return(e=function(e,t){for(var n,r="",o=0,i=-1,a=0,s=0;s<=e.length;++s){if(s<e.length)n=e.charCodeAt(s);else{if(47===n)break;n=47}if(47===n){if(i===s-1||1===a);else if(i!==s-1&&2===a){if(r.length<2||2!==o||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var h=r.lastIndexOf("/");if(h!==r.length-1){-1===h?(r="",o=0):o=(r=r.slice(0,h)).length-1-r.lastIndexOf("/"),i=s,a=0;continue}}else if(2===r.length||1===r.length){r="",o=0,i=s,a=0;continue}t&&(r.length>0?r+="/..":r="..",o=2)}else r.length>0?r+="/"+e.slice(i+1,s):r=e.slice(i+1,s),o=s-i-1;i=s,a=0}else 46===n&&-1!==a?++a:a=-1}return r}(e,!1)).length>0&&r&&(e+="/"),n?"/"+e:t+e},isAbsolute:function(e){return he(e),e=this.toPosix(e),!!this.hasProtocol(e)||e.startsWith("/")},join:function(){for(var e,t,n=arguments,r=[],o=0;o<arguments.length;o++)r[o]=n[o];if(0===r.length)return".";for(var i=0;i<r.length;++i){var a=r[i];if(he(a),a.length>0)if(void 0===t)t=a;else{var s=null!==(e=r[i-1])&&void 0!==e?e:"";this.extname(s)?t+="/../"+a:t+="/"+a}}return void 0===t?".":this.normalize(t)},dirname:function(e){if(he(e),0===e.length)return".";for(var t=(e=this.toPosix(e)).charCodeAt(0),n=47===t,r=-1,o=!0,i=this.getProtocol(e),a=e,s=(e=e.slice(i.length)).length-1;s>=1;--s)if(47===(t=e.charCodeAt(s))){if(!o){r=s;break}}else o=!1;return-1===r?n?"/":this.isUrl(a)?i+e:i:n&&1===r?"//":i+e.slice(0,r)},rootname:function(e){he(e);var t="";if(t=(e=this.toPosix(e)).startsWith("/")?"/":this.getProtocol(e),this.isUrl(e)){var n=e.indexOf("/",t.length);(t=-1!==n?e.slice(0,n):e).endsWith("/")||(t+="/")}return t},basename:function(e,t){he(e),t&&he(t),e=this.toPosix(e);var n,r=0,o=-1,i=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var a=t.length-1,s=-1;for(n=e.length-1;n>=0;--n){var h=e.charCodeAt(n);if(47===h){if(!i){r=n+1;break}}else-1===s&&(i=!1,s=n+1),a>=0&&(h===t.charCodeAt(a)?-1==--a&&(o=n):(a=-1,o=s))}return r===o?o=s:-1===o&&(o=e.length),e.slice(r,o)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!i){r=n+1;break}}else-1===o&&(i=!1,o=n+1);return-1===o?"":e.slice(r,o)},extname:function(e){he(e);for(var t=-1,n=0,r=-1,o=!0,i=0,a=(e=this.toPosix(e)).length-1;a>=0;--a){var s=e.charCodeAt(a);if(47!==s)-1===r&&(o=!1,r=a+1),46===s?-1===t?t=a:1!==i&&(i=1):-1!==t&&(i=-1);else if(!o){n=a+1;break}}return-1===t||-1===r||0===i||1===i&&t===r-1&&t===n+1?"":e.slice(t,r)},parse:function(e){he(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var n,r=(e=this.toPosix(e)).charCodeAt(0),o=this.isAbsolute(e);t.root=this.rootname(e),n=o||this.hasProtocol(e)?1:0;for(var i=-1,a=0,s=-1,h=!0,f=e.length-1,l=0;f>=n;--f)if(47!==(r=e.charCodeAt(f)))-1===s&&(h=!1,s=f+1),46===r?-1===i?i=f:1!==l&&(l=1):-1!==i&&(l=-1);else if(!h){a=f+1;break}return-1===i||-1===s||0===l||1===l&&i===s-1&&i===a+1?-1!==s&&(t.base=t.name=0===a&&o?e.slice(1,s):e.slice(a,s)):(0===a&&o?(t.name=e.slice(1,i),t.base=e.slice(1,s)):(t.name=e.slice(a,i),t.base=e.slice(a,s)),t.ext=e.slice(i,s)),t.dir=this.dirname(e),t},sep:"/",delimiter:":"};t.settings.RETINA_PREFIX=/@([0-9\.]+)x/,t.settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT=!1;var ue,ce=!1;var pe={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};var de=function(){for(var e=[],t=[],r=0;r<32;r++)e[r]=r,t[r]=r;e[n.BLEND_MODES.NORMAL_NPM]=n.BLEND_MODES.NORMAL,e[n.BLEND_MODES.ADD_NPM]=n.BLEND_MODES.ADD,e[n.BLEND_MODES.SCREEN_NPM]=n.BLEND_MODES.SCREEN,t[n.BLEND_MODES.NORMAL]=n.BLEND_MODES.NORMAL_NPM,t[n.BLEND_MODES.ADD]=n.BLEND_MODES.ADD_NPM,t[n.BLEND_MODES.SCREEN]=n.BLEND_MODES.SCREEN_NPM;var o=[];return o.push(t),o.push(e),o}();function ve(e){if(4===e.BYTES_PER_ELEMENT)return e instanceof Float32Array?"Float32Array":e instanceof Uint32Array?"Uint32Array":"Int32Array";if(2===e.BYTES_PER_ELEMENT){if(e instanceof Uint16Array)return"Uint16Array"}else if(1===e.BYTES_PER_ELEMENT&&e instanceof Uint8Array)return"Uint8Array";return null}var ge={Float32Array:Float32Array,Uint32Array:Uint32Array,Int32Array:Int32Array,Uint8Array:Uint8Array};var xe=0;var ye={};var me=Object.create(null),be=Object.create(null);var we=function(){function e(e,n,r){this.canvas=t.settings.ADAPTER.createCanvas(),this.context=this.canvas.getContext("2d"),this.resolution=r||t.settings.RESOLUTION,this.resize(e,n)}return e.prototype.clear=function(){this.context.setTransform(1,0,0,1,0,0),this.context.clearRect(0,0,this.canvas.width,this.canvas.height)},e.prototype.resize=function(e,t){this.canvas.width=Math.round(e*this.resolution),this.canvas.height=Math.round(t*this.resolution)},e.prototype.destroy=function(){this.context=null,this.canvas=null},Object.defineProperty(e.prototype,"width",{get:function(){return this.canvas.width},set:function(e){this.canvas.width=Math.round(e)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this.canvas.height},set:function(e){this.canvas.height=Math.round(e)},enumerable:!1,configurable:!0}),e}();var Ae,Ee=/^\s*data:(?:([\w-]+)\/([\w+.-]+))?(?:;charset=([\w-]+))?(?:;(base64))?,(.*)/i;return Object.defineProperty(e,"isMobile",{enumerable:!0,get:function(){return t.isMobile}}),e.BaseTextureCache=be,e.CanvasRenderTarget=we,e.DATA_URI=Ee,e.EventEmitter=i,e.ProgramCache={},e.TextureCache=me,e.clearTextureCache=function(){var e;for(e in me)delete me[e];for(e in be)delete be[e]},e.correctBlendMode=function(e,t){return de[t?1:0][e]},e.createIndicesForQuads=function(e,t){void 0===t&&(t=null);var n=6*e;if((t=t||new Uint16Array(n)).length!==n)throw new Error("Out buffer length is incorrect, got "+t.length+" and expected "+n);for(var r=0,o=0;r<n;r+=6,o+=4)t[r+0]=o+0,t[r+1]=o+1,t[r+2]=o+2,t[r+3]=o+0,t[r+4]=o+2,t[r+5]=o+3;return t},e.decomposeDataUri=function(e){var t=Ee.exec(e);if(t)return{mediaType:t[1]?t[1].toLowerCase():void 0,subType:t[2]?t[2].toLowerCase():void 0,charset:t[3]?t[3].toLowerCase():void 0,encoding:t[4]?t[4].toLowerCase():void 0,data:t[5]}},e.deprecation=function(e,t,n){if(void 0===n&&(n=3),!ye[t]){var r=(new Error).stack;void 0===r?console.warn("PixiJS Deprecation Warning: ",t+"\nDeprecated since v"+e):(r=r.split("\n").splice(n).join("\n"),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",t+"\nDeprecated since v"+e),console.warn(r),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",t+"\nDeprecated since v"+e),console.warn(r))),ye[t]=!0}},e.destroyTextureCache=function(){var e;for(e in me)me[e].destroy();for(e in be)be[e].destroy()},e.determineCrossOrigin=function(e,t){if(void 0===t&&(t=globalThis.location),0===e.indexOf("data:"))return"";t=t||globalThis.location,Ae||(Ae=document.createElement("a")),Ae.href=e;var n=se.parse(Ae.href),r=!n.port&&""===t.port||n.port===t.port;return n.hostname===t.hostname&&r&&n.protocol===t.protocol?"":"anonymous"},e.earcut=a,e.getBufferType=ve,e.getResolutionOfUrl=function(e,n){var r=t.settings.RETINA_PREFIX.exec(e);return r?parseFloat(r[1]):void 0!==n?n:1},e.hex2rgb=function(e,t){return void 0===t&&(t=[]),t[0]=(e>>16&255)/255,t[1]=(e>>8&255)/255,t[2]=(255&e)/255,t},e.hex2string=function(e){var t=e.toString(16);return"#"+(t="000000".substring(0,6-t.length)+t)},e.interleaveTypedArrays=function(e,t){for(var n=0,r=0,o={},i=0;i<e.length;i++)r+=t[i],n+=e[i].length;var a=new ArrayBuffer(4*n),s=null,h=0;for(i=0;i<e.length;i++){var f=t[i],l=e[i],u=ve(l);o[u]||(o[u]=new ge[u](a)),s=o[u];for(var c=0;c<l.length;c++){s[(c/f|0)*r+h+c%f]=l[c]}h+=f}return new Float32Array(a)},e.isPow2=function(e){return!(e&e-1||!e)},e.isWebGLSupported=function(){return void 0===ue&&(ue=function(){var e={stencil:!0,failIfMajorPerformanceCaveat:t.settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT};try{if(!t.settings.ADAPTER.getWebGLRenderingContext())return!1;var n=t.settings.ADAPTER.createCanvas(),r=n.getContext("webgl",e)||n.getContext("experimental-webgl",e),o=!(!r||!r.getContextAttributes().stencil);if(r){var i=r.getExtension("WEBGL_lose_context");i&&i.loseContext()}return r=null,o}catch(e){return!1}}()),ue},e.log2=function(e){var t=(e>65535?1:0)<<4,n=((e>>>=t)>255?1:0)<<3;return t|=n,t|=n=((e>>>=n)>15?1:0)<<2,(t|=n=((e>>>=n)>3?1:0)<<1)|(e>>>=n)>>1},e.nextPow2=function(e){return e+=0===e?1:0,--e,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,(e|=e>>>16)+1},e.path=le,e.premultiplyBlendMode=de,e.premultiplyRgba=function(e,t,n,r){return n=n||new Float32Array(4),r||void 0===r?(n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t):(n[0]=e[0],n[1]=e[1],n[2]=e[2]),n[3]=t,n},e.premultiplyTint=function(e,t){if(1===t)return(255*t<<24)+e;if(0===t)return 0;var n=e>>16&255,r=e>>8&255,o=255&e;return(255*t<<24)+((n=n*t+.5|0)<<16)+((r=r*t+.5|0)<<8)+(o=o*t+.5|0)},e.premultiplyTintToRgba=function(e,t,n,r){return(n=n||new Float32Array(4))[0]=(e>>16&255)/255,n[1]=(e>>8&255)/255,n[2]=(255&e)/255,(r||void 0===r)&&(n[0]*=t,n[1]*=t,n[2]*=t),n[3]=t,n},e.removeItems=function(e,t,n){var r,o=e.length;if(!(t>=o||0===n)){var i=o-(n=t+n>o?o-t:n);for(r=t;r<i;++r)e[r]=e[r+n];e.length=i}},e.rgb2hex=function(e){return(255*e[0]<<16)+(255*e[1]<<8)+(255*e[2]|0)},e.sayHello=function(e){var n;if(!ce){if(t.settings.ADAPTER.getNavigator().userAgent.toLowerCase().indexOf("chrome")>-1){var r=["\n %c %c %c PixiJS 6.5.10 - ✰ "+e+" ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \n\n","background: #ff66a5; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff66a5; background: #030307; padding:5px 0;","background: #ff66a5; padding:5px 0;","background: #ffc3dc; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;"];(n=globalThis.console).log.apply(n,r)}else globalThis.console&&globalThis.console.log("PixiJS 6.5.10 - "+e+" - http://www.pixijs.com/");ce=!0}},e.sign=function(e){return 0===e?0:e<0?-1:1},e.skipHello=function(){ce=!0},e.string2hex=function(e){return"string"==typeof e&&"#"===(e=pe[e.toLowerCase()]||e)[0]&&(e=e.slice(1)),parseInt(e,16)},e.trimCanvas=function(e){var t,n,r,o=e.width,i=e.height,a=e.getContext("2d",{willReadFrequently:!0}),s=a.getImageData(0,0,o,i).data,h=s.length,f={top:null,left:null,right:null,bottom:null},l=null;for(t=0;t<h;t+=4)0!==s[t+3]&&(n=t/4%o,r=~~(t/4/o),null===f.top&&(f.top=r),(null===f.left||n<f.left)&&(f.left=n),(null===f.right||f.right<n)&&(f.right=n+1),(null===f.bottom||f.bottom<r)&&(f.bottom=r));return null!==f.top&&(o=f.right-f.left,i=f.bottom-f.top+1,l=a.getImageData(f.left,f.top,o,i)),{height:i,width:o,data:l}},e.uid=function(){return++xe},e.url=se,Object.defineProperty(e,"__esModule",{value:!0}),e}({},PIXI,PIXI);Object.assign(this.PIXI.utils,_pixi_utils);
//# sourceMappingURL=utils.min.js.map
