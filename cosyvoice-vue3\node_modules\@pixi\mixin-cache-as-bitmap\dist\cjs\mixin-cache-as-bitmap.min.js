/*!
 * @pixi/mixin-cache-as-bitmap - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/mixin-cache-as-bitmap is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("@pixi/core"),a=require("@pixi/sprite"),e=require("@pixi/display"),i=require("@pixi/math"),s=require("@pixi/utils"),r=require("@pixi/settings"),n=require("@pixi/constants"),h=new i.Matrix;e.DisplayObject.prototype._cacheAsBitmap=!1,e.DisplayObject.prototype._cacheData=null,e.DisplayObject.prototype._cacheAsBitmapResolution=null,e.DisplayObject.prototype._cacheAsBitmapMultisample=n.MSAA_QUALITY.NONE;var o=function(){this.textureCacheId=null,this.originalRender=null,this.originalRenderCanvas=null,this.originalCalculateBounds=null,this.originalGetLocalBounds=null,this.originalUpdateTransform=null,this.originalDestroy=null,this.originalMask=null,this.originalFilterArea=null,this.originalContainsPoint=null,this.sprite=null};Object.defineProperties(e.DisplayObject.prototype,{cacheAsBitmapResolution:{get:function(){return this._cacheAsBitmapResolution},set:function(t){t!==this._cacheAsBitmapResolution&&(this._cacheAsBitmapResolution=t,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmapMultisample:{get:function(){return this._cacheAsBitmapMultisample},set:function(t){t!==this._cacheAsBitmapMultisample&&(this._cacheAsBitmapMultisample=t,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmap:{get:function(){return this._cacheAsBitmap},set:function(t){var a;this._cacheAsBitmap!==t&&(this._cacheAsBitmap=t,t?(this._cacheData||(this._cacheData=new o),(a=this._cacheData).originalRender=this.render,a.originalRenderCanvas=this.renderCanvas,a.originalUpdateTransform=this.updateTransform,a.originalCalculateBounds=this.calculateBounds,a.originalGetLocalBounds=this.getLocalBounds,a.originalDestroy=this.destroy,a.originalContainsPoint=this.containsPoint,a.originalMask=this._mask,a.originalFilterArea=this.filterArea,this.render=this._renderCached,this.renderCanvas=this._renderCachedCanvas,this.destroy=this._cacheAsBitmapDestroy):((a=this._cacheData).sprite&&this._destroyCachedDisplayObject(),this.render=a.originalRender,this.renderCanvas=a.originalRenderCanvas,this.calculateBounds=a.originalCalculateBounds,this.getLocalBounds=a.originalGetLocalBounds,this.destroy=a.originalDestroy,this.updateTransform=a.originalUpdateTransform,this.containsPoint=a.originalContainsPoint,this._mask=a.originalMask,this.filterArea=a.originalFilterArea))}}}),e.DisplayObject.prototype._renderCached=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObject(t),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._render(t))},e.DisplayObject.prototype._initCachedDisplayObject=function(e){var i;if(!this._cacheData||!this._cacheData.sprite){var n=this.alpha;this.alpha=1,e.batch.flush();var o=this.getLocalBounds(null,!0).clone();if(this.filters&&this.filters.length){var c=this.filters[0].padding;o.pad(c)}o.ceil(r.settings.RESOLUTION);var l=e.renderTexture.current,p=e.renderTexture.sourceFrame.clone(),d=e.renderTexture.destinationFrame.clone(),u=e.projection.transform,m=t.RenderTexture.create({width:o.width,height:o.height,resolution:this.cacheAsBitmapResolution||e.resolution,multisample:null!==(i=this.cacheAsBitmapMultisample)&&void 0!==i?i:e.multisample}),_="cacheAsBitmap_"+s.uid();this._cacheData.textureCacheId=_,t.BaseTexture.addToCache(m.baseTexture,_),t.Texture.addToCache(m,_);var f=this.transform.localTransform.copyTo(h).invert().translate(-o.x,-o.y);this.render=this._cacheData.originalRender,e.render(this,{renderTexture:m,clear:!0,transform:f,skipUpdateTransform:!1}),e.framebuffer.blit(),e.projection.transform=u,e.renderTexture.bind(l,p,d),this.render=this._renderCached,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=n;var D=new a.Sprite(m);D.transform.worldTransform=this.transform.worldTransform,D.anchor.x=-o.x/o.width,D.anchor.y=-o.y/o.height,D.alpha=n,D._bounds=this._bounds,this._cacheData.sprite=D,this.transform._parentID=-1,this.parent?this.updateTransform():(this.enableTempParent(),this.updateTransform(),this.disableTempParent(null)),this.containsPoint=D.containsPoint.bind(D)}},e.DisplayObject.prototype._renderCachedCanvas=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObjectCanvas(t),this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._renderCanvas(t))},e.DisplayObject.prototype._initCachedDisplayObjectCanvas=function(e){if(!this._cacheData||!this._cacheData.sprite){var i=this.getLocalBounds(null,!0),n=this.alpha;this.alpha=1;var o=e.context,c=e._projTransform;i.ceil(r.settings.RESOLUTION);var l=t.RenderTexture.create({width:i.width,height:i.height}),p="cacheAsBitmap_"+s.uid();this._cacheData.textureCacheId=p,t.BaseTexture.addToCache(l.baseTexture,p),t.Texture.addToCache(l,p);var d=h;this.transform.localTransform.copyTo(d),d.invert(),d.tx-=i.x,d.ty-=i.y,this.renderCanvas=this._cacheData.originalRenderCanvas,e.render(this,{renderTexture:l,clear:!0,transform:d,skipUpdateTransform:!1}),e.context=o,e._projTransform=c,this.renderCanvas=this._renderCachedCanvas,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=n;var u=new a.Sprite(l);u.transform.worldTransform=this.transform.worldTransform,u.anchor.x=-i.x/i.width,u.anchor.y=-i.y/i.height,u.alpha=n,u._bounds=this._bounds,this._cacheData.sprite=u,this.transform._parentID=-1,this.parent?this.updateTransform():(this.parent=e._tempDisplayObjectParent,this.updateTransform(),this.parent=null),this.containsPoint=u.containsPoint.bind(u)}},e.DisplayObject.prototype._calculateCachedBounds=function(){this._bounds.clear(),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite._calculateBounds(),this._bounds.updateID=this._boundsID},e.DisplayObject.prototype._getCachedLocalBounds=function(){return this._cacheData.sprite.getLocalBounds(null)},e.DisplayObject.prototype._destroyCachedDisplayObject=function(){this._cacheData.sprite._texture.destroy(!0),this._cacheData.sprite=null,t.BaseTexture.removeFromCache(this._cacheData.textureCacheId),t.Texture.removeFromCache(this._cacheData.textureCacheId),this._cacheData.textureCacheId=null},e.DisplayObject.prototype._cacheAsBitmapDestroy=function(t){this.cacheAsBitmap=!1,this.destroy(t)},exports.CacheData=o;
//# sourceMappingURL=mixin-cache-as-bitmap.min.js.map
