{"version": 3, "file": "utils.min.js", "sources": ["../../src/url.ts", "../../src/path.ts", "../../src/settings.ts", "../../src/browser/hello.ts", "../../src/browser/isWebGLSupported.ts", "../../src/color/premultiply.ts", "../../src/data/getBufferType.ts", "../../src/data/interleaveTypedArrays.ts", "../../src/data/uid.ts", "../../src/logging/deprecation.ts", "../../src/media/caches.ts", "../../src/media/CanvasRenderTarget.ts", "../../src/const.ts", "../../src/network/determineCrossOrigin.ts", "../../src/data/createIndicesForQuads.ts", "../../src/network/decomposeDataUri.ts", "../../src/network/getResolutionOfUrl.ts", "../../src/color/hex.ts", "../../src/data/pow2.ts", "../../src/data/removeItems.ts", "../../src/data/sign.ts", "../../src/media/trimCanvas.ts"], "sourcesContent": ["/**\n * This file contains redeclared types for Node `url` and `querystring` modules. These modules\n * don't provide their own typings but instead are a part of the full Node typings. The purpose of\n * this file is to redeclare the required types to avoid having the whole Node types as a\n * dependency.\n */\n\nimport { parse as _parse, format as _format, resolve as _resolve } from 'url';\n\ninterface ParsedUrlQuery\n{\n    [key: string]: string | string[];\n}\n\ninterface ParsedUrlQueryInput\n{\n    [key: string]: unknown;\n}\n\ninterface UrlObjectCommon\n{\n    auth?: string;\n    hash?: string;\n    host?: string;\n    hostname?: string;\n    href?: string;\n    path?: string;\n    pathname?: string;\n    protocol?: string;\n    search?: string;\n    slashes?: boolean;\n}\n\n// Input to `url.format`\ninterface UrlObject extends UrlObjectCommon\n{\n    port?: string | number;\n    query?: string | null | ParsedUrlQueryInput;\n}\n\n// Output of `url.parse`\ninterface Url extends UrlObjectCommon\n{\n    port?: string;\n    query?: string | null | ParsedUrlQuery;\n}\n\ninterface UrlWithParsedQuery extends Url\n{\n    query: ParsedUrlQuery;\n}\n\ninterface UrlWithStringQuery extends Url\n{\n    query: string | null;\n}\n\ninterface URLFormatOptions\n{\n    auth?: boolean;\n    fragment?: boolean;\n    search?: boolean;\n    unicode?: boolean;\n}\n\ntype ParseFunction = {\n    (urlStr: string): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: false | undefined, slashesDenoteHost?: boolean): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: true, slashesDenoteHost?: boolean): UrlWithParsedQuery;\n    (urlStr: string, parseQueryString: boolean, slashesDenoteHost?: boolean): Url;\n};\n\ntype FormatFunction = {\n    (URL: URL, options?: URLFormatOptions): string;\n    (urlObject: UrlObject | string): string;\n};\n\ntype ResolveFunction = {\n    (from: string, to: string): string;\n};\n\nexport const url = {\n    parse: _parse as ParseFunction,\n    format: _format as FormatFunction,\n    resolve: _resolve as ResolveFunction,\n};\n", "import { settings } from '@pixi/settings';\n\nfunction assertPath(path: string)\n{\n    if (typeof path !== 'string')\n    {\n        throw new TypeError(`Path must be a string. Received ${JSON.stringify(path)}`);\n    }\n}\n\nfunction removeUrlParams(url: string): string\n{\n    const re = url.split('?')[0];\n\n    return re.split('#')[0];\n}\n\nfunction escapeRegExp(string: string)\n{\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n}\n\nfunction replaceAll(str: string, find: string, replace: string)\n{\n    return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path: string, allowAboveRoot: boolean)\n{\n    let res = '';\n    let lastSegmentLength = 0;\n    let lastSlash = -1;\n    let dots = 0;\n    let code: number;\n\n    for (let i = 0; i <= path.length; ++i)\n    {\n        if (i < path.length)\n        {\n            code = path.charCodeAt(i);\n        }\n        else if (code === 47)\n        {\n            break;\n        }\n        else\n        {\n            code = 47;\n        }\n        if (code === 47)\n        {\n            if (lastSlash === i - 1 || dots === 1)\n            {\n                // NOOP\n            }\n            else if (lastSlash !== i - 1 && dots === 2)\n            {\n                if (\n                    res.length < 2\n                    || lastSegmentLength !== 2\n                    || res.charCodeAt(res.length - 1) !== 46\n                    || res.charCodeAt(res.length - 2) !== 46\n                )\n                {\n                    if (res.length > 2)\n                    {\n                        const lastSlashIndex = res.lastIndexOf('/');\n\n                        if (lastSlashIndex !== res.length - 1)\n                        {\n                            if (lastSlashIndex === -1)\n                            {\n                                res = '';\n                                lastSegmentLength = 0;\n                            }\n                            else\n                            {\n                                res = res.slice(0, lastSlashIndex);\n                                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n                            }\n                            lastSlash = i;\n                            dots = 0;\n                            continue;\n                        }\n                    }\n                    else if (res.length === 2 || res.length === 1)\n                    {\n                        res = '';\n                        lastSegmentLength = 0;\n                        lastSlash = i;\n                        dots = 0;\n                        continue;\n                    }\n                }\n                if (allowAboveRoot)\n                {\n                    if (res.length > 0)\n                    { res += '/..'; }\n                    else\n                    { res = '..'; }\n                    lastSegmentLength = 2;\n                }\n            }\n            else\n            {\n                if (res.length > 0)\n                {\n                    res += `/${path.slice(lastSlash + 1, i)}`;\n                }\n                else\n                {\n                    res = path.slice(lastSlash + 1, i);\n                }\n                lastSegmentLength = i - lastSlash - 1;\n            }\n            lastSlash = i;\n            dots = 0;\n        }\n        else if (code === 46 && dots !== -1)\n        {\n            ++dots;\n        }\n        else\n        {\n            dots = -1;\n        }\n    }\n\n    return res;\n}\n\nexport interface Path\n{\n    toPosix: (path: string) => string;\n    toAbsolute: (url: string, baseUrl?: string, rootUrl?: string) => string;\n    isUrl: (path: string) => boolean;\n    isDataUrl: (path: string) => boolean;\n    hasProtocol: (path: string) => boolean;\n    getProtocol: (path: string) => string;\n    normalize: (path: string) => string;\n    join: (...paths: string[]) => string;\n    isAbsolute: (path: string) => boolean;\n    dirname: (path: string) => string;\n    rootname: (path: string) => string;\n    basename: (path: string, ext?: string) => string;\n    extname: (path: string) => string;\n    parse: (path: string) => { root?: string, dir?: string, base?: string, ext?: string, name?: string };\n    sep: string,\n    delimiter: string\n}\n\nexport const path: Path = {\n    /**\n     * Converts a path to posix format.\n     * @param path - The path to convert to posix\n     */\n    toPosix(path: string) { return replaceAll(path, '\\\\', '/'); },\n    /**\n     * Checks if the path is a URL\n     * @param path - The path to check\n     */\n    isUrl(path: string) { return (/^https?:/).test(this.toPosix(path)); },\n    /**\n     * Checks if the path is a data URL\n     * @param path - The path to check\n     */\n    isDataUrl(path: string)\n    {\n        // eslint-disable-next-line max-len\n        return (/^data:([a-z]+\\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s<>]*?)$/i)\n            .test(path);\n    },\n    /**\n     * Checks if the path has a protocol e.g. http://\n     * This will return true for windows file paths\n     * @param path - The path to check\n     */\n    hasProtocol(path: string) { return (/^[^/:]+:\\//).test(this.toPosix(path)); },\n    /**\n     * Returns the protocol of the path e.g. http://, C:/, file:///\n     * @param path - The path to get the protocol from\n     */\n    getProtocol(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let protocol = '';\n\n        const isFile = (/^file:\\/\\/\\//).exec(path);\n        const isHttp = (/^[^/:]+:\\/\\//).exec(path);\n        const isWindows = (/^[^/:]+:\\//).exec(path);\n\n        if (isFile || isHttp || isWindows)\n        {\n            const arr = isFile?.[0] || isHttp?.[0] || isWindows?.[0];\n\n            protocol = arr;\n            path = path.slice(arr.length);\n        }\n\n        return protocol;\n    },\n\n    /**\n     * Converts URL to an absolute path.\n     * When loading from a Web Worker, we must use absolute paths.\n     * If the URL is already absolute we return it as is\n     * If it's not, we convert it\n     * @param url - The URL to test\n     * @param customBaseUrl - The base URL to use\n     * @param customRootUrl - The root URL to use\n     */\n    toAbsolute(url: string, customBaseUrl?: string, customRootUrl?: string)\n    {\n        if (this.isDataUrl(url)) return url;\n\n        const baseUrl = removeUrlParams(this.toPosix(customBaseUrl ?? settings.ADAPTER.getBaseUrl()));\n        const rootUrl = removeUrlParams(this.toPosix(customRootUrl ?? this.rootname(baseUrl)));\n\n        assertPath(url);\n        url = this.toPosix(url);\n\n        // root relative url\n        if (url.startsWith('/'))\n        {\n            return path.join(rootUrl, url.slice(1));\n        }\n\n        const absolutePath = this.isAbsolute(url) ? url : this.join(baseUrl, url);\n\n        return absolutePath;\n    },\n\n    /**\n     * Normalizes the given path, resolving '..' and '.' segments\n     * @param path - The path to normalize\n     */\n    normalize(path: string)\n    {\n        path = this.toPosix(path);\n        assertPath(path);\n\n        if (path.length === 0) return '.';\n\n        let protocol = '';\n        const isAbsolute = path.startsWith('/');\n\n        if (this.hasProtocol(path))\n        {\n            protocol = this.rootname(path);\n            path = path.slice(protocol.length);\n        }\n\n        const trailingSeparator = path.endsWith('/');\n\n        // Normalize the path\n        path = normalizeStringPosix(path, false);\n\n        if (path.length > 0 && trailingSeparator) path += '/';\n        if (isAbsolute) return `/${path}`;\n\n        return protocol + path;\n    },\n\n    /**\n     * Determines if path is an absolute path.\n     * Absolute paths can be urls, data urls, or paths on disk\n     * @param path - The path to test\n     */\n    isAbsolute(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        if (this.hasProtocol(path)) return true;\n\n        return path.startsWith('/');\n    },\n\n    /**\n     * Joins all given path segments together using the platform-specific separator as a delimiter,\n     * then normalizes the resulting path\n     * @param segments - The segments of the path to join\n     */\n    join(...segments: string[])\n    {\n        if (segments.length === 0)\n        { return '.'; }\n        let joined;\n\n        for (let i = 0; i < segments.length; ++i)\n        {\n            const arg = segments[i];\n\n            assertPath(arg);\n            if (arg.length > 0)\n            {\n                if (joined === undefined) joined = arg;\n                else\n                {\n                    const prevArg = segments[i - 1] ?? '';\n\n                    if (this.extname(prevArg))\n                    {\n                        joined += `/../${arg}`;\n                    }\n                    else\n                    {\n                        joined += `/${arg}`;\n                    }\n                }\n            }\n        }\n        if (joined === undefined) { return '.'; }\n\n        return this.normalize(joined);\n    },\n\n    /**\n     * Returns the directory name of a path\n     * @param path - The path to parse\n     */\n    dirname(path: string)\n    {\n        assertPath(path);\n        if (path.length === 0) return '.';\n        path = this.toPosix(path);\n        let code = path.charCodeAt(0);\n        const hasRoot = code === 47;\n        let end = -1;\n        let matchedSlash = true;\n\n        const proto = this.getProtocol(path);\n        const origpath = path;\n\n        path = path.slice(proto.length);\n\n        for (let i = path.length - 1; i >= 1; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                if (!matchedSlash)\n                {\n                    end = i;\n                    break;\n                }\n            }\n            else\n            {\n                // We saw the first non-path separator\n                matchedSlash = false;\n            }\n        }\n\n        // if end is -1 and its a url then we need to add the path back\n        // eslint-disable-next-line no-nested-ternary\n        if (end === -1) return hasRoot ? '/' : this.isUrl(origpath) ? proto + path : proto;\n        if (hasRoot && end === 1) return '//';\n\n        return proto + path.slice(0, end);\n    },\n\n    /**\n     * Returns the root of the path e.g. /, C:/, file:///, http://domain.com/\n     * @param path - The path to parse\n     */\n    rootname(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let root = '';\n\n        if (path.startsWith('/')) root = '/';\n        else\n        {\n            root = this.getProtocol(path);\n        }\n\n        if (this.isUrl(path))\n        {\n            // need to find the first path separator\n            const index = path.indexOf('/', root.length);\n\n            if (index !== -1)\n            {\n                root = path.slice(0, index);\n            }\n            else root = path;\n\n            if (!root.endsWith('/')) root += '/';\n        }\n\n        return root;\n    },\n\n    /**\n     * Returns the last portion of a path\n     * @param path - The path to test\n     * @param ext - Optional extension to remove\n     */\n    basename(path: string, ext?: string)\n    {\n        assertPath(path);\n        if (ext) assertPath(ext);\n\n        path = this.toPosix(path);\n\n        let start = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i: number;\n\n        if (ext !== undefined && ext.length > 0 && ext.length <= path.length)\n        {\n            if (ext.length === path.length && ext === path) return '';\n            let extIdx = ext.length - 1;\n            let firstNonSlashEnd = -1;\n\n            for (i = path.length - 1; i >= 0; --i)\n            {\n                const code = path.charCodeAt(i);\n\n                if (code === 47)\n                {\n                    // If we reached a path separator that was not part of a set of path\n                    // separators at the end of the string, stop now\n                    if (!matchedSlash)\n                    {\n                        start = i + 1;\n                        break;\n                    }\n                }\n                else\n                {\n                    if (firstNonSlashEnd === -1)\n                    {\n                        // We saw the first non-path separator, remember this index in case\n                        // we need it if the extension ends up not matching\n                        matchedSlash = false;\n                        firstNonSlashEnd = i + 1;\n                    }\n                    if (extIdx >= 0)\n                    {\n                        // Try to match the explicit extension\n                        if (code === ext.charCodeAt(extIdx))\n                        {\n                            if (--extIdx === -1)\n                            {\n                                // We matched the extension, so mark this as the end of our path\n                                // component\n                                end = i;\n                            }\n                        }\n                        else\n                        {\n                            // Extension does not match, so our result is the entire path\n                            // component\n                            extIdx = -1;\n                            end = firstNonSlashEnd;\n                        }\n                    }\n                }\n            }\n\n            if (start === end) end = firstNonSlashEnd; else if (end === -1) end = path.length;\n\n            return path.slice(start, end);\n        }\n        for (i = path.length - 1; i >= 0; --i)\n        {\n            if (path.charCodeAt(i) === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    start = i + 1;\n                    break;\n                }\n            }\n            else if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // path component\n                matchedSlash = false;\n                end = i + 1;\n            }\n        }\n\n        if (end === -1) return '';\n\n        return path.slice(start, end);\n    },\n\n    /**\n     * Returns the extension of the path, from the last occurrence of the . (period) character to end of string in the last\n     * portion of the path. If there is no . in the last portion of the path, or if there are no . characters other than\n     * the first character of the basename of path, an empty string is returned.\n     * @param path - The path to parse\n     */\n    extname(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        for (let i = path.length - 1; i >= 0; --i)\n        {\n            const code = path.charCodeAt(i);\n\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            return '';\n        }\n\n        return path.slice(startDot, end);\n    },\n\n    /**\n     * Parses a path into an object containing the 'root', `dir`, `base`, `ext`, and `name` properties.\n     * @param path - The path to parse\n     */\n    parse(path: string)\n    {\n        assertPath(path);\n\n        const ret = { root: '', dir: '', base: '', ext: '', name: '' };\n\n        if (path.length === 0) return ret;\n        path = this.toPosix(path);\n\n        let code = path.charCodeAt(0);\n        const isAbsolute = this.isAbsolute(path);\n        let start: number;\n        const protocol = '';\n\n        ret.root = this.rootname(path);\n\n        if (isAbsolute || this.hasProtocol(path))\n        {\n            start = 1;\n        }\n        else\n        {\n            start = 0;\n        }\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i = path.length - 1;\n\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        // Get non-dir info\n        for (; i >= start; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            if (end !== -1)\n            {\n                if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);\n                else ret.base = ret.name = path.slice(startPart, end);\n            }\n        }\n        else\n        {\n            if (startPart === 0 && isAbsolute)\n            {\n                ret.name = path.slice(1, startDot);\n                ret.base = path.slice(1, end);\n            }\n            else\n            {\n                ret.name = path.slice(startPart, startDot);\n                ret.base = path.slice(startPart, end);\n            }\n            ret.ext = path.slice(startDot, end);\n        }\n\n        ret.dir = this.dirname(path);\n        if (protocol) ret.dir = protocol + ret.dir;\n\n        return ret;\n    },\n\n    sep: '/',\n    delimiter: ':'\n} as Path;\n", "import { settings } from '@pixi/settings';\n\n/**\n * The prefix that denotes a URL is for a retina asset.\n * @static\n * @name RETINA_PREFIX\n * @memberof PIXI.settings\n * @type {RegExp}\n * @default /@([0-9\\.]+)x/\n * @example `@2x`\n */\nsettings.RETINA_PREFIX = /@([0-9\\.]+)x/;\n\n/**\n * Should the `failIfMajorPerformanceCaveat` flag be enabled as a context option used in the `isWebGLSupported` function.\n * If set to true, a WebGL renderer can fail to be created if the browser thinks there could be performance issues when\n * using WebGL.\n *\n * In PixiJS v6 this has changed from true to false by default, to allow WebGL to work in as many scenarios as possible.\n * However, some users may have a poor experience, for example, if a user has a gpu or driver version blacklisted by the\n * browser.\n *\n * If your application requires high performance rendering, you may wish to set this to false.\n * We recommend one of two options if you decide to set this flag to false:\n *\n * 1: Use the `pixi.js-legacy` package, which includes a Canvas renderer as a fallback in case high performance WebGL is\n *    not supported.\n *\n * 2: Call `isWebGLSupported` (which if found in the PIXI.utils package) in your code before attempting to create a PixiJS\n *    renderer, and show an error message to the user if the function returns false, explaining that their device & browser\n *    combination does not support high performance WebGL.\n *    This is a much better strategy than trying to create a PixiJS renderer and finding it then fails.\n * @static\n * @name FAIL_IF_MAJOR_PERFORMANCE_CAVEAT\n * @memberof PIXI.settings\n * @type {boolean}\n * @default false\n */\nsettings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT = false;\n\nexport { settings };\n", "import { settings } from '@pixi/settings';\n\nlet saidHello = false;\nconst VERSION = '$_VERSION';\n\n/**\n * Skips the hello message of renderers that are created after this is run.\n * @function skipHello\n * @memberof PIXI.utils\n */\nexport function skipHello(): void\n{\n    saidHello = true;\n}\n\n/**\n * Logs out the version and renderer information for this running instance of PIXI.\n * If you don't want to see this message you can run `PIXI.utils.skipHello()` before\n * creating your renderer. Keep in mind that doing that will forever make you a jerk face.\n * @static\n * @function sayHello\n * @memberof PIXI.utils\n * @param {string} type - The string renderer type to log.\n */\nexport function sayHello(type: string): void\n{\n    if (saidHello)\n    {\n        return;\n    }\n\n    if (settings.ADAPTER.getNavigator().userAgent.toLowerCase().indexOf('chrome') > -1)\n    {\n        const args = [\n            `\\n %c %c %c PixiJS ${VERSION} - ✰ ${type} ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \\n\\n`,\n            'background: #ff66a5; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'color: #ff66a5; background: #030307; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'background: #ffc3dc; padding:5px 0;',\n            'background: #ff66a5; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n            'color: #ff2424; background: #fff; padding:5px 0;',\n        ];\n\n        globalThis.console.log(...args);\n    }\n    else if (globalThis.console)\n    {\n        globalThis.console.log(`PixiJS ${VERSION} - ${type} - http://www.pixijs.com/`);\n    }\n\n    saidHello = true;\n}\n", "import { settings } from '../settings';\n\nlet supported: boolean | undefined;\n\n/**\n * Helper for checking for WebGL support.\n * @memberof PIXI.utils\n * @function isWebGLSupported\n * @returns {boolean} Is WebGL supported.\n */\nexport function isWebGLSupported(): boolean\n{\n    if (typeof supported === 'undefined')\n    {\n        supported = (function supported(): boolean\n        {\n            const contextOptions = {\n                stencil: true,\n                failIfMajorPerformanceCaveat: settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT,\n            };\n\n            try\n            {\n                if (!settings.ADAPTER.getWebGLRenderingContext())\n                {\n                    return false;\n                }\n\n                const canvas = settings.ADAPTER.createCanvas();\n                let gl = (\n                    canvas.getContext('webgl', contextOptions)\n                    || canvas.getContext('experimental-webgl', contextOptions)\n                ) as WebGLRenderingContext;\n\n                const success = !!(gl && gl.getContextAttributes().stencil);\n\n                if (gl)\n                {\n                    const loseContext = gl.getExtension('WEBGL_lose_context');\n\n                    if (loseContext)\n                    {\n                        loseContext.loseContext();\n                    }\n                }\n\n                gl = null;\n\n                return success;\n            }\n            catch (e)\n            {\n                return false;\n            }\n        })();\n    }\n\n    return supported;\n}\n", "import { BLEND_MODES } from '@pixi/constants';\n\n/**\n * Corrects PixiJS blend, takes premultiplied alpha into account\n * @memberof PIXI.utils\n * @function mapPremultipliedBlendModes\n * @private\n * @returns {Array<number[]>} Mapped modes.\n */\nfunction mapPremultipliedBlendModes(): number[][]\n{\n    const pm = [];\n    const npm = [];\n\n    for (let i = 0; i < 32; i++)\n    {\n        pm[i] = i;\n        npm[i] = i;\n    }\n\n    pm[BLEND_MODES.NORMAL_NPM] = BLEND_MODES.NORMAL;\n    pm[BLEND_MODES.ADD_NPM] = BLEND_MODES.ADD;\n    pm[BLEND_MODES.SCREEN_NPM] = BLEND_MODES.SCREEN;\n\n    npm[BLEND_MODES.NORMAL] = BLEND_MODES.NORMAL_NPM;\n    npm[BLEND_MODES.ADD] = BLEND_MODES.ADD_NPM;\n    npm[BLEND_MODES.SCREEN] = BLEND_MODES.SCREEN_NPM;\n\n    const array: number[][] = [];\n\n    array.push(npm);\n    array.push(pm);\n\n    return array;\n}\n\n/**\n * maps premultiply flag and blendMode to adjusted blendMode\n * @memberof PIXI.utils\n * @constant premultiplyBlendMode\n * @type {Array<number[]>}\n */\nexport const premultiplyBlendMode = mapPremultipliedBlendModes();\n\n/**\n * changes blendMode according to texture format\n * @memberof PIXI.utils\n * @function correctBlendMode\n * @param {number} blendMode - supposed blend mode\n * @param {boolean} premultiplied - whether source is premultiplied\n * @returns {number} true blend mode for this texture\n */\nexport function correctBlendMode(blendMode: number, premultiplied: boolean): number\n{\n    return premultiplyBlendMode[premultiplied ? 1 : 0][blendMode];\n}\n\n/**\n * combines rgb and alpha to out array\n * @memberof PIXI.utils\n * @function premultiplyRgba\n * @param {Float32Array|number[]} rgb - input rgb\n * @param {number} alpha - alpha param\n * @param {Float32Array} [out] - output\n * @param {boolean} [premultiply=true] - do premultiply it\n * @returns {Float32Array} vec4 rgba\n */\nexport function premultiplyRgba(\n    rgb: Float32Array | number[],\n    alpha: number,\n    out?: Float32Array,\n    premultiply?: boolean\n): Float32Array\n{\n    out = out || new Float32Array(4);\n    if (premultiply || premultiply === undefined)\n    {\n        out[0] = rgb[0] * alpha;\n        out[1] = rgb[1] * alpha;\n        out[2] = rgb[2] * alpha;\n    }\n    else\n    {\n        out[0] = rgb[0];\n        out[1] = rgb[1];\n        out[2] = rgb[2];\n    }\n    out[3] = alpha;\n\n    return out;\n}\n\n/**\n * premultiplies tint\n * @memberof PIXI.utils\n * @function premultiplyTint\n * @param {number} tint - integer RGB\n * @param {number} alpha - floating point alpha (0.0-1.0)\n * @returns {number} tint multiplied by alpha\n */\nexport function premultiplyTint(tint: number, alpha: number): number\n{\n    if (alpha === 1.0)\n    {\n        return (alpha * 255 << 24) + tint;\n    }\n    if (alpha === 0.0)\n    {\n        return 0;\n    }\n    let R = ((tint >> 16) & 0xFF);\n    let G = ((tint >> 8) & 0xFF);\n    let B = (tint & 0xFF);\n\n    R = ((R * alpha) + 0.5) | 0;\n    G = ((G * alpha) + 0.5) | 0;\n    B = ((B * alpha) + 0.5) | 0;\n\n    return (alpha * 255 << 24) + (R << 16) + (G << 8) + B;\n}\n\n/**\n * converts integer tint and float alpha to vec4 form, premultiplies by default\n * @memberof PIXI.utils\n * @function premultiplyTintToRgba\n * @param {number} tint - input tint\n * @param {number} alpha - alpha param\n * @param {Float32Array} [out] - output\n * @param {boolean} [premultiply=true] - do premultiply it\n * @returns {Float32Array} vec4 rgba\n */\nexport function premultiplyTintToRgba(tint: number, alpha: number, out: Float32Array, premultiply?: boolean): Float32Array\n{\n    out = out || new Float32Array(4);\n    out[0] = ((tint >> 16) & 0xFF) / 255.0;\n    out[1] = ((tint >> 8) & 0xFF) / 255.0;\n    out[2] = (tint & 0xFF) / 255.0;\n    if (premultiply || premultiply === undefined)\n    {\n        out[0] *= alpha;\n        out[1] *= alpha;\n        out[2] *= alpha;\n    }\n    out[3] = alpha;\n\n    return out;\n}\n", "import type { ITypedArray } from '@pixi/core';\n\nexport function getBufferType(\n    array: ITypedArray\n): 'Float32Array' | 'Uint32Array' | 'Int32Array' | 'Uint16Array' | 'Uint8Array' | null\n{\n    if (array.BYTES_PER_ELEMENT === 4)\n    {\n        if (array instanceof Float32Array)\n        {\n            return 'Float32Array';\n        }\n        else if (array instanceof Uint32Array)\n        {\n            return 'Uint32Array';\n        }\n\n        return 'Int32Array';\n    }\n    else if (array.BYTES_PER_ELEMENT === 2)\n    {\n        if (array instanceof Uint16Array)\n        {\n            return 'Uint16Array';\n        }\n    }\n    else if (array.BYTES_PER_ELEMENT === 1)\n    {\n        if (array instanceof Uint8Array)\n        {\n            return 'Uint8Array';\n        }\n    }\n\n    // TODO map out the rest of the array elements!\n    return null;\n}\n", "import { getBufferType } from './getBufferType';\n\n/* eslint-disable object-shorthand */\nconst map = { Float32Array: Float32Array, Uint32Array: Uint32Array, Int32Array: Int32Array, Uint8Array: Uint8Array };\n\ntype PackedArray = Float32Array | Uint32Array | Int32Array | Uint8Array;\n\nexport function interleaveTypedArrays(arrays: PackedArray[], sizes: number[]): Float32Array\n{\n    let outSize = 0;\n    let stride = 0;\n    const views: {[key: string]: PackedArray} = {};\n\n    for (let i = 0; i < arrays.length; i++)\n    {\n        stride += sizes[i];\n        outSize += arrays[i].length;\n    }\n\n    const buffer = new ArrayBuffer(outSize * 4);\n\n    let out = null;\n    let littleOffset = 0;\n\n    for (let i = 0; i < arrays.length; i++)\n    {\n        const size = sizes[i];\n        const array = arrays[i];\n\n        /*\n        @todo This is unsafe casting but consistent with how the code worked previously. Should it stay this way\n              or should and `getBufferTypeUnsafe` function be exposed that throws an Error if unsupported type is passed?\n         */\n        const type = getBufferType(array) as keyof typeof map;\n\n        if (!views[type])\n        {\n            views[type] = new map[type](buffer);\n        }\n\n        out = views[type];\n\n        for (let j = 0; j < array.length; j++)\n        {\n            const indexStart = ((j / size | 0) * stride) + littleOffset;\n            const index = j % size;\n\n            out[indexStart + index] = array[j];\n        }\n\n        littleOffset += size;\n    }\n\n    return new Float32Array(buffer);\n}\n", "let nextUid = 0;\n\n/**\n * Gets the next unique identifier\n * @memberof PIXI.utils\n * @function uid\n * @returns {number} The next unique identifier to use.\n */\nexport function uid(): number\n{\n    return ++nextUid;\n}\n", "import type { Dict } from '../types';\n\n// A map of warning messages already fired\nconst warnings: Dict<boolean> = {};\n\n/**\n * Helper for warning developers about deprecated features & settings.\n * A stack track for warnings is given; useful for tracking-down where\n * deprecated methods/properties/classes are being used within the code.\n * @memberof PIXI.utils\n * @function deprecation\n * @param {string} version - The version where the feature became deprecated\n * @param {string} message - Message should include what is deprecated, where, and the new solution\n * @param {number} [ignoreDepth=3] - The number of steps to ignore at the top of the error stack\n *        this is mostly to ignore internal deprecation calls.\n */\nexport function deprecation(version: string, message: string, ignoreDepth = 3): void\n{\n    // Ignore duplicat\n    if (warnings[message])\n    {\n        return;\n    }\n\n    /* eslint-disable no-console */\n    let stack = new Error().stack;\n\n    // Handle IE < 10 and Safari < 6\n    if (typeof stack === 'undefined')\n    {\n        console.warn('PixiJS Deprecation Warning: ', `${message}\\nDeprecated since v${version}`);\n    }\n    else\n    {\n        // chop off the stack trace which includes PixiJS internal calls\n        stack = stack.split('\\n').splice(ignoreDepth).join('\\n');\n\n        if (console.groupCollapsed)\n        {\n            console.groupCollapsed(\n                '%cPixiJS Deprecation Warning: %c%s',\n                'color:#614108;background:#fffbe6',\n                'font-weight:normal;color:#614108;background:#fffbe6',\n                `${message}\\nDeprecated since v${version}`\n            );\n            console.warn(stack);\n            console.groupEnd();\n        }\n        else\n        {\n            console.warn('PixiJS Deprecation Warning: ', `${message}\\nDeprecated since v${version}`);\n            console.warn(stack);\n        }\n    }\n    /* eslint-enable no-console */\n\n    warnings[message] = true;\n}\n", "import type { Program, Texture, BaseTexture } from '@pixi/core';\n\n/**\n * @todo Describe property usage\n * @static\n * @name ProgramCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const ProgramCache: {[key: string]: Program} = {};\n\n/**\n * @todo Describe property usage\n * @static\n * @name TextureCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const TextureCache: {[key: string]: Texture} = Object.create(null);\n\n/**\n * @todo Describe property usage\n * @static\n * @name BaseTextureCache\n * @memberof PIXI.utils\n * @type {object}\n */\nexport const BaseTextureCache: {[key: string]: BaseTexture} = Object.create(null);\n\n/**\n * Destroys all texture in the cache\n * @memberof PIXI.utils\n * @function destroyTextureCache\n */\nexport function destroyTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        TextureCache[key].destroy();\n    }\n    for (key in BaseTextureCache)\n    {\n        BaseTextureCache[key].destroy();\n    }\n}\n\n/**\n * Removes all textures from cache, but does not destroy them\n * @memberof PIXI.utils\n * @function clearTextureCache\n */\nexport function clearTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        delete TextureCache[key];\n    }\n    for (key in BaseTextureCache)\n    {\n        delete BaseTextureCache[key];\n    }\n}\n", "import { settings } from '@pixi/settings';\n\n/**\n * Creates a Canvas element of the given size to be used as a target for rendering to.\n * @class\n * @memberof PIXI.utils\n */\nexport class CanvasRenderTarget\n{\n    /** The Canvas object that belongs to this CanvasRenderTarget. */\n    public canvas: HTMLCanvasElement;\n\n    /** A CanvasRenderingContext2D object representing a two-dimensional rendering context. */\n    public context: CanvasRenderingContext2D;\n\n    /**\n     * The resolution / device pixel ratio of the canvas\n     * @default 1\n     */\n    public resolution: number;\n\n    /**\n     * @param width - the width for the newly created canvas\n     * @param height - the height for the newly created canvas\n     * @param {number} [resolution=PIXI.settings.RESOLUTION] - The resolution / device pixel ratio of the canvas\n     */\n    constructor(width: number, height: number, resolution?: number)\n    {\n        this.canvas = settings.ADAPTER.createCanvas();\n\n        this.context = this.canvas.getContext('2d');\n\n        this.resolution = resolution || settings.RESOLUTION;\n\n        this.resize(width, height);\n    }\n\n    /**\n     * Clears the canvas that was created by the CanvasRenderTarget class.\n     * @private\n     */\n    clear(): void\n    {\n        this.context.setTransform(1, 0, 0, 1, 0, 0);\n        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    }\n\n    /**\n     * Resizes the canvas to the specified width and height.\n     * @param desiredWidth - the desired width of the canvas\n     * @param desiredHeight - the desired height of the canvas\n     */\n    resize(desiredWidth: number, desiredHeight: number): void\n    {\n        this.canvas.width = Math.round(desiredWidth * this.resolution);\n        this.canvas.height = Math.round(desiredHeight * this.resolution);\n    }\n\n    /** Destroys this canvas. */\n    destroy(): void\n    {\n        this.context = null;\n        this.canvas = null;\n    }\n\n    /**\n     * The width of the canvas buffer in pixels.\n     * @member {number}\n     */\n    get width(): number\n    {\n        return this.canvas.width;\n    }\n\n    set width(val: number)\n    {\n        this.canvas.width = Math.round(val);\n    }\n\n    /**\n     * The height of the canvas buffer in pixels.\n     * @member {number}\n     */\n    get height(): number\n    {\n        return this.canvas.height;\n    }\n\n    set height(val: number)\n    {\n        this.canvas.height = Math.round(val);\n    }\n}\n", "/**\n * Regexp for data URI.\n * Based on: {@link https://github.com/ragingwind/data-uri-regex}\n * @static\n * @constant {RegExp|string} DATA_URI\n * @memberof PIXI\n * @example data:image/png;base64\n */\nexport const DATA_URI = /^\\s*data:(?:([\\w-]+)\\/([\\w+.-]+))?(?:;charset=([\\w-]+))?(?:;(base64))?,(.*)/i;\n", "import { url as _url } from '../url';\n\nlet tempAnchor: HTMLAnchorElement | undefined;\n\n/**\n * Sets the `crossOrigin` property for this resource based on if the url\n * for this resource is cross-origin. If crossOrigin was manually set, this\n * function does nothing.\n * Nipped from the resource loader!\n * @ignore\n * @param {string} url - The url to test.\n * @param {object} [loc=window.location] - The location object to test against.\n * @returns {string} The crossOrigin value to use (or empty string for none).\n */\nexport function determineCrossOrigin(url: string, loc: Location = globalThis.location): string\n{\n    // data: and javascript: urls are considered same-origin\n    if (url.indexOf('data:') === 0)\n    {\n        return '';\n    }\n\n    // default is window.location\n    loc = loc || globalThis.location;\n\n    if (!tempAnchor)\n    {\n        tempAnchor = document.createElement('a');\n    }\n\n    // let the browser determine the full href for the url of this resource and then\n    // parse with the node url lib, we can't use the properties of the anchor element\n    // because they don't work in IE9 :(\n    tempAnchor.href = url;\n    const parsedUrl = _url.parse(tempAnchor.href);\n\n    const samePort = (!parsedUrl.port && loc.port === '') || (parsedUrl.port === loc.port);\n\n    // if cross origin\n    if (parsedUrl.hostname !== loc.hostname || !samePort || parsedUrl.protocol !== loc.protocol)\n    {\n        return 'anonymous';\n    }\n\n    return '';\n}\n", "/**\n * Generic Mask Stack data structure\n * @memberof PIXI.utils\n * @function createIndicesForQuads\n * @param {number} size - Number of quads\n * @param {Uint16Array|Uint32Array} [outBuffer] - Buffer for output, length has to be `6 * size`\n * @returns {Uint16Array|Uint32Array} - Resulting index buffer\n */\nexport function createIndicesForQuads(size: number, outBuffer: Uint16Array | Uint32Array = null): Uint16Array | Uint32Array\n{\n    // the total number of indices in our array, there are 6 points per quad.\n    const totalIndices = size * 6;\n\n    outBuffer = outBuffer || new Uint16Array(totalIndices);\n\n    if (outBuffer.length !== totalIndices)\n    {\n        throw new Error(`Out buffer length is incorrect, got ${outBuffer.length} and expected ${totalIndices}`);\n    }\n\n    // fill the indices with the quads to draw\n    for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4)\n    {\n        outBuffer[i + 0] = j + 0;\n        outBuffer[i + 1] = j + 1;\n        outBuffer[i + 2] = j + 2;\n        outBuffer[i + 3] = j + 0;\n        outBuffer[i + 4] = j + 2;\n        outBuffer[i + 5] = j + 3;\n    }\n\n    return outBuffer;\n}\n", "import { DATA_URI } from '../const';\n\nexport interface DecomposedDataUri\n{\n    mediaType: string;\n    subType: string;\n    charset: string;\n    encoding: string;\n    data: string;\n}\n\n/**\n * @memberof PIXI.utils\n * @interface DecomposedDataUri\n */\n\n/**\n * type, eg. `image`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} mediaType\n */\n\n/**\n * Sub type, eg. `png`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} subType\n */\n\n/**\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} charset\n */\n\n/**\n * Data encoding, eg. `base64`\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} encoding\n */\n\n/**\n * The actual data\n * @memberof PIXI.utils.DecomposedDataUri#\n * @member {string} data\n */\n\n/**\n * Split a data URI into components. Returns undefined if\n * parameter `dataUri` is not a valid data URI.\n * @memberof PIXI.utils\n * @function decomposeDataUri\n * @param {string} dataUri - the data URI to check\n * @returns {PIXI.utils.DecomposedDataUri|undefined} The decomposed data uri or undefined\n */\nexport function decomposeDataUri(dataUri: string): DecomposedDataUri\n{\n    const dataUriMatch = DATA_URI.exec(dataUri);\n\n    if (dataUriMatch)\n    {\n        return {\n            mediaType: dataUriMatch[1] ? dataUriMatch[1].toLowerCase() : undefined,\n            subType: dataUriMatch[2] ? dataUriMatch[2].toLowerCase() : undefined,\n            charset: dataUriMatch[3] ? dataUriMatch[3].toLowerCase() : undefined,\n            encoding: dataUriMatch[4] ? dataUriMatch[4].toLowerCase() : undefined,\n            data: dataUriMatch[5],\n        };\n    }\n\n    return undefined;\n}\n", "import { settings } from '../settings';\n\n/**\n * get the resolution / device pixel ratio of an asset by looking for the prefix\n * used by spritesheets and image urls\n * @memberof PIXI.utils\n * @function getResolutionOfUrl\n * @param {string} url - the image path\n * @param {number} [defaultValue=1] - the defaultValue if no filename prefix is set.\n * @returns {number} resolution / device pixel ratio of an asset\n */\nexport function getResolutionOfUrl(url: string, defaultValue?: number): number\n{\n    const resolution = settings.RETINA_PREFIX.exec(url);\n\n    if (resolution)\n    {\n        return parseFloat(resolution[1]);\n    }\n\n    return defaultValue !== undefined ? defaultValue : 1;\n}\n", "import { default as cssColorNames } from 'css-color-names';\n\n/**\n * Converts a hexadecimal color number to an [R, G, B] array of normalized floats (numbers from 0.0 to 1.0).\n * @example\n * PIXI.utils.hex2rgb(0xffffff); // returns [1, 1, 1]\n * @memberof PIXI.utils\n * @function hex2rgb\n * @param {number} hex - The hexadecimal number to convert\n * @param  {number[]} [out=[]] - If supplied, this array will be used rather than returning a new one\n * @returns {number[]} An array representing the [R, G, B] of the color where all values are floats.\n */\nexport function hex2rgb(hex: number, out: Array<number> | Float32Array = []): Array<number> | Float32Array\n{\n    out[0] = ((hex >> 16) & 0xFF) / 255;\n    out[1] = ((hex >> 8) & 0xFF) / 255;\n    out[2] = (hex & 0xFF) / 255;\n\n    return out;\n}\n\n/**\n * Converts a hexadecimal color number to a string.\n * @example\n * PIXI.utils.hex2string(0xffffff); // returns \"#ffffff\"\n * @memberof PIXI.utils\n * @function hex2string\n * @param {number} hex - Number in hex (e.g., `0xffffff`)\n * @returns {string} The string color (e.g., `\"#ffffff\"`).\n */\nexport function hex2string(hex: number): string\n{\n    let hexString = hex.toString(16);\n\n    hexString = '000000'.substring(0, 6 - hexString.length) + hexString;\n\n    return `#${hexString}`;\n}\n\n/**\n * Converts a string to a hexadecimal color number.\n * It can handle:\n *  hex strings starting with #: \"#ffffff\"\n *  hex strings starting with 0x: \"0xffffff\"\n *  hex strings without prefix: \"ffffff\"\n *  css colors: \"black\"\n * @example\n * PIXI.utils.string2hex(\"#ffffff\"); // returns 0xffffff, which is 16777215 as an integer\n * @memberof PIXI.utils\n * @function string2hex\n * @param {string} string - The string color (e.g., `\"#ffffff\"`)\n * @returns {number} Number in hexadecimal.\n */\nexport function string2hex(string: string): number\n{\n    if (typeof string === 'string')\n    {\n        string = (cssColorNames as {[key: string]: string})[string.toLowerCase()] || string;\n\n        if (string[0] === '#')\n        {\n            string = string.slice(1);\n        }\n    }\n\n    return parseInt(string, 16);\n}\n\n/**\n * Converts a color as an [R, G, B] array of normalized floats to a hexadecimal number.\n * @example\n * PIXI.utils.rgb2hex([1, 1, 1]); // returns 0xffffff, which is 16777215 as an integer\n * @memberof PIXI.utils\n * @function rgb2hex\n * @param {number[]} rgb - Array of numbers where all values are normalized floats from 0.0 to 1.0.\n * @returns {number} Number in hexadecimal.\n */\nexport function rgb2hex(rgb: number[] | Float32Array): number\n{\n    return (((rgb[0] * 255) << 16) + ((rgb[1] * 255) << 8) + (rgb[2] * 255 | 0));\n}\n", "// Taken from the bit-twiddle package\n\n/**\n * Rounds to next power of two.\n * @function nextPow2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {number} - next rounded power of two\n */\nexport function nextPow2(v: number): number\n{\n    v += v === 0 ? 1 : 0;\n    --v;\n    v |= v >>> 1;\n    v |= v >>> 2;\n    v |= v >>> 4;\n    v |= v >>> 8;\n    v |= v >>> 16;\n\n    return v + 1;\n}\n\n/**\n * Checks if a number is a power of two.\n * @function isPow2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {boolean} `true` if value is power of two\n */\nexport function isPow2(v: number): boolean\n{\n    return !(v & (v - 1)) && (!!v);\n}\n\n/**\n * Computes ceil of log base 2\n * @function log2\n * @memberof PIXI.utils\n * @param {number} v - input value\n * @returns {number} logarithm base 2\n */\nexport function log2(v: number): number\n{\n    let r = (v > 0xFFFF ? 1 : 0) << 4;\n\n    v >>>= r;\n\n    let shift = (v > 0xFF ? 1 : 0) << 3;\n\n    v >>>= shift; r |= shift;\n    shift = (v > 0xF ? 1 : 0) << 2;\n    v >>>= shift; r |= shift;\n    shift = (v > 0x3 ? 1 : 0) << 1;\n    v >>>= shift; r |= shift;\n\n    return r | (v >> 1);\n}\n", "/**\n * Remove items from a javascript array without generating garbage\n * @function removeItems\n * @memberof PIXI.utils\n * @param {Array<any>} arr - Array to remove elements from\n * @param {number} startIdx - starting index\n * @param {number} removeCount - how many to remove\n */\nexport function removeItems(arr: any[], startIdx: number, removeCount: number): void\n{\n    const length = arr.length;\n    let i;\n\n    if (startIdx >= length || removeCount === 0)\n    {\n        return;\n    }\n\n    removeCount = (startIdx + removeCount > length ? length - startIdx : removeCount);\n\n    const len = length - removeCount;\n\n    for (i = startIdx; i < len; ++i)\n    {\n        arr[i] = arr[i + removeCount];\n    }\n\n    arr.length = len;\n}\n", "/**\n * Returns sign of number\n * @memberof PIXI.utils\n * @function sign\n * @param {number} n - the number to check the sign of\n * @returns {number} 0 if `n` is 0, -1 if `n` is negative, 1 if `n` is positive\n */\nexport function sign(n: number): -1 | 0 | 1\n{\n    if (n === 0) return 0;\n\n    return n < 0 ? -1 : 1;\n}\n", "interface Inset\n{\n    top?: number;\n    left?: number;\n    right?: number;\n    bottom?: number;\n}\n\n/**\n * Trim transparent borders from a canvas\n * @memberof PIXI.utils\n * @function trimCanvas\n * @param {HTMLCanvasElement} canvas - the canvas to trim\n * @returns {object} Trim data\n */\nexport function trimCanvas(canvas: HTMLCanvasElement): {width: number; height: number; data?: ImageData}\n{\n    // https://gist.github.com/remy/784508\n\n    let width = canvas.width;\n    let height = canvas.height;\n\n    const context = canvas.getContext('2d', {\n        willReadFrequently: true,\n    } as CanvasRenderingContext2DSettings);\n    const imageData = context.getImageData(0, 0, width, height);\n    const pixels = imageData.data;\n    const len = pixels.length;\n\n    const bound: Inset = {\n        top: null,\n        left: null,\n        right: null,\n        bottom: null,\n    };\n    let data = null;\n    let i;\n    let x;\n    let y;\n\n    for (i = 0; i < len; i += 4)\n    {\n        if (pixels[i + 3] !== 0)\n        {\n            x = (i / 4) % width;\n            y = ~~((i / 4) / width);\n\n            if (bound.top === null)\n            {\n                bound.top = y;\n            }\n\n            if (bound.left === null)\n            {\n                bound.left = x;\n            }\n            else if (x < bound.left)\n            {\n                bound.left = x;\n            }\n\n            if (bound.right === null)\n            {\n                bound.right = x + 1;\n            }\n            else if (bound.right < x)\n            {\n                bound.right = x + 1;\n            }\n\n            if (bound.bottom === null)\n            {\n                bound.bottom = y;\n            }\n            else if (bound.bottom < y)\n            {\n                bound.bottom = y;\n            }\n        }\n    }\n\n    if (bound.top !== null)\n    {\n        width = bound.right - bound.left;\n        height = bound.bottom - bound.top + 1;\n        data = context.getImageData(bound.left, bound.top, width, height);\n    }\n\n    return {\n        height,\n        width,\n        data,\n    };\n}\n"], "names": ["url", "parse", "_parse", "format", "_format", "resolve", "_resolve", "assertPath", "path", "TypeError", "JSON", "stringify", "removeUrlParams", "split", "toPosix", "find", "replace", "RegExp", "isUrl", "test", "this", "isDataUrl", "hasProtocol", "getProtocol", "protocol", "isFile", "exec", "isHttp", "isWindows", "arr", "slice", "length", "toAbsolute", "customBaseUrl", "customRootUrl", "baseUrl", "settings", "ADAPTER", "getBaseUrl", "rootUrl", "rootname", "startsWith", "join", "isAbsolute", "normalize", "trailingSeparator", "endsWith", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "i", "charCodeAt", "lastSlashIndex", "lastIndexOf", "normalizeStringPosix", "joined", "segments", "_i", "arguments", "arg", "undefined", "prevArg", "_a", "extname", "dirname", "hasRoot", "end", "matchedSlash", "proto", "origpath", "root", "index", "indexOf", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "startDot", "startPart", "preDotState", "ret", "dir", "base", "name", "sep", "delimiter", "RETINA_PREFIX", "FAIL_IF_MAJOR_PERFORMANCE_CAVEAT", "supported", "<PERSON><PERSON><PERSON>", "premultiplyBlendMode", "pm", "npm", "BLEND_MODES", "NORMAL_NPM", "NORMAL", "ADD_NPM", "ADD", "SCREEN_NPM", "SCREEN", "array", "push", "mapPremultipliedBlendModes", "getBufferType", "BYTES_PER_ELEMENT", "Float32Array", "Uint32Array", "Uint16Array", "Uint8Array", "map", "Int32Array", "nextUid", "warnings", "TextureCache", "Object", "create", "BaseTextureCache", "CanvasRenderTarget", "width", "height", "resolution", "canvas", "createCanvas", "context", "getContext", "RESOLUTION", "resize", "prototype", "clear", "setTransform", "clearRect", "desiredWidth", "desiredHeight", "Math", "round", "destroy", "defineProperty", "get", "set", "val", "tempAnchor", "DATA_URI", "key", "blendMode", "premultiplied", "size", "outBuffer", "totalIndices", "Error", "j", "dataUri", "dataUriMatch", "mediaType", "toLowerCase", "subType", "charset", "encoding", "data", "version", "message", "<PERSON><PERSON><PERSON><PERSON>", "stack", "console", "warn", "splice", "groupCollapsed", "groupEnd", "loc", "globalThis", "location", "document", "createElement", "href", "parsedUrl", "_url", "samePort", "port", "hostname", "defaultValue", "parseFloat", "hex", "out", "hexString", "toString", "substring", "arrays", "sizes", "outSize", "stride", "views", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littleOffset", "type", "v", "contextOptions", "stencil", "failIfMajorPerformanceCaveat", "getWebGLRenderingContext", "gl", "success", "getContextAttributes", "loseContext", "getExtension", "e", "r", "shift", "rgb", "alpha", "premultiply", "tint", "R", "G", "B", "startIdx", "removeCount", "len", "getNavigator", "userAgent", "args", "log", "apply", "n", "string", "cssColorNames", "parseInt", "x", "y", "willReadFrequently", "pixels", "getImageData", "bound", "top", "left", "right", "bottom"], "mappings": ";;;;;;;2RAiFaA,EAAM,CACfC,MAAOC,EAAuBD,MAC9BE,OAAQC,EAAyBD,OACjCE,QAASC,EAA2BD,SClFxC,SAASE,EAAWC,GAEhB,GAAoB,iBAATA,EAEP,MAAM,IAAIC,UAAU,mCAAmCC,KAAKC,UAAUH,IAI9E,SAASI,EAAgBZ,GAIrB,OAFWA,EAAIa,MAAM,KAAK,GAEhBA,MAAM,KAAK,GA0IlB,IAAML,EAAa,CAKtBM,QAAA,SAAQN,GAAgB,OAvIKO,EAuImB,KAvILC,EAuIW,IAAZR,EArI/BQ,QAAQ,IAAIC,OAAoBF,EAL7BC,QAAQ,sBAAuB,QAKK,KAAMA,GAF5D,IAAiCD,EAAcC,GA4I3CE,MAAA,SAAMV,GAAgB,MAAO,WAAaW,KAAKC,KAAKN,QAAQN,KAK5Da,UAAA,SAAUb,GAGN,MAAO,yIACFW,KAAKX,IAOdc,YAAA,SAAYd,GAAgB,MAAO,aAAeW,KAAKC,KAAKN,QAAQN,KAKpEe,YAAA,SAAYf,GAERD,EAAWC,GACXA,EAAOY,KAAKN,QAAQN,GAEpB,IAAIgB,EAAW,GAETC,EAAS,eAAiBC,KAAKlB,GAC/BmB,EAAS,eAAiBD,KAAKlB,GAC/BoB,EAAY,aAAeF,KAAKlB,GAEtC,GAAIiB,GAAUE,GAAUC,EACxB,CACI,IAAMC,GAAMJ,MAAAA,OAAA,EAAAA,EAAS,MAAME,MAAAA,OAAA,EAAAA,EAAS,MAAMC,MAAAA,OAAS,EAATA,EAAY,IAEtDJ,EAAWK,EACXrB,EAAOA,EAAKsB,MAAMD,EAAIE,QAG1B,OAAOP,GAYXQ,WAAA,SAAWhC,EAAaiC,EAAwBC,GAE5C,GAAId,KAAKC,UAAUrB,GAAM,OAAOA,EAEhC,IAAMmC,EAAUvB,EAAgBQ,KAAKN,QAAQmB,MAAAA,EAAAA,EAAiBG,EAAQA,SAACC,QAAQC,eACzEC,EAAU3B,EAAgBQ,KAAKN,QAAQoB,MAAAA,EAAAA,EAAiBd,KAAKoB,SAASL,KAM5E,OAJA5B,EAAWP,IACXA,EAAMoB,KAAKN,QAAQd,IAGXyC,WAAW,KAERjC,EAAKkC,KAAKH,EAASvC,EAAI8B,MAAM,IAGnBV,KAAKuB,WAAW3C,GAAOA,EAAMoB,KAAKsB,KAAKP,EAASnC,IASzE4C,UAAA,SAAUpC,GAKN,GAFAD,EADAC,EAAOY,KAAKN,QAAQN,IAGA,IAAhBA,EAAKuB,OAAc,MAAO,IAE9B,IAAIP,EAAW,GACTmB,EAAanC,EAAKiC,WAAW,KAE/BrB,KAAKE,YAAYd,KAEjBgB,EAAWJ,KAAKoB,SAAShC,GACzBA,EAAOA,EAAKsB,MAAMN,EAASO,SAG/B,IAAMc,EAAoBrC,EAAKsC,SAAS,KAMxC,OAHAtC,EAtOR,SAA8BA,EAAcuC,GAQxC,IANA,IAIIC,EAJAC,EAAM,GACNC,EAAoB,EACpBC,GAAa,EACbC,EAAO,EAGFC,EAAI,EAAGA,GAAK7C,EAAKuB,SAAUsB,EACpC,CACI,GAAIA,EAAI7C,EAAKuB,OAETiB,EAAOxC,EAAK8C,WAAWD,OAEtB,CAAA,GAAa,KAATL,EAEL,MAIAA,EAAO,GAEX,GAAa,KAATA,EACJ,CACI,GAAIG,IAAcE,EAAI,GAAc,IAATD,QAItB,GAAID,IAAcE,EAAI,GAAc,IAATD,EAChC,CACI,GACIH,EAAIlB,OAAS,GACY,IAAtBmB,GACmC,KAAnCD,EAAIK,WAAWL,EAAIlB,OAAS,IACO,KAAnCkB,EAAIK,WAAWL,EAAIlB,OAAS,GAG/B,GAAIkB,EAAIlB,OAAS,EACjB,CACI,IAAMwB,EAAiBN,EAAIO,YAAY,KAEvC,GAAID,IAAmBN,EAAIlB,OAAS,EACpC,EAC4B,IAApBwB,GAEAN,EAAM,GACNC,EAAoB,GAKpBA,GADAD,EAAMA,EAAInB,MAAM,EAAGyB,IACKxB,OAAS,EAAIkB,EAAIO,YAAY,KAEzDL,EAAYE,EACZD,EAAO,EACP,eAGH,GAAmB,IAAfH,EAAIlB,QAA+B,IAAfkB,EAAIlB,OACjC,CACIkB,EAAM,GACNC,EAAoB,EACpBC,EAAYE,EACZD,EAAO,EACP,SAGJL,IAEIE,EAAIlB,OAAS,EACfkB,GAAO,MAEPA,EAAM,KACRC,EAAoB,QAKpBD,EAAIlB,OAAS,EAEbkB,GAAO,IAAIzC,EAAKsB,MAAMqB,EAAY,EAAGE,GAIrCJ,EAAMzC,EAAKsB,MAAMqB,EAAY,EAAGE,GAEpCH,EAAoBG,EAAIF,EAAY,EAExCA,EAAYE,EACZD,EAAO,OAEO,KAATJ,IAAyB,IAAVI,IAElBA,EAIFA,GAAQ,EAIhB,OAAOH,EAiIIQ,CAAqBjD,GAAM,IAEzBuB,OAAS,GAAKc,IAAmBrC,GAAQ,KAC9CmC,EAAmB,IAAInC,EAEpBgB,EAAWhB,GAQtBmC,WAAA,SAAWnC,GAKP,OAHAD,EAAWC,GACXA,EAAOY,KAAKN,QAAQN,KAEhBY,KAAKE,YAAYd,IAEdA,EAAKiC,WAAW,MAQ3BC,KAAA,qBAIQgB,cAJkBC,EAAA,GAAAC,EAAA,EAArBA,EAAqBC,UAAA9B,OAArB6B,IAAAD,EAAqBC,GAAAC,EAAAD,GAEtB,GAAwB,IAApBD,EAAS5B,OACX,MAAO,IAGT,IAAK,IAAIsB,EAAI,EAAGA,EAAIM,EAAS5B,SAAUsB,EACvC,CACI,IAAMS,EAAMH,EAASN,GAGrB,GADA9C,EAAWuD,GACPA,EAAI/B,OAAS,EAEb,QAAegC,IAAXL,EAAsBA,EAASI,MAEnC,CACI,IAAME,EAA6B,QAAnBC,EAAAN,EAASN,EAAI,UAAM,IAAAY,EAAAA,EAAA,GAE/B7C,KAAK8C,QAAQF,GAEbN,GAAU,OAAOI,EAIjBJ,GAAU,IAAII,GAK9B,YAAeC,IAAXL,EAA+B,IAE5BtC,KAAKwB,UAAUc,IAO1BS,QAAA,SAAQ3D,GAGJ,GADAD,EAAWC,GACS,IAAhBA,EAAKuB,OAAc,MAAO,IAY9B,IAVA,IAAIiB,GADJxC,EAAOY,KAAKN,QAAQN,IACJ8C,WAAW,GACrBc,EAAmB,KAATpB,EACZqB,GAAO,EACPC,GAAe,EAEbC,EAAQnD,KAAKG,YAAYf,GACzBgE,EAAWhE,EAIR6C,GAFT7C,EAAOA,EAAKsB,MAAMyC,EAAMxC,SAENA,OAAS,EAAGsB,GAAK,IAAKA,EAGpC,GAAa,MADbL,EAAOxC,EAAK8C,WAAWD,KAGnB,IAAKiB,EACL,CACID,EAAMhB,EACN,YAMJiB,GAAe,EAMvB,OAAa,IAATD,EAAmBD,EAAU,IAAMhD,KAAKF,MAAMsD,GAAYD,EAAQ/D,EAAO+D,EACzEH,GAAmB,IAARC,EAAkB,KAE1BE,EAAQ/D,EAAKsB,MAAM,EAAGuC,IAOjC7B,SAAA,SAAShC,GAELD,EAAWC,GAGX,IAAIiE,EAAO,GAQX,GAN0BA,GAJ1BjE,EAAOY,KAAKN,QAAQN,IAIXiC,WAAW,KAAa,IAGtBrB,KAAKG,YAAYf,GAGxBY,KAAKF,MAAMV,GACf,CAEI,IAAMkE,EAAQlE,EAAKmE,QAAQ,IAAKF,EAAK1C,SAIjC0C,GAFW,IAAXC,EAEOlE,EAAKsB,MAAM,EAAG4C,GAEblE,GAEFsC,SAAS,OAAM2B,GAAQ,KAGrC,OAAOA,GAQXG,SAAA,SAASpE,EAAcqE,GAEnBtE,EAAWC,GACPqE,GAAKtE,EAAWsE,GAEpBrE,EAAOY,KAAKN,QAAQN,GAEpB,IAGI6C,EAHAyB,EAAQ,EACRT,GAAO,EACPC,GAAe,EAGnB,QAAYP,IAARc,GAAqBA,EAAI9C,OAAS,GAAK8C,EAAI9C,QAAUvB,EAAKuB,OAC9D,CACI,GAAI8C,EAAI9C,SAAWvB,EAAKuB,QAAU8C,IAAQrE,EAAM,MAAO,GACvD,IAAIuE,EAASF,EAAI9C,OAAS,EACtBiD,GAAoB,EAExB,IAAK3B,EAAI7C,EAAKuB,OAAS,EAAGsB,GAAK,IAAKA,EACpC,CACI,IAAML,EAAOxC,EAAK8C,WAAWD,GAE7B,GAAa,KAATL,GAIA,IAAKsB,EACL,CACIQ,EAAQzB,EAAI,EACZ,YAKsB,IAAtB2B,IAIAV,GAAe,EACfU,EAAmB3B,EAAI,GAEvB0B,GAAU,IAGN/B,IAAS6B,EAAIvB,WAAWyB,IAEN,KAAZA,IAIFV,EAAMhB,IAOV0B,GAAU,EACVV,EAAMW,IAQtB,OAFIF,IAAUT,EAAKA,EAAMW,GAAoC,IAATX,IAAYA,EAAM7D,EAAKuB,QAEpEvB,EAAKsB,MAAMgD,EAAOT,GAE7B,IAAKhB,EAAI7C,EAAKuB,OAAS,EAAGsB,GAAK,IAAKA,EAEhC,GAA2B,KAAvB7C,EAAK8C,WAAWD,IAIhB,IAAKiB,EACL,CACIQ,EAAQzB,EAAI,EACZ,YAGU,IAATgB,IAILC,GAAe,EACfD,EAAMhB,EAAI,GAIlB,OAAa,IAATgB,EAAmB,GAEhB7D,EAAKsB,MAAMgD,EAAOT,IAS7BH,QAAA,SAAQ1D,GAEJD,EAAWC,GAWX,IARA,IAAIyE,GAAY,EACZC,EAAY,EACZb,GAAO,EACPC,GAAe,EAGfa,EAAc,EAET9B,GAVT7C,EAAOY,KAAKN,QAAQN,IAUFuB,OAAS,EAAGsB,GAAK,IAAKA,EACxC,CACI,IAAML,EAAOxC,EAAK8C,WAAWD,GAE7B,GAAa,KAATL,GAWS,IAATqB,IAIAC,GAAe,EACfD,EAAMhB,EAAI,GAED,KAATL,GAGkB,IAAdiC,EAAiBA,EAAW5B,EACP,IAAhB8B,IAAmBA,EAAc,IAEvB,IAAdF,IAILE,GAAe,QAxBf,IAAKb,EACL,CACIY,EAAY7B,EAAI,EAChB,OAyBZ,OACkB,IAAd4B,IAA4B,IAATZ,GAEA,IAAhBc,GAGgB,IAAhBA,GAAqBF,IAAaZ,EAAM,GAAKY,IAAaC,EAAY,EAGlE,GAGJ1E,EAAKsB,MAAMmD,EAAUZ,IAOhCpE,MAAA,SAAMO,GAEFD,EAAWC,GAEX,IAAM4E,EAAM,CAAEX,KAAM,GAAIY,IAAK,GAAIC,KAAM,GAAIT,IAAK,GAAIU,KAAM,IAE1D,GAAoB,IAAhB/E,EAAKuB,OAAc,OAAOqD,EAG9B,IAEIN,EAFA9B,GAFJxC,EAAOY,KAAKN,QAAQN,IAEJ8C,WAAW,GACrBX,EAAavB,KAAKuB,WAAWnC,GAInC4E,EAAIX,KAAOrD,KAAKoB,SAAShC,GAIrBsE,EAFAnC,GAAcvB,KAAKE,YAAYd,GAEvB,EAIA,EAaZ,IAXA,IAAIyE,GAAY,EACZC,EAAY,EACZb,GAAO,EACPC,GAAe,EACfjB,EAAI7C,EAAKuB,OAAS,EAIlBoD,EAAc,EAGX9B,GAAKyB,IAASzB,EAGjB,GAAa,MADbL,EAAOxC,EAAK8C,WAAWD,KAYV,IAATgB,IAIAC,GAAe,EACfD,EAAMhB,EAAI,GAED,KAATL,GAGkB,IAAdiC,EAAiBA,EAAW5B,EACP,IAAhB8B,IAAmBA,EAAc,IAEvB,IAAdF,IAILE,GAAe,QAxBf,IAAKb,EACL,CACIY,EAAY7B,EAAI,EAChB,MA0DZ,OAhCkB,IAAd4B,IAA4B,IAATZ,GAEA,IAAhBc,GAGgB,IAAhBA,GAAqBF,IAAaZ,EAAM,GAAKY,IAAaC,EAAY,GAG5D,IAATb,IAEmCe,EAAIE,KAAOF,EAAIG,KAAhC,IAAdL,GAAmBvC,EAAkCnC,EAAKsB,MAAM,EAAGuC,GAC5C7D,EAAKsB,MAAMoD,EAAWb,KAKnC,IAAda,GAAmBvC,GAEnByC,EAAIG,KAAO/E,EAAKsB,MAAM,EAAGmD,GACzBG,EAAIE,KAAO9E,EAAKsB,MAAM,EAAGuC,KAIzBe,EAAIG,KAAO/E,EAAKsB,MAAMoD,EAAWD,GACjCG,EAAIE,KAAO9E,EAAKsB,MAAMoD,EAAWb,IAErCe,EAAIP,IAAMrE,EAAKsB,MAAMmD,EAAUZ,IAGnCe,EAAIC,IAAMjE,KAAK+C,QAAQ3D,GAGhB4E,GAGXI,IAAK,IACLC,UAAW,KC5pBfrD,EAAAA,SAASsD,cAAgB,eA2BzBtD,EAAQA,SAACuD,kCAAmC,ECpC5C,ICAIC,EDAAC,GAAY,g5FEwCH,IAAAC,EAjCb,WAKI,IAHA,IAAMC,EAAK,GACLC,EAAM,GAEH3C,EAAI,EAAGA,EAAI,GAAIA,IAEpB0C,EAAG1C,GAAKA,EACR2C,EAAI3C,GAAKA,EAGb0C,EAAGE,EAAAA,YAAYC,YAAcD,EAAAA,YAAYE,OACzCJ,EAAGE,EAAAA,YAAYG,SAAWH,EAAAA,YAAYI,IACtCN,EAAGE,EAAAA,YAAYK,YAAcL,EAAAA,YAAYM,OAEzCP,EAAIC,EAAAA,YAAYE,QAAUF,EAAAA,YAAYC,WACtCF,EAAIC,EAAAA,YAAYI,KAAOJ,EAAAA,YAAYG,QACnCJ,EAAIC,EAAAA,YAAYM,QAAUN,EAAAA,YAAYK,WAEtC,IAAME,EAAoB,GAK1B,OAHAA,EAAMC,KAAKT,GACXQ,EAAMC,KAAKV,GAEJS,EASyBE,GCxC9B,SAAUC,EACZH,GAGA,GAAgC,IAA5BA,EAAMI,kBAEN,OAAIJ,aAAiBK,aAEV,eAEFL,aAAiBM,YAEf,cAGJ,aAEN,GAAgC,IAA5BN,EAAMI,mBAEX,GAAIJ,aAAiBO,YAEjB,MAAO,mBAGV,GAAgC,IAA5BP,EAAMI,mBAEPJ,aAAiBQ,WAEjB,MAAO,aAKf,OAAO,KChCX,IAAMC,EAAM,CAAEJ,aAAcA,aAAcC,YAAaA,YAAaI,WAAYA,WAAYF,WAAYA,YCHxG,IAAIG,EAAU,ECGd,IAAMC,EAA0B,GCMnB,IASAC,EAAyCC,OAAOC,OAAO,MASvDC,EAAiDF,OAAOC,OAAO,MCpB5E,IAAAE,EAAA,WAmBI,SAAAA,EAAYC,EAAeC,EAAgBC,GAEvCxG,KAAKyG,OAASzF,EAAAA,SAASC,QAAQyF,eAE/B1G,KAAK2G,QAAU3G,KAAKyG,OAAOG,WAAW,MAEtC5G,KAAKwG,WAAaA,GAAcxF,EAAAA,SAAS6F,WAEzC7G,KAAK8G,OAAOR,EAAOC,GA0D3B,OAnDIF,EAAAU,UAAAC,MAAA,WAEIhH,KAAK2G,QAAQM,aAAa,EAAG,EAAG,EAAG,EAAG,EAAG,GACzCjH,KAAK2G,QAAQO,UAAU,EAAG,EAAGlH,KAAKyG,OAAOH,MAAOtG,KAAKyG,OAAOF,SAQhEF,EAAAU,UAAAD,OAAA,SAAOK,EAAsBC,GAEzBpH,KAAKyG,OAAOH,MAAQe,KAAKC,MAAMH,EAAenH,KAAKwG,YACnDxG,KAAKyG,OAAOF,OAASc,KAAKC,MAAMF,EAAgBpH,KAAKwG,aAIzDH,EAAAU,UAAAQ,QAAA,WAEIvH,KAAK2G,QAAU,KACf3G,KAAKyG,OAAS,MAOlBP,OAAAsB,eAAInB,EAAKU,UAAA,QAAA,CAATU,IAAA,WAEI,OAAOzH,KAAKyG,OAAOH,OAGvBoB,IAAA,SAAUC,GAEN3H,KAAKyG,OAAOH,MAAQe,KAAKC,MAAMK,oCAOnCzB,OAAAsB,eAAInB,EAAMU,UAAA,SAAA,CAAVU,IAAA,WAEI,OAAOzH,KAAKyG,OAAOF,QAGvBmB,IAAA,SAAWC,GAEP3H,KAAKyG,OAAOF,OAASc,KAAKC,MAAMK,oCAEvCtB,KCpFY,ICNTuB,EDMSC,EAAW,mcFC8B,+DA8ClD,IAAIC,EAEJ,IAAKA,KAAO7B,SAEDA,EAAa6B,GAExB,IAAKA,KAAO1B,SAEDA,EAAiB0B,6BLXhB,SAAiBC,EAAmBC,GAEhD,OAAOtD,EAAqBsD,EAAgB,EAAI,GAAGD,kCS9CvC,SAAsBE,EAAcC,QAAA,IAAAA,IAAAA,EAA2C,MAG3F,IAAMC,EAAsB,EAAPF,EAIrB,IAFAC,EAAYA,GAAa,IAAIvC,YAAYwC,IAE3BxH,SAAWwH,EAErB,MAAM,IAAIC,MAAM,uCAAuCF,EAAUvH,OAAM,iBAAiBwH,GAI5F,IAAK,IAAIlG,EAAI,EAAGoG,EAAI,EAAGpG,EAAIkG,EAAclG,GAAK,EAAGoG,GAAK,EAElDH,EAAUjG,EAAI,GAAKoG,EAAI,EACvBH,EAAUjG,EAAI,GAAKoG,EAAI,EACvBH,EAAUjG,EAAI,GAAKoG,EAAI,EACvBH,EAAUjG,EAAI,GAAKoG,EAAI,EACvBH,EAAUjG,EAAI,GAAKoG,EAAI,EACvBH,EAAUjG,EAAI,GAAKoG,EAAI,EAG3B,OAAOH,4BCsBL,SAA2BI,GAE7B,IAAMC,EAAeV,EAASvH,KAAKgI,GAEnC,GAAIC,EAEA,MAAO,CACHC,UAAWD,EAAa,GAAKA,EAAa,GAAGE,mBAAgB9F,EAC7D+F,QAASH,EAAa,GAAKA,EAAa,GAAGE,mBAAgB9F,EAC3DgG,QAASJ,EAAa,GAAKA,EAAa,GAAGE,mBAAgB9F,EAC3DiG,SAAUL,EAAa,GAAKA,EAAa,GAAGE,mBAAgB9F,EAC5DkG,KAAMN,EAAa,kCNhDHO,EAAiBC,EAAiBC,GAG1D,QAH0D,IAAAA,IAAAA,EAAe,IAGrEhD,EAAS+C,GAAb,CAMA,IAAIE,GAAQ,IAAIb,OAAQa,WAGH,IAAVA,EAEPC,QAAQC,KAAK,+BAAmCJ,EAAO,uBAAuBD,IAK9EG,EAAQA,EAAMxJ,MAAM,MAAM2J,OAAOJ,GAAa1H,KAAK,MAE/C4H,QAAQG,gBAERH,QAAQG,eACJ,qCACA,mCACA,sDACGN,EAA8B,uBAAAD,GAErCI,QAAQC,KAAKF,GACbC,QAAQI,aAIRJ,QAAQC,KAAK,+BAAmCJ,EAAO,uBAAuBD,GAC9EI,QAAQC,KAAKF,KAKrBjD,EAAS+C,IAAW,2CCpBpB,IAAIjB,EAEJ,IAAKA,KAAO7B,EAERA,EAAa6B,GAAKP,UAEtB,IAAKO,KAAO1B,EAERA,EAAiB0B,GAAKP,wCG9Bd,SAAqB3I,EAAa2K,GAG9C,QAH8C,IAAAA,IAAAA,EAAgBC,WAAWC,UAG5C,IAAzB7K,EAAI2E,QAAQ,SAEZ,MAAO,GAIXgG,EAAMA,GAAOC,WAAWC,SAEnB7B,IAEDA,EAAa8B,SAASC,cAAc,MAMxC/B,EAAWgC,KAAOhL,EAClB,IAAMiL,EAAYC,EAAKjL,MAAM+I,EAAWgC,MAElCG,GAAaF,EAAUG,MAAqB,KAAbT,EAAIS,MAAiBH,EAAUG,OAAST,EAAIS,KAGjF,OAAIH,EAAUI,WAAaV,EAAIU,UAAaF,GAAYF,EAAUzJ,WAAamJ,EAAInJ,SAK5E,GAHI,gEG9BC,SAAmBxB,EAAasL,GAE5C,IAAM1D,EAAaxF,EAAQA,SAACsD,cAAchE,KAAK1B,GAE/C,OAAI4H,EAEO2D,WAAW3D,EAAW,SAGT7D,IAAjBuH,EAA6BA,EAAe,mBCRvC,SAAQE,EAAaC,GAMjC,YANiC,IAAAA,IAAAA,EAAsC,IAEvEA,EAAI,IAAOD,GAAO,GAAM,KAAQ,IAChCC,EAAI,IAAOD,GAAO,EAAK,KAAQ,IAC/BC,EAAI,IAAY,IAAND,GAAc,IAEjBC,sBAYL,SAAqBD,GAEvB,IAAIE,EAAYF,EAAIG,SAAS,IAI7B,MAAO,KAFPD,EAAY,SAASE,UAAU,EAAG,EAAIF,EAAU3J,QAAU2J,kCV3B9C,SAAsBG,EAAuBC,GAMzD,IAJA,IAAIC,EAAU,EACVC,EAAS,EACPC,EAAsC,GAEnC5I,EAAI,EAAGA,EAAIwI,EAAO9J,OAAQsB,IAE/B2I,GAAUF,EAAMzI,GAChB0I,GAAWF,EAAOxI,GAAGtB,OAGzB,IAAMmK,EAAS,IAAIC,YAAsB,EAAVJ,GAE3BN,EAAM,KACNW,EAAe,EAEnB,IAAS/I,EAAI,EAAGA,EAAIwI,EAAO9J,OAAQsB,IACnC,CACI,IAAMgG,EAAOyC,EAAMzI,GACbmD,EAAQqF,EAAOxI,GAMfgJ,EAAO1F,EAAcH,GAEtByF,EAAMI,KAEPJ,EAAMI,GAAQ,IAAIpF,EAAIoF,GAAMH,IAGhCT,EAAMQ,EAAMI,GAEZ,IAAK,IAAI5C,EAAI,EAAGA,EAAIjD,EAAMzE,OAAQ0H,IAClC,CAIIgC,GAHqBhC,EAAIJ,EAAO,GAAK2C,EAAUI,EACjC3C,EAAIJ,GAEQ7C,EAAMiD,GAGpC2C,GAAgB/C,EAGpB,OAAO,IAAIxC,aAAaqF,mBWxBtB,SAAiBI,GAEnB,QAASA,EAAKA,EAAI,IAAUA,wCd0B5B,YA7CyB,IAAd1G,IAEPA,EAAY,WAER,IAAM2G,EAAiB,CACnBC,SAAS,EACTC,6BAA8BrK,EAAQA,SAACuD,kCAG3C,IAEI,IAAKvD,EAAQA,SAACC,QAAQqK,2BAElB,OAAO,EAGX,IAAM7E,EAASzF,EAAAA,SAASC,QAAQyF,eAC5B6E,EACA9E,EAAOG,WAAW,QAASuE,IACxB1E,EAAOG,WAAW,qBAAsBuE,GAGzCK,KAAaD,IAAMA,EAAGE,uBAAuBL,SAEnD,GAAIG,EACJ,CACI,IAAMG,EAAcH,EAAGI,aAAa,sBAEhCD,GAEAA,EAAYA,cAMpB,OAFAH,EAAK,KAEEC,EAEX,MAAOI,GAEH,OAAO,GAtCH,IA2CTpH,gBchBL,SAAe0G,GAEjB,IAAIW,GAAKX,EAAI,MAAS,EAAI,IAAM,EAI5BY,IAFJZ,KAAOW,GAEU,IAAO,EAAI,IAAM,EAQlC,OANcA,GAAKC,EAELD,GADdC,IADAZ,KAAOY,GACM,GAAM,EAAI,IAAM,GAGfD,GADdC,IADAZ,KAAOY,GACM,EAAM,EAAI,IAAM,IAC7BZ,KAAOY,IAEU,oBA9Cf,SAAmBZ,GAUrB,OARAA,GAAW,IAANA,EAAU,EAAI,IACjBA,EACFA,GAAKA,IAAM,EACXA,GAAKA,IAAM,EACXA,GAAKA,IAAM,EACXA,GAAKA,IAAM,GACXA,GAAKA,IAAM,IAEA,yEbgDT,SACFa,EACAC,EACA3B,EACA4B,GAkBA,OAfA5B,EAAMA,GAAO,IAAI5E,aAAa,GAC1BwG,QAA+BtJ,IAAhBsJ,GAEf5B,EAAI,GAAK0B,EAAI,GAAKC,EAClB3B,EAAI,GAAK0B,EAAI,GAAKC,EAClB3B,EAAI,GAAK0B,EAAI,GAAKC,IAIlB3B,EAAI,GAAK0B,EAAI,GACb1B,EAAI,GAAK0B,EAAI,GACb1B,EAAI,GAAK0B,EAAI,IAEjB1B,EAAI,GAAK2B,EAEF3B,2BAWK,SAAgB6B,EAAcF,GAE1C,GAAc,IAAVA,EAEA,OAAgB,IAARA,GAAe,IAAME,EAEjC,GAAc,IAAVF,EAEA,OAAO,EAEX,IAAIG,EAAMD,GAAQ,GAAM,IACpBE,EAAMF,GAAQ,EAAK,IACnBG,EAAY,IAAPH,EAMT,OAAgB,IAARF,GAAe,MAJvBG,EAAMA,EAAIH,EAAS,GAAO,IAIS,MAHnCI,EAAMA,EAAIJ,EAAS,GAAO,IAGqB,IAF/CK,EAAMA,EAAIL,EAAS,GAAO,kCAexB,SAAgCE,EAAcF,EAAe3B,EAAmB4B,GAclF,OAZA5B,EAAMA,GAAO,IAAI5E,aAAa,IAC1B,IAAOyG,GAAQ,GAAM,KAAQ,IACjC7B,EAAI,IAAO6B,GAAQ,EAAK,KAAQ,IAChC7B,EAAI,IAAa,IAAP6B,GAAe,KACrBD,QAA+BtJ,IAAhBsJ,KAEf5B,EAAI,IAAM2B,EACV3B,EAAI,IAAM2B,EACV3B,EAAI,IAAM2B,GAEd3B,EAAI,GAAK2B,EAEF3B,gCczIiB5J,EAAY6L,EAAkBC,GAEtD,IACItK,EADEtB,EAASF,EAAIE,OAGnB,KAAI2L,GAAY3L,GAA0B,IAAhB4L,GAA1B,CAOA,IAAMC,EAAM7L,GAFZ4L,EAAeD,EAAWC,EAAc5L,EAASA,EAAS2L,EAAWC,GAIrE,IAAKtK,EAAIqK,EAAUrK,EAAIuK,IAAOvK,EAE1BxB,EAAIwB,GAAKxB,EAAIwB,EAAIsK,GAGrB9L,EAAIE,OAAS6L,oBFkDX,SAAkBT,GAEpB,OAAmB,IAATA,EAAI,IAAa,KAAiB,IAATA,EAAI,IAAa,IAAe,IAATA,EAAI,GAAW,qBdvDvE,SAAmBd,SAErB,IAAIxG,EAAJ,CAKA,GAAIzD,WAASC,QAAQwL,eAAeC,UAAUjE,cAAclF,QAAQ,WAAa,EACjF,CACI,IAAMoJ,EAAO,CACT,iCAAqC1B,EAA4D,yDACjG,sCACA,sCACA,sDACA,sCACA,sCACA,sCACA,mDACA,mDACA,qDAGJpI,EAAA2G,WAAWN,SAAQ0D,IAAOC,MAAAhK,EAAA8J,QAErBnD,WAAWN,SAEhBM,WAAWN,QAAQ0D,IAAI,mBAAuB3B,EAA+B,6BAGjFxG,GAAY,iBiB9CV,SAAeqI,GAEjB,OAAU,IAANA,EAAgB,EAEbA,EAAI,GAAK,EAAI,gCjBCpBrI,GAAY,sBcyCV,SAAqBsI,GAYvB,MAVsB,iBAAXA,GAIW,OAFlBA,EAAUC,EAA0CD,EAAOtE,gBAAkBsE,GAElE,KAEPA,EAASA,EAAOrM,MAAM,IAIvBuM,SAASF,EAAQ,wBIlDtB,SAAqBtG,GAIvB,IAiBIxE,EACAiL,EACAC,EAnBA7G,EAAQG,EAAOH,MACfC,EAASE,EAAOF,OAEdI,EAAUF,EAAOG,WAAW,KAAM,CACpCwG,oBAAoB,IAGlBC,EADY1G,EAAQ2G,aAAa,EAAG,EAAGhH,EAAOC,GAC3BsC,KACnB2D,EAAMa,EAAO1M,OAEb4M,EAAe,CACjBC,IAAK,KACLC,KAAM,KACNC,MAAO,KACPC,OAAQ,MAER9E,EAAO,KAKX,IAAK5G,EAAI,EAAGA,EAAIuK,EAAKvK,GAAK,EAEA,IAAlBoL,EAAOpL,EAAI,KAEXiL,EAAKjL,EAAI,EAAKqE,EACd6G,KAAQlL,EAAI,EAAKqE,GAEC,OAAdiH,EAAMC,MAEND,EAAMC,IAAML,IAGG,OAAfI,EAAME,MAIDP,EAAIK,EAAME,QAFfF,EAAME,KAAOP,IAOG,OAAhBK,EAAMG,OAIDH,EAAMG,MAAQR,KAFnBK,EAAMG,MAAQR,EAAI,IAOD,OAAjBK,EAAMI,QAIDJ,EAAMI,OAASR,KAFpBI,EAAMI,OAASR,IAgB3B,OAPkB,OAAdI,EAAMC,MAENlH,EAAQiH,EAAMG,MAAQH,EAAME,KAC5BlH,EAASgH,EAAMI,OAASJ,EAAMC,IAAM,EACpC3E,EAAOlC,EAAQ2G,aAAaC,EAAME,KAAMF,EAAMC,IAAKlH,EAAOC,IAGvD,CACHA,OAAMA,EACND,MAAKA,EACLuC,KAAIA,2BbjFR,QAAS9C"}