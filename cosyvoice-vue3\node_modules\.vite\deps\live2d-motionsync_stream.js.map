{"version": 3, "sources": ["../../live2d-motionsync/dist/stream.es.js"], "sourcesContent": ["var h = Object.defineProperty;\nvar d = (s, t, e) => t in s ? h(s, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : s[t] = e;\nvar o = (s, t, e) => d(s, typeof t != \"symbol\" ? t + \"\" : t, e);\nimport { C as r, M as f, a as p, f as _, c as y } from \"./fallback.motionsync3-1oooNFSa.js\";\nconst m = `/**\n * Copyright(c) Live2D Inc. All rights reserved.\n *\n * Use of this source code is governed by the Live2D Open Software license\n * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.\n */\n\nclass LAppAudioWorkletProcessor extends AudioWorkletProcessor {\n  constructor() {\n    super();\n    this.useChannel = 0;\n  }\n\n  process(inputs, outputs, parameters) {\n    const channel = this.useChannel % inputs[0].length;\n    const input = inputs[0][channel];\n    if (input == undefined || input == null) {\n      return true;\n    }\n\n    // 後ろに追加する\n    const audioBuffer = Float32Array.from([...input]);\n\n    this.port.postMessage({\n      eventType: \"data\",\n      audioBuffer: audioBuffer,\n    });\n\n    let inputArray = inputs[0];\n    let output = outputs[0];\n    for (let currentChannel = 0; currentChannel < inputArray.length; ++currentChannel) {\n      let inputChannel = inputArray[currentChannel];\n      let outputChannel = output[currentChannel];\n      for (let i = 0; i < inputChannel.length; ++i){\n        outputChannel[i] = inputChannel[i];\n      }\n    }\n\n    return true;\n  }\n}\n\nregisterProcessor('lappaudioworkletprocessor', LAppAudioWorkletProcessor);\n`, a = 48e3;\nclass g {\n  constructor(t) {\n    o(this, \"_motionSync\", null);\n    o(this, \"_internalModel\");\n    o(this, \"_model\");\n    o(this, \"_context\", null);\n    o(this, \"_source\", null);\n    o(this, \"_buffer\", null);\n    this._internalModel = t, this._model = t.coreModel, r.startUp(new f()), r.initialize();\n  }\n  async play(t) {\n    (this._source || this._context) && this.reset();\n    const e = t.getAudioTracks();\n    if (e.length == 0) {\n      p(\"没有找到音频轨道.\");\n      return;\n    }\n    const n = 48e3, i = 30, c = 2;\n    this._buffer = new M(\n      Math.trunc(n / i * c)\n    ), this._context = new AudioContext({ sampleRate: n }), this._source = this._context.createMediaStreamSource(\n      new MediaStream([e[0]])\n    );\n    const l = URL.createObjectURL(\n      new Blob([m], {\n        type: \"application/javascript\"\n      })\n    );\n    await this._context.audioWorklet.addModule(l);\n    const u = new AudioWorkletNode(\n      this._context,\n      \"lappaudioworkletprocessor\"\n    );\n    this._source.connect(u), u.port.onmessage = this.onMessage.bind(this);\n  }\n  async reset() {\n    this._source && (this._source.disconnect(), this._source = null), this._context && (this._context.close(), this._context = null), this._buffer = null, this.resetMouthStatus();\n  }\n  resetMouthStatus() {\n    try {\n      if (!this._motionSync) return;\n      const t = this._motionSync.getData().getSetting(0);\n      if (!t) return;\n      const e = t.cubismParameterList;\n      if (!e) return;\n      const n = e._ptr.map(\n        (i) => i.parameterIndex\n      );\n      for (const i of n)\n        this._model.setParameterValueByIndex(i, 0);\n    } catch (t) {\n      console.error(t);\n    }\n  }\n  pop() {\n    if (!this._buffer)\n      return;\n    const t = this._buffer.toVector();\n    return this._buffer.clear(), t;\n  }\n  onMessage(t) {\n    const e = t.data;\n    if (e.eventType === \"data\" && e.audioBuffer)\n      for (let n = 0; n < e.audioBuffer.length; n++)\n        this._buffer.addLast(e.audioBuffer[n]);\n  }\n  updateMotionSync() {\n    const t = performance.now() / 1e3, e = this.pop();\n    e && (this._motionSync.setSoundBuffer(0, e, 0), this._motionSync.updateParameters(this._model, t));\n  }\n  modelUpdateWithMotionSync() {\n    if (!this._motionSync) return;\n    const e = this._internalModel, n = e.motionManager.update;\n    e.motionManager.update = (...i) => {\n      n.apply(this._internalModel.motionManager, i), this.updateMotionSync();\n    };\n  }\n  loadMotionSync(t, e = a) {\n    if (t == null || t.byteLength == 0) {\n      console.warn(\"Failed to loadMotionSync().\");\n      return;\n    }\n    this._motionSync = r.create(\n      this._model,\n      t,\n      t.byteLength,\n      e\n    ), this.modelUpdateWithMotionSync();\n  }\n  async loadDefaultMotionSync(t = a) {\n    const n = await new Blob([_], { type: \"application/json\" }).arrayBuffer();\n    this.loadMotionSync(n, t);\n  }\n  async loadMotionSyncFromUrl(t, e = a) {\n    try {\n      const i = await (await fetch(t)).arrayBuffer();\n      this.loadMotionSync(i, e);\n    } catch {\n      console.warn(\"Failed to loadMotionSync(). Use default fallback.\"), await this.loadDefaultMotionSync(e);\n    }\n  }\n}\nclass M {\n  constructor(t) {\n    o(this, \"_buffer\");\n    o(this, \"_size\");\n    o(this, \"_head\");\n    this._buffer = new Float32Array(t), this._size = 0, this._head = 0;\n  }\n  get size() {\n    return this._size;\n  }\n  addLast(t) {\n    this._buffer[this._head] = t, this._size = Math.min(this._size + 1, this._buffer.length), this._head++, this._head >= this._buffer.length && (this._head = 0);\n  }\n  toVector() {\n    const t = new y(this._size);\n    let e = this._head - this._size;\n    e < 0 && (e += this._buffer.length);\n    for (let n = 0; n < this._size; n++)\n      t.pushBack(this._buffer[e]), e++, e >= this._buffer.length && (e = 0);\n    return t;\n  }\n  clear() {\n    this._size = 0, this._head = 0;\n  }\n}\nexport {\n  g as MotionSync\n};\n"], "mappings": ";;;;;;;;;;AAAA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,EAAE,GAAG,GAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAC7G,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,EAAE,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC;AAE9D,IAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAV,IA2CG,IAAI;AACP,IAAM,IAAN,MAAQ;AAAA,EACN,YAAY,GAAG;AACb,MAAE,MAAM,eAAe,IAAI;AAC3B,MAAE,MAAM,gBAAgB;AACxB,MAAE,MAAM,QAAQ;AAChB,MAAE,MAAM,YAAY,IAAI;AACxB,MAAE,MAAM,WAAW,IAAI;AACvB,MAAE,MAAM,WAAW,IAAI;AACvB,SAAK,iBAAiB,GAAG,KAAK,SAAS,EAAE,WAAW,EAAE,QAAQ,IAAI,GAAE,CAAC,GAAG,EAAE,WAAW;AAAA,EACvF;AAAA,EACA,MAAM,KAAK,GAAG;AACZ,KAAC,KAAK,WAAW,KAAK,aAAa,KAAK,MAAM;AAC9C,UAAM,IAAI,EAAE,eAAe;AAC3B,QAAI,EAAE,UAAU,GAAG;AACjB,QAAE,WAAW;AACb;AAAA,IACF;AACA,UAAM,IAAI,MAAM,IAAI,IAAI,IAAI;AAC5B,SAAK,UAAU,IAAI;AAAA,MACjB,KAAK,MAAM,IAAI,IAAI,CAAC;AAAA,IACtB,GAAG,KAAK,WAAW,IAAI,aAAa,EAAE,YAAY,EAAE,CAAC,GAAG,KAAK,UAAU,KAAK,SAAS;AAAA,MACnF,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IACxB;AACA,UAAMA,KAAI,IAAI;AAAA,MACZ,IAAI,KAAK,CAAC,CAAC,GAAG;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,UAAM,KAAK,SAAS,aAAa,UAAUA,EAAC;AAC5C,UAAMC,KAAI,IAAI;AAAA,MACZ,KAAK;AAAA,MACL;AAAA,IACF;AACA,SAAK,QAAQ,QAAQA,EAAC,GAAGA,GAAE,KAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EACtE;AAAA,EACA,MAAM,QAAQ;AACZ,SAAK,YAAY,KAAK,QAAQ,WAAW,GAAG,KAAK,UAAU,OAAO,KAAK,aAAa,KAAK,SAAS,MAAM,GAAG,KAAK,WAAW,OAAO,KAAK,UAAU,MAAM,KAAK,iBAAiB;AAAA,EAC/K;AAAA,EACA,mBAAmB;AACjB,QAAI;AACF,UAAI,CAAC,KAAK,YAAa;AACvB,YAAM,IAAI,KAAK,YAAY,QAAQ,EAAE,WAAW,CAAC;AACjD,UAAI,CAAC,EAAG;AACR,YAAM,IAAI,EAAE;AACZ,UAAI,CAAC,EAAG;AACR,YAAM,IAAI,EAAE,KAAK;AAAA,QACf,CAAC,MAAM,EAAE;AAAA,MACX;AACA,iBAAW,KAAK;AACd,aAAK,OAAO,yBAAyB,GAAG,CAAC;AAAA,IAC7C,SAAS,GAAG;AACV,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF;AAAA,EACA,MAAM;AACJ,QAAI,CAAC,KAAK;AACR;AACF,UAAM,IAAI,KAAK,QAAQ,SAAS;AAChC,WAAO,KAAK,QAAQ,MAAM,GAAG;AAAA,EAC/B;AAAA,EACA,UAAU,GAAG;AACX,UAAM,IAAI,EAAE;AACZ,QAAI,EAAE,cAAc,UAAU,EAAE;AAC9B,eAAS,IAAI,GAAG,IAAI,EAAE,YAAY,QAAQ;AACxC,aAAK,QAAQ,QAAQ,EAAE,YAAY,CAAC,CAAC;AAAA,EAC3C;AAAA,EACA,mBAAmB;AACjB,UAAM,IAAI,YAAY,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI;AAChD,UAAM,KAAK,YAAY,eAAe,GAAG,GAAG,CAAC,GAAG,KAAK,YAAY,iBAAiB,KAAK,QAAQ,CAAC;AAAA,EAClG;AAAA,EACA,4BAA4B;AAC1B,QAAI,CAAC,KAAK,YAAa;AACvB,UAAM,IAAI,KAAK,gBAAgB,IAAI,EAAE,cAAc;AACnD,MAAE,cAAc,SAAS,IAAI,MAAM;AACjC,QAAE,MAAM,KAAK,eAAe,eAAe,CAAC,GAAG,KAAK,iBAAiB;AAAA,IACvE;AAAA,EACF;AAAA,EACA,eAAe,GAAG,IAAI,GAAG;AACvB,QAAI,KAAK,QAAQ,EAAE,cAAc,GAAG;AAClC,cAAQ,KAAK,6BAA6B;AAC1C;AAAA,IACF;AACA,SAAK,cAAc,EAAE;AAAA,MACnB,KAAK;AAAA,MACL;AAAA,MACA,EAAE;AAAA,MACF;AAAA,IACF,GAAG,KAAK,0BAA0B;AAAA,EACpC;AAAA,EACA,MAAM,sBAAsB,IAAI,GAAG;AACjC,UAAM,IAAI,MAAM,IAAI,KAAK,CAAC,EAAC,GAAG,EAAE,MAAM,mBAAmB,CAAC,EAAE,YAAY;AACxE,SAAK,eAAe,GAAG,CAAC;AAAA,EAC1B;AAAA,EACA,MAAM,sBAAsB,GAAG,IAAI,GAAG;AACpC,QAAI;AACF,YAAM,IAAI,OAAO,MAAM,MAAM,CAAC,GAAG,YAAY;AAC7C,WAAK,eAAe,GAAG,CAAC;AAAA,IAC1B,QAAQ;AACN,cAAQ,KAAK,mDAAmD,GAAG,MAAM,KAAK,sBAAsB,CAAC;AAAA,IACvG;AAAA,EACF;AACF;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,YAAY,GAAG;AACb,MAAE,MAAM,SAAS;AACjB,MAAE,MAAM,OAAO;AACf,MAAE,MAAM,OAAO;AACf,SAAK,UAAU,IAAI,aAAa,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,EACnE;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ,GAAG;AACT,SAAK,QAAQ,KAAK,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,GAAG,KAAK,QAAQ,MAAM,GAAG,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,WAAW,KAAK,QAAQ;AAAA,EAC7J;AAAA,EACA,WAAW;AACT,UAAM,IAAI,IAAI,EAAE,KAAK,KAAK;AAC1B,QAAI,IAAI,KAAK,QAAQ,KAAK;AAC1B,QAAI,MAAM,KAAK,KAAK,QAAQ;AAC5B,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO;AAC9B,QAAE,SAAS,KAAK,QAAQ,CAAC,CAAC,GAAG,KAAK,KAAK,KAAK,QAAQ,WAAW,IAAI;AACrE,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,SAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,EAC/B;AACF;", "names": ["l", "u"]}