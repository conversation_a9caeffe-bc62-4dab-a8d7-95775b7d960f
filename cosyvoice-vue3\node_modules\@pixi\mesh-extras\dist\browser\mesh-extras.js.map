{"version": 3, "file": "mesh-extras.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/geometry/PlaneGeometry.ts", "../../src/geometry/RopeGeometry.ts", "../../src/SimpleRope.ts", "../../src/SimplePlane.ts", "../../src/SimpleMesh.ts", "../../src/NineSlicePlane.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { MeshGeometry } from '@pixi/mesh';\n\n/**\n * @memberof PIXI\n */\nexport class PlaneGeometry extends MeshGeometry\n{\n    public segWidth: number;\n    public segHeight: number;\n    public width: number;\n    public height: number;\n\n    /**\n     * @param width - The width of the plane.\n     * @param height - The height of the plane.\n     * @param segWidth - Number of horizontal segments.\n     * @param segHeight - Number of vertical segments.\n     */\n    constructor(width = 100, height = 100, segWidth = 10, segHeight = 10)\n    {\n        super();\n\n        this.segWidth = segWidth;\n        this.segHeight = segHeight;\n\n        this.width = width;\n        this.height = height;\n\n        this.build();\n    }\n\n    /**\n     * Refreshes plane coordinates\n     * @private\n     */\n    build(): void\n    {\n        const total = this.segWidth * this.segHeight;\n        const verts = [];\n        const uvs = [];\n        const indices = [];\n\n        const segmentsX = this.segWidth - 1;\n        const segmentsY = this.segHeight - 1;\n\n        const sizeX = (this.width) / segmentsX;\n        const sizeY = (this.height) / segmentsY;\n\n        for (let i = 0; i < total; i++)\n        {\n            const x = (i % this.segWidth);\n            const y = ((i / this.segWidth) | 0);\n\n            verts.push(x * sizeX, y * sizeY);\n            uvs.push(x / segmentsX, y / segmentsY);\n        }\n\n        const totalSub = segmentsX * segmentsY;\n\n        for (let i = 0; i < totalSub; i++)\n        {\n            const xpos = i % segmentsX;\n            const ypos = (i / segmentsX) | 0;\n\n            const value = (ypos * this.segWidth) + xpos;\n            const value2 = (ypos * this.segWidth) + xpos + 1;\n            const value3 = ((ypos + 1) * this.segWidth) + xpos;\n            const value4 = ((ypos + 1) * this.segWidth) + xpos + 1;\n\n            indices.push(value, value2, value3,\n                value2, value4, value3);\n        }\n\n        this.buffers[0].data = new Float32Array(verts);\n        this.buffers[1].data = new Float32Array(uvs);\n        this.indexBuffer.data = new Uint16Array(indices);\n\n        // ensure that the changes are uploaded\n        this.buffers[0].update();\n        this.buffers[1].update();\n        this.indexBuffer.update();\n    }\n}\n", "import { MeshGeometry } from '@pixi/mesh';\nimport type { IPoint } from '@pixi/math';\n\n/**\n * RopeGeometry allows you to draw a geometry across several points and then manipulate these points.\n *\n * ```js\n * for (let i = 0; i < 20; i++) {\n *     points.push(new PIXI.Point(i * 50, 0));\n * };\n * const rope = new PIXI.RopeGeometry(100, points);\n * ```\n * @memberof PIXI\n */\nexport class RopeGeometry extends MeshGeometry\n{\n    /** An array of points that determine the rope. */\n    public points: IPoint[];\n\n    /** Rope texture scale, if zero then the rope texture is stretched. */\n    public readonly textureScale: number;\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    _width: number;\n\n    /**\n     * @param width - The width (i.e., thickness) of the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param textureScale - By default the rope texture will be stretched to match\n     *     rope length. If textureScale is positive this value will be treated as a scaling\n     *     factor and the texture will preserve its aspect ratio instead. To create a tiling rope\n     *     set baseTexture.wrapMode to {@link PIXI.WRAP_MODES.REPEAT} and use a power of two texture,\n     *     then set textureScale=1 to keep the original texture pixel size.\n     *     In order to reduce alpha channel artifacts provide a larger texture and downsample -\n     *     i.e. set textureScale=0.5 to scale it down twice.\n     */\n    constructor(width = 200, points: IPoint[], textureScale = 0)\n    {\n        super(new Float32Array(points.length * 4),\n            new Float32Array(points.length * 4),\n            new Uint16Array((points.length - 1) * 6));\n\n        this.points = points;\n        this._width = width;\n        this.textureScale = textureScale;\n\n        this.build();\n    }\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    /** Refreshes Rope indices and uvs */\n    private build(): void\n    {\n        const points = this.points;\n\n        if (!points) return;\n\n        const vertexBuffer = this.getBuffer('aVertexPosition');\n        const uvBuffer = this.getBuffer('aTextureCoord');\n        const indexBuffer = this.getIndex();\n\n        // if too little points, or texture hasn't got UVs set yet just move on.\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        // if the number of points has changed we will need to recreate the arraybuffers\n        if (vertexBuffer.data.length / 4 !== points.length)\n        {\n            vertexBuffer.data = new Float32Array(points.length * 4);\n            uvBuffer.data = new Float32Array(points.length * 4);\n            indexBuffer.data = new Uint16Array((points.length - 1) * 6);\n        }\n\n        const uvs = uvBuffer.data;\n        const indices = indexBuffer.data;\n\n        uvs[0] = 0;\n        uvs[1] = 0;\n        uvs[2] = 0;\n        uvs[3] = 1;\n\n        let amount = 0;\n        let prev = points[0];\n        const textureWidth = this._width * this.textureScale;\n        const total = points.length; // - 1;\n\n        for (let i = 0; i < total; i++)\n        {\n            // time to do some smart drawing!\n            const index = i * 4;\n\n            if (this.textureScale > 0)\n            {\n                // calculate pixel distance from previous point\n                const dx = prev.x - points[i].x;\n                const dy = prev.y - points[i].y;\n                const distance = Math.sqrt((dx * dx) + (dy * dy));\n\n                prev = points[i];\n                amount += distance / textureWidth;\n            }\n            else\n            {\n                // stretch texture\n                amount = i / (total - 1);\n            }\n\n            uvs[index] = amount;\n            uvs[index + 1] = 0;\n\n            uvs[index + 2] = amount;\n            uvs[index + 3] = 1;\n        }\n\n        let indexCount = 0;\n\n        for (let i = 0; i < total - 1; i++)\n        {\n            const index = i * 2;\n\n            indices[indexCount++] = index;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 2;\n\n            indices[indexCount++] = index + 2;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 3;\n        }\n\n        // ensure that the changes are uploaded\n        uvBuffer.update();\n        indexBuffer.update();\n\n        this.updateVertices();\n    }\n\n    /** refreshes vertices of Rope mesh */\n    public updateVertices(): void\n    {\n        const points = this.points;\n\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        let lastPoint = points[0];\n        let nextPoint;\n        let perpX = 0;\n        let perpY = 0;\n\n        const vertices = this.buffers[0].data;\n        const total = points.length;\n\n        for (let i = 0; i < total; i++)\n        {\n            const point = points[i];\n            const index = i * 4;\n\n            if (i < points.length - 1)\n            {\n                nextPoint = points[i + 1];\n            }\n            else\n            {\n                nextPoint = point;\n            }\n\n            perpY = -(nextPoint.x - lastPoint.x);\n            perpX = nextPoint.y - lastPoint.y;\n\n            let ratio = (1 - (i / (total - 1))) * 10;\n\n            if (ratio > 1)\n            {\n                ratio = 1;\n            }\n\n            const perpLength = Math.sqrt((perpX * perpX) + (perpY * perpY));\n            const num = this.textureScale > 0 ? this.textureScale * this._width / 2 : this._width / 2;\n\n            perpX /= perpLength;\n            perpY /= perpLength;\n\n            perpX *= num;\n            perpY *= num;\n\n            vertices[index] = point.x + perpX;\n            vertices[index + 1] = point.y + perpY;\n            vertices[index + 2] = point.x - perpX;\n            vertices[index + 3] = point.y - perpY;\n\n            lastPoint = point;\n        }\n\n        this.buffers[0].update();\n    }\n\n    public update(): void\n    {\n        if (this.textureScale > 0)\n        {\n            this.build(); // we need to update UVs\n        }\n        else\n        {\n            this.updateVertices();\n        }\n    }\n}\n", "import { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { WRAP_MODES } from '@pixi/constants';\nimport { RopeGeometry } from './geometry/RopeGeometry';\n\nimport type { Texture, Renderer } from '@pixi/core';\nimport type { IPoint } from '@pixi/math';\n\n/**\n * The rope allows you to draw a texture across several points and then manipulate these points\n *\n *```js\n * for (let i = 0; i < 20; i++) {\n *     points.push(new PIXI.Point(i * 50, 0));\n * };\n * let rope = new PIXI.SimpleRope(PIXI.Texture.from(\"snake.png\"), points);\n *  ```\n * @memberof PIXI\n */\nexport class SimpleRope extends Mesh\n{\n    public autoUpdate: boolean;\n\n    /**\n     * @param texture - The texture to use on the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param {number} textureScale - Optional. Positive values scale rope texture\n     * keeping its aspect ratio. You can reduce alpha channel artifacts by providing a larger texture\n     * and downsampling here. If set to zero, texture will be stretched instead.\n     */\n    constructor(texture: Texture, points: IPoint[], textureScale = 0)\n    {\n        const ropeGeometry = new RopeGeometry(texture.height, points, textureScale);\n        const meshMaterial = new MeshMaterial(texture);\n\n        if (textureScale > 0)\n        {\n            // attempt to set UV wrapping, will fail on non-power of two textures\n            texture.baseTexture.wrapMode = WRAP_MODES.REPEAT;\n        }\n        super(ropeGeometry, meshMaterial);\n\n        /**\n         * re-calculate vertices by rope points each frame\n         * @member {boolean}\n         */\n        this.autoUpdate = true;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        const geometry: RopeGeometry = this.geometry as any;\n\n        if (this.autoUpdate || geometry._width !== this.shader.texture.height)\n        {\n            geometry._width = this.shader.texture.height;\n            geometry.update();\n        }\n\n        super._render(renderer);\n    }\n}\n", "import { Texture } from '@pixi/core';\nimport { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { PlaneGeometry } from './geometry/PlaneGeometry';\n\nimport type{ Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * The SimplePlane allows you to draw a texture across several points and then manipulate these points\n *\n *```js\n * for (let i = 0; i < 20; i++) {\n *     points.push(new PIXI.Point(i * 50, 0));\n * };\n * let SimplePlane = new PIXI.SimplePlane(PIXI.Texture.from(\"snake.png\"), points);\n *  ```\n * @memberof PIXI\n */\nexport class SimplePlane extends Mesh\n{\n    /** The geometry is automatically updated when the texture size changes. */\n    public autoResize: boolean;\n\n    protected _textureID: number;\n\n    /**\n     * @param texture - The texture to use on the SimplePlane.\n     * @param verticesX - The number of vertices in the x-axis\n     * @param verticesY - The number of vertices in the y-axis\n     */\n    constructor(texture: Texture, verticesX?: number, verticesY?: number)\n    {\n        const planeGeometry = new PlaneGeometry(texture.width, texture.height, verticesX, verticesY);\n        const meshMaterial = new MeshMaterial(Texture.WHITE);\n\n        super(planeGeometry, meshMaterial);\n\n        // lets call the setter to ensure all necessary updates are performed\n        this.texture = texture;\n        this.autoResize = true;\n    }\n\n    /**\n     * Method used for overrides, to do something in case texture frame was changed.\n     * Meshes based on plane can override it and change more details based on texture.\n     */\n    public textureUpdated(): void\n    {\n        this._textureID = this.shader.texture._updateID;\n\n        const geometry: PlaneGeometry = this.geometry as any;\n        const { width, height } = this.shader.texture;\n\n        if (this.autoResize && (geometry.width !== width || geometry.height !== height))\n        {\n            geometry.width = this.shader.texture.width;\n            geometry.height = this.shader.texture.height;\n            geometry.build();\n        }\n    }\n\n    set texture(value: Texture)\n    {\n        // Track texture same way sprite does.\n        // For generated meshes like NineSlicePlane it can change the geometry.\n        // Unfortunately, this method might not work if you directly change texture in material.\n\n        if (this.shader.texture === value)\n        {\n            return;\n        }\n\n        this.shader.texture = value;\n        this._textureID = -1;\n\n        if (value.baseTexture.valid)\n        {\n            this.textureUpdated();\n        }\n        else\n        {\n            value.once('update', this.textureUpdated, this);\n        }\n    }\n\n    get texture(): Texture\n    {\n        return this.shader.texture;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this._textureID !== this.shader.texture._updateID)\n        {\n            this.textureUpdated();\n        }\n\n        super._render(renderer);\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this.shader.texture.off('update', this.textureUpdated, this);\n        super.destroy(options);\n    }\n}\n", "import { Mesh, MeshGeometry, MeshMaterial } from '@pixi/mesh';\nimport { Texture } from '@pixi/core';\n\nimport type { ITypedArray, <PERSON><PERSON><PERSON><PERSON><PERSON>uffer, Renderer } from '@pixi/core';\nimport type { DRAW_MODES } from '@pixi/constants';\n\n/**\n * The Simple Mesh class mimics Mesh in PixiJS v4, providing easy-to-use constructor arguments.\n * For more robust customization, use {@link PIXI.Mesh}.\n * @memberof PIXI\n */\nexport class SimpleMesh extends Mesh\n{\n    /** Upload vertices buffer each frame. */\n    public autoUpdate: boolean;\n\n    /**\n     * @param texture - The texture to use\n     * @param {Float32Array} [vertices] - if you want to specify the vertices\n     * @param {Float32Array} [uvs] - if you want to specify the uvs\n     * @param {Uint16Array} [indices] - if you want to specify the indices\n     * @param drawMode - the drawMode, can be any of the Mesh.DRAW_MODES consts\n     */\n    constructor(\n        texture: Texture = Texture.EMPTY,\n        vertices?: I<PERSON><PERSON><PERSON><PERSON>uffer,\n        uvs?: IArrayBuffer,\n        indices?: I<PERSON><PERSON>yBuffer,\n        drawMode?: DRAW_MODES\n    )\n    {\n        const geometry = new MeshGeometry(vertices, uvs, indices);\n\n        geometry.getBuffer('aVertexPosition').static = false;\n\n        const meshMaterial = new MeshMaterial(texture);\n\n        super(geometry, meshMaterial, null, drawMode);\n\n        this.autoUpdate = true;\n    }\n\n    /**\n     * Collection of vertices data.\n     * @type {Float32Array}\n     */\n    get vertices(): ITypedArray\n    {\n        return this.geometry.getBuffer('aVertexPosition').data;\n    }\n    set vertices(value: ITypedArray)\n    {\n        this.geometry.getBuffer('aVertexPosition').data = value;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this.autoUpdate)\n        {\n            this.geometry.getBuffer('aVertexPosition').update();\n        }\n\n        super._render(renderer);\n    }\n}\n", "import { Texture } from '@pixi/core';\nimport { SimplePlane } from './SimplePlane';\n\nimport type { ITypedArray } from '@pixi/core';\n\nconst DEFAULT_BORDER_SIZE = 10;\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface NineSlicePlane extends GlobalMixins.NineSlicePlane {}\n\n/**\n * The NineSlicePlane allows you to stretch a texture using 9-slice scaling. The corners will remain unscaled (useful\n * for buttons with rounded corners for example) and the other areas will be scaled horizontally and or vertically\n *\n *```js\n * let Plane9 = new PIXI.NineSlicePlane(PIXI.Texture.from('BoxWithRoundedCorners.png'), 15, 15, 15, 15);\n *  ```\n * <pre>\n *      A                          B\n *    +---+----------------------+---+\n *  C | 1 |          2           | 3 |\n *    +---+----------------------+---+\n *    |   |                      |   |\n *    | 4 |          5           | 6 |\n *    |   |                      |   |\n *    +---+----------------------+---+\n *  D | 7 |          8           | 9 |\n *    +---+----------------------+---+\n *  When changing this objects width and/or height:\n *     areas 1 3 7 and 9 will remain unscaled.\n *     areas 2 and 8 will be stretched horizontally\n *     areas 4 and 6 will be stretched vertically\n *     area 5 will be stretched both horizontally and vertically\n * </pre>\n * @memberof PIXI\n */\nexport class NineSlicePlane extends SimplePlane\n{\n    private _origWidth: number;\n    private _origHeight: number;\n\n    /**\n     * The width of the left column (a).\n     * @private\n     */\n    _leftWidth: number;\n\n    /**\n     * The width of the right column (b)\n     * @private\n     */\n    _rightWidth: number;\n\n    /**\n     * The height of the top row (c)\n     * @private\n     */\n    _topHeight: number;\n\n    /**\n     * The height of the bottom row (d)\n     * @private\n     */\n    _bottomHeight: number;\n\n    /**\n     * @param texture - The texture to use on the NineSlicePlane.\n     * @param {number} [leftWidth=10] - size of the left vertical bar (A)\n     * @param {number} [topHeight=10] - size of the top horizontal bar (C)\n     * @param {number} [rightWidth=10] - size of the right vertical bar (B)\n     * @param {number} [bottomHeight=10] - size of the bottom horizontal bar (D)\n     */\n    constructor(\n        texture: Texture,\n        leftWidth = DEFAULT_BORDER_SIZE,\n        topHeight = DEFAULT_BORDER_SIZE,\n        rightWidth = DEFAULT_BORDER_SIZE,\n        bottomHeight = DEFAULT_BORDER_SIZE\n    )\n    {\n        super(Texture.WHITE, 4, 4);\n\n        this._origWidth = texture.orig.width;\n        this._origHeight = texture.orig.height;\n\n        /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n        this._width = this._origWidth;\n\n        /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n        this._height = this._origHeight;\n\n        this._leftWidth = leftWidth;\n        this._rightWidth = rightWidth;\n        this._topHeight = topHeight;\n        this._bottomHeight = bottomHeight;\n\n        // lets call the setter to ensure all necessary updates are performed\n        this.texture = texture;\n    }\n\n    public textureUpdated(): void\n    {\n        this._textureID = this.shader.texture._updateID;\n        this._refresh();\n    }\n\n    get vertices(): ITypedArray\n    {\n        return this.geometry.getBuffer('aVertexPosition').data;\n    }\n\n    set vertices(value: ITypedArray)\n    {\n        this.geometry.getBuffer('aVertexPosition').data = value;\n    }\n\n    /** Updates the horizontal vertices. */\n    public updateHorizontalVertices(): void\n    {\n        const vertices = this.vertices;\n\n        const scale = this._getMinScale();\n\n        vertices[9] = vertices[11] = vertices[13] = vertices[15] = this._topHeight * scale;\n        vertices[17] = vertices[19] = vertices[21] = vertices[23] = this._height - (this._bottomHeight * scale);\n        vertices[25] = vertices[27] = vertices[29] = vertices[31] = this._height;\n    }\n\n    /** Updates the vertical vertices. */\n    public updateVerticalVertices(): void\n    {\n        const vertices = this.vertices;\n\n        const scale = this._getMinScale();\n\n        vertices[2] = vertices[10] = vertices[18] = vertices[26] = this._leftWidth * scale;\n        vertices[4] = vertices[12] = vertices[20] = vertices[28] = this._width - (this._rightWidth * scale);\n        vertices[6] = vertices[14] = vertices[22] = vertices[30] = this._width;\n    }\n\n    /**\n     * Returns the smaller of a set of vertical and horizontal scale of nine slice corners.\n     * @returns Smaller number of vertical and horizontal scale.\n     */\n    private _getMinScale(): number\n    {\n        const w = this._leftWidth + this._rightWidth;\n        const scaleW = this._width > w ? 1.0 : this._width / w;\n\n        const h = this._topHeight + this._bottomHeight;\n        const scaleH = this._height > h ? 1.0 : this._height / h;\n\n        const scale = Math.min(scaleW, scaleH);\n\n        return scale;\n    }\n\n    /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    set width(value: number)\n    {\n        this._width = value;\n        this._refresh();\n    }\n\n    /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    set height(value: number)\n    {\n        this._height = value;\n        this._refresh();\n    }\n\n    /** The width of the left column. */\n    get leftWidth(): number\n    {\n        return this._leftWidth;\n    }\n\n    set leftWidth(value: number)\n    {\n        this._leftWidth = value;\n        this._refresh();\n    }\n\n    /** The width of the right column. */\n    get rightWidth(): number\n    {\n        return this._rightWidth;\n    }\n\n    set rightWidth(value: number)\n    {\n        this._rightWidth = value;\n        this._refresh();\n    }\n\n    /** The height of the top row. */\n    get topHeight(): number\n    {\n        return this._topHeight;\n    }\n\n    set topHeight(value: number)\n    {\n        this._topHeight = value;\n        this._refresh();\n    }\n\n    /** The height of the bottom row. */\n    get bottomHeight(): number\n    {\n        return this._bottomHeight;\n    }\n\n    set bottomHeight(value: number)\n    {\n        this._bottomHeight = value;\n        this._refresh();\n    }\n\n    /** Refreshes NineSlicePlane coords. All of them. */\n    private _refresh(): void\n    {\n        const texture = this.texture;\n\n        const uvs = this.geometry.buffers[1].data;\n\n        this._origWidth = texture.orig.width;\n        this._origHeight = texture.orig.height;\n\n        const _uvw = 1.0 / this._origWidth;\n        const _uvh = 1.0 / this._origHeight;\n\n        uvs[0] = uvs[8] = uvs[16] = uvs[24] = 0;\n        uvs[1] = uvs[3] = uvs[5] = uvs[7] = 0;\n        uvs[6] = uvs[14] = uvs[22] = uvs[30] = 1;\n        uvs[25] = uvs[27] = uvs[29] = uvs[31] = 1;\n\n        uvs[2] = uvs[10] = uvs[18] = uvs[26] = _uvw * this._leftWidth;\n        uvs[4] = uvs[12] = uvs[20] = uvs[28] = 1 - (_uvw * this._rightWidth);\n        uvs[9] = uvs[11] = uvs[13] = uvs[15] = _uvh * this._topHeight;\n        uvs[17] = uvs[19] = uvs[21] = uvs[23] = 1 - (_uvh * this._bottomHeight);\n\n        this.updateHorizontalVertices();\n        this.updateVerticalVertices();\n\n        this.geometry.buffers[0].update();\n        this.geometry.buffers[1].update();\n    }\n}\n"], "names": ["arguments", "MeshGeometry", "MeshMaterial", "WRAP_MODES", "<PERSON><PERSON>", "Texture"], "mappings": ";;;;;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;;AAAC;IACtD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvF,QAAQ,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;IACvE,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,gBAAA,EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAClC,SAAS,EAAA;IACT,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;IACnI,SAAS,EAAA,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAA,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAA,EAAA,EAAA;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD;IACO,SAAS,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;IAC/C,IAAI,OAAO,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;IACzE,CAAC;AACD;IACO,SAAS,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE;IACvD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,EAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAA;IACnI,CAAC;AACD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,EAAA;IACtE,QAAQ,OAAO,CAAC,EAAA,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAA;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAA,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAA,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AACD;IACO,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IAC7C,IAAI,IAAI,EAAE,KAAK,SAAS,IAAE,EAAE,GAAG,CAAC,CAAC,EAAA;IACjC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;AACD;IACO,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IAC1F,CAAC;AACD;IACO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAClF,IAAI,IAAI,CAAC,EAAA,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAA,EAAE,OAAO;IAClD,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAA;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,EAAA;IACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;AACD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,EAAA;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAA,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAA,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA,EAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;;AAAC;IAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAA,EAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAACA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,cAAc,GAAG;;AAAC;IAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAA,CAAC,IAAIA,WAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA;IACxF,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;IACpD,QAAQ,EAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IACzE,YAAY,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA;IACxB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;AACF;IACO,SAAS,OAAO,CAAC,CAAC,EAAE;IAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;IACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA,EAAC,EAAE;IAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;IACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;IACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAA,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;IACtF,CAAC;AACD;IACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnJ,CAAC;AACD;IACO,SAAS,aAAa,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAA,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,EAAA;IAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;IAChI,CAAC;AACD;IACO,SAAS,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE;IAClD,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;IACnH,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACF;IACO,SAAS,YAAY,CAAC,GAAG,EAAE;IAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAA,EAAE,OAAO,GAAG,CAAC,EAAA;IAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,IAAI,IAAI,IAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAA,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA,EAAA,EAAA;IACnG,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;AACD;IACO,SAAS,eAAe,CAAC,GAAG,EAAE;IACrC,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5D,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE;IAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;AACD;IACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,OAAO,KAAK,CAAC;IACjB;;ICvNA;;IAEG;AACH,QAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;QAAmC,SAAY,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;IAO3C;;;;;IAKG;IACH,IAAA,SAAA,aAAA,CAAY,KAAW,EAAE,MAAY,EAAE,QAAa,EAAE,SAAc,EAAA;IAAxD,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAW,GAAA,GAAA,CAAA,EAAA;IAAE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAY,GAAA,GAAA,CAAA,EAAA;IAAE,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAa,GAAA,EAAA,CAAA,EAAA;IAAE,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAc,GAAA,EAAA,CAAA,EAAA;IAApE,QAAA,IAAA,KAAA,GAEI,iBAAO,IASV,IAAA,CAAA;IAPG,QAAA,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,QAAA,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAE3B,QAAA,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YAErB,KAAI,CAAC,KAAK,EAAE,CAAC;;SAChB;IAED;;;IAGG;IACH,IAAA,aAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;YAEI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC7C,IAAM,KAAK,GAAG,EAAE,CAAC;YACjB,IAAM,GAAG,GAAG,EAAE,CAAC;YACf,IAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACpC,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YAErC,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC;YACvC,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;YAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAC9B;gBACI,IAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9B,YAAA,IAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;gBAEpC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;gBACjC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;IAC1C,SAAA;IAED,QAAA,IAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;YAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EACjC;IACI,YAAA,IAAM,IAAI,GAAG,CAAC,GAAG,SAAS,CAAC;gBAC3B,IAAM,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,IAAI,CAAC,CAAC;gBAEjC,IAAM,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;IAC5C,YAAA,IAAM,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC;IACjD,YAAA,IAAM,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;IACnD,YAAA,IAAM,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC;IAEvD,YAAA,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAC9B,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAC/B,SAAA;IAED,QAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;IAC/C,QAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;;YAGjD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACzB,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;SAC7B,CAAA;QACL,OAAC,aAAA,CAAA;IAAD,CA7EA,CAAmCC,iBAAY,CA6E9C;;IC/ED;;;;;;;;;;IAUG;AACH,QAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;QAAkC,SAAY,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;IAc1C;;;;;;;;;;IAUG;IACH,IAAA,SAAA,YAAA,CAAY,KAAW,EAAE,MAAgB,EAAE,YAAgB,EAAA;IAA/C,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAW,GAAA,GAAA,CAAA,EAAA;IAAoB,QAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAAgB,GAAA,CAAA,CAAA,EAAA;IAA3D,QAAA,IAAA,KAAA,GAEI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EACrC,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EACnC,IAAI,WAAW,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAOhD,IAAA,CAAA;IALG,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,QAAA,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,QAAA,KAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YAEjC,KAAI,CAAC,KAAK,EAAE,CAAC;;SAChB;IAMD,IAAA,MAAA,CAAA,cAAA,CAAI,YAAK,CAAA,SAAA,EAAA,OAAA,EAAA;IAJT;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,MAAM,CAAC;aACtB;;;IAAA,KAAA,CAAA,CAAA;;IAGO,IAAA,YAAA,CAAA,SAAA,CAAA,KAAK,GAAb,YAAA;IAEI,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,QAAA,IAAI,CAAC,MAAM;kBAAE,OAAO,EAAA;YAEpB,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACvD,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IACjD,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;;IAGpC,QAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EACrB;gBACI,OAAO;IACV,SAAA;;YAGD,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,EAClD;IACI,YAAA,YAAY,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxD,YAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpD,YAAA,WAAW,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,SAAA;IAED,QAAA,IAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;IAC1B,QAAA,IAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC;IAEjC,QAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,QAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAEX,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,QAAA,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;IACrD,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;YAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAC9B;;IAEI,YAAA,IAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAEpB,YAAA,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EACzB;;IAEI,gBAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,gBAAA,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAElD,gBAAA,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACjB,gBAAA,MAAM,IAAI,QAAQ,GAAG,YAAY,CAAC;IACrC,aAAA;IAED,iBAAA;;oBAEI,MAAM,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;IAC5B,aAAA;IAED,YAAA,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;IACpB,YAAA,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAEnB,YAAA,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;IACxB,YAAA,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACtB,SAAA;YAED,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAClC;IACI,YAAA,IAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAEpB,YAAA,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC;gBAC9B,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;gBAClC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;gBAElC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;gBAClC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;gBAClC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;IACrC,SAAA;;YAGD,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,WAAW,CAAC,MAAM,EAAE,CAAC;YAErB,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB,CAAA;;IAGM,IAAA,YAAA,CAAA,SAAA,CAAA,cAAc,GAArB,YAAA;IAEI,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,QAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EACrB;gBACI,OAAO;IACV,SAAA;IAED,QAAA,IAAI,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,QAAA,IAAI,SAAS,CAAC;YACd,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtC,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;YAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAC9B;IACI,YAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,YAAA,IAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAEpB,YAAA,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EACzB;IACI,gBAAA,SAAS,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7B,aAAA;IAED,iBAAA;oBACI,SAAS,GAAG,KAAK,CAAC;IACrB,aAAA;gBAED,KAAK,GAAG,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACrC,KAAK,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;IAElC,YAAA,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAEzC,IAAI,KAAK,GAAG,CAAC,EACb;oBACI,KAAK,GAAG,CAAC,CAAC;IACb,aAAA;IAED,YAAA,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;gBAChE,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAE1F,KAAK,IAAI,UAAU,CAAC;gBACpB,KAAK,IAAI,UAAU,CAAC;gBAEpB,KAAK,IAAI,GAAG,CAAC;gBACb,KAAK,IAAI,GAAG,CAAC;gBAEb,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;gBAClC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;gBACtC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;gBACtC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;gBAEtC,SAAS,GAAG,KAAK,CAAC;IACrB,SAAA;YAED,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SAC5B,CAAA;IAEM,IAAA,YAAA,CAAA,SAAA,CAAA,MAAM,GAAb,YAAA;IAEI,QAAA,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EACzB;IACI,YAAA,IAAI,CAAC,KAAK,EAAE,CAAC;IAChB,SAAA;IAED,aAAA;gBACI,IAAI,CAAC,cAAc,EAAE,CAAC;IACzB,SAAA;SACJ,CAAA;QACL,OAAC,YAAA,CAAA;IAAD,CAhNA,CAAkCA,iBAAY,CAgN7C;;ICvND;;;;;;;;;;IAUG;AACH,QAAA,UAAA,kBAAA,UAAA,MAAA,EAAA;QAAgC,SAAI,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA;IAIhC;;;;;;IAMG;IACH,IAAA,SAAA,UAAA,CAAY,OAAgB,EAAE,MAAgB,EAAE,YAAgB,EAAA;IAAhB,QAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAAgB,GAAA,CAAA,CAAA,EAAA;YAAhE,IAiBC,KAAA,GAAA,IAAA,CAAA;IAfG,QAAA,IAAM,YAAY,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC5E,QAAA,IAAM,YAAY,GAAG,IAAIC,iBAAY,CAAC,OAAO,CAAC,CAAC;YAE/C,IAAI,YAAY,GAAG,CAAC,EACpB;;gBAEI,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAGC,oBAAU,CAAC,MAAM,CAAC;IACpD,SAAA;IACD,QAAA,KAAA,GAAA,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,YAAY,EAAE,YAAY,CAAC,IAAC,IAAA,CAAA;IAElC;;;IAGG;IACH,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;;SAC1B;QAED,UAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,QAAkB,EAAA;IAEtB,QAAA,IAAM,QAAQ,GAAiB,IAAI,CAAC,QAAe,CAAC;IAEpD,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EACrE;gBACI,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC7C,QAAQ,CAAC,MAAM,EAAE,CAAC;IACrB,SAAA;IAED,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;SAC3B,CAAA;QACL,OAAC,UAAA,CAAA;IAAD,CA1CA,CAAgCC,SAAI,CA0CnC;;ICrDD;;;;;;;;;;IAUG;AACH,QAAA,WAAA,kBAAA,UAAA,MAAA,EAAA;QAAiC,SAAI,CAAA,WAAA,EAAA,MAAA,CAAA,CAAA;IAOjC;;;;IAIG;IACH,IAAA,SAAA,WAAA,CAAY,OAAgB,EAAE,SAAkB,EAAE,SAAkB,EAAA;YAApE,IAUC,KAAA,GAAA,IAAA,CAAA;IARG,QAAA,IAAM,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAC7F,IAAM,YAAY,GAAG,IAAIF,iBAAY,CAACG,YAAO,CAAC,KAAK,CAAC,CAAC;IAErD,QAAA,KAAA,GAAA,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,aAAa,EAAE,YAAY,CAAC,IAAC,IAAA,CAAA;;IAGnC,QAAA,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;;SAC1B;IAED;;;IAGG;IACI,IAAA,WAAA,CAAA,SAAA,CAAA,cAAc,GAArB,YAAA;YAEI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;IAEhD,QAAA,IAAM,QAAQ,GAAkB,IAAI,CAAC,QAAe,CAAC;IAC/C,QAAA,IAAA,EAAoB,GAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAArC,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAwB,CAAC;IAE9C,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,KAAK,KAAK,KAAK,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,EAC/E;gBACI,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC3C,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC7C,QAAQ,CAAC,KAAK,EAAE,CAAC;IACpB,SAAA;SACJ,CAAA;IAED,IAAA,MAAA,CAAA,cAAA,CAAI,WAAO,CAAA,SAAA,EAAA,SAAA,EAAA;IAwBX,QAAA,GAAA,EAAA,YAAA;IAEI,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;aAC9B;IA3BD,QAAA,GAAA,EAAA,UAAY,KAAc,EAAA;;;;IAMtB,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,KAAK,EACjC;oBACI,OAAO;IACV,aAAA;IAED,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;IAC5B,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAErB,YAAA,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,EAC3B;oBACI,IAAI,CAAC,cAAc,EAAE,CAAC;IACzB,aAAA;IAED,iBAAA;oBACI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACnD,aAAA;aACJ;;;IAAA,KAAA,CAAA,CAAA;QAOD,WAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,QAAkB,EAAA;YAEtB,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EACrD;gBACI,IAAI,CAAC,cAAc,EAAE,CAAC;IACzB,SAAA;IAED,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;SAC3B,CAAA;QAEM,WAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;IAE9C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAC7D,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;SAC1B,CAAA;QACL,OAAC,WAAA,CAAA;IAAD,CAvFA,CAAiCD,SAAI,CAuFpC;;ICnGD;;;;IAIG;AACH,QAAA,UAAA,kBAAA,UAAA,MAAA,EAAA;QAAgC,SAAI,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA;IAKhC;;;;;;IAMG;QACH,SACI,UAAA,CAAA,OAAgC,EAChC,QAAuB,EACvB,GAAkB,EAClB,OAAsB,EACtB,QAAqB,EAAA;IAJrB,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAmBC,YAAO,CAAC,KAAK,CAAA,EAAA;YADpC,IAiBC,KAAA,GAAA,IAAA,CAAA;YATG,IAAM,QAAQ,GAAG,IAAIJ,iBAAY,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;YAE1D,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IAErD,QAAA,IAAM,YAAY,GAAG,IAAIC,iBAAY,CAAC,OAAO,CAAC,CAAC;YAE/C,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,QAAQ,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAC,IAAA,CAAA;IAE9C,QAAA,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;;SAC1B;IAMD,IAAA,MAAA,CAAA,cAAA,CAAI,UAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IAJZ;;;IAGG;IACH,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;aAC1D;IACD,QAAA,GAAA,EAAA,UAAa,KAAkB,EAAA;gBAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC;aAC3D;;;IAJA,KAAA,CAAA,CAAA;QAMD,UAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,QAAkB,EAAA;YAEtB,IAAI,IAAI,CAAC,UAAU,EACnB;gBACI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,MAAM,EAAE,CAAC;IACvD,SAAA;IAED,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;SAC3B,CAAA;QACL,OAAC,UAAA,CAAA;IAAD,CArDA,CAAgCE,SAAI,CAqDnC;;IC3DD,IAAM,mBAAmB,GAAG,EAAE,CAAC;IAK/B;;;;;;;;;;;;;;;;;;;;;;;;;IAyBG;AACH,QAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;QAAoC,SAAW,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;IA6B3C;;;;;;IAMG;QACH,SACI,cAAA,CAAA,OAAgB,EAChB,SAA+B,EAC/B,SAA+B,EAC/B,UAAgC,EAChC,YAAkC,EAAA;IAHlC,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA+B,GAAA,mBAAA,CAAA,EAAA;IAC/B,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAA+B,GAAA,mBAAA,CAAA,EAAA;IAC/B,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAgC,GAAA,mBAAA,CAAA,EAAA;IAChC,QAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAAkC,GAAA,mBAAA,CAAA,EAAA;YALtC,IAQI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAMC,YAAO,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,IAkB7B,IAAA,CAAA;YAhBG,KAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;YACrC,KAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;IAGvC,QAAA,KAAI,CAAC,MAAM,GAAG,KAAI,CAAC,UAAU,CAAC;;IAG9B,QAAA,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,WAAW,CAAC;IAEhC,QAAA,KAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC5B,QAAA,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAC9B,QAAA,KAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC5B,QAAA,KAAI,CAAC,aAAa,GAAG,YAAY,CAAC;;IAGlC,QAAA,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;SAC1B;IAEM,IAAA,cAAA,CAAA,SAAA,CAAA,cAAc,GAArB,YAAA;YAEI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB,CAAA;IAED,IAAA,MAAA,CAAA,cAAA,CAAI,cAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;IAAZ,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;aAC1D;IAED,QAAA,GAAA,EAAA,UAAa,KAAkB,EAAA;gBAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC;aAC3D;;;IALA,KAAA,CAAA,CAAA;;IAQM,IAAA,cAAA,CAAA,SAAA,CAAA,wBAAwB,GAA/B,YAAA;IAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAE/B,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAElC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IACnF,QAAA,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC;YACxG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;SAC5E,CAAA;;IAGM,IAAA,cAAA,CAAA,SAAA,CAAA,sBAAsB,GAA7B,YAAA;IAEI,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAE/B,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAElC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IACnF,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;YACpG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;SAC1E,CAAA;IAED;;;IAGG;IACK,IAAA,cAAA,CAAA,SAAA,CAAA,YAAY,GAApB,YAAA;YAEI,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;IAC7C,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAEvD,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC;IAC/C,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;YAEzD,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAEvC,QAAA,OAAO,KAAK,CAAC;SAChB,CAAA;IAGD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAK,CAAA,SAAA,EAAA,OAAA,EAAA;;IAAT,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,MAAM,CAAC;aACtB;IAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;IAEnB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;;;IANA,KAAA,CAAA,CAAA;IASD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAM,CAAA,SAAA,EAAA,QAAA,EAAA;;IAAV,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,OAAO,CAAC;aACvB;IAED,QAAA,GAAA,EAAA,UAAW,KAAa,EAAA;IAEpB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;;;IANA,KAAA,CAAA,CAAA;IASD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAS,CAAA,SAAA,EAAA,WAAA,EAAA;;IAAb,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,UAAU,CAAC;aAC1B;IAED,QAAA,GAAA,EAAA,UAAc,KAAa,EAAA;IAEvB,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;;;IANA,KAAA,CAAA,CAAA;IASD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;IAAd,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;IAED,QAAA,GAAA,EAAA,UAAe,KAAa,EAAA;IAExB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;;;IANA,KAAA,CAAA,CAAA;IASD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAS,CAAA,SAAA,EAAA,WAAA,EAAA;;IAAb,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,UAAU,CAAC;aAC1B;IAED,QAAA,GAAA,EAAA,UAAc,KAAa,EAAA;IAEvB,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;;;IANA,KAAA,CAAA,CAAA;IASD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAY,CAAA,SAAA,EAAA,cAAA,EAAA;;IAAhB,QAAA,GAAA,EAAA,YAAA;gBAEI,OAAO,IAAI,CAAC,aAAa,CAAC;aAC7B;IAED,QAAA,GAAA,EAAA,UAAiB,KAAa,EAAA;IAE1B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;;;IANA,KAAA,CAAA,CAAA;;IASO,IAAA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAhB,YAAA;IAEI,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAE7B,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAE1C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;YACrC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;IAEvC,QAAA,IAAM,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,QAAA,IAAM,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;YAEpC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACxC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACtC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACzC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YAE1C,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAC9D,QAAA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YACrE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAC9D,QAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;YAExE,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SACrC,CAAA;QACL,OAAC,cAAA,CAAA;IAAD,CA9NA,CAAoC,WAAW,CA8N9C;;;;;;;;;;;;;;;;;;;;"}