/**
 * 修复ComicResultValidator.ts中的ID生成逻辑
 * 确保ID生成的一致性和可预测性
 */

const fs = require('fs');
const path = require('path');

const validatorPath = '/mnt/h/AI/CosyVoice/cosyvoice-vue3/src/components/modules/漫画生成/utils/comicResultValidator.ts';

console.log('🔧 开始修复ComicResultValidator中的ID生成逻辑...');

// 读取文件
const fileContent = fs.readFileSync(validatorPath, 'utf8');

// 查找ID生成逻辑并增强
const enhancedIdGeneration = `    // 🔧 增强ID生成逻辑：确保ID的唯一性和可预测性
    if (!fixed.id) {
      // 使用时间戳 + 随机数 + story hash 确保唯一性
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substr(2, 9);
      
      // 如果有故事文本，使用前20字符的hash作为标识
      let storyHash = '';
      if (fixed.storyText) {
        const storyContent = fixed.storyText.substring(0, 20);
        storyHash = '_' + storyContent.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
      }
      
      fixed.id = \`comic_\${timestamp}_\${randomId}\${storyHash}\`;
      console.log('🆔 自动生成漫画ID:', fixed.id);
    }`;

// 替换ID生成逻辑
const modifiedContent = fileContent.replace(
  /if \(!fixed\.id\) \{[^}]*\}/g,
  enhancedIdGeneration
);

// 写入修复后的文件
fs.writeFileSync(validatorPath, modifiedContent);

console.log('✅ ComicResultValidator修复完成！');
console.log('📝 修复内容：');
console.log('  1. 增强了ID生成逻辑，包含时间戳+随机数+故事hash');
console.log('  2. 提高了ID的唯一性和可识别性');
console.log('  3. 添加了ID生成的调试日志');