{"version": 3, "sources": ["../../pixi-live2d-display/dist/index.es.js"], "sourcesContent": ["var __pow = Math.pow;\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\nimport { EventEmitter, url } from \"@pixi/utils\";\nimport { Matrix, Transform, Point, ObservablePoint } from \"@pixi/math\";\nimport { Texture } from \"@pixi/core\";\nimport { Container } from \"@pixi/display\";\nconst LOGICAL_WIDTH = 2;\nconst LOGICAL_HEIGHT = 2;\nvar CubismConfig;\n((CubismConfig2) => {\n  CubismConfig2.supportMoreMaskDivisions = true;\n  CubismConfig2.setOpacityFromMotion = false;\n})(CubismConfig || (CubismConfig = {}));\nvar config;\n((config2) => {\n  config2.LOG_LEVEL_VERBOSE = 0;\n  config2.LOG_LEVEL_WARNING = 1;\n  config2.LOG_LEVEL_ERROR = 2;\n  config2.LOG_LEVEL_NONE = 999;\n  config2.logLevel = config2.LOG_LEVEL_WARNING;\n  config2.sound = true;\n  config2.motionSync = true;\n  config2.motionFadingDuration = 500;\n  config2.idleMotionFadingDuration = 2e3;\n  config2.expressionFadingDuration = 500;\n  config2.preserveExpressionOnMotion = true;\n  config2.cubism4 = CubismConfig;\n})(config || (config = {}));\nconst VERSION = \"0.4.0\";\nconst logger = {\n  log(tag, ...messages) {\n    if (config.logLevel <= config.LOG_LEVEL_VERBOSE) {\n      console.log(`[${tag}]`, ...messages);\n    }\n  },\n  warn(tag, ...messages) {\n    if (config.logLevel <= config.LOG_LEVEL_WARNING) {\n      console.warn(`[${tag}]`, ...messages);\n    }\n  },\n  error(tag, ...messages) {\n    if (config.logLevel <= config.LOG_LEVEL_ERROR) {\n      console.error(`[${tag}]`, ...messages);\n    }\n  }\n};\nfunction clamp(num, lower, upper) {\n  return num < lower ? lower : num > upper ? upper : num;\n}\nfunction rand(min, max) {\n  return Math.random() * (max - min) + min;\n}\nfunction copyProperty(type, from, to, fromKey, toKey) {\n  const value = from[fromKey];\n  if (value !== null && typeof value === type) {\n    to[toKey] = value;\n  }\n}\nfunction copyArray(type, from, to, fromKey, toKey) {\n  const array = from[fromKey];\n  if (Array.isArray(array)) {\n    to[toKey] = array.filter((item) => item !== null && typeof item === type);\n  }\n}\nfunction applyMixins(derivedCtor, baseCtors) {\n  baseCtors.forEach((baseCtor) => {\n    Object.getOwnPropertyNames(baseCtor.prototype).forEach((name) => {\n      if (name !== \"constructor\") {\n        Object.defineProperty(derivedCtor.prototype, name, Object.getOwnPropertyDescriptor(baseCtor.prototype, name));\n      }\n    });\n  });\n}\nfunction folderName(url2) {\n  let lastSlashIndex = url2.lastIndexOf(\"/\");\n  if (lastSlashIndex != -1) {\n    url2 = url2.slice(0, lastSlashIndex);\n  }\n  lastSlashIndex = url2.lastIndexOf(\"/\");\n  if (lastSlashIndex !== -1) {\n    url2 = url2.slice(lastSlashIndex + 1);\n  }\n  return url2;\n}\nfunction remove(array, item) {\n  const index = array.indexOf(item);\n  if (index !== -1) {\n    array.splice(index, 1);\n  }\n}\nclass ExpressionManager extends EventEmitter {\n  constructor(settings, options) {\n    super();\n    this.expressions = [];\n    this.reserveExpressionIndex = -1;\n    this.destroyed = false;\n    this.settings = settings;\n    this.tag = `ExpressionManager(${settings.name})`;\n  }\n  init() {\n    this.defaultExpression = this.createExpression({}, void 0);\n    this.currentExpression = this.defaultExpression;\n    this.stopAllExpressions();\n  }\n  loadExpression(index) {\n    return __async(this, null, function* () {\n      if (!this.definitions[index]) {\n        logger.warn(this.tag, `Undefined expression at [${index}]`);\n        return void 0;\n      }\n      if (this.expressions[index] === null) {\n        logger.warn(this.tag, `Cannot set expression at [${index}] because it's already failed in loading.`);\n        return void 0;\n      }\n      if (this.expressions[index]) {\n        return this.expressions[index];\n      }\n      const expression = yield this._loadExpression(index);\n      this.expressions[index] = expression;\n      return expression;\n    });\n  }\n  _loadExpression(index) {\n    throw new Error(\"Not implemented.\");\n  }\n  setRandomExpression() {\n    return __async(this, null, function* () {\n      if (this.definitions.length) {\n        const availableIndices = [];\n        for (let i = 0; i < this.definitions.length; i++) {\n          if (this.expressions[i] !== null && this.expressions[i] !== this.currentExpression && i !== this.reserveExpressionIndex) {\n            availableIndices.push(i);\n          }\n        }\n        if (availableIndices.length) {\n          const index = Math.floor(Math.random() * availableIndices.length);\n          return this.setExpression(index);\n        }\n      }\n      return false;\n    });\n  }\n  resetExpression() {\n    this._setExpression(this.defaultExpression);\n  }\n  restoreExpression() {\n    this._setExpression(this.currentExpression);\n  }\n  setExpression(index) {\n    return __async(this, null, function* () {\n      if (typeof index !== \"number\") {\n        index = this.getExpressionIndex(index);\n      }\n      if (!(index > -1 && index < this.definitions.length)) {\n        return false;\n      }\n      if (index === this.expressions.indexOf(this.currentExpression)) {\n        return false;\n      }\n      this.reserveExpressionIndex = index;\n      const expression = yield this.loadExpression(index);\n      if (!expression || this.reserveExpressionIndex !== index) {\n        return false;\n      }\n      this.reserveExpressionIndex = -1;\n      this.currentExpression = expression;\n      this._setExpression(expression);\n      return true;\n    });\n  }\n  update(model, now) {\n    if (!this.isFinished()) {\n      return this.updateParameters(model, now);\n    }\n    return false;\n  }\n  destroy() {\n    this.destroyed = true;\n    this.emit(\"destroy\");\n    const self = this;\n    self.definitions = void 0;\n    self.expressions = void 0;\n  }\n}\nconst EPSILON = 0.01;\nconst MAX_SPEED = 40 / 7.5;\nconst ACCELERATION_TIME = 1 / (0.15 * 1e3);\nclass FocusController {\n  constructor() {\n    this.targetX = 0;\n    this.targetY = 0;\n    this.x = 0;\n    this.y = 0;\n    this.vx = 0;\n    this.vy = 0;\n  }\n  focus(x, y, instant = false) {\n    this.targetX = clamp(x, -1, 1);\n    this.targetY = clamp(y, -1, 1);\n    if (instant) {\n      this.x = this.targetX;\n      this.y = this.targetY;\n    }\n  }\n  update(dt) {\n    const dx = this.targetX - this.x;\n    const dy = this.targetY - this.y;\n    if (Math.abs(dx) < EPSILON && Math.abs(dy) < EPSILON)\n      return;\n    const d = Math.sqrt(__pow(dx, 2) + __pow(dy, 2));\n    const maxSpeed = MAX_SPEED / (1e3 / dt);\n    let ax = maxSpeed * (dx / d) - this.vx;\n    let ay = maxSpeed * (dy / d) - this.vy;\n    const a = Math.sqrt(__pow(ax, 2) + __pow(ay, 2));\n    const maxA = maxSpeed * ACCELERATION_TIME * dt;\n    if (a > maxA) {\n      ax *= maxA / a;\n      ay *= maxA / a;\n    }\n    this.vx += ax;\n    this.vy += ay;\n    const v = Math.sqrt(__pow(this.vx, 2) + __pow(this.vy, 2));\n    const maxV = 0.5 * (Math.sqrt(__pow(maxA, 2) + 8 * maxA * d) - maxA);\n    if (v > maxV) {\n      this.vx *= maxV / v;\n      this.vy *= maxV / v;\n    }\n    this.x += this.vx;\n    this.y += this.vy;\n  }\n}\nclass ModelSettings {\n  constructor(json) {\n    this.json = json;\n    let url2 = json.url;\n    if (typeof url2 !== \"string\") {\n      throw new TypeError(\"The `url` field in settings JSON must be defined as a string.\");\n    }\n    this.url = url2;\n    this.name = folderName(this.url);\n  }\n  resolveURL(path) {\n    return url.resolve(this.url, path);\n  }\n  replaceFiles(replacer) {\n    this.moc = replacer(this.moc, \"moc\");\n    if (this.pose !== void 0) {\n      this.pose = replacer(this.pose, \"pose\");\n    }\n    if (this.physics !== void 0) {\n      this.physics = replacer(this.physics, \"physics\");\n    }\n    for (let i = 0; i < this.textures.length; i++) {\n      this.textures[i] = replacer(this.textures[i], `textures[${i}]`);\n    }\n  }\n  getDefinedFiles() {\n    const files = [];\n    this.replaceFiles((file) => {\n      files.push(file);\n      return file;\n    });\n    return files;\n  }\n  validateFiles(files) {\n    const assertFileExists = (expectedFile, shouldThrow) => {\n      const actualPath = this.resolveURL(expectedFile);\n      if (!files.includes(actualPath)) {\n        if (shouldThrow) {\n          throw new Error(`File \"${expectedFile}\" is defined in settings, but doesn't exist in given files`);\n        }\n        return false;\n      }\n      return true;\n    };\n    const essentialFiles = [this.moc, ...this.textures];\n    essentialFiles.forEach((texture) => assertFileExists(texture, true));\n    const definedFiles = this.getDefinedFiles();\n    return definedFiles.filter((file) => assertFileExists(file, false));\n  }\n}\nvar MotionPriority = /* @__PURE__ */ ((MotionPriority2) => {\n  MotionPriority2[MotionPriority2[\"NONE\"] = 0] = \"NONE\";\n  MotionPriority2[MotionPriority2[\"IDLE\"] = 1] = \"IDLE\";\n  MotionPriority2[MotionPriority2[\"NORMAL\"] = 2] = \"NORMAL\";\n  MotionPriority2[MotionPriority2[\"FORCE\"] = 3] = \"FORCE\";\n  return MotionPriority2;\n})(MotionPriority || {});\nclass MotionState {\n  constructor() {\n    this.debug = false;\n    this.currentPriority = 0;\n    this.reservePriority = 0;\n  }\n  reserve(group, index, priority) {\n    if (priority <= 0) {\n      logger.log(this.tag, `Cannot start a motion with MotionPriority.NONE.`);\n      return false;\n    }\n    if (group === this.currentGroup && index === this.currentIndex) {\n      logger.log(this.tag, `Motion is already playing.`, this.dump(group, index));\n      return false;\n    }\n    if (group === this.reservedGroup && index === this.reservedIndex || group === this.reservedIdleGroup && index === this.reservedIdleIndex) {\n      logger.log(this.tag, `Motion is already reserved.`, this.dump(group, index));\n      return false;\n    }\n    if (priority === 1) {\n      if (this.currentPriority !== 0) {\n        logger.log(this.tag, `Cannot start idle motion because another motion is playing.`, this.dump(group, index));\n        return false;\n      }\n      if (this.reservedIdleGroup !== void 0) {\n        logger.log(this.tag, `Cannot start idle motion because another idle motion has reserved.`, this.dump(group, index));\n        return false;\n      }\n      this.setReservedIdle(group, index);\n    } else {\n      if (priority < 3) {\n        if (priority <= this.currentPriority) {\n          logger.log(this.tag, \"Cannot start motion because another motion is playing as an equivalent or higher priority.\", this.dump(group, index));\n          return false;\n        }\n        if (priority <= this.reservePriority) {\n          logger.log(this.tag, \"Cannot start motion because another motion has reserved as an equivalent or higher priority.\", this.dump(group, index));\n          return false;\n        }\n      }\n      this.setReserved(group, index, priority);\n    }\n    return true;\n  }\n  start(motion, group, index, priority) {\n    if (priority === 1) {\n      this.setReservedIdle(void 0, void 0);\n      if (this.currentPriority !== 0) {\n        logger.log(this.tag, \"Cannot start idle motion because another motion is playing.\", this.dump(group, index));\n        return false;\n      }\n    } else {\n      if (group !== this.reservedGroup || index !== this.reservedIndex) {\n        logger.log(this.tag, \"Cannot start motion because another motion has taken the place.\", this.dump(group, index));\n        return false;\n      }\n      this.setReserved(void 0, void 0, 0);\n    }\n    if (!motion) {\n      return false;\n    }\n    this.setCurrent(group, index, priority);\n    return true;\n  }\n  complete() {\n    this.setCurrent(void 0, void 0, 0);\n  }\n  setCurrent(group, index, priority) {\n    this.currentPriority = priority;\n    this.currentGroup = group;\n    this.currentIndex = index;\n  }\n  setReserved(group, index, priority) {\n    this.reservePriority = priority;\n    this.reservedGroup = group;\n    this.reservedIndex = index;\n  }\n  setReservedIdle(group, index) {\n    this.reservedIdleGroup = group;\n    this.reservedIdleIndex = index;\n  }\n  isActive(group, index) {\n    return group === this.currentGroup && index === this.currentIndex || group === this.reservedGroup && index === this.reservedIndex || group === this.reservedIdleGroup && index === this.reservedIdleIndex;\n  }\n  reset() {\n    this.setCurrent(void 0, void 0, 0);\n    this.setReserved(void 0, void 0, 0);\n    this.setReservedIdle(void 0, void 0);\n  }\n  shouldRequestIdleMotion() {\n    return this.currentGroup === void 0 && this.reservedIdleGroup === void 0;\n  }\n  shouldOverrideExpression() {\n    return !config.preserveExpressionOnMotion && this.currentPriority > 1;\n  }\n  dump(requestedGroup, requestedIndex) {\n    if (this.debug) {\n      const keys = [\n        \"currentPriority\",\n        \"reservePriority\",\n        \"currentGroup\",\n        \"currentIndex\",\n        \"reservedGroup\",\n        \"reservedIndex\",\n        \"reservedIdleGroup\",\n        \"reservedIdleIndex\"\n      ];\n      return `\n<Requested> group = \"${requestedGroup}\", index = ${requestedIndex}\n` + keys.map((key) => \"[\" + key + \"] \" + this[key]).join(\"\\n\");\n    }\n    return \"\";\n  }\n}\nconst TAG$2 = \"SoundManager\";\nconst VOLUME = 0.5;\nclass SoundManager {\n  static get volume() {\n    return this._volume;\n  }\n  static set volume(value) {\n    this._volume = (value > 1 ? 1 : value < 0 ? 0 : value) || 0;\n    this.audios.forEach((audio) => audio.volume = this._volume);\n  }\n  static add(file, onFinish, onError) {\n    const audio = new Audio(file);\n    audio.volume = this._volume;\n    audio.preload = \"auto\";\n    audio.addEventListener(\"ended\", () => {\n      this.dispose(audio);\n      onFinish == null ? void 0 : onFinish();\n    });\n    audio.addEventListener(\"error\", (e) => {\n      this.dispose(audio);\n      logger.warn(TAG$2, `Error occurred on \"${file}\"`, e.error);\n      onError == null ? void 0 : onError(e.error);\n    });\n    this.audios.push(audio);\n    return audio;\n  }\n  static play(audio) {\n    return new Promise((resolve, reject) => {\n      var _a;\n      (_a = audio.play()) == null ? void 0 : _a.catch((e) => {\n        audio.dispatchEvent(new ErrorEvent(\"error\", { error: e }));\n        reject(e);\n      });\n      if (audio.readyState === audio.HAVE_ENOUGH_DATA) {\n        resolve();\n      } else {\n        audio.addEventListener(\"canplaythrough\", resolve);\n      }\n    });\n  }\n  static dispose(audio) {\n    audio.pause();\n    audio.removeAttribute(\"src\");\n    remove(this.audios, audio);\n  }\n  static destroy() {\n    for (let i = this.audios.length - 1; i >= 0; i--) {\n      this.dispose(this.audios[i]);\n    }\n  }\n}\nSoundManager.audios = [];\nSoundManager._volume = VOLUME;\nvar MotionPreloadStrategy = /* @__PURE__ */ ((MotionPreloadStrategy2) => {\n  MotionPreloadStrategy2[\"ALL\"] = \"ALL\";\n  MotionPreloadStrategy2[\"IDLE\"] = \"IDLE\";\n  MotionPreloadStrategy2[\"NONE\"] = \"NONE\";\n  return MotionPreloadStrategy2;\n})(MotionPreloadStrategy || {});\nclass MotionManager extends EventEmitter {\n  constructor(settings, options) {\n    super();\n    this.motionGroups = {};\n    this.state = new MotionState();\n    this.playing = false;\n    this.destroyed = false;\n    this.settings = settings;\n    this.tag = `MotionManager(${settings.name})`;\n    this.state.tag = this.tag;\n  }\n  init(options) {\n    if (options == null ? void 0 : options.idleMotionGroup) {\n      this.groups.idle = options.idleMotionGroup;\n    }\n    this.setupMotions(options);\n    this.stopAllMotions();\n  }\n  setupMotions(options) {\n    for (const group of Object.keys(this.definitions)) {\n      this.motionGroups[group] = [];\n    }\n    let groups;\n    switch (options == null ? void 0 : options.motionPreload) {\n      case \"NONE\":\n        return;\n      case \"ALL\":\n        groups = Object.keys(this.definitions);\n        break;\n      case \"IDLE\":\n      default:\n        groups = [this.groups.idle];\n        break;\n    }\n    for (const group of groups) {\n      if (this.definitions[group]) {\n        for (let i = 0; i < this.definitions[group].length; i++) {\n          this.loadMotion(group, i).then();\n        }\n      }\n    }\n  }\n  loadMotion(group, index) {\n    return __async(this, null, function* () {\n      var _a;\n      if (!((_a = this.definitions[group]) == null ? void 0 : _a[index])) {\n        logger.warn(this.tag, `Undefined motion at \"${group}\"[${index}]`);\n        return void 0;\n      }\n      if (this.motionGroups[group][index] === null) {\n        logger.warn(this.tag, `Cannot start motion at \"${group}\"[${index}] because it's already failed in loading.`);\n        return void 0;\n      }\n      if (this.motionGroups[group][index]) {\n        return this.motionGroups[group][index];\n      }\n      const motion = yield this._loadMotion(group, index);\n      if (this.destroyed) {\n        return;\n      }\n      this.motionGroups[group][index] = motion != null ? motion : null;\n      return motion;\n    });\n  }\n  _loadMotion(group, index) {\n    throw new Error(\"Not implemented.\");\n  }\n  startMotion(_0, _1) {\n    return __async(this, arguments, function* (group, index, priority = MotionPriority.NORMAL) {\n      var _a;\n      if (!this.state.reserve(group, index, priority)) {\n        return false;\n      }\n      const definition = (_a = this.definitions[group]) == null ? void 0 : _a[index];\n      if (!definition) {\n        return false;\n      }\n      if (this.currentAudio) {\n        SoundManager.dispose(this.currentAudio);\n      }\n      let audio;\n      if (config.sound) {\n        const soundURL = this.getSoundFile(definition);\n        if (soundURL) {\n          try {\n            audio = SoundManager.add(this.settings.resolveURL(soundURL), () => this.currentAudio = void 0, () => this.currentAudio = void 0);\n            this.currentAudio = audio;\n          } catch (e) {\n            logger.warn(this.tag, \"Failed to create audio\", soundURL, e);\n          }\n        }\n      }\n      const motion = yield this.loadMotion(group, index);\n      if (audio) {\n        const readyToPlay = SoundManager.play(audio).catch((e) => logger.warn(this.tag, \"Failed to play audio\", audio.src, e));\n        if (config.motionSync) {\n          yield readyToPlay;\n        }\n      }\n      if (!this.state.start(motion, group, index, priority)) {\n        if (audio) {\n          SoundManager.dispose(audio);\n          this.currentAudio = void 0;\n        }\n        return false;\n      }\n      logger.log(this.tag, \"Start motion:\", this.getMotionName(definition));\n      this.emit(\"motionStart\", group, index, audio);\n      if (this.state.shouldOverrideExpression()) {\n        this.expressionManager && this.expressionManager.resetExpression();\n      }\n      this.playing = true;\n      this._startMotion(motion);\n      return true;\n    });\n  }\n  startRandomMotion(group, priority) {\n    return __async(this, null, function* () {\n      const groupDefs = this.definitions[group];\n      if (groupDefs == null ? void 0 : groupDefs.length) {\n        const availableIndices = [];\n        for (let i = 0; i < groupDefs.length; i++) {\n          if (this.motionGroups[group][i] !== null && !this.state.isActive(group, i)) {\n            availableIndices.push(i);\n          }\n        }\n        if (availableIndices.length) {\n          const index = Math.floor(Math.random() * availableIndices.length);\n          return this.startMotion(group, availableIndices[index], priority);\n        }\n      }\n      return false;\n    });\n  }\n  stopAllMotions() {\n    this._stopAllMotions();\n    this.state.reset();\n    if (this.currentAudio) {\n      SoundManager.dispose(this.currentAudio);\n      this.currentAudio = void 0;\n    }\n  }\n  update(model, now) {\n    var _a;\n    if (this.isFinished()) {\n      if (this.playing) {\n        this.playing = false;\n        this.emit(\"motionFinish\");\n      }\n      if (this.state.shouldOverrideExpression()) {\n        (_a = this.expressionManager) == null ? void 0 : _a.restoreExpression();\n      }\n      this.state.complete();\n      if (this.state.shouldRequestIdleMotion()) {\n        this.startRandomMotion(this.groups.idle, MotionPriority.IDLE);\n      }\n    }\n    return this.updateParameters(model, now);\n  }\n  destroy() {\n    var _a;\n    this.destroyed = true;\n    this.emit(\"destroy\");\n    this.stopAllMotions();\n    (_a = this.expressionManager) == null ? void 0 : _a.destroy();\n    const self = this;\n    self.definitions = void 0;\n    self.motionGroups = void 0;\n  }\n}\nconst tempBounds = { x: 0, y: 0, width: 0, height: 0 };\nclass InternalModel extends EventEmitter {\n  constructor() {\n    super(...arguments);\n    this.focusController = new FocusController();\n    this.originalWidth = 0;\n    this.originalHeight = 0;\n    this.width = 0;\n    this.height = 0;\n    this.localTransform = new Matrix();\n    this.drawingMatrix = new Matrix();\n    this.hitAreas = {};\n    this.textureFlipY = false;\n    this.viewport = [0, 0, 0, 0];\n    this.destroyed = false;\n  }\n  init() {\n    this.setupLayout();\n    this.setupHitAreas();\n  }\n  setupLayout() {\n    const self = this;\n    const size = this.getSize();\n    self.originalWidth = size[0];\n    self.originalHeight = size[1];\n    const layout = Object.assign({\n      width: LOGICAL_WIDTH,\n      height: LOGICAL_HEIGHT\n    }, this.getLayout());\n    this.localTransform.scale(layout.width / LOGICAL_WIDTH, layout.height / LOGICAL_HEIGHT);\n    self.width = this.originalWidth * this.localTransform.a;\n    self.height = this.originalHeight * this.localTransform.d;\n    const offsetX = layout.x !== void 0 && layout.x - layout.width / 2 || layout.centerX !== void 0 && layout.centerX || layout.left !== void 0 && layout.left - layout.width / 2 || layout.right !== void 0 && layout.right + layout.width / 2 || 0;\n    const offsetY = layout.y !== void 0 && layout.y - layout.height / 2 || layout.centerY !== void 0 && layout.centerY || layout.top !== void 0 && layout.top - layout.height / 2 || layout.bottom !== void 0 && layout.bottom + layout.height / 2 || 0;\n    this.localTransform.translate(this.width * offsetX, -this.height * offsetY);\n  }\n  setupHitAreas() {\n    const definitions = this.getHitAreaDefs().filter((hitArea) => hitArea.index >= 0);\n    for (const def of definitions) {\n      this.hitAreas[def.name] = def;\n    }\n  }\n  hitTest(x, y) {\n    return Object.keys(this.hitAreas).filter((hitAreaName) => this.isHit(hitAreaName, x, y));\n  }\n  isHit(hitAreaName, x, y) {\n    if (!this.hitAreas[hitAreaName]) {\n      return false;\n    }\n    const drawIndex = this.hitAreas[hitAreaName].index;\n    const bounds = this.getDrawableBounds(drawIndex, tempBounds);\n    return bounds.x <= x && x <= bounds.x + bounds.width && bounds.y <= y && y <= bounds.y + bounds.height;\n  }\n  getDrawableBounds(index, bounds) {\n    const vertices = this.getDrawableVertices(index);\n    let left = vertices[0];\n    let right = vertices[0];\n    let top = vertices[1];\n    let bottom = vertices[1];\n    for (let i = 0; i < vertices.length; i += 2) {\n      const vx = vertices[i];\n      const vy = vertices[i + 1];\n      left = Math.min(vx, left);\n      right = Math.max(vx, right);\n      top = Math.min(vy, top);\n      bottom = Math.max(vy, bottom);\n    }\n    bounds != null ? bounds : bounds = {};\n    bounds.x = left;\n    bounds.y = top;\n    bounds.width = right - left;\n    bounds.height = bottom - top;\n    return bounds;\n  }\n  updateTransform(transform) {\n    this.drawingMatrix.copyFrom(transform).append(this.localTransform);\n  }\n  update(dt, now) {\n    this.focusController.update(dt);\n  }\n  destroy() {\n    this.destroyed = true;\n    this.emit(\"destroy\");\n    this.motionManager.destroy();\n    this.motionManager = void 0;\n  }\n}\nconst TAG$1 = \"XHRLoader\";\nclass NetworkError extends Error {\n  constructor(message, url2, status, aborted = false) {\n    super(message);\n    this.url = url2;\n    this.status = status;\n    this.aborted = aborted;\n  }\n}\nconst _XHRLoader = class {\n  static createXHR(target, url2, type, onload, onerror) {\n    const xhr = new XMLHttpRequest();\n    _XHRLoader.allXhrSet.add(xhr);\n    if (target) {\n      let xhrSet = _XHRLoader.xhrMap.get(target);\n      if (!xhrSet) {\n        xhrSet = /* @__PURE__ */ new Set([xhr]);\n        _XHRLoader.xhrMap.set(target, xhrSet);\n      } else {\n        xhrSet.add(xhr);\n      }\n      if (!target.listeners(\"destroy\").includes(_XHRLoader.cancelXHRs)) {\n        target.once(\"destroy\", _XHRLoader.cancelXHRs);\n      }\n    }\n    xhr.open(\"GET\", url2);\n    xhr.responseType = type;\n    xhr.onload = () => {\n      if ((xhr.status === 200 || xhr.status === 0) && xhr.response) {\n        onload(xhr.response);\n      } else {\n        xhr.onerror();\n      }\n    };\n    xhr.onerror = () => {\n      logger.warn(TAG$1, `Failed to load resource as ${xhr.responseType} (Status ${xhr.status}): ${url2}`);\n      onerror(new NetworkError(\"Network error.\", url2, xhr.status));\n    };\n    xhr.onabort = () => onerror(new NetworkError(\"Aborted.\", url2, xhr.status, true));\n    xhr.onloadend = () => {\n      var _a;\n      _XHRLoader.allXhrSet.delete(xhr);\n      if (target) {\n        (_a = _XHRLoader.xhrMap.get(target)) == null ? void 0 : _a.delete(xhr);\n      }\n    };\n    return xhr;\n  }\n  static cancelXHRs() {\n    var _a;\n    (_a = _XHRLoader.xhrMap.get(this)) == null ? void 0 : _a.forEach((xhr) => {\n      xhr.abort();\n      _XHRLoader.allXhrSet.delete(xhr);\n    });\n    _XHRLoader.xhrMap.delete(this);\n  }\n  static release() {\n    _XHRLoader.allXhrSet.forEach((xhr) => xhr.abort());\n    _XHRLoader.allXhrSet.clear();\n    _XHRLoader.xhrMap = /* @__PURE__ */ new WeakMap();\n  }\n};\nlet XHRLoader = _XHRLoader;\nXHRLoader.xhrMap = /* @__PURE__ */ new WeakMap();\nXHRLoader.allXhrSet = /* @__PURE__ */ new Set();\nXHRLoader.loader = (context, next) => {\n  return new Promise((resolve, reject) => {\n    const xhr = _XHRLoader.createXHR(context.target, context.settings ? context.settings.resolveURL(context.url) : context.url, context.type, (data) => {\n      context.result = data;\n      resolve();\n    }, reject);\n    xhr.send();\n  });\n};\nfunction runMiddlewares(middleware, context) {\n  let index = -1;\n  return dispatch(0);\n  function dispatch(i, err) {\n    if (err)\n      return Promise.reject(err);\n    if (i <= index)\n      return Promise.reject(new Error(\"next() called multiple times\"));\n    index = i;\n    const fn = middleware[i];\n    if (!fn)\n      return Promise.resolve();\n    try {\n      return Promise.resolve(fn(context, dispatch.bind(null, i + 1)));\n    } catch (err2) {\n      return Promise.reject(err2);\n    }\n  }\n}\nclass Live2DLoader {\n  static load(context) {\n    return runMiddlewares(this.middlewares, context).then(() => context.result);\n  }\n}\nLive2DLoader.middlewares = [XHRLoader.loader];\nfunction createTexture(url2, options = {}) {\n  var _a;\n  const textureOptions = { resourceOptions: { crossorigin: options.crossOrigin } };\n  if (Texture.fromURL) {\n    return Texture.fromURL(url2, textureOptions).catch((e) => {\n      if (e instanceof Error) {\n        throw e;\n      }\n      const err = new Error(\"Texture loading error\");\n      err.event = e;\n      throw err;\n    });\n  }\n  textureOptions.resourceOptions.autoLoad = false;\n  const texture = Texture.from(url2, textureOptions);\n  if (texture.baseTexture.valid) {\n    return Promise.resolve(texture);\n  }\n  const resource = texture.baseTexture.resource;\n  (_a = resource._live2d_load) != null ? _a : resource._live2d_load = new Promise((resolve, reject) => {\n    const errorHandler = (event) => {\n      resource.source.removeEventListener(\"error\", errorHandler);\n      const err = new Error(\"Texture loading error\");\n      err.event = event;\n      reject(err);\n    };\n    resource.source.addEventListener(\"error\", errorHandler);\n    resource.load().then(() => resolve(texture)).catch(errorHandler);\n  });\n  return resource._live2d_load;\n}\nconst TAG = \"Live2DFactory\";\nconst urlToJSON = (context, next) => __async(void 0, null, function* () {\n  if (typeof context.source === \"string\") {\n    const data = yield Live2DLoader.load({\n      url: context.source,\n      type: \"json\",\n      target: context.live2dModel\n    });\n    data.url = context.source;\n    context.source = data;\n    context.live2dModel.emit(\"settingsJSONLoaded\", data);\n  }\n  return next();\n});\nconst jsonToSettings = (context, next) => __async(void 0, null, function* () {\n  if (context.source instanceof ModelSettings) {\n    context.settings = context.source;\n    return next();\n  } else if (typeof context.source === \"object\") {\n    const runtime = Live2DFactory.findRuntime(context.source);\n    if (runtime) {\n      const settings = runtime.createModelSettings(context.source);\n      context.settings = settings;\n      context.live2dModel.emit(\"settingsLoaded\", settings);\n      return next();\n    }\n  }\n  throw new TypeError(\"Unknown settings format.\");\n});\nconst waitUntilReady = (context, next) => {\n  if (context.settings) {\n    const runtime = Live2DFactory.findRuntime(context.settings);\n    if (runtime) {\n      return runtime.ready().then(next);\n    }\n  }\n  return next();\n};\nconst setupOptionals = (context, next) => __async(void 0, null, function* () {\n  yield next();\n  const internalModel = context.internalModel;\n  if (internalModel) {\n    const settings = context.settings;\n    const runtime = Live2DFactory.findRuntime(settings);\n    if (runtime) {\n      const tasks = [];\n      if (settings.pose) {\n        tasks.push(Live2DLoader.load({\n          settings,\n          url: settings.pose,\n          type: \"json\",\n          target: internalModel\n        }).then((data) => {\n          internalModel.pose = runtime.createPose(internalModel.coreModel, data);\n          context.live2dModel.emit(\"poseLoaded\", internalModel.pose);\n        }).catch((e) => {\n          context.live2dModel.emit(\"poseLoadError\", e);\n          logger.warn(TAG, \"Failed to load pose.\", e);\n        }));\n      }\n      if (settings.physics) {\n        tasks.push(Live2DLoader.load({\n          settings,\n          url: settings.physics,\n          type: \"json\",\n          target: internalModel\n        }).then((data) => {\n          internalModel.physics = runtime.createPhysics(internalModel.coreModel, data);\n          context.live2dModel.emit(\"physicsLoaded\", internalModel.physics);\n        }).catch((e) => {\n          context.live2dModel.emit(\"physicsLoadError\", e);\n          logger.warn(TAG, \"Failed to load physics.\", e);\n        }));\n      }\n      if (tasks.length) {\n        yield Promise.all(tasks);\n      }\n    }\n  }\n});\nconst setupEssentials = (context, next) => __async(void 0, null, function* () {\n  if (context.settings) {\n    const live2DModel = context.live2dModel;\n    const textureLoadings = context.settings.textures.map((tex) => {\n      const url2 = context.settings.resolveURL(tex);\n      return createTexture(url2, { crossOrigin: context.options.crossOrigin });\n    });\n    yield next();\n    if (context.internalModel) {\n      live2DModel.internalModel = context.internalModel;\n      live2DModel.emit(\"modelLoaded\", context.internalModel);\n    } else {\n      throw new TypeError(\"Missing internal model.\");\n    }\n    live2DModel.textures = yield Promise.all(textureLoadings);\n    live2DModel.emit(\"textureLoaded\", live2DModel.textures);\n  } else {\n    throw new TypeError(\"Missing settings.\");\n  }\n});\nconst createInternalModel = (context, next) => __async(void 0, null, function* () {\n  const settings = context.settings;\n  if (settings instanceof ModelSettings) {\n    const runtime = Live2DFactory.findRuntime(settings);\n    if (!runtime) {\n      throw new TypeError(\"Unknown model settings.\");\n    }\n    const modelData = yield Live2DLoader.load({\n      settings,\n      url: settings.moc,\n      type: \"arraybuffer\",\n      target: context.live2dModel\n    });\n    if (!runtime.isValidMoc(modelData)) {\n      throw new Error(\"Invalid moc data\");\n    }\n    const coreModel = runtime.createCoreModel(modelData);\n    context.internalModel = runtime.createInternalModel(coreModel, settings, context.options);\n    return next();\n  }\n  throw new TypeError(\"Missing settings.\");\n});\nconst _Live2DFactory = class {\n  static registerRuntime(runtime) {\n    _Live2DFactory.runtimes.push(runtime);\n    _Live2DFactory.runtimes.sort((a, b) => b.version - a.version);\n  }\n  static findRuntime(source) {\n    for (const runtime of _Live2DFactory.runtimes) {\n      if (runtime.test(source)) {\n        return runtime;\n      }\n    }\n  }\n  static setupLive2DModel(live2dModel, source, options) {\n    return __async(this, null, function* () {\n      const textureLoaded = new Promise((resolve) => live2dModel.once(\"textureLoaded\", resolve));\n      const modelLoaded = new Promise((resolve) => live2dModel.once(\"modelLoaded\", resolve));\n      const readyEventEmitted = Promise.all([textureLoaded, modelLoaded]).then(() => live2dModel.emit(\"ready\"));\n      yield runMiddlewares(_Live2DFactory.live2DModelMiddlewares, {\n        live2dModel,\n        source,\n        options: options || {}\n      });\n      yield readyEventEmitted;\n      live2dModel.emit(\"load\");\n    });\n  }\n  static loadMotion(motionManager, group, index) {\n    var _a, _b;\n    const handleError = (e) => motionManager.emit(\"motionLoadError\", group, index, e);\n    try {\n      const definition = (_a = motionManager.definitions[group]) == null ? void 0 : _a[index];\n      if (!definition) {\n        return Promise.resolve(void 0);\n      }\n      if (!motionManager.listeners(\"destroy\").includes(_Live2DFactory.releaseTasks)) {\n        motionManager.once(\"destroy\", _Live2DFactory.releaseTasks);\n      }\n      let tasks = _Live2DFactory.motionTasksMap.get(motionManager);\n      if (!tasks) {\n        tasks = {};\n        _Live2DFactory.motionTasksMap.set(motionManager, tasks);\n      }\n      let taskGroup = tasks[group];\n      if (!taskGroup) {\n        taskGroup = [];\n        tasks[group] = taskGroup;\n      }\n      const path = motionManager.getMotionFile(definition);\n      (_b = taskGroup[index]) != null ? _b : taskGroup[index] = Live2DLoader.load({\n        url: path,\n        settings: motionManager.settings,\n        type: motionManager.motionDataType,\n        target: motionManager\n      }).then((data) => {\n        var _a2;\n        const taskGroup2 = (_a2 = _Live2DFactory.motionTasksMap.get(motionManager)) == null ? void 0 : _a2[group];\n        if (taskGroup2) {\n          delete taskGroup2[index];\n        }\n        const motion = motionManager.createMotion(data, group, definition);\n        motionManager.emit(\"motionLoaded\", group, index, motion);\n        return motion;\n      }).catch((e) => {\n        logger.warn(motionManager.tag, `Failed to load motion: ${path}\n`, e);\n        handleError(e);\n      });\n      return taskGroup[index];\n    } catch (e) {\n      logger.warn(motionManager.tag, `Failed to load motion at \"${group}\"[${index}]\n`, e);\n      handleError(e);\n    }\n    return Promise.resolve(void 0);\n  }\n  static loadExpression(expressionManager, index) {\n    var _a;\n    const handleError = (e) => expressionManager.emit(\"expressionLoadError\", index, e);\n    try {\n      const definition = expressionManager.definitions[index];\n      if (!definition) {\n        return Promise.resolve(void 0);\n      }\n      if (!expressionManager.listeners(\"destroy\").includes(_Live2DFactory.releaseTasks)) {\n        expressionManager.once(\"destroy\", _Live2DFactory.releaseTasks);\n      }\n      let tasks = _Live2DFactory.expressionTasksMap.get(expressionManager);\n      if (!tasks) {\n        tasks = [];\n        _Live2DFactory.expressionTasksMap.set(expressionManager, tasks);\n      }\n      const path = expressionManager.getExpressionFile(definition);\n      (_a = tasks[index]) != null ? _a : tasks[index] = Live2DLoader.load({\n        url: path,\n        settings: expressionManager.settings,\n        type: \"json\",\n        target: expressionManager\n      }).then((data) => {\n        const tasks2 = _Live2DFactory.expressionTasksMap.get(expressionManager);\n        if (tasks2) {\n          delete tasks2[index];\n        }\n        const expression = expressionManager.createExpression(data, definition);\n        expressionManager.emit(\"expressionLoaded\", index, expression);\n        return expression;\n      }).catch((e) => {\n        logger.warn(expressionManager.tag, `Failed to load expression: ${path}\n`, e);\n        handleError(e);\n      });\n      return tasks[index];\n    } catch (e) {\n      logger.warn(expressionManager.tag, `Failed to load expression at [${index}]\n`, e);\n      handleError(e);\n    }\n    return Promise.resolve(void 0);\n  }\n  static releaseTasks() {\n    if (this instanceof MotionManager) {\n      _Live2DFactory.motionTasksMap.delete(this);\n    } else {\n      _Live2DFactory.expressionTasksMap.delete(this);\n    }\n  }\n};\nlet Live2DFactory = _Live2DFactory;\nLive2DFactory.runtimes = [];\nLive2DFactory.urlToJSON = urlToJSON;\nLive2DFactory.jsonToSettings = jsonToSettings;\nLive2DFactory.waitUntilReady = waitUntilReady;\nLive2DFactory.setupOptionals = setupOptionals;\nLive2DFactory.setupEssentials = setupEssentials;\nLive2DFactory.createInternalModel = createInternalModel;\nLive2DFactory.live2DModelMiddlewares = [\n  urlToJSON,\n  jsonToSettings,\n  waitUntilReady,\n  setupOptionals,\n  setupEssentials,\n  createInternalModel\n];\nLive2DFactory.motionTasksMap = /* @__PURE__ */ new WeakMap();\nLive2DFactory.expressionTasksMap = /* @__PURE__ */ new WeakMap();\nMotionManager.prototype[\"_loadMotion\"] = function(group, index) {\n  return Live2DFactory.loadMotion(this, group, index);\n};\nExpressionManager.prototype[\"_loadExpression\"] = function(index) {\n  return Live2DFactory.loadExpression(this, index);\n};\nclass InteractionMixin {\n  constructor() {\n    this._autoInteract = false;\n  }\n  get autoInteract() {\n    return this._autoInteract;\n  }\n  set autoInteract(autoInteract) {\n    if (autoInteract !== this._autoInteract) {\n      if (autoInteract) {\n        this.on(\"pointertap\", onTap, this);\n      } else {\n        this.off(\"pointertap\", onTap, this);\n      }\n      this._autoInteract = autoInteract;\n    }\n  }\n  registerInteraction(manager) {\n    if (manager !== this.interactionManager) {\n      this.unregisterInteraction();\n      if (this._autoInteract && manager) {\n        this.interactionManager = manager;\n        manager.on(\"pointermove\", onPointerMove, this);\n      }\n    }\n  }\n  unregisterInteraction() {\n    var _a;\n    if (this.interactionManager) {\n      (_a = this.interactionManager) == null ? void 0 : _a.off(\"pointermove\", onPointerMove, this);\n      this.interactionManager = void 0;\n    }\n  }\n}\nfunction onTap(event) {\n  this.tap(event.data.global.x, event.data.global.y);\n}\nfunction onPointerMove(event) {\n  this.focus(event.data.global.x, event.data.global.y);\n}\nclass Live2DTransform extends Transform {\n}\nconst tempPoint = new Point();\nconst tempMatrix$1 = new Matrix();\nlet tickerRef;\nclass Live2DModel extends Container {\n  constructor(options) {\n    super();\n    this.tag = \"Live2DModel(uninitialized)\";\n    this.textures = [];\n    this.transform = new Live2DTransform();\n    this.anchor = new ObservablePoint(this.onAnchorChange, this, 0, 0);\n    this.glContextID = -1;\n    this.elapsedTime = performance.now();\n    this.deltaTime = 0;\n    this._autoUpdate = false;\n    this.once(\"modelLoaded\", () => this.init(options));\n  }\n  static from(source, options) {\n    const model = new this(options);\n    return Live2DFactory.setupLive2DModel(model, source, options).then(() => model);\n  }\n  static fromSync(source, options) {\n    const model = new this(options);\n    Live2DFactory.setupLive2DModel(model, source, options).then(options == null ? void 0 : options.onLoad).catch(options == null ? void 0 : options.onError);\n    return model;\n  }\n  static registerTicker(tickerClass) {\n    tickerRef = tickerClass;\n  }\n  get autoUpdate() {\n    return this._autoUpdate;\n  }\n  set autoUpdate(autoUpdate) {\n    var _a;\n    tickerRef || (tickerRef = (_a = window.PIXI) == null ? void 0 : _a.Ticker);\n    if (autoUpdate) {\n      if (!this._destroyed) {\n        if (tickerRef) {\n          tickerRef.shared.add(this.onTickerUpdate, this);\n          this._autoUpdate = true;\n        } else {\n          logger.warn(this.tag, \"No Ticker registered, please call Live2DModel.registerTicker(Ticker).\");\n        }\n      }\n    } else {\n      tickerRef == null ? void 0 : tickerRef.shared.remove(this.onTickerUpdate, this);\n      this._autoUpdate = false;\n    }\n  }\n  init(options) {\n    this.tag = `Live2DModel(${this.internalModel.settings.name})`;\n    const _options = Object.assign({\n      autoUpdate: true,\n      autoInteract: true\n    }, options);\n    if (_options.autoInteract) {\n      this.interactive = true;\n    }\n    this.autoInteract = _options.autoInteract;\n    this.autoUpdate = _options.autoUpdate;\n  }\n  onAnchorChange() {\n    this.pivot.set(this.anchor.x * this.internalModel.width, this.anchor.y * this.internalModel.height);\n  }\n  motion(group, index, priority) {\n    return index === void 0 ? this.internalModel.motionManager.startRandomMotion(group, priority) : this.internalModel.motionManager.startMotion(group, index, priority);\n  }\n  expression(id) {\n    if (this.internalModel.motionManager.expressionManager) {\n      return id === void 0 ? this.internalModel.motionManager.expressionManager.setRandomExpression() : this.internalModel.motionManager.expressionManager.setExpression(id);\n    }\n    return Promise.resolve(false);\n  }\n  focus(x, y, instant = false) {\n    tempPoint.x = x;\n    tempPoint.y = y;\n    this.toModelPosition(tempPoint, tempPoint, true);\n    let tx = tempPoint.x / this.internalModel.originalWidth * 2 - 1;\n    let ty = tempPoint.y / this.internalModel.originalHeight * 2 - 1;\n    let radian = Math.atan2(ty, tx);\n    this.internalModel.focusController.focus(Math.cos(radian), -Math.sin(radian), instant);\n  }\n  tap(x, y) {\n    const hitAreaNames = this.hitTest(x, y);\n    if (hitAreaNames.length) {\n      logger.log(this.tag, `Hit`, hitAreaNames);\n      this.emit(\"hit\", hitAreaNames);\n    }\n  }\n  hitTest(x, y) {\n    tempPoint.x = x;\n    tempPoint.y = y;\n    this.toModelPosition(tempPoint, tempPoint);\n    return this.internalModel.hitTest(tempPoint.x, tempPoint.y);\n  }\n  toModelPosition(position, result = position.clone(), skipUpdate) {\n    if (!skipUpdate) {\n      this._recursivePostUpdateTransform();\n      if (!this.parent) {\n        this.parent = this._tempDisplayObjectParent;\n        this.displayObjectUpdateTransform();\n        this.parent = null;\n      } else {\n        this.displayObjectUpdateTransform();\n      }\n    }\n    this.transform.worldTransform.applyInverse(position, result);\n    this.internalModel.localTransform.applyInverse(result, result);\n    return result;\n  }\n  containsPoint(point) {\n    return this.getBounds(true).contains(point.x, point.y);\n  }\n  _calculateBounds() {\n    this._bounds.addFrame(this.transform, 0, 0, this.internalModel.width, this.internalModel.height);\n  }\n  onTickerUpdate() {\n    this.update(tickerRef.shared.deltaMS);\n  }\n  update(dt) {\n    this.deltaTime += dt;\n    this.elapsedTime += dt;\n  }\n  _render(renderer) {\n    this.registerInteraction(renderer.plugins.interaction);\n    renderer.batch.reset();\n    renderer.geometry.reset();\n    renderer.shader.reset();\n    renderer.state.reset();\n    let shouldUpdateTexture = false;\n    if (this.glContextID !== renderer.CONTEXT_UID) {\n      this.glContextID = renderer.CONTEXT_UID;\n      this.internalModel.updateWebGLContext(renderer.gl, this.glContextID);\n      shouldUpdateTexture = true;\n    }\n    for (let i = 0; i < this.textures.length; i++) {\n      const texture = this.textures[i];\n      if (!texture.valid) {\n        continue;\n      }\n      if (shouldUpdateTexture || !texture.baseTexture._glTextures[this.glContextID]) {\n        renderer.gl.pixelStorei(WebGLRenderingContext.UNPACK_FLIP_Y_WEBGL, this.internalModel.textureFlipY);\n        renderer.texture.bind(texture.baseTexture, 0);\n      }\n      this.internalModel.bindTexture(i, texture.baseTexture._glTextures[this.glContextID].texture);\n      texture.baseTexture.touched = renderer.textureGC.count;\n    }\n    const viewport = renderer.framebuffer.viewport;\n    this.internalModel.viewport = [viewport.x, viewport.y, viewport.width, viewport.height];\n    if (this.deltaTime) {\n      this.internalModel.update(this.deltaTime, this.elapsedTime);\n      this.deltaTime = 0;\n    }\n    const internalTransform = tempMatrix$1.copyFrom(renderer.globalUniforms.uniforms.projectionMatrix).append(this.worldTransform);\n    this.internalModel.updateTransform(internalTransform);\n    this.internalModel.draw(renderer.gl);\n    renderer.state.reset();\n    renderer.texture.reset();\n  }\n  destroy(options) {\n    this.emit(\"destroy\");\n    this.autoUpdate = false;\n    this.unregisterInteraction();\n    if (options == null ? void 0 : options.texture) {\n      this.textures.forEach((texture) => texture.destroy(options.baseTexture));\n    }\n    this.internalModel.destroy();\n    super.destroy(options);\n  }\n}\napplyMixins(Live2DModel, [InteractionMixin]);\nconst _FileLoader = class {\n  static resolveURL(settingsURL, filePath) {\n    var _a;\n    const resolved = (_a = _FileLoader.filesMap[settingsURL]) == null ? void 0 : _a[filePath];\n    if (resolved === void 0) {\n      throw new Error(\"Cannot find this file from uploaded files: \" + filePath);\n    }\n    return resolved;\n  }\n  static upload(files, settings) {\n    return __async(this, null, function* () {\n      const fileMap = {};\n      for (const definedFile of settings.getDefinedFiles()) {\n        const actualPath = decodeURI(url.resolve(settings.url, definedFile));\n        const actualFile = files.find((file) => file.webkitRelativePath === actualPath);\n        if (actualFile) {\n          fileMap[definedFile] = URL.createObjectURL(actualFile);\n        }\n      }\n      _FileLoader.filesMap[settings._objectURL] = fileMap;\n    });\n  }\n  static createSettings(files) {\n    return __async(this, null, function* () {\n      const settingsFile = files.find((file) => file.name.endsWith(\"model.json\") || file.name.endsWith(\"model3.json\"));\n      if (!settingsFile) {\n        throw new TypeError(\"Settings file not found\");\n      }\n      const settingsText = yield _FileLoader.readText(settingsFile);\n      const settingsJSON = JSON.parse(settingsText);\n      settingsJSON.url = settingsFile.webkitRelativePath;\n      const runtime = Live2DFactory.findRuntime(settingsJSON);\n      if (!runtime) {\n        throw new Error(\"Unknown settings JSON\");\n      }\n      const settings = runtime.createModelSettings(settingsJSON);\n      settings._objectURL = URL.createObjectURL(settingsFile);\n      return settings;\n    });\n  }\n  static readText(file) {\n    return __async(this, null, function* () {\n      return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsText(file, \"utf8\");\n      });\n    });\n  }\n};\nlet FileLoader = _FileLoader;\nFileLoader.filesMap = {};\nFileLoader.factory = (context, next) => __async(void 0, null, function* () {\n  if (Array.isArray(context.source) && context.source[0] instanceof File) {\n    const files = context.source;\n    let settings = files.settings;\n    if (!settings) {\n      settings = yield _FileLoader.createSettings(files);\n    } else if (!settings._objectURL) {\n      throw new Error('\"_objectURL\" must be specified in ModelSettings');\n    }\n    settings.validateFiles(files.map((file) => encodeURI(file.webkitRelativePath)));\n    yield _FileLoader.upload(files, settings);\n    settings.resolveURL = function(url2) {\n      return _FileLoader.resolveURL(this._objectURL, url2);\n    };\n    context.source = settings;\n    context.live2dModel.once(\"modelLoaded\", (internalModel) => {\n      internalModel.once(\"destroy\", function() {\n        const objectURL = this.settings._objectURL;\n        URL.revokeObjectURL(objectURL);\n        if (_FileLoader.filesMap[objectURL]) {\n          for (const resourceObjectURL of Object.values(_FileLoader.filesMap[objectURL])) {\n            URL.revokeObjectURL(resourceObjectURL);\n          }\n        }\n        delete _FileLoader.filesMap[objectURL];\n      });\n    });\n  }\n  return next();\n});\nLive2DFactory.live2DModelMiddlewares.unshift(FileLoader.factory);\nconst _ZipLoader = class {\n  static unzip(reader, settings) {\n    return __async(this, null, function* () {\n      const filePaths = yield _ZipLoader.getFilePaths(reader);\n      const requiredFilePaths = [];\n      for (const definedFile of settings.getDefinedFiles()) {\n        const actualPath = decodeURI(url.resolve(settings.url, definedFile));\n        if (filePaths.includes(actualPath)) {\n          requiredFilePaths.push(actualPath);\n        }\n      }\n      const files = yield _ZipLoader.getFiles(reader, requiredFilePaths);\n      for (let i = 0; i < files.length; i++) {\n        const path = requiredFilePaths[i];\n        const file = files[i];\n        Object.defineProperty(file, \"webkitRelativePath\", {\n          value: path\n        });\n      }\n      return files;\n    });\n  }\n  static createSettings(reader) {\n    return __async(this, null, function* () {\n      const filePaths = yield _ZipLoader.getFilePaths(reader);\n      const settingsFilePath = filePaths.find((path) => path.endsWith(\"model.json\") || path.endsWith(\"model3.json\"));\n      if (!settingsFilePath) {\n        throw new Error(\"Settings file not found\");\n      }\n      const settingsText = yield _ZipLoader.readText(reader, settingsFilePath);\n      if (!settingsText) {\n        throw new Error(\"Empty settings file: \" + settingsFilePath);\n      }\n      const settingsJSON = JSON.parse(settingsText);\n      settingsJSON.url = settingsFilePath;\n      const runtime = Live2DFactory.findRuntime(settingsJSON);\n      if (!runtime) {\n        throw new Error(\"Unknown settings JSON\");\n      }\n      return runtime.createModelSettings(settingsJSON);\n    });\n  }\n  static zipReader(data, url2) {\n    return __async(this, null, function* () {\n      throw new Error(\"Not implemented\");\n    });\n  }\n  static getFilePaths(reader) {\n    return __async(this, null, function* () {\n      throw new Error(\"Not implemented\");\n    });\n  }\n  static getFiles(reader, paths) {\n    return __async(this, null, function* () {\n      throw new Error(\"Not implemented\");\n    });\n  }\n  static readText(reader, path) {\n    return __async(this, null, function* () {\n      throw new Error(\"Not implemented\");\n    });\n  }\n  static releaseReader(reader) {\n  }\n};\nlet ZipLoader = _ZipLoader;\nZipLoader.ZIP_PROTOCOL = \"zip://\";\nZipLoader.uid = 0;\nZipLoader.factory = (context, next) => __async(void 0, null, function* () {\n  const source = context.source;\n  let sourceURL;\n  let zipBlob;\n  let settings;\n  if (typeof source === \"string\" && (source.endsWith(\".zip\") || source.startsWith(_ZipLoader.ZIP_PROTOCOL))) {\n    if (source.startsWith(_ZipLoader.ZIP_PROTOCOL)) {\n      sourceURL = source.slice(_ZipLoader.ZIP_PROTOCOL.length);\n    } else {\n      sourceURL = source;\n    }\n    zipBlob = yield Live2DLoader.load({\n      url: sourceURL,\n      type: \"blob\",\n      target: context.live2dModel\n    });\n  } else if (Array.isArray(source) && source.length === 1 && source[0] instanceof File && source[0].name.endsWith(\".zip\")) {\n    zipBlob = source[0];\n    sourceURL = URL.createObjectURL(zipBlob);\n    settings = source.settings;\n  }\n  if (zipBlob) {\n    if (!zipBlob.size) {\n      throw new Error(\"Empty zip file\");\n    }\n    const reader = yield _ZipLoader.zipReader(zipBlob, sourceURL);\n    if (!settings) {\n      settings = yield _ZipLoader.createSettings(reader);\n    }\n    settings._objectURL = _ZipLoader.ZIP_PROTOCOL + _ZipLoader.uid + \"/\" + settings.url;\n    const files = yield _ZipLoader.unzip(reader, settings);\n    files.settings = settings;\n    context.source = files;\n    if (sourceURL.startsWith(\"blob:\")) {\n      context.live2dModel.once(\"modelLoaded\", (internalModel) => {\n        internalModel.once(\"destroy\", function() {\n          URL.revokeObjectURL(sourceURL);\n        });\n      });\n    }\n    _ZipLoader.releaseReader(reader);\n  }\n  return next();\n});\nLive2DFactory.live2DModelMiddlewares.unshift(ZipLoader.factory);\nif (!window.Live2D) {\n  throw new Error(\"Could not find Cubism 2 runtime. This plugin requires live2d.min.js to be loaded.\");\n}\nconst originalUpdateParam = Live2DMotion.prototype.updateParam;\nLive2DMotion.prototype.updateParam = function(model, entry) {\n  originalUpdateParam.call(this, model, entry);\n  if (entry.isFinished() && this.onFinishHandler) {\n    this.onFinishHandler(this);\n    delete this.onFinishHandler;\n  }\n};\nclass Live2DExpression extends AMotion {\n  constructor(json) {\n    super();\n    this.params = [];\n    this.setFadeIn(json.fade_in > 0 ? json.fade_in : config.expressionFadingDuration);\n    this.setFadeOut(json.fade_out > 0 ? json.fade_out : config.expressionFadingDuration);\n    if (Array.isArray(json.params)) {\n      json.params.forEach((param) => {\n        const calc = param.calc || \"add\";\n        if (calc === \"add\") {\n          const defaultValue = param.def || 0;\n          param.val -= defaultValue;\n        } else if (calc === \"mult\") {\n          const defaultValue = param.def || 1;\n          param.val /= defaultValue;\n        }\n        this.params.push({\n          calc,\n          val: param.val,\n          id: param.id\n        });\n      });\n    }\n  }\n  updateParamExe(model, time, weight, motionQueueEnt) {\n    this.params.forEach((param) => {\n      model.setParamFloat(param.id, param.val * weight);\n    });\n  }\n}\nclass Cubism2ExpressionManager extends ExpressionManager {\n  constructor(settings, options) {\n    var _a;\n    super(settings, options);\n    this.queueManager = new MotionQueueManager();\n    this.definitions = (_a = this.settings.expressions) != null ? _a : [];\n    this.init();\n  }\n  isFinished() {\n    return this.queueManager.isFinished();\n  }\n  getExpressionIndex(name) {\n    return this.definitions.findIndex((def) => def.name === name);\n  }\n  getExpressionFile(definition) {\n    return definition.file;\n  }\n  createExpression(data, definition) {\n    return new Live2DExpression(data);\n  }\n  _setExpression(motion) {\n    return this.queueManager.startMotion(motion);\n  }\n  stopAllExpressions() {\n    this.queueManager.stopAllMotions();\n  }\n  updateParameters(model, dt) {\n    return this.queueManager.updateParam(model);\n  }\n}\nclass Cubism2MotionManager extends MotionManager {\n  constructor(settings, options) {\n    super(settings, options);\n    this.groups = { idle: \"idle\" };\n    this.motionDataType = \"arraybuffer\";\n    this.queueManager = new MotionQueueManager();\n    this.definitions = this.settings.motions;\n    this.init(options);\n  }\n  init(options) {\n    super.init(options);\n    if (this.settings.expressions) {\n      this.expressionManager = new Cubism2ExpressionManager(this.settings, options);\n    }\n  }\n  isFinished() {\n    return this.queueManager.isFinished();\n  }\n  createMotion(data, group, definition) {\n    const motion = Live2DMotion.loadMotion(data);\n    const defaultFadingDuration = group === this.groups.idle ? config.idleMotionFadingDuration : config.motionFadingDuration;\n    motion.setFadeIn(definition.fade_in > 0 ? definition.fade_in : defaultFadingDuration);\n    motion.setFadeOut(definition.fade_out > 0 ? definition.fade_out : defaultFadingDuration);\n    return motion;\n  }\n  getMotionFile(definition) {\n    return definition.file;\n  }\n  getMotionName(definition) {\n    return definition.file;\n  }\n  getSoundFile(definition) {\n    return definition.sound;\n  }\n  _startMotion(motion, onFinish) {\n    motion.onFinishHandler = onFinish;\n    this.queueManager.stopAllMotions();\n    return this.queueManager.startMotion(motion);\n  }\n  _stopAllMotions() {\n    this.queueManager.stopAllMotions();\n  }\n  updateParameters(model, now) {\n    return this.queueManager.updateParam(model);\n  }\n  destroy() {\n    super.destroy();\n    this.queueManager = void 0;\n  }\n}\nclass Live2DEyeBlink {\n  constructor(coreModel) {\n    this.coreModel = coreModel;\n    this.blinkInterval = 4e3;\n    this.closingDuration = 100;\n    this.closedDuration = 50;\n    this.openingDuration = 150;\n    this.eyeState = 0;\n    this.eyeParamValue = 1;\n    this.closedTimer = 0;\n    this.nextBlinkTimeLeft = this.blinkInterval;\n    this.leftParam = coreModel.getParamIndex(\"PARAM_EYE_L_OPEN\");\n    this.rightParam = coreModel.getParamIndex(\"PARAM_EYE_R_OPEN\");\n  }\n  setEyeParams(value) {\n    this.eyeParamValue = clamp(value, 0, 1);\n    this.coreModel.setParamFloat(this.leftParam, this.eyeParamValue);\n    this.coreModel.setParamFloat(this.rightParam, this.eyeParamValue);\n  }\n  update(dt) {\n    switch (this.eyeState) {\n      case 0:\n        this.nextBlinkTimeLeft -= dt;\n        if (this.nextBlinkTimeLeft < 0) {\n          this.eyeState = 1;\n          this.nextBlinkTimeLeft = this.blinkInterval + this.closingDuration + this.closedDuration + this.openingDuration + rand(0, 2e3);\n        }\n        break;\n      case 1:\n        this.setEyeParams(this.eyeParamValue + dt / this.closingDuration);\n        if (this.eyeParamValue <= 0) {\n          this.eyeState = 2;\n          this.closedTimer = 0;\n        }\n        break;\n      case 2:\n        this.closedTimer += dt;\n        if (this.closedTimer >= this.closedDuration) {\n          this.eyeState = 3;\n        }\n        break;\n      case 3:\n        this.setEyeParams(this.eyeParamValue + dt / this.openingDuration);\n        if (this.eyeParamValue >= 1) {\n          this.eyeState = 0;\n        }\n    }\n  }\n}\nconst tempMatrixArray = new Float32Array([\n  1,\n  0,\n  0,\n  0,\n  0,\n  1,\n  0,\n  0,\n  0,\n  0,\n  1,\n  0,\n  0,\n  0,\n  0,\n  1\n]);\nclass Cubism2InternalModel extends InternalModel {\n  constructor(coreModel, settings, options) {\n    super();\n    this.textureFlipY = true;\n    this.drawDataCount = 0;\n    this.disableCulling = false;\n    this.coreModel = coreModel;\n    this.settings = settings;\n    this.motionManager = new Cubism2MotionManager(settings, options);\n    this.eyeBlink = new Live2DEyeBlink(coreModel);\n    this.eyeballXParamIndex = coreModel.getParamIndex(\"PARAM_EYE_BALL_X\");\n    this.eyeballYParamIndex = coreModel.getParamIndex(\"PARAM_EYE_BALL_Y\");\n    this.angleXParamIndex = coreModel.getParamIndex(\"PARAM_ANGLE_X\");\n    this.angleYParamIndex = coreModel.getParamIndex(\"PARAM_ANGLE_Y\");\n    this.angleZParamIndex = coreModel.getParamIndex(\"PARAM_ANGLE_Z\");\n    this.bodyAngleXParamIndex = coreModel.getParamIndex(\"PARAM_BODY_ANGLE_X\");\n    this.breathParamIndex = coreModel.getParamIndex(\"PARAM_BREATH\");\n    this.init();\n  }\n  init() {\n    super.init();\n    if (this.settings.initParams) {\n      this.settings.initParams.forEach(({ id, value }) => this.coreModel.setParamFloat(id, value));\n    }\n    if (this.settings.initOpacities) {\n      this.settings.initOpacities.forEach(({ id, value }) => this.coreModel.setPartsOpacity(id, value));\n    }\n    this.coreModel.saveParam();\n    const arr = this.coreModel.getModelContext()._$aS;\n    if (arr == null ? void 0 : arr.length) {\n      this.drawDataCount = arr.length;\n    }\n    let culling = this.coreModel.drawParamWebGL.culling;\n    Object.defineProperty(this.coreModel.drawParamWebGL, \"culling\", {\n      set: (v) => culling = v,\n      get: () => this.disableCulling ? false : culling\n    });\n    const clipManager = this.coreModel.getModelContext().clipManager;\n    const originalSetupClip = clipManager.setupClip;\n    clipManager.setupClip = (modelContext, drawParam) => {\n      originalSetupClip.call(clipManager, modelContext, drawParam);\n      drawParam.gl.viewport(...this.viewport);\n    };\n  }\n  getSize() {\n    return [this.coreModel.getCanvasWidth(), this.coreModel.getCanvasHeight()];\n  }\n  getLayout() {\n    const layout = {};\n    if (this.settings.layout) {\n      for (const key of Object.keys(this.settings.layout)) {\n        let commonKey = key;\n        if (key === \"center_x\") {\n          commonKey = \"centerX\";\n        } else if (key === \"center_y\") {\n          commonKey = \"centerY\";\n        }\n        layout[commonKey] = this.settings.layout[key];\n      }\n    }\n    return layout;\n  }\n  updateWebGLContext(gl, glContextID) {\n    const drawParamWebGL = this.coreModel.drawParamWebGL;\n    drawParamWebGL.firstDraw = true;\n    drawParamWebGL.setGL(gl);\n    drawParamWebGL.glno = glContextID;\n    for (const prop in drawParamWebGL) {\n      if (drawParamWebGL.hasOwnProperty(prop) && drawParamWebGL[prop] instanceof WebGLBuffer) {\n        drawParamWebGL[prop] = null;\n      }\n    }\n    const clipManager = this.coreModel.getModelContext().clipManager;\n    clipManager.curFrameNo = glContextID;\n    const framebuffer = gl.getParameter(gl.FRAMEBUFFER_BINDING);\n    clipManager.getMaskRenderTexture();\n    gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n  }\n  bindTexture(index, texture) {\n    this.coreModel.setTexture(index, texture);\n  }\n  getHitAreaDefs() {\n    var _a;\n    return ((_a = this.settings.hitAreas) == null ? void 0 : _a.map((hitArea) => ({\n      id: hitArea.id,\n      name: hitArea.name,\n      index: this.coreModel.getDrawDataIndex(hitArea.id)\n    }))) || [];\n  }\n  getDrawableIDs() {\n    const modelContext = this.coreModel.getModelContext();\n    const ids = [];\n    for (let i = 0; i < this.drawDataCount; i++) {\n      const drawData = modelContext.getDrawData(i);\n      if (drawData) {\n        ids.push(drawData.getDrawDataID().id);\n      }\n    }\n    return ids;\n  }\n  getDrawableIndex(id) {\n    return this.coreModel.getDrawDataIndex(id);\n  }\n  getDrawableVertices(drawIndex) {\n    if (typeof drawIndex === \"string\") {\n      drawIndex = this.coreModel.getDrawDataIndex(drawIndex);\n      if (drawIndex === -1)\n        throw new TypeError(\"Unable to find drawable ID: \" + drawIndex);\n    }\n    return this.coreModel.getTransformedPoints(drawIndex).slice();\n  }\n  update(dt, now) {\n    var _a, _b, _c, _d;\n    super.update(dt, now);\n    const model = this.coreModel;\n    this.emit(\"beforeMotionUpdate\");\n    const motionUpdated = this.motionManager.update(this.coreModel, now);\n    this.emit(\"afterMotionUpdate\");\n    model.saveParam();\n    (_a = this.motionManager.expressionManager) == null ? void 0 : _a.update(model, now);\n    if (!motionUpdated) {\n      (_b = this.eyeBlink) == null ? void 0 : _b.update(dt);\n    }\n    this.updateFocus();\n    this.updateNaturalMovements(dt, now);\n    (_c = this.physics) == null ? void 0 : _c.update(now);\n    (_d = this.pose) == null ? void 0 : _d.update(dt);\n    this.emit(\"beforeModelUpdate\");\n    model.update();\n    model.loadParam();\n  }\n  updateFocus() {\n    this.coreModel.addToParamFloat(this.eyeballXParamIndex, this.focusController.x);\n    this.coreModel.addToParamFloat(this.eyeballYParamIndex, this.focusController.y);\n    this.coreModel.addToParamFloat(this.angleXParamIndex, this.focusController.x * 30);\n    this.coreModel.addToParamFloat(this.angleYParamIndex, this.focusController.y * 30);\n    this.coreModel.addToParamFloat(this.angleZParamIndex, this.focusController.x * this.focusController.y * -30);\n    this.coreModel.addToParamFloat(this.bodyAngleXParamIndex, this.focusController.x * 10);\n  }\n  updateNaturalMovements(dt, now) {\n    const t = now / 1e3 * 2 * Math.PI;\n    this.coreModel.addToParamFloat(this.angleXParamIndex, 15 * Math.sin(t / 6.5345) * 0.5);\n    this.coreModel.addToParamFloat(this.angleYParamIndex, 8 * Math.sin(t / 3.5345) * 0.5);\n    this.coreModel.addToParamFloat(this.angleZParamIndex, 10 * Math.sin(t / 5.5345) * 0.5);\n    this.coreModel.addToParamFloat(this.bodyAngleXParamIndex, 4 * Math.sin(t / 15.5345) * 0.5);\n    this.coreModel.setParamFloat(this.breathParamIndex, 0.5 + 0.5 * Math.sin(t / 3.2345));\n  }\n  draw(gl) {\n    const disableCulling = this.disableCulling;\n    if (gl.getParameter(gl.FRAMEBUFFER_BINDING)) {\n      this.disableCulling = true;\n    }\n    const matrix = this.drawingMatrix;\n    tempMatrixArray[0] = matrix.a;\n    tempMatrixArray[1] = matrix.b;\n    tempMatrixArray[4] = matrix.c;\n    tempMatrixArray[5] = matrix.d;\n    tempMatrixArray[12] = matrix.tx;\n    tempMatrixArray[13] = matrix.ty;\n    this.coreModel.setMatrix(tempMatrixArray);\n    this.coreModel.draw();\n    this.disableCulling = disableCulling;\n  }\n  destroy() {\n    super.destroy();\n    this.coreModel = void 0;\n  }\n}\nclass Cubism2ModelSettings extends ModelSettings {\n  constructor(json) {\n    super(json);\n    this.motions = {};\n    if (!Cubism2ModelSettings.isValidJSON(json)) {\n      throw new TypeError(\"Invalid JSON.\");\n    }\n    this.moc = json.model;\n    copyArray(\"string\", json, this, \"textures\", \"textures\");\n    this.copy(json);\n  }\n  static isValidJSON(json) {\n    var _a;\n    return !!json && typeof json.model === \"string\" && ((_a = json.textures) == null ? void 0 : _a.length) > 0 && json.textures.every((item) => typeof item === \"string\");\n  }\n  copy(json) {\n    copyProperty(\"string\", json, this, \"name\", \"name\");\n    copyProperty(\"string\", json, this, \"pose\", \"pose\");\n    copyProperty(\"string\", json, this, \"physics\", \"physics\");\n    copyProperty(\"object\", json, this, \"layout\", \"layout\");\n    copyProperty(\"object\", json, this, \"motions\", \"motions\");\n    copyArray(\"object\", json, this, \"hit_areas\", \"hitAreas\");\n    copyArray(\"object\", json, this, \"expressions\", \"expressions\");\n    copyArray(\"object\", json, this, \"init_params\", \"initParams\");\n    copyArray(\"object\", json, this, \"init_opacities\", \"initOpacities\");\n  }\n  replaceFiles(replace) {\n    super.replaceFiles(replace);\n    for (const [group, motions] of Object.entries(this.motions)) {\n      for (let i = 0; i < motions.length; i++) {\n        motions[i].file = replace(motions[i].file, `motions.${group}[${i}].file`);\n        if (motions[i].sound !== void 0) {\n          motions[i].sound = replace(motions[i].sound, `motions.${group}[${i}].sound`);\n        }\n      }\n    }\n    if (this.expressions) {\n      for (let i = 0; i < this.expressions.length; i++) {\n        this.expressions[i].file = replace(this.expressions[i].file, `expressions[${i}].file`);\n      }\n    }\n  }\n}\nconst SRC_TYPE_MAP = {\n  x: PhysicsHair.Src.SRC_TO_X,\n  y: PhysicsHair.Src.SRC_TO_Y,\n  angle: PhysicsHair.Src.SRC_TO_G_ANGLE\n};\nconst TARGET_TYPE_MAP = {\n  x: PhysicsHair.Src.SRC_TO_X,\n  y: PhysicsHair.Src.SRC_TO_Y,\n  angle: PhysicsHair.Src.SRC_TO_G_ANGLE\n};\nclass Live2DPhysics {\n  constructor(coreModel, json) {\n    this.coreModel = coreModel;\n    this.physicsHairs = [];\n    if (json.physics_hair) {\n      this.physicsHairs = json.physics_hair.map((definition) => {\n        const physicsHair = new PhysicsHair();\n        physicsHair.setup(definition.setup.length, definition.setup.regist, definition.setup.mass);\n        definition.src.forEach(({ id, ptype, scale, weight }) => {\n          const type = SRC_TYPE_MAP[ptype];\n          if (type) {\n            physicsHair.addSrcParam(type, id, scale, weight);\n          }\n        });\n        definition.targets.forEach(({ id, ptype, scale, weight }) => {\n          const type = TARGET_TYPE_MAP[ptype];\n          if (type) {\n            physicsHair.addTargetParam(type, id, scale, weight);\n          }\n        });\n        return physicsHair;\n      });\n    }\n  }\n  update(elapsed) {\n    this.physicsHairs.forEach((physicsHair) => physicsHair.update(this.coreModel, elapsed));\n  }\n}\nclass Live2DPartsParam {\n  constructor(id) {\n    this.id = id;\n    this.paramIndex = -1;\n    this.partsIndex = -1;\n    this.link = [];\n  }\n  initIndex(model) {\n    this.paramIndex = model.getParamIndex(\"VISIBLE:\" + this.id);\n    this.partsIndex = model.getPartsDataIndex(PartsDataID.getID(this.id));\n    model.setParamFloat(this.paramIndex, 1);\n  }\n}\nclass Live2DPose {\n  constructor(coreModel, json) {\n    this.coreModel = coreModel;\n    this.opacityAnimDuration = 500;\n    this.partsGroups = [];\n    if (json.parts_visible) {\n      this.partsGroups = json.parts_visible.map(({ group }) => group.map(({ id, link }) => {\n        const parts = new Live2DPartsParam(id);\n        if (link) {\n          parts.link = link.map((l) => new Live2DPartsParam(l));\n        }\n        return parts;\n      }));\n      this.init();\n    }\n  }\n  init() {\n    this.partsGroups.forEach((group) => {\n      group.forEach((parts) => {\n        parts.initIndex(this.coreModel);\n        if (parts.paramIndex >= 0) {\n          const visible = this.coreModel.getParamFloat(parts.paramIndex) !== 0;\n          this.coreModel.setPartsOpacity(parts.partsIndex, visible ? 1 : 0);\n          this.coreModel.setParamFloat(parts.paramIndex, visible ? 1 : 0);\n          if (parts.link.length > 0) {\n            parts.link.forEach((p) => p.initIndex(this.coreModel));\n          }\n        }\n      });\n    });\n  }\n  normalizePartsOpacityGroup(partsGroup, dt) {\n    const model = this.coreModel;\n    const phi = 0.5;\n    const maxBackOpacity = 0.15;\n    let visibleOpacity = 1;\n    let visibleIndex = partsGroup.findIndex(({ paramIndex, partsIndex }) => partsIndex >= 0 && model.getParamFloat(paramIndex) !== 0);\n    if (visibleIndex >= 0) {\n      const originalOpacity = model.getPartsOpacity(partsGroup[visibleIndex].partsIndex);\n      visibleOpacity = clamp(originalOpacity + dt / this.opacityAnimDuration, 0, 1);\n    } else {\n      visibleIndex = 0;\n      visibleOpacity = 1;\n    }\n    partsGroup.forEach(({ partsIndex }, index) => {\n      if (partsIndex >= 0) {\n        if (visibleIndex == index) {\n          model.setPartsOpacity(partsIndex, visibleOpacity);\n        } else {\n          let opacity = model.getPartsOpacity(partsIndex);\n          let a1;\n          if (visibleOpacity < phi) {\n            a1 = visibleOpacity * (phi - 1) / phi + 1;\n          } else {\n            a1 = (1 - visibleOpacity) * phi / (1 - phi);\n          }\n          let backOp = (1 - a1) * (1 - visibleOpacity);\n          if (backOp > maxBackOpacity) {\n            a1 = 1 - maxBackOpacity / (1 - visibleOpacity);\n          }\n          if (opacity > a1) {\n            opacity = a1;\n          }\n          model.setPartsOpacity(partsIndex, opacity);\n        }\n      }\n    });\n  }\n  copyOpacity(partsGroup) {\n    const model = this.coreModel;\n    partsGroup.forEach(({ partsIndex, link }) => {\n      if (partsIndex >= 0 && link) {\n        const opacity = model.getPartsOpacity(partsIndex);\n        link.forEach(({ partsIndex: partsIndex2 }) => {\n          if (partsIndex2 >= 0) {\n            model.setPartsOpacity(partsIndex2, opacity);\n          }\n        });\n      }\n    });\n  }\n  update(dt) {\n    this.partsGroups.forEach((partGroup) => {\n      this.normalizePartsOpacityGroup(partGroup, dt);\n      this.copyOpacity(partGroup);\n    });\n  }\n}\nLive2DFactory.registerRuntime({\n  version: 2,\n  test(source) {\n    return source instanceof Cubism2ModelSettings || Cubism2ModelSettings.isValidJSON(source);\n  },\n  ready() {\n    return Promise.resolve();\n  },\n  isValidMoc(modelData) {\n    if (modelData.byteLength < 3) {\n      return false;\n    }\n    const view = new Int8Array(modelData, 0, 3);\n    return String.fromCharCode(...view) === \"moc\";\n  },\n  createModelSettings(json) {\n    return new Cubism2ModelSettings(json);\n  },\n  createCoreModel(data) {\n    const model = Live2DModelWebGL.loadModel(data);\n    const error = Live2D.getError();\n    if (error)\n      throw error;\n    return model;\n  },\n  createInternalModel(coreModel, settings, options) {\n    return new Cubism2InternalModel(coreModel, settings, options);\n  },\n  createPose(coreModel, data) {\n    return new Live2DPose(coreModel, data);\n  },\n  createPhysics(coreModel, data) {\n    return new Live2DPhysics(coreModel, data);\n  }\n});\nif (!window.Live2DCubismCore) {\n  throw new Error(\"Could not find Cubism 4 runtime. This plugin requires live2dcubismcore.js to be loaded.\");\n}\nclass CubismVector2 {\n  constructor(x, y) {\n    this.x = x || 0;\n    this.y = y || 0;\n  }\n  add(vector2) {\n    const ret = new CubismVector2(0, 0);\n    ret.x = this.x + vector2.x;\n    ret.y = this.y + vector2.y;\n    return ret;\n  }\n  substract(vector2) {\n    const ret = new CubismVector2(0, 0);\n    ret.x = this.x - vector2.x;\n    ret.y = this.y - vector2.y;\n    return ret;\n  }\n  multiply(vector2) {\n    const ret = new CubismVector2(0, 0);\n    ret.x = this.x * vector2.x;\n    ret.y = this.y * vector2.y;\n    return ret;\n  }\n  multiplyByScaler(scalar) {\n    return this.multiply(new CubismVector2(scalar, scalar));\n  }\n  division(vector2) {\n    const ret = new CubismVector2(0, 0);\n    ret.x = this.x / vector2.x;\n    ret.y = this.y / vector2.y;\n    return ret;\n  }\n  divisionByScalar(scalar) {\n    return this.division(new CubismVector2(scalar, scalar));\n  }\n  getLength() {\n    return Math.sqrt(this.x * this.x + this.y * this.y);\n  }\n  getDistanceWith(a) {\n    return Math.sqrt((this.x - a.x) * (this.x - a.x) + (this.y - a.y) * (this.y - a.y));\n  }\n  dot(a) {\n    return this.x * a.x + this.y * a.y;\n  }\n  normalize() {\n    const length = Math.pow(this.x * this.x + this.y * this.y, 0.5);\n    this.x = this.x / length;\n    this.y = this.y / length;\n  }\n  isEqual(rhs) {\n    return this.x == rhs.x && this.y == rhs.y;\n  }\n  isNotEqual(rhs) {\n    return !this.isEqual(rhs);\n  }\n}\nconst _CubismMath = class {\n  static range(value, min, max) {\n    if (value < min) {\n      value = min;\n    } else if (value > max) {\n      value = max;\n    }\n    return value;\n  }\n  static sin(x) {\n    return Math.sin(x);\n  }\n  static cos(x) {\n    return Math.cos(x);\n  }\n  static abs(x) {\n    return Math.abs(x);\n  }\n  static sqrt(x) {\n    return Math.sqrt(x);\n  }\n  static cbrt(x) {\n    if (x === 0) {\n      return x;\n    }\n    let cx = x;\n    const isNegativeNumber = cx < 0;\n    if (isNegativeNumber) {\n      cx = -cx;\n    }\n    let ret;\n    if (cx === Infinity) {\n      ret = Infinity;\n    } else {\n      ret = Math.exp(Math.log(cx) / 3);\n      ret = (cx / (ret * ret) + 2 * ret) / 3;\n    }\n    return isNegativeNumber ? -ret : ret;\n  }\n  static getEasingSine(value) {\n    if (value < 0) {\n      return 0;\n    } else if (value > 1) {\n      return 1;\n    }\n    return 0.5 - 0.5 * this.cos(value * Math.PI);\n  }\n  static max(left, right) {\n    return left > right ? left : right;\n  }\n  static min(left, right) {\n    return left > right ? right : left;\n  }\n  static degreesToRadian(degrees) {\n    return degrees / 180 * Math.PI;\n  }\n  static radianToDegrees(radian) {\n    return radian * 180 / Math.PI;\n  }\n  static directionToRadian(from, to) {\n    const q1 = Math.atan2(to.y, to.x);\n    const q2 = Math.atan2(from.y, from.x);\n    let ret = q1 - q2;\n    while (ret < -Math.PI) {\n      ret += Math.PI * 2;\n    }\n    while (ret > Math.PI) {\n      ret -= Math.PI * 2;\n    }\n    return ret;\n  }\n  static directionToDegrees(from, to) {\n    const radian = this.directionToRadian(from, to);\n    let degree = this.radianToDegrees(radian);\n    if (to.x - from.x > 0) {\n      degree = -degree;\n    }\n    return degree;\n  }\n  static radianToDirection(totalAngle) {\n    const ret = new CubismVector2();\n    ret.x = this.sin(totalAngle);\n    ret.y = this.cos(totalAngle);\n    return ret;\n  }\n  static quadraticEquation(a, b, c) {\n    if (this.abs(a) < _CubismMath.Epsilon) {\n      if (this.abs(b) < _CubismMath.Epsilon) {\n        return -c;\n      }\n      return -c / b;\n    }\n    return -(b + this.sqrt(b * b - 4 * a * c)) / (2 * a);\n  }\n  static cardanoAlgorithmForBezier(a, b, c, d) {\n    if (this.sqrt(a) < _CubismMath.Epsilon) {\n      return this.range(this.quadraticEquation(b, c, d), 0, 1);\n    }\n    const ba = b / a;\n    const ca = c / a;\n    const da = d / a;\n    const p = (3 * ca - ba * ba) / 3;\n    const p3 = p / 3;\n    const q = (2 * ba * ba * ba - 9 * ba * ca + 27 * da) / 27;\n    const q2 = q / 2;\n    const discriminant = q2 * q2 + p3 * p3 * p3;\n    const center = 0.5;\n    const threshold = center + 0.01;\n    if (discriminant < 0) {\n      const mp3 = -p / 3;\n      const mp33 = mp3 * mp3 * mp3;\n      const r = this.sqrt(mp33);\n      const t = -q / (2 * r);\n      const cosphi = this.range(t, -1, 1);\n      const phi = Math.acos(cosphi);\n      const crtr = this.cbrt(r);\n      const t1 = 2 * crtr;\n      const root12 = t1 * this.cos(phi / 3) - ba / 3;\n      if (this.abs(root12 - center) < threshold) {\n        return this.range(root12, 0, 1);\n      }\n      const root2 = t1 * this.cos((phi + 2 * Math.PI) / 3) - ba / 3;\n      if (this.abs(root2 - center) < threshold) {\n        return this.range(root2, 0, 1);\n      }\n      const root3 = t1 * this.cos((phi + 4 * Math.PI) / 3) - ba / 3;\n      return this.range(root3, 0, 1);\n    }\n    if (discriminant == 0) {\n      let u12;\n      if (q2 < 0) {\n        u12 = this.cbrt(-q2);\n      } else {\n        u12 = -this.cbrt(q2);\n      }\n      const root12 = 2 * u12 - ba / 3;\n      if (this.abs(root12 - center) < threshold) {\n        return this.range(root12, 0, 1);\n      }\n      const root2 = -u12 - ba / 3;\n      return this.range(root2, 0, 1);\n    }\n    const sd = this.sqrt(discriminant);\n    const u1 = this.cbrt(sd - q2);\n    const v1 = this.cbrt(sd + q2);\n    const root1 = u1 - v1 - ba / 3;\n    return this.range(root1, 0, 1);\n  }\n  constructor() {\n  }\n};\nlet CubismMath = _CubismMath;\nCubismMath.Epsilon = 1e-5;\nclass CubismMatrix44 {\n  constructor() {\n    this._tr = new Float32Array(16);\n    this.loadIdentity();\n  }\n  static multiply(a, b, dst) {\n    const c = new Float32Array([\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0\n    ]);\n    const n = 4;\n    for (let i = 0; i < n; ++i) {\n      for (let j = 0; j < n; ++j) {\n        for (let k = 0; k < n; ++k) {\n          c[j + i * 4] += a[k + i * 4] * b[j + k * 4];\n        }\n      }\n    }\n    for (let i = 0; i < 16; ++i) {\n      dst[i] = c[i];\n    }\n  }\n  loadIdentity() {\n    const c = new Float32Array([\n      1,\n      0,\n      0,\n      0,\n      0,\n      1,\n      0,\n      0,\n      0,\n      0,\n      1,\n      0,\n      0,\n      0,\n      0,\n      1\n    ]);\n    this.setMatrix(c);\n  }\n  setMatrix(tr) {\n    for (let i = 0; i < 16; ++i) {\n      this._tr[i] = tr[i];\n    }\n  }\n  getArray() {\n    return this._tr;\n  }\n  getScaleX() {\n    return this._tr[0];\n  }\n  getScaleY() {\n    return this._tr[5];\n  }\n  getTranslateX() {\n    return this._tr[12];\n  }\n  getTranslateY() {\n    return this._tr[13];\n  }\n  transformX(src) {\n    return this._tr[0] * src + this._tr[12];\n  }\n  transformY(src) {\n    return this._tr[5] * src + this._tr[13];\n  }\n  invertTransformX(src) {\n    return (src - this._tr[12]) / this._tr[0];\n  }\n  invertTransformY(src) {\n    return (src - this._tr[13]) / this._tr[5];\n  }\n  translateRelative(x, y) {\n    const tr1 = new Float32Array([\n      1,\n      0,\n      0,\n      0,\n      0,\n      1,\n      0,\n      0,\n      0,\n      0,\n      1,\n      0,\n      x,\n      y,\n      0,\n      1\n    ]);\n    CubismMatrix44.multiply(tr1, this._tr, this._tr);\n  }\n  translate(x, y) {\n    this._tr[12] = x;\n    this._tr[13] = y;\n  }\n  translateX(x) {\n    this._tr[12] = x;\n  }\n  translateY(y) {\n    this._tr[13] = y;\n  }\n  scaleRelative(x, y) {\n    const tr1 = new Float32Array([\n      x,\n      0,\n      0,\n      0,\n      0,\n      y,\n      0,\n      0,\n      0,\n      0,\n      1,\n      0,\n      0,\n      0,\n      0,\n      1\n    ]);\n    CubismMatrix44.multiply(tr1, this._tr, this._tr);\n  }\n  scale(x, y) {\n    this._tr[0] = x;\n    this._tr[5] = y;\n  }\n  multiplyByMatrix(m) {\n    CubismMatrix44.multiply(m.getArray(), this._tr, this._tr);\n  }\n  clone() {\n    const cloneMatrix = new CubismMatrix44();\n    for (let i = 0; i < this._tr.length; i++) {\n      cloneMatrix._tr[i] = this._tr[i];\n    }\n    return cloneMatrix;\n  }\n}\nclass CubismRenderer {\n  initialize(model) {\n    this._model = model;\n  }\n  drawModel() {\n    if (this.getModel() == null)\n      return;\n    this.doDrawModel();\n  }\n  setMvpMatrix(matrix44) {\n    this._mvpMatrix4x4.setMatrix(matrix44.getArray());\n  }\n  getMvpMatrix() {\n    return this._mvpMatrix4x4;\n  }\n  setModelColor(red, green, blue, alpha) {\n    if (red < 0) {\n      red = 0;\n    } else if (red > 1) {\n      red = 1;\n    }\n    if (green < 0) {\n      green = 0;\n    } else if (green > 1) {\n      green = 1;\n    }\n    if (blue < 0) {\n      blue = 0;\n    } else if (blue > 1) {\n      blue = 1;\n    }\n    if (alpha < 0) {\n      alpha = 0;\n    } else if (alpha > 1) {\n      alpha = 1;\n    }\n    this._modelColor.R = red;\n    this._modelColor.G = green;\n    this._modelColor.B = blue;\n    this._modelColor.A = alpha;\n  }\n  getModelColor() {\n    return Object.assign({}, this._modelColor);\n  }\n  setIsPremultipliedAlpha(enable) {\n    this._isPremultipliedAlpha = enable;\n  }\n  isPremultipliedAlpha() {\n    return this._isPremultipliedAlpha;\n  }\n  setIsCulling(culling) {\n    this._isCulling = culling;\n  }\n  isCulling() {\n    return this._isCulling;\n  }\n  setAnisotropy(n) {\n    this._anisortopy = n;\n  }\n  getAnisotropy() {\n    return this._anisortopy;\n  }\n  getModel() {\n    return this._model;\n  }\n  constructor() {\n    this._isCulling = false;\n    this._isPremultipliedAlpha = false;\n    this._anisortopy = 0;\n    this._modelColor = new CubismTextureColor();\n    this._mvpMatrix4x4 = new CubismMatrix44();\n    this._mvpMatrix4x4.loadIdentity();\n  }\n}\nvar CubismBlendMode = /* @__PURE__ */ ((CubismBlendMode2) => {\n  CubismBlendMode2[CubismBlendMode2[\"CubismBlendMode_Normal\"] = 0] = \"CubismBlendMode_Normal\";\n  CubismBlendMode2[CubismBlendMode2[\"CubismBlendMode_Additive\"] = 1] = \"CubismBlendMode_Additive\";\n  CubismBlendMode2[CubismBlendMode2[\"CubismBlendMode_Multiplicative\"] = 2] = \"CubismBlendMode_Multiplicative\";\n  return CubismBlendMode2;\n})(CubismBlendMode || {});\nclass CubismTextureColor {\n  constructor() {\n    this.R = 1;\n    this.G = 1;\n    this.B = 1;\n    this.A = 1;\n  }\n}\nlet s_isStarted = false;\nlet s_isInitialized = false;\nlet s_option = void 0;\nconst Constant = {\n  vertexOffset: 0,\n  vertexStep: 2\n};\nclass CubismFramework {\n  static startUp(option) {\n    if (s_isStarted) {\n      CubismLogInfo(\"CubismFramework.startUp() is already done.\");\n      return s_isStarted;\n    }\n    if (Live2DCubismCore._isStarted) {\n      s_isStarted = true;\n      return true;\n    }\n    Live2DCubismCore._isStarted = true;\n    s_option = option;\n    if (s_option) {\n      Live2DCubismCore.Logging.csmSetLogFunction(s_option.logFunction);\n    }\n    s_isStarted = true;\n    if (s_isStarted) {\n      const version = Live2DCubismCore.Version.csmGetVersion();\n      const major = (version & 4278190080) >> 24;\n      const minor = (version & 16711680) >> 16;\n      const patch = version & 65535;\n      const versionNumber = version;\n      CubismLogInfo(`Live2D Cubism Core version: {0}.{1}.{2} ({3})`, (\"00\" + major).slice(-2), (\"00\" + minor).slice(-2), (\"0000\" + patch).slice(-4), versionNumber);\n    }\n    CubismLogInfo(\"CubismFramework.startUp() is complete.\");\n    return s_isStarted;\n  }\n  static cleanUp() {\n    s_isStarted = false;\n    s_isInitialized = false;\n    s_option = void 0;\n  }\n  static initialize() {\n    if (!s_isStarted) {\n      CubismLogWarning(\"CubismFramework is not started.\");\n      return;\n    }\n    if (s_isInitialized) {\n      CubismLogWarning(\"CubismFramework.initialize() skipped, already initialized.\");\n      return;\n    }\n    s_isInitialized = true;\n    CubismLogInfo(\"CubismFramework.initialize() is complete.\");\n  }\n  static dispose() {\n    if (!s_isStarted) {\n      CubismLogWarning(\"CubismFramework is not started.\");\n      return;\n    }\n    if (!s_isInitialized) {\n      CubismLogWarning(\"CubismFramework.dispose() skipped, not initialized.\");\n      return;\n    }\n    CubismRenderer.staticRelease();\n    s_isInitialized = false;\n    CubismLogInfo(\"CubismFramework.dispose() is complete.\");\n  }\n  static isStarted() {\n    return s_isStarted;\n  }\n  static isInitialized() {\n    return s_isInitialized;\n  }\n  static coreLogFunction(message) {\n    if (!Live2DCubismCore.Logging.csmGetLogFunction()) {\n      return;\n    }\n    Live2DCubismCore.Logging.csmGetLogFunction()(message);\n  }\n  static getLoggingLevel() {\n    if (s_option != null) {\n      return s_option.loggingLevel;\n    }\n    return LogLevel.LogLevel_Off;\n  }\n  constructor() {\n  }\n}\nvar LogLevel = /* @__PURE__ */ ((LogLevel2) => {\n  LogLevel2[LogLevel2[\"LogLevel_Verbose\"] = 0] = \"LogLevel_Verbose\";\n  LogLevel2[LogLevel2[\"LogLevel_Debug\"] = 1] = \"LogLevel_Debug\";\n  LogLevel2[LogLevel2[\"LogLevel_Info\"] = 2] = \"LogLevel_Info\";\n  LogLevel2[LogLevel2[\"LogLevel_Warning\"] = 3] = \"LogLevel_Warning\";\n  LogLevel2[LogLevel2[\"LogLevel_Error\"] = 4] = \"LogLevel_Error\";\n  LogLevel2[LogLevel2[\"LogLevel_Off\"] = 5] = \"LogLevel_Off\";\n  return LogLevel2;\n})(LogLevel || {});\nconst CSM_ASSERT = () => {\n};\nfunction CubismLogDebug(fmt, ...args) {\n  CubismDebug.print(LogLevel.LogLevel_Debug, \"[CSM][D]\" + fmt + \"\\n\", args);\n}\nfunction CubismLogInfo(fmt, ...args) {\n  CubismDebug.print(LogLevel.LogLevel_Info, \"[CSM][I]\" + fmt + \"\\n\", args);\n}\nfunction CubismLogWarning(fmt, ...args) {\n  CubismDebug.print(LogLevel.LogLevel_Warning, \"[CSM][W]\" + fmt + \"\\n\", args);\n}\nfunction CubismLogError(fmt, ...args) {\n  CubismDebug.print(LogLevel.LogLevel_Error, \"[CSM][E]\" + fmt + \"\\n\", args);\n}\nclass CubismDebug {\n  static print(logLevel, format, args) {\n    if (logLevel < CubismFramework.getLoggingLevel()) {\n      return;\n    }\n    const logPrint = CubismFramework.coreLogFunction;\n    if (!logPrint)\n      return;\n    const buffer = format.replace(/{(\\d+)}/g, (m, k) => {\n      return args[k];\n    });\n    logPrint(buffer);\n  }\n  static dumpBytes(logLevel, data, length) {\n    for (let i = 0; i < length; i++) {\n      if (i % 16 == 0 && i > 0)\n        this.print(logLevel, \"\\n\");\n      else if (i % 8 == 0 && i > 0)\n        this.print(logLevel, \"  \");\n      this.print(logLevel, \"{0} \", [data[i] & 255]);\n    }\n    this.print(logLevel, \"\\n\");\n  }\n  constructor() {\n  }\n}\nclass ACubismMotion {\n  constructor() {\n    this._fadeInSeconds = -1;\n    this._fadeOutSeconds = -1;\n    this._weight = 1;\n    this._offsetSeconds = 0;\n    this._firedEventValues = [];\n  }\n  release() {\n    this._weight = 0;\n  }\n  updateParameters(model, motionQueueEntry, userTimeSeconds) {\n    if (!motionQueueEntry.isAvailable() || motionQueueEntry.isFinished()) {\n      return;\n    }\n    if (!motionQueueEntry.isStarted()) {\n      motionQueueEntry.setIsStarted(true);\n      motionQueueEntry.setStartTime(userTimeSeconds - this._offsetSeconds);\n      motionQueueEntry.setFadeInStartTime(userTimeSeconds);\n      const duration = this.getDuration();\n      if (motionQueueEntry.getEndTime() < 0) {\n        motionQueueEntry.setEndTime(duration <= 0 ? -1 : motionQueueEntry.getStartTime() + duration);\n      }\n    }\n    let fadeWeight = this._weight;\n    const fadeIn = this._fadeInSeconds == 0 ? 1 : CubismMath.getEasingSine((userTimeSeconds - motionQueueEntry.getFadeInStartTime()) / this._fadeInSeconds);\n    const fadeOut = this._fadeOutSeconds == 0 || motionQueueEntry.getEndTime() < 0 ? 1 : CubismMath.getEasingSine((motionQueueEntry.getEndTime() - userTimeSeconds) / this._fadeOutSeconds);\n    fadeWeight = fadeWeight * fadeIn * fadeOut;\n    motionQueueEntry.setState(userTimeSeconds, fadeWeight);\n    this.doUpdateParameters(model, userTimeSeconds, fadeWeight, motionQueueEntry);\n    if (motionQueueEntry.getEndTime() > 0 && motionQueueEntry.getEndTime() < userTimeSeconds) {\n      motionQueueEntry.setIsFinished(true);\n    }\n  }\n  setFadeInTime(fadeInSeconds) {\n    this._fadeInSeconds = fadeInSeconds;\n  }\n  setFadeOutTime(fadeOutSeconds) {\n    this._fadeOutSeconds = fadeOutSeconds;\n  }\n  getFadeOutTime() {\n    return this._fadeOutSeconds;\n  }\n  getFadeInTime() {\n    return this._fadeInSeconds;\n  }\n  setWeight(weight) {\n    this._weight = weight;\n  }\n  getWeight() {\n    return this._weight;\n  }\n  getDuration() {\n    return -1;\n  }\n  getLoopDuration() {\n    return -1;\n  }\n  setOffsetTime(offsetSeconds) {\n    this._offsetSeconds = offsetSeconds;\n  }\n  getFiredEvent(beforeCheckTimeSeconds, motionTimeSeconds) {\n    return this._firedEventValues;\n  }\n  setFinishedMotionHandler(onFinishedMotionHandler) {\n    this._onFinishedMotion = onFinishedMotionHandler;\n  }\n  getFinishedMotionHandler() {\n    return this._onFinishedMotion;\n  }\n}\nconst DefaultFadeTime = 1;\nclass CubismExpressionMotion extends ACubismMotion {\n  constructor() {\n    super();\n    this._parameters = [];\n  }\n  static create(json) {\n    const expression = new CubismExpressionMotion();\n    const fadeInTime = json.FadeInTime;\n    const fadeOutTime = json.FadeOutTime;\n    expression.setFadeInTime(fadeInTime !== void 0 ? fadeInTime : DefaultFadeTime);\n    expression.setFadeOutTime(fadeOutTime !== void 0 ? fadeOutTime : DefaultFadeTime);\n    const parameters = json.Parameters || [];\n    for (let i = 0; i < parameters.length; ++i) {\n      const param = parameters[i];\n      const parameterId = param.Id;\n      const value = param.Value;\n      let blendType;\n      switch (param.Blend) {\n        case \"Multiply\":\n          blendType = ExpressionBlendType.ExpressionBlendType_Multiply;\n          break;\n        case \"Overwrite\":\n          blendType = ExpressionBlendType.ExpressionBlendType_Overwrite;\n          break;\n        case \"Add\":\n        default:\n          blendType = ExpressionBlendType.ExpressionBlendType_Add;\n          break;\n      }\n      const item = {\n        parameterId,\n        blendType,\n        value\n      };\n      expression._parameters.push(item);\n    }\n    return expression;\n  }\n  doUpdateParameters(model, userTimeSeconds, weight, motionQueueEntry) {\n    for (let i = 0; i < this._parameters.length; ++i) {\n      const parameter = this._parameters[i];\n      switch (parameter.blendType) {\n        case ExpressionBlendType.ExpressionBlendType_Add: {\n          model.addParameterValueById(parameter.parameterId, parameter.value, weight);\n          break;\n        }\n        case ExpressionBlendType.ExpressionBlendType_Multiply: {\n          model.multiplyParameterValueById(parameter.parameterId, parameter.value, weight);\n          break;\n        }\n        case ExpressionBlendType.ExpressionBlendType_Overwrite: {\n          model.setParameterValueById(parameter.parameterId, parameter.value, weight);\n          break;\n        }\n      }\n    }\n  }\n}\nvar ExpressionBlendType = /* @__PURE__ */ ((ExpressionBlendType2) => {\n  ExpressionBlendType2[ExpressionBlendType2[\"ExpressionBlendType_Add\"] = 0] = \"ExpressionBlendType_Add\";\n  ExpressionBlendType2[ExpressionBlendType2[\"ExpressionBlendType_Multiply\"] = 1] = \"ExpressionBlendType_Multiply\";\n  ExpressionBlendType2[ExpressionBlendType2[\"ExpressionBlendType_Overwrite\"] = 2] = \"ExpressionBlendType_Overwrite\";\n  return ExpressionBlendType2;\n})(ExpressionBlendType || {});\nclass CubismMotionQueueEntry {\n  constructor() {\n    this._autoDelete = false;\n    this._available = true;\n    this._finished = false;\n    this._started = false;\n    this._startTimeSeconds = -1;\n    this._fadeInStartTimeSeconds = 0;\n    this._endTimeSeconds = -1;\n    this._stateTimeSeconds = 0;\n    this._stateWeight = 0;\n    this._lastEventCheckSeconds = 0;\n    this._motionQueueEntryHandle = this;\n    this._fadeOutSeconds = 0;\n    this._isTriggeredFadeOut = false;\n  }\n  release() {\n    if (this._autoDelete && this._motion) {\n      this._motion.release();\n    }\n  }\n  setFadeOut(fadeOutSeconds) {\n    this._fadeOutSeconds = fadeOutSeconds;\n    this._isTriggeredFadeOut = true;\n  }\n  startFadeOut(fadeOutSeconds, userTimeSeconds) {\n    const newEndTimeSeconds = userTimeSeconds + fadeOutSeconds;\n    this._isTriggeredFadeOut = true;\n    if (this._endTimeSeconds < 0 || newEndTimeSeconds < this._endTimeSeconds) {\n      this._endTimeSeconds = newEndTimeSeconds;\n    }\n  }\n  isFinished() {\n    return this._finished;\n  }\n  isStarted() {\n    return this._started;\n  }\n  getStartTime() {\n    return this._startTimeSeconds;\n  }\n  getFadeInStartTime() {\n    return this._fadeInStartTimeSeconds;\n  }\n  getEndTime() {\n    return this._endTimeSeconds;\n  }\n  setStartTime(startTime) {\n    this._startTimeSeconds = startTime;\n  }\n  setFadeInStartTime(startTime) {\n    this._fadeInStartTimeSeconds = startTime;\n  }\n  setEndTime(endTime) {\n    this._endTimeSeconds = endTime;\n  }\n  setIsFinished(f) {\n    this._finished = f;\n  }\n  setIsStarted(f) {\n    this._started = f;\n  }\n  isAvailable() {\n    return this._available;\n  }\n  setIsAvailable(v) {\n    this._available = v;\n  }\n  setState(timeSeconds, weight) {\n    this._stateTimeSeconds = timeSeconds;\n    this._stateWeight = weight;\n  }\n  getStateTime() {\n    return this._stateTimeSeconds;\n  }\n  getStateWeight() {\n    return this._stateWeight;\n  }\n  getLastCheckEventSeconds() {\n    return this._lastEventCheckSeconds;\n  }\n  setLastCheckEventSeconds(checkSeconds) {\n    this._lastEventCheckSeconds = checkSeconds;\n  }\n  isTriggeredFadeOut() {\n    return this._isTriggeredFadeOut;\n  }\n  getFadeOutSeconds() {\n    return this._fadeOutSeconds;\n  }\n}\nclass CubismMotionQueueManager {\n  constructor() {\n    this._userTimeSeconds = 0;\n    this._eventCustomData = null;\n    this._motions = [];\n  }\n  release() {\n    for (let i = 0; i < this._motions.length; ++i) {\n      if (this._motions[i]) {\n        this._motions[i].release();\n      }\n    }\n    this._motions = void 0;\n  }\n  startMotion(motion, autoDelete, userTimeSeconds) {\n    if (motion == null) {\n      return InvalidMotionQueueEntryHandleValue;\n    }\n    let motionQueueEntry;\n    for (let i = 0; i < this._motions.length; ++i) {\n      motionQueueEntry = this._motions[i];\n      if (motionQueueEntry == null) {\n        continue;\n      }\n      motionQueueEntry.setFadeOut(motionQueueEntry._motion.getFadeOutTime());\n    }\n    motionQueueEntry = new CubismMotionQueueEntry();\n    motionQueueEntry._autoDelete = autoDelete;\n    motionQueueEntry._motion = motion;\n    this._motions.push(motionQueueEntry);\n    return motionQueueEntry._motionQueueEntryHandle;\n  }\n  isFinished() {\n    let i = 0;\n    while (i < this._motions.length) {\n      const motionQueueEntry = this._motions[i];\n      if (motionQueueEntry == null) {\n        this._motions.splice(i, 1);\n        continue;\n      }\n      const motion = motionQueueEntry._motion;\n      if (motion == null) {\n        motionQueueEntry.release();\n        this._motions.splice(i, 1);\n        continue;\n      }\n      if (!motionQueueEntry.isFinished()) {\n        return false;\n      }\n      i++;\n    }\n    return true;\n  }\n  isFinishedByHandle(motionQueueEntryNumber) {\n    for (let i = 0; i < this._motions.length; i++) {\n      const motionQueueEntry = this._motions[i];\n      if (motionQueueEntry == null) {\n        continue;\n      }\n      if (motionQueueEntry._motionQueueEntryHandle == motionQueueEntryNumber && !motionQueueEntry.isFinished()) {\n        return false;\n      }\n    }\n    return true;\n  }\n  stopAllMotions() {\n    for (let i = 0; i < this._motions.length; i++) {\n      const motionQueueEntry = this._motions[i];\n      if (motionQueueEntry != null) {\n        motionQueueEntry.release();\n      }\n    }\n    this._motions = [];\n  }\n  getCubismMotionQueueEntry(motionQueueEntryNumber) {\n    return this._motions.find((entry) => entry != null && entry._motionQueueEntryHandle == motionQueueEntryNumber);\n  }\n  setEventCallback(callback, customData = null) {\n    this._eventCallBack = callback;\n    this._eventCustomData = customData;\n  }\n  doUpdateMotion(model, userTimeSeconds) {\n    let updated = false;\n    let i = 0;\n    while (i < this._motions.length) {\n      const motionQueueEntry = this._motions[i];\n      if (motionQueueEntry == null) {\n        this._motions.splice(i, 1);\n        continue;\n      }\n      const motion = motionQueueEntry._motion;\n      if (motion == null) {\n        motionQueueEntry.release();\n        this._motions.splice(i, 1);\n        continue;\n      }\n      motion.updateParameters(model, motionQueueEntry, userTimeSeconds);\n      updated = true;\n      const firedList = motion.getFiredEvent(motionQueueEntry.getLastCheckEventSeconds() - motionQueueEntry.getStartTime(), userTimeSeconds - motionQueueEntry.getStartTime());\n      for (let i2 = 0; i2 < firedList.length; ++i2) {\n        this._eventCallBack(this, firedList[i2], this._eventCustomData);\n      }\n      motionQueueEntry.setLastCheckEventSeconds(userTimeSeconds);\n      if (motionQueueEntry.isFinished()) {\n        motionQueueEntry.release();\n        this._motions.splice(i, 1);\n      } else {\n        if (motionQueueEntry.isTriggeredFadeOut()) {\n          motionQueueEntry.startFadeOut(motionQueueEntry.getFadeOutSeconds(), userTimeSeconds);\n        }\n        i++;\n      }\n    }\n    return updated;\n  }\n}\nconst InvalidMotionQueueEntryHandleValue = -1;\nclass Cubism4ExpressionManager extends ExpressionManager {\n  constructor(settings, options) {\n    var _a;\n    super(settings, options);\n    this.queueManager = new CubismMotionQueueManager();\n    this.definitions = (_a = settings.expressions) != null ? _a : [];\n    this.init();\n  }\n  isFinished() {\n    return this.queueManager.isFinished();\n  }\n  getExpressionIndex(name) {\n    return this.definitions.findIndex((def) => def.Name === name);\n  }\n  getExpressionFile(definition) {\n    return definition.File;\n  }\n  createExpression(data, definition) {\n    return CubismExpressionMotion.create(data);\n  }\n  _setExpression(motion) {\n    return this.queueManager.startMotion(motion, false, performance.now());\n  }\n  stopAllExpressions() {\n    this.queueManager.stopAllMotions();\n  }\n  updateParameters(model, now) {\n    return this.queueManager.doUpdateMotion(model, now);\n  }\n}\nclass CubismModelSettingsJson {\n  constructor(json) {\n    this.groups = json.Groups;\n    this.hitAreas = json.HitAreas;\n    this.layout = json.Layout;\n    this.moc = json.FileReferences.Moc;\n    this.expressions = json.FileReferences.Expressions;\n    this.motions = json.FileReferences.Motions;\n    this.textures = json.FileReferences.Textures;\n    this.physics = json.FileReferences.Physics;\n    this.pose = json.FileReferences.Pose;\n  }\n  getEyeBlinkParameters() {\n    var _a, _b;\n    return (_b = (_a = this.groups) == null ? void 0 : _a.find((group) => group.Name === \"EyeBlink\")) == null ? void 0 : _b.Ids;\n  }\n  getLipSyncParameters() {\n    var _a, _b;\n    return (_b = (_a = this.groups) == null ? void 0 : _a.find((group) => group.Name === \"LipSync\")) == null ? void 0 : _b.Ids;\n  }\n}\nclass Cubism4ModelSettings extends ModelSettings {\n  constructor(json) {\n    super(json);\n    if (!Cubism4ModelSettings.isValidJSON(json)) {\n      throw new TypeError(\"Invalid JSON.\");\n    }\n    Object.assign(this, new CubismModelSettingsJson(json));\n  }\n  static isValidJSON(json) {\n    var _a;\n    return !!(json == null ? void 0 : json.FileReferences) && typeof json.FileReferences.Moc === \"string\" && ((_a = json.FileReferences.Textures) == null ? void 0 : _a.length) > 0 && json.FileReferences.Textures.every((item) => typeof item === \"string\");\n  }\n  replaceFiles(replace) {\n    super.replaceFiles(replace);\n    if (this.motions) {\n      for (const [group, motions] of Object.entries(this.motions)) {\n        for (let i = 0; i < motions.length; i++) {\n          motions[i].File = replace(motions[i].File, `motions.${group}[${i}].File`);\n          if (motions[i].Sound !== void 0) {\n            motions[i].Sound = replace(motions[i].Sound, `motions.${group}[${i}].Sound`);\n          }\n        }\n      }\n    }\n    if (this.expressions) {\n      for (let i = 0; i < this.expressions.length; i++) {\n        this.expressions[i].File = replace(this.expressions[i].File, `expressions[${i}].File`);\n      }\n    }\n  }\n}\napplyMixins(Cubism4ModelSettings, [CubismModelSettingsJson]);\nvar CubismMotionCurveTarget = /* @__PURE__ */ ((CubismMotionCurveTarget2) => {\n  CubismMotionCurveTarget2[CubismMotionCurveTarget2[\"CubismMotionCurveTarget_Model\"] = 0] = \"CubismMotionCurveTarget_Model\";\n  CubismMotionCurveTarget2[CubismMotionCurveTarget2[\"CubismMotionCurveTarget_Parameter\"] = 1] = \"CubismMotionCurveTarget_Parameter\";\n  CubismMotionCurveTarget2[CubismMotionCurveTarget2[\"CubismMotionCurveTarget_PartOpacity\"] = 2] = \"CubismMotionCurveTarget_PartOpacity\";\n  return CubismMotionCurveTarget2;\n})(CubismMotionCurveTarget || {});\nvar CubismMotionSegmentType = /* @__PURE__ */ ((CubismMotionSegmentType2) => {\n  CubismMotionSegmentType2[CubismMotionSegmentType2[\"CubismMotionSegmentType_Linear\"] = 0] = \"CubismMotionSegmentType_Linear\";\n  CubismMotionSegmentType2[CubismMotionSegmentType2[\"CubismMotionSegmentType_Bezier\"] = 1] = \"CubismMotionSegmentType_Bezier\";\n  CubismMotionSegmentType2[CubismMotionSegmentType2[\"CubismMotionSegmentType_Stepped\"] = 2] = \"CubismMotionSegmentType_Stepped\";\n  CubismMotionSegmentType2[CubismMotionSegmentType2[\"CubismMotionSegmentType_InverseStepped\"] = 3] = \"CubismMotionSegmentType_InverseStepped\";\n  return CubismMotionSegmentType2;\n})(CubismMotionSegmentType || {});\nclass CubismMotionPoint {\n  constructor(time = 0, value = 0) {\n    this.time = time;\n    this.value = value;\n  }\n}\nclass CubismMotionSegment {\n  constructor() {\n    this.basePointIndex = 0;\n    this.segmentType = 0;\n  }\n}\nclass CubismMotionCurve {\n  constructor() {\n    this.id = \"\";\n    this.type = 0;\n    this.segmentCount = 0;\n    this.baseSegmentIndex = 0;\n    this.fadeInTime = 0;\n    this.fadeOutTime = 0;\n  }\n}\nclass CubismMotionEvent {\n  constructor() {\n    this.fireTime = 0;\n    this.value = \"\";\n  }\n}\nclass CubismMotionData {\n  constructor() {\n    this.duration = 0;\n    this.loop = false;\n    this.curveCount = 0;\n    this.eventCount = 0;\n    this.fps = 0;\n    this.curves = [];\n    this.segments = [];\n    this.points = [];\n    this.events = [];\n  }\n}\nclass CubismMotionJson {\n  constructor(json) {\n    this._json = json;\n  }\n  release() {\n    this._json = void 0;\n  }\n  getMotionDuration() {\n    return this._json.Meta.Duration;\n  }\n  isMotionLoop() {\n    return this._json.Meta.Loop || false;\n  }\n  getEvaluationOptionFlag(flagType) {\n    if (EvaluationOptionFlag.EvaluationOptionFlag_AreBeziersRistricted == flagType) {\n      return !!this._json.Meta.AreBeziersRestricted;\n    }\n    return false;\n  }\n  getMotionCurveCount() {\n    return this._json.Meta.CurveCount;\n  }\n  getMotionFps() {\n    return this._json.Meta.Fps;\n  }\n  getMotionTotalSegmentCount() {\n    return this._json.Meta.TotalSegmentCount;\n  }\n  getMotionTotalPointCount() {\n    return this._json.Meta.TotalPointCount;\n  }\n  getMotionFadeInTime() {\n    return this._json.Meta.FadeInTime;\n  }\n  getMotionFadeOutTime() {\n    return this._json.Meta.FadeOutTime;\n  }\n  getMotionCurveTarget(curveIndex) {\n    return this._json.Curves[curveIndex].Target;\n  }\n  getMotionCurveId(curveIndex) {\n    return this._json.Curves[curveIndex].Id;\n  }\n  getMotionCurveFadeInTime(curveIndex) {\n    return this._json.Curves[curveIndex].FadeInTime;\n  }\n  getMotionCurveFadeOutTime(curveIndex) {\n    return this._json.Curves[curveIndex].FadeOutTime;\n  }\n  getMotionCurveSegmentCount(curveIndex) {\n    return this._json.Curves[curveIndex].Segments.length;\n  }\n  getMotionCurveSegment(curveIndex, segmentIndex) {\n    return this._json.Curves[curveIndex].Segments[segmentIndex];\n  }\n  getEventCount() {\n    return this._json.Meta.UserDataCount || 0;\n  }\n  getTotalEventValueSize() {\n    return this._json.Meta.TotalUserDataSize;\n  }\n  getEventTime(userDataIndex) {\n    return this._json.UserData[userDataIndex].Time;\n  }\n  getEventValue(userDataIndex) {\n    return this._json.UserData[userDataIndex].Value;\n  }\n}\nvar EvaluationOptionFlag = /* @__PURE__ */ ((EvaluationOptionFlag2) => {\n  EvaluationOptionFlag2[EvaluationOptionFlag2[\"EvaluationOptionFlag_AreBeziersRistricted\"] = 0] = \"EvaluationOptionFlag_AreBeziersRistricted\";\n  return EvaluationOptionFlag2;\n})(EvaluationOptionFlag || {});\nconst EffectNameEyeBlink = \"EyeBlink\";\nconst EffectNameLipSync = \"LipSync\";\nconst TargetNameModel = \"Model\";\nconst TargetNameParameter = \"Parameter\";\nconst TargetNamePartOpacity = \"PartOpacity\";\nconst UseOldBeziersCurveMotion = false;\nfunction lerpPoints(a, b, t) {\n  const result = new CubismMotionPoint();\n  result.time = a.time + (b.time - a.time) * t;\n  result.value = a.value + (b.value - a.value) * t;\n  return result;\n}\nfunction linearEvaluate(points, time) {\n  let t = (time - points[0].time) / (points[1].time - points[0].time);\n  if (t < 0) {\n    t = 0;\n  }\n  return points[0].value + (points[1].value - points[0].value) * t;\n}\nfunction bezierEvaluate(points, time) {\n  let t = (time - points[0].time) / (points[3].time - points[0].time);\n  if (t < 0) {\n    t = 0;\n  }\n  const p01 = lerpPoints(points[0], points[1], t);\n  const p12 = lerpPoints(points[1], points[2], t);\n  const p23 = lerpPoints(points[2], points[3], t);\n  const p012 = lerpPoints(p01, p12, t);\n  const p123 = lerpPoints(p12, p23, t);\n  return lerpPoints(p012, p123, t).value;\n}\nfunction bezierEvaluateCardanoInterpretation(points, time) {\n  const x = time;\n  const x1 = points[0].time;\n  const x2 = points[3].time;\n  const cx1 = points[1].time;\n  const cx2 = points[2].time;\n  const a = x2 - 3 * cx2 + 3 * cx1 - x1;\n  const b = 3 * cx2 - 6 * cx1 + 3 * x1;\n  const c = 3 * cx1 - 3 * x1;\n  const d = x1 - x;\n  const t = CubismMath.cardanoAlgorithmForBezier(a, b, c, d);\n  const p01 = lerpPoints(points[0], points[1], t);\n  const p12 = lerpPoints(points[1], points[2], t);\n  const p23 = lerpPoints(points[2], points[3], t);\n  const p012 = lerpPoints(p01, p12, t);\n  const p123 = lerpPoints(p12, p23, t);\n  return lerpPoints(p012, p123, t).value;\n}\nfunction steppedEvaluate(points, time) {\n  return points[0].value;\n}\nfunction inverseSteppedEvaluate(points, time) {\n  return points[1].value;\n}\nfunction evaluateCurve(motionData, index, time) {\n  const curve = motionData.curves[index];\n  let target = -1;\n  const totalSegmentCount = curve.baseSegmentIndex + curve.segmentCount;\n  let pointPosition = 0;\n  for (let i = curve.baseSegmentIndex; i < totalSegmentCount; ++i) {\n    pointPosition = motionData.segments[i].basePointIndex + (motionData.segments[i].segmentType == CubismMotionSegmentType.CubismMotionSegmentType_Bezier ? 3 : 1);\n    if (motionData.points[pointPosition].time > time) {\n      target = i;\n      break;\n    }\n  }\n  if (target == -1) {\n    return motionData.points[pointPosition].value;\n  }\n  const segment = motionData.segments[target];\n  return segment.evaluate(motionData.points.slice(segment.basePointIndex), time);\n}\nclass CubismMotion extends ACubismMotion {\n  constructor() {\n    super();\n    this._eyeBlinkParameterIds = [];\n    this._lipSyncParameterIds = [];\n    this._sourceFrameRate = 30;\n    this._loopDurationSeconds = -1;\n    this._isLoop = false;\n    this._isLoopFadeIn = true;\n    this._lastWeight = 0;\n  }\n  static create(json, onFinishedMotionHandler) {\n    const ret = new CubismMotion();\n    ret.parse(json);\n    ret._sourceFrameRate = ret._motionData.fps;\n    ret._loopDurationSeconds = ret._motionData.duration;\n    ret._onFinishedMotion = onFinishedMotionHandler;\n    return ret;\n  }\n  doUpdateParameters(model, userTimeSeconds, fadeWeight, motionQueueEntry) {\n    if (this._modelCurveIdEyeBlink == null) {\n      this._modelCurveIdEyeBlink = EffectNameEyeBlink;\n    }\n    if (this._modelCurveIdLipSync == null) {\n      this._modelCurveIdLipSync = EffectNameLipSync;\n    }\n    let timeOffsetSeconds = userTimeSeconds - motionQueueEntry.getStartTime();\n    if (timeOffsetSeconds < 0) {\n      timeOffsetSeconds = 0;\n    }\n    let lipSyncValue = Number.MAX_VALUE;\n    let eyeBlinkValue = Number.MAX_VALUE;\n    const MaxTargetSize = 64;\n    let lipSyncFlags = 0;\n    let eyeBlinkFlags = 0;\n    if (this._eyeBlinkParameterIds.length > MaxTargetSize) {\n      CubismLogDebug(\"too many eye blink targets : {0}\", this._eyeBlinkParameterIds.length);\n    }\n    if (this._lipSyncParameterIds.length > MaxTargetSize) {\n      CubismLogDebug(\"too many lip sync targets : {0}\", this._lipSyncParameterIds.length);\n    }\n    const tmpFadeIn = this._fadeInSeconds <= 0 ? 1 : CubismMath.getEasingSine((userTimeSeconds - motionQueueEntry.getFadeInStartTime()) / this._fadeInSeconds);\n    const tmpFadeOut = this._fadeOutSeconds <= 0 || motionQueueEntry.getEndTime() < 0 ? 1 : CubismMath.getEasingSine((motionQueueEntry.getEndTime() - userTimeSeconds) / this._fadeOutSeconds);\n    let value;\n    let c, parameterIndex;\n    let time = timeOffsetSeconds;\n    if (this._isLoop) {\n      while (time > this._motionData.duration) {\n        time -= this._motionData.duration;\n      }\n    }\n    const curves = this._motionData.curves;\n    for (c = 0; c < this._motionData.curveCount && curves[c].type == CubismMotionCurveTarget.CubismMotionCurveTarget_Model; ++c) {\n      value = evaluateCurve(this._motionData, c, time);\n      if (curves[c].id == this._modelCurveIdEyeBlink) {\n        eyeBlinkValue = value;\n      } else if (curves[c].id == this._modelCurveIdLipSync) {\n        lipSyncValue = value;\n      }\n    }\n    for (; c < this._motionData.curveCount && curves[c].type == CubismMotionCurveTarget.CubismMotionCurveTarget_Parameter; ++c) {\n      parameterIndex = model.getParameterIndex(curves[c].id);\n      if (parameterIndex == -1) {\n        continue;\n      }\n      const sourceValue = model.getParameterValueByIndex(parameterIndex);\n      value = evaluateCurve(this._motionData, c, time);\n      if (eyeBlinkValue != Number.MAX_VALUE) {\n        for (let i = 0; i < this._eyeBlinkParameterIds.length && i < MaxTargetSize; ++i) {\n          if (this._eyeBlinkParameterIds[i] == curves[c].id) {\n            value *= eyeBlinkValue;\n            eyeBlinkFlags |= 1 << i;\n            break;\n          }\n        }\n      }\n      if (lipSyncValue != Number.MAX_VALUE) {\n        for (let i = 0; i < this._lipSyncParameterIds.length && i < MaxTargetSize; ++i) {\n          if (this._lipSyncParameterIds[i] == curves[c].id) {\n            value += lipSyncValue;\n            lipSyncFlags |= 1 << i;\n            break;\n          }\n        }\n      }\n      let v;\n      if (curves[c].fadeInTime < 0 && curves[c].fadeOutTime < 0) {\n        v = sourceValue + (value - sourceValue) * fadeWeight;\n      } else {\n        let fin;\n        let fout;\n        if (curves[c].fadeInTime < 0) {\n          fin = tmpFadeIn;\n        } else {\n          fin = curves[c].fadeInTime == 0 ? 1 : CubismMath.getEasingSine((userTimeSeconds - motionQueueEntry.getFadeInStartTime()) / curves[c].fadeInTime);\n        }\n        if (curves[c].fadeOutTime < 0) {\n          fout = tmpFadeOut;\n        } else {\n          fout = curves[c].fadeOutTime == 0 || motionQueueEntry.getEndTime() < 0 ? 1 : CubismMath.getEasingSine((motionQueueEntry.getEndTime() - userTimeSeconds) / curves[c].fadeOutTime);\n        }\n        const paramWeight = this._weight * fin * fout;\n        v = sourceValue + (value - sourceValue) * paramWeight;\n      }\n      model.setParameterValueByIndex(parameterIndex, v, 1);\n    }\n    {\n      if (eyeBlinkValue != Number.MAX_VALUE) {\n        for (let i = 0; i < this._eyeBlinkParameterIds.length && i < MaxTargetSize; ++i) {\n          const sourceValue = model.getParameterValueById(this._eyeBlinkParameterIds[i]);\n          if (eyeBlinkFlags >> i & 1) {\n            continue;\n          }\n          const v = sourceValue + (eyeBlinkValue - sourceValue) * fadeWeight;\n          model.setParameterValueById(this._eyeBlinkParameterIds[i], v);\n        }\n      }\n      if (lipSyncValue != Number.MAX_VALUE) {\n        for (let i = 0; i < this._lipSyncParameterIds.length && i < MaxTargetSize; ++i) {\n          const sourceValue = model.getParameterValueById(this._lipSyncParameterIds[i]);\n          if (lipSyncFlags >> i & 1) {\n            continue;\n          }\n          const v = sourceValue + (lipSyncValue - sourceValue) * fadeWeight;\n          model.setParameterValueById(this._lipSyncParameterIds[i], v);\n        }\n      }\n    }\n    for (; c < this._motionData.curveCount && curves[c].type == CubismMotionCurveTarget.CubismMotionCurveTarget_PartOpacity; ++c) {\n      value = evaluateCurve(this._motionData, c, time);\n      if (CubismConfig.setOpacityFromMotion) {\n        model.setPartOpacityById(curves[c].id, value);\n      } else {\n        parameterIndex = model.getParameterIndex(curves[c].id);\n        if (parameterIndex == -1) {\n          continue;\n        }\n        model.setParameterValueByIndex(parameterIndex, value);\n      }\n    }\n    if (timeOffsetSeconds >= this._motionData.duration) {\n      if (this._isLoop) {\n        motionQueueEntry.setStartTime(userTimeSeconds);\n        if (this._isLoopFadeIn) {\n          motionQueueEntry.setFadeInStartTime(userTimeSeconds);\n        }\n      } else {\n        if (this._onFinishedMotion) {\n          this._onFinishedMotion(this);\n        }\n        motionQueueEntry.setIsFinished(true);\n      }\n    }\n    this._lastWeight = fadeWeight;\n  }\n  setIsLoop(loop) {\n    this._isLoop = loop;\n  }\n  isLoop() {\n    return this._isLoop;\n  }\n  setIsLoopFadeIn(loopFadeIn) {\n    this._isLoopFadeIn = loopFadeIn;\n  }\n  isLoopFadeIn() {\n    return this._isLoopFadeIn;\n  }\n  getDuration() {\n    return this._isLoop ? -1 : this._loopDurationSeconds;\n  }\n  getLoopDuration() {\n    return this._loopDurationSeconds;\n  }\n  setParameterFadeInTime(parameterId, value) {\n    const curves = this._motionData.curves;\n    for (let i = 0; i < this._motionData.curveCount; ++i) {\n      if (parameterId == curves[i].id) {\n        curves[i].fadeInTime = value;\n        return;\n      }\n    }\n  }\n  setParameterFadeOutTime(parameterId, value) {\n    const curves = this._motionData.curves;\n    for (let i = 0; i < this._motionData.curveCount; ++i) {\n      if (parameterId == curves[i].id) {\n        curves[i].fadeOutTime = value;\n        return;\n      }\n    }\n  }\n  getParameterFadeInTime(parameterId) {\n    const curves = this._motionData.curves;\n    for (let i = 0; i < this._motionData.curveCount; ++i) {\n      if (parameterId == curves[i].id) {\n        return curves[i].fadeInTime;\n      }\n    }\n    return -1;\n  }\n  getParameterFadeOutTime(parameterId) {\n    const curves = this._motionData.curves;\n    for (let i = 0; i < this._motionData.curveCount; ++i) {\n      if (parameterId == curves[i].id) {\n        return curves[i].fadeOutTime;\n      }\n    }\n    return -1;\n  }\n  setEffectIds(eyeBlinkParameterIds, lipSyncParameterIds) {\n    this._eyeBlinkParameterIds = eyeBlinkParameterIds;\n    this._lipSyncParameterIds = lipSyncParameterIds;\n  }\n  release() {\n    this._motionData = void 0;\n  }\n  parse(motionJson) {\n    this._motionData = new CubismMotionData();\n    let json = new CubismMotionJson(motionJson);\n    this._motionData.duration = json.getMotionDuration();\n    this._motionData.loop = json.isMotionLoop();\n    this._motionData.curveCount = json.getMotionCurveCount();\n    this._motionData.fps = json.getMotionFps();\n    this._motionData.eventCount = json.getEventCount();\n    const areBeziersRestructed = json.getEvaluationOptionFlag(EvaluationOptionFlag.EvaluationOptionFlag_AreBeziersRistricted);\n    const fadeInSeconds = json.getMotionFadeInTime();\n    const fadeOutSeconds = json.getMotionFadeOutTime();\n    if (fadeInSeconds !== void 0) {\n      this._fadeInSeconds = fadeInSeconds < 0 ? 1 : fadeInSeconds;\n    } else {\n      this._fadeInSeconds = 1;\n    }\n    if (fadeOutSeconds !== void 0) {\n      this._fadeOutSeconds = fadeOutSeconds < 0 ? 1 : fadeOutSeconds;\n    } else {\n      this._fadeOutSeconds = 1;\n    }\n    this._motionData.curves = Array.from({ length: this._motionData.curveCount }).map(() => new CubismMotionCurve());\n    this._motionData.segments = Array.from({ length: json.getMotionTotalSegmentCount() }).map(() => new CubismMotionSegment());\n    this._motionData.events = Array.from({ length: this._motionData.eventCount }).map(() => new CubismMotionEvent());\n    this._motionData.points = [];\n    let totalPointCount = 0;\n    let totalSegmentCount = 0;\n    for (let curveCount = 0; curveCount < this._motionData.curveCount; ++curveCount) {\n      const curve = this._motionData.curves[curveCount];\n      switch (json.getMotionCurveTarget(curveCount)) {\n        case TargetNameModel:\n          curve.type = CubismMotionCurveTarget.CubismMotionCurveTarget_Model;\n          break;\n        case TargetNameParameter:\n          curve.type = CubismMotionCurveTarget.CubismMotionCurveTarget_Parameter;\n          break;\n        case TargetNamePartOpacity:\n          curve.type = CubismMotionCurveTarget.CubismMotionCurveTarget_PartOpacity;\n          break;\n        default:\n          CubismLogWarning('Warning : Unable to get segment type from Curve! The number of \"CurveCount\" may be incorrect!');\n      }\n      curve.id = json.getMotionCurveId(curveCount);\n      curve.baseSegmentIndex = totalSegmentCount;\n      const fadeInTime = json.getMotionCurveFadeInTime(curveCount);\n      const fadeOutTime = json.getMotionCurveFadeOutTime(curveCount);\n      curve.fadeInTime = fadeInTime !== void 0 ? fadeInTime : -1;\n      curve.fadeOutTime = fadeOutTime !== void 0 ? fadeOutTime : -1;\n      for (let segmentPosition = 0; segmentPosition < json.getMotionCurveSegmentCount(curveCount); ) {\n        if (segmentPosition == 0) {\n          this._motionData.segments[totalSegmentCount].basePointIndex = totalPointCount;\n          this._motionData.points[totalPointCount] = new CubismMotionPoint(json.getMotionCurveSegment(curveCount, segmentPosition), json.getMotionCurveSegment(curveCount, segmentPosition + 1));\n          totalPointCount += 1;\n          segmentPosition += 2;\n        } else {\n          this._motionData.segments[totalSegmentCount].basePointIndex = totalPointCount - 1;\n        }\n        const segment = json.getMotionCurveSegment(curveCount, segmentPosition);\n        switch (segment) {\n          case CubismMotionSegmentType.CubismMotionSegmentType_Linear: {\n            this._motionData.segments[totalSegmentCount].segmentType = CubismMotionSegmentType.CubismMotionSegmentType_Linear;\n            this._motionData.segments[totalSegmentCount].evaluate = linearEvaluate;\n            this._motionData.points[totalPointCount] = new CubismMotionPoint(json.getMotionCurveSegment(curveCount, segmentPosition + 1), json.getMotionCurveSegment(curveCount, segmentPosition + 2));\n            totalPointCount += 1;\n            segmentPosition += 3;\n            break;\n          }\n          case CubismMotionSegmentType.CubismMotionSegmentType_Bezier: {\n            this._motionData.segments[totalSegmentCount].segmentType = CubismMotionSegmentType.CubismMotionSegmentType_Bezier;\n            if (areBeziersRestructed || UseOldBeziersCurveMotion) {\n              this._motionData.segments[totalSegmentCount].evaluate = bezierEvaluate;\n            } else {\n              this._motionData.segments[totalSegmentCount].evaluate = bezierEvaluateCardanoInterpretation;\n            }\n            this._motionData.points[totalPointCount] = new CubismMotionPoint(json.getMotionCurveSegment(curveCount, segmentPosition + 1), json.getMotionCurveSegment(curveCount, segmentPosition + 2));\n            this._motionData.points[totalPointCount + 1] = new CubismMotionPoint(json.getMotionCurveSegment(curveCount, segmentPosition + 3), json.getMotionCurveSegment(curveCount, segmentPosition + 4));\n            this._motionData.points[totalPointCount + 2] = new CubismMotionPoint(json.getMotionCurveSegment(curveCount, segmentPosition + 5), json.getMotionCurveSegment(curveCount, segmentPosition + 6));\n            totalPointCount += 3;\n            segmentPosition += 7;\n            break;\n          }\n          case CubismMotionSegmentType.CubismMotionSegmentType_Stepped: {\n            this._motionData.segments[totalSegmentCount].segmentType = CubismMotionSegmentType.CubismMotionSegmentType_Stepped;\n            this._motionData.segments[totalSegmentCount].evaluate = steppedEvaluate;\n            this._motionData.points[totalPointCount] = new CubismMotionPoint(json.getMotionCurveSegment(curveCount, segmentPosition + 1), json.getMotionCurveSegment(curveCount, segmentPosition + 2));\n            totalPointCount += 1;\n            segmentPosition += 3;\n            break;\n          }\n          case CubismMotionSegmentType.CubismMotionSegmentType_InverseStepped: {\n            this._motionData.segments[totalSegmentCount].segmentType = CubismMotionSegmentType.CubismMotionSegmentType_InverseStepped;\n            this._motionData.segments[totalSegmentCount].evaluate = inverseSteppedEvaluate;\n            this._motionData.points[totalPointCount] = new CubismMotionPoint(json.getMotionCurveSegment(curveCount, segmentPosition + 1), json.getMotionCurveSegment(curveCount, segmentPosition + 2));\n            totalPointCount += 1;\n            segmentPosition += 3;\n            break;\n          }\n        }\n        ++curve.segmentCount;\n        ++totalSegmentCount;\n      }\n      this._motionData.curves.push(curve);\n    }\n    for (let userdatacount = 0; userdatacount < json.getEventCount(); ++userdatacount) {\n      this._motionData.events[userdatacount].fireTime = json.getEventTime(userdatacount);\n      this._motionData.events[userdatacount].value = json.getEventValue(userdatacount);\n    }\n    json.release();\n  }\n  getFiredEvent(beforeCheckTimeSeconds, motionTimeSeconds) {\n    this._firedEventValues.length = 0;\n    for (let u = 0; u < this._motionData.eventCount; ++u) {\n      if (this._motionData.events[u].fireTime > beforeCheckTimeSeconds && this._motionData.events[u].fireTime <= motionTimeSeconds) {\n        this._firedEventValues.push(this._motionData.events[u].value);\n      }\n    }\n    return this._firedEventValues;\n  }\n}\nclass Cubism4MotionManager extends MotionManager {\n  constructor(settings, options) {\n    var _a;\n    super(settings, options);\n    this.groups = { idle: \"Idle\" };\n    this.motionDataType = \"json\";\n    this.queueManager = new CubismMotionQueueManager();\n    this.definitions = (_a = settings.motions) != null ? _a : {};\n    this.eyeBlinkIds = settings.getEyeBlinkParameters() || [];\n    this.lipSyncIds = settings.getLipSyncParameters() || [];\n    this.init(options);\n  }\n  init(options) {\n    super.init(options);\n    if (this.settings.expressions) {\n      this.expressionManager = new Cubism4ExpressionManager(this.settings, options);\n    }\n    this.queueManager.setEventCallback((caller, eventValue, customData) => {\n      this.emit(\"motion:\" + eventValue);\n    });\n  }\n  isFinished() {\n    return this.queueManager.isFinished();\n  }\n  _startMotion(motion, onFinish) {\n    motion.setFinishedMotionHandler(onFinish);\n    this.queueManager.stopAllMotions();\n    return this.queueManager.startMotion(motion, false, performance.now());\n  }\n  _stopAllMotions() {\n    this.queueManager.stopAllMotions();\n  }\n  createMotion(data, group, definition) {\n    const motion = CubismMotion.create(data);\n    const json = new CubismMotionJson(data);\n    const defaultFadingDuration = (group === this.groups.idle ? config.idleMotionFadingDuration : config.motionFadingDuration) / 1e3;\n    if (json.getMotionFadeInTime() === void 0) {\n      motion.setFadeInTime(definition.FadeInTime > 0 ? definition.FadeInTime : defaultFadingDuration);\n    }\n    if (json.getMotionFadeOutTime() === void 0) {\n      motion.setFadeOutTime(definition.FadeOutTime > 0 ? definition.FadeOutTime : defaultFadingDuration);\n    }\n    motion.setEffectIds(this.eyeBlinkIds, this.lipSyncIds);\n    return motion;\n  }\n  getMotionFile(definition) {\n    return definition.File;\n  }\n  getMotionName(definition) {\n    return definition.File;\n  }\n  getSoundFile(definition) {\n    return definition.Sound;\n  }\n  updateParameters(model, now) {\n    return this.queueManager.doUpdateMotion(model, now);\n  }\n  destroy() {\n    super.destroy();\n    this.queueManager.release();\n    this.queueManager = void 0;\n  }\n}\nconst ParamAngleX = \"ParamAngleX\";\nconst ParamAngleY = \"ParamAngleY\";\nconst ParamAngleZ = \"ParamAngleZ\";\nconst ParamEyeBallX = \"ParamEyeBallX\";\nconst ParamEyeBallY = \"ParamEyeBallY\";\nconst ParamBodyAngleX = \"ParamBodyAngleX\";\nconst ParamBreath = \"ParamBreath\";\nclass CubismBreath {\n  constructor() {\n    this._breathParameters = [];\n    this._currentTime = 0;\n  }\n  static create() {\n    return new CubismBreath();\n  }\n  setParameters(breathParameters) {\n    this._breathParameters = breathParameters;\n  }\n  getParameters() {\n    return this._breathParameters;\n  }\n  updateParameters(model, deltaTimeSeconds) {\n    this._currentTime += deltaTimeSeconds;\n    const t = this._currentTime * 2 * 3.14159;\n    for (let i = 0; i < this._breathParameters.length; ++i) {\n      const data = this._breathParameters[i];\n      model.addParameterValueById(data.parameterId, data.offset + data.peak * Math.sin(t / data.cycle), data.weight);\n    }\n  }\n}\nclass BreathParameterData {\n  constructor(parameterId, offset, peak, cycle, weight) {\n    this.parameterId = parameterId == void 0 ? void 0 : parameterId;\n    this.offset = offset == void 0 ? 0 : offset;\n    this.peak = peak == void 0 ? 0 : peak;\n    this.cycle = cycle == void 0 ? 0 : cycle;\n    this.weight = weight == void 0 ? 0 : weight;\n  }\n}\nconst _CubismEyeBlink = class {\n  static create(modelSetting) {\n    return new _CubismEyeBlink(modelSetting);\n  }\n  setBlinkingInterval(blinkingInterval) {\n    this._blinkingIntervalSeconds = blinkingInterval;\n  }\n  setBlinkingSetting(closing, closed, opening) {\n    this._closingSeconds = closing;\n    this._closedSeconds = closed;\n    this._openingSeconds = opening;\n  }\n  setParameterIds(parameterIds) {\n    this._parameterIds = parameterIds;\n  }\n  getParameterIds() {\n    return this._parameterIds;\n  }\n  updateParameters(model, deltaTimeSeconds) {\n    this._userTimeSeconds += deltaTimeSeconds;\n    let parameterValue;\n    let t = 0;\n    switch (this._blinkingState) {\n      case EyeState.EyeState_Closing:\n        t = (this._userTimeSeconds - this._stateStartTimeSeconds) / this._closingSeconds;\n        if (t >= 1) {\n          t = 1;\n          this._blinkingState = EyeState.EyeState_Closed;\n          this._stateStartTimeSeconds = this._userTimeSeconds;\n        }\n        parameterValue = 1 - t;\n        break;\n      case EyeState.EyeState_Closed:\n        t = (this._userTimeSeconds - this._stateStartTimeSeconds) / this._closedSeconds;\n        if (t >= 1) {\n          this._blinkingState = EyeState.EyeState_Opening;\n          this._stateStartTimeSeconds = this._userTimeSeconds;\n        }\n        parameterValue = 0;\n        break;\n      case EyeState.EyeState_Opening:\n        t = (this._userTimeSeconds - this._stateStartTimeSeconds) / this._openingSeconds;\n        if (t >= 1) {\n          t = 1;\n          this._blinkingState = EyeState.EyeState_Interval;\n          this._nextBlinkingTime = this.determinNextBlinkingTiming();\n        }\n        parameterValue = t;\n        break;\n      case EyeState.EyeState_Interval:\n        if (this._nextBlinkingTime < this._userTimeSeconds) {\n          this._blinkingState = EyeState.EyeState_Closing;\n          this._stateStartTimeSeconds = this._userTimeSeconds;\n        }\n        parameterValue = 1;\n        break;\n      case EyeState.EyeState_First:\n      default:\n        this._blinkingState = EyeState.EyeState_Interval;\n        this._nextBlinkingTime = this.determinNextBlinkingTiming();\n        parameterValue = 1;\n        break;\n    }\n    if (!_CubismEyeBlink.CloseIfZero) {\n      parameterValue = -parameterValue;\n    }\n    for (let i = 0; i < this._parameterIds.length; ++i) {\n      model.setParameterValueById(this._parameterIds[i], parameterValue);\n    }\n  }\n  constructor(modelSetting) {\n    var _a, _b;\n    this._blinkingState = EyeState.EyeState_First;\n    this._nextBlinkingTime = 0;\n    this._stateStartTimeSeconds = 0;\n    this._blinkingIntervalSeconds = 4;\n    this._closingSeconds = 0.1;\n    this._closedSeconds = 0.05;\n    this._openingSeconds = 0.15;\n    this._userTimeSeconds = 0;\n    this._parameterIds = [];\n    if (modelSetting == null) {\n      return;\n    }\n    this._parameterIds = (_b = (_a = modelSetting.getEyeBlinkParameters()) == null ? void 0 : _a.slice()) != null ? _b : this._parameterIds;\n  }\n  determinNextBlinkingTiming() {\n    const r = Math.random();\n    return this._userTimeSeconds + r * (2 * this._blinkingIntervalSeconds - 1);\n  }\n};\nlet CubismEyeBlink = _CubismEyeBlink;\nCubismEyeBlink.CloseIfZero = true;\nvar EyeState = /* @__PURE__ */ ((EyeState2) => {\n  EyeState2[EyeState2[\"EyeState_First\"] = 0] = \"EyeState_First\";\n  EyeState2[EyeState2[\"EyeState_Interval\"] = 1] = \"EyeState_Interval\";\n  EyeState2[EyeState2[\"EyeState_Closing\"] = 2] = \"EyeState_Closing\";\n  EyeState2[EyeState2[\"EyeState_Closed\"] = 3] = \"EyeState_Closed\";\n  EyeState2[EyeState2[\"EyeState_Opening\"] = 4] = \"EyeState_Opening\";\n  return EyeState2;\n})(EyeState || {});\nclass csmRect {\n  constructor(x = 0, y = 0, w = 0, h = 0) {\n    this.x = x;\n    this.y = y;\n    this.width = w;\n    this.height = h;\n  }\n  getCenterX() {\n    return this.x + 0.5 * this.width;\n  }\n  getCenterY() {\n    return this.y + 0.5 * this.height;\n  }\n  getRight() {\n    return this.x + this.width;\n  }\n  getBottom() {\n    return this.y + this.height;\n  }\n  setRect(r) {\n    this.x = r.x;\n    this.y = r.y;\n    this.width = r.width;\n    this.height = r.height;\n  }\n  expand(w, h) {\n    this.x -= w;\n    this.y -= h;\n    this.width += w * 2;\n    this.height += h * 2;\n  }\n}\nconst ColorChannelCount = 4;\nconst shaderCount = 10;\nlet s_instance;\nlet s_viewport;\nlet s_fbo;\nclass CubismClippingManager_WebGL {\n  getChannelFlagAsColor(channelNo) {\n    return this._channelColors[channelNo];\n  }\n  getMaskRenderTexture() {\n    let ret = 0;\n    if (this._maskTexture && this._maskTexture.texture != 0) {\n      this._maskTexture.frameNo = this._currentFrameNo;\n      ret = this._maskTexture.texture;\n    }\n    if (ret == 0) {\n      const size = this._clippingMaskBufferSize;\n      this._colorBuffer = this.gl.createTexture();\n      this.gl.bindTexture(this.gl.TEXTURE_2D, this._colorBuffer);\n      this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, size, size, 0, this.gl.RGBA, this.gl.UNSIGNED_BYTE, null);\n      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);\n      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);\n      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);\n      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);\n      this.gl.bindTexture(this.gl.TEXTURE_2D, null);\n      ret = this.gl.createFramebuffer();\n      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, ret);\n      this.gl.framebufferTexture2D(this.gl.FRAMEBUFFER, this.gl.COLOR_ATTACHMENT0, this.gl.TEXTURE_2D, this._colorBuffer, 0);\n      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, s_fbo);\n      this._maskTexture = new CubismRenderTextureResource(this._currentFrameNo, ret);\n    }\n    return ret;\n  }\n  setGL(gl) {\n    this.gl = gl;\n  }\n  calcClippedDrawTotalBounds(model, clippingContext) {\n    let clippedDrawTotalMinX = Number.MAX_VALUE;\n    let clippedDrawTotalMinY = Number.MAX_VALUE;\n    let clippedDrawTotalMaxX = Number.MIN_VALUE;\n    let clippedDrawTotalMaxY = Number.MIN_VALUE;\n    const clippedDrawCount = clippingContext._clippedDrawableIndexList.length;\n    for (let clippedDrawableIndex = 0; clippedDrawableIndex < clippedDrawCount; clippedDrawableIndex++) {\n      const drawableIndex = clippingContext._clippedDrawableIndexList[clippedDrawableIndex];\n      const drawableVertexCount = model.getDrawableVertexCount(drawableIndex);\n      const drawableVertexes = model.getDrawableVertices(drawableIndex);\n      let minX = Number.MAX_VALUE;\n      let minY = Number.MAX_VALUE;\n      let maxX = Number.MIN_VALUE;\n      let maxY = Number.MIN_VALUE;\n      const loop = drawableVertexCount * Constant.vertexStep;\n      for (let pi = Constant.vertexOffset; pi < loop; pi += Constant.vertexStep) {\n        const x = drawableVertexes[pi];\n        const y = drawableVertexes[pi + 1];\n        if (x < minX) {\n          minX = x;\n        }\n        if (x > maxX) {\n          maxX = x;\n        }\n        if (y < minY) {\n          minY = y;\n        }\n        if (y > maxY) {\n          maxY = y;\n        }\n      }\n      if (minX == Number.MAX_VALUE) {\n        continue;\n      }\n      if (minX < clippedDrawTotalMinX) {\n        clippedDrawTotalMinX = minX;\n      }\n      if (minY < clippedDrawTotalMinY) {\n        clippedDrawTotalMinY = minY;\n      }\n      if (maxX > clippedDrawTotalMaxX) {\n        clippedDrawTotalMaxX = maxX;\n      }\n      if (maxY > clippedDrawTotalMaxY) {\n        clippedDrawTotalMaxY = maxY;\n      }\n      if (clippedDrawTotalMinX == Number.MAX_VALUE) {\n        clippingContext._allClippedDrawRect.x = 0;\n        clippingContext._allClippedDrawRect.y = 0;\n        clippingContext._allClippedDrawRect.width = 0;\n        clippingContext._allClippedDrawRect.height = 0;\n        clippingContext._isUsing = false;\n      } else {\n        clippingContext._isUsing = true;\n        const w = clippedDrawTotalMaxX - clippedDrawTotalMinX;\n        const h = clippedDrawTotalMaxY - clippedDrawTotalMinY;\n        clippingContext._allClippedDrawRect.x = clippedDrawTotalMinX;\n        clippingContext._allClippedDrawRect.y = clippedDrawTotalMinY;\n        clippingContext._allClippedDrawRect.width = w;\n        clippingContext._allClippedDrawRect.height = h;\n      }\n    }\n  }\n  constructor() {\n    this._maskRenderTexture = null;\n    this._colorBuffer = null;\n    this._currentFrameNo = 0;\n    this._clippingMaskBufferSize = 256;\n    this._clippingContextListForMask = [];\n    this._clippingContextListForDraw = [];\n    this._channelColors = [];\n    this._tmpBoundsOnModel = new csmRect();\n    this._tmpMatrix = new CubismMatrix44();\n    this._tmpMatrixForMask = new CubismMatrix44();\n    this._tmpMatrixForDraw = new CubismMatrix44();\n    let tmp = new CubismTextureColor();\n    tmp.R = 1;\n    tmp.G = 0;\n    tmp.B = 0;\n    tmp.A = 0;\n    this._channelColors.push(tmp);\n    tmp = new CubismTextureColor();\n    tmp.R = 0;\n    tmp.G = 1;\n    tmp.B = 0;\n    tmp.A = 0;\n    this._channelColors.push(tmp);\n    tmp = new CubismTextureColor();\n    tmp.R = 0;\n    tmp.G = 0;\n    tmp.B = 1;\n    tmp.A = 0;\n    this._channelColors.push(tmp);\n    tmp = new CubismTextureColor();\n    tmp.R = 0;\n    tmp.G = 0;\n    tmp.B = 0;\n    tmp.A = 1;\n    this._channelColors.push(tmp);\n  }\n  release() {\n    var _a, _b, _c;\n    const self = this;\n    for (let i = 0; i < this._clippingContextListForMask.length; i++) {\n      if (this._clippingContextListForMask[i]) {\n        (_a = this._clippingContextListForMask[i]) == null ? void 0 : _a.release();\n      }\n    }\n    self._clippingContextListForMask = void 0;\n    self._clippingContextListForDraw = void 0;\n    if (this._maskTexture) {\n      (_b = this.gl) == null ? void 0 : _b.deleteFramebuffer(this._maskTexture.texture);\n      self._maskTexture = void 0;\n    }\n    self._channelColors = void 0;\n    (_c = this.gl) == null ? void 0 : _c.deleteTexture(this._colorBuffer);\n    this._colorBuffer = null;\n  }\n  initialize(model, drawableCount, drawableMasks, drawableMaskCounts) {\n    for (let i = 0; i < drawableCount; i++) {\n      if (drawableMaskCounts[i] <= 0) {\n        this._clippingContextListForDraw.push(null);\n        continue;\n      }\n      let clippingContext = this.findSameClip(drawableMasks[i], drawableMaskCounts[i]);\n      if (clippingContext == null) {\n        clippingContext = new CubismClippingContext(this, drawableMasks[i], drawableMaskCounts[i]);\n        this._clippingContextListForMask.push(clippingContext);\n      }\n      clippingContext.addClippedDrawable(i);\n      this._clippingContextListForDraw.push(clippingContext);\n    }\n  }\n  setupClippingContext(model, renderer) {\n    this._currentFrameNo++;\n    let usingClipCount = 0;\n    for (let clipIndex = 0; clipIndex < this._clippingContextListForMask.length; clipIndex++) {\n      const cc = this._clippingContextListForMask[clipIndex];\n      this.calcClippedDrawTotalBounds(model, cc);\n      if (cc._isUsing) {\n        usingClipCount++;\n      }\n    }\n    if (usingClipCount > 0) {\n      this.gl.viewport(0, 0, this._clippingMaskBufferSize, this._clippingMaskBufferSize);\n      this._maskRenderTexture = this.getMaskRenderTexture();\n      renderer.getMvpMatrix();\n      renderer.preDraw();\n      this.setupLayoutBounds(usingClipCount);\n      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, this._maskRenderTexture);\n      this.gl.clearColor(1, 1, 1, 1);\n      this.gl.clear(this.gl.COLOR_BUFFER_BIT);\n      for (let clipIndex = 0; clipIndex < this._clippingContextListForMask.length; clipIndex++) {\n        const clipContext = this._clippingContextListForMask[clipIndex];\n        const allClipedDrawRect = clipContext._allClippedDrawRect;\n        const layoutBoundsOnTex01 = clipContext._layoutBounds;\n        const MARGIN = 0.05;\n        this._tmpBoundsOnModel.setRect(allClipedDrawRect);\n        this._tmpBoundsOnModel.expand(allClipedDrawRect.width * MARGIN, allClipedDrawRect.height * MARGIN);\n        const scaleX = layoutBoundsOnTex01.width / this._tmpBoundsOnModel.width;\n        const scaleY = layoutBoundsOnTex01.height / this._tmpBoundsOnModel.height;\n        {\n          this._tmpMatrix.loadIdentity();\n          {\n            this._tmpMatrix.translateRelative(-1, -1);\n            this._tmpMatrix.scaleRelative(2, 2);\n          }\n          {\n            this._tmpMatrix.translateRelative(layoutBoundsOnTex01.x, layoutBoundsOnTex01.y);\n            this._tmpMatrix.scaleRelative(scaleX, scaleY);\n            this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x, -this._tmpBoundsOnModel.y);\n          }\n          this._tmpMatrixForMask.setMatrix(this._tmpMatrix.getArray());\n        }\n        {\n          this._tmpMatrix.loadIdentity();\n          {\n            this._tmpMatrix.translateRelative(layoutBoundsOnTex01.x, layoutBoundsOnTex01.y);\n            this._tmpMatrix.scaleRelative(scaleX, scaleY);\n            this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x, -this._tmpBoundsOnModel.y);\n          }\n          this._tmpMatrixForDraw.setMatrix(this._tmpMatrix.getArray());\n        }\n        clipContext._matrixForMask.setMatrix(this._tmpMatrixForMask.getArray());\n        clipContext._matrixForDraw.setMatrix(this._tmpMatrixForDraw.getArray());\n        const clipDrawCount = clipContext._clippingIdCount;\n        for (let i = 0; i < clipDrawCount; i++) {\n          const clipDrawIndex = clipContext._clippingIdList[i];\n          if (!model.getDrawableDynamicFlagVertexPositionsDidChange(clipDrawIndex)) {\n            continue;\n          }\n          renderer.setIsCulling(model.getDrawableCulling(clipDrawIndex) != false);\n          renderer.setClippingContextBufferForMask(clipContext);\n          renderer.drawMesh(model.getDrawableTextureIndices(clipDrawIndex), model.getDrawableVertexIndexCount(clipDrawIndex), model.getDrawableVertexCount(clipDrawIndex), model.getDrawableVertexIndices(clipDrawIndex), model.getDrawableVertices(clipDrawIndex), model.getDrawableVertexUvs(clipDrawIndex), model.getDrawableOpacity(clipDrawIndex), CubismBlendMode.CubismBlendMode_Normal, false);\n        }\n      }\n      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, s_fbo);\n      renderer.setClippingContextBufferForMask(null);\n      this.gl.viewport(s_viewport[0], s_viewport[1], s_viewport[2], s_viewport[3]);\n    }\n  }\n  findSameClip(drawableMasks, drawableMaskCounts) {\n    for (let i = 0; i < this._clippingContextListForMask.length; i++) {\n      const clippingContext = this._clippingContextListForMask[i];\n      const count = clippingContext._clippingIdCount;\n      if (count != drawableMaskCounts) {\n        continue;\n      }\n      let sameCount = 0;\n      for (let j = 0; j < count; j++) {\n        const clipId = clippingContext._clippingIdList[j];\n        for (let k = 0; k < count; k++) {\n          if (drawableMasks[k] == clipId) {\n            sameCount++;\n            break;\n          }\n        }\n      }\n      if (sameCount == count) {\n        return clippingContext;\n      }\n    }\n    return null;\n  }\n  setupLayoutBounds(usingClipCount) {\n    let div = usingClipCount / ColorChannelCount;\n    let mod = usingClipCount % ColorChannelCount;\n    div = ~~div;\n    mod = ~~mod;\n    let curClipIndex = 0;\n    for (let channelNo = 0; channelNo < ColorChannelCount; channelNo++) {\n      const layoutCount = div + (channelNo < mod ? 1 : 0);\n      if (layoutCount == 0)\n        ;\n      else if (layoutCount == 1) {\n        const clipContext = this._clippingContextListForMask[curClipIndex++];\n        clipContext._layoutChannelNo = channelNo;\n        clipContext._layoutBounds.x = 0;\n        clipContext._layoutBounds.y = 0;\n        clipContext._layoutBounds.width = 1;\n        clipContext._layoutBounds.height = 1;\n      } else if (layoutCount == 2) {\n        for (let i = 0; i < layoutCount; i++) {\n          let xpos = i % 2;\n          xpos = ~~xpos;\n          const cc = this._clippingContextListForMask[curClipIndex++];\n          cc._layoutChannelNo = channelNo;\n          cc._layoutBounds.x = xpos * 0.5;\n          cc._layoutBounds.y = 0;\n          cc._layoutBounds.width = 0.5;\n          cc._layoutBounds.height = 1;\n        }\n      } else if (layoutCount <= 4) {\n        for (let i = 0; i < layoutCount; i++) {\n          let xpos = i % 2;\n          let ypos = i / 2;\n          xpos = ~~xpos;\n          ypos = ~~ypos;\n          const cc = this._clippingContextListForMask[curClipIndex++];\n          cc._layoutChannelNo = channelNo;\n          cc._layoutBounds.x = xpos * 0.5;\n          cc._layoutBounds.y = ypos * 0.5;\n          cc._layoutBounds.width = 0.5;\n          cc._layoutBounds.height = 0.5;\n        }\n      } else if (layoutCount <= 9) {\n        for (let i = 0; i < layoutCount; i++) {\n          let xpos = i % 3;\n          let ypos = i / 3;\n          xpos = ~~xpos;\n          ypos = ~~ypos;\n          const cc = this._clippingContextListForMask[curClipIndex++];\n          cc._layoutChannelNo = channelNo;\n          cc._layoutBounds.x = xpos / 3;\n          cc._layoutBounds.y = ypos / 3;\n          cc._layoutBounds.width = 1 / 3;\n          cc._layoutBounds.height = 1 / 3;\n        }\n      } else if (CubismConfig.supportMoreMaskDivisions && layoutCount <= 16) {\n        for (let i = 0; i < layoutCount; i++) {\n          let xpos = i % 4;\n          let ypos = i / 4;\n          xpos = ~~xpos;\n          ypos = ~~ypos;\n          const cc = this._clippingContextListForMask[curClipIndex++];\n          cc._layoutChannelNo = channelNo;\n          cc._layoutBounds.x = xpos / 4;\n          cc._layoutBounds.y = ypos / 4;\n          cc._layoutBounds.width = 1 / 4;\n          cc._layoutBounds.height = 1 / 4;\n        }\n      } else {\n        CubismLogError(\"not supported mask count : {0}\", layoutCount);\n      }\n    }\n  }\n  getColorBuffer() {\n    return this._colorBuffer;\n  }\n  getClippingContextListForDraw() {\n    return this._clippingContextListForDraw;\n  }\n  setClippingMaskBufferSize(size) {\n    this._clippingMaskBufferSize = size;\n  }\n  getClippingMaskBufferSize() {\n    return this._clippingMaskBufferSize;\n  }\n}\nclass CubismRenderTextureResource {\n  constructor(frameNo, texture) {\n    this.frameNo = frameNo;\n    this.texture = texture;\n  }\n}\nclass CubismClippingContext {\n  constructor(manager, clippingDrawableIndices, clipCount) {\n    this._isUsing = false;\n    this._owner = manager;\n    this._clippingIdList = clippingDrawableIndices;\n    this._clippingIdCount = clipCount;\n    this._allClippedDrawRect = new csmRect();\n    this._layoutBounds = new csmRect();\n    this._clippedDrawableIndexList = [];\n    this._matrixForMask = new CubismMatrix44();\n    this._matrixForDraw = new CubismMatrix44();\n  }\n  release() {\n    const self = this;\n    self._layoutBounds = void 0;\n    self._allClippedDrawRect = void 0;\n    self._clippedDrawableIndexList = void 0;\n  }\n  addClippedDrawable(drawableIndex) {\n    this._clippedDrawableIndexList.push(drawableIndex);\n  }\n  getClippingManager() {\n    return this._owner;\n  }\n  setGl(gl) {\n    this._owner.setGL(gl);\n  }\n}\nclass CubismShader_WebGL {\n  static getInstance() {\n    if (s_instance == null) {\n      s_instance = new CubismShader_WebGL();\n      return s_instance;\n    }\n    return s_instance;\n  }\n  static deleteInstance() {\n    if (s_instance) {\n      s_instance.release();\n      s_instance = void 0;\n    }\n  }\n  constructor() {\n    this._shaderSets = [];\n  }\n  release() {\n    this.releaseShaderProgram();\n  }\n  setupShaderProgram(renderer, textureId, vertexCount, vertexArray, indexArray, uvArray, bufferData, opacity, colorBlendMode, baseColor, isPremultipliedAlpha, matrix4x4, invertedMask) {\n    if (!isPremultipliedAlpha) {\n      CubismLogError(\"NoPremultipliedAlpha is not allowed\");\n    }\n    if (this._shaderSets.length == 0) {\n      this.generateShaders();\n    }\n    let SRC_COLOR;\n    let DST_COLOR;\n    let SRC_ALPHA;\n    let DST_ALPHA;\n    const clippingContextBufferForMask = renderer.getClippingContextBufferForMask();\n    if (clippingContextBufferForMask != null) {\n      const shaderSet = this._shaderSets[ShaderNames.ShaderNames_SetupMask];\n      this.gl.useProgram(shaderSet.shaderProgram);\n      this.gl.activeTexture(this.gl.TEXTURE0);\n      this.gl.bindTexture(this.gl.TEXTURE_2D, textureId);\n      this.gl.uniform1i(shaderSet.samplerTexture0Location, 0);\n      if (bufferData.vertex == null) {\n        bufferData.vertex = this.gl.createBuffer();\n      }\n      this.gl.bindBuffer(this.gl.ARRAY_BUFFER, bufferData.vertex);\n      this.gl.bufferData(this.gl.ARRAY_BUFFER, vertexArray, this.gl.DYNAMIC_DRAW);\n      this.gl.enableVertexAttribArray(shaderSet.attributePositionLocation);\n      this.gl.vertexAttribPointer(shaderSet.attributePositionLocation, 2, this.gl.FLOAT, false, 0, 0);\n      if (bufferData.uv == null) {\n        bufferData.uv = this.gl.createBuffer();\n      }\n      this.gl.bindBuffer(this.gl.ARRAY_BUFFER, bufferData.uv);\n      this.gl.bufferData(this.gl.ARRAY_BUFFER, uvArray, this.gl.DYNAMIC_DRAW);\n      this.gl.enableVertexAttribArray(shaderSet.attributeTexCoordLocation);\n      this.gl.vertexAttribPointer(shaderSet.attributeTexCoordLocation, 2, this.gl.FLOAT, false, 0, 0);\n      const channelNo = clippingContextBufferForMask._layoutChannelNo;\n      const colorChannel = clippingContextBufferForMask.getClippingManager().getChannelFlagAsColor(channelNo);\n      this.gl.uniform4f(shaderSet.uniformChannelFlagLocation, colorChannel.R, colorChannel.G, colorChannel.B, colorChannel.A);\n      this.gl.uniformMatrix4fv(shaderSet.uniformClipMatrixLocation, false, clippingContextBufferForMask._matrixForMask.getArray());\n      const rect = clippingContextBufferForMask._layoutBounds;\n      this.gl.uniform4f(shaderSet.uniformBaseColorLocation, rect.x * 2 - 1, rect.y * 2 - 1, rect.getRight() * 2 - 1, rect.getBottom() * 2 - 1);\n      SRC_COLOR = this.gl.ZERO;\n      DST_COLOR = this.gl.ONE_MINUS_SRC_COLOR;\n      SRC_ALPHA = this.gl.ZERO;\n      DST_ALPHA = this.gl.ONE_MINUS_SRC_ALPHA;\n    } else {\n      const clippingContextBufferForDraw = renderer.getClippingContextBufferForDraw();\n      const masked = clippingContextBufferForDraw != null;\n      const offset = masked ? invertedMask ? 2 : 1 : 0;\n      let shaderSet;\n      switch (colorBlendMode) {\n        case CubismBlendMode.CubismBlendMode_Normal:\n        default:\n          shaderSet = this._shaderSets[ShaderNames.ShaderNames_NormalPremultipliedAlpha + offset];\n          SRC_COLOR = this.gl.ONE;\n          DST_COLOR = this.gl.ONE_MINUS_SRC_ALPHA;\n          SRC_ALPHA = this.gl.ONE;\n          DST_ALPHA = this.gl.ONE_MINUS_SRC_ALPHA;\n          break;\n        case CubismBlendMode.CubismBlendMode_Additive:\n          shaderSet = this._shaderSets[ShaderNames.ShaderNames_AddPremultipliedAlpha + offset];\n          SRC_COLOR = this.gl.ONE;\n          DST_COLOR = this.gl.ONE;\n          SRC_ALPHA = this.gl.ZERO;\n          DST_ALPHA = this.gl.ONE;\n          break;\n        case CubismBlendMode.CubismBlendMode_Multiplicative:\n          shaderSet = this._shaderSets[ShaderNames.ShaderNames_MultPremultipliedAlpha + offset];\n          SRC_COLOR = this.gl.DST_COLOR;\n          DST_COLOR = this.gl.ONE_MINUS_SRC_ALPHA;\n          SRC_ALPHA = this.gl.ZERO;\n          DST_ALPHA = this.gl.ONE;\n          break;\n      }\n      this.gl.useProgram(shaderSet.shaderProgram);\n      if (bufferData.vertex == null) {\n        bufferData.vertex = this.gl.createBuffer();\n      }\n      this.gl.bindBuffer(this.gl.ARRAY_BUFFER, bufferData.vertex);\n      this.gl.bufferData(this.gl.ARRAY_BUFFER, vertexArray, this.gl.DYNAMIC_DRAW);\n      this.gl.enableVertexAttribArray(shaderSet.attributePositionLocation);\n      this.gl.vertexAttribPointer(shaderSet.attributePositionLocation, 2, this.gl.FLOAT, false, 0, 0);\n      if (bufferData.uv == null) {\n        bufferData.uv = this.gl.createBuffer();\n      }\n      this.gl.bindBuffer(this.gl.ARRAY_BUFFER, bufferData.uv);\n      this.gl.bufferData(this.gl.ARRAY_BUFFER, uvArray, this.gl.DYNAMIC_DRAW);\n      this.gl.enableVertexAttribArray(shaderSet.attributeTexCoordLocation);\n      this.gl.vertexAttribPointer(shaderSet.attributeTexCoordLocation, 2, this.gl.FLOAT, false, 0, 0);\n      if (clippingContextBufferForDraw != null) {\n        this.gl.activeTexture(this.gl.TEXTURE1);\n        const tex = clippingContextBufferForDraw.getClippingManager().getColorBuffer();\n        this.gl.bindTexture(this.gl.TEXTURE_2D, tex);\n        this.gl.uniform1i(shaderSet.samplerTexture1Location, 1);\n        this.gl.uniformMatrix4fv(shaderSet.uniformClipMatrixLocation, false, clippingContextBufferForDraw._matrixForDraw.getArray());\n        const channelNo = clippingContextBufferForDraw._layoutChannelNo;\n        const colorChannel = clippingContextBufferForDraw.getClippingManager().getChannelFlagAsColor(channelNo);\n        this.gl.uniform4f(shaderSet.uniformChannelFlagLocation, colorChannel.R, colorChannel.G, colorChannel.B, colorChannel.A);\n      }\n      this.gl.activeTexture(this.gl.TEXTURE0);\n      this.gl.bindTexture(this.gl.TEXTURE_2D, textureId);\n      this.gl.uniform1i(shaderSet.samplerTexture0Location, 0);\n      this.gl.uniformMatrix4fv(shaderSet.uniformMatrixLocation, false, matrix4x4.getArray());\n      this.gl.uniform4f(shaderSet.uniformBaseColorLocation, baseColor.R, baseColor.G, baseColor.B, baseColor.A);\n    }\n    if (bufferData.index == null) {\n      bufferData.index = this.gl.createBuffer();\n    }\n    this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, bufferData.index);\n    this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER, indexArray, this.gl.DYNAMIC_DRAW);\n    this.gl.blendFuncSeparate(SRC_COLOR, DST_COLOR, SRC_ALPHA, DST_ALPHA);\n  }\n  releaseShaderProgram() {\n    for (let i = 0; i < this._shaderSets.length; i++) {\n      this.gl.deleteProgram(this._shaderSets[i].shaderProgram);\n      this._shaderSets[i].shaderProgram = 0;\n    }\n    this._shaderSets = [];\n  }\n  generateShaders() {\n    for (let i = 0; i < shaderCount; i++) {\n      this._shaderSets.push({});\n    }\n    this._shaderSets[0].shaderProgram = this.loadShaderProgram(vertexShaderSrcSetupMask, fragmentShaderSrcsetupMask);\n    this._shaderSets[1].shaderProgram = this.loadShaderProgram(vertexShaderSrc, fragmentShaderSrcPremultipliedAlpha);\n    this._shaderSets[2].shaderProgram = this.loadShaderProgram(vertexShaderSrcMasked, fragmentShaderSrcMaskPremultipliedAlpha);\n    this._shaderSets[3].shaderProgram = this.loadShaderProgram(vertexShaderSrcMasked, fragmentShaderSrcMaskInvertedPremultipliedAlpha);\n    this._shaderSets[4].shaderProgram = this._shaderSets[1].shaderProgram;\n    this._shaderSets[5].shaderProgram = this._shaderSets[2].shaderProgram;\n    this._shaderSets[6].shaderProgram = this._shaderSets[3].shaderProgram;\n    this._shaderSets[7].shaderProgram = this._shaderSets[1].shaderProgram;\n    this._shaderSets[8].shaderProgram = this._shaderSets[2].shaderProgram;\n    this._shaderSets[9].shaderProgram = this._shaderSets[3].shaderProgram;\n    this._shaderSets[0].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[0].shaderProgram, \"a_position\");\n    this._shaderSets[0].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[0].shaderProgram, \"a_texCoord\");\n    this._shaderSets[0].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[0].shaderProgram, \"s_texture0\");\n    this._shaderSets[0].uniformClipMatrixLocation = this.gl.getUniformLocation(this._shaderSets[0].shaderProgram, \"u_clipMatrix\");\n    this._shaderSets[0].uniformChannelFlagLocation = this.gl.getUniformLocation(this._shaderSets[0].shaderProgram, \"u_channelFlag\");\n    this._shaderSets[0].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[0].shaderProgram, \"u_baseColor\");\n    this._shaderSets[1].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[1].shaderProgram, \"a_position\");\n    this._shaderSets[1].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[1].shaderProgram, \"a_texCoord\");\n    this._shaderSets[1].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[1].shaderProgram, \"s_texture0\");\n    this._shaderSets[1].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[1].shaderProgram, \"u_matrix\");\n    this._shaderSets[1].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[1].shaderProgram, \"u_baseColor\");\n    this._shaderSets[2].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[2].shaderProgram, \"a_position\");\n    this._shaderSets[2].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[2].shaderProgram, \"a_texCoord\");\n    this._shaderSets[2].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[2].shaderProgram, \"s_texture0\");\n    this._shaderSets[2].samplerTexture1Location = this.gl.getUniformLocation(this._shaderSets[2].shaderProgram, \"s_texture1\");\n    this._shaderSets[2].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[2].shaderProgram, \"u_matrix\");\n    this._shaderSets[2].uniformClipMatrixLocation = this.gl.getUniformLocation(this._shaderSets[2].shaderProgram, \"u_clipMatrix\");\n    this._shaderSets[2].uniformChannelFlagLocation = this.gl.getUniformLocation(this._shaderSets[2].shaderProgram, \"u_channelFlag\");\n    this._shaderSets[2].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[2].shaderProgram, \"u_baseColor\");\n    this._shaderSets[3].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[3].shaderProgram, \"a_position\");\n    this._shaderSets[3].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[3].shaderProgram, \"a_texCoord\");\n    this._shaderSets[3].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[3].shaderProgram, \"s_texture0\");\n    this._shaderSets[3].samplerTexture1Location = this.gl.getUniformLocation(this._shaderSets[3].shaderProgram, \"s_texture1\");\n    this._shaderSets[3].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[3].shaderProgram, \"u_matrix\");\n    this._shaderSets[3].uniformClipMatrixLocation = this.gl.getUniformLocation(this._shaderSets[3].shaderProgram, \"u_clipMatrix\");\n    this._shaderSets[3].uniformChannelFlagLocation = this.gl.getUniformLocation(this._shaderSets[3].shaderProgram, \"u_channelFlag\");\n    this._shaderSets[3].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[3].shaderProgram, \"u_baseColor\");\n    this._shaderSets[4].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[4].shaderProgram, \"a_position\");\n    this._shaderSets[4].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[4].shaderProgram, \"a_texCoord\");\n    this._shaderSets[4].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[4].shaderProgram, \"s_texture0\");\n    this._shaderSets[4].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[4].shaderProgram, \"u_matrix\");\n    this._shaderSets[4].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[4].shaderProgram, \"u_baseColor\");\n    this._shaderSets[5].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[5].shaderProgram, \"a_position\");\n    this._shaderSets[5].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[5].shaderProgram, \"a_texCoord\");\n    this._shaderSets[5].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[5].shaderProgram, \"s_texture0\");\n    this._shaderSets[5].samplerTexture1Location = this.gl.getUniformLocation(this._shaderSets[5].shaderProgram, \"s_texture1\");\n    this._shaderSets[5].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[5].shaderProgram, \"u_matrix\");\n    this._shaderSets[5].uniformClipMatrixLocation = this.gl.getUniformLocation(this._shaderSets[5].shaderProgram, \"u_clipMatrix\");\n    this._shaderSets[5].uniformChannelFlagLocation = this.gl.getUniformLocation(this._shaderSets[5].shaderProgram, \"u_channelFlag\");\n    this._shaderSets[5].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[5].shaderProgram, \"u_baseColor\");\n    this._shaderSets[6].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[6].shaderProgram, \"a_position\");\n    this._shaderSets[6].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[6].shaderProgram, \"a_texCoord\");\n    this._shaderSets[6].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[6].shaderProgram, \"s_texture0\");\n    this._shaderSets[6].samplerTexture1Location = this.gl.getUniformLocation(this._shaderSets[6].shaderProgram, \"s_texture1\");\n    this._shaderSets[6].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[6].shaderProgram, \"u_matrix\");\n    this._shaderSets[6].uniformClipMatrixLocation = this.gl.getUniformLocation(this._shaderSets[6].shaderProgram, \"u_clipMatrix\");\n    this._shaderSets[6].uniformChannelFlagLocation = this.gl.getUniformLocation(this._shaderSets[6].shaderProgram, \"u_channelFlag\");\n    this._shaderSets[6].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[6].shaderProgram, \"u_baseColor\");\n    this._shaderSets[7].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[7].shaderProgram, \"a_position\");\n    this._shaderSets[7].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[7].shaderProgram, \"a_texCoord\");\n    this._shaderSets[7].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[7].shaderProgram, \"s_texture0\");\n    this._shaderSets[7].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[7].shaderProgram, \"u_matrix\");\n    this._shaderSets[7].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[7].shaderProgram, \"u_baseColor\");\n    this._shaderSets[8].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[8].shaderProgram, \"a_position\");\n    this._shaderSets[8].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[8].shaderProgram, \"a_texCoord\");\n    this._shaderSets[8].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[8].shaderProgram, \"s_texture0\");\n    this._shaderSets[8].samplerTexture1Location = this.gl.getUniformLocation(this._shaderSets[8].shaderProgram, \"s_texture1\");\n    this._shaderSets[8].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[8].shaderProgram, \"u_matrix\");\n    this._shaderSets[8].uniformClipMatrixLocation = this.gl.getUniformLocation(this._shaderSets[8].shaderProgram, \"u_clipMatrix\");\n    this._shaderSets[8].uniformChannelFlagLocation = this.gl.getUniformLocation(this._shaderSets[8].shaderProgram, \"u_channelFlag\");\n    this._shaderSets[8].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[8].shaderProgram, \"u_baseColor\");\n    this._shaderSets[9].attributePositionLocation = this.gl.getAttribLocation(this._shaderSets[9].shaderProgram, \"a_position\");\n    this._shaderSets[9].attributeTexCoordLocation = this.gl.getAttribLocation(this._shaderSets[9].shaderProgram, \"a_texCoord\");\n    this._shaderSets[9].samplerTexture0Location = this.gl.getUniformLocation(this._shaderSets[9].shaderProgram, \"s_texture0\");\n    this._shaderSets[9].samplerTexture1Location = this.gl.getUniformLocation(this._shaderSets[9].shaderProgram, \"s_texture1\");\n    this._shaderSets[9].uniformMatrixLocation = this.gl.getUniformLocation(this._shaderSets[9].shaderProgram, \"u_matrix\");\n    this._shaderSets[9].uniformClipMatrixLocation = this.gl.getUniformLocation(this._shaderSets[9].shaderProgram, \"u_clipMatrix\");\n    this._shaderSets[9].uniformChannelFlagLocation = this.gl.getUniformLocation(this._shaderSets[9].shaderProgram, \"u_channelFlag\");\n    this._shaderSets[9].uniformBaseColorLocation = this.gl.getUniformLocation(this._shaderSets[9].shaderProgram, \"u_baseColor\");\n  }\n  loadShaderProgram(vertexShaderSource, fragmentShaderSource) {\n    let shaderProgram = this.gl.createProgram();\n    let vertShader = this.compileShaderSource(this.gl.VERTEX_SHADER, vertexShaderSource);\n    if (!vertShader) {\n      CubismLogError(\"Vertex shader compile error!\");\n      return 0;\n    }\n    let fragShader = this.compileShaderSource(this.gl.FRAGMENT_SHADER, fragmentShaderSource);\n    if (!fragShader) {\n      CubismLogError(\"Vertex shader compile error!\");\n      return 0;\n    }\n    this.gl.attachShader(shaderProgram, vertShader);\n    this.gl.attachShader(shaderProgram, fragShader);\n    this.gl.linkProgram(shaderProgram);\n    const linkStatus = this.gl.getProgramParameter(shaderProgram, this.gl.LINK_STATUS);\n    if (!linkStatus) {\n      CubismLogError(\"Failed to link program: {0}\", shaderProgram);\n      this.gl.deleteShader(vertShader);\n      this.gl.deleteShader(fragShader);\n      if (shaderProgram) {\n        this.gl.deleteProgram(shaderProgram);\n      }\n      return 0;\n    }\n    this.gl.deleteShader(vertShader);\n    this.gl.deleteShader(fragShader);\n    return shaderProgram;\n  }\n  compileShaderSource(shaderType, shaderSource) {\n    const source = shaderSource;\n    const shader = this.gl.createShader(shaderType);\n    this.gl.shaderSource(shader, source);\n    this.gl.compileShader(shader);\n    if (!shader) {\n      const log = this.gl.getShaderInfoLog(shader);\n      CubismLogError(\"Shader compile log: {0} \", log);\n    }\n    const status = this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS);\n    if (!status) {\n      this.gl.deleteShader(shader);\n      return null;\n    }\n    return shader;\n  }\n  setGl(gl) {\n    this.gl = gl;\n  }\n}\nvar ShaderNames = /* @__PURE__ */ ((ShaderNames2) => {\n  ShaderNames2[ShaderNames2[\"ShaderNames_SetupMask\"] = 0] = \"ShaderNames_SetupMask\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_NormalPremultipliedAlpha\"] = 1] = \"ShaderNames_NormalPremultipliedAlpha\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_NormalMaskedPremultipliedAlpha\"] = 2] = \"ShaderNames_NormalMaskedPremultipliedAlpha\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_NomralMaskedInvertedPremultipliedAlpha\"] = 3] = \"ShaderNames_NomralMaskedInvertedPremultipliedAlpha\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_AddPremultipliedAlpha\"] = 4] = \"ShaderNames_AddPremultipliedAlpha\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_AddMaskedPremultipliedAlpha\"] = 5] = \"ShaderNames_AddMaskedPremultipliedAlpha\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_AddMaskedPremultipliedAlphaInverted\"] = 6] = \"ShaderNames_AddMaskedPremultipliedAlphaInverted\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_MultPremultipliedAlpha\"] = 7] = \"ShaderNames_MultPremultipliedAlpha\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_MultMaskedPremultipliedAlpha\"] = 8] = \"ShaderNames_MultMaskedPremultipliedAlpha\";\n  ShaderNames2[ShaderNames2[\"ShaderNames_MultMaskedPremultipliedAlphaInverted\"] = 9] = \"ShaderNames_MultMaskedPremultipliedAlphaInverted\";\n  return ShaderNames2;\n})(ShaderNames || {});\nconst vertexShaderSrcSetupMask = \"attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;varying vec4       v_myPos;uniform mat4       u_clipMatrix;void main(){   gl_Position = u_clipMatrix * a_position;   v_myPos = u_clipMatrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}\";\nconst fragmentShaderSrcsetupMask = \"precision mediump float;varying vec2       v_texCoord;varying vec4       v_myPos;uniform vec4       u_baseColor;uniform vec4       u_channelFlag;uniform sampler2D  s_texture0;void main(){   float isInside =        step(u_baseColor.x, v_myPos.x/v_myPos.w)       * step(u_baseColor.y, v_myPos.y/v_myPos.w)       * step(v_myPos.x/v_myPos.w, u_baseColor.z)       * step(v_myPos.y/v_myPos.w, u_baseColor.w);   gl_FragColor = u_channelFlag * texture2D(s_texture0, v_texCoord).a * isInside;}\";\nconst vertexShaderSrc = \"attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;uniform mat4       u_matrix;void main(){   gl_Position = u_matrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}\";\nconst vertexShaderSrcMasked = \"attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;varying vec4       v_clipPos;uniform mat4       u_matrix;uniform mat4       u_clipMatrix;void main(){   gl_Position = u_matrix * a_position;   v_clipPos = u_clipMatrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}\";\nconst fragmentShaderSrcPremultipliedAlpha = \"precision mediump float;varying vec2       v_texCoord;uniform vec4       u_baseColor;uniform sampler2D  s_texture0;void main(){   gl_FragColor = texture2D(s_texture0 , v_texCoord) * u_baseColor;}\";\nconst fragmentShaderSrcMaskPremultipliedAlpha = \"precision mediump float;varying vec2       v_texCoord;varying vec4       v_clipPos;uniform vec4       u_baseColor;uniform vec4       u_channelFlag;uniform sampler2D  s_texture0;uniform sampler2D  s_texture1;void main(){   vec4 col_formask = texture2D(s_texture0 , v_texCoord) * u_baseColor;   vec4 clipMask = (1.0 - texture2D(s_texture1, v_clipPos.xy / v_clipPos.w)) * u_channelFlag;   float maskVal = clipMask.r + clipMask.g + clipMask.b + clipMask.a;   col_formask = col_formask * maskVal;   gl_FragColor = col_formask;}\";\nconst fragmentShaderSrcMaskInvertedPremultipliedAlpha = \"precision mediump float;varying vec2 v_texCoord;varying vec4 v_clipPos;uniform sampler2D s_texture0;uniform sampler2D s_texture1;uniform vec4 u_channelFlag;uniform vec4 u_baseColor;void main(){vec4 col_formask = texture2D(s_texture0, v_texCoord) * u_baseColor;vec4 clipMask = (1.0 - texture2D(s_texture1, v_clipPos.xy / v_clipPos.w)) * u_channelFlag;float maskVal = clipMask.r + clipMask.g + clipMask.b + clipMask.a;col_formask = col_formask * (1.0 - maskVal);gl_FragColor = col_formask;}\";\nclass CubismRenderer_WebGL extends CubismRenderer {\n  constructor() {\n    super();\n    this._clippingContextBufferForMask = null;\n    this._clippingContextBufferForDraw = null;\n    this._clippingManager = new CubismClippingManager_WebGL();\n    this.firstDraw = true;\n    this._textures = {};\n    this._sortedDrawableIndexList = [];\n    this._bufferData = {\n      vertex: null,\n      uv: null,\n      index: null\n    };\n  }\n  initialize(model) {\n    if (model.isUsingMasking()) {\n      this._clippingManager = new CubismClippingManager_WebGL();\n      this._clippingManager.initialize(model, model.getDrawableCount(), model.getDrawableMasks(), model.getDrawableMaskCounts());\n    }\n    for (let i = model.getDrawableCount() - 1; i >= 0; i--) {\n      this._sortedDrawableIndexList[i] = 0;\n    }\n    super.initialize(model);\n  }\n  bindTexture(modelTextureNo, glTexture) {\n    this._textures[modelTextureNo] = glTexture;\n  }\n  getBindedTextures() {\n    return this._textures;\n  }\n  setClippingMaskBufferSize(size) {\n    this._clippingManager.release();\n    this._clippingManager = new CubismClippingManager_WebGL();\n    this._clippingManager.setClippingMaskBufferSize(size);\n    this._clippingManager.initialize(this.getModel(), this.getModel().getDrawableCount(), this.getModel().getDrawableMasks(), this.getModel().getDrawableMaskCounts());\n  }\n  getClippingMaskBufferSize() {\n    return this._clippingManager.getClippingMaskBufferSize();\n  }\n  release() {\n    var _a, _b, _c;\n    const self = this;\n    this._clippingManager.release();\n    self._clippingManager = void 0;\n    (_a = this.gl) == null ? void 0 : _a.deleteBuffer(this._bufferData.vertex);\n    this._bufferData.vertex = null;\n    (_b = this.gl) == null ? void 0 : _b.deleteBuffer(this._bufferData.uv);\n    this._bufferData.uv = null;\n    (_c = this.gl) == null ? void 0 : _c.deleteBuffer(this._bufferData.index);\n    this._bufferData.index = null;\n    self._bufferData = void 0;\n    self._textures = void 0;\n  }\n  doDrawModel() {\n    this.preDraw();\n    if (this._clippingManager != null) {\n      this._clippingManager.setupClippingContext(this.getModel(), this);\n    }\n    const drawableCount = this.getModel().getDrawableCount();\n    const renderOrder = this.getModel().getDrawableRenderOrders();\n    for (let i = 0; i < drawableCount; ++i) {\n      const order = renderOrder[i];\n      this._sortedDrawableIndexList[order] = i;\n    }\n    for (let i = 0; i < drawableCount; ++i) {\n      const drawableIndex = this._sortedDrawableIndexList[i];\n      if (!this.getModel().getDrawableDynamicFlagIsVisible(drawableIndex)) {\n        continue;\n      }\n      this.setClippingContextBufferForDraw(this._clippingManager != null ? this._clippingManager.getClippingContextListForDraw()[drawableIndex] : null);\n      this.setIsCulling(this.getModel().getDrawableCulling(drawableIndex));\n      this.drawMesh(this.getModel().getDrawableTextureIndices(drawableIndex), this.getModel().getDrawableVertexIndexCount(drawableIndex), this.getModel().getDrawableVertexCount(drawableIndex), this.getModel().getDrawableVertexIndices(drawableIndex), this.getModel().getDrawableVertices(drawableIndex), this.getModel().getDrawableVertexUvs(drawableIndex), this.getModel().getDrawableOpacity(drawableIndex), this.getModel().getDrawableBlendMode(drawableIndex), this.getModel().getDrawableInvertedMaskBit(drawableIndex));\n    }\n  }\n  drawMesh(textureNo, indexCount, vertexCount, indexArray, vertexArray, uvArray, opacity, colorBlendMode, invertedMask) {\n    if (this.isCulling()) {\n      this.gl.enable(this.gl.CULL_FACE);\n    } else {\n      this.gl.disable(this.gl.CULL_FACE);\n    }\n    this.gl.frontFace(this.gl.CCW);\n    const modelColorRGBA = this.getModelColor();\n    if (this.getClippingContextBufferForMask() == null) {\n      modelColorRGBA.A *= opacity;\n      if (this.isPremultipliedAlpha()) {\n        modelColorRGBA.R *= modelColorRGBA.A;\n        modelColorRGBA.G *= modelColorRGBA.A;\n        modelColorRGBA.B *= modelColorRGBA.A;\n      }\n    }\n    let drawtexture = null;\n    if (this._textures[textureNo] != null) {\n      drawtexture = this._textures[textureNo];\n    }\n    CubismShader_WebGL.getInstance().setupShaderProgram(this, drawtexture, vertexCount, vertexArray, indexArray, uvArray, this._bufferData, opacity, colorBlendMode, modelColorRGBA, this.isPremultipliedAlpha(), this.getMvpMatrix(), invertedMask);\n    this.gl.drawElements(this.gl.TRIANGLES, indexCount, this.gl.UNSIGNED_SHORT, 0);\n    this.gl.useProgram(null);\n    this.setClippingContextBufferForDraw(null);\n    this.setClippingContextBufferForMask(null);\n  }\n  static doStaticRelease() {\n    CubismShader_WebGL.deleteInstance();\n  }\n  setRenderState(fbo, viewport) {\n    s_fbo = fbo;\n    s_viewport = viewport;\n  }\n  preDraw() {\n    if (this.firstDraw) {\n      this.firstDraw = false;\n      this._anisortopy = this.gl.getExtension(\"EXT_texture_filter_anisotropic\") || this.gl.getExtension(\"WEBKIT_EXT_texture_filter_anisotropic\") || this.gl.getExtension(\"MOZ_EXT_texture_filter_anisotropic\");\n    }\n    this.gl.disable(this.gl.SCISSOR_TEST);\n    this.gl.disable(this.gl.STENCIL_TEST);\n    this.gl.disable(this.gl.DEPTH_TEST);\n    this.gl.frontFace(this.gl.CW);\n    this.gl.enable(this.gl.BLEND);\n    this.gl.colorMask(true, true, true, true);\n    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, null);\n    this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, null);\n  }\n  setClippingContextBufferForMask(clip) {\n    this._clippingContextBufferForMask = clip;\n  }\n  getClippingContextBufferForMask() {\n    return this._clippingContextBufferForMask;\n  }\n  setClippingContextBufferForDraw(clip) {\n    this._clippingContextBufferForDraw = clip;\n  }\n  getClippingContextBufferForDraw() {\n    return this._clippingContextBufferForDraw;\n  }\n  startUp(gl) {\n    this.gl = gl;\n    this._clippingManager.setGL(gl);\n    CubismShader_WebGL.getInstance().setGl(gl);\n  }\n}\nCubismRenderer.staticRelease = () => {\n  CubismRenderer_WebGL.doStaticRelease();\n};\nconst tempMatrix = new CubismMatrix44();\nclass Cubism4InternalModel extends InternalModel {\n  constructor(coreModel, settings, options) {\n    super();\n    this.lipSync = true;\n    this.breath = CubismBreath.create();\n    this.renderer = new CubismRenderer_WebGL();\n    this.idParamAngleX = ParamAngleX;\n    this.idParamAngleY = ParamAngleY;\n    this.idParamAngleZ = ParamAngleZ;\n    this.idParamEyeBallX = ParamEyeBallX;\n    this.idParamEyeBallY = ParamEyeBallY;\n    this.idParamBodyAngleX = ParamBodyAngleX;\n    this.idParamBreath = ParamBreath;\n    this.pixelsPerUnit = 1;\n    this.centeringTransform = new Matrix();\n    this.coreModel = coreModel;\n    this.settings = settings;\n    this.motionManager = new Cubism4MotionManager(settings, options);\n    this.init();\n  }\n  init() {\n    var _a;\n    super.init();\n    if (((_a = this.settings.getEyeBlinkParameters()) == null ? void 0 : _a.length) > 0) {\n      this.eyeBlink = CubismEyeBlink.create(this.settings);\n    }\n    this.breath.setParameters([\n      new BreathParameterData(this.idParamAngleX, 0, 15, 6.5345, 0.5),\n      new BreathParameterData(this.idParamAngleY, 0, 8, 3.5345, 0.5),\n      new BreathParameterData(this.idParamAngleZ, 0, 10, 5.5345, 0.5),\n      new BreathParameterData(this.idParamBodyAngleX, 0, 4, 15.5345, 0.5),\n      new BreathParameterData(this.idParamBreath, 0, 0.5, 3.2345, 0.5)\n    ]);\n    this.renderer.initialize(this.coreModel);\n    this.renderer.setIsPremultipliedAlpha(true);\n  }\n  getSize() {\n    return [this.coreModel.getModel().canvasinfo.CanvasWidth, this.coreModel.getModel().canvasinfo.CanvasHeight];\n  }\n  getLayout() {\n    const layout = {};\n    if (this.settings.layout) {\n      for (const key of Object.keys(this.settings.layout)) {\n        const commonKey = key.charAt(0).toLowerCase() + key.slice(1);\n        layout[commonKey] = this.settings.layout[key];\n      }\n    }\n    return layout;\n  }\n  setupLayout() {\n    super.setupLayout();\n    this.pixelsPerUnit = this.coreModel.getModel().canvasinfo.PixelsPerUnit;\n    this.centeringTransform.scale(this.pixelsPerUnit, this.pixelsPerUnit).translate(this.originalWidth / 2, this.originalHeight / 2);\n  }\n  updateWebGLContext(gl, glContextID) {\n    this.renderer.firstDraw = true;\n    this.renderer._bufferData = {\n      vertex: null,\n      uv: null,\n      index: null\n    };\n    this.renderer.startUp(gl);\n    this.renderer._clippingManager._currentFrameNo = glContextID;\n    this.renderer._clippingManager._maskTexture = void 0;\n    CubismShader_WebGL.getInstance()._shaderSets = [];\n  }\n  bindTexture(index, texture) {\n    this.renderer.bindTexture(index, texture);\n  }\n  getHitAreaDefs() {\n    var _a, _b;\n    return (_b = (_a = this.settings.hitAreas) == null ? void 0 : _a.map((hitArea) => ({\n      id: hitArea.Id,\n      name: hitArea.Name,\n      index: this.coreModel.getDrawableIndex(hitArea.Id)\n    }))) != null ? _b : [];\n  }\n  getDrawableIDs() {\n    return this.coreModel.getDrawableIds();\n  }\n  getDrawableIndex(id) {\n    return this.coreModel.getDrawableIndex(id);\n  }\n  getDrawableVertices(drawIndex) {\n    if (typeof drawIndex === \"string\") {\n      drawIndex = this.coreModel.getDrawableIndex(drawIndex);\n      if (drawIndex === -1)\n        throw new TypeError(\"Unable to find drawable ID: \" + drawIndex);\n    }\n    const arr = this.coreModel.getDrawableVertices(drawIndex).slice();\n    for (let i = 0; i < arr.length; i += 2) {\n      arr[i] = arr[i] * this.pixelsPerUnit + this.originalWidth / 2;\n      arr[i + 1] = -arr[i + 1] * this.pixelsPerUnit + this.originalHeight / 2;\n    }\n    return arr;\n  }\n  updateTransform(transform) {\n    this.drawingMatrix.copyFrom(this.centeringTransform).prepend(this.localTransform).prepend(transform);\n  }\n  update(dt, now) {\n    var _a, _b, _c, _d;\n    super.update(dt, now);\n    dt /= 1e3;\n    now /= 1e3;\n    const model = this.coreModel;\n    this.emit(\"beforeMotionUpdate\");\n    const motionUpdated = this.motionManager.update(this.coreModel, now);\n    this.emit(\"afterMotionUpdate\");\n    model.saveParameters();\n    (_a = this.motionManager.expressionManager) == null ? void 0 : _a.update(model, now);\n    if (!motionUpdated) {\n      (_b = this.eyeBlink) == null ? void 0 : _b.updateParameters(model, dt);\n    }\n    this.updateFocus();\n    this.updateNaturalMovements(dt * 1e3, now * 1e3);\n    (_c = this.physics) == null ? void 0 : _c.evaluate(model, dt);\n    (_d = this.pose) == null ? void 0 : _d.updateParameters(model, dt);\n    this.emit(\"beforeModelUpdate\");\n    model.update();\n    model.loadParameters();\n  }\n  updateFocus() {\n    this.coreModel.addParameterValueById(this.idParamEyeBallX, this.focusController.x);\n    this.coreModel.addParameterValueById(this.idParamEyeBallY, this.focusController.y);\n    this.coreModel.addParameterValueById(this.idParamAngleX, this.focusController.x * 30);\n    this.coreModel.addParameterValueById(this.idParamAngleY, this.focusController.y * 30);\n    this.coreModel.addParameterValueById(this.idParamAngleZ, this.focusController.x * this.focusController.y * -30);\n    this.coreModel.addParameterValueById(this.idParamBodyAngleX, this.focusController.x * 10);\n  }\n  updateNaturalMovements(dt, now) {\n    var _a;\n    (_a = this.breath) == null ? void 0 : _a.updateParameters(this.coreModel, dt / 1e3);\n  }\n  draw(gl) {\n    const matrix = this.drawingMatrix;\n    const array = tempMatrix.getArray();\n    array[0] = matrix.a;\n    array[1] = matrix.b;\n    array[4] = -matrix.c;\n    array[5] = -matrix.d;\n    array[12] = matrix.tx;\n    array[13] = matrix.ty;\n    this.renderer.setMvpMatrix(tempMatrix);\n    this.renderer.setRenderState(gl.getParameter(gl.FRAMEBUFFER_BINDING), this.viewport);\n    this.renderer.drawModel();\n  }\n  destroy() {\n    super.destroy();\n    this.renderer.release();\n    this.coreModel.release();\n    this.renderer = void 0;\n    this.coreModel = void 0;\n  }\n}\nlet startupPromise;\nlet startupRetries = 20;\nfunction cubism4Ready() {\n  if (CubismFramework.isStarted()) {\n    return Promise.resolve();\n  }\n  startupPromise != null ? startupPromise : startupPromise = new Promise((resolve, reject) => {\n    function startUpWithRetry() {\n      try {\n        startUpCubism4();\n        resolve();\n      } catch (e) {\n        startupRetries--;\n        if (startupRetries < 0) {\n          const err = new Error(\"Failed to start up Cubism 4 framework.\");\n          err.cause = e;\n          reject(err);\n          return;\n        }\n        logger.log(\"Cubism4\", \"Startup failed, retrying 10ms later...\");\n        setTimeout(startUpWithRetry, 10);\n      }\n    }\n    startUpWithRetry();\n  });\n  return startupPromise;\n}\nfunction startUpCubism4(options) {\n  options = Object.assign({\n    logFunction: console.log,\n    loggingLevel: LogLevel.LogLevel_Verbose\n  }, options);\n  CubismFramework.startUp(options);\n  CubismFramework.initialize();\n}\nconst Epsilon = 1e-3;\nconst DefaultFadeInSeconds = 0.5;\nclass CubismPose {\n  static create(pose3json) {\n    const ret = new CubismPose();\n    if (typeof pose3json.FadeInTime === \"number\") {\n      ret._fadeTimeSeconds = pose3json.FadeInTime;\n      if (ret._fadeTimeSeconds <= 0) {\n        ret._fadeTimeSeconds = DefaultFadeInSeconds;\n      }\n    }\n    const poseListInfo = pose3json.Groups;\n    const poseCount = poseListInfo.length;\n    for (let poseIndex = 0; poseIndex < poseCount; ++poseIndex) {\n      const idListInfo = poseListInfo[poseIndex];\n      const idCount = idListInfo.length;\n      let groupCount = 0;\n      for (let groupIndex = 0; groupIndex < idCount; ++groupIndex) {\n        const partInfo = idListInfo[groupIndex];\n        const partData = new PartData();\n        partData.partId = partInfo.Id;\n        const linkListInfo = partInfo.Link;\n        if (linkListInfo) {\n          const linkCount = linkListInfo.length;\n          for (let linkIndex = 0; linkIndex < linkCount; ++linkIndex) {\n            const linkPart = new PartData();\n            linkPart.partId = linkListInfo[linkIndex];\n            partData.link.push(linkPart);\n          }\n        }\n        ret._partGroups.push(partData);\n        ++groupCount;\n      }\n      ret._partGroupCounts.push(groupCount);\n    }\n    return ret;\n  }\n  updateParameters(model, deltaTimeSeconds) {\n    if (model != this._lastModel) {\n      this.reset(model);\n    }\n    this._lastModel = model;\n    if (deltaTimeSeconds < 0) {\n      deltaTimeSeconds = 0;\n    }\n    let beginIndex = 0;\n    for (let i = 0; i < this._partGroupCounts.length; i++) {\n      const partGroupCount = this._partGroupCounts[i];\n      this.doFade(model, deltaTimeSeconds, beginIndex, partGroupCount);\n      beginIndex += partGroupCount;\n    }\n    this.copyPartOpacities(model);\n  }\n  reset(model) {\n    let beginIndex = 0;\n    for (let i = 0; i < this._partGroupCounts.length; ++i) {\n      const groupCount = this._partGroupCounts[i];\n      for (let j = beginIndex; j < beginIndex + groupCount; ++j) {\n        this._partGroups[j].initialize(model);\n        const partsIndex = this._partGroups[j].partIndex;\n        const paramIndex = this._partGroups[j].parameterIndex;\n        if (partsIndex < 0) {\n          continue;\n        }\n        model.setPartOpacityByIndex(partsIndex, j == beginIndex ? 1 : 0);\n        model.setParameterValueByIndex(paramIndex, j == beginIndex ? 1 : 0);\n        for (let k = 0; k < this._partGroups[j].link.length; ++k) {\n          this._partGroups[j].link[k].initialize(model);\n        }\n      }\n      beginIndex += groupCount;\n    }\n  }\n  copyPartOpacities(model) {\n    for (let groupIndex = 0; groupIndex < this._partGroups.length; ++groupIndex) {\n      const partData = this._partGroups[groupIndex];\n      if (partData.link.length == 0) {\n        continue;\n      }\n      const partIndex = this._partGroups[groupIndex].partIndex;\n      const opacity = model.getPartOpacityByIndex(partIndex);\n      for (let linkIndex = 0; linkIndex < partData.link.length; ++linkIndex) {\n        const linkPart = partData.link[linkIndex];\n        const linkPartIndex = linkPart.partIndex;\n        if (linkPartIndex < 0) {\n          continue;\n        }\n        model.setPartOpacityByIndex(linkPartIndex, opacity);\n      }\n    }\n  }\n  doFade(model, deltaTimeSeconds, beginIndex, partGroupCount) {\n    let visiblePartIndex = -1;\n    let newOpacity = 1;\n    const phi = 0.5;\n    const backOpacityThreshold = 0.15;\n    for (let i = beginIndex; i < beginIndex + partGroupCount; ++i) {\n      const partIndex = this._partGroups[i].partIndex;\n      const paramIndex = this._partGroups[i].parameterIndex;\n      if (model.getParameterValueByIndex(paramIndex) > Epsilon) {\n        if (visiblePartIndex >= 0) {\n          break;\n        }\n        visiblePartIndex = i;\n        newOpacity = model.getPartOpacityByIndex(partIndex);\n        newOpacity += deltaTimeSeconds / this._fadeTimeSeconds;\n        if (newOpacity > 1) {\n          newOpacity = 1;\n        }\n      }\n    }\n    if (visiblePartIndex < 0) {\n      visiblePartIndex = 0;\n      newOpacity = 1;\n    }\n    for (let i = beginIndex; i < beginIndex + partGroupCount; ++i) {\n      const partsIndex = this._partGroups[i].partIndex;\n      if (visiblePartIndex == i) {\n        model.setPartOpacityByIndex(partsIndex, newOpacity);\n      } else {\n        let opacity = model.getPartOpacityByIndex(partsIndex);\n        let a1;\n        if (newOpacity < phi) {\n          a1 = newOpacity * (phi - 1) / phi + 1;\n        } else {\n          a1 = (1 - newOpacity) * phi / (1 - phi);\n        }\n        const backOpacity = (1 - a1) * (1 - newOpacity);\n        if (backOpacity > backOpacityThreshold) {\n          a1 = 1 - backOpacityThreshold / (1 - newOpacity);\n        }\n        if (opacity > a1) {\n          opacity = a1;\n        }\n        model.setPartOpacityByIndex(partsIndex, opacity);\n      }\n    }\n  }\n  constructor() {\n    this._fadeTimeSeconds = DefaultFadeInSeconds;\n    this._lastModel = void 0;\n    this._partGroups = [];\n    this._partGroupCounts = [];\n  }\n}\nclass PartData {\n  constructor(v) {\n    this.parameterIndex = 0;\n    this.partIndex = 0;\n    this.partId = \"\";\n    this.link = [];\n    if (v != void 0) {\n      this.assignment(v);\n    }\n  }\n  assignment(v) {\n    this.partId = v.partId;\n    this.link = v.link.map((link) => link.clone());\n    return this;\n  }\n  initialize(model) {\n    this.parameterIndex = model.getParameterIndex(this.partId);\n    this.partIndex = model.getPartIndex(this.partId);\n    model.setParameterValueByIndex(this.parameterIndex, 1);\n  }\n  clone() {\n    const clonePartData = new PartData();\n    clonePartData.partId = this.partId;\n    clonePartData.parameterIndex = this.parameterIndex;\n    clonePartData.partIndex = this.partIndex;\n    clonePartData.link = this.link.map((link) => link.clone());\n    return clonePartData;\n  }\n}\nclass CubismModel {\n  update() {\n    this._model.update();\n    this._model.drawables.resetDynamicFlags();\n  }\n  getCanvasWidth() {\n    if (this._model == null) {\n      return 0;\n    }\n    return this._model.canvasinfo.CanvasWidth / this._model.canvasinfo.PixelsPerUnit;\n  }\n  getCanvasHeight() {\n    if (this._model == null) {\n      return 0;\n    }\n    return this._model.canvasinfo.CanvasHeight / this._model.canvasinfo.PixelsPerUnit;\n  }\n  saveParameters() {\n    const parameterCount = this._model.parameters.count;\n    const savedParameterCount = this._savedParameters.length;\n    for (let i = 0; i < parameterCount; ++i) {\n      if (i < savedParameterCount) {\n        this._savedParameters[i] = this._parameterValues[i];\n      } else {\n        this._savedParameters.push(this._parameterValues[i]);\n      }\n    }\n  }\n  getModel() {\n    return this._model;\n  }\n  getPartIndex(partId) {\n    let partIndex;\n    const partCount = this._model.parts.count;\n    for (partIndex = 0; partIndex < partCount; ++partIndex) {\n      if (partId == this._partIds[partIndex]) {\n        return partIndex;\n      }\n    }\n    if (partId in this._notExistPartId) {\n      return this._notExistPartId[partId];\n    }\n    partIndex = partCount + this._notExistPartId.length;\n    this._notExistPartId[partId] = partIndex;\n    this._notExistPartOpacities[partIndex] = 0;\n    return partIndex;\n  }\n  getPartCount() {\n    return this._model.parts.count;\n  }\n  setPartOpacityByIndex(partIndex, opacity) {\n    if (partIndex in this._notExistPartOpacities) {\n      this._notExistPartOpacities[partIndex] = opacity;\n      return;\n    }\n    CSM_ASSERT(0 <= partIndex && partIndex < this.getPartCount());\n    this._partOpacities[partIndex] = opacity;\n  }\n  setPartOpacityById(partId, opacity) {\n    const index = this.getPartIndex(partId);\n    if (index < 0) {\n      return;\n    }\n    this.setPartOpacityByIndex(index, opacity);\n  }\n  getPartOpacityByIndex(partIndex) {\n    if (partIndex in this._notExistPartOpacities) {\n      return this._notExistPartOpacities[partIndex];\n    }\n    CSM_ASSERT(0 <= partIndex && partIndex < this.getPartCount());\n    return this._partOpacities[partIndex];\n  }\n  getPartOpacityById(partId) {\n    const index = this.getPartIndex(partId);\n    if (index < 0) {\n      return 0;\n    }\n    return this.getPartOpacityByIndex(index);\n  }\n  getParameterIndex(parameterId) {\n    let parameterIndex;\n    const idCount = this._model.parameters.count;\n    for (parameterIndex = 0; parameterIndex < idCount; ++parameterIndex) {\n      if (parameterId != this._parameterIds[parameterIndex]) {\n        continue;\n      }\n      return parameterIndex;\n    }\n    if (parameterId in this._notExistParameterId) {\n      return this._notExistParameterId[parameterId];\n    }\n    parameterIndex = this._model.parameters.count + Object.keys(this._notExistParameterId).length;\n    this._notExistParameterId[parameterId] = parameterIndex;\n    this._notExistParameterValues[parameterIndex] = 0;\n    return parameterIndex;\n  }\n  getParameterCount() {\n    return this._model.parameters.count;\n  }\n  getParameterMaximumValue(parameterIndex) {\n    return this._model.parameters.maximumValues[parameterIndex];\n  }\n  getParameterMinimumValue(parameterIndex) {\n    return this._model.parameters.minimumValues[parameterIndex];\n  }\n  getParameterDefaultValue(parameterIndex) {\n    return this._model.parameters.defaultValues[parameterIndex];\n  }\n  getParameterValueByIndex(parameterIndex) {\n    if (parameterIndex in this._notExistParameterValues) {\n      return this._notExistParameterValues[parameterIndex];\n    }\n    CSM_ASSERT(0 <= parameterIndex && parameterIndex < this.getParameterCount());\n    return this._parameterValues[parameterIndex];\n  }\n  getParameterValueById(parameterId) {\n    const parameterIndex = this.getParameterIndex(parameterId);\n    return this.getParameterValueByIndex(parameterIndex);\n  }\n  setParameterValueByIndex(parameterIndex, value, weight = 1) {\n    if (parameterIndex in this._notExistParameterValues) {\n      this._notExistParameterValues[parameterIndex] = weight == 1 ? value : this._notExistParameterValues[parameterIndex] * (1 - weight) + value * weight;\n      return;\n    }\n    CSM_ASSERT(0 <= parameterIndex && parameterIndex < this.getParameterCount());\n    if (this._model.parameters.maximumValues[parameterIndex] < value) {\n      value = this._model.parameters.maximumValues[parameterIndex];\n    }\n    if (this._model.parameters.minimumValues[parameterIndex] > value) {\n      value = this._model.parameters.minimumValues[parameterIndex];\n    }\n    this._parameterValues[parameterIndex] = weight == 1 ? value : this._parameterValues[parameterIndex] = this._parameterValues[parameterIndex] * (1 - weight) + value * weight;\n  }\n  setParameterValueById(parameterId, value, weight = 1) {\n    const index = this.getParameterIndex(parameterId);\n    this.setParameterValueByIndex(index, value, weight);\n  }\n  addParameterValueByIndex(parameterIndex, value, weight = 1) {\n    this.setParameterValueByIndex(parameterIndex, this.getParameterValueByIndex(parameterIndex) + value * weight);\n  }\n  addParameterValueById(parameterId, value, weight = 1) {\n    const index = this.getParameterIndex(parameterId);\n    this.addParameterValueByIndex(index, value, weight);\n  }\n  multiplyParameterValueById(parameterId, value, weight = 1) {\n    const index = this.getParameterIndex(parameterId);\n    this.multiplyParameterValueByIndex(index, value, weight);\n  }\n  multiplyParameterValueByIndex(parameterIndex, value, weight = 1) {\n    this.setParameterValueByIndex(parameterIndex, this.getParameterValueByIndex(parameterIndex) * (1 + (value - 1) * weight));\n  }\n  getDrawableIds() {\n    return this._drawableIds.slice();\n  }\n  getDrawableIndex(drawableId) {\n    const drawableCount = this._model.drawables.count;\n    for (let drawableIndex = 0; drawableIndex < drawableCount; ++drawableIndex) {\n      if (this._drawableIds[drawableIndex] == drawableId) {\n        return drawableIndex;\n      }\n    }\n    return -1;\n  }\n  getDrawableCount() {\n    return this._model.drawables.count;\n  }\n  getDrawableId(drawableIndex) {\n    return this._model.drawables.ids[drawableIndex];\n  }\n  getDrawableRenderOrders() {\n    return this._model.drawables.renderOrders;\n  }\n  getDrawableTextureIndices(drawableIndex) {\n    return this._model.drawables.textureIndices[drawableIndex];\n  }\n  getDrawableDynamicFlagVertexPositionsDidChange(drawableIndex) {\n    const dynamicFlags = this._model.drawables.dynamicFlags;\n    return Live2DCubismCore.Utils.hasVertexPositionsDidChangeBit(dynamicFlags[drawableIndex]);\n  }\n  getDrawableVertexIndexCount(drawableIndex) {\n    return this._model.drawables.indexCounts[drawableIndex];\n  }\n  getDrawableVertexCount(drawableIndex) {\n    return this._model.drawables.vertexCounts[drawableIndex];\n  }\n  getDrawableVertices(drawableIndex) {\n    return this.getDrawableVertexPositions(drawableIndex);\n  }\n  getDrawableVertexIndices(drawableIndex) {\n    return this._model.drawables.indices[drawableIndex];\n  }\n  getDrawableVertexPositions(drawableIndex) {\n    return this._model.drawables.vertexPositions[drawableIndex];\n  }\n  getDrawableVertexUvs(drawableIndex) {\n    return this._model.drawables.vertexUvs[drawableIndex];\n  }\n  getDrawableOpacity(drawableIndex) {\n    return this._model.drawables.opacities[drawableIndex];\n  }\n  getDrawableCulling(drawableIndex) {\n    const constantFlags = this._model.drawables.constantFlags;\n    return !Live2DCubismCore.Utils.hasIsDoubleSidedBit(constantFlags[drawableIndex]);\n  }\n  getDrawableBlendMode(drawableIndex) {\n    const constantFlags = this._model.drawables.constantFlags;\n    return Live2DCubismCore.Utils.hasBlendAdditiveBit(constantFlags[drawableIndex]) ? CubismBlendMode.CubismBlendMode_Additive : Live2DCubismCore.Utils.hasBlendMultiplicativeBit(constantFlags[drawableIndex]) ? CubismBlendMode.CubismBlendMode_Multiplicative : CubismBlendMode.CubismBlendMode_Normal;\n  }\n  getDrawableInvertedMaskBit(drawableIndex) {\n    const constantFlags = this._model.drawables.constantFlags;\n    return Live2DCubismCore.Utils.hasIsInvertedMaskBit(constantFlags[drawableIndex]);\n  }\n  getDrawableMasks() {\n    return this._model.drawables.masks;\n  }\n  getDrawableMaskCounts() {\n    return this._model.drawables.maskCounts;\n  }\n  isUsingMasking() {\n    for (let d = 0; d < this._model.drawables.count; ++d) {\n      if (this._model.drawables.maskCounts[d] <= 0) {\n        continue;\n      }\n      return true;\n    }\n    return false;\n  }\n  getDrawableDynamicFlagIsVisible(drawableIndex) {\n    const dynamicFlags = this._model.drawables.dynamicFlags;\n    return Live2DCubismCore.Utils.hasIsVisibleBit(dynamicFlags[drawableIndex]);\n  }\n  getDrawableDynamicFlagVisibilityDidChange(drawableIndex) {\n    const dynamicFlags = this._model.drawables.dynamicFlags;\n    return Live2DCubismCore.Utils.hasVisibilityDidChangeBit(dynamicFlags[drawableIndex]);\n  }\n  getDrawableDynamicFlagOpacityDidChange(drawableIndex) {\n    const dynamicFlags = this._model.drawables.dynamicFlags;\n    return Live2DCubismCore.Utils.hasOpacityDidChangeBit(dynamicFlags[drawableIndex]);\n  }\n  getDrawableDynamicFlagRenderOrderDidChange(drawableIndex) {\n    const dynamicFlags = this._model.drawables.dynamicFlags;\n    return Live2DCubismCore.Utils.hasRenderOrderDidChangeBit(dynamicFlags[drawableIndex]);\n  }\n  loadParameters() {\n    let parameterCount = this._model.parameters.count;\n    const savedParameterCount = this._savedParameters.length;\n    if (parameterCount > savedParameterCount) {\n      parameterCount = savedParameterCount;\n    }\n    for (let i = 0; i < parameterCount; ++i) {\n      this._parameterValues[i] = this._savedParameters[i];\n    }\n  }\n  initialize() {\n    this._parameterValues = this._model.parameters.values;\n    this._partOpacities = this._model.parts.opacities;\n    this._parameterMaximumValues = this._model.parameters.maximumValues;\n    this._parameterMinimumValues = this._model.parameters.minimumValues;\n    {\n      const parameterIds = this._model.parameters.ids;\n      const parameterCount = this._model.parameters.count;\n      for (let i = 0; i < parameterCount; ++i) {\n        this._parameterIds.push(parameterIds[i]);\n      }\n    }\n    {\n      const partIds = this._model.parts.ids;\n      const partCount = this._model.parts.count;\n      for (let i = 0; i < partCount; ++i) {\n        this._partIds.push(partIds[i]);\n      }\n    }\n    {\n      const drawableIds = this._model.drawables.ids;\n      const drawableCount = this._model.drawables.count;\n      for (let i = 0; i < drawableCount; ++i) {\n        this._drawableIds.push(drawableIds[i]);\n      }\n    }\n  }\n  constructor(model) {\n    this._model = model;\n    this._savedParameters = [];\n    this._parameterIds = [];\n    this._drawableIds = [];\n    this._partIds = [];\n    this._notExistPartId = {};\n    this._notExistParameterId = {};\n    this._notExistParameterValues = {};\n    this._notExistPartOpacities = {};\n    this.initialize();\n  }\n  release() {\n    this._model.release();\n    this._model = void 0;\n  }\n}\nclass CubismMoc {\n  static create(mocBytes) {\n    const moc = Live2DCubismCore.Moc.fromArrayBuffer(mocBytes);\n    if (moc) {\n      return new CubismMoc(moc);\n    }\n    throw new Error(\"Unknown error\");\n  }\n  createModel() {\n    let cubismModel;\n    const model = Live2DCubismCore.Model.fromMoc(this._moc);\n    if (model) {\n      cubismModel = new CubismModel(model);\n      ++this._modelCount;\n      return cubismModel;\n    }\n    throw new Error(\"Unknown error\");\n  }\n  deleteModel(model) {\n    if (model != null) {\n      --this._modelCount;\n    }\n  }\n  constructor(moc) {\n    this._moc = moc;\n    this._modelCount = 0;\n  }\n  release() {\n    this._moc._release();\n    this._moc = void 0;\n  }\n}\nvar CubismPhysicsTargetType = /* @__PURE__ */ ((CubismPhysicsTargetType2) => {\n  CubismPhysicsTargetType2[CubismPhysicsTargetType2[\"CubismPhysicsTargetType_Parameter\"] = 0] = \"CubismPhysicsTargetType_Parameter\";\n  return CubismPhysicsTargetType2;\n})(CubismPhysicsTargetType || {});\nvar CubismPhysicsSource = /* @__PURE__ */ ((CubismPhysicsSource2) => {\n  CubismPhysicsSource2[CubismPhysicsSource2[\"CubismPhysicsSource_X\"] = 0] = \"CubismPhysicsSource_X\";\n  CubismPhysicsSource2[CubismPhysicsSource2[\"CubismPhysicsSource_Y\"] = 1] = \"CubismPhysicsSource_Y\";\n  CubismPhysicsSource2[CubismPhysicsSource2[\"CubismPhysicsSource_Angle\"] = 2] = \"CubismPhysicsSource_Angle\";\n  return CubismPhysicsSource2;\n})(CubismPhysicsSource || {});\nclass CubismPhysicsParticle {\n  constructor() {\n    this.initialPosition = new CubismVector2(0, 0);\n    this.position = new CubismVector2(0, 0);\n    this.lastPosition = new CubismVector2(0, 0);\n    this.lastGravity = new CubismVector2(0, 0);\n    this.force = new CubismVector2(0, 0);\n    this.velocity = new CubismVector2(0, 0);\n  }\n}\nclass CubismPhysicsSubRig {\n  constructor() {\n    this.normalizationPosition = {};\n    this.normalizationAngle = {};\n  }\n}\nclass CubismPhysicsInput {\n  constructor() {\n    this.source = {};\n  }\n}\nclass CubismPhysicsOutput {\n  constructor() {\n    this.destination = {};\n    this.translationScale = new CubismVector2(0, 0);\n  }\n}\nclass CubismPhysicsRig {\n  constructor() {\n    this.settings = [];\n    this.inputs = [];\n    this.outputs = [];\n    this.particles = [];\n    this.gravity = new CubismVector2(0, 0);\n    this.wind = new CubismVector2(0, 0);\n  }\n}\nclass CubismPhysicsJson {\n  constructor(json) {\n    this._json = json;\n  }\n  release() {\n    this._json = void 0;\n  }\n  getGravity() {\n    const ret = new CubismVector2(0, 0);\n    ret.x = this._json.Meta.EffectiveForces.Gravity.X;\n    ret.y = this._json.Meta.EffectiveForces.Gravity.Y;\n    return ret;\n  }\n  getWind() {\n    const ret = new CubismVector2(0, 0);\n    ret.x = this._json.Meta.EffectiveForces.Wind.X;\n    ret.y = this._json.Meta.EffectiveForces.Wind.Y;\n    return ret;\n  }\n  getSubRigCount() {\n    return this._json.Meta.PhysicsSettingCount;\n  }\n  getTotalInputCount() {\n    return this._json.Meta.TotalInputCount;\n  }\n  getTotalOutputCount() {\n    return this._json.Meta.TotalOutputCount;\n  }\n  getVertexCount() {\n    return this._json.Meta.VertexCount;\n  }\n  getNormalizationPositionMinimumValue(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Normalization.Position.Minimum;\n  }\n  getNormalizationPositionMaximumValue(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Normalization.Position.Maximum;\n  }\n  getNormalizationPositionDefaultValue(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Normalization.Position.Default;\n  }\n  getNormalizationAngleMinimumValue(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Normalization.Angle.Minimum;\n  }\n  getNormalizationAngleMaximumValue(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Normalization.Angle.Maximum;\n  }\n  getNormalizationAngleDefaultValue(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Normalization.Angle.Default;\n  }\n  getInputCount(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Input.length;\n  }\n  getInputWeight(physicsSettingIndex, inputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Input[inputIndex].Weight;\n  }\n  getInputReflect(physicsSettingIndex, inputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Input[inputIndex].Reflect;\n  }\n  getInputType(physicsSettingIndex, inputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Input[inputIndex].Type;\n  }\n  getInputSourceId(physicsSettingIndex, inputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Input[inputIndex].Source.Id;\n  }\n  getOutputCount(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Output.length;\n  }\n  getOutputVertexIndex(physicsSettingIndex, outputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Output[outputIndex].VertexIndex;\n  }\n  getOutputAngleScale(physicsSettingIndex, outputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Output[outputIndex].Scale;\n  }\n  getOutputWeight(physicsSettingIndex, outputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Output[outputIndex].Weight;\n  }\n  getOutputDestinationId(physicsSettingIndex, outputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Output[outputIndex].Destination.Id;\n  }\n  getOutputType(physicsSettingIndex, outputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Output[outputIndex].Type;\n  }\n  getOutputReflect(physicsSettingIndex, outputIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Output[outputIndex].Reflect;\n  }\n  getParticleCount(physicsSettingIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Vertices.length;\n  }\n  getParticleMobility(physicsSettingIndex, vertexIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Vertices[vertexIndex].Mobility;\n  }\n  getParticleDelay(physicsSettingIndex, vertexIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Vertices[vertexIndex].Delay;\n  }\n  getParticleAcceleration(physicsSettingIndex, vertexIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Vertices[vertexIndex].Acceleration;\n  }\n  getParticleRadius(physicsSettingIndex, vertexIndex) {\n    return this._json.PhysicsSettings[physicsSettingIndex].Vertices[vertexIndex].Radius;\n  }\n  getParticlePosition(physicsSettingIndex, vertexIndex) {\n    const ret = new CubismVector2(0, 0);\n    ret.x = this._json.PhysicsSettings[physicsSettingIndex].Vertices[vertexIndex].Position.X;\n    ret.y = this._json.PhysicsSettings[physicsSettingIndex].Vertices[vertexIndex].Position.Y;\n    return ret;\n  }\n}\nconst PhysicsTypeTagX = \"X\";\nconst PhysicsTypeTagY = \"Y\";\nconst PhysicsTypeTagAngle = \"Angle\";\nconst AirResistance = 5;\nconst MaximumWeight = 100;\nconst MovementThreshold = 1e-3;\nclass CubismPhysics {\n  static create(json) {\n    const ret = new CubismPhysics();\n    ret.parse(json);\n    ret._physicsRig.gravity.y = 0;\n    return ret;\n  }\n  evaluate(model, deltaTimeSeconds) {\n    let totalAngle;\n    let weight;\n    let radAngle;\n    let outputValue;\n    const totalTranslation = new CubismVector2();\n    let currentSetting;\n    let currentInput;\n    let currentOutput;\n    let currentParticles;\n    let parameterValue;\n    let parameterMaximumValue;\n    let parameterMinimumValue;\n    let parameterDefaultValue;\n    parameterValue = model.getModel().parameters.values;\n    parameterMaximumValue = model.getModel().parameters.maximumValues;\n    parameterMinimumValue = model.getModel().parameters.minimumValues;\n    parameterDefaultValue = model.getModel().parameters.defaultValues;\n    for (let settingIndex = 0; settingIndex < this._physicsRig.subRigCount; ++settingIndex) {\n      totalAngle = { angle: 0 };\n      totalTranslation.x = 0;\n      totalTranslation.y = 0;\n      currentSetting = this._physicsRig.settings[settingIndex];\n      currentInput = this._physicsRig.inputs.slice(currentSetting.baseInputIndex);\n      currentOutput = this._physicsRig.outputs.slice(currentSetting.baseOutputIndex);\n      currentParticles = this._physicsRig.particles.slice(currentSetting.baseParticleIndex);\n      for (let i = 0; i < currentSetting.inputCount; ++i) {\n        weight = currentInput[i].weight / MaximumWeight;\n        if (currentInput[i].sourceParameterIndex == -1) {\n          currentInput[i].sourceParameterIndex = model.getParameterIndex(currentInput[i].source.id);\n        }\n        currentInput[i].getNormalizedParameterValue(totalTranslation, totalAngle, parameterValue[currentInput[i].sourceParameterIndex], parameterMinimumValue[currentInput[i].sourceParameterIndex], parameterMaximumValue[currentInput[i].sourceParameterIndex], parameterDefaultValue[currentInput[i].sourceParameterIndex], currentSetting.normalizationPosition, currentSetting.normalizationAngle, currentInput[i].reflect, weight);\n      }\n      radAngle = CubismMath.degreesToRadian(-totalAngle.angle);\n      totalTranslation.x = totalTranslation.x * CubismMath.cos(radAngle) - totalTranslation.y * CubismMath.sin(radAngle);\n      totalTranslation.y = totalTranslation.x * CubismMath.sin(radAngle) + totalTranslation.y * CubismMath.cos(radAngle);\n      updateParticles(currentParticles, currentSetting.particleCount, totalTranslation, totalAngle.angle, this._options.wind, MovementThreshold * currentSetting.normalizationPosition.maximum, deltaTimeSeconds, AirResistance);\n      for (let i = 0; i < currentSetting.outputCount; ++i) {\n        const particleIndex = currentOutput[i].vertexIndex;\n        if (particleIndex < 1 || particleIndex >= currentSetting.particleCount) {\n          break;\n        }\n        if (currentOutput[i].destinationParameterIndex == -1) {\n          currentOutput[i].destinationParameterIndex = model.getParameterIndex(currentOutput[i].destination.id);\n        }\n        const translation = new CubismVector2();\n        translation.x = currentParticles[particleIndex].position.x - currentParticles[particleIndex - 1].position.x;\n        translation.y = currentParticles[particleIndex].position.y - currentParticles[particleIndex - 1].position.y;\n        outputValue = currentOutput[i].getValue(translation, currentParticles, particleIndex, currentOutput[i].reflect, this._options.gravity);\n        const destinationParameterIndex = currentOutput[i].destinationParameterIndex;\n        const outParameterValue = !Float32Array.prototype.slice && \"subarray\" in Float32Array.prototype ? JSON.parse(JSON.stringify(parameterValue.subarray(destinationParameterIndex))) : parameterValue.slice(destinationParameterIndex);\n        updateOutputParameterValue(outParameterValue, parameterMinimumValue[destinationParameterIndex], parameterMaximumValue[destinationParameterIndex], outputValue, currentOutput[i]);\n        for (let offset = destinationParameterIndex, outParamIndex = 0; offset < parameterValue.length; offset++, outParamIndex++) {\n          parameterValue[offset] = outParameterValue[outParamIndex];\n        }\n      }\n    }\n  }\n  setOptions(options) {\n    this._options = options;\n  }\n  getOption() {\n    return this._options;\n  }\n  constructor() {\n    this._options = new Options();\n    this._options.gravity.y = -1;\n    this._options.gravity.x = 0;\n    this._options.wind.x = 0;\n    this._options.wind.y = 0;\n  }\n  release() {\n    this._physicsRig = void 0;\n  }\n  parse(physicsJson) {\n    this._physicsRig = new CubismPhysicsRig();\n    let json = new CubismPhysicsJson(physicsJson);\n    this._physicsRig.gravity = json.getGravity();\n    this._physicsRig.wind = json.getWind();\n    this._physicsRig.subRigCount = json.getSubRigCount();\n    let inputIndex = 0, outputIndex = 0, particleIndex = 0;\n    for (let i = 0; i < this._physicsRig.subRigCount; ++i) {\n      const setting = new CubismPhysicsSubRig();\n      setting.normalizationPosition.minimum = json.getNormalizationPositionMinimumValue(i);\n      setting.normalizationPosition.maximum = json.getNormalizationPositionMaximumValue(i);\n      setting.normalizationPosition.defalut = json.getNormalizationPositionDefaultValue(i);\n      setting.normalizationAngle.minimum = json.getNormalizationAngleMinimumValue(i);\n      setting.normalizationAngle.maximum = json.getNormalizationAngleMaximumValue(i);\n      setting.normalizationAngle.defalut = json.getNormalizationAngleDefaultValue(i);\n      setting.inputCount = json.getInputCount(i);\n      setting.baseInputIndex = inputIndex;\n      inputIndex += setting.inputCount;\n      for (let j = 0; j < setting.inputCount; ++j) {\n        const input = new CubismPhysicsInput();\n        input.sourceParameterIndex = -1;\n        input.weight = json.getInputWeight(i, j);\n        input.reflect = json.getInputReflect(i, j);\n        switch (json.getInputType(i, j)) {\n          case PhysicsTypeTagX:\n            input.type = CubismPhysicsSource.CubismPhysicsSource_X;\n            input.getNormalizedParameterValue = getInputTranslationXFromNormalizedParameterValue;\n            break;\n          case PhysicsTypeTagY:\n            input.type = CubismPhysicsSource.CubismPhysicsSource_Y;\n            input.getNormalizedParameterValue = getInputTranslationYFromNormalizedParamterValue;\n            break;\n          case PhysicsTypeTagAngle:\n            input.type = CubismPhysicsSource.CubismPhysicsSource_Angle;\n            input.getNormalizedParameterValue = getInputAngleFromNormalizedParameterValue;\n            break;\n        }\n        input.source.targetType = CubismPhysicsTargetType.CubismPhysicsTargetType_Parameter;\n        input.source.id = json.getInputSourceId(i, j);\n        this._physicsRig.inputs.push(input);\n      }\n      setting.outputCount = json.getOutputCount(i);\n      setting.baseOutputIndex = outputIndex;\n      outputIndex += setting.outputCount;\n      for (let j = 0; j < setting.outputCount; ++j) {\n        const output = new CubismPhysicsOutput();\n        output.destinationParameterIndex = -1;\n        output.vertexIndex = json.getOutputVertexIndex(i, j);\n        output.angleScale = json.getOutputAngleScale(i, j);\n        output.weight = json.getOutputWeight(i, j);\n        output.destination.targetType = CubismPhysicsTargetType.CubismPhysicsTargetType_Parameter;\n        output.destination.id = json.getOutputDestinationId(i, j);\n        switch (json.getOutputType(i, j)) {\n          case PhysicsTypeTagX:\n            output.type = CubismPhysicsSource.CubismPhysicsSource_X;\n            output.getValue = getOutputTranslationX;\n            output.getScale = getOutputScaleTranslationX;\n            break;\n          case PhysicsTypeTagY:\n            output.type = CubismPhysicsSource.CubismPhysicsSource_Y;\n            output.getValue = getOutputTranslationY;\n            output.getScale = getOutputScaleTranslationY;\n            break;\n          case PhysicsTypeTagAngle:\n            output.type = CubismPhysicsSource.CubismPhysicsSource_Angle;\n            output.getValue = getOutputAngle;\n            output.getScale = getOutputScaleAngle;\n            break;\n        }\n        output.reflect = json.getOutputReflect(i, j);\n        this._physicsRig.outputs.push(output);\n      }\n      setting.particleCount = json.getParticleCount(i);\n      setting.baseParticleIndex = particleIndex;\n      particleIndex += setting.particleCount;\n      for (let j = 0; j < setting.particleCount; ++j) {\n        const particle = new CubismPhysicsParticle();\n        particle.mobility = json.getParticleMobility(i, j);\n        particle.delay = json.getParticleDelay(i, j);\n        particle.acceleration = json.getParticleAcceleration(i, j);\n        particle.radius = json.getParticleRadius(i, j);\n        particle.position = json.getParticlePosition(i, j);\n        this._physicsRig.particles.push(particle);\n      }\n      this._physicsRig.settings.push(setting);\n    }\n    this.initialize();\n    json.release();\n  }\n  initialize() {\n    let strand;\n    let currentSetting;\n    let radius;\n    for (let settingIndex = 0; settingIndex < this._physicsRig.subRigCount; ++settingIndex) {\n      currentSetting = this._physicsRig.settings[settingIndex];\n      strand = this._physicsRig.particles.slice(currentSetting.baseParticleIndex);\n      strand[0].initialPosition = new CubismVector2(0, 0);\n      strand[0].lastPosition = new CubismVector2(strand[0].initialPosition.x, strand[0].initialPosition.y);\n      strand[0].lastGravity = new CubismVector2(0, -1);\n      strand[0].lastGravity.y *= -1;\n      strand[0].velocity = new CubismVector2(0, 0);\n      strand[0].force = new CubismVector2(0, 0);\n      for (let i = 1; i < currentSetting.particleCount; ++i) {\n        radius = new CubismVector2(0, 0);\n        radius.y = strand[i].radius;\n        strand[i].initialPosition = new CubismVector2(strand[i - 1].initialPosition.x + radius.x, strand[i - 1].initialPosition.y + radius.y);\n        strand[i].position = new CubismVector2(strand[i].initialPosition.x, strand[i].initialPosition.y);\n        strand[i].lastPosition = new CubismVector2(strand[i].initialPosition.x, strand[i].initialPosition.y);\n        strand[i].lastGravity = new CubismVector2(0, -1);\n        strand[i].lastGravity.y *= -1;\n        strand[i].velocity = new CubismVector2(0, 0);\n        strand[i].force = new CubismVector2(0, 0);\n      }\n    }\n  }\n}\nclass Options {\n  constructor() {\n    this.gravity = new CubismVector2(0, 0);\n    this.wind = new CubismVector2(0, 0);\n  }\n}\nfunction getInputTranslationXFromNormalizedParameterValue(targetTranslation, targetAngle, value, parameterMinimumValue, parameterMaximumValue, parameterDefaultValue, normalizationPosition, normalizationAngle, isInverted, weight) {\n  targetTranslation.x += normalizeParameterValue(value, parameterMinimumValue, parameterMaximumValue, parameterDefaultValue, normalizationPosition.minimum, normalizationPosition.maximum, normalizationPosition.defalut, isInverted) * weight;\n}\nfunction getInputTranslationYFromNormalizedParamterValue(targetTranslation, targetAngle, value, parameterMinimumValue, parameterMaximumValue, parameterDefaultValue, normalizationPosition, normalizationAngle, isInverted, weight) {\n  targetTranslation.y += normalizeParameterValue(value, parameterMinimumValue, parameterMaximumValue, parameterDefaultValue, normalizationPosition.minimum, normalizationPosition.maximum, normalizationPosition.defalut, isInverted) * weight;\n}\nfunction getInputAngleFromNormalizedParameterValue(targetTranslation, targetAngle, value, parameterMinimumValue, parameterMaximumValue, parameterDefaultValue, normalizaitionPosition, normalizationAngle, isInverted, weight) {\n  targetAngle.angle += normalizeParameterValue(value, parameterMinimumValue, parameterMaximumValue, parameterDefaultValue, normalizationAngle.minimum, normalizationAngle.maximum, normalizationAngle.defalut, isInverted) * weight;\n}\nfunction getOutputTranslationX(translation, particles, particleIndex, isInverted, parentGravity) {\n  let outputValue = translation.x;\n  if (isInverted) {\n    outputValue *= -1;\n  }\n  return outputValue;\n}\nfunction getOutputTranslationY(translation, particles, particleIndex, isInverted, parentGravity) {\n  let outputValue = translation.y;\n  if (isInverted) {\n    outputValue *= -1;\n  }\n  return outputValue;\n}\nfunction getOutputAngle(translation, particles, particleIndex, isInverted, parentGravity) {\n  let outputValue;\n  if (particleIndex >= 2) {\n    parentGravity = particles[particleIndex - 1].position.substract(particles[particleIndex - 2].position);\n  } else {\n    parentGravity = parentGravity.multiplyByScaler(-1);\n  }\n  outputValue = CubismMath.directionToRadian(parentGravity, translation);\n  if (isInverted) {\n    outputValue *= -1;\n  }\n  return outputValue;\n}\nfunction getRangeValue(min, max) {\n  return Math.abs(Math.max(min, max) - Math.min(min, max));\n}\nfunction getDefaultValue(min, max) {\n  const minValue = Math.min(min, max);\n  return minValue + getRangeValue(min, max) / 2;\n}\nfunction getOutputScaleTranslationX(translationScale, angleScale) {\n  return translationScale.x;\n}\nfunction getOutputScaleTranslationY(translationScale, angleScale) {\n  return translationScale.y;\n}\nfunction getOutputScaleAngle(translationScale, angleScale) {\n  return angleScale;\n}\nfunction updateParticles(strand, strandCount, totalTranslation, totalAngle, windDirection, thresholdValue, deltaTimeSeconds, airResistance) {\n  let totalRadian;\n  let delay;\n  let radian;\n  let currentGravity;\n  let direction = new CubismVector2(0, 0);\n  let velocity = new CubismVector2(0, 0);\n  let force = new CubismVector2(0, 0);\n  let newDirection = new CubismVector2(0, 0);\n  strand[0].position = new CubismVector2(totalTranslation.x, totalTranslation.y);\n  totalRadian = CubismMath.degreesToRadian(totalAngle);\n  currentGravity = CubismMath.radianToDirection(totalRadian);\n  currentGravity.normalize();\n  for (let i = 1; i < strandCount; ++i) {\n    strand[i].force = currentGravity.multiplyByScaler(strand[i].acceleration).add(windDirection);\n    strand[i].lastPosition = new CubismVector2(strand[i].position.x, strand[i].position.y);\n    delay = strand[i].delay * deltaTimeSeconds * 30;\n    direction = strand[i].position.substract(strand[i - 1].position);\n    radian = CubismMath.directionToRadian(strand[i].lastGravity, currentGravity) / airResistance;\n    direction.x = CubismMath.cos(radian) * direction.x - direction.y * CubismMath.sin(radian);\n    direction.y = CubismMath.sin(radian) * direction.x + direction.y * CubismMath.cos(radian);\n    strand[i].position = strand[i - 1].position.add(direction);\n    velocity = strand[i].velocity.multiplyByScaler(delay);\n    force = strand[i].force.multiplyByScaler(delay).multiplyByScaler(delay);\n    strand[i].position = strand[i].position.add(velocity).add(force);\n    newDirection = strand[i].position.substract(strand[i - 1].position);\n    newDirection.normalize();\n    strand[i].position = strand[i - 1].position.add(newDirection.multiplyByScaler(strand[i].radius));\n    if (CubismMath.abs(strand[i].position.x) < thresholdValue) {\n      strand[i].position.x = 0;\n    }\n    if (delay != 0) {\n      strand[i].velocity = strand[i].position.substract(strand[i].lastPosition);\n      strand[i].velocity = strand[i].velocity.divisionByScalar(delay);\n      strand[i].velocity = strand[i].velocity.multiplyByScaler(strand[i].mobility);\n    }\n    strand[i].force = new CubismVector2(0, 0);\n    strand[i].lastGravity = new CubismVector2(currentGravity.x, currentGravity.y);\n  }\n}\nfunction updateOutputParameterValue(parameterValue, parameterValueMinimum, parameterValueMaximum, translation, output) {\n  let outputScale;\n  let value;\n  let weight;\n  outputScale = output.getScale(output.translationScale, output.angleScale);\n  value = translation * outputScale;\n  if (value < parameterValueMinimum) {\n    if (value < output.valueBelowMinimum) {\n      output.valueBelowMinimum = value;\n    }\n    value = parameterValueMinimum;\n  } else if (value > parameterValueMaximum) {\n    if (value > output.valueExceededMaximum) {\n      output.valueExceededMaximum = value;\n    }\n    value = parameterValueMaximum;\n  }\n  weight = output.weight / MaximumWeight;\n  if (weight >= 1) {\n    parameterValue[0] = value;\n  } else {\n    value = parameterValue[0] * (1 - weight) + value * weight;\n    parameterValue[0] = value;\n  }\n}\nfunction normalizeParameterValue(value, parameterMinimum, parameterMaximum, parameterDefault, normalizedMinimum, normalizedMaximum, normalizedDefault, isInverted) {\n  let result = 0;\n  const maxValue = CubismMath.max(parameterMaximum, parameterMinimum);\n  if (maxValue < value) {\n    value = maxValue;\n  }\n  const minValue = CubismMath.min(parameterMaximum, parameterMinimum);\n  if (minValue > value) {\n    value = minValue;\n  }\n  const minNormValue = CubismMath.min(normalizedMinimum, normalizedMaximum);\n  const maxNormValue = CubismMath.max(normalizedMinimum, normalizedMaximum);\n  const middleNormValue = normalizedDefault;\n  const middleValue = getDefaultValue(minValue, maxValue);\n  const paramValue = value - middleValue;\n  switch (Math.sign(paramValue)) {\n    case 1: {\n      const nLength = maxNormValue - middleNormValue;\n      const pLength = maxValue - middleValue;\n      if (pLength != 0) {\n        result = paramValue * (nLength / pLength);\n        result += middleNormValue;\n      }\n      break;\n    }\n    case -1: {\n      const nLength = minNormValue - middleNormValue;\n      const pLength = minValue - middleValue;\n      if (pLength != 0) {\n        result = paramValue * (nLength / pLength);\n        result += middleNormValue;\n      }\n      break;\n    }\n    case 0: {\n      result = middleNormValue;\n      break;\n    }\n  }\n  return isInverted ? result : result * -1;\n}\nLive2DFactory.registerRuntime({\n  version: 4,\n  ready: cubism4Ready,\n  test(source) {\n    return source instanceof Cubism4ModelSettings || Cubism4ModelSettings.isValidJSON(source);\n  },\n  isValidMoc(modelData) {\n    if (modelData.byteLength < 4) {\n      return false;\n    }\n    const view = new Int8Array(modelData, 0, 4);\n    return String.fromCharCode(...view) === \"MOC3\";\n  },\n  createModelSettings(json) {\n    return new Cubism4ModelSettings(json);\n  },\n  createCoreModel(data) {\n    const moc = CubismMoc.create(data);\n    try {\n      const model = moc.createModel();\n      model.__moc = moc;\n      return model;\n    } catch (e) {\n      try {\n        moc.release();\n      } catch (ignored) {\n      }\n      throw e;\n    }\n  },\n  createInternalModel(coreModel, settings, options) {\n    const model = new Cubism4InternalModel(coreModel, settings, options);\n    const coreModelWithMoc = coreModel;\n    if (coreModelWithMoc.__moc) {\n      model.__moc = coreModelWithMoc.__moc;\n      delete coreModelWithMoc.__moc;\n      model.once(\"destroy\", releaseMoc);\n    }\n    return model;\n  },\n  createPhysics(coreModel, data) {\n    return CubismPhysics.create(data);\n  },\n  createPose(coreModel, data) {\n    return CubismPose.create(data);\n  }\n});\nfunction releaseMoc() {\n  var _a;\n  (_a = this.__moc) == null ? void 0 : _a.release();\n}\nexport { Cubism2ExpressionManager, Cubism2InternalModel, Cubism2ModelSettings, Cubism2MotionManager, Cubism4ExpressionManager, Cubism4InternalModel, Cubism4ModelSettings, Cubism4MotionManager, ExpressionManager, FileLoader, FocusController, InteractionMixin, InternalModel, LOGICAL_HEIGHT, LOGICAL_WIDTH, Live2DExpression, Live2DEyeBlink, Live2DFactory, Live2DLoader, Live2DModel, Live2DPhysics, Live2DPose, Live2DTransform, ModelSettings, MotionManager, MotionPreloadStrategy, MotionPriority, MotionState, SoundManager, VERSION, XHRLoader, ZipLoader, applyMixins, clamp, config, copyArray, copyProperty, cubism4Ready, folderName, logger, rand, remove, startUpCubism4 };\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAI,QAAQ,KAAK;AACjB,IAAI,UAAU,CAAC,QAAQ,aAAa,cAAc;AAChD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,YAAY,CAAC,UAAU;AACzB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,QAAI,WAAW,CAAC,UAAU;AACxB,UAAI;AACF,aAAK,UAAU,MAAM,KAAK,CAAC;AAAA,MAC7B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,QAAI,OAAO,CAAC,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,IAAI,QAAQ,QAAQ,EAAE,KAAK,EAAE,KAAK,WAAW,QAAQ;AAC/F,UAAM,YAAY,UAAU,MAAM,QAAQ,WAAW,GAAG,KAAK,CAAC;AAAA,EAChE,CAAC;AACH;AAKA,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAI;AAAA,CACH,CAAC,kBAAkB;AAClB,gBAAc,2BAA2B;AACzC,gBAAc,uBAAuB;AACvC,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,CAAC,YAAY;AACZ,UAAQ,oBAAoB;AAC5B,UAAQ,oBAAoB;AAC5B,UAAQ,kBAAkB;AAC1B,UAAQ,iBAAiB;AACzB,UAAQ,WAAW,QAAQ;AAC3B,UAAQ,QAAQ;AAChB,UAAQ,aAAa;AACrB,UAAQ,uBAAuB;AAC/B,UAAQ,2BAA2B;AACnC,UAAQ,2BAA2B;AACnC,UAAQ,6BAA6B;AACrC,UAAQ,UAAU;AACpB,GAAG,WAAW,SAAS,CAAC,EAAE;AAC1B,IAAM,UAAU;AAChB,IAAM,SAAS;AAAA,EACb,IAAI,QAAQ,UAAU;AACpB,QAAI,OAAO,YAAY,OAAO,mBAAmB;AAC/C,cAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA,EACA,KAAK,QAAQ,UAAU;AACrB,QAAI,OAAO,YAAY,OAAO,mBAAmB;AAC/C,cAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,QAAQ;AAAA,IACtC;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,UAAU;AACtB,QAAI,OAAO,YAAY,OAAO,iBAAiB;AAC7C,cAAQ,MAAM,IAAI,GAAG,KAAK,GAAG,QAAQ;AAAA,IACvC;AAAA,EACF;AACF;AACA,SAAS,MAAM,KAAK,OAAO,OAAO;AAChC,SAAO,MAAM,QAAQ,QAAQ,MAAM,QAAQ,QAAQ;AACrD;AACA,SAAS,KAAK,KAAK,KAAK;AACtB,SAAO,KAAK,OAAO,KAAK,MAAM,OAAO;AACvC;AACA,SAAS,aAAa,MAAM,MAAM,IAAI,SAAS,OAAO;AACpD,QAAM,QAAQ,KAAK,OAAO;AAC1B,MAAI,UAAU,QAAQ,OAAO,UAAU,MAAM;AAC3C,OAAG,KAAK,IAAI;AAAA,EACd;AACF;AACA,SAAS,UAAU,MAAM,MAAM,IAAI,SAAS,OAAO;AACjD,QAAM,QAAQ,KAAK,OAAO;AAC1B,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,OAAG,KAAK,IAAI,MAAM,OAAO,CAAC,SAAS,SAAS,QAAQ,OAAO,SAAS,IAAI;AAAA,EAC1E;AACF;AACA,SAAS,YAAY,aAAa,WAAW;AAC3C,YAAU,QAAQ,CAAC,aAAa;AAC9B,WAAO,oBAAoB,SAAS,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC/D,UAAI,SAAS,eAAe;AAC1B,eAAO,eAAe,YAAY,WAAW,MAAM,OAAO,yBAAyB,SAAS,WAAW,IAAI,CAAC;AAAA,MAC9G;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,WAAW,MAAM;AACxB,MAAI,iBAAiB,KAAK,YAAY,GAAG;AACzC,MAAI,kBAAkB,IAAI;AACxB,WAAO,KAAK,MAAM,GAAG,cAAc;AAAA,EACrC;AACA,mBAAiB,KAAK,YAAY,GAAG;AACrC,MAAI,mBAAmB,IAAI;AACzB,WAAO,KAAK,MAAM,iBAAiB,CAAC;AAAA,EACtC;AACA,SAAO;AACT;AACA,SAAS,OAAO,OAAO,MAAM;AAC3B,QAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,MAAI,UAAU,IAAI;AAChB,UAAM,OAAO,OAAO,CAAC;AAAA,EACvB;AACF;AACA,IAAM,oBAAN,cAAgC,6BAAa;AAAA,EAC3C,YAAY,UAAU,SAAS;AAC7B,UAAM;AACN,SAAK,cAAc,CAAC;AACpB,SAAK,yBAAyB;AAC9B,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,MAAM,qBAAqB,SAAS,IAAI;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,SAAK,oBAAoB,KAAK,iBAAiB,CAAC,GAAG,MAAM;AACzD,SAAK,oBAAoB,KAAK;AAC9B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,eAAe,OAAO;AACpB,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,UAAI,CAAC,KAAK,YAAY,KAAK,GAAG;AAC5B,eAAO,KAAK,KAAK,KAAK,4BAA4B,KAAK,GAAG;AAC1D,eAAO;AAAA,MACT;AACA,UAAI,KAAK,YAAY,KAAK,MAAM,MAAM;AACpC,eAAO,KAAK,KAAK,KAAK,6BAA6B,KAAK,2CAA2C;AACnG,eAAO;AAAA,MACT;AACA,UAAI,KAAK,YAAY,KAAK,GAAG;AAC3B,eAAO,KAAK,YAAY,KAAK;AAAA,MAC/B;AACA,YAAM,aAAa,MAAM,KAAK,gBAAgB,KAAK;AACnD,WAAK,YAAY,KAAK,IAAI;AAC1B,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AAAA,EACA,sBAAsB;AACpB,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,UAAI,KAAK,YAAY,QAAQ;AAC3B,cAAM,mBAAmB,CAAC;AAC1B,iBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,cAAI,KAAK,YAAY,CAAC,MAAM,QAAQ,KAAK,YAAY,CAAC,MAAM,KAAK,qBAAqB,MAAM,KAAK,wBAAwB;AACvH,6BAAiB,KAAK,CAAC;AAAA,UACzB;AAAA,QACF;AACA,YAAI,iBAAiB,QAAQ;AAC3B,gBAAM,QAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,iBAAiB,MAAM;AAChE,iBAAO,KAAK,cAAc,KAAK;AAAA,QACjC;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,SAAK,eAAe,KAAK,iBAAiB;AAAA,EAC5C;AAAA,EACA,oBAAoB;AAClB,SAAK,eAAe,KAAK,iBAAiB;AAAA,EAC5C;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,KAAK,mBAAmB,KAAK;AAAA,MACvC;AACA,UAAI,EAAE,QAAQ,MAAM,QAAQ,KAAK,YAAY,SAAS;AACpD,eAAO;AAAA,MACT;AACA,UAAI,UAAU,KAAK,YAAY,QAAQ,KAAK,iBAAiB,GAAG;AAC9D,eAAO;AAAA,MACT;AACA,WAAK,yBAAyB;AAC9B,YAAM,aAAa,MAAM,KAAK,eAAe,KAAK;AAClD,UAAI,CAAC,cAAc,KAAK,2BAA2B,OAAO;AACxD,eAAO;AAAA,MACT;AACA,WAAK,yBAAyB;AAC9B,WAAK,oBAAoB;AACzB,WAAK,eAAe,UAAU;AAC9B,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,KAAK;AACjB,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB,aAAO,KAAK,iBAAiB,OAAO,GAAG;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,KAAK,SAAS;AACnB,UAAM,OAAO;AACb,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AACF;AACA,IAAM,UAAU;AAChB,IAAM,YAAY,KAAK;AACvB,IAAM,oBAAoB,KAAK,OAAO;AACtC,IAAM,kBAAN,MAAsB;AAAA,EACpB,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,MAAM,GAAG,GAAG,UAAU,OAAO;AAC3B,SAAK,UAAU,MAAM,GAAG,IAAI,CAAC;AAC7B,SAAK,UAAU,MAAM,GAAG,IAAI,CAAC;AAC7B,QAAI,SAAS;AACX,WAAK,IAAI,KAAK;AACd,WAAK,IAAI,KAAK;AAAA,IAChB;AAAA,EACF;AAAA,EACA,OAAO,IAAI;AACT,UAAM,KAAK,KAAK,UAAU,KAAK;AAC/B,UAAM,KAAK,KAAK,UAAU,KAAK;AAC/B,QAAI,KAAK,IAAI,EAAE,IAAI,WAAW,KAAK,IAAI,EAAE,IAAI;AAC3C;AACF,UAAM,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC;AAC/C,UAAM,WAAW,aAAa,MAAM;AACpC,QAAI,KAAK,YAAY,KAAK,KAAK,KAAK;AACpC,QAAI,KAAK,YAAY,KAAK,KAAK,KAAK;AACpC,UAAM,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC;AAC/C,UAAM,OAAO,WAAW,oBAAoB;AAC5C,QAAI,IAAI,MAAM;AACZ,YAAM,OAAO;AACb,YAAM,OAAO;AAAA,IACf;AACA,SAAK,MAAM;AACX,SAAK,MAAM;AACX,UAAM,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC;AACzD,UAAM,OAAO,OAAO,KAAK,KAAK,MAAM,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;AAC/D,QAAI,IAAI,MAAM;AACZ,WAAK,MAAM,OAAO;AAClB,WAAK,MAAM,OAAO;AAAA,IACpB;AACA,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AAAA,EACjB;AACF;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,UAAU,+DAA+D;AAAA,IACrF;AACA,SAAK,MAAM;AACX,SAAK,OAAO,WAAW,KAAK,GAAG;AAAA,EACjC;AAAA,EACA,WAAW,MAAM;AACf,WAAO,IAAI,QAAQ,KAAK,KAAK,IAAI;AAAA,EACnC;AAAA,EACA,aAAa,UAAU;AACrB,SAAK,MAAM,SAAS,KAAK,KAAK,KAAK;AACnC,QAAI,KAAK,SAAS,QAAQ;AACxB,WAAK,OAAO,SAAS,KAAK,MAAM,MAAM;AAAA,IACxC;AACA,QAAI,KAAK,YAAY,QAAQ;AAC3B,WAAK,UAAU,SAAS,KAAK,SAAS,SAAS;AAAA,IACjD;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,WAAK,SAAS,CAAC,IAAI,SAAS,KAAK,SAAS,CAAC,GAAG,YAAY,CAAC,GAAG;AAAA,IAChE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,UAAM,QAAQ,CAAC;AACf,SAAK,aAAa,CAAC,SAAS;AAC1B,YAAM,KAAK,IAAI;AACf,aAAO;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,mBAAmB,CAAC,cAAc,gBAAgB;AACtD,YAAM,aAAa,KAAK,WAAW,YAAY;AAC/C,UAAI,CAAC,MAAM,SAAS,UAAU,GAAG;AAC/B,YAAI,aAAa;AACf,gBAAM,IAAI,MAAM,SAAS,YAAY,4DAA4D;AAAA,QACnG;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ;AAClD,mBAAe,QAAQ,CAAC,YAAY,iBAAiB,SAAS,IAAI,CAAC;AACnE,UAAM,eAAe,KAAK,gBAAgB;AAC1C,WAAO,aAAa,OAAO,CAAC,SAAS,iBAAiB,MAAM,KAAK,CAAC;AAAA,EACpE;AACF;AACA,IAAI,kBAAkC,CAAC,oBAAoB;AACzD,kBAAgB,gBAAgB,MAAM,IAAI,CAAC,IAAI;AAC/C,kBAAgB,gBAAgB,MAAM,IAAI,CAAC,IAAI;AAC/C,kBAAgB,gBAAgB,QAAQ,IAAI,CAAC,IAAI;AACjD,kBAAgB,gBAAgB,OAAO,IAAI,CAAC,IAAI;AAChD,SAAO;AACT,GAAG,kBAAkB,CAAC,CAAC;AACvB,IAAM,cAAN,MAAkB;AAAA,EAChB,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,QAAQ,OAAO,OAAO,UAAU;AAC9B,QAAI,YAAY,GAAG;AACjB,aAAO,IAAI,KAAK,KAAK,iDAAiD;AACtE,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,gBAAgB,UAAU,KAAK,cAAc;AAC9D,aAAO,IAAI,KAAK,KAAK,8BAA8B,KAAK,KAAK,OAAO,KAAK,CAAC;AAC1E,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,UAAU,KAAK,qBAAqB,UAAU,KAAK,mBAAmB;AACxI,aAAO,IAAI,KAAK,KAAK,+BAA+B,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3E,aAAO;AAAA,IACT;AACA,QAAI,aAAa,GAAG;AAClB,UAAI,KAAK,oBAAoB,GAAG;AAC9B,eAAO,IAAI,KAAK,KAAK,+DAA+D,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3G,eAAO;AAAA,MACT;AACA,UAAI,KAAK,sBAAsB,QAAQ;AACrC,eAAO,IAAI,KAAK,KAAK,sEAAsE,KAAK,KAAK,OAAO,KAAK,CAAC;AAClH,eAAO;AAAA,MACT;AACA,WAAK,gBAAgB,OAAO,KAAK;AAAA,IACnC,OAAO;AACL,UAAI,WAAW,GAAG;AAChB,YAAI,YAAY,KAAK,iBAAiB;AACpC,iBAAO,IAAI,KAAK,KAAK,8FAA8F,KAAK,KAAK,OAAO,KAAK,CAAC;AAC1I,iBAAO;AAAA,QACT;AACA,YAAI,YAAY,KAAK,iBAAiB;AACpC,iBAAO,IAAI,KAAK,KAAK,gGAAgG,KAAK,KAAK,OAAO,KAAK,CAAC;AAC5I,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,YAAY,OAAO,OAAO,QAAQ;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,QAAQ,OAAO,OAAO,UAAU;AACpC,QAAI,aAAa,GAAG;AAClB,WAAK,gBAAgB,QAAQ,MAAM;AACnC,UAAI,KAAK,oBAAoB,GAAG;AAC9B,eAAO,IAAI,KAAK,KAAK,+DAA+D,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3G,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,UAAI,UAAU,KAAK,iBAAiB,UAAU,KAAK,eAAe;AAChE,eAAO,IAAI,KAAK,KAAK,mEAAmE,KAAK,KAAK,OAAO,KAAK,CAAC;AAC/G,eAAO;AAAA,MACT;AACA,WAAK,YAAY,QAAQ,QAAQ,CAAC;AAAA,IACpC;AACA,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,SAAK,WAAW,OAAO,OAAO,QAAQ;AACtC,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,SAAK,WAAW,QAAQ,QAAQ,CAAC;AAAA,EACnC;AAAA,EACA,WAAW,OAAO,OAAO,UAAU;AACjC,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,YAAY,OAAO,OAAO,UAAU;AAClC,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO,OAAO;AAC5B,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,SAAS,OAAO,OAAO;AACrB,WAAO,UAAU,KAAK,gBAAgB,UAAU,KAAK,gBAAgB,UAAU,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,UAAU,KAAK,qBAAqB,UAAU,KAAK;AAAA,EAC1L;AAAA,EACA,QAAQ;AACN,SAAK,WAAW,QAAQ,QAAQ,CAAC;AACjC,SAAK,YAAY,QAAQ,QAAQ,CAAC;AAClC,SAAK,gBAAgB,QAAQ,MAAM;AAAA,EACrC;AAAA,EACA,0BAA0B;AACxB,WAAO,KAAK,iBAAiB,UAAU,KAAK,sBAAsB;AAAA,EACpE;AAAA,EACA,2BAA2B;AACzB,WAAO,CAAC,OAAO,8BAA8B,KAAK,kBAAkB;AAAA,EACtE;AAAA,EACA,KAAK,gBAAgB,gBAAgB;AACnC,QAAI,KAAK,OAAO;AACd,YAAM,OAAO;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,uBACU,cAAc,cAAc,cAAc;AAAA,IAC7D,KAAK,IAAI,CAAC,QAAQ,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,eAAN,MAAmB;AAAA,EACjB,WAAW,SAAS;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,OAAO,OAAO;AACvB,SAAK,WAAW,QAAQ,IAAI,IAAI,QAAQ,IAAI,IAAI,UAAU;AAC1D,SAAK,OAAO,QAAQ,CAAC,UAAU,MAAM,SAAS,KAAK,OAAO;AAAA,EAC5D;AAAA,EACA,OAAO,IAAI,MAAM,UAAU,SAAS;AAClC,UAAM,QAAQ,IAAI,MAAM,IAAI;AAC5B,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU;AAChB,UAAM,iBAAiB,SAAS,MAAM;AACpC,WAAK,QAAQ,KAAK;AAClB,kBAAY,OAAO,SAAS,SAAS;AAAA,IACvC,CAAC;AACD,UAAM,iBAAiB,SAAS,CAAC,MAAM;AACrC,WAAK,QAAQ,KAAK;AAClB,aAAO,KAAK,OAAO,sBAAsB,IAAI,KAAK,EAAE,KAAK;AACzD,iBAAW,OAAO,SAAS,QAAQ,EAAE,KAAK;AAAA,IAC5C,CAAC;AACD,SAAK,OAAO,KAAK,KAAK;AACtB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,OAAO;AACjB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACJ,OAAC,KAAK,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,MAAM,CAAC,MAAM;AACrD,cAAM,cAAc,IAAI,WAAW,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AACzD,eAAO,CAAC;AAAA,MACV,CAAC;AACD,UAAI,MAAM,eAAe,MAAM,kBAAkB;AAC/C,gBAAQ;AAAA,MACV,OAAO;AACL,cAAM,iBAAiB,kBAAkB,OAAO;AAAA,MAClD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,QAAQ,OAAO;AACpB,UAAM,MAAM;AACZ,UAAM,gBAAgB,KAAK;AAC3B,WAAO,KAAK,QAAQ,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO,UAAU;AACf,aAAS,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,WAAK,QAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,IAC7B;AAAA,EACF;AACF;AACA,aAAa,SAAS,CAAC;AACvB,aAAa,UAAU;AACvB,IAAI,yBAAyC,CAAC,2BAA2B;AACvE,yBAAuB,KAAK,IAAI;AAChC,yBAAuB,MAAM,IAAI;AACjC,yBAAuB,MAAM,IAAI;AACjC,SAAO;AACT,GAAG,yBAAyB,CAAC,CAAC;AAC9B,IAAM,gBAAN,cAA4B,6BAAa;AAAA,EACvC,YAAY,UAAU,SAAS;AAC7B,UAAM;AACN,SAAK,eAAe,CAAC;AACrB,SAAK,QAAQ,IAAI,YAAY;AAC7B,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,MAAM,iBAAiB,SAAS,IAAI;AACzC,SAAK,MAAM,MAAM,KAAK;AAAA,EACxB;AAAA,EACA,KAAK,SAAS;AACZ,QAAI,WAAW,OAAO,SAAS,QAAQ,iBAAiB;AACtD,WAAK,OAAO,OAAO,QAAQ;AAAA,IAC7B;AACA,SAAK,aAAa,OAAO;AACzB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,aAAa,SAAS;AACpB,eAAW,SAAS,OAAO,KAAK,KAAK,WAAW,GAAG;AACjD,WAAK,aAAa,KAAK,IAAI,CAAC;AAAA,IAC9B;AACA,QAAI;AACJ,YAAQ,WAAW,OAAO,SAAS,QAAQ,eAAe;AAAA,MACxD,KAAK;AACH;AAAA,MACF,KAAK;AACH,iBAAS,OAAO,KAAK,KAAK,WAAW;AACrC;AAAA,MACF,KAAK;AAAA,MACL;AACE,iBAAS,CAAC,KAAK,OAAO,IAAI;AAC1B;AAAA,IACJ;AACA,eAAW,SAAS,QAAQ;AAC1B,UAAI,KAAK,YAAY,KAAK,GAAG;AAC3B,iBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,EAAE,QAAQ,KAAK;AACvD,eAAK,WAAW,OAAO,CAAC,EAAE,KAAK;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO,OAAO;AACvB,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,UAAI;AACJ,UAAI,GAAG,KAAK,KAAK,YAAY,KAAK,MAAM,OAAO,SAAS,GAAG,KAAK,IAAI;AAClE,eAAO,KAAK,KAAK,KAAK,wBAAwB,KAAK,KAAK,KAAK,GAAG;AAChE,eAAO;AAAA,MACT;AACA,UAAI,KAAK,aAAa,KAAK,EAAE,KAAK,MAAM,MAAM;AAC5C,eAAO,KAAK,KAAK,KAAK,2BAA2B,KAAK,KAAK,KAAK,2CAA2C;AAC3G,eAAO;AAAA,MACT;AACA,UAAI,KAAK,aAAa,KAAK,EAAE,KAAK,GAAG;AACnC,eAAO,KAAK,aAAa,KAAK,EAAE,KAAK;AAAA,MACvC;AACA,YAAM,SAAS,MAAM,KAAK,YAAY,OAAO,KAAK;AAClD,UAAI,KAAK,WAAW;AAClB;AAAA,MACF;AACA,WAAK,aAAa,KAAK,EAAE,KAAK,IAAI,UAAU,OAAO,SAAS;AAC5D,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AAAA,EACA,YAAY,IAAI,IAAI;AAClB,WAAO,QAAQ,MAAM,WAAW,WAAW,OAAO,OAAO,WAAW,eAAe,QAAQ;AACzF,UAAI;AACJ,UAAI,CAAC,KAAK,MAAM,QAAQ,OAAO,OAAO,QAAQ,GAAG;AAC/C,eAAO;AAAA,MACT;AACA,YAAM,cAAc,KAAK,KAAK,YAAY,KAAK,MAAM,OAAO,SAAS,GAAG,KAAK;AAC7E,UAAI,CAAC,YAAY;AACf,eAAO;AAAA,MACT;AACA,UAAI,KAAK,cAAc;AACrB,qBAAa,QAAQ,KAAK,YAAY;AAAA,MACxC;AACA,UAAI;AACJ,UAAI,OAAO,OAAO;AAChB,cAAM,WAAW,KAAK,aAAa,UAAU;AAC7C,YAAI,UAAU;AACZ,cAAI;AACF,oBAAQ,aAAa,IAAI,KAAK,SAAS,WAAW,QAAQ,GAAG,MAAM,KAAK,eAAe,QAAQ,MAAM,KAAK,eAAe,MAAM;AAC/H,iBAAK,eAAe;AAAA,UACtB,SAAS,GAAG;AACV,mBAAO,KAAK,KAAK,KAAK,0BAA0B,UAAU,CAAC;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,MAAM,KAAK,WAAW,OAAO,KAAK;AACjD,UAAI,OAAO;AACT,cAAM,cAAc,aAAa,KAAK,KAAK,EAAE,MAAM,CAAC,MAAM,OAAO,KAAK,KAAK,KAAK,wBAAwB,MAAM,KAAK,CAAC,CAAC;AACrH,YAAI,OAAO,YAAY;AACrB,gBAAM;AAAA,QACR;AAAA,MACF;AACA,UAAI,CAAC,KAAK,MAAM,MAAM,QAAQ,OAAO,OAAO,QAAQ,GAAG;AACrD,YAAI,OAAO;AACT,uBAAa,QAAQ,KAAK;AAC1B,eAAK,eAAe;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AACA,aAAO,IAAI,KAAK,KAAK,iBAAiB,KAAK,cAAc,UAAU,CAAC;AACpE,WAAK,KAAK,eAAe,OAAO,OAAO,KAAK;AAC5C,UAAI,KAAK,MAAM,yBAAyB,GAAG;AACzC,aAAK,qBAAqB,KAAK,kBAAkB,gBAAgB;AAAA,MACnE;AACA,WAAK,UAAU;AACf,WAAK,aAAa,MAAM;AACxB,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,OAAO,UAAU;AACjC,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,YAAY,KAAK,YAAY,KAAK;AACxC,UAAI,aAAa,OAAO,SAAS,UAAU,QAAQ;AACjD,cAAM,mBAAmB,CAAC;AAC1B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,KAAK,aAAa,KAAK,EAAE,CAAC,MAAM,QAAQ,CAAC,KAAK,MAAM,SAAS,OAAO,CAAC,GAAG;AAC1E,6BAAiB,KAAK,CAAC;AAAA,UACzB;AAAA,QACF;AACA,YAAI,iBAAiB,QAAQ;AAC3B,gBAAM,QAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,iBAAiB,MAAM;AAChE,iBAAO,KAAK,YAAY,OAAO,iBAAiB,KAAK,GAAG,QAAQ;AAAA,QAClE;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,SAAK,gBAAgB;AACrB,SAAK,MAAM,MAAM;AACjB,QAAI,KAAK,cAAc;AACrB,mBAAa,QAAQ,KAAK,YAAY;AACtC,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,KAAK;AACjB,QAAI;AACJ,QAAI,KAAK,WAAW,GAAG;AACrB,UAAI,KAAK,SAAS;AAChB,aAAK,UAAU;AACf,aAAK,KAAK,cAAc;AAAA,MAC1B;AACA,UAAI,KAAK,MAAM,yBAAyB,GAAG;AACzC,SAAC,KAAK,KAAK,sBAAsB,OAAO,SAAS,GAAG,kBAAkB;AAAA,MACxE;AACA,WAAK,MAAM,SAAS;AACpB,UAAI,KAAK,MAAM,wBAAwB,GAAG;AACxC,aAAK,kBAAkB,KAAK,OAAO,MAAM,eAAe,IAAI;AAAA,MAC9D;AAAA,IACF;AACA,WAAO,KAAK,iBAAiB,OAAO,GAAG;AAAA,EACzC;AAAA,EACA,UAAU;AACR,QAAI;AACJ,SAAK,YAAY;AACjB,SAAK,KAAK,SAAS;AACnB,SAAK,eAAe;AACpB,KAAC,KAAK,KAAK,sBAAsB,OAAO,SAAS,GAAG,QAAQ;AAC5D,UAAM,OAAO;AACb,SAAK,cAAc;AACnB,SAAK,eAAe;AAAA,EACtB;AACF;AACA,IAAM,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AACrD,IAAM,gBAAN,cAA4B,6BAAa;AAAA,EACvC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,kBAAkB,IAAI,gBAAgB;AAC3C,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,iBAAiB,IAAI,OAAO;AACjC,SAAK,gBAAgB,IAAI,OAAO;AAChC,SAAK,WAAW,CAAC;AACjB,SAAK,eAAe;AACpB,SAAK,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;AAC3B,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,UAAM,OAAO;AACb,UAAM,OAAO,KAAK,QAAQ;AAC1B,SAAK,gBAAgB,KAAK,CAAC;AAC3B,SAAK,iBAAiB,KAAK,CAAC;AAC5B,UAAM,SAAS,OAAO,OAAO;AAAA,MAC3B,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,GAAG,KAAK,UAAU,CAAC;AACnB,SAAK,eAAe,MAAM,OAAO,QAAQ,eAAe,OAAO,SAAS,cAAc;AACtF,SAAK,QAAQ,KAAK,gBAAgB,KAAK,eAAe;AACtD,SAAK,SAAS,KAAK,iBAAiB,KAAK,eAAe;AACxD,UAAM,UAAU,OAAO,MAAM,UAAU,OAAO,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,UAAU,OAAO,WAAW,OAAO,SAAS,UAAU,OAAO,OAAO,OAAO,QAAQ,KAAK,OAAO,UAAU,UAAU,OAAO,QAAQ,OAAO,QAAQ,KAAK;AAC/O,UAAM,UAAU,OAAO,MAAM,UAAU,OAAO,IAAI,OAAO,SAAS,KAAK,OAAO,YAAY,UAAU,OAAO,WAAW,OAAO,QAAQ,UAAU,OAAO,MAAM,OAAO,SAAS,KAAK,OAAO,WAAW,UAAU,OAAO,SAAS,OAAO,SAAS,KAAK;AAClP,SAAK,eAAe,UAAU,KAAK,QAAQ,SAAS,CAAC,KAAK,SAAS,OAAO;AAAA,EAC5E;AAAA,EACA,gBAAgB;AACd,UAAM,cAAc,KAAK,eAAe,EAAE,OAAO,CAAC,YAAY,QAAQ,SAAS,CAAC;AAChF,eAAW,OAAO,aAAa;AAC7B,WAAK,SAAS,IAAI,IAAI,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,QAAQ,GAAG,GAAG;AACZ,WAAO,OAAO,KAAK,KAAK,QAAQ,EAAE,OAAO,CAAC,gBAAgB,KAAK,MAAM,aAAa,GAAG,CAAC,CAAC;AAAA,EACzF;AAAA,EACA,MAAM,aAAa,GAAG,GAAG;AACvB,QAAI,CAAC,KAAK,SAAS,WAAW,GAAG;AAC/B,aAAO;AAAA,IACT;AACA,UAAM,YAAY,KAAK,SAAS,WAAW,EAAE;AAC7C,UAAM,SAAS,KAAK,kBAAkB,WAAW,UAAU;AAC3D,WAAO,OAAO,KAAK,KAAK,KAAK,OAAO,IAAI,OAAO,SAAS,OAAO,KAAK,KAAK,KAAK,OAAO,IAAI,OAAO;AAAA,EAClG;AAAA,EACA,kBAAkB,OAAO,QAAQ;AAC/B,UAAM,WAAW,KAAK,oBAAoB,KAAK;AAC/C,QAAI,OAAO,SAAS,CAAC;AACrB,QAAI,QAAQ,SAAS,CAAC;AACtB,QAAI,MAAM,SAAS,CAAC;AACpB,QAAI,SAAS,SAAS,CAAC;AACvB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,YAAM,KAAK,SAAS,CAAC;AACrB,YAAM,KAAK,SAAS,IAAI,CAAC;AACzB,aAAO,KAAK,IAAI,IAAI,IAAI;AACxB,cAAQ,KAAK,IAAI,IAAI,KAAK;AAC1B,YAAM,KAAK,IAAI,IAAI,GAAG;AACtB,eAAS,KAAK,IAAI,IAAI,MAAM;AAAA,IAC9B;AACA,cAAU,OAAO,SAAS,SAAS,CAAC;AACpC,WAAO,IAAI;AACX,WAAO,IAAI;AACX,WAAO,QAAQ,QAAQ;AACvB,WAAO,SAAS,SAAS;AACzB,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,WAAW;AACzB,SAAK,cAAc,SAAS,SAAS,EAAE,OAAO,KAAK,cAAc;AAAA,EACnE;AAAA,EACA,OAAO,IAAI,KAAK;AACd,SAAK,gBAAgB,OAAO,EAAE;AAAA,EAChC;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,KAAK,SAAS;AACnB,SAAK,cAAc,QAAQ;AAC3B,SAAK,gBAAgB;AAAA,EACvB;AACF;AACA,IAAM,QAAQ;AACd,IAAM,eAAN,cAA2B,MAAM;AAAA,EAC/B,YAAY,SAAS,MAAM,QAAQ,UAAU,OAAO;AAClD,UAAM,OAAO;AACb,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAM,aAAa,MAAM;AAAA,EACvB,OAAO,UAAU,QAAQ,MAAM,MAAM,QAAQ,SAAS;AACpD,UAAM,MAAM,IAAI,eAAe;AAC/B,eAAW,UAAU,IAAI,GAAG;AAC5B,QAAI,QAAQ;AACV,UAAI,SAAS,WAAW,OAAO,IAAI,MAAM;AACzC,UAAI,CAAC,QAAQ;AACX,iBAAyB,oBAAI,IAAI,CAAC,GAAG,CAAC;AACtC,mBAAW,OAAO,IAAI,QAAQ,MAAM;AAAA,MACtC,OAAO;AACL,eAAO,IAAI,GAAG;AAAA,MAChB;AACA,UAAI,CAAC,OAAO,UAAU,SAAS,EAAE,SAAS,WAAW,UAAU,GAAG;AAChE,eAAO,KAAK,WAAW,WAAW,UAAU;AAAA,MAC9C;AAAA,IACF;AACA,QAAI,KAAK,OAAO,IAAI;AACpB,QAAI,eAAe;AACnB,QAAI,SAAS,MAAM;AACjB,WAAK,IAAI,WAAW,OAAO,IAAI,WAAW,MAAM,IAAI,UAAU;AAC5D,eAAO,IAAI,QAAQ;AAAA,MACrB,OAAO;AACL,YAAI,QAAQ;AAAA,MACd;AAAA,IACF;AACA,QAAI,UAAU,MAAM;AAClB,aAAO,KAAK,OAAO,8BAA8B,IAAI,YAAY,YAAY,IAAI,MAAM,MAAM,IAAI,EAAE;AACnG,cAAQ,IAAI,aAAa,kBAAkB,MAAM,IAAI,MAAM,CAAC;AAAA,IAC9D;AACA,QAAI,UAAU,MAAM,QAAQ,IAAI,aAAa,YAAY,MAAM,IAAI,QAAQ,IAAI,CAAC;AAChF,QAAI,YAAY,MAAM;AACpB,UAAI;AACJ,iBAAW,UAAU,OAAO,GAAG;AAC/B,UAAI,QAAQ;AACV,SAAC,KAAK,WAAW,OAAO,IAAI,MAAM,MAAM,OAAO,SAAS,GAAG,OAAO,GAAG;AAAA,MACvE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,aAAa;AAClB,QAAI;AACJ,KAAC,KAAK,WAAW,OAAO,IAAI,IAAI,MAAM,OAAO,SAAS,GAAG,QAAQ,CAAC,QAAQ;AACxE,UAAI,MAAM;AACV,iBAAW,UAAU,OAAO,GAAG;AAAA,IACjC,CAAC;AACD,eAAW,OAAO,OAAO,IAAI;AAAA,EAC/B;AAAA,EACA,OAAO,UAAU;AACf,eAAW,UAAU,QAAQ,CAAC,QAAQ,IAAI,MAAM,CAAC;AACjD,eAAW,UAAU,MAAM;AAC3B,eAAW,SAAyB,oBAAI,QAAQ;AAAA,EAClD;AACF;AACA,IAAI,YAAY;AAChB,UAAU,SAAyB,oBAAI,QAAQ;AAC/C,UAAU,YAA4B,oBAAI,IAAI;AAC9C,UAAU,SAAS,CAAC,SAAS,SAAS;AACpC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,MAAM,WAAW,UAAU,QAAQ,QAAQ,QAAQ,WAAW,QAAQ,SAAS,WAAW,QAAQ,GAAG,IAAI,QAAQ,KAAK,QAAQ,MAAM,CAAC,SAAS;AAClJ,cAAQ,SAAS;AACjB,cAAQ;AAAA,IACV,GAAG,MAAM;AACT,QAAI,KAAK;AAAA,EACX,CAAC;AACH;AACA,SAAS,eAAe,YAAY,SAAS;AAC3C,MAAI,QAAQ;AACZ,SAAO,SAAS,CAAC;AACjB,WAAS,SAAS,GAAG,KAAK;AACxB,QAAI;AACF,aAAO,QAAQ,OAAO,GAAG;AAC3B,QAAI,KAAK;AACP,aAAO,QAAQ,OAAO,IAAI,MAAM,8BAA8B,CAAC;AACjE,YAAQ;AACR,UAAM,KAAK,WAAW,CAAC;AACvB,QAAI,CAAC;AACH,aAAO,QAAQ,QAAQ;AACzB,QAAI;AACF,aAAO,QAAQ,QAAQ,GAAG,SAAS,SAAS,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC;AAAA,IAChE,SAAS,MAAM;AACb,aAAO,QAAQ,OAAO,IAAI;AAAA,IAC5B;AAAA,EACF;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,OAAO,KAAK,SAAS;AACnB,WAAO,eAAe,KAAK,aAAa,OAAO,EAAE,KAAK,MAAM,QAAQ,MAAM;AAAA,EAC5E;AACF;AACA,aAAa,cAAc,CAAC,UAAU,MAAM;AAC5C,SAAS,cAAc,MAAM,UAAU,CAAC,GAAG;AACzC,MAAI;AACJ,QAAM,iBAAiB,EAAE,iBAAiB,EAAE,aAAa,QAAQ,YAAY,EAAE;AAC/E,MAAI,QAAQ,SAAS;AACnB,WAAO,QAAQ,QAAQ,MAAM,cAAc,EAAE,MAAM,CAAC,MAAM;AACxD,UAAI,aAAa,OAAO;AACtB,cAAM;AAAA,MACR;AACA,YAAM,MAAM,IAAI,MAAM,uBAAuB;AAC7C,UAAI,QAAQ;AACZ,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,iBAAe,gBAAgB,WAAW;AAC1C,QAAM,UAAU,QAAQ,KAAK,MAAM,cAAc;AACjD,MAAI,QAAQ,YAAY,OAAO;AAC7B,WAAO,QAAQ,QAAQ,OAAO;AAAA,EAChC;AACA,QAAM,WAAW,QAAQ,YAAY;AACrC,GAAC,KAAK,SAAS,iBAAiB,OAAO,KAAK,SAAS,eAAe,IAAI,QAAQ,CAAC,SAAS,WAAW;AACnG,UAAM,eAAe,CAAC,UAAU;AAC9B,eAAS,OAAO,oBAAoB,SAAS,YAAY;AACzD,YAAM,MAAM,IAAI,MAAM,uBAAuB;AAC7C,UAAI,QAAQ;AACZ,aAAO,GAAG;AAAA,IACZ;AACA,aAAS,OAAO,iBAAiB,SAAS,YAAY;AACtD,aAAS,KAAK,EAAE,KAAK,MAAM,QAAQ,OAAO,CAAC,EAAE,MAAM,YAAY;AAAA,EACjE,CAAC;AACD,SAAO,SAAS;AAClB;AACA,IAAM,MAAM;AACZ,IAAM,YAAY,CAAC,SAAS,SAAS,QAAQ,QAAQ,MAAM,aAAa;AACtE,MAAI,OAAO,QAAQ,WAAW,UAAU;AACtC,UAAM,OAAO,MAAM,aAAa,KAAK;AAAA,MACnC,KAAK,QAAQ;AAAA,MACb,MAAM;AAAA,MACN,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,SAAK,MAAM,QAAQ;AACnB,YAAQ,SAAS;AACjB,YAAQ,YAAY,KAAK,sBAAsB,IAAI;AAAA,EACrD;AACA,SAAO,KAAK;AACd,CAAC;AACD,IAAM,iBAAiB,CAAC,SAAS,SAAS,QAAQ,QAAQ,MAAM,aAAa;AAC3E,MAAI,QAAQ,kBAAkB,eAAe;AAC3C,YAAQ,WAAW,QAAQ;AAC3B,WAAO,KAAK;AAAA,EACd,WAAW,OAAO,QAAQ,WAAW,UAAU;AAC7C,UAAM,UAAU,cAAc,YAAY,QAAQ,MAAM;AACxD,QAAI,SAAS;AACX,YAAM,WAAW,QAAQ,oBAAoB,QAAQ,MAAM;AAC3D,cAAQ,WAAW;AACnB,cAAQ,YAAY,KAAK,kBAAkB,QAAQ;AACnD,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,IAAI,UAAU,0BAA0B;AAChD,CAAC;AACD,IAAM,iBAAiB,CAAC,SAAS,SAAS;AACxC,MAAI,QAAQ,UAAU;AACpB,UAAM,UAAU,cAAc,YAAY,QAAQ,QAAQ;AAC1D,QAAI,SAAS;AACX,aAAO,QAAQ,MAAM,EAAE,KAAK,IAAI;AAAA,IAClC;AAAA,EACF;AACA,SAAO,KAAK;AACd;AACA,IAAM,iBAAiB,CAAC,SAAS,SAAS,QAAQ,QAAQ,MAAM,aAAa;AAC3E,QAAM,KAAK;AACX,QAAM,gBAAgB,QAAQ;AAC9B,MAAI,eAAe;AACjB,UAAM,WAAW,QAAQ;AACzB,UAAM,UAAU,cAAc,YAAY,QAAQ;AAClD,QAAI,SAAS;AACX,YAAM,QAAQ,CAAC;AACf,UAAI,SAAS,MAAM;AACjB,cAAM,KAAK,aAAa,KAAK;AAAA,UAC3B;AAAA,UACA,KAAK,SAAS;AAAA,UACd,MAAM;AAAA,UACN,QAAQ;AAAA,QACV,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,wBAAc,OAAO,QAAQ,WAAW,cAAc,WAAW,IAAI;AACrE,kBAAQ,YAAY,KAAK,cAAc,cAAc,IAAI;AAAA,QAC3D,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,kBAAQ,YAAY,KAAK,iBAAiB,CAAC;AAC3C,iBAAO,KAAK,KAAK,wBAAwB,CAAC;AAAA,QAC5C,CAAC,CAAC;AAAA,MACJ;AACA,UAAI,SAAS,SAAS;AACpB,cAAM,KAAK,aAAa,KAAK;AAAA,UAC3B;AAAA,UACA,KAAK,SAAS;AAAA,UACd,MAAM;AAAA,UACN,QAAQ;AAAA,QACV,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,wBAAc,UAAU,QAAQ,cAAc,cAAc,WAAW,IAAI;AAC3E,kBAAQ,YAAY,KAAK,iBAAiB,cAAc,OAAO;AAAA,QACjE,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,kBAAQ,YAAY,KAAK,oBAAoB,CAAC;AAC9C,iBAAO,KAAK,KAAK,2BAA2B,CAAC;AAAA,QAC/C,CAAC,CAAC;AAAA,MACJ;AACA,UAAI,MAAM,QAAQ;AAChB,cAAM,QAAQ,IAAI,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,IAAM,kBAAkB,CAAC,SAAS,SAAS,QAAQ,QAAQ,MAAM,aAAa;AAC5E,MAAI,QAAQ,UAAU;AACpB,UAAM,cAAc,QAAQ;AAC5B,UAAM,kBAAkB,QAAQ,SAAS,SAAS,IAAI,CAAC,QAAQ;AAC7D,YAAM,OAAO,QAAQ,SAAS,WAAW,GAAG;AAC5C,aAAO,cAAc,MAAM,EAAE,aAAa,QAAQ,QAAQ,YAAY,CAAC;AAAA,IACzE,CAAC;AACD,UAAM,KAAK;AACX,QAAI,QAAQ,eAAe;AACzB,kBAAY,gBAAgB,QAAQ;AACpC,kBAAY,KAAK,eAAe,QAAQ,aAAa;AAAA,IACvD,OAAO;AACL,YAAM,IAAI,UAAU,yBAAyB;AAAA,IAC/C;AACA,gBAAY,WAAW,MAAM,QAAQ,IAAI,eAAe;AACxD,gBAAY,KAAK,iBAAiB,YAAY,QAAQ;AAAA,EACxD,OAAO;AACL,UAAM,IAAI,UAAU,mBAAmB;AAAA,EACzC;AACF,CAAC;AACD,IAAM,sBAAsB,CAAC,SAAS,SAAS,QAAQ,QAAQ,MAAM,aAAa;AAChF,QAAM,WAAW,QAAQ;AACzB,MAAI,oBAAoB,eAAe;AACrC,UAAM,UAAU,cAAc,YAAY,QAAQ;AAClD,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,UAAU,yBAAyB;AAAA,IAC/C;AACA,UAAM,YAAY,MAAM,aAAa,KAAK;AAAA,MACxC;AAAA,MACA,KAAK,SAAS;AAAA,MACd,MAAM;AAAA,MACN,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,QAAI,CAAC,QAAQ,WAAW,SAAS,GAAG;AAClC,YAAM,IAAI,MAAM,kBAAkB;AAAA,IACpC;AACA,UAAM,YAAY,QAAQ,gBAAgB,SAAS;AACnD,YAAQ,gBAAgB,QAAQ,oBAAoB,WAAW,UAAU,QAAQ,OAAO;AACxF,WAAO,KAAK;AAAA,EACd;AACA,QAAM,IAAI,UAAU,mBAAmB;AACzC,CAAC;AACD,IAAM,iBAAiB,MAAM;AAAA,EAC3B,OAAO,gBAAgB,SAAS;AAC9B,mBAAe,SAAS,KAAK,OAAO;AACpC,mBAAe,SAAS,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,EAAE,OAAO;AAAA,EAC9D;AAAA,EACA,OAAO,YAAY,QAAQ;AACzB,eAAW,WAAW,eAAe,UAAU;AAC7C,UAAI,QAAQ,KAAK,MAAM,GAAG;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,iBAAiB,aAAa,QAAQ,SAAS;AACpD,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,gBAAgB,IAAI,QAAQ,CAAC,YAAY,YAAY,KAAK,iBAAiB,OAAO,CAAC;AACzF,YAAM,cAAc,IAAI,QAAQ,CAAC,YAAY,YAAY,KAAK,eAAe,OAAO,CAAC;AACrF,YAAM,oBAAoB,QAAQ,IAAI,CAAC,eAAe,WAAW,CAAC,EAAE,KAAK,MAAM,YAAY,KAAK,OAAO,CAAC;AACxG,YAAM,eAAe,eAAe,wBAAwB;AAAA,QAC1D;AAAA,QACA;AAAA,QACA,SAAS,WAAW,CAAC;AAAA,MACvB,CAAC;AACD,YAAM;AACN,kBAAY,KAAK,MAAM;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,WAAW,eAAe,OAAO,OAAO;AAC7C,QAAI,IAAI;AACR,UAAM,cAAc,CAAC,MAAM,cAAc,KAAK,mBAAmB,OAAO,OAAO,CAAC;AAChF,QAAI;AACF,YAAM,cAAc,KAAK,cAAc,YAAY,KAAK,MAAM,OAAO,SAAS,GAAG,KAAK;AACtF,UAAI,CAAC,YAAY;AACf,eAAO,QAAQ,QAAQ,MAAM;AAAA,MAC/B;AACA,UAAI,CAAC,cAAc,UAAU,SAAS,EAAE,SAAS,eAAe,YAAY,GAAG;AAC7E,sBAAc,KAAK,WAAW,eAAe,YAAY;AAAA,MAC3D;AACA,UAAI,QAAQ,eAAe,eAAe,IAAI,aAAa;AAC3D,UAAI,CAAC,OAAO;AACV,gBAAQ,CAAC;AACT,uBAAe,eAAe,IAAI,eAAe,KAAK;AAAA,MACxD;AACA,UAAI,YAAY,MAAM,KAAK;AAC3B,UAAI,CAAC,WAAW;AACd,oBAAY,CAAC;AACb,cAAM,KAAK,IAAI;AAAA,MACjB;AACA,YAAM,OAAO,cAAc,cAAc,UAAU;AACnD,OAAC,KAAK,UAAU,KAAK,MAAM,OAAO,KAAK,UAAU,KAAK,IAAI,aAAa,KAAK;AAAA,QAC1E,KAAK;AAAA,QACL,UAAU,cAAc;AAAA,QACxB,MAAM,cAAc;AAAA,QACpB,QAAQ;AAAA,MACV,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,YAAI;AACJ,cAAM,cAAc,MAAM,eAAe,eAAe,IAAI,aAAa,MAAM,OAAO,SAAS,IAAI,KAAK;AACxG,YAAI,YAAY;AACd,iBAAO,WAAW,KAAK;AAAA,QACzB;AACA,cAAM,SAAS,cAAc,aAAa,MAAM,OAAO,UAAU;AACjE,sBAAc,KAAK,gBAAgB,OAAO,OAAO,MAAM;AACvD,eAAO;AAAA,MACT,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,eAAO,KAAK,cAAc,KAAK,0BAA0B,IAAI;AAAA,GAClE,CAAC;AACI,oBAAY,CAAC;AAAA,MACf,CAAC;AACD,aAAO,UAAU,KAAK;AAAA,IACxB,SAAS,GAAG;AACV,aAAO,KAAK,cAAc,KAAK,6BAA6B,KAAK,KAAK,KAAK;AAAA,GAC9E,CAAC;AACE,kBAAY,CAAC;AAAA,IACf;AACA,WAAO,QAAQ,QAAQ,MAAM;AAAA,EAC/B;AAAA,EACA,OAAO,eAAe,mBAAmB,OAAO;AAC9C,QAAI;AACJ,UAAM,cAAc,CAAC,MAAM,kBAAkB,KAAK,uBAAuB,OAAO,CAAC;AACjF,QAAI;AACF,YAAM,aAAa,kBAAkB,YAAY,KAAK;AACtD,UAAI,CAAC,YAAY;AACf,eAAO,QAAQ,QAAQ,MAAM;AAAA,MAC/B;AACA,UAAI,CAAC,kBAAkB,UAAU,SAAS,EAAE,SAAS,eAAe,YAAY,GAAG;AACjF,0BAAkB,KAAK,WAAW,eAAe,YAAY;AAAA,MAC/D;AACA,UAAI,QAAQ,eAAe,mBAAmB,IAAI,iBAAiB;AACnE,UAAI,CAAC,OAAO;AACV,gBAAQ,CAAC;AACT,uBAAe,mBAAmB,IAAI,mBAAmB,KAAK;AAAA,MAChE;AACA,YAAM,OAAO,kBAAkB,kBAAkB,UAAU;AAC3D,OAAC,KAAK,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM,KAAK,IAAI,aAAa,KAAK;AAAA,QAClE,KAAK;AAAA,QACL,UAAU,kBAAkB;AAAA,QAC5B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,cAAM,SAAS,eAAe,mBAAmB,IAAI,iBAAiB;AACtE,YAAI,QAAQ;AACV,iBAAO,OAAO,KAAK;AAAA,QACrB;AACA,cAAM,aAAa,kBAAkB,iBAAiB,MAAM,UAAU;AACtE,0BAAkB,KAAK,oBAAoB,OAAO,UAAU;AAC5D,eAAO;AAAA,MACT,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,eAAO,KAAK,kBAAkB,KAAK,8BAA8B,IAAI;AAAA,GAC1E,CAAC;AACI,oBAAY,CAAC;AAAA,MACf,CAAC;AACD,aAAO,MAAM,KAAK;AAAA,IACpB,SAAS,GAAG;AACV,aAAO,KAAK,kBAAkB,KAAK,iCAAiC,KAAK;AAAA,GAC5E,CAAC;AACE,kBAAY,CAAC;AAAA,IACf;AACA,WAAO,QAAQ,QAAQ,MAAM;AAAA,EAC/B;AAAA,EACA,OAAO,eAAe;AACpB,QAAI,gBAAgB,eAAe;AACjC,qBAAe,eAAe,OAAO,IAAI;AAAA,IAC3C,OAAO;AACL,qBAAe,mBAAmB,OAAO,IAAI;AAAA,IAC/C;AAAA,EACF;AACF;AACA,IAAI,gBAAgB;AACpB,cAAc,WAAW,CAAC;AAC1B,cAAc,YAAY;AAC1B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,kBAAkB;AAChC,cAAc,sBAAsB;AACpC,cAAc,yBAAyB;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,cAAc,iBAAiC,oBAAI,QAAQ;AAC3D,cAAc,qBAAqC,oBAAI,QAAQ;AAC/D,cAAc,UAAU,aAAa,IAAI,SAAS,OAAO,OAAO;AAC9D,SAAO,cAAc,WAAW,MAAM,OAAO,KAAK;AACpD;AACA,kBAAkB,UAAU,iBAAiB,IAAI,SAAS,OAAO;AAC/D,SAAO,cAAc,eAAe,MAAM,KAAK;AACjD;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,cAAc;AACZ,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,cAAc;AAC7B,QAAI,iBAAiB,KAAK,eAAe;AACvC,UAAI,cAAc;AAChB,aAAK,GAAG,cAAc,OAAO,IAAI;AAAA,MACnC,OAAO;AACL,aAAK,IAAI,cAAc,OAAO,IAAI;AAAA,MACpC;AACA,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS;AAC3B,QAAI,YAAY,KAAK,oBAAoB;AACvC,WAAK,sBAAsB;AAC3B,UAAI,KAAK,iBAAiB,SAAS;AACjC,aAAK,qBAAqB;AAC1B,gBAAQ,GAAG,eAAe,eAAe,IAAI;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,QAAI;AACJ,QAAI,KAAK,oBAAoB;AAC3B,OAAC,KAAK,KAAK,uBAAuB,OAAO,SAAS,GAAG,IAAI,eAAe,eAAe,IAAI;AAC3F,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AACF;AACA,SAAS,MAAM,OAAO;AACpB,OAAK,IAAI,MAAM,KAAK,OAAO,GAAG,MAAM,KAAK,OAAO,CAAC;AACnD;AACA,SAAS,cAAc,OAAO;AAC5B,OAAK,MAAM,MAAM,KAAK,OAAO,GAAG,MAAM,KAAK,OAAO,CAAC;AACrD;AACA,IAAM,kBAAN,cAA8B,UAAU;AACxC;AACA,IAAM,YAAY,IAAI,MAAM;AAC5B,IAAM,eAAe,IAAI,OAAO;AAChC,IAAI;AACJ,IAAM,cAAN,cAA0B,UAAU;AAAA,EAClC,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,MAAM;AACX,SAAK,WAAW,CAAC;AACjB,SAAK,YAAY,IAAI,gBAAgB;AACrC,SAAK,SAAS,IAAI,gBAAgB,KAAK,gBAAgB,MAAM,GAAG,CAAC;AACjE,SAAK,cAAc;AACnB,SAAK,cAAc,YAAY,IAAI;AACnC,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,KAAK,eAAe,MAAM,KAAK,KAAK,OAAO,CAAC;AAAA,EACnD;AAAA,EACA,OAAO,KAAK,QAAQ,SAAS;AAC3B,UAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,WAAO,cAAc,iBAAiB,OAAO,QAAQ,OAAO,EAAE,KAAK,MAAM,KAAK;AAAA,EAChF;AAAA,EACA,OAAO,SAAS,QAAQ,SAAS;AAC/B,UAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,kBAAc,iBAAiB,OAAO,QAAQ,OAAO,EAAE,KAAK,WAAW,OAAO,SAAS,QAAQ,MAAM,EAAE,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AACvJ,WAAO;AAAA,EACT;AAAA,EACA,OAAO,eAAe,aAAa;AACjC,gBAAY;AAAA,EACd;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,QAAI;AACJ,kBAAc,aAAa,KAAK,OAAO,SAAS,OAAO,SAAS,GAAG;AACnE,QAAI,YAAY;AACd,UAAI,CAAC,KAAK,YAAY;AACpB,YAAI,WAAW;AACb,oBAAU,OAAO,IAAI,KAAK,gBAAgB,IAAI;AAC9C,eAAK,cAAc;AAAA,QACrB,OAAO;AACL,iBAAO,KAAK,KAAK,KAAK,uEAAuE;AAAA,QAC/F;AAAA,MACF;AAAA,IACF,OAAO;AACL,mBAAa,OAAO,SAAS,UAAU,OAAO,OAAO,KAAK,gBAAgB,IAAI;AAC9E,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,KAAK,SAAS;AACZ,SAAK,MAAM,eAAe,KAAK,cAAc,SAAS,IAAI;AAC1D,UAAM,WAAW,OAAO,OAAO;AAAA,MAC7B,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,GAAG,OAAO;AACV,QAAI,SAAS,cAAc;AACzB,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,eAAe,SAAS;AAC7B,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,iBAAiB;AACf,SAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,cAAc,OAAO,KAAK,OAAO,IAAI,KAAK,cAAc,MAAM;AAAA,EACpG;AAAA,EACA,OAAO,OAAO,OAAO,UAAU;AAC7B,WAAO,UAAU,SAAS,KAAK,cAAc,cAAc,kBAAkB,OAAO,QAAQ,IAAI,KAAK,cAAc,cAAc,YAAY,OAAO,OAAO,QAAQ;AAAA,EACrK;AAAA,EACA,WAAW,IAAI;AACb,QAAI,KAAK,cAAc,cAAc,mBAAmB;AACtD,aAAO,OAAO,SAAS,KAAK,cAAc,cAAc,kBAAkB,oBAAoB,IAAI,KAAK,cAAc,cAAc,kBAAkB,cAAc,EAAE;AAAA,IACvK;AACA,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACA,MAAM,GAAG,GAAG,UAAU,OAAO;AAC3B,cAAU,IAAI;AACd,cAAU,IAAI;AACd,SAAK,gBAAgB,WAAW,WAAW,IAAI;AAC/C,QAAI,KAAK,UAAU,IAAI,KAAK,cAAc,gBAAgB,IAAI;AAC9D,QAAI,KAAK,UAAU,IAAI,KAAK,cAAc,iBAAiB,IAAI;AAC/D,QAAI,SAAS,KAAK,MAAM,IAAI,EAAE;AAC9B,SAAK,cAAc,gBAAgB,MAAM,KAAK,IAAI,MAAM,GAAG,CAAC,KAAK,IAAI,MAAM,GAAG,OAAO;AAAA,EACvF;AAAA,EACA,IAAI,GAAG,GAAG;AACR,UAAM,eAAe,KAAK,QAAQ,GAAG,CAAC;AACtC,QAAI,aAAa,QAAQ;AACvB,aAAO,IAAI,KAAK,KAAK,OAAO,YAAY;AACxC,WAAK,KAAK,OAAO,YAAY;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,QAAQ,GAAG,GAAG;AACZ,cAAU,IAAI;AACd,cAAU,IAAI;AACd,SAAK,gBAAgB,WAAW,SAAS;AACzC,WAAO,KAAK,cAAc,QAAQ,UAAU,GAAG,UAAU,CAAC;AAAA,EAC5D;AAAA,EACA,gBAAgB,UAAU,SAAS,SAAS,MAAM,GAAG,YAAY;AAC/D,QAAI,CAAC,YAAY;AACf,WAAK,8BAA8B;AACnC,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,SAAS,KAAK;AACnB,aAAK,6BAA6B;AAClC,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AACA,SAAK,UAAU,eAAe,aAAa,UAAU,MAAM;AAC3D,SAAK,cAAc,eAAe,aAAa,QAAQ,MAAM;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,UAAU,IAAI,EAAE,SAAS,MAAM,GAAG,MAAM,CAAC;AAAA,EACvD;AAAA,EACA,mBAAmB;AACjB,SAAK,QAAQ,SAAS,KAAK,WAAW,GAAG,GAAG,KAAK,cAAc,OAAO,KAAK,cAAc,MAAM;AAAA,EACjG;AAAA,EACA,iBAAiB;AACf,SAAK,OAAO,UAAU,OAAO,OAAO;AAAA,EACtC;AAAA,EACA,OAAO,IAAI;AACT,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,QAAQ,UAAU;AAChB,SAAK,oBAAoB,SAAS,QAAQ,WAAW;AACrD,aAAS,MAAM,MAAM;AACrB,aAAS,SAAS,MAAM;AACxB,aAAS,OAAO,MAAM;AACtB,aAAS,MAAM,MAAM;AACrB,QAAI,sBAAsB;AAC1B,QAAI,KAAK,gBAAgB,SAAS,aAAa;AAC7C,WAAK,cAAc,SAAS;AAC5B,WAAK,cAAc,mBAAmB,SAAS,IAAI,KAAK,WAAW;AACnE,4BAAsB;AAAA,IACxB;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,UAAU,KAAK,SAAS,CAAC;AAC/B,UAAI,CAAC,QAAQ,OAAO;AAClB;AAAA,MACF;AACA,UAAI,uBAAuB,CAAC,QAAQ,YAAY,YAAY,KAAK,WAAW,GAAG;AAC7E,iBAAS,GAAG,YAAY,sBAAsB,qBAAqB,KAAK,cAAc,YAAY;AAClG,iBAAS,QAAQ,KAAK,QAAQ,aAAa,CAAC;AAAA,MAC9C;AACA,WAAK,cAAc,YAAY,GAAG,QAAQ,YAAY,YAAY,KAAK,WAAW,EAAE,OAAO;AAC3F,cAAQ,YAAY,UAAU,SAAS,UAAU;AAAA,IACnD;AACA,UAAM,WAAW,SAAS,YAAY;AACtC,SAAK,cAAc,WAAW,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,OAAO,SAAS,MAAM;AACtF,QAAI,KAAK,WAAW;AAClB,WAAK,cAAc,OAAO,KAAK,WAAW,KAAK,WAAW;AAC1D,WAAK,YAAY;AAAA,IACnB;AACA,UAAM,oBAAoB,aAAa,SAAS,SAAS,eAAe,SAAS,gBAAgB,EAAE,OAAO,KAAK,cAAc;AAC7H,SAAK,cAAc,gBAAgB,iBAAiB;AACpD,SAAK,cAAc,KAAK,SAAS,EAAE;AACnC,aAAS,MAAM,MAAM;AACrB,aAAS,QAAQ,MAAM;AAAA,EACzB;AAAA,EACA,QAAQ,SAAS;AACf,SAAK,KAAK,SAAS;AACnB,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAC3B,QAAI,WAAW,OAAO,SAAS,QAAQ,SAAS;AAC9C,WAAK,SAAS,QAAQ,CAAC,YAAY,QAAQ,QAAQ,QAAQ,WAAW,CAAC;AAAA,IACzE;AACA,SAAK,cAAc,QAAQ;AAC3B,UAAM,QAAQ,OAAO;AAAA,EACvB;AACF;AACA,YAAY,aAAa,CAAC,gBAAgB,CAAC;AAC3C,IAAM,cAAc,MAAM;AAAA,EACxB,OAAO,WAAW,aAAa,UAAU;AACvC,QAAI;AACJ,UAAM,YAAY,KAAK,YAAY,SAAS,WAAW,MAAM,OAAO,SAAS,GAAG,QAAQ;AACxF,QAAI,aAAa,QAAQ;AACvB,YAAM,IAAI,MAAM,gDAAgD,QAAQ;AAAA,IAC1E;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,OAAO,UAAU;AAC7B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,UAAU,CAAC;AACjB,iBAAW,eAAe,SAAS,gBAAgB,GAAG;AACpD,cAAM,aAAa,UAAU,IAAI,QAAQ,SAAS,KAAK,WAAW,CAAC;AACnE,cAAM,aAAa,MAAM,KAAK,CAAC,SAAS,KAAK,uBAAuB,UAAU;AAC9E,YAAI,YAAY;AACd,kBAAQ,WAAW,IAAI,IAAI,gBAAgB,UAAU;AAAA,QACvD;AAAA,MACF;AACA,kBAAY,SAAS,SAAS,UAAU,IAAI;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,OAAO,eAAe,OAAO;AAC3B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,eAAe,MAAM,KAAK,CAAC,SAAS,KAAK,KAAK,SAAS,YAAY,KAAK,KAAK,KAAK,SAAS,aAAa,CAAC;AAC/G,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC/C;AACA,YAAM,eAAe,MAAM,YAAY,SAAS,YAAY;AAC5D,YAAM,eAAe,KAAK,MAAM,YAAY;AAC5C,mBAAa,MAAM,aAAa;AAChC,YAAM,UAAU,cAAc,YAAY,YAAY;AACtD,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AACA,YAAM,WAAW,QAAQ,oBAAoB,YAAY;AACzD,eAAS,aAAa,IAAI,gBAAgB,YAAY;AACtD,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS,MAAM;AACpB,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAM,SAAS,IAAI,WAAW;AAC9B,eAAO,SAAS,MAAM,QAAQ,OAAO,MAAM;AAC3C,eAAO,UAAU;AACjB,eAAO,WAAW,MAAM,MAAM;AAAA,MAChC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAI,aAAa;AACjB,WAAW,WAAW,CAAC;AACvB,WAAW,UAAU,CAAC,SAAS,SAAS,QAAQ,QAAQ,MAAM,aAAa;AACzE,MAAI,MAAM,QAAQ,QAAQ,MAAM,KAAK,QAAQ,OAAO,CAAC,aAAa,MAAM;AACtE,UAAM,QAAQ,QAAQ;AACtB,QAAI,WAAW,MAAM;AACrB,QAAI,CAAC,UAAU;AACb,iBAAW,MAAM,YAAY,eAAe,KAAK;AAAA,IACnD,WAAW,CAAC,SAAS,YAAY;AAC/B,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AACA,aAAS,cAAc,MAAM,IAAI,CAAC,SAAS,UAAU,KAAK,kBAAkB,CAAC,CAAC;AAC9E,UAAM,YAAY,OAAO,OAAO,QAAQ;AACxC,aAAS,aAAa,SAAS,MAAM;AACnC,aAAO,YAAY,WAAW,KAAK,YAAY,IAAI;AAAA,IACrD;AACA,YAAQ,SAAS;AACjB,YAAQ,YAAY,KAAK,eAAe,CAAC,kBAAkB;AACzD,oBAAc,KAAK,WAAW,WAAW;AACvC,cAAM,YAAY,KAAK,SAAS;AAChC,YAAI,gBAAgB,SAAS;AAC7B,YAAI,YAAY,SAAS,SAAS,GAAG;AACnC,qBAAW,qBAAqB,OAAO,OAAO,YAAY,SAAS,SAAS,CAAC,GAAG;AAC9E,gBAAI,gBAAgB,iBAAiB;AAAA,UACvC;AAAA,QACF;AACA,eAAO,YAAY,SAAS,SAAS;AAAA,MACvC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO,KAAK;AACd,CAAC;AACD,cAAc,uBAAuB,QAAQ,WAAW,OAAO;AAC/D,IAAM,aAAa,MAAM;AAAA,EACvB,OAAO,MAAM,QAAQ,UAAU;AAC7B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,YAAY,MAAM,WAAW,aAAa,MAAM;AACtD,YAAM,oBAAoB,CAAC;AAC3B,iBAAW,eAAe,SAAS,gBAAgB,GAAG;AACpD,cAAM,aAAa,UAAU,IAAI,QAAQ,SAAS,KAAK,WAAW,CAAC;AACnE,YAAI,UAAU,SAAS,UAAU,GAAG;AAClC,4BAAkB,KAAK,UAAU;AAAA,QACnC;AAAA,MACF;AACA,YAAM,QAAQ,MAAM,WAAW,SAAS,QAAQ,iBAAiB;AACjE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,OAAO,kBAAkB,CAAC;AAChC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,eAAe,MAAM,sBAAsB;AAAA,UAChD,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,eAAe,QAAQ;AAC5B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,YAAY,MAAM,WAAW,aAAa,MAAM;AACtD,YAAM,mBAAmB,UAAU,KAAK,CAAC,SAAS,KAAK,SAAS,YAAY,KAAK,KAAK,SAAS,aAAa,CAAC;AAC7G,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AACA,YAAM,eAAe,MAAM,WAAW,SAAS,QAAQ,gBAAgB;AACvE,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,0BAA0B,gBAAgB;AAAA,MAC5D;AACA,YAAM,eAAe,KAAK,MAAM,YAAY;AAC5C,mBAAa,MAAM;AACnB,YAAM,UAAU,cAAc,YAAY,YAAY;AACtD,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AACA,aAAO,QAAQ,oBAAoB,YAAY;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,OAAO,UAAU,MAAM,MAAM;AAC3B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,aAAa,QAAQ;AAC1B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS,QAAQ,MAAM;AAC5B,WAAO,QAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,cAAc,QAAQ;AAAA,EAC7B;AACF;AACA,IAAI,YAAY;AAChB,UAAU,eAAe;AACzB,UAAU,MAAM;AAChB,UAAU,UAAU,CAAC,SAAS,SAAS,QAAQ,QAAQ,MAAM,aAAa;AACxE,QAAM,SAAS,QAAQ;AACvB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,WAAW,aAAa,OAAO,SAAS,MAAM,KAAK,OAAO,WAAW,WAAW,YAAY,IAAI;AACzG,QAAI,OAAO,WAAW,WAAW,YAAY,GAAG;AAC9C,kBAAY,OAAO,MAAM,WAAW,aAAa,MAAM;AAAA,IACzD,OAAO;AACL,kBAAY;AAAA,IACd;AACA,cAAU,MAAM,aAAa,KAAK;AAAA,MAChC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH,WAAW,MAAM,QAAQ,MAAM,KAAK,OAAO,WAAW,KAAK,OAAO,CAAC,aAAa,QAAQ,OAAO,CAAC,EAAE,KAAK,SAAS,MAAM,GAAG;AACvH,cAAU,OAAO,CAAC;AAClB,gBAAY,IAAI,gBAAgB,OAAO;AACvC,eAAW,OAAO;AAAA,EACpB;AACA,MAAI,SAAS;AACX,QAAI,CAAC,QAAQ,MAAM;AACjB,YAAM,IAAI,MAAM,gBAAgB;AAAA,IAClC;AACA,UAAM,SAAS,MAAM,WAAW,UAAU,SAAS,SAAS;AAC5D,QAAI,CAAC,UAAU;AACb,iBAAW,MAAM,WAAW,eAAe,MAAM;AAAA,IACnD;AACA,aAAS,aAAa,WAAW,eAAe,WAAW,MAAM,MAAM,SAAS;AAChF,UAAM,QAAQ,MAAM,WAAW,MAAM,QAAQ,QAAQ;AACrD,UAAM,WAAW;AACjB,YAAQ,SAAS;AACjB,QAAI,UAAU,WAAW,OAAO,GAAG;AACjC,cAAQ,YAAY,KAAK,eAAe,CAAC,kBAAkB;AACzD,sBAAc,KAAK,WAAW,WAAW;AACvC,cAAI,gBAAgB,SAAS;AAAA,QAC/B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,eAAW,cAAc,MAAM;AAAA,EACjC;AACA,SAAO,KAAK;AACd,CAAC;AACD,cAAc,uBAAuB,QAAQ,UAAU,OAAO;AAC9D,IAAI,CAAC,OAAO,QAAQ;AAClB,QAAM,IAAI,MAAM,mFAAmF;AACrG;AACA,IAAM,sBAAsB,aAAa,UAAU;AACnD,aAAa,UAAU,cAAc,SAAS,OAAO,OAAO;AAC1D,sBAAoB,KAAK,MAAM,OAAO,KAAK;AAC3C,MAAI,MAAM,WAAW,KAAK,KAAK,iBAAiB;AAC9C,SAAK,gBAAgB,IAAI;AACzB,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,mBAAN,cAA+B,QAAQ;AAAA,EACrC,YAAY,MAAM;AAChB,UAAM;AACN,SAAK,SAAS,CAAC;AACf,SAAK,UAAU,KAAK,UAAU,IAAI,KAAK,UAAU,OAAO,wBAAwB;AAChF,SAAK,WAAW,KAAK,WAAW,IAAI,KAAK,WAAW,OAAO,wBAAwB;AACnF,QAAI,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC9B,WAAK,OAAO,QAAQ,CAAC,UAAU;AAC7B,cAAM,OAAO,MAAM,QAAQ;AAC3B,YAAI,SAAS,OAAO;AAClB,gBAAM,eAAe,MAAM,OAAO;AAClC,gBAAM,OAAO;AAAA,QACf,WAAW,SAAS,QAAQ;AAC1B,gBAAM,eAAe,MAAM,OAAO;AAClC,gBAAM,OAAO;AAAA,QACf;AACA,aAAK,OAAO,KAAK;AAAA,UACf;AAAA,UACA,KAAK,MAAM;AAAA,UACX,IAAI,MAAM;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,OAAO,MAAM,QAAQ,gBAAgB;AAClD,SAAK,OAAO,QAAQ,CAAC,UAAU;AAC7B,YAAM,cAAc,MAAM,IAAI,MAAM,MAAM,MAAM;AAAA,IAClD,CAAC;AAAA,EACH;AACF;AACA,IAAM,2BAAN,cAAuC,kBAAkB;AAAA,EACvD,YAAY,UAAU,SAAS;AAC7B,QAAI;AACJ,UAAM,UAAU,OAAO;AACvB,SAAK,eAAe,IAAI,mBAAmB;AAC3C,SAAK,eAAe,KAAK,KAAK,SAAS,gBAAgB,OAAO,KAAK,CAAC;AACpE,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,aAAa;AACX,WAAO,KAAK,aAAa,WAAW;AAAA,EACtC;AAAA,EACA,mBAAmB,MAAM;AACvB,WAAO,KAAK,YAAY,UAAU,CAAC,QAAQ,IAAI,SAAS,IAAI;AAAA,EAC9D;AAAA,EACA,kBAAkB,YAAY;AAC5B,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB,MAAM,YAAY;AACjC,WAAO,IAAI,iBAAiB,IAAI;AAAA,EAClC;AAAA,EACA,eAAe,QAAQ;AACrB,WAAO,KAAK,aAAa,YAAY,MAAM;AAAA,EAC7C;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa,eAAe;AAAA,EACnC;AAAA,EACA,iBAAiB,OAAO,IAAI;AAC1B,WAAO,KAAK,aAAa,YAAY,KAAK;AAAA,EAC5C;AACF;AACA,IAAM,uBAAN,cAAmC,cAAc;AAAA,EAC/C,YAAY,UAAU,SAAS;AAC7B,UAAM,UAAU,OAAO;AACvB,SAAK,SAAS,EAAE,MAAM,OAAO;AAC7B,SAAK,iBAAiB;AACtB,SAAK,eAAe,IAAI,mBAAmB;AAC3C,SAAK,cAAc,KAAK,SAAS;AACjC,SAAK,KAAK,OAAO;AAAA,EACnB;AAAA,EACA,KAAK,SAAS;AACZ,UAAM,KAAK,OAAO;AAClB,QAAI,KAAK,SAAS,aAAa;AAC7B,WAAK,oBAAoB,IAAI,yBAAyB,KAAK,UAAU,OAAO;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK,aAAa,WAAW;AAAA,EACtC;AAAA,EACA,aAAa,MAAM,OAAO,YAAY;AACpC,UAAM,SAAS,aAAa,WAAW,IAAI;AAC3C,UAAM,wBAAwB,UAAU,KAAK,OAAO,OAAO,OAAO,2BAA2B,OAAO;AACpG,WAAO,UAAU,WAAW,UAAU,IAAI,WAAW,UAAU,qBAAqB;AACpF,WAAO,WAAW,WAAW,WAAW,IAAI,WAAW,WAAW,qBAAqB;AACvF,WAAO;AAAA,EACT;AAAA,EACA,cAAc,YAAY;AACxB,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,cAAc,YAAY;AACxB,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa,YAAY;AACvB,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa,QAAQ,UAAU;AAC7B,WAAO,kBAAkB;AACzB,SAAK,aAAa,eAAe;AACjC,WAAO,KAAK,aAAa,YAAY,MAAM;AAAA,EAC7C;AAAA,EACA,kBAAkB;AAChB,SAAK,aAAa,eAAe;AAAA,EACnC;AAAA,EACA,iBAAiB,OAAO,KAAK;AAC3B,WAAO,KAAK,aAAa,YAAY,KAAK;AAAA,EAC5C;AAAA,EACA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,eAAe;AAAA,EACtB;AACF;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,oBAAoB,KAAK;AAC9B,SAAK,YAAY,UAAU,cAAc,kBAAkB;AAC3D,SAAK,aAAa,UAAU,cAAc,kBAAkB;AAAA,EAC9D;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,gBAAgB,MAAM,OAAO,GAAG,CAAC;AACtC,SAAK,UAAU,cAAc,KAAK,WAAW,KAAK,aAAa;AAC/D,SAAK,UAAU,cAAc,KAAK,YAAY,KAAK,aAAa;AAAA,EAClE;AAAA,EACA,OAAO,IAAI;AACT,YAAQ,KAAK,UAAU;AAAA,MACrB,KAAK;AACH,aAAK,qBAAqB;AAC1B,YAAI,KAAK,oBAAoB,GAAG;AAC9B,eAAK,WAAW;AAChB,eAAK,oBAAoB,KAAK,gBAAgB,KAAK,kBAAkB,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,GAAG,GAAG;AAAA,QAC/H;AACA;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,gBAAgB,KAAK,KAAK,eAAe;AAChE,YAAI,KAAK,iBAAiB,GAAG;AAC3B,eAAK,WAAW;AAChB,eAAK,cAAc;AAAA,QACrB;AACA;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB,YAAI,KAAK,eAAe,KAAK,gBAAgB;AAC3C,eAAK,WAAW;AAAA,QAClB;AACA;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,gBAAgB,KAAK,KAAK,eAAe;AAChE,YAAI,KAAK,iBAAiB,GAAG;AAC3B,eAAK,WAAW;AAAA,QAClB;AAAA,IACJ;AAAA,EACF;AACF;AACA,IAAM,kBAAkB,IAAI,aAAa;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,uBAAN,cAAmC,cAAc;AAAA,EAC/C,YAAY,WAAW,UAAU,SAAS;AACxC,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,gBAAgB,IAAI,qBAAqB,UAAU,OAAO;AAC/D,SAAK,WAAW,IAAI,eAAe,SAAS;AAC5C,SAAK,qBAAqB,UAAU,cAAc,kBAAkB;AACpE,SAAK,qBAAqB,UAAU,cAAc,kBAAkB;AACpE,SAAK,mBAAmB,UAAU,cAAc,eAAe;AAC/D,SAAK,mBAAmB,UAAU,cAAc,eAAe;AAC/D,SAAK,mBAAmB,UAAU,cAAc,eAAe;AAC/D,SAAK,uBAAuB,UAAU,cAAc,oBAAoB;AACxE,SAAK,mBAAmB,UAAU,cAAc,cAAc;AAC9D,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,UAAM,KAAK;AACX,QAAI,KAAK,SAAS,YAAY;AAC5B,WAAK,SAAS,WAAW,QAAQ,CAAC,EAAE,IAAI,MAAM,MAAM,KAAK,UAAU,cAAc,IAAI,KAAK,CAAC;AAAA,IAC7F;AACA,QAAI,KAAK,SAAS,eAAe;AAC/B,WAAK,SAAS,cAAc,QAAQ,CAAC,EAAE,IAAI,MAAM,MAAM,KAAK,UAAU,gBAAgB,IAAI,KAAK,CAAC;AAAA,IAClG;AACA,SAAK,UAAU,UAAU;AACzB,UAAM,MAAM,KAAK,UAAU,gBAAgB,EAAE;AAC7C,QAAI,OAAO,OAAO,SAAS,IAAI,QAAQ;AACrC,WAAK,gBAAgB,IAAI;AAAA,IAC3B;AACA,QAAI,UAAU,KAAK,UAAU,eAAe;AAC5C,WAAO,eAAe,KAAK,UAAU,gBAAgB,WAAW;AAAA,MAC9D,KAAK,CAAC,MAAM,UAAU;AAAA,MACtB,KAAK,MAAM,KAAK,iBAAiB,QAAQ;AAAA,IAC3C,CAAC;AACD,UAAM,cAAc,KAAK,UAAU,gBAAgB,EAAE;AACrD,UAAM,oBAAoB,YAAY;AACtC,gBAAY,YAAY,CAAC,cAAc,cAAc;AACnD,wBAAkB,KAAK,aAAa,cAAc,SAAS;AAC3D,gBAAU,GAAG,SAAS,GAAG,KAAK,QAAQ;AAAA,IACxC;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,UAAU,eAAe,GAAG,KAAK,UAAU,gBAAgB,CAAC;AAAA,EAC3E;AAAA,EACA,YAAY;AACV,UAAM,SAAS,CAAC;AAChB,QAAI,KAAK,SAAS,QAAQ;AACxB,iBAAW,OAAO,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG;AACnD,YAAI,YAAY;AAChB,YAAI,QAAQ,YAAY;AACtB,sBAAY;AAAA,QACd,WAAW,QAAQ,YAAY;AAC7B,sBAAY;AAAA,QACd;AACA,eAAO,SAAS,IAAI,KAAK,SAAS,OAAO,GAAG;AAAA,MAC9C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,IAAI,aAAa;AAClC,UAAM,iBAAiB,KAAK,UAAU;AACtC,mBAAe,YAAY;AAC3B,mBAAe,MAAM,EAAE;AACvB,mBAAe,OAAO;AACtB,eAAW,QAAQ,gBAAgB;AACjC,UAAI,eAAe,eAAe,IAAI,KAAK,eAAe,IAAI,aAAa,aAAa;AACtF,uBAAe,IAAI,IAAI;AAAA,MACzB;AAAA,IACF;AACA,UAAM,cAAc,KAAK,UAAU,gBAAgB,EAAE;AACrD,gBAAY,aAAa;AACzB,UAAM,cAAc,GAAG,aAAa,GAAG,mBAAmB;AAC1D,gBAAY,qBAAqB;AACjC,OAAG,gBAAgB,GAAG,aAAa,WAAW;AAAA,EAChD;AAAA,EACA,YAAY,OAAO,SAAS;AAC1B,SAAK,UAAU,WAAW,OAAO,OAAO;AAAA,EAC1C;AAAA,EACA,iBAAiB;AACf,QAAI;AACJ,aAAS,KAAK,KAAK,SAAS,aAAa,OAAO,SAAS,GAAG,IAAI,CAAC,aAAa;AAAA,MAC5E,IAAI,QAAQ;AAAA,MACZ,MAAM,QAAQ;AAAA,MACd,OAAO,KAAK,UAAU,iBAAiB,QAAQ,EAAE;AAAA,IACnD,EAAE,MAAM,CAAC;AAAA,EACX;AAAA,EACA,iBAAiB;AACf,UAAM,eAAe,KAAK,UAAU,gBAAgB;AACpD,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,eAAe,KAAK;AAC3C,YAAM,WAAW,aAAa,YAAY,CAAC;AAC3C,UAAI,UAAU;AACZ,YAAI,KAAK,SAAS,cAAc,EAAE,EAAE;AAAA,MACtC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,IAAI;AACnB,WAAO,KAAK,UAAU,iBAAiB,EAAE;AAAA,EAC3C;AAAA,EACA,oBAAoB,WAAW;AAC7B,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,KAAK,UAAU,iBAAiB,SAAS;AACrD,UAAI,cAAc;AAChB,cAAM,IAAI,UAAU,iCAAiC,SAAS;AAAA,IAClE;AACA,WAAO,KAAK,UAAU,qBAAqB,SAAS,EAAE,MAAM;AAAA,EAC9D;AAAA,EACA,OAAO,IAAI,KAAK;AACd,QAAI,IAAI,IAAI,IAAI;AAChB,UAAM,OAAO,IAAI,GAAG;AACpB,UAAM,QAAQ,KAAK;AACnB,SAAK,KAAK,oBAAoB;AAC9B,UAAM,gBAAgB,KAAK,cAAc,OAAO,KAAK,WAAW,GAAG;AACnE,SAAK,KAAK,mBAAmB;AAC7B,UAAM,UAAU;AAChB,KAAC,KAAK,KAAK,cAAc,sBAAsB,OAAO,SAAS,GAAG,OAAO,OAAO,GAAG;AACnF,QAAI,CAAC,eAAe;AAClB,OAAC,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,OAAO,EAAE;AAAA,IACtD;AACA,SAAK,YAAY;AACjB,SAAK,uBAAuB,IAAI,GAAG;AACnC,KAAC,KAAK,KAAK,YAAY,OAAO,SAAS,GAAG,OAAO,GAAG;AACpD,KAAC,KAAK,KAAK,SAAS,OAAO,SAAS,GAAG,OAAO,EAAE;AAChD,SAAK,KAAK,mBAAmB;AAC7B,UAAM,OAAO;AACb,UAAM,UAAU;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,gBAAgB,KAAK,oBAAoB,KAAK,gBAAgB,CAAC;AAC9E,SAAK,UAAU,gBAAgB,KAAK,oBAAoB,KAAK,gBAAgB,CAAC;AAC9E,SAAK,UAAU,gBAAgB,KAAK,kBAAkB,KAAK,gBAAgB,IAAI,EAAE;AACjF,SAAK,UAAU,gBAAgB,KAAK,kBAAkB,KAAK,gBAAgB,IAAI,EAAE;AACjF,SAAK,UAAU,gBAAgB,KAAK,kBAAkB,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,GAAG;AAC3G,SAAK,UAAU,gBAAgB,KAAK,sBAAsB,KAAK,gBAAgB,IAAI,EAAE;AAAA,EACvF;AAAA,EACA,uBAAuB,IAAI,KAAK;AAC9B,UAAM,IAAI,MAAM,MAAM,IAAI,KAAK;AAC/B,SAAK,UAAU,gBAAgB,KAAK,kBAAkB,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,GAAG;AACrF,SAAK,UAAU,gBAAgB,KAAK,kBAAkB,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,GAAG;AACpF,SAAK,UAAU,gBAAgB,KAAK,kBAAkB,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,GAAG;AACrF,SAAK,UAAU,gBAAgB,KAAK,sBAAsB,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,GAAG;AACzF,SAAK,UAAU,cAAc,KAAK,kBAAkB,MAAM,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC;AAAA,EACtF;AAAA,EACA,KAAK,IAAI;AACP,UAAM,iBAAiB,KAAK;AAC5B,QAAI,GAAG,aAAa,GAAG,mBAAmB,GAAG;AAC3C,WAAK,iBAAiB;AAAA,IACxB;AACA,UAAM,SAAS,KAAK;AACpB,oBAAgB,CAAC,IAAI,OAAO;AAC5B,oBAAgB,CAAC,IAAI,OAAO;AAC5B,oBAAgB,CAAC,IAAI,OAAO;AAC5B,oBAAgB,CAAC,IAAI,OAAO;AAC5B,oBAAgB,EAAE,IAAI,OAAO;AAC7B,oBAAgB,EAAE,IAAI,OAAO;AAC7B,SAAK,UAAU,UAAU,eAAe;AACxC,SAAK,UAAU,KAAK;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,YAAY;AAAA,EACnB;AACF;AACA,IAAM,uBAAN,MAAM,8BAA6B,cAAc;AAAA,EAC/C,YAAY,MAAM;AAChB,UAAM,IAAI;AACV,SAAK,UAAU,CAAC;AAChB,QAAI,CAAC,sBAAqB,YAAY,IAAI,GAAG;AAC3C,YAAM,IAAI,UAAU,eAAe;AAAA,IACrC;AACA,SAAK,MAAM,KAAK;AAChB,cAAU,UAAU,MAAM,MAAM,YAAY,UAAU;AACtD,SAAK,KAAK,IAAI;AAAA,EAChB;AAAA,EACA,OAAO,YAAY,MAAM;AACvB,QAAI;AACJ,WAAO,CAAC,CAAC,QAAQ,OAAO,KAAK,UAAU,cAAc,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,UAAU,KAAK,KAAK,SAAS,MAAM,CAAC,SAAS,OAAO,SAAS,QAAQ;AAAA,EACtK;AAAA,EACA,KAAK,MAAM;AACT,iBAAa,UAAU,MAAM,MAAM,QAAQ,MAAM;AACjD,iBAAa,UAAU,MAAM,MAAM,QAAQ,MAAM;AACjD,iBAAa,UAAU,MAAM,MAAM,WAAW,SAAS;AACvD,iBAAa,UAAU,MAAM,MAAM,UAAU,QAAQ;AACrD,iBAAa,UAAU,MAAM,MAAM,WAAW,SAAS;AACvD,cAAU,UAAU,MAAM,MAAM,aAAa,UAAU;AACvD,cAAU,UAAU,MAAM,MAAM,eAAe,aAAa;AAC5D,cAAU,UAAU,MAAM,MAAM,eAAe,YAAY;AAC3D,cAAU,UAAU,MAAM,MAAM,kBAAkB,eAAe;AAAA,EACnE;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,aAAa,OAAO;AAC1B,eAAW,CAAC,OAAO,OAAO,KAAK,OAAO,QAAQ,KAAK,OAAO,GAAG;AAC3D,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAQ,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,EAAE,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;AACxE,YAAI,QAAQ,CAAC,EAAE,UAAU,QAAQ;AAC/B,kBAAQ,CAAC,EAAE,QAAQ,QAAQ,QAAQ,CAAC,EAAE,OAAO,WAAW,KAAK,IAAI,CAAC,SAAS;AAAA,QAC7E;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,aAAK,YAAY,CAAC,EAAE,OAAO,QAAQ,KAAK,YAAY,CAAC,EAAE,MAAM,eAAe,CAAC,QAAQ;AAAA,MACvF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,eAAe;AAAA,EACnB,GAAG,YAAY,IAAI;AAAA,EACnB,GAAG,YAAY,IAAI;AAAA,EACnB,OAAO,YAAY,IAAI;AACzB;AACA,IAAM,kBAAkB;AAAA,EACtB,GAAG,YAAY,IAAI;AAAA,EACnB,GAAG,YAAY,IAAI;AAAA,EACnB,OAAO,YAAY,IAAI;AACzB;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,WAAW,MAAM;AAC3B,SAAK,YAAY;AACjB,SAAK,eAAe,CAAC;AACrB,QAAI,KAAK,cAAc;AACrB,WAAK,eAAe,KAAK,aAAa,IAAI,CAAC,eAAe;AACxD,cAAM,cAAc,IAAI,YAAY;AACpC,oBAAY,MAAM,WAAW,MAAM,QAAQ,WAAW,MAAM,QAAQ,WAAW,MAAM,IAAI;AACzF,mBAAW,IAAI,QAAQ,CAAC,EAAE,IAAI,OAAO,OAAO,OAAO,MAAM;AACvD,gBAAM,OAAO,aAAa,KAAK;AAC/B,cAAI,MAAM;AACR,wBAAY,YAAY,MAAM,IAAI,OAAO,MAAM;AAAA,UACjD;AAAA,QACF,CAAC;AACD,mBAAW,QAAQ,QAAQ,CAAC,EAAE,IAAI,OAAO,OAAO,OAAO,MAAM;AAC3D,gBAAM,OAAO,gBAAgB,KAAK;AAClC,cAAI,MAAM;AACR,wBAAY,eAAe,MAAM,IAAI,OAAO,MAAM;AAAA,UACpD;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,SAAS;AACd,SAAK,aAAa,QAAQ,CAAC,gBAAgB,YAAY,OAAO,KAAK,WAAW,OAAO,CAAC;AAAA,EACxF;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,OAAO,CAAC;AAAA,EACf;AAAA,EACA,UAAU,OAAO;AACf,SAAK,aAAa,MAAM,cAAc,aAAa,KAAK,EAAE;AAC1D,SAAK,aAAa,MAAM,kBAAkB,YAAY,MAAM,KAAK,EAAE,CAAC;AACpE,UAAM,cAAc,KAAK,YAAY,CAAC;AAAA,EACxC;AACF;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,WAAW,MAAM;AAC3B,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAC3B,SAAK,cAAc,CAAC;AACpB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,KAAK,cAAc,IAAI,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE,IAAI,KAAK,MAAM;AACnF,cAAM,QAAQ,IAAI,iBAAiB,EAAE;AACrC,YAAI,MAAM;AACR,gBAAM,OAAO,KAAK,IAAI,CAAC,MAAM,IAAI,iBAAiB,CAAC,CAAC;AAAA,QACtD;AACA,eAAO;AAAA,MACT,CAAC,CAAC;AACF,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,YAAY,QAAQ,CAAC,UAAU;AAClC,YAAM,QAAQ,CAAC,UAAU;AACvB,cAAM,UAAU,KAAK,SAAS;AAC9B,YAAI,MAAM,cAAc,GAAG;AACzB,gBAAM,UAAU,KAAK,UAAU,cAAc,MAAM,UAAU,MAAM;AACnE,eAAK,UAAU,gBAAgB,MAAM,YAAY,UAAU,IAAI,CAAC;AAChE,eAAK,UAAU,cAAc,MAAM,YAAY,UAAU,IAAI,CAAC;AAC9D,cAAI,MAAM,KAAK,SAAS,GAAG;AACzB,kBAAM,KAAK,QAAQ,CAAC,MAAM,EAAE,UAAU,KAAK,SAAS,CAAC;AAAA,UACvD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,2BAA2B,YAAY,IAAI;AACzC,UAAM,QAAQ,KAAK;AACnB,UAAM,MAAM;AACZ,UAAM,iBAAiB;AACvB,QAAI,iBAAiB;AACrB,QAAI,eAAe,WAAW,UAAU,CAAC,EAAE,YAAY,WAAW,MAAM,cAAc,KAAK,MAAM,cAAc,UAAU,MAAM,CAAC;AAChI,QAAI,gBAAgB,GAAG;AACrB,YAAM,kBAAkB,MAAM,gBAAgB,WAAW,YAAY,EAAE,UAAU;AACjF,uBAAiB,MAAM,kBAAkB,KAAK,KAAK,qBAAqB,GAAG,CAAC;AAAA,IAC9E,OAAO;AACL,qBAAe;AACf,uBAAiB;AAAA,IACnB;AACA,eAAW,QAAQ,CAAC,EAAE,WAAW,GAAG,UAAU;AAC5C,UAAI,cAAc,GAAG;AACnB,YAAI,gBAAgB,OAAO;AACzB,gBAAM,gBAAgB,YAAY,cAAc;AAAA,QAClD,OAAO;AACL,cAAI,UAAU,MAAM,gBAAgB,UAAU;AAC9C,cAAI;AACJ,cAAI,iBAAiB,KAAK;AACxB,iBAAK,kBAAkB,MAAM,KAAK,MAAM;AAAA,UAC1C,OAAO;AACL,kBAAM,IAAI,kBAAkB,OAAO,IAAI;AAAA,UACzC;AACA,cAAI,UAAU,IAAI,OAAO,IAAI;AAC7B,cAAI,SAAS,gBAAgB;AAC3B,iBAAK,IAAI,kBAAkB,IAAI;AAAA,UACjC;AACA,cAAI,UAAU,IAAI;AAChB,sBAAU;AAAA,UACZ;AACA,gBAAM,gBAAgB,YAAY,OAAO;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,YAAY;AACtB,UAAM,QAAQ,KAAK;AACnB,eAAW,QAAQ,CAAC,EAAE,YAAY,KAAK,MAAM;AAC3C,UAAI,cAAc,KAAK,MAAM;AAC3B,cAAM,UAAU,MAAM,gBAAgB,UAAU;AAChD,aAAK,QAAQ,CAAC,EAAE,YAAY,YAAY,MAAM;AAC5C,cAAI,eAAe,GAAG;AACpB,kBAAM,gBAAgB,aAAa,OAAO;AAAA,UAC5C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,SAAK,YAAY,QAAQ,CAAC,cAAc;AACtC,WAAK,2BAA2B,WAAW,EAAE;AAC7C,WAAK,YAAY,SAAS;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AACA,cAAc,gBAAgB;AAAA,EAC5B,SAAS;AAAA,EACT,KAAK,QAAQ;AACX,WAAO,kBAAkB,wBAAwB,qBAAqB,YAAY,MAAM;AAAA,EAC1F;AAAA,EACA,QAAQ;AACN,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,WAAW,WAAW;AACpB,QAAI,UAAU,aAAa,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,IAAI,UAAU,WAAW,GAAG,CAAC;AAC1C,WAAO,OAAO,aAAa,GAAG,IAAI,MAAM;AAAA,EAC1C;AAAA,EACA,oBAAoB,MAAM;AACxB,WAAO,IAAI,qBAAqB,IAAI;AAAA,EACtC;AAAA,EACA,gBAAgB,MAAM;AACpB,UAAM,QAAQ,iBAAiB,UAAU,IAAI;AAC7C,UAAM,QAAQ,OAAO,SAAS;AAC9B,QAAI;AACF,YAAM;AACR,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,WAAW,UAAU,SAAS;AAChD,WAAO,IAAI,qBAAqB,WAAW,UAAU,OAAO;AAAA,EAC9D;AAAA,EACA,WAAW,WAAW,MAAM;AAC1B,WAAO,IAAI,WAAW,WAAW,IAAI;AAAA,EACvC;AAAA,EACA,cAAc,WAAW,MAAM;AAC7B,WAAO,IAAI,cAAc,WAAW,IAAI;AAAA,EAC1C;AACF,CAAC;AACD,IAAI,CAAC,OAAO,kBAAkB;AAC5B,QAAM,IAAI,MAAM,yFAAyF;AAC3G;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,GAAG,GAAG;AAChB,SAAK,IAAI,KAAK;AACd,SAAK,IAAI,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS;AACX,UAAM,MAAM,IAAI,eAAc,GAAG,CAAC;AAClC,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,WAAO;AAAA,EACT;AAAA,EACA,UAAU,SAAS;AACjB,UAAM,MAAM,IAAI,eAAc,GAAG,CAAC;AAClC,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAAS;AAChB,UAAM,MAAM,IAAI,eAAc,GAAG,CAAC;AAClC,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,QAAQ;AACvB,WAAO,KAAK,SAAS,IAAI,eAAc,QAAQ,MAAM,CAAC;AAAA,EACxD;AAAA,EACA,SAAS,SAAS;AAChB,UAAM,MAAM,IAAI,eAAc,GAAG,CAAC;AAClC,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,QAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,QAAQ;AACvB,WAAO,KAAK,SAAS,IAAI,eAAc,QAAQ,MAAM,CAAC;AAAA,EACxD;AAAA,EACA,YAAY;AACV,WAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EACpD;AAAA,EACA,gBAAgB,GAAG;AACjB,WAAO,KAAK,MAAM,KAAK,IAAI,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,KAAK,IAAI,EAAE,EAAE;AAAA,EACpF;AAAA,EACA,IAAI,GAAG;AACL,WAAO,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAAA,EACnC;AAAA,EACA,YAAY;AACV,UAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG;AAC9D,SAAK,IAAI,KAAK,IAAI;AAClB,SAAK,IAAI,KAAK,IAAI;AAAA,EACpB;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,EAC1C;AAAA,EACA,WAAW,KAAK;AACd,WAAO,CAAC,KAAK,QAAQ,GAAG;AAAA,EAC1B;AACF;AACA,IAAM,cAAc,MAAM;AAAA,EACxB,OAAO,MAAM,OAAO,KAAK,KAAK;AAC5B,QAAI,QAAQ,KAAK;AACf,cAAQ;AAAA,IACV,WAAW,QAAQ,KAAK;AACtB,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,IAAI,GAAG;AACZ,WAAO,KAAK,IAAI,CAAC;AAAA,EACnB;AAAA,EACA,OAAO,IAAI,GAAG;AACZ,WAAO,KAAK,IAAI,CAAC;AAAA,EACnB;AAAA,EACA,OAAO,IAAI,GAAG;AACZ,WAAO,KAAK,IAAI,CAAC;AAAA,EACnB;AAAA,EACA,OAAO,KAAK,GAAG;AACb,WAAO,KAAK,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,OAAO,KAAK,GAAG;AACb,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,KAAK;AACT,UAAM,mBAAmB,KAAK;AAC9B,QAAI,kBAAkB;AACpB,WAAK,CAAC;AAAA,IACR;AACA,QAAI;AACJ,QAAI,OAAO,UAAU;AACnB,YAAM;AAAA,IACR,OAAO;AACL,YAAM,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC;AAC/B,aAAO,MAAM,MAAM,OAAO,IAAI,OAAO;AAAA,IACvC;AACA,WAAO,mBAAmB,CAAC,MAAM;AAAA,EACnC;AAAA,EACA,OAAO,cAAc,OAAO;AAC1B,QAAI,QAAQ,GAAG;AACb,aAAO;AAAA,IACT,WAAW,QAAQ,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,MAAM,KAAK,IAAI,QAAQ,KAAK,EAAE;AAAA,EAC7C;AAAA,EACA,OAAO,IAAI,MAAM,OAAO;AACtB,WAAO,OAAO,QAAQ,OAAO;AAAA,EAC/B;AAAA,EACA,OAAO,IAAI,MAAM,OAAO;AACtB,WAAO,OAAO,QAAQ,QAAQ;AAAA,EAChC;AAAA,EACA,OAAO,gBAAgB,SAAS;AAC9B,WAAO,UAAU,MAAM,KAAK;AAAA,EAC9B;AAAA,EACA,OAAO,gBAAgB,QAAQ;AAC7B,WAAO,SAAS,MAAM,KAAK;AAAA,EAC7B;AAAA,EACA,OAAO,kBAAkB,MAAM,IAAI;AACjC,UAAM,KAAK,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC;AAChC,UAAM,KAAK,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC;AACpC,QAAI,MAAM,KAAK;AACf,WAAO,MAAM,CAAC,KAAK,IAAI;AACrB,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO,MAAM,KAAK,IAAI;AACpB,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,mBAAmB,MAAM,IAAI;AAClC,UAAM,SAAS,KAAK,kBAAkB,MAAM,EAAE;AAC9C,QAAI,SAAS,KAAK,gBAAgB,MAAM;AACxC,QAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AACrB,eAAS,CAAC;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,kBAAkB,YAAY;AACnC,UAAM,MAAM,IAAI,cAAc;AAC9B,QAAI,IAAI,KAAK,IAAI,UAAU;AAC3B,QAAI,IAAI,KAAK,IAAI,UAAU;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,kBAAkB,GAAG,GAAG,GAAG;AAChC,QAAI,KAAK,IAAI,CAAC,IAAI,YAAY,SAAS;AACrC,UAAI,KAAK,IAAI,CAAC,IAAI,YAAY,SAAS;AACrC,eAAO,CAAC;AAAA,MACV;AACA,aAAO,CAAC,IAAI;AAAA,IACd;AACA,WAAO,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI;AAAA,EACpD;AAAA,EACA,OAAO,0BAA0B,GAAG,GAAG,GAAG,GAAG;AAC3C,QAAI,KAAK,KAAK,CAAC,IAAI,YAAY,SAAS;AACtC,aAAO,KAAK,MAAM,KAAK,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AAAA,IACzD;AACA,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI,KAAK,KAAK,MAAM;AAC/B,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM;AACvD,UAAM,KAAK,IAAI;AACf,UAAM,eAAe,KAAK,KAAK,KAAK,KAAK;AACzC,UAAM,SAAS;AACf,UAAM,YAAY,SAAS;AAC3B,QAAI,eAAe,GAAG;AACpB,YAAM,MAAM,CAAC,IAAI;AACjB,YAAM,OAAO,MAAM,MAAM;AACzB,YAAM,IAAI,KAAK,KAAK,IAAI;AACxB,YAAM,IAAI,CAAC,KAAK,IAAI;AACpB,YAAM,SAAS,KAAK,MAAM,GAAG,IAAI,CAAC;AAClC,YAAM,MAAM,KAAK,KAAK,MAAM;AAC5B,YAAM,OAAO,KAAK,KAAK,CAAC;AACxB,YAAM,KAAK,IAAI;AACf,YAAM,SAAS,KAAK,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK;AAC7C,UAAI,KAAK,IAAI,SAAS,MAAM,IAAI,WAAW;AACzC,eAAO,KAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,MAChC;AACA,YAAM,QAAQ,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK;AAC5D,UAAI,KAAK,IAAI,QAAQ,MAAM,IAAI,WAAW;AACxC,eAAO,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,MAC/B;AACA,YAAM,QAAQ,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK;AAC5D,aAAO,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,IAC/B;AACA,QAAI,gBAAgB,GAAG;AACrB,UAAI;AACJ,UAAI,KAAK,GAAG;AACV,cAAM,KAAK,KAAK,CAAC,EAAE;AAAA,MACrB,OAAO;AACL,cAAM,CAAC,KAAK,KAAK,EAAE;AAAA,MACrB;AACA,YAAM,SAAS,IAAI,MAAM,KAAK;AAC9B,UAAI,KAAK,IAAI,SAAS,MAAM,IAAI,WAAW;AACzC,eAAO,KAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,MAChC;AACA,YAAM,QAAQ,CAAC,MAAM,KAAK;AAC1B,aAAO,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,IAC/B;AACA,UAAM,KAAK,KAAK,KAAK,YAAY;AACjC,UAAM,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5B,UAAM,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5B,UAAM,QAAQ,KAAK,KAAK,KAAK;AAC7B,WAAO,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,EAC/B;AAAA,EACA,cAAc;AAAA,EACd;AACF;AACA,IAAI,aAAa;AACjB,WAAW,UAAU;AACrB,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc;AACZ,SAAK,MAAM,IAAI,aAAa,EAAE;AAC9B,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,SAAS,GAAG,GAAG,KAAK;AACzB,UAAM,IAAI,IAAI,aAAa;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,IAAI;AACV,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,iBAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAE,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,CAAC,IAAI,EAAE,CAAC;AAAA,IACd;AAAA,EACF;AAAA,EACA,eAAe;AACb,UAAM,IAAI,IAAI,aAAa;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,UAAU,CAAC;AAAA,EAClB;AAAA,EACA,UAAU,IAAI;AACZ,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,WAAK,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,IACpB;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY;AACV,WAAO,KAAK,IAAI,CAAC;AAAA,EACnB;AAAA,EACA,YAAY;AACV,WAAO,KAAK,IAAI,CAAC;AAAA,EACnB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,IAAI,EAAE;AAAA,EACpB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,IAAI,EAAE;AAAA,EACpB;AAAA,EACA,WAAW,KAAK;AACd,WAAO,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,EAAE;AAAA,EACxC;AAAA,EACA,WAAW,KAAK;AACd,WAAO,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,EAAE;AAAA,EACxC;AAAA,EACA,iBAAiB,KAAK;AACpB,YAAQ,MAAM,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EAC1C;AAAA,EACA,iBAAiB,KAAK;AACpB,YAAQ,MAAM,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EAC1C;AAAA,EACA,kBAAkB,GAAG,GAAG;AACtB,UAAM,MAAM,IAAI,aAAa;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,oBAAe,SAAS,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACjD;AAAA,EACA,UAAU,GAAG,GAAG;AACd,SAAK,IAAI,EAAE,IAAI;AACf,SAAK,IAAI,EAAE,IAAI;AAAA,EACjB;AAAA,EACA,WAAW,GAAG;AACZ,SAAK,IAAI,EAAE,IAAI;AAAA,EACjB;AAAA,EACA,WAAW,GAAG;AACZ,SAAK,IAAI,EAAE,IAAI;AAAA,EACjB;AAAA,EACA,cAAc,GAAG,GAAG;AAClB,UAAM,MAAM,IAAI,aAAa;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,oBAAe,SAAS,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACjD;AAAA,EACA,MAAM,GAAG,GAAG;AACV,SAAK,IAAI,CAAC,IAAI;AACd,SAAK,IAAI,CAAC,IAAI;AAAA,EAChB;AAAA,EACA,iBAAiB,GAAG;AAClB,oBAAe,SAAS,EAAE,SAAS,GAAG,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1D;AAAA,EACA,QAAQ;AACN,UAAM,cAAc,IAAI,gBAAe;AACvC,aAAS,IAAI,GAAG,IAAI,KAAK,IAAI,QAAQ,KAAK;AACxC,kBAAY,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,WAAW,OAAO;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,YAAY;AACV,QAAI,KAAK,SAAS,KAAK;AACrB;AACF,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,aAAa,UAAU;AACrB,SAAK,cAAc,UAAU,SAAS,SAAS,CAAC;AAAA,EAClD;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,KAAK,OAAO,MAAM,OAAO;AACrC,QAAI,MAAM,GAAG;AACX,YAAM;AAAA,IACR,WAAW,MAAM,GAAG;AAClB,YAAM;AAAA,IACR;AACA,QAAI,QAAQ,GAAG;AACb,cAAQ;AAAA,IACV,WAAW,QAAQ,GAAG;AACpB,cAAQ;AAAA,IACV;AACA,QAAI,OAAO,GAAG;AACZ,aAAO;AAAA,IACT,WAAW,OAAO,GAAG;AACnB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,GAAG;AACb,cAAQ;AAAA,IACV,WAAW,QAAQ,GAAG;AACpB,cAAQ;AAAA,IACV;AACA,SAAK,YAAY,IAAI;AACrB,SAAK,YAAY,IAAI;AACrB,SAAK,YAAY,IAAI;AACrB,SAAK,YAAY,IAAI;AAAA,EACvB;AAAA,EACA,gBAAgB;AACd,WAAO,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW;AAAA,EAC3C;AAAA,EACA,wBAAwB,QAAQ;AAC9B,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,SAAS;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,GAAG;AACf,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,wBAAwB;AAC7B,SAAK,cAAc;AACnB,SAAK,cAAc,IAAI,mBAAmB;AAC1C,SAAK,gBAAgB,IAAI,eAAe;AACxC,SAAK,cAAc,aAAa;AAAA,EAClC;AACF;AACA,IAAI,mBAAmC,CAAC,qBAAqB;AAC3D,mBAAiB,iBAAiB,wBAAwB,IAAI,CAAC,IAAI;AACnE,mBAAiB,iBAAiB,0BAA0B,IAAI,CAAC,IAAI;AACrE,mBAAiB,iBAAiB,gCAAgC,IAAI,CAAC,IAAI;AAC3E,SAAO;AACT,GAAG,mBAAmB,CAAC,CAAC;AACxB,IAAM,qBAAN,MAAyB;AAAA,EACvB,cAAc;AACZ,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACX;AACF;AACA,IAAI,cAAc;AAClB,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAM,WAAW;AAAA,EACf,cAAc;AAAA,EACd,YAAY;AACd;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,OAAO,QAAQ,QAAQ;AACrB,QAAI,aAAa;AACf,oBAAc,4CAA4C;AAC1D,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,YAAY;AAC/B,oBAAc;AACd,aAAO;AAAA,IACT;AACA,qBAAiB,aAAa;AAC9B,eAAW;AACX,QAAI,UAAU;AACZ,uBAAiB,QAAQ,kBAAkB,SAAS,WAAW;AAAA,IACjE;AACA,kBAAc;AACd,QAAI,aAAa;AACf,YAAM,UAAU,iBAAiB,QAAQ,cAAc;AACvD,YAAM,SAAS,UAAU,eAAe;AACxC,YAAM,SAAS,UAAU,aAAa;AACtC,YAAM,QAAQ,UAAU;AACxB,YAAM,gBAAgB;AACtB,oBAAc,kDAAkD,OAAO,OAAO,MAAM,EAAE,IAAI,OAAO,OAAO,MAAM,EAAE,IAAI,SAAS,OAAO,MAAM,EAAE,GAAG,aAAa;AAAA,IAC9J;AACA,kBAAc,wCAAwC;AACtD,WAAO;AAAA,EACT;AAAA,EACA,OAAO,UAAU;AACf,kBAAc;AACd,sBAAkB;AAClB,eAAW;AAAA,EACb;AAAA,EACA,OAAO,aAAa;AAClB,QAAI,CAAC,aAAa;AAChB,uBAAiB,iCAAiC;AAClD;AAAA,IACF;AACA,QAAI,iBAAiB;AACnB,uBAAiB,4DAA4D;AAC7E;AAAA,IACF;AACA,sBAAkB;AAClB,kBAAc,2CAA2C;AAAA,EAC3D;AAAA,EACA,OAAO,UAAU;AACf,QAAI,CAAC,aAAa;AAChB,uBAAiB,iCAAiC;AAClD;AAAA,IACF;AACA,QAAI,CAAC,iBAAiB;AACpB,uBAAiB,qDAAqD;AACtE;AAAA,IACF;AACA,mBAAe,cAAc;AAC7B,sBAAkB;AAClB,kBAAc,wCAAwC;AAAA,EACxD;AAAA,EACA,OAAO,YAAY;AACjB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,gBAAgB;AACrB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,gBAAgB,SAAS;AAC9B,QAAI,CAAC,iBAAiB,QAAQ,kBAAkB,GAAG;AACjD;AAAA,IACF;AACA,qBAAiB,QAAQ,kBAAkB,EAAE,OAAO;AAAA,EACtD;AAAA,EACA,OAAO,kBAAkB;AACvB,QAAI,YAAY,MAAM;AACpB,aAAO,SAAS;AAAA,IAClB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,cAAc;AAAA,EACd;AACF;AACA,IAAI,YAA4B,CAAC,cAAc;AAC7C,YAAU,UAAU,kBAAkB,IAAI,CAAC,IAAI;AAC/C,YAAU,UAAU,gBAAgB,IAAI,CAAC,IAAI;AAC7C,YAAU,UAAU,eAAe,IAAI,CAAC,IAAI;AAC5C,YAAU,UAAU,kBAAkB,IAAI,CAAC,IAAI;AAC/C,YAAU,UAAU,gBAAgB,IAAI,CAAC,IAAI;AAC7C,YAAU,UAAU,cAAc,IAAI,CAAC,IAAI;AAC3C,SAAO;AACT,GAAG,YAAY,CAAC,CAAC;AACjB,IAAM,aAAa,MAAM;AACzB;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,cAAY,MAAM,SAAS,gBAAgB,aAAa,MAAM,MAAM,IAAI;AAC1E;AACA,SAAS,cAAc,QAAQ,MAAM;AACnC,cAAY,MAAM,SAAS,eAAe,aAAa,MAAM,MAAM,IAAI;AACzE;AACA,SAAS,iBAAiB,QAAQ,MAAM;AACtC,cAAY,MAAM,SAAS,kBAAkB,aAAa,MAAM,MAAM,IAAI;AAC5E;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,cAAY,MAAM,SAAS,gBAAgB,aAAa,MAAM,MAAM,IAAI;AAC1E;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,OAAO,MAAM,UAAU,QAAQ,MAAM;AACnC,QAAI,WAAW,gBAAgB,gBAAgB,GAAG;AAChD;AAAA,IACF;AACA,UAAM,WAAW,gBAAgB;AACjC,QAAI,CAAC;AACH;AACF,UAAM,SAAS,OAAO,QAAQ,YAAY,CAAC,GAAG,MAAM;AAClD,aAAO,KAAK,CAAC;AAAA,IACf,CAAC;AACD,aAAS,MAAM;AAAA,EACjB;AAAA,EACA,OAAO,UAAU,UAAU,MAAM,QAAQ;AACvC,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAI,IAAI,MAAM,KAAK,IAAI;AACrB,aAAK,MAAM,UAAU,IAAI;AAAA,eAClB,IAAI,KAAK,KAAK,IAAI;AACzB,aAAK,MAAM,UAAU,IAAI;AAC3B,WAAK,MAAM,UAAU,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;AAAA,IAC9C;AACA,SAAK,MAAM,UAAU,IAAI;AAAA,EAC3B;AAAA,EACA,cAAc;AAAA,EACd;AACF;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,cAAc;AACZ,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,oBAAoB,CAAC;AAAA,EAC5B;AAAA,EACA,UAAU;AACR,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,iBAAiB,OAAO,kBAAkB,iBAAiB;AACzD,QAAI,CAAC,iBAAiB,YAAY,KAAK,iBAAiB,WAAW,GAAG;AACpE;AAAA,IACF;AACA,QAAI,CAAC,iBAAiB,UAAU,GAAG;AACjC,uBAAiB,aAAa,IAAI;AAClC,uBAAiB,aAAa,kBAAkB,KAAK,cAAc;AACnE,uBAAiB,mBAAmB,eAAe;AACnD,YAAM,WAAW,KAAK,YAAY;AAClC,UAAI,iBAAiB,WAAW,IAAI,GAAG;AACrC,yBAAiB,WAAW,YAAY,IAAI,KAAK,iBAAiB,aAAa,IAAI,QAAQ;AAAA,MAC7F;AAAA,IACF;AACA,QAAI,aAAa,KAAK;AACtB,UAAM,SAAS,KAAK,kBAAkB,IAAI,IAAI,WAAW,eAAe,kBAAkB,iBAAiB,mBAAmB,KAAK,KAAK,cAAc;AACtJ,UAAM,UAAU,KAAK,mBAAmB,KAAK,iBAAiB,WAAW,IAAI,IAAI,IAAI,WAAW,eAAe,iBAAiB,WAAW,IAAI,mBAAmB,KAAK,eAAe;AACtL,iBAAa,aAAa,SAAS;AACnC,qBAAiB,SAAS,iBAAiB,UAAU;AACrD,SAAK,mBAAmB,OAAO,iBAAiB,YAAY,gBAAgB;AAC5E,QAAI,iBAAiB,WAAW,IAAI,KAAK,iBAAiB,WAAW,IAAI,iBAAiB;AACxF,uBAAiB,cAAc,IAAI;AAAA,IACrC;AAAA,EACF;AAAA,EACA,cAAc,eAAe;AAC3B,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,eAAe,gBAAgB;AAC7B,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,WAAO;AAAA,EACT;AAAA,EACA,cAAc,eAAe;AAC3B,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc,wBAAwB,mBAAmB;AACvD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,yBAAyB,yBAAyB;AAChD,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,2BAA2B;AACzB,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,kBAAkB;AACxB,IAAM,yBAAN,MAAM,gCAA+B,cAAc;AAAA,EACjD,cAAc;AACZ,UAAM;AACN,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,OAAO,OAAO,MAAM;AAClB,UAAM,aAAa,IAAI,wBAAuB;AAC9C,UAAM,aAAa,KAAK;AACxB,UAAM,cAAc,KAAK;AACzB,eAAW,cAAc,eAAe,SAAS,aAAa,eAAe;AAC7E,eAAW,eAAe,gBAAgB,SAAS,cAAc,eAAe;AAChF,UAAM,aAAa,KAAK,cAAc,CAAC;AACvC,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,YAAM,QAAQ,WAAW,CAAC;AAC1B,YAAM,cAAc,MAAM;AAC1B,YAAM,QAAQ,MAAM;AACpB,UAAI;AACJ,cAAQ,MAAM,OAAO;AAAA,QACnB,KAAK;AACH,sBAAY,oBAAoB;AAChC;AAAA,QACF,KAAK;AACH,sBAAY,oBAAoB;AAChC;AAAA,QACF,KAAK;AAAA,QACL;AACE,sBAAY,oBAAoB;AAChC;AAAA,MACJ;AACA,YAAM,OAAO;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,iBAAW,YAAY,KAAK,IAAI;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,OAAO,iBAAiB,QAAQ,kBAAkB;AACnE,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,EAAE,GAAG;AAChD,YAAM,YAAY,KAAK,YAAY,CAAC;AACpC,cAAQ,UAAU,WAAW;AAAA,QAC3B,KAAK,oBAAoB,yBAAyB;AAChD,gBAAM,sBAAsB,UAAU,aAAa,UAAU,OAAO,MAAM;AAC1E;AAAA,QACF;AAAA,QACA,KAAK,oBAAoB,8BAA8B;AACrD,gBAAM,2BAA2B,UAAU,aAAa,UAAU,OAAO,MAAM;AAC/E;AAAA,QACF;AAAA,QACA,KAAK,oBAAoB,+BAA+B;AACtD,gBAAM,sBAAsB,UAAU,aAAa,UAAU,OAAO,MAAM;AAC1E;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,uBAAuC,CAAC,yBAAyB;AACnE,uBAAqB,qBAAqB,yBAAyB,IAAI,CAAC,IAAI;AAC5E,uBAAqB,qBAAqB,8BAA8B,IAAI,CAAC,IAAI;AACjF,uBAAqB,qBAAqB,+BAA+B,IAAI,CAAC,IAAI;AAClF,SAAO;AACT,GAAG,uBAAuB,CAAC,CAAC;AAC5B,IAAM,yBAAN,MAA6B;AAAA,EAC3B,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,0BAA0B;AAC/B,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,yBAAyB;AAC9B,SAAK,0BAA0B;AAC/B,SAAK,kBAAkB;AACvB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,UAAU;AACR,QAAI,KAAK,eAAe,KAAK,SAAS;AACpC,WAAK,QAAQ,QAAQ;AAAA,IACvB;AAAA,EACF;AAAA,EACA,WAAW,gBAAgB;AACzB,SAAK,kBAAkB;AACvB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,aAAa,gBAAgB,iBAAiB;AAC5C,UAAM,oBAAoB,kBAAkB;AAC5C,SAAK,sBAAsB;AAC3B,QAAI,KAAK,kBAAkB,KAAK,oBAAoB,KAAK,iBAAiB;AACxE,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,WAAW;AACtB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,mBAAmB,WAAW;AAC5B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,cAAc,GAAG;AACf,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,aAAa,GAAG;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,GAAG;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,SAAS,aAAa,QAAQ;AAC5B,SAAK,oBAAoB;AACzB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,2BAA2B;AACzB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,yBAAyB,cAAc;AACrC,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,2BAAN,MAA+B;AAAA,EAC7B,cAAc;AACZ,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,WAAW,CAAC;AAAA,EACnB;AAAA,EACA,UAAU;AACR,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,EAAE,GAAG;AAC7C,UAAI,KAAK,SAAS,CAAC,GAAG;AACpB,aAAK,SAAS,CAAC,EAAE,QAAQ;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY,QAAQ,YAAY,iBAAiB;AAC/C,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AACA,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,EAAE,GAAG;AAC7C,yBAAmB,KAAK,SAAS,CAAC;AAClC,UAAI,oBAAoB,MAAM;AAC5B;AAAA,MACF;AACA,uBAAiB,WAAW,iBAAiB,QAAQ,eAAe,CAAC;AAAA,IACvE;AACA,uBAAmB,IAAI,uBAAuB;AAC9C,qBAAiB,cAAc;AAC/B,qBAAiB,UAAU;AAC3B,SAAK,SAAS,KAAK,gBAAgB;AACnC,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,aAAa;AACX,QAAI,IAAI;AACR,WAAO,IAAI,KAAK,SAAS,QAAQ;AAC/B,YAAM,mBAAmB,KAAK,SAAS,CAAC;AACxC,UAAI,oBAAoB,MAAM;AAC5B,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB;AAAA,MACF;AACA,YAAM,SAAS,iBAAiB;AAChC,UAAI,UAAU,MAAM;AAClB,yBAAiB,QAAQ;AACzB,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB;AAAA,MACF;AACA,UAAI,CAAC,iBAAiB,WAAW,GAAG;AAClC,eAAO;AAAA,MACT;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,wBAAwB;AACzC,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,mBAAmB,KAAK,SAAS,CAAC;AACxC,UAAI,oBAAoB,MAAM;AAC5B;AAAA,MACF;AACA,UAAI,iBAAiB,2BAA2B,0BAA0B,CAAC,iBAAiB,WAAW,GAAG;AACxG,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,mBAAmB,KAAK,SAAS,CAAC;AACxC,UAAI,oBAAoB,MAAM;AAC5B,yBAAiB,QAAQ;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,WAAW,CAAC;AAAA,EACnB;AAAA,EACA,0BAA0B,wBAAwB;AAChD,WAAO,KAAK,SAAS,KAAK,CAAC,UAAU,SAAS,QAAQ,MAAM,2BAA2B,sBAAsB;AAAA,EAC/G;AAAA,EACA,iBAAiB,UAAU,aAAa,MAAM;AAC5C,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,eAAe,OAAO,iBAAiB;AACrC,QAAI,UAAU;AACd,QAAI,IAAI;AACR,WAAO,IAAI,KAAK,SAAS,QAAQ;AAC/B,YAAM,mBAAmB,KAAK,SAAS,CAAC;AACxC,UAAI,oBAAoB,MAAM;AAC5B,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB;AAAA,MACF;AACA,YAAM,SAAS,iBAAiB;AAChC,UAAI,UAAU,MAAM;AAClB,yBAAiB,QAAQ;AACzB,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB;AAAA,MACF;AACA,aAAO,iBAAiB,OAAO,kBAAkB,eAAe;AAChE,gBAAU;AACV,YAAM,YAAY,OAAO,cAAc,iBAAiB,yBAAyB,IAAI,iBAAiB,aAAa,GAAG,kBAAkB,iBAAiB,aAAa,CAAC;AACvK,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,EAAE,IAAI;AAC5C,aAAK,eAAe,MAAM,UAAU,EAAE,GAAG,KAAK,gBAAgB;AAAA,MAChE;AACA,uBAAiB,yBAAyB,eAAe;AACzD,UAAI,iBAAiB,WAAW,GAAG;AACjC,yBAAiB,QAAQ;AACzB,aAAK,SAAS,OAAO,GAAG,CAAC;AAAA,MAC3B,OAAO;AACL,YAAI,iBAAiB,mBAAmB,GAAG;AACzC,2BAAiB,aAAa,iBAAiB,kBAAkB,GAAG,eAAe;AAAA,QACrF;AACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,qCAAqC;AAC3C,IAAM,2BAAN,cAAuC,kBAAkB;AAAA,EACvD,YAAY,UAAU,SAAS;AAC7B,QAAI;AACJ,UAAM,UAAU,OAAO;AACvB,SAAK,eAAe,IAAI,yBAAyB;AACjD,SAAK,eAAe,KAAK,SAAS,gBAAgB,OAAO,KAAK,CAAC;AAC/D,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,aAAa;AACX,WAAO,KAAK,aAAa,WAAW;AAAA,EACtC;AAAA,EACA,mBAAmB,MAAM;AACvB,WAAO,KAAK,YAAY,UAAU,CAAC,QAAQ,IAAI,SAAS,IAAI;AAAA,EAC9D;AAAA,EACA,kBAAkB,YAAY;AAC5B,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB,MAAM,YAAY;AACjC,WAAO,uBAAuB,OAAO,IAAI;AAAA,EAC3C;AAAA,EACA,eAAe,QAAQ;AACrB,WAAO,KAAK,aAAa,YAAY,QAAQ,OAAO,YAAY,IAAI,CAAC;AAAA,EACvE;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa,eAAe;AAAA,EACnC;AAAA,EACA,iBAAiB,OAAO,KAAK;AAC3B,WAAO,KAAK,aAAa,eAAe,OAAO,GAAG;AAAA,EACpD;AACF;AACA,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,MAAM;AAChB,SAAK,SAAS,KAAK;AACnB,SAAK,WAAW,KAAK;AACrB,SAAK,SAAS,KAAK;AACnB,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,cAAc,KAAK,eAAe;AACvC,SAAK,UAAU,KAAK,eAAe;AACnC,SAAK,WAAW,KAAK,eAAe;AACpC,SAAK,UAAU,KAAK,eAAe;AACnC,SAAK,OAAO,KAAK,eAAe;AAAA,EAClC;AAAA,EACA,wBAAwB;AACtB,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG,KAAK,CAAC,UAAU,MAAM,SAAS,UAAU,MAAM,OAAO,SAAS,GAAG;AAAA,EAC1H;AAAA,EACA,uBAAuB;AACrB,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG,KAAK,CAAC,UAAU,MAAM,SAAS,SAAS,MAAM,OAAO,SAAS,GAAG;AAAA,EACzH;AACF;AACA,IAAM,uBAAN,MAAM,8BAA6B,cAAc;AAAA,EAC/C,YAAY,MAAM;AAChB,UAAM,IAAI;AACV,QAAI,CAAC,sBAAqB,YAAY,IAAI,GAAG;AAC3C,YAAM,IAAI,UAAU,eAAe;AAAA,IACrC;AACA,WAAO,OAAO,MAAM,IAAI,wBAAwB,IAAI,CAAC;AAAA,EACvD;AAAA,EACA,OAAO,YAAY,MAAM;AACvB,QAAI;AACJ,WAAO,CAAC,EAAE,QAAQ,OAAO,SAAS,KAAK,mBAAmB,OAAO,KAAK,eAAe,QAAQ,cAAc,KAAK,KAAK,eAAe,aAAa,OAAO,SAAS,GAAG,UAAU,KAAK,KAAK,eAAe,SAAS,MAAM,CAAC,SAAS,OAAO,SAAS,QAAQ;AAAA,EAC1P;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,aAAa,OAAO;AAC1B,QAAI,KAAK,SAAS;AAChB,iBAAW,CAAC,OAAO,OAAO,KAAK,OAAO,QAAQ,KAAK,OAAO,GAAG;AAC3D,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,kBAAQ,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,EAAE,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;AACxE,cAAI,QAAQ,CAAC,EAAE,UAAU,QAAQ;AAC/B,oBAAQ,CAAC,EAAE,QAAQ,QAAQ,QAAQ,CAAC,EAAE,OAAO,WAAW,KAAK,IAAI,CAAC,SAAS;AAAA,UAC7E;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,aAAK,YAAY,CAAC,EAAE,OAAO,QAAQ,KAAK,YAAY,CAAC,EAAE,MAAM,eAAe,CAAC,QAAQ;AAAA,MACvF;AAAA,IACF;AAAA,EACF;AACF;AACA,YAAY,sBAAsB,CAAC,uBAAuB,CAAC;AAC3D,IAAI,2BAA2C,CAAC,6BAA6B;AAC3E,2BAAyB,yBAAyB,+BAA+B,IAAI,CAAC,IAAI;AAC1F,2BAAyB,yBAAyB,mCAAmC,IAAI,CAAC,IAAI;AAC9F,2BAAyB,yBAAyB,qCAAqC,IAAI,CAAC,IAAI;AAChG,SAAO;AACT,GAAG,2BAA2B,CAAC,CAAC;AAChC,IAAI,2BAA2C,CAAC,6BAA6B;AAC3E,2BAAyB,yBAAyB,gCAAgC,IAAI,CAAC,IAAI;AAC3F,2BAAyB,yBAAyB,gCAAgC,IAAI,CAAC,IAAI;AAC3F,2BAAyB,yBAAyB,iCAAiC,IAAI,CAAC,IAAI;AAC5F,2BAAyB,yBAAyB,wCAAwC,IAAI,CAAC,IAAI;AACnG,SAAO;AACT,GAAG,2BAA2B,CAAC,CAAC;AAChC,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,OAAO,GAAG,QAAQ,GAAG;AAC/B,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,iBAAiB;AACtB,SAAK,cAAc;AAAA,EACrB;AACF;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,cAAc;AAAA,EACrB;AACF;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS,CAAC;AACf,SAAK,SAAS,CAAC;AAAA,EACjB;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,MAAM;AAChB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,eAAe;AACb,WAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,EACjC;AAAA,EACA,wBAAwB,UAAU;AAChC,QAAI,qBAAqB,6CAA6C,UAAU;AAC9E,aAAO,CAAC,CAAC,KAAK,MAAM,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,eAAe;AACb,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,6BAA6B;AAC3B,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,2BAA2B;AACzB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,qBAAqB,YAAY;AAC/B,WAAO,KAAK,MAAM,OAAO,UAAU,EAAE;AAAA,EACvC;AAAA,EACA,iBAAiB,YAAY;AAC3B,WAAO,KAAK,MAAM,OAAO,UAAU,EAAE;AAAA,EACvC;AAAA,EACA,yBAAyB,YAAY;AACnC,WAAO,KAAK,MAAM,OAAO,UAAU,EAAE;AAAA,EACvC;AAAA,EACA,0BAA0B,YAAY;AACpC,WAAO,KAAK,MAAM,OAAO,UAAU,EAAE;AAAA,EACvC;AAAA,EACA,2BAA2B,YAAY;AACrC,WAAO,KAAK,MAAM,OAAO,UAAU,EAAE,SAAS;AAAA,EAChD;AAAA,EACA,sBAAsB,YAAY,cAAc;AAC9C,WAAO,KAAK,MAAM,OAAO,UAAU,EAAE,SAAS,YAAY;AAAA,EAC5D;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,MAAM,KAAK,iBAAiB;AAAA,EAC1C;AAAA,EACA,yBAAyB;AACvB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,MAAM,SAAS,aAAa,EAAE;AAAA,EAC5C;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,MAAM,SAAS,aAAa,EAAE;AAAA,EAC5C;AACF;AACA,IAAI,wBAAwC,CAAC,0BAA0B;AACrE,wBAAsB,sBAAsB,2CAA2C,IAAI,CAAC,IAAI;AAChG,SAAO;AACT,GAAG,wBAAwB,CAAC,CAAC;AAC7B,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB;AACxB,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,2BAA2B;AACjC,SAAS,WAAW,GAAG,GAAG,GAAG;AAC3B,QAAM,SAAS,IAAI,kBAAkB;AACrC,SAAO,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;AAC3C,SAAO,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS;AAC/C,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,MAAI,KAAK,OAAO,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE;AAC9D,MAAI,IAAI,GAAG;AACT,QAAI;AAAA,EACN;AACA,SAAO,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,SAAS;AACjE;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,MAAI,KAAK,OAAO,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE;AAC9D,MAAI,IAAI,GAAG;AACT,QAAI;AAAA,EACN;AACA,QAAM,MAAM,WAAW,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC9C,QAAM,MAAM,WAAW,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC9C,QAAM,MAAM,WAAW,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC9C,QAAM,OAAO,WAAW,KAAK,KAAK,CAAC;AACnC,QAAM,OAAO,WAAW,KAAK,KAAK,CAAC;AACnC,SAAO,WAAW,MAAM,MAAM,CAAC,EAAE;AACnC;AACA,SAAS,oCAAoC,QAAQ,MAAM;AACzD,QAAM,IAAI;AACV,QAAM,KAAK,OAAO,CAAC,EAAE;AACrB,QAAM,KAAK,OAAO,CAAC,EAAE;AACrB,QAAM,MAAM,OAAO,CAAC,EAAE;AACtB,QAAM,MAAM,OAAO,CAAC,EAAE;AACtB,QAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM;AACnC,QAAM,IAAI,IAAI,MAAM,IAAI,MAAM,IAAI;AAClC,QAAM,IAAI,IAAI,MAAM,IAAI;AACxB,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,WAAW,0BAA0B,GAAG,GAAG,GAAG,CAAC;AACzD,QAAM,MAAM,WAAW,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC9C,QAAM,MAAM,WAAW,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC9C,QAAM,MAAM,WAAW,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC9C,QAAM,OAAO,WAAW,KAAK,KAAK,CAAC;AACnC,QAAM,OAAO,WAAW,KAAK,KAAK,CAAC;AACnC,SAAO,WAAW,MAAM,MAAM,CAAC,EAAE;AACnC;AACA,SAAS,gBAAgB,QAAQ,MAAM;AACrC,SAAO,OAAO,CAAC,EAAE;AACnB;AACA,SAAS,uBAAuB,QAAQ,MAAM;AAC5C,SAAO,OAAO,CAAC,EAAE;AACnB;AACA,SAAS,cAAc,YAAY,OAAO,MAAM;AAC9C,QAAM,QAAQ,WAAW,OAAO,KAAK;AACrC,MAAI,SAAS;AACb,QAAM,oBAAoB,MAAM,mBAAmB,MAAM;AACzD,MAAI,gBAAgB;AACpB,WAAS,IAAI,MAAM,kBAAkB,IAAI,mBAAmB,EAAE,GAAG;AAC/D,oBAAgB,WAAW,SAAS,CAAC,EAAE,kBAAkB,WAAW,SAAS,CAAC,EAAE,eAAe,wBAAwB,iCAAiC,IAAI;AAC5J,QAAI,WAAW,OAAO,aAAa,EAAE,OAAO,MAAM;AAChD,eAAS;AACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU,IAAI;AAChB,WAAO,WAAW,OAAO,aAAa,EAAE;AAAA,EAC1C;AACA,QAAM,UAAU,WAAW,SAAS,MAAM;AAC1C,SAAO,QAAQ,SAAS,WAAW,OAAO,MAAM,QAAQ,cAAc,GAAG,IAAI;AAC/E;AACA,IAAM,eAAN,MAAM,sBAAqB,cAAc;AAAA,EACvC,cAAc;AACZ,UAAM;AACN,SAAK,wBAAwB,CAAC;AAC9B,SAAK,uBAAuB,CAAC;AAC7B,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAC5B,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,MAAM,yBAAyB;AAC3C,UAAM,MAAM,IAAI,cAAa;AAC7B,QAAI,MAAM,IAAI;AACd,QAAI,mBAAmB,IAAI,YAAY;AACvC,QAAI,uBAAuB,IAAI,YAAY;AAC3C,QAAI,oBAAoB;AACxB,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,OAAO,iBAAiB,YAAY,kBAAkB;AACvE,QAAI,KAAK,yBAAyB,MAAM;AACtC,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,KAAK,wBAAwB,MAAM;AACrC,WAAK,uBAAuB;AAAA,IAC9B;AACA,QAAI,oBAAoB,kBAAkB,iBAAiB,aAAa;AACxE,QAAI,oBAAoB,GAAG;AACzB,0BAAoB;AAAA,IACtB;AACA,QAAI,eAAe,OAAO;AAC1B,QAAI,gBAAgB,OAAO;AAC3B,UAAM,gBAAgB;AACtB,QAAI,eAAe;AACnB,QAAI,gBAAgB;AACpB,QAAI,KAAK,sBAAsB,SAAS,eAAe;AACrD,qBAAe,oCAAoC,KAAK,sBAAsB,MAAM;AAAA,IACtF;AACA,QAAI,KAAK,qBAAqB,SAAS,eAAe;AACpD,qBAAe,mCAAmC,KAAK,qBAAqB,MAAM;AAAA,IACpF;AACA,UAAM,YAAY,KAAK,kBAAkB,IAAI,IAAI,WAAW,eAAe,kBAAkB,iBAAiB,mBAAmB,KAAK,KAAK,cAAc;AACzJ,UAAM,aAAa,KAAK,mBAAmB,KAAK,iBAAiB,WAAW,IAAI,IAAI,IAAI,WAAW,eAAe,iBAAiB,WAAW,IAAI,mBAAmB,KAAK,eAAe;AACzL,QAAI;AACJ,QAAI,GAAG;AACP,QAAI,OAAO;AACX,QAAI,KAAK,SAAS;AAChB,aAAO,OAAO,KAAK,YAAY,UAAU;AACvC,gBAAQ,KAAK,YAAY;AAAA,MAC3B;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY;AAChC,SAAK,IAAI,GAAG,IAAI,KAAK,YAAY,cAAc,OAAO,CAAC,EAAE,QAAQ,wBAAwB,+BAA+B,EAAE,GAAG;AAC3H,cAAQ,cAAc,KAAK,aAAa,GAAG,IAAI;AAC/C,UAAI,OAAO,CAAC,EAAE,MAAM,KAAK,uBAAuB;AAC9C,wBAAgB;AAAA,MAClB,WAAW,OAAO,CAAC,EAAE,MAAM,KAAK,sBAAsB;AACpD,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,WAAO,IAAI,KAAK,YAAY,cAAc,OAAO,CAAC,EAAE,QAAQ,wBAAwB,mCAAmC,EAAE,GAAG;AAC1H,uBAAiB,MAAM,kBAAkB,OAAO,CAAC,EAAE,EAAE;AACrD,UAAI,kBAAkB,IAAI;AACxB;AAAA,MACF;AACA,YAAM,cAAc,MAAM,yBAAyB,cAAc;AACjE,cAAQ,cAAc,KAAK,aAAa,GAAG,IAAI;AAC/C,UAAI,iBAAiB,OAAO,WAAW;AACrC,iBAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,UAAU,IAAI,eAAe,EAAE,GAAG;AAC/E,cAAI,KAAK,sBAAsB,CAAC,KAAK,OAAO,CAAC,EAAE,IAAI;AACjD,qBAAS;AACT,6BAAiB,KAAK;AACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,gBAAgB,OAAO,WAAW;AACpC,iBAAS,IAAI,GAAG,IAAI,KAAK,qBAAqB,UAAU,IAAI,eAAe,EAAE,GAAG;AAC9E,cAAI,KAAK,qBAAqB,CAAC,KAAK,OAAO,CAAC,EAAE,IAAI;AAChD,qBAAS;AACT,4BAAgB,KAAK;AACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI;AACJ,UAAI,OAAO,CAAC,EAAE,aAAa,KAAK,OAAO,CAAC,EAAE,cAAc,GAAG;AACzD,YAAI,eAAe,QAAQ,eAAe;AAAA,MAC5C,OAAO;AACL,YAAI;AACJ,YAAI;AACJ,YAAI,OAAO,CAAC,EAAE,aAAa,GAAG;AAC5B,gBAAM;AAAA,QACR,OAAO;AACL,gBAAM,OAAO,CAAC,EAAE,cAAc,IAAI,IAAI,WAAW,eAAe,kBAAkB,iBAAiB,mBAAmB,KAAK,OAAO,CAAC,EAAE,UAAU;AAAA,QACjJ;AACA,YAAI,OAAO,CAAC,EAAE,cAAc,GAAG;AAC7B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,OAAO,CAAC,EAAE,eAAe,KAAK,iBAAiB,WAAW,IAAI,IAAI,IAAI,WAAW,eAAe,iBAAiB,WAAW,IAAI,mBAAmB,OAAO,CAAC,EAAE,WAAW;AAAA,QACjL;AACA,cAAM,cAAc,KAAK,UAAU,MAAM;AACzC,YAAI,eAAe,QAAQ,eAAe;AAAA,MAC5C;AACA,YAAM,yBAAyB,gBAAgB,GAAG,CAAC;AAAA,IACrD;AACA;AACE,UAAI,iBAAiB,OAAO,WAAW;AACrC,iBAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,UAAU,IAAI,eAAe,EAAE,GAAG;AAC/E,gBAAM,cAAc,MAAM,sBAAsB,KAAK,sBAAsB,CAAC,CAAC;AAC7E,cAAI,iBAAiB,IAAI,GAAG;AAC1B;AAAA,UACF;AACA,gBAAM,IAAI,eAAe,gBAAgB,eAAe;AACxD,gBAAM,sBAAsB,KAAK,sBAAsB,CAAC,GAAG,CAAC;AAAA,QAC9D;AAAA,MACF;AACA,UAAI,gBAAgB,OAAO,WAAW;AACpC,iBAAS,IAAI,GAAG,IAAI,KAAK,qBAAqB,UAAU,IAAI,eAAe,EAAE,GAAG;AAC9E,gBAAM,cAAc,MAAM,sBAAsB,KAAK,qBAAqB,CAAC,CAAC;AAC5E,cAAI,gBAAgB,IAAI,GAAG;AACzB;AAAA,UACF;AACA,gBAAM,IAAI,eAAe,eAAe,eAAe;AACvD,gBAAM,sBAAsB,KAAK,qBAAqB,CAAC,GAAG,CAAC;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AACA,WAAO,IAAI,KAAK,YAAY,cAAc,OAAO,CAAC,EAAE,QAAQ,wBAAwB,qCAAqC,EAAE,GAAG;AAC5H,cAAQ,cAAc,KAAK,aAAa,GAAG,IAAI;AAC/C,UAAI,aAAa,sBAAsB;AACrC,cAAM,mBAAmB,OAAO,CAAC,EAAE,IAAI,KAAK;AAAA,MAC9C,OAAO;AACL,yBAAiB,MAAM,kBAAkB,OAAO,CAAC,EAAE,EAAE;AACrD,YAAI,kBAAkB,IAAI;AACxB;AAAA,QACF;AACA,cAAM,yBAAyB,gBAAgB,KAAK;AAAA,MACtD;AAAA,IACF;AACA,QAAI,qBAAqB,KAAK,YAAY,UAAU;AAClD,UAAI,KAAK,SAAS;AAChB,yBAAiB,aAAa,eAAe;AAC7C,YAAI,KAAK,eAAe;AACtB,2BAAiB,mBAAmB,eAAe;AAAA,QACrD;AAAA,MACF,OAAO;AACL,YAAI,KAAK,mBAAmB;AAC1B,eAAK,kBAAkB,IAAI;AAAA,QAC7B;AACA,yBAAiB,cAAc,IAAI;AAAA,MACrC;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,UAAU,MAAM;AACd,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,gBAAgB,YAAY;AAC1B,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,UAAU,KAAK,KAAK;AAAA,EAClC;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,uBAAuB,aAAa,OAAO;AACzC,UAAM,SAAS,KAAK,YAAY;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,YAAY,EAAE,GAAG;AACpD,UAAI,eAAe,OAAO,CAAC,EAAE,IAAI;AAC/B,eAAO,CAAC,EAAE,aAAa;AACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,wBAAwB,aAAa,OAAO;AAC1C,UAAM,SAAS,KAAK,YAAY;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,YAAY,EAAE,GAAG;AACpD,UAAI,eAAe,OAAO,CAAC,EAAE,IAAI;AAC/B,eAAO,CAAC,EAAE,cAAc;AACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB,aAAa;AAClC,UAAM,SAAS,KAAK,YAAY;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,YAAY,EAAE,GAAG;AACpD,UAAI,eAAe,OAAO,CAAC,EAAE,IAAI;AAC/B,eAAO,OAAO,CAAC,EAAE;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB,aAAa;AACnC,UAAM,SAAS,KAAK,YAAY;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,YAAY,EAAE,GAAG;AACpD,UAAI,eAAe,OAAO,CAAC,EAAE,IAAI;AAC/B,eAAO,OAAO,CAAC,EAAE;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,sBAAsB,qBAAqB;AACtD,SAAK,wBAAwB;AAC7B,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,UAAU;AACR,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,MAAM,YAAY;AAChB,SAAK,cAAc,IAAI,iBAAiB;AACxC,QAAI,OAAO,IAAI,iBAAiB,UAAU;AAC1C,SAAK,YAAY,WAAW,KAAK,kBAAkB;AACnD,SAAK,YAAY,OAAO,KAAK,aAAa;AAC1C,SAAK,YAAY,aAAa,KAAK,oBAAoB;AACvD,SAAK,YAAY,MAAM,KAAK,aAAa;AACzC,SAAK,YAAY,aAAa,KAAK,cAAc;AACjD,UAAM,uBAAuB,KAAK,wBAAwB,qBAAqB,yCAAyC;AACxH,UAAM,gBAAgB,KAAK,oBAAoB;AAC/C,UAAM,iBAAiB,KAAK,qBAAqB;AACjD,QAAI,kBAAkB,QAAQ;AAC5B,WAAK,iBAAiB,gBAAgB,IAAI,IAAI;AAAA,IAChD,OAAO;AACL,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,mBAAmB,QAAQ;AAC7B,WAAK,kBAAkB,iBAAiB,IAAI,IAAI;AAAA,IAClD,OAAO;AACL,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,YAAY,SAAS,MAAM,KAAK,EAAE,QAAQ,KAAK,YAAY,WAAW,CAAC,EAAE,IAAI,MAAM,IAAI,kBAAkB,CAAC;AAC/G,SAAK,YAAY,WAAW,MAAM,KAAK,EAAE,QAAQ,KAAK,2BAA2B,EAAE,CAAC,EAAE,IAAI,MAAM,IAAI,oBAAoB,CAAC;AACzH,SAAK,YAAY,SAAS,MAAM,KAAK,EAAE,QAAQ,KAAK,YAAY,WAAW,CAAC,EAAE,IAAI,MAAM,IAAI,kBAAkB,CAAC;AAC/G,SAAK,YAAY,SAAS,CAAC;AAC3B,QAAI,kBAAkB;AACtB,QAAI,oBAAoB;AACxB,aAAS,aAAa,GAAG,aAAa,KAAK,YAAY,YAAY,EAAE,YAAY;AAC/E,YAAM,QAAQ,KAAK,YAAY,OAAO,UAAU;AAChD,cAAQ,KAAK,qBAAqB,UAAU,GAAG;AAAA,QAC7C,KAAK;AACH,gBAAM,OAAO,wBAAwB;AACrC;AAAA,QACF,KAAK;AACH,gBAAM,OAAO,wBAAwB;AACrC;AAAA,QACF,KAAK;AACH,gBAAM,OAAO,wBAAwB;AACrC;AAAA,QACF;AACE,2BAAiB,+FAA+F;AAAA,MACpH;AACA,YAAM,KAAK,KAAK,iBAAiB,UAAU;AAC3C,YAAM,mBAAmB;AACzB,YAAM,aAAa,KAAK,yBAAyB,UAAU;AAC3D,YAAM,cAAc,KAAK,0BAA0B,UAAU;AAC7D,YAAM,aAAa,eAAe,SAAS,aAAa;AACxD,YAAM,cAAc,gBAAgB,SAAS,cAAc;AAC3D,eAAS,kBAAkB,GAAG,kBAAkB,KAAK,2BAA2B,UAAU,KAAK;AAC7F,YAAI,mBAAmB,GAAG;AACxB,eAAK,YAAY,SAAS,iBAAiB,EAAE,iBAAiB;AAC9D,eAAK,YAAY,OAAO,eAAe,IAAI,IAAI,kBAAkB,KAAK,sBAAsB,YAAY,eAAe,GAAG,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,CAAC;AACrL,6BAAmB;AACnB,6BAAmB;AAAA,QACrB,OAAO;AACL,eAAK,YAAY,SAAS,iBAAiB,EAAE,iBAAiB,kBAAkB;AAAA,QAClF;AACA,cAAM,UAAU,KAAK,sBAAsB,YAAY,eAAe;AACtE,gBAAQ,SAAS;AAAA,UACf,KAAK,wBAAwB,gCAAgC;AAC3D,iBAAK,YAAY,SAAS,iBAAiB,EAAE,cAAc,wBAAwB;AACnF,iBAAK,YAAY,SAAS,iBAAiB,EAAE,WAAW;AACxD,iBAAK,YAAY,OAAO,eAAe,IAAI,IAAI,kBAAkB,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,GAAG,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,CAAC;AACzL,+BAAmB;AACnB,+BAAmB;AACnB;AAAA,UACF;AAAA,UACA,KAAK,wBAAwB,gCAAgC;AAC3D,iBAAK,YAAY,SAAS,iBAAiB,EAAE,cAAc,wBAAwB;AACnF,gBAAI,wBAAwB,0BAA0B;AACpD,mBAAK,YAAY,SAAS,iBAAiB,EAAE,WAAW;AAAA,YAC1D,OAAO;AACL,mBAAK,YAAY,SAAS,iBAAiB,EAAE,WAAW;AAAA,YAC1D;AACA,iBAAK,YAAY,OAAO,eAAe,IAAI,IAAI,kBAAkB,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,GAAG,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,CAAC;AACzL,iBAAK,YAAY,OAAO,kBAAkB,CAAC,IAAI,IAAI,kBAAkB,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,GAAG,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,CAAC;AAC7L,iBAAK,YAAY,OAAO,kBAAkB,CAAC,IAAI,IAAI,kBAAkB,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,GAAG,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,CAAC;AAC7L,+BAAmB;AACnB,+BAAmB;AACnB;AAAA,UACF;AAAA,UACA,KAAK,wBAAwB,iCAAiC;AAC5D,iBAAK,YAAY,SAAS,iBAAiB,EAAE,cAAc,wBAAwB;AACnF,iBAAK,YAAY,SAAS,iBAAiB,EAAE,WAAW;AACxD,iBAAK,YAAY,OAAO,eAAe,IAAI,IAAI,kBAAkB,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,GAAG,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,CAAC;AACzL,+BAAmB;AACnB,+BAAmB;AACnB;AAAA,UACF;AAAA,UACA,KAAK,wBAAwB,wCAAwC;AACnE,iBAAK,YAAY,SAAS,iBAAiB,EAAE,cAAc,wBAAwB;AACnF,iBAAK,YAAY,SAAS,iBAAiB,EAAE,WAAW;AACxD,iBAAK,YAAY,OAAO,eAAe,IAAI,IAAI,kBAAkB,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,GAAG,KAAK,sBAAsB,YAAY,kBAAkB,CAAC,CAAC;AACzL,+BAAmB;AACnB,+BAAmB;AACnB;AAAA,UACF;AAAA,QACF;AACA,UAAE,MAAM;AACR,UAAE;AAAA,MACJ;AACA,WAAK,YAAY,OAAO,KAAK,KAAK;AAAA,IACpC;AACA,aAAS,gBAAgB,GAAG,gBAAgB,KAAK,cAAc,GAAG,EAAE,eAAe;AACjF,WAAK,YAAY,OAAO,aAAa,EAAE,WAAW,KAAK,aAAa,aAAa;AACjF,WAAK,YAAY,OAAO,aAAa,EAAE,QAAQ,KAAK,cAAc,aAAa;AAAA,IACjF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,cAAc,wBAAwB,mBAAmB;AACvD,SAAK,kBAAkB,SAAS;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,YAAY,EAAE,GAAG;AACpD,UAAI,KAAK,YAAY,OAAO,CAAC,EAAE,WAAW,0BAA0B,KAAK,YAAY,OAAO,CAAC,EAAE,YAAY,mBAAmB;AAC5H,aAAK,kBAAkB,KAAK,KAAK,YAAY,OAAO,CAAC,EAAE,KAAK;AAAA,MAC9D;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,uBAAN,cAAmC,cAAc;AAAA,EAC/C,YAAY,UAAU,SAAS;AAC7B,QAAI;AACJ,UAAM,UAAU,OAAO;AACvB,SAAK,SAAS,EAAE,MAAM,OAAO;AAC7B,SAAK,iBAAiB;AACtB,SAAK,eAAe,IAAI,yBAAyB;AACjD,SAAK,eAAe,KAAK,SAAS,YAAY,OAAO,KAAK,CAAC;AAC3D,SAAK,cAAc,SAAS,sBAAsB,KAAK,CAAC;AACxD,SAAK,aAAa,SAAS,qBAAqB,KAAK,CAAC;AACtD,SAAK,KAAK,OAAO;AAAA,EACnB;AAAA,EACA,KAAK,SAAS;AACZ,UAAM,KAAK,OAAO;AAClB,QAAI,KAAK,SAAS,aAAa;AAC7B,WAAK,oBAAoB,IAAI,yBAAyB,KAAK,UAAU,OAAO;AAAA,IAC9E;AACA,SAAK,aAAa,iBAAiB,CAAC,QAAQ,YAAY,eAAe;AACrE,WAAK,KAAK,YAAY,UAAU;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AACX,WAAO,KAAK,aAAa,WAAW;AAAA,EACtC;AAAA,EACA,aAAa,QAAQ,UAAU;AAC7B,WAAO,yBAAyB,QAAQ;AACxC,SAAK,aAAa,eAAe;AACjC,WAAO,KAAK,aAAa,YAAY,QAAQ,OAAO,YAAY,IAAI,CAAC;AAAA,EACvE;AAAA,EACA,kBAAkB;AAChB,SAAK,aAAa,eAAe;AAAA,EACnC;AAAA,EACA,aAAa,MAAM,OAAO,YAAY;AACpC,UAAM,SAAS,aAAa,OAAO,IAAI;AACvC,UAAM,OAAO,IAAI,iBAAiB,IAAI;AACtC,UAAM,yBAAyB,UAAU,KAAK,OAAO,OAAO,OAAO,2BAA2B,OAAO,wBAAwB;AAC7H,QAAI,KAAK,oBAAoB,MAAM,QAAQ;AACzC,aAAO,cAAc,WAAW,aAAa,IAAI,WAAW,aAAa,qBAAqB;AAAA,IAChG;AACA,QAAI,KAAK,qBAAqB,MAAM,QAAQ;AAC1C,aAAO,eAAe,WAAW,cAAc,IAAI,WAAW,cAAc,qBAAqB;AAAA,IACnG;AACA,WAAO,aAAa,KAAK,aAAa,KAAK,UAAU;AACrD,WAAO;AAAA,EACT;AAAA,EACA,cAAc,YAAY;AACxB,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,cAAc,YAAY;AACxB,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa,YAAY;AACvB,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB,OAAO,KAAK;AAC3B,WAAO,KAAK,aAAa,eAAe,OAAO,GAAG;AAAA,EACpD;AAAA,EACA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,aAAa,QAAQ;AAC1B,SAAK,eAAe;AAAA,EACtB;AACF;AACA,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,cAAc;AACpB,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,SAAK,oBAAoB,CAAC;AAC1B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO,SAAS;AACd,WAAO,IAAI,cAAa;AAAA,EAC1B;AAAA,EACA,cAAc,kBAAkB;AAC9B,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB,OAAO,kBAAkB;AACxC,SAAK,gBAAgB;AACrB,UAAM,IAAI,KAAK,eAAe,IAAI;AAClC,aAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,EAAE,GAAG;AACtD,YAAM,OAAO,KAAK,kBAAkB,CAAC;AACrC,YAAM,sBAAsB,KAAK,aAAa,KAAK,SAAS,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM;AAAA,IAC/G;AAAA,EACF;AACF;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,aAAa,QAAQ,MAAM,OAAO,QAAQ;AACpD,SAAK,cAAc,eAAe,SAAS,SAAS;AACpD,SAAK,SAAS,UAAU,SAAS,IAAI;AACrC,SAAK,OAAO,QAAQ,SAAS,IAAI;AACjC,SAAK,QAAQ,SAAS,SAAS,IAAI;AACnC,SAAK,SAAS,UAAU,SAAS,IAAI;AAAA,EACvC;AACF;AACA,IAAM,kBAAkB,MAAM;AAAA,EAC5B,OAAO,OAAO,cAAc;AAC1B,WAAO,IAAI,gBAAgB,YAAY;AAAA,EACzC;AAAA,EACA,oBAAoB,kBAAkB;AACpC,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,mBAAmB,SAAS,QAAQ,SAAS;AAC3C,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,gBAAgB,cAAc;AAC5B,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB,OAAO,kBAAkB;AACxC,SAAK,oBAAoB;AACzB,QAAI;AACJ,QAAI,IAAI;AACR,YAAQ,KAAK,gBAAgB;AAAA,MAC3B,KAAK,SAAS;AACZ,aAAK,KAAK,mBAAmB,KAAK,0BAA0B,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,eAAK,iBAAiB,SAAS;AAC/B,eAAK,yBAAyB,KAAK;AAAA,QACrC;AACA,yBAAiB,IAAI;AACrB;AAAA,MACF,KAAK,SAAS;AACZ,aAAK,KAAK,mBAAmB,KAAK,0BAA0B,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,eAAK,iBAAiB,SAAS;AAC/B,eAAK,yBAAyB,KAAK;AAAA,QACrC;AACA,yBAAiB;AACjB;AAAA,MACF,KAAK,SAAS;AACZ,aAAK,KAAK,mBAAmB,KAAK,0BAA0B,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,eAAK,iBAAiB,SAAS;AAC/B,eAAK,oBAAoB,KAAK,2BAA2B;AAAA,QAC3D;AACA,yBAAiB;AACjB;AAAA,MACF,KAAK,SAAS;AACZ,YAAI,KAAK,oBAAoB,KAAK,kBAAkB;AAClD,eAAK,iBAAiB,SAAS;AAC/B,eAAK,yBAAyB,KAAK;AAAA,QACrC;AACA,yBAAiB;AACjB;AAAA,MACF,KAAK,SAAS;AAAA,MACd;AACE,aAAK,iBAAiB,SAAS;AAC/B,aAAK,oBAAoB,KAAK,2BAA2B;AACzD,yBAAiB;AACjB;AAAA,IACJ;AACA,QAAI,CAAC,gBAAgB,aAAa;AAChC,uBAAiB,CAAC;AAAA,IACpB;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,QAAQ,EAAE,GAAG;AAClD,YAAM,sBAAsB,KAAK,cAAc,CAAC,GAAG,cAAc;AAAA,IACnE;AAAA,EACF;AAAA,EACA,YAAY,cAAc;AACxB,QAAI,IAAI;AACR,SAAK,iBAAiB,SAAS;AAC/B,SAAK,oBAAoB;AACzB,SAAK,yBAAyB;AAC9B,SAAK,2BAA2B;AAChC,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB,CAAC;AACtB,QAAI,gBAAgB,MAAM;AACxB;AAAA,IACF;AACA,SAAK,iBAAiB,MAAM,KAAK,aAAa,sBAAsB,MAAM,OAAO,SAAS,GAAG,MAAM,MAAM,OAAO,KAAK,KAAK;AAAA,EAC5H;AAAA,EACA,6BAA6B;AAC3B,UAAM,IAAI,KAAK,OAAO;AACtB,WAAO,KAAK,mBAAmB,KAAK,IAAI,KAAK,2BAA2B;AAAA,EAC1E;AACF;AACA,IAAI,iBAAiB;AACrB,eAAe,cAAc;AAC7B,IAAI,YAA4B,CAAC,cAAc;AAC7C,YAAU,UAAU,gBAAgB,IAAI,CAAC,IAAI;AAC7C,YAAU,UAAU,mBAAmB,IAAI,CAAC,IAAI;AAChD,YAAU,UAAU,kBAAkB,IAAI,CAAC,IAAI;AAC/C,YAAU,UAAU,iBAAiB,IAAI,CAAC,IAAI;AAC9C,YAAU,UAAU,kBAAkB,IAAI,CAAC,IAAI;AAC/C,SAAO;AACT,GAAG,YAAY,CAAC,CAAC;AACjB,IAAM,UAAN,MAAc;AAAA,EACZ,YAAY,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AACtC,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,IAAI,MAAM,KAAK;AAAA,EAC7B;AAAA,EACA,aAAa;AACX,WAAO,KAAK,IAAI,MAAM,KAAK;AAAA,EAC7B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AAAA,EACA,YAAY;AACV,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AAAA,EACA,QAAQ,GAAG;AACT,SAAK,IAAI,EAAE;AACX,SAAK,IAAI,EAAE;AACX,SAAK,QAAQ,EAAE;AACf,SAAK,SAAS,EAAE;AAAA,EAClB;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,SAAS,IAAI;AAClB,SAAK,UAAU,IAAI;AAAA,EACrB;AACF;AACA,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAM,8BAAN,MAAkC;AAAA,EAChC,sBAAsB,WAAW;AAC/B,WAAO,KAAK,eAAe,SAAS;AAAA,EACtC;AAAA,EACA,uBAAuB;AACrB,QAAI,MAAM;AACV,QAAI,KAAK,gBAAgB,KAAK,aAAa,WAAW,GAAG;AACvD,WAAK,aAAa,UAAU,KAAK;AACjC,YAAM,KAAK,aAAa;AAAA,IAC1B;AACA,QAAI,OAAO,GAAG;AACZ,YAAM,OAAO,KAAK;AAClB,WAAK,eAAe,KAAK,GAAG,cAAc;AAC1C,WAAK,GAAG,YAAY,KAAK,GAAG,YAAY,KAAK,YAAY;AACzD,WAAK,GAAG,WAAW,KAAK,GAAG,YAAY,GAAG,KAAK,GAAG,MAAM,MAAM,MAAM,GAAG,KAAK,GAAG,MAAM,KAAK,GAAG,eAAe,IAAI;AAChH,WAAK,GAAG,cAAc,KAAK,GAAG,YAAY,KAAK,GAAG,gBAAgB,KAAK,GAAG,aAAa;AACvF,WAAK,GAAG,cAAc,KAAK,GAAG,YAAY,KAAK,GAAG,gBAAgB,KAAK,GAAG,aAAa;AACvF,WAAK,GAAG,cAAc,KAAK,GAAG,YAAY,KAAK,GAAG,oBAAoB,KAAK,GAAG,MAAM;AACpF,WAAK,GAAG,cAAc,KAAK,GAAG,YAAY,KAAK,GAAG,oBAAoB,KAAK,GAAG,MAAM;AACpF,WAAK,GAAG,YAAY,KAAK,GAAG,YAAY,IAAI;AAC5C,YAAM,KAAK,GAAG,kBAAkB;AAChC,WAAK,GAAG,gBAAgB,KAAK,GAAG,aAAa,GAAG;AAChD,WAAK,GAAG,qBAAqB,KAAK,GAAG,aAAa,KAAK,GAAG,mBAAmB,KAAK,GAAG,YAAY,KAAK,cAAc,CAAC;AACrH,WAAK,GAAG,gBAAgB,KAAK,GAAG,aAAa,KAAK;AAClD,WAAK,eAAe,IAAI,4BAA4B,KAAK,iBAAiB,GAAG;AAAA,IAC/E;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,IAAI;AACR,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,2BAA2B,OAAO,iBAAiB;AACjD,QAAI,uBAAuB,OAAO;AAClC,QAAI,uBAAuB,OAAO;AAClC,QAAI,uBAAuB,OAAO;AAClC,QAAI,uBAAuB,OAAO;AAClC,UAAM,mBAAmB,gBAAgB,0BAA0B;AACnE,aAAS,uBAAuB,GAAG,uBAAuB,kBAAkB,wBAAwB;AAClG,YAAM,gBAAgB,gBAAgB,0BAA0B,oBAAoB;AACpF,YAAM,sBAAsB,MAAM,uBAAuB,aAAa;AACtE,YAAM,mBAAmB,MAAM,oBAAoB,aAAa;AAChE,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO,OAAO;AAClB,YAAM,OAAO,sBAAsB,SAAS;AAC5C,eAAS,KAAK,SAAS,cAAc,KAAK,MAAM,MAAM,SAAS,YAAY;AACzE,cAAM,IAAI,iBAAiB,EAAE;AAC7B,cAAM,IAAI,iBAAiB,KAAK,CAAC;AACjC,YAAI,IAAI,MAAM;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,MAAM;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,MAAM;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,MAAM;AACZ,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,QAAQ,OAAO,WAAW;AAC5B;AAAA,MACF;AACA,UAAI,OAAO,sBAAsB;AAC/B,+BAAuB;AAAA,MACzB;AACA,UAAI,OAAO,sBAAsB;AAC/B,+BAAuB;AAAA,MACzB;AACA,UAAI,OAAO,sBAAsB;AAC/B,+BAAuB;AAAA,MACzB;AACA,UAAI,OAAO,sBAAsB;AAC/B,+BAAuB;AAAA,MACzB;AACA,UAAI,wBAAwB,OAAO,WAAW;AAC5C,wBAAgB,oBAAoB,IAAI;AACxC,wBAAgB,oBAAoB,IAAI;AACxC,wBAAgB,oBAAoB,QAAQ;AAC5C,wBAAgB,oBAAoB,SAAS;AAC7C,wBAAgB,WAAW;AAAA,MAC7B,OAAO;AACL,wBAAgB,WAAW;AAC3B,cAAM,IAAI,uBAAuB;AACjC,cAAM,IAAI,uBAAuB;AACjC,wBAAgB,oBAAoB,IAAI;AACxC,wBAAgB,oBAAoB,IAAI;AACxC,wBAAgB,oBAAoB,QAAQ;AAC5C,wBAAgB,oBAAoB,SAAS;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,qBAAqB;AAC1B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,0BAA0B;AAC/B,SAAK,8BAA8B,CAAC;AACpC,SAAK,8BAA8B,CAAC;AACpC,SAAK,iBAAiB,CAAC;AACvB,SAAK,oBAAoB,IAAI,QAAQ;AACrC,SAAK,aAAa,IAAI,eAAe;AACrC,SAAK,oBAAoB,IAAI,eAAe;AAC5C,SAAK,oBAAoB,IAAI,eAAe;AAC5C,QAAI,MAAM,IAAI,mBAAmB;AACjC,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,SAAK,eAAe,KAAK,GAAG;AAC5B,UAAM,IAAI,mBAAmB;AAC7B,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,SAAK,eAAe,KAAK,GAAG;AAC5B,UAAM,IAAI,mBAAmB;AAC7B,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,SAAK,eAAe,KAAK,GAAG;AAC5B,UAAM,IAAI,mBAAmB;AAC7B,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,SAAK,eAAe,KAAK,GAAG;AAAA,EAC9B;AAAA,EACA,UAAU;AACR,QAAI,IAAI,IAAI;AACZ,UAAM,OAAO;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,4BAA4B,QAAQ,KAAK;AAChE,UAAI,KAAK,4BAA4B,CAAC,GAAG;AACvC,SAAC,KAAK,KAAK,4BAA4B,CAAC,MAAM,OAAO,SAAS,GAAG,QAAQ;AAAA,MAC3E;AAAA,IACF;AACA,SAAK,8BAA8B;AACnC,SAAK,8BAA8B;AACnC,QAAI,KAAK,cAAc;AACrB,OAAC,KAAK,KAAK,OAAO,OAAO,SAAS,GAAG,kBAAkB,KAAK,aAAa,OAAO;AAChF,WAAK,eAAe;AAAA,IACtB;AACA,SAAK,iBAAiB;AACtB,KAAC,KAAK,KAAK,OAAO,OAAO,SAAS,GAAG,cAAc,KAAK,YAAY;AACpE,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,WAAW,OAAO,eAAe,eAAe,oBAAoB;AAClE,aAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,UAAI,mBAAmB,CAAC,KAAK,GAAG;AAC9B,aAAK,4BAA4B,KAAK,IAAI;AAC1C;AAAA,MACF;AACA,UAAI,kBAAkB,KAAK,aAAa,cAAc,CAAC,GAAG,mBAAmB,CAAC,CAAC;AAC/E,UAAI,mBAAmB,MAAM;AAC3B,0BAAkB,IAAI,sBAAsB,MAAM,cAAc,CAAC,GAAG,mBAAmB,CAAC,CAAC;AACzF,aAAK,4BAA4B,KAAK,eAAe;AAAA,MACvD;AACA,sBAAgB,mBAAmB,CAAC;AACpC,WAAK,4BAA4B,KAAK,eAAe;AAAA,IACvD;AAAA,EACF;AAAA,EACA,qBAAqB,OAAO,UAAU;AACpC,SAAK;AACL,QAAI,iBAAiB;AACrB,aAAS,YAAY,GAAG,YAAY,KAAK,4BAA4B,QAAQ,aAAa;AACxF,YAAM,KAAK,KAAK,4BAA4B,SAAS;AACrD,WAAK,2BAA2B,OAAO,EAAE;AACzC,UAAI,GAAG,UAAU;AACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,iBAAiB,GAAG;AACtB,WAAK,GAAG,SAAS,GAAG,GAAG,KAAK,yBAAyB,KAAK,uBAAuB;AACjF,WAAK,qBAAqB,KAAK,qBAAqB;AACpD,eAAS,aAAa;AACtB,eAAS,QAAQ;AACjB,WAAK,kBAAkB,cAAc;AACrC,WAAK,GAAG,gBAAgB,KAAK,GAAG,aAAa,KAAK,kBAAkB;AACpE,WAAK,GAAG,WAAW,GAAG,GAAG,GAAG,CAAC;AAC7B,WAAK,GAAG,MAAM,KAAK,GAAG,gBAAgB;AACtC,eAAS,YAAY,GAAG,YAAY,KAAK,4BAA4B,QAAQ,aAAa;AACxF,cAAM,cAAc,KAAK,4BAA4B,SAAS;AAC9D,cAAM,oBAAoB,YAAY;AACtC,cAAM,sBAAsB,YAAY;AACxC,cAAM,SAAS;AACf,aAAK,kBAAkB,QAAQ,iBAAiB;AAChD,aAAK,kBAAkB,OAAO,kBAAkB,QAAQ,QAAQ,kBAAkB,SAAS,MAAM;AACjG,cAAM,SAAS,oBAAoB,QAAQ,KAAK,kBAAkB;AAClE,cAAM,SAAS,oBAAoB,SAAS,KAAK,kBAAkB;AACnE;AACE,eAAK,WAAW,aAAa;AAC7B;AACE,iBAAK,WAAW,kBAAkB,IAAI,EAAE;AACxC,iBAAK,WAAW,cAAc,GAAG,CAAC;AAAA,UACpC;AACA;AACE,iBAAK,WAAW,kBAAkB,oBAAoB,GAAG,oBAAoB,CAAC;AAC9E,iBAAK,WAAW,cAAc,QAAQ,MAAM;AAC5C,iBAAK,WAAW,kBAAkB,CAAC,KAAK,kBAAkB,GAAG,CAAC,KAAK,kBAAkB,CAAC;AAAA,UACxF;AACA,eAAK,kBAAkB,UAAU,KAAK,WAAW,SAAS,CAAC;AAAA,QAC7D;AACA;AACE,eAAK,WAAW,aAAa;AAC7B;AACE,iBAAK,WAAW,kBAAkB,oBAAoB,GAAG,oBAAoB,CAAC;AAC9E,iBAAK,WAAW,cAAc,QAAQ,MAAM;AAC5C,iBAAK,WAAW,kBAAkB,CAAC,KAAK,kBAAkB,GAAG,CAAC,KAAK,kBAAkB,CAAC;AAAA,UACxF;AACA,eAAK,kBAAkB,UAAU,KAAK,WAAW,SAAS,CAAC;AAAA,QAC7D;AACA,oBAAY,eAAe,UAAU,KAAK,kBAAkB,SAAS,CAAC;AACtE,oBAAY,eAAe,UAAU,KAAK,kBAAkB,SAAS,CAAC;AACtE,cAAM,gBAAgB,YAAY;AAClC,iBAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,gBAAM,gBAAgB,YAAY,gBAAgB,CAAC;AACnD,cAAI,CAAC,MAAM,+CAA+C,aAAa,GAAG;AACxE;AAAA,UACF;AACA,mBAAS,aAAa,MAAM,mBAAmB,aAAa,KAAK,KAAK;AACtE,mBAAS,gCAAgC,WAAW;AACpD,mBAAS,SAAS,MAAM,0BAA0B,aAAa,GAAG,MAAM,4BAA4B,aAAa,GAAG,MAAM,uBAAuB,aAAa,GAAG,MAAM,yBAAyB,aAAa,GAAG,MAAM,oBAAoB,aAAa,GAAG,MAAM,qBAAqB,aAAa,GAAG,MAAM,mBAAmB,aAAa,GAAG,gBAAgB,wBAAwB,KAAK;AAAA,QAC7X;AAAA,MACF;AACA,WAAK,GAAG,gBAAgB,KAAK,GAAG,aAAa,KAAK;AAClD,eAAS,gCAAgC,IAAI;AAC7C,WAAK,GAAG,SAAS,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,aAAa,eAAe,oBAAoB;AAC9C,aAAS,IAAI,GAAG,IAAI,KAAK,4BAA4B,QAAQ,KAAK;AAChE,YAAM,kBAAkB,KAAK,4BAA4B,CAAC;AAC1D,YAAM,QAAQ,gBAAgB;AAC9B,UAAI,SAAS,oBAAoB;AAC/B;AAAA,MACF;AACA,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAM,SAAS,gBAAgB,gBAAgB,CAAC;AAChD,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAI,cAAc,CAAC,KAAK,QAAQ;AAC9B;AACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,aAAa,OAAO;AACtB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,gBAAgB;AAChC,QAAI,MAAM,iBAAiB;AAC3B,QAAI,MAAM,iBAAiB;AAC3B,UAAM,CAAC,CAAC;AACR,UAAM,CAAC,CAAC;AACR,QAAI,eAAe;AACnB,aAAS,YAAY,GAAG,YAAY,mBAAmB,aAAa;AAClE,YAAM,cAAc,OAAO,YAAY,MAAM,IAAI;AACjD,UAAI,eAAe;AACjB;AAAA,eACO,eAAe,GAAG;AACzB,cAAM,cAAc,KAAK,4BAA4B,cAAc;AACnE,oBAAY,mBAAmB;AAC/B,oBAAY,cAAc,IAAI;AAC9B,oBAAY,cAAc,IAAI;AAC9B,oBAAY,cAAc,QAAQ;AAClC,oBAAY,cAAc,SAAS;AAAA,MACrC,WAAW,eAAe,GAAG;AAC3B,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,cAAI,OAAO,IAAI;AACf,iBAAO,CAAC,CAAC;AACT,gBAAM,KAAK,KAAK,4BAA4B,cAAc;AAC1D,aAAG,mBAAmB;AACtB,aAAG,cAAc,IAAI,OAAO;AAC5B,aAAG,cAAc,IAAI;AACrB,aAAG,cAAc,QAAQ;AACzB,aAAG,cAAc,SAAS;AAAA,QAC5B;AAAA,MACF,WAAW,eAAe,GAAG;AAC3B,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,cAAI,OAAO,IAAI;AACf,cAAI,OAAO,IAAI;AACf,iBAAO,CAAC,CAAC;AACT,iBAAO,CAAC,CAAC;AACT,gBAAM,KAAK,KAAK,4BAA4B,cAAc;AAC1D,aAAG,mBAAmB;AACtB,aAAG,cAAc,IAAI,OAAO;AAC5B,aAAG,cAAc,IAAI,OAAO;AAC5B,aAAG,cAAc,QAAQ;AACzB,aAAG,cAAc,SAAS;AAAA,QAC5B;AAAA,MACF,WAAW,eAAe,GAAG;AAC3B,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,cAAI,OAAO,IAAI;AACf,cAAI,OAAO,IAAI;AACf,iBAAO,CAAC,CAAC;AACT,iBAAO,CAAC,CAAC;AACT,gBAAM,KAAK,KAAK,4BAA4B,cAAc;AAC1D,aAAG,mBAAmB;AACtB,aAAG,cAAc,IAAI,OAAO;AAC5B,aAAG,cAAc,IAAI,OAAO;AAC5B,aAAG,cAAc,QAAQ,IAAI;AAC7B,aAAG,cAAc,SAAS,IAAI;AAAA,QAChC;AAAA,MACF,WAAW,aAAa,4BAA4B,eAAe,IAAI;AACrE,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,cAAI,OAAO,IAAI;AACf,cAAI,OAAO,IAAI;AACf,iBAAO,CAAC,CAAC;AACT,iBAAO,CAAC,CAAC;AACT,gBAAM,KAAK,KAAK,4BAA4B,cAAc;AAC1D,aAAG,mBAAmB;AACtB,aAAG,cAAc,IAAI,OAAO;AAC5B,aAAG,cAAc,IAAI,OAAO;AAC5B,aAAG,cAAc,QAAQ,IAAI;AAC7B,aAAG,cAAc,SAAS,IAAI;AAAA,QAChC;AAAA,MACF,OAAO;AACL,uBAAe,kCAAkC,WAAW;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,gCAAgC;AAC9B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,0BAA0B,MAAM;AAC9B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,8BAAN,MAAkC;AAAA,EAChC,YAAY,SAAS,SAAS;AAC5B,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,SAAS,yBAAyB,WAAW;AACvD,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,4BAA4B,CAAC;AAClC,SAAK,iBAAiB,IAAI,eAAe;AACzC,SAAK,iBAAiB,IAAI,eAAe;AAAA,EAC3C;AAAA,EACA,UAAU;AACR,UAAM,OAAO;AACb,SAAK,gBAAgB;AACrB,SAAK,sBAAsB;AAC3B,SAAK,4BAA4B;AAAA,EACnC;AAAA,EACA,mBAAmB,eAAe;AAChC,SAAK,0BAA0B,KAAK,aAAa;AAAA,EACnD;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAM,IAAI;AACR,SAAK,OAAO,MAAM,EAAE;AAAA,EACtB;AACF;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,cAAc;AACnB,QAAI,cAAc,MAAM;AACtB,mBAAa,IAAI,oBAAmB;AACpC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,iBAAiB;AACtB,QAAI,YAAY;AACd,iBAAW,QAAQ;AACnB,mBAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,UAAU;AACR,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,mBAAmB,UAAU,WAAW,aAAa,aAAa,YAAY,SAAS,YAAY,SAAS,gBAAgB,WAAW,sBAAsB,WAAW,cAAc;AACpL,QAAI,CAAC,sBAAsB;AACzB,qBAAe,qCAAqC;AAAA,IACtD;AACA,QAAI,KAAK,YAAY,UAAU,GAAG;AAChC,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,+BAA+B,SAAS,gCAAgC;AAC9E,QAAI,gCAAgC,MAAM;AACxC,YAAM,YAAY,KAAK,YAAY,YAAY,qBAAqB;AACpE,WAAK,GAAG,WAAW,UAAU,aAAa;AAC1C,WAAK,GAAG,cAAc,KAAK,GAAG,QAAQ;AACtC,WAAK,GAAG,YAAY,KAAK,GAAG,YAAY,SAAS;AACjD,WAAK,GAAG,UAAU,UAAU,yBAAyB,CAAC;AACtD,UAAI,WAAW,UAAU,MAAM;AAC7B,mBAAW,SAAS,KAAK,GAAG,aAAa;AAAA,MAC3C;AACA,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,WAAW,MAAM;AAC1D,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,aAAa,KAAK,GAAG,YAAY;AAC1E,WAAK,GAAG,wBAAwB,UAAU,yBAAyB;AACnE,WAAK,GAAG,oBAAoB,UAAU,2BAA2B,GAAG,KAAK,GAAG,OAAO,OAAO,GAAG,CAAC;AAC9F,UAAI,WAAW,MAAM,MAAM;AACzB,mBAAW,KAAK,KAAK,GAAG,aAAa;AAAA,MACvC;AACA,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,WAAW,EAAE;AACtD,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,SAAS,KAAK,GAAG,YAAY;AACtE,WAAK,GAAG,wBAAwB,UAAU,yBAAyB;AACnE,WAAK,GAAG,oBAAoB,UAAU,2BAA2B,GAAG,KAAK,GAAG,OAAO,OAAO,GAAG,CAAC;AAC9F,YAAM,YAAY,6BAA6B;AAC/C,YAAM,eAAe,6BAA6B,mBAAmB,EAAE,sBAAsB,SAAS;AACtG,WAAK,GAAG,UAAU,UAAU,4BAA4B,aAAa,GAAG,aAAa,GAAG,aAAa,GAAG,aAAa,CAAC;AACtH,WAAK,GAAG,iBAAiB,UAAU,2BAA2B,OAAO,6BAA6B,eAAe,SAAS,CAAC;AAC3H,YAAM,OAAO,6BAA6B;AAC1C,WAAK,GAAG,UAAU,UAAU,0BAA0B,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,GAAG,KAAK,UAAU,IAAI,IAAI,CAAC;AACvI,kBAAY,KAAK,GAAG;AACpB,kBAAY,KAAK,GAAG;AACpB,kBAAY,KAAK,GAAG;AACpB,kBAAY,KAAK,GAAG;AAAA,IACtB,OAAO;AACL,YAAM,+BAA+B,SAAS,gCAAgC;AAC9E,YAAM,SAAS,gCAAgC;AAC/C,YAAM,SAAS,SAAS,eAAe,IAAI,IAAI;AAC/C,UAAI;AACJ,cAAQ,gBAAgB;AAAA,QACtB,KAAK,gBAAgB;AAAA,QACrB;AACE,sBAAY,KAAK,YAAY,YAAY,uCAAuC,MAAM;AACtF,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB;AAAA,QACF,KAAK,gBAAgB;AACnB,sBAAY,KAAK,YAAY,YAAY,oCAAoC,MAAM;AACnF,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB;AAAA,QACF,KAAK,gBAAgB;AACnB,sBAAY,KAAK,YAAY,YAAY,qCAAqC,MAAM;AACpF,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB,sBAAY,KAAK,GAAG;AACpB;AAAA,MACJ;AACA,WAAK,GAAG,WAAW,UAAU,aAAa;AAC1C,UAAI,WAAW,UAAU,MAAM;AAC7B,mBAAW,SAAS,KAAK,GAAG,aAAa;AAAA,MAC3C;AACA,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,WAAW,MAAM;AAC1D,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,aAAa,KAAK,GAAG,YAAY;AAC1E,WAAK,GAAG,wBAAwB,UAAU,yBAAyB;AACnE,WAAK,GAAG,oBAAoB,UAAU,2BAA2B,GAAG,KAAK,GAAG,OAAO,OAAO,GAAG,CAAC;AAC9F,UAAI,WAAW,MAAM,MAAM;AACzB,mBAAW,KAAK,KAAK,GAAG,aAAa;AAAA,MACvC;AACA,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,WAAW,EAAE;AACtD,WAAK,GAAG,WAAW,KAAK,GAAG,cAAc,SAAS,KAAK,GAAG,YAAY;AACtE,WAAK,GAAG,wBAAwB,UAAU,yBAAyB;AACnE,WAAK,GAAG,oBAAoB,UAAU,2BAA2B,GAAG,KAAK,GAAG,OAAO,OAAO,GAAG,CAAC;AAC9F,UAAI,gCAAgC,MAAM;AACxC,aAAK,GAAG,cAAc,KAAK,GAAG,QAAQ;AACtC,cAAM,MAAM,6BAA6B,mBAAmB,EAAE,eAAe;AAC7E,aAAK,GAAG,YAAY,KAAK,GAAG,YAAY,GAAG;AAC3C,aAAK,GAAG,UAAU,UAAU,yBAAyB,CAAC;AACtD,aAAK,GAAG,iBAAiB,UAAU,2BAA2B,OAAO,6BAA6B,eAAe,SAAS,CAAC;AAC3H,cAAM,YAAY,6BAA6B;AAC/C,cAAM,eAAe,6BAA6B,mBAAmB,EAAE,sBAAsB,SAAS;AACtG,aAAK,GAAG,UAAU,UAAU,4BAA4B,aAAa,GAAG,aAAa,GAAG,aAAa,GAAG,aAAa,CAAC;AAAA,MACxH;AACA,WAAK,GAAG,cAAc,KAAK,GAAG,QAAQ;AACtC,WAAK,GAAG,YAAY,KAAK,GAAG,YAAY,SAAS;AACjD,WAAK,GAAG,UAAU,UAAU,yBAAyB,CAAC;AACtD,WAAK,GAAG,iBAAiB,UAAU,uBAAuB,OAAO,UAAU,SAAS,CAAC;AACrF,WAAK,GAAG,UAAU,UAAU,0BAA0B,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC;AAAA,IAC1G;AACA,QAAI,WAAW,SAAS,MAAM;AAC5B,iBAAW,QAAQ,KAAK,GAAG,aAAa;AAAA,IAC1C;AACA,SAAK,GAAG,WAAW,KAAK,GAAG,sBAAsB,WAAW,KAAK;AACjE,SAAK,GAAG,WAAW,KAAK,GAAG,sBAAsB,YAAY,KAAK,GAAG,YAAY;AACjF,SAAK,GAAG,kBAAkB,WAAW,WAAW,WAAW,SAAS;AAAA,EACtE;AAAA,EACA,uBAAuB;AACrB,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,WAAK,GAAG,cAAc,KAAK,YAAY,CAAC,EAAE,aAAa;AACvD,WAAK,YAAY,CAAC,EAAE,gBAAgB;AAAA,IACtC;AACA,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,kBAAkB;AAChB,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,WAAK,YAAY,KAAK,CAAC,CAAC;AAAA,IAC1B;AACA,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,kBAAkB,0BAA0B,0BAA0B;AAC/G,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,kBAAkB,iBAAiB,mCAAmC;AAC/G,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,kBAAkB,uBAAuB,uCAAuC;AACzH,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,kBAAkB,uBAAuB,+CAA+C;AACjI,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,YAAY,CAAC,EAAE;AACxD,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,YAAY,CAAC,EAAE;AACxD,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,YAAY,CAAC,EAAE;AACxD,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,YAAY,CAAC,EAAE;AACxD,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,YAAY,CAAC,EAAE;AACxD,SAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,YAAY,CAAC,EAAE;AACxD,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,cAAc;AAC5H,SAAK,YAAY,CAAC,EAAE,6BAA6B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,eAAe;AAC9H,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,cAAc;AAC5H,SAAK,YAAY,CAAC,EAAE,6BAA6B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,eAAe;AAC9H,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,cAAc;AAC5H,SAAK,YAAY,CAAC,EAAE,6BAA6B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,eAAe;AAC9H,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,cAAc;AAC5H,SAAK,YAAY,CAAC,EAAE,6BAA6B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,eAAe;AAC9H,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,cAAc;AAC5H,SAAK,YAAY,CAAC,EAAE,6BAA6B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,eAAe;AAC9H,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,cAAc;AAC5H,SAAK,YAAY,CAAC,EAAE,6BAA6B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,eAAe;AAC9H,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAC1H,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,kBAAkB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACzH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,0BAA0B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,YAAY;AACxH,SAAK,YAAY,CAAC,EAAE,wBAAwB,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,UAAU;AACpH,SAAK,YAAY,CAAC,EAAE,4BAA4B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,cAAc;AAC5H,SAAK,YAAY,CAAC,EAAE,6BAA6B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,eAAe;AAC9H,SAAK,YAAY,CAAC,EAAE,2BAA2B,KAAK,GAAG,mBAAmB,KAAK,YAAY,CAAC,EAAE,eAAe,aAAa;AAAA,EAC5H;AAAA,EACA,kBAAkB,oBAAoB,sBAAsB;AAC1D,QAAI,gBAAgB,KAAK,GAAG,cAAc;AAC1C,QAAI,aAAa,KAAK,oBAAoB,KAAK,GAAG,eAAe,kBAAkB;AACnF,QAAI,CAAC,YAAY;AACf,qBAAe,8BAA8B;AAC7C,aAAO;AAAA,IACT;AACA,QAAI,aAAa,KAAK,oBAAoB,KAAK,GAAG,iBAAiB,oBAAoB;AACvF,QAAI,CAAC,YAAY;AACf,qBAAe,8BAA8B;AAC7C,aAAO;AAAA,IACT;AACA,SAAK,GAAG,aAAa,eAAe,UAAU;AAC9C,SAAK,GAAG,aAAa,eAAe,UAAU;AAC9C,SAAK,GAAG,YAAY,aAAa;AACjC,UAAM,aAAa,KAAK,GAAG,oBAAoB,eAAe,KAAK,GAAG,WAAW;AACjF,QAAI,CAAC,YAAY;AACf,qBAAe,+BAA+B,aAAa;AAC3D,WAAK,GAAG,aAAa,UAAU;AAC/B,WAAK,GAAG,aAAa,UAAU;AAC/B,UAAI,eAAe;AACjB,aAAK,GAAG,cAAc,aAAa;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AACA,SAAK,GAAG,aAAa,UAAU;AAC/B,SAAK,GAAG,aAAa,UAAU;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,YAAY,cAAc;AAC5C,UAAM,SAAS;AACf,UAAM,SAAS,KAAK,GAAG,aAAa,UAAU;AAC9C,SAAK,GAAG,aAAa,QAAQ,MAAM;AACnC,SAAK,GAAG,cAAc,MAAM;AAC5B,QAAI,CAAC,QAAQ;AACX,YAAM,MAAM,KAAK,GAAG,iBAAiB,MAAM;AAC3C,qBAAe,4BAA4B,GAAG;AAAA,IAChD;AACA,UAAM,SAAS,KAAK,GAAG,mBAAmB,QAAQ,KAAK,GAAG,cAAc;AACxE,QAAI,CAAC,QAAQ;AACX,WAAK,GAAG,aAAa,MAAM;AAC3B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,IAAI;AACR,SAAK,KAAK;AAAA,EACZ;AACF;AACA,IAAI,eAA+B,CAAC,iBAAiB;AACnD,eAAa,aAAa,uBAAuB,IAAI,CAAC,IAAI;AAC1D,eAAa,aAAa,sCAAsC,IAAI,CAAC,IAAI;AACzE,eAAa,aAAa,4CAA4C,IAAI,CAAC,IAAI;AAC/E,eAAa,aAAa,oDAAoD,IAAI,CAAC,IAAI;AACvF,eAAa,aAAa,mCAAmC,IAAI,CAAC,IAAI;AACtE,eAAa,aAAa,yCAAyC,IAAI,CAAC,IAAI;AAC5E,eAAa,aAAa,iDAAiD,IAAI,CAAC,IAAI;AACpF,eAAa,aAAa,oCAAoC,IAAI,CAAC,IAAI;AACvE,eAAa,aAAa,0CAA0C,IAAI,CAAC,IAAI;AAC7E,eAAa,aAAa,kDAAkD,IAAI,CAAC,IAAI;AACrF,SAAO;AACT,GAAG,eAAe,CAAC,CAAC;AACpB,IAAM,2BAA2B;AACjC,IAAM,6BAA6B;AACnC,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,sCAAsC;AAC5C,IAAM,0CAA0C;AAChD,IAAM,kDAAkD;AACxD,IAAM,uBAAN,cAAmC,eAAe;AAAA,EAChD,cAAc;AACZ,UAAM;AACN,SAAK,gCAAgC;AACrC,SAAK,gCAAgC;AACrC,SAAK,mBAAmB,IAAI,4BAA4B;AACxD,SAAK,YAAY;AACjB,SAAK,YAAY,CAAC;AAClB,SAAK,2BAA2B,CAAC;AACjC,SAAK,cAAc;AAAA,MACjB,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,MAAM,eAAe,GAAG;AAC1B,WAAK,mBAAmB,IAAI,4BAA4B;AACxD,WAAK,iBAAiB,WAAW,OAAO,MAAM,iBAAiB,GAAG,MAAM,iBAAiB,GAAG,MAAM,sBAAsB,CAAC;AAAA,IAC3H;AACA,aAAS,IAAI,MAAM,iBAAiB,IAAI,GAAG,KAAK,GAAG,KAAK;AACtD,WAAK,yBAAyB,CAAC,IAAI;AAAA,IACrC;AACA,UAAM,WAAW,KAAK;AAAA,EACxB;AAAA,EACA,YAAY,gBAAgB,WAAW;AACrC,SAAK,UAAU,cAAc,IAAI;AAAA,EACnC;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,0BAA0B,MAAM;AAC9B,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,mBAAmB,IAAI,4BAA4B;AACxD,SAAK,iBAAiB,0BAA0B,IAAI;AACpD,SAAK,iBAAiB,WAAW,KAAK,SAAS,GAAG,KAAK,SAAS,EAAE,iBAAiB,GAAG,KAAK,SAAS,EAAE,iBAAiB,GAAG,KAAK,SAAS,EAAE,sBAAsB,CAAC;AAAA,EACnK;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,iBAAiB,0BAA0B;AAAA,EACzD;AAAA,EACA,UAAU;AACR,QAAI,IAAI,IAAI;AACZ,UAAM,OAAO;AACb,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,mBAAmB;AACxB,KAAC,KAAK,KAAK,OAAO,OAAO,SAAS,GAAG,aAAa,KAAK,YAAY,MAAM;AACzE,SAAK,YAAY,SAAS;AAC1B,KAAC,KAAK,KAAK,OAAO,OAAO,SAAS,GAAG,aAAa,KAAK,YAAY,EAAE;AACrE,SAAK,YAAY,KAAK;AACtB,KAAC,KAAK,KAAK,OAAO,OAAO,SAAS,GAAG,aAAa,KAAK,YAAY,KAAK;AACxE,SAAK,YAAY,QAAQ;AACzB,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ;AACb,QAAI,KAAK,oBAAoB,MAAM;AACjC,WAAK,iBAAiB,qBAAqB,KAAK,SAAS,GAAG,IAAI;AAAA,IAClE;AACA,UAAM,gBAAgB,KAAK,SAAS,EAAE,iBAAiB;AACvD,UAAM,cAAc,KAAK,SAAS,EAAE,wBAAwB;AAC5D,aAAS,IAAI,GAAG,IAAI,eAAe,EAAE,GAAG;AACtC,YAAM,QAAQ,YAAY,CAAC;AAC3B,WAAK,yBAAyB,KAAK,IAAI;AAAA,IACzC;AACA,aAAS,IAAI,GAAG,IAAI,eAAe,EAAE,GAAG;AACtC,YAAM,gBAAgB,KAAK,yBAAyB,CAAC;AACrD,UAAI,CAAC,KAAK,SAAS,EAAE,gCAAgC,aAAa,GAAG;AACnE;AAAA,MACF;AACA,WAAK,gCAAgC,KAAK,oBAAoB,OAAO,KAAK,iBAAiB,8BAA8B,EAAE,aAAa,IAAI,IAAI;AAChJ,WAAK,aAAa,KAAK,SAAS,EAAE,mBAAmB,aAAa,CAAC;AACnE,WAAK,SAAS,KAAK,SAAS,EAAE,0BAA0B,aAAa,GAAG,KAAK,SAAS,EAAE,4BAA4B,aAAa,GAAG,KAAK,SAAS,EAAE,uBAAuB,aAAa,GAAG,KAAK,SAAS,EAAE,yBAAyB,aAAa,GAAG,KAAK,SAAS,EAAE,oBAAoB,aAAa,GAAG,KAAK,SAAS,EAAE,qBAAqB,aAAa,GAAG,KAAK,SAAS,EAAE,mBAAmB,aAAa,GAAG,KAAK,SAAS,EAAE,qBAAqB,aAAa,GAAG,KAAK,SAAS,EAAE,2BAA2B,aAAa,CAAC;AAAA,IAChgB;AAAA,EACF;AAAA,EACA,SAAS,WAAW,YAAY,aAAa,YAAY,aAAa,SAAS,SAAS,gBAAgB,cAAc;AACpH,QAAI,KAAK,UAAU,GAAG;AACpB,WAAK,GAAG,OAAO,KAAK,GAAG,SAAS;AAAA,IAClC,OAAO;AACL,WAAK,GAAG,QAAQ,KAAK,GAAG,SAAS;AAAA,IACnC;AACA,SAAK,GAAG,UAAU,KAAK,GAAG,GAAG;AAC7B,UAAM,iBAAiB,KAAK,cAAc;AAC1C,QAAI,KAAK,gCAAgC,KAAK,MAAM;AAClD,qBAAe,KAAK;AACpB,UAAI,KAAK,qBAAqB,GAAG;AAC/B,uBAAe,KAAK,eAAe;AACnC,uBAAe,KAAK,eAAe;AACnC,uBAAe,KAAK,eAAe;AAAA,MACrC;AAAA,IACF;AACA,QAAI,cAAc;AAClB,QAAI,KAAK,UAAU,SAAS,KAAK,MAAM;AACrC,oBAAc,KAAK,UAAU,SAAS;AAAA,IACxC;AACA,uBAAmB,YAAY,EAAE,mBAAmB,MAAM,aAAa,aAAa,aAAa,YAAY,SAAS,KAAK,aAAa,SAAS,gBAAgB,gBAAgB,KAAK,qBAAqB,GAAG,KAAK,aAAa,GAAG,YAAY;AAC/O,SAAK,GAAG,aAAa,KAAK,GAAG,WAAW,YAAY,KAAK,GAAG,gBAAgB,CAAC;AAC7E,SAAK,GAAG,WAAW,IAAI;AACvB,SAAK,gCAAgC,IAAI;AACzC,SAAK,gCAAgC,IAAI;AAAA,EAC3C;AAAA,EACA,OAAO,kBAAkB;AACvB,uBAAmB,eAAe;AAAA,EACpC;AAAA,EACA,eAAe,KAAK,UAAU;AAC5B,YAAQ;AACR,iBAAa;AAAA,EACf;AAAA,EACA,UAAU;AACR,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY;AACjB,WAAK,cAAc,KAAK,GAAG,aAAa,gCAAgC,KAAK,KAAK,GAAG,aAAa,uCAAuC,KAAK,KAAK,GAAG,aAAa,oCAAoC;AAAA,IACzM;AACA,SAAK,GAAG,QAAQ,KAAK,GAAG,YAAY;AACpC,SAAK,GAAG,QAAQ,KAAK,GAAG,YAAY;AACpC,SAAK,GAAG,QAAQ,KAAK,GAAG,UAAU;AAClC,SAAK,GAAG,UAAU,KAAK,GAAG,EAAE;AAC5B,SAAK,GAAG,OAAO,KAAK,GAAG,KAAK;AAC5B,SAAK,GAAG,UAAU,MAAM,MAAM,MAAM,IAAI;AACxC,SAAK,GAAG,WAAW,KAAK,GAAG,cAAc,IAAI;AAC7C,SAAK,GAAG,WAAW,KAAK,GAAG,sBAAsB,IAAI;AAAA,EACvD;AAAA,EACA,gCAAgC,MAAM;AACpC,SAAK,gCAAgC;AAAA,EACvC;AAAA,EACA,kCAAkC;AAChC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,gCAAgC,MAAM;AACpC,SAAK,gCAAgC;AAAA,EACvC;AAAA,EACA,kCAAkC;AAChC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ,IAAI;AACV,SAAK,KAAK;AACV,SAAK,iBAAiB,MAAM,EAAE;AAC9B,uBAAmB,YAAY,EAAE,MAAM,EAAE;AAAA,EAC3C;AACF;AACA,eAAe,gBAAgB,MAAM;AACnC,uBAAqB,gBAAgB;AACvC;AACA,IAAM,aAAa,IAAI,eAAe;AACtC,IAAM,uBAAN,cAAmC,cAAc;AAAA,EAC/C,YAAY,WAAW,UAAU,SAAS;AACxC,UAAM;AACN,SAAK,UAAU;AACf,SAAK,SAAS,aAAa,OAAO;AAClC,SAAK,WAAW,IAAI,qBAAqB;AACzC,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB,IAAI,OAAO;AACrC,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,gBAAgB,IAAI,qBAAqB,UAAU,OAAO;AAC/D,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,QAAI;AACJ,UAAM,KAAK;AACX,UAAM,KAAK,KAAK,SAAS,sBAAsB,MAAM,OAAO,SAAS,GAAG,UAAU,GAAG;AACnF,WAAK,WAAW,eAAe,OAAO,KAAK,QAAQ;AAAA,IACrD;AACA,SAAK,OAAO,cAAc;AAAA,MACxB,IAAI,oBAAoB,KAAK,eAAe,GAAG,IAAI,QAAQ,GAAG;AAAA,MAC9D,IAAI,oBAAoB,KAAK,eAAe,GAAG,GAAG,QAAQ,GAAG;AAAA,MAC7D,IAAI,oBAAoB,KAAK,eAAe,GAAG,IAAI,QAAQ,GAAG;AAAA,MAC9D,IAAI,oBAAoB,KAAK,mBAAmB,GAAG,GAAG,SAAS,GAAG;AAAA,MAClE,IAAI,oBAAoB,KAAK,eAAe,GAAG,KAAK,QAAQ,GAAG;AAAA,IACjE,CAAC;AACD,SAAK,SAAS,WAAW,KAAK,SAAS;AACvC,SAAK,SAAS,wBAAwB,IAAI;AAAA,EAC5C;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,UAAU,SAAS,EAAE,WAAW,aAAa,KAAK,UAAU,SAAS,EAAE,WAAW,YAAY;AAAA,EAC7G;AAAA,EACA,YAAY;AACV,UAAM,SAAS,CAAC;AAChB,QAAI,KAAK,SAAS,QAAQ;AACxB,iBAAW,OAAO,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG;AACnD,cAAM,YAAY,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAC3D,eAAO,SAAS,IAAI,KAAK,SAAS,OAAO,GAAG;AAAA,MAC9C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,gBAAgB,KAAK,UAAU,SAAS,EAAE,WAAW;AAC1D,SAAK,mBAAmB,MAAM,KAAK,eAAe,KAAK,aAAa,EAAE,UAAU,KAAK,gBAAgB,GAAG,KAAK,iBAAiB,CAAC;AAAA,EACjI;AAAA,EACA,mBAAmB,IAAI,aAAa;AAClC,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,cAAc;AAAA,MAC1B,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,OAAO;AAAA,IACT;AACA,SAAK,SAAS,QAAQ,EAAE;AACxB,SAAK,SAAS,iBAAiB,kBAAkB;AACjD,SAAK,SAAS,iBAAiB,eAAe;AAC9C,uBAAmB,YAAY,EAAE,cAAc,CAAC;AAAA,EAClD;AAAA,EACA,YAAY,OAAO,SAAS;AAC1B,SAAK,SAAS,YAAY,OAAO,OAAO;AAAA,EAC1C;AAAA,EACA,iBAAiB;AACf,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,SAAS,aAAa,OAAO,SAAS,GAAG,IAAI,CAAC,aAAa;AAAA,MACjF,IAAI,QAAQ;AAAA,MACZ,MAAM,QAAQ;AAAA,MACd,OAAO,KAAK,UAAU,iBAAiB,QAAQ,EAAE;AAAA,IACnD,EAAE,MAAM,OAAO,KAAK,CAAC;AAAA,EACvB;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,UAAU,eAAe;AAAA,EACvC;AAAA,EACA,iBAAiB,IAAI;AACnB,WAAO,KAAK,UAAU,iBAAiB,EAAE;AAAA,EAC3C;AAAA,EACA,oBAAoB,WAAW;AAC7B,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,KAAK,UAAU,iBAAiB,SAAS;AACrD,UAAI,cAAc;AAChB,cAAM,IAAI,UAAU,iCAAiC,SAAS;AAAA,IAClE;AACA,UAAM,MAAM,KAAK,UAAU,oBAAoB,SAAS,EAAE,MAAM;AAChE,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACtC,UAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,KAAK,gBAAgB;AAC5D,UAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,KAAK,iBAAiB;AAAA,IACxE;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,WAAW;AACzB,SAAK,cAAc,SAAS,KAAK,kBAAkB,EAAE,QAAQ,KAAK,cAAc,EAAE,QAAQ,SAAS;AAAA,EACrG;AAAA,EACA,OAAO,IAAI,KAAK;AACd,QAAI,IAAI,IAAI,IAAI;AAChB,UAAM,OAAO,IAAI,GAAG;AACpB,UAAM;AACN,WAAO;AACP,UAAM,QAAQ,KAAK;AACnB,SAAK,KAAK,oBAAoB;AAC9B,UAAM,gBAAgB,KAAK,cAAc,OAAO,KAAK,WAAW,GAAG;AACnE,SAAK,KAAK,mBAAmB;AAC7B,UAAM,eAAe;AACrB,KAAC,KAAK,KAAK,cAAc,sBAAsB,OAAO,SAAS,GAAG,OAAO,OAAO,GAAG;AACnF,QAAI,CAAC,eAAe;AAClB,OAAC,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,iBAAiB,OAAO,EAAE;AAAA,IACvE;AACA,SAAK,YAAY;AACjB,SAAK,uBAAuB,KAAK,KAAK,MAAM,GAAG;AAC/C,KAAC,KAAK,KAAK,YAAY,OAAO,SAAS,GAAG,SAAS,OAAO,EAAE;AAC5D,KAAC,KAAK,KAAK,SAAS,OAAO,SAAS,GAAG,iBAAiB,OAAO,EAAE;AACjE,SAAK,KAAK,mBAAmB;AAC7B,UAAM,OAAO;AACb,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,sBAAsB,KAAK,iBAAiB,KAAK,gBAAgB,CAAC;AACjF,SAAK,UAAU,sBAAsB,KAAK,iBAAiB,KAAK,gBAAgB,CAAC;AACjF,SAAK,UAAU,sBAAsB,KAAK,eAAe,KAAK,gBAAgB,IAAI,EAAE;AACpF,SAAK,UAAU,sBAAsB,KAAK,eAAe,KAAK,gBAAgB,IAAI,EAAE;AACpF,SAAK,UAAU,sBAAsB,KAAK,eAAe,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,GAAG;AAC9G,SAAK,UAAU,sBAAsB,KAAK,mBAAmB,KAAK,gBAAgB,IAAI,EAAE;AAAA,EAC1F;AAAA,EACA,uBAAuB,IAAI,KAAK;AAC9B,QAAI;AACJ,KAAC,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG,iBAAiB,KAAK,WAAW,KAAK,GAAG;AAAA,EACpF;AAAA,EACA,KAAK,IAAI;AACP,UAAM,SAAS,KAAK;AACpB,UAAM,QAAQ,WAAW,SAAS;AAClC,UAAM,CAAC,IAAI,OAAO;AAClB,UAAM,CAAC,IAAI,OAAO;AAClB,UAAM,CAAC,IAAI,CAAC,OAAO;AACnB,UAAM,CAAC,IAAI,CAAC,OAAO;AACnB,UAAM,EAAE,IAAI,OAAO;AACnB,UAAM,EAAE,IAAI,OAAO;AACnB,SAAK,SAAS,aAAa,UAAU;AACrC,SAAK,SAAS,eAAe,GAAG,aAAa,GAAG,mBAAmB,GAAG,KAAK,QAAQ;AACnF,SAAK,SAAS,UAAU;AAAA,EAC1B;AAAA,EACA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,SAAS,QAAQ;AACtB,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AACA,IAAI;AACJ,IAAI,iBAAiB;AACrB,SAAS,eAAe;AACtB,MAAI,gBAAgB,UAAU,GAAG;AAC/B,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,oBAAkB,OAAO,iBAAiB,iBAAiB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1F,aAAS,mBAAmB;AAC1B,UAAI;AACF,uBAAe;AACf,gBAAQ;AAAA,MACV,SAAS,GAAG;AACV;AACA,YAAI,iBAAiB,GAAG;AACtB,gBAAM,MAAM,IAAI,MAAM,wCAAwC;AAC9D,cAAI,QAAQ;AACZ,iBAAO,GAAG;AACV;AAAA,QACF;AACA,eAAO,IAAI,WAAW,wCAAwC;AAC9D,mBAAW,kBAAkB,EAAE;AAAA,MACjC;AAAA,IACF;AACA,qBAAiB;AAAA,EACnB,CAAC;AACD,SAAO;AACT;AACA,SAAS,eAAe,SAAS;AAC/B,YAAU,OAAO,OAAO;AAAA,IACtB,aAAa,QAAQ;AAAA,IACrB,cAAc,SAAS;AAAA,EACzB,GAAG,OAAO;AACV,kBAAgB,QAAQ,OAAO;AAC/B,kBAAgB,WAAW;AAC7B;AACA,IAAM,UAAU;AAChB,IAAM,uBAAuB;AAC7B,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,WAAW;AACvB,UAAM,MAAM,IAAI,YAAW;AAC3B,QAAI,OAAO,UAAU,eAAe,UAAU;AAC5C,UAAI,mBAAmB,UAAU;AACjC,UAAI,IAAI,oBAAoB,GAAG;AAC7B,YAAI,mBAAmB;AAAA,MACzB;AAAA,IACF;AACA,UAAM,eAAe,UAAU;AAC/B,UAAM,YAAY,aAAa;AAC/B,aAAS,YAAY,GAAG,YAAY,WAAW,EAAE,WAAW;AAC1D,YAAM,aAAa,aAAa,SAAS;AACzC,YAAM,UAAU,WAAW;AAC3B,UAAI,aAAa;AACjB,eAAS,aAAa,GAAG,aAAa,SAAS,EAAE,YAAY;AAC3D,cAAM,WAAW,WAAW,UAAU;AACtC,cAAM,WAAW,IAAI,SAAS;AAC9B,iBAAS,SAAS,SAAS;AAC3B,cAAM,eAAe,SAAS;AAC9B,YAAI,cAAc;AAChB,gBAAM,YAAY,aAAa;AAC/B,mBAAS,YAAY,GAAG,YAAY,WAAW,EAAE,WAAW;AAC1D,kBAAM,WAAW,IAAI,SAAS;AAC9B,qBAAS,SAAS,aAAa,SAAS;AACxC,qBAAS,KAAK,KAAK,QAAQ;AAAA,UAC7B;AAAA,QACF;AACA,YAAI,YAAY,KAAK,QAAQ;AAC7B,UAAE;AAAA,MACJ;AACA,UAAI,iBAAiB,KAAK,UAAU;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,OAAO,kBAAkB;AACxC,QAAI,SAAS,KAAK,YAAY;AAC5B,WAAK,MAAM,KAAK;AAAA,IAClB;AACA,SAAK,aAAa;AAClB,QAAI,mBAAmB,GAAG;AACxB,yBAAmB;AAAA,IACrB;AACA,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ,KAAK;AACrD,YAAM,iBAAiB,KAAK,iBAAiB,CAAC;AAC9C,WAAK,OAAO,OAAO,kBAAkB,YAAY,cAAc;AAC/D,oBAAc;AAAA,IAChB;AACA,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA,EACA,MAAM,OAAO;AACX,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ,EAAE,GAAG;AACrD,YAAM,aAAa,KAAK,iBAAiB,CAAC;AAC1C,eAAS,IAAI,YAAY,IAAI,aAAa,YAAY,EAAE,GAAG;AACzD,aAAK,YAAY,CAAC,EAAE,WAAW,KAAK;AACpC,cAAM,aAAa,KAAK,YAAY,CAAC,EAAE;AACvC,cAAM,aAAa,KAAK,YAAY,CAAC,EAAE;AACvC,YAAI,aAAa,GAAG;AAClB;AAAA,QACF;AACA,cAAM,sBAAsB,YAAY,KAAK,aAAa,IAAI,CAAC;AAC/D,cAAM,yBAAyB,YAAY,KAAK,aAAa,IAAI,CAAC;AAClE,iBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,CAAC,EAAE,KAAK,QAAQ,EAAE,GAAG;AACxD,eAAK,YAAY,CAAC,EAAE,KAAK,CAAC,EAAE,WAAW,KAAK;AAAA,QAC9C;AAAA,MACF;AACA,oBAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO;AACvB,aAAS,aAAa,GAAG,aAAa,KAAK,YAAY,QAAQ,EAAE,YAAY;AAC3E,YAAM,WAAW,KAAK,YAAY,UAAU;AAC5C,UAAI,SAAS,KAAK,UAAU,GAAG;AAC7B;AAAA,MACF;AACA,YAAM,YAAY,KAAK,YAAY,UAAU,EAAE;AAC/C,YAAM,UAAU,MAAM,sBAAsB,SAAS;AACrD,eAAS,YAAY,GAAG,YAAY,SAAS,KAAK,QAAQ,EAAE,WAAW;AACrE,cAAM,WAAW,SAAS,KAAK,SAAS;AACxC,cAAM,gBAAgB,SAAS;AAC/B,YAAI,gBAAgB,GAAG;AACrB;AAAA,QACF;AACA,cAAM,sBAAsB,eAAe,OAAO;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,kBAAkB,YAAY,gBAAgB;AAC1D,QAAI,mBAAmB;AACvB,QAAI,aAAa;AACjB,UAAM,MAAM;AACZ,UAAM,uBAAuB;AAC7B,aAAS,IAAI,YAAY,IAAI,aAAa,gBAAgB,EAAE,GAAG;AAC7D,YAAM,YAAY,KAAK,YAAY,CAAC,EAAE;AACtC,YAAM,aAAa,KAAK,YAAY,CAAC,EAAE;AACvC,UAAI,MAAM,yBAAyB,UAAU,IAAI,SAAS;AACxD,YAAI,oBAAoB,GAAG;AACzB;AAAA,QACF;AACA,2BAAmB;AACnB,qBAAa,MAAM,sBAAsB,SAAS;AAClD,sBAAc,mBAAmB,KAAK;AACtC,YAAI,aAAa,GAAG;AAClB,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,GAAG;AACxB,yBAAmB;AACnB,mBAAa;AAAA,IACf;AACA,aAAS,IAAI,YAAY,IAAI,aAAa,gBAAgB,EAAE,GAAG;AAC7D,YAAM,aAAa,KAAK,YAAY,CAAC,EAAE;AACvC,UAAI,oBAAoB,GAAG;AACzB,cAAM,sBAAsB,YAAY,UAAU;AAAA,MACpD,OAAO;AACL,YAAI,UAAU,MAAM,sBAAsB,UAAU;AACpD,YAAI;AACJ,YAAI,aAAa,KAAK;AACpB,eAAK,cAAc,MAAM,KAAK,MAAM;AAAA,QACtC,OAAO;AACL,gBAAM,IAAI,cAAc,OAAO,IAAI;AAAA,QACrC;AACA,cAAM,eAAe,IAAI,OAAO,IAAI;AACpC,YAAI,cAAc,sBAAsB;AACtC,eAAK,IAAI,wBAAwB,IAAI;AAAA,QACvC;AACA,YAAI,UAAU,IAAI;AAChB,oBAAU;AAAA,QACZ;AACA,cAAM,sBAAsB,YAAY,OAAO;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,cAAc,CAAC;AACpB,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AACF;AACA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,GAAG;AACb,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,OAAO,CAAC;AACb,QAAI,KAAK,QAAQ;AACf,WAAK,WAAW,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,WAAW,GAAG;AACZ,SAAK,SAAS,EAAE;AAChB,SAAK,OAAO,EAAE,KAAK,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC;AAC7C,WAAO;AAAA,EACT;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,iBAAiB,MAAM,kBAAkB,KAAK,MAAM;AACzD,SAAK,YAAY,MAAM,aAAa,KAAK,MAAM;AAC/C,UAAM,yBAAyB,KAAK,gBAAgB,CAAC;AAAA,EACvD;AAAA,EACA,QAAQ;AACN,UAAM,gBAAgB,IAAI,UAAS;AACnC,kBAAc,SAAS,KAAK;AAC5B,kBAAc,iBAAiB,KAAK;AACpC,kBAAc,YAAY,KAAK;AAC/B,kBAAc,OAAO,KAAK,KAAK,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC;AACzD,WAAO;AAAA,EACT;AACF;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,SAAS;AACP,SAAK,OAAO,OAAO;AACnB,SAAK,OAAO,UAAU,kBAAkB;AAAA,EAC1C;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,UAAU,MAAM;AACvB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,OAAO,WAAW,cAAc,KAAK,OAAO,WAAW;AAAA,EACrE;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,MAAM;AACvB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,OAAO,WAAW,eAAe,KAAK,OAAO,WAAW;AAAA,EACtE;AAAA,EACA,iBAAiB;AACf,UAAM,iBAAiB,KAAK,OAAO,WAAW;AAC9C,UAAM,sBAAsB,KAAK,iBAAiB;AAClD,aAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,UAAI,IAAI,qBAAqB;AAC3B,aAAK,iBAAiB,CAAC,IAAI,KAAK,iBAAiB,CAAC;AAAA,MACpD,OAAO;AACL,aAAK,iBAAiB,KAAK,KAAK,iBAAiB,CAAC,CAAC;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,QAAQ;AACnB,QAAI;AACJ,UAAM,YAAY,KAAK,OAAO,MAAM;AACpC,SAAK,YAAY,GAAG,YAAY,WAAW,EAAE,WAAW;AACtD,UAAI,UAAU,KAAK,SAAS,SAAS,GAAG;AACtC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,UAAU,KAAK,iBAAiB;AAClC,aAAO,KAAK,gBAAgB,MAAM;AAAA,IACpC;AACA,gBAAY,YAAY,KAAK,gBAAgB;AAC7C,SAAK,gBAAgB,MAAM,IAAI;AAC/B,SAAK,uBAAuB,SAAS,IAAI;AACzC,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,WAAO,KAAK,OAAO,MAAM;AAAA,EAC3B;AAAA,EACA,sBAAsB,WAAW,SAAS;AACxC,QAAI,aAAa,KAAK,wBAAwB;AAC5C,WAAK,uBAAuB,SAAS,IAAI;AACzC;AAAA,IACF;AACA,eAAW,KAAK,aAAa,YAAY,KAAK,aAAa,CAAC;AAC5D,SAAK,eAAe,SAAS,IAAI;AAAA,EACnC;AAAA,EACA,mBAAmB,QAAQ,SAAS;AAClC,UAAM,QAAQ,KAAK,aAAa,MAAM;AACtC,QAAI,QAAQ,GAAG;AACb;AAAA,IACF;AACA,SAAK,sBAAsB,OAAO,OAAO;AAAA,EAC3C;AAAA,EACA,sBAAsB,WAAW;AAC/B,QAAI,aAAa,KAAK,wBAAwB;AAC5C,aAAO,KAAK,uBAAuB,SAAS;AAAA,IAC9C;AACA,eAAW,KAAK,aAAa,YAAY,KAAK,aAAa,CAAC;AAC5D,WAAO,KAAK,eAAe,SAAS;AAAA,EACtC;AAAA,EACA,mBAAmB,QAAQ;AACzB,UAAM,QAAQ,KAAK,aAAa,MAAM;AACtC,QAAI,QAAQ,GAAG;AACb,aAAO;AAAA,IACT;AACA,WAAO,KAAK,sBAAsB,KAAK;AAAA,EACzC;AAAA,EACA,kBAAkB,aAAa;AAC7B,QAAI;AACJ,UAAM,UAAU,KAAK,OAAO,WAAW;AACvC,SAAK,iBAAiB,GAAG,iBAAiB,SAAS,EAAE,gBAAgB;AACnE,UAAI,eAAe,KAAK,cAAc,cAAc,GAAG;AACrD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,eAAe,KAAK,sBAAsB;AAC5C,aAAO,KAAK,qBAAqB,WAAW;AAAA,IAC9C;AACA,qBAAiB,KAAK,OAAO,WAAW,QAAQ,OAAO,KAAK,KAAK,oBAAoB,EAAE;AACvF,SAAK,qBAAqB,WAAW,IAAI;AACzC,SAAK,yBAAyB,cAAc,IAAI;AAChD,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,OAAO,WAAW;AAAA,EAChC;AAAA,EACA,yBAAyB,gBAAgB;AACvC,WAAO,KAAK,OAAO,WAAW,cAAc,cAAc;AAAA,EAC5D;AAAA,EACA,yBAAyB,gBAAgB;AACvC,WAAO,KAAK,OAAO,WAAW,cAAc,cAAc;AAAA,EAC5D;AAAA,EACA,yBAAyB,gBAAgB;AACvC,WAAO,KAAK,OAAO,WAAW,cAAc,cAAc;AAAA,EAC5D;AAAA,EACA,yBAAyB,gBAAgB;AACvC,QAAI,kBAAkB,KAAK,0BAA0B;AACnD,aAAO,KAAK,yBAAyB,cAAc;AAAA,IACrD;AACA,eAAW,KAAK,kBAAkB,iBAAiB,KAAK,kBAAkB,CAAC;AAC3E,WAAO,KAAK,iBAAiB,cAAc;AAAA,EAC7C;AAAA,EACA,sBAAsB,aAAa;AACjC,UAAM,iBAAiB,KAAK,kBAAkB,WAAW;AACzD,WAAO,KAAK,yBAAyB,cAAc;AAAA,EACrD;AAAA,EACA,yBAAyB,gBAAgB,OAAO,SAAS,GAAG;AAC1D,QAAI,kBAAkB,KAAK,0BAA0B;AACnD,WAAK,yBAAyB,cAAc,IAAI,UAAU,IAAI,QAAQ,KAAK,yBAAyB,cAAc,KAAK,IAAI,UAAU,QAAQ;AAC7I;AAAA,IACF;AACA,eAAW,KAAK,kBAAkB,iBAAiB,KAAK,kBAAkB,CAAC;AAC3E,QAAI,KAAK,OAAO,WAAW,cAAc,cAAc,IAAI,OAAO;AAChE,cAAQ,KAAK,OAAO,WAAW,cAAc,cAAc;AAAA,IAC7D;AACA,QAAI,KAAK,OAAO,WAAW,cAAc,cAAc,IAAI,OAAO;AAChE,cAAQ,KAAK,OAAO,WAAW,cAAc,cAAc;AAAA,IAC7D;AACA,SAAK,iBAAiB,cAAc,IAAI,UAAU,IAAI,QAAQ,KAAK,iBAAiB,cAAc,IAAI,KAAK,iBAAiB,cAAc,KAAK,IAAI,UAAU,QAAQ;AAAA,EACvK;AAAA,EACA,sBAAsB,aAAa,OAAO,SAAS,GAAG;AACpD,UAAM,QAAQ,KAAK,kBAAkB,WAAW;AAChD,SAAK,yBAAyB,OAAO,OAAO,MAAM;AAAA,EACpD;AAAA,EACA,yBAAyB,gBAAgB,OAAO,SAAS,GAAG;AAC1D,SAAK,yBAAyB,gBAAgB,KAAK,yBAAyB,cAAc,IAAI,QAAQ,MAAM;AAAA,EAC9G;AAAA,EACA,sBAAsB,aAAa,OAAO,SAAS,GAAG;AACpD,UAAM,QAAQ,KAAK,kBAAkB,WAAW;AAChD,SAAK,yBAAyB,OAAO,OAAO,MAAM;AAAA,EACpD;AAAA,EACA,2BAA2B,aAAa,OAAO,SAAS,GAAG;AACzD,UAAM,QAAQ,KAAK,kBAAkB,WAAW;AAChD,SAAK,8BAA8B,OAAO,OAAO,MAAM;AAAA,EACzD;AAAA,EACA,8BAA8B,gBAAgB,OAAO,SAAS,GAAG;AAC/D,SAAK,yBAAyB,gBAAgB,KAAK,yBAAyB,cAAc,KAAK,KAAK,QAAQ,KAAK,OAAO;AAAA,EAC1H;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,aAAa,MAAM;AAAA,EACjC;AAAA,EACA,iBAAiB,YAAY;AAC3B,UAAM,gBAAgB,KAAK,OAAO,UAAU;AAC5C,aAAS,gBAAgB,GAAG,gBAAgB,eAAe,EAAE,eAAe;AAC1E,UAAI,KAAK,aAAa,aAAa,KAAK,YAAY;AAClD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,OAAO,UAAU,IAAI,aAAa;AAAA,EAChD;AAAA,EACA,0BAA0B;AACxB,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B;AAAA,EACA,0BAA0B,eAAe;AACvC,WAAO,KAAK,OAAO,UAAU,eAAe,aAAa;AAAA,EAC3D;AAAA,EACA,+CAA+C,eAAe;AAC5D,UAAM,eAAe,KAAK,OAAO,UAAU;AAC3C,WAAO,iBAAiB,MAAM,+BAA+B,aAAa,aAAa,CAAC;AAAA,EAC1F;AAAA,EACA,4BAA4B,eAAe;AACzC,WAAO,KAAK,OAAO,UAAU,YAAY,aAAa;AAAA,EACxD;AAAA,EACA,uBAAuB,eAAe;AACpC,WAAO,KAAK,OAAO,UAAU,aAAa,aAAa;AAAA,EACzD;AAAA,EACA,oBAAoB,eAAe;AACjC,WAAO,KAAK,2BAA2B,aAAa;AAAA,EACtD;AAAA,EACA,yBAAyB,eAAe;AACtC,WAAO,KAAK,OAAO,UAAU,QAAQ,aAAa;AAAA,EACpD;AAAA,EACA,2BAA2B,eAAe;AACxC,WAAO,KAAK,OAAO,UAAU,gBAAgB,aAAa;AAAA,EAC5D;AAAA,EACA,qBAAqB,eAAe;AAClC,WAAO,KAAK,OAAO,UAAU,UAAU,aAAa;AAAA,EACtD;AAAA,EACA,mBAAmB,eAAe;AAChC,WAAO,KAAK,OAAO,UAAU,UAAU,aAAa;AAAA,EACtD;AAAA,EACA,mBAAmB,eAAe;AAChC,UAAM,gBAAgB,KAAK,OAAO,UAAU;AAC5C,WAAO,CAAC,iBAAiB,MAAM,oBAAoB,cAAc,aAAa,CAAC;AAAA,EACjF;AAAA,EACA,qBAAqB,eAAe;AAClC,UAAM,gBAAgB,KAAK,OAAO,UAAU;AAC5C,WAAO,iBAAiB,MAAM,oBAAoB,cAAc,aAAa,CAAC,IAAI,gBAAgB,2BAA2B,iBAAiB,MAAM,0BAA0B,cAAc,aAAa,CAAC,IAAI,gBAAgB,iCAAiC,gBAAgB;AAAA,EACjR;AAAA,EACA,2BAA2B,eAAe;AACxC,UAAM,gBAAgB,KAAK,OAAO,UAAU;AAC5C,WAAO,iBAAiB,MAAM,qBAAqB,cAAc,aAAa,CAAC;AAAA,EACjF;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B;AAAA,EACA,iBAAiB;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,UAAU,OAAO,EAAE,GAAG;AACpD,UAAI,KAAK,OAAO,UAAU,WAAW,CAAC,KAAK,GAAG;AAC5C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,gCAAgC,eAAe;AAC7C,UAAM,eAAe,KAAK,OAAO,UAAU;AAC3C,WAAO,iBAAiB,MAAM,gBAAgB,aAAa,aAAa,CAAC;AAAA,EAC3E;AAAA,EACA,0CAA0C,eAAe;AACvD,UAAM,eAAe,KAAK,OAAO,UAAU;AAC3C,WAAO,iBAAiB,MAAM,0BAA0B,aAAa,aAAa,CAAC;AAAA,EACrF;AAAA,EACA,uCAAuC,eAAe;AACpD,UAAM,eAAe,KAAK,OAAO,UAAU;AAC3C,WAAO,iBAAiB,MAAM,uBAAuB,aAAa,aAAa,CAAC;AAAA,EAClF;AAAA,EACA,2CAA2C,eAAe;AACxD,UAAM,eAAe,KAAK,OAAO,UAAU;AAC3C,WAAO,iBAAiB,MAAM,2BAA2B,aAAa,aAAa,CAAC;AAAA,EACtF;AAAA,EACA,iBAAiB;AACf,QAAI,iBAAiB,KAAK,OAAO,WAAW;AAC5C,UAAM,sBAAsB,KAAK,iBAAiB;AAClD,QAAI,iBAAiB,qBAAqB;AACxC,uBAAiB;AAAA,IACnB;AACA,aAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,WAAK,iBAAiB,CAAC,IAAI,KAAK,iBAAiB,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,mBAAmB,KAAK,OAAO,WAAW;AAC/C,SAAK,iBAAiB,KAAK,OAAO,MAAM;AACxC,SAAK,0BAA0B,KAAK,OAAO,WAAW;AACtD,SAAK,0BAA0B,KAAK,OAAO,WAAW;AACtD;AACE,YAAM,eAAe,KAAK,OAAO,WAAW;AAC5C,YAAM,iBAAiB,KAAK,OAAO,WAAW;AAC9C,eAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,aAAK,cAAc,KAAK,aAAa,CAAC,CAAC;AAAA,MACzC;AAAA,IACF;AACA;AACE,YAAM,UAAU,KAAK,OAAO,MAAM;AAClC,YAAM,YAAY,KAAK,OAAO,MAAM;AACpC,eAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,aAAK,SAAS,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC/B;AAAA,IACF;AACA;AACE,YAAM,cAAc,KAAK,OAAO,UAAU;AAC1C,YAAM,gBAAgB,KAAK,OAAO,UAAU;AAC5C,eAAS,IAAI,GAAG,IAAI,eAAe,EAAE,GAAG;AACtC,aAAK,aAAa,KAAK,YAAY,CAAC,CAAC;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,SAAS;AACd,SAAK,mBAAmB,CAAC;AACzB,SAAK,gBAAgB,CAAC;AACtB,SAAK,eAAe,CAAC;AACrB,SAAK,WAAW,CAAC;AACjB,SAAK,kBAAkB,CAAC;AACxB,SAAK,uBAAuB,CAAC;AAC7B,SAAK,2BAA2B,CAAC;AACjC,SAAK,yBAAyB,CAAC;AAC/B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,UAAU;AACR,SAAK,OAAO,QAAQ;AACpB,SAAK,SAAS;AAAA,EAChB;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,OAAO,OAAO,UAAU;AACtB,UAAM,MAAM,iBAAiB,IAAI,gBAAgB,QAAQ;AACzD,QAAI,KAAK;AACP,aAAO,IAAI,WAAU,GAAG;AAAA,IAC1B;AACA,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,QAAI;AACJ,UAAM,QAAQ,iBAAiB,MAAM,QAAQ,KAAK,IAAI;AACtD,QAAI,OAAO;AACT,oBAAc,IAAI,YAAY,KAAK;AACnC,QAAE,KAAK;AACP,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,SAAS,MAAM;AACjB,QAAE,KAAK;AAAA,IACT;AAAA,EACF;AAAA,EACA,YAAY,KAAK;AACf,SAAK,OAAO;AACZ,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,UAAU;AACR,SAAK,KAAK,SAAS;AACnB,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,2BAA2C,CAAC,6BAA6B;AAC3E,2BAAyB,yBAAyB,mCAAmC,IAAI,CAAC,IAAI;AAC9F,SAAO;AACT,GAAG,2BAA2B,CAAC,CAAC;AAChC,IAAI,uBAAuC,CAAC,yBAAyB;AACnE,uBAAqB,qBAAqB,uBAAuB,IAAI,CAAC,IAAI;AAC1E,uBAAqB,qBAAqB,uBAAuB,IAAI,CAAC,IAAI;AAC1E,uBAAqB,qBAAqB,2BAA2B,IAAI,CAAC,IAAI;AAC9E,SAAO;AACT,GAAG,uBAAuB,CAAC,CAAC;AAC5B,IAAM,wBAAN,MAA4B;AAAA,EAC1B,cAAc;AACZ,SAAK,kBAAkB,IAAI,cAAc,GAAG,CAAC;AAC7C,SAAK,WAAW,IAAI,cAAc,GAAG,CAAC;AACtC,SAAK,eAAe,IAAI,cAAc,GAAG,CAAC;AAC1C,SAAK,cAAc,IAAI,cAAc,GAAG,CAAC;AACzC,SAAK,QAAQ,IAAI,cAAc,GAAG,CAAC;AACnC,SAAK,WAAW,IAAI,cAAc,GAAG,CAAC;AAAA,EACxC;AACF;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,wBAAwB,CAAC;AAC9B,SAAK,qBAAqB,CAAC;AAAA,EAC7B;AACF;AACA,IAAM,qBAAN,MAAyB;AAAA,EACvB,cAAc;AACZ,SAAK,SAAS,CAAC;AAAA,EACjB;AACF;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,cAAc,CAAC;AACpB,SAAK,mBAAmB,IAAI,cAAc,GAAG,CAAC;AAAA,EAChD;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,cAAc;AACZ,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS,CAAC;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,IAAI,cAAc,GAAG,CAAC;AACrC,SAAK,OAAO,IAAI,cAAc,GAAG,CAAC;AAAA,EACpC;AACF;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,MAAM;AAChB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,aAAa;AACX,UAAM,MAAM,IAAI,cAAc,GAAG,CAAC;AAClC,QAAI,IAAI,KAAK,MAAM,KAAK,gBAAgB,QAAQ;AAChD,QAAI,IAAI,KAAK,MAAM,KAAK,gBAAgB,QAAQ;AAChD,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,UAAM,MAAM,IAAI,cAAc,GAAG,CAAC;AAClC,QAAI,IAAI,KAAK,MAAM,KAAK,gBAAgB,KAAK;AAC7C,QAAI,IAAI,KAAK,MAAM,KAAK,gBAAgB,KAAK;AAC7C,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EACA,qCAAqC,qBAAqB;AACxD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,cAAc,SAAS;AAAA,EAChF;AAAA,EACA,qCAAqC,qBAAqB;AACxD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,cAAc,SAAS;AAAA,EAChF;AAAA,EACA,qCAAqC,qBAAqB;AACxD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,cAAc,SAAS;AAAA,EAChF;AAAA,EACA,kCAAkC,qBAAqB;AACrD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,cAAc,MAAM;AAAA,EAC7E;AAAA,EACA,kCAAkC,qBAAqB;AACrD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,cAAc,MAAM;AAAA,EAC7E;AAAA,EACA,kCAAkC,qBAAqB;AACrD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,cAAc,MAAM;AAAA,EAC7E;AAAA,EACA,cAAc,qBAAqB;AACjC,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,MAAM;AAAA,EAC/D;AAAA,EACA,eAAe,qBAAqB,YAAY;AAC9C,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,MAAM,UAAU,EAAE;AAAA,EAC3E;AAAA,EACA,gBAAgB,qBAAqB,YAAY;AAC/C,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,MAAM,UAAU,EAAE;AAAA,EAC3E;AAAA,EACA,aAAa,qBAAqB,YAAY;AAC5C,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,MAAM,UAAU,EAAE;AAAA,EAC3E;AAAA,EACA,iBAAiB,qBAAqB,YAAY;AAChD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,MAAM,UAAU,EAAE,OAAO;AAAA,EAClF;AAAA,EACA,eAAe,qBAAqB;AAClC,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,OAAO;AAAA,EAChE;AAAA,EACA,qBAAqB,qBAAqB,aAAa;AACrD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,EAAE;AAAA,EAC7E;AAAA,EACA,oBAAoB,qBAAqB,aAAa;AACpD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,EAAE;AAAA,EAC7E;AAAA,EACA,gBAAgB,qBAAqB,aAAa;AAChD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,EAAE;AAAA,EAC7E;AAAA,EACA,uBAAuB,qBAAqB,aAAa;AACvD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,EAAE,YAAY;AAAA,EACzF;AAAA,EACA,cAAc,qBAAqB,aAAa;AAC9C,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,EAAE;AAAA,EAC7E;AAAA,EACA,iBAAiB,qBAAqB,aAAa;AACjD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,EAAE;AAAA,EAC7E;AAAA,EACA,iBAAiB,qBAAqB;AACpC,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,SAAS;AAAA,EAClE;AAAA,EACA,oBAAoB,qBAAqB,aAAa;AACpD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,SAAS,WAAW,EAAE;AAAA,EAC/E;AAAA,EACA,iBAAiB,qBAAqB,aAAa;AACjD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,SAAS,WAAW,EAAE;AAAA,EAC/E;AAAA,EACA,wBAAwB,qBAAqB,aAAa;AACxD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,SAAS,WAAW,EAAE;AAAA,EAC/E;AAAA,EACA,kBAAkB,qBAAqB,aAAa;AAClD,WAAO,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,SAAS,WAAW,EAAE;AAAA,EAC/E;AAAA,EACA,oBAAoB,qBAAqB,aAAa;AACpD,UAAM,MAAM,IAAI,cAAc,GAAG,CAAC;AAClC,QAAI,IAAI,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,SAAS,WAAW,EAAE,SAAS;AACvF,QAAI,IAAI,KAAK,MAAM,gBAAgB,mBAAmB,EAAE,SAAS,WAAW,EAAE,SAAS;AACvF,WAAO;AAAA,EACT;AACF;AACA,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,sBAAsB;AAC5B,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,MAAM;AAClB,UAAM,MAAM,IAAI,eAAc;AAC9B,QAAI,MAAM,IAAI;AACd,QAAI,YAAY,QAAQ,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO,kBAAkB;AAChC,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,mBAAmB,IAAI,cAAc;AAC3C,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,qBAAiB,MAAM,SAAS,EAAE,WAAW;AAC7C,4BAAwB,MAAM,SAAS,EAAE,WAAW;AACpD,4BAAwB,MAAM,SAAS,EAAE,WAAW;AACpD,4BAAwB,MAAM,SAAS,EAAE,WAAW;AACpD,aAAS,eAAe,GAAG,eAAe,KAAK,YAAY,aAAa,EAAE,cAAc;AACtF,mBAAa,EAAE,OAAO,EAAE;AACxB,uBAAiB,IAAI;AACrB,uBAAiB,IAAI;AACrB,uBAAiB,KAAK,YAAY,SAAS,YAAY;AACvD,qBAAe,KAAK,YAAY,OAAO,MAAM,eAAe,cAAc;AAC1E,sBAAgB,KAAK,YAAY,QAAQ,MAAM,eAAe,eAAe;AAC7E,yBAAmB,KAAK,YAAY,UAAU,MAAM,eAAe,iBAAiB;AACpF,eAAS,IAAI,GAAG,IAAI,eAAe,YAAY,EAAE,GAAG;AAClD,iBAAS,aAAa,CAAC,EAAE,SAAS;AAClC,YAAI,aAAa,CAAC,EAAE,wBAAwB,IAAI;AAC9C,uBAAa,CAAC,EAAE,uBAAuB,MAAM,kBAAkB,aAAa,CAAC,EAAE,OAAO,EAAE;AAAA,QAC1F;AACA,qBAAa,CAAC,EAAE,4BAA4B,kBAAkB,YAAY,eAAe,aAAa,CAAC,EAAE,oBAAoB,GAAG,sBAAsB,aAAa,CAAC,EAAE,oBAAoB,GAAG,sBAAsB,aAAa,CAAC,EAAE,oBAAoB,GAAG,sBAAsB,aAAa,CAAC,EAAE,oBAAoB,GAAG,eAAe,uBAAuB,eAAe,oBAAoB,aAAa,CAAC,EAAE,SAAS,MAAM;AAAA,MACja;AACA,iBAAW,WAAW,gBAAgB,CAAC,WAAW,KAAK;AACvD,uBAAiB,IAAI,iBAAiB,IAAI,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,WAAW,IAAI,QAAQ;AACjH,uBAAiB,IAAI,iBAAiB,IAAI,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,WAAW,IAAI,QAAQ;AACjH,sBAAgB,kBAAkB,eAAe,eAAe,kBAAkB,WAAW,OAAO,KAAK,SAAS,MAAM,oBAAoB,eAAe,sBAAsB,SAAS,kBAAkB,aAAa;AACzN,eAAS,IAAI,GAAG,IAAI,eAAe,aAAa,EAAE,GAAG;AACnD,cAAM,gBAAgB,cAAc,CAAC,EAAE;AACvC,YAAI,gBAAgB,KAAK,iBAAiB,eAAe,eAAe;AACtE;AAAA,QACF;AACA,YAAI,cAAc,CAAC,EAAE,6BAA6B,IAAI;AACpD,wBAAc,CAAC,EAAE,4BAA4B,MAAM,kBAAkB,cAAc,CAAC,EAAE,YAAY,EAAE;AAAA,QACtG;AACA,cAAM,cAAc,IAAI,cAAc;AACtC,oBAAY,IAAI,iBAAiB,aAAa,EAAE,SAAS,IAAI,iBAAiB,gBAAgB,CAAC,EAAE,SAAS;AAC1G,oBAAY,IAAI,iBAAiB,aAAa,EAAE,SAAS,IAAI,iBAAiB,gBAAgB,CAAC,EAAE,SAAS;AAC1G,sBAAc,cAAc,CAAC,EAAE,SAAS,aAAa,kBAAkB,eAAe,cAAc,CAAC,EAAE,SAAS,KAAK,SAAS,OAAO;AACrI,cAAM,4BAA4B,cAAc,CAAC,EAAE;AACnD,cAAM,oBAAoB,CAAC,aAAa,UAAU,SAAS,cAAc,aAAa,YAAY,KAAK,MAAM,KAAK,UAAU,eAAe,SAAS,yBAAyB,CAAC,CAAC,IAAI,eAAe,MAAM,yBAAyB;AACjO,mCAA2B,mBAAmB,sBAAsB,yBAAyB,GAAG,sBAAsB,yBAAyB,GAAG,aAAa,cAAc,CAAC,CAAC;AAC/K,iBAAS,SAAS,2BAA2B,gBAAgB,GAAG,SAAS,eAAe,QAAQ,UAAU,iBAAiB;AACzH,yBAAe,MAAM,IAAI,kBAAkB,aAAa;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,SAAS,QAAQ,IAAI;AAC1B,SAAK,SAAS,QAAQ,IAAI;AAC1B,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,UAAU;AACR,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,MAAM,aAAa;AACjB,SAAK,cAAc,IAAI,iBAAiB;AACxC,QAAI,OAAO,IAAI,kBAAkB,WAAW;AAC5C,SAAK,YAAY,UAAU,KAAK,WAAW;AAC3C,SAAK,YAAY,OAAO,KAAK,QAAQ;AACrC,SAAK,YAAY,cAAc,KAAK,eAAe;AACnD,QAAI,aAAa,GAAG,cAAc,GAAG,gBAAgB;AACrD,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,aAAa,EAAE,GAAG;AACrD,YAAM,UAAU,IAAI,oBAAoB;AACxC,cAAQ,sBAAsB,UAAU,KAAK,qCAAqC,CAAC;AACnF,cAAQ,sBAAsB,UAAU,KAAK,qCAAqC,CAAC;AACnF,cAAQ,sBAAsB,UAAU,KAAK,qCAAqC,CAAC;AACnF,cAAQ,mBAAmB,UAAU,KAAK,kCAAkC,CAAC;AAC7E,cAAQ,mBAAmB,UAAU,KAAK,kCAAkC,CAAC;AAC7E,cAAQ,mBAAmB,UAAU,KAAK,kCAAkC,CAAC;AAC7E,cAAQ,aAAa,KAAK,cAAc,CAAC;AACzC,cAAQ,iBAAiB;AACzB,oBAAc,QAAQ;AACtB,eAAS,IAAI,GAAG,IAAI,QAAQ,YAAY,EAAE,GAAG;AAC3C,cAAM,QAAQ,IAAI,mBAAmB;AACrC,cAAM,uBAAuB;AAC7B,cAAM,SAAS,KAAK,eAAe,GAAG,CAAC;AACvC,cAAM,UAAU,KAAK,gBAAgB,GAAG,CAAC;AACzC,gBAAQ,KAAK,aAAa,GAAG,CAAC,GAAG;AAAA,UAC/B,KAAK;AACH,kBAAM,OAAO,oBAAoB;AACjC,kBAAM,8BAA8B;AACpC;AAAA,UACF,KAAK;AACH,kBAAM,OAAO,oBAAoB;AACjC,kBAAM,8BAA8B;AACpC;AAAA,UACF,KAAK;AACH,kBAAM,OAAO,oBAAoB;AACjC,kBAAM,8BAA8B;AACpC;AAAA,QACJ;AACA,cAAM,OAAO,aAAa,wBAAwB;AAClD,cAAM,OAAO,KAAK,KAAK,iBAAiB,GAAG,CAAC;AAC5C,aAAK,YAAY,OAAO,KAAK,KAAK;AAAA,MACpC;AACA,cAAQ,cAAc,KAAK,eAAe,CAAC;AAC3C,cAAQ,kBAAkB;AAC1B,qBAAe,QAAQ;AACvB,eAAS,IAAI,GAAG,IAAI,QAAQ,aAAa,EAAE,GAAG;AAC5C,cAAM,SAAS,IAAI,oBAAoB;AACvC,eAAO,4BAA4B;AACnC,eAAO,cAAc,KAAK,qBAAqB,GAAG,CAAC;AACnD,eAAO,aAAa,KAAK,oBAAoB,GAAG,CAAC;AACjD,eAAO,SAAS,KAAK,gBAAgB,GAAG,CAAC;AACzC,eAAO,YAAY,aAAa,wBAAwB;AACxD,eAAO,YAAY,KAAK,KAAK,uBAAuB,GAAG,CAAC;AACxD,gBAAQ,KAAK,cAAc,GAAG,CAAC,GAAG;AAAA,UAChC,KAAK;AACH,mBAAO,OAAO,oBAAoB;AAClC,mBAAO,WAAW;AAClB,mBAAO,WAAW;AAClB;AAAA,UACF,KAAK;AACH,mBAAO,OAAO,oBAAoB;AAClC,mBAAO,WAAW;AAClB,mBAAO,WAAW;AAClB;AAAA,UACF,KAAK;AACH,mBAAO,OAAO,oBAAoB;AAClC,mBAAO,WAAW;AAClB,mBAAO,WAAW;AAClB;AAAA,QACJ;AACA,eAAO,UAAU,KAAK,iBAAiB,GAAG,CAAC;AAC3C,aAAK,YAAY,QAAQ,KAAK,MAAM;AAAA,MACtC;AACA,cAAQ,gBAAgB,KAAK,iBAAiB,CAAC;AAC/C,cAAQ,oBAAoB;AAC5B,uBAAiB,QAAQ;AACzB,eAAS,IAAI,GAAG,IAAI,QAAQ,eAAe,EAAE,GAAG;AAC9C,cAAM,WAAW,IAAI,sBAAsB;AAC3C,iBAAS,WAAW,KAAK,oBAAoB,GAAG,CAAC;AACjD,iBAAS,QAAQ,KAAK,iBAAiB,GAAG,CAAC;AAC3C,iBAAS,eAAe,KAAK,wBAAwB,GAAG,CAAC;AACzD,iBAAS,SAAS,KAAK,kBAAkB,GAAG,CAAC;AAC7C,iBAAS,WAAW,KAAK,oBAAoB,GAAG,CAAC;AACjD,aAAK,YAAY,UAAU,KAAK,QAAQ;AAAA,MAC1C;AACA,WAAK,YAAY,SAAS,KAAK,OAAO;AAAA,IACxC;AACA,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,aAAa;AACX,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,aAAS,eAAe,GAAG,eAAe,KAAK,YAAY,aAAa,EAAE,cAAc;AACtF,uBAAiB,KAAK,YAAY,SAAS,YAAY;AACvD,eAAS,KAAK,YAAY,UAAU,MAAM,eAAe,iBAAiB;AAC1E,aAAO,CAAC,EAAE,kBAAkB,IAAI,cAAc,GAAG,CAAC;AAClD,aAAO,CAAC,EAAE,eAAe,IAAI,cAAc,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,EAAE,gBAAgB,CAAC;AACnG,aAAO,CAAC,EAAE,cAAc,IAAI,cAAc,GAAG,EAAE;AAC/C,aAAO,CAAC,EAAE,YAAY,KAAK;AAC3B,aAAO,CAAC,EAAE,WAAW,IAAI,cAAc,GAAG,CAAC;AAC3C,aAAO,CAAC,EAAE,QAAQ,IAAI,cAAc,GAAG,CAAC;AACxC,eAAS,IAAI,GAAG,IAAI,eAAe,eAAe,EAAE,GAAG;AACrD,iBAAS,IAAI,cAAc,GAAG,CAAC;AAC/B,eAAO,IAAI,OAAO,CAAC,EAAE;AACrB,eAAO,CAAC,EAAE,kBAAkB,IAAI,cAAc,OAAO,IAAI,CAAC,EAAE,gBAAgB,IAAI,OAAO,GAAG,OAAO,IAAI,CAAC,EAAE,gBAAgB,IAAI,OAAO,CAAC;AACpI,eAAO,CAAC,EAAE,WAAW,IAAI,cAAc,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,EAAE,gBAAgB,CAAC;AAC/F,eAAO,CAAC,EAAE,eAAe,IAAI,cAAc,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,EAAE,gBAAgB,CAAC;AACnG,eAAO,CAAC,EAAE,cAAc,IAAI,cAAc,GAAG,EAAE;AAC/C,eAAO,CAAC,EAAE,YAAY,KAAK;AAC3B,eAAO,CAAC,EAAE,WAAW,IAAI,cAAc,GAAG,CAAC;AAC3C,eAAO,CAAC,EAAE,QAAQ,IAAI,cAAc,GAAG,CAAC;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,cAAc;AACZ,SAAK,UAAU,IAAI,cAAc,GAAG,CAAC;AACrC,SAAK,OAAO,IAAI,cAAc,GAAG,CAAC;AAAA,EACpC;AACF;AACA,SAAS,iDAAiD,mBAAmB,aAAa,OAAO,uBAAuB,uBAAuB,uBAAuB,uBAAuB,oBAAoB,YAAY,QAAQ;AACnO,oBAAkB,KAAK,wBAAwB,OAAO,uBAAuB,uBAAuB,uBAAuB,sBAAsB,SAAS,sBAAsB,SAAS,sBAAsB,SAAS,UAAU,IAAI;AACxO;AACA,SAAS,gDAAgD,mBAAmB,aAAa,OAAO,uBAAuB,uBAAuB,uBAAuB,uBAAuB,oBAAoB,YAAY,QAAQ;AAClO,oBAAkB,KAAK,wBAAwB,OAAO,uBAAuB,uBAAuB,uBAAuB,sBAAsB,SAAS,sBAAsB,SAAS,sBAAsB,SAAS,UAAU,IAAI;AACxO;AACA,SAAS,0CAA0C,mBAAmB,aAAa,OAAO,uBAAuB,uBAAuB,uBAAuB,wBAAwB,oBAAoB,YAAY,QAAQ;AAC7N,cAAY,SAAS,wBAAwB,OAAO,uBAAuB,uBAAuB,uBAAuB,mBAAmB,SAAS,mBAAmB,SAAS,mBAAmB,SAAS,UAAU,IAAI;AAC7N;AACA,SAAS,sBAAsB,aAAa,WAAW,eAAe,YAAY,eAAe;AAC/F,MAAI,cAAc,YAAY;AAC9B,MAAI,YAAY;AACd,mBAAe;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,aAAa,WAAW,eAAe,YAAY,eAAe;AAC/F,MAAI,cAAc,YAAY;AAC9B,MAAI,YAAY;AACd,mBAAe;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,eAAe,aAAa,WAAW,eAAe,YAAY,eAAe;AACxF,MAAI;AACJ,MAAI,iBAAiB,GAAG;AACtB,oBAAgB,UAAU,gBAAgB,CAAC,EAAE,SAAS,UAAU,UAAU,gBAAgB,CAAC,EAAE,QAAQ;AAAA,EACvG,OAAO;AACL,oBAAgB,cAAc,iBAAiB,EAAE;AAAA,EACnD;AACA,gBAAc,WAAW,kBAAkB,eAAe,WAAW;AACrE,MAAI,YAAY;AACd,mBAAe;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAK,KAAK;AAC/B,SAAO,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC;AACzD;AACA,SAAS,gBAAgB,KAAK,KAAK;AACjC,QAAM,WAAW,KAAK,IAAI,KAAK,GAAG;AAClC,SAAO,WAAW,cAAc,KAAK,GAAG,IAAI;AAC9C;AACA,SAAS,2BAA2B,kBAAkB,YAAY;AAChE,SAAO,iBAAiB;AAC1B;AACA,SAAS,2BAA2B,kBAAkB,YAAY;AAChE,SAAO,iBAAiB;AAC1B;AACA,SAAS,oBAAoB,kBAAkB,YAAY;AACzD,SAAO;AACT;AACA,SAAS,gBAAgB,QAAQ,aAAa,kBAAkB,YAAY,eAAe,gBAAgB,kBAAkB,eAAe;AAC1I,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY,IAAI,cAAc,GAAG,CAAC;AACtC,MAAI,WAAW,IAAI,cAAc,GAAG,CAAC;AACrC,MAAI,QAAQ,IAAI,cAAc,GAAG,CAAC;AAClC,MAAI,eAAe,IAAI,cAAc,GAAG,CAAC;AACzC,SAAO,CAAC,EAAE,WAAW,IAAI,cAAc,iBAAiB,GAAG,iBAAiB,CAAC;AAC7E,gBAAc,WAAW,gBAAgB,UAAU;AACnD,mBAAiB,WAAW,kBAAkB,WAAW;AACzD,iBAAe,UAAU;AACzB,WAAS,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACpC,WAAO,CAAC,EAAE,QAAQ,eAAe,iBAAiB,OAAO,CAAC,EAAE,YAAY,EAAE,IAAI,aAAa;AAC3F,WAAO,CAAC,EAAE,eAAe,IAAI,cAAc,OAAO,CAAC,EAAE,SAAS,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC;AACrF,YAAQ,OAAO,CAAC,EAAE,QAAQ,mBAAmB;AAC7C,gBAAY,OAAO,CAAC,EAAE,SAAS,UAAU,OAAO,IAAI,CAAC,EAAE,QAAQ;AAC/D,aAAS,WAAW,kBAAkB,OAAO,CAAC,EAAE,aAAa,cAAc,IAAI;AAC/E,cAAU,IAAI,WAAW,IAAI,MAAM,IAAI,UAAU,IAAI,UAAU,IAAI,WAAW,IAAI,MAAM;AACxF,cAAU,IAAI,WAAW,IAAI,MAAM,IAAI,UAAU,IAAI,UAAU,IAAI,WAAW,IAAI,MAAM;AACxF,WAAO,CAAC,EAAE,WAAW,OAAO,IAAI,CAAC,EAAE,SAAS,IAAI,SAAS;AACzD,eAAW,OAAO,CAAC,EAAE,SAAS,iBAAiB,KAAK;AACpD,YAAQ,OAAO,CAAC,EAAE,MAAM,iBAAiB,KAAK,EAAE,iBAAiB,KAAK;AACtE,WAAO,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,SAAS,IAAI,QAAQ,EAAE,IAAI,KAAK;AAC/D,mBAAe,OAAO,CAAC,EAAE,SAAS,UAAU,OAAO,IAAI,CAAC,EAAE,QAAQ;AAClE,iBAAa,UAAU;AACvB,WAAO,CAAC,EAAE,WAAW,OAAO,IAAI,CAAC,EAAE,SAAS,IAAI,aAAa,iBAAiB,OAAO,CAAC,EAAE,MAAM,CAAC;AAC/F,QAAI,WAAW,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC,IAAI,gBAAgB;AACzD,aAAO,CAAC,EAAE,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,SAAS,GAAG;AACd,aAAO,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,SAAS,UAAU,OAAO,CAAC,EAAE,YAAY;AACxE,aAAO,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,SAAS,iBAAiB,KAAK;AAC9D,aAAO,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,SAAS,iBAAiB,OAAO,CAAC,EAAE,QAAQ;AAAA,IAC7E;AACA,WAAO,CAAC,EAAE,QAAQ,IAAI,cAAc,GAAG,CAAC;AACxC,WAAO,CAAC,EAAE,cAAc,IAAI,cAAc,eAAe,GAAG,eAAe,CAAC;AAAA,EAC9E;AACF;AACA,SAAS,2BAA2B,gBAAgB,uBAAuB,uBAAuB,aAAa,QAAQ;AACrH,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,gBAAc,OAAO,SAAS,OAAO,kBAAkB,OAAO,UAAU;AACxE,UAAQ,cAAc;AACtB,MAAI,QAAQ,uBAAuB;AACjC,QAAI,QAAQ,OAAO,mBAAmB;AACpC,aAAO,oBAAoB;AAAA,IAC7B;AACA,YAAQ;AAAA,EACV,WAAW,QAAQ,uBAAuB;AACxC,QAAI,QAAQ,OAAO,sBAAsB;AACvC,aAAO,uBAAuB;AAAA,IAChC;AACA,YAAQ;AAAA,EACV;AACA,WAAS,OAAO,SAAS;AACzB,MAAI,UAAU,GAAG;AACf,mBAAe,CAAC,IAAI;AAAA,EACtB,OAAO;AACL,YAAQ,eAAe,CAAC,KAAK,IAAI,UAAU,QAAQ;AACnD,mBAAe,CAAC,IAAI;AAAA,EACtB;AACF;AACA,SAAS,wBAAwB,OAAO,kBAAkB,kBAAkB,kBAAkB,mBAAmB,mBAAmB,mBAAmB,YAAY;AACjK,MAAI,SAAS;AACb,QAAM,WAAW,WAAW,IAAI,kBAAkB,gBAAgB;AAClE,MAAI,WAAW,OAAO;AACpB,YAAQ;AAAA,EACV;AACA,QAAM,WAAW,WAAW,IAAI,kBAAkB,gBAAgB;AAClE,MAAI,WAAW,OAAO;AACpB,YAAQ;AAAA,EACV;AACA,QAAM,eAAe,WAAW,IAAI,mBAAmB,iBAAiB;AACxE,QAAM,eAAe,WAAW,IAAI,mBAAmB,iBAAiB;AACxE,QAAM,kBAAkB;AACxB,QAAM,cAAc,gBAAgB,UAAU,QAAQ;AACtD,QAAM,aAAa,QAAQ;AAC3B,UAAQ,KAAK,KAAK,UAAU,GAAG;AAAA,IAC7B,KAAK,GAAG;AACN,YAAM,UAAU,eAAe;AAC/B,YAAM,UAAU,WAAW;AAC3B,UAAI,WAAW,GAAG;AAChB,iBAAS,cAAc,UAAU;AACjC,kBAAU;AAAA,MACZ;AACA;AAAA,IACF;AAAA,IACA,KAAK,IAAI;AACP,YAAM,UAAU,eAAe;AAC/B,YAAM,UAAU,WAAW;AAC3B,UAAI,WAAW,GAAG;AAChB,iBAAS,cAAc,UAAU;AACjC,kBAAU;AAAA,MACZ;AACA;AAAA,IACF;AAAA,IACA,KAAK,GAAG;AACN,eAAS;AACT;AAAA,IACF;AAAA,EACF;AACA,SAAO,aAAa,SAAS,SAAS;AACxC;AACA,cAAc,gBAAgB;AAAA,EAC5B,SAAS;AAAA,EACT,OAAO;AAAA,EACP,KAAK,QAAQ;AACX,WAAO,kBAAkB,wBAAwB,qBAAqB,YAAY,MAAM;AAAA,EAC1F;AAAA,EACA,WAAW,WAAW;AACpB,QAAI,UAAU,aAAa,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,IAAI,UAAU,WAAW,GAAG,CAAC;AAC1C,WAAO,OAAO,aAAa,GAAG,IAAI,MAAM;AAAA,EAC1C;AAAA,EACA,oBAAoB,MAAM;AACxB,WAAO,IAAI,qBAAqB,IAAI;AAAA,EACtC;AAAA,EACA,gBAAgB,MAAM;AACpB,UAAM,MAAM,UAAU,OAAO,IAAI;AACjC,QAAI;AACF,YAAM,QAAQ,IAAI,YAAY;AAC9B,YAAM,QAAQ;AACd,aAAO;AAAA,IACT,SAAS,GAAG;AACV,UAAI;AACF,YAAI,QAAQ;AAAA,MACd,SAAS,SAAS;AAAA,MAClB;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,oBAAoB,WAAW,UAAU,SAAS;AAChD,UAAM,QAAQ,IAAI,qBAAqB,WAAW,UAAU,OAAO;AACnE,UAAM,mBAAmB;AACzB,QAAI,iBAAiB,OAAO;AAC1B,YAAM,QAAQ,iBAAiB;AAC/B,aAAO,iBAAiB;AACxB,YAAM,KAAK,WAAW,UAAU;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,WAAW,MAAM;AAC7B,WAAO,cAAc,OAAO,IAAI;AAAA,EAClC;AAAA,EACA,WAAW,WAAW,MAAM;AAC1B,WAAO,WAAW,OAAO,IAAI;AAAA,EAC/B;AACF,CAAC;AACD,SAAS,aAAa;AACpB,MAAI;AACJ,GAAC,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,QAAQ;AAClD;", "names": []}