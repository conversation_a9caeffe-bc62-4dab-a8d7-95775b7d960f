#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
健康检查端点测试脚本
测试修改后的健康检查端点是否正确返回HTTP 503当应用处于starting状态
"""

import requests
import json
import time
from typing import Dict, Any

def test_health_endpoint(base_url: str = "http://localhost:7860") -> None:
    """测试健康检查端点"""
    
    print("🧪 开始测试健康检查端点...")
    
    # 测试端点列表
    endpoints = [
        "/api/health",
        "/api/system/health",
        "/api/system/startup-state"
    ]
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n📍 测试端点: {endpoint}")
        
        try:
            response = requests.get(url, timeout=10)
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            # 尝试解析JSON响应
            try:
                data = response.json()
                print(f"   响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"   响应内容 (非JSON): {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 请求失败: {e}")
    
    # 测试手动更新启动状态
    print(f"\n🔧 测试手动更新启动状态...")
    
    # 设置为starting状态
    try:
        url = f"{base_url}/api/system/startup-state"
        payload = {"status": "starting"}
        
        response = requests.post(url, json=payload, timeout=10)
        print(f"   设置starting状态 - 状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {response.json()}")
        
        # 再次测试健康检查，应该返回503
        print(f"\n🔍 验证starting状态下的健康检查...")
        for endpoint in ["/api/health", "/api/system/health"]:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=10)
            print(f"   {endpoint} - 状态码: {response.status_code} (期望: 503)")
            if response.status_code == 503:
                print(f"   ✅ 正确返回503状态码")
            else:
                print(f"   ⚠️ 未返回期望的503状态码")
                
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 更新状态请求失败: {e}")
    
    # 恢复为ready状态
    try:
        url = f"{base_url}/api/system/startup-state"
        payload = {"status": "ready"}
        
        response = requests.post(url, json=payload, timeout=10)
        print(f"\n🔧 恢复ready状态 - 状态码: {response.status_code}")
        
        # 再次测试健康检查，应该返回200
        print(f"\n🔍 验证ready状态下的健康检查...")
        for endpoint in ["/api/health", "/api/system/health"]:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=10)
            print(f"   {endpoint} - 状态码: {response.status_code} (期望: 200)")
            if response.status_code == 200:
                print(f"   ✅ 正确返回200状态码")
            else:
                print(f"   ⚠️ 未返回期望的200状态码")
                
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 恢复状态请求失败: {e}")

def main():
    """主函数"""
    print("🚀 健康检查端点测试工具")
    print("=" * 50)
    
    # 检查服务是否运行
    base_url = "http://localhost:7860"
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API服务运行正常")
        else:
            print(f"⚠️ API服务响应异常: {response.status_code}")
    except requests.exceptions.RequestException:
        print("❌ API服务未运行，请先启动api_bridge.py")
        print("   启动命令: python api_bridge.py")
        return
    
    # 运行测试
    test_health_endpoint(base_url)
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()