/**
 * 文件名处理工具
 * 专门用于解决重复.png后缀问题
 */

export class FilenameUtils {
  /**
   * 清理文件名，移除重复的扩展名
   */
  static cleanFilename(filename: string): string {
    if (!filename || typeof filename !== 'string') {
      return filename;
    }

    let cleaned = filename;
    
    // 🚨 使用正则表达式移除所有重复的.png后缀
    cleaned = cleaned.replace(/(\.png)+$/gi, '');
    
    // 移除其他可能的重复扩展名
    cleaned = cleaned.replace(/(\.[^.]+)\1+$/g, '$1');
    
    return cleaned;
  }

  /**
   * 安全地添加扩展名，确保不会重复
   */
  static ensureExtension(filename: string, extension: string = 'png'): string {
    const cleaned = this.cleanFilename(filename);
    const ext = extension.startsWith('.') ? extension : `.${extension}`;
    
    // 检查是否已经有正确的扩展名
    if (cleaned.toLowerCase().endsWith(ext.toLowerCase())) {
      return cleaned;
    }
    
    return cleaned + ext;
  }

  /**
   * 从ComfyUI文件名中提取核心信息
   */
  static extractCoreFromComfyUIFilename(filename: string): string {
    const cleaned = this.cleanFilename(filename);
    
    // 处理SceneCard类型文件名
    if (cleaned.includes('SceneCard_')) {
      const match = cleaned.match(/SceneCard_(\d+)/);
      if (match) {
        return `SceneCard_${match[1]}`;
      }
    }
    
    // 处理A4、A5、Square类型
    if (cleaned.includes('_A4_') || cleaned.includes('_A5_') || cleaned.includes('_Square_')) {
      const typeMatch = cleaned.match(/_(A4|A5|Square)_/);
      if (typeMatch) {
        return typeMatch[1];
      }
    }
    
    // 移除ComfyUI的序号后缀 (_00001_)
    const withoutSequence = cleaned.replace(/_\d{5}_$/, '');
    
    return withoutSequence;
  }

  /**
   * 生成安全的图片ID
   */
  static generateSafeImageId(baseFilename: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    
    // 清理并提取核心文件名
    const coreFilename = this.extractCoreFromComfyUIFilename(baseFilename);
    const safeName = coreFilename
      .replace(/[^a-zA-Z0-9_-]/g, '_')
      .substring(0, 30);
    
    return `img_${timestamp}_${random}_${safeName}`;
  }

  /**
   * 验证文件名是否有重复后缀问题
   */
  static validateFilename(filename: string): {
    hasIssue: boolean;
    issues: string[];
    cleaned: string;
  } {
    const issues: string[] = [];
    
    // 检查重复的.png后缀
    if (filename.match(/\.png\.png/gi)) {
      issues.push('检测到重复的.png后缀');
    }
    
    // 检查其他重复扩展名
    if (filename.match(/(\.[^.]+)\1+$/)) {
      issues.push('检测到重复的扩展名');
    }
    
    // 检查过长的文件名
    if (filename.length > 255) {
      issues.push('文件名过长（超过255字符）');
    }
    
    return {
      hasIssue: issues.length > 0,
      issues,
      cleaned: this.cleanFilename(filename)
    };
  }

  /**
   * 批量修复文件名数组
   */
  static batchCleanFilenames(filenames: string[]): string[] {
    return filenames.map(filename => this.cleanFilename(filename));
  }
}

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  (window as any).FilenameUtils = FilenameUtils;
  console.log('🛠️ FilenameUtils已暴露到全局: window.FilenameUtils');
}