{"name": "@css-render/vue3-ssr", "version": "0.15.14", "devDependencies": {"@babel/preset-env": "^7.12.17", "@babel/preset-typescript": "^7.12.17", "@css-render/eslint-config": "0.0.0", "@rushstack/eslint-config": "~2.5.1", "@types/jest": "^27.0.3", "@vue/server-renderer": "^3.0.11", "babel-jest": "^27.4.5", "css-render": "~0.15.14", "eslint": "~8.5.0", "jest": "^27.4.5", "jest-standard-reporter": "~2.0.0", "typescript": "~4.4.4", "vue": "^3.2.45"}, "peerDependencies": {"vue": "^3.0.11"}, "main": "lib/index.js", "module": "esm/index.js", "sideEffects": false, "license": "MIT", "scripts": {"lint": "eslint --fix *.js src/**/*.ts __tests__/**/*.ts", "test": "jest", "build": "npm run lint && rm -rf es lib && npm run test && tsc -p tsconfig.esm.json && tsc -p tsconfig.cjs.json"}}