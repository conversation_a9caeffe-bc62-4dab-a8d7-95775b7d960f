{"version": 3, "file": "loaders.mjs", "sources": ["../../src/base/Signal.ts", "../../src/base/parseUri.ts", "../../src/LoaderResource.ts", "../../src/base/AsyncQueue.ts", "../../src/Loader.ts", "../../src/AppLoaderPlugin.ts", "../../src/TextureLoader.ts", "../../src/base/encodeBinary.ts", "../../src/middleware/parsing.ts", "../../src/ParsingLoader.ts", "../../src/index.ts"], "sourcesContent": ["/* jshint -W097 */\n\n/**\n * @memberof PIXI\n */\nexport class SignalBinding<CbType>\n{\n    _fn: any;\n    _once: boolean;\n    _next: SignalBinding<CbType>;\n    _prev: SignalBinding<CbType>;\n    _owner: Signal<CbType>;\n    _thisArg: any;\n\n    /**\n     * SignalBinding constructor.\n     * @constructs SignalBinding\n     * @param {Function} fn - Event handler to be called.\n     * @param {boolean} [once=false] - Should this listener be removed after dispatch\n     * @param {object} [thisArg] - The context of the callback function.\n     * @api private\n     */\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    constructor(fn: CbType, once = false, thisArg: any)\n    {\n        this._fn = fn;\n        this._once = once;\n        this._thisArg = thisArg;\n        this._next = this._prev = this._owner = null;\n    }\n\n    detach(): boolean\n    {\n        if (this._owner === null) return false;\n        this._owner.detach(this);\n\n        return true;\n    }\n}\n\n/**\n * @param self\n * @param node\n * @private\n */\nfunction _addSignalBinding<CbType>(self: Signal<CbType>, node: SignalBinding<CbType>)\n{\n    if (!self._head)\n    {\n        self._head = node;\n        self._tail = node;\n    }\n    else\n    {\n        self._tail._next = node;\n        node._prev = self._tail;\n        self._tail = node;\n    }\n\n    node._owner = self;\n\n    return node;\n}\n\n/**\n * @memberof PIXI\n */\nexport class Signal<CbType = (...args: any) => void>\n{\n    _head: SignalBinding<CbType>;\n    _tail: SignalBinding<CbType>;\n\n    /**\n     * MiniSignal constructor.\n     * @example\n     * let mySignal = new Signal();\n     * let binding = mySignal.add(onSignal);\n     * mySignal.dispatch('foo', 'bar');\n     * mySignal.detach(binding);\n     */\n    constructor()\n    {\n        this._head = this._tail = undefined;\n    }\n\n    /**\n     * Return an array of attached SignalBinding.\n     * @param {boolean} [exists=false] - We only need to know if there are handlers.\n     * @returns {PIXI.SignalBinding[] | boolean} Array of attached SignalBinding or Boolean if called with exists = true\n     * @api public\n     */\n    handlers(exists = false): Array<SignalBinding<CbType>> | boolean\n    {\n        let node = this._head;\n\n        if (exists) return !!node;\n\n        const ee = [];\n\n        while (node)\n        {\n            ee.push(node);\n            node = node._next;\n        }\n\n        return ee;\n    }\n\n    /**\n     * Return true if node is a SignalBinding attached to this MiniSignal\n     * @param {PIXI.SignalBinding} node - Node to check.\n     * @returns {boolean} True if node is attache to mini-signal\n     */\n    has(node: SignalBinding<CbType>): boolean\n    {\n        if (!(node instanceof SignalBinding))\n        {\n            throw new Error('MiniSignal#has(): First arg must be a SignalBinding object.');\n        }\n\n        return node._owner === this;\n    }\n\n    /**\n     * Dispaches a signal to all registered listeners.\n     * @param {...any} args\n     * @returns {boolean} Indication if we've emitted an event.\n     */\n    dispatch(...args: any[]): boolean\n    {\n        let node = this._head;\n\n        if (!node) return false;\n\n        while (node)\n        {\n            if (node._once) this.detach(node);\n            node._fn.apply(node._thisArg, args);\n            node = node._next;\n        }\n\n        return true;\n    }\n\n    /**\n     * Register a new listener.\n     * @param {Function} fn - Callback function.\n     * @param {object} [thisArg] - The context of the callback function.\n     * @returns {PIXI.SignalBinding} The SignalBinding node that was added.\n     */\n    add(fn: CbType, thisArg: any = null): SignalBinding<CbType>\n    {\n        if (typeof fn !== 'function')\n        {\n            throw new Error('MiniSignal#add(): First arg must be a Function.');\n        }\n\n        return _addSignalBinding<CbType>(this, new SignalBinding<CbType>(fn, false, thisArg));\n    }\n\n    /**\n     * Register a new listener that will be executed only once.\n     * @param {Function} fn - Callback function.\n     * @param {object} [thisArg] - The context of the callback function.\n     * @returns {PIXI.SignalBinding} The SignalBinding node that was added.\n     */\n    once(fn: CbType, thisArg: any = null): SignalBinding<CbType>\n    {\n        if (typeof fn !== 'function')\n        {\n            throw new Error('MiniSignal#once(): First arg must be a Function.');\n        }\n\n        return _addSignalBinding<CbType>(this, new SignalBinding<CbType>(fn, true, thisArg));\n    }\n\n    /**\n     * Remove binding object.\n     * @param {PIXI.SignalBinding} node - The binding node that will be removed.\n     * @returns {Signal} The instance on which this method was called.\n      @api public */\n    detach(node: SignalBinding<CbType>): this\n    {\n        if (!(node instanceof SignalBinding))\n        {\n            throw new Error('MiniSignal#detach(): First arg must be a SignalBinding object.');\n        }\n        if (node._owner !== this) return this; // todo: or error?\n\n        if (node._prev) node._prev._next = node._next;\n        if (node._next) node._next._prev = node._prev;\n\n        if (node === this._head)\n        { // first node\n            this._head = node._next;\n            if (node._next === null)\n            {\n                this._tail = null;\n            }\n        }\n        else if (node === this._tail)\n        { // last node\n            this._tail = node._prev;\n            this._tail._next = null;\n        }\n\n        node._owner = null;\n\n        return this;\n    }\n\n    /**\n     * Detach all listeners.\n     * @returns {Signal} The instance on which this method was called.\n     */\n    detachAll(): this\n    {\n        let node = this._head;\n\n        if (!node) return this;\n\n        this._head = this._tail = null;\n\n        while (node)\n        {\n            node._owner = null;\n            node = node._next;\n        }\n\n        return this;\n    }\n}\n", "/**\n * function from npm package `parseUri`, converted to TS to avoid leftpad incident\n * @param {string} str\n * @param [opts] - options\n * @param {boolean} [opts.strictMode] - type of parser\n */\nexport function parseUri(str: string, opts: { strictMode?: boolean }): any\n{\n    opts = opts || {};\n\n    const o = {\n        // eslint-disable-next-line max-len\n        key: ['source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'],\n        q: {\n            name: 'query<PERSON>ey',\n            parser: /(?:^|&)([^&=]*)=?([^&]*)/g\n        },\n        parser: {\n            // eslint-disable-next-line max-len\n            strict: /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n            // eslint-disable-next-line max-len\n            loose: /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/\n        }\n    };\n\n    const m = o.parser[opts.strictMode ? 'strict' : 'loose'].exec(str);\n    const uri: any = {};\n    let i = 14;\n\n    while (i--) uri[o.key[i]] = m[i] || '';\n\n    uri[o.q.name] = {};\n    uri[o.key[12]].replace(o.q.parser, (_t0: any, t1: any, t2: any) =>\n    {\n        if (t1) uri[o.q.name][t1] = t2;\n    });\n\n    return uri;\n}\n", "import type { Dict } from '@pixi/utils';\nimport { Signal } from './base/Signal';\nimport { parseUri } from './base/parseUri';\nimport type { IBaseTextureOptions, Texture } from '@pixi/core';\n\n// tests if CORS is supported in XHR, if not we need to use XDR\nlet useXdr: boolean;\nlet tempAnchor: any = null;\n\n// some status constants\nconst STATUS_NONE = 0;\nconst STATUS_OK = 200;\nconst STATUS_EMPTY = 204;\nconst STATUS_IE_BUG_EMPTY = 1223;\nconst STATUS_TYPE_OK = 2;\n\n// noop\nfunction _noop(): void { /* empty */ }\n\n/**\n * Quick helper to set a value on one of the extension maps. Ensures there is no\n * dot at the start of the extension.\n * @ignore\n * @param map - The map to set on.\n * @param extname - The extension (or key) to set.\n * @param val - The value to set.\n */\nfunction setExtMap(map: Dict<any>, extname: string, val: number)\n{\n    if (extname && extname.indexOf('.') === 0)\n    {\n        extname = extname.substring(1);\n    }\n\n    if (!extname)\n    {\n        return;\n    }\n\n    map[extname] = val;\n}\n\n/**\n * Quick helper to get string xhr type.\n * @ignore\n * @param xhr - The request to check.\n * @returns The type.\n */\nfunction reqType(xhr: XMLHttpRequest)\n{\n    return xhr.toString().replace('object ', '');\n}\n\n/**\n * Metadata for loader resource. It is very messy way to pass options for loader middlewares\n *\n * Can be extended in `GlobalMixins.IResourceMetadata`\n * @memberof PIXI\n */\nexport interface IResourceMetadata extends GlobalMixins.IResourceMetadata, IBaseTextureOptions\n{\n    /** The element to use for loading, instead of creating one. */\n    loadElement?: HTMLImageElement | HTMLAudioElement | HTMLVideoElement;\n    /**\n     * Skips adding source(s) to the load element. This\n     * is useful if you want to pass in a `loadElement` that you already added load sources to.\n     */\n    skipSource?: boolean;\n    /**\n     * The mime type to use for the source element\n     * of a video/audio elment. If the urls are an array, you can pass this as an array as well\n     * where each index is the mime type to use for the corresponding url index.\n     */\n    mimeType?: string | string[];\n\n    /**\n     * Used by BitmapFonts, Spritesheet and CompressedTextures as the options to used for\n     * metadata when loading the child image.\n     */\n    imageMetadata?: IResourceMetadata;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\ninterface LoaderResource extends GlobalMixins.LoaderResource, GlobalMixins.ILoaderResource {}\n\n/**\n * Manages the state and loading of a resource and all child resources.\n *\n * Can be extended in `GlobalMixins.LoaderResource`.\n * @memberof PIXI\n */\nclass LoaderResource\n{\n    /**\n     * Texture reference for loading images and other textures.\n     * @type {PIXI.Texture}\n     */\n    texture?: Texture;\n\n    /** used by parsing middleware */\n    blob?: Blob;\n\n    /**\n     * The name of this resource.\n     * @readonly\n     * @type {string}\n     */\n    readonly name: string;\n    /**\n     * The url used to load this resource.\n     * @readonly\n     * @type {string}\n     */\n    readonly url: string;\n    /**\n     * The extension used to load this resource.\n     * @readonly\n     * @type {string}\n     */\n    readonly extension: string;\n    /** The data that was loaded by the resource. */\n    data: any;\n    /** Is this request cross-origin? If unset, determined automatically. */\n    crossOrigin: string | boolean;\n    /**\n     * A timeout in milliseconds for the load. If the load takes longer than this time\n     * it is cancelled and the load is considered a failure. If this value is set to `0`\n     * then there is no explicit timeout.\n     * @type {number}\n     */\n    timeout: number;\n    /**\n     * The method of loading to use for this resource.\n     * @type {PIXI.LoaderResource.LOAD_TYPE}\n     */\n    loadType: LoaderResource.LOAD_TYPE;\n    /**\n     * The type used to load the resource via XHR. If unset, determined automatically.\n     * @member {string}\n     */\n    xhrType: string;\n\n    /**\n     * Extra info for middleware, and controlling specifics about how the resource loads.\n     *\n     * Note that if you pass in a `loadElement`, the Resource class takes ownership of it.\n     * Meaning it will modify it as it sees fit.\n     * @type {PIXI.IResourceMetadata}\n     */\n    metadata: IResourceMetadata;\n    /**\n     * The error that occurred while loading (if any).\n     * @readonly\n     * @member {Error}\n     */\n    error: Error;\n    /**\n     * The XHR object that was used to load this resource. This is only set\n     * when `loadType` is `LoaderResource.LOAD_TYPE.XHR`.\n     * @readonly\n     */\n    xhr: XMLHttpRequest;\n\n    private xdr: any;\n    /**\n     * The child resources this resource owns.\n     * @type {PIXI.LoaderResource[]}\n     */\n    readonly children: LoaderResource[];\n    /**\n     * The resource type.\n     * @readonly\n     * @type {PIXI.LoaderResource.TYPE}\n     */\n    type: LoaderResource.TYPE;\n    /**\n     * The progress chunk owned by this resource.\n     * @readonly\n     * @member {number}\n     */\n    progressChunk: number;\n    /**\n     * Dispatched when the resource beings to load.\n     *\n     * The callback looks like {@link LoaderResource.OnStartSignal}.\n     * @type {PIXI.Signal}\n     */\n    onStart: Signal<LoaderResource.OnStartSignal>;\n    /**\n     * Dispatched each time progress of this resource load updates.\n     * Not all resources types and loader systems can support this event\n     * so sometimes it may not be available. If the resource\n     * is being loaded on a modern browser, using XHR, and the remote server\n     * properly sets Content-Length headers, then this will be available.\n     *\n     * The callback looks like {@link LoaderResource.OnProgressSignal}.\n     * @type {PIXI.Signal}\n     */\n    onProgress: Signal<LoaderResource.OnProgressSignal>;\n    /**\n     * Dispatched once this resource has loaded, if there was an error it will\n     * be in the `error` property.\n     *\n     * The callback looks like {@link LoaderResource.OnCompleteSignal}.\n     * @type {PIXI.Signal}\n     */\n    onComplete: Signal<LoaderResource.OnCompleteSignal>;\n    /**\n     * Dispatched after this resource has had all the *after* middleware run on it.\n     *\n     * The callback looks like {@link LoaderResource.OnCompleteSignal}.\n     * @type {PIXI.Signal}\n     */\n    onAfterMiddleware: Signal<LoaderResource.OnCompleteSignal>;\n\n    /**\n     * The state flags of this resource.\n     * @private\n     * @member {number}\n     */\n    private _flags: number;\n\n    /**\n     * The `dequeue` method that will be used a storage place for the async queue dequeue method\n     * used privately by the loader.\n     * @private\n     * @member {Function}\n     */\n    _dequeue: any = _noop;\n\n    /**\n     * Used a storage place for the on load binding used privately by the loader.\n     * @private\n     * @member {Function}\n     */\n    _onLoadBinding: any = null;\n\n    /**\n     * The timer for element loads to check if they timeout.\n     * @private\n     */\n    private _elementTimer = 0;\n\n    /**\n     * The `complete` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundComplete: any = null;\n\n    /**\n     * The `_onError` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundOnError: any = null;\n\n    /**\n     * The `_onProgress` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundOnProgress: any = null;\n\n    /**\n     * The `_onTimeout` function bound to this resource's context.\n     * @private\n     * @type {Function}\n     */\n    private _boundOnTimeout: any = null;\n\n    private _boundXhrOnError: any = null;\n    private _boundXhrOnTimeout: any = null;\n    private _boundXhrOnAbort: any = null;\n    private _boundXhrOnLoad: any = null;\n\n    /**\n     * Sets the load type to be used for a specific extension.\n     * @static\n     * @param {string} extname - The extension to set the type for, e.g. \"png\" or \"fnt\"\n     * @param {PIXI.LoaderResource.LOAD_TYPE} loadType - The load type to set it to.\n     */\n    static setExtensionLoadType(extname: string, loadType: LoaderResource.LOAD_TYPE): void\n    {\n        setExtMap(LoaderResource._loadTypeMap, extname, loadType);\n    }\n    /**\n     * Sets the load type to be used for a specific extension.\n     * @static\n     * @param {string} extname - The extension to set the type for, e.g. \"png\" or \"fnt\"\n     * @param {PIXI.LoaderResource.XHR_RESPONSE_TYPE} xhrType - The xhr type to set it to.\n     */\n    static setExtensionXhrType(extname: string, xhrType: LoaderResource.XHR_RESPONSE_TYPE): void\n    {\n        setExtMap(LoaderResource._xhrTypeMap, extname, xhrType as any);\n    }\n\n    /**\n     * @param {string} name - The name of the resource to load.\n     * @param {string|string[]} url - The url for this resource, for audio/video loads you can pass\n     *      an array of sources.\n     * @param {object} [options] - The options for the load.\n     * @param {string|boolean} [options.crossOrigin] - Is this request cross-origin? Default is to\n     *      determine automatically.\n     * @param {number} [options.timeout=0] - A timeout in milliseconds for the load. If the load takes\n     *      longer than this time it is cancelled and the load is considered a failure. If this value is\n     *      set to `0` then there is no explicit timeout.\n     * @param {PIXI.LoaderResource.LOAD_TYPE} [options.loadType=LOAD_TYPE.XHR] - How should this resource\n     *      be loaded?\n     * @param {PIXI.LoaderResource.XHR_RESPONSE_TYPE} [options.xhrType=XHR_RESPONSE_TYPE.DEFAULT] - How\n     *      should the data being loaded be interpreted when using XHR?\n     * @param {PIXI.LoaderResource.IMetadata} [options.metadata] - Extra configuration for middleware\n     *      and the Resource object.\n     */\n    constructor(name: string, url: string | string[], options?: {\n        crossOrigin?: string | boolean;\n        timeout?: number;\n        loadType?: LoaderResource.LOAD_TYPE;\n        xhrType?: LoaderResource.XHR_RESPONSE_TYPE;\n        metadata?: IResourceMetadata;\n    })\n    {\n        if (typeof name !== 'string' || typeof url !== 'string')\n        {\n            throw new Error('Both name and url are required for constructing a resource.');\n        }\n\n        options = options || {};\n\n        this._flags = 0;\n\n        // set data url flag, needs to be set early for some _determineX checks to work.\n        this._setFlag(LoaderResource.STATUS_FLAGS.DATA_URL, url.indexOf('data:') === 0);\n\n        this.name = name;\n\n        this.url = url;\n\n        this.extension = this._getExtension();\n\n        this.data = null;\n\n        this.crossOrigin = options.crossOrigin === true ? 'anonymous' : options.crossOrigin;\n\n        this.timeout = options.timeout || 0;\n\n        this.loadType = options.loadType || this._determineLoadType();\n\n        // The type used to load the resource via XHR. If unset, determined automatically.\n        this.xhrType = options.xhrType;\n\n        // Extra info for middleware, and controlling specifics about how the resource loads.\n        // Note that if you pass in a `loadElement`, the Resource class takes ownership of it.\n        // Meaning it will modify it as it sees fit.\n        this.metadata = options.metadata || {};\n\n        // The error that occurred while loading (if any).\n        this.error = null;\n\n        // The XHR object that was used to load this resource. This is only set\n        // when `loadType` is `LoaderResource.LOAD_TYPE.XHR`.\n        this.xhr = null;\n\n        // The child resources this resource owns.\n        this.children = [];\n\n        // The resource type.\n        this.type = LoaderResource.TYPE.UNKNOWN;\n\n        // The progress chunk owned by this resource.\n        this.progressChunk = 0;\n\n        // The `dequeue` method that will be used a storage place for the async queue dequeue method\n        // used privately by the loader.\n        this._dequeue = _noop;\n\n        // Used a storage place for the on load binding used privately by the loader.\n        this._onLoadBinding = null;\n\n        // The timer for element loads to check if they timeout.\n        this._elementTimer = 0;\n\n        this._boundComplete = this.complete.bind(this);\n        this._boundOnError = this._onError.bind(this);\n        this._boundOnProgress = this._onProgress.bind(this);\n        this._boundOnTimeout = this._onTimeout.bind(this);\n\n        // xhr callbacks\n        this._boundXhrOnError = this._xhrOnError.bind(this);\n        this._boundXhrOnTimeout = this._xhrOnTimeout.bind(this);\n        this._boundXhrOnAbort = this._xhrOnAbort.bind(this);\n        this._boundXhrOnLoad = this._xhrOnLoad.bind(this);\n\n        // Dispatched when the resource beings to load.\n        this.onStart = new Signal();\n\n        // Dispatched each time progress of this resource load updates.\n        // Not all resources types and loader systems can support this event\n        // so sometimes it may not be available. If the resource\n        // is being loaded on a modern browser, using XHR, and the remote server\n        // properly sets Content-Length headers, then this will be available.\n        this.onProgress = new Signal();\n\n        // Dispatched once this resource has loaded, if there was an error it will\n        // be in the `error` property.\n        this.onComplete = new Signal();\n\n        // Dispatched after this resource has had all the *after* middleware run on it.\n        this.onAfterMiddleware = new Signal();\n    }\n\n    /**\n     * When the resource starts to load.\n     * @memberof PIXI.LoaderResource\n     * @callback OnStartSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n\n    /**\n     * When the resource reports loading progress.\n     * @memberof PIXI.LoaderResource\n     * @callback OnProgressSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     * @param {number} percentage - The progress of the load in the range [0, 1].\n     */\n\n    /**\n     * When the resource finishes loading.\n     * @memberof PIXI.LoaderResource\n     * @callback OnCompleteSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n\n    /**\n     * @memberof PIXI.LoaderResource\n     * @typedef {object} IMetadata\n     * @property {HTMLImageElement|HTMLAudioElement|HTMLVideoElement} [loadElement=null] - The\n     *      element to use for loading, instead of creating one.\n     * @property {boolean} [skipSource=false] - Skips adding source(s) to the load element. This\n     *      is useful if you want to pass in a `loadElement` that you already added load sources to.\n     * @property {string|string[]} [mimeType] - The mime type to use for the source element\n     *      of a video/audio elment. If the urls are an array, you can pass this as an array as well\n     *      where each index is the mime type to use for the corresponding url index.\n     */\n\n    /**\n     * Stores whether or not this url is a data url.\n     * @readonly\n     * @member {boolean}\n     */\n    get isDataUrl(): boolean\n    {\n        return this._hasFlag(LoaderResource.STATUS_FLAGS.DATA_URL);\n    }\n\n    /**\n     * Describes if this resource has finished loading. Is true when the resource has completely\n     * loaded.\n     * @readonly\n     * @member {boolean}\n     */\n    get isComplete(): boolean\n    {\n        return this._hasFlag(LoaderResource.STATUS_FLAGS.COMPLETE);\n    }\n\n    /**\n     * Describes if this resource is currently loading. Is true when the resource starts loading,\n     * and is false again when complete.\n     * @readonly\n     * @member {boolean}\n     */\n    get isLoading(): boolean\n    {\n        return this._hasFlag(LoaderResource.STATUS_FLAGS.LOADING);\n    }\n\n    /** Marks the resource as complete. */\n    complete(): void\n    {\n        this._clearEvents();\n        this._finish();\n    }\n\n    /**\n     * Aborts the loading of this resource, with an optional message.\n     * @param {string} message - The message to use for the error\n     */\n    abort(message: string): void\n    {\n        // abort can be called multiple times, ignore subsequent calls.\n        if (this.error)\n        {\n            return;\n        }\n\n        // store error\n        this.error = new Error(message);\n\n        // clear events before calling aborts\n        this._clearEvents();\n\n        // abort the actual loading\n        if (this.xhr)\n        {\n            this.xhr.abort();\n        }\n        else if (this.xdr)\n        {\n            this.xdr.abort();\n        }\n        else if (this.data)\n        {\n            // single source\n            if (this.data.src)\n            {\n                this.data.src = LoaderResource.EMPTY_GIF;\n            }\n            // multi-source\n            else\n            {\n                while (this.data.firstChild)\n                {\n                    this.data.removeChild(this.data.firstChild);\n                }\n            }\n        }\n\n        // done now.\n        this._finish();\n    }\n\n    /**\n     * Kicks off loading of this resource. This method is asynchronous.\n     * @param {PIXI.LoaderResource.OnCompleteSignal} [cb] - Optional callback to call once the resource is loaded.\n     */\n    load(cb?: LoaderResource.OnCompleteSignal): void\n    {\n        if (this.isLoading)\n        {\n            return;\n        }\n\n        if (this.isComplete)\n        {\n            if (cb)\n            {\n                setTimeout(() => cb(this), 1);\n            }\n\n            return;\n        }\n        else if (cb)\n        {\n            this.onComplete.once(cb);\n        }\n\n        this._setFlag(LoaderResource.STATUS_FLAGS.LOADING, true);\n\n        this.onStart.dispatch(this);\n\n        // if unset, determine the value\n        if (this.crossOrigin === false || typeof this.crossOrigin !== 'string')\n        {\n            this.crossOrigin = this._determineCrossOrigin(this.url);\n        }\n\n        switch (this.loadType)\n        {\n            case LoaderResource.LOAD_TYPE.IMAGE:\n                this.type = LoaderResource.TYPE.IMAGE;\n                this._loadElement('image');\n                break;\n\n            case LoaderResource.LOAD_TYPE.AUDIO:\n                this.type = LoaderResource.TYPE.AUDIO;\n                this._loadSourceElement('audio');\n                break;\n\n            case LoaderResource.LOAD_TYPE.VIDEO:\n                this.type = LoaderResource.TYPE.VIDEO;\n                this._loadSourceElement('video');\n                break;\n\n            case LoaderResource.LOAD_TYPE.XHR:\n            /* falls through */\n            default:\n                if (typeof useXdr === 'undefined')\n                {\n                    useXdr = !!((globalThis as any).XDomainRequest && !('withCredentials' in (new XMLHttpRequest())));\n                }\n                if (useXdr && this.crossOrigin)\n                {\n                    this._loadXdr();\n                }\n                else\n                {\n                    this._loadXhr();\n                }\n                break;\n        }\n    }\n\n    /**\n     * Checks if the flag is set.\n     * @param flag - The flag to check.\n     * @returns True if the flag is set.\n     */\n    private _hasFlag(flag: number): boolean\n    {\n        return (this._flags & flag) !== 0;\n    }\n\n    /**\n     * (Un)Sets the flag.\n     * @param flag - The flag to (un)set.\n     * @param value - Whether to set or (un)set the flag.\n     */\n    private _setFlag(flag: number, value: boolean): void\n    {\n        this._flags = value ? (this._flags | flag) : (this._flags & ~flag);\n    }\n\n    /** Clears all the events from the underlying loading source. */\n    private _clearEvents(): void\n    {\n        clearTimeout(this._elementTimer);\n\n        if (this.data && this.data.removeEventListener)\n        {\n            this.data.removeEventListener('error', this._boundOnError, false);\n            this.data.removeEventListener('load', this._boundComplete, false);\n            this.data.removeEventListener('progress', this._boundOnProgress, false);\n            this.data.removeEventListener('canplaythrough', this._boundComplete, false);\n        }\n\n        if (this.xhr)\n        {\n            if (this.xhr.removeEventListener)\n            {\n                this.xhr.removeEventListener('error', this._boundXhrOnError, false);\n                this.xhr.removeEventListener('timeout', this._boundXhrOnTimeout, false);\n                this.xhr.removeEventListener('abort', this._boundXhrOnAbort, false);\n                this.xhr.removeEventListener('progress', this._boundOnProgress, false);\n                this.xhr.removeEventListener('load', this._boundXhrOnLoad, false);\n            }\n            else\n            {\n                this.xhr.onerror = null;\n                this.xhr.ontimeout = null;\n                this.xhr.onprogress = null;\n                this.xhr.onload = null;\n            }\n        }\n    }\n\n    /** Finalizes the load. */\n    private _finish(): void\n    {\n        if (this.isComplete)\n        {\n            throw new Error('Complete called again for an already completed resource.');\n        }\n\n        this._setFlag(LoaderResource.STATUS_FLAGS.COMPLETE, true);\n        this._setFlag(LoaderResource.STATUS_FLAGS.LOADING, false);\n\n        this.onComplete.dispatch(this);\n    }\n\n    /**\n     * Loads this resources using an element that has a single source,\n     * like an HTMLImageElement.\n     * @private\n     * @param type - The type of element to use.\n     */\n    _loadElement(type: string): void\n    {\n        if (this.metadata.loadElement)\n        {\n            this.data = this.metadata.loadElement;\n        }\n        else if (type === 'image' && typeof globalThis.Image !== 'undefined')\n        {\n            this.data = new Image();\n        }\n        else\n        {\n            this.data = document.createElement(type);\n        }\n\n        if (this.crossOrigin)\n        {\n            this.data.crossOrigin = this.crossOrigin;\n        }\n\n        if (!this.metadata.skipSource)\n        {\n            this.data.src = this.url;\n        }\n\n        this.data.addEventListener('error', this._boundOnError, false);\n        this.data.addEventListener('load', this._boundComplete, false);\n        this.data.addEventListener('progress', this._boundOnProgress, false);\n\n        if (this.timeout)\n        {\n            this._elementTimer = setTimeout(this._boundOnTimeout, this.timeout) as any;\n        }\n    }\n\n    /**\n     * Loads this resources using an element that has multiple sources,\n     * like an HTMLAudioElement or HTMLVideoElement.\n     * @param type - The type of element to use.\n     */\n    private _loadSourceElement(type: string): void\n    {\n        if (this.metadata.loadElement)\n        {\n            this.data = this.metadata.loadElement;\n        }\n        else if (type === 'audio' && typeof globalThis.Audio !== 'undefined')\n        {\n            this.data = new Audio();\n        }\n        else\n        {\n            this.data = document.createElement(type);\n        }\n\n        if (this.data === null)\n        {\n            this.abort(`Unsupported element: ${type}`);\n\n            return;\n        }\n\n        if (this.crossOrigin)\n        {\n            this.data.crossOrigin = this.crossOrigin;\n        }\n\n        if (!this.metadata.skipSource)\n        {\n            // support for CocoonJS Canvas+ runtime, lacks document.createElement('source')\n            if ((navigator as any).isCocoonJS)\n            {\n                this.data.src = Array.isArray(this.url) ? this.url[0] : this.url;\n            }\n            else if (Array.isArray(this.url))\n            {\n                const mimeTypes = this.metadata.mimeType;\n\n                for (let i = 0; i < this.url.length; ++i)\n                {\n                    this.data.appendChild(\n                        this._createSource(type, this.url[i], Array.isArray(mimeTypes) ? mimeTypes[i] : mimeTypes)\n                    );\n                }\n            }\n            else\n            {\n                const mimeTypes = this.metadata.mimeType;\n\n                this.data.appendChild(\n                    this._createSource(type, this.url, Array.isArray(mimeTypes) ? mimeTypes[0] : mimeTypes)\n                );\n            }\n        }\n\n        this.data.addEventListener('error', this._boundOnError, false);\n        this.data.addEventListener('load', this._boundComplete, false);\n        this.data.addEventListener('progress', this._boundOnProgress, false);\n        this.data.addEventListener('canplaythrough', this._boundComplete, false);\n\n        this.data.load();\n\n        if (this.timeout)\n        {\n            this._elementTimer = setTimeout(this._boundOnTimeout, this.timeout) as any;\n        }\n    }\n\n    /** Loads this resources using an XMLHttpRequest. */\n    private _loadXhr(): void\n    {\n        // if unset, determine the value\n        if (typeof this.xhrType !== 'string')\n        {\n            this.xhrType = this._determineXhrType();\n        }\n\n        const xhr = this.xhr = new XMLHttpRequest();\n\n        // send credentials when crossOrigin with credentials requested\n        if (this.crossOrigin === 'use-credentials')\n        {\n            xhr.withCredentials = true;\n        }\n\n        // set the request type and url\n        xhr.open('GET', this.url, true);\n\n        xhr.timeout = this.timeout;\n\n        // load json as text and parse it ourselves. We do this because some browsers\n        // *cough* safari *cough* can't deal with it.\n        if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.JSON\n            || this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT)\n        {\n            xhr.responseType = LoaderResource.XHR_RESPONSE_TYPE.TEXT;\n        }\n        else\n        {\n            xhr.responseType = this.xhrType as any;\n        }\n\n        xhr.addEventListener('error', this._boundXhrOnError, false);\n        xhr.addEventListener('timeout', this._boundXhrOnTimeout, false);\n        xhr.addEventListener('abort', this._boundXhrOnAbort, false);\n        xhr.addEventListener('progress', this._boundOnProgress, false);\n        xhr.addEventListener('load', this._boundXhrOnLoad, false);\n\n        xhr.send();\n    }\n\n    /** Loads this resources using an XDomainRequest. This is here because we need to support IE9 (gross). */\n    private _loadXdr(): void\n    {\n        // if unset, determine the value\n        if (typeof this.xhrType !== 'string')\n        {\n            this.xhrType = this._determineXhrType();\n        }\n\n        const xdr = this.xhr = new (globalThis as any).XDomainRequest(); // eslint-disable-line no-undef\n\n        // XDomainRequest has a few quirks. Occasionally it will abort requests\n        // A way to avoid this is to make sure ALL callbacks are set even if not used\n        // More info here: http://stackoverflow.com/questions/15786966/xdomainrequest-aborts-post-on-ie-9\n        xdr.timeout = this.timeout || 5000; // XDR needs a timeout value or it breaks in IE9\n\n        xdr.onerror = this._boundXhrOnError;\n        xdr.ontimeout = this._boundXhrOnTimeout;\n        xdr.onprogress = this._boundOnProgress;\n        xdr.onload = this._boundXhrOnLoad;\n\n        xdr.open('GET', this.url, true);\n\n        // Note: The xdr.send() call is wrapped in a timeout to prevent an\n        // issue with the interface where some requests are lost if multiple\n        // XDomainRequests are being sent at the same time.\n        // Some info here: https://github.com/photonstorm/phaser/issues/1248\n        setTimeout(() => xdr.send(), 1);\n    }\n\n    /**\n     * Creates a source used in loading via an element.\n     * @param type - The element type (video or audio).\n     * @param url - The source URL to load from.\n     * @param [mime] - The mime type of the video\n     * @returns The source element.\n     */\n    private _createSource(type: string, url: string, mime: string): HTMLSourceElement\n    {\n        if (!mime)\n        {\n            mime = `${type}/${this._getExtension(url)}`;\n        }\n\n        const source = document.createElement('source');\n\n        source.src = url;\n        source.type = mime;\n\n        return source;\n    }\n\n    /**\n     * Called if a load errors out.\n     * @param event - The error event from the element that emits it.\n     */\n    private _onError(event: Event): void\n    {\n        this.abort(`Failed to load element using: ${(event.target as any).nodeName}`);\n    }\n\n    /**\n     * Called if a load progress event fires for an element or xhr/xdr.\n     * @param event - Progress event.\n     */\n    private _onProgress(event: ProgressEvent): void\n    {\n        if (event && event.lengthComputable)\n        {\n            this.onProgress.dispatch(this, event.loaded / event.total);\n        }\n    }\n\n    /** Called if a timeout event fires for an element. */\n    private _onTimeout(): void\n    {\n        this.abort(`Load timed out.`);\n    }\n\n    /** Called if an error event fires for xhr/xdr. */\n    private _xhrOnError(): void\n    {\n        const xhr = this.xhr;\n\n        this.abort(`${reqType(xhr)} Request failed. Status: ${xhr.status}, text: \"${xhr.statusText}\"`);\n    }\n\n    /** Called if an error event fires for xhr/xdr. */\n    private _xhrOnTimeout(): void\n    {\n        const xhr = this.xhr;\n\n        this.abort(`${reqType(xhr)} Request timed out.`);\n    }\n\n    /** Called if an abort event fires for xhr/xdr. */\n    private _xhrOnAbort(): void\n    {\n        const xhr = this.xhr;\n\n        this.abort(`${reqType(xhr)} Request was aborted by the user.`);\n    }\n\n    /** Called when data successfully loads from an xhr/xdr request. */\n    private _xhrOnLoad(): void\n    {\n        const xhr = this.xhr;\n        let text = '';\n        let status = typeof xhr.status === 'undefined' ? STATUS_OK : xhr.status; // XDR has no `.status`, assume 200.\n\n        // responseText is accessible only if responseType is '' or 'text' and on older browsers\n        if (xhr.responseType === '' || xhr.responseType === 'text' || typeof xhr.responseType === 'undefined')\n        {\n            text = xhr.responseText;\n        }\n\n        // status can be 0 when using the `file://` protocol so we also check if a response is set.\n        // If it has a response, we assume 200; otherwise a 0 status code with no contents is an aborted request.\n        if (status === STATUS_NONE && (text.length > 0 || xhr.responseType === LoaderResource.XHR_RESPONSE_TYPE.BUFFER))\n        {\n            status = STATUS_OK;\n        }\n        // handle IE9 bug: http://stackoverflow.com/questions/10046972/msie-returns-status-code-of-1223-for-ajax-request\n        else if (status === STATUS_IE_BUG_EMPTY)\n        {\n            status = STATUS_EMPTY;\n        }\n\n        const statusType = (status / 100) | 0;\n\n        if (statusType === STATUS_TYPE_OK)\n        {\n            // if text, just return it\n            if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.TEXT)\n            {\n                this.data = text;\n                this.type = LoaderResource.TYPE.TEXT;\n            }\n            // if json, parse into json object\n            else if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.JSON)\n            {\n                try\n                {\n                    this.data = JSON.parse(text);\n                    this.type = LoaderResource.TYPE.JSON;\n                }\n                catch (e)\n                {\n                    this.abort(`Error trying to parse loaded json: ${e}`);\n\n                    return;\n                }\n            }\n            // if xml, parse into an xml document or div element\n            else if (this.xhrType === LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT)\n            {\n                try\n                {\n                    if (globalThis.DOMParser)\n                    {\n                        const domparser = new DOMParser();\n\n                        this.data = domparser.parseFromString(text, 'text/xml');\n                    }\n                    else\n                    {\n                        const div = document.createElement('div');\n\n                        div.innerHTML = text;\n\n                        this.data = div;\n                    }\n\n                    this.type = LoaderResource.TYPE.XML;\n                }\n                catch (e)\n                {\n                    this.abort(`Error trying to parse loaded xml: ${e}`);\n\n                    return;\n                }\n            }\n            // other types just return the response\n            else\n            {\n                this.data = xhr.response || text;\n            }\n        }\n        else\n        {\n            this.abort(`[${xhr.status}] ${xhr.statusText}: ${xhr.responseURL}`);\n\n            return;\n        }\n\n        this.complete();\n    }\n\n    /**\n     * Sets the `crossOrigin` property for this resource based on if the url\n     * for this resource is cross-origin. If crossOrigin was manually set, this\n     * function does nothing.\n     * @private\n     * @param url - The url to test.\n     * @param [loc=globalThis.location] - The location object to test against.\n     * @returns The crossOrigin value to use (or empty string for none).\n     */\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    _determineCrossOrigin(url: string, loc?: any): string\n    {\n        // data: and javascript: urls are considered same-origin\n        if (url.indexOf('data:') === 0)\n        {\n            return '';\n        }\n\n        // A sandboxed iframe without the 'allow-same-origin' attribute will have a special\n        // origin designed not to match globalThis.location.origin, and will always require\n        // crossOrigin requests regardless of whether the location matches.\n        if (globalThis.origin !== globalThis.location.origin)\n        {\n            return 'anonymous';\n        }\n\n        // default is globalThis.location\n        loc = loc || globalThis.location;\n\n        if (!tempAnchor)\n        {\n            tempAnchor = document.createElement('a');\n        }\n\n        // let the browser determine the full href for the url of this resource and then\n        // parse with the node url lib, we can't use the properties of the anchor element\n        // because they don't work in IE9 :(\n        tempAnchor.href = url;\n        const parsedUrl = parseUri(tempAnchor.href, { strictMode: true });\n\n        const samePort = (!parsedUrl.port && loc.port === '') || (parsedUrl.port === loc.port);\n        const protocol = parsedUrl.protocol ? `${parsedUrl.protocol}:` : '';\n\n        // if cross origin\n        if (parsedUrl.host !== loc.hostname || !samePort || protocol !== loc.protocol)\n        {\n            return 'anonymous';\n        }\n\n        return '';\n    }\n\n    /**\n     * Determines the responseType of an XHR request based on the extension of the\n     * resource being loaded.\n     * @private\n     * @returns {PIXI.LoaderResource.XHR_RESPONSE_TYPE} The responseType to use.\n     */\n    private _determineXhrType(): LoaderResource.XHR_RESPONSE_TYPE\n    {\n        return LoaderResource._xhrTypeMap[this.extension] || LoaderResource.XHR_RESPONSE_TYPE.TEXT;\n    }\n\n    /**\n     * Determines the loadType of a resource based on the extension of the\n     * resource being loaded.\n     * @private\n     * @returns {PIXI.LoaderResource.LOAD_TYPE} The loadType to use.\n     */\n    private _determineLoadType(): LoaderResource.LOAD_TYPE\n    {\n        return LoaderResource._loadTypeMap[this.extension] || LoaderResource.LOAD_TYPE.XHR;\n    }\n\n    /**\n     * Extracts the extension (sans '.') of the file being loaded by the resource.\n     * @param [url] - url to parse, `this.url` by default.\n     * @returns The extension.\n     */\n    private _getExtension(url = this.url): string\n    {\n        let ext = '';\n\n        if (this.isDataUrl)\n        {\n            const slashIndex = url.indexOf('/');\n\n            ext = url.substring(slashIndex + 1, url.indexOf(';', slashIndex));\n        }\n        else\n        {\n            const queryStart = url.indexOf('?');\n            const hashStart = url.indexOf('#');\n            const index = Math.min(\n                queryStart > -1 ? queryStart : url.length,\n                hashStart > -1 ? hashStart : url.length\n            );\n\n            url = url.substring(0, index);\n            ext = url.substring(url.lastIndexOf('.') + 1);\n        }\n\n        return ext.toLowerCase();\n    }\n\n    /**\n     * Determines the mime type of an XHR request based on the responseType of\n     * resource being loaded.\n     * @param type - The type to get a mime type for.\n     * @private\n     * @returns The mime type to use.\n     */\n    _getMimeFromXhrType(type: LoaderResource.XHR_RESPONSE_TYPE): string\n    {\n        switch (type)\n        {\n            case LoaderResource.XHR_RESPONSE_TYPE.BUFFER:\n                return 'application/octet-binary';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.BLOB:\n                return 'application/blob';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT:\n                return 'application/xml';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.JSON:\n                return 'application/json';\n\n            case LoaderResource.XHR_RESPONSE_TYPE.DEFAULT:\n            case LoaderResource.XHR_RESPONSE_TYPE.TEXT:\n            /* falls through */\n            default:\n                return 'text/plain';\n        }\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace LoaderResource\n{\n    /**\n     * When the resource starts to load.\n     * @memberof PIXI.LoaderResource\n     * @callback OnStartSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n    export type OnStartSignal = (resource: LoaderResource) => void;\n    /**\n     * When the resource reports loading progress.\n     * @memberof PIXI.LoaderResource\n     * @callback OnProgressSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     * @param {number} percentage - The progress of the load in the range [0, 1].\n     */\n    export type OnProgressSignal = (resource: LoaderResource, percentage: number) => void;\n    /**\n     * When the resource finishes loading.\n     * @memberof PIXI.LoaderResource\n     * @callback OnCompleteSignal\n     * @param {PIXI.Resource} resource - The resource that the event happened on.\n     */\n    export type OnCompleteSignal = (resource: LoaderResource) => void;\n\n    /**\n     * The types of resources a resource could represent.\n     * @static\n     * @readonly\n     * @enum {number}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum STATUS_FLAGS\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** None */\n        NONE = 0,\n        /** Data URL */\n        DATA_URL = (1 << 0),\n        /** Complete */\n        COMPLETE = (1 << 1),\n        /** Loading */\n        LOADING = (1 << 2),\n    }\n\n    /**\n     * The types of resources a resource could represent.\n     * @static\n     * @readonly\n     * @enum {number}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum TYPE\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** Unknown */\n        UNKNOWN = 0,\n        /** JSON */\n        JSON = 1,\n        /** XML */\n        XML = 2,\n        /** Image */\n        IMAGE = 3,\n        /** Audio */\n        AUDIO = 4,\n        /** Video */\n        VIDEO = 5,\n        /** Plain text */\n        TEXT = 6,\n    }\n\n    /**\n     * The types of loading a resource can use.\n     * @static\n     * @readonly\n     * @enum {number}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum LOAD_TYPE\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** Uses XMLHttpRequest to load the resource. */\n        XHR = 1,\n        /** Uses an `Image` object to load the resource. */\n        IMAGE = 2,\n        /** Uses an `Audio` object to load the resource. */\n        AUDIO = 3,\n        /** Uses a `Video` object to load the resource. */\n        VIDEO = 4,\n    }\n\n    /**\n     * The XHR ready states, used internally.\n     * @static\n     * @readonly\n     * @enum {string}\n     * @memberof PIXI.LoaderResource\n     */\n    export enum XHR_RESPONSE_TYPE\n    // eslint-disable-next-line @typescript-eslint/indent\n    {\n        /** string */\n        DEFAULT = 'text',\n        /** ArrayBuffer */\n        BUFFER = 'arraybuffer',\n        /** Blob */\n        BLOB = 'blob',\n        /** Document */\n        DOCUMENT = 'document',\n        /** Object */\n        JSON = 'json',\n        /** String */\n        TEXT = 'text',\n    }\n\n    export const _loadTypeMap: Dict<number> = {\n        // images\n        gif: LoaderResource.LOAD_TYPE.IMAGE,\n        png: LoaderResource.LOAD_TYPE.IMAGE,\n        bmp: LoaderResource.LOAD_TYPE.IMAGE,\n        jpg: LoaderResource.LOAD_TYPE.IMAGE,\n        jpeg: LoaderResource.LOAD_TYPE.IMAGE,\n        tif: LoaderResource.LOAD_TYPE.IMAGE,\n        tiff: LoaderResource.LOAD_TYPE.IMAGE,\n        webp: LoaderResource.LOAD_TYPE.IMAGE,\n        tga: LoaderResource.LOAD_TYPE.IMAGE,\n        avif: LoaderResource.LOAD_TYPE.IMAGE,\n        svg: LoaderResource.LOAD_TYPE.IMAGE,\n        'svg+xml': LoaderResource.LOAD_TYPE.IMAGE, // for SVG data urls\n\n        // audio\n        mp3: LoaderResource.LOAD_TYPE.AUDIO,\n        ogg: LoaderResource.LOAD_TYPE.AUDIO,\n        wav: LoaderResource.LOAD_TYPE.AUDIO,\n\n        // videos\n        mp4: LoaderResource.LOAD_TYPE.VIDEO,\n        webm: LoaderResource.LOAD_TYPE.VIDEO,\n    };\n\n    export const _xhrTypeMap: Dict<XHR_RESPONSE_TYPE> = {\n        // xml\n        xhtml: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        html: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        htm: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        xml: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        tmx: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n        svg: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n\n        // This was added to handle Tiled Tileset XML, but .tsx is also a TypeScript React Component.\n        // Since it is way less likely for people to be loading TypeScript files instead of Tiled files,\n        // this should probably be fine.\n        tsx: LoaderResource.XHR_RESPONSE_TYPE.DOCUMENT,\n\n        // images\n        gif: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        png: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        bmp: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        jpg: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        jpeg: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        tif: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        tiff: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        webp: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        tga: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n        avif: LoaderResource.XHR_RESPONSE_TYPE.BLOB,\n\n        // json\n        json: LoaderResource.XHR_RESPONSE_TYPE.JSON,\n\n        // text\n        text: LoaderResource.XHR_RESPONSE_TYPE.TEXT,\n        txt: LoaderResource.XHR_RESPONSE_TYPE.TEXT,\n\n        // fonts\n        ttf: LoaderResource.XHR_RESPONSE_TYPE.BUFFER,\n        otf: LoaderResource.XHR_RESPONSE_TYPE.BUFFER,\n    };\n\n    // We can't set the `src` attribute to empty string, so on abort we set it to this 1px transparent gif\n    export const EMPTY_GIF = 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';\n}\n\nexport { LoaderResource };\n\n/** @deprecated - Use LoaderResource instead */\nexport type ILoaderResource = LoaderResource;\n", "/**\n * Smaller version of the async library constructs.\n * @ignore\n */\nfunction _noop(): void\n{ /* empty */\n}\n\n/**\n * Ensures a function is only called once.\n * @ignore\n * @param {Function} fn - The function to wrap.\n * @returns {Function} The wrapping function.\n */\nfunction onlyOnce(fn: () => void): () => void\n{\n    return function onceWrapper(this: any, ...args: any)\n    {\n        if (fn === null)\n        {\n            throw new Error('Callback was already called.');\n        }\n\n        const callFn = fn;\n\n        fn = null;\n        callFn.apply(this, args);\n    };\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IQueue\n{\n\n}\n\n/**\n * @private\n * @memberof PIXI\n */\nexport class AsyncQueueItem<TaskData>\n{\n    data: TaskData;\n    callback: (...args: any[]) => void;\n\n    /**\n     * @param data\n     * @param callback\n     * @private\n     */\n    constructor(data: TaskData, callback: (...args: any[]) => void)\n    {\n        this.data = data;\n        this.callback = callback;\n    }\n}\n\n/**\n * @private\n * @memberof PIXI\n */\nexport class AsyncQueue<TaskData>\n{\n    workers = 0;\n\n    concurrency: number;\n    buffer: number;\n\n    saturated: () => void = _noop;\n    unsaturated: () => void = _noop;\n    empty: () => void = _noop;\n    drain: () => void = _noop;\n    error: (err: Error, task: TaskData) => void = _noop;\n\n    started = false;\n    paused = false;\n\n    private _worker: (x: TaskData, next: () => void) => void;\n    _tasks: Array<AsyncQueueItem<TaskData>> = [];\n\n    /**\n     * @param worker\n     * @param concurrency\n     * @private\n     */\n    constructor(worker: (x: TaskData, next: () => void) => void, concurrency = 1)\n    {\n        this._worker = worker;\n\n        if (concurrency === 0)\n        {\n            throw new Error('Concurrency must not be zero');\n        }\n\n        this.concurrency = concurrency;\n        this.buffer = concurrency / 4.0;\n    }\n\n    private _insert = (data: any, insertAtFront: boolean, callback?: () => void) =>\n    {\n        if (callback && typeof callback !== 'function')\n        {\n            throw new Error('task callback must be a function');\n        }\n\n        this.started = true;\n\n        // eslint-disable-next-line no-eq-null,eqeqeq\n        if (data == null && this.idle())\n        {\n            // call drain immediately if there are no tasks\n            setTimeout(() => this.drain(), 1);\n\n            return;\n        }\n\n        const item = new AsyncQueueItem<TaskData>(\n            data,\n            typeof callback === 'function' ? callback : _noop\n        );\n\n        if (insertAtFront)\n        {\n            this._tasks.unshift(item);\n        }\n        else\n        {\n            this._tasks.push(item);\n        }\n\n        setTimeout(this.process, 1);\n    };\n\n    process = (): void =>\n    {\n        while (!this.paused && this.workers < this.concurrency && this._tasks.length)\n        {\n            const task = this._tasks.shift();\n\n            if (this._tasks.length === 0)\n            {\n                this.empty();\n            }\n\n            this.workers += 1;\n\n            if (this.workers === this.concurrency)\n            {\n                this.saturated();\n            }\n\n            this._worker(task.data, onlyOnce(this._next(task)));\n        }\n    };\n\n    /**\n     * @param task\n     * @private\n     */\n    _next(task: AsyncQueueItem<TaskData>): (...args: any) => void\n    {\n        return (...args: any) =>\n        {\n            this.workers -= 1;\n\n            task.callback(...args);\n\n            // eslint-disable-next-line no-eq-null,eqeqeq\n            if (args[0] != null)\n            {\n                this.error(args[0], task.data);\n            }\n\n            if (this.workers <= (this.concurrency - this.buffer))\n            {\n                this.unsaturated();\n            }\n\n            if (this.idle())\n            {\n                this.drain();\n            }\n\n            this.process();\n        };\n    }\n\n    // That was in object\n\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    push(data: any, callback?: (...args: any[]) => void): void\n    {\n        this._insert(data, false, callback);\n    }\n\n    kill(): void\n    {\n        this.workers = 0;\n        this.drain = _noop;\n        this.started = false;\n        this._tasks = [];\n    }\n\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    unshift(data: any, callback?: (...args: any[]) => void): void\n    {\n        this._insert(data, true, callback);\n    }\n\n    length(): number\n    {\n        return this._tasks.length;\n    }\n\n    running(): number\n    {\n        return this.workers;\n    }\n\n    idle(): boolean\n    {\n        return this._tasks.length + this.workers === 0;\n    }\n\n    pause(): void\n    {\n        if (this.paused === true)\n        {\n            return;\n        }\n\n        this.paused = true;\n    }\n\n    resume(): void\n    {\n        if (this.paused === false)\n        {\n            return;\n        }\n\n        this.paused = false;\n\n        // Need to call this.process once per concurrent\n        // worker to preserve full concurrency after pause\n        for (let w = 1; w <= this.concurrency; w++)\n        {\n            this.process();\n        }\n    }\n\n    /**\n     * Iterates an array in series.\n     * @param {Array.<*>} array - Array to iterate.\n     * @param {Function} iterator - Function to call for each element.\n     * @param {Function} callback - Function to call when done, or on error.\n     * @param {boolean} [deferNext=false] - Break synchronous each loop by calling next with a setTimeout of 1.\n     */\n    static eachSeries(array: Array<any>, iterator: (x: any, next: (err?: any) => void) => void,\n        callback?: (err?: any) => void, deferNext?: boolean): void\n    {\n        let i = 0;\n        const len = array.length;\n\n        function next(err?: any)\n        {\n            if (err || i === len)\n            {\n                if (callback)\n                {\n                    callback(err);\n                }\n\n                return;\n            }\n\n            if (deferNext)\n            {\n                setTimeout(() =>\n                {\n                    iterator(array[i++], next);\n                }, 1);\n            }\n            else\n            {\n                iterator(array[i++], next);\n            }\n        }\n\n        next();\n    }\n\n    /**\n     * Async queue implementation,\n     * @param {Function} worker - The worker function to call for each task.\n     * @param {number} concurrency - How many workers to run in parrallel.\n     * @returns {*} The async queue object.\n     */\n    static queue(worker: (x: any, next: (...args: any) => void) => void, concurrency?: number): AsyncQueue<any>\n    {\n        return new AsyncQueue<any>(worker, concurrency);\n    }\n}\n", "import { Signal } from './base/Signal';\nimport { parseUri } from './base/parseUri';\nimport type { IResourceMetadata } from './LoaderResource';\nimport { LoaderResource } from './LoaderResource';\nimport { AsyncQueue } from './base/AsyncQueue';\nimport type { Dict } from '@pixi/utils';\nimport { deprecation } from '@pixi/utils';\nimport { extensions, ExtensionType } from '@pixi/core';\n\n// some constants\nconst MAX_PROGRESS = 100;\nconst rgxExtractUrlHash = /(#[\\w-]+)?$/;\n\nexport type ILoaderMiddleware = (resource: LoaderResource, next: (...args: any[]) => void) => void;\n\nexport interface ILoaderAdd\n{\n    (this: Loader, name: string, url: string, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, name: string, url: string, options?: IAddOptions, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, url: string, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, url: string, options?: IAddOptions, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, options: IAddOptions, callback?: LoaderResource.OnCompleteSignal): Loader;\n    (this: Loader, resources: (IAddOptions | string)[], callback?: LoaderResource.OnCompleteSignal): Loader;\n}\n\n/**\n * Options for a call to `.add()`.\n * @see Loader#add\n * @property {string} name - The name of the resource to load, if not passed the url is used.\n * @property {string} key - Alias for `name`.\n * @property {string} url - The url for this resource, relative to the baseUrl of this loader.\n * @property {string|boolean} crossOrigin - Is this request cross-origin? Default is to determine automatically.\n * @property {number} [timeout=0] - A timeout in milliseconds for the load. If the load takes longer\n *      than this time it is cancelled and the load is considered a failure. If this value is\n *      set to `0` then there is no explicit timeout.\n * @property {LoaderResource.LOAD_TYPE} [loadType=LoaderResource.LOAD_TYPE.XHR] - How should this resource be loaded?\n * @property {LoaderResource.XHR_RESPONSE_TYPE} [xhrType=LoaderResource.XHR_RESPONSE_TYPE.DEFAULT] - How should the data\n *      being loaded be interpreted when using XHR?\n * @property {LoaderResource.OnCompleteSignal} onComplete - Callback to add an an onComplete signal istener.\n * @property {LoaderResource.OnCompleteSignal} callback - Alias for `onComplete`.\n * @property {IResourceMetadata} metadata - Extra configuration for middleware and the Resource object.\n */\nexport interface IAddOptions\n{\n    name?: string;\n    key?: string;\n    url?: string;\n    crossOrigin?: string | boolean;\n    timeout?: number;\n    parentResource?: LoaderResource;\n    loadType?: LoaderResource.LOAD_TYPE;\n    xhrType?: LoaderResource.XHR_RESPONSE_TYPE;\n    onComplete?: LoaderResource.OnCompleteSignal;\n    callback?: LoaderResource.OnCompleteSignal;\n    metadata?: IResourceMetadata;\n}\n\n/**\n * The new loader, forked from Resource Loader by Chad Engler: https://github.com/englercj/resource-loader\n *\n * ```js\n * const loader = PIXI.Loader.shared; // PixiJS exposes a premade instance for you to use.\n * // or\n * const loader = new PIXI.Loader(); // You can also create your own if you want\n *\n * const sprites = {};\n *\n * // Chainable `add` to enqueue a resource\n * loader.add('bunny', 'data/bunny.png')\n *       .add('spaceship', 'assets/spritesheet.json');\n * loader.add('scoreFont', 'assets/score.fnt');\n *\n * // Chainable `pre` to add a middleware that runs for each resource, *before* loading that resource.\n * // This is useful to implement custom caching modules (using filesystem, indexeddb, memory, etc).\n * loader.pre(cachingMiddleware);\n *\n * // Chainable `use` to add a middleware that runs for each resource, *after* loading that resource.\n * // This is useful to implement custom parsing modules (like spritesheet parsers, spine parser, etc).\n * loader.use(parsingMiddleware);\n *\n * // The `load` method loads the queue of resources, and calls the passed in callback called once all\n * // resources have loaded.\n * loader.load((loader, resources) => {\n *     // resources is an object where the key is the name of the resource loaded and the value is the resource object.\n *     // They have a couple default properties:\n *     // - `url`: The URL that the resource was loaded from\n *     // - `error`: The error that happened when trying to load (if any)\n *     // - `data`: The raw data that was loaded\n *     // also may contain other properties based on the middleware that runs.\n *     sprites.bunny = new PIXI.TilingSprite(resources.bunny.texture);\n *     sprites.spaceship = new PIXI.TilingSprite(resources.spaceship.texture);\n *     sprites.scoreFont = new PIXI.TilingSprite(resources.scoreFont.texture);\n * });\n *\n * // throughout the process multiple signals can be dispatched.\n * loader.onProgress.add(() => {}); // called once per loaded/errored file\n * loader.onError.add(() => {}); // called once per errored file\n * loader.onLoad.add(() => {}); // called once per loaded file\n * loader.onComplete.add(() => {}); // called once when the queued resources all load.\n * ```\n * @memberof PIXI\n */\nclass Loader\n{\n    /** The base url for all resources loaded by this loader. */\n    baseUrl: string;\n\n    /** The progress percent of the loader going through the queue. */\n    progress = 0;\n\n    /** Loading state of the loader, true if it is currently loading resources. */\n    loading = false;\n\n    /**\n     * A querystring to append to every URL added to the loader.\n     *\n     * This should be a valid query string *without* the question-mark (`?`). The loader will\n     * also *not* escape values for you. Make sure to escape your parameters with\n     * [`encodeURIComponent`](https://mdn.io/encodeURIComponent) before assigning this property.\n     * @example\n     * const loader = new Loader();\n     *\n     * loader.defaultQueryString = 'user=me&password=secret';\n     *\n     * // This will request 'image.png?user=me&password=secret'\n     * loader.add('image.png').load();\n     *\n     * loader.reset();\n     *\n     * // This will request 'image.png?v=1&user=me&password=secret'\n     * loader.add('iamge.png?v=1').load();\n     */\n    defaultQueryString = '';\n\n    /** The middleware to run before loading each resource. */\n    private _beforeMiddleware: Array<ILoaderMiddleware> = [];\n\n    /** The middleware to run after loading each resource. */\n    private _afterMiddleware: Array<ILoaderMiddleware> = [];\n\n    /** The tracks the resources we are currently completing parsing for. */\n    private _resourcesParsing: Array<LoaderResource> = [];\n\n    /**\n     * The `_loadResource` function bound with this object context.\n     * @param r - The resource to load\n     * @param d - The dequeue function\n     */\n    private _boundLoadResource = (r: LoaderResource, d: () => void): void => this._loadResource(r, d);\n\n    /** The resources waiting to be loaded. */\n    private _queue: AsyncQueue<any>;\n\n    /** All the resources for this loader keyed by name. */\n    resources: Dict<LoaderResource> = {};\n\n    /** Dispatched once per loaded or errored resource. */\n    onProgress: Signal<Loader.OnProgressSignal>;\n\n    /** Dispatched once per errored resource. */\n    onError: Signal<Loader.OnErrorSignal>;\n\n    /** Dispatched once per loaded resource. */\n    onLoad: Signal<Loader.OnLoadSignal>;\n\n    /** Dispatched when the loader begins to process the queue. */\n    onStart: Signal<Loader.OnStartSignal>;\n\n    /** Dispatched when the queued resources all load. */\n    onComplete: Signal<Loader.OnCompleteSignal>;\n\n    /**\n     * @param baseUrl - The base url for all resources loaded by this loader.\n     * @param concurrency - The number of resources to load concurrently.\n     */\n    constructor(baseUrl = '', concurrency = 10)\n    {\n        this.baseUrl = baseUrl;\n        this._beforeMiddleware = [];\n        this._afterMiddleware = [];\n        this._resourcesParsing = [];\n        this._boundLoadResource = (r, d) => this._loadResource(r, d);\n        this._queue = AsyncQueue.queue(this._boundLoadResource, concurrency);\n        this._queue.pause();\n        this.resources = {};\n        this.onProgress = new Signal();\n        this.onError = new Signal();\n        this.onLoad = new Signal();\n        this.onStart = new Signal();\n        this.onComplete = new Signal();\n\n        for (let i = 0; i < Loader._plugins.length; ++i)\n        {\n            const plugin = Loader._plugins[i];\n            const { pre, use } = plugin;\n\n            if (pre)\n            {\n                this.pre(pre);\n            }\n\n            if (use)\n            {\n                this.use(use);\n            }\n        }\n\n        this._protected = false;\n    }\n\n    /**\n     * Adds a resource (or multiple resources) to the loader queue.\n     *\n     * This function can take a wide variety of different parameters. The only thing that is always\n     * required the url to load. All the following will work:\n     *\n     * ```js\n     * loader\n     *     // normal param syntax\n     *     .add('key', 'http://...', function () {})\n     *     .add('http://...', function () {})\n     *     .add('http://...')\n     *\n     *     // object syntax\n     *     .add({\n     *         name: 'key2',\n     *         url: 'http://...'\n     *     }, function () {})\n     *     .add({\n     *         url: 'http://...'\n     *     }, function () {})\n     *     .add({\n     *         name: 'key3',\n     *         url: 'http://...'\n     *         onComplete: function () {}\n     *     })\n     *     .add({\n     *         url: 'https://...',\n     *         onComplete: function () {},\n     *         crossOrigin: true\n     *     })\n     *\n     *     // you can also pass an array of objects or urls or both\n     *     .add([\n     *         { name: 'key4', url: 'http://...', onComplete: function () {} },\n     *         { url: 'http://...', onComplete: function () {} },\n     *         'http://...'\n     *     ])\n     *\n     *     // and you can use both params and options\n     *     .add('key', 'http://...', { crossOrigin: true }, function () {})\n     *     .add('http://...', { crossOrigin: true }, function () {});\n     * ```\n     */\n    add: ILoaderAdd;\n\n    /**\n     * Same as add, params have strict order\n     * @private\n     * @param name - The name of the resource to load.\n     * @param url - The url for this resource, relative to the baseUrl of this loader.\n     * @param options - The options for the load.\n     * @param callback - Function to call when this specific resource completes loading.\n     * @returns The loader itself.\n     */\n    protected _add(name: string, url: string, options: IAddOptions, callback?: LoaderResource.OnCompleteSignal): this\n    {\n        // if loading already you can only add resources that have a parent.\n        if (this.loading && (!options || !options.parentResource))\n        {\n            throw new Error('Cannot add resources while the loader is running.');\n        }\n\n        // check if resource already exists.\n        if (this.resources[name])\n        {\n            throw new Error(`Resource named \"${name}\" already exists.`);\n        }\n\n        // add base url if this isn't an absolute url\n        url = this._prepareUrl(url);\n\n        // create the store the resource\n        this.resources[name] = new LoaderResource(name, url, options);\n\n        if (typeof callback === 'function')\n        {\n            this.resources[name].onAfterMiddleware.once(callback);\n        }\n\n        // if actively loading, make sure to adjust progress chunks for that parent and its children\n        if (this.loading)\n        {\n            const parent = options.parentResource;\n            const incompleteChildren = [];\n\n            for (let i = 0; i < parent.children.length; ++i)\n            {\n                if (!parent.children[i].isComplete)\n                {\n                    incompleteChildren.push(parent.children[i]);\n                }\n            }\n\n            const fullChunk = parent.progressChunk * (incompleteChildren.length + 1); // +1 for parent\n            const eachChunk = fullChunk / (incompleteChildren.length + 2); // +2 for parent & new child\n\n            parent.children.push(this.resources[name]);\n            parent.progressChunk = eachChunk;\n\n            for (let i = 0; i < incompleteChildren.length; ++i)\n            {\n                incompleteChildren[i].progressChunk = eachChunk;\n            }\n\n            this.resources[name].progressChunk = eachChunk;\n        }\n\n        // add the resource to the queue\n        this._queue.push(this.resources[name]);\n\n        return this;\n    }\n\n    /* eslint-enable require-jsdoc,valid-jsdoc */\n\n    /**\n     * Sets up a middleware function that will run *before* the\n     * resource is loaded.\n     * @param fn - The middleware function to register.\n     * @returns The loader itself.\n     */\n    pre(fn: ILoaderMiddleware): this\n    {\n        this._beforeMiddleware.push(fn);\n\n        return this;\n    }\n\n    /**\n     * Sets up a middleware function that will run *after* the\n     * resource is loaded.\n     * @param fn - The middleware function to register.\n     * @returns The loader itself.\n     */\n    use(fn: ILoaderMiddleware): this\n    {\n        this._afterMiddleware.push(fn);\n\n        return this;\n    }\n\n    /**\n     * Resets the queue of the loader to prepare for a new load.\n     * @returns The loader itself.\n     */\n    reset(): this\n    {\n        this.progress = 0;\n        this.loading = false;\n\n        this._queue.kill();\n        this._queue.pause();\n\n        // abort all resource loads\n        for (const k in this.resources)\n        {\n            const res = this.resources[k];\n\n            if (res._onLoadBinding)\n            {\n                res._onLoadBinding.detach();\n            }\n\n            if (res.isLoading)\n            {\n                res.abort('loader reset');\n            }\n        }\n\n        this.resources = {};\n\n        return this;\n    }\n\n    /**\n     * Starts loading the queued resources.\n     * @param cb - Optional callback that will be bound to the `complete` event.\n     * @returns The loader itself.\n     */\n    load(cb?: Loader.OnCompleteSignal): this\n    {\n        // #if _DEBUG\n        deprecation('6.5.0', '@pixi/loaders is being replaced with @pixi/assets in the next major release.');\n        // #endif\n\n        // register complete callback if they pass one\n        if (typeof cb === 'function')\n        {\n            this.onComplete.once(cb);\n        }\n\n        // if the queue has already started we are done here\n        if (this.loading)\n        {\n            return this;\n        }\n\n        if (this._queue.idle())\n        {\n            this._onStart();\n            this._onComplete();\n        }\n        else\n        {\n            // distribute progress chunks\n            const numTasks = this._queue._tasks.length;\n            const chunk = MAX_PROGRESS / numTasks;\n\n            for (let i = 0; i < this._queue._tasks.length; ++i)\n            {\n                this._queue._tasks[i].data.progressChunk = chunk;\n            }\n\n            // notify we are starting\n            this._onStart();\n\n            // start loading\n            this._queue.resume();\n        }\n\n        return this;\n    }\n\n    /**\n     * The number of resources to load concurrently.\n     * @default 10\n     */\n    get concurrency(): number\n    {\n        return this._queue.concurrency;\n    }\n    set concurrency(concurrency: number)\n    {\n        this._queue.concurrency = concurrency;\n    }\n\n    /**\n     * Prepares a url for usage based on the configuration of this object\n     * @param url - The url to prepare.\n     * @returns The prepared url.\n     */\n    private _prepareUrl(url: string): string\n    {\n        const parsedUrl = parseUri(url, { strictMode: true });\n        let result;\n\n        // absolute url, just use it as is.\n        if (parsedUrl.protocol || !parsedUrl.path || url.indexOf('//') === 0)\n        {\n            result = url;\n        }\n        // if baseUrl doesn't end in slash and url doesn't start with slash, then add a slash inbetween\n        else if (this.baseUrl.length\n            && this.baseUrl.lastIndexOf('/') !== this.baseUrl.length - 1\n            && url.charAt(0) !== '/'\n        )\n        {\n            result = `${this.baseUrl}/${url}`;\n        }\n        else\n        {\n            result = this.baseUrl + url;\n        }\n\n        // if we need to add a default querystring, there is a bit more work\n        if (this.defaultQueryString)\n        {\n            const hash = rgxExtractUrlHash.exec(result)[0];\n\n            result = result.slice(0, result.length - hash.length);\n\n            if (result.indexOf('?') !== -1)\n            {\n                result += `&${this.defaultQueryString}`;\n            }\n            else\n            {\n                result += `?${this.defaultQueryString}`;\n            }\n\n            result += hash;\n        }\n\n        return result;\n    }\n\n    /**\n     * Loads a single resource.\n     * @param resource - The resource to load.\n     * @param dequeue - The function to call when we need to dequeue this item.\n     */\n    private _loadResource(resource: LoaderResource, dequeue: () => void): void\n    {\n        resource._dequeue = dequeue;\n\n        // run before middleware\n        AsyncQueue.eachSeries(\n            this._beforeMiddleware,\n            (fn: any, next: (...args: any) => void) =>\n            {\n                fn.call(this, resource, () =>\n                {\n                    // if the before middleware marks the resource as complete,\n                    // break and don't process any more before middleware\n                    next(resource.isComplete ? {} : null);\n                });\n            },\n            () =>\n            {\n                if (resource.isComplete)\n                {\n                    this._onLoad(resource);\n                }\n                else\n                {\n                    resource._onLoadBinding = resource.onComplete.once(this._onLoad, this);\n                    resource.load();\n                }\n            },\n            true\n        );\n    }\n\n    /** Called once loading has started. */\n    private _onStart(): void\n    {\n        this.progress = 0;\n        this.loading = true;\n        this.onStart.dispatch(this);\n    }\n\n    /** Called once each resource has loaded. */\n    private _onComplete(): void\n    {\n        this.progress = MAX_PROGRESS;\n        this.loading = false;\n        this.onComplete.dispatch(this, this.resources);\n    }\n\n    /**\n     * Called each time a resources is loaded.\n     * @param resource - The resource that was loaded\n     */\n    private _onLoad(resource: LoaderResource): void\n    {\n        resource._onLoadBinding = null;\n\n        // remove this resource from the async queue, and add it to our list of resources that are being parsed\n        this._resourcesParsing.push(resource);\n        resource._dequeue();\n\n        // run all the after middleware for this resource\n        AsyncQueue.eachSeries(\n            this._afterMiddleware,\n            (fn: any, next: any) =>\n            {\n                fn.call(this, resource, next);\n            },\n            () =>\n            {\n                resource.onAfterMiddleware.dispatch(resource);\n\n                this.progress = Math.min(MAX_PROGRESS, this.progress + resource.progressChunk);\n                this.onProgress.dispatch(this, resource);\n\n                if (resource.error)\n                {\n                    this.onError.dispatch(resource.error, this, resource);\n                }\n                else\n                {\n                    this.onLoad.dispatch(this, resource);\n                }\n\n                this._resourcesParsing.splice(this._resourcesParsing.indexOf(resource), 1);\n\n                // do completion check\n                if (this._queue.idle() && this._resourcesParsing.length === 0)\n                {\n                    this._onComplete();\n                }\n            },\n            true\n        );\n    }\n\n    static _plugins: Array<ILoaderPlugin> = [];\n    private static _shared: Loader;\n    /**\n     * If this loader cannot be destroyed.\n     * @default false\n     */\n    private _protected: boolean;\n\n    /** Destroy the loader, removes references. */\n    public destroy(): void\n    {\n        if (!this._protected)\n        {\n            this.reset();\n        }\n    }\n\n    /** A premade instance of the loader that can be used to load resources. */\n    public static get shared(): Loader\n    {\n        let shared = Loader._shared;\n\n        if (!shared)\n        {\n            shared = new Loader();\n            shared._protected = true;\n            Loader._shared = shared;\n        }\n\n        return shared;\n    }\n\n    /**\n     * Use the {@link PIXI.extensions.add} API to register plugins.\n     * @deprecated since 6.5.0\n     * @param plugin - The plugin to add\n     * @returns Reference to PIXI.Loader for chaining\n     */\n    public static registerPlugin(plugin: ILoaderPlugin): typeof Loader\n    {\n        // #if _DEBUG\n        deprecation('6.5.0', 'Loader.registerPlugin() is deprecated, use extensions.add() instead.');\n        // #endif\n\n        extensions.add({\n            type: ExtensionType.Loader,\n            ref: plugin,\n        });\n\n        return Loader;\n    }\n}\n\nextensions.handleByList(ExtensionType.Loader, Loader._plugins);\n\nLoader.prototype.add = function add(this: Loader, name: any, url?: any, options?: any, callback?: any): Loader\n{\n    // special case of an array of objects or urls\n    if (Array.isArray(name))\n    {\n        for (let i = 0; i < name.length; ++i)\n        {\n            this.add((name as any)[i]);\n        }\n\n        return this;\n    }\n\n    // if an object is passed instead of params\n    if (typeof name === 'object')\n    {\n        options = name;\n        callback = (url as any) || options.callback || options.onComplete;\n        url = options.url;\n        name = options.name || options.key || options.url;\n    }\n\n    // case where no name is passed shift all args over by one.\n    if (typeof url !== 'string')\n    {\n        callback = options as any;\n        options = url;\n        url = name;\n    }\n\n    // now that we shifted make sure we have a proper url.\n    if (typeof url !== 'string')\n    {\n        throw new Error('No url passed to add resource to loader.');\n    }\n\n    // options are optional so people might pass a function and no options\n    if (typeof options === 'function')\n    {\n        callback = options;\n        options = null;\n    }\n\n    return this._add(name, url, options, callback);\n};\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Loader\n{\n    /**\n     * When the resource starts to load.\n     * @param resource - The resource that the event happened on.\n     */\n    export type OnStartSignal = (loader: Loader) => void;\n    /**\n     * When the progress changes the loader and resource are dispatched.\n     * @param loader - The loader the progress is advancing on.\n     * @param resource - The resource that has completed or failed to cause the progress to advance.\n     */\n    export type OnProgressSignal = (loader: Loader, resource: LoaderResource) => void;\n    /**\n     * When a load completes without error the loader and resource are dispatched.\n     * @param loader - The loader that has started loading resources.\n     * @param resource - The resource that has completed.\n     */\n    export type OnLoadSignal = (loader: Loader, resource: LoaderResource) => void;\n    /**\n     * When the loader starts loading resources it dispatches this callback.\n     * @param loader - The loader that has started loading resources.\n     */\n    export type OnCompleteSignal = (loader: Loader, resources: Dict<LoaderResource>) => void;\n    /**\n     * When an error occurs the loader and resource are dispatched.\n     * @param loader - The loader the error happened in.\n     * @param resource - The resource that caused the error.\n     */\n    export type OnErrorSignal = (error: Error, loader: Loader, resource: LoaderResource) => void;\n}\n\nexport { Loader };\n\n/**\n * Plugin to be installed for handling specific Loader resources.\n * @property {Function} add - Function to call immediate after registering plugin.\n * @property {Function} pre - Middleware function to run before load, the\n *           arguments for this are `(resource, next)`\n * @property {Function} use - Middleware function to run after load, the\n *           arguments for this are `(resource, next)`\n */\nexport interface ILoaderPlugin\n{\n    /** Function to call immediate after registering plugin. */\n    add?(): void;\n\n    /**\n     * Middleware function to run before load\n     * @param {LoaderResource} resource - resource\n     * @param {LoaderResource} next - next middleware\n     */\n    pre?(resource: LoaderResource, next: (...args: any[]) => void): void;\n\n    /**\n     * Middleware function to run after load\n     * @param {LoaderResource} resource - resource\n     * @param {LoaderResource} next - next middleware\n     */\n    use?(resource: LoaderResource, next: (...args: any[]) => void): void;\n}\n", "import type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport { Loader } from './Loader';\n\n/**\n * Application plugin for supporting loader option. Installing the LoaderPlugin\n * is not necessary if using **pixi.js** or **pixi.js-legacy**.\n * @example\n * import {AppLoaderPlugin} from '@pixi/loaders';\n * import {extensions} from '@pixi/core';\n * extensions.add(AppLoaderPlugin);\n * @memberof PIXI\n */\nexport class AppLoaderPlugin\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Application;\n\n    /**\n     * Loader instance to help with asset loading.\n     * @memberof PIXI.Application#\n     * @readonly\n     */\n    public static loader: Loader;\n\n    /**\n     * Called on application constructor\n     * @param options\n     * @private\n     */\n    static init(options?: GlobalMixins.IApplicationOptions): void\n    {\n        options = Object.assign({\n            sharedLoader: false,\n        }, options);\n\n        this.loader = options.sharedLoader ? Loader.shared : new Loader();\n    }\n\n    /**\n     * Called when application destroyed\n     * @private\n     */\n    static destroy(): void\n    {\n        if (this.loader)\n        {\n            this.loader.destroy();\n            this.loader = null;\n        }\n    }\n}\n", "import type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType, Texture } from '@pixi/core';\nimport { LoaderResource } from './LoaderResource';\n\n/**\n * Loader plugin for handling Texture resources.\n * @memberof PIXI\n */\nexport class TextureLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    /** Handle SVG elements a text, render with SVGResource. */\n    public static add(): void\n    {\n        LoaderResource.setExtensionLoadType('svg', LoaderResource.LOAD_TYPE.XHR);\n        LoaderResource.setExtensionXhrType('svg', LoaderResource.XHR_RESPONSE_TYPE.TEXT);\n    }\n\n    /**\n     * Called after a resource is loaded.\n     * @see PIXI.Loader.loaderMiddleware\n     * @param resource\n     * @param {Function} next\n     */\n    public static use(resource: LoaderResource, next: (...args: any[]) => void): void\n    {\n        // create a new texture if the data is an Image object\n        if (resource.data && (resource.type === LoaderResource.TYPE.IMAGE || resource.extension === 'svg'))\n        {\n            const { data, url, name, metadata } = resource;\n\n            Texture.fromLoader(data, url, name, metadata).then((texture) =>\n            {\n                resource.texture = texture;\n                next();\n            })\n            // TODO: handle errors in Texture.fromLoader\n            // so we can pass them to the Loader\n                .catch(next);\n        }\n        else\n        {\n            next();\n        }\n    }\n}\n", "const _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n\n/**\n * Encodes binary into base64.\n * @function encodeBinary\n * @param {string} input - The input data to encode.\n * @returns {string} The encoded base64 string\n */\nexport function encodeBinary(input: string): string\n{\n    let output = '';\n    let inx = 0;\n\n    while (inx < input.length)\n    {\n        // Fill byte buffer array\n        const bytebuffer = [0, 0, 0];\n        const encodedCharIndexes = [0, 0, 0, 0];\n\n        for (let jnx = 0; jnx < bytebuffer.length; ++jnx)\n        {\n            if (inx < input.length)\n            {\n                // throw away high-order byte, as documented at:\n                // https://developer.mozilla.org/En/Using_XMLHttpRequest#Handling_binary_data\n                bytebuffer[jnx] = input.charCodeAt(inx++) & 0xff;\n            }\n            else\n            {\n                bytebuffer[jnx] = 0;\n            }\n        }\n\n        // Get each encoded character, 6 bits at a time\n        // index 1: first 6 bits\n        encodedCharIndexes[0] = bytebuffer[0] >> 2;\n\n        // index 2: second 6 bits (2 least significant bits from input byte 1 + 4 most significant bits from byte 2)\n        encodedCharIndexes[1] = ((bytebuffer[0] & 0x3) << 4) | (bytebuffer[1] >> 4);\n\n        // index 3: third 6 bits (4 least significant bits from input byte 2 + 2 most significant bits from byte 3)\n        encodedCharIndexes[2] = ((bytebuffer[1] & 0x0f) << 2) | (bytebuffer[2] >> 6);\n\n        // index 3: forth 6 bits (6 least significant bits from input byte 3)\n        encodedCharIndexes[3] = bytebuffer[2] & 0x3f;\n\n        // Determine whether padding happened, and adjust accordingly\n        const paddingBytes = inx - (input.length - 1);\n\n        switch (paddingBytes)\n        {\n            case 2:\n                // Set last 2 characters to padding char\n                encodedCharIndexes[3] = 64;\n                encodedCharIndexes[2] = 64;\n                break;\n\n            case 1:\n                // Set last character to padding char\n                encodedCharIndexes[3] = 64;\n                break;\n\n            default:\n                break; // No padding - proceed\n        }\n\n        // Now we will grab each appropriate character out of our keystring\n        // based on our index array and append it to the output string\n        for (let jnx = 0; jnx < encodedCharIndexes.length; ++jnx)\n        {\n            output += _keyStr.charAt(encodedCharIndexes[jnx]);\n        }\n    }\n\n    return output;\n}\n", "import { LoaderResource } from '../LoaderResource';\nimport { encodeBinary } from '../base/encodeBinary';\n\n/**\n * A middleware for transforming XHR loaded Blobs into more useful objects\n * @ignore\n * @function parsing\n * @example\n * import { Loader, middleware } from 'resource-loader';\n * const loader = new Loader();\n * loader.use(middleware.parsing);\n * @param resource - Current Resource\n * @param next - Callback when complete\n */\nexport function parsing(resource: LoaderResource, next: (...args: any) => void): void\n{\n    if (!resource.data)\n    {\n        next();\n\n        return;\n    }\n\n    // if this was an XHR load of a blob\n    if (resource.xhr && resource.xhrType === LoaderResource.XHR_RESPONSE_TYPE.BLOB)\n    {\n        // if there is no blob support we probably got a binary string back\n        if (!self.Blob || typeof resource.data === 'string')\n        {\n            const type = resource.xhr.getResponseHeader('content-type');\n\n            // this is an image, convert the binary string into a data url\n            if (type && type.indexOf('image') === 0)\n            {\n                resource.data = new Image();\n                resource.data.src = `data:${type};base64,${encodeBinary(resource.xhr.responseText)}`;\n\n                resource.type = LoaderResource.TYPE.IMAGE;\n\n                // wait until the image loads and then callback\n                resource.data.onload = () =>\n                {\n                    resource.data.onload = null;\n\n                    next();\n                };\n\n                // next will be called on load\n                return;\n            }\n        }\n        // if content type says this is an image, then we should transform the blob into an Image object\n        else if (resource.data.type.indexOf('image') === 0)\n        {\n            const Url = globalThis.URL || globalThis.webkitURL;\n            const src = Url.createObjectURL(resource.data);\n\n            resource.blob = resource.data;\n            resource.data = new Image();\n            resource.data.src = src;\n\n            resource.type = LoaderResource.TYPE.IMAGE;\n\n            // cleanup the no longer used blob after the image loads\n            // TODO: Is this correct? Will the image be invalid after revoking?\n            resource.data.onload = () =>\n            {\n                Url.revokeObjectURL(src);\n                resource.data.onload = null;\n\n                next();\n            };\n\n            // next will be called on load.\n            return;\n        }\n    }\n\n    next();\n}\n", "import { parsing } from './middleware';\nimport type { ExtensionMetadata } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\n\n/**\n * Parse any blob into more usable objects (e.g. Image).\n * @memberof PIXI\n */\nclass ParsingLoader\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Loader;\n\n    static use = parsing;\n}\n\nexport { ParsingLoader };\n", "export * from './AppLoaderPlugin';\nexport * from './LoaderResource';\nexport * from './Loader';\nexport * from './TextureLoader';\n\nimport { ParsingLoader } from './ParsingLoader';\nimport { TextureLoader } from './TextureLoader';\nimport { extensions } from '@pixi/core';\n\nextensions.add(\n    TextureLoader,\n    ParsingLoader\n);\n"], "names": ["arguments", "_noop", "e"], "mappings": ";;;;;;;;;;AAAA;AAEA;;AAEG;AACH,IAAA,aAAA,kBAAA,YAAA;AASI;;;;;;;AAOG;;AAEH,IAAA,SAAA,aAAA,CAAY,EAAU,EAAE,IAAY,EAAE,OAAY,EAAA;AAA1B,QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAY,GAAA,KAAA,CAAA,EAAA;AAEhC,QAAA,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AACxB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KAChD;AAED,IAAA,aAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;AAAE,YAAA,EAAA,OAAO,KAAK,CAAC,EAAA;AACvC,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEzB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;AAIG;AACH,SAAS,iBAAiB,CAAS,IAAoB,EAAE,IAA2B,EAAA;AAEhF,IAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EACf;AACI,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,KAAA;AAED,SAAA;AACI,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACxB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,KAAA;AAED,IAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAEnB,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;AAEG;AACH,IAAA,MAAA,kBAAA,YAAA;AAKI;;;;;;;AAOG;AACH,IAAA,SAAA,MAAA,GAAA;QAEI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;KACvC;AAED;;;;;AAKG;IACH,MAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,MAAc,EAAA;AAAd,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAc,GAAA,KAAA,CAAA,EAAA;AAEnB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAEtB,QAAA,IAAI,MAAM;cAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAA;QAE1B,IAAM,EAAE,GAAG,EAAE,CAAC;AAEd,QAAA,OAAO,IAAI,EACX;AACI,YAAA,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACd,YAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AACrB,SAAA;AAED,QAAA,OAAO,EAAE,CAAC;KACb,CAAA;AAED;;;;AAIG;IACH,MAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,IAA2B,EAAA;AAE3B,QAAA,IAAI,EAAE,IAAI,YAAY,aAAa,CAAC,EACpC;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;AAClF,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC;KAC/B,CAAA;AAED;;;;AAIG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;;AAAA;QAAS,IAAc,IAAA,GAAA,EAAA,CAAA;aAAd,IAAc,EAAA,GAAA,CAAA,EAAd,EAAc,GAAA,SAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAA;YAAd,IAAc,CAAA,EAAA,CAAA,GAAAA,WAAA,CAAA,EAAA,CAAA,CAAA;;AAEnB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAEtB,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,EAAA,OAAO,KAAK,CAAC,EAAA;AAExB,QAAA,OAAO,IAAI,EACX;YACI,IAAI,IAAI,CAAC,KAAK;AAAE,gBAAA,EAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAA;YAClC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACpC,YAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AACrB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,EAAU,EAAE,OAAmB,EAAA;AAAnB,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAmB,GAAA,IAAA,CAAA,EAAA;AAE/B,QAAA,IAAI,OAAO,EAAE,KAAK,UAAU,EAC5B;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACtE,SAAA;AAED,QAAA,OAAO,iBAAiB,CAAS,IAAI,EAAE,IAAI,aAAa,CAAS,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;KACzF,CAAA;AAED;;;;;AAKG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,EAAU,EAAE,OAAmB,EAAA;AAAnB,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAmB,GAAA,IAAA,CAAA,EAAA;AAEhC,QAAA,IAAI,OAAO,EAAE,KAAK,UAAU,EAC5B;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACvE,SAAA;AAED,QAAA,OAAO,iBAAiB,CAAS,IAAI,EAAE,IAAI,aAAa,CAAS,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;KACxF,CAAA;AAED;;;;AAIgB;IAChB,MAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,IAA2B,EAAA;AAE9B,QAAA,IAAI,EAAE,IAAI,YAAY,aAAa,CAAC,EACpC;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;AACrF,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;YAAE,EAAA,OAAO,IAAI,CAAC,EAAA;QAEtC,IAAI,IAAI,CAAC,KAAK;YAAE,EAAA,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAA;QAC9C,IAAI,IAAI,CAAC,KAAK;YAAE,EAAA,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAA;AAE9C,QAAA,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EACvB;AACI,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACxB,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EACvB;AACI,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,aAAA;AACJ,SAAA;AACI,aAAA,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EAC5B;AACI,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACxB,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAEnB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;AAEI,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAEtB,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,EAAA,OAAO,IAAI,CAAC,EAAA;QAEvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAE/B,QAAA,OAAO,IAAI,EACX;AACI,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,YAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AACrB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IACL,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACvOD;;;;;AAKG;AACa,SAAA,QAAQ,CAAC,GAAW,EAAE,IAA8B,EAAA;AAEhE,IAAA,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AAElB,IAAA,IAAM,CAAC,GAAG;;AAEN,QAAA,GAAG,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;AACpJ,QAAA,CAAC,EAAE;AACC,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,MAAM,EAAE,2BAA2B;AACtC,SAAA;AACD,QAAA,MAAM,EAAE;;AAEJ,YAAA,MAAM,EAAE,yIAAyI;;AAEjJ,YAAA,KAAK,EAAE,kMAAkM;AAC5M,SAAA;KACJ,CAAC;IAEF,IAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnE,IAAM,GAAG,GAAQ,EAAE,CAAC;IACpB,IAAI,CAAC,GAAG,EAAE,CAAC;AAEX,IAAA,OAAO,CAAC,EAAE;AAAE,QAAA,EAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAA;IAEvC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;IACnB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,UAAC,GAAQ,EAAE,EAAO,EAAE,EAAO,EAAA;AAE1D,QAAA,IAAI,EAAE;AAAE,YAAA,EAAA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAA;AACnC,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,GAAG,CAAC;AACf;;ACjCA;AACA,IAAI,MAAe,CAAC;AACpB,IAAI,UAAU,GAAQ,IAAI,CAAC;AAE3B;AACA,IAAM,WAAW,GAAG,CAAC,CAAC;AACtB,IAAM,SAAS,GAAG,GAAG,CAAC;AACtB,IAAM,YAAY,GAAG,GAAG,CAAC;AACzB,IAAM,mBAAmB,GAAG,IAAI,CAAC;AACjC,IAAM,cAAc,GAAG,CAAC,CAAC;AAEzB;AACA,SAASC,OAAK,MAAwB;AAEtC;;;;;;;AAOG;AACH,SAAS,SAAS,CAAC,GAAc,EAAE,OAAe,EAAE,GAAW,EAAA;IAE3D,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EACzC;AACI,QAAA,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAClC,KAAA;IAED,IAAI,CAAC,OAAO,EACZ;QACI,OAAO;AACV,KAAA;AAED,IAAA,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;AACvB,CAAC;AAED;;;;;AAKG;AACH,SAAS,OAAO,CAAC,GAAmB,EAAA;IAEhC,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACjD,CAAC;AAkCD;;;;;AAKG;AACH,IAAA,cAAA,kBAAA,YAAA;AA8MI;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,SAAA,cAAA,CAAY,IAAY,EAAE,GAAsB,EAAE,OAMjD,EAAA;AAlGD;;;;;AAKG;QACH,IAAQ,CAAA,QAAA,GAAQA,OAAK,CAAC;AAEtB;;;;AAIG;QACH,IAAc,CAAA,cAAA,GAAQ,IAAI,CAAC;AAE3B;;;AAGG;QACK,IAAa,CAAA,aAAA,GAAG,CAAC,CAAC;AAE1B;;;;AAIG;QACK,IAAc,CAAA,cAAA,GAAQ,IAAI,CAAC;AAEnC;;;;AAIG;QACK,IAAa,CAAA,aAAA,GAAQ,IAAI,CAAC;AAElC;;;;AAIG;QACK,IAAgB,CAAA,gBAAA,GAAQ,IAAI,CAAC;AAErC;;;;AAIG;QACK,IAAe,CAAA,eAAA,GAAQ,IAAI,CAAC;QAE5B,IAAgB,CAAA,gBAAA,GAAQ,IAAI,CAAC;QAC7B,IAAkB,CAAA,kBAAA,GAAQ,IAAI,CAAC;QAC/B,IAAgB,CAAA,gBAAA,GAAQ,IAAI,CAAC;QAC7B,IAAe,CAAA,eAAA,GAAQ,IAAI,CAAC;QAgDhC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,EACvD;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;AAClF,SAAA;AAED,QAAA,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAExB,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;;AAGhB,QAAA,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAEhF,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAEjB,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAEf,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AAEtC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAEjB,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,KAAK,IAAI,GAAG,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAEpF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;QAEpC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;;AAG9D,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;;;;QAK/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;;AAGvC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;;AAIlB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;;AAGhB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;;QAGnB,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGxC,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;;;AAIvB,QAAA,IAAI,CAAC,QAAQ,GAAGA,OAAK,CAAC;;AAGtB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;AAG3B,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QAEvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;QAGlD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;AAGlD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,EAAE,CAAC;;;;;;AAO5B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;;;AAI/B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;;AAG/B,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,MAAM,EAAE,CAAC;KACzC;AArID;;;;;AAKG;AACI,IAAA,cAAA,CAAA,oBAAoB,GAA3B,UAA4B,OAAe,EAAE,QAAkC,EAAA;QAE3E,SAAS,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC7D,CAAA;AACD;;;;;AAKG;AACI,IAAA,cAAA,CAAA,mBAAmB,GAA1B,UAA2B,OAAe,EAAE,OAAyC,EAAA;QAEjF,SAAS,CAAC,cAAc,CAAC,WAAW,EAAE,OAAO,EAAE,OAAc,CAAC,CAAC;KAClE,CAAA;AA2JD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AAvCb;;;;;AAKG;AAEH;;;;;;AAMG;AAEH;;;;;AAKG;AAEH;;;;;;;;;;AAUG;AAEH;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC9D;;;AAAA,KAAA,CAAA,CAAA;AAQD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AANd;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC9D;;;AAAA,KAAA,CAAA,CAAA;AAQD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AANb;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC7D;;;AAAA,KAAA,CAAA,CAAA;;AAGD,IAAA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QAEI,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,OAAO,EAAE,CAAC;KAClB,CAAA;AAED;;;AAGG;IACH,cAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,OAAe,EAAA;;QAGjB,IAAI,IAAI,CAAC,KAAK,EACd;YACI,OAAO;AACV,SAAA;;QAGD,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;;QAGhC,IAAI,CAAC,YAAY,EAAE,CAAC;;QAGpB,IAAI,IAAI,CAAC,GAAG,EACZ;AACI,YAAA,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;AACpB,SAAA;aACI,IAAI,IAAI,CAAC,GAAG,EACjB;AACI,YAAA,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;AACpB,SAAA;aACI,IAAI,IAAI,CAAC,IAAI,EAClB;;AAEI,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EACjB;gBACI,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,SAAS,CAAC;AAC5C,aAAA;;AAGD,iBAAA;AACI,gBAAA,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAC3B;oBACI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/C,iBAAA;AACJ,aAAA;AACJ,SAAA;;QAGD,IAAI,CAAC,OAAO,EAAE,CAAC;KAClB,CAAA;AAED;;;AAGG;IACH,cAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,EAAoC,EAAA;QAAzC,IAiEC,KAAA,GAAA,IAAA,CAAA;QA/DG,IAAI,IAAI,CAAC,SAAS,EAClB;YACI,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,UAAU,EACnB;AACI,YAAA,IAAI,EAAE,EACN;AACI,gBAAA,UAAU,CAAC,YAAA,EAAM,OAAA,EAAE,CAAC,KAAI,CAAC,CAAA,EAAA,EAAE,CAAC,CAAC,CAAC;AACjC,aAAA;YAED,OAAO;AACV,SAAA;AACI,aAAA,IAAI,EAAE,EACX;AACI,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC5B,SAAA;QAED,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAEzD,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;AAG5B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EACtE;YACI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3D,SAAA;QAED,QAAQ,IAAI,CAAC,QAAQ;AAEjB,YAAA,KAAK,cAAc,CAAC,SAAS,CAAC,KAAK;gBAC/B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACtC,gBAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,cAAc,CAAC,SAAS,CAAC,KAAK;gBAC/B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACtC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,cAAc,CAAC,SAAS,CAAC,KAAK;gBAC/B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACtC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC;;AAElC,YAAA;AACI,gBAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EACjC;AACI,oBAAA,MAAM,GAAG,CAAC,EAAG,UAAkB,CAAC,cAAc,IAAI,EAAE,iBAAiB,KAAK,IAAI,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;AACrG,iBAAA;AACD,gBAAA,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,EAC9B;oBACI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,iBAAA;AAED,qBAAA;oBACI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,iBAAA;gBACD,MAAM;AACb,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACK,cAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAAiB,IAAY,EAAA;QAEzB,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,CAAC;KACrC,CAAA;AAED;;;;AAIG;AACK,IAAA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAhB,UAAiB,IAAY,EAAE,KAAc,EAAA;QAEzC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;KACtE,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,YAAY,GAApB,YAAA;AAEI,QAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAC9C;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC/E,SAAA;QAED,IAAI,IAAI,CAAC,GAAG,EACZ;AACI,YAAA,IAAI,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAChC;AACI,gBAAA,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACpE,gBAAA,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACxE,gBAAA,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACpE,gBAAA,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACvE,gBAAA,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;AACrE,aAAA;AAED,iBAAA;AACI,gBAAA,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,gBAAA,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,gBAAA,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1B,aAAA;AACJ,SAAA;KACJ,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAf,YAAA;QAEI,IAAI,IAAI,CAAC,UAAU,EACnB;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;AAC/E,SAAA;QAED,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAE1D,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KAClC,CAAA;AAED;;;;;AAKG;IACH,cAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,IAAY,EAAA;AAErB,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAC7B;YACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;AACzC,SAAA;aACI,IAAI,IAAI,KAAK,OAAO,IAAI,OAAO,UAAU,CAAC,KAAK,KAAK,WAAW,EACpE;AACI,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;AAC3B,SAAA;AAED,aAAA;YACI,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC5C,SAAA;QAED,IAAI,IAAI,CAAC,WAAW,EACpB;YACI,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAC7B;YACI,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAC5B,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,OAAO,EAChB;AACI,YAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAQ,CAAC;AAC9E,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACK,cAAkB,CAAA,SAAA,CAAA,kBAAA,GAA1B,UAA2B,IAAY,EAAA;AAEnC,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAC7B;YACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;AACzC,SAAA;aACI,IAAI,IAAI,KAAK,OAAO,IAAI,OAAO,UAAU,CAAC,KAAK,KAAK,WAAW,EACpE;AACI,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;AAC3B,SAAA;AAED,aAAA;YACI,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EACtB;AACI,YAAA,IAAI,CAAC,KAAK,CAAC,uBAAwB,GAAA,IAAM,CAAC,CAAC;YAE3C,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,WAAW,EACpB;YACI,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAC7B;;YAEI,IAAK,SAAiB,CAAC,UAAU,EACjC;AACI,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AACpE,aAAA;iBACI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAChC;AACI,gBAAA,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAEzC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EACxC;AACI,oBAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CACjB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAC7F,CAAC;AACL,iBAAA;AACJ,aAAA;AAED,iBAAA;AACI,gBAAA,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAEzC,gBAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CACjB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAC1F,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAEzE,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEjB,IAAI,IAAI,CAAC,OAAO,EAChB;AACI,YAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAQ,CAAC;AAC9E,SAAA;KACJ,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAhB,YAAA;;AAGI,QAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EACpC;AACI,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC3C,SAAA;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;;AAG5C,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,iBAAiB,EAC1C;AACI,YAAA,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;AAC9B,SAAA;;QAGD,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAEhC,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;;;QAI3B,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc,CAAC,iBAAiB,CAAC,IAAI;eACnD,IAAI,CAAC,OAAO,KAAK,cAAc,CAAC,iBAAiB,CAAC,QAAQ,EACjE;YACI,GAAG,CAAC,YAAY,GAAG,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC;AAC5D,SAAA;AAED,aAAA;AACI,YAAA,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,OAAc,CAAC;AAC1C,SAAA;QAED,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAC5D,GAAG,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAChE,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAC5D,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAC/D,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAE1D,GAAG,CAAC,IAAI,EAAE,CAAC;KACd,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAhB,YAAA;;AAGI,QAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EACpC;AACI,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC3C,SAAA;AAED,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAK,UAAkB,CAAC,cAAc,EAAE,CAAC;;;;QAKhE,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;AAEnC,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACpC,QAAA,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACxC,QAAA,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACvC,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QAElC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;;;;AAMhC,QAAA,UAAU,CAAC,YAAA,EAAM,OAAA,GAAG,CAAC,IAAI,EAAE,CAAA,EAAA,EAAE,CAAC,CAAC,CAAC;KACnC,CAAA;AAED;;;;;;AAMG;AACK,IAAA,cAAA,CAAA,SAAA,CAAA,aAAa,GAArB,UAAsB,IAAY,EAAE,GAAW,EAAE,IAAY,EAAA;QAEzD,IAAI,CAAC,IAAI,EACT;YACI,IAAI,GAAM,IAAI,GAAI,GAAA,GAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAG,CAAC;AAC/C,SAAA;QAED,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAEhD,QAAA,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,QAAA,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AAEnB,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;AAGG;IACK,cAAQ,CAAA,SAAA,CAAA,QAAA,GAAhB,UAAiB,KAAY,EAAA;QAEzB,IAAI,CAAC,KAAK,CAAC,gCAAkC,GAAA,KAAK,CAAC,MAAc,CAAC,QAAU,CAAC,CAAC;KACjF,CAAA;AAED;;;AAGG;IACK,cAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,KAAoB,EAAA;AAEpC,QAAA,IAAI,KAAK,IAAI,KAAK,CAAC,gBAAgB,EACnC;AACI,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9D,SAAA;KACJ,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,YAAA;AAEI,QAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;KACjC,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;AAEI,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAErB,QAAA,IAAI,CAAC,KAAK,CAAI,OAAO,CAAC,GAAG,CAAC,GAAA,2BAAA,GAA4B,GAAG,CAAC,MAAM,GAAY,YAAA,GAAA,GAAG,CAAC,UAAU,GAAA,IAAG,CAAC,CAAC;KAClG,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,aAAa,GAArB,YAAA;AAEI,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,KAAK,CAAI,OAAO,CAAC,GAAG,CAAC,GAAqB,qBAAA,CAAC,CAAC;KACpD,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;AAEI,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,KAAK,CAAI,OAAO,CAAC,GAAG,CAAC,GAAmC,mCAAA,CAAC,CAAC;KAClE,CAAA;;AAGO,IAAA,cAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,YAAA;AAEI,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,MAAM,GAAG,OAAO,GAAG,CAAC,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;;AAGxE,QAAA,IAAI,GAAG,CAAC,YAAY,KAAK,EAAE,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,IAAI,OAAO,GAAG,CAAC,YAAY,KAAK,WAAW,EACrG;AACI,YAAA,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC;AAC3B,SAAA;;;QAID,IAAI,MAAM,KAAK,WAAW,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,YAAY,KAAK,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAC/G;YACI,MAAM,GAAG,SAAS,CAAC;AACtB,SAAA;;aAEI,IAAI,MAAM,KAAK,mBAAmB,EACvC;YACI,MAAM,GAAG,YAAY,CAAC;AACzB,SAAA;QAED,IAAM,UAAU,GAAG,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC;QAEtC,IAAI,UAAU,KAAK,cAAc,EACjC;;YAEI,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAC1D;AACI,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AACxC,aAAA;;iBAEI,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAC/D;gBACI,IACA;oBACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC7B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AACxC,iBAAA;AACD,gBAAA,OAAO,CAAC,EACR;AACI,oBAAA,IAAI,CAAC,KAAK,CAAC,qCAAsC,GAAA,CAAG,CAAC,CAAC;oBAEtD,OAAO;AACV,iBAAA;AACJ,aAAA;;iBAEI,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc,CAAC,iBAAiB,CAAC,QAAQ,EACnE;gBACI,IACA;oBACI,IAAI,UAAU,CAAC,SAAS,EACxB;AACI,wBAAA,IAAM,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;wBAElC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC3D,qBAAA;AAED,yBAAA;wBACI,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAE1C,wBAAA,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;AAErB,wBAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACnB,qBAAA;oBAED,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC;AACvC,iBAAA;AACD,gBAAA,OAAOC,GAAC,EACR;AACI,oBAAA,IAAI,CAAC,KAAK,CAAC,oCAAqC,GAAAA,GAAG,CAAC,CAAC;oBAErD,OAAO;AACV,iBAAA;AACJ,aAAA;;AAGD,iBAAA;gBACI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC;AACpC,aAAA;AACJ,SAAA;AAED,aAAA;AACI,YAAA,IAAI,CAAC,KAAK,CAAC,GAAI,GAAA,GAAG,CAAC,MAAM,GAAA,IAAA,GAAK,GAAG,CAAC,UAAU,GAAK,IAAA,GAAA,GAAG,CAAC,WAAa,CAAC,CAAC;YAEpE,OAAO;AACV,SAAA;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB,CAAA;AAED;;;;;;;;AAQG;;AAEH,IAAA,cAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,UAAsB,GAAW,EAAE,GAAS,EAAA;;QAGxC,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAC9B;AACI,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;;;;QAKD,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,MAAM,EACpD;AACI,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;;AAGD,QAAA,GAAG,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC;QAEjC,IAAI,CAAC,UAAU,EACf;AACI,YAAA,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC5C,SAAA;;;;AAKD,QAAA,UAAU,CAAC,IAAI,GAAG,GAAG,CAAC;AACtB,QAAA,IAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QAElE,IAAM,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,MAAM,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;AACvF,QAAA,IAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,GAAM,SAAS,CAAC,QAAQ,GAAG,GAAA,GAAG,EAAE,CAAC;;AAGpE,QAAA,IAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,GAAG,CAAC,QAAQ,EAC7E;AACI,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;AAED,QAAA,OAAO,EAAE,CAAC;KACb,CAAA;AAED;;;;;AAKG;AACK,IAAA,cAAA,CAAA,SAAA,CAAA,iBAAiB,GAAzB,YAAA;AAEI,QAAA,OAAO,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC;KAC9F,CAAA;AAED;;;;;AAKG;AACK,IAAA,cAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,YAAA;AAEI,QAAA,OAAO,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC;KACtF,CAAA;AAED;;;;AAIG;IACK,cAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,GAAc,EAAA;AAAd,QAAA,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAA,GAAM,IAAI,CAAC,GAAG,CAAA,EAAA;QAEhC,IAAI,GAAG,GAAG,EAAE,CAAC;QAEb,IAAI,IAAI,CAAC,SAAS,EAClB;YACI,IAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAEpC,YAAA,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;AACrE,SAAA;AAED,aAAA;YACI,IAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACpC,IAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACnC,YAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAClB,UAAU,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,CAAC,MAAM,EACzC,SAAS,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,GAAG,CAAC,MAAM,CAC1C,CAAC;YAEF,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9B,YAAA,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;KAC5B,CAAA;AAED;;;;;;AAMG;IACH,cAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,IAAsC,EAAA;AAEtD,QAAA,QAAQ,IAAI;AAER,YAAA,KAAK,cAAc,CAAC,iBAAiB,CAAC,MAAM;AACxC,gBAAA,OAAO,0BAA0B,CAAC;AAEtC,YAAA,KAAK,cAAc,CAAC,iBAAiB,CAAC,IAAI;AACtC,gBAAA,OAAO,kBAAkB,CAAC;AAE9B,YAAA,KAAK,cAAc,CAAC,iBAAiB,CAAC,QAAQ;AAC1C,gBAAA,OAAO,iBAAiB,CAAC;AAE7B,YAAA,KAAK,cAAc,CAAC,iBAAiB,CAAC,IAAI;AACtC,gBAAA,OAAO,kBAAkB,CAAC;AAE9B,YAAA,KAAK,cAAc,CAAC,iBAAiB,CAAC,OAAO,CAAC;AAC9C,YAAA,KAAK,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC;;AAE3C,YAAA;AACI,gBAAA,OAAO,YAAY,CAAC;AAC3B,SAAA;KACJ,CAAA;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;AACA,CAAA,UAAU,cAAc,EAAA;AAgCpB,IAAA,CAAA,UAAY,YAAY,EAAA;;AAIpB,QAAA,YAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;;AAER,QAAA,YAAA,CAAA,YAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAmB,CAAA;;AAEnB,QAAA,YAAA,CAAA,YAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAmB,CAAA;;AAEnB,QAAA,YAAA,CAAA,YAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAkB,CAAA;AACtB,KAAC,EAXW,cAAY,CAAA,YAAA,KAAZ,2BAAY,GAWvB,EAAA,CAAA,CAAA,CAAA;AASD,IAAA,CAAA,UAAY,IAAI,EAAA;;AAIZ,QAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;;AAEX,QAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;;AAER,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;;AAEP,QAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;;AAET,QAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;;AAET,QAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;;AAET,QAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACZ,KAAC,EAjBW,cAAI,CAAA,IAAA,KAAJ,mBAAI,GAiBf,EAAA,CAAA,CAAA,CAAA;AASD,IAAA,CAAA,UAAY,SAAS,EAAA;;AAIjB,QAAA,SAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;;AAEP,QAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;;AAET,QAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;;AAET,QAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACb,KAAC,EAXW,cAAS,CAAA,SAAA,KAAT,wBAAS,GAWpB,EAAA,CAAA,CAAA,CAAA;AASD,IAAA,CAAA,UAAY,iBAAiB,EAAA;;AAIzB,QAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,MAAgB,CAAA;;AAEhB,QAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,aAAsB,CAAA;;AAEtB,QAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;;AAEb,QAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;;AAErB,QAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;;AAEb,QAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,KAAC,EAfW,cAAiB,CAAA,iBAAA,KAAjB,gCAAiB,GAe5B,EAAA,CAAA,CAAA,CAAA;AAEY,IAAA,cAAA,CAAA,YAAY,GAAiB;;AAEtC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACpC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACpC,QAAA,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACpC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACpC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;;AAGzC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;;AAGnC,QAAA,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;AACnC,QAAA,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,KAAK;KACvC,CAAC;AAEW,IAAA,cAAA,CAAA,WAAW,GAA4B;;AAEhD,QAAA,KAAK,EAAE,cAAc,CAAC,iBAAiB,CAAC,QAAQ;AAChD,QAAA,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,QAAQ;AAC/C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,QAAQ;AAC9C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,QAAQ;AAC9C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,QAAQ;AAC9C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,QAAQ;;;;AAK9C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,QAAQ;;AAG9C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC1C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC1C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC1C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC1C,QAAA,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC3C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC1C,QAAA,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC3C,QAAA,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC3C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC1C,QAAA,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;;AAG3C,QAAA,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;;AAG3C,QAAA,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;AAC3C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI;;AAG1C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,MAAM;AAC5C,QAAA,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC,MAAM;KAC/C,CAAC;;IAGW,cAAS,CAAA,SAAA,GAAG,oFAAoF,CAAC;AAClH,CAAC,EApLS,cAAc,KAAd,cAAc,GAoLvB,EAAA,CAAA,CAAA;;AC/zCD;;;AAGG;AACH,SAAS,KAAK,GAAA;AAEd,CAAC;AAED;;;;;AAKG;AACH,SAAS,QAAQ,CAAC,EAAc,EAAA;AAE5B,IAAA,OAAO,SAAS,WAAW,GAAA;;AAAA;QAAY,IAAY,IAAA,GAAA,EAAA,CAAA;aAAZ,IAAY,EAAA,GAAA,CAAA,EAAZ,EAAY,GAAA,SAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAA;YAAZ,IAAY,CAAA,EAAA,CAAA,GAAAF,WAAA,CAAA,EAAA,CAAA,CAAA;;QAE/C,IAAI,EAAE,KAAK,IAAI,EACf;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AACnD,SAAA;QAED,IAAM,MAAM,GAAG,EAAE,CAAC;QAElB,EAAE,GAAG,IAAI,CAAC;AACV,QAAA,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7B,KAAC,CAAC;AACN,CAAC;AAQD;;;AAGG;AACH,IAAA,cAAA,kBAAA,YAAA;AAKI;;;;AAIG;IACH,SAAY,cAAA,CAAA,IAAc,EAAE,QAAkC,EAAA;AAE1D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC5B;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;AAGG;AACH,IAAA,UAAA,kBAAA,YAAA;AAmBI;;;;AAIG;IACH,SAAY,UAAA,CAAA,MAA+C,EAAE,WAAe,EAAA;QAA5E,IAWC,KAAA,GAAA,IAAA,CAAA;AAX4D,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAe,GAAA,CAAA,CAAA,EAAA;QAtB5E,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;QAKZ,IAAS,CAAA,SAAA,GAAe,KAAK,CAAC;QAC9B,IAAW,CAAA,WAAA,GAAe,KAAK,CAAC;QAChC,IAAK,CAAA,KAAA,GAAe,KAAK,CAAC;QAC1B,IAAK,CAAA,KAAA,GAAe,KAAK,CAAC;QAC1B,IAAK,CAAA,KAAA,GAAyC,KAAK,CAAC;QAEpD,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;QAChB,IAAM,CAAA,MAAA,GAAG,KAAK,CAAC;QAGf,IAAM,CAAA,MAAA,GAAoC,EAAE,CAAC;AAoBrC,QAAA,IAAA,CAAA,OAAO,GAAG,UAAC,IAAS,EAAE,aAAsB,EAAE,QAAqB,EAAA;AAEvE,YAAA,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,UAAU,EAC9C;AACI,gBAAA,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACvD,aAAA;AAED,YAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;YAGpB,IAAI,IAAI,IAAI,IAAI,IAAI,KAAI,CAAC,IAAI,EAAE,EAC/B;;AAEI,gBAAA,UAAU,CAAC,YAAA,EAAM,OAAA,KAAI,CAAC,KAAK,EAAE,CAAA,EAAA,EAAE,CAAC,CAAC,CAAC;gBAElC,OAAO;AACV,aAAA;YAED,IAAM,IAAI,GAAG,IAAI,cAAc,CAC3B,IAAI,EACJ,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,KAAK,CACpD,CAAC;AAEF,YAAA,IAAI,aAAa,EACjB;AACI,gBAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7B,aAAA;AAED,iBAAA;AACI,gBAAA,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,aAAA;AAED,YAAA,UAAU,CAAC,KAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAChC,SAAC,CAAC;AAEF,QAAA,IAAA,CAAA,OAAO,GAAG,YAAA;AAEN,YAAA,OAAO,CAAC,KAAI,CAAC,MAAM,IAAI,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,WAAW,IAAI,KAAI,CAAC,MAAM,CAAC,MAAM,EAC5E;gBACI,IAAM,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AAEjC,gBAAA,IAAI,KAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAC5B;oBACI,KAAI,CAAC,KAAK,EAAE,CAAC;AAChB,iBAAA;AAED,gBAAA,KAAI,CAAC,OAAO,IAAI,CAAC,CAAC;AAElB,gBAAA,IAAI,KAAI,CAAC,OAAO,KAAK,KAAI,CAAC,WAAW,EACrC;oBACI,KAAI,CAAC,SAAS,EAAE,CAAC;AACpB,iBAAA;AAED,gBAAA,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvD,aAAA;AACL,SAAC,CAAC;AAlEE,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,WAAW,KAAK,CAAC,EACrB;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AACnD,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,MAAM,GAAG,WAAW,GAAG,GAAG,CAAC;KACnC;AA2DD;;;AAGG;IACH,UAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,IAA8B,EAAA;QAApC,IA0BC,KAAA,GAAA,IAAA,CAAA;QAxBG,OAAO,YAAA;;AAAA;YAAC,IAAY,IAAA,GAAA,EAAA,CAAA;iBAAZ,IAAY,EAAA,GAAA,CAAA,EAAZ,EAAY,GAAA,SAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAA;gBAAZ,IAAY,CAAA,EAAA,CAAA,GAAAA,WAAA,CAAA,EAAA,CAAA,CAAA;;AAEhB,YAAA,KAAI,CAAC,OAAO,IAAI,CAAC,CAAC;AAElB,YAAA,IAAI,CAAC,QAAQ,CAAA,KAAA,CAAb,IAAI,EAAa,IAAI,CAAE,CAAA;;AAGvB,YAAA,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EACnB;AACI,gBAAA,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC,aAAA;AAED,YAAA,IAAI,KAAI,CAAC,OAAO,KAAK,KAAI,CAAC,WAAW,GAAG,KAAI,CAAC,MAAM,CAAC,EACpD;gBACI,KAAI,CAAC,WAAW,EAAE,CAAC;AACtB,aAAA;AAED,YAAA,IAAI,KAAI,CAAC,IAAI,EAAE,EACf;gBACI,KAAI,CAAC,KAAK,EAAE,CAAC;AAChB,aAAA;YAED,KAAI,CAAC,OAAO,EAAE,CAAC;AACnB,SAAC,CAAC;KACL,CAAA;;;AAKD,IAAA,UAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,IAAS,EAAE,QAAmC,EAAA;QAE/C,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;KACvC,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AAEI,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;KACpB,CAAA;;AAGD,IAAA,UAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,IAAS,EAAE,QAAmC,EAAA;QAElD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;KACtC,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AAEI,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;KAC7B,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QAEI,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QAEI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC;KAClD,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EACxB;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AAEI,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EACzB;YACI,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;;;AAIpB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAC1C;YACI,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,SAAA;KACJ,CAAA;AAED;;;;;;AAMG;IACI,UAAU,CAAA,UAAA,GAAjB,UAAkB,KAAiB,EAAE,QAAqD,EACtF,QAA8B,EAAE,SAAmB,EAAA;QAEnD,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,QAAA,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QAEzB,SAAS,IAAI,CAAC,GAAS,EAAA;AAEnB,YAAA,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,EACpB;AACI,gBAAA,IAAI,QAAQ,EACZ;oBACI,QAAQ,CAAC,GAAG,CAAC,CAAC;AACjB,iBAAA;gBAED,OAAO;AACV,aAAA;AAED,YAAA,IAAI,SAAS,EACb;AACI,gBAAA,UAAU,CAAC,YAAA;oBAEP,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC9B,EAAE,CAAC,CAAC,CAAC;AACT,aAAA;AAED,iBAAA;gBACI,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9B,aAAA;SACJ;AAED,QAAA,IAAI,EAAE,CAAC;KACV,CAAA;AAED;;;;;AAKG;AACI,IAAA,UAAA,CAAA,KAAK,GAAZ,UAAa,MAAsD,EAAE,WAAoB,EAAA;AAErF,QAAA,OAAO,IAAI,UAAU,CAAM,MAAM,EAAE,WAAW,CAAC,CAAC;KACnD,CAAA;IACL,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACrSD;AACA,IAAM,YAAY,GAAG,GAAG,CAAC;AACzB,IAAM,iBAAiB,GAAG,aAAa,CAAC;AA8CxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CG;AACH,IAAA,MAAA,kBAAA,YAAA;AAqEI;;;AAGG;IACH,SAAY,MAAA,CAAA,OAAY,EAAE,WAAgB,EAAA;QAA1C,IAiCC,KAAA,GAAA,IAAA,CAAA;AAjCW,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAY,GAAA,EAAA,CAAA,EAAA;AAAE,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAgB,GAAA,EAAA,CAAA,EAAA;;QAnE1C,IAAQ,CAAA,QAAA,GAAG,CAAC,CAAC;;QAGb,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;AAEhB;;;;;;;;;;;;;;;;;;AAkBG;QACH,IAAkB,CAAA,kBAAA,GAAG,EAAE,CAAC;;QAGhB,IAAiB,CAAA,iBAAA,GAA6B,EAAE,CAAC;;QAGjD,IAAgB,CAAA,gBAAA,GAA6B,EAAE,CAAC;;QAGhD,IAAiB,CAAA,iBAAA,GAA0B,EAAE,CAAC;AAEtD;;;;AAIG;AACK,QAAA,IAAA,CAAA,kBAAkB,GAAG,UAAC,CAAiB,EAAE,CAAa,IAAW,OAAA,KAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,EAAA,CAAC;;QAMlG,IAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;AAuBjC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,kBAAkB,GAAG,UAAC,CAAC,EAAE,CAAC,EAAK,EAAA,OAAA,KAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,EAAA,CAAC;AAC7D,QAAA,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;AAE/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAC/C;YACI,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAA,GAAG,GAAU,MAAM,CAAA,GAAhB,EAAE,GAAG,GAAK,MAAM,CAAA,GAAX,CAAY;AAE5B,YAAA,IAAI,GAAG,EACP;AACI,gBAAA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjB,aAAA;AAED,YAAA,IAAI,GAAG,EACP;AACI,gBAAA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjB,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;KAC3B;AAgDD;;;;;;;;AAQG;IACO,MAAI,CAAA,SAAA,CAAA,IAAA,GAAd,UAAe,IAAY,EAAE,GAAW,EAAE,OAAoB,EAAE,QAA0C,EAAA;;AAGtG,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EACzD;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;AACxE,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EACxB;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,sBAAmB,IAAI,GAAA,oBAAmB,CAAC,CAAC;AAC/D,SAAA;;AAGD,QAAA,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;;AAG5B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAE9D,QAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAClC;AACI,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzD,SAAA;;QAGD,IAAI,IAAI,CAAC,OAAO,EAChB;AACI,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;YACtC,IAAM,kBAAkB,GAAG,EAAE,CAAC;AAE9B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAC/C;gBACI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,EAClC;oBACI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,iBAAA;AACJ,aAAA;AAED,YAAA,IAAM,SAAS,GAAG,MAAM,CAAC,aAAa,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACzE,YAAA,IAAM,SAAS,GAAG,SAAS,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAE9D,YAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3C,YAAA,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;AAEjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC,EAClD;AACI,gBAAA,kBAAkB,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC;AACnD,aAAA;YAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC;AAClD,SAAA;;AAGD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAEvC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;;AAID;;;;;AAKG;IACH,MAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,EAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAEhC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;IACH,MAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,EAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAE/B,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAEI,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAErB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;;AAGpB,QAAA,KAAK,IAAM,CAAC,IAAI,IAAI,CAAC,SAAS,EAC9B;YACI,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAI,GAAG,CAAC,cAAc,EACtB;AACI,gBAAA,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;AAC/B,aAAA;YAED,IAAI,GAAG,CAAC,SAAS,EACjB;AACI,gBAAA,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC7B,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAEpB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,MAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,EAA4B,EAAA;AAG7B,QAAA,WAAW,CAAC,OAAO,EAAE,8EAA8E,CAAC,CAAC;;AAIrG,QAAA,IAAI,OAAO,EAAE,KAAK,UAAU,EAC5B;AACI,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC5B,SAAA;;QAGD,IAAI,IAAI,CAAC,OAAO,EAChB;AACI,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EACtB;YACI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;AAED,aAAA;;YAEI,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC3C,YAAA,IAAM,KAAK,GAAG,YAAY,GAAG,QAAQ,CAAC;AAEtC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAClD;AACI,gBAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AACpD,aAAA;;YAGD,IAAI,CAAC,QAAQ,EAAE,CAAC;;AAGhB,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;AACxB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,MAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAJf;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;SAClC;AACD,QAAA,GAAA,EAAA,UAAgB,WAAmB,EAAA;AAE/B,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;SACzC;;;AAJA,KAAA,CAAA,CAAA;AAMD;;;;AAIG;IACK,MAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,GAAW,EAAA;AAE3B,QAAA,IAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;AACtD,QAAA,IAAI,MAAM,CAAC;;AAGX,QAAA,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACpE;YACI,MAAM,GAAG,GAAG,CAAC;AAChB,SAAA;;AAEI,aAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM;AACrB,eAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;AACzD,eAAA,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAE5B;AACI,YAAA,MAAM,GAAM,IAAI,CAAC,OAAO,GAAA,GAAA,GAAI,GAAK,CAAC;AACrC,SAAA;AAED,aAAA;AACI,YAAA,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;AAC/B,SAAA;;QAGD,IAAI,IAAI,CAAC,kBAAkB,EAC3B;YACI,IAAM,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAE/C,YAAA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YAEtD,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAC9B;AACI,gBAAA,MAAM,IAAI,GAAA,GAAI,IAAI,CAAC,kBAAoB,CAAC;AAC3C,aAAA;AAED,iBAAA;AACI,gBAAA,MAAM,IAAI,GAAA,GAAI,IAAI,CAAC,kBAAoB,CAAC;AAC3C,aAAA;YAED,MAAM,IAAI,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;AAIG;AACK,IAAA,MAAA,CAAA,SAAA,CAAA,aAAa,GAArB,UAAsB,QAAwB,EAAE,OAAmB,EAAA;QAAnE,IA8BC,KAAA,GAAA,IAAA,CAAA;AA5BG,QAAA,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC;;QAG5B,UAAU,CAAC,UAAU,CACjB,IAAI,CAAC,iBAAiB,EACtB,UAAC,EAAO,EAAE,IAA4B,EAAA;AAElC,YAAA,EAAE,CAAC,IAAI,CAAC,KAAI,EAAE,QAAQ,EAAE,YAAA;;;AAIpB,gBAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAC1C,aAAC,CAAC,CAAC;AACP,SAAC,EACD,YAAA;YAEI,IAAI,QAAQ,CAAC,UAAU,EACvB;AACI,gBAAA,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC1B,aAAA;AAED,iBAAA;AACI,gBAAA,QAAQ,CAAC,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE,KAAI,CAAC,CAAC;gBACvE,QAAQ,CAAC,IAAI,EAAE,CAAC;AACnB,aAAA;SACJ,EACD,IAAI,CACP,CAAC;KACL,CAAA;;AAGO,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAhB,YAAA;AAEI,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KAC/B,CAAA;;AAGO,IAAA,MAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;AAEI,QAAA,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;AAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;KAClD,CAAA;AAED;;;AAGG;IACK,MAAO,CAAA,SAAA,CAAA,OAAA,GAAf,UAAgB,QAAwB,EAAA;QAAxC,IAyCC,KAAA,GAAA,IAAA,CAAA;AAvCG,QAAA,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC;;AAG/B,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,QAAQ,CAAC,QAAQ,EAAE,CAAC;;QAGpB,UAAU,CAAC,UAAU,CACjB,IAAI,CAAC,gBAAgB,EACrB,UAAC,EAAO,EAAE,IAAS,EAAA;YAEf,EAAE,CAAC,IAAI,CAAC,KAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAClC,SAAC,EACD,YAAA;AAEI,YAAA,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAE9C,YAAA,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC/E,KAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAI,EAAE,QAAQ,CAAC,CAAC;YAEzC,IAAI,QAAQ,CAAC,KAAK,EAClB;AACI,gBAAA,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAI,EAAE,QAAQ,CAAC,CAAC;AACzD,aAAA;AAED,iBAAA;gBACI,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAI,EAAE,QAAQ,CAAC,CAAC;AACxC,aAAA;AAED,YAAA,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;AAG3E,YAAA,IAAI,KAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,KAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAC7D;gBACI,KAAI,CAAC,WAAW,EAAE,CAAC;AACtB,aAAA;SACJ,EACD,IAAI,CACP,CAAC;KACL,CAAA;;AAWM,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAd,YAAA;AAEI,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EACpB;YACI,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,SAAA;KACJ,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAkB,MAAM,EAAA,QAAA,EAAA;;AAAxB,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;YAE5B,IAAI,CAAC,MAAM,EACX;AACI,gBAAA,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AACtB,gBAAA,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AACzB,gBAAA,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AAC3B,aAAA;AAED,YAAA,OAAO,MAAM,CAAC;SACjB;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;;AAKG;IACW,MAAc,CAAA,cAAA,GAA5B,UAA6B,MAAqB,EAAA;AAG9C,QAAA,WAAW,CAAC,OAAO,EAAE,sEAAsE,CAAC,CAAC;QAG7F,UAAU,CAAC,GAAG,CAAC;YACX,IAAI,EAAE,aAAa,CAAC,MAAM;AAC1B,YAAA,GAAG,EAAE,MAAM;AACd,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;IAlDM,MAAQ,CAAA,QAAA,GAAyB,EAAE,CAAC;IAmD/C,OAAC,MAAA,CAAA;AAAA,CAliBD,EAkiBC,EAAA;AAED,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AAE/D,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,CAAe,IAAS,EAAE,GAAS,EAAE,OAAa,EAAE,QAAc,EAAA;;AAGjG,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EACvB;AACI,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EACpC;YACI,IAAI,CAAC,GAAG,CAAE,IAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;;AAGD,IAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;QACI,OAAO,GAAG,IAAI,CAAC;QACf,QAAQ,GAAI,GAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,CAAC;AAClE,QAAA,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AAClB,QAAA,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;AACrD,KAAA;;AAGD,IAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAC3B;QACI,QAAQ,GAAG,OAAc,CAAC;QAC1B,OAAO,GAAG,GAAG,CAAC;QACd,GAAG,GAAG,IAAI,CAAC;AACd,KAAA;;AAGD,IAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAC3B;AACI,QAAA,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC/D,KAAA;;AAGD,IAAA,IAAI,OAAO,OAAO,KAAK,UAAU,EACjC;QACI,QAAQ,GAAG,OAAO,CAAC;QACnB,OAAO,GAAG,IAAI,CAAC;AAClB,KAAA;AAED,IAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AACnD,CAAC;;ACprBD;;;;;;;;AAQG;AACH,IAAA,eAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,eAAA,GAAA;KAsCC;AA1BG;;;;AAIG;IACI,eAAI,CAAA,IAAA,GAAX,UAAY,OAA0C,EAAA;AAElD,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACpB,YAAA,YAAY,EAAE,KAAK;SACtB,EAAE,OAAO,CAAC,CAAC;AAEZ,QAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;KACrE,CAAA;AAED;;;AAGG;AACI,IAAA,eAAA,CAAA,OAAO,GAAd,YAAA;QAEI,IAAI,IAAI,CAAC,MAAM,EACf;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;KACJ,CAAA;;AAlCM,IAAA,eAAA,CAAA,SAAS,GAAsB,aAAa,CAAC,WAAW,CAAC;IAmCpE,OAAC,eAAA,CAAA;AAAA,CAtCD,EAsCC;;AC/CD;;;AAGG;AACH,IAAA,aAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,aAAA,GAAA;KAuCC;;AAjCiB,IAAA,aAAA,CAAA,GAAG,GAAjB,YAAA;QAEI,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACzE,cAAc,CAAC,mBAAmB,CAAC,KAAK,EAAE,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KACpF,CAAA;AAED;;;;;AAKG;AACW,IAAA,aAAA,CAAA,GAAG,GAAjB,UAAkB,QAAwB,EAAE,IAA8B,EAAA;;QAGtE,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,SAAS,KAAK,KAAK,CAAC,EAClG;AACY,YAAA,IAAA,IAAI,GAA0B,QAAQ,KAAlC,EAAE,GAAG,GAAqB,QAAQ,CAAA,GAA7B,EAAE,IAAI,GAAe,QAAQ,CAAvB,IAAA,EAAE,QAAQ,GAAK,QAAQ,SAAb,CAAc;AAE/C,YAAA,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAC,OAAO,EAAA;AAEvD,gBAAA,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,gBAAA,IAAI,EAAE,CAAC;AACX,aAAC,CAAC;;;iBAGG,KAAK,CAAC,IAAI,CAAC,CAAC;AACpB,SAAA;AAED,aAAA;AACI,YAAA,IAAI,EAAE,CAAC;AACV,SAAA;KACJ,CAAA;;AAnCM,IAAA,aAAA,CAAA,SAAS,GAAsB,aAAa,CAAC,MAAM,CAAC;IAoC/D,OAAC,aAAA,CAAA;AAAA,CAvCD,EAuCC;;AC/CD,IAAM,OAAO,GAAG,mEAAmE,CAAC;AAEpF;;;;;AAKG;AACG,SAAU,YAAY,CAAC,KAAa,EAAA;IAEtC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,GAAG,GAAG,CAAC,CAAC;AAEZ,IAAA,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM,EACzB;;QAEI,IAAM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,IAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAExC,QAAA,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,GAAG,EAChD;AACI,YAAA,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EACtB;;;AAGI,gBAAA,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;AACpD,aAAA;AAED,iBAAA;AACI,gBAAA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,aAAA;AACJ,SAAA;;;QAID,kBAAkB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;;QAG3C,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;QAG5E,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;QAG7E,kBAAkB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;;QAG7C,IAAM,YAAY,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAE9C,QAAA,QAAQ,YAAY;AAEhB,YAAA,KAAK,CAAC;;AAEF,gBAAA,kBAAkB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC3B,gBAAA,kBAAkB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,CAAC;;AAEF,gBAAA,kBAAkB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM;AAIb,SAAA;;;AAID,QAAA,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,kBAAkB,CAAC,MAAM,EAAE,EAAE,GAAG,EACxD;YACI,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAClB;;ACxEA;;;;;;;;;;AAUG;AACa,SAAA,OAAO,CAAC,QAAwB,EAAE,IAA4B,EAAA;AAE1E,IAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAClB;AACI,QAAA,IAAI,EAAE,CAAC;QAEP,OAAO;AACV,KAAA;;AAGD,IAAA,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,OAAO,KAAK,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAC9E;;QAEI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,EACnD;YACI,IAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;;YAG5D,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EACvC;AACI,gBAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;AAC5B,gBAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,UAAQ,IAAI,GAAA,UAAA,GAAW,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAG,CAAC;gBAErF,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG1C,gBAAA,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,YAAA;AAEnB,oBAAA,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAE5B,oBAAA,IAAI,EAAE,CAAC;AACX,iBAAC,CAAC;;gBAGF,OAAO;AACV,aAAA;AACJ,SAAA;;AAEI,aAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAClD;YACI,IAAM,KAAG,GAAG,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC;YACnD,IAAM,KAAG,GAAG,KAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAE/C,YAAA,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC9B,YAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;AAC5B,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,KAAG,CAAC;YAExB,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAI1C,YAAA,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,YAAA;AAEnB,gBAAA,KAAG,CAAC,eAAe,CAAC,KAAG,CAAC,CAAC;AACzB,gBAAA,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAE5B,gBAAA,IAAI,EAAE,CAAC;AACX,aAAC,CAAC;;YAGF,OAAO;AACV,SAAA;AACJ,KAAA;AAED,IAAA,IAAI,EAAE,CAAC;AACX;;AC3EA;;;AAGG;AACH,IAAA,aAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,aAAA,GAAA;KAMC;;AAHU,IAAA,aAAA,CAAA,SAAS,GAAsB,aAAa,CAAC,MAAM,CAAC;IAEpD,aAAG,CAAA,GAAA,GAAG,OAAO,CAAC;IACzB,OAAC,aAAA,CAAA;AAAA,CAND,EAMC,CAAA;;ACLD,UAAU,CAAC,GAAG,CACV,aAAa,EACb,aAAa,CAChB;;;;"}