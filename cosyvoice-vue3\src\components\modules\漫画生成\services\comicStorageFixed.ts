import type { ComicResult } from '../types';
import { getMasterWebSocketManager } from '@/services/masterWebSocketManager';

export class ComicStorageServiceFixed {
  private readonly STORAGE_KEY = 'comic-generation-results';
  private readonly MAX_STORED_COMICS = 50;

  /**
   * 🔧 统一持久化：上传图片到统一存储位置
   */
  private async uploadImageToServer(imageBase64: string, filename: string): Promise<string> {
    try {
      console.log(`📤 上传图片到服务器: ${filename}`);
      
      // 获取当前主机IP
      const hostname = window.location.hostname;
      const port = '3001';
      const uploadUrl = `http://${hostname}:${port}/api/upload-image`;
      
      // 🔑 修复：使用正确的API格式
      const imageId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageId: imageId,
          base64Data: imageBase64,
          subPath: 'static/images'
        })
      });
      
      if (!response.ok) {
        throw new Error(`上传失败: ${response.status}`);
      }
      
      const result = await response.json();
      if (result.success) {
        console.log(`✅ 图片上传成功: ${result.url}`);
        return result.url;
      } else {
        throw new Error(result.error || '上传失败');
      }
    } catch (error) {
      console.error('❌ 图片上传失败:', error);
      throw error;
    }
  }

  /**
   * 🔧 统一持久化：转换Base64图片为HTTP URL
   */
  private async convertBase64ImagesToUrls(comic: ComicResult): Promise<ComicResult> {
    console.log(`🔄 转换Base64图片为HTTP URL: ${comic.id}`);
    
    try {
      // 转换images数组中的Base64图片
      const convertedImages: string[] = [];
      for (let i = 0; i < comic.images.length; i++) {
        const image = comic.images[i];
        if (typeof image === 'string' && image.startsWith('data:image/')) {
          const filename = `comic_${comic.id}_image_${i}`;
          const url = await this.uploadImageToServer(image, filename);
          convertedImages.push(url);
        } else {
          convertedImages.push(image);
        }
      }
      
      // 转换finalComicUrl
      let convertedFinalComicUrl = comic.finalComicUrl;
      if (convertedFinalComicUrl && convertedFinalComicUrl.startsWith('data:image/')) {
        const filename = `comic_${comic.id}_final`;
        convertedFinalComicUrl = await this.uploadImageToServer(convertedFinalComicUrl, filename);
      }
      
      return {
        ...comic,
        images: convertedImages,
        finalComicUrl: convertedFinalComicUrl
      };
    } catch (error) {
      console.error('❌ 转换图片失败:', error);
      throw error;
    }
  }

  /**
   * 🔧 统一持久化：通过WebSocket同步漫画数据
   */
  private async syncComicViaWebSocket(comic: ComicResult): Promise<void> {
    try {
      console.log(`📡 通过WebSocket同步漫画数据: ${comic.id}`);
      
      // 获取WebSocket管理器
      const wsManager = getMasterWebSocketManager({}, 'comic-generation');
      
      // 🔧 改进：检查连接状态
      if (!wsManager.isConnected()) {
        console.log('🔗 WebSocket未连接，尝试连接...');
        await wsManager.connect();
      }
      
      const socket = wsManager.getSocket();
      if (!socket) {
        throw new Error('WebSocket连接未建立');
      }
      
      console.log(`✅ WebSocket连接状态: ${wsManager.isConnected()}, Socket ID: ${socket.id}`);
      
      // 发送ADD_COMIC事件
      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('WebSocket同步超时'));
        }, 10000);
        
        socket.emit('ADD_COMIC', { comic });
        
        socket.once('ADD_COMIC_RESPONSE', (response: any) => {
          clearTimeout(timeoutId);
          if (response.success) {
            console.log(`✅ WebSocket同步成功: ${comic.id}`);
            resolve();
          } else {
            reject(new Error(response.error || 'WebSocket同步失败'));
          }
        });
      });
    } catch (error) {
      console.error('❌ WebSocket同步失败:', error);
      throw error;
    }
  }

  /**
   * 🔧 统一持久化：保存漫画生成结果
   */
  async saveComic(comic: ComicResult): Promise<void> {
    console.log(`💾 [统一持久化] ComicStorage.saveComic 被调用: ${comic.id}`);
    
    try {
      // 基本验证
      if (!comic?.id) {
        throw new Error('漫画数据缺少ID字段');
      }
      
      // 🔑 核心修复：简化数据标准化
      const normalizedComic: ComicResult = {
        ...comic,
        createdAt: comic.createdAt instanceof Date ? comic.createdAt : new Date(comic.createdAt),
        title: comic.title || (comic.storyText ? comic.storyText.substring(0, 20) + '...' : '未命名作品'),
        style: comic.style || '默认风格',
        storyText: comic.storyText || '',
        images: comic.images || [],
        finalComicUrl: comic.finalComicUrl || '',
        status: comic.status || 'completed'
      };
      
      console.log('📋 标准化数据:', {
        id: normalizedComic.id,
        title: normalizedComic.title,
        imagesCount: normalizedComic.images.length
      });
      
      // 🔧 统一持久化：转换Base64图片为HTTP URL
      const comicWithUrls = await this.convertBase64ImagesToUrls(normalizedComic);
      
      // 🔧 统一持久化：通过WebSocket同步到Electron Store
      await this.syncComicViaWebSocket(comicWithUrls);
      
      // 🔧 统一持久化：保留轻量级备份到localStorage（仅ID和URL，限制数量）
      const lightBackup = {
        id: comicWithUrls.id,
        title: comicWithUrls.title?.substring(0, 50) || '', // 限制标题长度
        createdAt: comicWithUrls.createdAt instanceof Date ? comicWithUrls.createdAt.toISOString() : comicWithUrls.createdAt,
        images: comicWithUrls.images?.slice(0, 3) || [], // 最多保留3张图片URL
        finalComicUrl: comicWithUrls.finalComicUrl || ''
      };
      
      try {
        const existingBackups = this.getLightBackups();
        const existingIndex = existingBackups.findIndex(b => b.id === lightBackup.id);
        
        if (existingIndex >= 0) {
          existingBackups[existingIndex] = lightBackup;
        } else {
          existingBackups.unshift(lightBackup);
          // 严格限制备份数量（最多20个）
          if (existingBackups.length > 20) {
            existingBackups.splice(20);
          }
        }
        
        const backupData = JSON.stringify(existingBackups);
        const backupSizeKB = backupData.length / 1024;
        
        console.log(`📦 轻量级备份大小: ${backupSizeKB.toFixed(2)}KB`);
        
        // 如果备份仍然过大，只保留最近的10个
        if (backupSizeKB > 100) {
          console.warn('⚠️ 备份仍然过大，减少到10个');
          const reducedBackups = existingBackups.slice(0, 10);
          localStorage.setItem(this.STORAGE_KEY, JSON.stringify(reducedBackups));
        } else {
          localStorage.setItem(this.STORAGE_KEY, backupData);
        }
        
        console.log('✅ 轻量级备份保存成功');
      } catch (backupError) {
        console.warn('⚠️ 轻量级备份保存失败，跳过localStorage备份:', backupError);
        // 不抛出错误，统一持久化已经成功
      }
      
      console.log('✅ 统一持久化保存完成');
      
    } catch (error) {
      console.error('❌ 统一持久化保存失败:', error);
      throw error;
    }
  }

  /**
   * 获取轻量级备份数据
   */
  private getLightBackups(): any[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('❌ 读取轻量级备份失败:', error);
      return [];
    }
  }

  /**
   * 🔍 诊断工具：检查后端API数据
   */
  async diagnoseBackendData(): Promise<void> {
    try {
      const hostname = window.location.hostname;
      const port = '3001';
      
      console.log('🔍 [API诊断] 开始检查后端API...');
      console.log(`🔍 [API诊断] 目标主机: ${hostname}:${port}`);
      
      // 1. 检查服务器状态
      const healthUrl = `http://${hostname}:${port}/api/health`;
      try {
        const healthResponse = await fetch(healthUrl);
        console.log('🔍 [API诊断] 服务器状态:', healthResponse.status);
      } catch (healthError) {
        console.log('🔍 [API诊断] 服务器状态检查失败:', healthError);
      }
      
      // 2. 检查数据存储API
      const apiUrl = `http://${hostname}:${port}/api/store/comic-generation-results`;
      console.log(`🔍 [API诊断] 调用API: ${apiUrl}`);
      
      const response = await fetch(apiUrl);
      console.log('🔍 [API诊断] API响应状态:', response.status);
      
      if (response.ok) {
        const result = await response.json();
        console.log('🔍 [API诊断] API响应数据:', {
          success: result.success,
          hasValue: !!result.value,
          valueType: typeof result.value,
          isArray: Array.isArray(result.value),
          valueLength: Array.isArray(result.value) ? result.value.length : 'N/A',
          firstItem: Array.isArray(result.value) && result.value.length > 0 ? result.value[0] : null
        });
      } else {
        console.log('🔍 [API诊断] API调用失败:', response.statusText);
      }
      
      // 3. 检查其他可能的API端点
      const alternativeUrls = [
        `http://${hostname}:${port}/api/comics`,
        `http://${hostname}:${port}/api/store`,
        `http://${hostname}:${port}/api/data/comics`
      ];
      
      for (const url of alternativeUrls) {
        try {
          const altResponse = await fetch(url);
          if (altResponse.ok) {
            const altData = await altResponse.json();
            console.log(`🔍 [API诊断] 替代API ${url}:`, {
              status: altResponse.status,
              hasData: !!altData,
              dataType: typeof altData
            });
          }
        } catch (error) {
          // 静默失败
        }
      }
      
    } catch (error) {
      console.error('🔍 [API诊断] 诊断失败:', error);
    }
  }

  /**
   * 🔧 统一持久化：获取所有漫画（从HTTP API）
   */
  async getAllComics(): Promise<ComicResult[]> {
    try {
      console.log('📚 从统一存储获取所有漫画...');
      
      // 获取当前主机IP
      const hostname = window.location.hostname;
      const port = '3001';
      const apiUrl = `http://${hostname}:${port}/api/store/comic-generation-results`;
      
      console.log(`🔍 [API调用] 请求URL: ${apiUrl}`);
      console.log(`🔍 [API调用] 当前环境: ${hostname === 'localhost' ? 'Electron' : 'LAN'}`);
      
      // 🔧 尝试多个可能的API端点
      const possibleEndpoints = [
        `http://${hostname}:${port}/api/store/comic-generation-results`,
        `http://${hostname}:${port}/api/store-get/comic-generation-results`,  
        `http://${hostname}:${port}/api/electron-store/comic-generation-results`
      ];
      
      let response;
      let workingUrl;
      
      for (const testUrl of possibleEndpoints) {
        try {
          console.log(`🔍 [API测试] 尝试: ${testUrl}`);
          const testResponse = await fetch(testUrl);
          console.log(`🔍 [API测试] ${testUrl} -> ${testResponse.status}`);
          
          if (testResponse.ok) {
            response = testResponse;
            workingUrl = testUrl;
            break;
          }
        } catch (testError) {
          console.log(`🔍 [API测试] ${testUrl} -> 失败: ${testError.message}`);
        }
      }
      
      if (!response) {
        throw new Error('所有API端点都无法访问');
      }
      
      console.log(`🔍 [API调用] 成功的URL: ${workingUrl}`);
      console.log(`🔍 [API调用] 响应状态: ${response.status} ${response.statusText}`);
      if (!response.ok) {
        throw new Error(`获取数据失败: ${response.status}`);
      }
      
      const responseText = await response.text();
      console.log(`🔍 [API响应] 原始文本 (${responseText.length}字符):`, responseText.substring(0, 500));
      
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('🔍 [API响应] JSON解析失败:', parseError);
        throw new Error(`API响应解析失败: ${parseError}`);
      }
      
      // 🔍 增加详细的API响应调试信息
      console.log('🔍 [API响应] 解析后数据:', {
        success: result.success,
        hasValue: !!result.value,
        valueType: typeof result.value,
        isArray: Array.isArray(result.value),
        valueLength: Array.isArray(result.value) ? result.value.length : 'N/A',
        keys: Object.keys(result),
        fullResponse: result
      });
      
      if (result.success && Array.isArray(result.value)) {
        const comics = result.value.map((comic: any) => ({
          ...comic,
          createdAt: new Date(comic.createdAt)
        }));
        console.log(`✅ 从统一存储获取 ${comics.length} 个漫画`);
        return comics;
      } else {
        console.warn('⚠️ 统一存储响应格式异常:', {
          success: result.success,
          valueType: typeof result.value,
          isArray: Array.isArray(result.value),
          response: result
        });
        return this.getAllComicsFromBackup();
      }
    } catch (error) {
      console.error('❌ 从统一存储获取漫画失败:', {
        errorType: error.constructor.name,
        errorMessage: error.message,
        errorStack: error.stack
      });
      
      // 局域网环境下不使用getAllComicsFromBackup（因为无法访问其他环境的localStorage）
      const isLAN = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
      if (isLAN) {
        console.log('🌐 局域网环境下不使用备份数据，返回空数组');
        return [];
      } else {
        return this.getAllComicsFromBackup();
      }
    }
  }

  /**
   * 🔧 统一持久化：从本地备份获取漫画（降级方案）
   */
  getAllComicsFromBackup(): ComicResult[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        if (Array.isArray(parsed)) {
          return parsed.map(comic => ({
            ...comic,
            createdAt: new Date(comic.createdAt)
          }));
        }
      }
      
      // 如果没有旧数据，返回轻量级备份
      const backups = this.getLightBackups();
      return backups.map((backup: any) => ({
        ...backup,
        createdAt: new Date(backup.createdAt),
        storyText: backup.storyText || '',
        style: backup.style || '默认风格',
        status: backup.status || 'completed'
      }));
    } catch (error) {
      console.error('❌ 读取备份漫画失败:', error);
      return [];
    }
  }

  /**
   * 🔧 兼容性：同步方法（用于不支持async的地方）
   */
  getAllComicsSync(): ComicResult[] {
    return this.getAllComicsFromBackup();
  }

  /**
   * 🔧 统一持久化：根据ID获取漫画
   */
  async getComicById(id: string): Promise<ComicResult | null> {
    try {
      const comics = await this.getAllComics();
      console.log('🔍 [统一持久化] getComicById调试:', {
        searchId: id,
        comicsType: typeof comics,
        isArray: Array.isArray(comics),
        comicsLength: comics?.length || 0,
        firstComic: comics?.[0]
      });
      
      if (!Array.isArray(comics)) {
        console.error('❌ [统一持久化] getAllComics返回的不是数组:', comics);
        return null;
      }
      
      return comics.find(comic => comic.id === id) || null;
    } catch (error) {
      console.error('❌ 获取漫画失败:', error);
      // 降级到同步备份方案
      const comics = this.getAllComicsFromBackup();
      return comics.find(comic => comic.id === id) || null;
    }
  }

  /**
   * 🔧 兼容性：同步方法（用于不支持async的地方）
   */
  getComicByIdSync(id: string): ComicResult | null {
    const comics = this.getAllComicsFromBackup();
    return comics.find(comic => comic.id === id) || null;
  }

  /**
   * 🔧 统一持久化：删除漫画
   */
  async deleteComic(id: string): Promise<boolean> {
    try {
      console.log(`🗑️ 删除漫画: ${id}`);
      
      const comics = await this.getAllComics();
      const filteredComics = comics.filter(comic => comic.id !== id);
      
      if (filteredComics.length === comics.length) {
        return false; // 没有找到要删除的漫画
      }
      
      // 🔧 统一持久化：通过HTTP API更新数据
      const hostname = window.location.hostname;
      const port = '3001';
      const apiUrl = `http://${hostname}:${port}/api/store/comic-generation-results`;
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: filteredComics.map(c => ({
            ...c,
            createdAt: c.createdAt instanceof Date ? c.createdAt.toISOString() : c.createdAt
          }))
        })
      });
      
      if (!response.ok) {
        throw new Error(`删除失败: ${response.status}`);
      }
      
      // 同时更新本地备份
      const backups = this.getLightBackups();
      const filteredBackups = backups.filter(b => b.id !== id);
      localStorage.setItem(this.STORAGE_KEY + '_backup', JSON.stringify(filteredBackups));
      
      console.log('✅ 漫画删除成功');
      return true;
    } catch (error) {
      console.error('❌ 删除漫画失败:', error);
      return false;
    }
  }

  /**
   * 🔧 统一持久化：清空所有漫画
   */
  async clearAllComics(): Promise<void> {
    try {
      console.log('🧹 清空所有漫画');
      
      // 🔧 统一持久化：通过HTTP API清空数据
      const hostname = window.location.hostname;
      const port = '3001';
      const apiUrl = `http://${hostname}:${port}/api/store/comic-generation-results`;
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ value: [] })
      });
      
      if (!response.ok) {
        throw new Error(`清空失败: ${response.status}`);
      }
      
      // 同时清空本地备份
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.STORAGE_KEY + '_backup');
      
      console.log('✅ 所有漫画清空成功');
    } catch (error) {
      console.error('❌ 清空漫画失败:', error);
      throw error;
    }
  }
}

// 导出修复版本的实例
export const comicStorageFixed = new ComicStorageServiceFixed();