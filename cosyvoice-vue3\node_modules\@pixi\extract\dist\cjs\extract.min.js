/*!
 * @pixi/extract - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/extract is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@pixi/constants"),t=require("@pixi/utils"),r=require("@pixi/math"),i=require("@pixi/core"),n=new r.Rectangle,a=function(){function r(e){this.renderer=e}return r.prototype.image=function(e,t,r){var i=new Image;return i.src=this.base64(e,t,r),i},r.prototype.base64=function(e,t,r){return this.canvas(e).toDataURL(t,r)},r.prototype.canvas=function(e,i){var n=this._rawPixels(e,i),a=n.pixels,o=n.width,s=n.height,u=n.flipY,h=new t.CanvasRenderTarget(o,s,1),d=h.context.getImageData(0,0,o,s);if(r.arrayPostDivide(a,d.data),h.context.putImageData(d,0,0),u){var f=new t.CanvasRenderTarget(h.width,h.height,1);f.context.scale(1,-1),f.context.drawImage(h.canvas,0,-s),h.destroy(),h=f}return h.canvas},r.prototype.pixels=function(e,t){var i=this._rawPixels(e,t).pixels;return r.arrayPostDivide(i,i),i},r.prototype._rawPixels=function(t,r){var a,o,s=this.renderer,u=!1,h=!1;if(t)if(t instanceof i.RenderTexture)o=t;else{var d=s.context.webGLVersion>=2?s.multisample:e.MSAA_QUALITY.NONE;if(o=this.renderer.generateTexture(t,{multisample:d}),d!==e.MSAA_QUALITY.NONE){var f=i.RenderTexture.create({width:o.width,height:o.height});s.framebuffer.bind(o.framebuffer),s.framebuffer.blit(f.framebuffer),s.framebuffer.bind(null),o.destroy(!0),o=f}h=!0}o?(a=o.baseTexture.resolution,r=null!=r?r:o.frame,u=!1,s.renderTexture.bind(o)):(a=s.resolution,r||((r=n).width=s.width,r.height=s.height),u=!0,s.renderTexture.bind(null));var l=Math.round(r.width*a),p=Math.round(r.height*a),x=new Uint8Array(4*l*p),c=s.gl;return c.readPixels(Math.round(r.x*a),Math.round(r.y*a),l,p,c.RGBA,c.UNSIGNED_BYTE,x),h&&o.destroy(!0),{pixels:x,width:l,height:p,flipY:u}},r.prototype.destroy=function(){this.renderer=null},r.arrayPostDivide=function(e,t){for(var r=0;r<e.length;r+=4){var i=t[r+3]=e[r+3];0!==i?(t[r]=Math.round(Math.min(255*e[r]/i,255)),t[r+1]=Math.round(Math.min(255*e[r+1]/i,255)),t[r+2]=Math.round(Math.min(255*e[r+2]/i,255))):(t[r]=e[r],t[r+1]=e[r+1],t[r+2]=e[r+2])}},r.extension={name:"extract",type:i.ExtensionType.RendererPlugin},r}();exports.Extract=a;
//# sourceMappingURL=extract.min.js.map
