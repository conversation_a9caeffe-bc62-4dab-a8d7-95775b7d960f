{"name": "@pixi/ticker", "version": "6.5.10", "main": "dist/cjs/ticker.js", "module": "dist/esm/ticker.mjs", "bundle": "dist/browser/ticker.js", "types": "index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./dist/esm/ticker.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/ticker.js"}}}, "description": "Tickers are control the timings within PixiJS", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixi.js/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixi.js.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "dist", "*.d.ts"], "peerDependencies": {"@pixi/extensions": "6.5.10", "@pixi/settings": "6.5.10"}, "gitHead": "8cdbf55064b7adc05f65c51e177f1c22f7329f0f"}