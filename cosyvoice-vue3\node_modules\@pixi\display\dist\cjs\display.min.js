/*!
 * @pixi/display - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/display is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("@pixi/settings"),e=require("@pixi/math"),i=require("@pixi/utils"),n=require("@pixi/constants");t.settings.SORTABLE_CHILDREN=!1;var r=function(){function t(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.rect=null,this.updateID=-1}return t.prototype.isEmpty=function(){return this.minX>this.maxX||this.minY>this.maxY},t.prototype.clear=function(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0},t.prototype.getRectangle=function(t){return this.minX>this.maxX||this.minY>this.maxY?e.Rectangle.EMPTY:((t=t||new e.Rectangle(0,0,1,1)).x=this.minX,t.y=this.minY,t.width=this.maxX-this.minX,t.height=this.maxY-this.minY,t)},t.prototype.addPoint=function(t){this.minX=Math.min(this.minX,t.x),this.maxX=Math.max(this.maxX,t.x),this.minY=Math.min(this.minY,t.y),this.maxY=Math.max(this.maxY,t.y)},t.prototype.addPointMatrix=function(t,e){var i=t.a,n=t.b,r=t.c,s=t.d,o=t.tx,a=t.ty,h=i*e.x+r*e.y+o,l=n*e.x+s*e.y+a;this.minX=Math.min(this.minX,h),this.maxX=Math.max(this.maxX,h),this.minY=Math.min(this.minY,l),this.maxY=Math.max(this.maxY,l)},t.prototype.addQuad=function(t){var e=this.minX,i=this.minY,n=this.maxX,r=this.maxY,s=t[0],o=t[1];e=s<e?s:e,i=o<i?o:i,n=s>n?s:n,r=o>r?o:r,e=(s=t[2])<e?s:e,i=(o=t[3])<i?o:i,n=s>n?s:n,r=o>r?o:r,e=(s=t[4])<e?s:e,i=(o=t[5])<i?o:i,n=s>n?s:n,r=o>r?o:r,e=(s=t[6])<e?s:e,i=(o=t[7])<i?o:i,n=s>n?s:n,r=o>r?o:r,this.minX=e,this.minY=i,this.maxX=n,this.maxY=r},t.prototype.addFrame=function(t,e,i,n,r){this.addFrameMatrix(t.worldTransform,e,i,n,r)},t.prototype.addFrameMatrix=function(t,e,i,n,r){var s=t.a,o=t.b,a=t.c,h=t.d,l=t.tx,d=t.ty,u=this.minX,p=this.minY,m=this.maxX,c=this.maxY,f=s*e+a*i+l,y=o*e+h*i+d;u=f<u?f:u,p=y<p?y:p,m=f>m?f:m,c=y>c?y:c,u=(f=s*n+a*i+l)<u?f:u,p=(y=o*n+h*i+d)<p?y:p,m=f>m?f:m,c=y>c?y:c,u=(f=s*e+a*r+l)<u?f:u,p=(y=o*e+h*r+d)<p?y:p,m=f>m?f:m,c=y>c?y:c,u=(f=s*n+a*r+l)<u?f:u,p=(y=o*n+h*r+d)<p?y:p,m=f>m?f:m,c=y>c?y:c,this.minX=u,this.minY=p,this.maxX=m,this.maxY=c},t.prototype.addVertexData=function(t,e,i){for(var n=this.minX,r=this.minY,s=this.maxX,o=this.maxY,a=e;a<i;a+=2){var h=t[a],l=t[a+1];n=h<n?h:n,r=l<r?l:r,s=h>s?h:s,o=l>o?l:o}this.minX=n,this.minY=r,this.maxX=s,this.maxY=o},t.prototype.addVertices=function(t,e,i,n){this.addVerticesMatrix(t.worldTransform,e,i,n)},t.prototype.addVerticesMatrix=function(t,e,i,n,r,s){void 0===r&&(r=0),void 0===s&&(s=r);for(var o=t.a,a=t.b,h=t.c,l=t.d,d=t.tx,u=t.ty,p=this.minX,m=this.minY,c=this.maxX,f=this.maxY,y=i;y<n;y+=2){var b=e[y],x=e[y+1],_=o*b+h*x+d,v=l*x+a*b+u;p=Math.min(p,_-r),c=Math.max(c,_+r),m=Math.min(m,v-s),f=Math.max(f,v+s)}this.minX=p,this.minY=m,this.maxX=c,this.maxY=f},t.prototype.addBounds=function(t){var e=this.minX,i=this.minY,n=this.maxX,r=this.maxY;this.minX=t.minX<e?t.minX:e,this.minY=t.minY<i?t.minY:i,this.maxX=t.maxX>n?t.maxX:n,this.maxY=t.maxY>r?t.maxY:r},t.prototype.addBoundsMask=function(t,e){var i=t.minX>e.minX?t.minX:e.minX,n=t.minY>e.minY?t.minY:e.minY,r=t.maxX<e.maxX?t.maxX:e.maxX,s=t.maxY<e.maxY?t.maxY:e.maxY;if(i<=r&&n<=s){var o=this.minX,a=this.minY,h=this.maxX,l=this.maxY;this.minX=i<o?i:o,this.minY=n<a?n:a,this.maxX=r>h?r:h,this.maxY=s>l?s:l}},t.prototype.addBoundsMatrix=function(t,e){this.addFrameMatrix(e,t.minX,t.minY,t.maxX,t.maxY)},t.prototype.addBoundsArea=function(t,e){var i=t.minX>e.x?t.minX:e.x,n=t.minY>e.y?t.minY:e.y,r=t.maxX<e.x+e.width?t.maxX:e.x+e.width,s=t.maxY<e.y+e.height?t.maxY:e.y+e.height;if(i<=r&&n<=s){var o=this.minX,a=this.minY,h=this.maxX,l=this.maxY;this.minX=i<o?i:o,this.minY=n<a?n:a,this.maxX=r>h?r:h,this.maxY=s>l?s:l}},t.prototype.pad=function(t,e){void 0===t&&(t=0),void 0===e&&(e=t),this.isEmpty()||(this.minX-=t,this.maxX+=t,this.minY-=e,this.maxY+=e)},t.prototype.addFramePad=function(t,e,i,n,r,s){t-=r,e-=s,i+=r,n+=s,this.minX=this.minX<t?this.minX:t,this.maxX=this.maxX>i?this.maxX:i,this.minY=this.minY<e?this.minY:e,this.maxY=this.maxY>n?this.maxY:n},t}(),s=function(t,e){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},s(t,e)};function o(t,e){function i(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var a=function(t){function i(){var i=t.call(this)||this;return i.tempDisplayObjectParent=null,i.transform=new e.Transform,i.alpha=1,i.visible=!0,i.renderable=!0,i.cullable=!1,i.cullArea=null,i.parent=null,i.worldAlpha=1,i._lastSortedIndex=0,i._zIndex=0,i.filterArea=null,i.filters=null,i._enabledFilters=null,i._bounds=new r,i._localBounds=null,i._boundsID=0,i._boundsRect=null,i._localBoundsRect=null,i._mask=null,i._maskRefCount=0,i._destroyed=!1,i.isSprite=!1,i.isMask=!1,i}return o(i,t),i.mixin=function(t){for(var e=Object.keys(t),n=0;n<e.length;++n){var r=e[n];Object.defineProperty(i.prototype,r,Object.getOwnPropertyDescriptor(t,r))}},Object.defineProperty(i.prototype,"destroyed",{get:function(){return this._destroyed},enumerable:!1,configurable:!0}),i.prototype._recursivePostUpdateTransform=function(){this.parent?(this.parent._recursivePostUpdateTransform(),this.transform.updateTransform(this.parent.transform)):this.transform.updateTransform(this._tempDisplayObjectParent.transform)},i.prototype.updateTransform=function(){this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha},i.prototype.getBounds=function(t,i){return t||(this.parent?(this._recursivePostUpdateTransform(),this.updateTransform()):(this.parent=this._tempDisplayObjectParent,this.updateTransform(),this.parent=null)),this._bounds.updateID!==this._boundsID&&(this.calculateBounds(),this._bounds.updateID=this._boundsID),i||(this._boundsRect||(this._boundsRect=new e.Rectangle),i=this._boundsRect),this._bounds.getRectangle(i)},i.prototype.getLocalBounds=function(t){t||(this._localBoundsRect||(this._localBoundsRect=new e.Rectangle),t=this._localBoundsRect),this._localBounds||(this._localBounds=new r);var i=this.transform,n=this.parent;this.parent=null,this.transform=this._tempDisplayObjectParent.transform;var s=this._bounds,o=this._boundsID;this._bounds=this._localBounds;var a=this.getBounds(!1,t);return this.parent=n,this.transform=i,this._bounds=s,this._bounds.updateID+=this._boundsID-o,a},i.prototype.toGlobal=function(t,e,i){return void 0===i&&(i=!1),i||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.apply(t,e)},i.prototype.toLocal=function(t,e,i,n){return e&&(t=e.toGlobal(t,i,n)),n||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.applyInverse(t,i)},i.prototype.setParent=function(t){if(!t||!t.addChild)throw new Error("setParent: Argument must be a Container");return t.addChild(this),t},i.prototype.setTransform=function(t,e,i,n,r,s,o,a,h){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=1),void 0===n&&(n=1),void 0===r&&(r=0),void 0===s&&(s=0),void 0===o&&(o=0),void 0===a&&(a=0),void 0===h&&(h=0),this.position.x=t,this.position.y=e,this.scale.x=i||1,this.scale.y=n||1,this.rotation=r,this.skew.x=s,this.skew.y=o,this.pivot.x=a,this.pivot.y=h,this},i.prototype.destroy=function(t){this.parent&&this.parent.removeChild(this),this._destroyed=!0,this.transform=null,this.parent=null,this._bounds=null,this.mask=null,this.cullArea=null,this.filters=null,this.filterArea=null,this.hitArea=null,this.interactive=!1,this.interactiveChildren=!1,this.emit("destroyed"),this.removeAllListeners()},Object.defineProperty(i.prototype,"_tempDisplayObjectParent",{get:function(){return null===this.tempDisplayObjectParent&&(this.tempDisplayObjectParent=new h),this.tempDisplayObjectParent},enumerable:!1,configurable:!0}),i.prototype.enableTempParent=function(){var t=this.parent;return this.parent=this._tempDisplayObjectParent,t},i.prototype.disableTempParent=function(t){this.parent=t},Object.defineProperty(i.prototype,"x",{get:function(){return this.position.x},set:function(t){this.transform.position.x=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"y",{get:function(){return this.position.y},set:function(t){this.transform.position.y=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"worldTransform",{get:function(){return this.transform.worldTransform},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"localTransform",{get:function(){return this.transform.localTransform},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"position",{get:function(){return this.transform.position},set:function(t){this.transform.position.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"scale",{get:function(){return this.transform.scale},set:function(t){this.transform.scale.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"pivot",{get:function(){return this.transform.pivot},set:function(t){this.transform.pivot.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"skew",{get:function(){return this.transform.skew},set:function(t){this.transform.skew.copyFrom(t)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"rotation",{get:function(){return this.transform.rotation},set:function(t){this.transform.rotation=t},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"angle",{get:function(){return this.transform.rotation*e.RAD_TO_DEG},set:function(t){this.transform.rotation=t*e.DEG_TO_RAD},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"zIndex",{get:function(){return this._zIndex},set:function(t){this._zIndex=t,this.parent&&(this.parent.sortDirty=!0)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"worldVisible",{get:function(){var t=this;do{if(!t.visible)return!1;t=t.parent}while(t);return!0},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"mask",{get:function(){return this._mask},set:function(t){if(this._mask!==t){var e;if(this._mask)(e=this._mask.isMaskData?this._mask.maskObject:this._mask)&&(e._maskRefCount--,0===e._maskRefCount&&(e.renderable=!0,e.isMask=!1));if(this._mask=t,this._mask)(e=this._mask.isMaskData?this._mask.maskObject:this._mask)&&(0===e._maskRefCount&&(e.renderable=!1,e.isMask=!0),e._maskRefCount++)}},enumerable:!1,configurable:!0}),i}(i.EventEmitter),h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.sortDirty=null,e}return o(e,t),e}(a);function l(t,e){return t.zIndex===e.zIndex?t._lastSortedIndex-e._lastSortedIndex:t.zIndex-e.zIndex}a.prototype.displayObjectUpdateTransform=a.prototype.updateTransform;var d=function(e){function r(){var i=e.call(this)||this;return i.children=[],i.sortableChildren=t.settings.SORTABLE_CHILDREN,i.sortDirty=!1,i}return o(r,e),r.prototype.onChildrenChange=function(t){},r.prototype.addChild=function(){for(var t=arguments,e=[],i=0;i<arguments.length;i++)e[i]=t[i];if(e.length>1)for(var n=0;n<e.length;n++)this.addChild(e[n]);else{var r=e[0];r.parent&&r.parent.removeChild(r),r.parent=this,this.sortDirty=!0,r.transform._parentID=-1,this.children.push(r),this._boundsID++,this.onChildrenChange(this.children.length-1),this.emit("childAdded",r,this,this.children.length-1),r.emit("added",this)}return e[0]},r.prototype.addChildAt=function(t,e){if(e<0||e>this.children.length)throw new Error(t+"addChildAt: The index "+e+" supplied is out of bounds "+this.children.length);return t.parent&&t.parent.removeChild(t),t.parent=this,this.sortDirty=!0,t.transform._parentID=-1,this.children.splice(e,0,t),this._boundsID++,this.onChildrenChange(e),t.emit("added",this),this.emit("childAdded",t,this,e),t},r.prototype.swapChildren=function(t,e){if(t!==e){var i=this.getChildIndex(t),n=this.getChildIndex(e);this.children[i]=e,this.children[n]=t,this.onChildrenChange(i<n?i:n)}},r.prototype.getChildIndex=function(t){var e=this.children.indexOf(t);if(-1===e)throw new Error("The supplied DisplayObject must be a child of the caller");return e},r.prototype.setChildIndex=function(t,e){if(e<0||e>=this.children.length)throw new Error("The index "+e+" supplied is out of bounds "+this.children.length);var n=this.getChildIndex(t);i.removeItems(this.children,n,1),this.children.splice(e,0,t),this.onChildrenChange(e)},r.prototype.getChildAt=function(t){if(t<0||t>=this.children.length)throw new Error("getChildAt: Index ("+t+") does not exist.");return this.children[t]},r.prototype.removeChild=function(){for(var t=arguments,e=[],n=0;n<arguments.length;n++)e[n]=t[n];if(e.length>1)for(var r=0;r<e.length;r++)this.removeChild(e[r]);else{var s=e[0],o=this.children.indexOf(s);if(-1===o)return null;s.parent=null,s.transform._parentID=-1,i.removeItems(this.children,o,1),this._boundsID++,this.onChildrenChange(o),s.emit("removed",this),this.emit("childRemoved",s,this,o)}return e[0]},r.prototype.removeChildAt=function(t){var e=this.getChildAt(t);return e.parent=null,e.transform._parentID=-1,i.removeItems(this.children,t,1),this._boundsID++,this.onChildrenChange(t),e.emit("removed",this),this.emit("childRemoved",e,this,t),e},r.prototype.removeChildren=function(t,e){void 0===t&&(t=0),void 0===e&&(e=this.children.length);var i,n=t,r=e-n;if(r>0&&r<=e){i=this.children.splice(n,r);for(var s=0;s<i.length;++s)i[s].parent=null,i[s].transform&&(i[s].transform._parentID=-1);this._boundsID++,this.onChildrenChange(t);for(s=0;s<i.length;++s)i[s].emit("removed",this),this.emit("childRemoved",i[s],this,s);return i}if(0===r&&0===this.children.length)return[];throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},r.prototype.sortChildren=function(){for(var t=!1,e=0,i=this.children.length;e<i;++e){var n=this.children[e];n._lastSortedIndex=e,t||0===n.zIndex||(t=!0)}t&&this.children.length>1&&this.children.sort(l),this.sortDirty=!1},r.prototype.updateTransform=function(){this.sortableChildren&&this.sortDirty&&this.sortChildren(),this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha;for(var t=0,e=this.children.length;t<e;++t){var i=this.children[t];i.visible&&i.updateTransform()}},r.prototype.calculateBounds=function(){this._bounds.clear(),this._calculateBounds();for(var t=0;t<this.children.length;t++){var e=this.children[t];if(e.visible&&e.renderable)if(e.calculateBounds(),e._mask){var i=e._mask.isMaskData?e._mask.maskObject:e._mask;i?(i.calculateBounds(),this._bounds.addBoundsMask(e._bounds,i._bounds)):this._bounds.addBounds(e._bounds)}else e.filterArea?this._bounds.addBoundsArea(e._bounds,e.filterArea):this._bounds.addBounds(e._bounds)}this._bounds.updateID=this._boundsID},r.prototype.getLocalBounds=function(t,i){void 0===i&&(i=!1);var n=e.prototype.getLocalBounds.call(this,t);if(!i)for(var r=0,s=this.children.length;r<s;++r){var o=this.children[r];o.visible&&o.updateTransform()}return n},r.prototype._calculateBounds=function(){},r.prototype._renderWithCulling=function(t){var e=t.renderTexture.sourceFrame;if(e.width>0&&e.height>0){var i,n;if(this.cullArea?(i=this.cullArea,n=this.worldTransform):this._render!==r.prototype._render&&(i=this.getBounds(!0)),i&&e.intersects(i,n))this._render(t);else if(this.cullArea)return;for(var s=0,o=this.children.length;s<o;++s){var a=this.children[s],h=a.cullable;a.cullable=h||!this.cullArea,a.render(t),a.cullable=h}}},r.prototype.render=function(t){if(this.visible&&!(this.worldAlpha<=0)&&this.renderable)if(this._mask||this.filters&&this.filters.length)this.renderAdvanced(t);else if(this.cullable)this._renderWithCulling(t);else{this._render(t);for(var e=0,i=this.children.length;e<i;++e)this.children[e].render(t)}},r.prototype.renderAdvanced=function(t){var e=this.filters,i=this._mask;if(e){this._enabledFilters||(this._enabledFilters=[]),this._enabledFilters.length=0;for(var r=0;r<e.length;r++)e[r].enabled&&this._enabledFilters.push(e[r])}var s=e&&this._enabledFilters&&this._enabledFilters.length||i&&(!i.isMaskData||i.enabled&&(i.autoDetect||i.type!==n.MASK_TYPES.NONE));if(s&&t.batch.flush(),e&&this._enabledFilters&&this._enabledFilters.length&&t.filter.push(this,this._enabledFilters),i&&t.mask.push(this,this._mask),this.cullable)this._renderWithCulling(t);else{this._render(t);r=0;for(var o=this.children.length;r<o;++r)this.children[r].render(t)}s&&t.batch.flush(),i&&t.mask.pop(this),e&&this._enabledFilters&&this._enabledFilters.length&&t.filter.pop()},r.prototype._render=function(t){},r.prototype.destroy=function(t){e.prototype.destroy.call(this),this.sortDirty=!1;var i="boolean"==typeof t?t:t&&t.children,n=this.removeChildren(0,this.children.length);if(i)for(var r=0;r<n.length;++r)n[r].destroy(t)},Object.defineProperty(r.prototype,"width",{get:function(){return this.scale.x*this.getLocalBounds().width},set:function(t){var e=this.getLocalBounds().width;this.scale.x=0!==e?t/e:1,this._width=t},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"height",{get:function(){return this.scale.y*this.getLocalBounds().height},set:function(t){var e=this.getLocalBounds().height;this.scale.y=0!==e?t/e:1,this._height=t},enumerable:!1,configurable:!0}),r}(a);d.prototype.containerUpdateTransform=d.prototype.updateTransform,exports.Bounds=r,exports.Container=d,exports.DisplayObject=a,exports.TemporaryDisplayObject=h;
//# sourceMappingURL=display.min.js.map
