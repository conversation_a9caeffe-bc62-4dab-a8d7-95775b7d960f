/*!
 * @pixi/extensions - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/extensions is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e,n=function(){return n=Object.assign||function(e){for(var n,r=arguments,t=1,o=arguments.length;t<o;t++)for(var a in n=r[t])Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a]);return e},n.apply(this,arguments)};exports.ExtensionType=void 0,(e=exports.ExtensionType||(exports.ExtensionType={})).Application="application",e.RendererPlugin="renderer-webgl-plugin",e.<PERSON>vas<PERSON>endererPlugin="renderer-canvas-plugin",e.<PERSON>="loader",e.<PERSON>="load-parser",e.<PERSON>solve<PERSON>arser="resolve-parser",e.<PERSON>="cache-parser",e.<PERSON>="detection-parser";var r=function(e){if("function"==typeof e||"object"==typeof e&&e.extension){var r="object"!=typeof e.extension?{type:e.extension}:e.extension;e=n(n({},r),{ref:e})}if("object"!=typeof e)throw new Error("Invalid extension type");return"string"==typeof(e=n({},e)).type&&(e.type=[e.type]),e},t={_addHandlers:null,_removeHandlers:null,_queue:{},remove:function(){for(var e=arguments,n=this,t=[],o=0;o<arguments.length;o++)t[o]=e[o];return t.map(r).forEach((function(e){e.type.forEach((function(r){var t,o;return null===(o=(t=n._removeHandlers)[r])||void 0===o?void 0:o.call(t,e)}))})),this},add:function(){for(var e=arguments,n=this,t=[],o=0;o<arguments.length;o++)t[o]=e[o];return t.map(r).forEach((function(e){e.type.forEach((function(r){var t=n._addHandlers,o=n._queue;t[r]?t[r](e):(o[r]=o[r]||[],o[r].push(e))}))})),this},handle:function(e,n,r){var t=this._addHandlers=this._addHandlers||{},o=this._removeHandlers=this._removeHandlers||{};t[e]=n,o[e]=r;var a=this._queue;return a[e]&&(a[e].forEach((function(e){return n(e)})),delete a[e]),this},handleByMap:function(e,n){return this.handle(e,(function(e){n[e.name]=e.ref}),(function(e){delete n[e.name]}))},handleByList:function(e,n){return this.handle(e,(function(r){var t,o;n.includes(r.ref)||(n.push(r.ref),e===exports.ExtensionType.Loader&&(null===(o=(t=r.ref).add)||void 0===o||o.call(t)))}),(function(e){var r=n.indexOf(e.ref);-1!==r&&n.splice(r,1)}))}};exports.extensions=t;
//# sourceMappingURL=extensions.min.js.map
