/*!
 * @pixi/constants - v6.5.10
 * Compiled Thu, 06 Jul 2023 15:25:11 UTC
 *
 * @pixi/constants is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */
this.PIXI=this.PIXI||{};var _pixi_constants=function(E){"use strict";var _,T,R,N,A,I,S,O,L,P,U,D,M,G,C,B,F,H,Y,i;return E.ENV=void 0,(_=E.ENV||(E.ENV={}))[_.WEBGL_LEGACY=0]="WEBGL_LEGACY",_[_.WEBGL=1]="WEBGL",_[_.WEBGL2=2]="WEBGL2",E.RENDERER_TYPE=void 0,(T=E.RENDERER_TYPE||(E.RENDERER_TYPE={}))[T.UNKNOWN=0]="UNKNOWN",T[T.WEBGL=1]="WEBGL",T[T.CANVAS=2]="CANVAS",E.BUFFER_BITS=void 0,(R=E.BUFFER_BITS||(E.BUFFER_BITS={}))[R.COLOR=16384]="COLOR",R[R.DEPTH=256]="DEPTH",R[R.STENCIL=1024]="STENCIL",E.BLEND_MODES=void 0,(N=E.BLEND_MODES||(E.BLEND_MODES={}))[N.NORMAL=0]="NORMAL",N[N.ADD=1]="ADD",N[N.MULTIPLY=2]="MULTIPLY",N[N.SCREEN=3]="SCREEN",N[N.OVERLAY=4]="OVERLAY",N[N.DARKEN=5]="DARKEN",N[N.LIGHTEN=6]="LIGHTEN",N[N.COLOR_DODGE=7]="COLOR_DODGE",N[N.COLOR_BURN=8]="COLOR_BURN",N[N.HARD_LIGHT=9]="HARD_LIGHT",N[N.SOFT_LIGHT=10]="SOFT_LIGHT",N[N.DIFFERENCE=11]="DIFFERENCE",N[N.EXCLUSION=12]="EXCLUSION",N[N.HUE=13]="HUE",N[N.SATURATION=14]="SATURATION",N[N.COLOR=15]="COLOR",N[N.LUMINOSITY=16]="LUMINOSITY",N[N.NORMAL_NPM=17]="NORMAL_NPM",N[N.ADD_NPM=18]="ADD_NPM",N[N.SCREEN_NPM=19]="SCREEN_NPM",N[N.NONE=20]="NONE",N[N.SRC_OVER=0]="SRC_OVER",N[N.SRC_IN=21]="SRC_IN",N[N.SRC_OUT=22]="SRC_OUT",N[N.SRC_ATOP=23]="SRC_ATOP",N[N.DST_OVER=24]="DST_OVER",N[N.DST_IN=25]="DST_IN",N[N.DST_OUT=26]="DST_OUT",N[N.DST_ATOP=27]="DST_ATOP",N[N.ERASE=26]="ERASE",N[N.SUBTRACT=28]="SUBTRACT",N[N.XOR=29]="XOR",E.DRAW_MODES=void 0,(A=E.DRAW_MODES||(E.DRAW_MODES={}))[A.POINTS=0]="POINTS",A[A.LINES=1]="LINES",A[A.LINE_LOOP=2]="LINE_LOOP",A[A.LINE_STRIP=3]="LINE_STRIP",A[A.TRIANGLES=4]="TRIANGLES",A[A.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",A[A.TRIANGLE_FAN=6]="TRIANGLE_FAN",E.FORMATS=void 0,(I=E.FORMATS||(E.FORMATS={}))[I.RGBA=6408]="RGBA",I[I.RGB=6407]="RGB",I[I.RG=33319]="RG",I[I.RED=6403]="RED",I[I.RGBA_INTEGER=36249]="RGBA_INTEGER",I[I.RGB_INTEGER=36248]="RGB_INTEGER",I[I.RG_INTEGER=33320]="RG_INTEGER",I[I.RED_INTEGER=36244]="RED_INTEGER",I[I.ALPHA=6406]="ALPHA",I[I.LUMINANCE=6409]="LUMINANCE",I[I.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",I[I.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",I[I.DEPTH_STENCIL=34041]="DEPTH_STENCIL",E.TARGETS=void 0,(S=E.TARGETS||(E.TARGETS={}))[S.TEXTURE_2D=3553]="TEXTURE_2D",S[S.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",S[S.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY",S[S.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",S[S.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",S[S.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",S[S.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",S[S.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",S[S.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",E.TYPES=void 0,(O=E.TYPES||(E.TYPES={}))[O.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",O[O.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",O[O.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",O[O.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",O[O.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",O[O.UNSIGNED_INT=5125]="UNSIGNED_INT",O[O.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",O[O.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",O[O.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",O[O.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",O[O.BYTE=5120]="BYTE",O[O.SHORT=5122]="SHORT",O[O.INT=5124]="INT",O[O.FLOAT=5126]="FLOAT",O[O.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV",O[O.HALF_FLOAT=36193]="HALF_FLOAT",E.SAMPLER_TYPES=void 0,(L=E.SAMPLER_TYPES||(E.SAMPLER_TYPES={}))[L.FLOAT=0]="FLOAT",L[L.INT=1]="INT",L[L.UINT=2]="UINT",E.SCALE_MODES=void 0,(P=E.SCALE_MODES||(E.SCALE_MODES={}))[P.NEAREST=0]="NEAREST",P[P.LINEAR=1]="LINEAR",E.WRAP_MODES=void 0,(U=E.WRAP_MODES||(E.WRAP_MODES={}))[U.CLAMP=33071]="CLAMP",U[U.REPEAT=10497]="REPEAT",U[U.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT",E.MIPMAP_MODES=void 0,(D=E.MIPMAP_MODES||(E.MIPMAP_MODES={}))[D.OFF=0]="OFF",D[D.POW2=1]="POW2",D[D.ON=2]="ON",D[D.ON_MANUAL=3]="ON_MANUAL",E.ALPHA_MODES=void 0,(M=E.ALPHA_MODES||(E.ALPHA_MODES={}))[M.NPM=0]="NPM",M[M.UNPACK=1]="UNPACK",M[M.PMA=2]="PMA",M[M.NO_PREMULTIPLIED_ALPHA=0]="NO_PREMULTIPLIED_ALPHA",M[M.PREMULTIPLY_ON_UPLOAD=1]="PREMULTIPLY_ON_UPLOAD",M[M.PREMULTIPLY_ALPHA=2]="PREMULTIPLY_ALPHA",M[M.PREMULTIPLIED_ALPHA=2]="PREMULTIPLIED_ALPHA",E.CLEAR_MODES=void 0,(G=E.CLEAR_MODES||(E.CLEAR_MODES={}))[G.NO=0]="NO",G[G.YES=1]="YES",G[G.AUTO=2]="AUTO",G[G.BLEND=0]="BLEND",G[G.CLEAR=1]="CLEAR",G[G.BLIT=2]="BLIT",E.GC_MODES=void 0,(C=E.GC_MODES||(E.GC_MODES={}))[C.AUTO=0]="AUTO",C[C.MANUAL=1]="MANUAL",E.PRECISION=void 0,(B=E.PRECISION||(E.PRECISION={})).LOW="lowp",B.MEDIUM="mediump",B.HIGH="highp",E.MASK_TYPES=void 0,(F=E.MASK_TYPES||(E.MASK_TYPES={}))[F.NONE=0]="NONE",F[F.SCISSOR=1]="SCISSOR",F[F.STENCIL=2]="STENCIL",F[F.SPRITE=3]="SPRITE",F[F.COLOR=4]="COLOR",E.COLOR_MASK_BITS=void 0,(H=E.COLOR_MASK_BITS||(E.COLOR_MASK_BITS={}))[H.RED=1]="RED",H[H.GREEN=2]="GREEN",H[H.BLUE=4]="BLUE",H[H.ALPHA=8]="ALPHA",E.MSAA_QUALITY=void 0,(Y=E.MSAA_QUALITY||(E.MSAA_QUALITY={}))[Y.NONE=0]="NONE",Y[Y.LOW=2]="LOW",Y[Y.MEDIUM=4]="MEDIUM",Y[Y.HIGH=8]="HIGH",E.BUFFER_TYPE=void 0,(i=E.BUFFER_TYPE||(E.BUFFER_TYPE={}))[i.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",i[i.ARRAY_BUFFER=34962]="ARRAY_BUFFER",i[i.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER",Object.defineProperty(E,"__esModule",{value:!0}),E}({});Object.assign(this.PIXI,_pixi_constants);
//# sourceMappingURL=constants.min.js.map
