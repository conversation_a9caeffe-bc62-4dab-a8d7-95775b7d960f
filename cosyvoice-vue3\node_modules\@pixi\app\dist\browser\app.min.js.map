{"version": 3, "file": "app.min.js", "sources": ["../../src/ResizePlugin.ts", "../../src/Application.ts", "../../src/index.ts"], "sourcesContent": ["import type { ExtensionMetadata, Renderer } from '@pixi/core';\nimport { ExtensionType } from '@pixi/core';\nimport type { IApplicationOptions } from './Application';\n\ntype ResizeableRenderer = Pick<Renderer, 'resize'>;\n\n/**\n * Middleware for for Application's resize functionality\n * @private\n * @class\n */\nexport class ResizePlugin\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Application;\n\n    public static resizeTo: Window | HTMLElement;\n    public static resize: () => void;\n    public static renderer: ResizeableRenderer;\n    public static queueResize: () => void;\n    private static _resizeId: number;\n    private static _resizeTo: Window | HTMLElement;\n    private static cancelResize: () => void;\n\n    /**\n     * Initialize the plugin with scope of application instance\n     * @static\n     * @private\n     * @param {object} [options] - See application options\n     */\n    static init(options?: IApplicationOptions): void\n    {\n        Object.defineProperty(this, 'resizeTo',\n            /**\n             * The HTML element or window to automatically resize the\n             * renderer's view element to match width and height.\n             * @member {Window|HTMLElement}\n             * @name resizeTo\n             * @memberof PIXI.Application#\n             */\n            {\n                set(dom: Window | HTMLElement)\n                {\n                    globalThis.removeEventListener('resize', this.queueResize);\n                    this._resizeTo = dom;\n                    if (dom)\n                    {\n                        globalThis.addEventListener('resize', this.queueResize);\n                        this.resize();\n                    }\n                },\n                get()\n                {\n                    return this._resizeTo;\n                },\n            });\n\n        /**\n         * Resize is throttled, so it's safe to call this multiple times per frame and it'll\n         * only be called once.\n         * @memberof PIXI.Application#\n         * @method queueResize\n         * @private\n         */\n        this.queueResize = (): void =>\n        {\n            if (!this._resizeTo)\n            {\n                return;\n            }\n\n            this.cancelResize();\n\n            // // Throttle resize events per raf\n            this._resizeId = requestAnimationFrame(() => this.resize());\n        };\n\n        /**\n         * Cancel the resize queue.\n         * @memberof PIXI.Application#\n         * @method cancelResize\n         * @private\n         */\n        this.cancelResize = (): void =>\n        {\n            if (this._resizeId)\n            {\n                cancelAnimationFrame(this._resizeId);\n                this._resizeId = null;\n            }\n        };\n\n        /**\n         * Execute an immediate resize on the renderer, this is not\n         * throttled and can be expensive to call many times in a row.\n         * Will resize only if `resizeTo` property is set.\n         * @memberof PIXI.Application#\n         * @method resize\n         */\n        this.resize = (): void =>\n        {\n            if (!this._resizeTo)\n            {\n                return;\n            }\n\n            // clear queue resize\n            this.cancelResize();\n\n            let width: number;\n            let height: number;\n\n            // Resize to the window\n            if (this._resizeTo === globalThis.window)\n            {\n                width = globalThis.innerWidth;\n                height = globalThis.innerHeight;\n            }\n            // Resize to other HTML entities\n            else\n            {\n                const { clientWidth, clientHeight } = this._resizeTo as HTMLElement;\n\n                width = clientWidth;\n                height = clientHeight;\n            }\n\n            this.renderer.resize(width, height);\n        };\n\n        // On resize\n        this._resizeId = null;\n        this._resizeTo = null;\n        this.resizeTo = options.resizeTo || null;\n    }\n\n    /**\n     * Clean up the ticker, scoped to application\n     * @static\n     * @private\n     */\n    static destroy(): void\n    {\n        globalThis.removeEventListener('resize', this.queueResize);\n        this.cancelResize();\n        this.cancelResize = null;\n        this.queueResize = null;\n        this.resizeTo = null;\n        this.resize = null;\n    }\n}\n", "import { Container } from '@pixi/display';\nimport { autoDetect<PERSON>enderer, extensions, ExtensionType } from '@pixi/core';\n\nimport type { Rectangle } from '@pixi/math';\nimport type { Ren<PERSON><PERSON>, IRendererOptionsAuto, AbstractRenderer } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\nimport { deprecation } from '@pixi/utils';\n\n/**\n * Any plugin that's usable for Application should contain these methods.\n * @memberof PIXI\n */\nexport interface IApplicationPlugin\n{\n    /**\n     * Called when Application is constructed, scoped to Application instance.\n     * Passes in `options` as the only argument, which are Application constructor options.\n     * @param {object} options - Application options.\n     */\n    init(options: IApplicationOptions): void;\n    /** Called when destroying Application, scoped to Application instance. */\n    destroy(): void;\n}\n\n/**\n * Application options supplied to constructor.\n * @memberof PIXI\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IApplicationOptions extends IRendererOptionsAuto, GlobalMixins.IApplicationOptions {}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Application extends GlobalMixins.Application {}\n\n/**\n * Convenience class to create a new PIXI application.\n *\n * This class automatically creates the renderer, ticker and root container.\n * @example\n * // Create the application\n * const app = new PIXI.Application();\n *\n * // Add the view to the DOM\n * document.body.appendChild(app.view);\n *\n * // ex, add display objects\n * app.stage.addChild(PIXI.Sprite.from('something.png'));\n * @class\n * @memberof PIXI\n */\nexport class Application\n{\n    /** Collection of installed plugins. */\n    static _plugins: IApplicationPlugin[] = [];\n\n    /**\n     * The root display container that's rendered.\n     * @member {PIXI.Container}\n     */\n    public stage: Container = new Container();\n\n    /**\n     * WebGL renderer if available, otherwise CanvasRenderer.\n     * @member {PIXI.Renderer|PIXI.CanvasRenderer}\n     */\n    public renderer: Renderer | AbstractRenderer;\n\n    /**\n     * @param {PIXI.IApplicationOptions} [options] - The optional application and renderer parameters.\n     * @param {boolean} [options.antialias=false] -\n     *  **WebGL Only.** Whether to enable anti-aliasing. This may affect performance.\n     * @param {boolean} [options.autoDensity=false] -\n     *  Whether the CSS dimensions of the renderer's view should be resized automatically.\n     * @param {boolean} [options.autoStart=true] - Automatically starts the rendering after the construction.\n     *  **Note**: Setting this parameter to false does NOT stop the shared ticker even if you set\n     *  `options.sharedTicker` to `true` in case that it is already started. Stop it by your own.\n     * @param {number} [options.backgroundAlpha=1] -\n     *  Transparency of the background color, value from `0` (fully transparent) to `1` (fully opaque).\n     * @param {number} [options.backgroundColor=0x000000] -\n     *  The background color used to clear the canvas. It accepts hex numbers (e.g. `0xff0000`).\n     * @param {boolean} [options.clearBeforeRender=true] - Whether to clear the canvas before new render passes.\n     * @param {PIXI.IRenderingContext} [options.context] - **WebGL Only.** User-provided WebGL rendering context object.\n     * @param {boolean} [options.forceCanvas=false] -\n     *  Force using {@link PIXI.CanvasRenderer}, even if WebGL is available. This option only is available when\n     *  using **pixi.js-legacy** or **@pixi/canvas-renderer** packages, otherwise it is ignored.\n     * @param {number} [options.height=600] - The height of the renderer's view.\n     * @param {string} [options.powerPreference] -\n     *  **WebGL Only.** A hint indicating what configuration of GPU is suitable for the WebGL context,\n     *  can be `'default'`, `'high-performance'` or `'low-power'`.\n     *  Setting to `'high-performance'` will prioritize rendering performance over power consumption,\n     *  while setting to `'low-power'` will prioritize power saving over rendering performance.\n     * @param {boolean} [options.premultipliedAlpha=true] -\n     *  **WebGL Only.** Whether the compositor will assume the drawing buffer contains colors with premultiplied alpha.\n     * @param {boolean} [options.preserveDrawingBuffer=false] -\n     *  **WebGL Only.** Whether to enable drawing buffer preservation. If enabled, the drawing buffer will preserve\n     *  its value until cleared or overwritten. Enable this if you need to call `toDataUrl` on the WebGL context.\n     * @param {Window|HTMLElement} [options.resizeTo] - Element to automatically resize stage to.\n     * @param {number} [options.resolution=PIXI.settings.RESOLUTION] -\n     *  The resolution / device pixel ratio of the renderer.\n     * @param {boolean} [options.sharedLoader=false] - `true` to use PIXI.Loader.shared, `false` to create new Loader.\n     * @param {boolean} [options.sharedTicker=false] - `true` to use PIXI.Ticker.shared, `false` to create new ticker.\n     *  If set to `false`, you cannot register a handler to occur before anything that runs on the shared ticker.\n     *  The system ticker will always run before both the shared ticker and the app ticker.\n     * @param {boolean} [options.transparent] -\n     *  **Deprecated since 6.0.0, Use `backgroundAlpha` instead.** \\\n     *  `true` sets `backgroundAlpha` to `0`, `false` sets `backgroundAlpha` to `1`.\n     * @param {boolean|'notMultiplied'} [options.useContextAlpha=true] -\n     *  Pass-through value for canvas' context attribute `alpha`. This option is for cases where the\n     *  canvas needs to be opaque, possibly for performance reasons on some older devices.\n     *  If you want to set transparency, please use `backgroundAlpha`. \\\n     *  **WebGL Only:** When set to `'notMultiplied'`, the canvas' context attribute `alpha` will be\n     *  set to `true` and `premultipliedAlpha` will be to `false`.\n     * @param {HTMLCanvasElement} [options.view=null] -\n     *  The canvas to use as the view. If omitted, a new canvas will be created.\n     * @param {number} [options.width=800] - The width of the renderer's view.\n     */\n    constructor(options?: IApplicationOptions)\n    {\n        // The default options\n        options = Object.assign({\n            forceCanvas: false,\n        }, options);\n\n        this.renderer = autoDetectRenderer(options);\n\n        // install plugins here\n        Application._plugins.forEach((plugin) =>\n        {\n            plugin.init.call(this, options);\n        });\n    }\n\n    /**\n     * Use the {@link PIXI.extensions.add} API to register plugins.\n     * @deprecated since 6.5.0\n     * @static\n     * @param {PIXI.IApplicationPlugin} plugin - Plugin being installed\n     */\n    static registerPlugin(plugin: IApplicationPlugin): void\n    {\n        // #if _DEBUG\n        deprecation('6.5.0', 'Application.registerPlugin() is deprecated, use extensions.add()');\n        // #endif\n        extensions.add({\n            type: ExtensionType.Application,\n            ref: plugin,\n        });\n    }\n\n    /** Render the current stage. */\n    public render(): void\n    {\n        this.renderer.render(this.stage);\n    }\n\n    /**\n     * Reference to the renderer's canvas element.\n     * @member {HTMLCanvasElement}\n     * @readonly\n     */\n    get view(): HTMLCanvasElement\n    {\n        return this.renderer.view;\n    }\n\n    /**\n     * Reference to the renderer's screen rectangle. Its safe to use as `filterArea` or `hitArea` for the whole screen.\n     * @member {PIXI.Rectangle}\n     * @readonly\n     */\n    get screen(): Rectangle\n    {\n        return this.renderer.screen;\n    }\n\n    /**\n     * Destroy and don't use after this.\n     * @param {boolean} [removeView=false] - Automatically remove canvas from DOM.\n     * @param {object|boolean} [stageOptions] - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [stageOptions.children=false] - if set to true, all the children will have their destroy\n     *  method called as well. 'stageOptions' will be passed on to those calls.\n     * @param {boolean} [stageOptions.texture=false] - Only used for child Sprites if stageOptions.children is set\n     *  to true. Should it destroy the texture of the child sprite\n     * @param {boolean} [stageOptions.baseTexture=false] - Only used for child Sprites if stageOptions.children is set\n     *  to true. Should it destroy the base texture of the child sprite\n     */\n    public destroy(removeView?: boolean, stageOptions?: IDestroyOptions | boolean): void\n    {\n        // Destroy plugins in the opposite order\n        // which they were constructed\n        const plugins = Application._plugins.slice(0);\n\n        plugins.reverse();\n        plugins.forEach((plugin) =>\n        {\n            plugin.destroy.call(this);\n        });\n\n        this.stage.destroy(stageOptions);\n        this.stage = null;\n\n        this.renderer.destroy(removeView);\n        this.renderer = null;\n    }\n}\n\nextensions.handleByList(ExtensionType.Application, Application._plugins);\n", "import { ResizePlugin } from './ResizePlugin';\nimport { extensions } from '@pixi/core';\n\nextensions.add(ResizePlugin);\n\nexport * from './Application';\nexport { ResizePlugin };\n"], "names": ["ResizePlugin", "init", "options", "_this", "this", "Object", "defineProperty", "set", "dom", "globalThis", "removeEventListener", "queueResize", "_resizeTo", "addEventListener", "resize", "get", "cancelResize", "_resizeId", "requestAnimationFrame", "cancelAnimationFrame", "width", "height", "window", "innerWidth", "innerHeight", "_a", "clientWidth", "clientHeight", "renderer", "resizeTo", "destroy", "extension", "ExtensionType", "Application", "stage", "Container", "assign", "forceCanvas", "autoDetect<PERSON><PERSON><PERSON>", "_plugins", "for<PERSON>ach", "plugin", "call", "registerPlugin", "extensions", "add", "type", "ref", "prototype", "render", "view", "screen", "<PERSON><PERSON><PERSON><PERSON>", "stageOptions", "plugins", "slice", "reverse", "core", "handleByList"], "mappings": ";;;;;;;mEAWA,IAAAA,EAAA,WAAA,SAAAA,KA2IA,OAxHWA,EAAIC,KAAX,SAAYC,GAAZ,IAwGCC,EAAAC,KAtGGC,OAAOC,eAAeF,KAAM,WAQxB,CACIG,IAAA,SAAIC,GAEAC,WAAWC,oBAAoB,SAAUN,KAAKO,aAC9CP,KAAKQ,UAAYJ,EACbA,IAEAC,WAAWI,iBAAiB,SAAUT,KAAKO,aAC3CP,KAAKU,WAGbC,IAAG,WAEC,OAAOX,KAAKQ,aAWxBR,KAAKO,YAAc,WAEVR,EAAKS,YAKVT,EAAKa,eAGLb,EAAKc,UAAYC,uBAAsB,WAAM,OAAAf,EAAKW,cAStDV,KAAKY,aAAe,WAEZb,EAAKc,YAELE,qBAAqBhB,EAAKc,WAC1Bd,EAAKc,UAAY,OAWzBb,KAAKU,OAAS,WAEV,GAAKX,EAAKS,UAAV,CAQA,IAAIQ,EACAC,EAGJ,GANAlB,EAAKa,eAMDb,EAAKS,YAAcH,WAAWa,OAE9BF,EAAQX,WAAWc,WACnBF,EAASZ,WAAWe,gBAIxB,CACU,IAAAC,EAAgCtB,EAAKS,UAE3CQ,EAFmBK,EAAAC,YAGnBL,EAHiCI,EAAAE,aAMrCxB,EAAKyB,SAASd,OAAOM,EAAOC,KAIhCjB,KAAKa,UAAY,KACjBb,KAAKQ,UAAY,KACjBR,KAAKyB,SAAW3B,EAAQ2B,UAAY,MAQjC7B,EAAA8B,QAAP,WAEIrB,WAAWC,oBAAoB,SAAUN,KAAKO,aAC9CP,KAAKY,eACLZ,KAAKY,aAAe,KACpBZ,KAAKO,YAAc,KACnBP,KAAKyB,SAAW,KAChBzB,KAAKU,OAAS,MAtIXd,EAAA+B,UAA+BC,EAAaA,cAACC,YAwIvDjC,KCpGDiC,EAAA,WAkEI,SAAAA,EAAY/B,GAAZ,IAcCC,EAAAC,KAvEMA,KAAA8B,MAAmB,IAAIC,EAAAA,UA4D1BjC,EAAUG,OAAO+B,OAAO,CACpBC,aAAa,GACdnC,GAEHE,KAAKwB,SAAWU,qBAAmBpC,GAGnC+B,EAAYM,SAASC,SAAQ,SAACC,GAE1BA,EAAOxC,KAAKyC,KAAKvC,EAAMD,MA6EnC,OAnEW+B,EAAcU,eAArB,SAAsBF,GAKlBG,EAAAA,WAAWC,IAAI,CACXC,KAAMd,EAAaA,cAACC,YACpBc,IAAKN,KAKNR,EAAAe,UAAAC,OAAP,WAEI7C,KAAKwB,SAASqB,OAAO7C,KAAK8B,QAQ9B7B,OAAAC,eAAI2B,EAAIe,UAAA,OAAA,CAARjC,IAAA,WAEI,OAAOX,KAAKwB,SAASsB,sCAQzB7C,OAAAC,eAAI2B,EAAMe,UAAA,SAAA,CAAVjC,IAAA,WAEI,OAAOX,KAAKwB,SAASuB,wCAelBlB,EAAAe,UAAAlB,QAAP,SAAesB,EAAsBC,GAArC,IAiBClD,EAAAC,KAbSkD,EAAUrB,EAAYM,SAASgB,MAAM,GAE3CD,EAAQE,UACRF,EAAQd,SAAQ,SAACC,GAEbA,EAAOX,QAAQY,KAAKvC,MAGxBC,KAAK8B,MAAMJ,QAAQuB,GACnBjD,KAAK8B,MAAQ,KAEb9B,KAAKwB,SAASE,QAAQsB,GACtBhD,KAAKwB,SAAW,MAtJbK,EAAQM,SAAyB,GAwJ3CN,YAESwB,EAAAb,WAACc,aAAa1B,EAAaA,cAACC,YAAaA,EAAYM,UC5M/DK,EAAAA,WAAWC,IAAI7C"}