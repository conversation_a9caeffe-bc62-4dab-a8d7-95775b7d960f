{"version": 3, "file": "text.mjs", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../src/const.ts", "../../src/TextStyle.ts", "../../src/TextMetrics.ts", "../../src/Text.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * Constants that define the type of gradient on text.\n * @static\n * @constant\n * @name TEXT_GRADIENT\n * @memberof PIXI\n * @type {object}\n * @property {number} LINEAR_VERTICAL Vertical gradient\n * @property {number} LINEAR_HORIZONTAL Linear gradient\n */\nexport enum TEXT_GRADIENT\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    LINEAR_VERTICAL = 0,\n    LINEAR_HORIZONTAL = 1\n}\n", "// disabling eslint for now, going to rewrite this in v5\n/* eslint-disable */\n\nimport { TEXT_GRADIENT } from './const';\nimport { hex2string } from '@pixi/utils';\n\nexport type TextStyleAlign = 'left'|'center'|'right'|'justify';\nexport type TextStyleFill = string|string[]|number|number[]|CanvasGradient|CanvasPattern;\nexport type TextStyleFontStyle = 'normal'|'italic'|'oblique';\nexport type TextStyleFontVariant = 'normal'|'small-caps';\nexport type TextStyleFontWeight = 'normal'|'bold'|'bolder'|'lighter'|'100'|'200'|'300'|'400'|'500'|'600'|'700'|'800'|'900';\nexport type TextStyleLineJoin = 'miter'|'round'|'bevel';\nexport type TextStyleTextBaseline = 'alphabetic'|'top'|'hanging'|'middle'|'ideographic'|'bottom';\nexport type TextStyleWhiteSpace = 'normal'|'pre'|'pre-line';\n\nexport interface ITextStyle {\n    align: TextStyleAlign;\n    breakWords: boolean;\n    dropShadow: boolean;\n    dropShadowAlpha: number;\n    dropShadowAngle: number;\n    dropShadowBlur: number;\n    dropShadowColor: string|number;\n    dropShadowDistance: number;\n    fill: TextStyleFill;\n    fillGradientType: TEXT_GRADIENT;\n    fillGradientStops: number[];\n    fontFamily: string | string[];\n    fontSize: number | string;\n    fontStyle: TextStyleFontStyle;\n    fontVariant: TextStyleFontVariant;\n    fontWeight: TextStyleFontWeight;\n    letterSpacing: number;\n    lineHeight: number;\n    lineJoin: TextStyleLineJoin;\n    miterLimit: number;\n    padding: number;\n    stroke: string|number;\n    strokeThickness: number;\n    textBaseline: TextStyleTextBaseline;\n    trim: boolean;\n    whiteSpace: TextStyleWhiteSpace;\n    wordWrap: boolean;\n    wordWrapWidth: number;\n    leading: number;\n}\n\nconst defaultStyle: ITextStyle = {\n    align: 'left',\n    breakWords: false,\n    dropShadow: false,\n    dropShadowAlpha: 1,\n    dropShadowAngle: Math.PI / 6,\n    dropShadowBlur: 0,\n    dropShadowColor: 'black',\n    dropShadowDistance: 5,\n    fill: 'black',\n    fillGradientType: TEXT_GRADIENT.LINEAR_VERTICAL,\n    fillGradientStops: [],\n    fontFamily: 'Arial',\n    fontSize: 26,\n    fontStyle: 'normal',\n    fontVariant: 'normal',\n    fontWeight: 'normal',\n    letterSpacing: 0,\n    lineHeight: 0,\n    lineJoin: 'miter',\n    miterLimit: 10,\n    padding: 0,\n    stroke: 'black',\n    strokeThickness: 0,\n    textBaseline: 'alphabetic',\n    trim: false,\n    whiteSpace: 'pre',\n    wordWrap: false,\n    wordWrapWidth: 100,\n    leading: 0,\n};\n\nconst genericFontFamilies = [\n    'serif',\n    'sans-serif',\n    'monospace',\n    'cursive',\n    'fantasy',\n    'system-ui',\n];\n\n/**\n * A TextStyle Object contains information to decorate a Text objects.\n *\n * An instance can be shared between multiple Text objects; then changing the style will update all text objects using it.\n *\n * A tool can be used to generate a text style [here](https://pixijs.io/pixi-text-style).\n *\n * @memberof PIXI\n */\nexport class TextStyle implements ITextStyle\n{\n    public styleID: number;\n\n    protected _align: TextStyleAlign;\n    protected _breakWords: boolean;\n    protected _dropShadow: boolean;\n    protected _dropShadowAlpha: number;\n    protected _dropShadowAngle: number;\n    protected _dropShadowBlur: number;\n    protected _dropShadowColor: string|number;\n    protected _dropShadowDistance: number;\n    protected _fill: TextStyleFill;\n    protected _fillGradientType: TEXT_GRADIENT;\n    protected _fillGradientStops: number[];\n    protected _fontFamily: string|string[];\n    protected _fontSize: number|string;\n    protected _fontStyle: TextStyleFontStyle;\n    protected _fontVariant: TextStyleFontVariant;\n    protected _fontWeight: TextStyleFontWeight;\n    protected _letterSpacing: number;\n    protected _lineHeight: number;\n    protected _lineJoin: TextStyleLineJoin;\n    protected _miterLimit: number;\n    protected _padding: number;\n    protected _stroke: string|number;\n    protected _strokeThickness: number;\n    protected _textBaseline: TextStyleTextBaseline;\n    protected _trim: boolean;\n    protected _whiteSpace: TextStyleWhiteSpace;\n    protected _wordWrap: boolean;\n    protected _wordWrapWidth: number;\n    protected _leading: number;\n\n    /**\n     * @param {object} [style] - The style parameters\n     * @param {string} [style.align='left'] - Alignment for multiline text ('left', 'center' or 'right'),\n     *  does not affect single line text\n     * @param {boolean} [style.breakWords=false] - Indicates if lines can be wrapped within words, it\n     *  needs wordWrap to be set to true\n     * @param {boolean} [style.dropShadow=false] - Set a drop shadow for the text\n     * @param {number} [style.dropShadowAlpha=1] - Set alpha for the drop shadow\n     * @param {number} [style.dropShadowAngle=Math.PI/6] - Set a angle of the drop shadow\n     * @param {number} [style.dropShadowBlur=0] - Set a shadow blur radius\n     * @param {string|number} [style.dropShadowColor='black'] - A fill style to be used on the dropshadow e.g 'red', '#00FF00'\n     * @param {number} [style.dropShadowDistance=5] - Set a distance of the drop shadow\n     * @param {string|string[]|number|number[]|CanvasGradient|CanvasPattern} [style.fill='black'] - A canvas\n     *  fillstyle that will be used on the text e.g 'red', '#00FF00'. Can be an array to create a gradient\n     *  eg ['#000000','#FFFFFF']\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle|MDN}\n     * @param {number} [style.fillGradientType=PIXI.TEXT_GRADIENT.LINEAR_VERTICAL] - If fill is an array of colours\n     *  to create a gradient, this can change the type/direction of the gradient. See {@link PIXI.TEXT_GRADIENT}\n     * @param {number[]} [style.fillGradientStops] - If fill is an array of colours to create a gradient, this array can set\n     * the stop points (numbers between 0 and 1) for the color, overriding the default behaviour of evenly spacing them.\n     * @param {string|string[]} [style.fontFamily='Arial'] - The font family\n     * @param {number|string} [style.fontSize=26] - The font size (as a number it converts to px, but as a string,\n     *  equivalents are '26px','20pt','160%' or '1.6em')\n     * @param {string} [style.fontStyle='normal'] - The font style ('normal', 'italic' or 'oblique')\n     * @param {string} [style.fontVariant='normal'] - The font variant ('normal' or 'small-caps')\n     * @param {string} [style.fontWeight='normal'] - The font weight ('normal', 'bold', 'bolder', 'lighter' and '100',\n     *  '200', '300', '400', '500', '600', '700', '800' or '900')\n     * @param {number} [style.leading=0] - The space between lines\n     * @param {number} [style.letterSpacing=0] - The amount of spacing between letters, default is 0\n     * @param {number} [style.lineHeight] - The line height, a number that represents the vertical space that a letter uses\n     * @param {string} [style.lineJoin='miter'] - The lineJoin property sets the type of corner created, it can resolve\n     *      spiked text issues. Possible values \"miter\" (creates a sharp corner), \"round\" (creates a round corner) or \"bevel\"\n     *      (creates a squared corner).\n     * @param {number} [style.miterLimit=10] - The miter limit to use when using the 'miter' lineJoin mode. This can reduce\n     *      or increase the spikiness of rendered text.\n     * @param {number} [style.padding=0] - Occasionally some fonts are cropped. Adding some padding will prevent this from\n     *     happening by adding padding to all sides of the text.\n     * @param {string|number} [style.stroke='black'] - A canvas fillstyle that will be used on the text stroke\n     *  e.g 'blue', '#FCFF00'\n     * @param {number} [style.strokeThickness=0] - A number that represents the thickness of the stroke.\n     *  Default is 0 (no stroke)\n     * @param {boolean} [style.trim=false] - Trim transparent borders\n     * @param {string} [style.textBaseline='alphabetic'] - The baseline of the text that is rendered.\n     * @param {string} [style.whiteSpace='pre'] - Determines whether newlines & spaces are collapsed or preserved \"normal\"\n     *      (collapse, collapse), \"pre\" (preserve, preserve) | \"pre-line\" (preserve, collapse). It needs wordWrap to be set to true\n     * @param {boolean} [style.wordWrap=false] - Indicates if word wrap should be used\n     * @param {number} [style.wordWrapWidth=100] - The width at which text will wrap, it needs wordWrap to be set to true\n     */\n    constructor(style?: Partial<ITextStyle>)\n    {\n        this.styleID = 0;\n\n        this.reset();\n\n        deepCopyProperties(this, style, style);\n    }\n\n    /**\n     * Creates a new TextStyle object with the same values as this one.\n     * Note that the only the properties of the object are cloned.\n     *\n     * @return New cloned TextStyle object\n     */\n    public clone(): TextStyle\n    {\n        const clonedProperties: Partial<ITextStyle> = {};\n\n        deepCopyProperties(clonedProperties, this, defaultStyle);\n\n        return new TextStyle(clonedProperties);\n    }\n\n    /** Resets all properties to the defaults specified in TextStyle.prototype._default */\n    public reset(): void\n    {\n        deepCopyProperties(this, defaultStyle, defaultStyle);\n    }\n\n    /**\n     * Alignment for multiline text ('left', 'center' or 'right'), does not affect single line text\n     *\n     * @member {string}\n     */\n    get align(): TextStyleAlign\n    {\n        return this._align;\n    }\n    set align(align: TextStyleAlign)\n    {\n        if (this._align !== align)\n        {\n            this._align = align;\n            this.styleID++;\n        }\n    }\n\n    /** Indicates if lines can be wrapped within words, it needs wordWrap to be set to true. */\n    get breakWords(): boolean\n    {\n        return this._breakWords;\n    }\n    set breakWords(breakWords: boolean)\n    {\n        if (this._breakWords !== breakWords)\n        {\n            this._breakWords = breakWords;\n            this.styleID++;\n        }\n    }\n\n    /** Set a drop shadow for the text. */\n    get dropShadow(): boolean\n    {\n        return this._dropShadow;\n    }\n    set dropShadow(dropShadow: boolean)\n    {\n        if (this._dropShadow !== dropShadow)\n        {\n            this._dropShadow = dropShadow;\n            this.styleID++;\n        }\n    }\n\n    /** Set alpha for the drop shadow. */\n    get dropShadowAlpha(): number\n    {\n        return this._dropShadowAlpha;\n    }\n    set dropShadowAlpha(dropShadowAlpha: number)\n    {\n        if (this._dropShadowAlpha !== dropShadowAlpha)\n        {\n            this._dropShadowAlpha = dropShadowAlpha;\n            this.styleID++;\n        }\n    }\n\n    /** Set a angle of the drop shadow. */\n    get dropShadowAngle(): number\n    {\n        return this._dropShadowAngle;\n    }\n    set dropShadowAngle(dropShadowAngle: number)\n    {\n        if (this._dropShadowAngle !== dropShadowAngle)\n        {\n            this._dropShadowAngle = dropShadowAngle;\n            this.styleID++;\n        }\n    }\n\n    /** Set a shadow blur radius. */\n    get dropShadowBlur(): number\n    {\n        return this._dropShadowBlur;\n    }\n    set dropShadowBlur(dropShadowBlur: number)\n    {\n        if (this._dropShadowBlur !== dropShadowBlur)\n        {\n            this._dropShadowBlur = dropShadowBlur;\n            this.styleID++;\n        }\n    }\n\n    /** A fill style to be used on the dropshadow e.g 'red', '#00FF00'. */\n    get dropShadowColor(): number | string\n    {\n        return this._dropShadowColor;\n    }\n    set dropShadowColor(dropShadowColor: number | string)\n    {\n        const outputColor = getColor(dropShadowColor);\n        if (this._dropShadowColor !== outputColor)\n        {\n            this._dropShadowColor = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /** Set a distance of the drop shadow. */\n    get dropShadowDistance(): number\n    {\n        return this._dropShadowDistance;\n    }\n    set dropShadowDistance(dropShadowDistance: number)\n    {\n        if (this._dropShadowDistance !== dropShadowDistance)\n        {\n            this._dropShadowDistance = dropShadowDistance;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A canvas fillstyle that will be used on the text e.g 'red', '#00FF00'.\n     *\n     * Can be an array to create a gradient eg ['#000000','#FFFFFF']\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle|MDN}\n     *\n     * @member {string|string[]|number|number[]|CanvasGradient|CanvasPattern}\n     */\n    get fill(): TextStyleFill\n    {\n        return this._fill;\n    }\n    set fill(fill: TextStyleFill)\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        // TODO: Not sure if getColor works properly with CanvasGradient and/or CanvasPattern, can't pass in\n        //       without casting here.\n        const outputColor = getColor(fill as any);\n        if (this._fill !== outputColor)\n        {\n            this._fill = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * If fill is an array of colours to create a gradient, this can change the type/direction of the gradient.\n     *\n     * @see PIXI.TEXT_GRADIENT\n     */\n    get fillGradientType(): TEXT_GRADIENT\n    {\n        return this._fillGradientType;\n    }\n    set fillGradientType(fillGradientType: TEXT_GRADIENT)\n    {\n        if (this._fillGradientType !== fillGradientType)\n        {\n            this._fillGradientType = fillGradientType;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * If fill is an array of colours to create a gradient, this array can set the stop points\n     * (numbers between 0 and 1) for the color, overriding the default behaviour of evenly spacing them.\n     */\n    get fillGradientStops(): number[]\n    {\n        return this._fillGradientStops;\n    }\n    set fillGradientStops(fillGradientStops: number[])\n    {\n        if (!areArraysEqual(this._fillGradientStops,fillGradientStops))\n        {\n            this._fillGradientStops = fillGradientStops;\n            this.styleID++;\n        }\n    }\n\n    /** The font family. */\n    get fontFamily(): string | string[]\n    {\n        return this._fontFamily;\n    }\n    set fontFamily(fontFamily: string | string[])\n    {\n        if (this.fontFamily !== fontFamily)\n        {\n            this._fontFamily = fontFamily;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font size\n     * (as a number it converts to px, but as a string, equivalents are '26px','20pt','160%' or '1.6em')\n     */\n    get fontSize(): number | string\n    {\n        return this._fontSize;\n    }\n    set fontSize(fontSize: number | string)\n    {\n        if (this._fontSize !== fontSize)\n        {\n            this._fontSize = fontSize;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font style\n     * ('normal', 'italic' or 'oblique')\n     *\n     * @member {string}\n     */\n    get fontStyle(): TextStyleFontStyle\n    {\n        return this._fontStyle;\n    }\n    set fontStyle(fontStyle: TextStyleFontStyle)\n    {\n        if (this._fontStyle !== fontStyle)\n        {\n            this._fontStyle = fontStyle;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font variant\n     * ('normal' or 'small-caps')\n     *\n     * @member {string}\n     */\n    get fontVariant(): TextStyleFontVariant\n    {\n        return this._fontVariant;\n    }\n    set fontVariant(fontVariant: TextStyleFontVariant)\n    {\n        if (this._fontVariant !== fontVariant)\n        {\n            this._fontVariant = fontVariant;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font weight\n     * ('normal', 'bold', 'bolder', 'lighter' and '100', '200', '300', '400', '500', '600', '700', 800' or '900')\n     *\n     * @member {string}\n     */\n    get fontWeight(): TextStyleFontWeight\n    {\n        return this._fontWeight;\n    }\n    set fontWeight(fontWeight: TextStyleFontWeight)\n    {\n        if (this._fontWeight !== fontWeight)\n        {\n            this._fontWeight = fontWeight;\n            this.styleID++;\n        }\n    }\n\n    /** The amount of spacing between letters, default is 0. */\n    get letterSpacing(): number\n    {\n        return this._letterSpacing;\n    }\n    set letterSpacing(letterSpacing: number)\n    {\n        if (this._letterSpacing !== letterSpacing)\n        {\n            this._letterSpacing = letterSpacing;\n            this.styleID++;\n        }\n    }\n\n    /** The line height, a number that represents the vertical space that a letter uses. */\n    get lineHeight(): number\n    {\n        return this._lineHeight;\n    }\n    set lineHeight(lineHeight: number)\n    {\n        if (this._lineHeight !== lineHeight)\n        {\n            this._lineHeight = lineHeight;\n            this.styleID++;\n        }\n    }\n\n    /** The space between lines. */\n    get leading(): number\n    {\n        return this._leading;\n    }\n    set leading(leading: number)\n    {\n        if (this._leading !== leading)\n        {\n            this._leading = leading;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The lineJoin property sets the type of corner created, it can resolve spiked text issues.\n     * Default is 'miter' (creates a sharp corner).\n     *\n     * @member {string}\n     */\n    get lineJoin(): TextStyleLineJoin\n    {\n        return this._lineJoin;\n    }\n    set lineJoin(lineJoin: TextStyleLineJoin)\n    {\n        if (this._lineJoin !== lineJoin)\n        {\n            this._lineJoin = lineJoin;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The miter limit to use when using the 'miter' lineJoin mode.\n     *\n     * This can reduce or increase the spikiness of rendered text.\n     */\n    get miterLimit(): number\n    {\n        return this._miterLimit;\n    }\n    set miterLimit(miterLimit: number)\n    {\n        if (this._miterLimit !== miterLimit)\n        {\n            this._miterLimit = miterLimit;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Occasionally some fonts are cropped. Adding some padding will prevent this from happening\n     * by adding padding to all sides of the text.\n     */\n    get padding(): number\n    {\n        return this._padding;\n    }\n    set padding(padding: number)\n    {\n        if (this._padding !== padding)\n        {\n            this._padding = padding;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A canvas fillstyle that will be used on the text stroke\n     * e.g 'blue', '#FCFF00'\n     */\n    get stroke(): string | number\n    {\n        return this._stroke;\n    }\n    set stroke(stroke: string | number)\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        const outputColor = getColor(stroke);\n        if (this._stroke !== outputColor)\n        {\n            this._stroke = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A number that represents the thickness of the stroke.\n     *\n     * @default 0\n     */\n    get strokeThickness(): number\n    {\n        return this._strokeThickness;\n    }\n    set strokeThickness(strokeThickness: number)\n    {\n        if (this._strokeThickness !== strokeThickness)\n        {\n            this._strokeThickness = strokeThickness;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The baseline of the text that is rendered.\n     *\n     * @member {string}\n     */\n    get textBaseline(): TextStyleTextBaseline\n    {\n        return this._textBaseline;\n    }\n    set textBaseline(textBaseline: TextStyleTextBaseline)\n    {\n        if (this._textBaseline !== textBaseline)\n        {\n            this._textBaseline = textBaseline;\n            this.styleID++;\n        }\n    }\n\n    /** Trim transparent borders. */\n    get trim(): boolean\n    {\n        return this._trim;\n    }\n    set trim(trim: boolean)\n    {\n        if (this._trim !== trim)\n        {\n            this._trim = trim;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * How newlines and spaces should be handled.\n     * Default is 'pre' (preserve, preserve).\n     *\n     *  value       | New lines     |   Spaces\n     *  ---         | ---           |   ---\n     * 'normal'     | Collapse      |   Collapse\n     * 'pre'        | Preserve      |   Preserve\n     * 'pre-line'   | Preserve      |   Collapse\n     *\n     * @member {string}\n     */\n    get whiteSpace(): TextStyleWhiteSpace\n    {\n        return this._whiteSpace;\n    }\n    set whiteSpace(whiteSpace: TextStyleWhiteSpace)\n    {\n        if (this._whiteSpace !== whiteSpace)\n        {\n            this._whiteSpace = whiteSpace;\n            this.styleID++;\n        }\n    }\n\n    /** Indicates if word wrap should be used. */\n    get wordWrap(): boolean\n    {\n        return this._wordWrap;\n    }\n    set wordWrap(wordWrap: boolean)\n    {\n        if (this._wordWrap !== wordWrap)\n        {\n            this._wordWrap = wordWrap;\n            this.styleID++;\n        }\n    }\n\n    /** The width at which text will wrap, it needs wordWrap to be set to true. */\n    get wordWrapWidth(): number\n    {\n        return this._wordWrapWidth;\n    }\n    set wordWrapWidth(wordWrapWidth: number)\n    {\n        if (this._wordWrapWidth !== wordWrapWidth)\n        {\n            this._wordWrapWidth = wordWrapWidth;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Generates a font style string to use for `TextMetrics.measureFont()`.\n     *\n     * @return Font style string, for passing to `TextMetrics.measureFont()`\n     */\n    public toFontString(): string\n    {\n        // build canvas api font setting from individual components. Convert a numeric this.fontSize to px\n        const fontSizeString = (typeof this.fontSize === 'number') ? `${this.fontSize}px` : this.fontSize;\n\n        // Clean-up fontFamily property by quoting each font name\n        // this will support font names with spaces\n        let fontFamilies: string|string[] = this.fontFamily;\n\n        if (!Array.isArray(this.fontFamily))\n        {\n            fontFamilies = this.fontFamily.split(',');\n        }\n\n        for (let i = fontFamilies.length - 1; i >= 0; i--)\n        {\n            // Trim any extra white-space\n            let fontFamily = fontFamilies[i].trim();\n\n            // Check if font already contains strings\n            if (!(/([\\\"\\'])[^\\'\\\"]+\\1/).test(fontFamily) && genericFontFamilies.indexOf(fontFamily) < 0)\n            {\n                fontFamily = `\"${fontFamily}\"`;\n            }\n            (fontFamilies as string[])[i] = fontFamily;\n        }\n\n        return `${this.fontStyle} ${this.fontVariant} ${this.fontWeight} ${fontSizeString} ${(fontFamilies as string[]).join(',')}`;\n    }\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * @private\n * @param color\n * @return The color as a string.\n */\nfunction getSingleColor(color: string|number): string\n{\n    if (typeof color === 'number')\n    {\n        return hex2string(color);\n    }\n    else if (typeof color === 'string')\n    {\n        if ( color.indexOf('0x') === 0 )\n        {\n            color = color.replace('0x', '#');\n        }\n    }\n\n    return color;\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * This version can also convert array of colors\n * @private\n * @param color\n * @return The color as a string.\n */\nfunction getColor(color: (string|number)[]): string[];\nfunction getColor(color: string|number): string;\nfunction getColor(color: string|number|(string|number)[]): string|string[]\n{\n    if (!Array.isArray(color))\n    {\n        return getSingleColor(color);\n    }\n    else\n    {\n        for (let i = 0; i < color.length; ++i)\n        {\n            color[i] = getSingleColor(color[i]);\n        }\n\n        return color as string[];\n    }\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * This version can also convert array of colors\n * @private\n * @param array1 - First array to compare\n * @param array2 - Second array to compare\n * @return Do the arrays contain the same values in the same order\n */\nfunction areArraysEqual<T>(array1: T[], array2: T[]): boolean\n{\n    if (!Array.isArray(array1) || !Array.isArray(array2))\n    {\n        return false;\n    }\n\n    if (array1.length !== array2.length)\n    {\n        return false;\n    }\n\n    for (let i = 0; i < array1.length; ++i)\n    {\n        if (array1[i] !== array2[i])\n        {\n            return false;\n        }\n    }\n\n    return true;\n}\n\n/**\n * Utility function to ensure that object properties are copied by value, and not by reference\n * @private\n * @param target - Target object to copy properties into\n * @param source - Source object for the properties to copy\n * @param propertyObj - Object containing properties names we want to loop over\n */\nfunction deepCopyProperties(target: Record<string, any>, source: Record<string, any>, propertyObj: Record<string, any>): void {\n    for (const prop in propertyObj) {\n        if (Array.isArray(source[prop])) {\n            target[prop] = source[prop].slice();\n        } else {\n            target[prop] = source[prop];\n        }\n    }\n}\n", "import { settings } from '@pixi/settings';\n\nimport type { TextStyle, TextStyleWhiteSpace } from './TextStyle';\n\ninterface IFontMetrics\n{\n    ascent: number;\n    descent: number;\n    fontSize: number;\n}\n\ntype CharacterWidthCache = { [key: string]: number };\n\n// Default settings used for all getContext calls\nconst contextSettings = {\n    // TextMetrics requires getImageData readback for measuring fonts.\n    willReadFrequently: true,\n} as CanvasRenderingContext2DSettings;\n\n/**\n * The TextMetrics object represents the measurement of a block of text with a specified style.\n *\n * ```js\n * let style = new PIXI.TextStyle({fontFamily : 'Arial', fontSize: 24, fill : 0xff1010, align : 'center'})\n * let textMetrics = PIXI.TextMetrics.measureText('Your text', style)\n * ```\n * @memberof PIXI\n */\nexport class TextMetrics\n{\n    /** The text that was measured. */\n    public text: string;\n\n    /** The style that was measured. */\n    public style: TextStyle;\n\n    /** The measured width of the text. */\n    public width: number;\n\n    /** The measured height of the text. */\n    public height: number;\n\n    /** An array of lines of the text broken by new lines and wrapping is specified in style. */\n    public lines: string[];\n\n    /** An array of the line widths for each line matched to `lines`. */\n    public lineWidths: number[];\n\n    /** The measured line height for this style. */\n    public lineHeight: number;\n\n    /** The maximum line width for all measured lines. */\n    public maxLineWidth: number;\n\n    /**\n     * The font properties object from TextMetrics.measureFont.\n     * @type {PIXI.IFontMetrics}\n     */\n    public fontProperties: IFontMetrics;\n\n    public static METRICS_STRING: string;\n    public static BASELINE_SYMBOL: string;\n    public static BASELINE_MULTIPLIER: number;\n    public static HEIGHT_MULTIPLIER: number;\n\n    private static __canvas: HTMLCanvasElement | OffscreenCanvas;\n    private static __context: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D;\n\n    // TODO: These should be protected but they're initialized outside of the class.\n    public static _fonts: { [font: string]: IFontMetrics };\n    public static _newlines: number[];\n    public static _breakingSpaces: number[];\n\n    /**\n     * @param text - the text that was measured\n     * @param style - the style that was measured\n     * @param width - the measured width of the text\n     * @param height - the measured height of the text\n     * @param lines - an array of the lines of text broken by new lines and wrapping if specified in style\n     * @param lineWidths - an array of the line widths for each line matched to `lines`\n     * @param lineHeight - the measured line height for this style\n     * @param maxLineWidth - the maximum line width for all measured lines\n     * @param {PIXI.IFontMetrics} fontProperties - the font properties object from TextMetrics.measureFont\n     */\n    constructor(text: string, style: TextStyle, width: number, height: number, lines: string[], lineWidths: number[],\n        lineHeight: number, maxLineWidth: number, fontProperties: IFontMetrics)\n    {\n        this.text = text;\n        this.style = style;\n        this.width = width;\n        this.height = height;\n        this.lines = lines;\n        this.lineWidths = lineWidths;\n        this.lineHeight = lineHeight;\n        this.maxLineWidth = maxLineWidth;\n        this.fontProperties = fontProperties;\n    }\n\n    /**\n     * Measures the supplied string of text and returns a Rectangle.\n     * @param text - The text to measure.\n     * @param style - The text style to use for measuring\n     * @param wordWrap - Override for if word-wrap should be applied to the text.\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns Measured width and height of the text.\n     */\n    public static measureText(\n        text: string,\n        style: TextStyle,\n        wordWrap?: boolean,\n        canvas: HTMLCanvasElement | OffscreenCanvas = TextMetrics._canvas\n    ): TextMetrics\n    {\n        wordWrap = (wordWrap === undefined || wordWrap === null) ? style.wordWrap : wordWrap;\n        const font = style.toFontString();\n        const fontProperties = TextMetrics.measureFont(font);\n\n        // fallback in case UA disallow canvas data extraction\n        // (toDataURI, getImageData functions)\n        if (fontProperties.fontSize === 0)\n        {\n            fontProperties.fontSize = style.fontSize as number;\n            fontProperties.ascent = style.fontSize as number;\n        }\n\n        const context = canvas.getContext('2d', contextSettings);\n\n        context.font = font;\n\n        const outputText = wordWrap ? TextMetrics.wordWrap(text, style, canvas) : text;\n        const lines = outputText.split(/(?:\\r\\n|\\r|\\n)/);\n        const lineWidths = new Array<number>(lines.length);\n        let maxLineWidth = 0;\n\n        for (let i = 0; i < lines.length; i++)\n        {\n            const lineWidth = context.measureText(lines[i]).width + ((lines[i].length - 1) * style.letterSpacing);\n\n            lineWidths[i] = lineWidth;\n            maxLineWidth = Math.max(maxLineWidth, lineWidth);\n        }\n        let width = maxLineWidth + style.strokeThickness;\n\n        if (style.dropShadow)\n        {\n            width += style.dropShadowDistance;\n        }\n\n        const lineHeight = style.lineHeight || fontProperties.fontSize + style.strokeThickness;\n        let height = Math.max(lineHeight, fontProperties.fontSize + style.strokeThickness)\n            + ((lines.length - 1) * (lineHeight + style.leading));\n\n        if (style.dropShadow)\n        {\n            height += style.dropShadowDistance;\n        }\n\n        return new TextMetrics(\n            text,\n            style,\n            width,\n            height,\n            lines,\n            lineWidths,\n            lineHeight + style.leading,\n            maxLineWidth,\n            fontProperties\n        );\n    }\n\n    /**\n     * Applies newlines to a string to have it optimally fit into the horizontal\n     * bounds set by the Text object's wordWrapWidth property.\n     * @param text - String to apply word wrapping to\n     * @param style - the style to use when wrapping\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns New string with new lines applied where required\n     */\n    private static wordWrap(\n        text: string,\n        style: TextStyle,\n        canvas: HTMLCanvasElement | OffscreenCanvas = TextMetrics._canvas\n    ): string\n    {\n        const context = canvas.getContext('2d', contextSettings);\n\n        let width = 0;\n        let line = '';\n        let lines = '';\n\n        const cache: CharacterWidthCache = Object.create(null);\n        const { letterSpacing, whiteSpace } = style;\n\n        // How to handle whitespaces\n        const collapseSpaces = TextMetrics.collapseSpaces(whiteSpace);\n        const collapseNewlines = TextMetrics.collapseNewlines(whiteSpace);\n\n        // whether or not spaces may be added to the beginning of lines\n        let canPrependSpaces = !collapseSpaces;\n\n        // There is letterSpacing after every char except the last one\n        // t_h_i_s_' '_i_s_' '_a_n_' '_e_x_a_m_p_l_e_' '_!\n        // so for convenience the above needs to be compared to width + 1 extra letterSpace\n        // t_h_i_s_' '_i_s_' '_a_n_' '_e_x_a_m_p_l_e_' '_!_\n        // ________________________________________________\n        // And then the final space is simply no appended to each line\n        const wordWrapWidth = style.wordWrapWidth + letterSpacing;\n\n        // break text into words, spaces and newline chars\n        const tokens = TextMetrics.tokenize(text);\n\n        for (let i = 0; i < tokens.length; i++)\n        {\n            // get the word, space or newlineChar\n            let token = tokens[i];\n\n            // if word is a new line\n            if (TextMetrics.isNewline(token))\n            {\n                // keep the new line\n                if (!collapseNewlines)\n                {\n                    lines += TextMetrics.addLine(line);\n                    canPrependSpaces = !collapseSpaces;\n                    line = '';\n                    width = 0;\n                    continue;\n                }\n\n                // if we should collapse new lines\n                // we simply convert it into a space\n                token = ' ';\n            }\n\n            // if we should collapse repeated whitespaces\n            if (collapseSpaces)\n            {\n                // check both this and the last tokens for spaces\n                const currIsBreakingSpace = TextMetrics.isBreakingSpace(token);\n                const lastIsBreakingSpace = TextMetrics.isBreakingSpace(line[line.length - 1]);\n\n                if (currIsBreakingSpace && lastIsBreakingSpace)\n                {\n                    continue;\n                }\n            }\n\n            // get word width from cache if possible\n            const tokenWidth = TextMetrics.getFromCache(token, letterSpacing, cache, context);\n\n            // word is longer than desired bounds\n            if (tokenWidth > wordWrapWidth)\n            {\n                // if we are not already at the beginning of a line\n                if (line !== '')\n                {\n                    // start newlines for overflow words\n                    lines += TextMetrics.addLine(line);\n                    line = '';\n                    width = 0;\n                }\n\n                // break large word over multiple lines\n                if (TextMetrics.canBreakWords(token, style.breakWords))\n                {\n                    // break word into characters\n                    const characters = TextMetrics.wordWrapSplit(token);\n\n                    // loop the characters\n                    for (let j = 0; j < characters.length; j++)\n                    {\n                        let char = characters[j];\n\n                        let k = 1;\n                        // we are not at the end of the token\n\n                        while (characters[j + k])\n                        {\n                            const nextChar = characters[j + k];\n                            const lastChar = char[char.length - 1];\n\n                            // should not split chars\n                            if (!TextMetrics.canBreakChars(lastChar, nextChar, token, j, style.breakWords))\n                            {\n                                // combine chars & move forward one\n                                char += nextChar;\n                            }\n                            else\n                            {\n                                break;\n                            }\n\n                            k++;\n                        }\n\n                        j += char.length - 1;\n\n                        const characterWidth = TextMetrics.getFromCache(char, letterSpacing, cache, context);\n\n                        if (characterWidth + width > wordWrapWidth)\n                        {\n                            lines += TextMetrics.addLine(line);\n                            canPrependSpaces = false;\n                            line = '';\n                            width = 0;\n                        }\n\n                        line += char;\n                        width += characterWidth;\n                    }\n                }\n\n                // run word out of the bounds\n                else\n                {\n                    // if there are words in this line already\n                    // finish that line and start a new one\n                    if (line.length > 0)\n                    {\n                        lines += TextMetrics.addLine(line);\n                        line = '';\n                        width = 0;\n                    }\n\n                    const isLastToken = i === tokens.length - 1;\n\n                    // give it its own line if it's not the end\n                    lines += TextMetrics.addLine(token, !isLastToken);\n                    canPrependSpaces = false;\n                    line = '';\n                    width = 0;\n                }\n            }\n\n            // word could fit\n            else\n            {\n                // word won't fit because of existing words\n                // start a new line\n                if (tokenWidth + width > wordWrapWidth)\n                {\n                    // if its a space we don't want it\n                    canPrependSpaces = false;\n\n                    // add a new line\n                    lines += TextMetrics.addLine(line);\n\n                    // start a new line\n                    line = '';\n                    width = 0;\n                }\n\n                // don't add spaces to the beginning of lines\n                if (line.length > 0 || !TextMetrics.isBreakingSpace(token) || canPrependSpaces)\n                {\n                    // add the word to the current line\n                    line += token;\n\n                    // update width counter\n                    width += tokenWidth;\n                }\n            }\n        }\n\n        lines += TextMetrics.addLine(line, false);\n\n        return lines;\n    }\n\n    /**\n     * Convienience function for logging each line added during the wordWrap method.\n     * @param line    - The line of text to add\n     * @param newLine - Add new line character to end\n     * @returns A formatted line\n     */\n    private static addLine(line: string, newLine = true): string\n    {\n        line = TextMetrics.trimRight(line);\n\n        line = (newLine) ? `${line}\\n` : line;\n\n        return line;\n    }\n\n    /**\n     * Gets & sets the widths of calculated characters in a cache object\n     * @param key            - The key\n     * @param letterSpacing  - The letter spacing\n     * @param cache          - The cache\n     * @param context        - The canvas context\n     * @returns The from cache.\n     */\n    private static getFromCache(key: string, letterSpacing: number, cache: CharacterWidthCache,\n        context: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D): number\n    {\n        let width = cache[key];\n\n        if (typeof width !== 'number')\n        {\n            const spacing = ((key.length) * letterSpacing);\n\n            width = context.measureText(key).width + spacing;\n            cache[key] = width;\n        }\n\n        return width;\n    }\n\n    /**\n     * Determines whether we should collapse breaking spaces.\n     * @param whiteSpace - The TextStyle property whiteSpace\n     * @returns Should collapse\n     */\n    private static collapseSpaces(whiteSpace: TextStyleWhiteSpace): boolean\n    {\n        return (whiteSpace === 'normal' || whiteSpace === 'pre-line');\n    }\n\n    /**\n     * Determines whether we should collapse newLine chars.\n     * @param whiteSpace - The white space\n     * @returns  should collapse\n     */\n    private static collapseNewlines(whiteSpace: TextStyleWhiteSpace): boolean\n    {\n        return (whiteSpace === 'normal');\n    }\n\n    /**\n     * Trims breaking whitespaces from string.\n     * @param  text - The text\n     * @returns Trimmed string\n     */\n    private static trimRight(text: string): string\n    {\n        if (typeof text !== 'string')\n        {\n            return '';\n        }\n\n        for (let i = text.length - 1; i >= 0; i--)\n        {\n            const char = text[i];\n\n            if (!TextMetrics.isBreakingSpace(char))\n            {\n                break;\n            }\n\n            text = text.slice(0, -1);\n        }\n\n        return text;\n    }\n\n    /**\n     * Determines if char is a newline.\n     * @param  char - The character\n     * @returns True if newline, False otherwise.\n     */\n    private static isNewline(char: string): boolean\n    {\n        if (typeof char !== 'string')\n        {\n            return false;\n        }\n\n        return (TextMetrics._newlines.indexOf(char.charCodeAt(0)) >= 0);\n    }\n\n    /**\n     * Determines if char is a breaking whitespace.\n     *\n     * It allows one to determine whether char should be a breaking whitespace\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param char - The character\n     * @param [_nextChar] - The next character\n     * @returns True if whitespace, False otherwise.\n     */\n    static isBreakingSpace(char: string, _nextChar?: string): boolean\n    {\n        if (typeof char !== 'string')\n        {\n            return false;\n        }\n\n        return (TextMetrics._breakingSpaces.indexOf(char.charCodeAt(0)) >= 0);\n    }\n\n    /**\n     * Splits a string into words, breaking-spaces and newLine characters\n     * @param  text - The text\n     * @returns  A tokenized array\n     */\n    private static tokenize(text: string): string[]\n    {\n        const tokens: string[] = [];\n        let token = '';\n\n        if (typeof text !== 'string')\n        {\n            return tokens;\n        }\n\n        for (let i = 0; i < text.length; i++)\n        {\n            const char = text[i];\n            const nextChar = text[i + 1];\n\n            if (TextMetrics.isBreakingSpace(char, nextChar) || TextMetrics.isNewline(char))\n            {\n                if (token !== '')\n                {\n                    tokens.push(token);\n                    token = '';\n                }\n\n                tokens.push(char);\n\n                continue;\n            }\n\n            token += char;\n        }\n\n        if (token !== '')\n        {\n            tokens.push(token);\n        }\n\n        return tokens;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to customise which words should break\n     * Examples are if the token is CJK or numbers.\n     * It must return a boolean.\n     * @param _token - The token\n     * @param  breakWords - The style attr break words\n     * @returns Whether to break word or not\n     */\n    static canBreakWords(_token: string, breakWords: boolean): boolean\n    {\n        return breakWords;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to determine whether a pair of characters\n     * should be broken by newlines\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param _char - The character\n     * @param _nextChar - The next character\n     * @param _token - The token/word the characters are from\n     * @param _index - The index in the token of the char\n     * @param _breakWords - The style attr break words\n     * @returns whether to break word or not\n     */\n    static canBreakChars(_char: string, _nextChar: string, _token: string, _index: number,\n        _breakWords: boolean): boolean\n    {\n        return true;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It is called when a token (usually a word) has to be split into separate pieces\n     * in order to determine the point to break a word.\n     * It must return an array of characters.\n     * @example\n     * // Correctly splits emojis, eg \"🤪🤪\" will result in two element array, each with one emoji.\n     * TextMetrics.wordWrapSplit = (token) => [...token];\n     * @param  token - The token to split\n     * @returns The characters of the token\n     */\n    static wordWrapSplit(token: string): string[]\n    {\n        return token.split('');\n    }\n\n    /**\n     * Calculates the ascent, descent and fontSize of a given font-style\n     * @param font - String representing the style of the font\n     * @returns Font properties object\n     */\n    public static measureFont(font: string): IFontMetrics\n    {\n        // as this method is used for preparing assets, don't recalculate things if we don't need to\n        if (TextMetrics._fonts[font])\n        {\n            return TextMetrics._fonts[font];\n        }\n\n        const properties: IFontMetrics = {\n            ascent: 0,\n            descent: 0,\n            fontSize: 0,\n        };\n\n        const canvas = TextMetrics._canvas;\n        const context = TextMetrics._context;\n\n        context.font = font;\n\n        const metricsString = TextMetrics.METRICS_STRING + TextMetrics.BASELINE_SYMBOL;\n        const width = Math.ceil(context.measureText(metricsString).width);\n        let baseline = Math.ceil(context.measureText(TextMetrics.BASELINE_SYMBOL).width);\n        const height = Math.ceil(TextMetrics.HEIGHT_MULTIPLIER * baseline);\n\n        baseline = baseline * TextMetrics.BASELINE_MULTIPLIER | 0;\n\n        canvas.width = width;\n        canvas.height = height;\n\n        context.fillStyle = '#f00';\n        context.fillRect(0, 0, width, height);\n\n        context.font = font;\n\n        context.textBaseline = 'alphabetic';\n        context.fillStyle = '#000';\n        context.fillText(metricsString, 0, baseline);\n\n        const imagedata = context.getImageData(0, 0, width, height).data;\n        const pixels = imagedata.length;\n        const line = width * 4;\n\n        let i = 0;\n        let idx = 0;\n        let stop = false;\n\n        // ascent. scan from top to bottom until we find a non red pixel\n        for (i = 0; i < baseline; ++i)\n        {\n            for (let j = 0; j < line; j += 4)\n            {\n                if (imagedata[idx + j] !== 255)\n                {\n                    stop = true;\n                    break;\n                }\n            }\n            if (!stop)\n            {\n                idx += line;\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        properties.ascent = baseline - i;\n\n        idx = pixels - line;\n        stop = false;\n\n        // descent. scan from bottom to top until we find a non red pixel\n        for (i = height; i > baseline; --i)\n        {\n            for (let j = 0; j < line; j += 4)\n            {\n                if (imagedata[idx + j] !== 255)\n                {\n                    stop = true;\n                    break;\n                }\n            }\n\n            if (!stop)\n            {\n                idx -= line;\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        properties.descent = i - baseline;\n        properties.fontSize = properties.ascent + properties.descent;\n\n        TextMetrics._fonts[font] = properties;\n\n        return properties;\n    }\n\n    /**\n     * Clear font metrics in metrics cache.\n     * @param {string} [font] - font name. If font name not set then clear cache for all fonts.\n     */\n    public static clearMetrics(font = ''): void\n    {\n        if (font)\n        {\n            delete TextMetrics._fonts[font];\n        }\n        else\n        {\n            TextMetrics._fonts = {};\n        }\n    }\n\n    /**\n     * Cached canvas element for measuring text\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    public static get _canvas(): HTMLCanvasElement | OffscreenCanvas\n    {\n        if (!TextMetrics.__canvas)\n        {\n            let canvas: HTMLCanvasElement | OffscreenCanvas;\n\n            try\n            {\n                // OffscreenCanvas2D measureText can be up to 40% faster.\n                const c = new OffscreenCanvas(0, 0);\n                const context = c.getContext('2d', contextSettings);\n\n                if (context && context.measureText)\n                {\n                    TextMetrics.__canvas = c;\n\n                    return c;\n                }\n\n                canvas = settings.ADAPTER.createCanvas();\n            }\n            catch (ex)\n            {\n                canvas = settings.ADAPTER.createCanvas();\n            }\n            canvas.width = canvas.height = 10;\n            TextMetrics.__canvas = canvas;\n        }\n\n        return TextMetrics.__canvas;\n    }\n\n    /**\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    public static get _context(): CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D\n    {\n        if (!TextMetrics.__context)\n        {\n            TextMetrics.__context = TextMetrics._canvas.getContext('2d', contextSettings);\n        }\n\n        return TextMetrics.__context;\n    }\n}\n\n/**\n * Internal return object for {@link PIXI.TextMetrics.measureFont `TextMetrics.measureFont`}.\n * @typedef {object} FontMetrics\n * @property {number} ascent - The ascent distance\n * @property {number} descent - The descent distance\n * @property {number} fontSize - Font size from ascent to descent\n * @memberof PIXI.TextMetrics\n * @private\n */\n\n/**\n * Cache of {@see PIXI.TextMetrics.FontMetrics} objects.\n * @memberof PIXI.TextMetrics\n * @type {object}\n * @private\n */\nTextMetrics._fonts = {};\n\n/**\n * String used for calculate font metrics.\n * These characters are all tall to help calculate the height required for text.\n * @static\n * @memberof PIXI.TextMetrics\n * @name METRICS_STRING\n * @type {string}\n * @default |ÉqÅ\n */\nTextMetrics.METRICS_STRING = '|ÉqÅ';\n\n/**\n * Baseline symbol for calculate font metrics.\n * @static\n * @memberof PIXI.TextMetrics\n * @name BASELINE_SYMBOL\n * @type {string}\n * @default M\n */\nTextMetrics.BASELINE_SYMBOL = 'M';\n\n/**\n * Baseline multiplier for calculate font metrics.\n * @static\n * @memberof PIXI.TextMetrics\n * @name BASELINE_MULTIPLIER\n * @type {number}\n * @default 1.4\n */\nTextMetrics.BASELINE_MULTIPLIER = 1.4;\n\n/**\n * Height multiplier for setting height of canvas to calculate font metrics.\n * @static\n * @memberof PIXI.TextMetrics\n * @name HEIGHT_MULTIPLIER\n * @type {number}\n * @default 2.00\n */\nTextMetrics.HEIGHT_MULTIPLIER = 2.0;\n\n/**\n * Cache of new line chars.\n * @memberof PIXI.TextMetrics\n * @type {number[]}\n * @private\n */\nTextMetrics._newlines = [\n    0x000A, // line feed\n    0x000D, // carriage return\n];\n\n/**\n * Cache of breaking spaces.\n * @memberof PIXI.TextMetrics\n * @type {number[]}\n * @private\n */\nTextMetrics._breakingSpaces = [\n    0x0009, // character tabulation\n    0x0020, // space\n    0x2000, // en quad\n    0x2001, // em quad\n    0x2002, // en space\n    0x2003, // em space\n    0x2004, // three-per-em space\n    0x2005, // four-per-em space\n    0x2006, // six-per-em space\n    0x2008, // punctuation space\n    0x2009, // thin space\n    0x200A, // hair space\n    0x205F, // medium mathematical space\n    0x3000, // ideographic space\n];\n\n/**\n * A number, or a string containing a number.\n * @memberof PIXI\n * @typedef {object} IFontMetrics\n * @property {number} ascent - Font ascent\n * @property {number} descent - Font descent\n * @property {number} fontSize - Font size\n */\n", "/* eslint max-depth: [2, 8] */\nimport { Sprite } from '@pixi/sprite';\nimport { Texture  } from '@pixi/core';\nimport { settings } from '@pixi/settings';\nimport { Rectangle } from '@pixi/math';\nimport { sign, trimCanvas, hex2rgb, string2hex } from '@pixi/utils';\nimport { TEXT_GRADIENT } from './const';\nimport { TextStyle } from './TextStyle';\nimport { TextMetrics } from './TextMetrics';\n\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { Renderer } from '@pixi/core';\nimport type { ITextStyle } from './TextStyle';\n\nconst defaultDestroyOptions: IDestroyOptions = {\n    texture: true,\n    children: false,\n    baseTexture: true,\n};\n\ninterface ModernContext2D extends CanvasRenderingContext2D\n{\n    // for chrome less 94\n    textLetterSpacing?: number;\n    // for chrome greater 94\n    letterSpacing?: number;\n}\n\n/**\n * A Text Object will create a line or multiple lines of text.\n *\n * The text is created using the [Canvas API](https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API).\n *\n * The primary advantage of this class over BitmapText is that you have great control over the style of the text,\n * which you can change at runtime.\n *\n * The primary disadvantages is that each piece of text has it's own texture, which can use more memory.\n * When text changes, this texture has to be re-generated and re-uploaded to the GPU, taking up time.\n *\n * To split a line you can use '\\n' in your text string, or, on the `style` object,\n * change its `wordWrap` property to true and and give the `wordWrapWidth` property a value.\n *\n * A Text can be created directly from a string and a style object,\n * which can be generated [here](https://pixijs.io/pixi-text-style).\n *\n * ```js\n * let text = new PIXI.Text('This is a PixiJS text',{fontFamily : 'Arial', fontSize: 24, fill : 0xff1010, align : 'center'});\n * ```\n * @memberof PIXI\n */\nexport class Text extends Sprite\n{\n    /**\n     * New behavior for `lineHeight` that's meant to mimic HTML text. A value of `true` will\n     * make sure the first baseline is offset by the `lineHeight` value if it is greater than `fontSize`.\n     * A value of `false` will use the legacy behavior and not change the baseline of the first line.\n     * In the next major release, we'll enable this by default.\n     */\n    public static nextLineHeightBehavior = false;\n\n    /**\n     * New rendering behavior for letter-spacing which uses Chrome's new native API. This will\n     * lead to more accurate letter-spacing results because it does not try to manually draw\n     * each character. However, this Chrome API is experimental and may not serve all cases yet.\n     */\n    public static experimentalLetterSpacing = false;\n\n    /** The canvas element that everything is drawn to. */\n    public canvas: HTMLCanvasElement;\n    /** The canvas 2d context that everything is drawn with. */\n    public context: ModernContext2D;\n    public localStyleID: number;\n    public dirty: boolean;\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default PIXI.settings.RESOLUTION\n     */\n    _resolution: number;\n    _autoResolution: boolean;\n\n    /**\n     * Private tracker for the current text.\n     * @private\n     */\n    protected _text: string;\n\n    /**\n     * Private tracker for the current font.\n     * @private\n     */\n    protected _font: string;\n\n    /**\n     * Private tracker for the current style.\n     * @private\n     */\n    protected _style: TextStyle;\n\n    /**\n     * Private listener to track style changes.\n     * @private\n     */\n    protected _styleListener: () => void;\n\n    /**\n     * Keep track if this Text object created it's own canvas\n     * element (`true`) or uses the constructor argument (`false`).\n     * Used to workaround a GC issues with Safari < 13 when\n     * destroying Text. See `destroy` for more info.\n     */\n    private _ownCanvas: boolean;\n\n    /**\n     * @param text - The string that you would like the text to display\n     * @param {object|PIXI.TextStyle} [style] - The style parameters\n     * @param canvas - The canvas element for drawing text\n     */\n    constructor(text?: string | number, style?: Partial<ITextStyle> | TextStyle, canvas?: HTMLCanvasElement)\n    {\n        let ownCanvas = false;\n\n        if (!canvas)\n        {\n            canvas = settings.ADAPTER.createCanvas();\n            ownCanvas = true;\n        }\n\n        canvas.width = 3;\n        canvas.height = 3;\n\n        const texture = Texture.from(canvas);\n\n        texture.orig = new Rectangle();\n        texture.trim = new Rectangle();\n\n        super(texture);\n\n        this._ownCanvas = ownCanvas;\n        this.canvas = canvas;\n        this.context = canvas.getContext('2d', {\n            // required for trimming to work without warnings\n            willReadFrequently: true,\n        }) as CanvasRenderingContext2D;\n\n        this._resolution = settings.RESOLUTION;\n        this._autoResolution = true;\n        this._text = null;\n        this._style = null;\n        this._styleListener = null;\n        this._font = '';\n\n        this.text = text;\n        this.style = style;\n\n        this.localStyleID = -1;\n    }\n\n    /**\n     * Renders text to its canvas, and updates its texture.\n     *\n     * By default this is used internally to ensure the texture is correct before rendering,\n     * but it can be used called externally, for example from this class to 'pre-generate' the texture from a piece of text,\n     * and then shared across multiple Sprites.\n     * @param respectDirty - Whether to abort updating the text if the Text isn't dirty and the function is called.\n     */\n    public updateText(respectDirty: boolean): void\n    {\n        const style = this._style;\n\n        // check if style has changed..\n        if (this.localStyleID !== style.styleID)\n        {\n            this.dirty = true;\n            this.localStyleID = style.styleID;\n        }\n\n        if (!this.dirty && respectDirty)\n        {\n            return;\n        }\n\n        this._font = this._style.toFontString();\n\n        const context = this.context;\n        const measured = TextMetrics.measureText(this._text || ' ', this._style, this._style.wordWrap, this.canvas);\n        const width = measured.width;\n        const height = measured.height;\n        const lines = measured.lines;\n        const lineHeight = measured.lineHeight;\n        const lineWidths = measured.lineWidths;\n        const maxLineWidth = measured.maxLineWidth;\n        const fontProperties = measured.fontProperties;\n\n        this.canvas.width = Math.ceil(Math.ceil((Math.max(1, width) + (style.padding * 2))) * this._resolution);\n        this.canvas.height = Math.ceil(Math.ceil((Math.max(1, height) + (style.padding * 2))) * this._resolution);\n\n        context.scale(this._resolution, this._resolution);\n\n        context.clearRect(0, 0, this.canvas.width, this.canvas.height);\n\n        context.font = this._font;\n        context.lineWidth = style.strokeThickness;\n        context.textBaseline = style.textBaseline;\n        context.lineJoin = style.lineJoin;\n        context.miterLimit = style.miterLimit;\n\n        let linePositionX: number;\n        let linePositionY: number;\n\n        // require 2 passes if a shadow; the first to draw the drop shadow, the second to draw the text\n        const passesCount = style.dropShadow ? 2 : 1;\n\n        // For v4, we drew text at the colours of the drop shadow underneath the normal text. This gave the correct zIndex,\n        // but features such as alpha and shadowblur did not look right at all, since we were using actual text as a shadow.\n        //\n        // For v5.0.0, we moved over to just use the canvas API for drop shadows, which made them look much nicer and more\n        // visually please, but now because the stroke is drawn and then the fill, drop shadows would appear on both the fill\n        // and the stroke; and fill drop shadows would appear over the top of the stroke.\n        //\n        // For v5.1.1, the new route is to revert to v4 style of drawing text first to get the drop shadows underneath normal\n        // text, but instead drawing text in the correct location, we'll draw it off screen (-paddingY), and then adjust the\n        // drop shadow so only that appears on screen (+paddingY). Now we'll have the correct draw order of the shadow\n        // beneath the text, whilst also having the proper text shadow styling.\n        for (let i = 0; i < passesCount; ++i)\n        {\n            const isShadowPass = style.dropShadow && i === 0;\n            // we only want the drop shadow, so put text way off-screen\n            const dsOffsetText = isShadowPass ? Math.ceil(Math.max(1, height) + (style.padding * 2)) : 0;\n            const dsOffsetShadow = dsOffsetText * this._resolution;\n\n            if (isShadowPass)\n            {\n                // On Safari, text with gradient and drop shadows together do not position correctly\n                // if the scale of the canvas is not 1: https://bugs.webkit.org/show_bug.cgi?id=197689\n                // Therefore we'll set the styles to be a plain black whilst generating this drop shadow\n                context.fillStyle = 'black';\n                context.strokeStyle = 'black';\n\n                const dropShadowColor = style.dropShadowColor;\n                const rgb = hex2rgb(typeof dropShadowColor === 'number' ? dropShadowColor : string2hex(dropShadowColor));\n                const dropShadowBlur = style.dropShadowBlur * this._resolution;\n                const dropShadowDistance = style.dropShadowDistance * this._resolution;\n\n                context.shadowColor = `rgba(${rgb[0] * 255},${rgb[1] * 255},${rgb[2] * 255},${style.dropShadowAlpha})`;\n                context.shadowBlur = dropShadowBlur;\n                context.shadowOffsetX = Math.cos(style.dropShadowAngle) * dropShadowDistance;\n                context.shadowOffsetY = (Math.sin(style.dropShadowAngle) * dropShadowDistance) + dsOffsetShadow;\n            }\n            else\n            {\n                // set canvas text styles\n                context.fillStyle = this._generateFillStyle(style, lines, measured);\n                // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n                //       the setter converts to string. See this thread for more details:\n                //       https://github.com/microsoft/TypeScript/issues/2521\n                context.strokeStyle = style.stroke as string;\n\n                context.shadowColor = 'black';\n                context.shadowBlur = 0;\n                context.shadowOffsetX = 0;\n                context.shadowOffsetY = 0;\n            }\n\n            let linePositionYShift = (lineHeight - fontProperties.fontSize) / 2;\n\n            if (!Text.nextLineHeightBehavior || lineHeight - fontProperties.fontSize < 0)\n            {\n                linePositionYShift = 0;\n            }\n\n            // draw lines line by line\n            for (let i = 0; i < lines.length; i++)\n            {\n                linePositionX = style.strokeThickness / 2;\n                linePositionY = ((style.strokeThickness / 2) + (i * lineHeight)) + fontProperties.ascent\n                    + linePositionYShift;\n\n                if (style.align === 'right')\n                {\n                    linePositionX += maxLineWidth - lineWidths[i];\n                }\n                else if (style.align === 'center')\n                {\n                    linePositionX += (maxLineWidth - lineWidths[i]) / 2;\n                }\n\n                if (style.stroke && style.strokeThickness)\n                {\n                    this.drawLetterSpacing(\n                        lines[i],\n                        linePositionX + style.padding,\n                        linePositionY + style.padding - dsOffsetText,\n                        true\n                    );\n                }\n\n                if (style.fill)\n                {\n                    this.drawLetterSpacing(\n                        lines[i],\n                        linePositionX + style.padding,\n                        linePositionY + style.padding - dsOffsetText\n                    );\n                }\n            }\n        }\n\n        this.updateTexture();\n    }\n\n    /**\n     * Render the text with letter-spacing.\n     * @param text - The text to draw\n     * @param x - Horizontal position to draw the text\n     * @param y - Vertical position to draw the text\n     * @param isStroke - Is this drawing for the outside stroke of the\n     *  text? If not, it's for the inside fill\n     */\n    private drawLetterSpacing(text: string, x: number, y: number, isStroke = false): void\n    {\n        const style = this._style;\n\n        // letterSpacing of 0 means normal\n        const letterSpacing = style.letterSpacing;\n\n        // Checking that we can use moddern canvas2D api\n        // https://developer.chrome.com/origintrials/#/view_trial/3585991203293757441\n        // note: this is unstable API, Chrome less 94 use a `textLetterSpacing`, newest use a letterSpacing\n        // eslint-disable-next-line max-len\n        const supportLetterSpacing = Text.experimentalLetterSpacing\n            && ('letterSpacing' in CanvasRenderingContext2D.prototype\n                || 'textLetterSpacing' in CanvasRenderingContext2D.prototype);\n\n        if (letterSpacing === 0 || supportLetterSpacing)\n        {\n            if (supportLetterSpacing)\n            {\n                this.context.letterSpacing = letterSpacing;\n                this.context.textLetterSpacing = letterSpacing;\n            }\n\n            if (isStroke)\n            {\n                this.context.strokeText(text, x, y);\n            }\n            else\n            {\n                this.context.fillText(text, x, y);\n            }\n\n            return;\n        }\n\n        let currentPosition = x;\n\n        // Using Array.from correctly splits characters whilst keeping emoji together.\n        // This is not supported on IE as it requires ES6, so regular text splitting occurs.\n        // This also doesn't account for emoji that are multiple emoji put together to make something else.\n        // Handling all of this would require a big library itself.\n        // https://medium.com/@giltayar/iterating-over-emoji-characters-the-es6-way-f06e4589516\n        // https://github.com/orling/grapheme-splitter\n        const stringArray = Array.from ? Array.from(text) : text.split('');\n        let previousWidth = this.context.measureText(text).width;\n        let currentWidth = 0;\n\n        for (let i = 0; i < stringArray.length; ++i)\n        {\n            const currentChar = stringArray[i];\n\n            if (isStroke)\n            {\n                this.context.strokeText(currentChar, currentPosition, y);\n            }\n            else\n            {\n                this.context.fillText(currentChar, currentPosition, y);\n            }\n            let textStr = '';\n\n            for (let j = i + 1; j < stringArray.length; ++j)\n            {\n                textStr += stringArray[j];\n            }\n            currentWidth = this.context.measureText(textStr).width;\n            currentPosition += previousWidth - currentWidth + letterSpacing;\n            previousWidth = currentWidth;\n        }\n    }\n\n    /** Updates texture size based on canvas size. */\n    private updateTexture(): void\n    {\n        const canvas = this.canvas;\n\n        if (this._style.trim)\n        {\n            const trimmed = trimCanvas(canvas);\n\n            if (trimmed.data)\n            {\n                canvas.width = trimmed.width;\n                canvas.height = trimmed.height;\n                this.context.putImageData(trimmed.data, 0, 0);\n            }\n        }\n\n        const texture = this._texture;\n        const style = this._style;\n        const padding = style.trim ? 0 : style.padding;\n        const baseTexture = texture.baseTexture;\n\n        texture.trim.width = texture._frame.width = canvas.width / this._resolution;\n        texture.trim.height = texture._frame.height = canvas.height / this._resolution;\n        texture.trim.x = -padding;\n        texture.trim.y = -padding;\n\n        texture.orig.width = texture._frame.width - (padding * 2);\n        texture.orig.height = texture._frame.height - (padding * 2);\n\n        // call sprite onTextureUpdate to update scale if _width or _height were set\n        this._onTextureUpdate();\n\n        baseTexture.setRealSize(canvas.width, canvas.height, this._resolution);\n\n        texture.updateUvs();\n\n        this.dirty = false;\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        if (this._autoResolution && this._resolution !== renderer.resolution)\n        {\n            this._resolution = renderer.resolution;\n            this.dirty = true;\n        }\n\n        this.updateText(true);\n\n        super._render(renderer);\n    }\n\n    /** Updates the transform on all children of this container for rendering. */\n    public updateTransform(): void\n    {\n        this.updateText(true);\n\n        super.updateTransform();\n    }\n\n    public getBounds(skipUpdate?: boolean, rect?: Rectangle): Rectangle\n    {\n        this.updateText(true);\n\n        if (this._textureID === -1)\n        {\n            // texture was updated: recalculate transforms\n            skipUpdate = false;\n        }\n\n        return super.getBounds(skipUpdate, rect);\n    }\n\n    /**\n     * Gets the local bounds of the text object.\n     * @param rect - The output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        this.updateText(true);\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /** Calculates the bounds of the Text as a rectangle. The bounds calculation takes the worldTransform into account. */\n    protected _calculateBounds(): void\n    {\n        this.calculateVertices();\n        // if we have already done this on THIS frame.\n        this._bounds.addQuad(this.vertexData);\n    }\n\n    /**\n     * Generates the fill style. Can automatically generate a gradient based on the fill style being an array\n     * @param style - The style.\n     * @param lines - The lines of text.\n     * @param metrics\n     * @returns The fill style\n     */\n    private _generateFillStyle(\n        style: TextStyle, lines: string[], metrics: TextMetrics\n    ): string | CanvasGradient | CanvasPattern\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        const fillStyle: string | string[] | CanvasGradient | CanvasPattern = style.fill as any;\n\n        if (!Array.isArray(fillStyle))\n        {\n            return fillStyle;\n        }\n        else if (fillStyle.length === 1)\n        {\n            return fillStyle[0];\n        }\n\n        // the gradient will be evenly spaced out according to how large the array is.\n        // ['#FF0000', '#00FF00', '#0000FF'] would created stops at 0.25, 0.5 and 0.75\n        let gradient: string[] | CanvasGradient;\n\n        // a dropshadow will enlarge the canvas and result in the gradient being\n        // generated with the incorrect dimensions\n        const dropShadowCorrection = (style.dropShadow) ? style.dropShadowDistance : 0;\n\n        // should also take padding into account, padding can offset the gradient\n        const padding = style.padding || 0;\n\n        const width = (this.canvas.width / this._resolution) - dropShadowCorrection - (padding * 2);\n        const height = (this.canvas.height / this._resolution) - dropShadowCorrection - (padding * 2);\n\n        // make a copy of the style settings, so we can manipulate them later\n        const fill = fillStyle.slice();\n        const fillGradientStops = style.fillGradientStops.slice();\n\n        // wanting to evenly distribute the fills. So an array of 4 colours should give fills of 0.25, 0.5 and 0.75\n        if (!fillGradientStops.length)\n        {\n            const lengthPlus1 = fill.length + 1;\n\n            for (let i = 1; i < lengthPlus1; ++i)\n            {\n                fillGradientStops.push(i / lengthPlus1);\n            }\n        }\n\n        // stop the bleeding of the last gradient on the line above to the top gradient of the this line\n        // by hard defining the first gradient colour at point 0, and last gradient colour at point 1\n        fill.unshift(fillStyle[0]);\n        fillGradientStops.unshift(0);\n\n        fill.push(fillStyle[fillStyle.length - 1]);\n        fillGradientStops.push(1);\n\n        if (style.fillGradientType === TEXT_GRADIENT.LINEAR_VERTICAL)\n        {\n            // start the gradient at the top center of the canvas, and end at the bottom middle of the canvas\n            gradient = this.context.createLinearGradient(width / 2, padding, width / 2, height + padding);\n\n            // we need to repeat the gradient so that each individual line of text has the same vertical gradient effect\n            // ['#FF0000', '#00FF00', '#0000FF'] over 2 lines would create stops at 0.125, 0.25, 0.375, 0.625, 0.75, 0.875\n\n            // Actual height of the text itself, not counting spacing for lineHeight/leading/dropShadow etc\n            const textHeight = metrics.fontProperties.fontSize + style.strokeThickness;\n\n            for (let i = 0; i < lines.length; i++)\n            {\n                const lastLineBottom = (metrics.lineHeight * (i - 1)) + textHeight;\n                const thisLineTop = metrics.lineHeight * i;\n                let thisLineGradientStart = thisLineTop;\n\n                // Handle case where last & this line overlap\n                if (i > 0 && lastLineBottom > thisLineTop)\n                {\n                    thisLineGradientStart = (thisLineTop + lastLineBottom) / 2;\n                }\n\n                const thisLineBottom = thisLineTop + textHeight;\n                const nextLineTop = metrics.lineHeight * (i + 1);\n                let thisLineGradientEnd = thisLineBottom;\n\n                // Handle case where this & next line overlap\n                if (i + 1 < lines.length && nextLineTop < thisLineBottom)\n                {\n                    thisLineGradientEnd = (thisLineBottom + nextLineTop) / 2;\n                }\n\n                // textHeight, but as a 0-1 size in global gradient stop space\n                const gradStopLineHeight = (thisLineGradientEnd - thisLineGradientStart) / height;\n\n                for (let j = 0; j < fill.length; j++)\n                {\n                    // 0-1 stop point for the current line, multiplied to global space afterwards\n                    let lineStop = 0;\n\n                    if (typeof fillGradientStops[j] === 'number')\n                    {\n                        lineStop = fillGradientStops[j];\n                    }\n                    else\n                    {\n                        lineStop = j / fill.length;\n                    }\n\n                    let globalStop = Math.min(1, Math.max(0,\n                        (thisLineGradientStart / height) + (lineStop * gradStopLineHeight)));\n\n                    // There's potential for floating point precision issues at the seams between gradient repeats.\n                    globalStop = Number(globalStop.toFixed(5));\n                    gradient.addColorStop(globalStop, fill[j]);\n                }\n            }\n        }\n        else\n        {\n            // start the gradient at the center left of the canvas, and end at the center right of the canvas\n            gradient = this.context.createLinearGradient(padding, height / 2, width + padding, height / 2);\n\n            // can just evenly space out the gradients in this case, as multiple lines makes no difference\n            // to an even left to right gradient\n            const totalIterations = fill.length + 1;\n            let currentIteration = 1;\n\n            for (let i = 0; i < fill.length; i++)\n            {\n                let stop: number;\n\n                if (typeof fillGradientStops[i] === 'number')\n                {\n                    stop = fillGradientStops[i];\n                }\n                else\n                {\n                    stop = currentIteration / totalIterations;\n                }\n                gradient.addColorStop(stop, fill[i]);\n                currentIteration++;\n            }\n        }\n\n        return gradient;\n    }\n\n    /**\n     * Destroys this text object.\n     *\n     * Note* Unlike a Sprite, a Text object will automatically destroy its baseTexture and texture as\n     * the majority of the time the texture will not be shared with any other Sprites.\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their\n     *  destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=true] - Should it destroy the current texture of the sprite as well\n     * @param {boolean} [options.baseTexture=true] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        if (typeof options === 'boolean')\n        {\n            options = { children: options };\n        }\n\n        options = Object.assign({}, defaultDestroyOptions, options);\n\n        super.destroy(options);\n\n        // set canvas width and height to 0 to workaround memory leak in Safari < 13\n        // https://stackoverflow.com/questions/52532614/total-canvas-memory-use-exceeds-the-maximum-limit-safari-12\n        if (this._ownCanvas)\n        {\n            this.canvas.height = this.canvas.width = 0;\n        }\n\n        // make sure to reset the context and canvas.. dont want this hanging around in memory!\n        this.context = null;\n        this.canvas = null;\n\n        this._style = null;\n    }\n\n    /** The width of the Text, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        this.updateText(true);\n\n        return Math.abs(this.scale.x) * this._texture.orig.width;\n    }\n\n    set width(value: number)\n    {\n        this.updateText(true);\n\n        const s = sign(this.scale.x) || 1;\n\n        this.scale.x = s * value / this._texture.orig.width;\n        this._width = value;\n    }\n\n    /** The height of the Text, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        this.updateText(true);\n\n        return Math.abs(this.scale.y) * this._texture.orig.height;\n    }\n\n    set height(value: number)\n    {\n        this.updateText(true);\n\n        const s = sign(this.scale.y) || 1;\n\n        this.scale.y = s * value / this._texture.orig.height;\n        this._height = value;\n    }\n\n    /**\n     * Set the style of the text.\n     *\n     * Set up an event listener to listen for changes on the style object and mark the text as dirty.\n     */\n    get style(): TextStyle | Partial<ITextStyle>\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the ITextStyle\n        //       since the setter creates the TextStyle. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        return this._style;\n    }\n\n    set style(style: TextStyle | Partial<ITextStyle>)\n    {\n        style = style || {};\n\n        if (style instanceof TextStyle)\n        {\n            this._style = style;\n        }\n        else\n        {\n            this._style = new TextStyle(style);\n        }\n\n        this.localStyleID = -1;\n        this.dirty = true;\n    }\n\n    /** Set the copy for the text object. To split a line you can use '\\n'. */\n    get text(): string\n    {\n        return this._text;\n    }\n\n    set text(text: string | number)\n    {\n        text = String(text === null || text === undefined ? '' : text);\n\n        if (this._text === text)\n        {\n            return;\n        }\n        this._text = text;\n        this.dirty = true;\n    }\n\n    /**\n     * The resolution / device pixel ratio of the canvas.\n     *\n     * This is set to automatically match the renderer resolution by default, but can be overridden by setting manually.\n     * @default 1\n     */\n    get resolution(): number\n    {\n        return this._resolution;\n    }\n\n    set resolution(value: number)\n    {\n        this._autoResolution = false;\n\n        if (this._resolution === value)\n        {\n            return;\n        }\n\n        this._resolution = value;\n        this.dirty = true;\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;AACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;AACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAA,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,EAAA,EAAC,EAAE,CAAC;AACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF;;AC3BA;;;;;;;;;AASG;AACH,IAAY,cAKX;AALD,CAAA,UAAY,aAAa,EAAA;AAGrB,IAAA,aAAA,CAAA,aAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,iBAAmB,CAAA;AACnB,IAAA,aAAA,CAAA,aAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;AACzB,CAAC,EALW,aAAa,KAAb,aAAa,GAKxB,EAAA,CAAA,CAAA;;ACfD;AA+CA,IAAM,YAAY,GAAe;AAC7B,IAAA,KAAK,EAAE,MAAM;AACb,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,eAAe,EAAE,CAAC;AAClB,IAAA,eAAe,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;AAC5B,IAAA,cAAc,EAAE,CAAC;AACjB,IAAA,eAAe,EAAE,OAAO;AACxB,IAAA,kBAAkB,EAAE,CAAC;AACrB,IAAA,IAAI,EAAE,OAAO;IACb,gBAAgB,EAAE,aAAa,CAAC,eAAe;AAC/C,IAAA,iBAAiB,EAAE,EAAE;AACrB,IAAA,UAAU,EAAE,OAAO;AACnB,IAAA,QAAQ,EAAE,EAAE;AACZ,IAAA,SAAS,EAAE,QAAQ;AACnB,IAAA,WAAW,EAAE,QAAQ;AACrB,IAAA,UAAU,EAAE,QAAQ;AACpB,IAAA,aAAa,EAAE,CAAC;AAChB,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,QAAQ,EAAE,OAAO;AACjB,IAAA,UAAU,EAAE,EAAE;AACd,IAAA,OAAO,EAAE,CAAC;AACV,IAAA,MAAM,EAAE,OAAO;AACf,IAAA,eAAe,EAAE,CAAC;AAClB,IAAA,YAAY,EAAE,YAAY;AAC1B,IAAA,IAAI,EAAE,KAAK;AACX,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,aAAa,EAAE,GAAG;AAClB,IAAA,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,IAAM,mBAAmB,GAAG;IACxB,OAAO;IACP,YAAY;IACZ,WAAW;IACX,SAAS;IACT,SAAS;IACT,WAAW,EACd,CAAC;AAEF;;;;;;;;AAQG;AACH,IAAA,SAAA,kBAAA,YAAA;AAkCI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CG;AACH,IAAA,SAAA,SAAA,CAAY,KAA2B,EAAA;AAEnC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QAEjB,IAAI,CAAC,KAAK,EAAE,CAAC;AAEb,QAAA,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KAC1C;AAED;;;;;AAKG;AACI,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;QAEI,IAAM,gBAAgB,GAAwB,EAAE,CAAC;AAEjD,QAAA,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AAEzD,QAAA,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC;KAC1C,CAAA;;AAGM,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,YAAA;AAEI,QAAA,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;KACxD,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AALT;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,MAAM,CAAC;SACtB;AACD,QAAA,GAAA,EAAA,UAAU,KAAqB,EAAA;AAE3B,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EACzB;AACI,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;AAAd,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AACD,QAAA,GAAA,EAAA,UAAe,UAAmB,EAAA;AAE9B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;AAAd,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AACD,QAAA,GAAA,EAAA,UAAe,UAAmB,EAAA;AAE9B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;;AAAnB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;AACD,QAAA,GAAA,EAAA,UAAoB,eAAuB,EAAA;AAEvC,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAC7C;AACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;gBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;;AAAnB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;AACD,QAAA,GAAA,EAAA,UAAoB,eAAuB,EAAA;AAEvC,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAC7C;AACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;gBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAc,CAAA,SAAA,EAAA,gBAAA,EAAA;;AAAlB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,eAAe,CAAC;SAC/B;AACD,QAAA,GAAA,EAAA,UAAmB,cAAsB,EAAA;AAErC,YAAA,IAAI,IAAI,CAAC,eAAe,KAAK,cAAc,EAC3C;AACI,gBAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;gBACtC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;;AAAnB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;AACD,QAAA,GAAA,EAAA,UAAoB,eAAgC,EAAA;AAEhD,YAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC;AAC9C,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,WAAW,EACzC;AACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;gBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AATA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;;AAAtB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,mBAAmB,CAAC;SACnC;AACD,QAAA,GAAA,EAAA,UAAuB,kBAA0B,EAAA;AAE7C,YAAA,IAAI,IAAI,CAAC,mBAAmB,KAAK,kBAAkB,EACnD;AACI,gBAAA,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;gBAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAkBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AARR;;;;;;;AAOG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AACD,QAAA,GAAA,EAAA,UAAS,IAAmB,EAAA;;;;;;AAOxB,YAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,IAAW,CAAC,CAAC;AAC1C,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAC9B;AACI,gBAAA,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;gBACzB,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AAdA,KAAA,CAAA,CAAA;AAqBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAgB,CAAA,SAAA,EAAA,kBAAA,EAAA;AALpB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,iBAAiB,CAAC;SACjC;AACD,QAAA,GAAA,EAAA,UAAqB,gBAA+B,EAAA;AAEhD,YAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,gBAAgB,EAC/C;AACI,gBAAA,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;gBAC1C,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAiB,CAAA,SAAA,EAAA,mBAAA,EAAA;AAJrB;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAClC;AACD,QAAA,GAAA,EAAA,UAAsB,iBAA2B,EAAA;YAE7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAC,iBAAiB,CAAC,EAC9D;AACI,gBAAA,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;gBAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;AAAd,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AACD,QAAA,GAAA,EAAA,UAAe,UAA6B,EAAA;AAExC,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,EAClC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAJZ;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;AACD,QAAA,GAAA,EAAA,UAAa,QAAyB,EAAA;AAElC,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAC/B;AACI,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AANb;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;AACD,QAAA,GAAA,EAAA,UAAc,SAA6B,EAAA;AAEvC,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EACjC;AACI,gBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AANf;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;AACD,QAAA,GAAA,EAAA,UAAgB,WAAiC,EAAA;AAE7C,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW,EACrC;AACI,gBAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;gBAChC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AANd;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AACD,QAAA,GAAA,EAAA,UAAe,UAA+B,EAAA;AAE1C,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;;AAAjB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;AACD,QAAA,GAAA,EAAA,UAAkB,aAAqB,EAAA;AAEnC,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EACzC;AACI,gBAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;gBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;;AAAd,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AACD,QAAA,GAAA,EAAA,UAAe,UAAkB,EAAA;AAE7B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;;AAAX,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,QAAQ,CAAC;SACxB;AACD,QAAA,GAAA,EAAA,UAAY,OAAe,EAAA;AAEvB,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAC7B;AACI,gBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;gBACxB,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAgBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AANZ;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;AACD,QAAA,GAAA,EAAA,UAAa,QAA2B,EAAA;AAEpC,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAC/B;AACI,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAeD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AALd;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AACD,QAAA,GAAA,EAAA,UAAe,UAAkB,EAAA;AAE7B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAJX;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,QAAQ,CAAC;SACxB;AACD,QAAA,GAAA,EAAA,UAAY,OAAe,EAAA;AAEvB,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAC7B;AACI,gBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;gBACxB,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAJV;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;AACD,QAAA,GAAA,EAAA,UAAW,MAAuB,EAAA;;;;AAK9B,YAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrC,YAAA,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,EAChC;AACI,gBAAA,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;gBAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AAZA,KAAA,CAAA,CAAA;AAmBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;AALnB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;AACD,QAAA,GAAA,EAAA,UAAoB,eAAuB,EAAA;AAEvC,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAC7C;AACI,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;gBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAeD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAY,CAAA,SAAA,EAAA,cAAA,EAAA;AALhB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,aAAa,CAAC;SAC7B;AACD,QAAA,GAAA,EAAA,UAAiB,YAAmC,EAAA;AAEhD,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY,EACvC;AACI,gBAAA,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;gBAClC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAI,CAAA,SAAA,EAAA,MAAA,EAAA;;AAAR,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AACD,QAAA,GAAA,EAAA,UAAS,IAAa,EAAA;AAElB,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EACvB;AACI,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAsBD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AAZd;;;;;;;;;;;AAWG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AACD,QAAA,GAAA,EAAA,UAAe,UAA+B,EAAA;AAE1C,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EACnC;AACI,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;;AAAZ,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,SAAS,CAAC;SACzB;AACD,QAAA,GAAA,EAAA,UAAa,QAAiB,EAAA;AAE1B,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAC/B;AACI,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAWD,IAAA,MAAA,CAAA,cAAA,CAAI,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;;AAAjB,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;AACD,QAAA,GAAA,EAAA,UAAkB,aAAqB,EAAA;AAEnC,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EACzC;AACI,gBAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;gBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,aAAA;SACJ;;;AARA,KAAA,CAAA,CAAA;AAUD;;;;AAIG;AACI,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAnB,YAAA;;QAGI,IAAM,cAAc,GAAG,CAAC,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAO,IAAI,CAAC,QAAQ,GAAI,IAAA,GAAG,IAAI,CAAC,QAAQ,CAAC;;;AAIlG,QAAA,IAAI,YAAY,GAAoB,IAAI,CAAC,UAAU,CAAC;QAEpD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EACnC;YACI,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7C,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EACjD;;YAEI,IAAI,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAGxC,YAAA,IAAI,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAC3F;AACI,gBAAA,UAAU,GAAG,IAAA,GAAI,UAAU,GAAA,IAAG,CAAC;AAClC,aAAA;AACA,YAAA,YAAyB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAC9C,SAAA;QAED,OAAU,IAAI,CAAC,SAAS,GAAA,GAAA,GAAI,IAAI,CAAC,WAAW,SAAI,IAAI,CAAC,UAAU,GAAI,GAAA,GAAA,cAAc,SAAK,YAAyB,CAAC,IAAI,CAAC,GAAG,CAAG,CAAC;KAC/H,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;;;;;AAKG;AACH,SAAS,cAAc,CAAC,KAAoB,EAAA;AAExC,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAC7B;AACI,QAAA,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AAC5B,KAAA;AACI,SAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAClC;QACI,IAAK,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAC9B;YACI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACpC,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAWD,SAAS,QAAQ,CAAC,KAAsC,EAAA;AAEpD,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EACzB;AACI,QAAA,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;AAChC,KAAA;AAED,SAAA;AACI,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EACrC;YACI,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,KAAiB,CAAC;AAC5B,KAAA;AACL,CAAC;AAED;;;;;;;AAOG;AACH,SAAS,cAAc,CAAI,MAAW,EAAE,MAAW,EAAA;AAE/C,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EACpD;AACI,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAED,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EACnC;AACI,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAED,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EACtC;QACI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAC3B;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,kBAAkB,CAAC,MAA2B,EAAE,MAA2B,EAAE,WAAgC,EAAA;AAClH,IAAA,KAAK,IAAM,IAAI,IAAI,WAAW,EAAE;QAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;YAC7B,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;AACvC,SAAA;AAAM,aAAA;YACH,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,SAAA;AACJ,KAAA;AACL;;AC9yBA;AACA,IAAM,eAAe,GAAG;;AAEpB,IAAA,kBAAkB,EAAE,IAAI;CACS,CAAC;AAEtC;;;;;;;;AAQG;AACH,IAAA,WAAA,kBAAA,YAAA;AA6CI;;;;;;;;;;AAUG;AACH,IAAA,SAAA,WAAA,CAAY,IAAY,EAAE,KAAgB,EAAE,KAAa,EAAE,MAAc,EAAE,KAAe,EAAE,UAAoB,EAC5G,UAAkB,EAAE,YAAoB,EAAE,cAA4B,EAAA;AAEtE,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;KACxC;AAED;;;;;;;AAOG;IACW,WAAW,CAAA,WAAA,GAAzB,UACI,IAAY,EACZ,KAAgB,EAChB,QAAkB,EAClB,MAAiE,EAAA;AAAjE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAA8C,WAAW,CAAC,OAAO,CAAA,EAAA;QAGjE,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACrF,QAAA,IAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;QAClC,IAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;;AAIrD,QAAA,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,EACjC;AACI,YAAA,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;AACnD,YAAA,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC,QAAkB,CAAC;AACpD,SAAA;QAED,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AAEzD,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAEpB,IAAM,UAAU,GAAG,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;QAC/E,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACjD,IAAM,UAAU,GAAG,IAAI,KAAK,CAAS,KAAK,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;AACI,YAAA,IAAM,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;AAEtG,YAAA,UAAU,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;YAC1B,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;AACpD,SAAA;AACD,QAAA,IAAI,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC,eAAe,CAAC;QAEjD,IAAI,KAAK,CAAC,UAAU,EACpB;AACI,YAAA,KAAK,IAAI,KAAK,CAAC,kBAAkB,CAAC;AACrC,SAAA;AAED,QAAA,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,CAAC;AACvF,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,CAAC;AAC5E,eAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAE1D,IAAI,KAAK,CAAC,UAAU,EACpB;AACI,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;AACtC,SAAA;QAED,OAAO,IAAI,WAAW,CAClB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,UAAU,EACV,UAAU,GAAG,KAAK,CAAC,OAAO,EAC1B,YAAY,EACZ,cAAc,CACjB,CAAC;KACL,CAAA;AAED;;;;;;;AAOG;AACY,IAAA,WAAA,CAAA,QAAQ,GAAvB,UACI,IAAY,EACZ,KAAgB,EAChB,MAAiE,EAAA;AAAjE,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAA8C,WAAW,CAAC,OAAO,CAAA,EAAA;QAGjE,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAEzD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,IAAM,KAAK,GAAwB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAA,aAAa,GAAiB,KAAK,CAAA,aAAtB,EAAE,UAAU,GAAK,KAAK,CAAA,UAAV,CAAW;;QAG5C,IAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;;AAGlE,QAAA,IAAI,gBAAgB,GAAG,CAAC,cAAc,CAAC;;;;;;;AAQvC,QAAA,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;;QAG1D,IAAM,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAE1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EACtC;;AAEI,YAAA,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;;AAGtB,YAAA,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,EAChC;;gBAEI,IAAI,CAAC,gBAAgB,EACrB;AACI,oBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnC,gBAAgB,GAAG,CAAC,cAAc,CAAC;oBACnC,IAAI,GAAG,EAAE,CAAC;oBACV,KAAK,GAAG,CAAC,CAAC;oBACV,SAAS;AACZ,iBAAA;;;gBAID,KAAK,GAAG,GAAG,CAAC;AACf,aAAA;;AAGD,YAAA,IAAI,cAAc,EAClB;;gBAEI,IAAM,mBAAmB,GAAG,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC/D,gBAAA,IAAM,mBAAmB,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAE/E,IAAI,mBAAmB,IAAI,mBAAmB,EAC9C;oBACI,SAAS;AACZ,iBAAA;AACJ,aAAA;;AAGD,YAAA,IAAM,UAAU,GAAG,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;;YAGlF,IAAI,UAAU,GAAG,aAAa,EAC9B;;gBAEI,IAAI,IAAI,KAAK,EAAE,EACf;;AAEI,oBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnC,IAAI,GAAG,EAAE,CAAC;oBACV,KAAK,GAAG,CAAC,CAAC;AACb,iBAAA;;gBAGD,IAAI,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,EACtD;;oBAEI,IAAM,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;AAGpD,oBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAC1C;AACI,wBAAA,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;wBAEzB,IAAI,CAAC,GAAG,CAAC,CAAC;;AAGV,wBAAA,OAAO,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EACxB;4BACI,IAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;4BACnC,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;AAGvC,4BAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,EAC9E;;gCAEI,IAAI,IAAI,QAAQ,CAAC;AACpB,6BAAA;AAED,iCAAA;gCACI,MAAM;AACT,6BAAA;AAED,4BAAA,CAAC,EAAE,CAAC;AACP,yBAAA;AAED,wBAAA,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAErB,wBAAA,IAAM,cAAc,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAErF,wBAAA,IAAI,cAAc,GAAG,KAAK,GAAG,aAAa,EAC1C;AACI,4BAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BACnC,gBAAgB,GAAG,KAAK,CAAC;4BACzB,IAAI,GAAG,EAAE,CAAC;4BACV,KAAK,GAAG,CAAC,CAAC;AACb,yBAAA;wBAED,IAAI,IAAI,IAAI,CAAC;wBACb,KAAK,IAAI,cAAc,CAAC;AAC3B,qBAAA;AACJ,iBAAA;;AAID,qBAAA;;;AAGI,oBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EACnB;AACI,wBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBACnC,IAAI,GAAG,EAAE,CAAC;wBACV,KAAK,GAAG,CAAC,CAAC;AACb,qBAAA;oBAED,IAAM,WAAW,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;;oBAG5C,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;oBAClD,gBAAgB,GAAG,KAAK,CAAC;oBACzB,IAAI,GAAG,EAAE,CAAC;oBACV,KAAK,GAAG,CAAC,CAAC;AACb,iBAAA;AACJ,aAAA;;AAID,iBAAA;;;AAGI,gBAAA,IAAI,UAAU,GAAG,KAAK,GAAG,aAAa,EACtC;;oBAEI,gBAAgB,GAAG,KAAK,CAAC;;AAGzB,oBAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;oBAGnC,IAAI,GAAG,EAAE,CAAC;oBACV,KAAK,GAAG,CAAC,CAAC;AACb,iBAAA;;AAGD,gBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,gBAAgB,EAC9E;;oBAEI,IAAI,IAAI,KAAK,CAAC;;oBAGd,KAAK,IAAI,UAAU,CAAC;AACvB,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAE1C,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;;AAKG;AACY,IAAA,WAAA,CAAA,OAAO,GAAtB,UAAuB,IAAY,EAAE,OAAc,EAAA;AAAd,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAc,GAAA,IAAA,CAAA,EAAA;AAE/C,QAAA,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAEnC,QAAA,IAAI,GAAG,CAAC,OAAO,IAAO,IAAI,GAAI,IAAA,GAAG,IAAI,CAAC;AAEtC,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;AAOG;IACY,WAAY,CAAA,YAAA,GAA3B,UAA4B,GAAW,EAAE,aAAqB,EAAE,KAA0B,EACtF,OAAqE,EAAA;AAErE,QAAA,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AAEvB,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAC7B;YACI,IAAM,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,aAAa,CAAC,CAAC;YAE/C,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;AACjD,YAAA,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACtB,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;AAIG;IACY,WAAc,CAAA,cAAA,GAA7B,UAA8B,UAA+B,EAAA;QAEzD,QAAQ,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,UAAU,EAAE;KACjE,CAAA;AAED;;;;AAIG;IACY,WAAgB,CAAA,gBAAA,GAA/B,UAAgC,UAA+B,EAAA;AAE3D,QAAA,QAAQ,UAAU,KAAK,QAAQ,EAAE;KACpC,CAAA;AAED;;;;AAIG;IACY,WAAS,CAAA,SAAA,GAAxB,UAAyB,IAAY,EAAA;AAEjC,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;AACI,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EACzC;AACI,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAErB,YAAA,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,EACtC;gBACI,MAAM;AACT,aAAA;YAED,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACY,WAAS,CAAA,SAAA,GAAxB,UAAyB,IAAY,EAAA;AAEjC,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QAAQ,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;KACnE,CAAA;AAED;;;;;;;;;AASG;AACI,IAAA,WAAA,CAAA,eAAe,GAAtB,UAAuB,IAAY,EAAE,SAAkB,EAAA;AAEnD,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;AACI,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QAAQ,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;KACzE,CAAA;AAED;;;;AAIG;IACY,WAAQ,CAAA,QAAA,GAAvB,UAAwB,IAAY,EAAA;QAEhC,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,EAAE,CAAC;AAEf,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAC5B;AACI,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;AACI,YAAA,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,IAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE7B,YAAA,IAAI,WAAW,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9E;gBACI,IAAI,KAAK,KAAK,EAAE,EAChB;AACI,oBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACnB,KAAK,GAAG,EAAE,CAAC;AACd,iBAAA;AAED,gBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAElB,SAAS;AACZ,aAAA;YAED,KAAK,IAAI,IAAI,CAAC;AACjB,SAAA;QAED,IAAI,KAAK,KAAK,EAAE,EAChB;AACI,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtB,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;;;;;;AASG;AACI,IAAA,WAAA,CAAA,aAAa,GAApB,UAAqB,MAAc,EAAE,UAAmB,EAAA;AAEpD,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;AAED;;;;;;;;;;;;;AAaG;IACI,WAAa,CAAA,aAAA,GAApB,UAAqB,KAAa,EAAE,SAAiB,EAAE,MAAc,EAAE,MAAc,EACjF,WAAoB,EAAA;AAEpB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;;;;;AAWG;IACI,WAAa,CAAA,aAAA,GAApB,UAAqB,KAAa,EAAA;AAE9B,QAAA,OAAO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;KAC1B,CAAA;AAED;;;;AAIG;IACW,WAAW,CAAA,WAAA,GAAzB,UAA0B,IAAY,EAAA;;AAGlC,QAAA,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAC5B;AACI,YAAA,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,SAAA;AAED,QAAA,IAAM,UAAU,GAAiB;AAC7B,YAAA,MAAM,EAAE,CAAC;AACT,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,QAAQ,EAAE,CAAC;SACd,CAAC;AAEF,QAAA,IAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;AACnC,QAAA,IAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;AAErC,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAEpB,IAAM,aAAa,GAAG,WAAW,CAAC,cAAc,GAAG,WAAW,CAAC,eAAe,CAAC;AAC/E,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC;AAClE,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC;AACjF,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,GAAG,QAAQ,CAAC,CAAC;QAEnE,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC,mBAAmB,GAAG,CAAC,CAAC;AAE1D,QAAA,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,QAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AAEvB,QAAA,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAEtC,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;AAEpB,QAAA,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;AACpC,QAAA,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;AAE7C,QAAA,IAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;AACjE,QAAA,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AAChC,QAAA,IAAM,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;QAEvB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,IAAI,GAAG,KAAK,CAAC;;QAGjB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAC7B;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAChC;gBACI,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAC9B;oBACI,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;AACT,iBAAA;AACJ,aAAA;YACD,IAAI,CAAC,IAAI,EACT;gBACI,GAAG,IAAI,IAAI,CAAC;AACf,aAAA;AAED,iBAAA;gBACI,MAAM;AACT,aAAA;AACJ,SAAA;AAED,QAAA,UAAU,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC;AAEjC,QAAA,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;QACpB,IAAI,GAAG,KAAK,CAAC;;QAGb,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAClC;AACI,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAChC;gBACI,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAC9B;oBACI,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;AACT,iBAAA;AACJ,aAAA;YAED,IAAI,CAAC,IAAI,EACT;gBACI,GAAG,IAAI,IAAI,CAAC;AACf,aAAA;AAED,iBAAA;gBACI,MAAM;AACT,aAAA;AACJ,SAAA;AAED,QAAA,UAAU,CAAC,OAAO,GAAG,CAAC,GAAG,QAAQ,CAAC;QAClC,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;AAE7D,QAAA,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;AAEtC,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;AAED;;;AAGG;IACW,WAAY,CAAA,YAAA,GAA1B,UAA2B,IAAS,EAAA;AAAT,QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAS,GAAA,EAAA,CAAA,EAAA;AAEhC,QAAA,IAAI,IAAI,EACR;AACI,YAAA,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,SAAA;AAED,aAAA;AACI,YAAA,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;AAC3B,SAAA;KACJ,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAkB,WAAO,EAAA,SAAA,EAAA;AALzB;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EACzB;gBACI,IAAI,MAAM,SAAqC,CAAC;gBAEhD,IACA;;oBAEI,IAAM,CAAC,GAAG,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACpC,IAAM,OAAO,GAAG,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AAEpD,oBAAA,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,EAClC;AACI,wBAAA,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC;AAEzB,wBAAA,OAAO,CAAC,CAAC;AACZ,qBAAA;AAED,oBAAA,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;AAC5C,iBAAA;AACD,gBAAA,OAAO,EAAE,EACT;AACI,oBAAA,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;AAC5C,iBAAA;gBACD,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;AAClC,gBAAA,WAAW,CAAC,QAAQ,GAAG,MAAM,CAAC;AACjC,aAAA;YAED,OAAO,WAAW,CAAC,QAAQ,CAAC;SAC/B;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAkB,WAAQ,EAAA,UAAA,EAAA;AAJ1B;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAC1B;AACI,gBAAA,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AACjF,aAAA;YAED,OAAO,WAAW,CAAC,SAAS,CAAC;SAChC;;;AAAA,KAAA,CAAA,CAAA;IACL,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;;;;;;;;AAQG;AAEH;;;;;AAKG;AACH,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;AAExB;;;;;;;;AAQG;AACH,WAAW,CAAC,cAAc,GAAG,MAAM,CAAC;AAEpC;;;;;;;AAOG;AACH,WAAW,CAAC,eAAe,GAAG,GAAG,CAAC;AAElC;;;;;;;AAOG;AACH,WAAW,CAAC,mBAAmB,GAAG,GAAG,CAAC;AAEtC;;;;;;;AAOG;AACH,WAAW,CAAC,iBAAiB,GAAG,GAAG,CAAC;AAEpC;;;;;AAKG;AACH,WAAW,CAAC,SAAS,GAAG;IACpB,MAAM;AACN,IAAA,MAAM,EACT,CAAC;AAEF;;;;;AAKG;AACH,WAAW,CAAC,eAAe,GAAG;IAC1B,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACN,IAAA,MAAM,EACT,CAAC;AAEF;;;;;;;AAOG;;AC/0BH,IAAM,qBAAqB,GAAoB;AAC3C,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,WAAW,EAAE,IAAI;CACpB,CAAC;AAUF;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,IAAA,IAAA,kBAAA,UAAA,MAAA,EAAA;IAA0B,SAAM,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AAiE5B;;;;AAIG;AACH,IAAA,SAAA,IAAA,CAAY,IAAsB,EAAE,KAAuC,EAAE,MAA0B,EAAA;QAAvG,IAsCC,KAAA,GAAA,IAAA,CAAA;QApCG,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,MAAM,EACX;AACI,YAAA,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YACzC,SAAS,GAAG,IAAI,CAAC;AACpB,SAAA;AAED,QAAA,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AACjB,QAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAElB,IAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAErC,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,SAAS,EAAE,CAAC;AAC/B,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,SAAS,EAAE,CAAC;QAE/B,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,IAAC,IAAA,CAAA;AAEf,QAAA,KAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE;;AAEnC,YAAA,kBAAkB,EAAE,IAAI;AAC3B,SAAA,CAA6B,CAAC;AAE/B,QAAA,KAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,QAAA,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,QAAA,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,KAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAEhB,QAAA,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAEnB,QAAA,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;;KAC1B;AAED;;;;;;;AAOG;IACI,IAAU,CAAA,SAAA,CAAA,UAAA,GAAjB,UAAkB,YAAqB,EAAA;AAEnC,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;;AAG1B,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,OAAO,EACvC;AACI,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,YAAY,EAC/B;YACI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;AAExC,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5G,QAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC7B,QAAA,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC/B,QAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC7B,QAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,QAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,QAAA,IAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC3C,QAAA,IAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;AAE/C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AACxG,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QAE1G,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAElD,QAAA,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAE/D,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1B,QAAA,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,eAAe,CAAC;AAC1C,QAAA,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;AAC1C,QAAA,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AAClC,QAAA,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;AAEtC,QAAA,IAAI,aAAqB,CAAC;AAC1B,QAAA,IAAI,aAAqB,CAAC;;AAG1B,QAAA,IAAM,WAAW,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;QAa7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EACpC;YACI,IAAM,YAAY,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC;;AAEjD,YAAA,IAAM,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7F,YAAA,IAAM,cAAc,GAAG,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;AAEvD,YAAA,IAAI,YAAY,EAChB;;;;AAII,gBAAA,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC;AAC5B,gBAAA,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;AAE9B,gBAAA,IAAM,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC;gBAC9C,IAAM,GAAG,GAAG,OAAO,CAAC,OAAO,eAAe,KAAK,QAAQ,GAAG,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;gBACzG,IAAM,cAAc,GAAG,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC/D,IAAM,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC;AAEvE,gBAAA,OAAO,CAAC,WAAW,GAAG,OAAA,GAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,GAAA,GAAA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAA,GAAA,GAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,GAAA,GAAA,KAAK,CAAC,eAAe,MAAG,CAAC;AACvG,gBAAA,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC;AACpC,gBAAA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,kBAAkB,CAAC;AAC7E,gBAAA,OAAO,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,kBAAkB,IAAI,cAAc,CAAC;AACnG,aAAA;AAED,iBAAA;;AAEI,gBAAA,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;;;;AAIpE,gBAAA,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,MAAgB,CAAC;AAE7C,gBAAA,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;AAC9B,gBAAA,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;AACvB,gBAAA,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;AAC1B,gBAAA,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;AAC7B,aAAA;YAED,IAAI,kBAAkB,GAAG,CAAC,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,CAAC,CAAC;AAEpE,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,GAAG,CAAC,EAC5E;gBACI,kBAAkB,GAAG,CAAC,CAAC;AAC1B,aAAA;;AAGD,YAAA,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,KAAK,CAAC,MAAM,EAAE,GAAC,EAAE,EACrC;AACI,gBAAA,aAAa,GAAG,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC;AAC1C,gBAAA,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,KAAK,GAAC,GAAG,UAAU,CAAC,IAAI,cAAc,CAAC,MAAM;AAClF,sBAAA,kBAAkB,CAAC;AAEzB,gBAAA,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,EAC3B;AACI,oBAAA,aAAa,IAAI,YAAY,GAAG,UAAU,CAAC,GAAC,CAAC,CAAC;AACjD,iBAAA;AACI,qBAAA,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,EACjC;oBACI,aAAa,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,GAAC,CAAC,IAAI,CAAC,CAAC;AACvD,iBAAA;AAED,gBAAA,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,EACzC;oBACI,IAAI,CAAC,iBAAiB,CAClB,KAAK,CAAC,GAAC,CAAC,EACR,aAAa,GAAG,KAAK,CAAC,OAAO,EAC7B,aAAa,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY,EAC5C,IAAI,CACP,CAAC;AACL,iBAAA;gBAED,IAAI,KAAK,CAAC,IAAI,EACd;oBACI,IAAI,CAAC,iBAAiB,CAClB,KAAK,CAAC,GAAC,CAAC,EACR,aAAa,GAAG,KAAK,CAAC,OAAO,EAC7B,aAAa,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY,CAC/C,CAAC;AACL,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB,CAAA;AAED;;;;;;;AAOG;IACK,IAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UAA0B,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,QAAgB,EAAA;AAAhB,QAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAgB,GAAA,KAAA,CAAA,EAAA;AAE1E,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;;AAG1B,QAAA,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;;;;;AAM1C,QAAA,IAAM,oBAAoB,GAAG,IAAI,CAAC,yBAAyB;AACpD,gBAAC,eAAe,IAAI,wBAAwB,CAAC,SAAS;AAClD,mBAAA,mBAAmB,IAAI,wBAAwB,CAAC,SAAS,CAAC,CAAC;AAEtE,QAAA,IAAI,aAAa,KAAK,CAAC,IAAI,oBAAoB,EAC/C;AACI,YAAA,IAAI,oBAAoB,EACxB;AACI,gBAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AAC3C,gBAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,aAAa,CAAC;AAClD,aAAA;AAED,YAAA,IAAI,QAAQ,EACZ;gBACI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,aAAA;YAED,OAAO;AACV,SAAA;QAED,IAAI,eAAe,GAAG,CAAC,CAAC;;;;;;;QAQxB,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACnE,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;QACzD,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAC3C;AACI,YAAA,IAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAEnC,YAAA,IAAI,QAAQ,EACZ;gBACI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;AAC5D,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;AAC1D,aAAA;YACD,IAAI,OAAO,GAAG,EAAE,CAAC;AAEjB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAC/C;AACI,gBAAA,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAC7B,aAAA;YACD,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AACvD,YAAA,eAAe,IAAI,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;YAChE,aAAa,GAAG,YAAY,CAAC;AAChC,SAAA;KACJ,CAAA;;AAGO,IAAA,IAAA,CAAA,SAAA,CAAA,aAAa,GAArB,YAAA;AAEI,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAE3B,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EACpB;AACI,YAAA,IAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;YAEnC,IAAI,OAAO,CAAC,IAAI,EAChB;AACI,gBAAA,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AAC7B,gBAAA,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC/B,gBAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9B,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC1B,QAAA,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;AAC/C,QAAA,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAExC,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;AAC5E,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;AAC/E,QAAA,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;AAC1B,QAAA,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;AAE1B,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;AAC1D,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;;QAG5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExB,QAAA,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEvE,OAAO,CAAC,SAAS,EAAE,CAAC;AAEpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB,CAAA;AAED;;;AAGG;IACO,IAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,QAAkB,EAAA;QAEhC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,EACpE;AACI,YAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAEtB,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;KAC3B,CAAA;;AAGM,IAAA,IAAA,CAAA,SAAA,CAAA,eAAe,GAAtB,YAAA;AAEI,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEtB,MAAM,CAAA,SAAA,CAAA,eAAe,WAAE,CAAC;KAC3B,CAAA;AAEM,IAAA,IAAA,CAAA,SAAA,CAAA,SAAS,GAAhB,UAAiB,UAAoB,EAAE,IAAgB,EAAA;AAEnD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAEtB,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,EAC1B;;YAEI,UAAU,GAAG,KAAK,CAAC;AACtB,SAAA;AAED,QAAA,OAAO,iBAAM,SAAS,CAAA,IAAA,CAAA,IAAA,EAAC,UAAU,EAAE,IAAI,CAAC,CAAC;KAC5C,CAAA;AAED;;;;AAIG;IACI,IAAc,CAAA,SAAA,CAAA,cAAA,GAArB,UAAsB,IAAgB,EAAA;AAElC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEtB,OAAO,MAAA,CAAA,SAAA,CAAM,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAChD,CAAA;;AAGS,IAAA,IAAA,CAAA,SAAA,CAAA,gBAAgB,GAA1B,YAAA;QAEI,IAAI,CAAC,iBAAiB,EAAE,CAAC;;QAEzB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACzC,CAAA;AAED;;;;;;AAMG;AACK,IAAA,IAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,UACI,KAAgB,EAAE,KAAe,EAAE,OAAoB,EAAA;;;;AAMvD,QAAA,IAAM,SAAS,GAAuD,KAAK,CAAC,IAAW,CAAC;AAExF,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAC7B;AACI,YAAA,OAAO,SAAS,CAAC;AACpB,SAAA;AACI,aAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAC/B;AACI,YAAA,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;AACvB,SAAA;;;AAID,QAAA,IAAI,QAAmC,CAAC;;;AAIxC,QAAA,IAAM,oBAAoB,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC;;AAG/E,QAAA,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;QAEnC,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI,oBAAoB,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;QAC5F,IAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,IAAI,oBAAoB,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;;AAG9F,QAAA,IAAM,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;;AAG1D,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAC7B;AACI,YAAA,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EACpC;AACI,gBAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;AAC3C,aAAA;AACJ,SAAA;;;QAID,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,QAAA,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,QAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE1B,QAAA,IAAI,KAAK,CAAC,gBAAgB,KAAK,aAAa,CAAC,eAAe,EAC5D;;YAEI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC;;;;YAM9F,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,CAAC;AAE3E,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EACrC;AACI,gBAAA,IAAM,cAAc,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC;AACnE,gBAAA,IAAM,WAAW,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;gBAC3C,IAAI,qBAAqB,GAAG,WAAW,CAAC;;AAGxC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,cAAc,GAAG,WAAW,EACzC;oBACI,qBAAqB,GAAG,CAAC,WAAW,GAAG,cAAc,IAAI,CAAC,CAAC;AAC9D,iBAAA;AAED,gBAAA,IAAM,cAAc,GAAG,WAAW,GAAG,UAAU,CAAC;gBAChD,IAAM,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjD,IAAI,mBAAmB,GAAG,cAAc,CAAC;;gBAGzC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,WAAW,GAAG,cAAc,EACxD;oBACI,mBAAmB,GAAG,CAAC,cAAc,GAAG,WAAW,IAAI,CAAC,CAAC;AAC5D,iBAAA;;gBAGD,IAAM,kBAAkB,GAAG,CAAC,mBAAmB,GAAG,qBAAqB,IAAI,MAAM,CAAC;AAElF,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;;oBAEI,IAAI,QAAQ,GAAG,CAAC,CAAC;AAEjB,oBAAA,IAAI,OAAO,iBAAiB,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC5C;AACI,wBAAA,QAAQ,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AACnC,qBAAA;AAED,yBAAA;AACI,wBAAA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC9B,qBAAA;oBAED,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EACnC,CAAC,qBAAqB,GAAG,MAAM,KAAK,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;;oBAGzE,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,aAAA;;YAEI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;;;AAI/F,YAAA,IAAM,eAAe,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YACxC,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EACpC;gBACI,IAAI,IAAI,SAAQ,CAAC;AAEjB,gBAAA,IAAI,OAAO,iBAAiB,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC5C;AACI,oBAAA,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC/B,iBAAA;AAED,qBAAA;AACI,oBAAA,IAAI,GAAG,gBAAgB,GAAG,eAAe,CAAC;AAC7C,iBAAA;gBACD,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAA,gBAAgB,EAAE,CAAC;AACtB,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAED;;;;;;;;;;;AAWG;IACI,IAAO,CAAA,SAAA,CAAA,OAAA,GAAd,UAAe,OAAmC,EAAA;AAE9C,QAAA,IAAI,OAAO,OAAO,KAAK,SAAS,EAChC;AACI,YAAA,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACnC,SAAA;QAED,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;AAE5D,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,OAAO,CAAC,CAAC;;;QAIvB,IAAI,IAAI,CAAC,UAAU,EACnB;AACI,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AAC9C,SAAA;;AAGD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAEnB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAK,CAAA,SAAA,EAAA,OAAA,EAAA;;AAAT,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAEtB,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;SAC5D;AAED,QAAA,GAAA,EAAA,UAAU,KAAa,EAAA;AAEnB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAEtB,YAAA,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAElC,YAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AACpD,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACvB;;;AAVA,KAAA,CAAA,CAAA;AAaD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAM,CAAA,SAAA,EAAA,QAAA,EAAA;;AAAV,QAAA,GAAA,EAAA,YAAA;AAEI,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAEtB,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;SAC7D;AAED,QAAA,GAAA,EAAA,UAAW,KAAa,EAAA;AAEpB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAEtB,YAAA,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAElC,YAAA,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AACrD,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;;;AAVA,KAAA,CAAA,CAAA;AAiBD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AALT;;;;AAIG;AACH,QAAA,GAAA,EAAA,YAAA;;;;YAKI,OAAO,IAAI,CAAC,MAAM,CAAC;SACtB;AAED,QAAA,GAAA,EAAA,UAAU,KAAsC,EAAA;AAE5C,YAAA,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;YAEpB,IAAI,KAAK,YAAY,SAAS,EAC9B;AACI,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACvB,aAAA;AAED,iBAAA;gBACI,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;AACtC,aAAA;AAED,YAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AACvB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;;;AAjBA,KAAA,CAAA,CAAA;AAoBD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAI,CAAA,SAAA,EAAA,MAAA,EAAA;;AAAR,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;AAED,QAAA,GAAA,EAAA,UAAS,IAAqB,EAAA;AAE1B,YAAA,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAE/D,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EACvB;gBACI,OAAO;AACV,aAAA;AACD,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;;;AAZA,KAAA,CAAA,CAAA;AAoBD,IAAA,MAAA,CAAA,cAAA,CAAI,IAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AANd;;;;;AAKG;AACH,QAAA,GAAA,EAAA,YAAA;YAEI,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;AAED,QAAA,GAAA,EAAA,UAAe,KAAa,EAAA;AAExB,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAE7B,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAC9B;gBACI,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;;;AAbA,KAAA,CAAA,CAAA;AA/sBD;;;;;AAKG;IACW,IAAsB,CAAA,sBAAA,GAAG,KAAK,CAAC;AAE7C;;;;AAIG;IACW,IAAyB,CAAA,yBAAA,GAAG,KAAK,CAAC;IAgtBpD,OAAC,IAAA,CAAA;CAAA,CA/tByB,MAAM,CA+tB/B;;;;"}